diff --git a/CMakeLists.txt b/CMakeLists.txt
index 9a0e4392..c22def25 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -65,11 +65,26 @@ if(BYTERAFT_WITH_BRPC_PROFILER)
 endif()
 add_subdirectory(third)
 
+if (BYTE_ENABLE_METRICS2)
+    add_compile_definitions(BYTE_ENABLE_METRICS2=1)
+endif()
+
 if(TARGET protoc)
-   set(PROTOBUF_PROTOC_EXECUTABLE $<TARGET_FILE:protoc>)
-   message(STATUS "use protobuf at ${PROTOBUF_PROTOC_EXECUTABLE}")
+    set(PROTOBUF_PROTOC protoc)
+    set(PROTOBUF_PROTOC_EXECUTABLE $<TARGET_FILE:protoc>)
+    message(STATUS "use protobuf at ${PROTOBUF_PROTOC_EXECUTABLE}")
 else()
-   message(FATAL_ERROR "no protobuf compiler found")
+    find_package(Protobuf REQUIRED)
+    if(TARGET protobuf::protoc)
+        set(PROTOBUF_PROTOC protobuf::protoc)
+    else()
+        set(PROTOBUF_PROTOC ${PROTOBUF_PROTOC_EXECUTABLE})
+    endif()
+    message(STATUS "protoc: ${PROTOBUF_PROTOC}, exe: ${PROTOBUF_PROTOC_EXECUTABLE}")
+endif()
+
+if (NOT PROTOBUF_PROTOC_EXECUTABLE)
+    message(FATAL_ERROR "no protobuf compiler found")
 endif()
 
 if (EXISTS ${CMAKE_SOURCE_DIR}/third/byte)
@@ -124,4 +139,4 @@ if(BYTERAFT_WITH_JEPSEN)
     add_subdirectory(jepsen)
 endif()
 
-add_subdirectory(tools)
+# add_subdirectory(tools)
diff --git a/byteraft/CMakeLists.txt b/byteraft/CMakeLists.txt
index 0c736ded..1e3d2345 100644
--- a/byteraft/CMakeLists.txt
+++ b/byteraft/CMakeLists.txt
@@ -1,4 +1,5 @@
 # set different compile options for proto.
+include_directories(${CMAKE_INSTALL_PREFIX}/include)
 add_subdirectory(proto)
 
 set(CXX_FLAGS
@@ -45,12 +46,6 @@ endif()
 string(REPLACE ";" " " CXX_FLAGS "${CXX_FLAGS}")
 
 include_directories(
-    ${BYTERAFT_THIRD_ROOT}/third/byte
-    ${BYTERAFT_THIRD_ROOT}/third/byte/thirdparty/abseil-cpp
-    ${BYTERAFT_THIRD_ROOT}/third/byte/thirdparty/gtest/googletest/include
-    ${BYTERAFT_THIRD_ROOT}/third/byte/thirdparty/spdlog/include
-    ${BYTERAFT_THIRD_ROOT}/third/byte/thirdparty/json/include
-    ${BYTERAFT_THIRD_ROOT}/third/rock
     ${PROJECT_BINARY_DIR}/byteraft
     ${PROJECT_SOURCE_DIR}/byteraft)
 
@@ -151,3 +146,12 @@ if(BYTERAFT_BUILD_TESTS)
     byteraft_test("wal/meta_manager_impl_test.cc")
 endif()
 
+install(TARGETS byteraft ARCHIVE DESTINATION lib)
+
+install(DIRECTORY ${PROJECT_SOURCE_DIR}/byteraft
+    DESTINATION include
+    FILES_MATCHING
+    PATTERN "*.h"
+    PATTERN "*.hpp"
+    PATTERN "CMakeFiles" EXCLUDE
+)
\ No newline at end of file
diff --git a/byteraft/proto/CMakeLists.txt b/byteraft/proto/CMakeLists.txt
index 3b37063c..388f0747 100644
--- a/byteraft/proto/CMakeLists.txt
+++ b/byteraft/proto/CMakeLists.txt
@@ -13,7 +13,7 @@ foreach(proto ${proto_files})
     set(input_proto_file ${CMAKE_CURRENT_SOURCE_DIR}/${proto})
     add_custom_command(OUTPUT "${output_cc_file}" "${output_hdr_file}"
         COMMAND ${PROTOBUF_PROTOC_EXECUTABLE} -I${CMAKE_CURRENT_SOURCE_DIR} --cpp_out=${CMAKE_CURRENT_BINARY_DIR} ${input_proto_file}
-        DEPENDS "${proto}" protoc
+        DEPENDS "${proto}" ${PROTOBUF_PROTOC}
         COMMENT "generate proto file ${cc_file}, ${hdr_file} at ${CMAKE_CURRENT_BINARY_DIR}"
         )
 
diff --git a/third/CMakeLists.txt b/third/CMakeLists.txt
index f01e3707..1ee3b3e3 100644
--- a/third/CMakeLists.txt
+++ b/third/CMakeLists.txt
@@ -1,8 +1,8 @@
 if(NOT TARGET byte)
-    add_subdirectory(byte)
+    # add_subdirectory(byte)
 endif()
 if (NOT TARGET rock)
-    add_subdirectory(rock)
+    # add_subdirectory(rock)
 endif()
 if(BYTERAFT_WITH_EXAMPLE OR BYTERAFT_WITH_JEPSEN OR BYTERAFT_BUILD_TESTS)
     include(ExternalProject)
@@ -17,5 +17,5 @@ if(BYTERAFT_WITH_EXAMPLE OR BYTERAFT_WITH_JEPSEN OR BYTERAFT_BUILD_TESTS)
     add_dependencies(rocksdb rocksdb-project)
 endif()
 if(NOT TARGET rock)
-    add_subdirectory(rock)
+    # add_subdirectory(rock)
 endif()
