diff --git a/CMakeLists.txt b/CMakeLists.txt
index 416bbe3..eb3e1dd 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -33,7 +33,13 @@ if(ROCK_BUILD_TESTS AND NOT TARGET gtest)
 endif()
 
 if(NOT TARGET absl::config)
-  add_subdirectory(thirdparty/abseil-cpp)
+  find_package(absl REQUIRED
+    COMPONENTS
+    strings
+    str_format
+    synchronization
+  )
+  # add_subdirectory(thirdparty/abseil-cpp)
 endif()
 
 add_subdirectory(rock)
diff --git a/rock/CMakeLists.txt b/rock/CMakeLists.txt
index 5172ede..936ae7d 100644
--- a/rock/CMakeLists.txt
+++ b/rock/CMakeLists.txt
@@ -27,6 +27,19 @@ add_library(rock ${ROCK_SOURCE_FILES})
 target_link_libraries(rock pthread absl::strings absl::str_format absl::synchronization)
 target_include_directories(rock PUBLIC ${PROJECT_SOURCE_DIR}/)
 set_target_properties(rock PROPERTIES COMPILE_FLAGS "${CXX_FLAGS}")
+install(TARGETS rock
+    ARCHIVE
+    DESTINATION lib
+)
+
+install(DIRECTORY ${PROJECT_SOURCE_DIR}/rock
+    DESTINATION include
+    FILES_MATCHING
+    PATTERN "*.h"
+    PATTERN "*.hpp"
+    PATTERN "CMakeFiles" EXCLUDE
+)
+
 
 if(ROCK_BUILD_TESTS)
     enable_testing()
