set(lib_name ev)
ExternalProject_Add(
  ${lib_name}
  DOWNLOAD_COMMAND ${CMAKE_COMMAND} -E echo "skip download"
  SOURCE_DIR ${CMAKE_SOURCE_DIR}/build/byte-thirdparty/src/byte-thirdparty
  SOURCE_SUBDIR ${lib_name}
  PREFIX ${CMAKE_CURRENT_BINARY_DIR}/${lib_name}
  DOWNLOAD_DIR ${DOWNLOAD_DIR}
  BUILD_IN_SOURCE TRUE
  CONFIGURE_COMMAND
    ${common_configure_envs}
    ./configure --prefix=${CMAKE_INSTALL_PREFIX} --with-pic=yes
  BUILD_COMMAND make -s -j${NUM_JOBS_PER_LIB}
  INSTALL_COMMAND make -s install
  LOG_CONFIGURE TRUE
  LOG_BUILD TRUE
  LOG_INSTALL TRUE
  )

ExternalProject_Add_Step(${lib_name} move_ev_header
  COMMAND ${CMAKE_COMMAND} -E make_directory ${CMAKE_INSTALL_PREFIX}/include/libev
  COMMAND ${CMAKE_COMMAND} -E copy ${CMAKE_INSTALL_PREFIX}/include/ev.h ${CMAKE_INSTALL_PREFIX}/include/libev/ev.h
  COMMAND ${CMAKE_COMMAND} -E copy ${CMAKE_INSTALL_PREFIX}/include/ev++.h ${CMAKE_INSTALL_PREFIX}/include/libev/ev++.h
  COMMAND ${CMAKE_COMMAND} -E copy ${CMAKE_INSTALL_PREFIX}/include/event.h ${CMAKE_INSTALL_PREFIX}/include/libev/event.h
  COMMAND ${CMAKE_COMMAND} -E remove ${CMAKE_INSTALL_PREFIX}/include/ev.h
  COMMAND ${CMAKE_COMMAND} -E remove ${CMAKE_INSTALL_PREFIX}/include/ev++.h
  COMMAND ${CMAKE_COMMAND} -E remove ${CMAKE_INSTALL_PREFIX}/include/event.h
  DEPENDEES install
)