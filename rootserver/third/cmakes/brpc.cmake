set(lib_name brpc)
ExternalProject_Add(
  ${lib_name}
  DOWNLOAD_COMMAND ${CMAKE_COMMAND} -E echo "skip download"
  SOURCE_DIR ${CMAKE_SOURCE_DIR}/build/byte-thirdparty/src/byte-thirdparty
  SOURCE_SUBDIR ${lib_name}
  PREFIX ${CMAKE_CURRENT_BINARY_DIR}/${lib_name}
  DOWNLOAD_DIR ${DOWNLOAD_DIR}
    BUILD_IN_SOURCE FALSE
  CMAKE_ARGS
    ${common_cmake_args}
    -DbRPC_GFLAGS_PROVIDER=package
    -DbRPC_PROTOBUF_PROVIDER=package
    -DbRPC_OPENSSL_PROVIDER=package
    -DbRPC_ZLIB_PROVIDER=package
    -DbRPC_SSL_PROVIDER=package
    -DBUILD_SHARED_LIBS=OFF
  BUILD_COMMAND make -j${NUM_JOBS_PER_LIB}
  INSTALL_COMMAND make install
  LOG_CONFIGURE TRUE
  LOG_BUILD TRUE
  LOG_INSTALL TRUE
)
