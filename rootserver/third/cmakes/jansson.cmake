set(lib_name j<PERSON><PERSON>)
ExternalProject_Add(
  ${lib_name}
  GIT_REPOSITORY ******************:basis/jansson.git
  GIT_TAG 60097f0096f337393b42d0ad13d403294ff4e782
  GIT_SUBMODULES ""
  PREFIX ${CMAKE_CURRENT_BINARY_DIR}/${lib_name}
  BUILD_IN_SOURCE FALSE
  CMAKE_ARGS
    ${common_cmake_args}
  BUILD_COMMAND make -s -j${NUM_JOBS_PER_LIB}
  INSTALL_COMMAND make -s install
  LOG_CONFIGURE TRUE
  LOG_BUILD TRUE
  LOG_INSTALL TRUE
  LOG_OUTPUT_ON_FAILURE TRUE
)
