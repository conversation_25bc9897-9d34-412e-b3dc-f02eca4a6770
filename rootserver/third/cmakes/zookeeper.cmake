set(lib_name zookeeper)
ExternalProject_Add(
  ${lib_name}
  DOWNLOAD_COMMAND ${CMAKE_COMMAND} -E echo "skip download"
  SOURCE_DIR ${CMAKE_SOURCE_DIR}/build/byte-thirdparty/src/byte-thirdparty
  SOURCE_SUBDIR ${lib_name}/zookeeper-client/zookeeper-client-c
  PREFIX ${CMAKE_CURRENT_BINARY_DIR}/${lib_name}
  DOWNLOAD_DIR ${DOWNLOAD_DIR}
  BUILD_IN_SOURCE FALSE
  CMAKE_ARGS
    ${common_cmake_args}
  BUILD_COMMAND make -j${NUM_JOBS_PER_LIB}
  INSTALL_COMMAND ""
  LOG_CONFIGURE TRUE
  LOG_BUILD TRUE
  LOG_INSTALL TRUE
  )

ExternalProject_Add_Step(${lib_name} copy_related
  DEPENDEES install
  COMMAND ${CMAKE_COMMAND} -E copy ${CMAKE_CURRENT_BINARY_DIR}/zookeeper/src/zookeeper-build/libhashtable.a ${CMAKE_INSTALL_PREFIX}/lib/libhashtable.a
  COMMAND ${CMAKE_COMMAND} -E copy ${CMAKE_CURRENT_BINARY_DIR}/zookeeper/src/zookeeper-build/libzookeeper.a ${CMAKE_INSTALL_PREFIX}/lib/libzookeeper.a
  COMMAND ${CMAKE_COMMAND} -E make_directory "${CMAKE_INSTALL_PREFIX}/include/zookeeper-client-c/include"
  COMMAND ${CMAKE_COMMAND} -E copy_directory "${CMAKE_SOURCE_DIR}/build/byte-thirdparty/src/byte-thirdparty/zookeeper/zookeeper-client/zookeeper-client-c/include" "${CMAKE_INSTALL_PREFIX}/include/zookeeper-client-c/include"
  COMMAND ${CMAKE_COMMAND} -E make_directory "${CMAKE_INSTALL_PREFIX}/include/zookeeper-client-c/generated"
  COMMAND ${CMAKE_COMMAND} -E copy "${CMAKE_SOURCE_DIR}/build/byte-thirdparty/src/byte-thirdparty/zookeeper/zookeeper-client/zookeeper-client-c/generated/zookeeper.jute.h" "${CMAKE_INSTALL_PREFIX}/include/zookeeper-client-c/generated/zookeeper.jute.h"
)