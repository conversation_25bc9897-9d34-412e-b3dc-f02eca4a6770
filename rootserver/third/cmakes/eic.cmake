set(lib_name eic)
ExternalProject_Add(
  ${lib_name}
  # GIT_REPOSITORY ******************:infcs/eic.git
  # GIT_TAG 5c95df9c7c5b58f531173b22959b74001f566e4d
  # GIT_SUBMODULES ""

  # GIT_SHALLOW OFF
  DOWNLOAD_COMMAND <NAME_EMAIL>:infcs/eic.git ${CMAKE_CURRENT_BINARY_DIR}/eic/src/eic
  UPDATE_COMMAND git checkout 306c7b9009f6662486af156f31b185d45cce1c09

  PREFIX ${CMAKE_CURRENT_BINARY_DIR}/${lib_name}
  DOWNLOAD_DIR ${DOWNLOAD_DIR}
  BUILD_IN_SOURCE FALSE
  CMAKE_ARGS
    ${common_cmake_args}
    -DEIC_BUILD_TESTS=OFF
    -DEIC_WITH_EXAMPLE=OFF
    -DEIC_WITH_JEPSEN=OFF
    -DEIC_WITH_ASAN=OFF
    -DEIC_WITH_TSAN=OFF
    -DEIC_WITH_METRICS=ON
    -DEIC_ENABLE_MOCK=OFF
    -DEIC_BUILD_MODE=master
    -DEIC_ENABLE_TEST=OFF
    -DEIC_ENABLE_FIBER=OFF
    -DEIC_ENABLE_GCOV=OFF 
    -DEIC_ENABLE_FIU=OFF
    -DEIC_ENABLE_ASAN=OFF
    -DBUILD_TESTING=ON 
    -DENABLE_PROFILER=ON
    -DEIC_ENABLE_RDMA=ON
    -DBYTERPC_ENABLE_IOBUF_MTHREADS=ON
    -DEIC_ENABLE_IPP=ON
    -DBYTE_ENABLE_METRICS2=ON
    -DEIC_ENABLE_DRIVECLIENT=OFF

  BUILD_COMMAND make -s -j${NUM_JOBS_PER_LIB}
  INSTALL_COMMAND make -s install
  LOG_CONFIGURE TRUE
  LOG_BUILD TRUE
  LOG_INSTALL TRUE
  LOG_OUTPUT_ON_FAILURE TRUE
)

message(STATUS "CMAKE_INSTALL_PREFIX: ${CMAKE_INSTALL_PREFIX}")
message(STATUS "CMAKE_CURRENT_BINARY_DIR: ${CMAKE_CURRENT_BINARY_DIR}")

set(EIC_BUILD_DIR ${CMAKE_CURRENT_BINARY_DIR}/eic/src/eic-build)
set(EIC_INSTALL_DIR ${CMAKE_INSTALL_PREFIX})

message(STATUS "EIC_BUILD_DIR: ${EIC_BUILD_DIR}")
message(STATUS "EIC_INSTALL_DIR: ${EIC_INSTALL_DIR}")

ExternalProject_Add_Step(${lib_name} copy_related
  DEPENDEES install
  COMMAND ${CMAKE_COMMAND} -E make_directory ${EIC_INSTALL_DIR}/lib
  COMMAND ${CMAKE_COMMAND} -E make_directory ${EIC_INSTALL_DIR}/include/eic/proto
  COMMAND ${CMAKE_COMMAND} -E copy ${EIC_BUILD_DIR}/src/proto/libeic_proto.a ${EIC_INSTALL_DIR}/lib/libeic_proto.a
  COMMAND ${CMAKE_COMMAND} -E copy ${EIC_BUILD_DIR}/src/comm/libeic_comm.a ${EIC_INSTALL_DIR}/lib/libeic_comm.a
  COMMAND ${CMAKE_COMMAND} -E copy ${EIC_BUILD_DIR}/src/master/libeic_master_lib.a ${EIC_INSTALL_DIR}/lib/libeic_master_lib.a
  COMMAND ${CMAKE_COMMAND} -E copy ${EIC_BUILD_DIR}/cityhash-build/lib/libcityhash.a ${EIC_INSTALL_DIR}/lib/libcityhash.a
  COMMAND ${CMAKE_COMMAND} -E copy ${EIC_BUILD_DIR}/lib/cache/shm_cache/libeic_shm_cache.a ${EIC_INSTALL_DIR}/lib/libeic_shm_cache.a
  COMMAND ${CMAKE_COMMAND} -E copy ${EIC_BUILD_DIR}/lib/cache/libeic_cache.a ${EIC_INSTALL_DIR}/lib/libeic_cache.a
  
  COMMAND ${CMAKE_COMMAND} -E copy ${EIC_BUILD_DIR}/src/proto/eic_agent.pb.cc ${EIC_INSTALL_DIR}/include/eic/proto/eic_agent.pb.cc
  COMMAND ${CMAKE_COMMAND} -E copy ${EIC_BUILD_DIR}/src/proto/eic_agent.pb.h ${EIC_INSTALL_DIR}/include/eic/proto/eic_agent.pb.h
  COMMAND ${CMAKE_COMMAND} -E copy ${EIC_BUILD_DIR}/src/proto/eic_base.pb.cc ${EIC_INSTALL_DIR}/include/eic/proto/eic_base.pb.cc
  COMMAND ${CMAKE_COMMAND} -E copy ${EIC_BUILD_DIR}/src/proto/eic_base.pb.h ${EIC_INSTALL_DIR}/include/eic/proto/eic_base.pb.h
  COMMAND ${CMAKE_COMMAND} -E copy ${EIC_BUILD_DIR}/src/proto/eic_client.pb.cc ${EIC_INSTALL_DIR}/include/eic/proto/eic_client.pb.cc
  COMMAND ${CMAKE_COMMAND} -E copy ${EIC_BUILD_DIR}/src/proto/eic_client.pb.h ${EIC_INSTALL_DIR}/include/eic/proto/eic_client.pb.h
  COMMAND ${CMAKE_COMMAND} -E copy ${EIC_BUILD_DIR}/src/proto/eic_fiu.pb.h ${EIC_INSTALL_DIR}/include/eic/proto/eic_fiu.pb.h
  COMMAND ${CMAKE_COMMAND} -E copy ${EIC_BUILD_DIR}/src/proto/eic_fiu.pb.cc ${EIC_INSTALL_DIR}/include/eic/proto/eic_fiu.pb.cc
  COMMAND ${CMAKE_COMMAND} -E copy ${EIC_BUILD_DIR}/src/proto/eic_master.pb.cc ${EIC_INSTALL_DIR}/include/eic/proto/eic_master.pb.cc
  COMMAND ${CMAKE_COMMAND} -E copy ${EIC_BUILD_DIR}/src/proto/eic_master.pb.h ${EIC_INSTALL_DIR}/include/eic/proto/eic_master.pb.h
)
