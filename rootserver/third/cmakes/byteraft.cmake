set(lib_name byteraft)
ExternalProject_Add(
  ${lib_name}
  GIT_REPOSITORY ******************:storage/byteraft.git
  GIT_TAG 2c844179709c33498c9d829198f2eed2eb057598
  GIT_SUBMODULES ""
  GIT_SHALLOW OFF
  PREFIX ${CMAKE_CURRENT_BINARY_DIR}/${lib_name}
  DOWNLOAD_DIR ${DOWNLOAD_DIR}
  PATCH_COMMAND
    git checkout -- . && git clean -f && patch -p1 < ${CMAKE_SOURCE_DIR}/patches/byteraft.patch
  BUILD_IN_SOURCE FALSE
  CMAKE_ARGS
    ${common_cmake_args}
    -DBYTERAFT_BUILD_TESTS=OFF
    -DBYTERAFT_WITH_EXAMPLE=OFF
    -DBYTERAFT_WITH_JEPSEN=OFF
    -DBYTERAFT_WITH_ASAN=OFF
    -DBYTERAFT_WITH_TSAN=OFF
    -DBYTERAFT_WITH_METRICS=ON
    -DROCK_BUILD_TESTS=OFF
  BUILD_COMMAND make -s -j${NUM_JOBS_PER_LIB}
  INSTALL_COMMAND make -s install
  LOG_CONFIGURE TRUE
  LOG_BUILD TRUE
  LOG_INSTALL TRUE
  LOG_OUTPUT_ON_FAILURE TRUE
)

ExternalProject_Add_Step(${lib_name} copy_related
  DEPENDEES install
  COMMAND ${CMAKE_COMMAND} -E make_directory "${CMAKE_INSTALL_PREFIX}/include/byteraft/proto"
  COMMAND ${CMAKE_COMMAND} -E copy ${CMAKE_CURRENT_BINARY_DIR}/byteraft/src/byteraft-build/byteraft/proto/raft.pb.h ${CMAKE_INSTALL_PREFIX}/include/byteraft/proto/raft.pb.h
  COMMAND ${CMAKE_COMMAND} -E copy ${CMAKE_CURRENT_BINARY_DIR}/byteraft/src/byteraft-build/byteraft/proto/raft_api.pb.h ${CMAKE_INSTALL_PREFIX}/include/byteraft/proto/raft_api.pb.h
  COMMAND ${CMAKE_COMMAND} -E copy ${CMAKE_CURRENT_BINARY_DIR}/byteraft/src/byteraft-build/byteraft/proto/meta.pb.h ${CMAKE_INSTALL_PREFIX}/include/byteraft/proto/meta.pb.h
  COMMAND ${CMAKE_COMMAND} -E copy ${CMAKE_CURRENT_BINARY_DIR}/byteraft/src/byteraft-build/byteraft/proto/snapshot.pb.h ${CMAKE_INSTALL_PREFIX}/include/byteraft/proto/snapshot.pb.h
  COMMAND ${CMAKE_COMMAND} -E copy ${CMAKE_CURRENT_BINARY_DIR}/byteraft/src/byteraft-build/byteraft/proto/libbyteraft_proto.a ${CMAKE_INSTALL_PREFIX}/lib/libbyteraft_proto.a
)