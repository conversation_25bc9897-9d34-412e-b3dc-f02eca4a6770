set(lib_name rock)
ExternalProject_Add(
  ${lib_name}
  GIT_REPOSITORY ******************:bytekv/rock.git
  GIT_TAG f7797f07ffa0178168b4705ba863b5a7f14a394e
  GIT_SUBMODULES ""
  GIT_SHALLOW OFF
  PREFIX ${CMAKE_CURRENT_BINARY_DIR}/${lib_name}
  DOWNLOAD_DIR ${DOWNLOAD_DIR}
  PATCH_COMMAND
    git checkout -- . && git clean -f && patch -p1 < ${CMAKE_SOURCE_DIR}/patches/rock.patch
  BUILD_IN_SOURCE FALSE
  CMAKE_ARGS
    ${common_cmake_args}
    -DROCK_BUILD_TESTS=OFF
  BUILD_COMMAND make -s -j${NUM_JOBS_PER_LIB}
  INSTALL_COMMAND make -s install
  LOG_CONFIGURE TRUE
  LOG_BUILD TRUE
  LOG_INSTALL TRUE
  LOG_OUTPUT_ON_FAILURE TRUE
)