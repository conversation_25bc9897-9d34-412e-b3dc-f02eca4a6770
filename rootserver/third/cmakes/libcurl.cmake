set(lib_name libcurl)
ExternalProject_Add(
  ${lib_name}
  URL http://tosv.byted.org/obj/cloudfs/dancedn-thirdparty/curl-7.55.1.tar.bz2
  URL_HASH MD5=8c153f282bbe482495214654cdcd4182
  DOWNLOAD_NAME curl-7.55.1.tar.bz2
  PREFIX ${CMAKE_CURRENT_BINARY_DIR}/${lib_name}
  DOWNLOAD_DIR ${DOWNLOAD_DIR}
  BUILD_IN_SOURCE TRUE
  CONFIGURE_COMMAND
    ${common_configure_envs}
    "LIBS=${LIBS}"
    ./configure --with-ssl --prefix=${CMAKE_INSTALL_PREFIX}
  BUILD_COMMAND make -s -j${NUM_JOBS_PER_LIB}
  INSTALL_COMMAND make -s install
  LOG_CONFIGURE TRUE
  LOG_BUILD TRUE
  LOG_INSTALL TRUE
)
