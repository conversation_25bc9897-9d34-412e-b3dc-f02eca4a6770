#include "root_server.h"

namespace bds {
namespace rootserver {
namespace cfs {

RootServer::RootServer() : is_running_(false) {
  // rs_core_service_ = std::make_shared<RSCoreServiceImpl> (this);
  // rs_dn_service_ = std::make_shared<RSDnServiceImpl> (this);
  cluster_manager_ =
      std::make_shared<ClusterManager>(this, FLAGS_cfs_cluster_uuid);
  space_manager_ = std::make_shared<SpaceManager>(this);
  heartbeat_scheduler_ =
      std::make_shared<HeartbeatScheduler>(this, "dn-heartbeat-scheduler");
  // flag param支持ONLY_EIC|ONLY_RS|ALL
  eic_master_ = std::make_shared<eic::master::EicMaster>();
}

RootServer::~RootServer() {
  if (is_running_) {
    Stop();
  }
}

RootServer* RootServer::GetRootServer() {
  static RootServer instance;
  return &instance;
}

byte::Status RootServer::Init() {
  // Initialization logic

  if (!eic_master_->Init()) {
    return byte::Status::Failed("failed to init master");
  }

  // if (!eic_master_.RegistServer(this)) {
  //     fprintf(stderr, "failed to register root server");
  //     return -1;
  // }

  // auto service_map = master.GetAdditionalServices();
  eic_master_->AddServices(new RSCoreServiceImpl(this), "");
  eic_master_->AddServices(new RSHttpServiceImpl(this),
                           "/rs/GetClusterInfo => GetClusterInfo,"
                           "/rs/AddSpace => AddSpace,"
                           "/rs/GetSpaceInfo => GetSpaceInfo");

  return byte::Status::OK();
}

byte::Status RootServer::Start() {
  LOG(DEBUG) << "start rootserver";
  is_running_ = true;

  // start only on leader start TODO
  space_manager_->Start();
  heartbeat_scheduler_->Start();

  if (!eic_master_->Start()) {
    LOG(DEBUG) << "failed to start master";
    return byte::Status::Failed("failed to start master");
  }
  LOG(DEBUG) << "rootserver heartbeat scheduler started success";

  return byte::Status::OK();
}

byte::Status RootServer::Stop() {
  is_running_ = false;
  eic_master_->Stop();

  space_manager_->Stop();
  heartbeat_scheduler_->Stop();

  return byte::Status::OK();
}

byte::Status RootServer::LoadConfig(const std::string& config_path) {
  // Load configuration from the specified path
  // This could involve reading a file, parsing JSON/YAML, etc.
  return byte::Status::OK();
}

}  // namespace cfs
}  // namespace rootserver
}  // namespace bds