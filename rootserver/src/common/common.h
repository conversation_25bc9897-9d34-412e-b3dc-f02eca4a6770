//=============================================================================
// Copyright (c) 2025-present, ByteDance Inc. All rights reserved.
// Author: <EMAIL>
// Date: 2025-05-27 20:18
// Description:
// This file defines the common class/functions used in the root server.
//=============================================================================

#pragma once

#include <byte/thread/waiter.h>

#include "common/flags.h"

#include <byterpc/util/endpoint.h>

namespace bds {
namespace rootserver {
namespace cfs {

#define IF_NOT_LEADER_THEN_RETURN(replica_driver)                              \
  if (!replica_driver-><PERSON><PERSON>ead<PERSON>()) {                                           \
    response->mutable_comm_resp()->set_status(                                 \
        bds::proto::Status::ERROR_RS_NOT_LEADER);                              \
    response->mutable_comm_resp()->set_msg(                                    \
        "Request rejected: Current node is not leader");                       \
    eic::master::MasterNodeId leader;                                          \
    if (replica_driver->GetLeaderNode(&leader)) {                              \
      response->mutable_comm_resp()->set_leader_addr(leader.addr_.ToString()); \
    }                                                                          \
    LOG(ERROR) << "I am not leader, leader is: " << leader.addr_.ToString();   \
    return;                                                                    \
  }

#define RS_TAKE_HANDLE_TIME_AND_RETURN_IF_TIMEOUT(req_ctx)                   \
  req_ctx->handle_time_us_ = byte::GetCurrentTimeInUs();                     \
  if (req_ctx->handle_time_us_ - req_ctx->start_time_us_ >=                  \
      FLAGS_rs_operation_timeout_in_us) {                                    \
    Done(bds::proto::ERROR_OPERATION_TIMEOUT, "timeout in operation queue"); \
    LOG(ERROR) << "timeout in operation queue";                              \
    return;                                                                  \
  }

#define HTTP_SVC_SET_RESP_BASE(cntl, resp, status, ret_msg)       \
  do {                                                            \
    (resp)->mutable_comm_resp()->set_status((status));            \
    (resp)->mutable_comm_resp()->set_msg((ret_msg));              \
    (cntl)->response_attachment().clear();                        \
    (cntl)->response_attachment().append(byte::pb2json(*(resp))); \
  } while (false);

#define HTTP_SVC_PARSE_JSON_REQ(cntl, req)                                  \
  do {                                                                      \
    std::string req_str;                                                    \
    req_str.resize((cntl)->request_attachment().length());                  \
    (cntl)->request_attachment().copy_to(const_cast<char*>(req_str.data()), \
                                         req_str.size());                   \
    byte::json2pb((req), req_str.data(), req_str.size());                   \
  } while (false);

#define HTTP_SVC_CHECK_AND_PARSE_REQ(cntl, req, resp)                   \
  do {                                                                  \
    if ((cntl)->request_attachment().empty()) {                         \
      HTTP_SVC_SET_RESP_BASE((cntl), (resp), bds::proto::Status::ERROR, \
                             "request body is empty");                  \
      return;                                                           \
    }                                                                   \
    try {                                                               \
      HTTP_SVC_PARSE_JSON_REQ((cntl), (req));                           \
    } catch (std::exception & e) {                                      \
      std::ostringstream oss;                                           \
      oss << "parse json failed: " << e.what();                         \
      HTTP_SVC_SET_RESP_BASE((cntl), (resp), bds::proto::Status::ERROR, \
                             oss.str());                                \
      return;                                                           \
    }                                                                   \
  } while (false);

#define HTTP_SVC_FORWARD_TO_RPC_SVC(controller, done, METHOD)                \
  do {                                                                       \
    byterpc::util::ClosureGuard done_guard((done));                          \
    auto cntl = reinterpret_cast<byterpc::Controller*>((controller));        \
    BYTE_CHECK_NOTNULL(cntl);                                                \
    auto req = std::make_unique<bds::proto::METHOD##Request>();              \
    auto resp = std::make_unique<bds::proto::METHOD##Response>();            \
    HTTP_SVC_CHECK_AND_PARSE_REQ(cntl, req.get(), resp.get());               \
    auto cb =                                                                \
        NewOnServiceDone(cntl, req.get(), resp.get(), done_guard.release()); \
    core_svc_->METHOD((controller), req.release(), resp.release(), cb);      \
  } while (false);


}  // namespace cfs
}  // namespace rootserver
}  // namespace bds