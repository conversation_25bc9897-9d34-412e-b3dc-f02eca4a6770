//=============================================================================
// Copyright (c) 2025-present, ByteDance Inc. All rights reserved.
// Author: <EMAIL>
// Date: 2025-05-27 20:18
// Description:
// This file defines the common class/functions used in the root server.
//=============================================================================

#pragma once

#include <byte/thread/waiter.h>
#include <byterpc/util/endpoint.h>

#include "common/flags.h"

namespace bds {
namespace rootserver {
namespace cfs {

inline std::string EndpointToString(byterpc::util::EndPoint end_point) {
  return byterpc::util::endpoint2str(end_point).c_str();
}

inline int String2Endpoint(const std::string& ip_and_port_str,
                           byterpc::util::EndPoint* point) {
  return byterpc::util::str2endpoint(ip_and_port_str.c_str(), point);
}

inline int String2Endpoint(const std::string& ip_str, int port,
                           byterpc::util::EndPoint* point) {
  return byterpc::util::str2endpoint(ip_str.c_str(), port, point);
}


}  // namespace cfs
}  // namespace rootserver
}  // namespace bds