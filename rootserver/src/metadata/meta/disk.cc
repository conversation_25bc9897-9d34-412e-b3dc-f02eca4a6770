//
// Created by ByteDance on 2025/5/24.
//

#include "metadata/meta/disk.h"
#include "metadata/meta/node.h"

namespace bds {
namespace rootserver {
namespace cfs {

byterpc::util::EndPoint Disk::addr() {
  byte::ScopedLiteRWLock rlock(&mutex_, 'r');
  return node_ptr_->getAddr();
}

bool Disk::isFull() {
  // Implementation to check if disk is full
  // For example:
  byte::ScopedLiteRWLock rlock(&mutex_, 'r');
  return freeUsage() < 0.1; // Less than 10% free space
}

bool Disk::isFullAdvanced() {
  GroupReplicaVector group_replica_list;
  getGroupReplicaList(&group_replica_list);

  uint64_t rewrite_overflow_size = 0;
  for (auto& group_replica : group_replica_list) {
    rewrite_overflow_size += 64 * 1024;
  }

  double max_disk_usage = 0.95;
  std::shared_ptr<Node> node_ptr = node();
  if (node_ptr) {
  }

  byte::ScopedLiteRWLock rlock(&mutex_, 'r');
  return (diskFreeSize() + rewrite_overflow_size) <= diskReserveSize() ||
         usage() >= max_disk_usage;
}

int Disk::getSpaceGroupReplicaNum(const std::string& space_name) {
  byte::ScopedLiteRWLock rlock(&mutex_, 'r');
  int count = 0;
  for (auto& pair : group_replica_map_) {
    if (pair.second->spaceName() == space_name) {
      count++;
    }
  }
  return count;
}

std::shared_ptr<GroupReplica> Disk::getGroupReplica(const uint64_t volume_id) {
  byte::ScopedLiteRWLock rlock(&mutex_, 'r');
  auto iter = group_replica_map_.find(volume_id);
  if (iter != group_replica_map_.end()) {
    return iter->second;
  }
  return nullptr;
}

int Disk::addGroupReplica(const std::shared_ptr<GroupReplica>& group_replica_ptr) {
  int ret = -1;

  if (!group_replica_ptr) {
    return ret;
  }

  // Get size information outside of the lock
  auto group_size = group_replica_ptr->getSize();

  byte::ScopedLiteRWLock wlock(&mutex_, 'w');
  uint64_t volume_id = group_replica_ptr->volumeId();
  auto iter = group_replica_map_.find(volume_id);
  if (iter == group_replica_map_.end()) {
    group_replica_map_[volume_id] = group_replica_ptr;
    calc_used_size_ += group_size;
    disk_proto_.set_block_group_count(disk_proto_.block_group_count() + 1);
    ret = 0;
  } else if (iter->second == group_replica_ptr) {

    ret = 0;
  }

  return ret;
}

int Disk::dropGroupReplica(const std::shared_ptr<GroupReplica>& group_replica_ptr) {
  int ret = -1;

  if (!group_replica_ptr) {
    return ret;
  }

  // Get size information outside of the lock
  auto group_size = group_replica_ptr->getSize();
  auto volume_id = group_replica_ptr->volumeId();

  byte::ScopedLiteRWLock wlock(&mutex_, 'w');
  auto iter = group_replica_map_.find(volume_id);
  if (iter != group_replica_map_.end()) {

    if (iter->second == group_replica_ptr) {
      group_replica_map_.erase(iter);
      calc_used_size_ = (calc_used_size_ >= group_size) ?
                        calc_used_size_ - group_size : 0;
      if (disk_proto_.block_group_count() > 0) {
        disk_proto_.set_block_group_count(disk_proto_.block_group_count() - 1);
      }
      ret = 0;
    }
  }

  return ret;
}

void Disk::getExistGroupReplicas(GroupReplicaVector* group_replicas) {
  byte::ScopedLiteRWLock rlock(&mutex_, 'r');
  for (auto& pair : group_replica_map_) {
    group_replicas->push_back(pair.second);
  }
}

void Disk::getSpaceExistGroupReplicas(const std::string& space_name,
                            GroupReplicaVector* group_replicas) {
  byte::ScopedLiteRWLock rlock(&mutex_, 'r');
  for (auto& pair : group_replica_map_) {
    if (pair.second->spaceName() == space_name) {
      group_replicas->push_back(pair.second);
    }
  }
}

void Disk::getGroupReplicaList(GroupReplicaVector* group_replica_list) {
  byte::ScopedLiteRWLock rlock(&mutex_, 'r');
  for (auto& pair : group_replica_map_) {
    group_replica_list->push_back(pair.second);
  }
}


int Disk::getSpaceGroupReplicaNumByName(const std::string& space_name) {
  GroupReplicaVector group_replica_list;
  this->getGroupReplicaList(&group_replica_list);
  int ret = 0;
  for (auto& group_replica : group_replica_list) {
    if (group_replica->spaceName() == space_name) {
      ++ret;
    }
  }
  return ret;
}

}
}
}