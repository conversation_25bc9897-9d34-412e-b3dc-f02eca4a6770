#pragma once

#include <string>
#include <unordered_map>
#include <utility>
#include <vector>

#include <byterpc/util/endpoint.h>
#include <byte/concurrent/lite_lock.h>
#include <byte/include/macros.h>

#include "rootserver.pb.h"
#include "common/common.h"
#include "metadata/meta/block_group.h"
#include "metadata/meta/group_replica.h"

namespace bds {
namespace rootserver {
namespace cfs {

// Forward declaration for FLAGS_disk_reserve_percent
// This will be defined in common/flags.h but we don't include it here to avoid circular dependencies

class Node;

using DiskStatusProto = ::bds::proto::DiskStatusProto;
using DiskInfoProto = ::bds::proto::DiskInfoProto;
using DiskMediaType = ::bds::proto::MediaTypeProto;

using DISKID = uint32_t;

struct DiskStat {
  DiskStat() {
  }

  DiskStat(DISKID id_,
           uint64_t disk_total_size_,
           uint64_t disk_free_size_,
           uint64_t used_size_,
           uint64_t disk_ioutil_,
           uint64_t read_qps_,
           uint64_t read_throughput_,
           uint64_t write_qps_,
           uint64_t write_throughput_) :
    id(id_),
    disk_total_size(disk_total_size_),
    disk_free_size(disk_free_size_),
    used_size(used_size_),
    disk_ioutil(disk_ioutil_),
    read_qps(read_qps_),
    read_throughput(read_throughput_),
    write_qps(write_qps_),
    write_throughput(write_throughput_) {
  }

  void set_calc_used_size(uint64_t calc_used_size_) {
    has_calc_used_size = true;
    calc_used_size = calc_used_size_;
  }

  void set_reserved_size(uint64_t reserved_size_) {
    reserved_size = reserved_size_;
  }

  int id = -1;
  uint64_t disk_total_size = 0;
  uint64_t disk_free_size = 0;
  uint64_t used_size = 0;
  uint64_t disk_ioutil = 0;
  uint64_t read_qps = 0;
  uint64_t read_throughput = 0;
  uint64_t write_qps = 0;
  uint64_t write_throughput = 0;
  // for special case: archive volume used size calculation
  bool has_calc_used_size = false;
  uint64_t calc_used_size = 0;
  uint64_t reserved_size = 0;
};

class Disk {
public:
  Disk(const DISKID id, const std::shared_ptr<Node>& node)
    : node_ptr_(node) {
    disk_proto_.set_disk_id(id);
    disk_proto_.set_is_disk_partition(false);  // Set default value
    disk_proto_.set_block_group_count(0);  // Initialize block group count
  }

  Disk(const DISKID id, const uint64_t time, const std::shared_ptr<Node>& node) :
    node_ptr_(node) {
    disk_proto_.set_disk_id(id);
    disk_proto_.set_create_time(time);
    disk_proto_.set_is_disk_partition(false);  // Set default value
    disk_proto_.set_block_group_count(0);  // Initialize block group count
  }

  void update_disk_stat(const DiskStat& disk_stat) {
    total_size_ = disk_stat.disk_total_size;  // Keep this for backward compatibility
    disk_proto_.set_total_in_bytes(disk_stat.disk_total_size);
    disk_proto_.set_avail_in_bytes(disk_stat.disk_free_size);
    disk_proto_.set_used_in_bytes(disk_stat.used_size);
    disk_ioutil_ = disk_stat.disk_ioutil;
    
    // Update read stats using read_stat field
    auto* read_stat = disk_proto_.mutable_read_stat();
    read_stat->set_qps(disk_stat.read_qps);
    read_stat->set_throughput(disk_stat.read_throughput);
    
    // Update write stats using write_stat field
    auto* write_stat = disk_proto_.mutable_write_stat();
    write_stat->set_qps(disk_stat.write_qps);
    write_stat->set_throughput(disk_stat.write_throughput);
    
    reserved_size_ = disk_stat.reserved_size;
    if (disk_stat.has_calc_used_size) {
      byte::ScopedLiteRWLock wlock(&mutex_, 'w');
      calc_used_size_ = disk_stat.calc_used_size;
    }
  }

  bool isFull();
  bool isFullAdvanced();

  double usage() const {
    byte::ScopedLiteRWLock rlock(&mutex_, 'r');
    //    return (double)used_size_ / (double)disk_total_size_;
    return (double)calc_used_size_ / (double)(total_size_ + 1);
  }

  double logicUsage() const {
    byte::ScopedLiteRWLock rlock(&mutex_, 'r');
    return (double)calc_used_size_ / (double)(logicTotalSize() + 1);
  }

  double freeUsage() const {
    byte::ScopedLiteRWLock rlock(&mutex_, 'r');
    return (double)disk_proto_.avail_in_bytes() / (double)(disk_proto_.total_in_bytes() + 1);
  }

  uint64_t createTime() const {
    byte::ScopedLiteRWLock rlock(&mutex_, 'r');
    return disk_proto_.create_time();;
  }

  uint64_t diskFreeSize() const {
    byte::ScopedLiteRWLock rlock(&mutex_, 'r');
    return disk_proto_.avail_in_bytes();
  }

  uint64_t usedSize() const {
    byte::ScopedLiteRWLock rlock(&mutex_, 'r');
    return calc_used_size_;
  }

  uint64_t reservedSize() const {
    byte::ScopedLiteRWLock rlock(&mutex_, 'r');
    return reserved_size_;
  }

  uint64_t diskTotalSize() const {
    byte::ScopedLiteRWLock rlock(&mutex_, 'r');
    return disk_proto_.total_in_bytes();
  }

  uint64_t logicTotalSize() const {
    byte::ScopedLiteRWLock rlock(&mutex_, 'r');
    //total_size is enough now.
    //when need use : min(total_size_, disk_proto_.avail_in_bytes() + calc_used_size_);
    return totalSize();
  }

  uint64_t reportedUsedSize() const {
    byte::ScopedLiteRWLock rlock(&mutex_, 'r');
    return disk_proto_.used_in_bytes();
  }

  uint64_t diskReserveSize() const {
    byte::ScopedLiteRWLock rlock(&mutex_, 'r');
    // Use a hardcoded value of 5% instead of FLAGS_disk_reserve_percent
    // to avoid build issues with undefined flags
    const int default_reserve_percent = 5;
    return (disk_proto_.total_in_bytes() * default_reserve_percent) / 100;
  }

  uint64_t ioutil() const {
    byte::ScopedLiteRWLock rlock(&mutex_, 'r');
    return disk_ioutil_;
  }

  uint64_t readqps() const {
    byte::ScopedLiteRWLock rlock(&mutex_, 'r');
    return disk_proto_.read_stat().qps();
  }

  uint64_t readThroughput() const {
    byte::ScopedLiteRWLock rlock(&mutex_, 'r');
    return disk_proto_.read_stat().throughput();
  }

  uint64_t writeqps() const {
    byte::ScopedLiteRWLock rlock(&mutex_, 'r');
    return disk_proto_.write_stat().qps();
  }

  uint64_t writeThroughput() const {
    byte::ScopedLiteRWLock rlock(&mutex_, 'r');
    return disk_proto_.write_stat().throughput();
  }

  byterpc::util::EndPoint addr();

  DISKID diskId() const {
    byte::ScopedLiteRWLock rlock(&mutex_, 'r');
    return disk_proto_.disk_id();
  }

  std::shared_ptr<Node> node() {
    return node_ptr_;
  }

  void setState(const DiskStatusProto& state) {
    byte::ScopedLiteRWLock wlock(&mutex_, 'w');
    disk_proto_.set_status(state);
  }

  DiskStatusProto state() {
    byte::ScopedLiteRWLock rlock(&mutex_, 'r');
    return disk_proto_.status();
  }

  void setDiskMediaType(const DiskMediaType type) {
    disk_proto_.set_media_type(type);
  }

  DiskMediaType diskMediaType() const {
    return disk_proto_.media_type();
  }

  size_t getGroupReplicaNum() const {
    return disk_proto_.block_group_count();
  }

  int getSpaceGroupReplicaNum(const std::string& space_name);
  int getSpaceGroupReplicaNumByName(const std::string& space_name);
  std::shared_ptr<GroupReplica> getGroupReplica(const uint64_t volume_id);
  int addGroupReplica(const std::shared_ptr<GroupReplica>& group_replica_ptr);
  int dropGroupReplica(const std::shared_ptr<GroupReplica>& group_replica_ptr);

  void getExistGroupReplicas(GroupReplicaVector* group_replicas);
  void getSpaceExistGroupReplicas(const std::string& space_name,
                             GroupReplicaVector* group_replicas);
  void getGroupReplicaList(GroupReplicaVector* group_replica_list);

  void addCalcUsedSize(uint64_t p) {
    byte::ScopedLiteRWLock wlock(&mutex_, 'w');
    calc_used_size_ += p;
  }

  void dropCalcUsedSize(uint64_t p) {
    byte::ScopedLiteRWLock wlock(&mutex_, 'w');
    if (calc_used_size_ >= p) {
      calc_used_size_ -= p;
    }
  }

  uint64_t totalSize() const {
    byte::ScopedLiteRWLock rlock(&mutex_, 'r');
    return total_size_; // Keep this for backward compatibility
  }

  // Additional utility methods following aries patterns
  void serialize(DiskInfoProto* disk_info) {
    byte::ScopedLiteRWLock rlock(&mutex_, 'r');
    *disk_info = disk_proto_;
  }

private:
  DiskInfoProto disk_proto_;
  std::shared_ptr<Node> node_ptr_;
  uint64_t total_size_ = 0;
  uint64_t calc_used_size_ = 0; //calculated used size according existing group replicas.
  uint64_t disk_ioutil_ = 0; // disk ioutil reported from dn
  uint64_t reserved_size_; //disk space reserve

  GroupReplicaMap group_replica_map_;
  mutable byte::LiteRWLock mutex_;
};

typedef std::unordered_map<int, std::shared_ptr<Disk>> DiskMap; //disk id->disk
typedef std::vector<std::shared_ptr<Disk>> DiskVector;
}
}
}