#pragma once

#include <byte/concurrent/lite_lock.h>
#include <byte/include/macros.h>
#include <byterpc/util/endpoint.h>

#include <memory>
#include <mutex>
#include <string>
#include <unordered_map>
#include <utility>
#include <vector>

#include "rootserver.pb.h"

namespace bds {
namespace rootserver {
namespace cfs {

using GroupStatusProto = ::bds::proto::GroupStatusProto;
using GroupReplicaProto = ::bds::proto::GroupReplicaProto;
using GroupProto = ::bds::proto::GroupProto;

class GroupReplica {
 public:
  explicit GroupReplica(const GroupReplicaProto& pb)
      : group_id_(pb.group_id()), index_(pb.index()) {
    byte::ScopedLiteRWLock wlock(&mutex_, 'w');
    pb_ = pb;
  }

  // Constructor with space name for better compatibility
  GroupReplica(const GroupReplicaProto& pb, const std::string& space_name)
      : group_id_(pb.group_id()), index_(pb.index()), space_name_(space_name) {
    byte::ScopedLiteRWLock wlock(&mutex_, 'w');
    pb_ = pb;
  }

  ~GroupReplica() = default;

  static uint64_t ReplicaId(uint32_t group_id, uint32_t index) {
    return static_cast<uint64_t>(group_id) << 32 | index;
  }

  uint64_t ReplicaId() {
    return ReplicaId(GroupId(), Index());
  }

  uint32_t Index() const {
    return index_;
  }

  uint64_t CtimeUs() {
    byte::ScopedLiteRWLock rlock(&mutex_, 'r');
    return pb_.ctime_us();
  }

  void SetMtimeUs(uint64_t mtime_us) {
    byte::ScopedLiteRWLock wlock(&mutex_, 'w');
    pb_.set_mtime_us(mtime_us);
  }

  uint64_t GroupId() const {
    return group_id_;
  }

  void GetAddr(uint32_t* dn_id, uint32_t* disk_id) {
    byte::ScopedLiteRWLock rlock(&mutex_, 'r');
    if (dn_id) {
      *dn_id = pb_.dn_id();
    }
    if (disk_id) {
      *disk_id = pb_.disk_id();
    }
  }

  uint64_t LastCheckTime() const {
    return last_check_time_;
  }

  bool UpdateLastNormalTime(uint64_t normal_time) {
    last_normal_time_ = normal_time;
    return true;
  }

  uint64_t LastNormalTime() const {
    return last_normal_time_;
  }

  bool UpdateLastUnnormalTime(uint64_t unnormal_time) {
    if (last_unnormal_time_ == 0 || unnormal_time == 0) {
      last_unnormal_time_ = unnormal_time;
    }
    return true;
  }

  uint64_t LastUnnormalTime() const {
    return last_unnormal_time_;
  }

  void GetReplicaProto(GroupReplicaProto* pb) {
    byte::ScopedLiteRWLock rlock(&mutex_, 'r');
    *pb = pb_;
  }

  std::string ToString() const {
    return "GroupReplica{group_id=" + std::to_string(GroupId()) +
           ", index=" + std::to_string(index_) + "}";
  }

  bool CheckEqualReplica(const GroupReplicaProto& pb) {
    byte::ScopedLiteRWLock rlock(&mutex_, 'r');
    return pb_.SerializeAsString() == pb.SerializeAsString();
  }

  // Methods to support vlet-like functionality for compatibility with aries patterns
  uint64_t getSize() const {
    // Return a default size for group replica
    // This could be made configurable or calculated based on actual data
    return 1024 * 1024; // 1MB default size
  }

  std::string spaceName() const {
    // Return the space name if set, otherwise default
    return space_name_.empty() ? "default_space" : space_name_;
  }

  void setSpaceName(const std::string& space_name) {
    space_name_ = space_name;
  }

  uint64_t volumeId() const {
    // Map group_id to volume_id for compatibility
    return group_id_;
  }

 private:
  const uint32_t group_id_ = 0;
  const uint32_t index_ = 0;
  uint64_t last_check_time_ = 0;
  uint64_t last_normal_time_ = 0;
  uint64_t last_unnormal_time_ = 0;
  std::string space_name_;  // Space name for this replica
  byte::LiteRWLock mutex_;
  GroupReplicaProto pb_;
};

typedef std::shared_ptr<GroupReplica> GroupReplicaPtr;
typedef std::vector<std::shared_ptr<GroupReplica>> GroupReplicaVector;
typedef std::unordered_map<uint64_t, std::shared_ptr<GroupReplica>>
    GroupReplicaMap;

}  // namespace cfs
}  // namespace rootserver
}  // namespace bds