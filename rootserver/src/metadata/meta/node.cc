/**
 * @file datanode.cc
 * @brief Implementation of DataNode class for ByteDance Storage system
 */
#include "metadata/meta/node.h"

namespace bds {
namespace rootserver {
namespace cfs {

Node::Node(const NodeOptions& options) {
    node_info_.set_group_name(options.group_name);
    node_info_.set_az_name(options.az_name);
    node_info_.set_idc_name(options.idc_name);
    node_info_.set_rack_name(options.rack_name);

    node_info_.set_status(options.state);
    node_info_.set_up_time(options.create_time);
    offline_time_ = options.create_time / 1000000L;
    setAddr(options.addr);
}

byterpc::util::EndPoint Node::addr() const {
  return String2Endpoint(node_info_.host(), node_info_.port());
}

void Node::setAddr(const byterpc::util::EndPoint& endpoint) {
    node_info_.set_host(byterpc::util::ip2str(endpoint.ip).c_str());
    node_info_.set_port(endpoint.port);
}

uint64_t Node::createTime() const {
    return node_info_.up_time();
}

std::string Node::groupName() const {
    byte::ScopedLiteRWLock rlock(&mutex_, 'r');
    return node_info_.group_name();
}

void Node::setGroupName(const std::string& group_name) {
    byte::ScopedLiteRWLock wlock(&mutex_, 'w');
    node_info_.set_group_name(group_name);
}

std::string Node::azName() const {
    byte::ScopedLiteRWLock rlock(&mutex_, 'r');
    return node_info_.az_name();
}

void Node::setAzName(const std::string& az_name) {
    byte::ScopedLiteRWLock wlock(&mutex_, 'w');
    node_info_.set_az_name(az_name);
}

std::string Node::idcName() const {
    byte::ScopedLiteRWLock rlock(&mutex_, 'r');
    return node_info_.idc_name();
}

void Node::setIdcName(const std::string& idc_name) {
    byte::ScopedLiteRWLock wlock(&mutex_, 'w');
    node_info_.set_idc_name(idc_name);
}

std::string Node::rackName() const {
    byte::ScopedLiteRWLock rlock(&mutex_, 'r');
    return node_info_.rack_name();
}

void Node::setRackName(const std::string& rack_name) {
    byte::ScopedLiteRWLock wlock(&mutex_, 'w');
    node_info_.set_rack_name(rack_name);
}

uint64_t Node::offlineTime() const {
    return offline_time_;
}

void Node::setOfflineTime(uint64_t time) {
    offline_time_ = time;
}

uint64_t Node::onlineTime() const {
    return online_time_;
}

void Node::setOnlineTime(uint64_t time) {
    online_time_ = time;
}

void Node::setState(DanceDNStatusProto state) {
    node_info_.set_status(state);
}

DanceDNStatusProto Node::state() const {
    return node_info_.status();
}

uint64_t Node::maxIoutil() const {
    return max_ioutil_;
}

int Node::maxIoutilDiskId() const {
    return max_ioutil_disk_id_;
}

double Node::minDiskUsage() {
    byte::ScopedLiteRWLock rlock(&mutex_, 'r');
    double min_usage = 1.0;
    for (const auto& disk_pair : disk_map_) {
        double disk_usage = disk_pair.second->usage();
        min_usage = std::min(min_usage, disk_usage);
    }
    return min_usage;
}

double Node::usage() {
    byte::ScopedLiteRWLock rlock(&mutex_, 'r');
    if (disk_total_size_ == 0) {
        return 1.0;
    }
    return (double)(disk_total_size_ - disk_free_size_) / (double)disk_total_size_;
}

double Node::freeUsage() {
    return (double)disk_free_size_ / (double)(disk_total_size_ + 1);
}

uint64_t Node::freeSpace() {
    return disk_free_size_;
}

bool Node::isDiskSafemode() {
    byte::ScopedLiteRWLock rlock(&mutex_, 'r');
    return is_disk_safemode_;
}

uint64_t Node::diskSafemodeUptime() {
    byte::ScopedLiteRWLock rlock(&mutex_, 'r');
    return disk_safemode_uptime_;
}

void Node::setDiskSafemode(bool is_safemode, uint64_t time) {
    byte::ScopedLiteRWLock wlock(&mutex_, 'w');
    is_disk_safemode_ = is_safemode;
    disk_safemode_uptime_ = time;
}

uint64_t Node::usedSize() const {
    return calc_used_size_;
}

uint64_t Node::totalSize() const {
    return disk_total_size_;
}

uint64_t Node::freeSize() const {
    return disk_free_size_;
}

uint64_t Node::diskTotalSize() const {
    return disk_total_size_;
}

uint64_t Node::lastHeartbeatTime() const {
    return node_info_.last_heartbeat_time();
}

void Node::setLastHeartbeatTime(uint64_t time) {
    node_info_.set_last_heartbeat_time(time);
}

void Node::setDnId(const std::string& id) {
    node_info_.set_dn_id(id);
}

std::string Node::dnId() const {
    return node_info_.dn_id();
}

void Node::updateDiskStat(const std::vector<DiskStat>& disk_stat_vector) {
    byte::ScopedLiteRWLock wlock(&mutex_, 'w');

    disk_total_size_ = 0;
    disk_free_size_ = 0;
    max_ioutil_ = 0;
    max_ioutil_disk_id_ = -1;

    for (const auto& disk_stat : disk_stat_vector) {
        auto disk_iter = disk_map_.find(disk_stat.id);
        if (disk_iter != disk_map_.end()) {
            auto& disk = disk_iter->second;

            disk->update_disk_stat(disk_stat);

            disk_total_size_ += disk->diskTotalSize();
            disk_free_size_ += disk->diskFreeSize();

            uint64_t current_ioutil = disk->ioutil();
            if (current_ioutil > max_ioutil_) {
                max_ioutil_ = current_ioutil;
                max_ioutil_disk_id_ = disk->diskId();
            }
        }
    }

    calc_used_size_ = disk_total_size_ - disk_free_size_;

    if (disk_total_size_ > 0) {
        usage_ = (double)calc_used_size_ / (double)disk_total_size_;
    } else {
        usage_ = 1.0;
    }
}

std::shared_ptr<Disk> Node::getDisk(int disk_id) {
    byte::ScopedLiteRWLock rlock(&mutex_, 'r');
    auto it = disk_map_.find(disk_id);
    if (it == disk_map_.end()) {
        return nullptr;
    }
    return it->second;
}

void Node::getDiskList(DiskVector* disk_list) {
    if (!disk_list) {
        return;
    }

    byte::ScopedLiteRWLock rlock(&mutex_, 'r');
    for (const auto& disk_pair : disk_map_) {
        disk_list->push_back(disk_pair.second);
    }
}

int Node::addDisk(const std::shared_ptr<Disk>& disk_ptr) {
    if (!disk_ptr) {
        return -1;
    }

    byte::ScopedLiteRWLock wlock(&mutex_, 'w');
    auto result = disk_map_.insert(std::make_pair(disk_ptr->diskId(), disk_ptr));

    if (result.second) {
        disk_total_size_ += disk_ptr->diskTotalSize();
        disk_free_size_ += disk_ptr->diskFreeSize();

        if (disk_total_size_ > 0) {
            usage_ = (double)(disk_total_size_ - disk_free_size_) / (double)disk_total_size_;
        }

        return 0;
    }

    return -1;
}

int Node::dropDisk(int disk_id) {
    byte::ScopedLiteRWLock wlock(&mutex_, 'w');

    // Check if in safemode
    if (is_disk_safemode_) {
        return -2; // Cannot drop disk in safemode
    }

    auto disk_iter = disk_map_.find(disk_id);
    if (disk_iter == disk_map_.end()) {
        return -1; // Disk not found
    }

    // Update stats before removing - use diskTotalSize() and diskFreeSize() methods
    disk_total_size_ -= disk_iter->second->diskTotalSize();
    disk_free_size_ -= disk_iter->second->diskFreeSize();

    // Remove disk
    disk_map_.erase(disk_iter);

    // Update usage
    if (disk_total_size_ > 0) {
        usage_ = (double)(disk_total_size_ - disk_free_size_) / (double)disk_total_size_;
    } else {
        usage_ = 1.0;
    }

    return 0;
}

void Node::getExistGroupReplicas(GroupReplicaVector* group_replicas) {
    if (!group_replicas) {
        return;
    }

    byte::ScopedLiteRWLock rlock(&mutex_, 'r');
    for (const auto& disk_pair : disk_map_) {
        disk_pair.second->getExistGroupReplicas(group_replicas);
    }
}

void Node::getGroupReplicaList(GroupReplicaVector* group_replicas) {
    if (!group_replicas) {
        return;
    }

    byte::ScopedLiteRWLock rlock(&mutex_, 'r');
    for (const auto& disk_pair : disk_map_) {
        disk_pair.second->getGroupReplicaList(group_replicas);
    }
}

size_t Node::getGroupReplicaNum() {
    size_t total = 0;

    byte::ScopedLiteRWLock rlock(&mutex_, 'r');
    for (const auto& disk_pair : disk_map_) {
        total += disk_pair.second->getGroupReplicaNum();
    }

    return total;
}

std::shared_ptr<GroupReplica> Node::getGroupReplica(uint64_t volume_id) {
    byte::ScopedLiteRWLock rlock(&mutex_, 'r');

    for (const auto& disk_pair : disk_map_) {
        auto group_replica = disk_pair.second->getGroupReplica(volume_id);
        if (group_replica) {
            return group_replica;
        }
    }

    return nullptr;
}

void Node::serialize(DanceDNInfoProto* node_info) {
    byte::ScopedLiteRWLock rlock(&mutex_, 'r');
    *node_info = node_info_;
}

} // namespace cfs
} // namespace rootserver
} // namespace bds