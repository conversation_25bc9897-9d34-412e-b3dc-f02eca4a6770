
#include "metadata/meta/block_group.h"

#include <sstream>

namespace bds {
namespace rootserver {
namespace cfs {

BlockGroup::BlockGroup(const GroupProto& group_pb)
    : group_id_(group_pb.group_id()), space_id_(group_pb.space_id()) {
  byte::ScopedLiteRWLock wlock(&mutex_, 'w');
  pb_ = group_pb;
}

BlockGroup::~BlockGroup() = default;

// 更新replica_list为group_rep_pb，并且设置add_rep和remove_rep变量
bool BlockGroup::UpdateGroupReplica(const GroupReplicaProto& group_rep_pb,
                                    GroupReplicaPtr* add_rep,
                                    GroupReplicaPtr* remove_rep) {
  byte::ScopedLiteRWLock wlock(&mutex_, 'w');
  uint32_t index = group_rep_pb.index();

  // 查找并移除旧的replica
  for (int i = 0; i < pb_.replica_list_size(); ++i) {
    if (pb_.replica_list(i).index() == index) {
      if (remove_rep) {
        *remove_rep = std::make_shared<GroupReplica>(pb_.replica_list(i));
      }
      // 移除旧的replica
      pb_.mutable_replica_list()->DeleteSubrange(i, 1);
      break;
    }
  }

  // 添加新的replica
  GroupReplicaProto* new_replica = pb_.add_replica_list();
  *new_replica = group_rep_pb;

  // 移除的replica应该加入history_replicas
  if (remove_rep) {
    GroupReplicaProto* history_replica = pb_.add_history_replicas();
    (*remove_rep)->GetReplicaProto(history_replica);
  }

  if (add_rep) {
    *add_rep = std::make_shared<GroupReplica>(group_rep_pb);
  }

  pb_.set_version(pb_.version() + 1);
  pb_.set_mtime_us(group_rep_pb.mtime_us());

  return true;
}

GroupStatusProto BlockGroup::Status() {
  byte::ScopedLiteRWLock rlock(&mutex_, 'r');
  return pb_.status();
}

uint32_t BlockGroup::Version() {
  byte::ScopedLiteRWLock rlock(&mutex_, 'r');
  return pb_.version();
}

void BlockGroup::GetGroupProto(GroupProto* pb) {
  byte::ScopedLiteRWLock rlock(&mutex_, 'r');
  *pb = pb_;
}

void BlockGroup::ListReplicas(std::vector<GroupReplicaPtr>* reps,
                              uint32_t* version) {
  byte::ScopedLiteRWLock rlock(&mutex_, 'r');
  if (version) {
    *version = pb_.version();
  }
  if (reps) {
    reps->clear();
    for (int i = 0; i < pb_.replica_list_size(); ++i) {
      reps->push_back(std::make_shared<GroupReplica>(pb_.replica_list(i)));
    }
  }
}

void BlockGroup::ListHistoryReplicas(std::vector<GroupReplicaPtr>* reps,
                                     uint32_t* version) {
  byte::ScopedLiteRWLock rlock(&mutex_, 'r');
  if (version) {
    *version = pb_.version();
  }
  if (reps) {
    reps->clear();
    for (int i = 0; i < pb_.history_replicas_size(); ++i) {
      reps->push_back(std::make_shared<GroupReplica>(pb_.history_replicas(i)));
    }
  }
}

void BlockGroup::GetReplicas(std::map<uint32_t, GroupReplicaPtr>* replicas,
                             uint32_t* version) {
  byte::ScopedLiteRWLock rlock(&mutex_, 'r');
  if (version) {
    *version = pb_.version();
  }
  if (replicas) {
    replicas->clear();
    for (int i = 0; i < pb_.replica_list_size(); ++i) {
      const GroupReplicaProto& rep_pb = pb_.replica_list(i);
      (*replicas)[rep_pb.index()] = std::make_shared<GroupReplica>(rep_pb);
    }
  }
}

void BlockGroup::GetHistoryReplicas(
    std::map<uint32_t, GroupReplicaPtr>* history_replicas, uint32_t* version) {
  byte::ScopedLiteRWLock rlock(&mutex_, 'r');
  if (version) {
    *version = pb_.version();
  }
  if (history_replicas) {
    history_replicas->clear();
    for (int i = 0; i < pb_.history_replicas_size(); ++i) {
      const GroupReplicaProto& rep_pb = pb_.history_replicas(i);
      (*history_replicas)[rep_pb.index()] =
          std::make_shared<GroupReplica>(rep_pb);
    }
  }
}

bool BlockGroup::GetReplica(uint32_t index, GroupReplicaPtr* replica) {
  byte::ScopedLiteRWLock rlock(&mutex_, 'r');
  for (int i = 0; i < pb_.replica_list_size(); ++i) {
    if (pb_.replica_list(i).index() == index) {
      if (replica) {
        *replica = std::make_shared<GroupReplica>(pb_.replica_list(i));
      }
      return true;
    }
  }
  return false;
}

bool BlockGroup::GetHistoryReplica(uint32_t index,
                                   GroupReplicaPtr* history_replica) {
  byte::ScopedLiteRWLock rlock(&mutex_, 'r');
  for (int i = 0; i < pb_.history_replicas_size(); ++i) {
    if (pb_.history_replicas(i).index() == index) {
      if (history_replica) {
        *history_replica =
            std::make_shared<GroupReplica>(pb_.history_replicas(i));
      }
      return true;
    }
  }
  return false;
}

void BlockGroup::ListAllReplicas(std::vector<GroupReplicaPtr>* reps,
                                 uint32_t* version) {
  byte::ScopedLiteRWLock rlock(&mutex_, 'r');
  if (version) {
    *version = pb_.version();
  }
  if (reps) {
    reps->clear();
    for (int i = 0; i < pb_.replica_list_size(); ++i) {
      reps->push_back(std::make_shared<GroupReplica>(pb_.replica_list(i)));
    }
    for (int i = 0; i < pb_.history_replicas_size(); ++i) {
      reps->push_back(std::make_shared<GroupReplica>(pb_.history_replicas(i)));
    }
  }
}

}  // namespace cfs
}  // namespace rootserver
}  // namespace bds
