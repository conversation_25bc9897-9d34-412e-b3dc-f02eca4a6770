/**
 * @file datanode.h
 * @brief Definition of DataNode structure for ByteDance Storage system
 */
#pragma once

#include <byterpc/util/endpoint.h>
#include <byte/concurrent/lite_lock.h>
#include <byte/include/macros.h>
#include "rootserver.pb.h"

#include <memory>
#include <string>
#include <unordered_map>
#include <utility>
#include <vector>

#include "metadata/meta/disk.h"

namespace bds {
namespace rootserver {
namespace cfs {

using MutexLock = byte::LiteRWLock;
using RwLock = byte::LiteRWLock;

using DanceDNStatusProto = ::bds::proto::DanceDNStatusProto;
using DanceDNInfoProto = ::bds::proto::DanceDNInfoProto;

struct NodeOptions {
    byterpc::util::EndPoint addr = byterpc::util::EndPoint();
    std::string group_name = "";
    std::string az_name = "";
    std::string idc_name = "";
    std::string rack_name = "";
    DanceDNStatusProto state = DanceDNStatusProto::DN_STATUS_PROTO_NORMAL;
    uint64_t create_time = 0;
};

/**
 * @class Node
 * @brief Represents a data node in the distributed storage system
 * 
 * DataNode manages storage information, disk statistics, and location
 * information within the cluster hierarchy.
 */
class Node {
public:

    explicit Node(const DataNodeOptions& options);
    ~Node() = default;
    

    byterpc::util::EndPoint getAddr() const;


    void setAddr(const byterpc::util::EndPoint& endpoint);
    

    uint64_t getCreateTime() const;
    

    std::string getLocationDc() const;

    void setLocationDc(const std::string& location_dc);

    std::string getLocationCell() const;

    void setLocationCell(const std::string& location_cell);

    std::string getLocationRack() const;

    void setLocationRack(const std::string& location_rack);

    uint64_t getOfflineTime();

    void setOfflineTime(uint64_t time);

    uint64_t getOnlineTime();

    void setOnlineTime(uint64_t time);

    void setState(DanceDNStatusProto state);

    DanceDNStatusProto getState() const;

    uint64_t getMaxIoutil() const;

    int getMaxIoutilDiskId() const;

    double getMinDiskUsage();

    double getUsage();

    double getFreeUsage();

    uint64_t getFreeSpace();

    bool isDiskSafemode();

    uint64_t getDiskSafemodeUptime();

    void setDiskSafemode(bool is_safemode, uint64_t time);

    uint64_t getUsedSize() const;

    uint64_t getTotalSize() const;

    uint64_t getFreeSize() const;

    uint64_t getDiskTotalSize() const;

    uint64_t getLastHeartbeatTime() const;

    void setLastHeartbeatTime(uint64_t time);

    void setDnId(const std::string& id);

    std::string getDnId() const;

    void updateDiskStat(const std::vector<DiskStat>& disk_stat_vector);

    std::shared_ptr<Disk> getDisk(int disk_id);

    void getDiskList(DiskVector* disk_list);

    int addDisk(const std::shared_ptr<Disk>& disk_ptr);

    int dropDisk(int disk_id);

    void getExistGroupReplicas(GroupReplicaVector* group_replicas);

    void getGroupReplicaList(GroupReplicaVector* group_replicas);

    size_t getGroupReplicaNum();

    std::shared_ptr<GroupReplica> getGroupReplica(uint64_t volume_id);

private:
    /** Node information proto */
    DanceDNInfoProto node_info_;
    
    /** Overall usage percentage */
    double usage_ = 1.0;
    
    /** Map of disk ID to disk object */
    DiskMap disk_map_;
    
    /** Mutex for thread safety */
    mutable RwLock mutex_;
    
    /** Timestamp when node went offline */
    uint64_t offline_time_ = 0;
    
    /** Timestamp when node came online */
    uint64_t online_time_ = 0;
    
    /** Flag indicating if disks are in safemode */
    bool is_disk_safemode_ = false;
    
    /** Timestamp when disk entered safemode */
    uint64_t disk_safemode_uptime_ = 0;
    
    /** Total disk size in bytes */
    uint64_t disk_total_size_ = 0;
    
    /** Free disk size in bytes */
    uint64_t disk_free_size_ = 0;
    
    /** Used size reported by data node */
    uint64_t report_used_size_ = 0;
    
    /** Used size calculated from disk usage */
    uint64_t calc_used_size_ = 0;
    
    /** ID of disk with maximum IO utilization */
    int max_ioutil_disk_id_ = -1;
    
    /** Maximum IO utilization value [0-100] */
    uint64_t max_ioutil_ = 0;
};

/** Type alias for map of node IDs to node objects */
using DataNodeMap = std::unordered_map<std::string, std::shared_ptr<Node>>;

/** Type alias for vector of node pointers */
using DataNodeVector = std::vector<std::shared_ptr<Node>>;

} // namespace cfs
} // namespace rootserver
} // namespace bds