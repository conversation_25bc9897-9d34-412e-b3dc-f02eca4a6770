//=============================================================================
// Copyright (c) 2025-present, ByteDance Inc. All rights reserved.
// Author: <EMAIL>
// Date: 2025-05-21 14:18
// Description:
// This file defines the ClusterManager class, manage cluster infos.
//=============================================================================

#pragma once

#include <chrono>
#include <string>
#include <unordered_map>
#include <vector>
#include <memory>
#include <mutex>

#include "metadata/meta/node.h"

namespace bds {
namespace rootserver {
namespace cfs {

class RootServer;
class Node;
class Rack;
class Cell;
class Dc;

using ClusterProto = ::bds::proto::ClusterProto;

// Forward declare types from rootserver.pb.h to avoid include issues
using NodeVector = std::vector<std::shared_ptr<Node>>;
using NodeMap = std::unordered_map<std::string, std::shared_ptr<Node>>;
using RackVector = std::vector<std::shared_ptr<Rack>>;
using RackMap = std::unordered_map<std::string, std::shared_ptr<Rack>>;
using CellVector = std::vector<std::shared_ptr<Cell>>;
using CellMap = std::unordered_map<std::string, std::shared_ptr<Cell>>;
using DcVector = std::vector<std::shared_ptr<Dc>>;
using DcMap = std::unordered_map<std::string, std::shared_ptr<Dc>>;

// az Cell rack..
class Rack {
public:
    Rack(const std::string& rack_name) : rack_name_(rack_name) {}

    std::string rackName() const {
        // Reading the rack name doesn't require a lock since it's immutable after construction
        return rack_name_;
    }

    int addNode(const std::shared_ptr<Node>& node_ptr);
    int dropNode(const std::shared_ptr<Node>& node_ptr);
    void getNodeList(NodeVector* node_list);

private:
    std::string rack_name_ = "";
    NodeMap node_map_;
    mutable std::mutex mutex_; // Using std::mutex for thread safety
};

class Cell {
public:
    Cell(const std::string& cell_name) : cell_name_(cell_name) {}

    int addNode(const std::shared_ptr<Node>& node_ptr);
    int dropNode(const std::shared_ptr<Node>& node_ptr);
    void getNodeList(NodeVector* node_list);
    void getRackList(RackVector* rack_list);

    std::string cellName() const {
        // Reading the cell name doesn't require a lock since it's immutable after construction
        return cell_name_;
    }

    int addRack(const std::shared_ptr<Rack>& rack_ptr);
    int dropRack(const std::shared_ptr<Rack>& rack_ptr);
    std::shared_ptr<Rack> getRack(const std::string& rack_name);

private:
    std::string cell_name_ = "";
    RackMap rack_map_;
    mutable std::mutex mutex_; // Using std::mutex for thread safety
};

// Dc (datacenter) contains cells
class Dc {
public:
    Dc(const std::string& dc_name) : dc_name_(dc_name) {}

    std::string dcName() const {
        // Reading the DC name doesn't require a lock since it's immutable after construction
        return dc_name_;
    }

    int addRack(const std::shared_ptr<Rack>& rack_ptr, const std::string& cell_name);
    std::shared_ptr<Rack> getRack(const std::string& rack_name, const std::string& cell_name);

    int addCell(const std::shared_ptr<Cell>& cell_ptr);
    int dropCell(const std::shared_ptr<Cell>& cell_ptr);
    void getCellList(CellVector* cell_list);

    int addNode(const std::shared_ptr<Node>& node_ptr);
    int dropNode(const std::shared_ptr<Node>& node_ptr);
    void getNodeList(NodeVector* node_list);

    std::shared_ptr<Cell> getCell(const std::string& cell_name);

private:
    std::string dc_name_ = "";
    CellMap cell_map_;
    mutable std::mutex mutex_; // Using std::mutex for thread safety
};

class ClusterManager {
 public:
  explicit ClusterManager(RootServer* root_server,
                          const std::string& cluster_uuid_);

    void Start();
    void Stop();

    std::string GetClusterUuid() const;
    std::shared_ptr<ClusterProto> GetClusterProto() const;

    // DC related operations
    int addDc(const std::shared_ptr<Dc>& dc_ptr);
    int dropDc(const std::shared_ptr<Dc>& dc_ptr);
    std::shared_ptr<Dc> getDc(const std::string& dc_name);
    void getDcList(DcVector* dc_list);

    // Methods to traverse the hierarchy
    int addNode(const std::shared_ptr<Node>& node_ptr);
    int dropNode(const std::shared_ptr<Node>& node_ptr);
    void getNodeList(NodeVector* node_list);

private:
    bool running_;
    std::string cluster_uuid_;
    std::shared_ptr<ClusterProto> cluster_pb_;
    RootServer* root_server_;

    // Map of datacenter name to Dc objects
    DcMap dc_map_;
    mutable std::mutex mutex_; // Protect the DC map
};

}  // namespace cfs
}  // namespace rootserver
}  // namespace bds