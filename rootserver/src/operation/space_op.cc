//=============================================================================
// Copyright (c) 2025-present, ByteDance Inc. All rights reserved.
// Author: <EMAIL>
// Date: 2025-05-24 14:20
// Description:
// This file implements the SpaceOp class.
//=============================================================================

#include "operation/space_op.h"

#include "metadata/store/rs_meta_reader.h"
#include "metadata/store/rs_meta_writer.h"

namespace bds {
namespace rootserver {
namespace cfs {

SpaceOp::SpaceOp(const eic::master::RequestContextPtr& req_ctx,
                 byterpc::ThreadContext* th_ctx, bds::proto::OpType op_type,
                 const RsOpCallback& callback, RootServer* root_server)
    : eic::master::Operation(eic::proto::OperationTypeProto::kWriteBatch,
                             nullptr, root_server->eic_master()),
      rs_callback_(callback),
      rs_op_type_(op_type),
      root_server_(root_server),
      req_ctx_(req_ctx),
      rpc_thread_ctx_(th_ctx) {}

SpaceOp::~SpaceOp() {}

void SpaceOp::addSpace() {
  RS_TAKE_HANDLE_TIME_AND_RETURN_IF_TIMEOUT(req_ctx_);
  BYTE_DEFER(req_ctx_->Done());
  const auto* request =
      static_cast<const bds::proto::AddSpaceRequest*>(req_ctx_->request());
  bds::proto::SpaceProto space_proto;
  space_proto.set_space_name(request->space_name());

  auto placement = request->placement();
  space_proto.set_group_num(placement.group_num());
  space_proto.set_replica_num(placement.replica_num());

  auto next_space_id = root_server_->GetSpaceManager()->GetMaxSpaceID() + 1;
  space_proto.set_space_id(next_space_id);
  std::unique_ptr<RSMetaWriter> writer =
      std::make_unique<RSMetaWriter>(root_server_);
  writer->AddSpace(space_proto);
  writer->UpdateMaxSpaceID(next_space_id);

  writer->Flush([this, request, space_proto](eic::proto::EicErrorCode e,
                                             const std::string& error_context) {
    if (LIKELY(e == eic::proto::EIC_SUCCESS)) {
      LOG(INFO) << "add space success"
                << ", space_name: " << request->space_name();
      auto st = root_server_->GetSpaceManager()->AddSpace(space_proto);
      BYTE_ASSERT(st.ok());
    } else {
      LOG(ERROR) << "add space failed"
                 << ", space_name: " << request->space_name();
      auto* response =
          static_cast<bds::proto::AddSpaceResponse*>(req_ctx_->response());
      response->mutable_comm_resp()->set_status(
          bds::proto::Status::ERROR_RS_ADD_SPACE_ERROR);
      response->mutable_comm_resp()->set_msg("add space failed");
      CallbackForCaller(bds::proto::Status::ERROR_RS_ADD_SPACE_ERROR);
    }
  });
}

void SpaceOp::GetSpaceInfo() {
  RS_TAKE_HANDLE_TIME_AND_RETURN_IF_TIMEOUT(req_ctx_);
  BYTE_DEFER(req_ctx_->Done());
  const auto* request =
      static_cast<const bds::proto::GetSpaceInfoRequest*>(req_ctx_->request());
  auto* response =
      static_cast<bds::proto::GetSpaceInfoResponse*>(req_ctx_->response());
  bds::proto::SpaceProto space_proto;
  if (1) {
    // just read from space manager, no need to read kv store
    LOG(INFO) << "get space info from space manager"
              << ", space_id: " << request->space_id();
    auto space = std::make_shared<Space>();
    auto ret =
        root_server_->GetSpaceManager()->GetSpace(request->space_id(), &space);
    if (!ret.ok()) {
      LOG(ERROR) << "get space info failed"
                 << ", space_id: " << request->space_id()
                 << ", error code: " << static_cast<int>(ret.code());
      response->mutable_comm_resp()->set_status(
          bds::proto::Status::ERROR_RS_GET_SPACE_ERROR);
      response->mutable_comm_resp()->set_msg(ret.ToString());
      CallbackForCaller(bds::proto::Status::ERROR_RS_GET_SPACE_ERROR);
      return;
    }
    space->GetSpaceProto(&space_proto);
  } else {
    // read from kv store
    LOG(INFO) << "get space info from kv store"
              << ", space_id: " << request->space_id();
    std::unique_ptr<RSMetaReader> reader =
        std::make_unique<RSMetaReader>(root_server_);
    auto ret = reader->GetSpace(request->space_id(), &space_proto);
    if (UNLIKELY(ret != bds::proto::Status::SUCCESS)) {
      LOG(ERROR) << "get space info failed"
                 << ", space_id: " << request->space_id()
                 << ", error code: " << bds::proto::Status_Name(ret);
      response->mutable_comm_resp()->set_status(
          bds::proto::Status::ERROR_RS_GET_SPACE_ERROR);
      response->mutable_comm_resp()->set_msg("get space info failed");
      CallbackForCaller(ret);
      return;
    }
  }
  response->mutable_space_info()->CopyFrom(space_proto);
  LOG(INFO) << "get space info success"
            << ", space_id: " << request->space_id()
            << ", space_name: " << response->space_info().space_name()
            << ", group_num: " << response->space_info().group_num()
            << ", replica_num: " << response->space_info().replica_num();

  CallbackForCaller(bds::proto::Status::SUCCESS);
}

void SpaceOp::Run() {
  switch (rs_op_type_) {
    case bds::proto::OpType::kAddSpace: addSpace(); break;
    case bds::proto::OpType::kGetSpaceInfo: GetSpaceInfo(); break;
    default:
      LOG(ERROR) << "unknown resource operation type "
                 << bds::proto::OpType_Name(rs_op_type_);
      CallbackForCaller(bds::proto::Status::ERROR_NOT_IMPLEMENTED);
      return;
  }
}

void SpaceOp::Done(bds::proto::Status e, const std::string& err_context) {
  if (!done_.CompareExchange(false, true)) {
    return;
  }
  CancelTimeout();
  auto self = shared_from_this();
  if (rs_callback_) {
    rs_callback_(e, err_context);
  }
  if (executor_) {
    executor_->OnFinishedOperation(self);
  }
  OnFinish();
}

void SpaceOp::CallbackForCaller(bds::proto::Status st) {
  LOG(DEBUG) << "submit space operation callback for caller"
             << ", caller thread context: " << rpc_thread_ctx_
             << ", space operation type: "
             << bds::proto::OpType_Name(rs_op_type_)
             << ", space operation retcode: " << bds::proto::Status_Name(st)
             << ", space operation cost(us): "
             << (byte::GetCurrentTimeInUs() - op_start_time_us_);
  rpc_thread_ctx_->Invoke([this, st] {
    Done(st, bds::proto::Status_Name(st));
  });
}

}  // namespace cfs
}  // namespace rootserver
}  // namespace bds