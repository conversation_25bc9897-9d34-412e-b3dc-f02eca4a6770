#include "rs_core_service.h"

#include "root_server.h"

namespace bds {
namespace rootserver {
namespace cfs {

void RSCoreServiceImpl::GetClusterInfo(
    google::protobuf::RpcController* controller,
    const bds::proto::GetClusterInfoRequest* request,
    bds::proto::GetClusterInfoResponse* response,
    google::protobuf::Closure* done) {
  byterpc::util::ClosureGuard done_guard(done);
  byterpc::Controller* cntl = dynamic_cast<byterpc::Controller*>(controller);
  BYTE_CHECK_NOTNULL(cntl);
  IF_NOT_LEADER_THEN_RETURN(root_server_->replica_driver());

  LOG(INFO) << "recv " << request->GetTypeName() << " from "
            << byterpc::util::endpoint2str(cntl->remote_side()).c_str()
            << " message:\n"
            << request->DebugString();
  auto cluster_info = response->mutable_cluster_info();

  // mock data
  cluster_info->set_version(10000);
  cluster_info->set_mtime_us(1672531200000000);
  auto config = cluster_info->mutable_config();
  config->set_cluster_uuid("uuid-123456");
  config->set_cluster_name("dora-cluster-1");
  config->set_region_name("beijing");

  auto space = cluster_info->add_space_list();
  space->set_space_id(1001);
  space->set_space_name("file");

  auto space1 = cluster_info->add_space_list();
  space1->set_space_id(1002);
  space1->set_space_name("dentry");

  response->mutable_comm_resp()->set_status(bds::proto::SUCCESS);
  LOG(DEBUG) << "GetClusterInfo response:\n" << response->DebugString();
}

void RSCoreServiceImpl::AddPeer(google::protobuf::RpcController* controller,
                                const bds::proto::AddPeerRequest* request,
                                bds::proto::AddPeerResponse* response,
                                google::protobuf::Closure* done) {
  byterpc::util::ClosureGuard done_guard(done);
  byterpc::Controller* cntl = dynamic_cast<byterpc::Controller*>(controller);
  BYTE_CHECK_NOTNULL(cntl);
  IF_NOT_LEADER_THEN_RETURN(root_server_->replica_driver());

  LOG(INFO) << "recv " << request->GetTypeName() << " from "
            << byterpc::util::endpoint2str(cntl->remote_side()).c_str()
            << " message:\n"
            << request->DebugString();

  if (request->new_peer_addr() == "") {
    LOG(INFO) << "add rs new peer invalid parameters: "
              << request->DebugString();
    response->mutable_comm_resp()->set_status(
        bds::proto::Status::ERROR_INVALID_PARAMETERS);
    response->mutable_comm_resp()->set_msg("new_peer_addr is empty");
    return;
  }

  eic::master::MasterNodeId new_peer;
  new_peer.peer_id_ = request->new_peer_id();
  new_peer.addr_.FromString(request->new_peer_addr());

  auto drv = root_server_->replica_driver();
  auto ec = drv->AddNode(new_peer);

  LOG(INFO) << "add rs new peer " << request->new_peer_id() << ":"
            << request->new_peer_addr() << " returns "
            << eic::proto::EicErrorCode_Name(ec);
}

void RSCoreServiceImpl::AddSpace(google::protobuf::RpcController* controller,
                                 const bds::proto::AddSpaceRequest* request,
                                 bds::proto::AddSpaceResponse* response,
                                 google::protobuf::Closure* done) {
  byterpc::util::ClosureGuard done_guard(done);
  byterpc::Controller* cntl = dynamic_cast<byterpc::Controller*>(controller);
  BYTE_CHECK_NOTNULL(cntl);
  IF_NOT_LEADER_THEN_RETURN(root_server_->replica_driver());

  LOG(INFO) << "recv " << request->GetTypeName() << " from "
            << byterpc::util::endpoint2str(cntl->remote_side()).c_str()
            << " message:\n"
            << request->DebugString();

  auto req_ctx = std::make_shared<eic::master::RequestContext>(
      request, response, cntl, done_guard.release(),
      root_server_->eic_master());
  auto req_op = std::make_shared<SpaceOp>(
      req_ctx, byterpc::ExecCtx::GetCurrentThreadContext(),
      bds::proto::OpType::kAddSpace,
      [request, response, cntl, req_ctx](bds::proto::Status ret_st,
                                         const std::string& ret_msg) {
        BYTE_DEFER(req_ctx->Done());
        response->mutable_comm_resp()->set_status(ret_st);
        response->mutable_comm_resp()->set_msg(ret_msg);
        LOG(DEBUG) << "AddSpace callback response:\n"
                   << response->DebugString();
      },
      root_server_);
  auto rc = root_server_->GetSpaceManager()->PushOperation(req_op);
  if (UNLIKELY(rc != eic::proto::EIC_SUCCESS)) {
    LOG(ERROR) << "submit " << request->GetTypeName() << " failed";
    response->mutable_comm_resp()->set_status(
        bds::proto::Status::ERROR_INVALID_PARAMETERS);
    response->mutable_comm_resp()->set_msg("submit to executor failed");
    return;
  }

  LOG(DEBUG) << "AddSpace done response:\n" << response->DebugString();
}

void RSCoreServiceImpl::GetSpaceInfo(
    google::protobuf::RpcController* controller,
    const bds::proto::GetSpaceInfoRequest* request,
    bds::proto::GetSpaceInfoResponse* response,
    google::protobuf::Closure* done) {
  byterpc::util::ClosureGuard done_guard(done);
  byterpc::Controller* cntl = dynamic_cast<byterpc::Controller*>(controller);
  BYTE_CHECK_NOTNULL(cntl);
  IF_NOT_LEADER_THEN_RETURN(root_server_->replica_driver());

  LOG(INFO) << "recv " << request->GetTypeName() << " from "
            << byterpc::util::endpoint2str(cntl->remote_side()).c_str()
            << " message:\n"
            << request->DebugString();
  // TODO just read from space manager, no need to read kv store
  auto req_ctx = std::make_shared<eic::master::RequestContext>(
      request, response, cntl, done_guard.release(),
      root_server_->eic_master());
  auto req_op = std::make_shared<SpaceOp>(
      req_ctx, byterpc::ExecCtx::GetCurrentThreadContext(),
      bds::proto::OpType::kGetSpaceInfo,
      [request, response, cntl, req_ctx](bds::proto::Status ret_st,
                                         const std::string& ret_msg) {
        BYTE_DEFER(req_ctx->Done());
        response->mutable_comm_resp()->set_status(ret_st);
        response->mutable_comm_resp()->set_msg(ret_msg);
        LOG(DEBUG) << "GetSpaceInfo callback response:\n"
                   << response->DebugString();
      },
      root_server_);
  auto rc = root_server_->GetSpaceManager()->PushOperation(req_op);
  if (UNLIKELY(rc != eic::proto::EIC_SUCCESS)) {
    LOG(ERROR) << "submit " << request->GetTypeName() << " failed";
    response->mutable_comm_resp()->set_status(
        bds::proto::Status::ERROR_INVALID_PARAMETERS);
    response->mutable_comm_resp()->set_msg("submit to executor failed");
    return;
  }

  LOG(DEBUG) << "GetSpaceInfo done response:\n" << response->DebugString();
}

}  // namespace cfs
}  // namespace rootserver
}  // namespace bds