#include "rs_dn_service.h"

#include "root_server.h"

namespace bds {
namespace rootserver {
namespace cfs {

RSDnServiceImpl::~RSDnServiceImpl() {}

void RSDnServiceImpl::RegisterDanceDN(
    google::protobuf::RpcController* controller,
    const bds::proto::RegisterDanceDNRequest* request,
    bds::proto::RegisterDanceDNResponse* response,
    google::protobuf::Closure* done) {
  // TODO
}

void RSDnServiceImpl::DanceDNHeartBeat(
    google::protobuf::RpcController* controller,
    const bds::proto::DNHeartBeatRequest* request,
    bds::proto::DNHeartBeatResponse* response,
    google::protobuf::Closure* done) {
  // TODO
}

}  // namespace cfs
}  // namespace rootserver
}  // namespace bds