 /*
 * Copyright (c) 2017 Baidu.com, Inc. All Rights Reserved.
 *
 * Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * Date: 2019-05-15
 * Desciption:
 *
 */

#include "gflags/gflags.h"
#include "gmock/gmock.h"
#include "gtest/gtest.h"
#include "bmock.h"
#include "baidu/inf/aries/monitorcenter/flags.h"
#include "baidu/inf/aries/monitorcenter/influxdb_scheduler.h"
namespace aries {
namespace monitorcenter {

using ::testing::Return ; 
using ::testing::_;

BMOCK_NS_METHOD2(aries::common, myexec, int32_t(const char*, std::vector<std::string>&));

class InfluxDBSchedulerTests : public ::testing::Test {

};
void gen_data(aries::pb::InfluxdbData* data) {
    auto tag = data->add_tags();
    tag->set_key("tag");
    tag->set_value("tag");
    auto filed = data->add_fields();
    filed->set_key("filed");
    filed->set_value("filed");
}
        
TEST_F(InfluxDBSchedulerTests, try_send_data) {
    InfluxdbScheduler sch;
    int data_num = 10;
    FLAGS_max_push_influxdb_data_num = 4;
    for (int i = 0; i < data_num; ++i) {
        aries::pb::InfluxdbData data;
        gen_data(&data);
        sch._waiting_datas.push_back(data, 1);
    }
    FLAGS_influxdb_service_address = "127.0.0.1:12345";
    FLAGS_influxdb_name = "test";
    EXPECT_CALL(BMOCK_NS_OBJECT(aries::common, myexec), myexec(_, _))
        .WillOnce(Return(0));
    sch.try_send_data(true);
    EXPECT_EQ(sch.waiting_queue_size(), data_num - FLAGS_max_push_influxdb_data_num);
}

TEST_F(InfluxDBSchedulerTests, run) {
    InfluxdbScheduler sch;
    EXPECT_EQ(sch._is_stopped, true);
    int data_num = 10;
    FLAGS_max_push_influxdb_data_num = 4;
    FLAGS_push_influxdb_sleep_ms = 3000;
    std::vector<aries::pb::InfluxdbData> vec;
    for (int i = 0; i < data_num; ++i) {
        aries::pb::InfluxdbData data;
        gen_data(&data);
        if (data_num % 2 == 0) {
            data.set_monitor_addr(std::to_string(i));
        }
        data.set_timestamp(2);
        vec.push_back(data);
    }
    for (int i = data_num; i < 2 * data_num; ++i) {
        aries::pb::InfluxdbData data;
        gen_data(&data);
        data.set_is_remote(true);
        data.set_timestamp(2);
        data.set_monitor_addr(std::to_string(i));
        vec.push_back(data);
    }
    sch._is_stopped = false;
    sch.push_data(vec);
    sch._is_stopped = true;
    FLAGS_influxdb_service_address = "127.0.0.1:12345";
    FLAGS_influxdb_name = "test";
    EXPECT_EQ(sch.waiting_queue_size(), 2 * data_num);

    EXPECT_CALL(BMOCK_NS_OBJECT(aries::common, myexec), myexec(_, _))
        .WillOnce(Return(0))
        .WillOnce(Return(0));

    sch.start();
    EXPECT_EQ(sch._is_stopped, false);
    usleep(1000 * 1000);
    sch.stop();
    EXPECT_EQ(sch._is_stopped, true);
    //
    EXPECT_EQ(sch.waiting_queue_size(), 2 * data_num - 2 * FLAGS_max_push_influxdb_data_num);
}

}
}