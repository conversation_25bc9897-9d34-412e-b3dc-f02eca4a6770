//=============================================================================
// Author: x<PERSON><PERSON><EMAIL>
// Data: 2016-10-11 16:50
// Filename: dataagent.h
// Description: 
//=============================================================================

#ifndef BAIDU_INF_ARIES_DATAAGENT_DATA_AGENT_H
#define BAIDU_INF_ARIES_DATAAGENT_DATA_AGENT_H 

#include <atomic>
#include <baidu/rpc/server.h>
#include "baidu/inf/aries/dataagent/data_service.h"
#include "baidu/inf/aries-api/proxy/heartbeat.h"
#include "baidu/inf/aries-api/proxy/util.h"

namespace aries {
namespace dataagent {

class DataAgent {
public:
    DataAgent() : _is_init(false), _is_running(false) {}
    ~DataAgent();

    int32_t init();
    int reload();
    int32_t start();
    int32_t stop();
    int32_t join();

private:
    std::atomic<bool> _is_init;
    std::atomic<bool> _is_running;
    aries::pb::NodeId _server_info;
    baidu::rpc::Server _rpc_server;
    //DataAgentMetaServiceImpl _meta_service;
    DataServiceImpl _data_service;
    Heartbeat       _heartbeat;
};

}
}

#endif
