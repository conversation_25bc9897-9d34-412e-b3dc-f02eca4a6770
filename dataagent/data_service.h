//=============================================================================
// Author: x<PERSON><EMAIL>
// Data: 2016-10-11 16:50
// Filename: data_service.h
// Description: 
//=============================================================================
#ifndef BAIDU_INF_ARIES_DATAAGENT_DATA_SERVICE_H
#define BAIDU_INF_ARIES_DATAAGENT_DATA_SERVICE_H

#include "baidu/inf/aries-api/common/proto/common.pb.h"
#include "baidu/inf/aries-api/common/proto/data_agent.pb.h"
#include "baidu/inf/aries-api/proxy/proxy.h"
#include "baidu/inf/aries/dataagent/util.h"

namespace aries {
namespace dataagent {

//aries::BlobProxy* g_blob_proxy;

class DataServiceImpl : public ::aries::pb::DADataService {
public:
    virtual void put(::google::protobuf::RpcController* controller,
            const ::aries::pb::BlobPutRequest* request,
            ::aries::pb::BlobPutResponse* response,
            ::google::protobuf::Closure* done);

    virtual void get(::google::protobuf::RpcController* controller,
            const ::aries::pb::BlobGetRequest* request,
            ::aries::pb::BlobGetResponse* response,
            ::google::protobuf::Closure* done);

    virtual void remove(::google::protobuf::RpcController* controller,
            const ::aries::pb::BlobRemoveRequest* request,
            ::aries::pb::AckResponse* response,
            ::google::protobuf::Closure* done);
};

}
}

#endif

