/**
 * ARIES - A Reliable and Impressive EB-level Storage.
 * Copyright (c) 2016 Baidu.com, Inc. All Rights Reserved.
 *
 * Author: ch<PERSON><PERSON> (<EMAIL>)
 * Date: 2016/11/11
 * Desciption: Mock master for test allocator
 *
 */

#ifndef BAIDU_INF_ARIES_ALLOCATOR_TEST_MOCK_MASTER_H
#define BAIDU_INF_ARIES_ALLOCATOR_TEST_MOCK_MASTER_H

#include "baidu/rpc/server.h"
#include "baidu/inf/aries-api/common/proto/master.pb.h"
#include "baidu/inf/aries/allocator/flags.h"
#include "baidu/inf/aries-api/common/config.h"

namespace aries {
namespace allocator{

typedef void (*Rpc_Method)(::google::protobuf::RpcController* controller,
        const ::google::protobuf::Message* request,
        ::google::protobuf::Message* response);
/*
 * Mock Master HeartbeatService
 */
void default_allocator_heartbeat(::google::protobuf::RpcController* controller,
                           const ::google::protobuf::Message* req, 
                           ::google::protobuf::Message* res) {
    ::aries::pb::AllocatorHeartbeatResponse* response = 
        reinterpret_cast<::aries::pb::AllocatorHeartbeatResponse*>(res);  
    const ::aries::pb::AllocatorHeartbeatRequest* request = 
        reinterpret_cast<const ::aries::pb::AllocatorHeartbeatRequest*>(req);   
    base::EndPoint req_addr;
    common::int2endpoint(request->req_addr());
    LOG(NOTICE) << "recv heartbeat from bloballocator "
        << common::endpoint2str(req_addr);
    response->mutable_status()->set_code(AIE_OK);
    response->mutable_status()->set_msg("ok");
}
class MockMasterHeartbeatService : public ::aries::pb::MasterHeartbeatService {
public:
    MockMasterHeartbeatService() : _heartbeat_method(default_allocator_heartbeat){}
    virtual ~MockMasterHeartbeatService() {}

    virtual void datanode_heartbeat(::google::protobuf::RpcController* controller,
                        const ::aries::pb::NodeHeartbeatRequest* request,
                        ::aries::pb::AckResponse* response,
                        ::google::protobuf::Closure* done) {
    }

    virtual void allocator_heartbeat(::google::protobuf::RpcController* controller,
                        const ::aries::pb::AllocatorHeartbeatRequest* request,
                        ::aries::pb::AllocatorHeartbeatResponse* response,
                        ::google::protobuf::Closure* done) {
        baidu::rpc::ClosureGuard done_guard(done);
        if (_is_return_not_exist) {
            response->mutable_status()->set_code(AIE_NOT_EXIST);
            response->mutable_status()->set_msg("allocator");
        } else {
            _heartbeat_method(controller, request, response);
        }
    }

    void set_return_not_exist() {
        _is_return_not_exist = true;
    }

    void clear_return_not_exist() {
        _is_return_not_exist = false;
    }

private:  
    Rpc_Method _heartbeat_method;
    bool _is_return_not_exist = false;
};

void default_assign_allocator_sequence_id(::google::protobuf::RpcController* controller,
                           const ::google::protobuf::Message* request, 
                           ::google::protobuf::Message* res) {
        ::aries::pb::AssignAllocatorSequenceIdResponse* response = 
            reinterpret_cast<::aries::pb::AssignAllocatorSequenceIdResponse*>(res);
        FLAGS_host_name = "allocator1";
        aries::pb::AllocatorInfo* info = response->add_allocator_list();
        info->set_allocator_name(FLAGS_host_name);
        info->set_allocator_addr(
             common::endpoint2int(common::get_local_addr()));
        info->set_is_alive(true);

        for (int i = 0; i < 4; i++) {
             aries::pb::AllocatorInfo* info = response->add_allocator_list();
             char buf[30]; 
             sprintf(buf, "mockallocator%d", i);
             info->set_allocator_name(buf);
             base::EndPoint ep;
             memset(buf, '\0', 30);
             sprintf(buf, "127.0.0.1:22%d", i);
             base::str2endpoint(buf, &ep);
             info->set_allocator_addr(common::endpoint2int(ep));
             info->set_is_alive(true);
             LOG(NOTICE) << " mock response:" << info->allocator_name();
        }
        response->set_sequence_id(2);
        response->mutable_status()->set_code(0);
        response->mutable_status()->set_msg("get succ");
}

class MockService : public aries::pb::MasterManageService {
public:
    MockService() : _allocator_sequence_method(default_assign_allocator_sequence_id){}
    virtual void assign_allocator_sequence_id(::google::protobuf::RpcController* controller,
                          const ::aries::pb::AssignAllocatorSequenceIdRequest* request,
                          ::aries::pb::AssignAllocatorSequenceIdResponse* response,
                          ::google::protobuf::Closure* done) {
                          
      baidu::rpc::ClosureGuard done_guard(done);
      LOG(NOTICE) << " recv request";
      _allocator_sequence_method(controller, request, response);
}

private:
    Rpc_Method _allocator_sequence_method;
};

class MockMaster {
public:
    bool start(uint32_t port) {
        // Start Data Server
        baidu::rpc::ServerOptions options;
        base::EndPoint listen_addr;
        base::str2endpoint("127.0.0.1", port, &listen_addr);
        if (_master_server.AddService(&_monitor_service,
                    baidu::rpc::SERVER_DOESNT_OWN_SERVICE)) {
            return false;
        }
        if (_master_server.AddService(&_heartbeat_service, 
                                     baidu::rpc::SERVER_DOESNT_OWN_SERVICE )) {
            return false;
        }
        if (_master_server.Start(listen_addr, &options)) {
            return false;
        }

        _master_server.listen_address();
        LOG(TRACE) << "===master rpc server" << listen_addr;
        _running = true;
        return true;
    }
    void stop() {
        if (_running) {
            _master_server.Stop(200);
            _master_server.Join();

        } else {
            LOG(NOTICE) << "master already stoped";
        }
    }
 
    void set_return_not_exist() {
        _heartbeat_service.set_return_not_exist();
    }

    void clear_return_not_exist() {
        _heartbeat_service.clear_return_not_exist();
    }
  
private:
    MockMasterHeartbeatService      _heartbeat_service;
    MockService              _monitor_service;
    baidu::rpc::Server              _master_server;
    bool _running = false;
};

} // end namespace of datanode
} // end namespace of aries

#endif //#ifndef BAIDU_INF_ARIES_DATANODE_TEST_MOCK_MASTER_H

/* vim: set ts=4 sw=4 sts=4 tw=100 */
