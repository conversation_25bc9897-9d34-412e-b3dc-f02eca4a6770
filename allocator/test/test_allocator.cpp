/**
 * ARIES - A Reliable and Impressive EB-level Storage.
 * Copyright (c) 2016 Baidu.com, Inc. All Rights Reserved.
 *
 * Author: ch<PERSON><PERSON> (<EMAIL>)
 * Date: 2016/10/10
 * Desciption: Unittests for test allocator
 *
 */

#include "baidu/inf/aries/allocator/checker.h"
#include "baidu/inf/aries/allocator/allocator.h"
#include "baidu/inf/aries/allocator/flags.h"

#include <stdio.h>
#include <unistd.h>
#include <fcntl.h>
#include <sys/types.h>
#include <sys/stat.h>
#include <sys/mman.h>
#include <sys/time.h>
#include <base/time.h>
#include <gtest/gtest.h>
#include "boost/filesystem.hpp"
namespace fs = boost::filesystem;

namespace aries {
namespace allocator {
int num = 10;

extern uint32_t g_max_id_upper_limit;

class AllocatorTest : public ::testing::Test {
public:
    virtual void SetUp() {
         //prepair g_checker
        FLAGS_host_name = "serverA";
        std::string server_a("serverA");
        std::vector<std::string> server_list;
        server_list.push_back(server_a);

        g_checker->change_allocator_list(server_list);
        std::string table_name = "test";

        _manager = new VolumeMetaManager();
        bool ok = g_allocator->_volume_meta_maps.insert(std::make_pair(table_name, _manager)).second;

        for (int i = 0; i < num; ++i) {
            VolumeMeta meta;
            meta.volume_id = i;
            meta.max_id = 0;
            meta.code = g_checker->get_code(i);
            meta.total_size = 1024*1024*500;
            meta.free_size = 1024*1024*200;
            meta.hole_size = 1024*1024;
            meta.space_name = table_name;
            g_allocator->update_volume_meta(meta);
        }

        std::shared_ptr<EditLog> editlog(new EditLog(_test_dir, 1));
        g_allocator->set_editlog(editlog);

        _test_dir = "./test_dir";
        _path = fs::path(_test_dir);
        fs::create_directory(_path);
    }

    virtual void TearDown() {
        g_allocator->reset();
        fs::remove_all(_path);
    }
private:
    std::string _test_dir;
    fs::path _path;
    VolumeMetaManager* _manager = nullptr;
};

TEST_F(AllocatorTest, test_normal) {
    std::string table_name = "test";
    aries::pb::Bid* bid = new aries::pb::Bid();
    VolumeInfo volume;
    _manager->vlet_reserve_free_size =  1000 * 1024 * 1024;
    int ret = g_allocator->allocate(table_name, 1, bid, &volume);
    ASSERT_EQ(ret, -1);
    _manager->vlet_reserve_free_size =  0;
    for (int i = 0; i < 10; ++i) {
        VolumeMeta meta;
        meta.volume_id = i;
        meta.max_id = 0;
        meta.code = g_checker->get_code(i);
        meta.free_size = 1024*1024*200;
        meta.space_name = table_name;
        g_allocator->update_volume_meta(meta);
    }
    ret = g_allocator->allocate(table_name, 1, bid, &volume);
    ASSERT_EQ(ret, 0);
    std::string error_msg;
    ret = g_allocator->allocate("testError", 1, bid, &volume, &error_msg);
    EXPECT_NE(ret, 0);
    ret = g_allocator->allocate("testError", 1, bid, &volume);
    EXPECT_NE(ret, 0);
    ASSERT_TRUE(error_msg.length() > 0);
    LOG(NOTICE) << "allocate fail, error msg: " << error_msg;
    ret = g_allocator->allocate(table_name, 1, bid, &volume);
    EXPECT_EQ(ret, 0);
    for (int i = 0; i < 10 * 2; ++i) {
        ret = g_allocator->allocate(table_name, 1, bid, &volume);
        EXPECT_EQ(ret, 0);
        printf("%lu\n", bid->vbid());
    }

    ret = g_allocator->allocate(table_name, 300 * 1024 * 1024, bid, &volume);
    EXPECT_NE(ret, 0);
    ASSERT_EQ(g_allocator->_volume_meta_maps[table_name]->_volume_meta_map.size(), 0);
    ASSERT_EQ(g_allocator->_volume_meta_maps[table_name]->_im_volume_meta_map.size(), 10);
    FLAGS_least_free_size = 1024 * 1024;
    ret = g_allocator->allocate(table_name, 1024, bid, &volume);
    EXPECT_NE(ret, 0);
    for (int i = 0; i < 10; ++i) {
        VolumeMeta meta;
        meta.volume_id = i;
        meta.max_id = 0;
        meta.code = g_checker->get_code(i);
        meta.free_size = 1024*1024*200;
        meta.space_name = table_name;
        g_allocator->update_volume_meta(meta);
    }
    ASSERT_EQ(g_allocator->_volume_meta_maps[table_name]->_volume_meta_map.size(), num);
    ASSERT_EQ(g_allocator->_volume_meta_maps[table_name]->_im_volume_meta_map.size(), 0);
    ret = g_allocator->allocate(table_name, 1024, bid, &volume);
    ASSERT_EQ(ret, 0);

    g_allocator->reset();
    for (int i = 0; i < 10; ++i) {
        VolumeMeta meta;
        meta.volume_id = i;
        meta.max_id = g_max_id_upper_limit;
        meta.code = g_checker->get_code(i);
        meta.free_size = 1024*1024*200;
        meta.space_name = table_name;
        g_allocator->update_volume_meta(meta);
    }
    ret = g_allocator->allocate(table_name, 1024, bid, &volume);
    ASSERT_NE(ret, 0);

    // delete manager;
    // delete g_allocator;
    delete bid;
}

TEST_F(AllocatorTest, test_serialize) {
    std::vector<uint64_t> dn_list;
    dn_list.push_back(1);
    dn_list.push_back(2);
    dn_list.push_back(3);
    std::string file = "./test_dir/test";
    bool ret = g_allocator->serialize(dn_list, file);
    ASSERT_TRUE(ret);
    fs::path path(file);
  
    bool exist = boost::filesystem::exists(path);
    ASSERT_TRUE(exist);
}

TEST_F(AllocatorTest, test_deserialize) {
    base::IOBuf iobuf;
    iobuf.append("fuck");
    common::DataInputBuffer in(&iobuf);
    bool ret = g_allocator->deserialize(in);
    ASSERT_TRUE(!ret);
}

TEST_F(AllocatorTest, test_snapshot) {
    Allocator backup;
    uint64_t txid;    
    std::string table_name = "test";
    auto _iter =  g_allocator->_volume_meta_maps[table_name]->_volume_meta_map.begin();       
    g_allocator->_volume_meta_maps[table_name]->_im_volume_meta_map[_iter->second->volume_id] = _iter->second;
    g_allocator->_volume_meta_maps[table_name]->_volume_meta_map.erase(_iter);
    g_allocator->do_snapshot(&backup, &txid);
    ASSERT_EQ(backup._volume_meta_maps.size(), 1);  
    auto iter = backup._volume_meta_maps.begin();
    VolumeMetaManager* manager = iter->second;
    ASSERT_EQ(manager->_volume_meta_map.size(), 9);
    ASSERT_EQ(manager->_im_volume_meta_map.size(), 1);
    ASSERT_EQ("test", iter->first);
}

TEST_F(AllocatorTest, test_deserialize_error) {
    base::IOBuf iobuf;
    common::DataOutputBuffer out(&iobuf);
    common::DataInputBuffer in(&iobuf);
    out.write_uint16(1u);
   
    bool ret = g_allocator->deserialize(in);
    ASSERT_FALSE(ret);
    out.write_uint32(1u);
    out.write_uint16(2u);
    ret = g_allocator->deserialize(in);
    
    ASSERT_FALSE(ret);
//    out.write_string("test");
    out.write_uint32(1u);
    out.write_uint32(2u);

    out.write_uint32(4);
    out.write_raw("tes", 3);
    ret = g_allocator->deserialize(in);
    ASSERT_FALSE(ret);

    out.write_uint32(1u);
    out.write_uint32(2u);
    out.write_string("test");
    ret = g_allocator->deserialize(in);
    ASSERT_FALSE(ret);
}

TEST_F(AllocatorTest, test_upd_seqid) {
    g_allocator->upd_seqid(100u);
    ASSERT_EQ(g_allocator->_allocator_sequence_id, 100);
}

TEST_F(AllocatorTest, test_add_gap) {
    std::string table_name = "test";
    for (int i = 0; i < 10; ++i) {
          VolumeMeta meta;
          meta.volume_id = i;
          meta.max_id = 0;
          meta.code = g_checker->get_code(i);
          meta.free_size = 1024*1024*200;
          meta.space_name = table_name;
          g_allocator->update_volume_meta(meta);
    }
    g_allocator->add_gap();
    ASSERT_EQ(g_allocator->_volume_meta_maps[table_name]->_volume_meta_map[0]->max_id, FLAGS_blob_gap);
}

TEST_F(AllocatorTest, test_eat_space) {
    std::string table_name = "test1";
    uint64_t vid = 20180919;
    {
          VolumeMeta meta;
          meta.volume_id = vid;
          meta.max_id = 100;
          meta.code = g_checker->get_code(vid);
          meta.free_size = 1024*1024*200;
          meta.space_name = table_name;
          g_allocator->update_volume_meta(meta);
    }
    table_name = "test2";
    {
          VolumeMeta meta;
          meta.volume_id = 20180919;
          meta.max_id = 0;
          meta.code = g_checker->get_code(vid);
          meta.free_size = 1024*1024*200;
          meta.space_name = table_name;
          g_allocator->update_volume_meta(meta);
    }
    ASSERT_EQ(g_allocator->_volume_meta_maps[table_name]->_volume_meta_map[20180919]->max_id, 100);
}

TEST_F(AllocatorTest, test_add_gap_err) {
    std::string table_name = "test";
    g_allocator->reset();
    for (int i = 0; i < 10; ++i) {
          VolumeMeta meta;
          meta.volume_id = i;
          meta.max_id = g_max_id_upper_limit - 100;
          meta.code = g_checker->get_code(i);
          meta.free_size = 1024*1024*200;
          meta.space_name = table_name;
          g_allocator->update_volume_meta(meta);
    }
    ASSERT_EQ(g_allocator->_volume_meta_maps[table_name]->_volume_meta_map[0]->max_id, g_max_id_upper_limit - 100);
    g_allocator->add_gap();
    ASSERT_EQ(g_allocator->_volume_meta_maps[table_name]->_volume_meta_map[0]->max_id, g_max_id_upper_limit - 100);
}

TEST_F(AllocatorTest, check_safewrite_volume_disable) {
    auto* meta_client = new meta_replica::MetaSubscriber();
    g_allocator->set_meta_client(meta_client);

    uint64_t vid = 1234;
    VolumeInfo volume;
    VolumeMetaManager volume_meta_manager;
    ASSERT_EQ(AIE_NOT_EXIST, volume_meta_manager.check_safewrite("shitspace", vid, &volume));

    auto & meta_service = g_allocator->_meta_client->_meta;
    aries::pb::SpaceInfo space_info;
    space_info.set_space_name("shitspace");
    space_info.set_k(4);
    space_info.set_n(6);
    space_info.set_put_quorum(5);
    meta_service.set_space_info("shitspace", space_info);

    aries::pb::MetaReplica::VolumeInfo volume_info;
    volume_info.set_volume_id(vid);
    volume_info.set_space_name("shitspace");
    volume_info.set_volume_state(VOLUME_STATE_READONLY);
    for (int index = 0; index < 6; ++index) {
        auto * vlet_info = volume_info.add_vlet_info();
        vlet_info->set_shard_index(index);
        vlet_info->set_node_addr(1000 + index);
            // vlet 0 & 1: NORMAL, and node ALIVE
        vlet_info->set_state(VLET_STATE_NORMAL);
        aries::pb::NodeInfo node_info;
        node_info.set_node_addr(1000 + index);
        node_info.set_is_alive(true);
        meta_service.set_node_info(1000 + index, node_info);

        vlet_info->set_disk_id(index);
    }
    meta_service.set_volume_info(vid, volume_info);

    ASSERT_EQ(AIE_FAIL, volume_meta_manager.check_safewrite("shitspace", vid, &volume)); 
}

TEST_F(AllocatorTest, check_safewrite) {
    auto* meta_client = new meta_replica::MetaSubscriber();
    g_allocator->set_meta_client(meta_client);

    uint64_t vid = 1234;
    VolumeInfo volume;
    VolumeMetaManager volume_meta_manager;
    ASSERT_EQ(AIE_NOT_EXIST, volume_meta_manager.check_safewrite("shitspace", vid, &volume));

    auto & meta_service = g_allocator->_meta_client->_meta;
    aries::pb::SpaceInfo space_info;
    space_info.set_space_name("shitspace");
    space_info.set_k(4);
    space_info.set_n(6);
    space_info.set_put_quorum(5);
    meta_service.set_space_info("shitspace", space_info);

    aries::pb::MetaReplica::VolumeInfo volume_info;
    volume_info.set_volume_id(vid);
    volume_info.set_space_name("shitspace");
    for (int index = 0; index < 6; ++index) {
        auto * vlet_info = volume_info.add_vlet_info();
        vlet_info->set_shard_index(index);
        vlet_info->set_node_addr(1000 + index);
        if (index == 0 || index == 1) {
            // vlet 0 & 1: NORMAL, and node ALIVE
            vlet_info->set_state(VLET_STATE_NORMAL);
            aries::pb::NodeInfo node_info;
            node_info.set_node_addr(1000 + index);
            node_info.set_is_alive(true);
            meta_service.set_node_info(1000 + index, node_info);
        } else if (index == 2) {
            // vlet 2: NORMAL, but node DEAD
            vlet_info->set_state(VLET_STATE_NORMAL);
            aries::pb::NodeInfo node_info;
            node_info.set_node_addr(1000 + index);
            node_info.set_is_alive(false);
            meta_service.set_node_info(1000 + index, node_info);
        } else if (index == 3) {
            // vlet 3: REPAIRING, and node ALIVE
            vlet_info->set_state(VLET_STATE_REPAIRING);
            aries::pb::NodeInfo node_info;
            node_info.set_node_addr(1000 + index);
            node_info.set_is_alive(true);
            meta_service.set_node_info(1000 + index, node_info);
        } else if (index == 4) {
            // vlet 3: REPAIRING, but node DEAD
            vlet_info->set_state(VLET_STATE_REPAIRING);
            aries::pb::NodeInfo node_info;
            node_info.set_node_addr(1000 + index);
            node_info.set_is_alive(false);
            meta_service.set_node_info(1000 + index, node_info);
        } else if (index == 5) {
            vlet_info->set_state(VLET_STATE_RECOVERING);
            aries::pb::NodeInfo node_info;
            node_info.set_node_addr(1000 + index);
            node_info.set_is_alive(true);
            meta_service.set_node_info(1000 + index, node_info);
        }
        vlet_info->set_disk_id(index);
    }
    meta_service.set_volume_info(vid, volume_info);

    ASSERT_EQ(-1, volume_meta_manager.check_safewrite("shitspace", vid, &volume)); 
}

TEST_F(AllocatorTest, update_hole_volume_meta) {
    aries::pb::Allocator_CapacityInfo info;
    std::string table_name = "test";
    uint64_t volume_num = 10;
    FLAGS_least_free_size = 0;
    _manager->collect_volume_capacity_info(info);
    EXPECT_EQ(1024 * 1024 * volume_num, info.physical_hole_space());
    EXPECT_EQ(1024 * 1024 * 200 * volume_num, info.physical_free_space());
    
    VolumeMeta meta;
    meta.space_name = table_name;
    meta.free_size = 1024 * 1024 * 300;
    meta.hole_size = 1024 * 1024 * 2;
    meta.volume_id = 0;
    g_allocator->update_volume_meta(meta);  
    _manager->collect_volume_capacity_info(info);
    EXPECT_EQ(1024 * 1024 * (volume_num - 1) + 1024 * 1024 * 2, info.physical_hole_space());
    EXPECT_EQ(1024 * 1024 * 200 * (volume_num - 1) + 1024 * 1024 * 300 , info.physical_free_space());
    
    meta.free_size = 1024 * 1024 * 100;
    meta.hole_size = 1024;
    g_allocator->update_volume_meta(meta);  
    _manager->collect_volume_capacity_info(info);
    EXPECT_EQ(1024 * 1024 * (volume_num - 1) + 1024, info.physical_hole_space());
    EXPECT_EQ(1024 * 1024 * 200 * (volume_num - 1) + 1024 * 1024 * 100 , info.physical_free_space());
}

TEST_F(AllocatorTest, check_volume_meta) {
    std::string table_name = "test_space";
    {
        g_allocator->reset();
        VolumeMeta meta;
        meta.space_name = table_name;
        meta.free_size = 1024 * 1024 * 300;
        meta.hole_size = 1024 * 1024 * 2;
        meta.readonly_size = 1024 * 1024 * 300;
        meta.volume_id = 0;
        g_allocator->check_volume_meta(meta);  
        ASSERT_EQ(g_allocator->_volume_meta_maps[table_name]->_volume_meta_map.size(), 0);
        ASSERT_EQ(g_allocator->_volume_meta_maps[table_name]->_im_volume_meta_map.size(), 0);

        meta.volume_id = 1;
        meta.readonly_size = 0;
        FLAGS_least_free_size = 1024 * 1024 * 1024;
        g_allocator->check_volume_meta(meta);
        ASSERT_EQ(g_allocator->_volume_meta_maps[table_name]->_volume_meta_map.size(), 0);
        ASSERT_EQ(g_allocator->_volume_meta_maps[table_name]->_im_volume_meta_map.size(), 0);
        FLAGS_least_free_size = 1024 * 1024;
    }
    {
        g_allocator->reset();
        VolumeMeta meta;
        meta.space_name = table_name;
        meta.free_size = 1024 * 1024 * 300;
        meta.hole_size = 1024 * 1024 * 2;
        meta.volume_id = 0;
        g_allocator->update_volume_meta(meta);  
        meta.volume_id = 1;
        g_allocator->update_volume_meta(meta);  
        ASSERT_EQ(g_allocator->_volume_meta_maps[table_name]->_volume_meta_map.size(), 2);
        ASSERT_EQ(g_allocator->_volume_meta_maps[table_name]->_im_volume_meta_map.size(), 0);


        meta.volume_id = 0;
        meta.readonly_size = 1024 * 1024 * 300;
        g_allocator->check_volume_meta(meta);
        ASSERT_EQ(g_allocator->_volume_meta_maps[table_name]->_volume_meta_map.size(), 1);
        ASSERT_EQ(g_allocator->_volume_meta_maps[table_name]->_im_volume_meta_map.size(), 1);

        meta.volume_id = 1;
        meta.readonly_size = 0;
        FLAGS_least_free_size = 1024 * 1024 * 1024;
        g_allocator->check_volume_meta(meta);
        ASSERT_EQ(g_allocator->_volume_meta_maps[table_name]->_volume_meta_map.size(), 0);
        ASSERT_EQ(g_allocator->_volume_meta_maps[table_name]->_im_volume_meta_map.size(), 2);
        FLAGS_least_free_size = 1024 * 1024;
    }
}


} // end namespace of datanode
} // end namespace of aries

int main(int argc, char* *argv) {

    ::testing::InitGoogleTest(&argc, argv);
    int ret = RUN_ALL_TESTS();
    return ret;
}

/* vim: set ts=4 sw=4 sts=4 tw=100 */
