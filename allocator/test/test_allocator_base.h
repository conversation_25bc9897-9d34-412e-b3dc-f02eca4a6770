
#ifndef BAIDU_INF_ARIES_ALLOCATOR_TEST_TEST_ALLOCATOR_BASE_H
#define BAIDU_INF_ARIES_ALLOCATOR_TEST_TEST_ALLOCATOR_BASE_H

#include <gtest/gtest.h>

/*
 * A base class for Master Unit Test.
 *
 * We have 10 VolumeMeta by default.
 * Please update the init value as needed!
 *
 * +-----------------------------+
 * |         VolumeMeta          |
 * +-----------------------------+
 * | space_name   : test_space_1 |
 * | volume_id    : 1            |
 * | max_id       : 0            |
 * | last_sync_id : 0            |
 * | free_size    : 20 GB        |
 * | total_size   : 36 GB        |
 * +-----------------------------+
 *          .............
 * +-----------------------------+
 * |         VolumeMeta          |
 * +-----------------------------+
 * | space_name   : test_space_10|
 * | volume_id    : 10           |
 * | max_id       : 0            |
 * | last_sync_id : 0            |
 * | free_size    : 20 GB        |
 * | total_size   : 36 GB        |
 * +-----------------------------+
 *
 */
namespace aries {
namespace allocator {

static const uint64_t KB = 1024;
static const uint64_t MB = 1024 * KB;
static const uint64_t GB = 1024 * MB;
static const uint64_t TB = 1024 * GB;

class AllocatorTestBase : public::testing::Test {
public:
    AllocatorTestBase() {
        init_volume_metas(_num_of_volume_meta);
    }

    ~AllocatorTestBase() {
        /*for (VolumeMeta* element : _volume_metas) {
            delete element;
        }*/
    }

    void init_volume_metas(uint64_t num_of_volume_meta) {
        for (uint64_t i = 0; i < num_of_volume_meta; ++i) {
            VolumeMeta* meta = new VolumeMeta();
            // Please update these fields as needed!
            meta->free_size = _free_space_per_volume;
            meta->total_size = _total_space_per_volume;
            meta->hole_size = _hole_space_per_volume;
            meta->readonly_size = _readonly_space_per_volume;
            meta->volume_id = i;
            _volume_metas.push_back(meta);
        }
    }

private:
    std::vector<VolumeMeta*> _volume_metas;
    const uint64_t _num_of_volume_meta = 10;
    const uint64_t _free_space_per_volume = 20 * GB;
    const uint64_t _total_space_per_volume = 36 * GB;
    int n = 18;
    const uint64_t _hole_space_per_volume = 2 * GB * 18;
    const uint64_t _readonly_space_per_volume = 1 * GB * 18;
    const std::string _space_name_default = "test_space";
};

}
}

#endif /* ARIES_ALLOCATOR_TEST_TEST_ALLOCATOR_BASE_H */
