/**
 * ARIES - A Reliable and Impressive EB-level Storage.
 * Copyright (c) 2016 Baidu.com, Inc. All Rights Reserved.
 *
 * Author: <EMAIL>
 * Date: 2016/09/29
 *
 */

#ifndef BAIDU_INF_ARIES_ALLOCATOR_CLIENT_SERVICE_H
#define BAIDU_INF_ARIES_ALLOCATOR_CLIENT_SERVICE_H 

#include <baidu/rpc/server.h>
#include "baidu/inf/aries-api/common/proto/common.pb.h"
#include "baidu/inf/aries-api/common/proto/allocator.pb.h"

namespace aries {
namespace allocator {

class BlobAllocatorClientServiceImpl : public ::aries::pb::BlobAllocatorClientService {
public:
    virtual void allocate_blob(::google::protobuf::RpcController* controller,
                            const ::aries::pb::AllocateBlobRequest* request,
                            ::aries::pb::AllocateBlobResponse* response,
                            ::google::protobuf::Closure* done);
    virtual void get_capacity_info_by_space(::google::protobuf::RpcController* controller,
                            const ::aries::pb::GetCapacityBySpaceRequest* request,
                            ::aries::pb::GetCapacityBySpaceResponse* response,
                            ::google::protobuf::Closure* done);
    virtual void list_volume(::google::protobuf::RpcController* controller, 
                            const ::aries::pb::EmptyMessage* request,
                            ::aries::pb::EmptyMessage* response, 
                            ::google::protobuf::Closure* done);
    virtual void show_allocator(::google::protobuf::RpcController* controller, 
                            const ::aries::pb::ShowAllocatorRequest* request,
                            ::aries::pb::ShowAllocatorResponse* response, 
                            ::google::protobuf::Closure* done);
    virtual void trigger_collect(::google::protobuf::RpcController* controller,
                            const ::aries::pb::TriggerCollectRequest* request,
                            ::aries::pb::AckResponse* response,
                            ::google::protobuf::Closure* done);
};

}
}

#endif
