/**
 * ARIES - A Reliable and Impressive EB-level Storage.
 * Copyright (c) 2017 Baidu.com, Inc. All Rights Reserved.
 *
 * Author: <EMAIL>
 * Date: 2017/01/09
 *
 */

#ifndef BAIDU_INF_ARIES_ALLOCATOR_CKPT_WORKER_H
#define BAIDU_INF_ARIES_ALLOCATOR_CKPT_WORKER_H

#include <thread>
#include <atomic>
#include <boost/filesystem.hpp>
#include <boost/regex.hpp>
#include <base/endpoint.h>
#include "baidu/inf/aries-api/common/lock.h"
#include "baidu/inf/aries-api/common/checksum.h"
#include "baidu/inf/aries-api/common/buffer.h"
#include "baidu/inf/aries/allocator/flags.h"
#include "baidu/inf/aries/allocator/collector.h"
#include "baidu/inf/aries/allocator/allocator.h"
#include "baidu/inf/aries/allocator/editlog.h"

namespace fs = boost::filesystem;

namespace aries {
namespace allocator {

class CkptWorker {
public:
    CkptWorker(Collector* collector, std::string image_dir, std::shared_ptr<EditLog> editlog) 
        : _ckpt_period(FLAGS_ckpt_interval_s),
        _dump_over(false), _collector(collector), _image_dir(image_dir), _editlog(editlog) {
        _is_stop = true;
        fs::path image_path(_image_dir);
        if (!fs::exists(image_path)) {
            assert(fs::create_directory(image_path));
        }
    }
    ~CkptWorker() {
        if (!_is_stop) {
            stop();
        }
    }
    void init();
    void start();
    void stop();
    void join();
    bool joinable() {
        return _thread.joinable();
    }
    inline void notify() {
        _dump_over = true;
        _cond.signal();
    }
    inline void wait_until_dump_over() {
        if (_dump_over) {
            return;
        }
        common::ScopedMutexLock lock(_cond);
        _cond.wait();
    }
  
    bool load();
    void recovery_determin_last_txid(uint64_t* txid);
        
private:
    static void run_thread(CkptWorker* ckptWorker);
    void run();
    void do_snapshot(Allocator* snapshot, uint64_t* txid);
    bool save_image(Allocator& snapshot, const uint64_t& txid);
    int load_edits(const uint64_t& from_txid);
    void purge_dirty_meta(Allocator& snapshot);
    
private:
    std::thread _thread;
    std::atomic<bool> _is_stop;
    uint64_t _ckpt_period;
    uint64_t _last_dump_time = 0ul; 
    std::atomic<bool> _dump_over;
    Collector* _collector;
    std::string _image_dir;
    std::shared_ptr<EditLog> _editlog;
    common::ConditionLock _cond;
};

class EditLogRoller {
public:
    EditLogRoller(std::shared_ptr<EditLog> editlog) : _roll_interval(FLAGS_roll_interval_s),
        _editlog(editlog) { 
        _is_stop = true;
        _last_roll_time = ::base::gettimeofday_s();
    };
    ~EditLogRoller() {  
        if (!_is_stop) {
            stop();
        }
    };
    void start();
    
    void stop();
    void join();
    bool joinable() {
        return _thread.joinable();
    }
private:
    void run();
    static void run_thread(EditLogRoller* roller);
    std::thread _thread;
    std::atomic<bool> _is_stop;
    uint64_t _roll_interval;// mearsure in second
    uint64_t _last_roll_time; 
    std::shared_ptr<EditLog> _editlog;

};
}//end of namespace
}

#endif

