/**
 * ARIES - A Reliable and Impressive EB-level Storage.
 * Copyright (c) 2016 Baidu.com, Inc. All Rights Reserved.
 *
 * Author: <EMAIL>
 *         <EMAIL>
 * Date: 2016/09/29
 *
 */

#ifndef BAIDU_INF_ARIES_ALLOCATOR_COLLECTOR_H
#define BAIDU_INF_ARIES_ALLOCATOR_COLLECTOR_H

#include <cstdint>
#include <thread>
#include <atomic>
#include <base/endpoint.h>
#include "baidu/rpc/server.h"
#include "baidu/rpc/policy/hasher.h"
#include "baidu/inf/aries-api/common/proto/allocator.pb.h"
#include "baidu/inf/aries-api/common/proto/datanode.pb.h"
#include "baidu/inf/aries/allocator/flags.h"
#include "allocator/finder.h"
#include "baidu/inf/aries-api/common/coding.h"
#include "baidu/inf/aries-api/common/meta.h"
#include "baidu/inf/aries-api/common/lock.h"
#include "meta_replica/meta_subscriber.h"

namespace aries {
namespace allocator {

#ifdef _UNIT_TEST
typedef aries::pb::SpaceInfo (*HOOK_inject_space_info)();
typedef void (*HOOK_get_datanode_list)(std::vector<uint64_t>* list);

void inject_space_info_4ut(HOOK_inject_space_info f);
#endif

struct VolumeCollectMeta {
    VolumeCollectMeta() {}

    VolumeCollectMeta(uint32_t num, uint32_t k, uint64_t volume_id, std::string space_name,
                       uint32_t least_shard)
        : shard_num(num), space_k(k), volume_id(volume_id), free_size(UINT64_MAX),
        total_size(0), hole_size(0), data_vlet_used_size(0), encode_vlet_used_size(0),
        least_shard(least_shard), space_name(space_name) {

        char* str = reinterpret_cast<char*>(&volume_id);
        code = ::baidu::rpc::policy::MurmurHash32(str, sizeof(uint32_t));
        for (uint32_t i = 0; i < shard_num; ++i) {
            collected_vlets.push_back(false);
        }
    }

    uint32_t shard_num;
    uint32_t space_k;
    uint32_t code;
    uint64_t volume_id;
    uint64_t free_size;
    uint64_t total_size;
    uint64_t hole_size;
    uint64_t data_vlet_used_size;
    uint64_t encode_vlet_used_size;
    uint32_t least_shard;
    std::string space_name;
    std::vector<bool> collected_vlets;
};

class Collector {
public:
    Collector() : _is_stop(false),
                  _collect_over(false),
                  _need_trigger_collect(false),
                  _meta_client(NULL),
                  _collect_task_concurrency(0),
                  _remain_collect_task_num(0){}

    ~Collector() {
        if (!_is_stop) {
            stop();
        }
    }
    bool start();
    void stop();

    void set_meta_client(meta_replica::MetaSubscriber* meta_client) {
        _meta_client = meta_client;
    }

    inline void notify() {
        _collect_over = true;
        _cond.signal();
    }
    inline void wait_until_collect_over() {
        if (_collect_over) {
            return;
        }
        common::ScopedMutexLock lock(_cond);
        _cond.wait();
    }
    inline void set_node_list(std::vector<uint64_t>* list) {
        common::ScopedMutexLock lock(_cond);
        _node_list.clear();
        _node_list.insert(_node_list.begin(), list->begin(), list->end());
    }
    inline void node_list(std::vector<uint64_t>& ret) {
        common::ScopedMutexLock lock(_cond);
        ret.insert(ret.begin(), _node_list.begin(), _node_list.end());
    }
    inline void trigger_collect() {
        _need_trigger_collect = true;
    }

private:
    static void* run_thread(void* args);
    void run();
    void update_volume_metas();
    void get_vlet_info_done(aries::pb::GetVletInfoRequest* request,
            aries::pb::GetVletInfoResponse* response, uint64_t node);

    void collect_volume_metas(std::vector<uint64_t>& server_list);
    void collect();
    void list_node(std::vector<uint64_t>* list);

private:
    std::atomic<bool> _is_stop;
    uint64_t _last_collect_time = 0ul;
    std::vector<uint64_t> _node_list;
    std::map<uint64_t, VolumeCollectMeta> _collect_meta;
    typedef std::map<uint64_t, VolumeCollectMeta>::iterator MapIter;
    std::atomic<bool> _collect_over;
    std::atomic<bool> _need_trigger_collect;
    common::ConditionLock _cond;
    meta_replica::MetaSubscriber* _meta_client;
    std::atomic<int> _collect_task_concurrency;
    std::atomic<int> _remain_collect_task_num;
    common::MutexLock _mutex;
    common::ConditionLock _has_collected_cond;
    common::ConditionLock _to_collect_cond;
    uint64_t _max_collect_task_concurrency;
};

extern Collector* g_collector;

}
}

#endif

