/**
 * @file test_task_manager.cpp
 * <AUTHOR>
 * @date 2021/12/27 20:07:22
 * @version 1.0
 * @brief 
 * 
 */
#include <gtest/gtest.h>

#include "baidu/inf/aries/tapenode/task_manager.h"
#include "baidu/inf/aries/tapenode/task_context.h"
#include "baidu/inf/aries/tapenode/flags.h"

namespace aries::tapenode {

class TaskManagerTest : public ::testing::Test {
public:
    void SetUp() {}
    void TearDown() {}
};

TEST_F(TaskManagerTest, test_task_manager) {
    ::system("rm -rf ./file");
    LOG(WARNING) << FLAGS_tapenode_snapshot_path;
    FLAGS_tapenode_snapshot_path = "./file";
    TaskCtxPtr ctx = std::make_shared<TransformTaskContext>();
    std::vector<TaskCtxPtr> ctx_vec;
    ctx->worker_id = 1;
    ctx->task_id = 456;
    ctx->task_timeout = 0;
    ctx->start_timestamp = base::gettimeofday_us();
    ctx->tape_store_file_path = "/ltfsee/aries/3232154.lt";
    std::unique_ptr<TaskManager> task_manager = std::make_unique<TaskManager>();
    int ret = task_manager->save_task(ctx);
    ASSERT_EQ(ret, AIE_INVALID);
    ret = task_manager->add_task(ctx);
    ASSERT_EQ(ret, AIE_OK);
    ret = task_manager->save_task(ctx);
    ASSERT_EQ(ret, AIE_OK);
    ret = task_manager->save_task(ctx);
    ASSERT_EQ(ret, AIE_EXIST);
    ret = task_manager->add_task(ctx);
    ASSERT_EQ(ret, AIE_EXIST);
    task_manager->list_tasks(&ctx_vec);
    ASSERT_EQ(ctx_vec.size(), 1);
    task_manager->finish_task(ctx);
    task_manager->finish_task(ctx);
    ctx_vec.clear();
    task_manager->list_tasks(&ctx_vec);
    ASSERT_EQ(ctx_vec.size(), 0);

    ctx_vec.clear();
    task_manager->add_task(ctx);
    task_manager->save_task(ctx);
    task_manager->list_saved_tasks(&ctx_vec);
    ASSERT_EQ(ctx_vec.size(), 1);
    ctx_vec.clear();
    std::unique_ptr<TaskManager> task_manager_ptr = std::make_unique<TaskManager>();
    task_manager_ptr->list_tasks(&ctx_vec);
    ASSERT_EQ(ctx_vec.size(), 1);

    ctx_vec.clear();
    FLAGS_transform_task_expire_time_s = 0;
    std::unique_ptr<TaskManager> task_manager_ptr_ = std::make_unique<TaskManager>();
    task_manager_ptr_->list_tasks(&ctx_vec);
    ASSERT_EQ(ctx_vec.size(), 0);
}

}