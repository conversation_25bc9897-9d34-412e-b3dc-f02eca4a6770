/**
 * @file test_tapenode.cpp
 * <AUTHOR>
 * @date 2022/01/24 16:15:24
 * @version 1.0
 * @brief 
 * 
 */

#include <gtest/gtest.h>

#include "baidu/inf/aries/tapenode/limit_manager.h"

namespace aries::tapenode {

class LimitManagerTest : public ::testing::Test {
public:
    void SetUp() {}
    void TearDown() {}
};

TEST_F(LimitManagerTest, test_limit_manager_update_conf) {
    aries::pb::TapeCenterLimitParam limit_params;
    g_limit_manager = std::make_shared<LimitManager>();
    limit_params.set_max_get_blob_flow_kb(UINT32_MAX);
    limit_params.set_max_get_blob_num_per_second(UINT32_MAX);
    limit_params.set_max_put_blob_flow_kb(UINT32_MAX);
    limit_params.set_max_put_blob_num_per_second(UINT32_MAX);
    g_limit_manager->update_conf(limit_params);
    
    ASSERT_EQ(g_limit_manager->_max_get_blob_num_per_second, UINT32_MAX);
    ASSERT_EQ(g_limit_manager->_max_get_blob_flow_kb, UINT32_MAX);
    ASSERT_EQ(g_limit_manager->_max_put_blob_num_per_second, UINT32_MAX);
    ASSERT_EQ(g_limit_manager->_max_put_blob_flow_kb, UINT32_MAX);
}

}