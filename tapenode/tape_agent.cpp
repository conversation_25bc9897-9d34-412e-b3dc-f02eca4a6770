/**
 * @file tape_agent.cpp
 * <AUTHOR>
 * @date 2021/12/13 16:04:31
 * @version 1.0
 * @brief 
 * 
 */
#include "tape_agent.h"

#include <sys/xattr.h>

#include "baidu/inf/aries/common/common.h"
#include "baidu/inf/aries/tapenode/tape_store.h"

namespace aries::tapenode {

ARIES_BVAR_RECORDER(tape_node, transform_task_waitting_queue_size);
ARIES_BVAR_RECORDER(tape_node, retrieve_task_waitting_queue_size);
ARIES_BVAR_AVERAGER_WITH_WINDOW(tape_node, cal_migrate_crc_latency_s, 300);
ARIES_BVAR_AVERAGER_WITH_WINDOW(tape_node, cal_retrieve_crc_latency_s, 300);

std::unique_ptr<TapeAgent> g_tape_agent = std::make_unique<TapeAgent>();

TapeAgent::TapeAgent() {}

void TapeAgent::start() {
    _is_stopped = false;
    LOG(NOTICE) << "tape agent worker " << std::hex << this << " started";
    for (int i = 0; i < FLAGS_tape_agent_migrate_thread_num; i++) {
        auto thread = std::thread(std::mem_fn(&TapeAgent::migrate_routine), this);
        _worker_threads.push_back(std::move(thread));
    }
    for (int i = 0; i < FLAGS_tape_agent_retrieve_thread_num; i++) {
        auto thread = std::thread(std::mem_fn(&TapeAgent::retrieve_routine), this);
        _worker_threads.push_back(std::move(thread));
    }
    
}

void TapeAgent::stop() {
    _is_stopped = true;
    for (auto & worker_thread : _worker_threads) {
        if (worker_thread.joinable()) {
            worker_thread.join();
        }
    }
}

void TapeAgent::migrate_routine() {
    uint64_t last_migrate_time = base::gettimeofday_s();
    while (!_is_stopped) {
        if(_migrate_queue.size() > FLAGS_migrate_total_num || base::gettimeofday_s() - last_migrate_time > FLAGS_migrate_interval_s) {
            ARIES_DEBUG_LOG(TRACE) << "start do migrate, task number:" << _migrate_queue.size();
            last_migrate_time = base::gettimeofday_s();
            do_migrate();
        }
        g_tape_node_transform_task_waitting_queue_size_recorder.set_value(_migrate_queue.size());
        std::this_thread::sleep_for(std::chrono::milliseconds(FLAGS_tape_agent_check_task_timespan_ms));
    }
}

void TapeAgent::retrieve_routine() {
    uint64_t last_retrieve_time = base::gettimeofday_s();
    while (!_is_stopped) {
        if(_retrieve_queue.size() > FLAGS_retrieve_total_num || base::gettimeofday_s() - last_retrieve_time > FLAGS_retrieve_interval_s) {
            ARIES_DEBUG_LOG(TRACE) << "start do retrieve, task number:" << _retrieve_queue.size();
            last_retrieve_time = base::gettimeofday_s();
            do_retrieve();
        } 
        g_tape_node_retrieve_task_waitting_queue_size_recorder.set_value(_retrieve_queue.size());
        std::this_thread::sleep_for(std::chrono::milliseconds(FLAGS_tape_agent_check_task_timespan_ms));
    }
}

int TapeAgent::init() {
    if (FLAGS_mock_ltfs){
        _ltfsee = std::make_unique<MockLtfsEE>(FLAGS_temp_dir_path, FLAGS_eeadm_path);
    } else {
        _ltfsee = std::make_unique<LtfsEE>(FLAGS_temp_dir_path, FLAGS_eeadm_path);
    }
    return _ltfsee->init();
}

int TapeAgent::migrate(std::shared_ptr<TaskContext> ctx) {
    common::ScopedMutexLock lock(_mutex);
    if (FLAGS_migrate_queue_max_size < _migrate_queue.size()) {
        LOG(WARNING) << "migrate queue is full current_size:" << _migrate_queue.size();
        return AIE_BUSY;
    }
    _migrate_queue.emplace_back(ctx);
    LOG(NOTICE) << "migrate task emplace back, task_id:" << ctx->task_id << " vid:" << ctx->volume_id;
    return AIE_OK;
}

int TapeAgent::retrieve(const std::vector<std::shared_ptr<RetrieveTaskContext>>& ctx_list) {
    for (const auto& ctx : ctx_list) {
        for (int i = 0; i < ctx->location.location_on_tape_list_size(); i++) {
            auto physical_pool_name = ctx->location.location_on_tape_list(i).physical_pool_name();
            if (!_ltfsee->check_tape_id(physical_pool_name)) {
                LOG(WARNING) << "invalid pool_name:" << physical_pool_name;
                return AIE_FAIL;
            }
        }
    }
    common::ScopedMutexLock lock(_mutex);
    for (const auto& ctx : ctx_list) {
        _retrieve_queue.emplace_back(ctx);
    }
    return AIE_OK;
}

int TapeAgent::remove_from_disk(std::string& filename) {
    std::vector<std::string> file_path_vec{filename};
    std::vector<TapeFileInfo> tape_file_info_vec;
    auto ret = _ltfsee->migrate(file_path_vec, &tape_file_info_vec);
    if (ret != AIE_OK || tape_file_info_vec.empty()) { 
        LOG(WARNING) << "remove from disk failed, filename:" << filename
                << " code:" << ret;
        return AIE_FAIL;
    }
    if (tape_file_info_vec[0].code != AIE_OK){
        LOG(WARNING) << "remove from disk failed, filename:" << filename
                << " code:" << tape_file_info_vec[0].code
                << " file_state:" << tape_file_info_vec[0].state;
    }
    LOG(NOTICE) << "remove from disk succeeded, filename:" << filename
            << " file_state:" << tape_file_info_vec[0].state;
    return AIE_OK;
}

int TapeAgent::do_migrate() {   
    _mutex.lock();
    if (_migrate_queue.empty()) {
        _mutex.unlock();
        return AIE_OK;
    }                
    std::unordered_map<std::string, std::shared_ptr<TaskContext>> waiting_ctx_map;       
    std::vector<std::string> file_path_vec;
    std::vector<TapeFileInfo> tape_file_info_vec;
    std::vector<std::shared_ptr<TaskContext>> ctx_vec;
    //1.将_migrate_queue中的任务取出, 一次进行的搬迁任务数小于FLAGS_migrate_total_num
    while (!_migrate_queue.empty() && waiting_ctx_map.size() < FLAGS_migrate_total_num) {
        auto ctx = _migrate_queue.front();
        ctx->cost_tracker->add_stage("waiting_in_queue");
        ctx->is_succ = true;
        _migrate_queue.pop_front();
        if (!check_files_before_migrate(ctx)) {
            ctx->is_succ = false;
            ctx->finish_timestamp = base::gettimeofday_us();
            g_transform_executor->queue_exec(ctx);
            continue;
        }
        ctx->cost_tracker->add_stage("cal_crc");
        g_tape_node_cal_migrate_crc_latency_s_averager << ctx->cost_tracker->recent_cost_time_us() / (1000 * 1000);
        for (auto& store_ptr : ctx->store_ptr_list) {
            file_path_vec.emplace_back(store_ptr->name());
            waiting_ctx_map.insert(std::make_pair(store_ptr->name(), ctx));
        }
        ctx_vec.push_back(ctx);
    }
    _mutex.unlock();
    if (file_path_vec.empty()) {
        LOG(NOTICE) << "file path empty";
        return AIE_EMPTY;
    }
    //2.调ltfsee进行migrate
    auto ret = _ltfsee->migrate(file_path_vec, &tape_file_info_vec);
    if (ret != AIE_OK) {    
        for (auto & ctx : ctx_vec) {
            ctx->finish_timestamp = base::gettimeofday_us();
            ctx->is_succ = false;
            g_transform_executor->queue_exec(ctx);
            LOG(WARNING) << "ltfsee migrate failed, error_code :" << ret << " vid:" << ctx->volume_id 
                    << " task_id:" << ctx->task_id;
        }
        return AIE_FAIL;
    }
    //3.根据ltfsee返回的结果, 将ctx加入到Executor中, 等待执行callback
    std::set<uint64_t> retry_task_set;
    for (auto & file_info : tape_file_info_vec) {
        auto iter = waiting_ctx_map.find(file_info.path);
        if (iter == waiting_ctx_map.end()) {
            LOG(FATAL) << "can't find in waiting_ctx_map, file_path:" << file_info.path;
            continue;
        }
        auto ctx = iter->second;
        if (retry_task_set.find(ctx->task_id) != retry_task_set.end()) {
            continue;
        }
        if (file_info.code == AIE_AGAIN) {
            ctx->is_succ = false;
            retry_task_set.insert(ctx->task_id);
            LOG(WARNING) << "task try again. task_id:" << file_info.task_id
                << " file_path:" << file_info.path;
            common::ScopedMutexLock lock(_mutex);
            _migrate_queue.emplace_back(ctx);
            continue;
        }
        if (file_info.code == AIE_NOT_EXIST) {
            LOG(WARNING) << "File not exist. task_id:" << file_info.task_id
                << " file_path:" << file_info.path;
        }

        if (file_info.code != AIE_OK) {  
            ctx->is_succ = false;
            LOG(WARNING) << "fail to migrate, task_id:" << ctx->task_id
                << " file_path:" << file_info.path << " ltfsee_task_id:" << file_info.task_id;
            continue;
        }
    }
    for (auto & file_info : tape_file_info_vec) {
        auto iter = waiting_ctx_map.find(file_info.path);
        if (iter == waiting_ctx_map.end()) {
            LOG(FATAL) << "can't find in waiting_ctx_map, file_path:" << file_info.path;
            continue;
        }
        auto ctx = iter->second;
        if (!ctx->is_succ) {
            continue;
        }
        if (retry_task_set.find(ctx->task_id) != retry_task_set.end()) {
            continue;
        }
        for (int i = 0; i < ctx->location.file_location_on_tape_list_size(); i++) {
            ctx->location.mutable_location_on_tape_list()->
                    CopyFrom(file_info.location_on_tape.location_on_tape_list());
        }
    }
    for (auto& ctx : ctx_vec) {
        if (retry_task_set.find(ctx->task_id) != retry_task_set.end()) {
            continue;
        }
        ctx->finish_timestamp = base::gettimeofday_us();
        g_transform_executor->queue_exec(ctx);
        LOG(NOTICE) << "migrate done, task_id:" << ctx->task_id << " vid:" << ctx->volume_id
                << " is_succ:" << ctx->is_succ;
    }
    return AIE_OK;
}

int TapeAgent::do_retrieve() {
    _mutex.lock();
    if (_retrieve_queue.empty()) {
        _mutex.unlock();
        return AIE_OK;
    } 
    std::unordered_map<std::string, std::vector<std::shared_ptr<RetrieveTaskContext>>> waiting_ctx_map;
    std::vector<std::shared_ptr<RetrieveTaskContext>> ctx_vec;
    std::vector<std::string> file_path_vec;
    std::vector<TapeFileInfo> tape_file_info_vec;
    std::string tape_id = "";
    std::string pool_name = "";
    while (!_retrieve_queue.empty()) {
        //1.从retrieve_queue中取出目标physical_pool_name相同的ctx(相同physical_pool_name的ctx连续存储在retrieve_queue中)
        auto front = _retrieve_queue.front();
        std::shared_ptr<RetrieveTaskContext> ctx = std::dynamic_pointer_cast<RetrieveTaskContext>(front);
        if (tape_id.empty()) {
            tape_id = ctx->optimal_tape.tape_id();
            pool_name = ctx->optimal_tape.physical_pool_name();
            ctx->cost_tracker->add_stage("waiting_in_queue");
            for (auto& iter : ctx->tape_file_map) {
                waiting_ctx_map[iter.second.file_path].push_back(ctx);
            }
            ctx->is_succ = true;
            ctx_vec.push_back(ctx);
            _retrieve_queue.pop_front();
        } else {
            if (ctx->optimal_tape.tape_id() == tape_id) {
                for (auto& iter : ctx->tape_file_map) {
                    if (!iter.second.vbid_list.empty()) {
                        waiting_ctx_map[iter.second.file_path].push_back(ctx);
                    }
                }
                ctx->cost_tracker->add_stage("waiting_in_queue");
                ctx->is_succ = true;
                ctx_vec.push_back(ctx);
                _retrieve_queue.pop_front();
            } else {
                break;
            }
        }
    }
    _mutex.unlock();
    if (waiting_ctx_map.empty()) {
        LOG(NOTICE) << "waiting_ctx_map is empty";
        return AIE_OK;
    }
    for (auto & i : waiting_ctx_map) {
        //2.构建需要取回的文件列表, waiting_ctx_map中key为file name, value为file name对应的ctx列表
        file_path_vec.push_back(i.first);
    }
    //3.调用ltfsee进行取回
    auto ret = _ltfsee->retrieve(file_path_vec, pool_name, &tape_file_info_vec);
    if (ret != AIE_OK) {
        for (auto & iter : waiting_ctx_map) {
            for (auto ctx : iter.second) {
                ctx->finish_timestamp = base::gettimeofday_us();
                ctx->is_succ = false;
                g_retrieve_executor->queue_exec(ctx);
            }
        }
        LOG(WARNING) << "ltfsee retrieve failed, error_code :" << ret;
        return AIE_FAIL;
    }
    //4.根据ltfsee返回的结果, 将ctx加入到Executor中, 等待执行callback
    for (auto& file_info : tape_file_info_vec) {
        auto iter = waiting_ctx_map.find(file_info.path);
        if (iter == waiting_ctx_map.end()) {
            LOG(FATAL) << "can't find in waiting_ctx_map file_path:" << file_info.path;
            continue;
        }
        auto vec = iter->second;
        if (file_info.code != AIE_OK) {
            if (file_info.code == AIE_NOT_EXIST) {
                LOG(WARNING) << "File not exist, file_path:" << file_info.path;
            }
            for (auto ctx : vec) {
                ctx->finish_timestamp = base::gettimeofday_us();
                ctx->is_succ = false;
            }
            continue;
        }
        std::shared_ptr<aries::tapenode::TapeStore> store_ptr;
        store_ptr.reset(TapeStore::open(file_info.path, true));
        if (store_ptr == nullptr) {
            file_info.code = AIE_FAIL;
            LOG(WARNING) << "open TapeStore failed, file:" << file_info.path;
        } else {
            base::Timer timer;
            timer.start();
            if (!check_file_after_retrieve(store_ptr)) {
                file_info.code = AIE_FAIL;
                LOG(FATAL) << "check file after retrieve failed, filename:" << file_info.path;
            } 
            timer.stop();
            g_tape_node_cal_retrieve_crc_latency_s_averager << timer.s_elapsed() / 60;
        }

        for (auto ctx : vec) {
            if (!ctx->is_succ) {
                continue;
            }
            ctx->finish_timestamp = base::gettimeofday_us();
            ctx->is_succ = file_info.code == AIE_OK ? true : false;
            if (ctx->is_succ) {
                for (auto& tape_file : ctx->tape_file_map) {
                    if (tape_file.second.file_path == file_info.path) {
                        tape_file.second.store_ptr = store_ptr;
                        break;
                    }
                }
            }
        }
    }
    for (auto ctx : ctx_vec) {
        ctx->finish_timestamp = base::gettimeofday_us();
        g_retrieve_executor->queue_exec(ctx);
        LOG(NOTICE) << "retrieve done, task_id:" << ctx->task_id << " vid:" << ctx->volume_id
                << " is_succ:" << ctx->is_succ;
    }
    return AIE_OK;
}

bool TapeAgent::check_files_before_migrate(std::shared_ptr<TaskContext> ctx) const {
    for (auto ptr : ctx->store_ptr_list) {
        if (!ptr->inited()) {
            TapeFileInfo tape_file_info;
            auto ret = _ltfsee->get_state(ptr->name(), &tape_file_info);
            if (tape_file_info.state == FILE_MIGRATE) {
                continue; 
            } else if (tape_file_info.state == FILE_NOT_EXIST) {
                return false;
            }
        }
        if (!check_file_before_migrate(ptr)) {
            LOG(FATAL) << "check file before migrate failed, filename:" << ptr->name()
                    << " vid:" << ctx->volume_id;
            return false;
        }
    }
    return true;
}

bool TapeAgent::check_file_before_migrate(std::shared_ptr<aries::tapenode::TapeStore> store_ptr) const {
    int woflags = O_RDWR;
    uint32_t footer_crc;
    auto fd = ::open(store_ptr->name().c_str(), woflags, 0644); 
    if (fd < 0) {
        ARIES_FUNC_LOG(FATAL) << "open TapeStore failed, filename:" << store_ptr->name().c_str()
            << " errno:(" << errno << ") " << strerror(errno);
        return false;
    }
    auto footer = store_ptr->get_footer();
    if (!check_data_crc(fd, footer.file_size, footer.file_crc)) {
        ARIES_FUNC_LOG(FATAL) << "check file footer crc failed, filename:" << store_ptr->name().c_str();
        ::close(fd);
        return false;
    }
    auto file_crc = cal_data_crc(fd, footer.file_size, footer.file_size + PAGE_SIZE_4K, footer.file_crc);
    if (set_file_xattr_md5(fd, file_crc) != AIE_OK) {
        ARIES_FUNC_LOG(FATAL) << "set file xattr failed, filename:" << store_ptr->name().c_str();
        ::close(fd);
        return false;
    }
    ::close(fd);
    return true;
}

bool TapeAgent::check_file_after_retrieve(std::shared_ptr<aries::tapenode::TapeStore> store_ptr) const {
    int woflags = O_RDWR;
    uint32_t footer_crc;
    auto fd = ::open(store_ptr->name().c_str(), woflags, 0644); 
    if (fd < 0) {
        ARIES_FUNC_LOG(FATAL) << "open TapeStore failed, filename:" << store_ptr->name().c_str()
            << " errno:(" << errno << ") " << strerror(errno);
        return false;
    }
    uint32_t file_crc;
    auto status = get_file_xattr_md5(fd, &file_crc);
    if (status == -1) {
        ARIES_FUNC_LOG(FATAL) << "set file xattr failed, filename:" << store_ptr->name().c_str();
        ::close(fd);
        return false;
    }
    auto footer = store_ptr->get_footer();
    auto ret = check_data_crc(fd, footer.file_size + PAGE_SIZE_4K, file_crc);
    ::close(fd);
    return ret;
}

int TapeAgent::set_file_xattr_md5(int fd, uint32_t crc) const {
    std::string key = FILE_XATTR_MD5SUM_KEY;
    int ret = fsetxattr(fd, key.c_str(), &crc, 4, 0);
    if (ret != 0) {
        ARIES_FUNC_LOG(WARNING) << "set xattr error, fd:" << fd << " file_name:" 
                << " key:" << "user.md5sum" << " value:" << crc 
                << " errno:(" << errno << ") " << strerror(errno);
        return AIE_FAIL;
    }
    return AIE_OK;
}

bool TapeAgent::check_data_crc(int fd, uint64_t size, uint32_t expect_crc) const {
    uint32_t file_crc = cal_data_crc(fd, 0, size, 0);
    if (file_crc != expect_crc) {
        LOG(WARNING) << "check data crc failed, fd:" << fd << " expect_crc:" << expect_crc << "file_crc"
                << file_crc << " size:" << size;
        return false;
    }
    return true;
}

uint32_t TapeAgent::cal_data_crc(int fd, uint64_t offset, uint64_t size, uint32_t current_crc) const {
    uint64_t read_block_size = PAGE_SIZE_4K * 1024;
    uint32_t data_crc = current_crc;
    for (; offset < size ; offset += read_block_size) {
        if (size - offset <= read_block_size) {
            read_block_size = size - offset;
        }
        common::Buffer buffer(read_block_size);
        ssize_t bytes_read = ::pread(fd, buffer.buf(), buffer.size(), offset);
        if (bytes_read != int64_t(buffer.size())) {
            ARIES_FUNC_LOG(WARNING) << "read error, fd:" << fd << " offset:" << offset
                    << " size:" << buffer.size() << " read:" << bytes_read << " errno:(" << errno << ") "
                    << strerror(errno);
            return -1;
        }
        data_crc = base::crc32c::Extend(data_crc, buffer.buf(), buffer.size());
    }
    return data_crc;
}

int TapeAgent::get_file_xattr_md5(int fd, uint32_t* crc) const {
    auto key = FILE_XATTR_MD5SUM_KEY;
    int ret = ::fgetxattr(fd, key.c_str(), crc, 4);
    if (ret == -1) {
        ARIES_FUNC_LOG(WARNING) << "get xattr error, fd:" << fd
                << " key:" << key << " value:" << *crc 
                << " size:"<< ret << " errno:(" << errno << ") " << strerror(errno);
        return -1;
    }
    return 0;
}

Status TapeAgent::get_file_state(std::shared_ptr<TaskContext> ctx, pb::VolumeLocationOnTape* location) {
    if (ctx->tape_store_file_path.empty()) {
        return Status(AIE_EMPTY);
    }
    std::string file_name = ctx->tape_store_file_path;
    TapeFileInfo tape_file_info;
    auto ret = _ltfsee->get_state(file_name, &tape_file_info);
    if (ret.code() != AIE_OK) {
        return ret;
    }
    if (tape_file_info.state == 4) {
        return AIE_NOT_EXIST;
    } 
    if (tape_file_info.state == 3) {
        return AIE_FAIL;
    }
    location->CopyFrom(tape_file_info.location_on_tape);

    return ret;
}
Status TapeAgent::reconcile_tape(std::shared_ptr<ReclaimTaskContext> ctx) {
    return _ltfsee->reconcile(ctx);
}
Status TapeAgent::reclaim_tape(std::string tape_id, std::string physical_pool_name, std::string* dst_tape) {
    return _ltfsee->reclaim(tape_id, physical_pool_name, dst_tape);
}

bool TapeAgent::is_busy() {
    return _retrieve_queue.size() != 0;
}

}
