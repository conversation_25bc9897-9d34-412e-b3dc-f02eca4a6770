/***************************************************************************
 * 
 * Copyright (c) 2022 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/

/**
 * @file mock_ltfsee.h
 * <AUTHOR>
 * @date 2022/03/07 10:48:54
 * @version 1.0
 * @brief 
 * 
 */

#pragma once

#include "baidu/inf/aries-api/common/common.h"
#include "baidu/inf/aries/tapenode/ltfsee.h"

namespace aries::tapenode {
extern std::vector<std::string> mock_pool_set;

class MockLtfsEE : public LtfsEE {
public:
    MockLtfsEE(const std::string& temp_dir_path, const std::string& eeadm_path) : Lt<PERSON><PERSON>(temp_dir_path, eeadm_path) {}
    virtual ~MockLtfsEE() {}
    int migrate(const std::vector<std::string>& file_list, std::vector<TapeFileInfo>* tape_file_info_vec);
    int retrieve(const std::vector<std::string>& file_list, 
            const std::string& pool_name, std::vector<TapeFileInfo>* tape_file_info_vec);
    Status get_state(const std::string& file_path, TapeFileInfo* tape_file_info);
    std::set<std::string> get_pool();
    int init();
private:
    /**
     * @brief 
     * 
     * @param file_list: migrate/retrieve file list
     * @param state: 1:migrate 2:premigrate 3:resident
     * @param tape_file_info_vec: result of migrate/retrieve
     * @return int 
     */
    int mock_to_tape_file_info(const std::vector<std::string>& file_list, int state,
                                    std::vector<TapeFileInfo>* tape_file_info_vec) const;
};

} // aries::tapenode