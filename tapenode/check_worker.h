/**
 * @file check_worker.h
 * <AUTHOR>
 * @date 2022/09/07 19:50:23
 * @version 1.0
 * @brief 
 * 
 */
#pragma once

#include <atomic>
#include <memory>
#include <thread>
#include <unordered_map>
#include <unordered_set>

#include "baidu/inf/aries-api/proxy/proxy.h"

#include "baidu/inf/aries-api/common/lock.h"
#include "baidu/inf/aries-api/common/speed_limit.h"
#include "baidu/inf/aries-api/common/proto/tapecenter.pb.h"
#include "baidu/inf/aries-api/common/proto/data_agent.pb.h"

#include "baidu/inf/aries/common/status.h"
#include "baidu/inf/aries/tapenode/tape_store.h"
#include "baidu/inf/aries/tapenode/task_context.h"
#include "baidu/inf/aries/tapenode/meta_handler.h"

namespace aries::tapenode {

class CheckWorker {
public:
    CheckWorker();
    virtual ~CheckWorker();

    void start();
    void stop();
    void report_check_task(std::shared_ptr<TaskContext> tctx);

    void set_worker_id(int16_t id) {
        _worker_id = id;
    }

    int16_t worker_id() const {
        return _worker_id;
    }

private:
    void thread_func();
    void do_check_work(std::shared_ptr<TaskContext> ctx);
    int fetch_check_task(std::shared_ptr<TaskContext> ctx);

private:
    int16_t _worker_id;
    std::thread _worker_thread;
    std::atomic<bool> _is_stopped;
    std::mutex _mutex;
    std::unordered_map<uint64_t, std::shared_ptr<TaskContext>> _ctx_map;
};

}


/* vim: set ts=4 sw=4 sts=4 tw=100 */