/***************************************************************************
 * 
 * Copyright (c) 2021 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/
 
 /**
 * @file limit_manager.cpp
 * <AUTHOR>
 * @date 2021/11/21 21:20:58
 * @version 1.0 
 * @brief 
 *  
 **/

#include "baidu/inf/aries/tapenode/limit_manager.h"

namespace aries::tapenode {

std::shared_ptr<LimitManager> g_limit_manager;

LimitManager::LimitManager(){
    _get_blob_token_pool =
        std::make_unique<common::TokenPool>(MAX_GET_BLOB_TOKEN_NUM, DEFAULT_GET_BLOB_NUM_PER_SECOND);
    _get_blob_flow_pool =
        std::make_unique<common::TokenPool>(MAX_GET_BLOB_FLOW_CAPACITY, DEFAULT_GET_BLOB_FLOW_PER_SECOND);
    _put_blob_token_pool =
        std::make_unique<common::TokenPool>(MAX_PUT_BLOB_TOKEN_NUM, DEFAULT_PUT_BLOB_NUM_PER_SECOND);
    _put_blob_flow_pool =
        std::make_unique<common::TokenPool>(MAX_PUT_BLOB_FLOW_CAPACITY, DEFAULT_PUT_BLOB_FLOW_PER_SECOND);
}

LimitManager::~LimitManager() {
}

void LimitManager::update_conf(const aries::pb::TapeCenterLimitParam& limit_params) {
    common::ScopedMutexLock lock(_mutex);
    if (limit_params.has_max_get_blob_num_per_second()
            && std::fabs(limit_params.max_get_blob_num_per_second() - _max_get_blob_num_per_second) > 10e-6) {
        _max_get_blob_num_per_second = limit_params.max_get_blob_num_per_second();
        _get_blob_token_pool->reset(MAX_GET_BLOB_TOKEN_NUM, _max_get_blob_num_per_second);
    }
    if (limit_params.has_max_get_blob_flow_kb()
            && std::fabs(limit_params.max_get_blob_flow_kb() - _max_get_blob_flow_kb) > 10e-6) {
        _max_get_blob_flow_kb = limit_params.max_get_blob_flow_kb();
        _get_blob_flow_pool->reset(MAX_GET_BLOB_FLOW_CAPACITY, _max_get_blob_flow_kb);
    }
    if (limit_params.has_max_put_blob_num_per_second()
            && std::fabs(limit_params.max_put_blob_num_per_second() - _max_put_blob_num_per_second) > 10e-6) {
        _max_put_blob_num_per_second = limit_params.max_put_blob_num_per_second();
        _put_blob_token_pool->reset(MAX_PUT_BLOB_TOKEN_NUM, _max_put_blob_num_per_second);
    }
    if (limit_params.has_max_put_blob_flow_kb()
            && std::fabs(limit_params.max_put_blob_flow_kb() - _max_put_blob_flow_kb) > 10e-6) {
        _max_put_blob_flow_kb = limit_params.max_put_blob_flow_kb();
        _put_blob_flow_pool->reset(MAX_PUT_BLOB_FLOW_CAPACITY, _max_put_blob_flow_kb);
    }
}

} // end namespace of datanode

/* vim: set ts=4 sw=4 sts=4 tw=100 */
