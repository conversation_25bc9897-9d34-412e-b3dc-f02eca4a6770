/**
 * @file check_worker.cpp
 * <AUTHOR>
 * @date 2022/09/07 19:50:18
 * @version 1.0
 * @brief 
 * 
 */

#include "baidu/inf/aries/tapenode/check_worker.h"

#include <filesystem>

#include "base/file_util.h"

#include "baidu/inf/aries-api/common/config.h"
#include "baidu/inf/aries-api/common/types.h"
#include "baidu/inf/aries-api/common/rpc_stub.h"
#include "baidu/inf/aries-api/common/bvar_define.h"
#include "baidu/inf/aries-api/common/tape_center_tracker.h"

#include "baidu/inf/aries/common/sync_point.h"
#include "baidu/inf/aries/tapenode/flags.h"
#include "baidu/inf/aries/tapenode/tape_agent.h"
#include "baidu/inf/aries/tapenode/transform_worker.h"

namespace aries::tapenode {

ARIES_BVAR_ADDER(tape_node, check_task_finished_num);
ARIES_BVAR_COUNTER(tape_node, check_task_failed_num);
ARIES_BVAR_AVERAGER_WITH_WINDOW(tape_node, ltfsee_get_file_state_latency_s, 300);

CheckWorker::CheckWorker() {}

CheckWorker::~CheckWorker() {}

void CheckWorker::start() {
    _is_stopped = false;
    LOG(NOTICE) << "check worker " << std::hex << this << "started";
    _worker_thread = std::thread(std::mem_fn(&CheckWorker::thread_func), this);
}

void CheckWorker::stop() {
    _is_stopped = true;
    if (_worker_thread.joinable()) {
        _worker_thread.join();
    }
}

void CheckWorker::thread_func() {
    while (!_is_stopped) {
        std::this_thread::sleep_for(std::chrono::milliseconds(FLAGS_worker_request_task_timespan_ms));
        auto task = std::make_shared<TaskContext>();
        auto ret = fetch_check_task(task);
        if (ret != AIE_OK) {
            ARIES_DEBUG_LOG(TRACE) << "fetch check task failed ret:" << ret;
            std::this_thread::sleep_for(std::chrono::milliseconds(FLAGS_worker_request_task_timespan_ms));
            continue;
        }
        do_check_work(task);
        report_check_task(task);

    }
}

void CheckWorker::report_check_task(std::shared_ptr<TaskContext> ctx) {
    aries::pb::CheckTaskReportRequest request;
    aries::pb::AckResponse response;
    request.set_token(FLAGS_token);
    request.set_version(__ARIES_VERSION_ID__);
    uint64_t id = common::endpoint2int(common::get_local_addr());
    id |= ((uint64_t)worker_id() << 16);
    request.set_req_addr(id);
    request.set_is_consistant(ctx->is_consistant);
    request.set_volume_id(ctx->volume_id);
    
    if (ctx->is_succ) {
        request.set_is_succ(true);
        request.mutable_location_on_tape()->CopyFrom(ctx->location);
        g_tape_node_check_task_finished_num_adder << 1;
    } else {
        request.set_is_succ(false);
        g_tape_node_check_task_failed_num_counter << 1;
    }

    RpcCallOptions options;
    options.need_retry = false;
    SynchronizedClosure closure;
    TapeCenterStub stub;
    base::EndPoint tape_center_addr = g_tape_center_tracker->get_tape_center();
    int retry_num = 3;
    int ret = AIE_FAIL;
    TEST_SYNC_POINT_CALLBACK("CheckWorker::report_check_task:counter", &g_tape_node_check_task_failed_num_counter);
    while (retry_num > 0) {
        retry_num--;
        response.Clear();
        closure.reset();
        stub.report_check_task(tape_center_addr, &request, &response, &closure, &options);
        ret = response.status().code();
        closure.wait();
        if (ret == AIE_NOT_PRIMARY) {
            tape_center_addr = common::node2endpoint(response.status().leader_hint());
            if (tape_center_addr.ip == base::IP_ANY) {
                break;
            }
            continue;
        } else if (ret == AIE_BUSY) {
            std::this_thread::sleep_for(std::chrono::milliseconds(FLAGS_worker_wait_timespan_ms));
        } else {
            break;
        }
    }
        
    if (ret != AIE_OK) {
        LOG(WARNING) << "report fail , vid:" << ctx->volume_id << " check file: " << ctx->tape_store_file_path;
    }

    LOG(NOTICE) << "report check task to tapecenter vid:" << ctx->volume_id
            << " is_succ:" << ctx->is_succ
            << " ret:" << common::pb2json(response.status())
            << " cost_us:" << ctx->cost_tracker->to_string();
}

void CheckWorker::do_check_work(std::shared_ptr<TaskContext> ctx) {
    pb::VolumeLocationOnTape location;
    auto ret = g_tape_agent->get_file_state(ctx, &location);
    ctx->cost_tracker->add_stage("get_file_state");
    g_tape_node_ltfsee_get_file_state_latency_s_averager << ctx->cost_tracker->recent_cost_time_us() / (1000 * 1000);
    TEST_SYNC_POINT_CALLBACK("CheckWorker::do_check_work:ret", &ret);
    if (ret.code() != AIE_OK) {
        if (ret.code() == AIE_NOT_EXIST) {
            LOG(FATAL) << "file not exist, file_name:" << ctx->tape_store_file_path
                    << " vid:" << ctx->volume_id;
        }
        ctx->is_succ = false;
        LOG(WARNING) << "fail to get file state, status:" << ret << " file_name:" 
                << ctx->tape_store_file_path;
        return;
    }
    TEST_SYNC_POINT_CALLBACK("CheckWorker::do_check_work:location", &location);
    if (location.location_on_tape_list_size() == 0) {
        ctx->is_succ = false;
        LOG(WARNING) << "location_on_tape_list is empty, status:" << ret << " file_name:" 
                << ctx->tape_store_file_path;
        return;
    }
    ctx->is_succ = true;
    std::set<std::string> location_on_tape_set;
    for (int i = 0; i < ctx->location.location_on_tape_list_size(); i++) {
        location_on_tape_set.insert(ctx->location.location_on_tape_list(i).tape_id());
    }
    for (int i = 0; i < location.location_on_tape_list_size(); i++) {
        auto tape_id = location.location_on_tape_list(i).tape_id();
        if (location_on_tape_set.find(tape_id) == location_on_tape_set.end()) {
            ctx->is_consistant = false;
            ctx->location.mutable_location_on_tape_list()->CopyFrom(location.location_on_tape_list());
            break;
        }
    }
    return;
}

int CheckWorker::fetch_check_task(std::shared_ptr<TaskContext> ctx) {
    auto local_addr = common::get_local_addr();
    aries::pb::FetchCheckTaskRequest request;
    aries::pb::FetchCheckTaskResponse response;
    request.set_token(FLAGS_token);
    request.set_version(__ARIES_VERSION_ID__);
    uint64_t id = common::endpoint2int(local_addr);
    id |= ((uint64_t)worker_id() << 16);
    request.set_req_addr(id);

    RpcCallOptions options;
    options.need_retry = false;
    SynchronizedClosure closure;
    TapeCenterStub stub;
    base::EndPoint tape_center_addr = g_tape_center_tracker->get_tape_center();
    stub.fetch_check_task(tape_center_addr, &request, &response, &closure, &options);
    closure.wait();
    TEST_SYNC_POINT_CALLBACK("CheckWorker::fetch_check_task:response", &response);
    if (response.status().code() != AIE_OK) {
        return response.status().code();
    }

    ctx->volume_id = response.volume_id();
    ctx->task_timeout = response.timeout();
    ctx->worker_id = worker_id();
    ctx->start_timestamp = base::gettimeofday_us();
    if (response.location_on_tape().has_type()) {
        if (response.location_on_tape().file_location_on_tape_list_size() > 0) {
            auto file_location = response.location_on_tape().file_location_on_tape_list(0);
            ctx->tape_store_finish_file_path = generate_tape_file_path(ctx->volume_id, 
                    common::ip2str((common::int2endpoint(file_location.worker_addr()).ip)), 0, false);
            std::string slice_file_path = ctx->finish_slice_file_path(0);
            ctx->tape_store_file_path = slice_file_path;
        }
    } else {
        ctx->tape_store_file_path = response.location_on_tape().location_on_gpfs();
    }
    ctx->location.CopyFrom(response.location_on_tape());

    LOG(NOTICE) << "recv check task, vid:" << ctx->volume_id
            << " file_path:" << ctx->tape_store_file_path;
    ctx->cost_tracker->add_stage("fetch_task");
    return AIE_OK;
}

}