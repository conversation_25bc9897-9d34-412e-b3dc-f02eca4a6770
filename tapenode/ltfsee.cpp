/***************************************************************************
 * 
 * Copyright (c) 2021 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/

/**
 * @file ltfsee.cpp
 * <AUTHOR>
 * @date 2021/12/08 16:04:31
 * @version 1.0
 * @brief 
 * 
 */
#include <fstream>
#include <filesystem>
#include <json/json.h>
#include <thread>

#include "baidu/base/common/base/popen.h"

#include "baidu/inf/aries/common/sync_point.h"

#include "baidu/inf/aries/tapenode/ltfsee.h"
#include "baidu/inf/aries/tapenode/flags.h"

namespace aries::tapenode {

int LtfsEE::migrate(const std::vector<std::string>& file_list, std::vector<TapeFileInfo>* tape_file_info_vec) {
    if (file_list.empty()) {
        LOG(NOTICE) << "file_list is empty";
        return AIE_FAIL;
    }
    std::thread::id thread_id = std::this_thread::get_id();
    std::string file_list_path;
    base::string_printf(&file_list_path, MIGRATE_FILE_LIST_FORMAT.c_str(), _temp_dir_path.c_str(), thread_id); 
    TEST_SYNC_POINT_CALLBACK("Ltfsee::migrate:file_list_path", &file_list_path);
    std::filesystem::path file_path(file_list_path);
    if (!std::filesystem::exists(file_path.parent_path())
            && !std::filesystem::create_directories(std::filesystem::absolute(file_path.parent_path()))) {
        LOG(FATAL) << "create directory failed, path:" << file_list_path
            << " errno:(" << errno << ")" << strerror(errno);
        return AIE_FAIL;
    }
    std::ofstream mig_file(file_list_path, std::ios::out);
    if(!mig_file) {
        LOG(FATAL) << "open file failed, file_path:" << file_list_path
            << " errno:(" << errno << ")" << strerror(errno);
        return AIE_FAIL;
    }
    for(auto i : file_list) {
        std::filesystem::path file_path(i);
        mig_file << "  -- " << file_path.string() << std::endl;
    }
    mig_file.close();
    std::string cmd;
    std::string pool_str = "";
    std::stringstream output;
    for(auto i = _pool_map.begin(); i != _pool_map.end(); ) {
        pool_str += i->first;
        pool_str += "@";
        pool_str += i->second;
        if (++i != _pool_map.end()){
            pool_str += ",";
        }
    }
    base::string_printf(&cmd, MIGRATE_CMD_FORMAT.c_str(), FLAGS_python_path.c_str(), 
            FLAGS_eeadm_tool_path.c_str(), file_list_path.c_str(), pool_str.c_str()); 
    int rc = base::read_command_output(output, cmd.c_str());
    LOG(NOTICE) << "execute cmd:" << cmd << " output:" << output.str();
    if(rc < 0) {
        LOG(FATAL) << "fail to execute cmd, cmd:" << cmd << " output:" << output.str();
        return AIE_FAIL;
    }
    return json_to_tape_file_info(output.str(), tape_file_info_vec);
}

int LtfsEE::retrieve(const std::vector<std::string>& file_list,
        const std::string& pool_name, std::vector<TapeFileInfo>* tape_file_info_vec) {
    if (file_list.empty()) {
        LOG(NOTICE) << "file_list is empty";
        return AIE_FAIL;
    }
    if (pool_name.empty()) {
        LOG(NOTICE) << "pool_name is empty";
        return AIE_FAIL;
    }
    std::thread::id thread_id = std::this_thread::get_id();
    std::string file_list_path;
    base::string_printf(&file_list_path, RETRIEVE_FILE_LIST_FORMAT.c_str(), _temp_dir_path.c_str(), thread_id); 
    TEST_SYNC_POINT_CALLBACK("Ltfsee::retrieve:file_list_path", &file_list_path);
    std::filesystem::path file_path(file_list_path);
    if (!std::filesystem::exists(file_path.parent_path())
            && !std::filesystem::create_directories(std::filesystem::absolute(file_path.parent_path()))) {
        LOG(FATAL) << "create directory failed, path:" << file_list_path
            << " errno:(" << errno << ")" << strerror(errno);
        return AIE_FAIL;
    }
    
    std::ofstream retrieve_file(file_list_path, std::ios::out);
    if(!retrieve_file) {
        LOG(FATAL) << "open file failed" << _temp_dir_path
            << " errno:(" << errno << ")" << strerror(errno);
        return AIE_FAIL;
    }
    for(auto i : file_list) {
        std::filesystem::path file_path(i);
        retrieve_file << "  -- " << file_path.string() << std::endl;
    }
    retrieve_file.close();
    std::string cmd;
    std::stringstream output;
    if (_pool_map.find(pool_name) == _pool_map.end()) {
        LOG(FATAL) << "pool name not exist in _pool_map, pool name:" << pool_name;
        return AIE_NOT_EXIST;
    }
    auto library = _pool_map[pool_name];
    base::string_printf(&cmd, RETRIEVE_CMD_FORMAT.c_str(), FLAGS_python_path.c_str(), 
            FLAGS_eeadm_tool_path.c_str(), file_list_path.c_str(), library.c_str()); 
    int ret = base::read_command_output(output, cmd.c_str());
    LOG(NOTICE) << "execute cmd:" << cmd << " output:" << output.str();
    if(ret < 0) {
        LOG(FATAL) << "fail to execute cmd, cmd:" << cmd << " output:" << output.str();
        return AIE_FAIL;
    }
    ret = json_to_tape_file_info(output.str(), tape_file_info_vec);
    for (auto tape_file_info : *tape_file_info_vec) {
        if (tape_file_info.code == AIE_INVALID_LIB) {
            base::string_printf(&cmd, RETRIEVE_CMD_FORMAT.c_str(), FLAGS_python_path.c_str(), 
                    FLAGS_eeadm_tool_path.c_str(), file_list_path.c_str(), ""); 
            ret = base::read_command_output(output, cmd.c_str());
            if(ret < 0) {
                LOG(FATAL) << "fail to execute cmd, cmd:" << cmd << "output:" << output.str();
                return AIE_FAIL;
            }
            ret = json_to_tape_file_info(output.str(), tape_file_info_vec);
            break;
        }
    }
    return ret;
}

Status LtfsEE::get_state(const std::string& file_path, TapeFileInfo* tape_file_info) {
    std::stringstream output;
    std::string cmd;
    base::string_printf(&cmd, STATE_CMD_FORMAT.c_str(), FLAGS_python_path.c_str(), 
            FLAGS_eeadm_tool_path.c_str(), file_path.c_str()); 
    int rc = base::read_command_output(output, cmd.c_str());
    LOG(NOTICE) << "execute cmd:" << cmd << " output:" << output.str();
    if(rc < 0) {
        LOG(FATAL) << "get state failed, file_path:" << file_path;
        return Status(AIE_FAIL, "fail to execute cmd");
    }
    std::string json_str = output.str();
    JSONCPP_STRING errs;
    Json::Value root;
    Json::CharReaderBuilder readerBuilder;
    std::unique_ptr<Json::CharReader> const jsonReader(readerBuilder.newCharReader());
    bool res = jsonReader->parse(json_str.c_str(), json_str.c_str() + json_str.length(), &root, &errs);
    if (!res || !errs.empty()) {
        LOG(FATAL) << "parse json err:" << errs << std::endl;
        return Status(AIE_FAIL, "parse json err");
    }
    tape_file_info->path = file_path;
    tape_file_info->state = root["state"].asInt();
    Json::Value location = root["location_on_tape"];
    tape_file_info->code = AIE_OK;
    for(uint j = 0; j < location.size(); j++) {
        aries::pb::LocationOntape* loc = tape_file_info->location_on_tape.add_location_on_tape_list();
        loc->set_tape_id(location[j]["tape_id"].asString());
        loc->set_physical_pool_name(location[j]["pool_name"].asString());
    }
    return Status();
}
//  json format
// {
// 	"tape_files": [
//         {
//             "path": "/home/<USER>/aries/a",
//             "code": "AE103",
//             "state": 1,    //1:migrated  2:premigrate   3:resident  
//             "task_id": 234,
//             "location_on_tape" : [{"tape_id":"I01EE1M8","pool_name":"pool01"},{"tape_id":"I02EE1M8","pool_name":"pool02"}]
//         }, 
//         {
//             "path": "/home/<USER>/aries/b",
//             "code": "AE105",
//             "state": 2,
//             "task_id": 234,
//             "location_on_tape" : [{"tape_id":"I01EE1M8","pool_name":"pool01"},{"tape_id":"I02EE1M8","pool_name":"pool02"}]
// 	    }
//     ]
// }
int LtfsEE::json_to_tape_file_info(const std::string& json_str, std::vector<TapeFileInfo>* tape_file_info_vec) const {
    std::string errs;
    Json::Value root;
    Json::CharReaderBuilder readerBuilder;
    std::unique_ptr<Json::CharReader> const jsonReader(readerBuilder.newCharReader());
    bool res = jsonReader->parse(json_str.c_str(), json_str.c_str() + json_str.length(), &root, &errs);
    if (!res || !errs.empty()) {
        LOG(FATAL) << "parse json fail, json_str:" << json_str << " err:" << errs ;
        return AIE_FAIL;
    }
    for(uint i = 0; i < root["tape_files"].size(); i++) {
        TapeFileInfo file = TapeFileInfo();
        std::string code = root["tape_files"][i]["code"].asString();
        file.path = root["tape_files"][i]["path"].asString();
        if (root["tape_files"][i]["result"].asString() == "success") {
            file.code = AIE_OK;
        } else if(code == "AGAIN") {
            file.code = AIE_AGAIN;
        } else if(code == "INVALID_LIB") {
            file.code = AIE_INVALID_LIB;
        } else if(code == "NOT_EXIST") {
            file.code = AIE_NOT_EXIST;
            tape_file_info_vec->push_back(file);
            continue;
        } else {
            file.code = AIE_FAIL;
        }
        file.state = root["tape_files"][i]["state"].asInt();
        file.task_id = root["tape_files"][i]["task_id"].asInt();
        if (static_cast<TapeFileState>(file.state) == MIGRATE) {
            Json::Value location = root["tape_files"][i]["location_on_tape"];
            for(uint j = 0; j < location.size(); j++) {
                aries::pb::LocationOntape* loc = file.location_on_tape.add_location_on_tape_list();
                loc->set_tape_id(location[j]["tape_id"].asString());
                loc->set_physical_pool_name(location[j]["pool_name"].asString());
            }
        } 
        if (static_cast<TapeFileState>(file.state) == RESISDENT) {
            LOG(WARNING) << "file state is resisdent after migrate or recall, file path:"
                    << file.path << " file failure code:" << code 
                    << " file ltfsee task_id::" << file.task_id; 
        }
        tape_file_info_vec->push_back(file);
    }
    
    return AIE_OK;
}
//  json format
// {
// 	"pools": [
//         {
//             "poolname":"pool01"
//             "library":"MLB01_A12_LLIB01"
//         }, 
//         {
//             "poolname":"pool02"
//             "library":"MLB01_A12_LLIB01"
// 	    }
//     ]
// }
int LtfsEE::init() {
    std::stringstream output;
    std::string cmd = "";
    base::string_printf(&cmd, LIST_POOL_CMD_FORMAT.c_str(), FLAGS_python_path.c_str(),
            FLAGS_eeadm_tool_path.c_str());
    int rc = base::read_command_output(output, cmd.c_str());
    if(rc < 0) {
        LOG(FATAL) << "fail to execute cmd, cmd:" << cmd << "output:" << output.str();
        return AIE_FAIL;
    }
    std::string json_str = output.str();
    LOG(NOTICE) << "ltfsee init output:" << output.str();  
    JSONCPP_STRING errs;
    Json::Value root; 
    Json::CharReaderBuilder readerBuilder;
    std::unique_ptr<Json::CharReader> const jsonReader(readerBuilder.newCharReader());
    bool res = jsonReader->parse(json_str.c_str(), json_str.c_str() + json_str.length(), &root, &errs);
    if (!res || !errs.empty()) {
        LOG(FATAL) << "fail to init ltfsee pool, parse json err:" << errs << std::endl;
        return AIE_FAIL;
    }
    for(uint i = 0; i < root["pools"].size(); i++) {
        auto pool_name = root["pools"][i]["poolname"].asString();
        auto library = root["pools"][i]["library"].asString();
        _pool_map.insert(std::make_pair(pool_name, library));
    }
    if (_pool_map.empty()) {
        LOG(FATAL) << "fail to init ltfsee pool";
        return AIE_FAIL;
    }
    LOG(NOTICE) << "ltfsee init finish";
    return AIE_OK;
}

bool LtfsEE::check_tape_id(std::string physical_pool_name) {
    auto iter = _pool_map.find(physical_pool_name);
    return iter != _pool_map.end();
}
//  json format
// {
//     "tapes" : [
//          {
//               "src_tape_id" : "xxx"
//               "dst_tape_id" : "xxx"
//               "is_succ" : "true"
//               "code" : "-"
//          }
//      ]
// }
Status LtfsEE::reclaim(std::string src_tape_id, std::string physical_pool_name, std::string* dst_tape_id) {
    std::stringstream output;
    std::string cmd = "";
    if (_pool_map.find(physical_pool_name) == _pool_map.end()) {
        LOG(WARNING) << "physical_pool_name not exist, physical_pool_name:" << physical_pool_name;
        return Status(AIE_FAIL, "physical_pool_name not exist");
    }
    std::string library_name = _pool_map[physical_pool_name];
    base::string_printf(&cmd, RECLAIM_TAPE_CMD_FORMAT.c_str(), FLAGS_python_path.c_str(),
            FLAGS_eeadm_tool_path.c_str(), src_tape_id.c_str(), physical_pool_name.c_str(), library_name.c_str());
    int rc = base::read_command_output(output, cmd.c_str());
    LOG(NOTICE) << "execute cmd:" << cmd << " output:" << output.str();
    if(rc < 0) {
        LOG(FATAL) << "fail to execute cmd, cmd:" << cmd << "output:" << output.str();
        return Status(AIE_FAIL, "fail to execute cmd");
    }

    std::string json_str = output.str();
    JSONCPP_STRING errs;
    Json::Value root;
    Json::CharReaderBuilder readerBuilder;
    std::unique_ptr<Json::CharReader> const jsonReader(readerBuilder.newCharReader());
    bool res = jsonReader->parse(json_str.c_str(), json_str.c_str() + json_str.length(), &root, &errs);
    if (!res || !errs.empty()) {
        LOG(FATAL) << "parse json err:" << errs << std::endl;
        return Status(AIE_FAIL, "parse json err");
    }
    LOG(WARNING) << json_str.c_str();
    for(uint i = 0; i < root["tapes"].size(); i++) {
        if (src_tape_id != root["tapes"][i]["src_tape_id"].asString()) {
            LOG(FATAL) << "src_tape_id inconsistency, src_tape_id:" << src_tape_id
                    << " json_tape_id:" << root["tapes"][i]["src_tape_id"].asString();
            return Status(AIE_FAIL, "tape_id inconsistency");
        }
        if (root["tapes"][i]["is_succ"].asString() == "true") {
            *dst_tape_id = root["tapes"][i]["dst_tape_id"].asString();
            return Status();
        } else {
            LOG(WARNING) << "fail to reclaim tape, code:" << root["tapes"][i]["code"].asString();
            return Status(AIE_FAIL, "fail to reclaim");
        }  
    }
    return Status();
}

//  json format
// {
//     "is_succ" : "true"
//     "code" : "-"
//     "tapes" : [
//          {
//               "tape_id" : "xxx"
//               "relaimable" : 90
//          },
//          {
//               "tape_id" : "xxx"
//               "relaimable" : 80
//          }
//      ]
// }

Status LtfsEE::reconcile(std::shared_ptr<ReclaimTaskContext> ctx) {
    std::stringstream output;
    std::string cmd = "";
    base::string_printf(&cmd, RECONCILE_TAPE_CMD_FORMAT.c_str(), FLAGS_python_path.c_str(),
            FLAGS_eeadm_tool_path.c_str());
    int rc = base::read_command_output(output, cmd.c_str());
    LOG(NOTICE) << "execute cmd:" << cmd << " output:" << output.str();
    if(rc < 0) {
        LOG(FATAL) << "fail to execute cmd, cmd:" << cmd << "output:" << output.str();
        return Status(AIE_FAIL, "fail to execute cmd");
    }

    std::string json_str = output.str();
    JSONCPP_STRING errs;
    Json::Value root;
    Json::CharReaderBuilder readerBuilder;
    std::unique_ptr<Json::CharReader> const jsonReader(readerBuilder.newCharReader());
    bool res = jsonReader->parse(json_str.c_str(), json_str.c_str() + json_str.length(), &root, &errs);
    if (!res || !errs.empty()) {
        LOG(FATAL) << "parse json err:" << errs << std::endl;
        return Status(AIE_FAIL, "parse json err");
    }
    if (root["is_succ"].asString() == "false") {
        LOG(WARNING) << "fail to reconcile tape, code:" << root["tapes"]["code"].asString();
        return Status(AIE_FAIL, "fail to reconcile tape");
    }

    if (root["tapes"].size() == 0) {
        LOG(WARNING) << "tape state list empty";
        return Status(AIE_FAIL, "tape state list empty");
    }

    for(uint i = 0; i < root["tapes"].size(); i++) {
        pb::TapeReclaimTaskReportRequest_TapeState tape_state;
        tape_state.set_tape_id(root["tapes"][i]["tape_id"].asString());
        tape_state.set_reclaimable_percentage(root["tapes"][i]["reclaimable"].asUInt64());
        tape_state.set_physical_pool_name(root["tapes"][i]["poolname"].asString());
        ctx->tape_state_list.push_back(tape_state);
    }
    return Status();
}

}
