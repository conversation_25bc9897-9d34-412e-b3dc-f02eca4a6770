#!/bin/bash

CURRENT_DIR=`pwd`

IPV4=`ip addr | grep -w "inet" | grep "scope global eth0" | awk '{print $2}' | awk -F / '{print $1}'`
IPV6=[`ip addr | grep -w "inet6" | grep "scope global" | awk '{print $2}' | awk -F / '{print $1}'`]
PORT=3000
SERVER_IP=($IPV4:$PORT $IPV6:$PORT)
PARSE_HOSTNAME_TO_IPV4=(true false)

CLIENT_LOG='http_client.INFO'
SERVER_LOG='http_server.INFO'

killall http_server

echo -e "*******http test*******"

echo "start http server"
$(./http_server --thread_num=4 --listen_addr=[0:0:0:0:0:0:0:0]:$PORT) & p1=$!
if [ $? != 0 ]; then
    echo "start http server failed"
    kill -9 $p1
    exit -1
fi
sleep 1

for ip in "${SERVER_IP[@]}"
do
    for parse in "${PARSE_HOSTNAME_TO_IPV4[@]}"
    do
        url="http://""$ip""/HttpService/Echo/Foo/Bar?haha=111&xixi=222"
        $(./http_client --url=$url --rpc_num=50 --parse_hostname_to_ipv4=$parse > /dev/null) & p2=$!
        if [ $? != 0 ]; then
            echo "start http client failed"
            kill -9 $p1
            exit -1
        fi
        wait $p2

        ## check whether rpc success success. "RPC call failed" is used to check whether rpc success, "==ERROR:" is used to check asan result.
        output=`grep -e "RPC call failed, error code" -e "==ERROR:" $CURRENT_DIR/$CLIENT_LOG`
        if [ $? == 0 ]
        then
            echo -e "*******http test , server ip " $ip " \033[31mfailed\033[0m*******"
            echo -e "see error in " $CURRENT_DIR/$CLIENT_LOG
            kill -9 $p1
            exit -1
        else
            ## grep "Avg latency is" is to ensure rpc are processed, in case of client/server starting failed.
            output=`grep "Avg latency is" $CURRENT_DIR/$CLIENT_LOG`
            if [ $? == 0 ]
            then
                echo -e "******http test , server ip " $ip " \033[32msuccess\033[0m*******"
            else
                echo -e "*******http test , server ip " $ip " \033[31mfailed\033[0m*******"
                echo -e "see error in " $CURRENT_DIR/$CLIENT_LOG
                kill -9 $p1
                exit -1
            fi
        fi
    done
done

killall http_server

find /opt/tiger/cores -mmin -10 | grep -E "http_client|http_server"
if [ $? == 0 ]
then
    echo "******find core in /opt/tiger/cores******"
    exit -1
fi

exit 0
