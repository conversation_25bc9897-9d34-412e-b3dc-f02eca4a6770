// Copyright (c) 2021, ByteDance Inc. All rights reserved.

#include <thread>
#include <vector>

#include "brpc_echo.pb.h"

#include "byterpc/builder.h"
#include "byterpc/byterpc_flags.h"
#include "byterpc/callback.h"
#include "byterpc/exec_ctx.h"
#include "byterpc/rpc.h"
#include "byterpc/util/logging.h"
#include "byterpc/util/numautils.h"
#include "byterpc/util/timestamp.h"

#include "absl/memory/memory.h"
#include "byte/stats/histogram.h"
#include "byte/string/algorithm.h"
#include "byte/string/number.h"

DEFINE_int32(thread_num, 4, "Number of threads to send requests");
DEFINE_string(bind_cpu_id_list, "", "bind cpu id, if have, must equal to thread_num");
DEFINE_int32(attachment_size, 4096, "use attachment");
DEFINE_int32(timeout_ms, 2000, "rpc timeout in ms");
DEFINE_int32(connect_timeout_ms, 1000, "connect timeout in ms");
DEFINE_uint32(protocol, 1, "1: PROTOCOL_BAIDU_STD; 2: PROTOCOL_BYTE_STD");
DEFINE_string(remote_addr, "0.0.0.0:18888", "Remote server address");
DEFINE_bool(use_ktcp, true, "enable ktcp or not");
DEFINE_bool(use_byte_express, false, "enable rdma or not");
DEFINE_bool(use_tarzan, false, "enable tarzan or not");
DEFINE_uint64(rpc_num, 1000000, "Number of requests per thread");
DEFINE_bool(use_fallback, false, "use fallback channel or not");

namespace byterpc {
DECLARE_int32(byterpc_tarzan_worker_num);
}  // namespace byterpc

class RpcCall {
public:
    RpcCall(std::vector<char>&& ch, byterpc::loop_type_t loop_type)
        : _finished(false),
          _loop_type(loop_type),
          _builder(),
          _req(new example::PerfRequest),
          _resp(new example::PerfResponse),
          _controller(),
          _start_timestamp(0),
          _count(0),
          _ch(std::move(ch)) {}

    ~RpcCall() {
        delete _attachment;
    }

    bool Finished() const {
        return _finished;
    }

    void Init() {
        byterpc::Builder::ChannelOptions options;
        options._rpc_timeout_ms = FLAGS_timeout_ms;
        options._connect_timeout_ms = FLAGS_connect_timeout_ms;
        if (FLAGS_use_ktcp) {
            options._trans_type = byterpc::TYPE_KERNEL_TCP;
            auto channel = _builder.BuildChannel(FLAGS_remote_addr, options);
            BYTERPC_CHECK(channel);
            _channels.push_back(std::move(channel));
            _transport_types.push_back(byterpc::TYPE_KERNEL_TCP);
        }
        if (FLAGS_use_tarzan) {
            options._trans_type = byterpc::TYPE_USERSPACE_TCP;
            options._enable_fallback = FLAGS_use_fallback;
            auto channel = _builder.BuildChannel(FLAGS_remote_addr, options);
            BYTERPC_CHECK(channel);
            _channels.push_back(std::move(channel));
            _transport_types.push_back(byterpc::TYPE_USERSPACE_TCP);
        }
        if (FLAGS_use_byte_express) {
            options._trans_type = byterpc::TYPE_RDMA;
            options._enable_fallback = FLAGS_use_fallback;
            auto channel = _builder.BuildChannel(FLAGS_remote_addr, options);
            BYTERPC_CHECK(channel);
            _channels.push_back(std::move(channel));
            _transport_types.push_back(byterpc::TYPE_RDMA);
        }
        BYTERPC_CHECK(_channels.size() > 0);

        if (FLAGS_attachment_size > 0) {
            _attachment = new byterpc::IOBuf();
            _attachment->append(_ch.data(), FLAGS_attachment_size);
        }

        _req->set_echo_attachment(false);

        _hist.resize(_channels.size());
    }

    void IssueRPC() {
        _start_timestamp = byterpc::util::TimeStamp::Now();
        _controller = _builder.CreateSessionController(
            static_cast<byterpc::ProtocolType>(FLAGS_protocol), FLAGS_timeout_ms * 1000);

        if (FLAGS_attachment_size > 0) {
            _controller->InstallOutgoingAttachment(*_attachment);
        }
        _controller->SetLogId(_count);

        google::protobuf::Closure* done =
            ::byterpc::NewCallback<RpcCall, RpcCall*>(this, &RpcCall::Reset);

        example::PerfService_Stub stub(_channels[_count % _channels.size()].get());
        stub.Perf(_controller, _req.get(), _resp.get(), done);
    }

    void Reset() {
        _resp->Clear();
        if (_controller->Failed()) {
            BYTERPC_LOG(WARNING) << "RPC call failed, error code: " << _controller->ErrorCode()
                                 << ", error text: " << _controller->ErrorText()
                                 << " _count = " << _count;
        }
        uint64_t ts = byterpc::util::TimeStamp::Now();
        _hist[_count % _channels.size()].Add(
            byterpc::util::TimeStamp::DurationToUs(ts - _start_timestamp));

        if (++_count < FLAGS_rpc_num) {
            IssueRPC();
        } else {
            for (size_t i = 0; i < _hist.size(); ++i) {
                BYTERPC_LOG(INFO) << "Transport: "
                                  << byterpc::TransportTypeToString(_transport_types[i]);
                BYTERPC_LOG(INFO) << "    Avg latency is: " << _hist[i].Average();
                BYTERPC_LOG(INFO) << "    99th latency is: " << _hist[i].Percentile(99.0);
                BYTERPC_LOG(INFO) << "    Median latency is: " << _hist[i].Median();
            }
            BYTERPC_LOG(INFO) << "Client exit";
            // Signal to event loop that it can quit now;
            _finished = true;
        }
    }

private:
    bool _finished;
    byterpc::loop_type_t _loop_type;
    byterpc::Builder _builder;
    std::unique_ptr<example::PerfRequest> _req;
    std::unique_ptr<example::PerfResponse> _resp;
    std::vector<std::shared_ptr<byterpc::Builder::Channel>> _channels;
    std::vector<byterpc::TransportType> _transport_types;
    byterpc::Controller* _controller;
    std::vector<byte::HistogramImpl> _hist;
    uint64_t _start_timestamp;
    uint32_t _count;
    std::vector<char> _ch;
    byterpc::IOBuf* _attachment;
};

static void StartClient(std::unique_ptr<RpcCall> call, int32_t cpu_id) {
    if (cpu_id >= 0) {
        if (!byterpc::util::bind_this_to_core(static_cast<size_t>(cpu_id))) {
            BYTERPC_LOG(ERROR) << "Fail to bind pthread to specified cpu";
            return;
        }
    }
    {
        byterpc::ExecCtx ctx(byterpc::LOOP_IF_POSSIBLE);
        call->Init();
        call->IssueRPC();
    }
    while (!call->Finished()) {
        byterpc::ExecCtx::LoopOnce();
    }
}

int main(int argc, char* argv[]) {
    GFLAGS_NAMESPACE::ParseCommandLineFlags(&argc, &argv, true);
    BYTERPC_LOG(INFO) << "thread_num is: " << FLAGS_thread_num
                      << "; attachment_size is: " << FLAGS_attachment_size;

    std::vector<std::string> cpu_ids;
    byte::SplitString(FLAGS_bind_cpu_id_list, ",", &cpu_ids);
    if (cpu_ids.size() && static_cast<int32_t>(cpu_ids.size()) != FLAGS_thread_num) {
        BYTERPC_LOG(ERROR) << "Cpu id number is not equal to thread num";
        return -1;
    }

    byterpc::loop_type_t loop_type = byterpc::LOOP_IF_POSSIBLE;
    byterpc::FLAGS_byterpc_tarzan_worker_num = FLAGS_thread_num;
    byterpc::InitOptions init_opt(
        FLAGS_use_tarzan ? true : false, FLAGS_use_byte_express ? true : false, "byterpc_test");
    if (byterpc::ExecCtx::Init(init_opt) < 0) {
        BYTERPC_LOG(ERROR) << "Fail to init tarzan or rdma";
        return -1;
    }

    std::vector<std::unique_ptr<std::thread>> pids;
    std::vector<std::unique_ptr<RpcCall>> calls;
    for (int i = 0; i < FLAGS_thread_num; i++) {
        std::vector<char> ch(FLAGS_attachment_size, 'a');
        std::unique_ptr<RpcCall> call(new RpcCall(std::move(ch), loop_type));
        calls.push_back(std::move(call));
    }
    for (int i = 0; i < FLAGS_thread_num; i++) {
        int32_t cpu_id = -1;
        if (cpu_ids.size()) {
            byte::StringToNumber(cpu_ids[i], &cpu_id);
        }
        std::unique_ptr<std::thread> th(new std::thread(StartClient, std::move(calls[i]), cpu_id));
        pids.push_back(std::move(th));
    }

    for (size_t i = 0; i < pids.size(); i++) {
        pids[i]->join();
    }
    return 0;
}
