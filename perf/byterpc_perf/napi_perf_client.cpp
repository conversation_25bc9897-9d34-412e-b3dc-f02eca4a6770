// Copyright (c) 2021, ByteDance Inc. All rights reserved.

#include <cstring>
#include <thread>
#include <vector>

#include "heavy.pb.h"

#include "byterpc/builder.h"
#include "byterpc/byterpc_flags.h"
#include "byterpc/callback.h"
#include "byterpc/exec_ctx.h"
#include "byterpc/io_buf.h"
#include "byterpc/log_setting.h"
#include "byterpc/server.h"
#include "byterpc/thread/ev_thread_helper.h"
#include "byterpc/thread/polling_thread_helper.h"
#include "byterpc/util/logging.h"
#include "byterpc/util/numautils.h"
#include "byterpc/util/timestamp.h"

#include "absl/memory/memory.h"
#include "byte/include/macros.h"
#include "byte/stats/histogram.h"
#include "byte/string/algorithm.h"
#include "byte/string/number.h"
#include "byte/thread/this_thread.h"

DEFINE_int32(thread_num, 4, "Number of threads to send requests");
DEFINE_string(bind_cpu_id_list, "", "bind cpu id, if have, must equal to thread_num");
DEFINE_int32(attachment_size, 4096, "use attachment");
DEFINE_int32(num_size, 0, "Number of int in request");
DEFINE_int32(str_size, 0, "Number of string in request");
DEFINE_string(str, "abc", "Content of string in request");
DEFINE_int32(timeout_ms, 2000, "rpc timeout in ms");
DEFINE_int32(connect_timeout_ms, 1000, "connect timeout in ms");
DEFINE_uint32(protocol, 2, "1: PROTOCOL_BAIDU_STD; 2: PROTOCOL_BYTE_STD");
DEFINE_string(remote_addr, "0.0.0.0:18888", "Remote server address");
DEFINE_uint32(depth, 1, "Number of inflight rpc");
DEFINE_uint64(rpc_num, 1000000, "Number of requests per thread");
DEFINE_bool(use_polling_mode, true, "rdma or tcp use polling mode or event triger mode");
DEFINE_bool(use_napi_mode, false, "use napi mode for rdma transport");
DEFINE_bool(enable_histogram, true, "enable histogram or not");
DEFINE_int32(max_round, 100, "max round for napi test");

template <typename T> inline T* get_pointer(T* p) {
    return p;
}

template <typename Class, typename Pointer, typename Arg1, typename Arg2>
class MyClosure : public ::google::protobuf::Closure {
public:
    typedef void (Class::*MethodType)(Arg1 arg1, Arg2 arg2);

    MyClosure(const Pointer& object, MethodType method) : object_(object), method_(method) {}
    ~MyClosure() {}

    void Run() {
        (get_pointer(object_)->*method_)(arg1_, arg2_);
    }

    Arg1 arg1_;
    Arg2 arg2_;

private:
    Pointer object_;
    MethodType method_;
};

static std::atomic<int> g_running_worker_threads;
static std::atomic<bool> g_last_round;

static void PrepareAttachment(byterpc::IOBuf* out_iob) {
    BYTERPC_CHECK(out_iob && out_iob->size() == 0);
    const size_t att_sz = FLAGS_attachment_size;

    // allocate data block.
    out_iob->append(byterpc::IOBlockRef::CreateV2(att_sz));
    BYTERPC_CHECK(out_iob->size() >= att_sz);

    // memset attachment data.
    size_t ref_idx = 0;
    size_t remain_sz = att_sz;
    while (remain_sz > 0) {
        BYTERPC_CHECK(ref_idx < out_iob->block_num());
        auto blk_ref = out_iob->block_ref_at(ref_idx);
        size_t sz = std::min(remain_sz, blk_ref.size());
        std::memset(blk_ref.data(), 'a', sz);
        remain_sz -= sz;
        ref_idx++;
    }
}

class RpcCall {
public:
    using callback_t = MyClosure<RpcCall, RpcCall*, uint32_t, uint64_t>;
    RpcCall(byterpc::ThreadBase* th, const byterpc::util::EndPoint& endpoint)
        : _builder(),
          _ep(endpoint),
          _req(new byterpc::HeavyRequest),
          _th(th),
          _stub(),
          _channel(),
          _controller(),
          _count(0),
          _inflight(0) {
        _rpc_done = (callback_t*)new char[sizeof(callback_t) * FLAGS_depth];
        for (uint32_t i = 0; i < FLAGS_depth; i++) {
            new (&_rpc_done[i]) callback_t(this, &RpcCall::Reset);
        }

        _resp = new byterpc::HeavyResponse[FLAGS_depth];
        _controller = new byterpc::Controller*[FLAGS_depth];
    }

    ~RpcCall() {
        delete _attachment;
        delete[] _resp;
        delete[] _controller;
        delete[] reinterpret_cast<char*>(_rpc_done);
    }

    void Init() {
        byterpc::Builder::ChannelOptions options;
        options._rpc_timeout_ms = FLAGS_timeout_ms;
        options._connect_timeout_ms = FLAGS_connect_timeout_ms;
        options._trans_type = byterpc::TYPE_RDMA;
        _channel = _builder.BuildChannel(_ep, options);
        BYTERPC_CHECK(_channel);

        for (int i = 0; i < FLAGS_num_size; i++) {
            _req->add_allnums(i);
        }
        for (int i = 0; i < FLAGS_str_size; i++) {
            _req->add_allstrs(FLAGS_str);
        }

        if (FLAGS_attachment_size > 0) {
            _attachment = new byterpc::IOBuf();
            PrepareAttachment(_attachment);
        }

        _stub.reset(new byterpc::heavy_Stub(_channel.get()));

        _timeout_us = FLAGS_timeout_ms * 1000;
        _protocol_type = static_cast<byterpc::ProtocolType>(FLAGS_protocol);
    }

    void Run() {
        // _start_timestamp will be set after first rpc completed.
        for (uint32_t i = 0; i < FLAGS_depth; ++i) {
            IssueRPC(i);
        }
    }

    void IssueRPC(uint32_t index) {
        uint64_t start_timestamp = byterpc::util::TimeStamp::Now();
        _controller[index] = _builder.CreateSessionController(_protocol_type, _timeout_us);

        if (FLAGS_attachment_size > 0) {
            _controller[index]->InstallOutgoingAttachment(*_attachment);
        }
        _controller[index]->SetLogId(_count);

        _rpc_done[index].arg1_ = index;
        _rpc_done[index].arg2_ = start_timestamp;

        if (UNLIKELY(_count == FLAGS_depth)) {
            _start_timestamp = start_timestamp;
        }
        _stub->default_method(_controller[index], _req.get(), &_resp[index], &_rpc_done[index]);
        ++_inflight;
        ++_count;
    }

    void Reset(uint32_t index, uint64_t start_timestamp) {
        --_inflight;
        uint64_t rpc_id = _controller[index]->LogId();
        if (_controller[index]->Failed()) {
            BYTERPC_LOG(WARNING) << "RPC call failed, error code: "
                                 << _controller[index]->ErrorCode()
                                 << ", error text: " << _controller[index]->ErrorText()
                                 << " rpc_id = " << rpc_id;
        }
        if (FLAGS_enable_histogram && LIKELY(rpc_id >= FLAGS_depth)) {
            _hist.Add(byterpc::util::TimeStamp::DurationToUs(byterpc::util::TimeStamp::Now() -
                                                             start_timestamp));
        }

        if (_count < FLAGS_rpc_num) {
            IssueRPC(index);
        } else if (0 == _inflight) {
            EchoOutput();
            if (g_last_round) {
                _th->Stop();
                delete this;
            } else {
                // Reset _count for next round
                _count = 0;
            }
            --g_running_worker_threads;
        }
    }

    void EchoOutput() {
        const uint64_t consumed_us = byterpc::util::TimeStamp::DurationToUs(
            byterpc::util::TimeStamp::Now() - _start_timestamp);
        BYTERPC_LOG(INFO) << "Qps is: " << (FLAGS_rpc_num - FLAGS_depth) * 1000000 / consumed_us;
        BYTERPC_LOG(INFO) << "Avg latency is: " << _hist.Average();
        BYTERPC_LOG(INFO) << "99th latency is: " << _hist.Percentile(99.0);
        BYTERPC_LOG(INFO) << "999th latency is: " << _hist.Percentile(99.9);
        BYTERPC_LOG(INFO) << "9999th latency is: " << _hist.Percentile(99.99);
        BYTERPC_LOG(INFO) << "max latency is: " << _hist.Percentile(100.0);
        BYTERPC_LOG(INFO) << "Median latency is: " << _hist.Median();
        BYTERPC_LOG(INFO) << "Client finish one round flow";
    }

private:
    byterpc::Builder _builder;
    byterpc::util::EndPoint _ep;
    std::unique_ptr<byterpc::HeavyRequest> _req;
    byterpc::HeavyResponse* _resp;
    byterpc::ThreadBase* _th;
    std::unique_ptr<byterpc::heavy_Stub> _stub;
    std::shared_ptr<byterpc::Builder::Channel> _channel;
    byterpc::Controller** _controller;
    callback_t* _rpc_done;
    byterpc::ProtocolType _protocol_type;
    int64_t _timeout_us;
    byte::HistogramImpl _hist;
    uint64_t _start_timestamp;
    uint32_t _count;
    uint32_t _inflight;
    byterpc::IOBuf* _attachment;
};

static void StartClient(RpcCall* call) {
    call->Init();
    call->Run();
}

static void StartFlow(RpcCall* call) {
    call->Run();
}

int main(int argc, char* argv[]) {
    g_running_worker_threads = 0;
    g_last_round = false;

    // Because the first rpc contain the cost of connection building,
    // we need filter it out.
    // if we only fire one rpc, we may get divided by zero error.
    BYTERPC_CHECK(FLAGS_rpc_num >= 2);

    GFLAGS_NAMESPACE::ParseCommandLineFlags(&argc, &argv, true);
    BYTERPC_LOG(INFO) << "thread_num is: " << FLAGS_thread_num << "; iodepth is: " << FLAGS_depth
                      << "; attachment_size is: " << FLAGS_attachment_size
                      << "; num_size is: " << FLAGS_num_size << "; str_size is: " << FLAGS_str_size
                      << "; str is: " << FLAGS_str;

    std::vector<std::string> cpu_ids;
    byte::SplitString(FLAGS_bind_cpu_id_list, ",", &cpu_ids);
    if (cpu_ids.size() && static_cast<int32_t>(cpu_ids.size()) != FLAGS_thread_num) {
        BYTERPC_LOG(ERROR) << "Cpu id number is not equal to thread num";
        return -1;
    }

    byterpc::util::EndPoint endpoint;
    if (byterpc::util::str2endpoint(FLAGS_remote_addr.c_str(), &endpoint) != 0) {
        BYTERPC_LOG(ERROR) << "Invalid remote_addr=" << FLAGS_remote_addr;
        return -1;
    }

    // Set BE span_size to 2MB, enable large payload allocate
    GFLAGS_NAMESPACE::SetCommandLineOption("byterpc_byte_express_span_size", "2097152");

    // Global Init
    byterpc::InitOptions init_opt(false, true, "byterpc_test");
    if (byterpc::ExecCtx::Init(init_opt) < 0) {
        BYTERPC_LOG(ERROR) << "Fail to init rdma";
        return -1;
    }

    // Start Threads
    std::vector<std::unique_ptr<byterpc::ThreadBase>> ths;
    byterpc::loop_type_t loop_type =
        FLAGS_use_polling_mode ? byterpc::LOOP_IF_POSSIBLE : byterpc::LOOP_UNTIL_QUIT;
    for (int i = 0; i < FLAGS_thread_num; i++) {
        std::unique_ptr<byterpc::ThreadBase> th;
        byterpc::ThreadOptions th_options;
        if (loop_type == byterpc::LOOP_IF_POSSIBLE) {
            th.reset(new byterpc::PollingThreadHelper());
        } else {
            th.reset(new byterpc::EvThreadHelper());
            th_options._transport = byterpc::TYPE_RDMA;
            th_options._enable_napi = FLAGS_use_napi_mode;
        }
        if (!cpu_ids.empty()) {
            byte::StringToNumber(cpu_ids[i], &th_options._cpu_id);
            th_options._thread_name = "rpc_th" + cpu_ids[i];
        }
        th->Init(th_options);
        th->Start();
        ths.push_back(std::move(th));
    }

    // send first rpc
    std::vector<RpcCall*> rpc_call_vec;
    for (int i = 0; i < FLAGS_thread_num; i++) {
        RpcCall* call =
            new RpcCall(ths[i].get(), byterpc::util::EndPoint(endpoint.ip, endpoint.port + i));
        g_running_worker_threads++;
        Closure<void>* closure = NewClosure(&StartClient, call);
        ths[i]->Invoke(closure);
        rpc_call_vec.push_back(call);
    }

    int round = 1;
    while (!g_last_round) {
        if (g_running_worker_threads != 0) {
            byte::ThisThread::SleepInMs(100);
        } else {
            // sleep 10s before next round
            byte::ThisThread::SleepInMs(10 * 1000);
            if (++round == FLAGS_max_round) {
                g_last_round = true;
            }
            for (int i = 0; i < FLAGS_thread_num; i++) {
                g_running_worker_threads++;
                Closure<void>* closure = NewClosure(&StartFlow, rpc_call_vec[i]);
                ths[i]->Invoke(closure);
            }
        }
    }

    for (size_t i = 0; i < ths.size(); i++) {
        ths[i]->Join();
    }
    return 0;
}
