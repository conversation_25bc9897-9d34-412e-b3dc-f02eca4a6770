// Copyright (c) 2021, ByteDance Inc. All rights reserved.

#include <signal.h>
#include <cstring>
#include <thread>
#include <vector>

#include "heavy.pb.h"

#include "byterpc/byterpc_flags.h"
#include "byterpc/controller.h"
#include "byterpc/exec_ctx.h"
#include "byterpc/server.h"
#include "byterpc/thread/ev_thread_helper.h"
#include "byterpc/thread/polling_thread_helper.h"
#include "byterpc/util/closure_guard.h"
#include "byterpc/util/logging.h"
#include "byterpc/util/numautils.h"

#include <absl/memory/memory.h>
#include <byte/string/algorithm.h>
#include <byte/string/number.h>
#include <byte/thread/base_thread_pool.h>

#ifdef BYTERPC_ENABLE_FIBER
#include <byte/fiber/fiber.h>
#endif

DEFINE_int32(thread_num, 4, "Number of threads to send requests");
DEFINE_int32(attachment_size, 0, "response attachment size append by server");
DEFINE_string(bind_cpu_id_list, "", "bind cpu id, if have, must equal to thread_num");
DEFINE_string(listen_addr, "0.0.0.0:18888", "Server listen address");
DEFINE_bool(use_byte_express, false, "enable rdma or not");
DEFINE_bool(use_tarzan, false, "enable tarzan or not");
DEFINE_bool(enable_builtin_service, false, "enable builtin service or not for polling threads");
DEFINE_bool(enable_service_status, false, "enable service status statistics for each service");
DEFINE_bool(builtin_service_with_another_server,
            false,
            "enable individual server to serve builtin services, port=8888");
DEFINE_bool(utcp_multiple_thread, false, "utcp multiple thread or single thread");
DEFINE_bool(use_polling_mode, true, "rdma or tcp use polling mode or event trigger mode");
DEFINE_bool(use_napi_mode, false, "use napi mode for rdma transport");
DEFINE_bool(use_fiber_mode, false, "enable fiber or not");
DEFINE_bool(use_new_api, true, "use new api for incoming attachment");
DEFINE_int32(backend_thread_nums, 0, "thread nums of backend threadpool, 0: disable threadpool");
DEFINE_string(note, "your-note-here", "A identification of running process.");

namespace byterpc {
DECLARE_int32(byterpc_tarzan_worker_num);
DECLARE_int32(byterpc_metric_thread_num);
}  // namespace byterpc

std::vector<std::unique_ptr<byterpc::ThreadBase>> ths;
std::unique_ptr<byte::BaseThreadPool> g_thread_pool;

// kill -15 will stop server then exit the process
void handle_SIGTERM_15(int signal) {
    for (auto& th : ths) {
        th->Stop();
    }
}

static void PrepareAttachment(byterpc::IOBuf* out_iob) {
    BYTERPC_CHECK(out_iob && out_iob->size() == 0);
    const size_t att_sz = FLAGS_attachment_size;

    // allocate data block.
    out_iob->append(byterpc::IOBlockRef::CreateV2(att_sz));
    BYTERPC_CHECK(out_iob->size() >= att_sz);

    // memset attachment data.
    size_t ref_idx = 0;
    size_t remain_sz = att_sz;
    while (remain_sz > 0) {
        BYTERPC_CHECK(ref_idx < out_iob->block_num());
        auto blk_ref = out_iob->block_ref_at(ref_idx);
        size_t sz = std::min(remain_sz, blk_ref.size());
        std::memset(blk_ref.data(), 'a', sz);
        remain_sz -= sz;
        ref_idx++;
    }
}

void InstallSignalHandler(int signal, void (*func)(int)) {
    struct sigaction sa;
    memset(&sa, 0, sizeof(sa));
    sa.sa_handler = func;
    BYTERPC_CHECK(0 == sigaction(signal, &sa, NULL));
}

class MyStatusService : public byterpc::heavy {
public:
    MyStatusService() {
        if (FLAGS_attachment_size > 0) {
            _attachment_to_append.reset(new byterpc::IOBuf);
            PrepareAttachment(_attachment_to_append.get());
        }
    }

    void default_method(::google::protobuf::RpcController* controller,
                        const byterpc::HeavyRequest* request,
                        byterpc::HeavyResponse* response,
                        ::google::protobuf::Closure* done) override {
        if (g_thread_pool) {
            g_thread_pool->AddTask(NewClosure(
                this, &MyStatusService::run_method, controller, request, response, done));
        } else {
            run_method(controller, request, response, done);
        }
    }

    void run_method(::google::protobuf::RpcController* controller,
                    const byterpc::HeavyRequest* request,
                    byterpc::HeavyResponse* response,
                    ::google::protobuf::Closure* done) {
        byterpc::util::ClosureGuard done_guard(done);
        auto cntl = static_cast<byterpc::Controller*>(controller);
        if (cntl->HasIncomingAttachment()) {
            if (FLAGS_use_new_api) {
                byterpc::IOBuf resp_attach;
                cntl->MoveIncomingAttachment(&resp_attach);
                if (_attachment_to_append) {
                    resp_attach.append(*_attachment_to_append);
                }
                cntl->InstallOutgoingAttachment(resp_attach);
            } else {
                byterpc::IOBuf* attachment = cntl->ReleaseIncomingAttachment();
                if (_attachment_to_append) {
                    attachment->append(*_attachment_to_append);
                }
                cntl->InstallOutgoingAttachment(*attachment);
                delete attachment;
            }
        } else if (_attachment_to_append) {
            cntl->InstallOutgoingAttachment(*_attachment_to_append);
        }
    }

private:
    std::unique_ptr<byterpc::IOBuf> _attachment_to_append;
};

static bool StartServer(const byterpc::util::EndPoint& ep) {
    byterpc::ServerOptions options;
    options._enable_builtin_service = FLAGS_enable_builtin_service;
    if (FLAGS_use_byte_express) {
        options._enable_rdma = true;
    } else if (FLAGS_use_tarzan) {
        options._enable_utcp = true;
        options._utcp_listen_mode = FLAGS_utcp_multiple_thread
                                        ? byterpc::UTCP_LISTEN_MULTIPLE_THREAD
                                        : byterpc::UTCP_LISTEN_SINGLE_THREAD;
    } else {
        options._enable_ktcp = true;
    }

    byterpc::Server* server = new byterpc::Server();
    if (server->RegisterService(new MyStatusService,
                                byterpc::SERVER_DOESNT_OWN_SERVICE,
                                FLAGS_enable_service_status) < 0) {
        BYTERPC_LOG(ERROR) << "Fail to register service";
        return false;
    }
    BYTERPC_LOG(INFO) << "Start server=" << ep;
    if (server->Start(ep, options) < 0) {
        BYTERPC_LOG(ERROR) << "Fail to start server";
        return false;
    }

    return true;
}

static bool StartBuiltinServer(const std::string& addr) {
    byterpc::ServerOptions options;
    options._enable_builtin_service = true;
    options._enable_ktcp = true;
    byterpc::Server* server = new byterpc::Server();
    BYTERPC_LOG(INFO) << "Start server=" << addr;
    if (server->Start(addr, options) < 0) {
        BYTERPC_LOG(ERROR) << "Fail to start server";
        return false;
    }

    return true;
}

int main(int argc, char* argv[]) {
    InstallSignalHandler(SIGTERM, handle_SIGTERM_15);

    GFLAGS_NAMESPACE::ParseCommandLineFlags(&argc, &argv, true);

    std::vector<std::string> cpu_ids;
    byte::SplitString(FLAGS_bind_cpu_id_list, ",", &cpu_ids);
    if (cpu_ids.size() && static_cast<int32_t>(cpu_ids.size()) != FLAGS_thread_num) {
        BYTERPC_LOG(ERROR) << "Cpu id number is not equal to thread_num";
        return -1;
    }

    byterpc::util::EndPoint endpoint;
    if (byterpc::util::str2endpoint(FLAGS_listen_addr.c_str(), &endpoint) != 0) {
        BYTERPC_LOG(ERROR) << "Invalid listen_addr=" << FLAGS_listen_addr;
        return -1;
    }

    BYTERPC_LOG(INFO) << "thread_num is: " << FLAGS_thread_num;
    byterpc::InitOptions init_opt(false, false, "byterpc_test");
    if (FLAGS_use_tarzan) {
        byterpc::FLAGS_byterpc_tarzan_worker_num = FLAGS_thread_num;
        init_opt._init_utcp = true;
    } else if (FLAGS_use_byte_express) {
        init_opt._init_rdma = true;
    }

    if (byterpc::ExecCtx::Init(init_opt) < 0) {
        BYTERPC_LOG(ERROR) << "Fail to init tarzan or rdma";
        return -1;
    }
    // Fiber global init, call once in the process
#ifdef BYTERPC_ENABLE_FIBER
    if (FLAGS_use_fiber_mode) {
        if (byte::fiber::GlobalInit({}) != 0) {
            BYTERPC_LOG(ERROR) << "Fail to init fiber";
            return -1;
        }
    }
#endif

    if (FLAGS_backend_thread_nums > 0) {
        g_thread_pool.reset(new byte::BaseThreadPool(FLAGS_backend_thread_nums, "backend"));
    }

    byterpc::loop_type_t loop_type =
        FLAGS_use_polling_mode ? byterpc::LOOP_IF_POSSIBLE : byterpc::LOOP_UNTIL_QUIT;
    for (int i = 0; i < FLAGS_thread_num; i++) {
        std::unique_ptr<byterpc::ThreadBase> th;
        byterpc::ThreadOptions th_options;
        if (loop_type == byterpc::LOOP_IF_POSSIBLE) {
            th.reset(new byterpc::PollingThreadHelper());
        } else {
            th.reset(new byterpc::EvThreadHelper());
            th_options._enable_napi = FLAGS_use_napi_mode;
            th_options._enable_fiber = FLAGS_use_fiber_mode;
            if (FLAGS_use_byte_express) {
                th_options._transport = byterpc::TYPE_RDMA;
            } else if (FLAGS_use_tarzan) {
                BYTERPC_LOG(ERROR) << "tarzan do not support event-trigger.";
                return -1;
            } else {
                th_options._transport = byterpc::TYPE_KERNEL_TCP;
            }
        }
        if (!cpu_ids.empty()) {
            byte::StringToNumber(cpu_ids[i], &th_options._cpu_id);
            th_options._thread_name = "rpc_th" + cpu_ids[i];
        }
        th->Init(th_options);
        if (!endpoint.is_extended()) {
            byterpc::util::EndPoint ep = endpoint;
            ep.port += i;
            th->Start(std::bind(StartServer, ep));
        } else {
            // create uds path separately for each thread
            std::string new_uds_path = FLAGS_listen_addr + "_" + std::to_string(i);
            byterpc::util::EndPoint ep;
            if (0 != byterpc::util::str2endpoint(new_uds_path.c_str(), &ep)) {
                BYTERPC_LOG(ERROR) << "UDS create addr failed, path:" << new_uds_path;
                return -1;
            }
            th->Start(std::bind(StartServer, ep));
        }
        ths.push_back(std::move(th));
    }

    if (FLAGS_builtin_service_with_another_server) {
        std::unique_ptr<byterpc::ThreadBase> th(new byterpc::EvThreadHelper());
        byterpc::ThreadOptions th_options;
        th_options._transport = byterpc::TYPE_KERNEL_TCP;
        th->Init(th_options);
        th->Start(std::bind(StartBuiltinServer, std::string("0.0.0.0:8888")));
        ths.push_back(std::move(th));
    }
    for (size_t i = 0; i < ths.size(); i++) {
        ths[i]->Join();
    }
    if (g_thread_pool) {
        g_thread_pool->Terminate();
    }
    return 0;
}
