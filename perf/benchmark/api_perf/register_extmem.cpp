// Copyright (c) 2024, ByteDance Inc. All rights reserved.

#include <gflags/gflags.h>
#include <sys/mman.h>
#include <iostream>

#include "benchmark/benchmark.h"
#include "byterpc/exec_ctx.h"
#include "byterpc/mem/mem.h"

namespace {
DEFINE_bool(use_hugepage, false, "use hugepage");
void* kAddr = nullptr;
}  // namespace

static void RegisterExternamMem(benchmark::State& state) {
    size_t i = 0;
    std::vector<byterpc::MemPageInfo> mem_pages;
    size_t size = state.range(0);

    byterpc::MemPageInfo page_info;
    page_info.hold_type = byterpc::HolderType::UserNormal;
    page_info.len = size;
    page_info.nr_page = 1;
    page_info.page_sz = size;
    for (auto _ : state) {
        page_info.vaddr = reinterpret_cast<char*>(kAddr) + i * size;
        i++;
        byterpc::ExtmemRegister(page_info);
        mem_pages.push_back(page_info);
    }

    for (auto page : mem_pages) {
        byterpc::ExtmemUnregister(page);
    }
}

BENCHMARK(RegisterExternamMem)->Iterations(100)->Repetitions(20)->Arg(4096);
BENCHMARK(RegisterExternamMem)->Iterations(100)->Repetitions(20)->Arg(2 * 1024 * 1024);
BENCHMARK(RegisterExternamMem)->Iterations(100)->Repetitions(20)->Arg(4 * 1024 * 1024);
BENCHMARK(RegisterExternamMem)->Iterations(100)->Repetitions(20)->Arg(8 * 1024 * 1024);
BENCHMARK(RegisterExternamMem)->Iterations(100)->Repetitions(20)->Arg(16 * 1024 * 1024);
BENCHMARK(RegisterExternamMem)->Iterations(50)->Repetitions(20)->Arg(32 * 1024 * 1024);
BENCHMARK(RegisterExternamMem)->Iterations(25)->Repetitions(20)->Arg(64 * 1024 * 1024);
BENCHMARK(RegisterExternamMem)->Iterations(2)->Repetitions(10)->Arg(1024 * 1024 * 1024);

int main(int argc, char** argv) {
    GFLAGS_NAMESPACE::ParseCommandLineFlags(&argc, &argv, true);
    byterpc::InitOptions init_opt;
#ifdef BYTERPC_ENABLE_BYTE_EXPRESS
    init_opt._init_rdma = true;
#endif
#ifdef BYTERPC_ENABLE_UTCP
    init_opt._init_utcp = true;
#endif
    if (byterpc::ExecCtx::Init(init_opt) < 0) {
        std::cout << "Fail to init byterpc" << std::endl;
        return -1;
    }

    // Allocate memory for register
    const size_t size = 2 * 1024 * 1024 * 1024UL;
    if (!FLAGS_use_hugepage) {
        kAddr = mmap(nullptr, size, PROT_READ | PROT_WRITE, MAP_PRIVATE | MAP_ANONYMOUS, -1, 0);
    } else {
        kAddr = mmap(nullptr,
                     size,
                     PROT_READ | PROT_WRITE,
                     MAP_PRIVATE | MAP_ANONYMOUS | MAP_HUGETLB,
                     -1,
                     0);
    }
    if (kAddr == MAP_FAILED) {
        std::cout << "Fail to allocate memory" << std::endl;
        return -1;
    }

    benchmark::Initialize(&argc, argv);
    if (benchmark::ReportUnrecognizedArguments(argc, argv)) {
        return -1;
    }
    benchmark::RunSpecifiedBenchmarks();
    return 0;
}
