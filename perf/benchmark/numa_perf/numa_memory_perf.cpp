// Copyright (c) 2021, ByteDance Inc. All rights reserved.

#include <gflags/gflags.h>
#include <string>

#include "benchmark/benchmark.h"
#include "byterpc/exec_ctx.h"
#include "byterpc/util/logging.h"
#include "byterpc/util/numautils.h"
#include "mem/memory_pool.h"

namespace {
DEFINE_int32(bind_cpu_id, 0, "bind cpu id");
DEFINE_uint32(allocate_memory_pool_id, 1, "allocate memory on numa node");
DEFINE_uint64(test_memory_size, 4, "memory size in KBytes");
DEFINE_bool(test_write_memory, true, "test write or read memory");

}  // namespace

using namespace byterpc;

static void test_access_memory(benchmark::State& state) {
    MemoryPool* g_pool = MemoryPool::Get();
    std::vector<void*> vec;
    vec.resize(state.max_iterations);
    size_t mem_size = FLAGS_test_memory_size * 1024UL;
    for (size_t i = 0; i < state.max_iterations; ++i) {
        void* addr = g_pool->Alloc(mem_size, FLAGS_allocate_memory_pool_id);
        memset(addr, 0, mem_size);
        vec[i] = addr;
    }

    size_t idx = 0;
    if (FLAGS_test_write_memory) {
        for (auto _ : state) {
            void* addr = vec[idx++];
            memset(addr, 'a', mem_size);
        }
    } else {
        uint64_t val = 0;
        for (auto _ : state) {
            uint64_t* addr = static_cast<uint64_t*>(vec[idx++]);
            for (size_t i = 0; i < mem_size / sizeof(uint64_t); ++i) {
                val = *(addr + i);
            }
        }
        BYTERPC_LOG(INFO) << "value: " << val;
    }
    for (size_t i = 0; i < vec.size(); ++i) {
        g_pool->Dealloc(vec[i]);
    }
    vec.clear();
}

BENCHMARK(test_access_memory)->Iterations(1000000)->Repetitions(20)->ReportAggregatesOnly(true);

int main(int argc, char** argv) {
    GFLAGS_NAMESPACE::ParseCommandLineFlags(&argc, &argv, true);
    if (FLAGS_bind_cpu_id >= 0) {
        util::bind_this_to_core(FLAGS_bind_cpu_id);
    }
    InitOptions opt(false, false);
#ifdef BYTERPC_ENABLE_BYTE_EXPRESS
    opt._init_rdma = true;
#endif
    ExecCtx::Init(opt);

    ::benchmark::RunSpecifiedBenchmarks();
    return 0;
}
