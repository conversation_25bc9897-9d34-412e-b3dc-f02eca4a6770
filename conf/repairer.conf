# Configure for repairer in the format with gflags

# local port
--port=8600

# master address: support BNS & DNS
#  BNS - address string should start with 'bns://',
#        if bns instance does not configure port, port will be set as which parsed from address string.
#  DNS - otherwise.
--master_address=bns://aries-fault-master.yun-object-qa.qd01:8100

# heartbeat interval
--heartbeat_interval_second=60

# tinker address address in BNS name
--tinker_address=bns://aries-fault-tinker.yun-object-qa.qd01
# validator address address in BNS name
--validator_address=bns://aries-fault-validator.yun-object-qa.qd01
# checkcenter address in ip:port
--checkcenter_address=bns://aries-fault-checkcenter.yun-object-qa.qd01
# volume_service address in BNS name
--volume_service_address=bns://aries-fault-volumeservice.yun-object-qa.qd01
--volume_service_load_balancer=rr

--query_meta_timeout_ms=500
# load balancer
--load_balancer=rr

# token
--token=default_token
--bvar_dump_interval=60

# worker nums
--diff_worker_num=1
--repair_worker_num=1
--delete_worker_num=1
--check_manager_num=1

# commlog configure
# log level definition: 0-TRACE 1-NOTICE 2-WARNING 3-ERROR 4-FATAL
--min_log_level=0
--comlog_enable_async=true
--comlog_enable_wf=true
--comlog_path=log
--comlog_split_type=1
--comlog_quota_size=204800
--comlog_quota_day=0
--comlog_quota_hour=0

############################### The following flags are reloadable ################################

# rpc timeouts
--call_timeout_ms=30000
--connect_timeout_ms=3000
--retry_interval_ms=3000
--fast_timeout_ms=2000
--idle_timeout_s=7200

# worker time related
--worker_wait_timespan_ms=200
--worker_request_task_timespan_ms=1000

# worker speed related
--max_diff_volume_num_per_second=2
--max_background_repair_num=20
--max_repair_blob_num_per_second=100
--max_repair_blob_flow_kb=400000
--max_delete_blob_num_per_second=100
--repair_last_check_all_time_second=604800
--delete_last_check_all_time_second=259200
--max_blob_task_num=300000
--avoid_fresh_blob_interval_second=10
--delay_delete_interval_second=604800
--per_batch_repair=20

# qps limitation
--max_busy_blob_num_per_volume=10
--speed_coefficient_of_repair_task=0.4
--speed_coefficient_of_delete_task=0.3

# blob fresh timespan in seconds
--blob_fresh_timespan=300

# max blob num per ReportVolumeCheckResultsRequest
--volume_check_result_max_blob_num=5000

# priority control
--high_level_repair_priority=2

# repair key meta
--need_to_check_key_meta=true

# worker speed limit
--max_check_blob_num_per_second=10
--max_check_blob_flow_kb=50000

--check_blob_retry_interval_s=3600
--check_blob_max_retry_times=5
--fast_timeout_ms=2000
--max_background_check_num=20

# compress service
--compress_priority_level_num=3
--compress_thread_num_in_pool=1
--compress_max_task_in_pool=5
--thread_num_on_brunsli=4
--recheck_compress=true