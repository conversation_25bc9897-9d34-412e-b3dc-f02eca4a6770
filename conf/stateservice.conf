# Configure for repairer in the format with gflags

# local port
# meta sync port = local port + 1
--port=8150

# master address: support BNS & DNS
#  BNS - address string should start with 'bns://',
#        if bns instance does not configure port, port will be set as which parsed from address string.
#  DNS - otherwise.
--master_address=bns://aries-rd-test-master.BCE.all:8044

--stateservice_address=bns://aries-rd-test-stateservice.BCE.all:8150

--stateservice_raft_group_count=1
# token 
--token=default_token

# bvar monitor
--bvar_monitor_include=all_cluster*;master*;aries_master*;*space

# commlog configure
# log level definition: 0-TRACE 1-NOTICE 2-WARNING 3-ERROR 4-FATAL
--min_log_level=0
--comlog_enable_async=true
--comlog_enable_wf=true
--comlog_path=log
--comlog_split_type=1
--comlog_quota_size=10240
--comlog_quota_day=0
--comlog_quota_hour=0

############################### The following flags are reloadable ################################

# rpc timeouts
--call_timeout_ms=30000
--connect_timeout_ms=3000
--retry_interval_ms=3000

# raft related
--raft_election_timeout_ms=1000

# bvar monitor
--bvar_max_monitor_num=2000
--bvar_history_num=120