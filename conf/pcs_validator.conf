# Configure for validator in the format with gflags

# local port
--port=8150
# token
--token=default_token
--bvar_dump_interval=60
# data agent address in BNS name
--dataagent_address=bns://aries-test-dataagent.yun-object.qd01:8130
# load balancer for access data agent
--keymap_url=http://sh01-pcs-dev00.sh01.baidu.com:8012/poms/validator

# commlog configure
# log level definition: 0-TRACE 1-NOTICE 2-WARNING 3-ERROR 4-FATAL
--min_log_level=0
--comlog_enable_async=true
--comlog_enable_wf=true
--comlog_path=log
--comlog_split_type=1
--comlog_quota_size=204800
--comlog_quota_day=0
--comlog_quota_hour=0

############################### The following flags are reloadable ################################

# rpc timeouts
--call_timeout_ms=30000
--connect_timeout_ms=3000
--retry_interval_ms=3000
--idle_timeout_s=7200
