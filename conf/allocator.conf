# Configure for allocator in the format with gflags

# local port
--port=8200

# master address: support BNS & DNS
#  BNS - address string should start with 'bns://',
#        if bns instance does not configure port, port will be set as which parsed from address string.
#  DNS - otherwise.
--master_address=bns://aries-test-master.yun-object.qd01:8100

# token
--token=default_token
--bvar_dump_interval=60

# edits log related
--image_dir=./meta/image
--blob_gap=10000

# recover mode
--recover_mode=false

# sync meta related
--meta_replica_snapshot_interval_second=3600
--meta_replica_keep_journal_num_threshold=1000000
--meta_replica_sub_journal_interval_second=1
--meta_replica_snapshot_download_speed_limit=3145728
--meta_replica_snapshot_path_file=./meta/meta.snapshot

# commlog configure
# log level definition: 0-TRACE 1-NOTICE 2-WARNING 3-ERROR 4-FATAL
--min_log_level=0
--comlog_enable_async=true
--comlog_enable_wf=true
--comlog_path=log
--comlog_split_type=1
--comlog_quota_size=20480
--comlog_quota_day=0
--comlog_quota_hour=0

############################### The following flags are reloadable ################################

# rpc timeouts
--call_timeout_ms=30000
--connect_timeout_ms=3000
--retry_interval_ms=3000
--idle_timeout_s=7200

# allocation & collection related
--least_free_size=335544320
--collect_period_s=300

# checkpoint related
--ckpt_interval_s=7200
--roll_interval_s=300

# max concurrency get vlet info task
--max_collect_task_concurrency=100
