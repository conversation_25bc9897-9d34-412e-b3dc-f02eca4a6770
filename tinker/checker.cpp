/**
 * ARIES - A Reliable and Impressive EB-level Storage.
 * Copyright (c) 2017 Baidu.com, Inc. All Rights Reserved.
 *
 * Author: <PERSON> <PERSON> (guo<PERSON>@baidu.com)
 * Date: 2017/02/17
 * Desciption: Implementations of consistence hash checker
 *
 */

#include "baidu/rpc/policy/hasher.h"
#include "tinker/checker.h"
#include "tinker/flags.h"

namespace aries {
namespace tinker {

Checker* g_checker = new Checker;

Checker::Checker() {
    _inited = false;
    _hashing_checker = new ::aries::common::ConsistentHashingChecker();
}

Checker::~Checker() {
    common::ScopedMutexLock lock(_cond);
    delete _hashing_checker;
}

void Checker::change_tinker_list(
            const std::vector<std::string>& new_tinkers) {
    common::ScopedMutexLock lock(_cond);

    // Add new tinkers
    _hashing_checker->replace_server(new_tinkers);

    _inited = true;

    _cond.signal();
}

bool Checker::is_mine_volume(uint64_t volume_id) {
    uint32_t code = get_code(volume_id);
    std::string server;
    int ret = _hashing_checker->get_server(code, &server);
    return ret == 0 && server == FLAGS_tinker_name;
}

uint32_t Checker::get_code(uint64_t volume_id) {
    char* str = reinterpret_cast<char*>(&volume_id);
    uint32_t code = ::baidu::rpc::policy::MurmurHash32(str, sizeof(uint64_t));
    return code;
}
 
void Checker::wait_until_init() {
    if (!_inited) {
        common::ScopedMutexLock lock(_cond);
        _cond.wait();
    }
}
   
}
}
