/*************************************************************************
 *
 * Copyright (c) Baidu.com, Inc. All Rights Reserved
 *
 ************************************************************************/

/**
 * @file tinker/task/task_base.h
 * <AUTHOR>
 * @date Fri 19 Oct 2018 05:58:35 PM CST
 * @brief 
 *
 **/
#ifndef BAIDU_INF_ARIES_TINKER_TASK_BASE_H
#define BAIDU_INF_ARIES_TINKER_TASK_BASE_H

#include <memory>
#include <string>
#include <unordered_map>
#include <unordered_set>
#include <bitset>
#include <base/endpoint.h>
#include <base/time.h>
#include <base/rand_util.h>
#include <baidu/rpc/server.h>
#include <baidu/rpc/channel.h>
#include "baidu/inf/aries/tinker/flags.h"
#include "baidu/inf/aries-api/common/lock.h"
#include "baidu/inf/aries-api/common/common.h"
#include "baidu/inf/aries-api/common/proto/master.pb.h"
#include "baidu/inf/aries-api/common/proto/tinker.pb.h"
#include "baidu/inf/aries-api/common/proto/enum.pb.h"

namespace aries {
namespace tinker {

constexpr int PRIORITY_LEVEL = 10;
constexpr int MIN_DELETE_TASK_PRIORITY_LEVEL = 5;

//===========================definition of base struct=========================

struct VolumeNode {
    uint64_t vid;
    struct VolumeNode* prev;
    struct VolumeNode* next;

    VolumeNode() : vid(0), prev(nullptr), next(nullptr) { }
    VolumeNode(uint64_t vid) : vid(vid), prev(nullptr), next(nullptr) { }
};

// fast queue, support push_front, push_back, pop_front, remove_vid
// insert, find, delete all O(1)
struct FastQueue {
    std::unordered_map<uint64_t, struct VolumeNode*> hash;
    struct VolumeNode* head;
    struct VolumeNode* tail;

    FastQueue() {
        head = new VolumeNode();
        tail = head;
        hash.clear();
    }

    ~FastQueue() {
        hash.clear();
        if (head) {
            auto tmp = head->next;
            while (tmp != nullptr) {
                auto next = tmp->next;
                delete tmp;
                tmp = next;
            }
            delete head;
            head = nullptr;
            tail = nullptr;
        }
    }

    // don't use
    bool push_front(const uint64_t vid) {
        if (hash.find(vid) != hash.end()) {
            return false;
        }
        VolumeNode* tmp = new VolumeNode(vid); 
        tmp->next = head->next;
        tmp->prev = head;
        if (head->next != nullptr) {
            head->next->prev = tmp;
        }
        head->next = tmp;
        if (tail == head) {
            tail = tmp;
        }
        auto ret = hash.insert(std::make_pair(vid, tmp)).second;
        return ret;
    }

    // don't use
    bool push_back(const uint64_t vid) {
        if (hash.find(vid) != hash.end()) {
            return false;
        }
        VolumeNode* tmp = new VolumeNode(vid); 
        tmp->prev = tail;
        tail->next = tmp;
        tail = tmp;
        auto ret = hash.insert(std::make_pair(vid, tmp)).second;
        return ret;
    }

    bool pop_front(uint64_t* vid) {
        if (hash.size() == 0) {
            return false;
        }
        *vid = head->next->vid;
        auto tmp = head->next;
        if (head->next->next != nullptr) {
            head->next->next->prev = head;
        } else { // last node
            tail = head;
        }

        head->next = head->next->next;
        hash.erase(*vid);
        delete tmp;
        return true;
    }

    bool remove_vid(const uint64_t vid) {
        auto itr = hash.find(vid);
        if (itr == hash.end()) {
            return false;
        }
        auto tmp = itr->second;
        tmp->prev->next = tmp->next;
        
        if (tmp->next != nullptr) {
            tmp->next->prev = tmp->prev;
        } else {  // last node
            tail = tmp->prev;
        }

        hash.erase(vid);
        delete tmp; 
        return true;
    }

    bool insert(const uint64_t vid) {
        if (hash.find(vid) != hash.end()) {
            return false;
        }

        auto pre = tail;
        auto cur = tail->next;
        VolumeNode* tmp = new VolumeNode(vid);
        pre->next = tmp;
        tmp->prev = pre;
        tmp->next = cur;
        if (cur == nullptr) {
            // insert to tail
            tail = tmp;
        } else {
            cur->prev = tmp;
        }

        auto ret = hash.insert(std::make_pair(vid, tmp)).second;
        return ret;
    }

    size_t size() {
        return hash.size();
    }
};

// Priority Queue base on FastQueue, support repair task management
template<int PriorityLevelNum>
class FastPriorityQueue {
public:
    FastPriorityQueue() : _priority_level_num(PriorityLevelNum), _total(0) {
        static_assert(PriorityLevelNum > 0, "PriorityLevelNum should > 0");
    }

    ~FastPriorityQueue() {
        _total = 0;
    }

    int priority_level_num() const {
        return _priority_level_num;
    }

    bool contains(uint64_t vid) {
        return _priority_map.find(vid) != _priority_map.end();
    }

    bool insert(const uint64_t vid, const int prio) {
        if (prio < 0 || prio >= _priority_level_num) {
            return false;
        }
        auto ret = _queues[prio].insert(vid);
        if (!ret) {
            return false;
        }
        _total++;
        _priority_map[vid] = prio;
        return true;
    }

    bool push_emergency(const uint64_t vid, const int prio) {
        if (prio < 0 || prio >= _priority_level_num) {
            return false;
        }
        auto ret = _queues[prio].push_front(vid);
        if (!ret) {
            return false;
        }
        _total++;
        _priority_map[vid] = prio;
        return true;
    }

    bool pop(uint64_t* vid) {
        int prio;
        return pop(vid, &prio);
    }

    bool pop(uint64_t* vid, int* prio) {
        for (int i = 0; i < _priority_level_num; ++i) {
            auto ok = _queues[i].pop_front(vid);
            if (!ok) {
                continue;
            }

            *prio = i;

            _total--;
            auto it = _priority_map.find(*vid);
            if (it != _priority_map.end()) {
                _priority_map.erase(it);
            }
            return true;
        }
        return false; 
    }

    bool change_priority(const uint64_t vid, const int prio) {
        if (prio < 0 || prio >= _priority_level_num) {
            return false;
        }
        auto it = _priority_map.find(vid);
        if (it == _priority_map.end()) {
            return false;
        }
        int from_prio = it->second;
        auto ret = _queues[from_prio].remove_vid(vid);
        if (!ret) {
            return false;
        }
        ret = _queues[prio].insert(vid);
        if (!ret) {
            auto ok = _queues[from_prio].insert(vid);
            return false;
        }
        it->second = prio;
        return ret;
    }
    
    bool empty() const {
        return _total == 0;
    }

    size_t size() const {
        return _total;
    }

    size_t size(int prio) {
        if (prio < 0 || prio >= _priority_level_num) {
            return 0;
        }

        return _queues[prio].size();
    }

    std::map<int, std::vector<uint64_t>> get_vids() {
        std::map<int, std::vector<uint64_t>> ret;
        for (int i = 0; i < _priority_level_num; ++i) {
            if (_queues[i].size() == 0) {
                continue;
            }
            std::vector<uint64_t> vids;
            auto tmp = _queues[i].head->next;
            while (tmp != nullptr) {
                vids.push_back(tmp->vid);
                tmp = tmp->next;
            }
            ret[i] = vids;
        }
        return ret;
    }

private:
    const int _priority_level_num;
    size_t _total;
    FastQueue _queues[PriorityLevelNum];
    std::unordered_map<uint64_t, int> _priority_map;
};

class CountLatencyRecorder {
public:
    CountLatencyRecorder(bool is_repair): _count(0), _latency(0), _is_repair(is_repair) { }
    void record_count_latency(int64_t count, int64_t latency);
    int64_t get_count();
    int64_t get_latency();
    void reset();
    bool is_repair();
    void lock();
    void unlock();
private:
    common::MutexLock _mutex;
    int64_t _count;
    int64_t _latency;
    bool _is_repair;
};

extern CountLatencyRecorder* g_blob_repair_recorder;
extern CountLatencyRecorder* g_blob_delete_recorder;

//===========================definition of task management struct=========================

struct TinkerTaskId {
    uint64_t task_begin_timestamp;  // task's create timestamp
    uint64_t random;

    TinkerTaskId() : task_begin_timestamp(0), random(0) { }
    bool operator==(const TinkerTaskId& taskid) const {
        return this->task_begin_timestamp == taskid.task_begin_timestamp
                && this->random == taskid.random;
    }

    bool operator!=(const TinkerTaskId& taskid) const {
        return !(*this == taskid);
    }

    bool operator<(const TinkerTaskId& taskid) const {
        if (this->random != taskid.random) {
            return this->random < taskid.random;
        }
        return this->task_begin_timestamp < taskid.task_begin_timestamp;
    }
};

struct VolumeTask {
    base::EndPoint worker;
    uint64_t vid;
    uint64_t task_begin_timestamp;
    VolumeTask() : vid(0), task_begin_timestamp(0) { }

    inline bool operator==(const VolumeTask& rhs) const {
        return this->worker == rhs.worker && this->vid == rhs.vid; 
    }
};

struct AbnormalVolumeTask {
    VolumeTask first_ab_task;
    VolumeTask last_ab_task;
    uint32_t failed_cnt;
    uint64_t last_reset_fail_cnt_time;
    AbnormalVolumeTask() : failed_cnt(0) {
        uint64_t now = base::gettimeofday_s();
        last_reset_fail_cnt_time = now;
    }
};

struct Vlet {
    int shard_index;
    uint32_t version;
    VletState state;
    base::EndPoint addr;
};

struct VolumeContext {
    uint64_t volume_id;
    std::string space_name;
    ECOption eco;
    std::vector<std::shared_ptr<Vlet> > vlets;
    std::string validator_addr;
    int repair_priority;
    int delete_priority;
    std::map<int, uint32_t> unrepaired_blobs;  // for update volume repair priority
    std::map<int, uint32_t> undeleted_blobs;  // for monitor and update volume delete priority
    uint32_t repair_last_check_all_timestamp; // for repair_worker do all check
    uint32_t delete_last_check_all_timestamp; // for delete_worker do all check
    uint32_t diff_last_check_all_timestamp; // for diff_worker do all check
    bool permit_batch_repair;
    bool already_on_tape;
    // to limit list blob index
    uint64_t last_diff_list_blob_index_timestamp;
    uint64_t last_repair_list_blob_index_timestamp;
    uint64_t last_delete_list_blob_index_timestamp;
    // last fetch task timestamp
    uint32_t last_fetch_repair_task_timestamp;
    // last finish repair timestamp
    uint32_t last_finish_repair_task_timestamp;
    // delay delete interval
    uint32_t delay_delete_interval_second;
    int disk_type; // 0 for hdd, 1 for smr, 2 for ssd

    VolumeContext() : 
        volume_id(0), 
        repair_priority(PRIORITY_LEVEL - 1),
        delete_priority(PRIORITY_LEVEL - 1),
        repair_last_check_all_timestamp(0),
        delete_last_check_all_timestamp(0),
        diff_last_check_all_timestamp(0),
        permit_batch_repair(false),
        already_on_tape(false),
        last_diff_list_blob_index_timestamp(0), 
        last_repair_list_blob_index_timestamp(0), 
        last_delete_list_blob_index_timestamp(0),
        last_fetch_repair_task_timestamp(0),
        last_finish_repair_task_timestamp(0),
        delay_delete_interval_second(FLAGS_delay_delete_interval_second),
        disk_type(0) {}
    
    VolumeContext(const VolumeContext& context) {
        volume_id = context.volume_id;
        eco.type = context.eco.type;
        eco.param.n = context.eco.param.n;
        eco.param.k = context.eco.param.k;
        vlets.resize(eco.param.n);
        validator_addr = context.validator_addr;
        repair_priority = context.repair_priority;
        delete_priority = context.delete_priority;
        repair_last_check_all_timestamp = context.repair_last_check_all_timestamp;
        delete_last_check_all_timestamp = context.delete_last_check_all_timestamp;
        diff_last_check_all_timestamp = context.diff_last_check_all_timestamp;
        permit_batch_repair = context.permit_batch_repair;
        already_on_tape = context.already_on_tape;
        last_diff_list_blob_index_timestamp = context.last_diff_list_blob_index_timestamp;
        last_repair_list_blob_index_timestamp = context.last_repair_list_blob_index_timestamp;
        last_delete_list_blob_index_timestamp = context.last_delete_list_blob_index_timestamp;
        last_fetch_repair_task_timestamp = context.last_fetch_repair_task_timestamp;
        last_finish_repair_task_timestamp = context.last_finish_repair_task_timestamp;
        delay_delete_interval_second = context.delay_delete_interval_second;
        disk_type = context.disk_type;

        for (size_t i = 0; i < context.vlets.size(); ++i) {
            vlets[i] = nullptr;
            if (context.vlets[i]) {
                vlets[i] = std::make_shared<Vlet>();
                vlets[i]->shard_index = context.vlets[i]->shard_index;
                vlets[i]->version = context.vlets[i]->version;
                vlets[i]->state = context.vlets[i]->state;
                vlets[i]->addr = context.vlets[i]->addr;
            }
        }

        for (auto pair : context.unrepaired_blobs) {
            unrepaired_blobs[pair.first] = pair.second;
        }

        for (auto pair : context.undeleted_blobs) {
            undeleted_blobs[pair.first] = pair.second;
        }
    }

    ~VolumeContext() {
        for (auto vlet : vlets) {
            vlet.reset();
        }
    }
};

//===========================definition of utils=========================

TinkerTaskId next_task_id();
}
}
#endif
