/**
 * ARIES - A Reliable and Impressive EB-level Storage.
 * Copyright (c) 2017 Baidu.com, Inc. All Rights Reserved.
 *
 * Author: <PERSON> (guo<PERSON>@baidu.com)
 * Date: 2017/02/17
 * Description: Unittest for Tinker
 *
 */

#include <gtest/gtest.h>
#include <base/logging.h>
#include "bmock.h"

#include "baidu/inf/aries-api/common/types.h"
#include "baidu/inf/aries-api/common/config.h"
#include "baidu/inf/aries-api/common/master_tracker.h"
#include "baidu/inf/aries/tinker/flags.h"
#include "baidu/inf/aries/tinker/tinker.h"
#include "baidu/inf/aries/tinker/task/task_base.h"

using ::testing::Return;
using ::testing::_;

BMOCK_NS_CLASS_METHOD0(aries::tinker, VolumeScheduler, init, bool());

namespace aries {
namespace tinker {

class TinkerTests : public ::testing::Test {
public:
    void SetUp() {
        FLAGS_meta_replica_snapshot_path_file = "./metafortesttinker/meta.snapshot";
        system("mkdir -p ./metafortesttinker");
        init_meta_snapshot();
    }
    void TearDown() {
        system("rm -rf ./metafortesttinker");
    }
    void init_meta_snapshot() {
        aries::meta_replica::MetaData meta_service;
        std::vector<aries::pb::TinkerInfo> tinker_list;
        for (size_t i = 0; i < 3; ++i) {
            base::EndPoint ep;
            common::str2endpoint("127.0.0.1", 61830 + i, &ep);
            aries::pb::TinkerInfo tinker;
            tinker.set_tinker_name("tinker" + std::to_string(i));
            tinker.set_tinker_addr(common::endpoint2int(ep));
            tinker_list.push_back(tinker);
        }
        meta_service.set_tinker_list(tinker_list);
        auto ret = meta_service.save_snapshot(FLAGS_meta_replica_snapshot_path_file, 1);
        ASSERT_EQ(ret, AIE_OK);
    }
};

TEST_F(TinkerTests, normal_mode_succeeded) {
    BMOCK_NS_CLASS_RESUME(aries::tinker, VolumeScheduler, init, bool());
    EXPECT_CALL(BMOCK_NS_CLASS_OBJECT(aries::tinker, VolumeScheduler, init), init())
            .WillRepeatedly(Return(true));

    common::FLAGS_master_address = "127.0.0.1:65535";
    common::FLAGS_port = 64350;
    common::FLAGS_interfaces = "lo";
    ASSERT_EQ(0, common::init_local_addr());

    Tinker tinker;
    ASSERT_EQ(0, tinker.start());
    tinker.stop();
    tinker.join();
    sleep(1);

    BMOCK_NS_CLASS_STOP(aries::tinker, VolumeScheduler, init, bool());
}

TEST_F(TinkerTests, recover_mode_failed_1) {
    common::FLAGS_master_address = "127.0.0.1:65535";
    common::FLAGS_port = 64351;
    common::FLAGS_interfaces = "lo";
    ASSERT_EQ(0, common::init_local_addr());
    FLAGS_recover_mode = true;

    // failed, due to tinker name not exist
    Tinker tinker;
    FLAGS_tinker_name = "xxxx"; 
    ASSERT_EQ(-1, tinker.start());
    tinker.stop();
    tinker.join();
    sleep(1);
}

TEST_F(TinkerTests, recover_mode_failed_2) {
    common::FLAGS_master_address = "127.0.0.1:65535";
    common::FLAGS_port = 64352;
    common::FLAGS_interfaces = "lo";
    ASSERT_EQ(0, common::init_local_addr());
    FLAGS_recover_mode = true;

    // failed, due to tinker name not match tinker addr
    Tinker tinker;
    FLAGS_tinker_name = "tinker0"; 
    ASSERT_EQ(-1, tinker.start());
    tinker.stop();
    tinker.join();
    sleep(1);
}

TEST_F(TinkerTests, recover_mode_succ) {
    common::FLAGS_master_address = "127.0.0.1:65535";
    common::FLAGS_port = 61830;
    common::FLAGS_interfaces = "lo";
    ASSERT_EQ(0, common::init_local_addr());
    FLAGS_recover_mode = true;

    // succeeded
    Tinker tinker;
    FLAGS_tinker_name = "tinker0"; 
    ASSERT_EQ(0, tinker.start());
    ASSERT_EQ(0, tinker.update_conf());
    sleep(1);
    tinker.stop();
    tinker.join();
    sleep(1);
}

}
}
