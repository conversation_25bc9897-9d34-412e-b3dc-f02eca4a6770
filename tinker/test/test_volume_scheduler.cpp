/**
 * ARIES - A Reliable and Impressive EB-level Storage.
 * Copyright (c) 2017 Baidu.com, Inc. All Rights Reserved.
 *
 * Author: <PERSON> (guo<PERSON>@baidu.com)
 * Date: 2017/02/09
 * Description: Unittest for VolumeScheduler
 *
 */

#include <gtest/gtest.h>
#include <base/logging.h>
#include <baidu/rpc/server.h>
#include <bthread.h>
#include <bthread_types.h>
#include "baidu/inf/aries-api/common/types.h"
#include "baidu/inf/aries-api/common/lock.h"
#include "baidu/inf/aries-api/common/master_tracker.h"
#include "baidu/inf/aries/tinker/test/mock_master.h"
#include "baidu/inf/aries/tinker/volume_scheduler.h"
#include "baidu/inf/aries/tinker/task/task_manager.h"

namespace aries {
namespace tinker {

static uint64_t g_volume_ids[3] = {10000, 10001, 10002};
static std::string g_space_names[3] = {"space0", "space1", "space2"};

class MockMetaClient : public meta_replica::MetaSubscriber {
public:
    virtual void wait_until_meta_very_new() {
        return;
    }
    virtual AriesErrno init(bool is_not_subsribe) {
        return _let_init_failed ? AIE_FAIL : AIE_OK;
    }
    virtual void list_volume(std::vector<uint64_t> *volume_ids) {
        common::ScopedMutexLock lock(_mutex);
        if (_volume_count < 0) {
            return;
        }
        for (int i = 0; i < _volume_count; ++i) {
            volume_ids->push_back(g_volume_ids[i]);
        }
    }
    virtual AriesErrno get_volume_info(const uint64_t volume_id,
                                   aries::pb::MetaReplica::VolumeInfo *volume_info,
                                   aries::pb::SpaceInfo *space_info) {
        if (_let_find_volume_failed) {
            return AIE_FAIL;
        }
        for (size_t i = 0; i < 3; ++i) {
            if (volume_id == g_volume_ids[i]) {
                volume_info->CopyFrom(*_volumes[i]);
                space_info->CopyFrom(*_spaces[i]);
                return AIE_OK;
            }
        }
        return AIE_NOT_EXIST;
    }

    static void let_init_failed() {
        _let_init_failed = true;
    }
    static void let_init_succ() {
        _let_init_failed = false;
    }

    static void let_find_volume_failed() {
        _let_find_volume_failed = true;
    }
    static void let_find_volume_succ() {
        _let_find_volume_failed = false;
    }

    static void set_volume_count(int count) {
        common::ScopedMutexLock lock(_mutex);
        assert(count <= (int)_volumes.size());
        _volume_count = count;
    }
    static void init_volumes_with_spaces() {
        for (size_t i = 0; i < 3; ++i) {
            auto * vi = new aries::pb::MetaReplica::VolumeInfo();
            vi->set_volume_id(g_volume_ids[i]);
            vi->set_space_name(g_space_names[i]);
            for (size_t j = 0; j < 6; ++j) {
                auto * vletinfo = vi->add_vlet_info();
                vletinfo->set_shard_index((uint32_t)j);
                base::EndPoint ep;
                ASSERT_EQ(0, common::str2endpoint("127.0.0.1:1234", &ep));
                if (j == 0) {
                    vletinfo->set_node_addr(0);
                } else {
                    vletinfo->set_node_addr(common::endpoint2int(ep) + j);
                }
                if (j == 1) {
                    vletinfo->set_state(VLET_STATE_REPAIRING);
                } else {
                    vletinfo->set_state(VLET_STATE_NORMAL);
                }
            }
            _volumes.push_back(vi);

            auto * si = new aries::pb::SpaceInfo();
            si->set_space_name(g_space_names[i]);
            si->set_ec_type(EC_RS_ISAL);
            si->set_vlet_type(VLET_TYPE_LINKED_16G_1M_512K);
            si->set_k(4);
            si->set_n(6);
            si->set_put_quorum(5);
            si->set_delete_quorum(3);
            si->set_allocator_collect_quorum(4);
            _spaces.push_back(si);
        }
        // let volume2 index duplicated (vlet1 and vlet5)
        _volumes[2]->mutable_vlet_info(5)->set_shard_index(1u);
        // let volume1 index overflow
        _volumes[1]->mutable_vlet_info(0)->set_shard_index(1000u);
    }

private:
    static bool _let_init_failed;
    static bool _let_find_volume_failed;
    static int  _volume_count;
    static std::vector<aries::pb::MetaReplica::VolumeInfo*> _volumes;
    static std::vector<aries::pb::SpaceInfo*> _spaces;
    static common::MutexLock _mutex;
};

bool MockMetaClient::_let_init_failed = false;
bool MockMetaClient::_let_find_volume_failed = false;
int  MockMetaClient::_volume_count = 0;
std::vector<aries::pb::MetaReplica::VolumeInfo*> MockMetaClient::_volumes;
std::vector<aries::pb::SpaceInfo*> MockMetaClient::_spaces;
common::MutexLock MockMetaClient::_mutex;

class MockVolumeScheduler : public VolumeScheduler {
public:
    MockVolumeScheduler() {
        g_meta_client = new MockMetaClient();
    }
};

class VolumeSchedulerTests : public ::testing::Test {
public:
    static void SetUpTestCase() {
        create_volume_managers();
    }
};

/**
 * Test Cases
 */

TEST_F(VolumeSchedulerTests, init) {
    MockVolumeScheduler *vs = new MockVolumeScheduler;

    // 1. meta client init failed
    common::FLAGS_master_address = "http://127.0.0.1:8888";
    MockMetaClient::let_init_failed();
    ASSERT_FALSE(vs->init());

    // 2. init succ
    MockMetaClient::let_init_succ();
    ASSERT_TRUE(vs->init());
}

TEST_F(VolumeSchedulerTests, schedule_volume) {
    MockVolumeScheduler vs;
    FLAGS_schedule_volume_interval_second = 3;

    MockMetaClient::init_volumes_with_spaces();
    MockMetaClient::let_init_succ();
    ASSERT_TRUE(vs.init());
    ASSERT_TRUE(vs.start());

    // list volume succ but no volume
    MockMetaClient::set_volume_count(0);
    ASSERT_TRUE(vs.schedule_volumes());

    // list volumes succ, but get volume info failed
    MockMetaClient::let_find_volume_failed();
    MockMetaClient::set_volume_count(3);
    ASSERT_TRUE(vs.schedule_volumes());
    
    // list volumes succ and enqueue succ
    MockMetaClient::let_find_volume_succ();
    ASSERT_TRUE(vs.schedule_volumes());
    ASSERT_EQ(1, g_volume_diff_manager->_volumes.size());
    for (size_t i = 0; i < 1; ++i) {
        uint64_t vid;
        ASSERT_TRUE(g_volume_diff_manager->dequeue_volume(&vid));
        auto volctx = g_volume_manager->find(vid);
        ASSERT_TRUE(volctx != nullptr);
        ASSERT_EQ(volctx->volume_id, g_volume_ids[i]);
        ASSERT_EQ(volctx->eco.type, EC_RS_ISAL);
        ASSERT_EQ(volctx->eco.param.k, 4);
        ASSERT_EQ(volctx->eco.param.n, 6);
        for (size_t j = 0; j < 6; ++j) {
            if (j == 0) {
                ASSERT_TRUE(volctx->vlets[j] == nullptr);
            } else {
                base::EndPoint ep;
                ASSERT_EQ(0, common::str2endpoint("127.0.0.1:1234", &ep));
                ASSERT_EQ(common::endpoint2int(volctx->vlets[j]->addr), common::endpoint2int(ep) + j);
                if (j == 1) {
                    ASSERT_EQ(volctx->vlets[j]->state, VLET_STATE_REPAIRING);
                } else {
                    ASSERT_EQ(volctx->vlets[j]->state, VLET_STATE_NORMAL);
                }
            }
        }
    }

    vs.stop();
}

}
}

