/**
 * ARIES - A Reliable and Impressive EB-level Storage.
 * Copyright (c) 2016 Baidu.com, Inc. All Rights Reserved.
 *
 * Author: <PERSON> (<EMAIL>)
 * Date: 2016/10/11
 * Desciption: Definition of gflags
 *
 */

#include "repairer/flags.h"

namespace aries {
namespace repairer {

DEFINE_string(token, "", "Token");

DEFINE_string(tinker_address, "bns://", "BNS address of tinkers");
DEFINE_string(validator_address, "bns://", "BNS address of validators");
DEFINE_string(load_balancer, "rr", "Load balance policy");

DEFINE_int32(diff_worker_num, 1, "Count of diff workers");

DEFINE_int32(repair_worker_num, 1, "Count of repair worker");
DEFINE_int32(max_background_repair_num, 12, "Max repair blob num concurrently in backgound");

DEFINE_int32(delete_worker_num, 1, "Count of delete worker");

DEFINE_int64(worker_wait_timespan_ms, 200, "Wait timespan of workers");
DEFINE_int64(worker_request_task_timespan_ms, 1000, "Request-task timespan of workers");

DEFINE_int64(blob_fresh_timespan, 300, "Blob fresh timespan in seconds");

DEFINE_int32(fast_timeout_ms, 2000, "Fast timeout for call in ms for RepairWorker and DeleteWorker");

DEFINE_uint64(high_level_repair_priority, 2,
        "Repairing-task whose priority higher than this priority will use HIGH qos level");

DEFINE_bool(need_to_check_key_meta, true, "when repair a blob, need to check has key before put");

DEFINE_double(max_diff_volume_num_per_second, 2,
                "Count of max volume num to diff in one second, used to limit diff worker speed");
DEFINE_double(max_repair_blob_num_per_second, 500,
                "Count of max blob num to repair in one second, used to limit repair worker speed");
DEFINE_int32(max_repair_blob_flow_kb, 100 * 1000,
                "Count of max blob num to repair in and out flow, used to limit repair worker speed");
DEFINE_double(max_delete_blob_num_per_second, 20,
                "Count of max blob num to delete in one second, used to limit delete worker speed");

DEFINE_int32(repair_last_check_all_time_second, 7*24*3600, "repair worker last time check all slots.");
DEFINE_int32(delete_last_check_all_time_second, 3*24*3600, "delete worker last time check all slots.");
DEFINE_int32(diff_last_check_all_time_second, 3*24*3600, "diff worker last time check all slots.");
DEFINE_int32(vlet_set_normal_max_need_repaired_blob_num, 2,
        "if need repairered blob num less than this, set vlet status to normal");

DEFINE_int32(max_blob_task_num, 300000, "max blob task memory can hold.");
DEFINE_int32(avoid_fresh_blob_interval_second, 10, "time between list fingerprint and list index to avoid new written blob fluence.");
DEFINE_uint64(delay_delete_interval_second, 7*24*3600, "delayed deletion interval, defalut 3 days, but max is 7 days");

DEFINE_int32(per_batch_repair, 20, "blob num a batch");
DEFINE_int32(vlet_abnormal_response_time_ms, 1000, "excepted dn response time");
DEFINE_int32(vlet_abnormal_count, 5, "when a dn has been excepted for such times, it will be kicked out next time");
DEFINE_double(vlet_abnormal_count_reduction_ratio, 0.75, "when a dn recover normal from abnormal, the abnormal count is reduced");
DEFINE_int32(max_background_batch_repair_num, 3, "Max repair blob num concurrently in backgound");

DEFINE_int32(vlet_request_max_timeout_ms, 64000, "a request max timeout time");

DEFINE_bool(limit_diff_speed, false, "when diff blob list, limit diff speed");

// checkworker
DEFINE_string(checkcenter_address, "bns://", "BNS address of checkercenter");
DEFINE_string(volume_service_address, "bns://", "BNS address of volumeservices");
DEFINE_string(volume_service_load_balancer, "rr", "load balancer of accessing VolumeService");
DEFINE_int32(query_meta_timeout_ms, 500, "query volume service time out");

DEFINE_int32(check_manager_num, 2, "Count of check workers, 500 for random mode");

DEFINE_double(max_check_blob_num_per_second, 10,
        "Count of max blob num to check in one second, used to limit check worker speed, 500000 for random mode");
DEFINE_int64(max_check_blob_flow_kb, 50 * 1000,
        "Count of max out and in web flow to check, used to limit check worker speed, 500 * 1000 for random mode");
DEFINE_int64(check_blob_retry_interval_s, 3600, "retry interval if check blob fail");
DEFINE_int32(check_blob_max_retry_times, 5, "max blob check retry times");
DEFINE_int32(max_background_check_num, 20, "Max check blob num concurrently in background, 10000 for random mode");
DEFINE_int32(max_check_blob_num_per_worker_second, 20, "Max check blob num per worker concurrently in background");
DEFINE_bool(need_check_data, true, "need check data");
DEFINE_int32(max_check_blob_num_per_volume, 1000, "Max blob num to be checked per volume.");
DEFINE_bool(random_check, false, "random check");

DEFINE_int32(max_report_bad_blob_num, 500, "only report max bad blob num");

DEFINE_bool(enable_repair_meta, false, "enable repair meta");
// compressor
DEFINE_int32(compress_priority_level_num, 4, "priority exector support max priority level num, 0 is highest level");
DEFINE_int32(compress_thread_num_in_pool, 1, "compress thread num in priority exector thread pool");
DEFINE_int32(compress_max_task_in_pool, 4, "max compress task num in pool, reached it task will return BUSY");
DEFINE_int32(thread_num_on_brunsli, 4, "thread num on brunsli compressor");
DEFINE_bool(recheck_compress, true, "do decompress right now when compress finished");
DEFINE_int32(check_hung_interval_s, 60, "compress check hung interval second");
DEFINE_int32(compress_hung_second, 60, "compress hung if wait xxx second");

//lostshard checkworker
DEFINE_int32(list_blob_timeout_ms, 30000, "list blob timeout");
DEFINE_int32(lost_shard_max_check_blob_num_per_second, 20, "Max check blob num concurrently in background");
DEFINE_int32(lost_shard_check_worker_num, 2, "Count of check workers, 500 for random mode");

//check batch
DEFINE_bool(enable_check_blob_batch, false, "enable check blob batch");
DEFINE_int32(max_block_read_num_per_second, 40, "block read qps per job");
DEFINE_int32(max_shard_check_num_per_second, 4000, "shard check qps per job");
DEFINE_int32(check_shard_max_wait_time_s, 10800, "check shard wait times");

// qps limitation param
DEFINE_uint64(max_busy_blob_num_per_volume, 10, "max busy blob num by iops per volume, repairer will report fail if more than this num");
DEFINE_double(speed_coefficient_of_repair_task, 0.4, "coefficient of repair speed limit, [0.1, 1]");
DEFINE_double(speed_coefficient_of_delete_task, 0.3, "coefficient of delete speed limit, [0.1, 1]");

DEFINE_string(cluster_name, "", "cluster sign used for validator");
}
}
