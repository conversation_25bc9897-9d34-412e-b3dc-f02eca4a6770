/**
 * ARIES - A Reliable and Impressive EB-level Storage.
 * Copyright (c) 2016 Baidu.com, Inc. All Rights Reserved.
 *
 * Author: <PERSON> <PERSON> (guo<PERSON>@baidu.com)
 * Date: 2016/10/18
 * Desciption: Declaration of repairer diff worker
 *
 */

#ifndef BAIDU_INF_ARIES_REPAIRER_DIFF_WORKER_H
#define BAIDU_INF_ARIES_REPAIRER_DIFF_WORKER_H

#include <atomic>
#include <thread>
#include <bthread.h>
#include <bthread_unstable.h>
#include "baidu/inf/aries-api/common/common.h"
#include "baidu/inf/aries-api/common/types.h"
#include "baidu/inf/aries-api/common/lock.h"
#include "baidu/inf/aries-api/common/utils.h"
#include "baidu/inf/aries/repairer/global.h"
#include "baidu/inf/aries/repairer/diff_base.h"
#include "baidu/inf/aries-api/common/proto/tinker.pb.h"
#include "baidu/inf/aries-api/common/proto/datanode.pb.h"
#include "baidu/inf/aries-api/common/proto/validator.pb.h"

namespace aries {
namespace common {
    class TokenPool;
}
namespace repairer {

struct VolumeDiffTask {
    aries::pb::TinkerTaskId taskid;
    std::shared_ptr<VolumeCheckContext> volume_ctx;
    VolumeDiffTask() : volume_ctx(nullptr) {

    }
    void clear() {
        taskid.set_task_begin_timestamp(0);
        taskid.set_random(0);
        volume_ctx = nullptr;
    }
};

class DiffWorker {
public:
    DiffWorker(common::TokenPool* token_pool);
    virtual ~DiffWorker();

    bool start();
    void stop();

private:
    bool report_volume_diff_result();
    bool fetch_volume_diff_task();

    void thread_func();

private:
    base::EndPoint get_data_service_addr(const base::EndPoint& control_addr) {
#if defined(_UNIT_TEST) || defined(_UNITTEST)
        return control_addr;
#else
        return common::get_data_service_addr(control_addr);
#endif
    }

private:
    std::thread _worker_thread;
    std::atomic<bool> _is_stopped;
    std::unique_ptr<VolumeDiffTask> _task;
    common::TokenPool* _token_pool; //alloc outside
    std::unique_ptr<DiffManager> _diff_manager;
    common::MutexLock _mutex;

// for unittest
private:
    virtual int last_error() {
        return _last_error;
    }
    int _last_error;
};

}
}

#endif
