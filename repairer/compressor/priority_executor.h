// Copyright (c) 2019 Baidu.com, Inc. All Rights Reserved.
// Author: men<PERSON><PERSON><PERSON><PERSON>@baidu.com
// Data: Mon Oct 28 19:08:26 CST 2019
// Filename: priority_executor.h

#pragma once

#include <bthread_unstable.h>
#include <baidu/rpc/server.h>
#include "baidu/inf/aries-api/common/proto/data_agent.pb.h"
#include "baidu/inf/aries-api/common/lock.h"
#include "baidu/inf/aries-api/common/common.h"
#include "baidu/inf/aries/common/threadpool/threadpool_with_task_priority.h"

namespace aries {
namespace repairer {
namespace compressor {

class PriorityExecutor;

struct IPrioriExecutorContext {
    uint64_t log_id = 0;
    uint64_t timeout_us = 0;
    uint64_t en_queue_time = 0;
    uint32_t priority = 0;
};

struct CompressContext : public IPrioriExecutorContext {
    CompressOption compress_option;
    const char* input;
    ssize_t input_size;
    char* dest;
    int64_t max_out_size;
    int64_t real_out_size;
};

struct PriorityExecutorConf {
    PriorityExecutorConf() :
        thread_num_in_pool(1), 
        priority_level_num(4), 
        max_task_in_pool(4),
        thread_num_on_brunsli(4),
        recheck_compress(true),
        check_hung_interval_s(60),
        compress_hung_second(60),
        token("default_token") {}
    PriorityExecutorConf& operator=(const PriorityExecutorConf& conf) {
        thread_num_in_pool.store(conf.thread_num_in_pool);
        priority_level_num.store(conf.priority_level_num);
        token = conf.token;
        reload(conf);
        return *this;
    }
    void reload(const PriorityExecutorConf& conf) {
        max_task_in_pool.store(conf.max_task_in_pool);
        thread_num_on_brunsli.store(conf.thread_num_on_brunsli);
        recheck_compress.store(conf.recheck_compress);
        check_hung_interval_s.store(conf.check_hung_interval_s);
        compress_hung_second.store(conf.compress_hung_second);
    }
    std::atomic<uint32_t> thread_num_in_pool;
    std::atomic<uint32_t> priority_level_num;
    std::atomic<uint32_t> max_task_in_pool;
    std::atomic<uint32_t> thread_num_on_brunsli;
    std::atomic<bool> recheck_compress;
    std::atomic<uint32_t> check_hung_interval_s;
    std::atomic<uint32_t> compress_hung_second;
    std::string token;
};

class PriorityExecutor {
public:
    PriorityExecutor() {}
    ~PriorityExecutor() {}
    int init(const PriorityExecutorConf& conf) {
        _conf = conf;
        return 0;
    }
    int reload(const PriorityExecutorConf& conf) {
        _conf.reload(conf);
        return 0;
    }
    int start();
    int stop();
    const PriorityExecutorConf& conf() {
        return _conf;
    }
    static void on_timer(void* args);
    base::Status compress(const std::shared_ptr<CompressContext> ctx);
    base::Status decompress(const std::shared_ptr<CompressContext> ctx);
protected:
    bthread_timer_t _timer;
    bool is_hung();
private:
    common::threadpool::ThreadpoolWithTaskPriority _thread_pool;
    PriorityExecutorConf _conf;
    std::atomic<uint32_t> _hung_count{0};
};

extern PriorityExecutor* g_priority_executor;

}
}
}
