// Copyright (c) 2019 Baidu.com, Inc. All Rights Reserved.
// Author: men<PERSON><PERSON><PERSON><PERSON>@baidu.com
// Data: Mon Oct 28 19:08:26 CST 2019
// Filename: compress_service.h

#pragma once

#include "baidu/inf/aries-api/common/proto/data_agent.pb.h"

namespace aries {
namespace repairer {
namespace compressor {

class CompressServiceImpl : public ::aries::pb::CompressService {
public:
    CompressServiceImpl();
    virtual ~CompressServiceImpl() {}
    virtual void compress(::google::protobuf::RpcController* controller,
            const ::aries::pb::CompressRequest* request,
            ::aries::pb::CompressResponse* response,
            ::google::protobuf::Closure* done);

    virtual void decompress(::google::protobuf::RpcController* controller,
            const ::aries::pb::DecompressRequest* request,
            ::aries::pb::DecompressResponse* response,
            ::google::protobuf::Closure* done);
};

}
}
}
