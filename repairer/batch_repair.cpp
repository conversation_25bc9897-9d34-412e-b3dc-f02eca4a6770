/*************************************************************************
 *
 * Copyright (c) Baidu.com, Inc. All Rights Reserved
 *
 ************************************************************************/

/**
 * @file repairer/batch_repair.cpp
 * <AUTHOR>
 * @date Thu 24 Jan 2019 04:20:02 PM CST
 * @brief 
 *
 **/

#include "base/logging.h"
#include "baidu/inf/aries/repairer/batch_repair.h"
#include <map>
#include <unordered_map>
#include <vector>

namespace aries {
namespace repairer {

typedef std::pair<uint32_t, uint32_t> PAIR;
bool cmp_by_value(const PAIR& lhs, const PAIR& rhs) {
    if (lhs.second > rhs.second) {
        return true;
    } else if (lhs.second == rhs.second) {
        return lhs.first <= rhs.first;
    }
    return false;
}

uint32_t SimilarCalculator::get_similar(std::map<uint32_t, std::vector<uint64_t>>& locations,
                uint32_t index,
                uint32_t k,
                std::vector<int>* vlet_priority) {
    std::unordered_map<uint64_t, uint32_t> lorder;
    for (size_t i = 0; i < locations[index].size(); ++i) {
        lorder[locations[index][i]] = i;
    }

    uint32_t ret = 0;
    std::map<uint32_t, uint32_t> similars;
    similars[index] = INT_MAX >> 1;
    for (auto &pair : locations) {
        if (pair.first == index) {
            continue;
        }
        if (_similars[pair.first][index] != -1) {
            similars[pair.first] = _similars[pair.first][index];
            _similars[index][pair.first] = _similars[pair.first][index];
            continue;
        }

        uint32_t cur = 0;
        int cur_similar = 0;
        for (size_t i = 0; i < pair.second.size(); ++i) {
            if (lorder.find(pair.second[i]) == lorder.end()) {
                ++cur;
                continue;
            }
            auto order = lorder[pair.second[i]];
            if (order == cur + 1) {
                ++cur_similar;
            }
            cur = order;
        }
        similars[pair.first] = cur_similar;
        _similars[index][pair.first] = cur_similar;
    }
    std::vector<PAIR> helper_similars(similars.begin(), similars.end());
    std::sort(helper_similars.begin(), helper_similars.end(), cmp_by_value);
    uint32_t i = 0;
    uint32_t max_uint32 = -1;
    for (auto &pair: helper_similars) {
        if (i < k) {
            (*vlet_priority)[pair.first] = 1;
            assert(ret < max_uint32 - pair.second);
            ret += pair.second;
        } else {
            (*vlet_priority)[pair.first] = 0;
        }
        ++i;
    }
    return ret;
}

int best_vlet(std::shared_ptr<GetLocationContext> location_ctx,
            std::vector<uint64_t>* vbid_list,
            std::vector<int>* vlet_priority) {
    // prepare vbids list
    std::map<uint32_t, std::vector<uint64_t>> locations;
    locations.swap(location_ctx->vbids);

    // find best vlet
    uint32_t max = 0;
    int max_index = 0;
    SimilarCalculator calculator(location_ctx->n);
    std::vector<int> tmp_vlet_priority;
    tmp_vlet_priority.resize(location_ctx->n);
    for (auto &pair : locations) {
        auto similar = calculator.get_similar(locations, pair.first, location_ctx->k, &tmp_vlet_priority); 
        if (max < similar) {
            max = similar;
            max_index = pair.first;
            for (size_t i = 0; i < tmp_vlet_priority.size(); ++i) {
                (*vlet_priority)[i] = tmp_vlet_priority[i];
            }
        }
    }

    // record standard order
    (*vbid_list).swap(locations[max_index]);

    return max_index;
}

uint64_t ewma(uint64_t old_time, uint64_t new_time) {
    return old_time * 0.1 + 0.9 * new_time;
}

}
}

