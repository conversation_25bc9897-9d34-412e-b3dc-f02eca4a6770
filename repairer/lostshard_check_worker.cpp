/***************************************************************************
 * 
 * Copyright (c) 2020 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/
 
 /**
 * @file lostcheck_worker.cpp
 * <AUTHOR>
 * @date 2020/05/06 18:58:37
 * @version 1.0 
 * @brief Definition of LostShardCheckWorker
 *  
 **/

#include "baidu/inf/aries/repairer/lostshard_check_worker.h"
#include "baidu/inf/aries/repairer/flags.h"

namespace aries {
namespace repairer {

extern baidu::rpc::Channel g_checkcenter_channel;

LostShardCheckWorker::LostShardCheckWorker(common::TokenPool* token_pool) : _token_pool(token_pool), _running(false) {
    _volctx = std::make_shared<aries::checker::VolumeCheckContext>();
    LOG(NOTICE) << "lostcheck worker " << std::hex << this << " created";
}

LostShardCheckWorker::~LostShardCheckWorker() {
    LOG(NOTICE) << "lostcheck worker " << std::hex << this << " destroyed";
}

bool LostShardCheckWorker::start() {
    _running = true;

    //start worker thread
    _worker_thread = std::thread(&LostShardCheckWorker::thread_func, this);
    LOG(WARNING) << "LostCheckerWorker:" << std::hex << this << " start";
    return true;
}

void LostShardCheckWorker::stop() {
    _running = false;
    LOG(WARNING) << "LostCheckerWorker begin to stop";
    _worker_thread.join();
    LOG(WARNING) << "LostCheckerWorker stop success";
}

void LostShardCheckWorker::thread_func() {
    while (_running) {
        if (!_token_pool->take(0)) {
            usleep(FLAGS_worker_wait_timespan_ms * 1000);
            continue;
        }
        //get volume
        bool ret = fetch_volume_lostcheck_task();
        if (!ret) {
            LOG(WARNING) << "fetch_volume_lostcheck_task failed";
            usleep(FLAGS_worker_wait_timespan_ms * 600 * 1000);
            continue;
        }
        _token_pool->get_force(1);
        list_blobs();
        auto lost_blobs = find_lost_shards();
        report_volume_lostcheck_results(lost_blobs);
    }
}

bool LostShardCheckWorker::fetch_volume_lostcheck_task() {
    _volctx.reset(new aries::checker::VolumeCheckContext);
    aries::pb::FetchLostShardCheckVolumesRequest request;
    aries::pb::FetchLostShardCheckVolumesResponse response;
    uint64_t lostcheckworker = common::endpoint2int(common::get_local_addr());
    lostcheckworker |= ((uint64_t)worker_id() << 16);
    request.set_req_addr(lostcheckworker);
    request.set_token(FLAGS_token);

    RpcCallOptions options;
    options.need_retry = false;
    SynchronizedClosure closure;

    LostShardCheckerStub stub;
    stub.fetch_lost_check_volumes(base::EndPoint(), &request, &response, &closure, &options, &g_checkcenter_channel);
    closure.wait();

    if (response.status().code() != AIE_OK) {
        return false;
    }

    if (!get_volume(response.volume_id())) {
        _volctx->check_failed = true;
        LOG(WARNING) << "fetch volume for lostcheck failed, because of get volume info failed";
        return false;
    }

    LOG(NOTICE) << "recv task for check volume, vid:" << response.volume_id();
    return true;
}

bool LostShardCheckWorker::get_volume(uint64_t volume_id) {
    VolumeInfo volume_info;
    uint64_t log_id = base::fast_rand();
    int ret = MetaHandler::instance().get_volume(log_id, volume_id, volume_info);
    if (ret != 0) {
        LOG(WARNING) << "get volume info failed, logid:" << log_id << " vid:" << volume_id << " ret:" << ret;
        return false;
    }

    _volctx->volume_id = volume_info.volume_id;
    _volctx->eco = volume_info.eco;
    _volctx->vlets.resize(volume_info.eco.param.n);

    for (std::size_t i = 0; i < volume_info.shards.size(); i++) {
        auto shard = volume_info.shards[i];
        auto vlet_ctx = std::make_shared<aries::checker::VolumeCheckContext::VletCtx>();
        vlet_ctx->shard_index = shard.shard_index;
        vlet_ctx->addr = shard.addr;
        _volctx->vlets[i] = vlet_ctx;
    }

    return true;
}

void LostShardCheckWorker::list_blobs() {
    if (_volctx->check_failed) {
        return;
    }

    // list blobs from datanode
    _volctx->mutex->lock();
    _volctx->init_counters();
    _volctx->log_id = base::fast_rand();
    uint64_t log_id = _volctx->log_id;
    ARIES_RPC_LOG(NOTICE) << "list blobs, vid:" << std::dec << _volctx->volume_id;

    for (size_t i = 0; i < _volctx->vlets.size(); i++) {
        auto vlet = _volctx->vlets[i];
        if (vlet->addr == common::int2endpoint(0)) {
            continue;
        }
        vlet->is_timeout = false;
        vlet->is_failed = false;
        aries::pb::ListBlobResponse* response = new aries::pb::ListBlobResponse;
        aries::pb::ListBlobRequest* request = new aries::pb::ListBlobRequest;
        request->set_token(FLAGS_token);
        request->set_volume_id(_volctx->volume_id);
        request->set_shard_index(vlet->shard_index);

        ::google::protobuf::Closure* done = ::baidu::rpc::NewCallback(this,
                &LostShardCheckWorker::list_blobs_done, request, response, _volctx);
        
        DataNodeStub stub;
        base::EndPoint data_addr = this->get_data_service_addr(vlet->addr);
        RpcCallOptions options;
        options.log_id = _volctx->log_id;
        options.call_timeout_ms = FLAGS_list_blob_timeout_ms;
        stub.list_blob(data_addr, request, response, done, &options);
        ++_volctx->pending_vlet_num;
    }
    _volctx->sync_point = std::shared_ptr<common::SyncPoint>(new common::SyncPoint(_volctx->pending_vlet_num));
    _volctx->mutex->unlock();

    _volctx->sync_point->wait();
    _volctx->sync_point.reset();
    LOG(NOTICE) << "list blobs finished,"
        << " vid:" << _volctx->volume_id
        << " k:" << _volctx->eco.param.k << " n:" << _volctx->eco.param.n
        << " succ_vlets:" << (int)_volctx->succ_vlet_num
        << " failed_vlets:" << (int)_volctx->failed_vlet_num
        << " timeout_vlets:" << (int)_volctx->timeout_vlet_num;
}

std::vector<std::shared_ptr<aries::pb::BlobLostShardInfo>> LostShardCheckWorker::find_lost_shards() {
    std::vector<std::shared_ptr<aries::pb::BlobLostShardInfo>> lost_blobs;
    if (_volctx->check_failed) {
        return lost_blobs;
    }

    // when vlet is invalid, mock blobs and iterator
    std::set<uint64_t> dummy_set;

    // 1. prepare blob list
    std::vector<std::set<uint64_t>* > blobs;
    std::vector<std::set<uint64_t>::iterator> its;
    for (size_t i = 0; i < _volctx->vlets.size(); ++i) {
        auto vlet = _volctx->vlets[i];
        if (vlet->addr != common::int2endpoint(0) && !vlet->is_timeout && !vlet->is_failed) {
            auto it = vlet->blob_lists.find(0);
            assert(it != _volctx->vlets[i]->blob_lists.end());
            std::set<uint64_t>* s = it->second;
            assert(s != nullptr);
            blobs.push_back(s);
            std::set<uint64_t>::iterator bit = s->begin();
            its.push_back(bit);
        } else {
            // vlet is invalid, mock blobs and iterator
            blobs.push_back(&dummy_set);
            std::set<uint64_t>::iterator bit = dummy_set.end();
            its.push_back(bit);
        }
    }

    // 2. do diff
    uint64_t next_vbid = UINT64_MAX;
    uint64_t curr_vbid = 0;
    
    if (!find_next_vbid(blobs, its, &curr_vbid)) {
        LOG(WARNING) << "the volume has no blob, skip this vid:" << _volctx->volume_id;
        return lost_blobs;
    }

    // init cursor & mark
    // meanings of mark:
    //  1 : shard exist
    //  0 : shard not exist, but vlet exist
    // -1 : vlet not exist
    uint32_t cursor = 0;
    std::vector<int> mark(_volctx->eco.param.n, -1);
    std::set<uint32_t> ended_set;
    aries::pb::BlobLostShardInfo blob;
    while (cursor < _volctx->vlets.size()) {
        if (ended_set.size() >= its.size()) {
            break;
        }

        uint64_t vbid = 0;
        if (its[cursor] != blobs[cursor]->end()) {
            vbid = *(its[cursor]);
        } else {
            vbid = UINT64_MAX;
            ended_set.insert(cursor);
        }

        if (vbid == curr_vbid) {
            mark[_volctx->vlets[cursor]->shard_index] = 1;
            ++its[cursor];
            if (its[cursor] != blobs[cursor]->end() && *(its[cursor]) < next_vbid) {
                next_vbid = *(its[cursor]);
            }
        } else {
            assert(vbid > curr_vbid);
            mark[_volctx->vlets[cursor]->shard_index] = 0;
            if (vbid < next_vbid) {
                next_vbid = vbid;
            }
        }

        if (++cursor >= its.size()) {

            if (_volctx->mark_deleted_blobs.find(curr_vbid) == _volctx->mark_deleted_blobs.end()) {
                std::shared_ptr<aries::pb::BlobLostShardInfo> blob(new aries::pb::BlobLostShardInfo);
                blob->set_vbid(curr_vbid);
                for (size_t i = 0; i < mark.size(); ++i) {
                    if (mark[i] != 1) {
                        auto shard = blob->add_shard_list();
                        shard->set_index(i);
                    }
                }
                // has lost shards
                if (blob->shard_list_size() > 0) {
                    lost_blobs.push_back(blob);
                }
                blob.reset();
            }

            if (next_vbid == UINT64_MAX) {
                if (!find_next_vbid(blobs, its, &curr_vbid)) {
                    break;
                }
            } else {
                curr_vbid = next_vbid;
                next_vbid = UINT64_MAX;
            }

            cursor = 0;
            mark.assign(_volctx->eco.param.n, -1);
        }
    }

    return lost_blobs;
}

bool LostShardCheckWorker::find_next_vbid(const std::vector<std::set<uint64_t>* >& blobs,
        const std::vector<std::set<uint64_t>::iterator>& its,
        uint64_t *vbid) {
    *vbid = UINT64_MAX;
    for (size_t i = 0; i < its.size(); ++i) {
        if (its[i] == blobs[i]->end()) {
            continue;
        }
        uint64_t bid = *(its[i]);
        if (bid < *vbid) {
            *vbid = bid;
        }
    }

    return *vbid != UINT64_MAX;
}

bool LostShardCheckWorker::report_volume_lostcheck_results(
        std::vector<std::shared_ptr<aries::pb::BlobLostShardInfo>>& lost_blobs) {
    aries::pb::ReportLostShardCheckResultsRequest request;
    aries::pb::AckResponse response;
    request.set_req_addr(common::endpoint2int(common::get_local_addr()));
    request.set_token(FLAGS_token);
    request.set_volume_id(_volctx->volume_id);
    if (_volctx->check_failed) {
        request.set_is_succ(false);
    } else {
        request.set_is_succ(true);
        for (auto lost : lost_blobs) {
            auto lost_blob = request.add_lost_blobs();
            lost_blob->CopyFrom(*lost);
        }
    }

    RpcCallOptions options;
    options.need_retry = false;
    SynchronizedClosure closure;

    LostShardCheckerStub stub;
    stub.report_lost_check_results(base::EndPoint(), &request, &response, &closure, &options, &g_checkcenter_channel);
    closure.wait();

    LOG(NOTICE) << __FUNCTION__
                << " report vid:" << _volctx->volume_id 
                << " lost check result to checkcenter: "
                << (response.status().code() == AIE_OK);

    _volctx.reset();
    return true;
}

void LostShardCheckWorker::list_blobs_done(aries::pb::ListBlobRequest* request, aries::pb::ListBlobResponse* response,
              std::shared_ptr<aries::checker::VolumeCheckContext> volctx) {
    assert(request);
    assert(response);
    assert(volctx.get());
    std::unique_ptr<aries::pb::ListBlobRequest> request_guard(request);
    std::unique_ptr<aries::pb::ListBlobResponse> response_guard(response);

    uint64_t volume_id = request->volume_id();
    assert(volume_id == volctx->volume_id);

    {
        volctx->mutex->lock();

        int shard_index = request->shard_index();
        assert(shard_index >= 0 && shard_index < (int)volctx->vlets.size());
        auto vlet = volctx->vlets[shard_index];
        base::EndPoint data_addr = this->get_data_service_addr(vlet->addr);
        uint64_t log_id = volctx->log_id;

        int code = response->status().code();
        if (code == AIE_TIMEOUT) {
            ARIES_RPC_LOG(WARNING) << "list blobs failed from addr "
                << common::endpoint2str(data_addr)
                << " vid:" << volume_id << " shard_index:" << shard_index
                << " error:" << code << " (" << response->status().msg() << ")";
            vlet->is_timeout = true;
            ++volctx->timeout_vlet_num;
            _volctx->check_failed = true;
        } else if (code != AIE_OK) {
            ARIES_RPC_LOG(WARNING) << "list blobs failed from addr "
                << common::endpoint2str(data_addr)
                << " vid:" << volume_id << " shard_index:" << shard_index
                << " error:" << code << " (" <<  response->status().msg() << ")";
            vlet->is_failed = true;
            ++volctx->failed_vlet_num;
            _volctx->check_failed = true;
        } else {
            ARIES_RPC_LOG(NOTICE) << "list blobs succeeded from addr "
                << common::endpoint2str(data_addr)
                << " vid:" << volume_id << " shard_index:" << shard_index;
            ++volctx->succ_vlet_num;

            vlet->version = response->vlet_version();

            if (volctx->max_vbid < response->max_vbid()) {
                volctx->max_vbid = response->max_vbid();
            }

            // aggregate deleted blob
            for (int i = 0; i < response->mark_deleted_vbid_list_size(); ++i) {
                uint64_t vbid = response->mark_deleted_vbid_list(i);
                record_mark_deleted_blob(vbid, volctx);
            }

            std::set<uint64_t> *blobs = new std::set<uint64_t>;
            for (int i = 0; i < response->vbid_list_size(); ++i) {
                blobs->insert(response->vbid_list(i));
            }

            // only a slot, include all vbid
            bool ok = vlet->blob_lists.insert(std::make_pair(0, blobs)).second;
            assert(ok);
        }

        volctx->mutex->unlock();
    }

    volctx->sync_point->signal();
}

void LostShardCheckWorker::record_mark_deleted_blob(uint64_t vbid,
        std::shared_ptr<aries::checker::VolumeCheckContext> volctx) {
    assert(volctx != NULL);

    auto it = volctx->mark_deleted_blobs.find(vbid);
    if (it == volctx->mark_deleted_blobs.end()) {
        bool ok = volctx->mark_deleted_blobs.insert(std::make_pair(vbid, nullptr)).second;
        assert(ok);
        it = volctx->mark_deleted_blobs.find(vbid);
        assert(it != volctx->mark_deleted_blobs.end());
    }
}

}
}
/* vim: set ts=4 sw=4 sts=4 tw=100 */
