/*************************************************************************
 *
 * Copyright (c) Baidu.com, Inc. All Rights Reserved
 *
 ************************************************************************/

/**
 * @file repairer/test/test_repairer.cpp
 * <AUTHOR>
 * @date Mon 24 Dec 2018 04:54:45 PM CST
 * @brief 
 *
 **/

#include <gtest/gtest.h>
#include "baidu/inf/aries/repairer/repairer.h"
#include "baidu/inf/aries/repairer/flags.h"
#include "baidu/inf/aries-api/common/master_tracker.h"
#include "baidu/inf/aries-api/common/config.h"
#include "bmock.h"

using ::testing::Return;
using ::testing::_;

namespace aries {
namespace repairer {

class RepairerTests : public ::testing::Test {

};

TEST_F(RepairerTests, start_and_stop) {
    common::FLAGS_master_address = "127.0.0.1:65535";
    FLAGS_tinker_address = "bns://127.0.0.1:62345";
    common::FLAGS_port = 64350;
    common::FLAGS_interfaces = "lo";
    ASSERT_EQ(0, common::init_local_addr());

    Repairer repairer;
    ASSERT_EQ(0, repairer.start());
    repairer.update_conf();
    repairer.stop();
    repairer.join();
    sleep(3);
}

}
}
