/**
 * ARIES - A Reliable and Impressive EB-level Storage.
 * Copyright (c) 2016 Baidu.com, Inc. All Rights Reserved.
 *
 * Author: <PERSON> (guo<PERSON>@baidu.com)
 * Date: 2016/09/26
 * Description: Unittest for DeleteWorker
 *
 */

#include <gtest/gtest.h>
#include <base/logging.h>
#include <baidu/rpc/server.h>
#include "baidu/inf/aries-api/common/types.h"
#include "baidu/inf/aries-api/common/speed_limit.h"
#include "baidu/inf/aries/repairer/test/mock_datanode.h"
#include "baidu/inf/aries/repairer/global.h"
#include "baidu/inf/aries/repairer/flags.h"
#include "baidu/inf/aries/repairer/delete_worker.h"
#include "baidu/inf/aries/repairer/test/mock_tinker.h"

namespace aries {
namespace repairer {

class MockDataNode {
public:
    void start(int port) {
        ASSERT_EQ(0, base::str2endpoint("127.0.0.1", port, &_listen_addr));
        ASSERT_EQ(0, _server.AddService(&_dn_data_service,
                                baidu::rpc::SERVER_DOESNT_OWN_SERVICE));
        ASSERT_EQ(0, _server.Start(_listen_addr, NULL));
    }
    void stop() {
        _server.Stop(200);
    }
private:
    baidu::rpc::Server _server;
    base::EndPoint _listen_addr;
    MockDataNodeDataService _dn_data_service;
};

class MockTinker {
public:
    void start(int port) {
        ASSERT_EQ(0, base::str2endpoint("127.0.0.1", port, &_listen_addr));
        ASSERT_EQ(0, _server.AddService(&_tinker_service,
                                baidu::rpc::SERVER_DOESNT_OWN_SERVICE));
        ASSERT_EQ(0, _server.Start(_listen_addr, NULL));
        ASSERT_EQ(0, g_tinker_channel.Init(_listen_addr, NULL));
    }
    void stop() {
        _server.Stop(200);
    }
    static void enable_fetch_task() {
        MockTinkerService::enable_fetch_task();
    }
    static void disable_fetch_task() {
        MockTinkerService::disable_fetch_task();
    }

private:
    baidu::rpc::Server _server;
    base::EndPoint _listen_addr;
    MockTinkerService _tinker_service;
};

class TestEnvironment : public ::testing::Environment {
public:
    void SetUp() {
        FLAGS_worker_wait_timespan_ms = 200;
        FLAGS_worker_request_task_timespan_ms = 200;
        FLAGS_call_timeout_ms = 5000;
        start_services();
        g_delete_timeout = 100;
    }
    void TearDown() {
        stop_services();
    }
    void start_services() {
        _tinker.start(g_tinker_port);
        for (int i = 0; i < 9; ++i) {
            _datanodes[i].start(g_datanode_ports[i]);
        }
    }
    void stop_services() {
        _tinker.stop();
        for (int i = 0; i < 9; ++i) {
            _datanodes[i].stop();
        }
    }

private:
    MockTinker _tinker;
    MockDataNode _datanodes[9];
};

class DeleteWorkerTests : public ::testing::Test {
};

TEST_F(DeleteWorkerTests, delete_worker_1) {
    g_delete_last_check_all_time = base::gettimeofday_s();
    MockTinker::enable_fetch_task();
    common::TokenPool* delete_token_pool = new common::TokenPool(3, 10);
    delete_token_pool->start();
    DeleteWorker *worker = new DeleteWorker(delete_token_pool);
    worker->start();
    sleep(3);
    MockTinker::enable_fetch_task();
    g_delete_last_check_all_time = 0;
    sleep(3);
    worker->stop();
    sleep(2);
    delete worker;
}

TEST_F(DeleteWorkerTests, delete_worker_2) {
    g_delete_last_check_all_time = 0;
    g_delete_need_param = true;
    MockTinker::enable_fetch_task();
    g_shard_remove_response_errors[0] = AIE_VOLUME_NOT_EXIST;
    g_shard_remove_response_errors[1] = AIE_VOLUME_NOT_EXIST; 
    g_mark_deleted_vbids[5].push_back(1111);
    common::TokenPool* delete_token_pool = new common::TokenPool(3, 10);
    delete_token_pool->start();
    g_param_manager->delete_token_pool = delete_token_pool;
    DeleteWorker *worker = new DeleteWorker(delete_token_pool);
    worker->start();
    sleep(3);
    worker->stop();
    sleep(2);
    delete worker;
    g_shard_remove_response_errors[0] = AIE_OK;
    g_shard_remove_response_errors[1] = AIE_OK; 
    g_mark_deleted_vbids[5].clear();
    g_delete_need_param = false;
}

TEST_F(DeleteWorkerTests, delete_worker_3) {
    MockTinker::enable_fetch_task();
    g_shard_remove_response_errors[0] = AIE_BLOB_NOT_EXIST;
    g_shard_remove_response_errors[1] = AIE_BLOB_NOT_EXIST; 
    g_mark_deleted_vbids[5].push_back(1111);
    common::TokenPool* delete_token_pool = new common::TokenPool(3, 10);
    delete_token_pool->start();
    DeleteWorker *worker = new DeleteWorker(delete_token_pool);
    worker->start();
    sleep(3);
    worker->stop();
    sleep(2);
    delete worker;
    g_shard_remove_response_errors[0] = AIE_OK;
    g_shard_remove_response_errors[1] = AIE_OK; 
    g_mark_deleted_vbids[5].clear();
}

TEST_F(DeleteWorkerTests, delete_worker_4) {
    MockTinker::enable_fetch_task();
    g_shard_remove_response_errors[2] = AIE_TIMEOUT;
    g_shard_remove_response_errors[3] = AIE_TIMEOUT; 
    g_mark_deleted_vbids[5].push_back(1111);
    common::TokenPool* delete_token_pool = new common::TokenPool(3, 10);
    delete_token_pool->start();
    DeleteWorker *worker = new DeleteWorker(delete_token_pool);
    worker->start();
    sleep((FLAGS_call_timeout_ms + 5000) / 1000);
    worker->stop();
    sleep(2);
    delete worker;
    g_shard_remove_response_errors[2] = AIE_OK;
    g_shard_remove_response_errors[3] = AIE_OK; 
    g_mark_deleted_vbids[5].clear();
}

TEST_F(DeleteWorkerTests, test_final_delete_shards) {
    common::TokenPool* delete_token_pool = new common::TokenPool(3, 10);
    delete_token_pool->start();
    DeleteWorker *worker = new DeleteWorker(delete_token_pool);
    auto ctx = std::shared_ptr<BlobDeleteContext>(new BlobDeleteContext);
    worker->_task.reset(new BlobDeleteTask);
    worker->_task->volume_ctx = std::make_shared<VolumeCheckContext>();
    worker->_task->volume_ctx->volume_id = 9; 
    auto ok = worker->final_delete_shards(ctx);
    ASSERT_TRUE(ok);

    delete worker;
}

TEST_F(DeleteWorkerTests, test_delete_blob) {
    // only mark delete, return true
    common::TokenPool* delete_token_pool = new common::TokenPool(3, 10);
    delete_token_pool->start();
    DeleteWorker *worker = new DeleteWorker(delete_token_pool);
    auto ctx = std::shared_ptr<BlobDeleteContext>(new BlobDeleteContext);
    worker->_task.reset(new BlobDeleteTask);
    worker->_task->volume_ctx = std::make_shared<VolumeCheckContext>();
    worker->_task->volume_ctx->volume_id = 9;
    ASSERT_EQ(worker->_task->mark_delete_fail_num, 0);
    ASSERT_EQ(worker->_task->final_delete_fail_num, 0);
    ctx->only_mark_delete = true;
    auto ok = worker->delete_blob(ctx);
    ASSERT_TRUE(ok);
    ASSERT_EQ(worker->_task->mark_delete_fail_num, 0);

    // delete_marked_blob fail, return false
    ctx->vbid = 123;
    ctx->only_mark_delete = false;
    ok = worker->delete_blob(ctx);
    ASSERT_TRUE(ok);
    ASSERT_EQ(worker->_task->final_delete_fail_num, 0);

    delete worker;
}

TEST_F(DeleteWorkerTests, delete_worker_due_to_check_failed) {
    g_delete_last_check_all_time = base::gettimeofday_s();
    g_list_fingerprint_response_errors[0] = AIE_FAIL;
    g_list_fingerprint_response_errors[1] = AIE_FAIL;
    g_list_fingerprint_response_errors[2] = AIE_FAIL;
    g_list_fingerprint_response_errors[3] = AIE_FAIL;

    MockTinker::disable_fetch_task();
    common::TokenPool* delete_token_pool = new common::TokenPool(3, 10);
    delete_token_pool->start();
    DeleteWorker *worker = new DeleteWorker(delete_token_pool);
    worker->start();
    MockTinker::enable_fetch_task();
    sleep(3);
    g_delete_last_check_all_time = 0;
    sleep(3);
    worker->stop();
    sleep(2);
    delete worker;

    // reset assumption
    g_list_fingerprint_response_errors[0] = AIE_OK;
    g_list_fingerprint_response_errors[1] = AIE_OK;
    g_list_fingerprint_response_errors[2] = AIE_OK;
    g_list_fingerprint_response_errors[3] = AIE_OK;
}

TEST_F(DeleteWorkerTests, check_without_task) {
    common::TokenPool* delete_token_pool = new common::TokenPool(3, 10);
    delete_token_pool->start();
    DeleteWorker *worker = new DeleteWorker(delete_token_pool);
    MockTinker::disable_fetch_task();

    worker->start();
    sleep(6);
    worker->stop();
    sleep(1);
    delete worker;
}

}
}

int main(int argc, char* argv[]) {
    ::testing::AddGlobalTestEnvironment(new aries::repairer::TestEnvironment());
    ::testing::InitGoogleTest(&argc, argv);
    return RUN_ALL_TESTS();
}

