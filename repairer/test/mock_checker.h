/**
 * ARIES - A Reliable and Impressive EB-level Storage.
 * Copyright (c) 2018 Baidu.com, Inc. All Rights Reserved.
 *
 * Author: <PERSON> Tianxing (<EMAIL>)
 * Date: 2018/04/25
 * Desciption: Mock checker for test repairer
 *
 */

#ifndef BAIDU_INF_ARIES_REPAIRER_TEST_MOCK_CHECKER_H
#define BAIDU_INF_ARIES_REPAIRER_TEST_MOCK_CHECKER_H

#include <base/time.h>
#include <base/rand_util.h>
#include "baidu/rpc/server.h"
#include "baidu/inf/aries-api/common/proto/checker.pb.h"
#include "baidu/inf/aries-api/common/common.h"
#include "baidu/inf/aries-api/common/types.h"
#include <vector>

namespace aries {
namespace repairer {

static uint16_t g_checker_port = 64389;
static uint16_t g_datanode_ports[9] =
        {61230, 61231, 61232, 61233, 61234, 61235, 61236, 61237, 61238};

static __uint128_t g_blob_ids[3] = {
        make_bid(1024, 1000),
        make_bid(1025, 1001),
        make_bid(1026, 1002)
};

static std::vector<uint64_t> g_volume_ids;
static uint32_t g_volume_id_rank = 0;
static bool g_vlet_losts[9] = {false, false, false, false, false, false, false, false, false};
static int g_check_results_call_time = 0;

static std::vector<::aries::pb::ReportDataCheckResultsRequest> g_check_results;


/*
 * Mock Checkcenter CheckerService
 */
class MockCheckerService : public ::aries::pb::CheckerService {
public:
    MockCheckerService() {}
    virtual ~MockCheckerService() {}

    void fetch_data_check_volumes(::google::protobuf::RpcController* controller,
            const ::aries::pb::FetchDataCheckVolumesRequest* request,
            ::aries::pb::FetchDataCheckVolumesResponse* response,
            ::google::protobuf::Closure* done) {
        baidu::rpc::Controller* cntl = (baidu::rpc::Controller*)controller;
        baidu::rpc::ClosureGuard done_guard(done);
        base::EndPoint req_addr = common::int2endpoint(request->req_addr());

        LOG(NOTICE) << "recv fetch blob fetch data check volume request from "
                << common::endpoint2str(req_addr)
                << " log_id: " << cntl->log_id();

        if (g_volume_id_rank >= g_volume_ids.size()) {
            response->mutable_status()->set_code(AIE_EMPTY);
            response->mutable_status()->set_msg("the waiting check volume queue is empty");
            return;
        }
        uint64_t volume_id = g_volume_ids[g_volume_id_rank++];
        response->set_volume_id(volume_id);
        response->mutable_status()->set_code(AIE_OK);
        response->mutable_status()->set_msg("ok");

        LOG(NOTICE) << "succ to fetch data check volume, log_id: " << cntl->log_id()
                << " volume_id: " << volume_id;
    }

    void report_data_check_results(::google::protobuf::RpcController* controller,
            const ::aries::pb::ReportDataCheckResultsRequest* request,
            ::aries::pb::AckResponse* response,
            ::google::protobuf::Closure* done) {
        baidu::rpc::Controller* cntl = (baidu::rpc::Controller*)controller;
        baidu::rpc::ClosureGuard done_guard(done);
        base::EndPoint req_addr = common::int2endpoint(request->req_addr());

        response->mutable_status()->set_code(AIE_OK);
        response->mutable_status()->set_msg("ok");

        g_check_results.push_back(*request);

        LOG(NOTICE) << "recv report data check results request from "
                << common::endpoint2str(req_addr)
                << " req:" << common::pb2json(*request)
                << " log_id: " << cntl->log_id();
    }
};

}
}

#endif
