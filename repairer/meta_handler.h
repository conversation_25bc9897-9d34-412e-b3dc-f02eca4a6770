/**
 * ARIES - A Reliable and Impressive EB-level Storage.
 * Copyright (c) 2018 Baidu.com, Inc. All Rights Reserved.
 *
 * Author: <PERSON> (<EMAIL>)
 * Date: 2018/04/19
 * Desciption: Declaration of meta_handler
 *
 */
#pragma once

#include "baidu/inf/aries-api/common/common.h"
#include "baidu/inf/aries-api/common/proto/volume_service.pb.h"
#include <baidu/rpc/channel.h>
#include <baidu/rpc/controller.h>
#include <memory>

namespace aries {
namespace repairer {

struct Shard {
    int  shard_index;
    base::EndPoint addr;
};

struct VolumeInfo {
    VolumeInfo(uint64_t value = 0) : volume_id(value) {
        shards.clear();
    }
    uint64_t volume_id;
    std::vector<Shard> shards;
    aries::ECOption eco;
    aries::pb::SpaceInfo space_info;
};

typedef std::shared_ptr<aries::pb::SpaceInfo> SpaceInfoPtr;

class MetaHandler {
public:
    static MetaHandler &instance() {
        return _s_instance;
    };

public:
    MetaHandler() : _vs_client(&_vs_channel) {}

    int init();

    int get_volume(uint64_t log_id, uint64_t volume_id, VolumeInfo& v_info);

private:
    static MetaHandler _s_instance;
private:
    baidu::rpc::Channel _vs_channel;
    aries::pb::volumeservice::VolumeService_Stub _vs_client;
};

}//end of namespace
}
