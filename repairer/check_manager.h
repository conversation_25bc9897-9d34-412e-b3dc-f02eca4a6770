/**
 * ARIES - A Reliable and Impressive EB-level Storage.
 * Copyright (c) 2018 Baidu.com, Inc. All Rights Reserved.
 *
 * Author: <PERSON> Tianxing (<EMAIL>)
 * Date: 2018/04/19
 * Desciption: Declaration of repairer check worker
 *
 */

#ifndef BAIDU_INF_ARIES_REPAIRER_CHECK_MANAGER_H
#define BAIDU_INF_ARIES_REPAIRER_CHECK_MANAGER_H

#include <atomic>
#include <string>
#include <bthread.h>
#include <queue>
#include <bthread_unstable.h>
#include <base/iobuf.h>
#include "baidu/inf/aries-api/common/common.h"
#include "baidu/inf/aries-api/common/types.h"
#include "baidu/inf/aries-api/common/lock.h"
#include "baidu/inf/aries-api/common/utils.h"
#include "baidu/inf/aries-api/common/erasure_code_factory.h"
#include "baidu/inf/aries-api/common/proto/datanode.pb.h"
#include "baidu/inf/aries-api/common/proto/tinker.pb.h"
#include "baidu/inf/aries-api/common/proto/repairer.pb.h"
#include "baidu/inf/aries/repairer/meta_handler.h"

namespace aries {

namespace common {
    class TokenPool;
}

namespace repairer {

struct CheckBlobErrInfo {
    uint64_t vbid;
    std::vector<int8_t> crc_failed_shard_indexes;
    bool code_decode_err;
    bool data_crc_err;
    bool meta_not_equal;

    CheckBlobErrInfo() {
        vbid = 0;
        code_decode_err = false;
        data_crc_err = false;
        meta_not_equal = false;
    }
};

struct VolumeCheckTask {
    std::shared_ptr<common::MutexLock> mutex;
    uint64_t volume_id;
    VolumeInfo volume_info;
    uint32_t blob_count;
    std::atomic<uint32_t> blob_finish_count;
    uint64_t last_get_volume_meta_time;
    std::queue<uint64_t> blob_queue;
    std::vector<std::shared_ptr<CheckBlobErrInfo> > err_blobs;
    std::atomic<bool> is_failed;
    std::atomic<bool> is_finished;

    //add for batch check
    std::atomic<uint32_t> current_check_index;
    std::shared_ptr<common::SyncPoint> sync_point;
    std::vector<aries::pb::ShardCheckedInfo> check_shard_results;
    std::string uuid;

    VolumeCheckTask() : blob_finish_count(0) {
        mutex = std::shared_ptr<common::MutexLock>(new common::MutexLock());
        volume_id = 0;
        blob_count = 0;
        last_get_volume_meta_time = 0;
        is_failed = false;
        is_finished = false;
        current_check_index = 0;
    }
    ~VolumeCheckTask() {
        mutex.reset();
        for (auto & blob : err_blobs) { blob.reset(); }
        err_blobs.clear();
    }
};

struct CheckBlobInfo {
    __uint128_t blob_id;
    std::shared_ptr<VolumeCheckTask> task;
};
typedef std::shared_ptr<CheckBlobInfo> CheckBlobInfoPtr;

struct BlobCheckContext {
    enum GetShardErrCode {
        GSEC_SUCC = 0,
        GSEC_VLET_NOT_EXIST = 1,
        GSEC_TIMEOUT = 2,
        GSEC_FAIL = 3,
        GSEC_DATA_ERR = 4,
        GSEC_PENDING = 5,
    };
    struct ShardCtx {
        int shard_index;
        uint32_t version;
        uint32_t shard_len;
        uint32_t shard_crc;
        char* data;
        base::EndPoint addr;
        GetShardErrCode err_code;
        int need_repaired;
        aries::pb::ShardMeta shard_meta;
        ShardCtx() :
            shard_index(-1), shard_len(0), shard_crc(0), data(NULL), err_code(GSEC_PENDING), need_repaired(0) {}
        ~ShardCtx() {
            if (NULL != data) {
                free(data);
            }
            data = NULL;
        }
    };

    std::shared_ptr<common::MutexLock> mutex;
    __uint128_t blob_id;
    std::shared_ptr<CheckBlobInfo> check_blob_info;
    ECOption eco;
    aries::pb::ShardMeta blob_meta;
    std::vector<std::shared_ptr<ShardCtx>> shards;
    std::map<int, char*> srcs;
    std::map<int, char*> dests;
    char* blob_data;

    // for rpc
    int8_t pending_shard_num;
    int8_t succ_shard_num;
    int8_t failed_shard_num;
    int8_t vlet_not_exist_shard_num;
    int8_t timeout_shard_num;
    bool is_mark_deleted;
    int8_t retry_times;
    uint64_t log_id;
    bool is_meta_equal;
    bool shard_compress_type_is_equal;
    bool shard_shard_key_is_equal;
    bool shard_shard_meta_is_equal;
    bool shard_key_meta_is_null;


    BlobCheckContext() : blob_id(0) {
        mutex = std::shared_ptr<common::MutexLock>(new common::MutexLock());
        is_mark_deleted = false;
        retry_times = 0;
        log_id = UINT64_MAX;
        is_meta_equal = true;
        shard_compress_type_is_equal = true;
        shard_shard_key_is_equal = true;
        shard_shard_meta_is_equal = true;
        shard_key_meta_is_null = false;
        blob_data = NULL;
        init_counters();
    }
    virtual ~BlobCheckContext() {
        mutex.reset();
        free_memory();
        srcs.clear();
        dests.clear();
        for (size_t i = 0; i < shards.size(); ++i) { 
            if (shards[i]) {
                shards[i].reset(); 
            }
        }
        shards.clear();
    }
    void init_counters() {
        pending_shard_num = 0;
        succ_shard_num = 0;
        failed_shard_num = 0;
        vlet_not_exist_shard_num = 0;
        timeout_shard_num = 0;
    }
    bool is_finished() {
        return 0 == pending_shard_num;
    }
    void free_memory() {
        for (auto& pair : srcs) {
            if (NULL != pair.second) {
                free(pair.second);
                pair.second = NULL;
            }
        }
        for (auto& pair : dests) {
            if (NULL != pair.second) {
                free(pair.second);
                pair.second = NULL;
            }
        }
        for (auto& shard : shards) {
            if (!shard) {
                continue;
            }
            if (NULL != shard->data) {
                free(shard->data);
                shard->data = NULL;
            }
        }
        if (NULL != blob_data) {
            free(blob_data);
            blob_data = NULL;
        }
    }
};

class CheckManager {
public:
    CheckManager(common::TokenPool* token_pool, common::TokenPool* flow_pool, bool is_ec_check);
    virtual ~CheckManager();

    bool start();
    void stop();

    void set_worker_id(int16_t id) {
        _worker_id = id;
    }

    void report_check_shard_results(const aries::pb::ReportCheckVletShardsRequest* request);

    static int background_check_num() {
        return _background_check_num;
    }
    static void inc_background_check_num() {
        ++_background_check_num;
    }
    static void dec_background_check_num() {
        --_background_check_num;
    }

private:
    static void* thread_func(void* args);

    bool report_volume_check_results();
    bool fetch_volume_check_task();
    bool prepare_volume_task(std::shared_ptr<VolumeCheckTask> task);
    bool get_blob_list(std::shared_ptr<VolumeCheckTask> task);
    std::shared_ptr<BlobCheckContext> get_next_blob_check_context();

    void check_blob(std::shared_ptr<BlobCheckContext> blobctx);
    void check_blob_each();
    bool get_shards(std::shared_ptr<BlobCheckContext> blobctx);
    bool could_check_shards(std::shared_ptr<BlobCheckContext> blobctx);
    void get_shards_done(aries::pb::ShardGetRequest* request, aries::pb::ShardGetResponse* response,
                        std::shared_ptr<BlobCheckContext> blobctx,
                        base::IOBuf* response_attachment);
    bool refresh_volume_meta(std::shared_ptr<VolumeCheckTask> task);
    bool check_data(std::shared_ptr<BlobCheckContext> blobctx,
                        aries::pb::ShardGetRequest* request, aries::pb::ShardGetResponse* response,
                        const std::string& data);
    bool check_meta(std::shared_ptr<BlobCheckContext> blobctx,
                        aries::pb::ShardGetRequest* request, aries::pb::ShardGetResponse* response);
    void get_shards_done(std::shared_ptr<BlobCheckContext> blobctx);
    bool datanode_check_shard();
    std::shared_ptr<CheckBlobErrInfo> check_shards(std::shared_ptr<BlobCheckContext> blobctx);
    int16_t worker_id() const {
        return _worker_id;
    }

    void try_repair_meta(std::shared_ptr<BlobCheckContext> blobctx);
    bool is_meta_equal(aries::pb::ShardMeta& a, aries::pb::ShardMeta& b);
    void repair_meta(std::shared_ptr<BlobCheckContext> blobctx,
        int blobctx_shard_number, aries::pb::ShardMeta& meta);
    void check_blob_batch();
    bool is_ec_check();

private:
    ARIES_VIRTUAL inline common::ECPtr get_erasure_code(const ECOption& eco) {
        return common::ErasureCodeFactory::get_erasure_code(eco);
    }

    base::EndPoint get_data_service_addr(const base::EndPoint& control_addr) {
#if defined(_UNIT_TEST) || defined(_UNITTEST)
        return control_addr;
#else
        return common::get_data_service_addr(control_addr);
#endif
    }

private:
    int16_t _worker_id;
    std::atomic<bool> _is_stopped;
    bthread_timer_t _timer;
    std::shared_ptr<VolumeCheckTask> _task;
    common::TokenPool* _token_pool;
    common::TokenPool* _flow_pool;
    std::unique_ptr<common::TokenPool> _own_pool;
    static std::atomic<int> _background_check_num;
    pthread_t _thread_id;
    bool _is_ec_check;
};

}
}

#endif
