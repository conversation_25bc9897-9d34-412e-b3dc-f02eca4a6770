/**
 * ARIES - A Reliable and Impressive EB-level Storage.
 * Copyright (c) 2016 Baidu.com, Inc. All Rights Reserved.
 *
 * Author: <PERSON> (guo<PERSON>@baidu.com)
 * Date: 2016/09/13
 * Desciption: Declaration of repairer delete worker
 *
 */

#ifndef BAIDU_INF_ARIES_REPAIRER_DELETE_WORKER_H
#define BAIDU_INF_ARIES_REPAIRER_DELETE_WORKER_H

#include <atomic>
#include <thread>
#include <bthread.h>
#include <bthread_unstable.h>
#include "baidu/inf/aries-api/common/common.h"
#include "baidu/inf/aries-api/common/types.h"
#include "baidu/inf/aries-api/common/lock.h"
#include "baidu/inf/aries-api/common/utils.h"
#include "baidu/inf/aries/repairer/global.h"
#include "baidu/inf/aries/repairer/diff_base.h"
#include "baidu/inf/aries-api/common/proto/datanode.pb.h"
#include "baidu/inf/aries-api/common/proto/tinker.pb.h"

namespace aries {
namespace common {
    class TokenPool;
}
namespace repairer {

struct BlobDeleteContext {
    std::shared_ptr<common::MutexLock> mutex;
    uint64_t vbid;
    bool only_mark_delete;
    // for rpc
    std::shared_ptr<common::SyncPoint> sync_point;
    int8_t pending_shard_num;
    int8_t succ_shard_num;
    int8_t failed_shard_num;
    int8_t timeout_shard_num;
    bool is_busy;

    BlobDeleteContext() : vbid(0), only_mark_delete(false) {
        mutex = std::make_shared<common::MutexLock>();
        init_counters();
        is_busy = false;
    }
    virtual ~BlobDeleteContext() {
        mutex.reset();
        sync_point.reset();
    }
    void init_counters() {
        pending_shard_num = 0;
        succ_shard_num = 0;
        failed_shard_num = 0;
        timeout_shard_num = 0;
    }
};

struct BlobDeleteTask {
    aries::pb::TinkerTaskId taskid;
    bool is_finished;
    uint64_t task_timeout;
    std::shared_ptr<VolumeCheckContext> volume_ctx;
    common::PtrSharedPriorityQueue<BlobDeleteContext, 1, false> blob_queue;
    // delete statistic
    uint32_t total_num;
    uint32_t succ_num;
    uint32_t mark_delete_fail_num;
    uint32_t final_delete_fail_num;
    uint64_t log_id;
    bool is_busy;
    BlobDeleteTask() {
        is_finished = false;
        task_timeout = 0;
        volume_ctx = nullptr;
        total_num = 0;
        succ_num = 0;
        mark_delete_fail_num = 0;
        final_delete_fail_num = 0;
        log_id = 0;
        is_busy = false;
    }
    void clear() {
        taskid.set_task_begin_timestamp(0);
        taskid.set_random(0);
        volume_ctx = nullptr;
        is_finished = false;
        task_timeout = 0;
        total_num = 0;
        succ_num = 0;
        mark_delete_fail_num = 0;
        final_delete_fail_num = 0;
        log_id = 0;
        is_busy = false;
        while(blob_queue.size() > 0) {
            auto blobctx = blob_queue.pop();
            blobctx.reset();
        }
    }

    bool is_finish() {
        if (succ_num + mark_delete_fail_num + final_delete_fail_num >= total_num) {
            return true;
        }
        return false;
    }
};

class DeleteWorker {
public:
    DeleteWorker(common::TokenPool* token_pool);
    virtual ~DeleteWorker();

    bool start();
    void stop();

private:
    void thread_func();
    bool delete_blob(std::shared_ptr<BlobDeleteContext> blobctx);
    bool mark_delete_shards(std::shared_ptr<BlobDeleteContext> blobctx);
    bool final_delete_shards(std::shared_ptr<BlobDeleteContext> blobctx);
    void delete_shards_done(aries::pb::ShardRemoveRequest* request, aries::pb::AckResponse* response,
                            std::shared_ptr<BlobDeleteContext> blobctx);

    bool report_volume_delete_result();
    bool fetch_volume_delete_task();
    void set_blob_delete_context();
    void do_delete_work();

private:
    std::thread _thread;
    std::atomic<bool> _is_stopped;
    bthread_timer_t _timer;
    common::MutexLock _mutex;
    std::unique_ptr<BlobDeleteTask> _task;
    std::unique_ptr<DiffManager> _diff_manager;
    common::TokenPool* _token_pool; //alloc outside
};

}
}

#endif
