#ifndef __CFS_FUSE_H__
#define __CFS_FUSE_H__

#define FUSE_USE_VERSION 35
#include "libfuse3/fuse.h"

int cfs_getattr(const char* path, struct stat* stat_buf,
                struct fuse_file_info* fi);

int cfs_readlink(const char* path, char* link, size_t size);

int cfs_mknod(const char* path, mode_t mode, dev_t dev);

int cfs_mkdir(const char* path, mode_t mode);

int cfs_unlink(const char* path);

int cfs_rmdir(const char* path);

int cfs_symlink(const char* path, const char* link);

int cfs_rename(const char* path, const char* new_path, unsigned int flags);

int cfs_link(const char* path, const char* link);

int cfs_chmod(const char* path, mode_t mode, struct fuse_file_info* fi);

int cfs_chown(const char* path, uid_t uid, gid_t gid,
              struct fuse_file_info* fi);

int cfs_truncate(const char* path, off_t off, struct fuse_file_info* fi);

int cfs_open(const char* path, struct fuse_file_info* fi);

int cfs_read(const char* path, char* buf, size_t size, off_t off,
             struct fuse_file_info* fi);

int cfs_write(const char* path, const char* buf, size_t size, off_t off,
              struct fuse_file_info* fi);

int cfs_statfs(const char* path, struct statvfs* stat_buf);

int cfs_flush(const char* path, struct fuse_file_info* fi);

int cfs_release(const char* path, struct fuse_file_info* fi);

int cfs_fsync(const char* path, int datasync, struct fuse_file_info* fi);

int cfs_setxattr(const char* path, const char* name, const char* value,
                 size_t size, int flags);

int cfs_getxattr(const char* path, const char* name, char* value, size_t size);

int cfs_listxattr(const char* path, char* list, size_t size);

int cfs_removexattr(const char* path, const char* name);

int cfs_opendir(const char* path, struct fuse_file_info* fi);

int cfs_readdir(const char* path, void* buf, fuse_fill_dir_t filler, off_t off,
                struct fuse_file_info* fi,
                enum fuse_readdir_flags readdir_flags);

int cfs_releasedir(const char* path, struct fuse_file_info* fi);

int cfs_fsyncdir(const char* path, int datasync, struct fuse_file_info* fi);

void* cfs_init(struct fuse_conn_info* conn, struct fuse_config* cfg);

void cfs_destory(void* userdata);

int cfs_access(const char* path, int mask);

int cfs_create(const char* path, mode_t mode, struct fuse_file_info* fi);

int cfs_utimens(const char* path, const struct timespec tv[2],
                struct fuse_file_info* fi);

int cfs_bmap(const char* path, size_t blocksize, uint64_t* idx);

int cfs_ioctl(const char* path, unsigned int cmd, void* arg,
              struct fuse_file_info* fi, unsigned int flags, void* data);

int cfs_poll(const char* path, struct fuse_file_info* fi,
             struct fuse_pollhandle* ph, unsigned* reventsp);

int cfs_fallocate(const char* path, int mode, off_t off, off_t len,
                  struct fuse_file_info* fi);

ssize_t cfs_copy_file_range(const char* path_in, struct fuse_file_info* fi_in,
                            off_t offset_in, const char* path_out,
                            struct fuse_file_info* fi_out, off_t offset_out,
                            size_t size, int flags);

off_t cfs_lseek(const char* path, off_t off, int whence,
                struct fuse_file_info* fuse_file_info);
#endif /* __CFS_FUSE_H__ */