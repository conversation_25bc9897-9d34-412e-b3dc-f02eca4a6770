
#include "../cfs_fuse.h"

int main(int argc, char* argv[]) {
  struct fuse_operations cfs_op = {.getattr = cfs_getattr,
                                   .readlink = cfs_readlink,
                                   .mknod = cfs_mknod,
                                   .mkdir = cfs_mkdir,
                                   .unlink = cfs_unlink,
                                   .rmdir = cfs_rmdir,
                                   .symlink = cfs_symlink,
                                   .rename = cfs_rename,
                                   .link = cfs_link,
                                   .chmod = cfs_chmod,
                                   .chown = cfs_chown,
                                   .truncate = cfs_truncate,
                                   .open = cfs_open,
                                   .read = cfs_read,
                                   .write = cfs_write,
                                   .statfs = cfs_statfs,
                                   .flush = cfs_flush,
                                   .release = cfs_release,
                                   .fsync = cfs_fsync,
                                   .setxattr = cfs_setxattr,
                                   .getxattr = cfs_getxattr,
                                   .listxattr = cfs_listxattr,
                                   .removexattr = cfs_removexattr,
                                   .opendir = cfs_opendir,
                                   .readdir = cfs_readdir,
                                   .releasedir = cfs_releasedir,
                                   .fsyncdir = cfs_fsyncdir,
                                   .init = cfs_init,
                                   .destroy = cfs_destory,
                                   .access = cfs_access,
                                   .create = cfs_create,
                                   .lock = NULL,  // fall back to system lock
                                   .utimens = cfs_utimens,
                                   .bmap = cfs_bmap,
                                   .ioctl = cfs_ioctl,
                                   .poll = cfs_poll,
                                   .write_buf = NULL,  // fallback to write
                                   .read_buf = NULL,   // fallback to read
                                   .flock = NULL,  // fall back to system flock
                                   .fallocate = cfs_fallocate,
                                   .copy_file_range = cfs_copy_file_range,
                                   .lseek = cfs_lseek};
  return fuse_main(argc, argv, &cfs_op, NULL);
}