#!/bin/bash

set -euo pipefail
set +x

BUILD_MODE="release"
ENABLE_JEMALLOC="true"
BUILD_SDKV1="true"
BUILD_UT=OFF
BUILD_ALL_TEST=OFF


show_help() {
    echo "Usage: $0 [OPTIONS]"
    echo "Build configuration script"
    echo ""
    echo "Options:"
    echo "  --mode=<debug/release/default>        Set build mode (default: $BUILD_MODE)"
    echo "  --jemalloc=<true/false>  Enable jemalloc (default: $ENABLE_JEMALLOC)"
    echo "  --build-sdkv1=<true/false>   Build SDK v1 components (default: $BUILD_SDKV1)"
    echo "  --build-ut Enable UT (default disabled)"
    echo "  --build-all-test Enable all test(perf/stable/benchmark) (default disabled)"
    echo "  -h, --help            Show this help message"
}

# 参数解析
for i in "$@"; do
    case $i in
        --mode=*)
            BUILD_MODE="${i#*=}"
            ;;
        --jemalloc=*)
            ENABLE_JEMALLOC="${i#*=}"
            ;;
        --build-sdkv1=*)
            BUILD_SDKV1="${i#*=}"
            ;;
        --build-ut) BUILD_UT=ON ; shift 1
            ;;
        --build-all-test) BUILD_ALL_TEST=ON ; shift 1
            ;;
        -h|--help)
            show_help
            exit 0
            ;;
        *)
            echo "Unknown option: $i"
            show_help
            exit 1
            ;;
    esac
done

echo "Build cfs_fuse_v1 started. mode: $BUILD_MODE, jemalloc: $ENABLE_JEMALLOC, sdkv1: $BUILD_SDKV1"

SCRIPT_DIR=$( cd "$( dirname "$0"  )" && pwd  )
BUILD_DIR=${SCRIPT_DIR}/build
OUTPUT_DIR=${SCRIPT_DIR}/output

if [ "${BUILD_MODE}" == "debug" ]; then
    BUILD_TYPE="Debug"
elif [ "${BUILD_MODE}" == "release" ]; then
    BUILD_TYPE="Release"
else
    BUILD_TYPE="RelWithDebInfo"
fi

if [ "${ENABLE_JEMALLOC}" == "true" ]; then
    ENABLE_JEMALLOC=true
elif [ "${ENABLE_JEMALLOC}" == "false" ]; then
    ENABLE_JEMALLOC=false
else
    echo "Unknown value for jemalloc, suppored true or false" >&2;
    exit 1
fi

FUSE_DIR=${SCRIPT_DIR}
FUSE_THIRD_PARTY_DIR=${FUSE_DIR}/third_party
mkdir -p ${FUSE_THIRD_PARTY_DIR}/include ${FUSE_THIRD_PARTY_DIR}/lib

git submodule update --init --recursive $FUSE_DIR/../cfs_sdk_v1


if [ "$BUILD_SDKV1" == "false" ]; then
    echo "Force skip build sdkv1!!!"
elif [ -f "$FUSE_THIRD_PARTY_DIR/lib/libcloudfs.so" ]; then
    echo "SDK V1 already existed in third dir, skip build it!!!"
else
    echo "Building cfs_sdk_v1 for cfs_fuse_v1"
    SDK_CPP_DIR=${SCRIPT_DIR}/../cfs_sdk_v1
    pushd ${SDK_CPP_DIR}
    bash build.sh --mode=$BUILD_MODE --concur=8 

    # Prepare sdk v1 and libfuse lib for FUSE
    echo "Copy libcloudfs.so to third_party"
    cp ./output/include/cloudfs.h ${FUSE_THIRD_PARTY_DIR}/include/cloudfs.h
    cp ./output/lib/libcloudfs.so ${FUSE_THIRD_PARTY_DIR}/lib/libcloudfs.so
    popd
fi

mkdir -p ${BUILD_DIR}
pushd ${BUILD_DIR}
cmake\
    -DCMAKE_BUILD_TYPE=${BUILD_TYPE}\
    -DCMAKE_INSTALL_PREFIX=${OUTPUT_DIR}\
    -DENABLE_JEMALLOC=${ENABLE_JEMALLOC}\
    -DCFS_BUILD_UNIT_TEST=${BUILD_UT}\
    -DCFS_BUILD_ALL_TEST=${BUILD_ALL_TEST}\
    ..
make -j$(nproc) VERBOSE=1
make install
popd
