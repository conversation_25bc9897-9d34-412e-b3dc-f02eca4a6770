#include <fstream>
#include <iostream>
#include <string>

#include "cfs_fuse.h"
#include "cloudfs.h"
#include "config.h"
#include "gtest/gtest.h"
#include "stub.h"
#include "utils.h"

class TestFuseRead : public ::testing::Test {
 public:
  // Sets up the stuff shared by all tests in this test case.
  //
  // Google Test will call Foo::SetUpTestCase() before running the first
  // test in test case Foo.  Hence a sub-class can define its own
  // SetUpTestCase() method to shadow the one defined in the super
  // class.
  static void SetUpTestCase() {}

  // Tears down the stuff shared by all tests in this test case.
  //
  // Google Test will call Foo::TearDownTestCase() after running the last
  // test in test case Foo.  Hence a sub-class can define its own
  // TearDownTestCase() method to shadow the one defined in the super
  // class.
  static void TearDownTestCase() {}

 protected:
  void SetUp() override {}

  void TearDown() override {}
};

typedef struct ReadStubParam {
  tOffset tell_stub_file_cursor;
  int tell_invoke_count;
  int seek_invoke_count;
  int read_invoke_count;
  int pread_invoke_count;
} ReadStubParamT;

tOffset cfsTell_stub(cfsFS fs, cfsFile file) {
  (void)fs;
  ReadStubParamT* read_stub_param = (ReadStubParamT*)file;
  std::cout << "cfsTell stub, cursor" << read_stub_param->tell_stub_file_cursor
            << std::endl;
  read_stub_param->tell_invoke_count += 1;
  return read_stub_param->tell_stub_file_cursor;
}

int cfsSeek_Stub(cfsFS fs, cfsFile file, tOffset desiredPos) {
  (void)fs;
  ReadStubParamT* read_stub_param = (ReadStubParamT*)file;
  std::cout << "cfsSeek stub desiredPos " << desiredPos << std::endl;
  read_stub_param->seek_invoke_count += 1;
  read_stub_param->tell_stub_file_cursor = desiredPos;
  return 0;
}

int64_t cfsRead_stub(cfsFS fs, cfsFile file, void* buffer, uint64_t length) {
  (void)fs;
  (void)buffer;
  ReadStubParamT* read_stub_param = (ReadStubParamT*)file;
  std::cout << "cfsRead stub" << std::endl;
  read_stub_param->read_invoke_count += 1;
  read_stub_param->tell_stub_file_cursor += length;
  return length;
}

tSize cfsPread_stub(cfsFS fs, cfsFile file, tOffset position, void* buffer,
                    tSize length) {
  (void)fs;
  (void)buffer;
  ReadStubParamT* read_stub_param = (ReadStubParamT*)file;
  std::cout << "cfsPread position " << position << ", length " << position
            << std::endl;
  read_stub_param->pread_invoke_count += 1;
  return length;
}

TEST_F(TestFuseRead, TestContinueslyReadThenSuccess) {
  root_config.set_is_adaptive_pread(false);
  Stub stub;
  stub.set(cfsTell, cfsTell_stub);
  stub.set(cfsRead, cfsRead_stub);
  std::string path = "/cloudfs/unit-fuse-test";
  ReadStubParamT read_stub_param;
  file_handler* fh = new file_handler;
  fh->file = (cfsFile)&read_stub_param;
  fuse_file_info* fi = new fuse_file_info;
  fi->fh = (uint64_t)fh;
  read_stub_param.tell_invoke_count = 0;
  read_stub_param.seek_invoke_count = 0;
  read_stub_param.read_invoke_count = 0;
  read_stub_param.pread_invoke_count = 0;

  size_t read_len = 223;
  char buf[read_len];
  read_stub_param.tell_stub_file_cursor = 0;
  int ret = cfs_read(path.c_str(), buf, read_len, 0, fi);
  EXPECT_EQ(ret, read_len);

  EXPECT_EQ(1, read_stub_param.tell_invoke_count);
  EXPECT_EQ(0, read_stub_param.seek_invoke_count);
  EXPECT_EQ(1, read_stub_param.read_invoke_count);
  EXPECT_EQ(0, read_stub_param.pread_invoke_count);

  delete fh;
  delete fi;
}

TEST_F(TestFuseRead, TestRandomReadWithSeekRreadThenSuccess) {
  root_config.set_is_adaptive_pread(false);
  Stub stub;
  stub.set(cfsTell, cfsTell_stub);
  stub.set(cfsSeek, cfsSeek_Stub);
  stub.set(cfsRead, cfsRead_stub);
  std::string path = "/cloudfs/unit-fuse-test";
  ReadStubParamT read_stub_param;
  file_handler* fh = new file_handler;
  fh->file = (cfsFile)&read_stub_param;
  fuse_file_info* fi = new fuse_file_info;
  fi->fh = (uint64_t)fh;
  off_t pread_pos = 1048590;
  size_t read_len = 456;
  char buf[read_len];
  read_stub_param.tell_stub_file_cursor = 0;
  int ret = cfs_read(path.c_str(), buf, read_len, pread_pos, fi);
  EXPECT_EQ(ret, read_len);
  delete fh;
  delete fi;
}

TEST_F(TestFuseRead, TestRandomReadWithPreadThenSuccess) {
  root_config.set_is_adaptive_pread(true);
  Stub stub;
  stub.set(cfsTell, cfsTell_stub);
  stub.set(cfsPread, cfsPread_stub);
  std::string path = "/cloudfs/unit-fuse-test";
  ReadStubParamT read_stub_param;
  file_handler* fh = new file_handler;
  fh->file = (cfsFile)&read_stub_param;
  fh->prevReadOff = 0;
  fuse_file_info* fi = new fuse_file_info;
  fi->fh = (uint64_t)fh;
  off_t pread_pos = 1048590;
  size_t read_len = 456;
  char buf[read_len];
  read_stub_param.tell_stub_file_cursor = 0;
  int ret = cfs_read(path.c_str(), buf, read_len, pread_pos, fi);
  EXPECT_EQ(ret, read_len);
  delete fh;
  delete fi;
}

TEST_F(TestFuseRead, TestRandomReadWithPreadThenSuccess2) {
  root_config.set_is_adaptive_pread(true);
  Stub stub;
  stub.set(cfsTell, cfsTell_stub);
  stub.set(cfsRead, cfsRead_stub);
  stub.set(cfsSeek, cfsSeek_Stub);
  stub.set(cfsPread, cfsPread_stub);
  std::string path = "/cloudfs/unit-fuse-test";
  ReadStubParamT read_stub_param;
  file_handler* fh = new file_handler;
  fh->file = (cfsFile)&read_stub_param;
  fh->prevReadOff = 0;
  fh->isReadContinuously = true;
  fuse_file_info* fi = new fuse_file_info;
  fi->fh = (uint64_t)fh;
  read_stub_param.tell_invoke_count = 0;
  read_stub_param.seek_invoke_count = 0;
  read_stub_param.read_invoke_count = 0;
  read_stub_param.pread_invoke_count = 0;
  read_stub_param.tell_stub_file_cursor = 0;

  // read
  char buf[123];
  off_t pread_pos = 0;
  size_t read_len = 1;
  int ret = cfs_read(path.c_str(), buf, read_len, pread_pos, fi);
  EXPECT_EQ(ret, read_len);
  EXPECT_EQ(1, read_stub_param.tell_invoke_count);
  EXPECT_EQ(0, read_stub_param.seek_invoke_count);
  EXPECT_EQ(1, read_stub_param.read_invoke_count);
  EXPECT_EQ(0, read_stub_param.pread_invoke_count);

  // pread
  pread_pos = 1048590;
  read_len = 20;
  ret = cfs_read(path.c_str(), buf, read_len, pread_pos, fi);
  EXPECT_EQ(ret, read_len);
  EXPECT_EQ(2, read_stub_param.tell_invoke_count);
  EXPECT_EQ(0, read_stub_param.seek_invoke_count);
  EXPECT_EQ(1, read_stub_param.read_invoke_count);
  EXPECT_EQ(1, read_stub_param.pread_invoke_count);

  // pread
  pread_pos += read_len;
  read_len = 30;
  ret = cfs_read(path.c_str(), buf, read_len, pread_pos, fi);
  EXPECT_EQ(ret, read_len);
  EXPECT_EQ(3, read_stub_param.tell_invoke_count);
  EXPECT_EQ(0, read_stub_param.seek_invoke_count);
  EXPECT_EQ(1, read_stub_param.read_invoke_count);
  EXPECT_EQ(2, read_stub_param.pread_invoke_count);

  // seek + read
  pread_pos += read_len;
  read_len = 30;
  ret = cfs_read(path.c_str(), buf, read_len, pread_pos, fi);
  EXPECT_EQ(ret, read_len);
  EXPECT_EQ(4, read_stub_param.tell_invoke_count);
  EXPECT_EQ(1, read_stub_param.seek_invoke_count);
  EXPECT_EQ(2, read_stub_param.read_invoke_count);
  EXPECT_EQ(2, read_stub_param.pread_invoke_count);

  // read
  pread_pos += read_len;
  read_len = 30;
  ret = cfs_read(path.c_str(), buf, read_len, pread_pos, fi);
  EXPECT_EQ(ret, read_len);
  EXPECT_EQ(5, read_stub_param.tell_invoke_count);
  EXPECT_EQ(1, read_stub_param.seek_invoke_count);
  EXPECT_EQ(3, read_stub_param.read_invoke_count);
  EXPECT_EQ(2, read_stub_param.pread_invoke_count);

  // read
  pread_pos += read_len;
  read_len = 30;
  ret = cfs_read(path.c_str(), buf, read_len, pread_pos, fi);
  EXPECT_EQ(ret, read_len);
  EXPECT_EQ(6, read_stub_param.tell_invoke_count);
  EXPECT_EQ(1, read_stub_param.seek_invoke_count);
  EXPECT_EQ(4, read_stub_param.read_invoke_count);
  EXPECT_EQ(2, read_stub_param.pread_invoke_count);

  // pread
  pread_pos += read_len;
  pread_pos += pread_pos;  // skip read
  read_len = 30;
  ret = cfs_read(path.c_str(), buf, read_len, pread_pos, fi);
  EXPECT_EQ(ret, read_len);
  EXPECT_EQ(7, read_stub_param.tell_invoke_count);
  EXPECT_EQ(1, read_stub_param.seek_invoke_count);
  EXPECT_EQ(4, read_stub_param.read_invoke_count);
  EXPECT_EQ(3, read_stub_param.pread_invoke_count);

  // pread
  pread_pos += read_len;
  read_len = 30;
  ret = cfs_read(path.c_str(), buf, read_len, pread_pos, fi);
  EXPECT_EQ(ret, read_len);
  EXPECT_EQ(8, read_stub_param.tell_invoke_count);
  EXPECT_EQ(1, read_stub_param.seek_invoke_count);
  EXPECT_EQ(4, read_stub_param.read_invoke_count);
  EXPECT_EQ(4, read_stub_param.pread_invoke_count);

  delete fh;
  delete fi;
}