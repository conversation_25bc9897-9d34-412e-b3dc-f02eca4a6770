get_property(GFLAGS_LIB GLOBAL PROPERTY gflags_lib_property)
get_property(GLOG_LIB GLOBAL PROPERTY glog_lib_property)

set(SRCS
    ${CMAKE_CURRENT_SOURCE_DIR}/main.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/Task.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/Utils.cpp

    ${CMAKE_CURRENT_SOURCE_DIR}/picosha2.h
    ${CMAKE_CURRENT_SOURCE_DIR}/Task.h
)

add_executable(cfs-fuse-stable
    ${SRCS}
)

target_include_directories(cfs-fuse-stable
    PRIVATE
    .
)

target_link_libraries(cfs-fuse-stable
    PRIVATE

    ${GLOG_LIB}
    ${GFLAGS_LIB}

    unwind
    pthread
)

add_dependencies(cfs-fuse-stable
    gflags
    glog
)

install(TARGETS cfs-fuse-stable
        COMPONENT Development
        DESTINATION test)
