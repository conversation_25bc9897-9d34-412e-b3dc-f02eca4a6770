package com.volcengine.cloudfs.handler;

import com.volcengine.cloudfs.fbs.MkdirRequestObj;
import com.volcengine.cloudfs.fs.coder.MkdirEncoder;
import com.volcengine.cloudfs.fs.coder.StatusDecoder;
import com.volcengine.cloudfs.fs.common.FilesystemContext;
import com.volcengine.cloudfs.jni.NativeResponse;
import org.apache.hadoop.fs.permission.FsPermission;

import java.io.IOException;
import java.nio.ByteBuffer;

public class MkdirHandler extends RequestHandler<MkdirRequestObj, Object, Boolean> {
  private String src;
  private short permission;
  private boolean createParent;

  public MkdirHandler(FilesystemContext context, String src, FsPermission masked, boolean createParent) {
    super(context);
    this.src = src;
    this.permission = masked.toOctal();
    this.createParent = createParent;
  }

  @Override
  protected MkdirRequestObj constructParams() {
    MkdirRequestObj requestObj = new MkdirRequestObj();
    requestObj.setPath(src);
    requestObj.setPermission(permission);
    requestObj.setCreateParent(createParent);
    return requestObj;
  }

  @Override
  protected ByteBuffer encodeParams(MkdirRequestObj request) throws IOException {
    this.encoder = new MkdirEncoder(request, clientContext.getConf());
    return encoder.encode();
  }

  @Override
  protected NativeResponse callNative(ByteBuffer encodeBuffer) throws IOException {
    return nativeFileSystem.mkdir(encodeBuffer);
  }

  @Override
  protected Object decodeResponse(NativeResponse nativeResp) throws IOException {
    this.decoder = new StatusDecoder(nativeResp);
    return decoder.decode();
  }

  @Override
  protected Boolean constructResponse(Object protoResp) {
    return (Boolean) protoResp;
  }
}
