package com.volcengine.cloudfs.jni;

import java.io.IOException;
import java.nio.ByteBuffer;

public class NativeReader extends NativeObject {

  public NativeReader(long nativeReaderPtr) {
    this.nativeObjectPtr = nativeReaderPtr;
  }

  public static NativeReader createNativeReader(long nativeReaderPtr) {
    return new NativeReader(nativeReaderPtr);
  }

  // Native call
  public native NativeResponse read(long position, ByteBuffer buf) throws IOException;

  public native NativeResponse closeFile() throws IOException;

  @Override
  protected void close() {

  }
}
