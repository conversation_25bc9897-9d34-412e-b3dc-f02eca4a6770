package com.volcengine.cloudfs.metrics;

import java.net.URI;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.ReadWriteLock;
import java.util.concurrent.locks.ReentrantReadWriteLock;

import org.apache.hadoop.conf.Configuration;

import com.bytedance.metrics.BytedanceMetrics;
import com.bytedance.metrics.simple.SimpleByteTSDMetrics;
import com.codahale.metrics.Counter;
import com.codahale.metrics.MetricRegistry;
import com.codahale.metrics.Timer;
import com.google.common.base.Strings;
import com.volcengine.cloudfs.fs.cfs.ConfigKeys;
import com.volcengine.cloudfs.utils.CfsInfoParser;
import com.volcengine.cloudfs.utils.NetUtil;

import lombok.extern.slf4j.Slf4j;

// static methods for getTimer and getCounter
@Slf4j
public class ClientMetrics {
    public static final String DN_IP_TAG = "dnIp";
    public static final String IS_FLUSH = "isFlush";

    private static final String CLIENT_UUID = UUID.randomUUID().toString();

    private static final String CLIENT_VERSION = "v2";

    static private final String CFS_METRICS_PREFIX = "inf.cfs.client";
    static private SimpleByteTSDMetrics byteTSDMetrics = null;
    
    static private volatile boolean isInited = false;
    
    static private ReadWriteLock lock;
    static private Map<String, Timer> timers;
    static private Map<String, Counter> counters;
    static private String fileSystemName;
    static private String region;
    static private String applicationId = "unknown";
    static private String unknown = "unknown";
    static private Map<String, String> tags = new HashMap<>();
    static private MetricRegistry metricRegistry;
    static private Map<String, String> host2FsName = new HashMap<>();
    static private boolean refreshFsNameEnable = false;

    private static final String TAG_KEY_FS = "fs";
    private static final String TAG_KEY_ENV = "env";
    private static final String TAG_KEY_HOST = "host";

    private static final String TAG_KEY_BUSINESS_ID = "businessId";

    private static final String TAG_KEY_DEPLOY_ENV = "deployEnv";

    private static final String TAG_KEY_UUID = "uuid";
    private static final String TAG_KEY_REGION = "region";

    private static final String TAG_KEY_VERSION = "version";

    private static Configuration metricsConf;

    public synchronized static void initialize(Configuration conf) {
        initialize(conf, null);
    }

    public synchronized static void initialize(Configuration conf, URI uri) {
        if (isInited) {
            return;
        }

        boolean enabled = conf.getBoolean(ConfigKeys.CFS_CLIENT_METRICS_ENABLE,
            ConfigKeys.CFS_CLIENT_METRICS_ENABLE_DEFAULT);
        if (!enabled) {
            return;
        }

        metricsConf = conf;
        String businessId = conf.get(ConfigKeys.CFS_BUSINESS_ID, ConfigKeys.CFS_BUSINESS_ID_DEFAULT);
        refreshFsNameEnable = conf.getBoolean(ConfigKeys.CFS_METRICS_REFRESH_FS_ENABLE,
            ConfigKeys.CFS_METRICS_REFRESH_FS_ENABLE_DEFAULT);

        String region = unknown;
        String env = "cfs";
        fileSystemName = unknown;
        try {
            region = CfsInfoParser.getRegion(uri, conf);
        } catch (Exception e) {
            log.error("Failed to acquire region");
        }
        try {
            fileSystemName = CfsInfoParser.fileSystemName(conf, uri);
        } catch (Exception e) {
            log.error("Failed to acquire filesystem");
        }
        try {
            env = CfsInfoParser.getEnv(uri, conf);
        } catch (Exception e) {
            log.error("Failed to acquire deploy env");
        }
        tags.put(TAG_KEY_FS, fileSystemName);
        tags.put(TAG_KEY_HOST, NetUtil.getClientAddress());
        tags.put(TAG_KEY_DEPLOY_ENV, env);
        tags.put(TAG_KEY_UUID, CLIENT_UUID);
        tags.put(TAG_KEY_VERSION, CLIENT_VERSION);
        tags.put(TAG_KEY_BUSINESS_ID, businessId);
        if (!Strings.isNullOrEmpty(region)) {
            tags.put(TAG_KEY_REGION, region);
        }

        metricRegistry = ByteDanceMetrics.getMetrics(CFS_METRICS_PREFIX, conf);
        lock = new ReentrantReadWriteLock();
        timers = new HashMap<>();
        counters = new HashMap<>();

        applicationId = System.getenv("LOAD_SERVICE_PSM"); //yarn.cluster_id.applicaition_1234
        if (applicationId == null) {
            applicationId = "unknown";
        }

        isInited = true;
        log.info("Initialize client metrics for fileSystem " + fileSystemName + " tags: " + tags);
    }

    public static boolean isMetricsEnabled() {
        return isInited;
    }

    public static void updateTimer(String name, long startTimestampMs) {
        if (!isInited) {
            return;
        }

        long costMs = System.currentTimeMillis() - startTimestampMs;
        Timer timer = getTimer(name);
        if (timer != null) {
            timer.update(costMs, TimeUnit.MILLISECONDS);
        }
    }

    public static void updateTimer(String name, String method, long startTimestampMs) {
        if (!isInited) {
            return;
        }

        long costMs = System.currentTimeMillis() - startTimestampMs;
        Timer timer = getTimer(name, MetricConstants.METRIC_METHOD_KEY, method);
        if (timer != null) {
            timer.update(costMs, TimeUnit.MILLISECONDS);
        }
    }

    public static void incCounter(String name, String method) {
        if (!isInited) {
            return;
        }
        Counter counter = getCounterWithMethod(name, method);
        if (counter != null) {
            counter.inc();
        }
    }



    private static Timer getTimer(String name, Map<String, String> customizeTags) {
        if (!isInited) {
            return null;
        }

        customizeTags.putAll(tags);
        name = name + BytedanceMetrics.getTagKVs(customizeTags);
        return innerGetTimer(name);
    }

    private static Timer getTimer(String name, String key, String val) {
        if (!isInited) {
            return null;
        }

        HashMap<String, String> tmp = new HashMap<>(tags);
        tmp.put(key, val);
        name = name + BytedanceMetrics.getTagKVs(tmp);
        return innerGetTimer(name);
    }

    private static Timer getTimerWithMethod(String name, String method) {
        return getTimer(name, MetricConstants.METRIC_METHOD_KEY, method);
    }

    private static Timer getTimer(String name) {
        if (!isInited) {
            return null;
        }

        name = name + BytedanceMetrics.getTagKVs(tags);
        return innerGetTimer(name);
    }

    private static Timer innerGetTimer(String name) {
        lock.readLock().lock();
        try {
            Timer t = timers.get(name);
            if (t != null) {
                return t;
            }
        } finally {
            lock.readLock().unlock();
        }
        lock.writeLock().lock();
        try {
            Timer t = timers.get(name);
            if (t != null) {
                return t;
            }
            t = metricRegistry.timer(name);
            timers.put(name, t);
            return t;
        } finally {
            lock.writeLock().unlock();
        }
    }

    private static Counter getCounter(String name) {
        name = name + BytedanceMetrics.getTagKVs(tags);
        return getCounterByFullName(name);
    }

    private static Counter getCounterWithMethod(String name, String method) {
        HashMap<String, String> tmp = new HashMap<>(tags);
        tmp.put(MetricConstants.METRIC_METHOD_KEY, method);
        name = name + BytedanceMetrics.getTagKVs(tmp);
        return getCounterByFullName(name);
    }

    private static Counter getCounterWithTag(String name, String tagK, String tagV) {
        HashMap<String, String> tmp = new HashMap<>(tags);
        tmp.put(tagK, tagV);
        name = name + BytedanceMetrics.getTagKVs(tmp);
        return getCounterByFullName(name);
    }

    private static Counter getCounterByFullName(String fullName) {
        if (!isInited) {
            return null;
        }

        lock.readLock().lock();
        try {
            Counter c = counters.get(fullName);
            if (c != null) {
                return c;
            }
        } finally {
            lock.readLock().unlock();
        }
        lock.writeLock().lock();
        try {
            Counter c = counters.get(fullName);
            if (c != null) {
                return c;
            }
            c = metricRegistry.counter(fullName);
            counters.put(fullName, c);
            return c;
        } finally {
            lock.writeLock().unlock();
        }
    }
}
