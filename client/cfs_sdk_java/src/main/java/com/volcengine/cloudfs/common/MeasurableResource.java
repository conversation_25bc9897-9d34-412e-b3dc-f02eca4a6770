package com.volcengine.cloudfs.common;

import lombok.Builder;
import lombok.Data;

/**
 * Generic container for tracked resources in pool management.
 * 
 * @param <T> Type of the wrapped resource object
 */
@Data
@Builder
public class MeasurableResource<T> {
  /** 
   * Timestamp in milliseconds (epoch time) for resource last use
   * Used for expiration or GC decisions.
   */
  private long timestamp;

  /** 
   * Capacity measurement in resource-specific units 
   * (e.g. bytes for memory buffers, connections for connection pools)
   */
  private int capacity;

  /** 
   * The actual resource instance being managed.
   */
  private T resource;
}
