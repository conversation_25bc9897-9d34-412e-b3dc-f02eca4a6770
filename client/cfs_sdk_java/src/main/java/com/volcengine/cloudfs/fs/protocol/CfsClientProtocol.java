package com.volcengine.cloudfs.fs.protocol;

import java.io.IOException;

import org.apache.hadoop.crypto.CryptoProtocolVersion;
import org.apache.hadoop.fs.CreateFlag;
import org.apache.hadoop.fs.permission.FsPermission;
import org.apache.hadoop.hdfs.protocol.ClientProtocol;
import org.apache.hadoop.io.EnumSetWritable;

public interface CfsClientProtocol extends ClientProtocol {
  long nativeCreate(String src, FsPermission masked,
              String clientName, EnumSetWritable<CreateFlag> flag,
              boolean createParent, short replication, long blockSize,
              CryptoProtocolVersion[] supportedVersions, String ecPolicyName,
              String storagePolicy)
      throws IOException;

  long nativeOpen(String src) throws IOException;

  void disconnect() throws IOException;
}
