package com.volcengine.cloudfs.handler;

import java.io.IOException;
import java.nio.ByteBuffer;

import org.apache.hadoop.fs.CreateFlag;
import org.apache.hadoop.io.EnumSetWritable;

import com.volcengine.cloudfs.fbs.NativeCreateRequestObj;
import com.volcengine.cloudfs.fbs.NativeFileRespObj;
import com.volcengine.cloudfs.fs.coder.NativeCreateDecoder;
import com.volcengine.cloudfs.fs.coder.NativeCreateEncoder;
import com.volcengine.cloudfs.fs.common.FilesystemContext;
import com.volcengine.cloudfs.jni.NativeResponse;
import com.volcengine.cloudfs.utils.FBHelper;

public class NativeCreateHandler extends RequestHandler<NativeCreateRequestObj, NativeFileRespObj, Long> {
  private String src;
  private String clientName;
  private EnumSetWritable<CreateFlag> createFlag;
  private boolean createParent;
  private long replication;
  private long blockSize;
  private String ecPolicyName;
  private String storagePolicy;

  public NativeCreateHandler(FilesystemContext context, String src, String clientName, EnumSetWritable<CreateFlag> createFlag, boolean createParent, long replication, long blockSize, String ecPolicyName, String storagePolicy) {
    super(context);
    this.src = src;
    this.clientName = clientName;
    this.createFlag = createFlag;
    this.createParent = createParent;
    this.replication = replication;
    this.blockSize = blockSize;
    this.ecPolicyName = ecPolicyName;
    this.storagePolicy = storagePolicy;
  }

  @Override
  protected NativeCreateRequestObj constructParams() {
    NativeCreateRequestObj requestObj = new NativeCreateRequestObj();
    requestObj.setSrc(src);
    requestObj.setClientName(clientName);
    requestObj.setCreateFlag(FBHelper.convertCreateFlag(createFlag));
    requestObj.setCreateParent(createParent);
    requestObj.setReplication(replication);
    requestObj.setBlockSize(blockSize);
    requestObj.setEcPolicyName(ecPolicyName);
    requestObj.setStoragePolicy(storagePolicy);
    return requestObj;
  }

  @Override
  protected ByteBuffer encodeParams(NativeCreateRequestObj request) throws IOException {
    this.encoder = new NativeCreateEncoder(request, clientContext.getConf());
    return this.encoder.encode();
  }

  @Override
  protected NativeResponse callNative(ByteBuffer encodeBuffer) throws IOException {
    return nativeFileSystem.nativeCreate(encodeBuffer);
  }

  @Override
  protected NativeFileRespObj decodeResponse(NativeResponse nativeResp) throws IOException {
    this.decoder = new NativeCreateDecoder(nativeResp);
    return (NativeFileRespObj) decoder.decode();
  }

  @Override
  protected Long constructResponse(NativeFileRespObj protoResp) {
    return protoResp.getNativeFilePtr();
  }
}
