package com.volcengine.cloudfs.fs.cfs;

import java.util.HashMap;
import java.util.Map;

import com.google.common.base.Strings;

public class ConfigKeys {
  // common
  public static final String CFS_FILESYSTEM_PORT = "cfs.filesystem.port";
  public static final int CFS_FILESYSTEM_PORT_DEFAULT = 65220;

  public static final String CFS_ACC_ENDPOINT_URI = "cfs.filesystem.acc.uri";

  public static final String CFS_ACC_UFS_URI = "cfs.filesystem.acc.ufs.uri";

  public static final String CFS_ACC_NAMESPACE_ID = "cfs.filesystem.acc.namespace.id";
  public static final String CFS_FILESYSTEM_MODE = "cfs.filesystem.mode";
  public static final String CFS_ACC_FILESYSTEM_MODE = "ACC";
  public static final String CFS_AUTH_ENABLE = "cfs.auth.enable";

  // Meta
  public static final String CFS_META_BUFFER_POOL_MAX_BYTES = "cfs.meta.buffer.pool.max.bytes";
  public static final long CFS_META_BUFFER_POOL_MAX_BYTES_DEFAULT = 1 * 1024 * 1024 * 1024L;

  public static final String CFS_META_BUFFER_INIT_BYTES = "cfs.meta.buffer.init.bytes";
  public static final int CFS_META_BUFFER_INIT_BYTES_DEFAULT = 1024;

  public static final String CFS_META_BUFFER_POOL_ACQUIRE_WAIT_MS = "cfs.meta.buffer.pool.acquire.wait.ms";
  public static final long CFS_META_BUFFER_POOL_ACQUIRE_WAIT_MS_DEFAULT = 10 * 60 * 1000;

  // IO
  public static final String CFS_IO_BUFFER_POOL_MAX_BYTES = "cfs.io.buffer.pool.max.bytes";
  public static final long CFS_IO_BUFFER_POOL_MAX_BYTES_DEFAULT = 2 * 1024 * 1024 * 1024L;

  public static final String CFS_IO_BUFFER_POOL_ACQUIRE_WAIT_MS = "cfs.io.buffer.pool.acquire.wait.ms";
  public static final long CFS_IO_BUFFER_POOL_ACQUIRE_WAIT_MS_DEFAULT = 10 * 60 * 1000;

  // Write IO
  public static final String CFS_IO_WRITE_BUFFER_SIZE = "cfs.io.write.buffer.size";
  public static final int CFS_IO_WRITE_BUFFER_SIZE_DEFAULT = 4 * 1024 * 1024;

  public static final String CFS_IO_WRITE_BUFFER_MINIMUM_SIZE = "cfs.io.write.buffer.minimum.size";
  public static final int CFS_IO_WRITE_BUFFER_MINIMUM_SIZE_DEFAULT = 64 * 1024;


  // Read IO
  public static final String CFS_IO_READ_BUFFER_SIZE = "cfs.io.read.buffer.size";
  public static final int CFS_IO_READ_BUFFER_SIZE_DEFAULT = 2 * 1024 * 1024;

  // Metrics
  public static final String CFS_CLIENT_METRICS_ENABLE = "cfs.client.io.metrics.enable";
  public static final boolean CFS_CLIENT_METRICS_ENABLE_DEFAULT = false;
  public static final String CFS_BUSINESS_ID = "cfs.client.business.id";
  public static final String CFS_BUSINESS_ID_DEFAULT = "unknown";
  public static final String CFS_METRICS_REFRESH_FS_ENABLE = "cfs.metrics.refresh.fs.enable";
  public static final boolean CFS_METRICS_REFRESH_FS_ENABLE_DEFAULT = true;
  public static final String FS_CFS_METRICS_SERVER_HOST = "fs.cfs.metrics.server.host";
  public static final String FS_CFS_METRICS_SERVER_PORT = "fs.cfs.metrics.server.port";

  /**
   * Compatible with Java SDK v1 config items
   */
  private static final String CFS_ACCESS_KEY = "cfs.access.key";
  private static final String CFS_SECRET_KEY = "cfs.secret.key";
  private static final String CFS_SECURITY_TOKEN = "cfs.security.token";
  private static final String CFS_CLIENT_NETWORK_SEGMENT = "cfs.client.network.segment";

  private static final String NATIVE_ACCESS_KEY = "cfs_access_key";
  private static final String NATIVE_SECRET_KEY = "cfs_secret_key";
  private static final String NATIVE_SECURITY_TOKEN = "cfs_security_token";
  private static final String NATIVE_CLIENT_NETWORK_SEGMENT = "cfs_expect_dn_subnet";
  private static final String NATIVE_CLIENT_METRIC_ENABLE = "cfs_metric_enabled";
  public static final String NATIVE_CLIENT_AUTH_ENABLE = "cfs_enable_auth";
  private static final String NATIVE_CLIENT_METRIC_SERVER_ENDPOINT = "cfs_metric_server_endpoint";
  private static final Map<String, String> Hadoop2NativeConf = new HashMap<String, String>(){{
    put(CFS_ACCESS_KEY, NATIVE_ACCESS_KEY);
    put(CFS_SECRET_KEY, NATIVE_SECRET_KEY);
    put(CFS_SECURITY_TOKEN, NATIVE_SECURITY_TOKEN);
    put(CFS_CLIENT_NETWORK_SEGMENT, NATIVE_CLIENT_NETWORK_SEGMENT);
    put(CFS_CLIENT_METRICS_ENABLE, NATIVE_CLIENT_METRIC_ENABLE);
    put(FS_CFS_METRICS_SERVER_HOST, NATIVE_CLIENT_METRIC_SERVER_ENDPOINT);
  }};
  private static final String NativeConfPrefix = "cfs.flag.";

  /**
   * Translate hadoop configuration key to native sdk key
   *
   * @param key Hadoop's configuration key
   * @return Configuration key of native sdk.
   *         If not the config key needed by native return empty string
   */
  public static String translateToNativeConf(String key) {
    String nativeKey = Hadoop2NativeConf.get(key);
    if (!Strings.isNullOrEmpty(nativeKey)) {
      return nativeKey;
    }
    if (key.startsWith(NativeConfPrefix)) {
      return key.substring(NativeConfPrefix.length()).replace(".", "_");
    }
    return "";
  }
}
