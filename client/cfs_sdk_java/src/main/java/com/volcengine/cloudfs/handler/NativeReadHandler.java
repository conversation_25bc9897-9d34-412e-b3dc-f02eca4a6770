package com.volcengine.cloudfs.handler;

import com.volcengine.cloudfs.jni.NativeReader;
import com.volcengine.cloudfs.jni.NativeResponse;

import java.io.IOException;
import java.nio.ByteBuffer;
import java.util.Objects;

public class <PERSON>ReadH<PERSON>ler extends RequestHandler<Object, Object, Integer> {
  private final ByteBuffer readBuffer;
  private final long position;
  private final NativeReader nativeReader;

  public NativeReadHandler(NativeReader nativeReader, long position, ByteBuffer readBuffer) {
    super(null);
    this.readBuffer = readBuffer;
    this.position = position;
    this.nativeReader = nativeReader;
  }

  @Override
  protected Object constructParams() {
    return null;
  }

  @Override
  protected ByteBuffer encodeParams(Object request) throws IOException {
    return null;
  }

  @Override
  protected NativeResponse callNative(ByteBuffer encodeBuffer) throws IOException {
    return nativeReader.read(this.position, this.readBuffer);
  }

  @Override
  protected Object decodeResponse(NativeResponse nativeResp) throws IOException {
    Object result = nativeResp.result();
    if (Objects.isNull(result)) {
      throw new IOException("Failed to resolve read response");
    }
    return result;
  }

  @Override
  protected Integer constructResponse(Object protoResp) {
    return ((Long) protoResp).intValue();
  }
}
