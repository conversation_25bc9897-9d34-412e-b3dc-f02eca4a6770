package com.volcengine.cloudfs.fs.coder;

import com.google.flatbuffers.FlatBufferBuilder;
import com.volcengine.cloudfs.fbs.NativeCreateRequest;
import com.volcengine.cloudfs.fbs.NativeCreateRequestObj;
import org.apache.hadoop.conf.Configuration;

import java.nio.ByteBuffer;

public class NativeCreateEncoder extends AbstractProtoEncoder<NativeCreateRequestObj> {
  public NativeCreateEncoder(NativeCreateRequestObj requestObj, Configuration conf) {
    super(requestObj, conf);
  }

  @Override
  protected ByteBuffer doEncode(NativeCreateRequestObj requestObj) {
    FlatBufferBuilder builder = newFlatBufferBuilder();
    builder.finish(NativeCreateRequest.pack(builder, requestObj));
    return builder.dataBuffer();
  }
}
