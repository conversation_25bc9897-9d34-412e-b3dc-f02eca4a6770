package com.volcengine.cloudfs.fs.io;

import java.io.EOFException;
import java.io.IOException;
import java.nio.ByteBuffer;
import java.util.EnumSet;
import java.util.Objects;

import org.apache.hadoop.conf.Configuration;
import org.apache.hadoop.fs.ByteBufferPositionedReadable;
import org.apache.hadoop.fs.ByteBufferReadable;
import org.apache.hadoop.fs.CanSetDropBehind;
import org.apache.hadoop.fs.CanSetReadahead;
import org.apache.hadoop.fs.CanUnbuffer;
import org.apache.hadoop.fs.FSInputStream;
import org.apache.hadoop.fs.HasEnhancedByteBufferAccess;
import org.apache.hadoop.fs.ReadOption;
import org.apache.hadoop.fs.StreamCapabilities;
import org.apache.hadoop.io.ByteBufferPool;
import org.apache.hadoop.util.StringUtils;

import com.volcengine.cloudfs.common.ByteBufferPoolSingleton;
import com.volcengine.cloudfs.common.CfsByteBufferPool;
import com.volcengine.cloudfs.fs.cfs.ConfigKeys;
import com.volcengine.cloudfs.fs.common.FilesystemContext;
import com.volcengine.cloudfs.fs.protocol.ReadProtocolTranslator;
import com.volcengine.cloudfs.jni.NativeReader;

import lombok.extern.slf4j.Slf4j;

/**
 * Buffered input stream 
 */
@Slf4j
public class CfsInputStream  extends FSInputStream
    implements ByteBufferReadable, CanSetDropBehind, CanSetReadahead,
    HasEnhancedByteBufferAccess, CanUnbuffer, StreamCapabilities,
    ByteBufferPositionedReadable {
  private final ReadContext readContext;
  private final ReadProtocolTranslator ioProxy;
  private ByteBuffer readBuffer = null;
  private long position;
  private final CfsByteBufferPool ioByteBufferPool;
  private byte[] oneByteBuf;

  // closed is accessed by different threads under different locks.
  private volatile boolean closed = false;

  public CfsInputStream(NativeReader reader, FilesystemContext ctx) {
    this.readContext = new ReadContext(ctx);
    this.ioProxy = new ReadProtocolTranslator(reader);

    this.ioByteBufferPool = ByteBufferPoolSingleton.getIOByteBufferPool(this.readContext.getConf());
    initReadBuffer();
  }

  private void initReadBuffer() {
    initReadBuffer(-1);
  }

  private void initReadBuffer(int capacity) {
    if (Objects.isNull(this.readBuffer)) {
      Configuration conf = this.readContext.getConf();

      int bufferSize = capacity > 0 ? capacity : conf.getInt(ConfigKeys.CFS_IO_READ_BUFFER_SIZE, ConfigKeys.CFS_IO_READ_BUFFER_SIZE_DEFAULT);
      long waitMs = conf.getLong(ConfigKeys.CFS_IO_BUFFER_POOL_ACQUIRE_WAIT_MS, ConfigKeys.CFS_IO_BUFFER_POOL_ACQUIRE_WAIT_MS_DEFAULT);

      try {
        this.readBuffer = ioByteBufferPool.acquire(bufferSize, waitMs);
        this.readBuffer.flip();
      } catch (IOException e) {
        throw new RuntimeException(e);
      }
    }
  }

  @Override
  public synchronized int read(byte b[], int off, int len) throws IOException {
    if (b == null) {
      throw new NullPointerException();
    } else if (off < 0 || len < 0 || len > b.length - off) {
      throw new IndexOutOfBoundsException();
    } else if (len == 0) {
      return 0;
    }
    return read(ByteBuffer.wrap(b, off, len));
  }

  @Override
  public int read(long position, ByteBuffer buf) throws IOException {
    if (position < 0) {
      return -1;
    }
    if (Objects.isNull(buf) || !buf.hasRemaining()) {
      return 0;
    }

    int readBytes;
    if (buf.isDirect()) {
      readBytes = ioProxy.read(position, buf);
      log.debug("PRead position {}, buffer {}, read bytes {}", position, buf, readBytes);
      if (readBytes > 0) {
        buf.position(buf.position() + readBytes);
      } else {
        // 
        return -1;
      }
    } else {
      ByteBuffer readBuf = ioByteBufferPool.acquire(this.readContext.getConf());
      try {
        readBytes = ioProxy.read(position, readBuf);
        buf.put(readBuf);
      } finally {
        ioByteBufferPool.release(readBuf);
      }
    }
    return readBytes;
  }

  @Override
  public void readFully(long position, ByteBuffer buf) throws IOException {
    int nread = 0;
    while (buf.hasRemaining()) {
      int nbytes = read(position + nread, buf);
      if (nbytes < 0) {
        throw new EOFException("End of file reached before reading fully.");
      }
      nread += nbytes;
    }
  }

  @Override
  public synchronized int read(ByteBuffer buf) throws IOException {
    if (Objects.isNull(buf) || !buf.hasRemaining()) {
      log.info("Read byte buffer is invalidate {}", buf);
      return 0;
    }
    if (Objects.isNull(this.readBuffer)) {
      initReadBuffer();
    }

    if (readBuffer.remaining() <= 0) {
      readBuffer.clear();
      int readBytes = read(this.position, readBuffer);
      readBuffer.flip();
      if (readBytes <= 0) {
        return readBytes;
      }
    }

    int readBufferLimit = readBuffer.limit();
    int readBytes = Math.min(readBuffer.remaining(), buf.remaining());
    readBuffer.limit(readBuffer.position() + readBytes);
    buf.put(readBuffer);
    readBuffer.limit(readBufferLimit);
    position += readBytes;
    return readBytes;
  }

  @Override
  public void setDropBehind(Boolean dropCache) throws IOException, UnsupportedOperationException {
    // determined by DN just mock implementation for the interface
  }

  @Override
  public synchronized void setReadahead(Long readahead) throws IOException, UnsupportedOperationException {
    int newBufSize = (int) Math.min(readahead, Integer.MAX_VALUE);

    if (Objects.isNull(this.readBuffer)) {
      initReadBuffer(newBufSize);
    } else {
      ByteBuffer oldReadBuffer = readBuffer;
      if (oldReadBuffer.capacity() == newBufSize) {
        return;
      }
      Configuration conf = this.readContext.getConf();
      long waitMs = conf.getLong(ConfigKeys.CFS_IO_BUFFER_POOL_ACQUIRE_WAIT_MS, ConfigKeys.CFS_IO_BUFFER_POOL_ACQUIRE_WAIT_MS_DEFAULT);

      ByteBuffer newReadBuffer = ioByteBufferPool.acquire(newBufSize, waitMs);

      int readableBytes = oldReadBuffer.remaining();
      if (newReadBuffer.capacity() >= readableBytes) {
        newReadBuffer.put(oldReadBuffer);
      } else {
        int maxWritableBytes = newReadBuffer.capacity();
        int abandonBytes = readableBytes - maxWritableBytes;
        int oldReadBufferLimit = oldReadBuffer.limit();
        oldReadBuffer.limit(oldReadBufferLimit - abandonBytes);
        seek(getPos() - abandonBytes);
        newReadBuffer.put(oldReadBuffer);
      }
      readBuffer = newReadBuffer;
      ioByteBufferPool.release(oldReadBuffer);
    }
  }

  @Override
  public synchronized void unbuffer() {
    if (Objects.isNull(readBuffer)) {
      return;
    }
    int readableBytes = readBuffer.remaining();
    seek(getPos() - readableBytes);
    ioByteBufferPool.release(readBuffer);
    readBuffer = null;
  }

  @Override
  public synchronized void seek(long pos) {
    long oldPos = getPos();
    if (oldPos == pos) {
      return;
    } else if (oldPos < pos) {
      long skipBytes = pos - oldPos;
      int readableBytes = readBuffer.remaining();
      if (skipBytes > readableBytes) {
        readBuffer.clear().flip();
        this.position = pos;
      } else {
        int oldBufferPos = readBuffer.position();
        readBuffer.position(oldBufferPos + (int)skipBytes);
      }
    } else {
      readBuffer.clear().flip();
      this.position = pos;
    }
  }

  @Override
  public synchronized long getPos() {
    int readableBytes = readBuffer.remaining();
    return position - readableBytes;
  }

  @Override
  public synchronized boolean seekToNewSource(long targetPos) throws IOException {
    seek(targetPos);
    // The interface handles error tolerance internally
    return true;
  }

  @Override
  public synchronized int read() throws IOException {
    if (oneByteBuf == null) {
      oneByteBuf = new byte[1];
    }
    int ret = read(oneByteBuf, 0, 1);
    return (ret <= 0) ? -1 : (oneByteBuf[0] & 0xff);
  }

  @Override
  public synchronized ByteBuffer read(ByteBufferPool factory, int maxLength, EnumSet<ReadOption> opts) throws IOException, UnsupportedOperationException {
    return null;
  }

  @Override
  public void releaseBuffer(ByteBuffer buffer) {

  }

  @Override
  public boolean hasCapability(String capability) {
    switch (StringUtils.toLowerCase(capability)) {
      case StreamCapabilities.READAHEAD:
      case StreamCapabilities.DROPBEHIND:
      case StreamCapabilities.UNBUFFER:
      case StreamCapabilities.READBYTEBUFFER:
      case StreamCapabilities.PREADBYTEBUFFER:
        return true;
      default:
        return false;
    }
  }

  @Override
  public synchronized void close() throws IOException {
    if (closed) {
      return;
    }
    closed = true;
    super.close();
    ioProxy.close();
    ioByteBufferPool.release(this.readBuffer);
  }
}
