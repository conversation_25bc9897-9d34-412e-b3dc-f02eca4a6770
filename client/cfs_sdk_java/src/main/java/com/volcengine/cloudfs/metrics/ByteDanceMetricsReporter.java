/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.volcengine.cloudfs.metrics;

import com.codahale.metrics.Counter;
import com.codahale.metrics.Gauge;
import com.codahale.metrics.Histogram;
import com.codahale.metrics.Meter;
import com.codahale.metrics.MetricFilter;
import com.codahale.metrics.MetricRegistry;
import com.codahale.metrics.ScheduledReporter;
import com.codahale.metrics.Snapshot;
import com.codahale.metrics.Timer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Locale;
import java.util.Map;
import java.util.SortedMap;
import java.util.concurrent.TimeUnit;

public class ByteDanceMetricsReporter extends ScheduledReporter {

  private static final Logger logger = LoggerFactory.getLogger(ByteDanceMetricsEmitter.class);

  private final ByteDanceMetricsEmitter emitter;

  public ByteDanceMetricsReporter(
      ByteDanceMetricsEmitter emitter,
      MetricRegistry registry, MetricFilter filter,
      TimeUnit rateUnit, TimeUnit durationUnit) {
    super(registry, "bytedance-reporter", filter, rateUnit, durationUnit);
    this.emitter = emitter;
  }

  private String format(Object o) {
    if (o instanceof Float) {
      return format(((Float) o).doubleValue());
    } else if (o instanceof Double) {
      return format(((Double) o).doubleValue());
    } else if (o instanceof Byte) {
      return format(((Byte) o).longValue());
    } else if (o instanceof Short) {
      return format(((Short) o).longValue());
    } else if (o instanceof Integer) {
      return format(((Integer) o).longValue());
    } else if (o instanceof Long) {
      return format(((Long) o).longValue());
    }
    return null;
  }

  private String format(long n) {
    return Long.toString(n);
  }

  private String format(double v) {
    // the Carbon plaintext format is pretty underspecified, but it seems like it just wants
    // US-formatted digits
    return String.format(Locale.US, "%2.2f", v);
  }


  @Override
  public void report(SortedMap<String, Gauge> gauges,
      SortedMap<String, Counter> counters,
      SortedMap<String, Histogram> histograms,
      SortedMap<String, Meter> meters,
      SortedMap<String, Timer> timers) {

    for (Map.Entry<String, Gauge> entry : gauges.entrySet()) {
      String name = entry.getKey();
      TagParser.ParsedMetricAndTags parsed = TagParser.parse(name);
      String tags = parsed.buildTags();
      emitter.emitStore(parsed.getMetricName(), format(entry.getValue().getValue()), tags);
    }

    for (Map.Entry<String, Counter> entry : counters.entrySet()) {
      String name = entry.getKey();
      TagParser.ParsedMetricAndTags parsed = TagParser.parse(name);
      String tags = parsed.buildTags();
      emitter.emitStore(parsed.getMetricName(), format(entry.getValue().getCount()), tags);
    }

    for (Map.Entry<String, Histogram> entry : histograms.entrySet()) {
      String name = entry.getKey();
      TagParser.ParsedMetricAndTags parsed = TagParser.parse(name);
      String tags = parsed.buildTags();
      String metricName = parsed.getMetricName();

      Histogram histogram = entry.getValue();
      emitter.emitStore(metricName + ".counter", format(histogram.getCount()));
      Snapshot snapshot = histogram.getSnapshot();
      if (snapshot.size() > 0) {
        emitter.emitStore(metricName + ".max", format(snapshot.getMax()), tags);
        emitter.emitStore(metricName + ".mean", format(snapshot.getMean()), tags);
        emitter.emitStore(metricName + ".min", format(snapshot.getMin()), tags);
        emitter.emitStore(metricName + ".pct50", format(snapshot.getMedian()), tags);
        emitter.emitStore(metricName + ".pct75", format(snapshot.get75thPercentile()), tags);
        emitter.emitStore(metricName + ".pct95", format(snapshot.get95thPercentile()), tags);
        emitter.emitStore(metricName + ".pct99", format(snapshot.get99thPercentile()), tags);
      }
    }

    for (Map.Entry<String, Meter> entry : meters.entrySet()) {
      String name = entry.getKey();
      TagParser.ParsedMetricAndTags parsed = TagParser.parse(name);
      String tags = parsed.buildTags();
      String metricName = parsed.getMetricName();

      Meter meter = entry.getValue();
      emitter.emitStore(metricName + ".counter",
          format(meter.getCount()), tags);
      emitter.emitStore(metricName + ".m1_rate",
          format(convertRate(meter.getOneMinuteRate())), tags);
      emitter.emitStore(metricName + ".m5_rate",
          format(convertRate(meter.getFiveMinuteRate())), tags);
      emitter.emitStore(metricName + ".m15_rate",
          format(convertRate(meter.getFifteenMinuteRate())), tags);
      emitter.emitStore(metricName + ".mean_rate",
          format(convertRate(meter.getMeanRate())), tags);
    }

    for (Map.Entry<String, Timer> entry : timers.entrySet()) {
      String name = entry.getKey();
      TagParser.ParsedMetricAndTags parsed = TagParser.parse(name);
      String tags = parsed.buildTags();
      String metricName = parsed.getMetricName();

      Timer timer = entry.getValue();
      Snapshot snapshot = timer.getSnapshot();
      if (snapshot.size() > 0) {
        emitter.emitStore(metricName + ".max",
            format(convertDuration(snapshot.getMax())), tags);
        emitter.emitStore(metricName + ".mean",
            format(convertDuration(snapshot.getMean())), tags);
        emitter.emitStore(metricName + ".min",
            format(convertDuration(snapshot.getMin())), tags);
        emitter.emitStore(metricName + ".p50",
            format(convertDuration(snapshot.getMedian())), tags);
        emitter.emitStore(metricName + ".p75",
            format(convertDuration(snapshot.get75thPercentile())), tags);
        emitter.emitStore(metricName + ".p95",
            format(convertDuration(snapshot.get95thPercentile())), tags);
        emitter.emitStore(metricName + ".p99",
            format(convertDuration(snapshot.get99thPercentile())), tags);
      }
    }
  }
}
