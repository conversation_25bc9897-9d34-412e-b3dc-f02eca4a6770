#include <iostream>
#include <string>

#include "cfs/com_volcengine_cloudfs_jni_NativeWriter.h"
#include "cloudfs2/base.h"
#include "cloudfs2/cloudfs2.h"
#include "jni/jni_util.h"

namespace cfs {
namespace jni {

JNIEXPORT jobject JNICALL Java_com_volcengine_cloudfs_jni_NativeWriter_write(
    JNIEnv* env, jobject obj, jobject write_buffer) {
  jlong data_size = env->GetDirectBufferCapacity(write_buffer);
  unsigned char* data =
      (unsigned char*)env->GetDirectBufferAddress(write_buffer);
  if (data == nullptr) {
    return nullptr;
  }

  cfs_file_handle file = JniUtil::ResolveFileHandle(env, obj);
  cfs_write(file, data, data_size);
  const char* msg = "OK";
  return JniUtil::JNativeResult(env, obj, 0, msg);
}

JNIEXPORT jobject JNICALL
Java_com_volcengine_cloudfs_jni_NativeWriter_closeFile(JNIEnv* env,
                                                       jobject obj) {
  cfs_file_handle file = JniUtil::ResolveFileHandle(env, obj);
  cfs_close(file);
  const char* msg = "OK";
  return JniUtil::JNativeResult(env, obj, 0, msg);
}

}  // namespace jni
}  // namespace cfs