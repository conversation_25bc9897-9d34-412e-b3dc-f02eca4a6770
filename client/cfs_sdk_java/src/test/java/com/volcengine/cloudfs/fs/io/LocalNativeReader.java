package com.volcengine.cloudfs.fs.io;

import com.volcengine.cloudfs.jni.NativeReader;
import com.volcengine.cloudfs.jni.NativeResponse;

import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.RandomAccessFile;
import java.nio.ByteBuffer;

public class LocalNativeReader extends NativeReader {
  private RandomAccessFile localFile;

  public LocalNativeReader(String localPath) throws FileNotFoundException {
    super(0);

    this.localFile = new RandomAccessFile(localPath, "r");
  }

  public NativeResponse read(long position, ByteBuffer buf) throws IOException {
    int readBytes = localFile.getChannel().read(buf, position);
    NativeResponse nativeResponse = new NativeResponse();
    nativeResponse.setResult(new Long(readBytes));
    return nativeResponse;
  }

  public NativeResponse closeFile() throws IOException {
    localFile.close();
    return successResp();
  }

  private NativeResponse successResp() {
    NativeResponse nativeResponse = new NativeResponse();
    nativeResponse.setStatus(0);
    return nativeResponse;
  }
}
