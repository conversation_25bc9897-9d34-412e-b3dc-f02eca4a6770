package com.volcengine.cloudfs.fs.io;

import com.volcengine.cloudfs.fs.CfsFileSystem;
import com.volcengine.cloudfs.fs.common.FilesystemContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.hadoop.conf.Configuration;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;

import java.io.File;
import java.io.IOException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

@Slf4j
public class CfsIOStreamTest {

  @Test
  @ParameterizedTest
  @ValueSource(longs = {5388608000L})
  public void testWriteReadLocal(long fileSize) throws IOException, NoSuchAlgorithmException {
    Configuration conf = new Configuration();
    FilesystemContext ctx = new FilesystemContext(conf);

    String tmpDir = System.getProperty("java.io.tmpdir");
    File localFile = new File(tmpDir, "testWriteLocal-" + fileSize);
    LocalNativeWriter writer = new LocalNativeWriter(localFile.getAbsolutePath());
    CfsFileSystem cfsFileSystem = new CfsFileSystem();
    CfsOutputStream cfsOutputStream = new CfsOutputStream(writer, ctx, cfsFileSystem);

    int bufferSize = 3571;
    byte[] buffer = TestIOUtils.GetSequenceBytes(bufferSize);
    MessageDigest md = MessageDigest.getInstance("MD5");

    // Write data
    TestIOUtils.Write(fileSize, buffer, cfsOutputStream, md);
    cfsOutputStream.close();

    // Read data
    LocalNativeReader localNativeReader = new LocalNativeReader(localFile.getAbsolutePath());
    CfsInputStream cfsInputStream = new CfsInputStream(localNativeReader, ctx);
    TestReadResult readResult = TestIOUtils.ReadFile(cfsInputStream, bufferSize, true);
    Assertions.assertEquals(fileSize, readResult.readSize);

    // Check md5
    byte[] md5Bytes = md.digest();
    String md5Hex = TestIOUtils.ByteArray2Hex(md5Bytes);

    String expectedMd5 = TestIOUtils.calculateLocalFileMD5(localFile);
    log.info("expectedMd5: " + expectedMd5 + " write md5: " + md5Hex + " read md5 " + readResult.md5);

    Assertions.assertEquals(expectedMd5, readResult.md5);
    Assertions.assertEquals(expectedMd5, md5Hex);

    localFile.deleteOnExit();
  }
}
