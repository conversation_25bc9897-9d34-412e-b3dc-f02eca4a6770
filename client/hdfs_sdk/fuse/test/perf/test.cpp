#include <fcntl.h>
#include <sys/stat.h>
#include <unistd.h>

#include <atomic>
#include <chrono>
#include <cstdio>
#include <cstdlib>
#include <cstring>
#include <ctime>
#include <fstream>
#include <iomanip>
#include <iostream>
#include <mutex>
#include <set>
#include <sstream>
#include <string>
#include <thread>
#include <utility>
#include <vector>

static const char* MOUNT_POINT = std::getenv("MOUNT_POINT");
static const int MOUNT_POINT_MAX_INDEX =
    std::atoi(std::getenv("MOUNT_POINT_MAX_INDEX"));
static const char* FILE_LIST = std::getenv("FILE_LIST");
static const char* RANDOM = std::getenv("RANDOM_READ");
static const char* CONTINUE = std::getenv("CONTINUE");
static const char* SAVE_PROGRESS = std::getenv("SAVE_PROGRESS");
static const char* META_ONLY = std::getenv("META_ONLY");
static int FILE_COUNT = std::atoi(std::getenv("FILE_COUNT"));
static int RUNNER_COUNT = std::atoi(std::getenv("RUNNER_COUNT"));
static int RUNNER_INDEX = std::atoi(std::getenv("RUNNER_INDEX"));
static int THREAD = std::atoi(std::getenv("THREAD"));

static int TOTAL_FILE_COUNT;
static int FILE_COUNT_CUR_RUNNER;
static int FILE_START_INDEX_CUR_RUNNER;
static int FILE_END_INDEX_CUR_RUNNER;
static int FILE_COUNT_PER_THREAD;

static std::mutex progress_mtx;
static std::fstream progress;

static std::atomic<int> file_count;
static std::atomic<int> load_count;
static std::chrono::system_clock::time_point start_time;
static std::atomic<unsigned long long> stat_time_cost;
static std::atomic<unsigned long long> open_time_cost;
static std::atomic<unsigned long long> read_time_cost;
static std::vector<std::string> files;
static std::set<std::pair<int, int>> loaded_files;

static std::mutex worker_mutex;
static std::vector<bool> alive_workers;

void print_qps() {
    while (true) {
        std::this_thread::sleep_for(std::chrono::seconds(1));
        auto now = std::chrono::system_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::nanoseconds>(
            now - start_time);
        int cur_load = load_count.load();
        int worker_cnt = 0;
        worker_mutex.lock();
        for (size_t i = 0; i < alive_workers.size(); i++) {
            worker_cnt += alive_workers[i];
            alive_workers[i] = false;
        }
        worker_mutex.unlock();
        std::ostringstream ss;
        if (isatty(fileno(stdout))) {
            std::cout << "\r";
        } else {
            std::time_t now_c = std::chrono::system_clock::to_time_t(now);
            std::tm now_tm = *std::localtime(&now_c);
            char time_b[80];
            strftime(time_b, 80, "%c ", &now_tm);
            ss << time_b;
        }
        ss << "Progress: " << cur_load;
        if (RANDOM == NULL) {
            ss << "/" << FILE_COUNT_CUR_RUNNER << " " << std::fixed
               << std::setprecision(2) << "("
               << static_cast<double>(cur_load) * 100 / FILE_COUNT_CUR_RUNNER
               << "%)";
        } else {
            ss << " file(s)";
        }
        ss << ". Alive worker cnt: " << worker_cnt << std::setprecision(3)
           << ". File per second: "
           << static_cast<double>(file_count.load()) / duration.count() * 1000 *
                  1000 * 1000
           << ", stat cost "
           << static_cast<double>(stat_time_cost.load()) / file_count.load() /
                  1000 / 1000
           << ", open cost "
           << static_cast<double>(open_time_cost.load()) / file_count.load() /
                  1000 / 1000
           << ", read cost "
           << static_cast<double>(read_time_cost.load()) / file_count.load() /
                  1000 / 1000
           << "(ms).";
        std::cout << ss.str();
        if (isatty(fileno(stdout))) {
            std::cout << std::flush;
        } else {
            std::cout << std::endl;
        }
        if (RANDOM == NULL && cur_load == FILE_COUNT_CUR_RUNNER) {
            std::cout << std::endl << "Load task finished" << std::endl;
            break;
        }
        start_time = now;
        file_count.store(1);
        stat_time_cost.store(1);
        open_time_cost.store(1);
        read_time_cost.store(1);
    }
}

void load_file_names() {
    std::ifstream file_list(FILE_LIST);
    for (std::string line; std::getline(file_list, line);) {
        files.push_back(line);
    }

    if (FILE_COUNT == -1) {
        FILE_COUNT = files.size();
    }

    TOTAL_FILE_COUNT = FILE_COUNT;
    if (MOUNT_POINT_MAX_INDEX != -1) {
        TOTAL_FILE_COUNT *= MOUNT_POINT_MAX_INDEX;
    }
}

void load_progress() {
    progress.open("load_progress", std::fstream::in);
    int mt(0), file(0);
    while (CONTINUE != NULL && progress >> mt >> file) {
        loaded_files.insert({mt, file});
    }
    load_count.store(loaded_files.size());
    progress.close();
    progress.open("load_progress", std::fstream::out | std::fstream::app);
}

void calc_task() {
    FILE_COUNT_CUR_RUNNER = TOTAL_FILE_COUNT / RUNNER_COUNT;
    FILE_START_INDEX_CUR_RUNNER = FILE_COUNT_CUR_RUNNER * RUNNER_INDEX;
    if (RUNNER_INDEX + 1 == RUNNER_COUNT) {
        FILE_END_INDEX_CUR_RUNNER = TOTAL_FILE_COUNT;
    } else {
        FILE_END_INDEX_CUR_RUNNER = FILE_COUNT_CUR_RUNNER * (RUNNER_INDEX + 1);
    }
    FILE_COUNT_CUR_RUNNER =
        FILE_END_INDEX_CUR_RUNNER - FILE_START_INDEX_CUR_RUNNER;
    FILE_COUNT_PER_THREAD = FILE_COUNT_CUR_RUNNER / THREAD;

    std::cout << "Runner info: "
              << " file start " << FILE_START_INDEX_CUR_RUNNER << " file end "
              << FILE_END_INDEX_CUR_RUNNER << " file per thread "
              << FILE_COUNT_PER_THREAD << std::endl;
}

void save_progress(int mt, int file) {
    if (SAVE_PROGRESS == NULL) {
        return;
    }
    std::lock_guard<std::mutex> guard(progress_mtx);
    loaded_files.insert({mt, file});
    progress << mt << " " << file << std::endl;
}

void get_file_idx_random(int* mt, int* file) {
    if (MOUNT_POINT_MAX_INDEX != -1) {
        *mt = (rand() % MOUNT_POINT_MAX_INDEX) + 1;
    }
    *file = rand() % FILE_COUNT;
}

void get_file_idx_ordered(int* mt, int* file, int* file_index, int max_index) {
    std::lock_guard<std::mutex> lock(progress_mtx);
    do {
        *mt = ((*file_index) / FILE_COUNT) + 1;
        *file = (*file_index) % FILE_COUNT;
        (*file_index)++;
        auto it = loaded_files.find({*mt, *file});
        if (it == loaded_files.end()) {
            break;
        }
        std::cerr << "Duplicated index " << *mt << " " << *file << std::endl;
    } while (*file_index < max_index);
}

void read_one_file(int th_idx) {
    bool retry = false;
    int file_index =
        th_idx * FILE_COUNT_PER_THREAD + FILE_START_INDEX_CUR_RUNNER;
    int end_index =
        (th_idx + 1) * FILE_COUNT_PER_THREAD + FILE_START_INDEX_CUR_RUNNER;
    if (th_idx + 1 == THREAD) {
        end_index = FILE_END_INDEX_CUR_RUNNER;
    }
    int mt(-1), file(-1);
    std::string path_str;
    do {
        if (!retry) {
            if (RANDOM != NULL) {
                get_file_idx_random(&mt, &file);
            } else {
                get_file_idx_ordered(&mt, &file, &file_index, end_index);
            }
            path_str = MOUNT_POINT;
            if (MOUNT_POINT_MAX_INDEX != -1) {
                path_str += "-" + std::to_string(mt);
            }
            path_str += "/train/" + files[file];
        }
        const char* path = path_str.c_str();
        int ret = 0;
        struct stat stat_buf;
        {
            auto s = std::chrono::system_clock::now();
            ret = stat(path, &stat_buf);
            auto e = std::chrono::system_clock::now();
            auto time =
                std::chrono::duration_cast<std::chrono::nanoseconds>(e - s)
                    .count();
            stat_time_cost.fetch_add(time);
        }
        if (ret == -1) {
            std::cerr << "Failed to stat " << path << " " << strerror(errno)
                      << std::endl;
            retry = true;
            continue;
        }

        int fd;
        {
            auto s = std::chrono::system_clock::now();
            fd = open(path, O_RDONLY);
            auto e = std::chrono::system_clock::now();
            auto time =
                std::chrono::duration_cast<std::chrono::nanoseconds>(e - s)
                    .count();
            open_time_cost.fetch_add(time);
        }
        if (fd == -1) {
            std::cerr << "Failed to open " << path << " " << strerror(errno)
                      << std::endl;
            retry = true;
            continue;
        }
        if (META_ONLY == NULL) {
            auto s = std::chrono::system_clock::now();
            char* buf = new char[stat_buf.st_size];
            int total_read = 0;
            do {
                ret = read(fd, buf, stat_buf.st_size);
                if (ret == -1) {
                    std::cerr << "Failed to read " << path << " "
                              << strerror(errno) << std::endl;
                    retry = true;
                    break;
                }
                total_read += ret;
                retry = false;
            } while (ret > 0 && total_read < stat_buf.st_size);
            delete[] buf;
            auto e = std::chrono::system_clock::now();
            auto time =
                std::chrono::duration_cast<std::chrono::nanoseconds>(e - s)
                    .count();
            read_time_cost.fetch_add(time);
        }
        close(fd);
        file_count.fetch_add(1);
        load_count.fetch_add(1);
        save_progress(mt, file);
        worker_mutex.lock();
        alive_workers[th_idx] = true;
        worker_mutex.unlock();
    } while (file_index < end_index);
}

int main(int argc, char* argv[]) {
    (void)argc;
    (void)argv;

    load_file_names();
    calc_task();
    load_progress();

    srand(time(NULL));

    file_count.store(1);
    start_time = std::chrono::system_clock::now();

    auto t = std::thread(print_qps);

    alive_workers.resize(THREAD);
    for (int i = 0; i < THREAD; i++) {
        auto tmp = std::thread(read_one_file, i);
        tmp.detach();
    }

    t.join();

    return 0;
}
