#include <assert.h>
#include <dirent.h>
#include <errno.h>
#include <fcntl.h>
#include <getopt.h>
#include <stdio.h>
#include <stdlib.h>
#include <sys/stat.h>
#include <sys/types.h>
#include <sys/xattr.h>
#include <time.h>
#include <unistd.h>
#include <utime.h>

#include <cstring>
#include <string>

char* TEST_PATH;
#define FUNC_NAME_LEN 16

static struct option long_options[] = {
    {"mount_path", required_argument, NULL, 'm'},
};

int test_setup() {
    char cmd[64];
    sprintf(cmd, "mkdir %s -p", TEST_PATH);
    return system(cmd);
}

int test_teardown() {
    char cmd[64];
    sprintf(cmd, "rm -rf %s", TEST_PATH);
    return system(cmd);
}

int test_getattr() {
    fprintf(stdout, "%-*s", FUNC_NAME_LEN, __func__);
    struct stat stat_buf;
    memset(&stat_buf, 0, sizeof(stat_buf));
    int ret = stat(TEST_PATH, &stat_buf);
    if (ret != 0) {
        return __LINE__;
    }
    if (!S_ISDIR(stat_buf.st_mode)) {
        return __LINE__;
    }
    return 0;
}

// Do not need test because link cannot be created.
int test_readlink() {
    fprintf(stdout, "%-*s", FUNC_NAME_LEN, __func__);
    return 0;
}

int test_mknod() {
    fprintf(stdout, "%-*s", FUNC_NAME_LEN, __func__);
    char buf[64];
    memset(buf, 0, 64);
    sprintf(buf, "%s/test_mknod_reg", TEST_PATH);
    if (mknod(buf, S_IFREG | S_IRWXU | S_IRWXG | S_IRWXO, 0) != 0) {
        return __LINE__;
    }
    if (access(buf, F_OK | R_OK | W_OK | X_OK) != 0) {
        return __LINE__;
    }
    memset(buf, 0, 64);
    sprintf(buf, "%s/test_mknod_fifo", TEST_PATH);
    if (mknod(buf, S_IFIFO, 0) == 0) {
        return __LINE__;
    }
    memset(buf, 0, 64);
    sprintf(buf, "%s/test_mknod_blk", TEST_PATH);
    if (mknod(buf, S_IFBLK, 0) == 0) {
        return __LINE__;
    }
    memset(buf, 0, 64);
    sprintf(buf, "%s/test_mknod_chr", TEST_PATH);
    if (mknod(buf, S_IFCHR, 0) == 0) {
        return __LINE__;
    }
    memset(buf, 0, 64);
    sprintf(buf, "%s/test_mknod_dir", TEST_PATH);
    if (mknod(buf, S_IFDIR, 0) == 0) {
        return __LINE__;
    }
    return 0;
}

int test_mkdir() {
    fprintf(stdout, "%-*s", FUNC_NAME_LEN, __func__);
    char buf[64];
    memset(buf, 0, 64);
    sprintf(buf, "%s/test_mkdir", TEST_PATH);
    if (mkdir(buf, S_IFDIR | S_IRWXU | S_IRWXG | S_IRWXO) != 0) {
        return __LINE__;
    }
    memset(buf, 0, 64);
    sprintf(buf, "%s/test_mkdir_parent/test_mkdir_child", TEST_PATH);
    if (mkdir(buf, S_IFDIR | S_IRWXU | S_IRWXG | S_IRWXO) == 0) {
        return __LINE__;
    }
    return 0;
}

int test_unlink() {
    fprintf(stdout, "%-*s", FUNC_NAME_LEN, __func__);
    char buf[64];
    memset(buf, 0, 64);
    sprintf(buf, "%s/test_unlink", TEST_PATH);
    if (mknod(buf, S_IFREG | S_IRWXU | S_IRWXG | S_IRWXO, 0) != 0) {
        return __LINE__;
    }
    if (unlink(buf) != 0) {
        return __LINE__;
    }
    return 0;
}

int test_rmdir() {
    fprintf(stdout, "%-*s", FUNC_NAME_LEN, __func__);
    char buf[64];
    memset(buf, 0, 64);
    sprintf(buf, "%s/test_rmdir", TEST_PATH);
    if (mkdir(buf, S_IFDIR | S_IRWXU | S_IRWXG | S_IRWXO) != 0) {
        return __LINE__;
    }
    char buf2[64];
    memset(buf2, 0, 64);
    sprintf(buf2, "%s/test_rmdir/file", TEST_PATH);
    if (mknod(buf2, S_IFREG | S_IRWXU | S_IRWXG | S_IRWXO, 0) != 0) {
        return __LINE__;
    }
    if (rmdir(buf) == 0) {
        return __LINE__;
    }
    if (unlink(buf2) != 0) {
        return __LINE__;
    }
    if (rmdir(buf) != 0) {
        return __LINE__;
    }
    return 0;
}

int test_symlink() {
    fprintf(stdout, "%-*s", FUNC_NAME_LEN, __func__);
    char buf[64];
    memset(buf, 0, 64);
    sprintf(buf, "%s/test_symlink_file", TEST_PATH);
    if (mknod(buf, S_IFREG | S_IRWXU | S_IRWXG | S_IRWXO, 0) != 0) {
        return __LINE__;
    }
    char buf2[64];
    memset(buf2, 0, 64);
    sprintf(buf2, "%s/test_symlink_link", TEST_PATH);
    if (symlink(buf, buf2) == 0) {
        return __LINE__;
    }
    return 0;
}

int test_rename() {
    fprintf(stdout, "%-*s", FUNC_NAME_LEN, __func__);
    char buf[64];
    memset(buf, 0, 64);
    sprintf(buf, "%s/test_rename_from", TEST_PATH);
    if (mknod(buf, S_IFREG | S_IRWXU | S_IRWXG | S_IRWXO, 0) != 0) {
        return __LINE__;
    }
    char buf2[64];
    memset(buf2, 0, 64);
    sprintf(buf2, "%s/test_rename_to", TEST_PATH);
    if (rename(buf, buf2) != 0) {
        return __LINE__;
    }
    if (access(buf2, F_OK) != 0) {
        return __LINE__;
    }
    memset(buf, 0, 64);
    sprintf(buf, "%s/test_rename_dir_from", TEST_PATH);
    if (mkdir(buf, S_IFDIR | S_IRWXU | S_IRWXG | S_IRWXO) != 0) {
        return __LINE__;
    }
    memset(buf2, 0, 64);
    sprintf(buf2, "%s/test_rename_dir_to", TEST_PATH);
    if (rename(buf, buf2) != 0) {
        return __LINE__;
    }
    if (access(buf2, F_OK) != 0) {
        return __LINE__;
    }
    return 0;
}

int test_link() {
    fprintf(stdout, "%-*s", FUNC_NAME_LEN, __func__);
    char buf[64];
    memset(buf, 0, 64);
    sprintf(buf, "%s/test_link_file", TEST_PATH);
    if (mknod(buf, S_IFREG | S_IRWXU | S_IRWXG | S_IRWXO, 0) != 0) {
        return __LINE__;
    }
    char buf2[64];
    memset(buf2, 0, 64);
    sprintf(buf2, "%s/test_link_link", TEST_PATH);
    if (link(buf, buf2) == 0) {
        return __LINE__;
    }
    return 0;
}

int test_chmod() {
    fprintf(stdout, "%-*s", FUNC_NAME_LEN, __func__);
    char buf[64];
    memset(buf, 0, 64);
    sprintf(buf, "%s/test_chmod", TEST_PATH);
    if (mknod(buf, S_IFREG | S_IRWXU | S_IRWXG | S_IRWXO, 0) != 0) {
        return __LINE__;
    }
    if (chmod(buf, 0) != 0) {
        return __LINE__;
    }
    struct stat stat_buf;
    if (stat(buf, &stat_buf) != 0) {
        return __LINE__;
    }
    if ((stat_buf.st_mode & ~S_IFMT) != 0) {
        return __LINE__;
    }
    return 0;
}

// chown is not supported by dancenn.
int test_chown() {
    fprintf(stdout, "%-*s", FUNC_NAME_LEN, __func__);
    return 0;
}

int test_truncate() {
    fprintf(stdout, "%-*s", FUNC_NAME_LEN, __func__);
    char buf[64];
    memset(buf, 0, 64);
    sprintf(buf, "%s/test_truncate", TEST_PATH);
    if (mknod(buf, S_IFREG | S_IRWXU | S_IRWXG | S_IRWXO, 0) != 0) {
        return __LINE__;
    }
    if (truncate(buf, 0) == 0) {
        return __LINE__;
    }
    return 0;
}

// fopen calls open in the background.
int test_open() {
    fprintf(stdout, "%-*s", FUNC_NAME_LEN, __func__);
    FILE* f;
    char buf[64];
    memset(buf, 0, 64);
    sprintf(buf, "%s/test_open", TEST_PATH);
    if (fopen(buf, "r") != NULL) {
        return __LINE__;
    }
    if (mknod(buf, S_IFREG | S_IRWXU | S_IRWXG | S_IRWXO, 0) != 0) {
        return __LINE__;
    }
    if ((f = fopen(buf, "r")) == NULL) {
        return __LINE__;
    }
    if (fclose(f) != 0) {
        return __LINE__;
    }
    if ((f = fopen(buf, "rb")) == NULL) {
        return __LINE__;
    }
    if (fclose(f) != 0) {
        return __LINE__;
    }
    if ((f = fopen(buf, "w")) == NULL) {
        return __LINE__;
    }
    if (fclose(f) != 0) {
        return __LINE__;
    }
    // if ((f = fopen(buf, "a")) == NULL) {
    //     return __LINE__;
    // }
    // if (fclose(f) != 0) {
    //     return __LINE__;
    // }
    // if ((f = fopen(buf, "ab")) == NULL) {
    //     return __LINE__;
    // }
    // if (fclose(f) != 0) {
    //     return __LINE__;
    // }
    if ((f = fopen(buf, "r+")) == NULL) {
        return __LINE__;
    }
    if (fclose(f) != 0) {
        return __LINE__;
    }
    if ((f = fopen(buf, "w+")) == NULL) {
        return __LINE__;
    }
    if (fclose(f) != 0) {
        return __LINE__;
    }
    if ((f = fopen(buf, "a+")) != NULL) {
        return __LINE__;
    }
    return 0;
}

// This test covers open, read, write, truncate, flush, release.
int test_io() {
    fprintf(stdout, "%-*s", FUNC_NAME_LEN, __func__);
    char buf[64];
    memset(buf, 0, 64);
    sprintf(buf, "%s/test_io", TEST_PATH);
    if (mknod(buf, S_IFREG | S_IRWXU | S_IRWXG | S_IRWXO, 0) != 0) {
        return __LINE__;
    }
    const char* test_str = "test_write";
    char write_buf[64];
    memset(write_buf, 0, 64);
    memcpy(write_buf, test_str, strlen(test_str) + 1);
    FILE* fw;
    if ((fw = fopen(buf, "w")) == NULL) {
        return __LINE__;
    }
    if (fwrite(write_buf, 1, strlen(test_str) + 1, fw) <= 0) {
        return __LINE__;
    }
    int ret = fseek(fw, 16, SEEK_SET);
    if (ret != 0) {
        return __LINE__;
    }
    if (truncate(buf, 0) == 0) {
        return __LINE__;
    }
    if (fclose(fw) != 0) {
        return __LINE__;
    }
    FILE* fr;
    char read_buf[64];
    memset(read_buf, 0, 64);
    if ((fr = fopen(buf, "r")) == NULL) {
        return __LINE__;
    }
    if (fread(read_buf, 1, strlen(test_str) + 1, fr) <= 0) {
        return __LINE__;
    }
    if (strcmp(test_str, read_buf) != 0) {
        return __LINE__;
    }
    memset(read_buf, 0, 64);
    if (fseek(fr, 0, SEEK_SET) != 0) {
        return __LINE__;
    }
    if (fread(read_buf, 1, strlen(test_str) + 1, fr) <= 0) {
        return __LINE__;
    }
    if (strcmp(test_str, read_buf) != 0) {
        return __LINE__;
    }
    if (fclose(fr) != 0) {
        return __LINE__;
    }
    return 0;
}

int test_dup() {
    fprintf(stdout, "%-*s", FUNC_NAME_LEN, __func__);
    FILE* fw;
    char buf[64];
    memset(buf, 0, 64);
    sprintf(buf, "%s/test_dup", TEST_PATH);
    if (mknod(buf, S_IFREG | S_IRWXU | S_IRWXG | S_IRWXO, 0) != 0) {
        return __LINE__;
    }
    const char* test_str = "test_write";
    if ((fw = fopen(buf, "w")) == NULL) {
        return __LINE__;
    }
    if (fwrite(test_str, 1, strlen(test_str) + 1, fw) <= 0) {
        return __LINE__;
    }
    if (fclose(fw) != 0) {
        return __LINE__;
    }
    int fd1, fd2;
    if ((fd1 = open(buf, O_RDONLY)) <= 0) {
        return __LINE__;
    }
    fd2 = dup(fd1);
    memset(buf, 0, 64);
    if (read(fd1, buf, strlen(test_str)) <= 0) {
        return __LINE__;
    }
    if (pread(fd1, buf, strlen(test_str) - 4, 4) <= 0) {
        return __LINE__;
    }
    if (lseek(fd2, 0, SEEK_SET) != 0) {
        return __LINE__;
    }
    if (close(fd2) != 0) {
        return __LINE__;
    }
    memset(buf, 0, 64);
    if (read(fd1, buf, strlen(test_str)) <= 0) {
        return __LINE__;
    }
    if (strcmp(buf, test_str) != 0) {
        return __LINE__;
    }
    if (close(fd1) != 0) {
        return __LINE__;
    }
    return 0;
}

int test_fsstat() {
    fprintf(stdout, "%-*s", FUNC_NAME_LEN, __func__);
    return 0;
}

// too hard to test
int test_fsync() {
    fprintf(stdout, "%-*s", FUNC_NAME_LEN, __func__);
    return 0;
}

int test_setxattr() {
    fprintf(stdout, "%-*s", FUNC_NAME_LEN, __func__);
    char buf[64];
    memset(buf, 0, 64);
    sprintf(buf, "%s/test_setxattr", TEST_PATH);
    if (mknod(buf, S_IFREG | S_IRWXU | S_IRWXG | S_IRWXO, 0) != 0) {
        return __LINE__;
    }
    if (setxattr(buf, "xattr_name", "xattr_value", 12, 0) != -1) {
        return __LINE__;
    }
    return 0;
}

int test_getxattr() {
    fprintf(stdout, "%-*s", FUNC_NAME_LEN, __func__);
    char buf[64];
    memset(buf, 0, 64);
    sprintf(buf, "%s/test_getxattr", TEST_PATH);
    if (mknod(buf, S_IFREG | S_IRWXU | S_IRWXG | S_IRWXO, 0) != 0) {
        return __LINE__;
    }
    char value[64];
    if (getxattr(buf, "xattr_name", value, 64) != -1) {
        return __LINE__;
    }
    return 0;
}

int test_listxattr() {
    fprintf(stdout, "%-*s", FUNC_NAME_LEN, __func__);
    char buf[64];
    memset(buf, 0, 64);
    sprintf(buf, "%s/test_listxattr", TEST_PATH);
    if (mknod(buf, S_IFREG | S_IRWXU | S_IRWXG | S_IRWXO, 0) != 0) {
        return __LINE__;
    }
    char list[64];
    if (listxattr(buf, list, 64) != -1) {
        return __LINE__;
    }
    return 0;
}

int test_removexattr() {
    fprintf(stdout, "%-*s", FUNC_NAME_LEN, __func__);
    char buf[64];
    memset(buf, 0, 64);
    sprintf(buf, "%s/test_removexattr", TEST_PATH);
    if (mknod(buf, S_IFREG | S_IRWXU | S_IRWXG | S_IRWXO, 0) != 0) {
        return __LINE__;
    }
    char name[64];
    if (removexattr(buf, name) != -1) {
        return __LINE__;
    }
    return 0;
}

// This test covers opendir, readdir, fsyncdir, releasedir
int test_dir() {
    fprintf(stdout, "%-*s", FUNC_NAME_LEN, __func__);
    char dir[64];
    memset(dir, 0, 64);
    sprintf(dir, "%s/test_dir", TEST_PATH);
    if (mkdir(dir, S_IFDIR | S_IRWXU | S_IRWXG | S_IRWXO) != 0) {
        return __LINE__;
    }
    char file1[64], file2[64];
    memset(file1, 0, 64);
    memset(file2, 0, 64);
    sprintf(file1, "%s/test_dir/file1", TEST_PATH);
    sprintf(file2, "%s/test_dir/file2", TEST_PATH);
    if (mknod(file1, S_IFREG | S_IRWXU | S_IRWXG | S_IRWXO, 0) != 0) {
        return __LINE__;
    }
    if (mknod(file2, S_IFREG | S_IRWXU | S_IRWXG | S_IRWXO, 0) != 0) {
        return __LINE__;
    }
    DIR* dp;
    if ((dp = opendir(dir)) == NULL) {
        return __LINE__;
    }
    struct dirent* de;
    if ((de = readdir(dp)) == NULL) {
        return __LINE__;
    }
    if (de->d_type != DT_UNKNOWN || strcmp(de->d_name, ".") != 0) {
        fprintf(stdout, "%d %s\n", de->d_type, de->d_name);
        return __LINE__;
    }
    if ((de = readdir(dp)) == NULL) {
        return __LINE__;
    }
    if (de->d_type != DT_UNKNOWN || strcmp(de->d_name, "..") != 0) {
        fprintf(stdout, "%d %s\n", de->d_type, de->d_name);
        return __LINE__;
    }
    if ((de = readdir(dp)) == NULL) {
        return __LINE__;
    }
    if (de->d_type != DT_REG || strcmp(de->d_name, "file1") != 0) {
        fprintf(stdout, "%d %s\n", de->d_type, de->d_name);
        return __LINE__;
    }
    if ((de = readdir(dp)) == NULL) {
        return __LINE__;
    }
    if (de->d_type != DT_REG || strcmp(de->d_name, "file2") != 0) {
        fprintf(stdout, "%d %s\n", de->d_type, de->d_name);
        return __LINE__;
    }
    if (readdir(dp) != NULL) {
        return __LINE__;
    }
    int fd;
    if ((fd = open(dir, O_RDONLY)) == -1) {
        return __LINE__;
    }
    if (fsync(fd) != 0) {
        return __LINE__;
    }
    if (close(fd) != 0) {
        return __LINE__;
    }
    if (closedir(dp) != 0) {
        return __LINE__;
    }
    return 0;
}

int test_access() {
    fprintf(stdout, "%-*s", FUNC_NAME_LEN, __func__);
    char buf[64];
    memset(buf, 0, 64);
    sprintf(buf, "%s/test_access", TEST_PATH);
    if (mknod(buf, S_IFREG | S_IRWXU | S_IRWXG | S_IRWXO, 0) != 0) {
        return __LINE__;
    }
    if (access(buf, R_OK | W_OK | X_OK) != 0) {
        return __LINE__;
    }
    return 0;
}

int test_utimes() {
    fprintf(stdout, "%-*s", FUNC_NAME_LEN, __func__);
    char buf[64];
    memset(buf, 0, 64);
    sprintf(buf, "%s/test_utimes", TEST_PATH);
    if (mknod(buf, S_IFREG | S_IRWXU | S_IRWXG | S_IRWXO, 0) != 0) {
        return __LINE__;
    }
    sleep(1);
    struct utimbuf tb;
    time(&tb.actime);
    tb.modtime = tb.actime;
    if (utime(buf, &tb) != 0) {
        return __LINE__;
    }
    struct stat st;
    if (stat(buf, &st) != 0) {
        return __LINE__;
    }
    if (st.st_atim.tv_sec != st.st_mtim.tv_sec &&
        st.st_atim.tv_sec != tb.actime) {
        return __LINE__;
    }
    return 0;
}

int (*funcs[])() = {
    test_getattr,     test_readlink, test_mknod,    test_mkdir,
    test_unlink,      test_rmdir,    test_symlink,  test_rename,
    test_link,        test_chmod,    test_chown,    test_truncate,
    test_open,        test_io,       test_dup,      test_fsstat,
    test_fsync,       test_setxattr, test_getxattr, test_listxattr,
    test_removexattr, test_dir,      test_access,   test_utimes,
};

int main(int argc, char* argv[]) {
    std::string mount_path;
    int index = 0;
    int c = 0;
    while (EOF != (c = getopt_long(argc, argv, "m:", long_options, &index))) {
        switch (c) {
            case 'm':
                fprintf(stdout, "get option -m, mount path is %s\n", optarg);
                mount_path = optarg;
                break;
            case '?':
                fprintf(stdout, "unknow option: %d\n", optopt);
                break;
            default:
                break;
        }
    }
    char test_path[256];
    TEST_PATH = test_path;
    memset(TEST_PATH, 0, 256);
    sprintf(TEST_PATH, "%s/tmp/test-%ld", mount_path.c_str(),
            (unsigned long)time(NULL));
    fprintf(stdout, "test path is %s\n", TEST_PATH);
    if (test_setup() != 0) {
        fprintf(stderr, "test_setup failed: %s\nCleanup %s manually\n",
                strerror(errno), TEST_PATH);
    }

    for (int i = 0; i < (int)(sizeof(funcs) / sizeof(int (*)())); i++) {
        fprintf(stdout, "[%02d] ", i + 1);
        int ret = funcs[i]();
        if (ret == 0) {
            fprintf(stdout, "\tPASS\n");
        } else {
            fprintf(stdout, "\tFAIL\t[%4d] %s\n", ret, strerror(errno));
        }
    }

    fprintf(stdout, "bmap, ioctl, poll, fallocate are not tested\n");
    if (test_teardown() != 0) {
        fprintf(stderr, "test_teardown failed: %s\nCleanup %s manually\n",
                strerror(errno), TEST_PATH);
    }
}