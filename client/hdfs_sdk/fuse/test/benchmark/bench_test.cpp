#include <fcntl.h>
#include <gflags/gflags.h>
#include <string.h>
#include <unistd.h>

#include <algorithm>
#include <atomic>
#include <chrono>
#include <functional>
#include <iostream>
#include <sstream>
#include <string>
#include <thread>
#include <vector>

DEFINE_int32(jobs, 1, "Number of threads to read/write");
DEFINE_string(test_dir, "/cfs_bench_dir", "Dir for read/write files");
DEFINE_string(op, "W", "Supported ops: W,R,RR");
DEFINE_int64(buf_len, 4 * 1024 * 1024, "Buffer size for one read/write");
DEFINE_int64(read_skip_bytes, 512 * 1024, "skip bytes for one random read");
DEFINE_int64(file_len, 4LLU * 1024 * 1024 * 1024, "File size");
DEFINE_bool(verbose, false, "Print actual io size");
DEFINE_int64(hist_ms, 1000, "Interval for speed histgram");
DEFINE_int64(sleep_second, 0, "spleep second time for each loop");

void WriteFunc(size_t id, const char* wbuf,
               std::atomic<int64_t>* realtime_bytes) {
    std::string path = FLAGS_test_dir + "/file_" + std::to_string(id);
    FILE* fr = fopen(path.c_str(), "w");
    if (fr == nullptr) {
        std::cerr << "Fail to open file for write, path=" << path
                  << ", errno=" << errno << std::endl;
        return;
    }
    int64_t finish = 0;
    int sleep_time = FLAGS_sleep_second;
    while (finish < FLAGS_file_len) {
        int32_t res = fwrite(wbuf, 1, FLAGS_buf_len, fr);
        if (res < 0) {
            std::cerr << "Fail to write data, id=" << id << ", errno=" << errno
                      << std::endl;
            return;
        }
        finish += res;
        realtime_bytes->fetch_add(res, std::memory_order_release);
        std::this_thread::sleep_for(std::chrono::seconds(sleep_time));
    }
    if (fclose(fr) != 0) {
        std::cerr << "Fail to close file, id=" << id << ", errno=" << errno
                  << std::endl;
    }
}

void ReadFunc(size_t id, std::atomic<int64_t>* realtime_bytes) {
    std::string path = FLAGS_test_dir + "/file_" + std::to_string(id);
    FILE* fr = fopen(path.c_str(), "r");
    if (fr == nullptr) {
        std::cerr << "Fail to open file for read, path=" << path
                  << ", errno=" << errno << std::endl;
        return;
    }

    int64_t finish = 0;
    char* rbuf = new char[FLAGS_buf_len];
    int sleep_time = FLAGS_sleep_second;
    while (finish < FLAGS_file_len) {
        int32_t res = fread(rbuf, 1, FLAGS_buf_len, fr);
        if (res < 0) {
            std::cerr << "Fail to read data, id=" << id << ", errno=" << errno
                      << std::endl;
            return;
        }
        if (res == 0) {
            std::cerr << "Reach EOF when read, id=" << id << std::endl;
        }
        if (FLAGS_verbose) {
            std::cout << "read io size: " << res << std::endl;
        }
        finish += res;
        realtime_bytes->fetch_add(res, std::memory_order_release);
        std::this_thread::sleep_for(std::chrono::seconds(sleep_time));
    }
    delete[] rbuf;

    if (fclose(fr) != 0) {
        std::cerr << "Fail to close file, id=" << id << ", errno=" << errno
                  << std::endl;
    }
}

void RandomReadFunc(size_t id, std::atomic<int64_t>* realtime_bytes) {
    std::string path = FLAGS_test_dir + "/file_" + std::to_string(id);
    FILE* fr = fopen(path.c_str(), "r");
    if (fr == nullptr) {
        std::cerr << "Fail to open file for read, path=" << path
                  << ", errno=" << errno << std::endl;
        return;
    }
    int fd = fileno(fr);
    int64_t already_read_bytes = 0;
    // int64_t buffer_len = std::max(FLAGS_buf_len, FLAGS_read_skip_bytes * 4);
    int64_t buffer_len = FLAGS_buf_len;
    // int64_t skip_number = buffer_len / FLAGS_read_skip_bytes;
    char* rbuf = new char[buffer_len];
    int sleep_time = FLAGS_sleep_second;
    int loop_count = 0;
    while (already_read_bytes < FLAGS_file_len) {
        if (loop_count % 2 != 0 &&
            already_read_bytes + FLAGS_read_skip_bytes < FLAGS_file_len) {
            off_t ret = lseek(fd, FLAGS_read_skip_bytes, SEEK_CUR);
            if (ret == -1) {
                std::cerr << "Fail to seek data, errno = " << errno
                          << std::endl;
            }
            if (ret != FLAGS_read_skip_bytes) {
                std::cout << "real seek ret " << ret
                          << "!= FLAGS_read_skip_bytes "
                          << FLAGS_read_skip_bytes << std::endl;
            }
            already_read_bytes += ret;
        }
        int32_t res = fread(rbuf, 1, buffer_len, fr);
        if (res < 0) {
            std::cerr << "Fail to read data, id=" << id << ", errno=" << errno
                      << std::endl;
            return;
        }
        if (res == 0) {
            std::cerr << "Reach EOF when read, id=" << id << std::endl;
        }
        if (FLAGS_verbose) {
            std::cout << "read io size: " << res << std::endl;
        }
        already_read_bytes += res;
        realtime_bytes->fetch_add(res, std::memory_order_release);
        std::this_thread::sleep_for(std::chrono::seconds(sleep_time));
    }
    delete[] rbuf;

    if (fclose(fr) != 0) {
        std::cerr << "Fail to close file, id=" << id << ", errno=" << errno
                  << std::endl;
    }
}

void MonitorFunc(std::atomic<int32_t>* alive_workers,
                 std::atomic<int64_t>* realtime_bytes) {
    std::vector<int64_t> bytes_hist;
    std::vector<int64_t> time_hist;   // ms
    std::vector<int64_t> speed_hist;  // MB/s

    int64_t last_bytes = 0;
    auto last_time = std::chrono::high_resolution_clock::now();

    while (alive_workers->load(std::memory_order_acquire) > 0) {
        std::this_thread::sleep_for(std::chrono::milliseconds(FLAGS_hist_ms));
        auto bytes_now = realtime_bytes->load(std::memory_order_acquire);
        int64_t bytes = bytes_now - last_bytes;
        last_bytes = bytes_now;

        auto now = std::chrono::high_resolution_clock::now();
        auto dur_us = std::chrono::duration_cast<std::chrono::microseconds>(
                          now - last_time)
                          .count();
        last_time = now;

        int64_t speed = bytes * 1000 / 1024 * 1000 / 1024 / dur_us;

        bytes_hist.push_back(bytes / 1024 / 1024);
        time_hist.push_back(dur_us);
        speed_hist.push_back(speed);
    }
    // print histgrams
    std::stringstream ss;
    for (const auto& speed : speed_hist) {
        ss << speed << ",";
    }
    std::cout << "Speed(MB/s) hist (interval=" << FLAGS_hist_ms << "ms):\n"
              << ss.str() << std::endl;

    ss.str("");
    ss.clear();
    for (const auto& bytes : bytes_hist) {
        ss << bytes << ",";
    }
    std::cout << "Bytes(MB) hist:\n" << ss.str() << std::endl;

    ss.str("");
    ss.clear();
    for (const auto& t : time_hist) {
        ss << t << ",";
    }
    std::cout << "Time(us) hist:\n" << ss.str() << std::endl;
}

int main(int argc, char* argv[]) {
    gflags::ParseCommandLineFlags(&argc, &argv, true);
    char* wbuf = new char[FLAGS_buf_len];
    memset(wbuf, 1, FLAGS_buf_len);

    std::vector<std::thread> workers;
    std::atomic<int64_t> realtime_bytes{0};
    std::atomic<int32_t> alive_workers;

    alive_workers.store(FLAGS_jobs);

    std::thread monitor([&alive_workers, &realtime_bytes]() {
        MonitorFunc(&alive_workers, &realtime_bytes);
    });
    auto start = std::chrono::high_resolution_clock::now();
    for (int32_t i = 0; i < FLAGS_jobs; i++) {
        std::thread th;
        if (FLAGS_op == "W") {
            th = std::thread([&, i, wbuf]() {
                WriteFunc(i, wbuf, &realtime_bytes);
                alive_workers.fetch_sub(1, std::memory_order_release);
            });
        } else if (FLAGS_op == "R") {
            th = std::thread([&, i]() {
                ReadFunc(i, &realtime_bytes);
                alive_workers.fetch_sub(1, std::memory_order_release);
            });
        } else if (FLAGS_op == "RR") {
            th = std::thread([&, i]() {
                RandomReadFunc(i, &realtime_bytes);
                alive_workers.fetch_sub(1, std::memory_order_release);
            });
        } else {
            std::cerr << "Unknow op: " << FLAGS_op << std::endl;
            return -1;
        }
        workers.push_back(std::move(th));
    }

    for (int32_t i = 0; i < FLAGS_jobs; i++) {
        workers[i].join();
    }

    auto end = std::chrono::high_resolution_clock::now();
    monitor.join();

    auto duration =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    // int64_t thput = FLAGS_file_len * FLAGS_jobs * 1000 / 1024
    //     * 1000 / 1024 / duration;
    int64_t thput = realtime_bytes * 1000 / 1024 * 1000 / 1024 / duration;
    std::cout << "Elapsed time: " << duration << "us. Throughput: " << thput
              << "MB/s" << std::endl;
    delete[] wbuf;
    return 0;
}
