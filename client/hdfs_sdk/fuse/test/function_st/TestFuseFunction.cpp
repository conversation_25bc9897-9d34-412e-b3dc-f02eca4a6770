#include <fstream>
#include <iostream>
#include <string>

#include "gtest/gtest.h"

const char* MOUNT_PATH = "/hdfs_test_dir";

class TestFuseFunction : public ::testing::Test {
   public:
    // Sets up the stuff shared by all tests in this test case.
    //
    // Google Test will call Foo::SetUpTestCase() before running the first
    // test in test case Foo.  Hence a sub-class can define its own
    // SetUpTestCase() method to shadow the one defined in the super
    // class.
    static void SetUpTestCase() {
        base_path = new char[256];
        const char* _mount_dir = std::getenv("FUSE_ST_MOUNT_DIR");
        if (_mount_dir != NULL) {
            MOUNT_PATH = _mount_dir;
        }
        sprintf(base_path, "%s/tmp/test-%ld", MOUNT_PATH,
                (unsigned long)time(NULL));
        char cmd[64];
        sprintf(cmd, "mkdir %s -p", base_path);
        int ret = system(cmd);
        if (ret != 0) {
            fprintf(stderr, "test_setup failed: %s\nCleanup %s manually\n",
                    strerror(errno), base_path);
        }
        ASSERT_EQ(0, ret);
    }

    // Tears down the stuff shared by all tests in this test case.
    //
    // Google Test will call Foo::TearDownTestCase() after running the last
    // test in test case Foo.  Hence a sub-class can define its own
    // TearDownTestCase() method to shadow the one defined in the super
    // class.
    static void TearDownTestCase() {
        char cmd[64];
        sprintf(cmd, "rm -rf %s", base_path);
        int ret = system(cmd);
        if (ret != 0) {
            fprintf(stderr, "test_teardown failed: %s\nCleanup %s manually\n",
                    strerror(errno), base_path);
        }
        delete[] base_path;
        base_path = nullptr;
    }

   protected:
    void SetUp() override {
        const std::string& test_name =
            ::testing::UnitTest::GetInstance()->current_test_info()->name();
        test_path = new char[256];
        memset(test_path, 0, 256);
        sprintf(test_path, "%s/test-%s", base_path, test_name.c_str());
        std::cout << "test_path: " << test_path << std::endl;
    }

    void TearDown() override {
        delete[] test_path;
        test_path = nullptr;
    }

   public:
    static char* base_path;

   protected:
    char* test_path;
};

char* TestFuseFunction::base_path;

static int testRemovePath(const char* path) {
    char cmd[64];
    sprintf(cmd, "rm -rf %s", path);
    int ret = system(cmd);
    if (ret != 0) {
        fprintf(stderr, "remove path %s failed: %s\n", path, strerror(errno));
    }
    return ret;
}

TEST_F(TestFuseFunction, TestfseekThenSuccess) {
    std::cout << base_path << std::endl;
    ASSERT_EQ(0, mknod(test_path, S_IFREG | S_IRWXU | S_IRWXG | S_IRWXO, 0));
    const char* test_str = "test_write";
    int writeSize = strlen(test_str) + 1;
    char write_buf[64];
    memset(write_buf, 0, 64);
    memcpy(write_buf, test_str, writeSize);
    FILE* fw = fopen(test_path, "a");
    EXPECT_NE(nullptr, fw);
    int retWrite = fwrite(write_buf, 1, writeSize, fw);
    EXPECT_EQ(writeSize, retWrite);
    int retSeek = fseek(fw, 0, SEEK_SET);
    EXPECT_EQ(0, retSeek);
    EXPECT_EQ(0, fclose(fw));
    EXPECT_EQ(0, testRemovePath(test_path));
}

TEST_F(TestFuseFunction, TestlseekThenSuccess) {
    ASSERT_EQ(0, mknod(test_path, S_IFREG | S_IRWXU | S_IRWXG | S_IRWXO, 0));
    const char* test_str = "test_write";
    int writeSize = strlen(test_str) + 1;
    char write_buf[64];
    memset(write_buf, 0, 64);
    memcpy(write_buf, test_str, writeSize);
    FILE* fw = fopen(test_path, "a");
    EXPECT_NE(nullptr, fw);
    int retWrite = fwrite(write_buf, 1, writeSize, fw);
    EXPECT_EQ(writeSize, retWrite);
    int fd = fileno(fw);
    int retSeek = lseek(fd, 0, SEEK_SET);
    EXPECT_EQ(0, retSeek);
    EXPECT_EQ(0, fclose(fw));
    EXPECT_EQ(0, testRemovePath(test_path));
}

TEST_F(TestFuseFunction, TestWriteThenAppend) {
    ASSERT_EQ(0, mknod(test_path, S_IFREG | S_IRWXU | S_IRWXG | S_IRWXO, 0));
    const char* test_str = "test_write";
    int writeSize = strlen(test_str) + 1;
    char write_buf[64];
    memset(write_buf, 0, 64);
    memcpy(write_buf, test_str, writeSize);
    FILE* fw = fopen(test_path, "a");
    EXPECT_NE(nullptr, fw);
    int retWrite = fwrite(write_buf, 1, writeSize, fw);
    EXPECT_EQ(writeSize, retWrite);
    EXPECT_EQ(0, fclose(fw));
    EXPECT_EQ(0, testRemovePath(test_path));
}

TEST_F(TestFuseFunction, TestAppendTwiceSuccess) {
    const char* test_str = "test_write";
    int writeSize = strlen(test_str) + 1;
    char write_buf[64];
    memset(write_buf, 0, 64);
    memcpy(write_buf, test_str, writeSize);
    FILE* fw = fopen(test_path, "a");
    EXPECT_NE(nullptr, fw);
    int retWrite = fwrite(write_buf, 1, writeSize, fw);
    EXPECT_EQ(writeSize, retWrite);
    EXPECT_EQ(0, fclose(fw));
    fw = fopen(test_path, "a");
    EXPECT_NE(nullptr, fw);
    retWrite = fwrite(write_buf, 1, writeSize, fw);
    EXPECT_EQ(writeSize, retWrite);
    EXPECT_EQ(0, fclose(fw));
    EXPECT_EQ(0, testRemovePath(test_path));
}

int main(int argc, char** argv) {
    testing::InitGoogleTest(&argc, argv);
    return RUN_ALL_TESTS();
}