#include "Utils.h"

#include <sys/stat.h>

#include <mutex>

int TASK_SUBMIT_INTERVAL = 60;
int TASK_MAX_DEPTH = 4;
int TASK_MAX_DELAY = 3600;
std::size_t MAX_BIG_FILE_SIZE = 8589934592;  // 8 * 1024 * 1024 * 1024

const char* MOUNT_DIR = "/hdfs_test_dir";
const std::size_t BUFFER_SIZE = 4194304;             // 4 * 1024 * 1024
const std::size_t RANDOMREAD_BUFFER_SIZE = 4194304;  // 4 * 1024 * 1024
const std::size_t MAX_SMALL_FILE_SIZE = 268435456;   // 256 * 1024 * 1024
int IS_RANDOM_READ = 0;

void load_envs() {
    const char* _interval = std::getenv("FUSE_STABLE_TASK_INTERVAL");
    if (_interval != NULL) {
        TASK_SUBMIT_INTERVAL = std::atoi(_interval);
    }

    const char* _depth = std::getenv("FUSE_STABLE_MAX_DEPTH");
    if (_depth != NULL) {
        TASK_MAX_DEPTH = std::atoi(_depth);
    }

    const char* _delay = std::getenv("FUSE_STABLE_MAX_DELAY");
    if (_delay != NULL) {
        TASK_MAX_DELAY = std::atoi(_delay);
    }

    const char* _mount_dir = std::getenv("FUSE_STABLE_MOUNT_DIR");
    if (_mount_dir != NULL) {
        MOUNT_DIR = _mount_dir;
    }

    const char* _max_file_size = std::getenv("FUSE_STABLE_MAX_BIG_FILESIZE");
    if (_max_file_size != NULL) {
        MAX_BIG_FILE_SIZE = std::atoi(_max_file_size);
    }

    const char* _is_random_read = std::getenv("FUSE_STABLE_RANDOM_READ");
    if (_is_random_read != NULL) {
        IS_RANDOM_READ = std::atoi(_is_random_read);
    }

    LOG(INFO) << "Load config from environment varibales:"
              << "\nMount dir: " << MOUNT_DIR
              << "\nTask submit interval: " << TASK_SUBMIT_INTERVAL
              << "\nMax task depth: " << TASK_MAX_DEPTH
              << "\nMax task delay: " << TASK_MAX_DELAY
              << "\nMax big file size: " << MAX_BIG_FILE_SIZE
              << "\nIs random read: " << IS_RANDOM_READ;
}

static std::mutex hashMtx;
std::map<std::string, std::string> HashRecord;
std::string GetHashRecord(const std::string& path) {
    std::lock_guard<std::mutex> lock(hashMtx);
    return HashRecord.at(path);
}
void SetHashRecord(const std::string& path, const std::string& hash) {
    std::lock_guard<std::mutex> lock(hashMtx);
    HashRecord.insert({path, hash});
}
void EraseHashRecord(const std::string& path) {
    std::lock_guard<std::mutex> lock(hashMtx);
    size_t res = HashRecord.erase(path);
    LOG_IF(FATAL, res == 0) << "Failed to erase hash record " << path;
}

static std::mutex sizeMtx;
std::map<std::string, std::size_t> sizeRecord;
std::size_t GetSizeRecord(const std::string& path) {
    std::lock_guard<std::mutex> lock(sizeMtx);
    return sizeRecord.at(path);
}
void SetSizeRecord(const std::string& path, std::size_t size) {
    std::lock_guard<std::mutex> lock(sizeMtx);
    sizeRecord.insert({path, size});
}
void EraseSizeRecord(const std::string& path) {
    std::lock_guard<std::mutex> lock(sizeMtx);
    std::size_t res = sizeRecord.erase(path);
    LOG_IF(FATAL, res == 0) << "Failed to erase size record " << path;
}
