#!/bin/bash

set -e

# Fuse dd bench
# 文件4G，一共16*16=256个，共计1TB的文件
# mode: R/W 代表下载和上传

mode=$1
parallel=$2
bs_size="32M"
count_size=128
fuse_dir="${FUSE_BENCH_FUSE_DIR:-/root/hdfs_fuse/dngray/high_density_test/xintao/1t_files}"
total_size_mb=1048576 # 1TB
each_range=$((256 / parallel))

# args: i
task_function() {
  task_id=$1
  start_i=$((task_id * each_range))
  end_i=$(((task_id+1) * each_range))
  echo "do job start=${start_i} end=${end_i}"
  for ((j=$start_i; j<$end_i; j++))
  do
    file_name="file_$j.txt"
    data_num=$((j / 16))
    format_j=$(printf "%02d" $data_num)
    if [ "$mode" = "R" ]; then
      local_dir="/data${format_j}/fuse_read"
      mkdir -p "$local_dir"
      dd if="$fuse_dir/$file_name" of="$local_dir/$file_name" bs="$bs_size" count=$count_size
    elif [ "$mode" = "W" ]; then
      local_dir="/data${format_j}/fuse_prepare"
      dd if="$local_dir/$file_name" of="$fuse_dir/$file_name" bs="$bs_size" count=$count_size
    else
      echo "invalid $mode"
      exit -1
    fi
  done
}

start_time=$(date +%s)

for ((i=0; i<parallel; i++))
do
  task_function $i &
done

wait

end_time=$(date +%s)
execution_time=$((end_time - start_time))
rate_speed=$(echo "scale=2; $total_size_mb / $execution_time" | bc)

echo "write all files to fuse dir done, avg: $rate_speed"
