// This file must be included before fuse.h

#ifndef _UTILS_H_
#define _UTILS_H_

#include <cstdlib>
#include <cstring>
#include <list>
#include <mutex>
#include <queue>
#include <regex>
#include <shared_mutex>

#include "config.h"
#include "hdfs.h"

// Internal version of FUSE 2.9.9 is 26
#define FUSE_USE_VERSION 35
// #include <fuse.h>
#include "libfuse-bm/fuse.h"

#define HDFS_FS root_fs
#define FUSE_UID (fuse_get_context()->uid)
#define FUSE_GID (fuse_get_context()->gid)
#define FUSE_PID (fuse_get_context()->pid)

struct file_handler {
    hdfsFile file;
    std::mutex
        lock;  // whole file lock for none parallel read, will removed later
    off_t prevReadOff;
    bool isReadContinuously;
    std::vector<std::mutex> read_locks;  // for parallel read
};

#define GET_FILE(fi) ((struct file_handler*)fi->fh)->file
#define GET_FILE_PREV_READ_OFFSET(fi) \
    ((struct file_handler*)fi->fh)->prevReadOff
#define SET_FILE_PREV_READ_OFFSET(fi, off) \
    (((struct file_handler*)fi->fh)->prevReadOff = off)
#define IS_READ_CONTINUOUSLY(fi) \
    ((struct file_handler*)fi->fh)->isReadContinuously
#define SET_READ_CONTINUOUSLY(fi, is_continuously) \
    ((((struct file_handler*)fi->fh)->isReadContinuously) = is_continuously)

#define LOCK_FILE(fi) \
    std::lock_guard<std::mutex> guard(((struct file_handler*)fi->fh)->lock)

#ifndef UNIT_TEST
#define CHECK_PARAMETERS(X)   \
    if (HDFS_FS == NULL) {    \
        return -ECONNREFUSED; \
    }                         \
    if (!(X)) {               \
        return -EINVAL;       \
    }
#else
#define CHECK_PARAMETERS(X)

#endif /* UNIT_TEST*/
// extract name from full path
// hdfs will add extra '/' to entries in root directory
static inline int extract_entryname(const char* path) {
    int i = 0, j = 0;
    while (path[i] != '\0') {
        i++;
        if (path[i] == '/') {
            j = i;
        }
    }
    return j + 1;
}

#define MAX_COMPONENT_LENGTH 255
static inline int check_path_length(const char* path) {
    int i = 0, j = 0;
    while (path[i] != '\0') {
        i++;
        if (path[i] == '/' || path[i] == '\0') {
            if (i - j - 1 > MAX_COMPONENT_LENGTH) {
                LOG(ERROR, "File path is too log %s", path);
                return -ENAMETOOLONG;
            }
            j = i;
        }
    }
    return 0;
}
#define CHECK_NAME_LENGTH(X)            \
    if (check_path_length((X)) == -1) { \
        return -errno;                  \
    }

class normalized_path {
   public:
    normalized_path(const char* path) { npath = prefix + path; }

    operator const char*() { return npath.c_str(); }

    const char* c_str() { return npath.c_str(); }

    static void set_prefix(const std::string& p) {
        prefix = p;

        if (prefix.empty()) {
            return;
        }

        if (prefix.back() == '/') {
            prefix.pop_back();
        }
    }

    const std::string& get_string() const { return npath; }

   private:
    std::string npath;

    static std::string prefix;
};

struct readdir_context {
    int has_remaining;
    std::string start_after;
    std::list<std::pair<std::string, struct stat>> entries;
    off_t off_internal;  // just for debug info
    off_t prev_req_off;
};

static inline std::string trimString(const std::string& str) {
    std::size_t start = str.find_first_not_of(' ');
    if (start == std::string::npos) return "";

    std::size_t end = str.find_last_not_of(' ');
    return str.substr(start, end - start + 1);
}

static inline bool isAllowLevelDir(const std::string& hdfs_path,
                                   const size_t allow_level) {
    static const std::string prefix = "hdfs://";

    std::string path = hdfs_path;
    std::size_t prefixPos = path.find(prefix);

    // need trim "hdfs://<name>"
    if (prefixPos != std::string::npos) {
        std::size_t thirdSlashPos = path.find('/', prefixPos + prefix.length());
        if (thirdSlashPos == std::string::npos) {
            // invalid hdfs path like only "hdfs://xyz"
            return false;
        }
        path = path.substr(thirdSlashPos);
    }

    if (path.length() <= 1 || *path.begin() != '/') {
        return false;
    }

    std::stringstream ss(path);
    std::string directory;
    int level = 0;

    while (std::getline(ss, directory, '/')) {
        ++level;
        if (level >= allow_level) return true;
    }

    return false;
}

struct UCFileInfo {
   public:
    UCFileInfo(hdfsFile file) : fd_(file) {}
    ~UCFileInfo() {}  // no need tp delete fd_ here
    hdfsFile GetFd() { return fd_; }
    void SetLength(long length) { length_.store(length); }
    long GetLength() { return length_.load(); }
    void SetClosed() {
        std::unique_lock<std::mutex> lock(mutex_);
        state_.store(CLOSED);
        closed_cv_.notify_all();
    }
    bool IsClosed() { return state_.load() == CLOSED; }
    std::mutex mutex_;
    std::condition_variable closed_cv_;  // when file is closed, notify it.

   private:
    enum STATE { OPEN, CLOSED };
    hdfsFile fd_;
    std::atomic<long> length_{0};
    std::atomic<STATE> state_{OPEN};
};

extern std::unordered_map<std::string, std::shared_ptr<UCFileInfo>>
    g_cache_uc_fileinfo;
extern std::shared_mutex cache_uc_fileinfo_mutex_;

static inline std::shared_ptr<UCFileInfo> GetUCFile(const char* path) {
    std::shared_lock<std::shared_mutex> lock(cache_uc_fileinfo_mutex_);
    auto it = g_cache_uc_fileinfo.find(path);
    if (it != g_cache_uc_fileinfo.end()) {
        return it->second;
    } else {
        return nullptr;
    }
}

static inline bool RemoveUCFile(const char* path) {
    std::unique_lock<std::shared_mutex> lock(cache_uc_fileinfo_mutex_);
    auto it = g_cache_uc_fileinfo.find(path);
    if (it != g_cache_uc_fileinfo.end()) {
        it->second->SetClosed();
        g_cache_uc_fileinfo.erase(it);
        return true;
    } else {
        return false;
    }
}

template <typename F>
struct ByteDefer {
    F f_;
    ByteDefer(F f) : f_(f) {}  // NOLINT(runtime/explicit)
    ~ByteDefer() { f_(); }
};

#define FuseTraceApi(type, path)                                               \
    auto iomonitor_trace = hdfsIOMonitorTraceApi(type, path, 0, true);         \
    bool trace_success = false;                                                \
    auto defer =                                                               \
        ByteDefer<std::function<void()>>([iomonitor_trace, &trace_success]() { \
            hdfsIOMonitorTraceApiDone(iomonitor_trace, trace_success);         \
        });

#define FuseEndTraceSuccess()         \
    if (iomonitor_trace != nullptr) { \
        trace_success = true;         \
    }

#endif /* _UTILS_H_ */
