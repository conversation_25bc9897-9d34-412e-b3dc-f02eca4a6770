#include "hdfs_fuse.h"

#include <dirent.h>
#include <grp.h>
#include <pthread.h>
#include <pwd.h>
#include <sys/ioctl.h>
#include <sys/mman.h>
#include <sys/stat.h>
#include <unistd.h>

#include <cerrno>
#include <cstdio>
#include <cstdlib>
#include <cstring>
#include <shared_mutex>
#include <string>

#include "config.h"
#include "hdfs.h"
#include "logger.h"
#include "recoverlease_worker.h"
#include "utils.h"

const tOffset default_block_size = 512;

static CreateFileOption NewCreateFileOption() {
    CreateFileOption option;
    option.parallel_io_num = 0;
    option.partial_file_size = 0;
    option.platform = IFT_NORMAL;
    option.permission = 0644;
    if (root_config.get_stripe_unit_size() > 0 &&
        root_config.get_stripe_unit_count() > 0) {
        option.format = FileStorageFormat::FT_PARALLEL;
        option.stripe_unit_size = root_config.get_stripe_unit_size();
        option.stripe_unit_count = root_config.get_stripe_unit_count();
    } else {
        option.format = FileStorageFormat::FT_NORMAL;
        option.stripe_unit_size = 0;
        option.stripe_unit_count = 0;
    }
    return option;
}

hdfsFS root_fs;

static std::mutex& get_read_lock(fuse_file_info* fi, int index) {
    if (index > 0 && ((struct file_handler*)fi->fh)->read_locks.size() > 0) {
        return ((struct file_handler*)fi->fh)->read_locks[index - 1];
    } else {
        return ((struct file_handler*)fi->fh)->lock;
    }
}

static void set_attr(const hdfsFileInfoEx& file, const char* path,
                     struct stat* stat_buf) {
    memset((char*)stat_buf, 0, sizeof(*stat_buf));
    if (file.mKind == kObjectKindSymlink) {
        stat_buf->st_mode = S_IFLNK;
        stat_buf->st_nlink = 1;
        stat_buf->st_size = 4096;
    } else if (file.mKind == kObjectKindDirectory) {
        stat_buf->st_mode = S_IFDIR;
        if ((path != nullptr) && (strcmp(path, "/") != 0)) {
            stat_buf->st_nlink = 2;
        } else {
            stat_buf->st_nlink = 1;
        }
        stat_buf->st_size = 4096;
    } else {
        stat_buf->st_mode = S_IFREG;
        stat_buf->st_nlink = 1;
        stat_buf->st_blksize = default_block_size;
        stat_buf->st_blocks = 0;
        stat_buf->st_blocks = file.mSize / default_block_size;
        stat_buf->st_blocks += (file.mSize % default_block_size != 0);
        stat_buf->st_size = file.mSize;
    }
    stat_buf->st_mode |= (mode_t)file.mPermissions;
    stat_buf->st_atim.tv_sec = file.mLastAccess / 1000;
    stat_buf->st_atim.tv_nsec = (file.mLastAccess % 1000) * 1000000;
    stat_buf->st_mtim.tv_sec = file.mLastMod / 1000;
    stat_buf->st_mtim.tv_nsec = (file.mLastMod % 1000) * 1000000;

    // st_ctime/st_uid/st_gid/st_dev/st_ino/st_rdev not support right now
    stat_buf->st_ctim.tv_sec = 0;
    stat_buf->st_ctim.tv_nsec = 0;
    stat_buf->st_gid = root_config.get_attr_gid();
    stat_buf->st_uid = root_config.get_attr_uid();
}

static inline int64_t GetCurTimestampUs() {
    auto time_now = std::chrono::system_clock::now().time_since_epoch();
    return std::chrono::duration_cast<std::chrono::microseconds>(time_now)
        .count();
}

// struct stat {
// X   dev_t     st_dev;         /* ID of device containing file */
// X   ino_t     st_ino;         /* Inode number */
//     mode_t    st_mode;        /* File type and mode */
//     nlink_t   st_nlink;       /* Number of hard links */
//     uid_t     st_uid;         /* User ID of owner */
//     gid_t     st_gid;         /* Group ID of owner */
// X   dev_t     st_rdev;        /* Device ID (if special file) */
//     off_t     st_size;        /* Total size, in bytes */
// X   blksize_t st_blksize;     /* Block size for filesystem I/O */
// X   blkcnt_t  st_blocks;      /* Number of 512B blocks allocated */

//     /* Since Linux 2.6, the kernel supports nanosecond
//         precision for the following timestamp fields.
//         For the details before Linux 2.6, see NOTES. */

//     struct timespec st_atim;  /* Time of last access */
//     struct timespec st_mtim;  /* Time of last modification */
// X   struct timespec st_ctim;  /* Time of last status change */

// #define st_atime st_atim.tv_sec      /* Backward compatibility */
// #define st_mtime st_mtim.tv_sec
// #define st_ctime st_ctim.tv_sec
// };

/** Get file attributes.
 *
 * Similar to stat().  The 'st_dev' and 'st_blksize' fields are
 * ignored.	 The 'st_ino' field is ignored except if the 'use_ino'
 * mount option is given.
 */
/**
 * Setting nlink of dir to 2 and file to 1 is convention of POSIX.
 * Some fields in stat are not set, check comment above. Leading 'X' stands
 * for not set.
 */

int hdfs_getattr(const char* path, struct stat* stat_buf,
                 struct fuse_file_info* fi) {
    (void)fi;
    CHECK_PARAMETERS(path != NULL && strlen(path) > 0 && stat_buf != NULL);
    LOG(DEBUG, "hdfs_getattr %s", path);
    CHECK_NAME_LENGTH(path);
    normalized_path npath(path);
    LOG(DEBUG, "hdfsGetPathInfo %s", npath.c_str());

    FuseTraceApi(hdfsFuseCliGetAttr, npath.c_str());
    hdfsFileInfoEx* file = hdfsGetPathInfoEx(HDFS_FS, npath);
    if (file == nullptr) {
        LOG(WARNING, "hdfs_getattr %s not exist", npath.c_str());
        return -ENOENT;
    }

    std::shared_ptr<UCFileInfo> uc_file = GetUCFile(npath.c_str());
    if (uc_file != nullptr) {
        file->mSize = uc_file->GetLength();
        LOG(DEBUG, "hdfsGetPathInfo %s ,get cache file length %lu",
            npath.c_str(), file->mSize);
    }

    set_attr(*file, path, stat_buf);

    LOG(DEBUG, "hdfs_getattr %s success!", npath.c_str());
    hdfsFreeFileInfoEx(file, 1);
    FuseEndTraceSuccess();
    return 0;
}

/** Read the target of a symbolic link
 * The buffer should be filled with a null terminated string.  The
 * buffer size argument includes the space for the terminating
 * null character.	If the linkname is too long to fit in the
 * buffer, it should be truncated.	The return value should be 0
 * for success.
 */
// Not supported
int hdfs_readlink(const char* path, char* link, size_t size) {
    CHECK_PARAMETERS(path != NULL && strlen(path) > 0);
    normalized_path npath(path);
    char* link_path = hdfsGetLinkTarget(HDFS_FS, npath.c_str());
    CHECK_ERROR(link_path, nullptr, hdfsGetLinkTarget);
    LOG(INFO, "hdfs_readlink %s, %s", npath.c_str(), link_path);
    int cp_size = std::min(size, strlen(link_path));
    memcpy(link, link_path, cp_size);
    memcpy(link + cp_size, "\0", 1);
    hdfsFreeString(link_path);
    return 0;
}

/** Create a file node
 *
 * This is called for creation of all non-directory, non-symlink
 * nodes.  If the filesystem defines a create() method, then for
 * regular files that will be called instead.
 */
/**
 * All create file operation will be done with mode O_CREAT.
 * This function will create a file and close it immediately.
 * It might get stuck in intermediate state due to succeeded create
 * and failed close.
 *
 * If this method is called with type other than regular file (e.g. FIFO,
 * SOCKET), return ENOSYS.
 */
int hdfs_mknod(const char* path, mode_t mode, dev_t dev) {
    (void)mode;
    (void)dev;

    CHECK_PARAMETERS(path != NULL && strlen(path) > 0);
    LOG(ERROR, "hdfs_mknod failed %s", path);
    return -ENOSYS;
}

/** Create a directory
 *
 * Note that the mode argument may not have the type specification
 * bits set, i.e. S_ISDIR(mode) can be false.  To obtain the
 * correct directory type bits use  mode|S_IFDIR
 * */
/**
 * Create directory will not create its parents.
 */
int hdfs_mkdir(const char* path, mode_t mode) {
    CHECK_PARAMETERS(path != NULL && strlen(path) > 0);
    LOG(DEBUG, "hdfs_mkdir %s, mode %o", path, mode);
    CHECK_NAME_LENGTH(path)
    normalized_path npath(path);

    LOG(DEBUG, "hdfsCreateDirectoryEx %s, mode %o", npath.c_str(), mode & 0777);
    FuseTraceApi(hdfsFuseCliMkdir, npath.c_str());
    int ret = hdfsCreateDirectoryEx(HDFS_FS, npath, mode & 0777, 0);
    CHECK_ERROR(ret, -1, hdfsCreateDirectoryEx);
    FuseEndTraceSuccess();
    return 0;
}

/** Remove a file */
int hdfs_unlink(const char* path) {
    CHECK_PARAMETERS(path != NULL && strlen(path) > 0);
    LOG(DEBUG, "hdfs_unlink %s", path);
    CHECK_NAME_LENGTH(path)
    normalized_path npath(path);
    FuseTraceApi(hdfsFuseCliUnlink, npath.c_str());
    if (!root_config.enable_hdfs_delete()) {
        LOG(WARNING, "hdfs_unlink %s disable delete", npath.c_str());
        return -1;
    }

    LOG(DEBUG, "hdfsDelete %s", npath.c_str());
    int ret = hdfsDelete(HDFS_FS, npath, 0);
    CHECK_ERROR(ret, -1, hdfsDelete);
    FuseEndTraceSuccess();
    return 0;
}

/** Remove a directory */
/**
 * Remove empty directory.
 */
int hdfs_rmdir(const char* path) {
    CHECK_PARAMETERS(path != NULL && strlen(path) > 0);
    LOG(DEBUG, "hdfs_rmdir %s", path);
    CHECK_NAME_LENGTH(path)
    normalized_path npath(path);
    FuseTraceApi(hdfsFuseCliRmdir, npath.c_str());
    if (!root_config.enable_hdfs_delete()) {
        LOG(WARNING, "hdfs_rmdir %s disable delete", npath.c_str());
        return -1;
    }

    LOG(DEBUG, "hdfsDelete %s", npath.c_str());
    int ret = hdfsDelete(HDFS_FS, npath, 0);
    CHECK_ERROR(ret, -1, hdfsDelete);
    FuseEndTraceSuccess();
    return 0;
}

/** Create a symbolic link */
int hdfs_symlink(const char* path, const char* link) {
    CHECK_PARAMETERS(path != NULL && strlen(path) > 0);
    LOG(DEBUG, "hdfs_symlink %s to %s", path, link);
    normalized_path nlink(link);
    normalized_path npath(path);
    FuseTraceApi(hdfsFuseCliSymlink, npath.c_str());
    auto ret = hdfsCreateSymlink(HDFS_FS, path, nlink.c_str(), 0777, false);
    if (ret < 0) {
        LOG(ERROR, "hdfs_symlink %s to %s errno %d", path, link, errno);
    } else {
        FuseEndTraceSuccess();
    }
    return ret;
}

/** Rename a file */
int hdfs_rename(const char* path, const char* new_path, unsigned int flags) {
    (void)flags;
    CHECK_PARAMETERS(path != NULL && strlen(path) > 0 && new_path != NULL &&
                     strlen(new_path) > 0);
    LOG(DEBUG, "hdfs_rename %s to %s", path, new_path);
    CHECK_NAME_LENGTH(path)
    normalized_path npath(path), nnpath(new_path);
    FuseTraceApi(hdfsFuseCliRename, npath.c_str());
    LOG(DEBUG, "hdfsRename %s to %s", npath.c_str(), nnpath.c_str());
    int ret = hdfsRename2(HDFS_FS, npath, nnpath);
    CHECK_ERROR(ret, -1, hdfsRename2);
    FuseEndTraceSuccess();
    return 0;
}

/** Create a hard link to a file */
int hdfs_link(const char* path, const char* link) {
    CHECK_PARAMETERS(path != NULL && strlen(path) > 0);
    LOG(DEBUG, "hdfs_link %s to %s", path, link);
    normalized_path npath(path);
    normalized_path nlink(link);
    FuseTraceApi(hdfsFuseCliLink, npath.c_str());
    auto ret =
        hdfsCreateHardlink(HDFS_FS, npath.c_str(), nlink.c_str(), 0644, false);
    CHECK_ERROR(ret, -1, hdfsCreateHardlink);
    FuseEndTraceSuccess();
    return ret;
}

/** Change the permission bits of a file */
int hdfs_chmod(const char* path, mode_t mode, struct fuse_file_info* fi) {
    (void)fi;
    CHECK_PARAMETERS(path != NULL && strlen(path) > 0);
    LOG(DEBUG, "hdfs_chmod %s, mode %o", path, mode);
    CHECK_NAME_LENGTH(path)
    normalized_path npath(path);
    FuseTraceApi(hdfsFuseCliChmod, npath.c_str());
    LOG(DEBUG, "hdfsChmod %s, mode %o", npath.c_str(), mode & 0777);
    int ret = hdfsChmod(HDFS_FS, npath, mode & 0777);
    CHECK_ERROR(ret, -1, hdfsChmod);
    FuseEndTraceSuccess();
    return 0;
}

/** Change the owner and group of a file */
int hdfs_chown(const char* path, uid_t uid, gid_t gid,
               struct fuse_file_info* fi) {
    (void)fi;
    CHECK_PARAMETERS(path != NULL && strlen(path) > 0);
    LOG(DEBUG, "hdfs_chown %s, uid %d, gid %d", path, uid, gid);

    // TODO(zhuangsiyu):
    // It is hard to convert user&group from hdfs to posix.
    // Do not call chown on server but return success.
    return 0;
}

/** Change the size of a file */
int hdfs_truncate(const char* path, off_t off, struct fuse_file_info* fi) {
    (void)off;
    (void)fi;

    if (!root_config.get_is_fio_test()) {
        CHECK_PARAMETERS(path != NULL && strlen(path) > 0);
        LOG(ERROR, "hdfs_truncate failed %s", path);
        return -ENOSYS;
    } else {
        (void)path;
        return 0;
    }
}

/** File open operation
 *
 * No creation (O_CREAT, O_EXCL) and by default also no
 * truncation (O_TRUNC) flags will be passed to open(). If an
 * application specifies O_TRUNC, fuse first calls truncate()
 * and then open(). Only if 'atomic_o_trunc' has been
 * specified and kernel version is 2.6.24 or later, O_TRUNC is
 * passed on to open.
 *
 * Unless the 'default_permissions' mount option is given,
 * open should check if the operation is permitted for the
 * given flags. Optionally open may also return an arbitrary
 * filehandle in the fuse_file_info structure, which will be
 * passed to all file operations.
 *
 * Changed in version 2.2
 */
/**
 * FUSE will call truncate() before open() and truncate() will
 * return ENOTSUP. So no truncate will be passed to here.
 *
 * In mknod, the file has been created, opened and closed.
 * write supports mode "a" or "ab"; "w" or "wb" implies truncate
 * and will invoke hdfs_truncate which is ENOTSUP.
 * Read supports mode "r" or "rb".
 * other modes are not supported.
 *
 * From hdfsOpenFile:
 * flags – - an | of bits/fcntl.h file flags - supported flags are
 * O_RDONLY, O_WRONLY (meaning create or overwrite i.e., implies
 * O_TRUNCAT), O_WRONLY|O_APPEND and O_SYNC. Other flags are
 * generally ignored other than (O_RDWR || (O_EXCL & O_CREAT))
 * which return NULL and set errno equal ENOTSUP.
 */
int hdfs_open(const char* path, struct fuse_file_info* fi) {
    CHECK_PARAMETERS(path != NULL && strlen(path) > 0 && fi != NULL);
    LOG(DEBUG, "hdfs_open %s", path);
    CHECK_NAME_LENGTH(path)
    normalized_path npath(path);
    FuseTraceApi(hdfsFuseCliOpen, npath.c_str());
    int fcntl_flags = fi->flags & O_ACCMODE;
    LOG(DEBUG, "hdfsOpenFileV3 %s, orignal flags %x, fcntl flags:%x",
        npath.c_str(), fi->flags, fcntl_flags);

    if (fcntl_flags & O_WRONLY) {
        if (!(fi->flags & (O_APPEND | O_TRUNC))) {
            LOG(ERROR,
                "open file:%s with fcntl flag:%d original flag:%d not permitted",
                path, fcntl_flags, fi->flags);
            return -EPERM;
        }
    }

    hdfsFile file = NULL;
    if ((fi->flags & O_APPEND) |
        (fcntl_flags & O_RDWR)) {  // ORDWR support appends
        std::shared_ptr<UCFileInfo> uc_file_info = GetUCFile(npath.c_str());

        // if the file is not closed, we wait the close process finished
        if (uc_file_info != nullptr && !uc_file_info->IsClosed()) {
            {
                std::unique_lock<std::mutex> lock(uc_file_info->mutex_);
                LOG(DEBUG, "file: %s waiting closed", npath.c_str());
                uc_file_info->closed_cv_.wait_for(
                    lock,
                    std::chrono::seconds(root_config.get_open_wait_closed()),
                    [uc_file_info] { return uc_file_info->IsClosed(); });
            }
            if (!uc_file_info->IsClosed()) {
                LOG(ERROR,
                    "open file:%s failed, the file is not closed, can't be opened "
                    "again",
                    npath.c_str());
                return -EBUSY;
            } else {
                LOG(DEBUG, "file: %s waiting closed finish", npath.c_str());
            }
        }
    }
    switch (root_config.get_filesystem_mode()) {
        // todo(liuxintao): I don't know what is ACC.
        // case filesystem_mode::ACC: {
        //   // TODO(zhuangsiyu):
        //   // We cannot handle O_RDWR now, for r+/w+/a+, downgrade to
        //   r/w/a if ((flags & O_RDWR) == O_RDWR) {
        //     flags &= ~O_RDWR;
        //     if ((flags & O_TRUNC) == O_TRUNC) {
        //       LOG(WARNING,
        //           "hdfs_open with O_RDWR|O_CREAT|O_TRUNC, downgrade "
        //           "to O_WRONLY|O_CREAT|O_TRUNC");
        //       flags |= O_WRONLY;
        //     } else if ((flags & O_APPEND) == O_APPEND) {
        //       LOG(WARNING,
        //           "hdfs_open with O_RDWR|O_CREAT|O_APPEND, downgrade "
        //           "to O_WRONLY|O_CREAT|O_APPEND");
        //       flags |= O_WRONLY;
        //     } else {
        //       LOG(WARNING, "hdfs_open with O_RDWR, downgrade to
        //       O_RDONLY"); flags |= O_RDONLY;
        //     }
        //   }

        //   if (flags & O_WRONLY) {
        //     switch (root_config.get_acc_write_mode()) {
        //       case acc_write_mode::ASYNC: flags |= O_ASYNC; break;
        //       case acc_write_mode::THROUGH:
        //         // No extra flags
        //         break;
        //       default:
        //         flags |= O_ASYNC;
        //         LOG(ERROR, "Unexpected acc write mode, use async");
        //     }
        //   }

        //   int is_appendable = 0;
        //   if ((flags & O_WRONLY) &&
        //       (flags & O_APPEND || is_acc_appendable_file(npath))) {
        //     LOG(INFO, "Open appendable file %s", npath.c_str());
        //     is_appendable = true;
        //   }

        //   file = hdfsOpenFileAcc(HDFS_FS, npath, flags, 0644, false,
        //   is_appendable); break;
        // }
        case filesystem_mode::HDFS: {
            file = hdfsOpenFileV3(HDFS_FS, npath, fi->flags, 0, 0, 0,
                                  NewCreateFileOption());
            break;
        }
        default:
            LOG(ERROR, "Unexpected filesystem mode");
    }
    CHECK_ERROR(file, nullptr, hdfsOpenFileV3);
    if (hdfsFileIsOpenForRead(file)) {
        hdfsFile uc_file = nullptr;
        std::shared_ptr<UCFileInfo> uc_file_info = GetUCFile(npath.c_str());
        if (uc_file_info != nullptr) {
            uc_file = uc_file_info->GetFd();
        } else {
            LOG(DEBUG, "hdfsOpenFileV3 %s cache not exist", npath.c_str());
        }
        if (uc_file) {
            LOG(DEBUG, "hdfsOpenFileV3 %s, start hdfsHSyncAndUpdateLength",
                npath.c_str());
            int ret = hdfsHSyncAndUpdateLength(HDFS_FS, uc_file);
            LOG(DEBUG,
                "hdfsOpenFileV3 %s, end hdfsHSyncAndUpdateLength, result: %d",
                npath.c_str(), ret);
        }
    }
    file_handler* fh = new file_handler;
    fh->file = file;
    fh->prevReadOff = 0;
    fh->isReadContinuously = true;
    fi->fh = (uint64_t)fh;
    FuseEndTraceSuccess();
    return 0;
}

/** Read data from an open file
 *
 * Read should return exactly the number of bytes requested except
 * on EOF or error, otherwise the rest of the data will be
 * substituted with zeroes.	 An exception to this is when the
 * 'direct_io' mount option is specified, in which case the return
 * value of the read system call will reflect the return value of
 * this operation.
 *
 * Changed in version 2.2
 */
/**
 * This function will call hdfsSeek to offset first.
 * So we need to disable async read.
 */
int hdfs_read(const char* path, char* buf, size_t size, off_t off,
              struct fuse_file_info* fi) {
    CHECK_PARAMETERS(path != NULL && strlen(path) > 0 && buf != NULL &&
                     fi != NULL);
    CHECK_NAME_LENGTH(path)
    normalized_path npath(path);

    FuseTraceApi(hdfsFuseCliRead, npath.c_str());
    auto start_us = GetCurTimestampUs();
    std::lock_guard<std::mutex> guard(get_read_lock(fi, fi->channel_id));
    auto lock_time_us = GetCurTimestampUs();
    hdfsFile file = GET_FILE(fi);
    hdfs_io_context ctx;
    ctx.channel_id = fi->channel_id;
    LOG(DEBUG, "hdfsFuseTell %s, chanenl_id %u", npath.c_str(), ctx.channel_id);
    int64_t cursor = hdfsFuseTell(HDFS_FS, file, ctx);
    LOG(DEBUG, "hdfs_read %s, size %lu, off %lu, hdfs cursor %lu, diff %d",
        path, size, off, cursor, int(off - cursor));
    CHECK_ERROR(cursor, -1, hdfsFuseTell);
    bool is_adaptive_pread = root_config.get_is_adaptive_pread();
    if (cursor != off && is_adaptive_pread) {
        int64_t offset_gap = cursor >= off ? cursor - off : off - cursor;
        is_adaptive_pread =
            offset_gap != 0 &&
            offset_gap > root_config.get_pread_adaptive_threshold();
    }
    if (is_adaptive_pread && GET_FILE_PREV_READ_OFFSET(fi) == off &&
        IS_READ_CONTINUOUSLY(fi)) {
        is_adaptive_pread = false;
    }

    if (is_adaptive_pread || root_config.get_is_pread()) {
        if (GET_FILE_PREV_READ_OFFSET(fi) == off) {
            SET_READ_CONTINUOUSLY(fi, true);
        } else {
            SET_READ_CONTINUOUSLY(fi, false);
        }
        int ret = 0;
        size_t total_read = 0, todo = size;
        tOffset to_off = off;
        LOG(DEBUG, "hdfsFusePread %s, size %lu, offset %lu, channel_id %u",
            npath.c_str(), size, off, ctx.channel_id);
        do {
            ret = hdfsFusePread(HDFS_FS, file, to_off, buf + total_read, todo,
                                ctx);
            CHECK_ERROR(ret, -1, hdfsFusePread);
            total_read += ret;
            todo -= ret;
            to_off += ret;
        } while (total_read < size && ret > 0);
        size_t cost_us = size_t(GetCurTimestampUs() - lock_time_us);
        LOG(DEBUG,
            "hdfsFusePread succeed %s, read_len %lu, offset %lu, size %lu, "
            "channel_id %u, lock %lu cost %lu",
            npath.c_str(), total_read, off, size, ctx.channel_id,
            lock_time_us - start_us, cost_us);
        SET_FILE_PREV_READ_OFFSET(fi, off + total_read);
        FuseEndTraceSuccess();
        return total_read;
    }

    if (cursor != off) {
        LOG(DEBUG, "hdfsSeek %s, cursor %lu, offset %lu, channel_id %u",
            npath.c_str(), cursor, off, ctx.channel_id);
        int ret = hdfsFuseSeek(HDFS_FS, file, off, ctx);
        CHECK_ERROR(ret, -1, hdfsFuseSeek);
    }

    LOG(DEBUG, "hdfsFuseRead %s, offset %lu, size %lu, channel_id %u",
        npath.c_str(), off, size, ctx.channel_id);
    int ret = 0;
    size_t total_read = 0, todo = size;
    do {
        ret = hdfsFuseRead(HDFS_FS, file, buf + total_read, todo, ctx);
        CHECK_ERROR(ret, -1, hdfsFuseRead);
        total_read += ret;
        todo -= ret;
    } while (total_read < size && ret > 0);
    size_t cost_us = size_t(GetCurTimestampUs() - lock_time_us);
    LOG(DEBUG,
        "hdfsFuseRead succeed %s, read_len %lu, offset %lu, size %lu, channel_id "
        "%u, lock %lu cost %lu",
        npath.c_str(), total_read, off, size, ctx.channel_id,
        lock_time_us - start_us, cost_us);
    SET_FILE_PREV_READ_OFFSET(fi, off + total_read);
    FuseEndTraceSuccess();
    return total_read;
}

/** Write data to an open file
 *
 * Write should return exactly the number of bytes requested
 * except on error.	 An exception to this is when the 'direct_io'
 * mount option is specified (see read operation).
 *
 * Changed in version 2.2
 */
/**
 * Write file are nonseekable. off will be checked.
 */
int hdfs_write(const char* path, const char* buf, size_t size, off_t off,
               struct fuse_file_info* fi) {
    CHECK_PARAMETERS(path != NULL && strlen(path) > 0 && buf != NULL &&
                     fi != NULL);
    LOG(DEBUG, "hdfs_write %s, size %lu, off %lu", path, size, off);
    CHECK_NAME_LENGTH(path)
    normalized_path npath(path);

    FuseTraceApi(hdfsFuseCliWrite, npath.c_str());
    hdfsFile file = GET_FILE(fi);
    LOG(DEBUG, "hdfsTell %s", npath.c_str());

    hdfs_io_context ctx;
    ctx.channel_id = fi->channel_id;

    int64_t offset = 0;
    if (size > 0) {
        offset = hdfsFuseTell(HDFS_FS, file, ctx);
        CHECK_ERROR(offset, -1, hdfsTell);
        if (offset != off) {
            LOG(ERROR,
                "hdfsWrite failed, path %s, random write is not supported,"
                " current write pos: %lu, offset: %lu, channel_id: %u",
                npath.c_str(), offset, off, ctx.channel_id);
            return -ENOTSUP;
        }
    }

    int ret = hdfsFuseWrite(HDFS_FS, file, buf, size, ctx);
    CHECK_ERROR(ret, -1, hdfsWrite);
    LOG(DEBUG, "hdfsWrite %s, offset: %lu, size %lu, channel_id %u succeed!",
        npath.c_str(), offset, size, ctx.channel_id);
    std::shared_ptr<UCFileInfo> uc_file_info = GetUCFile(npath.c_str());
    if (uc_file_info != nullptr) {
        uc_file_info->SetLength(off + size);
        LOG(DEBUG, "hdfsWrite cache file %s, update length %lu", npath.c_str(),
            off + size);
    }
    FuseEndTraceSuccess();
    return ret;
}

int hdfs_write_buf(const char* path, struct fuse_bufvec* buf, off_t off,
                   struct fuse_file_info* fi) {
    CHECK_PARAMETERS(path != NULL && strlen(path) > 0 && buf != NULL &&
                     fi != NULL);
    LOG(DEBUG, "hdfs_write_buffer %s, off %lu", path, off);
    CHECK_NAME_LENGTH(path)
    normalized_path npath(path);

    FuseTraceApi(hdfsFuseCliWritebuf, npath.c_str());
    hdfs_io_context ctx;
    ctx.channel_id = fi->channel_id;

    hdfsFile file = GET_FILE(fi);
    LOG(DEBUG, "hdfsTell %s", npath.c_str());
    int64_t offset = hdfsFuseTell(HDFS_FS, file, ctx);
    CHECK_ERROR(offset, -1, hdfsFuseTell);
    if (offset != off) {
        LOG(ERROR, "hdfsWrite failed, path %s, random write is not supported",
            npath.c_str());
        return -ENOTSUP;
    }

    if ((buf->count == 1) && (buf->buf[0].size == 0)) {
        int32_t ret = hdfsFuseWrite(HDFS_FS, file, nullptr, 0, ctx);
        if (ret >= 0) {
            FuseEndTraceSuccess();
        }
        return ret;
    }

    int i = 0;
    int total_write_size = 0;
    for (int i = 0; i < buf->count; ++i) {
        auto buffer = static_cast<const char*>(buf->buf[i].mem);
        int size = buf->buf[i].size;
        int remain = size;
        int write_size = 0;

        while (remain > 0) {
            int ret =
                hdfsFuseWrite(HDFS_FS, file, buffer + write_size, remain, ctx);
            if (ret == 0) {
                continue;
            } else if (ret > 0) {
                remain -= ret;
                write_size += ret;
                total_write_size += ret;
            } else {
                return -1;
            }
        }
    }
    FuseEndTraceSuccess();
    return total_write_size;
}

//    struct statvfs {
//        unsigned long  f_bsize;    /* Filesystem block size */
//        unsigned long  f_frsize;   /* Fragment size */
//        fsblkcnt_t     f_blocks;   /* Size of fs in f_frsize units */
//        fsblkcnt_t     f_bfree;    /* Number of free blocks */
//        fsblkcnt_t     f_bavail;   /* Number of free blocks for
//                                      unprivileged users */
//        fsfilcnt_t     f_files;    /* Number of inodes */
//        fsfilcnt_t     f_ffree;    /* Number of free inodes */
//        fsfilcnt_t     f_favail;   /* Number of free inodes for
//                                      unprivileged users */
//        unsigned long  f_fsid;     /* Filesystem ID */
//        unsigned long  f_flag;     /* Mount flags */
//        unsigned long  f_namemax;  /* Maximum filename length */
//    };

/** Get file system statistics
 *
 * The 'f_frsize', 'f_favail', 'f_fsid' and 'f_flag' fields are ignored
 *
 * Replaced 'struct statfs' parameter with 'struct statvfs' in
 * version 2.5
 */
int hdfs_statfs(const char* path, struct statvfs* stat_buf) {
    CHECK_PARAMETERS(path != NULL && strlen(path) > 0);
    LOG(DEBUG, "hdfs_statfs path %s", path);
    FuseTraceApi(hdfsFuseCliStatfs, path);
    // todo(liuxintao): native hdfs has no this API.
    // hdfsFileSytemInfo fsInfo = hdfsGetFileSystemInfo(HDFS_FS);
    tOffset hdfs_capacity = 1124800395214848;
    tOffset hdfs_remaining = 2196875771904;
    int64_t max_blocks_num = 0;
    int64_t remaining_blocks_num = 0;
    max_blocks_num = hdfs_capacity / default_block_size;
    remaining_blocks_num = hdfs_remaining / default_block_size;
    stat_buf->f_bsize = default_block_size;
    stat_buf->f_blocks = max_blocks_num;
    stat_buf->f_bfree = remaining_blocks_num;
    stat_buf->f_bavail = remaining_blocks_num;
    stat_buf->f_files = 888;
    stat_buf->f_ffree = 10;
    stat_buf->f_favail = 50;
    FuseEndTraceSuccess();
    return 0;
}

/** Possibly flush cached data
 *
 * BIG NOTE: This is not equivalent to fsync().  It's not a
 * request to sync dirty data.
 *
 * Flush is called on each close() of a file descriptor.  So if a
 * filesystem wants to return write errors in close() and the file
 * has cached dirty data, this is a good place to write back data
 * and return any errors.  Since many applications ignore close()
 * errors this is not always useful.
 *
 * NOTE: The flush() method may be called more than once for each
 * open().	This happens if more than one file descriptor refers
 * to an opened file due to dup(), dup2() or fork() calls.	It is
 * not possible to determine if a flush is final, so each flush
 * should be treated equally.  Multiple write-flush sequences are
 * relatively rare, so this shouldn't be a problem.
 *
 * Filesystems shouldn't assume that flush will always be called
 * after some writes, or that if will be called at all.
 *
 * Changed in version 2.2
 */
int hdfs_flush(const char* path, struct fuse_file_info* fi) {
    CHECK_PARAMETERS(path != NULL && strlen(path) > 0 && fi != NULL);
    LOG(DEBUG, "hdfs_flush %s", path);
    CHECK_NAME_LENGTH(path)
    normalized_path npath(path);

    // LOCK_FILE(fi);
    FuseTraceApi(hdfsFuseCliFlush, npath.c_str());
    hdfsFile file = GET_FILE(fi);
    if (hdfsFileIsOpenForWrite(file)) {
        LOG(DEBUG, "hdfsFlush %s", npath.c_str());
        int ret = hdfsFlush(HDFS_FS, file);
        CHECK_ERROR(ret, -1, hdfsFlush);
    }
    FuseEndTraceSuccess();
    return 0;
}

/** Release an open file
 *
 * Release is called when there are no more references to an open
 * file: all file descriptors are closed and all memory mappings
 * are unmapped.
 *
 * For every open() call there will be exactly one release() call
 * with the same flags and file descriptor.	 It is possible to
 * have a file opened more than once, in which case only the last
 * release will mean, that no more reads/writes will happen on the
 * file.  The return value of release is ignored.
 *
 * Changed in version 2.2
 */
int hdfs_release(const char* path, struct fuse_file_info* fi) {
    CHECK_PARAMETERS(path != NULL && strlen(path) > 0 && fi != NULL);
    LOG(DEBUG, "hdfs_release %s", path);
    CHECK_NAME_LENGTH(path)
    normalized_path npath(path);

    FuseTraceApi(hdfsFuseCliRelease, npath.c_str());
    hdfsFile file = GET_FILE(fi);
    bool is_write = hdfsFileIsOpenForWrite(file);
    LOG(DEBUG, "hdfsCloseFile %s", npath.c_str());
    int ret = hdfsCloseFile(HDFS_FS, file);
    LOG(DEBUG, "success hdfsCloseFile %s", npath.c_str());
    if (ret != 0) {
        RecoverLeaseWorker::addPath(npath.c_str());
    }
    delete (file_handler*)fi->fh;
    CHECK_ERROR(ret, -1, hdfsCloseFile);
    if (is_write && RemoveUCFile(npath.c_str())) {
        LOG(DEBUG, "hdfs_release %s, erase from uc file", npath.c_str());
    }
    FuseEndTraceSuccess();
    return 0;
}

/** Synchronize file contents
 *
 * If the datasync parameter is non-zero, then only the user data
 * should be flushed, not the meta data.
 *
 * Changed in version 2.2
 */
/**
 * datasync is ignored here. Always sync metadata.
 */
int hdfs_fsync(const char* path, int datasync, struct fuse_file_info* fi) {
    CHECK_PARAMETERS(path != NULL && strlen(path) > 0 && fi != NULL);
    LOG(DEBUG, "hdfs_fsync %s, datasync: %d", path, datasync);
    CHECK_NAME_LENGTH(path)
    normalized_path npath(path);

    // LOCK_FILE(fi);
    FuseTraceApi(hdfsFuseCliFsync, npath.c_str());
    hdfsFile file = GET_FILE(fi);
    int ret = 0;
    if (datasync) {
        LOG(DEBUG, "hdfsSync %s", npath.c_str());
        ret = hdfsSync(HDFS_FS, file);
        CHECK_ERROR(ret, -1, hdfsSync);
    } else {
        LOG(DEBUG, "hdfsHSyncAndUpdateLength %s", npath.c_str());
        ret = hdfsHSyncAndUpdateLength(HDFS_FS, file);
        CHECK_ERROR(ret, -1, hdfsHSyncAndUpdateLength);
    }
    FuseEndTraceSuccess();
    return 0;
}

/** Set extended attributes */
// Not supported
int hdfs_setxattr(const char* path, const char* name, const char* value,
                  size_t size, int flags) {
    (void)path;
    (void)name;
    (void)value;
    (void)size;
    (void)flags;

    CHECK_PARAMETERS(path != NULL && strlen(path) > 0);
    LOG(ERROR, "hdfs_setxattr failed %s", path);
    return -ENOSYS;
}

/** Get extended attributes */
// Not supported
int hdfs_getxattr(const char* path, const char* name, char* value,
                  size_t size) {
    (void)path;
    (void)name;
    (void)value;
    (void)size;

    CHECK_PARAMETERS(path != NULL && strlen(path) > 0);
    LOG(ERROR, "hdfs_getxattr failed %s", path);
    return -ENOSYS;
}

/** List extended attributes */
// Not supported
int hdfs_listxattr(const char* path, char* list, size_t size) {
    (void)path;
    (void)list;
    (void)size;

    CHECK_PARAMETERS(path != NULL && strlen(path) > 0);
    LOG(ERROR, "hdfs_listxattr failed %s", path);
    return -ENOSYS;
}

/** Remove extended attributes */
// Not supported
int hdfs_removexattr(const char* path, const char* name) {
    (void)path;
    (void)name;

    CHECK_PARAMETERS(path != NULL && strlen(path) > 0);
    LOG(ERROR, "hdfs_removexattr failed %s", path);
    return -ENOSYS;
}

/** Open directory
 *
 * Unless the 'default_permissions' mount option is given,
 * this method should check if opendir is permitted for this
 * directory. Optionally opendir may also return an arbitrary
 * filehandle in the fuse_file_info structure, which will be
 * passed to readdir, releasedir and fsyncdir.
 *
 * Introduced in version 2.3
 */
int hdfs_opendir(const char* path, struct fuse_file_info* fi) {
    CHECK_PARAMETERS(path != NULL && strlen(path) > 0 && fi != NULL);
    LOG(DEBUG, "hdfs_opendir %s", path);
    normalized_path npath(path);

    FuseTraceApi(hdfsFuseCliOpendir, npath.c_str());
    LOG(DEBUG, "hdfsExistsExtended %s", npath.c_str());
    int ret = hdfsExistsExtended(HDFS_FS, npath);
    if (ret == 0) {
        ret = -1;
        return -ENOENT;
    }

    readdir_context* ctx = new readdir_context;
    ctx->has_remaining = true;
    ctx->off_internal = 0;
    fi->fh = (uint64_t)ctx;
    FuseEndTraceSuccess();
    return 0;
}

/** Read directory
 *
 * This supersedes the old getdir() interface.  New applications
 * should use this.
 *
 * The filesystem may choose between two modes of operation:
 *
 * 1) The readdir implementation ignores the offset parameter, and
 * passes zero to the filler function's offset.  The filler
 * function will not return '1' (unless an error happens), so the
 * whole directory is read in a single readdir operation.  This
 * works just like the old getdir() method.
 *
 * 2) The readdir implementation keeps track of the offsets of the
 * directory entries.  It uses the offset parameter and always
 * passes non-zero offset to the filler function.  When the buffer
 * is full (or an error happens) the filler function will return
 * '1'.
 *
 * Introduced in version 2.3
 */
// Mode 2
// fi->fh store a pointer to startAfter
// off is ignored
int hdfs_readdir(const char* path, void* buf, fuse_fill_dir_t filler, off_t off,
                 struct fuse_file_info* fi,
                 enum fuse_readdir_flags readdir_flags) {
    CHECK_PARAMETERS(path != NULL && strlen(path) > 0 && buf != NULL &&
                     fi != NULL);

    LOG(DEBUG, "hdfs_readdir %s, off %lu, readdir_flags: %d", path, off,
        readdir_flags);
    CHECK_NAME_LENGTH(path)
    normalized_path npath(path);

    FuseTraceApi(hdfsFuseCliReaddir, npath.c_str());
    readdir_context* ctx = (readdir_context*)fi->fh;
    if (!ctx->has_remaining && ctx->entries.empty()) {
        LOG(DEBUG,
            "hdfs_readdir %s, off %lu, has_remaining %d, entry is empty %d",
            path, off, ctx->has_remaining, ctx->entries.empty());
        FuseEndTraceSuccess();
        return 0;
    }

    int filler_ret = 0;
    enum fuse_fill_dir_flags dir_flags =
        (readdir_flags & FUSE_READDIR_PLUS)
            ? FUSE_FILL_DIR_PLUS
            : static_cast<fuse_fill_dir_flags>(0);

    if (off == 0) {
        filler_ret = filler(buf, ".", nullptr, ++off, dir_flags);
        CHECK_ERROR(filler_ret, 1, filler);
        ctx->prev_req_off = 1;
    }

    if (off == 1) {
        filler_ret = filler(buf, "..", nullptr, ++off, dir_flags);
        CHECK_ERROR(filler_ret, 1, filler);
        ctx->prev_req_off =
            2;  // fill ".." may failed, so should not accumulate 1
    }
    int origin_off = off;
    off_t skip_number = off - ctx->prev_req_off;
    LOG(DEBUG,
        "hdfs_readdir %s ctx->off_internal(%lu), off(%lu), prev req "
        "offser(%lu), skip (%lu) entry from begin of entries",
        path, ctx->off_internal, off, ctx->prev_req_off, skip_number);
    while (skip_number > 0) {
        ctx->entries.pop_front();
        skip_number--;
    }
    auto itr = ctx->entries.begin();
    ctx->off_internal = off;
    int skip_number_inner = 0;
    do {
        while (itr != ctx->entries.end()) {
            filler_ret =
                filler(buf, itr->first.c_str(), &itr->second, ++off, dir_flags);
            if (filler_ret == 0) {
                itr++;
                ctx->off_internal++;
                skip_number_inner++;
            } else {
                LOG(DEBUG, "hdfs_readdir %s, off %lu, filler_ret(%d) != 0",
                    path, off, filler_ret);
                break;
            }
        }
        if (filler_ret != 0) {
            LOG(DEBUG, "hdfs_readdir %s, off %lu, filler_ret(%d) != 0", path,
                off, filler_ret);
            break;
        }
        if (filler_ret == 0 && !ctx->has_remaining) {
            LOG(DEBUG,
                "hdfs_readdir %s, off %lu, filler_ret == 0 and "
                "ctx->has_remaining(%d) "
                "== 0",
                path, off, ctx->has_remaining);
            break;
        }

        LOG(DEBUG, "hdfsListDirectory %s, start_after: %s, off is %lu",
            npath.c_str(), ctx->start_after.c_str(), off);
        int num_entries;
        hdfsFileInfoEx* entries = hdfsListDirectoryExV2(
            HDFS_FS, npath, &num_entries, ctx->start_after.c_str(),
            &ctx->has_remaining);
        CHECK_ERROR(entries, nullptr, hdfsListDirectory);
        LOG(DEBUG, "hdfsListDirectory %s, find %d entries", npath.c_str(),
            num_entries);
        for (int i = 0; i < num_entries; i++) {
            struct stat stat_buf;
            int start = extract_entryname(entries[i].mName);
            set_attr(entries[i], nullptr, &stat_buf);
            ctx->entries.emplace_back(entries[i].mName + start, stat_buf);
        }
        hdfsFreeFileInfoEx(entries, num_entries);
        if (!ctx->entries.empty()) {
            ctx->start_after = ctx->entries.back().first;
        }
        itr = ctx->entries.begin();
        std::advance(itr, skip_number_inner);
    } while (true);
    ctx->prev_req_off = origin_off;
    FuseEndTraceSuccess();
    return 0;
}

/** Release directory
 *
 * Introduced in version 2.3
 */
int hdfs_releasedir(const char* path, struct fuse_file_info* fi) {
    CHECK_PARAMETERS(path != NULL && strlen(path) > 0 && fi != NULL);
    LOG(DEBUG, "hdfs_releasedir %s", path);
    CHECK_NAME_LENGTH(path)
    FuseTraceApi(hdfsFuseCliReleasedir, path);
    delete (readdir_context*)fi->fh;
    FuseEndTraceSuccess();
    return 0;
}

/** Synchronize directory contents
 *
 * If the datasync parameter is non-zero, then only the user data
 * should be flushed, not the meta data
 *
 * Introduced in version 2.3
 */
// Do nothing
int hdfs_fsyncdir(const char* path, int datasync, struct fuse_file_info* fi) {
    CHECK_PARAMETERS(path != NULL && strlen(path) > 0);
    (void)datasync;
    (void)fi;

    LOG(DEBUG, "hdfs_fsyncdir %s", path);
    CHECK_NAME_LENGTH(path)
    return 0;
}

/**
 * Initialize filesystem
 *
 * The return value will passed in the private_data field of
 * fuse_context to all file operations and as a parameter to the
 * destroy() method.
 *
 * Introduced in version 2.3
 * Changed in version 2.6
 */
void* hdfs_init(struct fuse_conn_info* conn, struct fuse_config* cfg) {
    cfg->entry_timeout = 30;
    cfg->attr_timeout = 30;
    cfg->negative_timeout = 30;

    // disable async read
    conn->want &= ~FUSE_CAP_ASYNC_READ;

    const char* hdfs_user = getenv("HDFS_USER");
    struct passwd* pwd;
    if (hdfs_user == NULL) {
        pwd = getpwuid(getuid());
        hdfs_user = pwd->pw_name;
    }

    struct hdfsBuilder* bld = hdfsNewBuilder();
    hdfsBuilderSetUserName(bld, hdfs_user);

    if (!root_config.parse_config(bld)) {
        LOG(ERROR, "parse config failed when init hdfs");
        hdfsFreeBuilder(bld);
        return NULL;
    }

    auto hdfs_dir_prefix = trimString(root_config.get_hdfs_prefix());
    static const size_t min_level = 3;
    if (!isAllowLevelDir(hdfs_dir_prefix, min_level)) {
        LOG(ERROR,
            "Failed to parse mount directory level: %s, minimum level: %ld",
            hdfs_dir_prefix.c_str(), min_level);
        hdfsFreeBuilder(bld);
        return NULL;
    }

    auto hdfs_namespace = root_config.get_hdfs_namespace();
    if (!hdfs_namespace.empty()) {
        hdfsBuilderSetNameNode(bld, hdfs_namespace.c_str());
    }

    hdfsFS fs = NULL;
    for (int i = 0; i < root_config.get_max_retry_on_connection(); i++) {
        fs = hdfsBuilderConnect(bld);
        if (fs == NULL) {
            switch (errno) {
                case EACCES:
                    LOG(ERROR,
                        "Failed to connect to filesystem, error message: %s, "
                        "errno: %d",
                        hdfsGetLastError(), errno);
                    break;
                default:
                    LOG(WARNING,
                        "Failed to connect to filesystem, error message: %s, "
                        "errno: %d",
                        hdfsGetLastError(), errno);
                    sleep(1);
            }
        } else {
            break;
        }
    }
    hdfsFreeBuilder(bld);
    if (fs == NULL) {
        LOG(ERROR, "Failed to connect to filesystem");
        return NULL;
    }

    root_logger.setLogLevel(root_config.get_log_severity());

    // todo(liuxintao): fuck ACC fuck only support hdfs
    root_config.set_filesystem_mode("hdfs");
    root_config.set_acc_write_mode();

    normalized_path::set_prefix(hdfs_dir_prefix);
    RecoverLeaseWorker::start(fs);
    root_fs = fs;
    return fs;
}

/**
 * Clean up filesystem
 *
 * Called on filesystem exit.
 *
 * Introduced in version 2.3
 */
void hdfs_destory(void* userdata) {
    (void)userdata;
    FuseTraceApi(hdfsFuseCliDestroy, "dummy");
    if (HDFS_FS != NULL) {
        RecoverLeaseWorker::stop();
        hdfsDisconnect(HDFS_FS);
        FuseEndTraceSuccess();
        return;
    }
    LOG(ERROR, "hdfs filesystem is null, hdfs distory skip");
}

/**
 * Check file access permissions
 *
 * This will be called for the access() system call.  If the
 * 'default_permissions' mount option is given, this method is not
 * called.
 *
 * This method is not called under Linux kernel versions 2.4.x
 *
 * Introduced in version 2.5
 */
// Do nothing
int hdfs_access(const char* path, int mask) {
    CHECK_PARAMETERS(path != NULL && strlen(path) > 0);
    LOG(DEBUG, "hdfs_access %s, mask %o", path, mask);
    ;
    CHECK_NAME_LENGTH(path)
    return 0;
}

/**
 * Create and open a file
 *
 * If the file does not exist, first create it with the specified
 * mode, and then open it.
 *
 * If this method is not implemented or under Linux kernel
 * versions earlier than 2.6.15, the mknod() and open() methods
 * will be called instead.
 *
 * Introduced in version 2.5
 */
int hdfs_create(const char* path, mode_t mode, struct fuse_file_info* fi) {
    CHECK_PARAMETERS(path != NULL && strlen(path) > 0 && fi != NULL);
    LOG(DEBUG, "hdfs_create %s", path);
    CHECK_NAME_LENGTH(path)
    normalized_path npath(path);

    FuseTraceApi(hdfsFuseCliCreate, npath.c_str());
    LOG(DEBUG, "hdfsOpenFileV3 %s, flags %x, mode %o", npath.c_str(), fi->flags,
        mode);
    int flags = fi->flags;

    // TODO(zhuangsiyu): Should check O_EXCL on server side.
    if (flags & (O_CREAT | O_EXCL)) {
        int ret = hdfsExistsExtended(HDFS_FS, npath);
        CHECK_ERROR(ret, -1, hdfsExistsExtended);
        if (ret > 0) {
            LOG(ERROR,
                "hdfs_create with O_EXCL, file already exsit, check failed! path:%s",
                path);
            return -EEXIST;
        }
        flags &= ~O_EXCL;
    }

    if ((flags & O_CREAT) != O_CREAT) {
        LOG(ERROR, "hdfs_create with flags:%d, not permitted! path:%s", flags,
            path);
        return -EPERM;
    }

    hdfsFile file = NULL;
    switch (root_config.get_filesystem_mode()) {
        // todo(liuxintao): I don't know what is ACC.
        // case filesystem_mode::ACC: {
        //   // TODO(zhuangsiyu):
        //   // We cannot handle O_RDWR now, for r+/w+/a+, downgrade to
        //   r/w/a if ((flags & O_RDWR) == O_RDWR) {
        //     flags &= ~O_RDWR;
        //     if ((flags & O_TRUNC) == O_TRUNC) {
        //       LOG(WARNING,
        //           "hdfs_create with O_RDWR|O_CREAT|O_TRUNC, downgrade "
        //           "to O_WRONLY|O_CREAT|O_TRUNC");
        //       flags |= O_WRONLY;
        //     } else if ((flags & O_APPEND) == O_APPEND) {
        //       LOG(WARNING,
        //           "hdfs_create with O_RDWR|O_CREAT|O_APPEND, downgrade "
        //           "to O_WRONLY|O_CREAT|O_APPEND");
        //       flags |= O_WRONLY;
        //     } else if ((flags & O_CREAT) == O_CREAT) {
        //       LOG(WARNING,
        //           "hdfs_create with O_RDWR|O_CREAT|O_APPEND, downgrade "
        //           "to O_WRONLY|O_CREAT");
        //       flags |= O_WRONLY;
        //     } else {
        //       LOG(WARNING, "hdfs_create with O_RDWR, downgrade to
        //       O_RDONLY"); flags |= O_RDONLY;
        //     }
        //   }

        //   if (flags & O_WRONLY) {
        //     switch (root_config.get_acc_write_mode()) {
        //       case acc_write_mode::ASYNC: flags |= O_ASYNC; break;
        //       case acc_write_mode::THROUGH:
        //         // No extra flags
        //         break;
        //       default:
        //         flags |= O_ASYNC;
        //         LOG(ERROR, "Unexpected acc write mode, use async");
        //     }
        //   }

        //   int is_appendable = 0;
        //   if ((flags & O_WRONLY) &&
        //       (flags & O_APPEND || is_acc_appendable_file(npath))) {
        //     LOG(INFO, "Open appendable file %s", npath.c_str());
        //     is_appendable = true;
        //   }

        //   file =
        //       hdfsOpenFileAcc(HDFS_FS, npath, flags, mode & 0777, false,
        //       is_appendable);
        //   break;
        // }
        case filesystem_mode::HDFS: {
            CreateFileOption option = NewCreateFileOption();
            option.permission = mode & 0777;
            LOG(DEBUG, "%s permission is %d", npath.c_str(), option.permission);
            file = hdfsOpenFileV3(HDFS_FS, npath, flags, 0, 0, 0, option);
            break;
        }
        default:
            LOG(ERROR, "Unexpected filesystem mode");
    }
    CHECK_ERROR(file, nullptr, hdfsOpenFileV3);
    {
        std::unique_lock<std::shared_mutex> lock(cache_uc_fileinfo_mutex_);
        g_cache_uc_fileinfo.emplace(npath.c_str(),
                                    std::make_shared<UCFileInfo>(file));
    }
    file_handler* fh = new file_handler;
    fh->file = file;
    fi->fh = (uint64_t)fh;
    FuseEndTraceSuccess();
    return 0;
}

/**
 * Change the size of an open file
 *
 * This method is called instead of the truncate() method if the
 * truncation was invoked from an ftruncate() system call.
 *
 * If this method is not implemented or under Linux kernel
 * versions earlier than 2.6.15, the truncate() method will be
 * called instead.
 *
 * Introduced in version 2.5
 */
// Not supported
// int hdfs_ftruncate(const char *path, off_t off, struct fuse_file_info *fi) {
//     (void)path;
//     (void)off;
//     (void)fi;

//     errno = ENOSYS;
//     PLOG(ERROR) << "hdfs_ftruncate failed";
//     return -errno;
// }

/**
 * Get attributes from an open file
 *
 * This method is called instead of the getattr() method if the
 * file information is available.
 *
 * Currently this is only called after the create() method if that
 * is implemented (see above).  Later it may be called for
 * invocations of fstat() too.
 *
 * Introduced in version 2.5
 */
/*
int hdfs_fgetattr(const char *path, struct stat *stat_buf,
        struct fuse_file_info *fi) {
    return -ENOSYS;
}
*/

/**
 * Perform POSIX file locking operation
 *
 * The cmd argument will be either F_GETLK, F_SETLK or F_SETLKW.
 *
 * For the meaning of fields in 'struct flock' see the man page
 * for fcntl(2).  The l_whence field will always be set to
 * SEEK_SET.
 *
 * For checking lock ownership, the 'fuse_file_info->owner'
 * argument must be used.
 *
 * For F_GETLK operation, the library will first check currently
 * held locks, and if a conflicting lock is found it will return
 * information without calling this method.	 This ensures, that
 * for local locks the l_pid field is correctly filled in.	The
 * results may not be accurate in case of race conditions and in
 * the presence of hard links, but it's unlikely that an
 * application would rely on accurate GETLK results in these
 * cases.  If a conflicting lock is not found, this method will be
 * called, and the filesystem may fill out l_pid by a meaningful
 * value, or it may leave this field zero.
 *
 * For F_SETLK and F_SETLKW the l_pid field will be set to the pid
 * of the process performing the locking operation.
 *
 * Note: if this method is not implemented, the kernel will still
 * allow file locking to work locally.  Hence it is only
 * interesting for network filesystems and similar.
 *
 * Introduced in version 2.6
 */
/*
// Not implemented. Fallback to system lock
int hdfs_lock(const char *path, struct fuse_file_info *fi, int cmd,
        struct flock *fl) {
    return -ENOSYS;
}
*/

/**
 * Change the access and modification times of a file with
 * nanosecond resolution
 *
 * This supersedes the old utime() interface.  New applications
 * should use this.
 *
 * See the utimensat(2) man page for details.
 *
 * Introduced in version 2.6
 */
int hdfs_utimens(const char* path, const struct timespec tv[2],
                 struct fuse_file_info* fi) {
    (void)fi;
    CHECK_PARAMETERS(path != NULL && strlen(path) > 0);
    LOG(DEBUG, "hdfs_utimes %s, mtime: %lu, atime: %lu", path, tv[1].tv_sec,
        tv[0].tv_sec);
    CHECK_NAME_LENGTH(path)
    normalized_path npath(path);

    FuseTraceApi(hdfsFuseCliUtimens, npath.c_str());
    LOG(DEBUG, "hdfsUtime %s, mtime: %lu, atime: %lu", npath.c_str(),
        tv[1].tv_sec * 1000, tv[0].tv_sec * 1000);
    int ret =
        hdfsUtime(HDFS_FS, npath, tv[1].tv_sec * 1000, tv[0].tv_sec * 1000);
    CHECK_ERROR(ret, -1, hdfsUtime);
    FuseEndTraceSuccess();
    return 0;
}

/**
 * Map block index within file to block index within device
 *
 * Note: This makes sense only for block device backed filesystems
 * mounted with the 'blkdev' option
 *
 * Introduced in version 2.6
 */
// Not supported
int hdfs_bmap(const char* path, size_t blocksize, uint64_t* idx) {
    (void)blocksize;
    (void)idx;

    CHECK_PARAMETERS(path != NULL && strlen(path) > 0);
    LOG(ERROR, "hdfs_bmap failed %s", path);
    return -ENOSYS;
}

static int switchFileModel(const char* path, unsigned int cmd, void* arg,
                           struct fuse_file_info* fi, unsigned int flags,
                           void* data) {
    (void)cmd;
    (void)arg;
    (void)flags;
    (void)data;

    CHECK_PARAMETERS(path != NULL && strlen(path) > 0 && fi != NULL);
    CHECK_NAME_LENGTH(path)
    normalized_path npath(path);

    // create new file
    FuseTraceApi(hdfsFuseCliSwitchfilemodel, npath.c_str());
    CreateFileOption option;
    option.parallel_io_num = FUSE_PARALLEL_NUM(arg);
    option.partial_file_size = FUSE_PARALLEL_FILESIZE(arg);
    option.stripe_unit_count = 0;
    option.stripe_unit_size = 0;
    bool create = FUSE_PARALLEL_TYPE(arg);

    // TODO just for test, the flag should consider seriasly
    flags = create ? (O_WRONLY | O_TRUNC) : O_RDONLY;
    auto file = hdfsOpenFileV3(HDFS_FS, npath, flags, 0, 0, 0, option);
    CHECK_ERROR(file, nullptr, hdfsOpenFileV3);

    // close old file
    hdfsFile old_file = (hdfsFile)GET_FILE(fi);
    int ret = hdfsCloseFile(HDFS_FS, old_file);
    // here we don't care old file close status, and it may close failed
    // because file already overwrite CHECK_ERROR(ret, -1, hdfsCloseFile);
    // set new file
    ((struct file_handler*)fi->fh)->file = file;
    if (!create && option.parallel_io_num > 0) {
        std::vector<std::mutex> locks(option.parallel_io_num);
        ((struct file_handler*)fi->fh)->read_locks.swap(locks);
    }

    LOG(INFO,
        "######switchFileModel succeed %s, create %d, flags %x, parallel_io_num "
        "%d, partial_file_size %lu",
        npath.c_str(), create, fi->flags, option.parallel_io_num,
        option.partial_file_size);
    FuseEndTraceSuccess();
    return 0;
}

/**
 * Ioctl
 *
 * flags will have FUSE_IOCTL_COMPAT set for 32bit ioctls in
 * 64bit environment.  The size and direction of data is
 * determined by _IOC_*() decoding of cmd.  For _IOC_NONE,
 * data will be NULL, for _IOC_WRITE data is out area, for
 * _IOC_READ in area and if both are set in/out area.  In all
 * non-NULL cases, the area is of _IOC_SIZE(cmd) bytes.
 *
 * If flags has FUSE_IOCTL_DIR then the fuse_file_info refers to a
 * directory file handle.
 *
 * Introduced in version 2.8
 */
// Not supported
int hdfs_ioctl(const char* path, unsigned int cmd, void* arg,
               struct fuse_file_info* fi, unsigned int flags, void* data) {
    (void)cmd;
    (void)arg;
    (void)fi;
    (void)flags;
    (void)data;

    CHECK_PARAMETERS(path != NULL && strlen(path) > 0);

    if (fuse_ioctl_is_parallel_rw(cmd)) {
        return switchFileModel(path, cmd, arg, fi, flags, data);
    }

    switch (cmd) {
        case TCGETS:
            return -ENOTTY;
            break;
        default:
            return -ENOSYS;
            break;
    }
}

/**
 * Poll for IO readiness events
 *
 * Note: If ph is non-NULL, the client should notify
 * when IO readiness events occur by calling
 * fuse_notify_poll() with the specified ph.
 *
 * Regardless of the number of times poll with a non-NULL ph
 * is received, single notification is enough to clear all.
 * Notifying more times incurs overhead but doesn't harm
 * correctness.
 *
 * The callee is responsible for destroying ph with
 * fuse_pollhandle_destroy() when no longer in use.
 *
 * Introduced in version 2.8
 */
// Not supported
int hdfs_poll(const char* path, struct fuse_file_info* fi,
              struct fuse_pollhandle* ph, unsigned* reventsp) {
    (void)fi;
    (void)ph;
    (void)reventsp;

    CHECK_PARAMETERS(path != NULL && strlen(path) > 0);
    LOG(ERROR, "hdfs_poll failed %s", path);
    return -ENOSYS;
}

/**
 * Allocates space for an open file
 *
 * This function ensures that required space is allocated for specified
 * file.  If this function returns success then any subsequent write
 * request to specified range is guaranteed not to fail because of lack
 * of space on the file system media.
 *
 * Introduced in version 2.9.1
 */
// Not supported
int hdfs_fallocate(const char* path, int mode, off_t off, off_t len,
                   struct fuse_file_info* fi) {
    (void)mode;
    (void)off;
    (void)len;
    (void)fi;

    CHECK_PARAMETERS(path != NULL && strlen(path) > 0);
    LOG(ERROR, "hdfs_fallocate failed %s", path);
    return -ENOSYS;
}

/**
 * Copy a range of data from one file to another
 *
 * Performs an optimized copy between two file descriptors without the
 * additional cost of transferring data through the FUSE kernel module
 * to user space (glibc) and then back into the FUSE filesystem again.
 *
 * In case this method is not implemented, applications are expected to
 * fall back to a regular file copy.   (Some glibc versions did this
 * emulation automatically, but the emulation has been removed from all
 * glibc release branches.)
 */
// Not supported
ssize_t hdfs_copy_file_range(const char* path_in, struct fuse_file_info* fi_in,
                             off_t offset_in, const char* path_out,
                             struct fuse_file_info* fi_out, off_t offset_out,
                             size_t size, int flags) {
    (void)fi_in;
    (void)offset_in;
    (void)fi_out;
    (void)offset_out;
    (void)size;
    (void)flags;

    CHECK_PARAMETERS(path_in != NULL && strlen(path_in) > 0 &&
                     path_out != NULL && strlen(path_out) > 0);
    LOG(ERROR, "hdfs_copy_file_range path in %s to path out %s failed", path_in,
        path_out);
    return -ENOSYS;
}

/**
 * Find next data or hole after the specified offset
 */
off_t hdfs_lseek(const char* path, off_t off, int whence,
                 struct fuse_file_info* fuse_file_info) {
    (void)off;
    (void)whence;
    (void)fuse_file_info;

    CHECK_PARAMETERS(path != NULL && strlen(path) > 0);
    LOG(ERROR, "hdfs_lseek failed path %s", path);
    return -ENOSYS;
}
