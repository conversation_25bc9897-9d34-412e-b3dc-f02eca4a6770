cmake_minimum_required(VERSION 3.13.0)

project("hdfs-fuse" C CXX)

execute_process(COMMAND uname -m OUTPUT_VARIABLE ARCH)
message(STATUS "Arch :${ARCH}")
if ("${ARCH}" MATCHES "arm64")
  set(<PERSON>CH "aarch64")
endif ()
if ("${ARCH}" MATCHES "aarch64")
  set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -fsigned-char -ffp-contract=off")
endif ()

set(CMAKE_POSITION_INDEPENDENT_CODE ON)
add_compile_options(-Wall)
add_compile_options(-Wextra)
add_compile_options(-Werror)
add_compile_options(-ggdb3)
add_compile_options(-fno-omit-frame-pointer)

if (NOT "${ARCH}" MATCHES "aarch64")
add_compile_options(-msse4)
endif ()

# third party libs should be included in libhdfs_client.so.
include_directories(SYSTEM ${HDFS_CLIENT_THIRDPARTY_ROOT}/include)
link_directories(${HDFS_CLIENT_THIRDPARTY_ROOT}/lib)

# build
add_subdirectory(cli)
if(HDFS_BUILD_TEST)
  add_subdirectory(test)
endif()
