diff --git a/.gitignore b/.gitignore
index 590153446..2a0df6846 100644
--- a/.gitignore
+++ b/.gitignore
@@ -12,3 +12,4 @@ __pycache__/
 .vscode
 .clang-format
 complile_commands.json
+tags
diff --git a/CMakeLists.txt b/CMakeLists.txt
index 150f50692..26ea80449 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -81,6 +81,7 @@ include(cmake/compile_proto.cmake)  # util fucntion easy to handle proto target.
 add_library(byterpc_compile_option INTERFACE)
 add_library(byterpc_user_compile_option INTERFACE)
 
+find_package(absl REQUIRED)
 # Exported targets: byterpc_include
 add_subdirectory(include)
 
diff --git a/cmake/dependency.cmake b/cmake/dependency.cmake
index 82bd59a49..e4c22f0a6 100644
--- a/cmake/dependency.cmake
+++ b/cmake/dependency.cmake
@@ -1,7 +1,7 @@
 
 # TOOD(crq): use target_compile_features
 # make sure byte target built with GNU++14
-set(CMAKE_CXX_STANDARD 14)
+set(CMAKE_CXX_STANDARD 17)
 set(CMAKE_CXX_STANDARD_REQUIRED ON)
 set(CMAKE_CXX_EXTENSIONS OFF)
 
@@ -28,11 +28,11 @@ target_include_directories(boost_header INTERFACE ${Boost_INCLUDE_DIR})
 add_library(tarzan_header INTERFACE)
 target_include_directories(tarzan_header INTERFACE ${byterpc_SOURCE_DIR}/third/tarzan/)
 
-add_library(nlohmann_json_header INTERFACE)
-target_include_directories(nlohmann_json_header INTERFACE ${THIRDPARTY_ROOT}/json/include/)
+# add_library(nlohmann_json_header INTERFACE)
+# target_include_directories(nlohmann_json_header INTERFACE ${THIRDPARTY_ROOT}/json/include/)
 
-add_library(spdlog_header INTERFACE)
-target_include_directories(spdlog_header INTERFACE ${THIRDPARTY_ROOT}/spdlog/include/)
+# add_library(spdlog_header INTERFACE)
+# target_include_directories(spdlog_header INTERFACE ${THIRDPARTY_ROOT}/spdlog/include/)
 
 add_library(bemalloc_header INTERFACE)
 target_include_directories(bemalloc_header INTERFACE ${byterpc_SOURCE_DIR}/third/byte_express/external/bemalloc/src/)
diff --git a/cmake/set_compile_option.cmake b/cmake/set_compile_option.cmake
index f7ee615c2..06dd3453b 100644
--- a/cmake/set_compile_option.cmake
+++ b/cmake/set_compile_option.cmake
@@ -1,6 +1,6 @@
 
 # TOOD(crq): use target_compile_features
-set(CMAKE_CXX_STANDARD 14)
+set(CMAKE_CXX_STANDARD 17)
 set(CMAKE_CXX_STANDARD_REQUIRED ON)
 set(CMAKE_CXX_EXTENSIONS OFF)
 
diff --git a/include/CMakeLists.txt b/include/CMakeLists.txt
index accaee8d4..0b8bec01c 100644
--- a/include/CMakeLists.txt
+++ b/include/CMakeLists.txt
@@ -56,3 +56,18 @@ target_link_libraries(byterpc_include INTERFACE
   gflags        # from byterpc_flags.h
   absl::base    # from tuil/timestamp.h
 )
+
+install(DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}/byterpc
+        DESTINATION include
+        FILES_MATCHING
+        PATTERN "*.h"
+        PATTERN "*.hpp"
+        )
+
+install(DIRECTORY ${GENERATED_HEADERS_DIR}/byterpc
+        DESTINATION include
+        FILES_MATCHING
+        PATTERN "*.h"
+        PATTERN "*.hpp"
+        )
+
diff --git a/src/CMakeLists.txt b/src/CMakeLists.txt
index a987dd541..7f5372da4 100644
--- a/src/CMakeLists.txt
+++ b/src/CMakeLists.txt
@@ -1,14 +1,14 @@
 set(CMAKE_VERBOSE_MAKEFILE ON)
 
 # --------- dep check --------- 
-if (NOT TARGET nlohmann_json_header)
-  message(FATAL_ERROR "json header")
-endif()
+# if (NOT TARGET nlohmann_json_header)
+#   message(FATAL_ERROR "json header")
+# endif()
 
 # metrics/metrics.cpp use it internally.
-if (NOT TARGET spdlog_header)
-  message(FATAL_ERROR "spdlog header")
-endif()
+# if (NOT TARGET spdlog_header)
+#   message(FATAL_ERROR "spdlog header")
+# endif()
 # ------------- end -------------
 
 set(GENERATED_INCLUDE_DIR ${CMAKE_CURRENT_BINARY_DIR}/gen_files)
@@ -43,7 +43,7 @@ target_link_libraries(byterpc_internal_include INTERFACE
   # - byte/byte/embedded_metrics/metrics_registry.h:16:29: 
   # - nlohmann/json.hpp: No such file or directory
   # maybe we can replace this dep when using target-based byte::embeded_metrics
-  nlohmann_json_header
+  # nlohmann_json_header
 
   # used for unify_memory_pool
   bemalloc_header)
@@ -109,3 +109,6 @@ target_link_libraries(byterpc PRIVATE bemalloc_uc)
 
 # byterpc_internal_include also included all deps that need to compile byterpc.
 target_link_libraries(byterpc PRIVATE byterpc_internal_include)
+
+install(TARGETS byterpc ARCHIVE DESTINATION lib)
+
diff --git a/src/builtin/dump_stacktrace_service.cpp b/src/builtin/dump_stacktrace_service.cpp
index d87d69656..2e7a94dd4 100644
--- a/src/builtin/dump_stacktrace_service.cpp
+++ b/src/builtin/dump_stacktrace_service.cpp
@@ -10,6 +10,7 @@
 #include <sstream>
 #include <utility>
 #include <vector>
+#include <signal.h>
 
 #include "byterpc/byterpc_flags.h"
 #include "byterpc/controller.h"
diff --git a/src/proto/CMakeLists.txt b/src/proto/CMakeLists.txt
index 4f81e5e25..9a312f6bb 100644
--- a/src/proto/CMakeLists.txt
+++ b/src/proto/CMakeLists.txt
@@ -5,7 +5,7 @@ set(BYTERPC_PROTO_FILES proto/builtin_service.proto
                         proto/streaming_rpc_meta.proto)
 
 set(BYTERPC_PROTO_OUTPUT_DIR ${GENERATED_INCLUDE_DIR})
-set(PROTOBUF_PROTOC_EXECUTABLE "protoc")
+find_program(PROTOBUF_PROTOC_EXECUTABLE protoc REQUIRED)
 set(PROTOC_FLAGS ${PROTOC_FLAGS} -I${byterpc_SOURCE_DIR}/third/byte/thirdparty/protobuf/src)
 
 compile_proto(BYTERPC_PROTO_HDRS BYTERPC_PROTO_SRCS
diff --git a/src/rpc/builder.cpp b/src/rpc/builder.cpp
index a68c4c5c6..2428e2514 100644
--- a/src/rpc/builder.cpp
+++ b/src/rpc/builder.cpp
@@ -2,6 +2,7 @@
 #include "byterpc/builder.h"
 
 #include <utility>
+#include <signal.h>
 
 #include "byterpc/byterpc_flags.h"
 #include "byterpc/controller.h"
diff --git a/src/rpc/server.cpp b/src/rpc/server.cpp
index 800d48575..998f44268 100644
--- a/src/rpc/server.cpp
+++ b/src/rpc/server.cpp
@@ -7,6 +7,7 @@
 #include <memory>
 #include <mutex>  // NOLINT(build/c++11)
 #include <vector>
+#include <signal.h>
 
 #include "builtin/service_greeter.h"
 #include "byterpc/rpc.h"
diff --git a/third/CMakeLists.txt b/third/CMakeLists.txt
index d90607e71..e36ccb1a2 100644
--- a/third/CMakeLists.txt
+++ b/third/CMakeLists.txt
@@ -4,7 +4,15 @@ if(BYTERPC_ENABLE_ASAN)
     set(THIRDPARTY_WITH_ASAN ON CACHE BOOL "Whether to enable thirdparty fsanitize=address" FORCE)
 endif()
 
+find_library(BYTE_LIB libbyte.a REQUIRED)
+add_library(byte STATIC IMPORTED GLOBAL)
+set_property(TARGET byte PROPERTY
+             IMPORTED_LOCATION "${BYTE_LIB}")
+
+target_link_libraries(byte INTERFACE ${BYTE_LIB})
+
 if(NOT TARGET byte)
+    message(FATAL_ERROR "byte not found!")
     # disable byte unittest
     set(BYTE_BUILD_TESTS OFF CACHE BOOL "Build unit tests of byte" FORCE)
     # disable dep-less lib in thirdparty
@@ -42,7 +50,15 @@ if(BYTERPC_ENABLE_BYTE_EXPRESS)
     # set BYTE_EXPRESS_USE_INTERNAL_BOOST_LIBRARY to OFF and let ByteRPC decide the BOOST_ROOT
     set(BYTE_EXPRESS_USE_INTERNAL_BOOST_LIBRARY OFF CACHE BOOL "build byte_express with system boost" FORCE)
     add_subdirectory(byte_express)
+    # install targets of the "bemalloc" third party
+    install(TARGETS bemalloc_uc ARCHIVE DESTINATION lib)
+    # install targets of the "rdma-core-dummy" third party
+    install(TARGETS ibverbs ARCHIVE DESTINATION lib)
+    install(TARGETS rdmacm ARCHIVE DESTINATION lib)
+    # install targets of "byte_express"
+    install(TARGETS byte_express ARCHIVE DESTINATION lib)
 else()
     # if not enable BYTE_EXPRESS, try to add bemalloc, it will use in unify_memory_pool
     add_subdirectory(byte_express/external/bemalloc)
+    install(TARGETS bemalloc_uc ARCHIVE DESTINATION lib)
 endif()
