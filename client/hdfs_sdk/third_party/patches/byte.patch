diff --git a/.gitignore b/.gitignore
index 74e1192d..c22e3adc 100644
--- a/.gitignore
+++ b/.gitignore
@@ -9,3 +9,4 @@ valgrind.log
 zk,log
 byte/encoding/testproto/*.pb.h
 byte/encoding/testproto/*.pb.cc
+tags
diff --git a/CMakeLists.txt b/CMakeLists.txt
index abbea5c5..58dd9661 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -52,7 +52,10 @@ if(BYTE_WITH_UBSAN)
     set(BYTE_CC_LIB_LINKOPTS "${BYTE_CC_LIB_LINKOPTS} -fsanitize=undefined")
 endif()
 
-add_subdirectory(thirdparty)
+option(BYTE_BUILD_THIRDPARTY "Whether to build thirdparty" ON)
+if(BYTE_BUILD_THIRDPARTY)
+    add_subdirectory(thirdparty)
+endif()
 option(BYTE_BUILD_TESTS "Build unit tests of byte " OFF)
 if(BYTE_BUILD_TESTS)
   enable_testing()
diff --git a/byte/CMakeLists.txt b/byte/CMakeLists.txt
index f8d5de87..9a5274cb 100644
--- a/byte/CMakeLists.txt
+++ b/byte/CMakeLists.txt
@@ -1,3 +1,6 @@
+include_directories(${CMAKE_INCLUDE_PATH})
+include_directories(${PROJECT_SOURCE_DIR})
+
 include("cmake/utils.cmake")
 
 set(CMAKE_CXX_FLAGS "-Wall -Werror -lpthread -lm ${CMAKE_CXX_FLAGS}")
@@ -74,7 +77,8 @@ list(APPEND BYTE_SRC
     "concurrent/rwlock.cc"
     "concurrent/spinlock.cc"
     "concurrent/timer_manager.cc"
-    "container/block_cache.cc"
+    # remove container/block_cache.cc to remove dependency on isa-l
+    # "container/block_cache.cc"
     "container/lru_cache.cc"
     "container/radix_tree.cc"
     "container/rbtree.cc"
@@ -83,16 +87,18 @@ list(APPEND BYTE_SRC
     "encoding/base64.cc"
     "encoding/int128.cc"
     "encoding/bin2ascii.cc"
-    "encoding/json2pb.cc"
+    # remove container/block_cache.cc to remove dependency on protobuf
+    # "encoding/json2pb.cc"
     "io/file.cc"
     "io/file_path.cc"
     "io/file_util.cc"
     "io/local_file.cc"
     "io/local_filesystem.cc"
     "io/recordio.cc"
-    "keeper/keeper.cc"
-    "keeper/keeper_config.cc"
-    "keeper/zk_client.cc"
+    # remove container/block_cache.cc to remove dependency on zookeeper
+    # "keeper/keeper.cc"
+    # "keeper/keeper_config.cc"
+    # "keeper/zk_client.cc"
     "metrics/metric_client.cc"
     "metrics/metric_collector.cc"
     "metrics/metrics.cc"
@@ -130,7 +136,8 @@ list(APPEND BYTE_SRC
     "thread/thread_local.cc"
     "thread/thread.cc"
     "thread/timer_thread.cc"
-    "thread/waiter.cc"
+    # remove container/block_cache.cc to remove dependency on bthread of brpc
+    # "thread/waiter.cc"
     "tracing/ioprof.cc"
     "tracing/tracing.cc"
     "tracing/tracing_collector.cc"
@@ -186,7 +193,7 @@ list(APPEND BYTE_INCLUDE
     "concurrent/spinlock.h"
     "concurrent/timer_manager.h"
     "container/autovector.h"
-    "container/block_cache.h"
+    # "container/block_cache.h"
     "container/btree/btree.h"
     "container/btree/btree_container.h"
     "container/btree/btree_map.h"
@@ -211,7 +218,7 @@ list(APPEND BYTE_INCLUDE
     "encoding/int128.h"
     "encoding/plain_buffer.h"
     "encoding/variant_int.h"
-    "encoding/json2pb.h"
+    # "encoding/json2pb.h"
     "include/assert.h"
     "include/byte_log.h"
     "include/macros.h"
@@ -225,10 +232,10 @@ list(APPEND BYTE_INCLUDE
     "io/local_file.h"
     "io/local_filesystem.h"
     "io/recordio.h"
-    "keeper/keeper.h"
-    "keeper/keeper_client.h"
-    "keeper/zk_client.h"
-    "keeper/keeper_config.h"
+    # "keeper/keeper.h"
+    # "keeper/keeper_client.h"
+    # "keeper/zk_client.h"
+    # "keeper/keeper_config.h"
     "metrics/metric_client.h"
     "metrics/metric_collector.h"
     "metrics/metric_collector_conf.h"
@@ -236,6 +243,7 @@ list(APPEND BYTE_INCLUDE
     "embedded_metrics/metrics_holder.h"
     "embedded_metrics/metrics.h"
     "embedded_metrics/metrics_registry.h"
+    "stats/histogram.h"
     "string/string_piece.h"
     "string/algorithm.h"
     "string/compare.h"
@@ -272,7 +280,7 @@ list(APPEND BYTE_INCLUDE
     "thread/thread.h"
     "thread/thread_local.h"
     "thread/timer_thread.h"
-    "thread/waiter.h"
+    # "thread/waiter.h"
     "tracing/ioprof.h"
     "tracing/tracing.h"
     "tracing/tracing_test.h"
@@ -350,339 +358,15 @@ if (TARGET msgpackc-static)
     target_link_libraries(byte PUBLIC msgpackc-static)
 endif()
 
-# besides the all-in-one "byte" target (libbyte.a), there are fine-grained
-# targets defined in the subdirectory.
-include(cmake/target_helper.cmake)
-add_subdirectory(absl_util)
-add_subdirectory(algorithm)
-add_subdirectory(base)
-add_subdirectory(block)
-add_subdirectory(byte_log)
-add_subdirectory(concurrent)
-add_subdirectory(container)
-add_subdirectory(embedded_metrics)
-add_subdirectory(dmclock)
-add_subdirectory(encoding)
-add_subdirectory(governance)
-add_subdirectory(include)
-add_subdirectory(internal)
-add_subdirectory(io)
-add_subdirectory(keeper)
-add_subdirectory(lrc)
-add_subdirectory(metrics)
-add_subdirectory(stats)
-add_subdirectory(string)
-add_subdirectory(system)
-add_subdirectory(thread)
-add_subdirectory(tracing)
-add_subdirectory(util)
-
-
-# per directory target
-
-# `byte::absl_util` target is not part of `byte`;
-# users should link `absl_util` directly to your target.
-byte_merged_cc_library(
-  NAME
-    absl_util
-  DEPS
-    byte::source_location
-    byte::status_builder
-    byte::status_matcher
-    byte::status_macros
-)
-
-byte_merged_cc_library(
-  NAME
-    algorithm
-  DEPS
-    byte::bit
-    byte::checksum
-    byte::crc32
-    byte::crc64
-    byte::random
-    byte::true_random
-)
-
-list(
-  APPEND
-  base_targets
-  byte::atomic
-  byte::bit_vector
-  byte::bloom_filter
-  byte::closure
-  byte::endian
-  byte::hash
-  byte::iovec
-  byte::mem_pool_lite
-  byte::ref_count
-  byte::ring_buffer
-  byte::singleton
-)
-if (TARGET ev-static)
-  list(APPEND base_targets byte::byte_timer)  
-endif()
-
-byte_merged_cc_library(
-  NAME
-    base
-  DEPS
-    ${base_targets}
-)
-
-byte_merged_cc_library(
-  NAME
-    block
-  DEPS
-    byte::range
-    byte::entry
-    byte::entry_table
-)
-
-byte_merged_cc_library(
-  NAME
-    # "byte_log" is already used; so add "merged_" prefix.
-    merged_byte_log
-  DEPS
-    byte::async_log_sink
-    byte::byte_log_impl
-    byte::log_config
-    byte::console_log_sink
-    byte::demangle
-    byte::log_sink
-    byte::signalhandler
-    byte::symbolize
-)
-
-byte_merged_cc_library(
-  NAME
-    concurrent
-  DEPS
-    byte::async_task
-    byte::blockingqueue
-    byte::cond
-    byte::count_down_latch
-    byte::event
-    byte::free_list
-    byte::hashtable
-    byte::limited_periodical_task
-    byte::lite_lock
-    byte::mutex
-    byte::periodical_task
-    byte::rwlock
-    byte::scoped_locker
-    byte::spinlock
-    byte::timer_manager
-)
-
-byte_merged_cc_library(
-  NAME
-    container
-  DEPS
-    byte::arena
-    byte::autovector
-    byte::block_cache
-    byte::cache
-    byte::concurrent_hash_table
-    byte::intrusive_list
-    byte::list
-    byte::lockfree_queue
-    byte::lockfree_stack
-    byte::lru_cache
-    byte::priority_queue
-    byte::radix_tree
-    byte::rbtree
-    byte::sharded_cache
-    byte::skiplist
-    byte::stl_util
-    byte::variant_vector    
-)
-
-byte_merged_cc_library(
-  NAME
-    dmclock
-  DEPS
-    byte::dmclock_types
-    byte::dmclock_util
-    byte::dmclock_client
-    byte::dmclock_server
-)
-
-byte_merged_cc_library(
-  NAME
-    # "embedded_metrics" is already used; so add "merged_" prefix.
-    merged_embedded_metrics
-  DEPS
-    byte::metrics_holder
-    byte::metrics_registry
-    byte::embedded_metrics
-)
-
-byte_merged_cc_library(
-  NAME
-    encoding
-  DEPS
-    byte::base64
-    byte::bin2ascii
-    byte::int128
-    byte::json2pb
-    byte::plain_buffer
-    byte::variant_int
-)
-
-byte_merged_cc_library(
-  NAME
-    governance
-  DEPS
-    byte::resource_trigger
-    byte::service_shield
-    byte::sys_util
-    byte::token_bucket
-)
-
-byte_merged_cc_library(
-  NAME
-    include
-  DEPS
-    byte::assert
-    byte::byte_log
-    byte::macros
-    byte::scoped_ptr
-    byte::status
-    byte::slice
-)
-
-byte_merged_cc_library(
-  NAME
-    lrc
-  DEPS
-    byte::erasure_code_lrc
-    byte::erasure_code_lrc_cache
-)
-
-byte_merged_cc_library(
-  NAME
-    # "keeper" is already used; so add "merged_" prefix.
-    merged_keeper
-  DEPS
-    byte::keeper_internal
-    byte::keeper_config
-    byte::keeper_client
-    byte::zk_client
-    byte::keeper
-)
-
-byte_merged_cc_library(
-  NAME
-    io
-  DEPS
-    byte::file_path
-    byte::file_system
-    byte::file_util
-    byte::file
-    byte::local_file
-    byte::local_filesystem
-    byte::recordio
-)
-
-byte_merged_cc_library(
-  NAME
-    # "metrics" is already used; so add "merged_" prefix.
-    merged_metrics
-  DEPS
-    byte::metrics
-    byte::metric_collector
-    byte::metric_collector_conf
-    byte::metric_client
-)
-
-byte_merged_cc_library(
-  NAME
-    stats
-  DEPS
-    byte::histogram
-)
-
-byte_merged_cc_library(
-  NAME
-    string
-  DEPS
-    byte::string_algorithm
-    byte::string_concat
-    byte::string_number
-    byte::string_escape
-    byte::string_piece
-)
-
-byte_merged_cc_library(
-  NAME
-    system
-  DEPS
-    byte::blkdev
-    byte::ip_address
-    byte::ip6_address
-    byte::os_socket
-    byte::process_info
-    byte::rdtsc
-    byte::sys_info
-    byte::timestamp
-    byte::uuid    
-)
-
-list(
-  APPEND 
-  thread_targets
-  byte::abstract_thread_pool
-  byte::base_thread_group
-  byte::base_thread_pool
-  byte::base_thread
-  byte::dynamic_thread_pool
-  byte::once_thread
-  byte::this_thread
-  byte::thread_group
-  byte::thread_local
-  byte::thread
-  byte::timer_thread
-  byte::waiter
-)
-if (TARGET ev-static)
-  list(
-    APPEND
-    thread_targets 
-    byte::async_thread
-    byte::periodical_thread
-  )
-endif()
-
-
-byte_merged_cc_library(
-  NAME
-    # "thread" is already used; so add "merged_" prefix.
-    merged_thread
-  DEPS
-    ${thread_targets}
-)
-
-byte_merged_cc_library(
-  NAME
-    # "tracing" is already used; so add "merged_" prefix.
-    merged_tracing
-  DEPS
-    byte::tracing
-    byte::tracing_pub_funcs
-    byte::tracing_dictionary
-    byte::tracing_config
-    byte::tracing_collector
-    byte::ioprof
-)
+install(TARGETS byte
+    ARCHIVE
+    DESTINATION lib
+    )
 
-byte_merged_cc_library(
-  NAME
-    util
-  DEPS
-    byte::defer
-    byte::map_util
-    byte::net_utils
-    byte::process_utils
-    byte::scope_guard
-)
+install(DIRECTORY ${PROJECT_BINARY_DIR}/byte
+    DESTINATION include
+    FILES_MATCHING
+    PATTERN "*.h"
+    PATTERN "*.hpp"
+    PATTERN "CMakeFiles" EXCLUDE
+    )
diff --git a/byte/concurrent/hashtable.h b/byte/concurrent/hashtable.h
index ff5e2606..1a61f387 100644
--- a/byte/concurrent/hashtable.h
+++ b/byte/concurrent/hashtable.h
@@ -200,7 +200,7 @@ public:
 
 private:
     mutable absl::Mutex mutex_;
-    std::unordered_map<Key, Value, Hash, KeyEqual, Allocator> table_ GUARDED_BY(mutex_);
+    std::unordered_map<Key, Value, Hash, KeyEqual, Allocator> table_ ABSL_GUARDED_BY(mutex_);
 };
 
 }  // namespace concurrent
diff --git a/byte/embedded_metrics/metrics_registry.h b/byte/embedded_metrics/metrics_registry.h
index b1644581..0f09327f 100644
--- a/byte/embedded_metrics/metrics_registry.h
+++ b/byte/embedded_metrics/metrics_registry.h
@@ -101,7 +101,7 @@ private:
         std::shared_ptr<MetricBase> metric;
         uint32_t user_ref;
     };
-    std::unordered_map<std::string, MetricRef> metrics_ GUARDED_BY(mu_);
+    std::unordered_map<std::string, MetricRef> metrics_ ABSL_GUARDED_BY(mu_);
     DISALLOW_COPY_AND_ASSIGN(MetricsRegistry);
 };
 
diff --git a/byte/keeper/keeper.h b/byte/keeper/keeper.h
index e6e4227e..e46147bc 100644
--- a/byte/keeper/keeper.h
+++ b/byte/keeper/keeper.h
@@ -11,7 +11,7 @@
 #include <utility>
 #include <vector>
 
-#include "absl/base/internal/thread_annotations.h"
+#include "absl/base/thread_annotations.h"
 #include "byte/base/atomic.h"
 #include "byte/concurrent/cond.h"
 #include "byte/concurrent/mutex.h"
diff --git a/byte/thread/timer_thread.h b/byte/thread/timer_thread.h
index eec979ab..68ef8323 100644
--- a/byte/thread/timer_thread.h
+++ b/byte/thread/timer_thread.h
@@ -13,7 +13,7 @@
 #include <utility>
 #include <vector>
 
-#include "absl/base/internal/thread_annotations.h"
+#include "absl/base/thread_annotations.h"
 #include "byte/concurrent/hashtable.h"
 #include "byte/include/status.h"
 #include "byte/thread/thread.h"
@@ -125,11 +125,11 @@ private:
     // See https://gcc.gnu.org/bugzilla/show_bug.cgi?id=41861
     // So we use pthread_cond_t directly.
     pthread_cond_t cv_;
-    bool stopped_ GUARDED_BY(mu_);
+    bool stopped_ ABSL_GUARDED_BY(mu_);
     std::unique_ptr<concurrent::HashTable<uint64_t, std::shared_ptr<Task>>> task_map_
-        GUARDED_BY(mu_);
+        ABSL_GUARDED_BY(mu_);
     std::priority_queue<std::shared_ptr<Task>, std::vector<std::shared_ptr<Task>>, TaskComparator>
-        timer_tasks_ GUARDED_BY(mu_);
+        timer_tasks_ ABSL_GUARDED_BY(mu_);
     std::unique_ptr<Thread> thread_;
     std::string thread_name_;
     void loop();
