set(name protobuf-3.6.1)
set(source_dir ${CMAKE_CURRENT_BINARY_DIR}/${name}/source)
ExternalProject_Add(
    ${name}
    URL https://github.com/protocolbuffers/protobuf/archive/v3.6.1.zip
    URL_HASH MD5=e09a2a7d3b34a271aedfc0b38ac2a4dc
    DOWNLOAD_NAME protobuf-3.6.1.zip
    PREFIX ${CMAKE_CURRENT_BINARY_DIR}/${name}
    TMP_DIR ${BUILD_INFO_DIR}
    STAMP_DIR ${BUILD_INFO_DIR}
    DOWNLOAD_DIR ${DOWNLOAD_DIR}
    SOURCE_DIR ${source_dir}
    CONFIGURE_COMMAND
        ${common_configure_envs}
        "LIBS=${LIBS}"
        ./configure ${common_configure_args} --enable-shared=false
    BUILD_IN_SOURCE 1
    BUILD_COMMAND make -s -j${BUILDING_JOBS_NUM}
    INSTALL_COMMAND make -s -j${BUILDING_JOBS_NUM} install
    LOG_CONFIGURE TRUE
    LOG_BUILD TRUE
    LOG_INSTALL TRUE
    LOG_OUTPUT_ON_FAILURE TRUE
)

ExternalProject_Add_Step(${name} autogen
    DEPENDEES download
    DEPENDERS configure
    ALWAYS FALSE
    COMMAND ./autogen.sh
    WORKING_DIRECTORY ${source_dir}
)
ExternalProject_Add_StepTargets(${name} autogen)
