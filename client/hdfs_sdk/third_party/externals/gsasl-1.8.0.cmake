set(name gsasl-1.8.0)
set(source_dir ${CMAKE_CURRENT_BINARY_DIR}/${name}/source)
ExternalProject_Add(
    ${name}
    URL http://ftp.gnu.org/gnu/gsasl/libgsasl-1.8.0.tar.gz
    URL_HASH MD5=5dbdf859f6e60e05813370e2b193b92b
    DOWNLOAD_NAME gsasl-1.8.0.tar.gz
    PREFIX ${CMAKE_CURRENT_BINARY_DIR}/${name}
    TMP_DIR ${BUILD_INFO_DIR}
    STAMP_DIR ${BUILD_INFO_DIR}
    DOWNLOAD_DIR ${DOWNLOAD_DIR}
    SOURCE_DIR ${source_dir}
    PATCH_COMMAND patch -p1 < ${CMAKE_SOURCE_DIR}/patches/libgsasl-1.8.0.patch
    CONFIGURE_COMMAND
        ${common_configure_envs}
        KRB5_CONFIG=${CMAKE_CURRENT_BINARY_DIR}/krb5-1.20.1/source/src/build-tools/krb5-config
        ./configure ${common_configure_args}
                    --disable-shared --enable-static --without-stringprep --disable-ntlm --with-gssapi-impl=mit
    BUILD_COMMAND make -s -j${BUILDING_JOBS_NUM}
    BUILD_IN_SOURCE 1
    INSTALL_COMMAND make -s install -j${BUILDING_JOBS_NUM}
    LOG_CONFIGURE TRUE
    LOG_BUILD TRUE
    LOG_INSTALL TRUE
    LOG_OUTPUT_ON_FAILURE TRUE
)
