/********************************************************************
 * 2014 -
 * open source under Apache License Version 2.0
 ********************************************************************/
/**
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
#ifndef _HDFS_LIBHDFS3_MOCK_MOCKLEASERENEWER_H_
#define _HDFS_LIBHDFS3_MOCK_MOCKLEASERENEWER_H_

#include "client/LeaseRenewer.h"
#include "gmock/gmock.h"

namespace Hdfs {
namespace Mock {

class MockLeaseRenewer : public Hdfs::Internal::LeaseRenewer {
   public:
    MOCK_METHOD1(StartRenew, void(shared_ptr<FileSystemInter>));
    MOCK_METHOD1(StopRenew, void(shared_ptr<FileSystemInter>));
};

static inline shared_ptr<LeaseRenewer> MakeMockLeaseRenewer() {
    Hdfs::Internal::LeaseRenewer::GetLeaseRenewer();
    shared_ptr<LeaseRenewer> old = Hdfs::Internal::LeaseRenewer::renewer;
    Hdfs::Internal::LeaseRenewer::renewer =
        shared_ptr<LeaseRenewer>(new MockLeaseRenewer);
    return old;
}

static inline MockLeaseRenewer& GetMockLeaseRenewer() {
    assert(Hdfs::Internal::LeaseRenewer::renewer);
    return static_cast<MockLeaseRenewer&>(
        *Hdfs::Internal::LeaseRenewer::renewer);
}

static inline void ResetMockLeaseRenewer(shared_ptr<LeaseRenewer> old) {
    Hdfs::Internal::LeaseRenewer::renewer = old;
}

}  // namespace Mock
}  // namespace Hdfs

#endif /* _HDFS_LIBHDFS3_MOCK_MOCKLEASERENEWER_H_ */
