#include <gtest/gtest.h>
#include <string.h>

#include "client/hdfs.h"

class ChmodFileTest : public testing::Test {
   public:
    ChmodFileTest() {
        hdfsBuilder* builder = hdfsNewBuilder();
        fs_ = hdfsBuilderConnect(builder);
        EXPECT_NE(fs_, nullptr);

        input_path_ = "/home/<USER>/cpp_client/2_file_used_by_ci";
        int exists = hdfsExists(fs_, input_path_.c_str());
        if (exists == 1) {
            int deleted = hdfsDelete(fs_, input_path_.c_str(), 0);
            if (deleted != 1) {
                input_path_ += std::to_string(2);
            }
        }

        hdfsFreeBuilder(builder);
    }

    ~ChmodFileTest() {
        int exists = hdfsExists(fs_, input_path_.c_str());
        if (exists == 1) {
            hdfsDelete(fs_, input_path_.c_str(), 0);
        }
        int res = hdfsDisconnect(fs_);
        EXPECT_EQ(res, 0);
    }

   protected:
    void SetUp() override {}

    void TearDown() override {}

    hdfsFS fs_{nullptr};
    hdfsFile hdfs_file_{nullptr};
    std::string input_path_;
};

TEST_F(ChmodFileTest, create_chmod) {
    short mode = 777;
    hdfsFile file =
        hdfsOpenFile(fs_, input_path_.c_str(), O_WRONLY | O_CREAT, 0644, 0, 0);
    if (file) {
        int res = hdfsChmod(fs_, input_path_.c_str(), mode);
        EXPECT_EQ(res, 0);
        hdfsCloseFile(fs_, file);
    }
}

int main(int argc, char** argv) {
    testing::InitGoogleTest(&argc, argv);
    return RUN_ALL_TESTS();
}
