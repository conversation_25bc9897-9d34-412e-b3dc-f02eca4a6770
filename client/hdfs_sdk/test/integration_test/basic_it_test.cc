#include <assert.h>
#include <gtest/gtest.h>
#include <string.h>

#include <chrono>
#include <future>
#include <iostream>
#include <thread>

#include "client/filesystem/FileSystem.h"
#include "client/hdfs.h"

// westeros 是中国区 BOE 入口
std::string nn = std::string("westeros");
const char* sfile_path = "/liuxintao/ufs_demo/small_file";
const char* big_file_path = "/liuxintao/ufs_demo/big_file";
const char* bfile_path = "/liuxintao/ufs_demo/small_coolfile";
const char* big_bfile_path = "/liuxintao/ufs_demo/big_coolfile";
const char* xattr_test_path = "/liuxintao/ufs_demo/xattr_test_file";
const char* test_close_path = "/liuxintao/ufs_demo/close_test_file";
const char* storage_policy_path = "/liuxintao/ufs_demo/storage_policy_file";
const int32_t BUF_SIZE = 4 * 1024;
// don't want to read too long, just test 4MB.
const int ExpectReadSize = 4 * 1024 * 1024;
const size_t PreadOnceSize = 4 * BUF_SIZE;

class HdfsBasicTest : public testing::Test {
   protected:
    void SetUp() override { setenv("CPP_HDFS_LOG_DIR", "/tmp/hdfs_ut_log", 1); }
    void TearDown() override {}
};

// test get/set xattr
TEST_F(HdfsBasicTest, Xattrs) {
    auto bld = hdfsNewBuilder();
    hdfsBuilderConfSetStr(bld, "dfs.client.cache.bytecool.ttl", "120");
    assert(bld);
    int ret;
    hdfsBuilderSetNameNode(bld, nn.data());
    auto fs = hdfsBuilderConnect(bld);
    if (!fs) {
        fprintf(stderr, "Failed to connect %s: %s\n", nn.c_str(),
                hdfsGetLastError());
        exit(-1);
    }

    int get_num = 0;
    auto xattrs = hdfsGetXAttrs(fs, xattr_test_path, &get_num);
    if (get_num != 1) {
        fprintf(stderr, "查看该文件是否正确被初始化设置, %d", get_num);
        exit(-1);
    }
    if (strcmp(xattrs->name, "trusted.init") != 0 ||
        strcmp(xattrs->value, "hello") != 0) {
        fprintf(stderr, "查看该文件是否正确被初始化设置, %s|%s", xattrs->name,
                xattrs->value);
    }
    hdfsFreeXAttrs(xattrs, get_num);

    ret = hdfsSetXAttr(fs, xattr_test_path, "user.to_be_removed", "hello", 1);
    if (ret != 0) {
        fprintf(stderr, "set xattr 失败");
        exit(-1);
    }

    xattrs = hdfsGetXAttr(fs, xattr_test_path, "user.to_be_removed");
    if (!xattrs) {
        fprintf(stderr, "Failed to get xattr user.to_be_removed: %s\n",
                hdfsGetLastError());
        exit(-1);
    }
    if (strcmp(xattrs->name, "user.to_be_removed") != 0 ||
        strcmp(xattrs->value, "hello") != 0) {
        fprintf(stderr, "set 结果确认失败, %s|%s", xattrs->name, xattrs->value);
    }
    hdfsFreeXAttrs(xattrs, 1);

    ret = hdfsRemoveXAttr(fs, xattr_test_path, "user.to_be_removed");
    if (ret != 0) {
        fprintf(stderr, "remove xattr 失败");
        exit(-1);
    }

    xattrs = hdfsGetXAttr(fs, xattr_test_path, "user.to_be_removed");
    if (xattrs != nullptr) {
        fprintf(stderr, "remove 结果确认失败");
    }
    hdfsFreeXAttrs(xattrs, 1);

    hdfsDisconnect(fs);
    hdfsFreeBuilder(bld);
    std::cout << "test_xattr finished\n";
}

// test stats parallel
TEST_F(HdfsBasicTest, StatNormalFile) {
    auto bld = hdfsNewBuilder();
    assert(bld);
    hdfsBuilderSetNameNode(bld, nn.data());
    auto fs = hdfsBuilderConnect(bld);
    if (!fs) {
        fprintf(stderr, "Failed to connect %s: %s\n", nn.c_str(),
                hdfsGetLastError());
        exit(-1);
    }

    std::thread threads[4];
    for (int i = 0; i < 4; i++) {
        threads[i] = std::thread([fs]() {
            for (int i = 0; i < 64; i++) {
                auto fileInfo = hdfsGetPathInfoEx(fs, sfile_path);
                if (!fileInfo) {
                    std::cout << "fileInfo get failed: " << hdfsGetLastError()
                              << std::endl;
                    exit(-1);
                }
                auto mName = std::string(fileInfo->mName);
                auto pos = mName.find("//");
                mName = mName.substr(pos + 2);
                pos = mName.find("/");
                mName = mName.substr(pos);
                if (strcmp(mName.c_str(), sfile_path) != 0) {
                    fprintf(stderr,
                            "sfile %s stat failed, you got file named %s\n",
                            sfile_path, mName.c_str());
                    exit(-1);
                }
                hdfsFreeFileInfoEx(fileInfo, 1);
            }
        });
    }

    std::cout << "Waiting 4 threads to be finished...\n";
    for (int i = 0; i < 4; i++) {
        threads[i].join();
    }

    hdfsDisconnect(fs);
    hdfsFreeBuilder(bld);
    std::cout << "stat_normal_file finished\n";
}

// test read 3 replica file
TEST_F(HdfsBasicTest, ReadNormalFile) {
    auto bld = hdfsNewBuilder();
    assert(bld);
    hdfsBuilderConfSetStr(bld, "dfs.client.conn.cache.ttl", "3500");
    hdfsBuilderSetNameNode(bld, nn.data());
    auto fs = hdfsBuilderConnect(bld);
    if (!fs) {
        fprintf(stderr, "Failed to connect %s: %s\n", nn.c_str(),
                hdfsGetLastError());
        exit(-1);
    }

    auto file = hdfsOpenFile(fs, sfile_path, O_RDONLY, 0, 0, 0);
    if (!file) {
        fprintf(stderr, "file %s open failed: %s\n", sfile_path,
                hdfsGetLastError());
        exit(-1);
    }

    auto file_info = hdfsGetPathInfo(fs, sfile_path);
    size_t file_size = file_info->mSize;

    int read_bytes = 0;
    int read_sum = 0;
    char buffer[BUF_SIZE];
    do {
        read_bytes = hdfsRead(fs, file, buffer, BUF_SIZE);
        if (read_bytes < 0) {
            fprintf(stderr, "file %s read failed, read %d bytes\n", sfile_path,
                    read_sum);
            exit(-1);
        }
        read_sum += read_bytes;
    } while (read_bytes > 0);
    if (read_sum <= 0 || file_size != read_sum) {
        fprintf(stderr, "file %s read failed, read %d bytes\n", sfile_path,
                read_sum);
        exit(-1);
    }

    hdfsFreeFileInfo(file_info, 1);
    int ret = hdfsCloseFile(fs, file);
    if (ret != 0) {
        fprintf(stderr, "file %s close failed\n", sfile_path);
        exit(-1);
    }
    hdfsDisconnect(fs);
    hdfsFreeBuilder(bld);
    std::cout << "read_normal_file finished\n";
}

// test read bytecool
TEST_F(HdfsBasicTest, ReadBytecoolFile) {
    auto bld = hdfsNewBuilder();
    assert(bld);
    hdfsBuilderConfSetStr(bld, "dfs.client.cache.bytecool.ttl", "120");
    hdfsBuilderSetNameNode(bld, nn.data());
    auto fs = hdfsBuilderConnect(bld);
    if (!fs) {
        fprintf(stderr, "Failed to connect %s: %s\n", nn.c_str(),
                hdfsGetLastError());
        exit(-1);
    }

    auto bfile = hdfsOpenFile(fs, bfile_path, O_RDONLY, 0, 0, 0);
    if (!bfile) {
        fprintf(stderr, "bfile %s open failed: %s\n", bfile_path,
                hdfsGetLastError());
        exit(-1);
    }

    auto file_info = hdfsGetPathInfo(fs, bfile_path);
    size_t file_size = file_info->mSize;

    int read_bytes = 0;
    int read_sum = 0;
    char buffer[BUF_SIZE];
    do {
        read_bytes = hdfsRead(fs, bfile, buffer, BUF_SIZE);
        if (read_bytes < 0) {
            fprintf(stderr, "bfile %s read failed, read %d bytes\n", bfile_path,
                    read_sum);
            exit(-1);
        }
        read_sum += read_bytes;
    } while (read_bytes > 0);
    if (read_sum <= 0 || file_size != read_sum) {
        fprintf(stderr, "bfile %s read failed, read %d bytes\n", bfile_path,
                read_sum);
        exit(-1);
    }

    hdfsFreeFileInfo(file_info, 1);
    int ret = hdfsCloseFile(fs, bfile);
    if (ret != 0) {
        fprintf(stderr, "bfile %s close failed\n", bfile_path);
        exit(-1);
    }
    hdfsDisconnect(fs);
    hdfsFreeBuilder(bld);
    std::cout << "read_bytecool_file finished\n";
}

TEST_F(HdfsBasicTest, PreadNormalFile) {
    auto bld = hdfsNewBuilder();
    assert(bld);
    hdfsBuilderSetNameNode(bld, nn.data());
    hdfsBuilderConfSetStr(bld, "dfs.client.conn.cache.ttl", "4000");
    auto fs = hdfsBuilderConnect(bld);
    if (!fs) {
        fprintf(stderr, "Failed to connect %s: %s\n", nn.c_str(),
                hdfsGetLastError());
        exit(-1);
    }

    auto file = hdfsOpenFile(fs, big_file_path, O_RDONLY, 0, 0, 0);
    if (!file) {
        fprintf(stderr, "file %s open failed: %s\n", big_file_path,
                hdfsGetLastError());
        exit(-1);
    }

    int read_bytes = 0;
    char buffer[PreadOnceSize];
    int pread_pos = 0;
    do {
        read_bytes = hdfsPread(fs, file, pread_pos, buffer, PreadOnceSize);
        if (read_bytes < 0) {
            fprintf(stderr, "file %s read failed, pread pos %d bytes\n",
                    big_file_path, pread_pos);
            exit(-1);
        } else if (read_bytes == 0) {
            fprintf(stderr, "file %s read EOF\n", big_file_path);
        } else {
            fprintf(stderr, "*");
        }
        pread_pos += read_bytes;
        if (pread_pos >= ExpectReadSize) {
            break;
        }
    } while (read_bytes > 0);
    fprintf(stderr, "\n");
    if (pread_pos < ExpectReadSize) {
        fprintf(stderr, "file %s read too short %d %d\n", big_file_path,
                pread_pos, ExpectReadSize);
        exit(-1);
    }

    int ret = hdfsCloseFile(fs, file);
    if (ret != 0) {
        fprintf(stderr, "file %s close failed\n", big_file_path);
        exit(-1);
    }
    hdfsDisconnect(fs);
    hdfsFreeBuilder(bld);
    std::cout << "pread_normal_file finished\n";
}

struct PreadContextArgs {
    hdfsStatus status;
    int32_t read_len;
    std::unique_ptr<std::promise<bool>> sender;
};

void ReadCallbackForTest(hdfsStatus status, int32_t read_length, char* buffer,
                         void* args) {
    std::cout << "[test] ReadCallbackForTest called" << std::endl;
    PreadContextArgs* pread_args = static_cast<PreadContextArgs*>(args);
    pread_args->status = status;
    pread_args->read_len = read_length;
    pread_args->sender->set_value(true);
}

TEST_F(HdfsBasicTest, AsyncPreadNormalFile) {
    auto bld = hdfsNewBuilder();
    assert(bld);
    hdfsBuilderSetNameNode(bld, nn.data());
    auto fs = hdfsBuilderConnect(bld);
    if (!fs) {
        fprintf(stderr, "Failed to connect %s: %s\n", nn.c_str(),
                hdfsGetLastError());
        exit(-1);
    }

    auto file = hdfsOpenFile(fs, big_file_path, O_RDONLY, 0, 0, 0);
    if (!file) {
        fprintf(stderr, "file %s open failed: %s\n", big_file_path,
                hdfsGetLastError());
        exit(-1);
    }

    auto args = new PreadContextArgs{
        .read_len = 0, .sender = std::make_unique<std::promise<bool>>()};
    auto fut = args->sender->get_future();
    hdfs_io_context hdfsContext;
    hdfsContext.read_callback = ReadCallbackForTest;
    hdfsContext.buffer = new char[260];
    hdfsContext.args = (void*)args;

    hdfsStatus hdfs_st = hdfsAsyncPRead(fs, file, 260, 512, hdfsContext);
    ASSERT_EQ(hdfs_st, hdfsStatus::STATUS_OK);
    ASSERT_TRUE(fut.get());
    ASSERT_EQ(args->status, hdfsStatus::STATUS_OK);
    ASSERT_EQ(args->read_len, 260);
    delete[] hdfsContext.buffer;
    hdfsContext.buffer = nullptr;
    delete args;

    int ret = hdfsCloseFile(fs, file);
    if (ret != 0) {
        fprintf(stderr, "file %s close failed\n", big_file_path);
        exit(-1);
    }
    hdfsDisconnect(fs);
    hdfsFreeBuilder(bld);
}

TEST_F(HdfsBasicTest, PreadBytecoolFile) {
    auto bld = hdfsNewBuilder();
    assert(bld);
    hdfsBuilderSetNameNode(bld, nn.data());
    auto fs = hdfsBuilderConnect(bld);
    if (!fs) {
        fprintf(stderr, "Failed to connect %s: %s\n", nn.c_str(),
                hdfsGetLastError());
        exit(-1);
    }

    auto file = hdfsOpenFile(fs, big_bfile_path, O_RDONLY, 0, 0, 0);
    if (!file) {
        fprintf(stderr, "file %s open failed: %s\n", big_bfile_path,
                hdfsGetLastError());
        exit(-1);
    }

    int read_bytes = 0;
    char buffer[PreadOnceSize];
    int pread_pos = 0;
    do {
        read_bytes = hdfsPread(fs, file, pread_pos, buffer, PreadOnceSize);
        if (read_bytes < 0) {
            fprintf(stderr, "file %s read failed, pread pos %d bytes\n",
                    big_bfile_path, pread_pos);
            exit(-1);
        } else if (read_bytes == 0) {
            fprintf(stderr, "file %s read EOF\n", big_bfile_path);
        } else {
            fprintf(stderr, "*");
        }
        pread_pos += read_bytes;
        if (pread_pos >= ExpectReadSize) {
            break;
        }
    } while (read_bytes > 0);
    fprintf(stderr, "\n");
    if (pread_pos < ExpectReadSize) {
        fprintf(stderr, "file %s read too short %d %d\n", big_bfile_path,
                pread_pos, ExpectReadSize);
        exit(-1);
    }

    int ret = hdfsCloseFile(fs, file);
    if (ret != 0) {
        fprintf(stderr, "file %s close failed\n", big_bfile_path);
        exit(-1);
    }
    hdfsDisconnect(fs);
    hdfsFreeBuilder(bld);
    std::cout << "pread_bytecool_file finished\n";
}

TEST_F(HdfsBasicTest, CheckClosedAfterOpen) {
    auto bld = hdfsNewBuilder();
    assert(bld);
    hdfsBuilderSetNameNode(bld, nn.data());
    auto fs = hdfsBuilderConnect(bld);
    if (!fs) {
        fprintf(stderr, "Failed to connect %s: %s\n", nn.c_str(),
                hdfsGetLastError());
        exit(-1);
    }

    hdfsDelete(fs, test_close_path, 0);

    auto file = hdfsOpenFile(fs, test_close_path, O_WRONLY | O_CREAT, 0, 0, 0);
    if (!file) {
        fprintf(stderr, "file %s open failed: %s\n", test_close_path,
                hdfsGetLastError());
        exit(-1);
    }

    auto filesystem =
        static_cast<Hdfs::FileSystem*>(hdfsGetFileSystemInstance(fs));
    auto closed = filesystem->isFileClosed(test_close_path);
    ASSERT_EQ(closed, false);

    int ret = hdfsCloseFile(fs, file);
    if (ret != 0) {
        fprintf(stderr, "file %s close failed\n", test_close_path);
        exit(-1);
    }

    closed = filesystem->isFileClosed(test_close_path);
    ASSERT_EQ(closed, true);

    hdfsDisconnect(fs);
    hdfsFreeBuilder(bld);
}

TEST_F(HdfsBasicTest, StoragePolicy) {
    auto bld = hdfsNewBuilder();
    assert(bld);
    hdfsBuilderSetNameNode(bld, nn.data());
    auto fs = hdfsBuilderConnect(bld);
    ASSERT_TRUE(fs) << "Failed to connect " << nn;

    // Create empty file as test target.
    hdfsDelete(fs, storage_policy_path, 0);
    auto file =
        hdfsOpenFile(fs, storage_policy_path, O_WRONLY | O_CREAT, 0, 0, 0);
    ASSERT_TRUE(file) << "Failed to open " << storage_policy_path;
    hdfsCloseFile(fs, file);

    // Test invalid argument case.
    auto ret = hdfsSetStoragePolicy(fs, "", "HOT");
    EXPECT_EQ(-1, ret);
    ret = hdfsSetStoragePolicy(fs, storage_policy_path, "");
    EXPECT_EQ(-1, ret);

    // Test set 'HOT' storage policy.
    ret = hdfsSetStoragePolicy(fs, storage_policy_path, "HOT");
    ASSERT_EQ(0, ret) << "Failed to set storage policy for "
                      << storage_policy_path
                      << " error: " << hdfsGetLastError();
    // Test get storage policy from file.
    auto fileInfo = hdfsGetPathInfoEx(fs, storage_policy_path);
    ASSERT_TRUE(fileInfo) << "Failed to get file info for "
                          << storage_policy_path
                          << " error: " << hdfsGetLastError();
    EXPECT_EQ(fileInfo->mStoragePolicyId, Hdfs::kHotStoragePolicy);
    hdfsFreeFileInfoEx(fileInfo, 1);
    // Test set 'WARM' storage policy.
    ret = hdfsSetStoragePolicy(fs, storage_policy_path, "WARM");
    ASSERT_EQ(0, ret) << "Failed to set storage policy for "
                      << storage_policy_path
                      << " error: " << hdfsGetLastError();
    // Test get storage policy from file.
    fileInfo = hdfsGetPathInfoEx(fs, storage_policy_path);
    ASSERT_TRUE(fileInfo) << "Failed to get file info for "
                          << storage_policy_path
                          << " error: " << hdfsGetLastError();
    EXPECT_EQ(fileInfo->mStoragePolicyId, Hdfs::kWarmStoragePolicy);
    hdfsFreeFileInfoEx(fileInfo, 1);
    // Test set 'COLD' storage policy.
    ret = hdfsSetStoragePolicy(fs, storage_policy_path, "COLD");
    ASSERT_EQ(0, ret) << "Failed to set storage policy for "
                      << storage_policy_path
                      << " error: " << hdfsGetLastError();
    // Test get storage policy from file.
    fileInfo = hdfsGetPathInfoEx(fs, storage_policy_path);
    ASSERT_TRUE(fileInfo) << "Failed to get file info for "
                          << storage_policy_path
                          << " error: " << hdfsGetLastError();
    EXPECT_EQ(fileInfo->mStoragePolicyId, Hdfs::kColdStoragePolicy);
    hdfsFreeFileInfoEx(fileInfo, 1);
    // Test set storage policy on not-exist file.
    auto storage_policy_path_notexist =
        std::string(storage_policy_path) + "_notexist";
    ret = hdfsSetStoragePolicy(fs, storage_policy_path_notexist.c_str(), "HOT");
    EXPECT_EQ(-1, ret);
    // Close filesystem manully to inject error.
    auto filesystem =
        static_cast<Hdfs::FileSystem*>(hdfsGetFileSystemInstance(fs));
    filesystem->disconnect();
    ret = hdfsSetStoragePolicy(fs, storage_policy_path, "COLD");
    EXPECT_EQ(-1, ret);

    hdfsDisconnect(fs);
    hdfsFreeBuilder(bld);
}

TEST_F(HdfsBasicTest, getStoragePolicies) {
    auto bld = hdfsNewBuilder();
    assert(bld);
    hdfsBuilderSetNameNode(bld, nn.data());
    auto fs = hdfsBuilderConnect(bld);
    hdfsFreeBuilder(bld);
    ASSERT_TRUE(fs) << "Failed to connect " << nn;

    int numStoragePolicies = 0;
    auto policies = hdfsGetStoragePolicies(fs, &numStoragePolicies);
    ASSERT_TRUE(policies) << "Failed to get storage policies"
                          << " error: " << hdfsGetLastError();
    ASSERT_GT(numStoragePolicies, 0) << "No storage policies found";
    hdfsFreeStoragePolicies(policies, numStoragePolicies);
    hdfsFreeStoragePolicies(NULL, 0);  // Won't abort.
    // Close filesystem manully to inject error.
    auto filesystem =
        static_cast<Hdfs::FileSystem*>(hdfsGetFileSystemInstance(fs));
    filesystem->disconnect();
    policies = hdfsGetStoragePolicies(fs, &numStoragePolicies);
    EXPECT_EQ(NULL, policies);
    hdfsDisconnect(fs);
}

int main(int argc, char** argv) {
    testing::InitGoogleTest(&argc, argv);
    return RUN_ALL_TESTS();
}
