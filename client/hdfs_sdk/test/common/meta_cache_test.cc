#include <glog/logging.h>
#include <gtest/gtest.h>

#include <iostream>
#include <memory>
#include <random>
#include <string>
#include <thread>
#include <vector>

#include "cache/MetaCache.h"
#include "common/HdfsEnv.h"
#include "common/LruCache.h"

namespace Hdfs {
namespace Internal {

class MetaCacheTest : public testing::Test {
   protected:
    void SetUp() override {}
    void TearDown() override {}
};

int64_t GenerateRandomId() {
    std::random_device rd;
    std::mt19937_64 engine(rd());
    std::uniform_int_distribution<int64_t> distribution(
        std::numeric_limits<int64_t>::min(),
        std::numeric_limits<int64_t>::max());
    int64_t randomValue = distribution(engine);
    return randomValue;
}

// create first 2 blocks, block max size = 1000. file_len: (1000,2000)
std::shared_ptr<LocatedBlocks> CreateFirst2FullBlocks(
    const std::string& backend, size_t file_len, int64_t blk_id) {
    auto new_blk = std::make_shared<LocatedBlocksImpl>();
    new_blk->setFileLength(file_len);
    new_blk->setIsLastBlockComplete(true);
    new_blk->setNamenodebackend(backend);
    new_blk->setUnderConstruction(false);

    auto blk0 = std::make_shared<LocatedBlock>(0);
    blk0->setNumBytes(1000);
    blk0->setBlockId(blk_id);
    auto blk1 = std::make_shared<LocatedBlock>(1000);
    blk1->setNumBytes(file_len - 1000);
    blk1->setBlockId(blk_id);
    new_blk->setLastBlock(blk1);
    new_blk->getBlocks().push_back(*blk0);
    new_blk->getBlocks().push_back(*blk1);
    return new_blk;
}

// create mid block, block max size = 1000. file_len: (2000,3000)
std::shared_ptr<LocatedBlocks> CreateMidFullBlock(const std::string& backend,
                                                  size_t file_len,
                                                  int64_t blk_id) {
    auto new_blk = std::make_shared<LocatedBlocksImpl>();
    new_blk->setFileLength(file_len);
    new_blk->setIsLastBlockComplete(true);
    new_blk->setNamenodebackend(backend);
    new_blk->setUnderConstruction(false);

    auto blk0 = std::make_shared<LocatedBlock>(1000);
    blk0->setNumBytes(1000);
    blk0->setBlockId(blk_id);
    new_blk->getBlocks().push_back(*blk0);
    return new_blk;
}

// create last block, block max size = 1000. file_len: (2000,3000)
std::shared_ptr<LocatedBlocks> CreateLastBlock(const std::string& backend,
                                               size_t file_len,
                                               int64_t blk_id) {
    auto new_blk = std::make_shared<LocatedBlocksImpl>();
    new_blk->setFileLength(file_len);
    new_blk->setIsLastBlockComplete(true);
    new_blk->setNamenodebackend(backend);
    new_blk->setUnderConstruction(false);

    auto blk0 = std::make_shared<LocatedBlock>(2000);
    blk0->setNumBytes(file_len - 2000);
    blk0->setBlockId(blk_id);
    new_blk->setLastBlock(blk0);
    new_blk->getBlocks().push_back(*blk0);

    return new_blk;
}

// create only 1 head block, block max size = 1000.
std::shared_ptr<LocatedBlocks> CreateFirst1FullBlock(const std::string& backend,
                                                     size_t file_len,
                                                     int64_t blk_id) {
    auto new_blk = std::make_shared<LocatedBlocksImpl>();
    new_blk->setFileLength(file_len);
    new_blk->setIsLastBlockComplete(true);
    new_blk->setNamenodebackend(backend);
    new_blk->setUnderConstruction(false);

    auto blk0 = std::make_shared<LocatedBlock>(0);
    blk0->setNumBytes(1000);
    blk0->setBlockId(blk_id);
    new_blk->getBlocks().push_back(*blk0);
    return new_blk;
}

TEST_F(MetaCacheTest, Basic) {
    HdfsEnv::Get()->GetMetaCache()->RemoveBlockLocations("/test_path");

    auto lbs_opt =
        HdfsEnv::Get()->GetMetaCache()->GetBlockLocations("/test_path", 0, 1);
    ASSERT_TRUE(!lbs_opt);
    auto blk_id1 = GenerateRandomId();
    auto blk_id2 = GenerateRandomId();

    auto blks = CreateFirst2FullBlocks("jjbackend", 1100, blk_id1);
    HdfsEnv::Get()->GetMetaCache()->SetBlockLocations("/test_path", blks);

    // should hit the 1st block.
    lbs_opt =
        HdfsEnv::Get()->GetMetaCache()->GetBlockLocations("/test_path", 0, 1);
    ASSERT_TRUE(lbs_opt);
    ASSERT_TRUE(lbs_opt->get() != nullptr);
    EXPECT_EQ(lbs_opt->get()->getFileLength(), 1100);
    EXPECT_EQ(lbs_opt->get()->getBlocks().size(), 2);
    EXPECT_EQ(lbs_opt->get()->getBlocks()[1].getNumBytes(), 100);

    // should hit the 2nd block.
    lbs_opt = HdfsEnv::Get()->GetMetaCache()->GetBlockLocations("/test_path",
                                                                1001, 1);
    ASSERT_TRUE(lbs_opt);
    ASSERT_TRUE(lbs_opt->get() != nullptr);
    EXPECT_EQ(lbs_opt->get()->getFileLength(), 1100);
    EXPECT_EQ(lbs_opt->get()->getBlocks().size(), 2);
    EXPECT_EQ(lbs_opt->get()->getBlocks()[1].getNumBytes(), 100);

    // should not hit.
    lbs_opt = HdfsEnv::Get()->GetMetaCache()->GetBlockLocations("/test_path",
                                                                1200, 1);
    ASSERT_TRUE(!lbs_opt);

    // Create new blocks and has more data.
    blks = CreateFirst2FullBlocks("jjbackend", 1500, blk_id1);
    HdfsEnv::Get()->GetMetaCache()->SetBlockLocations("/test_path", blks);

    // Newer file larger so updated to 1500 length file info.
    lbs_opt = HdfsEnv::Get()->GetMetaCache()->GetBlockLocations("/test_path",
                                                                1200, 1);
    ASSERT_TRUE(lbs_opt);
    ASSERT_TRUE(lbs_opt->get() != nullptr);
    EXPECT_EQ(lbs_opt->get()->getFileLength(), 1500);
    EXPECT_EQ(lbs_opt->get()->getBlocks().size(), 2);
    EXPECT_EQ(lbs_opt->get()->getBlocks()[1].getNumBytes(), 500);

    // re-insert cache.
    blks = CreateFirst2FullBlocks("jjbackend", 1800, blk_id1);
    HdfsEnv::Get()->GetMetaCache()->SetBlockLocations("/test_path", blks);
    lbs_opt =
        HdfsEnv::Get()->GetMetaCache()->GetBlockLocations("/test_path", 0, 1);
    ASSERT_TRUE(lbs_opt);

    // Update with smaller length so cache invalid.
    blks = CreateFirst2FullBlocks("jjbackend", 1700, blk_id1);
    HdfsEnv::Get()->GetMetaCache()->SetBlockLocations("/test_path", blks);
    lbs_opt =
        HdfsEnv::Get()->GetMetaCache()->GetBlockLocations("/test_path", 0, 1);
    ASSERT_TRUE(!lbs_opt);

    // re-insert cache.
    blks = CreateFirst2FullBlocks("jjbackend", 1150, blk_id1);
    HdfsEnv::Get()->GetMetaCache()->SetBlockLocations("/test_path", blks);
    lbs_opt =
        HdfsEnv::Get()->GetMetaCache()->GetBlockLocations("/test_path", 0, 1);
    ASSERT_TRUE(lbs_opt);

    // Inconsistent backend will disable cache.
    blks = CreateFirst2FullBlocks("xxbackend", 1150, blk_id1);
    HdfsEnv::Get()->GetMetaCache()->SetBlockLocations("/test_path", blks);
    lbs_opt =
        HdfsEnv::Get()->GetMetaCache()->GetBlockLocations("/test_path", 0, 1);
    ASSERT_TRUE(!lbs_opt);

    // re-insert cache.
    blks = CreateFirst2FullBlocks("jjbackend", 1050, blk_id1);
    HdfsEnv::Get()->GetMetaCache()->SetBlockLocations("/test_path", blks);
    lbs_opt =
        HdfsEnv::Get()->GetMetaCache()->GetBlockLocations("/test_path", 0, 1);
    ASSERT_TRUE(lbs_opt);

    // Last block Id mismatch so invalid.
    blks = CreateFirst2FullBlocks("jjbackend", 1050, blk_id2);
    HdfsEnv::Get()->GetMetaCache()->SetBlockLocations("/test_path", blks);
    lbs_opt =
        HdfsEnv::Get()->GetMetaCache()->GetBlockLocations("/test_path", 0, 1);
    ASSERT_TRUE(!lbs_opt);
}

TEST_F(MetaCacheTest, MidSingle) {
    HdfsEnv::Get()->GetMetaCache()->RemoveBlockLocations("/test_path");

    std::shared_ptr<Hdfs::Internal::LocatedBlocks> blks;
    auto blk_id1 = GenerateRandomId();

    blks = CreateMidFullBlock("jjbackend", 1800, blk_id1);
    HdfsEnv::Get()->GetMetaCache()->SetBlockLocations("/test_path", blks);

    // test only 1 mid block. [1000,1800) is valid.
    auto lbs_opt =
        HdfsEnv::Get()->GetMetaCache()->GetBlockLocations("/test_path", 999, 1);
    ASSERT_TRUE(!lbs_opt);
    lbs_opt = HdfsEnv::Get()->GetMetaCache()->GetBlockLocations("/test_path",
                                                                1000, 1);
    ASSERT_TRUE(lbs_opt);
    lbs_opt = HdfsEnv::Get()->GetMetaCache()->GetBlockLocations("/test_path",
                                                                1799, 1);
    ASSERT_TRUE(lbs_opt);
    lbs_opt = HdfsEnv::Get()->GetMetaCache()->GetBlockLocations("/test_path",
                                                                1800, 1);
    ASSERT_TRUE(!lbs_opt);

    // add 2 blocks so [0,1900) is valid.
    blks = CreateFirst2FullBlocks("jjbackend", 1900, blk_id1);
    HdfsEnv::Get()->GetMetaCache()->SetBlockLocations("/test_path", blks);
    lbs_opt =
        HdfsEnv::Get()->GetMetaCache()->GetBlockLocations("/test_path", 0, 1);
    ASSERT_TRUE(lbs_opt);
    lbs_opt =
        HdfsEnv::Get()->GetMetaCache()->GetBlockLocations("/test_path", 999, 1);
    ASSERT_TRUE(lbs_opt);
    lbs_opt = HdfsEnv::Get()->GetMetaCache()->GetBlockLocations("/test_path",
                                                                1899, 1);
    ASSERT_TRUE(lbs_opt);
    lbs_opt = HdfsEnv::Get()->GetMetaCache()->GetBlockLocations("/test_path",
                                                                1900, 1);
    ASSERT_TRUE(!lbs_opt);
    lbs_opt = HdfsEnv::Get()->GetMetaCache()->GetBlockLocations("/test_path",
                                                                2000, 1);
    ASSERT_TRUE(!lbs_opt);
}

TEST_F(MetaCacheTest, CheckRange) {
    HdfsEnv::Get()->GetMetaCache()->RemoveBlockLocations("/test_path");

    std::shared_ptr<Hdfs::Internal::LocatedBlocks> blks;
    auto blk_id1 = GenerateRandomId();
    auto blk_id2 = GenerateRandomId();

    blks = CreateLastBlock("jjbackend", 2345, blk_id1);
    HdfsEnv::Get()->GetMetaCache()->SetBlockLocations("/test_path", blks);

    // only update last block [2000,2345)
    auto lbs_opt = HdfsEnv::Get()->GetMetaCache()->GetBlockLocations(
        "/test_path", 1999, 1);
    ASSERT_TRUE(!lbs_opt);
    lbs_opt = HdfsEnv::Get()->GetMetaCache()->GetBlockLocations("/test_path",
                                                                2000, 1);
    ASSERT_TRUE(lbs_opt);
    lbs_opt = HdfsEnv::Get()->GetMetaCache()->GetBlockLocations("/test_path",
                                                                2000, 345);
    ASSERT_TRUE(lbs_opt);
    lbs_opt = HdfsEnv::Get()->GetMetaCache()->GetBlockLocations("/test_path",
                                                                2000, 9999);
    ASSERT_TRUE(lbs_opt);
    ASSERT_TRUE(lbs_opt->get() != nullptr);
    EXPECT_EQ(lbs_opt->get()->getFileLength(), 2345);
    EXPECT_EQ(lbs_opt->get()->getBlocks().size(), 1);
    EXPECT_EQ(lbs_opt->get()->getBlocks()[0].getOffset(), 2000);
    EXPECT_EQ(lbs_opt->get()->getBlocks()[0].getNumBytes(), 345);
    EXPECT_EQ(lbs_opt->get()->getBlocks()[0].getBlockId(), blk_id1);

    // after this, we have 2 blocks [0,1000) and [1000,2345) in cache.
    blks = CreateFirst1FullBlock("jjbackend", 2345, blk_id2);
    HdfsEnv::Get()->GetMetaCache()->SetBlockLocations("/test_path", blks);
    lbs_opt =
        HdfsEnv::Get()->GetMetaCache()->GetBlockLocations("/test_path", 0, 1);
    ASSERT_TRUE(lbs_opt);
    ASSERT_TRUE(lbs_opt->get() != nullptr);
    EXPECT_EQ(lbs_opt->get()->getFileLength(), 2345);
    EXPECT_EQ(lbs_opt->get()->getBlocks().size(), 2);
    EXPECT_EQ(lbs_opt->get()->getBlocks()[0].getOffset(), 0);
    EXPECT_EQ(lbs_opt->get()->getBlocks()[0].getNumBytes(), 1000);
    EXPECT_EQ(lbs_opt->get()->getBlocks()[0].getBlockId(), blk_id2);
    EXPECT_EQ(lbs_opt->get()->getBlocks()[1].getOffset(), 2000);
    EXPECT_EQ(lbs_opt->get()->getBlocks()[1].getNumBytes(), 345);
    EXPECT_EQ(lbs_opt->get()->getBlocks()[1].getBlockId(), blk_id1);

    lbs_opt =
        HdfsEnv::Get()->GetMetaCache()->GetBlockLocations("/test_path", 0, 0);
    ASSERT_TRUE(!lbs_opt);
    lbs_opt =
        HdfsEnv::Get()->GetMetaCache()->GetBlockLocations("/test_path", 0, 1);
    ASSERT_TRUE(lbs_opt);
    lbs_opt =
        HdfsEnv::Get()->GetMetaCache()->GetBlockLocations("/test_path", 999, 1);
    ASSERT_TRUE(lbs_opt);
    lbs_opt =
        HdfsEnv::Get()->GetMetaCache()->GetBlockLocations("/test_path", 999, 2);
    ASSERT_TRUE(!lbs_opt);
    lbs_opt = HdfsEnv::Get()->GetMetaCache()->GetBlockLocations("/test_path", 0,
                                                                1500);
    ASSERT_TRUE(!lbs_opt);
    lbs_opt = HdfsEnv::Get()->GetMetaCache()->GetBlockLocations("/test_path",
                                                                1100, 600);
    ASSERT_TRUE(!lbs_opt);
    lbs_opt = HdfsEnv::Get()->GetMetaCache()->GetBlockLocations("/test_path",
                                                                1100, 1000);
    ASSERT_TRUE(!lbs_opt);
    lbs_opt = HdfsEnv::Get()->GetMetaCache()->GetBlockLocations("/test_path", 0,
                                                                2100);
    ASSERT_TRUE(!lbs_opt);
    lbs_opt = HdfsEnv::Get()->GetMetaCache()->GetBlockLocations("/test_path",
                                                                2345, 1);
    ASSERT_TRUE(!lbs_opt);
    lbs_opt = HdfsEnv::Get()->GetMetaCache()->GetBlockLocations("/test_path",
                                                                2000, 1);
    ASSERT_TRUE(lbs_opt);
}

}  // namespace Internal
}  // namespace Hdfs

int main(int argc, char** argv) {
    testing::InitGoogleTest(&argc, argv);
    return RUN_ALL_TESTS();
}
