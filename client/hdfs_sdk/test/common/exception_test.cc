#include "common/Exception.h"

#include <unistd.h>

#include "gmock/gmock.h"
#include "gtest/gtest.h"

using namespace testing;

namespace Hdfs {
namespace Internal {

class TestException : public ::testing::Test {
   public:
    virtual void SetUp() {}

    virtual void TearDown() {}
};

std::string file = "file";
std::string stack = "stack";

TEST(TestException, test_common) {
    {
        HdfsException e("test", file.c_str(), 0, stack.c_str());
        EXPECT_EQ(std::string(e.ReflexName), "java.lang.Exception");
    }

    {
        HdfsIOException e("test", file.c_str(), 0, stack.c_str());
        EXPECT_EQ(std::string(e.ReflexName), "java.io.IOException");
    }

    {
        MissingBlockException e("test", file.c_str(), 0, stack.c_str());
        EXPECT_EQ(std::string(e.ReflexName),
                  "org.apache.hadoop.hdfs.BlockMissingException");
    }

    {
        HdfsSlowReadIOException e("test", file.c_str(), 0, stack.c_str());
        EXPECT_EQ(std::string(e.ReflexName),
                  "org.apache.hadoop.hdfs.SlowReadIOException");
    }

    {
        HdfsNetworkException e("test", file.c_str(), 0, stack.c_str());
        EXPECT_EQ(std::string(e.ReflexName), "java.io.Exception");
    }

    {
        AccessControlException e("test", file.c_str(), 0, stack.c_str());
        EXPECT_EQ(std::string(e.ReflexName),
                  "org.apache.hadoop.security.AccessControlException");
    }

    {
        AlreadyBeingCreatedException e("test", file.c_str(), 0, stack.c_str());
        EXPECT_EQ(
            std::string(e.ReflexName),
            "org.apache.hadoop.hdfs.protocol.AlreadyBeingCreatedException");
    }

    {
        ChecksumException e("test", file.c_str(), 0, stack.c_str());
        EXPECT_EQ(std::string(e.ReflexName),
                  "org.apache.hadoop.fs.ChecksumException");
    }

    {
        DSQuotaExceededException e("test", file.c_str(), 0, stack.c_str());
        EXPECT_EQ(std::string(e.ReflexName),
                  "org.apache.hadoop.hdfs.protocol.DSQuotaExceededException");
    }

    {
        FileAlreadyExistsException e("test", file.c_str(), 0, stack.c_str());
        EXPECT_EQ(std::string(e.ReflexName),
                  "org.apache.hadoop.fs.FileAlreadyExistsException");
    }

    {
        FileNotFoundException e("test", file.c_str(), 0, stack.c_str());
        EXPECT_EQ(std::string(e.ReflexName), "java.io.FileNotFoundException");
    }

    {
        PathIsNotEmptyDirectoryException e("test", file.c_str(), 0,
                                           stack.c_str());
        EXPECT_EQ(std::string(e.ReflexName),
                  "org.apache.hadoop.fs.PathIsNotEmptyDirectoryException");
    }

    {
        HdfsBadBoolFoumat e("test", file.c_str(), 0, stack.c_str());
        EXPECT_EQ(std::string(e.ReflexName),
                  "java.lang.IllegalArgumentException");
    }

    {
        HdfsBadConfigFoumat e("test", file.c_str(), 0, stack.c_str());
        EXPECT_EQ(std::string(e.ReflexName),
                  "javax.xml.parsers.ParserConfigurationException");
    }

    {
        HdfsBadNumFoumat e("test", file.c_str(), 0, stack.c_str());
        EXPECT_EQ(std::string(e.ReflexName),
                  "java.lang.IllegalArgumentException");
    }

    {
        HdfsCanceled e("test", file.c_str(), 0, stack.c_str());
        EXPECT_EQ(std::string(e.ReflexName), "java.io.InterruptedIOException");
    }

    {
        HdfsFileSystemClosed e("test", file.c_str(), 0, stack.c_str());
        EXPECT_EQ(std::string(e.ReflexName), "java.lang.Exception");
    }

    {
        HdfsFileClosed e("test", file.c_str(), 0, stack.c_str());
        EXPECT_EQ(std::string(e.ReflexName), "java.lang.Exception");
    }

    {
        HdfsConfigInvalid e("test", file.c_str(), 0, stack.c_str());
        EXPECT_EQ(std::string(e.ReflexName), "java.lang.Exception");
    }

    {
        HdfsConfigNotFound e("test", file.c_str(), 0, stack.c_str());
        EXPECT_EQ(std::string(e.ReflexName), "java.lang.Exception");
    }

    {
        HdfsEndOfStream e("test", file.c_str(), 0, stack.c_str());
        EXPECT_EQ(std::string(e.ReflexName), "java.io.EOFException");
    }

    {
        HdfsInvalidBlockToken e("test", file.c_str(), 0, stack.c_str());
        EXPECT_EQ(
            std::string(e.ReflexName),
            "org.apache.hadoop.security.token.SecretManager$InvalidToken");
    }

    {
        HdfsFailoverException e("test", file.c_str(), 0, stack.c_str());
        EXPECT_EQ(std::string(e.ReflexName),
                  "org.apache.hadoop.ha.FailoverFailedException");
    }

    {
        HdfsRpcException e("test", file.c_str(), 0, stack.c_str());
        EXPECT_EQ(std::string(e.ReflexName),
                  "org.apache.hadoop.ipc.RpcException");
    }

    {
        HdfsRpcServerException e("test", file.c_str(), 0, stack.c_str());
        EXPECT_EQ(std::string(e.ReflexName),
                  "org.apache.hadoop.ipc.RpcServerException");
    }

    {
        HdfsRpcServerException e("test", file.c_str(), 0, stack.c_str());
        EXPECT_EQ(std::string(e.ReflexName),
                  "org.apache.hadoop.ipc.RpcServerException");
    }

    {
        HdfsRemoteException e("test", file.c_str(), 0, stack.c_str());
        EXPECT_EQ(std::string(e.ReflexName),
                  "org.apache.hadoop.ipc.RemoteException");
    }

    {
        HdfsTimeoutException e("test", file.c_str(), 0, stack.c_str());
        EXPECT_EQ(std::string(e.ReflexName),
                  "java.util.concurrent.TimeoutException");
    }

    {
        InvalidParameter e("test", file.c_str(), 0, stack.c_str());
        EXPECT_EQ(std::string(e.ReflexName),
                  "java.lang.IllegalArgumentException");
    }

    {
        HadoopIllegalArgumentException e("test", file.c_str(), 0,
                                         stack.c_str());
        EXPECT_EQ(std::string(e.ReflexName),
                  "org.apache.hadoop.HadoopIllegalArgumentException");
    }

    {
        InvalidPath e("test", file.c_str(), 0, stack.c_str());
        EXPECT_EQ(std::string(e.ReflexName),
                  "org.apache.hadoop.fs.InvalidPathException");
    }

    {
        NotReplicatedYetException e("test", file.c_str(), 0, stack.c_str());
        EXPECT_EQ(
            std::string(e.ReflexName),
            "org.apache.hadoop.hdfs.server.namenode.NotReplicatedYetException");
    }

    {
        NSQuotaExceededException e("test", file.c_str(), 0, stack.c_str());
        EXPECT_EQ(std::string(e.ReflexName),
                  "org.apache.hadoop.hdfs.protocol.NSQuotaExceededException");
    }

    {
        ParentNotDirectoryException e("test", file.c_str(), 0, stack.c_str());
        EXPECT_EQ(std::string(e.ReflexName),
                  "org.apache.hadoop.fs.ParentNotDirectoryException");
    }

    {
        ReplicaNotFoundException e("test", file.c_str(), 0, stack.c_str());
        EXPECT_EQ(
            std::string(e.ReflexName),
            "org.apache.hadoop.hdfs.server.datanode.ReplicaNotFoundException");
    }

    {
        SafeModeException e("test", file.c_str(), 0, stack.c_str());
        EXPECT_EQ(std::string(e.ReflexName),
                  "org.apache.hadoop.hdfs.server.namenode.SafeModeException");
    }

    {
        UnresolvedLinkException e("test", file.c_str(), 0, stack.c_str());
        EXPECT_EQ(std::string(e.ReflexName),
                  "org.apache.hadoop.fs.UnresolvedLinkException");
    }

    {
        UnsupportedOperationException e("test", file.c_str(), 0, stack.c_str());
        EXPECT_EQ(std::string(e.ReflexName),
                  "java.lang.UnsupportedOperationException");
    }

    {
        SaslException e("test", file.c_str(), 0, stack.c_str());
        EXPECT_EQ(std::string(e.ReflexName),
                  "javax.security.sasl.SaslException");
    }

    {
        NamenodeThrottlerException e("test", file.c_str(), 0, stack.c_str());
        EXPECT_EQ(std::string(e.ReflexName),
                  "org.apache.hadoop.ipc.NamenodeThrottlerException");
    }

    {
        ThrottlerLowLimitException e("test", file.c_str(), 0, stack.c_str());
        EXPECT_EQ(std::string(e.ReflexName),
                  "org.apache.hadoop.ipc.ThrottlerLowLimitException");
    }

    {
        NameNodeStandbyException e("test", file.c_str(), 0, stack.c_str());
        EXPECT_EQ(std::string(e.ReflexName),
                  "org.apache.hadoop.ipc.StandbyException");
    }

    {
        RpcNoSuchMethodException e("test", file.c_str(), 0, stack.c_str());
        EXPECT_EQ(std::string(e.ReflexName),
                  "org.apache.hadoop.ipc.RpcNoSuchMethodException");
    }

    {
        RecoveryInProgressException e("test", file.c_str(), 0, stack.c_str());
        EXPECT_EQ(
            std::string(e.ReflexName),
            "org.apache.hadoop.hdfs.protocol.RecoveryInProgressException");
    }

    {
        LeaseExpiredException e("test", file.c_str(), 0, stack.c_str());
        EXPECT_EQ(
            std::string(e.ReflexName),
            "org.apache.hadoop.hdfs.server.namenode.LeaseExpiredException");
    }

    {
        ReadOnlyCoolFileException e("test", file.c_str(), 0, stack.c_str());
        EXPECT_EQ(
            std::string(e.ReflexName),
            "org.apache.hadoop.hdfs.server.namenode.ReadOnlyCoolFileException");
    }
    {
        ThirdPartyCloudStorageException e;
        EXPECT_EQ("", std::string(e.what()));
    }
}

}  // namespace Internal
}  // namespace Hdfs

int main(int argc, char** argv) {
    testing::InitGoogleTest(&argc, argv);
    return RUN_ALL_TESTS();
}
