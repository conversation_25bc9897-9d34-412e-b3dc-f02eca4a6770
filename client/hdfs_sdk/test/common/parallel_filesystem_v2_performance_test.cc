
#include <unistd.h>

#include <cstdlib>
#include <mutex>
#include <random>
#include <thread>

#include "client/Hdfs.cpp"
#include "client/filesystem/OutputStream.h"
#include "client/filesystem/ParallelInputStreamV2.h"
#include "client/filesystem/ParallelOutputStreamV2.h"
#include "client/hdfs.h"
#include "common/json.h"
#include "common/ut_common.h"
#include "gtest/gtest.h"

using namespace Hdfs::Internal;
using namespace testing;

namespace Hdfs {

namespace Internal {
class TestParallelFileSystem : public ::testing::Test {
   public:
    void SetUp() override {
        auto ftenv = HdfsFunctionTestEnvironment::Instance();
        ASSERT_TRUE(ftenv != nullptr);
        test_dir = ftenv->GetHdfsTestDir();
        ASSERT_TRUE(ftenv->EnsureHdfsTestDirReady(test_dir));
    }

    void TearDown() override {}

    void gen_files();
    void read_files(int index, int count);
    void readom_seek_read_files(int index, int count);

   protected:
    std::string test_dir;
};

const static int64_t ONE_MB = 1024 * 1024;
std::vector<int64_t> file_sizes{512,         618,         1024,
                                4 * 1024,    ONE_MB,      ONE_MB * 2,
                                ONE_MB * 10, ONE_MB * 20, ONE_MB * 512};
std::vector<int> stripe_sizes{128 * 1024, 256 * 1024, 512 * 1024, 1024 * 1024};

std::vector<std::string> files;
std::vector<std::string> datas;

void TestParallelFileSystem::gen_files() {
    hdfsBuilder* builder = hdfsNewBuilder();
    hdfsBuilderConfSetStr(
        builder, "dfs.client.parallel.input.performance.switch", "true");
    hdfsBuilderConfSetStr(
        builder, "dfs.client.parallel.output.performance.switch", "true");
    hdfsFS fs = hdfsBuilderConnect(builder);
    for (auto stripe_size : stripe_sizes) {
        for (auto file_size : file_sizes) {
            std::string file_path = test_dir + "s" +
                                    std::to_string(stripe_size) + "_f" +
                                    std::to_string(file_size);
            HDFSLOG(INFO, "start write file {}, size {}", file_path, file_size);
            hdfsFileInfo* info = hdfsGetPathInfo(fs, file_path.c_str());
            if (info) {
                hdfsFreeFileInfo(info, 1);
                info = nullptr;
                hdfsDelete(fs, file_path.c_str(), true);
            }

            CreateFileOption option = GetDefaultCreateFileOption();
            option.format = FileStorageFormat::FT_PARALLEL;
            option.stripe_unit_size = stripe_size;
            option.stripe_unit_count = 16;
            auto hFile =
                hdfsOpenFileV3(fs, file_path.c_str(),
                               O_WRONLY | O_CREAT | O_TRUNC, 0, 0, 0, option);
            std::string write_data_buf;
            write_data_buf.resize(file_size);
            std::random_device rd;
            std::mt19937 g(rd());
            for (size_t i = 0; i < file_size; ++i) {
                write_data_buf[i] = (g() % 128);
            }
            const char* buffer = write_data_buf.c_str();
            EXPECT_EQ(hdfsWrite(fs, hFile, buffer, file_size), file_size);
            hdfsCloseFile(fs, hFile);
            HDFSLOG(INFO, "end write file {}, size {}", file_path, file_size);

            info = hdfsGetPathInfo(fs, file_path.c_str());

            EXPECT_EQ(info->mSize, file_size);
            hdfsFreeFileInfo(info, 1);
            info = nullptr;

            files.emplace_back(file_path);
            datas.emplace_back(std::move(write_data_buf));
        }
    }
    hdfsDisconnect(fs);
    hdfsFreeBuilder(builder);
}

void TestParallelFileSystem::read_files(int index, int count) {
    hdfsBuilder* builder = hdfsNewBuilder();
    hdfsBuilderConfSetStr(
        builder, "dfs.client.parallel.input.performance.switch", "true");
    hdfsBuilderConfSetStr(
        builder, "dfs.client.parallel.output.performance.switch", "true");
    hdfsFS fs = hdfsBuilderConnect(builder);

    for (int i = 0; i < files.size(); ++i) {
        if ((i % count) != index) {
            continue;
        }

        auto hFile = hdfsOpenFile(fs, files[i].c_str(), O_RDONLY, 0, 0, 0);
        HDFSLOG(INFO, "start read file {}, size {}", files[i], datas[i].size());
        std::string read_data;
        char buffer[1024 * 1024] = {0};
        int read_size = 0;
        do {
            read_size = hdfsRead(fs, hFile, buffer, 1024 * 1024);
            if (read_size > 0) {
                read_data.append(std::string(buffer, read_size));
            }
        } while (read_size > 0);
        EXPECT_TRUE(datas[i] == read_data);
        hdfsCloseFile(fs, hFile);
        HDFSLOG(INFO, "end read file {}, size {}", files[i], datas[i].size());
    }

    hdfsDisconnect(fs);
    hdfsFreeBuilder(builder);
}

void TestParallelFileSystem::readom_seek_read_files(int index, int count) {
    hdfsBuilder* builder = hdfsNewBuilder();
    std::mutex lock;
    hdfsBuilderConfSetStr(
        builder, "dfs.client.parallel.input.performance.switch", "true");
    hdfsBuilderConfSetStr(
        builder, "dfs.client.parallel.output.performance.switch", "true");
    hdfsFS fs = hdfsBuilderConnect(builder);

    for (int i = 0; i < files.size(); ++i) {
        if ((i % count) != index) {
            continue;
        }
        auto hFile = hdfsOpenFile(fs, files[i].c_str(), O_RDONLY, 0, 0, 0);
        HDFSLOG(INFO, "start random seek read file {}, size {}", files[i],
                datas[i].size());
        int file_size = datas[i].size();

        char* buffer = new char[ONE_MB + 1];

        char* pread_buffer = new char[ONE_MB + 1];

        auto pread_fun = [&](int64_t pos, int64_t read_size) {
            std::lock_guard<std::mutex> lo(lock);
            HDFSLOG(INFO, "pread start read file {}, pos {}, want size {}",
                    files[i], pos, read_size);
            auto actual_read_size =
                hdfsPread(fs, hFile, pos, pread_buffer, read_size);
            char* compare_buf = const_cast<char*>(datas[i].c_str()) + pos;
            if (std::string(pread_buffer, actual_read_size) !=
                std::string(compare_buf, actual_read_size)) {
                HDFSLOG(
                    INFO,
                    "pread failed random seek read file {}, pos {}, actual read "
                    "size {}",
                    files[i], pos, actual_read_size);
                EXPECT_TRUE(false);
            } else {
                EXPECT_TRUE(true);
            }
            HDFSLOG(
                INFO,
                "pread end random seek read file {}, pos {}, actual read size {}",
                files[i], pos, actual_read_size);
        };

        auto seek_and_read_fully = [&](int64_t pos, int64_t read_size) {
            std::lock_guard<std::mutex> lo(lock);
            HDFSLOG(
                INFO,
                "seek_and_read_fully start random seek read file {}, pos {}, "
                "want size {}",
                files[i], pos, read_size);
            hdfsSeek(fs, hFile, pos);

            int64_t total_read_size = 0;
            int target_read_size = read_size;
            HDFSLOG(
                INFO,
                "seek_and_read_fully start read file {}, pos {}, want size {}",
                files[i], pos, read_size);
            while (target_read_size > 0) {
                auto once_read_size = hdfsRead(
                    fs, hFile, buffer + total_read_size, target_read_size);
                if (once_read_size <= 0) {
                    break;
                }
                target_read_size -= once_read_size;
                total_read_size += once_read_size;
            }
            char* compare_buf = const_cast<char*>(datas[i].c_str()) + pos;
            if (std::string(buffer, total_read_size) !=
                std::string(compare_buf, total_read_size)) {
                HDFSLOG(
                    INFO,
                    "seek_and_read_fully failed random seek read file {}, pos {}, "
                    "actual read size {}",
                    files[i], pos, total_read_size);
                EXPECT_TRUE(false);
            } else {
                EXPECT_TRUE(true);
            }
            HDFSLOG(INFO,
                    "seek_and_read_fully end random seek read file {}, pos {}, "
                    "actual read size {}",
                    files[i], pos, total_read_size);
        };

        auto read_task = [&](bool pread) {
            std::random_device rd;
            std::mt19937 g(rd());

            for (int i = 0; i < 10; ++i) {
                int64_t seek_pos = g() % (file_size / 2);
                int64_t read_size = (file_size - seek_pos) / 4;
                read_size = std::min(read_size, (ONE_MB - 1024));
                if (pread) {
                    pread_fun(seek_pos, read_size);
                } else {
                    seek_and_read_fully(seek_pos, read_size);
                }
            }
        };

        std::vector<std::thread> workers;
        for (int i = 0; i < 4; ++i) {
            workers.push_back(std::thread([&]() { read_task(true); }));
            workers.push_back(std::thread([&]() { read_task(false); }));
        }

        for (auto& worker : workers) {
            worker.join();
        }

        delete[] buffer;
        buffer = nullptr;
        delete[] pread_buffer;
        pread_buffer = nullptr;

        hdfsCloseFile(fs, hFile);
        HDFSLOG(INFO, "end random seek read file {}, size {}", files[i],
                datas[i].size());
    }

    hdfsDisconnect(fs);
    hdfsFreeBuilder(builder);
}

TEST_F(TestParallelFileSystem, test_normal_read) {
    gen_files();

    int worker_nums = 4;
    std::vector<std::thread> read_workers;
    for (int i = 0; i < worker_nums; ++i) {
        read_workers.push_back(
            std::thread([=]() { read_files(i, worker_nums); }));
    }

    std::vector<std::thread> seek_read_workers;
    for (int i = 0; i < worker_nums; ++i) {
        seek_read_workers.push_back(
            std::thread([=]() { readom_seek_read_files(i, worker_nums); }));
    }

    for (auto& worker : read_workers) {
        worker.join();
    }

    for (auto& worker : seek_read_workers) {
        worker.join();
    }
}

}  // namespace Internal
}  // namespace Hdfs
