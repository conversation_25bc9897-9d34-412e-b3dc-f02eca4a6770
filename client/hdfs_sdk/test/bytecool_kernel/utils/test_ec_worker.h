#pragma once

#include <gtest/gtest.h>

#include <random>

#include "bytecool_kernel/utils/erasure/ec_worker.h"
#include "bytecool_kernel/utils/misc/crc_utils.h"

using CRCUtils = BYTECOOL::UTILS::CRCUtils;
using ECWorker = BYTECOOL::UTILS::ECWorker;

TEST(ECWorkerTest, EncodeAndDecode) {
    uint32_t data_num = 6;
    uint32_t parity_num = 3;
    uint32_t chunk_size = 128 * 1024;
    ECWorker ec_worker(data_num, parity_num, chunk_size);

    std::random_device rd;
    uint32_t test_str_length = 6 * 128 * 1024 + rd() % (10 * 128 * 1024);
    std::string test_str_data(test_str_length, 0);
    for (int i = 0; i < test_str_length; ++i) {
        test_str_data[i] = (char)(rd() % 26 + 'a');
    }

    std::unordered_map<uint32_t, std::string> encode_data;
    int ret = ec_worker.Encode(test_str_data, &encode_data);
    ASSERT_EQ(ret, 0);

    std::unordered_map<uint32_t, std::string> decode_raw_data;
    decode_raw_data.emplace(0, encode_data[0]);
    decode_raw_data.emplace(2, encode_data[2]);
    decode_raw_data.emplace(3, encode_data[3]);
    decode_raw_data.emplace(5, encode_data[5]);
    decode_raw_data.emplace(6, encode_data[6]);
    decode_raw_data.emplace(8, encode_data[8]);
    std::unordered_map<uint32_t, std::string> decode_data;
    ret = ec_worker.Decode(decode_raw_data, &decode_data);
    ASSERT_EQ(ret, 0);

    ASSERT_EQ(CRCUtils::Value(encode_data[1]), CRCUtils::Value(decode_data[1]));
    ASSERT_EQ(CRCUtils::Value(encode_data[4]), CRCUtils::Value(decode_data[4]));
    ASSERT_EQ(CRCUtils::Value(encode_data[7]), CRCUtils::Value(decode_data[7]));

    std::printf("[ECWorkerTest]Test encode and decode done.\n");
}