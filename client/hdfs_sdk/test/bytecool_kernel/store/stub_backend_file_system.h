#include "bytecool_kernel/common/result.h"
#include "client/filesystem/HyperFile.h"
#include "client/filesystem/HyperFileImpl.h"
#include "client/hdfs.h"

namespace BYTECOOL::STORE {

using Result = BYTECOOL::COMMON::Result;
using ErrorCode = BYTECOOL::COMMON::ErrorCode;
using CodingPolicy = BYTECOOL::COMMON::CodingPolicy;

static std::string hyperFileObj = "stub_test_hyperfile";
HyperBlockFilePairs g_hyperblockfiles;
struct HyperFileImpl : public Hdfs::HyperFile {
   public:
    HyperFileImpl() = default;
    HyperFileImpl(const HyperFileImpl&) = delete;
    HyperFileImpl& operator=(const HyperFileImpl&) = delete;

    ~HyperFileImpl() override { Close(); }

    void Close() override {}

    void StartImport(const std::string& original_path) override {}

    void CommitImport(uint64_t slice_offset, uint64_t slice_length,
                      const std::string& slice_payload) override {}

    void CommitRepair(const std::vector<std::string>& replace_names,
                      bool clean_trash) override {}

    const HyperBlockFilePairs& GetBlockFiles() const override {
        // for (int i = 0; i < 9; i++) {
        //     g_hyperblockfiles.push_back({std::to_string(i), nullptr});
        // }
        return g_hyperblockfiles;
    }

    bool IsInput() const override { return false; }

    const std::string& GetPath() const override { return hyperFileObj; }

    uint64_t GetEpoch() const override { return 0; }

   private:
    std::atomic<bool> closed_{false};
    HyperStream* streamer_{nullptr};
    HyperBlockFilePairs block_files_list_;

    friend Hdfs::HyperFile;
};

std::shared_ptr<Hdfs::HyperFile> Stub_hdfsCreateHyperFile_Success() {
    return std::make_shared<HyperFileImpl>();
}

hdfsHFile Stub_hdfsCreateHyperFile_Failed() { return nullptr; }

hdfsHyperFileHandle* Stub_hdfsGetHyperFileHandle_Success() {
    hdfsHyperFileHandle* handler = new hdfsHyperFileHandle;
    CodingPolicy coding_policy{};
    const int file_nums = coding_policy.data_num + coding_policy.parity_num;
    handler->blockFilesNum = file_nums;
    handler->blockFiles = (hdfsFile*)malloc(file_nums * sizeof(void*));
    std::vector<hdfsFile> blockFiles;
    blockFiles.resize(file_nums, nullptr);

    for (int i = 0; i < file_nums; i++) {
        handler->blockFiles[i] = blockFiles[i];
    }
    return handler;
}

hdfsHyperFileHandle* Stub_hdfsGetHyperFileHandle_Failed() { return nullptr; }

void Stub_hdfsFreeHyperFileHandle(hdfsHyperFileHandle* hyper_file_handle) {
    if (hyper_file_handle != nullptr) {
        free(hyper_file_handle->blockFiles);
        delete hyper_file_handle;
    }
}

int Stub_hdfsCloseHyperFile_Success() { return 0; }

int Stub_hdfsCloseHyperFile_Failed() { return -1; }

Result Stub_BackendMetaSystem_CloseHyperFile() {
    return Result::Error(ErrorCode::kArchiveReadPoolInitArchiveFailed);
}

}  // namespace BYTECOOL::STORE
