#pragma once

#include <gtest/gtest.h>

#include "../utils/util.h"
#include "bytecool_kernel/store/backend_file_system.h"
#include "bytecool_kernel/utils/misc/time_utils.h"

namespace BYTECOOL::STORE {

class ReadBenchTest : public testing::Test {
   protected:
    void SetUp() override {
        hdfs_builder = hdfsNewCoolBuilder();
        EXPECT_TRUE(hdfs_builder != nullptr);
        hdfs_file_system = hdfsBuilderConnect(hdfs_builder);
        EXPECT_TRUE(hdfs_file_system != nullptr);
    }

    void TearDown() override {
        if (hdfs_file_system != nullptr) {
            hdfsDisconnect(hdfs_file_system);
            hdfs_file_system = nullptr;
        }
        if (hdfs_builder != nullptr) {
            hdfsFreeBuilder(hdfs_builder);
            hdfs_builder = nullptr;
        }
    }

    hdfsBuilder* hdfs_builder{nullptr};
    hdfsFS hdfs_file_system{nullptr};
};

TEST_F(ReadBenchTest, Base) {
    uint32_t chunk_len = 128 * 1024;
    uint32_t chunk_num = 6;
    std::vector<std::vector<char>> buf1(chunk_num,
                                        std::vector<char>(chunk_len, '1'));
    std::vector<char> buf2;
    buf2.resize(chunk_num * chunk_len);

    uint64_t tt0 = BYTECOOL::UTILS::TimeUtils::GetCurTimestampMs();
    for (uint32_t i = 0; i < chunk_num; ++i) {
        for (uint32_t j = 0; j < chunk_len; ++j) {
            buf2[i * chunk_len + j] = buf1[i][j];
        }
    }
    uint64_t tt1 = BYTECOOL::UTILS::TimeUtils::GetCurTimestampMs();
    fmt::print("copy_cost_ms={}\n", tt1 - tt0);

    uint32_t read_loop_num = 100;
    uint32_t read_length = 4 * 1024 * 1024;
    std::string file_v0_path{
        "/user/bytecool/test/read_bench/1GB/1G_file_016.data"};
    std::string file_v1_path(
        "/user/bytecool/test/read_bench/1GB_V1/1G_file_016.data");

    std::string file_v0_buf(read_length, 0);
    uint64_t ts_0 = BYTECOOL::UTILS::TimeUtils::GetCurTimestampMs();
    auto file_v0_handle =
        hdfsOpenFile(hdfs_file_system, file_v0_path.c_str(), O_RDONLY, 0, 0, 0);
    for (uint32_t i = 0; i < read_loop_num; ++i) {
        uint64_t ts1 = BYTECOOL::UTILS::TimeUtils::GetCurTimestampMs();
        uint32_t real_read_length = 0;
        while (real_read_length < read_length) {
            auto len =
                hdfsRead(hdfs_file_system, file_v0_handle,
                         file_v0_buf.data() + real_read_length, read_length);
            real_read_length += len;
        }
        uint64_t ts2 = BYTECOOL::UTILS::TimeUtils::GetCurTimestampMs();
        fmt::print("Normal read: loop={} cost_ms={} real_read_length={}\n", i,
                   ts2 - ts1, real_read_length);
    }
    uint64_t ts_1 = BYTECOOL::UTILS::TimeUtils::GetCurTimestampMs();

    std::string file_v1_buf(read_length, 0);
    uint64_t ts_2 = BYTECOOL::UTILS::TimeUtils::GetCurTimestampMs();
    auto file_v1_handle =
        hdfsOpenFile(hdfs_file_system, file_v1_path.c_str(), O_RDONLY, 0, 0, 0);
    for (uint32_t i = 0; i < read_loop_num; ++i) {
        uint64_t ts1 = BYTECOOL::UTILS::TimeUtils::GetCurTimestampMs();
        auto real_read_length = hdfsRead(hdfs_file_system, file_v1_handle,
                                         file_v1_buf.data(), read_length);
        uint64_t ts2 = BYTECOOL::UTILS::TimeUtils::GetCurTimestampMs();
        fmt::print("ByteCool v1 read: loop={} cost_ms={} real_read_length={}\n",
                   i, ts2 - ts1, real_read_length);
    }
    uint64_t ts_3 = BYTECOOL::UTILS::TimeUtils::GetCurTimestampMs();

    fmt::print("Normal read: loop_num={} total_cost_ms={}\n", read_loop_num,
               ts_1 - ts_0);
    fmt::print("ByteCool v1 read: loop_num={} total_cost_ms={}\n",
               read_loop_num, ts_3 - ts_2);
}

}  // namespace BYTECOOL::STORE