#pragma once

#include <gtest/gtest.h>

#include "../utils/util.h"
#include "bytecool_kernel/handler/bytecool_handler.h"

class ValidateHandlerTest : public testing::Test {
   protected:
    void SetUp() override {
        hdfs_builder = hdfsNewCoolBuilder();
        EXPECT_TRUE(hdfs_builder != nullptr);
        hdfs_file_system = hdfsBuilderConnect(hdfs_builder);
        EXPECT_TRUE(hdfs_file_system != nullptr);

        std::vector<uint32_t> test_file_lengths = {6543210};
        for (const auto& test_file_length : test_file_lengths) {
            GenUserFile(test_file_length);
        }
    }

    void TearDown() override {
        if (hdfs_file_system != nullptr) {
            hdfsDisconnect(hdfs_file_system);
            hdfs_file_system = nullptr;
        }
        if (hdfs_builder != nullptr) {
            hdfsFreeBuilder(hdfs_builder);
            hdfs_builder = nullptr;
        }
    }

    void GenUserFile(uint64_t file_length) {
        std::random_device rd;
        std::string file_path =
            fmt::format("/user/bytecool/test/user_file/test_file_{}",
                        BYTECOOL::UTILS::UuidUtils::ToString(
                            BYTECOOL::UTILS::UuidUtils::Generate()));
        BYTECOOL::STORE::BackendFileSystem backend_fs(
            BYTECOOL::COMMON::OperatePermission::kWriteOnly, hdfs_file_system);
        std::string file_data(file_length, 0);
        for (int i = 0; i < file_length; ++i) {
            file_data[i] = (char)(rd() % 26 + 'a');
        }
        uint32_t file_crc = BYTECOOL::UTILS::CRCUtils::Value(file_data);
        BYTECOOL::COMMON::Result ret = backend_fs.Open(file_path);
        ASSERT_TRUE(ret);
        ret = backend_fs.Write(file_data);
        ASSERT_TRUE(ret);
        ret = backend_fs.Flush();
        ASSERT_TRUE(ret);
        backend_fs.Close();
        hdfs_file_path.emplace_back(file_path, file_length);
        hdfs_file_data.insert({file_path, std::move(file_data)});
        std::printf(
            "[ReadHandlerTest]Get test file info success. path=%s length=%lu "
            "crc=%d\n",
            file_path.c_str(), file_length, file_crc);
    }

    hdfsBuilder* hdfs_builder{nullptr};
    hdfsFS hdfs_file_system{nullptr};
    std::vector<std::pair<std::string, uint64_t>> hdfs_file_path{};
    std::map<std::string, std::string> hdfs_file_data{};
};

TEST_F(ValidateHandlerTest, Validate) {
    BYTECOOL::COMMON::Config config = {
        .backend_fs = hdfs_file_system,
        .archive_path_prefix = "/user/bytecool/test/archive",
    };
    BYTECOOL::HANDLER::ByteCoolHandler bytecool_handler(config);
    ASSERT_TRUE(bytecool_handler.Init());

    BYTECOOL::COMMON::ImportRequest import_request = {
        .task_id = 10,
        .paths = hdfs_file_path,
        .cluster = "test_cluster",
        .tenant = "test_tenant",
        .partition = "20240101",
        .coding_policy = BYTECOOL::COMMON::CodingPolicy({
            .data_num = 6,
            .parity_num = 3,
            .chunk_size = 128 * 1024,
        }),
        .import_type = BYTECOOL::COMMON::ImportType::kImport,
    };
    BYTECOOL::COMMON::ImportResponse import_response;
    ASSERT_TRUE(bytecool_handler.Import(import_request, &import_response));
    ASSERT_EQ(import_response.success_paths.size(), hdfs_file_path.size());

    BYTECOOL::COMMON::ValidateRequest validate_request = {
        .path = hdfs_file_path[0].first,
        .is_strict = true,
    };
    BYTECOOL::COMMON::ValidateResponse validate_response{};
    ASSERT_TRUE(
        bytecool_handler.Validate(validate_request, &validate_response));
    ASSERT_EQ(validate_response.code, BYTECOOL::COMMON::ErrorCode::kSuccess);

    ASSERT_TRUE(bytecool_handler.Close());
    std::printf("[ValidateHandlerTest]Test validate done. file_num=%zu\n",
                hdfs_file_path.size());
}