#include <glog/logging.h>
#include <signal.h>

#include <chrono>
#include <thread>

#include "async_read_press.h"
#include "async_write_press.h"
#include "meta_press.h"
#include "read_press.h"
#include "write_press.h"

std::shared_ptr<PressBase> PressBase::press_runner_ = nullptr;

void quitHandler(int signal) {
    LOG(ERROR) << "Receive signal " << signal << ". ";
    if (PressBase::press_runner_) {
        LOG(ERROR) << "Prepare to stop press_runner_";
        PressBase::press_runner_->stop();
    }
}

int main(int argc, char* argv[]) {
    FLAGS_logtostderr = true;
    FLAGS_minloglevel = google::INFO;
    char* logv = getenv("GLOG_v");
    if (logv) {
        FLAGS_v = atoi(logv);
    } else {
        FLAGS_v = 8;
    }

    LOG(ERROR) << "argc: " << argc;
    if (argc == 1) {
        throw std::runtime_error("Invalid argc: 1");
    }

    struct sigaction newhandler;
    newhandler.sa_handler = &quitHandler;
    sigaddset(&newhandler.sa_mask, SIGQUIT);
    newhandler.sa_flags = 0;
    sigaction(SIGINT, &newhandler, NULL);

    if (std::string(argv[1]) == "write" && argc == 6) {
        // ./test/press_test/_build/hdfs_press_test write
        // /usr/jinrj/formHDFS.log1
        // /usr/hdfs_client/test/write_press_test 10.227.159.28 10
        PressBase::press_runner_ = std::make_shared<WritePress>(
            argv[2], argv[3], argv[4], atoi(argv[5]));
        PressBase::press_runner_->start();
    } else if (std::string(argv[1]) == "async_write" && argc == 6) {
        // ./test/press_test/_build/hdfs_press_test async_write
        // /usr/jinrj/formHDFS.log1
        // /usr/hdfs_client/test/asyncwrite_press_test 10.227.159.28 10
        PressBase::press_runner_ = std::make_shared<AsyncWritePress>(
            argv[2], argv[3], argv[4], atoi(argv[5]));
        PressBase::press_runner_->start();
    } else if (std::string(argv[1]) == "read" && argc == 6) {
        // ./test/press_test/_build/hdfs_press_test read
        // /usr/jinrj/formHDFS.log1
        // /usr/hdfs_client/test/write_press_test 10.227.159.28 10
        PressBase::press_runner_ = std::make_shared<ReadPress>(
            argv[2], argv[3], argv[4], atoi(argv[5]));
        PressBase::press_runner_->start();
    } else if (std::string(argv[1]) == "async_read" && argc == 6) {
        // ./test/press_test/_build/hdfs_press_test async_read
        // /usr/jinrj/formHDFS.log1
        // /usr/hdfs_client/test/asyncwrite_press_test 10.227.159.28 10
        PressBase::press_runner_ = std::make_shared<AsyncReadPress>(
            argv[2], argv[3], argv[4], atoi(argv[5]));
        PressBase::press_runner_->start();
    } else if (std::string(argv[1]) == "meta" && argc == 5) {
        // ./test/press_test/_build/hdfs_press_test meta
        // /usr/hdfs_client/test/meta_press_test 10.227.159.28 10
        PressBase::press_runner_ =
            std::make_shared<MetaPress>(argv[2], argv[3], atoi(argv[4]));
        PressBase::press_runner_->start();
    } else {
        throw std::runtime_error("Invalid arguement");
    }

    PressBase::press_runner_->join();
    LOG(ERROR) << "Press runner stopped.";

    return 0;
}