#ifndef _HDFS_LIBHDFS3_WRITE_PRESS_H_
#define _HDFS_LIBHDFS3_WRITE_PRESS_H_

#include <glog/logging.h>

#include <atomic>
#include <cassert>
#include <cstring>

#include "press_base.h"

// ./test/press_test/_build/hdfs_press_test write /usr/jinrj/formHDFS.log1
// /usr/hdfs_client/test/write_press_test ************* 10
class WritePress : public PressBase {
   public:
    WritePress(std::string read_file_path, std::string write_file_path,
               std::string namenode_ip, int32_t thread_num)
        : PressBase(namenode_ip, thread_num),
          write_file_path_(write_file_path) {
        if (write_file_path.find("/usr/hdfs_client/test/") ==
            std::string::npos) {
            throw std::runtime_error(
                "Invalid path to write: " + write_file_path +
                ". Expect to start with /usr/hdfs_client/test/ ");
        }

        hdfsDelete(fs_, write_file_path.c_str(), true);
        hdfsCreateDirectory(fs_, write_file_path.c_str());

        hdfsFileInfo* info = hdfsGetPathInfo(fs_, read_file_path.c_str());
        if (info == nullptr || info[0].mKind == kObjectKindDirectory) {
            throw std::runtime_error("Invalid file to read: " + read_file_path);
        }

        buffer_ = new char[info[0].mSize];
        buffer_size_ = info[0].mSize;
        int64_t length_to_read = info[0].mSize;
        hdfsFreeFileInfo(info, 1);

        LOG(ERROR) << "Start to read file: " << read_file_path;
        hdfsFile hdfs_file =
            hdfsOpenFile(fs_, read_file_path.c_str(), O_RDONLY, 0, 0, 0);
        int64_t has_read = 0;
        while (has_read < length_to_read) {
            has_read +=
                hdfsRead(fs_, hdfs_file, buffer_ + has_read, 4 * 1024 * 1024);
        }
        LOG(ERROR) << "Finished to read file: " << read_file_path;
        hdfsCloseFile(fs_, hdfs_file);
    }

    ~WritePress() { delete[] buffer_; }

   protected:
    virtual void threadloopTask() {
        std::shared_ptr<noodle::Counter> throughput_counter_ =
            Hdfs::Internal::getMetricSource().Counter(
                "native_client.write_press.throughput", {});
        std::shared_ptr<noodle::Counter> fail_counter_ =
            Hdfs::Internal::getMetricSource().Counter(
                "native_client.write_press.fail_counter", {});

        while (!stopped_) {
            int64_t file_id = unique_file_id_.fetch_add(1);
            std::string current_path_name =
                write_file_path_ + "/file" + std::to_string(file_id);
            hdfsFile file =
                hdfsOpenFile(fs_, current_path_name.c_str(), O_CREAT, 64 * 1024,
                             3, 512 * 1024 * 1024);
            if (!file) {
                throw std::runtime_error("Open file failed." +
                                         std::to_string(errno));
            }

            int64_t has_write = 0;
            while (has_write < buffer_size_) {
                int64_t write_this_turn = hdfsWrite(
                    fs_, file, buffer_ + has_write, buffer_size_ - has_write);
                if (write_this_turn == -1) {
                    fail_counter_->Increase();
                    throw std::runtime_error("write failed!!");
                }
                has_write += write_this_turn;
                throughput_counter_->Increase(write_this_turn);
            }
            hdfsCloseFile(fs_, file);
            hdfsDelete(fs_, current_path_name.c_str(), false);
            LOG(ERROR) << "Write file: " << current_path_name << " finished.";
        }
    }

   protected:
    std::string write_file_path_;
    std::atomic<int> unique_file_id_{0};
    int64_t buffer_size_{0};
    char* buffer_;
};

#endif /* _HDFS_LIBHDFS3_WRITE_PRESS_H_ */