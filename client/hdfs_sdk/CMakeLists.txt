cmake_minimum_required(VERSION 3.13.0)

project("native-hdfs-client" C CXX)

set(PACKAGE_NAME      "native-hdfs-client")
set(PACKAGE_VERSION   "v1")
set(PACKAGE_STRING    "${PACKAGE_NAME} ${PACKAGE_VERSION}")
set(PACKAGE_TARNAME   "${PACKAGE_NAME}-${PACKAGE_VERSION}")
set(HDFS_CLIENT_HOME ${CMAKE_CURRENT_SOURCE_DIR})

set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)
set(CMAKE_CXX_EXTENSIONS OFF)
set(CMAKE_EXPORT_COMPILE_COMMANDS ON)

message(STATUS "Cmake version: ${CMAKE_VERSION}")

option(HDFS_ENABLE_STATIC "Whether to build and install static library" ON)
option(HDFS_ENABLE_SHARED "Whether to build and install shared library" ON)
option(HDFS_BUILD_CMD "Whether to build and install hdfs CMD binary" ON)
option(HDFS_BUILD_BENCH "Whether to build and install hdfs CMD binary" OFF)
option(HDFS_BUILD_FUSE "Whether to build and install fuse tools" ON)
option(HDFS_BUILD_TEST "Whether to build tests" ON)
option(HDFS_BUILD_EXAMPLE "Whether to build examples" OFF)
option(HDFS_ENABLE_ASAN "Whether to enable AddressSanitizer" OFF)
option(HDFS_WITH_JEMALLOC "Whether to build jemalloc, if not then hidden symbols" OFF)
option(HDFS_BUILD_COMMUNITY_SASL "Whether to suppport community sasl challenge" OFF)
option(HDFS_DISABLE_IO_MONITOR "Whether to support IO monitor" OFF)
option(HDFS_BUILD_WITH_VERSION "Whether to refresh git version header file" ON)
option(HDFS_EXPORT_SYMBOL "Whether to support IO monitor" OFF)
option(HDFS_ENABLE_COV "Enable code coverage" OFF)
option(HDFS_DISABLE_THIRDPARTY_STORAGE "Disable ThirdParty Storage Build" OFF)
option(HDFS_DISABLE_BYTERPC "Disable Byte Rpc" OFF)

if(HDFS_ENABLE_ASAN)
    set(BUILD_3RD_OPTS --enable-asan)
endif()

if(HDFS_WITH_JEMALLOC)
    set(BUILD_3RD_OPTS --enable-jemalloc)
endif()

if(NOT HDFS_BUILD_FUSE)
    set(BUILD_3RD_OPTS ${BUILD_3RD_OPTS} --disable-fuse)
endif()

if(HDFS_DISABLE_THIRDPARTY_STORAGE)
    set(BUILD_3RD_OPTS ${BUILD_3RD_OPTS} --disable-third-storage)
endif()

if(HDFS_DISABLE_BYTERPC)
    set(BUILD_3RD_OPTS ${BUILD_3RD_OPTS} --disable-byterpc)
endif()

if("${CMAKE_CXX_COMPILER_ID}" STREQUAL "Clang")
    set(BUILD_3RD_OPTS ${BUILD_3RD_OPTS} --use-clang)
endif()

message(STATUS "BUILD_3RD_OPTS=${BUILD_3RD_OPTS}")

if(HDFS_BUILD_WITH_VERSION)
    execute_process(COMMAND ${HDFS_CLIENT_HOME}/refresh-git-version.sh WORKING_DIRECTORY ${HDFS_CLIENT_HOME} RESULT_VARIABLE refresh_version_res)
    if(${refresh_version_res})
        message(FATAL_ERROR "Script refresh-git-version failed")
    else()
        message(STATUS "Script refresh-git-version success!")
    endif()
endif()

if("${HDFS_CLIENT_THIRDPARTY_ROOT}" STREQUAL "")
    # Build thirdparty on our own if the thirdparty is not built
    set(CMAKE_PREFIX_PATH ${HDFS_CLIENT_HOME}/third_party/install)
    message(STATUS "Use default HDFS_CLIENT_THIRDPARTY_ROOT: ${CMAKE_PREFIX_PATH}")
    find_library(FOLLY_LIB_FOUND libfolly.a)
    find_library(METRICS_SDK_LIB_FOUND libmetrics_sdk.a)
    if(NOT FOLLY_LIB_FOUND OR NOT METRICS_SDK_LIB_FOUND)
        message(STATUS "Thirdparty not found. Start to build thirdparty, please wait...")
        if(${CMAKE_VERSION} VERSION_LESS 3.18)
            message(STATUS "WARNING: Current CMAKE_VERSION < 3.18, subprocess output "
                "of building thirdparty can not print to stdout. Please check "
                "${CMAKE_CURRENT_BINARY_DIR}/build-3rd-out.log and "
                "${CMAKE_CURRENT_BINARY_DIR}/build-3rd-err.log."
                )
            execute_process(
                COMMAND ./build-thirdparty.sh ${BUILD_3RD_OPTS}
                WORKING_DIRECTORY ${HDFS_CLIENT_HOME}
                RESULT_VARIABLE build_3rd_res
                OUTPUT_FILE ${CMAKE_CURRENT_BINARY_DIR}/build-3rd-out.log
                ERROR_FILE ${CMAKE_CURRENT_BINARY_DIR}/build-3rd-err.log
                )
        else()
            execute_process(
                COMMAND ./build-thirdparty.sh ${BUILD_3RD_OPTS}
                WORKING_DIRECTORY ${HDFS_CLIENT_HOME}
                RESULT_VARIABLE build_3rd_res
                ECHO_OUTPUT_VARIABLE
                )
        endif()
        if(${build_3rd_res})
            message(FATAL_ERROR "Fail to build third party. Please check the log for details.")
        else()
            message(STATUS "Build thirdparty success!")
        endif()
    else()
        message(STATUS "Skip building thirdparty because it has existed.")
    endif()
else()
    # Use user-specified thirparty
    set(CMAKE_PREFIX_PATH ${HDFS_CLIENT_THIRDPARTY_ROOT})
    message(STATUS "Specified HDFS_CLIENT_THIRDPARTY_ROOT: ${HDFS_CLIENT_THIRDPARTY_ROOT}")
endif()

if("${CMAKE_BUILD_TYPE}" STREQUAL "")
    SET(CMAKE_BUILD_TYPE "Debug")
endif()

find_library(NUMA_LIBRARY libnuma.a REQUIRED)
find_library(PROTOBUF_LIBRARY libprotobuf.a REQUIRED)
find_library(GFLAGS_LIBRARY libgflags.a REQUIRED)
find_library(GLOG_LIBRARY libglog.a REQUIRED)
find_library(GMOCK_LIBRARY libgmock.a REQUIRED)
find_library(GTEST_LIBRARY libgtest.a REQUIRED)
find_library(DOUBLE_CONVERSION_LIBRARY libdouble-conversion.a REQUIRED)
find_library(LIBXML2_LIBRARY libxml2.a REQUIRED)
find_library(CURL_LIBRARY libcurl.a REQUIRED)
find_library(Z_LIBRARY libz.a REQUIRED)
find_library(LZMA_LIBRARY liblzma.a REQUIRED)
find_library(ZSTD_LIBRARY libzstd.a REQUIRED)
find_library(FMT_LIBRARY libfmt.a REQUIRED)
find_library(EVENT_LIBRARY libevent.a REQUIRED)
find_library(UUID_LIBRARY libuuid.a REQUIRED)
find_library(CONSUL_LIBRARY libconsul.a REQUIRED)

find_library(CRC32_LIBRARY libcrc32c.a REQUIRED)

if(NOT HDFS_DISABLE_THIRDPARTY_STORAGE OR NOT HDFS_DISABLE_BYTERPC)
    find_library(ABSL_FLAGS_COMMANDLINEFLAG_INTERNAL_LIBRARY libabsl_flags_commandlineflag_internal.a REQUIRED)
    find_library(ABSL_LEAK_CHECK_LIBRARY libabsl_leak_check.a REQUIRED)
    find_library(ABSL_UTF8_FOR_CODE_POINT_LIBRARY libabsl_utf8_for_code_point.a REQUIRED)
    find_library(ABSL_RANDOM_INTERNAL_PLATFORM_LIBRARY libabsl_random_internal_platform.a REQUIRED)
    find_library(ABSL_SPINLOCK_WAIT_LIBRARY libabsl_spinlock_wait.a REQUIRED)
    find_library(ABSL_LOG_INTERNAL_NULLGUARD_LIBRARY libabsl_log_internal_nullguard.a REQUIRED)
    find_library(ABSL_EXPONENTIAL_BIASED_LIBRARY libabsl_exponential_biased.a REQUIRED)
    find_library(ABSL_STRERROR_LIBRARY libabsl_strerror.a REQUIRED)
    find_library(ABSL_PERIODIC_SAMPLER_LIBRARY libabsl_periodic_sampler.a REQUIRED)
    find_library(ABSL_LOG_SEVERITY_LIBRARY libabsl_log_severity.a REQUIRED)
    find_library(ABSL_RANDOM_SEED_GEN_EXCEPTION_LIBRARY libabsl_random_seed_gen_exception.a REQUIRED)
    find_library(ABSL_DECODE_RUST_PUNYCODE_LIBRARY libabsl_decode_rust_punycode.a REQUIRED)
    find_library(ABSL_CIVIL_TIME_LIBRARY libabsl_civil_time.a REQUIRED)
    find_library(ABSL_RANDOM_INTERNAL_RANDEN_SLOW_LIBRARY libabsl_random_internal_randen_slow.a REQUIRED)
    find_library(ABSL_INT128_LIBRARY libabsl_int128.a REQUIRED)
    find_library(ABSL_RAW_LOGGING_INTERNAL_LIBRARY libabsl_raw_logging_internal.a REQUIRED)
    find_library(ABSL_RANDOM_INTERNAL_RANDEN_HWAES_IMPL_LIBRARY libabsl_random_internal_randen_hwaes_impl.a REQUIRED)
    find_library(ABSL_RANDOM_INTERNAL_RANDEN_HWAES_LIBRARY libabsl_random_internal_randen_hwaes.a REQUIRED)
    find_library(ABSL_DEMANGLE_RUST_LIBRARY libabsl_demangle_rust.a REQUIRED)
    find_library(ABSL_BAD_VARIANT_ACCESS_LIBRARY libabsl_bad_variant_access.a REQUIRED)
    find_library(ABSL_SCOPED_SET_ENV_LIBRARY libabsl_scoped_set_env.a REQUIRED)
    find_library(ABSL_BAD_OPTIONAL_ACCESS_LIBRARY libabsl_bad_optional_access.a REQUIRED)
    find_library(ABSL_BAD_ANY_CAST_IMPL_LIBRARY libabsl_bad_any_cast_impl.a REQUIRED)
    find_library(ABSL_DEBUGGING_INTERNAL_LIBRARY libabsl_debugging_internal.a REQUIRED)
    find_library(ABSL_THROW_DELEGATE_LIBRARY libabsl_throw_delegate.a REQUIRED)
    find_library(ABSL_TIME_ZONE_LIBRARY libabsl_time_zone.a REQUIRED)
    find_library(ABSL_RANDOM_INTERNAL_RANDEN_LIBRARY libabsl_random_internal_randen.a REQUIRED)
    find_library(ABSL_CORDZ_FUNCTIONS_LIBRARY libabsl_cordz_functions.a REQUIRED)
    find_library(ABSL_STACKTRACE_LIBRARY libabsl_stacktrace.a REQUIRED)
    find_library(ABSL_DEMANGLE_INTERNAL_LIBRARY libabsl_demangle_internal.a REQUIRED)
    find_library(ABSL_BASE_LIBRARY libabsl_base.a REQUIRED)
    find_library(ABSL_LOG_INTERNAL_CONDITIONS_LIBRARY libabsl_log_internal_conditions.a REQUIRED)
    find_library(ABSL_CRC_CPU_DETECT_LIBRARY libabsl_crc_cpu_detect.a REQUIRED)
    find_library(ABSL_CITY_LIBRARY libabsl_city.a REQUIRED)
    find_library(ABSL_LOW_LEVEL_HASH_LIBRARY libabsl_low_level_hash.a REQUIRED)
    find_library(ABSL_STRINGS_INTERNAL_LIBRARY libabsl_strings_internal.a REQUIRED)
    find_library(ABSL_STRING_VIEW_LIBRARY libabsl_string_view.a REQUIRED)
    find_library(ABSL_MALLOC_INTERNAL_LIBRARY libabsl_malloc_internal.a REQUIRED)
    find_library(ABSL_POISON_LIBRARY libabsl_poison.a REQUIRED)
    find_library(ABSL_CRC_INTERNAL_LIBRARY libabsl_crc_internal.a REQUIRED)
    find_library(ABSL_GRAPHCYCLES_INTERNAL_LIBRARY libabsl_graphcycles_internal.a REQUIRED)
    find_library(ABSL_STRINGS_LIBRARY libabsl_strings.a REQUIRED)
    find_library(ABSL_FLAGS_COMMANDLINEFLAG_LIBRARY libabsl_flags_commandlineflag.a REQUIRED)
    find_library(ABSL_LOG_INTERNAL_FNMATCH_LIBRARY libabsl_log_internal_fnmatch.a REQUIRED)
    find_library(ABSL_RANDOM_DISTRIBUTIONS_LIBRARY libabsl_random_distributions.a REQUIRED)
    find_library(ABSL_LOG_INTERNAL_PROTO_LIBRARY libabsl_log_internal_proto.a REQUIRED)
    find_library(ABSL_SYMBOLIZE_LIBRARY libabsl_symbolize.a REQUIRED)
    find_library(ABSL_HASH_LIBRARY libabsl_hash.a REQUIRED)
    find_library(ABSL_STR_FORMAT_INTERNAL_LIBRARY libabsl_str_format_internal.a REQUIRED)
    find_library(ABSL_RANDOM_INTERNAL_SEED_MATERIAL_LIBRARY libabsl_random_internal_seed_material.a REQUIRED)
    find_library(ABSL_TIME_LIBRARY libabsl_time.a REQUIRED)
    find_library(ABSL_FLAGS_PRIVATE_HANDLE_ACCESSOR_LIBRARY libabsl_flags_private_handle_accessor.a REQUIRED)
    find_library(ABSL_EXAMINE_STACK_LIBRARY libabsl_examine_stack.a REQUIRED)
    find_library(ABSL_LOG_ENTRY_LIBRARY libabsl_log_entry.a REQUIRED)
    find_library(ABSL_FAILURE_SIGNAL_HANDLER_LIBRARY libabsl_failure_signal_handler.a REQUIRED)
    find_library(ABSL_KERNEL_TIMEOUT_INTERNAL_LIBRARY libabsl_kernel_timeout_internal.a REQUIRED)
    find_library(ABSL_LOG_INTERNAL_GLOBALS_LIBRARY libabsl_log_internal_globals.a REQUIRED)
    find_library(ABSL_RANDOM_INTERNAL_POOL_URBG_LIBRARY libabsl_random_internal_pool_urbg.a REQUIRED)
    find_library(ABSL_RANDOM_INTERNAL_DISTRIBUTION_TEST_UTIL_LIBRARY libabsl_random_internal_distribution_test_util.a REQUIRED)
    find_library(ABSL_CRC32C_LIBRARY libabsl_crc32c.a REQUIRED)
    find_library(ABSL_LOG_SINK_LIBRARY libabsl_log_sink.a REQUIRED)
    find_library(ABSL_FLAGS_MARSHALLING_LIBRARY libabsl_flags_marshalling.a REQUIRED)
    find_library(ABSL_RANDOM_SEED_SEQUENCES_LIBRARY libabsl_random_seed_sequences.a REQUIRED)
    find_library(ABSL_SYNCHRONIZATION_LIBRARY libabsl_synchronization.a REQUIRED)
    find_library(ABSL_LOG_INTERNAL_FORMAT_LIBRARY libabsl_log_internal_format.a REQUIRED)
    find_library(ABSL_CRC_CORD_STATE_LIBRARY libabsl_crc_cord_state.a REQUIRED)
    find_library(ABSL_FLAGS_PROGRAM_NAME_LIBRARY libabsl_flags_program_name.a REQUIRED)
    find_library(ABSL_HASHTABLEZ_SAMPLER_LIBRARY libabsl_hashtablez_sampler.a REQUIRED)
    find_library(ABSL_CORDZ_HANDLE_LIBRARY libabsl_cordz_handle.a REQUIRED)
    find_library(ABSL_VLOG_CONFIG_INTERNAL_LIBRARY libabsl_vlog_config_internal.a REQUIRED)
    find_library(ABSL_CORD_INTERNAL_LIBRARY libabsl_cord_internal.a REQUIRED)
    find_library(ABSL_FLAGS_CONFIG_LIBRARY libabsl_flags_config.a REQUIRED)
    find_library(ABSL_RAW_HASH_SET_LIBRARY libabsl_raw_hash_set.a REQUIRED)
    find_library(ABSL_LOG_GLOBALS_LIBRARY libabsl_log_globals.a REQUIRED)
    find_library(ABSL_CORDZ_INFO_LIBRARY libabsl_cordz_info.a REQUIRED)
    find_library(ABSL_LOG_INITIALIZE_LIBRARY libabsl_log_initialize.a REQUIRED)
    find_library(ABSL_FLAGS_INTERNAL_LIBRARY libabsl_flags_internal.a REQUIRED)
    find_library(ABSL_LOG_INTERNAL_LOG_SINK_SET_LIBRARY libabsl_log_internal_log_sink_set.a REQUIRED)
    find_library(ABSL_CORDZ_SAMPLE_TOKEN_LIBRARY libabsl_cordz_sample_token.a REQUIRED)
    find_library(ABSL_LOG_INTERNAL_MESSAGE_LIBRARY libabsl_log_internal_message.a REQUIRED)
    find_library(ABSL_CORD_LIBRARY libabsl_cord.a REQUIRED)
    find_library(ABSL_DIE_IF_NULL_LIBRARY libabsl_die_if_null.a REQUIRED)
    find_library(ABSL_LOG_INTERNAL_CHECK_OP_LIBRARY libabsl_log_internal_check_op.a REQUIRED)
    find_library(ABSL_STATUS_LIBRARY libabsl_status.a REQUIRED)
    find_library(ABSL_FLAGS_REFLECTION_LIBRARY libabsl_flags_reflection.a REQUIRED)
    find_library(ABSL_STATUSOR_LIBRARY libabsl_statusor.a REQUIRED)
    find_library(ABSL_LOG_FLAGS_LIBRARY libabsl_log_flags.a REQUIRED)
    find_library(ABSL_FLAGS_USAGE_INTERNAL_LIBRARY libabsl_flags_usage_internal.a REQUIRED)
    find_library(ABSL_FLAGS_USAGE_LIBRARY libabsl_flags_usage.a REQUIRED)
    find_library(ABSL_FLAGS_PARSE_LIBRARY libabsl_flags_parse.a REQUIRED)

    set(ABSL_STRINGS_LIBRARYS ${ABSL_STRINGS_LIBRARY} ${ABSL_STRINGS_INTERNAL_LIBRARY})

    set(ABSL_LIBRARY
        ${ABSL_FLAGS_PARSE_LIBRARY}
        ${ABSL_FLAGS_USAGE_LIBRARY}
        ${ABSL_FLAGS_USAGE_INTERNAL_LIBRARY}
        ${ABSL_LOG_FLAGS_LIBRARY}
        ${ABSL_STATUSOR_LIBRARY}
        ${ABSL_FLAGS_REFLECTION_LIBRARY}
        ${ABSL_STATUS_LIBRARY}
        ${ABSL_LOG_INTERNAL_CHECK_OP_LIBRARY}
        ${ABSL_DIE_IF_NULL_LIBRARY}
        ${ABSL_CORD_LIBRARY}
        ${ABSL_LOG_INTERNAL_MESSAGE_LIBRARY}
        ${ABSL_CORDZ_SAMPLE_TOKEN_LIBRARY}
        ${ABSL_LOG_INTERNAL_LOG_SINK_SET_LIBRARY}
        ${ABSL_FLAGS_INTERNAL_LIBRARY}
        ${ABSL_LOG_INITIALIZE_LIBRARY}
        ${ABSL_CORDZ_INFO_LIBRARY}
        ${ABSL_LOG_GLOBALS_LIBRARY}
        ${ABSL_RAW_HASH_SET_LIBRARY}
        ${ABSL_FLAGS_CONFIG_LIBRARY}
        ${ABSL_CORD_INTERNAL_LIBRARY}
        ${ABSL_VLOG_CONFIG_INTERNAL_LIBRARY}
        ${ABSL_CORDZ_HANDLE_LIBRARY}
        ${ABSL_HASHTABLEZ_SAMPLER_LIBRARY}
        ${ABSL_FLAGS_PROGRAM_NAME_LIBRARY}
        ${ABSL_CRC_CORD_STATE_LIBRARY}
        ${ABSL_LOG_INTERNAL_FORMAT_LIBRARY}
        ${ABSL_SYNCHRONIZATION_LIBRARY}
        ${ABSL_RANDOM_SEED_SEQUENCES_LIBRARY}
        ${ABSL_FLAGS_MARSHALLING_LIBRARY}
        ${ABSL_LOG_SINK_LIBRARY}
        ${ABSL_CRC32C_LIBRARY}
        ${ABSL_RANDOM_INTERNAL_DISTRIBUTION_TEST_UTIL_LIBRARY}
        ${ABSL_RANDOM_INTERNAL_POOL_URBG_LIBRARY}
        ${ABSL_LOG_INTERNAL_GLOBALS_LIBRARY}
        ${ABSL_KERNEL_TIMEOUT_INTERNAL_LIBRARY}
        ${ABSL_FAILURE_SIGNAL_HANDLER_LIBRARY}
        ${ABSL_LOG_ENTRY_LIBRARY}
        ${ABSL_EXAMINE_STACK_LIBRARY}
        ${ABSL_FLAGS_PRIVATE_HANDLE_ACCESSOR_LIBRARY}
        ${ABSL_TIME_LIBRARY}
        ${ABSL_RANDOM_INTERNAL_SEED_MATERIAL_LIBRARY}
        ${ABSL_STR_FORMAT_INTERNAL_LIBRARY}
        ${ABSL_HASH_LIBRARY}
        ${ABSL_SYMBOLIZE_LIBRARY}
        ${ABSL_LOG_INTERNAL_PROTO_LIBRARY}
        ${ABSL_RANDOM_DISTRIBUTIONS_LIBRARY}
        ${ABSL_LOG_INTERNAL_FNMATCH_LIBRARY}
        ${ABSL_FLAGS_COMMANDLINEFLAG_LIBRARY}
        ${ABSL_STRINGS_LIBRARYS}
        ${ABSL_GRAPHCYCLES_INTERNAL_LIBRARY}
        ${ABSL_CRC_INTERNAL_LIBRARY}
        ${ABSL_POISON_LIBRARY}
        ${ABSL_MALLOC_INTERNAL_LIBRARY}
        ${ABSL_STRING_VIEW_LIBRARY}
        ${ABSL_STRINGS_INTERNAL_LIBRARY}
        ${ABSL_LOW_LEVEL_HASH_LIBRARY}
        ${ABSL_CITY_LIBRARY}
        ${ABSL_CRC_CPU_DETECT_LIBRARY}
        ${ABSL_LOG_INTERNAL_CONDITIONS_LIBRARY}
        ${ABSL_BASE_LIBRARY}
        ${ABSL_DEMANGLE_INTERNAL_LIBRARY}
        ${ABSL_STACKTRACE_LIBRARY}
        ${ABSL_CORDZ_FUNCTIONS_LIBRARY}
        ${ABSL_RANDOM_INTERNAL_RANDEN_LIBRARY}
        ${ABSL_TIME_ZONE_LIBRARY}
        ${ABSL_THROW_DELEGATE_LIBRARY}
        ${ABSL_DEBUGGING_INTERNAL_LIBRARY}
        ${ABSL_BAD_ANY_CAST_IMPL_LIBRARY}
        ${ABSL_BAD_OPTIONAL_ACCESS_LIBRARY}
        ${ABSL_SCOPED_SET_ENV_LIBRARY}
        ${ABSL_BAD_VARIANT_ACCESS_LIBRARY}
        ${ABSL_DEMANGLE_RUST_LIBRARY}
        ${ABSL_RANDOM_INTERNAL_RANDEN_HWAES_LIBRARY}
        ${ABSL_RANDOM_INTERNAL_RANDEN_HWAES_IMPL_LIBRARY}
        ${ABSL_RAW_LOGGING_INTERNAL_LIBRARY}
        ${ABSL_RANDOM_INTERNAL_RANDEN_SLOW_LIBRARY}
        ${ABSL_CIVIL_TIME_LIBRARY}
        ${ABSL_DECODE_RUST_PUNYCODE_LIBRARY}
        ${ABSL_RANDOM_SEED_GEN_EXCEPTION_LIBRARY}
        ${ABSL_LOG_SEVERITY_LIBRARY}
        ${ABSL_PERIODIC_SAMPLER_LIBRARY}
        ${ABSL_STRERROR_LIBRARY}
        ${ABSL_EXPONENTIAL_BIASED_LIBRARY}
        ${ABSL_LOG_INTERNAL_NULLGUARD_LIBRARY}
        ${ABSL_SPINLOCK_WAIT_LIBRARY}
        ${ABSL_RANDOM_INTERNAL_PLATFORM_LIBRARY}
        ${ABSL_UTF8_FOR_CODE_POINT_LIBRARY}
        ${ABSL_INT128_LIBRARY}
        ${ABSL_LEAK_CHECK_LIBRARY}
        ${ABSL_FLAGS_COMMANDLINEFLAG_INTERNAL_LIBRARY}
    )
    find_library(AZURE_STORAGE_BLOBS_LIBRARY libazure-storage-blobs.a REQUIRED)
    find_library(AZURE_STORAGE_BLOBS_COMMON_LIBRARY libazure-storage-common.a REQUIRED)
    find_library(AZURE_CORE_LIBRARY libazure-core.a REQUIRED)
    find_library(GOOGLE_CLOUDE_STORAGE_LIBRARY libgoogle_cloud_cpp_storage.a REQUIRED)
    find_library(GOOGLE_CLOUDE_REST_INTERNAL_LIBRARY libgoogle_cloud_cpp_rest_internal.a REQUIRED)
    find_library(GOOGLE_CLOUDE_REST_COMMON_LIBRARY libgoogle_cloud_cpp_common.a REQUIRED)
endif()

if(NOT HDFS_DISABLE_BYTERPC)
    find_library(BYTERPC_LIBRARY libbyterpc.a REQUIRED)
    find_library(BYTE_LIBRARY libbyte.a REQUIRED)
    find_library(BYTE_EXPRESS_LIBRARY libbyte_express.a REQUIRED)
    find_library(BYTELOG_LIBRARY libbytelog.a REQUIRED)
    find_library(BRPC_LIBRARY libbrpc.a REQUIRED)
    find_library(BEMALLOC_UC_LIBRARY libbemalloc_uc.a REQUIRED)
    find_library(RDMACM_LIBRARY librdmacm.a REQUIRED)
    find_library(IBVERBS_LIBRARY libibverbs.a REQUIRED)
    find_library(VERTO_LIBRARY libverto.a REQUIRED)
    find_library(MSGPACK_LIBRARY libmsgpackc.a REQUIRED)
endif()

if(HDFS_WITH_JEMALLOC)
    find_library(JEMALLOC_LIBRARY libjemalloc.a REQUIRED)
endif()

if(HDFS_BUILD_FUSE)
    find_library(FUSE3_LIBRARY libfuse3.a REQUIRED)
endif()

find_library(UNWIND_LIBRARY libunwind.a REQUIRED)
find_library(UNWIND_GENERIC_LIBRARY libunwind-generic.a REQUIRED)
set(UNWIND_LIBRARIES ${UNWIND_LIBRARY} ${UNWIND_GENERIC_LIBRARY})

find_library(GSASL_LIBRARY libgsasl.a REQUIRED)

find_library(FOLLY_LIBRARY libfolly.a REQUIRED)
find_library(SYS_IBERTY_LIB iberty)
# TODO(dongbenchao) folly will link libiberty if it exists in the system.
# I don/t know how to disable libiberty when building folly.
if(SYS_IBERTY_LIB)
    set(FOLLY_LIBRARY ${FOLLY_LIBRARY} ${SYS_IBERTY_LIB})
endif()

find_library(OPENSSL_SSL_LIBRARY libssl.a REQUIRED)
find_library(OPENSSL_CRYPTO_LIBRARY libcrypto.a REQUIRED)
set(OPENSSL_LIBRARIES ${OPENSSL_SSL_LIBRARY} ${OPENSSL_CRYPTO_LIBRARY})

find_library(BOOST_CONTEXT_LIBRARY libboost_context.a REQUIRED)
find_library(BOOST_REGEX_LIBRARY libboost_regex.a REQUIRED)
find_library(BOOST_FILESYSTEM_LIBRARY libboost_filesystem.a REQUIRED)
find_library(BOOST_THREAD_LIBRARY libboost_thread.a REQUIRED)
set(BOOST_LIBRARIES
    ${BOOST_CONTEXT_LIBRARY}
    ${BOOST_REGEX_LIBRARY}
    ${BOOST_FILESYSTEM_LIBRARY}
    ${BOOST_THREAD_LIBRARY}
    )

find_library(KRB5_LIBRARY NAMES libkrb5.a)
find_library(KRB5_OPT_LIBRARY NAMES libkrb5_otp.a)
find_library(KRB5_DB2LIBRARY NAMES libkrb5_db2.a)
find_library(KRB5_K5TLS_LIBRARY NAMES libkrb5_k5tls.a)
find_library(SSAPI_KRB5_LIBRARY NAMES libgssapi_krb5.a)
find_library(K5CRYPTO_LIBRARY NAMES libk5crypto.a)
find_library(KRB5_SUPPORT_LIBRARY NAMES libkrb5support.a)
find_library(COM_ERR_LIBRARY NAMES libcom_err.a)
set(KRB5_LIBRARIES
    ${SSAPI_KRB5_LIBRARY}
    ${KRB5_LIBRARY}
    ${KRB5_OPT_LIBRARY}
    ${KRB5_DB2LIBRARY}
    ${KRB5_K5TLS_LIBRARY}
    ${K5CRYPTO_LIBRARY}
    ${KRB5_SUPPORT_LIBRARY}
    ${COM_ERR_LIBRARY}
)
# Must use find_package here because spdlogConfigTargets.cmake will add
# target_compile_definitions of SPDLOG_COMPILED_LIB to the targets who links
# to spdlog::spdlog. This definitions will use the static-built libspdlog.a
# but not header-only. See
# https://github.com/gabime/spdlog/blob/v1.x/example/CMakeLists.txt
# for details.
find_package(spdlog REQUIRED)

# system static/shared libs
find_library(RESOLV_LIBRARY libresolv.so REQUIRED)

# bytecool related
find_library(ISAL_LIBRARY libisal.a REQUIRED)
list(APPEND HDFS_DEP_LIBS ${ISAL_LIBRARY})

if(NOT HDFS_DISABLE_IO_MONITOR)
    find_library(ENV_LIBRARY libenv.a REQUIRED)
    find_library(APM_VENDOR_LIBRARY libapm_ingress_vendor.a REQUIRED)
    find_library(MONITORING_LIBRARY libmonitoring-codec.a REQUIRED)
    find_library(APM_METRICS_LIBRARY libapm_ingress_metrics.a REQUIRED)
    find_library(METRICS_SDK_LIBRARY libmetrics_sdk.a REQUIRED)
    find_library(METRICS2_LIBRARY libmetrics2.a REQUIRED)
    find_library(DATABUS_PROTO_LIBRARY libdatabus_proto.a REQUIRED)
    find_library(DATABUS_SDK_LIBRARY libdatabus.a REQUIRED)
    find_library(BYTELOG_LIBRARY libbytelog.a REQUIRED)
    find_library(INFSEC_LIBRARY libinfsec.a REQUIRED)

    list(APPEND HDFS_DEP_LIBS
        ${DATABUS_SDK_LIBRARY}
        ${DATABUS_PROTO_LIBRARY}
        ${BYTELOG_LIBRARY}
        ${METRICS2_LIBRARY}
        ${INFSEC_LIBRARY}
        ${METRICS_SDK_LIBRARY}
        ${APM_METRICS_LIBRARY}
        ${MONITORING_LIBRARY}
        ${APM_VENDOR_LIBRARY}
        ${ENV_LIBRARY}
    )
endif()

if(NOT HDFS_DISABLE_THIRDPARTY_STORAGE)
    find_library(ABSL_FLAGS_COMMANDLINEFLAG_INTERNAL_LIBRARY libabsl_flags_commandlineflag_internal.a REQUIRED)
    find_library(ABSL_LEAK_CHECK_LIBRARY libabsl_leak_check.a REQUIRED)
    find_library(ABSL_UTF8_FOR_CODE_POINT_LIBRARY libabsl_utf8_for_code_point.a REQUIRED)
    find_library(ABSL_RANDOM_INTERNAL_PLATFORM_LIBRARY libabsl_random_internal_platform.a REQUIRED)
    find_library(ABSL_SPINLOCK_WAIT_LIBRARY libabsl_spinlock_wait.a REQUIRED)
    find_library(ABSL_LOG_INTERNAL_NULLGUARD_LIBRARY libabsl_log_internal_nullguard.a REQUIRED)
    find_library(ABSL_EXPONENTIAL_BIASED_LIBRARY libabsl_exponential_biased.a REQUIRED)
    find_library(ABSL_STRERROR_LIBRARY libabsl_strerror.a REQUIRED)
    find_library(ABSL_PERIODIC_SAMPLER_LIBRARY libabsl_periodic_sampler.a REQUIRED)
    find_library(ABSL_LOG_SEVERITY_LIBRARY libabsl_log_severity.a REQUIRED)
    find_library(ABSL_RANDOM_SEED_GEN_EXCEPTION_LIBRARY libabsl_random_seed_gen_exception.a REQUIRED)
    find_library(ABSL_DECODE_RUST_PUNYCODE_LIBRARY libabsl_decode_rust_punycode.a REQUIRED)
    find_library(ABSL_CIVIL_TIME_LIBRARY libabsl_civil_time.a REQUIRED)
    find_library(ABSL_RANDOM_INTERNAL_RANDEN_SLOW_LIBRARY libabsl_random_internal_randen_slow.a REQUIRED)
    find_library(ABSL_INT128_LIBRARY libabsl_int128.a REQUIRED)
    find_library(ABSL_RAW_LOGGING_INTERNAL_LIBRARY libabsl_raw_logging_internal.a REQUIRED)
    find_library(ABSL_RANDOM_INTERNAL_RANDEN_HWAES_IMPL_LIBRARY libabsl_random_internal_randen_hwaes_impl.a REQUIRED)
    find_library(ABSL_RANDOM_INTERNAL_RANDEN_HWAES_LIBRARY libabsl_random_internal_randen_hwaes.a REQUIRED)
    find_library(ABSL_DEMANGLE_RUST_LIBRARY libabsl_demangle_rust.a REQUIRED)
    find_library(ABSL_BAD_VARIANT_ACCESS_LIBRARY libabsl_bad_variant_access.a REQUIRED)
    find_library(ABSL_SCOPED_SET_ENV_LIBRARY libabsl_scoped_set_env.a REQUIRED)
    find_library(ABSL_BAD_OPTIONAL_ACCESS_LIBRARY libabsl_bad_optional_access.a REQUIRED)
    find_library(ABSL_BAD_ANY_CAST_IMPL_LIBRARY libabsl_bad_any_cast_impl.a REQUIRED)
    find_library(ABSL_DEBUGGING_INTERNAL_LIBRARY libabsl_debugging_internal.a REQUIRED)
    find_library(ABSL_THROW_DELEGATE_LIBRARY libabsl_throw_delegate.a REQUIRED)
    find_library(ABSL_TIME_ZONE_LIBRARY libabsl_time_zone.a REQUIRED)
    find_library(ABSL_RANDOM_INTERNAL_RANDEN_LIBRARY libabsl_random_internal_randen.a REQUIRED)
    find_library(ABSL_CORDZ_FUNCTIONS_LIBRARY libabsl_cordz_functions.a REQUIRED)
    find_library(ABSL_STACKTRACE_LIBRARY libabsl_stacktrace.a REQUIRED)
    find_library(ABSL_DEMANGLE_INTERNAL_LIBRARY libabsl_demangle_internal.a REQUIRED)
    find_library(ABSL_BASE_LIBRARY libabsl_base.a REQUIRED)
    find_library(ABSL_LOG_INTERNAL_CONDITIONS_LIBRARY libabsl_log_internal_conditions.a REQUIRED)
    find_library(ABSL_CRC_CPU_DETECT_LIBRARY libabsl_crc_cpu_detect.a REQUIRED)
    find_library(ABSL_CITY_LIBRARY libabsl_city.a REQUIRED)
    find_library(ABSL_LOW_LEVEL_HASH_LIBRARY libabsl_low_level_hash.a REQUIRED)
    find_library(ABSL_STRINGS_INTERNAL_LIBRARY libabsl_strings_internal.a REQUIRED)
    find_library(ABSL_STRING_VIEW_LIBRARY libabsl_string_view.a REQUIRED)
    find_library(ABSL_MALLOC_INTERNAL_LIBRARY libabsl_malloc_internal.a REQUIRED)
    find_library(ABSL_POISON_LIBRARY libabsl_poison.a REQUIRED)
    find_library(ABSL_CRC_INTERNAL_LIBRARY libabsl_crc_internal.a REQUIRED)
    find_library(ABSL_GRAPHCYCLES_INTERNAL_LIBRARY libabsl_graphcycles_internal.a REQUIRED)
    find_library(ABSL_STRINGS_LIBRARY libabsl_strings.a REQUIRED)
    find_library(ABSL_FLAGS_COMMANDLINEFLAG_LIBRARY libabsl_flags_commandlineflag.a REQUIRED)
    find_library(ABSL_LOG_INTERNAL_FNMATCH_LIBRARY libabsl_log_internal_fnmatch.a REQUIRED)
    find_library(ABSL_RANDOM_DISTRIBUTIONS_LIBRARY libabsl_random_distributions.a REQUIRED)
    find_library(ABSL_LOG_INTERNAL_PROTO_LIBRARY libabsl_log_internal_proto.a REQUIRED)
    find_library(ABSL_SYMBOLIZE_LIBRARY libabsl_symbolize.a REQUIRED)
    find_library(ABSL_HASH_LIBRARY libabsl_hash.a REQUIRED)
    find_library(ABSL_STR_FORMAT_INTERNAL_LIBRARY libabsl_str_format_internal.a REQUIRED)
    find_library(ABSL_RANDOM_INTERNAL_SEED_MATERIAL_LIBRARY libabsl_random_internal_seed_material.a REQUIRED)
    find_library(ABSL_TIME_LIBRARY libabsl_time.a REQUIRED)
    find_library(ABSL_FLAGS_PRIVATE_HANDLE_ACCESSOR_LIBRARY libabsl_flags_private_handle_accessor.a REQUIRED)
    find_library(ABSL_EXAMINE_STACK_LIBRARY libabsl_examine_stack.a REQUIRED)
    find_library(ABSL_LOG_ENTRY_LIBRARY libabsl_log_entry.a REQUIRED)
    find_library(ABSL_FAILURE_SIGNAL_HANDLER_LIBRARY libabsl_failure_signal_handler.a REQUIRED)
    find_library(ABSL_KERNEL_TIMEOUT_INTERNAL_LIBRARY libabsl_kernel_timeout_internal.a REQUIRED)
    find_library(ABSL_LOG_INTERNAL_GLOBALS_LIBRARY libabsl_log_internal_globals.a REQUIRED)
    find_library(ABSL_RANDOM_INTERNAL_POOL_URBG_LIBRARY libabsl_random_internal_pool_urbg.a REQUIRED)
    find_library(ABSL_RANDOM_INTERNAL_DISTRIBUTION_TEST_UTIL_LIBRARY libabsl_random_internal_distribution_test_util.a REQUIRED)
    find_library(ABSL_CRC32C_LIBRARY libabsl_crc32c.a REQUIRED)
    find_library(ABSL_LOG_SINK_LIBRARY libabsl_log_sink.a REQUIRED)
    find_library(ABSL_FLAGS_MARSHALLING_LIBRARY libabsl_flags_marshalling.a REQUIRED)
    find_library(ABSL_RANDOM_SEED_SEQUENCES_LIBRARY libabsl_random_seed_sequences.a REQUIRED)
    find_library(ABSL_SYNCHRONIZATION_LIBRARY libabsl_synchronization.a REQUIRED)
    find_library(ABSL_LOG_INTERNAL_FORMAT_LIBRARY libabsl_log_internal_format.a REQUIRED)
    find_library(ABSL_CRC_CORD_STATE_LIBRARY libabsl_crc_cord_state.a REQUIRED)
    find_library(ABSL_FLAGS_PROGRAM_NAME_LIBRARY libabsl_flags_program_name.a REQUIRED)
    find_library(ABSL_HASHTABLEZ_SAMPLER_LIBRARY libabsl_hashtablez_sampler.a REQUIRED)
    find_library(ABSL_CORDZ_HANDLE_LIBRARY libabsl_cordz_handle.a REQUIRED)
    find_library(ABSL_VLOG_CONFIG_INTERNAL_LIBRARY libabsl_vlog_config_internal.a REQUIRED)
    find_library(ABSL_CORD_INTERNAL_LIBRARY libabsl_cord_internal.a REQUIRED)
    find_library(ABSL_FLAGS_CONFIG_LIBRARY libabsl_flags_config.a REQUIRED)
    find_library(ABSL_RAW_HASH_SET_LIBRARY libabsl_raw_hash_set.a REQUIRED)
    find_library(ABSL_LOG_GLOBALS_LIBRARY libabsl_log_globals.a REQUIRED)
    find_library(ABSL_CORDZ_INFO_LIBRARY libabsl_cordz_info.a REQUIRED)
    find_library(ABSL_LOG_INITIALIZE_LIBRARY libabsl_log_initialize.a REQUIRED)
    find_library(ABSL_FLAGS_INTERNAL_LIBRARY libabsl_flags_internal.a REQUIRED)
    find_library(ABSL_LOG_INTERNAL_LOG_SINK_SET_LIBRARY libabsl_log_internal_log_sink_set.a REQUIRED)
    find_library(ABSL_CORDZ_SAMPLE_TOKEN_LIBRARY libabsl_cordz_sample_token.a REQUIRED)
    find_library(ABSL_LOG_INTERNAL_MESSAGE_LIBRARY libabsl_log_internal_message.a REQUIRED)
    find_library(ABSL_CORD_LIBRARY libabsl_cord.a REQUIRED)
    find_library(ABSL_DIE_IF_NULL_LIBRARY libabsl_die_if_null.a REQUIRED)
    find_library(ABSL_LOG_INTERNAL_CHECK_OP_LIBRARY libabsl_log_internal_check_op.a REQUIRED)
    find_library(ABSL_STATUS_LIBRARY libabsl_status.a REQUIRED)
    find_library(ABSL_FLAGS_REFLECTION_LIBRARY libabsl_flags_reflection.a REQUIRED)
    find_library(ABSL_STATUSOR_LIBRARY libabsl_statusor.a REQUIRED)
    find_library(ABSL_LOG_FLAGS_LIBRARY libabsl_log_flags.a REQUIRED)
    find_library(ABSL_FLAGS_USAGE_INTERNAL_LIBRARY libabsl_flags_usage_internal.a REQUIRED)
    find_library(ABSL_FLAGS_USAGE_LIBRARY libabsl_flags_usage.a REQUIRED)
    find_library(ABSL_FLAGS_PARSE_LIBRARY libabsl_flags_parse.a REQUIRED)

    set(ABSL_STRINGS_LIBRARYS ${ABSL_STRINGS_LIBRARY} ${ABSL_STRINGS_INTERNAL_LIBRARY})

    set(ABSL_LIBRARY
        ${ABSL_FLAGS_PARSE_LIBRARY}
        ${ABSL_FLAGS_USAGE_LIBRARY}
        ${ABSL_FLAGS_USAGE_INTERNAL_LIBRARY}
        ${ABSL_LOG_FLAGS_LIBRARY}
        ${ABSL_STATUSOR_LIBRARY}
        ${ABSL_FLAGS_REFLECTION_LIBRARY}
        ${ABSL_STATUS_LIBRARY}
        ${ABSL_LOG_INTERNAL_CHECK_OP_LIBRARY}
        ${ABSL_DIE_IF_NULL_LIBRARY}
        ${ABSL_CORD_LIBRARY}
        ${ABSL_LOG_INTERNAL_MESSAGE_LIBRARY}
        ${ABSL_CORDZ_SAMPLE_TOKEN_LIBRARY}
        ${ABSL_LOG_INTERNAL_LOG_SINK_SET_LIBRARY}
        ${ABSL_FLAGS_INTERNAL_LIBRARY}
        ${ABSL_LOG_INITIALIZE_LIBRARY}
        ${ABSL_CORDZ_INFO_LIBRARY}
        ${ABSL_LOG_GLOBALS_LIBRARY}
        ${ABSL_RAW_HASH_SET_LIBRARY}
        ${ABSL_FLAGS_CONFIG_LIBRARY}
        ${ABSL_CORD_INTERNAL_LIBRARY}
        ${ABSL_VLOG_CONFIG_INTERNAL_LIBRARY}
        ${ABSL_CORDZ_HANDLE_LIBRARY}
        ${ABSL_HASHTABLEZ_SAMPLER_LIBRARY}
        ${ABSL_FLAGS_PROGRAM_NAME_LIBRARY}
        ${ABSL_CRC_CORD_STATE_LIBRARY}
        ${ABSL_LOG_INTERNAL_FORMAT_LIBRARY}
        ${ABSL_SYNCHRONIZATION_LIBRARY}
        ${ABSL_RANDOM_SEED_SEQUENCES_LIBRARY}
        ${ABSL_FLAGS_MARSHALLING_LIBRARY}
        ${ABSL_LOG_SINK_LIBRARY}
        ${ABSL_CRC32C_LIBRARY}
        ${ABSL_RANDOM_INTERNAL_DISTRIBUTION_TEST_UTIL_LIBRARY}
        ${ABSL_RANDOM_INTERNAL_POOL_URBG_LIBRARY}
        ${ABSL_LOG_INTERNAL_GLOBALS_LIBRARY}
        ${ABSL_KERNEL_TIMEOUT_INTERNAL_LIBRARY}
        ${ABSL_FAILURE_SIGNAL_HANDLER_LIBRARY}
        ${ABSL_LOG_ENTRY_LIBRARY}
        ${ABSL_EXAMINE_STACK_LIBRARY}
        ${ABSL_FLAGS_PRIVATE_HANDLE_ACCESSOR_LIBRARY}
        ${ABSL_TIME_LIBRARY}
        ${ABSL_RANDOM_INTERNAL_SEED_MATERIAL_LIBRARY}
        ${ABSL_STR_FORMAT_INTERNAL_LIBRARY}
        ${ABSL_HASH_LIBRARY}
        ${ABSL_SYMBOLIZE_LIBRARY}
        ${ABSL_LOG_INTERNAL_PROTO_LIBRARY}
        ${ABSL_RANDOM_DISTRIBUTIONS_LIBRARY}
        ${ABSL_LOG_INTERNAL_FNMATCH_LIBRARY}
        ${ABSL_FLAGS_COMMANDLINEFLAG_LIBRARY}
        ${ABSL_STRINGS_LIBRARYS}
        ${ABSL_GRAPHCYCLES_INTERNAL_LIBRARY}
        ${ABSL_CRC_INTERNAL_LIBRARY}
        ${ABSL_POISON_LIBRARY}
        ${ABSL_MALLOC_INTERNAL_LIBRARY}
        ${ABSL_STRING_VIEW_LIBRARY}
        ${ABSL_STRINGS_INTERNAL_LIBRARY}
        ${ABSL_LOW_LEVEL_HASH_LIBRARY}
        ${ABSL_CITY_LIBRARY}
        ${ABSL_CRC_CPU_DETECT_LIBRARY}
        ${ABSL_LOG_INTERNAL_CONDITIONS_LIBRARY}
        ${ABSL_BASE_LIBRARY}
        ${ABSL_DEMANGLE_INTERNAL_LIBRARY}
        ${ABSL_STACKTRACE_LIBRARY}
        ${ABSL_CORDZ_FUNCTIONS_LIBRARY}
        ${ABSL_RANDOM_INTERNAL_RANDEN_LIBRARY}
        ${ABSL_TIME_ZONE_LIBRARY}
        ${ABSL_THROW_DELEGATE_LIBRARY}
        ${ABSL_DEBUGGING_INTERNAL_LIBRARY}
        ${ABSL_BAD_ANY_CAST_IMPL_LIBRARY}
        ${ABSL_BAD_OPTIONAL_ACCESS_LIBRARY}
        ${ABSL_SCOPED_SET_ENV_LIBRARY}
        ${ABSL_BAD_VARIANT_ACCESS_LIBRARY}
        ${ABSL_DEMANGLE_RUST_LIBRARY}
        ${ABSL_RANDOM_INTERNAL_RANDEN_HWAES_LIBRARY}
        ${ABSL_RANDOM_INTERNAL_RANDEN_HWAES_IMPL_LIBRARY}
        ${ABSL_RAW_LOGGING_INTERNAL_LIBRARY}
        ${ABSL_RANDOM_INTERNAL_RANDEN_SLOW_LIBRARY}
        ${ABSL_CIVIL_TIME_LIBRARY}
        ${ABSL_DECODE_RUST_PUNYCODE_LIBRARY}
        ${ABSL_RANDOM_SEED_GEN_EXCEPTION_LIBRARY}
        ${ABSL_LOG_SEVERITY_LIBRARY}
        ${ABSL_PERIODIC_SAMPLER_LIBRARY}
        ${ABSL_STRERROR_LIBRARY}
        ${ABSL_EXPONENTIAL_BIASED_LIBRARY}
        ${ABSL_LOG_INTERNAL_NULLGUARD_LIBRARY}
        ${ABSL_SPINLOCK_WAIT_LIBRARY}
        ${ABSL_RANDOM_INTERNAL_PLATFORM_LIBRARY}
        ${ABSL_UTF8_FOR_CODE_POINT_LIBRARY}
        ${ABSL_INT128_LIBRARY}
        ${ABSL_LEAK_CHECK_LIBRARY}
        ${ABSL_FLAGS_COMMANDLINEFLAG_INTERNAL_LIBRARY}
    )

    set(AZURE_STORAGE_LIBRARY
        ${AZURE_STORAGE_BLOBS_LIBRARY}
        ${AZURE_STORAGE_BLOBS_COMMON_LIBRARY}
        ${AZURE_CORE_LIBRARY})
    set(GOOGLE_STORAGE_LIBRARY
        ${GOOGLE_CLOUDE_STORAGE_LIBRARY}
        ${GOOGLE_CLOUDE_REST_INTERNAL_LIBRARY}
        ${GOOGLE_CLOUDE_REST_COMMON_LIBRARY})
    list(APPEND HDFS_DEP_LIBS
        ${GOOGLE_STORAGE_LIBRARY}
        ${AZURE_STORAGE_LIBRARY})
endif()

if(NOT HDFS_DISABLE_BYTERPC)
    set(BYTERPC_LIBRARYS
        ${BYTERPC_LIBRARY}
        ${BYTE_LIBRARY}
        ${BYTE_EXPRESS_LIBRARY}
        ${BYTELOG_LIBRARY}
        ${BRPC_LIBRARY}
        ${BEMALLOC_UC_LIBRARY}
        ${RDMACM_LIBRARY}
        ${IBVERBS_LIBRARY}
        ${VERTO_LIBRARY}
        ${MSGPACK_LIBRARY})
    list(APPEND HDFS_DEP_LIBS
        ${BYTERPC_LIBRARYS})
endif()

list(APPEND HDFS_DEP_LIBS
    ${FOLLY_LIBRARY}
    ${PROTOBUF_LIBRARY}
    ${GSASL_LIBRARY}
    ${GLOG_LIBRARY}
    ${GFLAGS_LIBRARY}
    ${DOUBLE_CONVERSION_LIBRARY}
    ${LIBXML2_LIBRARY}
    ${CURL_LIBRARY}
    ${Z_LIBRARY}
    ${LZMA_LIBRARY}
    ${ZSTD_LIBRARY}
    ${FMT_LIBRARY}
    ${EVENT_LIBRARY}
    ${UNWIND_LIBRARIES}
    ${BOOST_LIBRARIES}
    ${OPENSSL_LIBRARIES}
    ${KRB5_LIBRARIES}
    ${UUID_LIBRARY}
    ${CONSUL_LIBRARY}
    spdlog::spdlog
    ${RESOLV_LIBRARY}
    ${NUMA_LIBRARY}
    ${CRC32_LIBRARY}
)

if(NOT HDFS_DISABLE_THIRDPARTY_STORAGE)
    list(APPEND HDFS_DEP_LIBS ${ABSL_LIBRARY})
endif()

list(APPEND HDFS_DEP_LIBS
    dl
    pthread
)

message(STATUS "HDFS_WITH_JEMALLOC: ${HDFS_WITH_JEMALLOC}")
if(HDFS_WITH_JEMALLOC)
    list(APPEND HDFS_DEP_LIBS ${JEMALLOC_LIBRARY})
endif()

# -lstdc++fs is needed for gcc < 9.1. For clang, it should be -lc++fs
if("${CMAKE_CXX_COMPILER_ID}" STREQUAL "GNU" AND
        CMAKE_CXX_COMPILER_VERSION VERSION_LESS 9.1)
    list(APPEND HDFS_DEP_LIBS stdc++fs)
elseif("${CMAKE_CXX_COMPILER_ID}" STREQUAL "Clang")
    list(APPEND HDFS_DEP_LIBS stdc++fs)
    list(APPEND HDFS_DEP_LIBS atomic)
endif()


if (CMAKE_HOST_SYSTEM_PROCESSOR MATCHES "x86_64")
    set(ARCH_SPECIAL_FLAGS "")
elseif (CMAKE_HOST_SYSTEM_PROCESSOR MATCHES "aarch64" OR CMAKE_HOST_SYSTEM_PROCESSOR MATCHES "arm64")
    set(ARCH_SPECIAL_FLAGS "-mcpu=generic+crc")
else()
    message(FATAL_ERROR "Fail to find correct architecture ${CMAKE_HOST_SYSTEM_PROCESSOR}")
endif()

set(CXX_COMMON_FLAGS "-g -Wall -Werror -fPIC -fno-omit-frame-pointer -Wno-sign-compare -Wno-unused-variable ${ARCH_SPECIAL_FLAGS}")
set(COMMON_LINKER_FLAGS "-no-pie -ldl -pthread")

if("${CMAKE_CXX_COMPILER_ID}" STREQUAL "Clang")
    if("${CLANG_HOME}" STREQUAL "")
        if(DEFINED ENV{CLANG_HOME})
            set(CLANG_HOME "$ENV{CLANG_HOME}")
        endif()
        message(STATUS "Use env clang_home $ENV{CLANG_HOME}")
        if("${CLANG_HOME}" STREQUAL "")
            message(FATAL_ERROR "CLANG_HOME should be specified!")
        endif()
    endif()
    message(STATUS "CLANG_HOME ${CLANG_HOME}")
    set(CXX_COMMON_FLAGS "${CXX_COMMON_FLAGS} -std=c++17 -Wno-attributes -Wno-inconsistent-missing-override -Wno-mismatched-tags -Wno-pessimizing-move -Wno-unused-private-field")
    set(COMMON_LINKER_FLAGS "${COMMON_LINKER_FLAGS} -fuse-ld=lld -Wno-unused-command-line-argument")
elseif("${CMAKE_CXX_COMPILER_ID}" STREQUAL "GNU")
    if(CMAKE_CXX_COMPILER_VERSION VERSION_LESS 8.3.0)
        message(FATAL_ERROR "GCC>=8.3.0 required!")
    endif()
    set(CXX_COMMON_FLAGS "${CXX_COMMON_FLAGS} -Wno-attributes")
    set(COMMON_LINKER_FLAGS "${COMMON_LINKER_FLAGS} -static-libstdc++ -static-libgcc -Wno-maybe-uninitialized")
endif()

include(cmake/cpu_features.cmake)
message(STATUS "Cpu Options: ${COMPILER_FLAGS}")

if(HDFS_ENABLE_ASAN)
    set(CXX_COMMON_FLAGS "${CXX_COMMON_FLAGS} -fsanitize=address,undefined")
    set(COMMON_LINKER_FLAGS "${COMMON_LINKER_FLAGS} -fsanitize=address,undefined")
endif()

if(HDFS_DISABLE_IO_MONITOR)
    set(CXX_COMMON_FLAGS "${CXX_COMMON_FLAGS} -DHDFS_DISABLE_IOMONITOR")
endif()

if(HDFS_DISABLE_THIRDPARTY_STORAGE)
    set(CXX_COMMON_FLAGS "${CXX_COMMON_FLAGS} -DDISABLE_THIRDPARTY_STORAGE")
endif()

if(HDFS_DISABLE_BYTERPC)
    set(CXX_COMMON_FLAGS "${CXX_COMMON_FLAGS} -DDISABLE_BYTERPC")
endif()

if(HDFS_ENABLE_COV)
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -fprofile-arcs -ftest-coverage")
endif()

set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} ${CXX_COMMON_FLAGS} ${COMPILER_FLAGS}")
set(CMAKE_EXE_LINKER_FLAGS "${CMAKE_EXE_LINKER_FLAGS} ${COMMON_LINKER_FLAGS}")
set(CMAKE_SHARED_LINKER_FLAGS "${CMAKE_SHARED_LINKER_FLAGS} ${COMMON_LINKER_FLAGS}")

message(STATUS "CMAKE_CXX_FLAGS: ${CMAKE_CXX_FLAGS}")
message(STATUS "CMAKE_EXE_LINKER_FLAGS: ${CMAKE_EXE_LINKER_FLAGS}")
message(STATUS "CMAKE_SHARED_LINKER_FLAGS: ${CMAKE_SHARED_LINKER_FLAGS}")

list(INSERT CMAKE_INCLUDE_PATH 0 ${CMAKE_PREFIX_PATH}/include)
list(INSERT CMAKE_LIBRARY_PATH 0 ${CMAKE_PREFIX_PATH}/lib)
list(INSERT CMAKE_PROGRAM_PATH 0 ${CMAKE_PREFIX_PATH}/bin)

message(STATUS "CMAKE_PREFIX_PATH: " ${CMAKE_PREFIX_PATH})
message(STATUS "CMAKE_INCLUDE_PATH: " ${CMAKE_INCLUDE_PATH})
message(STATUS "CMAKE_LIBRARY_PATH: " ${CMAKE_LIBRARY_PATH})
message(STATUS "CMAKE_PROGRAM_PATH: " ${CMAKE_PROGRAM_PATH})
message(STATUS "CMAKE_INSTALL_PREFIX: ${CMAKE_INSTALL_PREFIX}")

include_directories(SYSTEM ${CMAKE_INCLUDE_PATH})
include_directories(SYSTEM ${CMAKE_INCLUDE_PATH}/libxml2)

macro(hdfs_add_executable)
    cmake_parse_arguments(
        hdfs_exec                   # prefix
        ""                          # <options>
        "NAME"                      # <one_value_args>
        "SOURCES;OBJECTS;LIBRARIES" # <multi_value_args>
        ${ARGN}
        )

    add_executable(${hdfs_exec_NAME}
        ${hdfs_exec_SOURCES}
        ${hdfs_exec_OBJECTS}
        )

    target_include_directories(${hdfs_exec_NAME}
        PRIVATE ${CMAKE_SOURCE_DIR}/src
        PRIVATE ${CMAKE_SOURCE_DIR}/mock
        PRIVATE ${VERSION_HEADER_PATH}
        PRIVATE ${PROTO_DST_PATH}
        PRIVATE ${CMAKE_SOURCE_DIR}/third_party/bundled/include
        )

    target_link_libraries(${hdfs_exec_NAME}
        ${hdfs_exec_LIBRARIES}
        )
endmacro()

# enable ctest
enable_testing()

message(STATUS "Build submodules, CMD:${HDFS_BUILD_CMD}, FUSE:${HDFS_BUILD_FUSE}, TEST:${HDFS_BUILD_TEST}, ASAN:${HDFS_ENABLE_ASAN}, JEMALLOC:${HDFS_WITH_JEMALLOC}, BENCH:${HDFS_BUILD_BENCH}")

add_subdirectory(src)
if(HDFS_BUILD_CMD)
    add_subdirectory(cmd)
endif()
if(HDFS_BUILD_BENCH)
    add_subdirectory(bench)
endif()
if(HDFS_BUILD_TEST)
    add_subdirectory(test)
endif()
if(HDFS_BUILD_FUSE)
    add_subdirectory(fuse)
endif()
