#!/bin/bash

set -e -o pipefail

export http_proxy=http://sys-proxy-rd-relay.byted.org:8118 https_proxy=http://sys-proxy-rd-relay.byted.org:8118 no_proxy=.byted.org
if [ "$1" == "asan" ]; then
    echo "build with asan"
    ./build-thirdparty.sh --enable-jemalloc --build-from-source --enable-asan
else
    echo "build with no asan"
    ./build-thirdparty.sh --enable-jemalloc --build-from-source
fi

CUR_DIR=`dirname "$0"`
HDFS_HOME=`cd "$CUR_DIR"; pwd`

mkdir output
mv ${HDFS_HOME}/third_party/install/include output
mv ${HDFS_HOME}/third_party/install/lib output
mv ${HDFS_HOME}/third_party/install/bin output

export http_proxy="" https_proxy="" no_proxy=""