#!/bin/bash

set -e -x -o pipefail

usage() {
  echo "
Usage: $0 <options>
  Optional options:
    --enable-asan
      enable asan when building third party (only for folly for now).
    --use-clang
      use clang as the compiler instead of gcc.
    --enable-jemalloc
      use jemalloc
    --disable-fuse
      disable fuse libs
    --build-from-source
      build from source
    -h, --help
      Print this usage.
  "
}

CUR_DIR=`dirname "$0"`
HDFS_HOME=`cd "$CUR_DIR"; pwd`

OPTS=$(getopt \
  -n $0 \
  -o 'h' \
  -l 'enable-asan' \
  -l 'enable-jemalloc' \
  -l 'disable-fuse' \
  -l 'disable-third-storage' \
  -l 'disable-byterpc' \
  -l 'delete-build-unused' \
  -l 'use-clang' \
  -l 'build-from-source' \
  -l 'help' \
  -- "$@")

if [ $? != 0 ] ; then
  usage
  exit 1
fi

eval set -- "$OPTS"

ENABLE_ASAN=0
USE_CLANG=0
ENABLE_JEMALLOC=0
DISABLE_FUSE=0
HDFS_DISABLE_THIRDPARTY_STORAGE=0
HDFS_DISABLE_BYTERPC=0
BUILD_FROM_SOURCE=0

while true; do
  case "$1" in
    --enable-asan) ENABLE_ASAN=1 ; shift 1 ;;
    --enable-jemalloc) ENABLE_JEMALLOC=1 ; shift 1 ;;
    --disable-fuse) DISABLE_FUSE=1 ; shift 1 ;;
    --use-clang) USE_CLANG=1 ; shift 1 ;;
    --build-from-source) BUILD_FROM_SOURCE=1 ; shift 1 ;;
    --disable-third-storage) HDFS_DISABLE_THIRDPARTY_STORAGE=1 ; shift 1 ;;
    --disable-byterpc) HDFS_DISABLE_BYTERPC=1 ; shift 1 ;;
    -h|--help) usage ; exit 0 ;;
    --) shift ;  break ;;
    *) usage; exit 1 ;;
  esac
done

if [ ! -f /usr/bin/nasm ] && [ ! -f /usr/local/bin/nasm  ]; then
  if [ `whoami` = "root" ]; then
    apt-get update -q && apt-get install -q -y --no-install-recommends nasm
  else
    echo "ERROR: nasm not installed! Compilation terminated."
    exit 1
  fi
fi

CMAKE_BUILD_ARG="-DCMAKE_BUILD_TYPE=Release"

if [ ${ENABLE_ASAN} -eq 1  ]; then
  CMAKE_BUILD_ARG+=" -DENABLE_ASAN=ON"
fi

if [ ${ENABLE_JEMALLOC} -eq 1  ]; then
  CMAKE_BUILD_ARG+=" -DENABLE_JEMALLOC=ON"
fi

if [ ${USE_CLANG} -eq 1  ]; then
  CMAKE_BUILD_ARG+=" -DCMAKE_CXX_COMPILER_ID=Clang -DCMAKE_C_COMPILER=clang -DCMAKE_CXX_COMPILER=clang++"
  HDFS_DISABLE_THIRDPARTY_STORAGE=1
  HDFS_DISABLE_BYTERPC=1
fi

if [ ${HDFS_DISABLE_THIRDPARTY_STORAGE} -eq 1  ]; then
  CMAKE_BUILD_ARG+=" -DHDFS_DISABLE_THIRDPARTY_STORAGE=ON"
fi

if [ ${HDFS_DISABLE_BYTERPC} -eq 1  ]; then
  CMAKE_BUILD_ARG+=" -DHDFS_DISABLE_BYTERPC=ON"
fi

mkdir -p ${HDFS_HOME}/third_party/build
mkdir -p ${HDFS_HOME}/third_party/install/include
mkdir -p ${HDFS_HOME}/third_party/install/lib
mkdir -p ${HDFS_HOME}/third_party/downloads

cp -r ${HDFS_HOME}/third_party/bundled/include/* ${HDFS_HOME}/third_party/install/include

# download libfuse source code.
FUSE_DIR=${HDFS_HOME}/third_party/downloads/libfuse-bm/build.sh
if [ -f "$FUSE_DIR" ]; then
    echo "fuse already exist"
else
    if [ ${DISABLE_FUSE} -eq 0  ]; then
      git submodule update --init
      # for use special version libfuse, should not use the follow command
      #git submodule update --remote
    fi
fi

ARCH_NAME="$(uname -m)"
LDD_VERSION_OUT=$(ldd --version)
LDD_VERSION=$(echo "$LDD_VERSION_OUT" | head -n 1 | awk '{print $NF}' | awk -F"." '{print $2}')
LDD_TARGET=28

if [ $LDD_VERSION -lt $LDD_TARGET ]; then
  curl "http://luban-source.byted.org/repository/scm/api/v1/download_latest/?name=inf/hdfs/cpp_client_deps_debian9&type=online&arch=${ARCH_NAME}" -o ${HDFS_HOME}/third_party/downloads/hdfs_deps.tar.gz
  #debian9 need build from source
  BUILD_FROM_SOURCE=1
else
  curl "http://luban-source.byted.org/repository/scm/api/v1/download_latest/?name=inf/hdfs/cpp_client_deps&type=online&arch=${ARCH_NAME}" -o ${HDFS_HOME}/third_party/downloads/hdfs_deps.tar.gz
fi

tar -xf ${HDFS_HOME}/third_party/downloads/hdfs_deps.tar.gz -C ${HDFS_HOME}/third_party/install

if [ ${BUILD_FROM_SOURCE} -eq 0  ]; then
  echo "get thirdparty output from scm"
  if [ ${ENABLE_ASAN} -eq 1  ]; then
    curl "http://luban-source.byted.org/repository/scm/inf.hdfs.native_client_thirdparty_1.0.0.10.tar.gz" -o ${HDFS_HOME}/third_party/downloads/thirdparty_deps.tar.gz
  else
    curl "http://luban-source.byted.org/repository/scm/api/v1/download_latest/?name=inf/hdfs/native_client_thirdparty&type=online&arch=${ARCH_NAME}" -o ${HDFS_HOME}/third_party/downloads/thirdparty_deps.tar.gz
  fi
  tar -xf ${HDFS_HOME}/third_party/downloads/thirdparty_deps.tar.gz -C ${HDFS_HOME}/third_party/install
else
  # enable libfuse
  if [ ${DISABLE_FUSE} -eq 0  ]; then
    # build libfuse
    LIBFUSE_BM_DIR=${HDFS_HOME}/third_party/downloads/libfuse-bm
    pushd ${LIBFUSE_BM_DIR}
    bash build.sh
    popd
    # copy libfuse build output
    mkdir -p ${HDFS_HOME}/third_party/install/include/libfuse-bm
    cp ${LIBFUSE_BM_DIR}/output/include/fuse3/* ${HDFS_HOME}/third_party/install/include/libfuse-bm
    cp ${LIBFUSE_BM_DIR}/output/lib/*/libfuse3.a ${HDFS_HOME}/third_party/install/lib || true
    cp ${LIBFUSE_BM_DIR}/output/lib/libfuse3.a ${HDFS_HOME}/third_party/install/lib || true
  fi

  echo "build thirdparty output from source"
  pushd ${HDFS_HOME}/third_party/build
  cmake ${CMAKE_BUILD_ARG} ..
  make VERBOSE=1 -j16
  popd
fi
