#!/usr/bin/env python
# -*- coding: utf-8 -*-

import base
import sys
sys.path.append('./')

job = base.testjob.make_testjob('test_common')
job.local_makedirs('dir0/dir00')
job.local_init_testfile_mb('dir0/dir00/test.data.16m', 16)
local0md5 = job.md5('dir0/dir00/test.data.16m')
ret = job.hdfs_put_parallel('dir0', 'dir0', '32')
job.local_makedirs('down.dir')
ret = job.hdfs_get('dir0', 'down.dir')
hdfs0md5 = job.md5('down.dir/dir0/dir00/test.data.16m')

print(local0md5, hdfs0md5)
if local0md5 != hdfs0md5:
    raise Exception('file invalid')

job.local_makedirs('temp_dir')
job.local_init_testfile_mb('temp_dir/temp.8m', 8)
localtempmd5 = job.md5('temp_dir/temp.8m')
ret = job.hdfs_put_force('temp_dir/temp.8m', 'dir0/dir00/test.data.16m')
ret = job.hdfs_put_force('temp_dir/temp.8m', 'dir0/dir00/test.data.8m')
job.local_makedirs('new.down.dir')
ret = job.hdfs_get('dir0/dir00/test.data.16m', 'new.down.dir')
hdfs1md5 = job.md5('new.down.dir/test.data.16m')

job.hdfs_mv('temp_dir/not.exists.temp.8m',  'dir0/dir00/test.data.16m', False)
job.hdfs_mv('temp_dir/not.exists.temp.8m',  'dir0/dir00/test.data.16m', True)

if not job.hdfs_exists('dir0/dir00/test.data.8m'):
    raise Exception('file invalid')
job.hdfs_mv('dir0/dir00/test.data.8m',  'dir0/dir00/test.data.16m', False)
if not job.hdfs_exists('dir0/dir00/test.data.8m'):
    raise Exception('file invalid')
job.hdfs_mv('dir0/dir00/test.data.8m',  'dir0/dir00/test.data.16m', True)
if job.hdfs_exists('dir0/dir00/test.data.8m'):
    raise Exception('file invalid')
if not job.hdfs_exists('dir0/dir00/test.data.16m'):
    raise Exception('file invalid')

print(localtempmd5, hdfs1md5)
if localtempmd5 != hdfs1md5:
    raise Exception('file invalid')

job.clean_data()
print('test_common success')
