#pragma once

#include <string>

/**
 * A data structure represents the schema, authority, path of an uri.
 */
class HdfsPath {
   public:
    /*
     * Build a Path instance from a std::string
     */
    static HdfsPath From(std::string path_str) {
        std::string schema, authority, path;
        size_t slashIndex = path_str.find_first_of("://");
        if (slashIndex != std::string::npos && slashIndex > 0) {
            size_t colon = path_str.find_first_of(":");
            size_t start = colon + 1;
            schema = path_str.substr(0, colon);
            if (path_str.size() - start > 2) {
                size_t nextSlash = path_str.find("/", start + 2);
                size_t authEnd = nextSlash > 0 ? nextSlash : path_str.length();
                authority = path_str.substr(start + 2, authEnd - (start + 2));
                start = authEnd;
            }
            if (start != std::string::npos) {
                path = path_str.substr(start);
            }
        } else {
            path = path_str;
        }
        return HdfsPath(schema, authority, path);
    }

    HdfsPath(const std::string& schema, const std::string& authority,
             const std::string& path)
        : schema_(schema), authority_(authority), path_(path) {}

    const std::string schema() const { return schema_; }

    const std::string authority() const { return authority_; }

    const std::string path() const { return path_; }

   private:
    std::string schema_;
    std::string authority_;
    std::string path_;
};
