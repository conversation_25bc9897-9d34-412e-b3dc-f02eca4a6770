
#pragma once

#include <folly/Uri.h>
#include <folly/dynamic.h>
#include <stdint.h>

#include <cerrno>
#include <chrono>
#include <iostream>
#include <map>
#include <memory>
#include <mutex>
#include <string>
#include <vector>

#include "client/hdfs.h"
#include "util/Path.h"
#include "util/macros.h"

class NamenodeProxy;

namespace Hdfs {
namespace Internal {
class IObjectUploadJob {
   public:
    IObjectUploadJob() = default;
    virtual ~IObjectUploadJob() = default;

    // For uploading small object.
    virtual void Upload() = 0;

    // For uploading large objects, the derived class should maintain the upload
    // context internally and provide the following interfaces.
    // @remark Not thread-safe.
    virtual void StartMultipartUpload(size_t partSize, int parallel) = 0;

    // @remark Thread-safe only if different parallelIdx used in each thread.
    virtual bool UploadNextPart(int parallelIdx) = 0;

    // @remark This method is not thread-safe. It must be called from the same
    // thread as StartMultipartUpload().
    virtual void CompleteMultipartUpload() = 0;
    // @remark This method is not thread-safe. It must be called from the same
    // thread as StartMultipartUpload().
    virtual void AbortMultipartUpload() = 0;

    virtual void SetTimeout(std::chrono::seconds timeout) {}
    virtual void SetForceCrc64Verify(bool newValue) {}

    virtual std::string ShortDebugString() {
        return "IObjectUploadJob: not derived yet";
    }
};
using IObjectUploadJobPtr = std::shared_ptr<IObjectUploadJob>;

class BaseHdfsInputStream {
   public:
    BaseHdfsInputStream(hdfsFS fs, const std::string& path, int64_t offset,
                        int64_t length);
    virtual ~BaseHdfsInputStream();
    DISABLE_COPY_AND_MOVE(BaseHdfsInputStream);

    void OpenHdfsFile();

    void CloseHdfsFile();

    bool IsCorrupted() const { return isCorrupted_; }
    bool IsEOF() const { return isEOF_; }
    int64_t GetCurrentOffset() const { return curOffset_; }
    int64_t GetLength() const { return endOffset_ - startOffset_; }

    // Seek() moves curOffset_ to the target position, while checksumOffset_
    // remains unchanged.
    // @remark Not thread-safe.
    void Seek(int64_t offset);

    // Read() reads at most count bytes from the stream and returns the number
    // of bytes read. If the stream is at EOF, it returns 0. If an error occurs,
    // it returns -1 and sets errno.
    // @remark Not thread-safe.
    int64_t Read(uint8_t* buffer, size_t count);

    // ReadFully() reads exactly count bytes from the stream and returns the
    // number of bytes read. If the stream is at EOF, it returns 0. If an error
    // occurs, it returns -1 and sets errno.
    // @remark Not thread-safe.
    int64_t ReadFully(uint8_t* buffer, size_t count,
                      size_t requestSize = 1024 * 1024);

   protected:
    // Derived class impplements this method to update checksum when new
    // data arrives.
    virtual void updateChecksum(const uint8_t* buffer, size_t count);

// for unittest
#ifndef NDEBUG
    static bool isDataCorruptionErrorInjected();
    static void injectDataCorruption(bool injectError);
#endif

   protected:
    hdfsFS fs_ = nullptr;
    std::string path_;
    hdfsFile hdfsFile_ = nullptr;
    int64_t startOffset_;
    int64_t endOffset_;
    int64_t curOffset_;
    int64_t checksumOffset_;
    int64_t length_;
    bool isCorrupted_ = false;
    bool isEOF_ = false;
};

class BaseObjectUploadJob : public IObjectUploadJob {
   public:
    BaseObjectUploadJob(hdfsFS srcFS, const HdfsPath& srcUri,
                        const folly::Uri& dstUri, bool overwrite);
    virtual ~BaseObjectUploadJob();

    void Upload() override;

    void StartMultipartUpload(size_t partSize, int parallel) override;

    bool UploadNextPart(int parallelIdx) override;

    void CompleteMultipartUpload() override;

    void AbortMultipartUpload() override;

    // Dump a json-formatted report of job status.
    std::string ShortDebugString() override;

    void SetForceCrc64Verify(bool newValue) override {
        forceCrc64Verify_ = newValue;
    }

   protected:
    // Derived class must implements uploadImpl() to support simple Upload()
    // API.
    virtual void uploadImpl() {}

    // Derived class must implements the following methods to support parallel
    // upload.
    virtual void startMultipartUploadImpl(size_t partSize, int parallel) {}
    virtual void completeMultipartUploadImpl() {}
    virtual void abortMultipartUploadImpl() {}
    virtual bool uploadNextPartImpl(int parallelIdx) { return false; }

    // Helper functions.
    size_t getFileSizeFromHdfs() const;

   protected:
    // Input parameters.
    hdfsFS srcFS_;
    HdfsPath srcUri_;
    folly::Uri dstUri_;
    bool overwrite_ = false;
    bool forceCrc64Verify_ = true;
    size_t partSize_ = 0;
    int partNum_ = 0;
    int parallel_ = 0;
    // Internal states.
    size_t fileSize_ = 0;
    uint64_t fileCrc64_ = 0;
    std::chrono::time_point<std::chrono::steady_clock> startTime_;
    std::vector<std::shared_ptr<BaseHdfsInputStream>> inputStreams_;
    bool isUploadPartStarted_ = false;
    // Output parameters.
    folly::dynamic report_;
};

class ObjectUploader {
   public:
    ObjectUploader();
    ObjectUploader(bool verbosePrint, bool forceCrc64Verify,
                   bool tagCrc64IfPossible);
    ~ObjectUploader();
    DISABLE_COPY_AND_MOVE(ObjectUploader);

    /**
     * @brief Upload object from src to dst.
     *
     * @param src Source path, should start with "hdfs://".
     * @param dst Destination path, should start with supported object scheme,
     * such as "wasb://".
     * @param overwrite Overwrite the destination object if it exists.
     * @return int32_t 0 if success, otherwise error code.
     */
    void Upload(const std::string& src, const std::string& dst, bool overwrite,
                std::chrono::seconds timeout);

    /**
     * @brief Upload object from src to dst with multipart upload.
     *
     * @param src Source path, should start with "hdfs://".
     * @param dst Destination path, should start with supported object scheme,
     * such as "wasb://".
     * @param overwrite Overwrite the destination object if it exists.
     * @param partSize Part size for multipart upload.
     * @param parallel Parallel level for multipart upload.
     * @return int32_t 0 if success, otherwise error code.
     */
    void MultiPartUpload(const std::string& src, const std::string& dst,
                         bool overwrite, size_t partSize, int parallel,
                         std::chrono::seconds timeout);

    static bool IsSupportedObjectStorage(const std::string& path);

    constexpr static const char* kEnableParallelInputPerformanceEnvKey =
        "CPP_HDFS_ENABLE_PARALLEL_INPUT_PERFORMANCE";

   private:
    IObjectUploadJobPtr prepareUploadJob(const std::string& src,
                                         const std::string& dst,
                                         bool overwrite);

   private:
    bool verbosePrint_ = true;
    bool forceCrc64Verify_ = true;
    bool tagCrc64IfPossible_ = true;

    using JobFactoryFuncType = std::function<IObjectUploadJobPtr(
        hdfsFS, const HdfsPath&, const folly::Uri&, bool)>;
    static std::map<std::string, JobFactoryFuncType> sJobFactory_;

    std::mutex mutex_;
    std::map<std::string, std::unique_ptr<NamenodeProxy>> nnproxy_;
};
}  // namespace Internal
}  // namespace Hdfs
