#pragma once

#include <fmt/chrono.h>
#include <fmt/format.h>

#include <algorithm>
#include <chrono>
#include <condition_variable>
#include <ctime>
#include <exception>
#include <filesystem>
#include <iostream>
#include <memory>
#include <sstream>
#include <thread>
#include <unordered_map>

#include "client/hdfs.h"
#include "common/Exception.h"
#include "common/ExceptionInternal.h"

extern bool show_progress;
extern uint64_t total_exec_size;
extern std::atomic<uint64_t> exec_size;

extern const char* CPP_HDFS_SHOW_CMD_PROGRESS_KEY;
extern bool enable_show_progress();
extern const std::string kFileNotFoundException;
extern const std::string kNoSuchFileOrDirectory;

struct HdfsTestException : public std::exception {
    HdfsTestException() {}
    const char* what() const throw() { return ""; }
};

struct HdfsCmdException : public std::exception {
    HdfsCmdException(const char* err_str) : err_msg(err_str) {}
    const char* what() const throw() {
        if (err_msg.empty()) {
            return "HDFS cmd unknown exception";
        } else {
            return err_msg.c_str();
        }
    }
    std::string err_msg;
};

struct HdfsInnerErrorException : public std::exception {
    HdfsInnerErrorException(const char* err_str) : err_msg(err_str) {}
    const char* what() const throw() {
        if (err_msg.empty()) {
            return "HDFS cmd unknown exception";
        } else {
            return err_msg.c_str();
        }
    }
    std::string err_msg;
};

extern bool IsHdfsInnerError();

#define EXIT(...)                               \
    char err_buf[2048];                         \
    std::snprintf(err_buf, 2048, __VA_ARGS__);  \
    if (IsHdfsInnerError()) {                   \
        throw HdfsInnerErrorException(err_buf); \
    } else {                                    \
        throw HdfsCmdException(err_buf);        \
    }

#define INFO(...)                 \
    fprintf(stdout, __VA_ARGS__); \
    fprintf(stdout, "\n")

extern std::unordered_map<std::string, std::string> GlobalConfig;

extern std::pair<std::string, std::string> detachFileName(
    const std::string& full_path);

extern std::string convertPerm(short perm, int kind);

extern void formatDoubleDigit(int num, std::stringstream& ss);

extern std::string generateTimeReadable(time_t ts);

extern size_t roundUpToValue(size_t value, size_t base);

// This value corresponds to the size of default block size in seed (128MB).
const size_t kDefaultParallelPutChunkBytes = 128 * 1024 * 1024;

static inline int64_t GetCurTimestampMs() {
    auto time_now = std::chrono::system_clock::now().time_since_epoch();
    return std::chrono::duration_cast<std::chrono::milliseconds>(time_now)
        .count();
}

extern int64_t GetCurTimestampUs();

class StatMonitor {
   public:
    StatMonitor() {
        running_ = true;
        cron_worker_ = std::make_unique<std::thread>(&StatMonitor::Run, this);
        start_time_ms_ = GetCurTimestampMs();
    }
    ~StatMonitor() {
        if (running_) {
            int64_t cost_time_ms = GetCurTimestampMs() - start_time_ms_;
            Stop();
            EmitEndStatus(cost_time_ms, std::to_string(-1));
        }
        if (cron_worker_->joinable()) {
            cron_worker_->join();
        }
    }

    void SetPath(std::vector<std::string>& paths) {
        if (paths.empty()) {
            return;
        }

        if (paths.size() == 1) {
            src_path_ = paths[0];
        } else {
            int size = paths.size();
            for (int i = 0; i < size - 1; ++i) {
                src_path_ += paths[i] + " ";
            }
            dest_path_ = paths[size - 1];
        }
    }

    void SetPath(std::string path) { src_path_ = path; }

    void SetOperation(const std::string& op) { operation_ = op; }

    void SetOption(const std::string& option) { option_ = option; }

    void SetException(const std::string& exception) { exception_ = exception; }

    void AddSize(int64_t size) {
        running_size_ += size;
        total_size_ += size;
        if (show_progress && total_exec_size != 0) {
            std::unique_lock<std::mutex> lock(print_mutex_);
            exec_size += size;
            if (exec_size >=
                (next_show_progress_rate_ * total_exec_size / 100)) {
                std::cout << "cmd progress:"
                          << exec_size * 100 / total_exec_size << "%("
                          << (exec_size.load() >> 20) << "MB/"
                          << (total_exec_size >> 20) << "MB)" << std::endl;
                next_show_progress_rate_ += 1;
            }
        }
    }

    void Run() {
        auto emit_status_interval_s = std::chrono::seconds(30);
        while (running_.load()) {
            auto before = GetCurTimestampMs();
            std::unique_lock<std::mutex> lock(mutex_);
            auto status = notify_cv_.wait_for(lock, emit_status_interval_s);
            if (status == std::cv_status::no_timeout) {
                break;
            }
            EmitRunningStatus(GetCurTimestampMs() - before);
        }
    }

    void EmitRunningStatus(int64_t cost_time) {
        hdfsOperationStatus running_status;
        running_status.state = hdfsOperationState::RUNNING;
        running_status.start_time_ms = start_time_ms_;
        running_status.length = running_size_;
        running_size_ = 0;

        running_status.lantency = cost_time;
        running_status.src_path = src_path_.c_str();
        running_status.dest_path = dest_path_.c_str();
        running_status.operation = operation_.c_str();
        running_status.option = option_.c_str();

        running_status.code = "";
        running_status.exception = exception_.c_str();

        EmitStatus(&running_status);
    }

    void EmitEndStatus(int64_t cost_time_ms, std::string code) {
        hdfsOperationStatus end_status;
        end_status.state = hdfsOperationState::DONE;
        end_status.start_time_ms = start_time_ms_;
        end_status.length = total_size_;
        total_size_ = 0;

        end_status.lantency = cost_time_ms;
        end_status.src_path = src_path_.c_str();
        end_status.dest_path = dest_path_.c_str();
        end_status.operation = operation_.c_str();
        end_status.option = option_.c_str();

        end_status.code = code.c_str();
        end_status.exception = exception_.c_str();

        EmitStatus(&end_status);
    }

    void Stop() {
        running_ = false;
        std::unique_lock<std::mutex> lock(mutex_);
        notify_cv_.notify_one();
    }

    std::string GetSrcPath() { return src_path_; }
    std::string GetDestPath() { return dest_path_; }

    std::string GetOperation() { return operation_; }

   private:
    void EmitStatus(hdfsOperationStatus* status) { hdfsMonitorStatus(status); }

    std::mutex mutex_;
    std::condition_variable notify_cv_;

    std::atomic<bool> running_{false};
    std::unique_ptr<std::thread> cron_worker_;

    int64_t start_time_ms_;
    std::atomic<int64_t> running_size_{0};
    std::atomic<int64_t> total_size_{0};
    std::string src_path_;
    std::string dest_path_;
    std::string operation_;
    std::string option_;
    std::string code_;
    std::string exception_;

    uint64_t next_show_progress_rate_ = 0;
    std::mutex print_mutex_;
};

extern StatMonitor g_stat;
extern bool g_arnold_user;
extern const uint64_t k_min_parallel_chunk_size_mb;
extern const int k_max_parallel_thread_num;
extern const uint64_t k_posix_memalign_size;
extern const uint64_t k_hdfs_default_read_packet_size;

extern std::string GetErrnoString();

const char* GetFileType(tObjectKind kind);

enum FileSystemType {
    FST_TMPFS =
        0,  // open without dio, no need trucate, no need mem 4KB alignment
    FST_NVME = 1,  // open with dio, need trucate, need mem 4KB alignment
    FST_HDFS_FUSE =
        2,        // open with dio, no need trucate, no need mem 4KB alignment
    FST_HDD = 3,  // open with dio, need trucate, need mem 4KB alignment
    FST_UNKNOWN = 4
};

extern int open_for_write(const std::string& path, FileSystemType type,
                          bool create_file);

template <typename F>
struct ByteDefer {
    F f_;
    ByteDefer(F f) : f_(f) {}  // NOLINT(runtime/explicit)
    ~ByteDefer() { f_(); }
};

struct EnvironmentVariableSaver {
    explicit EnvironmentVariableSaver(const char* key) : key_(key) {
        char* value = ::getenv(key);
        if (value) {
            saved_ = value;
        } else {
            not_exist_ = true;
        }
    }

    ~EnvironmentVariableSaver() {
        if (not_exist_) {
            ::unsetenv(key_.c_str());
        } else {
            ::setenv(key_.c_str(), saved_.c_str(), 1);
        }
    }

    std::string key_;
    std::string saved_;
    bool not_exist_ = false;
};

extern FileSystemType String2FileSystemType(const std::string& device);
extern std::string FileSystemType2String(FileSystemType type);

extern void InitPathFileSystemMap();
extern FileSystemType GetPathFileSystemType(std::string& exist_path);
extern std::map<std::string, std::string> g_path_to_filesystem_map;

extern bool need_direct_io(FileSystemType type);
extern bool need_trucate(FileSystemType type);
extern bool need_memory_alignment(FileSystemType type);

constexpr size_t round_up(size_t value, int bits) {
    size_t align = (1UL << bits);
    return (value + align - 1) & ~(align - 1);
}

inline std::string GetCurrentTimeInString() {
    return fmt::format("{:%Y-%m-%d %H:%M:%S}",
                       std::chrono::system_clock::now());
}
