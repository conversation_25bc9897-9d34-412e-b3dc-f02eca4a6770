/********************************************************************
 * 2014 -
 * open source under Apache License Version 2.0
 ********************************************************************/
/**
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
#ifndef _HDFS_LIBHDFS3_RPC_RPCCONTENTWRAPPER_H_
#define _HDFS_LIBHDFS3_RPC_RPCCONTENTWRAPPER_H_

#include <google/protobuf/message.h>

#include "common/WriteBuffer.h"

namespace Hdfs {
namespace Internal {

class RpcContentWrapper {
   public:
    RpcContentWrapper(::google::protobuf::Message* header,
                      ::google::protobuf::Message* msg);

    int getLength();
    void writeTo(WriteBuffer& buffer);

   public:
    ::google::protobuf::Message* header;
    ::google::protobuf::Message* msg;
};

}  // namespace Internal
}  // namespace Hdfs

#endif /* _HDFS_LIBHDFS3_RPC_RPCCONTENTWRAPPER_H_ */
