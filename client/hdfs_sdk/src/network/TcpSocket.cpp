/********************************************************************
 * 2014 -
 * open source under Apache License Version 2.0
 ********************************************************************/
/**
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
#include "TcpSocket.h"

#include <arpa/inet.h>
#include <errno.h>
#include <fcntl.h>
#include <fmt/format.h>
#include <netdb.h>
#include <netinet/in.h>
#include <netinet/tcp.h>
#include <poll.h>
#include <stdint.h>
#include <sys/socket.h>
#include <sys/types.h>
#include <unistd.h>

#include <climits>
#include <cstring>

#include "Syscall.h"
#include "client/CoreLogRecords.h"
#include "common/DateTime.h"
#include "common/Exception.h"
#include "common/ExceptionInternal.h"
#include "common/IPv6Util.h"
#include "common/Logger.h"
#include "common/assert.h"
#include "monitor/macro.h"
#include "platform.h"

namespace Hdfs {
namespace Internal {

TcpSocketImpl::TcpSocketImpl(std::string path)
    : path_(path), sock(-1), lingerTimeout(-1) {}

TcpSocketImpl::~TcpSocketImpl() { close(); }

int32_t TcpSocketImpl::read(char* buffer, int32_t size) {
    hdfs_assert(-1 != sock);
    hdfs_assert(NULL != buffer && size > 0);
    int32_t rc;

    do {
        rc = HdfsSystem::recv(sock, buffer, size, 0);
    } while (-1 == rc && EINTR == errno && !CheckOperationCanceled());

    if (-1 == rc) {
        THROW(HdfsNetworkException, "Read %d bytes failed from %s: %s", size,
              remoteAddr_.c_str(), GetSystemErrorInfo(errno));
    }

    if (0 == rc) {
        THROW(HdfsEndOfStream,
              "Read %d bytes failed from %s: End of the stream error:%s", size,
              remoteAddr_.c_str(), GetSystemErrorInfo(errno));
    }

    HdfsEnv::Get()->GetStatsMonitor()->RecordSocketBytes(rc,
                                                         BytesMode::TcpRead);

    return rc;
}

void TcpSocketImpl::readFully(char* buffer, int32_t size, int timeout) {
    hdfs_assert(-1 != sock);
    hdfs_assert(NULL != buffer && size > 0);
    int32_t todo = size, rc = 0;
    int deadline = timeout;

    // IOMonitorTraceApi(hdfsEvTypTcpSocketRead, path_.data());

    while (todo > 0) {
        std::chrono::steady_clock::time_point s =
            std::chrono::steady_clock::now();
        CheckOperationCanceled();

        if (poll(true, false, deadline)) {
            rc = read(buffer + (size - todo), todo);
            todo -= rc;
            // iomonitor_trace->data_size_ += rc;
        }

        std::chrono::steady_clock::time_point e =
            std::chrono::steady_clock::now();
        if (switch_read_strategy_ && rc > 0) {
            switch_read_strategy_->recordThroughput(rc, s, e);
            rc = 0;
        }

        if (timeout > 0) {
            deadline -= ToMilliSeconds(s, e);
        }

        if (todo > 0 && timeout >= 0 && deadline <= 0) {
            HDFSLOG(INFO, "ReadTimeout: current ReadTimeout = {} ms.", timeout);
            THROW(HdfsTimeoutException, "Read %d bytes timeout from %s", size,
                  remoteAddr_.c_str());
        }
    }

    // IOMonitorTraceApiSuccess();
}

int32_t TcpSocketImpl::write(const char* buffer, int32_t size) {
    hdfs_assert(-1 != sock);
    hdfs_assert(NULL != buffer && size > 0);
    int32_t rc;

    do {
#ifdef MSG_NOSIGNAL  // on linux
        rc = HdfsSystem::send(sock, buffer, size, MSG_NOSIGNAL);
#else
        rc = HdfsSystem::send(sock, buffer, size, 0);
#endif
    } while (-1 == rc && EINTR == errno && !CheckOperationCanceled());

    if (-1 == rc) {
        THROW(HdfsNetworkException, "Write %d bytes failed to %s: %s", size,
              remoteAddr_.c_str(), GetSystemErrorInfo(errno));
    }

    HdfsEnv::Get()->GetStatsMonitor()->RecordSocketBytes(rc,
                                                         BytesMode::TcpWrite);

    return rc;
}

void TcpSocketImpl::writeFully(const char* buffer, int32_t size, int timeout) {
    hdfs_assert(-1 != sock);
    hdfs_assert(NULL != buffer && size > 0);
    int32_t todo = size, rc;
    int deadline = timeout;

    // IOMonitorTraceApi(hdfsEvTypTcpSocketWrite, path_.data());

    while (todo > 0) {
        std::chrono::steady_clock::time_point s =
            std::chrono::steady_clock::now();
        CheckOperationCanceled();

        if (poll(false, true, deadline)) {
            rc = write(buffer + (size - todo), todo);
            todo -= rc;
            // iomonitor_trace->data_size_ += rc;
        }

        std::chrono::steady_clock::time_point e =
            std::chrono::steady_clock::now();

        if (timeout > 0) {
            deadline -= ToMilliSeconds(s, e);
        }

        if (todo > 0 && timeout >= 0 && deadline <= 0) {
            HDFSLOG(INFO, "WriteTimeout: current WriteTimeout = {} ms.",
                    timeout);
            THROW(HdfsTimeoutException, "Write %d bytes timeout to %s", size,
                  remoteAddr_.c_str());
        }
    }

    // IOMonitorTraceApiSuccess();
}

void TcpSocketImpl::connect(const char* host, int port, int timeout) {
    connect(host, std::to_string(port).c_str(), timeout);
}

void TcpSocketImpl::connect(const char* host, const char* port, int timeout) {
    hdfs_assert(-1 == sock);
    struct addrinfo hints, *addrs, *paddr;
    memset(&hints, 0, sizeof(hints));
    hints.ai_family = PF_UNSPEC;
    hints.ai_socktype = SOCK_STREAM;

    std::string real_addr(host);
    const char* replace_ipv6_env = getenv(kIPv6ReplaceEnableKey);
    if (replace_ipv6_env != nullptr) {
        std::string replace_ipv6_env_str(replace_ipv6_env);
        if ((replace_ipv6_env_str == "true" || replace_ipv6_env_str == "1")) {
            int family = getAddrFamily(real_addr);
            if (family == AF_INET6) {
                real_addr = getIPv4FromIPv6(real_addr);
                HDFSLOG(INFO, "TcpSocket replace IPv6 addr: {} to {}", host,
                        real_addr.c_str());
            } else {
                HDFSLOG(INFO, "TcpSocket skip IPv4 addr: {}", host);
            }
        }
    }

    int retval =
        HdfsSystem::getaddrinfo(real_addr.c_str(), port, &hints, &addrs);

    if (0 != retval) {
        THROW(HdfsNetworkConnectException,
              "Failed to resolve address \"%s:%s\" %s", real_addr.c_str(), port,
              gai_strerror(retval));
    }

    int deadline = timeout;
    remoteAddr_ = fmt::format("\"{}:{}\"", real_addr.c_str(), port);
    HdfsEnv::Get()->GetStatsMonitor()->NewSocket(remoteAddr_);

    try {
        for (paddr = addrs; NULL != paddr; paddr = paddr->ai_next) {
            std::chrono::steady_clock::time_point s =
                std::chrono::steady_clock::now();
            CheckOperationCanceled();

            try {
                connect(paddr, real_addr.c_str(), port, deadline);
            } catch (HdfsNetworkConnectException& e) {
                if (NULL == paddr->ai_next) {
                    throw;
                }
            } catch (HdfsTimeoutException& e) {
                if (NULL == paddr->ai_next) {
                    throw;
                }
            }

            if (-1 != sock) {
                HdfsSystem::freeaddrinfo(addrs);
                return;
            }

            std::chrono::steady_clock::time_point e =
                std::chrono::steady_clock::now();

            if (timeout > 0) {
                deadline -= ToMilliSeconds(s, e);
            }

            if (-1 == sock && timeout >= 0 && deadline <= 0) {
                HDFSLOG(INFO, "ConnectTimeout: current ConnectTimeout = {} ms.",
                        timeout);
                THROW(HdfsTimeoutException, "Connect to \"%s:%s\" timeout",
                      real_addr.c_str(), port);
            }
        }
    } catch (...) {
        HdfsSystem::freeaddrinfo(addrs);
        throw;
    }
}

void TcpSocketImpl::connect(struct addrinfo* paddr, const char* host,
                            const char* port, int timeout) {
    hdfs_assert(-1 == sock);
    sock = HdfsSystem::socket(paddr->ai_family, paddr->ai_socktype,
                              paddr->ai_protocol);

    if (-1 == sock) {
        THROW(HdfsNetworkException,
              "Create socket failed when connect to %s: %s",
              remoteAddr_.c_str(), GetSystemErrorInfo(errno));
    }

    if (lingerTimeout >= 0) {
        setLingerTimeoutInternal(lingerTimeout);
    }

    setRecvBufferInternal(recvBufferSize);

#ifdef __linux__
    /*
     * on linux some kernel use SO_SNDTIMEO as connect timeout.
     * It is OK to set a very large value here since the user has its own
     * timeout mechanism.
     */
    setSendTimeout(60000);
    /*
     * We use epoll to determine whether a socket is available, so the
     * SO_RCVTIMEO can be set smaller, mainly to prevent some internal errors
     * from causing recv to block all the time.
     */
    setRecvTimeout(15000);
#endif

    try {
        setBlockMode(false);
        disableSigPipe();
        int rc = 0;

        do {
            rc = HdfsSystem::connect(sock, paddr->ai_addr, paddr->ai_addrlen);
        } while (rc < 0 && EINTR == errno && !CheckOperationCanceled());

        if (rc < 0) {
            if (EINPROGRESS != errno && EWOULDBLOCK != errno) {
                if (ETIMEDOUT == errno) {
                    THROW(HdfsTimeoutException, "Connect to \"%s:%s\" timeout",
                          host, port);
                } else {
                    THROW(HdfsNetworkConnectException,
                          "Connect to \"%s:%s\" failed: %s", host, port,
                          GetSystemErrorInfo(errno));
                }
            }

            if (!poll(false, true, timeout)) {
                THROW(HdfsTimeoutException, "Connect to \"%s:%s\" timeout",
                      host, port);
            }

            struct sockaddr peer;

            unsigned int len = sizeof(peer);

            memset(&peer, 0, sizeof(peer));

            if (HdfsSystem::getpeername(sock, &peer, &len)) {
                /*
                 * connect failed, find out the error info.
                 */
                char c;
                rc = HdfsSystem::recv(sock, &c, 1, 0);
                hdfs_assert(rc < 0);

                if (ETIMEDOUT == errno) {
                    THROW(HdfsTimeoutException, "Connect to \"%s:%s\" timeout",
                          host, port);
                }

                THROW(HdfsNetworkConnectException,
                      "Connect to \"%s:%s\" failed: %s", host, port,
                      GetSystemErrorInfo(errno));
            }
        }

        setBlockMode(true);
    } catch (...) {
        close();
        throw;
    }
}

void TcpSocketImpl::setBlockMode(bool enable) {
    int flag;
    flag = HdfsSystem::fcntl(sock, F_GETFL, 0);

    if (-1 == flag) {
        THROW(HdfsNetworkException,
              "Get socket flag failed for remote node %s: %s",
              remoteAddr_.c_str(), GetSystemErrorInfo(errno));
    }

    flag = enable ? (flag & ~O_NONBLOCK) : (flag | O_NONBLOCK);

    if (-1 == HdfsSystem::fcntl(sock, F_SETFL, flag)) {
        THROW(HdfsNetworkException,
              "Set socket flag O_NONBLOCK failed for remote node %s: %s",
              remoteAddr_.c_str(), GetSystemErrorInfo(errno));
    }
}

void TcpSocketImpl::disableSigPipe() {
#ifdef SO_NOSIGPIPE /* only available on macos*/
    int flag = 1;

    if (HdfsSystem::setsockopt(sock, SOL_SOCKET, SO_NOSIGPIPE, (char*)&flag,
                               sizeof(flag))) {
        THROW(HdfsNetworkException,
              "Set socket flag failed for remote node %s: %s",
              remoteAddr.c_str(), GetSystemErrorInfo(errno));
    }

#endif
}

bool TcpSocketImpl::poll(bool read, bool write, int timeout) {
    hdfs_assert(-1 != sock);
    int rc;
    struct pollfd pfd;

    do {
        memset(&pfd, 0, sizeof(pfd));
        pfd.fd = sock;

        if (read) {
            pfd.events |= POLLIN;
        }

        if (write) {
            pfd.events |= POLLOUT;
        }
        rc = HdfsSystem::poll(&pfd, 1, timeout);
        if (closed_) {
            // fd is closed and set to -1 in close()
            close();
            HDFS_LOG(WARN, "Socket Interrupted: pfd is: {}. rc is {}.", pfd.fd,
                     rc);
            THROW(HdfsNetworkException,
                  "Socket for remote node %s is interrupted. ",
                  remoteAddr_.c_str());
        }
    } while (-1 == rc && EINTR == errno && !CheckOperationCanceled());

    if (-1 == rc) {
        THROW(HdfsNetworkException, "Poll failed for remote node %s: %s",
              remoteAddr_.c_str(), GetSystemErrorInfo(errno));
    }

    return 0 != rc;
}

void TcpSocketImpl::setNoDelay(bool enable) {
    hdfs_assert(-1 != sock);
    int flag = enable ? 1 : 0;

    if (HdfsSystem::setsockopt(sock, IPPROTO_TCP, TCP_NODELAY, (char*)&flag,
                               sizeof(flag))) {
        THROW(HdfsNetworkException,
              "Set socket flag failed for remote node %s: %s",
              remoteAddr_.c_str(), GetSystemErrorInfo(errno));
    }
}

void TcpSocketImpl::setLingerTimeout(int timeout) { lingerTimeout = timeout; }

void TcpSocketImpl::setRecvBuffer(int size) { recvBufferSize = size; }

void TcpSocketImpl::setLingerTimeoutInternal(int timeout) {
    hdfs_assert(-1 != sock);
    struct linger l;
    l.l_onoff = timeout > 0 ? true : false;
    l.l_linger = timeout > 0 ? timeout : 0;

    if (HdfsSystem::setsockopt(sock, SOL_SOCKET, SO_LINGER, &l, sizeof(l))) {
        THROW(HdfsNetworkException,
              "Set socket flag failed for remote node %s: %s",
              remoteAddr_.c_str(), GetSystemErrorInfo(errno));
    }
}

void TcpSocketImpl::setRecvBufferInternal(int size) {
    hdfs_assert(-1 != sock);

    if (size > 0) {
        if (HdfsSystem::setsockopt(sock, SOL_SOCKET, SO_RCVBUF,
                                   (const char*)&size, sizeof(size))) {
            THROW(HdfsNetworkException,
                  "Set socket flag failed for remote node %s: %s",
                  remoteAddr_.c_str(), GetSystemErrorInfo(errno));
        }
    }
}

void TcpSocketImpl::setSendTimeout(int timeMs) {
    hdfs_assert(-1 != sock);
    struct timeval timeo;
    timeo.tv_sec = timeMs / 1000;
    timeo.tv_usec = (timeMs % 1000) * 1000;

    if (HdfsSystem::setsockopt(sock, SOL_SOCKET, SO_SNDTIMEO, &timeo,
                               sizeof(timeo))) {
        THROW(HdfsNetworkException,
              "Set socket flag SO_SNDTIMEO failed for remote node %s: %s",
              remoteAddr_.c_str(), GetSystemErrorInfo(errno));
    }
}

void TcpSocketImpl::setRecvTimeout(int timeMs) {
    hdfs_assert(-1 != sock);
    struct timeval timeo;
    timeo.tv_sec = timeMs / 1000;
    timeo.tv_usec = (timeMs % 1000) * 1000;

    if (HdfsSystem::setsockopt(sock, SOL_SOCKET, SO_RCVTIMEO, &timeo,
                               sizeof(timeo))) {
        THROW(HdfsNetworkException,
              "Set socket flag SO_RCVTIMEO failed for remote node %s: %s",
              remoteAddr_.c_str(), GetSystemErrorInfo(errno));
    }
}

void TcpSocketImpl::close() {
    std::unique_lock<std::mutex> lk(mu_);
    if (-1 != sock) {
        HdfsSystem::shutdown(sock, SHUT_RDWR);
        HdfsSystem::close(sock);
        sock = -1;
        HdfsEnv::Get()->GetStatsMonitor()->DeleteSocket(remoteAddr_);
    }
}

}  // namespace Internal
}  // namespace Hdfs
