#include "infsec/InfSecClient.h"

#include <fmt/format.h>

#include <algorithm>
#include <cstring>
#include <fstream>
#include <iostream>
#include <iterator>
#include <vector>

#include "client/CoreLogRecords.h"
#include "common/Logger.h"
#include "common/StackPrinter.h"
#include "common/json.h"
#include "util.h"

namespace Hdfs {
namespace Internal {

bool InfSecClient::parseToken(std::string tokenStr, Identity& identity) {
    try {
        std::vector<std::string> tokenDict;
        split(tokenStr, tokenDict, '.');
        if (tokenDict.size() != 3) {
            return false;
        }
        std::string idjson = base64_decode(tokenDict.at(1));
        hdfsjson::json d;
        d = d.parse(idjson);
        if (d["psm"].is_string()) {
            identity.PSM = d["psm"];
        }
        if (d["version"].is_string()) {
            identity.Version = d["version"];
        }
        if (d["authority"].is_string()) {
            identity.Authority = d["authority"];
        }
        if (d["primaryAuthType"].is_string()) {
            identity.PrimaryAuthType = d["primaryAuthType"];
        }
        if (d["user"].is_string()) {
            identity.User = d["user"];
        }
        if (d["expireTime"].is_string()) {
            identity.ExpireTime = d["expireTime"];
        }
    } catch (std::exception& e) {
        HDFS_LOG(WARN, "parseToken failed {}", e.what());
    }
    return true;
}

std::string InfSecClient::getToken() {
    if (!this->secTokenStr.empty()) {
        return std::string(this->secTokenStr);
    }

    std::time_t now = std::time(nullptr);
    if (now > this->lastUpdateTime + 180) {
        pthread_rwlock_wrlock(&rw_mutex_);
        if (now > lastUpdateTime + 180) {
            lastUpdateTime = now;
            if (!this->secTokenPath.empty()) {
                std::ifstream tIF;
                tIF.open(this->secTokenPath);
                if (tIF.is_open()) {
                    char bufer[2048];
                    memset(bufer, 0, 2048);
                    tIF.read(bufer, 2048);
                    if (strlen(bufer) > 1) {
                        strcpy(this->secToken, bufer);
                        this->secToken[strlen(bufer)] = '\0';
                    }
                }
                tIF.close();
            }
        }
        pthread_rwlock_unlock(&rw_mutex_);
    }

    std::string token = "";
    pthread_rwlock_rdlock(&rw_mutex_);
    if (strlen(this->secToken) > 0) {
        token = std::string(this->secToken);
    }

    pthread_rwlock_unlock(&rw_mutex_);
    return token;
}

InfSecClient::InfSecClient() {
    memset(this->secToken, 0, sizeof(this->secToken));
    auto secTokenStr = getenv("SEC_TOKEN_STRING");
    if (secTokenStr != nullptr) {
        this->secTokenStr = std::string(secTokenStr);
    }
    auto secTokenPath = getenv("SEC_TOKEN_PATH");
    if (secTokenPath != nullptr) {
        this->secTokenPath = std::string(secTokenPath);
    }
    lastUpdateTime = 0;
    pthread_rwlock_init(&rw_mutex_, NULL);
}

InfSecClient::~InfSecClient() { pthread_rwlock_destroy(&rw_mutex_); }

std::string Identity::toString() {
    return fmt::format(
        "PSM:{} User:{} Version:{} Authority:{} "
        "PrimaryAuthType:{} ExpireTime:{}",
        this->PSM, this->User, this->Version, this->Authority,
        this->PrimaryAuthType, this->ExpireTime);
}
}  // namespace Internal
}  // namespace Hdfs
