#include "ResponseProcessor.h"

#include <cstdint>
#include <exception>

#include "DataReader.h"
#include "client/CoreLogRecords.h"
#include "hdfsAsyncContext.h"
#include "monitor/macro.h"

namespace Hdfs {
namespace Internal {
static const int kMaxPacketNumToCheckNet = 10;
ResponseProcessor::ResponseProcessor(
    std::shared_ptr<FileSystemInter> filesystem,
    std::shared_ptr<OutputStreamAsyncContext> context,
    std::shared_ptr<DataTransferProtocolSender> sender,
    std::shared_ptr<BufferedSocketReader> reader,
    std::shared_ptr<FastFailoverStrategy> strategy, int32_t read_timeout,
    int32_t get_additonal_node_max_time, std::string file_path,
    uint64_t file_id)
    : filesystem_(filesystem),
      ctx_(context),
      sender_(sender),
      reader_(reader),
      strategy_(strategy),
      read_timeout_(read_timeout),
      // get_additonal_node_max_time_(get_additonal_node_max_time),
      file_path_(file_path),
      file_id_(file_id) {
    FdTrace fd_trace = tl_fd_trace;
    receive_ack_loop_ = std::thread([&, fd_trace]() {
        tl_fd_trace = fd_trace;
        this->run();
    });
    SetThreadName(receive_ack_loop_, "hdfs_rsp_ack");
}

void ResponseProcessor::run() {
    char error_text[2048];
    sprintf(error_text, "for block %s.", ctx_->GetLastBlockString().c_str());

    int32_t ack_size = 0;
    PipelineAck ack;
    DataReader datareader(sender_.get(), reader_, read_timeout_);

    while (!responder_closed_ && !is_last_packet_in_block_) {
        // process responses from datanodes.
        IOMonitorTraceApi(hdfsEvTypReadAck, 0,
                          ctx_->last_block_->getLocationIps());
        try {
            // read an ack from the pipeline
            std::vector<char>& buf =
                datareader.readResponse(error_text, ack_size);

            ack.reset();
            ack.readFrom(&buf[0], ack_size);
            if (ack.isInvalid()) {
                THROW(
                    HdfsIOException,
                    "ProcessAcks: get an invalid DataStreamer packet ack for block %s",
                    ctx_->GetLastBlockString().c_str());
            }
            HDFSLOG(DEBUG, "Receive one ack. Seqno: {}", ack.getSeqno());
            int64_t seqno = ack.getSeqno();

            // processes response status from datanodes.
            for (int i = ack.getNumOfReplies() - 1; i >= 0; i--) {
                Status reply = ack.getReply(i);
                // Restart will not be treated differently unless it is
                // the local node or the only one in the pipeline.
                if (reply == Status::DT_PROTO_OOB_RESTART) {
                    ctx_->error_index_ = i;
                    THROW(
                        HdfsIOException,
                        "ProcessAcks: Datanode is restarting block %s. Index is %d",
                        ctx_->GetLastBlockString().c_str(), i);
                }

                // node error
                if (reply != Status::DT_PROTO_SUCCESS) {
                    // Equal to setErrorIndex(i);
                    ctx_->error_index_ = i;
                    THROW(HdfsIOException,
                          "Bad response for block %s. Datanode Index is %d",
                          ctx_->GetLastBlockString().c_str(), i);
                }
            }

            hdfs_assert(seqno != UNKOWN_SEQNO);

            if (seqno == HEART_BEAT_SEQNO) {  // a heartbeat ack
                IOMonitorTraceApiSuccess();
                continue;
            }

            // a success ack for a data packet
            std::shared_ptr<Packet> packet_to_be_acked;
            {
                std::unique_lock<std::mutex> lock(ctx_->data_queue_mutex_);
                packet_to_be_acked = ctx_->ack_queue_.front();
            }

            if (FailureMocker::current_mock_) {
                FailureMocker::current_mock_->mockWhenReceivePacket(
                    seqno, packet_to_be_acked->getOffsetInBlock());
            }

            if (packet_to_be_acked->getSeqno() != seqno) {
                THROW(HdfsIOException,
                      "ResponseProcessor: Expecting seqno %s. But received %s.",
                      std::to_string(packet_to_be_acked->getSeqno()).c_str(),
                      std::to_string(seqno).c_str());
            }

            is_last_packet_in_block_ =
                packet_to_be_acked->isLastPacketInBlock();

            std::chrono::time_point<std::chrono::steady_clock> now =
                std::chrono::steady_clock::now();

            if (packet_to_be_acked->get_packet_index_in_block() <=
                    kMaxPacketNumToCheckNet &&
                ack.get_down_stream_ack_time_nanos() != 0) {
                auto trace1 = HdfsEnv::Get()->GetIOMonitor()->TraceApi(
                    hdfsEvTypDataNodeWriteNet);
                trace1->SetCostUs(
                    ((now - packet_to_be_acked->getSendTimeNS()).count() -
                     ack.get_down_stream_ack_time_nanos()) /
                    1000);
                trace1->SetSuccess();
            }

            auto ack_time_per_peer_ns =
                ack.getPeerAckNS(now - packet_to_be_acked->getSendTimeNS());
            int32_t replace_index = strategy_->reportAckTimeNs(
                packet_to_be_acked->getDataSize(), now,
                packet_to_be_acked->getSendTimeNS(), ack_time_per_peer_ns,
                packet_to_be_acked);

            if (ctx_->failed_nodes_.size() + 1 < 8 && replace_index > -1) {
                ctx_->error_caused_by_slow_node_ = true;
                ctx_->error_index_ = replace_index;

                THROW_NO_STACK(
                    HdfsIOException,
                    "Detect write slow, Pipeline replacement strategy suggest "
                    "replace DN. Replace index is %d",
                    replace_index);
            }

            // update bytes acked
            if (ctx_->last_block_) {
                ctx_->last_block_->setNumBytes(
                    packet_to_be_acked->getLastByteOffsetBlock());
            }

            // remove packet from ack queue and notify data streamer
            {
                std::unique_lock<std::mutex> lock(ctx_->data_queue_mutex_);
                ctx_->last_acked_seqno_ = seqno;
                ctx_->ack_queue_.pop_front();
                ctx_->cv_.notify_all();
            }

            // Check if there is any callback to trigger.
            bool need_flush = packet_to_be_acked->getFlushWhenAcked();
            for (auto& callback : packet_to_be_acked->getAsyncWriteCallBack()) {
                ctx_->executor_.add([callback, need_flush, this]() {
                    if (need_flush && ctx_->persist_blocks_.exchange(false)) {
                        HDFSLOG(
                            DEBUG,
                            "Perform fsync in async write callback. Path is: {}",
                            file_path_);
                        std::unique_lock<std::mutex> lock(
                            ctx_->last_block_mutex_);
                        int64_t last_block_length =
                            ctx_->last_block_ ? ctx_->last_block_->getNumBytes()
                                              : -1;
                        filesystem_->fsync(file_path_, last_block_length,
                                           file_id_);
                    }
                    callback.write_callback(hdfsStatus::STATUS_OK,
                                            callback.args);
                });
            }
            IOMonitorTraceApiSuccess();
        } catch (const std::exception& e) {
            HDFS_LOG(ERROR, "Exception thrown when receive ack: {}", e.what());
            iomonitor_trace->SetLastException("response processor exception");
            iomonitor_trace->SetExceptionDetail(e.what());
            if (!responder_closed_) {
                // If no explicit error report was received, mark the primary
                // node as failed.
                if (ctx_->error_index_ == -1) {
                    HDFS_LOG(
                        WARN,
                        "ResponseProcessor throws exception and error_index = -1. "
                        "Mark primary node as failed. file_path:{}, block:{}, "
                        "failed_times:{}",
                        file_path_, ctx_->last_block_->toString(),
                        ctx_->failed_nodes_.size());
                    ctx_->error_index_ = 0;
                    HDFS_LOG_APPEND(ctx_->core_logs_, tl_core_log_records);
                }
                ctx_->has_error_ = true;

                {
                    std::unique_lock<std::mutex> lock(ctx_->data_queue_mutex_);
                    ctx_->cv_.notify_all();
                }

                responder_closed_ = true;
            }
        }
    }
}

void ResponseProcessor::close() {
    responder_closed_ = true;
    // C++ thread cannot interrupt.
    // The socket must be closed in DataStreamer first so that readFully can
    // return.
}

void ResponseProcessor::join() { receive_ack_loop_.join(); }

}  // namespace Internal
}  // namespace Hdfs
