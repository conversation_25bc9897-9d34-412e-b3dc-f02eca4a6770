/********************************************************************
 * 2014 -
 * open source under Apache License Version 2.0
 ********************************************************************/
/**
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
#ifndef _HDFS_LIBHDFS3_CLIENT_CONTENTSUMMARY_H_
#define _HDFS_LIBHDFS3_CLIENT_CONTENTSUMMARY_H_

#include <string.h>

#include <cstdint>

#include "client/FileEncryptionInfo.h"
#include "client/Permission.h"

namespace Hdfs {

class ContentSummary {
   public:
    uint64_t getLength() { return length_; }

    uint64_t getDirectoryCount() { return directory_count_; }

    uint64_t getFileCount() { return file_count_; }

    uint64_t getQuota() { return quota_; }

    uint64_t getSpaceConsumed() { return space_consumed_; }

    uint64_t getSpaceQuota() { return space_quota_; }

    void setLength(uint64_t length) { length_ = length; }

    void setDirectoryCount(uint64_t directory_count) {
        directory_count_ = directory_count;
    }

    void setFileCount(uint64_t file_count) { file_count_ = file_count; }

    void setQuota(uint64_t quota) { quota_ = quota; }

    void setSpaceConsumed(uint64_t space_consumed) {
        space_consumed_ = space_consumed;
    }

    void setSpaceQuota(uint64_t space_quota) { space_quota_ = space_quota; }

   private:
    uint64_t length_;
    uint64_t directory_count_;
    uint64_t file_count_;
    uint64_t quota_;
    uint64_t space_consumed_;
    uint64_t space_quota_;
};

}  // namespace Hdfs
#endif