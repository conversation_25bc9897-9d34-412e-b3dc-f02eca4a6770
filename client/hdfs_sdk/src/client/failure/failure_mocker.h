#ifndef HDFS_CLIENT_FAILURE_MOCKER_H
#define HDFS_CLIENT_FAILURE_MOCKER_H

#include <stdint.h>

namespace Hdfs {
namespace Internal {

class FailureMocker {
   public:
    FailureMocker() = default;

    virtual ~FailureMocker() = default;

    static FailureMocker* current_mock_;

    virtual void mockWhenSendPacket(int64_t packet_recv,
                                    int64_t offset_in_block = -1) = 0;

    virtual void mockWhenReceivePacket(int64_t packet_seq,
                                       int64_t offset_in_block = -1) = 0;
};

}  // namespace Internal
}  // namespace Hdfs

#endif  // HDFS_CLIENT_FAILURE_MOCKER_H