/********************************************************************
 * 2014 -
 * open source under Apache License Version 2.0
 ********************************************************************/
/**
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
#include "InputStreamImpl.h"

#include <ifaddrs.h>
#include <inttypes.h>
#include <sys/socket.h>
#include <sys/types.h>
#include <unistd.h>

#include <algorithm>
#include <chrono>
#include <cstdint>
#include <ctime>
#include <iomanip>
#include <iostream>
#include <memory>
#include <sstream>
#include <vector>

#include "FileSystemInter.h"
#include "InputStreamInter.h"
#include "async/AsyncIOWorker.h"
#include "async/PReadTaskRunner.h"
#include "bytecool/bytecool_handler.h"
#include "cache/MetaCache.h"
#include "client/BlockLocationRefresher.h"
#include "client/ByteRpcBlockReader.h"
#include "client/CoreLogRecords.h"
#include "client/LocalBlockReader.h"
#include "client/RemoteBlockReader.h"
#include "client/failover_strategy/FastSwitchReadStrategy.h"
#include "client/filesystem/HyperFile.h"
#include "client/hdfsAsyncContext.h"
#include "common/Defer.h"
#include "common/Exception.h"
#include "common/ExceptionInternal.h"
#include "common/HdfsEnv.h"
#include "common/Logger.h"
#include "common/SessionConfig.h"
#include "common/iovutil.h"
#include "datatransfer.pb.h"
#include "monitor/macro.h"
#include "server/Datanode.h"

namespace Hdfs {
namespace Internal {

std::shared_ptr<HyperFileMeta> PrepareCoolMeta(
    std::shared_ptr<HyperFileMeta> meta) {
    return meta;
}

std::unordered_set<std::string> BuildLocalAddrSet() {
    std::unordered_set<std::string> set;
    struct ifaddrs* ifAddr = NULL;
    struct ifaddrs* pifAddr = NULL;
    struct sockaddr* addr;

    if (getifaddrs(&ifAddr)) {
        THROW(HdfsNetworkException,
              "InputStreamImpl: cannot get local network interface: %s",
              GetSystemErrorInfo(errno));
    }

    try {
        std::vector<char> host;
        const char* pHost;
        host.resize(INET6_ADDRSTRLEN + 1);

        for (pifAddr = ifAddr; pifAddr != NULL; pifAddr = pifAddr->ifa_next) {
            addr = pifAddr->ifa_addr;

            if (!addr) {
                continue;
            }

            memset(&host[0], 0, INET6_ADDRSTRLEN + 1);

            if (addr->sa_family == AF_INET) {
                pHost = inet_ntop(
                    addr->sa_family,
                    &(reinterpret_cast<struct sockaddr_in*>(addr))->sin_addr,
                    &host[0], INET6_ADDRSTRLEN);
            } else if (addr->sa_family == AF_INET6) {
                pHost = inet_ntop(
                    addr->sa_family,
                    &(reinterpret_cast<struct sockaddr_in6*>(addr))->sin6_addr,
                    &host[0], INET6_ADDRSTRLEN);
            } else {
                continue;
            }

            if (NULL == pHost) {
                THROW(
                    HdfsNetworkException,
                    "InputStreamImpl: cannot get convert network address to textual "
                    "form: %s",
                    GetSystemErrorInfo(errno));
            }

            set.insert(pHost);
        }

        /*
         * add hostname.
         */
        long hostlen = sysconf(_SC_HOST_NAME_MAX);
        host.resize(hostlen + 1);

        if (gethostname(&host[0], host.size())) {
            THROW(HdfsNetworkException,
                  "InputStreamImpl: cannot get hostname: %s",
                  GetSystemErrorInfo(errno));
        }

        set.insert(&host[0]);
    } catch (...) {
        if (ifAddr != NULL) {
            freeifaddrs(ifAddr);
        }

        throw;
    }

    if (ifAddr != NULL) {
        freeifaddrs(ifAddr);
    }

    return set;
}

InputStreamImpl::InputStreamImpl()
    : closed(true),
      localRead(true),
      readFromUnderConstructedBlock(false),
      verify(true),
      maxGetBlockInfoRetry(3),
      cursor(0),
      endOfCurBlock(0) {
#ifdef MOCK
    stub = NULL;
#endif
}

InputStreamImpl::~InputStreamImpl() {}

void InputStreamImpl::checkStatus() {
    checkClosed();

    if (lastError != exception_ptr()) {
        rethrow_exception(lastError);
    }
}

void InputStreamImpl::checkClosed() {
    if (closed) {
        THROW(HdfsIOException, "InputStreamImpl: stream is not opened.");
    }
}

int64_t InputStreamImpl::readBlockLength(const LocatedBlock& b) {
    const std::vector<DatanodeInfo>& nodes = b.getLocations();
    int replicaNotFoundCount = nodes.size();

    for (size_t i = 0; i < nodes.size(); ++i) {
        try {
            int64_t n = 0;
            std::shared_ptr<Datanode> dn;
            RpcAuth a = auth;
            // client does not need a gdpr/zti token to communicate with
            // datanode. So we set the method to SIMPLE.
            a.setMethod(AuthMethod::SIMPLE);
            // TODO(dbc) Block token is not used for now
            // a.getUser().addToken(b.getToken());
#ifdef MOCK

            if (stub) {
                dn = stub->getDatanode();
            } else {
                dn = std::shared_ptr<Datanode>(
                    new DatanodeImpl(nodes[i].getIpAddr().c_str(),
                                     nodes[i].getIpcPort(), *sconf_, a));
            }

#else
            dn = std::make_shared<DatanodeImpl>(nodes[i].getIpAddr().c_str(),
                                                nodes[i].getIpcPort(), *sconf_,
                                                a);
#endif
            n = dn->getReplicaVisibleLength(b);

            if (n >= 0) {
                return n;
            }
        } catch (const ReplicaNotFoundException& e) {
            /*
             * may be ok, see comment about @replicaNotFoundCount below
             */
            HDFSLOG(
                INFO,
                "InputStreamImpl: failed to get block visible length for Block: "
                "{} file {} from Datanode: {}\n{}",
                b.toString(), path, nodes[i].formatAddress(),
                GetExceptionDetail(e));
            HDFSLOG(
                INFO,
                "InputStreamImpl: retry get block visible length for Block: {} "
                "file {} from other datanode",
                b.toString(), path);
            --replicaNotFoundCount;
        } catch (const HdfsIOException& e) {
            HDFSLOG(
                ERROR,
                "InputStreamImpl: failed to get block visible length for Block: "
                "{} file {} from Datanode: {}, "
                "will try other nodes\n{}",
                b.toString(), path, nodes[i].formatAddress(),
                GetExceptionDetail(e));
            HDFSLOG(
                INFO,
                "InputStreamImpl: retry get block visible length for Block: {} "
                "file {} from other datanode",
                b.toString(), path);
        }
    }

    // Namenode told us about these locations, but none know about the replica
    // means that we hit the race between pipeline creation start and end.
    // we require all 3 because some other exception could have happened
    // on a DN that has it.  we want to report that error
    if (replicaNotFoundCount == 0) {
        return 0;
    }

    return -1;
}

std::shared_ptr<LocatedBlocks> InputStreamImpl::GetBlockLocations() {
    std::shared_lock<std::shared_mutex> lock(lbs_mutex_);
    return lbs_;
}

std::shared_ptr<LocatedBlocks> InputStreamImpl::updateBlockInfos(
    std::shared_ptr<LocatedBlocks> lb, bool disable_fastvisit,
    bool force_update_block) {
    IOMonitorTraceApi(hdfsEvTypInternalupdateBlockInfos);
    std::shared_ptr<LocatedBlocks> res_block = nullptr;
    if (lb != nullptr) {
        // 1. Update lbs_ with the value of lb. This happens only for
        // HyperFiles.
        SetBlockLocations(lb);
        return lb;
    } else {
        // 2. Block locations are not known. Get block locations from NN.
        res_block = UpdateBlockLocations(
            (!disable_fastvisit) && sconf_->getEnableFastVisit(),
            force_update_block);
        if (!res_block) {
            THROW(HdfsIOException,
                  "InputStreamImpl: failed to get block location for file: %s.",
                  path.c_str());
        }
    }
    IOMonitorTraceApiSuccess();
    return res_block;
}

std::shared_ptr<LocatedBlocks> InputStreamImpl::UpdateBlockLocations(
    bool fastvisit, bool force_update_block) {
    static int64_t offset = 0;
    static int64_t length = INT64_MAX - 1;
    std::unique_lock<std::shared_mutex> lock(lbs_mutex_);

    if (enableLocationCache_) {
        if (!force_update_block) {
            auto loc_cache = HdfsEnv::Get()->GetMetaCache()->GetBlockLocations(
                path, offset, length);
            if (loc_cache.has_value()) {
                lbs_ = loc_cache.value();
                return lbs_;
            }
        } else {
            HdfsEnv::Get()->GetMetaCache()->RemoveBlockLocations(path);
        }
    }

    long now = std::time(nullptr);

    for (int i = 0; i < maxGetBlockInfoRetry; ++i) {
        try {
            auto lbs = std::make_shared<LocatedBlocksImpl>();
            auto coolMeta = std::make_shared<HyperFileMeta>();
            filesystem_->getBlockLocations(path, offset, length, *lbs,
                                           *coolMeta, fastvisit,
                                           force_update_block, true);
            if (coolMeta->isInited()) {
                setCoolFileState(coolMeta);
                return lbs_;
            } else {
                setCoolFileState(nullptr);
                // If the file contains no blocks, it is meaningless to put the
                // empty lbs into the cache.
                // If the file is UC, lbs should not be put into the cache
                // either because its location will change soon.
                if (enableLocationCache_ && !force_update_block) {
                    HdfsEnv::Get()->GetMetaCache()->SetBlockLocations(path,
                                                                      lbs);
                }
            }

            lbs_ = std::move(lbs);
            if (!isBytecoolFile()) {
                // After got block locations, we need to update the
                // being-written-length for the last block if this file is under
                // construction. Should be protected under the same lock
                bool res = updateLastBlockLength(lbs_);
                if (!res) {
                    THROW(
                        HdfsIOException,
                        "InputStreamImpl: failed to get last block length for file=%s "
                        "from all datanodes.",
                        path.c_str());
                }
            }
            return lbs_;
        } catch (const HdfsRpcException& e) {
            HDFSLOG(
                ERROR,
                "InputStreamImpl: fail to get block location for file {}, {}",
                path, GetExceptionDetail(e));
        }
        HDFSLOG(INFO,
                "InputStreamImpl: retry to get block location for file: {}, "
                "already tried {} times.",
                path, i + 1);
    }
    return lbs_;
}

bool InputStreamImpl::UpdateLastBlockLength() {
    std::shared_ptr<LocatedBlocks> locations = GetBlockLocations();
    return updateLastBlockLength(locations);
}

bool InputStreamImpl::updateLastBlockLength(
    std::shared_ptr<LocatedBlocks> lbs) {
    if (!lbs) {
        return false;
    }
    if (lbs->isLastBlockComplete()) {
        lastBlockBeingWrittenLength = 0;
        return true;
    }
    std::shared_ptr<LocatedBlock> last = lbs->getLastBlock();
    if (!last) {
        // If this file is empty, locations->isLastBlockComplete() returns false
        // but locations->getLastBlock() is nullptr. In this case we should
        // assum the last_block_length is updated successfully.
        return true;
    }
    try {
        // TODO(): implenment hedged getVisibleLength
        int64_t len = readBlockLength(*last);

        if (len == -1) {
            HDFS_LOG(WARN, "Failed to updateLastBlockLength for file {}", path);
            return false;
        }
        last->setNumBytes(len);
        lastBlockBeingWrittenLength = len;
    } catch (const HdfsRpcException& e) {
        HDFSLOG(INFO,
                "Failed to updateLastBlockLength for file {}. Err msg: {}",
                path, GetExceptionDetail(e));
        return false;
    }
    return true;
}

bool InputStreamImpl::refreshInfo() {
    // TODO(lbw): getVisibleLength from dn by hedged read.
    int64_t oldLength = getFileLength();
    std::string cause;
    if (UpdateLastBlockLength()) {
        cause = "tail";
        HDFSLOG(DEBUG, "refreshInfo - updateLastBlockLength() success");
    } else {
        int64_t newLength1 = getFileLength();
        if (oldLength != newLength1) {
            HDFSLOG(DEBUG,
                    "refreshInfo success by src={}, oldFileLength={}, "
                    "newFileLength={}.  But the block is already in the "
                    "FINALIZED state, still try to reopen.",
                    path, oldLength, newLength1);
        }
        cause = "reopen";
        updateBlockInfos();
        HDFSLOG(DEBUG, "refreshInfo - reopen() success");
    }
    int64_t newLength = getFileLength();
    if (oldLength != newLength) {
        HDFSLOG(DEBUG,
                "refreshInfo success by {}. src={}, oldFileLength={}, "
                "newFileLength={}",
                cause, path, oldLength, newLength);
    }
    return oldLength != newLength;
}

int64_t InputStreamImpl::getFileLength() {
    std::shared_lock<std::shared_mutex> lock(lbs_mutex_);
    std::shared_ptr<LocatedBlocks> locations = lbs_;
    int64_t length = locations->getFileLength();

    if (!locations->isLastBlockComplete()) {
        length += lastBlockBeingWrittenLength;
    }

    return length;
}

void InputStreamImpl::seekToBlock(const LocatedBlock& lb) {
    std::shared_ptr<LocatedBlocks> locations = GetBlockLocations();
    if (cursor >= locations->getFileLength()) {
        hdfs_assert(!locations->isLastBlockComplete());
        readFromUnderConstructedBlock = true;
    } else {
        readFromUnderConstructedBlock = false;
    }

    hdfs_assert(cursor >= lb.getOffset() &&
                cursor < lb.getOffset() + lb.getNumBytes());
    curBlock = std::make_shared<LocatedBlock>(lb);
    int64_t blockSize = curBlock->getNumBytes();
    hdfs_assert(blockSize > 0);
    endOfCurBlock = blockSize + curBlock->getOffset();
    failedNodes.clear();
    block_reader_.reset();
    auto& nodes = curBlock->mutableLocations();
    HdfsEnv::Get()->GetDataNodeManager()->SortNodes(nodes);
}

bool InputStreamImpl::choseBestNode() {
    auto nodes = curBlock->getLocations();

    for (size_t i = 0; i < nodes.size(); ++i) {
        if (std::binary_search(failedNodes.begin(), failedNodes.end(),
                               nodes[i])) {
            continue;
        }
        cross_dc_ = HdfsEnv::Get()->GetIOMonitor()->get_cross_dc(
            nodes[i].getDataCenter());
        if (!curBlock->getStorageTypes().empty()) {
            media_type_ = HdfsEnv::Get()->GetIOMonitor()->get_media_type(
                curBlock->getStorageTypes()[i]);
        }
        curNode = nodes[i];
        return true;
    }

    return false;
}

bool InputStreamImpl::isLocalNode() {
    static const std::unordered_set<std::string> LocalAddrSet =
        BuildLocalAddrSet();
    bool retval = LocalAddrSet.find(curNode.getIpAddr()) != LocalAddrSet.end();
    return retval;
}

void InputStreamImpl::setupBlockReader(bool temporaryDisableLocalRead) {
    bool lastReadFromLocal = false;
    exception_ptr lastException;
    IOMonitorTraceApi(hdfsEvTypInternalsetupBlockReader);
    while (true) {
        if (!choseBestNode()) {
            try {
                if (lastException) {
                    rethrow_exception(lastException);
                }
            } catch (const ReplicaNotFoundException& e) {
                THROW_NO_STACK(
                    ReplicaNotFoundException,
                    "InputStreamImpl: all nodes have been tried but no "
                    "valid replica can be read for Block: %s.",
                    curBlock->toString().c_str());
            }

            THROW_NO_STACK(
                HdfsIOException,
                "InputStreamImpl: all nodes have been tried and no valid "
                "replica can be read for Block: %s.",
                curBlock->toString().c_str());
        }

        try {
            int64_t offset, len;
            offset = cursor - curBlock->getOffset();
            hdfs_assert(offset >= 0);
            len = curBlock->getNumBytes() - offset;
            hdfs_assert(len > 0);

            if (!temporaryDisableLocalRead && !lastReadFromLocal &&
                !readFromUnderConstructedBlock && localRead && isLocalNode()) {
                lastReadFromLocal = true;

                std::shared_ptr<ReadShortCircuitInfo> info;
                ReadShortCircuitInfoBuilder builder(curNode, auth, *sconf_);
                EncryptionKey ekey = filesystem_->getEncryptionKeys();

                try {
                    info = builder.fetchOrCreate(*curBlock,
                                                 curBlock->getToken(), ekey);

                    if (!info) {
                        continue;
                    }

                    hdfs_assert(info->isValid());
                    block_reader_ = std::make_unique<LocalBlockReader>(
                        info, *curBlock, offset, verify, *sconf_,
                        localReaderBuffer);
                    block_reader_type_ = BlockReaderType::LOCAL;
                } catch (...) {
                    if (info) {
                        info->setValid(false);
                    }

                    throw;
                }
            } else {
                const char* clientName = filesystem_->getClientName();
                lastReadFromLocal = false;
                createBlockReader(offset, len, clientName);
            }

            break;
        } catch (const HdfsException& e) {
            lastException = current_exception();

            if (lastReadFromLocal) {
                HDFS_LOG(
                    WARN,
                    "cannot setup block reader for Block: {} file {} on Datanode: "
                    "{}.\n{}\n"
                    "retry the same node but disable read shortcircuit feature ",
                    curBlock->toString(), path, curNode.formatAddress(),
                    GetExceptionDetail(e));
                /*
                 * do not add node into failedNodes since we will retry the same
                 * node but disable local block reading
                 */
            } else {
                std::string exception_msg = "";
                if (!sconf_->getEncryptedDatanode() &&
                    !sconf_->getSecureDatanode()) {
                    exception_msg = GetExceptionDetail(e);
                }
                HDFS_LOG(
                    WARN,
                    "cannot setup block reader for Block: {} file {} on Datanode: "
                    "{}.\n {}",
                    curBlock->toString(), path, curNode.formatAddress(),
                    exception_msg);
                if (shouldCreateByteRpcBlockReader() &&
                    sconf_->byte_rpc_read_fallback) {
                    // create ByteRPC reader failed,
                    // fallback to Xfer for the same DN
                    force_block_reader_xfer_ = true;
                } else {
                    failedNodes.push_back(curNode);
                    force_block_reader_xfer_ = false;
                }
                std::sort(failedNodes.begin(), failedNodes.end());
            }
        }
    }
    IOMonitorTraceApiSuccess();
}

void InputStreamImpl::createBlockReader(int64_t offset, int64_t len,
                                        const char* clientName) {
    if (shouldCreateByteRpcBlockReader()) {
#ifndef DISABLE_BYTERPC
        // If we disable ByteRPC in compile time, fallback to Xfer
        ByteRpcBlockReaderArgs args(filesystem_, curBlock, curNode, offset, len,
                                    verify, path, *sconf_,
                                    switch_read_strategy_);
        block_reader_ = std::make_unique<ByteRpcBlockReader>(args);
        block_reader_type_ = BlockReaderType::BYTE_RPC;
        return;
#endif
    }
    block_reader_ = std::make_unique<RemoteBlockReader>(
        filesystem_, *curBlock, curNode, offset, len, curBlock->getToken(),
        clientName, verify, path, *sconf_, switch_read_strategy_);
    block_reader_type_ = BlockReaderType::XFER;
    // we created the fallback-ed one, next time we still try ByteRPC one
    force_block_reader_xfer_ = false;
}

bool InputStreamImpl::shouldCreateByteRpcBlockReader() {
    return curNode.getByteRpcPort() != 0 && !force_block_reader_xfer_ &&
           sconf_->byte_rpc_read_enable;
}

int32_t InputStreamImpl::pread(char* buf, int64_t offset, int32_t size) {
    checkClosed();
    if (isBytecoolFile()) {
        return cool_file_handler_->PRead(offset, size, buf);
    } else {
        THROW(UnsupportedOperationException,
              "InputStreamImpl: sync pread only support bytecool file");
    }
}

hdfsStatus InputStreamImpl::asyncPRead(int64_t offset, int32_t length,
                                       inner_hdfs_io_context& context) {
    PathTraceSetType(hdfsEvTypHdfsPread);
    checkClosed();
#ifndef DISABLE_BYTERPC
    bool is_byterpc = length <= sconf_->byte_rpc_pread_threshold;
#else
    bool is_byterpc = false;
#endif
    std::vector<std::shared_ptr<AsyncReadContext>> ctxs;
    hdfsStatus status =
        GetReadTaskPerBlock(ctxs, context, offset, length, is_byterpc);
    if (status != hdfsStatus::STATUS_OK) {
        return status;
    }

    auto* worker_group =
        HdfsEnv::Get()->GetAsyncIOWorkerGroup(async_worker_group_size_, sconf_);

    auto fly_num = worker_group->GetFlyAsyncRead();
    if (fly_num > max_num_fly_async_read_) {
        HDFS_LOG(WARN,
                 "AsyncPread: Too many flying requests, current={}, max={}",
                 fly_num, max_num_fly_async_read_);
        return hdfsStatus::STATUS_IO_BUSY;
    } else {
        worker_group->IncFlyAsyncRead();
    }

    // Ensure the same blockid in the same eventloop to increase the
    // cache_hit_rate of connection cache.
    for (auto& ctx : ctxs) {
        auto& worker =
            worker_group->chooseWorkerByHash(ctx->block_.getBlockId());
#ifndef DISABLE_BYTERPC
        if (ctx->is_byterpc_) {
            HDFS_LOG(DEBUG, "[{}] Pread Triggered ByteRPC", ctx->IdString());
            PReadTaskRunner::getConnectionAndLaunchByteRpcReadTask(ctx);
        } else {
#endif
            worker.registerTaskToEventLoop(ctx);
#ifndef DISABLE_BYTERPC
        }
#endif
    }
    return hdfsStatus::STATUS_OK;
}

hdfsStatus InputStreamImpl::GetReadTaskPerBlock(
    std::vector<std::shared_ptr<AsyncReadContext>>& ctxs,
    inner_hdfs_io_context& user_context, int64_t offset,
    int32_t expect_data_length, bool is_byterpc) {
    int64_t available_data_length{0};
    bool has_updated_block_info{false};
    std::shared_ptr<AsyncReadResult> result =
        std::make_shared<AsyncReadResult>();
    bool wantUpdateBlockCache = user_context.wantForceUpdateBlocks->load();
    long now = std::time(nullptr);
    if (wantUpdateBlockCache &&
        now - last_update_time.load() > sconf_->force_update_interval_sec_) {
        user_context.wantForceUpdateBlocks->store(false);
        updateBlockInfos(nullptr, false, true);
        last_update_time.store(now);
        HDFS_LOG(WARN, "forceUpdate finish. path: {}", path);
    }
    std::shared_ptr<LocatedBlocks> locations = GetBlockLocations();

    std::pair<size_t, size_t> buffer_iov_cursor{0, 0};

    while (available_data_length < expect_data_length) {
        const LocatedBlock* lb = nullptr;
        if (!locations || (offset + available_data_length >= getFileLength()) ||
            !(lb = locations->findBlock(offset + available_data_length))) {
            // Fail to find corresponding block_info.
            // As block_info may have changed since last getBlockLocation(eg.
            // more data has written by append), try to update block_info only
            // once.
            if (available_data_length > 0) {
                // Has already update block list, but still cannot find
                // corresponding block_info. Maybe offset + length is larger
                // than current file_length. But we can still read some data
                // start from offset.
                break;
            } else if (!has_updated_block_info) {
                // Try to fetch as much data as possible,
                // so we need not to retry updating when located_block can not
                // be found again.
                locations =
                    updateBlockInfos(nullptr, false, wantUpdateBlockCache);
                has_updated_block_info = true;
                // Try to findBlock again.
                continue;
            } else {
                // No data can be read. Directly return error.
                // TODO(huanglixin.626 remove when invalid range bug is fixed)
                if (offset != getFileLength()) {
                    bool block_locations_empty = (locations == nullptr);
                    std::string block_locations_str;
                    if (locations) {
                        auto blocks = locations->getBlocks();
                        for (auto& block : blocks) {
                            block_locations_str +=
                                "block:" + block.toDebugString();
                        }
                        if (locations->getLastBlock()) {
                            block_locations_str +=
                                "last block:" +
                                locations->getLastBlock()->toDebugString();
                        }
                    }
                    HdfsEnv::Get()->GetIOMonitor()->DebugInvalidRange();
                    HDFS_LOG(
                        WARN,
                        "[GetReadTaskPerBlock] invalid range, offset={}, expect={}, "
                        "fileLen={}, file={}, avaliable={}, has updated={}, block "
                        "locations={}, is empty={}",
                        offset, expect_data_length, getFileLength(),
                        GetFilePath(), available_data_length,
                        has_updated_block_info, block_locations_str,
                        block_locations_empty);
                } else {
                    HDFS_LOG(
                        WARN,
                        "[GetReadTaskPerBlock] invalid range, offset:{}, expect:{}, "
                        "fileLen:{}, file {}",
                        offset, expect_data_length, getFileLength(),
                        GetFilePath());
                }
                return hdfsStatus::STATUS_INVALID_RANGE;
            }
        } else {
            if (lb->getLocations().size() == 0) {
                HdfsEnv::Get()->GetMetaCache()->RemoveBlockLocations(path);
                // In persistent mode, if we fetch empty vector<Datanode>, we
                // want force update.
                user_context.wantForceUpdateBlocks->store(true);
                HDFS_LOG(
                    WARN,
                    "[GetReadTaskPerBlock] block location is empty, path={} "
                    "offset={} expect_data_length={}",
                    path, offset, expect_data_length);
                // No block can be read return missing_block by advance.
                return hdfsStatus::STATUS_BLOCK_EMPTY;
            }

            int64_t inner_block_offset =
                offset + available_data_length - lb->getOffset();
            int64_t inner_block_read_length =
                std::min(lb->getNumBytes() - inner_block_offset,
                         expect_data_length - available_data_length);

#ifndef DISABLE_BYTERPC
            if (is_byterpc) {
                // use byterpc if all locations have the byterpc port
                // TODO: better fallback scheme?
                for (const auto& loc : lb->getLocations()) {
                    if (loc.getByteRpcPort() == 0) {
                        is_byterpc = false;
                        break;
                    }
                }
            }
#endif

            // iomonitor_trace->success_ will be set in
            // PReadTaskRunner::markTaskFinishedAndTriggerCallback
            IOMonitorTraceApi(hdfsEvTypInternalAsyncReadOneBlk,
                              inner_block_read_length, lb->getLocationIps());
            if (user_context.iovcnt > 0) {
                // extract the sub_iov for read
                std::vector<iovec> sub_iov;
                auto callback = [&](iovec cur_iov, size_t cur_offset,
                                    size_t copy) {
                    iovec iov{};
                    iov.iov_base =
                        static_cast<char*>(cur_iov.iov_base) + cur_offset;
                    iov.iov_len = copy;
                    sub_iov.push_back(iov);
                };
                auto& cursor_index = buffer_iov_cursor.first;
                auto& cursor_offset = buffer_iov_cursor.second;
                proceed_iovec(cursor_index, cursor_offset,
                              inner_block_read_length, user_context.iov,
                              user_context.iovcnt, callback);
                ctxs.push_back(std::make_shared<AsyncReadContext>(
                    inner_block_offset,      /* offset inner this block */
                    inner_block_read_length, /* length to read start from offset
                                                inner block*/
                    std::move(sub_iov),      /* dst to copy data */
                    *lb,                     /* located block */
                    result, user_context, std::move(iomonitor_trace), path,
                    is_byterpc, sconf_));
            } else {
                ctxs.push_back(std::make_shared<AsyncReadContext>(
                    inner_block_offset,      /* offset inner this block */
                    inner_block_read_length, /* length to read start from offset
                                                inner block*/
                    user_context.buffer +
                        available_data_length, /* dst to copy data */
                    *lb,                       /* located block */
                    result, user_context, std::move(iomonitor_trace), path,
                    is_byterpc, sconf_));
            }
            available_data_length += inner_block_read_length;
        }
    }

    result->task_to_finish_count_ = ctxs.size();
    return hdfsStatus::STATUS_OK;
}

int32_t InputStreamImpl::read(char* buf, int32_t size) {
    checkClosed();
    PathTraceSetType(hdfsEvTypHdfsRead);

    if (isBytecoolFile()) {
        return cool_file_handler_->Read(size, buf);
    }

    try {
        [[maybe_unused]] int64_t prvious = cursor;
        int32_t done = readInternal(buf, size);
        HDFSLOG(DEBUG,
                "{} read file {} size is {}, offset {} done {}, next pos {}",
                static_cast<void*>(this), path, size, prvious, done, cursor);
        return done;
    } catch (const HdfsEndOfStream& e) {
        throw;
    } catch (...) {
        lastError = current_exception();
        throw;
    }
}

int32_t InputStreamImpl::readOneBlock(char* buf, int32_t size,
                                      std::string* error_msg) {
    bool temporaryDisableLocalRead = false;
    IOMonitorTraceApi(hdfsEvTypInternalreadOneBlock);
    while (true) {
        try {
            // Setup block reader here and handle failure.
            if (!block_reader_) {
                // 1. set up block reader
                setupBlockReader(temporaryDisableLocalRead);
                temporaryDisableLocalRead = false;
            }
        } catch (const ReplicaNotFoundException& e) {
            *error_msg = fmt::format(
                "InputStreamImpl: failed to read Block (replica not found): {} file "
                "{}, {}, "
                "retry after updating block information.",
                curBlock->toString(), path, GetExceptionDetail(e));
            HDFS_LOG(WARN, "{}", *error_msg);
            return -2;
        } catch (const HdfsInvalidBlockToken& e) {
            *error_msg = fmt::format(
                "InputStreamImpl: failed to read Block (stale token): {} file"
                "{}, {}, retry after updating block information.",
                curBlock->toString(), path, GetExceptionDetail(e));
            HDFS_LOG(WARN, "{}", *error_msg);
            return -1;
        } catch (const HdfsIOException& e) {
            // In setupBlockReader, we have tried all the replicas.
            // We now update block information once, and try again.
            *error_msg = fmt::format(
                "InputStreamImpl: failed to read Block: {} file {}, {}, "
                "retry after updating block information.",
                curBlock->toString(), path, GetExceptionDetail(e));
            HDFS_LOG(WARN, "{}", *error_msg);
            return -1;
        }

        // Block reader has been setup, read from block reader.
        try {
            int32_t todo = size;
            {
                IOMonitorTraceApi(hdfsEvTypDataNoderead);
                iomonitor_trace->SetCrossDC(cross_dc_);
                iomonitor_trace->SetMediaType(media_type_);
                iomonitor_trace->AddRemoteAddr(
                    block_reader_->remote_svr_addr());
                todo = todo < endOfCurBlock - cursor
                           ? todo
                           : static_cast<int32_t>(endOfCurBlock - cursor);
                hdfs_assert(block_reader_);
                // 2. read it
                todo = block_reader_->read(buf, todo);
                cursor += todo;
                /*
                 * Exit the loop and function from here if success.
                 */
                iomonitor_trace->SetThroughput(todo);
                IOMonitorTraceApiSuccess();
            }
            iomonitor_trace->SetThroughput(todo);
            IOMonitorTraceApiSuccess();
            return todo;
        } catch (const HdfsIOException& e) {
            /*
             * Failed to read from current block reader,
             * add the current datanode to invalid node list and try again.
             */
            *error_msg = fmt::format(
                "InputStreamImpl: failed to read Block: {} file {} from "
                "Datanode: {}, {}, retry read again from another Datanode.",
                curBlock->toString(), path, curNode.formatAddress(),
                GetExceptionDetail(e));
            HDFS_LOG(WARN, "{}", *error_msg);

            if (sconf_->doesNotRetryAnotherNode()) {
                throw;
            }
        } catch (const ChecksumException& e) {
            *error_msg = fmt::format(
                "InputStreamImpl: failed to read Block: {} file {} from Datanode: "
                "{}, {}, "
                "retry read again from another Datanode.",
                curBlock->toString(), path, curNode.formatAddress(),
                GetExceptionDetail(e));
            HDFS_LOG(WARN, "{}", *error_msg);
        } catch (const ReplicaNotFoundException& e) {
            // For ByteRPC, it throws ReplicaNotFoundException from read
            // operation
            *error_msg = fmt::format(
                "InputStreamImpl: failed to read Block (replica not found): {} file "
                "{}, {}, "
                "retry after updating block information.",
                curBlock->toString(), path, GetExceptionDetail(e));
            HDFS_LOG(WARN, "{}", *error_msg);
            return -2;
        }

        /*
         * Successfully create the block reader but failed to read.
         * Disable the local block reader and try the same node again.
         */
        if (!block_reader_ ||
            dynamic_cast<LocalBlockReader*>(block_reader_.get())) {
            temporaryDisableLocalRead = true;
        } else {
            /*
             * Remote block reader failed to read, try another node.
             */
            if (block_reader_type_ == BlockReaderType::BYTE_RPC &&
                sconf_->byte_rpc_read_fallback) {
                // we don't add curNode to failedNodes for ByteRPC
                // as it will fall back to the Xfer one
                HDFSLOG(
                    INFO,
                    "InputStreamImpl: datanode {} fallback to RemoteBlockReader "
                    "for file {}.",
                    curNode.formatAddress(), path);
                force_block_reader_xfer_ = true;
            } else {
                HDFSLOG(
                    INFO,
                    "InputStreamImpl: Add invalid datanode {} to failed datanode "
                    "and try another datanode again for file {}.",
                    curNode.formatAddress(), path);
                failedNodes.push_back(curNode);
                force_block_reader_xfer_ = false;
            }
            std::sort(failedNodes.begin(), failedNodes.end());
        }

        block_reader_.reset();
    }
}

// bool InputStreamImpl::chooseBestNode(AsyncReadTask& task, std::string& dn_ip,
//                                      int32_t& dn_port) {
//   auto& locs = task.block_->getLocations();
//   for (size_t i = 0; i < locs.size(); ++i) {
//     if (std::binary_search(failedNodes.begin(), failedNodes.end(),
//                            locs[i])) {
//       continue;
//     }

//     task.current_dn_idx_ = i;
//     dn_ip = locs[i].getIpAddr();
//     dn_port = locs[i].getXferPort();
//     return true;
//   }

//   return false;
// }

int32_t InputStreamImpl::readInternal(char* buf, int32_t size) {
    IOMonitorTraceApi(hdfsEvTypHdfsReadInternal);
    try {
        int64_t ret = readInternalImpl(buf, size);
        iomonitor_trace->SetThroughput(ret);
        IOMonitorTraceApiSuccess();
        return ret;
    } catch (const HdfsEndOfStream& e) {
        IOMonitorTraceApiSuccess();
        throw;
    } catch (...) {
        throw;
    }
}

/**
 * To read data from hdfs.
 * @param buf the buffer used to filled.
 * @param size buffer size.
 * @return return the number of bytes filled in the buffer, it may less than
 * size.
 */
int32_t InputStreamImpl::readInternalImpl(char* buf, int32_t size) {
    assertReadUseState(false, true);
    DEFER([&]() { assertReadUseState(true, false); });

    bool reset_parameter_for_FSR = true;
    // This flag is used to temporarily disable fastvisit when fast_visit_ is
    // true. This happens when all replicas from the fastvisit-ed DN are not
    // accessable, where we should fallback to getting block locations for
    // original DNs.
    bool disable_fastvisit = false;
    bool force_update_meta = false;
    int32_t read_dn_failure_times = 0;
    int32_t retry_backoff = 200;
    std::string error_msg;

    auto locations = GetBlockLocations();

    try {
        do {
            const LocatedBlock* lb = NULL;

            // Check if we have got the block information we need.
            if (!locations || cursor >= getFileLength() ||
                (cursor >= endOfCurBlock &&
                 !(lb = locations->findBlock(cursor)))) {
                /*
                 * Get block information from namenode.
                 * Do RPC failover work in updateBlockInfos.
                 */
                locations = updateBlockInfos(nullptr, disable_fastvisit,
                                             force_update_meta);

                /*
                 * We already have the up-to-date block information,
                 * Check if we reach the end of file.
                 */
                if (cursor >= getFileLength()) {
                    THROW(
                        HdfsEndOfStream,
                        "InputStreamImpl: read over EOF, current position: %" PRId64
                        ", read size: %d, from file: %s",
                        cursor, size, path.c_str());
                }
            }

            /*
             * If we reach the end of block or the block information has just
             * updated, seek to the right block to read.
             */
            if (cursor >= endOfCurBlock) {
                lb = locations->findBlock(cursor);

                if (!lb) {
                    THROW(
                        HdfsIOException,
                        "InputStreamImpl: cannot find block information at position: "
                        "%" PRId64 " for file: %s",
                        cursor, path.c_str());
                }

                /*
                 * Seek to the right block, setup all needed variable,
                 * but do not setup block reader, setup it latter.
                 */
                seekToBlock(*lb);

                if (switch_read_strategy_) {
                    // fast_failover_strategy_=true, when switch a new block
                    // fast_failover_strategy_=false, when switch the same block
                    // Because all dn throw exception
                    switch_read_strategy_->resetFactor(reset_parameter_for_FSR);
                }
            }

            int32_t retval = readOneBlock(buf, size, &error_msg);

            /*
             * Now we have tried all replicas and failed.
             * We will update metadata once and try again.
             */
            if (retval < 0) {
                locations.reset();
                disable_fastvisit = true;
                reset_parameter_for_FSR = false;
                endOfCurBlock = 0;
                read_dn_failure_times += 1;
                if (block_reader_type_ == BlockReaderType::BYTE_RPC &&
                    sconf_->byte_rpc_read_fallback) {
                    // we try xfer, this time does not count
                    read_dn_failure_times -= 1;
                }
                if (read_dn_failure_times < sconf_->getMaxReadBlockRetry() ||
                    (retval == -2 && !force_update_meta)) {
                    force_update_meta = true;
                    HDFS_LOG(WARN, "Block is missing. Retry. Current times: {}",
                             read_dn_failure_times);

                    retry_backoff =
                        std::min(retry_backoff << 1,
                                 sconf_->getMaxReadBlockRetryBackoff());
                    std::this_thread::sleep_for(
                        std::chrono::milliseconds(retry_backoff));
                    continue;
                } else {
                    HDFS_LOG(
                        WARN,
                        "Block is missing. Retry time reaches the maximum {}",
                        sconf_->getMaxReadBlockRetry());

                    THROW(
                        MissingBlockException,
                        "Block is missing. Retry time reaches the maximum %d."
                        " InputStreamImpl: cannot read file: %s, from position %" PRId64
                        ", size: %d last_error: %s.",
                        read_dn_failure_times, path.c_str(), cursor, size,
                        error_msg.c_str());
                }
            }

            return retval;
        } while (true);
    } catch (const HdfsCanceled& e) {
        throw;
    } catch (const HdfsEndOfStream& e) {
        throw;
    } catch (const MissingBlockException& e) {
        throw;
    } catch (const HdfsException& e) {
        /*
         * wrap the underlying error and rethrow.
         */
        NESTED_THROW(
            HdfsIOException,
            "InputStreamImpl: cannot read file: %s, from position %" PRId64
            ", size: %d last_error: %s.",
            path.c_str(), cursor, size, error_msg.c_str());
    }
}

/**
 * To read data from hdfs, block until get the given size of bytes.
 * @param buf the buffer used to filled.
 * @param size the number of bytes to be read.
 */
void InputStreamImpl::readFully(char* buf, int64_t size) {
    HDFSLOG(DEBUG, "readFully file {} size is {}, offset {}", path, size,
            cursor);
    checkClosed();

    try {
        return readFullyInternal(buf, size);
    } catch (const HdfsEndOfStream& e) {
        throw;
    } catch (...) {
        lastError = current_exception();
        throw;
    }
}

void InputStreamImpl::readFullyInternal(char* buf, int64_t size) {
    int32_t done;
    int64_t pos = cursor, todo = size;

    try {
        while (todo > 0) {
            done = todo < std::numeric_limits<int32_t>::max()
                       ? static_cast<int32_t>(todo)
                       : std::numeric_limits<int32_t>::max();
            done = readInternal(buf + (size - todo), done);
            todo -= done;
        }
    } catch (const HdfsCanceled& e) {
        throw;
    } catch (const HdfsEndOfStream& e) {
        THROW(HdfsEndOfStream,
              "InputStreamImpl: read over EOF, current position: %" PRId64
              ", read size: %" PRId64 ", from file: %s",
              pos, size, path.c_str());
    } catch (const HdfsException& e) {
        NESTED_THROW(HdfsIOException,
                     "InputStreamImpl: cannot read fully from file: %s, from "
                     "position %" PRId64 ", size: %" PRId64 ".",
                     path.c_str(), pos, size);
    }
}

int64_t InputStreamImpl::available() {
    checkStatus();

    try {
        if (block_reader_) {
            return block_reader_->available();
        }
    } catch (...) {
        lastError = current_exception();
        throw;
    }

    return 0;
}

/**
 * To move the file point to the given position.
 * @param size the given position.
 */
void InputStreamImpl::seek(int64_t pos) {
    HDFSLOG(DEBUG, "{} seek file {} to {}, offset {}", static_cast<void*>(this),
            path, pos, cursor);
    checkClosed();

    assertReadUseState(false, true);
    DEFER([&]() { assertReadUseState(true, false); });

    if (isBytecoolFile()) {
        return cool_file_handler_->Seek(pos);
    }

    try {
        seekInternal(pos);
    } catch (const HdfsEndOfStream& e) {
        // do not treat the end of as error.
        // User can continue to seek to a valid offset, which is the same as
        // java code.
        throw;
    } catch (...) {
        lastError = current_exception();
        throw;
    }
}

void InputStreamImpl::seekInternal(int64_t pos) {
    if (cursor == pos) {
        return;
    }
    int64_t previous = cursor;
    IOMonitorTraceApi(hdfsEvTypInternalSeek);

    auto locations = GetBlockLocations();

    if (!locations || pos > getFileLength()) {
        updateBlockInfos();

        if (pos > getFileLength()) {
            THROW(HdfsEndOfStream,
                  "InputStreamImpl: seek over EOF, current position: %" PRId64
                  ", seek target: %" PRId64 ", in file: %s",
                  cursor, pos, path.c_str());
        }
    }

    try {
        if (block_reader_ && pos > cursor && pos < endOfCurBlock &&
            pos - cursor <= sconf_->getSeekThreshold()) {
            block_reader_->skip(pos - cursor);
            cursor = pos;
            HdfsEnv::Get()->Get()->GetStatsMonitor()->RecordSeekPos(cursor -
                                                                    previous);
            iomonitor_trace->SetThroughput(cursor - previous);
            IOMonitorTraceApiSuccess();
            return;
        }
    } catch (const HdfsIOException& e) {
        HDFSLOG(
            ERROR,
            "InputStreamImpl: failed to skip {} bytes in current block reader "
            "for file {} {}",
            pos - cursor, path, GetExceptionDetail(e));
        HDFSLOG(INFO,
                "InputStreamImpl: retry to seek to position {} for file {}",
                pos, path);
        IOMonitorAddException(e.ReflexName, e.what());
    } catch (const ChecksumException& e) {
        HDFSLOG(
            ERROR,
            "InputStreamImpl: failed to skip {} bytes in current block reader "
            "for file {} {}",
            pos - cursor, path, GetExceptionDetail(e));
        HDFSLOG(INFO,
                "InputStreamImpl: retry to seek to position {} for file {}",
                pos, path);
        IOMonitorAddException(e.ReflexName, e.what());
    }

    /**
     * the seek target exceed the current block or skip failed in current block
     * reader. reset current block reader and set the cursor to the target
     * position to seek.
     */
    endOfCurBlock = 0;
    block_reader_.reset();
    cursor = pos;
    HdfsEnv::Get()->Get()->GetStatsMonitor()->RecordSeekPos(cursor - previous);
    iomonitor_trace->SetThroughput(cursor - previous);
    IOMonitorTraceApiSuccess();
}

/**
 * To get the current file point position.
 * @return the position of current file point.
 */
int64_t InputStreamImpl::tell() {
    checkClosed();
    if (isBytecoolFile()) {
        return cool_file_handler_->Tell();
    }
    HDFSLOG(DEBUG, "tell file {} at {}", path, cursor);
    return cursor;
}

/**
 * Get the visible length of the file. It will include the length of the last
 * block even if that is in UnderConstruction state.
 * @return int64_t the File Length
 */
int64_t InputStreamImpl::getVisibleLength() { return getFileLength(); }

/**
 * Close the stream.
 */
void InputStreamImpl::close() {
    HDFSLOG(DEBUG, "{} close file {} for read", static_cast<void*>(this), path);
    HdfsEnv::Get()->GetStatsMonitor()->DeleteInputFile(path);
    closed = true;
    localRead = true;
    readFromUnderConstructedBlock = false;
    verify = true;
    filesystem_.reset();
    cursor = 0;
    endOfCurBlock = 0;
    lastBlockBeingWrittenLength = 0;
    block_reader_.reset();
    curBlock.reset();
    lbs_.reset();
    coolFileMeta_.reset();
    sconf_.reset();
    failedNodes.clear();
    path.clear();
    localReaderBuffer.resize(0);
    lastError = exception_ptr();
    if (cool_file_handler_) {
        auto ret = cool_file_handler_->Close();
        cool_file_handler_.reset();
        if (!ret) {
            HDFS_LOG(WARN,
                     "[InputStreamImpl] close cool file handler {} failed.",
                     path);
        }
    }
}

std::string InputStreamImpl::toString() {
    if (!path.empty()) {
        return std::string("InputStream for path ") + path;
    } else {
        return std::string("InputStream (not opened)");
    }
}

const std::string& InputStreamImpl::GetFilePath() const { return path; }

void InputStreamImpl::setCoolFileState(std::shared_ptr<HyperFileMeta> meta) {
    bool meta_not_empty = (meta != nullptr);

    bool old_state = is_inited_cool_state_.exchange(true);

    if (meta) {
        coolFileMeta_ = PrepareCoolMeta(meta);
    }

    if (!old_state) {
        is_bytecool_file_ = meta_not_empty;
        return;
    } else if (is_bytecool_file_ != meta_not_empty) {
        THROW_NO_STACK(HdfsIOException,
                       "InputStreamImpl: file %s, original cool state: %d, now "
                       "cool state: %d.",
                       path.c_str(), is_bytecool_file_, meta_not_empty);
    }
}

void InputStreamImpl::tryAsyncRefreshLocation(bool force_update_refresh) {
    if (sconf_ && sconf_->enable_async_refresh_location) {
        HdfsEnv::Get()->GetBlockLocationRefresher(sconf_)->refresh(
            shared_from_this(), nullptr, false, true, force_update_refresh);
    }
}

void InputStreamImpl::openBytecoolHandler(HdfsFileSystemInternalWrapper* fs) {
    if (fs == nullptr || !isBytecoolFile()) {
        THROW_NO_STACK(InvalidParameter,
                       "InputStreamImpl: file %s open bytecool object nil file "
                       "system or not bytecool file",
                       path.c_str());
    }

    if (coolFileMeta_->get_version() == FileStorageFormat::FT_BYTECOOL_V1) {
        auto obj =
            Hdfs::Bytecool::OpenCoolFileObject(fs, coolFileMeta_, sconf_, path);
        if (!obj) {
            THROW_NO_STACK(HdfsIOException,
                           "InputStreamImpl: bytecool file %s open failed",
                           path.c_str());
        }
        cool_file_handler_ =
            std::make_shared<Hdfs::Bytecool::BytecoolHandler>(std::move(obj));
    } else if (coolFileMeta_->get_version() ==
               FileStorageFormat::FT_BYTECOOL_V2) {
        std::shared_ptr<Hdfs::HyperFile> hyper_file{nullptr};
        try {
            if (!sconf_->getBytecoolKernelEnable()) {
                THROW_NO_STACK(HdfsException,
                               "dfs.bytecool.kernel.enable is false");
            }
            hyper_file = Hdfs::HyperFile::OpenForRead(fs, *coolFileMeta_);
            if (!hyper_file) {
                THROW_NO_STACK(HdfsException,
                               "[HyperFile] OpenForRead return null");
            }
        } catch (const std::exception& e) {
            THROW_NO_STACK(
                HdfsIOException,
                "InputStreamImpl: bytecool v2 file %s open failed, err: %s",
                path.c_str(), e.what());
        }
        cool_file_handler_ = std::make_shared<Hdfs::Bytecool::BytecoolHandler>(
            hyper_file, coolFileMeta_, filesystem_->getBytecoolHandler());
    }
}

bool InputStreamImpl::isBytecoolFile() { return is_bytecool_file_; }

void InputStreamImpl::init(std::shared_ptr<Hdfs::Internal::FileSystemInter> fs,
                           std::shared_ptr<Hdfs::Internal::SessionConfig> conf,
                           bool verifyChecksum, std::string path_,
                           bool is_bytecool) {
    filesystem_ = fs;
    verify = verifyChecksum;
    path = path_;
    if (is_bytecool) {
        if (fs->getByteCoolConf() != nullptr) {
            sconf_ = fs->getByteCoolConf();
        } else {
            sconf_ = fs->getConf();
        }
    } else {
        sconf_ = conf;
    }

    enableLocationCache_ = fs->getConf()->getEnableBlockLocationCache();
    auth = RpcAuth(fs->getUserInfo(),
                   RpcAuth::ParseMethod(sconf_->getRpcAuthMethod()));
    prefetch_size_ =
        sconf_->getDefaultBlockSize() * sconf_->GetPrefetchNumBlocks();
    localRead = sconf_->isReadFromLocal();
    maxGetBlockInfoRetry = sconf_->getMaxGetBlockInfoRetry();
    closed = false;

    if (sconf_->getEnableFastSwitchRead()) {
        HDFSLOG(
            DEBUG,
            "sconf_->getFSRSlowIOThreshold(): {}. sconf_->getFSRIOTimeout(): {}. "
            "sconf_->getFSRTimeWindowLengthNS(): {}. "
            "sconf_->getFSRMinimumTimeNS(): {}.",
            sconf_->getFSRSlowIOThreshold(), sconf_->getFSRIOTimeout(),
            sconf_->getFSRTimeWindowLengthNS(), sconf_->getFSRMinimumTimeNS());
        switch_read_strategy_ = std::make_shared<FastSwitchReadStrategy>(
            sconf_->getFSRSlowIOThreshold(), sconf_->getFSRIOTimeout(),
            sconf_->getFSRConnectTimeout(),
            std::chrono::nanoseconds(sconf_->getFSRTimeWindowLengthNS()),
            std::chrono::nanoseconds(sconf_->getFSRMinimumTimeNS()));
    }
    max_num_fly_async_read_ =
        static_cast<uint32_t>(sconf_->getMaxAsyncPreadRequestCount());
    async_worker_group_size_ =
        static_cast<uint32_t>(sconf_->getAsyncWorkerGroupSize());
    HdfsEnv::Get()->GetStatsMonitor()->NewInputFile(path);
}

void InputStreamImpl::assertReadUseState(bool expect_use, bool set_use) {
    if (!readInUse_.compare_exchange_strong(expect_use, set_use)) {
        THROW(HdfsException,
              "Inputstream use state not match, expect=%d, set=%d, path=%s, "
              "cursor=%ld",
              expect_use, set_use, path.c_str(), cursor);
    }
}

}  // namespace Internal
}  // namespace Hdfs
