//
// Created by <PERSON><PERSON><PERSON> on 2021-05-08.
//

#ifndef HDFS_CLIENT_OUTPUTSTREAMASYNCIMPL_H
#define HDFS_CLIENT_OUTPUTSTREAMASYNCIMPL_H

#include <memory>
#include <string>

#include "AbstractOutputStream.h"
#include "FileSystem.h"
#include "OutputDataStreamer.h"
#include "OutputStream.h"
#include "OutputStreamAsyncContext.h"
#include "OutputStreamInter.h"
#include "client/PacketHeader.h"
#include "client/PacketPool.h"
#include "client/Permission.h"
#include "client/Pipeline.h"
#include "client/failover_strategy/FastFailoverStrategy.h"
#include "client/failover_strategy/ThroughputFailoverStrategy.h"
#include "client/hdfsAsyncContext.h"
#include "common/Checksum.h"
#include "common/DateTime.h"
#include "common/ExceptionInternal.h"
#include "common/HWCrc32c.h"
#include "common/SWCrc32c.h"
#include "common/SessionConfig.h"
#include "monitor/macro.h"
#include "server/LocatedBlock.h"

namespace Hdfs {
namespace Internal {

// OutputStreamAsyncImpl creates files from a stream of bytes.
//
// The client application writes data that is cached internally by
// this stream. Data is broken up into packets, each packet is
// typically 64K in size. A packet comprises of chunks. Each chunk
// is typically 512 bytes and has an associated checksum with it.
//
// When a client application fills up the currentPacket, it is
// enqueued into dataQueue.  The DataStreamer thread picks up
// packets from the dataQueue, sends it to the first datanode in
// the pipeline and moves it from the dataQueue to the ackQueue.
// The ResponseProcessor receives acks from the datanodes. When an
// successful ack for a packet is received from all datanodes, the
// ResponseProcessor removes the corresponding packet from the
// ackQueue.
//
// In case of error, all outstanding packets and moved from
// ackQueue. A new pipeline is setup by eliminating the bad
// datanode from the original pipeline. The DataStreamer now
// starts sending packets from the dataQueue.
class OutputStreamAsyncImpl : public AbstractOutputStream {
   public:
    OutputStreamAsyncImpl();

    virtual ~OutputStreamAsyncImpl() noexcept;

    // To append data to file.
    // @param buf the data used to append.
    // @param size the data size.
    void append(const char* buf, int64_t size) override;

    // Flush all data in buffer and waiting for ack.
    // Will block until get all acks.
    void flush() override;

    // return the current file length.
    // @return current file length.
    int64_t tell() override;

    // @ref OutputStream::sync
    void sync(bool updateLength) override;

    // close the stream.
    void close() override;

    void closeImpl();

    void setError(const exception_ptr& error) override;

    // asyncWrite. Write data to the internal buffer of outputstream,
    // once data is written to internal buffer, the user callback will be
    // called.
    // * @param buf The buffer to copy read bytes into.
    // * @param size The length of the buf.
    // * @context the callback context passed by user.
    hdfsStatus asyncWrite(const char* buf, int64_t size,
                          inner_hdfs_io_context& context) override;

    // asyncWriteAndFlush. Write data to remote datanode.
    // once all the data is acked, the user callback will be called.
    // * @param buf The buffer to copy read bytes into.
    // * @param size The length of the buf.
    // * @context the callback context passed by user.
    hdfsStatus asyncWriteAndFlush(const char* buf, int64_t size,
                                  inner_hdfs_io_context& context) override;

    // hdfsAsyncFlush. Wait for data is acked.
    // once all the data is acked, the user callback will be called.
    // * @context the callback context passed by user.
    hdfsStatus asyncFlush(inner_hdfs_io_context& context) override;

    void initAppend(std::shared_ptr<FileStatus> file_status) override;

   private:
    void reset() override;

    bool shouldTriggerFastFailover();

    void openInternal(
        std::shared_ptr<FileSystemInter> fs, const char* path, int flag,
        const Permission& permission, bool create_parent, int replication,
        int64_t block_size,
        std::shared_ptr<std::map<std::string, HyperCacheMeta>> xattrs,
        std::shared_ptr<FileStatus> status_create_or_append,
        int stripe_unit_count, int stripe_unit_size) override;

    bool tooMuchPacketOnTheFly() {
        std::unique_lock<std::mutex> lock(ctx_->data_queue_mutex_);
        return ctx_->data_queue_.size() + ctx_->ack_queue_.size() >=
               (uint32_t)packet_pool_.getMaxSize();
    }

    void finishCurBlock(bool wait);

    void flushOrSync(bool is_sync, bool updateLength);

    void flushOrSyncImpl(bool is_sync, bool updateLength);

    void waitForAckedSeqno(int64_t seqno);

    void checkClosed();

    void mayFinishCurrentBlock(bool wait_enqueue) override {
        if (bytes_written_in_block_ == block_size_ ||
            shouldTriggerFastFailover()) {
            finishCurBlock(wait_enqueue);
        }
    }

    void sendPacket(bool wait_enqueue = true) override {
        IOMonitorTraceApi(hdfsEvTypInternalAppendPacket,
                          current_packet_->getDataSize());
        std::unique_lock<std::mutex> lock(ctx_->data_queue_mutex_);

        if (wait_enqueue && !conf_->packet_num_unlimited()) {
            // If queue is full, then wait till we have enough space
            IOMonitorTraceApi(hdfsEvTypInternalwaitQueue);
            while (!ctx_->output_stream_closed_ &&
                   ctx_->data_queue_.size() + ctx_->ack_queue_.size() >
                       (uint32_t)packet_pool_.getMaxSize()) {
                ctx_->cv_.wait_for(lock, std::chrono::milliseconds(1000));
            }
            IOMonitorTraceApiSuccess();
        }

        checkClosed();

        hdfs_assert(current_packet_);

        last_seen_seqno_ = current_packet_->getSeqno();
        ctx_->last_queued_seqno_ = current_packet_->getSeqno();
        ctx_->data_queue_.push_back(std::move(current_packet_));
        ctx_->cv_.notify_all();
        IOMonitorTraceApiSuccess();
    }

   private:
    //
    void flushAndRegisterCallBack(bool is_sync,
                                  inner_hdfs_io_context* callback_context);

   private:
    int64_t last_seen_seqno_{-1};
    bool should_sync_block_{false};

    std::mutex output_stream_mutex_;

    std::shared_ptr<OutputStreamAsyncContext> ctx_;
    std::shared_ptr<OutputDataStreamer> streamer_;
    std::shared_ptr<FastFailoverStrategy> failover_strategy_;
};

}  // namespace Internal
}  // namespace Hdfs

#endif  // HDFS_CLIENT_OUTPUTSTREAMASYNCIMPL_H
