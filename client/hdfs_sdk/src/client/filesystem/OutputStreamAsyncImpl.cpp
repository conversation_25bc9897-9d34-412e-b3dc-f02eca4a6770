#include "OutputStreamAsyncImpl.h"

#include "cache/MetaCache.h"
#include "client/LeaseRenewer.h"
#include "common/HdfsEnv.h"

namespace Hdfs {
namespace Internal {

OutputStreamAsyncImpl::OutputStreamAsyncImpl() {
    ctx_ = std::make_shared<OutputStreamAsyncContext>();
}

OutputStreamAsyncImpl::~OutputStreamAsyncImpl() noexcept {
    if (!ctx_->output_stream_closed_) {
        try {
            close();
        } catch (const std::exception& e) {
            HDFSLOG(ERROR, "[OutputStreamAsyncImpl] close error: {}", e.what());
        }
    }
}

void OutputStreamAsyncImpl::openInternal(
    std::shared_ptr<FileSystemInter> fs, const char* path, int flag,
    const Permission& permission, bool create_parent, int replication,
    int64_t block_size,
    std::shared_ptr<std::map<std::string, HyperCacheMeta>> xattrs,
    std::shared_ptr<FileStatus> status_create_or_append, int stripe_unit_count,
    int stripe_unit_size) {
    initFileStatus(fs, path, flag, permission, create_parent, replication,
                   block_size, xattrs, status_create_or_append,
                   stripe_unit_count, stripe_unit_size);
    ctx_->output_stream_closed_ = false;
    failover_strategy_ = std::make_shared<ThroughputFailoverStrategy>(conf_);
    streamer_ = std::make_shared<OutputDataStreamer>(
        src_, fileStatus_->getFileId(), is_append_, conf_, CHECKSUM_TYPE_CRC32C,
        bytes_per_chunk_, replication_, block_size_, filesystem_, ctx_,
        failover_strategy_, fileStatus_->getBlockStoragePolicyProto());
    streamer_->start();
    PathTraceSetType(hdfsEvTypHdfsWrite);
}

void OutputStreamAsyncImpl::reset() {
    resetInternal();
    ctx_->output_stream_closed_ = true;
    ctx_->last_block_.reset();
}

void OutputStreamAsyncImpl::append(const char* buf, int64_t size) {
    if (NULL == buf || size < 0) {
        THROW(InvalidParameter, "Invalid parameter.");
    }

    checkClosed();
    IOMonitorTraceApi(hdfsEvTypHdfsAppend);
    iomonitor_trace->SetThroughput(size);
    try {
        std::unique_lock<std::mutex> lock(output_stream_mutex_);
        appendInternal(buf, size);
    } catch (...) {
        setError(current_exception());
        throw;
    }
    IOMonitorTraceApiSuccess();
}

hdfsStatus OutputStreamAsyncImpl::asyncWrite(const char* buf, int64_t size,
                                             inner_hdfs_io_context& context) {
    // NoData need to flush. Start to trigger callback.
    if (ctx_->output_stream_closed_) {
        return hdfsStatus::STATUS_CONNECTION_CLOSED;
    }

    try {
        std::unique_lock<std::mutex> lock(output_stream_mutex_);
        if (tooMuchPacketOnTheFly()) {
            return hdfsStatus::STATUS_IO_BUSY;
        }
        appendInternal(buf, size, false);
    } catch (...) {
        setError(current_exception());
        throw;
    }

    // As asyncWrite just write data to OutputStream Buffer, which need_not to
    // wait for ack. Just trigger callback
    ctx_->executor_.add([context]() {
        context.write_callback(hdfsStatus::STATUS_OK, context.args);
    });

    return hdfsStatus::STATUS_OK;
}

hdfsStatus OutputStreamAsyncImpl::asyncFlush(inner_hdfs_io_context& context) {
    // NoData need to flush. Start to trigger callback.
    if (ctx_->output_stream_closed_) {
        return hdfsStatus::STATUS_CONNECTION_CLOSED;
    }

    try {
        std::unique_lock<std::mutex> lock(output_stream_mutex_);
        // We must hould output_stream_closed_mutex_, to prevent output_stream
        // is closed during AppendInternal. Or the user callback will not be
        // called.
        std::unique_lock<std::mutex> closed_lock(
            ctx_->output_stream_closed_mutex_);
        if (ctx_->output_stream_closed_) {
            return hdfsStatus::STATUS_CONNECTION_CLOSED;
        }
        if (tooMuchPacketOnTheFly()) {
            return hdfsStatus::STATUS_IO_BUSY;
        }
        flushAndRegisterCallBack(false, &context);
    } catch (...) {
        setError(current_exception());
        throw;
    }

    return hdfsStatus::STATUS_OK;
}

hdfsStatus OutputStreamAsyncImpl::asyncWriteAndFlush(
    const char* buf, int64_t size, inner_hdfs_io_context& context) {
    if (ctx_->output_stream_closed_) {
        return hdfsStatus::STATUS_CONNECTION_CLOSED;
    }

    try {
        std::unique_lock<std::mutex> lock(output_stream_mutex_);
        // We must hould output_stream_closed_mutex_, to prevent output_stream
        // is closed during AppendInternal. Or the user callback will not be
        // called.
        std::unique_lock<std::mutex> close_lock(
            ctx_->output_stream_closed_mutex_);
        if (ctx_->output_stream_closed_) {
            return hdfsStatus::STATUS_CONNECTION_CLOSED;
        }
        if (tooMuchPacketOnTheFly()) {
            return hdfsStatus::STATUS_IO_BUSY;
        }
        appendInternal(buf, size, false);
        flushAndRegisterCallBack(false, &context);
        return hdfsStatus::STATUS_OK;
    } catch (...) {
        setError(current_exception());
        throw;
    }
}

// Notice: When calling flushAndRegisterCallBack(), output_stream_mutex_ should
// be held.
void OutputStreamAsyncImpl::flushAndRegisterCallBack(
    bool is_sync, inner_hdfs_io_context* callback_context) {
    if (position_in_buffer_ > 0) {
        appendChunkToPacket(&chunk_buffer_[0], position_in_buffer_);
    }

    if (!current_packet_ && is_sync) {
        // Create an empty packet just for sync.
        current_packet_ = packet_pool_.getPacket(
            PacketHeader::GetPkgHeaderSize(), 0, bytes_written_in_block_,
            next_seq_no_++, checksum_size_);
    }

    // Has packet to send, attach callback to current_packet.
    if (current_packet_) {
        current_packet_->setSyncFlag(is_sync);
        if (callback_context) {
            current_packet_->addAsyncWriteCallBack(*callback_context);
        }
        current_packet_->setFlushWhenAcked(true);
        sendPacket(true);
    } else if (callback_context) {
        // No packet can be sent. Try to attach the callback to the earliest
        // packet.
        std::unique_lock<std::mutex> lock(ctx_->data_queue_mutex_);
        if (ctx_->ack_queue_.size() > 0) {
            // Wait last packet in ack_queue to be acked.
            ctx_->ack_queue_.back()->setFlushWhenAcked(true);
            ctx_->ack_queue_.back()->addAsyncWriteCallBack(*callback_context);
        } else if (ctx_->data_queue_.size() > 0) {
            // AckQueue is empty. Wait last packet
            ctx_->data_queue_.back()->setFlushWhenAcked(true);
            ctx_->data_queue_.back()->addAsyncWriteCallBack(*callback_context);
        } else {
            // NoData need to be acked. Directly trigger user callback.
            inner_hdfs_io_context& call_back = *callback_context;
            ctx_->executor_.add([call_back, this]() {
                if (ctx_->persist_blocks_.exchange(false)) {
                    HDFSLOG(
                        DEBUG,
                        "Perform fsync in async write callback. Path is: {}",
                        src_);
                    std::unique_lock<std::mutex> lock(ctx_->last_block_mutex_);
                    int64_t last_block_length =
                        ctx_->last_block_ ? ctx_->last_block_->getNumBytes()
                                          : -1;
                    filesystem_->fsync(src_, last_block_length,
                                       fileStatus_->getFileId());
                }
                call_back.write_callback(hdfsStatus::STATUS_OK, call_back.args);
            });
        }
    }
}

bool OutputStreamAsyncImpl::shouldTriggerFastFailover() {
    return (bytes_written_in_block_ % bytes_in_cur_chunk_) == 0 &&
           failover_strategy_->shouldFailoverPipeline();
}

void OutputStreamAsyncImpl::initAppend(
    std::shared_ptr<FileStatus> file_status) {
    ctx_->last_block_ = filesystem_->append(src_);
    if (!file_status) {
        file_status = std::make_shared<FileStatus>(
            filesystem_->getFileStatus(this->src_.c_str()));
    }

    ctx_->output_stream_closed_ = false;

    try {
        block_size_ = file_status->getBlockSize();
        cursor_in_file_ = file_status->getLength();

        if (ctx_->last_block_) {
            is_append_ = true;
            bytes_written_in_block_ = ctx_->last_block_->getNumBytes();

            int32_t used_in_cksum = cursor_in_file_ % bytes_in_cur_chunk_;
            int32_t free_in_cksum = bytes_in_cur_chunk_ - used_in_cksum;

            int64_t free_in_last_block = block_size_ - bytes_written_in_block_;

            if (used_in_cksum > 0 && free_in_cksum > 0) {
                // if there is space in the last partial chunk, then
                // setup in such a way that the next packet will have only
                // one chunk that fills up the partial chunk.
                packet_size_ = 0;
                bytes_in_cur_chunk_ = free_in_cksum;
            } else {
                // if the remaining space in the block is smaller than
                // that expected size of of a packet, then create
                // smaller size packet.
                packet_size_ = packet_size_ < free_in_last_block
                                   ? packet_size_
                                   : static_cast<int32_t>(free_in_last_block);
            }
        }
    } catch (...) {
        completeFile(false, ctx_->last_block_);
        reset();
        throw;
    }

    computePacketChunkSize();
}

void OutputStreamAsyncImpl::finishCurBlock(bool wait_enqueue = true) {
    // Create an empty packet to notify the datanode the end of block.
    current_packet_ = packet_pool_.getPacket(PacketHeader::GetPkgHeaderSize(),
                                             0, bytes_written_in_block_,
                                             next_seq_no_++, checksum_size_);
    current_packet_->setLastPacketInBlock(true);

    if (sync_block_) {
        current_packet_->setSyncFlag(sync_block_);
    }

    sendPacket(wait_enqueue);
    bytes_written_in_block_ = 0;
    last_flush_offset_ = 0;
}

void OutputStreamAsyncImpl::checkClosed() {
    if (ctx_->output_stream_closed_) {
        auto last_error = ctx_->exception_;
        if (last_error) {
            HDFS_LOG_APPEND(tl_core_log_records, ctx_->core_logs_);
            rethrow_exception(last_error);
        } else {
            THROW(HdfsIOException,
                  "OutputStreamImplAsync: stream is not opened.");
        }
    }
}

// Flush all data in buffer and waiting for ack.
// Will block until get all acks.
void OutputStreamAsyncImpl::flush() { flushOrSync(false, false); }

// return the current file length.
// @return current file length.
int64_t OutputStreamAsyncImpl::tell() {
    std::unique_lock<std::mutex> lock(output_stream_mutex_);
    return cursor_in_file_;
}

// @ref OutputStream::sync
void OutputStreamAsyncImpl::sync(bool updateLength) {
    flushOrSync(true, updateLength);
}

void OutputStreamAsyncImpl::close() {
    IOMonitorTraceApi(hdfsEvTypHdfsCloseOutputStream);
    try {
        closeImpl();
    } catch (...) {
        throw;
    }
    IOMonitorTraceApiSuccess();
}

// close the stream.
void OutputStreamAsyncImpl::closeImpl() {
    std::unique_lock<std::mutex> lock(output_stream_mutex_);

    if (ctx_->output_stream_closed_) {
        auto exception = ctx_->exception_;
        ctx_->exception_ = nullptr;
        if (exception) {
            HDFS_LOG_APPEND(tl_core_log_records, ctx_->core_logs_);
            // Throwing exception when closed indicates that this stream is no
            // longer available.
            HdfsEnv::Get()->GetLeaseRenewer()->StopRenew(filesystem_);
            rethrow_exception(exception);
        } else {
            return;
        }
    }
    exception_ptr e;
    try {
        // flush from all upper layers
        if (position_in_buffer_ > 0) {
            // Because partial flush will not increase bytes_written_in_block_.
            // However if there is partial close when file close, we need to
            // send last_packet It has the same effect as JavaCode:
            // DFSOutputStream#flushBuffer
            appendChunkToPacket(&chunk_buffer_[0], position_in_buffer_);
        }

        if (current_packet_) {
            sendPacket();
        }

        bytes_written_in_block_ += position_in_buffer_;
        if (bytes_written_in_block_ != 0) {
            // send an empty packet to mark the end of the block
            current_packet_ = packet_pool_.getPacket(
                PacketHeader::GetPkgHeaderSize(), 0, bytes_written_in_block_,
                next_seq_no_++, checksum_size_);
            current_packet_->setLastPacketInBlock(true);
            current_packet_->setSyncFlag(should_sync_block_);
            sendPacket();
        }

        waitForAckedSeqno(last_seen_seqno_);

        if (streamer_) {
            streamer_->close();
            streamer_->join();
            streamer_.reset();
        }
        completeFile(true, ctx_->last_block_);
        HdfsEnv::Get()->GetMetaCache()->RemoveBlockLocations(src_);
    } catch (...) {
        e = current_exception();
    }

    // Throwing exception when closed indicates that this stream is no longer
    // available.
    HdfsEnv::Get()->GetLeaseRenewer()->StopRenew(filesystem_);
    if (e) {
        rethrow_exception(e);
    }
}

void OutputStreamAsyncImpl::flushOrSync(bool is_sync, bool updateLength) {
    IOMonitorTraceApi(hdfsEvTypHdfsFlushOrSync);
    try {
        flushOrSyncImpl(is_sync, updateLength);
    } catch (...) {
        throw;
    }
    IOMonitorTraceApiSuccess();
}

void OutputStreamAsyncImpl::flushOrSyncImpl(bool is_sync, bool updateLength) {
    checkClosed();
    int64_t last_block_length{-1};

    {
        std::unique_lock<std::mutex> lock(output_stream_mutex_);
        flushInternal(is_sync);
    }

    waitForAckedSeqno(last_seen_seqno_);

    if (ctx_->persist_blocks_.exchange(false) || updateLength) {
        {
            std::unique_lock<std::mutex> lock(output_stream_mutex_);
            std::unique_lock<std::mutex> last_block_lock(
                ctx_->last_block_mutex_);
            if (streamer_ && ctx_->last_block_) {
                last_block_length = ctx_->last_block_->getNumBytes();
            }
            try {
                filesystem_->fsync(src_, last_block_length,
                                   fileStatus_->getFileId());
            } catch (const HdfsIOException& e) {
                HDFSLOG(
                    WARN,
                    "Unable to persist blocks in hflush for {}. Caused by: {}",
                    src_, e.msg());
                // If we got an error here, it might be because some other
                // thread called close before our hflush completed. In that
                // case, we should throw an exception that the stream is closed.
                checkClosed();
                // If we aren't closed but failed to sync, we should expose that
                // to the caller.
                throw;
            }
        }
    }

    HdfsEnv::Get()->GetMetaCache()->RemoveBlockLocations(src_);
}

void OutputStreamAsyncImpl::setError(const exception_ptr& error) {
    ctx_->exception_ = error;
}

void OutputStreamAsyncImpl::waitForAckedSeqno(int64_t seqno) {
    {
        std::unique_lock<std::mutex> lock(ctx_->data_queue_mutex_);

        while (!ctx_->output_stream_closed_) {
            checkClosed();
            if (ctx_->last_acked_seqno_ >= seqno) {
                break;
            }

            ctx_->cv_.wait_for(lock, std::chrono::milliseconds(1000));
        }
    }
    checkClosed();
}

}  // namespace Internal
}  // namespace Hdfs
