#pragma once

#include <folly/executors/CPUThreadPoolExecutor.h>
#include <folly/executors/ThreadPoolExecutor.h>

#include <memory>

#include "FileSystemImpl.h"
#include "OutputStream.h"
#include "client/hdfs.h"
#include "common/common.h"
#include "server/LocatedBlock.h"

namespace Hdfs {

using namespace Hdfs::Internal;

namespace Internal {
class LocatedBlock;
}

/**
 * A output stream used to parallel write data to hdfs.
 */
class ParallelOutputStreamV2 : public OutputStream {
   public:
    ParallelOutputStreamV2(const OutPutStreamOpenOption& option);
    virtual ~ParallelOutputStreamV2();

    void open(FileSystem& fs) override;

    void append(const char* buf, int64_t size,
                const inner_hdfs_io_context& context) override;

    void appendv(const struct iovec* iov, int iov_cnt,
                 const inner_hdfs_io_context& context) override;

    void flush() override;

    int64_t tell(const inner_hdfs_io_context& context) override;

    void sync(bool updateLength) override;

    void close() override;

    hdfsStatus asyncWrite(const char* buf, int64_t size,
                          inner_hdfs_io_context& context) override;

    hdfsStatus asyncFlush(inner_hdfs_io_context& context) override;

    hdfsStatus asyncWriteAndFlush(const char* buf, int64_t size,
                                  inner_hdfs_io_context& context) override;

    const std::string& getFilePath() const override;

   private:
    size_t get_stripe_unit_index(int64_t offset) {
        return offset / option_.stripe_unit_size_ % option_.stripe_unit_count_;
    }

    size_t get_next_stripe_unit_offset(int64_t offset) {
        return (offset / option_.stripe_unit_size_ + 1) *
               option_.stripe_unit_size_;
    }

    void caculate_skip_size_when_init_append();

    std::vector<std::shared_ptr<OutputStream>> impls_;
    std::vector<int64_t> skip_size_vec_;
    std::shared_ptr<FileSystemInter> filesystem_;
    int64_t offset_{0};
    std::unique_ptr<IOTrace> append_interval_trace_ = nullptr;

    std::unique_ptr<folly::CPUThreadPoolExecutor> executor_ = nullptr;
    std::vector<IOVector> io_vec_;
};

}  // namespace Hdfs