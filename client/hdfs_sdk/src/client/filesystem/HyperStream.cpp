#include "HyperStream.h"

#include <unordered_map>

#include "common/common.h"

namespace Hdfs {
namespace Internal {

HyperStream::~HyperStream() {
    try {
        Close();
    } catch (const std::exception& e) {
        HDFSLOG(ERROR, "[HyperStream] close error: {}", e.what());
    }
}

std::vector<InputStream*> HyperStream::OpenForRead(const HyperFileMeta& meta) {
    if (state_ != KInit || meta.get_archive_blocks() == nullptr) {
        THROW(
            InvalidParameter,
            "HyperStream: open for read failed, src: %s, invalid state: %d, meta "
            "init: %d, meta version: %d",
            hyper_src_.c_str(), state_, meta.isInited(), meta.get_version());
    }

    std::vector<InputStream*> is_list;
    auto hbs = meta.get_archive_blocks();
    auto hBlockMap = hbs->getLocatedHyperBlocks();
    for (auto& hB : hBlockMap) {
        InputStream* iStream = nullptr;
        try {
            InputStreamOpenOption option;
            option.path_ = hbs->getHBlockFullPath(hB.first);
            option.verify_checksum_ = true;
            option.lbs_ = hbs->getLocatedBlocks(hB.first);
            option.is_bytecool_ = true;

            iStream = new InputStream(option);
            iStream->open(filesystem_);
            is_list.push_back(iStream);
        } catch (...) {
            if (iStream) {
                delete iStream;
            }
        }
    }
    state_ = KRead;

    HDFSLOG(DEBUG, "HyperStream: OpenForRead success, path={}, version={}",
            hyper_src_, meta.get_version());

    return is_list;
}

std::vector<OutputStream*> HyperStream::Create(
    const Permission& masked, bool createParent,
    const std::vector<std::string>& hyperBlockNames,
    const std::string& payload) {
    if (state_ != KInit || hyperBlockNames.empty()) {
        THROW(
            InvalidParameter,
            "HyperStream: create file failed, src: %s, invalid state: %d, block "
            "size: %ld",
            hyper_src_.c_str(), state_, hyperBlockNames.size());
    }

    // Actually create the hyper file.
    std::vector<FileStatus> retHBlocksInfo;
    auto fs =
        filesystem_.createHyperFile(hyper_src_, masked, createParent,
                                    hyperBlockNames, payload, retHBlocksInfo);
    if (fs == nullptr || retHBlocksInfo.size() != hyperBlockNames.size()) {
        THROW(
            HdfsException,
            "HyperStream: createHyperFile but the expected file infos were not "
            "obtained, hyper info exist: %d, block size returned: %ld, create "
            "block size: %ld",
            fs != nullptr, retHBlocksInfo.size(), hyperBlockNames.size());
    }

    // Construct OutputStreams that be opened.
    std::unordered_map<std::string, std::shared_ptr<FileStatus>> tempInfoMap;

    for (auto& info : retHBlocksInfo) {
        auto info_ptr = std::make_shared<FileStatus>(info);
        tempInfoMap[get_last_name_from_path(info_ptr->getPath())] = info_ptr;
    }

    if (tempInfoMap.size() != hyperBlockNames.size()) {
        THROW(HdfsException,
              "HyperStream: import names not independent, input: %ld, get: %ld",
              hyperBlockNames.size(), tempInfoMap.size());
    }

    std::vector<OutputStream*> os_list;

    for (auto& hName : hyperBlockNames) {
        OutputStream* os = nullptr;
        try {
            auto& status = tempInfoMap[hName];
            OutPutStreamOpenOption option;
            option.path_ = status->getPath();
            option.flag_ = Hdfs::Create | Hdfs::Overwrite;
            option.permission_ = 0644;
            option.create_parent_ = true;
            option.replication_ = 0;
            option.block_size_ = 0;
            option.status_ = status;

            os = new OutputStream(option);
            os->open(filesystem_);
        } catch (...) {
            if (os) {
                delete os;
            }
            for (auto ptr : os_list) {
                delete ptr;
            }
            os_list.clear();
            auto e = std::current_exception();
            std::rethrow_exception(e);
            return os_list;
        }
        os_list.push_back(os);
    }

    hyper_info_ = fs;
    state_ = KCreated;

    HDFSLOG(DEBUG, "HyperStream: import success, path={}, hyperBlockNames={}",
            hyper_src_, fmt::join(hyperBlockNames, "-"));

    return os_list;
}

void HyperStream::StartImport(const std::string& originalSrc,
                              const bool is_xreimport) {
    if (state_ != KCreated && state_ != KImportCommitted) {
        THROW(
            InvalidParameter,
            "HyperStream: start import failed, src: %s, original: %s, invalid "
            "state: %d",
            hyper_src_.c_str(), originalSrc.c_str(), state_);
    }

    // original user file must be closed.
    if (!filesystem_.isFileClosed(originalSrc.c_str())) {
        THROW(InvalidParameter,
              "HyperStream: original file %s is not closed, hyper file: %s",
              originalSrc.c_str(), hyper_src_.c_str());
    }

    // We need to associate the original file info so that we can verify the
    // file id when committing.
    // The sizes passed down by upper platform such as alfred are not used here.
    // Because this part of meta used for validation is fine as long as it is
    // obtained before reading user files.
    original_info_ = std::make_shared<FileStatus>(
        filesystem_.getFileStatus(originalSrc.c_str()));
    original_src_ = originalSrc;
    is_xreimport_ = is_xreimport;
    state_ = KStartImport;
}

// `CommitImport` can only be called after `StartImport`.
void HyperStream::CommitImport(uint64_t sliceOff, uint64_t sliceLen,
                               const std::string& slicePayload) {
    if (state_ != KStartImport) {
        THROW(InvalidParameter,
              "HyperStream: commit failed, src: %s, invalid state: %d",
              hyper_src_.c_str(), state_);
    }

    uint64_t archiveFileId = hyper_info_ ? hyper_info_->getFileId() : 0;
    uint64_t originalFileId = original_info_ ? original_info_->getFileId() : 0;
    // do not check original file length when doing xreimport
    // as the original file is an empty file
    auto originalLen =
        original_info_ && !is_xreimport_
            ? std::optional<uint64_t>(original_info_->getLength())
            : std::nullopt;
    auto originalMTime =
        original_info_
            ? std::optional<uint64_t>(original_info_->getModificationTime())
            : std::nullopt;
    uint64_t retEpoch = 0;

    filesystem_.commitSlice(hyper_src_, archiveFileId, original_src_,
                            originalFileId, originalLen, originalMTime,
                            sliceOff, sliceLen, epoch_, slicePayload, retEpoch);
    epoch_ = retEpoch;
    state_ = KImportCommitted;
}

// return false if do not need repair and do not return epoch.
void HyperStream::StartRepairHFile(
    const std::vector<std::string>& corruptPaths,
    std::vector<std::string>& retRealCorruptPaths, bool force) {
    if (state_ != KInit || corruptPaths.empty()) {
        THROW(InvalidParameter,
              "HyperStream: repair failed, src: %s, invalid state: %d, corrupt "
              "size: %ld",
              hyper_src_.c_str(), state_, corruptPaths.size());
    }

    uint64_t retEpoch = 0;
    retRealCorruptPaths.clear();
    filesystem_.repairHFile(hyper_src_, force, corruptPaths, retEpoch,
                            repair_path_, retRealCorruptPaths);
    if (force) {
        retRealCorruptPaths = corruptPaths;
    }
    // when corruptPaths & real corrupted paths (at NN side) mismatch
    // plus force is disabled
    // then epoch & repair_path will be cleared, with retRealCorruptPaths
    // returned
    epoch_ = retEpoch;
    corruptPaths_ = retRealCorruptPaths;
    state_ = KStartRepaired;

    HDFSLOG(DEBUG,
            "HyperStream: start repair success, path={}, corruptPaths={}",
            hyper_src_, fmt::join(corruptPaths, "-"));
}

// `CommitRepairHFile` can only be called after `StartRepairHFile`.
void HyperStream::CommitRepairHFile(
    const std::vector<std::string>& replaceNames,
    std::optional<bool> cleanTrash) {
    if (state_ != KStartRepaired || replaceNames.empty() ||
        replaceNames.size() != corruptPaths_.size()) {
        THROW(InvalidParameter,
              "HyperStream: commit repair failed, src: %s, invalid state: %d, "
              "replace size: %ld, corrupt size: %ld",
              hyper_src_.c_str(), state_, replaceNames.size(),
              corruptPaths_.size());
    }

    filesystem_.commitRepairHFile(hyper_src_, corruptPaths_, replaceNames,
                                  epoch_, cleanTrash);

    HDFSLOG(DEBUG,
            "HyperStream: commit repair success, path={}, corruptPaths={}, "
            "replaceNames={}",
            hyper_src_, fmt::join(corruptPaths_, "-"),
            fmt::join(replaceNames, "-"));

    state_ = KRepairCommitted;
}

void HyperStream::Close() {
    // The hyper file itself also needs to call complete for import.
    // NOTE: For repair, no need to close as archive's hblocks will be
    //       closed/completed by dtor of volume_worker,
    //       and by hblock's inode id (not by path).
    //       And as "hfile/.tmp.repair" is not a hfile,
    //       close/complete it will get exception from NN.
    if (state_ == KCreated || state_ == KStartImport ||
        state_ == KImportCommitted) {
        bool file_complete = false;
        std::chrono::milliseconds local_timeout =
            std::chrono::milliseconds(100);
        int retries = 6;
        while (!file_complete) {
            file_complete = filesystem_.impl->filesystem->complete(
                hyper_src_, NULL, hyper_info_->getFileId());
            --retries;
            if (!file_complete) {
                if (retries <= 0) {
                    THROW(
                        HdfsIOException,
                        "Unable to close hyper file because complete failed, src=%s, "
                        "state=%d",
                        hyper_src_.c_str(), state_);
                }
                std::this_thread::sleep_for(local_timeout);
                local_timeout *= 2;
            }
        }
        state_ = KClosed;
        HDFSLOG(DEBUG, "HyperStream: complete success, path={}, state={}",
                hyper_src_, state_);
    }
}

const std::string& HyperStream::getHyperPath() const { return hyper_src_; }

const std::string& HyperStream::getRepairPath() const { return repair_path_; }

uint64_t HyperStream::getEpoch() const { return epoch_; }

bool HyperStream::isInput() const { return (state_ == KRead); }

const std::vector<std::string>& HyperStream::getRealCorruptPaths() const {
    return corruptPaths_;
}

}  // namespace Internal
}  // namespace Hdfs