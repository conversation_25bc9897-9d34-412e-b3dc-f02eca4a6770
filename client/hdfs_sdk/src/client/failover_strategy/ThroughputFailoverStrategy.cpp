//
// Created by <PERSON><PERSON><PERSON> on 2021-05-16.
//

#include "ThroughputFailoverStrategy.h"

namespace Hdfs {
namespace Internal {

ThroughputFailoverStrategy::ThroughputFailoverStrategy(
    std::shared_ptr<SessionConfig> conf)
    : FastFailoverStrategy(conf) {
    slow_throughput_threshold_ = conf->getSlowThroughputThreshold();
    slow_duration_threshold_ms_ =
        std::chrono::milliseconds(conf->getSlowDurationThresholdMs());
    pipeline_minimun_packet_ = conf->getPipelineMinimunPacket();
    pipeline_throughput_window_ns_ =
        std::chrono::nanoseconds(conf->getPipelineThroughputWindow());
    pipeline_minimum_window_ns_ =
        std::chrono::nanoseconds(conf->getPipelineMinimumWindow());
};

int32_t ThroughputFailoverStrategy::reportAckTimeNs(
    int64_t packet_size,
    std::chrono::time_point<std::chrono::steady_clock> received_time,
    std::chrono::time_point<std::chrono::steady_clock> send_time,
    std::vector<std::chrono::nanoseconds>& ack_time_per_peer_ns,
    std::shared_ptr<Packet> packet) {
    // The return value of FastFailoverStrategy::reportAckTimeNs is always -1.
    [[maybe_unused]] int32_t replace_index =
        FastFailoverStrategy::reportAckTimeNs(packet_size, received_time,
                                              send_time, ack_time_per_peer_ns,
                                              packet);

    if (block_->getLocations().size() == 0) {
        return -1;
    } else if (block_->getLocations().size() != ack_time_per_peer_ns.size()) {
        return -1;
    }

    for (uint32_t i = 0; i < ack_time_per_peer_ns.size(); i++) {
        int64_t throughput =
            throughput_windows_[i].calculate_throughput_in_window(
                packet_size, ack_time_per_peer_ns[i], received_time);
        if (throughput >= slow_throughput_threshold_ ||
            ack_time_per_peer_ns[i].count() < kMinThrouhputableLantecyNs) {
            slow_traces_[i].is_slow = false;
            continue;
        }
        if (!slow_traces_[i].is_slow) {
            slow_traces_[i].start_time = received_time;
            slow_traces_[i].is_slow = true;
        }
        if (received_time - slow_traces_[i].start_time <
            slow_duration_threshold_ms_) {
            // The slow write process does not sustain for enough time. We do
            // not need to replace this DN currently so that the strategy can
            // resist temporary latency spike.
            continue;
        }

        HDFSLOG(INFO,
                "Find slow node via throughput. Index: {}, data_size: {},"
                "latency_ns: {}, slow_throughput_threshold_: {}",
                i, packet_size, ack_time_per_peer_ns[i].count(),
                slow_throughput_threshold_);
        return i;
    }

    return -1;
}

void ThroughputFailoverStrategy::resetPipeline(
    std::shared_ptr<LocatedBlock> block) {
    FastFailoverStrategy::resetPipeline(block);
    throughput_windows_.clear();
    slow_traces_.clear();
    for (uint32_t i = 0; i < block->getLocations().size(); i++) {
        throughput_windows_.push_back(PipelineThroughputWindow(
            pipeline_throughput_window_ns_, pipeline_minimum_window_ns_,
            pipeline_minimun_packet_));
        slow_traces_.push_back(SlowTrace(false));
    }
}

}  // namespace Internal
}  // namespace Hdfs
