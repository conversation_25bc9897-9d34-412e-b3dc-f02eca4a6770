//
// Created by re<PERSON><PERSON> on 2021-05-16.
//

#include "PipelineThroughputWindow.h"

#include "common/Logger.h"

namespace Hdfs {
namespace Internal {

#define max_int64 0x7fffffffffffffff
#define SEC_TO_NS_CONSTANT 1000000000

int64_t PipelineThroughputWindow::calculate_throughput_in_window(
    int64_t bytes, std::chrono::nanoseconds latency_ns,
    std::chrono::time_point<std::chrono::steady_clock> io_finish_timestamp) {
    total_io_bytes_in_window_ += bytes;
    packet_number_received_++;

    if (throuput_windows_.size() == 0) {
        total_io_time_ns_ += latency_ns;
        throuput_windows_.push_back(
            Throughput(bytes, latency_ns, io_finish_timestamp));
    } else {
        auto io_start_timestamp_ = io_finish_timestamp - latency_ns;
        // Get last packet to check if there is overlap in io time.
        const Throughput& last_packet = throuput_windows_.back();
        if (last_packet.io_finish_timestamp_ > io_start_timestamp_) {
            total_io_time_ns_ +=
                (io_finish_timestamp - last_packet.io_finish_timestamp_);
        } else {
            total_io_time_ns_ += latency_ns;
        }

        throuput_windows_.push_back(
            Throughput(bytes, latency_ns, io_finish_timestamp));

        // Delete packet info which is out the range of time window.
        auto time_lower_boundary = io_finish_timestamp - window_length_ns_;

        while (throuput_windows_.size() > 1) {
            Throughput earliest_packet = throuput_windows_[0];
            if (earliest_packet.io_finish_timestamp_ < time_lower_boundary) {
                total_io_bytes_in_window_ -= earliest_packet.io_bytes_;
                Throughput packet_after_earliest = throuput_windows_[1];
                if (packet_after_earliest.io_start_timestamp_ <
                    earliest_packet.io_finish_timestamp_) {
                    total_io_time_ns_ -=
                        (packet_after_earliest.io_start_timestamp_ -
                         earliest_packet.io_start_timestamp_);
                } else {
                    total_io_time_ns_ -= earliest_packet.io_cost_time_ns_;
                }
                throuput_windows_.pop_front();
            } else {
                break;
            }
        }
    }

    // There is not enough sampling time, which may cause the judgement of slow
    // node too sensitive. When the sampling time is too short, we don't decide
    // whether the node is slow.
    if (total_io_time_ns_ < mininmum_time_required_ns_ ||
        packet_number_received_ < minimun_packet_required_) {
        return max_int64;
    }

    int64_t result = total_io_bytes_in_window_ * SEC_TO_NS_CONSTANT /
                     total_io_time_ns_.count();

    HDFSLOG(DEBUG,
            "Total_io_time_ns_: {}. mininmum_time_required_ns_: {}. "
            "packet_number_received_: {}. minimun_packet_required_: {}. "
            "throughput is: {}",
            static_cast<int64_t>(total_io_time_ns_.count()),
            static_cast<int64_t>(mininmum_time_required_ns_.count()),
            packet_number_received_, minimun_packet_required_, result);
    return result;
}

}  // namespace Internal
}  // namespace Hdfs
