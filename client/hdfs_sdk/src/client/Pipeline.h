/********************************************************************
 * 2014 -
 * open source under Apache License Version 2.0
 ********************************************************************/
/**
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
#ifndef _HDFS_LIBHDFS3_CLIENT_PIPELINE_H_
#define _HDFS_LIBHDFS3_CLIENT_PIPELINE_H_

#include <cstdint>
#include <deque>
#include <memory>
#include <vector>

#include "client/DataTransferProtocolSender.h"
#include "client/Packet.h"
#include "client/PacketPool.h"
#include "client/PipelineAck.h"
#include "client/filesystem/FileSystemInter.h"
#include "common/SessionConfig.h"
#include "network/BufferedSocketReader.h"
#include "network/TcpSocket.h"
#include "server/DatanodeInfo.h"
#include "server/LocatedBlock.h"
#include "server/Namenode.h"

namespace Hdfs {
namespace Internal {

enum BlockConstructionStage {
    /**
     * The enumerates are always listed as regular stage followed by the
     * recovery stage.
     * Changing this order will make getRecoveryStage not working.
     */
    // pipeline set up for block append
    PIPELINE_SETUP_APPEND = 0,
    // pipeline set up for failed PIPELINE_SETUP_APPEND recovery
    PIPELINE_SETUP_APPEND_RECOVERY = 1,
    // data streaming
    DATA_STREAMING = 2,
    // pipeline setup for failed data streaming recovery
    PIPELINE_SETUP_STREAMING_RECOVERY = 3,
    // close the block and pipeline
    PIPELINE_CLOSE = 4,
    // Recover a failed PIPELINE_CLOSE
    PIPELINE_CLOSE_RECOVERY = 5,
    // pipeline set up for block creation
    PIPELINE_SETUP_CREATE = 6
};

static inline const char* StageToString(BlockConstructionStage stage) {
    switch (stage) {
        case PIPELINE_SETUP_APPEND:
            return "PIPELINE_SETUP_APPEND";

        case PIPELINE_SETUP_APPEND_RECOVERY:
            return "PIPELINE_SETUP_APPEND_RECOVERY";

        case DATA_STREAMING:
            return "DATA_STREAMING";

        case PIPELINE_SETUP_STREAMING_RECOVERY:
            return "PIPELINE_SETUP_STREAMING_RECOVERY";

        case PIPELINE_CLOSE:
            return "PIPELINE_CLOSE";

        case PIPELINE_CLOSE_RECOVERY:
            return "PIPELINE_CLOSE_RECOVERY";

        case PIPELINE_SETUP_CREATE:
            return "PIPELINE_SETUP_CREATE";

        default:
            return "UNKNOWN STAGE";
    }
}

}  // namespace Internal
}  // namespace Hdfs

#endif /* _HDFS_LIBHDFS3_CLIENT_PIPELINE_H_ */
