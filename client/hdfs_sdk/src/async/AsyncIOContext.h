#ifndef NATIVE_HDFS_CLIENT_ASYNC_ASYNCIOCONTEXT_H
#define NATIVE_HDFS_CLIENT_ASYNC_ASYNCIOCONTEXT_H

#include <folly/io/async/AsyncTransport.h>

#include <atomic>
#include <boost/uuid/uuid_generators.hpp>
#include <boost/uuid/uuid_io.hpp>
#include <future>
#include <memory>
#include <unordered_set>
#include <utility>
#include <vector>

#include "client/CoreLogRecords.h"
#ifndef DISABLE_BYTERPC
#include "ClientDatanodeV2Protocol.pb.h"
#include "byterpc/builder.h"
#include "byterpc/callback.h"
#include "byterpc/exec_ctx.h"
#include "byterpc/io_buf.h"
#include "byterpc/server.h"
#include "byterpc/thread/ev_thread_helper.h"
#include "byterpc/thread/polling_thread_helper.h"
#include "byterpc/util/numautils.h"
#include "byterpc/util/timestamp.h"
#endif
#include "client/hdfsAsyncContext.h"
#include "common/DataNodeManager.h"
#include "common/HdfsEnv.h"
#include "common/NetUtil.h"
#include "common/SessionConfig.h"
#include "monitor/macro.h"
#include "server/LocatedBlock.h"

namespace Hdfs {
namespace Internal {

struct AsyncReadResult {
    // The final read_status_. Maybe we can pass error_string in the future.
    hdfsStatus read_status_{hdfsStatus::STATUS_OK};

    // These are expected to be used in one
    std::atomic<size_t> bytes_has_read_{0};
    std::atomic<size_t> task_finished_{0};
    // The value of all tasks to finish should never change.
    size_t task_to_finish_count_{0};
    std::atomic<bool> failed_{false};
};
#ifndef DISABLE_BYTERPC
struct ByteRpcCall {
    ByteRpcCall(std::string dn_ip, uint32_t port,
                byterpc::Builder::ChannelOptions options)
        : dn_ip_string_(dn_ip),
          port_(port),
          options_(options),
          req_(new hadoop::hdfs::ClientReadBlockRequestProto),
          resp_(new hadoop::hdfs::ClientReadBlockResponseProto) {}
    std::string dn_ip_string_;
    uint32_t port_;
    byterpc::Builder::ChannelOptions options_;
    std::shared_ptr<hadoop::hdfs::ClientReadBlockRequestProto> req_;
    std::shared_ptr<hadoop::hdfs::ClientReadBlockResponseProto> resp_;
    std::shared_ptr<hadoop::hdfs::ClientDatanodeIOService_Stub> stub_;
    byterpc::Controller* controller_;
    std::shared_ptr<byterpc::Builder::Channel> channel_;

    bool init() {
        byterpc::util::ip_t dn_ip;
        int res = byterpc::util::str2ip(dn_ip_string_.c_str(), &dn_ip);
        if (res == -1) {
            HDFS_LOG(ERROR,
                     "Failed to transfer dn_ip {} to byterpc::util::ip_t",
                     dn_ip_string_);
            return false;
        }
        byterpc::util::EndPoint ep(dn_ip, port_);
        byterpc::Builder builder;
        channel_ = builder.BuildChannel(ep, options_);

        stub_.reset(
            new hadoop::hdfs::ClientDatanodeIOService_Stub(channel_.get()));
        controller_ = builder.CreateSessionController(
            byterpc::PROTOCOL_BYTE_STD, options_._rpc_timeout_ms * 1000);
        HDFSLOG(
            DEBUG,
            "Connected to dn: {}, port: {}, channel {}, stub {}, controller {}",
            dn_ip_string_, port_, static_cast<void*>(channel_.get()),
            static_cast<void*>(stub_.get()), static_cast<void*>(controller_));
        return true;
    }
    ~ByteRpcCall() {}
};
#endif
struct AsyncReadContext {
    AsyncReadContext(int64_t offset, int32_t length, char* buffer,
                     LocatedBlock block,
                     std::shared_ptr<AsyncReadResult> result,
                     inner_hdfs_io_context user_context_,
                     std::unique_ptr<IOTrace> trace, const std::string& path,
                     bool is_byterpc,
                     std::shared_ptr<Hdfs::Internal::SessionConfig> sconf)
        : offset_(offset),
          length_(length),
          buffer_(buffer),
          buffer_iov_({}),
          block_(block),
          result_(result),
          user_context_(user_context_),
          io_trace_(std::move(trace)),
          path_(path),
          confBtrace_(sconf->getBTrace()),
          sconf_(sconf),
          is_byterpc_(is_byterpc),
          ctx_id_(boost::uuids::random_generator()()) {
        init();
    }

    AsyncReadContext(int64_t offset, int32_t length, std::vector<iovec> iov,
                     LocatedBlock block,
                     std::shared_ptr<AsyncReadResult> result,
                     inner_hdfs_io_context user_context_,
                     std::unique_ptr<IOTrace> trace, const std::string& path,
                     bool is_byterpc,
                     std::shared_ptr<Hdfs::Internal::SessionConfig> sconf)
        : offset_(offset),
          length_(length),
          buffer_(nullptr),
          buffer_iov_(std::move(iov)),
          block_(block),
          result_(result),
          user_context_(user_context_),
          io_trace_(std::move(trace)),
          path_(path),
          confBtrace_(sconf->getBTrace()),
          sconf_(sconf),
          is_byterpc_(is_byterpc),
          ctx_id_(boost::uuids::random_generator()()) {
        init();
    }

    void init() {
        // We expect the keepalive time on the Datanode side to always be 1
        // second longer than the internal conn cache of the client.
        dn_conn_keepalive_ms_ = sconf_->connCacheKeepAliveMs_ + 1000;
        hedged_base_timeout_ = sconf_->getAsyncHedgedBaseTimeout();
        hedged_extra_timeout_ = sconf_->get_async_hedge_extra_timeout();
        task_timeout_ = sconf_->getAsyncTaskReadTimeout();
        max_try_loops_ = sconf_->async_failover_times_;
        conn_cache_enable_ = sconf_->isEnableConnCache();
        nodes_count_ = block_.getLocations().size();
        auto& nodes = block_.mutableLocations();
        if (is_byterpc_) {
            HdfsEnv::Get()->GetByteRpcDataNodeManager()->SortNodes(nodes);
        } else {
            HdfsEnv::Get()->GetDataNodeManager()->SortNodes(nodes);
        }
        HdfsEnv::Get()->GetStatsMonitor()->AsyncPReadNumInrc();
    }

    virtual ~AsyncReadContext() {
        // 析构的时候，所有的hedged read线程看起来都已经确保结束了？
        HdfsEnv::Get()->GetStatsMonitor()->AsyncPReadNumDesc();
#ifndef DISABLE_BYTERPC
        calls.clear();
#endif
    }

    void SetStartTime() {
        // this function is called in user thread not evb
        path_trace_ = g_paths_trace.GetPathTrace(io_trace_->GetHdfsFile());
    }

    int32_t CalculateHedgedTimeout() const {
        int32_t extra_timeout = sconf_->get_async_hedge_extra_timeout();
        // 3MB/s (determine whether hdfs is slow), so 128KB need 42ms at least,
        // hedged_base_timeout_ is 50ms by default.
        // hedged_extra_timeout_ is 500ms by default.(for connect time cost)
        int32_t base_timeout = (length_ >> 17) * hedged_base_timeout_;
        if (base_timeout == 0) {
            base_timeout = hedged_base_timeout_;
        }
        return base_timeout * std::pow(2, launch_hedged_read_count_) +
               extra_timeout;
    }

    std::string IdString() const {
        return boost::uuids::to_string(ctx_id_).substr(0, 8);
    }

    void MarkNodeFailed(size_t dn_index) {
        ++failure_count_;
        failedNodeSet_.insert(dn_index);
        user_context_.hasBadNode->store(true);
    }

    bool ShouldStop() {
        if (failedNodeSet_.size() >= nodes_count_) {
            if (++cur_try_loops_ >= max_try_loops_) {
                return true;
            } else {
                HDFSLOG(
                    INFO,
                    "[WARN] [{}] start another retry loop, cur: {}, max: {}",
                    IdString(), cur_try_loops_, max_try_loops_);
                failedNodeSet_.clear();
            }
        }
        return false;
    }
#ifndef DISABLE_BYTERPC
    virtual std::shared_ptr<ByteRpcCall> initByteRpc() {
        auto& locs = block_.getLocations();
        current_dn_idx_ = current_dn_idx_ % locs.size();
        on_going_try_count_++;
        auto& chosen_dn = locs[current_dn_idx_];
        std::string chosen_dn_ip(chosen_dn.getIpAddr());
        uint32_t port = chosen_dn.getByteRpcPort();
        if (port == 0) {
            HDFS_LOG(ERROR, "[{}] Failed to init ByteRPC call as port is 0",
                     IdString(), chosen_dn_ip);
            return nullptr;
        }
        byterpc::util::ip_t dn_ip;
        byterpc::Builder::ChannelOptions options;
        options._rpc_timeout_ms = sconf_->byte_rpc_timeout_ms_;
        options._connect_timeout_ms = sconf_->byte_rpc_connect_timeout_ms_;
        options._trans_type = byterpc::TYPE_KERNEL_TCP;
        int res = byterpc::util::str2ip(chosen_dn_ip.c_str(), &dn_ip);
        if (res == -1) {
            HDFS_LOG(ERROR,
                     "[{}] Failed to transform dn_ip {} to byterpc::util::ip_t",
                     IdString(), chosen_dn_ip);
            return nullptr;
        }
        std::shared_ptr<ByteRpcCall> call =
            std::make_shared<ByteRpcCall>(chosen_dn_ip, port, options);
        if (!call->init()) {
            HdfsEnv::Get()->GetByteRpcDataNodeManager()->MarkConnFailure(
                chosen_dn_ip);
            HDFS_LOG(ERROR, "[{}] Failed to initialize ByteRpcCall",
                     IdString());
            return nullptr;
        }
        get_connect_time_us_ = GetNowTimeUs();
        return call;
    }
#endif

    // Offset inside the block to read.
    int64_t offset_;
    // Length to read in the block.
    int32_t length_;
    // char* to read block data.
    char* buffer_;
    // io_vec to read block data into non-continuous memory
    // it is a sub-vec from user_context, each iovec is newly created
    std::vector<iovec> buffer_iov_;
    // As hedged read exists, there maybe multiple read for one block.
    // We must use another bool to mark whether one readTask is finished.
    bool finished_{false};

    // All Location of this block. This will be used when read failed.
    // And another dn should be chosen for failover.
    LocatedBlock block_;
    // Used to store the result of read.
    // As there maybe cross block read for one Pread,
    // this maybe shared between multiple AsyncReadContext
    std::shared_ptr<AsyncReadResult> result_;
    // The datanode index in block_ to read.
    int32_t current_dn_idx_{0};
    // If tried all nodes, then consider pread task failed.
    std::unordered_set<size_t> failedNodeSet_;
    int32_t failure_count_{0};
    int32_t nodes_count_{0};
    int32_t cur_try_loops_{0};
    int32_t max_try_loops_{1};
    int32_t launch_hedged_read_count_{0};
    uint32_t on_going_try_count_{0};
    int32_t hedged_base_timeout_{100};
    int32_t hedged_extra_timeout_{500};
    int32_t task_timeout_{1800000};
    uint32_t dn_conn_keepalive_ms_{0};

    // User context, which used to trigger callback.
    inner_hdfs_io_context user_context_;
    std::unique_ptr<IOTrace> io_trace_;
    std::shared_ptr<PathTrace> path_trace_;

    int64_t start_time_us_{0};
    int64_t get_connect_time_us_{0};
    int64_t read_block_us_{0};
    int64_t end_time_us_{0};

    folly::AsyncTimeout* launch_hedged_read_timer_{nullptr};
    folly::AsyncTimeout* stop_task_timer_{nullptr};

    std::string path_;
    const SessionConfigBTrace& confBtrace_;

    std::shared_ptr<Hdfs::Internal::SessionConfig> sconf_{nullptr};

    bool is_byterpc_{false};

    boost::uuids::uuid ctx_id_;

    bool conn_cache_enable_{true};

    CoreLogRecords core_log_records_;

#ifndef DISABLE_BYTERPC
    std::vector<std::shared_ptr<ByteRpcCall>> calls;
#endif
};
}  // namespace Internal
}  // namespace Hdfs
#endif  // NATIVE_HDFS_CLIENT_ASYNC_ASYNCIOCONTEXT_H
