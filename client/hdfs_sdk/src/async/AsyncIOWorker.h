#pragma once

#include <folly/executors/CPUThreadPoolExecutor.h>
#include <folly/executors/ThreadPoolExecutor.h>
#include <folly/io/async/EventBase.h>

#include <memory>
#include <thread>

#include "async/AsyncConnectionManager.h"
#include "async/AsyncIOContext.h"
#ifndef DISABLE_BYTERPC
#include "byterpc/thread/ev_thread_helper.h"
#endif

namespace Hdfs {
namespace Internal {

// AsyncIOWorker is the eventloop for executing the AsyncReadContext.
class AsyncIOWorker {
   public:
    AsyncIOWorker(std::shared_ptr<SessionConfig> sconf,
                  std::shared_ptr<AsyncConnectionManager> cmgr);
    ~AsyncIOWorker();
    void Stop();

    void registerTaskToEventLoop(std::shared_ptr<AsyncReadContext> task);
    void registerByteRpcTaskToEventLoop(std::shared_ptr<AsyncReadContext> task);

   private:
    std::shared_ptr<std::atomic<bool>> stop_ =
        std::make_shared<std::atomic<bool>>(false);
    std::unique_ptr<folly::EventBase> evb_;
    std::shared_ptr<AsyncConnectionManager> cmgr_;
    std::thread looper_;
};

// AsyncIOWorkerGroup consists of multiple AsyncIOWorker, which can be seen
// as a thread pool.
class AsyncIOWorkerGroup {
   public:
    AsyncIOWorkerGroup(int32_t worker_group_size = kDefaultWorkerGroupSize,
                       std::shared_ptr<SessionConfig> sconf = nullptr);
    ~AsyncIOWorkerGroup();
    void Stop();

    AsyncIOWorker& chooseWorkerByRoundRobin();

    AsyncIOWorker& chooseWorkerByHash(int64_t value);

    void IncFlyAsyncRead(uint32_t val = 1);

    void DecFlyAsyncRead(uint32_t val = 1);

    uint32_t GetFlyAsyncRead() const;

   private:
    static constexpr uint32_t kDefaultWorkerGroupSize = 4;
    std::shared_ptr<std::atomic<bool>> stop_ =
        std::make_shared<std::atomic<bool>>(false);

    // If worker_group_size_ is set to 1, the throughput for one file can
    // reach 1.4GB/S However if we set worker_group_size_ to 4, and use round
    // robin to choose worker for each task the throughput for one file can only
    // reach 1.2 ~ 1.3GB/S.(Use Java DN with page-cache)
    uint32_t worker_group_size_;

    std::atomic<uint32_t> current_index_{0};
    std::vector<std::unique_ptr<AsyncIOWorker>> worker_groups_;

    std::atomic<uint32_t> num_fly_async_read_{0};
};
#ifndef DISABLE_BYTERPC
class RpcThreadGroup {
   public:
    RpcThreadGroup(int32_t worker_group_size = 8) {
        byterpc::InitOptions init_opt(false, false, "hdfs_byterpc");
        if (byterpc::ExecCtx::Init(init_opt) < 0) {
            assert(false);
        }
        for (int32_t i = 0; i < worker_group_size; ++i) {
            byterpc::ThreadOptions th_options;
            th_options._transport = byterpc::TYPE_KERNEL_TCP;
            th_options._thread_name = "read_rpc_thread";
            auto th = new byterpc::EvThreadHelper();
            th->Init(th_options);
            th->Start();
            workers_.emplace_back(th);
        }
    }

    ~RpcThreadGroup() {
        for (size_t i = 0; i < workers_.size(); ++i) {
            workers_[i]->Stop();
        }
        for (size_t i = 0; i < workers_.size(); ++i) {
            workers_[i]->Join();
        }
        byterpc::ExecCtx::Fini();
    }

    void Invoke(Closure<void>* closure) {
        workers_[(++index_ % workers_.size())]->Invoke(closure);
    }

   private:
    std::vector<std::unique_ptr<byterpc::ThreadBase>> workers_;
    std::atomic<size_t> index_{0};
};
#endif

class AsyncTaskExecutor {
   public:
    AsyncTaskExecutor() {
        executor_ = std::make_unique<folly::CPUThreadPoolExecutor>(
            4,
            std::make_shared<folly::NamedThreadFactory>("AsyncTaskExecutor"));
    }
    ~AsyncTaskExecutor() = default;
    template <typename Func>
    void AddTask(Func&& func) {
        executor_->add(std::forward<Func>(func));
    }
    void Stop() {
        if (executor_) {
            executor_->stop();
            executor_->join();
            executor_.reset();
        }
    }

   private:
    std::unique_ptr<folly::CPUThreadPoolExecutor> executor_;
};

}  // namespace Internal
}  // namespace Hdfs
