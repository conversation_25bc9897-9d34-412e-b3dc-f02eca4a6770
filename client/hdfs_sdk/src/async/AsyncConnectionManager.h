#pragma once

#include <folly/container/EvictingCacheMap.h>
#include <folly/io/async/AsyncSocket.h>

#include <array>
#include <atomic>
#include <chrono>
#include <memory>
#include <mutex>
#include <thread>
#include <unordered_map>
#include <vector>

#include "common/SessionConfig.h"
#include "monitor/byted/StatsUtil.h"
#include "monitor/macro.h"

namespace Hdfs {
namespace Internal {

// ConnnectionManager is consist of multiple connection pools belong to
// different datanode. ConnectioinManager also cleans the connection which has
// not used for long time.
class AsyncConnectionManager {
   public:
    const static size_t CONNECTION_MAP_ARRAY_SIZE = 64;
    AsyncConnectionManager();
    ~AsyncConnectionManager();

    // Get one cached connection which connected ip.
    // @param ip. The ip of datanode.
    // @return the folly_socket which connect to dn. Or nullptr if cache miss.
    std::shared_ptr<folly::AsyncSocket> getConnection(const std::string& ip);

    // Cache one connection to connection manager
    // @param ip. The ip of datanode
    // @socket the folly::socket that has already connected to datanode.
    void addConnection(const std::string& ip,
                       std::shared_ptr<folly::AsyncSocket> socket);

    void updateConfig(std::shared_ptr<SessionConfig> config);

   private:
    void doSocketCleanWork();

    using SocketTimePair =
        std::pair<std::shared_ptr<folly::AsyncSocket>,
                  std::chrono::time_point<std::chrono::steady_clock>>;
    using ConnectionPool = std::deque<SocketTimePair>;
    using ConnectionPoolMap =
        std::unordered_map<std::string, std::unique_ptr<ConnectionPool>>;
    // used to find the connection_pool belong to certain ip.
    std::array<ConnectionPoolMap, CONNECTION_MAP_ARRAY_SIZE>
        ip_to_pool_map_arr_;

    // async cleaner worker
    bool enabled_{false};
    std::array<std::shared_ptr<std::mutex>, CONNECTION_MAP_ARRAY_SIZE>
        clean_mu_arr_;
    std::shared_ptr<std::atomic<bool>> stop_ =
        std::make_shared<std::atomic<bool>>(false);
    std::shared_ptr<std::atomic<bool>> inited_config_ =
        std::make_shared<std::atomic<bool>>(false);
    std::condition_variable cv_;
    std::mutex cv_m_;
    std::thread clean_worker_;
    size_t maxAddrKeys_{10000};
    size_t connTTLMillis_{3000};
    size_t socketPerIpSize_{12};
    std::hash<std::string> hash_;
};

}  // namespace Internal
}  // namespace Hdfs
