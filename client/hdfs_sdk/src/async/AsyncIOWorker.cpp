#include "async/AsyncIOWorker.h"

#include "async/PReadTaskRunner.h"
#include "common/Logger.h"
#include "monitor/macro.h"

namespace Hdfs {
namespace Internal {

AsyncIOWorker::AsyncIOWorker(std::shared_ptr<SessionConfig> sconf,
                             std::shared_ptr<AsyncConnectionManager> cmgr) {
    evb_ = std::make_unique<folly::EventBase>();
    cmgr_ = cmgr;
    looper_ = std::thread([&]() { evb_->loopForever(); });
    SetThreadName(looper_, "hdfs_async_io");
}

AsyncIOWorker::~AsyncIOWorker() { Stop(); }

void AsyncIOWorker::Stop() {
    auto old_state = stop_->exchange(true);
    if (old_state) {
        return;
    }
    evb_->terminateLoopSoon();
    looper_.join();
    evb_.reset();
    // cmgr_ must be destructed after evb_.
    cmgr_.reset();
}

void AsyncIOWorker::registerTaskToEventLoop(
    std::shared_ptr<AsyncReadContext> task) {
    task->SetStartTime();
    evb_->runInEventBaseThread([&, task = std::move(task)] {
        PReadTaskRunner::getConnectionAndLaunchReadTask(evb_.get(), cmgr_.get(),
                                                        std::move(task));
    });
}

AsyncIOWorkerGroup::~AsyncIOWorkerGroup() { Stop(); }

void AsyncIOWorkerGroup::Stop() {
    auto old_state = stop_->exchange(true);
    if (old_state) {
        return;
    }
    for (auto& worker : worker_groups_) {
        worker->Stop();
    }
}

AsyncIOWorkerGroup::AsyncIOWorkerGroup(int32_t worker_group_size,
                                       std::shared_ptr<SessionConfig> sconf) {
    if (worker_group_size <= 0) {
        worker_group_size_ = kDefaultWorkerGroupSize;
    } else {
        worker_group_size_ = static_cast<uint32_t>(worker_group_size);
    }

    auto cmgr = std::make_shared<AsyncConnectionManager>();
    cmgr->updateConfig(sconf);

    for (uint32_t i = 0; i < worker_group_size_; i++) {
        worker_groups_.push_back(std::make_unique<AsyncIOWorker>(sconf, cmgr));
    }
}

AsyncIOWorker& AsyncIOWorkerGroup::chooseWorkerByRoundRobin() {
    uint32_t idx = current_index_.fetch_add(1, std::memory_order_relaxed);
    return *worker_groups_[idx % worker_group_size_];
}

AsyncIOWorker& AsyncIOWorkerGroup::chooseWorkerByHash(int64_t value) {
    return *worker_groups_[static_cast<uint32_t>(value) % worker_group_size_];
}

void AsyncIOWorkerGroup::IncFlyAsyncRead(uint32_t val) {
    num_fly_async_read_.fetch_add(val, std::memory_order_relaxed);
}

void AsyncIOWorkerGroup::DecFlyAsyncRead(uint32_t val) {
    num_fly_async_read_.fetch_sub(val, std::memory_order_relaxed);
}

uint32_t AsyncIOWorkerGroup::GetFlyAsyncRead() const {
    return num_fly_async_read_.load(std::memory_order_relaxed);
}

}  // namespace Internal
}  // namespace Hdfs
