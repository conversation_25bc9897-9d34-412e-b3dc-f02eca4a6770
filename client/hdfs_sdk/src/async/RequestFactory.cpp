#include "RequestFactory.h"

#include <google/protobuf/io/coded_stream.h>

#include "common/Logger.h"
#include "datatransfer.pb.h"
#include "platform.h"
#include "rpc/ByteTrace.h"

namespace Hdfs {
namespace Internal {

std::unique_ptr<folly::IOBuf> RequestFactory::getRequestBuffer(
    const std::shared_ptr<AsyncReadContext>& task) {
    OpReadBlockProto op;

    op.set_len(task->length_);
    op.set_offset(task->offset_);
    op.mutable_header()->set_clientname(default_client_name);
    op.set_sendchecksums(true);
    if (task->dn_conn_keepalive_ms_ > 4000) {
        op.set_connectionkeepalive(true);
        op.set_keepalivetimems(task->dn_conn_keepalive_ms_);
    }

    op.mutable_header()->mutable_baseheader()->mutable_block()->set_poolid(
        task->block_.getPoolId());
    op.mutable_header()->mutable_baseheader()->mutable_block()->set_blockid(
        task->block_.getBlockId());
    op.mutable_header()
        ->mutable_baseheader()
        ->mutable_block()
        ->set_generationstamp(task->block_.getGenerationStamp());
    op.mutable_header()->mutable_baseheader()->mutable_block()->set_numbytes(
        task->length_);

    ByteTraceHelper::Instance()->SetBaseHeaderProtoBySessionConfigBTrace(
        task->path_, task->confBtrace_,
        *op.mutable_header()->mutable_baseheader());

    HDFSLOG(DEBUG, "[RequestFactory] request is: {}", op.ShortDebugString());

    int request_byte_size = static_cast<int>(op.BYTE_SIZE());

    // For write_op 0x001C is the DATA_TRANSFER_VERSION between client and
    // datanode. See DataNode:
    // org.apache.hadoop.hdfs.protocol.datatransfer.Receiver#readOp 0x51 means
    // Op#READ_BLOCK. See DataNode:
    // org.apache.hadoop.hdfs.protocol.datatransfer.Op
    char write_op[] = {0x00, 0x1C, 0x51};
    int max_size = sizeof(write_op) + sizeof(int32_t) + request_byte_size;

    std::unique_ptr<folly::IOBuf> buf(folly::IOBuf::create(max_size));
    memcpy(buf->writableData(), write_op, sizeof(write_op));
    buf->append(sizeof(write_op));

    size_t size =
        google::protobuf::io::CodedOutputStream::WriteVarint32ToArray(
            request_byte_size, buf->writableData() + sizeof(write_op)) -
        buf->data() - sizeof(write_op);
    buf->append(size);

    // Serialize request.
    op.SerializeToArray(buf->writableData() + buf->length(), request_byte_size);
    buf->append(request_byte_size);
    return buf;
}

std::unique_ptr<folly::IOBuf> RequestFactory::getStatusBuffer() {
    Internal::ClientReadStatusProto status;
    status.set_status(Internal::Status::DT_PROTO_SUCCESS);
    size_t serialized_size = (size_t)status.BYTE_SIZE();
    std::unique_ptr<folly::IOBuf> buf =
        folly::IOBuf::create(serialized_size + sizeof(uint32_t));
    size_t written =
        google::protobuf::io::CodedOutputStream::WriteVarint32ToArray(
            serialized_size, buf->writableTail()) -
        buf->data();
    buf->append(written);
    status.SerializeToArray(buf->writableTail(), serialized_size);
    buf->append(serialized_size);
    return buf;
}

}  // namespace Internal
}  // namespace Hdfs
