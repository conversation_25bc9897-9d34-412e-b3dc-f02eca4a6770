#pragma once

#include <boost/uuid/nil_generator.hpp>
#include <boost/uuid/string_generator.hpp>
#include <boost/uuid/uuid.hpp>
#include <boost/uuid/uuid_io.hpp>
#include <string>

namespace bytecool_sdk {

struct UUID {
    boost::uuids::uuid val;

    UUID() : val(boost::uuids::nil_generator()()) {}
    explicit UUID(boost::uuids::uuid val) : val(val) {}
    explicit UUID(const std::string& str)
        : val(boost::uuids::string_generator()(str)) {}
    std::string ToString() const { return boost::uuids::to_string(val); }
    std::size_t Size() const { return val.size(); }
    bool operator==(const UUID& rhs) const { return val == rhs.val; }
    bool operator!=(const UUID& rhs) const { return val != rhs.val; }
    bool operator<(const UUID& rhs) const { return val < rhs.val; }
};

using ObjectId = UUID;
using ArchiveId = UUID;

}  // namespace bytecool_sdk
