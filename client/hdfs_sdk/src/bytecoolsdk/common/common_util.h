#pragma once

#include <rapidjson/document.h>
#include <rapidjson/error/en.h>
#include <stdio.h>

#include <string>
#include <vector>

#include "bytecoolsdk/common/result.h"
#include "bytecoolsdk/common/uuid.h"

namespace bytecool_sdk {

struct VolumeInfo {
    std::string file_name;

    std::string ToString() const {
        std::string s = "(FileName: " + file_name + ")";
        return s;
    }
};

struct ArchiveInfo {
    ArchiveId archive_id;
    bool is_data;
    std::string tenant_name;
    std::string expire_date;
    std::vector<VolumeInfo> volumes;
    bool is_hyperfile;
    // Path of the archive: /dir/of/path/archive_id
    std::string path;
    int vol_num = 0;

    std::string ToString() const {
        std::string s = "[Archive ID: " + archive_id.ToString() +
                        ", is_data: " + std::to_string(is_data) +
                        ", tenant: " + tenant_name +
                        ", exipre date: " + expire_date + ", path: " + path +
                        ", is HyperFile: " + (is_hyperfile ? "true" : "false") +
                        ", volume number: " + std::to_string(vol_num) +
                        ", Volumes = [";
        for (const auto& vol : volumes) {
            s += vol.ToString() + ",";
        }
        s += "]]";
        return s;
    }
};

std::ostream& operator<<(std::ostream& os, const ArchiveInfo& arch);

struct ObjectInfo {
    ObjectId object_id;
    std::string object_name;
    std::string tenant_name;
    std::string expire_date;
    size_t object_size;
    size_t phys_object_size;
    size_t object_checksum;
    size_t ec_chunk_size;
    size_t obj_version;

    ArchiveId data_archive_id;
    size_t data_shard_offset;

    ArchiveId parity_archive_id;
    size_t parity_shard_offset;

    ObjectInfo() {}
    ObjectInfo(const ObjectId& id, const std::string& name, size_t size,
               const std::string& tenant, const std::string& expire_date)
        : object_id(id),
          object_name(name),
          tenant_name(tenant),
          expire_date(expire_date),
          object_size(size),
          obj_version(0) {}
    ObjectInfo(const ObjectId& id, const std::string& name,
               const std::string& tenant, const std::string& expire_date,
               size_t obj_size, size_t phys_size, size_t checksum,
               size_t chunk_size, size_t version, const ArchiveId& data_arch,
               size_t data_off, const ArchiveId& parity_arch, size_t parity_off)
        : object_id(id),
          object_name(name),
          tenant_name(tenant),
          expire_date(expire_date),
          object_size(obj_size),
          phys_object_size(phys_size),
          object_checksum(checksum),
          ec_chunk_size(chunk_size),
          obj_version(version),
          data_archive_id(data_arch),
          data_shard_offset(data_off),
          parity_archive_id(parity_arch),
          parity_shard_offset(parity_off) {}

    ObjectInfo(const ObjectInfo& o2) = default;
    std::string ToString() const {
        std::string s =
            "[Object ID: " + object_id.ToString() + ", Name: " + object_name +
            ", Tenant: " + tenant_name +
            ", Size = " + std::to_string(object_size) +
            ", Phys size = " + std::to_string(phys_object_size) +
            ", Checksum = " + std::to_string(object_checksum) +
            ", EC chunk size = " + std::to_string(ec_chunk_size) +
            ", Data archive ID = " + data_archive_id.ToString() +
            ", Data shard offset = " + std::to_string(data_shard_offset) +
            ", Parity archive ID = " + parity_archive_id.ToString() +
            ", Parity shard offset = " + std::to_string(parity_shard_offset) +
            ", Object version = " + std::to_string(obj_version) +
            ", Expire date = " + expire_date + "]";
        return s;
    }
};

std::ostream& operator<<(std::ostream& os, const ObjectId& object_id);

// TODO(renming):: move this function to base/
Result CheckUUIDFormat(const std::string& uuid_str);
Result CheckDateFormat(const std::string& date_str);

typedef struct _ErasureCodeParameters {
    uint32_t num_data_units;
    uint32_t num_parity_units;
    uint32_t chunk_size;

    std::string ToString() const {
        std::string s = "[Data units: " + std::to_string(num_data_units) +
                        ", Parity units: " + std::to_string(num_parity_units) +
                        ", Chunk size = " + std::to_string(chunk_size) + "]";
        return s;
    }
} ErasureCodeParameters;

typedef struct _ArchiveWriterManagerParameters {
    uint32_t max_archive_num_;
    std::string doc_root_;
} ArchiveWriterManagerParameters;

size_t CalculateSuggestedChunkSize(const ssize_t chunk_size_hint,
                                   const size_t max_ec_chunk_size,
                                   const int data_units,
                                   const size_t object_size);

size_t RoundUp(size_t what, size_t step_size);

std::ostream& operator<<(std::ostream& os, const ObjectInfo& obj);

namespace body_key {
#define DEFINE_BODY_KEY(name) const std::string name = "body_key:" #name

DEFINE_BODY_KEY(OBJECT_NAME);
DEFINE_BODY_KEY(OBJECT_SIZE);
DEFINE_BODY_KEY(OBJECT_ID);
DEFINE_BODY_KEY(OBJECT_DATA_FG_ID);
DEFINE_BODY_KEY(OBJECT_PARITY_FG_ID);
DEFINE_BODY_KEY(OBJECT_DATA_FG_OFFSET);
DEFINE_BODY_KEY(OBJECT_PARITY_FG_OFFSET);

}  // namespace body_key

void AppendAsMetricsTags(std::string& tags, const std::string& key,
                         const std::string& val);

}  // namespace bytecool_sdk
