#pragma once

#include <chrono>
#include <condition_variable>
#include <cstdint>
#include <mutex>

namespace bytecool_sdk {
class CountDownLatch {
   public:
    explicit CountDownLatch(size_t count) : count_(count) {}

    void Reset(size_t count) { count_ = count; }

    void CountDown();

    bool Await(
        std::chrono::milliseconds timeout = std::chrono::milliseconds(-1));

   private:
    size_t count_{0};
    std::mutex mutex_;
    std::condition_variable cv_;
};

}  // namespace bytecool_sdk
