#pragma once

#include <sys/types.h>

#include <algorithm>
#include <fstream>
#include <functional>
#include <future>
#include <ios>
#include <iostream>
#include <map>
#include <memory>
#include <mutex>
#include <streambuf>
#include <string>
#include <utility>
#include <vector>

#include "common/assert.h"

#pragma GCC diagnostic ignored "-Wnon-virtual-dtor"
#pragma GCC diagnostic ignored "-Wmissing-field-initializers"
#include <boost/asio/buffers_iterator.hpp>
#pragma GCC diagnostic warning "-Wmissing-field-initializers"
#pragma GCC diagnostic warning "-Wnon-virtual-dtor"

#include "bytecoolsdk/application/ec_read_streambuf.h"
#include "bytecoolsdk/archive/archive_reader.h"
#include "bytecoolsdk/common/context.h"
#include "bytecoolsdk/common/result.h"
#include "bytecoolsdk/ec/dump.h"
#include "bytecoolsdk/ec/erasure_coder.h"
#include "bytecoolsdk/fs/backend_fs.h"
#include "bytecoolsdk/meta/meta_manager.h"

namespace bytecool_sdk {

class HDFSIOService {
   public:
    HDFSIOService(const ErasureCodeParameters& ec_params,
                  std::shared_ptr<BackendFileSystem> bfs,
                  std::shared_ptr<MetaManager> meta_mgr,
                  std::shared_ptr<ArchiveReaderManager> archive_reader_manager,
                  std::shared_ptr<Hdfs::Internal::SessionConfig> session_config)
        : ec_params_(ec_params),
          backend_fs_(bfs),
          meta_mgr_(meta_mgr),
          archive_reader_mgr_(archive_reader_manager),
          session_config_(session_config) {}

    // Only for read.
    HDFSIOService(
        const ErasureCodeParameters& ec_params,
        std::shared_ptr<BackendFileSystem> bfs,
        std::shared_ptr<MetaManager> meta_mgr,
        std::shared_ptr<Hdfs::Internal::SessionConfig> session_config);

    ECReadStreambufPtr MakeECReadStreambufPtr(
        Context& ctx, const ObjectInfo& object_info, size_t offset = 0,
        const std::vector<int>& detected_error_ind = {},
        const std::map<int, std::string>& temp_fixing_volumes = {});

    ECReadStreamPtr MakeECReadStreamWithOffsetPtr(
        Context& ctx, const ObjectInfo& object_info, size_t offset,
        const std::vector<int>& detected_error_ind = {},
        const std::map<int, std::string>& temp_fixing_volumes = {});

    Result GetObjectByID(Context& ctx, const ObjectId& object_id,
                         ObjectInfo& out);

    ErasureCodeParameters GetEcParams() { return ec_params_; }

    std::shared_ptr<Tinker> MakeTinker(ErasureCodeParameters ec_params);
    ErasureCodeParameters ec_params_;
    std::shared_ptr<BackendFileSystem> backend_fs_;
    std::shared_ptr<MetaManager> meta_mgr_;

    std::shared_ptr<ArchiveReaderManager> archive_reader_mgr_;
    std::shared_ptr<Hdfs::Internal::SessionConfig> session_config_{nullptr};
};

}  // namespace bytecool_sdk
