#pragma once

#include <glog/logging.h>

#include <unordered_map>

#include "bytecoolsdk/fs/backend_file.h"
#include "server/HyperFileMeta.h"

namespace bytecool_sdk {

#ifndef NDEBUG
class BackendFileSystem
    : public std::enable_shared_from_this<BackendFileSystem> {
#else
class BackendFileSystem {
#endif
   public:
    BackendFileSystem() = default;
    virtual ~BackendFileSystem() = default;

    BackendFileSystem(const BackendFileSystem&) = delete;
    BackendFileSystem& operator=(const BackendFileSystem&) = delete;

    virtual bool IsConnected() = 0;

    virtual Result OpenNormalFile(const std::string& path, int flags,
                                  BackendFilePtr& handle) = 0;

    virtual Result OpenHyperFile(const std::string& file_name,
                                 HyperBlockSetPtr& handle, bool is_data) = 0;

    virtual Result ListHblocksInHfile(const std::string& hfile_path,
                                      std::vector<std::string>& hblock_paths,
                                      bool is_data) = 0;

    virtual std::shared_ptr<Hdfs::Internal::HyperFileMeta>
    UpdateCoolFileMeta() = 0;
};

}  // namespace bytecool_sdk
