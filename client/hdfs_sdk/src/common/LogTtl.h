#pragma once

#include <atomic>
#include <memory>
#include <string>
#include <thread>

namespace Hdfs {
namespace Internal {

class LogTtl final {
   public:
    LogTtl(std::string logPath, std::string checkFileName, int ttl);
    ~LogTtl();

   private:
    void Loop();
    void Ttl();

   private:
    bool isInited_ = false;
    std::atomic_bool isNeedRun = false;
    std::thread t_;
    std::string logPath_;
    std::string checkFileName_;
    int64_t ttl_;
};

}  // namespace Internal
}  // namespace Hdfs
