#include "common/DataNodeManager.h"

#include "common/HdfsEnv.h"
#include "server/ServiceDiscovery.h"

namespace Hdfs {
namespace Internal {

static std::string processStrToLowerCace(const std::string& input) {
    // Trim whitespace from both ends of the string
    auto start = input.find_first_not_of(" ");
    auto end = input.find_last_not_of(" ");
    auto trimmed = input.substr(start, end - start + 1);

    // Convert the string to lowercase
    std::string result;
    result.reserve(trimmed.size());
    std::transform(trimmed.begin(), trimmed.end(), std::back_inserter(result),
                   [](unsigned char c) { return std::tolower(c); });

    return result;
}

void DataNodeManager::checkAndGC(
    const std::chrono::steady_clock::time_point& now, bool force) {
    if (force || keyMap_.size() > capacity_ || now - lastGCTime_ > GCTTL) {
        HDFSLOG(INFO, "[DataNodeManager] begin gc, capacity:{}",
                keyMap_.size());
        keyMap_.clear();
        lastGCTime_ = std::chrono::steady_clock::now();
    }
}

void DataNodeManager::MarkConnSlow(const std::string& address,
                                   std::chrono::microseconds lat) {
    if (!enable_) {
        return;
    }

    HDFSLOG(DEBUG, "[DataNodeManager] MarkConnSlow addr: {}, lat: {}", address,
            lat.count());

    if (lat <= connSlowLevel0) {
        return;
    }

    StatusState DNStateType;
    if (lat > connSlowLevel2) {
        DNStateType = DNSlow2;
    } else if (lat > connSlowLevel1) {
        DNStateType = DNSlow1;
    } else {
        DNStateType = DNSlow0;
    }

    auto key = processStrToLowerCace(address);
    auto now = std::chrono::steady_clock::now();
    std::unique_lock<std::shared_mutex> lock(mu_);

    auto it = keyMap_.find(key);
    if (it == keyMap_.end()) {
        keyMap_.emplace(key, std::make_unique<Status>(now, DNStateType));
        HDFSLOG(INFO, "[DataNodeManager] mark {} slows 1 type {}", key,
                DNStateType);
    } else {
        auto& status = it->second;
        auto curSlows = status->AddFailuresOrSlow(now, DNStateType);
        HDFSLOG(INFO, "[DataNodeManager] mark {} slows {} type {}", key,
                curSlows, DNStateType);
    }
}

void DataNodeManager::MarkConnFailure(const std::string& address) {
    if (!enable_) {
        return;
    }

    auto key = processStrToLowerCace(address);
    auto now = std::chrono::steady_clock::now();
    std::unique_lock<std::shared_mutex> lock(mu_);
    checkAndGC(now, false);

    auto it = keyMap_.find(key);
    if (it == keyMap_.end()) {
        keyMap_.emplace(key, std::make_unique<Status>(now, DNError));
        HDFSLOG(INFO, "[DataNodeManager] mark {} errors 1 type {}", key,
                DNError);
    } else {
        auto& status = it->second;
        auto curFailures = status->AddFailuresOrSlow(now, DNError);
        HDFSLOG(INFO, "[DataNodeManager] mark {} errors {} type {}", key,
                curFailures, DNError);
    }
}

bool DataNodeManager::HasFailures(const std::string& address) {
    if (!enable_) {
        return false;
    }

    std::shared_lock<std::shared_mutex> lock(mu_);
    auto now = std::chrono::steady_clock::now();
    auto key = processStrToLowerCace(address);
    auto it = keyMap_.find(key);
    if (it == keyMap_.end()) {
        return false;
    }

    auto& status = it->second;
    auto curFailures = status->GetValidFailures(now - black_ttl_);
    return (curFailures > 0);
}

// The DNs of the same vdc as the client are always in the first places, and
// sorted according to the health from largest to smallest.
void DataNodeManager::SortNodes(std::vector<DatanodeInfo>& nodes) {
    if (!enable_ || nodes.size() <= 1) {
        return;
    }

    std::shared_lock<std::shared_mutex> lock(mu_);
    auto now = std::chrono::steady_clock::now();

    auto partitionPoint = std::stable_partition(
        nodes.begin(), nodes.end(), [&](const DatanodeInfo& node) {
            return (!selfDc_.empty() && node.getDataCenter() == selfDc_);
        });

    auto compareFunc = [&](const DatanodeInfo& a, const DatanodeInfo& b) {
        auto a_array = getAddrCounts(a.getIpAddr(), now);
        auto b_array = getAddrCounts(b.getIpAddr(), now);

        // compare DNError
        if (a_array[3] != b_array[3]) {
            return a_array[3] < b_array[3];
        }
        // compare DNSlow
        size_t score_a = a_array[2] * 25 + a_array[1] * 5 + a_array[0];
        size_t score_b = b_array[2] * 25 + b_array[1] * 5 + b_array[0];
        return score_a < score_b;
    };

    std::stable_sort(nodes.begin(), partitionPoint, compareFunc);
    std::stable_sort(partitionPoint, nodes.end(), compareFunc);
}

// return {DNSlow0, DNSlow1, DNSlow2, DNError}
std::array<size_t, 4> DataNodeManager::getAddrCounts(
    const std::string& address,
    const std::chrono::steady_clock::time_point& now) {
    if (!enable_) {
        return {0, 0, 0};
    }
    auto key = processStrToLowerCace(address);
    auto it = keyMap_.find(key);
    if (it == keyMap_.end()) {
        return {0, 0, 0};
    } else {
        auto& status = it->second;
        std::array<size_t, 4> retArray{0, 0, 0, 0};
        retArray[3] = status->GetValidFailures(now - black_ttl_);
        auto slowTuple = status->GetValidSlowTuple(now - slow_ttl_);
        retArray[0] = std::get<0>(slowTuple);
        retArray[1] = std::get<1>(slowTuple);
        retArray[2] = std::get<2>(slowTuple);
        return retArray;
    }
}

void DataNodeManager::UpdateConfig(const SessionConfig& config) {
    auto old_state = init_cfg_.exchange(true);
    if (!old_state) {
        black_ttl_ = std::chrono::seconds(config.datanode_blacklist_ttl);
        slow_ttl_ = black_ttl_ / 5;
        capacity_ = config.datanode_blacklist_capacity;
        enable_ = config.datanode_blacklist_enable_;
        lastGCTime_ = std::chrono::steady_clock::now();
        selfDc_ = HdfsEnv::Get()->GetConsulService()->GetDC();
        HDFSLOG(
            INFO,
            "[DataNodeManager] Update enable: {}, black_ttl: {}, slow_ttl: {}, "
            "capacity: {}, dc: {}",
            enable_, black_ttl_.count(), slow_ttl_.count(), capacity_, selfDc_);
    }
}

void DataNodeManager::StopAndClear() {
    std::unique_lock<std::shared_mutex> lock(mu_);
    enable_ = false;
    checkAndGC(std::chrono::steady_clock::now(), true);
    init_cfg_.store(false);
}

}  // namespace Internal
}  // namespace Hdfs
