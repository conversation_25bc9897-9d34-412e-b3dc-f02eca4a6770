#pragma once

#include <atomic>
#include <chrono>
#include <condition_variable>
#include <cstdint>
#include <mutex>

namespace Hdfs {
namespace Internal {

class CountDownLatch {
   public:
    explicit CountDownLatch(size_t count) : count_(count) {}

    void Reset(size_t count) { count_ = count; }

    void Inc();

    void CountDown();

    int32_t GetCount();

    bool Await(
        std::chrono::milliseconds timeout = std::chrono::milliseconds(-1));

   private:
    std::atomic<int32_t> count_{0};
    std::mutex mutex_;
    std::condition_variable cv_;
};

}  // namespace Internal
}  // namespace Hdfs