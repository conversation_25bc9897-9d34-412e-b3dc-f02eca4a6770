#include "monitor/byted/log_emitter.h"

#include <functional>

#include "common/DateTime.h"
#include "common/Logger.h"
#include "log_emitter.h"

namespace Hdfs {
namespace Internal {

MonitorLogEmitter::MonitorLogEmitter(IOMonitor* iomonitor)
    : iomonitor_(iomonitor) {}

void MonitorLogEmitter::EmitSlowIOTrace(const IOTrace* trace) {
    HDFSLOG(INFO, "Slow IOTrace, {}", trace->ToString());
}

}  // namespace Internal
}  // namespace Hdfs
