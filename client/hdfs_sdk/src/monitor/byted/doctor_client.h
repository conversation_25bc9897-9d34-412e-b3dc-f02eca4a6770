#pragma once

#include <atomic>
#include <boost/asio.hpp>
#include <boost/beast/core.hpp>
#include <boost/beast/http.hpp>
#include <boost/beast/version.hpp>
#include <chrono>
#include <memory>
#include <nlohmann/json.hpp>
#include <random>
#include <shared_mutex>
#include <string>
#include <thread>
#include <unordered_map>
#include <vector>

#include "common/HdfsEnv.h"
#include "common/Logger.h"
#include "monitor/byted/StatsUtil.h"
#include "server/ServiceDiscovery.h"

namespace Hdfs {
namespace Internal {

static const char* kDoctorConsulName = "CPP_HDFS_DOCTOR_CONSUL_NAME";
static const char* kDefaultDoctorConsulName = "data.inf.hdfs_client_manager";

static boost::asio::io_context global_monitor_io_context;

struct DoctorClient {
    using tcp = boost::asio::ip::tcp;

    void Start() {
        bool old_state = running_.exchange(true);
        if (!old_state) {
            worker_ = std::thread([this]() {
                if (consul_name_.empty()) {
                    HDFSLOG(INFO, "[Doctor<PERSON><PERSON>] consul name empty");
                    return;
                }
                auto addrs = HdfsEnv::Get()->GetConsulService()->GetConsulAddrs(
                    consul_name_, 1, {});
                if (addrs.empty()) {
                    HDFSLOG(
                        INFO,
                        "[DoctorClient] can't get any address from consul={}",
                        consul_name_);
                    return;
                }
                std::random_device rd;
                std::mt19937 g(rd());
                std::shuffle(addrs.begin(), addrs.end(), g);
                chosen_addr_ = addrs[0];
                this->StartLoopWork();
            });
        }
    }

    void Close() {
        bool old_state = running_.exchange(false);
        if (old_state) {
            cv_.notify_all();
            worker_.join();
        }
    }

   private:
    DoctorClient() {
        const char* consul_name = getenv(kDoctorConsulName);
        if (consul_name != nullptr) {
            consul_name_ = std::string(consul_name);
        } else {
            consul_name_ = kDefaultDoctorConsulName;
        }
    }

    ~DoctorClient() { Close(); }

    DoctorClient(const DoctorClient&) = delete;

    DoctorClient& operator=(const DoctorClient&) = delete;

    void StartLoopWork() {
        HDFSLOG(INFO, "[DoctorClient] background job start, addr={}",
                chosen_addr_);

        tcp::socket socket(global_monitor_io_context);
        tcp::resolver resolver(global_monitor_io_context);

        std::size_t colon_pos = chosen_addr_.rfind(':');
        if (colon_pos == std::string::npos) {
            HDFSLOG(WARN, "[DoctorClient] addr parse failed {}", chosen_addr_);
            return;
        }
        std::string ip_str = chosen_addr_.substr(0, colon_pos);
        std::string port_str = chosen_addr_.substr(colon_pos + 1);
        boost::asio::ip::address ip_address;
        unsigned short port = 0;
        try {
            ip_address = boost::asio::ip::make_address(ip_str);
            port = static_cast<unsigned short>(std::stoi(port_str));
        } catch (const std::exception& e) {
            HDFSLOG(WARN, "[DoctorClient] addr parse failed {}", e.what());
            return;
        }
        std::vector<boost::asio::ip::tcp::endpoint> eps;
        eps.push_back(boost::asio::ip::tcp::endpoint(ip_address, port));

        try {
            boost::asio::connect(socket, eps);
        } catch (const std::exception& e) {
            HDFSLOG(WARN, "[DoctorClient] error when init {}", e.what());
            return;
        }

        while (running_.load()) {
            std::unique_lock<std::mutex> lk(cv_m_);
            auto status = cv_.wait_for(lk, std::chrono::seconds(30));
            if (status == std::cv_status::no_timeout) {
                boost::system::error_code ec;
                socket.shutdown(tcp::socket::shutdown_both, ec);
                if (ec && ec != boost::system::errc::not_connected) {
                    HDFSLOG(WARN, "[DoctorClient] close socket met error: {}",
                            ec.message());
                }
                break;
            }
            // Do real job below
            try {
                HDFSLOG(INFO, "[DoctorClient] do once job");
                auto req = GenerateReq();
                boost::beast::http::write(socket, req);
                boost::beast::flat_buffer fbuf;
                fbuf.prepare(8192);
                boost::beast::http::response<boost::beast::http::dynamic_body>
                    response;
                boost::beast::http::read(socket, fbuf, response);
                HDFSLOG(INFO, "[DoctorClient] report and get response {}",
                        response.result_int());
            } catch (const std::exception& e) {
                HDFSLOG(INFO, "[DoctorClient] report met fatal error {}",
                        e.what());
                break;
            }
        }
    }

    boost::beast::http::request<boost::beast::http::string_body> GenerateReq() {
        boost::beast::http::request<boost::beast::http::string_body> request(
            boost::beast::http::verb::post, "/", 11);
        request.set(boost::beast::http::field::user_agent,
                    BOOST_BEAST_VERSION_STRING);
        request.set(boost::beast::http::field::content_type,
                    "application/json");
        nlohmann::json j;
        auto stats = HdfsEnv::Get()->GetStatsMonitor()->GetStats(true);
        j["inputs"] = stats.inputs_;
        j["outputs"] = stats.outputs_;
        j["tcp_socks"] = stats.tcp_socks_;
        j["async_socks"] = stats.async_socks_;
        j["threads_info"] = stats.threads_info_;
        request.body() = j.dump();
        request.prepare_payload();
        return request;
    }

    std::string chosen_addr_;
    std::string consul_name_;
    std::atomic<bool> running_{false};
    std::thread worker_;
    std::condition_variable cv_;
    std::mutex cv_m_;
};

}  // namespace Internal
}  // namespace Hdfs
