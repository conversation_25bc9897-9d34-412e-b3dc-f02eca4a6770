#pragma once

#include <iostream>

#ifndef HDFS_DISABLE_IOMONITOR

#include "common/HdfsEnv.h"
#include "monitor/byted/byted_stats.h"
#include "monitor/byted/io_trace.h"
#include "monitor/byted/iomonitor.h"
#include "monitor/byted/paths_trace.h"

#define IOMonitorTraceApi(type, args...)                                \
    auto iomonitor_trace =                                              \
        (Hdfs::Internal::HdfsEnv::Get()->GetIOMonitor()->Trace<PERSON>pi(type, \
                                                                  ##args))

#define IOMonitorTraceApiWithFdTrace(type, path, args...)                 \
    HdfsFileWrapper hdfs_file_wrapper(new HdfsFileInternalWrapper(path)); \
    auto iomonitor_trace =                                                \
        (Hdfs::Internal::HdfsEnv::Get()->GetIOMonitor()->TraceApi(type,   \
                                                                  ##args))

#define MonitorEmitBytecoolMeta(kind, num)                                  \
    (Hdfs::Internal::HdfsEnv::Get()->GetIOMonitor()->EmitBytecoolMeta(kind, \
                                                                      num))

#define IOMonitorTraceApiSuccess() iomonitor_trace->SetSuccess()

#define IOMonitorTraceApiSetUserInterface(file) \
    iomonitor_trace->SetUserInterface(file)

#define IOMonitorTraceApiSetPriority()                                      \
    if (fs && fs->filesystem != nullptr) {                                  \
        auto priority = fs->getFilesystem().getConfig()->getUserPriority(); \
        iomonitor_trace->SetUserPriority(std::to_string(priority));         \
    }

#define PathTraceSetType(type) \
    Hdfs::Internal::g_paths_trace.SetType(tl_fd_trace, type);

#define IOMonitorAddException(exception_str, detail_str) \
    iomonitor_trace->SetLastException(exception_str);    \
    iomonitor_trace->SetExceptionDetail(detail_str);

#else

#include "monitor/fake/iomonitor.h"
#define IOMonitorTraceApi(type, args...) \
    auto iomonitor_trace = std::make_unique<IOTrace>(nullptr, type);

#define MonitorEmitBytecoolMeta(kind, num) {};
#define IOMonitorTraceApiSuccess() {};
#define IOMonitorTraceApiSetPriority() {};

#endif
