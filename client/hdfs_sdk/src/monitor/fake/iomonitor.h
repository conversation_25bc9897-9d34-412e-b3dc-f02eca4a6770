#pragma once

#include <folly/io/async/AsyncSocket.h>

#include <atomic>
#include <chrono>
#include <memory>
#include <string>
#include <vector>

#include "client/hdfs.h"

namespace Hdfs {
namespace Internal {

class IOMonitor;

class IOTrace {
    IOMonitor* iomonitor_ = nullptr;
    hdfsEvTyp type_;
    std::string path_;
    // datanode address
    std::vector<std::string> remote_addr_;
    uint64_t data_size_ = 0;
    int64_t start_at_us_;
    int64_t end_at_us_;
    uint64_t lat_us_ = 0;
    // whether this API call returns success
    bool success_ = false;
    std::string last_exception_{"unknown"};
    std::string userPriority_{"0"};

   public:
    IOTrace(IOMonitor* iomonitor, hdfsEvTyp type) {}
    ~IOTrace() {}
    std::string ToJsonStr() const { return ""; }
};

class IOMonitor {
   public:
    IOMonitor() {}
    ~IOMonitor() {}

    void EmitBytecoolMeta(const std::string& kind, int64_t num) {}
    void TraceApiDone(const IOTrace* trace) {}
    void MonitorThreadBegin(const char* appId, const char* containerId,
                            const char* name) {}
    void MonitorThreadDone() {}
    void SetEventCallBack(hdfsEvCbkFunc cbk) {}
};

extern void SetThreadName(std::thread& thread, const char* name);

struct HdfsStatsMonitor {
   public:
    ~HdfsStatsMonitor() {}

    void Start(std::chrono::seconds interval = std::chrono::seconds(30)) {
        return;
    }

    void NewInputFile(const std::string& path) {}
    void DeleteInputFile(const std::string& path) {}
    void NewOutputFile(const std::string& path) {}
    void DeleteOutputFile(const std::string& path) {}
    void NewSocket(std::string addr) {}
    void NewAsyncSocket(std::shared_ptr<folly::AsyncSocket> folly_sock) {}
    void DeleteSocket(std::string addr) {}
    void DeleteAsyncSocket(std::shared_ptr<folly::AsyncSocket> folly_sock) {}
    void RecordSeekPos(int64_t diff) {}
    void RecordPreadRealPacket(size_t num) {}
    void RecordReadRealPacket(size_t num, bool isSkip = false) {}
    void RecordReadFromBuffer(size_t num, bool isSkip = false) {}
    // void RecordSocketBytes(size_t num, BytesMode mode) {}
};

}  // namespace Internal
}  // namespace Hdfs
