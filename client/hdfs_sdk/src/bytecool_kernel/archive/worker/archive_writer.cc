#include "bytecool_kernel/archive/worker/archive.h"

namespace BYTECOOL::ARCHIVE {

Result Archive::WriteShards_(
    VOLUME::ShardCompositionType shard_comps,
    std::unordered_map<uint32_t, std::string>&& shard_datas,
    uint32_t timeout_ms) {
    // Check and init base var
    CHECK_ARCHIVE_IS_OPENED;

    uint64_t shard_data_length = shard_datas.begin()->second.length();
    for (const auto& [_, shard_data] : shard_datas) {
        if (shard_data.length() != shard_data_length) {
            BLOG(ERROR,
                 fmt::format("Archive failed to write shards because shard "
                             "datas length mismatch. {} shard_comps={}",
                             ToString(), shard_comps));
            return Result::Error(ErrorCode::kArchiveWriteShardsFailed);
        }
    }

    if (permission_ == OperatePermission::kWriteOnly) {
        if (volume_workers_.size() !=
            (coding_policy_.data_num + coding_policy_.parity_num)) {
            BLOG(ERROR,
                 fmt::format("Archive failed to write shards because valid "
                             "indexes not enough. {} shard_comps={}",
                             ToString(), shard_comps));
            return Result::Error(ErrorCode::kArchiveWriteShardsFailed);
        }
    }

    // Init quorum controller
    uint32_t total_task_num = volume_workers_.size();
    uint32_t quorum_task_num = total_task_num;
    auto quorum_controller = std::make_shared<COMMON::QuorumController>(
        total_task_num, quorum_task_num);

    // Init archive write shard task and leave it to thread scheduling
    for (const auto& [index, volume_worker] : volume_workers_) {
        auto write_shard_task = std::make_shared<VOLUME::VolumeWriteShardTask>(
            index, volume_worker, quorum_controller, shared_from_this());
        write_shard_task->Init(shard_comps, std::move(shard_datas[index]));
        thread_workers_[index]->AddTask(write_shard_task);
    }

    // Wait result
    std::unordered_map<uint32_t, std::pair<Result, std::string>> results;
    Result ret = quorum_controller->Wait(timeout_ms, &results);
    if (!ret) {
        std::string error_details_msg;
        std::vector<uint32_t> failed_indexes;
        for (const auto& [index, result] : results) {
            if (!result.first) {
                failed_indexes.emplace_back(index);
                error_details_msg +=
                    fmt::format("[index={} error={}]", index, result.first);
            }
        }
        BLOG(ERROR,
             fmt::format(
                 "Archive failed to write shards due to error in quorum "
                 "controller. {} shard_comps={} timeout_ms={} "
                 "total_task_num={} quorum_task_num={} failed_indexes={} "
                 "error={} error_details={}",
                 ToString(), shard_comps, timeout_ms, total_task_num,
                 quorum_task_num, fmt::join(failed_indexes, "-"), ret,
                 error_details_msg));
        return Result::Error(
            ErrorCode::kArchiveWriteShardsFailed,
            fmt::format("[failed_index={}]", fmt::join(failed_indexes, "-")) +
                error_details_msg);
    }

    // Update archive length
    length_ += shard_data_length;
    if (shard_comps == VOLUME::ShardCompositionType::kChunk) {
        length_ += (shard_data_length / coding_policy_.chunk_size *
                    sizeof(VOLUME::ChunkFooter));
    }

    return Result::Ok();
}

Result Archive::WriteHeader(uint64_t file_length, uint32_t timeout_ms) {
    std::string header;
    VOLUME::VolumeHeader volume_header{0};
    VOLUME::ShardHeader shard_header{};

    shard_header.chunk_size = coding_policy_.chunk_size;
    shard_header.object_size = file_length;
    shard_header.shard_size = 0;
    shard_header.shard_offset = length_;
    if (is_need_write_volume_header_) {
        header.append(VOLUME::Serialize(volume_header));
        is_need_write_volume_header_ = false;
        shard_header.shard_offset = sizeof(VOLUME::VolumeHeader);
    }
    header.append(VOLUME::Serialize(shard_header));

    std::unordered_map<uint32_t, std::string> headers;
    for (const auto& [valid_index, _] : volume_workers_) {
        headers.insert({valid_index, header});
    }

    return WriteShards_(VOLUME::ShardCompositionType::kHeader,
                        std::move(headers), timeout_ms);
}

Result Archive::WriteFooter(uint32_t timeout_ms) {
    std::string footer = VOLUME::Serialize(VOLUME::ShardFooter{
        .magic_number = VOLUME::kMagicNumber_ShardFooter,
    });

    std::unordered_map<uint32_t, std::string> footers;
    for (const auto& [valid_index, _] : volume_workers_) {
        footers.insert({valid_index, footer});
    }

    return WriteShards_(VOLUME::ShardCompositionType::kFooter,
                        std::move(footers), timeout_ms);
}

Result Archive::WriteStripe(
    std::unordered_map<uint32_t, std::string>&& shard_datas,
    uint32_t timeout_ms) {
    return WriteShards_(VOLUME::ShardCompositionType::kChunk,
                        std::move(shard_datas), timeout_ms);
}

}  // namespace BYTECOOL::ARCHIVE