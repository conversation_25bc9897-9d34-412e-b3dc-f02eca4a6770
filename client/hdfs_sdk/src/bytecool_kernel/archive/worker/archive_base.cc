#include "bytecool_kernel/archive/worker/archive.h"

namespace BYTECOOL::ARCHIVE {

Result Archive::OpenForRead(
    const std::unordered_map<
        uint32_t, std::shared_ptr<HdfsFileInternalWrapper>>& file_handlers) {
    if (is_opened_) {
        return Result::Ok();
    }
    if (permission_ != OperatePermission::kReadOnly) {
        BLOG(ERROR,
             fmt::format("Archive check permission failed. {} allowed={}",
                         ToString(), OperatePermission::kReadOnly));
        return Result::Error(ErrorCode::kArchiveInvalidPermission);
    }

    if (file_handlers.empty()) {  // Open volume worker with file path
        for (uint32_t i = 0;
             i < coding_policy_.data_num + coding_policy_.parity_num; ++i) {
            auto volume_worker = std::make_shared<VOLUME::VolumeWorker>(
                i, coding_policy_.chunk_size, permission_, config_.backend_fs);
            Result ret =
                volume_worker->Open(fmt::format("{}/{:0>2d}", path_, i));
            if (ret) {
                volume_workers_.insert({i, std::move(volume_worker)});
            } else {
                BLOG(WARN, fmt::format(
                               "Archive open one read volume worker with hyper "
                               "file path failed. {} index={} error={}",
                               ToString(), i, ret));
            }
        }
    } else {  // Open volume worker with file handler
        for (const auto& [index, file_handler] : file_handlers) {
            auto volume_worker = std::make_shared<VOLUME::VolumeWorker>(
                index, coding_policy_.chunk_size, permission_,
                config_.backend_fs);
            Result ret = volume_worker->Open(file_handler);
            if (ret) {
                volume_workers_.insert({index, std::move(volume_worker)});
            } else {
                BLOG(WARN,
                     fmt::format(
                         "Archive open one read volume worker with hyper block "
                         "file handler failed. {} index={} error={}",
                         ToString(), index, ret));
            }
        }
    }

    if (volume_workers_.size() < coding_policy_.data_num) {
        std::set<uint32_t> valid_indexes;
        for (const auto& [valid_index, _] : volume_workers_) {
            valid_indexes.insert(valid_index);
        }
        BLOG(ERROR, fmt::format("Archive open for read failed because valid "
                                "indexes not enough. {} valid_indexes={}",
                                ToString(), fmt::join(valid_indexes, "-")));
        return Result::Error(ErrorCode::kArchiveValidVolumesNotEnough);
    }

    is_opened_ = true;
    return Result::Ok();
}

Result Archive::OpenForRead(
    const std::unordered_map<uint32_t, std::string>& file_paths) {
    if (is_opened_) {
        return Result::Ok();
    }
    if (permission_ != OperatePermission::kReadOnly) {
        BLOG(ERROR,
             fmt::format("Archive check permission failed. {} allowed={}",
                         ToString(), OperatePermission::kReadOnly));
        return Result::Error(ErrorCode::kArchiveInvalidPermission);
    }

    for (const auto& [index, file_path] : file_paths) {
        auto volume_worker = std::make_shared<VOLUME::VolumeWorker>(
            index, coding_policy_.chunk_size, permission_, config_.backend_fs);
        Result ret = volume_worker->Open(file_path);
        if (ret) {
            volume_workers_.insert({index, std::move(volume_worker)});
        } else {
            BLOG(WARN,
                 fmt::format(
                     "Archive open one read volume worker with hyper block "
                     "file path failed. {} index={} file_path={} error={}",
                     ToString(), index, file_path, ret));
        }
    }

    if (volume_workers_.size() < coding_policy_.data_num) {
        std::set<uint32_t> valid_indexes;
        for (const auto& [valid_index, _] : volume_workers_) {
            valid_indexes.insert(valid_index);
        }
        BLOG(ERROR, fmt::format("Archive open for read failed because valid "
                                "indexes not enough. {} valid_indexes={}",
                                ToString(), fmt::join(valid_indexes, "-")));
        return Result::Error(ErrorCode::kArchiveValidVolumesNotEnough);
    }

    is_opened_ = true;
    return Result::Ok();
}

Result Archive::OpenForWrite(const std::set<uint32_t>& specific_indexes) {
    if (is_opened_) {
        return Result::Ok();
    }
    if (permission_ != OperatePermission::kWriteOnly &&
        permission_ != OperatePermission::kRepairOnly) {
        BLOG(ERROR,
             fmt::format("Archive check permission failed. {} allowed={}_{}",
                         ToString(), OperatePermission::kWriteOnly,
                         OperatePermission::kRepairOnly));
        return Result::Error(ErrorCode::kArchiveInvalidPermission);
    }

    if (permission_ == OperatePermission::kWriteOnly) {  // For write
        std::unordered_map<uint32_t, std::shared_ptr<HdfsFileInternalWrapper>>
            hdfs_hyper_block_handlers;
        Result ret = backend_meta_system_->CreateHyperFile(
            path_, coding_policy_, &hdfs_hyper_file_,
            &hdfs_hyper_block_handlers);
        if (!ret) {
            BLOG(ERROR,
                 fmt::format("Archive create hyper file failed. {} error={}",
                             ToString(), ret));
            return Result::Error(ErrorCode::kArchiveCreateHyperFileFailed,
                                 ret.GetErrorMsg());
        }

        for (uint32_t i = 0;
             i < coding_policy_.data_num + coding_policy_.parity_num; ++i) {
            auto volume_worker = std::make_shared<VOLUME::VolumeWorker>(
                i, coding_policy_.chunk_size, permission_, config_.backend_fs);
            ret = volume_worker->Open(hdfs_hyper_block_handlers[i]);
            if (!ret) {
                BLOG(
                    ERROR,
                    fmt::format(
                        "Archive open one write volume worker failed. {} error={}",
                        ToString(), ret));
                return Result::Error(ErrorCode::kArchiveOpenVolumeWorkFailed,
                                     ret.GetErrorMsg());
            }
            volume_workers_.insert({i, std::move(volume_worker)});
        }
    } else {  // For repair
        for (const uint32_t& index : specific_indexes) {
            auto volume_worker = std::make_shared<VOLUME::VolumeWorker>(
                index, coding_policy_.chunk_size, permission_,
                config_.backend_fs);
            Result ret =
                volume_worker->Open(fmt::format("{}/{:0>2d}", path_, index));
            if (!ret) {
                BLOG(ERROR, fmt::format(
                                "Archive open one repair volume worker failed. "
                                "{} index={} specific_indexes={} error={}",
                                ToString(), index,
                                fmt::join(specific_indexes, "-"), ret));
                return Result::Error(ErrorCode::kArchiveOpenVolumeWorkFailed,
                                     ret.GetErrorMsg());
            }
            volume_workers_.insert({index, std::move(volume_worker)});
        }
    }

    is_need_write_volume_header_ = true;
    is_opened_ = true;
    return Result::Ok();
}

Result Archive::Close() {
    Result ret = Result::Ok();
    if (is_opened_.exchange(false)) {
        // Close volume workers(by destructor) and thread workers
        volume_workers_.clear();
        thread_workers_.clear();
        WaitForInflightIO();
        // Close hyper file on write
        if (permission_ == OperatePermission::kWriteOnly &&
            backend_meta_system_ != nullptr && hdfs_hyper_file_ != nullptr) {
            ret = backend_meta_system_->CloseHyperFile(hdfs_hyper_file_);
            hdfs_hyper_file_ = nullptr;
        }
        backend_meta_system_ = nullptr;
    }

    return ret;
}

Result Archive::Lock() {
    CHECK_ARCHIVE_IS_OPENED;

    for (const auto& [_, volume_worker] : volume_workers_) {
        Result ret = volume_worker->Lock();
        if (!ret) {
            BLOG(ERROR, fmt::format("Archive lock failed. {} error={}",
                                    ToString(), ret));
            return Result::Error(ErrorCode::kArchiveLockVolumeWorkFailed,
                                 ret.GetErrorMsg());
        }
    }

    return Result::Ok();
}

Result Archive::AttachThreadWorkers(
    const std::vector<std::shared_ptr<UTILS::ThreadWorker>>& thread_workers) {
    CHECK_ARCHIVE_IS_OPENED;

    if (thread_workers.size() !=
        (coding_policy_.data_num + coding_policy_.parity_num)) {
        BLOG(ERROR,
             fmt::format("Archive attach thread workers failed. {} "
                         "need_worker_count={} actual_worker_count={}",
                         ToString(),
                         coding_policy_.data_num + coding_policy_.parity_num,
                         thread_workers.size()));
        return Result::Error(ErrorCode::kArchiveWorkThreadsSizeMismatch);
    }

    for (uint32_t i = 0; i < thread_workers.size(); ++i) {
        thread_workers_[i] = thread_workers[i];
    }

    return Result::Ok();
}

Result Archive::DetachThreadWorkers(
    std::vector<std::shared_ptr<UTILS::ThreadWorker>>* thread_workers) {
    CHECK_ARCHIVE_IS_OPENED;

    thread_workers->clear();
    for (auto& [_, thread_worker] : thread_workers_) {
        thread_workers->emplace_back(std::move(thread_worker));
    }
    thread_workers_.clear();

    return Result::Ok();
}

}  // namespace BYTECOOL::ARCHIVE