#pragma once

#include "bytecool_kernel/utils/concurrency/thread_pool.h"

namespace BYTECOOL::ARCHIVE {

class ArchiveWorkerThreadPool {
   public:
    ~ArchiveWorkerThreadPool() {
        if (archive_thread_worker_pool_) {
            archive_thread_worker_pool_->Close();
            archive_thread_worker_pool_.reset();
        }
    }

    ArchiveWorkerThreadPool(ArchiveWorkerThreadPool const&) = delete;

    void operator=(ArchiveWorkerThreadPool const&) = delete;

    static std::shared_ptr<UTILS::ThreadPool>& getInstance(
        const Config& config) {
        // Guaranteed to be destroyed. Instantiated on first use.
        static ArchiveWorkerThreadPool instance(config);
        return instance.archive_thread_worker_pool_;
    }

   private:
    explicit ArchiveWorkerThreadPool(const Config& config) {
        // Init thread worker pool
        archive_thread_worker_pool_ = std::make_shared<UTILS::ThreadPool>(
            config.archive_thread_worker_pool_capacity);
        archive_thread_worker_pool_->Init();
    }

   private:
    std::shared_ptr<UTILS::ThreadPool> archive_thread_worker_pool_{nullptr};
};

}  // namespace BYTECOOL::ARCHIVE
