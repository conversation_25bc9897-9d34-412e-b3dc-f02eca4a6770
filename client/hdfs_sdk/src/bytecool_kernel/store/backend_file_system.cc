#include "bytecool_kernel/store/backend_file_system.h"

namespace BYTECOOL::STORE {

Result BackendFileSystem::Open(
    const std::shared_ptr<HdfsFileInternalWrapper>& hdfs_file_handler) {
    if (is_opened_) {
        return Result::Ok();
    }
    if (hdfs_file_system_ == nullptr || hdfs_file_handler == nullptr) {
        BLOG(ERROR,
             fmt::format(
                 "Backend file system open file handler parameter invalid."));
        return Result::Error(ErrorCode::kStoreInvalidParameter);
    }

    auto hdfs_file_path_str_ptr =
        hdfsGetPathStr(hdfs_file_system_, hdfs_file_handler.get());
    DEFER([=]() { hdfsFreeString(hdfs_file_path_str_ptr); });
    hdfs_file_path_ = std::string(hdfs_file_path_str_ptr);
    hdfs_file_handler_sp_ = hdfs_file_handler;
    hdfs_file_handler_ = hdfs_file_handler_sp_.get();

    is_close_file_handler_ = false;
    is_opened_ = true;
    return Result::Ok();
}

Result BackendFileSystem::Open(const std::string& hdfs_file_path) {
    if (is_opened_) {
        return Result::Ok();
    }
    if (hdfs_file_system_ == nullptr || hdfs_file_path.empty()) {
        BLOG(ERROR,
             fmt::format(
                 "Backend file system open file path parameter invalid."));
        return Result::Error(ErrorCode::kStoreInvalidParameter);
    }

    hdfsFile hdfs_file_handler;
    if (permission_ == OperatePermission::kWriteOnly ||
        permission_ == OperatePermission::kRepairOnly) {
        hdfs_file_handler =
            hdfsOpenFile(hdfs_file_system_, hdfs_file_path.c_str(),
                         O_APPEND | O_WRONLY, 0, 1, 0);
    } else {
        hdfs_file_handler = hdfsOpenFile(
            hdfs_file_system_, hdfs_file_path.c_str(), O_RDONLY, 0, 0, 0);
    }
    if (!hdfs_file_handler) {
        std::string hdfs_last_error = hdfsGetLastError();
        BLOG(ERROR,
             fmt::format("Backend file system open failed. path={} error={}",
                         hdfs_file_path, hdfs_last_error));
        return Result::Error(ErrorCode::kStoreOpenFileFailed,
                             ParseBriefInfoFromException_(hdfs_last_error));
    }
    hdfs_file_path_ = hdfs_file_path;
    hdfs_file_handler_ = hdfs_file_handler;

    is_close_file_handler_ = true;
    is_opened_ = true;
    return Result::Ok();
}

Result BackendFileSystem::Read(uint64_t offset, uint64_t length, bool is_pread,
                               bool is_need_refresh, std::string* data) {
    CHECK_FS_IS_OPENED;
    if (permission_ != OperatePermission::kReadOnly) {
        BLOG(ERROR,
             fmt::format("Backend file system check read permission failed. "
                         "allowed_permissions={} actual_permissions={}",
                         OperatePermission::kReadOnly, permission_));
        return Result::Error(ErrorCode::kStoreInvalidPermission);
    }

    data->resize(length);
    char* data_ptr = data->data();
    size_t has_read_length = 0;

    if (!is_pread) {
        int ret = hdfsSeek(hdfs_file_system_, hdfs_file_handler_,
                           static_cast<tOffset>(offset));
        if (ret != 0) {
            std::string hdfs_last_error = hdfsGetLastError();
            BLOG(
                ERROR,
                fmt::format(
                    "Backend file system seek failed. path={} offset={} error={}/{}",
                    hdfs_file_path_, offset, ret, hdfs_last_error));
            return Result::Error(ErrorCode::kStoreSeekFileFailed,
                                 ParseBriefInfoFromException_(hdfs_last_error));
        }
    }

    while (has_read_length < length) {
        int read_length = 0;
        if (is_need_refresh) {
            int ret = hdfsInputStreamRefreshInfo(hdfs_file_system_,
                                                 hdfs_file_handler_);
            if (ret != 0) {
                std::string hdfs_last_error = hdfsGetLastError();
                BLOG(
                    ERROR,
                    fmt::format(
                        "Backend file system refresh info failed. path={} error={}/{}",
                        hdfs_file_path_, ret, hdfs_last_error));
                return Result::Error(
                    ErrorCode::kStoreRefreshInfoFailed,
                    ParseBriefInfoFromException_(hdfs_last_error));
            }
        }
        auto read_max_length =
            std::min(length - has_read_length,
                     static_cast<size_t>(std::numeric_limits<tSize>::max()));
        if (!is_pread) {
            read_length = hdfsRead(hdfs_file_system_, hdfs_file_handler_,
                                   data_ptr + has_read_length,
                                   static_cast<tSize>(read_max_length));
        } else {
            read_length =
                hdfsPread(hdfs_file_system_, hdfs_file_handler_,
                          static_cast<tOffset>(offset + has_read_length),
                          data_ptr + has_read_length,
                          static_cast<tSize>(read_max_length));
        }
        if (read_length < 0) {
            std::string hdfs_last_error = hdfsGetLastError();
            BLOG(
                ERROR,
                fmt::format(
                    "Backend file system read failed. path={} offset={} length={} "
                    "is_pread={} has_read_length={} cur_read_length={} "
                    "error={}",
                    hdfs_file_path_, offset, length, is_pread, has_read_length,
                    read_length, hdfs_last_error));
            return Result::Error(ErrorCode::kStoreReadFileFailed,
                                 ParseBriefInfoFromException_(hdfs_last_error));
        }
        if (read_length == 0) {
            break;
        }
        has_read_length += read_length;
    }

    if (has_read_length != length) {
        BLOG(ERROR,
             fmt::format(
                 "Backend file system must read failed. path={} offset={} "
                 "length={} is_pread={} has_read_length={}",
                 hdfs_file_path_, offset, length, is_pread, has_read_length));
        return Result::Error(ErrorCode::kStoreReadFileLengthInvalid);
    }

    return Result::Ok();
}

Result BackendFileSystem::Write(const std::string& data) {
    CHECK_FS_IS_OPENED;
    if (permission_ != OperatePermission::kWriteOnly &&
        permission_ != OperatePermission::kRepairOnly) {
        BLOG(ERROR,
             fmt::format("Backend file system check write permission failed. "
                         "allowed_permissions={}_{} actual_permissions={}",
                         OperatePermission::kWriteOnly,
                         OperatePermission::kRepairOnly, permission_));
        return Result::Error(ErrorCode::kStoreInvalidPermission);
    }

    size_t data_length = data.length();
    char* data_ptr = const_cast<char*>(data.data());
    size_t has_write_length = 0;

    while (has_write_length < data_length) {
        auto write_max_length =
            std::min(data_length - has_write_length,
                     static_cast<size_t>(std::numeric_limits<tSize>::max()));
        auto write_length = hdfsWrite(hdfs_file_system_, hdfs_file_handler_,
                                      data_ptr + has_write_length,
                                      static_cast<tSize>(write_max_length));
        if (write_length < 0) {
            std::string hdfs_last_error = hdfsGetLastError();
            BLOG(ERROR, fmt::format("Backend file system write failed. path={} "
                                    "length={} has_write_length={} error={}",
                                    hdfs_file_path_, data_length,
                                    has_write_length, hdfs_last_error));
            return Result::Error(ErrorCode::kStoreWriteFileFailed,
                                 ParseBriefInfoFromException_(hdfs_last_error));
        }
        if (write_length == 0) {
            break;
        }
        has_write_length += write_length;
    }

    if (has_write_length != data_length) {
        BLOG(ERROR,
             fmt::format("Backend file system must write failed. path={} "
                         "length={} has_write_length={}",
                         hdfs_file_path_, data_length, has_write_length));
        return Result::Error(ErrorCode::kStoreWriteFileFailed);
    }

    return Result::Ok();
}

Result BackendFileSystem::Flush() {
    CHECK_FS_IS_OPENED;
    if (permission_ != OperatePermission::kWriteOnly &&
        permission_ != OperatePermission::kRepairOnly) {
        BLOG(ERROR,
             fmt::format("Backend file system check write permission failed. "
                         "allowed_permissions={}_{} actual_permissions={}",
                         OperatePermission::kWriteOnly,
                         OperatePermission::kRepairOnly, permission_));
        return Result::Error(ErrorCode::kStoreInvalidPermission);
    }

    int ret = hdfsFlush(hdfs_file_system_, hdfs_file_handler_);
    if (ret != 0) {
        std::string hdfs_last_error = hdfsGetLastError();
        BLOG(ERROR, fmt::format(
                        "Backend file system flush failed. path={} error={}/{}",
                        hdfs_file_path_, ret, hdfs_last_error));
        return Result::Error(ErrorCode::kStoreFlushFileFailed,
                             ParseBriefInfoFromException_(hdfs_last_error));
    }

    return Result::Ok();
}

Result BackendFileSystem::RecoverLease() {
    CHECK_FS_IS_OPENED;
    if (permission_ != OperatePermission::kReadOnly) {
        BLOG(ERROR,
             fmt::format("Backend file system check recover lease permission "
                         "failed. allowed_permissions={} actual_permissions={}",
                         OperatePermission::kReadOnly, permission_));
        return Result::Error(ErrorCode::kStoreInvalidPermission);
    }

    int ret = hdfsRecoverLease(hdfs_file_system_, hdfs_file_path_.c_str());
    if (ret < 0) {
        std::string hdfs_last_error = hdfsGetLastError();
        BLOG(
            ERROR,
            fmt::format(
                "Backend file system recover lease failed. path={} error={}/{}",
                hdfs_file_path_, ret, hdfs_last_error));
        return Result::Error(ErrorCode::kStoreRecoverLeaseFailed,
                             ParseBriefInfoFromException_(hdfs_last_error));
    }

    return Result::Ok();
}

Result BackendFileSystem::Refresh() {
    CHECK_FS_IS_OPENED;
    if (permission_ != OperatePermission::kReadOnly) {
        BLOG(ERROR,
             fmt::format("Backend file system check refresh permission failed. "
                         "allowed_permissions={} actual_permissions={}",
                         OperatePermission::kReadOnly, permission_));
        return Result::Error(ErrorCode::kStoreInvalidPermission);
    }

    int ret = hdfsInputStreamRefreshInfo(hdfs_file_system_, hdfs_file_handler_);
    if (ret != 0) {
        BLOG(ERROR,
             fmt::format(
                 "Backend file system refresh info failed. path={} error={}/{}",
                 hdfs_file_path_, ret, hdfsGetLastError()));
        return Result::Error(ErrorCode::kStoreRefreshInfoFailed,
                             ParseBriefInfoFromException_(hdfsGetLastError()));
    }

    return Result::Ok();
}

Result BackendFileSystem::Close() {
    Result ret = Result::Ok();
    if (is_opened_.exchange(false)) {
        if (is_close_file_handler_) {
            auto res = hdfsCloseFile(hdfs_file_system_, hdfs_file_handler_);
            if (res != 0) {
                std::string hdfs_last_error = hdfsGetLastError();
                BLOG(
                    WARN,
                    fmt::format(
                        "Backend file system close failed. path={} error={}/{}",
                        hdfs_file_path_, res, hdfs_last_error));
                ret = Result::Error(
                    ErrorCode::kStoreCloseFileFailed,
                    ParseBriefInfoFromException_(hdfs_last_error));
            }
            hdfs_file_handler_ = nullptr;
            hdfs_file_handler_sp_ = nullptr;
        }
        hdfs_file_path_.clear();
    }

    return ret;
}

}  // namespace BYTECOOL::STORE