#pragma once

#include <atomic>
#include <string>
#include <vector>

#include "bytecool_kernel/common/log.h"
#include "bytecool_kernel/common/permission.h"
#include "bytecool_kernel/common/result.h"
#include "bytecool_kernel/utils/misc/defer_utils.h"
#include "bytecool_kernel/utils/misc/string_utils.h"
#include "client/hdfs.h"
#include "monitor/macro.h"

namespace BYTECOOL::STORE {

using ErrorCode = BYTECOOL::COMMON::ErrorCode;
using OperatePermission = BYTECOOL::COMMON::OperatePermission;
using Result = BYTECOOL::COMMON::Result;

#define CHECK_FS_IS_OPENED                                                     \
    do {                                                                       \
        if (!is_opened_) {                                                     \
            BLOG(ERROR, fmt::format("Backend file system not opened. path={}", \
                                    hdfs_file_path_));                         \
            return Result::Error(ErrorCode::kStoreNotOpenYet);                 \
        }                                                                      \
    } while (0)

class BackendFileSystem {
   public:
    BackendFileSystem() = delete;
    explicit BackendFileSystem(const OperatePermission& permission,
                               const hdfsFS& hdfs_file_system)
        : permission_(permission), hdfs_file_system_(hdfs_file_system) {}
    ~BackendFileSystem() { Close(); }

    // User
    Result Open(
        const std::shared_ptr<HdfsFileInternalWrapper>& hdfs_file_handler);
    Result Open(const std::string& hdfs_file_path);
    Result Read(uint64_t offset, uint64_t length, bool is_pread,
                bool is_need_refresh, std::string* data);
    Result Write(const std::string& data);
    Result Flush();
    Result RecoverLease();
    Result Refresh();
    Result Close();

    // Utils
    [[nodiscard]] hdfsFS GetFileSystem() const { return hdfs_file_system_; }
    static std::string ParseBriefInfoFromException_(
        const std::string& exception) {
        if (exception.find("java.io.IOException:") != std::string::npos) {
            return UTILS::StringUtils::Split(
                UTILS::StringUtils::Split(exception, "java.io.IOException:")[1],
                "\n")[0];
        } else if (exception.find("java.io.EOFException:") !=
                   std::string::npos) {
            return UTILS::StringUtils::Split(
                UTILS::StringUtils::Split(exception,
                                          "java.io.EOFException:")[1],
                "\n")[0];
        } else if (exception.find("java.util.concurrent.TimeoutException:") !=
                   std::string::npos) {
            return UTILS::StringUtils::Split(
                UTILS::StringUtils::Split(
                    exception, "java.util.concurrent.TimeoutException:")[1],
                "\n")[0];
        } else {
            return exception;
        }
    }

   private:
    std::atomic_bool is_opened_{false};
    std::atomic_bool is_close_file_handler_{true};
    OperatePermission permission_{OperatePermission::kReadOnly};
    // Hdfs op struct
    hdfsFS hdfs_file_system_{nullptr};
    std::string hdfs_file_path_{};
    hdfsFile hdfs_file_handler_{nullptr};
    std::shared_ptr<HdfsFileInternalWrapper> hdfs_file_handler_sp_{nullptr};
};

}  // namespace BYTECOOL::STORE