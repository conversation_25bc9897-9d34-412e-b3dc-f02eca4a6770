#include "bytecool_kernel/handler/bytecool_handler.h"

namespace BYTECOOL::HANDLER {

Result ByteCoolHandler::ReadOneFile_(
    const std::shared_ptr<ARCHIVE::Archive>& archive, uint64_t shard_offset,
    uint64_t shard_length, uint64_t read_offset, uint64_t read_length,
    bool is_pread, std::string* data) const {
    // 1. Check
    if (read_length == 0) {
        return Result::Ok();
    }

    // 2. Calculate read range parameters and reserve output data buf
    CodingPolicy coding_policy = archive->Schema();
    UTILS::ECWorker ec_worker(coding_policy.data_num, coding_policy.parity_num,
                              coding_policy.chunk_size);
    uint32_t max_stripe_num_per_read =
        config_.handler_max_length_per_read /
            (coding_policy.data_num * coding_policy.chunk_size) +
        1;
    uint32_t start_stripe_index =
        read_offset / (coding_policy.data_num * coding_policy.chunk_size);
    uint64_t start_stripe_offset =
        read_offset % (coding_policy.data_num * coding_policy.chunk_size);
    uint32_t end_stripe_index =
        (read_offset + read_length) /
        (coding_policy.data_num * coding_policy.chunk_size);
    // TODO(mkx, P0): make end_offset be inclusive would make it easier to
    // understand? end_stripe_offset is exclusive
    uint64_t end_stripe_offset =
        (read_offset + read_length) %
        (coding_policy.data_num * coding_policy.chunk_size);
    if (end_stripe_offset == 0) {
        end_stripe_index -= 1;
        end_stripe_offset = coding_policy.data_num * coding_policy.chunk_size;
    }
    uint32_t read_stripe_num = end_stripe_index - start_stripe_index + 1;
    data->reserve(
        read_stripe_num * coding_policy.data_num *
        coding_policy.chunk_size);  // the maximum length required to read data
    data->clear();

    // 3. Range read
    const bool range_read_stripe_condition =
        (start_stripe_index == end_stripe_index) &&
        ((read_length / coding_policy.chunk_size) <=
         (coding_policy.data_num / 2));
    if (range_read_stripe_condition) {
        uint32_t chunk_start_index =
            start_stripe_offset / coding_policy.chunk_size;
        uint32_t chunk_start_offset =
            start_stripe_offset % coding_policy.chunk_size;
        uint32_t chunk_end_index = end_stripe_offset / coding_policy.chunk_size;
        // chunk_end_offset is exclusive
        uint32_t chunk_end_offset =
            end_stripe_offset % coding_policy.chunk_size;
        if (chunk_end_offset == 0) {
            chunk_end_index -= 1;
            chunk_end_offset = coding_policy.chunk_size;
        }

        const bool range_read_chunk_condition =
            chunk_start_index == chunk_end_index && is_pread;
        if (range_read_chunk_condition) {
            // 3.1. Chunk range read (for single chunk)
            std::string chunk_data;
            Result ret = archive->ReadChunk(
                shard_offset, shard_length, chunk_start_offset,
                chunk_end_offset - chunk_start_offset, start_stripe_index,
                chunk_start_index, is_pread,
                config_.handler_range_read_timeout_ms, &chunk_data);
            if (!ret) {
                BLOG(WARN,
                     fmt::format(
                         "Read handler range read chunk failed. "
                         "cur_stripe_index={} cur_chunk_index={} error={}",
                         start_stripe_index, chunk_start_index, ret));
            } else {
                *data = std::move(chunk_data);
                return Result::Ok();
            }
        } else {
            // 3.2. Stripe range read (for single stripe)
            std::set<uint32_t> volume_indexes;
            for (uint32_t i = chunk_start_index; i <= chunk_end_index; ++i) {
                volume_indexes.insert(i);
            }

            std::unordered_map<uint32_t, std::string> stripe_read_shard_datas;
            Result ret = archive->ReadStripe(
                shard_offset, shard_length, start_stripe_index, 1, true,
                volume_indexes, is_pread, false,
                config_.handler_range_read_timeout_ms,
                &stripe_read_shard_datas);
            if (!ret) {
                BLOG(
                    WARN,
                    fmt::format(
                        "Read handler range read stripe failed. "
                        "cur_read_stripe_index={} cur_read_stripe_num={} error={}",
                        start_stripe_index, 1, ret));
            } else {
                if (chunk_start_index == chunk_end_index) {
                    data->append(
                        stripe_read_shard_datas[chunk_end_index].substr(
                            chunk_start_offset,
                            chunk_end_offset - chunk_start_offset));
                } else {
                    data->append(
                        stripe_read_shard_datas[chunk_start_index].substr(
                            chunk_start_offset,
                            coding_policy.chunk_size - chunk_start_offset));
                    for (uint32_t i = (chunk_start_index + 1);
                         i < chunk_end_index; ++i) {
                        data->append(stripe_read_shard_datas[i]);
                    }
                    data->append(
                        stripe_read_shard_datas[chunk_end_index].substr(
                            0, chunk_end_offset));
                }
                return Result::Ok();
            }
        }
    }

    // 4. Normal read
    uint32_t cur_read_stripe_index = start_stripe_index;
    while (cur_read_stripe_index <= end_stripe_index) {
        // Read
        std::unordered_map<uint32_t, std::string> stripe_read_shard_datas;
        uint32_t cur_read_stripe_num =
            std::min(max_stripe_num_per_read,
                     end_stripe_index - cur_read_stripe_index + 1);
        Result ret = archive->ReadStripe(
            shard_offset, shard_length, cur_read_stripe_index,
            cur_read_stripe_num, false, {}, is_pread, false,
            config_.handler_normal_read_timeout_ms, &stripe_read_shard_datas);
        if (!ret) {
            BLOG(ERROR,
                 fmt::format(
                     "Read handler normal read stripe failed. "
                     "cur_read_stripe_index={} cur_read_stripe_num={} error={}",
                     cur_read_stripe_index, cur_read_stripe_num, ret));
            return Result::Error(ErrorCode::kHandlerReadStripeDataFailed,
                                 ret.GetErrorMsg());
        }

        // Decode
        bool need_decode = false;
        for (uint32_t i = 0; i < coding_policy.data_num; ++i) {
            if (stripe_read_shard_datas.find(i) ==
                stripe_read_shard_datas.end()) {
                need_decode = true;
                break;
            }
        }
        std::unordered_map<uint32_t, std::string> stripe_decode_shard_datas;
        if (need_decode) {
            // only need to decode when there is data volume missing
            int ec_ret = ec_worker.Decode(stripe_read_shard_datas,
                                          &stripe_decode_shard_datas);
            if (ec_ret != 0) {
                BLOG(ERROR,
                     fmt::format("Read handler decode stripe failed. error={}",
                                 ret));
                return Result::Error(ErrorCode::kHandlerDecodeFailed);
            }
        }
        std::vector<std::string> stripe_shard_datas(coding_policy.data_num);
        for (uint32_t i = 0; i < coding_policy.data_num; ++i) {
            if (stripe_read_shard_datas.find(i) !=
                stripe_read_shard_datas.end()) {
                stripe_shard_datas[i] = std::move(stripe_read_shard_datas[i]);
            } else {
                stripe_shard_datas[i] = std::move(stripe_decode_shard_datas[i]);
            }
        }

        // Combine
        for (uint32_t index = cur_read_stripe_index;
             index < (cur_read_stripe_index + cur_read_stripe_num); ++index) {
            uint32_t chunk_index = index - cur_read_stripe_index;
            bool is_contain_start_index = (index == start_stripe_index);
            bool is_contain_end_index = (index == end_stripe_index);

            if (is_contain_start_index && !is_contain_end_index) {
                uint32_t shard_start_index =
                    start_stripe_offset / coding_policy.chunk_size;
                uint32_t chunk_offset_of_start_shard =
                    start_stripe_offset % coding_policy.chunk_size;
                data->append(stripe_shard_datas[shard_start_index].substr(
                    chunk_index * coding_policy.chunk_size +
                        chunk_offset_of_start_shard,
                    coding_policy.chunk_size - chunk_offset_of_start_shard));
                for (uint32_t i = (shard_start_index + 1);
                     i < coding_policy.data_num; ++i) {
                    data->append(stripe_shard_datas[i].substr(
                        chunk_index * coding_policy.chunk_size,
                        coding_policy.chunk_size));
                }
            } else if (!is_contain_start_index && is_contain_end_index) {
                uint32_t shard_end_index =
                    end_stripe_offset / coding_policy.chunk_size;
                uint32_t chunk_offset_of_end_shard =
                    end_stripe_offset % coding_policy.chunk_size;
                if (chunk_offset_of_end_shard == 0) {
                    shard_end_index -= 1;
                    chunk_offset_of_end_shard = coding_policy.chunk_size;
                }
                for (uint32_t i = 0; i < shard_end_index; ++i) {
                    data->append(stripe_shard_datas[i].substr(
                        chunk_index * coding_policy.chunk_size,
                        coding_policy.chunk_size));
                }
                data->append(stripe_shard_datas[shard_end_index].substr(
                    chunk_index * coding_policy.chunk_size,
                    chunk_offset_of_end_shard));
            } else if (is_contain_start_index && is_contain_end_index) {
                uint32_t shard_start_index =
                    start_stripe_offset / coding_policy.chunk_size;
                uint32_t chunk_offset_of_start_shard =
                    start_stripe_offset % coding_policy.chunk_size;
                uint32_t shard_end_index =
                    end_stripe_offset / coding_policy.chunk_size;
                uint32_t chunk_offset_of_end_shard =
                    end_stripe_offset % coding_policy.chunk_size;
                if (chunk_offset_of_end_shard == 0) {
                    shard_end_index -= 1;
                    chunk_offset_of_end_shard = coding_policy.chunk_size;
                }
                data->append(stripe_shard_datas[shard_start_index].substr(
                    chunk_index * coding_policy.chunk_size +
                        chunk_offset_of_start_shard,
                    coding_policy.chunk_size - chunk_offset_of_start_shard));
                for (uint32_t i = (shard_start_index + 1); i < shard_end_index;
                     ++i) {
                    data->append(stripe_shard_datas[i].substr(
                        chunk_index * coding_policy.chunk_size,
                        coding_policy.chunk_size));
                }
                data->append(stripe_shard_datas[shard_end_index].substr(
                    chunk_index * coding_policy.chunk_size,
                    chunk_offset_of_end_shard));
            } else {
                for (uint32_t i = 0; i < coding_policy.data_num; ++i) {
                    data->append(stripe_shard_datas[i].substr(
                        chunk_index * coding_policy.chunk_size,
                        coding_policy.chunk_size));
                }
            }
        }

        // Update
        cur_read_stripe_index += cur_read_stripe_num;
    }

    return Result::Ok();
}

Result ByteCoolHandler::Read(const std::string& file_path, uint64_t read_offset,
                             uint64_t read_length, bool is_pread,
                             std::string* data) const {
    // 1. Check and gen thread context
    CHECK_HANDLER_IS_INITED;

    // 2. Get shadow file info and hyper file info
    STORE::BackendMetaSystem backend_meta_system(config_.backend_fs);
    STORE::ShadowFileInfo shadow_file_info;
    Result ret =
        backend_meta_system.GetShadowFileInfo(file_path, &shadow_file_info);
    if (!ret) {
        BLOG(ERROR,
             fmt::format(
                 "Read handler get shadow file info failed. path={} error={}",
                 file_path, ret));
        return Result::Error(ErrorCode::kHandlerReadFailed, ret.GetErrorMsg());
    }

    STORE::HyperFileInfo hyper_file_info;
    ret = backend_meta_system.GetHyperFileInfo(shadow_file_info.path,
                                               &hyper_file_info);
    if (!ret) {
        BLOG(ERROR,
             fmt::format(
                 "Read handler get hyper file info failed. path={} error={}",
                 shadow_file_info.path, ret));
        return Result::Error(ErrorCode::kHandlerReadFailed, ret.GetErrorMsg());
    }

    // 3. Acquire archive from pool and alloc threads
    std::shared_ptr<ARCHIVE::Archive> read_archive;
    ARCHIVE::ArchiveReadSelector read_archive_selector;
    read_archive_selector.path = shadow_file_info.path;
    read_archive_selector.coding_policy = hyper_file_info.payload.coding_policy;
    read_archive_selector.backend_handlers.clear();
    read_archive_selector.backend_paths.clear();
    ret = resource_scheduler_->AcquireArchive(
        RequestType::kRead, read_archive_selector, &read_archive);
    if (!ret) {
        BLOG(
            ERROR,
            fmt::format(
                "Read handler acquire read archive failed. selector={} error={}",
                read_archive_selector.ToString(), ret));
        return Result::Error(ErrorCode::kHandlerReadFailed, ret.GetErrorMsg());
    }
    DEFER([&]() {
        if (read_archive != nullptr) {
            ret = resource_scheduler_->ReclaimArchive(RequestType::kRead,
                                                      read_archive);
            if (!ret) {
                BLOG(
                    WARN,
                    fmt::format(
                        "Read handler reclaim read archive failed. path={} error={}",
                        read_archive->Path(), ret));
            }
        }
    });

    // 4. Read
    CodingPolicy coding_policy = read_archive->Schema();
    uint32_t volume_num = coding_policy.data_num + coding_policy.parity_num;
    ret = ReadOneFile_(read_archive, shadow_file_info.offset,
                       shadow_file_info.length / volume_num, read_offset,
                       read_length, is_pread, data);
    if (!ret) {
        BLOG(ERROR,
             fmt::format("Read handler read internal failed. path={} "
                         "read_offset={} read_length={} is_pread={} error={}",
                         file_path, read_offset, read_length, is_pread, ret));
        return Result::Error(ErrorCode::kHandlerReadFailed, ret.GetErrorMsg());
    }

    return Result::Ok();
}

// PRead User
Result ByteCoolHandler::Read(
    const std::shared_ptr<Hdfs::HyperFile>& hyper_file,
    const std::shared_ptr<Hdfs::Internal::HyperFileMeta>& meta_info,
    uint64_t read_offset, uint64_t read_length, bool is_pread,
    std::string* data) const {
    // 1. Check and gen thread context
    CHECK_HANDLER_IS_INITED;

    // 2. Get shadow/hyper file info and hyper block file handler
    STORE::ShadowFileInfo shadow_file_info;
    Result ret = STORE::BackendMetaSystem::GetShadowFileInfo(*meta_info,
                                                             &shadow_file_info);
    if (!ret) {
        BLOG(ERROR,
             fmt::format(
                 "Read handler get shadow file info failed. path={} error={}",
                 shadow_file_info.original_path, ret));
        return Result::Error(ErrorCode::kHandlerReadFailed, ret.GetErrorMsg());
    }

    STORE::HyperFileInfo hyper_file_info;
    ret = STORE::BackendMetaSystem::GetHyperFileInfo(*meta_info,
                                                     &hyper_file_info);
    if (!ret) {
        BLOG(ERROR,
             fmt::format(
                 "Read handler get hyper file info failed. path={} error={}",
                 shadow_file_info.path, ret));
        return Result::Error(ErrorCode::kHandlerReadFailed, ret.GetErrorMsg());
    }

    std::unordered_map<uint32_t, std::shared_ptr<HdfsFileInternalWrapper>>
        hyper_block_file_handlers;
    ret = STORE::BackendMetaSystem::GetHyperBlockFileHandler(
        hyper_file, &hyper_block_file_handlers,
        hyper_file_info.payload.coding_policy.data_num);
    if (!ret) {
        BLOG(ERROR, fmt::format("Read handler get hyper block file handlers "
                                "failed. path={} error={}",
                                hyper_file_info.path, ret));
        return Result::Error(ErrorCode::kHandlerReadFailed, ret.GetErrorMsg());
    }

    // 3. Acquire archive from pool and alloc threads
    std::shared_ptr<ARCHIVE::Archive> read_archive;
    ARCHIVE::ArchiveReadSelector read_archive_selector;
    read_archive_selector.path = hyper_file_info.path;
    read_archive_selector.coding_policy = hyper_file_info.payload.coding_policy;
    read_archive_selector.backend_handlers = hyper_block_file_handlers;
    read_archive_selector.backend_paths.clear();
    ret = resource_scheduler_->AcquireArchive(
        RequestType::kRead, read_archive_selector, &read_archive);
    if (!ret) {
        BLOG(
            ERROR,
            fmt::format(
                "Read handler acquire read archive failed. selector={} error={}",
                read_archive_selector.ToString(), ret));
        return Result::Error(ErrorCode::kHandlerReadFailed, ret.GetErrorMsg());
    }
    DEFER([&]() {
        if (read_archive != nullptr) {
            ret = resource_scheduler_->ReclaimArchive(RequestType::kRead,
                                                      read_archive);
            if (!ret) {
                BLOG(
                    WARN,
                    fmt::format(
                        "Read handler reclaim read archive failed. path={} error={}",
                        read_archive->Path(), ret));
            }
        }
    });

    // 4. Read
    CodingPolicy coding_policy = hyper_file_info.payload.coding_policy;
    uint32_t volume_num = coding_policy.data_num + coding_policy.parity_num;
    ret = ReadOneFile_(read_archive, shadow_file_info.offset,
                       shadow_file_info.length / volume_num, read_offset,
                       read_length, is_pread, data);
    if (!ret) {
        BLOG(ERROR,
             fmt::format("Read handler read internal failed. path={} "
                         "read_offset={} read_length={} is_pread={} error={}",
                         shadow_file_info.original_path, read_offset,
                         read_length, is_pread, ret));
        return Result::Error(ErrorCode::kHandlerReadFailed, ret.GetErrorMsg());
    }

    return Result::Ok();
}

// Read from user
Result ByteCoolHandler::Read(
    const std::shared_ptr<Hdfs::Internal::HyperFileMeta>& meta_info,
    const std::shared_ptr<ARCHIVE::Archive>& read_archive, uint64_t read_offset,
    uint64_t read_length, bool is_pread, std::string* data) const {
    // 1. Check and gen thread context
    CHECK_HANDLER_IS_INITED;

    // 2. Get meta info
    STORE::ShadowFileInfo shadow_file_info;
    Result ret = STORE::BackendMetaSystem::GetShadowFileInfo(*meta_info,
                                                             &shadow_file_info);
    if (!ret) {
        BLOG(ERROR,
             fmt::format(
                 "Read handler get shadow file info failed. path={} error={}",
                 shadow_file_info.original_path, ret));
        return Result::Error(ErrorCode::kHandlerReadFailed, ret.GetErrorMsg());
    }
    // 3. Read
    CodingPolicy coding_policy = read_archive->Schema();
    uint32_t volume_num = coding_policy.data_num + coding_policy.parity_num;
    ret = ReadOneFile_(read_archive, shadow_file_info.offset,
                       shadow_file_info.length / volume_num, read_offset,
                       read_length, is_pread, data);
    if (!ret) {
        BLOG(ERROR,
             fmt::format("Read handler read internal failed. path={} "
                         "read_offset={} read_length={} is_pread={} error={}",
                         shadow_file_info.original_path, read_offset,
                         read_length, is_pread, ret));
        return Result::Error(ErrorCode::kHandlerReadFailed, ret.GetErrorMsg());
    }

    return Result::Ok();
}

}  // namespace BYTECOOL::HANDLER