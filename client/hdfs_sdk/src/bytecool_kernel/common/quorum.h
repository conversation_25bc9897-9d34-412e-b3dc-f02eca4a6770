#pragma once

#include <chrono>
#include <future>
#include <string>
#include <unordered_map>

#include "bytecool_kernel/common/result.h"

namespace BYTECOOL::COMMON {

class QuorumController {
   public:
    QuorumController() = delete;
    explicit QuorumController(
        uint32_t total_num, uint32_t quorum_num,
        const std::set<uint32_t>& required_task_indexes = {},
        const bool fail_fast = true) {
        total_num_ = total_num;
        quorum_num_ = quorum_num;
        required_task_indexes_ = required_task_indexes;
        fail_fast_ = fail_fast;
        results_.reserve(total_num_);
        future_ = promise_.get_future();
    }
    ~QuorumController() = default;

    void Done(uint32_t index, const Result& result, std::string&& data = "") {
        std::lock_guard<std::mutex> lock(mutex_);
        uint32_t failed_num_threshold = total_num_ - quorum_num_;
        if (result.GetErrorCode() == ErrorCode::kSuccess) {
            ++success_num_;
        } else {
            ++failed_num_;
        }

        results_.insert({index, {result, std::move(data)}});
        bool is_required =
            required_task_indexes_.find(index) != required_task_indexes_.end();
        required_task_indexes_.erase(index);

        // required tasks failed, fail the whole job
        // but if fail_fast_ is disabled, it won't return
        if (result.GetErrorCode() != ErrorCode::kSuccess && is_required &&
            fail_fast_) {
            if (!is_done_) {
                promise_.set_value(-1);
                is_done_ = true;
            }
            return;
        }
        // over the failure limit, fail the whole job
        // but if fail_fast_ is disabled, it won't return
        if (failed_num_ > failed_num_threshold && fail_fast_) {
            if (!is_done_) {
                promise_.set_value(-1);
                is_done_ = true;
            }
            return;
        }
        // success over the quorum and all required tasks are ready, make it
        // succeeded
        if (success_num_ >= quorum_num_ && required_task_indexes_.empty()) {
            if (!is_done_) {
                promise_.set_value(0);
                is_done_ = true;
            }
            return;
        }
        // if fail_fast_ is disabled, will reach to this code segment
        // to return the response
        if (success_num_ + failed_num_ >= total_num_) {
            if (!is_done_) {
                bool success = failed_num_ <= failed_num_threshold;
                promise_.set_value(success ? 0 : -1);
                is_done_ = true;
            }
            return;
        }
    }

    Result Wait(
        uint32_t timeout_ms,
        std::unordered_map<uint32_t, std::pair<Result, std::string>>* results) {
        std::future_status status = std::future_status::ready;
        if (timeout_ms == 0) {
            future_.wait();
        } else {
            status = future_.wait_for(std::chrono::milliseconds(timeout_ms));
        }
        // Check future status
        if (status == std::future_status::ready) {
            int32_t quorum_result = future_.get();
            // Record
            results->clear();
            std::lock_guard<std::mutex> lock(mutex_);
            for (auto& [index, result] : results_) {
                results->insert(
                    {index, {result.first, std::move(result.second)}});
            }
            // Quorum result
            if (quorum_result != 0) {
                return Result::Error(ErrorCode::kCommonQuorumResultFailed);
            } else {
                return Result::Ok();
            }
        } else if (status == std::future_status::timeout) {
            return Result::Error(ErrorCode::kCommonQuorumWaitTimeout);
        } else {
            return Result::Error(ErrorCode::kCommonQuorumWaitDeferred);
        }
    }

   private:
    std::mutex mutex_;
    std::promise<int32_t> promise_;
    std::future<int32_t> future_;

    uint32_t total_num_{0};
    uint32_t quorum_num_{0};
    uint32_t success_num_{0};
    uint32_t failed_num_{0};
    bool is_done_{false};

    std::set<uint32_t> required_task_indexes_{};
    bool fail_fast_{true};

    std::unordered_map<uint32_t, std::pair<Result, std::string>> results_;
};

}  // namespace BYTECOOL::COMMON