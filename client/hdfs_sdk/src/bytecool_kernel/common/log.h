#pragma once

#include "bytecool_kernel/utils/concurrency/thread_context.h"
#include "common/Logger.h"

namespace BYTECOOL::COMMON {

// Supported log levels include: DEBUG, INFO, WARN, ERROR, CRITICAL
#define BLOG(level, format_msg)                                      \
    SPDLOG_LOGGER_##level(                                           \
        Hdfs::Internal::HdfsEnv::Get()->GetLogger()->GetRaw(),       \
        fmt::format("[ByteCool 2.0][{}] {}",                         \
                    BYTECOOL::UTILS::GetThreadContext()->context_id, \
                    format_msg))

}  // namespace BYTECOOL::COMMON

template <typename T>
struct fmt::formatter<T, std::enable_if_t<std::is_enum_v<T>, char>> {
    constexpr auto parse(format_parse_context& ctx) { return ctx.end(); }
    template <typename FormatContext>
    auto format(const T& value, FormatContext& ctx) {
        return fmt::format_to(ctx.out(), "{}", static_cast<int>(value));
    }
};