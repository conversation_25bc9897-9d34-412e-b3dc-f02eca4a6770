#pragma once

#include <string>

#include "fmt/format.h"

struct HdfsFileInternalWrapper;
typedef struct HdfsFileSystemInternalWrapper* hdfsFS;

namespace BYTECOOL::COMMON {

struct Config {
    // HDFS backend file system
    hdfsFS backend_fs;

    // Resource
    uint32_t archive_read_pool_capacity{1024};
    uint32_t archive_repair_pool_capacity{1024};
    uint32_t archive_write_pool_capacity{1024};
    uint32_t archive_thread_worker_pool_capacity{128};

    // Handler
    uint64_t handler_max_length_per_user_read{4 * 1024 * 1024};
    uint64_t handler_max_length_per_read{6 * 1024 * 1024};
    uint32_t handler_range_read_timeout_ms{0};
    uint32_t handler_normal_read_timeout_ms{0};
    uint32_t handler_repair_timeout_ms{0};
    uint32_t handler_write_timeout_ms{0};

    // Archive
    std::string archive_path_prefix{"/user/bytecool"};
    uint32_t archive_size_threshold{1 << 30};         // One volume
    bool archive_full_shard_hedge_read_enable{true};  // shard fully hedge read

    [[nodiscard]] std::string ToString() const {
        return fmt::format(
            "backend_fs_ptr={} archive_read_pool_capacity={} "
            "archive_repair_pool_capacity={} archive_write_pool_capacity={} "
            "archive_thread_worker_pool_capacity={} "
            "handler_max_length_per_user_read={} handler_max_length_per_read={} "
            "handler_range_read_timeout_ms={} handler_normal_read_timeout_ms={} "
            "handler_repair_timeout_ms={} handler_write_timeout_ms={} "
            "archive_path_prefix={} archive_size_threshold={}",
            (void*)backend_fs, archive_read_pool_capacity,
            archive_repair_pool_capacity, archive_write_pool_capacity,
            archive_thread_worker_pool_capacity,
            handler_max_length_per_user_read, handler_max_length_per_read,
            handler_range_read_timeout_ms, handler_normal_read_timeout_ms,
            handler_repair_timeout_ms, handler_write_timeout_ms,
            archive_path_prefix, archive_size_threshold);
    }
};

}  // namespace BYTECOOL::COMMON