#pragma once

#include <atomic>
#include <memory>

#include "bytecool_kernel/utils/concurrency/thread_context.h"

namespace BYTECOOL::UTILS {

class Task {
   public:
    Task() { context_ = *GetThreadContext(); }
    virtual ~Task() = default;

    virtual void Run() { SetThreadContext(context_); }
    virtual void OnCancel() {}
    virtual void OnExpire() {}

    void Cancel() {
        is_canceled_.store(true, std::memory_order_release);
        OnCancel();
    }

    bool IsCanceled() { return is_canceled_.load(std::memory_order_acquire); }

   private:
    std::atomic_bool is_canceled_{false};
    ThreadContext context_{};
};

}  // namespace BYTECOOL::UTILS