#include "bytecool_kernel/utils/erasure/erasure_coder.h"

void (*GetEncodeFunc())(int, int, int, uint8_t*, uint8_t**, uint8_t**) {
#if defined(__i386__) || defined(__x86_64__)
    if (__builtin_cpu_supports("avx2")) {
        return &ec_encode_data_avx2;
    }
    if (__builtin_cpu_supports("avx")) {
        return &ec_encode_data_avx;
    }
    if (__builtin_cpu_supports("sse")) {
        return &ec_encode_data_sse;
    }
#elif defined(__aarch64__)
    return &ec_encode_data;
#endif
    return &ec_encode_data_base;
}

void gf_mul(uint8_t* vec, uint8_t* matrix, uint8_t* result, int k) {
    for (int i = 0; i < k; ++i) {
        uint8_t s = 0;
        for (int j = 0; j < k; ++j) {
            s ^= gf_mul(vec[j], matrix[j * k + i]);
        }
        result[i] = s;
    }
}

namespace BYTECOOL::UTILS {

ErasureCoder::ErasureCoder(const CodingSchema& schema) : schema_(schema) {
    auto k = (int)schema_.k;
    auto n = (int)schema_.n;
    cauchy_matrix_ = std::make_unique<uint8_t[]>(k * n);
    gf_table_ = std::make_unique<uint8_t[]>(k * (n - k) * 32);
    gf_gen_cauchy1_matrix(cauchy_matrix_.get(), n, k);
    ec_init_tables(k, n - k, &cauchy_matrix_[k * k], gf_table_.get());
    encode_ = GetEncodeFunc();
}

int ErasureCoder::Encode(const std::vector<uint8_t*>& buffs, int data_length) {
    auto k = (int)schema_.k;
    auto n = (int)schema_.n;
    if (buffs.size() != n) {
        return -1;
    }
    auto datas = std::make_unique<uint8_t*[]>(n);
    for (size_t i = 0; i < n; ++i) {
        datas[i] = buffs[i];
    }
    encode_(data_length, k, n - k, gf_table_.get(), &datas[0], &datas[k]);
    return 0;
}

int ErasureCoder::Decode(const std::vector<uint8_t*>& buffs, int data_length,
                         const std::vector<bool>& is_survive,
                         const std::vector<bool>& need_decode) {
    if ((int)buffs.size() != schema_.n || (int)is_survive.size() != schema_.n ||
        (int)need_decode.size() != schema_.n) {
        return -1;
    }
    auto k = (int)schema_.k;
    auto n = (int)schema_.n;
    int need_decode_cnt = 0;
    int survive_cnt = 0;
    auto survive = std::make_unique<uint8_t*[]>(k);
    auto decoded = std::make_unique<uint8_t*[]>(n);
    auto decode_matrix = std::make_unique<uint8_t[]>(k * k);
    for (size_t i = 0; i < n; ++i) {
        if (!is_survive[i]) {
            if (need_decode[i]) {
                decoded[need_decode_cnt++] = buffs[i];
            }
            continue;
        }
        if (survive_cnt < k) {
            std::memcpy(&decode_matrix[survive_cnt * k], &cauchy_matrix_[i * k],
                        k);
            survive[survive_cnt++] = buffs[i];
        }
    }
    if (survive_cnt < k) {
        return -2;
    }
    if (need_decode_cnt == 0) {
        return 0;
    }
    auto invert_decode_matrix = std::make_unique<uint8_t[]>(k * k);
    gf_invert_matrix(&decode_matrix[0], &invert_decode_matrix[0], k);
    decode_matrix = std::make_unique<uint8_t[]>(need_decode_cnt * k);
    for (size_t i = 0, j = 0; i < n; ++i) {
        if (need_decode[i] && !is_survive[i]) {
            if (i < k) {
                std::memcpy(&decode_matrix[j++ * k],
                            &invert_decode_matrix[i * k], k);
            } else {
                gf_mul(&cauchy_matrix_[i * k], &invert_decode_matrix[0],
                       &decode_matrix[j++ * k], k);
            }
        }
    }
    auto g_tbls = std::make_unique<uint8_t[]>(need_decode_cnt * k * 32);
    ec_init_tables(k, need_decode_cnt, decode_matrix.get(), g_tbls.get());
    encode_(data_length, k, need_decode_cnt, g_tbls.get(), survive.get(),
            decoded.get());
    return 0;
}

}  // namespace BYTECOOL::UTILS