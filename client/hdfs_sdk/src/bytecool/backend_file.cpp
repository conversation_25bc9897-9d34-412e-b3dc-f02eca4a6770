#include "bytecool/backend_file.h"

#include "common/Logger.h"

namespace Hdfs {
namespace Bytecool {

const int DefaultMaxFailuresAllowRemoveIdx = 2;

void RemoveIndex(std::vector<int>& indexes, int index) {
    auto it = std::find(indexes.begin(), indexes.end(), index);
    if (it != indexes.end()) {
        indexes.erase(it);
    }
}

bytecool_sdk::Result BackendFileLibhdfs::PRead(void* buffer,
                                               const size_t offset,
                                               const size_t length,
                                               size_t& read_bytes,
                                               bool is_pread_mode) {
    tSize ret{0};
    if (is_pread_mode && !disable_hdfs_pread_) {
        ret = hdfsByteCoolPread(hdfs_fs_, hdfsFileHandle_, offset, buffer,
                                length);
        if (ret < 0) {
            std::stringstream ss;
            ss << "Failed to pread " << length << " bytes (errno=" << errno
               << ") by reason " << std::string(hdfsGetLastError());
            return bytecool_sdk::Result::err(ss.str());
        }
    } else {
        if (hdfsByteCoolSeek(hdfs_fs_, hdfsFileHandle_, offset) != 0) {
            std::stringstream ss;
            ss << "Failed to seek file to " << offset << " (errno=" << errno
               << ") by reason " << std::string(hdfsGetLastError());
            return bytecool_sdk::Result::err(ss.str());
        }

        ret = hdfsByteCoolRead(hdfs_fs_, hdfsFileHandle_, buffer, length);
        if (ret < 0) {
            std::stringstream ss;
            ss << "Failed to read " << length << " bytes (errno=" << errno
               << ") by reason " << std::string(hdfsGetLastError());
            return bytecool_sdk::Result::err(ss.str());
        }
    }

    read_bytes = ret;
    return bytecool_sdk::Result::ok();
}

bytecool_sdk::Result BackendFileLibhdfs::Seek(const size_t pos) {
    if (hdfsByteCoolSeek(hdfs_fs_, hdfsFileHandle_, pos) != 0) {
        std::stringstream ss;
        ss << "Error when seeking file (errno=" << errno << ") by reason "
           << std::string(hdfsGetLastError());
        return bytecool_sdk::Result::err(ss.str());
    } else {
        return bytecool_sdk::Result::ok();
    }
}

bytecool_sdk::Result BackendFileLibhdfs::Flush() {
    if (hdfsHFlush(hdfs_fs_, hdfsFileHandle_) != 0) {
        std::stringstream ss;
        ss << "Error when flushing file (errno=" << errno << ") by reason "
           << std::string(hdfsGetLastError());
        return bytecool_sdk::Result::err(ss.str());
    } else {
        return bytecool_sdk::Result::ok();
    }
}

bytecool_sdk::Result BackendFileLibhdfs::Tell(size_t& pos) {
    auto ret = hdfsTell(hdfs_fs_, hdfsFileHandle_);
    if (ret == -1) {
        std::stringstream ss;
        ss << "Failed to tell pos (errno=" << errno << ") by reason "
           << std::string(hdfsGetLastError());
        return bytecool_sdk::Result::err(ss.str());
    }
    pos = ret;
    return bytecool_sdk::Result::ok();
}

bytecool_sdk::Result BackendFileLibhdfs::Close() {
    if (!is_hdfsfile_owner_ || is_closed_) {
        return bytecool_sdk::Result::ok();
    }
    is_closed_ = true;
    if (hdfsCloseFile(hdfs_fs_, hdfsFileHandle_) != 0) {
        std::stringstream ss;
        ss << "Error when closing file (errno=" << errno << ") by reason "
           << std::string(hdfsGetLastError());
        return bytecool_sdk::Result::err(ss.str());
    } else {
        return bytecool_sdk::Result::ok();
    }
}

bytecool_sdk::Result HyperBlockSetLibhdfs::GetHyperBlockReader(
    int index, bytecool_sdk::BackendFilePtr& handle) {
    auto hdfsFileHandle =
        hdfsGetHyperBlockReader(hdfs_fs_, hdfsHyperBlockSetHandle_, index);
    if (!hdfsFileHandle) {
        return bytecool_sdk::Result::err(
            "Can't get hyper block reader: " + path_ + " with index: " +
            std::to_string(index) + ": " + std::string(hdfsGetLastError()));
    }

    std::string hblock_path = hblock_paths_[index];
    handle = std::make_shared<BackendFileLibhdfs>(
        hdfs_fs_, hdfsFileHandle, hblock_path, false, disable_hdfs_pread_);

    return bytecool_sdk::Result::ok();
}

std::vector<int> HyperBlockSetLibhdfs::GetAvailableIndexes() {
    int* indexes_addr = hdfsHyperBlockSetHandle_->availableIndexes;
    std::vector<int> indexes(
        indexes_addr,
        indexes_addr + hdfsHyperBlockSetHandle_->availableHyperBlockNum);

    auto failed_num = hdfsHyperBlockSetHandle_->unavaialableHyperBlockNum;
    auto may_failed_num = hdfsHyperBlockSetHandle_->unhealthyHyperBlockNum;

    if (failed_num + may_failed_num > 0) {
        HDFSLOG(WARN, "[HyperBlockSetLibhdfs] failed num:{}, unhealthy num:{}",
                failed_num, may_failed_num);
    }

    if (failed_num + may_failed_num <= DefaultMaxFailuresAllowRemoveIdx) {
        int* unhealth_addr = hdfsHyperBlockSetHandle_->unhealthyIndexes;
        for (size_t i = 0; i < may_failed_num; i++) {
            RemoveIndex(indexes, unhealth_addr[i]);
        }
    }

    return indexes;
}

bytecool_sdk::Result HyperBlockSetLibhdfs::Close() {
    if (is_closed_) {
        return bytecool_sdk::Result::ok();
    }
    is_closed_ = true;

    if (hdfsCloseHyperBlockSet(hdfs_fs_, hdfsHyperBlockSetHandle_, true) != 0) {
        return bytecool_sdk::Result::err(
            "Error when closing hyper block set: " +
            std::string(hdfsGetLastError()));
    } else {
        return bytecool_sdk::Result::ok();
    }
}

}  // namespace Bytecool
}  // namespace Hdfs
