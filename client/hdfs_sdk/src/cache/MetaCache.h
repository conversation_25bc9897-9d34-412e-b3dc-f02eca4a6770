// Created on 2024-01-30.
// New Cache Manager Implement.

#pragma once

#include <algorithm>
#include <atomic>
#include <condition_variable>
#include <memory>
#include <mutex>
#include <optional>
#include <thread>

#include "client/ContentSummary.h"
#include "common/HdfsEnv.h"
#include "common/LruCache.h"
#include "common/SessionConfig.h"
#include "server/HyperFileMeta.h"
#include "server/LocatedBlock.h"
#include "server/Namenode.h"

namespace Hdfs {
namespace Internal {

const uint64_t kMetaCacheExpireBatch = 1000;

class MetaCache {
   public:
    MetaCache();
    ~MetaCache();
    void Stop();

    bool GetHyperBlockLocations(const std::string& src, int64_t offset,
                                int64_t length, LocatedHyperBlocks& lhbs);
    void SetHyperBlockLocations(const std::string& src,
                                LocatedHyperBlocks& lhbs);
    bool GetCoolMetaCache(const std::string& src, HyperFileMeta& meta);
    void SetCoolMetaCache(const std::string& src, HyperFileMeta& meta);
    std::optional<std::shared_ptr<LocatedBlocks>> GetBlockLocations(
        const std::string& path, int64_t offset, int64_t length);
    void SetBlockLocations(const std::string& path,
                           std::shared_ptr<LocatedBlocks> new_located_blocks);
    void RemoveBlockLocations(const std::string& path);
    void UpdateConfig(const SessionConfig& config);

   private:
    void doMetaCleanWork();

    std::unique_ptr<LRUCache<std::string, std::shared_ptr<LocatedHyperBlocks>>>
        hblock_cache_;
    std::unique_ptr<LRUCache<std::string, std::shared_ptr<HyperFileMeta>>>
        cool_meta_cache_;
    std::unique_ptr<LRUCache<std::string, std::shared_ptr<LocatedBlocks>>>
        normal_block_cache_;

    std::atomic<bool> inited_{false};
    std::chrono::seconds hblock_cache_ttl_ = std::chrono::seconds(10);
    std::chrono::seconds cool_cache_ttl_ = std::chrono::seconds(300);
    std::chrono::seconds blk_cache_ttl_ = std::chrono::seconds(300);

    std::thread clean_worker_;
    std::shared_ptr<std::atomic<bool>> stop_ =
        std::make_shared<std::atomic<bool>>(false);
    std::condition_variable cv_;
    std::mutex cv_m_;
};

}  // namespace Internal
}  // namespace Hdfs
