/**
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

/**
 * These .proto interfaces are private and stable.
 * Please see http://wiki.apache.org/hadoop/Compatibility
 * for what changes are allowed for a *stable* .proto interface.
 */

// This file contains protocol buffers that are used throughout HDFS -- i.e.
// by the client, server, and data transfer protocols.
syntax = "proto2";

option java_package = "org.apache.hadoop.hdfs.protocol.proto";
option java_outer_classname = "HdfsProtos";
option java_generate_equals_and_hash = true;
package Hdfs.Internal;

/* XXX
 * path prefix is for building with Blade, incompatible with CMake.
 * so remote path prefix when building with CMake
 */
import "Security.proto";
import "RpcHeader.proto";

/**
 * Extended block idenfies a block
 */
message ExtendedBlockProto {
  required string poolId = 1;   // Block pool id - gloablly unique across clusters
  required uint64 blockId = 2;  // the local id within a pool
  required uint64 generationStamp = 3;
  optional uint64 numBytes = 4 [default = 0];  // len does not belong in ebid
                                               // here for historical reasons
}

/**
 * Identifies a Datanode
 */
message DatanodeIDProto {
  required string ipAddr = 1;    // IP address
  required string hostName = 2;  // hostname
  required string datanodeUuid = 3;     // UUID assigned to the Datanode. For
                                        // upgraded clusters this is the same
                                        // as the original StorageID of the
                                        // Datanode.
  required uint32 xferPort = 4;  // data streaming port
  required uint32 infoPort = 5;  // datanode http port
  required uint32 ipcPort = 6;   // ipc server port
  optional uint32 infoSecurePort = 7 [default = 0]; // datanode https port
  optional uint32 byterpcPort = 9;
}

/**
 * DatanodeInfo array
 */
message DatanodeInfosProto {
  repeated DatanodeInfoProto datanodes = 1;
}

/**
 * The status of a Datanode
 */
message DatanodeInfoProto {
  required DatanodeIDProto id = 1;
  optional uint64 capacity = 2 [default = 0];
  optional uint64 dfsUsed = 3 [default = 0];
  optional uint64 remaining = 4 [default = 0];
  optional uint64 blockPoolUsed = 5 [default = 0];
  optional uint64 lastUpdate = 6 [default = 0];
  optional uint32 xceiverCount = 7 [default = 0];
  optional string location = 8;
  enum AdminState {
    NORMAL = 0;
    DECOMMISSION_INPROGRESS = 1;
    DECOMMISSIONED = 2;
  }

  optional AdminState adminState = 10 [default = NORMAL];
  optional uint64 cacheCapacity = 11 [default = 0];
  optional uint64 cacheUsed = 12 [default = 0];
  optional uint64 nonDfsUsed = 13;
  optional string dataCenter = 14;
}

/**
 * Summary of a file or directory
 */
message ContentSummaryProto {
  required uint64 length = 1;
  required uint64 fileCount = 2;
  required uint64 directoryCount = 3;
  required uint64 quota = 4;
  required uint64 spaceConsumed = 5;
  required uint64 spaceQuota = 6;
}

/**
 * Contains a list of paths corresponding to corrupt files and a cookie
 * used for iterative calls to NameNode.listCorruptFileBlocks.
 *
 */
message CorruptFileBlocksProto {
 repeated string files = 1;
 required string   cookie = 2;
}

/**
 * File or Directory permision - same spec as posix
 */
message FsPermissionProto {
  required uint32 perm = 1;       // Actually a short - only 16bits used
}

/**
 * Types of recognized storage media.
 */
enum StorageTypeProto {
  DISK = 1;
  SSD = 2;
  ARCHIVE = 3;
  RAM_DISK = 4;
  DISK2 = 5;
  DISK3 = 6;
}

/**
 * A list of storage types. 
 */
message StorageTypesProto {
  repeated StorageTypeProto storageTypes = 1;
}

enum IOPriority {
  PRIORITY_CRITICAL = 0;
  PRIORITY_REAL_TIME = 1;
  PRIORITY_ELASTIC = 2;
  PRIORITY_BEST_EFFORT = 3;
  PRIORITY_SCAVENGER = 4;
}

message IOPriorityProto {
  required IOPriority priority = 1;
}

/**
 * Block replica storage policy.
 */
message BlockStoragePolicyProto {
  required uint32 policyId = 1;
  required string name = 2;
  // a list of storage types for storing the block replicas when creating a block.
  required StorageTypesProto creationPolicy = 3;
  // A list of storage types for creation fallback storage.
  optional StorageTypesProto creationFallbackPolicy = 4;
  optional StorageTypesProto replicationFallbackPolicy = 5;
  optional bool copyOnCreateFile = 6;
}

/**
 * A list of storage IDs.
 */
message StorageUuidsProto {
  repeated string storageUuids = 1;
}

/**
 * A LocatedBlock gives information about a block and its location.
 */
message LocatedBlockProto {
  required ExtendedBlockProto b  = 1;
  required uint64 offset = 2;           // offset of first byte of block in the file
  repeated DatanodeInfoProto locs = 3;  // Locations ordered by proximity to client ip
  required bool corrupt = 4;            // true if all replicas of a block are corrupt, else false
                                        // If block has few corrupt replicas, they are filtered and
                                        // their locations are not part of this object

  required TokenProto blockToken = 5;
  repeated bool isCached = 6 [packed=true]; // if a location in locs is cached
  repeated StorageTypeProto storageTypes = 7;
  repeated string storageIDs = 8;
}

message DataEncryptionKeyProto {
  required uint32 keyId = 1;
  required string blockPoolId = 2;
  required bytes nonce = 3;
  required bytes encryptionKey = 4;
  required uint64 expiryDate = 5;
  optional string encryptionAlgorithm = 6;
}

/**
 * Cipher suite.
 */
enum CipherSuiteProto {
    UNKNOWN = 1;
    AES_CTR_NOPADDING = 2;
}

/**
 * Crypto protocol version used to access encrypted files.
 */
enum CryptoProtocolVersionProto {
    UNKNOWN_PROTOCOL_VERSION = 1;
    ENCRYPTION_ZONES = 2;
}

/**
 * Encryption information for a file.
 */
message FileEncryptionInfoProto {
  required CipherSuiteProto suite = 1;
  required CryptoProtocolVersionProto cryptoProtocolVersion = 2;
  required bytes key = 3;
  required bytes iv = 4;
  required string keyName = 5;
  required string ezKeyVersionName = 6;
}

/**
 * Encryption information for an individual
 * file within an encryption zone
 */
message PerFileEncryptionInfoProto {
  required bytes key = 1;
  required bytes iv = 2;
  required string ezKeyVersionName = 3;
}

/**
 * Encryption information for an encryption
 * zone
 */
message ZoneEncryptionInfoProto {
  required CipherSuiteProto suite = 1;
  required CryptoProtocolVersionProto cryptoProtocolVersion = 2;
  required string keyName = 3;
}

/**
 * Cipher option
 */
message CipherOptionProto {
  required CipherSuiteProto suite = 1;
  optional bytes inKey = 2;
  optional bytes inIv = 3;
  optional bytes outKey = 4;
  optional bytes outIv = 5;
}

/**
 * A set of file blocks and their locations.
 */
message LocatedBlocksProto {
  required uint64 fileLength = 1;
  repeated LocatedBlockProto blocks = 2;
  required bool underConstruction = 3;
  optional LocatedBlockProto lastBlock = 4;
  required bool isLastBlockComplete = 5;
}

/**
 * The archive file related slice information.
 */
message SliceInfo {
  enum SliceState {
    kCommited = 1; // CommitSlice is done.
    kDeprecated = 2; // The shadow(user) file is deleted.
  }
  required uint64 id = 1;
  required uint64 archive_id = 2;
  required uint64 offset = 3;
  required uint64 length = 4;
  optional uint64 original_size = 5;
  required SliceState state = 6;
  optional string path = 7;
  optional bytes payload = 10;
}

/**
 * Status of a Hyper File(ByteCool Archive File)
 */
message HyperFileInfo {
  enum State {
    kComplete = 1;
    kUnderConstruction = 2;
    kGarbage = 3;
  }
  required uint64 id = 1;
  required uint64 volume_num = 3;
  required State state = 4;
  required uint64 epoch = 5;
  optional string lease_lock = 6; // The client name that allows modification and be set to empty when last modification is done.
  optional bytes payload = 10; // extra attributes, include EC Schema, Chunk Size and etc, generated by client bytecool kernel.
}

message ExternalOSSPolicyProto {
  enum Type {
    AZURE = 1;
    GCS = 2;
    S3 = 3;
  };
  
  optional Type type = 1;
  // Token related should not be filled when ExternalOSSPolicyProto is persisted into inode xattr
  // It should only be filled inner the response return to user.
  optional string token = 2;
  optional string bucket_name = 3;
  optional string account_name = 4;
  // ToDO:
  // optional string gcs_related_info = 5;
}

/**
 * ShadowFile Inode info
 */
message ShadowFileInfo {
  required uint64 archive_id = 1;
  required uint64 archive_offset = 2;
  required uint64 archive_length = 3;
  required uint64 epoch = 4;
  optional bytes payload = 5;
  optional string archive_path = 6;
  required uint64 original_size = 7;
}

message StripeLayout {
  required uint32 stripeWidth = 1; // chunk cnt in one stripe
  required uint32 chunkSize = 2;
}
message StripeColumnInfo {
  required uint32 index = 1;
  required bytes path = 2;
  required uint64 length = 3;
}

/**
 * Status of a file, directory or symlink
 * Optionally includes a file's block locations if requested by client on the rpc call.
 */
message HdfsFileStatusProto {
  enum FileType {
    IS_DIR = 1;
    IS_FILE = 2;
    IS_SYMLINK = 3;
  }
  required FileType fileType = 1;
  required bytes path = 2;          // local name of inode encoded java UTF8
  required uint64 length = 3;
  required FsPermissionProto permission = 4;
  required string owner = 5;
  required string group = 6;
  required uint64 modification_time = 7;
  required uint64 access_time = 8;

  // Optional fields for symlink
  optional bytes symlink = 9;             // if symlink, target encoded java UTF8

  // Optional fields for file
  optional uint32 block_replication = 10 [default = 0]; // only 16bits used
  optional uint64 blocksize = 11 [default = 0];
  optional LocatedBlocksProto locations = 12;  // suppled only if asked by client

  // Optional field for fileId
  optional uint64 fileId = 13 [default = 0]; // default as an invalid id
  optional int32 childrenNum = 14 [default = -1]; // also means volumes size
  // Optional field for file encryption
  optional FileEncryptionInfoProto fileEncryptionInfo = 15;

  optional uint32 storagePolicy = 16 [default = 0]; // block storage policy id
  optional string object_id = 17;
  optional string temperature = 18 [default = "WARM"];
  optional string temperature_mtime = 19;
  optional int32  ucState = 20; // for nnproxy fileinfo cache
  optional StripeLayout stripeLayout = 21;  // exist iff is striped file
  repeated StripeColumnInfo columnList = 22;     // exist iff is striped file

  optional HyperFileInfo hfile_info = 25;
  optional ShadowFileInfo shadow_info = 26;  // for user bytecool file(shadow file) path.

  
  optional ExternalOSSPolicyProto externalOSSPolicy = 30;
}

/**
 * Checksum algorithms/types used in HDFS
 * Make sure this enum's integer values match enum values' id properties defined
 * in org.apache.hadoop.util.DataChecksum.Type
 */
enum ChecksumTypeProto {
  CHECKSUM_NULL = 0;
  CHECKSUM_CRC32 = 1;
  CHECKSUM_CRC32C = 2;
}

/**
 * HDFS Server Defaults
 */
message FsServerDefaultsProto {
  required uint64 blockSize = 1;
  required uint32 bytesPerChecksum = 2;
  required uint32 writePacketSize = 3;
  required uint32 replication = 4; // Actually a short - only 16 bits used
  required uint32 fileBufferSize = 5;
  optional bool encryptDataTransfer = 6 [default = false];
  optional uint64 trashInterval = 7 [default = 0];
  optional ChecksumTypeProto checksumType = 8 [default = CHECKSUM_CRC32];
}


/**
 * Directory listing
 */
message DirectoryListingProto {
  repeated HdfsFileStatusProto partialListing = 1;
  required uint32 remainingEntries  = 2;
}

/**
 * Status of a snapshottable directory: besides the normal information for
 * a directory status, also include snapshot quota, number of snapshots, and
 * the full path of the parent directory.
 */
message SnapshottableDirectoryStatusProto {
  required HdfsFileStatusProto dirStatus = 1;

  // Fields specific for snapshottable directory
  required uint32 snapshot_quota = 2;
  required uint32 snapshot_number = 3;
  required bytes parent_fullpath = 4;
}

/**
 * Snapshottable directory listing
 */
message SnapshottableDirectoryListingProto {
  repeated SnapshottableDirectoryStatusProto snapshottableDirListing = 1;
}

/**
 * Snapshot diff report entry
 */
message SnapshotDiffReportEntryProto {
  required bytes fullpath = 1;
  required string modificationLabel = 2;
}

/**
 * Snapshot diff report
 */
message SnapshotDiffReportProto {
  // full path of the directory where snapshots were taken
  required string snapshotRoot = 1;
  required string fromSnapshot = 2;
  required string toSnapshot = 3;
  repeated SnapshotDiffReportEntryProto diffReportEntries = 4;
}

/**
 * Common node information shared by all the nodes in the cluster
 */
message StorageInfoProto {
  required uint32 layoutVersion = 1; // Layout version of the file system
  required uint32 namespceID = 2;    // File system namespace ID
  required string clusterID = 3;     // ID of the cluster
  required uint64 cTime = 4;         // File system creation time
}

/**
 * Information sent by a namenode to identify itself to the primary namenode.
 */
message NamenodeRegistrationProto {
  required string rpcAddress = 1;    // host:port of the namenode RPC address
  required string httpAddress = 2;   // host:port of the namenode http server
  enum NamenodeRoleProto {
    NAMENODE = 1;
    BACKUP = 2;
    CHECKPOINT = 3;
  }
  required StorageInfoProto storageInfo = 3;  // Node information
  optional NamenodeRoleProto role = 4 [default = NAMENODE];        // Namenode role
}

/**
 * Unique signature to identify checkpoint transactions.
 */
message CheckpointSignatureProto {
  required string blockPoolId = 1;
  required uint64 mostRecentCheckpointTxId = 2;
  required uint64 curSegmentTxId = 3;
  required StorageInfoProto storageInfo = 4;
}

/**
 * Command sent from one namenode to another namenode.
 */
message NamenodeCommandProto {
  enum Type {
    NamenodeCommand = 0;      // Base command
    CheckPointCommand = 1;    // Check point command
  }
  required uint32 action = 1;
  required Type type = 2;
  optional CheckpointCommandProto checkpointCmd = 3;
}

/**
 * Command returned from primary to checkpointing namenode.
 * This command has checkpoint signature that identifies
 * checkpoint transaction and is needed for further
 * communication related to checkpointing.
 */
message CheckpointCommandProto {
  // Unique signature to identify checkpoint transation
  required CheckpointSignatureProto signature = 1;

  // If true, return transfer image to primary upon the completion of checkpoint
  required bool needToReturnImage = 2;
}

/**
 * Block information
 */
message BlockProto {
  required uint64 blockId = 1;
  required uint64 genStamp = 2;
  optional uint64 numBytes = 3 [default = 0];
}

/**
 * Block and datanodes where is it located
 */
message BlockWithLocationsProto {
  required BlockProto block = 1;   // Block
  repeated string datanodeUuids = 2; // Datanodes with replicas of the block
  repeated string storageUuids = 3;  // Storages with replicas of the block
}

/**
 * List of block with locations
 */
message BlocksWithLocationsProto {
  repeated BlockWithLocationsProto blocks = 1;
}

/**
 * Editlog information with available transactions
 */
message RemoteEditLogProto {
  required uint64 startTxId = 1;  // Starting available edit log transaction
  required uint64 endTxId = 2;    // Ending available edit log transaction
  optional bool isInProgress = 3 [default = false];
}

/**
 * Enumeration of editlogs available on a remote namenode
 */
message RemoteEditLogManifestProto {
  repeated RemoteEditLogProto logs = 1;
}

/**
 * Namespace information that describes namespace on a namenode
 */
message NamespaceInfoProto {
  required string buildVersion = 1;         // Software revision version (e.g. an svn or git revision)
  required uint32 unused = 2;               // Retained for backward compatibility
  required string blockPoolID = 3;          // block pool used by the namespace
  required StorageInfoProto storageInfo = 4;// Node information
  required string softwareVersion = 5;      // Software version number (e.g. 2.0.0)
}

/**
 * Block access token information
 */
message BlockKeyProto {
  required uint32 keyId = 1;      // Key identifier
  required uint64 expiryDate = 2; // Expiry time in milliseconds
  optional bytes keyBytes = 3;    // Key secret
}

/**
 * Current key and set of block keys at the namenode.
 */
message ExportedBlockKeysProto {
  required bool isBlockTokenEnabled = 1;
  required uint64 keyUpdateInterval = 2;
  required uint64 tokenLifeTime = 3;
  required BlockKeyProto currentKey = 4;
  repeated BlockKeyProto allKeys = 5;
}

/**
 * State of a block replica at a datanode
 */
enum ReplicaStateProto {
  FINALIZED = 0;  // State of a replica when it is not modified
  RBW = 1;        // State of replica that is being written to
  RWR = 2;        // State of replica that is waiting to be recovered
  RUR = 3;        // State of replica that is under recovery
  TEMPORARY = 4;  // State of replica that is created for replication
}

/**
 * Block that needs to be recovered with at a given location
 */
message RecoveringBlockProto {
  required uint64 newGenStamp = 1;      // New genstamp post recovery
  required LocatedBlockProto block = 2; // Block to be recovered
}

/**
 * void request
 */
message VersionRequestProto {
}

/**
 * Version response from namenode.
 */
message VersionResponseProto {
  required NamespaceInfoProto info = 1;
}

/**
 * Information related to a snapshot
 * TODO: add more information
 */
message SnapshotInfoProto {
  required string snapshotName = 1;
  required string snapshotRoot = 2;
  required FsPermissionProto permission = 3;
  required string owner = 4;
  required string group = 5;
  required string createTime = 6;
  // TODO: do we need access time?
}

message ClientTraceTagProto {
    // global trace info
    optional string host = 1;
    optional string dc = 2;
    optional string psm = 3;
    optional string task_famliy = 4;
    optional string task_id = 5;
    optional int64 pid = 6;

    //user data
    optional string method = 7;
    optional string cross_dc = 8; // "none", "true", "false"
    optional string media_type = 9; // "none", "ssd", "hdd"
    optional int64 commit_time = 10;
    optional string path = 11;
}

message ClientTraceMetricProto {
    optional int64 throughout = 1;
    optional int64 qps = 2;
    optional int64 lantency_avg = 3;
    optional int64 lantency_p90 = 4;
    optional int64 lantency_p95 = 5;
    optional int64 lantency_p99 = 6;
    optional int64 lantency_p999 = 7;
    optional int64 lantency_max = 8;
    optional bool success = 9;
    optional ClientTraceTagProto tag = 10;
}

message SlowClientTraceProto {
    optional int64 throughout = 1;
    optional int64 lantency = 2;
    optional string remote_addr = 3;
    optional string path = 4;
    optional ClientTraceTagProto tag = 5;
}

message PathTraceProto {
    optional string type = 1;
    optional bool success = 2;
    optional string status = 3;//Normal, Warning, Fatal
    optional string task_family = 4;//primary key 1
    optional string task_id = 5;//primary key 2
    optional string host = 6;//primary key 3
    optional string path = 7;//primary key 4
    optional string dc = 8;
    optional int64 total_io_size = 9;
    optional int64 start_time_ms = 10;
    optional int64 end_time_ms = 11;
    optional int64 cost_time_us = 12;
    optional string exit_message = 13;
    optional string exit_exception = 14;
    optional string stage_trace = 15;
    optional string nn_trace = 16;
    optional string dn_trace = 17;
    optional int32 count = 18;
    optional string analysis = 19;
    optional string storage_type = 20;
    optional string merlin_job_id = 21;
    optional string sdk_version = 22;
    optional bool cross_dc = 23;
}


enum RpcStatus {
  RPC_SUCCESS = 0;
  RPC_ERROR = 1;
  RPC_ERROR_CHECKSUM = 2;
  RPC_ERROR_INVALID = 3;
  RPC_ERROR_EXISTS = 4;
  RPC_ERROR_ACCESS_TOKEN = 5;
  RPC_CHECKSUM_OK = 6;
  RPC_ERROR_UNSUPPORTED = 7;
  RPC_OOB_RESTART = 8;            // Quick restart
  RPC_OOB_RESERVED1 = 9;          // Reserved
  RPC_OOB_RESERVED2 = 10;         // Reserved
  RPC_OOB_RESERVED3 = 11;         // Reserved
  RPC_IN_PROGRESS = 12;
  RPC_FREEZE = 13;
  RPC_ERROR_DN_INVALID_PORT = 100;
  RPC_ERROR_DN_BLOCK_NOT_EXIST = 101;
  RPC_ERROR_DN_INTERNAL_ERROR = 102;
  RPC_ERROR_DN_NO_VISIBLE_DATA = 103;  // Block's visible length is less than request offset
  RPC_ERROR_DN_BLOCK_SEALED = 104;     // Block is sealed, write is forbidden.
  RPC_ERROR_NOT_ENOUGH_SPACE = 10001;  // cannot allocate enough disk to write
  RPC_ERROR_BUSY = 10002;
}

message CommonResponseHeaderProto {
  required RpcStatus status = 1;
  optional string errorContext = 2;
  optional uint32 serverEpoch = 3;
  repeated TraceBaggageProto baggages = 100;
}
