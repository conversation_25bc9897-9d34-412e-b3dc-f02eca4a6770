/********************************************************************
 * 2014 -
 * open source under Apache License Version 2.0
 ********************************************************************/
/**
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
#ifndef _HDFS_LIBHDFS3_SERVER_NAMENODEIMPL_H_
#define _HDFS_LIBHDFS3_SERVER_NAMENODEIMPL_H_

#include <atomic>
#include <mutex>

#include "client/ContentSummary.h"
#include "server/Namenode.h"
#include "server/NamenodeInfo.h"

namespace Hdfs {
namespace Internal {

class NamenodeImpl : public Namenode {
   public:
    NamenodeImpl(const std::vector<NamenodeInfo>& namenodeInfos,
                 const std::string& tokenService,
                 std::shared_ptr<SessionConfig> sc, std::shared_ptr<Config> c,
                 const RpcAuth& a);

    virtual ~NamenodeImpl();

    // Idempotent
    void getBlockLocations(const std::string& src, int64_t offset,
                           int64_t length, LocatedBlocks& lbs,
                           HyperFileMeta& coolMeta, bool fastvisit,
                           bool force_update_block,
                           bool for_input_stream = false);

    // Idempotent
    void getHyperBlockLocations(const std::string& src, int64_t offset,
                                int64_t length,
                                LocatedHyperBlocks& lhbs) /* throw
(AccessControlException, FileNotFoundException,
UnresolvedLinkException, HdfsIOException) */
        ;

    std::shared_ptr<FileStatus> create(
        const std::string& src, const Permission& masked,
        const std::string& clientName, int flag, bool createParent,
        short replication, int64_t blockSize,
        std::shared_ptr<std::map<std::string, HyperCacheMeta>> xttrs,
        int stripe_unit_count, int stripe_unit_size) /* throw
  (AccessControlException, AlreadyBeingCreatedException,
  DSQuotaExceededException, FileAlreadyExistsException, FileNotFoundException,
  NSQuotaExceededException, ParentNotDirectoryException,
  ReadOnlyCoolFileException, SafeModeException, UnresolvedLinkException,
  HdfsIOException) */
        ;

    std::pair<std::shared_ptr<LocatedBlock>, std::string> append(
        const std::string& src, const std::string& clientName)
        /* throw (AccessControlException,
             DSQuotaExceededException, FileNotFoundException,
             SafeModeException, UnresolvedLinkException, HdfsIOException) */
        ;

    // Idempotent
    bool setReplication(const std::string& src, short replication)
        /* throw (AccessControlException, DSQuotaExceededException,
         FileNotFoundException, SafeModeException, UnresolvedLinkException,
         HdfsIOException) */
        ;

    // Idempotent
    void setStoragePolicy(const std::string& src, const std::string& policyName)
        /* throw (AccessControlException, FileNotFoundException,
         SafeModeException, UnresolvedLinkException, NSQuotaExceededException,
         HdfsIOException) */
        ;

    void getStoragePolicies(std::vector<StoragePolicy>& policies)
        /* throw (HdfsIoException) */
        ;

    // Idempotent
    void setPermission(const std::string& src, const Permission& permission)
        /* throw (AccessControlException, FileNotFoundException,
         SafeModeException, UnresolvedLinkException, HdfsIOException) */
        ;

    // Idempotent
    void setOwner(const std::string& src, const std::string& username,
                  const std::string& groupname) /* throw
            (AccessControlException, FileNotFoundException, SafeModeException,
            UnresolvedLinkException, HdfsIOException) */
        ;

    void abandonBlock(const ExtendedBlock& b, const std::string& src,
                      const std::string& holder, uint64_t file_id) /* throw
            (AccessControlException, FileNotFoundException,
            UnresolvedLinkException, HdfsIOException) */
        ;

    std::shared_ptr<LocatedBlock> addBlock(
        const std::string& src, const std::string& clientName,
        const ExtendedBlock* previous,
        const std::vector<DatanodeInfo>& excludeNodes, uint64_t file_id,
        const std::shared_ptr<BlockStoragePolicyProto>& storagePolicy)
        /* throw (AccessControlException, FileNotFoundException,
         NotReplicatedYetException, SafeModeException,
         UnresolvedLinkException, HdfsIOException) */
        ;

    // Idempotent
    std::shared_ptr<LocatedBlock> getAdditionalDatanode(
        const std::string& src, const ExtendedBlock& blk,
        const std::vector<DatanodeInfo>& existings,
        const std::vector<std::string>& storageIDs,
        const std::vector<DatanodeInfo>& excludes, int numAdditionalNodes,
        const std::string& clientName, uint64_t file_id)
        /* throw (AccessControlException, FileNotFoundException,
         SafeModeException, UnresolvedLinkException, HdfsIOException) */
        ;

    bool complete(const std::string& src, const std::string& clientName,
                  const ExtendedBlock* last, uint64_t file_id) /* throw
            (AccessControlException, FileNotFoundException, SafeModeException,
            UnresolvedLinkException, HdfsIOException) */
        ;

    // Idempotent
    void reportBadBlocks(const std::vector<LocatedBlock>& blocks)
        /* throw (HdfsIOException) */;

    bool rename(const std::string& src, const std::string& dst)
        /* throw (UnresolvedLinkException, HdfsIOException) */;

    void concat(const std::string& trg, const std::vector<std::string>& srcs)
        /* throw (HdfsIOException, UnresolvedLinkException) */;

    bool truncate(const std::string& src, int64_t size,
                  const std::string& clientName)
        /* throw (HdfsIOException, UnresolvedLinkException) */;

    void getLease(const std::string& src, const std::string& clientName)
        /* throw (HdfsIOException, UnresolvedLinkException) */;

    void releaseLease(const std::string& src, const std::string& clientName)
        /* throw (HdfsIOException, UnresolvedLinkException) */;

    void rename2(const std::string& src, const std::string& dst)
        /* throw (DSQuotaExceededException,
         FileAlreadyExistsException, FileNotFoundException,
         NSQuotaExceededException, ParentNotDirectoryException,
         UnresolvedLinkException, HdfsIOException) ;*/
        ;

    bool deleteFile(const std::string& src, bool recursive)
        /* throw (AccessControlException, FileNotFoundException,
         SafeModeException, UnresolvedLinkException, HdfsIOException) */
        ;

    bool recoverLease(const std::string& path, const std::string& clientName)
        /* throw (AccessControlException, FileNotFoundException,
         SafeModeException, UnresolvedLinkException, HdfsIOException) */
        ;

    // Idempotent
    bool mkdirs(const std::string& src, const Permission& masked,
                bool createParent) /* throw (AccessControlException,
             FileAlreadyExistsException, FileNotFoundException,
             NSQuotaExceededException, ParentNotDirectoryException,
             SafeModeException, UnresolvedLinkException, HdfsIOException) */
        ;

    // Idempotent
    bool getListing(const std::string& src, const std::string& startAfter,
                    bool needLocation, std::vector<FileStatus>& dl)
        /* throw (AccessControlException, FileNotFoundException,
         UnresolvedLinkException, HdfsIOException) */
        ;

    // Idempotent
    void renewLease(const std::string& clientName,
                    const std::vector<std::string>& namenodebackends)
        /* throw (AccessControlException, HdfsIOException) */;

    // Idempotent
    std::vector<int64_t> getFsStats() /* throw (HdfsIOException) */;

    void metaSave(const std::string& filename) /* throw (HdfsIOException) */;

    // Idempotent
    FileStatus getFileInfo(const std::string& src, bool* exist)
        /* throw (AccessControlException, FileNotFoundException,
         UnresolvedLinkException, HdfsIOException) */
        ;

    // Idempotent
    bool isFileClosed(const std::string& src)
        /* throw (AccessControlException, FileNotFoundException,
            UnresolvedLinkException, HdfsIOException) */
        ;

    // Idempotent
    FileStatus getFileLinkInfo(const std::string& src)
        /* throw (AccessControlException, UnresolvedLinkException,
         HdfsIOException) */
        ;

    // Idempotent
    ContentSummary getContentSummary(const std::string& path)
        /* throw(AccessControlException, FileNotFoundException,
           UnresolvedLinkException, HdfsIOException) */
        ;

    // Idempotent
    void setQuota(const std::string& path, int64_t namespaceQuota,
                  int64_t diskspaceQuota) /* throw (AccessControlException,
             FileNotFoundException, UnresolvedLinkException,
             HdfsIOException) */
        ;

    // Idempotent
    void fsync(const std::string& src, const std::string& client,
               int64_t last_block_length, uint64_t file_id)
        /* throw (AccessControlException, FileNotFoundException,
         UnresolvedLinkException, HdfsIOException) */
        ;

    // Idempotent
    void setTimes(const std::string& src, int64_t mtime, int64_t atime)
        /* throw (AccessControlException, FileNotFoundException,
         UnresolvedLinkException, HdfsIOException) */
        ;

    void createSymlink(const std::string& target, const std::string& link,
                       const Permission& dirPerm, bool createParent)
        /* throw (AccessControlException, FileAlreadyExistsException,
         FileNotFoundException, ParentNotDirectoryException,
         SafeModeException, UnresolvedLinkException, HdfsIOException) */
        ;

    // Idempotent
    std::string getLinkTarget(const std::string& path)
        /* throw (AccessControlException, FileNotFoundException,
         HdfsIOException) */
        ;

    void createHardlink(const std::string& target, const std::string& link,
                        const Permission& dirPerm, bool createParent)
        /* throw (AccessControlException, FileAlreadyExistsException,
         FileNotFoundException, ParentNotDirectoryException,
         SafeModeException, UnresolvedLinkException, HdfsIOException) */
        ;

    // Idempotent
    std::shared_ptr<LocatedBlock> updateBlockForPipeline(
        const ExtendedBlock& block, const std::string& clientName)
        /* throw (HdfsIOException) */;

    void updatePipeline(const std::string& clientName,
                        const ExtendedBlock& oldBlock,
                        const ExtendedBlock& newBlock,
                        const std::vector<DatanodeInfo>& newNodes,
                        const std::vector<std::string>&
                            storageIDs) /* throw (HdfsIOException) */;

    // Idempotent
    Token getDelegationToken(const std::string& renewer)
        /* throws IOException*/;

    // Idempotent
    int64_t renewDelegationToken(const Token& token)
        /*throws IOException*/;

    // Idempotent
    void cancelDelegationToken(const Token& token)
        /*throws IOException*/;

    EncryptionKey getEncryptionKeys();

    bool createEncryptionZone(const std::string& src,
                              const std::string& keyName);
    /* throws HdfsIOException If an I/O error occurred */

    EncryptionZoneInfo getEncryptionZoneInfo(const std::string& src,
                                             bool* exist);
    /* throw (FileNotFoundException, UnresolvedLinkException, HdfsIOException)
     */
    bool listEncryptionZones(const int64_t id,
                             std::vector<EncryptionZoneInfo>& ezl);
    /* throw (AccessControlException, UnresolvedLinkException, HdfsIOException)
     */

    std::vector<XAttr> getXAttrs(const std::string& src,
                                 std::vector<XAttr>& xattrs);
    /* throw (FileNotFoundException, AccessControlException,
    UnresolvedLinkException, HdfsIOException) */

    void setXAttr(const std::string& src, const XAttr& xattr, int flag);
    /* throw (FileNotFoundException, AccessControlException,
    UnresolvedLinkException, HdfsIOException) */

    void removeXAttr(const std::string& src, const XAttr& xattr);

    std::shared_ptr<FileStatus> createHyperFile(
        const std::string& src, const std::string& clientName,
        const Permission& masked, bool createParent,
        const std::vector<std::string>& hyperBlockNames,
        const std::string& payload, std::vector<FileStatus>& retHBlocksInfo);

    void commitSlice(const std::string& archivePath, uint64_t archiveFileId,
                     const std::string& originalPath, uint64_t originalFileId,
                     std::optional<uint64_t> originalLen,
                     std::optional<uint64_t> originalMTime, uint64_t sliceOff,
                     uint64_t sliceLen, uint64_t expectEpoch,
                     const std::string& payload, uint64_t& retEpoch);

    void getArchiveSlices(const std::string& src,
                          std::optional<uint64_t> offset,
                          std::optional<uint64_t> length, uint64_t& retEpoch,
                          std::vector<HyperSliceInfo>& slices);

    void repairHFile(const std::string& hyperFilePath, bool forceRepair,
                     const std::vector<std::string>& corruptPaths,
                     uint64_t& retEpoch, std::string& retRepairPath,
                     std::vector<std::string>& realCorruptPaths);

    void commitRepairHFile(const std::string& hyperFilePath,
                           const std::vector<std::string>& corruptBlockNames,
                           const std::vector<std::string>& replaceBlockNames,
                           uint64_t expectEpoch,
                           std::optional<bool> cleanTrash);

    void close();

    virtual void invoke(const RpcCall& call);

   private:
    std::string pbToStr(const google::protobuf::Message& msg);

    void verifyGetBlockLocations(LocatedBlocks& lbs, int64_t off, int64_t len);

    RpcServerInfo getActiveServerInfo(uint32_t& oldValue);

    void failoverToNextNamenode(uint32_t oldValue);

    void sleepBeforeRetryForThrottleLimit(int times);
    void sleepBeforeFailover(int times);

    uint32_t calculateFailoverSleepTime(int times);

    void installNamenodeServers(const std::vector<NamenodeInfo>& servers);

    void updateAndInstallServers();

   private:
    std::shared_ptr<Config> xml_config_;
    RpcAuth auth;
    RpcConfig conf;
    RpcProtocolInfo protocol;
    std::shared_ptr<SessionConfig> session_config_;

    std::mutex mutNameNodes_;
    std::string clusterid_;
    std::string tokenService_;
    uint32_t currentNamenode_;
    bool enableNamenodeHA_;
    int maxNamenodeHARetry_;
    int HASleepBase_;
    int HASleepMax_;
    std::atomic<std::vector<RpcServerInfo>*> namenodes_;
};

}  // namespace Internal
}  // namespace Hdfs

#endif /* _HDFS_LIBHDFS3_SERVER_NAMENODEIMPL_H_ */
