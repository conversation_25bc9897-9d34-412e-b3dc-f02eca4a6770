/********************************************************************
 * 2014 -
 * open source under Apache License Version 2.0
 ********************************************************************/
/**
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
#ifndef _HDFS_LIBHDFS3_SERVER_LOCATEDBLOCKS_H_
#define _HDFS_LIBHDFS3_SERVER_LOCATEDBLOCKS_H_

#include <memory>
#include <string>

#include "common/assert.h"
#include "server/LocatedBlock.h"

namespace Hdfs {
namespace Internal {

class LocatedBlocks {
   public:
    virtual ~LocatedBlocks() {}

    virtual int64_t getFileLength() const = 0;

    virtual void setFileLength(int64_t fileLength) = 0;

    virtual bool isLastBlockComplete() const = 0;

    virtual void setIsLastBlockComplete(bool lastBlockComplete) = 0;

    virtual std::shared_ptr<LocatedBlock> getLastBlock() = 0;

    virtual void setLastBlock(std::shared_ptr<LocatedBlock> lastBlock) = 0;

    virtual bool isUnderConstruction() const = 0;

    virtual void setUnderConstruction(bool underConstruction) = 0;

    virtual const LocatedBlock* findBlock(int64_t position,
                                          bool return_last_block = true) = 0;

    virtual bool checkRangeAvailable(int64_t start, int64_t len) = 0;

    virtual std::vector<LocatedBlock>& getBlocks() = 0;

    virtual std::string getNamenodebackend() = 0;

    virtual void setNamenodebackend(const std::string& m_namenodebackend) = 0;

    virtual std::shared_ptr<LocatedBlocks> deepCopy() = 0;
};

/**
 * Collection of blocks with their locations and the file length.
 */
class LocatedBlocksImpl : public LocatedBlocks {
   public:
    int64_t getFileLength() const { return fileLength; }

    void setFileLength(int64_t fileLength) { this->fileLength = fileLength; }

    bool isLastBlockComplete() const { return lastBlockComplete; }

    void setIsLastBlockComplete(bool lastBlockComplete) {
        this->lastBlockComplete = lastBlockComplete;
    }

    std::shared_ptr<LocatedBlock> getLastBlock() {
        hdfs_assert(!lastBlockComplete);
        return lastBlock;
    }

    void setLastBlock(std::shared_ptr<LocatedBlock> lastBlock) {
        this->lastBlock = lastBlock;
    }

    bool isUnderConstruction() const { return underConstruction; }

    void setUnderConstruction(bool underConstruction) {
        this->underConstruction = underConstruction;
    }

    const LocatedBlock* findBlock(int64_t position,
                                  bool return_last_block = true);

    bool checkRangeAvailable(int64_t start, int64_t len);

    std::vector<LocatedBlock>& getBlocks() { return blocks; }

    std::string getNamenodebackend() { return namenodebackend; }

    void setNamenodebackend(const std::string& m_namenodebackend) {
        namenodebackend = m_namenodebackend;
    }

    std::shared_ptr<LocatedBlocks> deepCopy() {
        auto copy = std::make_shared<LocatedBlocksImpl>();
        copy->lastBlockComplete = this->lastBlockComplete;
        copy->underConstruction = this->underConstruction;
        copy->fileLength = this->fileLength;
        if (lastBlock) {
            copy->lastBlock = std::make_shared<LocatedBlock>(*lastBlock);
        }
        copy->blocks = blocks;
        copy->namenodebackend = this->namenodebackend;
        return copy;
    }

   private:
    bool lastBlockComplete;
    bool underConstruction;
    int64_t fileLength;
    std::shared_ptr<LocatedBlock> lastBlock;
    std::vector<LocatedBlock> blocks;
    std::string namenodebackend;
};

}  // namespace Internal
}  // namespace Hdfs
#endif /* _HDFS_LIBHDFS3_SERVER_LOCATEDBLOCKS_H_ */
