/********************************************************************
 * 2014 -
 * open source under Apache License Version 2.0
 ********************************************************************/
/**
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
#pragma once

#include <common/NetUtil.h>
#include <fmt/format.h>

#include <sstream>
#include <string>
#include <vector>

namespace Hdfs {
namespace Internal {

/**
 * This class extends the primary identifier of a Datanode with ephemeral
 * state, eg usage information, current administrative state, and the
 * network location that is communicated to clients.
 */
class DatanodeInfo {
   public:
    const std::string& getHostName() const { return hostName; }

    void setHostName(const std::string& hostName) { this->hostName = hostName; }

    uint32_t getInfoPort() const { return infoPort; }

    void setInfoPort(uint32_t infoPort) { this->infoPort = infoPort; }

    const std::string& getIpAddr() const { return ipAddr; }

    void setIpAddr(const std::string& ipAddr) { this->ipAddr = ipAddr; }

    uint32_t getIpcPort() const { return ipcPort; }

    void setIpcPort(uint32_t ipcPort) { this->ipcPort = ipcPort; }

    const std::string& getDatanodeId() const { return datanodeId; }

    void setDatanodeId(const std::string& storageId) {
        this->datanodeId = storageId;
    }

    uint32_t getXferPort() const { return xferPort; }

    void setXferPort(uint32_t xferPort) { this->xferPort = xferPort; }

    uint32_t getByteRpcPort() const { return byteRpcPort; };

    void setByteRpcPort(uint32_t byteRpcPort) {
        this->byteRpcPort = byteRpcPort;
    }

    const std::string formatAddress() const {
        return fmt::format("{}({})", hostName, getIpAddr());
    }

    bool operator<(const DatanodeInfo& other) const {
        return datanodeId < other.datanodeId;
    }

    bool operator==(const DatanodeInfo& other) const {
        return this->datanodeId == other.datanodeId &&
               this->ipAddr == other.ipAddr;
    }

    const std::string& getLocation() const { return location; }

    void setLocation(const std::string& location) {
        this->location = location;
        // /HL/xxxxx parse to dc
        if (location.empty()) {
            return;
        }
        if (location[0] != '/') {
            return;
        }

        std::string sub_location = location.substr(1);
        size_t pos = sub_location.find_first_of("/");
        if (pos == std::string::npos) {
            return;
        }
        dataCenter = sub_location.substr(0, pos);
    }

    std::string getXferAddr() const {
        if (isIPAdressV6(ipAddr)) {
            return fmt::format("[{}]:{}", ipAddr, getXferPort());
        } else {
            return fmt::format("{}:{}", ipAddr, getXferPort());
        }
    }

    const std::string& getDataCenter() const { return dataCenter; }

    void setDataCenter(const std::string& dc) { this->dataCenter = dc; }

    std::string toDebugString() const {
        std::stringstream ss;
        ss << "DatanodeInfo{"
           << "hostName=" << hostName << ", ipAddr=" << ipAddr
           << ", location=" << location << ", dataCenter=" << dataCenter << "}";
        return ss.str();
    }

   private:
    uint32_t xferPort;
    uint32_t infoPort;
    uint32_t ipcPort;
    uint32_t byteRpcPort{0};
    std::string ipAddr;
    std::string hostName;
    std::string datanodeId;
    std::string location;
    std::string dataCenter;
};

inline std::string FormatNodes(const std::vector<DatanodeInfo>& node_list) {
    std::stringstream ss;
    ss.imbue(std::locale::classic());
    ss << "[";

    for (size_t i = 0; i < node_list.size() - 1; ++i) {
        ss << node_list[i].formatAddress() << ", ";
    }

    if (node_list.empty()) {
        ss << "Empty";
    } else {
        ss << node_list.back().formatAddress();
    }

    ss << "]";
    return ss.str();
}

}  // namespace Internal
}  // namespace Hdfs
