package constants

import (
	"math"
)

type JobStatus int
type JobType int
type CommandType int

const (
	Accepted JobStatus = iota
	Submitted
	Running
	Finished
	Cancelled
	Failed
)

const (
	UnknownJob JobType = iota
	LoadDataJob
	LoadMetadataJob
	FreeJob
)

const (
	List CommandType = iota
	Du
)

const (
	SyncInterval  = string("cfs.filesystem.sync-interval")
	UfsPath       = string("cfs.filesystem.ufs-path")
	IgnoreConfKey = string("cfs.filesystem.limit")
	TimeTemplate  = string("2006-01-02 15:04:05")

	MetaConfFile = string("/.cfs/config")
	LogFile      = string("/var/log/cloudfs/acc-cli/cfs.log")

	TosPathPrefix = string("tos://")

	//Meta conf
	MetaConfLocalMountPointKey = string("local_mount_point")
	MetaConfTosMountPointKey   = string("tos_mount_point")
	MetaConfConfigPath         = string("config_path")

	//Job
	JobLoad = string("load")
	JobFree = string("free")
	JobLookup = string("lookup")

	//Pin
	Pin = string("True")
	Unpin = string("False")

	// libhdfs3
	RESP_SUCCESS = string("Success")

	InvalidUploadIntervalMs = -2
	InvalidReplicaLocalSwitch = math.MaxInt32
	InvalidReplicaOtherSwitch = math.MaxInt32
	InvalidDcNames = "invalid_dc"
)

