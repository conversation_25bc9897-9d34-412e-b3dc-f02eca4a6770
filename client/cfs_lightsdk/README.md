1、编译fsclient.so<br>
cd src目录下执行:make, 编译出来的fslient.so在bin目录下.<br>
make支持添加以下编译选项的单个或者多个的组合：<br>
make DEBUG=true  表式编译带debug信息的so<br>
make DEBUG_LOG=true 表示开启debug日志<br>
make CHECK_READ_DATA=true 表示开启数据校验<br>
<br>
<br>
2、创建配置文件或配置环境变量<br>
fslient.so初始化有两种方式，第一种是读取/etc/fsclient.conf的信息用于初始化。将fsclient.conf拷贝到/etc目录下即可。<br>
/etc/fsclient.conf文件的格式如下:<br>
<br>
mount_dir = /data00/tiger/new_home/chencheng/cfs_mnt<br>
shm_dir = /dev/shm<br>
meta_dir = /dev/shm<br>
mirror_dir = /data00/tiger/new_home/chencheng<br>
log_path = /tmp/fsclient.log<br>
<br>
mount_dir fuse挂载目录。<br>
mount_dir最多可以有4个，挂载路径用分号隔开。即一个应用程序允许和最多4个后端Agent交互，例如训练任务可以挂载只读数据集和读写checkpoint两个CFS Fuse作为后端。后面的shm_dir、meta_dir等也需要配置相同数量的目录<br>
shm_dir表示共享内存所挂载的路径，通常位于/dev/shm内<br>
meta_dir为用于数据同步/前后端通信的共享内存的路径。如果没有该目录需要创建<br>
mirror_dir是mount_dir对应的镜像目录，用于数据校验相关，和make CHECK_READ_DATA=true结合使用。<br>
当编译so开启数据校验时，需要把校验的数据目录从data_dir中拷贝到mirror_data_dir，这里需要保持目录层级结构对应一致。这时每一个读IO都会先走fsclient流程从data_dir读取文件，然后再调用原生的read从mirror_dir读取同名文件读取，两者内容作比较，内容不一致则失败退出。开起数据校验会导致读性能下降。<br>
<br>
第二种方法是直接配置以下环境变量：<br>
export FSCLIENT_MOUNT_DIR="/data00/tiger/new_home/chencheng/cfs_mnt"<br>
export FSCLIENT_SHM_DIR="/dev/shm"<br>
export FSCLIENT_META_DIR="/dev/shm"<br>
export FSCLIENT_MIRROR_DIR="/data00/tiger/new_home/chencheng"<br>
export FSCLIENT_LOG_PATH="/tmp/fsclient.log"<br>
<br>
推荐使用第二种方法。注意环境变量的配置会覆盖/etc/fsclient.conf中的配置。<br>
<br>

3、在执行命令时前面增加：LD_PRELOAD=$fsclient/src/bin/libfsclient.so<br>
比如执行:md5sum a.txt时命令改成：<br>
LD_PRELOAD=$fsclient/src/bin/libfsclient.so md5sum a.txt<br>
<br>
4、fsclient需要与后端配合使用。后端目前暂时对接了CFS fuse。需要在CFS fuse的conf文件里增加：<br>
--cfs_fuse_agent=true<br>

