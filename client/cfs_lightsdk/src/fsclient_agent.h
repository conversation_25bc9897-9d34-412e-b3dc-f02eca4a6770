#ifndef __FSCLIENT_AGENT_H
#define __FSCLIENT_AGENT_H

#include <limits.h>
#include <stdint.h>
#include <sys/xattr.h>

#include <atomic>
#include <chrono>
#include <deque>
#include <map>
#include <string>
#include <vector>

#define XATTR_SIZE 600
#define MAX_DEFAULT_NUM 65536
#define MAX_CLIENT_NUM 4

#define FS_CLIENT_BLOCK_SIZE (128 * 1024 * 1024)
#define FS_CLIENT_PAGE_SIZE (1 * 1024 * 1024 * 1024)
#define FS_CLIENT_SUBBLOCK_SIZE (2 * 1024 * 1024)
#define FS_CLIENT_MAX_NUM_PAGES 128
#define FS_CLIENT_BLOCKS_PER_PAGE 8
#define FS_CLIENT_SUBBLOCKS_PER_PAGE 512
#define FS_CLIENT_SUBBLOCKS_PER_BLOCK 64
#define FS_CLIENT_PREFETCH_BLOCKS_FRONT 16
#define FS_CLIENT_RESERVE_BLOCKS_BEHIND 0
#define FS_CLIENT_RANDOM_READ_MIN_IO_SIZE (4 * 1024 * 1024)

#define FS_CLIENT_MAX_WRITE_CONCURRENCY 16
#define FS_CLIENT_MAX_WRITE_PART_BLOCKS 8

#define DIV_ROUND_UP(n, d) (((n) + (d)-1) / (d))
#define MIN(a, b) ((a) < (b) ? (a) : (b))
#define MAX(a, b) ((a) > (b) ? (a) : (b))

#define STREAM_TYPE_READ_ONLY_SEQUENCE 1
#define STREAM_TYPE_READ_ONLY_RANDOM 2
#define STREAM_TYPE_WRITE_ONLY 3
#define STREAM_TYPE_READ_WRITE 4

struct StreamInfo {
  int fd;
  int type;  // 0: read-only  1: write-only  2: read-write
  std::string abs_path;
  int64_t file_id;
  int client_idx;
  int64_t file_size;
  int64_t start_blk_offset;  // start block offset of a stream
  std::deque<int64_t>
      blocks;  // list of continuous blocks (start from start_blk_offset) inside
               // a file that are referenced by a stream
  std::vector<int64_t> subblocks;  // used only for read only small file

  int64_t curr_write_blk_idx_in_file;
  int64_t curr_write_blk_idx;
  int64_t total_write_size;
  std::deque<int64_t> curr_write_blocks;
  std::atomic<int64_t> write_cursor;

  std::chrono::high_resolution_clock::time_point start_time;
  bool start_io;

  StreamInfo(int fd)
      : fd(fd),
        file_id(-1),
        start_blk_offset(-1),
        total_write_size(0),
        start_io(false){};
};

typedef struct fileHandle {
  int fd;
  int64_t file_id;
  int client_idx;
  char file_name[PATH_MAX];
  int64_t current_offset;
  int64_t file_size;
  int is_eof;
  int is_err;
  StreamInfo* stream;
  pthread_rwlock_t rwlock;
  int read_invalid;

  int total_futex;
  int total_wait_futex;
#ifdef CHECK_READ_DATA
  int mirror_file_fd;
  char mirror_file_name[PATH_MAX];
#endif
} fileHandle_t;

typedef struct FhTableInfo {
  pthread_mutex_t fh_table_mutex;
  std::atomic<long> max_fds;  // fhTable可存储的fd数量
  fileHandle_t** fh_table;    // 可变的数组, fd超过65536时使用
  fileHandle_t* fh_array[MAX_DEFAULT_NUM];  // 默认数组，fd小于65536时使用
} FhTableInfo_t;

typedef struct writeFileMap {
  pthread_rwlock_t rwlock;
  std::map<std::string, int> file_map;
} writeFileMap_t;

typedef struct openFileMap {
  pthread_mutex_t mutex;
  std::map<std::string, std::map<int, fileHandle_t*>> file_map;
} openFileMap_t;

// 定义命令槽位状态
enum SlotState : uint32_t {
  SLOT_EMPTY = 0,
  SLOT_READY = 1,
  SLOT_PROCESSING = 2,
  SLOT_DONE = 3
};

constexpr size_t CACHE_LINE_SIZE = 64;
struct alignas(CACHE_LINE_SIZE) CommandDescriptor {
  std::atomic<uint32_t> state;
  int32_t type;
  uint32_t param_size;
  uint32_t result_len;
  uint64_t param_offset;
  uint64_t result_offset;
  uint64_t completion_token;
  char padding[CACHE_LINE_SIZE - sizeof(std::atomic<uint32_t>) -
               sizeof(int32_t) - 2 * sizeof(uint32_t) - 3 * sizeof(uint64_t)];
};

struct alignas(CACHE_LINE_SIZE) RingMeta {
  uint32_t ring_size;
  uint32_t mask;

  alignas(CACHE_LINE_SIZE) std::atomic<uint32_t> head;
  alignas(CACHE_LINE_SIZE) std::atomic<uint32_t> tail;

  uint32_t cmd_offset;
  uint32_t data_offset;
  std::atomic<uint32_t> notify;
  char padding[CACHE_LINE_SIZE - 6 * sizeof(uint32_t)];
};

struct FsRing {
  void* shm_addr;
  char* data_area;
  size_t shm_size;
  RingMeta* meta;
  CommandDescriptor* cmds;
};

typedef struct agentClientInterface {
  pthread_mutex_t shm_init_mutex;
  void* pages[FS_CLIENT_MAX_NUM_PAGES];
  std::atomic<int64_t>
      brefcount[FS_CLIENT_MAX_NUM_PAGES * FS_CLIENT_BLOCKS_PER_PAGE];
  int current_page_num;
  void* nsync;  // per-subblock futex to sync between frontend and backend
  FsRing ior;
} agentClientInterface_t;

typedef struct agentGlobal {
  agentClientInterface_t client[MAX_CLIENT_NUM];

  FhTableInfo_t file_handle_table;
  openFileMap_t open_file_map;
} agentGlobal_t;

#define COMMAND_AGENT_PREFETCH 0
#define COMMAND_AGENT_PREFETCH_SMALL_FILE 1
#define COMMAND_AGENT_FETCH 2
#define COMMAND_AGENT_EVICT 3
#define COMMAND_AGENT_FLUSH 4
#define COMMAND_AGENT_WRITE 4
#define COMMAND_AGENT_CLOSE_FLUSH 5

#define AGENT_CACHE_MAX_PREFETCH_BLOCKS 64
struct AgentPrefetchResponse {
  int res;
  int64_t num_blocks;
  int64_t blocks[AGENT_CACHE_MAX_PREFETCH_BLOCKS];
};

struct AgentPrefetchRequest {
  int64_t file_id;
  int64_t start_blk_idx;
  int num_blocks;
};

struct AgentFetchResponse {
  int res;
  bool is_subblock;
  int64_t num_blocks;
  int64_t blocks[AGENT_CACHE_MAX_PREFETCH_BLOCKS];
};

struct AgentFetchRequest {
  int64_t file_id;
  int64_t start_fetch_offset;
  int64_t fetch_size;
};

struct AgentPrefetchSmallFileRequest {
  int64_t file_id;
};

struct AgentEvictResponse {
  int res;
};
struct AgentEvictRequest {
  int64_t blk_idx;
};

struct AgentFlushResponse {
  int res;
  int64_t block;
};

struct AgentFlushRequest {
  int64_t file_id;
  int64_t offset;
};

struct AgentWriteResponse {
  int res;
  int64_t num_blocks;
  int64_t blocks[AGENT_CACHE_MAX_PREFETCH_BLOCKS];
};

struct AgentWriteRequest {
  int64_t file_id;
  int64_t offset;
  int64_t num_blocks;
};

struct AgentCloseFlushResponse {
  int res;
};

struct AgentCloseFlushRequest {
  int64_t file_id;
  int64_t end_cursor;
  int64_t curr_blk_idx;
};

struct AgentGetFileIdResponse {
  int64_t file_id;
};

struct FuseCacheNsync {  // per-subblock futex list to sync between frontend and
                         // backend
  std::atomic<uint32_t>
      futex[FS_CLIENT_MAX_NUM_PAGES * FS_CLIENT_BLOCKS_PER_PAGE]
           [FS_CLIENT_SUBBLOCKS_PER_BLOCK];
};

void fsclientInit();
#endif