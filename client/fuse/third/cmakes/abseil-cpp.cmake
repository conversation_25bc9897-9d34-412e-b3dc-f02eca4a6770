set(lib_name abseil-cpp)
ExternalProject_Add(
  ${lib_name}
  URL https://github.com/abseil/abseil-cpp/archive/refs/tags/20230802.1.tar.gz
  URL_HASH MD5=84665b6daa5dda7c9082b1a00057457a
  DOWNLOAD_NAME abseil-cpp-20230802.1.tar.gz
  PREFIX ${CMAKE_CURRENT_BINARY_DIR}/${lib_name}
  DOWNLOAD_DIR ${DOWNLOAD_DIR}
  BUILD_IN_SOURCE FALSE
  CMAKE_ARGS
    ${common_cmake_args}
    -DCMAKE_CXX_STANDARD=${CMAKE_CXX_STANDARD}
    -DCMAKE_CXX_STANDARD_REQUIRED=${CMAKE_CXX_STANDARD_REQUIRED}
    -DCMAKE_CXX_EXTENSIONS=${CMAKE_CXX_EXTENSIONS}
  BUILD_COMMAND make -s -j${NUM_JOBS_PER_LIB}
  INSTALL_COMMAND make -s install
  LOG_CONFIGURE TRUE
  LOG_BUILD TRUE
  LOG_INSTALL TRUE
  )
