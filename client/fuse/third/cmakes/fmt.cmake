set(lib_name fmt)
ExternalProject_Add(
    ${lib_name}
    URL https://github.com/fmtlib/fmt/archive/refs/tags/10.1.1.tar.gz
    URL_HASH MD5=0d41a16f1b3878d44e6fd7ff1f6cc45a
    DOWNLOAD_NAME fmt-10.1.1.tar.gz
    PREFIX ${CMAKE_CURRENT_BINARY_DIR}/${lib_name}
    DOWNLOAD_DIR ${DOWNLOAD_DIR}
    BUILD_IN_SOURCE FALSE
    CMAKE_ARGS
      ${common_cmake_args}
      -DFMT_MASTER_PROJECT=OFF
    BUILD_COMMAND make -s -j${NUM_JOBS_PER_LIB}
    INSTALL_COMMAND make -s install
    LOG_CONFIGURE TRUE
    LOG_BUILD TRUE
    LOG_INSTALL TRUE
)
