# Usage (1 or 2):
#   1. `export CFS_SDK_CONF=/path_to_this_file`
#   2. `./cfs_fuse2 mount /mount_point_path --config-file /path_to_this_file`
--cfs_filesystem_mode=ACC
--cfs_filesystem_uri=cfs://xxx.cfs-cn-beijing.ivolces.com
--cfs_filesystem_ns_id=0
--cfs_filesystem_ufs_uri=tos://bucket/nn_prefix/client_prefix/
--cfs_filesystem_endpoint_resolve_by_dns=true
--cfs_filesystem_endpoint_ip=10.0.0.0
--cfs_expect_dn_subnet=***********/16
--cfs_enable_multi_data_nic=false
--cfs_multi_data_nic_blacklist=
--cfs_log_type=file
--cfs_log_level=info
--cfs_log_dir=~/cfs_fuse_log
--cfs_enable_byterpc_log=true
--cfs_enable_auth=false
--cfs_access_key=AKXXXXXXX
--cfs_secret_key=SKXXXXXXX==
--cfs_security_token=STSXXXXXXXX
