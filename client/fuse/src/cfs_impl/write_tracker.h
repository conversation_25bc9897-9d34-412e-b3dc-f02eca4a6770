/*
#pragma once

#include <shared_mutex>
#include <unordered_map>

#include "impl/cfs_impl.h"

namespace cfs {
namespace fuse {

// This class is used to track append-writing files. When FUSE appends a file
// and getattr at the same time, it will use the file-len from getattr as the
// next write offset passed to cfs_write. Since the file-len from getattr is
// got from cfs_get_file_info, which is shorter than the being-writing-file,
// it will cause the offset-backward for the next write. So we must track
// all appending-files and return the std::max(write_cursor, flen_from_NN)
// as the file_lengh to getattr.
class WriteTracker {
 public:
  static WriteTracker* GetInstance();

  ~WriteTracker() = default;

  void AddWritingFile(uint64_t file_id, cfs_file* fh);

  void RemoveWritingFile(uint64_t file_id, cfs_file* fh);

  // return -1 if file_id not exist.
  int64_t GetWriteCursor(uint64_t file_id) const;

 private:
  WriteTracker() = default;
  WriteTracker(const WriteTracker& other) = delete;
  WriteTracker(WriteTracker&& other) = delete;

 private:
  // TODO(dbc) Use concurrent hashset to get rid of mutex lock each time. This
  // is useful when there are many concurrent create-close of small files.
  std::unordered_map<uint64_t, cfs_file*> file_map_;
  mutable std::shared_mutex mtx_;
};

}  // namespace fuse
}  // namespace cfs
*/