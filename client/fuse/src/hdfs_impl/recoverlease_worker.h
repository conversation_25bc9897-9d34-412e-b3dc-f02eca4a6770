#pragma once

#include <condition_variable>
#include <deque>
#include <mutex>
#include <string>
#include <thread>

#include "hdfs.h"

class RecoverLeaseWorker {
 public:
  static void start(hdfsFS fs, int sleepTimeMs = 10000, int intervalMs = 30000);
  static void stop();
  static void addPath(std::string path);

 private:
  static void recoverleaseAllPath();
  static void flushQueue();

 private:
  static bool running;
  static std::thread thread_worker;
  static std::mutex mtx;
  static std::mutex queue_mtx;
  static std::condition_variable cv;
  static hdfsFS fs_inner;
  static std::deque<std::string> path_queue;
  static std::deque<std::string> path_to_recover_queue;
  static int baseSleepTimeMs;
};
