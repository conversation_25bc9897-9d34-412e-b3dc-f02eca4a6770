#pragma once

#define FUSE_USE_VERSION 35
#include <libfuse3/fuse.h>

#include "hdfs.h"

extern hdfsFS root_fs;

int hdfs_getattr(const char* path, struct stat* stat_buf,
                 struct fuse_file_info* fi);

int hdfs_readlink(const char* path, char* link, size_t size);

int hdfs_mknod(const char* path, mode_t mode, dev_t dev);

int hdfs_mkdir(const char* path, mode_t mode);

int hdfs_unlink(const char* path);

int hdfs_rmdir(const char* path);

int hdfs_symlink(const char* path, const char* link);

int hdfs_rename(const char* path, const char* new_path, unsigned int flags);

int hdfs_link(const char* path, const char* link);

int hdfs_chmod(const char* path, mode_t mode, struct fuse_file_info* fi);

int hdfs_chown(const char* path, uid_t uid, gid_t gid,
               struct fuse_file_info* fi);

int hdfs_truncate(const char* path, off_t off, struct fuse_file_info* fi);

int hdfs_open(const char* path, struct fuse_file_info* fi);

int hdfs_read(const char* path, char* buf, size_t size, off_t off,
              struct fuse_file_info* fi);

int hdfs_write(const char* path, const char* buf, size_t size, off_t off,
               struct fuse_file_info* fi);

int hdfs_write_buf(const char* path, struct fuse_bufvec* buf, off_t off,
                   struct fuse_file_info* fi);

int hdfs_statfs(const char* path, struct statvfs* stat_buf);

int hdfs_flush(const char* path, struct fuse_file_info* fi);

int hdfs_release(const char* path, struct fuse_file_info* fi);

int hdfs_fsync(const char* path, int datasync, struct fuse_file_info* fi);

int hdfs_setxattr(const char* path, const char* name, const char* value,
                  size_t size, int flags);

int hdfs_getxattr(const char* path, const char* name, char* value, size_t size);

int hdfs_listxattr(const char* path, char* list, size_t size);

int hdfs_removexattr(const char* path, const char* name);

int hdfs_opendir(const char* path, struct fuse_file_info* fi);

int hdfs_readdir(const char* path, void* buf, fuse_fill_dir_t filler, off_t off,
                 struct fuse_file_info* fi,
                 enum fuse_readdir_flags readdir_flags);

int hdfs_releasedir(const char* path, struct fuse_file_info* fi);

int hdfs_fsyncdir(const char* path, int datasync, struct fuse_file_info* fi);

void* hdfs_init(struct fuse_conn_info* conn, struct fuse_config* cfg);

void hdfs_destroy(void* userdata);

int hdfs_access(const char* path, int mask);

int hdfs_create(const char* path, mode_t mode, struct fuse_file_info* fi);

int hdfs_utimens(const char* path, const struct timespec tv[2],
                 struct fuse_file_info* fi);

int hdfs_bmap(const char* path, size_t blocksize, uint64_t* idx);

int hdfs_ioctl(const char* path, unsigned int cmd, void* arg,
               struct fuse_file_info* fi, unsigned int flags, void* data);

int hdfs_poll(const char* path, struct fuse_file_info* fi,
              struct fuse_pollhandle* ph, unsigned* reventsp);

int hdfs_fallocate(const char* path, int mode, off_t off, off_t len,
                   struct fuse_file_info* fi);

ssize_t hdfs_copy_file_range(const char* path_in, struct fuse_file_info* fi_in,
                             off_t offset_in, const char* path_out,
                             struct fuse_file_info* fi_out, off_t offset_out,
                             size_t size, int flags);

off_t hdfs_lseek(const char* path, off_t off, int whence,
                 struct fuse_file_info* fuse_file_info);
