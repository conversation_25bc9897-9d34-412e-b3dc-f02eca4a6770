#pragma once

#include <cstdlib>

#include "common/macros.h"

// enable/disable log calls at compile time according to global level.
//
// define SPDLOG_ACTIVE_LEVEL to one of those (before including spdlog.h):
// SPDLOG_LEVEL_TRACE,
// SPDLOG_LEVEL_DEBUG,
// SPDLOG_LEVEL_INFO,
// SPDLOG_LEVEL_WARN,
// SPDLOG_LEVEL_ERROR,
// SPDLOG_LEVEL_CRITICAL,
// SPDLOG_LEVEL_OFF
#ifndef SPDLOG_ACTIVE_LEVEL
#define SPDLOG_ACTIVE_LEVEL SPDLOG_LEVEL_DEBUG
#endif
#include <spdlog/spdlog.h>

namespace bds {
namespace fuse {

// The logger is used to record logs for hdfs client. It use an internal
// spdlog as its implementation. Users should use the following macros
// in most cases:
//
// BDSLOG(DEBUG, "some fmt string", arg1, ...);
// BDSLOG(INFO, "some fmt string", arg1, ...);
// BDSLOG(WARN, "some fmt string", arg1, ...);
// BDSLOG(ERROR, "some fmt string", arg1, ...);
// BDSLOG(CRITICAL, "some fmt string", arg1, ...);
//
// The fmt string derived from fmt library, for example:
// BDSLOG(INFO, "int: {}, double: {}, string: {}", 10, 1.1, "hello");
//
// You can refer to https://github.com/gabime/spdlog and
// https://fmt.dev/latest/syntax.html for more advanced usage.
// A example is also provided in test/common/logger_test.cc.
namespace LogLevel = spdlog::level;

class Logger final {
 public:
  static Logger* Instance();
  static void DestroySingleton();
  // supported log levels are: debug, info, warn, error, critical

  ~Logger() = default;

  // Get the pointer to the internal logger impl.
  spdlog::logger* GetRaw() const;

  // Determine whether the default logger should log messages with a
  // certain level
  bool ShouldLog(LogLevel::level_enum level);

 private:
  Logger();

  void InitLogger();

  std::shared_ptr<spdlog::logger> logger_;
};

}  // namespace fuse
}  // namespace bds

// Supported log levels include: DEBUG, INFO, WARN, ERROR, CRITICAL, FATAL
//
// Note that spdlog does not support FATAL log originally. We implement FATAL
// log by calling std::abort() manually.
#define SPDLOG_LOGGER_FATAL(logger, ...)         \
  do {                                           \
    SPDLOG_LOGGER_CRITICAL(logger, __VA_ARGS__); \
    logger->flush();                             \
    spdlog::shutdown();                          \
    std::abort();                                \
  } while (false)

#define BDSLOG(level, ...) \
  SPDLOG_LOGGER_##level(bds::fuse::Logger::Instance()->GetRaw(), __VA_ARGS__)

#define BDSLOG_IF(level, condition, ...) \
  if (condition) {                       \
    BDSLOG(level, __VA_ARGS__);          \
  }

#define BDS_CHECK(condition) \
  BDSLOG_IF(FATAL, BDS_UNLIKELY(!(condition)), "Check failed: " #condition " ")

#define BDS_CHECK_OP(op, val1, val2)                                        \
  BDSLOG_IF(FATAL, BDS_UNLIKELY(!((val1)op(val2))),                         \
            "Check failed: " #val1 " " #op " " #val2 " , {} vs {}", (val1), \
            (val2))

#define BDS_CHECK_EQ(val1, val2) BDS_CHECK_OP(==, val1, val2)
#define BDS_CHECK_NE(val1, val2) BDS_CHECK_OP(!=, val1, val2)
#define BDS_CHECK_LE(val1, val2) BDS_CHECK_OP(<=, val1, val2)
#define BDS_CHECK_LT(val1, val2) BDS_CHECK_OP(<, val1, val2)
#define BDS_CHECK_GE(val1, val2) BDS_CHECK_OP(>=, val1, val2)
#define BDS_CHECK_GT(val1, val2) BDS_CHECK_OP(>, val1, val2)

#if defined(NDEBUG)
#define BDS_DCHECK_IS_ON() 0
#define BDS_DLOG_IS_ON() 0
#else
#define BDS_DCHECK_IS_ON() 1
#define BDS_DLOG_IS_ON() 1
#endif

#if BDS_DCHECK_IS_ON()
#define BDS_DCHECK(condition) BDS_CHECK(condition)
#define BDS_DCHECK_EQ(val1, val2) BDS_CHECK_EQ(val1, val2)
#define BDS_DCHECK_NE(val1, val2) BDS_CHECK_NE(val1, val2)
#define BDS_DCHECK_LE(val1, val2) BDS_CHECK_LE(val1, val2)
#define BDS_DCHECK_LT(val1, val2) BDS_CHECK_LT(val1, val2)
#define BDS_DCHECK_GE(val1, val2) BDS_CHECK_GE(val1, val2)
#define BDS_DCHECK_GT(val1, val2) BDS_CHECK_GT(val1, val2)
#else  // !BDS_DCHECK_IS_ON()
#define BDS_DCHECK(condition) void(0)
#define BDS_DCHECK_EQ(val1, val2) void(0)
#define BDS_DCHECK_NE(val1, val2) void(0)
#define BDS_DCHECK_LE(val1, val2) void(0)
#define BDS_DCHECK_LT(val1, val2) void(0)
#define BDS_DCHECK_GE(val1, val2) void(0)
#define BDS_DCHECK_GT(val1, val2) void(0)
#endif  // BDS_DCHECK_IS_ON()

#if BDS_DLOG_IS_ON()
#define BDSDLOG(level, ...) BDSLOG(level, __VA_ARGS__)
#else  // !BDS_DLOG_IS_ON()
#define BDSDLOG(level, ...) void(0)
#endif  // BDS_DLOG_IS_ON()

#define CHECK_ERROR(X, Y, Z)                                           \
  if (X == Y) {                                                        \
    BDSLOG(ERROR, #Z " failed, path {}, error message: {}, errno: {}", \
           npath.c_str(), hdfsGetLastError(), errno);                  \
    return -errno;                                                     \
  }
