#include "common/datetime_util.h"

#include <sys/time.h>

#include <cstdint>

#include "common/logger.h"

namespace bds {
namespace fuse {

std::string DatetimeUtil::Time2UTCStr(time_t time_sec,
                                      const std::string_view& format) {
  struct tm cal_tm;
  // The string representation of a date is most 19 + 1('\0') = 20 chars long
  // (e.g. 2021-12-02 12:12:12), so a 32-bytes buf is enough.
  char buf[32];
  struct tm* tm_res = gmtime_r(&time_sec, &cal_tm);
  size_t str_len = strftime(buf, sizeof(buf), format.data(), tm_res);
  // strftime must succeed with a valid 'format'
  BDS_DCHECK_NE(str_len, 0);
  return std::string(buf, str_len);
}

std::string DatetimeUtil::Time2LocalStr(time_t time_sec,
                                        const std::string_view& format) {
  struct tm cal_tm;
  // The string representation of a date is most 19 + 1('\0') = 20 chars long
  // (e.g. 2021-12-02 12:12:12), so a 32-bytes buf is enough.
  char buf[32];
  struct tm* tm_res = localtime_r(&time_sec, &cal_tm);
  size_t str_len = strftime(buf, sizeof(buf), format.data(), tm_res);
  // strftime must succeed with a valid 'format'
  BDS_DCHECK_NE(str_len, 0);
  return std::string(buf, str_len);
}

std::string DatetimeUtil::GetNowTimeUTCStr(const std::string_view& format) {
  struct timeval now_time;
  gettimeofday(&now_time, nullptr);
  return Time2UTCStr(now_time.tv_sec, format);
}

std::string DatetimeUtil::GetNowTimeLocalStr(const std::string_view& format) {
  struct timeval now_time;
  gettimeofday(&now_time, nullptr);
  return Time2LocalStr(now_time.tv_sec, format);
}

int64_t DatetimeUtil::GetNowTimeUs() {
  struct timeval now_time;
  gettimeofday(&now_time, nullptr);
  return now_time.tv_sec * 1000000 + now_time.tv_usec;
}

int64_t DatetimeUtil::GetNowTimeMs() {
  return GetNowTimeUs() / 1000;
}

}  // namespace fuse
}  // namespace bds
