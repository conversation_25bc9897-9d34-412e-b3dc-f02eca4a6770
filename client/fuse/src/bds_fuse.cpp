#include "bds_fuse.h"

#include <dirent.h>
#include <grp.h>
#include <pthread.h>
#include <pwd.h>
#include <sys/ioctl.h>
#include <sys/mman.h>
#include <sys/stat.h>
#include <unistd.h>

#include <cerrno>
#include <cstdio>
#include <cstdlib>
#include <cstring>
#include <iostream>
#include <string>

#include "bds_config.h"
#include "cfs_impl/cfs_fuse.h"
#include "common/logger.h"

#ifdef DISABLE_BYTE_HDFS

#define META_OP(method, ...) cfsv2_##method(__VA_ARGS__)

#define DATA_OP(method, ...) cfsv2_raw_##method(__VA_ARGS__)

#else

#include "hdfs_impl/hdfs_fuse.h"

#define META_OP(method, ...)                                           \
  FileSystemType::CFS == BdsConfig::GetInstance()->GetFileSystemType() \
      ? cfsv2_##method(__VA_ARGS__)                                    \
      : hdfs_##method(__VA_ARGS__)

#define DATA_OP(method, ...)                                           \
  FileSystemType::CFS == BdsConfig::GetInstance()->GetFileSystemType() \
      ? cfsv2_raw_##method(__VA_ARGS__)                                \
      : hdfs_##method(__VA_ARGS__)

#endif

int bds_getattr(const char* path, struct stat* stat_buf,
                struct fuse_file_info* fi) {
  return META_OP(getattr, path, stat_buf, fi);
}

int bds_readlink(const char* path, char* link, size_t size) {
  return META_OP(readlink, path, link, size);
}

int bds_mknod(const char* path, mode_t mode, dev_t dev) {
  return META_OP(mknod, path, mode, dev);
}

int bds_mkdir(const char* path, mode_t mode) {
  return META_OP(mkdir, path, mode);
}

int bds_unlink(const char* path) {
  return META_OP(unlink, path);
}

int bds_rmdir(const char* path) {
  return META_OP(rmdir, path);
}

int bds_symlink(const char* path, const char* link) {
  return META_OP(symlink, path, link);
}

int bds_rename(const char* path, const char* new_path, unsigned int flags) {
  return META_OP(rename, path, new_path, flags);
}

int bds_link(const char* path, const char* link) {
  return META_OP(link, path, link);
}

int bds_chmod(const char* path, mode_t mode, struct fuse_file_info* fi) {
  return META_OP(chmod, path, mode, fi);
}

int bds_chown(const char* path, uid_t uid, gid_t gid,
              struct fuse_file_info* fi) {
  return META_OP(chown, path, uid, gid, fi);
}

int bds_truncate(const char* path, off_t off, struct fuse_file_info* fi) {
  return META_OP(truncate, path, off, fi);
}

int bds_open(const char* path, struct fuse_file_info* fi) {
  return META_OP(open, path, fi);
}

/*
int bds_write_buf(const char* path, struct fuse_bufvec* buf, off_t off,
                  struct fuse_file_info* fi) {
  return -1;
}
*/

int bds_statfs(const char* path, struct statvfs* stat_buf) {
  return META_OP(statfs, path, stat_buf);
}

int bds_flush(const char* path, struct fuse_file_info* fi) {
  return META_OP(flush, path, fi);
}

int bds_release(const char* path, struct fuse_file_info* fi) {
  return META_OP(release, path, fi);
}

int bds_fsync(const char* path, int datasync, struct fuse_file_info* fi) {
  return META_OP(fsync, path, datasync, fi);
}

int bds_setxattr(const char* path, const char* name, const char* value,
                 size_t size, int flags) {
  return META_OP(setxattr, path, name, value, size, flags);
}

int bds_getxattr(const char* path, const char* name, char* value, size_t size) {
  return META_OP(getxattr, path, name, value, size);
}

int bds_listxattr(const char* path, char* list, size_t size) {
  return META_OP(listxattr, path, list, size);
}

int bds_removexattr(const char* path, const char* name) {
  return META_OP(removexattr, path, name);
}

int bds_opendir(const char* path, struct fuse_file_info* fi) {
  return META_OP(opendir, path, fi);
}

int bds_readdir(const char* path, void* buf, fuse_fill_dir_t filler, off_t off,
                struct fuse_file_info* fi,
                enum fuse_readdir_flags readdir_flags) {
  return META_OP(readdir, path, buf, filler, off, fi, readdir_flags);
}

int bds_releasedir(const char* path, struct fuse_file_info* fi) {
  return META_OP(releasedir, path, fi);
}

int bds_fsyncdir(const char* path, int datasync, struct fuse_file_info* fi) {
  return META_OP(fsyncdir, path, datasync, fi);
}

void* bds_init(struct fuse_conn_info* conn, struct fuse_config* cfg) {
  (void)cfg;
  std::cout << "bds_init" << std::endl;
  if (!BdsConfig::GetInstance()->Init()) {
    std::cerr << "Failed to init bds config" << std::endl;
    return NULL;
  }
  return META_OP(init, conn, cfg);
}

void bds_destroy(void* userdata) {
  return META_OP(destroy, userdata);
}

int bds_access(const char* path, int mask) {
  return META_OP(access, path, mask);
}

int bds_create(const char* path, mode_t mode, struct fuse_file_info* fi) {
  return META_OP(create, path, mode, fi);
}

int bds_utimens(const char* path, const struct timespec tv[2],
                struct fuse_file_info* fi) {
  return META_OP(utimens, path, tv, fi);
}

int bds_bmap(const char* path, size_t blocksize, uint64_t* idx) {
  return META_OP(bmap, path, blocksize, idx);
}

int bds_ioctl(const char* path, unsigned int cmd, void* arg,
              struct fuse_file_info* fi, unsigned int flags, void* data) {
  return META_OP(ioctl, path, cmd, arg, fi, flags, data);
}

int bds_poll(const char* path, struct fuse_file_info* fi,
             struct fuse_pollhandle* ph, unsigned* reventsp) {
  return META_OP(poll, path, fi, ph, reventsp);
}

int bds_fallocate(const char* path, int mode, off_t off, off_t len,
                  struct fuse_file_info* fi) {
  return META_OP(fallocate, path, mode, off, len, fi);
}

ssize_t bds_copy_file_range(const char* path_in, struct fuse_file_info* fi_in,
                            off_t offset_in, const char* path_out,
                            struct fuse_file_info* fi_out, off_t offset_out,
                            size_t size, int flags) {
  return META_OP(copy_file_range, path_in, fi_in, offset_in, path_out, fi_out,
                 offset_out, size, flags);
}

off_t bds_lseek(const char* path, off_t off, int whence,
                struct fuse_file_info* fuse_file_info) {
  return META_OP(lseek, path, off, whence, fuse_file_info);
}

int bds_read(const char* path, char* buf, size_t size, off_t off,
             struct fuse_file_info* fi) {
  return META_OP(read, path, buf, size, off, fi);
}

int bds_write(const char* path, const char* buf, size_t size, off_t off,
              struct fuse_file_info* fi) {
  return META_OP(write, path, buf, size, off, fi);
}
