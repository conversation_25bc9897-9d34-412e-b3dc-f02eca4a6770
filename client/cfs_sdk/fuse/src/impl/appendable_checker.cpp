#include "impl/appendable_checker.h"

#include "common/logger.h"

namespace cfs {
namespace fuse {

AppendableChecker* AppendableChecker::GetInstance() {
  static AppendableChecker instance;
  return &instance;
}

void AppendableChecker::Init(const std::string& pattern) {
  CFSLOG(INFO, "Init AppendableChecker with regex pattern=`{}`", pattern);
  if (pattern.empty()) {
    return;
  }
  try {
    matcher_.assign(pattern);
  } catch (const std::regex_error& e) {
    CFSLOG(ERROR, "Fail to parse appendable regex: `{}`, err={}", pattern,
           e.what());
  }
}

bool AppendableChecker::Match(const std::string& fname) {
  return std::regex_match(fname, matcher_);
}

}  // namespace fuse
}  // namespace cfs