#include "impl/cfs_fuse.h"

int main(int argc, char* argv[]) {
  struct fuse_operations cfs_op = {
      .getattr = cfsv2_getattr,
      .readlink = cfsv2_readlink,
      .mknod = cfsv2_mknod,
      .mkdir = cfsv2_mkdir,
      .unlink = cfsv2_unlink,
      .rmdir = cfsv2_rmdir,
      .symlink = cfsv2_symlink,
      .rename = cfsv2_rename,
      .link = cfsv2_link,
      .chmod = cfsv2_chmod,
      .chown = cfsv2_chown,
      .truncate = cfsv2_truncate,
      .open = cfsv2_open,
      .read = cfsv2_read,
      .write = cfsv2_write,
      .statfs = cfsv2_statfs,
      .flush = cfsv2_flush,
      .release = cfsv2_release,
      .fsync = cfsv2_fsync,
      .setxattr = cfsv2_setxattr,
      .getxattr = cfsv2_getxattr,
      .listxattr = cfsv2_listxattr,
      .removexattr = cfsv2_removexattr,
      .opendir = cfsv2_opendir,
      .readdir = cfsv2_readdir,
      .releasedir = cfsv2_releasedir,
      .fsyncdir = cfsv2_fsyncdir,
      .init = cfsv2_init,
      .destroy = cfsv2_destroy,
      .access = cfsv2_access,
      .create = cfsv2_create,
      .lock = nullptr,  // fall back to system lock
      .utimens = cfsv2_utimens,
      .bmap = cfsv2_bmap,
      .ioctl = cfsv2_ioctl,
      .poll = cfsv2_poll,
      .write_buf = nullptr,  // fallback to write
      .read_buf = nullptr,   // fallback to read
      .flock = nullptr,      // fall back to system flock
      .fallocate = cfsv2_fallocate,
      .copy_file_range = cfsv2_copy_file_range,
      .lseek = cfsv2_lseek};
  return fuse_main(argc, argv, &cfs_op, nullptr);
}