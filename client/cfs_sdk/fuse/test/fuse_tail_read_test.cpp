#include <fcntl.h>
#include <gflags/gflags.h>
#include <gtest/gtest.h>
#include <unistd.h>

#include <filesystem>
#include <thread>

DEFINE_string(parent_dir, "/parent/dir", "must exist");

class FuseTailReadTest : public testing::Test {
 public:
  static void SetUpTestSuite() {
    std::filesystem::path parent(FLAGS_parent_dir);
    auto s = std::filesystem::status(parent);
    ASSERT_TRUE(std::filesystem::is_directory(s));
    test_dir_ = parent / "fuse_tail_read_test";
    // Make sure test_dir not eixsts
    ASSERT_FALSE(std::filesystem::exists(test_dir_));
    file_path_ = test_dir_ / "tail_read_test_file";
  }

  static void TearDownTestSuite() {}

  static bool CheckContent(const char* data, uint64_t len, uint64_t off) {
    if (len == 0) {
      return true;
    }
    uint64_t finish_len = 0;
    uint64_t start_off = off % kFileContent.size();
    while (finish_len < len) {
      uint64_t cmp_len = std::min(len - finish_len, kFileContent.size());
      if (strncmp(data + finish_len, &kCmpContent[start_off], cmp_len) != 0) {
        return false;
      }
      finish_len += cmp_len;
    }
    return true;
  }

 protected:
  void SetUp() override {
    ASSERT_TRUE(std::filesystem::create_directory(test_dir_));
  }

  void TearDown() override {
    ASSERT_GT(std::filesystem::remove_all(test_dir_), 0);
  }

 public:
  static constexpr std::string_view kFileContent = "0123456789";
  // kCmpContent must be twice of kFileContent
  static constexpr std::string_view kCmpContent = "01234567890123456789";

  static std::filesystem::path test_dir_;
  static std::filesystem::path file_path_;
};

std::filesystem::path FuseTailReadTest::test_dir_;
std::filesystem::path FuseTailReadTest::file_path_;

TEST_F(FuseTailReadTest, 1Thread1Fd) {
  int fd1 = open(file_path_.c_str(), O_RDWR | O_CREAT | O_DIRECT, 0644);
  ASSERT_GT(fd1, 0);

  uint64_t wbuf_len = kFileContent.size() * 1024 * 10;
  std::string wbuf;
  wbuf.reserve(wbuf_len);
  for (uint64_t i = 0; i < 1024 * 10; i++) {
    wbuf.append(kFileContent);
  }
  ASSERT_EQ(wbuf.size(), wbuf_len);

  uint64_t rbuf_len = 1024 * 128;
  std::string rbuf;
  rbuf.resize(rbuf_len);

  // read empty file should return EOF
  int64_t read_res = pread(fd1, rbuf.data(), rbuf_len, 0);
  EXPECT_EQ(read_res, 0);

  int64_t write_res = write(fd1, wbuf.data(), wbuf_len);
  ASSERT_EQ(write_res, static_cast<int64_t>(wbuf_len));
  // FUSE will not update file mtime unless fsync is called. And if mtime is not
  // changed, 'pread' will be cached by kernal and no cfsv2_read is issued.
  ASSERT_EQ(fsync(fd1), 0);

  uint64_t finish_len = 0;
  uint32_t sleep_sec = 0;
  while (finish_len < wbuf_len) {
    // FUSE attr_timeout is 1s at default
    if (sleep_sec > 3) {
      EXPECT_TRUE(false);
      break;
    }
    read_res = pread(fd1, rbuf.data(), rbuf_len, finish_len);
    EXPECT_TRUE(CheckContent(rbuf.data(), read_res, finish_len));
    finish_len += read_res;
    ASSERT_LE(finish_len, wbuf_len);
    if (finish_len < wbuf_len) {
      std::this_thread::sleep_for(std::chrono::milliseconds(1000));
      sleep_sec++;
    }
  }

  ASSERT_EQ(close(fd1), 0);
}

TEST_F(FuseTailReadTest, 1Thread2Fd) {
  int fd1 = open(file_path_.c_str(), O_RDWR | O_CREAT, 0644);
  ASSERT_GT(fd1, 0);
  int fd2 = open(file_path_.c_str(), O_RDONLY);
  ASSERT_GT(fd2, 0);

  uint64_t wbuf_len = kFileContent.size() * 1024 * 10;
  std::string wbuf;
  wbuf.reserve(wbuf_len);
  for (uint64_t i = 0; i < 1024 * 10; i++) {
    wbuf.append(kFileContent);
  }
  ASSERT_EQ(wbuf.size(), wbuf_len);

  uint64_t rbuf_len = 1024 * 128;
  std::string rbuf;
  rbuf.resize(rbuf_len);

  // read empty file should return EOF
  int64_t read_res = pread(fd2, rbuf.data(), rbuf_len, 0);
  EXPECT_EQ(read_res, 0);

  int64_t write_res = write(fd1, wbuf.data(), wbuf_len);
  ASSERT_EQ(write_res, static_cast<int64_t>(wbuf_len));
  ASSERT_EQ(fsync(fd1), 0);

  uint64_t finish_len = 0;
  uint32_t sleep_sec = 0;
  while (finish_len < wbuf_len) {
    // FUSE attr_timeout is 1s at default
    if (sleep_sec > 3) {
      EXPECT_TRUE(false);
      break;
    }
    read_res = pread(fd2, rbuf.data(), rbuf_len, 0);
    EXPECT_TRUE(CheckContent(rbuf.data(), read_res, finish_len));
    finish_len += read_res;
    ASSERT_LE(finish_len, wbuf_len);
    if (finish_len < wbuf_len) {
      std::this_thread::sleep_for(std::chrono::milliseconds(1000));
      sleep_sec++;
    }
  }

  ASSERT_EQ(close(fd1), 0);
  ASSERT_EQ(close(fd2), 0);
}

int main(int argc, char** argv) {
  // InitGoogleTest must be put before ParseCommandLineFlags because
  // 'ParseCommandLineFlags' does not known '--gtest_filter' and will report
  // error
  testing::InitGoogleTest(&argc, argv);
  gflags::ParseCommandLineFlags(&argc, &argv, true);
  return RUN_ALL_TESTS();
}
