#include <fcntl.h>
#include <fmt/format.h>
#include <gflags/gflags.h>
#include <gtest/gtest.h>
#include <unistd.h>

#include <filesystem>
#include <thread>
#include <vector>

DEFINE_string(parent_dir, "/parent/dir", "must exist");

struct WriteParam {
  uint64_t buf_len;
  uint64_t file_len;
};

class FuseWriteTest : public testing::TestWithParam<WriteParam> {
 public:
  static void SetUpTestSuite() {
    std::filesystem::path parent(FLAGS_parent_dir);
    auto s = std::filesystem::status(parent);
    ASSERT_TRUE(std::filesystem::is_directory(s));
    test_dir_ = parent / "fuse_write_test";
    // Make sure test_dir not eixsts
    ASSERT_FALSE(std::filesystem::exists(test_dir_));
  }

  static void TearDownTestSuite() {}

 protected:
  void SetUp() override {
    ASSERT_TRUE(std::filesystem::create_directory(test_dir_));
  }

  void TearDown() override {
    ASSERT_GT(std::filesystem::remove_all(test_dir_), 0);
  }

 public:
  static std::filesystem::path test_dir_;
};

std::filesystem::path FuseWriteTest::test_dir_;

TEST_F(FuseWriteTest, TruncOrAppend) {
  std::string buf;
  buf.resize(1000, '1');
  buf.resize(2000, '2');
  {
    auto fpath = test_dir_ / "flag_test_trunc";
    // 1. No O_TRUNC and O_APPEND. For new-create files, default to O_TRUNC
    int fd = open(fpath.c_str(), O_WRONLY | O_CREAT, 0644);
    ASSERT_GT(fd, 0);
    ssize_t res = write(fd, buf.data(), buf.size());
    EXPECT_EQ(res, buf.size());
    EXPECT_EQ(close(fd), 0);
    EXPECT_EQ(std::filesystem::file_size(fpath), buf.size());

    // 2. O_TRUNC to overwrite the old file
    fd = open(fpath.c_str(), O_WRONLY | O_TRUNC, 0644);
    ASSERT_GT(fd, 0);
    res = write(fd, buf.data(), buf.size() - 10);
    EXPECT_EQ(res, buf.size() - 10);
    EXPECT_EQ(close(fd), 0);
    EXPECT_EQ(std::filesystem::file_size(fpath), buf.size() - 10);
  }
  {
    auto fpath1 = test_dir_ / "flag_test_append";
    // 3. O_APPEND creates appendable object (only used in ACC mode)
    int fd1 = open(fpath1.c_str(), O_WRONLY | O_APPEND | O_CREAT, 0644);
    ASSERT_GT(fd1, 0);
    ssize_t res1 = write(fd1, buf.data(), buf.size());
    EXPECT_EQ(res1, buf.size());
    EXPECT_EQ(close(fd1), 0);
    EXPECT_EQ(std::filesystem::file_size(fpath1), buf.size());

    // 4. O_APPEND extend the file length
    fd1 = open(fpath1.c_str(), O_WRONLY | O_APPEND | O_CREAT, 0644);
    ASSERT_GT(fd1, 0);
    res1 = write(fd1, buf.data(), buf.size());
    EXPECT_EQ(res1, buf.size());
    EXPECT_EQ(close(fd1), 0);
    EXPECT_EQ(std::filesystem::file_size(fpath1), buf.size() * 2);
  }
}

TEST_F(FuseWriteTest, LeaseTest) {
  std::string buf;
  buf.resize(1000, '1');

  auto fpath = test_dir_ / "lease_test";
  int fd1 = open(fpath.c_str(), O_WRONLY | O_TRUNC | O_CREAT, 0644);
  ASSERT_GT(fd1, 0);
  ssize_t res = write(fd1, buf.data(), buf.size());
  EXPECT_EQ(res, buf.size());

  // Open fd2 must fail because the lease is already acquired by fd1.
  int fd2 = open(fpath.c_str(), O_WRONLY | O_APPEND | O_CREAT, 0644);
  ASSERT_EQ(fd2, -1);
  EXPECT_EQ(errno, EIO);

  // fd2 open success after fd1 is closed.
  EXPECT_EQ(close(fd1), 0);
  fd2 = open(fpath.c_str(), O_WRONLY | O_APPEND | O_CREAT, 0644);
  ASSERT_GT(fd2, 0);
  res = write(fd2, buf.data(), buf.size() - 1);
  EXPECT_EQ(res, buf.size() - 1);
  EXPECT_EQ(close(fd2), 0);
  EXPECT_EQ(std::filesystem::file_size(fpath), buf.size() * 2 - 1);
}

TEST_P(FuseWriteTest, 1ThreadWrite) {
  auto arg = GetParam();
  std::string fname = fmt::format("{}Buf{}File", arg.buf_len, arg.file_len);
  std::string buf;
  buf.resize(arg.buf_len, '1');
  auto fpath = test_dir_ / fname;
  int fd = open(fpath.c_str(), O_WRONLY | O_CREAT | O_TRUNC, 0644);
  ASSERT_GT(fd, 0);
  uint64_t cur_len = 0;
  while (cur_len < arg.file_len) {
    ssize_t res = write(fd, buf.data(), buf.size());
    ASSERT_EQ(res, buf.size());
    cur_len += res;
  }
  ASSERT_EQ(close(fd), 0);
  ASSERT_EQ(std::filesystem::file_size(fpath), arg.file_len);
}

TEST_P(FuseWriteTest, 20ThreadWrite) {
  auto arg = GetParam();
  std::string buf;
  buf.resize(arg.buf_len, '1');
  std::vector<std::thread> thds;
  for (uint32_t thid = 0; thid < 20; ++thid) {
    std::string fname =
        fmt::format("{}Buf{}File_Th{}", arg.buf_len, arg.file_len, thid);
    auto fpath = test_dir_ / fname;
    auto flen = arg.file_len;
    thds.push_back(std::thread([fpath, flen, &buf]() {
      int fd = open(fpath.c_str(), O_WRONLY | O_CREAT | O_TRUNC, 0644);
      ASSERT_GT(fd, 0);
      uint64_t cur_len = 0;
      while (cur_len < flen) {
        ssize_t res = write(fd, buf.data(), buf.size());
        ASSERT_EQ(res, buf.size());
        cur_len += res;
      }
      ASSERT_EQ(close(fd), 0);
      ASSERT_EQ(std::filesystem::file_size(fpath), flen);
    }));
  }
  for (auto& th : thds) {
    th.join();
  }
}

static std::string Size2Str(uint64_t bytes) {
  constexpr uint64_t glimit = 1024 * 1024 * 1024;
  constexpr uint64_t mlimit = 1024 * 1024;
  constexpr uint64_t klimit = 1024;
  if (bytes >= glimit) {
    return std::to_string(bytes / glimit) + "G";
  } else if (bytes >= mlimit) {
    return std::to_string(bytes / mlimit) + "M";
  } else if (bytes >= klimit) {
    return std::to_string(bytes / klimit) + "K";
  } else {
    return std::to_string(bytes);
  }
}

constexpr WriteParam kParamList[] = {{16, 16 * 64},
                                     {1024, 1024 * 64},
                                     {1024 * 512, 1024 * 512 * 32},
                                     {1024 * 1024 * 2, 1024 * 1024 * 256}};

INSTANTIATE_TEST_SUITE_P(
    MultiBufLenFileLen, FuseWriteTest, testing::ValuesIn(kParamList),
    [](const testing::TestParamInfo<FuseWriteTest::ParamType>& info) {
      return fmt::format("{}Buf{}File", Size2Str(info.param.buf_len),
                         Size2Str(info.param.file_len));
    });

int main(int argc, char** argv) {
  // InitGoogleTest must be put before ParseCommandLineFlags because
  // 'ParseCommandLineFlags' does not known '--gtest_filter' and will report
  // error
  testing::InitGoogleTest(&argc, argv);
  gflags::ParseCommandLineFlags(&argc, &argv, true);
  return RUN_ALL_TESTS();
}
