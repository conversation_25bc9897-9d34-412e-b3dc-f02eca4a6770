#pragma once

#include <stdint.h>
#include <sys/types.h>
#include <sys/uio.h>  // for struct iovec
#include <time.h>

#include "cloudfs2/base.h"

#ifdef __cplusplus
extern "C" {
#endif

struct cfs_file;
typedef struct cfs_file* cfs_file_handle;
struct cfs_iobuf;  // Hide the implementation details of byterpc::IO<PERSON><PERSON>
typedef struct cfs_iobuf cfs_iobuf_t;

enum cfs_file_type {
  CFS_FILE_UNKNOWN = 0,
  CFS_FILE_DIR = 1,
  CFS_FILE_REGULAR = 2,
  CFS_FILE_SYMLINK = 3
};

enum cfs_off_type { CFS_READ_OFF = 0, CFS_WRITE_OFF = 1 };

// Bit masks for `flags` in `cfs_open_option_t`.
// The flag must only include one of 'CFS_O_RDONLY', 'CFS_O_WRONLY' and
// 'CFS_O_RDWR'.
// The flag must only include one of 'CFS_O_APPEND' and 'CFS_O_TRUNC' in write
// mode.
// 'CFS_O_CREATE':
//     The file will be created if it not exists.
// 'CFS_O_APPEND':
//     The file is opened in append mode. This flag also implies
//     'CFS_O_ACC_APPENDABLE' (i.e. if this flag is set, then
//     'CFS_O_ACC_APPENDABLE' is automatically set).
// 'CFS_O_TRUNC':
//     Only used when writing file. If the file already exists, overwrite it.
// 'CFS_O_SYNC_WRITE':
//     Write data to Datanode disk directly (Not in page cache).
// 'CFS_O_ACC_APPENDABLE':
//     Only used in ACC mode. When this flag is set, the file will be an
//     "appendable-object" on TOS. If 'CFS_O_APPEND' is set, this flag will
//     be set automatically.
// 'CFS_O_ACC_WRITE_THROUGH':
//     Only used in ACC mode. When this flag is set, CFS will wait all data
//     uploading to TOS when closing file. Otherwise, CFS will close file
//     immediately and let the upload-TOS-task run asynchronously.
// 'CFS_O_ACC_WRITE_BACK':
//     Only used in ACC mode. When this flag is set, CFS will mark this file
//     as need-to-WRITE_BACK no matter its parent is WRITE_BACK or not. Note
//     that this flag only applies for new-created file or overwrite-file(i.e.
//     append-to-file will ignore this flag).
#define CFS_O_RDONLY 0x1
#define CFS_O_WRONLY 0x2
#define CFS_O_RDWR 0x4
#define CFS_O_CREATE 0x8
#define CFS_O_APPEND 0x10
#define CFS_O_TRUNC 0x20
#define CFS_O_SYNC_WRITE 0x40
#define CFS_O_ACC_APPENDABLE 0x100
#define CFS_O_ACC_WRITE_THROUGH 0x200
#define CFS_O_ACC_WRITE_BACK 0x400
#define CFS_O_ACC_NO_WRITE_BACK 0x800

typedef struct {
  int32_t flags;  // bitwise-or'd of CFS_O_xxx above
  mode_t mode;    // same as `mode` in `open` syscall
  // Whether to create parent directory when CFS_O_CREATE is set in flags and
  // parent director is missing.
  bool create_parent;
  // The replica number to create this file. If set to 0, the value of config
  // "cfs_write_replication" will be used.
  uint32_t replication;
  // If set to true, force-open the file even if it is being written by another
  // client. After this, the other client will fail to write data because it
  // has lost the file lease.
  bool force;
} cfs_open_option_t;

typedef struct {
  const char** paths;
  const uint32_t* part_nums;
  // if 'wb_policy' is nullptr, it acts as CFS_WB_UNSPEC
  const cfs_write_back_policy* wb_policy;
  uint32_t num;
  mode_t mode;  // same as `mode` in `open` syscall
  // The replica number to create this file. If set to 0, the value of config
  // "cfs_write_replication" will be used.
  uint32_t replication;
} cfs_batch_create_option_t;

// The enum value for AccFileStatus corresponds with the value of
// HdfsFileStatusProto::AccFileStatus.
enum cfs_acc_status {
  CFS_ACC_UNKNOWN = 0,
  CFS_ACC_FILE_LOCAL = 1,
  CFS_ACC_FILE_TO_BE_PERSISTED = 2,
  CFS_ACC_FILE_PERSISTED = 3,
  CFS_ACC_DIR_LOCAL = 4,
  CFS_ACC_DIR_INCOMPLETE = 5,
  CFS_ACC_DIR_SYNCED = 6
};

typedef struct {
  // TODO(dbc) file type and perm can store in one uint32, like linux syscall
  cfs_file_type type;
  uint64_t size;
  // The unit of time is millisecond
  time_t atime;
  time_t mtime;
  // ctime is not available in DanceNN now
  // time_t ctime;
  mode_t perm;
  uint32_t replication;
  char* name;
  char* owner;
  char* group;
  uint64_t file_id;
  cfs_acc_status acc_stat;
} cfs_file_info_t;

typedef struct {
  uint64_t length;
  uint64_t file_count;
  uint64_t directory_count;
  uint64_t quota;
  uint64_t space_consumed;
  uint64_t space_quota;
} cfs_content_summary_t;

typedef struct {
  uint64_t capacity;
  uint64_t used;
  uint64_t remaining;
  uint64_t inode_capacity;
  uint64_t inode_used;
  uint64_t inode_remaining;
  // wait_upload contains no_upload and uploading
  uint64_t inode_wait_upload;
  uint64_t inode_no_upload;
  uint64_t inode_uploading;
} cfs_fs_stats_t;

typedef void (*cfs_async_read_cb)(cfs_status status, int64_t size_read,
                                  void* args);

typedef void (*cfs_async_write_cb)(cfs_status status, int64_t size_write,
                                   void* args);

typedef void (*cfs_async_sync_cb)(cfs_status status, void* args);

cfs_file_handle cfs_open(cfs_fs_handle fs, const char* path,
                         const cfs_open_option_t* opts);

// No matter if this method returns success or not, `file` pointer is invalid
// after calling this method.
int32_t cfs_close(cfs_file_handle file);

// Batch open multiple files.
// For example:
//   `paths` = ["/a/b/c1", "/a/b/c2", "/a/b/c3"]
//   `part_nums` = [1,3,1]
//   `num` = 3
// The return values will be:
//   [[fd for "/a/b/c1"],
//   [fd for "/a/b/c2", fd for "/a/b/c2.part1", fd for "/a/b/c2.part2"],
//   [fd for "/a/b/c3"]]
//
// Then the caller can use the returned 5 fds to write data and finally call
// cfs_batch_close
cfs_file_handle** cfs_batch_create(cfs_fs_handle fs,
                                   const cfs_batch_create_option_t* opts);

// Batch close multiple files and optionally concat some files into one.
// For example:
//   `files` = [[fd for "/a/b/c1"],
//          [fd for "/a/b/c2", fd for "/a/b/c2.part1", fd for "/a/b/c2.part2"],
//          [fd for "/a/b/c3"]]
//    `part_nums` = [1,3,1]
//    `num` = 3
// Then "/a/b/c1" and "/a/b/c3" will be closed as before. "/a/b/c2",
// "/a/b/c2.part1" and "/a/b/c2.part2" will be closed and concat into
// one file named "/a/b/c2" ("/a/b/c2.part1" is appended after "/a/b/c2",
// "/a/b/c2.part2" is appended after "/a/b/c2.part1")
int32_t cfs_batch_close(cfs_file_handle** files, const uint32_t* part_nums,
                        uint32_t num);

int64_t cfs_write(cfs_file_handle file, const void* data, uint64_t size);

int32_t cfs_async_write(cfs_file_handle file, const void* data, uint64_t size,
                        cfs_async_write_cb callback, void* args);

int64_t cfs_read(cfs_file_handle file, void* buffer, uint64_t size);

int64_t cfs_pread(cfs_file_handle file, void* buffer, uint64_t size,
                  int64_t offset);

int32_t cfs_async_pread(cfs_file_handle file, void* buffer, uint64_t size,
                        int64_t offset, cfs_async_read_cb callback, void* args);

// Return 'buf' on success, return nullptr on error.
char* cfs_get_working_directory(cfs_fs_handle fs, char* buf, uint64_t buf_size);

int32_t cfs_set_working_directory(cfs_fs_handle fs, const char* buf);

// When ACC backend is TOS, `create_parent` is always 'true' no matter what
// value user passed to this method.
int32_t cfs_mkdir(cfs_fs_handle fs, const char* path, mode_t mode,
                  bool create_parent);

// The parameter `whence` is the same as in linux syscall `lseek`. Supported
// values are:
// 1. SEEK_SET: The file offset is set to offset bytes.
// 2. SEEK_CUR: The file offset is set to its current location plus offset
// bytes.
// 3. SEEK_END: The file offset is set to the size of the file plus offset
// bytes.
//
// `SEEK_DATA` and `SEEK_HOLE` are not supported.
int64_t cfs_seek(cfs_file_handle file, int64_t offset, int32_t whence);

int64_t cfs_tell(cfs_file_handle file, cfs_off_type type);

// Sync both data and meta
int32_t cfs_sync(cfs_file_handle file);

int32_t cfs_async_sync(cfs_file_handle file, cfs_async_sync_cb callback,
                       void* args);

int32_t cfs_delete(cfs_fs_handle fs, const char* path, bool recursive);

// If force is true, ignore un-exist file. Otherwise report error if any file
// not exist
int32_t cfs_batch_delete(cfs_fs_handle fs, const char** paths, uint32_t num,
                         bool force);

int32_t cfs_rename(cfs_fs_handle fs, const char* old_path, const char* new_path,
                   bool overwrite);

int32_t cfs_utime(cfs_fs_handle fs, const char* path, int64_t atime_ms,
                  int64_t mtime_ms);

int32_t cfs_concat(cfs_fs_handle fs, const char** srcs, const char* dst,
                   uint32_t num_srcs);

// Return 1 if file exists.
// Return 0 if file not exists.
// Return -1 if error happens.
int32_t cfs_exist(cfs_fs_handle fs, const char* path);

cfs_file_info_t* cfs_get_file_info(cfs_fs_handle fs, const char* path);
cfs_file_info_t* cfs_get_file_info2(cfs_file_handle file);

// If (has_remaining == nullptr), this method will list all entries under
// 'path'.
// If (has_remaining != nullptr), this method will return at most
// FLASG_cfs_list_dir_page_size entries.
//
// No matter whether 'has_remaining' is null or not, start_after' can be set
// to the file_name under 'path' in order to list only childres after this
// file_name. Note that 'start_after' should be 'file_name' but not 'full_path'.
// If start_after is nullptr, this method will list from begining.
//
// If error occured, return nullptr and set 'num_entries' to -1.
// If this path is an empty dir, return nullptr and set num_entries' to 0.
//
// NOTE: In ACC mode, if file num on TOS exceeds FLAGS_cfs_max_list_dir_num_ufs,
// this method will:
// 1. return null and set error msg if FLAGS_cfs_filesystem_sync_interval_sec
//    >= 0.
// 2. return partial-listed results if FLAGS_cfs_filesystem_sync_interval_sec
//    <= -1. If user then call cfs_list_dir again with 'start_after' set to
//    the last file_name got from last response, the 2nd call will return
//    nullptr.
cfs_file_info_t* cfs_list_dir(cfs_fs_handle fs, const char* path,
                              int32_t* num_entries, const char* start_after,
                              bool* has_remaining);

int32_t cfs_free_file_info(cfs_fs_handle fs, cfs_file_info_t* info,
                           int32_t num_entries);

cfs_content_summary_t cfs_get_content_summary(cfs_fs_handle fs,
                                              const char* path);

int32_t cfs_statfs(cfs_fs_handle fs, cfs_fs_stats_t* buf);

// If policy is 'CFS_WB_UNSPEC', do nothing.
int32_t cfs_set_writeback_policy(cfs_fs_handle fs, const char* path,
                                 cfs_write_back_policy policy);

// the return policy is set to `policy`
int32_t cfs_get_writeback_policy(cfs_fs_handle fs, const char* path,
                                 cfs_write_back_policy* policy);

int32_t cfs_rm_writeback_policy(cfs_fs_handle fs, const char* path);

// If policy is 'CFS_WB_UNSPEC', do nothing.
int32_t cfs_set_sync_policy(cfs_fs_handle fs, const char* path,
                            cfs_write_back_policy policy);

int32_t cfs_set_ttl(cfs_fs_handle fs, const char* path, int64_t seconds,
                    bool whole, bool atime_based);

// Get the TTL on the request path. If no TTL is set on this path, `seconds`
// will be set to -1 and return_val will be 0.
int32_t cfs_get_ttl(cfs_fs_handle fs, const char* path, int64_t* seconds,
                    bool* whole, bool* atime_based);

int32_t cfs_rm_ttl(cfs_fs_handle fs, const char* path);

/**
 * Copy data from the under storage system into Datanode
 * @param fs    The configured filesystem handle.
 * @param path the path which data will be copied
 * @param recursive recursively load in subdirectories
 * @param load_metadata load metadata from UFS to Namenode
 * @param load_data load data from UFS to Datanode
 * @param replica_num load replica number
 * @param dc_name data be loaded
 * @return Job ID
 */
char* cfs_load_cache(cfs_fs_handle fs, const char* path, bool recursive,
                     bool load_metadata, bool load_data, int replica_num);

/**
 * Delete data from Datanode
 * @param fs    The configured filesystem handle.
 * @param path the path which data will be deleted
 * @param recursive recursively free in subdirectories
 * @param free_metadata free metadata
 * @return Job status
 */
char* cfs_free_cache(cfs_fs_handle fs, const char* path, bool recursive,
                     bool free_metadata);

int32_t cfs_free_job_id(char* job_id);

/**
 * Lookup job status
 * @param fs    The configured filesystem handle.
 * @param job_id
 * @return Job status
 */
cfs_lookup_job_response* cfs_lookup_job(cfs_fs_handle fs, const char* job_id);

int32_t cfs_free_lookup_resp(cfs_lookup_job_response* resp);

/**
 * Cancel job
 * @param fs   The configured filesystem handle.
 * @param job_id
 */
int32_t cfs_cancel_job(cfs_fs_handle fs, const char* job_id);

// -------------------------------------------
// Functions below are used for zero-copy read/write.
// -------------------------------------------

// Creates a new IOBuf with size being `size`. Callers can obtain the interval
// raw buffer via `cfs_iobuf_get_iov` and then write data to it.
//
// If the `size` is larger than 4MB-128B, the returned iobuf will consist of
// multiple incontinuous address(in struct iov) and the caller should write data
// to them separately. If the `size` is smaller than 4MB-128B, we ensure that
// the return iobuf consist only one struct iov. See `cfs_iobuf_get_iov` for
// details.
//
// If `size` is 0, this function returns an empty IOBuf (usually used for
// reading data). If the callers want to write data to this empty IOBuf later,
// they can only append data to it via `cfs_iobuf_append`, which will bring one
// memcpy.
//
// Return NULL if it is failed to create IOBuf.
//
// The caller must call `cfs_free_iobuf` to free this iobuf when it is not used.
cfs_iobuf_t* cfs_create_iobuf(uint64_t size);

// Return 0 on success and -1 on failure.
int32_t cfs_free_iobuf(cfs_iobuf_t* iobuf);

// Copy `size` bytes of data from `data` to `iobuf`.
// Return the bytes copied on success, or -1 on failure.
int64_t cfs_iobuf_append(cfs_iobuf_t* iobuf, const void* data, uint64_t size);

// Return the total size of data in this iobuf.
int64_t cfs_iobuf_size(const cfs_iobuf_t* iobuf);

// Get the data pointer and length (in struct iovec) of the internel raw
// buffers. The parameter `num_entries` will be set to the total number of
// returned iovecs.
//
// The callers should call `cfs_iobuf_free_iov` to free the returned struct
// iovec* when they are not used.
struct iovec* cfs_iobuf_get_iov(cfs_iobuf_t* iobuf, int32_t* num_entries);

void cfs_iobuf_free_iov(struct iovec* iov, int32_t num_entries);

int64_t cfs_writex(cfs_file_handle file, const cfs_iobuf_t* data);

int32_t cfs_async_writex(cfs_file_handle file, const cfs_iobuf_t* data,
                         cfs_async_write_cb callback, void* args);

int64_t cfs_readx(cfs_file_handle file, cfs_iobuf_t* iobuf, uint64_t size);

int64_t cfs_preadx(cfs_file_handle file, cfs_iobuf_t* iobuf, uint64_t size,
                   int64_t offset);

int32_t cfs_async_preadx(cfs_file_handle file, cfs_iobuf_t* iobuf,
                         uint64_t size, int64_t offset,
                         cfs_async_read_cb callback, void* args);

#ifdef __cplusplus
}
#endif
