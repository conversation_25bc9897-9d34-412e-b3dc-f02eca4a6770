set(lib_name spdlog)
ExternalProject_Add(
    ${lib_name}
    URL https://github.com/gabime/spdlog/archive/refs/tags/v1.12.0.tar.gz
    URL_HASH MD5=6b4446526264c1d1276105482adc18d1
    DOWNLOAD_NAME spdlog-1.12.0.tar.gz
    PREFIX ${CMAKE_CURRENT_BINARY_DIR}/${lib_name}
    DOWNLOAD_DIR ${DOWNLOAD_DIR}
    BUILD_IN_SOURCE FALSE
    CMAKE_ARGS
      ${common_cmake_args}
      -DSPDLOG_FMT_EXTERNAL=ON
    BUILD_COMMAND make -s -j${NUM_JOBS_PER_LIB}
    INSTALL_COMMAND make -s install
    LOG_CONFIGURE TRUE
    LOG_BUILD TRUE
    LOG_INSTALL TRUE
)
