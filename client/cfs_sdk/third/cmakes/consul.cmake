set(lib_name consul)
ExternalProject_Add(
    ${lib_name}
    GIT_REPOSITORY ******************:cpputil/consul.git
    GIT_TAG 1.0.0_cmake
    GIT_SHALLOW TRUE
    PREFIX ${CMAKE_CURRENT_BINARY_DIR}/${lib_name}
    DOWNLOAD_DIR ${DOWNLOAD_DIR}
    BUILD_IN_SOURCE FALSE
    CMAKE_ARGS ${common_cmake_args}
    BUILD_COMMAND make -j${NUM_JOBS_PER_LIB}
    INSTALL_COMMAND make -s install
    LOG_CONFIGURE TRUE
    LOG_BUILD TRUE
    LOG_INSTALL TRUE
    LOG_OUTPUT_ON_FAILURE TRUE
)