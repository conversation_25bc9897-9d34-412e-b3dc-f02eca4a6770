set(lib_name function2)
ExternalProject_Add(
  ${lib_name}
  URL https://github.com/Naios/function2/archive/refs/tags/4.2.4.tar.gz
  URL_HASH MD5=9ddc716e959ecd441933beaeb2a8aaf5
  DOWNLOAD_NAME function2-4.2.4.tar.gz
  PREFIX ${CMAKE_CURRENT_BINARY_DIR}/${lib_name}
  DOWNLOAD_DIR ${DOWNLOAD_DIR}
  BUILD_IN_SOURCE FALSE
  CMAKE_ARGS
    ${common_cmake_args} -DBUILD_TESTING=OFF
  BUILD_COMMAND make -s -j${NUM_JOBS_PER_LIB}
  INSTALL_COMMAND make -s install
  LOG_CONFIGURE TRUE
  LOG_BUILD TRUE
  LOG_INSTALL TRUE
  LOG_OUTPUT_ON_FAILURE TRUE
  )
