#include <fmt/format.h>
#include <gflags/gflags.h>

#include <atomic>
#include <functional>
#include <random>
#include <thread>

#include "cloudfs2/cloudfs2.h"
#include "common/datetime_util.h"
#include "common/string_util.h"
#include "impl/cfs_impl.h"
#include "metric/metric_sdk/counter.h"
#include "metric/metric_sdk/summary.h"

DEFINE_uint32(jobs, 1, "Number of threads (one file per thread) to read");
DEFINE_string(bench_dir, "/cfs_bench_dir", "Dir for read files");
DEFINE_string(op, "R", "Supported ops: R,RX,AR,ARX");
DEFINE_uint64(buf_len, 8 * 1024 * 1024, "Buffer size for read");
DEFINE_uint64(file_len, 8LLU * 1024 * 1024 * 1024, "File size");
DEFINE_uint32(files_per_job, 1, "File num per job");
DEFINE_uint32(sub_dir_num, 1, "subdir num for all jobs");
DEFINE_uint32(read_round, 1, "");
DEFINE_int64(hist_sec, 5, "Interval for speed histgram");
DEFINE_bool(long_run, false, "Long run without exit");
DEFINE_bool(output_each_interval, true, "");
DEFINE_uint64(async_max_fly_bytes, 16 * 1024 * 1024, "");
DEFINE_bool(rand, false, "random read or seq read");

using DatetimeUtil = cfs::internal::DatetimeUtil;
using StringUtil = cfs::internal::StringUtil;
using Summary = cfs::internal::Summary;
using Counter = cfs::internal::Counter;

struct AsyncReadArg {
  uint64_t req_size;
  cfs_iobuf* iobuf;
  Summary* summary;
  Counter* io_bytes;
  int64_t start_us;
  std::atomic<uint64_t>* fly_req_bytes;
};

static void OnAsyncReadDone(cfs_status status, int64_t size_read, void* args) {
  auto* read_arg = static_cast<AsyncReadArg*>(args);
  if (read_arg->iobuf) {
    cfs_free_iobuf(read_arg->iobuf);
  }
  if (status != CFS_STATUS_OK) {
    fmt::print(stderr, "Fail to read file, status={}, err={}\n",
               static_cast<int32_t>(status), cfs_get_last_error()->message);
  } else {
    if (static_cast<uint64_t>(size_read) != read_arg->req_size) {
      fmt::print(stderr, "Write size mismatch, request={}, response={}\n",
                 read_arg->req_size, size_read);
    }
    read_arg->summary->Add(DatetimeUtil::GetNowTimeUs() - read_arg->start_us);
    read_arg->io_bytes->Increase(size_read);
  }
  read_arg->fly_req_bytes->fetch_sub(read_arg->req_size,
                                     std::memory_order_release);
  delete read_arg;
}

static void ReadFunc(uint32_t id, cfs_fs* fs, Summary* summary,
                     Counter* io_bytes) {
  uint32_t dir_id = id % FLAGS_sub_dir_num;
  std::string dir =
      StringUtil::StrFormat("{}/subdir_{}", FLAGS_bench_dir, dir_id);
  cfs_open_option_t open_opts;
  open_opts.flags = CFS_O_RDONLY;

  std::string rbuf;
  if (FLAGS_op == "R") {
    rbuf.resize(FLAGS_buf_len);
  }

  for (uint32_t j = 0; j < FLAGS_read_round; ++j) {
    for (uint32_t i = 0; i < FLAGS_files_per_job; ++i) {
      std::string path = StringUtil::StrFormat("{}/job_{}_file_{}", dir, id, i);
      cfs_file* file = cfs_open(fs, path.c_str(), &open_opts);
      if (file == nullptr) {
        fmt::print(stderr, "Fail to open file for read, path={}\n", path);
        return;
      }
      auto file_size = file->file_handle->GetFileLength();
      uint64_t total_read_len = std::min(FLAGS_file_len, file_size);

      int64_t rand_end = 0;
      if (FLAGS_buf_len < total_read_len) {
        rand_end = total_read_len - FLAGS_buf_len;
      }
      std::random_device rd;
      std::mt19937_64 gen(rd());
      std::uniform_int_distribution<int64_t> distrib(0, rand_end);

      uint64_t finish = 0;
      while (finish < total_read_len) {
        int64_t res = 0;
        int64_t start_us = DatetimeUtil::GetNowTimeUs();
        int64_t off = finish;
        if (FLAGS_rand) {
          off = distrib(gen);
        }
        auto req_len = std::min(FLAGS_buf_len, total_read_len - finish);
        if (FLAGS_op == "R") {
          res = cfs_pread(file, rbuf.data(), req_len, off);
        } else if (FLAGS_op == "RX") {
          cfs_iobuf* io_buf = cfs_create_iobuf(0);
          if (io_buf == nullptr) {
            fmt::print(stderr, "Fail to create iobuf, path={}\n", path);
            break;
          }
          res = cfs_preadx(file, io_buf, req_len, off);
          cfs_free_iobuf(io_buf);
        } else {
          std::abort();
        }

        if (res < 0) {
          fmt::print(stderr, "Fail to read, path={}, err={}\n", path,
                     cfs_get_last_error()->message);
          break;
        } else if (res == 0) {
          // EOF
          break;
        }
        summary->Add(DatetimeUtil::GetNowTimeUs() - start_us);
        io_bytes->Increase(res);
        finish += res;
      }
      if (cfs_close(file) != 0) {
        fmt::print(stderr, "Fail to close file, path={}\n", path);
      }
    }
  }  // end read_round
}

static void AsyncPReadFunc(uint32_t id, cfs_fs* fs, Summary* summary,
                           Counter* io_bytes) {
  uint32_t dir_id = id % FLAGS_sub_dir_num;
  std::string dir =
      StringUtil::StrFormat("{}/subdir_{}", FLAGS_bench_dir, dir_id);
  cfs_open_option_t open_opts;
  open_opts.flags = CFS_O_RDONLY;

  std::string rbuf;
  if (FLAGS_op == "AR") {
    rbuf.resize(FLAGS_buf_len);
  }

  std::atomic<uint64_t> fly_bytes{0};

  for (uint32_t j = 0; j < FLAGS_read_round; ++j) {
    for (uint32_t i = 0; i < FLAGS_files_per_job; ++i) {
      std::string path = StringUtil::StrFormat("{}/job_{}_file_{}", dir, id, i);
      cfs_file* file = cfs_open(fs, path.c_str(), &open_opts);
      if (file == nullptr) {
        fmt::print(stderr, "Fail to open file for read, path={}\n", path);
        return;
      }
      auto file_size = file->file_handle->GetFileLength();
      uint64_t total_read_len = std::min(FLAGS_file_len, file_size);

      int64_t rand_end = 0;
      if (FLAGS_buf_len < total_read_len) {
        rand_end = total_read_len - FLAGS_buf_len;
      }
      std::random_device rd;
      std::mt19937_64 gen(rd());
      std::uniform_int_distribution<int64_t> distrib(0, rand_end);

      uint64_t finish = 0;
      while (finish < total_read_len) {
        while (fly_bytes.load(std::memory_order_acquire) >=
               FLAGS_async_max_fly_bytes) {
          // busy wait
        }

        auto req_len = std::min(FLAGS_buf_len, total_read_len - finish);
        AsyncReadArg* arg = new AsyncReadArg();
        arg->req_size = req_len;
        arg->summary = summary;
        arg->io_bytes = io_bytes;
        arg->start_us = DatetimeUtil::GetNowTimeUs();
        arg->fly_req_bytes = &fly_bytes;

        int64_t off = finish;
        if (FLAGS_rand) {
          off = distrib(gen);
        }
        if (FLAGS_op == "AR") {
          arg->iobuf = nullptr;
          int32_t rc = cfs_async_pread(file, rbuf.data(), req_len, off,
                                       &OnAsyncReadDone, arg);
          if (rc < 0) {
            fmt::print(stderr, "Fail to async read file, path={}, err={}\n",
                       path, cfs_get_last_error()->message);
            delete arg;
            break;
          }
        } else if (FLAGS_op == "ARX") {
          arg->iobuf = cfs_create_iobuf(0);
          if (arg->iobuf == nullptr) {
            fmt::print(stderr, "Fail to create iobuf, path={}\n", path);
            break;
          }
          int32_t rc = cfs_async_preadx(file, arg->iobuf, req_len, off,
                                        &OnAsyncReadDone, arg);
          if (rc < 0) {
            fmt::print(stderr, "Fail to async readx, path={}, err={}\n", path,
                       cfs_get_last_error()->message);
            cfs_free_iobuf(arg->iobuf);
            delete arg;
            break;
          }
        }

        finish += req_len;
        fly_bytes.fetch_add(req_len, std::memory_order_release);
      }
      if (cfs_close(file) != 0) {
        fmt::print(stderr, "Fail to close file, path={}\n", path);
      }
    }
  }  // end read_round
}

static void MonitorFunc(std::atomic<uint32_t>* alive_workers, Summary* summary,
                        Counter* io_bytes) {
  std::vector<int64_t> speed_hist;  // MB/s
  std::vector<int64_t> lat_avg_hist;
  std::vector<int64_t> lat_p99_hist;
  std::vector<double> qps_hist;
  bool clear_output = false;

  int64_t last_bytes = 0;
  int64_t last_time_us = DatetimeUtil::GetNowTimeUs();

  while (alive_workers->load(std::memory_order_acquire) > 0) {
    std::this_thread::sleep_for(std::chrono::seconds(FLAGS_hist_sec));
    int64_t now = DatetimeUtil::GetNowTimeUs();
    int64_t dur_us = now - last_time_us;
    last_time_us = now;

    int64_t bytes_now = io_bytes->GetValue();
    int64_t bytes = bytes_now - last_bytes;
    last_bytes = bytes_now;
    int64_t thpt = bytes * 1000 / 1024 * 1000 / 1024 / dur_us;
    speed_hist.push_back(thpt);

    auto snap = summary->GetSnapshot();
    lat_avg_hist.push_back(snap.avg);
    lat_p99_hist.push_back(snap.p99);
    qps_hist.push_back(snap.qps);

    if (FLAGS_output_each_interval) {
      if (clear_output) {
        for (uint32_t i = 0; i < 5; i++) {
          // Move the cursor up one line
          std::cout << "\x1b[A";
          // Clear the entire line
          std::cout << "\x1b[2K";
        }
      }
      fmt::print("======= statictics({}) =======\n",
                 DatetimeUtil::GetNowTimeLocalStr("%H:%M:%S"));
      fmt::print("Latency-avg(us) hist:  {}\n", fmt::join(lat_avg_hist, ","));
      fmt::print("Latency-p99(us) hist:  {}\n", fmt::join(lat_p99_hist, ","));
      fmt::print("QPS hist:  {:.2f}\n", fmt::join(qps_hist, ","));
      fmt::print("Throughput(MB/s) hist:  {}\n", fmt::join(speed_hist, ","));
      clear_output = true;
    }
  }
  // print histgrams
  if (!FLAGS_output_each_interval) {
    fmt::print("======== statictics (interval={}s) ========\n", FLAGS_hist_sec);
    fmt::print("Throughput(MB/s) hist:\n  {}\n", fmt::join(speed_hist, ","));
    fmt::print("Latency-avg(us) hist:\n  {}\n", fmt::join(lat_avg_hist, ","));
    fmt::print("Latency-p99(us) hist:\n  {}\n", fmt::join(lat_p99_hist, ","));
    fmt::print("QPS hist:\n  {:.2f}\n", fmt::join(qps_hist, ","));
  }
}

int main(int argc, char* argv[]) {
  gflags::ParseCommandLineFlags(&argc, &argv, true);

  if (FLAGS_op != "R" && FLAGS_op != "RX" && FLAGS_op != "AR" &&
      FLAGS_op != "ARX") {
    fmt::print(stderr, "Unknow op: {}\n", FLAGS_op);
    return -1;
  }

  cfs_fs* fs = cfs_connect(nullptr);
  if (fs == nullptr) {
    fmt::print(stderr, "Fail to connect to fs");
    return -1;
  }

  do {
    std::vector<std::thread> workers;
    Summary summary(FLAGS_hist_sec);
    Counter io_bytes;
    std::atomic<uint32_t> alive_workers;
    alive_workers.store(FLAGS_jobs);

    std::thread monitor([&alive_workers, &summary, &io_bytes]() {
      MonitorFunc(&alive_workers, &summary, &io_bytes);
    });
    auto start_us = DatetimeUtil::GetNowTimeUs();
    for (uint32_t i = 0; i < FLAGS_jobs; i++) {
      if (FLAGS_op == "AR" || FLAGS_op == "ARX") {
        workers.push_back(
            std::thread([i, fs, &summary, &io_bytes, &alive_workers]() {
              AsyncPReadFunc(i, fs, &summary, &io_bytes);
              alive_workers.fetch_sub(1, std::memory_order_release);
            }));
      } else if (FLAGS_op == "R" || FLAGS_op == "RX") {
        workers.push_back(
            std::thread([i, fs, &summary, &io_bytes, &alive_workers]() {
              ReadFunc(i, fs, &summary, &io_bytes);
              alive_workers.fetch_sub(1, std::memory_order_release);
            }));
      } else {
        std::abort();
      }
    }

    for (uint32_t i = 0; i < FLAGS_jobs; i++) {
      workers[i].join();
    }
    auto duration_us = DatetimeUtil::GetNowTimeUs() - start_us;
    monitor.join();

    int64_t thput =
        io_bytes.GetValue() * 1000 / 1024 * 1000 / 1024 / duration_us;
    fmt::print("Elapsed time: {}us. Throughput: {} MB/s\n", duration_us, thput);
  } while (FLAGS_long_run);

  if (cfs_disconnect(fs) != 0) {
    fmt::print(stderr, "Fail to disconnect fs\n");
  }
  return 0;
}
