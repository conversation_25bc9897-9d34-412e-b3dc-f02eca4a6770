#include <fmt/format.h>
#include <gflags/gflags.h>

#include <atomic>
#include <functional>
#include <iostream>
#include <thread>

#include "cloudfs2/cloudfs2.h"
#include "common/datetime_util.h"
#include "common/defer.h"
#include "common/string_util.h"
#include "metric/metric_sdk/counter.h"
#include "metric/metric_sdk/summary.h"

DEFINE_uint32(jobs, 1, "Number of threads (one file per thread) to write");
DEFINE_string(bench_dir, "/cfs_bench_dir", "Dir for write files");
DEFINE_string(op, "W", "Supported ops: W,WX,AW,AWX");
DEFINE_uint64(buf_len, 2 * 1024 * 1024, "Buffer size for write");
DEFINE_uint64(file_len, 8LLU * 1024 * 1024 * 1024, "File size");
DEFINE_uint32(files_per_job, 1, "File num per job");
DEFINE_uint32(sub_dir_num, 1, "subdir num for all jobs");
DEFINE_int64(hist_sec, 5, "Interval for speed histgram");
DEFINE_bool(output_each_interval, true, "");

DECLARE_uint64(cfs_max_ongoing_write_size);

using DatetimeUtil = cfs::internal::DatetimeUtil;
using StringUtil = cfs::internal::StringUtil;
using Summary = cfs::internal::Summary;
using Counter = cfs::internal::Counter;

struct AsyncWriteArg {
  uint64_t req_size;
  Summary* summary;
  Counter* io_bytes;
  int64_t start_us;
  std::atomic<uint64_t>* fly_req_bytes;
};

static void OnAsyncWriteDone(cfs_status status, int64_t size_write,
                             void* args) {
  auto* write_arg = static_cast<AsyncWriteArg*>(args);
  if (status != CFS_STATUS_OK) {
    fmt::print(stderr, "Fail to write file, status={}\n",
               static_cast<int32_t>(status));
  } else {
    if (static_cast<uint64_t>(size_write) != write_arg->req_size) {
      fmt::print(stderr, "Write size mismatch, request={}, response={}\n",
                 write_arg->req_size, size_write);
    }
    write_arg->summary->Add(DatetimeUtil::GetNowTimeUs() - write_arg->start_us);
    write_arg->io_bytes->Increase(size_write);
  }
  write_arg->fly_req_bytes->fetch_sub(write_arg->req_size,
                                      std::memory_order_release);
  delete write_arg;
}

static void AsyncWriteFunc(uint32_t id, cfs_fs* fs, Summary* summary,
                           Counter* io_bytes) {
  uint32_t dir_id = id % FLAGS_sub_dir_num;
  std::string dir =
      StringUtil::StrFormat("{}/subdir_{}", FLAGS_bench_dir, dir_id);
  cfs_open_option_t open_opts = {
      .flags = CFS_O_WRONLY | CFS_O_CREATE | CFS_O_TRUNC,
      .mode = 0644,
      .create_parent = true,
      .replication = 0,
      .force = false};

  std::string wbuf;
  cfs_iobuf* wiobuf = nullptr;
  if (FLAGS_op == "AW") {
    wbuf.resize(FLAGS_buf_len, '1');
  } else if (FLAGS_op == "AWX") {
    wiobuf = cfs_create_iobuf(FLAGS_buf_len);
    if (wiobuf == nullptr) {
      fmt::print("Fail to create iobuf, len={}", FLAGS_buf_len);
      return;
    }
  } else {
    // impossile
    std::abort();
  }

  std::atomic<uint64_t> fly_bytes{0};

  for (uint32_t i = 0; i < FLAGS_files_per_job; ++i) {
    std::string path = StringUtil::StrFormat("{}/job_{}_file_{}", dir, id, i);
    cfs_file* file = cfs_open(fs, path.c_str(), &open_opts);
    if (file == nullptr) {
      fmt::print(stderr, "Fail to open file for write, path={}, err={}\n", path,
                 cfs_get_last_error()->message);
      return;
    }

    uint64_t finish = 0;
    auto max_fly_bytes = FLAGS_cfs_max_ongoing_write_size;
    while (finish < FLAGS_file_len) {
      while (fly_bytes.load(std::memory_order_acquire) >= max_fly_bytes) {
        // busy wait
      }

      AsyncWriteArg* arg = new AsyncWriteArg();
      arg->req_size = FLAGS_buf_len;
      arg->summary = summary;
      arg->io_bytes = io_bytes;
      arg->start_us = DatetimeUtil::GetNowTimeUs();
      arg->fly_req_bytes = &fly_bytes;
      if (FLAGS_op == "AW") {
        int32_t res = cfs_async_write(file, wbuf.data(), wbuf.size(),
                                      &OnAsyncWriteDone, arg);
        if (res < 0) {
          fmt::print(stderr, "Fail to write file, id={}, err={}\n", id,
                     cfs_get_last_error()->message);
          break;
        }
      } else if (FLAGS_op == "AWX") {
        int32_t res = cfs_async_writex(file, wiobuf, &OnAsyncWriteDone, arg);
        if (res < 0) {
          fmt::print(stderr, "Fail to async_writex file, id={}, err={}\n", id,
                     cfs_get_last_error()->message);
          break;
        }
      } else {
        std::abort();
      }
      fly_bytes.fetch_add(FLAGS_buf_len, std::memory_order_release);
      finish += FLAGS_buf_len;
    }
    // wait all fly requests to finish
    // No need to wait here. cfs_close will wait flying requests automatically
    // while (fly_bytes.load(std::memory_order_acquire) > 0) {
    //   std::this_thread::sleep_for(std::chrono::seconds(1));
    // }
    if (cfs_close(file) != 0) {
      fmt::print(stderr, "Fail to close file, id={}, err={}\n", id,
                 cfs_get_last_error()->message);
    }
  }

  if (cfs_free_iobuf(wiobuf) != 0) {
    fmt::print("Fail to free iobuf, err={}", cfs_get_last_error()->message);
  }
}

static void WriteFunc(uint32_t id, cfs_fs* fs, Summary* summary,
                      Counter* io_bytes) {
  uint32_t dir_id = id % FLAGS_sub_dir_num;
  std::string dir =
      StringUtil::StrFormat("{}/subdir_{}", FLAGS_bench_dir, dir_id);
  cfs_open_option_t open_opts = {
      .flags = CFS_O_WRONLY | CFS_O_CREATE | CFS_O_TRUNC,
      .mode = 0644,
      .create_parent = true,
      .replication = 0,
      .force = false};

  std::string wbuf;
  cfs_iobuf* wiobuf = nullptr;
  Defer defer1 = [&wiobuf]() {
    if (cfs_free_iobuf(wiobuf) != 0) {
      fmt::print("Fail to free iobuf, err={}", cfs_get_last_error()->message);
    }
  };
  if (FLAGS_op == "W") {
    wbuf.resize(FLAGS_buf_len, '1');
  } else if (FLAGS_op == "WX") {
    wiobuf = cfs_create_iobuf(FLAGS_buf_len);
    if (wiobuf == nullptr) {
      fmt::print("Fail to create iobuf, len={}", FLAGS_buf_len);
      return;
    }
  } else {
    // impossile
    std::abort();
  }

  for (uint32_t i = 0; i < FLAGS_files_per_job; ++i) {
    std::string path = StringUtil::StrFormat("{}/job_{}_file_{}", dir, id, i);
    cfs_file* file = cfs_open(fs, path.c_str(), &open_opts);
    if (file == nullptr) {
      fmt::print(stderr, "Fail to open file for write, path={}\n", path);
      return;
    }
    uint64_t finish = 0;
    while (finish < FLAGS_file_len) {
      int64_t res = 0;
      int64_t start_us = DatetimeUtil::GetNowTimeUs();
      if (FLAGS_op == "W") {
        res = cfs_write(file, wbuf.data(), wbuf.size());
        if (res < 0) {
          fmt::print(stderr, "Fail to write file, id={}, err={}\n", id,
                     cfs_get_last_error()->message);
          break;
        }
      } else if (FLAGS_op == "WX") {
        res = cfs_writex(file, wiobuf);
        if (res < 0) {
          fmt::print(stderr, "Fail to async_writex file, id={}, err={}\n", id,
                     cfs_get_last_error()->message);
          break;
        }
      } else {
        std::abort();
      }
      summary->Add(DatetimeUtil::GetNowTimeUs() - start_us);
      io_bytes->Increase(res);
      finish += res;
    }

    if (cfs_close(file) != 0) {
      fmt::print(stderr, "Fail to close file, id={}, err={}\n", id,
                 cfs_get_last_error()->message);
    }
  }
}

static void MonitorFunc(std::atomic<uint32_t>* alive_workers, Summary* summary,
                        Counter* io_bytes) {
  std::vector<int64_t> speed_hist;  // MB/s
  std::vector<int64_t> lat_avg_hist;
  std::vector<int64_t> lat_p99_hist;
  std::vector<double> qps_hist;
  bool clear_output = false;

  int64_t last_bytes = 0;
  int64_t last_time_us = DatetimeUtil::GetNowTimeUs();

  while (alive_workers->load(std::memory_order_acquire) > 0) {
    std::this_thread::sleep_for(std::chrono::seconds(FLAGS_hist_sec));
    int64_t now = DatetimeUtil::GetNowTimeUs();
    int64_t dur_us = now - last_time_us;
    last_time_us = now;

    int64_t bytes_now = io_bytes->GetValue();
    int64_t bytes = bytes_now - last_bytes;
    last_bytes = bytes_now;
    int64_t thpt = bytes * 1000 / 1024 * 1000 / 1024 / dur_us;
    speed_hist.push_back(thpt);

    auto snap = summary->GetSnapshot();
    lat_avg_hist.push_back(snap.avg);
    lat_p99_hist.push_back(snap.p99);
    qps_hist.push_back(snap.qps);
    if (FLAGS_output_each_interval) {
      if (clear_output) {
        for (uint32_t i = 0; i < 5; i++) {
          // Move the cursor up one line
          std::cout << "\x1b[A";
          // Clear the entire line
          std::cout << "\x1b[2K";
        }
      }
      fmt::print("======= statictics({}) =======\n",
                 DatetimeUtil::GetNowTimeLocalStr("%H:%M:%S"));
      fmt::print("Latency-avg(us) hist:  {}\n", fmt::join(lat_avg_hist, ","));
      fmt::print("Latency-p99(us) hist:  {}\n", fmt::join(lat_p99_hist, ","));
      fmt::print("QPS hist:  {:.2f}\n", fmt::join(qps_hist, ","));
      fmt::print("Throughput(MB/s) hist:  {}\n", fmt::join(speed_hist, ","));
      clear_output = true;
    }
  }
  // print histgrams
  if (!FLAGS_output_each_interval) {
    fmt::print("======== statictics (interval={}s) ========\n", FLAGS_hist_sec);
    fmt::print("Throughput(MB/s) hist:\n  {}\n", fmt::join(speed_hist, ","));
    fmt::print("Latency-avg(us) hist:\n  {}\n", fmt::join(lat_avg_hist, ","));
    fmt::print("Latency-p99(us) hist:\n  {}\n", fmt::join(lat_p99_hist, ","));
    fmt::print("QPS hist:\n  {:.2f}\n", fmt::join(qps_hist, ","));
  }
}

int main(int argc, char* argv[]) {
  gflags::ParseCommandLineFlags(&argc, &argv, true);
  if (FLAGS_op != "W" && FLAGS_op != "WX" && FLAGS_op != "AW" &&
      FLAGS_op != "AWX") {
    fmt::print(stderr, "Unknow op: {}\n", FLAGS_op);
    return -1;
  }

  cfs_fs* fs = cfs_connect(nullptr);
  if (fs == nullptr) {
    fmt::print(stderr, "Fail to connect to fs");
    return -1;
  }

  if (cfs_exist(fs, FLAGS_bench_dir.c_str()) == 1) {
    if (cfs_delete(fs, FLAGS_bench_dir.c_str(), true) != 0) {
      fmt::print(stderr, "Fail to delete bench dir\n");
      return -1;
    }
  }
  if (cfs_mkdir(fs, FLAGS_bench_dir.c_str(), 0755, true) != 0) {
    fmt::print(stderr, "Fail to create bench dir\n");
    return -1;
  }

  std::vector<std::thread> workers;
  Summary summary(FLAGS_hist_sec);
  Counter io_bytes;
  std::atomic<uint32_t> alive_workers;
  alive_workers.store(FLAGS_jobs);

  std::thread monitor([&alive_workers, &summary, &io_bytes]() {
    MonitorFunc(&alive_workers, &summary, &io_bytes);
  });
  auto start_us = DatetimeUtil::GetNowTimeUs();
  for (uint32_t i = 0; i < FLAGS_jobs; i++) {
    if (FLAGS_op == "AW" || FLAGS_op == "AWX") {
      workers.push_back(
          std::thread([i, fs, &summary, &io_bytes, &alive_workers]() {
            AsyncWriteFunc(i, fs, &summary, &io_bytes);
            alive_workers.fetch_sub(1, std::memory_order_release);
          }));
    } else if (FLAGS_op == "W" || FLAGS_op == "WX") {
      workers.push_back(
          std::thread([i, fs, &summary, &io_bytes, &alive_workers]() {
            WriteFunc(i, fs, &summary, &io_bytes);
            alive_workers.fetch_sub(1, std::memory_order_release);
          }));
    } else {
      std::abort();
    }
  }

  for (uint32_t i = 0; i < FLAGS_jobs; i++) {
    workers[i].join();
  }
  auto duration_us = DatetimeUtil::GetNowTimeUs() - start_us;
  monitor.join();

  int64_t thput =
      FLAGS_file_len * FLAGS_jobs * 1000 / 1024 * 1000 / 1024 / duration_us;
  fmt::print("Elapsed time: {}us. Throughput: {}MB/s\n", duration_us, thput);

  if (cfs_disconnect(fs) != 0) {
    fmt::print(stderr, "Fail to disconnect fs\n");
  }
  return 0;
}
