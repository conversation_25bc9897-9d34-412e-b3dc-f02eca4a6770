// This benchmark simulate seed's io-pattern of long-context read. It will read
// all tensors (simultaneously) in one round (wait all tensors finish to read)
// and then start the next round

#include <fmt/format.h>
#include <gflags/gflags.h>
#include <sys/mman.h>

#include <atomic>
#include <condition_variable>
#include <future>
#include <iostream>
#include <mutex>
#include <thread>

#include "cloudfs2/cloudfs2.h"
#include "common/datetime_util.h"
#include "common/string_util.h"
#include "common/thread_pool.h"
#include "metric/metric_sdk/counter.h"
#include "metric/metric_sdk/summary.h"

DEFINE_uint32(jobs, 1, "Number of threads");
DEFINE_string(bench_dir, "/cfs_seed_bench_dir", "");
DEFINE_string(op, "R", "R,W");
DEFINE_uint64(value_len, 132 * 1024 * 1024, "value size for mset");
DEFINE_uint64(align_len, 4 * 1024, "");
DEFINE_uint64(write_buf_size, 1024 * 1024, "");
DEFINE_uint32(batch_size, 1, "keys in each batch");
DEFINE_uint32(batch_num, 1, "number of batches");
DEFINE_uint32(part_num, 8, "number of parts for each file");
DEFINE_int64(hist_sec, 3, "Interval for speed histgram");
DEFINE_bool(long_run, false, "");

DECLARE_uint64(cfs_max_ongoing_write_size);

using DatetimeUtil = cfs::internal::DatetimeUtil;
using StringUtil = cfs::internal::StringUtil;
using Summary = cfs::internal::Summary;
using Counter = cfs::internal::Counter;
using ThreadPool = cfs::internal::ThreadPool;

struct JobCtx {
  uint32_t job_id;
  cfs_fs* fs;
  Summary* summary;
  Counter* io_bytes;
  std::atomic<uint32_t>* alive_jobs;

  JobCtx(uint32_t id, cfs_fs* f, Summary* s, Counter* c,
         std::atomic<uint32_t>* a)
      : job_id(id), fs(f), summary(s), io_bytes(c), alive_jobs(a) {}
};

struct AsyncWriteCtx {
  const char* data;
  uint64_t total_len;
  uint64_t pending_bytes;
  std::atomic<uint64_t> fly_bytes{0};
  // 0: running, 1: success, -1: fail
  std::atomic<int32_t> result{0};

  AsyncWriteCtx(const char* d, uint64_t len)
      : data(d), total_len(len), pending_bytes(len) {}
};

static std::vector<uint64_t> SplitPart(uint64_t total_len, uint32_t num,
                                       uint64_t align) {
  uint64_t n_aligns = total_len / align;
  if (total_len % align > 0) {
    n_aligns++;
  }
  uint64_t n_align_each_part = n_aligns / num;
  std::vector<uint64_t> ret;
  ret.resize(num, n_align_each_part * align);
  uint64_t remain_len = total_len - n_align_each_part * align * num;
  uint32_t idx = 0;
  while (remain_len > 0) {
    if (remain_len >= align) {
      ret[idx] += align;
      remain_len -= align;
      idx++;
    } else {
      // put the last un-aligned len to the end
      ret[num - 1] += remain_len;
      remain_len = 0;
    }
  }
  return ret;
}

static void OnAsyncWriteDone(cfs_status status, int64_t size_write,
                             void* args) {
  auto* ctx = static_cast<AsyncWriteCtx*>(args);
  if (status != CFS_STATUS_OK) {
    ctx->result.store(-1, std::memory_order_release);
    fmt::print(stderr, "Fail to write file, status={}\n",
               static_cast<int32_t>(status));
  } else {
  }
  ctx->fly_bytes.fetch_sub(size_write, std::memory_order_release);
}

static void DoAsyncWrite(cfs_file_handle fd, AsyncWriteCtx* ctx) {
  while ((ctx->pending_bytes > 0) &&
         (ctx->fly_bytes.load(std::memory_order_acquire) <
          FLAGS_cfs_max_ongoing_write_size)) {
    auto buflen = std::min(ctx->pending_bytes, FLAGS_write_buf_size);
    ctx->fly_bytes.fetch_add(buflen, std::memory_order_release);
    const char* val_ptr = ctx->data + (ctx->total_len - ctx->pending_bytes);
    int32_t res1 = cfs_async_write(fd, val_ptr, buflen, OnAsyncWriteDone, ctx);
    if (res1 != 0) {
      ctx->result.store(-1, std::memory_order_release);
      fmt::print(stderr, "Fail async_write\n");
      return;
    }
    ctx->pending_bytes -= buflen;
  }
}

static int32_t WriteFunc(const JobCtx& jobctx) {
  std::string dir =
      StringUtil::StrFormat("{}/jobdir_{}", FLAGS_bench_dir, jobctx.job_id);

  // 1. construct common data used for each batch_num
  std::string wbuf;
  wbuf.resize(FLAGS_value_len, '1');

  std::vector<uint32_t> part_nums;
  part_nums.resize(FLAGS_batch_size, FLAGS_part_num);
  std::vector<uint64_t> part_lens =
      SplitPart(FLAGS_value_len, FLAGS_part_num, FLAGS_align_len);

  for (uint32_t i = 0; i < FLAGS_batch_num; i++) {
    int64_t start_us = DatetimeUtil::GetNowTimeUs();
    std::vector<std::string> paths_str;
    std::vector<const char*> paths;

    std::vector<std::vector<std::unique_ptr<AsyncWriteCtx>>> subctxs;
    subctxs.resize(FLAGS_batch_size);
    paths_str.reserve(FLAGS_batch_size);
    paths.reserve(FLAGS_batch_size);
    for (uint32_t j = 0; j < FLAGS_batch_size; j++) {
      paths_str.push_back(
          StringUtil::StrFormat("{}/batch_num{}_idx{}", dir, i, j));
      uint64_t cursor = 0;
      for (uint32_t k = 0; k < FLAGS_part_num; k++) {
        const char* ptr = wbuf.data() + cursor;
        cursor += part_lens[k];
        auto subctx = std::make_unique<AsyncWriteCtx>(ptr, part_lens[k]);
        subctxs[j].push_back(std::move(subctx));
      }
    }
    for (uint32_t j = 0; j < FLAGS_batch_size; j++) {
      paths.push_back(paths_str[j].data());
    }

    cfs_batch_create_option_t opt;
    opt.paths = paths.data();
    opt.part_nums = part_nums.data();
    opt.wb_policy = nullptr;
    opt.num = FLAGS_batch_size;
    opt.mode = 0644;
    opt.replication = 0;
    auto** fds = cfs_batch_create(jobctx.fs, &opt);
    if (fds == nullptr) {
      fmt::print(stderr, "Fail to batch_create file, dir={}\n", dir);
      return -1;
    }

    // async-write to all the fds
    while (true) {
      uint32_t running_num = 0;
      for (uint32_t j = 0; j < part_nums.size(); ++j) {
        for (uint32_t k = 0; k < part_nums[j]; ++k) {
          int32_t res = subctxs[j][k]->result.load(std::memory_order_acquire);
          if (res != 0) {
            // This fd succeed to write all data or it has failed
            if (res < 0 &&
                subctxs[j][k]->fly_bytes.load(std::memory_order_acquire) > 0) {
              running_num++;
            }
            continue;
          }

          if (subctxs[j][k]->pending_bytes > 0) {
            DoAsyncWrite(fds[j][k], subctxs[j][k].get());
            running_num++;
          } else {
            if (subctxs[j][k]->fly_bytes.load(std::memory_order_acquire) > 0) {
              running_num++;
            } else {
              subctxs[j][k]->result.store(1, std::memory_order_release);
              // Only successful writes-thpt can be add to metrics
              jobctx.io_bytes->Increase(subctxs[j][k]->total_len);
            }
          }
        }
      }
      if (running_num == 0) {
        break;
      }
    }
    int32_t close_res =
        cfs_batch_close(fds, part_nums.data(), part_nums.size());
    if (close_res < 0) {
      fmt::print(stderr, "Fail to batch_close, dir={}\n", dir);
    }
    // record latency and thpt
    jobctx.summary->Add(DatetimeUtil::GetNowTimeUs() - start_us);
  }

  return 0;
}

static constexpr uint64_t kHugePgLimit = 1024 * 1024 * 4;
static constexpr uint64_t kHugePgAlign = 1024 * 1024 * 2;

static void* DoAlloc(uint64_t len) {
  void* ret = nullptr;
  if (len < kHugePgLimit) {
    ret = new (std::nothrow) char[len];
    if (ret == nullptr) {
      fmt::print("Fail to alloc small page, len={}\n", len);
    }
  } else {
    int res = posix_memalign(&ret, kHugePgAlign, len);
    if (res != 0) {
      fmt::print("Fail to alloc huge page, len={}, errno={}\n", len, res);
      return ret;
    }
    if (madvise(ret, len, MADV_HUGEPAGE) != 0) {
      fmt::print("Fail to madvise huge page, len={}, errno={}\n", len, errno);
    }
  }
  return ret;
}

static void DoFree(void* ptr, uint64_t len) {
  if (len < kHugePgLimit) {
    delete[] static_cast<char*>(ptr);
  } else {
    free(ptr);
  }
}

struct AsyncReadCtx {
  std::atomic<int32_t> fly_reqs;
  std::promise<bool> prom;
  Counter* io_bytes;
  std::string path;
  cfs_fs* fs;
  std::vector<void*>* part_buf;
  cfs_file* fd{nullptr};

  AsyncReadCtx(int32_t fly, Counter* c, const std::string& p, cfs_fs* f,
               std::vector<void*>* bf)
      : fly_reqs(fly), io_bytes(c), path(p), fs(f), part_buf(bf) {}
};

static void OnAsyncReadDone(cfs_status status, int64_t size_read, void* args) {
  auto* ctx = static_cast<AsyncReadCtx*>(args);
  if (status != CFS_STATUS_OK) {
    fmt::print(stderr, "Fail to read, status={}\n",
               static_cast<int32_t>(status));
  } else {
    // read_success
    ctx->io_bytes->Increase(size_read);
  }
  auto n = ctx->fly_reqs.fetch_sub(1, std::memory_order_release);
  if (n == 1) {
    ctx->prom.set_value(true);
  }
}

static void DoRead1Batch(AsyncReadCtx* ctx) {
  cfs_open_option_t open_opts;
  open_opts.flags = CFS_O_RDONLY;
  uint64_t part_len = FLAGS_value_len / FLAGS_part_num;
  cfs_file* fd = cfs_open(ctx->fs, ctx->path.c_str(), &open_opts);
  if (fd == nullptr) {
    fmt::print(stderr, "Fail to open file for read, path={}\n", ctx->path);
    return;
  }
  ctx->fd = fd;
  for (uint32_t j = 0; j < FLAGS_part_num; ++j) {
    int32_t res1 = cfs_async_pread(fd, ctx->part_buf->at(j), part_len,
                                   part_len * j, OnAsyncReadDone, ctx);
    if (res1 < 0) {
      fmt::print("Fail to async_pread, path={}\n", ctx->path);
    }
  }
}

static int32_t ReadFunc(const JobCtx& jobctx) {
  std::string dir =
      StringUtil::StrFormat("{}/jobdir_{}", FLAGS_bench_dir, jobctx.job_id);

  uint64_t part_len = FLAGS_value_len / FLAGS_part_num;
  std::vector<std::vector<void*>> part_bufs;
  part_bufs.resize(FLAGS_batch_size);
  for (uint32_t i = 0; i < FLAGS_batch_size; i++) {
    part_bufs[i].resize(FLAGS_part_num, nullptr);
    for (uint32_t j = 0; j < FLAGS_part_num; j++) {
      part_bufs[i][j] = DoAlloc(part_len);
    }
  }

  ThreadPool tp(FLAGS_batch_size, "SeedBench");

  for (uint32_t i = 0; i < FLAGS_batch_num; i++) {
    int64_t start_us = DatetimeUtil::GetNowTimeUs();
    std::vector<std::unique_ptr<AsyncReadCtx>> rctxs;
    // rctxs.reserve(FLAGS_batch_size);
    std::vector<std::future<bool>> futs;
    futs.reserve(FLAGS_batch_size);
    for (uint32_t j = 0; j < FLAGS_batch_size; j++) {
      auto p = StringUtil::StrFormat("{}/batch_num{}_idx{}", dir, i, j);
      auto* bf = &part_bufs[j];
      auto c = std::make_unique<AsyncReadCtx>(FLAGS_part_num, jobctx.io_bytes,
                                              p, jobctx.fs, bf);
      rctxs.push_back(std::move(c));
      futs.push_back(rctxs[j]->prom.get_future());
    }

    for (uint32_t j = 0; j < FLAGS_batch_size; j++) {
      auto* ctx_ptr = rctxs[j].get();
      tp.AddTask([ctx_ptr]() {
        DoRead1Batch(ctx_ptr);
      });
    }

    // wait all async_pread to finish
    for (uint32_t j = 0; j < FLAGS_batch_size; j++) {
      futs[j].get();
      if (cfs_close(rctxs[j]->fd) < 0) {
        fmt::print(stderr, "Fail to close, path={}\n", rctxs[j]->path);
      }
    }

    jobctx.summary->Add(DatetimeUtil::GetNowTimeUs() - start_us);
  }

  for (uint32_t i = 0; i < FLAGS_batch_size; i++) {
    for (uint32_t j = 0; j < FLAGS_part_num; j++) {
      DoFree(part_bufs[i][j], part_len);
    }
  }
  return 0;
}

static void MonitorFunc(const JobCtx& ctx, std::mutex& mtx,
                        std::condition_variable& cv) {
  std::vector<int64_t> lat_avg_hist;
  std::vector<int64_t> lat_p50_hist;
  std::vector<int64_t> lat_p99_hist;
  std::vector<double> qps_hist;
  std::vector<int64_t> speed_hist;  // MB/s
  bool clear_output = false;
  int64_t last_bytes = 0;
  int64_t last_time_us = DatetimeUtil::GetNowTimeUs();

  while (ctx.alive_jobs->load(std::memory_order_acquire) > 0) {
    std::unique_lock<std::mutex> lk(mtx);
    auto tp = std::chrono::time_point<std::chrono::system_clock>(
        std::chrono::microseconds(last_time_us + FLAGS_hist_sec * 1000000));
    cv.wait_until(lk, tp);

    int64_t now = DatetimeUtil::GetNowTimeUs();
    int64_t dur_us = now - last_time_us;
    last_time_us = now;

    int64_t bytes_now = ctx.io_bytes->GetValue();
    int64_t bytes = bytes_now - last_bytes;
    last_bytes = bytes_now;
    int64_t thpt = bytes * 1000 / 1024 * 1000 / 1024 / dur_us;
    speed_hist.push_back(thpt);

    auto snap = ctx.summary->GetSnapshot();
    lat_avg_hist.push_back(snap.avg);
    lat_p50_hist.push_back(snap.p50);
    lat_p99_hist.push_back(snap.p99);
    qps_hist.push_back(snap.qps);
    if (clear_output) {
      for (uint32_t i = 0; i < 6; i++) {
        // Move the cursor up one line
        std::cout << "\x1b[A";
        // Clear the entire line
        std::cout << "\x1b[2K";
      }
    }
    fmt::print("======= statictics({}) =======\n",
               DatetimeUtil::GetNowTimeLocalStr("%H:%M:%S"));
    fmt::print("Latency-avg(us) hist:  {}\n", fmt::join(lat_avg_hist, ","));
    fmt::print("Latency-p50(us) hist:  {}\n", fmt::join(lat_p50_hist, ","));
    fmt::print("Latency-p99(us) hist:  {}\n", fmt::join(lat_p99_hist, ","));
    fmt::print("QPS hist:  {:.2f}\n", fmt::join(qps_hist, ","));
    fmt::print("Throughput(MB/s) hist:  {}\n", fmt::join(speed_hist, ","));
    clear_output = true;
  }
}

int main(int argc, char* argv[]) {
  gflags::ParseCommandLineFlags(&argc, &argv, true);
  if (FLAGS_op != "R" && FLAGS_op != "W") {
    fmt::print(stderr, "Unknow op: {}\n", FLAGS_op);
    return -1;
  }

  cfs_fs* fs = cfs_connect(nullptr);
  if (fs == nullptr) {
    fmt::print(stderr, "Fail to connect to fs");
    return -1;
  }

  // Only mset need to delete old dir
  if (FLAGS_op == "W") {
    if (cfs_exist(fs, FLAGS_bench_dir.c_str()) == 1) {
      fmt::print("Deleting the old bench_dir `{}`, please wait...\n",
                 FLAGS_bench_dir);
      if (cfs_delete(fs, FLAGS_bench_dir.c_str(), true) != 0) {
        fmt::print(stderr, "Fail to delete bench dir\n");
        return -1;
      }
    }
    if (cfs_mkdir(fs, FLAGS_bench_dir.c_str(), 0755, true) != 0) {
      fmt::print(stderr, "Fail to create bench dir\n");
      return -1;
    }
    for (uint32_t i = 0; i < FLAGS_jobs; i++) {
      std::string subdir = FLAGS_bench_dir + "/jobdir_" + std::to_string(i);
      if (cfs_mkdir(fs, subdir.c_str(), 0755, false) != 0) {
        fmt::print(stderr, "Fail to create job_dir={}\n", subdir);
        return -1;
      }
    }
    fmt::print("===mkdir subdir complete ======\n");
  }

  std::vector<std::thread> workers;
  Summary summary(FLAGS_hist_sec);
  Counter io_bytes;
  std::atomic<uint32_t> alive_jobs(FLAGS_jobs);

  std::mutex monitor_mtx;
  std::condition_variable monitor_cv;
  JobCtx m_ctx(0, fs, &summary, &io_bytes, &alive_jobs);
  std::thread monitor([m_ctx, &monitor_mtx, &monitor_cv]() {
    MonitorFunc(m_ctx, monitor_mtx, monitor_cv);
  });
  for (uint32_t i = 0; i < FLAGS_jobs; i++) {
    JobCtx ctx(i, fs, &summary, &io_bytes, &alive_jobs);
    if (FLAGS_op == "W") {
      workers.push_back(std::thread([ctx]() {
        do {
          WriteFunc(ctx);
        } while (FLAGS_long_run);
        ctx.alive_jobs->fetch_sub(1, std::memory_order_release);
      }));
    } else if (FLAGS_op == "R") {
      workers.push_back(std::thread([ctx]() {
        do {
          ReadFunc(ctx);
        } while (FLAGS_long_run);
        ctx.alive_jobs->fetch_sub(1, std::memory_order_release);
      }));
    } else {
      std::abort();
    }
  }

  for (uint32_t i = 0; i < FLAGS_jobs; i++) {
    workers[i].join();
  }
  monitor_cv.notify_all();
  monitor.join();

  if (cfs_disconnect(fs) != 0) {
    fmt::print(stderr, "Fail to disconnect fs\n");
  }
  fmt::print("benchmark finish\n");
  return 0;
}
