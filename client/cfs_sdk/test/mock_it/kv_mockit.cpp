#include <gflags/gflags.h>
#include <gmock/gmock.h>
#include <gtest/gtest.h>

#include <thread>

#include "cloudfs2/cloudfs2.h"
#include "cloudfs2/cloudfs2_kv.h"
#include "mock/rpc_mock.h"
#include "test/tools/mock_util.h"

DEFINE_string(parent_dir, "/parent/dir", "must exist");
DEFINE_string(test_dir, "kv_mockit", "must not exist under parent_dir");

DECLARE_uint32(cfs_kv_write_replication);

namespace cfs {
namespace internal {

using ::testing::_;
using ::testing::AtLeast;
using ::testing::Gt;
using ::testing::NiceMock;
using ::testing::Return;

class KvMockIT : public testing::Test {
 public:
  static void SetUpTestSuite() {
    fs_ = cfs_connect(nullptr);
    ASSERT_NE(fs_, nullptr);
    // Make sure parent_dir exists
    auto* finfo = cfs_get_file_info(fs_, FLAGS_parent_dir.c_str());
    ASSERT_NE(finfo, nullptr);
    ASSERT_EQ(finfo->type, CFS_FILE_DIR);
    ASSERT_EQ(cfs_free_file_info(fs_, finfo, 1), 0);

    test_dir_ = FLAGS_parent_dir + "/" + FLAGS_test_dir;
    auto exist_res = cfs_exist(fs_, test_dir_.c_str());
    ASSERT_GE(exist_res, 0);
    if (exist_res > 0) {
      ASSERT_EQ(cfs_delete(fs_, test_dir_.c_str(), true), 0);
    }
    ASSERT_EQ(cfs_mkdir(fs_, test_dir_.c_str(), 0755, false), 0);

    MockUtil::SetDefaultMockReturnVal();
  }

  static void TearDownTestSuite() {
    ASSERT_EQ(cfs_disconnect(fs_), 0);
  }

 protected:
  void SetUp() override {}

  void TearDown() override {}

 protected:
  static cfs_fs* fs_;
  static std::string test_dir_;
};

cfs_fs* KvMockIT::fs_ = nullptr;
std::string KvMockIT::test_dir_;

// MGet/MCheck UC-file should report file-not-found
TEST_F(KvMockIT, MGetMCheckUCFile) {
  NiceMock<MockRpcStub> mock;
  EXPECT_CALL(mock, BatchCompleteFile(_, _, _, _, _, _))
      .Times(3)
      .WillOnce(Return(Status(CFS_ERR_RPC_TIMEDOUT)))
      .WillOnce(Return(Status(CFS_ERR_RPC_TIMEDOUT)))
      .WillOnce(Return(Status(CFS_ERR_MOCK_SEND_REMOTE)));
  RpcMock::GetInstance()->SetMockStub(&mock);

  std::string space1 = test_dir_ + "/MGetUCFile";
  ASSERT_EQ(cfs_kv_new_keyspace(fs_, space1.c_str(), nullptr), 0);
  std::vector<std::string> keys_str;
  std::vector<std::string> vals_str;
  for (uint32_t i = 0; i < 3; i++) {
    keys_str.push_back("mset_key_" + std::to_string(i));
    vals_str.push_back(std::string(100, 'a'));
  }
  // mset
  std::vector<cfs_kv_buffer_t> keys;
  for (auto& k : keys_str) {
    keys.push_back(cfs_kv_buffer_t{k.data(), k.size()});
  }
  std::vector<cfs_kv_buffer_t> vals;
  for (auto& v : vals_str) {
    vals.push_back(cfs_kv_buffer_t{v.data(), v.size()});
  }
  std::vector<cfs_status> mset_results;
  mset_results.resize(keys.size(), CFS_STATUS_INTERNAL_ERROR);
  std::thread mset_th([&]() {
    int res1 = cfs_kv_mset(fs_, space1.c_str(), keys.size(), keys.data(),
                           vals.data(), mset_results.data());
    EXPECT_EQ(res1, 0);
    for (auto& r : mset_results) {
      EXPECT_EQ(r, CFS_STATUS_OK);
    }
  });

  // Now the files are being writing but not completed
  std::this_thread::sleep_for(std::chrono::milliseconds(500));
  std::vector<cfs_kv_result_t> mget_results;
  mget_results.resize(keys.size());
  int res2 = cfs_kv_mget(fs_, space1.c_str(), keys.size(), keys.data(),
                         mget_results.data());
  EXPECT_EQ(res2, -1);
  for (uint32_t i = 0; i < keys.size(); i++) {
    EXPECT_EQ(mget_results[i].ret, CFS_STATUS_FILE_UC);
  }

  std::vector<cfs_status> mcheck_results;
  mcheck_results.resize(keys.size(), CFS_STATUS_INTERNAL_ERROR);
  int res3 = cfs_kv_mcheck(fs_, space1.c_str(), keys.size(), keys.data(),
                           mcheck_results.data(), false);
  EXPECT_EQ(res3, 0);
  for (uint32_t i = 0; i < keys.size(); i++) {
    EXPECT_EQ(mcheck_results[i], CFS_STATUS_FILE_UC);
  }

  // Now the files are completed
  std::this_thread::sleep_for(std::chrono::milliseconds(1000));
  res2 = cfs_kv_mget(fs_, space1.c_str(), keys.size(), keys.data(),
                     mget_results.data());
  EXPECT_EQ(res2, 0);
  for (uint32_t i = 0; i < keys.size(); i++) {
    EXPECT_EQ(mget_results[i].ret, CFS_STATUS_OK);
    EXPECT_EQ(mget_results[i].value.size, vals_str[i].size());
  }
  ASSERT_EQ(cfs_kv_mfree(fs_, mget_results.data(), mget_results.size()), 0);

  res3 = cfs_kv_mcheck(fs_, space1.c_str(), keys.size(), keys.data(),
                       mcheck_results.data(), false);
  EXPECT_EQ(res3, 0);
  for (uint32_t i = 0; i < keys.size(); i++) {
    EXPECT_EQ(mcheck_results[i], CFS_STATUS_OK);
  }

  mset_th.join();
  RpcMock::GetInstance()->SetMockStub(nullptr);
}

TEST_F(KvMockIT, MSetAbandonFirstBlk) {
  auto replica_saved = FLAGS_cfs_kv_write_replication;
  FLAGS_cfs_kv_write_replication = 1;
  NiceMock<MockRpcStub> mock;
  EXPECT_CALL(mock, WriteBlock(_, 0, Gt(0), _, _, _, _, _, _))
      .Times(AtLeast(1))
      .WillRepeatedly(Return(Status(CFS_ERR_MOCK_SEND_REMOTE)));
  EXPECT_CALL(mock, WriteBlock(_, 0, 0, _, _, _, _, _, _))
      .Times(AtLeast(3))
      .WillOnce(Return(Status(CFS_ERR_DN_BUSY, "non-retriable")))
      .WillOnce(Return(Status(CFS_ERR_DN_BUSY, "non-retriable")))
      .WillRepeatedly(Return(Status(CFS_ERR_MOCK_SEND_REMOTE)));
  EXPECT_CALL(mock, AbandonBlock(_, _, 0, 0, _))
      .Times(2)
      .WillRepeatedly(Return(Status(CFS_ERR_MOCK_SEND_REMOTE)));
  EXPECT_CALL(mock, AddBlock(_, 0, 0, _, _))
      .Times(2)
      .WillRepeatedly(Return(Status(CFS_ERR_MOCK_SEND_REMOTE)));
  RpcMock::GetInstance()->SetMockStub(&mock);

  std::string space1 = test_dir_ + "/MSetAbandonFirstBlk";
  ASSERT_EQ(cfs_kv_new_keyspace(fs_, space1.c_str(), nullptr), 0);
  std::vector<std::string> keys_str;
  std::vector<std::string> vals_str;
  for (uint32_t i = 0; i < 1; i++) {
    keys_str.push_back("mset_key_" + std::to_string(i));
    vals_str.push_back(std::string(16000000, 'a'));
  }
  // mset
  std::vector<cfs_kv_buffer_t> keys;
  for (auto& k : keys_str) {
    keys.push_back(cfs_kv_buffer_t{k.data(), k.size()});
  }
  std::vector<cfs_kv_buffer_t> vals;
  for (auto& v : vals_str) {
    vals.push_back(cfs_kv_buffer_t{v.data(), v.size()});
  }
  std::vector<cfs_status> mset_results;
  mset_results.resize(keys.size(), CFS_STATUS_INTERNAL_ERROR);
  int res1 = cfs_kv_mset(fs_, space1.c_str(), keys.size(), keys.data(),
                         vals.data(), mset_results.data());
  EXPECT_EQ(res1, 0);
  for (auto& r : mset_results) {
    EXPECT_EQ(r, CFS_STATUS_OK);
  }
  RpcMock::GetInstance()->SetMockStub(nullptr);

  FLAGS_cfs_kv_write_replication = replica_saved;
}

}  // namespace internal
}  // namespace cfs

int main(int argc, char** argv) {
  // InitGoogleTest must be put before ParseCommandLineFlags because
  // 'ParseCommandLineFlags' does not known '--gtest_filter' and will report
  // error
  testing::InitGoogleTest(&argc, argv);
  gflags::ParseCommandLineFlags(&argc, &argv, true);
  return RUN_ALL_TESTS();
}