#pragma once

#include <string>

#include "cloudfs2/cloudfs2.h"

namespace cfs {
namespace internal {

class TestUtil {
 public:
  static bool WriteGenData2Remote(cfs_file* fd, uint64_t file_len);

  static bool CopyLocalFile2Remote(cfs_file* fd, const std::string& local_path,
                                   uint64_t max_len);

  static std::string NicName2IpStr(const std::string& nic_name);
};

}  // namespace internal
}  // namespace cfs
