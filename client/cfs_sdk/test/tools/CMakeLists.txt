aux_source_directory(. TEST_TOOLS_SRCS)

add_library(test_tools_obj OBJECT
  ${TEST_TOOLS_SRCS}
  )
target_include_directories(test_tools_obj
  PRIVATE ${CFS_HOME}/include
  PRIVATE ${CFS_HOME}/src
  PRIVATE ${CFS_HOME}
  PRIVATE ${PROTO_DST_PATH}
  )

# This target_link_libraries is used to pass SPDLOG_COMPILED_LIB to
# cfs_obj so that it will not use header-only version of spdlog.
target_link_libraries(test_tools_obj
  spdlog::spdlog
  )
add_dependencies(test_tools_obj
    proto_gen
)
