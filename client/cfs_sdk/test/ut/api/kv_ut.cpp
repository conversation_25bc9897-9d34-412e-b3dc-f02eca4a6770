#include <gflags/gflags.h>
#include <gmock/gmock.h>
#include <gtest/gtest.h>

#include "cloudfs2/cloudfs2.h"
#include "cloudfs2/cloudfs2_kv.h"
#include "mock/rpc_mock.h"
#include "test/tools/mock_util.h"

DECLARE_bool(cfs_filesystem_endpoint_resolve_by_dns);
DECLARE_string(cfs_log_type);
DECLARE_string(cfs_byterpc_trans_type_dn);
DECLARE_string(cfs_expect_dn_subnet);
DECLARE_uint32(cfs_read_packet_size);
DECLARE_int64(cfs_read_max_retry_time_ms);
DECLARE_int64(cfs_read_retry_interval_ms);
DECLARE_uint32(cfs_read_max_retry_missing_block_num);
DECLARE_bool(cfs_read_blk_shuffle_thread);
DECLARE_int64(cfs_dn_rpc_max_retry_time_ms);
DECLARE_bool(cfs_enable_slow_node_management);

namespace cfs {
namespace internal {

using ::testing::_;
using ::testing::AnyOf;
using ::testing::AtLeast;
using ::testing::Return;
using ::testing::StrictMock;
using ::testing::WithArgs;

class KvUT : public testing::Test {
 public:
  static void SetUpTestSuite() {
    FLAGS_cfs_filesystem_endpoint_resolve_by_dns = false;
    FLAGS_cfs_log_type = "stdout";
    FLAGS_cfs_byterpc_trans_type_dn = "KTCP";
    FLAGS_cfs_expect_dn_subnet = "172.1.0.0/16";
    FLAGS_cfs_read_blk_shuffle_thread = true;
    // disable slow node management to avoid invoke GetDatanodeReport RPC
    FLAGS_cfs_enable_slow_node_management = false;
    fs_ = cfs_connect(nullptr);
    ASSERT_NE(fs_, nullptr);
  }

  static void TearDownTestSuite() {
    ASSERT_EQ(cfs_disconnect(fs_), 0);
  }

 protected:
  void SetUp() override {}

  void TearDown() override {}

 protected:
  static cfs_fs* fs_;
};

cfs_fs* KvUT::fs_ = nullptr;

TEST_F(KvUT, MissingPartReplicaRetrySucc) {
  auto retry_interval_saved = FLAGS_cfs_read_retry_interval_ms;
  auto retry_time_saved = FLAGS_cfs_read_max_retry_time_ms;
  FLAGS_cfs_read_retry_interval_ms = 1000;
  FLAGS_cfs_read_max_retry_time_ms = 1000 * 60;

  std::string space1 = "/KeySpace1";
  std::vector<std::string> keys_str = {"mset0"};
  std::vector<std::string> paths = {"/KeySpace1/mset0"};
  constexpr uint64_t kFileSize = kMockBlkSize * 2;
  std::vector<MockLocatedBlk> blks1 = {
      {1001,
       0,
       kMockBlkSize,
       {"10.1.1.1", "10.1.1.2"},
       {{"10.1.1.1", "172.1.1.1"}, {"10.1.1.2", "172.1.1.2"}},
       {{}, {}},
       {6102, 6101}},
      {1002, kMockBlkSize, kMockBlkSize, {}, {}, {}, {}}};
  std::vector<MockLocatedBlk> blks2 = {
      {1001,
       0,
       kMockBlkSize,
       {"10.1.1.1", "10.1.1.2"},
       {{"10.1.1.1", "172.1.1.1"}, {"10.1.1.2", "172.1.1.2"}},
       {{}, {}},
       {6102, 6101}},
      {1002,
       kMockBlkSize,
       kMockBlkSize,
       {"10.1.1.3", "10.1.1.4"},
       {{"10.1.1.3", "172.1.1.3"}, {"10.1.1.4", "172.1.1.4"}},
       {{}, {}},
       {6101, 6102}}};
  std::vector<uint32_t> storage_ports = {6101, 6102};
  auto finfo1 = MockUtil::GenBatchGetRsp(paths, {blks1}, kFileSize);
  auto lbs1 = MockUtil::GenBlkLocs(kFileSize, blks1, storage_ports);
  auto lbs2 = MockUtil::GenBlkLocs(kFileSize, blks2, storage_ports);
  auto bres = MockUtil::GenReadBlockRsp(0);

  StrictMock<MockRpcStub> mock;
  EXPECT_CALL(mock, BatchGetFile(paths, true))
      .Times(1)
      .WillOnce(Return(finfo1));
  EXPECT_CALL(mock, GetBlockLocations(0, _))
      .Times(2)
      .WillOnce(Return(lbs1))
      .WillOnce(Return(lbs2));
  EXPECT_CALL(mock, ReadBlock(1001, 0, _, _, "172.1.1.1", _, _))
      .Times(kMockBlkSize / FLAGS_cfs_read_packet_size)
      .WillRepeatedly(WithArgs<2, 3>([&bres](uint64_t off, uint64_t len) {
        return std::make_pair(bres, MockUtil::GenReadData(off, len));
      }));
  EXPECT_CALL(mock, ReadBlock(1002, 0, _, _, "172.1.1.3", _, _))
      .Times(kMockBlkSize / FLAGS_cfs_read_packet_size)
      .WillRepeatedly(WithArgs<2, 3>([&bres](uint64_t off, uint64_t len) {
        return std::make_pair(bres, MockUtil::GenReadData(off, len));
      }));
  RpcMock::GetInstance()->SetMockStub(&mock);

  std::vector<cfs_kv_buffer_t> keys;
  for (auto& k : keys_str) {
    keys.push_back(cfs_kv_buffer_t{k.data(), k.size()});
  }
  std::vector<cfs_kv_result_t> mget_results;
  mget_results.resize(keys.size());
  int res2 = cfs_kv_mget(fs_, space1.c_str(), keys.size(), keys.data(),
                         mget_results.data());
  EXPECT_EQ(res2, 0);
  for (uint32_t i = 0; i < keys.size(); i++) {
    ASSERT_EQ(mget_results[i].ret, CFS_STATUS_OK);
    EXPECT_NE(mget_results[i].value.data, nullptr);
  }
  ASSERT_EQ(cfs_kv_mfree(fs_, mget_results.data(), mget_results.size()), 0);

  FLAGS_cfs_read_retry_interval_ms = retry_interval_saved;
  FLAGS_cfs_read_max_retry_time_ms = retry_time_saved;
}

TEST_F(KvUT, ReadAllDnBlkNotExist) {
  auto retry_saved = FLAGS_cfs_read_max_retry_missing_block_num;
  FLAGS_cfs_read_max_retry_missing_block_num = 0;
  std::string space1 = "/KeySpace1";
  std::vector<std::string> keys_str = {"mset0"};
  std::vector<std::string> paths = {"/KeySpace1/mset0"};
  const uint64_t kFileSize = FLAGS_cfs_read_packet_size * 2;
  std::vector<MockLocatedBlk> blks1 = {
      {1001,
       0,
       FLAGS_cfs_read_packet_size,
       {"10.1.1.1", "10.1.1.2"},
       {{"10.1.1.1", "172.1.1.1"}, {"10.1.1.2", "172.1.1.2"}},
       {{}, {}},
       {6102, 6101}},
      {1002,
       FLAGS_cfs_read_packet_size,
       FLAGS_cfs_read_packet_size,
       {"10.1.1.3", "10.1.1.4"},
       {{"10.1.1.3", "172.1.1.3"}, {"10.1.1.4", "172.1.1.4"}},
       {{}, {}},
       {}}};  // Set blk-1002's byterpc_port to undefined to test GetBlockInfo
  std::vector<uint32_t> storage_ports = {6101, 6102};
  auto finfo1 = MockUtil::GenBatchGetRsp(paths, {blks1}, kFileSize);
  auto bres = MockUtil::GenReadBlockRsp(0);

  StrictMock<MockRpcStub> mock;
  EXPECT_CALL(mock, BatchGetFile(paths, true))
      .Times(1)
      .WillOnce(Return(finfo1));
  EXPECT_CALL(mock, GetBlockLocations(_, _)).Times(0);
  EXPECT_CALL(mock,
              ReadBlock(1001, 0, _, _, AnyOf("172.1.1.1", "172.1.1.2"), _, _))
      .Times(2)  // 2 replicas
      .WillRepeatedly(Return(Status(CFS_ERR_DN_BLOCK_NOT_EXIST)));
  EXPECT_CALL(mock, GetReplicaInfo(1002, AnyOf("172.1.1.3", "172.1.1.4"), _))
      .Times(2)
      .WillRepeatedly(Return(Status(CFS_ERR_DN_BLOCK_NOT_EXIST_REMOTE)));
  RpcMock::GetInstance()->SetMockStub(&mock);

  std::vector<cfs_kv_buffer_t> keys;
  for (auto& k : keys_str) {
    keys.push_back(cfs_kv_buffer_t{k.data(), k.size()});
  }
  std::vector<cfs_kv_result_t> mget_results;
  mget_results.resize(keys.size());
  int res2 = cfs_kv_mget(fs_, space1.c_str(), keys.size(), keys.data(),
                         mget_results.data());
  EXPECT_EQ(res2, -1);
  for (uint32_t i = 0; i < keys.size(); i++) {
    ASSERT_EQ(mget_results[i].ret, CFS_STATUS_NO_REPLICA);
    EXPECT_EQ(mget_results[i].value.data, nullptr);
  }
  ASSERT_EQ(cfs_kv_mfree(fs_, mget_results.data(), mget_results.size()), 0);
  FLAGS_cfs_read_max_retry_missing_block_num = retry_saved;
}

TEST_F(KvUT, ReadAllDnBlkNotExistRetrySucc) {
  auto retry_saved = FLAGS_cfs_read_max_retry_missing_block_num;
  FLAGS_cfs_read_max_retry_missing_block_num = 2;
  const uint64_t kFileSize = FLAGS_cfs_read_packet_size * 4;
  std::string space1 = "/KeySpace1";
  std::vector<std::string> keys_str = {"mset0"};
  std::vector<std::string> paths = {"/KeySpace1/mset0"};
  std::vector<MockLocatedBlk> blks1 = {
      {1001,
       0,
       FLAGS_cfs_read_packet_size * 2,
       {"10.1.1.1", "10.1.1.2"},
       {{"10.1.1.1", "172.1.1.1"}, {"10.1.1.2", "172.1.1.2"}},
       {{}, {}},
       {6102, 6101}},
      {1002,
       FLAGS_cfs_read_packet_size * 2,
       FLAGS_cfs_read_packet_size * 2,
       {"10.1.1.3", "10.1.1.4"},
       {{"10.1.1.3", "172.1.1.3"}, {"10.1.1.4", "172.1.1.4"}},
       {{}, {}},
       {6101, 6101}}};
  std::vector<MockLocatedBlk> blks2 = {
      {1001,
       0,
       FLAGS_cfs_read_packet_size * 2,
       {"10.1.2.1", "10.1.2.2"},
       {{"10.1.2.1", "172.1.2.1"}, {"10.1.2.2", "172.1.2.2"}},
       {{}, {}},
       {6102, 6101}},
      {1002,
       FLAGS_cfs_read_packet_size * 2,
       FLAGS_cfs_read_packet_size * 2,
       {"10.1.2.3", "10.1.2.4"},
       {{"10.1.2.3", "172.1.2.3"}, {"10.1.2.4", "172.1.2.4"}},
       {{}, {}},
       {6101, 6101}}};
  std::vector<MockLocatedBlk> blks3 = {
      {1001,
       0,
       FLAGS_cfs_read_packet_size * 2,
       {"10.1.3.1", "10.1.3.2"},
       {{"10.1.3.1", "172.1.3.1"}, {"10.1.3.2", "172.1.3.2"}},
       {{}, {}},
       {6102, 6101}},
      {1002,
       FLAGS_cfs_read_packet_size * 2,
       FLAGS_cfs_read_packet_size * 2,
       {"10.1.3.3", "10.1.3.4"},
       {{"10.1.3.3", "172.1.3.3"}, {"10.1.3.4", "172.1.3.4"}},
       {{}, {}},
       {6101, 6101}}};
  std::vector<uint32_t> storage_ports = {6101, 6102};
  auto finfo1 = MockUtil::GenBatchGetRsp(paths, {blks1}, kFileSize);
  auto lbs2 = MockUtil::GenBlkLocs(kFileSize, blks2, storage_ports);
  auto lbs3 = MockUtil::GenBlkLocs(kFileSize, blks3, storage_ports);
  auto bres = MockUtil::GenReadBlockRsp(0);

  StrictMock<MockRpcStub> mock;
  EXPECT_CALL(mock, BatchGetFile(paths, true))
      .Times(1)
      .WillOnce(Return(finfo1));
  EXPECT_CALL(mock, GetBlockLocations(0, _))
      .Times(2)
      .WillOnce(Return(lbs2))
      .WillOnce(Return(lbs3));
  // 1. read both blocks fail because DN_BLOCK_NOT_EXIST
  EXPECT_CALL(mock,
              ReadBlock(1001, 0, _, _, AnyOf("172.1.1.1", "172.1.1.2"), _, _))
      .Times(4)
      .WillRepeatedly(Return(Status(CFS_ERR_DN_BLOCK_NOT_EXIST)));
  EXPECT_CALL(mock,
              ReadBlock(1002, 0, _, _, AnyOf("172.1.1.3", "172.1.1.4"), _, _))
      .Times(4)
      .WillRepeatedly(Return(Status(CFS_ERR_DN_BLOCK_NOT_EXIST)));

  // 2. read one block success but other fail because DN_BLOCK_NOT_EXIST
  //    this shoud not_allow_partial_read and retry
  EXPECT_CALL(mock, ReadBlock(1001, 0, _, _, "172.1.2.1", _, _))
      .Times(2)
      .WillRepeatedly(WithArgs<2, 3>([](uint64_t off, uint64_t len) {
        return std::make_pair(MockUtil::GenReadBlockRsp(0),
                              MockUtil::GenReadData(off, len));
      }));
  EXPECT_CALL(mock,
              ReadBlock(1002, 0, _, _, AnyOf("172.1.2.3", "172.1.2.4"), _, _))
      .Times(4)
      .WillRepeatedly(Return(Status(CFS_ERR_DN_BLOCK_NOT_EXIST)));
  RpcMock::GetInstance()->SetMockStub(&mock);

  // 3. read both block success
  EXPECT_CALL(mock, ReadBlock(1001, 0, _, _, "172.1.3.1", _, _))
      .Times(2)
      .WillRepeatedly(WithArgs<2, 3>([](uint64_t off, uint64_t len) {
        return std::make_pair(MockUtil::GenReadBlockRsp(0),
                              MockUtil::GenReadData(off, len));
      }));
  EXPECT_CALL(mock, ReadBlock(1002, 0, _, _, "172.1.3.3", _, _))
      .Times(2)
      .WillRepeatedly(WithArgs<2, 3>([](uint64_t off, uint64_t len) {
        return std::make_pair(MockUtil::GenReadBlockRsp(0),
                              MockUtil::GenReadData(off, len));
      }));

  RpcMock::GetInstance()->SetMockStub(&mock);
  std::vector<cfs_kv_buffer_t> keys;
  for (auto& k : keys_str) {
    keys.push_back(cfs_kv_buffer_t{k.data(), k.size()});
  }
  std::vector<cfs_kv_result_t> mget_results;
  mget_results.resize(keys.size());
  int res2 = cfs_kv_mget(fs_, space1.c_str(), keys.size(), keys.data(),
                         mget_results.data());
  EXPECT_EQ(res2, 0);
  for (uint32_t i = 0; i < keys.size(); i++) {
    ASSERT_EQ(mget_results[i].ret, CFS_STATUS_OK);
    EXPECT_NE(mget_results[i].value.data, nullptr);
  }
  ASSERT_EQ(cfs_kv_mfree(fs_, mget_results.data(), mget_results.size()), 0);
  FLAGS_cfs_read_max_retry_missing_block_num = retry_saved;
}

TEST_F(KvUT, ReadAllDnBlkNotExistRetryFail) {
  auto retry_saved = FLAGS_cfs_read_max_retry_missing_block_num;
  FLAGS_cfs_read_max_retry_missing_block_num = 2;
  const uint64_t kFileSize = FLAGS_cfs_read_packet_size * 4;
  std::string space1 = "/KeySpace1";
  std::vector<std::string> keys_str = {"mset0"};
  std::vector<std::string> paths = {"/KeySpace1/mset0"};
  std::vector<MockLocatedBlk> blks1 = {
      {1001,
       0,
       FLAGS_cfs_read_packet_size * 2,
       {"10.1.1.1", "10.1.1.2"},
       {{"10.1.1.1", "172.1.1.1"}, {"10.1.1.2", "172.1.1.2"}},
       {{}, {}},
       {6102, 6101}},
      {1002,
       FLAGS_cfs_read_packet_size * 2,
       FLAGS_cfs_read_packet_size * 2,
       {"10.1.1.3", "10.1.1.4"},
       {{"10.1.1.3", "172.1.1.3"}, {"10.1.1.4", "172.1.1.4"}},
       {{}, {}},
       {6101, 6101}}};
  std::vector<MockLocatedBlk> blks2 = {
      {1001,
       0,
       FLAGS_cfs_read_packet_size * 2,
       {"10.1.2.1", "10.1.2.2"},
       {{"10.1.2.1", "172.1.2.1"}, {"10.1.2.2", "172.1.2.2"}},
       {{}, {}},
       {6102, 6101}},
      {1002,
       FLAGS_cfs_read_packet_size * 2,
       FLAGS_cfs_read_packet_size * 2,
       {"10.1.2.3", "10.1.2.4"},
       {{"10.1.2.3", "172.1.2.3"}, {"10.1.2.4", "172.1.2.4"}},
       {{}, {}},
       {6101, 6101}}};
  std::vector<MockLocatedBlk> blks3 = {
      {1001,
       0,
       FLAGS_cfs_read_packet_size * 2,
       {"10.1.3.1", "10.1.3.2"},
       {{"10.1.3.1", "172.1.3.1"}, {"10.1.3.2", "172.1.3.2"}},
       {{}, {}},
       {6102, 6101}},
      {1002,
       FLAGS_cfs_read_packet_size * 2,
       FLAGS_cfs_read_packet_size * 2,
       {"10.1.3.3", "10.1.3.4"},
       {{"10.1.3.3", "172.1.3.3"}, {"10.1.3.4", "172.1.3.4"}},
       {{}, {}},
       {6101, 6101}}};
  std::vector<uint32_t> storage_ports = {6101, 6102};
  auto finfo1 = MockUtil::GenBatchGetRsp(paths, {blks1}, kFileSize);
  auto lbs2 = MockUtil::GenBlkLocs(kFileSize, blks2, storage_ports);
  auto lbs3 = MockUtil::GenBlkLocs(kFileSize, blks3, storage_ports);
  auto bres = MockUtil::GenReadBlockRsp(0);

  StrictMock<MockRpcStub> mock;
  EXPECT_CALL(mock, BatchGetFile(paths, true))
      .Times(1)
      .WillOnce(Return(finfo1));
  EXPECT_CALL(mock, GetBlockLocations(0, _))
      .Times(2)
      .WillOnce(Return(lbs2))
      .WillOnce(Return(lbs3));
  // 1. read both blocks fail because DN_BLOCK_NOT_EXIST
  EXPECT_CALL(mock,
              ReadBlock(1001, 0, _, _, AnyOf("172.1.1.1", "172.1.1.2"), _, _))
      .Times(4)
      .WillRepeatedly(Return(Status(CFS_ERR_DN_BLOCK_NOT_EXIST)));
  EXPECT_CALL(mock,
              ReadBlock(1002, 0, _, _, AnyOf("172.1.1.3", "172.1.1.4"), _, _))
      .Times(4)
      .WillRepeatedly(Return(Status(CFS_ERR_DN_BLOCK_NOT_EXIST)));

  // 2. read one block success but other fail because DN_BLOCK_NOT_EXIST
  //    this shoud not_allow_partial_read and retry
  EXPECT_CALL(mock, ReadBlock(1001, 0, _, _, "172.1.2.1", _, _))
      .Times(2)
      .WillRepeatedly(WithArgs<2, 3>([](uint64_t off, uint64_t len) {
        return std::make_pair(MockUtil::GenReadBlockRsp(0),
                              MockUtil::GenReadData(off, len));
      }));
  EXPECT_CALL(mock,
              ReadBlock(1002, 0, _, _, AnyOf("172.1.2.3", "172.1.2.4"), _, _))
      .Times(4)
      .WillRepeatedly(Return(Status(CFS_ERR_DN_BLOCK_NOT_EXIST)));
  RpcMock::GetInstance()->SetMockStub(&mock);

  // 3. read one block success but other fail because DN_BLOCK_NOT_EXIST
  EXPECT_CALL(mock,
              ReadBlock(1001, 0, _, _, AnyOf("172.1.3.1", "172.1.3.2"), _, _))
      .Times(4)
      .WillRepeatedly(Return(Status(CFS_ERR_DN_BLOCK_NOT_EXIST)));
  EXPECT_CALL(mock, ReadBlock(1002, 0, _, _, "172.1.3.3", _, _))
      .Times(2)
      .WillRepeatedly(WithArgs<2, 3>([](uint64_t off, uint64_t len) {
        return std::make_pair(MockUtil::GenReadBlockRsp(0),
                              MockUtil::GenReadData(off, len));
      }));

  RpcMock::GetInstance()->SetMockStub(&mock);
  std::vector<cfs_kv_buffer_t> keys;
  for (auto& k : keys_str) {
    keys.push_back(cfs_kv_buffer_t{k.data(), k.size()});
  }
  std::vector<cfs_kv_result_t> mget_results;
  mget_results.resize(keys.size());
  int res2 = cfs_kv_mget(fs_, space1.c_str(), keys.size(), keys.data(),
                         mget_results.data());
  EXPECT_EQ(res2, -1);
  for (uint32_t i = 0; i < keys.size(); i++) {
    ASSERT_EQ(mget_results[i].ret, CFS_STATUS_NO_REPLICA);
    EXPECT_EQ(mget_results[i].value.data, nullptr);
  }
  auto* err = cfs_get_last_error();
  CFSLOG(INFO, "last_err: status={}, msg={}", static_cast<int32_t>(err->status),
         err->message);
  ASSERT_EQ(cfs_kv_mfree(fs_, mget_results.data(), mget_results.size()), 0);
  FLAGS_cfs_read_max_retry_missing_block_num = retry_saved;
}

TEST_F(KvUT, ReadPartKeyFail) {
  auto retry_saved = FLAGS_cfs_dn_rpc_max_retry_time_ms;
  FLAGS_cfs_dn_rpc_max_retry_time_ms = 0;
  std::string space1 = "/ReadPartKeyFail";
  std::vector<std::string> keys_str = {"mget0", "mget1"};
  std::vector<std::string> paths = {"/ReadPartKeyFail/mget0",
                                    "/ReadPartKeyFail/mget1"};
  const uint64_t kFileSize = FLAGS_cfs_read_packet_size * 1;
  std::vector<MockLocatedBlk> blks1 = {
      {1001,
       0,
       FLAGS_cfs_read_packet_size,
       {"10.1.1.1", "10.1.1.2"},
       {{"10.1.1.1", "172.1.1.1"}, {"10.1.1.2", "172.1.1.2"}},
       {{}, {}},
       {6102, 6101}}};
  std::vector<MockLocatedBlk> blks2 = {
      {1002,
       0,
       FLAGS_cfs_read_packet_size,
       {"10.1.1.3", "10.1.1.4"},
       {{"10.1.1.3", "172.1.1.3"}, {"10.1.1.4", "172.1.1.4"}},
       {{}, {}},
       {6102, 6101}}};
  auto finfo1 = MockUtil::GenBatchGetRsp(paths, {blks1, blks2}, kFileSize);

  StrictMock<MockRpcStub> mock;
  EXPECT_CALL(mock, BatchGetFile(paths, true))
      .Times(1)
      .WillOnce(Return(finfo1));
  EXPECT_CALL(mock, GetBlockLocations(_, _)).Times(0);
  EXPECT_CALL(mock, ReadBlock(1001, 0, _, _, "172.1.1.1", _, _))
      .Times(1)
      .WillRepeatedly(WithArgs<2, 3>([](uint64_t off, uint64_t len) {
        return std::make_pair(MockUtil::GenReadBlockRsp(0),
                              MockUtil::GenReadData(off, len));
      }));
  EXPECT_CALL(mock,
              ReadBlock(1002, 0, _, _, AnyOf("172.1.1.3", "172.1.1.4"), _, _))
      .Times(AtLeast(2))
      .WillRepeatedly(Return(Status(CFS_ERR_RPC_CONNECT_FAIL)));
  RpcMock::GetInstance()->SetMockStub(&mock);

  std::vector<cfs_kv_buffer_t> keys;
  for (auto& k : keys_str) {
    keys.push_back(cfs_kv_buffer_t{k.data(), k.size()});
  }
  std::vector<cfs_kv_result_t> mget_results;
  mget_results.resize(keys.size());
  int res2 = cfs_kv_mget(fs_, space1.c_str(), keys.size(), keys.data(),
                         mget_results.data());
  ASSERT_EQ(res2, 1);
  ASSERT_EQ(mget_results[0].ret, CFS_STATUS_OK);
  EXPECT_NE(mget_results[0].value.data, nullptr);
  ASSERT_EQ(mget_results[1].ret, CFS_STATUS_INTERNAL_ERROR);
  EXPECT_EQ(mget_results[1].value.data, nullptr);

  auto* err = cfs_get_last_error();
  CFSLOG(INFO, "last_err: status={}, msg={}", static_cast<int32_t>(err->status),
         err->message);
  EXPECT_EQ(err->status, CFS_STATUS_INTERNAL_ERROR);
  ASSERT_EQ(cfs_kv_mfree(fs_, mget_results.data(), mget_results.size()), 0);
  FLAGS_cfs_dn_rpc_max_retry_time_ms = retry_saved;
}

}  // namespace internal
}  // namespace cfs

int main(int argc, char** argv) {
  testing::InitGoogleTest(&argc, argv);
  return RUN_ALL_TESTS();
}