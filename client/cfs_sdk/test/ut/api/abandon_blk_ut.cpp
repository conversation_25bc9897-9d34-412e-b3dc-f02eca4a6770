#include <gflags/gflags.h>
#include <gmock/gmock.h>
#include <gtest/gtest.h>

#include "cloudfs2/cloudfs2.h"
#include "mock/rpc_mock.h"
#include "test/tools/mock_util.h"

DECLARE_bool(cfs_filesystem_endpoint_resolve_by_dns);
DECLARE_string(cfs_log_type);
DECLARE_string(cfs_byterpc_trans_type_dn);
DECLARE_string(cfs_expect_dn_subnet);
DECLARE_uint64(cfs_write_block_size);
DECLARE_uint32(cfs_dn_rpc_instant_retry_num);
DECLARE_int64(cfs_dn_rpc_retry_interval_ms);
DECLARE_int64(cfs_write_retry_interval_ms);
DECLARE_int64(cfs_write_max_retry_time_ms);
DECLARE_uint32(cfs_renew_lease_interval_sec);
DECLARE_uint32(cfs_ping_block_interval_ms);
DECLARE_uint32(cfs_ping_block_delay_ms);
DECLARE_bool(cfs_enable_slow_node_management);

namespace cfs {
namespace internal {

using ::testing::_;
using ::testing::AnyNumber;
using ::testing::AnyOf;
using ::testing::Args;
using ::testing::AtLeast;
using ::testing::ElementsAre;
using ::testing::Eq;
using ::testing::IsEmpty;
using ::testing::Le;
using ::testing::Lt;
using ::testing::Return;
using ::testing::StrictMock;
using ::testing::UnorderedElementsAre;

class AbandonBlkTest : public testing::Test {
 public:
  static void SetUpTestSuite() {
    FLAGS_cfs_filesystem_endpoint_resolve_by_dns = false;
    FLAGS_cfs_log_type = "stdout";
    FLAGS_cfs_byterpc_trans_type_dn = "KTCP";
    FLAGS_cfs_expect_dn_subnet = "172.1.0.0/16";
    FLAGS_cfs_dn_rpc_retry_interval_ms = 500;
    FLAGS_cfs_write_retry_interval_ms = 1000;
    FLAGS_cfs_write_block_size = kMockBlkSize;
    // disable slow node management to avoid invoke GetDatanodeReport RPC
    FLAGS_cfs_enable_slow_node_management = false;
    fs_ = cfs_connect(nullptr);
    ASSERT_NE(fs_, nullptr);
  }

  static void TearDownTestSuite() {
    ASSERT_EQ(cfs_disconnect(fs_), 0);
  }

 protected:
  void SetUp() override {}

  void TearDown() override {}

 protected:
  static cfs_fs* fs_;
};

cfs_fs* AbandonBlkTest::fs_ = nullptr;

// Write success -> Write fail -> AddBlk -> WriteFail -> AbandonBlk -> AddBlk
// -> Write success
TEST_F(AbandonBlkTest, AbandonBetweenWrite) {
  std::string path1 = "/abs/wfile";
  constexpr uint32_t kPerm = 0666;
  constexpr uint32_t kRep = 2;
  constexpr uint64_t kFileId = 10089;
  constexpr uint64_t kBufLen = 1024 * 1024 * 1;
  constexpr bool kCreateParent = true;
  constexpr uint32_t kCreateFlag = ::cloudfs::CreateFlagProto::CREATE |
                                   ::cloudfs::CreateFlagProto::OVERWRITE |
                                   ::cloudfs::CreateFlagProto::ACC_ASYNC;
  auto create1 = MockUtil::GenCreateRsp(path1, kPerm, kRep, kFileId);
  MockLocatedBlk blk1 = {1001,
                         0,
                         0,
                         {"10.1.1.1", "10.1.1.2"},
                         {{"10.1.1.1", "172.1.1.1"}, {"10.1.1.2", "172.1.1.2"}},
                         {{}, {}},
                         {6101, 6101}};
  MockLocatedBlk blk2 = {1002,
                         kBufLen * 2,
                         0,
                         {"10.1.1.3", "10.1.1.4"},
                         {{"10.1.1.3", "172.1.1.3"}, {"10.1.1.4", "172.1.1.4"}},
                         {{}, {}},
                         {6101, 6101}};
  // since blk2 will be abandoned, blk3's offset is still 'kBufLen * 2'
  MockLocatedBlk blk3 = {1003,
                         kBufLen * 2,
                         0,
                         {"10.1.1.1", "10.1.1.2"},
                         {{"10.1.1.1", "172.1.1.1"}, {"10.1.1.2", "172.1.1.2"}},
                         {{}, {}},
                         {6101, 6101}};
  auto addblk1 = MockUtil::GenAddBlockRsp(blk1);
  auto addblk2 = MockUtil::GenAddBlockRsp(blk2);
  auto addblk3 = MockUtil::GenAddBlockRsp(blk3);
  Status addblk_err2(CFS_ERR_NN_E_APPLICATION,
                     "xx could only be replicated to xx",
                     "java.io.IOException");
  auto createblk1 = MockUtil::GenCreateBlkRsp(6101);
  auto writeblk1 = MockUtil::GenWriteBlkRsp(0);
  Status writeblk_err1(CFS_ERR_RPC_CONNECT_FAIL, "retriable", "");
  Status writeblk_err2(CFS_ERR_DN_BUSY, "non-retriable", "");
  auto sealblk1 = MockUtil::GenSealBlkRsp(0, kBufLen * 2);
  auto sealblk2 = MockUtil::GenSealBlkRsp(0, kBufLen);
  auto finalizeblk1 = MockUtil::GenFinalizeBlkRsp(0);
  auto pingblk1 = MockUtil::GenPingBlkRsp(0);
  auto complete1 = MockUtil::GenCompleteRsp(true);
  std::string kDataBuf = MockUtil::GenReadData(3, kBufLen);

  StrictMock<MockRpcStub> mock;
  EXPECT_CALL(mock, Create(path1, kPerm, kCreateFlag, kRep, kCreateParent))
      .Times(1)
      .WillOnce(Return(create1));

  // Add block1001 succ
  EXPECT_CALL(mock, AddBlock(path1, 0, 0, IsEmpty(), kFileId))
      .Times(1)
      .WillOnce(Return(addblk1));

  // Add block1002 succ for 2nd time
  EXPECT_CALL(mock, AddBlock(path1, 1001, kBufLen * 2, ElementsAre("10.1.1.1"),
                             kFileId))
      .Times(1)
      .WillOnce(Return(addblk2));

  // Add block1003 fail for 1st time and success at 2nd time after clearing
  // exclude_nodes. Note that since blk1002 is abandoned, the pre-blkid when
  // adding blk1003 is 1001 rather than 1002
  EXPECT_CALL(mock, AddBlock(path1, 1001, kBufLen * 2, IsEmpty(), kFileId))
      .Times(1)
      .WillOnce(Return(addblk3));
  EXPECT_CALL(mock,
              AddBlock(path1, 1001, kBufLen * 2,
                       UnorderedElementsAre("10.1.1.1", "10.1.1.3", "10.1.1.4"),
                       kFileId))
      .Times(1)
      .WillOnce(Return(addblk_err2));

  // CFS_O_SYNC_WRITE is not set below, so 'sync' should be false
  EXPECT_CALL(mock, CreateBlock(AnyOf(1001, 1002, 1003), 0, false, _, 6101, _))
      .Times(6)
      .WillRepeatedly(Return(createblk1));

  // blk1001, write succ for 2 times. Then write fail for
  // FLAGS_cfs_dn_rpc_instant_retry_num times.
  // Write to blk1 fail for 172.1.1.1 for the 3rd time
  EXPECT_CALL(mock,
              WriteBlock(1001, 0, _, kBufLen, false, _, _, "172.1.1.1", 6101))
      .With(Args<2, 5>(Eq()))
      .Times(FLAGS_cfs_dn_rpc_instant_retry_num)
      .WillRepeatedly(Return(writeblk_err1));
  // Write to blk1, succ for 2 times
  EXPECT_CALL(mock,
              WriteBlock(1001, 0, _, kBufLen, false, _, _, "172.1.1.1", 6101))
      .With(Args<2, 5>(Eq()))
      .Times(2)
      .WillRepeatedly(Return(writeblk1))
      .RetiresOnSaturation();
  // 172.1.1.2 always success, but its 3rd write data will be discarded by
  // SealBlock
  EXPECT_CALL(mock,
              WriteBlock(1001, 0, _, kBufLen, false, _, _, "172.1.1.2", 6101))
      .With(Args<2, 5>(Eq()))
      .Times(3)
      .WillRepeatedly(Return(writeblk1));

  // blk1002, always write fail.
  EXPECT_CALL(mock, WriteBlock(1002, 0, 0, kBufLen, false, 0, _,
                               AnyOf("172.1.1.3", "172.1.1.4"), 6101))
      .Times(2)
      .WillRepeatedly(Return(writeblk_err2));

  // blk1003, always write success
  EXPECT_CALL(mock, WriteBlock(1003, 0, 0, kBufLen, false, 0, _,
                               AnyOf("172.1.1.1", "172.1.1.2"), 6101))
      .Times(2)
      .WillRepeatedly(Return(writeblk1));

  // Seal blk1001
  EXPECT_CALL(mock, SealBlock(1001, 0, kBufLen * 2,
                              AnyOf("172.1.1.1", "172.1.1.2"), 6101))
      .Times(2)
      .WillRepeatedly(Return(sealblk1));
  // Seal blk1003
  EXPECT_CALL(
      mock, SealBlock(1003, 0, kBufLen, AnyOf("172.1.1.1", "172.1.1.2"), 6101))
      .Times(2)
      .WillRepeatedly(Return(sealblk2));
  // finalize blk1001
  EXPECT_CALL(mock, FinalizeBlock(1001, 0, kBufLen * 2,
                                  AnyOf("172.1.1.1", "172.1.1.2"), 6101))
      .Times(2)
      .WillRepeatedly(Return(finalizeblk1));
  // finalize blk1003
  EXPECT_CALL(mock, FinalizeBlock(1003, 0, kBufLen,
                                  AnyOf("172.1.1.1", "172.1.1.2"), 6101))
      .Times(2)
      .WillRepeatedly(Return(finalizeblk1));

  // Abandon blk1002
  EXPECT_CALL(mock, AbandonBlock(path1, 1002, 0, 0, kFileId))
      .Times(1)
      .WillOnce(Return(::cloudfs::AbandonBlockResponseProto()));

  EXPECT_CALL(mock, Complete(path1, 1003, 0, kBufLen, kFileId))
      .Times(1)
      .WillOnce(Return(complete1));

  EXPECT_CALL(mock, RenewLease("mock_nn_backend"))
      .Times(AnyNumber())
      .WillRepeatedly(Return(::cloudfs::RenewLeaseResponseProto()));
  EXPECT_CALL(mock, PingBlock(AnyOf(1001, 1003), 0, Le(kBufLen * 2), _, 6101))
      .Times(AnyNumber())
      .WillRepeatedly(Return(pingblk1));
  RpcMock::GetInstance()->SetMockStub(&mock);

  cfs_open_option_t open_opts = {
      .flags = CFS_O_WRONLY | CFS_O_CREATE | CFS_O_TRUNC,
      .mode = kPerm,
      .create_parent = kCreateParent,
      .replication = kRep,
      .force = false};
  cfs_file* fd = cfs_open(fs_, path1.c_str(), &open_opts);
  ASSERT_NE(fd, nullptr);
  uint64_t finish_len = 0;
  while (finish_len < kBufLen * 3) {
    int64_t write_res1 = cfs_write(fd, kDataBuf.data(), kBufLen);
    EXPECT_EQ(write_res1, static_cast<int64_t>(kBufLen));
    finish_len += kBufLen;
  }
  ASSERT_EQ(cfs_close(fd), 0);
}

TEST_F(AbandonBlkTest, AbandonEmptyBlkSuc) {
  std::string path1 = "/abs/wfile";
  constexpr uint32_t kPerm = 0666;
  constexpr uint32_t kRep = 2;
  constexpr uint64_t kFileId = 10089;
  constexpr uint64_t kBufLen = 1024 * 1024 * 1;
  constexpr bool kCreateParent = true;
  constexpr uint32_t kCreateFlag = ::cloudfs::CreateFlagProto::CREATE |
                                   ::cloudfs::CreateFlagProto::OVERWRITE |
                                   ::cloudfs::CreateFlagProto::ACC_ASYNC;
  auto create1 = MockUtil::GenCreateRsp(path1, kPerm, kRep, kFileId);
  MockLocatedBlk blk1 = {1001,
                         0,
                         0,
                         {"10.1.1.1", "10.1.1.2"},
                         {{"10.1.1.1", "172.1.1.1"}, {"10.1.1.2", "172.1.1.2"}},
                         {{}, {}},
                         {6101, 6101}};
  // Empty block will be abandoned
  MockLocatedBlk blk2 = {1002, kBufLen * 2, 0, {}, {}, {}, {}};
  MockLocatedBlk blk3 = {1003,
                         kBufLen * 2,
                         0,
                         {"10.1.1.3", "10.1.1.4"},
                         {{"10.1.1.3", "172.1.1.3"}, {"10.1.1.4", "172.1.1.4"}},
                         {{}, {}},
                         {6101, 6101}};
  auto addblk1 = MockUtil::GenAddBlockRsp(blk1);
  auto addblk2 = MockUtil::GenAddBlockRsp(blk2);
  auto addblk3 = MockUtil::GenAddBlockRsp(blk3);
  auto createblk1 = MockUtil::GenCreateBlkRsp(6101);
  auto writeblk1 = MockUtil::GenWriteBlkRsp(0);
  Status writeblk_err2(CFS_ERR_DN_BUSY, "non-retriable", "");
  auto sealblk1 = MockUtil::GenSealBlkRsp(0, kBufLen * 2);
  auto finalizebkl1 = MockUtil::GenFinalizeBlkRsp(0);
  auto pingblk1 = MockUtil::GenPingBlkRsp(0);
  auto complete1 = MockUtil::GenCompleteRsp(true);
  std::string kDataBuf = MockUtil::GenReadData(3, kBufLen);

  StrictMock<MockRpcStub> mock;
  EXPECT_CALL(mock, Create(path1, kPerm, kCreateFlag, kRep, kCreateParent))
      .Times(1)
      .WillOnce(Return(create1));

  // Add block1001 succ
  EXPECT_CALL(mock, AddBlock(path1, 0, 0, IsEmpty(), kFileId))
      .Times(1)
      .WillOnce(Return(addblk1));
  // Add block1002 succ and then abandoned. Add block1003 succ.
  EXPECT_CALL(mock, AddBlock(path1, 1001, kBufLen * 2, ElementsAre("10.1.1.1"),
                             kFileId))
      .Times(2)
      .WillOnce(Return(addblk2))
      .WillOnce(Return(addblk3));
  // Abandon blk1002
  EXPECT_CALL(mock, AbandonBlock(path1, 1002, 0, 0, kFileId))
      .Times(1)
      .WillOnce(Return(::cloudfs::AbandonBlockResponseProto()));

  // CFS_O_SYNC_WRITE is not set below, so 'sync' should be false
  EXPECT_CALL(mock, CreateBlock(AnyOf(1001, 1003), 0, false, _, 6101, _))
      .Times(4)
      .WillRepeatedly(Return(createblk1));

  // Write to blk1 of 172.1.1.1, succ for 2 times and then fail for 3rd time
  EXPECT_CALL(mock,
              WriteBlock(1001, 0, _, kBufLen, false, _, _, "172.1.1.1", 6101))
      .With(Args<2, 5>(Eq()))
      .Times(3)
      .WillOnce(Return(writeblk1))
      .WillOnce(Return(writeblk1))
      .WillRepeatedly(Return(writeblk_err2));
  // 172.1.1.2 always success, but its 3rd write data will be discarded by
  // SealBlock
  EXPECT_CALL(mock,
              WriteBlock(1001, 0, _, kBufLen, false, _, _, "172.1.1.2", 6101))
      .With(Args<2, 5>(Eq()))
      .Times(3)
      .WillRepeatedly(Return(writeblk1));

  // blk1003, always write success
  EXPECT_CALL(mock, WriteBlock(1003, 0, _, kBufLen, false, _, _,
                               AnyOf("172.1.1.3", "172.1.1.4"), 6101))
      .With(Args<2, 5>(Eq()))
      .Times(4)
      .WillRepeatedly(Return(writeblk1));

  // Seal blk1001
  EXPECT_CALL(mock, SealBlock(1001, 0, kBufLen * 2,
                              AnyOf("172.1.1.1", "172.1.1.2"), 6101))
      .Times(2)
      .WillRepeatedly(Return(sealblk1));
  // Seal blk1003
  EXPECT_CALL(mock, SealBlock(1003, 0, kBufLen * 2,
                              AnyOf("172.1.1.3", "172.1.1.4"), 6101))
      .Times(2)
      .WillRepeatedly(Return(sealblk1));

  // finazlie blk1001
  EXPECT_CALL(mock, FinalizeBlock(1001, 0, kBufLen * 2,
                                  AnyOf("172.1.1.1", "172.1.1.2"), 6101))
      .Times(2)
      .WillRepeatedly(Return(finalizebkl1));
  // finazlie blk1003
  EXPECT_CALL(mock, FinalizeBlock(1003, 0, kBufLen * 2,
                                  AnyOf("172.1.1.3", "172.1.1.4"), 6101))
      .Times(2)
      .WillRepeatedly(Return(finalizebkl1));

  EXPECT_CALL(mock, Complete(path1, 1003, 0, kBufLen * 2, kFileId))
      .Times(1)
      .WillOnce(Return(complete1));

  EXPECT_CALL(mock, RenewLease("mock_nn_backend"))
      .Times(AnyNumber())
      .WillRepeatedly(Return(::cloudfs::RenewLeaseResponseProto()));
  EXPECT_CALL(mock, PingBlock(AnyOf(1001, 1003), 0, Le(kBufLen * 2), _, 6101))
      .Times(AnyNumber())
      .WillRepeatedly(Return(pingblk1));
  RpcMock::GetInstance()->SetMockStub(&mock);

  cfs_open_option_t open_opts = {
      .flags = CFS_O_WRONLY | CFS_O_CREATE | CFS_O_TRUNC,
      .mode = kPerm,
      .create_parent = kCreateParent,
      .replication = kRep,
      .force = false};
  cfs_file* fd = cfs_open(fs_, path1.c_str(), &open_opts);
  ASSERT_NE(fd, nullptr);
  uint64_t finish_len = 0;
  while (finish_len < kBufLen * 4) {
    int64_t write_res1 = cfs_write(fd, kDataBuf.data(), kBufLen);
    EXPECT_EQ(write_res1, static_cast<int64_t>(kBufLen));
    finish_len += kBufLen;
  }
  ASSERT_EQ(cfs_close(fd), 0);
}

TEST_F(AbandonBlkTest, DelayAbandon) {
  auto cfs_write_max_retry_time_ms_saved = FLAGS_cfs_write_max_retry_time_ms;
  FLAGS_cfs_write_max_retry_time_ms = 2000;
  std::string path1 = "/abs/wfile";
  constexpr uint32_t kPerm = 0666;
  constexpr uint32_t kRep = 1;
  constexpr uint64_t kFileId = 10089;
  constexpr uint64_t kBufLen = 1024 * 1024 * 1;
  constexpr bool kCreateParent = true;
  constexpr uint32_t kCreateFlag = ::cloudfs::CreateFlagProto::CREATE |
                                   ::cloudfs::CreateFlagProto::OVERWRITE |
                                   ::cloudfs::CreateFlagProto::ACC_ASYNC;
  auto create1 = MockUtil::GenCreateRsp(path1, kPerm, kRep, kFileId);
  MockLocatedBlk blk1 = {
      1001, 0, 0, {"10.1.1.1"}, {{"10.1.1.1", "172.1.1.1"}}, {{}}, {6101}};
  auto addblk1 = MockUtil::GenAddBlockRsp(blk1);
  auto createblk1 = MockUtil::GenCreateBlkRsp(6101);
  std::string kDataBuf = MockUtil::GenReadData(3, kBufLen);

  StrictMock<MockRpcStub> mock;
  EXPECT_CALL(mock, Create(path1, kPerm, kCreateFlag, kRep, kCreateParent))
      .Times(1)
      .WillOnce(Return(create1));
  EXPECT_CALL(mock, AddBlock(path1, 0, 0, _, kFileId))
      .Times(AtLeast(2))
      .WillRepeatedly(Return(addblk1));
  EXPECT_CALL(mock, CreateBlock(1001, 0, _, _, 6101, _))
      .Times(AtLeast(2))
      .WillRepeatedly(Return(Status(CFS_ERR_DN_BUSY, "non-retriable")));
  EXPECT_CALL(mock, AbandonBlock(path1, 1001, 0, 0, kFileId))
      .Times(AtLeast(2))
      .WillRepeatedly(Return(::cloudfs::AbandonBlockResponseProto()));
  EXPECT_CALL(mock, Complete(path1, 0, 0, 0, kFileId))
      .Times(1)
      .WillOnce(Return(MockUtil::GenCompleteRsp(true)));
  EXPECT_CALL(mock, RenewLease("mock_nn_backend"))
      .Times(AnyNumber())
      .WillRepeatedly(Return(::cloudfs::RenewLeaseResponseProto()));
  RpcMock::GetInstance()->SetMockStub(&mock);

  cfs_open_option_t open_opts = {
      .flags = CFS_O_WRONLY | CFS_O_CREATE | CFS_O_TRUNC,
      .mode = kPerm,
      .create_parent = kCreateParent,
      .replication = kRep,
      .force = false};
  cfs_file* fd = cfs_open(fs_, path1.c_str(), &open_opts);
  ASSERT_NE(fd, nullptr);
  int64_t write_res1 = cfs_write(fd, kDataBuf.data(), kBufLen);
  EXPECT_EQ(write_res1, -1);
  EXPECT_EQ(cfs_close(fd), 0);
  FLAGS_cfs_write_max_retry_time_ms = cfs_write_max_retry_time_ms_saved;
}

}  // namespace internal
}  // namespace cfs

int main(int argc, char** argv) {
  testing::InitGoogleTest(&argc, argv);
  return RUN_ALL_TESTS();
}