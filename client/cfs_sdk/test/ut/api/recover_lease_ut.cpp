#include <gflags/gflags.h>
#include <gmock/gmock.h>
#include <gtest/gtest.h>

#include "cloudfs2/cloudfs2.h"
#include "impl/cfs_impl.h"
#include "mock/rpc_mock.h"
#include "test/tools/mock_util.h"

DECLARE_bool(cfs_filesystem_endpoint_resolve_by_dns);
DECLARE_string(cfs_log_type);
// DECLARE_string(cfs_log_level);
DECLARE_string(cfs_byterpc_trans_type_dn);
DECLARE_string(cfs_expect_dn_subnet);
DECLARE_uint64(cfs_write_block_size);
DECLARE_int64(cfs_recover_lease_timeout_ms);
DECLARE_int64(cfs_recover_lease_retry_interval_ms);
DECLARE_bool(cfs_enable_slow_node_management);

namespace cfs {
namespace internal {

using ::testing::_;
using ::testing::AnyNumber;
using ::testing::AnyOf;
using ::testing::Args;
using ::testing::AtLeast;
using ::testing::Eq;
using ::testing::IsEmpty;
using ::testing::Le;
using ::testing::Lt;
using ::testing::Return;
using ::testing::StrictMock;

class RecoverLeaseTest : public testing::Test {
 public:
  static void SetUpTestSuite() {
    FLAGS_cfs_filesystem_endpoint_resolve_by_dns = false;
    FLAGS_cfs_log_type = "stdout";
    // FLAGS_cfs_log_level = "debug";
    FLAGS_cfs_byterpc_trans_type_dn = "KTCP";
    FLAGS_cfs_expect_dn_subnet = "172.1.0.0/16";
    FLAGS_cfs_write_block_size = kMockBlkSize;
    // disable slow node management to avoid invoke GetDatanodeReport RPC
    FLAGS_cfs_enable_slow_node_management = false;
    fs_ = cfs_connect(nullptr);
    ASSERT_NE(fs_, nullptr);
  }

  static void TearDownTestSuite() {
    ASSERT_EQ(::cfs_disconnect(fs_), 0);
  }

 protected:
  void SetUp() override {}

  void TearDown() override {}

 protected:
  static cfs_fs* fs_;
};

cfs_fs* RecoverLeaseTest::fs_ = nullptr;

TEST_F(RecoverLeaseTest, AppendNoForceFail) {
  std::string path1 = "/abs/wfile";
  constexpr uint32_t kPerm = 0666;
  constexpr uint32_t kRep = 2;
  constexpr uint64_t kFileId = 10089;
  constexpr uint64_t kBufLen = 1024 * 1024 * 1;
  constexpr bool kCreateParent = false;
  auto finfo1 = MockUtil::GenFileInfo(path1, FileInfo::IS_FILE, kBufLen, kPerm,
                                      kRep, kFileId);
  // set_under_construction_client_name to non-empty to simulate that this file
  // is being written by another client.
  finfo1.set_under_construction_client_name("client-abcd");

  StrictMock<MockRpcStub> mock;
  EXPECT_CALL(mock, GetFileInfo(path1, false))
      .Times(1)
      .WillOnce(Return(finfo1));
  RpcMock::GetInstance()->SetMockStub(&mock);

  cfs_open_option_t open_opts = {
      .flags = CFS_O_WRONLY | CFS_O_CREATE | CFS_O_APPEND,
      .mode = kPerm,
      .create_parent = kCreateParent,
      .replication = kRep,
      .force = false};
  cfs_file* fd = ::cfs_open(fs_, path1.c_str(), &open_opts);
  ASSERT_EQ(fd, nullptr);
  EXPECT_EQ(cfs_get_last_error()->status, CFS_STATUS_BUSY);
}

TEST_F(RecoverLeaseTest, AppendCommitedBlockOnceSucc) {
  std::string path1 = "/abs/wfile";
  constexpr uint32_t kPerm = 0666;
  constexpr uint32_t kRep = 2;
  constexpr uint64_t kFileId = 10089;
  constexpr uint64_t kBufLen = 1024 * 1024 * 1;
  constexpr bool kCreateParent = false;
  auto finfo1 = MockUtil::GenFileInfo(path1, FileInfo::IS_FILE, kBufLen, kPerm,
                                      kRep, kFileId);
  // set_under_construction_client_name to non-empty to simulate that this file
  // is being written by another client.
  finfo1.set_under_construction_client_name("client-abcd");
  // after force-append, this file length is changed
  auto finfo2 = MockUtil::GenFileInfo(path1, FileInfo::IS_FILE, kBufLen * 2,
                                      kPerm, kRep, kFileId);
  auto append1 = MockUtil::GenAppendRsp(nullptr);
  auto complete1 = MockUtil::GenCompleteRsp(true);

  StrictMock<MockRpcStub> mock;
  EXPECT_CALL(mock, GetFileInfo(path1, false))
      .Times(2)
      .WillOnce(Return(finfo1))
      .WillOnce(Return(finfo2));
  EXPECT_CALL(mock, Append(path1)).Times(1).WillOnce(Return(append1));
  EXPECT_CALL(mock, Complete(path1, 0, 0, 0, kFileId))
      .Times(1)
      .WillOnce(Return(complete1));
  EXPECT_CALL(mock, RenewLease("mock_nn_backend"))
      .Times(AnyNumber())
      .WillRepeatedly(Return(::cloudfs::RenewLeaseResponseProto()));
  // We do not write to this file, so it should never PingBlock
  EXPECT_CALL(mock, PingBlock(_, _, _, _, _)).Times(0);
  RpcMock::GetInstance()->SetMockStub(&mock);

  cfs_open_option_t open_opts = {
      .flags = CFS_O_WRONLY | CFS_O_CREATE | CFS_O_APPEND,
      .mode = kPerm,
      .create_parent = kCreateParent,
      .replication = kRep,
      .force = true};  // force-open
  cfs_file* fd = ::cfs_open(fs_, path1.c_str(), &open_opts);
  ASSERT_NE(fd, nullptr);

  EXPECT_EQ(fd->file_handle->GetFileLength(), kBufLen * 2);
  EXPECT_EQ(fd->file_handle->GetWriteCursor(), kBufLen * 2);
  ASSERT_EQ(::cfs_close(fd), 0);
}

TEST_F(RecoverLeaseTest, AppendUncommitedBlockOnceSucc) {
  std::string path1 = "/abs/wfile";
  constexpr uint32_t kPerm = 0666;
  constexpr uint32_t kRep = 2;
  constexpr uint64_t kFileId = 10089;
  constexpr uint64_t kBufLen = 1024 * 1024 * 1;
  constexpr bool kCreateParent = false;
  MockLocatedBlk blk1 = {1001,
                         0,        // offset
                         kBufLen,  // length
                         {"10.1.1.1", "10.1.1.2"},
                         {{"10.1.1.1", "172.1.1.1"}, {"10.1.1.2", "172.1.1.2"}},
                         {{}, {}},
                         {6101, 6101}};
  MockLocatedBlk blk2 = {1002,
                         kBufLen + 2,
                         0,  // length
                         {"10.1.1.1", "10.1.1.3"},
                         {{"10.1.1.1", "172.1.1.1"}, {"10.1.1.3", "172.1.1.3"}},
                         {{}, {}},
                         {6101, 6101}};
  auto finfo1 = MockUtil::GenFileInfo(path1, FileInfo::IS_FILE, kBufLen, kPerm,
                                      kRep, kFileId);
  // set_under_construction_client_name to non-empty to simulate that this file
  // is being written by another client.
  finfo1.set_under_construction_client_name("client-abcd");
  auto finfo2 = MockUtil::GenFileInfo(path1, FileInfo::IS_FILE, kBufLen, kPerm,
                                      kRep, kFileId);
  auto append1 = MockUtil::GenAppendRsp(&blk1);
  *(append1.mutable_fs()) = std::move(finfo2);
  auto complete1 = MockUtil::GenCompleteRsp(true);
  auto addblk1 = MockUtil::GenAddBlockRsp(blk2);
  auto createblk1 = MockUtil::GenCreateBlkRsp(6101);
  auto finalizeblk1 = MockUtil::GenFinalizeBlkRsp(0);

  StrictMock<MockRpcStub> mock;
  EXPECT_CALL(mock, GetFileInfo(path1, false))
      .Times(1)
      .WillOnce(Return(finfo1));
  EXPECT_CALL(mock, Append(path1)).Times(1).WillOnce(Return(append1));
  EXPECT_CALL(mock, Complete(path1, 1001, 0, kBufLen + 2, kFileId))
      .Times(1)
      .WillOnce(Return(complete1));
  EXPECT_CALL(mock, RenewLease("mock_nn_backend"))
      .Times(AnyNumber())
      .WillRepeatedly(Return(::cloudfs::RenewLeaseResponseProto()));

  EXPECT_CALL(mock, CreateBlock(1002, 0, false, _, 6101, _))
      .Times(2)
      .WillRepeatedly(Return(createblk1));
  EXPECT_CALL(mock, AddBlock(path1, 1001, kBufLen + 2, IsEmpty(), kFileId))
      .Times(1)
      .WillOnce(Return(addblk1));
  EXPECT_CALL(mock, AbandonBlock(path1, 1002, 0, 0, kFileId))
      .Times(1)
      .WillOnce(Return(::cloudfs::AbandonBlockResponseProto()));
  // Seal blk1001 return 2 lengths, and we will use the shorter one
  EXPECT_CALL(mock,
              SealBlock(1001, 0, 0, AnyOf("172.1.1.1", "172.1.1.2"), 6101))
      .Times(2)
      .WillOnce(Return(MockUtil::GenSealBlkRsp(0, kBufLen + 2)))
      .WillOnce(Return(MockUtil::GenSealBlkRsp(0, kBufLen + 4)));
  // finalize blk1001
  EXPECT_CALL(mock, FinalizeBlock(1001, 0, kBufLen + 2,
                                  AnyOf("172.1.1.1", "172.1.1.2"), 6101))
      .Times(2)
      .WillRepeatedly(Return(finalizeblk1));

  // We do not write to this file, so it should never PingBlock
  EXPECT_CALL(mock, PingBlock(_, _, _, _, _)).Times(0);
  RpcMock::GetInstance()->SetMockStub(&mock);

  cfs_open_option_t open_opts = {
      .flags = CFS_O_WRONLY | CFS_O_CREATE | CFS_O_APPEND,
      .mode = kPerm,
      .create_parent = kCreateParent,
      .replication = kRep,
      .force = true};  // force-open
  cfs_file* fd = ::cfs_open(fs_, path1.c_str(), &open_opts);
  ASSERT_NE(fd, nullptr);

  EXPECT_EQ(fd->file_handle->GetFileLength(), kBufLen + 2);
  EXPECT_EQ(fd->file_handle->GetWriteCursor(), kBufLen + 2);
  ASSERT_EQ(::cfs_close(fd), 0);
}

}  // namespace internal
}  // namespace cfs

int main(int argc, char** argv) {
  testing::InitGoogleTest(&argc, argv);
  return RUN_ALL_TESTS();
}
