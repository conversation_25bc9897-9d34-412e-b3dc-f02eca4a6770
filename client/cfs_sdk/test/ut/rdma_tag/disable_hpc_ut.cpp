#include <gflags/gflags.h>
#include <gmock/gmock.h>
#include <gtest/gtest.h>

#include "cloudfs2/cloudfs2.h"
#include "mock/rpc_mock.h"
#include "test/tools/mock_util.h"
#include "test/tools/test_util.h"

DECLARE_bool(cfs_filesystem_endpoint_resolve_by_dns);
DECLARE_string(cfs_log_type);
DECLARE_string(cfs_byterpc_trans_type_dn);
DECLARE_string(cfs_expect_dn_subnet);
DECLARE_uint64(cfs_write_block_size);
DECLARE_bool(cfs_enable_multi_data_nic);
DECLARE_bool(cfs_enable_hpc);
DECLARE_string(cfs_rdma_tag);
DECLARE_string(cfs_vpc_tag);

namespace cfs {
namespace internal {

using ::testing::_;
using ::testing::AnyNumber;
using ::testing::AnyOfArray;
using ::testing::Ge;
using ::testing::IsEmpty;
using ::testing::Return;
using ::testing::StrictMock;
using ::testing::WithArgs;

struct TagParam {
  std::string name;
  std::vector<std::vector<std::string>> opt_ips;
  std::vector<std::vector<std::string>> opt_tags;
  std::vector<std::string> expect_ips;
  std::vector<std::string> local_nics;
};

class RdmaTag2MatchTest : public testing::TestWithParam<TagParam> {
 public:
  static void SetUpTestSuite() {
    FLAGS_cfs_filesystem_endpoint_resolve_by_dns = false;
    FLAGS_cfs_log_type = "stdout";
    FLAGS_cfs_byterpc_trans_type_dn = "KTCP";
    FLAGS_cfs_expect_dn_subnet = "********/16";
    FLAGS_cfs_write_block_size = kMockBlkSize;
    FLAGS_cfs_enable_multi_data_nic = true;
    fs_ = cfs_connect(nullptr);
    ASSERT_NE(fs_, nullptr);
  }

  static void TearDownTestSuite() {
    ASSERT_EQ(cfs_disconnect(fs_), 0);
  }

 protected:
  void SetUp() override {
    FLAGS_cfs_enable_hpc = false;
    FLAGS_cfs_rdma_tag = "";
    FLAGS_cfs_vpc_tag = "";
  }

  void TearDown() override {}

 protected:
  static cfs_fs* fs_;
};

cfs_fs* RdmaTag2MatchTest::fs_ = nullptr;

TEST_P(RdmaTag2MatchTest, Write) {
  FLAGS_cfs_rdma_tag = "hpc123";
  FLAGS_cfs_vpc_tag = "vpc-11";

  auto arg = GetParam();
  std::string path1 = "/abs/wfile";
  constexpr uint32_t kPerm = 0666;
  constexpr uint32_t kRep = 2;
  constexpr uint64_t kFileId = 10089;
  constexpr uint64_t kBufLen = 1024 * 1024 * 2;
  constexpr bool kCreateParent = true;
  constexpr uint32_t kCreateFlag = ::cloudfs::CreateFlagProto::CREATE |
                                   ::cloudfs::CreateFlagProto::APPEND |
                                   ::cloudfs::CreateFlagProto::ACC_APPENDABLE |
                                   ::cloudfs::CreateFlagProto::ACC_ASYNC;
  CFS_CHECK_EQ(arg.opt_ips.size(), 2);
  CFS_CHECK_EQ(arg.opt_tags.size(), 2);
  CFS_CHECK_EQ(arg.opt_ips[0].size(), 2);
  MockLocatedBlk blk1 = {1001,
                         0,
                         0,
                         {arg.opt_ips[0][0], arg.opt_ips[1][0]},
                         {arg.opt_ips[0], arg.opt_ips[1]},
                         {arg.opt_tags[0], arg.opt_tags[1]},
                         {6101, 6102}};
  auto create1 = MockUtil::GenCreateRsp(path1, kPerm, kRep, kFileId);
  auto addblk1 = MockUtil::GenAddBlockRsp(blk1);
  // port may change after CreateBlock
  auto createblk11 = MockUtil::GenCreateBlkRsp(6102);
  auto writeblk1 = MockUtil::GenWriteBlkRsp(0);
  auto sealblk1 = MockUtil::GenSealBlkRsp(0, kBufLen);
  auto finalizeblk1 = MockUtil::GenFinalizeBlkRsp(0);
  auto pingblk1 = MockUtil::GenPingBlkRsp(0);
  auto complete1 = MockUtil::GenCompleteRsp(true);
  std::string kDataBuf = MockUtil::GenReadData(3, kBufLen);

  StrictMock<MockRpcStub> mock;
  EXPECT_CALL(mock, GetFileInfo(path1, false))
      .Times(1)
      .WillOnce(Return(Status(CFS_ERR_FILE_NOT_FOUND, "not found ctx", "")));
  EXPECT_CALL(mock, Create(path1, kPerm, kCreateFlag, kRep, kCreateParent))
      .Times(1)
      .WillOnce(Return(create1));
  EXPECT_CALL(mock, AddBlock(path1, 0, 0, IsEmpty(), kFileId))
      .Times(1)
      .WillOnce(Return(addblk1));
  EXPECT_CALL(mock, CreateBlock(1001, 0, _, arg.expect_ips[0], _,
                                TestUtil::NicName2IpStr(arg.local_nics[0])))
      .Times(1)
      .WillOnce(Return(createblk11));
  EXPECT_CALL(mock, CreateBlock(1001, 0, _, arg.expect_ips[1], _,
                                TestUtil::NicName2IpStr(arg.local_nics[1])))
      .Times(1)
      .WillOnce(Return(createblk11));
  EXPECT_CALL(mock, WriteBlock(1001, 0, 0, kBufLen, false, 0, _,
                               AnyOfArray(arg.expect_ips), _))
      .Times(2)
      .WillRepeatedly(Return(writeblk1));
  EXPECT_CALL(mock, SealBlock(1001, 0, _, AnyOfArray(arg.expect_ips), _))
      .Times(2)
      .WillRepeatedly(Return(sealblk1));
  EXPECT_CALL(mock, FinalizeBlock(1001, 0, _, AnyOfArray(arg.expect_ips), _))
      .Times(2)
      .WillRepeatedly(Return(finalizeblk1));
  EXPECT_CALL(mock, Complete(path1, 1001, 0, kBufLen, kFileId))
      .Times(1)
      .WillOnce(Return(complete1));
  EXPECT_CALL(mock, RenewLease("mock_nn_backend"))
      .Times(AnyNumber())
      .WillRepeatedly(Return(::cloudfs::RenewLeaseResponseProto()));
  EXPECT_CALL(mock, PingBlock(1001, 0, _, AnyOfArray(arg.expect_ips), _))
      .Times(AnyNumber())
      .WillRepeatedly(Return(pingblk1));
  RpcMock::GetInstance()->SetMockStub(&mock);

  cfs_open_option_t open_opts = {
      .flags = CFS_O_WRONLY | CFS_O_CREATE | CFS_O_APPEND,
      .mode = kPerm,
      .create_parent = kCreateParent,
      .replication = kRep,
      .force = false};
  cfs_file* fd = cfs_open(fs_, path1.c_str(), &open_opts);
  ASSERT_NE(fd, nullptr);
  int64_t write_res1 = cfs_write(fd, kDataBuf.data(), kBufLen);
  EXPECT_EQ(write_res1, static_cast<int64_t>(kBufLen));
  ASSERT_EQ(cfs_close(fd), 0);
}

TEST_P(RdmaTag2MatchTest, Read) {
  FLAGS_cfs_rdma_tag = "hpc123";
  FLAGS_cfs_vpc_tag = "vpc-11";

  auto arg = GetParam();
  std::string path1 = "/abs/rfile";
  constexpr uint64_t kFileSize = kMockBlkSize;
  constexpr uint64_t kBufLen = 10000;

  CFS_CHECK_GE(arg.opt_ips.size(), 1);
  CFS_CHECK_GE(arg.opt_tags.size(), 1);
  CFS_CHECK_EQ(arg.opt_ips[0].size(), 2);
  std::vector<MockLocatedBlk> blks = {{1001,
                                       0,
                                       kMockBlkSize,
                                       {arg.opt_ips[0][0]},
                                       {arg.opt_ips[0]},
                                       {arg.opt_tags[0]},
                                       {6101}}};
  std::vector<uint32_t> storage_ports = {6101, 6102, 6103};
  auto finfo1 = MockUtil::GenFileInfo(path1, FileInfo::IS_FILE, kFileSize);
  auto lbs = MockUtil::GenBlkLocs(kFileSize, blks, storage_ports);
  // auto binfo1 = MockUtil::GenReplicaInfo(6101);
  auto bres = MockUtil::GenReadBlockRsp(0);

  StrictMock<MockRpcStub> mock;
  EXPECT_CALL(mock, GetFileInfo(path1, true)).Times(1).WillOnce(Return(finfo1));
  EXPECT_CALL(mock, GetBlockLocations(0, Ge(kBufLen)))
      .Times(1)
      .WillOnce(Return(lbs));
  EXPECT_CALL(mock, ReadBlock(1001, 0, 0, kBufLen, arg.expect_ips[0], 6101,
                              TestUtil::NicName2IpStr(arg.local_nics[0])))
      .Times(1)
      .WillOnce(WithArgs<2, 3>([&bres](uint64_t off, uint64_t len) {
        return std::make_pair(bres, MockUtil::GenReadData(off, len));
      }));

  RpcMock::GetInstance()->SetMockStub(&mock);

  cfs_open_option_t open_opts;
  open_opts.flags = CFS_O_RDONLY;
  cfs_file* fd = cfs_open(fs_, path1.c_str(), &open_opts);
  ASSERT_NE(fd, nullptr);

  std::string buf;
  buf.resize(kBufLen);
  int64_t res1 = cfs_pread(fd, buf.data(), buf.size(), 0);
  EXPECT_EQ(res1, kBufLen);
  ASSERT_EQ(cfs_close(fd), 0);
}

// No matter it's rdma or vpc, when cfs_enable_hpc == false, it always uses
// dn_subnet
const TagParam kSuccParamList[] = {
    {"2rdma",
     {{"********", "********"}, {"********", "********"}},
     {{"vpc-11", "rdma-hpc123"}, {"vpc-11", "rdma-hpc123"}},
     {"********", "********"},
     {"eth0", "eth0"}},
    {"1vpc1rdma",
     {{"********", "********"}, {"********", "********"}},
     {{"vpc-11", "rdma-hpc222"}, {"vpc-11", "rdma-hpc123"}},
     {"********", "********"},
     {"eth0", "eth0"}},
    {"1rdma1vpc",
     {{"********", "********"}, {"********", "********"}},
     {{"vpc-11", "rdma-hpc123"}, {"vpc-11", "rdma-hpc222"}},
     {"********", "********"},
     {"eth0", "eth0"}},
    {"dn_subnet1",
     {{"********", "********"}, {"********", "********"}},
     {{"vpc-22", "rdma-hpc222"}, {"vpc-22", "rdma-hpc222"}},
     {"********", "********"},
     {"eth0", "eth0"}},
    {"dn_subnet2",
     {{"********", "********"}, {"********", "********"}},
     {{"", ""}, {"", ""}},
     {"********", "********"},
     {"eth0", "eth0"}},
    {"dn_subnet3",
     {{"********", "********"}, {"********", "********"}},
     {{}, {}},
     {"********", "********"},
     {"eth0", "eth0"}}};

INSTANTIATE_TEST_SUITE_P(
    DifferentIpTag, RdmaTag2MatchTest, testing::ValuesIn(kSuccParamList),
    [](const testing::TestParamInfo<RdmaTag2MatchTest::ParamType>& info) {
      return info.param.name;
    });

}  // namespace internal
}  // namespace cfs

int main(int argc, char** argv) {
  testing::InitGoogleTest(&argc, argv);
  return RUN_ALL_TESTS();
}
