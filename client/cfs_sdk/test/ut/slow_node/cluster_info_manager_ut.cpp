#include <gflags/gflags.h>
#include <gmock/gmock.h>
#include <gtest/gtest.h>

#include <vector>

#include "cloudfs2/cloudfs2.h"
#include "common/logger.h"
#include "io/cluster_info_manager.h"
#include "mock/rpc_mock.h"
#include "test/tools/mock_util.h"

DECLARE_bool(cfs_filesystem_endpoint_resolve_by_dns);
DECLARE_string(cfs_log_type);
DECLARE_string(cfs_byterpc_trans_type_dn);
DECLARE_string(cfs_expect_dn_subnet);
DECLARE_uint64(cfs_get_block_loc_batch_size);
DECLARE_uint32(cfs_read_packet_size);
DECLARE_int64(cfs_dn_rpc_retry_interval_ms);
DECLARE_uint32(cfs_get_cluster_info_interval_s);
DECLARE_bool(cfs_enable_slow_node_management);
DECLARE_int64(cfs_nn_rpc_max_retry_time_ms);

namespace cfs {
namespace internal {

using ::testing::_;
using ::testing::AnyNumber;
using ::testing::AnyOf;
using ::testing::AnyOfArray;
using ::testing::Args;
using ::testing::AtLeast;
using ::testing::Le;
using ::testing::Lt;
using ::testing::Return;
using ::testing::StrictMock;
using ::testing::WithArgs;

class ClusterInfoManagerTest : public testing::Test {
 public:
  static void SetUpTestSuite() {
    FLAGS_cfs_filesystem_endpoint_resolve_by_dns = false;
    FLAGS_cfs_log_type = "stdout";
    FLAGS_cfs_byterpc_trans_type_dn = "KTCP";
    FLAGS_cfs_expect_dn_subnet = "172.1.0.0/16";
    FLAGS_cfs_dn_rpc_retry_interval_ms = 500;
    FLAGS_cfs_get_cluster_info_interval_s = 1;
    FLAGS_cfs_enable_slow_node_management = true;
    FLAGS_cfs_nn_rpc_max_retry_time_ms = 2000;
    fs_ = cfs_connect(nullptr);
    ASSERT_NE(fs_, nullptr);
  }

  static void TearDownTestSuite() {
    ASSERT_EQ(cfs_disconnect(fs_), 0);
    ASSERT_EQ(cfs_finalize(), 0);
  }
  void SetUp() override {}
  void TearDown() override {}

 protected:
  static cfs_fs* fs_;
};

cfs_fs* ClusterInfoManagerTest::fs_ = nullptr;

TEST_F(ClusterInfoManagerTest, GetClusterInfoFailed) {
  Status err1(CFS_ERR_NN_E_RPC_SERVER, "mock err ctx", "kMockException");
  StrictMock<MockRpcStub> mock;
  EXPECT_CALL(mock, GetDatanodeReport(_))
      .Times(AtLeast(1))
      .WillRepeatedly(Return(err1));
  /// EXPECT_CALL(mock, GetFileInfo(path1,
  /// true)).Times(1).WillOnce(Return(err1));
  RpcMock::GetInstance()->SetMockStub(&mock);
  CFSLOG(INFO, "start sleep {}", FLAGS_cfs_get_cluster_info_interval_s);
  std::this_thread::sleep_for(
      std::chrono::seconds(FLAGS_cfs_get_cluster_info_interval_s + 1));
  auto count = ClusterInfoManager::GetInstance()->GetDataNodeCount();
  EXPECT_EQ(count, 0);
}

TEST_F(ClusterInfoManagerTest, GetClusterInfoSuccess) {
  std::vector<DatanodeInfo> node_list;
  uint32_t node_count = 3;
  for (uint32_t i = 0; i < node_count; i++) {
    DatanodeInfo node;
    node.SetByteRpcPortReliable(true);
    node.SetIpAddr("172.1.1." + std::to_string(i));
    node.SetUuid("uuid" + std::to_string(i));
    node.SetHostName("hostname" + std::to_string(i));
    node_list.push_back(node);
  }
  auto data_rsp = MockUtil::GenGetDatanodeReportRsp(node_list);

  StrictMock<MockRpcStub> mock;
  EXPECT_CALL(mock, GetDatanodeReport(::cloudfs::DatanodeReportTypeProto::LIVE))
      .Times(::testing::AtLeast(1))
      .WillRepeatedly(Return(data_rsp));
  /// EXPECT_CALL(mock, GetFileInfo(path1,
  /// true)).Times(1).WillOnce(Return(err1));
  RpcMock::GetInstance()->SetMockStub(&mock);
  CFSDLOG(INFO, "start sleep {}", FLAGS_cfs_get_cluster_info_interval_s);
  std::this_thread::sleep_for(
      std::chrono::seconds(FLAGS_cfs_get_cluster_info_interval_s + 1));
  auto count = ClusterInfoManager::GetInstance()->GetDataNodeCount();
  EXPECT_EQ(count, node_count);
  for (uint32_t i = 0; i < node_count; i++) {
    EXPECT_TRUE(
        ClusterInfoManager::GetInstance()->IsExist(node_list[i].GetIpAddr()));
    DatanodeInfo node;
    bool exists = ClusterInfoManager::GetInstance()->GetDatanodeInfo(
        node_list[i].GetIpAddr(), &node);
    EXPECT_EQ(exists, true);
    EXPECT_EQ(node.GetIpAddr(), node_list[i].GetIpAddr());
    EXPECT_EQ(node.GetUuid(), node_list[i].GetUuid());
    EXPECT_EQ(node.GetHostName(), node_list[i].GetHostName());
  }
}

}  // namespace internal
}  // namespace cfs

int main(int argc, char** argv) {
  testing::InitGoogleTest(&argc, argv);
  return RUN_ALL_TESTS();
}
