#include <gflags/gflags.h>
#include <gtest/gtest.h>

#include <random>

#include "cloudfs2/cloudfs2.h"
#include "cloudfs2/cloudfs2_kv.h"

DEFINE_string(parent_dir, "/parent/dir", "must exist");
DEFINE_string(test_dir, "kv_it", "must not exist under parent_dir");
DEFINE_bool(keep_file, false, "");
DEFINE_uint64(value_len, 2 * 1024 * 1024, "value length");
DEFINE_uint32(batch_size, 3, "");
DECLARE_bool(cfs_kv_mset_atomic);

static std::string GenRandStr(uint64_t len) {
  std::random_device rd;
  std::mt19937_64 gen(rd());
  uint32_t minValue = static_cast<uint64_t>('a');
  uint32_t maxValue = static_cast<uint64_t>('z');
  std::uniform_int_distribution<uint32_t> dis(minValue, maxValue);
  uint32_t random_num = dis(gen);
  return std::string(len, static_cast<char>(random_num));
}

class KvIT : public testing::Test {
 public:
  static void SetUpTestSuite() {
    test_dir_ = FLAGS_parent_dir + "/" + FLAGS_test_dir;
    fs_ = cfs_connect(nullptr);
    ASSERT_NE(fs_, nullptr);
    // Make sure parent_dir exists
    auto* finfo = cfs_get_file_info(fs_, FLAGS_parent_dir.c_str());
    ASSERT_NE(finfo, nullptr)
        << "Please mkdir `" << FLAGS_parent_dir << "` in advance!";
    ASSERT_EQ(finfo->type, CFS_FILE_DIR) << FLAGS_parent_dir << " is not dir!";
    ASSERT_EQ(cfs_free_file_info(fs_, finfo, 1), 0);

    // Make sure test_dir_ not eixsts. If it exists, delete it
    if (cfs_exist(fs_, test_dir_.c_str()) > 0) {
      ASSERT_EQ(cfs_delete(fs_, test_dir_.c_str(), true), 0);
    }
    ASSERT_EQ(cfs_mkdir(fs_, test_dir_.c_str(), 0755, false), 0);
  }

  static void TearDownTestSuite() {
    if (!FLAGS_keep_file) {
      ASSERT_EQ(cfs_delete(fs_, test_dir_.c_str(), true), 0);
    }
    ASSERT_EQ(cfs_disconnect(fs_), 0);
  }

 protected:
  void SetUp() override {
    // ASSERT_EQ(cfs_mkdir(fs_, test_dir_.c_str(), 0755, false), 0);
  }

  void TearDown() override {
    // if (!FLAGS_keep_file) {
    //   ASSERT_EQ(cfs_delete(fs_, test_dir_.c_str(), true), 0);
    // }
  }

 protected:
  static cfs_kv_handle fs_;
  static std::string test_dir_;
};

cfs_fs* KvIT::fs_ = nullptr;
std::string KvIT::test_dir_;

TEST_F(KvIT, NewDelExistKeySpaceSucc) {
  std::string name1 = test_dir_ + "/NewDelExistKeySpaceSucc";
  cfs_kv_keyspace_option_t opt1{
      .wb_policy = CFS_WB_UNSPEC,
      .ttl = {.seconds = -1, .whole = false, .atime_based = false}};
  ASSERT_EQ(cfs_kv_exist_keyspace(fs_, name1.c_str()), 0);
  ASSERT_EQ(cfs_kv_new_keyspace(fs_, name1.c_str(), &opt1), 0);
  ASSERT_EQ(cfs_kv_exist_keyspace(fs_, name1.c_str()), 1);
  ASSERT_EQ(cfs_kv_del_keyspace(fs_, name1.c_str()), 0);
  ASSERT_EQ(cfs_kv_exist_keyspace(fs_, name1.c_str()), 0);

  std::string name2 = test_dir_ + "/keyspace_upload";
  cfs_kv_keyspace_option_t opt2{
      .wb_policy = CFS_WB_TRUE,
      .ttl = {.seconds = -1, .whole = false, .atime_based = false}};
  ASSERT_EQ(cfs_kv_new_keyspace(fs_, name2.c_str(), &opt2), 0);

  std::string name3 = test_dir_ + "/keyspace_not_upload";
  cfs_kv_keyspace_option_t opt3{
      .wb_policy = CFS_WB_FALSE,
      .ttl = {.seconds = -1, .whole = false, .atime_based = false}};
  ASSERT_EQ(cfs_kv_new_keyspace(fs_, name3.c_str(), &opt3), 0);
}

TEST_F(KvIT, MSetMGetSucc) {
  std::string space1 = test_dir_ + "/MSetMGetSucc";
  cfs_kv_keyspace_option_t opt1{
      .wb_policy = CFS_WB_UNSPEC,
      .ttl = {.seconds = -1, .whole = false, .atime_based = false}};
  ASSERT_EQ(cfs_kv_new_keyspace(fs_, space1.c_str(), &opt1), 0);

  std::vector<std::string> keys_str = {"mset0", "mset1", "mset2"};
  std::vector<std::string> vals_str = {std::string(10000000, 'a'),
                                       std::string(11000000, 'b'),
                                       std::string(12000000, 'c')};

  // 1. test mset
  std::vector<cfs_kv_buffer_t> keys;
  for (auto& k : keys_str) {
    keys.push_back(cfs_kv_buffer_t{k.data(), k.size()});
  }
  std::vector<cfs_kv_buffer_t> vals;
  for (auto& v : vals_str) {
    vals.push_back(cfs_kv_buffer_t{v.data(), v.size()});
  }
  std::vector<cfs_status> mset_results;
  mset_results.resize(keys.size(), CFS_STATUS_INTERNAL_ERROR);
  int res1 = cfs_kv_mset(fs_, space1.c_str(), keys.size(), keys.data(),
                         vals.data(), mset_results.data());
  EXPECT_EQ(res1, 0);
  for (auto& r : mset_results) {
    ASSERT_EQ(r, CFS_STATUS_OK);
  }

  // 2. test mget fully-succ
  std::vector<cfs_kv_result_t> mget_results;
  mget_results.resize(keys.size());
  int res2 = cfs_kv_mget(fs_, space1.c_str(), keys.size(), keys.data(),
                         mget_results.data());
  EXPECT_EQ(res2, 0);
  for (uint32_t i = 0; i < keys.size(); i++) {
    // If this assert failed, following EXPECT should not be executed, otherwise
    // it may access freed-mem or nullptr
    ASSERT_EQ(mget_results[i].ret, CFS_STATUS_OK);
    auto v = std::string_view(static_cast<char*>(mget_results[i].value.data),
                              mget_results[i].value.size);
    EXPECT_EQ(vals_str[i], v);
  }
  ASSERT_EQ(cfs_kv_mfree(fs_, mget_results.data(), mget_results.size()), 0);

  // 3. test mget partial-succ
  std::vector<std::string> partial_keys_str = {"mset0", "mset1",
                                               "no_exist_key"};
  std::vector<cfs_kv_buffer_t> partial_keys;
  for (auto& k : partial_keys_str) {
    partial_keys.push_back(cfs_kv_buffer_t{k.data(), k.size()});
  }
  std::vector<cfs_kv_result_t> partial_mget_results;
  partial_mget_results.resize(keys.size());
  int res3 = cfs_kv_mget(fs_, space1.c_str(), partial_keys.size(),
                         partial_keys.data(), partial_mget_results.data());
  EXPECT_EQ(res3, 1);
  for (uint32_t i = 0; i < partial_keys.size(); i++) {
    if (i == 2) {
      EXPECT_EQ(partial_mget_results[i].ret, CFS_STATUS_FILE_NOT_FOUND);
      EXPECT_EQ(partial_mget_results[i].value.data, nullptr);
      EXPECT_EQ(partial_mget_results[i].value.size, 0);
      continue;
    }
    ASSERT_EQ(partial_mget_results[i].ret, CFS_STATUS_OK);
    auto v =
        std::string_view(static_cast<char*>(partial_mget_results[i].value.data),
                         partial_mget_results[i].value.size);
    EXPECT_EQ(vals_str[i], v);
  }
  ASSERT_EQ(cfs_kv_mfree(fs_, partial_mget_results.data(),
                         partial_mget_results.size()),
            0);

  // 4. test mget all-not-found
  std::vector<std::string> err_keys_str = {"no_exist0", "no_exist1",
                                           "no_exist2"};
  std::vector<cfs_kv_buffer_t> err_keys;
  for (auto& k : err_keys_str) {
    err_keys.push_back(cfs_kv_buffer_t{k.data(), k.size()});
  }
  std::vector<cfs_kv_result_t> err_mget_results;
  err_mget_results.resize(keys.size());
  int res4 = cfs_kv_mget(fs_, space1.c_str(), err_keys.size(), err_keys.data(),
                         err_mget_results.data());
  EXPECT_EQ(res4, -1);
  for (uint32_t i = 0; i < err_keys.size(); i++) {
    EXPECT_EQ(err_mget_results[i].ret, CFS_STATUS_FILE_NOT_FOUND);
    EXPECT_EQ(err_mget_results[i].value.data, nullptr);
    EXPECT_EQ(err_mget_results[i].value.size, 0);
  }
}

TEST_F(KvIT, MSetMGetVarLen) {
  std::string space1 = test_dir_ + "/MSetMGetVarLen";
  cfs_kv_keyspace_option_t opt1{
      .wb_policy = CFS_WB_UNSPEC,
      .ttl = {.seconds = -1, .whole = false, .atime_based = false}};
  ASSERT_EQ(cfs_kv_new_keyspace(fs_, space1.c_str(), &opt1), 0);

  std::vector<std::string> keys_str;
  std::vector<std::string> vals_str;
  for (uint32_t i = 0; i < FLAGS_batch_size; i++) {
    keys_str.push_back("mset_key_" + std::to_string(i));
    vals_str.push_back(GenRandStr(FLAGS_value_len));
  }

  // 1. test mset
  std::vector<cfs_kv_buffer_t> keys;
  for (auto& k : keys_str) {
    keys.push_back(cfs_kv_buffer_t{k.data(), k.size()});
  }
  std::vector<cfs_kv_buffer_t> vals;
  for (auto& v : vals_str) {
    vals.push_back(cfs_kv_buffer_t{v.data(), v.size()});
  }
  std::vector<cfs_status> mset_results;
  mset_results.resize(keys.size(), CFS_STATUS_INTERNAL_ERROR);
  int res1 = cfs_kv_mset(fs_, space1.c_str(), keys.size(), keys.data(),
                         vals.data(), mset_results.data());
  EXPECT_EQ(res1, 0);
  for (auto& r : mset_results) {
    ASSERT_EQ(r, CFS_STATUS_OK);
  }

  // 2. test mget fully-succ
  std::vector<cfs_kv_result_t> mget_results;
  mget_results.resize(keys.size());
  int res2 = cfs_kv_mget(fs_, space1.c_str(), keys.size(), keys.data(),
                         mget_results.data());
  EXPECT_EQ(res2, 0);
  for (uint32_t i = 0; i < keys.size(); i++) {
    // If this assert failed, following EXPECT should not be executed, otherwise
    // memcmp may access freed-mem or nullptr
    ASSERT_EQ(mget_results[i].ret, CFS_STATUS_OK);
    EXPECT_EQ(mget_results[i].value.size, vals_str[i].size());
    EXPECT_EQ(memcmp(mget_results[i].value.data, vals_str[i].c_str(),
                     vals_str[i].size()),
              0);
  }
  ASSERT_EQ(cfs_kv_mfree(fs_, mget_results.data(), mget_results.size()), 0);
}

TEST_F(KvIT, MSetMayEmpty) {
  std::string space1 = test_dir_ + "/MSetMayEmpty";
  cfs_kv_keyspace_option_t opt1{
      .wb_policy = CFS_WB_UNSPEC,
      .ttl = {.seconds = -1, .whole = false, .atime_based = false}};
  ASSERT_EQ(cfs_kv_new_keyspace(fs_, space1.c_str(), &opt1), 0);

  std::vector<std::string> keys_str;
  std::vector<std::string> vals_str;
  for (uint32_t i = 0; i < 4; i++) {
    keys_str.push_back("mset_key_" + std::to_string(i));
    vals_str.push_back(GenRandStr(FLAGS_value_len));
  }

  // 1. test mset
  std::vector<cfs_kv_buffer_t> keys;
  for (auto& k : keys_str) {
    keys.push_back(cfs_kv_buffer_t{k.data(), k.size()});
  }
  std::vector<cfs_kv_buffer_t> vals;
  for (auto& v : vals_str) {
    vals.push_back(cfs_kv_buffer_t{v.data(), v.size()});
  }
  // non-null char* and len==0
  vals[1].size = 0;
  vals[3].data = nullptr;
  vals[3].size = 0;
  std::vector<cfs_status> mset_results;
  mset_results.resize(keys.size(), CFS_STATUS_INTERNAL_ERROR);
  int res1 = cfs_kv_mset(fs_, space1.c_str(), keys.size(), keys.data(),
                         vals.data(), mset_results.data());
  EXPECT_EQ(res1, 0);
  for (auto& r : mset_results) {
    ASSERT_EQ(r, CFS_STATUS_OK);
  }

  // 2. test mget fully-succ
  std::vector<cfs_kv_result_t> mget_results;
  mget_results.resize(keys.size());
  int res2 = cfs_kv_mget(fs_, space1.c_str(), keys.size(), keys.data(),
                         mget_results.data());
  EXPECT_EQ(res2, 0);
  for (uint32_t i = 0; i < keys.size(); i++) {
    // If this assert failed, following EXPECT should not be executed, otherwise
    // memcmp may access freed-mem or nullptr
    ASSERT_EQ(mget_results[i].ret, CFS_STATUS_OK);
    if (i == 1 || i == 3) {
      EXPECT_EQ(mget_results[i].value.size, 0);
      EXPECT_EQ(mget_results[i].value.data, nullptr);
      continue;
    }
    EXPECT_EQ(mget_results[i].value.size, vals_str[i].size());
    EXPECT_EQ(memcmp(mget_results[i].value.data, vals_str[i].c_str(),
                     vals_str[i].size()),
              0);
  }
  ASSERT_EQ(cfs_kv_mfree(fs_, mget_results.data(), mget_results.size()), 0);
}

TEST_F(KvIT, MSetInvalidEmpty) {
  std::string space1 = test_dir_ + "/MSetInvalidEmpty";
  cfs_kv_keyspace_option_t opt1{
      .wb_policy = CFS_WB_UNSPEC,
      .ttl = {.seconds = -1, .whole = false, .atime_based = false}};
  ASSERT_EQ(cfs_kv_new_keyspace(fs_, space1.c_str(), &opt1), 0);

  std::vector<std::string> keys_str;
  std::vector<std::string> vals_str;
  for (uint32_t i = 0; i < 4; i++) {
    keys_str.push_back("mset_key_" + std::to_string(i));
    vals_str.push_back(GenRandStr(FLAGS_value_len));
  }

  // 1. test mset
  std::vector<cfs_kv_buffer_t> keys;
  for (auto& k : keys_str) {
    keys.push_back(cfs_kv_buffer_t{k.data(), k.size()});
  }
  std::vector<cfs_kv_buffer_t> vals;
  for (auto& v : vals_str) {
    vals.push_back(cfs_kv_buffer_t{v.data(), v.size()});
  }
  // null char* and len>0. this is invalid param
  vals[3].data = nullptr;
  std::vector<cfs_status> mset_results;
  mset_results.resize(keys.size(), CFS_STATUS_INTERNAL_ERROR);
  int res1 = cfs_kv_mset(fs_, space1.c_str(), keys.size(), keys.data(),
                         vals.data(), mset_results.data());
  EXPECT_EQ(res1, -1);
  for (auto& r : mset_results) {
    ASSERT_EQ(r, CFS_STATUS_INVALID_ARGUMENT);
  }
}

TEST_F(KvIT, MCheckSucc) {
  std::string space1 = test_dir_ + "/MCheckSucc";
  cfs_kv_keyspace_option_t opt1{
      .wb_policy = CFS_WB_FALSE,
      .ttl = {.seconds = -1, .whole = false, .atime_based = false}};
  ASSERT_EQ(cfs_kv_new_keyspace(fs_, space1.c_str(), &opt1), 0);

  std::vector<std::string> keys_str = {"mset0", "mset1", "mset2"};
  std::vector<std::string> vals_str = {std::string(10000, 'a'),
                                       std::string(11000, 'b'),
                                       std::string(12000, 'c')};

  // 1. test mset
  std::vector<cfs_kv_buffer_t> keys;
  for (auto& k : keys_str) {
    keys.push_back(cfs_kv_buffer_t{k.data(), k.size()});
  }
  std::vector<cfs_kv_buffer_t> vals;
  for (auto& v : vals_str) {
    vals.push_back(cfs_kv_buffer_t{v.data(), v.size()});
  }
  std::vector<cfs_status> mset_results;
  mset_results.resize(keys.size(), CFS_STATUS_INTERNAL_ERROR);
  int res1 = cfs_kv_mset(fs_, space1.c_str(), keys.size(), keys.data(),
                         vals.data(), mset_results.data());
  EXPECT_EQ(res1, 0);
  for (auto& r : mset_results) {
    ASSERT_EQ(r, CFS_STATUS_OK);
  }

  // 2. test mcheck fully-succ
  std::vector<cfs_status> mcheck_results;
  mcheck_results.resize(keys.size());
  int res2 = cfs_kv_mcheck(fs_, space1.c_str(), keys.size(), keys.data(),
                           mcheck_results.data(), false);
  EXPECT_EQ(res2, 0);
  for (uint32_t i = 0; i < keys.size(); i++) {
    EXPECT_EQ(mcheck_results[i], CFS_STATUS_OK);
  }

  // 3. test mcheck partial-succ
  std::vector<std::string> partial_keys_str = {"mset0", "mset1",
                                               "no_exist_key"};
  std::vector<cfs_kv_buffer_t> partial_keys;
  for (auto& k : partial_keys_str) {
    partial_keys.push_back(cfs_kv_buffer_t{k.data(), k.size()});
  }
  std::vector<cfs_status> partial_mcheck_results;
  partial_mcheck_results.resize(keys.size());
  int res3 =
      cfs_kv_mcheck(fs_, space1.c_str(), partial_keys.size(),
                    partial_keys.data(), partial_mcheck_results.data(), false);
  EXPECT_EQ(res3, 0);
  for (uint32_t i = 0; i < partial_keys.size(); i++) {
    if (i == 2) {
      EXPECT_EQ(partial_mcheck_results[i], CFS_STATUS_FILE_NOT_FOUND);
      continue;
    }
    EXPECT_EQ(partial_mcheck_results[i], CFS_STATUS_OK);
  }

  // 4. test mcheck all-not-found
  std::vector<std::string> err_keys_str = {"no_exist0", "no_exist1",
                                           "no_exist2"};
  std::vector<cfs_kv_buffer_t> err_keys;
  for (auto& k : err_keys_str) {
    err_keys.push_back(cfs_kv_buffer_t{k.data(), k.size()});
  }
  std::vector<cfs_status> err_mcheck_results;
  err_mcheck_results.resize(keys.size());
  int res4 = cfs_kv_mcheck(fs_, space1.c_str(), err_keys.size(),
                           err_keys.data(), err_mcheck_results.data(), false);
  EXPECT_EQ(res4, 0);
  for (uint32_t i = 0; i < err_keys.size(); i++) {
    EXPECT_EQ(err_mcheck_results[i], CFS_STATUS_FILE_NOT_FOUND);
  }
}

TEST_F(KvIT, MSetOverwrite) {
  std::string space1 = test_dir_ + "/MSetOverwrite";
  cfs_kv_keyspace_option_t opt1{
      .wb_policy = CFS_WB_TRUE,
      .ttl = {.seconds = -1, .whole = false, .atime_based = false}};
  ASSERT_EQ(cfs_kv_new_keyspace(fs_, space1.c_str(), &opt1), 0);

  std::vector<std::string> keys_str = {"mset0", "mset1", "mset2"};
  std::vector<std::string> vals_str = {
      std::string(10, 'a'), std::string(11, 'b'), std::string(12, 'c')};

  // 1. initial mset
  std::vector<cfs_kv_buffer_t> keys;
  for (auto& k : keys_str) {
    keys.push_back(cfs_kv_buffer_t{k.data(), k.size()});
  }
  std::vector<cfs_kv_buffer_t> vals;
  for (auto& v : vals_str) {
    vals.push_back(cfs_kv_buffer_t{v.data(), v.size()});
  }
  std::vector<cfs_status> mset_results;
  mset_results.resize(keys.size(), CFS_STATUS_INTERNAL_ERROR);
  int res1 = cfs_kv_mset(fs_, space1.c_str(), keys.size(), keys.data(),
                         vals.data(), mset_results.data());
  EXPECT_EQ(res1, 0);
  for (auto& r : mset_results) {
    ASSERT_EQ(r, CFS_STATUS_OK);
  }

  // 1.1 mset again to overwrite the old value
  std::vector<std::string> keys1_str = {"mset3", "mset1", "mset2"};
  std::vector<std::string> vals1_str = {
      std::string(10, 'd'), std::string(21, 'e'), std::string(22, 'f')};
  std::vector<cfs_kv_buffer_t> keys1;
  for (auto& k : keys1_str) {
    keys1.push_back(cfs_kv_buffer_t{k.data(), k.size()});
  }
  std::vector<cfs_kv_buffer_t> vals1;
  for (auto& v : vals1_str) {
    vals1.push_back(cfs_kv_buffer_t{v.data(), v.size()});
  }
  res1 = cfs_kv_mset(fs_, space1.c_str(), keys1.size(), keys1.data(),
                     vals1.data(), mset_results.data());
  EXPECT_EQ(res1, 0);
  for (auto& r : mset_results) {
    EXPECT_EQ(r, CFS_STATUS_OK);
  }

  // 2. test mget fully-succ
  std::vector<cfs_kv_result_t> mget_results;
  mget_results.resize(keys.size());
  int res2 = cfs_kv_mget(fs_, space1.c_str(), keys.size(), keys.data(),
                         mget_results.data());
  EXPECT_EQ(res2, 0);
  for (uint32_t i = 0; i < keys.size(); i++) {
    ASSERT_EQ(mget_results[i].ret, CFS_STATUS_OK);
    auto v = std::string_view(static_cast<char*>(mget_results[i].value.data),
                              mget_results[i].value.size);
    if (i == 0) {
      EXPECT_EQ(vals_str[i], v);
    } else {
      EXPECT_EQ(vals1_str[i], v);
    }
  }
  ASSERT_EQ(cfs_kv_mfree(fs_, mget_results.data(), mget_results.size()), 0);
}

TEST_F(KvIT, MDelFullPartialNon) {
  std::string space1 = test_dir_ + "/MDelFullPartialNon";
  cfs_kv_keyspace_option_t opt1{
      .wb_policy = CFS_WB_FALSE,
      .ttl = {.seconds = -1, .whole = false, .atime_based = false}};
  ASSERT_EQ(cfs_kv_new_keyspace(fs_, space1.c_str(), &opt1), 0);

  std::vector<std::string> keys_str = {"mset0", "mset1", "mset2"};
  std::vector<std::string> vals_str = {std::string(10000, 'a'),
                                       std::string(11000, 'b'),
                                       std::string(12000, 'c')};

  // 1. test mset
  std::vector<cfs_kv_buffer_t> keys;
  for (auto& k : keys_str) {
    keys.push_back(cfs_kv_buffer_t{k.data(), k.size()});
  }
  std::vector<cfs_kv_buffer_t> vals;
  for (auto& v : vals_str) {
    vals.push_back(cfs_kv_buffer_t{v.data(), v.size()});
  }
  std::vector<cfs_status> mset_results;
  mset_results.resize(keys.size(), CFS_STATUS_INTERNAL_ERROR);
  int res1 = cfs_kv_mset(fs_, space1.c_str(), keys.size(), keys.data(),
                         vals.data(), mset_results.data());
  EXPECT_EQ(res1, 0);
  for (auto& r : mset_results) {
    ASSERT_EQ(r, CFS_STATUS_OK);
  }

  // 2. mdel all un-exist keys should succeed
  std::vector<std::string> err_keys_str = {"no_exist0", "unexist1", "unexist2"};
  std::vector<cfs_kv_buffer_t> err_keys;
  for (auto& k : err_keys_str) {
    err_keys.push_back(cfs_kv_buffer_t{k.data(), k.size()});
  }
  int del_res1 =
      cfs_kv_mdel(fs_, space1.c_str(), err_keys.size(), err_keys.data());
  EXPECT_EQ(del_res1, 0);

  // 3. mdel partial-exist keys should succeed
  std::vector<std::string> partial_keys_str = {"mset2", "unexist3", "unexist4"};
  std::vector<cfs_kv_buffer_t> partial_keys;
  for (auto& k : partial_keys_str) {
    partial_keys.push_back(cfs_kv_buffer_t{k.data(), k.size()});
  }
  int del_res2 = cfs_kv_mdel(fs_, space1.c_str(), partial_keys.size(),
                             partial_keys.data());
  EXPECT_EQ(del_res2, 0);
  EXPECT_EQ(cfs_exist(fs_, (space1 + "/mset2").c_str()), 0);

  // 3. mdel all-exist keys should succeed
  int del_res3 = cfs_kv_mdel(fs_, space1.c_str(), keys.size() - 1, keys.data());
  EXPECT_EQ(del_res3, 0);
  EXPECT_EQ(cfs_exist(fs_, (space1 + "/mset0").c_str()), 0);
  EXPECT_EQ(cfs_exist(fs_, (space1 + "/mset2").c_str()), 0);
}

TEST_F(KvIT, NoSpaceMSetMGetMDel) {
  std::string space1 = test_dir_ + "/NoSpaceMSetMGetMDel";
  std::vector<std::string> keys_str = {"mset0", "mset1", "mset2"};
  std::vector<std::string> vals_str = {
      std::string(10, 'a'), std::string(11, 'b'), std::string(12, 'c')};

  // 1. test mset without create keyspace
  std::vector<cfs_kv_buffer_t> keys;
  for (auto& k : keys_str) {
    keys.push_back(cfs_kv_buffer_t{k.data(), k.size()});
  }
  std::vector<cfs_kv_buffer_t> vals;
  for (auto& v : vals_str) {
    vals.push_back(cfs_kv_buffer_t{v.data(), v.size()});
  }
  std::vector<cfs_status> mset_results;
  mset_results.resize(keys.size(), CFS_STATUS_INTERNAL_ERROR);
  int res1 = cfs_kv_mset(fs_, space1.c_str(), keys.size(), keys.data(),
                         vals.data(), mset_results.data());
  EXPECT_EQ(res1, -1);
  for (auto& r : mset_results) {
    EXPECT_EQ(r, CFS_STATUS_KEYSPACE_NOT_EXIST);
  }
  EXPECT_EQ(cfs_get_last_error()->status, CFS_STATUS_KEYSPACE_NOT_EXIST);

  // 2. test mget without create keyspace
  std::vector<cfs_kv_result_t> mget_results;
  mget_results.resize(keys.size());
  int res2 = cfs_kv_mget(fs_, space1.c_str(), keys.size(), keys.data(),
                         mget_results.data());
  EXPECT_EQ(res2, -1);
  for (uint32_t i = 0; i < keys.size(); i++) {
    EXPECT_EQ(mget_results[i].ret, CFS_STATUS_KEYSPACE_NOT_EXIST);
  }
  EXPECT_EQ(cfs_get_last_error()->status, CFS_STATUS_KEYSPACE_NOT_EXIST);

  // 3. test mcheck without create keyspace
  std::vector<cfs_status> mcheck_results;
  mcheck_results.resize(keys.size());
  // 3.1. wrong keyspace should fail
  int check_res = cfs_kv_mcheck(fs_, space1.c_str(), keys.size(), keys.data(),
                                mcheck_results.data(), false);
  EXPECT_EQ(check_res, -1);
  for (uint32_t i = 0; i < keys.size(); i++) {
    EXPECT_EQ(mcheck_results[i], CFS_STATUS_KEYSPACE_NOT_EXIST);
  }
  EXPECT_EQ(cfs_get_last_error()->status, CFS_STATUS_KEYSPACE_NOT_EXIST);

  // 4. test mdel without create keyspace
  int del_res = cfs_kv_mdel(fs_, space1.c_str(), keys.size(), keys.data());
  EXPECT_EQ(del_res, -1);
  EXPECT_EQ(cfs_get_last_error()->status, CFS_STATUS_KEYSPACE_NOT_EXIST);
}

TEST_F(KvIT, MSetOverwriteDir) {
  auto atomic_saved = FLAGS_cfs_kv_mset_atomic;
  FLAGS_cfs_kv_mset_atomic = true;
  std::string space1 = test_dir_ + "/MSetOverwriteDir";
  cfs_kv_keyspace_option_t opt1{
      .wb_policy = CFS_WB_TRUE,
      .ttl = {.seconds = -1, .whole = false, .atime_based = false}};
  ASSERT_EQ(cfs_kv_new_keyspace(fs_, space1.c_str(), &opt1), 0);

  std::string dir = space1 + "/mset0";
  ASSERT_EQ(cfs_mkdir(fs_, dir.c_str(), 0755, false), 0);

  std::vector<std::string> keys_str = {"mset0", "mset1", "mset2"};
  std::vector<std::string> vals_str = {
      std::string(10, 'a'), std::string(11, 'b'), std::string(12, 'c')};

  // 1. initial mset
  std::vector<cfs_kv_buffer_t> keys;
  for (auto& k : keys_str) {
    keys.push_back(cfs_kv_buffer_t{k.data(), k.size()});
  }
  std::vector<cfs_kv_buffer_t> vals;
  for (auto& v : vals_str) {
    vals.push_back(cfs_kv_buffer_t{v.data(), v.size()});
  }
  std::vector<cfs_status> mset_results;
  mset_results.resize(keys.size(), CFS_STATUS_INTERNAL_ERROR);
  int res1 = cfs_kv_mset(fs_, space1.c_str(), keys.size(), keys.data(),
                         vals.data(), mset_results.data());
  // 1.1 Overwrite mset0 should fail because it is a dir
  EXPECT_EQ(res1, -1);
  for (auto& r : mset_results) {
    EXPECT_EQ(r, CFS_STATUS_IO_ERROR);
  }

  // 2. all files should be not-found when mget
  std::vector<cfs_kv_result_t> mget_results;
  mget_results.resize(keys.size());
  int res2 = cfs_kv_mget(fs_, space1.c_str(), keys.size(), keys.data(),
                         mget_results.data());
  EXPECT_EQ(res2, -1);
  for (uint32_t i = 0; i < keys.size(); i++) {
    if (i == 0) {
      EXPECT_EQ(mget_results[i].ret, CFS_STATUS_WRONG_FILE_TYPE);
    } else {
      EXPECT_EQ(mget_results[i].ret, CFS_STATUS_FILE_NOT_FOUND);
    }
    EXPECT_EQ(mget_results[i].value.data, nullptr);
    EXPECT_EQ(mget_results[i].value.size, 0);
  }
  FLAGS_cfs_kv_mset_atomic = atomic_saved;
}

int main(int argc, char** argv) {
  // InitGoogleTest must be put before ParseCommandLineFlags because
  // 'ParseCommandLineFlags' does not known '--gtest_filter' and will report
  // error
  testing::InitGoogleTest(&argc, argv);
  gflags::ParseCommandLineFlags(&argc, &argv, true);
  return RUN_ALL_TESTS();
}
