#include <gflags/gflags.h>
#include <gtest/gtest.h>

#include "cloudfs2/cloudfs2.h"
#include "impl/cfs_impl.h"  // access FileSystem::BatchGetFileInfo

DECLARE_uint64(cfs_write_block_size);

DEFINE_string(parent_dir, "/parent/dir", "must exist");
DEFINE_string(test_dir, "batch_api_it", "must not exist under parent_dir");
DEFINE_bool(keep_file, false, "");

class BatchApiIT : public testing::Test {
 public:
  static void SetUpTestSuite() {
    // Set block size to a small value in order to test write beyond one block
    FLAGS_cfs_write_block_size = 16 * 1024 * 1024;
    fs_ = cfs_connect(nullptr);
    ASSERT_NE(fs_, nullptr);
    // Make sure parent_dir exists
    auto* finfo = cfs_get_file_info(fs_, FLAGS_parent_dir.c_str());
    ASSERT_NE(finfo, nullptr);
    ASSERT_EQ(finfo->type, CFS_FILE_DIR);
    ASSERT_EQ(cfs_free_file_info(fs_, finfo, 1), 0);

    // Make sure test_dir not eixsts
    std::string test_dir = FLAGS_parent_dir + "/" + FLAGS_test_dir;
    ASSERT_EQ(cfs_exist(fs_, test_dir.c_str()), 0);
  }

  static void TearDownTestSuite() {
    ASSERT_EQ(cfs_disconnect(fs_), 0);
  }

 protected:
  void SetUp() override {
    test_dir_ = FLAGS_parent_dir + "/" + FLAGS_test_dir;
    ASSERT_EQ(cfs_mkdir(fs_, test_dir_.c_str(), 0755, false), 0);
  }

  void TearDown() override {
    if (!FLAGS_keep_file) {
      ASSERT_EQ(cfs_delete(fs_, test_dir_.c_str(), true), 0);
    }
  }

 protected:
  static cfs_fs* fs_;

  std::string test_dir_;
};

cfs_fs* BatchApiIT::fs_ = nullptr;

TEST_F(BatchApiIT, 1BlkNonEmptyCreateCloseGetnolocDel) {
  constexpr uint64_t kBufLen = 1024 * 1024 * 1;
  std::array<char, kBufLen> buf;
  buf.fill('1');
  std::vector<std::string> paths = {
      test_dir_ + "/single0", test_dir_ + "/single1", test_dir_ + "/bigfile0"};
  std::vector<uint32_t> part_nums = {1, 1, 3};
  std::vector<const char*> paths_cstr;
  for (const auto& p : paths) {
    paths_cstr.push_back(p.c_str());
  }

  // 1. BatchCreate and BatchClose
  cfs_batch_create_option_t open_opts = {
      .paths = paths_cstr.data(),
      .part_nums = part_nums.data(),
      .wb_policy = nullptr,
      .num = static_cast<uint32_t>(paths_cstr.size()),
      .mode = 0644,
      .replication = 2};
  cfs_file*** fds = cfs_batch_create(fs_, &open_opts);
  ASSERT_NE(fds, nullptr);
  for (uint32_t i = 0; i < part_nums.size(); i++) {
    ASSERT_NE(fds[i], nullptr);
    for (uint32_t j = 0; j < part_nums[i]; j++) {
      ASSERT_NE(fds[i][j], nullptr);
      int64_t write_res = cfs_write(fds[i][j], buf.data(), kBufLen);
      EXPECT_EQ(write_res, static_cast<int64_t>(kBufLen));
    }
  }
  ASSERT_EQ(cfs_batch_close(fds, part_nums.data(), part_nums.size()), 0);

  // 2. BatchGetFileInfo
  auto get_res = fs_->fs_handle->BatchGetFileInfo(paths, false, false, -1);
  ASSERT_TRUE(get_res.IsOk());
  auto& finfos = get_res.GetValue();
  EXPECT_EQ(finfos.size(), paths.size());
  for (uint32_t i = 0; i < paths.size(); i++) {
    ASSERT_TRUE(finfos[i]);  // std::optional should have value
    EXPECT_EQ(finfos[i]->GetLength(), part_nums[i] * kBufLen);
    EXPECT_TRUE(finfos[i]->GetLocatedBlocks().GetBlocks().empty());
  }

  // 3. BatchDelete
  auto del_res =
      cfs_batch_delete(fs_, paths_cstr.data(), paths_cstr.size(), false);
  EXPECT_EQ(del_res, 0);

  // 4. re-BatchGetFileInfo to check Del success
  get_res = fs_->fs_handle->BatchGetFileInfo(paths, false, false, -1);
  ASSERT_TRUE(get_res.IsOk());
  finfos = get_res.GetValue();
  EXPECT_EQ(finfos.size(), paths.size());
  for (uint32_t i = 0; i < paths.size(); i++) {
    EXPECT_FALSE(finfos[i]);
  }
}

TEST_F(BatchApiIT, 2BlkNonEmptyCreateCloseGetwithloc) {
  constexpr uint64_t kBufLen = 1024 * 1024 * 1;
  std::array<char, kBufLen> buf;
  buf.fill('1');
  std::vector<std::string> paths = {
      test_dir_ + "/single0", test_dir_ + "/bigfile0", test_dir_ + "/bigfile1"};
  std::vector<uint32_t> part_nums = {1, 2, 3};
  std::vector<const char*> paths_cstr;
  for (const auto& p : paths) {
    paths_cstr.push_back(p.c_str());
  }

  // 1. BatchCreate and BatchClose
  cfs_batch_create_option_t open_opts = {
      .paths = paths_cstr.data(),
      .part_nums = part_nums.data(),
      .wb_policy = nullptr,
      .num = static_cast<uint32_t>(paths_cstr.size()),
      .mode = 0644,
      .replication = 2};
  cfs_file*** fds = cfs_batch_create(fs_, &open_opts);
  ASSERT_NE(fds, nullptr);

  uint64_t part_len = FLAGS_cfs_write_block_size + kBufLen;
  for (uint32_t i = 0; i < part_nums.size(); i++) {
    ASSERT_NE(fds[i], nullptr);
    for (uint32_t j = 0; j < part_nums[i]; j++) {
      ASSERT_NE(fds[i][j], nullptr);
      int64_t fini_len = 0;
      while (fini_len < static_cast<int64_t>(part_len)) {
        int64_t write_res = cfs_write(fds[i][j], buf.data(), kBufLen);
        EXPECT_EQ(write_res, static_cast<int64_t>(kBufLen));
        fini_len += write_res;
        if (write_res < 0) {
          break;
        }
      }
    }
  }
  ASSERT_EQ(cfs_batch_close(fds, part_nums.data(), part_nums.size()), 0);

  // 2. BatchGetFileInfo with both existed and non-existed files
  auto mix_paths = paths;
  // this part1 file should be concated and deleted
  mix_paths[1] = test_dir_ + "/bigfile1.part1";
  auto get_res = fs_->fs_handle->BatchGetFileInfo(mix_paths, true, false, -1);
  ASSERT_TRUE(get_res.IsOk());
  auto& finfos = get_res.GetValue();
  EXPECT_EQ(finfos.size(), mix_paths.size());
  ASSERT_TRUE(finfos[0]);
  EXPECT_EQ(finfos[0]->GetLength(), part_len);
  EXPECT_EQ(finfos[0]->GetLocatedBlocks().GetBlocks().size(), 2);
  ASSERT_FALSE(finfos[1]);
  ASSERT_TRUE(finfos[2]);
  EXPECT_EQ(finfos[2]->GetLength(), part_len * 3);
  EXPECT_EQ(finfos[2]->GetLocatedBlocks().GetBlocks().size(), 2 * 3);
}

TEST_F(BatchApiIT, 1BlkEmptyCreateCloseGetwithloc) {
  std::vector<std::string> paths = {test_dir_ + "/single0",
                                    test_dir_ + "/bigfile0"};
  std::vector<uint32_t> part_nums = {1, 3};
  std::vector<const char*> paths_cstr;
  for (const auto& p : paths) {
    paths_cstr.push_back(p.c_str());
  }

  // 1. BatchCreate and BatchClose
  cfs_batch_create_option_t open_opts = {
      .paths = paths_cstr.data(),
      .part_nums = part_nums.data(),
      .wb_policy = nullptr,
      .num = static_cast<uint32_t>(paths_cstr.size()),
      .mode = 0644,
      .replication = 2};
  cfs_file*** fds = cfs_batch_create(fs_, &open_opts);
  ASSERT_NE(fds, nullptr);
  for (uint32_t i = 0; i < part_nums.size(); i++) {
    ASSERT_NE(fds[i], nullptr);
    for (uint32_t j = 0; j < part_nums[i]; j++) {
      ASSERT_NE(fds[i][j], nullptr);
    }
  }
  // Close without write
  ASSERT_EQ(cfs_batch_close(fds, part_nums.data(), part_nums.size()), 0);

  // 2. BatchGetFileInfo
  auto get_res = fs_->fs_handle->BatchGetFileInfo(paths, true, false, -1);
  ASSERT_TRUE(get_res.IsOk());
  auto& finfos = get_res.GetValue();
  EXPECT_EQ(finfos.size(), paths.size());
  for (uint32_t i = 0; i < paths.size(); i++) {
    ASSERT_TRUE(finfos[i]);
    EXPECT_EQ(finfos[i]->GetLength(), 0);
    EXPECT_TRUE(finfos[i]->GetLocatedBlocks().GetBlocks().empty());
  }
}

TEST_F(BatchApiIT, DelNonExist) {
  std::string parent = test_dir_ + "/DelNonExist";
  ASSERT_EQ(cfs_mkdir(fs_, parent.c_str(), 0755, false), 0);

  std::vector<std::string> paths = {parent + "/single0", parent + "/bigfile0"};
  std::vector<uint32_t> part_nums = {1, 3};
  std::vector<const char*> paths_cstr;
  for (const auto& p : paths) {
    paths_cstr.push_back(p.c_str());
  }

  // 1. BatchCreate and BatchClose
  cfs_batch_create_option_t open_opts = {
      .paths = paths_cstr.data(),
      .part_nums = part_nums.data(),
      .wb_policy = nullptr,
      .num = static_cast<uint32_t>(paths_cstr.size()),
      .mode = 0644,
      .replication = 2};
  cfs_file*** fds = cfs_batch_create(fs_, &open_opts);
  ASSERT_NE(fds, nullptr);
  for (uint32_t i = 0; i < part_nums.size(); i++) {
    ASSERT_NE(fds[i], nullptr);
    for (uint32_t j = 0; j < part_nums[i]; j++) {
      ASSERT_NE(fds[i][j], nullptr);
    }
  }
  // Close without write
  ASSERT_EQ(cfs_batch_close(fds, part_nums.data(), part_nums.size()), 0);

  // 2. BatchDelete some non-exist files
  auto mix_paths = paths;
  mix_paths[1] = parent + "/non_exist_file";
  std::vector<const char*> mix_paths_cstr;
  for (const auto& p : mix_paths) {
    mix_paths_cstr.push_back(p.c_str());
  }
  // 2.1 batch-del should fail when 'force' is false and the files should remain
  // existed
  auto del_res1 = cfs_batch_delete(fs_, mix_paths_cstr.data(),
                                   mix_paths_cstr.size(), false);
  EXPECT_EQ(del_res1, -1);
  EXPECT_EQ(cfs_exist(fs_, paths[0].c_str()), 1);

  // 2.2 batch-del should succeed when `force` is true
  auto del_res2 =
      cfs_batch_delete(fs_, mix_paths_cstr.data(), mix_paths_cstr.size(), true);
  EXPECT_EQ(del_res2, 0);
  EXPECT_EQ(cfs_exist(fs_, paths[0].c_str()), 0);
}

int main(int argc, char** argv) {
  // InitGoogleTest must be put before ParseCommandLineFlags because
  // 'ParseCommandLineFlags' does not known '--gtest_filter' and will report
  // error
  testing::InitGoogleTest(&argc, argv);
  gflags::ParseCommandLineFlags(&argc, &argv, true);
  return RUN_ALL_TESTS();
}
