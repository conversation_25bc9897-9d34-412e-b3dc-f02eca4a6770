#include <gflags/gflags.h>
#include <gtest/gtest.h>

#include <thread>

#include "cloudfs2/cloudfs2.h"
#include "common/datetime_util.h"
#include "common/logger.h"

DECLARE_uint64(cfs_write_block_size);
DECLARE_string(cfs_log_type);

DEFINE_string(test_dir, "/parent/dir", "must exist");

class MetricTest : public testing::Test {
 public:
  static void SetUpTestSuite() {
    FLAGS_cfs_log_type = "stdout";
    // Set block size to a small value in order to test write beyond one block
    FLAGS_cfs_write_block_size = 16 * 1024 * 1024;
    fs_ = cfs_connect(nullptr);
    ASSERT_NE(fs_, nullptr);
    // Make sure test_dir exists
    auto* finfo = cfs_get_file_info(fs_, FLAGS_test_dir.c_str());
    ASSERT_NE(finfo, nullptr);
    ASSERT_EQ(finfo->type, CFS_FILE_DIR);
    ASSERT_EQ(cfs_free_file_info(fs_, finfo, 1), 0);

    // Make sure test_dir not eixsts
    std::string test_dir = FLAGS_test_dir + "/metric_test";
    ASSERT_EQ(cfs_exist(fs_, test_dir.c_str()), 0);
  }

  static void TearDownTestSuite() {
    int32_t res = cfs_disconnect(fs_);
    ASSERT_EQ(res, 0);
  }

 protected:
  void SetUp() override {
    test_dir_ = FLAGS_test_dir + "/metric_test";
    ASSERT_EQ(cfs_mkdir(fs_, test_dir_.c_str(), 0755, false), 0);
  }

  void TearDown() override {
    ASSERT_EQ(cfs_delete(fs_, test_dir_.c_str(), true), 0);
  }

 protected:
  static cfs_fs* fs_;

  std::string test_dir_;
};

cfs_fs* MetricTest::fs_ = nullptr;

TEST_F(MetricTest, LongRun) {
  std::string path = test_dir_ + "/metric_test_file";
  int64_t file_len = 100 * 1024 * 1024;
  constexpr uint64_t kBufSize = 1024 * 1024;
  std::array<char, kBufSize> buf;
  buf.fill('1');
  int64_t start_time = cfs::internal::DatetimeUtil::GetNowTimeMs();
  int64_t duration = 5 * 60 * 1000;  // 5 min

  while (cfs::internal::DatetimeUtil::GetNowTimeMs() < start_time + duration) {
    // 1. write file
    cfs_open_option_t open_opts = {
        .flags = CFS_O_WRONLY | CFS_O_CREATE | CFS_O_TRUNC,
        .mode = 0644,
        .create_parent = true,
        .replication = 0,
        .force = false};
    cfs_file* wfile = cfs_open(fs_, path.c_str(), &open_opts);
    ASSERT_NE(wfile, nullptr);
    int64_t finish_len = 0;
    while (finish_len < file_len) {
      int64_t written_size = cfs_write(wfile, buf.data(), buf.size());
      ASSERT_GT(written_size, 0);
      finish_len += written_size;
    }
    ASSERT_EQ(cfs_close(wfile), 0);

    // 2. ls file 10 times
    for (uint32_t i = 0; i < 10; i++) {
      auto* finfo = cfs_get_file_info(fs_, path.c_str());
      ASSERT_NE(finfo, nullptr);
      ASSERT_EQ(finfo->type, CFS_FILE_REGULAR);
      EXPECT_EQ(finfo->size, file_len);
      ASSERT_EQ(cfs_free_file_info(fs_, finfo, 1), 0);
    }

    // 3. read file
    cfs_open_option_t open_opts2 = {.flags = CFS_O_RDONLY,
                                    .mode = 0644,
                                    .create_parent = false,
                                    .replication = 0,
                                    .force = false};
    cfs_file* rfile = cfs_open(fs_, path.c_str(), &open_opts2);
    ASSERT_NE(wfile, nullptr);
    finish_len = 0;
    while (finish_len < file_len) {
      int64_t rc = cfs_read(rfile, buf.data(), buf.size());
      if (rc <= 0) {
        CFSLOG(INFO, "read EOF: rc={}", rc);
        break;
      } else {
        finish_len += rc;
      }
    }
    ASSERT_EQ(cfs_close(rfile), 0);

    // 4. delete file
    ASSERT_EQ(cfs_delete(fs_, path.c_str(), false), 0);

    // 5. sleep for a while
    CFSLOG(INFO, "sleep for 5 seconds...");
    std::this_thread::sleep_for(std::chrono::seconds(5));
  }
}

int main(int argc, char** argv) {
  // InitGoogleTest must be put before ParseCommandLineFlags because
  // 'ParseCommandLineFlags' does not known '--gtest_filter' and will report
  // error
  testing::InitGoogleTest(&argc, argv);
  gflags::ParseCommandLineFlags(&argc, &argv, true);
  int ret = RUN_ALL_TESTS();
  return ret;
}