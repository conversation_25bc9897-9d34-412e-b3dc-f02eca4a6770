#include <gflags/gflags.h>
#include <gtest/gtest.h>

#include "cloudfs2/cloudfs2.h"

DECLARE_string(cfs_filesystem_mode);

DEFINE_string(parent_dir, "/parent/dir", "must exist");
DEFINE_string(test_dir, "rename_test", "must not exist under parent_dir");

class RenameTest : public testing::Test {
 public:
  static void SetUpTestSuite() {
    // Set connect parameter to nullptr so that values are load from conf file
    fs_ = cfs_connect(nullptr);
    ASSERT_NE(fs_, nullptr);
    // Make sure parent_dir exists
    auto* finfo = cfs_get_file_info(fs_, FLAGS_parent_dir.c_str());
    ASSERT_NE(finfo, nullptr);
    ASSERT_EQ(finfo->type, CFS_FILE_DIR);
    ASSERT_EQ(cfs_free_file_info(fs_, finfo, 1), 0);

    // Make sure test_dir not eixsts
    std::string test_dir = FLAGS_parent_dir + "/" + FLAGS_test_dir;
    ASSERT_EQ(cfs_exist(fs_, test_dir.c_str()), 0);
  }

  static void TearDownTestSuite() {
    int32_t res = cfs_disconnect(fs_);
    ASSERT_EQ(res, 0);
  }

 protected:
  void SetUp() override {
    test_dir_ = FLAGS_parent_dir + "/" + FLAGS_test_dir;
    ASSERT_EQ(cfs_mkdir(fs_, test_dir_.c_str(), 0755, false), 0);
  }

  void TearDown() override {
    ASSERT_EQ(cfs_delete(fs_, test_dir_.c_str(), true), 0);
  }

 protected:
  static cfs_fs* fs_;

  std::string test_dir_;
};

cfs_fs* RenameTest::fs_ = nullptr;

TEST_F(RenameTest, RenameDir) {
  std::string rename_src = test_dir_ + "/rename_src_dir";
  std::string rename_dst = test_dir_ + "/rename_dst_dir";
  std::string rename_exist = test_dir_ + "/rename_exist_dir";

  int32_t res = cfs_mkdir(fs_, rename_src.c_str(), 0755, false);
  ASSERT_EQ(res, 0);
  res = cfs_mkdir(fs_, rename_exist.c_str(), 0755, false);
  ASSERT_EQ(res, 0);
  ASSERT_EQ(cfs_exist(fs_, rename_src.c_str()), 1);
  ASSERT_EQ(cfs_exist(fs_, rename_dst.c_str()), 0);
  ASSERT_EQ(cfs_exist(fs_, rename_exist.c_str()), 1);

  // normal rename
  res = cfs_rename(fs_, rename_src.c_str(), rename_dst.c_str(), false);
  ASSERT_EQ(res, 0);
  ASSERT_EQ(cfs_exist(fs_, rename_src.c_str()), 0);
  ASSERT_EQ(cfs_exist(fs_, rename_dst.c_str()), 1);

  // rename to existed path with overwrite == false should fail
  res = cfs_rename(fs_, rename_dst.c_str(), rename_exist.c_str(), false);
  ASSERT_EQ(res, -1);
  ASSERT_EQ(cfs_exist(fs_, rename_dst.c_str()), 1);
  ASSERT_EQ(cfs_exist(fs_, rename_exist.c_str()), 1);

  // overwrite non-empty exist dir should fail
  std::string sub_dir = rename_exist + "/sub_dir";
  ASSERT_EQ(cfs_mkdir(fs_, sub_dir.c_str(), 0755, false), 0);
  res = cfs_rename(fs_, rename_dst.c_str(), rename_exist.c_str(), true);
  ASSERT_EQ(res, -1);
  ASSERT_EQ(cfs_exist(fs_, rename_dst.c_str()), 1);

  // overwrite empty exist dir should success
  ASSERT_EQ(cfs_delete(fs_, sub_dir.c_str(), false), 0);
  res = cfs_rename(fs_, rename_dst.c_str(), rename_exist.c_str(), true);
  ASSERT_EQ(res, 0);
  ASSERT_EQ(cfs_exist(fs_, rename_dst.c_str()), 0);
  ASSERT_EQ(cfs_exist(fs_, rename_exist.c_str()), 1);
}

static void WriteFile(cfs_fs* fs, const std::string& path) {
  constexpr uint64_t kBufSize = 1024 * 128;
  std::array<char, kBufSize> buf;
  buf.fill('1');
  cfs_open_option_t open_opts = {
      .flags = CFS_O_WRONLY | CFS_O_CREATE | CFS_O_TRUNC,
      .mode = 0644,
      .create_parent = true,
      .replication = 0,
      .force = false};

  cfs_file* file = cfs_open(fs, path.c_str(), &open_opts);
  ASSERT_NE(file, nullptr);
  int64_t written_size = cfs_write(file, buf.data(), buf.size());
  ASSERT_GT(written_size, 0);
  ASSERT_EQ(cfs_close(file), 0);
}

TEST_F(RenameTest, RenameRegularFile) {
  std::string rename_src = test_dir_ + "/rename_src_file";
  std::string rename_dst = test_dir_ + "/rename_dst_file";
  std::string rename_exist = test_dir_ + "/rename_exist_file";

  WriteFile(fs_, rename_src);
  WriteFile(fs_, rename_exist);

  ASSERT_EQ(cfs_exist(fs_, rename_src.c_str()), 1);
  ASSERT_EQ(cfs_exist(fs_, rename_dst.c_str()), 0);
  ASSERT_EQ(cfs_exist(fs_, rename_exist.c_str()), 1);

  // normal rename
  int32_t res = cfs_rename(fs_, rename_src.c_str(), rename_dst.c_str(), false);
  ASSERT_EQ(res, 0);
  ASSERT_EQ(cfs_exist(fs_, rename_src.c_str()), 0);
  ASSERT_EQ(cfs_exist(fs_, rename_dst.c_str()), 1);

  // rename to existed path with overwrite == false should fail
  res = cfs_rename(fs_, rename_dst.c_str(), rename_exist.c_str(), false);
  ASSERT_EQ(res, -1);
  ASSERT_EQ(cfs_exist(fs_, rename_dst.c_str()), 1);
  ASSERT_EQ(cfs_exist(fs_, rename_exist.c_str()), 1);

  // rename to existed path with overwrite == true should succeed
  res = cfs_rename(fs_, rename_dst.c_str(), rename_exist.c_str(), true);
  ASSERT_EQ(res, 0);
  ASSERT_EQ(cfs_exist(fs_, rename_dst.c_str()), 0);
  ASSERT_EQ(cfs_exist(fs_, rename_exist.c_str()), 1);
}

// Rename Dir to RegularFile and vise versa
TEST_F(RenameTest, RenameDirToRegular) {
  std::string rename_dir = test_dir_ + "/rename_dir";
  std::string rename_file = test_dir_ + "/rename_file";

  ASSERT_EQ(cfs_mkdir(fs_, rename_dir.c_str(), 0755, false), 0);
  WriteFile(fs_, rename_file);

  ASSERT_EQ(cfs_exist(fs_, rename_dir.c_str()), 1);
  ASSERT_EQ(cfs_exist(fs_, rename_file.c_str()), 1);

  int32_t res = cfs_rename(fs_, rename_dir.c_str(), rename_file.c_str(), false);
  ASSERT_EQ(res, -1);
  res = cfs_rename(fs_, rename_dir.c_str(), rename_file.c_str(), true);
  ASSERT_EQ(res, -1);
  ASSERT_EQ(cfs_exist(fs_, rename_dir.c_str()), 1);
  ASSERT_EQ(cfs_exist(fs_, rename_file.c_str()), 1);
}

TEST_F(RenameTest, RenameRegularToDir) {
  std::string rename_dir = test_dir_ + "/rename_dir";
  std::string rename_file = test_dir_ + "/rename_file";

  ASSERT_EQ(cfs_mkdir(fs_, rename_dir.c_str(), 0755, false), 0);
  WriteFile(fs_, rename_file);

  ASSERT_EQ(cfs_exist(fs_, rename_dir.c_str()), 1);
  ASSERT_EQ(cfs_exist(fs_, rename_file.c_str()), 1);

  int32_t res = cfs_rename(fs_, rename_file.c_str(), rename_dir.c_str(), false);
  ASSERT_EQ(res, -1);
  res = cfs_rename(fs_, rename_file.c_str(), rename_dir.c_str(), true);
  ASSERT_EQ(res, -1);
  ASSERT_EQ(cfs_exist(fs_, rename_file.c_str()), 1);
  ASSERT_EQ(cfs_exist(fs_, rename_dir.c_str()), 1);
}

TEST_F(RenameTest, SrcNotExist) {
  std::string rename_src = test_dir_ + "/noexist_src_file";
  std::string rename_dst = test_dir_ + "/noexist_dst_file";

  ASSERT_EQ(cfs_exist(fs_, rename_src.c_str()), 0);
  ASSERT_EQ(cfs_exist(fs_, rename_dst.c_str()), 0);

  int32_t res = cfs_rename(fs_, rename_src.c_str(), rename_dst.c_str(), false);
  ASSERT_EQ(res, -1);
  res = cfs_rename(fs_, rename_src.c_str(), rename_dst.c_str(), true);
  ASSERT_EQ(res, -1);
  ASSERT_EQ(cfs_exist(fs_, rename_src.c_str()), 0);
  ASSERT_EQ(cfs_exist(fs_, rename_dst.c_str()), 0);
}

int main(int argc, char** argv) {
  // InitGoogleTest must be put before ParseCommandLineFlags because
  // 'ParseCommandLineFlags' does not known '--gtest_filter' and will report
  // error
  testing::InitGoogleTest(&argc, argv);
  gflags::ParseCommandLineFlags(&argc, &argv, true);
  return RUN_ALL_TESTS();
}