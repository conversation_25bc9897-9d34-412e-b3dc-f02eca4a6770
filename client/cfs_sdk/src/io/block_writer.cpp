#include "io/block_writer.h"

#include <gflags/gflags.h>

#include "common/datetime_util.h"
#include "common/logger.h"
#include "common/net_util.h"
#include "common/nic_manager.h"
#include "common/string_util.h"
#include "common/thread_pool.h"
#include "io/nic_selector.h"
#include "io/slow_node_manager.h"
#include "limiter/rate_limiter_factory.h"
#include "metric/cfs_metrics.h"

DECLARE_bool(cfs_enable_multi_data_nic);
DECLARE_string(cfs_expect_dn_subnet);
DECLARE_bool(cfs_enable_hpc);
DECLARE_string(cfs_rdma_tag);
DECLARE_string(cfs_vpc_tag);
DECLARE_int64(cfs_dn_rpc_max_retry_time_ms);
DECLARE_int64(cfs_dn_rpc_retry_interval_ms);
DECLARE_bool(cfs_enable_region_aware);
DECLARE_bool(cfs_enable_multi_data_nic);
DECLARE_uint32(cfs_multi_data_nic_selector);

namespace cfs {

extern thread_local uint32_t g_cur_thread_dispatch_id;

namespace internal {

extern thread_local ThreadPool* g_cur_thread_pool;

static const std::string kRdmaTagPrefix = "rdma-";

BlockWriter::BlockWriter(const std::string& path, const LocatedBlock& blk,
                         bool sync, FileSystem* fs, int64_t trace_id,
                         uint64_t file_id)
    : file_path_(path),
      located_blk_(blk),
      dn_infos_(located_blk_.GetLocations()),
      sync_(sync),
      fs_(fs),
      dn_client_(fs, trace_id, blk.GetBlockId(), file_path_, file_id) {
  // FileImpl haved ensured that the locs can not be empty
  CFS_DCHECK_GT(dn_infos_.size(), 0);

  dead_dns_.resize(dn_infos_.size(), false);

  std::string rdma_tag;
  std::string vpc_tag;
  if (FLAGS_cfs_enable_hpc) {
    if (!FLAGS_cfs_rdma_tag.empty()) {
      rdma_tag = kRdmaTagPrefix + FLAGS_cfs_rdma_tag;
    }
    vpc_tag = FLAGS_cfs_vpc_tag;
  }
  channel_handles_.reserve(dn_infos_.size());
  for (uint32_t i = 0; i < dn_infos_.size(); ++i) {
    EndPoint local_ep;
    EndPoint remote_ep;
    valid_ips_.push_back(dn_infos_[i].FilterValidIp(
        rdma_tag, FLAGS_cfs_enable_hpc, fs_->GetMetricRegistryId()));
    if (valid_ips_[i].opt_ips.empty()) {
      // no dn-ip matches rdma-tag, vpc-tag and dn-subnet. Push the failed DNs
      // into failed_list and fail fast below.
      CFSLOG(WARN,
             "Fail to select DN optIp, client_rdma_tag={}, client_vpc_tag={}, "
             "expect_dn_subnet={}, dn_info=[{}]",
             rdma_tag, vpc_tag, FLAGS_cfs_expect_dn_subnet,
             dn_infos_[i].ToString());
      dead_dns_[i] = true;
      ++dead_dn_num_;
      remote_ep = EndPoint(dn_infos_[i].GetIpAddr(), std::string(),
                           dn_infos_[i].GetByteRpcPort());
    } else {
      auto local_res = NicSelector::SelectLocalNic(
          dn_infos_[i], valid_ips_[i].is_vpc, fs_->GetClientId());
      std::string local_ip;
      if (local_res) {
        local_ip = (*local_res)->GetAddrStr();
      } else {
        local_ip = NICManager::GetInstance()->GetMainIp();
      }
      auto remote_ip = NicSelector::SelectRemoteIp(
          dn_infos_[i], valid_ips_[i].opt_ips, local_ip, fs_->GetClientId());
      remote_ep = EndPoint(dn_infos_[i].GetIpAddr(), remote_ip,
                           dn_infos_[i].GetByteRpcPort());
      if (local_res) {
        bool is_ipv6 = NetUtil::IsIpv6(remote_ip);
        local_ep = EndPoint(NICManager::GetInstance()->GetMainIp(),
                            (*local_res)->GetIp(is_ipv6), 0,
                            (*local_res)->GetAddrStr());
      } else {
        // use vpc NIC (i.e. default NIC)
        // update the conn_ip_str used for trace-print.
        local_ep.SetConnIpStr("DEFAULT");
      }
      CFSLOG(DEBUG,
             "Success select DN optIp when init BlockWriter, blkid={}, "
             "DN=[{}], remote_nic={}({}), local_nic={}",
             located_blk_.GetBlockId(), dn_infos_[i].ToString(), remote_ip,
             valid_ips_[i].opt_tag, local_ep.GetConnectIp());
    }

    auto dn_trans = fs_->GetDnTransType();
    if (valid_ips_[i].is_vpc) {
      dn_trans = byterpc::TYPE_KERNEL_TCP;
    }
    channel_handles_.emplace_back(std::move(remote_ep), std::move(local_ep),
                                  dn_trans);
    channel_handles_[i].SetRemotePortReliable(
        dn_infos_[i].IsByteRpcPortReliable());
  }
}

std::vector<DatanodeInfo> BlockWriter::GetDeadDns() const {
  CFS_DCHECK_EQ(dead_dns_.size(), dn_infos_.size());
  std::vector<DatanodeInfo> ret;
  for (uint32_t i = 0; i < dead_dns_.size(); ++i) {
    if (dead_dns_[i]) {
      ret.push_back(dn_infos_[i]);
    }
  }
  return ret;
}

void BlockWriter::CreateBlock(BlkWriterCreateBlockCallback callback) {
  // We use shared_ptr here because the CreateBlockContext is shared by
  // every replicas.
  auto ctx = std::make_shared<CreateBlockContext>();
  ctx->replica_num = dn_infos_.size();
  ctx->ongoing_num = dn_infos_.size();
  ctx->callback = std::move(callback);
  // prepare storage_uuids list so that OnCreateBlockDone can write the uuid
  // to corresponding slots.
  ctx->storage_ids.resize(dn_infos_.size());

  if (dead_dn_num_ > 0) {
    // Fail fast because we can not select matching optIps of some DNs
    ctx->ongoing_num = 0;
    BlkWriterOpResult result;
    result.err_code = CFS_ERR_DN_INVALID_IP;
    result.failed_dns = GetDeadDns();
    ctx->callback(result);
    return;
  }

  DNClientCreateBlockOptions options;
  options.lb = &located_blk_;
  options.sync = sync_;

  for (uint32_t i = 0; i < dn_infos_.size(); ++i) {
    options.channel_handle = &channel_handles_[i];
    options.storage_type = located_blk_.GetStorageTypes()[i];
    auto done = [ctx, i, this](Result<DNCreateBlkResult> res) {
      OnCreateBlockDone(ctx.get(), std::move(res), i);
    };
    dn_client_.CreateBlock(options, std::move(done));
  }
}

void BlockWriter::WriteBlock(BlkWriterWriteBlockOptions options,
                             BlkWriterWriteBlockCallback callback) {
  // We use shared_ptr here because the context is shared by every replicas.
  auto ctx = std::make_shared<WriteBlockContext>();
  ctx->replica_num = dn_infos_.size();
  ctx->ongoing_num = dn_infos_.size();
  ctx->callback = std::move(callback);
  ctx->data = options.data;
  ctx->offset = options.offset_in_blk;
  ctx->visible_length = options.visible_length;

  pending_reqs_.push_back(std::move(ctx));
  if (pending_reqs_.size() > 1) {
    // pre-order req has been waiting. enqueue this req and the pre-order req
    // will wake up and send all pending reqs later
    return;
  }
  TrySendPendingReqs();
}

void BlockWriter::TrySendPendingReqs() {
  while (!pending_reqs_.empty()) {
    auto& first_ctx_ref = pending_reqs_.front();

    if (write_failed_) {
      first_ctx_ref->ongoing_num = 0;
      CFSDLOG(INFO,
              "Cancel pending write_reqs because previous req failed, path={}, "
              "blkid={}, off={}, len={}",
              file_path_, located_blk_.GetBlockId(), first_ctx_ref->offset,
              first_ctx_ref->data->size());
      auto ctx = std::move(pending_reqs_.front());
      pending_reqs_.pop_front();
      // TODO(dbc) We can not call ctx->callback() directly here because it will
      // cause deadlock (this function may already holds
      // FileImpl::write_list_mtx_, and the callback will also retry to acquire
      // that lock).
      // After we eliminate FileImpl::write_list_mtx_ in the future, we can
      // directly call callback here.
      g_cur_thread_pool->AddTask(
          [ctx1 = std::move(ctx)]() {
            BlkWriterOpResult res1;
            res1.err_code = CFS_ERR_INTERNAL_ERROR;
            ctx1->callback(res1);
          },
          g_cur_thread_dispatch_id);
      continue;
    }

    uint64_t bytes = first_ctx_ref->data->size() * first_ctx_ref->replica_num;
    int64_t wait_us = 0;
    auto* limiter = RateLimiterFactory::GetWLimiter();
    if (limiter != nullptr) {
      if (!limiter->TryAcquire(bytes, &wait_us)) {
        if (flying_bytes_ == 0) {
          int64_t wait_ms = std::max(wait_us / 1000, 1L);
          CFSDLOG(
              DEBUG,
              "Delay WriteBock for {}ms because rate limit, path={}, blkid={}",
              wait_ms, file_path_, located_blk_.GetBlockId());
          g_cur_thread_pool->AddDelayTask(
              [this]() {
                TrySendPendingReqs();
              },
              wait_ms, g_cur_thread_dispatch_id);
        } else {
          // there is flying WriteBlock, when the response returns, they will
          // send-pending-reqs. So we do not need to add delay-task here.
        }
        return;
      } else {
        // got permits, continue to send req
      }
    }
    auto ctx = std::move(pending_reqs_.front());
    pending_reqs_.pop_front();
    flying_bytes_ += ctx->data->size() * ctx->replica_num;

    DNClientWriteBlockOptions opts;
    opts.lb = &located_blk_;
    opts.data = ctx->data;
    opts.offset = ctx->offset;
    opts.visible_length = ctx->visible_length;
    opts.sync = sync_;

    for (uint32_t i = 0; i < dn_infos_.size(); ++i) {
      opts.channel_handle = &channel_handles_[i];
      dn_client_.WriteBlock(opts, [ctx, i, this](Result<DNWriteBlkResult> res) {
        OnWriteBlockDone(std::move(ctx), std::move(res), i);
      });
    }
  }
}

void BlockWriter::SealBlock(int64_t blk_length, bool need_retry,
                            BlkWriterSealBlockCallback callback) {
  // We use shared_ptr here because the context is shared by every replicas.
  auto ctx = std::make_shared<SealBlockContext>();
  ctx->replica_num = dn_infos_.size();
  ctx->callback = std::move(callback);
  ctx->need_retry = need_retry;
  ctx->start_time_ms = DatetimeUtil::GetNowTimeMs();

  DNClientSealBlockOptions opts;
  opts.lb = &located_blk_;
  opts.block_length = blk_length;
  opts.retry_once = need_retry;
  DoSealBlock(std::move(ctx), opts);
}

void BlockWriter::DoSealBlock(std::shared_ptr<SealBlockContext> ctx,
                              DNClientSealBlockOptions& opts) {
  ctx->ongoing_num = dn_infos_.size();
  ctx->last_send_time_ms = DatetimeUtil::GetNowTimeMs();
  CFS_DCHECK_EQ(channel_handles_.size(), dn_infos_.size());
  for (uint32_t i = 0; i < dn_infos_.size(); ++i) {
    opts.channel_handle = &channel_handles_[i];
    dn_client_.SealBlock(
        opts, [ctx, opts, i, this](Result<DNSealBlkResult> res) {
          OnSealBlockDone(std::move(ctx), opts, std::move(res), i);
        });
  }
}

void BlockWriter::FinalizeBlock(int64_t blk_length,
                                BlkWriterOpCallback callback) {
  auto ctx = std::make_shared<FinalizeBlockContext>();
  ctx->replica_num = dn_infos_.size();
  ctx->ongoing_num = dn_infos_.size();
  ctx->callback = std::move(callback);

  DNFinBlkOpts opts;
  opts.lb = &located_blk_;
  opts.block_length = blk_length;
  CFSDLOG(DEBUG, "Start finalize block, path={}, blkid={}, blk_len={}, dn=[{}]",
          file_path_, located_blk_.GetBlockId(), blk_length,
          located_blk_.GetLocationShortString());
  for (uint32_t i = 0; i < dn_infos_.size(); ++i) {
    opts.channel_handle = &channel_handles_[i];
    // 'opts' is captured because it is needed when writing to a redirect port
    // in OnFinalizeBlockDone.
    auto done = [ctx, opts, i, this](Result<uint32_t> res) {
      OnFinalizeBlockDone(std::move(ctx), opts, std::move(res), i);
    };
    dn_client_.FinalizeBlock(opts, std::move(done));
  }
}

void BlockWriter::SyncBlock(BlkWriterOpCallback callback) {
  auto ctx = std::make_shared<SyncBlockContext>();
  ctx->replica_num = dn_infos_.size();
  ctx->ongoing_num = dn_infos_.size();
  ctx->callback = std::move(callback);

  DNClientSyncBlockOptions opts;
  opts.lb = &located_blk_;

  // channel_handles_ must have been prepared in CreateBlock
  CFS_DCHECK_EQ(channel_handles_.size(), dn_infos_.size());
  for (uint32_t i = 0; i < dn_infos_.size(); ++i) {
    opts.channel_handle = &channel_handles_[i];
    dn_client_.SyncBlock(opts, [ctx, opts, i, this](Result<uint32_t> res) {
      OnSyncBlockDone(std::move(ctx), std::move(opts), std::move(res), i);
    });
  }
}

void BlockWriter::PingBlock(int64_t visible_len, bool need_retry,
                            BlkWriterOpCallback callback) {
  auto ctx = std::make_shared<PingBlockContext>();
  ctx->replica_num = dn_infos_.size();
  ctx->callback = std::move(callback);
  ctx->need_retry = need_retry;
  ctx->start_time_ms = DatetimeUtil::GetNowTimeMs();

  DNClientPingBlockOptions opts;
  opts.lb = &located_blk_;
  opts.visible_length = visible_len;
  opts.retry_once = need_retry;

  DoPingBlock(std::move(ctx), opts);
}

void BlockWriter::DoPingBlock(std::shared_ptr<PingBlockContext> ctx,
                              DNClientPingBlockOptions& opts) {
  ctx->ongoing_num = dn_infos_.size();
  ctx->last_send_time_ms = DatetimeUtil::GetNowTimeMs();
  CFS_DCHECK_EQ(channel_handles_.size(), dn_infos_.size());
  for (uint32_t i = 0; i < dn_infos_.size(); ++i) {
    opts.channel_handle = &channel_handles_[i];
    dn_client_.PingBlock(opts, [ctx, opts, i, this](Result<uint32_t> res) {
      OnPingBlockDone(std::move(ctx), std::move(opts), std::move(res), i);
    });
  }
}

void BlockWriter::OnCreateBlockDone(CreateBlockContext* ctx,
                                    Result<DNCreateBlkResult> res,
                                    uint32_t dn_idx) {
  if (res.IsOk()) {
    CFSDLOG(DEBUG,
            "Succeed to CreateBlock on channel={}, path={}, blkid={}, port={}, "
            "storage_id={}",
            channel_handles_[dn_idx].ToString(), file_path_,
            located_blk_.GetBlockId(), res.GetValue().port,
            res.GetValue().storage_id);
    UpdateChannelPort(dn_idx, res.GetValue().port);
    // We have resized ctx->storage_ids above
    CFS_DCHECK_GT(ctx->storage_ids.size(), dn_idx);
    ctx->storage_ids[dn_idx] = res.GetValue().storage_id;
  } else {
    CFSLOG(ERROR, "Fail to create block, path={}, blkid={}, channel={}, err={}",
           file_path_, located_blk_.GetBlockId(),
           channel_handles_[dn_idx].ToString(), res.GetError());
    ctx->failed_dns.push_back(dn_infos_[dn_idx]);
    ctx->failed_results.push_back(res.GetError());
  }

  // No need to use lock or atomic here because byterpc use Run-To-Complete
  // thread model. All callbacks of this block run in the same thread.
  --(ctx->ongoing_num);
  if (ctx->ongoing_num == 0) {
    // last block
    BlkWriterOpResult result;
    if (!ctx->failed_results.empty()) {
      result.err_code = ctx->failed_results[0].err_code;
      result.failed_dns.swap(ctx->failed_dns);
    } else {
      CFSLOG(DEBUG, "Success to CreateBlock, path={}, blkid={}, dn=[{}]",
             file_path_, located_blk_.GetBlockId(),
             located_blk_.GetLocationShortString());
    }
    result.create_blk_storage_ids = std::move(ctx->storage_ids);
    ctx->callback(result);
  }
}

void BlockWriter::OnWriteBlockDone(std::shared_ptr<WriteBlockContext> ctx,
                                   Result<DNWriteBlkResult> res,
                                   uint32_t dn_idx) {
  if (!res.IsOk()) {
    CFSLOG(ERROR,
           "Failed to write block, path={}, blkid={}, blk_off={}, len={}, "
           "DN={}, err={}",
           file_path_, located_blk_.GetBlockId(), ctx->offset,
           ctx->data->size(), channel_handles_[dn_idx].ToString(),
           res.GetError());
    ctx->failed_dns.push_back(dn_infos_[dn_idx]);
    ctx->failed_results.push_back(res.GetError());
    write_failed_ = true;
  } else if (res.GetValue().redirect_port > 0) {
    // Retry sending this RPC to the "redirect_port"
    CFSLOG(INFO,
           "Retry writing block because of redirecting port, file={}, "
           "old_port={}, new_port={}",
           file_path_, channel_handles_[dn_idx].GetRemoteEndPoint().GetPort(),
           res.GetValue().redirect_port);
    UpdateChannelPort(dn_idx, res.GetValue().redirect_port);
    DNClientWriteBlockOptions opts;
    opts.lb = &located_blk_;
    opts.data = ctx->data;
    opts.offset = ctx->offset;
    opts.visible_length = ctx->visible_length;
    opts.sync = sync_;
    opts.channel_handle = &channel_handles_[dn_idx];
    auto done = [ctx1 = std::move(ctx), dn_idx,
                 this](Result<DNWriteBlkResult> res) {
      OnWriteBlockDone(std::move(ctx1), std::move(res), dn_idx);
    };
    dn_client_.WriteBlock(opts, std::move(done));
    return;
  } else {
    CFSDLOG(
        DEBUG,
        "Succeed to write block, path={}, blkid={}, channel={}, blk_off={}, "
        "len={}.",
        file_path_, located_blk_.GetBlockId(),
        channel_handles_[dn_idx].ToString(), ctx->offset, ctx->data->size());
    const auto& local_az = fs_->GetLocationTag().az_;
    const auto& remote_az = dn_infos_[dn_idx].GetLocationAz();
    if (!local_az.empty() && !remote_az.empty()) {
      auto tk = StringUtil::StrFormat("{}_TO_{}", local_az, remote_az);
      CfsMetrics::GetInstance()->SumPSIncWithRegister(
          fs_->GetMetricRegistryId(), "az_thpt_ps_write", {{"direction", tk}},
          false, ctx->data->size());
    } else if (FLAGS_cfs_enable_region_aware) {
      METRIC_SPS_INC(fs_->GetMetricRegistryId(), az_thpt_ps_unknown_az_write,
                     ctx->data->size());
    }
    // record write rpc latency for slow node detection
    SlowNodeManager::GetInstance()->RecordLatencyUs(
        dn_infos_[dn_idx], MediaType::TYPE_LOCAL_STORE,
        res.GetValue().write_rpc_latency_us, true);
  }

  flying_bytes_ -= ctx->data->size();
  --(ctx->ongoing_num);
  if (ctx->ongoing_num == 0) {
    // last block
    BlkWriterOpResult result;
    if (!ctx->failed_results.empty()) {
      result.err_code = ctx->failed_results[0].err_code;
      result.failed_dns.swap(ctx->failed_dns);
    }
    ctx->callback(result);
  }
  TrySendPendingReqs();
}

// Retrun true if at lease 1 err in `errs` is retriable
static bool ExistFailRetriable(const std::vector<Status> errs) {
  if (errs.empty()) {
    return true;
  }
  for (const auto& e : errs) {
    if (RpcUtil::IsRpcErrRetriable(e.err_code)) {
      return true;
    }
  }
  return false;
}

void BlockWriter::OnSealBlockDone(std::shared_ptr<SealBlockContext> ctx,
                                  DNClientSealBlockOptions opts,
                                  Result<DNSealBlkResult> res,
                                  uint32_t dn_idx) {
  if (!res.IsOk()) {
    ctx->failed_dns.push_back(dn_infos_[dn_idx]);
    ctx->failed_results.push_back(res.GetError());
    CFSLOG(WARN,
           "Fail to SealBlock on 1 DN, path={}, blkid={}, seal_len={}, "
           "channel={}, err={}",
           file_path_, located_blk_.GetBlockId(), opts.block_length,
           channel_handles_[dn_idx].ToString(), res.GetError());
  } else if (res.GetValue().redirect_port > 0) {
    // Retry sending this RPC to the "redirect_port"
    CFSLOG(INFO,
           "Retry SealBlock because of redirect port, path={}, blkid={}, "
           "old_port={}, new_port={}",
           file_path_, located_blk_.GetBlockId(),
           channel_handles_[dn_idx].GetRemoteEndPoint().GetPort(),
           res.GetValue().redirect_port);
    UpdateChannelPort(dn_idx, res.GetValue().redirect_port);
    opts.channel_handle = &channel_handles_[dn_idx];
    auto done = [ctx = std::move(ctx), opts, dn_idx,
                 this](Result<DNSealBlkResult> res) {
      OnSealBlockDone(std::move(ctx), opts, std::move(res), dn_idx);
    };
    dn_client_.SealBlock(opts, std::move(done));
    return;
  } else {
    if (opts.block_length < 0) {
      // seal-blk-with-no-len
      CFSLOG(INFO,
             "SealBlock and query len form DN success, path={}, blkid={}, "
             "DN={}, len={}",
             file_path_, located_blk_.GetBlockId(),
             dn_infos_[dn_idx].GetIpAddr(), res.GetValue().block_length);
    } else {
      CFSDLOG(DEBUG,
              "Succeed to SealBlock on channel={}, path={}, blkid={}, len={}",
              channel_handles_[dn_idx].ToString(), file_path_,
              located_blk_.GetBlockId(), res.GetValue().block_length);
    }
    if (ctx->min_seal_len == -1) {
      ctx->min_seal_len = res.GetValue().block_length;
    } else {
      ctx->min_seal_len =
          std::min(ctx->min_seal_len, res.GetValue().block_length);
    }
  }

  --(ctx->ongoing_num);
  if (ctx->ongoing_num > 0) {
    return;
  }

  BlkWriterOpResult result;
  result.min_seal_len = ctx->min_seal_len;
  // 1. All DN success
  if (ctx->failed_results.empty()) {
    CFSLOG(DEBUG, "Success to SealBlock, path={}, blkid={}, dn=[{}]",
           file_path_, located_blk_.GetBlockId(),
           located_blk_.GetLocationShortString());
    ctx->callback(result);
    return;
  }

  // 2. partial DN success
  result.failed_dns.swap(ctx->failed_dns);
  auto succ_num = ctx->replica_num - ctx->failed_results.size();
  if (succ_num > 0) {
    CFSLOG(WARN,
           "Seal block partial success, path={}, blkid={}, succ_num={}, "
           "fail_num={}",
           file_path_, located_blk_.GetBlockId(), succ_num,
           ctx->failed_results.size());
    ctx->callback(result);
    return;
  }

  // 3. All Dn fail
  auto now_ms = DatetimeUtil::GetNowTimeMs();
  auto tried_ms = now_ms - ctx->start_time_ms;
  if (ctx->need_retry && (tried_ms < FLAGS_cfs_dn_rpc_max_retry_time_ms) &&
      ExistFailRetriable(ctx->failed_results)) {
    ctx->failed_dns.clear();
    ctx->failed_results.clear();
    int64_t sleep_ms = std::max(0L, FLAGS_cfs_dn_rpc_retry_interval_ms -
                                        (now_ms - ctx->last_send_time_ms));
    CFSLOG(WARN,
           "All Dn fail to SealBlock, path={}, blkid={}. Retry after {}ms, "
           "already tried for {}ms",
           file_path_, located_blk_.GetBlockId(), sleep_ms, tried_ms);
    if (sleep_ms > 0) {
      CFSDLOG(INFO, "Add SealBlock DelayTask, path={}, blkid={}", file_path_,
              located_blk_.GetBlockId());
      CFS_DCHECK(g_cur_thread_pool != nullptr);
      g_cur_thread_pool->AddDelayTask(
          [ctx = std::move(ctx), opts, this]() mutable {
            DoSealBlock(std::move(ctx), opts);
          },
          sleep_ms, g_cur_thread_dispatch_id);
    } else {
      DoSealBlock(std::move(ctx), opts);
    }
  } else {
    result.err_code = ctx->failed_results[0].err_code;
    CFSLOG(ERROR,
           "Seal block fail, path={}, blkid={}, succ_num={}, fail_num={}",
           file_path_, located_blk_.GetBlockId(), succ_num,
           ctx->failed_results.size());
    ctx->callback(result);
  }
}

void BlockWriter::OnSyncBlockDone(std::shared_ptr<SyncBlockContext> ctx,
                                  DNClientSyncBlockOptions opts,
                                  Result<uint32_t> res, uint32_t dn_idx) {
  if (!res.IsOk()) {
    ctx->failed_dns.push_back(dn_infos_[dn_idx]);
    ctx->failed_results.push_back(res.GetError());
    CFSLOG(WARN, "Failed to Sync block, file={}, blkid={}, channel={}, err={}",
           file_path_, located_blk_.GetBlockId(),
           channel_handles_[dn_idx].ToString(), res.GetError());
  } else if (res.GetValue() > 0) {
    // redirector port is not 0. Retry sending this RPC to the "redirect_port"
    CFSLOG(INFO,
           "Retry sync block because of redirecting port, file={}, "
           "old_port={}, new_port={}",
           file_path_, channel_handles_[dn_idx].GetRemoteEndPoint().GetPort(),
           res.GetValue());
    UpdateChannelPort(dn_idx, res.GetValue());
    opts.channel_handle = &channel_handles_[dn_idx];
    dn_client_.SyncBlock(opts, [ctx = std::move(ctx), opts, dn_idx,
                                this](Result<uint32_t> res) {
      OnSyncBlockDone(std::move(ctx), std::move(opts), std::move(res), dn_idx);
    });
    return;
  } else {
    // Sync success
    CFSLOG(DEBUG, "Succeed to Sync block, file={}, blkid={}, channel={}.",
           file_path_, located_blk_.GetBlockId(),
           channel_handles_[dn_idx].ToString());
  }

  --(ctx->ongoing_num);
  if (ctx->ongoing_num == 0) {
    BlkWriterOpResult result;
    if (!ctx->failed_results.empty()) {
      auto succ_num = ctx->replica_num - ctx->failed_results.size();
      if (succ_num > 0) {
        CFSLOG(WARN,
               "Sync block partial success, path={}, blkid={}, succ_num={}, "
               "fail_num={}",
               file_path_, located_blk_.GetBlockId(), succ_num,
               ctx->failed_results.size());
      } else {
        result.err_code = ctx->failed_results[0].err_code;
        CFSLOG(ERROR,
               "Sync block fail, path={}, blkid={}, succ_num={}, fail_num={}",
               file_path_, located_blk_.GetBlockId(), succ_num,
               ctx->failed_results.size());
      }
      result.failed_dns.swap(ctx->failed_dns);
    } else {
      CFSLOG(INFO, "Success to Sync block, path={}, blkid={}", file_path_,
             located_blk_.GetBlockId());
    }
    ctx->callback(result);
  }
}

void BlockWriter::OnFinalizeBlockDone(std::shared_ptr<FinalizeBlockContext> ctx,
                                      DNFinBlkOpts opts, Result<uint32_t> res,
                                      uint32_t dn_idx) {
  if (!res.IsOk()) {
    ctx->failed_dns.push_back(dn_infos_[dn_idx]);
    ctx->failed_results.push_back(res.GetError());
    CFSLOG(WARN,
           "Fail to Finalize block for one DN, path={}, blkid={}, channel={}, "
           "err={}",
           file_path_, located_blk_.GetBlockId(),
           channel_handles_[dn_idx].ToString(), res.GetError());
  } else if (res.GetValue() > 0) {
    // redirector port is not 0. Retry sending this RPC to the "redirect_port"
    CFSLOG(INFO,
           "Retry finalize block because of redirecting port, path={}, "
           "old_port={}, new_port={}",
           file_path_, channel_handles_[dn_idx].GetRemoteEndPoint().GetPort(),
           res.GetValue());
    UpdateChannelPort(dn_idx, res.GetValue());
    opts.channel_handle = &channel_handles_[dn_idx];
    dn_client_.FinalizeBlock(
        opts, [ctx = std::move(ctx), opts, dn_idx, this](Result<uint32_t> res) {
          OnFinalizeBlockDone(std::move(ctx), std::move(opts), std::move(res),
                              dn_idx);
        });
    return;
  }
  --(ctx->ongoing_num);
  if (ctx->ongoing_num > 0) {
    return;
  }
  // todo-qpc, Merge the following redundant code of
  // create/seal/sync/ping/finalize
  BlkWriterOpResult result;
  if (!ctx->failed_results.empty()) {
    auto succ_num = ctx->replica_num - ctx->failed_results.size();
    if (succ_num > 0) {
      CFSLOG(WARN,
             "Finalize block partial success, path={}, blkid={}, succ_num={}, "
             "fail_num={}",
             file_path_, located_blk_.GetBlockId(), succ_num,
             ctx->failed_results.size());
    } else {
      // all dn failed, no need to retry
      result.err_code = ctx->failed_results[0].err_code;
      CFSLOG(ERROR,
             "Finalize block all DN fail, path={}, blkid={}, fail_num={}",
             file_path_, located_blk_.GetBlockId(), ctx->failed_results.size());
    }
    result.failed_dns.swap(ctx->failed_dns);
  } else {
    CFSLOG(DEBUG, "Success to Finalize block, path={}, blkid={}", file_path_,
           located_blk_.GetBlockId());
  }
  ctx->callback(result);
}

void BlockWriter::OnPingBlockDone(std::shared_ptr<PingBlockContext> ctx,
                                  DNClientPingBlockOptions opts,
                                  Result<uint32_t> res, uint32_t dn_idx) {
  if (!res.IsOk()) {
    CFSLOG(WARN, "Failed to Ping block, path={}, blkid={}, channel={}, err={}",
           file_path_, located_blk_.GetBlockId(),
           channel_handles_[dn_idx].ToString(), res.GetError());
    ctx->failed_dns.push_back(dn_infos_[dn_idx]);
    ctx->failed_results.push_back(res.GetError());
  } else if (res.GetValue() > 0) {
    // redirector port is not 0. Retry sending this RPC to the "redirect_port"
    CFSLOG(INFO,
           "Retry ping block because of redirecting port, file={}, "
           "old_port={}, new_port={}",
           file_path_, channel_handles_[dn_idx].GetRemoteEndPoint().GetPort(),
           res.GetValue());
    UpdateChannelPort(dn_idx, res.GetValue());
    opts.channel_handle = &channel_handles_[dn_idx];
    dn_client_.PingBlock(opts, [ctx = std::move(ctx), opts, dn_idx,
                                this](Result<uint32_t> res) {
      OnPingBlockDone(std::move(ctx), std::move(opts), std::move(res), dn_idx);
    });
    return;
  } else {
    // Ping success
    CFSDLOG(DEBUG, "Succeed to Ping block, file={}, blkid={}, channel={}.",
            file_path_, located_blk_.GetBlockId(),
            channel_handles_[dn_idx].ToString());
  }

  --(ctx->ongoing_num);
  if (ctx->ongoing_num > 0) {
    return;
  }
  BlkWriterOpResult result;
  // 1. All DN success
  if (ctx->failed_results.empty()) {
    CFSDLOG(INFO, "Success to Ping block, path={}, blkid={}", file_path_,
            located_blk_.GetBlockId());
    ctx->callback(result);
    return;
  }

  // 2. partial DN success
  // When partial success, `failed_dns` is also set into `result`.
  // FileImpl decide whether to SwitchWritingBlock according to this.
  result.failed_dns.swap(ctx->failed_dns);
  auto succ_num = ctx->replica_num - ctx->failed_results.size();
  if (succ_num > 0) {
    CFSLOG(WARN,
           "Ping block partial success, path={}, blkid={}, succ_num={}, "
           "fail_num={}",
           file_path_, located_blk_.GetBlockId(), succ_num,
           ctx->failed_results.size());
    ctx->callback(result);
    return;
  }

  // 3. All Dn fail
  auto now_ms = DatetimeUtil::GetNowTimeMs();
  auto tried_ms = now_ms - ctx->start_time_ms;
  if (ctx->need_retry && (tried_ms < FLAGS_cfs_dn_rpc_max_retry_time_ms) &&
      ExistFailRetriable(ctx->failed_results)) {
    ctx->failed_dns.clear();
    ctx->failed_results.clear();
    int64_t sleep_ms = std::max(0L, FLAGS_cfs_dn_rpc_retry_interval_ms -
                                        (now_ms - ctx->last_send_time_ms));
    CFSLOG(WARN,
           "All Dn fail to PingBlock, path={}, blkid={}. Retry after {}ms, "
           "already tried for {}ms",
           file_path_, located_blk_.GetBlockId(), sleep_ms, tried_ms);
    if (sleep_ms > 0) {
      CFSDLOG(INFO, "Add PingBlock DelayTask, path={}, blkid={}", file_path_,
              located_blk_.GetBlockId());
      CFS_DCHECK(g_cur_thread_pool != nullptr);
      g_cur_thread_pool->AddDelayTask(
          [ctx = std::move(ctx), opts, this]() mutable {
            DoPingBlock(std::move(ctx), opts);
          },
          sleep_ms, g_cur_thread_dispatch_id);
    } else {
      DoPingBlock(std::move(ctx), opts);
    }
  } else {
    result.err_code = ctx->failed_results[0].err_code;
    CFSLOG(WARN,
           "All Dn PingBlock fail after retry {}ms, stop retry. path={}, "
           "blkid={}, fail_num={}",
           tried_ms, file_path_, located_blk_.GetBlockId(),
           ctx->failed_results.size());
    ctx->callback(result);
  }
}

void BlockWriter::UpdateChannelPort(uint32_t dn_idx, uint32_t target_port) {
  auto& dn = const_cast<DatanodeInfo&>(dn_infos_[dn_idx]);
  dn.SetByteRpcPortReliable(true);
  channel_handles_[dn_idx].SetRemotePortReliable(true);
  if (channel_handles_[dn_idx].GetRemoteEndPoint().GetPort() == target_port) {
    // no need to update port
    return;
  }
  dn.SetByteRpcPort(target_port);

  if ((!FLAGS_cfs_enable_multi_data_nic) ||
      (FLAGS_cfs_multi_data_nic_selector != 0)) {
    channel_handles_[dn_idx].MutableRemoteEndPoint()->SetPort(target_port);
    return;
  }
  const NICInfo* local_nic = nullptr;
  // multi-data-nic is enabled and use consistent-hash to select NIC
  // Update local-NIC and remote-NIC to save RDMA-QP
  auto local_res = NicSelector::SelectLocalNic(dn, valid_ips_[dn_idx].is_vpc,
                                               fs_->GetClientId());
  std::string local_ip;
  if (local_res) {
    local_nic = *local_res;
    local_ip = local_nic->GetAddrStr();
  } else {
    local_ip = NICManager::GetInstance()->GetMainIp();
  }
  auto remote_ip = NicSelector::SelectRemoteIp(dn, valid_ips_[dn_idx].opt_ips,
                                               local_ip, fs_->GetClientId());
  channel_handles_[dn_idx].SetRemoteEndPoint(
      EndPoint(dn.GetIpAddr(), remote_ip, dn.GetByteRpcPort()));

  if (local_nic != nullptr) {
    bool is_ipv6 = NetUtil::IsIpv6(remote_ip);
    channel_handles_[dn_idx].SetLocalEndPoint(
        EndPoint(NICManager::GetInstance()->GetMainIp(),
                 local_nic->GetIp(is_ipv6), 0, local_nic->GetAddrStr()));
  } else {
    // use vpc NIC (i.e. default NIC)
  }
}

}  // namespace internal
}  // namespace cfs
