#pragma once

#include <deque>
#include <memory>

#include "common/cfs_error.h"
#include "io/datanode_client.h"
#include "protocol/located_block.h"

namespace cfs {
namespace internal {

using BlkReaderGetBlockInfoCallback =
    fu2::unique_function<void(Result<ReplicaInfo>) const>;
using BlkReaderReadBlockCallback =
    fu2::unique_function<void(Result<uint64_t>) const>;

class BlockReader {
 public:
  BlockReader(const std::string& path, uint64_t file_id,
              const LocatedBlock& blk, FileSystem* fs, uint64_t stream_id,
              int64_t trace_id);

  ~BlockReader() = default;

  void ReadBlock(uint64_t offset, uint64_t length, DataHolder* buf,
                 ReadType read_type, BlkReaderReadBlockCallback callback);

  void GetBlockInfo(bool retry_if_no_visible_len, bool retry_if_blk_not_exist,
                    BlkReaderGetBlockInfoCallback callback);

  void GetReplicaOrderAndStatus(std::vector<DatanodeInfo>* replica_list,
                                std::vector<bool>* failed_dns,
                                std::vector<Errorcode>* failed_err);
  void AdjustReplicaOrder();

 private:
  struct ReadBlockContext {
    uint64_t offset;
    uint64_t length;
    DataHolder* blk_buf;
    ReadType read_type;
    // This callback is invoked when all sub-packets finish (success, failed or
    // skipped). It will call 'OnReadBlockDone' internally, which will call
    // user's callback.
    fu2::unique_function<void() const> callback;
    std::vector<std::unique_ptr<DataHolder>> packet_buf;
    uint32_t ongoing_num;
    // The start time of reading this block. Used to retry until exceeding
    // the time limit.
    // Update: this field is replaced by first_start_time_ms in
    // ReadPacketContext
    // int64_t start_time_ms;

    // The failed ReadPacketReq with smallest offset. When this field is not
    // -1, all post-order ReadPacketReqs are thought to be invalid (packets on
    // the fly will be discarded when their responses arrives; packets in the
    // queue waiting to be send will be marked failure without sending out any
    // more)
    int64_t failed_pkt_offset{-1};
    Status failed_pkt_err;

   public:
    bool IsFailed() const {
      return failed_pkt_offset > -1;
    }

    void DecOngoingNum();
  };

  struct ReadPacketContext {
    uint64_t offset;
    uint64_t length;
    // This packet_idx is used to find corresponding DataHolder in
    // 'blk_ctx->packet_buf'
    uint32_t packet_idx;
    uint32_t cur_dn_idx;
    // The first time of reading this packet. It is only set for the first time
    // the request is sent
    int64_t first_start_time_ms;
    // The last time of reading this packet. It will be updated each time
    // retry to ReadPakcet.
    int64_t last_start_time_ms;
    std::shared_ptr<ReadBlockContext> blk_ctx;
  };

  struct GetBlkInfoCtx {
    BlkReaderGetBlockInfoCallback callback;
    bool retry_if_no_visible_len;
    bool retry_if_blk_not_exist;
    uint32_t cur_dn_idx;
    int64_t start_time_ms;
    // The last time of getting blockinfo. It will be updated each time
    // retry to GetBlkInfo.
    int64_t last_send_time_ms;
    std::vector<Status> failed_results;
  };

  void DoGetBlockInfo(std::unique_ptr<GetBlkInfoCtx> ctx);

  void OnGetBlockInfoDone(std::unique_ptr<GetBlkInfoCtx> ctx,
                          Result<ReplicaInfo> res);

  void OnReadBlockDone(ReadBlockContext* ctx,
                       const BlkReaderReadBlockCallback& cb);

  void ReadPacket(std::unique_ptr<ReadPacketContext> ctx);

  void OnReadPacketDone(std::unique_ptr<ReadPacketContext> ctx,
                        Result<DNReadBlkResult> res);

  void SealReplica(std::unique_ptr<ReadPacketContext> ctx);

  void OnSealReplicaDone(std::unique_ptr<ReadPacketContext> ctx,
                         Result<DNSealBlkResult> res);

  void RetryReadPktOrFail(std::unique_ptr<ReadPacketContext> ctx,
                          uint32_t dn_idx, const Status& err);

  void UpdateChannelPort(uint32_t dn_idx, uint32_t target_port);

  uint32_t SwitchNextDN(uint32_t dn_idx);

  void ClearFailedDNs();

  void TrySendPendingReqs();

  void PrepareAndSendPendingReqs();

  std::deque<std::unique_ptr<BlockReader::ReadPacketContext>>
  SplitBlockToPackets(uint64_t offset, uint64_t length,
                      std::shared_ptr<ReadBlockContext> blk_ctx);
  uint32_t GetCurrentDNIndex();

 private:
  std::string file_path_;
  // MUST use value but not ref here! Because LocatedBlock in FileImpl may
  // change after RetryReadAfterRefreshLbs.
  LocatedBlock located_blk_;
  const std::vector<DatanodeInfo>& dn_infos_;
  const FileSystem* fs_;
  DatanodeClient dn_client_;
  // stream_id_ is used by DN to cache data from TOS. Each opened file should
  // have a different stream_id_.
  uint64_t stream_id_;
  // std::map<std::string, uint32_t> dn_ip_to_direct_port_;
  std::vector<ChannelHandle> channel_handles_;
  // Valid opt_ips and dn-rdma-tag. Must have the same lenth with dn_infos_
  std::vector<OptIpAndTag> valid_ips_;
  // When 'is_sealing_blk_' is true, it means there is a flying SealBlockRequest
  // to the current DN. This case happens when current DN with a COMPLETE block
  // lost its visible_length because of some error (e.g. DN restart).
  bool is_sealing_blk_{false};
  bool is_preparing_{false};
  uint32_t cur_dn_idx_{0};
  // This vector has the same size with dn_infos_. It is initialized to be all
  // 'false'. When one DN fail, the corresponding bit is set to 'true'.
  std::vector<bool> failed_dns_;
  // Only used for ReadBlock but not GetBlockInfo
  std::vector<Errorcode> failed_dn_err_;
  // If a DN has no valid optIps matching rdma_tag, vpc_tag and dn_subnet, it
  // is marked as DEAD. And it will not be tried in SwitchNextDN().
  std::vector<bool> dead_dns_;
  uint32_t dead_dn_num_{0};
  // When current flying ReadPacketReqs exceeds the sending window, pending
  // requests are stored in this deque. When the flying reqeusts return, they
  // will send these pending requests in their callback function.
  std::deque<std::unique_ptr<ReadPacketContext>> pending_packet_reqs_;
  uint64_t read_window_bytes_{0};
  // Used for replica reorder in slow node or fail node case
  int64_t update_replica_order_ts_{0};
  std::vector<uint32_t> replica_reorder_index_;
  bool reset_fail_node_stat_{false};
  int64_t reset_fail_node_stat_ts_{0};
};

}  // namespace internal
}  // namespace cfs