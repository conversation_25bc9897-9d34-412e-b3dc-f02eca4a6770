#pragma once

#include <memory>

#include "common/cfs_error.h"
#include "common/data_holder.h"
#include "common/result.h"
#include "impl/filesystem.h"
#include "protocol/storage_type.h"
#include "rpc/byterpc_call.h"
#include "rpc/channel_handle.h"
#include "rpc/rpc_result.h"
#include "rpc/type_traits.h"

namespace cfs {
namespace internal {

enum class ReadType : uint8_t { Read = 0, PRead = 1, KvPRead = 2 };

struct DNClientCreateBlockOptions {
  const LocatedBlock* lb;
  StorageType storage_type;
  bool sync;
  const ChannelHandle* channel_handle;
};

struct DNClientWriteBlockOptions {
  const LocatedBlock* lb;
  const byterpc::IOBuf* data;
  uint64_t offset;
  uint64_t visible_length;
  bool sync;
  const ChannelHandle* channel_handle;
};

struct DNClientSealBlockOptions {
  const LocatedBlock* lb;
  // use int64 but not uint64! -1 means query length from DN
  int64_t block_length;
  bool retry_once;
  const ChannelHandle* channel_handle;
};

struct DNFinBlkOpts {
  const LocatedBlock* lb;
  int64_t block_length;
  int64_t timeout_ms;
  const ChannelHandle* channel_handle;
};

struct DNClientSyncBlockOptions {
  const LocatedBlock* lb;
  const ChannelHandle* channel_handle;
};

struct DNClientPingBlockOptions {
  const LocatedBlock* lb;
  const ChannelHandle* channel_handle;
  int64_t visible_length;
  bool retry_once;
};

struct DNReadBlockOptions {
  const LocatedBlock* lb;
  uint64_t offset;
  uint64_t length;
  ReadType read_type;
  uint64_t stream_id;
  const ChannelHandle* channel_handle;
};

struct DNGetReplicaInfoOptions {
  const LocatedBlock* lb;
  const ChannelHandle* channel_handle;
};

// This class is used to comunicate with Datanode.
class DatanodeClient {
 public:
  explicit DatanodeClient(FileSystem* fs, int64_t trace_id, uint64_t blk_id,
                          const std::string& path, uint64_t file_id)
      : fs_(fs),
        trace_id_(trace_id),
        blk_id_(blk_id),
        path_(path),
        file_id_(file_id) {}

  ~DatanodeClient() = default;

  void GetReplicaInfo(const DNGetReplicaInfoOptions& opts,
                      DNReplicaInfoCb callback);

  void ReadBlock(const DNReadBlockOptions& opts, DNReadBlkCb callback);

  void CreateBlock(const DNClientCreateBlockOptions& opts,
                   DNCreateBlkCb callback);

  void WriteBlock(const DNClientWriteBlockOptions& opts, DNWriteBlkCb callback);

  void SealBlock(const DNClientSealBlockOptions& opts, DNSealBlockCb callback);

  void SyncBlock(const DNClientSyncBlockOptions& opts, DNPortCb callback);

  void PingBlock(const DNClientPingBlockOptions& opts, DNPortCb callback);

  void FinalizeBlock(const DNFinBlkOpts& opts, DNPortCb cb);

 private:
  template <RpcType RpcT>
  struct DNRpcCtx {
    typedef void (DatanodeClient::*DoneFunc)(std::unique_ptr<DNRpcCtx<RpcT>>,
                                             const RpcResult&);

    std::unique_ptr<typename DNRpcTypeTraits<RpcT>::ReqPb> request;
    std::unique_ptr<typename DNRpcTypeTraits<RpcT>::RspPb> response;
    typename DNRpcTypeTraits<RpcT>::CbType callback;
    DoneFunc done;
    const byterpc::IOBuf* req_attachment{nullptr};
    std::unique_ptr<byterpc::IOBuf> rsp_attachment;
    int64_t timeout_us;
    const ChannelHandle* channel_handle;
    uint32_t failed_num{0};
    // Retry once when meeting CONNECT_FAIL or TIMEOUT or EEOF
    bool retry_once{true};
    TraceCtx trace_ctx;

    DNRpcCtx() {
      trace_ctx.data_size = 0;
      trace_ctx.data_offset = 0;
    }
  };

  template <class Req>
  void BuildHeader(Req* req, const LocatedBlock* lb);

  template <RpcType RpcT>
  void DoSendRpc(std::unique_ptr<DNRpcCtx<RpcT>> ctx);

  template <RpcType RpcT>
  void OnDNRpcDone(std::unique_ptr<DNRpcCtx<RpcT>> ctx, const RpcResult& res);

 private:
  FileSystem* fs_;
  int64_t trace_id_;
  uint64_t blk_id_;
  const std::string& path_;
  uint64_t file_id_;
};

}  // namespace internal
}  // namespace cfs
