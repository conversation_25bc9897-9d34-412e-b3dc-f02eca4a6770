#include "io/namenode_client.h"

#include <byterpc/io_buf_as_zero_copy_stream.h>
#include <gflags/gflags.h>

#include "ClientNamenodeProtocol.pb.h"
#include "cloudfs2/version.h"
#include "common/logger.h"
#include "common/net_util.h"
#include "common/nic_manager.h"
#include "common/string_util.h"
#include "common/thread_pool.h"
#include "impl/filesystem.h"
#include "metric/cfs_metrics.h"
#include "namenode_client.h"
#include "protocol/append_rsp.h"
#include "rpc/byterpc_call.h"
#include "rpc/channel_handle.h"
#include "rpc/rpc_converter.h"

DECLARE_string(cfs_byterpc_protocol_nn);
DECLARE_string(cfs_byterpc_trans_type_nn);
DECLARE_int64(cfs_byterpc_nn_timeout_us);
DECLARE_int64(cfs_nn_rpc_max_retry_time_ms);
DECLARE_int64(cfs_nn_rpc_retry_interval_ms);
DECLARE_bool(cfs_enable_auth);
DECLARE_bool(cfs_enable_zti_auth);
DECLARE_string(cfs_auth_service_name);
DECLARE_string(cfs_meta_nic);
DECLARE_bool(cfs_enable_hpc);
DECLARE_string(cfs_rdma_tag);
DECLARE_string(cfs_task_id);

namespace cfs {

extern thread_local uint32_t g_cur_thread_dispatch_id;

namespace internal {

extern thread_local ThreadPool* g_cur_thread_pool;

static const std::string kFsNameKey = "fileSystemName";
static const std::string kStrToSignKey = "stringToSign";
static const std::string kSignatureKey = "signature";
static const std::string kAccessKeyKey = "accessKey";
static const std::string kSecurityTokenKey = "securityToken";
static const std::string kZtiTokenKey = "ztiToken";
static const std::string kLocationTagKey = "location_tag";
static constexpr std::string_view kEmptyStrView = "";
static const std::string kRdmaTagPrefix = "rdma-";
static const std::string kUploadPolicyXAttrKey = "hdfs.upload.policy.string";
// Always NOT-Allow replica num to decrease!
static constexpr bool kStrictReplica = true;

std::string NNBatchCompleteEntry::ToString() const {
  return StringUtil::StrCat("{path=", path, ", wb_policy=", wb_policy, "}");
}

std::string NNBatchCompleteOpts::ToString() const {
  std::string single = StringUtil::StrJoin(
      single_files, ",", [](std::string* out, const NNBatchCompleteEntry& e) {
        StringUtil::StrAppend(out, e.ToString());
      });
  std::string srcs;
  for (const auto& sv : concat_srcs) {
    srcs.push_back('[');
    auto s = StringUtil::StrJoin(
        sv, ",", [](std::string* out, const NNBatchCompleteEntry& e) {
          StringUtil::StrAppend(out, e.ToString());
        });
    srcs.append(std::move(s));
    srcs.push_back(']');
  }
  std::string tgts = StringUtil::StrJoin(
      concat_targets, ",", [](std::string* out, const NNBatchCompleteEntry& e) {
        StringUtil::StrAppend(out, e.ToString());
      });
  return StringUtil::StrFormat(
      "{{single_files=[{}], concat_srcs=[{}], concat_targets=[{}], "
      "is_atomic={} }}",
      single, srcs, tgts, is_atomic);
}

std::atomic<uint32_t> NamenodeClient::cur_call_id_{0};

NamenodeClient::NamenodeClient(FileSystem* fs)
    : fs_(fs),
      client_name_(fs_->GetClientName()),
      client_id_(fs_->GetClientId()) {
  if (FLAGS_cfs_enable_auth) {
    std::string ns_id_str;
    if (fs_->GetFsMode() == ::cloudfs::FsMode::ACC) {
      ns_id_str = std::to_string(fs_->GetNsId());
    }
    signer_ =
        std::make_unique<VolcSigner>(fs_->GetFsName(), fs_->GetRegion(),
                                     FLAGS_cfs_auth_service_name, ns_id_str);
  }
  client_ip_ = NICManager::GetInstance()->GetMainIp();
  if (FLAGS_cfs_enable_hpc && (!FLAGS_cfs_rdma_tag.empty())) {
    rdma_tag_ = kRdmaTagPrefix + FLAGS_cfs_rdma_tag;
  }
}

Result<> NamenodeClient::Init(const std::string& host_or_ip, int32_t port) {
  // 1. Init EndPoint and Channel
  EndPoint local_ep;
  EndPoint remote_ep;
  const NICInfo* local_nic = nullptr;
  if (!FLAGS_cfs_meta_nic.empty()) {
    local_nic = NICManager::GetInstance()->GetNICByName(FLAGS_cfs_meta_nic);
    if (local_nic != nullptr) {
      CFSLOG(INFO, "Use {} to connect to NN", FLAGS_cfs_meta_nic);
    } else {
      CFSLOG(WARN, "NIC `{}` not found, use default NIC to connect to NN",
             FLAGS_cfs_meta_nic);
    }
  } else {
    CFSLOG(INFO, "Use the default NIC to connect to NN");
  }
  bool prefer_ipv6 = false;
  if (NetUtil::IsValidIp(host_or_ip)) {
    prefer_ipv6 = NetUtil::IsIpv6(host_or_ip);
    remote_ep = EndPoint(host_or_ip, host_or_ip, port);
  } else {
    if (local_nic != nullptr) {
      prefer_ipv6 = local_nic->HasIpv6();
    } else {
      const auto& nic = NICManager::GetInstance()->GetDefaultNIC();
      prefer_ipv6 = nic.HasIpv6();
    }
    auto res = NetUtil::Hostname2Ip6(host_or_ip, SOCK_STREAM, prefer_ipv6);
    if (!res.IsOk()) {
      return Status(
          CFS_ERR_INVALID_PARAMETER,
          StringUtil::StrFormat("Invalid FS URI={}, err: {}", host_or_ip,
                                res.GetError().ToString()));
    }
    prefer_ipv6 = NetUtil::IsRealIpv6(res.GetValue());
    auto conn_ip_str = NetUtil::Ip6ToStr(res.GetValue());
    remote_ep = EndPoint(host_or_ip, res.GetValue(), port, conn_ip_str);
  }

  if (local_nic != nullptr) {
    local_ep =
        EndPoint(NICManager::GetInstance()->GetMainIp(),
                 local_nic->GetIp(prefer_ipv6), 0, local_nic->GetAddrStr());
  } else {
    // update the conn_ip_str used for trace-print.
    local_ep.SetConnIpStr("DEFAULT");
  }
  channel_handle_ =
      ChannelHandle(std::move(remote_ep), std::move(local_ep),
                    RpcUtil::Str2TransType(FLAGS_cfs_byterpc_trans_type_nn));
  return Result<>();
}

static void SetUploadUfsAttr(::cloudfs::XAttrProto* attr,
                             cfs_write_back_policy wb) {
  attr->set_namespace_(::cloudfs::XAttrProto_XAttrNamespaceProto_SYSTEM);
  attr->set_name(kUploadPolicyXAttrKey);
  if (wb != CFS_WB_UNSPEC) {
    ::cloudfs::UfsUploadPolicyProto policy;
    policy.set_upload_interval_ms(wb == CFS_WB_TRUE ? 0 : -1);
    std::string val;
    bool res = policy.SerializeToString(&val);
    CFS_CHECK(res);
    attr->set_value(std::move(val));
  }
}

void NamenodeClient::Create(const NNCreateOpts& opts, NNCreateRspCb callback) {
  auto request = std::make_unique<::cloudfs::CreateRequestProto>();
  request->set_src(opts.path);
  request->mutable_masked()->set_perm(opts.permission);
  request->set_clientname(client_name_);
  request->set_createflag(opts.flags);
  request->set_createparent(opts.create_parent);
  request->set_replication(opts.replication);
  request->set_blocksize(opts.blocksize);
  request->set_strict_replica_num(kStrictReplica);
  request->set_withaddblock(opts.with_add_blk);
  for (const auto& dn : opts.exclude_nodes) {
    *(request->add_excludenodes()) = RpcConverter::DatanodeInfo2PB(dn);
  }
  request->set_rpc_type(::cloudfs::RPC_BYTERPC_MODE);
  if (!rdma_tag_.empty()) {
    request->set_rdmatag(rdma_tag_);
  }
  if (opts.wb_policy != CFS_WB_UNSPEC) {
    auto* attr = request->add_attr();
    SetUploadUfsAttr(attr, opts.wb_policy);
  }
  CFSDLOG(DEBUG, "CreateRequestProto: \n{}", request->DebugString());
  auto ctx = std::make_unique<NNRpcCtx<RPC_NN_CREATE>>(opts.path,
                                                       opts.com_opt.trace_id);
  ctx->request = std::move(request);
  ctx->response = std::make_unique<::cloudfs::CreateResponseProto>();
  ctx->callback = std::move(callback);
  ctx->done = &NamenodeClient::OnNNRpcDone<RPC_NN_CREATE>;
  ctx->call_id = NextCallId();
  DoSendRpc(std::move(ctx));
}

void NamenodeClient::BatchCreate(const NNBatchCreateOpts& opts,
                                 NNBatchCreateCb cb) {
  CFS_DCHECK_EQ(opts.paths.size(), opts.wb_policy.size());
  CFS_DCHECK_GT(opts.paths.size(), 0);
  auto req = std::make_unique<::cloudfs::BatchCreateFileRequestProto>();
  std::string sign_path;
  for (size_t i = 0; i < opts.paths.size(); ++i) {
    sign_path.append(opts.paths[i]);
    auto* ent = req->add_files();
    ent->set_src(opts.paths[i]);
    ent->mutable_masked()->set_perm(opts.permission);
    ent->set_createflag(opts.flags);
    ent->set_replication(opts.replication);
    ent->set_blocksize(opts.blocksize);
    if (opts.wb_policy[i] != CFS_WB_UNSPEC) {
      auto* attr = ent->add_attr();
      SetUploadUfsAttr(attr, opts.wb_policy[i]);
    }
    ent->set_strict_replica_num(kStrictReplica);
  }
  req->set_clientname(client_name_);
  req->set_withaddblock(opts.with_add_blk);
  for (const auto& dn : opts.exclude_nodes) {
    *(req->add_excludenodes()) = RpcConverter::DatanodeInfo2PB(dn);
  }
  req->set_overwrite_flag(opts.overwrite_flag);
  req->set_atomic_flag(opts.is_atomic);
  req->set_rpc_type(::cloudfs::RPC_BYTERPC_MODE);
  if (!rdma_tag_.empty()) {
    req->set_rdmatag(rdma_tag_);
  }
  CFSDLOG(DEBUG, "BatchCreateFileRequestProto: \n{}", req->DebugString());
  auto ctx = std::make_unique<NNRpcCtx<RPC_NN_BATCHCREATEFILE>>(
      sign_path, opts.com_opt.trace_id);
  ctx->request = std::move(req);
  ctx->response = std::make_unique<::cloudfs::BatchCreateFileResponseProto>();
  ctx->callback = std::move(cb);
  ctx->done = &NamenodeClient::OnNNRpcDone<RPC_NN_BATCHCREATEFILE>;
  ctx->call_id = NextCallId();
  DoSendRpc(std::move(ctx));
}

void NamenodeClient::Append(const NNAppendOpts& opts, NNAppendRspCb callback) {
  auto request = std::make_unique<::cloudfs::AppendRequestProto>();
  request->set_src(opts.path);
  request->set_clientname(client_name_);
  request->set_force(opts.force);
  auto ctx = std::make_unique<NNRpcCtx<RPC_NN_APPEND>>(opts.path,
                                                       opts.com_opt.trace_id);
  ctx->request = std::move(request);
  ctx->response = std::make_unique<::cloudfs::AppendResponseProto>();
  ctx->callback = std::move(callback);
  ctx->done = &NamenodeClient::OnNNRpcDone<RPC_NN_APPEND>;
  ctx->call_id = NextCallId();
  DoSendRpc(std::move(ctx));
}

void NamenodeClient::GetFileInfo(const NNGetFInfoOpts& opts,
                                 NNFileInfoCb callback) {
  auto request = std::make_unique<::cloudfs::GetFileInfoRequestProto>();
  request->set_src(opts.path);
  request->set_needlocation(opts.need_location);
  request->set_updatettlatime(opts.update_atime);
  if (!rdma_tag_.empty()) {
    request->set_rdmatag(rdma_tag_);
  }
  CFSDLOG(DEBUG, "GetFileInfoRequestProto: \n{}", request->DebugString());
  auto ctx = std::make_unique<NNRpcCtx<RPC_NN_GETFILEINFO>>(
      opts.path, opts.com_opt.trace_id);
  ctx->request = std::move(request);
  ctx->response = std::make_unique<::cloudfs::GetFileInfoResponseProto>();
  ctx->callback = std::move(callback);
  ctx->done = &NamenodeClient::OnNNRpcDone<RPC_NN_GETFILEINFO>;
  ctx->call_id = NextCallId();
  DoSendRpc(std::move(ctx));
}

void NamenodeClient::BatchGetFileInfo(const NNBatchGetFInfoOpts& opts,
                                      NNBatchFileInfoCb cb) {
  CFS_DCHECK_GT(opts.paths.size(), 0);
  std::string sign_path;
  auto req = std::make_unique<::cloudfs::BatchGetFileRequestProto>();
  for (size_t i = 0; i < opts.paths.size(); ++i) {
    req->add_srcs(opts.paths[i]);
    sign_path.append(opts.paths[i]);
  }
  req->set_needlocation(opts.need_location);
  req->set_updatettlatime(opts.update_atime);
  if (!rdma_tag_.empty()) {
    req->set_rdmatag(rdma_tag_);
  }
  CFSDLOG(DEBUG, "BatchGetFileRequestProto: \n{}", req->DebugString());
  auto ctx = std::make_unique<NNRpcCtx<RPC_NN_BATCHGETFILE>>(
      sign_path, opts.com_opt.trace_id);
  ctx->request = std::move(req);
  ctx->response = std::make_unique<::cloudfs::BatchGetFileResponseProto>();
  ctx->callback = std::move(cb);
  ctx->done = &NamenodeClient::OnNNRpcDone<RPC_NN_BATCHGETFILE>;
  ctx->call_id = NextCallId();
  DoSendRpc(std::move(ctx));
}

void NamenodeClient::AddBlock(const NNAddBlkOpts& opts,
                              NNLocatedBlkCb callback) {
  auto request = std::make_unique<::cloudfs::AddBlockRequestProto>();
  request->set_src(opts.path.data(), opts.path.size());
  request->set_clientname(client_name_);
  if (opts.prev_blk != nullptr) {
// v2 pb has changed prev_blk from ExtendedBlock to LocatedBlock. the old one
// is deprecated
#pragma GCC diagnostic push
#pragma GCC diagnostic ignored "-Wdeprecated-declarations"
    *(request->mutable_previous()) = opts.prev_blk->GetExBlock();
    // prepare located block proto
    *(request->mutable_located_previous()) =
        RpcConverter::LocatedBlock2PB(*(opts.prev_blk));
#pragma GCC diagnostic pop
  }
  for (const auto& dn : opts.exclude_nodes) {
    *(request->add_excludenodes()) = RpcConverter::DatanodeInfo2PB(dn);
  }
  request->set_fileid(opts.file_id);
  // rpc_type in request should be set to RPC_BYTERPC_MODE so that NN can set
  // correct GenerationStamp.
  request->set_rpc_type(::cloudfs::RPC_BYTERPC_MODE);
  request->set_strict_replica_num(kStrictReplica);
  if (!rdma_tag_.empty()) {
    request->set_rdmatag(rdma_tag_);
  }
  CFSDLOG(DEBUG, "AddBlockRequestProto: \n{}", request->DebugString());
  auto ctx = std::make_unique<NNRpcCtx<RPC_NN_ADDBLOCK>>(opts.path,
                                                         opts.com_opt.trace_id);
  ctx->trace_ctx.inode_id = opts.file_id;
  ctx->request = std::move(request);
  ctx->response = std::make_unique<::cloudfs::AddBlockResponseProto>();
  ctx->callback = std::move(callback);
  ctx->done = &NamenodeClient::OnNNRpcDone<RPC_NN_ADDBLOCK>;
  ctx->call_id = NextCallId();
  DoSendRpc(std::move(ctx));
}

void NamenodeClient::AbandonBlock(const NNAbandonBlkOpts& opts,
                                  NNSimpleCb callback) {
  auto request = std::make_unique<::cloudfs::AbandonBlockRequestProto>();
  request->set_src(opts.path.data(), opts.path.size());
  request->set_holder(client_name_);
  *(request->mutable_b()) = *(opts.blk);
  request->set_fileid(opts.file_id);
  CFSDLOG(INFO, "AbandonBlockRequestProto: \n{}", request->DebugString());
  auto ctx = std::make_unique<NNRpcCtx<RPC_NN_ABANDONBLOCK>>(
      opts.path, opts.com_opt.trace_id);
  ctx->trace_ctx.inode_id = opts.file_id;
  ctx->request = std::move(request);
  ctx->response = std::make_unique<::cloudfs::AbandonBlockResponseProto>();
  ctx->callback = std::move(callback);
  ctx->done = &NamenodeClient::OnNNRpcDone<RPC_NN_ABANDONBLOCK>;
  ctx->call_id = NextCallId();
  DoSendRpc(std::move(ctx));
}

void NamenodeClient::Complete(const NNCompleteOpts& opts, NNBoolCb callback) {
  auto request = std::make_unique<::cloudfs::CompleteRequestProto>();
  request->set_src(opts.path);
  request->set_clientname(client_name_);
  // If a file is created and closed without writing any data, the last_blk
  // is null.
  if (opts.last_blk != nullptr) {
    *(request->mutable_last()) = opts.last_blk->GetExBlock();
    *(request->mutable_located_last()) =
        RpcConverter::LocatedBlock2PB(*(opts.last_blk));
  }
  request->set_fileid(opts.file_id);
  auto ctx = std::make_unique<NNRpcCtx<RPC_NN_COMPLETE>>(opts.path,
                                                         opts.com_opt.trace_id);
  ctx->trace_ctx.inode_id = opts.file_id;
  ctx->need_retry = opts.need_retry;
  ctx->request = std::move(request);
  ctx->response = std::make_unique<::cloudfs::CompleteResponseProto>();
  ctx->callback = std::move(callback);
  ctx->done = &NamenodeClient::OnNNRpcDone<RPC_NN_COMPLETE>;
  ctx->call_id = NextCallId();
  DoSendRpc(std::move(ctx));
}

void NamenodeClient::BatchComplete(const NNBatchCompleteOpts& opts,
                                   NNBatchCompleteCb cb) {
  auto req = std::make_unique<::cloudfs::BatchCompleteFileRequestProto>();
  std::string sign_path;
  uint64_t trace_inode_id = 0;
  for (size_t i = 0; i < opts.single_files.size(); ++i) {
    auto* ent = req->add_singlefile();
    auto& f = opts.single_files[i];
    sign_path.append(f.path);
    ent->set_src(f.path.data(), f.path.size());
    if (f.last_blk != nullptr) {
#pragma GCC diagnostic push
#pragma GCC diagnostic ignored "-Wdeprecated-declarations"
      *(ent->mutable_lastblock()) = f.last_blk->GetExBlock();
      *(ent->mutable_locatedlastblock()) =
          RpcConverter::LocatedBlock2PB(*(f.last_blk));
#pragma GCC diagnostic pop
    }
    ent->set_fileid(f.file_id);
    if (trace_inode_id == 0) {
      trace_inode_id = f.file_id;
    }
    // single files do not need to set `add_attr` again because it has set it
    // when BatchCreate
    // if (f.wb_policy != CFS_WB_UNSPEC) {
    //   auto* attr = ent->add_add_attr();
    //   SetUploadUfsAttr(attr, f.wb_policy);
    // }
  }

  CFS_DCHECK_EQ(opts.concat_srcs.size(), opts.concat_targets.size());
  for (size_t i = 0; i < opts.concat_srcs.size(); ++i) {
    auto* ent = req->add_concatfile();
    // set concat src
    auto& src_files = opts.concat_srcs[i];
    auto& tf = opts.concat_targets[i];
    CFS_DCHECK_GT(src_files.size(), 0);
    for (size_t j = 0; j < src_files.size(); ++j) {
      auto* src_ent = ent->add_srcs();
      auto& sf = src_files[j];
      src_ent->set_src(sf.path.data(), sf.path.size());
      if (sf.last_blk != nullptr) {
#pragma GCC diagnostic push
#pragma GCC diagnostic ignored "-Wdeprecated-declarations"
        *(src_ent->mutable_lastblock()) = sf.last_blk->GetExBlock();
        *(src_ent->mutable_locatedlastblock()) =
            RpcConverter::LocatedBlock2PB(*(sf.last_blk));
#pragma GCC diagnostic pop
      }
      src_ent->set_fileid(sf.file_id);
      // concat srcs must have been set to CFS_WB_FALSE. And they do not need
      // to set wb_policy when BatchComplete basause it is already set in
      // BatchCreate
      CFS_DCHECK_EQ(static_cast<std::underlying_type_t<cfs_write_back_policy>>(
                        sf.wb_policy),
                    static_cast<std::underlying_type_t<cfs_write_back_policy>>(
                        CFS_WB_FALSE));
      // if (sf.wb_policy != CFS_WB_UNSPEC) {
      //   auto* attr = src_ent->add_add_attr();
      //   SetUploadUfsAttr(attr, sf.wb_policy);
      // }
    }
    // set concat target
    auto* tgt_ent = ent->mutable_target();
    sign_path.append(tf.path);
    tgt_ent->set_src(tf.path.data(), tf.path.size());
    if (tf.last_blk != nullptr) {
#pragma GCC diagnostic push
#pragma GCC diagnostic ignored "-Wdeprecated-declarations"
      *(tgt_ent->mutable_lastblock()) = tf.last_blk->GetExBlock();
      *(tgt_ent->mutable_locatedlastblock()) =
          RpcConverter::LocatedBlock2PB(*(tf.last_blk));
#pragma GCC diagnostic pop
    }
    tgt_ent->set_fileid(tf.file_id);
    if (tf.wb_policy != CFS_WB_UNSPEC) {
      auto* attr = tgt_ent->add_add_attr();
      SetUploadUfsAttr(attr, tf.wb_policy);
    } else {
      // When the concat_target's wb_policy is CFS_WB_UNSPEC, we must delete
      // its xattr set in BatchCreate, which must be CFS_WB_FALSE.
      auto* attr = tgt_ent->add_del_attr();
      SetUploadUfsAttr(attr, tf.wb_policy);
    }
    if (trace_inode_id == 0) {
      trace_inode_id = tf.file_id;
    }
  }
  req->set_clientname(client_name_);
  req->set_atomic_flag(opts.is_atomic);
  req->set_rpc_type(::cloudfs::RPC_BYTERPC_MODE);
  CFSDLOG(DEBUG, "BatchCompleteFileRequestProto: \n{}", req->DebugString());

  auto ctx = std::make_unique<NNRpcCtx<RPC_NN_BATCHCOMPLETEFILE>>(
      sign_path, opts.com_opt.trace_id);
  ctx->trace_ctx.inode_id = trace_inode_id;
  ctx->request = std::move(req);
  ctx->response = std::make_unique<::cloudfs::BatchCompleteFileResponseProto>();
  ctx->callback = std::move(cb);
  ctx->done = &NamenodeClient::OnNNRpcDone<RPC_NN_BATCHCOMPLETEFILE>;
  ctx->call_id = NextCallId();
  DoSendRpc(std::move(ctx));
}

void NamenodeClient::GetBlockLocations(const NNGetBlkLocsOpts& opts,
                                       NNLocatedBlksCb callback) {
  auto request = std::make_unique<::cloudfs::GetBlockLocationsRequestProto>();
  request->set_src(opts.path.data(), opts.path.size());
  request->set_offset(opts.offset);
  request->set_length(opts.length);
  request->set_fileid(opts.file_id);
  request->set_expectediomode(
      ::cloudfs::ExpectedIoMode::DATANODE_BLOCK_EXPECTED);
  if (!rdma_tag_.empty()) {
    request->set_rdmatag(rdma_tag_);
  }
  CFSDLOG(DEBUG, "GetBlockLocationsRequestProto: \n{}", request->DebugString());
  auto ctx = std::make_unique<NNRpcCtx<RPC_NN_GETBLOCKLOCATIONS>>(
      opts.path, opts.com_opt.trace_id);
  ctx->trace_ctx.inode_id = opts.file_id;
  ctx->request = std::move(request);
  ctx->response = std::make_unique<::cloudfs::GetBlockLocationsResponseProto>();
  ctx->callback = std::move(callback);
  ctx->done = &NamenodeClient::OnNNRpcDone<RPC_NN_GETBLOCKLOCATIONS>;
  ctx->call_id = NextCallId();
  DoSendRpc(std::move(ctx));
}

void NamenodeClient::RecoverLease(const NNRecoverLeaseOpts& opts,
                                  NNBoolCb callback) {
  auto request = std::make_unique<::cloudfs::RecoverLeaseRequestProto>();
  request->set_src(opts.path);
  request->set_clientname(client_name_);
  if (opts.file_id > 0) {
    request->set_fileid(opts.file_id);
  }
  auto ctx = std::make_unique<NNRpcCtx<RPC_NN_RECOVERLEASE>>(
      opts.path, opts.com_opt.trace_id);
  ctx->trace_ctx.inode_id = opts.file_id;
  ctx->request = std::move(request);
  ctx->response = std::make_unique<::cloudfs::RecoverLeaseResponseProto>();
  ctx->callback = std::move(callback);
  ctx->done = &NamenodeClient::OnNNRpcDone<RPC_NN_RECOVERLEASE>;
  ctx->call_id = NextCallId();
  DoSendRpc(std::move(ctx));
}

void NamenodeClient::IsFileClosed(const NNIsFClosedOpts& opts, NNBoolCb cb) {
  auto request = std::make_unique<::cloudfs::IsFileClosedRequestProto>();
  request->set_src(opts.path);
  auto ctx = std::make_unique<NNRpcCtx<RPC_NN_ISFILECLOSED>>(
      opts.path, opts.com_opt.trace_id);
  ctx->request = std::move(request);
  ctx->response = std::make_unique<::cloudfs::IsFileClosedResponseProto>();
  ctx->callback = std::move(cb);
  ctx->done = &NamenodeClient::OnNNRpcDone<RPC_NN_ISFILECLOSED>;
  ctx->call_id = NextCallId();
  DoSendRpc(std::move(ctx));
}

void NamenodeClient::MkDir(const NNMkDirOpts& opts, NNSimpleCb callback) {
  auto request = std::make_unique<::cloudfs::MkdirsRequestProto>();
  request->set_src(opts.path);
  request->mutable_masked()->set_perm(opts.perm);
  request->set_createparent(opts.create_parent);
  auto ctx = std::make_unique<NNRpcCtx<RPC_NN_MKDIR>>(opts.path,
                                                      opts.com_opt.trace_id);
  ctx->request = std::move(request);
  ctx->response = std::make_unique<::cloudfs::MkdirsResponseProto>();
  ctx->callback = std::move(callback);
  ctx->done = &NamenodeClient::OnNNRpcDone<RPC_NN_MKDIR>;
  ctx->call_id = NextCallId();
  DoSendRpc(std::move(ctx));
}

void NamenodeClient::MkDirExt(const NNMkDirExtOpts& opts, NNSimpleCb callback) {
  auto request = std::make_unique<::cloudfs::MkdirExtendedRequestProto>();
  request->set_src(opts.path);
  request->mutable_masked()->set_perm(opts.perm);
  request->set_createparent(opts.create_parent);
  if (opts.ttl_seconds > 0) {
    auto* exp = request->mutable_exprule();
    exp->set_seconds(opts.ttl_seconds);
    exp->set_recycle_whole_directory(opts.ttl_whole);
    if (opts.ttl_atime_based) {
      exp->set_expiration_type(::cloudfs::ExpirationTypeProto::ATIME_BASED);
    } else {
      exp->set_expiration_type(::cloudfs::ExpirationTypeProto::MTIME_BASED);
    }
  }
  if (opts.wb != CFS_WB_UNSPEC) {
    if (opts.wb == CFS_WB_TRUE) {
      request->mutable_upload_policy()->set_upload_interval_ms(0);
      request->set_sync_policy(::cloudfs::kSyncPolicySync);
    } else {
      request->mutable_upload_policy()->set_upload_interval_ms(-1);
      request->set_sync_policy(::cloudfs::kSyncPolicyLocal);
    }
  }
  CFSDLOG(DEBUG, "MkdirExtendedRequestProto:\n{}", request->DebugString());
  auto ctx = std::make_unique<NNRpcCtx<RPC_NN_MKDIREXTENDED>>(
      opts.path, opts.com_opt.trace_id);
  ctx->request = std::move(request);
  ctx->response = std::make_unique<::cloudfs::MkdirExtendedResponseProto>();
  ctx->callback = std::move(callback);
  ctx->done = &NamenodeClient::OnNNRpcDone<RPC_NN_MKDIREXTENDED>;
  ctx->call_id = NextCallId();
  DoSendRpc(std::move(ctx));
}

void NamenodeClient::Delete(const NNDeleteOpts& opts, NNSimpleCb callback) {
  auto request = std::make_unique<::cloudfs::DeleteRequestProto>();
  request->set_src(opts.path);
  request->set_recursive(opts.recursive);
  if (opts.max_count != 0) {
    request->set_maxcount(opts.max_count);
  }
  auto ctx = std::make_unique<NNRpcCtx<RPC_NN_DELETE>>(opts.path,
                                                       opts.com_opt.trace_id);
  ctx->request = std::move(request);
  ctx->response = std::make_unique<::cloudfs::DeleteResponseProto>();
  ctx->callback = std::move(callback);
  ctx->done = &NamenodeClient::OnNNRpcDone<RPC_NN_DELETE>;
  ctx->call_id = NextCallId();
  DoSendRpc(std::move(ctx));
}

void NamenodeClient::BatchDelete(const NNBatchDeleteOpts& opts, NNSimpleCb cb) {
  auto req = std::make_unique<::cloudfs::BatchDeleteFileRequestProto>();
  std::string sign_path;
  for (size_t i = 0; i < opts.paths.size(); ++i) {
    sign_path.append(opts.paths[i]);
    req->add_srcs(opts.paths[i]);
  }
  req->set_force(opts.force);
  auto ctx = std::make_unique<NNRpcCtx<RPC_NN_BATCHDELETEFILE>>(
      sign_path, opts.com_opt.trace_id);
  ctx->request = std::move(req);
  ctx->response = std::make_unique<::cloudfs::BatchDeleteFileResponseProto>();
  ctx->callback = std::move(cb);
  ctx->done = &NamenodeClient::OnNNRpcDone<RPC_NN_BATCHDELETEFILE>;
  ctx->call_id = NextCallId();
  DoSendRpc(std::move(ctx));
}

void NamenodeClient::Rename(const NNRenameOpts& opts, NNSimpleCb callback) {
  auto request = std::make_unique<::cloudfs::Rename2RequestProto>();
  request->set_src(opts.old_path);
  request->set_dst(opts.new_path);
  request->set_overwritedest(opts.overwrite);
  if (fs_->GetFsMode() == ::cloudfs::FsMode::ACC) {
    request->set_maxcount(opts.acc_max_count);
    request->set_maxsize(opts.acc_max_size_gb);
    request->set_maxoverwritecount(opts.acc_max_overwrite_cnt);
  }
  auto ctx = std::make_unique<NNRpcCtx<RPC_NN_RENAME>>(opts.old_path,
                                                       opts.com_opt.trace_id);
  ctx->request = std::move(request);
  ctx->response = std::make_unique<::cloudfs::Rename2ResponseProto>();
  ctx->callback = std::move(callback);
  ctx->done = &NamenodeClient::OnNNRpcDone<RPC_NN_RENAME>;
  ctx->call_id = NextCallId();
  DoSendRpc(std::move(ctx));
}

void NamenodeClient::Fsync(const NNFsyncOpts& opts, NNSimpleCb callback) {
  auto request = std::make_unique<::cloudfs::FsyncRequestProto>();
  request->set_src(opts.path.data(), opts.path.size());
  request->set_client(client_name_);
  if (opts.last_blk_len > -1) {
    request->set_lastblocklength(opts.last_blk_len);
  }
  request->set_fileid(opts.file_id);
  auto ctx = std::make_unique<NNRpcCtx<RPC_NN_FSYNC>>(opts.path,
                                                      opts.com_opt.trace_id);
  ctx->trace_ctx.inode_id = opts.file_id;
  ctx->request = std::move(request);
  ctx->response = std::make_unique<::cloudfs::FsyncResponseProto>();
  ctx->callback = std::move(callback);
  ctx->done = &NamenodeClient::OnNNRpcDone<RPC_NN_FSYNC>;
  ctx->call_id = NextCallId();
  DoSendRpc(std::move(ctx));
}

void NamenodeClient::SetTimes(const NNSetTimesOpts& opts, NNSimpleCb cb) {
  CFSDLOG(DEBUG, "SetTimes, path={}, atime_ms={}, mtime_ms={}", opts.path,
          opts.atime_ms, opts.mtime_ms);
  auto req = std::make_unique<::cloudfs::SetTimesRequestProto>();
  req->set_src(opts.path);
  req->set_atime(opts.atime_ms);
  req->set_mtime(opts.mtime_ms);
  auto ctx = std::make_unique<NNRpcCtx<RPC_NN_SETTIMES>>(opts.path,
                                                         opts.com_opt.trace_id);
  ctx->request = std::move(req);
  ctx->response = std::make_unique<::cloudfs::SetTimesResponseProto>();
  ctx->callback = std::move(cb);
  ctx->done = &NamenodeClient::OnNNRpcDone<RPC_NN_SETTIMES>;
  ctx->call_id = NextCallId();
  DoSendRpc(std::move(ctx));
}

void NamenodeClient::ListDir(const NNListDirOpts& opts, NNListDirCb callback) {
  auto request = std::make_unique<::cloudfs::GetListingRequestProto>();
  request->set_src(opts.path);
  request->set_startafter(opts.start_after.data(), opts.start_after.size());
  request->set_needlocation(opts.need_location);
  request->set_pagesize(opts.page_size);
  if (fs_->GetFsMode() == ::cloudfs::FsMode::ACC) {
    request->set_maxcount(opts.acc_max_count);
  }
  auto ctx = std::make_unique<NNRpcCtx<RPC_NN_GETLISTING>>(
      opts.path, opts.com_opt.trace_id);
  ctx->request = std::move(request);
  ctx->response = std::make_unique<::cloudfs::GetListingResponseProto>();
  ctx->callback = std::move(callback);
  ctx->done = &NamenodeClient::OnNNRpcDone<RPC_NN_GETLISTING>;
  ctx->call_id = NextCallId();
  DoSendRpc(std::move(ctx));
}

void NamenodeClient::RenewLease(const std::string& namenode, bool need_retry,
                                NNSimpleCb callback) {
  auto request = std::make_unique<::cloudfs::RenewLeaseRequestProto>();
  request->set_clientname(client_name_);
  request->add_namenodebackends(namenode);
  // set trace_id to -1 because RenewLease does not need trace
  auto ctx = std::make_unique<NNRpcCtx<RPC_NN_RENEWLEASE>>(kEmptyStrView, -1);
  ctx->need_retry = need_retry;
  ctx->request = std::move(request);
  ctx->response = std::make_unique<::cloudfs::RenewLeaseResponseProto>();
  ctx->callback = std::move(callback);
  ctx->done = &NamenodeClient::OnNNRpcDone<RPC_NN_RENEWLEASE>;
  ctx->call_id = NextCallId();
  ctx->path = kEmptyStrView;
  DoSendRpc(std::move(ctx));
}

void NamenodeClient::Concat(const NNConcatOpts& opts, NNSimpleCb callback) {
  auto request = std::make_unique<::cloudfs::ConcatRequestProto>();
  for (const auto& src : opts.srcs) {
    request->add_srcs(src);
  }
  request->set_trg(opts.dst);
  CFSDLOG(DEBUG, "ConcatRequestProto:\n{}", request->DebugString());
  auto ctx = std::make_unique<NNRpcCtx<RPC_NN_CONCAT>>(opts.dst,
                                                       opts.com_opt.trace_id);
  ctx->request = std::move(request);
  ctx->response = std::make_unique<::cloudfs::ConcatResponseProto>();
  ctx->callback = std::move(callback);
  ctx->done = &NamenodeClient::OnNNRpcDone<RPC_NN_CONCAT>;
  ctx->call_id = NextCallId();
  DoSendRpc(std::move(ctx));
}

void NamenodeClient::GetFsStats(const NNGetFsStatsOpts& opts,
                                NNFsStatsCb callback) {
  auto request = std::make_unique<::cloudfs::GetFsStatusRequestProto>();
  auto ctx = std::make_unique<NNRpcCtx<RPC_NN_GETFSSTATS>>(
      kEmptyStrView, opts.com_opt.trace_id);
  ctx->request = std::move(request);
  ctx->response = std::make_unique<::cloudfs::GetFsStatsResponseProto>();
  ctx->callback = std::move(callback);
  ctx->done = &NamenodeClient::OnNNRpcDone<RPC_NN_GETFSSTATS>;
  ctx->call_id = NextCallId();
  DoSendRpc(std::move(ctx));
}

void NamenodeClient::SetDirPolicy(const NNDirPolicyOpts& opts, NNSimpleCb cb) {
  auto req = std::make_unique<::cloudfs::SetDirPolicyRequestProto>();
  req->set_path(opts.path);
  if (opts.wb != CFS_WB_UNSPEC) {
    int32_t val = (opts.wb == CFS_WB_TRUE ? 0 : -1);
    req->mutable_upload_policy()->set_upload_interval_ms(val);
  }
  auto ctx = std::make_unique<NNRpcCtx<RPC_NN_SETDIRPOLICY>>(
      opts.path, opts.com_opt.trace_id);
  ctx->request = std::move(req);
  ctx->response = std::make_unique<::cloudfs::SetDirPolicyResponseProto>();
  ctx->callback = std::move(cb);
  ctx->done = &NamenodeClient::OnNNRpcDone<RPC_NN_SETDIRPOLICY>;
  ctx->call_id = NextCallId();
  DoSendRpc(std::move(ctx));
}

void NamenodeClient::GetDirPolicy(const NNDirPolicyOpts& opts,
                                  NNDirPolicyCb cb) {
  auto req = std::make_unique<::cloudfs::GetDirPolicyRequestProto>();
  req->set_path(opts.path);
  // Now only upload_policy is supported
  req->set_need_read_policy(false);
  req->set_need_replica_policy(false);
  req->set_need_upload_policy(true);
  auto ctx = std::make_unique<NNRpcCtx<RPC_NN_GETDIRPOLICY>>(
      opts.path, opts.com_opt.trace_id);
  ctx->request = std::move(req);
  ctx->response = std::make_unique<::cloudfs::GetDirPolicyResponseProto>();
  ctx->callback = std::move(cb);
  ctx->done = &NamenodeClient::OnNNRpcDone<RPC_NN_GETDIRPOLICY>;
  ctx->call_id = NextCallId();
  DoSendRpc(std::move(ctx));
}

void NamenodeClient::RmDirPolicy(const NNDirPolicyOpts& opts, NNSimpleCb cb) {
  auto req = std::make_unique<::cloudfs::RemoveDirPolicyRequestProto>();
  req->set_path(opts.path);
  req->set_remove_read_policy(false);
  req->set_remove_replica_policy(false);
  req->set_remove_upload_policy(true);
  auto ctx = std::make_unique<NNRpcCtx<RPC_NN_RMDIRPOLICY>>(
      opts.path, opts.com_opt.trace_id);
  ctx->request = std::move(req);
  ctx->response = std::make_unique<::cloudfs::RemoveDirPolicyResponseProto>();
  ctx->callback = std::move(cb);
  ctx->done = &NamenodeClient::OnNNRpcDone<RPC_NN_RMDIRPOLICY>;
  ctx->call_id = NextCallId();
  DoSendRpc(std::move(ctx));
}

void NamenodeClient::ListDirPolicy(const NNDirPolicyOpts& opts,
                                   NNDirPoliciesCb cb) {
  auto req = std::make_unique<::cloudfs::ListDirPolicyRequestProto>();
  // Now only upload_policy is supported
  req->set_need_read_policy(false);
  req->set_need_replica_policy(false);
  req->set_need_upload_policy(true);
  auto ctx = std::make_unique<NNRpcCtx<RPC_NN_LISTDIRPOLICY>>(
      kEmptyStrView, opts.com_opt.trace_id);
  ctx->request = std::move(req);
  ctx->response = std::make_unique<::cloudfs::ListDirPolicyResponseProto>();
  ctx->callback = std::move(cb);
  ctx->done = &NamenodeClient::OnNNRpcDone<RPC_NN_LISTDIRPOLICY>;
  ctx->call_id = NextCallId();
  DoSendRpc(std::move(ctx));
}

void NamenodeClient::SetSyncPolicy(const NNDirPolicyOpts& opts, NNSimpleCb cb) {
  CFS_CHECK(opts.wb != CFS_WB_UNSPEC);
  auto req = std::make_unique<::cloudfs::SetSyncPolicyRequestProto>();
  req->set_path(opts.path);
  if (opts.wb == CFS_WB_TRUE) {
    req->set_sync_policy(::cloudfs::kSyncPolicySync);
  } else {
    req->set_sync_policy(::cloudfs::kSyncPolicyLocal);
  }
  auto ctx = std::make_unique<NNRpcCtx<RPC_NN_SETSYNCPOLICY>>(
      opts.path, opts.com_opt.trace_id);
  ctx->request = std::move(req);
  ctx->response = std::make_unique<::cloudfs::SetSyncPolicyResponseProto>();
  ctx->callback = std::move(cb);
  ctx->done = &NamenodeClient::OnNNRpcDone<RPC_NN_SETSYNCPOLICY>;
  ctx->call_id = NextCallId();
  DoSendRpc(std::move(ctx));
}

void NamenodeClient::ListSyncPolicy(const NNDirPolicyOpts& opts,
                                    NNDirPoliciesCb cb) {
  auto req = std::make_unique<::cloudfs::ListSyncPolicyRequestProto>();
  // Now only upload_policy is supported
  req->set_need_local_sync_policy(true);
  auto ctx = std::make_unique<NNRpcCtx<RPC_NN_LISTSYNCPOLICY>>(
      kEmptyStrView, opts.com_opt.trace_id);
  ctx->request = std::move(req);
  ctx->response = std::make_unique<::cloudfs::ListSyncPolicyResponseProto>();
  ctx->callback = std::move(cb);
  ctx->done = &NamenodeClient::OnNNRpcDone<RPC_NN_LISTSYNCPOLICY>;
  ctx->call_id = NextCallId();
  DoSendRpc(std::move(ctx));
}

void NamenodeClient::SetLifecyclePolicy(const NNLifecyclePolicyOpts& opts,
                                        NNSimpleCb cb) {
  auto req = std::make_unique<::cloudfs::SetLifecyclePolicyRequestProto>();
  req->set_path(opts.path);
  auto* exp_rule = req->mutable_lifecyclepolicy()->mutable_exprule();
  exp_rule->set_seconds(opts.seconds);
  exp_rule->set_recycle_whole_directory(opts.whole);
  if (opts.atime_based) {
    exp_rule->set_expiration_type(::cloudfs::ExpirationTypeProto::ATIME_BASED);
  } else {
    exp_rule->set_expiration_type(::cloudfs::ExpirationTypeProto::MTIME_BASED);
  }
  CFSDLOG(DEBUG, "SetLifecyclePolicyRequestProto:\n{}", req->DebugString());
  auto ctx = std::make_unique<NNRpcCtx<RPC_NN_SETLIFECYCLEPOLICY>>(
      opts.path, opts.com_opt.trace_id);
  ctx->request = std::move(req);
  ctx->response =
      std::make_unique<::cloudfs::SetLifecyclePolicyResponseProto>();
  ctx->callback = std::move(cb);
  ctx->done = &NamenodeClient::OnNNRpcDone<RPC_NN_SETLIFECYCLEPOLICY>;
  ctx->call_id = NextCallId();
  DoSendRpc(std::move(ctx));
}

void NamenodeClient::GetLifecyclePolicy(const NNLifecyclePolicyOpts& opts,
                                        NNLifecycleCb cb) {
  auto req = std::make_unique<::cloudfs::GetLifecyclePolicyRequestProto>();
  req->set_path(opts.path);
  auto ctx = std::make_unique<NNRpcCtx<RPC_NN_GETLIFECYCLEPOLICY>>(
      opts.path, opts.com_opt.trace_id);
  ctx->request = std::move(req);
  ctx->response =
      std::make_unique<::cloudfs::GetLifecyclePolicyResponseProto>();
  ctx->callback = std::move(cb);
  ctx->done = &NamenodeClient::OnNNRpcDone<RPC_NN_GETLIFECYCLEPOLICY>;
  ctx->call_id = NextCallId();
  DoSendRpc(std::move(ctx));
}

void NamenodeClient::RmLifecyclePolicy(const NNLifecyclePolicyOpts& opts,
                                       NNSimpleCb cb) {
  auto req = std::make_unique<::cloudfs::UnsetLifecyclePolicyRequestProto>();
  req->set_path(opts.path);
  auto ctx = std::make_unique<NNRpcCtx<RPC_NN_UNSETLIFECYCLEPOLICY>>(
      opts.path, opts.com_opt.trace_id);
  ctx->request = std::move(req);
  ctx->response =
      std::make_unique<::cloudfs::UnsetLifecyclePolicyResponseProto>();
  ctx->callback = std::move(cb);
  ctx->done = &NamenodeClient::OnNNRpcDone<RPC_NN_UNSETLIFECYCLEPOLICY>;
  ctx->call_id = NextCallId();
  DoSendRpc(std::move(ctx));
}

void NamenodeClient::GetDatanodeReport(
    ::cloudfs::DatanodeReportTypeProto node_type, NNGetDatanodeReportCb cb) {
  auto req = std::make_unique<::cloudfs::GetDatanodeReportRequestProto>();
  req->set_type(node_type);
  auto ctx = std::make_unique<NNRpcCtx<RPC_NN_GETDATANODEREPORT>>();
  ctx->request = std::move(req);
  ctx->response = std::make_unique<::cloudfs::GetDatanodeReportResponseProto>();
  ctx->callback = std::move(cb);
  ctx->done = &NamenodeClient::OnNNRpcDone<RPC_NN_GETDATANODEREPORT>;
  ctx->call_id = NextCallId();
  ctx->path = kEmptyStrView;
  DoSendRpc(std::move(ctx));
}

void NamenodeClient::Load(const NNLoadOpts& opts, NNJobIdCb cb) {
  auto req = std::make_unique<::cloudfs::LoadRequestProto>();
  req->set_src(opts.path);
  req->set_recursive(opts.recursive);
  req->set_metadata(opts.metadata);
  req->set_data(opts.data);
  req->set_replicanum(opts.replica_num);

  auto ctx =
      std::make_unique<NNRpcCtx<RPC_NN_LOAD>>(opts.path, opts.com_opt.trace_id);
  ctx->request = std::move(req);
  ctx->response = std::make_unique<::cloudfs::LoadResponseProto>();
  ctx->callback = std::move(cb);
  ctx->done = &NamenodeClient::OnNNRpcDone<RPC_NN_LOAD>;
  ctx->call_id = NextCallId();
  DoSendRpc(std::move(ctx));
}

void NamenodeClient::Free(const NNFreeOpts& opts, NNJobIdCb cb) {
  auto req = std::make_unique<::cloudfs::FreeRequestProto>();
  req->set_src(opts.path);
  req->set_recursive(opts.recursive);
  req->set_metadata(opts.metadata);

  auto ctx =
      std::make_unique<NNRpcCtx<RPC_NN_FREE>>(opts.path, opts.com_opt.trace_id);
  ctx->request = std::move(req);
  ctx->response = std::make_unique<::cloudfs::FreeResponseProto>();
  ctx->callback = std::move(cb);
  ctx->done = &NamenodeClient::OnNNRpcDone<RPC_NN_FREE>;
  ctx->call_id = NextCallId();
  DoSendRpc(std::move(ctx));
}

void NamenodeClient::LookupJob(const NNLookupJobOpts& opts, NNLookupJobCb cb) {
  auto req = std::make_unique<::cloudfs::LookupJobRequestProto>();
  req->set_jobid(opts.job_id);

  auto ctx = std::make_unique<NNRpcCtx<RPC_NN_LOOKUPJOB>>(
      opts.job_id, opts.com_opt.trace_id);
  ctx->request = std::move(req);
  ctx->response = std::make_unique<::cloudfs::LookupJobResponseProto>();
  ctx->callback = std::move(cb);
  ctx->done = &NamenodeClient::OnNNRpcDone<RPC_NN_LOOKUPJOB>;
  ctx->call_id = NextCallId();
  DoSendRpc(std::move(ctx));
}

void NamenodeClient::CancelJob(const NNCancelJobOpts& opts, NNSimpleCb cb) {
  auto req = std::make_unique<::cloudfs::CancelJobRequestProto>();
  req->set_jobid(opts.job_id);

  auto ctx = std::make_unique<NNRpcCtx<RPC_NN_CANCELJOB>>(
      opts.job_id, opts.com_opt.trace_id);
  ctx->request = std::move(req);
  ctx->response = std::make_unique<::cloudfs::CancelJobResponseProto>();
  ctx->callback = std::move(cb);
  ctx->done = &NamenodeClient::OnNNRpcDone<RPC_NN_CANCELJOB>;
  ctx->call_id = NextCallId();
  DoSendRpc(std::move(ctx));
}

int32_t NamenodeClient::NextCallId() {
  // call_id is a int32_t, and it can not be LT 0!
  static constexpr uint32_t kMaxCallIdPlus1 = (1 << 31);
  uint32_t tmp = cur_call_id_.fetch_add(1, std::memory_order_relaxed);
  return static_cast<int32_t>(tmp % kMaxCallIdPlus1);
}

void NamenodeClient::UpdateMaxTxid(int64_t txid) {
  int64_t cur_txid = max_txid_.load(std::memory_order_acquire);
  if (txid <= cur_txid) {
    return;
  }
  while (!max_txid_.compare_exchange_weak(cur_txid, txid)) {
    if (txid <= cur_txid) {
      break;
    }
  }
}

byterpc::IOBuf NamenodeClient::SerializeRpcHeader(
    int32_t call_id, const std::string_view& method,
    const std::string_view& path, int64_t trace_id) {
  ::cloudfs::RpcRequestHeaderProto rpc_header;
  rpc_header.set_rpckind(::cloudfs::RPC_PROTOCOL_BUFFER);
  rpc_header.set_rpcop(
      ::cloudfs::RpcRequestHeaderProto_OperationProto_RPC_FINAL_PACKET);
  rpc_header.set_callid(call_id);
  rpc_header.set_clientid(client_id_);
  // rpc_header.set_retrycount(-1);
  rpc_header.set_clientaddress(client_ip_);

  auto* b = rpc_header.add_baggages();
  b->set_name(kFsNameKey);
  b->set_value(fs_->GetFsName());
  if (FLAGS_cfs_enable_auth) {
    CFS_DCHECK(signer_ != nullptr);
    auto sign_res = signer_->ComputeSignature(method, path);
    // stringToSign
    b = rpc_header.add_baggages();
    b->set_name(kStrToSignKey);
    b->set_value(sign_res.str_to_sign);
    // signature
    b = rpc_header.add_baggages();
    b->set_name(kSignatureKey);
    b->set_value(sign_res.signature);
    // ak
    b = rpc_header.add_baggages();
    b->set_name(kAccessKeyKey);
    b->set_value(sign_res.access_key);
    // sts token
    b = rpc_header.add_baggages();
    b->set_name(kSecurityTokenKey);
    b->set_value(sign_res.security_token);
  }
  if (FLAGS_cfs_enable_zti_auth) {
    b = rpc_header.add_baggages();
    b->set_name(kZtiTokenKey);
    b->set_value(ZtiManager::GetInstance()->GetToken());
  }
  {
    // No matter FLAGS_cfs_enable_region_aware is true or false, we both need
    // to set location_tag because NN will use it to select 'nearest' DN for
    // client to read/write.
    b = rpc_header.add_baggages();
    b->set_name(kLocationTagKey);
    b->set_value(fs_->GetLocationTagStr());
  }
  if (trace_id >= 0) {
    b = rpc_header.add_baggages();
    b->set_name(CfsTrace::GetTraceIdKey());
    b->set_value(std::to_string(trace_id));
    b = rpc_header.add_baggages();
    b->set_name(CfsTrace::GetTaskIdKey());
    b->set_value(FLAGS_cfs_task_id);
  }

  rpc_header.set_txid(max_txid_.load(std::memory_order_acquire));
  rpc_header.set_mode(fs_->GetFsMode());
  if (rpc_header.mode() == ::cloudfs::FsMode::ACC) {
    auto* acc_info = fs_->GetAccFsInfo();
    CFS_DCHECK(acc_info != nullptr);
    *(rpc_header.mutable_accfsinfo()) = *acc_info;
  }
  rpc_header.set_clientmajorversion(CFS_MAJOR_VERSION);
  rpc_header.set_clientminorversion(CFS_MINOR_VERSION);
  rpc_header.set_clientpatchversion(CFS_PATCH_VERSION);

  byterpc::IOBuf header_attachment;
  {
    // 'header_attachment' size will only be updated until
    // 'ZeroCopyOutputStream' is destroyed.
    byterpc::iobuf::ZeroCopyOutputStream iobuf_os(&header_attachment);
    bool res = rpc_header.SerializeToZeroCopyStream(&iobuf_os);
    CFS_CHECK(res);
  }
  return header_attachment;
}

template <RpcType RpcT>
void NamenodeClient::DoSendRpc(std::unique_ptr<NNRpcCtx<RpcT>> ctx) {
  auto* req = ctx->request.get();
  auto* rsp = ctx->response.get();
  // rpc_header must be re-generated because the auth info in it may become
  // out-of-date if retry too many times.
  ctx->rpc_header =
      SerializeRpcHeader(ctx->call_id, NNRpcTypeTraits<RpcT>::method_name,
                         ctx->path, ctx->trace_ctx.trace_id);
  if (ctx->trace_ctx.trace_id >= 0) {
    ctx->trace_ctx.start_us = DatetimeUtil::GetNowTimeUs();
    ctx->trace_ctx.ns_id = fs_->GetNsId();
    auto& local_ep = channel_handle_.GetLocalEndPoint();
    auto& remote_ep = channel_handle_.GetRemoteEndPoint();
    ctx->trace_ctx.src_ip = local_ep.GetAddr();
    ctx->trace_ctx.dst_ip = remote_ep.GetAddr();
    ctx->trace_ctx.src_conn_ip = local_ep.GetConnectIp();
    ctx->trace_ctx.dst_conn_ip = remote_ep.GetConnectIp();
    ctx->trace_ctx.client_name = fs_->GetClientName();
    ctx->trace_ctx.call_id = ctx->call_id;
  }
  CFSDLOG(DEBUG, "DoSendRpc: call_id={}, method={}, path={}", ctx->call_id,
          NNRpcTypeTraits<RpcT>::method_name, ctx->path);
  auto* header_ptr = &(ctx->rpc_header);
  auto done = [ctx = std::move(ctx), this](const RpcResult& res) mutable {
    auto* ctx_ptr = ctx.get();
    (this->*(ctx_ptr->done))(std::move(ctx), res);
  };
  SendByterpc(NNRpcTypeTraits<RpcT>::method, req, rsp, std::move(done),
              FLAGS_cfs_byterpc_protocol_nn, FLAGS_cfs_byterpc_nn_timeout_us,
              header_ptr, nullptr, &channel_handle_);
}

template <RpcType RpcT>
void NamenodeClient::OnNNRpcDone(std::unique_ptr<NNRpcCtx<RpcT>> ctx,
                                 const RpcResult& res) {
  auto regid = fs_->GetMetricRegistryId();
  METRIC_TIMER_ADD1(regid, NNRpcTypeTraits<RpcT>::summary_metric,
                    res.end_time_us - res.start_time_us);
  METRIC_TIMER_ADD1(regid, NNRpcTypeTraits<RpcT>::pf_serial,
                    res.profile.serialize_req_us);
  METRIC_TIMER_ADD1(regid, NNRpcTypeTraits<RpcT>::pf_writereq,
                    res.profile.write_req_us);
  METRIC_TIMER_ADD1(regid, NNRpcTypeTraits<RpcT>::pf_trans,
                    res.profile.transport_us);
  METRIC_TIMER_ADD1(regid, NNRpcTypeTraits<RpcT>::pf_waitdeserial,
                    res.profile.wait_deserialize_resp_us);
  METRIC_TIMER_ADD1(regid, NNRpcTypeTraits<RpcT>::pf_deserial,
                    res.profile.deserialize_resp_us);
  METRIC_TIMER_ADD1(regid, NNRpcTypeTraits<RpcT>::pf_connect,
                    res.profile.wait_connecting_us);
  CFS_DCHECK_GE(res.txid, 0);
  UpdateMaxTxid(res.txid);
  int64_t now_us = DatetimeUtil::GetNowTimeUs();
  int64_t now_ms = now_us / 1000;
  if (ctx->trace_ctx.trace_id >= 0) {
    if (res.err_code == CFS_OK) {
      ctx->trace_ctx.success = true;
    } else {
      ctx->trace_ctx.success = false;
      ctx->trace_ctx.msg = res.GetErrCtx();
    }
    ctx->trace_ctx.cost_us = now_us - ctx->trace_ctx.start_us;
    CfsTrace::AddTrace(ctx->trace_ctx);
  }
  if (res.err_code == CFS_OK) {
    // 1. RPC success
    METRIC_SCOPED_TIMER1(regid, NNRpcTypeTraits<RpcT>::cb_metric);
    if constexpr (UseBoolCallback<RpcT>) {
      ctx->callback(ctx->response->result());
    } else if constexpr (UseBoolCheckCallback<RpcT>) {
      if (CFS_UNLIKELY(!ctx->response->result())) {
        CfsMetrics::GetInstance()->SumPSIncWithRegister(
            regid, "nn_rpc_fail_ps1",
            {{"method", std::string(NNRpcTypeTraits<RpcT>::method_name)},
             {"err", "fail_bool_check"}},
            false, 1);
        auto msg = StringUtil::StrFormat(
            "{} response status is OK but result is false, path={}",
            NNRpcTypeTraits<RpcT>::method_name, ctx->path);
        CFSLOG(WARN, "{}", msg);
        // Should not retry in this case
        ctx->callback(Status(CFS_ERR_RPC_UNKNOWN, std::move(msg)));
      } else {
        ctx->callback(Result<>());
      }
    } else if constexpr (RpcT == RPC_NN_GETLISTING) {
      std::vector<FileInfo> flist;
      bool has_remain = false;
      if (ctx->response->has_dirlist() &&
          ctx->response->dirlist().partiallisting_size() > 0) {
        flist.reserve(ctx->response->dirlist().partiallisting_size());
        for (const auto& pb : ctx->response->dirlist().partiallisting()) {
          flist.push_back(RpcConverter::PB2FileInfo(pb));
        }
        has_remain = ctx->response->dirlist().remainingentries() > 0;
      }
      ctx->callback(std::make_pair(std::move(flist), std::move(has_remain)));
    } else if constexpr (RpcT == RPC_NN_CREATE) {
      CFSDLOG(DEBUG, "Create file success, CreateResponseProto=\n{}",
              ctx->response->DebugString());
      auto result = RpcConverter::PB2CreateRsp(*(ctx->response));
      // FileInfoResponseProto's 'path' from NN is wrong when ACC mode has a
      // prefix (e.g. if TOS prefix is '/a', and sdk create a file named '/a/b',
      // then the CreateResponse's path will be '/b').
      // We must set it using the value from Req.
      result.finfo_.SetPath(ctx->request->src());
      ctx->callback(std::move(result));
    } else if constexpr (RpcT == RPC_NN_BATCHCREATEFILE) {
      auto succ_batch_size = ctx->response->files_size();
      if (succ_batch_size > 0) {
        CFSDLOG(DEBUG, "BatchCreateFile success, ResponseProto=\n{}",
                ctx->response->DebugString());
      } else {
        CFSDLOG(WARN, "BatchCreateFile fail, ResponseProto=\n{}",
                ctx->response->DebugString());
      }
      auto res_v =
          RpcConverter::PB2BatchCreateRsp(*(ctx->response), *(ctx->request));
      ctx->callback(std::move(res_v));
    } else if constexpr (RpcT == RPC_NN_BATCHCOMPLETEFILE) {
      CFSDLOG(DEBUG, "BatchCompleteFile success, ResponseProto=\n{}",
              ctx->response->DebugString());
      auto res_v =
          RpcConverter::PB2BatchCompleteRsp(*(ctx->response), *(ctx->request));
      ctx->callback(std::move(res_v));
    } else if constexpr (RpcT == RPC_NN_APPEND) {
      CFSDLOG(DEBUG, "Append file success, AppendResponseProto=\n{}",
              ctx->response->DebugString());
      ctx->callback(RpcConverter::PB2AppendRsp(*(ctx->response)));
    } else if constexpr (RpcT == RPC_NN_GETFILEINFO) {
      CFSDLOG(DEBUG, "GetFileInfo success, ResponseProto=\n{}",
              ctx->response->DebugString());
      // When the file path does not exist, NN will not set error_code to OK
      // and set the response fs field to nullptr.
      if (ctx->response->has_fs()) {
        auto result = RpcConverter::PB2FileInfo(ctx->response->fs());
        // FileInfoResponseProto's 'path' is empty from NN. We must set it
        // manually in Client.
        result.SetPath(ctx->request->src());
        ctx->callback(std::move(result));
      } else {
        ctx->callback(Status(CFS_ERR_FILE_NOT_FOUND,
                             "File not found, path=" + ctx->request->src()));
      }
    } else if constexpr (RpcT == RPC_NN_BATCHGETFILE) {
      CFSDLOG(DEBUG, "BatchGetFile success, ResponseProto=\n{}",
              ctx->response->DebugString());
      ctx->callback(
          RpcConverter::PB2BatchFileInfo(*(ctx->response), *(ctx->request)));
    } else if constexpr (RpcT == RPC_NN_ADDBLOCK) {
      CFSDLOG(DEBUG, "AddBlock success, ResponseProto=\n{}",
              ctx->response->DebugString());
      auto lb = RpcConverter::PB2LocatedBlock(ctx->response->block());
      // New added block is always not complete.
      lb.SetComplete(false);
      ctx->callback(std::move(lb));
    } else if constexpr (RpcT == RPC_NN_GETFSSTATS) {
      CFSDLOG(DEBUG, "getFsStats success, ResponseProto=\n{}",
              ctx->response->DebugString());
      ctx->callback(RpcConverter::PB2FsStats(*(ctx->response)));
    } else if constexpr (RpcT == RPC_NN_GETDIRPOLICY) {
      CFSDLOG(DEBUG, "getDirPolicy success, ResponseProto=\n{}",
              ctx->response->DebugString());
      ctx->callback(
          RpcConverter::PB2DirPolicy(*(ctx->response), ctx->request->path()));
    } else if constexpr (RpcT == RPC_NN_LISTDIRPOLICY) {
      CFSDLOG(DEBUG, "listDirPolicy success, ResponseProto=\n{}",
              ctx->response->DebugString());
      ctx->callback(RpcConverter::PB2DirPolicies(*(ctx->response)));
    } else if constexpr (RpcT == RPC_NN_LISTSYNCPOLICY) {
      CFSDLOG(DEBUG, "listSyncPolicy success, ResponseProto=\n{}",
              ctx->response->DebugString());
      ctx->callback(RpcConverter::PB2SyncPolicies(*(ctx->response)));
    } else if constexpr (RpcT == RPC_NN_GETLIFECYCLEPOLICY) {
      CFSDLOG(DEBUG, "getLifecyclePolicy success, ResponseProto=\n{}",
              ctx->response->DebugString());
      ctx->callback(RpcConverter::PB2LifecyclePolicy(*(ctx->response)));
    } else if constexpr (RpcT == RPC_NN_GETBLOCKLOCATIONS) {
      // CFSDLOG(DEBUG, "GetBlockLocations success, ResponseProto=\n{}",
      //         ctx->response->DebugString());
      ctx->callback(RpcConverter::PB2LocatedBlocks(ctx->response->locations()));
    } else if constexpr (RpcT == RPC_NN_GETDATANODEREPORT) {
      ctx->callback(RpcConverter::PB2DatanodeInfos(*(ctx->response)));
    } else if constexpr (RpcT == RPC_NN_LOAD) {
      ctx->callback(RpcConverter::PB2JobId(*(ctx->response)));
    } else if constexpr (RpcT == RPC_NN_FREE) {
      ctx->callback(RpcConverter::PB2JobId(*(ctx->response)));
    } else if constexpr (RpcT == RPC_NN_LOOKUPJOB) {
      ctx->callback(RpcConverter::PB2LookupJobRsp(*(ctx->response)));
    } else {
      ctx->callback(Result<>());
    }
  } else {
    // 2. RPC fail
    if (res.err_code == CFS_ERR_RPC_NEED_RETRY && !ctx->last_eeof) {
      // When meet socket EOF for the 1st time, retry immediately and do not
      // inc fail_metric
      ctx->last_eeof = true;
      CFSLOG(
          INFO,
          "{} meets CFS_ERR_RPC_NEED_RETRY and retry now, path={}, channel={}",
          NNRpcTypeTraits<RpcT>::method_name, ctx->path,
          channel_handle_.ToString());
      DoSendRpc(std::move(ctx));
      return;
    }
    CfsMetrics::GetInstance()->SumPSIncWithRegister(
        regid, "nn_rpc_fail_ps1",
        {{"method", std::string(NNRpcTypeTraits<RpcT>::method_name)},
         {"err", GetErrorString(res.err_code)}},
        false, 1);
    if ((!ctx->need_retry) || (!res.Retriable())) {
      METRIC_SCOPED_TIMER1(regid, NNRpcTypeTraits<RpcT>::cb_metric);
      ctx->callback(Status(res.err_code, res.GetErrCtx(), res.exception_name));
      return;
    }
    if (now_ms - ctx->start_time_ms >= FLAGS_cfs_nn_rpc_max_retry_time_ms) {
      CFSLOG(ERROR,
             "`{}` still fails after {}ms retry, path={}, channel={}, err={}",
             NNRpcTypeTraits<RpcT>::method_name,
             FLAGS_cfs_nn_rpc_max_retry_time_ms, ctx->path,
             channel_handle_.ToString(), res.ToString());
      METRIC_SCOPED_TIMER1(regid, NNRpcTypeTraits<RpcT>::cb_metric);
      ctx->callback(Status(res.err_code, res.GetErrCtx(), res.exception_name));
      return;
    }
    int64_t sleep_ms = std::max(
        0L, ctx->next_retry_interval_ms - (now_ms - res.start_time_us / 1000));
    if (ctx->next_retry_interval_ms <= 0) {
      // next_retry_interval_ms will be set to at least 1000 in the next step
      ctx->next_retry_interval_ms = 500;
    }
    ctx->next_retry_interval_ms = std::min(ctx->next_retry_interval_ms * 2,
                                           FLAGS_cfs_nn_rpc_retry_interval_ms);
    CFSLOG(WARN,
           "`{}` got retryable error: {}. path={}. Retry after {}ms, Already "
           "retried {}ms. channel={}",
           NNRpcTypeTraits<RpcT>::method_name, res.ToString(), ctx->path,
           sleep_ms, now_ms - ctx->start_time_ms, channel_handle_.ToString());
    if (sleep_ms > 0) {
      if (g_cur_thread_pool != nullptr) {
        // addBlock, sealBlock, readBlock, writeBlock), it should not sleep
        // in current thread.
        CFSDLOG(INFO, "Add NN RPC DelayTask, method={}, path={}",
                ctx->request->GetTypeName(), ctx->path);
        g_cur_thread_pool->AddDelayTask(
            [ctx = std::move(ctx), this]() mutable {
              DoSendRpc(std::move(ctx));
            },
            sleep_ms, g_cur_thread_dispatch_id);
      } else {
        // This request was submitted by non-exector threads (e.g. ls, mkdir
        // and some other RPCs are submitted by caller threads directly), it
        // should sleep in current thread and then retry.
        CFSDLOG(INFO, "Sleep for NN RPC, method={}, path={}",
                ctx->request->GetTypeName(), ctx->path);
        std::this_thread::sleep_for(std::chrono::milliseconds(sleep_ms));
        DoSendRpc(std::move(ctx));
      }
    } else {
      DoSendRpc(std::move(ctx));
    }
  }
}

}  // namespace internal
}  // namespace cfs
