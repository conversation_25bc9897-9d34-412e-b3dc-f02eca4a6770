syntax = "proto2";
package collector;

message ApplicationMessage {
  optional bytes key = 1;
  required bytes value = 2;
  optional int32 codec = 3 [default = 0];
  // only used when the kafka hash policy is manual
  optional int32 partition = 4 [default = -1];
}

message RequestPayload {
  required string channel = 1;
  repeated ApplicationMessage messages = 2;
  optional int32 need_resp = 3;
  optional string token = 4;
}

message ResponsePayload {
  required int32 code = 1;
}
