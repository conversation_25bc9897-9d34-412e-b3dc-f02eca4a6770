#pragma once

#include <sys/socket.h>
#include <sys/un.h>

#include <cerrno>
#include <cstdint>
#include <memory>
#include <mutex>
#include <string>
#include <vector>

#include "trace/databusclient/databus_cpp_cache.h"
#include "trace/databusclient/pb_gen/collector.pb.h"

namespace cfs {
namespace databus_cpp_client {

class DatabusCollector;
class DatabusChannelImpl;

// 向databus collector 一次发送的数据量的最大大小，超过该大小则发送失败
// 注意：该值并不意味发送到kafka的单条message一定能达到此值
// 发送到kafka的单条message大小需要在channel配置中心配置
// 并且不能超过kafka集群配置的大小
extern const size_t kMaxPayloadSize;

/* type interfaces */
class DatabusChannel {
 public:
  DatabusChannel(DatabusCollector* collector, const std::string& channel,
                 bool nonblocking);

  ~DatabusChannel() = default;

  error_t send(const std::string& message, const std::string& key);
  error_t send(const std::string& message) {
    return send(message, std::string());
  }

  error_t send(const std::vector<std::string>& messages,
               const std::vector<std::string>& keys);
  error_t send(const std::vector<std::string>& messages);

  error_t send_with_response(const std::string& message,
                             const std::string& key);
  error_t send_with_response(const std::string& message) {
    return send_with_response(message, std::string());
  }

  error_t send_with_response(const std::vector<std::string>& messages,
                             const std::vector<std::string>& keys);
  error_t send_with_response(const std::vector<std::string>& messages) {
    return send_with_response(messages, std::vector<std::string>());
  }

  void add_message(const std::string& message);
  error_t emit();

  // cache adjust method
  void set_cache_max_size_bytes(int64_t max_cache_size_bytes) {
    this->databus_cache_.set_max_cache_bytes(max_cache_size_bytes);
  }

  void set_cache_tolerate_time_ms(int cache_tolerate_time_ms) {
    this->databus_cache_.set_max_cache_time_ms(cache_tolerate_time_ms);
  }

  int64_t get_cache_length() {
    return databus_cache_.get_cache_length();
  }

  int64_t get_cache_used_bytes() {
    return databus_cache_.get_cache_used_bytes();
  }

  std::string err2str(error_t err);

 public:
  enum ReturnValue : error_t {
    SUCCESS = 0,
    CHANNEL_NOT_EXIST = -1,
    BUFFER_FULL = -2,
    PERMISSION_DENY = -4,
    PERMISSION_DENY_SHADOW = -5,
    VERSION_NOT_MATCH = -499,
    NO_RESPONSE = -501,
    MSG_TOO_LARGE = -502
  };

 private:
  friend class DatabusCache;

  error_t emit_payload(const ::collector::RequestPayload& payload,
                       bool from_cache);
  error_t emit_payload_with_response(
      const ::collector::RequestPayload& payload);

  error_t _emit();

  ::collector::RequestPayload GenPayload();

 private:
  const std::string channel_;
  std::vector<std::string> messages_;  // 缓存message
  std::mutex mu_;
  std::unique_ptr<DatabusChannelImpl> impl_;
  DatabusCache databus_cache_;
};

class DatabusCollector {
 public:
  DatabusCollector();
  DatabusCollector(const std::string& socket_path,
                   const std::string& stream_socket_path);
  std::shared_ptr<DatabusChannel> get_channel(const std::string& name,
                                              bool nonblocking = true);

  const sockaddr_un* get_socket_address() const {
    return &socket_address_;
  }

  const sockaddr_un* get_stream_socket_address() const {
    return &stream_socket_address_;
  }

 private:
  sockaddr_un socket_address_;
  sockaddr_un stream_socket_address_;
};

}  // namespace databus_cpp_client
}  // namespace cfs
