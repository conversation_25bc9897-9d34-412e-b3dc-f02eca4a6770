#include "rpc/channel_handle.h"

#include <gflags/gflags.h>

#include "common/logger.h"
#include "common/string_util.h"
#include "rpc/rpc_util.h"

DECLARE_int64(cfs_byterpc_channel_timeout_ms);
DECLARE_bool(cfs_byterpc_reuse_port);

namespace cfs {
namespace internal {

ChannelHandle::ChannelHandle(const EndPoint& remote_ep,
                             ::byterpc::TransportType trans_type)
    : remote_ep_(remote_ep) {
  InitByteRpcOptions(trans_type);
}

ChannelHandle::ChannelHandle(EndPoint&& remote_ep,
                             ::byterpc::TransportType trans_type)
    : remote_ep_(std::move(remote_ep)) {
  InitByteRpcOptions(trans_type);
}

ChannelHandle::ChannelHandle(const EndPoint& remote_ep,
                             const EndPoint& local_ep,
                             ::byterpc::TransportType trans_type)
    : remote_ep_(remote_ep), local_ep_(local_ep) {
  InitByteRpcOptions(trans_type);
}

ChannelHandle::ChannelHandle(EndPoint&& remote_ep, EndPoint&& local_ep,
                             ::byterpc::TransportType trans_type)
    : remote_ep_(std::move(remote_ep)), local_ep_(std::move(local_ep)) {
  InitByteRpcOptions(trans_type);
}

std::string ChannelHandle::ToString() const {
  return StringUtil::StrFormat("[local={}, remote={}]", local_ep_.ToString(),
                               remote_ep_.ToString());
}

void ChannelHandle::InitByteRpcOptions(::byterpc::TransportType trans_type) {
  byterpc_opts_._rpc_timeout_ms = FLAGS_cfs_byterpc_channel_timeout_ms;
  byterpc_opts_._trans_type = trans_type;
  byterpc_opts_._enable_fallback = false;
  byterpc_opts_._fallback_port = -1;
  byterpc_opts_._reuse_client_port = FLAGS_cfs_byterpc_reuse_port;
}

std::shared_ptr<byterpc::Builder::Channel> ChannelHandle::GetChannel() const {
  // We must build channel in time but not save the built channel in
  // ChannelHandle because the channel can only be used in one thread.
  // ChannelHandle may be shared by mutiple threads. So we should either
  // save the channel as a thread-local variable or build channel in time.
  byterpc::Builder builder;
  if (local_ep_.Empty()) {
    return builder.BuildChannel(remote_ep_.GetByterpcEndPoint(), byterpc_opts_);
  } else {
    // Return nullptr if `BuildChannel` fails
    return builder.BuildChannel(remote_ep_.GetByterpcEndPoint(),
                                local_ep_.GetByterpcEndPoint(), byterpc_opts_);
  }
}

}  // namespace internal
}  // namespace cfs
