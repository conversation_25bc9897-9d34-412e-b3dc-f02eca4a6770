#pragma once

#include <byterpc/util/endpoint.h>

namespace cfs {
namespace internal {

class EndPoint {
 public:
  EndPoint() = default;

  EndPoint(const std::string& addr, const std::string& connect_ip,
           uint32_t port);
  EndPoint(std::string&& addr, std::string&& connect_ip, uint32_t port);

  EndPoint(const std::string& addr, const in6_addr& connect_ip, uint32_t port);

  EndPoint(const std::string& addr, const in6_addr& connect_ip, uint32_t port,
           const std::string& conn_ip_str);

  const std::string& GetAddr() const {
    return addr_;
  }

  const std::string& GetConnectIp() const {
    return connect_ip_;
  }

  // Only set connect_ip_str for print-usage. Do not actually update the
  // byterpc_ep_
  void SetConnIpStr(const std::string& ip) {
    connect_ip_ = ip;
  }

  void SetPort(uint32_t port) {
    byterpc_ep_.port = port;
  }

  uint32_t GetPort() const {
    return byterpc_ep_.port;
  }

  // void SetAz(const std::string& az) {
  //   az_ = az;
  // }
  //
  // const std::string& GetAz() const {
  //   return az_;
  // }

  const byterpc::util::EndPoint& GetByterpcEndPoint() const {
    return byterpc_ep_;
  }

  std::string ToString() const;

  bool Empty() const;

 private:
  std::string addr_;
  std::string connect_ip_;
  // LocationAz is used to track cross-AZ throughput
  // std::string az_;
  // default value of byterpc::util::EndPoint is IP_ANY
  byterpc::util::EndPoint byterpc_ep_;
};

}  // namespace internal
}  // namespace cfs
