#include "rpc/endpoint.h"

#include <arpa/inet.h>   // inet_pton
#include <netinet/in.h>  // in_addr, in6_addr

#include <string>

#include "common/logger.h"
#include "common/net_util.h"
#include "common/string_util.h"

namespace cfs {
namespace internal {

static struct in6_addr Str2Ip6(const std::string& ip_str) {
  if (CFS_UNLIKELY(ip_str.empty())) {
    return byterpc::util::IP_NONE;
  }

  struct in_addr ip4;
  if (inet_pton(AF_INET, ip_str.c_str(), &ip4) > 0) {
    // Ipv4 address
    return byterpc::util::ipv4_to_ipv6(ip4);
  }

  struct in6_addr ip6;
  if (inet_pton(AF_INET6, ip_str.c_str(), &ip6) > 0) {
    // Ipv6 address
    return ip6;
  }
  CFSLOG(ERROR, "Invalid ip_str={} when parsing ip from string. Return IP_NONE",
         ip_str);
  return byterpc::util::IP_NONE;
}

EndPoint::EndPoint(const std::string& addr, const std::string& connect_ip,
                   uint32_t port)
    : addr_(addr), connect_ip_(connect_ip) {
  byterpc_ep_.ip = Str2Ip6(connect_ip_);
  byterpc_ep_.port = port;
}

EndPoint::EndPoint(std::string&& addr, std::string&& connect_ip, uint32_t port)
    : addr_(std::move(addr)), connect_ip_(std::move(connect_ip)) {
  byterpc_ep_.ip = Str2Ip6(connect_ip_);
  byterpc_ep_.port = port;
}

EndPoint::EndPoint(const std::string& addr, const in6_addr& connect_ip,
                   uint32_t port)
    : addr_(addr) {
  byterpc_ep_.ip = connect_ip;
  byterpc_ep_.port = port;
}

EndPoint::EndPoint(const std::string& addr, const in6_addr& connect_ip,
                   uint32_t port, const std::string& conn_ip_str)
    : addr_(addr), connect_ip_(conn_ip_str) {
  byterpc_ep_.ip = connect_ip;
  byterpc_ep_.port = port;
}

std::string EndPoint::ToString() const {
  return StringUtil::StrFormat("[addr={}, connect_ip={}, port={}]", addr_,
                               byterpc::util::ip2str(byterpc_ep_.ip).c_str(),
                               byterpc_ep_.port);
}

bool EndPoint::Empty() const {
  return memcmp(&byterpc_ep_.ip, &in6addr_any, sizeof(byterpc_ep_.ip)) == 0;
}

}  // namespace internal
}  // namespace cfs
