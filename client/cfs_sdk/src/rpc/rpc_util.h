#pragma once

#include <byterpc/exec_ctx.h>
#include <byterpc/rpc.h>

#include "RpcHeader.pb.h"
#include "common/cfs_error.h"
#include "hdfs.pb.h"

namespace cfs {
namespace internal {

// This class contains utility functions used by RPC module.
class RpcUtil {
 public:
  static byterpc::ProtocolType Str2Protocol(const std::string& protocol_str);

  static byterpc::TransportType Str2TransType(const std::string& trans_str);

  static Errorcode ByterpcErrToCfsErr(const int err);
  // Return -1 when 'e' is not an ByteRpcErr
  static int CfsErr2ByterpcErr(Errorcode e);

  static Errorcode CfsStatusProtoToCfsErr(const ::cloudfs::Status s);
  static ::cloudfs::Status CfsErr2CfsStatusProto(Errorcode e);

  static Errorcode HRpcErrToCfsErr(
      const ::cloudfs::RpcResponseHeaderProto_RpcErrorCodeProto e,
      const std::string& ex);
  static ::cloudfs::RpcResponseHeaderProto_RpcErrorCodeProto CfsErr2HRpcErr(
      Errorcode e);

  static bool IsRpcErrRetriable(Errorcode e);

  static uint64_t GenerateRpcLogId();

  // ByteRPC env should be inited only globally.
  static Errorcode InitByteRpcEnvOnce();

  static void DestroyByteRpcEnv();

  // Each thread should init byterpc ctx before sending byterpc call.
  // The average latency for this operation is 300~800us for the first time
  // and <1us later in each thread.
  static void InitByteRpcThreadCtx(byterpc::loop_type_t loop_type);

  static void ByteRpcLoopOnce();

  static void ByteRpcLoopUntilQuit();
  static void ByteRpcQuitLoop();
};

}  // namespace internal
}  // namespace cfs
