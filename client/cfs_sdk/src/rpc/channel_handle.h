#pragma once

#include <byterpc/builder.h>

#include <memory>

#include "rpc/endpoint.h"

namespace cfs {
namespace internal {

class ChannelHandle {
 public:
  ChannelHandle() = default;

  // When local_ep is omitted, default local NIC (i.e. the first local NIC
  // address) will be used.
  ChannelHandle(const EndPoint& remote_ep, ::byterpc::TransportType trans_type);
  ChannelHandle(EndPoint&& remote_ep, ::byterpc::TransportType trans_type);

  ChannelHandle(const EndPoint& remote_ep, const EndPoint& local_ep,
                ::byterpc::TransportType trans_type);
  ChannelHandle(EndPoint&& remote_ep, EndPoint&& local_ep,
                ::byterpc::TransportType trans_type);

  const EndPoint& GetLocalEndPoint() const {
    return local_ep_;
  }

  const EndPoint& GetRemoteEndPoint() const {
    return remote_ep_;
  }

  template <class T>
  void SetLocalEndPoint(T&& ep) {
    local_ep_ = std::forward<T>(ep);
  }

  template <class T>
  void SetRemoteEndPoint(T&& ep) {
    remote_ep_ = std::forward<T>(ep);
  }

  EndPoint* MutableRemoteEndPoint() {
    return &remote_ep_;
  }

  void SetRemotePortReliable(bool reliable) {
    remote_port_reliable_ = reliable;
  }

  bool IsRemotePortReliable() const {
    return remote_port_reliable_;
  }

  std::shared_ptr<byterpc::Builder::Channel> GetChannel() const;

  std::string ToString() const;

 private:
  void InitByteRpcOptions(::byterpc::TransportType trans_type);

 private:
  EndPoint remote_ep_;
  EndPoint local_ep_;
  byterpc::Builder::ChannelOptions byterpc_opts_;
  bool remote_port_reliable_{true};
};

}  // namespace internal
}  // namespace cfs
