#pragma once

#include <string>

#include "common/cfs_error.h"

namespace cfs {
namespace internal {

struct RpcProfile {
  // Protocol serialization
  uint32_t serialize_req_us;
  // Request submission latency. E.g., the cost of `writev` a request in kTCP
  uint32_t write_req_us;
  // Transport layer latency, including RTTs, server time, and the time of
  // reading all packets of the current response
  uint32_t transport_us;
  // Includes the waiting time for handling preceding requests if reading
  // multiple responses at once
  uint32_t wait_deserialize_resp_us;
  // Parsing meta && attachment && protobuf
  uint32_t deserialize_resp_us;
  // Fiber ONLY, `_client_wait_user_cb` in TimeProfiler
  uint32_t client_wait_user_cb_us;
  // Used for requests waiting for connection, its value is interpreted as:
  // - 0: default, ignore
  // - others: latecy of waiting for connection establishment, which is set
  //           ONLY in requests sent before connection finished
  uint64_t wait_connecting_us;
};

struct RpcResult {
  Errorcode err_code;
  std::string exception_name;
  std::string err_msg;
  int64_t txid{0};
  int64_t start_time_us;
  int64_t end_time_us;
  RpcProfile profile;

  RpcResult() : err_code(CFS_OK) {}
  RpcResult(Errorcode e) : err_code(e) {}
  RpcResult(Errorcode e, int64_t _txid) : err_code(e), txid(_txid) {}

  RpcResult(Errorcode e, std::string ex_name, std::string msg, int64_t _txid);
  RpcResult(Errorcode e, std::string ex_name, std::string msg)
      : RpcResult(e, std::move(ex_name), std::move(msg), 0) {}

  void set_err_code(Errorcode e) {
    err_code = e;
  }

  template <class T>
  void set_exception_name(T&& ex) {
    exception_name = std::forward<T>(ex);
  }

  template <class T>
  void set_err_msg(T&& msg) {
    err_msg = std::forward<T>(msg);
  }

  std::string ToString() const;

  std::string GetErrCtx() const;

  bool Retriable() const;
};

}  // namespace internal
}  // namespace cfs
