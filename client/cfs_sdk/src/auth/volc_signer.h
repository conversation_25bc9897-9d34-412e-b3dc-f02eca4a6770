#pragma once

#include <memory>
#include <string>
#include <string_view>

#include "auth/volc_credential.h"

namespace cfs {
namespace internal {

struct SignResult {
  std::string str_to_sign;
  std::string signature;
  std::string access_key;
  std::string security_token;
};

class VolcSigner {
 public:
  VolcSigner(const std::string& fs_name, const std::string& region,
             const std::string& service, const std::string& ns_id);

  SignResult ComputeSignature(const std::string_view& method,
                              const std::string_view& path);

 private:
  std::string GenSignKey(const VolcCredential& cred,
                         const std::string& time_str) const;
  std::string GenStrToSign(const std::string& time_str,
                           const std::string_view& method,
                           const std::string_view& path) const;
  std::string CanonicalRequest(const std::string& time_str,
                               const std::string_view& method,
                               const std::string_view& path) const;

 private:
  std::string region_;
  std::string service_;
  std::string ns_id_;
  std::unique_ptr<CredentialProvider> cred_provider_;
};

}  // namespace internal
}  // namespace cfs
