#pragma once

#include <string>

#include "protocol/located_blocks.h"

namespace cfs {
namespace internal {

class FileInfo {
 public:
  // The enum value for FileType corresponds with the value of
  // HdfsFileStatusProto::FileType.
  enum FileType : int32_t {
    // proto type does not has UNKNOWN, and IS_UNKNOWN should never be used.
    IS_UNKNOWN = 0,
    IS_DIR = 1,
    IS_FILE = 2,
    IS_SYMLINK = 3
  };

  // The enum value for AccFileStatus corresponds with the value of
  // HdfsFileStatusProto::AccFileStatus.
  enum AccFileStatus : int32_t {
    // proto type does not has UNKNOWN, and ACC_UNKNOWN should never be used.
    ACC_UNKNOWN = 0,
    ACC_FILE_LOCAL = 1,
    ACC_FILE_TO_BE_PERSISTED = 2,
    ACC_FILE_PERSISTED = 3,
    ACC_DIR_LOCAL = 4,
    ACC_DIR_INCOMPLETE = 5,
    ACC_DIR_SYNCED = 6
  };

  FileInfo() = default;

  FileType GetFileType() const {
    return type_;
  }

  void SetFileType(FileType type) {
    type_ = type;
  }

  const std::string& GetPath() const {
    return path_;
  }

  template <class T>
  void SetPath(T&& path) {
    path_ = std::forward<T>(path);
  }

  uint64_t GetLength() const {
    return length_;
  }

  void SetLength(uint64_t len) {
    length_ = len;
  }

  uint32_t GetPermission() const {
    return permission_;
  }

  void SetPermission(uint32_t permission) {
    permission_ = permission;
  }

  const std::string& GetOwner() const {
    return owner_;
  }

  template <class T>
  void SetOwner(T&& owner) {
    owner_ = std::forward<T>(owner);
  }

  const std::string& GetGroup() const {
    return group_;
  }

  template <class T>
  void SetGroup(T&& group) {
    group_ = std::forward<T>(group);
  }

  int64_t GetMtime() const {
    return mtime_;
  }

  void SetMtime(int64_t mtime) {
    mtime_ = mtime;
  }

  int64_t GetAtime() const {
    return atime_;
  }

  void SetAtime(int64_t atime) {
    atime_ = atime;
  }

  const std::string& GetUCClientName() const {
    return uc_client_name_;
  }

  template <class T>
  void SetUCClientName(T&& uc_client_name) {
    uc_client_name_ = std::forward<T>(uc_client_name);
  }

  const std::string& GetSymlink() const {
    return symlink_;
  }

  template <class T>
  void SetSymlink(T&& symlink) {
    symlink_ = std::forward<T>(symlink);
  }

  uint32_t GetReplication() const {
    return replication_;
  }

  void SetReplication(uint32_t replication) {
    replication_ = replication;
  }

  uint64_t GetBlocksize() const {
    return blocksize_;
  }

  void SetBlocksize(uint64_t blocksize) {
    blocksize_ = blocksize;
  }

  uint64_t GetFileId() const {
    return file_id_;
  }

  void SetFileId(uint64_t file_id) {
    file_id_ = file_id;
  }

  const LocatedBlocks& GetLocatedBlocks() const {
    return lbs_;
  }

  LocatedBlocks&& MoveLocatedBlocks() {
    return std::move(lbs_);
  }

  template <class T>
  void SetLocatedBlocks(T&& lbs) {
    lbs_ = std::forward<T>(lbs);
  }

  AccFileStatus GetAccStatus() const {
    return acc_status_;
  }

  void SetAccStatus(AccFileStatus acc_status) {
    acc_status_ = acc_status;
  }

 private:
  FileType type_;
  // 'path' is abs-path in Create and GetFileInfo. But in ListDir, it is
  // last-level relative path (file_name).
  // TODO(dbc) make path_ abs-path in ListDir.
  std::string path_;
  uint64_t length_;
  uint32_t permission_;
  std::string owner_;
  std::string group_;
  int64_t mtime_;
  int64_t atime_;
  std::string uc_client_name_;  // under_construction_client_name
  std::string symlink_;
  uint32_t replication_;
  uint64_t blocksize_;
  uint64_t file_id_;
  // int32_t children_num;  // not used
  // FileEncryptionInfo fileEncryption;  // not used
  LocatedBlocks lbs_;
  AccFileStatus acc_status_;
};

}  // namespace internal
}  // namespace cfs
