#pragma once

#include <map>
#include <memory>
#include <shared_mutex>
#include <string>
#include <type_traits>
#include <unordered_map>
#include <vector>

#include "common/logger.h"
#include "metric/metric_sdk/counter.h"
#include "metric/metric_sdk/gauge.h"
#include "metric/metric_sdk/metric_id.h"
#include "metric/metric_sdk/sum_per_second.h"
#include "metric/metric_sdk/summary.h"

namespace cfs {
namespace internal {

class MetricVisitor {
 public:
  virtual ~MetricVisitor() = default;

  // It is safe to deregister a metric even the current one in Visit().
  virtual void Visit(const MetricId& id,
                     const std::shared_ptr<Counter>& counter) = 0;

  virtual void Visit(const MetricId& id,
                     const std::shared_ptr<Gauge>& gauge) = 0;

  virtual void Visit(const MetricId& id,
                     const std::shared_ptr<SumPerSecond>& sps) = 0;

  virtual void Visit(const MetricId& id,
                     const std::shared_ptr<Summary>& summary) = 0;
};

class MetricRegistry {
 public:
  MetricRegistry() : MetricRegistry(std::string(), {}) {}

  MetricRegistry(std::string prefix) : MetricRegistry(std::move(prefix), {}) {}

  MetricRegistry(std::string prefix, std::map<std::string, std::string> tags);

  ~MetricRegistry() = default;

  template <class T, class... Args>
  std::shared_ptr<T> Register(const MetricId& id, Args&&... args) {
    auto wrapped_id = WrapMetricId(id);
    std::unique_lock<std::shared_mutex> write_lk(mtx_);
    auto res = GetMetricMap<T>()->try_emplace(
        wrapped_id, std::make_shared<T>(std::forward<Args>(args)...));
    if (CFS_UNLIKELY(!res.second)) {
      CFSLOG(FATAL, "Duplicate metric: {}", wrapped_id.ToString());
    }
    return res.first->second;
  }

  template <class T>
  void Deregister(MetricId id) {
    auto wrapped_id = WrapMetricId(id);
    std::lock_guard<std::mutex> lk(mtx_);
    auto* m = GetMetricMap<T>();
    auto itr = m.find(wrapped_id);
    if (itr != m.end()) {
      m.erase(itr);
    } else {
      CFSLOG(FATAL, "Deregiester un-existed metric: {}", wrapped_id.ToString());
    }
  }

  template <class T>
  std::shared_ptr<T> GetMetric(MetricId id) {
    auto wrapped_id = WrapMetricId(id);
    std::shared_lock<std::shared_mutex> read_lk(mtx_);
    auto* m = GetMetricMap<T>();
    auto itr = m.find(wrapped_id);
    if (itr == m.end()) {
      return nullptr;
    }
    return itr->second;
  }

  template <class T, class... Args>
  std::pair<bool, std::shared_ptr<T>> GetOrRegister(const MetricId& id,
                                                    Args&&... args) {
    auto wrapped_id = WrapMetricId(id);
    {
      std::shared_lock<std::shared_mutex> read_lk(mtx_);
      auto* m = GetMetricMap<T>();
      auto itr = m->find(wrapped_id);
      if (itr != m->end()) {
        return std::make_pair(false, itr->second);
      }
    }
    {
      std::unique_lock<std::shared_mutex> write_lk(mtx_);
      bool is_insert = false;
      auto res = GetMetricMap<T>()->try_emplace(
          wrapped_id, std::make_shared<T>(std::forward<Args>(args)...));
      if (res.second) {
        is_insert = true;
        CFSDLOG(DEBUG, "Insert metric in GetOrRegister: {}",
                wrapped_id.ToString());
      } else {
        CFSDLOG(DEBUG, "Get existing metric in GetOrRegister: {}",
                wrapped_id.ToString());
      }
      return std::make_pair(is_insert, res.first->second);
    }
  }

  void Accept(MetricVisitor* visitor) const;

 private:
  template <class T>
  using MetricMap =
      std::unordered_map<MetricId, std::shared_ptr<T>, MetricId::HashFunc>;

  MetricId WrapMetricId(const MetricId& id) const;

  template <class T>
  MetricMap<T>* GetMetricMap() {
    if constexpr (std::is_same_v<T, Counter>) {
      return &counter_map_;
    } else if constexpr (std::is_same_v<T, Gauge>) {
      return &gauge_map_;
    } else if constexpr (std::is_same_v<T, SumPerSecond>) {
      return &sps_map_;
    } else if constexpr (std::is_same_v<T, Summary>) {
      return &summary_map_;
    } else {
      CFSLOG(FATAL, "Impossible");
    }
  }

 private:
  MetricMap<Counter> counter_map_;
  MetricMap<Gauge> gauge_map_;
  MetricMap<SumPerSecond> sps_map_;
  MetricMap<Summary> summary_map_;
  mutable std::shared_mutex mtx_;

  std::string prefix_;
  std::vector<MetricTag> tags_;
};

}  // namespace internal
}  // namespace cfs
