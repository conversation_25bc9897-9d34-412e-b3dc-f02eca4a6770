#pragma once

#include <condition_variable>
#include <deque>
#include <memory>
#include <mutex>
#include <string>
#include <thread>

#include "metric/metric_sdk/metric_collector.h"
#include "metric/metric_sdk/metric_registry.h"

namespace cfs {
namespace internal {

class MetricReporter {
 public:
  Met<PERSON><PERSON>eporter(
      const std::string& name, int64_t interval_ms,
      std::unique_ptr<MetricEmitter> emitter,
      std::function<std::deque<std::shared_ptr<MetricRegistry>>()> getter);

  ~MetricReporter();

  void Start();

  void Stop();

 private:
  void WorkRoutine();

  void DoReport();

 private:
  std::string name_;
  // metrics report interval
  int64_t interval_ms_;
  std::function<std::deque<std::shared_ptr<MetricRegistry>>()> reg_getter_;
  int64_t last_report_ms_;
  MetricCollector collector_;
  std::thread worker_thread_;
  bool stopped_{true};
  std::mutex stop_mtx_;
  std::condition_variable stop_cv_;
};

}  // namespace internal
}  // namespace cfs
