#pragma once

#include <netinet/in.h>
#include <sys/socket.h>
#include <sys/un.h>

#include <map>
#include <memory>
#include <string>
#include <string_view>
#include <vector>

#include "metric/metric_sdk/metric_emitter.h"

namespace cfs {
namespace internal {

struct MetricServerConf {
  std::string unix_domain;
  std::string tcp_addr;
  uint32_t tcp_port;
};

// A helper class to manages a connection and send metrics records to a metrics
// server backend.
class MetricServerEmitter : public MetricEmitter {
 public:
  explicit MetricServerEmitter(const MetricServerConf& conf) : conf_(conf) {}

  virtual ~MetricServerEmitter();

  bool Prepare() override;

  bool Emit(const std::string& name, int64_t value, const MetricId& id,
            int64_t time_sec) override;

  bool Emit(const std::string& name, double value, const MetricId& id,
            int64_t time_sec) override;

  bool Flush() override;

 private:
  // A help class to serialize a sequence of metrics records as a two-level
  // nested array using the MessagePack format, as defined at:
  // https://github.com/msgpack/msgpack/blob/master/spec.md#array-format-family
  //
  // Each metrics record is represented by a 6-tuple:
  //   (msg_type, metric_type, name, value, tags, timestamp)
  // and is serialized as a "level-one" array. One or more of such
  // serialized "level-one" arrays are again serialized into a bigger
  // "level-two" array, where each element of the "level-two" array is a
  // serialized "level-one" array. The serialized "level-two" array thus
  // represents an array of metrics record, and is sent to the metrics backend
  // as a single message.
  class MetricRequest {
   public:
    MetricRequest() {
      Reset();
    }

    void Reset() {
      buf_.resize(kReserveSize);
      num_elements_ = 0;
      offset_ = kReserveSize;
    }

    const char* GetData() const {
      return buf_.data() + offset_;
    }

    size_t GetSize() const {
      return buf_.size() - offset_;
    }

    bool IsEmpty() const {
      return num_elements_ == 0;
    }

    bool IsFull() const {
      return buf_.size() >= kFullDatagramSize;
    }

    bool Append(const std::vector<std::string_view>& record);

    void Finalize();

   private:
    static constexpr size_t kReserveSize = 5;

    // Ethernet MTU is default set to 1500. On Internet, the MTU of routers may
    // be 576 bytes. So we set kFullDatagramSize to 576 - 20 (IP header)
    // - 8 (UDP header) = 548 bytes.
    static constexpr size_t kFullDatagramSize = 548;

    bool IsFinalized() const {
      return offset_ != kReserveSize;
    }

    void AppendString(const std::string_view& s);

    std::string buf_;
    size_t num_elements_;
    size_t offset_;
  };

  bool DoEmit(const std::string& name, const std::string& value,
              const MetricId& id, int64_t time_sec);

  bool Send(const void* data, std::size_t len, int flags = 0);

  // Try to connect to the metrics backend. Returns true if connection is
  // successfully established. Retry will be throttled to avoid too many
  // connection attempts to overwhelm the metrics backend.
  bool Connect();

  // Close the connection to the metrics backend.
  void Close();

 private:
  union SockAddrUnion {
    struct sockaddr_un unix_domain_;
    struct sockaddr_in ip4_ep_;
    struct sockaddr_in6 ip6_ep_;
  };

  MetricServerConf conf_;
  bool prepared_{false};

  int64_t last_connect_time_ms_{0};

  SockAddrUnion sockaddr_;

  sockaddr* sockaddr_ptr_;
  socklen_t sock_len_;

  int sock_{-1};

  // The buffered messages to the metrics backend.
  MetricRequest req_;
};

}  // namespace internal
}  // namespace cfs
