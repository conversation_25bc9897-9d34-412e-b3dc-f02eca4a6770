#include "metric/metric_sdk/metric_reporter.h"

#include "common/datetime_util.h"
#include "common/logger.h"

namespace cfs {
namespace internal {

MetricReporter::MetricReporter(
    const std::string& name, int64_t interval_ms,
    std::unique_ptr<MetricEmitter> emitter,
    std::function<std::deque<std::shared_ptr<MetricRegistry>>()> getter)
    : interval_ms_(interval_ms),
      reg_getter_(std::move(getter)),
      collector_(std::move(emitter)) {
  // thread name must not be larger than 16 bytes (including '\0')
  if (!name.empty()) {
    name_ = name.substr(0, 15);
  } else {
    name_ = "MetricReporter";
  }
  last_report_ms_ = DatetimeUtil::GetNowTimeMs();
}

MetricReporter::~MetricReporter() {
  if (!stopped_) {
    Stop();
  }
}

void MetricReporter::Start() {
  CFS_DCHECK(stopped_);
  stopped_ = false;
  worker_thread_ = std::thread([this]() {
    WorkRoutine();
  });
  int res = pthread_setname_np(worker_thread_.native_handle(), name_.c_str());
  if (res != 0) {
    CFSLOG(WARN, "Fail to set thread name of CfsMetricReporter, errno={}", res);
  }
}

void MetricReporter::Stop() {
  CFS_DCHECK(!stopped_);
  {
    std::unique_lock<std::mutex> lk(stop_mtx_);
    stopped_ = true;
  }
  stop_cv_.notify_all();
  if (worker_thread_.joinable()) {
    worker_thread_.join();
  }
}

void MetricReporter::WorkRoutine() {
  std::unique_lock<std::mutex> lk(stop_mtx_);
  while (!stopped_) {
    int64_t now1 = DatetimeUtil::GetNowTimeMs();
    if (now1 >= last_report_ms_ + interval_ms_) {
      DoReport();
      last_report_ms_ += interval_ms_;
      int64_t now2 = DatetimeUtil::GetNowTimeMs();
      if (now2 >= last_report_ms_ + interval_ms_) {
        CFSLOG(WARN,
               "Last metric report takes {}ms while report interval is {}ms. "
               "Skip some interval",
               now2 - now1, interval_ms_);
        do {
          last_report_ms_ += interval_ms_;
        } while (now2 >= last_report_ms_ + interval_ms_);
      }
    }

    auto tp = std::chrono::time_point<std::chrono::system_clock>(
        std::chrono::milliseconds(last_report_ms_ + interval_ms_));
    stop_cv_.wait_until(lk, tp);
  }
}

void MetricReporter::DoReport() {
  CFSLOG(INFO, "Start current metric collection round");
  if (!collector_.Begin()) {
    // Fail to start collector for this round. Skip report until next round
    return;
  }
  auto reg_list = reg_getter_();
  for (const auto& reg : reg_list) {
    reg->Accept(&collector_);
    // We must flush when each-registry finish so that common_tags are sured
    // to be re-set.
    collector_.Flush();
  }
  collector_.Finish();
  CFSLOG(INFO, "Finish current metric collection round");
}

}  // namespace internal
}  // namespace cfs
