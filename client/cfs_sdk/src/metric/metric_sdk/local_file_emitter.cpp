#include "metric/metric_sdk/local_file_emitter.h"

#include <fcntl.h>
#include <unistd.h>

#include <filesystem>

#include "common/fs_util.h"
#include "common/logger.h"
#include "common/string_util.h"

namespace cfs {
namespace internal {

static const std::string kLocalMetricFileName = "cfs_metric.prometheus.log";
static const std::string kLocalMetricTmpFile = "cfs_metric.prometheus.log.tmp";
static constexpr uint64_t kMaxMetricBufLen = 2 * 1024 * 1024;

static std::string EncodeTags(const std::vector<MetricTag>& common_tags,
                              const std::map<std::string, std::string>& tags) {
  std::string ret("{");
  for (const auto& tag : common_tags) {
    ret.append(tag.key_);
    ret.append("=\"");
    ret.append(tag.val_);
    ret.append("\",");
  }
  for (const auto& tag : tags) {
    ret.append(tag.first);
    ret.append("=\"");
    ret.append(tag.second);
    ret.append("\",");
  }
  if (ret.back() == ',') {
    ret.back() = '}';
    return ret;
  } else {
    return std::string();
  }
}

bool LocalFileEmitter::Prepare() {
  if (prepared_) {
    return prepared_;
  }

  dst_dir_ = FsUtil::AbsoluteCanonicalHomePath(dst_dir_);
  std::filesystem::path dir_path(dst_dir_);
  std::error_code ec;
  std::filesystem::create_directories(dir_path, ec);
  ec.clear();
  auto fst = std::filesystem::status(dir_path, ec);
  if ((fst.type() != std::filesystem::file_type::directory) ||
      (access(dir_path.c_str(), R_OK | W_OK | X_OK) != 0)) {
    CFSLOG(ERROR,
           "Can not create/access local_metric dir `{}`, err_code={}, errno={}",
           ec.message(), errno);
    return prepared_;
  }
  if (dst_dir_.back() != '/') {
    dst_dir_.push_back('/');
  }
  dst_file_ = dst_dir_ + kLocalMetricFileName;
  tmp_file_ = dst_dir_ + kLocalMetricTmpFile;

  CFSLOG(INFO, "Initing LocalMetricEmitter with dst_file={}", dst_file_);
  prepared_ = true;
  return prepared_;
}

// Gen the TYPE of a metric in prometheus format
static std::string GenMetricTypeStr(const std::string& name) {
  return StringUtil::StrCat("# TYPE ", name, " counter\n");
}

bool LocalFileEmitter::Emit(const std::string& name, int64_t value,
                            const MetricId& id, int64_t time_sec) {
  return Emit(name, static_cast<double>(value), id, time_sec);
}

bool LocalFileEmitter::Emit(const std::string& name, double value,
                            const MetricId& id, int64_t time_sec) {
  if (metric_family_.find(name) == metric_family_.end()) {
    // meet this name for the first time. Output its TYPE first
    metric_buf_.append(GenMetricTypeStr(name));
    metric_family_.emplace(name);
  }
  metric_buf_.append(name);
  metric_buf_.append(EncodeTags(id.GetCommonTags(), id.GetTags()));
  metric_buf_.push_back(' ');

  // value should never be NaN
  CFS_DCHECK(!std::isnan(value));
  if (std::isinf(value)) {
    metric_buf_.append(value < 0 ? "-Inf" : "+Inf");
  } else {
    metric_buf_.append(std::to_string(value));
  }

  metric_buf_.push_back(' ');
  // nano seconds
  metric_buf_.append(std::to_string(time_sec * 1000000000));
  metric_buf_.push_back('\n');
  if (metric_buf_.size() >= kMaxMetricBufLen) {
    return Flush();
  }
  return true;
}

static int CreateLocalFile(const std::string& path) {
  int fd = open(path.c_str(), O_WRONLY | O_CREAT | O_TRUNC, 0644);
  if (fd < 0) {
    CFSLOG(ERROR,
           "Fail to CreateLocalFile in LocalMetricEmitter, path={}, errno={}",
           path, errno);
    return -1;
  }
  return fd;
}

static bool WriteLocalFile(const std::string& content, int fd) {
  size_t len = content.size();
  const char* ptr = content.data();
  size_t finish = 0;
  while (finish < len) {
    auto res = write(fd, ptr, len - finish);
    if (res < 0) {
      if (errno == EAGAIN || errno == EWOULDBLOCK) {
        continue;
      } else {
        CFSLOG(ERROR, "Fail to write metrics to LocalFile, errno={}", errno);
        return false;
      }
    }
    auto w = static_cast<size_t>(res);
    finish += w;
    ptr = reinterpret_cast<const char*>(reinterpret_cast<uint64_t>(ptr) + w);
  }
  return true;
}

bool LocalFileEmitter::Flush() {
  if (metric_buf_.empty()) {
    return true;
  }
  if (tmp_fd_ < 0) {
    tmp_fd_ = CreateLocalFile(tmp_file_);
    if (tmp_fd_ < 0) {
      metric_buf_.clear();
      return false;
    }
  }
  CFS_DCHECK_GT(tmp_fd_, 0);
  auto ret = WriteLocalFile(metric_buf_, tmp_fd_);
  // No matter Flush success or not, clear the buf
  metric_buf_.clear();
  return ret;
}

void LocalFileEmitter::Finalize() {
  metric_family_.clear();
  if (tmp_fd_ <= 0) {
    return;
  }
  if (close(tmp_fd_) < 0) {
    CFSLOG(ERROR, "Fail to close tmp_file, errno={}", errno);
  }
  tmp_fd_ = -1;
  int res1 = rename(tmp_file_.c_str(), dst_file_.c_str());
  if (res1 < 0) {
    CFSLOG(ERROR,
           "Fail to rename tmp_file to dst_file in LocalFile metric emitter, "
           "old_path={}, new_path={}, errno={}",
           tmp_file_, dst_file_, errno);
  }
}

}  // namespace internal
}  // namespace cfs
