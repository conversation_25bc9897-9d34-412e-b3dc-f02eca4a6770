#pragma once

#include <atomic>
#include <condition_variable>
#include <function2/function2.hpp>
#include <list>
#include <memory>
#include <mutex>
#include <queue>
#include <string>
#include <thread>
#include <vector>

#include "common/macros.h"

namespace cfs {
namespace internal {

class ThreadPool {
 public:
  using Task = fu2::unique_function<void()>;

  // If num_threads == 0, the thread number will be set to num-of-logical-core.
  ThreadPool(uint32_t num_threads, const std::string& name)
      : ThreadPool(num_threads, name, std::function<bool()>(),
                   std::function<void()>(), 0, -1, false) {}

  // even there is no flying-tasks and pending-tasks, `looper` still runs every
  // `loop_sec` if `loop_sec > 0`.
  ThreadPool(uint32_t num_threads, const std::string& name,
             std::function<bool()> looper, std::function<void()> initer,
             uint32_t loop_sec, int64_t metric_regid, bool is_meta);

  ~ThreadPool();

  template <class F>
  void AddTask(F&& func) {
    AddDelayTask(std::forward<F>(func), 0);
  }
  // Specifies a particular thread to run the task. `dispatch_id` will be mod
  // agasint total thread number.
  template <class F>
  void AddTask(F&& func, uint32_t dispatch_id) {
    AddDelayTask(std::forward<F>(func), 0, dispatch_id);
  }

  template <class F>
  void AddDelayTask(F&& func, int64_t delay_ms) {
    uint32_t id = dispatch_id_.fetch_add(1, std::memory_order_relaxed);
    AddDelayTask(std::forward<F>(func), delay_ms, id);
  }

  template <class F>
  void AddDelayTask(F&& func, int64_t delay_ms, uint32_t dispatch_id) {
    DoAddTask(std::forward<F>(func), delay_ms, dispatch_id);
  }

  uint64_t GetPendingTaskNum() const;

  void Terminate();

 private:
  struct DelayTask {
    mutable Task task_;
    int64_t wake_ms_;
    // if dispatch_id_ < 0, select a random thread to run this task.
    int32_t dispatch_id_;

    // DelayTask(const Task& task, int64_t wake_ms, int32_t id)
    //     : task_(task), wake_ms_(wake_ms), dispatch_id_(id) {}

    DelayTask(Task&& task, int64_t wake_ms, int32_t id)
        : task_(std::move(task)), wake_ms_(wake_ms), dispatch_id_(id) {}
  };

  struct DelayTaskComp {
    bool operator()(const DelayTask& l, const DelayTask& r) const noexcept {
      return l.wake_ms_ > r.wake_ms_;
    }
  };

  struct CFS_CACHELINE_ALIGNED ThreadContext {
    uint32_t loop_sec_{0};
    std::list<Task> pending_tasks_;
    std::atomic<uint64_t> num_pending_tasks_{0};
    std::mutex mtx_;
    std::condition_variable cv_;

    // Get pending tasks in blocking way, i.e. if there is no pending tasks,
    // this function will wait.
    std::list<Task> GetPendingTasks();
    // Get pending tasks in unblocking way, i.e. if there is no pending tasks,
    // this function will return an empty list immediately.
    std::list<Task> TryGetPendingTasks();
    template <class F>
    void PushTask(F&& func);
  };

  // void DoAddTask(const Task& func, int64_t delay_ms, uint32_t dispatch_id);
  void DoAddTask(Task&& func, int64_t delay_ms, uint32_t dispatch_id);

  template <class F>
  void AddTaskInternal(F&& func, int64_t delay_ms, uint32_t dispatch_id);

  void WorkRoutine(ThreadContext* thread);
  // Return true if there are flying requests after last loop
  bool DoOneLoop();

  void DelayWorkRoutine();

 private:
  std::atomic<bool> stopped_{false};

  uint32_t num_threads_;
  std::vector<std::unique_ptr<ThreadContext>> thread_ctxs_;
  std::vector<std::thread> threads_;
  // For each thread in this thread-pool, when there is no tasks to run, it
  // will run the function 'looper' again and again until it returns 'false'.
  std::function<bool()> looper_;
  std::function<void()> initer_;

  std::thread delay_worker_;
  std::priority_queue<DelayTask, std::vector<DelayTask>, DelayTaskComp>
      delay_tasks_;
  std::mutex delay_mtx_;
  std::condition_variable delay_cv_;
  std::atomic<uint32_t> dispatch_id_{0};

  // metric registry_id of the first-connect-fs.
  //
  // DataExecutor is shared by all fs. We use the first fs's registry_id
  // to report metrics in thread_pool for simplicity.
  int64_t metric_regid_;

  // whether this thread_pool is a data-threadpool or meta-threadpool.
  // Different metrics are sent for them.
  bool is_meta_{false};
};

}  // namespace internal
}  // namespace cfs
