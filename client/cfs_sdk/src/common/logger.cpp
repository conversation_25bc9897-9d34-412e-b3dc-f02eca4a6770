#include "common/logger.h"

#include <byterpc/log_setting.h>
#include <gflags/gflags.h>
#include <spdlog/sinks/rotating_file_sink.h>
#include <spdlog/sinks/stdout_color_sinks.h>
#include <unistd.h>

#include <filesystem>
#include <iostream>
#include <vector>

#include "common/datetime_util.h"
#include "common/fs_util.h"
#include "common/string_util.h"

DECLARE_string(cfs_log_dir);
DECLARE_string(cfs_log_level);
DECLARE_string(cfs_log_type);
DECLARE_uint64(cfs_max_logfile_size);
DECLARE_uint32(cfs_max_logfile_num);
DECLARE_uint32(cfs_log_flush_interval_sec);
DECLARE_bool(cfs_enable_byterpc_log);

namespace cfs {
namespace internal {

using LogTypeBitmap = uint32_t;
static constexpr LogTypeBitmap kFileLogMask = 0x1;
static constexpr LogTypeBitmap kStdoutLogMask = 0x2;
static constexpr LogTypeBitmap kStderrLogMask = 0x4;

static constexpr std::string_view kLogFileName = "cfs_sdk.log";
static constexpr char kLogTypeDelimiter = ',';
static constexpr std::string_view kLogFilePathTimeFmt = "%Y%m%d-%H%M%S";

static Logger* g_logger = nullptr;

Logger* Logger::Instance() {
  static std::once_flag flag;
  std::call_once(flag, []() {
    g_logger = new Logger();
  });
  return g_logger;
}

void Logger::DestroySingleton() {
  if (g_logger != nullptr) {
    delete g_logger;
    g_logger = nullptr;
  }
}

Logger::Logger() {
  InitLogger();
}

spdlog::logger* Logger::GetRaw() const {
  return logger_.get();
}

bool Logger::ShouldLog(LogLevel::level_enum level) {
  return spdlog::should_log(level);
}

static spdlog::level::level_enum ParseLogLevel(const std::string& level) {
  if (level == "debug") {
    return spdlog::level::debug;
  } else if (level == "info") {
    return spdlog::level::info;
  } else if (level == "warn") {
    return spdlog::level::warn;
  } else if (level == "error") {
    return spdlog::level::err;
  } else {
    fmt::print("Unknown log level: {}. Use default log level: info\n", level);
    return spdlog::level::info;
  }
}

static int ToByteRpcLogLevel(spdlog::level::level_enum lvl) {
  switch (lvl) {
    case spdlog::level::debug: return -1;
    case spdlog::level::info: return 0;
    case spdlog::level::warn: return 1;
    case spdlog::level::err: return 2;
    default: return 0;
  }
}

static LogTypeBitmap ParseLogType(const std::vector<std::string>& types) {
  LogTypeBitmap bitmap = 0;
  for (const auto& type : types) {
    if (type == "file") {
      bitmap |= kFileLogMask;
    } else if (type == "stdout") {
      bitmap |= kStdoutLogMask;
    } else if (type == "stderr") {
      bitmap |= kStderrLogMask;
    } else if (type == "none") {
      bitmap = 0;
    } else {
      fmt::print("Ignore unknown log type: {}\n", type);
    }
  }
  return bitmap;
}

static std::string GetFileLogDir() {
  static std::once_flag init_log_dir_flag;
  static std::string logdir;
  std::call_once(init_log_dir_flag, [] {
    std::filesystem::path dir_path(
        FsUtil::AbsoluteCanonicalHomePath(FLAGS_cfs_log_dir));
    std::error_code err_code;
    // std::filesystem::create_directories will return;
    //   1. true when the dir did not exist and be created successfully
    //   2. false when fail to create this dir:
    //     2.1. err_code.value() will be 0 if the dir has existed
    //     2.2. err_code.value() will NOT be 0 if the dir not exist but
    //     can not be created.
    std::filesystem::create_directories(dir_path, err_code);
    err_code.clear();
    auto fst = std::filesystem::status(dir_path, err_code);
    if ((fst.type() != std::filesystem::file_type::directory) ||
        (access(dir_path.c_str(), R_OK | W_OK | X_OK) != 0)) {
      // Can not access the logdir. Use the current dir as log dir.
      auto cwd_path = std::filesystem::current_path();
      fmt::print(
          "CFS sdk can not create log_dir at `{}`. Use CWD=`{}` as log dir\n",
          dir_path.string(), cwd_path.string());
      dir_path = cwd_path / "cfs_client_log";
      std::filesystem::create_directories(dir_path, err_code);
      err_code.clear();
      fst = std::filesystem::status(dir_path, err_code);
      if ((fst.type() != std::filesystem::file_type::directory) ||
          (access(dir_path.c_str(), R_OK | W_OK | X_OK) != 0)) {
        // The current dir can not be used, report error.
        fmt::print("CFS sdk can not create log_dir at: {}\n",
                   dir_path.string());
        throw std::runtime_error("Error: Failed to create log dir");
      }
    }
    logdir = dir_path.string();
    if (logdir.back() != '/') {
      logdir.push_back('/');
    }
  });
  return logdir;
}

static std::vector<spdlog::sink_ptr> InitFileLogSinks(
    spdlog::level::level_enum level) {
  std::vector<spdlog::sink_ptr> sinks;
  std::string logdir = GetFileLogDir();

  std::string log_path = StringUtil::StrCat(
      logdir, kLogFileName, ".",
      DatetimeUtil::GetNowTimeLocalStr(kLogFilePathTimeFmt), ".", getpid());
  if (level <= spdlog::level::debug) {
    auto debug_sink = std::make_shared<spdlog::sinks::rotating_file_sink_mt>(
        log_path + ".DEBUG", FLAGS_cfs_max_logfile_size,
        FLAGS_cfs_max_logfile_num);
    debug_sink->set_level(spdlog::level::debug);
    sinks.push_back(std::move(debug_sink));
  }
  if (level <= spdlog::level::info) {
    auto info_sink = std::make_shared<spdlog::sinks::rotating_file_sink_mt>(
        log_path + ".INFO", FLAGS_cfs_max_logfile_size,
        FLAGS_cfs_max_logfile_num);
    info_sink->set_level(spdlog::level::info);
    sinks.push_back(std::move(info_sink));
  }
  if (level <= spdlog::level::warn) {
    auto warn_sink = std::make_shared<spdlog::sinks::rotating_file_sink_mt>(
        log_path + ".WARN", FLAGS_cfs_max_logfile_size,
        FLAGS_cfs_max_logfile_num);
    warn_sink->set_level(spdlog::level::warn);
    sinks.push_back(std::move(warn_sink));
  }
  if (level <= spdlog::level::err) {
    auto error_sink = std::make_shared<spdlog::sinks::rotating_file_sink_mt>(
        log_path + ".ERROR", FLAGS_cfs_max_logfile_size,
        FLAGS_cfs_max_logfile_num);
    error_sink->set_level(spdlog::level::err);
    sinks.push_back(std::move(error_sink));
  }
  return sinks;
}

void Logger::InitLogger() {
  std::vector<spdlog::sink_ptr> sinks;

  auto log_level = ParseLogLevel(FLAGS_cfs_log_level);
  LogTypeBitmap log_types =
      ParseLogType(StringUtil::StrSplit(FLAGS_cfs_log_type, kLogTypeDelimiter));
  // Init log sinks
  if (log_types & kFileLogMask) {
    sinks = InitFileLogSinks(log_level);
  }
  if (log_types & kStdoutLogMask) {
    auto stdout_sink = std::make_shared<spdlog::sinks::stdout_color_sink_mt>();
    stdout_sink->set_level(log_level);
    sinks.push_back(std::move(stdout_sink));
  }
  if (log_types & kStderrLogMask) {
    auto stderr_sink = std::make_shared<spdlog::sinks::stderr_color_sink_mt>();
    stderr_sink->set_level(log_level);
    sinks.push_back(std::move(stderr_sink));
  }

  // TODO(dbc) add an option to decide use sync-logger or async-logger
  logger_ = std::make_shared<spdlog::logger>("multi_sink", sinks.begin(),
                                             sinks.end());
  logger_->set_level(log_level);
  logger_->set_pattern("%L%Y-%m-%d %T.%f %s:%# p%P-t%t] %v");
  // Flush whenever warnings or more severe messages are logged.
  logger_->flush_on(spdlog::level::warn);
  spdlog::register_logger(logger_);
  spdlog::flush_every(std::chrono::seconds(FLAGS_cfs_log_flush_interval_sec));

  // Init byterpc log, which uses bytelog as the log engine
  byterpc::LoggingSettings log_settings;
  log_settings.name = "byterpc";
  if (FLAGS_cfs_enable_byterpc_log) {
    if (log_types & kFileLogMask) {
      log_settings.log_dir = GetFileLogDir() + "byterpc_logs";
    } else {
      auto cwd_path = std::filesystem::current_path();
      log_settings.log_dir = (cwd_path / "byterpc_logs").string();
    }
    log_settings.min_log_level = ToByteRpcLogLevel(log_level);
  } else {
    // Leave log_settings.log_dir empty
    //
    // "3" means FATAL log in byterpc log
    log_settings.min_log_level = 3;
  }
  log_settings.write_to_default_file = false;
  int res = byterpc::InitLogging(log_settings);
  if (res != 0) {
    fmt::print("CFS sdk can not init byterpc log, log_dir={}\n",
               log_settings.log_dir);
    throw std::runtime_error("Error: Failed to init byterpc log");
  }
}

}  // namespace internal
}  // namespace cfs
