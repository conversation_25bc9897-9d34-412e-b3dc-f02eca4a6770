#pragma once

#include "common/macros.h"

namespace cfs {
namespace internal {

template <class T>
inline T GetUnaligned(const char* src) {
  struct Unaligned {
    T v;
  } CFS_PACKED;
  return reinterpret_cast<const Unaligned*>(src)->v;
}

template <class T>
inline void PutUnaligned(const T& value, char* dst) {
  struct Unaligned {
    T v;
  } CFS_PACKED;
  reinterpret_cast<Unaligned*>(dst)->v = value;
}

template <class T>
inline void PutUnaligned(const T& value, std::string* dst) {
  auto old_size = dst->size();
  dst->resize(old_size + sizeof(value));
  PutUnaligned(value, &((*dst)[old_size]));
}

}  // namespace internal
}  // namespace cfs
