#include "impl/cfs_impl.h"

#include <gflags/gflags.h>

#include <algorithm>
#include <cstdint>
#include <cstring>
#include <filesystem>
#include <future>
#include <memory>

#include "cloudfs2/cloudfs2.h"
#include "common/cfs_error.h"
#include "common/conf_loader.h"
#include "common/data_holder.h"
#include "common/logger.h"
#include "common/nic_manager.h"
#include "common/string_util.h"
#include "impl/err_ctx.h"
#include "impl/file_impl.h"
#include "impl/file_status.h"
#include "impl/write_guard.h"
#include "io/cluster_info_manager.h"
#include "io/io_executor.h"
#include "io/slow_node_manager.h"
#include "limiter/rate_limiter_factory.h"
#include "metric/cfs_metrics.h"
#include "protocol/file_info.h"
#include "protocol/located_block.h"
#include "rpc/rpc_util.h"
#include "trace/cfs_trace.h"

DECLARE_string(cfs_scheme_prefix);
DECLARE_string(cfs_filesystem_uri);
DECLARE_int32(cfs_filesystem_port);
DECLARE_bool(cfs_metric_enabled);
DECLARE_bool(cfs_read_finfo_with_location_enabled);

namespace cfs {

extern thread_local bool g_is_byterpc_inited;
extern thread_local uint32_t g_cur_thread_dispatch_id;

using FileImpl = ::cfs::internal::FileImpl;
using FileInfo = ::cfs::internal::FileInfo;
using DatetimeUtil = ::cfs::internal::DatetimeUtil;
using StringUtil = ::cfs::internal::StringUtil;
using IOExecutor = ::cfs::internal::IOExecutor;
using CfsTrace = ::cfs::internal::CfsTrace;
using CfsMetrics = ::cfs::internal::CfsMetrics;
using RpcUtil = ::cfs::internal::RpcUtil;
using WriteGuard = ::cfs::internal::WriteGuard;

static void DoConvertFileInfo(const FileInfo& in, cfs_file_info_t* out) {
  out->type = static_cast<cfs_file_type>(in.GetFileType());
  out->size = in.GetLength();
  out->atime = in.GetAtime();
  out->mtime = in.GetMtime();
  out->perm = in.GetPermission();
  out->replication = in.GetReplication();
  out->name = new char[in.GetPath().size() + 1];
  std::strncpy(out->name, in.GetPath().c_str(), in.GetPath().size() + 1);
  out->owner = new char[in.GetOwner().size() + 1];
  std::strncpy(out->owner, in.GetOwner().c_str(), in.GetOwner().size() + 1);
  out->group = new char[in.GetGroup().size() + 1];
  std::strncpy(out->group, in.GetGroup().c_str(), in.GetGroup().size() + 1);
  out->file_id = in.GetFileId();
  // For now, the enum values are same in `cfs_acc_status` and
  // `FileInfo::AccFileStatus`. So we can use static_cast here.
  out->acc_stat = static_cast<cfs_acc_status>(in.GetAccStatus());
}

static cfs_file_info_t* ConvertFileInfo(const FileInfo& in) {
  // We use an array with 1 element here because cfs_free_file_info will use
  // `delete[] info` to free file_info(s) from both cfs_get_file_info and
  // cfs_list_dir.
  auto* ret = new cfs_file_info_t[1];
  DoConvertFileInfo(in, ret);
  return ret;
}

static cfs_file_info_t* ConvertFileInfos(const std::vector<FileInfo>& in) {
  if (in.empty()) {
    return nullptr;
  }
  auto* ret = new cfs_file_info_t[in.size()];
  size_t idx = 0;
  for (const auto& f : in) {
    DoConvertFileInfo(f, &ret[idx]);
    idx++;
  }
  return ret;
}

static void ConvertFsStats(const internal::FsStats& in, cfs_fs_stats_t* out) {
  out->capacity = in.GetCapacity();
  out->used = in.GetUsed();
  // There is a bug with capacity-usage of DN-report that its
  // "DN-capacity != DN-used + DN-remaining". This is because DN disks have
  // much reserved-space can not be used (e.g. ext4-reserved,
  // program-binary-occupied). So the FsStats from NN also has this problem.
  //
  // To fix this problem, client compute remaining_bytes by "capacity - used".
  // SDKv1 and SDKv2 must use the same solution here.
  out->remaining = in.GetCapacity() - in.GetUsed();
  out->inode_capacity = in.GetInodeCapacity();
  out->inode_used = in.GetInodeUsed();
  out->inode_remaining = in.GetInodeRemaining();
  out->inode_wait_upload = in.GetInodeWaitUpload();
  out->inode_no_upload = in.GetInodeNoUpload();
  out->inode_uploading = in.GetInodeUploading();
}

static void ConvertLookupJobRsp(const LookupJobRsp& in,
                                cfs_lookup_job_response* out) {
  out->job_status = in.status;
  out->msg = new char[in.msg.size() + 1];
  std::strncpy(out->msg, in.msg.c_str(), in.msg.size() + 1);

  const std::vector<cfs_job_state>& job_states = in.job_states;
  size_t size = job_states.size();
  out->num_entries = size;

  cfs_job_state* states = new cfs_job_state[size];
  out->job_states = states;

  for (size_t i = 0; i < size; ++i) {
    states[i].done = job_states[i].done;
    states[i].create_timestamp = job_states[i].create_timestamp;
    states[i].complete_timestamp = job_states[i].complete_timestamp;
    states[i].success_tasks = job_states[i].success_tasks;
    states[i].failed_tasks = job_states[i].failed_tasks;
    states[i].canceled_tasks = job_states[i].canceled_tasks;
    states[i].timeout_tasks = job_states[i].timeout_tasks;
    states[i].throttled_tasks = job_states[i].throttled_tasks;
    states[i].total_tasks = job_states[i].total_tasks;
    states[i].job_type = static_cast<cfs_job_type>(job_states[i].job_type);
  }
}

// This function is used convert sync-API(e.g. cfs_write, cfs_writex, cfs_read,
// cfs_readx) to async-API.
static void OnAsyncReadWriteDone(cfs_status status, int64_t size, void* args) {
  auto* promise =
      static_cast<std::promise<std::tuple<cfs_status, std::string, int64_t>>*>(
          args);
  std::string err_msg;
  if (status != CFS_STATUS_OK) {
    err_msg = GetLastErrorMsg();
  }
  promise->set_value(
      std::make_tuple(std::move(status), std::move(err_msg), std::move(size)));
}

static void CfsFinalize() {
  int64_t start_us = DatetimeUtil::GetNowTimeUs();
  // Destroy all Singletons of CFS-SDK
  // We do not create these singletons with "static local_var" because users
  // may create their own Singletons and call cfs_connect/cfs_finalize in the
  // Singleton's ctor/dtor. Since the global static vars' destroy order is not
  // sure, it may cause cfs_finalize called after some global-static-vars
  // destroyed (e.g. cfs logger). And this may cause use-after-free.
  WriteGuard::DestroySingleton();

  // TODO(dbc) For now ClusterInfoManager and SlowNodeManager is init after
  // atexit(CfsFinalize). So their dtor must be called before CfsFinalize.
  //
  // Stop slow node and cluster info manager before DestroyExecutor because
  // manager may access IO ThreadPool.
  // cfs::internal::ClusterInfoManager::GetInstance()->Stop();
  // cfs::internal::SlowNodeManager::GetInstance()->Stop();

  // IO ThreadPool must be destroyed before DestroyByteRpcEnv because
  // their worker threads should release thread-local ByteRpc variables before
  // calling DestroyByteRpcEnv()
  IOExecutor::DestroyExecutor();
  // metrics must be stopped after DestroyExecutor because the threads in
  // DataExecutor send looper-metrics
  CfsMetrics::DestroySingleton();
  RpcUtil::DestroyByteRpcEnv();

  int64_t end_us = DatetimeUtil::GetNowTimeUs();
  auto trid = CfsTrace::GenTraceId(start_us, true);
  CfsTrace::AddTrace("CLI_FINALIZE", trid, "", start_us, end_us - start_us,
                     true);
  cfs::internal::CfsTrace::DestroySingleton();
  cfs::internal::ZtiManager::DestroySingleton();
  cfs::internal::NICManager::DestroySingleton();
  cfs::internal::RateLimiterFactory::DestroyRWLimiter();
  CFSLOG(INFO, "Destroy All CFS global variables done in CfsFinalize");
}

static void DestroyLoggerOnce() {
  // Logger must be destroyed at last since above steps may print logs
  cfs::internal::Logger::DestroySingleton();
}

static void RegisterExitOnce() {
  static std::once_flag flag;
  std::call_once(flag, []() {
    int res = std::atexit(CfsFinalize);
    if (res != 0) {
      CFSLOG(ERROR, "Fail to register ExitHandler! res={}, errno={}", res,
             errno);
    }
  });
}

static void RegisterDestroyLoggerOnce() {
  static std::once_flag flag;
  std::call_once(flag, []() {
    int res = std::atexit(DestroyLoggerOnce);
    if (res != 0) {
      fmt::print(stderr,
                 "Fail to register DestroyLoggerOnce! res={}, errno={}\n", res,
                 errno);
    }
  });
}

cfs_fs* cfs_connect(const cfs_conn_option_t* opts) {
  // DestroyLogger is registered first before RegisterExitOnce because we
  // should clean logger no matter LoadSdkConfOnce succeed or not.
  RegisterDestroyLoggerOnce();

  if (!internal::ConfLoader::LoadSdkConfOnce()) {
    SetLastError(CFS_STATUS_INVALID_ARGUMENT, "Invalid conf");
    return nullptr;
  }
  std::string uri = FLAGS_cfs_filesystem_uri;
  int32_t port = FLAGS_cfs_filesystem_port;
  if (opts != nullptr) {
    if (opts->namenode != nullptr) {
      uri = opts->namenode;
    }
    if (opts->port > 0) {
      port = opts->port;
    }
  }
  if (uri.empty() || port <= 0 || uri == FLAGS_cfs_scheme_prefix) {
    CFSLOG(ERROR, "Invalid arguments in cfs_connect, namenode={}, port={}", uri,
           port);
    SetLastError(CFS_STATUS_INVALID_ARGUMENT, "Invalid uri or port");
    return nullptr;
  }
  if (uri.find(FLAGS_cfs_scheme_prefix) == 0) {
    // remove leading schema (e.g. "cfs://")
    uri = uri.substr(FLAGS_cfs_scheme_prefix.size());
  }

  // InitByteRpcEnvOnce MUST be executed before RegisterExitOnce! We must
  // ensure the static-global-var is inited before we register ExitHandler
  // with std::atexit. Because we must join all io-threads before the
  // destruction of these byterpc-static-global-vars. Otherwise it may cause
  // coredump.
  // Ref: https://en.cppreference.com/w/cpp/utility/program/exit
  if (RpcUtil::InitByteRpcEnvOnce() != CFS_OK) {
    return nullptr;
  }
  RegisterExitOnce();

  auto fs_handle = std::make_unique<internal::FileSystem>(uri, port);
  auto res = fs_handle->Connect(opts);
  if (!res.IsOk()) {
    CFSLOG(ERROR, "Fail to Connect FS, namenode={}, port={}, err={}", uri, port,
           res.GetError());
    SetLastError(res.GetError());
    return nullptr;
  }

  // metric registry_id for DataExecutor
  int64_t tp_metric_regid = -1;

  if (FLAGS_cfs_metric_enabled) {
    static std::once_flag init_metric_flag;
    // Start the metric reporter for all registries/filesystems.
    std::call_once(init_metric_flag, [&uri, port]() {
      cfs::internal::CfsMetrics::GetInstance()->Start(uri, port);
    });
    // Init current filesystem's metric.
    fs_handle->InitMetrics();
    tp_metric_regid = fs_handle->GetMetricRegistryId();
  }

  // InitAllExecutorOnce must run before SlowNodeManager::Start
  IOExecutor::InitAllExecutorOnce(tp_metric_regid);
  {
    static std::once_flag init_slow_node_flag;
    // Start the slow nodee and cluster info manager for all filesystems.
    internal::FileSystem* fs = fs_handle.get();
    std::call_once(init_slow_node_flag, [fs, uri, port]() {
      cfs::internal::ClusterInfoManager::GetInstance()->Init(fs, uri, port);
      cfs::internal::ClusterInfoManager::GetInstance()->Start();
      cfs::internal::SlowNodeManager::GetInstance()->Start();
    });
  }

  cfs_fs* ret = new cfs_fs();
  ret->fs_handle = std::move(fs_handle);
  CFSLOG(INFO, "cfs_connect success: namenode={}, port={}", uri, port);
  return ret;
}

int32_t cfs_disconnect(cfs_fs* fs) {
  if (fs) {
    // No need to destroy metrics context for 'fs' here. It will be destroyed
    // in the Filesystem's dtor.
    delete fs;
  }
  return 0;
}

void CfsSetConf(const char* key, const char* value) {
  gflags::SetCommandLineOption(key, value);
}

cfs_file* CfsOpen(cfs_fs* fs, const char* path, const cfs_open_option_t* opts,
                  int64_t trace_id) {
  if (fs == nullptr || path == nullptr || path[0] == '\0' || opts == nullptr) {
    std::string p = (path == nullptr ? "<NULL>" : path);
    CFSLOG(ERROR, "Invalid arguments in cfs_open: fs={}, path={}, opts={}",
           fmt::ptr(fs), p, fmt::ptr(opts));
    SetLastError(CFS_STATUS_INVALID_ARGUMENT, "Invalid argument");
    return nullptr;
  }

  FileFlags flags(opts->flags);
  if (!flags.IsValid()) {
    CFSLOG(ERROR, "Invalid arg flags in cfs_open, path={}, flags={:#x}", path,
           opts->flags);
    SetLastError(CFS_STATUS_INVALID_ARGUMENT, "Invalid flags");
    return nullptr;
  }
  auto& fs_handle = fs->fs_handle;

  // 1. Write && Truncate, skip GetFileInfo and call Create-RPC.
  if (flags.Writable() && flags.IsCreate() && flags.IsTruncate()) {
    auto cres =
        fs_handle->CreateFile(path, flags, opts->mode, opts->create_parent,
                              opts->replication, trace_id);
    if (cres.IsOk()) {
      auto& rsp = cres.GetValue();
      CFSLOG(INFO,
             "cfs_open file success, path={}, file_id={}, flags={:#x}, len={}",
             path, rsp.finfo_.GetFileId(), opts->flags, rsp.finfo_.GetLength());
      auto fd = std::make_unique<FileImpl>(rsp.finfo_, flags, fs_handle.get(),
                                           trace_id);
      fd->SetBufAddBlock(std::move(rsp.blk_));
      return new cfs_file{.file_handle = std::move(fd)};
    }
    // When create file fail, there is no need to RecoverLease because `create`
    // always try to overwrite the file and will never meet NO_LEASE err.
    CFS_DCHECK_NE(cres.GetError().GetErrCode(), CFS_ERR_NO_LEASE);
    CFSLOG(ERROR, "Fail to create file, path={}, err={}", path,
           cres.GetError());
    SetLastError(cres.GetError());
    return nullptr;
  }

  // 2. ReadOnly
  // 3. Write && Append, GetFileInfo first
  bool need_loc = FLAGS_cfs_read_finfo_with_location_enabled &&
                  flags.Readable() && (!flags.IsTruncate());
  auto finfo_res = fs_handle->GetFileInfo(path, need_loc, true, trace_id);
  if (!finfo_res.IsOk()) {
    if ((finfo_res.GetError().err_code == CFS_ERR_FILE_NOT_FOUND) &&
        (flags.IsCreate() && flags.Writable())) {
      // Open-appendable a non-exist file, we should CREATE this file.
      auto cres2 =
          fs_handle->CreateFile(path, flags, opts->mode, opts->create_parent,
                                opts->replication, trace_id);
      if (!cres2.IsOk()) {
        CFSLOG(ERROR, "Fail to create file, path={}, err={}", path,
               cres2.GetError());
        SetLastError(cres2.GetError());
        return nullptr;
      }
      auto& rsp = cres2.GetValue();
      CFSLOG(INFO,
             "cfs_open file success, path={}, file_id={}, flags={:#x}, len={}",
             path, rsp.finfo_.GetFileId(), opts->flags, rsp.finfo_.GetLength());
      auto fd = std::make_unique<FileImpl>(rsp.finfo_, flags, fs_handle.get(),
                                           trace_id);
      fd->SetBufAddBlock(std::move(rsp.blk_));
      return new cfs_file{.file_handle = std::move(fd)};
    } else {
      CFSLOG(ERROR, "Fail to open file, path={}, err={}", path,
             finfo_res.GetError());
      SetLastError(finfo_res.GetError());
      return nullptr;
    }
  }

  // File already exists.
  auto& finfo = finfo_res.GetValue();
  if ((finfo.GetFileType() == FileInfo::FileType::IS_DIR) ||
      (finfo.GetFileType() == FileInfo::FileType::IS_UNKNOWN)) {
    CFSLOG(ERROR, "Unsupported file type to open, path={}, file_type={}", path,
           static_cast<int32_t>(finfo.GetFileType()));
    SetLastError(CFS_STATUS_INVALID_ARGUMENT, "Unsupported open file type");
    return nullptr;
  }

  // 2. Open this file in read-only-mode
  if (!flags.Writable()) {
    CFSLOG(INFO,
           "cfs_open file success, path={}, file_id={}, flags={:#x}, len={}",
           path, finfo.GetFileId(), opts->flags, finfo.GetLength());
    return new cfs_file{.file_handle = std::make_unique<FileImpl>(
                            finfo, flags, fs_handle.get(), trace_id)};
  }

  // 3. Open this file in APPEND mode
  bool need_recover = false;
  CFS_DCHECK(flags.IsAppend());
  if (!finfo.GetUCClientName().empty()) {
    if (!opts->force) {
      if (finfo.GetUCClientName() != fs_handle->GetClientName()) {
        // This file is under-construction and lease is hold by another
        // client.
        CFSLOG(WARN,
               "Fail to append file because file has been opened "
               "in write-mode by another client, path={}, other-client={}. "
               "Try force-open to RecoverLease.",
               path, finfo.GetUCClientName());
        SetLastError(CFS_STATUS_BUSY, "File lease not acquired");
      } else {
        // This file has been opened in write-mode by myself before.
        CFSLOG(WARN,
               "Fail to append file because file has been opened "
               "in write-mode by current FS, path={}. Please close the "
               "existing file_handle and retry",
               path);
        SetLastError(CFS_STATUS_DUP_WOPEN, "File dup open in write mode");
      }
      return nullptr;
    } else {
      need_recover = (finfo.GetUCClientName() != fs_handle->GetClientName());
    }
  }

  auto ares = fs_handle->AppendFile(path, trace_id, opts->force);
  if (!ares.IsOk()) {
    CFSLOG(ERROR, "Fail to append file, path={}, flags={:#x}, err={}", path,
           opts->flags, ares.GetError());
    SetLastError(ares.GetError());
    return nullptr;
  }
  if (ares.GetValue().finfo_ != nullptr) {
    // after NN >= 1.6.2, NN will return file_info in AppendResponse
    finfo = *(ares.GetValue().finfo_);
  } else {
    // to be campatible with NN < 1.6.2
    // Force-open success, re-GetFileInfo to get the latest file length
    auto finfo_res2 = fs_handle->GetFileInfo(path, need_loc, true, trace_id);
    if (CFS_UNLIKELY(!finfo_res2.IsOk())) {
      CFSLOG(ERROR, "Fail to GetFileInfo after force-append, path={}, err={}",
             path, finfo_res2.GetError());
      SetLastError(finfo_res2.GetError());
      return nullptr;
    }
    auto old_id = finfo.GetFileId();
    auto new_id = finfo_res2.GetValue().GetFileId();
    if (CFS_UNLIKELY(old_id != new_id)) {
      CFSLOG(WARN,
             "Detect file_id changed after force-open. This file was "
             "overwrited by another thread! path={}, old_fileid={}, "
             "new_fileid={}. Ignore and continue",
             path, old_id, new_id);
    }
    finfo_res = std::move(finfo_res2);
    finfo = finfo_res.GetValue();
  }

  finfo.SetUCClientName(fs_handle->GetClientName());
  auto fd1 =
      std::make_unique<FileImpl>(finfo, flags, fs_handle.get(), trace_id);
  auto& uc_lb_ptr = ares.GetValue().uc_blk_;
  if (need_recover && (uc_lb_ptr != nullptr)) {
    auto res2 = fd1->RecoverForceOpenFile(*uc_lb_ptr);
    if (res2.IsOk()) {
      CFSLOG(INFO, "Recover force-open-file success, path={}", path);
    } else {
      CFSLOG(ERROR,
             "Fail to recover force-open-file, path={}, file_id={}, err={}",
             path, finfo.GetFileId(), res2.GetError());
      SetLastError(Status(
          CFS_ERR_FAIL_RECOVER,
          "Fail to recover file, err is: " + res2.GetError().ToString()));
      return nullptr;
    }
  }
  // Append success, now we can IncWritingFileNum
  // We can NOT inc num in FileSystem::AppendFile because the recovery process
  // may fail even AppendFile success.
  fs_handle->IncWritingFileNum(ares.GetValue().namenode_);
  CFSLOG(INFO,
         "cfs_open file success, path={}, file_id={}, flags={:#x}, len={}",
         path, finfo.GetFileId(), opts->flags, finfo.GetLength());
  return new cfs_file{.file_handle = std::move(fd1)};
}

int32_t CfsClose(cfs_file* file) {
  if (file == nullptr || file->file_handle == nullptr) {
    return 0;
  }
  auto& fh = file->file_handle;
  auto res = fh->Close();
  if (res.IsOk()) {
    CFSLOG(INFO, "cfs_close success, path={}, file_id={}, len={}",
           fh->GetPath(), fh->GetFileId(), fh->GetFileLength());
    delete file;
    return 0;
  }
  CFSLOG(ERROR, "Fail to close file, path={}, file_id={}, err={}",
         fh->GetPath(), fh->GetFileId(), res.GetError());
  SetLastError(res.GetError());
  // delay RetryComplete in background
  if (RpcUtil::IsRpcErrRetriable(res.GetError().GetErrCode())) {
    // RdOnly file will never close fail, so this must be a writable-file
    CFS_DCHECK(fh->Writable());
    WriteGuard::GetInstance()->AddRetryCompleteTask(
        std::move(file->file_handle));
  }
  delete file;
  return -1;
}

static bool CheckBatchCreateParams(cfs_fs* fs,
                                   const cfs_batch_create_option_t* opts) {
  if (fs == nullptr || opts == nullptr) {
    CFSLOG(ERROR, "Invalid arguments in cfs_batch_create: fs={}, opts={}",
           fmt::ptr(fs), fmt::ptr(opts));
    return false;
  }
  if (opts->paths == nullptr || opts->part_nums == nullptr || opts->num == 0) {
    CFSLOG(ERROR,
           "Invalid arguments in cfs_batch_create: paths={}, "
           "part_nums={}, num={}",
           fmt::ptr(opts->paths), fmt::ptr(opts->part_nums), opts->num);
    return false;
  }
  for (uint32_t i = 0; i < opts->num; ++i) {
    if (opts->paths[i] == nullptr || opts->paths[i][0] == '\0') {
      CFSLOG(ERROR,
             "Invalid argument in cfs_batch_create: paths[{}] is invalid", i);
      return false;
    }
    if (opts->part_nums[i] < 1) {
      CFSLOG(
          ERROR,
          "Invalid argument in cfs_batch_create: part_nums[{}]={} is invalid",
          i, opts->part_nums[i]);
      return false;
    }
  }
  return true;
}

cfs_file*** CfsBatchCreate(cfs_fs* fs, const cfs_batch_create_option_t* opts,
                           int64_t trace_id) {
  if (!CheckBatchCreateParams(fs, opts)) {
    SetLastError(CFS_STATUS_INVALID_ARGUMENT, "Invalid argument");
    return nullptr;
  }

  std::vector<std::string> paths1;
  std::vector<cfs_write_back_policy> wb1;
  for (uint32_t i = 0; i < opts->num; ++i) {
    paths1.emplace_back(opts->paths[i]);
    // part_files should always NOT upload to ufs because it will be
    // concat-ed when cfs_batch_close.
    cfs_write_back_policy wb = CFS_WB_FALSE;

    // For single files, default wb_policy is UNSPEC
    if (opts->part_nums[i] == 1) {
      if (opts->wb_policy != nullptr) {
        wb = opts->wb_policy[i];
      } else {
        wb = CFS_WB_UNSPEC;
      }
    }
    wb1.emplace_back(wb);
    // j must start from 1
    for (uint32_t j = 1; j < opts->part_nums[i]; ++j) {
      paths1.emplace_back(StringUtil::StrCat(opts->paths[i], ".part", j));
      wb1.emplace_back(CFS_WB_FALSE);
    }
  }

  FileFlags flags(CFS_O_WRONLY | CFS_O_TRUNC | CFS_O_CREATE);
  auto create_res = fs->fs_handle->BatchCreateFile(
      paths1, wb1, opts->mode, flags, opts->replication, true, true, trace_id);
  if (!create_res.IsOk()) {
    CFSLOG(ERROR, "Fail to batch_create file, paths=[{}], err={}",
           StringUtil::StrJoin(paths1, ","), create_res.GetError());
    SetLastError(create_res.GetError());
    return nullptr;
  }
  auto& rsp_v = create_res.GetValue();
  CFS_CHECK_EQ(rsp_v.size(), paths1.size());
  for (uint32_t i = 0; i < rsp_v.size(); i++) {
    if (!rsp_v[i].is_success_) {
      // Because is_atomic is set to true in BatchCreateFile above, so if one
      // path fail, all paths should fail.
      CFSLOG(ERROR,
             "Fail to batch_create file, paths=[{}], fail_index={}, err={}",
             StringUtil::StrJoin(paths1, ","), i, rsp_v[i].fail_reason_);
      SetLastError(Status(CFS_ERR_NN_BATCH_API_FAIL, rsp_v[i].fail_reason_));
      return nullptr;
    }
  }
  CFSLOG(INFO, "cfs_batch_create file success, paths=[{}], ",
         StringUtil::StrJoin(paths1, ","));

  // Construct return arrays
  uint32_t idx = 0;
  auto* ret = new cfs_file**[opts->num];
  for (uint32_t i = 0; i < opts->num; ++i) {
    ret[i] = new cfs_file*[opts->part_nums[i]];
    cfs_write_back_policy tgt_wb = CFS_WB_UNSPEC;
    if (opts->wb_policy != nullptr) {
      tgt_wb = opts->wb_policy[i];
    }
    for (uint32_t j = 0; j < opts->part_nums[i]; ++j) {
      auto fd = std::make_unique<FileImpl>(rsp_v[idx].finfo_, flags,
                                           fs->fs_handle.get(), trace_id);
      fd->SetBufAddBlock(std::move(rsp_v[idx].blk_));
      // part_files (j>0) should never upload to UFS because they will be
      // concat-ed.
      fd->SetWbPolicy(j > 0 ? CFS_WB_FALSE : tgt_wb);
      idx++;
      ret[i][j] = new cfs_file{.file_handle = std::move(fd)};
    }
  }
  return ret;
}

static bool CheckBatchCloseParams(cfs_file*** files, const uint32_t* part_nums,
                                  uint32_t num) {
  if (files == nullptr || part_nums == nullptr || num == 0) {
    CFSLOG(ERROR,
           "Invalid arguments in cfs_batch_close: files={}, "
           "part_nums={}, num={}",
           fmt::ptr(files), fmt::ptr(part_nums), num);
    return false;
  }
  for (uint32_t i = 0; i < num; ++i) {
    if (part_nums[i] < 1) {
      CFSLOG(ERROR,
             "Invalid argument in cfs_batch_close: part_nums[{}]={} is invalid",
             i, part_nums[i]);
      return false;
    }
    if (files[i] == nullptr) {
      CFSLOG(ERROR, "Invalid argument in cfs_batch_close: files[{}] is null",
             i);
      return false;
    }
    for (uint32_t j = 0; j < part_nums[i]; j++) {
      if (files[i][j] == nullptr) {
        CFSLOG(ERROR,
               "Invalid argument in cfs_batch_close: files[{}][{}] is null", i,
               j);
        return false;
      }
    }
  }
  return true;
}

int32_t CfsBatchClose(cfs_file*** files, const uint32_t* part_nums,
                      uint32_t num) {
  int32_t ret = 0;
  // Validate input params
  if (!CheckBatchCloseParams(files, part_nums, num)) {
    SetLastError(CFS_STATUS_INVALID_ARGUMENT, "Invalid argument");
    return -1;
  }

  std::vector<const FileImpl*> single_fds;
  std::vector<const FileImpl*> concat_tgts;
  std::vector<std::vector<const FileImpl*>> concat_srcs;

  std::vector<std::future<bool>> wait_close;
  for (uint32_t i = 0; i < num; ++i) {
    auto* main_fd = files[i][0];
    auto& main_fh = main_fd->file_handle;
    if (!main_fh->IsClosed()) {
      wait_close.push_back(main_fh->PrepareClose());
    }
    if (part_nums[i] == 1) {
      // single file
      single_fds.push_back(main_fh.get());
    } else {
      // concat target
      concat_tgts.push_back(main_fh.get());
      // concat srcs
      std::vector<const FileImpl*> srcs;
      for (uint32_t j = 1; j < part_nums[i]; ++j) {
        auto* part_fd = files[i][j];
        auto& part_fh = part_fd->file_handle;
        if (!part_fh->IsClosed()) {
          wait_close.push_back(part_fh->PrepareClose());
        }
        srcs.push_back(part_fh.get());
      }
      concat_srcs.push_back(std::move(srcs));
    }
  }

  // wait PrepareClose to finish
  uint32_t fail_wait_close_num = 0;
  for (auto& fut : wait_close) {
    if (!fut.get()) {
      fail_wait_close_num++;
    }
  }
  if (fail_wait_close_num > 0) {
    CFSLOG(WARN,
           "Fail to PrepareClose some files when BatchClose, ignore this err "
           "and continue to BatchComplete. fail={}, total={}",
           fail_wait_close_num, wait_close.size());
  }
  auto* fs = files[0][0]->file_handle->GetFs();
  auto trace_id = files[0][0]->file_handle->GetTraceId();
  fs->DecWritingFileNum(wait_close.size());
  auto res = fs->BatchCompleteFile(single_fds, concat_srcs, concat_tgts, true,
                                   trace_id);
  if (!res.IsOk()) {
    CFSLOG(ERROR,
           "Fail to BatchClose files, err={}. Ignore this error and continue "
           "to destroy all fds.",
           res.GetError());
    SetLastError(res.GetError());
    ret = -1;
  } else {
    uint32_t fail_complete_num = 0;
    std::vector<std::future<bool>> wait_finalzie;
    auto& rsp = res.GetValue();
    for (uint32_t i = 0; i < rsp.single_.size(); i++) {
      if (!rsp.single_[i].is_success_) {
        fail_complete_num++;
      } else {
        wait_finalzie.push_back(single_fds[i]->PrepareFinLastBlk());
      }
    }
    for (uint32_t i = 0; i < rsp.concat_.size(); i++) {
      if (!rsp.concat_[i].is_success_) {
        fail_complete_num++;
      } else {
        wait_finalzie.push_back(concat_tgts[i]->PrepareFinLastBlk());
      }
    }
    // wait PrepareFinalize to finish
    uint32_t fail_wait_finalize_num = 0;
    for (auto& fut : wait_finalzie) {
      if (!fut.get()) {
        fail_wait_finalize_num++;
      }
    }
    if (fail_wait_finalize_num > 0) {
      CFSLOG(WARN,
             "Fail to PrepareFinalize some files when BatchClose, ignore this "
             "err and continue. fail={}, total={}",
             fail_wait_finalize_num, wait_finalzie.size());
    }

    if (fail_complete_num > 0) {
      auto msg = StringUtil::StrFormat(
          "Fail BatchComplete, req_num={}, fail_num={}, single_msg: {}, "
          "concat_msg: {}",
          num, fail_complete_num, rsp.GetSingleFailStr(),
          rsp.GetConcatFailStr());
      CFSLOG(ERROR, "{}", msg);
      SetLastError(CFS_STATUS_IO_ERROR, std::move(msg));
      ret = -1;
    } else {
      CFSDLOG(INFO, "cfs_batch_close success");
    }
  }

  for (uint32_t i = 0; i < num; ++i) {
    for (uint32_t j = 0; j < part_nums[i]; ++j) {
      delete files[i][j];
    }
    delete[] files[i];
  }
  delete[] files;
  return ret;
}

static Status DoAsyncWrite(cfs_file* file, const void* data, uint64_t size,
                           cfs_async_write_cb callback, void* args,
                           bool async) {
  int64_t start_us = DatetimeUtil::GetNowTimeUs();
  auto res1 = file->file_handle->PrepareAsyncWrite(size);
  if (!res1.IsOk()) {
    SetLastError(res1.GetError());
    if (async) {
      auto end_us = DatetimeUtil::GetNowTimeUs();
      METRIC_TIMER_ADD(file->file_handle->GetMetricRegistryId(),
                       api_summary_cfs_async_write, end_us - start_us);
      int64_t trid = file->file_handle->GetTraceId();
      CfsTrace::AddTrace(
          "CLI_API_ASYNCWRITE", trid, file->file_handle->GetPath(), start_us,
          end_us - start_us, false, size, "PrepareAsyncWrite fail");
    }
    return res1.GetError();
  }
  auto* tp = IOExecutor::GetDataExecutor();
  auto* req = res1.GetValue();
  tp->AddTask(
      [file, data, size, callback, args, req, start_us, async]() {
        auto ts1 = DatetimeUtil::GetNowTimeUs();
        METRIC_TIMER_ADD(file->file_handle->GetMetricRegistryId(),
                         wait_time_write_queue, ts1 - start_us);
        byterpc::IOBuf iobuf;
        int64_t sz_copied = iobuf.append(static_cast<const char*>(data), size);
        if (sz_copied != static_cast<int64_t>(size)) {
          CFSLOG(ERROR,
                 "Fail to copy data into byterpc IOBuf, path={}, size={}",
                 file->file_handle->GetPath(), size);
          // TODO(dbc) make all post-order requests fail, this may be more
          // convient to do in file_impl.cpp
        }
        auto ts2 = DatetimeUtil::GetNowTimeUs();
        METRIC_TIMER_ADD(file->file_handle->GetMetricRegistryId(),
                         io_summary_read_ptr, ts2 - ts1);
        auto cb = [file, callback, args, req, start_us, async,
                   size](const Result<uint64_t>& res) {
          int64_t trid = file->file_handle->GetTraceId();
          auto& tr_path = file->file_handle->GetPath();
          auto regid = file->file_handle->GetMetricRegistryId();
          auto end_us = DatetimeUtil::GetNowTimeUs();
          if (async) {
            METRIC_TIMER_ADD(regid, api_summary_cfs_async_write,
                             end_us - start_us);
          }
          if (!res.IsOk()) {
            SetLastError(res.GetError());
            if (async) {
              CfsMetrics::GetInstance()->SumPSIncWithRegister(
                  regid, "api_fail_ps1",
                  {{"method", "cfs_async_write"},
                   {"err", cfs::GetLastErrStatusStr()}},
                  false, 1);
              CfsTrace::AddTrace("CLI_API_ASYNCWRITE", trid, tr_path, start_us,
                                 end_us - start_us, false, size);
            }
            callback(Err2CfsStatus(res.GetError().GetErrCode()), 0, args);
          } else {
            if (async) {
              METRIC_SPS_INC(regid, api_thpt_ps_cfs_async_write,
                             res.GetValue());
              CfsTrace::AddTrace("CLI_API_ASYNCWRITE", trid, tr_path, start_us,
                                 end_us - start_us, true, res.GetValue());
            }
            callback(CFS_STATUS_OK, res.GetValue(), args);
          }
        };
        file->file_handle->AsyncWrite(req, iobuf, std::move(cb));
      },
      file->file_handle->GetFileId());
  return Status();
}

int64_t CfsWrite(cfs_file* file, const void* data, uint64_t size) {
  if (file == nullptr || data == nullptr) {
    CFSLOG(ERROR, "Invalid arguments in cfs_write: file={}, data={}",
           fmt::ptr(file), fmt::ptr(data));
    SetLastError(CFS_STATUS_INVALID_ARGUMENT, "Invalid argument");
    return -1;
  }
  if (size == 0) {
    CFSLOG(WARN,
           "Write 0 bytes of data to file, path={}. Return success immediately",
           file->file_handle->GetPath());
    return 0;
  }
  METRIC_GAUGE_SET(file->file_handle->GetMetricRegistryId(), io_size_write,
                   size);
  // the parameter in tuple is <return_code, err_msg, size_write>. If no error
  // occured, err_msg is an empty string.
  // TODO(dbc) After we add cpp-style API, the C-style API should invoke
  // cpp-style API, then tuple should not removed here.
  std::promise<std::tuple<cfs_status, std::string, int64_t>> write_promise;
  auto write_fut = write_promise.get_future();

  auto res = DoAsyncWrite(file, data, size, &OnAsyncReadWriteDone,
                          static_cast<void*>(&write_promise), false);
  if (!res.IsOk()) {
    CFSLOG(ERROR, "Fail to cfs_write, path={}, err={}",
           file->file_handle->GetPath(), res);
    // No need to SetLastError here because DoAsyncWrite has set the error.
    return -1;
  }
  auto write_res = write_fut.get();
  auto status = std::get<0>(write_res);
  if (status != CFS_STATUS_OK) {
    std::string err_msg = std::get<1>(std::move(write_res));
    CFSLOG(ERROR, "Fail to write file, path={}, err={}",
           file->file_handle->GetPath(), err_msg);
    SetLastError((std::get<0>(write_res)), std::move(err_msg));
    return -1;
  }
  METRIC_SPS_INC(file->file_handle->GetMetricRegistryId(),
                 api_thpt_ps_cfs_write, std::get<2>(write_res));
  return std::get<2>(write_res);
}

int32_t CfsAsyncWrite(cfs_file* file, const void* data, uint64_t size,
                      cfs_async_write_cb callback, void* args) {
  if (file == nullptr || data == nullptr || callback == nullptr) {
    CFSLOG(ERROR,
           "Invalid arguments in CfsAsyncWrite: file={}, data={}, callback={}",
           fmt::ptr(file), fmt::ptr(data), fmt::ptr(callback));
    SetLastError(CFS_STATUS_INVALID_ARGUMENT, "Invalid argument");
    return -1;
  }
  if (size == 0) {
    CFSLOG(WARN,
           "Write 0 bytes of data to file, path={}. Call callback with success "
           "status immediately",
           file->file_handle->GetPath());
    callback(CFS_STATUS_OK, 0, args);
    return 0;
  }

  METRIC_GAUGE_SET(file->file_handle->GetMetricRegistryId(),
                   io_size_async_write, size);
  auto res = DoAsyncWrite(file, data, size, callback, args, true);
  if (!res.IsOk()) {
    CFSLOG(ERROR, "Fail to CfsAsyncWrite, path={}, err={}",
           file->file_handle->GetPath(), res);
    CfsMetrics::GetInstance()->SumPSIncWithRegister(
        file->file_handle->GetMetricRegistryId(), "api_fail_ps1",
        {{"method", "cfs_async_write"}, {"err", cfs::GetLastErrStatusStr()}},
        false, 1);
    return -1;
  }
  return 0;
}

char* cfs_get_working_directory(cfs_fs* fs, char* buf, uint64_t buf_size) {
  if (fs == nullptr || buf == nullptr || buf_size == 0) {
    CFSLOG(ERROR,
           "Invalid arguments in cfs_get_working_directory: fs={}, buf={}, "
           "buf_size={}",
           fmt::ptr(fs), fmt::ptr(buf), buf_size);
    SetLastError(CFS_STATUS_INVALID_ARGUMENT, "Invalid argument");
    return nullptr;
  }
  auto cwd = fs->fs_handle->GetWorkingDirectory();
  CFS_DCHECK(!cwd.empty());
  if (cwd.size() + 1 > buf_size) {
    // buf is too small to fit cwd
    CFSLOG(ERROR, "buf is too small in cfs_get_working_directory");
    SetLastError(CFS_STATUS_INVALID_ARGUMENT, "buf is too small to fit CWD");
    return nullptr;
  }
  std::strncpy(buf, cwd.c_str(), cwd.size() + 1);
  return buf;
}

int32_t cfs_set_working_directory(cfs_fs* fs, const char* path) {
  if (fs == nullptr || path == nullptr) {
    CFSLOG(ERROR,
           "Invalid arguments in cfs_set_working_directory: fs={}, path={}",
           fmt::ptr(fs), fmt::ptr(path));
    SetLastError(CFS_STATUS_INVALID_ARGUMENT, "Invalid argument");
    return -1;
  }
  auto res = fs->fs_handle->SetWorkingDirectory(path);
  if (res.IsOk()) {
    return 0;
  } else {
    CFSLOG(ERROR, "Fail to cfs_set_working_directory, path={}, err={}", path,
           res.GetError());
    SetLastError(res.GetError());
    return -1;
  }
}

int32_t CfsMkdir(cfs_fs* fs, const char* path, mode_t mode, bool create_parent,
                 int64_t trace_id) {
  if (fs == nullptr || path == nullptr || path[0] == '\0') {
    std::string p = (path == nullptr ? "<NULL>" : path);
    CFSLOG(ERROR, "Invalid arguments in cfs_mkdir: fs={}, path={}",
           fmt::ptr(fs), p);
    SetLastError(CFS_STATUS_INVALID_ARGUMENT, "Invalid argument");
    return -1;
  }
  auto res = fs->fs_handle->MkDir(path, mode, create_parent, trace_id);
  if (!res.IsOk()) {
    SetLastError(res.GetError());
    return -1;
  }
  return 0;
}

int32_t CfsMkdirExt(cfs_fs* fs, const std::string_view& path, mode_t mode,
                    bool create_parent, const cfs_ttl_t& ttl,
                    cfs_write_back_policy wb, int64_t trace_id) {
  if (fs == nullptr) {
    CFSLOG(ERROR, "Invalid arguments in CfsMkdirExt: fs={}", fmt::ptr(fs));
    SetLastError(CFS_STATUS_INVALID_ARGUMENT, "");
    return -1;
  }
  auto res =
      fs->fs_handle->MkDirExt(path, mode, create_parent, ttl, wb, trace_id);
  if (!res.IsOk()) {
    CFSLOG(ERROR, "Fail to CfsMkdirExt, err={}", res.GetError());
    SetLastError(res.GetError());
    return -1;
  }
  return 0;
}

int64_t CfsSeek(cfs_file* file, int64_t offset, int32_t whence) {
  if ((file == nullptr) ||
      (whence != SEEK_SET && whence != SEEK_CUR && whence != SEEK_END)) {
    CFSLOG(ERROR, "Invalid arguments in cfs_seek: file={}, whence={}",
           fmt::ptr(file), whence);
    SetLastError(CFS_STATUS_INVALID_ARGUMENT, "Invalid argument");
    return -1;
  }
  return file->file_handle->Seek(offset, whence);
}

int64_t CfsTell(cfs_file* file, cfs_off_type type) {
  if (type == CFS_READ_OFF) {
    return file->file_handle->GetReadCursor();
  } else {
    CFS_DCHECK(type == CFS_WRITE_OFF);
    return file->file_handle->GetWriteCursor();
  }
}

static void OnAsyncSyncDone(cfs_status status, void* args) {
  auto* promise =
      static_cast<std::promise<std::pair<cfs_status, std::string>>*>(args);
  std::string err_msg;
  if (status != CFS_STATUS_OK) {
    err_msg = GetLastErrorMsg();
  }
  promise->set_value(std::make_pair(std::move(status), std::move(err_msg)));
}

int32_t DoAsyncSync(cfs_file* file, cfs_async_sync_cb callback, void* args,
                    bool async) {
  int64_t start_us = DatetimeUtil::GetNowTimeUs();
  if (!file->file_handle->Writable()) {
    if (async) {
      auto end_us = DatetimeUtil::GetNowTimeUs();
      int64_t trid = file->file_handle->GetTraceId();
      CfsTrace::AddTrace("CLI_API_ASYNCSYNC", trid,
                         file->file_handle->GetPath(), start_us,
                         end_us - start_us, true, 0, "Sync RD-only file");
    }
    // Read-only file does not need to fsync
    callback(CFS_STATUS_OK, args);
    return 0;
  }
  auto* tp = IOExecutor::GetDataExecutor();
  tp->AddTask(
      [file, callback, args, start_us, async]() {
        METRIC_TIMER_ADD(file->file_handle->GetMetricRegistryId(),
                         wait_time_write_queue,
                         DatetimeUtil::GetNowTimeUs() - start_us);
        file->file_handle->AsyncSync(
            [file, callback, args, start_us, async](Result<> res) {
              auto regid = file->file_handle->GetMetricRegistryId();
              int64_t trid = file->file_handle->GetTraceId();
              auto& tr_path = file->file_handle->GetPath();
              auto end_us = DatetimeUtil::GetNowTimeUs();
              if (async) {
                METRIC_TIMER_ADD(regid, api_summary_cfs_async_sync,
                                 end_us - start_us);
              }
              if (!res.IsOk()) {
                SetLastError(res.GetError());
                if (async) {
                  CfsMetrics::GetInstance()->SumPSIncWithRegister(
                      file->file_handle->GetMetricRegistryId(), "api_fail_ps1",
                      {{"method", "cfs_async_sync"},
                       {"err", cfs::GetLastErrStatusStr()}},
                      false, 1);
                  CfsTrace::AddTrace("CLI_API_ASYNCSYNC", trid, tr_path,
                                     start_us, end_us - start_us, false);
                }
                callback(Err2CfsStatus(res.GetError().GetErrCode()), args);
              } else {
                if (async) {
                  CfsTrace::AddTrace("CLI_API_ASYNCSYNC", trid, tr_path,
                                     start_us, end_us - start_us, true);
                }
                callback(CFS_STATUS_OK, args);
              }
            });
      },
      file->file_handle->GetFileId());
  return 0;
}

int32_t CfsSync(cfs_file* file) {
  if (file == nullptr) {
    CFSLOG(ERROR, "Invalid arguments in cfs_sync: file={}", fmt::ptr(file));
    SetLastError(CFS_STATUS_INVALID_ARGUMENT, "Invalid argument");
    return -1;
  }
  std::promise<std::pair<cfs_status, std::string>> sync_promise;
  auto sync_fut = sync_promise.get_future();

  int32_t res = DoAsyncSync(file, &OnAsyncSyncDone,
                            static_cast<void*>(&sync_promise), false);
  if (res < 0) {
    // No need to set last error here because DoAsyncSync has set the error.
    return res;
  }
  auto sync_res = sync_fut.get();
  if (sync_res.first != CFS_STATUS_OK) {
    CFSLOG(ERROR, "Fail to sync file, path={}, err={}",
           file->file_handle->GetPath(), sync_res.second);
    SetLastError(sync_res.first, std::move(sync_res.second));
    return -1;
  }
  return 0;
}

int32_t CfsAsyncSync(cfs_file* file, cfs_async_sync_cb callback, void* args) {
  if (file == nullptr || callback == nullptr) {
    CFSLOG(ERROR, "Invalid arguments in CfsAsyncSync: file={}, callback={}",
           fmt::ptr(file), fmt::ptr(callback));
    SetLastError(CFS_STATUS_INVALID_ARGUMENT, "Invalid argument");
    return -1;
  }
  int32_t ret = DoAsyncSync(file, callback, args, true);
  if (ret < 0) {
    CfsMetrics::GetInstance()->SumPSIncWithRegister(
        file->file_handle->GetMetricRegistryId(), "api_fail_ps1",
        {{"method", "cfs_async_sync"}, {"err", cfs::GetLastErrStatusStr()}},
        false, 1);
  }
  return ret;
}

int32_t CfsDelete(cfs_fs* fs, const char* path, bool recursive,
                  int64_t trace_id) {
  if (fs == nullptr || path == nullptr || path[0] == '\0') {
    std::string p = (path == nullptr ? "<NULL>" : path);
    CFSLOG(ERROR, "Invalid arguments in cfs_delete: fs={}, path={}",
           fmt::ptr(fs), p);
    SetLastError(CFS_STATUS_INVALID_ARGUMENT, "Invalid argument");
    return -1;
  }
  auto res = fs->fs_handle->Delete(path, recursive, trace_id);
  if (!res.IsOk()) {
    SetLastError(res.GetError());
    return -1;
  }
  return 0;
}

static bool CheckBatchParams(cfs_fs* fs, const char** paths, uint32_t num,
                             const std::string_view& api) {
  if (fs == nullptr || paths == nullptr || num == 0) {
    CFSLOG(ERROR, "Invalid argument in {}: fs={}, paths={}", api, fmt::ptr(fs),
           fmt::ptr(paths));
    return false;
  }
  for (uint32_t i = 0; i < num; ++i) {
    if (paths[i] == nullptr || paths[i][0] == '\0') {
      CFSLOG(ERROR, "Invalid argument in {}: paths[{}] is invalid", api, i);
      return false;
    }
  }
  return true;
}

int32_t CfsBatchDelete(cfs_fs* fs, const char** paths, uint32_t num, bool force,
                       int64_t trace_id) {
  if (!CheckBatchParams(fs, paths, num, "cfs_batch_delete")) {
    SetLastError(CFS_STATUS_INVALID_ARGUMENT, "");
    return -1;
  }
  std::vector<std::string> paths1;
  for (uint32_t i = 0; i < num; i++) {
    paths1.emplace_back(paths[i]);
  }
  auto res = fs->fs_handle->BatchDelete(paths1, force, trace_id);
  if (!res.IsOk()) {
    SetLastError(res.GetError());
    return -1;
  }
  return 0;
}

int32_t CfsRename(cfs_fs* fs, const char* old_path, const char* new_path,
                  bool overwrite, int64_t trace_id) {
  if (fs == nullptr || old_path == nullptr || new_path == nullptr ||
      old_path[0] == '\0' || new_path[0] == '\0') {
    std::string old_p = (old_path == nullptr ? "<NULL>" : old_path);
    std::string new_p = (new_path == nullptr ? "<NULL>" : new_path);
    CFSLOG(ERROR,
           "Invalid arguments in CfsRename: fs={}, old_path={}, new_path={}",
           fmt::ptr(fs), old_p, new_p);
    SetLastError(CFS_STATUS_INVALID_ARGUMENT, "Invalid argument");
    return -1;
  }
  auto res = fs->fs_handle->Rename(old_path, new_path, overwrite, trace_id);
  if (!res.IsOk()) {
    SetLastError(res.GetError());
    return -1;
  }
  return 0;
}

int32_t CfsUtime(cfs_fs* fs, const char* path, int64_t atime_ms,
                 int64_t mtime_ms, int64_t trace_id) {
  if (fs == nullptr || path == nullptr || path[0] == '\0') {
    std::string p = (path == nullptr ? "<NULL>" : path);
    CFSLOG(ERROR, "Invalid arguments in CfsUtime: fs={}, path={}", fmt::ptr(fs),
           p);
    SetLastError(CFS_STATUS_INVALID_ARGUMENT, "Invalid argument");
    return -1;
  }
  auto res = fs->fs_handle->Utime(path, atime_ms, mtime_ms, trace_id);
  if (!res.IsOk()) {
    SetLastError(res.GetError());
    return -1;
  }
  return 0;
}

int32_t CfsConcat(cfs_fs* fs, const char** srcs, const char* dst,
                  uint32_t num_srcs, int64_t trace_id) {
  if (fs == nullptr || srcs == nullptr || dst == nullptr || dst[0] == '\0' ||
      num_srcs <= 0) {
    std::string dst_p = (dst == nullptr ? "<NULL>" : dst);
    CFSLOG(
        ERROR,
        "Invalid arguments in cfs_concat: fs={}, srcs={}, dst={}, num_srcs={}",
        fmt::ptr(fs), fmt::ptr(srcs), dst_p, num_srcs);
    SetLastError(CFS_STATUS_INVALID_ARGUMENT, "Invalid argument");
    return -1;
  }
  std::vector<std::string_view> srcs1;
  for (uint32_t i = 0; i < num_srcs; i++) {
    const char* s = srcs[i];
    if (s == nullptr || s[0] == '\0') {
      CFSLOG(ERROR, "Invalid arguments in cfs_concat: srcs[{}] is null/empty",
             i);
      SetLastError(CFS_STATUS_INVALID_ARGUMENT, "srcs has empty string");
      return -1;
    }
    srcs1.emplace_back(s);
  }
  std::string_view dst1 = dst;
  auto res = fs->fs_handle->Concat(srcs1, dst1, trace_id);
  if (!res.IsOk()) {
    SetLastError(res.GetError());
    return -1;
  }
  return 0;
}

int32_t CfsExist(cfs_fs* fs, const char* path, int64_t trace_id) {
  if (fs == nullptr || path == nullptr || path[0] == '\0') {
    std::string p = (path == nullptr ? "<NULL>" : path);
    CFSLOG(ERROR, "Invalid arguments in cfs_exist: fs={}, path={}",
           fmt::ptr(fs), p);
    SetLastError(CFS_STATUS_INVALID_ARGUMENT, "Invalid argument");
    return -1;
  }
  auto res = fs->fs_handle->GetFileInfo(path, false, false, trace_id);
  if (!res.IsOk()) {
    if (res.GetError().err_code == CFS_ERR_FILE_NOT_FOUND) {
      return 0;
    }
    CFSLOG(ERROR, "Fail to GetFileInfo in cfs_exist, path={}, err={}", path,
           res.GetError());
    SetLastError(res.GetError());
    return -1;
  }
  return 1;
}

std::pair<cfs_file_info_t*, bool> CfsGetFileInfo(cfs_fs* fs, const char* path,
                                                 int64_t trace_id) {
  if (fs == nullptr || path == nullptr || path[0] == '\0') {
    std::string p = (path == nullptr ? "<NULL>" : path);
    CFSLOG(ERROR, "Invalid arguments in cfs_get_file_info: fs={}, path={}",
           fmt::ptr(fs), p);
    SetLastError(CFS_STATUS_INVALID_ARGUMENT, "Invalid argument");
    return {nullptr, false};
  }

  auto res = fs->fs_handle->GetFileInfo(path, false, false, trace_id);
  if (!res.IsOk()) {
    // FILE_NOT_FOUND do not need to print error log
    bool succ = true;
    if (res.GetError().err_code != CFS_ERR_FILE_NOT_FOUND) {
      CFSLOG(ERROR, "Fail to GetFileInfo, path={}, err={}", path,
             res.GetError());
      succ = false;
    }
    SetLastError(res.GetError());
    return {nullptr, succ};
  }
  auto* ptr = ConvertFileInfo(res.GetValue());
  return {ptr, true};
}

std::pair<cfs_file_info_t*, bool> CfsGetFileInfo2(cfs_file* file) {
  if (file == nullptr) {
    CFSLOG(ERROR, "Invalid arguments in CfsGetFileInfo2: file=null");
    SetLastError(CFS_STATUS_INVALID_ARGUMENT, "file is null");
    return {nullptr, false};
  }
  auto* ptr = ConvertFileInfo(file->file_handle->GetFileInfo());
  return {ptr, true};
}

cfs_file_info_t* CfsListDir(cfs_fs* fs, const char* path, int32_t* num_entries,
                            const char* start_after, bool* has_remaining,
                            int64_t trace_id) {
  if (fs == nullptr || path == nullptr || num_entries == nullptr) {
    CFSLOG(ERROR,
           "Invalid arguments in cfs_list_dir: fs={}, path={}, num_entries={}",
           fmt::ptr(fs), fmt::ptr(path), fmt::ptr(num_entries));
    SetLastError(CFS_STATUS_INVALID_ARGUMENT, "Invalid argument");
    if (num_entries != nullptr) {
      *num_entries = -1;
    }
    return nullptr;
  }
  std::string_view after;
  if (start_after != nullptr) {
    after = start_after;
  }
  if (has_remaining != nullptr) {
    // List partial dir
    auto res1 = fs->fs_handle->ListPartialDir(path, after, trace_id);
    if (!res1.IsOk()) {
      CFSLOG(ERROR, "Fail to list partial dir, err={}", res1.GetError());
      SetLastError(res1.GetError());
      *num_entries = -1;
      return nullptr;
    }
    auto& flist = res1.GetValue().first;
    *num_entries = flist.size();
    *has_remaining = res1.GetValue().second;
    return ConvertFileInfos(flist);
  } else {
    // List all entries in dir
    auto res2 = fs->fs_handle->ListAllDir(path, after, trace_id);
    if (!res2.IsOk()) {
      CFSLOG(ERROR, "Fail to list all dir, err={}", res2.GetError());
      SetLastError(res2.GetError());
      *num_entries = -1;
      return nullptr;
    }
    auto& flist = res2.GetValue();
    *num_entries = flist.size();
    return ConvertFileInfos(flist);
  }
}

int32_t CfsFreeFileInfo(cfs_fs* fs, cfs_file_info_t* info,
                        int32_t num_entries) {
  if (fs == nullptr || num_entries < 0) {
    CFSLOG(ERROR, "Invalid arguments in CfsFreeFileInfo: fs={}, num_entries={}",
           fmt::ptr(fs), num_entries);
    SetLastError(CFS_STATUS_INVALID_ARGUMENT, "Invalid argument");
    return -1;
  }
  METRIC_SCOPED_TIMER(fs->fs_handle->GetMetricRegistryId(),
                      api_summary_cfs_free_file_info);
  if (info == nullptr) {
    CFS_DCHECK_EQ(num_entries, 0);
    return 0;
  }
  for (int32_t i = 0; i < num_entries; i++) {
    cfs_file_info_t* ptr = info + i;
    delete[] ptr->name;
    delete[] ptr->owner;
    delete[] ptr->group;
  }
  delete[] info;
  return 0;
}

cfs_content_summary_t cfs_get_content_summary(cfs_fs* fs, const char* path) {
  // TODO(dbc)
  (void)fs;
  (void)path;
  CFSLOG(ERROR, "cfs_get_content_summary is not implement!");
  cfs_content_summary_t a;
  return a;
}

int32_t CfsStatfs(cfs_fs* fs, cfs_fs_stats_t* buf, int64_t trace_id) {
  if (fs == nullptr || buf == nullptr) {
    CFSLOG(ERROR, "Invalid arguments in cfs_statfs: fs={}, buf={}",
           fmt::ptr(fs), fmt::ptr(buf));
    SetLastError(CFS_STATUS_INVALID_ARGUMENT, "Invalid argument");
    return -1;
  }
  auto res = fs->fs_handle->GetFsStats(trace_id);
  if (!res.IsOk()) {
    CFSLOG(ERROR, "Fail to cfs_statfs, err={}", res.GetError());
    SetLastError(res.GetError());
    return -1;
  }
  ConvertFsStats(res.GetValue(), buf);
  return 0;
}

int32_t CfsSetWriteBackPolicy(cfs_fs* fs, const char* path,
                              cfs_write_back_policy policy, int64_t trace_id) {
  if (fs == nullptr || path == nullptr || path[0] == '\0') {
    std::string p = (path == nullptr ? "<NULL>" : path);
    CFSLOG(ERROR, "Invalid arguments in CfsSetWriteBackPolicy: fs={}, path={}",
           fmt::ptr(fs), p);
    SetLastError(CFS_STATUS_INVALID_ARGUMENT, "");
    return -1;
  }
  auto res = fs->fs_handle->SetDirPolicy(path, policy, trace_id);
  if (!res.IsOk()) {
    CFSLOG(ERROR, "Fail to CfsSetWriteBackPolicy, path={}, err={}", path,
           res.GetError());
    SetLastError(res.GetError());
    return -1;
  }
  return 0;
}

int32_t CfsGetWriteBackPolicy(cfs_fs* fs, const char* path,
                              cfs_write_back_policy* policy, int64_t trace_id) {
  if (fs == nullptr || path == nullptr || path[0] == '\0' ||
      policy == nullptr) {
    std::string p = (path == nullptr ? "<NULL>" : path);
    CFSLOG(
        ERROR,
        "Invalid arguments in CfsGetWriteBackPolicy: fs={}, path={}, policy={}",
        fmt::ptr(fs), p, fmt::ptr(policy));
    SetLastError(CFS_STATUS_INVALID_ARGUMENT, "");
    return -1;
  }
  auto res = fs->fs_handle->GetDirPolicy(path, trace_id);
  if (!res.IsOk()) {
    CFSLOG(ERROR, "Fail to CfsGetWriteBackPolicy, path={}, err={}", path,
           res.GetError());
    SetLastError(res.GetError());
    return -1;
  }
  *policy = res.GetValue().GetWbPolicy();
  return 0;
}

int32_t CfsRmWriteBackPolicy(cfs_fs* fs, const char* path, int64_t trace_id) {
  if (fs == nullptr || path == nullptr || path[0] == '\0') {
    std::string p = (path == nullptr ? "<NULL>" : path);
    CFSLOG(ERROR, "Invalid arguments in CfsGetWriteBackPolicy: fs={}, path={}",
           fmt::ptr(fs), p);
    SetLastError(CFS_STATUS_INVALID_ARGUMENT, "");
    return -1;
  }
  auto res = fs->fs_handle->RmDirPolicy(path, trace_id);
  if (!res.IsOk()) {
    CFSLOG(ERROR, "Fail to CfsRmWriteBackPolicy, path={}, err={}", path,
           res.GetError());
    SetLastError(res.GetError());
    return -1;
  }
  return 0;
}

int32_t CfsListWriteBackPolicy(cfs_fs* fs, std::vector<CfsDirPolicy>* policies,
                               int64_t trace_id) {
  if (fs == nullptr || policies == nullptr) {
    CFSLOG(ERROR,
           "Invalid arguments in CfsListWriteBackPolicy: fs={}, policies={}",
           fmt::ptr(fs), fmt::ptr(policies));
    SetLastError(CFS_STATUS_INVALID_ARGUMENT, "");
    return -1;
  }
  auto res = fs->fs_handle->ListDirPolicy(trace_id);
  if (!res.IsOk()) {
    CFSLOG(ERROR, "Fail to CfsListWriteBackPolicy, err={}", res.GetError());
    SetLastError(res.GetError());
    return -1;
  }
  for (const auto& p : res.GetValue()) {
    CfsDirPolicy v{.path_ = p.GetPath(), .wb_ = p.GetWbPolicy()};
    policies->emplace_back(std::move(v));
  }
  return 0;
}

int32_t CfsSetSyncPolicy(cfs_fs* fs, const char* path,
                         cfs_write_back_policy policy, int64_t trace_id) {
  if (fs == nullptr || path == nullptr || path[0] == '\0') {
    std::string p = (path == nullptr ? "<NULL>" : path);
    CFSLOG(ERROR, "Invalid arguments in CfsSetSyncPolicy: fs={}, path={}",
           fmt::ptr(fs), p);
    SetLastError(CFS_STATUS_INVALID_ARGUMENT, "");
    return -1;
  }
  if (policy == CFS_WB_UNSPEC) {
    return 0;
  }
  auto res = fs->fs_handle->SetSyncPolicy(path, policy, trace_id);
  if (!res.IsOk()) {
    CFSLOG(ERROR, "Fail to CfsSetSyncPolicy, path={}, err={}", path,
           res.GetError());
    SetLastError(res.GetError());
    return -1;
  }
  return 0;
}

int32_t CfsListSyncPolicy(cfs_fs* fs, std::vector<CfsDirPolicy>* policies,
                          int64_t trace_id) {
  if (fs == nullptr || policies == nullptr) {
    CFSLOG(ERROR, "Invalid arguments in CfsListSyncPolicy: fs={}, policies={}",
           fmt::ptr(fs), fmt::ptr(policies));
    SetLastError(CFS_STATUS_INVALID_ARGUMENT, "");
    return -1;
  }
  auto res = fs->fs_handle->ListSyncPolicy(trace_id);
  if (!res.IsOk()) {
    CFSLOG(ERROR, "Fail to CfsListSyncPolicy, err={}", res.GetError());
    SetLastError(res.GetError());
    return -1;
  }
  for (const auto& p : res.GetValue()) {
    CfsDirPolicy v{.path_ = p.GetPath(), .wb_ = p.GetWbPolicy()};
    policies->emplace_back(std::move(v));
  }
  return 0;
}

int32_t CfsSetTtl(cfs_fs* fs, const char* path, int64_t seconds, bool whole,
                  bool atime_based, int64_t trace_id) {
  if (fs == nullptr || path == nullptr || path[0] == '\0' || seconds < 0) {
    std::string p = (path == nullptr ? "<NULL>" : path);
    CFSLOG(
        ERROR,
        "Invalid arguments in CfsSetTtl: fs={}, path={}, seconds={}, whole={}",
        fmt::ptr(fs), p, seconds, whole);
    SetLastError(CFS_STATUS_INVALID_ARGUMENT, "");
    return -1;
  }
  auto res = fs->fs_handle->SetLifecyclePolicy(path, seconds, whole,
                                               atime_based, trace_id);
  if (!res.IsOk()) {
    CFSLOG(ERROR, "Fail to CfsSetTtl, path={}, err={}", path, res.GetError());
    SetLastError(res.GetError());
    return -1;
  }
  return 0;
}

int32_t CfsGetTtl(cfs_fs* fs, const char* path, int64_t* seconds, bool* whole,
                  bool* atime_based, int64_t trace_id) {
  if (fs == nullptr || path == nullptr || path[0] == '\0' ||
      seconds == nullptr || atime_based == nullptr) {
    std::string p = (path == nullptr ? "<NULL>" : path);
    CFSLOG(ERROR,
           "Invalid arguments in CfsSetTtl: fs={}, path={}, seconds={}, "
           "whole={}, atime_based={}",
           fmt::ptr(fs), p, fmt::ptr(seconds), fmt::ptr(whole),
           fmt::ptr(atime_based));
    SetLastError(CFS_STATUS_INVALID_ARGUMENT, "");
    return -1;
  }
  auto res = fs->fs_handle->GetLifecyclePolicy(path, trace_id);
  if (!res.IsOk()) {
    CFSLOG(ERROR, "Fail to CfsGetTtl, path={}, err={}", path, res.GetError());
    SetLastError(res.GetError());
    return -1;
  }
  if (res.GetValue().is_set_) {
    *seconds = res.GetValue().seconds_;
    *whole = res.GetValue().whole_;
    *atime_based = res.GetValue().atime_based_;
  } else {
    *seconds = -1;
    *whole = false;
    *atime_based = false;
  }
  return 0;
}

int32_t CfsRmTtl(cfs_fs* fs, const char* path, int64_t trace_id) {
  if (fs == nullptr || path == nullptr || path[0] == '\0') {
    std::string p = (path == nullptr ? "<NULL>" : path);
    CFSLOG(ERROR, "Invalid arguments in CfsRmTtl: fs={}, path={}", fmt::ptr(fs),
           p);
    SetLastError(CFS_STATUS_INVALID_ARGUMENT, "");
    return -1;
  }
  auto res = fs->fs_handle->RmLifecyclePolicy(path, trace_id);
  if (!res.IsOk()) {
    CFSLOG(ERROR, "Fail to CfsRmTtl, path={}, err={}", path, res.GetError());
    SetLastError(res.GetError());
    return -1;
  }
  return 0;
}

std::string CfsLoad(cfs_fs_handle fs, const char* path, bool recursive,
                    bool load_metadata, bool load_data, int replica_num,
                    int64_t trace_id) {
  if (fs == nullptr || path == nullptr || path[0] == '\0') {
    std::string p = (path == nullptr ? "<NULL>" : path);
    CFSLOG(ERROR, "Invalid arguments in CfsLoad: fs={}, path={}", fmt::ptr(fs),
           p);
    SetLastError(CFS_STATUS_INVALID_ARGUMENT, "");
    return nullptr;
  }

  auto res = fs->fs_handle->Load(path, recursive, load_metadata, load_data,
                                 replica_num, trace_id);
  if (!res.IsOk()) {
    CFSLOG(ERROR, "Fail to CfsLoad, path={}, err={}", path, res.GetError());
    SetLastError(res.GetError());
    return nullptr;
  }
  return res.GetValue();
}

std::string CfsFree(cfs_fs_handle fs, const char* path, bool recursive,
                    bool free_metadata, int64_t trace_id) {
  if (fs == nullptr || path == nullptr || path[0] == '\0') {
    std::string p = (path == nullptr ? "<NULL>" : path);
    CFSLOG(ERROR, "Invalid arguments in CfsFree: fs={}, path={}", fmt::ptr(fs),
           p);
    SetLastError(CFS_STATUS_INVALID_ARGUMENT, "");
    return nullptr;
  }

  auto res = fs->fs_handle->Free(path, recursive, free_metadata, trace_id);
  if (!res.IsOk()) {
    CFSLOG(ERROR, "Fail to CfsFree, path={}, err={}", path, res.GetError());
    SetLastError(res.GetError());
    return nullptr;
  }
  return res.GetValue();
}

int32_t CfsLookupJob(cfs_fs_handle fs, const char* job_id, LookupJobRsp* resp,
                     int64_t trace_id) {
  if (fs == nullptr || job_id == nullptr || job_id[0] == '\0' ||
      resp == nullptr) {
    std::string j = (job_id == nullptr ? "<NULL>" : job_id);
    CFSLOG(ERROR, "Invalid arguments in CfsLookupJob: fs={}, job_id={}",
           fmt::ptr(fs), j);
    SetLastError(CFS_STATUS_INVALID_ARGUMENT, "");
    return -1;
  }

  auto res = fs->fs_handle->LookupJob(job_id, trace_id);
  if (!res.IsOk()) {
    CFSLOG(ERROR, "Fail to lookup job, job_id={}, err={}", job_id,
           res.GetError());
    SetLastError(res.GetError());
    return -1;
  }
  *resp = res.GetValue();
  return 0;
}

int32_t CfsLookupJob(cfs_fs_handle fs, const char* job_id,
                     cfs_lookup_job_response* resp, int64_t trace_id) {
  if (fs == nullptr || job_id == nullptr || job_id[0] == '\0' ||
      resp == nullptr) {
    std::string j = (job_id == nullptr ? "<NULL>" : job_id);
    CFSLOG(ERROR, "Invalid arguments in CfsLookupJob: fs={}, job_id={}",
           fmt::ptr(fs), j);
    SetLastError(CFS_STATUS_INVALID_ARGUMENT, "");
    return -1;
  }

  auto res = fs->fs_handle->LookupJob(job_id, trace_id);
  if (!res.IsOk()) {
    CFSLOG(ERROR, "Fail to lookup job, job_id={}, err={}", job_id,
           res.GetError());
    SetLastError(res.GetError());
    return -1;
  }
  // convert
  ConvertLookupJobRsp(res.GetValue(), resp);
  return 0;
}

int32_t CfsFreeLookupResp(cfs_lookup_job_response* resp) {
  if (resp == nullptr) {
    return 0;
  }
  delete[] resp->job_states;
  delete[] resp->msg;
  return 0;
}

int32_t CfsCancelJob(cfs_fs_handle fs, const char* job_id, int64_t trace_id) {
  if (fs == nullptr || job_id == nullptr || job_id[0] == '\0') {
    std::string j = (job_id == nullptr ? "<NULL>" : job_id);
    CFSLOG(ERROR, "Invalid arguments in CfsLookupJob: fs={}, job_id={}",
           fmt::ptr(fs), j);
    SetLastError(CFS_STATUS_INVALID_ARGUMENT, "");
    return -1;
  }

  auto res = fs->fs_handle->CancelJob(job_id, trace_id);
  if (!res.IsOk()) {
    CFSLOG(ERROR, "Fail to cancel job, job_id={}, err={}", job_id,
           res.GetError());
    SetLastError(res.GetError());
    return -1;
  }
  return 0;
}

// TODO(dbc) add a hint in the argument of file-id to know which thread to run
// this task.
cfs_iobuf* cfs_create_iobuf(uint64_t size) {
  bool succ = false;
  auto* ret = new cfs_iobuf();
  if (size == 0) {
    return ret;
  }
  if (g_is_byterpc_inited) {
    // Inside byterpc looper thread
    if (ret->iobuf.resize(size) != 0) {
      succ = false;
    } else {
      ret->likely_dispatch_id = static_cast<int32_t>(g_cur_thread_dispatch_id);
      succ = true;
    }
  } else {
    std::promise<bool> prom;
    auto fut = prom.get_future();
    IOExecutor::GetDataExecutor()->AddTask([&prom, &size, &ret]() {
      // TODO(dbc) resize is not efficient because it will memset the
      // allocated space to '\0'. We should use more efficient way later.
      if (ret->iobuf.resize(size) != 0) {
        prom.set_value(false);
      } else {
        ret->likely_dispatch_id =
            static_cast<int32_t>(g_cur_thread_dispatch_id);
        prom.set_value(true);
      }
    });
    succ = fut.get();
  }
  if (!succ) {
    delete ret;
    ret = nullptr;
    std::string err_msg = "Fail to cfs_create_iobuf, byterpc mem alloc fail";
    CFSLOG(ERROR, err_msg);
    SetLastError(CFS_STATUS_IO_ERROR, std::move(err_msg));
  }
  return ret;
}

int32_t cfs_free_iobuf(cfs_iobuf* iobuf) {
  if (iobuf == nullptr) {
    return 0;
  }
  if (!iobuf->iobuf.empty()) {
    if (g_is_byterpc_inited) {
      // Inside byterpc looper thread
      iobuf->iobuf.clear();
    } else {
      std::promise<bool> prom;
      auto fut = prom.get_future();
      auto cb = [&prom, &iobuf]() {
        iobuf->iobuf.clear();
        prom.set_value(true);
      };
      if (iobuf->likely_dispatch_id > -1) {
        IOExecutor::GetDataExecutor()->AddTask(std::move(cb),
                                               iobuf->likely_dispatch_id);
      } else {
        IOExecutor::GetDataExecutor()->AddTask(std::move(cb));
      }
      fut.get();
    }
  }
  delete iobuf;
  return 0;
}

int64_t cfs_iobuf_append(cfs_iobuf* iobuf, const void* data, uint64_t size) {
  if ((iobuf == nullptr) || (data == nullptr)) {
    CFSLOG(ERROR, "Invalid argument in cfs_iobuf_append: iobuf={}, data={}",
           fmt::ptr(iobuf), fmt::ptr(data));
    SetLastError(CFS_STATUS_INVALID_ARGUMENT, "Invalid argument");
    return -1;
  }
  if (size == 0) {
    return 0;
  }

  if (g_is_byterpc_inited) {
    // Inside byterpc looper thread. This may happen when this API is called
    // inside the callback of cfs_async_xxx.
    auto res = iobuf->iobuf.append(static_cast<const char*>(data), size);
    if (res < 0) {
      std::string err_msg = "Fail to alloc memory in byterpc";
      CFSLOG(ERROR, err_msg);
      SetLastError(CFS_STATUS_IO_ERROR, std::move(err_msg));
    } else {
      iobuf->likely_dispatch_id =
          static_cast<int32_t>(g_cur_thread_dispatch_id);
    }
    return res;
  } else {
    std::promise<int64_t> prom;
    auto fut = prom.get_future();
    auto cb = [&prom, iobuf, data, size]() {
      auto res = iobuf->iobuf.append(static_cast<const char*>(data), size);
      if (res > 0) {
        iobuf->likely_dispatch_id =
            static_cast<int32_t>(g_cur_thread_dispatch_id);
      }
      prom.set_value(res);
    };
    if (iobuf->likely_dispatch_id > -1) {
      IOExecutor::GetDataExecutor()->AddTask(std::move(cb),
                                             iobuf->likely_dispatch_id);
    } else {
      IOExecutor::GetDataExecutor()->AddTask(std::move(cb));
    }
    auto ret = fut.get();
    if (ret < 0) {
      std::string err_msg = "Fail to alloc memory in byterpc";
      CFSLOG(ERROR, err_msg);
      SetLastError(CFS_STATUS_IO_ERROR, std::move(err_msg));
    }
    return ret;
  }
}

int64_t cfs_iobuf_size(const cfs_iobuf* iobuf) {
  if (iobuf == nullptr) {
    CFSLOG(ERROR, "Invalid argument in cfs_iobuf_size, iobuf == nullptr");
    SetLastError(CFS_STATUS_INVALID_ARGUMENT, "Invalid argument");
    return -1;
  }
  return iobuf->iobuf.size();
}

struct iovec* cfs_iobuf_get_iov(cfs_iobuf* iobuf, int32_t* num_entries) {
  if ((iobuf == nullptr) || (num_entries == nullptr)) {
    CFSLOG(ERROR,
           "Invalid arguments in cfs_iobuf_get_iov: iobuf={}, num_entries={}",
           fmt::ptr(iobuf), fmt::ptr(num_entries));
    *num_entries = -1;
    SetLastError(CFS_STATUS_INVALID_ARGUMENT, "Invalid argument");
    return nullptr;
  }
  size_t blk_num = iobuf->iobuf.block_num();
  if (blk_num == 0) {
    *num_entries = 0;
    return nullptr;
  }
  auto* iov = new iovec[blk_num];
  for (size_t i = 0; i < blk_num; ++i) {
    auto& blk_ref = iobuf->iobuf.block_ref_at(i);
    iov[i].iov_base = blk_ref.data();
    iov[i].iov_len = blk_ref.size();
  }
  *num_entries = blk_num;
  return iov;
}

void cfs_iobuf_free_iov(struct iovec* iov, int32_t num_entries) {
  if ((iov == nullptr) || (num_entries <= 0)) {
    CFSLOG(ERROR,
           "Invalid arguments in cfs_iobuf_free_iov: iov={}, num_entries={}",
           fmt::ptr(iov), num_entries);
    SetLastError(CFS_STATUS_INVALID_ARGUMENT, "Invalid argument");
    return;
  }
  delete[] iov;
}

Status DoAsyncWriteX(cfs_file* file, const cfs_iobuf* data,
                     cfs_async_write_cb callback, void* args, bool async) {
  int64_t start_us = DatetimeUtil::GetNowTimeUs();
  auto size = data->iobuf.size();
  auto res1 = file->file_handle->PrepareAsyncWrite(size);
  if (!res1.IsOk()) {
    SetLastError(res1.GetError());
    if (async) {
      auto end_us = DatetimeUtil::GetNowTimeUs();
      METRIC_TIMER_ADD(file->file_handle->GetMetricRegistryId(),
                       api_summary_cfs_async_writex, end_us - start_us);
      int64_t trid = file->file_handle->GetTraceId();
      CfsTrace::AddTrace(
          "CLI_API_ASYNCWRITEX", trid, file->file_handle->GetPath(), start_us,
          end_us - start_us, false, size, "PrepareAsyncWriteX fail");
    }
    return res1.GetError();
  }
  auto* tp = IOExecutor::GetDataExecutor();
  auto* req = res1.GetValue();
  tp->AddTask(
      [file, data, size, callback, args, req, start_us, async]() {
        METRIC_TIMER_ADD(file->file_handle->GetMetricRegistryId(),
                         wait_time_write_queue,
                         DatetimeUtil::GetNowTimeUs() - start_us);
        auto cb = [file, callback, args, req, start_us, size,
                   async](const Result<uint64_t>& res) {
          int64_t trid = file->file_handle->GetTraceId();
          auto& tr_path = file->file_handle->GetPath();
          auto regid = file->file_handle->GetMetricRegistryId();
          auto end_us = DatetimeUtil::GetNowTimeUs();
          if (async) {
            METRIC_TIMER_ADD(regid, api_summary_cfs_async_writex,
                             end_us - start_us);
          }
          if (!res.IsOk()) {
            SetLastError(res.GetError());
            if (async) {
              CfsMetrics::GetInstance()->SumPSIncWithRegister(
                  file->file_handle->GetMetricRegistryId(), "api_fail_ps1",
                  {{"method", "cfs_async_writex"},
                   {"err", cfs::GetLastErrStatusStr()}},
                  false, 1);
              CfsTrace::AddTrace("CLI_API_ASYNCWRITEX", trid, tr_path, start_us,
                                 end_us - start_us, false, size);
            }
            callback(Err2CfsStatus(res.GetError().GetErrCode()), 0, args);
          } else {
            if (async) {
              METRIC_SPS_INC(regid, api_thpt_ps_cfs_async_writex,
                             res.GetValue());
              CfsTrace::AddTrace("CLI_API_ASYNCWRITEX", trid, tr_path, start_us,
                                 end_us - start_us, true, res.GetValue());
            }
            callback(CFS_STATUS_OK, res.GetValue(), args);
          }
        };
        file->file_handle->AsyncWrite(req, data->iobuf, std::move(cb));
      },
      file->file_handle->GetFileId());
  return Status();
}

int64_t CfsWriteX(cfs_file* file, const cfs_iobuf* data) {
  if (file == nullptr || data == nullptr) {
    CFSLOG(ERROR, "Invalid arguments in cfs_writex: file={}, data={}",
           fmt::ptr(file), fmt::ptr(data));
    SetLastError(CFS_STATUS_INVALID_ARGUMENT, "Invalid argument");
    return -1;
  }
  if (data->iobuf.size() == 0) {
    CFSLOG(WARN,
           "Write 0 bytes of data to file, path={}. Return success immediately",
           file->file_handle->GetPath());
    return 0;
  }

  METRIC_GAUGE_SET(file->file_handle->GetMetricRegistryId(), io_size_writex,
                   data->iobuf.size());
  std::promise<std::tuple<cfs_status, std::string, int64_t>> write_promise;
  auto write_fut = write_promise.get_future();
  auto res = DoAsyncWriteX(file, data, &OnAsyncReadWriteDone,
                           static_cast<void*>(&write_promise), false);
  if (!res.IsOk()) {
    CFSLOG(ERROR, "Fail to cfs_writex, path={}, err={}",
           file->file_handle->GetPath(), res);
    // No need to SetLastError here because DoAsyncWriteX has set the error.
    return -1;
  }
  auto write_res = write_fut.get();
  auto status = std::get<0>(write_res);
  if (status != CFS_STATUS_OK) {
    std::string err_msg = std::get<1>(std::move(write_res));
    CFSLOG(ERROR, "Fail to write file, path={}, err={}",
           file->file_handle->GetPath(), err_msg);
    SetLastError((std::get<0>(write_res)), std::move(err_msg));
    return -1;
  }
  METRIC_SPS_INC(file->file_handle->GetMetricRegistryId(),
                 api_thpt_ps_cfs_writex, std::get<2>(write_res));
  return std::get<2>(write_res);
}

int32_t CfsAsyncWriteX(cfs_file* file, const cfs_iobuf* data,
                       cfs_async_write_cb callback, void* args) {
  if (file == nullptr || data == nullptr || callback == nullptr) {
    CFSLOG(ERROR,
           "Invalid arguments in CfsAsyncWriteX: file={}, data={}, callback={}",
           fmt::ptr(file), fmt::ptr(data), fmt::ptr(callback));
    SetLastError(CFS_STATUS_INVALID_ARGUMENT, "Invalid argument");
    return -1;
  }
  if (data->iobuf.size() == 0) {
    CFSLOG(WARN, "Write 0 bytes to path={}. Success immediately",
           file->file_handle->GetPath());
    callback(CFS_STATUS_OK, 0, args);
    return 0;
  }

  METRIC_GAUGE_SET(file->file_handle->GetMetricRegistryId(),
                   io_size_async_writex, data->iobuf.size());
  auto res = DoAsyncWriteX(file, data, callback, args, true);
  if (!res.IsOk()) {
    CFSLOG(ERROR, "Fail to CfsAsyncWriteX, path={}, err={}",
           file->file_handle->GetPath(), res);
    CfsMetrics::GetInstance()->SumPSIncWithRegister(
        file->file_handle->GetMetricRegistryId(), "api_fail_ps1",
        {{"method", "cfs_async_writex"}, {"err", cfs::GetLastErrStatusStr()}},
        false, 1);
    return -1;
  }
  return 0;
}

int64_t CfsRead(cfs_file* file, void* buffer, uint64_t size) {
  if (file == nullptr || buffer == nullptr) {
    CFSLOG(ERROR, "Invalid arguments in CfsRead: file={}, buffer={}",
           fmt::ptr(file), fmt::ptr(buffer));
    SetLastError(CFS_STATUS_INVALID_ARGUMENT, "Invalid argument");
    return -1;
  }
  if (size == 0) {
    CFSLOG(INFO, "CfsRead 0 bytes of data, path={}. Return success immediately",
           file->file_handle->GetPath());
    return 0;
  }
  METRIC_GAUGE_SET(file->file_handle->GetMetricRegistryId(), io_size_read,
                   size);
  auto res1 = file->file_handle->PrepareAsyncRead();
  if (!res1.IsOk()) {
    CFSLOG(ERROR, "Fail to cfs_read, path={}, err={}",
           file->file_handle->GetPath(), res1.GetError());
    SetLastError(res1.GetError());
    return -1;
  }
  std::promise<Result<uint64_t>> pro;
  auto fut = pro.get_future();
  auto* tp = IOExecutor::GetDataExecutor();
  tp->AddTask(
      [file, buffer, size, &pro]() {
        auto buf = std::make_unique<internal::PlainDataHolder>(buffer, size);
        file->file_handle->AsyncRead(std::move(buf), size,
                                     [&pro](Result<uint64_t> res) {
                                       pro.set_value(std::move(res));
                                     });
      },
      file->file_handle->GetFileId());
  auto res = fut.get();
  if (!res.IsOk()) {
    if (res.GetError().err_code == CFS_ERR_EOF) {
      // EOF is not an error for sync-API like read, readx, pread, preadx
      return 0;
    }
    CFSLOG(ERROR, "Fail to cfs_read file, path={}, err={}",
           file->file_handle->GetPath(), res.GetError());
    SetLastError(res.GetError());
    return -1;
  }
  METRIC_SPS_INC(file->file_handle->GetMetricRegistryId(), api_thpt_ps_cfs_read,
                 res.GetValue());
  return res.GetValue();
}

static Status DoAsyncPRead(cfs_file* file, void* buffer, uint64_t size,
                           int64_t offset, cfs_async_read_cb callback,
                           void* args, bool async) {
  int64_t start_us = DatetimeUtil::GetNowTimeUs();
  auto res1 = file->file_handle->PrepareAsyncRead();
  if (!res1.IsOk()) {
    SetLastError(res1.GetError());
    if (async) {
      auto end_us = DatetimeUtil::GetNowTimeUs();
      METRIC_TIMER_ADD(file->file_handle->GetMetricRegistryId(),
                       api_summary_cfs_async_pread, end_us - start_us);
      int64_t trid = file->file_handle->GetTraceId();
      CfsTrace::AddTrace(
          "CLI_API_ASYNCPREAD", trid, file->file_handle->GetPath(), start_us,
          end_us - start_us, false, size, "PrepareAsyncPRead fail");
    }
    return res1.GetError();
  }
  auto* tp = IOExecutor::GetDataExecutor();
  tp->AddTask(
      [file, buffer, size, offset, callback, args, start_us, async]() {
        METRIC_TIMER_ADD(file->file_handle->GetMetricRegistryId(),
                         wait_time_read_queue,
                         DatetimeUtil::GetNowTimeUs() - start_us);
        auto buf = std::make_unique<internal::PlainDataHolder>(buffer, size);
        file->file_handle->AsyncPRead(
            std::move(buf), size, offset,
            [file, callback, args, start_us, async,
             size](Result<uint64_t> res) {
              int64_t trid = file->file_handle->GetTraceId();
              auto& tr_path = file->file_handle->GetPath();
              auto regid = file->file_handle->GetMetricRegistryId();
              auto end_us = DatetimeUtil::GetNowTimeUs();
              if (async) {
                METRIC_TIMER_ADD(regid, api_summary_cfs_async_pread,
                                 end_us - start_us);
              }
              if (res.IsOk()) {
                if (async) {
                  METRIC_SPS_INC(regid, api_thpt_ps_cfs_async_pread,
                                 res.GetValue());
                  CfsTrace::AddTrace("CLI_API_ASYNCPREAD", trid, tr_path,
                                     start_us, end_us - start_us, true,
                                     res.GetValue());
                }
                callback(CFS_STATUS_OK, res.GetValue(), args);
              } else {
                SetLastError(res.GetError());
                if (async) {
                  CfsMetrics::GetInstance()->SumPSIncWithRegister(
                      file->file_handle->GetMetricRegistryId(), "api_fail_ps1",
                      {{"method", "cfs_async_pread"},
                       {"err", cfs::GetLastErrStatusStr()}},
                      false, 1);
                  CfsTrace::AddTrace("CLI_API_ASYNCPREAD", trid, tr_path,
                                     start_us, end_us - start_us, false, size);
                }
                callback(Err2CfsStatus(res.GetError().GetErrCode()), 0, args);
              }
            });
      },
      file->file_handle->GetFileId());
  return Status();
}

int64_t CfsPread(cfs_file* file, void* buffer, uint64_t size, int64_t offset) {
  if (file == nullptr || buffer == nullptr || offset < 0) {
    CFSLOG(ERROR,
           "Invalid arguments in cfs_pread: file={}, buffer={}, offset={}",
           fmt::ptr(file), fmt::ptr(buffer), offset);
    SetLastError(CFS_STATUS_INVALID_ARGUMENT, "Invalid argument");
    return -1;
  }
  if (size == 0) {
    CFSLOG(INFO,
           "cfs_pread 0 bytes of data from file, path={}. Return success "
           "immediately",
           file->file_handle->GetPath());
    return 0;
  }
  METRIC_GAUGE_SET(file->file_handle->GetMetricRegistryId(), io_size_pread,
                   size);
  std::promise<std::tuple<cfs_status, std::string, int64_t>> pro;
  auto fut = pro.get_future();

  auto res1 = DoAsyncPRead(file, buffer, size, offset, &OnAsyncReadWriteDone,
                           static_cast<void*>(&pro), false);
  if (!res1.IsOk()) {
    CFSLOG(ERROR, "Fail to cfs_pread, path={}, err={}",
           file->file_handle->GetPath(), res1);
    // No need to SetLastError here because ::cfs::cfs_async_write has set the
    // error.
    return -1;
  }
  auto res2 = fut.get();
  auto status = std::get<0>(res2);
  if (status != CFS_STATUS_OK) {
    if (status == CFS_STATUS_EOF) {
      // EOF is not an error for sync-API like read, readx, pread, preadx
      return 0;
    }
    std::string err_msg = std::get<1>(std::move(res2));
    CFSLOG(ERROR, "Fail to cfs_pread, path={}, err={}",
           file->file_handle->GetPath(), err_msg);
    SetLastError(status, std::move(err_msg));
    return -1;
  }
  METRIC_SPS_INC(file->file_handle->GetMetricRegistryId(),
                 api_thpt_ps_cfs_pread, std::get<2>(res2));
  return std::get<2>(res2);
}

int32_t CfsAsyncPread(cfs_file* file, void* buffer, uint64_t size,
                      int64_t offset, cfs_async_read_cb callback, void* args) {
  // TODO(dbc) cfs_async_pread and cfs_async_preadx are almost the same. We
  // should unify them as one function (e.g. use template). We can not use
  // sth like DoAsyncPread(...) because the data_holder is unique_ptr and can
  // not be put in the capture list of lambda below (ThreadPool::AddTask
  // receives std::function but not unique_function. std::function requres
  // the lambda must be copyable).
  if (file == nullptr || buffer == nullptr || callback == nullptr ||
      offset < 0) {
    CFSLOG(ERROR,
           "Invalid arguments in CfsAsyncPread: file={}, buffer={}, "
           "offset={}, callback={}",
           fmt::ptr(file), fmt::ptr(buffer), offset, fmt::ptr(callback));
    SetLastError(CFS_STATUS_INVALID_ARGUMENT, "Invalid argument");
    return -1;
  }
  if (size == 0) {
    callback(CFS_STATUS_OK, 0, args);
    return 0;
  }
  METRIC_GAUGE_SET(file->file_handle->GetMetricRegistryId(),
                   io_size_async_pread, size);
  auto res = DoAsyncPRead(file, buffer, size, offset, callback, args, true);
  if (!res.IsOk()) {
    CFSLOG(ERROR, "Fail to CfsAsyncPread, path={}, err={}",
           file->file_handle->GetPath(), res);
    CfsMetrics::GetInstance()->SumPSIncWithRegister(
        file->file_handle->GetMetricRegistryId(), "api_fail_ps1",
        {{"method", "cfs_async_pread"}, {"err", cfs::GetLastErrStatusStr()}},
        false, 1);
    return -1;
  }
  return 0;
}

int64_t CfsReadX(cfs_file* file, cfs_iobuf* iobuf, uint64_t size) {
  if (file == nullptr || iobuf == nullptr) {
    CFSLOG(ERROR, "Invalid arguments in cfs_readx: file={}, iobuf={}",
           fmt::ptr(file), fmt::ptr(iobuf));
    SetLastError(CFS_STATUS_INVALID_ARGUMENT, "Invalid argument");
    return -1;
  }
  if (size == 0) {
    CFSLOG(INFO,
           "cfs_readx 0 bytes of data from file, path={}. Return success "
           "immediately",
           file->file_handle->GetPath());
    return 0;
  }

  METRIC_GAUGE_SET(file->file_handle->GetMetricRegistryId(), io_size_readx,
                   size);
  auto res1 = file->file_handle->PrepareAsyncRead();
  if (!res1.IsOk()) {
    CFSLOG(ERROR, "Fail to cfs_readx, path={}, err={}",
           file->file_handle->GetPath(), res1.GetError());
    SetLastError(res1.GetError());
    return -1;
  }
  std::promise<Result<uint64_t>> pro;
  auto fut = pro.get_future();
  auto* tp = IOExecutor::GetDataExecutor();
  tp->AddTask(
      [file, iobuf, size, &pro]() {
        iobuf->likely_dispatch_id =
            static_cast<int32_t>(g_cur_thread_dispatch_id);
        auto buf = std::make_unique<internal::IOBufDataHolder>(&(iobuf->iobuf),
                                                               size, false);
        file->file_handle->AsyncRead(std::move(buf), size,
                                     [&pro](Result<uint64_t> res) {
                                       pro.set_value(std::move(res));
                                     });
      },
      file->file_handle->GetFileId());
  auto res = fut.get();
  if (!res.IsOk()) {
    if (res.GetError().err_code == CFS_ERR_EOF) {
      // EOF is not an error for sync-API like read, readx, pread, preadx
      return 0;
    }
    CFSLOG(ERROR, "Fail to cfs_readx file, path={}, err={}",
           file->file_handle->GetPath(), res.GetError());
    SetLastError(res.GetError());
    return -1;
  }
  METRIC_SPS_INC(file->file_handle->GetMetricRegistryId(),
                 api_thpt_ps_cfs_readx, res.GetValue());
  return res.GetValue();
}

Status DoAsyncPReadX(cfs_file* file, cfs_iobuf* iobuf, uint64_t size,
                     int64_t offset, cfs_async_read_cb callback, void* args,
                     bool async) {
  int64_t start_us = DatetimeUtil::GetNowTimeUs();
  auto res1 = file->file_handle->PrepareAsyncRead();
  if (!res1.IsOk()) {
    SetLastError(res1.GetError());
    if (async) {
      auto end_us = DatetimeUtil::GetNowTimeUs();
      METRIC_TIMER_ADD(file->file_handle->GetMetricRegistryId(),
                       api_summary_cfs_async_preadx,
                       DatetimeUtil::GetNowTimeUs() - start_us);
      int64_t trid = file->file_handle->GetTraceId();
      CfsTrace::AddTrace(
          "CLI_API_ASYNCPREADX", trid, file->file_handle->GetPath(), start_us,
          end_us - start_us, false, size, "PrepareAsyncPReadX fail");
    }
    return res1.GetError();
  }
  auto* tp = IOExecutor::GetDataExecutor();
  tp->AddTask(
      [file, iobuf, size, offset, callback, args, start_us, async]() {
        METRIC_TIMER_ADD(file->file_handle->GetMetricRegistryId(),
                         wait_time_read_queue,
                         DatetimeUtil::GetNowTimeUs() - start_us);
        iobuf->likely_dispatch_id =
            static_cast<int32_t>(g_cur_thread_dispatch_id);
        auto buf = std::make_unique<internal::IOBufDataHolder>(&(iobuf->iobuf),
                                                               size, false);
        file->file_handle->AsyncPRead(
            std::move(buf), size, offset,
            [file, callback, args, start_us, async,
             size](Result<uint64_t> res) {
              int64_t trid = file->file_handle->GetTraceId();
              auto& tr_path = file->file_handle->GetPath();
              auto regid = file->file_handle->GetMetricRegistryId();
              auto end_us = DatetimeUtil::GetNowTimeUs();
              if (async) {
                METRIC_TIMER_ADD(regid, api_summary_cfs_async_preadx,
                                 end_us - start_us);
              }
              if (res.IsOk()) {
                if (async) {
                  METRIC_SPS_INC(regid, api_thpt_ps_cfs_async_preadx,
                                 res.GetValue());
                  CfsTrace::AddTrace("CLI_API_ASYNCPREADX", trid, tr_path,
                                     start_us, end_us - start_us, true,
                                     res.GetValue());
                }
                callback(CFS_STATUS_OK, res.GetValue(), args);
              } else {
                SetLastError(res.GetError());
                if (async) {
                  CfsMetrics::GetInstance()->SumPSIncWithRegister(
                      file->file_handle->GetMetricRegistryId(), "api_fail_ps1",
                      {{"method", "cfs_async_preadx"},
                       {"err", cfs::GetLastErrStatusStr()}},
                      false, 1);
                  CfsTrace::AddTrace("CLI_API_ASYNCPREADX", trid, tr_path,
                                     start_us, end_us - start_us, false, size);
                }
                callback(Err2CfsStatus(res.GetError().GetErrCode()), 0, args);
              }
            });
      },
      file->file_handle->GetFileId());
  return Status();
}

int64_t CfsPreadX(cfs_file* file, cfs_iobuf* iobuf, uint64_t size,
                  int64_t offset) {
  if (file == nullptr || iobuf == nullptr || offset < 0) {
    CFSLOG(ERROR,
           "Invalid arguments in cfs_preadx: file={}, iobuf={}, offset={}",
           fmt::ptr(file), fmt::ptr(iobuf), offset);
    SetLastError(CFS_STATUS_INVALID_ARGUMENT, "Invalid argument");
    return -1;
  }
  if (size == 0) {
    CFSLOG(INFO,
           "cfs_preadx 0 bytes of data from file, path={}. Return success "
           "immediately",
           file->file_handle->GetPath());
    return 0;
  }
  METRIC_GAUGE_SET(file->file_handle->GetMetricRegistryId(), io_size_preadx,
                   size);
  std::promise<std::tuple<cfs_status, std::string, int64_t>> pro;
  auto fut = pro.get_future();

  auto res1 = DoAsyncPReadX(file, iobuf, size, offset, &OnAsyncReadWriteDone,
                            static_cast<void*>(&pro), false);
  if (!res1.IsOk()) {
    CFSLOG(ERROR, "Fail to cfs_preadx, path={}, err={}",
           file->file_handle->GetPath(), res1);
    return -1;
  }
  auto res2 = fut.get();
  auto status = std::get<0>(res2);
  if (status != CFS_STATUS_OK) {
    if (status == CFS_STATUS_EOF) {
      // EOF is not an error for sync-API like read, readx, pread, preadx
      return 0;
    }
    std::string err_msg = std::get<1>(std::move(res2));
    CFSLOG(ERROR, "Fail to cfs_preadx, path={}, err={}",
           file->file_handle->GetPath(), err_msg);
    SetLastError(status, std::move(err_msg));
    return -1;
  }
  METRIC_SPS_INC(file->file_handle->GetMetricRegistryId(),
                 api_thpt_ps_cfs_preadx, std::get<2>(res2));
  return std::get<2>(res2);
}

int32_t CfsAsyncPreadX(cfs_file* file, cfs_iobuf* iobuf, uint64_t size,
                       int64_t offset, cfs_async_read_cb callback, void* args) {
  if (file == nullptr || iobuf == nullptr || callback == nullptr ||
      offset < 0) {
    CFSLOG(ERROR,
           "Invalid arguments in CfsAsyncPreadX: file={}, iobuf={}, "
           "offset={}, callback={}",
           fmt::ptr(file), fmt::ptr(iobuf), offset, fmt::ptr(callback));
    SetLastError(CFS_STATUS_INVALID_ARGUMENT, "Invalid argument");
    return -1;
  }
  if (size == 0) {
    CFSLOG(INFO, "CfsAsyncPreadX size=0, path={}, return success directly",
           file->file_handle->GetPath());
    callback(CFS_STATUS_OK, 0, args);
    return 0;
  }
  METRIC_GAUGE_SET(file->file_handle->GetMetricRegistryId(),
                   io_size_async_preadx, size);
  auto res1 = DoAsyncPReadX(file, iobuf, size, offset, callback, args, true);
  if (!res1.IsOk()) {
    CFSLOG(ERROR, "Fail to cfs_async_preadx, path={}, err={}",
           file->file_handle->GetPath(), res1);
    CfsMetrics::GetInstance()->SumPSIncWithRegister(
        file->file_handle->GetMetricRegistryId(), "api_fail_ps1",
        {{"method", "cfs_async_preadx"}, {"err", cfs::GetLastErrStatusStr()}},
        false, 1);
    return -1;
  }
  return 0;
}

}  // namespace cfs
