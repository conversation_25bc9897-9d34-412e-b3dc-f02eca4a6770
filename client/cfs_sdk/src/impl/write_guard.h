#pragma once

// #include <absl/container/flat_hash_set.h>

#include <condition_variable>
#include <list>
#include <mutex>
#include <thread>
#include <unordered_set>

#include "impl/file_impl.h"
#include "impl/filesystem.h"

namespace cfs {
namespace internal {

class WriteGuard {
 public:
  static WriteGuard* GetInstance();
  static void DestroySingleton();

  // Destroy the old global singleton of WriteGuard and re-create a new one.
  // This is only used for testing!
  static void TEST_Reset();

  ~WriteGuard();

  void AddFileSystem(const FileSystem* fs);

  void RemoveFileSystem(const FileSystem* fs);

  void AddWritingFile(FileImpl* file);

  void RemoveWritingFile(FileImpl* file);

  void AddRetryCompleteTask(std::unique_ptr<FileImpl> fd);

  void RmRetryCompleteTask(const FileSystem* fs);

  void Stop();

  // For test only!
  void TEST_RunPingBlock();

 private:
  WriteGuard();
  WriteGuard(const WriteGuard& other) = delete;
  WriteGuard(WriteGuard&& other) = delete;

  void Init();

  void DoRenewLease();

  void DoRetryComplete();

  void SubmitPingBlock();

 private:
  // Use absl::flat_hash_set here because its performace is better than
  // std::unordered_set. Especially for k/v with small size.
  // absl::flat_hash_set<const FileSystem*> fs_set_;

  // No need to use absl::flat_hash_set since this set is not accessed so
  // frequently. And absl::flat_hash_set can not be used with ASAN.
  std::unordered_set<const FileSystem*> fs_set_;
  // We use two mutex here for better performance. When lease_thread_
  // DoRenewLease, it first copies all existing FileSystem* to a temporary
  // vector. Then update_fs_mtx_ is released so that new incoming FileSystem*
  // can  push into fs_set_. But rm_fs_mtx_ must be held to keep the
  // FileSystem* in the vector valid. When calling RemoveFileSystem, the caller
  // must also acquire rm_fs_mtx_.
  std::mutex update_fs_mtx_;
  std::mutex rm_fs_mtx_;
  // This thread do RenewLease periodically.
  std::thread lease_thread_;
  std::mutex stop_lease_mtx_;
  std::condition_variable stop_lease_cv_;

  std::list<std::unique_ptr<FileImpl>> retry_complete_;
  std::mutex complete_mtx_;

  // absl::flat_hash_set<FileImpl*> file_set_;
  // TODO(dbc) Use concurrent hashset to get rid of mutex lock each time. This
  // is useful when there are many concurrent create-close of small files.
  std::unordered_set<FileImpl*> file_set_;
  std::mutex file_mtx_;
  // This thread do PingBlock periodically.
  std::thread pingblk_thread_;
  std::mutex stop_pingblk_mtx_;
  std::condition_variable stop_pingblk_cv_;

  std::atomic<bool> stopped_{false};
};

}  // namespace internal
}  // namespace cfs