#pragma once

#include <cstdint>
#include <mutex>

namespace cfs {
namespace internal {

// RateLimiter is a simple token-bucket based rate limiter implementation.
// It can't supply smoothy rate limit compared to leaky-bucket.
//
// If a user failed to acquire permits, RateLimiter won't block but returns
// a suggested microseconds user should wait for. If the user want try again,
// he should call <PERSON><PERSON><PERSON><PERSON>() again after some time to check if it still have
// permits.
//
// The RateLimiter doesn't remember the time of the last request, but it
// remembers the expected time of the next request.
// Consider a RateLimiter with rate of 1 permit per second, currently completely
// unused, and an expensive acquire(100) request comes. It would be nonsensical
// to just wait for 100 seconds, and then start the actual task. Why wait
// without  doing anything? A much better approach is to allow the request
// right away (as if it was an acquire(1) request instead), and postpone
// subsequent requests as needed.
class RateLimiter {
 public:
  RateLimiter(int64_t permits_per_sec);
  ~RateLimiter() = default;

  void UpdateRate(double permits_per_sec);

  // If success, return true
  // If permits is not enough, return false and set `wait_us` to > 0
  // If the requested permits is too large, return false and set `wait_us` to -1
  bool <PERSON>(int64_t permits, int64_t* wait_us);

 private:
  // Update unused_permits_ and next_ticket_us_ base on current time.
  void Refresh(int64_t now_us);

 private:
  int64_t max_permits_per_sec_;
  // 1ps = 10^-3 ns
  int64_t ps_per_permit_;

  // current number of permits in bucket.
  int64_t unused_permits_;

  // The time when the next request will be granted.
  // After grating a request, this time will be set future int the future.
  // A larger requests will set this time further than smaller ones.
  int64_t next_ticket_us_;

  std::mutex mtx_;
};

}  // namespace internal
}  // namespace cfs
