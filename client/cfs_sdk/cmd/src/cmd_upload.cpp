#include <fcntl.h>
#include <gflags/gflags.h>
#include <sys/mman.h>
#include <sys/uio.h>
#include <unistd.h>

#include <filesystem>

#include "cloudfs2/cloudfs2.hpp"
#include "cmd_ctx.h"
#include "common/datetime_util.h"
#include "common/defer.h"
#include "common/logger.h"

DEFINE_uint64(cfs_cmd_upload_buf_size, 1024 * 1024 * 2, "buf size for upload");

DECLARE_uint64(cfs_max_ongoing_write_size);
DECLARE_uint64(cfs_write_block_size);

namespace cfs {
namespace internal {

static struct iovec* SeekIOVecForward(struct iovec* iov, int32_t* num,
                                      uint64_t offset) {
  int32_t remain_num = *num;
  uint64_t remain_off = offset;
  uint32_t idx = 0;
  while (remain_num > 0) {
    if (iov[idx].iov_len > remain_off) {
      auto ptr = reinterpret_cast<uintptr_t>(iov[idx].iov_base) + remain_off;
      iov[idx].iov_base = reinterpret_cast<void*>(ptr);
      iov[idx].iov_len = iov[idx].iov_len - remain_off;
      *num = remain_num;
      return &iov[idx];
    }
    remain_num--;
    remain_off -= iov[idx].iov_len;
    idx++;
  }
  *num = remain_num;
  return nullptr;
}

static int64_t ReadIOBufFromLocal(int32_t local_fd, cfs_iobuf* iobuf,
                                  int64_t count, uint64_t offset) {
  int32_t num_entries = 0;
  struct iovec* iov = cfs_iobuf_get_iov(iobuf, &num_entries);
  if (CFS_UNLIKELY(iov == nullptr)) {
    CFSLOG(ERROR, "Error: empty iobuf!");
    return -1;
  }
  int64_t finish_bytes = 0;
  struct iovec* start_iov = iov;
  int32_t left_iov_cnt = num_entries;
  while (finish_bytes < count) {
    CFS_CHECK(start_iov != nullptr);
    int32_t iovcnt = std::min(left_iov_cnt, IOV_MAX);
    int64_t bytes = preadv(local_fd, start_iov, iovcnt, offset);
    if (bytes < 0) {
      // error readv
      finish_bytes = -1;
      break;
    }
    if (bytes == 0) {
      // meet EOF
      break;
    }
    finish_bytes += bytes;
    start_iov = SeekIOVecForward(start_iov, &left_iov_cnt, bytes);
  }
  cfs_iobuf_free_iov(iov, num_entries);
  return finish_bytes;
}

static cfs_iobuf* GetUploadIOBuf(uint64_t len, std::deque<cfs_iobuf*>* pool,
                                 std::mutex* mtx) {
  CFS_DCHECK(pool != nullptr);
  if (len != FLAGS_cfs_cmd_upload_buf_size) {
    return cfs_create_iobuf(len);
  }
  {
    // TODO(dbc) use concurrent queue to avoid mutex
    std::lock_guard<std::mutex> lk(*mtx);
    if (!pool->empty()) {
      auto* ret = pool->back();
      pool->pop_back();
      return ret;
    }
  }
  // cfs_create_iobuf MUST be put outside of iobuf_mtx. Otherwise it will
  // cause dead lock!
  return cfs_create_iobuf(len);
}

static void RecycleUploadIOBuf(cfs_iobuf* buf, std::deque<cfs_iobuf*>* pool,
                               std::mutex* mtx) {
  CFS_DCHECK(buf != nullptr);
  CFS_DCHECK(pool != nullptr);
  if (cfs_iobuf_size(buf) !=
      static_cast<int64_t>(FLAGS_cfs_cmd_upload_buf_size)) {
    cfs_free_iobuf(buf);
    return;
  }
  std::lock_guard<std::mutex> lk(*mtx);
  pool->push_back(buf);
}

static void ClearCachedIOBuf(const std::deque<cfs_iobuf*>& pool) {
  for (cfs_iobuf* buf : pool) {
    cfs_free_iobuf(buf);
  }
}

static void OnAsyncWriteDone(cfs_status status, int64_t size_write,
                             void* args) {
  auto* ctx = static_cast<CmdCtx::UploadCtx*>(args);
  if (status != CFS_STATUS_OK) {
    auto* err = cfs_get_last_error();
    CFSLOG(ERROR, "Fail to async_writex, err: {}", err->message);
    ctx->success->store(false);
  } else {
    CFS_CHECK_EQ(size_write, ctx->req_bytes);
  }
  if (ctx->use_iobuf_) {
    RecycleUploadIOBuf(ctx->iobuf, ctx->iobuf_pool, ctx->iobuf_mtx);
  }
  {
    // must acquire lock here because we need to update ctx->fly_req_bytes
    std::unique_lock<std::mutex> lk(*(ctx->done_mtx));
    ctx->fly_req_bytes->fetch_sub(ctx->req_bytes, std::memory_order_relaxed);
  }
  ctx->done_cv->notify_all();
  delete ctx;
}

// Return <check_res, need_to_delete_remote>
std::pair<bool, bool> CmdCtx::CheckAndUpdateRemotePath(bool force,
                                                       bool is_dir) {
  auto* finfo = cfs_get_file_info(fs_, remote_path_.c_str());
  if (finfo == nullptr) {
    auto* err = cfs_get_last_error();
    if (err->status != CFS_STATUS_FILE_NOT_FOUND) {
      fmt::print(stderr, "Fail to get remote file info, err={}\n",
                 err->message);
      return {false, false};
    }
    // file not found, no need to update remote_path_
    return {true, false};
  }
  auto ftype = finfo->type;
  cfs_free_file_info(fs_, finfo, 1);
  if (ftype == CFS_FILE_REGULAR) {
    // remote file exist, check if overwrite
    if (!force) {
      fmt::print(stderr, "Error: remote path `{}` already exist\n",
                 remote_path_);
      return {false, false};
    }
    if (is_dir) {
      // Overwrite remote regular-file with new dir
      return {true, true};
    }
    return {true, false};
  } else if (ftype == CFS_FILE_DIR) {
    auto fname = std::filesystem::path(local_path_).filename();
    auto remote_str = (std::filesystem::path(remote_path_) / fname).string();
    auto* finfo1 = cfs_get_file_info(fs_, remote_str.c_str());
    int32_t existed = -1;
    auto ftype1 = ftype;
    if (finfo1 == nullptr) {
      if (cfs_get_last_error()->status == CFS_STATUS_FILE_NOT_FOUND) {
        existed = 0;
      }
    } else {
      ftype1 = finfo1->type;
      cfs_free_file_info(fs_, finfo1, 1);
      existed = 1;
    }
    if (existed < 0) {
      fmt::print(stderr, "Fail to check FileInfo for remote path={}, err={}\n",
                 remote_str, cfs_get_last_error()->message);
      return {false, false};
    } else if (existed == 0) {
      // remote path not exist. it is safe to create a new file/dir.
      remote_path_ = remote_str;
      return {true, false};
    } else if (!force) {
      // remote path exist and not force-overwrite
      fmt::print(stderr, "Error: remote path `{}` already exist\n", remote_str);
      return {false, false};
    } else {
      // remote file/dir exist but 'force' is true
      remote_path_ = remote_str;
      if (is_dir || ftype1 == CFS_FILE_DIR) {
        return {true, true};
      } else {
        return {true, false};
      }
    }
  } else {
    fmt::print(stderr, "Error: remote path `{}` already eixst\n", remote_path_);
    return {false, false};
  }
}

int32_t CmdCtx::UploadFileOrDir(bool force, int32_t wb_policy) {
  bool is_dir;
  auto stat = std::filesystem::status(local_path_);
  if (std::filesystem::is_directory(stat)) {
    is_dir = true;
  } else if (std::filesystem::is_regular_file(stat)) {
    is_dir = false;
  } else {
    fmt::print(stderr, "Unsupported file type for put, path={}\n", local_path_);
    return -1;
  }

  if (!InitFs()) {
    return -1;
  }
  auto check_res = CheckAndUpdateRemotePath(force, is_dir);
  if (!check_res.first) {
    return -1;
  }
  if (check_res.second) {
    // Must delete exsiting-remote-path before create new file/dir
    // This happens when force-overwrite regular-file with dir or overwrite
    // remote dir with dir/file.
    int32_t del_res = cfs_delete(fs_, remote_path_.c_str(), true);
    if (del_res != 0) {
      fmt::print(
          stderr,
          "Fail to delete remote path before uploading, remote=`{}`, err={}\n",
          remote_path_, cfs_get_last_error()->message);
      return -1;
    }
  }

  cfs_write_back_policy wb = CFS_WB_UNSPEC;
  if (wb_policy == -1) {
    wb = CFS_WB_FALSE;
  } else if (wb_policy == 0) {
    wb = CFS_WB_TRUE;
  }
  if (!is_dir) {
    return UploadOneFile(remote_path_, local_path_, force, wb);
  }

  if (dir_thread_num_ > 1) {
    dir_executor_.reset(new ThreadPool(dir_thread_num_, "CfsCmdDirExe"));
  }
  int32_t res = UploadDir(remote_path_, local_path_, wb);
  // Wait for async tasks to finish
  if (dir_thread_num_ > 1) {
    std::unique_lock<std::mutex> lk(dir_flying_mtx_);
    while (dir_flying_num_.load(std::memory_order_acquire) > 0) {
      dir_flying_cv_.wait(lk);
    }
    // dir_executor_->WaitForIdle();
  }
  if (res < 0 || dir_failed_num_.load(std::memory_order_relaxed) > 0) {
    fmt::print(stderr, "Fail to upload `{}` to `{}`\n", local_path_,
               remote_path_);
    return -1;
  }
  return 0;
}

int32_t CmdCtx::UploadDir(const std::string& remote_path,
                          const std::string& local_path,
                          cfs_write_back_policy wb) {
  if (cfs_mkdir(fs_, remote_path.c_str(), 0755, false) < 0) {
    CFSLOG(ERROR, "Fail to create directory `{}`, err={}", remote_path,
           cfs_get_last_error()->message);
    return -1;
  }
  if (wb != CFS_WB_UNSPEC) {
    if (cfs_set_writeback_policy(fs_, remote_path.c_str(), wb) != 0) {
      CFSLOG(ERROR, "Fail to set_writeback_policy for `{}`, err={}",
             remote_path, cfs_get_last_error()->message);
      return -1;
    }
  }

  for (const auto& entry : std::filesystem::directory_iterator(local_path)) {
    auto local_child = entry.path();
    std::string local_child_str = local_child.string();
    auto fname = local_child.filename();
    auto remote_child = (std::filesystem::path(remote_path) / fname);
    std::string remote_child_str = remote_child.string();
    CFSDLOG(INFO, "CMD uploading child=`{}` in dir='{}'", fname.string(),
            local_path);
    if (entry.is_directory()) {
      // Because current dir already set writeback policy, its children do not
      // need to set it again.
      if (UploadDir(remote_child_str, local_child_str, CFS_WB_UNSPEC) < 0) {
        return -1;
      }
    } else if (entry.is_regular_file() || entry.is_symlink()) {
      if (dir_thread_num_ > 1) {
        dir_flying_num_.fetch_add(1, std::memory_order_relaxed);
        dir_executor_->AddTask(
            [remote_child_str, local_child_str, this]() {
              int32_t up_res = UploadOneFile(remote_child_str, local_child_str,
                                             false, CFS_WB_UNSPEC);
              if (up_res < 0) {
                CFSLOG(ERROR, "Fail to upload file={}", local_child_str);
                dir_failed_num_.fetch_add(1, std::memory_order_relaxed);
              }
              if (dir_flying_num_.fetch_sub(1, std::memory_order_relaxed) ==
                  1) {
                dir_flying_cv_.notify_all();
              }
            },
            dispatch_id_++);
      } else {
        int32_t up_res = UploadOneFile(remote_child_str, local_child_str, false,
                                       CFS_WB_UNSPEC);
        if (up_res < 0) {
          CFSLOG(ERROR, "Fail to upload child=`{}` in dir=`{}`", fname.string(),
                 local_path);
          return -1;
        }
      }
    } else {
      CFSLOG(WARN,
             "Unsupported file type when UploadDir, path={}. Ignore it and "
             "continue.",
             local_child_str);
    }
  }
  return 0;
}

static int32_t CreateEmptyRemoteFile(cfs_fs* fs, const std::string& remote_path,
                                     uint32_t replica, bool force) {
  cfs_open_option_t open_opts = {
      .flags = CFS_O_WRONLY | CFS_O_CREATE | CFS_O_TRUNC,
      .mode = 0644,
      .create_parent = true,
      .replication = replica,
      .force = force};
  cfs_file* remote_file = cfs_open(fs, remote_path.c_str(), &open_opts);
  if (remote_file == nullptr) {
    fmt::print(stderr, "Fail to open remote file for write, path={}, err={}\n",
               remote_path, cfs_get_last_error()->message);
    return -1;
  }
  if (cfs_close(remote_file) != 0) {
    CFSLOG(ERROR, "Fail to close remote file, path={}, err={}", remote_path,
           cfs_get_last_error()->message);
    return -1;
  }
  return 0;
}

int32_t CmdCtx::UploadOneFile(const std::string& remote_path,
                              const std::string& local_path, bool force,
                              cfs_write_back_policy wb) {
  auto start0 = DatetimeUtil::GetNowTimeUs();
  uint64_t local_size = std::filesystem::file_size(local_path);
  if (local_size == 0) {
    return CreateEmptyRemoteFile(fs_, remote_path, replica_num_, force);
  }
  int32_t local_fd = open(local_path.c_str(), O_RDONLY);
  if (local_fd < 0) {
    fmt::print(stderr, "Fail to open local file for read, path={}, errno={}\n",
               local_path, errno);
    return -1;
  }
  Defer defer1 = [local_fd, local_path, this]() {
    if (close(local_fd) < 0) {
      CFSLOG(WARN, "Fail to close local file, path={}, err={}", local_path,
             errno);
    }
  };

  int32_t ret;
  if (mmap_) {
    ret = UploadOneFileByMmap(remote_path, local_fd, local_size, force, wb);
  } else {
    ret = UploadOneFileByPread(remote_path, local_fd, local_size, force, wb);
  }

  if (ret == 0) {
    auto start1 = DatetimeUtil::GetNowTimeUs();
    CFSLOG(INFO, "Upload file success, path={}, Cost time: upload_time_us={}",
           remote_path, start1 - start0);
  } else {
    fmt::print(stderr, "Fail to upload, path={}\n", remote_path);
  }
  return ret;
}

int32_t CmdCtx::UploadOneFileByMmap(const std::string& remote_path,
                                    int32_t local_fd, uint64_t local_size,
                                    bool force, cfs_write_back_policy wb) {
  void* faddr = mmap(nullptr, local_size, PROT_READ, MAP_SHARED, local_fd, 0);
  if (faddr == MAP_FAILED) {
    CFSLOG(ERROR, "Fail to mmap local file for read, errno={}", errno);
    return -1;
  }
  Defer defer2 = [faddr, local_size]() {
    if (munmap(faddr, local_size) < 0) {
      CFSLOG(ERROR, "Fail to munmap local file, err={}", errno);
    }
  };

  if ((file_thread_num_ > 1) && (local_size > FLAGS_cfs_write_block_size)) {
    return SplitAndUploadFileByMmap(remote_path, faddr, local_size, wb);
  } else {
    return SimpleUploadFileByMmap(remote_path, faddr, local_size, force, wb);
  }
}

static int32_t UploadOnePartByMmap(cfs_file_handle remote_fd,
                                   const void* local_addr, uint64_t len) {
  uint64_t finish_size = 0;
  int64_t write_remote_us = 0;

  auto start1 = DatetimeUtil::GetNowTimeUs();
  while (finish_size < len) {
    uint64_t buf_len =
        std::min(len - finish_size, FLAGS_cfs_cmd_upload_buf_size);
    const void* addr = reinterpret_cast<const void*>(
        reinterpret_cast<uintptr_t>(local_addr) + finish_size);
    int64_t rc1 = cfs_write(remote_fd, addr, buf_len);
    if (rc1 < 0) {
      CFSLOG(ERROR, "Fail to write remote file, err={}",
             cfs_get_last_error()->message);
      return -1;
    }
    finish_size += rc1;
  }
  write_remote_us += (DatetimeUtil::GetNowTimeUs() - start1);
  CFSLOG(INFO, "UploadPartByMmap success. Cost time: {}us", write_remote_us);
  return 0;
}

static int32_t AsyncUploadOnePartByMmap(cfs_file_handle remote_fd,
                                        const void* local_addr,
                                        uint64_t length) {
  uint64_t finish_size = 0;
  int64_t write_remote_us = 0;
  std::atomic<bool> is_success{true};
  std::atomic<uint64_t> fly_bytes{0};
  std::mutex done_mtx;
  std::condition_variable done_cv;
  auto max_fly_bytes = FLAGS_cfs_max_ongoing_write_size;

  auto start0 = DatetimeUtil::GetNowTimeUs();
  while (finish_size < length) {
    uint64_t len =
        std::min(length - finish_size, FLAGS_cfs_cmd_upload_buf_size);
    if (fly_bytes.load(std::memory_order_acquire) >= max_fly_bytes) {
      std::unique_lock<std::mutex> lk(done_mtx);
      while (fly_bytes.load(std::memory_order_acquire) >= max_fly_bytes) {
        done_cv.wait(lk);
      }
    }
    if (!is_success.load(std::memory_order_acquire)) {
      // previous write request has failed. No need to send post-order requests
      break;
    }
    const void* addr = reinterpret_cast<const void*>(
        reinterpret_cast<uintptr_t>(local_addr) + finish_size);
    auto* arg = new CmdCtx::UploadCtx(false);
    arg->success = &is_success;
    arg->fly_req_bytes = &fly_bytes;
    arg->done_mtx = &done_mtx;
    arg->done_cv = &done_cv;
    arg->req_bytes = len;
    int32_t res = cfs_async_write(remote_fd, addr, len, &OnAsyncWriteDone, arg);
    if (res < 0) {
      CFSLOG(ERROR, "Fail to async_write remote file, err={}",
             cfs_get_last_error()->message);
      is_success.store(false);
      break;
    }
    fly_bytes.fetch_add(len, std::memory_order_release);
    finish_size += len;
  }
  // wait all fly requests to finish
  if (fly_bytes.load(std::memory_order_acquire) > 0) {
    std::unique_lock<std::mutex> lk(done_mtx);
    while (fly_bytes.load(std::memory_order_acquire) > 0) {
      done_cv.wait(lk);
    }
  }

  if (is_success.load(std::memory_order_acquire)) {
    write_remote_us += (DatetimeUtil::GetNowTimeUs() - start0);
    CFSLOG(INFO, "AsyncUploadPartByMmap success. Cost time: {}us",
           write_remote_us);
    return 0;
  } else {
    return -1;
  }
}

int32_t CmdCtx::SimpleUploadFileByMmap(const std::string& remote_path,
                                       const void* local_addr, uint64_t length,
                                       bool force, cfs_write_back_policy wb) {
  int32_t flag = CFS_O_WRONLY | CFS_O_CREATE | CFS_O_TRUNC;
  if (wb == CFS_WB_TRUE) {
    flag |= CFS_O_ACC_WRITE_BACK;
  } else if (wb == CFS_WB_FALSE) {
    flag |= CFS_O_ACC_NO_WRITE_BACK;
  }
  cfs_open_option_t open_opts = {.flags = flag,
                                 .mode = 0644,
                                 .create_parent = true,
                                 .replication = replica_num_,
                                 .force = force};
  auto remote_fd = cfs_open(fs_, remote_path.c_str(), &open_opts);
  if (remote_fd == nullptr) {
    fmt::print(stderr, "Fail to open remote file for write, path={}, err={}\n",
               remote_path, cfs_get_last_error()->message);
    return -1;
  }
  Defer defer2 = [remote_fd, remote_path, this]() {
    if (cfs_close(remote_fd) != 0) {
      CFSLOG(WARN, "Fail to close remote file, path={}, err={}", remote_path,
             cfs_get_last_error()->message);
    }
  };

  if (async_) {
    return AsyncUploadOnePartByMmap(remote_fd, local_addr, length);
  } else {
    return UploadOnePartByMmap(remote_fd, local_addr, length);
  }
}

static std::vector<uint64_t> SplitPart(uint64_t total_len, uint32_t num_hint) {
  uint32_t blk_num =
      (total_len + FLAGS_cfs_write_block_size - 1) / FLAGS_cfs_write_block_size;
  uint32_t part_num = std::min(blk_num, num_hint);
  CFS_DCHECK_GT(part_num, 1);
  std::vector<uint64_t> len_per_part(part_num, 0);
  for (uint32_t i = 0; i < blk_num; i++) {
    uint32_t idx = i % part_num;
    len_per_part[idx] += FLAGS_cfs_write_block_size;
  }
  len_per_part[part_num - 1] -=
      (FLAGS_cfs_write_block_size * blk_num - total_len);
  return len_per_part;
}

int32_t CmdCtx::SplitAndUploadFileByMmap(const std::string& remote_path,
                                         const void* local_addr,
                                         uint64_t length,
                                         cfs_write_back_policy wb) {
  auto len_per_part = SplitPart(length, file_thread_num_);
  uint32_t part_num = len_per_part.size();
  const char* path_v = remote_path.c_str();

  cfs_batch_create_option_t opt{.paths = &path_v,
                                .part_nums = &part_num,
                                .wb_policy = &wb,
                                .num = 1,
                                .mode = 0644,
                                .replication = replica_num_};
  auto* fds = cfs_batch_create(fs_, &opt);
  if (fds == nullptr) {
    fmt::print(stderr, "Fail to batch_create file, path={}, err={}\n",
               remote_path, cfs_get_last_error()->message);
    return -1;
  }

  std::vector<int32_t> part_res(part_num, 0);
  std::vector<std::thread> workers;
  workers.reserve(part_num);
  uint64_t off = 0;
  for (uint32_t i = 0; i < part_num; i++) {
    auto remote_fd = fds[0][i];
    CFS_DCHECK(remote_fd != nullptr);
    uint64_t len = len_per_part[i];
    CFS_DCHECK_GT(len, 0);
    const void* addr = reinterpret_cast<const void*>(
        reinterpret_cast<uintptr_t>(local_addr) + off);
    if (async_) {
      workers.push_back(std::thread([remote_fd, addr, len, i, &part_res]() {
        part_res[i] = AsyncUploadOnePartByMmap(remote_fd, addr, len);
      }));
    } else {
      workers.push_back(std::thread([remote_fd, addr, len, i, &part_res]() {
        part_res[i] = UploadOnePartByMmap(remote_fd, addr, len);
      }));
    }
    off += len;
  }
  for (auto& th : workers) {
    th.join();
  }

  int32_t ret = 0;
  for (uint32_t i = 0; i < part_num; i++) {
    if (part_res[i] != 0) {
      fmt::print(stderr, "Fail to upload part {} of file `{}`\n", i,
                 remote_path);
      // TODO(dbc) batch delete fds
      ret = -1;
    }
  }
  int32_t close_res = cfs_batch_close(fds, &part_num, 1);
  if (close_res != 0) {
    fmt::print(stderr, "Fail to batch_close file, path={}, err={}\n",
               remote_path, cfs_get_last_error()->message);
    ret = -1;
  }
  return ret;
}

static int32_t UploadOnePartByPread(cfs_file_handle remote_fd, int32_t local_fd,
                                    uint64_t length, int64_t offset) {
  CFSDLOG(DEBUG, "Start UploadOnePartByPread, len={}, off={}", length, offset);
  uint64_t finish_size = 0;
  int64_t read_local_us = 0;
  int64_t write_remote_us = 0;
  int64_t prepare_us = 0;

  auto start0 = DatetimeUtil::GetNowTimeUs();

  cfs_iobuf* iobuf = cfs_create_iobuf(FLAGS_cfs_cmd_upload_buf_size);
  if (iobuf == nullptr) {
    CFSLOG(ERROR, "Fail to create iobuf when write file");
    return -1;
  }
  Defer defer3 = [iobuf]() {
    cfs_free_iobuf(iobuf);
  };
  prepare_us = (DatetimeUtil::GetNowTimeUs() - start0);

  while (finish_size < length) {
    uint64_t len =
        std::min(length - finish_size, FLAGS_cfs_cmd_upload_buf_size);
    cfs_iobuf* tmp_iobuf;
    if (len < FLAGS_cfs_cmd_upload_buf_size) {
      tmp_iobuf = cfs_create_iobuf(len);
    } else {
      tmp_iobuf = iobuf;
    }
    auto start1 = DatetimeUtil::GetNowTimeUs();
    int64_t rc =
        ReadIOBufFromLocal(local_fd, tmp_iobuf, len, offset + finish_size);
    if (rc < 0) {
      CFSLOG(ERROR, "Fail to read local file, errno={}", errno);
      if (len < FLAGS_cfs_cmd_upload_buf_size) {
        cfs_free_iobuf(tmp_iobuf);
      }
      return -1;
    }
    CFS_CHECK_EQ(len, static_cast<uint64_t>(rc));
    auto start2 = DatetimeUtil::GetNowTimeUs();
    read_local_us += (start2 - start1);
    int64_t rc1 = cfs_writex(remote_fd, tmp_iobuf);
    if (len < FLAGS_cfs_cmd_upload_buf_size) {
      cfs_free_iobuf(tmp_iobuf);
    }
    if (rc1 < 0) {
      CFSLOG(ERROR, "Fail to write remote file, err={}",
             cfs_get_last_error()->message);
      return -1;
    }
    if (CFS_UNLIKELY(rc != rc1)) {
      CFSLOG(ERROR, "Write size not match! expected={}, actual={}", rc, rc1);
      return -1;
    }

    write_remote_us += (DatetimeUtil::GetNowTimeUs() - start2);
    finish_size += rc1;
  }
  CFSLOG(INFO,
         "UploadPartByPread success. Cost time: read_local_us={}, "
         "write_remote_us={}, prepare_us={}",
         read_local_us, write_remote_us, prepare_us);
  return 0;
}

static int32_t AsyncUploadOnePartByPread(cfs_file_handle remote_fd,
                                         int32_t local_fd, uint64_t length,
                                         int64_t offset) {
  uint64_t sent_size = 0;
  int64_t prepare_iobuf_us = 0;
  int64_t read_local_us = 0;
  int64_t write_remote_us = 0;
  std::atomic<bool> is_success{true};
  std::atomic<uint64_t> fly_bytes{0};
  std::mutex done_mtx;
  std::condition_variable done_cv;
  std::deque<cfs_iobuf*> iobuf_pool;
  std::mutex iobuf_mtx;
  auto max_fly_bytes = FLAGS_cfs_max_ongoing_write_size;

  while (sent_size < length) {
    uint64_t len = std::min(length - sent_size, FLAGS_cfs_cmd_upload_buf_size);
    auto start0 = DatetimeUtil::GetNowTimeUs();
    cfs_iobuf* iobuf = GetUploadIOBuf(len, &iobuf_pool, &iobuf_mtx);
    if (iobuf == nullptr) {
      CFSLOG(ERROR, "Fail to create iobuf when write file");
      is_success.store(false);
      break;
    }
    auto start1 = DatetimeUtil::GetNowTimeUs();
    prepare_iobuf_us += (start1 - start0);
    int64_t rc = ReadIOBufFromLocal(local_fd, iobuf, len, offset + sent_size);
    if (rc < 0) {
      CFSLOG(ERROR, "Fail to read local file, errno={}", errno);
      RecycleUploadIOBuf(iobuf, &iobuf_pool, &iobuf_mtx);
      is_success.store(false, std::memory_order_release);
      break;
    }
    CFS_CHECK_EQ(len, static_cast<uint64_t>(rc));
    auto start2 = DatetimeUtil::GetNowTimeUs();
    read_local_us += (start2 - start1);

    if (fly_bytes.load(std::memory_order_acquire) >= max_fly_bytes) {
      std::unique_lock<std::mutex> lk(done_mtx);
      while (fly_bytes.load(std::memory_order_acquire) >= max_fly_bytes) {
        done_cv.wait(lk);
      }
    }
    if (!is_success.load(std::memory_order_acquire)) {
      // previous write request has failed. No need to send post-order requests
      RecycleUploadIOBuf(iobuf, &iobuf_pool, &iobuf_mtx);
      break;
    }

    auto* arg = new CmdCtx::UploadCtx(true);
    arg->success = &is_success;
    arg->fly_req_bytes = &fly_bytes;
    arg->done_mtx = &done_mtx;
    arg->done_cv = &done_cv;
    arg->iobuf = iobuf;
    arg->req_bytes = len;
    arg->iobuf_pool = &iobuf_pool;
    arg->iobuf_mtx = &iobuf_mtx;
    int32_t res = cfs_async_writex(remote_fd, iobuf, &OnAsyncWriteDone, arg);
    write_remote_us += (DatetimeUtil::GetNowTimeUs() - start2);
    if (res < 0) {
      CFSLOG(ERROR, "Fail to write remote file, err={}",
             cfs_get_last_error()->message);
      RecycleUploadIOBuf(iobuf, &iobuf_pool, &iobuf_mtx);
      is_success.store(false);
      break;
    }
    fly_bytes.fetch_add(len, std::memory_order_release);
    sent_size += len;
  }
  // wait all fly requests to finish
  if (fly_bytes.load(std::memory_order_acquire) > 0) {
    auto start3 = DatetimeUtil::GetNowTimeUs();
    std::unique_lock<std::mutex> lk(done_mtx);
    while (fly_bytes.load(std::memory_order_acquire) > 0) {
      done_cv.wait(lk);
    }
    write_remote_us += (DatetimeUtil::GetNowTimeUs() - start3);
  }

  ClearCachedIOBuf(iobuf_pool);
  if (is_success.load(std::memory_order_acquire)) {
    CFSLOG(INFO,
           "AsyncUploadPartByPread success. Cost time: read_local_us={}, "
           "write_remote_us: {}, prepare_iobuf_us={}",
           read_local_us, write_remote_us, prepare_iobuf_us);
    return 0;
  } else {
    return -1;
  }
}

int32_t CmdCtx::SimpleUploadFileByPread(const std::string& remote_path,
                                        int32_t local_fd, uint64_t length,
                                        bool force, cfs_write_back_policy wb) {
  int32_t flag = CFS_O_WRONLY | CFS_O_CREATE | CFS_O_TRUNC;
  if (wb == CFS_WB_TRUE) {
    flag |= CFS_O_ACC_WRITE_BACK;
  } else if (wb == CFS_WB_FALSE) {
    flag |= CFS_O_ACC_NO_WRITE_BACK;
  }
  cfs_open_option_t open_opts = {.flags = flag,
                                 .mode = 0644,
                                 .create_parent = true,
                                 .replication = replica_num_,
                                 .force = force};
  auto remote_fd = cfs_open(fs_, remote_path.c_str(), &open_opts);
  if (remote_fd == nullptr) {
    fmt::print(stderr, "Fail to open remote file for write, path={}, err={}\n",
               remote_path, cfs_get_last_error()->message);
    return -1;
  }
  Defer defer2 = [remote_fd, remote_path, this]() {
    if (cfs_close(remote_fd) != 0) {
      CFSLOG(WARN, "Fail to close remote file, path={}, err={}", remote_path,
             cfs_get_last_error()->message);
    }
  };

  if (async_) {
    return AsyncUploadOnePartByPread(remote_fd, local_fd, length, 0);
  } else {
    return UploadOnePartByPread(remote_fd, local_fd, length, 0);
  }
}

int32_t CmdCtx::SplitAndUploadFileByPread(const std::string& remote_path,
                                          int32_t local_fd, uint64_t length,
                                          cfs_write_back_policy wb) {
  auto len_per_part = SplitPart(length, file_thread_num_);
  uint32_t part_num = len_per_part.size();
  const char* path_v = remote_path.c_str();

  cfs_batch_create_option_t opt{.paths = &path_v,
                                .part_nums = &part_num,
                                .wb_policy = &wb,
                                .num = 1,
                                .mode = 0644,
                                .replication = replica_num_};
  auto* fds = cfs_batch_create(fs_, &opt);
  if (fds == nullptr) {
    fmt::print(stderr, "Fail to batch_create file, path={}, err={}\n",
               remote_path, cfs_get_last_error()->message);
    return -1;
  }

  std::vector<int32_t> part_res(part_num, 0);
  std::vector<std::thread> workers;
  workers.reserve(part_num);
  int64_t off = 0;
  for (uint32_t i = 0; i < part_num; i++) {
    auto remote_fd = fds[0][i];
    CFS_DCHECK(remote_fd != nullptr);
    uint64_t len = len_per_part[i];
    CFS_DCHECK_GT(len, 0);
    if (async_) {
      workers.push_back(std::thread([remote_fd, local_fd, len, off, i,
                                     &part_res, this]() {
        part_res[i] = AsyncUploadOnePartByPread(remote_fd, local_fd, len, off);
      }));
    } else {
      workers.push_back(
          std::thread([remote_fd, local_fd, len, off, i, &part_res, this]() {
            part_res[i] = UploadOnePartByPread(remote_fd, local_fd, len, off);
          }));
    }
    off += static_cast<int64_t>(len);
  }
  for (auto& th : workers) {
    th.join();
  }

  int32_t ret = 0;
  for (uint32_t i = 0; i < part_num; i++) {
    if (part_res[i] != 0) {
      fmt::print(stderr, "Fail to upload part {} of file `{}`\n", i,
                 remote_path);
      // TODO(dbc) batch delete fds
      ret = -1;
    }
  }
  int32_t close_res = cfs_batch_close(fds, &part_num, 1);
  if (close_res != 0) {
    fmt::print(stderr, "Fail to batch_close file, path={}, err={}\n",
               remote_path, cfs_get_last_error()->message);
    ret = -1;
  }
  return ret;
}

int32_t CmdCtx::UploadOneFileByPread(const std::string& remote_path,
                                     int32_t local_fd, uint64_t local_size,
                                     bool force, cfs_write_back_policy wb) {
  if ((file_thread_num_ > 1) && (local_size > FLAGS_cfs_write_block_size)) {
    return SplitAndUploadFileByPread(remote_path, local_fd, local_size, wb);
  } else {
    return SimpleUploadFileByPread(remote_path, local_fd, local_size, force,
                                   wb);
  }
}

}  // namespace internal
}  // namespace cfs
