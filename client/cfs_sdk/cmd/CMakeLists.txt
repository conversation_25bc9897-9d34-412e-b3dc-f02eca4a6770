set(CMD_EXE cfs_cmd2.bin)
find_package(CLI11 REQUIRED CONFIG)

aux_source_directory(./src SRCS_CMD)
add_library(cfs_cmd_obj OBJECT ${SRCS_CMD})
target_include_directories(cfs_cmd_obj
  PRIVATE ${CMAKE_CURRENT_LIST_DIR}/src
  PRIVATE ${CFS_HOME}/include
  PRIVATE ${CFS_HOME}/src
)
# This target_link_libraries is used to pass SPDLOG_COMPILED_LIB to
# cfs_cmd_obj so that it will not use header-only version of spdlog.
target_link_libraries(cfs_cmd_obj
  spdlog::spdlog
  )

cfs_add_executable(
  NAME ${CMD_EXE}
  SOURCES main.cpp
  OBJECTS $<TARGET_OBJECTS:cfs_cmd_obj>
  LIBRARIES
    cfs_shared
    gflags::gflags_static
    CLI11::CLI11
    ${CFS_EXE_DEP_LIBS}
  )
target_include_directories(${CMD_EXE}
  PRIVATE ${CMAKE_CURRENT_LIST_DIR}/src
)
set_target_properties(${CMD_EXE} PROPERTIES INSTALL_RPATH "${CMAKE_INSTALL_PREFIX}/lib")

install(TARGETS ${CMD_EXE} RUNTIME DESTINATION bin)
install(PROGRAMS ${CMAKE_CURRENT_LIST_DIR}/bin/cfs2 DESTINATION bin)
install(FILES ${CMAKE_CURRENT_LIST_DIR}/conf/cfs_cmd_flags.conf.example DESTINATION conf)
