find_path(PROTOBUF_INCLUDE_DIR
  NAMES google/protobuf/message.h
  PATHS ${CMAKE_INCLUDE_PATH}
  NO_DEFAULT_PATH)

find_library(PROTOBUF_LIBRARY
  NAMES libprotobuf.a
  PATHS ${CMAKE_LIBRARY_PATH}
  NO_DEFAULT_PATH)

find_program(PROTOBUF_PROTOC_EXECUTABLE
  NAMES protoc
  PATHS ${CMAKE_PROGRAM_PATH}
  NO_DEFAULT_PATH)

include(FindPackageHandleStandardArgs)
find_package_handle_standard_args(
  Protobuf DEFAULT_MSG
  PROTOBUF_LIBRARY PROTOBUF_INCLUDE_DIR PROTOBUF_PROTOC_EXECUTABLE
  )

mark_as_advanced(PROTOBUF_INCLUDE_DIR PROTOBUF_LIBRARY PROTOBUF_PROTOC_EXECUTABLE)
