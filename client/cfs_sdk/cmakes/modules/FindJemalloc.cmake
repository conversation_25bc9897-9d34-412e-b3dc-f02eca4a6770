find_path(J<PERSON><PERSON>L<PERSON>_INCLUDE_DIR
  NAMES jemalloc/jemalloc.h
  PATHS ${CMAKE_INCLUDE_PATH}
  NO_DEFAULT_PATH)

if(CFS_SHARED_JEMALLOC)
  find_library(JEMALLOC_LIBRARY
    NAMES libjemalloc.so.2
    PATHS ${CMAKE_LIBRARY_PATH}
    NO_CACHE
    NO_DEFAULT_PATH)
else()
  find_library(JEMALLOC_LIBRARY
    NAMES libjemalloc_pic.a
    PATHS ${CMAKE_LIBRARY_PATH}
    NO_CACHE
    NO_DEFAULT_PATH)
endif()

include(FindPackageHandleStandardArgs)
find_package_handle_standard_args(
  Jemalloc DEFAULT_MSG
  JEMALLOC_LIBRARY JEMALLOC_INCLUDE_DIR
  )

mark_as_advanced(JEMALLOC_INCLUDE_DIR JEMALLOC_LIBRARY)
