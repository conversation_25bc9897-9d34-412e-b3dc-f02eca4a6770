#!/bin/bash

########################################
# 清理脚本 clean.sh
# 功能：删除本地临时文件
# 参数说明：
#   --all        删除所有非 git 代码临时文件; 默认选项。
#   --build      仅删除构建过程文件和产物
#   -h/--help    显示帮助信息
########################################

SCRIPT_DIR=$(cd "$(dirname "$0")" && pwd)

# 初始化互斥参数标志位
all_flag=0
buildonly_flag=0

# 帮助信息函数
function show_help() {
    cat <<EOF
Usage: $0 [OPTIONS]

互斥参数选项:
  --all        删除所有非 git 代码临时文件; 默认选项。
  --build      仅删除构建过程文件和产物
  -h/--help    显示帮助信息

示例:
  $0
  $0 --all
  $0 --build
EOF
}

# 使用 getopt 解析长参数（兼容性处理）
TEMP=$(getopt -o h --long all,build,help -n 'clean.sh' -- "$@")

if [ $? != 0 ]; then
    echo "参数解析失败，使用 --help 查看帮助" >&2
    exit 1
fi

eval set -- "$TEMP"

# 参数解析循环
while true ; do
    case "$1" in
        --all)
            all_flag=1
            shift ;;
        --build)
            buildonly_flag=1
            shift ;;
        -h|--help)
            show_help
            exit 0 ;;
        --) shift ; break ;;
        *) echo "未知参数: $1" >&2; exit 1 ;;
    esac
done

if [ $all_flag -eq 0 ] && [ $buildonly_flag -eq 0 ]; then
    all_flag=1
fi

# 执行清理操作
function clean_all() {
    echo "正在删除所有非 git 代码文件..."
    git clean -fdx .

    pushd $SCRIPT_DIR/cfs_fuse_v1/libfuse
    git clean -fdx .
    popd

    pushd $SCRIPT_DIR/cfs_sdk_v1
    git clean -fdx .
    popd
}

function clean_build() {
    echo "正在清理构建目录..."

    pushd $SCRIPT_DIR/cfs_cli
    rm -rf output include lib
    popd

    pushd $SCRIPT_DIR/cfs_fuse_v1
    rm -rf build output
    rm -rf libfuse/output
    rm -rf third_party/include/libfuse3
    rm -rf third_party/lib/libfuse3.a
    rm -rf third_party/include/cloudfs.h
    rm -rf third_party/lib/libcloudfs.so
    popd

    pushd $SCRIPT_DIR/cfs_sdk
    rm -rf build output
    popd

    pushd $SCRIPT_DIR/cfs_sdk_java
    rm -rf builds target dependency-reduced-pom.xml
    rm -rf src/main/native/include/cloudfs2
    rm -rf src/main/native/include/fbs
    rm -rf src/main/native/include/flatbuffers
    rm -rf src/main/resources/lib
    popd

    pushd $SCRIPT_DIR/cfs_sdk_v1
    rm -rf build output
    popd

    pushd $SCRIPT_DIR/fuse
    rm -rf build output third/cfs_sdk
    popd

    # TODO: fsclient hdfs_sdk
}

# 根据参数调用对应函数
if [ $all_flag -eq 1 ]; then
    clean_all
elif [ $buildonly_flag -eq 1 ]; then
    clean_build
fi

exit 0
