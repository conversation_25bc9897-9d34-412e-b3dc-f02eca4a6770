/**
 * ARIES - A Reliable and Impressive EB-level Storage.
 * Copyright (c) 2016 Baidu.com, Inc. All Rights Reserved.
 *
 * Author: <PERSON> <PERSON> (guo<PERSON>@baidu.com)
 * Date: 2016/09/01
 * Desciption: Declaration of validator control service implements
 *
 */

#ifndef BAIDU_INF_ARIES_VALIDATOR_CONTROL_SERVICE_H
#define BAIDU_INF_ARIES_VALIDATOR_CONTROL_SERVICE_H

#include "baidu/rpc/server.h"
#include "baidu/inf/aries-api/common/proto/validator.pb.h"
#include "baidu/inf/aries-api/common/common.h"
#include "baidu/inf/aries-api/common/priority_queue.h"
#include "baidu/inf/aries-api/common/types.h"
#include "baidu/inf/aries/validator/flags.h"

namespace aries {
namespace validator {

class ControlServiceImpl : public ::aries::pb::ValidatorControlService {
public:
    virtual void verify_blob(::google::protobuf::RpcController* controller,
                            const ::aries::pb::VerifyBlobRequest* request,
                            ::aries::pb::AckResponse* response,
                            ::google::protobuf::Closure* done);
};

}
}

#endif
