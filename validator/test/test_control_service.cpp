/**
 * ARIES - A Reliable and Impressive EB-level Storage.
 * Copyright (c) 2016 Baidu.com, Inc. All Rights Reserved.
 *
 * Author: <PERSON> (guo<PERSON>@baidu.com)
 * Date: 2016/12/01
 * Description: Unittest for ControlService
 *
 */

#include <gtest/gtest.h>
#include <base/logging.h>
#include <baidu/rpc/server.h>
#include "baidu/inf/aries-api/common/types.h"
#include "baidu/inf/aries-api/common/rpc_stub.h"
#include "baidu/inf/aries-api/common/closure.h"
#include "baidu/inf/aries/validator/control_service.h"
#include "baidu/inf/aries/validator/keymap/keymap.h"
#include "baidu/inf/aries/validator/global.h"
#include "baidu/inf/aries/validator/flags.h"

namespace aries {
namespace validator {

static int g_server_port = 60001;
static int g_stub_port = 60002;

class MockFakeKeyMap : public FakeKeyMap {
public:
    virtual bool is_blob_exist(const std::string& object_key,
                               __uint128_t blob_id) {
        return _is_return_exist;
    }

    static void set_return_exist() {
        _is_return_exist = true;
    }

    static void clear_return_exist() {
        _is_return_exist = false;
    }

private:
    static bool _is_return_exist;
};

bool MockFakeKeyMap::_is_return_exist = false;
MockFakeKeyMap* g_mock_keymap = NULL;

class TestEnvironment : public ::testing::Environment {
public:
    void SetUp() {
        // start local server
        base::EndPoint listen_addr;
        ASSERT_EQ(0, base::str2endpoint("127.0.0.1", g_server_port, &listen_addr));
        ASSERT_EQ(0, _server.AddService(&_control_service, baidu::rpc::SERVER_DOESNT_OWN_SERVICE));
        ASSERT_EQ(0, _server.Start(listen_addr, NULL));
        KeyMapFactory::register_keymap("FAKE", FLAGS_dataagent_address);
        g_mock_keymap = new MockFakeKeyMap;
        KeyMapFactory::_keymaps["FAKE"] = g_mock_keymap;
    }
    void TearDown() {
        _server.Stop(200);
    }
private:
    baidu::rpc::Server _server;
    ControlServiceImpl _control_service;
};

class ControlServiceTests : public ::testing::Test {
};

/**
 * Test Cases
 */
TEST_F(ControlServiceTests, verify_blob) {
    base::EndPoint remote_addr;
    ASSERT_EQ(0, base::str2endpoint("127.0.0.1", g_server_port, &remote_addr));
    base::EndPoint stub_addr;
    ASSERT_EQ(0, base::str2endpoint("127.0.0.1", g_stub_port, &stub_addr));
    aries::pb::NodeId stub_node = common::endpoint2node(stub_addr);

    aries::pb::AckResponse response;
    aries::pb::VerifyBlobRequest request;
    request.set_token(FLAGS_token);
    request.set_volume_id(1);
    request.set_vbid(1);
    request.set_object_key("key");

    FLAGS_keymap_owner = "FAKE";
    MockFakeKeyMap::set_return_exist();

    ValidatorStub stub;
    SynchronizedClosure closure;
    stub.verify_blob(remote_addr, &request, &response, &closure);
    closure.wait();
    ASSERT_EQ(AIE_OK, response.status().code());
    ASSERT_EQ(1, g_blob_queue->size());
}

}
}

int main(int argc, char* argv[]) {
    ::testing::AddGlobalTestEnvironment(new aries::validator::TestEnvironment());
    ::testing::InitGoogleTest(&argc, argv);
    return RUN_ALL_TESTS();
}

