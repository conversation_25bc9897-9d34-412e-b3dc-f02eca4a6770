/**
 * ARIES - A Reliable and Impressive EB-level Storage.
 * Copyright (c) 2016 Baidu.com, Inc. All Rights Reserved.
 *
 * Author: <PERSON> (guo<PERSON>@baidu.com)
 * Date: 2016/11/29
 * Desciption: Implementation of validator verify worker
 *
 */

#include <atomic>
#include <bthread.h>
#include <bthread_types.h>
#include "baidu/inf/aries-api/common/common.h"
#include "baidu/inf/aries-api/common/types.h"
#include "baidu/inf/aries-api/common/lock.h"
#include "baidu/inf/aries-api/common/rpc_stub.h"
#include "baidu/inf/aries-api/common/closure.h"
#include "baidu/inf/aries/validator/global.h"
#include "baidu/inf/aries/validator/flags.h"
#include "baidu/inf/aries/validator/keymap/keymap.h"
#include "baidu/inf/aries/validator/verify_worker.h"
#include "baidu/inf/aries-api/common/proto/data_agent.pb.h"

namespace aries {
namespace validator {

VerifyWorker::VerifyWorker() {
    _thread_id = 0;
    _attr = BTHREAD_ATTR_NORMAL;
    LOG(NOTICE) << "verify worker " << std::hex << this << " created";
}

VerifyWorker::~VerifyWorker() {
    LOG(NOTICE) << "verify worker " << std::hex << this << " destroyed";
}

static void* thread_func(void *args) {
    VerifyWorker* worker = static_cast<VerifyWorker*>(args);
    worker->verify_blob();
    return NULL;
}

void VerifyWorker::start() {
    int ret = bthread_start_background(&_thread_id, &_attr, thread_func, (void*)this);
    assert(ret == 0);
}

void VerifyWorker::verify_blob() {
    while (true) {
        std::shared_ptr<BlobContext> blobctx = g_blob_queue->pop();
        if (!blobctx) {
            bthread_usleep(100 * 1000);
            continue;
        }
        uint64_t vid = bid2vid(blobctx->blob_id);
        uint64_t vbid = bid2vbid(blobctx->blob_id);
        LOG(NOTICE) << "verify blob, vid:" << vid << " vbid:" << vbid
                << " object_key:" << blobctx->object_key;

        KeyMap* keymap = KeyMapFactory::get_keymap(FLAGS_keymap_owner);
        bool exist = keymap->is_blob_exist(blobctx->object_key, blobctx->blob_id);
        if (exist) {
            LOG(NOTICE) << "do nothing due to blob exist from keymap,"
                << " vid:" << vid << " vbid:" << vbid
                << " object_key:" << blobctx->object_key;
            continue;
        }

        // If blob is garbage, send remove blob to data agent
        LOG(WARNING) << "remove blob due to not exist from keymap,"
                << " vid:" << vid << " vbid:" << vbid
                << " object_key:" << blobctx->object_key;
        aries::pb::AckResponse resp;
        aries::pb::BlobRemoveRequest req;
        req.set_token(FLAGS_token);
        req.set_vid(vid);
        req.set_vbid(vbid);

        SynchronizedClosure closure;
        DataAgentStub stub;
        stub.remove(base::EndPoint(), &req, &resp, &closure, NULL, &g_dataagent_channel);
        closure.wait();
    }
}

}
}
