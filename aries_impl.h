/**
 * ARIES - A Reliable and Impressive EB-level Storage.
 * Copyright (c) 2016 Baidu.com, Inc. All Rights Reserved.
 *
 * Author: <PERSON> (guo<PERSON>@baidu.com)
 * Date: 2016/08/31
 * Desciption: aries api entrance
 *
 */

#pragma once

#include <atomic>
#include <baidu/rpc/channel.h>
#include <baidu/rpc/retry_policy.h>
#include "baidu/inf/aries-api/aries.h"
#include "baidu/inf/aries-api/common/proto/data_agent.pb.h"
#include "baidu/inf/aries-api/common/checksum.h"
#include "baidu/inf/aries-api/proxy/proxy.h"

namespace aries {

class AriesRetryPolicy : public baidu::rpc::RetryPolicy {
public:
    bool DoRetry(const baidu::rpc::Controller* cntl) const {
        const int error_code = cntl->ErrorCode();
        if (!error_code) {
            return false;
        }
        if (baidu::rpc::SYS_ENETUNREACH == error_code ||
            baidu::rpc::SYS_ENETDOWN == error_code ||
            baidu::rpc::SYS_EAGAIN == error_code) {
            return true;
        }
        return baidu::rpc::DefaultRetryPolicy()->DoRetry(cntl);
    }
};

extern AriesRetryPolicy g_aries_retry_policy;

class LocalRpcWrapper {
public:
    LocalRpcWrapper(baidu::rpc::Channel *channel) : _channel(channel), _local_channel(NULL) {}
    int init_local(int local_port);
    ~LocalRpcWrapper() {
        if (_local_channel != NULL) {
            delete _local_channel;
        }
    }

    void put(baidu::rpc::Controller* controller,
            const ::aries::pb::BlobPutRequest* request,
            ::aries::pb::BlobPutResponse* response,
            ::google::protobuf::Closure* done);
    void get(baidu::rpc::Controller* controller,
            const ::aries::pb::BlobGetRequest* request,
            ::aries::pb::BlobGetResponse* response,
            ::google::protobuf::Closure* done);
    void remove(baidu::rpc::Controller* controller,
            const ::aries::pb::BlobRemoveRequest* request,
            ::aries::pb::AckResponse* response,
            ::google::protobuf::Closure* done);
private:
    baidu::rpc::Channel *_channel;
    baidu::rpc::Channel *_local_channel;
};

class ProxyWrapper {
public:
    int init(const ProxyConf& conf, bool use_shared_proxy);
    Errno get(RequestOption &request_option, uint128_t bid, const BlobRange *range,
            std::string* data, BlobMeta *blob_meta,
            pb::BlobRequestErrorInfo* request_error_info);

    Errno put(RequestOption &request_option, const std::string& space_name,
            const std::string& key, const std::string& meta, const std::string& data,
            uint128_t* bid, BlobMeta* blob_meta,
            pb::BlobRequestErrorInfo* request_error_info);

    Errno remove(RequestOption &request_option, uint128_t bid,
            pb::BlobRequestErrorInfo* request_error_info);

    Errno retrieve_blobs(RequestOption& request_option, const RetrieveOption& option);

    Errno get_retrieve_task_status(RequestOption& request_option, 
            const std::string& task_id, RetrieveTaskStatus* status);

    Errno cancel_retrieve_task(RequestOption& request_option, const std::string& task_id);

    Errno allocate_volume(RequestOption& request_option,
            const std::string& space_name, uint64_t volume_id_hint, uint64_t* volume_id);

    Errno append_blob(RequestOption& request_option, 
                      uint64_t volume_id,
                      const std::string& key,
                      const std::string& meta,
                      const std::string& data,
                      uint128_t *bid);

    Errno release_volume(RequestOption& request_option, uint64_t volume_id);

    Errno seal_volume(RequestOption& request_option, uint64_t volume_id);

    Errno get_volume_size(uint64_t volume_id, int64_t* size);

    Errno set_volume_ttl(RequestOption& request_option, 
        uint64_t volume_id, uint64_t volume_ttl);

    Errno set_blob_ttl(RequestOption& request_option, uint128_t bid, uint32_t blob_ttl);

    Errno show_space(RequestOption& request_option, const std::string& space_name,
            aries::pb::SpaceInfo* space_info);

    Errno list_disk_usage(RequestOption& request_option, bool show_az, bool show_idc,
            bool show_rack, bool show_group, bool show_disk_type,
            aries::pb::ClusterDiskUsageInfo* usage_info);

    Errno list_disk_io_stats(RequestOption& request_option, bool show_az, bool show_idc,
            bool show_rack, bool show_group, bool show_disk_type, bool show_user,
            aries::pb::ClusterDiskIoStatsInfo* io_stats_info);

    Errno show_space_usage(RequestOption& request_option, const std::string& space_name,
            aries::pb::SpaceUsageInfo* usage_info);

    Errno create_stream(CreateStreamOptions& opt, uint128_t* stream_id);

    Errno unlink_stream(RequestOption& request_option, uint128_t stream_id);

    Errno truncate_stream(RequestOption& request_option, uint128_t stream_id,
        uint64_t tail_offset, std::optional<uint64_t> stream_version);

    Errno head_truncate_stream(RequestOption& request_option, uint128_t stream_id,
        uint64_t head_offset, std::optional<uint64_t> stream_version);

    Errno stat_stream(RequestOption& request_option, uint128_t stream_id, StreamStatus* st);

    Errno stat_streams(RequestOption& request_option, const std::vector<uint128_t>& stream_ids,
        std::vector<StreamStatus>* stream_status);

    Errno show_stream_space_statistics(RequestOption& request_option,
        const std::string& space_name, aries::pb::StreamSpaceStatistics* statistic);

    Errno link_stream(RequestOption& request_option, uint128_t stream_id);

    Errno utime_stream(RequestOption& request_option, uint128_t stream_id,
        std::optional<uint64_t> atime, std::optional<uint64_t> mtime);

    Errno set_stream_cache_policy(StreamCacheOptions& opt);

    Errno open_stream_writer(StreamWriterOptions& opt, std::shared_ptr<StreamWriter>* writer);

    Errno force_open_stream_writer(StreamWriterOptions& opt, std::shared_ptr<StreamWriter>* writer);

    Errno open_stream_reader(StreamReaderOptions& opt, std::shared_ptr<StreamReader>* reader);
private:
    std::shared_ptr<Proxy> _proxy = nullptr;
    ProxyConf _conf;
};

// default no-op
class AriesInfoTransform {
public:
    virtual void transform(aries::pb::SpaceUsageInfo* usage_info) {}
    virtual void transform(aries::pb::SpaceInfo* space_info) {}
    virtual void transform(aries::pb::ClusterDiskUsageInfo* usage_info) {}
    virtual void transform(aries::pb::ClusterDiskIoStatsInfo* io_stats_info) {}
};

class AriesClientImpl : public AriesStreamClient {
public:
    virtual ~AriesClientImpl() {
        if (_proxy_wrapper) {
            delete _proxy_wrapper;
            _proxy_wrapper = nullptr;
        }
    }

    AriesClientImpl() : _stub(&_channel), _info_transform(new AriesInfoTransform) {}

    virtual Errno get(RequestOption &request_option, uint128_t bid, const BlobRange *range, std::string* data,
            BlobMeta *blob_meta,
            pb::BlobRequestErrorInfo* request_error_info);

    virtual Errno put(RequestOption &request_option, const std::string& space_name,
            const std::string& key, const std::string& meta, const std::string& data,
            uint128_t* bid, BlobMeta* blob_meta,
            pb::BlobRequestErrorInfo* request_error_info);

    virtual Errno remove(RequestOption &request_option, uint128_t bid,
            pb::BlobRequestErrorInfo* request_error_info);
    
    virtual Errno allocate_volume(uint64_t trace_id,
                                  const std::string& space_name,
                                  uint64_t volume_id_hint,
                                  uint64_t* volume_id);

    virtual Errno append_blob(RequestOption& request_option,
                              uint64_t volume_id,
                              const std::string& key,
                              const std::string& meta,
                              const std::string& data,
                              uint128_t *bid);

    virtual Errno release_volume(uint64_t volume_id);

    virtual Errno seal_volume(uint64_t volume_id);

    virtual Errno get_volume_size(uint64_t volume_id, int64_t* size);

    virtual Errno set_volume_ttl(uint64_t volume_id, uint64_t volume_ttl);

    virtual Errno set_blob_ttl(uint128_t bid, uint32_t blob_ttl);

    virtual Errno retrieve_blobs(uint64_t trace_id, const RetrieveOption& option);

    virtual Errno get_retrieve_task_status(uint64_t trace_id, const std::string& task_id, RetrieveTaskStatus* status);

    virtual Errno cancel_retrieve_task(uint64_t trace_id, const std::string& task_id);

    virtual Errno show_space(RequestOption& request_option, const std::string& space_name,
            aries::pb::SpaceInfo* space_info);

    virtual Errno list_disk_usage(RequestOption& request_option, bool show_az, bool show_idc,
            bool show_rack, bool show_group, bool show_disk_type,
            aries::pb::ClusterDiskUsageInfo* usage_info);

    virtual Errno list_disk_io_stats(RequestOption& request_option, bool show_az, bool show_idc,
            bool show_rack, bool show_group, bool show_disk_type, bool show_user,
            aries::pb::ClusterDiskIoStatsInfo* io_stats_info);

    virtual Errno show_space_usage(RequestOption& request_option, const std::string& space_name,
            aries::pb::SpaceUsageInfo* usage_info);

    virtual Errno init(const ConfInfo & conf_info);

    void init_rpc_option(RequestOption &request_option, baidu::rpc::Controller &ctl);

protected:
    ConfInfo _conf_info;
    baidu::rpc::Channel _channel;
    LocalRpcWrapper _stub;
    ProxyWrapper *_proxy_wrapper{nullptr};
    std::unique_ptr<AriesInfoTransform> _info_transform{nullptr};
};

class AriesStreamClientImpl : public AriesClientImpl {
public:
    virtual ~AriesStreamClientImpl() {}

    Errno init(const ConfInfo& conf_info) {
        return AriesClientImpl::init(conf_info);
    }

    virtual Errno create_stream(CreateStreamOptions& opt, uint128_t* stream_id);

    virtual Errno unlink_stream(RequestOption& request_option, uint128_t stream_id);

    virtual Errno truncate_stream(RequestOption& request_option, uint128_t stream_id,
        uint64_t tail_offset, std::optional<uint64_t> stream_version);

    virtual Errno head_truncate_stream(RequestOption& request_option, uint128_t stream_id,
        uint64_t head_offset, std::optional<uint64_t> stream_version);

    virtual Errno stat_stream(RequestOption& request_option, uint128_t stream_id, StreamStatus* st);

    virtual Errno stat_streams(RequestOption& request_option, const std::vector<uint128_t>& stream_ids,
        std::vector<StreamStatus>* stream_status);

    virtual Errno show_stream_space_statistics(RequestOption& request_option,
        const std::string& space_name, aries::pb::StreamSpaceStatistics* statistic);

    virtual Errno link_stream(RequestOption& request_option, uint128_t stream_id);

    virtual Errno utime_stream(RequestOption& request_option, uint128_t stream_id,
        std::optional<uint64_t> atime, std::optional<uint64_t> mtime);

    virtual Errno set_stream_cache_policy(StreamCacheOptions& opt);

    virtual Errno open_stream_writer(StreamWriterOptions& opt, std::shared_ptr<StreamWriter>* writer);

    virtual Errno force_open_stream_writer(StreamWriterOptions& opt, std::shared_ptr<StreamWriter>* writer);

    virtual Errno open_stream_reader(StreamReaderOptions& opt, std::shared_ptr<StreamReader>* reader);

};

}
