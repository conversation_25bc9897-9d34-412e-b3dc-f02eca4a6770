/***************************************************************************
 * 
 * Copyright (c) 2021 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/
 
 /**
 * @file tape_center_state_machine.cpp
 * <AUTHOR>
 * @date 2021/11/03 09:28:38
 * @version 1.0 
 * @brief 
 *  
 **/

#include "baidu/inf/aries/tapecenter/tape_center_state_machine.h"

#include "baidu/rpc/server.h"
#include "raft/snapshot.h"
#include "baidu/inf/aries-api/common/common.h"

#include "baidu/inf/aries/tapecenter/flags.h"
#include "baidu/inf/aries/tapecenter/common.h"
#include "baidu/inf/aries/tapecenter/snapshot.h"
#include "baidu/inf/aries/tapecenter/task_manager.h"
#include "baidu/inf/aries/tapecenter/scheduler/task_scheduler.h"

namespace aries::tapecenter {

std::unique_ptr<TapeCenterStateMachine> g_tape_center_state_machine = std::make_unique<TapeCenterStateMachine>();

void TapeCenterStateMachine::on_apply(raft::Iterator& iter) {
    common::ScopedMutexLock lock(_mutex);

    for (; iter.valid(); iter.next()) {
        raft::Closure* done = iter.done();
        baidu::rpc::ClosureGuard done_guard(done);
        uint64_t raft_index = iter.index();
        g_task_manager->set_raft_index(raft_index);

        base::Status st;
        base::IOBuf log_data(iter.data());
        TapeCenterLogType type;
        log_data.cutn(&type, sizeof(type));

        LOG(TRACE) << "apply " << tape_center_log_type2string(type) << " raft_index:" << raft_index;
        st = on_apply_do(type, log_data, done);

        if (done) {
            done->status().swap(st);
        }
    }
}

void TapeCenterStateMachine::on_leader_start() {
    LOG(WARNING) << "on leader start";
    common::ScopedMutexLock lock(_mutex);

    g_task_manager->set_state(TAPE_CENTER_STATE_LEADER_STARTING);
    g_task_scheduler->init();
    g_task_scheduler->start();
    g_task_manager->set_state(TAPE_CENTER_STATE_LEADER_WORKING);
    LOG(WARNING) << "leader start succeeded";
}

void TapeCenterStateMachine::on_leader_stop() {
    LOG(WARNING) << "not support on leader stop, master will just exit.";
    //just exist.
    _exit(0);
}

void TapeCenterStateMachine::on_shutdown() {
    common::ScopedMutexLock lock(_mutex);
    LOG(NOTICE) << "tapecenter on shutdown";
}

void TapeCenterStateMachine::on_start_following(const ::raft::LeaderChangeContext& ctx) {
    common::ScopedMutexLock lock(_mutex);
    LOG(NOTICE) << "tapecenter start following:" << ctx.leader_id().addr;
}

void TapeCenterStateMachine::on_error(const ::raft::Error& e) {
    LOG(FATAL) << "tapecenter get a raft error:" << e << ", can't be healthy again, must exist";
    _exit(-1);    
}

int TapeCenterStateMachine::on_snapshot_load(::raft::SnapshotReader* reader) {
    auto path = reader->get_path();
    LOG(NOTICE) << "begin to load snapshot in path:" << path;
    std::string snapshot_path = path + "/" + FLAGS_snapshot_file_name;
    TapeCenterSnapshot snapshot;
    auto ret = snapshot.load_snapshot(snapshot_path);
    return ret;
}

void TapeCenterStateMachine::on_snapshot_save(::raft::SnapshotWriter* writer,
                ::raft::Closure* done) {
    base::Timer timer;
    timer.start();
    _taking_snapshot = true;
    g_task_manager->set_last_snapshot_timestamp(base::gettimeofday_s());

    baidu::rpc::ClosureGuard done_guard(done);
    std::string path = writer->get_path();
    LOG(NOTICE) << "begin to save snapshot in path:" << path;
    std::string snapshot_path = path + "/" + FLAGS_snapshot_file_name;
    TapeCenterSnapshot snapshot;
    auto ret = snapshot.save_snapshot(snapshot_path);
    _taking_snapshot = false;
    timer.stop();
    LOG(TRACE) << "tapecenter do save snapshot cost_ms:" << timer.m_elapsed();
    if (ret != 0) {
        done->status().set_error(AIE_FAIL, "snapshot failed");
        LOG(FATAL) << "tapecenter do save snapshot failed due to write failed, ret:" << ret;
        return;
    }
    ret = writer->add_file(FLAGS_snapshot_file_name);
    if (ret != 0) {
        done->status().set_error(AIE_FAIL, "snapshot failed");
        LOG(FATAL) << "tapecenter do save snapshot failed due to add file failed, ret:" << ret;
        return;
    }
}

base::Status TapeCenterStateMachine::on_apply_do(const TapeCenterLogType& type,
                const base::IOBuf& log_data,
                raft::Closure* done) {
    base::Status st;
    auto raft_index = g_task_manager->raft_index();
    switch(type) {
        case TAPE_CENTER_LOG_ADD_TASK:
            st = do_add_task(log_data);
            break;
        case TAPE_CENTER_LOG_UPDATE_TASK:
            st = do_update_task(log_data);
            break;
        case TAPE_CENTER_LOG_FINISH_TASK:
            st = do_finish_task(log_data);
            break;
        case TAPE_CENTER_LOG_BATCH_UPDATE_TASK:
            st = do_batch_update_task(log_data);
            break;
        default:
            LOG(FATAL) << "unknown log type " << type;
            st.set_error(AIE_FAIL, "unknown log type");
            break;
    }
    return st;
}

base::Status TapeCenterStateMachine::do_add_task(const ::base::IOBuf& msg) {
    base::Status st;
    pb::TapeTaskInfo task;
    if (!common::parse_message_from_iobuf(&task, msg, &st)) {
        st.set_error(AIE_FAIL, "parse proto failed");
        LOG(WARNING) << "add task failed due to parse message error";
        return st;
    }

    auto ret = g_task_manager->add_task(task);
    if (ret != AIE_OK) {
        LOG(WARNING) << "add task failed, task_id:" << task.task_id()
            << " task_type:" << task.task_type()
            << " ret:" << ret;
        st.set_error(ret, "task exist");
    }
    return st;
}

base::Status TapeCenterStateMachine::do_update_task(const ::base::IOBuf& msg) {
    base::Status st;
    pb::TapeTaskInfo task;
    if (!common::parse_message_from_iobuf(&task, msg, &st)) {
        st.set_error(AIE_FAIL, "parse proto failed");
        LOG(WARNING) << "update task failed due to parse message error";
        return st;
    }

    auto ret = g_task_manager->update_task(task);
    if (ret != AIE_OK) {
        LOG(WARNING) << "update task failed, task_id:" << task.task_id()
            << " task_type:" << task.task_type()
            << " ret:" << ret;
        st.set_error(ret, "task not exist");
    }
    return st;
}

base::Status TapeCenterStateMachine::do_batch_update_task(const ::base::IOBuf& msg) {
    base::Status st;
    pb::BatchUpdateTaskRequest request;
    if (!common::parse_message_from_iobuf(&request, msg, &st)) {
        st.set_error(AIE_FAIL, "parse proto failed");
        LOG(WARNING) << "update task failed due to parse message error";
        return st;
    }
    for (int i = 0; i < request.task_list_size(); ++i) {
        auto ret = g_task_manager->update_task(request.task_list(i));
        if (ret != AIE_OK) {
            LOG(WARNING) << "update task failed, task_id:" << request.task_list(i).task_id()
                << " task_type:" << request.task_list(i).task_type()
                << " ret:" << ret;
            st.set_error(ret, "some task not exist");
        }
    }
    return st;
}

base::Status TapeCenterStateMachine::do_finish_task(const ::base::IOBuf& msg) {
    base::Status st;
    pb::TapeTaskInfo task;
    if (!common::parse_message_from_iobuf(&task, msg, &st)) {
        st.set_error(AIE_FAIL, "parse proto failed");
        LOG(WARNING) << "finish task failed due to parse message error";
        return st;
    }

    auto ret = g_task_manager->finish_task(task);
    if (ret != AIE_OK) {
        LOG(WARNING) << "finish task failed, task_id:" << task.task_id()
            << " task_type:" << task.task_type()
            << " ret:" << ret;
        st.set_error(ret, "finish task error");
    }
    return st;
}
}
/* vim: set ts=4 sw=4 sts=4 tw=100 */
