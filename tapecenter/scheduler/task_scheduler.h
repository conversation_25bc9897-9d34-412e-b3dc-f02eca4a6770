/***************************************************************************
 * 
 * Copyright (c) 2021 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/
 
 /**
 * @file task_scheduler.h
 * <AUTHOR>
 * @date 2021/11/05 15:01:08
 * @version 1.0 
 * @brief 
 *  
 **/
#pragma once

#include <queue>
#include <atomic>
#include <memory>
#include <thread>
#include <unordered_map>
#include "baidu/inf/aries-api/common/lock.h"
#include "baidu/inf/aries-api/common/proto/common.pb.h"

#include "baidu/inf/aries/common/status.h"
#include "baidu/inf/aries/tapecenter/task_manager.h"
#include "baidu/inf/aries/meta_replica/meta_subscriber.h"
#include "baidu/inf/aries/tapecenter/scheduler/delete_scheduler.h"
#include "baidu/inf/aries/tapecenter/scheduler/transform_scheduler.h"
#include "baidu/inf/aries/tapecenter/scheduler/retrieve_scheduler.h"
#include "baidu/inf/aries/tapecenter/scheduler/reclaim_scheduler.h"
#include "baidu/inf/aries/tapecenter/scheduler/check_scheduler.h"
#include "baidu/inf/aries/tapecenter/scheduler/task_context.h"

namespace aries::tapecenter {

class TaskScheduler;
extern std::unique_ptr<TaskScheduler> g_task_scheduler;
extern std::unique_ptr<meta_replica::MetaSubscriber> g_meta_client;

class TaskScheduler {
public:
    TaskScheduler() {}
    virtual ~TaskScheduler() {}
    int init();
    int start();
    void stop();
    int set_last_active_timestamp(const aries::pb::TapeNodeTaskHeartbeatRequest_HeartbeatTaskInfo& task_info);
    Status fetch_transform_task(uint64_t req_addr, pb::TapeTaskInfo* task);
    Status report_transform_task(const pb::TapeTransformTaskReportRequest* request);
    Status report_retrieve_task(const pb::TapeRetrieveTaskReportRequest* request, pb::TapeRetrieveTaskReportResponse* response);
    Status fetch_retrieve_task(uint64_t log_id, uint64_t req_addr, std::vector<pb::TapeTaskInfo>* task,
            pb::LocationOntape* optimal_tape);
    Status report_delete_task(const pb::TapeDeleteTaskReportRequest* request);
    Status fetch_delete_task(uint64_t req_addr, pb::TapeTaskInfo* task);
    Status fetch_reclaim_task(uint64_t req_addr, pb::TapeTaskInfo* task);
    Status report_reclaim_task(const pb::TapeReclaimTaskReportRequest* request);
    Status fetch_check_task(const pb::FetchCheckTaskRequest& request,
            pb::FetchCheckTaskResponse* response);
    Status report_check_task(const aries::pb::CheckTaskReportRequest& request);
    Status retrieve_blobs(const pb::RetrieveBlobsRequest* request);
    Status show_tape_center_task(uint64_t volume_id, TaskContext* ctx);

private:
    void thread_func();
    void notify_thread_func();
    void find_task();
    void clean_task();
    Status update_tape_node_info();

private:
    std::atomic<bool> _stopped;
    std::thread _thread;
    std::thread _notify_thread;
    TransformTaskScheduler _transform_task_scheduler;
    RetrieveTaskScheduler _retrieve_task_scheduler;
    DeleteTaskScheduler _delete_task_scheduler;
    ReclaimTaskScheduler _reclaim_task_scheduler;
    CheckTaskScheduler _check_task_scheduler;
};

}

/* vim: set ts=4 sw=4 sts=4 tw=100 */
