/**
 * @file delete_scheduler.h
 * <AUTHOR>
 * @date 2022/09/13 20:03:37
 * @version 1.0
 * @brief 
 * 
 */
#pragma once

#include <queue>
#include <atomic>
#include <memory>
#include <thread>
#include <unordered_map>
#include "baidu/inf/aries-api/common/lock.h"
#include "baidu/inf/aries-api/common/speed_limit.h"
#include "baidu/inf/aries-api/common/proto/common.pb.h"

#include "baidu/inf/aries/common/status.h"
#include "baidu/inf/aries/tapecenter/task_manager.h"
#include "baidu/inf/aries/meta_replica/meta_subscriber.h"
#include "baidu/inf/aries/tapecenter/scheduler/task_context.h"

namespace aries::tapecenter {

class DeleteTaskScheduler {
public:
    DeleteTaskScheduler() {
        _delete_token_pool =
        std::make_unique<common::TokenPool>(FLAGS_max_delete_token, FLAGS_max_delete_task_num_per_second);
        _delete_token_pool->start();
    }
    virtual ~DeleteTaskScheduler(){} 

    int init(const std::vector<TaskInfoPtr>& task_list);        
    void update_tasks(const std::vector<TaskCtxPtr>& tasks);
    Status fetch_task(uint64_t req_addr, pb::TapeTaskInfo* task);
    Status report_task(const pb::TapeDeleteTaskReportRequest* request);
    void reclaim_expired_tasks();
    int set_last_active_timestamp(uint64_t task_id);
    void report_volume_tape_info();
    void update_tape_node_info(const aries::pb::ListTapeNodeResponse* response);
    void reload_conf();

private:
    Status drop_volume(const pb::TapeTaskInfo& task);
    Status do_update_volume_tape_info(const pb::TapeTaskInfo& task);
    Status do_drop_volume(const pb::TapeTaskInfo& task);

private:
    common::MutexLock _mutex;
    // key:value volume_id:task context
    std::unordered_map<uint64_t, TaskCtxPtr> _task_ctxs;
    std::unordered_map<std::string, TaskCtxPtrQueue> _waiting_task_queue_map;
    std::unordered_map<uint64_t, std::string> _tape_node_map;
    std::unordered_map<uint64_t, TaskCtxPtr> _running_tasks;
    std::unique_ptr<common::TokenPool> _delete_token_pool;
};

}