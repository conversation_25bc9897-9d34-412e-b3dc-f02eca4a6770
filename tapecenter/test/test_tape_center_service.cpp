/**
 * @file test_tape_center_service.cpp
 * <AUTHOR>
 * @date 2023/07/26 16:36:41
 * @version 1.0
 * @brief 
 * 
 */
#include <memory>
#include <gtest/gtest.h>
#include <base/logging.h>
#include <bthread.h>
#include <baidu/rpc/server.h>
#include "baidu/inf/aries-api/common/lock.h"
#include "baidu/inf/aries-api/common/proto/tapecenter.pb.h"
#include "baidu/inf/aries/common/sync_point.h"
#include "baidu/inf/aries-api/test/bmock_util.h"
#include "baidu/inf/aries/common/common.h"
#include "baidu/inf/aries/tapecenter/scheduler/task_scheduler.h"
#include "baidu/inf/aries/tapecenter/tape_center_control.h"

namespace aries::tapecenter {

static uint64_t g_volume_ids[10] = {10000, 10001, 10002, 1, 2, 3, 4, 5, 6, 7};
static std::string g_space_names[10] = {"space0", "space1", "space2", "space3", "space4", "space5", "space6", "space7", "space8", "space9"};

class MockMetaClient : public meta_replica::MetaSubscriber {
public:
    virtual void wait_until_meta_very_new() {
        return;
    }
    virtual AriesErrno init(bool is_not_subsribe) {
        return _let_init_failed ? AIE_FAIL : AIE_OK;
    }
    virtual void list_space(std::vector<std::string>* space_name) {
        for (auto& s : g_space_names) {
            space_name->push_back(s);
        }
    }
    virtual std::shared_ptr<aries::pb::SpaceInfo> get_space_info_ptr(
            const std::string& space_name) {
        auto space_info = std::make_shared<aries::pb::SpaceInfo>();
        for (auto& space : _spaces) {
            if (space->space_name() == space_name) {
                space_info->CopyFrom(*space);
                break;
            }
        }
        return space_info;
    }
    virtual void list_volume(std::vector<uint64_t> *volume_ids) {
        common::ScopedMutexLock lock(_mutex);
        if (_volume_count < 0) {
            return;
        }
        for (int i = 0; i < _volume_count; ++i) {
            volume_ids->push_back(g_volume_ids[i]);
        }
    }
    virtual AriesErrno get_volume_info(const uint64_t volume_id,
                                   aries::pb::MetaReplica::VolumeInfo *volume_info,
                                   aries::pb::SpaceInfo *space_info) {
        if (_let_find_volume_failed) {
            return AIE_FAIL;
        }
        for (size_t i = 0; i < 10; ++i) {
            if (volume_id == g_volume_ids[i] && _volumes.size() > i) {
                volume_info->CopyFrom(*_volumes[i]);
                space_info->CopyFrom(*_spaces[i]);
                return AIE_OK;
            }
        }
        
        return AIE_NOT_EXIST;
    }

    static void add_volume_location_info() {
        auto location_on_tape = _volumes[6]->mutable_location_on_tape();
        _volumes[6]->set_volume_ttl(10);
        location_on_tape->set_location_on_gpfs("/ltfsee/a");
        location_on_tape->set_logical_pool_name("group1");
        auto location = location_on_tape->add_location_on_tape_list();
        location->set_tape_id("tape1");
        location->set_physical_pool_name("pool01");

        location_on_tape = _volumes[2]->mutable_location_on_tape();
        location_on_tape->set_location_on_gpfs("/ltfsee/a");
        location_on_tape->set_logical_pool_name("group1");
        location = location_on_tape->add_location_on_tape_list();
        location->set_tape_id("tape1");
        location->set_physical_pool_name("pool01");

        location_on_tape = _volumes[7]->mutable_location_on_tape();
        _volumes[7]->set_volume_ttl(base::gettimeofday_s() + 9999);
        location_on_tape->set_location_on_gpfs("/ltfsee/a");
        location_on_tape->set_logical_pool_name("group1");
        location = location_on_tape->add_location_on_tape_list();
        location->set_tape_id("tape1");
        location->set_physical_pool_name("pool01");

        _volumes[8]->set_volume_ttl(base::gettimeofday_s() + 9999);
        location_on_tape = _volumes[8]->mutable_location_on_tape();
        location_on_tape->set_location_on_gpfs("/ltfsee/a");
        location_on_tape->set_logical_pool_name("group1");
        location = location_on_tape->add_location_on_tape_list();
        location->set_tape_id("tape1");
        location->set_physical_pool_name("pool01");

        _volumes[9]->set_volume_ttl(base::gettimeofday_s() + 9999);
        location_on_tape = _volumes[9]->mutable_location_on_tape();
        location_on_tape->set_location_on_gpfs("/ltfsee/a");
        location_on_tape->set_logical_pool_name("group2");
        location = location_on_tape->add_location_on_tape_list();
        location->set_tape_id("tape1");
        location->set_physical_pool_name("pool01");
    }

    static void let_init_failed() {
        _let_init_failed = true;
    }
    static void let_init_succ() {
        _let_init_failed = false;
    }

    static void let_find_volume_failed() {
        _let_find_volume_failed = true;
    }
    static void let_find_volume_succ() {
        _let_find_volume_failed = false;
    }

    static void set_volume_count(int count) {
        common::ScopedMutexLock lock(_mutex);
        assert(count <= (int)_volumes.size());
        _volume_count = count;
    }
    static void init_volumes_with_spaces() {
        for (size_t i = 0; i < 10; ++i) {
            auto * vi = new aries::pb::MetaReplica::VolumeInfo();
            vi->set_volume_id(g_volume_ids[i]);
            vi->set_space_name(g_space_names[i]);
            vi->set_volume_ttl(0);
            for (size_t j = 0; j < 6; ++j) {
                auto * vletinfo = vi->add_vlet_info();
                vletinfo->set_shard_index((uint32_t)j);
                base::EndPoint ep;
                ASSERT_EQ(0, common::str2endpoint("127.0.0.1:1234", &ep));
                if (j == 0) {
                    vletinfo->set_node_addr(0);
                } else {
                    vletinfo->set_node_addr(common::endpoint2int(ep) + j);
                }
                if (j == 1) {
                    vletinfo->set_state(VLET_STATE_REPAIRING);
                } else {
                    vletinfo->set_state(VLET_STATE_NORMAL);
                }
            }
            _volumes.push_back(vi);

            if (i != 2) {
                vi->set_is_sealed(true);
            }

            auto * si = new aries::pb::SpaceInfo();
            si->set_space_name(g_space_names[i]);
            si->set_ec_type(EC_RS_ISAL);
            si->set_vlet_type(VLET_TYPE_LINKED_16G_1M_512K);
            si->set_k(4);
            si->set_n(6);
            si->set_put_quorum(5);
            si->set_delete_quorum(3);
            si->set_allocator_collect_quorum(4);
            if (i != 0) {
                si->set_need_archived(true);
                si->set_archive_mode(TAPE);
            }
            si->set_tape_library_group_name("tape_library_group_name");
            _spaces.push_back(si);
        }
        // let volume2 index duplicated (vlet1 and vlet5)
        _volumes[2]->mutable_vlet_info(5)->set_shard_index(1u);
        // let volume1 index overflow
        _volumes[1]->mutable_vlet_info(0)->set_shard_index(1000u);
    }

private:
    static bool _let_init_failed;
    static bool _let_find_volume_failed;
    static int  _volume_count;
    static std::vector<aries::pb::MetaReplica::VolumeInfo*> _volumes;
    static std::vector<aries::pb::SpaceInfo*> _spaces;
    static common::MutexLock _mutex;
};

class MockTapeCenterControl : public TapeCenterControl {
    void add_task(const aries::pb::TapeTaskInfo& task_info) {
        g_task_manager->add_task(task_info);
    }

    void update_task(const aries::pb::TapeTaskInfo& task_info) {
        g_task_manager->update_task(task_info);
    }

    void batch_update_task(const aries::pb::BatchUpdateTaskRequest& request) {
        for (int i = 0; i < request.task_list_size(); i++) {
            g_task_manager->update_task(request.task_list(i));
        }
    }

    void finish_task(const aries::pb::TapeTaskInfo& task_info) {
        g_task_manager->finish_task(task_info);
    }
    void list_raft_peers(std::vector<base::EndPoint>* peer_list) {
        base::EndPoint endpoint;
        peer_list->push_back(endpoint);
    }
    void add_tape_center_peer(const ::aries::pb::AddTapeCenterPeerRequest* request) {
        return;
    }
    void drop_tape_center_peer(const ::aries::pb::DropTapeCenterPeerRequest* request) {
        return;
    }
};

bool MockMetaClient::_let_init_failed = false;
bool MockMetaClient::_let_find_volume_failed = false;
int  MockMetaClient::_volume_count = 0;
std::vector<aries::pb::MetaReplica::VolumeInfo*> MockMetaClient::_volumes;
std::vector<aries::pb::SpaceInfo*> MockMetaClient::_spaces;
common::MutexLock MockMetaClient::_mutex;

class TapeCenterServiceTests : public ::testing::Test {
public:
    TapeCenterServiceTests() {
    }
    virtual ~TapeCenterServiceTests() {}

    void SetUp() {
        auto tapecenter_service = new TapeCenterServiceImpl();
        // _master_control = new MockMasterControl;
        // g_master_control = _master_control;
        CHECK_EQ(0, _server.AddService(tapecenter_service, baidu::rpc::SERVER_OWNS_SERVICE));
        ASSERT_EQ(0, base::str2endpoint("127.0.0.1:60235", &_listen_addr));
        ASSERT_EQ(0, _server.Start(_listen_addr, NULL));
    }

    void TearDown() {
        // stop and join server
        _server.Stop(100);
        _server.Join();
    }

    base::IOBuf prepair_err_data() {
        base::IOBuf data;
        data = "some thing...}";
        return data;
    }

private:
    baidu::rpc::Server _server;
    base::EndPoint _listen_addr;
    // MockMasterControl* _master_control;
};

TEST_F(TapeCenterServiceTests, test_all_not_primary) {
    TapeCenterStub stub(_listen_addr);
        // retrieve_blobs
    {   
        aries::pb::RetrieveBlobsRequest request;
        aries::pb::AckResponse response;
        request.set_token("default_token");
        SynchronizedClosure rpc_waiter;
        stub.retrieve_blobs(_listen_addr, &request, &response, &rpc_waiter, NULL);
        rpc_waiter.wait();
        ASSERT_EQ(response.status().code(), AIE_NOT_PRIMARY);
    }
        // get_retrieve_task_status
    {   
        aries::pb::GetRetrieveTaskStatusRequest request;
        aries::pb::GetRetrieveTaskStatusResponse response;
        request.set_token("default_token");
        SynchronizedClosure rpc_waiter;
        stub.get_retrieve_task_status(_listen_addr, &request, &response, &rpc_waiter, NULL);
        rpc_waiter.wait();
        ASSERT_EQ(response.status().code(), AIE_NOT_PRIMARY);
    }
        // cancel_retrieve_task
    {   
        aries::pb::CancelRetrieveTaskRequest request;
        aries::pb::AckResponse response;
        request.set_token("default_token");
        SynchronizedClosure rpc_waiter;
        stub.cancel_retrieve_task(_listen_addr, &request, &response, &rpc_waiter, NULL);
        rpc_waiter.wait();
        ASSERT_EQ(response.status().code(), AIE_NOT_PRIMARY);
    }
        // check_leader
    {   
        aries::pb::CheckLeaderRequest request;
        aries::pb::CheckLeaderResponse response;
        request.set_token("default_token");
        SynchronizedClosure rpc_waiter;
        stub.check_leader(_listen_addr, &request, &response, &rpc_waiter, NULL);
        rpc_waiter.wait();
        ASSERT_EQ(response.status().code(), AIE_NOT_PRIMARY);
    }
        // fetch_transform_task
    {   
        aries::pb::FetchTaskRequest request;
        aries::pb::FetchTransformTaskResponse response;
        request.set_token("default_token");
        request.set_req_addr(123);
        SynchronizedClosure rpc_waiter;
        stub.fetch_transform_task(_listen_addr, &request, &response, &rpc_waiter, NULL);
        rpc_waiter.wait();
        ASSERT_EQ(response.status().code(), AIE_NOT_PRIMARY);
    }
        // report_transform_task
    {   
        aries::pb::TapeTransformTaskReportRequest request;
        aries::pb::AckResponse response;
        request.set_token("default_token");
        request.set_req_addr(123);
        request.set_task_id(123);
        request.set_volume_id(123);
        SynchronizedClosure rpc_waiter;
        stub.report_transform_task(_listen_addr, &request, &response, &rpc_waiter, NULL);
        rpc_waiter.wait();
        ASSERT_EQ(response.status().code(), AIE_NOT_PRIMARY);
    }
        // fetch_retrieve_task
    {   
        aries::pb::FetchTaskRequest request;
        aries::pb::FetchRetrieveTaskResponse response;
        request.set_token("default_token");
        request.set_req_addr(123);
        SynchronizedClosure rpc_waiter;
        stub.fetch_retrieve_task(_listen_addr, &request, &response, &rpc_waiter, NULL);
        rpc_waiter.wait();
        ASSERT_EQ(response.status().code(), AIE_NOT_PRIMARY);
    }
        // report_retrieve_task
    {   
        aries::pb::TapeRetrieveTaskReportRequest request;
        aries::pb::TapeRetrieveTaskReportResponse response;
        request.set_token("default_token");
        request.set_req_addr(123);
        request.set_task_id(123);
        request.set_volume_id(123);
        request.set_task_begin_timestamp(123);
        auto blob_info = request.add_blob_list();
        blob_info->set_vbid(123);
        SynchronizedClosure rpc_waiter;
        stub.report_retrieve_task(_listen_addr, &request, &response, &rpc_waiter, NULL);
        rpc_waiter.wait();
        ASSERT_EQ(response.status().code(), AIE_NOT_PRIMARY);
    }
        // drop_tape_center_task
    {   
        aries::pb::DropTapeTaskRequest request;
        aries::pb::AckResponse response;
        request.set_token("default_token");
        SynchronizedClosure rpc_waiter;
        stub.drop_tape_center_task(_listen_addr, &request, &response, &rpc_waiter, NULL);
        rpc_waiter.wait();
        ASSERT_EQ(response.status().code(), AIE_NOT_PRIMARY);
    }
        // add_tape_center_peer
    {   
        aries::pb::AddTapeCenterPeerRequest request;
        aries::pb::AckResponse response;
        request.set_token("default_token");
        request.set_tape_center_addr(123);
        SynchronizedClosure rpc_waiter;
        stub.add_tape_center_peer(_listen_addr, &request, &response, &rpc_waiter, NULL);
        rpc_waiter.wait();
        ASSERT_EQ(response.status().code(), AIE_NOT_PRIMARY);
    }
        // drop_tape_center_peer
    {   
        aries::pb::DropTapeCenterPeerRequest request;
        aries::pb::AckResponse response;
        request.set_token("default_token");
        request.set_tape_center_addr(123);
        SynchronizedClosure rpc_waiter;
        stub.drop_tape_center_peer(_listen_addr, &request, &response, &rpc_waiter, NULL);
        rpc_waiter.wait();
        ASSERT_EQ(response.status().code(), AIE_NOT_PRIMARY);
    }
        // fetch_delete_task
    {   
        aries::pb::FetchDeleteTaskRequest request;
        aries::pb::FetchDeleteTaskResponse response;
        request.set_token("default_token");
        SynchronizedClosure rpc_waiter;
        stub.fetch_delete_task(_listen_addr, &request, &response, &rpc_waiter, NULL);
        rpc_waiter.wait();
        ASSERT_EQ(response.status().code(), AIE_NOT_PRIMARY);
    }
        // report_delete_task
    {   
        aries::pb::TapeDeleteTaskReportRequest request;
        aries::pb::AckResponse response;
        request.set_token("default_token");
        SynchronizedClosure rpc_waiter;
        stub.report_delete_task(_listen_addr, &request, &response, &rpc_waiter, NULL);
        rpc_waiter.wait();
        ASSERT_EQ(response.status().code(), AIE_NOT_PRIMARY);
    }
        // fetch_check_task
    {   
        aries::pb::FetchCheckTaskRequest request;
        aries::pb::FetchCheckTaskResponse response;
        request.set_token("default_token");
        SynchronizedClosure rpc_waiter;
        stub.fetch_check_task(_listen_addr, &request, &response, &rpc_waiter, NULL);
        rpc_waiter.wait();
        ASSERT_EQ(response.status().code(), AIE_NOT_PRIMARY);
    }
        // report_check_task
    {   
        aries::pb::CheckTaskReportRequest request;
        aries::pb::AckResponse response;
        request.set_token("default_token");
        request.set_req_addr(123);
        request.set_volume_id(123);
        SynchronizedClosure rpc_waiter;
        stub.report_check_task(_listen_addr, &request, &response, &rpc_waiter, NULL);
        rpc_waiter.wait();
        ASSERT_EQ(response.status().code(), AIE_NOT_PRIMARY);
    }
        // fetch_reclaim_task
    {   
        aries::pb::FetchReclaimTaskRequest request;
        aries::pb::FetchReclaimTaskResponse response;
        request.set_token("default_token");
        SynchronizedClosure rpc_waiter;
        stub.fetch_reclaim_task(_listen_addr, &request, &response, &rpc_waiter, NULL);
        rpc_waiter.wait();
        ASSERT_EQ(response.status().code(), AIE_NOT_PRIMARY);
    }
        // report_reclaim_task
    {   
        aries::pb::TapeReclaimTaskReportRequest request;
        aries::pb::AckResponse response;
        request.set_token("default_token");
        SynchronizedClosure rpc_waiter;
        stub.report_reclaim_task(_listen_addr, &request, &response, &rpc_waiter, NULL);
        rpc_waiter.wait();
        ASSERT_EQ(response.status().code(), AIE_NOT_PRIMARY);
    }
        // update_tape_center_task
    {   
        aries::pb::UpdateTapeCenterTaskRequest request;
        aries::pb::AckResponse response;
        request.set_token("default_token");
        SynchronizedClosure rpc_waiter;
        stub.update_tape_center_task(_listen_addr, &request, &response, &rpc_waiter, NULL);
        rpc_waiter.wait();
        ASSERT_EQ(response.status().code(), AIE_NOT_EXIST);
    }
}

TEST_F(TapeCenterServiceTests, test_all) {
    TapeCenterStub stub(_listen_addr);
    g_task_manager->set_state(TAPE_CENTER_STATE_LEADER_WORKING);
    g_meta_client.reset(new MockMetaClient());
    g_tape_center_control.reset(new MockTapeCenterControl());
    MockMetaClient::init_volumes_with_spaces();
    MockMetaClient::add_volume_location_info();
        // retrieve_blobs
    {   
        {
            aries::pb::RetrieveBlobsRequest request;
            aries::pb::AckResponse response;
            request.set_token("default_token");
            request.set_task_id("user_task_id");
            request.set_req_addr(123);
            request.set_log_id(123);
            request.set_task_deadline(123);
            auto blob_group = request.add_blob_groups();
            blob_group->set_tag("test1");
            auto blob = blob_group->add_blob_ids();
            blob->set_volume_id(4);
            blob->set_vbid(123);
            SynchronizedClosure rpc_waiter;
            stub.retrieve_blobs(_listen_addr, &request, &response, &rpc_waiter, NULL);
            rpc_waiter.wait();
            ASSERT_EQ(response.status().code(), AE_VOLUME_NOT_EXIST);
        }
        {
            aries::pb::RetrieveBlobsRequest request;
            aries::pb::AckResponse response;
            request.set_token("default_token");
            request.set_task_id("user_task_id");
            request.set_req_addr(123);
            request.set_log_id(123);
            request.set_task_deadline(123);
            SynchronizedClosure rpc_waiter;
            auto blob_group = request.add_blob_groups();
            blob_group->set_tag("test1");
            auto blob2 = blob_group->add_blob_ids();
            blob2->set_volume_id(100000);
            blob2->set_vbid(123);
            stub.retrieve_blobs(_listen_addr, &request, &response, &rpc_waiter, NULL);
            rpc_waiter.wait();
            ASSERT_EQ(response.status().code(), AE_NOT_EXIST);
        }
        {
            aries::pb::RetrieveBlobsRequest request;
            aries::pb::AckResponse response;
            request.set_token("default_token");
            request.set_task_id("user_task_id");
            request.set_req_addr(123);
            request.set_log_id(123);
            request.set_task_deadline(123);
            SynchronizedClosure rpc_waiter;
            auto blob_group = request.add_blob_groups();
            blob_group->set_tag("test1");
            auto blob2 = blob_group->add_blob_ids();
            blob2->set_volume_id(10000);
            blob2->set_vbid(123);
            stub.retrieve_blobs(_listen_addr, &request, &response, &rpc_waiter, NULL);
            rpc_waiter.wait();
            ASSERT_EQ(response.status().code(), AIE_FAIL);
        }
        {
            aries::pb::RetrieveBlobsRequest request;
            aries::pb::AckResponse response;
            request.set_token("default_token");
            request.set_task_id("user_task_id");
            request.set_req_addr(123);
            request.set_log_id(123);
            request.set_task_deadline(123);
            SynchronizedClosure rpc_waiter;
            auto blob_group = request.add_blob_groups();
            blob_group->set_tag("test1");
            auto blob2 = blob_group->add_blob_ids();
            blob2->set_volume_id(7);
            blob2->set_vbid(123);
            stub.retrieve_blobs(_listen_addr, &request, &response, &rpc_waiter, NULL);
            rpc_waiter.wait();
            ASSERT_EQ(response.status().code(), AIE_OK);
        }
        {
            aries::pb::RetrieveBlobsRequest request;
            aries::pb::AckResponse response;
            request.set_token("default_token");
            request.set_task_id("user_task_id");
            request.set_req_addr(123);
            request.set_log_id(123);
            request.set_task_deadline(123);
            SynchronizedClosure rpc_waiter;
            auto blob_group = request.add_blob_groups();
            blob_group->set_tag("test1");
            auto blob2 = blob_group->add_blob_ids();
            blob2->set_volume_id(7);
            blob2->set_vbid(123);
            stub.retrieve_blobs(_listen_addr, &request, &response, &rpc_waiter, NULL);
            rpc_waiter.wait();
            ASSERT_EQ(response.status().code(), AIE_EXIST);
        }
    }
        // get_retrieve_task_status
    {   
        {
            aries::pb::GetRetrieveTaskStatusRequest request;
            aries::pb::GetRetrieveTaskStatusResponse response;
            request.set_token("default_token");
            SynchronizedClosure rpc_waiter;
            stub.get_retrieve_task_status(_listen_addr, &request, &response, &rpc_waiter, NULL);
            rpc_waiter.wait();
            ASSERT_EQ(response.state(), RTS_NONE);
        }
        {
            aries::pb::GetRetrieveTaskStatusRequest request;
            aries::pb::GetRetrieveTaskStatusResponse response;
            request.set_token("default_token");
            SynchronizedClosure rpc_waiter;
            request.set_task_id("user_task_id");
            stub.get_retrieve_task_status(_listen_addr, &request, &response, &rpc_waiter, NULL);
            rpc_waiter.wait();
            ASSERT_EQ(response.state(), RTS_RUNNING);
        }
    }
        // cancel_retrieve_task
    {   
        {
            aries::pb::CancelRetrieveTaskRequest request;
            aries::pb::AckResponse response;
            request.set_token("default_token");
            SynchronizedClosure rpc_waiter;
            stub.cancel_retrieve_task(_listen_addr, &request, &response, &rpc_waiter, NULL);
            rpc_waiter.wait();
            ASSERT_EQ(response.status().code(), AE_NOT_EXIST);
        }
        {
            aries::pb::CancelRetrieveTaskRequest request;
            aries::pb::AckResponse response;
            request.set_token("default_token");
            request.set_task_id("user_task_id");
            SynchronizedClosure rpc_waiter;
            stub.cancel_retrieve_task(_listen_addr, &request, &response, &rpc_waiter, NULL);
            rpc_waiter.wait();
            ASSERT_EQ(response.status().code(), AIE_OK);
        }
    }
        // check_leader
    {   
        {
            aries::pb::CheckLeaderRequest request;
            aries::pb::CheckLeaderResponse response;
            SynchronizedClosure rpc_waiter;
            stub.check_leader(_listen_addr, &request, &response, &rpc_waiter, NULL);
            rpc_waiter.wait();
            ASSERT_EQ(response.status().code(), AIE_INVALID_TOKEN);
        }
        {
            aries::pb::CheckLeaderRequest request;
            aries::pb::CheckLeaderResponse response;
            request.set_token("default_token");
            SynchronizedClosure rpc_waiter;
            stub.check_leader(_listen_addr, &request, &response, &rpc_waiter, NULL);
            rpc_waiter.wait();
            ASSERT_EQ(response.status().code(), AIE_OK);
        }
        
    }
        // fetch_transform_task
    {       
            // aries::pb::ListTapeNodeResponse response;
            // response.mutable_status()->set_code(aries::AIE_OK);
            // auto tapenode = response.add_tape_node_list();
            // tapenode->set_node_addr(123);
            // tapenode->add_physical_pool_names("pool01");
            // tapenode->add_physical_pool_names("pool02");
            // tapenode->set_logical_pool_name("group1");
            // tapenode = response->add_tape_node_list();
            // tapenode->set_node_addr(345);
            // tapenode->add_physical_pool_names("pool01");
            // tapenode->add_physical_pool_names("pool02");
            // tapenode->set_logical_pool_name("group2");
            // g_task_scheduler->_transform_task_scheduler.update_tape_node_info(&response);
        {
            FLAGS_enable_fetch_transform_task = false;
            aries::pb::FetchTaskRequest request;
            aries::pb::FetchTransformTaskResponse response;
            request.set_token("default_token");
            request.set_req_addr(123);
            SynchronizedClosure rpc_waiter;
            stub.fetch_transform_task(_listen_addr, &request, &response, &rpc_waiter, NULL);
            rpc_waiter.wait();
            ASSERT_EQ(response.status().code(), AIE_FAIL);
        }
        {
            FLAGS_enable_fetch_transform_task = true;
            aries::pb::FetchTaskRequest request;
            aries::pb::FetchTransformTaskResponse response;
            request.set_token("default_token");
            request.set_req_addr(789);
            SynchronizedClosure rpc_waiter;
            stub.fetch_transform_task(_listen_addr, &request, &response, &rpc_waiter, NULL);
            rpc_waiter.wait();
            ASSERT_EQ(response.status().code(), AIE_NOT_EXIST);
        }
    }
        // report_transform_task
    {   
        aries::pb::TapeTransformTaskReportRequest request;
        aries::pb::AckResponse response;
        request.set_token("default_token");
        request.set_req_addr(123);
        request.set_task_id(123);
        request.set_volume_id(123);
        SynchronizedClosure rpc_waiter;
        stub.report_transform_task(_listen_addr, &request, &response, &rpc_waiter, NULL);
        rpc_waiter.wait();
        ASSERT_EQ(response.status().code(), AIE_NOT_EXIST);
    }
        // fetch_retrieve_task
    {   
        {   
            FLAGS_enable_fetch_retrieve_task = false;
            aries::pb::FetchTaskRequest request;
            aries::pb::FetchRetrieveTaskResponse response;
            request.set_token("default_token");
            request.set_req_addr(123);
            SynchronizedClosure rpc_waiter;
            stub.fetch_retrieve_task(_listen_addr, &request, &response, &rpc_waiter, NULL);
            rpc_waiter.wait();
            ASSERT_EQ(response.status().code(), AIE_FAIL);
        }
        {   
            FLAGS_enable_fetch_retrieve_task = true;
            aries::pb::FetchTaskRequest request;
            aries::pb::FetchRetrieveTaskResponse response;
            request.set_token("default_token");
            request.set_req_addr(123);
            SynchronizedClosure rpc_waiter;
            stub.fetch_retrieve_task(_listen_addr, &request, &response, &rpc_waiter, NULL);
            rpc_waiter.wait();
            ASSERT_EQ(response.status().code(), AIE_NOT_EXIST);
        }
    }
        // report_retrieve_task
    {   
        aries::pb::TapeRetrieveTaskReportRequest request;
        aries::pb::TapeRetrieveTaskReportResponse response;
        request.set_token("default_token");
        request.set_req_addr(123);
        request.set_task_id(123);
        request.set_volume_id(123);
        request.set_task_begin_timestamp(123);
        auto blob_info = request.add_blob_list();
        blob_info->set_vbid(123);
        SynchronizedClosure rpc_waiter;
        stub.report_retrieve_task(_listen_addr, &request, &response, &rpc_waiter, NULL);
        rpc_waiter.wait();
        ASSERT_EQ(response.status().code(), AIE_NOT_EXIST);
    }
        // drop_tape_center_task
    {   
        {
            aries::pb::DropTapeTaskRequest request;
            aries::pb::AckResponse response;
            request.set_token("default_token");
            SynchronizedClosure rpc_waiter;
            stub.drop_tape_center_task(_listen_addr, &request, &response, &rpc_waiter, NULL);
            rpc_waiter.wait();
            ASSERT_EQ(response.status().code(), AIE_NOT_EXIST);
        }
        {   
            aries::pb::TapeTaskInfo task_info;
            task_info.set_task_id(123);
            g_task_manager->add_task(task_info);
            aries::pb::DropTapeTaskRequest request;
            aries::pb::AckResponse response;
            request.set_token("default_token");
            request.set_task_id(123);
            SynchronizedClosure rpc_waiter;
            stub.drop_tape_center_task(_listen_addr, &request, &response, &rpc_waiter, NULL);
            rpc_waiter.wait();
            ASSERT_EQ(response.status().code(), AIE_OK);
        }
    }
        // add_tape_center_peer
    {   
        aries::pb::AddTapeCenterPeerRequest request;
        aries::pb::AckResponse response;
        request.set_token("default_token");
        request.set_tape_center_addr(123);
        SynchronizedClosure rpc_waiter;
        stub.add_tape_center_peer(_listen_addr, &request, &response, &rpc_waiter, NULL);
        rpc_waiter.wait();
        ASSERT_EQ(response.status().code(), AIE_OK);
    }
        // drop_tape_center_peer
    {   
        aries::pb::DropTapeCenterPeerRequest request;
        aries::pb::AckResponse response;
        request.set_token("default_token");
        request.set_tape_center_addr(123);
        SynchronizedClosure rpc_waiter;
        stub.drop_tape_center_peer(_listen_addr, &request, &response, &rpc_waiter, NULL);
        rpc_waiter.wait();
        ASSERT_EQ(response.status().code(), AIE_OK);
    }
        // fetch_delete_task
    {   
        {
            FLAGS_enable_fetch_delete_task = true;
            aries::pb::FetchDeleteTaskRequest request;
            aries::pb::FetchDeleteTaskResponse response;
            request.set_token("default_token");
            SynchronizedClosure rpc_waiter;
            stub.fetch_delete_task(_listen_addr, &request, &response, &rpc_waiter, NULL);
            rpc_waiter.wait();
            ASSERT_EQ(response.status().code(), AIE_NOT_EXIST);
        }   
        {
            FLAGS_enable_fetch_delete_task = false;
            aries::pb::FetchDeleteTaskRequest request;
            aries::pb::FetchDeleteTaskResponse response;
            request.set_token("default_token");
            SynchronizedClosure rpc_waiter;
            stub.fetch_delete_task(_listen_addr, &request, &response, &rpc_waiter, NULL);
            rpc_waiter.wait();
            ASSERT_EQ(response.status().code(), AIE_FAIL);
        }
    }
        // report_delete_task
    {   
        aries::pb::TapeDeleteTaskReportRequest request;
        aries::pb::AckResponse response;
        request.set_token("default_token");
        SynchronizedClosure rpc_waiter;
        stub.report_delete_task(_listen_addr, &request, &response, &rpc_waiter, NULL);
        rpc_waiter.wait();
        ASSERT_EQ(response.status().code(), AIE_NOT_EXIST);
    }
        // fetch_check_task
    {   
        {
            FLAGS_enable_fetch_check_task = true;
            aries::pb::FetchCheckTaskRequest request;
            aries::pb::FetchCheckTaskResponse response;
            request.set_token("default_token");
            SynchronizedClosure rpc_waiter;
            stub.fetch_check_task(_listen_addr, &request, &response, &rpc_waiter, NULL);
            rpc_waiter.wait();
            ASSERT_EQ(response.status().code(), AIE_NOT_EXIST);
        }
        {
            FLAGS_enable_fetch_check_task = false;
            aries::pb::FetchCheckTaskRequest request;
            aries::pb::FetchCheckTaskResponse response;
            request.set_token("default_token");
            SynchronizedClosure rpc_waiter;
            stub.fetch_check_task(_listen_addr, &request, &response, &rpc_waiter, NULL);
            rpc_waiter.wait();
            ASSERT_EQ(response.status().code(), AIE_FAIL);
        }
    }
        // report_check_task
    {   
        aries::pb::CheckTaskReportRequest request;
        aries::pb::AckResponse response;
        request.set_token("default_token");
        request.set_req_addr(123);
        request.set_volume_id(123);
        SynchronizedClosure rpc_waiter;
        stub.report_check_task(_listen_addr, &request, &response, &rpc_waiter, NULL);
        rpc_waiter.wait();
        ASSERT_EQ(response.status().code(), AIE_OK);
    }
        // fetch_reclaim_task
    {   
        {   
            FLAGS_enable_fetch_reclaim_task = true;
            aries::pb::FetchReclaimTaskRequest request;
            aries::pb::FetchReclaimTaskResponse response;
            request.set_token("default_token");
            SynchronizedClosure rpc_waiter;
            stub.fetch_reclaim_task(_listen_addr, &request, &response, &rpc_waiter, NULL);
            rpc_waiter.wait();
            ASSERT_EQ(response.status().code(), AIE_NOT_EXIST);
        }
        {   
            FLAGS_enable_fetch_reclaim_task = false;
            aries::pb::FetchReclaimTaskRequest request;
            aries::pb::FetchReclaimTaskResponse response;
            request.set_token("default_token");
            SynchronizedClosure rpc_waiter;
            stub.fetch_reclaim_task(_listen_addr, &request, &response, &rpc_waiter, NULL);
            rpc_waiter.wait();
            ASSERT_EQ(response.status().code(), AIE_FAIL);
        }
    }
        // report_reclaim_task
    {   
        aries::pb::TapeReclaimTaskReportRequest request;
        aries::pb::AckResponse response;
        request.set_token("default_token");
        SynchronizedClosure rpc_waiter;
        stub.report_reclaim_task(_listen_addr, &request, &response, &rpc_waiter, NULL);
        rpc_waiter.wait();
        ASSERT_EQ(response.status().code(), AIE_NOT_EXIST);
    }
        // update_tape_center_task
    {   
        aries::pb::UpdateTapeCenterTaskRequest request;
        aries::pb::AckResponse response;
        request.set_token("default_token");
        SynchronizedClosure rpc_waiter;
        stub.update_tape_center_task(_listen_addr, &request, &response, &rpc_waiter, NULL);
        rpc_waiter.wait();
        ASSERT_EQ(response.status().code(), AIE_NOT_EXIST);
    }
}

}