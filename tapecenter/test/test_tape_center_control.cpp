/***************************************************************************
 * 
 * Copyright (c) 2021 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/
 
 /**
 * @file test_tape_center_control.cpp
 * <AUTHOR>
 * @date 2021/11/29 10:32:34
 * @version 1.0 
 * @brief 
 *  
 **/

#include <gtest/gtest.h>
#include <base/logging.h>
#include <baidu/rpc/server.h>
#include "baidu/inf/aries-api/common/utils.h"
#include "baidu/inf/aries-api/common/common.h"
#include "baidu/inf/aries-api/common/proto/tapecenter.pb.h"
#include "baidu/inf/aries-api/common/tape_center_tracker.h"

#include "baidu/inf/aries/common/common.h"
#include "baidu/inf/aries/tapecenter/tape_center_control.h"

namespace aries::tapecenter {

class MockFSM : public raft::StateMachine {
public:
    MockFSM() {
    }
    virtual ~MockFSM() {
    }

    virtual void on_apply(raft::Iterator& iter) {
        for (; iter.valid(); iter.next()) {
            ::baidu::rpc::ClosureGuard guard(iter.done());
        }
    }

    virtual void on_shutdown() {
        delete this;
    }
};

class TapeCenterControlTests : public ::testing::Test {
public:
    TapeCenterControlTests() {}
    virtual ~TapeCenterControlTests() {}
    void SetUp() {}
    void TearDown() {}
};

TEST_F(TapeCenterControlTests, test_node_apply) {
    baidu::rpc::Server server;
    int ret = raft::add_service(&server, "0.0.0.0:60001");
    ASSERT_EQ(0, ret);
    ASSERT_EQ(0, server.Start("0.0.0.0:60001", NULL));

    raft::NodeOptions options;
    options.fsm = new MockFSM();
    options.log_uri = "local://./data/log";
    options.stable_uri = "local://./data/stable";
    options.snapshot_uri = "local://./data/snapshot";

    raft::Node* node = new raft::Node("unittest", raft::PeerId(base::EndPoint(base::my_ip(), 60001), 0));
    ASSERT_EQ(0, node->init(options));

    g_tape_center_control->_node = node;
    {
        pb::TapeTaskInfo task;
        uint64_t task_id = common::generate_tape_task_id(1234, TAPE_TRANSFORM_TASK);
        task.set_task_id(task_id);
        task.set_task_state(TAPE_TASK_WAITING);
        g_tape_center_control->add_task(task);

        task.set_task_state(TAPE_TASK_TRANSFORM_RUNNING);
        g_tape_center_control->update_task(task);

        g_tape_center_control->finish_task(task);

    }
    delete node;
    server.Stop(100);
}

TEST_F(TapeCenterControlTests, test_init) {
    common::FLAGS_tape_center_address = "123";
    auto ret = g_tape_center_control->init();
    ASSERT_NE(ret, 0);
    common::FLAGS_tape_center_address = "http://127.0.0.1:1234";
    ret = g_tape_center_control->init();
    ASSERT_NE(ret, 0);

    common::FLAGS_port = 60001;
    ::aries::common::init_local_addr();
    baidu::rpc::Server server;
    ret = raft::add_service(&server, "0.0.0.0:60001");
    ASSERT_EQ(0, ret);
    ASSERT_EQ(0, server.Start("0.0.0.0:60001", nullptr));
    common::FLAGS_tape_center_address = "127.0.0.1:60001";
    ret = g_tape_center_control->init();
    ASSERT_EQ(ret, 0);
    server.Stop(100);
}

TEST_F(TapeCenterControlTests, add_and_drop_peer) {
    common::init_local_addr();
    ::system("rm -rf ./data1");
    ::system("rm -rf ./data2");
    ::system("rm -rf ./data3");

    std::vector<raft::PeerId> peers;
    base::EndPoint peer = common::get_local_addr();;
    peer.port = 60001;
    peers.push_back(raft::PeerId(peer));

    peer.port = 60002;
    peers.push_back(raft::PeerId(peer));

    baidu::rpc::Server server1;
    int ret = raft::add_service(&server1, "0.0.0.0:60001");
    ASSERT_EQ(0, ret);
    ASSERT_EQ(0, server1.Start("0.0.0.0:60001", NULL));
    raft::NodeOptions options;
    options.fsm = new MockFSM();
    options.initial_conf = raft::Configuration(peers);
    options.log_uri = "local://./data1/log";
    options.stable_uri = "local://./data1/stable";
    options.snapshot_uri = "local://./data1/snapshot";
    peer.port = 60001;
    raft::Node* node = new raft::Node("unittest1", raft::PeerId(peer, 0));
    ASSERT_EQ(0, node->init(options));

    baidu::rpc::Server server2;
    ret = raft::add_service(&server2, "0.0.0.0:60002");
    ASSERT_EQ(0, ret);
    ASSERT_EQ(0, server2.Start("0.0.0.0:60002", NULL));
    raft::NodeOptions options2;
    options2.fsm = new MockFSM();
    options2.initial_conf = raft::Configuration(peers);
    options2.log_uri = "local://./data2/log";
    options2.stable_uri = "local://./data2/stable";
    options2.snapshot_uri = "local://./data2/snapshot";
    peer.port = 60002;
    raft::Node* node2 = new raft::Node("unittest1", raft::PeerId(peer, 0));
    ASSERT_EQ(0, node2->init(options2));

    baidu::rpc::Server server3;
    ret = raft::add_service(&server3, "0.0.0.0:60003");
    ASSERT_EQ(0, ret);
    ASSERT_EQ(0, server3.Start("0.0.0.0:60003", NULL));
    raft::NodeOptions options3;
    options3.fsm = new MockFSM();
    options3.initial_conf = raft::Configuration(peers);
    options3.log_uri = "local://./data3/log";
    options3.stable_uri = "local://./data3/stable";
    options3.snapshot_uri = "local://./data3/snapshot";
    peer.port = 60003;
    raft::Node* node3 = new raft::Node("unittest1", raft::PeerId(peer, 0));
    ASSERT_EQ(0, node3->init(options3));

    sleep(10);
    peers.clear();
    raft::Node* leader_node = nullptr;
    if (node->is_leader()) {
        leader_node = node;
    }
    if (node2->is_leader()) {
        leader_node = node2;
    }
    ASSERT_TRUE(leader_node != nullptr);
    leader_node->list_peers(&peers);
    ASSERT_EQ(2, peers.size());

    // test add_tape_center_peer
    g_tape_center_control->_node = leader_node;
    aries::pb::AddTapeCenterPeerRequest request;
    peer.port = 60003;
    request.set_tape_center_addr(common::endpoint2int(peer));
    g_tape_center_control->add_tape_center_peer(&request);
    sleep(3);
    peers.clear();
    leader_node->list_peers(&peers);
    ASSERT_EQ(3, peers.size());

    // test drop_tape_center_peer
    aries::pb::DropTapeCenterPeerRequest drop_request;
    drop_request.set_tape_center_addr(common::endpoint2int(peer));
    g_tape_center_control->drop_tape_center_peer(&drop_request);
    sleep(3);
    peers.clear();
    leader_node->list_peers(&peers);
    ASSERT_EQ(2, peers.size());

    delete node;
    server1.Stop(100);
    server2.Stop(100);
    server3.Stop(100);
}

}
/* vim: set ts=4 sw=4 sts=4 tw=100 */
