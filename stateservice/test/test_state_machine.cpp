/**
 * ARIES - A Reliable and Impressive EB-level Storage.
 * Copyright (c) 2022 Baidu.com, Inc. All Rights Reserved.
 *
 */
#include <fstream>
#include <sstream>
#include <gtest/gtest.h>
#include <base/logging.h>
#include "baidu/inf/aries-api/common/types.h"
#include "baidu/inf/aries-api/common/proto/stateservice.pb.h"
#include "baidu/inf/aries/stateservice/test/ut_common.h"
#include "baidu/inf/aries/stateservice/stateservice_state_machine.h"

namespace aries::stateservice {
class StateMachineTests : public::testing::Test {
protected:
    StateMachineTests() {
        FLAGS_stateservice_raft_group_count = 1;
        common::FLAGS_port = 60131;
        common::init_local_addr();
        auto local = common::get_local_addr();
        FLAGS_stateservice_address = common::endpoint2str(local);
        if (raft::add_service(&_server, local) != 0) {
            LOG(FATAL) << "fail to add raft service";
        }
        baidu::rpc::ServerOptions options;
        options.idle_timeout_sec = 1;
        if (_server.Start(common::FLAGS_port, &options) != 0) {
            LOG(FATAL) << "rpc server fail to start server at port " << common::FLAGS_port;
        }
    }
    virtual ~StateMachineTests() {
        _server.Stop(200);
        _server.Join();
    }

    void SetUp() {
        _ins = generate_raft_instance(0);
        _ins->start();
        _state_machine = _ins->control()->_fsm.get();
        _control = _ins->control();
        _volume_manager = _ins->meta_data()->volume_manager();
        _space_volume_session_manager = _ins->meta_data()->space_volume_session_manager();
    }

    void TearDown() {
        _ins->stop();
    }
private:
    baidu::rpc::Server _server;
    std::shared_ptr<StateServiceRaftInstance> _ins;
    StateServiceStateMachine* _state_machine;
    StateServiceControl* _control;
    VolumeManager* _volume_manager;
    SpaceVolumeSessionManager* _space_volume_session_manager;
};

TEST_F(StateMachineTests, test_open_archive_volume) {
    {
        aries::pb::AllocateVolumeRequest request;
        aries::pb::AllocateVolumeResponse response;
        request.set_token("default_token");
        request.set_volume_id_hint(1);
        request.set_req_addr(1234);
        request.set_lease_timeout_timestamp(1638866860);

        LogClosure<aries::pb::AllocateVolumeRequest, aries::pb::AllocateVolumeResponse>
            closure(nullptr, &request, &response);
        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);
        base::Status st;
        // err iobuf
        base::IOBuf err_data = prepare_err_data();
        st = _state_machine->do_open_archive_volume(err_data, &closure, 1);
        ASSERT_EQ(st.error_code(), AIE_FAIL);
    }
    {
        // volume not exist
        aries::pb::AllocateVolumeRequest request;
        aries::pb::AllocateVolumeResponse response;
        request.set_token("default_token");
        request.set_volume_id_hint(12345);
        request.set_req_addr(1234);
        request.set_lease_timeout_timestamp(1638866860);

        LogClosure<aries::pb::AllocateVolumeRequest, aries::pb::AllocateVolumeResponse>
            closure(nullptr, &request, &response);
        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);
        base::Status st;
        st = _state_machine->do_open_archive_volume(data, &closure, 1);
        ASSERT_EQ(st.error_code(), AIE_NOT_EXIST);
    }
    {
        // state is wrong
        auto volume = _volume_manager->get_volume(1);
        volume->set_volume_session_state(VOLUME_SESSION_STATE_NONE);
        aries::pb::AllocateVolumeRequest request;
        aries::pb::AllocateVolumeResponse response;
        request.set_token("default_token");
        request.set_volume_id_hint(1);
        request.set_req_addr(1234);
        request.set_lease_timeout_timestamp(1638866860);

        LogClosure<aries::pb::AllocateVolumeRequest, aries::pb::AllocateVolumeResponse>
            closure(nullptr, &request, &response);
        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);
        base::Status st;
        st = _state_machine->do_open_archive_volume(data, &closure, 1);
        ASSERT_EQ(st.error_code(), AIE_INVALID);
    }
    {
        // succ
        auto volume = _volume_manager->get_volume(2);
        volume->set_volume_session_state(VOLUME_SESSION_STATE_CLOSED);
        volume->set_max_volume_session_version(0);
        volume->set_append_logical_size(0);
        EXPECT_EQ(_space_volume_session_manager->_space_volume_map[volume->space_name()]
            .opened_volumes.size(), 1);
        aries::pb::AllocateVolumeRequest request;
        aries::pb::AllocateVolumeResponse response;
        request.set_token("default_token");
        request.set_volume_id_hint(2);
        request.set_req_addr(1234);
        request.set_lease_timeout_timestamp(1638866860);

        LogClosure<aries::pb::AllocateVolumeRequest, aries::pb::AllocateVolumeResponse>
            closure(nullptr, &request, &response);
        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);
        base::Status st;
        st = _state_machine->do_open_archive_volume(data, &closure, 1);
        EXPECT_EQ(volume->leases_size(), 1);
        EXPECT_EQ(volume->get_lease(1234).has_value(), true);
        EXPECT_EQ(volume->max_volume_session_version(), 1);
        EXPECT_EQ(response.status().code(), AIE_OK);
        EXPECT_EQ(response.session_version(), 1);
        EXPECT_EQ(response.volume_id(), 2);
        EXPECT_EQ(response.logical_free_size(), volume->append_logical_total_size());
        EXPECT_EQ(response.lease_timestamp(), 1638866860);
        EXPECT_EQ(st.error_code(), AIE_OK);
        EXPECT_EQ(_space_volume_session_manager->_space_volume_map[volume->space_name()]
            .opened_volumes.size(), 2);
    }
}

TEST_F(StateMachineTests, test_close_archive_volume) {
    {
        // err iobuf
        base::Status st;
        base::IOBuf err_data = prepare_err_data();
        st = _state_machine->do_close_archive_volume(err_data, NULL, 1);
        ASSERT_EQ(st.error_code(), AIE_FAIL);
    }
    {
        // volume not exist
        aries::pb::ReleaseVolumeRequest request;
        request.set_token("default_token");
        request.set_volume_id(12345);

        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);
        base::Status st;
        st = _state_machine->do_close_archive_volume(data, NULL, 1);
        ASSERT_EQ(st.error_code(), AIE_NOT_EXIST);
    }
    {
        // state is wrong
        auto volume = _volume_manager->get_volume(1);
        aries::pb::ReleaseVolumeRequest request;
        request.set_token("default_token");
        request.set_volume_id(1);
        request.set_req_addr(1234);
        volume->set_volume_session_state(VOLUME_SESSION_STATE_NONE);

        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);
        base::Status st;
        st = _state_machine->do_close_archive_volume(data, NULL, 1);
        ASSERT_EQ(st.error_code(), AIE_INVALID);
    }
    {
        // closed
        auto volume = _volume_manager->get_volume(2);
        aries::pb::ReleaseVolumeRequest request;
        request.set_token("default_token");
        request.set_volume_id(2);
        request.set_req_addr(1234);
        request.set_lease_timeout_timestamp(12345);
        volume->set_volume_session_state(VOLUME_SESSION_STATE_CLOSED);

        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);
        base::Status st;
        st = _state_machine->do_close_archive_volume(data, NULL, 1);
        ASSERT_EQ(st.error_code(), AIE_OK);
    }
    {
        // no lease
        auto volume = _volume_manager->get_volume(2);
        aries::pb::ReleaseVolumeRequest request;
        request.set_token("default_token");
        request.set_volume_id(2);
        request.set_req_addr(1234);
        request.set_lease_timeout_timestamp(12345);
        volume->set_volume_session_state(VOLUME_SESSION_STATE_OPENED);

        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);
        base::Status st;
        st = _state_machine->do_close_archive_volume(data, NULL, 1);
        ASSERT_EQ(st.error_code(), AIE_OK);
    }
    {
        // timestamp wrong
        auto volume = _volume_manager->get_volume(2);
        aries::pb::ReleaseVolumeRequest request;
        request.set_token("default_token");
        request.set_volume_id(2);
        request.set_req_addr(1234);
        request.set_lease_timeout_timestamp(12345);
        volume->set_volume_session_state(VOLUME_SESSION_STATE_OPENED);
        VolumeSessionLease lease1;
        lease1.volume_session_version = 1;
        lease1.volume_session_lease_timestamp = 23456;
        lease1.volume_session_lessee_proxy_address = 1234;
        volume->upsert_lease(lease1);

        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);
        base::Status st;
        st = _state_machine->do_close_archive_volume(data, NULL, 1);
        ASSERT_EQ(st.error_code(), AIE_FAIL);
    }
    {
        // succ
        auto volume = _volume_manager->get_volume(3);
        aries::pb::ReleaseVolumeRequest request;
        request.set_token("default_token");
        request.set_volume_id(3);
        request.set_req_addr(1234);
        request.set_lease_timeout_timestamp(12345);
        volume->set_volume_session_state(VOLUME_SESSION_STATE_OPENED);
        VolumeSessionLease lease1;
        lease1.volume_session_version = 1;
        lease1.volume_session_lease_timestamp = 12345;
        lease1.volume_session_lessee_proxy_address = 1234;
        volume->upsert_lease(lease1);
        _space_volume_session_manager->_space_volume_map[volume->space_name()]
            .closed_volumes.erase(3);
        _space_volume_session_manager->_space_volume_map[volume->space_name()]
            .opened_volumes.insert(3);
        EXPECT_EQ(_space_volume_session_manager->_space_volume_map[volume->space_name()]
            .closed_volumes.size(), 2);
        EXPECT_EQ(_space_volume_session_manager->_space_volume_map[volume->space_name()]
            .opened_volumes.size(), 2);

        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);
        base::Status st;
        st = _state_machine->do_close_archive_volume(data, NULL, 1);
        EXPECT_EQ(st.error_code(), AIE_OK);
        EXPECT_EQ(volume->volume_session_state(), VOLUME_SESSION_STATE_CLOSED);
        EXPECT_EQ(volume->leases_size(), 0);
        EXPECT_EQ(_space_volume_session_manager->_space_volume_map[volume->space_name()]
            .closed_volumes.size(), 3);
        EXPECT_EQ(_space_volume_session_manager->_space_volume_map[volume->space_name()]
            .opened_volumes.size(), 1);
    }
}

TEST_F(StateMachineTests, test_update_archive_volume_size) {
    {
        // err iobuf
        base::Status st;
        base::IOBuf err_data = prepare_err_data();
        st = _state_machine->do_update_archive_volume_size(err_data, NULL, 1);
        ASSERT_EQ(st.error_code(), AIE_FAIL);
    }
    {
        // volume not exist
        aries::pb::UpdateVolumeSizeRequest request;
        request.set_token("default_token");
        request.set_volume_id(12345);

        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);
        base::Status st;
        st = _state_machine->do_update_archive_volume_size(data, NULL, 1);
        ASSERT_EQ(st.error_code(), AIE_NOT_EXIST);
    }
    {
        // succ
        auto volume = _volume_manager->get_volume(3);
        aries::pb::UpdateVolumeSizeRequest request;
        request.set_token("default_token");
        request.set_volume_id(3);
        request.set_logical_size(234567);
        volume->set_append_logical_size(123);

        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);
        base::Status st;
        st = _state_machine->do_update_archive_volume_size(data, NULL, 1);
        EXPECT_EQ(st.error_code(), AIE_OK);
        EXPECT_EQ(volume->append_logical_size(), 234567);
    }
}

TEST_F(StateMachineTests, test_renew_volume_session_lease) {
    {
        aries::pb::RenewVolumeLeaseRequest request;
        aries::pb::RenewVolumeLeaseResponse response;
        request.set_token("default_token");
        request.set_req_addr(1234);
        request.set_lease_timeout_timestamp(1638866860);
        request.add_volume_id(1);

        LogClosure<aries::pb::RenewVolumeLeaseRequest, aries::pb::RenewVolumeLeaseResponse>
            closure(nullptr, &request, &response);
        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);
        base::Status st;

        // err iobuf
        base::IOBuf err_data = prepare_err_data();
        st = _state_machine->do_renew_volume_session_lease(err_data, &closure, 1);
        ASSERT_EQ(st.error_code(), AIE_FAIL);
    }
    {
        aries::pb::RenewVolumeLeaseRequest request;
        aries::pb::RenewVolumeLeaseResponse response;
        request.set_token("default_token");
        request.set_req_addr(1234);
        request.set_lease_timeout_timestamp(1638866860);
        // state wrong
        request.add_volume_id(2); 
        auto volume2 = _volume_manager->get_volume(2);
        volume2->set_volume_session_state(VOLUME_SESSION_STATE_NONE);
        // lease not exist
        request.add_volume_id(3);
        auto volume3 = _volume_manager->get_volume(3);
        volume3->set_volume_session_state(VOLUME_SESSION_STATE_OPENED);
        VolumeSessionLease lease0;
        lease0.volume_session_version = 1;
        lease0.volume_session_lease_timestamp = 1638866800;
        lease0.volume_session_lessee_proxy_address = 1235;
        volume3->upsert_lease(lease0);
        // ok
        request.add_volume_id(4);
        auto volume4 = _volume_manager->get_volume(4);
        volume4->set_volume_session_state(VOLUME_SESSION_STATE_OPENED);
        VolumeSessionLease lease1;
        lease1.volume_session_version = 1;
        lease1.volume_session_lease_timestamp = 1638866800;
        lease1.volume_session_lessee_proxy_address = 1234;
        volume4->upsert_lease(lease1);
        // not exist
        request.add_volume_id(12345);

        LogClosure<aries::pb::RenewVolumeLeaseRequest, aries::pb::RenewVolumeLeaseResponse>
            closure(nullptr, &request, &response);
        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);
        base::Status st;
        st = _state_machine->do_renew_volume_session_lease(data, &closure, 1);
        ASSERT_EQ(st.error_code(), AIE_OK);
        EXPECT_EQ(volume3->_leases[1235].volume_session_lease_timestamp, 1638866800);
        EXPECT_EQ(volume4->_leases[1234].volume_session_lease_timestamp, 1638866860);
        EXPECT_EQ(response.master_volume_sessions_size(), 4);
        for (auto i = 0; i < response.master_volume_sessions_size(); i++) {
            auto& volume_session = response.master_volume_sessions(i);
            if (volume_session.volume_id() == 12345) {
                EXPECT_EQ(volume_session.status().code(), AIE_NOT_EXIST);
            }
            if (volume_session.volume_id() == 2) {
                EXPECT_EQ(volume_session.status().code(), AIE_INVALID);
            }
            if (volume_session.volume_id() == 3) {
                EXPECT_EQ(volume_session.status().code(), AIE_INVALID);
            }
            if (volume_session.volume_id() == 4) {
                EXPECT_EQ(volume_session.status().code(), AIE_OK);
                EXPECT_EQ(volume_session.lease_timestamp(), 1638866860);
            }
        }
    }
}

TEST_F(StateMachineTests, test_init_archive_volume) {
    {
        // err iobuf
        base::Status st;
        base::IOBuf err_data = prepare_err_data();
        st = _state_machine->do_init_archive_volume(err_data, NULL, 1);
        ASSERT_EQ(st.error_code(), AIE_FAIL);
    }
    {
        // volume exist
        aries::pb::InitVolumeRequest request;
        aries::pb::AckResponse response;
        request.set_token("default_token");
        request.set_volume_id(1);
        request.set_req_addr(1234);
        request.set_space_name("test_space_1");
        request.set_create_time(33333);
        request.set_append_logical_total_size(10000);

        LogClosure<aries::pb::InitVolumeRequest, aries::pb::AckResponse>
            closure(nullptr, &request, &response);
        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);
        base::Status st;
        st = _state_machine->do_init_archive_volume(data, &closure, 1);
        ASSERT_EQ(st.error_code(), AIE_EXIST);
    }
    {
        // succ
        aries::pb::InitVolumeRequest request;
        aries::pb::AckResponse response;
        request.set_token("default_token");
        request.set_volume_id(10);
        request.set_req_addr(1234);
        request.set_space_name("test_space_2");
        request.set_create_time(33333);
        request.set_append_logical_total_size(10000);

        LogClosure<aries::pb::InitVolumeRequest, aries::pb::AckResponse>
            closure(nullptr, &request, &response);
        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);
        base::Status st;
        st = _state_machine->do_init_archive_volume(data, &closure, 1);

        auto volume = _volume_manager->get_volume(10);
        EXPECT_EQ(volume->append_logical_total_size(), 10000);
        EXPECT_EQ(volume->create_time(), 33333);
        EXPECT_EQ(volume->max_volume_session_version(), 0);
        EXPECT_EQ(st.error_code(), AIE_OK);
        EXPECT_EQ(_space_volume_session_manager->_space_volume_map[volume->space_name()]
            .new_volumes.size(), 1);
    }
}

TEST_F(StateMachineTests, test_seal_archive_volume) {
    {
        // err iobuf
        base::Status st;
        base::IOBuf err_data = prepare_err_data();
        st = _state_machine->do_seal_archive_volume(err_data, NULL, 1);
        ASSERT_EQ(st.error_code(), AIE_FAIL);
    }
    {
        // volume not exist
        aries::pb::SealVolumeRequest request;
        request.set_token("default_token");
        request.set_volume_id(12345);

        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);
        base::Status st;
        st = _state_machine->do_seal_archive_volume(data, NULL, 1);
        ASSERT_EQ(st.error_code(), AIE_NOT_EXIST);
    }
    {
        // state is wrong
        auto volume = _volume_manager->get_volume(1);
        aries::pb::SealVolumeRequest request;
        request.set_token("default_token");
        request.set_volume_id(1);
        request.set_finish_seal(true);
        volume->set_volume_seal_state(VOLUME_SEAL_STATE_NONE);

        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);
        base::Status st;
        st = _state_machine->do_seal_archive_volume(data, NULL, 1);
        ASSERT_EQ(st.error_code(), AIE_FAIL);
    }
    {
        // state is sealing
        auto volume = _volume_manager->get_volume(1);
        aries::pb::SealVolumeRequest request;
        request.set_token("default_token");
        request.set_volume_id(1);
        request.set_finish_seal(false);
        volume->set_volume_seal_state(VOLUME_SEAL_STATE_SEALING);

        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);
        base::Status st;
        st = _state_machine->do_seal_archive_volume(data, NULL, 1);
        ASSERT_EQ(st.error_code(), AIE_OK);
    }
    {
        // succ(sealing)
        auto volume = _volume_manager->get_volume(3);
        aries::pb::SealVolumeRequest request;
        request.set_token("default_token");
        request.set_volume_id(3);
        request.set_finish_seal(false);
        volume->set_volume_seal_state(VOLUME_SEAL_STATE_NONE);
        volume->set_append_logical_size(123);
        EXPECT_EQ(_space_volume_session_manager->_space_volume_map[volume->space_name()]
            .sealing_volumes.size(), 1);

        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);
        base::Status st;
        st = _state_machine->do_seal_archive_volume(data, NULL, 1);
        EXPECT_EQ(st.error_code(), AIE_OK);
        EXPECT_EQ(volume->volume_seal_state(), VOLUME_SEAL_STATE_SEALING);
        EXPECT_EQ(volume->is_sealed(), false);
        EXPECT_EQ(_space_volume_session_manager->_space_volume_map[volume->space_name()]
            .sealing_volumes.size(), 2);
    }
    {
        // succ(sealed)
        auto volume = _volume_manager->get_volume(3);
        aries::pb::SealVolumeRequest request;
        request.set_token("default_token");
        request.set_volume_id(3);
        request.set_finish_seal(true);
        volume->set_volume_seal_state(VOLUME_SEAL_STATE_SEALING);
        volume->set_append_logical_size(123);
        EXPECT_EQ(_space_volume_session_manager->_space_volume_map[volume->space_name()]
            .sealing_volumes.size(), 2);

        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);
        base::Status st;
        st = _state_machine->do_seal_archive_volume(data, NULL, 1);
        EXPECT_EQ(st.error_code(), AIE_OK);
        EXPECT_EQ(volume->volume_seal_state(), VOLUME_SEAL_STATE_SEALED);
        EXPECT_EQ(volume->is_sealed(), true);
        EXPECT_EQ(_space_volume_session_manager->_space_volume_map[volume->space_name()]
            .sealing_volumes.size(), 1);
    }
}

}
