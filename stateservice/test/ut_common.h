/***************************************************************************
 * 
 * Copyright (c) 2022 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/
 
#pragma once

#include <vector>
#include <base/iobuf.h>
#include "baidu/inf/aries-api/common/types.h"
#include "baidu/inf/aries-api/common/rpc_stub.h"
#include "baidu/inf/aries-api/common/proto/stateservice.pb.h"
#include "baidu/inf/aries/stateservice/flags.h"
#include "baidu/inf/aries/stateservice/stateservice_raft_instance.h"
#include "baidu/inf/aries/stateservice/stateservice_control.h"

namespace aries::stateservice {
    
inline std::shared_ptr<StateServiceRaftInstance> generate_raft_instance(uint64_t group_id) {
    std::shared_ptr<StateServiceRaftInstance> ins = std::make_shared<StateServiceRaftInstance>(group_id);
    ins->meta_data()->set_raft_index(123);
    pb::StateServiceVolumeInfo volume_info;
    volume_info.set_volume_id(1);
    volume_info.set_space_name("test_space1");
    volume_info.set_create_time(1234);
    volume_info.set_append_logical_total_size(10000);
    volume_info.set_append_logical_size(0);
    volume_info.set_volume_session_state(VOLUME_SESSION_STATE_CLOSED);
    volume_info.set_volume_seal_state(VOLUME_SEAL_STATE_NONE);
    volume_info.set_close_timestamp(0);
    std::shared_ptr<Volume> volume = std::shared_ptr<Volume>(Volume::deserialize(volume_info));
    ins->meta_data()->add_volume(volume);
    volume_info.set_volume_id(2);
    volume_info.set_create_time(2222);
    volume_info.set_max_volume_session_version(2);
    volume_info.set_close_timestamp(12345);
    volume = std::shared_ptr<Volume>(Volume::deserialize(volume_info));
    ins->meta_data()->add_volume(volume);
    volume_info.set_volume_id(3);
    volume_info.set_create_time(1234);
    volume_info.set_max_volume_session_version(3);
    volume_info.set_append_logical_size(11000);
    volume_info.set_close_timestamp(123);
    volume = std::shared_ptr<Volume>(Volume::deserialize(volume_info));
    ins->meta_data()->add_volume(volume);
    volume_info.set_volume_id(4);
    volume_info.set_create_time(1234);
    volume_info.set_append_logical_size(3000);
    volume_info.set_volume_seal_state(VOLUME_SEAL_STATE_SEALED);
    volume_info.set_close_timestamp(2222);
    volume = std::shared_ptr<Volume>(Volume::deserialize(volume_info));
    ins->meta_data()->add_volume(volume);
    volume_info.set_volume_id(5);
    volume_info.set_create_time(1234);
    volume_info.set_append_logical_size(0);
    volume_info.set_volume_session_state(VOLUME_SESSION_STATE_OPENED);
    volume_info.set_volume_seal_state(VOLUME_SEAL_STATE_NONE);
    volume_info.set_close_timestamp(0);
    auto lease_info = volume_info.add_lease_infos();
    lease_info->set_volume_session_version(3);
    volume = std::shared_ptr<Volume>(Volume::deserialize(volume_info));
    ins->meta_data()->add_volume(volume);
    volume_info.clear_lease_infos();
    volume_info.set_volume_id(6);
    volume_info.set_append_logical_size(0);
    volume_info.set_volume_session_state(VOLUME_SESSION_STATE_CLOSED);
    volume_info.set_close_timestamp(0);
    volume = std::shared_ptr<Volume>(Volume::deserialize(volume_info));
    ins->meta_data()->add_volume(volume);
    volume_info.set_volume_id(7);
    volume_info.set_append_logical_size(0);
    volume_info.set_volume_session_state(VOLUME_SESSION_STATE_CLOSED);
    volume_info.set_volume_seal_state(VOLUME_SEAL_STATE_SEALING);
    volume_info.set_close_timestamp(0);
    volume = std::shared_ptr<Volume>(Volume::deserialize(volume_info));
    ins->meta_data()->add_volume(volume);
    return ins;
}

inline base::IOBuf prepare_err_data() {
    base::IOBuf data;
    data = "some thing...}";
    return data;
}

struct MockRaftInstances {
    MockRaftInstances() {
        for (uint64_t i = 0; i < FLAGS_stateservice_raft_group_count; i++) {
            raft_instances.push_back(generate_raft_instance(i));
            raft_instances_ptrs.push_back(raft_instances[i].get());
        }
    }
    void start() {
        for (auto& ins : raft_instances) {
            ins->start();
        }
    }
    void stop() {
        for (auto& ins : raft_instances) {
            ins->stop();
        }
    }
    void wait_primary() {
        for (auto& ins : raft_instances) {
            while (!ins->control()->is_leader()) {
                LOG(NOTICE) << "leader:" << common::endpoint2str(ins->control()->get_raft_leader());
                sleep(1);
            }
        }
    }

    template<typename T>
    void replace_control() {
        for (auto& ins : raft_instances) {
            ins->_stateservice_control = std::make_unique<T>(ins.get());
        }
    }

    std::vector<std::shared_ptr<StateServiceRaftInstance>> raft_instances;
    std::vector<StateServiceRaftInstance*> raft_instances_ptrs;
};

}

/* vim: set ts=4 sw=4 sts=4 tw=100 */
