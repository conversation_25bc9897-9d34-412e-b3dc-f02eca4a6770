/***************************************************************************
 * 
 * Copyright (c) 2022 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/
 
#pragma once

#include <atomic>
#include <memory>
#include <raft/raft.h>

#include "baidu/inf/aries-api/common/lock.h"
#include "baidu/inf/aries-api/common/proto/enum.pb.h"
#include "baidu/inf/aries-api/common/proto/stateservice.pb.h"
#include "baidu/inf/aries/stateservice/common.h"
#include "baidu/inf/aries/stateservice/stateservice_raft_instance.h"


namespace aries::stateservice {

class StateServiceRaftInstance;

class StateServiceStateMachine : public raft::StateMachine {
public:
    StateServiceStateMachine(StateServiceRaftInstance* raft_instance) :
        _raft_ins(raft_instance), _taking_snapshot(false) {}
    virtual ~StateServiceStateMachine();

    // raft fsm interface
    void on_apply(raft::Iterator& iter);
    void on_leader_start();
    void on_leader_stop();
    void on_shutdown();
    void on_start_following(const ::raft::LeaderChangeContext& ctx);
    void on_error(const ::raft::Error& e);

    inline bool taking_snapshot() {
        return _taking_snapshot;
    }

private:
    virtual int on_snapshot_load(::raft::SnapshotReader* reader);
    virtual void on_snapshot_save(::raft::SnapshotWriter* writer,
                                  ::raft::Closure* done);
    base::Status on_apply_do(const StateServiceLogType& type,
                     const base::IOBuf& log_data,
                     raft::Closure* done);
    base::Status do_open_archive_volume(const ::base::IOBuf& msg,
        LogClosure<aries::pb::AllocateVolumeRequest, aries::pb::AllocateVolumeResponse>* done,
        uint64_t raft_index);
    base::Status do_close_archive_volume(const ::base::IOBuf& msg,
        LogClosure<aries::pb::ReleaseVolumeRequest, aries::pb::AckResponse>* done,
        uint64_t raft_index);
    base::Status do_update_archive_volume_size(const ::base::IOBuf& msg,
        LogClosure<aries::pb::UpdateVolumeSizeRequest, aries::pb::AckResponse>* done,
        uint64_t raft_index);
    base::Status do_renew_volume_session_lease(const ::base::IOBuf& msg,
        LogClosure<aries::pb::RenewVolumeLeaseRequest, aries::pb::RenewVolumeLeaseResponse>* done,
        uint64_t raft_index);
    base::Status do_init_archive_volume(const ::base::IOBuf& msg,
        LogClosure<aries::pb::InitVolumeRequest, aries::pb::AckResponse>* done,
        uint64_t raft_index);
    base::Status do_seal_archive_volume(const ::base::IOBuf& msg,
        LogClosure<aries::pb::SealVolumeRequest, aries::pb::AckResponse>* done,
        uint64_t raft_index);
private:

private:
    common::MutexLock _mutex;
    StateServiceRaftInstance* _raft_ins;
    std::atomic<bool> _taking_snapshot;
    std::vector<std::thread> _scheduler_join_threads;
};
}

/* vim: set ts=4 sw=4 sts=4 tw=100 */
