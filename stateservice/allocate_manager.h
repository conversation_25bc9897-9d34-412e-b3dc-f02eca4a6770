/***************************************************************************
 * 
 * Copyright (c) 2022 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/
 
#pragma once
#include <cassert>
#include "baidu/inf/aries/stateservice/stateservice_raft_instance.h"

namespace aries::stateservice {

class StateServiceRaftInstance;

class AllocateManager {
public:
    AllocateManager() {}
    ~AllocateManager() {
    }
    void init(std::vector<StateServiceRaftInstance*> instances) {
        assert(instances.size() == FLAGS_stateservice_raft_group_count);
        _raft_ins = instances;
    }
    void allocate(const ::aries::pb::AllocateVolumeRequest* req,
        ::aries::pb::AllocateVolumeResponse* res);
private:
    struct AllocateTarget {
        uint64_t group_id;
        bool local = false;
        base::EndPoint addr;
    };
private:
    void allocate_volume(const ::aries::pb::AllocateVolumeRequest* req,
        ::aries::pb::AllocateVolumeResponse* res, StateServiceRaftInstance* raft_ins);
    AllocateTarget choose_target(const std::string& space_name, uint64_t volume_id_hint);
private:
    std::vector<StateServiceRaftInstance*> _raft_ins;
};
}

/* vim: set ts=4 sw=4 sts=4 tw=100 */
