/***************************************************************************
 * 
 * Copyright (c) 2022 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/

#include "baidu/inf/aries/stateservice/manage_service_impl.h"

#include "baidu/inf/aries/stateservice/common.h"
#include "baidu/inf/aries/stateservice/flags.h"
#include "baidu/inf/aries-api/common/types.h"
#include "baidu/inf/aries-api/common/common.h"
#include "baidu/inf/aries-api/common/utils.h"

namespace aries::stateservice {

common::BvarPerSecondWithTag<int64_t> g_bvar_release_volume("stateservice", "release_volume");
common::BvarPerSecondWithTag<int64_t> g_bvar_seal_volume("stateservice", "seal_volume");

void StateServiceManageServiceImpl::allocate_volume(::google::protobuf::RpcController* controller,
                            const ::aries::pb::AllocateVolumeRequest* request,
                            ::aries::pb::AllocateVolumeResponse* response,
                            ::google::protobuf::Closure* done) {
    baidu::rpc::Controller* cntl = (baidu::rpc::Controller*)controller;
    baidu::rpc::ClosureGuard done_guard(done);
    uint64_t log_id = request->log_id();
    base::EndPoint remote_addr = cntl->remote_side();
    auto status = response->mutable_status();

    do {
        if (!check_token(request, response)) {
            break;
        }
        _allocate_manager->allocate(request, response);
    } while (0);
    SERVICE_LOG(NOTICE);
}

void StateServiceManageServiceImpl::release_volume(::google::protobuf::RpcController* controller,
        const ::aries::pb::ReleaseVolumeRequest* request,
        ::aries::pb::AckResponse* response,
        ::google::protobuf::Closure* done) {
    baidu::rpc::Controller* cntl = (baidu::rpc::Controller*)controller;
    baidu::rpc::ClosureGuard done_guard(done);
    uint64_t log_id = request->log_id();
    base::EndPoint remote_addr = cntl->remote_side();
    auto status = response->mutable_status();

    do {
        if (!check_token(request, response)) {
            break;
        }
        if (!check_group_id(request, response)) {
            break;
        }
        auto control = _raft_ins[request->raft_group_id()]->control();
        if (!check_leader(request, response, control)) {
            break;
        }
        ::aries::pb::ReleaseVolumeRequest new_request;
        new_request.CopyFrom(*request);
        new_request.set_log_id(log_id);
        new_request.set_close_timestamp(base::gettimeofday_s());
        control->close_archive_volume(cntl, &new_request, response);
        g_bvar_release_volume.put(std::to_string(request->raft_group_id()), 1);
    } while (0);
    SERVICE_LOG(NOTICE);
}

void StateServiceManageServiceImpl::seal_volume(::google::protobuf::RpcController* controller,
        const ::aries::pb::SealVolumeRequest* request,
        ::aries::pb::AckResponse* response,
        ::google::protobuf::Closure* done) {
    baidu::rpc::Controller* cntl = (baidu::rpc::Controller*)controller;
    baidu::rpc::ClosureGuard done_guard(done);
    uint64_t log_id = request->log_id();
    base::EndPoint remote_addr = cntl->remote_side();
    auto status = response->mutable_status();

    do {
        if (!check_token(request, response)) {
            break;
        }
        if (!check_group_id(request, response)) {
            break;
        }
        auto control = _raft_ins[request->raft_group_id()]->control();
        if (!check_leader(request, response, control)) {
            break;
        }
        ::aries::pb::SealVolumeRequest new_request;
        new_request.CopyFrom(*request);
        control->seal_archive_volume(cntl, &new_request, response);
        g_bvar_seal_volume.put(std::to_string(request->raft_group_id()), 1);
    } while (0);
    SERVICE_LOG(NOTICE);
}

void StateServiceManageServiceImpl::renew_volume_lease(::google::protobuf::RpcController* controller,
        const ::aries::pb::RenewVolumeLeaseRequest* request,
        ::aries::pb::RenewVolumeLeaseResponse* response,
        ::google::protobuf::Closure* done) {
    baidu::rpc::Controller* cntl = (baidu::rpc::Controller*)controller;
    baidu::rpc::ClosureGuard done_guard(done);
    uint64_t log_id = request->log_id();
    base::EndPoint remote_addr = cntl->remote_side();
    auto status = response->mutable_status();

    do {
        if (!check_token(request, response)) {
            break;
        }
        if (!check_group_id(request, response)) {
            break;
        }
        auto control = _raft_ins[request->raft_group_id()]->control();
        if (!check_leader(request, response, control)) {
            break;
        }
        uint64_t now_time = ::base::gettimeofday_s();
        ::aries::pb::RenewVolumeLeaseRequest new_request;
        new_request.CopyFrom(*request);
        new_request.set_log_id(log_id);
        new_request.set_lease_timeout_timestamp(now_time + FLAGS_volume_session_default_lease_duration);

        control->renew_volume_session_lease(cntl, &new_request, response);
    } while (0);

    SERVICE_LOG(NOTICE);
}

void StateServiceManageServiceImpl::add_peer(::google::protobuf::RpcController* controller,
        const ::aries::pb::StateServiceAddPeerRequest* request,
        ::aries::pb::AckResponse* response,
        ::google::protobuf::Closure* done) {
    baidu::rpc::Controller* cntl = (baidu::rpc::Controller*)controller;
    baidu::rpc::ClosureGuard done_guard(done);
    uint64_t log_id = request->log_id();
    base::EndPoint remote_addr = cntl->remote_side();
    auto status = response->mutable_status();

    do {
        if (!check_token(request, response)) {
            break;
        }
        if (!check_group_id(request, response)) {
            break;
        }
        auto control = _raft_ins[request->raft_group_id()]->control();
        if (!check_leader(request, response, control)) {
            break;
        }

        ::aries::pb::StateServiceAddPeerRequest new_request;
        new_request.CopyFrom(*request);
        control->add_peer(cntl, &new_request, response);
    } while (0);

    SERVICE_LOG(NOTICE);
}

void StateServiceManageServiceImpl::drop_peer(::google::protobuf::RpcController* controller,
        const ::aries::pb::StateServiceDropPeerRequest* request,
        ::aries::pb::AckResponse* response,
        ::google::protobuf::Closure* done) {
    baidu::rpc::Controller* cntl = (baidu::rpc::Controller*)controller;
    baidu::rpc::ClosureGuard done_guard(done);
    uint64_t log_id = request->log_id();
    base::EndPoint remote_addr = cntl->remote_side();
    auto status = response->mutable_status();

    do {
        if (!check_token(request, response)) {
            break;
        }
        if (!check_group_id(request, response)) {
            break;
        }
        auto control = _raft_ins[request->raft_group_id()]->control();
        if (!check_leader(request, response, control)) {
            break;
        }

        ::aries::pb::StateServiceDropPeerRequest new_request;
        new_request.CopyFrom(*request);
        control->drop_peer(cntl, &new_request, response);
    } while (0);

    SERVICE_LOG(NOTICE);
}

void StateServiceManageServiceImpl::set_peers(::google::protobuf::RpcController* controller,
        const ::aries::pb::StateServiceSetPeersRequest* request,
        ::aries::pb::AckResponse* response,
        ::google::protobuf::Closure* done) {
    baidu::rpc::Controller* cntl = (baidu::rpc::Controller*)controller;
    baidu::rpc::ClosureGuard done_guard(done);
    uint64_t log_id = request->log_id();
    base::EndPoint remote_addr = cntl->remote_side();
    auto status = response->mutable_status();

    do {
        if (!check_token(request, response)) {
            break;
        }
        if (!check_group_id(request, response)) {
            break;
        }
        auto control = _raft_ins[request->raft_group_id()]->control();

        ::aries::pb::StateServiceSetPeersRequest new_request;
        new_request.CopyFrom(*request);
        control->set_peers(cntl, &new_request, response);
    } while (0);

    SERVICE_LOG(NOTICE);
}
} // namespace aries
/* vim: set ts=4 sw=4 sts=4 tw=100 */
