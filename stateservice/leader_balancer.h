/***************************************************************************
 * 
 * Copyright (c) 2022 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/
 
#pragma once
#include <cassert>
#include "baidu/inf/aries/stateservice/stateservice_raft_instance.h"

namespace aries::stateservice {

class StateServiceRaftInstance;

class LeaderBalancer {
public:
    LeaderBalancer() {}
    ~LeaderBalancer() {
        stop();
        join();
    }
    void init(std::vector<StateServiceRaftInstance*> instances) {
        assert(instances.size() == FLAGS_stateservice_raft_group_count);
        _raft_ins = instances;
    }
    void start() {
        _is_running = true;
        _thread = std::thread{ std::bind(&LeaderBalancer::run, this) };
    }
    void stop() {
        _is_running = false;
    }
    void join() {
        if (_thread.joinable()) {
            _thread.join();
        }
    }
private:
    void run();
    void try_to_balance_leadership();
private:
    std::atomic<bool> _is_running;
    std::thread _thread;
    std::vector<StateServiceRaftInstance*> _raft_ins;
};
}

/* vim: set ts=4 sw=4 sts=4 tw=100 */
