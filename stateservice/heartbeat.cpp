/***************************************************************************
 * 
 * Copyright (c) 2022 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/

#include "baidu/inf/aries/stateservice/heartbeat.h"

namespace aries::stateservice {

void Heartbeat::heartbeat_rpc() {
    // refresh interval
    heartbeat_interval_second.store(aries::common::FLAGS_heartbeat_interval_second);
    g_master_tracker->update_refresh_interval_second(aries::common::FLAGS_refresh_master_interval_second);

    aries::pb::AckResponse* response = new aries::pb::AckResponse;
    aries::pb::StateServiceHeartbeatRequest* request = new aries::pb::StateServiceHeartbeatRequest;

    ::google::protobuf::Closure* done = ::baidu::rpc::NewCallback(
                            this, &Heartbeat::heartbeat_done, request, response);

    uint64_t req_addr  = common::endpoint2int(_listen_addr);
    request->set_req_addr(req_addr);
    request->set_token(FLAGS_token);
    request->set_version(__ARIES_VERSION_ID__);

    auto basic_metrics = request->mutable_basic_metrics();
    add_basic_metrics_in_heartbeat(basic_metrics);

    request->set_raft_group_id(_raft_ins->group_id());
    request->set_leader(_raft_ins->control()->is_leader());
    uint64_t now = base::gettimeofday_s();
    if (request->leader()) {
        if (now >= _last_heartbeat_with_volume_timestamp +
                FLAGS_heartbeat_with_volumes_interval_second) {
            add_volumes(request);
            _last_heartbeat_with_volume_timestamp = now;
        } else {
            add_incremental_volumes(request);
        }
    }

    base::EndPoint master_addr = get_master_addr();
    ARIES_ST_LOG(NOTICE) << "heartbeat to master " << common::endpoint2str(master_addr);

    RpcCallOptions options;
    options.need_retry = false;
    options.redirect = true;
    MasterStub stub;
    stub.stateservice_heartbeat(master_addr, request, response, done, &options);
}

void Heartbeat::heartbeat_done(aries::pb::StateServiceHeartbeatRequest* request,
                               aries::pb::AckResponse* response) {

    if (response->status().code() != AIE_OK) {
        ARIES_ST_LOG(NOTICE) << "heartbeat with master failed,"
                << " errno:" << response->status().code()
                << " (" << response->status().msg() << ")"; 
    }
    delete request;
    delete response;
}

void Heartbeat::add_volumes(aries::pb::StateServiceHeartbeatRequest* request) {
    std::vector<std::shared_ptr<Volume>> volume_list;
    _raft_ins->meta_data()->volume_manager()->get_volume_list(&volume_list);
    for (auto& volume : volume_list) {
        auto info = request->add_volumes();
        info->set_volume_id(volume->volume_id());
        info->set_is_sealed(volume->is_sealed());
        info->set_append_logical_size(volume->append_logical_size());
    }
}

void Heartbeat::add_incremental_volumes(aries::pb::StateServiceHeartbeatRequest* request) {
    auto volume_id_list = _raft_ins->volume_session_scheduler()->heartbeat_volumes();
    for (auto& vid : volume_id_list) {
        auto volume = _raft_ins->meta_data()->volume_manager()->get_volume(vid);
        if (volume) {
            auto info = request->add_volumes();
            info->set_volume_id(volume->volume_id());
            info->set_is_sealed(volume->is_sealed());
            info->set_append_logical_size(volume->append_logical_size());
        }
    }
}

}

/* vim: set ts=4 sw=4 sts=4 tw=100 */
