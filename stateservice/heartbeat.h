/***************************************************************************
 * 
 * Copyright (c) 2022 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/

#pragma once

#include <atomic>

#include <base/endpoint.h>
#include <bthread.h>
#include <bthread_unstable.h>
#include "baidu/inf/aries-api/common/types.h"
#include "baidu/inf/aries-api/common/config.h"
#include "baidu/inf/aries-api/common/rpc_call.h"
#include "baidu/inf/aries-api/common/rpc_stub.h"
#include "baidu/inf/aries-api/common/heartbeat.h"
#include "baidu/inf/aries-api/common/master_tracker.h"
#include "baidu/inf/aries-api/common/proto/common.pb.h"
#include "baidu/inf/aries-api/common/proto/master.pb.h"
#include "baidu/inf/aries/stateservice/flags.h"
#include "baidu/inf/aries/stateservice/stateservice_raft_instance.h"

namespace aries::stateservice {

class StateServiceRaftInstance;

class Heartbeat : public common::Heartbeat {
public:
    Heartbeat(StateServiceRaftInstance* raft_instance) : _raft_ins(raft_instance) {

    }
    virtual void heartbeat_rpc();
    void heartbeat_done(aries::pb::StateServiceHeartbeatRequest* request,
                        aries::pb::AckResponse* response);
protected:
    void add_volumes(aries::pb::StateServiceHeartbeatRequest* request);
    void add_incremental_volumes(aries::pb::StateServiceHeartbeatRequest* request);
    virtual base::EndPoint get_master_addr() {
        return g_master_tracker->get_master();
    }
protected:
    StateServiceRaftInstance* _raft_ins;
    std::atomic<uint64_t> _last_heartbeat_with_volume_timestamp = 0;
};
}

/* vim: set ts=4 sw=4 sts=4 tw=100 */
