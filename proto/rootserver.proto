syntax="proto2";
package bds.proto;

option cc_generic_services = true;

import "common.proto";

message ConfigProto {
    optional string cluster_uuid = 1;
    optional string region_name = 2;        
    optional string cluster_name = 3;
    // optional uint32 cluster_id = 4;
    optional IPStackModeProto ip_stack_mode = 5;
    optional uint32 version = 6;

    message DCStatusInfoProto {
        optional string dc_name = 1;
        // optional DCStatusProto dc_status = 2;
    }
    repeated DCStatusInfoProto dc_infos = 7;
    optional uint64 mtime_us = 8;
    optional uint64 ctime_us = 9;
}

message RackProto {
    optional string datacenter_name = 1;
    optional string cell_name = 2;
    optional string rack_name = 3;
}

message ClusterProto {
    optional uint64 version = 1;
    optional uint64 mtime_us = 2;
    optional ConfigProto config = 3;
    repeated SpaceProto space_list = 4;
    repeated DanceDNInfoProto dancedn_list = 5;
    repeated RackProto rack_list = 6;
}

message SpaceProto {
    optional uint32 space_id = 1;
    optional string space_name = 2;
    optional uint64 plm_info = 3;
    optional uint64 rep_param = 4;
    optional uint64 ctime_us = 5;
    optional uint64 mtime_us = 6;
    optional uint32 version = 7;
    optional uint64 replica_num = 8;
    optional uint32 least_normal_num = 9;
    optional uint64 group_num = 10;
    repeated uint64 group_id_list = 11;
}

enum GroupStatusProto {
    GROUP_STATUS_PROTO_NORMAL = 0;
    GROUP_STATUS_PROTO_REPAIRING = 1;
    GROUP_STATUS_PROTO_MIGRATING = 2;
    GROUP_STATUS_PROTO_REBALANCING = 3;
    GROUP_STATUS_PROTO_NUM = 4;
}

message GroupReplicaProto {
    optional string host = 1;
    optional uint32 port = 2;
    optional uint64 dn_id = 3;
    optional uint32 disk_id = 4;
    optional uint64 group_id = 5;
    optional uint32 index = 6;
    optional uint64 ctime_us = 7;
    optional uint64 mtime_us = 8;
}

message GroupProto {
    optional uint64 group_id = 1;
    optional uint32 space_id = 2;
    optional uint32 version = 3;
    optional GroupStatusProto status = 4;
    repeated GroupReplicaProto replica_list = 5;
    repeated GroupReplicaProto history_replicas = 6;
    optional uint64 ctime_us = 7;
    optional uint64 mtime_us = 8;
};

enum DanceDNStatusProto {
    DN_STATUS_PROTO_UNKNOWN = 1; // DN刚启动的初始状态，检查RS是否存在该DN，无则注册，有则判断状态是否能转为NORMAL
    DN_STATUS_PROTO_PENDING = 2; // DN已向RootServer注册成功
    DN_STATUS_PROTO_JOINING = 3; // DN正在加入集群, 准备group数据中
    DN_STATUS_PROTO_NORMAL = 4; // DN正常运行, 提供数据读写服务
    DN_STATUS_PROTO_READONLY = 5; // DN仅接受读请求，不接受写请求
    DN_STATUS_PROTO_UPGRADING = 6; // DN正常重启升级退出时修改为此状态，避免DN被判DEAD触发补副本任务
    DN_STATUS_PROTO_DECOMMISSIONING = 7; // DN缩容/下线中, 提供数据读写服务, 数据正在被主动复制到其他节点 
    DN_STATUS_PROTO_REBALANCING = 8; // DN执行数据均衡任务中, 提供数据读写服务(按需使用)
    DN_STATUS_PROTO_DECOMMISSIONED = 9; // DN主动下线完成, 更新视图不再提供数据读写服务，可以执行数据清理或直接进入OFFLINE
    DN_STATUS_PROTO_SUSPECT = 10; // DN健康检查为疑似故障状态 
    DN_STATUS_PROTO_DEAD = 11; // DN判活为最终故障状态，需要对其上数据副本进行补齐后转为OFFLINE
    DN_STATUS_PROTO_OFFLINE = 12; // DN下线后的最终状态
}

message IOStatProto {
    optional uint64 qps = 1;
    optional uint64 throughput = 2;
    optional uint64 lat_us_avg = 3;
    optional uint64 lat_us_p99 = 4;
}

enum MediaTypeProto {
    MEDIA_TYPE_PROTO_UNKNOWN = 1;
    MEDIA_TYPE_PROTO_SATA_HDD = 2;
    MEDIA_TYPE_PROTO_SATA_SSD = 3;
    MEDIA_TYPE_PROTO_NVME_SSD = 4;
    MEDIA_TYPE_PROTO_RAM_DISK = 5;
}

enum DiskStatusProto {
    DISK_STATUS_PROTO_UNKNOWN = 1;         // 磁盘初始状态
    DISK_STATUS_PROTO_PENDING = 2;         // 磁盘处于待加入状态，暂时不参与数据存储
    DISK_STATUS_PROTO_JOINING = 3;         // 磁盘正在加入集群，准备数据中
    DISK_STATUS_PROTO_NORMAL = 4;          // 磁盘正常读写
    DISK_STATUS_PROTO_DECOMMISSIONING = 5; // 磁盘正在被退役，数据正在迁移到其他磁盘
    DISK_STATUS_PROTO_DECOMMISSIONED = 6;  // 磁盘已退役，数据已迁移完成
    DISK_STATUS_PROTO_REBALANCING = 7;     // 磁盘正在进行数据均衡
    DISK_STATUS_PROTO_FROZEN = 8;          // 磁盘被冻结，不能读写数据
    DISK_STATUS_PROTO_READONLY = 9;        // 磁盘处于只读状态，只能读取数据，不能写入 
    DISK_STATUS_PROTO_SUSPECT = 10;        // 磁盘健康检查为疑似故障状态
    DISK_STATUS_PROTO_DEAD = 11;            // 磁盘发生故障，无法访问
    DISK_STATUS_PROTO_OFFLINE = 12;         // 磁盘已下线，不再参与数据存储
}

message DiskInfoProto {
    optional uint32 disk_id = 1;
    optional DiskStatusProto status = 2;
    optional MediaTypeProto media_type = 3;
    optional bool is_disk_partition = 4;
    optional uint64 used_in_bytes = 5;
    optional uint64 avail_in_bytes = 6;
    optional uint64 total_in_bytes = 7;
    optional uint64 block_group_count = 8;
    optional uint64 create_time = 9;
    optional IOStatProto read_stat = 10;
    optional IOStatProto write_stat = 11;
}

// http service message
message EmptyMessage {
}

message GetClusterInfoRequest {
    optional CommonRequest comm_req = 1;
    optional uint32 cluster_id = 2;
    optional string cluster_uuid = 3;
    optional string region_name = 4;
    optional string cluster_name = 5;
}

message GetClusterInfoResponse {
    optional CommonResponse comm_resp = 1;
    optional ClusterProto cluster_info = 2;
}

message GetSpaceInfoRequest {
    optional CommonRequest comm_req = 1;
    optional string cluster_uuid = 2;
    optional uint32 space_id = 3;
    optional string space_name = 4;
}

message GetSpaceInfoResponse {
    optional CommonResponse comm_resp = 1;
    optional SpaceProto space_info = 2;
}

message ListSpacesRequest {
    optional CommonRequest comm_req = 1;
    optional string cluster_uuid = 2;
}

message ListSpacesResponse {
    optional CommonResponse comm_resp = 1;
    repeated SpaceProto space_list = 2;
}

message ListBlockGroupsRequest {
    optional CommonRequest comm_req = 1;
    optional string cluster_uuid = 2;
    optional uint32 space_id = 3;
}

message ListBlockGroupsResponse {
    optional CommonResponse comm_resp = 1;
    repeated GroupProto group_list = 2;
}

message ShowBlockGroupRequest {
    optional CommonRequest comm_req = 1;
    optional uint64 group_id = 2;
}

message ShowBlockGroupResponse {
    optional CommonResponse comm_resp = 1;
    optional GroupProto group = 2;
}

message GetBlockGroupInfoRequest {
    optional CommonRequest comm_req = 1;
    optional uint64 group_id = 2;
}

message GetBlockGroupInfoResponse {
    optional CommonResponse comm_resp = 1;
    optional GroupProto group = 2;
}

message UpdateBlockGroupRequest {
    optional CommonRequest comm_req = 1;
    optional uint64 group_id = 2;
    optional GroupProto group = 3;
}

message UpdateBlockGroupResponse {
    optional CommonResponse comm_resp = 1;
}

message CheckBlockGroupRequest {
    optional CommonRequest comm_req = 1;
    optional uint64 group_id = 2;
}

message CheckBlockGroupResponse {
    optional CommonResponse comm_resp = 1;
    optional bool is_healthy = 2;
}

enum ReplicaPlacementPolicyProto {
    // 随机选择DN策略
    REPLICA_PLACEMENT_STRATEGY_RANDOM = 0;
    // 机架感知策略：将副本分布在不同机架上，以防止机架级别故障导致数据不可用
    REPLICA_PLACEMENT_STRATEGY_RACK_AWARE = 1;
    // 负载均衡策略：基于节点当前利用率指标分布副本，防止资源热点并提高系统整体性能
    REPLICA_PLACEMENT_STRATEGY_LOAD_BALANCED = 2;
    // 容量均衡策略：基于DN磁盘容量分布副本，确保每个DN存储的容量相对均衡
    REPLICA_PLACEMENT_STRATEGY_CAPACITY_AWARE = 3;
    // 延迟优化策略：将副本分布在距离客户端最近的DN上，以最小化数据访问延迟
    REPLICA_PLACEMENT_STRATEGY_LATENCY_OPTIMIZED = 4;
}

message PlacementInfoProto {
    optional uint32 group_num = 1;
    optional uint32 replica_num = 2;
    optional ReplicaPlacementPolicyProto policy = 3; // replica的选择策略
}

message AddSpaceRequest {
    optional CommonRequest comm_req = 1;
    optional string cluster_uuid = 2;
    optional string space_name = 3;
    optional PlacementInfoProto placement = 4;
}

message AddSpaceResponse {
    optional CommonResponse comm_resp = 1;
    optional uint32 space_id = 2;
}

message DeleteSpaceRequest {
    optional CommonRequest comm_req = 1;
    optional string cluster_uuid = 2;
    optional uint32 space_id = 3;
    optional string space_name = 4;
}

message DeleteSpaceResponse {
    optional CommonResponse comm_resp = 1;
    optional uint32 space_id = 2;
    optional string space_name = 3;
}

message UpdateSpaceRequest {
    optional CommonRequest comm_req = 1;
    optional uint32 space_id = 2;
    optional string space_name = 3;
}

message UpdateSpaceResponse {
    optional CommonResponse comm_resp = 1;
    optional uint32 space_id = 2;
    optional string space_name = 3;
}

// 磁盘相关
message ListDisksRequest {
    optional CommonRequest comm_req = 1;
    optional uint64 dance_dn_addr = 2;
}

message ListDisksResponse {
    optional CommonResponse comm_resp = 1;
    repeated DiskInfoProto disks = 2;
}

// DN register时汇报RS主动add disk到manager
message AddDiskRequest {
    optional CommonRequest comm_req = 1;
    optional uint64 dance_dn_addr = 2;
    optional string disk_type = 3;
    required uint32 disk_uuid = 4;
}

message AddDiskResponse {
    optional CommonResponse comm_resp = 1;
    optional uint32 disk_id = 2;
}

message RemoveDiskRequest {
    optional CommonRequest comm_req = 1;
    optional uint64 dance_dn_addr = 2;
    optional uint32 disk_id = 3;
}

message RemoveDiskResponse {
    optional CommonResponse comm_resp = 1;
}


message UpdateDiskRequest {
    optional CommonRequest comm_req = 1;
    optional uint64 dance_dn_addr = 2;
    optional uint32 disk_id = 3;
    optional DiskStatusProto state = 4;
}

message UpdateDiskResponse {
    optional CommonResponse comm_resp = 1;
}

message GetDiskInfoRequest {
    optional CommonRequest comm_req = 1;
    optional uint64 dance_dn_addr = 2;
    optional uint32 disk_id = 3;
}

message GetDiskInfoResponse {
    optional CommonResponse comm_resp = 1;
    optional DiskInfoProto disk_info = 2;
}

// Raft相关
message IsLeaderRequest {
    optional CommonRequest comm_req = 1;
    optional string token = 2;
    optional string addr = 3; // ip:port
}

message IsLeaderResponse {
    optional CommonResponse comm_resp = 1;
    optional bool is_leader = 2;
    repeated string peers = 3;
}

message GetLeaderRequest {
    optional CommonRequest comm_req = 1;
    optional string token = 2;
}

message GetLeaderResponse {
    optional CommonResponse comm_resp = 1;
    optional string leader_addr = 2;
    optional int64 leader_id = 3;
}

message AddPeerRequest {
    optional CommonRequest comm_req = 1;
    optional string token = 2;
    optional string new_peer_addr = 3;
    optional int64 new_peer_id = 4;
}

message AddPeerResponse {
    optional CommonResponse comm_resp = 1;
    repeated string peers = 2;
    repeated int64 peer_ids = 3;
}

message RemovePeerRequest {
    optional CommonRequest comm_req = 1;
    optional string peer_addr = 2;
    optional string token = 3;
}

message RemovePeerResponse {
    optional CommonResponse comm_resp = 1;
}

message ListPeersRequest {
    optional CommonRequest comm_req = 1;
    optional string token = 2;
}

message ListPeersResponse {
    optional CommonResponse comm_resp = 1;
    repeated string peer_addrs = 2;
    repeated int64 peer_ids = 3;
}

message TransferLeaderRequest {
    optional CommonRequest comm_req = 1;
    optional string token = 2;
    optional string new_leader_addr = 3;
}

message TransferLeaderResponse {
    optional CommonResponse comm_resp = 1;
    optional int64 leader_id = 2;
}

// DN注册相关
message RegisterDanceDNRequest {
    optional CommonRequest comm_req = 1;
    optional string cluster_uuid = 2;
    optional string host = 3;
    optional uint32 port = 4;
    optional DanceDNStatusProto dn_status = 5;
    optional uint64 up_time = 6;
    optional string dc_name = 7;
    optional string cell_name = 8;
    optional string rack_name = 9;
    optional uint32 disk_count = 10;
    repeated DiskInfoProto disk_infos = 11;
}

message RegisterDanceDNResponse {
    optional CommonResponse comm_resp = 1;
    optional string dn_id = 2;
    optional DanceDNStatusProto dn_status = 3;
}

message DNHeartBeatRequest {
    optional CommonRequest comm_req = 1;
    optional string dn_id = 2;
}

message DNHeartBeatResponse {
    optional CommonResponse comm_resp = 1;
}

message CreateBlockGroupRequest {
    optional CommonRequest comm_req = 1;
    optional string cluster_uuid = 2;
    optional uint32 space_id = 3;
}

message CreateBlockGroupResponse {
    optional CommonResponse comm_resp = 1;
    optional GroupProto group = 2;
}

message BalanceBlockGroupRequest {
    optional CommonRequest comm_req = 1;
    optional uint32 space_id = 2;
    optional uint64 group_id = 3;
}

message BalanceBlockGroupResponse {
    optional CommonResponse comm_resp = 1;
}

message RemoveBlockGroupRequest {
    optional CommonRequest comm_req = 1;
    optional uint32 space_id = 2;
    optional uint64 group_id = 3;
}

message RemoveBlockGroupResponse {
    optional CommonResponse comm_resp = 1;
    optional uint64 group_id = 2;
}

// 磁盘查询相关
message ShowDiskRequest {
    optional CommonRequest comm_req = 1;
    optional string dn_id = 2;
    optional uint32 disk_id = 3;
}

message ShowDiskResponse {
    optional CommonResponse comm_resp = 1;
    optional DiskInfoProto disk = 2;
}

// Namespace相关
message CreateNamespaceRequest {
    optional CommonRequest comm_req = 1;
    optional uint32 space_id = 2;
    optional string namespace_name = 3;
}

message CreateNamespaceResponse {
    optional CommonResponse comm_resp = 1;
    optional uint32 namespace_id = 2;
}

message DeleteNamespaceRequest {
    optional CommonRequest comm_req = 1;
    optional uint32 space_id = 2;
    optional uint32 namespace_id = 3;
    optional string namespace_name = 4;
}

message DeleteNamespaceResponse {
    optional CommonResponse comm_resp = 1;
}

message ListNamespacesRequest {
    optional CommonRequest comm_req = 1;
    optional uint32 space_id = 2;
}

message ListNamespacesResponse {
    optional CommonResponse comm_resp = 1;
    repeated NamespaceProto namespaces = 2;
}

message NamespaceProto {
    optional uint32 namespace_id = 1;
    optional string namespace_name = 2;
    optional uint64 ctime_us = 3;
    optional uint64 mtime_us = 4;
}

// DanceDN 相关
message ListDanceDNsRequest {
    optional CommonRequest comm_req = 1;
}

message ListDanceDNsResponse {
    optional CommonResponse comm_resp = 1;
    repeated DanceDNInfoProto dn_infos = 2;
}

message GetDanceDNInfoRequest {
    optional CommonRequest comm_req = 1;
    optional uint64 dance_dn_addr = 2;
}

message GetDanceDNInfoResponse {
    optional CommonResponse comm_resp = 1;
    optional DanceDNInfoProto dn_info = 2;
}

message AddDanceDNRequest {
    optional CommonRequest comm_req = 1;
    optional uint64 node_addr = 2;
    optional string az_name = 3;
    optional string idc_name = 4;
    optional string rack_name = 5;
    optional string group_name = 6;
    optional DanceDNStatusProto state = 7;
    optional uint64 create_time = 8;
}

message AddDanceDNResponse {
    optional CommonResponse comm_resp = 1;
}

message RemoveDanceDNRequest {
    optional CommonRequest comm_req = 1;
    optional uint64 node_addr = 2;
    optional bool is_force_drop = 3;
}

message RemoveDanceDNResponse {
    optional CommonResponse comm_resp = 1;
}
message DanceDNInfoProto {
    optional string dn_id = 1;
    optional string host = 2;
    optional uint32 port = 3;
    optional DanceDNStatusProto status = 4;
    optional string location_dc = 5;     // Renamed from dc_name for clarity
    optional string location_cell = 6;   // Renamed from cell_name for consistency
    optional string location_rack = 7;   // Renamed from rack_name for consistency
    optional uint64 up_time = 8;
    optional uint64 last_heartbeat_time = 9;
    repeated DiskInfoProto disk_infos = 10;
}

message UpdateDanceDNRequest {
    optional CommonRequest comm_req = 1;
    optional uint64 node_addr = 2;
    optional DanceDNStatusProto state = 3;
    optional DanceDNStatusProto src_state = 4;
}

message UpdateDanceDNResponse {
    optional CommonResponse comm_resp = 1;
}


// 手动均衡相关
message AddManualBalanceRequest {
    optional CommonRequest comm_req = 1;
    optional string space_name = 2;
    repeated uint64 src_node_list = 3;
    repeated uint64 dest_node_list = 4;
    repeated uint64 src_disk_list = 5;
    repeated uint64 dest_disk_list = 6;
    optional uint64 balance_block_group_replica_num = 7;
    optional uint64 balance_block_group_index = 8;
    optional uint64 max_running_task_num = 9;
}

message AddManualBalanceResponse {
    optional CommonResponse comm_resp = 1;
    optional uint32 task_id = 2;
}

message CancelManualBalanceRequest {
    optional CommonRequest comm_req = 1;
    optional uint32 task_id = 2;
}

message CancelManualBalanceResponse {
    optional CommonResponse comm_resp = 1;
}

message ShowManualBalanceRequest {
    optional CommonRequest comm_req = 1;
    optional uint32 task_id = 2;
}

message ShowManualBalanceResponse {
    optional CommonResponse comm_resp = 1;
    optional ManualBalanceInfo balance_info = 2;
}

message ListManualBalanceRequest {
    optional CommonRequest comm_req = 1;
}

message ListManualBalanceResponse {
    optional CommonResponse comm_resp = 1;
    repeated ManualBalanceInfo balance_infos = 2;
}

message ManualBalanceInfo {
    optional uint32 task_id = 1;
    optional string src_dn_id = 2;
    optional string dst_dn_id = 3;
    optional uint64 create_time = 4;
    optional uint64 finish_time = 5;
    optional string status = 6;
    optional uint64 progress = 7;
    optional uint64 estimated_time_remaining = 8;
}

message AddManualReplicateRequest {
    optional CommonRequest comm_req = 1;
    optional uint32 task_id = 2;
    optional string dst_dn_id = 3;
}

message AddManualReplicateResponse {
    optional CommonResponse comm_resp = 1;
}

message RemoveManualReplicateRequest {
    optional CommonRequest comm_req = 1;
    optional uint32 task_id = 2;
}

message RemoveManualReplicateResponse {
    optional CommonResponse comm_resp = 1;
}

message ShowManualReplicateRequest {
    optional CommonRequest comm_req = 1;
    optional uint32 task_id = 2;
}

message ShowManualReplicateResponse {
    optional CommonResponse comm_resp = 1;
    optional ManualReplicateInfoProto replicate_info = 2;
}

message ListManualReplicateRequest {
    optional CommonRequest comm_req = 1;
}

message ListManualReplicateResponse {
    optional CommonResponse comm_resp = 1;
    repeated ManualReplicateInfoProto replicate_infos = 2;
}

message ManualReplicateInfoProto {
    optional uint64 group_id = 1;
    optional string dst_dn_id = 2;
    optional uint64 create_time = 3;
    optional uint64 finish_time = 4;
    optional string status = 5;
    optional uint64 progress = 6;
    optional uint64 estimated_time_remaining = 7;
}

message ReportDanceDNInfoRequest {
    optional CommonRequest comm_req = 1;
    optional string dn_id = 2;
    optional DanceDNInfoProto dn_info = 3;
}

message ReportDanceDNInfoResponse {
    optional CommonResponse comm_resp = 1;
}

message GroupInfoProto {
    optional uint64 group_id = 1;
    optional uint32 version = 2;
    optional GroupStatusProto status = 3;
    repeated GroupReplicaProto replica_list = 5;
    optional uint64 ctime_us = 10;
    optional uint64 mtime_us = 11;
}

message NamespaceInfoProto {
    optional uint64 id = 1;
    optional string name = 2;
    optional uint64 ctime_us = 3;
    optional uint64 mtime_us = 4;
}

message SpaceRouteInfo {
    optional uint32 space_id = 1; 
    optional string space_name = 2; 
    optional uint64 space_version = 3;
    repeated NamespaceInfoProto namespace_infos = 4;
    repeated GroupInfoProto group_infos = 5;
}

message GetRouteInfoRequest {
    optional CommonRequest comm_req = 1;
    repeated uint32 space_id = 2; // (100,101)
    repeated string space_name = 3; // (FILE,DENTRY)
    repeated uint64 space_version = 4;
}

message GetRouteInfoResponse {
    optional CommonResponse comm_resp = 1;
    repeated SpaceRouteInfo route_infos = 2;
}

service RSCoreService {
    // Cluster 相关操作
    rpc GetClusterInfo(GetClusterInfoRequest) returns (GetClusterInfoResponse);
    // rpc SetClusterInfo(SetClusterInfoRequest) returns (SetClusterInfoResponse);

    // 路由信息
    rpc GetRouteInfo(GetRouteInfoRequest) returns (GetRouteInfoResponse);

    // Space 相关操作
    rpc GetSpaceInfo(GetSpaceInfoRequest) returns (GetSpaceInfoResponse);
    rpc ListSpaces(ListSpacesRequest) returns (ListSpacesResponse);
    rpc AddSpace(AddSpaceRequest) returns (AddSpaceResponse);
    rpc DeleteSpace(DeleteSpaceRequest) returns (DeleteSpaceResponse);
    rpc UpdateSpace(UpdateSpaceRequest) returns (UpdateSpaceResponse);

    // BlockGroup 相关操作
    rpc ListBlockGroups(ListBlockGroupsRequest) returns (ListBlockGroupsResponse);
    rpc GetBlockGroupInfo(GetBlockGroupInfoRequest) returns (GetBlockGroupInfoResponse);
    rpc UpdateBlockGroup(UpdateBlockGroupRequest) returns (UpdateBlockGroupResponse);
    rpc CheckBlockGroup(CheckBlockGroupRequest) returns (CheckBlockGroupResponse);
    rpc CreateBlockGroup(CreateBlockGroupRequest) returns (CreateBlockGroupResponse);
    rpc BalanceBlockGroup(BalanceBlockGroupRequest) returns (BalanceBlockGroupResponse);
    rpc RemoveBlockGroup(RemoveBlockGroupRequest) returns (RemoveBlockGroupResponse);

    // Namespace 相关操作, 用户侧可见的namespace, id全局唯一递增
    rpc CreateNamespace(CreateNamespaceRequest) returns (CreateNamespaceResponse);
    rpc DeleteNamespace(DeleteNamespaceRequest) returns (DeleteNamespaceResponse);
    rpc ListNamespaces(ListNamespacesRequest) returns (ListNamespacesResponse);

    // DanceDN 相关操作
    rpc ListDanceDNs(ListDanceDNsRequest) returns (ListDanceDNsResponse);
    rpc GetDanceDNInfo(GetDanceDNInfoRequest) returns (GetDanceDNInfoResponse);
    rpc AddDanceDN(AddDanceDNRequest) returns (AddDanceDNResponse);
    rpc RemoveDanceDN(RemoveDanceDNRequest) returns (RemoveDanceDNResponse);
    rpc UpdateDanceDN(UpdateDanceDNRequest) returns (UpdateDanceDNResponse);

    // Disk 相关操作
    rpc ListDisks(ListDisksRequest) returns (ListDisksResponse);
    rpc AddDisk(AddDiskRequest) returns (AddDiskResponse);
    rpc RemoveDisk(RemoveDiskRequest) returns (RemoveDiskResponse);
    rpc UpdateDisk(UpdateDiskRequest) returns (UpdateDiskResponse);
    rpc GetDiskInfo(GetDiskInfoRequest) returns (GetDiskInfoResponse);

    // Balance/Replicate 相关操作
    rpc AddManualBalance(AddManualBalanceRequest) returns (AddManualBalanceResponse);
    rpc CancelManualBalance(CancelManualBalanceRequest) returns (CancelManualBalanceResponse);
    rpc ShowManualBalance(ShowManualBalanceRequest) returns (ShowManualBalanceResponse);
    rpc ListManualBalance(ListManualBalanceRequest) returns (ListManualBalanceResponse);

    rpc AddManualReplicate(AddManualReplicateRequest) returns (AddManualReplicateResponse);
    rpc RemoveManualReplicate(RemoveManualReplicateRequest) returns (RemoveManualReplicateResponse);
    rpc ShowManualReplicate(ShowManualReplicateRequest) returns (ShowManualReplicateResponse);
    rpc ListManualReplicate(ListManualReplicateRequest) returns (ListManualReplicateResponse);

    // raft cli service
    rpc IsLeader(IsLeaderRequest) returns (IsLeaderResponse);
    rpc GetLeader(GetLeaderRequest) returns (GetLeaderResponse);
    rpc AddPeer(AddPeerRequest) returns (AddPeerResponse);
    rpc RemovePeer(RemovePeerRequest) returns (RemovePeerResponse);
    rpc ListPeers(ListPeersRequest) returns (ListPeersResponse);
    rpc TransferLeader(TransferLeaderRequest) returns (TransferLeaderResponse);
}

service RSDanceDNService {
    rpc RegisterDanceDN(RegisterDanceDNRequest) returns (RegisterDanceDNResponse);
    rpc DanceDNHeartBeat(DNHeartBeatRequest) returns (DNHeartBeatResponse);
    rpc ReportDanceDNInfo(ReportDanceDNInfoRequest) returns (ReportDanceDNInfoResponse);
}

message ClientHeartBeatRequest {
    optional CommonRequest comm_req = 1;
}

message ClientHeartBeatResponse {
    optional CommonResponse comm_resp = 1;
}

message ReportClientInfoRequest {
    optional CommonRequest comm_req = 1;
    // repeated ClientInfoProto client_infos = 2;
}

message ReportClientInfoResponse {
    optional CommonResponse comm_resp = 1;
}

service RSClientService {
    rpc ClientHeartBeat(ClientHeartBeatRequest) returns (ClientHeartBeatResponse);
    rpc ReportClientInfo(ReportClientInfoRequest) returns (ReportClientInfoResponse);
}

service RSHttpService {
    rpc default_method(EmptyMessage) returns (EmptyMessage);

    rpc GetClusterInfo(EmptyMessage) returns (EmptyMessage);

    rpc AddSpace(EmptyMessage) returns (EmptyMessage);
    rpc GetSpaceInfo(EmptyMessage) returns (EmptyMessage);

    rpc ListSpaces(EmptyMessage) returns (EmptyMessage);
    rpc ListBlockGroups(EmptyMessage) returns (EmptyMessage);
    rpc ListDanceDNs(EmptyMessage) returns (EmptyMessage);
    rpc ListDisks(EmptyMessage) returns (EmptyMessage);
    rpc ListClients(EmptyMessage) returns (EmptyMessage);
    
    rpc ShowCluster(EmptyMessage) returns (EmptyMessage);
    rpc ShowSpace(EmptyMessage) returns (EmptyMessage);
    rpc ShowDanceDN(EmptyMessage) returns (EmptyMessage);
    rpc ShowDisk(EmptyMessage) returns (EmptyMessage);
    rpc ShowBlockGroup(EmptyMessage) returns (EmptyMessage);

    // 运维类curl接口
    rpc AddPeer(EmptyMessage) returns (EmptyMessage);
    rpc AddDanceDN(EmptyMessage) returns (EmptyMessage);    
    rpc AddDisk(EmptyMessage) returns (EmptyMessage);    
}
