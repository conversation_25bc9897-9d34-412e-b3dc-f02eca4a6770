// Copyright (c) 2021, ByteDance Inc. All rights reserved.
#pragma once
#include <memory>

#include "byterpc/memory_id.h"
#include "rpc/event_base.h"

namespace byte {
namespace embedded_metrics {
class Guage;
}  // namespace embedded_metrics
}  // namespace byte

namespace byterpc {

struct IOBufStats;
class IOBufStatWatcher;
extern thread_local IOBufStats* cur_thread_iobuf_stat;

class IOBufStats {
    friend class IOBufStatWatcher;

public:
    // At present byterpc support 16 modules, however module_id 0
    // is already used by byterpc internal, user can use from 1 to 255.
    // NOTE: `MAX_MODULE_NUM' depends on `uint8_t _module_id'.
    static const size_t MAX_MODULE_NUM = 256;

    IOBufStats();
    ~IOBufStats();

    static void Init();
    // Start current thread iobuf monitor, and it's NOT thread-safe.
    static void StartMonitor(EventRegistry* ev_reg);
    // Stop current thread iobuf monitor, and it's NOT thread-safe.
    static void StopMonitor();

    void Add(size_t block_size, MemoryId memory_id, size_t module_id) {
        if (memory_id >= MemoryId::NUM_MEMORY_ID || module_id >= MAX_MODULE_NUM) {
            return;
        }
        _block_mem[static_cast<size_t>(memory_id)][module_id] += block_size;
        _nblock[static_cast<size_t>(memory_id)][module_id]++;
    }

    void Remove(size_t block_size, MemoryId memory_id, size_t module_id) {
        if (memory_id >= MemoryId::NUM_MEMORY_ID || module_id >= MAX_MODULE_NUM) {
            return;
        }
        _block_mem[static_cast<size_t>(memory_id)][module_id] -= block_size;
        _nblock[static_cast<size_t>(memory_id)][module_id]--;
    }

private:
    void AddIOBufStatWatcher();

    void SetEventRegistry(EventRegistry* ev_reg);

    void ReportMetrics(bool reg_timer = true);

    size_t _block_mem[static_cast<size_t>(MemoryId::NUM_MEMORY_ID)][MAX_MODULE_NUM] = {{0}};
    size_t _nblock[static_cast<size_t>(MemoryId::NUM_MEMORY_ID)][MAX_MODULE_NUM] = {{0}};

    byte::embedded_metrics::Guage* _used_memory[static_cast<size_t>(MemoryId::NUM_MEMORY_ID)]
                                               [MAX_MODULE_NUM] = {{nullptr}};
    byte::embedded_metrics::Guage* _used_block[static_cast<size_t>(MemoryId::NUM_MEMORY_ID)]
                                              [MAX_MODULE_NUM] = {{nullptr}};

    std::unique_ptr<TimeEventHandler> _iobuf_stat_watcher;
    EventRegistry* _watcher_ev_reg;
    TimeoutIterator _timeout_iter;
    bool _add_timer;
};

}  // namespace byterpc
