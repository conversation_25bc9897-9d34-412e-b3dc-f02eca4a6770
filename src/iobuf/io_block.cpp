// Copyright (c) 2021, ByteDance Inc. All rights reserved.

#include "byterpc/io_block.h"

#include <cstdlib>
#include <memory>

#include "byterpc/io_buf.h"
#include "byterpc/util/compiler_specific.h"
#include "iobuf/io_buf_monitor.h"
#include "mem/memory_pool.h"
#include "metrics/byterpc_metrics.h"
#include "util/tls_object_cache.h"

#include "byterpc/util/logging.h"
#include "byterpc/util/numautils.h"

namespace byterpc {

class TLSBlockCache : public LocalObjectCache<IOBlock> {
public:
    IOBlock* GetIOBlock() {
        IOBlock* blk = GetOne();
        if (BYTERPC_UNLIKELY(!blk)) {
            return new IOBlock;
        }
        return blk;
    }

    void PutIOBlock(IOBlock* blk) {
        if (BYTERPC_LIKELY(IsNotFull())) {
            Return(blk);
        } else {
            delete blk;
        }
    }
};
static thread_local TLSBlockCache s_ioblock_cache;

void UnifyMemoryFree(void* addr, void*) {
    MemoryPool::Get()->Dealloc(addr);
}

void DefaultFree(void* addr, void*) {
    free(addr);
}

IOBlock* IOBlock::Create(size_t block_size, uint8_t module_id) {
    return Create(block_size, MemoryOwner(UnifyMemoryFree, nullptr), module_id);
}

IOBlock* IOBlock::Create(size_t block_size, MemoryOwner deleter, uint8_t module_id) {
    BYTERPC_DCHECK(block_size > sizeof(IOBlock)) << "block_size is too small";
    void* vaddr = MemoryPool::Get()->Alloc(block_size, util::get_numa_id_of_this_thread());
    if (BYTERPC_UNLIKELY(!vaddr)) {
        // Fallback to use malloc() allocate if failed.
        METRICS_byterpc_block_create_memory_fallback()
            ->GetMetric({{rpctagk::kMemoryId, MemoryIdToString(MemoryId::UNIFY_ALLOCATED)}})
            ->Set(true);
        BYTERPC_LOG_EVERY(WARNING, 10000)
            << "come to fallback path, memory_id:" << MemoryIdToString(MemoryId::UNIFY_ALLOCATED);
        // Not set deleter when use malloc to create IOBlock. deleter only used for IOBlockTlsPool
        // to return IOBlock to list. For malloc IOBlock, no need return it to list because too
        // many malloc IOBlocks may avoid MemoryPoolBase IOBlock to return back to list.
        return DefaultCreate(block_size, module_id);
    }

    char* mem = static_cast<char*>(vaddr);
    IOBlock* blk = new (mem) IOBlock;
    blk->_memory_id = MemoryId::UNIFY_ALLOCATED;
    blk->_refs = 1;
    blk->_data = mem + sizeof(IOBlock);
    blk->_cap = block_size - sizeof(IOBlock);
    blk->_size = 0;
    blk->_flag = BlockFlag::kNone;
    blk->_next = nullptr;
    blk->_mem_owner.free = deleter.free;
    blk->_mem_owner.param = deleter.param;
    blk->_phy_addr = 1;
    BYTERPC_DCHECK(module_id < IOBufStats::MAX_MODULE_NUM) << "module_id is too big";
    blk->_module_id = module_id;
#ifdef BYTERPC_ENABLE_IOBUF_MONITOR
    if (cur_thread_iobuf_stat) {
        cur_thread_iobuf_stat->Add(block_size, MemoryId::UNIFY_ALLOCATED, module_id);
    }
#endif

    return blk;
}

IOBlock* IOBlock::DefaultCreate(size_t block_size, uint8_t module_id) {
    BYTERPC_DCHECK(block_size > sizeof(IOBlock)) << "block_size is too small";
    char* mem = static_cast<char*>(malloc(block_size));
    if (BYTERPC_UNLIKELY(!mem)) {
        return nullptr;
    }

    IOBlock* blk = new (mem) IOBlock;
    blk->_memory_id = MemoryId::DEFAULT_ALLOCATED;
    blk->_refs = 1;
    blk->_data = mem + sizeof(IOBlock);
    blk->_cap = block_size - sizeof(IOBlock);
    blk->_size = 0;
    blk->_flag = BlockFlag::kNone;
    blk->_next = nullptr;
    blk->_mem_owner.free = DefaultFree;
    blk->_mem_owner.param = nullptr;
    blk->_phy_addr = 0;
    BYTERPC_DCHECK(module_id < IOBufStats::MAX_MODULE_NUM) << "module_id is too big";
    blk->_module_id = module_id;
#ifdef BYTERPC_ENABLE_IOBUF_MONITOR
    if (cur_thread_iobuf_stat) {
        cur_thread_iobuf_stat->Add(block_size, MemoryId::DEFAULT_ALLOCATED, module_id);
    }
#endif

    return blk;
}

IOBlock* IOBlock::TakeOwnership(const MemoryDescriptor& meta, void (*deleter)(void*, void*)) {
    if (BYTERPC_UNLIKELY(!deleter)) {
        if (meta.memory_id != MemoryId::DEFAULT_ALLOCATED) {
            BYTERPC_LOG(WARNING) << "Must set `deleter` for non DEFAULT_ALLOCATED memory";
            return nullptr;
        }
        deleter = DefaultFree;
    }

    IOBlock* blk = s_ioblock_cache.GetIOBlock();
    if (BYTERPC_UNLIKELY(!blk)) {
        return nullptr;
    }

    // If the memory is allocated by byterpc, memory id need be UNIFY_ALLOCATED, ignore
    // BYTE_EXPRESS_ALLOCATED/TARZAN_ALLOCATED. If the memory id is DEFAULT_ALLOCATE,
    // the memory is allocated by user.
    // TODO(chenmeng.sky): add deprecated conditional compilation. If open deprecated compilation,
    // be/tarzan memory id will be invisible to user, and memory id can only be
    // UNIFY_ALLOCATED.
    blk->_memory_id = meta.memory_id == MemoryId::DEFAULT_ALLOCATED ? MemoryId::DEFAULT_ALLOCATED
                                                                    : MemoryId::UNIFY_ALLOCATED;
    blk->_refs = 1;
    blk->_data = static_cast<char*>(meta.virtual_address);
    blk->_size = 0;
    blk->_cap = meta.length;
    blk->_flag = BlockFlag::kSelfDestory;
    blk->_next = nullptr;
    blk->_mem_owner.free = deleter;
    blk->_mem_owner.param = meta.param;
    blk->_phy_addr = meta.physical_addr;
    BYTERPC_DCHECK(meta.module_id < IOBufStats::MAX_MODULE_NUM) << "module_id is too big";
    blk->_module_id = meta.module_id;
#ifdef BYTERPC_ENABLE_IOBUF_MONITOR
    if (cur_thread_iobuf_stat) {
        cur_thread_iobuf_stat->Add(meta.length, meta.memory_id, meta.module_id);
    }
#endif

    return blk;
}

IOBlock* IOBlock::TakeOwnership2(const MemoryDescriptor& meta, void (*deleter)(void*, void*)) {
    char* mem = static_cast<char*>(meta.virtual_address);
    IOBlock* blk = new (mem) IOBlock;
    blk->_memory_id = meta.memory_id;
    blk->_refs = 1;
    blk->_data = mem + sizeof(IOBlock);
    blk->_size = 0;
    blk->_cap = meta.length - sizeof(IOBlock);
    blk->_flag = BlockFlag::kNone;
    blk->_next = nullptr;
    blk->_mem_owner.free = deleter;
    blk->_mem_owner.param = meta.param;
    blk->_phy_addr = meta.physical_addr;
    BYTERPC_DCHECK(meta.module_id < IOBufStats::MAX_MODULE_NUM) << "module_id is too big";
    blk->_module_id = meta.module_id;
#ifdef BYTERPC_ENABLE_IOBUF_MONITOR
    if (cur_thread_iobuf_stat) {
        cur_thread_iobuf_stat->Add(meta.length, meta.memory_id, meta.module_id);
    }
#endif

    return blk;
}

IOBlock* IOBlock::AllocIOBlockHeader(size_t len) {
    IOBlock* blk = s_ioblock_cache.GetIOBlock();
    if (BYTERPC_UNLIKELY(!blk)) {
        return nullptr;
    }

    blk->_refs = 1;
    // NOTE: set _size same with _cap directly HERE
    blk->_size = len;
    blk->_cap = len;
    blk->_flag = BlockFlag::kSelfDestory;
    blk->_next = nullptr;

    return blk;
}

void IOBlock::release_block() {
    if (_flag & BlockFlag::kSelfDestory) {
#ifdef BYTERPC_ENABLE_IOBUF_MONITOR
        // `cur_thread_iobuf_stat' maybe was already dectructed when recycle
        // ioblock inside thread-local IOBlockChain.
        if (cur_thread_iobuf_stat) {
            cur_thread_iobuf_stat->Remove(_cap, _memory_id, _module_id);
        }
#endif
        _mem_owner.free(_data, _mem_owner.param);
        s_ioblock_cache.PutIOBlock(this);
    } else {
#ifdef BYTERPC_ENABLE_IOBUF_MONITOR
        // `cur_thread_iobuf_stat' maybe was already dectructed when recycle
        // ioblock inside thread-local IOBlockChain.
        if (cur_thread_iobuf_stat) {
            cur_thread_iobuf_stat->Remove(_cap + sizeof(IOBlock), _memory_id, _module_id);
        }
#endif
        _mem_owner.free(this, _mem_owner.param);
    }
}

}  // namespace byterpc
