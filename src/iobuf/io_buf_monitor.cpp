// Copyright (c) 2021, ByteDance Inc. All rights reserved.

#include "iobuf/io_buf_monitor.h"

#include <byte/string/number.h>
#include <byte/thread/this_thread.h>

#include "byterpc/byterpc_flags.h"
#include "metrics/byterpc_metrics.h"

namespace byterpc {

thread_local IOBufStats* cur_thread_iobuf_stat = nullptr;

class IOBufStatWatcher : public TimeEventHandler {
public:
    void HandleTimeEvent() override {
        cur_thread_iobuf_stat->ReportMetrics();
    }
};

IOBufStats::IOBufStats() : _watcher_ev_reg(nullptr), _add_timer(false) {
    _iobuf_stat_watcher.reset(new IOBufStatWatcher());
    int tid = byte::ThisThread::GetId();
    for (size_t i = 0; i < static_cast<size_t>(MemoryId::NUM_MEMORY_ID); ++i) {
        for (size_t j = 0; j < MAX_MODULE_NUM; ++j) {
            _used_memory[i][j] = METRICS_byterpc_iobuf_used_memory()->GetMetric(
                {{rpctagk::kThreadId, byte::NumberToString(tid)},
                 {rpctagk::kMemoryId, byte::NumberToString(i)},
                 {rpctagk::kModuleId, byte::NumberToString(j)}});
            _used_block[i][j] = METRICS_byterpc_iobuf_nblock()->GetMetric(
                {{rpctagk::kThreadId, byte::NumberToString(tid)},
                 {rpctagk::kMemoryId, byte::NumberToString(i)},
                 {rpctagk::kModuleId, byte::NumberToString(j)}});
        }
    }
}

IOBufStats::~IOBufStats() {
    if (_watcher_ev_reg && _add_timer) {
        _watcher_ev_reg->RemoveTimeConsumer(_timeout_iter);
    }
    // report before exit
    ReportMetrics(false);

    for (size_t i = 0; i < static_cast<size_t>(MemoryId::NUM_MEMORY_ID); ++i) {
        for (size_t j = 0; j < MAX_MODULE_NUM; ++j) {
            _used_memory[i][j] = nullptr;
            _used_block[i][j] = nullptr;
        }
    }
    _watcher_ev_reg = nullptr;
    _add_timer = false;
}

void IOBufStats::Init() {
    if (!FLAGS_byterpc_enable_iobuf_stats_monitor) {
        return;
    }

    if (!cur_thread_iobuf_stat) {
        cur_thread_iobuf_stat = new IOBufStats();
    }
}

void IOBufStats::StartMonitor(EventRegistry* ev_reg) {
    if (!FLAGS_byterpc_enable_iobuf_stats_monitor) {
        return;
    }

    if (!cur_thread_iobuf_stat) {
        cur_thread_iobuf_stat = new IOBufStats();
    }
    cur_thread_iobuf_stat->SetEventRegistry(ev_reg);
}

void IOBufStats::StopMonitor() {
    if (cur_thread_iobuf_stat) {
        delete cur_thread_iobuf_stat;
        cur_thread_iobuf_stat = nullptr;
    }
}

void IOBufStats::ReportMetrics(bool reg_timer) {
    for (size_t i = 0; i < static_cast<size_t>(MemoryId::NUM_MEMORY_ID); ++i) {
        for (size_t j = 0; j < MAX_MODULE_NUM; ++j) {
            if (_block_mem[i][j] > 0) {
                _used_memory[i][j]->Set(_block_mem[i][j]);
                _used_block[i][j]->Set(_nblock[i][j]);
            }
        }
    }

    // Register timer for next round
    if (reg_timer) {
        AddIOBufStatWatcher();
    }
}

void IOBufStats::SetEventRegistry(EventRegistry* ev_reg) {
    if (_watcher_ev_reg) {
        // _watcher_ev_reg is already set by other transport
        return;
    }

    _watcher_ev_reg = ev_reg;
    if (!_add_timer) {
        AddIOBufStatWatcher();
    }
}

void IOBufStats::AddIOBufStatWatcher() {
    _add_timer = true;
    _timeout_iter = _watcher_ev_reg->AddTimeConsumer(
        _iobuf_stat_watcher.get(), FLAGS_byterpc_iobuf_stats_mon_interval_ms * 1000UL);
}

}  // namespace byterpc
