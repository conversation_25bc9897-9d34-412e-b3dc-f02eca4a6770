// Copyright (c) 2021, ByteDance Inc. All rights reserved.
#pragma once

#include "byterpc/io_buf.h"

#include <string>

#include <boost/intrusive/list.hpp>

namespace byterpc {

struct IOBlockRefLink : public boost::intrusive::list_base_hook<> {
    IOBlockRef block_ref;

    IOBlockRefLink() : block_ref() {}
};

class IORbufPipe {
public:
    IORbufPipe();
    ~IORbufPipe();

    // Pre-allocate a IOBlockRef with partial-initialized IOBlock header.
    // @param nbytes size of BlockRef
    IOBlock* PreAllocIOBlock(size_t nbytes);

    // Attach the data in `ref' into rbuf pipe w/o memcpy.
    // used for tcp transport.
    void append(const IOBlockRef& ref);

    // Copy min(nbytes, size()) bytes to `out'.
    // Return bytes copied.
    size_t copy_to(char* out, size_t nbytes, size_t pos = 0) const;

    // Attach min(n, length()) bytes to `buf' w/o memcpy.
    size_t copy_to(IOBuf* buf, size_t nbytes);

    // Attach min(n, length()) bytes to `pipe' w/o memcpy.
    size_t copy_to(IORbufPipe* pipe, size_t nbytes);

    // Pop the min(nbytes, size()) bytes in front of rbuf pipe
    // Return bytes popped.
    size_t pop_front(size_t nbytes);

    // Cut and copy min(nbytes, size()) bytes to `out'.
    // Returns bytes cutted.
    size_t cut_to(char* out, size_t nbytes);
    size_t cut_to(std::string* out, size_t nbytes);

    // Attach min(nbytes, size()) bytes to `empty_buf' w/o memcpy.
    // Return bytes copied.
    // NOTE: `empty_buf' MUST be empty!!!
    size_t cut_to(IOBuf* empty_buf, size_t nbytes);

    bool empty() const {
        return _size == 0;
    }

    size_t size() const {
        return _size;
    }

    size_t length() const {
        return _size;
    }

    IOBlockRef& front_ref() {
        return _io_block_list.front().block_ref;
    }

    const IOBlockRef& front_ref() const {
        return _io_block_list.front().block_ref;
    }

    void clear();

    // Convert rbuf pipe data to std::string
    std::string to_string(size_t size);

private:
    static const size_t IOBLOCK_REF_CACHE_MAX_COUNT = 8192;

    IOBlockRefLink* get_ioblock_ref_link();
    void put_ioblock_ref_link(IOBlockRefLink* ref_link);

    // IOBlockRef cache
    IOBlockRefLink* _ioblock_ref_cache[IOBLOCK_REF_CACHE_MAX_COUNT];
    size_t _ioblock_ref_cache_size;

    // recv buf pipe
    boost::intrusive::list<IOBlockRefLink> _io_block_list;
    // total size
    size_t _size;
};

}  // namespace byterpc
