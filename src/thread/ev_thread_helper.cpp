//  Copyright (c) 2021, ByteDance Inc. All rights reserved.

#include "byterpc/thread/ev_thread_helper.h"

#include <byte/concurrent/count_down_latch.h>
#include <byte/include/assert.h>

#include <utility>

#include "byterpc/exec_ctx.h"
#include "byterpc/thread/priority_task.h"
#include "byterpc/util/compiler_specific.h"
#include "byterpc/util/logging.h"
#include "byterpc/util/numautils.h"

namespace byterpc {

EvThreadHelper::EvThreadHelper()
    : _thread_name(""),
      _tid(0),
      _cpu_id(-1),
      _transport(NUM_TRANSPORT_TYPE),
      _enable_napi(false),
      _enable_fiber(false),
      _fiber_mode_option(),
      _stopped(false),
      _state(ThreadState::SPAWN),
      _ready_to_invoke(false),
      _init_function(nullptr),
      _fini_function(nullptr),
      _thread_context(nullptr),
      _init_success(false),
      _init_function_latch(nullptr) {}

EvThreadHelper::~EvThreadHelper() {
    BYTE_ASSERT(_state != ThreadState::RUNNING) << " thread is still running";
}

bool EvThreadHelper::Init(const ThreadOptions& thread_options) {
    _thread_name = thread_options._thread_name;
    _cpu_id = thread_options._cpu_id;
    _transport = thread_options._transport;
    _enable_napi = thread_options._enable_napi;
    _enable_fiber = thread_options._enable_fiber;
    _fiber_mode_option = thread_options._fiber_mode_option;
    _init_function = std::move(thread_options._init_function);
    _fini_function = std::move(thread_options._fini_function);
    _state = ThreadState::INIT;
    return true;
}

bool EvThreadHelper::Start() {
    if (_state != ThreadState::INIT) {
        BYTERPC_LOG(ERROR) << "Fail to Start EvThread, cur_state=" << _state;
        return false;
    }

    _init_success = false;
    _init_function_latch.reset(new byte::CountDownLatch(1));
    int ret = pthread_create(&_tid, NULL, &EvThreadHelper::LoopEntry, this);
    if (ret) {
        BYTERPC_LOG(ERROR) << "pthread_create failed! errno: " << ret;
        return false;
    }
    _init_function_latch->Wait();

    if (_init_success) {
        _state = ThreadState::RUNNING;
        return true;
    } else {
        if (pthread_join(_tid, NULL)) {
            BYTERPC_LOG(ERROR) << "pthread_join failed!. errno: " << ret;
        }
        _tid = 0;
        return false;
    }
}

bool EvThreadHelper::Start(std::function<bool()>&& init_function) {
    if (_init_function) {
        LOG(WARNING) << "init_function is already set";
        return false;
    }
    _init_function = std::move(init_function);
    return Start();
}

void* EvThreadHelper::LoopEntry(void* args) {
    EvThreadHelper* th = static_cast<EvThreadHelper*>(args);
    SetCurrentThread(th);
    th->LoopImpl();
    SetCurrentThread(nullptr);

    return nullptr;
}

void EvThreadHelper::LoopImpl() {
    {
        byte::ScopedCountDownLatch scoped_latch(_init_function_latch.get());

        if (!_thread_name.empty()) {
            // Set thread name for easy debugging.
            SetCurrentThreadName(_thread_name.c_str());
        }

        if ((_cpu_id >= 0) && !util::bind_this_to_core(static_cast<size_t>(_cpu_id))) {
            BYTERPC_LOG(ERROR) << "bind event thread failed! cpu_id: " << _cpu_id;
        }

        // construct thread-local `ExecCtxNode`
        ExecCtx ctx(LOOP_UNTIL_QUIT);
        if (_enable_napi && (0 != ExecCtx::EnableNAPIMode())) {
            BYTERPC_LOG(ERROR) << "fail to enable NAPI mode";
            return;
        }
        if (_enable_fiber && (0 != ExecCtx::EnableFiberMode(_fiber_mode_option))) {
            BYTERPC_LOG(ERROR) << "fail to enable fiber mode";
            return;
        }
        ExecCtx::GetOrNewThreadEventRegistry(_transport);
        _thread_context = ExecCtx::GetCurrentThreadContext();
        BYTERPC_CHECK(_thread_context != nullptr);

        // call `_init_function`
        if (_init_function) {
            if (!_init_function()) {
                BYTERPC_LOG(ERROR) << "Init function fails";
                return;
            }
            _init_function = nullptr;
        }
        _ready_to_invoke = true;
        _init_success = true;
    }

    // Start event-driven loop
    ExecCtx::LoopUntilQuit();

    if (_fini_function) {
        _fini_function();
        _fini_function = nullptr;
    }
}

bool EvThreadHelper::Stop() {
    ThreadState expected = ThreadState::RUNNING;
    if (!_state.compare_exchange_strong(expected, ThreadState::STOPPING)) {
        BYTERPC_LOG(WARNING) << "Fail to Stop EvThread, cur_state=" << _state;
        return false;
    }

    // wait until ev loop is quit
    WaitLoopQuit();

    _stopped = true;
    return true;
}

void EvThreadHelper::WaitLoopQuit() {
    if (GetCurrentThread() == this) {
        // All pending tasks will be handled before `kQuit` event
        ExecCtx::QuitLoop();
        return;
    }

    byte::CountDownLatch latch(1);
    InvokeImpl(NewClosure(this, &EvThreadHelper::QuitLoop, &latch), true);
    latch.Wait();
}

void EvThreadHelper::QuitLoop(byte::CountDownLatch* latch) {
    ExecCtx::QuitLoop();
    latch->CountDown();
}

bool EvThreadHelper::Join() {
    int ret = pthread_join(_tid, NULL);
    if (ret) {
        BYTERPC_LOG(ERROR) << "pthread_join failed!. errno: " << ret;
        return false;
    }
    _tid = 0;
    _state = ThreadState::STOPPED;
    return true;
}

bool EvThreadHelper::Invoke(Closure<void>* callback) {
    return InvokeImpl(callback, false);
}

bool EvThreadHelper::InvokeImpl(Closure<void>* callback, bool force) {
    if (_state != ThreadState::RUNNING && !force) {
        BYTERPC_LOG(WARNING) << "Evthread is NOT running, EvThread: " << this;
        return false;
    }

    // busy-waiting until `_thread_context` is initialized
    while (!_ready_to_invoke) {
    }
    return _thread_context->Invoke(callback);
}

pthread_t EvThreadHelper::GetThreadId() const {
    return _tid;
}

}  // namespace byterpc
