// Copyright (c) 2024, ByteDance Inc. All rights reserved.
#pragma once

#include <memory>

#include "byterpc/mem/region_alloc_hook.h"
#include "mem/region.h"

namespace byterpc {

using region_alloc_callback_t = void (*)(void* addr, size_t size, void** user_data);
using region_dealloc_callback_t = void (*)(void* addr, size_t size, void* user_data);
struct RegionAllocCallBack {
    RegionAllocCallBack() : on_alloc(nullptr), on_dealloc(nullptr) {}
    RegionAllocCallBack(region_alloc_callback_t on_alloc, region_dealloc_callback_t on_dealloc)
        : on_alloc(on_alloc), on_dealloc(on_dealloc) {}
    region_alloc_callback_t on_alloc;
    region_dealloc_callback_t on_dealloc;
};

class RegionAllocator {
public:
    RegionAllocator(const RegionAllocHook& hugepage_region_alloc_hook,
                    const RegionAllocHook& default_region_alloc_hook,
                    const RegionAllocCallBack& region_alloc_call_back);
    ~RegionAllocator() = default;
    Region* AllocRegion(size_t region_size, int numa_id, bool try_hugepage);
    void DeallocRegion(Region* region);

private:
    const RegionAllocHook _hugepage_region_alloc_hook;
    const RegionAllocHook _default_region_alloc_hook;
    const RegionAllocCallBack _region_alloc_call_back;
};

}  // namespace byterpc
