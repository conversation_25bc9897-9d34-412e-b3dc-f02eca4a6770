// Copyright (c) 2024, ByteDance Inc. All rights reserved.
#pragma once

#include <bemalloc.h>

#include <memory>
#include <vector>

#include <byte/thread/thread_local.h>

#include "byterpc/util/logging.h"
#include "mem/memory_pool.h"

namespace byterpc {

struct BemallocGlobalStatistics {
    //! Per span cached count statistics: e.g. span_cached[1] contains the number of spans in
    //! global cache whose size is 2 * 64KiB.
    size_t span_cached[64];
};

// TODO(fza): support heap stat
struct BemallocHeapStatistics {
    //     //! Current number of bytes available in thread size class caches for small and medium
    //     sizes
    //     //! (<32KiB)
    //     size_t size_cached;
    //     //! Per span cached count statistics: e.g. span_cached[1] contains the number of spans in
    //     //! thread cache whose size is 2 * 64KiB.
    //     size_t span_cached[64];
    //     // heap belong to thread id
    //     pid_t thread_id;
    //     // heap belong to subpool
    //     int pool_index;
};

struct BemallocAPILevelStatistics {
    size_t allocate_size;
};

class BemallocMemoryPool;
// Thread local heap for memory allocation.
class BemallocContext {
public:
    // Initialize a thread heap using the given context.
    explicit BemallocContext(BemallocMemoryPool* mem_pool, size_t idx)
        : _mem_pool(mem_pool), _idx(idx), _bemalloc_ctx(nullptr) {}

    ~BemallocContext() {
        if (_bemalloc_ctx) {
            bemalloc_context_finalize(_bemalloc_ctx);
        }
    }

    bool InitCtx(const bemalloc_context_config_t& config) {
        _bemalloc_ctx = bemalloc_context_initialize(&config);
        if (BYTERPC_UNLIKELY(!_bemalloc_ctx)) {
            return false;
        }
        return true;
    }

    bemalloc_context_t* Ctx() const {
        return _bemalloc_ctx;
    }

    BemallocMemoryPool* MemPool() const {
        return _mem_pool;
    }

    size_t Idx() const {
        return _idx;
    }

private:
    BemallocMemoryPool* _mem_pool;
    size_t _idx;
    bemalloc_context_t* _bemalloc_ctx;
};

// Thread local heap for memory allocation.
class BemallocHeap {
public:
    // Initialize a thread heap using the given context.
    explicit BemallocHeap(bemalloc_context_t* ctx) : _heap(bemalloc_heap_acquire(ctx)) {
        BYTERPC_CHECK(ctx);
        BYTERPC_CHECK(_heap);
    }

    ~BemallocHeap() {
        bemalloc_heap_release(_heap);
    }

    // Allocate a block from the heap with at least the given size. Note
    // that this function MUST NOT be called in other threads!
    void* Alloc(size_t size) {
        return bemalloc(_heap, size);
    }

    // Allocate a block aligned to the given alignment
    void* AlignedAlloc(size_t alignment, size_t size) {
        return bealigned_alloc(_heap, alignment, size);
    }

    void AdoptDeferred() {
        bemalloc_heap_cache_adopt_deferred(_heap);
    }

private:
    bemalloc_heap_t* _heap;
};

class BemallocMemoryPool : public MemoryPool {
public:
    explicit BemallocMemoryPool(const RegionAllocator& region_allocator);
    ~BemallocMemoryPool() override;
    void* Alloc(size_t size, int numa_id = -1) override;
    void* AlignedAlloc(size_t alignment, size_t size, int numa_id = -1) override;
    void Dealloc(void* addr) override;
    void* GetUserDataQuickly(const void* addr) override;

    // Recycle deferred spans that are deallocated in another thread. This serves as garbage
    // collection procedure.
    void AdoptDeferred();

    // return the statistics for all nodes
    std::vector<BemallocGlobalStatistics> GetGlobalStatistics() const;

    // return the statistics for all active heaps
    std::vector<BemallocHeapStatistics> GetHeapStatistics() const;

    // return the statistics for Allocate/Deallocate call,
    // and there's no need to count by different NUMA node here
    BemallocAPILevelStatistics GetAPILevelMemStatistics() const;

private:
    void InitThreadHeap(size_t idx);
    static void* MemoryMapWrapper(void* idx, size_t size, size_t* offset, void** data);
    static void MemoryUnMapWrapper(
        void* idx, void* address, size_t size, size_t offset, size_t release, void* data);

private:
    // each numa has a context
    std::vector<std::unique_ptr<BemallocContext>> _bemalloc_ctxs;
    // TODO(dj): do not use byte::ThreadLocal
    // counter for per thread size of allocated memory
    byte::ThreadLocal<uint64_t> _allocated_size;

private:
    // each numa has a thread_local heap
    static thread_local std::vector<std::unique_ptr<BemallocHeap>> thread_heaps;
};

}  // namespace byterpc
