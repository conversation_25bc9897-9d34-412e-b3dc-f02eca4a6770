// Copyright (c) 2024, ByteDance Inc. All rights reserved.
#pragma once

#include <memory>
#include <vector>

#include <boost/intrusive/list.hpp>

#include "byterpc/mem/mem_page_info.h"

namespace byterpc {

/* Region and Segment

      low addr                                                                          high addr
         ____________________________________________________________________________________
        |                                                                                    |
        |                                       Region                                       |
        |------------------------------------------------------------------------------------|
        |           |         (first)           |           |        (second)           |    |
        | alignment |         Segment           | alignment |         Segment           | ...|
        |___________|___________________________|___________|___________________________|____|
        :           :                           :
        :           :<------------------------->:
        :           :            \
        :           :             \ _segment_size
        :           :
        :      aligned by _segment_alignment
        :
 _mem_page_info.vaddr

*/

class Region;
struct Segment {
    void* addr;
    size_t size;
    Region* belong_to_region;
};

enum RegionStatus {
    UNINIT,  // region has not been split into segements
    READY,   // region has been split into segements, but no segment is in use
    INUSE,   // some segments are in-use
    FULL     // all segments are in-use
};

using boost_auto_unlink_hook =
    boost::intrusive::list_base_hook<boost::intrusive::link_mode<boost::intrusive::auto_unlink>>;
class Region : public boost_auto_unlink_hook {
public:
    Region(const MemPageInfo& mem_page_info, int numa_id, bool is_hugepage, void* user_data);
    bool Init(size_t segment_size, size_t segment_alignment);
    void Reset();
    Segment* GetSegment();
    void ReturnSegment(Segment* segment);

    RegionStatus Status() const {
        return _status;
    }
    const MemPageInfo& RegionInfo() const {
        return _mem_page_info;
    }
    size_t SegmentSize() const {
        return _segment_size;
    }
    size_t SegmentAlignment() const {
        return _segment_alignment;
    }
    size_t FreeSegmentNum() const {
        return _free_segment_num;
    }
    int NumaId() const {
        return _numa_id;
    }
    void* Data() const {
        return _user_data;
    }
    bool IsHugePage() const {
        return _is_hugepage;
    }
    void unlink() {
        boost_auto_unlink_hook::unlink();
    }
    bool is_linked() {
        return boost_auto_unlink_hook::is_linked();
    }

private:
    const MemPageInfo _mem_page_info;
    const int _numa_id;
    const bool _is_hugepage;
    void* _user_data;  // currently only for ByteExpress's MemoryRegion
    RegionStatus _status;

    size_t _segment_size;
    size_t _segment_alignment;
    std::vector<Segment> _segments;
    std::vector<Segment*> _free_segment_list;
    size_t _free_segment_num;
};

}  // namespace byterpc
