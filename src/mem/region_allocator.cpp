// Copyright (c) 2024, ByteDance Inc. All rights reserved.

#include "mem/region_allocator.h"
#include "byterpc/util/logging.h"
#include "mem/hub.h"

namespace byterpc {

RegionAllocator::RegionAllocator(const RegionAllocHook& hugepage_region_alloc_hook,
                                 const RegionAllocHook& default_region_alloc_hook,
                                 const RegionAllocCallBack& region_alloc_call_back)
    : _hugepage_region_alloc_hook(hugepage_region_alloc_hook),
      _default_region_alloc_hook(default_region_alloc_hook),
      _region_alloc_call_back(region_alloc_call_back) {
    BYTERPC_CHECK((hugepage_region_alloc_hook.alloc && hugepage_region_alloc_hook.dealloc) ||
                  (!hugepage_region_alloc_hook.alloc && !hugepage_region_alloc_hook.dealloc));
    BYTERPC_CHECK(default_region_alloc_hook.alloc);
    BYTERPC_CHECK(default_region_alloc_hook.dealloc);
}

Region* RegionAllocator::AllocRegion(size_t region_size, int numa_id, bool try_hugepage) {
    size_t page_size = 0;
    void* addr = nullptr;
    bool is_hugepage = false;

    // try to alloc hugepage
    if (try_hugepage && _hugepage_region_alloc_hook.alloc) {
        addr = _hugepage_region_alloc_hook.alloc(region_size, &page_size, numa_id);
        if (addr) {
            is_hugepage = true;
        }
    }

    // try to alloc default mem
    if (!addr) {
        addr = _default_region_alloc_hook.alloc(region_size, &page_size, numa_id);
        if (BYTERPC_UNLIKELY(!addr)) {
            BYTERPC_LOG(ERROR) << "Fail to allocate region memory with size = " << region_size
                               << "B, in numa: " << numa_id;
            return nullptr;
        }
    }

    // Every time we got a page, we submit this page to hub.
    MemPageInfo page_info(
        HolderType::Unify, addr, nullptr, region_size, region_size / page_size, page_size);
    if (BYTERPC_UNLIKELY(g_mem_page_hub.SubmitHoldPages({page_info}) != 0)) {
        BYTERPC_LOG(ERROR) << "Fail to SubmitHoldPages " << page_info;
        const RegionAllocHook& alloc_hook =
            is_hugepage ? _hugepage_region_alloc_hook : _default_region_alloc_hook;
        alloc_hook.dealloc(addr, region_size);
        return nullptr;
    }

    void* data = nullptr;
    if (_region_alloc_call_back.on_alloc) {
        _region_alloc_call_back.on_alloc(addr, region_size, &data);
    }

    return new Region(page_info, numa_id, is_hugepage, data);
}

void RegionAllocator::DeallocRegion(Region* region) {
    BYTERPC_DCHECK(region->Status() == RegionStatus::UNINIT);

    if (_region_alloc_call_back.on_dealloc) {
        _region_alloc_call_back.on_dealloc(
            region->RegionInfo().vaddr, region->RegionInfo().len, region->Data());
    }

    if (g_mem_page_hub.DeleteHoldPages(region->RegionInfo()) != 0) {
        BYTERPC_LOG(ERROR) << "Fail to DeleteHoldPages " << region->RegionInfo();
    }

    const RegionAllocHook& alloc_hook =
        region->IsHugePage() ? _hugepage_region_alloc_hook : _default_region_alloc_hook;
    alloc_hook.dealloc(region->RegionInfo().vaddr, region->RegionInfo().len);

    delete region;
}

}  // namespace byterpc
