// Copyright (c) 2024, ByteDance Inc. All rights reserved.
#pragma once

#include <google/protobuf/message.h>
#include <google/protobuf/service.h>
#include <google/protobuf/stubs/callback.h>

#include <memory>
#include <string>
#include <utility>
#include <vector>

#include "byterpc/controller.h"
#include "byterpc/util/object_pool.h"
#include "proto/protobuf_byte_rpc_meta.pb.h"
#include "protocol/message_base.h"
#include "rpc/rpc_service_registry.h"
#include "rpc/server_socket.h"

namespace byterpc {

class ThreadContext;
class ProtobufByteServerController;

class ProtobufByteServerMessageHandler : public ServerMessageHandler {
public:
    ProtobufByteServerMessageHandler();

    std::string GetMessageHeader() const override;
    ProtocolType GetProtocolType() const override;

    ParseError HandleRequest(ServerSocket* socket, IORbufPipe* rbuf_pipe) override;

private:
    ProtobufByteServerController* GetSeverController(ServerSocket* ss);
};

class ProtobufByteServerController final : public Controller {
    BYTERPC_OBJECT_POOL_FRIENDS;
    friend class ProtobufByteServerMessageHandler;

public:
    ProtobufByteServerController();

    ~ProtobufByteServerController();

    void OnRecycle() override;

    void DoRecycle() override;

    ParseError ProcessRequest(IOBuf* meta_buf, IOBuf* payload_buf);

    inline util::EndPoint local_side() override {
        return _parent->local_side();
    }

    inline util::EndPoint remote_side() override {
        return _parent->remote_side();
    }

    void Reset() override;

    ProtocolType request_protocol() const override {
        return PROTOCOL_PROTOBUF_BYTE_STD;
    }

    void InstallRdmaSendBuf(const IOBuf& send_buf,
                            const RdmaBufInfo& remote_recv_buf_info) override {
        _send_bufs.emplace_back(std::make_unique<IOBuf>(send_buf), remote_recv_buf_info);
    }

    void InstallRdmaRecvBuf(const IOBlockRef& recv_buf) override {
        _to_recv_bufs.emplace_back(recv_buf);
    }

    std::vector<IOBlockRef> ReleaseAllRdmaReceivedBufs() override {
        return std::move(_received_bufs);
    }

    std::vector<RdmaBufInfo> GetRemoteRdmaRecvBufInfos() const override {
        return _remote_recv_buf_infos;
    }

private:
    util::owner_ptr<ServerSocket> _parent;
    std::unique_ptr<proto::ProtobufByteMeta> _meta;
    std::unique_ptr<google::protobuf::Message> _req;
    std::unique_ptr<google::protobuf::Message> _resp;
    MethodStatus* _method_status;
    uint64_t _received_timestamp;
    // server send rdma bufs to client
    std::vector<std::pair<std::unique_ptr<IOBuf>, RdmaBufInfo>> _send_bufs;
    // server-prepared rdma bufs to recv data
    std::vector<IOBlockRef> _to_recv_bufs;
    // server received rdma bufs, which client sends to server
    std::vector<IOBlockRef> _received_bufs;
    // client-prepared rdma bufs to recv data
    std::vector<RdmaBufInfo> _remote_recv_buf_infos;

    void SendResponse(ThreadContext* thread_context);
    void SendResponseImpl();

    void RecycleBuf();
};

}  // namespace byterpc
