// Copyright (c) 2021, ByteDance Inc. All rights reserved.
#pragma once

#include <google/protobuf/message.h>
#include <google/protobuf/service.h>
#include <google/protobuf/stubs/callback.h>

#include <memory>
#include <string>

#include "byterpc/util/object_pool.h"
#include "protocol/byte_std/byte_message_serdes.h"
#include "protocol/message_base.h"
#include "rpc/client_socket.h"
#include "rpc/stateful_controller.h"

namespace byterpc {

class ByteClientMessageHandler : public ClientMessageHandler {
public:
    ByteClientMessageHandler() {}

    std::string GetMessageHeader() const override;
    ProtocolType GetProtocolType() const override;

    ParseError HandleResponse(ClientSocket* socket, IORbufPipe* rbuf_pipe) override;

    Controller* CreateSessionController(int64_t rpc_timeout_us,
                                        CrcMode crc_mode = CrcMode::TYPE_NONE) override;

    bool SupportCrc() const override {
        return true;
    }
};

// The concrete class that triggers a RPC client call;
class ByteRpcCall : public StatefulController {
    BYTERPC_OBJECT_POOL_FRIENDS;

public:
    ByteRpcCall();

    explicit ByteRpcCall(int64_t rpc_timeout_us);

    // Entry of client side RPC
    void IssueRPC(ClientSocket* socket,
                  const google::protobuf::MethodDescriptor* method,
                  const google::protobuf::Message* request,
                  google::protobuf::Message* response,
                  google::protobuf::Closure* done) override;

    void ProcessResponse(const ByteStdSerDes::ResponseMeta& meta,
                         IORbufPipe* rbuf_pipe,
                         uint32_t before_payload_size,
                         uint32_t payload_size);

    void Reset() override;

    void DoRecycle() override;

    ProtocolType request_protocol() const override {
        return PROTOCOL_BYTE_STD;
    }

private:
    google::protobuf::Message* _resp;
    google::protobuf::Closure* _done;

    void OnRecycle() override;

    // In byterpc, a controller deletes itself automatically when the session
    // reaches a complete state. Please do not try to delete a controller
    // explicitly in the code. If you do want to terminate it prematurely,
    // use `SetFailed` to transit it to a complete state;
    ~ByteRpcCall() override;

    void RecycleBuf();
};

}  // namespace byterpc
