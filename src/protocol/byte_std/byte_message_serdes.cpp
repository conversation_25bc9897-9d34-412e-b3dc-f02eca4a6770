// Copyright (c) 2021, ByteDance Inc. All rights reserved.
#include "protocol/byte_std/byte_message_serdes.h"

#include <google/protobuf/io/zero_copy_stream_impl_lite.h>
#include <google/protobuf/message.h>

#include "byterpc/byterpc_flags.h"
#include "byterpc/io_buf.h"
#include "byterpc/util/compiler_specific.h"
#include "byterpc/util/logging.h"
#include "iobuf/io_rbuf_pipe.h"

namespace byterpc {

// Notes:
// 1. 12-byte header [TRPC][body_size][meta_size]
// 2. body_size and meta_size are in network byte order
// 3. Use service->full_name() + method_name to specify the method to call
// 4. `attachment_size' is set iff request/response has attachment
// 5. Not use protobuf for rpc meta in consideration of performance
// 6. Ignore endianess conversion in consideration of performance

static inline bool IsByteStdMessage(void* dummy) {
    return *(const uint32_t*)dummy == *(const uint32_t*)"TRPC";
}

ParseError ByteStdSerDes::ParseRpcHeader(IORbufPipe* rbuf_pipe,
                                         bool is_request,
                                         uint32_t* meta_size,
                                         uint32_t* body_size,
                                         ExtendHeader* extend_header) {
    size_t extend_header_length = 0;
#ifdef BYTERPC_ENABLE_EXTEND_HEADER
    ParseError extend_pr = ExtendHeaderSerDes::MatchExtendHeader(rbuf_pipe);
    if (PARSE_OK != extend_pr && PARSE_ERROR_TRY_OTHERS != extend_pr) {
        return extend_pr;
    }
    if (extend_pr == PARSE_OK) {
        extend_header_length = ExtendHeaderSerDes::FIXED_HEADER_LENGTH;
    }
#endif

    const size_t source_size = rbuf_pipe->size();
    const uint32_t fix_meta_size = is_request ? REQUEST_META_FIX_SIZE : RESPONSE_META_FIX_SIZE;
    if (BYTERPC_UNLIKELY(source_size < extend_header_length + 12 + fix_meta_size)) {
        // data is not enough,
        // check magic number if `source' length exceeds 4.
        if (source_size >= extend_header_length + 4) {
            char magic_buf[4];
            rbuf_pipe->copy_to(magic_buf, sizeof(magic_buf), extend_header_length);
            if (!IsByteStdMessage(magic_buf)) {
                return PARSE_ERROR_TRY_OTHERS;
            }
        }
        return PARSE_ERROR_NOT_ENOUGH_DATA;
    }

    auto& front_ref = rbuf_pipe->front_ref();
    if (BYTERPC_LIKELY(front_ref.length() >= extend_header_length + 12)) {
        // fast path for parsing header
        char* header = const_cast<char*>(front_ref.data() + extend_header_length);
        if (!IsByteStdMessage(header)) {
            return PARSE_ERROR_TRY_OTHERS;
        }
        util::RawUnpacker(header + 4).unpack32(*body_size).unpack32(*meta_size);
    } else {
        char header_buf[12];
        rbuf_pipe->copy_to(header_buf, sizeof(header_buf), extend_header_length);
        if (!IsByteStdMessage(header_buf)) {
            return PARSE_ERROR_TRY_OTHERS;
        }
        util::RawUnpacker(header_buf + 4).unpack32(*body_size).unpack32(*meta_size);
    }

    if (BYTERPC_UNLIKELY(*body_size > FLAGS_byterpc_max_body_size)) {
        BYTERPC_LOG(ERROR) << "body_size=" << *body_size << " is too large,"
                           << " FLAGS_byterpc_max_body_size=" << FLAGS_byterpc_max_body_size;
        return PARSE_ERROR_TOO_BIG_DATA;
    } else if (source_size < extend_header_length + 12 + *body_size) {
        return PARSE_ERROR_NOT_ENOUGH_DATA;
    }

    if (BYTERPC_UNLIKELY(*meta_size > *body_size)) {
        BYTERPC_LOG(ERROR) << "meta_size=" << *meta_size
                           << " is bigger than body_size=" << *body_size;
        return PARSE_ERROR_ABSOLUTELY_WRONG;
    }
    if (is_request && BYTERPC_UNLIKELY(*meta_size > REQUEST_META_MAX_SIZE)) {
        BYTERPC_LOG(ERROR) << "meta_size=" << *meta_size
                           << " is bigger than max limit=" << REQUEST_META_MAX_SIZE;
        return PARSE_ERROR_ABSOLUTELY_WRONG;
    }

#ifdef BYTERPC_ENABLE_EXTEND_HEADER
    if (PARSE_OK == extend_pr) {
        // It will pop out data of length ExtendHeaderSerDes::FIXED_HEADER_LENGTH
        // when succeeds
        extend_pr = ExtendHeaderSerDes::ParseHeader(rbuf_pipe, extend_header);
        if (PARSE_OK != extend_pr) {
            return extend_pr;
        }
        rbuf_pipe->pop_front(extend_header_length);
    }
#endif

    return PARSE_OK;
}

}  // namespace byterpc
