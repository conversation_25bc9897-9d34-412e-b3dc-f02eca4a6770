// Copyright (c) 2021, ByteDance Inc. All rights reserved.

#include "byte_client_message_handler.h"

#include <utility>

#include "byterpc/exec_ctx.h"
#include "byterpc/io_buf_accessor.h"
#include "byterpc/util/logging.h"
#include "iobuf/io_block_pool.h"
#include "iobuf/io_rbuf_pipe.h"
#include "metrics/metrics_inl.h"
#include "protocol/extend_header.h"
#include "rpc/client_socket.h"
#include "rpc/exec_ctx_node.h"
#include "rpc/protobuf_serdes.h"
#include "rpc/time_profiler.h"
#include "util/tls_object_cache.h"

#ifdef BYTERPC_ENABLE_FIBER
#include <byte/fiber/fiber.h>
#endif

namespace byterpc {

struct ByteRpcCallDeleter {
    void operator()(ByteRpcCall* cntl) {
        util::return_tls_object(cntl);
    }
};

static thread_local LocalObjectCache<ByteRpcCall, ByteRpcCallDeleter> s_cntl_cache;

ByteRpcCall::ByteRpcCall() : StatefulController(), _resp(nullptr), _done(nullptr) {}

ByteRpcCall::ByteRpcCall(int64_t rpc_timeout_us)
    : StatefulController(rpc_timeout_us), _resp(nullptr), _done(nullptr) {}

ByteRpcCall::~ByteRpcCall() {
    RecycleBuf();
}

void ByteRpcCall::RecycleBuf() {
    if (_incoming_attachment) {
        ExecCtxNode::ReturnIOBuf(_incoming_attachment.release());
    }
}

void ByteRpcCall::IssueRPC(ClientSocket* socket,
                           const google::protobuf::MethodDescriptor* method,
                           const google::protobuf::Message* request,
                           google::protobuf::Message* response,
                           google::protobuf::Closure* done) {
    BYTERPC_CHECK(socket != nullptr);

    InitCorrelationId(socket);
    _client_socket->OwnerRef();
    _resp = response;
    _done = done;
#ifdef BYTERPC_ENABLE_FIBER
    _rpc_fiber = (ExecCtx::FiberMode() && !done) ? byte::fiber::GetCurrentFiber() : nullptr;
#endif

    do {
        // Check service name and method name size, need to shorter than FULL_METHOD_NAME_MAX_SIZE
        size_t service_name_size = method->service()->full_name().size();
        size_t full_service_name_size = service_name_size + method->name().size();
        BYTERPC_CHECK(full_service_name_size < FULL_METHOD_NAME_MAX_SIZE)
            << "The request full method name size is too large";

        size_t request_size = request->ByteSize();
        size_t meta_size = ByteStdSerDes::REQUEST_META_FIX_SIZE + full_service_name_size;
        size_t attach_size = _outgoing_attachment ? _outgoing_attachment->size() : 0;

        IOBlockRef blk_ref =
            NeedCalCrc()
                ? IOBlockRefPrivateAccessor::Move(
                      alloc_ioblock_resource<META_IOBLOCKSIZE::BYTESTD_REQUEST_META_SIZE +
                                             ExtendHeaderSerDes::FIXED_HEADER_LENGTH>())
                : IOBlockRefPrivateAccessor::Move(
                      alloc_ioblock_resource<META_IOBLOCKSIZE::BYTESTD_REQUEST_META_SIZE>());
        if (BYTERPC_UNLIKELY(blk_ref.invalid())) {
            std::stringstream ss;
            ss << remote_side();
            std::string error_text = "No memory to alloc request meta, remote_addr " + ss.str();
            MarkFailed(ENOMEM, error_text);
            ExecCtx::Schedule([this, error_text]() { this->SetFailed(ENOMEM, error_text); });
            break;
        }

        // pack rpc header
        char* header = NeedCalCrc() ? blk_ref.data() + ExtendHeaderSerDes::FIXED_HEADER_LENGTH
                                    : blk_ref.data();
        ByteStdSerDes::PackRpcHeader(header, meta_size, request_size + attach_size);
        // pack request meta
        ByteStdSerDes::RequestMeta* meta =
            reinterpret_cast<ByteStdSerDes::RequestMeta*>(header + ByteStdSerDes::RPC_HEADER_SIZE);
        meta->correlation_id = CorrelationId();
        meta->log_id = LogId();
        meta->attachment_size = attach_size;
        meta->service_name_size = service_name_size;
        memcpy(meta->full_method_name, method->service()->full_name().c_str(), service_name_size);
        memcpy(meta->full_method_name + service_name_size,
               method->name().c_str(),
               method->name().size());

        // Update IOBlockRef length
        IOBlockRefPrivateEditor blk_ref_edt(blk_ref);
        size_t used_size = NeedCalCrc() ? ByteStdSerDes::RPC_HEADER_SIZE + meta_size +
                                              ExtendHeaderSerDes::FIXED_HEADER_LENGTH
                                        : ByteStdSerDes::RPC_HEADER_SIZE + meta_size;
        BYTERPC_DCHECK(blk_ref.size() >= used_size);
        blk_ref_edt.backup(blk_ref.size() - used_size);

        std::unique_ptr<IOBuf> user_pb(nullptr);
        if (request_size > 0) {
            user_pb = std::make_unique<IOBuf>();
            // Now serialize request itself
            ProtobufSerDes::SerializePbToWriteBuffer(request, user_pb.get());
        }

        if (NeedCalCrc()) {
            IOBuf crc_buf;
            // meta
            crc_buf.append(blk_ref);
            crc_buf.pop_front(ExtendHeaderSerDes::FIXED_HEADER_LENGTH);
            // user pb
            if (user_pb) {
                crc_buf.append(*user_pb);
            }
            // attachment
            if (_crc_mode == CrcMode::TYPE_FULL && _outgoing_attachment) {
                crc_buf.append(*_outgoing_attachment);
            }
            uint32_t crc_value = ExtendHeaderSerDes::CalCrc32(&crc_buf);
            ExtendHeaderSerDes::PackHeader(blk_ref.data(), _crc_mode, crc_value);
        }

        if (user_pb) {
            _client_socket->SubmitWrite(
                this, std::move(blk_ref), std::move(user_pb), std::move(_outgoing_attachment));
        } else {
            _client_socket->SubmitWrite(this, std::move(blk_ref), std::move(_outgoing_attachment));
        }
    } while (false);

#ifdef BYTERPC_ENABLE_FIBER
    if (RpcFiberMode()) {
        // wait until response recieved or rpc failed
        byte::fiber::Usleep(-1);
        CTRL_SET_PROFILER_FIELD(
            this, _client_wait_user_cb, util::TimeStamp::Now(), TotalLatencyType::None);
    }
#endif
}

void ByteRpcCall::ProcessResponse(const ByteStdSerDes::ResponseMeta& meta,
                                  IORbufPipe* rbuf_pipe,
                                  uint32_t before_payload_size,
                                  uint32_t payload_size) {
    if (BYTERPC_UNLIKELY(0 != meta.error_code)) {
        SetFailed(meta.error_code, meta.error_text);
        rbuf_pipe->pop_front(before_payload_size + payload_size);
        return;
    }

    uint32_t attach_size = meta.attachment_size;
    // WARNING: Can not access meta anymore.
    rbuf_pipe->pop_front(before_payload_size);

    if (attach_size > 0 && attach_size == payload_size) {
        // fast path for empty response
        _incoming_attachment.reset(ExecCtxNode::GetIOBuf());
        rbuf_pipe->cut_to(_incoming_attachment.get(), attach_size);
    } else {
        if (attach_size > payload_size) {
            rbuf_pipe->pop_front(payload_size);
            SetFailed(ERESPONSE,
                      "Attachment size of %d is larger than response size of %d, local_side %s, "
                      "remote_side %s",
                      attach_size,
                      payload_size,
                      util::endpoint2str(local_side()).c_str(),
                      util::endpoint2str(remote_side()).c_str());
            return;
        }
        if (payload_size == 0) {
            OnRecycle();
            return;
        }
        IOBuf resp_buf;
        rbuf_pipe->cut_to(&resp_buf, payload_size - attach_size);
        if (attach_size > 0) {
            _incoming_attachment.reset(ExecCtxNode::GetIOBuf());
            rbuf_pipe->cut_to(_incoming_attachment.get(), attach_size);
        }

        if (!ProtobufSerDes::ParsePbFromReadBuffer(_resp, &resp_buf)) {
            SetFailed(ERESPONSE,
                      "Parse response failed, local_side %s, remote_side %s",
                      util::endpoint2str(local_side()).c_str(),
                      util::endpoint2str(remote_side()).c_str());
            return;
        }
    }

    OnRecycle();
}

void ByteRpcCall::OnRecycle() {
    // _is_terminating must be false here, otherwise leading to double-free
    BYTERPC_DCHECK_EQ(_is_terminating, false);
    _is_terminating = true;

    CTRL_SET_PROFILER_FIELD(
        this, _client_deserialize_resp, util::TimeStamp::Now(), TotalLatencyType::None);
    CancelTimeoutEventHandler();

    if (RpcFiberMode()) {
        // user is responsible to DoRecycle() in fiber mode
        AwakenRpcFiber();
    } else {
        if (BYTERPC_LIKELY(_done)) {
            _done->Run();
            _done = nullptr;
        }
        DoRecycle();
    }
}

void ByteRpcCall::DoRecycle() {
    if (FLAGS_byterpc_enable_time_profiler) {
        CTRL_SET_PROFILER_FIELD(
            this, _client_user_cb, util::TimeStamp::Now(), TotalLatencyType::ClientTotalTime);
        rpc_metrics->ReportClientRpcLatency(
            _client_socket ? _client_socket->GetTransportType() : TransportType::TYPE_KERNEL_TCP,
            GetTimeProfiler());
    }

    if (BYTERPC_LIKELY(_client_socket)) {
        _client_socket->ReleaseController(this);
        _client_socket->OwnerUnref();
        _client_socket = nullptr;
    }

    if (BYTERPC_LIKELY(s_cntl_cache.IsNotFull())) {
        Reset();
        s_cntl_cache.Return(this);
    } else {
        util::return_tls_object(this);
    }
}

void ByteRpcCall::Reset() {
    // prepare for next reuse
    RecycleBuf();
    _client_socket = nullptr;
    _correlation_id = INVALID_CORRELATION_ID;
    _is_terminating = false;
#ifdef BYTERPC_ENABLE_FIBER
    _rpc_fiber = nullptr;
#endif
    Controller::Reset();
}

std::string ByteClientMessageHandler::GetMessageHeader() const {
    return "TRPC";
}

ProtocolType ByteClientMessageHandler::GetProtocolType() const {
    return PROTOCOL_BYTE_STD;
}

ByteStdSerDes::ResponseMeta* GetRespMeta(IORbufPipe* rbuf_pipe,
                                         uint32_t meta_size,
                                         ByteStdSerDes::ResponseMeta* buf,
                                         uint32_t* before_payload_size) {
    auto& first_ref = rbuf_pipe->front_ref();
    ByteStdSerDes::ResponseMeta* meta = nullptr;
    constexpr uint32_t min_noncopy_size =
        ByteStdSerDes::RPC_HEADER_SIZE + ByteStdSerDes::RESPONSE_META_FIX_SIZE;
    if (BYTERPC_LIKELY(first_ref.length() >= min_noncopy_size) &&
        meta_size == ByteStdSerDes::RESPONSE_META_FIX_SIZE) {
        // if we have a continuous data buf, and no error,
        // cast to meta directly.
        meta = reinterpret_cast<ByteStdSerDes::ResponseMeta*>(first_ref.data() +
                                                              ByteStdSerDes::RPC_HEADER_SIZE);
        if (BYTERPC_LIKELY(0 == meta->error_code)) {
            *before_payload_size = min_noncopy_size;
            return meta;
        }
    }

    // if data not continuous or rpc failed, we need pour data out,
    // as response may contain error, that's variable length, so we have to use cut instead of
    // copy.
    rbuf_pipe->pop_front(ByteStdSerDes::RPC_HEADER_SIZE);
    rbuf_pipe->cut_to(reinterpret_cast<char*>(buf), ByteStdSerDes::RESPONSE_META_FIX_SIZE);
    meta = buf;
    *before_payload_size = 0;
    if (BYTERPC_UNLIKELY(meta_size > ByteStdSerDes::RESPONSE_META_FIX_SIZE)) {
        rbuf_pipe->cut_to(&meta->error_text, meta_size - ByteStdSerDes::RESPONSE_META_FIX_SIZE);
    }
    return meta;
}

ParseError ByteClientMessageHandler::HandleResponse(ClientSocket* socket, IORbufPipe* rbuf_pipe) {
    while (true) {
        uint64_t start_ts = 0;
        if (BYTERPC_UNLIKELY(FLAGS_byterpc_enable_time_profiler)) {
            start_ts = util::TimeStamp::Now();
        }
        uint32_t meta_size = 0;
        uint32_t body_size = 0;
        ExtendHeader extend_header;
        // if PARSE_OK, extend_header will be poped out of rbuf_pipe
        ParseError pr = ByteStdSerDes::ParseRpcHeader(
            rbuf_pipe, false /* is_request */, &meta_size, &body_size, &extend_header);
        if (PARSE_OK != pr) {
            return pr;
        }

        // connection built-in service
        if (FLAGS_byterpc_enable_collecting_connection_info) {
            socket->AddInput(1, ByteStdSerDes::RPC_HEADER_SIZE + body_size);
        }

#ifdef BYTERPC_ENABLE_EXTEND_HEADER
        IORbufPipe maintain;
        if (extend_header.crc_mode != CrcMode::TYPE_NONE) {
            rbuf_pipe->copy_to(&maintain, ByteStdSerDes::RPC_HEADER_SIZE + body_size);
        }
#endif

        // Deserialize response meta
        uint32_t before_payload_size;
        uint32_t payload_size = body_size - meta_size;
        ByteStdSerDes::ResponseMeta buf;
        ByteStdSerDes::ResponseMeta* meta =
            GetRespMeta(rbuf_pipe, meta_size, &buf, &before_payload_size);

#ifdef BYTERPC_ENABLE_EXTEND_HEADER
        if (extend_header.crc_mode != CrcMode::TYPE_NONE) {
            size_t crc_size = 0;
            if (extend_header.crc_mode == CrcMode::TYPE_META_PB) {
                crc_size += ByteStdSerDes::RPC_HEADER_SIZE + body_size - meta->attachment_size;
            } else if (extend_header.crc_mode == CrcMode::TYPE_FULL) {
                crc_size += ByteStdSerDes::RPC_HEADER_SIZE + body_size;
            } else {
                BYTERPC_LOG(ERROR) << "Invalid crc mode";
                return PARSE_ERROR_ABSOLUTELY_WRONG;
            }
            uint32_t crc_value = ExtendHeaderSerDes::CalCrc32(&maintain, crc_size);
            if (crc_value != extend_header.crc_value) {
                BYTERPC_LOG(ERROR) << "Crc check failed";
                return PARSE_ERROR_ABSOLUTELY_WRONG;
            }
            maintain.clear();
        }
#endif

        auto cntl = socket->MatchPendingCall(meta->correlation_id);
        if (BYTERPC_UNLIKELY(nullptr == cntl || cntl->Failed())) {
            BYTERPC_LOG(WARNING) << "No such pending call correlation id " << meta->correlation_id
                                 << ", may encounter timeout or be cancelled by user, attach_size="
                                 << meta->attachment_size << " local_side=" << socket->local_side()
                                 << " remote_side=" << socket->remote_side() << " transport_type="
                                 << TransportTypeToString(socket->GetTransportType());
            // drop remaining data
            rbuf_pipe->pop_front(before_payload_size + payload_size);
            continue;
        }

        auto call = static_cast<ByteRpcCall*>(cntl);
        CTRL_SET_PROFILER_FIELD(
            call, _client_transport, socket->GetLastInvokeTimeStamp(), TotalLatencyType::None);
        CTRL_SET_PROFILER_FIELD(
            call, _client_wait_deserialize_resp, start_ts, TotalLatencyType::None);

        call->ProcessResponse(*meta, rbuf_pipe, before_payload_size, payload_size);
    }

    return PARSE_OK;
}

Controller* ByteClientMessageHandler::CreateSessionController(int64_t timeout_us,
                                                              CrcMode crc_mode) {
    auto cntl = s_cntl_cache.GetOne();
    if (BYTERPC_UNLIKELY(!cntl)) {
        cntl = util::get_tls_object<ByteRpcCall>();
        if (BYTERPC_UNLIKELY(!cntl)) {
            return nullptr;
        }
    }
    cntl->_rpc_timeout_us = timeout_us;
    cntl->_crc_mode = crc_mode;
    return cntl;
}

}  // namespace byterpc
