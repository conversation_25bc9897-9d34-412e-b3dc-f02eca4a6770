// Copyright (c) 2021, ByteDance Inc. All rights reserved.

#pragma once

#include <google/protobuf/service.h>

#include <memory>
#include <string>

#include "byterpc/controller.h"
#include "byterpc/io_buf.h"
#include "byterpc/rpc.h"

namespace byterpc {

/*
 * A new definition of ParseResult;
 */
enum ParseError {
    PARSE_OK = 0,
    PARSE_ERROR_TRY_OTHERS,
    PARSE_ERROR_NOT_ENOUGH_DATA,
    PARSE_ERROR_TOO_BIG_DATA,
    PARSE_ERROR_NO_RESOURCE,
    PARSE_ERROR_ABSOLUTELY_WRONG,
    // for protobuf byte protocol, in certain cases we need to reset socket by returning such error
    PARSE_ERROR_PROTOBUF_BYTE_ERROR,
};

static const size_t MESSAGE_HEADER_LENGTH = 4;

#ifndef BYTERPC_ENABLE_ALIGNED_BYTE_STD
// Alloc byte_std meta IOBlock size is 256 bytes, include at most 56 bytes IOBlock meta,
// 12 bytes header and 24 bytes of fixed request meta size, the remaining can be method name
static const uint32_t FULL_METHOD_NAME_MAX_SIZE = 164;
#else
// aligned_byte_std contains 28 bytes of fixed request meta size
static const uint32_t FULL_METHOD_NAME_MAX_SIZE = 160;
#endif

// The actual parser for protocols without magic headers
// returns PARSE_OK if the rpc message in read_buf can be parsed by the protocol
// otherwise returns the corresponding ParseError
struct ParsePlugin {
    virtual ParseError TryParse(IOBuf* read_buf) const = 0;
};

struct MessageHandler : public Destructable {
    virtual std::string GetMessageHeader() const = 0;

    virtual ProtocolType GetProtocolType() const = 0;

    // When a new message comes in, the socket checks its magic header (first 4 bytes)
    // to identify which protocol the message belongs to. If none matches, the socket will then try
    // each ExtendParser. Currently both HTTP and Thrift protocol have ExtendParser.
    // HTTP's MessageHeader is 'HTTP', which is OK for client to read response 'HTTP/1.1 200 OK',
    // but from server-side, data start with 'GET /path HTTP/1.1', so we need ExtendParser to
    // handle this situation.
    // As for Thrift protocol, there is no such magic header because we want compactibility with
    // native Thrift client/server.
    virtual std::unique_ptr<ParsePlugin> GetExtendParser() const {
        return nullptr;
    }
};

class IORbufPipe;

// Parse a response and handle it.
class ClientSocket;
struct ClientMessageHandler : public MessageHandler {
    // Return a parse error code.
    // Parsing and handling the response does not need any external
    // intervention, so that the response will be handled by
    // a concrete class of this type internally without any
    // external trigger or interface API.
    virtual ParseError HandleResponse(ClientSocket* socket, IORbufPipe* rbuf_pipe) = 0;

    // Return a new Controller instance to represent a new `session`,
    // which is a state machine of `send request`, `wait for reply` and
    // `process the reply`. The returned instance is self owned, in another
    // word, it itself controlls when to be deleted.
    virtual Controller* CreateSessionController(int64_t rpc_timeout_us = 0,
                                                CrcMode crc_mode = CrcMode::TYPE_NONE) = 0;

    // Return if the relevant protocol supports checksum, which is supposed to
    // check when building client controller.
    virtual bool SupportCrc() const = 0;
};

// Parse an incoming request, and handle that request.
// The concrete class of this type may also serialize response
// of the request as well;
class ServerSocket;
struct ServiceObserver;
struct ServerMessageHandler : public MessageHandler {
    // Return a parse error code.
    virtual ParseError HandleRequest(ServerSocket* socket, IORbufPipe* rbuf_pipe) = 0;

    // TODO(crq): return null here  will cause `incomplete type.`
    // so we put default impl after ServiceObserver is defined.
    // (inside rpc_service_registry.cpp)
    virtual std::unique_ptr<ServiceObserver> GetServiceObserver() const;
};

}  // namespace byterpc
