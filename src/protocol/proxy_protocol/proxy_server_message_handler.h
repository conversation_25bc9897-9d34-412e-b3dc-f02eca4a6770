// Copyright (c) 2024, ByteDance Inc. All rights reserved.
#pragma once

#include <memory>
#include <string>

#include "protocol/message_base.h"
#include "proxy_plugin.h"
#include "rpc/server_socket.h"

namespace byterpc {

class ProxyServerMessageHandler : public ServerMessageHandler {
public:
    ProxyServerMessageHandler();

    std::string GetMessageHeader() const override;
    ProtocolType GetProtocolType() const override;
    std::unique_ptr<ParsePlugin> GetExtendParser() const override {
        return std::make_unique<ProxyParsePlugin>();
    }

    ParseError HandleRequest(ServerSocket* socket, IORbufPipe* rbuf_pipe) override;
};

}  // namespace byterpc
