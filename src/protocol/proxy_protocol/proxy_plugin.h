// Copyright (c) 2024, ByteDance Inc. All rights reserved.
#pragma once

#include "protocol/message_base.h"
#include "protocol/proxy_protocol/pp_message_serdes.h"

namespace byterpc {

class IOBuf;
struct ProxyParsePlugin : public ParsePlugin {
    ParseError TryParse(IOBuf* read_buf) const override {
        char buf[ProxyProtocolV2Parser::kProxySignatureLength];
        size_t data_len = read_buf->copy_to(buf, ProxyProtocolV2Parser::kProxySignatureLength);
        if (ProxyProtocolV2Parser::MatchProxyProtocolMessage(buf, data_len)) {
            return (data_len == ProxyProtocolV2Parser::kProxySignatureLength)
                       ? PARSE_OK
                       : PARSE_ERROR_NOT_ENOUGH_DATA;
        }
        // If mismatching happened
        return PARSE_ERROR_TRY_OTHERS;
    }
};

}  // namespace byterpc
