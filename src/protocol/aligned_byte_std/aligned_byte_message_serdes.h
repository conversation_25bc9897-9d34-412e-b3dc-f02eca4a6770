// Copyright (c) 2021, ByteDance Inc. All rights reserved.
#pragma once

#include <string>

#include "protocol/extend_header.h"
#include "protocol/message_base.h"
#include "util/raw_pack.h"

namespace byterpc {

class IOBuf;
class IORbufPipe;

// `aligned_byte_std' protocol serdes
class AlignedByteStdSerDes {
public:
    // aligned_byte_std request meta
    struct RequestMeta {
        int64_t correlation_id;
        uint64_t log_id;
        uint32_t attachment_size;
        uint32_t service_name_size;
        uint32_t method_name_size;
        // record service_name and method_name, format is: service_name + method_name + padding
        char full_method_name[FULL_METHOD_NAME_MAX_SIZE];

        RequestMeta()
            : correlation_id(0),
              log_id(0),
              attachment_size(0),
              service_name_size(0),
              method_name_size(0),
              full_method_name{0} {}
    };
    static constexpr uint32_t REQUEST_META_FIX_SIZE = 28;
    static constexpr uint32_t REQUEST_META_MAX_SIZE =
        FULL_METHOD_NAME_MAX_SIZE + REQUEST_META_FIX_SIZE;
    static constexpr size_t kAlignment = 4;
    static constexpr char kPaddingChar = '\0';

    // byte-rpc response meta
    struct ResponseMeta {
        int64_t correlation_id;
        uint32_t attachment_size;
        int32_t error_code;
        std::string error_text;

        ResponseMeta() : correlation_id(0), attachment_size(0), error_code(0) {}
    };
    static constexpr uint32_t RESPONSE_META_FIX_SIZE = 16;

    static constexpr uint32_t RPC_HEADER_SIZE = 12;

    // Parse the message header contained in a read buffer `source`.
    // NOTE `source' will NOT be modified in this API.
    static ParseError ParseRpcHeader(IORbufPipe* rbuf_pipe,
                                     bool is_request,
                                     uint32_t* meta_size,
                                     uint32_t* body_size,
                                     ExtendHeader* extend_header);

    inline static void PackRpcHeader(char* rpc_header, size_t meta_size, size_t payload_size) {
        uint32_t* dummy = reinterpret_cast<uint32_t*>(rpc_header);
        *dummy = *reinterpret_cast<const uint32_t*>("ARPC");
        util::RawPacker(rpc_header + 4).pack32(meta_size + payload_size).pack32(meta_size);
    }
};

}  // namespace byterpc
