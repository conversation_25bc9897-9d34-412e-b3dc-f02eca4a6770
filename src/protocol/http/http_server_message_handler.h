// Copyright (c) 2021, ByteDance Inc. All rights reserved.
#pragma once

#include <google/protobuf/message.h>
#include <google/protobuf/service.h>
#include <google/protobuf/stubs/callback.h>

#include <deque>
#include <memory>
#include <string>
#include <utility>

#include "byterpc/controller.h"
#include "byterpc/util/object_pool.h"
#include "byterpc/util/owner_ptr.h"
#include "protocol/http/http_plugin.h"
#include "protocol/message_base.h"
#include "rpc/rpc_service_registry.h"
#include "rpc/server_socket.h"

namespace byterpc {

class ThreadContext;

class HttpServerMessageHandler : public ServerMessageHandler {
public:
    HttpServerMessageHandler();

    std::string GetMessageHeader() const override;
    ProtocolType GetProtocolType() const override;

    ParseError HandleRequest(ServerSocket* socket, IORbufPipe* rbuf_pipe) override;

    std::unique_ptr<ParsePlugin> GetExtendParser() const override {
        return std::make_unique<HTTPParsePlugin>();
    }

    std::unique_ptr<ServiceObserver> GetServiceObserver() const override;
};

// The place to invoke svc->CallMethod(), and where
// the callback to send response back.
class HttpServerController : public Controller {
    BYTERPC_OBJECT_POOL_FRIENDS;
    friend HttpServerMessageHandler;

public:
    HttpServerController();

    ~HttpServerController() {}

    void SetFailed(int err_code, const std::string& reason) override;
    void SetFailed(int err_code, const char* reason_fmt, ...) override;

    void OnRecycle() override;

    void DoRecycle() override;

    void Reset() override;

    void ProcessRequest();

    IOBuf& request_attachment() override;
    IOBuf& response_attachment() override;

    util::EndPoint local_side() override {
        return _parent->local_side();
    }

    util::EndPoint remote_side() override {
        return _parent->remote_side();
    }

#ifdef BYTERPC_ENABLE_PROXY_PROTOCOL
    const ProxyProtocolInfo& GetProxyProtocolInfo() const override {
        return _parent->GetProxyProtocolInfo();
    }
#endif

    inline HttpHeader& http_response() override {
        if (!_http_response) {
            _http_response.reset(new HttpHeader);
        }
        return *_http_response;
    }

    inline HttpHeader& http_request() override {
        if (!_http_request) {
            _http_request.reset(new HttpHeader);
        }
        return *_http_request;
    }

    // In order to be compatible with brpc http APIs
    inline bool has_http_response() const override {
        return _http_response != nullptr;
    }
    inline bool has_http_request() const override {
        return _http_request != nullptr;
    }

    ProtocolType request_protocol() const override {
        return PROTOCOL_HTTP;
    }

private:
    bool _is_terminating;
    util::owner_ptr<ServerSocket> _parent;
    std::unique_ptr<google::protobuf::Message> _req;
    std::unique_ptr<google::protobuf::Message> _resp;
    std::unique_ptr<HttpHeader> _http_request;
    std::unique_ptr<HttpHeader> _http_response;

    void SendResponse(ThreadContext* thread_context);
    void SendResponseImpl();

    void SetFailResp(int err_code, const std::string& error_text);
};

}  // namespace byterpc
