// Copyright (c) 2021, ByteDance Inc. All rights reserved.

#pragma once

#include <string>

#include "proto/builtin_service.pb.h"

namespace byterpc {

class Controller;
class FlagsService : public proto::flags {
public:
    void default_method(::google::protobuf::RpcController* cntl_base,
                        const proto::FlagsRequest* request,
                        proto::FlagsResponse* response,
                        ::google::protobuf::Closure* done);

private:
    void set_value_page(Controller* cntl, ::google::protobuf::Closure* done);
};

}  // namespace byterpc
