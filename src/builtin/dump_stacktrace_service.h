// Copyright (c) 2021, ByteDance Inc. All rights reserved.

#pragma once

#include <string>

#include "proto/builtin_service.pb.h"

namespace byterpc {

class DumpStackTraceService : public proto::dump_stack_trace {
public:
    void default_method(::google::protobuf::RpcController* cntl_base,
                        const proto::DumpStackTraceRequest* request,
                        proto::DumpStackTraceResponse* response,
                        ::google::protobuf::Closure* done);
};

}  // namespace byterpc
