// Copyright (c) 2021, ByteDance Inc. All rights reserved.

#pragma once

#include <mutex>  // NOLINT(build/c++11)
#include <string>
#include <vector>

#include "proto/builtin_service.pb.h"

namespace byterpc {

class ConnectionsService : public proto::connections {
public:
    void default_method(::google::protobuf::RpcController* cntl_base,
                        const proto::ConnectionsRequest* request,
                        proto::ConnectionsResponse* response,
                        ::google::protobuf::Closure* done);
};

}  // namespace byterpc
