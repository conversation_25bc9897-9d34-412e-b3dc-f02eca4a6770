// Copyright (c) 2022, ByteDance Inc. All rights reserved.

#include "byterpc/util/tls_resource_pool.h"

#include "byterpc/util/rpc_conf.h"

#ifdef BYTERPC_ENABLE_BYTE_EXPRESS
#include "transport/byte_express/byte_express_memory_pool.h"
#endif

namespace byterpc {
namespace util {
namespace detail {

void* real_rdma_alloc(size_t sz) {
#ifdef BYTERPC_ENABLE_BYTE_EXPRESS
    return ::be::RdmaMemoryPool::Get().AlignedAllocate(sz, sz);
#else
    _BYTERPC_LOG_FATAL("RDMA is NOT enabled, pls re-compile with --be");
    return nullptr;
#endif
}

bool real_rdma_dealloc(void* ptr, size_t sz) {
#ifdef BYTERPC_ENABLE_BYTE_EXPRESS
    ::be::RdmaMemoryPool::Get().Deallocate(ptr);
    return true;
#else
    _BYTERPC_LOG_FATAL("RDMA is NOT enabled, pls re-compile with --be");
    return false;
#endif
}
}  // namespace detail
}  // namespace util
}  // namespace byterpc
