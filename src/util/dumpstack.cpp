// Copyright (c) 2024, ByteDance Inc. All rights reserved.

#include "util/dumpstack.h"

#include <cxxabi.h>
#include <memory>
#include <sstream>
#include <unordered_map>

namespace byterpc {
namespace util {

bool CaptureStackFrames(uint32_t skip_depth, uint32_t max_depth, StackFrame* frames) {
    if (!frames) {
        return false;
    }
    if (max_depth > kMaxStackDepth) {
        max_depth = kMaxStackDepth;
    }
    frames->fill(0);

    unw_cursor_t cursor;
    unw_context_t context;
    unw_word_t ip;
    if (0 != unw_getcontext(&context)) {
        return false;
    }
    if (0 != unw_init_local(&cursor, &context)) {
        return false;
    }

    uint32_t depth = 0;
    uint32_t idx = 0;
    while (unw_step(&cursor) > 0) {
        if (depth >= skip_depth && idx < max_depth) {
            // If reading the value of register IPR fails, just quit the process with no side
            // effects.
            if (0 != unw_get_reg(&cursor, UNW_REG_IP, &ip)) {
                return false;
            }
            (*frames)[idx++] = ip;
        }
        depth++;
        if (idx >= max_depth) {
            break;
        }
    }

    return true;
}

bool DumpStackAsync(const std::string& user_hint, const StackFrame& frames, StackPrinter printer) {
    unw_cursor_t cursor;
    unw_context_t context;
    char func_name[512];  // 512 is rather sufficient
    size_t size;
    int status;
    std::unique_ptr<char, decltype(&free)> real_name(nullptr, &free);

    std::stringstream ss;
    if (!user_hint.empty()) {
        ss << user_hint << std::endl;
    } else {
        ss << "Stack trace:" << std::endl;
    }

    for (const auto& frame : frames) {
        // Encountering 0 indicates the end
        if (0 == frame) {
            break;
        }
        if (0 != unw_init_local(&cursor, &context)) {
            return false;
        }
        if (0 != unw_set_reg(&cursor, UNW_REG_IP, frame)) {
            return false;
        }
        if (0 == unw_get_proc_name(&cursor, func_name, sizeof(func_name), &size)) {
            real_name.reset(abi::__cxa_demangle(func_name, 0, 0, &status));
            if (0 == status && nullptr != real_name) {
                ss << "    @     0x" << std::hex << frame << "  " << real_name.get() << std::endl;
            } else {
                ss << "    @     0x" << std::hex << frame << " (unknown)" << std::endl;
            }
        } else {
            ss << "    @     0x" << std::hex << frame << " (unknown)" << std::endl;
        }
    }

    printer(ss.str());
    return true;
}
}  // namespace util
}  // namespace byterpc
