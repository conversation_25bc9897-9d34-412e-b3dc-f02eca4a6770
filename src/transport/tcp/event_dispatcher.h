// Copyright (c) 2021, ByteDance Inc. All rights reserved.
#pragma once

#include <functional>
#include <memory>
#include <queue>
#include <vector>

#include "byterpc/rpc_stats.h"
#include "byterpc/thread/priority_task.h"
#include "byterpc/util/container_wrapper.h"
#include "rpc/event_base.h"

/*
 * Dispatch edge triggered events of EventHandler
 */

namespace byterpc {

struct EventData;
class TimeoutManager;
class LoopMetrics;

class EventDispatcher : public EventDispatcherBase {
public:
    EventDispatcher();

    ~EventDispatcher() override;

    bool IsStopped() const {
        return _stop;
    }

    int LoopIfPossible() override;

    int LoopForever() override;

    int QuitForeverLoop() override;

    int WakeForeverLoop() override;

    size_t NumRegisteredIOHandler() const override;

    // When edge-trigged events happen on the IoHandler, call corresponding
    // callback (i.e. HandleReadEvent());
    // Returns 0 on success, -1 otherwise;
    int AddConsumer(IOHandler* h) override;

    int RemoveConsumer(IOHandler* h) override;

    // When edge-trigged events happen on the IoHandler, call corresponding
    // callback (HandleWriteEvent() if pollin is false, otherwise can be
    // either HandleReadEvent() or HandleWriteEvent());
    // Returns 0 on success, -1 otherwise;
    int AddEpollOut(IOHandler* h, bool pollin) override;

    // Remove EPOLLOUT event on @h. If `pollin` is true, EPOLLIN event
    // will be kept and EPOLL_CTL_MOD will be used instead of EPOLL_CTL_DEL;
    // Returns 0 on success, -1 otherwise;
    int RemoveEpollOut(IOHandler* h, bool pollin) override;

    // After `microseconds_since_now`, a time event happen, call corresponding
    // callback (i.e. HandleTimeEvent())
    // Returns Timeout Iterator
    TimeoutIterator AddTimeConsumer(TimeEventHandler* h, uint64_t microseconds_since_now) override;

    // Remove time event on TimeoutIterator;
    // Return 0 on success, -1 otherwise;
    int RemoveTimeConsumer(TimeoutIterator timeout_iter) override;

    void ReadLoopStats(LoopStats* loop_stats) override;

private:
    int _epfd;
    bool _stop;
    // timestamp which is registered to _timer_fd
    uint64_t _timer_fd_ts;
    int _quit_fd;
    int _wakeup_fd;
    int _timer_fd;
    EventDispatcherLoopStats _loop_stats;
    std::unique_ptr<TimeoutManager> _timeout_manager;
    util::HashMap<uint64_t, EventData*> _reg_event_datas;
    std::queue<EventData*> _delay_deleted_event_datas;
    std::unique_ptr<LoopMetrics> _loop_metrics;
    ThreadContext* _thread_ctx;

    int LoopInternal(int duration);

    int WakeLoop(int event_fd);
    void HandleWakeEvent(int event_fd);
    void RecycleDelayDeletedEventDatas();

    void SetOutActive(IOHandler* h) override {}
    void ClearOutActive(IOHandler* h) override {}
};

}  // namespace byterpc
