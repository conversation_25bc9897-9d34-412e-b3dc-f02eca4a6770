// Copyright (c) 2021, ByteDance Inc. All rights reserved.
#include "transport/tcp/event_dispatcher.h"

#include <byte/string/number.h>
#include <byte/thread/this_thread.h>
#include <sys/epoll.h>
#include <sys/eventfd.h>
#include <sys/socket.h>
#include <sys/types.h>
#include <unistd.h>

#include <algorithm>
#include <iterator>
#include <utility>

#include "byterpc/byterpc_flags.h"
#include "byterpc/exec_ctx.h"
#include "byterpc/util/compiler_specific.h"
#include "byterpc/util/owner_ptr.h"
#include "byterpc/util/timestamp.h"
#include "metrics/metrics_inl.h"
#include "rpc/timeout_manager.h"
#include "rpc/timerfd_manager.h"
#include "util/fd_guard.h"
#include "util/fd_utility.h"

#include "byterpc/util/logging.h"

#ifdef BYTERPC_ENABLE_FIBER
#include <byte/fiber/event.h>
#include <byte/fiber/fiber.h>
#endif

namespace {
static unsigned int check_epollrdhup() {
    using byterpc::util::fd_guard;
    fd_guard epfd(epoll_create(16));
    if (epfd < 0) {
        return 0;
    }
    fd_guard fds[2];
    if (socketpair(AF_UNIX, SOCK_STREAM, 0, reinterpret_cast<int*>(fds)) < 0) {
        return 0;
    }
    epoll_event evt = {static_cast<uint32_t>(EPOLLIN | EPOLLRDHUP | EPOLLET), {NULL}};
    if (epoll_ctl(epfd, EPOLL_CTL_ADD, fds[0], &evt) < 0) {
        return 0;
    }
    if (close(fds[1].release()) < 0) {
        return 0;
    }
    epoll_event e;
    int n;
    while ((n = epoll_wait(epfd, &e, 1, -1)) == 0) {
    }
    if (n < 0) {
        return 0;
    }
    return (e.events & EPOLLRDHUP) ? EPOLLRDHUP : static_cast<EPOLL_EVENTS>(0);
}

static const unsigned int has_epollrdhup = check_epollrdhup();
}  // namespace

namespace byterpc {

struct EventData {
    util::owner_ptr<IOHandler> handler;
    uint32_t reset_count;

    explicit EventData(IOHandler* handler)
        : handler(handler), reset_count(handler->NumOfResets()) {}
};

EventDispatcher::EventDispatcher() : _epfd(-1), _stop(false), _timer_fd_ts(UINT64_MAX) {
    _timeout_manager.reset(new TimeoutManager());
    int thread_id = byte::ThisThread::GetId();
    _loop_metrics.reset(new LoopMetrics(
        TransportType::TYPE_KERNEL_TCP, byte::NumberToString(thread_id), &_loop_stats));

    _epfd = epoll_create(1024 * 1024);
    if (_epfd < 0) {
        BYTERPC_LOG(FATAL) << "Fail to create epoll";
        return;
    }
    BYTERPC_CHECK_EQ(0, util::make_close_on_exec(_epfd));

    _quit_fd = eventfd(0, EFD_NONBLOCK);
    BYTERPC_CHECK_GE(_quit_fd, 0);
    epoll_event quit_fd_evt;
    quit_fd_evt.events = EPOLLIN | EPOLLET;
    quit_fd_evt.data.u64 = static_cast<uint64_t>(_quit_fd);
    if (epoll_ctl(_epfd, EPOLL_CTL_ADD, _quit_fd, &quit_fd_evt) != 0) {
        BYTERPC_LOG(FATAL) << "Fail to epoll_ctl add quit fd";
        return;
    }

    _wakeup_fd = eventfd(0, EFD_NONBLOCK);
    BYTERPC_CHECK_GE(_wakeup_fd, 0);
    epoll_event wakeup_evt;
    wakeup_evt.events = EPOLLIN | EPOLLET;
    wakeup_evt.data.u64 = static_cast<uint64_t>(_wakeup_fd);
    if (epoll_ctl(_epfd, EPOLL_CTL_ADD, _wakeup_fd, &wakeup_evt) != 0) {
        BYTERPC_LOG(FATAL) << "Fail to epoll_ctl add wakeup fd";
        return;
    }

    _timer_fd = TimerfdManager::TimerfdCreate();
    if (_timer_fd == -1) {
        BYTERPC_LOG(FATAL) << "Fail to create timer_fd";
        return;
    }
    epoll_event timerfd_evt;
    timerfd_evt.events = EPOLLIN | EPOLLET;
    timerfd_evt.data.u64 = static_cast<uint64_t>(_timer_fd);
    if (epoll_ctl(_epfd, EPOLL_CTL_ADD, _timer_fd, &timerfd_evt) != 0) {
        BYTERPC_LOG(FATAL) << "Fail to epoll_ctl add timer_fd";
        return;
    }

    _thread_ctx = ExecCtx::GetCurrentThreadContext();
}

EventDispatcher::~EventDispatcher() {
    // Release IOHandlers will remove consumer from event dispatcher, and delete all event
    // datas in _delay_deleted_event_datas.
    std::vector<EventData*> event_datas;
    std::transform(_reg_event_datas.begin(),
                   _reg_event_datas.end(),
                   std::back_inserter(event_datas),
                   [](std::pair<uint64_t, EventData*> it) { return it.second; });
    for (auto& event_data : event_datas) {
        event_data->handler->Reset(ECLOSE);
    }

    // The above IOHandler::Reset(i.e. TcpTransport::Reset) will trigger
    // ClientSocket::SetFailed, which submits Controller::SetFailed async tasks to ExecCtxNode.
    // In those tasks, StatefulController::SetFailed will in return call
    // EventDisptacher::RemoveTimeConsumer. The following line consumes the tasks before
    // end of this destructor, otherwise later a use after delete problem will occur.
    ExecCtx::LoopIfPossible();
    BYTERPC_CHECK(_reg_event_datas.empty());

    // release remaining EventData
    RecycleDelayDeletedEventDatas();

    _timeout_manager.reset();

    if (_epfd >= 0) {
        close(_epfd);
        _epfd = -1;
    }
    if (_quit_fd > 0) {
        close(_quit_fd);
    }
    if (_wakeup_fd > 0) {
        close(_wakeup_fd);
    }
    if (_timer_fd > 0) {
        TimerfdManager::TimerfdClose(_timer_fd);
        _timer_fd = -1;
    }
}

int EventDispatcher::LoopIfPossible() {
    return LoopInternal(0);
}

int EventDispatcher::LoopForever() {
    return LoopInternal(-1);
}

inline int EventDispatcher::LoopInternal(int duration) {
    uint64_t start_timestamp = 0;
    if (FLAGS_byterpc_enable_loop_metrics) {
        start_timestamp = util::TimeStamp::Now();
    }

    if (_epfd < 0) {
        BYTERPC_LOG(ERROR) << "No epoll fd";
        return -1;
    }

    int epoll_wait_ms = 0;
    // set '_stop' here to false to enable reenterable LoopForever
    _stop = false;
    while (!_stop) {
        _loop_stats.loop_count += 1;

        const int kNumEvents = FLAGS_byterpc_tcp_max_events_at_once;
        epoll_event e[kNumEvents];
        IOHandler* h = nullptr;

#ifdef BYTERPC_ENABLE_FIBER
        int n = 0;
        if (ExecCtx::FiberMode()) {
            if (duration == 0) {  // ExecCtx::GetLoopType == LOOP_IF_POSSIBLE
                byte::fiber::Yield();
            } else if (epoll_wait_ms != 0) {
                int err = byte::fiber::WaitForEvent(
                    _epfd,
                    byte::fiber::EVENT_IN,
                    BYTERPC_LIKELY(epoll_wait_ms < 0) ? -1 : epoll_wait_ms * 1000);
                if (BYTERPC_UNLIKELY(err != 0 && err != EINTR && err != ETIMEDOUT)) {
                    BYTERPC_LOG(ERROR) << "Fail to WaitForEvent epfd=" << _epfd;
                    break;
                }
            }
            n = epoll_wait(_epfd, e, kNumEvents, 0);
        } else {
            n = epoll_wait(_epfd, e, kNumEvents, epoll_wait_ms);
        }
#else
        const int n = epoll_wait(_epfd, e, kNumEvents, epoll_wait_ms);
#endif

        if (n < 0) {
            if (EINTR == errno) {
                continue;
            } else {
                BYTERPC_LOG(ERROR) << "Fail to epoll_wait epfd=" << _epfd;
                break;
            }
        }
        _loop_stats.num_active_events += n;
        _loop_stats.num_events += n;

        // Make sure that timeout events are processed before IO events;
        int time_num = _timeout_manager->ProcessTimeout();
        _loop_stats.num_time_events += time_num;
        _loop_stats.num_events += time_num;

        for (int i = 0; i < n; ++i) {
            if (e[i].data.u64 == static_cast<uint64_t>(_timer_fd)) {
                _timer_fd_ts = UINT64_MAX;
                continue;
            }
            if (e[i].data.u64 == static_cast<uint64_t>(_quit_fd) ||
                e[i].data.u64 == static_cast<uint64_t>(_wakeup_fd)) {
                HandleWakeEvent(static_cast<int>(e[i].data.u64));
                continue;
            }
            if (e[i].events & (EPOLLIN | EPOLLERR | EPOLLHUP)) {
                auto data = reinterpret_cast<EventData*>(e[i].data.u64);
                h = data->handler.get();
                if (h->NumOfResets() != data->reset_count) {
                    continue;
                }
                h->HandleReadEvent();
                ++_loop_stats.num_read_events;
            }
            if (e[i].events & (EPOLLOUT | EPOLLERR | EPOLLHUP)) {
                auto data = reinterpret_cast<EventData*>(e[i].data.u64);
                h = data->handler.get();
                if (h->NumOfResets() != data->reset_count) {
                    continue;
                }
                h->HandleWriteEvent();
                ++_loop_stats.num_write_events;
            }
        }

        ExecCtx::LoopIfPossible();

        // release delay-deleted EventData
        RecycleDelayDeletedEventDatas();

        if (duration == 0) {
            break;
        }

        // currently polling mode will consume remote tasks outer
        ExecCtx::ConsumeRemoteTasks();

        // reconfigure epoll_wait_ms
        epoll_wait_ms = -1;
        if ((ExecCtx::GetCurrentEventQueueSize() > 0) || (_thread_ctx->PendingTaskNum() > 0)) {
            // we have schedule tasks or remote tasks
            epoll_wait_ms = 0;
        } else {
            uint64_t timer_wait_us = _timeout_manager->GetLatestEventWaitUs();
            if (timer_wait_us == 0) {
                epoll_wait_ms = 0;
            } else if (timer_wait_us != UINT64_MAX) {
                uint64_t event_ts = _timeout_manager->GetLatestEventTimeStamp();
                // if the latest event in _timeout_manager happens before that in _timer_fd, we
                // should re-register time to _timer_fd
                if (event_ts < _timer_fd_ts) {
                    TimerfdManager::TimerfdSettime(_timer_fd, timer_wait_us);
                    _timer_fd_ts = event_ts;
                }
            }
        }
    }

    ExecCtx::LoopIfPossible();

    if (FLAGS_byterpc_enable_loop_metrics) {
        uint64_t end_timestamp = util::TimeStamp::Now();
        _loop_metrics->UpdateMetrics(start_timestamp, end_timestamp);
    }

    return 0;
}

int EventDispatcher::QuitForeverLoop() {
    return WakeLoop(_quit_fd);
}

int EventDispatcher::WakeForeverLoop() {
    return WakeLoop(_wakeup_fd);
}

size_t EventDispatcher::NumRegisteredIOHandler() const {
    return _reg_event_datas.size();
}

int EventDispatcher::AddConsumer(IOHandler* h) {
    BYTERPC_CHECK_GE(_epfd, 0);

    epoll_event evt;
    evt.events = EPOLLIN | EPOLLET;
    EventData* data = new EventData(h);
    evt.data.u64 = (uint64_t)data;
    evt.events |= has_epollrdhup;
    if (epoll_ctl(_epfd, EPOLL_CTL_ADD, h->GetFD(), &evt) < 0) {
        delete data;
        return -1;
    }
    _reg_event_datas.emplace(reinterpret_cast<uint64_t>(h), data);
    return 0;
}

int EventDispatcher::RemoveConsumer(IOHandler* h) {
    BYTERPC_CHECK_GE(_epfd, 0);

    int ret = epoll_ctl(_epfd, EPOLL_CTL_DEL, h->GetFD(), NULL);
    if (ret == 0) {
        // Delay EventData deletion, because this `IOHandle' may have
        // events triggered in current Loop, and this function may
        // be called in `HandleTimeEvent' of timer, which is processed
        // ahead of epoll events
        auto it = _reg_event_datas.find(reinterpret_cast<uint64_t>(h));
        if (it == _reg_event_datas.end()) {
            BYTERPC_LOG(FATAL) << "Should not reach here";
            return ret;
        }
        _delay_deleted_event_datas.push(it->second);
        _reg_event_datas.erase(it);
    }
    return ret;
}

int EventDispatcher::AddEpollOut(IOHandler* h, bool pollin) {
    BYTERPC_CHECK_GE(_epfd, 0);

    epoll_event evt;
    EventData* data = new EventData(h);
    evt.data.u64 = (uint64_t)data;
    evt.events = EPOLLOUT | EPOLLET;
    evt.events |= has_epollrdhup;

    if (pollin) {
        evt.events |= EPOLLIN;
        if (epoll_ctl(_epfd, EPOLL_CTL_MOD, h->GetFD(), &evt) < 0) {
            // This fd has been removed from epoll via `RemoveConsumer',
            // in which case errno will be ENOENT
            delete data;
            return -1;
        }
        // Delay EventData deletion, because this `IOHandle' may have
        // events triggered in current Loop, and this function may
        // be called in `HandleTimeEvent' of timer, which is processed
        // ahead of epoll events
        auto it = _reg_event_datas.find(reinterpret_cast<uint64_t>(h));
        if (it != _reg_event_datas.end()) {
            _delay_deleted_event_datas.push(it->second);
            _reg_event_datas.erase(it);
        }
    } else {
        if (epoll_ctl(_epfd, EPOLL_CTL_ADD, h->GetFD(), &evt) < 0) {
            delete data;
            return -1;
        }
    }
    _reg_event_datas.emplace(reinterpret_cast<uint64_t>(h), data);
    return 0;
}

int EventDispatcher::RemoveEpollOut(IOHandler* h, bool pollin) {
    BYTERPC_CHECK_GE(_epfd, 0);

    int fd = h->GetFD();
    int ret = 0;
    EventData* data = nullptr;
    if (pollin) {
        epoll_event evt;
        data = new EventData(h);
        evt.data.u64 = (uint64_t)data;
        evt.events = EPOLLIN | EPOLLET;
        evt.events |= has_epollrdhup;
        ret = epoll_ctl(_epfd, EPOLL_CTL_MOD, fd, &evt);
        if (ret < 0) {
            delete data;
        }
    } else {
        ret = epoll_ctl(_epfd, EPOLL_CTL_DEL, fd, NULL);
    }

    if (ret == 0) {
        // Delay EventData deletion, because this `IOHandle' may have
        // events triggered in current Loop, and this function may
        // be called in `HandleTimeEvent' of timer, which is processed
        // ahead of epoll events
        auto it = _reg_event_datas.find(reinterpret_cast<uint64_t>(h));
        if (it != _reg_event_datas.end()) {
            _delay_deleted_event_datas.push(it->second);
            _reg_event_datas.erase(it);
        }
        if (pollin) {
            _reg_event_datas.emplace(reinterpret_cast<uint64_t>(h), data);
        }
    }
    return ret;
}

TimeoutIterator EventDispatcher::AddTimeConsumer(TimeEventHandler* h,
                                                 uint64_t microseconds_since_now) {
    return _timeout_manager->AddTimeConsumer(h, microseconds_since_now);
}

int EventDispatcher::RemoveTimeConsumer(TimeoutIterator timeout_iter) {
    return _timeout_manager->RemoveTimeConsumer(timeout_iter);
}

int EventDispatcher::WakeLoop(int event_fd) {
    int n = 0;
    size_t dummy = 1;
    do {
        n = write(event_fd, &dummy, sizeof(dummy));
    } while (BYTERPC_UNLIKELY(n < 0 && errno == EINTR));
    if (BYTERPC_UNLIKELY(n < 0)) {
        BYTERPC_PLOG(WARNING) << "Fail to wakeup loop";
        return -1;
    }
    return 0;
}

void EventDispatcher::HandleWakeEvent(int event_fd) {
    int n = 0;
    size_t dummy = 1;
    do {
        n = read(event_fd, &dummy, sizeof(dummy));
    } while (BYTERPC_UNLIKELY(n < 0 && errno == EINTR));
    if (BYTERPC_UNLIKELY(n < 0)) {
        BYTERPC_PLOG(ERROR) << "Fail to read wakeup fd";
    }
    if (event_fd == _quit_fd) {
        _stop = true;
    }
}

void EventDispatcher::RecycleDelayDeletedEventDatas() {
    while (!_delay_deleted_event_datas.empty()) {
        auto data = _delay_deleted_event_datas.front();
        delete data;
        _delay_deleted_event_datas.pop();
    }
}

void EventDispatcher::ReadLoopStats(LoopStats* loop_stats) {
    loop_stats->loop_count = _loop_stats.loop_count;
    loop_stats->num_time_events = _loop_stats.num_time_events;
    loop_stats->num_read_events = _loop_stats.num_read_events;
    loop_stats->num_write_events = _loop_stats.num_write_events;
    loop_stats->num_active_events = _loop_stats.num_active_events;
    loop_stats->num_events = _loop_stats.num_events;
}

}  // namespace byterpc
