set(transport_SRCS
  basic_transport.cpp
  trans_greeter.cpp
  transport.cpp)

if(BYTERPC_ENABLE_KTCP)
  list(APPEND transport_SRCS
    tcp/event_dispatcher.cpp
    tcp/tcp_acceptor.cpp
    tcp/tcp_transport.cpp)
endif()

if(BYTERPC_ENABLE_UTCP)
  list(APPEND transport_SRCS
    tarzan/tarzan.cpp
    tarzan/tarzan_context.cpp
    tarzan/tarzan_event_dispatcher.cpp
    tarzan/tarzan_tcp_transport.cpp
    tarzan/tarzan_acceptor.cpp)

endif()

if(BYTERPC_ENABLE_BYTE_EXPRESS)
  list(APPEND transport_SRCS
    byte_express/byte_express_acceptor.cpp
    byte_express/byte_express_context.cpp
    byte_express/byte_express_event_dispatcher.cpp
    byte_express/byte_express_memory_pool.cpp
    byte_express/byte_express_transport.cpp)
endif()

add_library(byterpc_transport OBJECT ${transport_SRCS})

target_link_libraries(byterpc_transport PRIVATE byterpc_internal_include)
