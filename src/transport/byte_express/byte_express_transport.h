// Copyright (c) 2021, ByteDance Inc. All rights reserved.
#pragma once

#include <be/client_engine.hpp>
#include <be/engine.hpp>
#include <be/socket/async_stream_socket.hpp>
#include <boost/intrusive/list.hpp>
#include <deque>
#include <memory>
#include <string>
#include <tuple>
#include <utility>

#include "byterpc/util/endpoint.h"
#include "iobuf/io_rbuf_pipe.h"
#include "transport/transport.h"

namespace byterpc {

class EventRegistry;
class ByteExpressEventDispatcher;
class Socket;
class IORbufPipe;
class ByteExpressMemoryPool;

class ByteExpressTransport : public Transport,
                             private ::be::AsyncStreamSocket::WriteCallback,
                             private ::be::AsyncStreamSocket::ReadCallback,
                             private ::be::EngineBase::RequestCallback {
public:
    // Construct a RDMA transport for RPC client;
    ByteExpressTransport(const util::EndPoint& remote_side,
                         const util::EndPoint& local_side,
                         EventRegistry* ev_reg,
                         Socket* socket,
                         int64_t conn_timeout_us = 0);

    // Construct a RDMA transport for an accepted server connection;
    ByteExpressTransport(::be::Engine::UPtr&& engine, Socket* socket);

    // ip/port of the local end of the connection
    util::EndPoint local_side() const override;

    // ip/port of the other end of the connection.
    util::EndPoint remote_side() const override;

    TransportType GetTransportType() const override {
        return TYPE_RDMA;
    }

    // Indicating that the caller wants to read from the transport
    // After data is ready, corresponding HandleReadEvent() will be
    // invoked on `_socket`;
    bool StartRead(size_t read_limit) override;

    // Write buffer data to transport.
    ssize_t StartWrite(IOBlockRef&& first,
                       std::unique_ptr<IOBuf>&& second = nullptr,
                       std::unique_ptr<IOBuf>&& third = nullptr,
                       std::vector<std::pair<std::unique_ptr<IOBuf>, RdmaBufInfo>>&&
                           rdma_write_bufs = {}) override;

    int GetRdmaMemoryRkey(const void* addr, uint32_t* rkey) const override;

    void ResetSocket(Socket* replacement) override;

    // reset a IOHandler
    void Reset(int error_code) override;

    uint32_t NumOfResets() const override;

    void OwnerRef() override;

    void OwnerUnref() override;

    class Connector : public boost::intrusive::list_base_hook<>,
                      private ::be::Engine::ConnectCallback {
    public:
        Connector(ByteExpressTransport* parent,
                  ::be::ClientEngine::UPtr&& engine,
                  const util::EndPoint& remote_side,
                  const util::EndPoint& local_side,
                  bool bind_local_ip,
                  int64_t conn_timeout_us)
            : _parent(parent),
              _engine(std::move(engine)),
              _remote_side(remote_side),
              _local_side(local_side),
              _bind_local_ip(bind_local_ip),
              _conn_timeout_us(conn_timeout_us),
              _ev_reg(parent->_ev_reg),
              _start_ts(0U) {}

        ~Connector() override;

        void Connect();

    private:
        void ConnectSuccess() noexcept override;
        void ConnectError(const ::be::TransportException& ex) noexcept override;
        void ConnectComplete();

        ByteExpressTransport* _parent;
        ::be::ClientEngine::UPtr _engine;
        util::EndPoint _remote_side;
        util::EndPoint _local_side;
        bool _bind_local_ip;
        int64_t _conn_timeout_us;
        ByteExpressEventDispatcher* _ev_reg;
        uint64_t _start_ts;
    };

    int PreConnect() override {
        return ConnectIfNot();
    }

    bool IsConnected() override {
        return _connected;
    }

protected:
    // XXX(zhp): we do not use IOHandler in RDMA
    void HandleReadEvent() override {}
    void HandleWriteEvent() override {}
    int GetFD() const override;

private:
    // Returns 0 on success/connecting, -1 otherwise
    int ConnectIfNot();

    bool IsConnecting() const {
        return _connector != nullptr;
    }

    void WriteSuccess() noexcept override;
    void WriteError(const ::be::TransportException& ex) noexcept override;

    void ReadBufferAvailable(const ::be::IOBuf& msg) noexcept override;
    void ReadBufferAvailable(std::unique_ptr<::be::IOBuf>&& msg) noexcept override;
    void ReadEOF() noexcept override;
    void ReadError(const ::be::TransportException& ex) noexcept override;

    void RequestSuccess() noexcept override;
    void RequestError(const ::be::TransportException& ex) noexcept override;

    void MaybeRecycle();
    void NotifyFailed(int error_code);

    void FailConnect(int error_code);
    void FailWrite(int error_code);
    void FailRead(int error_code);
    void FailRequest(int error_code);

    void StartFail(int error_code);
    void FinishFail();

    void DoWrite(size_t block_num);
    void WriteImpl(struct ibv_sge* sglist, size_t block_num);
    void DoWrite(IOBuf* buf);
    void WriteImpl(IOBuf* buf, struct ibv_sge* sglist, size_t block_num);
    void DoRdmaWrite(IOBuf* buf, uint64_t remote_addr, uint32_t rkey);
    void RdmaWriteImpl(
        IOBuf* buf, struct ibv_sge* sglist, size_t block_num, uint64_t remote_addr, uint32_t rkey);
    void AssembleWriteSglist(IOBlockRef* ref, struct ibv_sge* sge, const struct ibv_pd* pd);

    void UpdateEndpoint(const ::be::Engine& engine);

    uint32_t GetSpareSlot() {
        if (BYTERPC_LIKELY(_producer_head >= _consumer_tail)) {
            return WRITE_QUEUE_SIZE - (_producer_head - _consumer_tail);
        } else {
            return WRITE_QUEUE_SIZE - (UINT32_MAX - _consumer_tail + 1 + _producer_head);
        }
    }

    // ONLY for test usage
    static int TEST_BEException2ErrCode(const ::be::TransportException& ex);

    bool _client;
    const ibv_pd* _fixed_pd;
    ::be::Engine* _engine;
    ::be::EngineBase::Attribute _engine_attribute;
    ::be::AsyncStreamSocket::UPtr _sock;
    util::EndPoint _local_side;
    util::EndPoint _remote_side;
    // True if _local_side is a designated non-default address
    // Must be decided before calling ConnectIfNot which may update _local_side
    bool _bind_local_ip;
    Socket* _socket;
    bool _connected;
    bool _read_enabled;
    std::unique_ptr<IORbufPipe> _rbuf_pipe;

    // For more explanation about this ring_buf.
    // check out comment in `tarzan_tcp_transport.h`
    IOBlockRef _write_buffer[WRITE_QUEUE_SIZE];
    // pointer to cache next submiting req/resp
    uint32_t _producer_head;
    // pointer to req/resp which will be sent next
    uint32_t _consumer_head;
    // pointer to release already sent IOBlockRef
    uint32_t _consumer_tail;
    // pending reqs/resps number
    // NOTE: record pending message numbers before connected
    size_t _pending_msg_num;
    // record block_num for every write
    std::deque<size_t> _writing_refs;
    // secondary buffer while `_write_buffer' is run out
    std::deque<std::unique_ptr<IOBuf>> _secondary_buffer;
    // tuple of (rdma_write_buf, remote_addr, rkey)
    std::deque<std::tuple<std::unique_ptr<IOBuf>, uint64_t, uint32_t>> _rdma_write_buffer;

    int _owner_ref;
    uint32_t _reset_count;
    int _err;
    // ONLY used by client side
    Connector* _connector;
    ByteExpressEventDispatcher* _ev_reg;
    int64_t _conn_timeout_us;
};

}  // namespace byterpc
