// Copyright (c) 2024, ByteDance Inc. All rights reserved.
#pragma once

#include <atomic>

#include <be/mm/rdma_memory_pool.hpp>

#include "byterpc/mem/mem_page_info.h"
#include "mem/hub.h"
#include "mem/memory_pool.h"
#include "transport/byte_express/byte_express_context.h"

namespace byterpc {

class ByteExpressMemoryPool final : public be::RdmaMemoryPool, public PageObserver {
public:
    static ByteExpressMemoryPool* Get() {
        return g_byte_express_memory_pool;
    }
    static bool Init();
    static void Reset();

    static void CreateMemoryRegion(void* addr, size_t size, void** user_data);
    static void DeleteMemoryRegion(void* addr, size_t size, void* user_data);

private:
    static ByteExpressMemoryPool* g_byte_express_memory_pool;

public:
    void* Allocate(size_t size, int node_id) override;
    void* AlignedAllocate(size_t alignment, size_t size, int node_id) override;
    void Deallocate(void* block) override;
    // GetMR only allows `addr` ptr returned from Allocate/AlignedAllocate
    const struct ibv_mr* GetMR(const void* addr, const struct ibv_pd* pd) override;
    int RegExtMemPage(const MemPageInfo& page) override;
    int UnregExtMemPage(const MemPageInfo& page) override;
    std::string GetObserverTransportType() override;

    // QueryMR allows `addr` either allocated from memory pool or registered by users, and `addr`
    // can be any ptr as long as it is within a memory region (not necessary to be the exact ptr
    // returned from Allocate/AlignedAllocate)
    const struct ibv_mr* QueryMR(const void* addr, const struct ibv_pd* pd);

protected:
    template <typename CtxIt> ByteExpressMemoryPool(CtxIt first, CtxIt last);
    ~ByteExpressMemoryPool() override;
    void Destroy() override {}

private:
    struct MemoryRegionInfo {
        template <typename CtxIt>
        MemoryRegionInfo(void* addr, size_t size, CtxIt first, CtxIt last)
            : addr(addr), size(size), mr(new MemoryRegion(addr, size, first, last)) {}
        bool ContainAddr(const void* ptr) const {
            return (addr <= ptr &&
                    static_cast<const uint8_t*>(ptr) < static_cast<uint8_t*>(addr) + size);
        }
        void* addr;
        size_t size;
        std::unique_ptr<MemoryRegion> mr;
    };

    // used to pass to ByteExpressContext to create be::Worker. it is also have one copy
    // in BemallocMemoryPool. the copy in BemallocMemoryPool will delete after numa-aware.
    std::vector<::be::RdmaContext::SPtr> _rdma_ctxs;

    // _ext_region_map contains regions registered by others except MemoryPool (e.g. Tarzan/User)
    std::map<const void*, const MemoryRegionInfo*> _ext_region_map;
    // rwlock for _ext_region_map
    byte::RwLock _ext_region_map_rwlock;

    // region_map_cache maintain cache of regions either from MemoryPool and others,
    // i.e. it is a cache for (_ext_region_map + MemPool::_region_map)
    static thread_local std::map<const void*, const MemoryRegionInfo*> region_map_cache;
};

}  // namespace byterpc
