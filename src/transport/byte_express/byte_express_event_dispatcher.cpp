// Copyright (c) 2021, ByteDance Inc. All rights reserved.
#include <be/event/async_timeout.hpp>

#include <sys/epoll.h>
#include <sys/eventfd.h>
#include <sys/socket.h>
#include <sys/types.h>
#include <unistd.h>
#include <algorithm>
#include <iterator>
#include <utility>

#include "byterpc/byterpc_flags.h"
#include "byterpc/exec_ctx.h"
#include "byterpc/thread/priority_task.h"
#include "byterpc/util/compiler_specific.h"
#include "byterpc/util/owner_ptr.h"
#include "byterpc/util/timestamp.h"
#include "metrics/metrics_inl.h"

// include order matter
// metrics_inl -> logging.h -> be_logging(byte_express_context)
#include "byterpc/util/logging.h"

#include "rpc/timeout_manager.h"
#include "rpc/timerfd_manager.h"
#include "transport/byte_express/byte_express_context.h"
#include "transport/byte_express/byte_express_event_dispatcher.h"
#include "util/fd_guard.h"
#include "util/fd_utility.h"

#include <byte/string/number.h>
#include <byte/thread/this_thread.h>

#ifdef BYTERPC_ENABLE_FIBER
#include <byte/fiber/event.h>
#include <byte/fiber/fiber.h>
#endif

namespace byterpc {

constexpr int kNumEvents = 32;

struct EventData {
    util::owner_ptr<IOHandler> handler;
    uint32_t reset_count;

    explicit EventData(IOHandler* handler)
        : handler(handler), reset_count(handler->NumOfResets()) {}
};

class EventContext : public IOHandler {
public:
    explicit EventContext(::be::EventHandler* handler)
        : _handler(handler), _reset_count(0), _owner_ref(0) {}

    void HandleReadEvent() override {
        _handler->NotifyEvent(::be::EventHandler::READ);
    }

    void HandleWriteEvent() override {
        _handler->NotifyEvent(::be::EventHandler::WRITE);
    }

    int GetFD() const override {
        return _handler->GetHandlerFD();
    }

    uint32_t NumOfResets() const override {
        return _reset_count;
    }

    void Reset(int error_code) {
        // This is only called in dtor of EventDispatcher
        ++_reset_count;
    }

    void OwnerRef() override {
        ++_owner_ref;
    }

    void OwnerUnref() override {
        --_owner_ref;
        if (_owner_ref == 0) {
            delete this;
        } else if (_owner_ref < 0) {
            BYTERPC_LOG(FATAL) << "reference should not be negative!";
        }
    }

    ::be::EventHandler* GetHandler() const {
        return _handler;
    }

private:
    ::be::EventHandler* _handler;
    uint32_t _reset_count;
    int _owner_ref;
};

class ByteExpressTimeoutHandler : public TimeEventHandler {
public:
    explicit ByteExpressTimeoutHandler(::be::AsyncTimeout* handler) : _handler(handler) {}

    void HandleTimeEvent() override {
        _handler->FireTimeout();
    }

    ::be::AsyncTimeout* GetHandler() const {
        return _handler;
    }

private:
    ::be::AsyncTimeout* _handler;
};

struct TimeoutData {
    ByteExpressTimeoutHandler* handler;
    TimeoutIterator timeout_iter;

    explicit TimeoutData(::be::AsyncTimeout* obj) {
        handler = new ByteExpressTimeoutHandler(obj);
        BYTERPC_CHECK(handler);
    }

    ~TimeoutData() {
        if (handler)
            delete handler;
    }
};

ByteExpressEventDispatcher::ByteExpressEventDispatcher(bool polling, bool enable_napi)
    : _epfd(-1),
      _stop(false),
      _timer_fd_ts(UINT64_MAX),
      _worker(nullptr),
      _thread_ctx(nullptr),
      _num_inflight_connectors(0),
      _enable_napi(enable_napi) {
    _timeout_manager.reset(new byterpc::TimeoutManager());
    int thread_id = byte::ThisThread::GetId();
    _loop_metrics.reset(new LoopMetrics(TYPE_RDMA, byte::NumberToString(thread_id), &_loop_stats));

    _epfd = epoll_create(1024 * 1024);
    if (_epfd < 0) {
        BYTERPC_LOG(FATAL) << "Fail to create epoll";
        return;
    }
    BYTERPC_CHECK_EQ(0, util::make_close_on_exec(_epfd));

    _quit_fd = eventfd(0, EFD_NONBLOCK);
    BYTERPC_CHECK_GE(_quit_fd, 0);
    epoll_event quit_fd_evt;
    quit_fd_evt.events = EPOLLIN | EPOLLET;
    quit_fd_evt.data.u64 = static_cast<uint64_t>(_quit_fd);
    if (epoll_ctl(_epfd, EPOLL_CTL_ADD, _quit_fd, &quit_fd_evt) != 0) {
        BYTERPC_LOG(FATAL) << "Fail to epoll_ctl add quit fd";
        return;
    }

    _wakeup_fd = eventfd(0, EFD_NONBLOCK);
    BYTERPC_CHECK_GE(_wakeup_fd, 0);
    epoll_event wakeup_evt;
    wakeup_evt.events = EPOLLIN | EPOLLET;
    wakeup_evt.data.u64 = static_cast<uint64_t>(_wakeup_fd);
    if (epoll_ctl(_epfd, EPOLL_CTL_ADD, _wakeup_fd, &wakeup_evt) != 0) {
        BYTERPC_LOG(FATAL) << "Fail to epoll_ctl add wakeup fd";
        return;
    }

    _timer_fd = TimerfdManager::TimerfdCreate();
    if (_timer_fd == -1) {
        BYTERPC_LOG(FATAL) << "Fail to create timer_fd";
        return;
    }
    epoll_event timerfd_evt;
    timerfd_evt.events = EPOLLIN | EPOLLET;
    timerfd_evt.data.u64 = static_cast<uint64_t>(_timer_fd);
    if (epoll_ctl(_epfd, EPOLL_CTL_ADD, _timer_fd, &timerfd_evt) != 0) {
        BYTERPC_LOG(FATAL) << "Fail to epoll_ctl add timer_fd";
        return;
    }

    _thread_ctx = ExecCtx::GetCurrentThreadContext();
    // create & start worker
    _worker = ByteExpressContext::Get().CreateWorker(this, polling, enable_napi);
    _worker->SetEventCallback(this);
    _worker->Start();
}

ByteExpressEventDispatcher::~ByteExpressEventDispatcher() {
    // assertion: all engines are destroyed or to be destroyed when this is invoked.
    // there can't be any pending connect requests when the ev_reg is destructing
    BYTERPC_CHECK(_pending_connectors.empty());

    // stop worker
    _worker->Stop();
    // sync with dispatcher thread. This ensures that all funcs scheduled from this thread to
    // dispatcher thread are invoked. If no acceptor is created on this thread, this is enough to
    // ensure that this thread can exit gracefully. More synchronization are needed if acceptor is
    // created, which is implemented in ByteExpressAcceptor::StopAccept().
    ByteExpressContext::Get().SyncWithDispatcherThread();

    // unregister & detach all events and timeouts in dtor to ensure that those handlers can be
    // properly destructed even if they outlives the EventDispatcher.
    decltype(_attached_events) events;
    events.swap(_attached_events);
    for (auto* event : events) {
        event->GetHandler()->UnregisterHandler();
        event->GetHandler()->DetachEventRegistry();
    }
    decltype(_attached_timeouts) timeouts;
    timeouts.swap(_attached_timeouts);
    for (auto* timeout : timeouts) {
        timeout->handler->GetHandler()->CancelTimeout();
        timeout->handler->GetHandler()->DetachTimeoutManager();
    }

    // Release IOHandlers will remove consumer from event dispatcher, and delete all event
    // datas in _delay_deleted_event_datas.
    std::vector<EventData*> event_datas;
    std::transform(_reg_event_datas.begin(),
                   _reg_event_datas.end(),
                   std::back_inserter(event_datas),
                   [](std::pair<uint64_t, EventData*> it) { return it.second; });
    for (auto& event_data : event_datas) {
        event_data->handler->Reset(ECLOSE);
    }

    // Refer to comments in ~EventDispatcher method located in event_dispatcher.cpp.
    ExecCtx::LoopIfPossible();
    BYTERPC_CHECK(_reg_event_datas.empty());

    // release remaining EventData
    RecycleDelayDeletedEventDatas();

    _timeout_manager.reset();

    if (_epfd >= 0) {
        close(_epfd);
        _epfd = -1;
    }
    if (_quit_fd > 0) {
        close(_quit_fd);
    }
    if (_wakeup_fd > 0) {
        close(_wakeup_fd);
    }
    if (_timer_fd > 0) {
        TimerfdManager::TimerfdClose(_timer_fd);
        _timer_fd = -1;
    }
}

int ByteExpressEventDispatcher::LoopIfPossible() {
    uint64_t start_timestamp = 0;
    if (FLAGS_byterpc_enable_loop_metrics) {
        start_timestamp = util::TimeStamp::Now();
    }

    LoopPoll();

    if (FLAGS_byterpc_enable_loop_metrics) {
        uint64_t end_timestamp = util::TimeStamp::Now();
        _loop_metrics->UpdateMetrics(start_timestamp, end_timestamp);
    }

    return 0;
}

int ByteExpressEventDispatcher::LoopForever() {
    BYTERPC_CHECK(_epfd >= 0);

    // set '_stop' here to false to enable reenterable LoopForever
    _stop = false;

    if (_enable_napi) {
        return LoopForeverNapi();
    }

    int event_wait_ms = 0;
    while (!_stop) {
        _loop_stats.loop_count += 1;

        int ret = LoopEvent(event_wait_ms, nullptr);
        if (BYTERPC_UNLIKELY(ret != 0)) {
            return ret;
        }

        event_wait_ms = GetWaitTimeoutInMs();
    }

    ExecCtx::LoopIfPossible();
    return 0;
}

int ByteExpressEventDispatcher::LoopForeverNapi() {
    int event_wait_ms = 0;

    if (FLAGS_byterpc_napi_event_to_polling_valid_cpu_utilization_threshold > 100U ||
        FLAGS_byterpc_napi_polling_to_event_valid_cpu_utilization_threshold > 100U) {
        BYTERPC_LOG(ERROR)
            << "NAPI mode's valid cpu utilization threshold should be in range [0, 100]";
        return -1;
    }

    bool event_mode = true;
    uint64_t e2p_interval_ts = util::TimeStamp::UsToDuration(
        FLAGS_byterpc_napi_event_to_polling_estimate_valid_cpu_utilization_interval_us);
    float e2p_threshold =
        static_cast<float>(FLAGS_byterpc_napi_event_to_polling_valid_cpu_utilization_threshold) /
        100;
    uint64_t p2e_interval_ts = util::TimeStamp::UsToDuration(
        FLAGS_byterpc_napi_polling_to_event_estimate_valid_cpu_utilization_interval_us);
    float p2e_threshold =
        static_cast<float>(FLAGS_byterpc_napi_polling_to_event_valid_cpu_utilization_threshold) /
        100;
    uint64_t interval_start_ts = util::TimeStamp::Now();
    uint64_t cpu_cost_ts = 0UL;

    while (!_stop) {
        _loop_stats.loop_count += 1;

        LoopStats loop_stats;
        _thread_ctx->ReadLoopStats(&loop_stats);
        uint32_t last_num_outer_events = loop_stats.num_outer_events;
        uint32_t last_num_events = _loop_stats.num_events;

        uint64_t loop_start_ts = 0;
        if (event_mode) {
            int ret = LoopEvent(event_wait_ms, &loop_start_ts);
            if (BYTERPC_UNLIKELY(ret != 0)) {
                return ret;
            }
            event_wait_ms = GetWaitTimeoutInMs();
        } else {
            loop_start_ts = util::TimeStamp::Now();
            LoopPoll();
            ExecCtx::ConsumeRemoteTasks();
            event_wait_ms = 0;
        }

        _thread_ctx->ReadLoopStats(&loop_stats);
        uint32_t is_active = (_loop_stats.num_events - last_num_events) +
                             (loop_stats.num_outer_events - last_num_outer_events);

        if (event_mode) {
            if (is_active) {
                uint64_t now = util::TimeStamp::Now();
                cpu_cost_ts += now - loop_start_ts;
                uint64_t current_interval_ts = now - interval_start_ts;
                if (current_interval_ts >= e2p_interval_ts) {
                    float cpu_utilization = static_cast<float>(cpu_cost_ts) / current_interval_ts;
                    if (cpu_utilization >= e2p_threshold) {
                        BYTERPC_DLOG(INFO) << "NAPI switches to polling mode, cpu utilization: "
                                           << cpu_utilization * 100 << "%";
                        event_mode = false;
                    }
                    cpu_cost_ts = 0;
                    interval_start_ts = now;
                }
            }
        } else {
            if (is_active) {
                cpu_cost_ts += util::TimeStamp::Now() - loop_start_ts;
            } else {
                uint64_t current_interval_ts = loop_start_ts - interval_start_ts;
                if (current_interval_ts >= p2e_interval_ts) {
                    float cpu_utilization = static_cast<float>(cpu_cost_ts) / current_interval_ts;
                    if (cpu_utilization <= p2e_threshold) {
                        BYTERPC_DLOG(INFO) << "NAPI switches to event mode, cpu utilization: "
                                           << cpu_utilization * 100 << "%";
                        event_mode = true;
                    }
                    cpu_cost_ts = 0;
                    interval_start_ts = loop_start_ts;
                }
            }
        }
    }

    ExecCtx::LoopIfPossible();
    return 0;
}

uint32_t ByteExpressEventDispatcher::LoopPoll() {
    _loop_stats.loop_count += 1;
    ExecCtx::LoopIfPossible();
#ifdef BYTERPC_ENABLE_FIBER
    if (ExecCtx::FiberMode()) {
        byte::fiber::Yield();
    }
#endif
    // Make sure that timeout events are processed AFTER IO events;
    uint32_t active_events = _worker->Poll(FLAGS_byterpc_byte_express_max_wc_at_once);
    // for polling mode, it cannot distinguish read event or write event, so only count active event
    _loop_stats.num_active_events += active_events;
    _loop_stats.num_events += active_events;
    int n = _timeout_manager->ProcessTimeout();
    _loop_stats.num_time_events += n;
    _loop_stats.num_events += n;

    return active_events;
}

int ByteExpressEventDispatcher::LoopEvent(int wait_ms, uint64_t* awaken_ts) {
    epoll_event e[kNumEvents];

#ifdef BYTERPC_ENABLE_FIBER
    int n = 0;
    if (ExecCtx::FiberMode()) {
        if (wait_ms != 0) {
            int err = byte::fiber::WaitForEvent(
                _epfd, byte::fiber::EVENT_IN, BYTERPC_LIKELY(wait_ms < 0) ? -1 : wait_ms * 1000);
            if (UNLIKELY(err != 0 && err != EINTR && err != ETIMEDOUT)) {
                BYTERPC_LOG(ERROR) << "Fail to WaitForEvent epfd=" << _epfd;
                return -1;
            }
        }
        n = epoll_wait(_epfd, e, kNumEvents, 0);
    } else {
        n = epoll_wait(_epfd, e, kNumEvents, wait_ms);
    }
#else
    const int n = epoll_wait(_epfd, e, kNumEvents, wait_ms);
#endif

    if (_enable_napi && awaken_ts) {
        *awaken_ts = util::TimeStamp::Now();
    }

    if (n < 0) {
        if (BYTERPC_LIKELY(EINTR == errno)) {
            return 0;
        } else {
            BYTERPC_LOG(ERROR) << "Fail to epoll_wait epfd=" << _epfd;
            return -1;
        }
    }
    _loop_stats.num_active_events += n;
    _loop_stats.num_events += n;

    // Make sure that timeout events are processed before IO events;
    int time_num = _timeout_manager->ProcessTimeout();
    _loop_stats.num_time_events += time_num;
    _loop_stats.num_events += time_num;

    ProcessEvents(e, n);

    ExecCtx::LoopIfPossible();
    ExecCtx::ConsumeRemoteTasks();

    // Release delay-deleted EventData
    RecycleDelayDeletedEventDatas();
    return 0;
}

int ByteExpressEventDispatcher::QuitForeverLoop() {
    return WakeLoop(_quit_fd);
}

int ByteExpressEventDispatcher::WakeForeverLoop() {
    return WakeLoop(_wakeup_fd);
}

size_t ByteExpressEventDispatcher::NumRegisteredIOHandler() const {
    return _reg_event_datas.size();
}

int ByteExpressEventDispatcher::AddConsumer(IOHandler* h) {
    BYTERPC_CHECK(_epfd >= 0);

    epoll_event evt;
    evt.events = EPOLLIN;
    EventData* data = new EventData(h);
    evt.data.u64 = (uint64_t)data;
    if (epoll_ctl(_epfd, EPOLL_CTL_ADD, h->GetFD(), &evt) < 0) {
        delete data;
        return -1;
    }
    _reg_event_datas.emplace(reinterpret_cast<uint64_t>(h), data);
    return 0;
}

int ByteExpressEventDispatcher::RemoveConsumer(IOHandler* h) {
    BYTERPC_CHECK(_epfd >= 0);

    int ret = epoll_ctl(_epfd, EPOLL_CTL_DEL, h->GetFD(), NULL);
    if (ret == 0) {
        // Delay EventData deletion, because this `IOHandle' may have
        // events triggered in current Loop, and this function may
        // be called in `HandleTimeEvent' of timer, which is processed
        // ahead of epoll events
        auto it = _reg_event_datas.find(reinterpret_cast<uint64_t>(h));
        if (it == _reg_event_datas.end()) {
            BYTERPC_LOG(FATAL) << "Should not reach here";
            return ret;
        }
        _delay_deleted_event_datas.push(it->second);
        _reg_event_datas.erase(it);
    }
    return ret;
}

int ByteExpressEventDispatcher::AddEpollOut(IOHandler* h, bool pollin) {
    BYTERPC_CHECK(_epfd >= 0);

    epoll_event evt;
    EventData* data = new EventData(h);
    evt.data.u64 = (uint64_t)data;
    evt.events = EPOLLOUT;

    if (pollin) {
        evt.events |= EPOLLIN;
        if (epoll_ctl(_epfd, EPOLL_CTL_MOD, h->GetFD(), &evt) < 0) {
            // This fd has been removed from epoll via `RemoveConsumer',
            // in which case errno will be ENOENT
            delete data;
            return -1;
        }
        // Delay EventData deletion, because this `IOHandle' may have
        // events triggered in current Loop, and this function may
        // be called in `HandleTimeEvent' of timer, which is processed
        // ahead of epoll events
        auto it = _reg_event_datas.find(reinterpret_cast<uint64_t>(h));
        if (it != _reg_event_datas.end()) {
            _delay_deleted_event_datas.push(it->second);
            _reg_event_datas.erase(it);
        }
    } else {
        if (epoll_ctl(_epfd, EPOLL_CTL_ADD, h->GetFD(), &evt) < 0) {
            delete data;
            return -1;
        }
    }
    _reg_event_datas.emplace(reinterpret_cast<uint64_t>(h), data);
    return 0;
}

int ByteExpressEventDispatcher::RemoveEpollOut(IOHandler* h, bool pollin) {
    BYTERPC_CHECK(_epfd >= 0);

    int fd = h->GetFD();
    int ret = 0;
    EventData* data = nullptr;
    if (pollin) {
        epoll_event evt;
        data = new EventData(h);
        evt.data.u64 = (uint64_t)data;
        evt.events = EPOLLIN;
        ret = epoll_ctl(_epfd, EPOLL_CTL_MOD, fd, &evt);
        if (ret < 0) {
            delete data;
        }
    } else {
        ret = epoll_ctl(_epfd, EPOLL_CTL_DEL, fd, NULL);
    }

    if (ret == 0) {
        // Delay EventData deletion, because this `IOHandle' may have
        // events triggered in current Loop, and this function may
        // be called in `HandleTimeEvent' of timer, which is processed
        // ahead of epoll events
        auto it = _reg_event_datas.find(reinterpret_cast<uint64_t>(h));
        if (it != _reg_event_datas.end()) {
            _delay_deleted_event_datas.push(it->second);
            _reg_event_datas.erase(it);
        }
        if (pollin) {
            _reg_event_datas.emplace(reinterpret_cast<uint64_t>(h), data);
        }
    }
    return ret;
}

TimeoutIterator ByteExpressEventDispatcher::AddTimeConsumer(TimeEventHandler* h,
                                                            uint64_t microseconds_since_now) {
    return _timeout_manager->AddTimeConsumer(h, microseconds_since_now);
}

int ByteExpressEventDispatcher::RemoveTimeConsumer(TimeoutIterator timeout_iter) {
    return _timeout_manager->RemoveTimeConsumer(timeout_iter);
}

int ByteExpressEventDispatcher::WakeLoop(int event_fd) {
    int n = 0;
    size_t dummy = 1;
    do {
        n = write(event_fd, &dummy, sizeof(dummy));
    } while (BYTERPC_UNLIKELY(n < 0 && errno == EINTR));
    if (BYTERPC_UNLIKELY(n < 0)) {
        BYTERPC_PLOG(WARNING) << "Fail to wakeup loop";
        return -1;
    }
    return 0;
}

void ByteExpressEventDispatcher::HandleWakeEvent(int event_fd) {
    int n = 0;
    size_t dummy = 1;
    do {
        n = read(event_fd, &dummy, sizeof(dummy));
    } while (BYTERPC_UNLIKELY(n < 0 && errno == EINTR));
    if (BYTERPC_UNLIKELY(n < 0)) {
        BYTERPC_PLOG(ERROR) << "Fail to read wakeup fd";
    }
    if (event_fd == _quit_fd) {
        _stop = true;
    }
}

void ByteExpressEventDispatcher::RecycleDelayDeletedEventDatas() {
    while (!_delay_deleted_event_datas.empty()) {
        auto data = _delay_deleted_event_datas.front();
        delete data;
        _delay_deleted_event_datas.pop();
    }
}

void ByteExpressEventDispatcher::ReadLoopStats(LoopStats* loop_stats) {
    loop_stats->loop_count = _loop_stats.loop_count;
    loop_stats->num_time_events = _loop_stats.num_time_events;
    loop_stats->num_read_events = _loop_stats.num_read_events;
    loop_stats->num_write_events = _loop_stats.num_write_events;
    loop_stats->num_active_events = _loop_stats.num_active_events;
    loop_stats->num_events = _loop_stats.num_events;
}

void ByteExpressEventDispatcher::AttachEventRegistry(::be::EventHandler* handler) {
    auto ctx = std::make_unique<EventContext>(handler);
    ctx->OwnerRef();
    _attached_events.insert(ctx.get());
    handler->SetHandle(ctx.release());
}

void ByteExpressEventDispatcher::DetachEventRegistry(::be::EventHandler* handler) {
    auto* ctx = static_cast<EventContext*>(handler->GetHandle());
    _attached_events.erase(ctx);
    ctx->OwnerUnref();
}

bool ByteExpressEventDispatcher::RegisterHandler(::be::EventHandler* handler, uint16_t events) {
    BYTERPC_DCHECK_NE(handler->GetRegisteredEvents(), events);
    auto* ctx = static_cast<EventContext*>(handler->GetHandle());

    if (handler->GetRegisteredEvents() != ::be::EventHandler::NONE) {
        UnregisterHandler(handler);
    }

    // re-register handler after remove it
    if (events & ::be::EventHandler::READ) {
        auto ret = AddConsumer(ctx);
        if (ret != 0)
            return false;
    }
    if (events & ::be::EventHandler::WRITE) {
        auto ret = AddEpollOut(ctx, events & ::be::EventHandler::READ);
        if (ret != 0)
            return false;
    }
    return true;
}

void ByteExpressEventDispatcher::UnregisterHandler(::be::EventHandler* handler) {
    auto* ctx = static_cast<EventContext*>(handler->GetHandle());
    auto events = handler->GetRegisteredEvents();
    if (events == ::be::EventHandler::READ) {
        RemoveConsumer(ctx);
        return;
    }
    // event = WRITE or READ_WRITE
    // specify pollin = false to use EPOLL_CTL_DEL
    RemoveEpollOut(ctx, false);
}

void ByteExpressEventDispatcher::AttachTimeoutManager(::be::AsyncTimeout* obj) {
    std::unique_ptr<TimeoutData> timeout_data = std::make_unique<TimeoutData>(obj);
    _attached_timeouts.insert(timeout_data.get());
    obj->SetHandle(timeout_data.release());
}

void ByteExpressEventDispatcher::DetachTimeoutManager(::be::AsyncTimeout* obj) {
    auto* timeout_data = static_cast<TimeoutData*>(obj->GetHandle());
    _attached_timeouts.erase(timeout_data);
    delete timeout_data;
}

bool ByteExpressEventDispatcher::ScheduleTimeout(::be::AsyncTimeout* obj, TimeoutType timeout) {
    auto* timeout_data = static_cast<TimeoutData*>(obj->GetHandle());
    if (obj->IsScheduled()) {
        // unlink the timer before reschedule it
        auto ret = RemoveTimeConsumer(timeout_data->timeout_iter);
        BYTERPC_DCHECK_EQ(ret, 0);
    }
    timeout_data->timeout_iter =
        AddTimeConsumer(timeout_data->handler,
                        std::chrono::duration_cast<std::chrono::microseconds>(timeout).count());
    return true;
}

void ByteExpressEventDispatcher::CancelTimeout(::be::AsyncTimeout* obj) {
    auto* timeout_data = static_cast<TimeoutData*>(obj->GetHandle());
    RemoveTimeConsumer(timeout_data->timeout_iter);
}

void ByteExpressEventDispatcher::ProcessEvents(const epoll_event* events, int num_events) {
    for (int i = 0; i < num_events; ++i) {
        if (events[i].data.u64 == static_cast<uint64_t>(_timer_fd)) {
            _timer_fd_ts = UINT64_MAX;
            continue;
        }
        if (events[i].data.u64 == static_cast<uint64_t>(_quit_fd) ||
            events[i].data.u64 == static_cast<uint64_t>(_wakeup_fd)) {
            HandleWakeEvent(static_cast<int>(events[i].data.u64));
            continue;
        }
        IOHandler* h = nullptr;
        if (events[i].events & (EPOLLIN | EPOLLERR | EPOLLHUP)) {
            auto data = reinterpret_cast<EventData*>(events[i].data.u64);
            h = data->handler.get();
            if (h->NumOfResets() != data->reset_count) {
                continue;
            }
            h->HandleReadEvent();
            _loop_stats.num_read_events += 1;
        }
        if (events[i].events & (EPOLLOUT | EPOLLERR | EPOLLHUP)) {
            auto data = reinterpret_cast<EventData*>(events[i].data.u64);
            h = data->handler.get();
            if (h->NumOfResets() != data->reset_count) {
                continue;
            }
            h->HandleWriteEvent();
            _loop_stats.num_write_events += 1;
        }
    }
}

int ByteExpressEventDispatcher::GetWaitTimeoutInMs() {
    if ((ExecCtx::GetCurrentEventQueueSize() > 0) || (_thread_ctx->PendingTaskNum() > 0)) {
        return 0;
    }
    uint64_t timer_wait_us = _timeout_manager->GetLatestEventWaitUs();
    if (timer_wait_us == 0) {
        return 0;
    }
    if (timer_wait_us != UINT64_MAX) {
        uint64_t event_ts = _timeout_manager->GetLatestEventTimeStamp();
        // if the latest event in _timeout_manager happens before that in _timer_fd, we should
        // re-register time to _timer_fd
        if (event_ts < _timer_fd_ts) {
            TimerfdManager::TimerfdSettime(_timer_fd, timer_wait_us);
            _timer_fd_ts = event_ts;
        }
    }
    return -1;
}

bool ByteExpressEventDispatcher::RunInEventRegistryThread(Func fn) {
    class Functor : public Closure<void> {
    public:
        explicit Functor(Func fn) : _fn(std::move(fn)) {}

        void Run() override {
            _fn();
            delete this;
        }

        bool IsSelfDelete() const {
            return true;
        }

    private:
        Func _fn;
    };

    return _thread_ctx->InvokeInnerTask(new Functor(std::move(fn)));
}

void ByteExpressEventDispatcher::ScheduleConnector(ByteExpressTransport::Connector* connector) {
    if (_num_inflight_connectors >= FLAGS_byterpc_byte_express_max_inflight_connectors) {
        // can not start more connect request, enqueue it and return
        _pending_connectors.push_back(*connector);
        return;
    }
    // this connect request can be started directly
    // increase counter first since Connect() may fail directly, which may invoke
    // CompleteConnector()
    ++_num_inflight_connectors;
    connector->Connect();
}

void ByteExpressEventDispatcher::CompleteConnector(ByteExpressTransport::Connector* connector) {
    BYTERPC_CHECK(!connector->is_linked());
    BYTERPC_CHECK_GT(_num_inflight_connectors, 0U);
    // return directly if there are no pending connect requests
    if (_pending_connectors.empty()) {
        --_num_inflight_connectors;
        return;
    }
    // start next connect request if any
    auto& next = _pending_connectors.front();
    _pending_connectors.pop_front();
    next.Connect();
}

void ByteExpressEventDispatcher::CancelConnector(ByteExpressTransport::Connector* connector) {
    BYTERPC_CHECK(connector->is_linked());
    _pending_connectors.erase(_pending_connectors.iterator_to(*connector));
}

}  // namespace byterpc
