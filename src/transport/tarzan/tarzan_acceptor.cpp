// Copyright (c) 2021, ByteDance Inc. All rights reserved.

#include "transport/tarzan/tarzan_acceptor.h"

#include <arpa/inet.h>

#include <mutex>  // NOLINT(build/c++11)

#include "byterpc/server.h"
#include "byterpc/util/container_wrapper.h"
#include "byterpc/util/logging.h"
#include "rpc/event_base.h"
#include "rpc/server_socket.h"
#include "transport/tarzan/tarzan_tcp_transport.h"

namespace byterpc {

class TarzanAcceptorDispatcher {
public:
    TarzanAcceptorDispatcher(const util::EndPoint& listen_addr, UTCPListenMode mode);

    int RegisterTarzan();

    void UnRegisterTarzan();

private:
    enum class State {
        INIT,
        LISTENING,
        STOPPING,
    };

    util::EndPoint _listen_addr;
    int32_t _listen_fd;
    State _state;
    int32_t _worker_id;
    UTCPListenMode _listen_mode;
    std::mutex _mutex;
};

extern thread_local int32_t current_thread_tarzan_serial_number;

// TODO(dingjie): use raw pointer and unify destructor into ExecCtxNode.
thread_local util::HashMap<int32_t, TarzanAcceptor*> g_acceptors;

using TarzanAcceptorDispatcherMap =
    util::HashMap<util::EndPoint, std::weak_ptr<TarzanAcceptorDispatcher>, util::EndPointHash>;

std::unique_ptr<TarzanAcceptorDispatcherMap> g_tarzan_dispatcher_map;
std::mutex g_tarzan_dispatcher_map_mutex;

static std::shared_ptr<TarzanAcceptorDispatcher> GetTarzanDispatcher(
    const util::EndPoint& listen_addr, UTCPListenMode mode) {
    std::lock_guard<std::mutex> lock_guard(g_tarzan_dispatcher_map_mutex);
    if (!g_tarzan_dispatcher_map)
        g_tarzan_dispatcher_map = std::make_unique<TarzanAcceptorDispatcherMap>();

    auto it = g_tarzan_dispatcher_map->find(listen_addr);
    std::shared_ptr<TarzanAcceptorDispatcher> dispatcher;
    if (it == g_tarzan_dispatcher_map->end() || !(dispatcher = it->second.lock())) {
        dispatcher = std::make_shared<TarzanAcceptorDispatcher>(listen_addr, mode);
        (*g_tarzan_dispatcher_map)[listen_addr] = dispatcher;
    }
    return dispatcher;
}

TarzanAcceptorDispatcher::TarzanAcceptorDispatcher(const util::EndPoint& listen_addr,
                                                   UTCPListenMode mode)
    : _listen_addr(listen_addr),
      _listen_fd(-1),
      _state(State::INIT),
      _worker_id(-1),
      _listen_mode(mode) {}

int TarzanAcceptorDispatcher::RegisterTarzan() {
    std::lock_guard<std::mutex> guard(_mutex);
    if (State::INIT == _state) {
        // 1. Only the first time to listen on the same port will success.
        // 2. If tarzan_listen_on fill listen_addr with NULL, it can support both ipv4 and ipv6.
        // 3. If has_worker_id and worker_id of ops are set, only one worker
        //    which is equal to worker_id will listen on the port
        struct tarzan_sock_opts opts;
        memset(&opts, 0, sizeof(opts));
        if (UTCP_LISTEN_SINGLE_THREAD == _listen_mode) {
            opts.has_worker_id = 1;
            opts.worker_id = current_thread_tarzan_serial_number;
        }
        _listen_fd = tarzan_listen_on(
            _listen_addr.ip != util::IP_ANY ? (util::ip2str(_listen_addr.ip)).c_str() : NULL,
            _listen_addr.port,
            &opts,
            static_cast<void*>(&_listen_fd));
        if (_listen_fd < 0) {
            BYTERPC_LOG(ERROR) << "Tarzan failed to listen on: " << _listen_addr;
            return -1;
        }
        _worker_id = current_thread_tarzan_serial_number;
        _state = State::LISTENING;
    } else if (UTCP_LISTEN_SINGLE_THREAD == _listen_mode || State::STOPPING == _state) {
        return -1;
    }
    return _listen_fd;
}

void TarzanAcceptorDispatcher::UnRegisterTarzan() {
    // TODO(chenmeng): support tarzan close for multiple_thread mode
    // when tarzan_close api support can be called in any worker thread
    std::lock_guard<std::mutex> guard(_mutex);
    if (_state == State::STOPPING || current_thread_tarzan_serial_number != _worker_id)
        return;
    // Set close flag to listen_fd
    tarzan_close(_listen_fd);
    _listen_fd = -2;
    _state = State::STOPPING;
    return;
}

TarzanAcceptor::TarzanAcceptor(EventRegistry* ev_reg,
                               MessageHandlerManager* msg_handler_mgr,
                               Server* server,
                               UTCPListenMode mode)
    : _listen_fd(-1),
      _owner_ref(0),
      _listen_mode(mode),
      _ev_reg(ev_reg),
      _msg_handler_mgr(msg_handler_mgr),
      _related_server(server) {}

int TarzanAcceptor::StartAccept(const util::EndPoint& listen_addr) {
    _listen_addr = listen_addr;
    _dispatcher = GetTarzanDispatcher(listen_addr, _listen_mode);
    _listen_fd = _dispatcher->RegisterTarzan();
    if (_listen_fd < 0) {
        return -1;
    }
    g_acceptors.emplace(_listen_fd, this);
    return 0;
}

int TarzanAcceptor::StopAccept() {
    // Stop accept new request
    Reset();
    // Need temporary socketmap, ss->SetFailed may modify iterator
    SocketMap sm(_socket_map);
    for (const auto& it : sm.GetMap()) {
        ServerSocket* ss = reinterpret_cast<ServerSocket*>(it);
        ss->ResetTransport();
    }
    return 0;
}

void TarzanAcceptor::HandleNewConnection(tarzan_accept_sock* sock) {
    auto socket = new ServerSocket(_msg_handler_mgr, _related_server->GetSvcReg(), this);
    auto trans = new TarzanTcpTransport(sock->sid, _ev_reg, socket);
    if (!socket->ReadyToServe(trans)) {
        // not need to delete socket and trans, because if ReadyToServe failed, TarzanTcpTransport
        // will do Reset() and recycle TarzanTcpTransport and ServerSocket automatically
        return;
    }

    if (!_socket_map.Insert(socket)) {
        BYTERPC_LOG(WARNING) << "Insert socket to socketmap fail";
    }
}

void TarzanAcceptor::Reset() {
    if (_listen_fd >= 0) {
        _dispatcher->UnRegisterTarzan();
        g_acceptors.erase(_listen_fd);
        _listen_fd = -2;
    }
}

void TarzanAcceptor::OwnerRef() {
    ++_owner_ref;
}

void TarzanAcceptor::OwnerUnref() {
    if (--_owner_ref == 0) {
        Reset();
        delete this;
    } else if (_owner_ref < 0) {
        BYTERPC_LOG(FATAL) << "reference should not be negative!";
    }
}

int32_t TarzanAcceptor::GetFD() {
    return _listen_fd;
}

}  // namespace byterpc
