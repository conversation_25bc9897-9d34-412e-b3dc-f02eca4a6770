// Copyright (c) 2021, ByteDance Inc. All rights reserved.

#include "transport/tarzan/tarzan_event_dispatcher.h"

#include <byte/base/atomic.h>
#include <byte/string/number.h>
#include <byte/thread/this_thread.h>
#include <sys/epoll.h>
#include <sys/socket.h>
#include <sys/types.h>
#include <tarzan.h>

#include <algorithm>
#include <utility>
#include <vector>

#include "byterpc/byterpc_flags.h"
#include "byterpc/exec_ctx.h"
#include "byterpc/util/compiler_specific.h"
#include "byterpc/util/container_wrapper.h"
#include "byterpc/util/owner_ptr.h"
#include "byterpc/util/timestamp.h"
#include "metrics/metrics_inl.h"
#include "transport/tarzan/tarzan_acceptor.h"
#include "transport/tarzan/tarzan_context.h"
#include "util/fd_guard.h"
#include "util/fd_utility.h"

#include "byterpc/util/logging.h"

#ifdef BYTERPC_ENABLE_FIBER
#include <byte/fiber/fiber.h>
#endif

namespace byterpc {

static const unsigned int has_epollrdhup = true;
constexpr int kNumConnectEvents = 32;

static byte::Atomic<int32_t> global_thread_serial_number = -1;

struct EventData {
    util::owner_ptr<IOHandler> handler;
    uint32_t reset_count;

    explicit EventData(IOHandler* handler)
        : handler(handler), reset_count(handler->NumOfResets()) {}
};

thread_local int32_t current_thread_tarzan_serial_number = -1;

extern thread_local util::HashMap<int32_t, TarzanAcceptor*> g_acceptors;

TarzanEventDispatcher::TarzanEventDispatcher() : _accept_conn(false) {
    _timeout_manager.reset(new TimeoutManager());
    int thread_id = byte::ThisThread::GetId();
    _rpc_metrics.reset(new LoopMetrics(
        TransportType::TYPE_USERSPACE_TCP, byte::NumberToString(thread_id), &_loop_stats));
    if (-1 == current_thread_tarzan_serial_number) {
        current_thread_tarzan_serial_number = global_thread_serial_number.Increment();
        TarzanContext::GetInstance()->AddOneReadyTarzanWorker();
    }
}

void TarzanEventDispatcher::ClearOutActive(IOHandler* h) {
    // When we want to clear some active trans,
    // in that time, trans may already inside `_active_trans`
    // so we have no way to clear that trans.
    // instead, we save this trans in a map _to_clear_active_trans,
    // so in next time we clear all active trans, we can filter it out.
    // In addition, we need to hold a extra OwnerPtr to keep our trans alive.
    // (that's the reason why we need a map instead of a set)

    BYTERPC_CHECK_EQ(0u, _to_clear_active_trans.count(h));
    // take extra ownership
    _to_clear_active_trans[h] = util::owner_ptr<IOHandler>(h);
}

void TarzanEventDispatcher::WriteActiveHandlers() {
    if (BYTERPC_LIKELY(_to_clear_active_trans.empty())) {
        // clear all.
        while (!_active_trans.empty()) {
            _active_trans.front()->HandleWriteEvent();
            _active_trans.pop_front();
        }
    } else {
        // slow path: some active trans has been cleared
        //            so we need to check every active trans.
        while (!_active_trans.empty()) {
            IOHandler* trans = _active_trans.front();
            if (_to_clear_active_trans.count(trans) == 0) {
                trans->HandleWriteEvent();
            } else {
                // remove extra ownership.
                _to_clear_active_trans.erase(trans);
            }
            _active_trans.pop_front();
        }
        BYTERPC_CHECK(_to_clear_active_trans.empty());
    }
}

TarzanEventDispatcher::~TarzanEventDispatcher() {
    // Release IOHandlers will remove consumer from event dispatcher, and delete all event
    // datas in _delay_deleted_event_datas.
    std::vector<EventData*> event_datas;
    std::transform(_reg_event_datas.begin(),
                   _reg_event_datas.end(),
                   std::back_inserter(event_datas),
                   [](std::pair<uint64_t, EventData*> it) { return it.second; });
    for (auto& event_data : event_datas) {
        event_data->handler->Reset(ECLOSE);
    }

    // Refer to comments in ~EventDispatcher method located in event_dispatcher.cpp.
    ExecCtx::LoopIfPossible();
    BYTERPC_CHECK(_reg_event_datas.empty());

    // release remaining EventData
    RecycleDelayDeletedEventDatas();

    _timeout_manager.reset();
}

int TarzanEventDispatcher::LoopIfPossible() {
    return LoopInternal(0);
}

int TarzanEventDispatcher::LoopForever() {
    BYTERPC_LOG(FATAL) << "Should not reach here.";
    return -1;
}

int TarzanEventDispatcher::QuitForeverLoop() {
    BYTERPC_LOG(FATAL) << "Should not reach here.";
    return -1;
}

int TarzanEventDispatcher::WakeForeverLoop() {
    BYTERPC_LOG(FATAL) << "Should not reach here.";
    return -1;
}

inline int TarzanEventDispatcher::LoopInternal(int duration) {
    uint64_t start_timestamp = 0;
    if (FLAGS_byterpc_enable_loop_metrics) {
        start_timestamp = util::TimeStamp::Now();
    }

    tarzan_worker_run(current_thread_tarzan_serial_number);

    if (_accept_conn) {
        tarzan_accept_sock socks[kNumConnectEvents];
        int nr_sock =
            tarzan_accept_burst(current_thread_tarzan_serial_number, socks, kNumConnectEvents);
        for (int i = 0; i < nr_sock; i++) {
            auto iter = g_acceptors.find(*(static_cast<int32_t*>(socks[i].data)));
            if (BYTERPC_LIKELY(iter != g_acceptors.end())) {
                iter->second->HandleNewConnection(&socks[i]);
            } else {
                tarzan_close(socks[i].sid);
            }
        }
    }

    _loop_stats.loop_count += 1;

    ExecCtx::LoopIfPossible();

#ifdef BYTERPC_ENABLE_FIBER
    if (ExecCtx::FiberMode()) {
        byte::fiber::Yield();
    }
#endif

    const int kNumEvents = FLAGS_byterpc_tarzan_max_events_at_once;
    struct tarzan_event e[kNumEvents];
    IOHandler* h = nullptr;
    const int n = tarzan_event_poll(current_thread_tarzan_serial_number, e, kNumEvents);
    if (n < 0) {
        BYTERPC_LOG(ERROR) << "Fail to epoll_wait, errno=" << errno;
        return -1;
    }
    _loop_stats.num_active_events += n;
    _loop_stats.num_events += n;
    // Make sure that timeout events are processed before IO events;
    int time_num = _timeout_manager->ProcessTimeout();
    _loop_stats.num_time_events += time_num;
    _loop_stats.num_events += time_num;

    for (int i = 0; i < n; ++i) {
        if (e[i].events & (TARZAN_EVENT_IN | TARZAN_EVENT_ERR | TARZAN_EVENT_HUP)) {
            auto data = reinterpret_cast<EventData*>(e[i].data);
            h = data->handler.get();
            if (h->NumOfResets() != data->reset_count) {
                continue;
            }
            h->HandleReadEvent();
            ++_loop_stats.num_read_events;
        }
        if (e[i].events & (TARZAN_EVENT_OUT | TARZAN_EVENT_ERR | TARZAN_EVENT_HUP)) {
            auto data = reinterpret_cast<EventData*>(e[i].data);
            h = data->handler.get();
            if (h->NumOfResets() != data->reset_count) {
                continue;
            }
            h->HandleWriteEvent();
            ++_loop_stats.num_write_events;
        }
    }

    // As we use event to control transport behavior
    // so in order to only actual send data inside polling-loop
    // we call all active transport(as IoHandler)'s HandleWriteEvent
    // (read already inside polling-loop)
    WriteActiveHandlers();

    // release delay-deleted EventData
    RecycleDelayDeletedEventDatas();

    if (FLAGS_byterpc_enable_loop_metrics) {
        uint64_t end_timestamp = util::TimeStamp::Now();
        _rpc_metrics->UpdateMetrics(start_timestamp, end_timestamp);
    }

    return 0;
}

size_t TarzanEventDispatcher::NumRegisteredIOHandler() const {
    return _reg_event_datas.size();
}

int TarzanEventDispatcher::AddConsumer(IOHandler* h) {
    struct tarzan_event evt;
    evt.events = TARZAN_EVENT_IN | EPOLLET;
    EventData* data = new EventData(h);
    evt.data = data;
    evt.events |= has_epollrdhup;
    if (tarzan_event_ctrl(h->GetFD(), TARZAN_EVENT_CTRL_ADD, &evt) < 0) {
        delete data;
        return -1;
    }
    _reg_event_datas.emplace(reinterpret_cast<uint64_t>(h), data);
    return 0;
}

int TarzanEventDispatcher::RemoveConsumer(IOHandler* h) {
    int ret = tarzan_event_ctrl(h->GetFD(), TARZAN_EVENT_CTRL_DEL, NULL);
    if (ret == 0) {
        // Delay EventData deletion, because this `IOHandle' may have
        // events triggered in current Loop, and this function may
        // be called in `HandleTimeEvent' of timer, which is processed
        // ahead of epoll events
        auto it = _reg_event_datas.find(reinterpret_cast<uint64_t>(h));
        if (it == _reg_event_datas.end()) {
            BYTERPC_LOG(ERROR) << "Already remove ";
            return ret;
        }
        _delay_deleted_event_datas.push(it->second);
        _reg_event_datas.erase(it);
    }
    return ret;
}

int TarzanEventDispatcher::AddEpollOut(IOHandler* h, bool pollin) {
    struct tarzan_event evt;
    EventData* data = new EventData(h);
    evt.data = data;
    evt.events = TARZAN_EVENT_OUT | EPOLLET;
    evt.events |= has_epollrdhup;

    if (pollin) {
        evt.events |= TARZAN_EVENT_IN;
        // Tarzan process TARZAN_EVENT_CTRL_MOD the same with TARZAN_EVENT_ADD,
        // that is to say, event if the fd has been removed from epoll
        // via `RemoveConsumer`, it also can success with add the fd.
        if (tarzan_event_ctrl(h->GetFD(), TARZAN_EVENT_CTRL_MOD, &evt) < 0) {
            delete data;
            return -1;
        }
        // Delay EventData deletion, because this `IOHandle' may have
        // events triggered in current Loop, and this function may
        // be called in `HandleTimeEvent' of timer, which is processed
        // ahead of epoll events
        auto it = _reg_event_datas.find(reinterpret_cast<uint64_t>(h));
        if (it != _reg_event_datas.end()) {
            _delay_deleted_event_datas.push(it->second);
            _reg_event_datas.erase(it);
        }
    } else {
        if (tarzan_event_ctrl(h->GetFD(), TARZAN_EVENT_CTRL_ADD, &evt) < 0) {
            delete data;
            return -1;
        }
    }

    _reg_event_datas.emplace(reinterpret_cast<uint64_t>(h), data);
    return 0;
}

int TarzanEventDispatcher::RemoveEpollOut(IOHandler* h, bool pollin) {
    int fd = h->GetFD();
    int ret = 0;
    EventData* data = nullptr;
    if (pollin) {
        struct tarzan_event evt;
        data = new EventData(h);
        evt.data = data;
        evt.events = TARZAN_EVENT_IN | EPOLLET;
        evt.events |= has_epollrdhup;
        ret = tarzan_event_ctrl(fd, TARZAN_EVENT_CTRL_MOD, &evt);
        if (ret < 0) {
            delete data;
        }
    } else {
        ret = tarzan_event_ctrl(fd, TARZAN_EVENT_CTRL_DEL, NULL);
    }

    if (ret == 0) {
        // Delay EventData deletion, because this `IOHandle' may have
        // events triggered in current Loop, and this function may
        // be called in `HandleTimeEvent' of timer, which is processed
        // ahead of epoll events
        auto it = _reg_event_datas.find(reinterpret_cast<uint64_t>(h));
        if (it != _reg_event_datas.end()) {
            _delay_deleted_event_datas.push(it->second);
            _reg_event_datas.erase(it);
        }
        if (pollin) {
            _reg_event_datas.emplace(reinterpret_cast<uint64_t>(h), data);
        }
    }
    return ret;
}

TimeoutIterator TarzanEventDispatcher::AddTimeConsumer(TimeEventHandler* h,
                                                       uint64_t microseconds_since_now) {
    return _timeout_manager->AddTimeConsumer(h, microseconds_since_now);
}

int TarzanEventDispatcher::RemoveTimeConsumer(TimeoutIterator timeout_iter) {
    return _timeout_manager->RemoveTimeConsumer(timeout_iter);
}

void TarzanEventDispatcher::RecycleDelayDeletedEventDatas() {
    while (!_delay_deleted_event_datas.empty()) {
        auto data = _delay_deleted_event_datas.front();
        delete data;
        _delay_deleted_event_datas.pop();
    }
}

void TarzanEventDispatcher::ReadLoopStats(LoopStats* loop_stats) {
    loop_stats->loop_count = _loop_stats.loop_count;
    loop_stats->num_time_events = _loop_stats.num_time_events;
    loop_stats->num_read_events = _loop_stats.num_read_events;
    loop_stats->num_write_events = _loop_stats.num_write_events;
    loop_stats->num_active_events = _loop_stats.num_active_events;
    loop_stats->num_events = _loop_stats.num_events;
}

}  // namespace byterpc
