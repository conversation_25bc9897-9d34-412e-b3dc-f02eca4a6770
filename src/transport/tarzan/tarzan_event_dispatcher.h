// Copyright (c) 2021, ByteDance Inc. All rights reserved.
#pragma once

#include <deque>
#include <functional>
#include <memory>
#include <queue>
#include <vector>

#include "byterpc/rpc_stats.h"
#include "byterpc/util/container_wrapper.h"
#include "byterpc/util/owner_ptr.h"
#include "rpc/event_base.h"
#include "transport/transport.h"

/*
 * Dispatch edge triggered events of EventHandler
 */

namespace byterpc {

struct EventData;
class LoopMetrics;

class TarzanEventDispatcher : public EventDispatcherBase {
public:
    TarzanEventDispatcher();

    ~TarzanEventDispatcher() override;

    int LoopIfPossible() override;

    // Returns 0 on success, -1 otherwise;
    int LoopForever() override;

    // This can be invoked either on the same thread the EventRegsitry is
    // running, or on a different thread;
    //
    // Returns 0 on success, -1 otherwise;
    int QuitForeverLoop() override;

    int WakeForeverLoop() override;

    size_t NumRegisteredIOHandler() const override;

    // When edge-trigged events happen on the IoHandler, call corresponding
    // callback (i.e. HandleReadEvent());
    // Returns 0 on success, -1 otherwise;
    int AddConsumer(IOHandler* h) override;

    int RemoveConsumer(IOHandler* h) override;

    // When edge-trigged events happen on the IoHandler, call corresponding
    // callback (HandleWriteEvent() if pollin is false, otherwise can be
    // either HandleReadEvent() or HandleWriteEvent());
    // Returns 0 on success, -1 otherwise;
    int AddEpollOut(IOHandler* h, bool pollin) override;

    // Remove EPOLLOUT event on @h. If `pollin` is true, EPOLLIN event
    // will be kept and EPOLL_CTL_MOD will be used instead of EPOLL_CTL_DEL;
    // Returns 0 on success, -1 otherwise;
    int RemoveEpollOut(IOHandler* h, bool pollin) override;

    // After `microseconds_since_now`, a time event happen, call corresponding
    // callback (i.e. HandleTimeEvent())
    // Returns Timeout Iterator
    TimeoutIterator AddTimeConsumer(TimeEventHandler* h, uint64_t microseconds_since_now) override;

    void SetOutActive(IOHandler* h) override {
        _active_trans.push_back(h);
    }

    void ClearOutActive(IOHandler* h) override;

    // call all active trans's HandleWriteEvent
    void WriteActiveHandlers();

    // Remove time event on TimeoutIterator;
    // Return 0 on success, -1 otherwise;
    int RemoveTimeConsumer(TimeoutIterator timeout_iter) override;

    void ReadLoopStats(LoopStats* stats) override;

    inline bool GetAcceptConnection() {
        return _accept_conn;
    }

    inline void SetNeedAcceptConnection(bool accept_connection) {
        _accept_conn = accept_connection;
    }

private:
    bool _accept_conn;
    EventDispatcherLoopStats _loop_stats;
    std::unique_ptr<TimeoutManager> _timeout_manager;
    util::HashMap<uint64_t, EventData*> _reg_event_datas;
    std::queue<EventData*> _delay_deleted_event_datas;

    util::HashMap<IOHandler*, util::owner_ptr<IOHandler>> _to_clear_active_trans;
    std::deque<IOHandler*> _active_trans;
    std::unique_ptr<LoopMetrics> _rpc_metrics;

    int LoopInternal(int duration);

    void RecycleDelayDeletedEventDatas();
};

}  // namespace byterpc
