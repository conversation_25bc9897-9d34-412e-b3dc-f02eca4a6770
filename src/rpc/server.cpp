// Copyright (c) 2021, ByteDance Inc. All rights reserved.

#include "byterpc/server.h"

#include <gflags/gflags.h>

#include <memory>
#include <mutex>  // NOLINT(build/c++11)
#include <vector>

#include "builtin/service_greeter.h"
#ifdef BYTERPC_ENABLE_THRIFT
#include "byterpc/protocol/thrift/thrift_service.h"
#endif
#include "byterpc/rpc.h"
#include "byterpc/util/logging.h"
#include "protocol/message_base.h"
#include "protocol/message_handler_manager.h"
#include "rpc/connection_info.h"
#include "rpc/rpc_service_registry.h"
#include "transport/trans_greeter.h"
#include "transport/transport.h"
#include "util/signal.h"

namespace byterpc {

extern bool g_inited;

namespace {

static std::once_flag g_init_flag;
static std::unique_ptr<byterpc::MessageHandlerManager> g_server_msg_handler_mgr;

void InitServerEnv() {
    if (!g_server_msg_handler_mgr) {
        g_server_msg_handler_mgr.reset(new MessageHandlerManager());
        g_server_msg_handler_mgr->RegisterMessageHandlers();
    }

    // Ignore SIGPIPE, avoid closing current process.
    // SIGPIPE is the "broken pipe" signal, which is sent to a process
    // when it attempts to write to a pipe whose read end has closed.
    // It is almost impossible to fail.
    if (util::IgnoreSignal(SIGPIPE) != 0) {
        BYTERPC_LOG(FATAL) << "Ignore SIGPIPE fail";
    }
}

std::string StatusToString(Server::Status status) {
    if (Server::INIT == status) return "INIT";
    if (Server::LISTENING == status) return "LISTENING";
    if (Server::STOPPING == status) return "STOPPING";
    return "";
}

void StopLoopCallback(bool* stop_signal) {
    if (*stop_signal) {
        ExecCtx::QuitLoop();
    } else {
        if (ExecCtx::AddTimer(nullptr, NewClosure(StopLoopCallback, stop_signal), 1000) != 0) {
            BYTERPC_LOG(WARNING) << "Trigger stop loop timer event fail";
        }
    }
}

}  // namespace

Server::Server() : _listen_ep(), _status(INIT) {
    _svc_reg.reset(new RpcServiceRegistry);

    // Register all service observers, and
    // it MUST execs BEFORE Server::RegisterService
    std::call_once(g_init_flag, InitServerEnv);
    std::vector<MessageHandler*> handler;
    g_server_msg_handler_mgr->ListMessageHandlers(&handler);  // depend on `InitServerEnv`
    for (auto& h : handler) {
        auto server_handler = static_cast<ServerMessageHandler*>(h);
        auto ob = server_handler->GetServiceObserver();
        if (ob) {
            _svc_reg->AddServiceObserver(std::move(ob));
        }
    }
}

Server::~Server() {
    StopServer();
    Reset();
}

int Server::RegisterService(google::protobuf::Service* svc,
                            ServiceOwnership ownership,
                            bool enable_method_status,
                            bool allow_http_body_to_pb) {
    ServiceOptions option;
    option.ownership = ownership;
    option.enable_method_status = enable_method_status;
    option.allow_http_body_to_pb = allow_http_body_to_pb;
    return _svc_reg->RegisterService(svc, option);
}

int Server::RegisterService(google::protobuf::Service* svc,
                            ServiceOwnership ownership,
                            const byte::StringPiece& restful_mappings,
                            bool allow_http_body_to_pb) {
    ServiceOptions option;
    option.ownership = ownership;
    option.plugin_protocol_args = restful_mappings.as_string();
    option.plugin_protocol = ProtocolType::PROTOCOL_HTTP;
    option.allow_http_body_to_pb = allow_http_body_to_pb;
    return _svc_reg->RegisterService(svc, option);
}

int Server::RegisterService(ThriftService* thrift_svc, ServiceOwnership ownership) {
#ifdef BYTERPC_ENABLE_THRIFT
    BYTERPC_CHECK(thrift_svc);
    ServiceOptions option;
    option.ownership = ownership;
    return _svc_reg->RegisterService(thrift_svc, option);
#else
    BYTERPC_LOG(ERROR)
        << "Not support thrift, should enable by set compile option BYTERPC_ENABLE_THRIFT";
    return -1;
#endif
}

int Server::Start(const std::string& addr, const ServerOptions& options) {
    util::EndPoint listen_ep;
    if (util::str2endpoint(addr.c_str(), &listen_ep) < 0) {
        BYTERPC_LOG(ERROR) << "Invalid server address=" << addr;
        return -1;
    }

    return Start(listen_ep, options);
}

int Server::Start(int port, const ServerOptions& options) {
    if (port < 0 || port > 65535) {
        BYTERPC_LOG(ERROR) << "Invalid port=" << port;
        return -1;
    }

    return Start(util::EndPoint(util::IP_ANY, port), options);
}

int Server::InitAcceptor(TransportType trans_type, UTCPListenMode mode) {
    auto ev_reg = ExecCtx::GetOrNewThreadEventRegistry(trans_type);
    if (!ev_reg) {
        BYTERPC_LOG(ERROR) << "Fail to get ev_reg for " << trans_type;
        return -1;
    }

    CreateAcceptorOptions opts;
    opts.trans_type = trans_type;
    opts.ev = ev_reg;
    opts.msg_handler = g_server_msg_handler_mgr.get();
    opts.server = this;
    opts.mode = mode;
    auto acceptor = TransportGreeter::CreateAcceptor(opts);
    if (!acceptor || acceptor->StartAccept(_listen_ep)) {
        BYTERPC_LOG(ERROR) << "failed to create acceptor:  " << trans_type;
        return -1;
    }

    if (_listen_ep != acceptor->GetListenAddress()) {
        // port=0 makes kernel dynamically select a port from
        // https://en.wikipedia.org/wiki/Ephemeral_port
        BYTERPC_LOG(WARNING) << "listen addr actually is: " << acceptor->GetListenAddress();
        _listen_ep = acceptor->GetListenAddress();
    }

    if (!TransportGreeter::PostAcceptorStarted(opts)) {
        BYTERPC_LOG(ERROR) << "Post process failed on: " << trans_type;
        return -1;
    }

    // transfer ownership to server.
    _acceptors_arr[trans_type].reset(acceptor.release());
    return 0;
}

int Server::Start(const util::EndPoint& endpoint, const ServerOptions& options) {
    if (BYTERPC_UNLIKELY(!g_inited)) {
        BYTERPC_LOG(ERROR) << "Not invoke ExecCtx::Init yet!";
        return -1;
    }

    if (!g_server_msg_handler_mgr) {
        BYTERPC_LOG(ERROR) << "Fail to init server environment";
        return -1;
    }

    if (INIT != _status) {
        BYTERPC_LOG(ERROR) << "Fail to start server on " << endpoint
                           << " because of invalid status=" << StatusToString(_status);
        return -1;
    }

    if (!options._enable_ktcp && !options._enable_rdma && !options._enable_utcp) {
        BYTERPC_LOG(ERROR) << "Fail to start server, need enable at lease one transport type "
                           << "among ktcp/rdma/utcp";
        return -1;
    }

    if (options._enable_ktcp && options._enable_utcp) {
        BYTERPC_LOG(ERROR) << "Fail to start tcp acceptor and tarzan acceptor for same ip:port";
        return -1;
    }

    if (options._enable_builtin_service && RegisterBuiltinServices() < 0) {
        BYTERPC_LOG(ERROR) << "Fail to register builtin services";
        Reset();
        return -1;
    }

    _listen_ep = endpoint;
    _server_options = options;

    if (options._enable_ktcp && InitAcceptor(TYPE_KERNEL_TCP)) {
        Reset();
        return -1;
    }

    if (options._enable_rdma && InitAcceptor(TYPE_RDMA)) {
        Reset();
        return -1;
    }

    if (options._enable_utcp && InitAcceptor(TYPE_USERSPACE_TCP, options._utcp_listen_mode)) {
        Reset();
        return -1;
    }

    ExposeToThreadConnectionInfo();

    _svc_reg->SealRegistry();
    _status = LISTENING;
    return 0;
}

void Server::Reset() {
    _svc_reg->ClearServices();
    for (auto i = 0; i < NUM_TRANSPORT_TYPE; i++) {
        _acceptors_arr[i].reset();
    }
}

int Server::RegisterBuiltinServices() {
    if (InstallBuiltinServices(_svc_reg.get()) != 0) {
        BYTERPC_LOG(ERROR) << "Fail to install services";
        return -1;
    }

    return 0;
}

int Server::StartDummyServer(const std::string& addr, bool* stop_signal, loop_type_t loop_type) {
    ExecCtx ctx(loop_type);

    Server server;
    ServerOptions options;
    options._enable_ktcp = true;
    options._enable_builtin_service = true;
    if (server.Start(addr, options) < 0) {
        BYTERPC_LOG(ERROR) << "Fail to build dummy server";
        return -1;
    }

    switch (loop_type) {
    case LOOP_IF_POSSIBLE: {
        while (!(*stop_signal)) {
            ExecCtx::LoopOnce();
        }
        break;
    }
    case LOOP_UNTIL_QUIT: {
        if (ExecCtx::AddTimer(nullptr, NewClosure(StopLoopCallback, stop_signal), 1000) != 0) {
            BYTERPC_LOG(WARNING) << "Trigger stop loop timer event fail";
        }
        ExecCtx::LoopUntilQuit();
        break;
    }
    default:
        BYTERPC_LOG(ERROR) << "Invalid dummy server loop_type: " << loop_type;
    }

    if (server.StopServer() != 0) {
        BYTERPC_LOG(WARNING) << "Stop dummy server fail";
        return -1;
    }

    return 0;
}

int Server::StopServer() {
    if (LISTENING != _status) {
        BYTERPC_LOG(ERROR) << "Unable to stop server under invalid status="
                           << StatusToString(_status);
        return -1;
    }
    _status = STOPPING;

    int ret = 0;

    for (auto i = 0; i < NUM_TRANSPORT_TYPE; i++) {
        if (_acceptors_arr[i]) {
            if (_acceptors_arr[i]->StopAccept() < 0) {
                BYTERPC_LOG(ERROR) << "Stop  acceptor fail, " << i;
                ret = -1;
            }
        }
    }
    RemoveFromThreadConnectionInfo();
    return ret;
}

void Server::ExposeToThreadConnectionInfo() {
    std::shared_ptr<ThreadConnectionInfo> thread_info_ptr = ThreadConnectionInfo::GetInstancePtr();
    thread_info_ptr->AddServer(this);
}

void Server::RemoveFromThreadConnectionInfo() {
    std::shared_ptr<ThreadConnectionInfo> thread_info_ptr = ThreadConnectionInfo::GetInstancePtr();
    thread_info_ptr->RemoveServer(this);
}

}  // namespace byterpc
