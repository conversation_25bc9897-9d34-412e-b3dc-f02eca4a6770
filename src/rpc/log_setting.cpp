// Copyright (c) 2021, ByteDance Inc. All rights reserved.

#include <sys/stat.h>
#include <unistd.h>

#include "byte/include/byte_log.h"
#include "byterpc/byterpc_flags.h"
#include "byterpc/log_setting.h"
#include "byterpc/util/compiler_specific.h"
#include "util/safe_strerror_posix.h"

#ifdef BYTERPC_ENABLE_BYTE_EXPRESS
#include "be/logging.hpp"
#endif

namespace byterpc {

byte::LogContext* byterpc_log_ctx = nullptr;
byte::LogContext* dumpstack_log_ctx = nullptr;

static const char default_ctx_name[] = "byterpc";
static const char default_log_dir[] = "";

/* If byterpc_log_ctx points to the LogContext in
 * byte::LoggingSystem, this LogContext may be invalid
 * when byte::LoggingSystem destructed in advance,
 * thus it causes UAF. So BYTERPC_LOG_IS_ON MUST NOT
 * use byterpc_log_ctx.
 */
byte::LogLevel rpc_log_level = byte::GetMinLogLevel();

// Byterpc provided 4 kinds of log level when using glog in the past:
// 0 - INFO
// 1 - WARNING
// 2 - ERROR
// 3 - FATAL
//
// Now byterpc changes from glog to bytelog, which has different definition of log levels:
// LOG_LEVEL_ALL     = 0,
// LOG_LEVEL_DEBUG   = 1,
// LOG_LEVEL_INFO    = 2,
// LOG_LEVEL_WARNING = 3,
// LOG_LEVEL_ERROR   = 4,
// LOG_LEVEL_FATAL   = 5,
// LOG_LEVEL_NONE    = 100,
// LOG_LEVEL_DEFAULT = LOG_LEVEL_INFO,
// LOG_LEVEL_MIN_SEVERITY = LOG_LEVEL_DEBUG,
// LOG_LEVEL_MAX_SEVERITY = LOG_LEVEL_FATAL,
// LOG_AUDIT = 11,
//
// For such historical purposes, we should map glog's log level to bytelog's, in order to keep users
// from changing their existing codes. In the future we may inform users of the changes in LOG lib.
inline byte::LogLevel ToByteLogLevel(int lvl) {
    return static_cast<byte::LogLevel>(lvl + 2);
}

inline bool IsLogLevelValid(int lvl) {
    return (lvl >= -1 && lvl <= 3);
}

byte::LogLevel GetMinLogLevel() {
    return byterpc_log_ctx ? rpc_log_level : byte::GetMinLogLevel();
}

#ifdef BYTERPC_ENABLE_BYTE_EXPRESS
#define BE_LOG_IS_ON(log_level) (log_level >= GetMinLogLevel())

void InitByteExpressLogging(byte::LogContext* be_log_ctx) {
    be::logging::SetLogger(
        [=](const char* file, int line, be::logging::LogSeverity severity, const std::string& msg) {
            if (BYTERPC_LIKELY(IsLogLevelValid(static_cast<int>(severity)))) {
                byte::LogLevel lvl = ToByteLogLevel(static_cast<int>(severity));
                if (BE_LOG_IS_ON(lvl)) {
                    byte::LogMessager(be_log_ctx, file, line, lvl).stream() << msg;
                }
            }
        });
}

// Init BE logging in advance.
// If not so, we have to call InitLogging explicitly in codes to enable BE logging. Now we can use
// LOG under default settings without calling InitLogging, which is convenient.
__attribute__((constructor)) void InitByteExpressLoggingInAdvance() {
    InitByteExpressLogging(byterpc_log_ctx);
}
#endif

LoggingSettings::LoggingSettings()
    : name(default_ctx_name),
      log_dir(default_log_dir),
      min_log_level(FLAGS_byterpc_log_min_level),
      write_to_default_file(false),
      flush_when_abort(false) {}


/* 1. If InitLogging invoked by user, rpc_log_level MUST be same as the log level
 * of byterpc_log_ctx. rpc_log_level be updated first, then byterpc_log_ctx's
 * log level be set. The purpose is to avoid UAF issue.
 * 2. If not, the behavior should be aligned with which using byte_log's
 * global variable min_log_level, and rpc_log_level SHOULD NOT be used.
 */
int InitLogging(const LoggingSettings& settings) {
    // TODO(dongpeiwen):
    // It will be better if we can check whether the user has already set LogDir with
    // byte::SetByteLogDir. And we should do nothing in that case. However, there is no way to
    // confirm it in current bytelog.
    if (!settings.log_dir.empty()) {
        const char* path = settings.log_dir.c_str();
        if ((mkdir(path, 0755) == -1 || access(path, R_OK | W_OK) == -1) && errno != EEXIST) {
            std::string errmsg = util::safe_strerror(errno);
            fprintf(stderr,
                    "Could not create loogging file %s: %s (%d)\n",
                    path,
                    errmsg.c_str(),
                    errno);
            return -1;
        }
        byte::SetByteLogDir(settings.log_dir);
    }

    if (!settings.name.empty()) {
        byte::RegisterLoggerContext(settings.name, settings.write_to_default_file,
                                    ToByteLogLevel(settings.min_log_level));
    }
    // If name is empty, it will return bytelog's default context
    byterpc_log_ctx = byte::GetLogContext(settings.name);

#ifdef BYTERPC_ENABLE_BYTE_EXPRESS
    InitByteExpressLogging(byterpc_log_ctx);
#endif

    // If slow polling dump stack is enabled, we redirect output to another log file
    if (FLAGS_byterpc_init_slow_polling_detect_task) {
        const std::string log_name = "dumpstack";
        byte::RegisterLoggerContext(log_name, false, byte::LOG_LEVEL_DEFAULT);
        dumpstack_log_ctx = byte::GetLogContext(log_name);
    }

    /* To make sure byterpc_log_ctx and rpc_log_level
     * correctly reset each time InitLogging was called,
     * need SetMinLogLevel here.
     */
    if (SetMinLogLevel(settings.min_log_level) != 0) {
        return -1;
    }

    if (settings.flush_when_abort) {
        byte::EnableSymbolizeStacktrace();
    }

    return 0;
}

int SetMinLogLevel(int lvl) {
    if (BYTERPC_LIKELY(IsLogLevelValid(lvl))) {
        // Just set loglevel in byte_log
        if (byterpc_log_ctx == nullptr) {
            byte::SetMinLogLevel(ToByteLogLevel(lvl));
            return 0;
        }

        rpc_log_level = ToByteLogLevel(lvl);
        byte::SetLogContextMinLevel(byterpc_log_ctx, rpc_log_level);
        return 0;
    }
    fprintf(stderr,
            "Set min log level failed. The value should be in range[-1, 3], but got %d.\n",
            lvl);
    return -1;
}

}  // namespace byterpc
