// Copyright (c) 2021, ByteDance Inc. All rights reserved.

#include "rpc/client_socket.h"

#include <errno.h>

#include <vector>

#include "byterpc/byterpc_flags.h"
#include "byterpc/util/logging.h"
#include "byterpc/util/timestamp.h"
#include "iobuf/io_rbuf_pipe.h"
#include "metrics/byterpc_metrics.h"
#include "protocol/message_handler_manager.h"
#include "rpc/connection_info.h"
#include "rpc/event_base.h"
#include "rpc/stateful_controller.h"
#include "rpc/time_profiler.h"
#include "transport/tcp/tcp_transport.h"
#include "transport/trans_greeter.h"

namespace byterpc {

extern thread_local bool thread_shutdowned;

ClientSocket::ClientSocket(const util::EndPoint& remote_side,
                           EventRegistry* ev_reg,
                           MessageHandlerManager* msg_handler_mgr,
                           TransportType transport_type,
                           int64_t connect_timeout_ms,
                           const util::EndPoint& local_side)
    : _remote_side(remote_side),
      _ev_reg(ev_reg),
      _trans(nullptr),
      _msg_handler_mgr(msg_handler_mgr),
      _unwritten_bytes(0),
      _is_overcrowded(false),
      _owner_ref(0),
      _connect_timeout_ms(connect_timeout_ms),
      _preferred_protocol(PROTOCOL_UNKNOWN),
      _next_correlation_id(INVALID_CORRELATION_ID + 1),
      _last_invoke_timestamp(0),
      _http_correlation_id(INVALID_CORRELATION_ID),
      _create_time(time(0)),
      _thread_info_ptr() {
    CreateTransOptions opts;
    opts.trans_type = transport_type;
    opts.remote_side = remote_side;
    opts.local_side = local_side;
    opts.ev = _ev_reg;
    opts.socket = this;
    opts.timeout_us = static_cast<uint64_t>(_connect_timeout_ms * 1000);

    auto trans = TransportGreeter::CreateTransport(opts);
    if (!trans) {
        BYTERPC_LOG(FATAL) << "failed to create transport";
    }
    _trans.reset(trans.release());

    // add this socket to thread local thread_connection_info
    _thread_info_ptr = ThreadConnectionInfo::GetInstancePtr();
    if (auto ptr = _thread_info_ptr.lock())
        ptr->AddClientSocket(this);
}

ClientSocket::ClientSocket(const util::EndPoint& remote_side,
                           EventRegistry* ev_reg,
                           MessageHandlerManager* msg_handler_mgr,
                           Transport* trans)
    : _remote_side(remote_side),
      _ev_reg(ev_reg),
      _trans(trans),
      _msg_handler_mgr(msg_handler_mgr),
      _unwritten_bytes(0),
      _is_overcrowded(false),
      _owner_ref(0),
      _connect_timeout_ms(0),
      _preferred_protocol(PROTOCOL_UNKNOWN),
      _next_correlation_id(INVALID_CORRELATION_ID + 1),
      _last_invoke_timestamp(0),
      _http_correlation_id(INVALID_CORRELATION_ID),
      _create_time(time(0)) {}

ClientSocket::~ClientSocket() {
    // remove this socket from thread local thread_connection_info
    if (auto ptr = _thread_info_ptr.lock())
        ptr->RemoveClientSocket(this);

    // It's safety to invoke virtual function here, because
    // ClientSocket is decorated with `final`.
    if (!Failed() && static_cast<bool>(_trans)) {
        // No references, no pending rpc requests of ClientSocket,
        // we should close underlying transport and recycle it's resources.
        _trans->Reset(byterpc::ECLOSE);
    }
}

bool ClientSocket::RegisterController(Controller* cntl) {
    if (BYTERPC_UNLIKELY(thread_shutdowned)) {
        std::string error_text = "Unable to send a request when thread exiting";
        static_cast<StatefulController*>(cntl)->MarkFailed(EPERM, error_text);
        ExecCtx::Schedule([cntl, error_text]() { cntl->SetFailed(EPERM, error_text); });
        return false;
    }

    if (BYTERPC_UNLIKELY(Failed())) {
        Socket::Reset();
    }

    if (BYTERPC_UNLIKELY(_is_overcrowded)) {
        // NOTE: NEVER call `SetFailed' directly here, which may cause stack overflow
        // if caller keep sending next RPC immediately in Callback. So we first
        // mark the failure inplace, and `SetFailed' will be invoked in next Loop.
        std::stringstream remote;
        remote << remote_side();
        std::stringstream local;
        local << local_side();
        std::string transport_type = TransportTypeToString(GetTransportType());
        std::string error_text =
            "The connection from client " + local.str() + " to server " + remote.str() +
            " is overcrowded at client side with transport type " + transport_type;
        METRICS_byterpc_client_rpc_overcrowded()
            ->GetMetric({{rpctagk::kClientAddr, local.str()},
                         {rpctagk::kServerAddr, remote.str()},
                         {rpctagk::kTransportType, transport_type}})
            ->Add(1);
        static_cast<StatefulController*>(cntl)->MarkFailed(EOVERCROWDED, error_text);
        ExecCtx::Schedule([cntl, error_text]() { cntl->SetFailed(EOVERCROWDED, error_text); });
        return false;
    }

    CTRL_SET_PROFILER_FIELD(
        cntl, _client_serialize_req, util::TimeStamp::Now(), TotalLatencyType::None);
    // Register rpc call
    _pending_map.Push(cntl);

    return true;
}

void ClientSocket::SubmitWrite(
    Controller* cntl,
    IOBlockRef&& first,
    std::unique_ptr<IOBuf>&& second,
    std::unique_ptr<IOBuf>&& third,
    std::vector<std::pair<std::unique_ptr<IOBuf>, RdmaBufInfo>>&& rdma_write_bufs) {
    if (!RegisterController(cntl)) {
        return;
    }

    size_t writing_bytes =
        first.size() + (second ? second->size() : 0) + (third ? third->size() : 0);
    for (const std::pair<std::unique_ptr<IOBuf>, RdmaBufInfo>& it : rdma_write_bufs) {
        writing_bytes += it.first->size();
    }
    BYTERPC_DCHECK_GT(writing_bytes, 0UL);

    ssize_t remain_size = _trans->StartWrite(
        std::move(first), std::move(second), std::move(third), std::move(rdma_write_bufs));
    /* If returns 0, send message completely;
       If returns -1, send msg failed and SetFailed() is executed to
       set all pending requests failed, So not need do anything here.
       Others mean send message partially, so need push cntl to _writing_queue to send later.
    */
    if (BYTERPC_LIKELY(remain_size == 0)) {
        CTRL_SET_PROFILER_FIELD(
            cntl, _client_write_req, util::TimeStamp::Now(), TotalLatencyType::None);
        // connection built-in service
        if (FLAGS_byterpc_enable_collecting_connection_info) {
            AddOutput(1, writing_bytes);
        }
    } else if (remain_size > 0) {
        // NOT write complete
        AddUnwrittenBytes(remain_size);
        _writing_queue.emplace_back(cntl->CorrelationId(), remain_size);
    }
}

void ClientSocket::OwnerUnref() {
    --_owner_ref;
    if (BYTERPC_UNLIKELY(_owner_ref <= 0)) {
        BYTERPC_DCHECK_EQ(_owner_ref, 0) << "reference should not be negative!";
        BYTERPC_DCHECK(_pending_map.Empty());
        _trans->ResetSocket();
        delete this;
    }
}

void ClientSocket::ReleaseController(Controller* cntl) {
    _pending_map.Erase(cntl);
}

void ClientSocket::HandleWriteEvent() {
    BYTERPC_DCHECK(!_writing_queue.empty());

    auto& context = _writing_queue.front();
    if (FLAGS_byterpc_enable_time_profiler) {
        Controller* cntl = MatchPendingCall(context.first);
        if (BYTERPC_LIKELY(cntl)) {
            CTRL_SET_PROFILER_FIELD(
                cntl, _client_write_req, util::TimeStamp::Now(), TotalLatencyType::None);
        }
    }
    size_t written_bytes = context.second;
    _writing_queue.pop_front();
    CancelUnwrittenBytes(written_bytes);

    // connection built-in service
    if (FLAGS_byterpc_enable_collecting_connection_info) {
        AddOutput(1, written_bytes);
    }
}

void ClientSocket::HandleReadEvent(IORbufPipe* rbuf_pipe) {
    _last_invoke_timestamp = util::TimeStamp::Now();

    // The child controller which HandleReadEvent invoked might release the references
    // of ClientSocket, so that the ClientSocket might be recycled if no any refs.
    util::owner_ptr<ClientSocket> self_guard(this);

    ParseError pr = PARSE_ERROR_TRY_OTHERS;
    MessageHandler* msg_handler = nullptr;
    if (BYTERPC_LIKELY(_preferred_protocol != PROTOCOL_UNKNOWN)) {
        msg_handler = _msg_handler_mgr->FindMessageHandler(_preferred_protocol);
        BYTERPC_DCHECK(msg_handler != nullptr);
        auto handler = static_cast<ClientMessageHandler*>(msg_handler);
        pr = handler->HandleResponse(this, rbuf_pipe);
    }

    while (pr == PARSE_ERROR_TRY_OTHERS) {
        pr = _msg_handler_mgr->ParseHeader(rbuf_pipe, &msg_handler);
        if (pr == PARSE_OK) {
            auto handler = static_cast<ClientMessageHandler*>(msg_handler);
            pr = handler->HandleResponse(this, rbuf_pipe);
        }
    }

    if (pr != PARSE_OK && pr != PARSE_ERROR_NOT_ENOUGH_DATA) {
        BYTERPC_LOG(WARNING) << "Close connection due to unknown message, pr=" << pr
                             << " local_side=" << local_side() << " remote_side=" << remote_side()
                             << " transport_type=" << TransportTypeToString(GetTransportType())
                             << " magic_header: " << rbuf_pipe->to_string(4);
        _trans->Reset(EINVAL);
    } else {
        if (msg_handler) {
            _preferred_protocol = msg_handler->GetProtocolType();
        }
    }
}

void ClientSocket::SetFailed(int err_code, const std::string& reason) {
    switch (err_code) {
    case byterpc::EEOF:
    case byterpc::ECLOSE:
    case byterpc::ECONNECTFAILED:
    case byterpc::ERDMAVERBS:
    case byterpc::ERDMACOMP:
    case EINVAL:
    case ENETUNREACH:
    case ENETRESET:
    case ECONNABORTED:
    case ECONNRESET:
    case ECONNREFUSED:
    case ETIMEDOUT:
    case EPIPE:
        BYTERPC_LOG(WARNING) << "client socket is SetFailed, err_code=" << err_code
                             << " reason=" << reason << " local_side=" << local_side()
                             << " remote_side=" << remote_side()
                             << " transport_type=" << TransportTypeToString(GetTransportType());
        break;
    default:
        BYTERPC_LOG(WARNING) << "SetFailed() got unknown error code=" << err_code
                             << " reason=" << reason << " local_side=" << local_side()
                             << " remote_side=" << remote_side()
                             << " transport_type=" << TransportTypeToString(GetTransportType());
    }

    // Must clear `_writing_queue' first, or next rpc of this thread may be lost
    _writing_queue.clear();
    _unwritten_bytes = 0;
    _is_overcrowded = false;
    _last_invoke_timestamp = 0;

    Socket::SetFailed(err_code, reason);

    // Schedule all controllers to be `SetFailed` in next event loop
    // Also clear all rpc requests in `_pending_map`
    Controller* cntl_head = _pending_map.MergeAll();
    Controller* prev = nullptr;
    for (Controller* cntl = cntl_head; cntl != nullptr; cntl = cntl->_next) {
        // Cntl is terminating or failed, which means it has been scheduled to SetFailed and we
        // should remove it from list to prevent from double free.
        if (static_cast<StatefulController*>(cntl)->IsTerminating() ||
            static_cast<StatefulController*>(cntl)->Failed()) {
            if (prev) {
                prev->_next = cntl->_next;
            } else {
                cntl_head = cntl_head->_next;
            }
        } else {
            static_cast<StatefulController*>(cntl)->MarkFailed(err_code, reason);
            prev = cntl;
        }
    }

    OwnerRef();  // Prevents destruction before the following async task is consumed
    ExecCtx::Schedule([this, cntl_head, err_code, reason]() {
        Controller* curr = cntl_head;
        while (curr) {
            Controller* next = curr->_next;
            curr->SetFailed(err_code, reason);
            curr = next;
        }
        this->OwnerUnref();
    });
}

void ClientSocket::AddUnwrittenBytes(size_t bytes) {
    _unwritten_bytes += bytes;
    if (BYTERPC_UNLIKELY(_unwritten_bytes >= FLAGS_byterpc_socket_max_unwritten_bytes)) {
        _is_overcrowded = true;
    }
}

void ClientSocket::CancelUnwrittenBytes(size_t bytes) {
    _unwritten_bytes -= bytes;
    if (BYTERPC_UNLIKELY(_is_overcrowded) &&
        BYTERPC_LIKELY(_unwritten_bytes < FLAGS_byterpc_socket_max_unwritten_bytes)) {
        _is_overcrowded = false;
    }
}

void ClientSocket::CancelRpc(int64_t correlation_id) {
    Controller* cntl = MatchPendingCall(correlation_id);
    if (cntl) {
        cntl->StartCancelImpl();
    }
}

void ClientSocket::ResetTransport(int error_code) {
    _trans->Reset(error_code);
}

}  // namespace byterpc
