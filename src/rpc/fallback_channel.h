// Copyright (c) 2021, ByteDance Inc. All rights reserved.
#pragma once

#include <byte/include/assert.h>
#include <google/protobuf/stubs/callback.h>

#include <memory>

#include "byterpc/builder.h"
#include "rpc/channel_manager.h"

namespace byterpc {

class SubDone;
class EventRegistry;

typedef void (*SwitchWatcher)(const util::EndPoint& peer_side, void* context);

class FallbackChannel : public Builder::Channel,
                        public std::enable_shared_from_this<FallbackChannel> {
    friend class SubDone;

public:
    FallbackChannel();
    FallbackChannel(SwitchWatcher watcher, void* context);

    void Init(std::shared_ptr<Builder::Channel> prefer_subchannel,
              std::shared_ptr<Builder::Channel> backup_subchannel,
              ChannelHealthChecker* health_checker = nullptr);

    void CallMethod(const google::protobuf::MethodDescriptor* method,
                    google::protobuf::RpcController* controller,
                    const google::protobuf::Message* request,
                    google::protobuf::Message* response,
                    google::protobuf::Closure* done) override;

    EventRegistry* GetEventRegistry() const override;

    size_t NumInvoked() const override {
        return _num_invoked;
    }

    int64_t CustomizedDeferCloseSecond() const override {
        return _prefer_subchannel->CustomizedDeferCloseSecond();
    }

    util::EndPoint ServerAddress() const override;

    util::EndPoint ClientAddress() const override;

    TransportType GetTransportType() const override;

    void AllocCorrelationId(Controller* cntl) override {
        BYTE_ASSERT(false) << "NOT support allocate correlation id for fallback channel";
    }

    void CancelRpc(int64_t correlation_id) override {
        BYTE_ASSERT(false) << "NOT support cancel RPC for fallback channel";
    }

    static void ForceSwitchChannel(bool switch_backup);

private:
    // Callback `_watcher' while transport switched
    void FireSwitchWatcher();

    std::shared_ptr<Builder::Channel> GetPreferSubChannel() const {
        return _prefer_subchannel;
    }

    std::shared_ptr<Builder::Channel> GetBackupSubChannel() const {
        return _backup_subchannel;
    }

private:
    std::shared_ptr<Builder::Channel> _prefer_subchannel;
    std::shared_ptr<Builder::Channel> _backup_subchannel;
    bool _use_prefer_channel;
    size_t _num_invoked;
    bool _inited;
    SwitchWatcher _watcher;
    void* _watcher_context;
    ChannelHealthChecker* _health_checker;
};

}  // namespace byterpc
