add_library(byterpc_rpc OBJECT
  log_setting.cpp
  exec_ctx.cpp
  socket.cpp
  connection_info.cpp
  timeout_manager.cpp
  timerfd_manager.cpp
  controller.cpp
  stateful_controller.cpp
  byterpc_flags.cpp
  protobuf_serdes.cpp

  # server-only
  rpc_service_registry.cpp
  server.cpp
  server_socket.cpp
  client_socket.cpp
  channel_manager.cpp

  # client only
  builder.cpp
  protobuf_channel.cpp
  fallback_channel.cpp
  random_channel.cpp
  matrix_channel.cpp
  robust_channel.cpp
)

target_link_libraries(byterpc_rpc PRIVATE byterpc_internal_include)
