// Copyright (c) 2021, ByteDance Inc. All rights reserved.
#include "rpc/socket.h"

#include "byterpc/byterpc_flags.h"

namespace byterpc {

Socket::ConnectionStatistic::ConnectionStatistic()
    : _statistic_period(FLAGS_byterpc_collecting_period), _last_statistic(), _cur_statistic() {}

void Socket::ConnectionStatistic::CheckPeriod() {
    // if FLAGS_byterpc_collecting_period is changed dynamicly (which may occur in
    // gflags built-in service), statistic will be reset
    if (BYTERPC_UNLIKELY(_statistic_period != FLAGS_byterpc_collecting_period)) {
        _statistic_period = FLAGS_byterpc_collecting_period;
        _cur_statistic = {0, 0, 0, 0, 0};
        _last_statistic = {0, 0, 0, 0, 0};
    }
}
void Socket::ConnectionStatistic::AddInput(size_t count, size_t bytes) {
    CheckPeriod();
    time_t now = time(0);
    time_t cur_timestamp = now / _statistic_period;
    if (cur_timestamp != _cur_statistic.timestamp) {
        _last_statistic = _cur_statistic;
        _cur_statistic = {count, 0, bytes, 0, cur_timestamp};
    } else {
        _cur_statistic.in_messages += count;
        _cur_statistic.in_size += bytes;
    }
}

void Socket::ConnectionStatistic::AddOutput(size_t count, size_t bytes) {
    CheckPeriod();
    time_t now = time(0);
    time_t cur_timestamp = now / _statistic_period;
    if (cur_timestamp != _cur_statistic.timestamp) {
        _last_statistic = _cur_statistic;
        _cur_statistic = {0, count, 0, bytes, cur_timestamp};
    } else {
        _cur_statistic.out_messages += count;
        _cur_statistic.out_size += bytes;
    }
}

void Socket::ConnectionStatistic::GetStatistic(float* in_qps,
                                               float* out_qps,
                                               float* in_throughput,
                                               float* out_throughput) {
    CheckPeriod();
    time_t now = time(0);
    time_t target_timestamp = now / _statistic_period - 1;
    if (target_timestamp == _last_statistic.timestamp) {
        *in_qps = static_cast<float>(_last_statistic.in_messages) / _statistic_period;
        *out_qps = static_cast<float>(_last_statistic.out_messages) / _statistic_period;
        *in_throughput = static_cast<float>(_last_statistic.in_size) / _statistic_period;
        *out_throughput = static_cast<float>(_last_statistic.out_size) / _statistic_period;
    } else if (target_timestamp == _cur_statistic.timestamp) {
        *in_qps = static_cast<float>(_cur_statistic.in_messages) / _statistic_period;
        *out_qps = static_cast<float>(_cur_statistic.out_messages) / _statistic_period;
        *in_throughput = static_cast<float>(_cur_statistic.in_size) / _statistic_period;
        *out_throughput = static_cast<float>(_cur_statistic.out_size) / _statistic_period;
    } else {
        *in_qps = 0;
        *out_qps = 0;
        *in_throughput = 0;
        *out_throughput = 0;
    }
}

}  // namespace byterpc
