// Copyright (c) 2021, ByteDance Inc. All rights reserved.

#include "rpc/channel_manager.h"

#include <array>
#include <algorithm>

#include "byterpc/byterpc_flags.h"
#include "byterpc/callback.h"
#include "byterpc/exec_ctx.h"
#include "byterpc/util/compiler_specific.h"
#include "byterpc/util/logging.h"
#include "byterpc/util/timestamp.h"
#include "rpc/protobuf_channel.h"
#include "proto/builtin_service.pb.h"

namespace byterpc {

class ChannelWatcher : public TimeEventHandler {
public:
    explicit ChannelWatcher(ChannelManager* channel_manager)
        : _channel_manager(channel_manager) {}

    void HandleTimeEvent() override {
        _channel_manager->WatchChannels();
    }

private:
    ChannelManager* _channel_manager;
};

class HealthCheckTimeEvent : public TimeEventHandler {
public:
    explicit HealthCheckTimeEvent(ConnectionHealthInfo* health_info) : _health_info(health_info) {}

    void HandleTimeEvent() override {
        // the timeout iter will be erased by TimeoutManager after HandleTimeEvent
        _health_info->timeout_iter_valid = false;
        _health_info->DoHealthCheck();
    }

private:
    ConnectionHealthInfo* _health_info;
};

ConnectionHealthInfo::ConnectionHealthInfo()
    : pair_eps(),
      status(ConnectionStatus::RISKY),
      failed_rpc_queue(),
      cont_succ_times(0),
      health_checker(nullptr),
      timer_ev_reg(nullptr),
      timeout_iter(),
      timeout_iter_valid(false),
      enable_health_check(false),
      owner_ref(0) {}

ConnectionHealthInfo::ConnectionHealthInfo(const PairEndpoints& eps,
                                           EventRegistry* ev_reg,
                                           bool health_check)
    : pair_eps(eps),
      status(ConnectionStatus::RISKY),
      failed_rpc_queue(),
      cont_succ_times(0),
      health_checker(nullptr),
      timer_ev_reg(ev_reg),
      timeout_iter(),
      timeout_iter_valid(false),
      enable_health_check(health_check),
      owner_ref(0) {}

bool ConnectionHealthInfo::IsRepeatedRPCFailure(const FailedRPCInfo& fail_info) const {
    if (0 == FLAGS_byterpc_fallback_dedup_failure_window_us || failed_rpc_queue.empty()) {
        return false;
    }
    return (fail_info.error_code == failed_rpc_queue.back().error_code) &&
           (fail_info.timestamp <= failed_rpc_queue.back().timestamp +
                util::TimeStamp::UsToDuration(FLAGS_byterpc_fallback_dedup_failure_window_us));
}

void ConnectionHealthInfo::PopExpiredFailureInfo() {
    uint64_t start_timestamp =
        failed_rpc_queue.back().timestamp -
        util::TimeStamp::MsToDuration(FLAGS_byterpc_fallback_transport_switch_window_ms);
    auto iter = std::lower_bound(
        failed_rpc_queue.begin(),
        failed_rpc_queue.end(),
        start_timestamp,
        [](const FailedRPCInfo& v, const uint64_t& s) { return v.timestamp < s; });
    BYTERPC_CHECK(iter != failed_rpc_queue.end());
    int pop_num = iter - failed_rpc_queue.begin();
    while (pop_num--) {
        failed_rpc_queue.pop_front();
    }
}

bool ConnectionHealthInfo::ShouldSwitchToUnhealthy() const {
    return failed_rpc_queue.size() >= FLAGS_byterpc_fallback_risky_max_failed_count;
}

static inline bool IsValidError(int error_code) {
    static constexpr std::array<int, 7> kErrors = {
        ERPCTIMEDOUT, ETIMEDOUT, ECONNECTFAILED, ECONNREFUSED, ERDMAVERBS, ERDMACOMP, ERDMAUNAVAIL};
    return std::find(kErrors.begin(), kErrors.end(), error_code) != kErrors.end();
}

void ConnectionHealthInfo::AddRPCFailureInfo(int error_code) {
    if (!IsValidError(error_code)) {
        return;
    }
    if (ERDMAUNAVAIL == error_code) {
        SetStatus(ConnectionStatus::DAMAGED);
        return;
    }
    if (status != ConnectionStatus::RISKY) {
        // Ignore failure info if connection is already UNHEALTHY or DAMAGED
        return;
    }

    FailedRPCInfo fail_info(error_code);
    if (IsRepeatedRPCFailure(fail_info)) {
        // Ignore repeated failures
        return;
    }
    failed_rpc_queue.push_back(fail_info);
    if (!ShouldSwitchToUnhealthy()) {
        return;
    }
    PopExpiredFailureInfo();
    if (ShouldSwitchToUnhealthy()) {
        SetStatus(ConnectionStatus::UNHEALTHY);
        StartHealthCheck();
    }
}

void ConnectionHealthInfo::SetStatus(ConnectionStatus new_status) {
    // NOTE: NOT need to flush queuing RPCs while transport switching,
    // because all failed RPCs will be cancelled from ClientSocket, and
    // these failed queuing requests will never be sent.
    BYTERPC_LOG(WARNING) << "Channel to remote side=" << pair_eps.remote_side
                         << " with transport_type=" << TransportTypeToString(pair_eps.trans_type)
                         << " and local_side=" << pair_eps.local_side
                         << " is switching from " << ConnectionStatusToString(status)
                         << " to " << ConnectionStatusToString(new_status);
    Reset();
    status = new_status;
}

void ConnectionHealthInfo::StartHealthCheck() {
    if (enable_health_check && FLAGS_byterpc_fallback_health_check_interval_s > 0) {
        if (!health_checker) {
            health_checker = std::make_unique<HealthCheckTimeEvent>(this);
        }
        timeout_iter = timer_ev_reg->AddTimeConsumer(
            health_checker.get(),
            FLAGS_byterpc_fallback_health_check_interval_s * 1000UL * 1000UL);
        timeout_iter_valid = true;
    }
}

void ConnectionHealthInfo::DoHealthCheck() {
    // setup new connection for health check
    Builder builder;
    auto cntl = builder.CreateSessionController(PROTOCOL_BYTE_STD);
    auto ev_reg = ExecCtx::GetThreadEventRegistry(pair_eps.trans_type);
    auto chan = new ProtobufChannel(ev_reg, Builder::GetClientMsgHandlerManager());
    Builder::ChannelOptions options;
    options._trans_type = pair_eps.trans_type;
    chan->Init(pair_eps.remote_side, options, pair_eps.local_side);

    OwnerRef();
    proto::HealthCheckRequest request;
    proto::HealthCheckResponse* response = new proto::HealthCheckResponse();
    google::protobuf::Closure* done = ::byterpc::NewCallback<Builder::Channel*,
                                                             Controller*,
                                                             proto::HealthCheckResponse*,
                                                             ConnectionHealthInfo*>(
        &ConnectionHealthInfo::HealthCheckCallback, chan, cntl, response, this);

    proto::transport_Stub stub(chan);
    stub.check_health(cntl, &request, response, done);
}

void ConnectionHealthInfo::HealthCheckCallback(Builder::Channel* channel,
                                               Controller* cntl,
                                               proto::HealthCheckResponse* response,
                                               ConnectionHealthInfo* health_info) {
    std::unique_ptr<Builder::Channel> chan_guard(channel);
    std::unique_ptr<proto::HealthCheckResponse> resp_guard(response);
    health_info->UpdateChannelStatusUponHealthCheck(cntl->ErrorCode());
    health_info->OwnerUnref();
}

void ConnectionHealthInfo::UpdateChannelStatusUponHealthCheck(int error_code) {
    if (error_code == 0) {
        ++cont_succ_times;
    } else {
        cont_succ_times = 0;
    }

    if (cont_succ_times >= FLAGS_byterpc_fallback_health_check_continuous_succ_time) {
        SetStatus(ConnectionStatus::RISKY);
    } else {
        // start next round health check timer
        StartHealthCheck();
    }
}

void ConnectionHealthInfo::StartHealthCheckIfNot() {
    if (status == ConnectionStatus::UNHEALTHY && !health_checker) {
        StartHealthCheck();
    }
}

void ConnectionHealthInfo::Reset() {
    if (timeout_iter_valid) {
        timer_ev_reg->RemoveTimeConsumer(timeout_iter);
        timeout_iter_valid = false;
    }
    health_checker.reset();
    failed_rpc_queue.clear();
    cont_succ_times = 0;
    status = ConnectionStatus::RISKY;
}

ChannelManager::ChannelManager(MessageHandlerManager* msg_handler_mgr)
    : _msg_handler_mgr(msg_handler_mgr),
      _map(),
      _fb_map(),
      _unhealthy_map(),
      _timer_ev_reg(nullptr),
      _watcher_timeout_iter(),
      _watcher_timeout_iter_valid(false) {
    _channel_watcher = std::make_unique<ChannelWatcher>(this);
}

ChannelManager::~ChannelManager() {
    if (_timer_ev_reg && _watcher_timeout_iter_valid) {
        _timer_ev_reg->RemoveTimeConsumer(_watcher_timeout_iter);
    }
    _fb_map.clear();
    _map.clear();
    _unhealthy_map.clear();
}

std::shared_ptr<Builder::Channel> ChannelManager::GetChannel(const util::EndPoint& remote_side,
        const util::EndPoint& local_side, const Builder::ChannelOptions& options) {
    auto ev_reg = ExecCtx::GetOrNewThreadEventRegistry(options._trans_type);
    if (BYTERPC_UNLIKELY(!ev_reg)) {
        BYTERPC_LOG(ERROR) << "Fail to get ev_reg for "
                           << TransportTypeToString(options._trans_type);
        return nullptr;
    }

    if (BYTERPC_UNLIKELY(!_timer_ev_reg)) {
        // register watcher for the first time
        _timer_ev_reg = ev_reg;
        AddChannelWatcher();
    }

    PairEndpoints pair_eps(remote_side, local_side, options._trans_type);
    auto it = _map.find(pair_eps);
    if (it != _map.end()) {
        return it->second.channel;
    }

    std::shared_ptr<Builder::Channel> ch =
        std::make_shared<ProtobufChannel>(ev_reg, _msg_handler_mgr);
    ProtobufChannel* pchannel = dynamic_cast<ProtobufChannel*>(ch.get());
    if (pchannel && pchannel->Init(remote_side, options, local_side) == 0) {
        _map.emplace(pair_eps, ChannelMapEntry(ch));
        return ch;
    }

    return nullptr;
}

std::shared_ptr<Builder::Channel> ChannelManager::GetFbChannel(const util::EndPoint& remote_side,
        const util::EndPoint& local_side, const Builder::ChannelOptions& options) {
    PairEndpoints pair_eps(remote_side, local_side, options._trans_type);
    auto it = _fb_map.find(pair_eps);
    if (it != _fb_map.end()) {
        return it->second.channel;
    }
    return nullptr;
}

void ChannelManager::PutFbChannel(const util::EndPoint& remote_side,
                                  const util::EndPoint& local_side,
                                  const Builder::ChannelOptions& options,
                                  std::shared_ptr<Builder::Channel> fb_channel) {
    PairEndpoints pair_eps(remote_side, local_side, options._trans_type);
    _fb_map.emplace(pair_eps, ChannelMapEntry(fb_channel));
}

void ChannelManager::WatchChannels() {
    WatchChannelsImpl(true);
    WatchChannelsImpl(false);
    WatchUnhealthyMap();

    // Register Watcher for next round
    AddChannelWatcher();
}

void ChannelManager::WatchChannelsImpl(bool is_fb_map) {
    const uint64_t global_defer_duration =
        util::TimeStamp::MsToDuration(FLAGS_byterpc_channel_defer_close_second * 1000L);
    const uint64_t now = util::TimeStamp::Now();

    auto& map = is_fb_map ? _fb_map : _map;
    auto it = map.begin();
    while (it != map.end()) {
        auto& sc = it->second;
        size_t num_invoked = sc.channel->NumInvoked();
        if (sc.num_invoked != num_invoked) {
            // This channel was invoked recently
            sc.no_ref_ts = 0;
            sc.num_invoked = num_invoked;
            it++;
            continue;
        }

        // Nobody use this channel recently
        uint64_t defer_duration =
            sc.customized_defer_close_valid ? sc.customized_defer_close_ts : global_defer_duration;
        if (sc.no_ref_ts == 0) {
            sc.no_ref_ts = now;
        } else if (now - sc.no_ref_ts >= defer_duration) {
            if (sc.channel.use_count() == 1) {
                // release this channel if nobody reference it
                it = map.erase(it);
                continue;
            }
            // If user still references this channel, recycle the underlay connection
            // for ProtobufChannel, and do nothing for FallbackChannel.
            if (!is_fb_map) {
                ProtobufChannel* chan = static_cast<ProtobufChannel*>(sc.channel.get());
                if (chan->IsConnected()) {
                    BYTERPC_LOG(WARNING)
                        << "Recycle idle connection,"
                        << " client_side=" << chan->ClientAddress()
                        << " server_side=" << chan->ServerAddress()
                        << " transport_type=" << TransportTypeToString(chan->GetTransportType());
                    chan->ResetTransport(byterpc::ECLOSE);
                }
            }
        }
        it++;
    }
}

void ChannelManager::AddChannelWatcher() {
    if (FLAGS_byterpc_channel_map_check_interval_ms <= 0) {
        BYTERPC_LOG(WARNING) << "Lazy channel watcher is disabled";
        _watcher_timeout_iter_valid = false;
        return;
    }

    _watcher_timeout_iter = _timer_ev_reg->AddTimeConsumer(
        _channel_watcher.get(), FLAGS_byterpc_channel_map_check_interval_ms * 1000UL);
    _watcher_timeout_iter_valid = true;
}

bool ChannelManager::IsChannelHealthy(const PairEndpoints& pair_eps) const {
    auto it = _unhealthy_map.find(pair_eps);
    return it == _unhealthy_map.end() || it->second->status == ConnectionStatus::RISKY;
}

bool ChannelManager::UpdateChannelStatusOnError(const PairEndpoints& pair_eps, int error_code) {
    // ChannelWatcher should already have been started by calling GetChannel
    BYTERPC_CHECK(_timer_ev_reg);

    auto it = _unhealthy_map.find(pair_eps);
    if (it == _unhealthy_map.end()) {
        it = _unhealthy_map
                 .emplace(pair_eps, new ConnectionHealthInfo(pair_eps, _timer_ev_reg, true))
                 .first;
    }

    ConnectionHealthInfo* health_info = it->second.get();
    health_info->AddRPCFailureInfo(error_code);  // May change status to UNHEALTHY or DAMAGED
    return health_info->status == ConnectionStatus::RISKY;
}

bool ChannelManager::CheckChannelRevived(const PairEndpoints& pair_eps) {
    auto it = _unhealthy_map.find(pair_eps);
    if (it == _unhealthy_map.end() || it->second->status == ConnectionStatus::RISKY) {
        return true;
    }
    if (it->second->status == ConnectionStatus::UNHEALTHY) {
        it->second->StartHealthCheckIfNot();
    }
    return false;
}

void ChannelManager::WatchUnhealthyMap() {
    static thread_local uint64_t run_count = 0;
    ++run_count;
    const uint64_t kTriggerThreshold = FLAGS_byterpc_fallback_risky_conn_gc_interval_s
        * 1000 / FLAGS_byterpc_channel_map_check_interval_ms;
    if (run_count >= kTriggerThreshold) {
        RemoveExpiredRiskyConnections();
        run_count = 0;
    }
}

void ChannelManager::RemoveExpiredRiskyConnections() {
    const uint64_t now = util::TimeStamp::Now();
    const uint64_t switch_window_duration =
        util::TimeStamp::MsToDuration(FLAGS_byterpc_fallback_transport_switch_window_ms);
    auto it = _unhealthy_map.begin();
    while (it != _unhealthy_map.end()) {
        const std::deque<FailedRPCInfo>& failure_info = it->second->failed_rpc_queue;
        const bool info_expired =
            failure_info.empty() || (now > failure_info.back().timestamp + switch_window_duration);
        if (it->second->status == ConnectionStatus::RISKY && info_expired) {
            it = _unhealthy_map.erase(it);
        } else {
            it++;
        }
    }
}

bool ChannelManager::TEST_IsChannelDamaged(const PairEndpoints& pair_eps) {
    auto it = _unhealthy_map.find(pair_eps);
    return it != _unhealthy_map.end() && it->second->status == ConnectionStatus::DAMAGED;
}

bool ChannelManager::TEST_IsChannelUnhealthy(const PairEndpoints& pair_eps) {
    auto it = _unhealthy_map.find(pair_eps);
    return it != _unhealthy_map.end() && it->second->status == ConnectionStatus::UNHEALTHY;
}

}  // namespace byterpc
