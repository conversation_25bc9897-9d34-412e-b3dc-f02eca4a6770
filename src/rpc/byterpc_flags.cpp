// Copyright (c) 2021, ByteDance Inc. All rights reserved.
#include "byterpc/byterpc_flags.h"

#include "byterpc/log_setting.h"
#include "util/reloadable_gflag.h"

namespace byterpc {

namespace {
// validator of FLAGS_byterpc_log_min_level
bool ValidateLogMinLevel(const char* flagname, int32_t value) {
    if (SetMinLogLevel(value) != 0)
        return false;
    return true;
}
}  // namespace

DEFINE_bool(byterpc_reuse_addr, true, "Bind to ports in TIME_WAIT state");

DEFINE_bool(byterpc_reuse_port, true, "Reuse port");

DEFINE_bool(byterpc_preempt_uds_path,
            true,
            "Merely remove unix domain socket file before listen to it, noting that it does NOT "
            "resemble FLAGS_byterpc_reuse_addr");

DEFINE_bool_r(byterpc_enable_time_profiler, false, "Enable TimeProfiler");

DEFINE_bool_r(byterpc_enable_loop_metrics, false, "Enable loop metrics");

DEFINE_int32(byterpc_report_loop_metrics_interval_us, 100000, "report loop metrics interval");

DEFINE_int32(byterpc_slow_loop_us, 300, "slow loop interval");

DEFINE_bool_r(byterpc_enable_poller_metrics, false, "Enable poller metrics");

DEFINE_int32(byterpc_rpc_timeout_ms, 4000, "default rpc timeout milli-seconds");

DEFINE_int32(byterpc_connect_timeout_ms, 2000, "default connect timeout milli-seconds");

DEFINE_int32_r(byterpc_channel_map_check_interval_ms,
               60000,
               "channel map check interval milli-seconds");

DEFINE_bool(byterpc_enable_iobuf_stats_monitor, false, "enable iobuf memory monitor or not");
DEFINE_int32_r(byterpc_iobuf_stats_mon_interval_ms,
               1000,
               "iobuf memory monitor interval in milli-seconds");

DEFINE_uint32_r(byterpc_channel_defer_close_second, 300, "defer close of channel in seconds");

DEFINE_int64(byterpc_socket_max_unwritten_bytes,
             64 * 1024 * 1024,
             "Max unwritten bytes in each socket, if the limit is reached,"
             " rpc fails with EOVERCROWDED");

DEFINE_uint64(byterpc_max_body_size,
              64 * 1024 * 1024,
              "Maximum size of a single message body in all protocols");

DEFINE_bool(byterpc_immutable_flags, false, "Disable modification of gflags by byterpc");
DEFINE_int32(byterpc_timeout_event_hashmap_bucket_num,
             128,
             "timeout hashmap reserve bucket num at beginning,"
             " buckets can be expanded when not enough to use");

DEFINE_bool(byterpc_enable_block_pool, false, "Enable block pool to optimize bemalloc");

DEFINE_bool(
    byterpc_enable_iobuf_multi_thread,
    true,
    "Enable IOBuf to support multi-thread, only works when -DBYTERPC_ENABLE_IOBUF_MTHREADS=true.");

DEFINE_bool(
    byterpc_enable_unregister_memory_dynamicly,
    false,
    "Enable ExtmemUnregister() at runtime, which also allows to register and unregister the same "
    "memory region for many times. If disabled, users should not call ExtmemUnregister() until the "
    "process is about to exit. Note that enable this flag may lead to performance degradation.");

// memory pool
// TODO(dpw): change name to `byterpc_memory_pool_enable_numa_aware`
DEFINE_bool(
    byterpc_enable_numa_aware,
    false,
    "enable memory pool numa-aware; only works when byterpc is built with -DBYTERPC_WITH_NUMA=ON");

// TODO(dpw): change name to `byterpc_memory_pool_use_hugepage`
DEFINE_bool(byterpc_byte_express_memory_pool_use_hugepage, true, "use hugepage for memory pool");
DEFINE_uint64(byterpc_memory_pool_region_size,
              1 * 1024 * 1024 * 1024,
              "memory pool allocate a region each time when pool runs out of mem, region is "
              "allocated by `mmap` with region size");
// TODO(dpw): change name to `byterpc_memory_pool_max_region_num`
DEFINE_uint32_r(byterpc_byte_express_memory_pool_max_regions,
                20,
                "max number of regions for memory pool to allocate");
// TODO(dpw): change name to `byterpc_memory_pool_pre_allocate_region_threshold`, and change from MB
// to B
DEFINE_uint64_r(byterpc_min_resident_capacity_in_MB_to_expand_memory,
                256,
                "if remaining memory (or each numa's if numa-aware) is smaller than this, "
                "pre-allocate a new region in background thread; set to 0 to disable this feat");
// - only numa-aware enabled
// TODO(dpw): change name to `byterpc_memory_pool_numa0_init_region_num`
DEFINE_uint32(
    byterpc_numa0_memory_pool_init_regions,
    0,
    "[Deprecated (use byterpc_memory_pool_numa_init_region_num instead)] region nums of memory "
    "pool on numa node 0 created when init; only works when numa-aware is enabled.");
// TODO(dpw): change name to `byterpc_memory_pool_numa1_init_region_num`
DEFINE_uint32(
    byterpc_numa1_memory_pool_init_regions,
    0,
    "[Deprecated (use byterpc_memory_pool_numa_init_region_num instead)] region nums of memory "
    "pool on numa node 1 created when init; only works when numa-aware is enabled.");
DEFINE_string(
    byterpc_memory_pool_numa_init_region_num,
    "0,0",
    "list of inited region nums on each numa node, split with comma. Only works when numa-aware is "
    "enabled. For compatibility, if all numbers are 0, byterpc_numa0(1)_memory_pool_init_regions "
    "will be used. e.g. --byterpc_memory_pool_numa_init_region_num=1,1,1,1 (for machine with 4 "
    "numa nodes). ");
DEFINE_string(
    byterpc_memory_pool_numa_max_hugepage_region_num,
    "5,5",
    "list of max hugepage region num on each numa node, split with comma. Only works when "
    "numa-aware is enabled. e.g. --byterpc_memory_pool_numa_max_hugepage_region_num=5,5,5,5 (for "
    "machine with 4 numa nodes). ");
// - only numa-aware disabled
// TODO(dpw): change name to `byterpc_memory_pool_init_region_num`
DEFINE_uint32(byterpc_byte_express_memory_pool_init_regions,
              1,
              "region nums of memory pool created in init; only works when numa-aware is disabled");
// TODO(dpw): change name to `byterpc_memory_pool_max_hugepage_region_num`
DEFINE_uint32(byterpc_byte_express_memory_pool_max_hugepage_regions,
              10,
              "max hugepage regions for memory pool; only works when numa-aware is disabled");
// - only bemalloc
// TODO(dpw): change name to
// `byterpc_memory_pool_bemalloc_garbage_collection_in_conn_mgr_interval_ms`
DEFINE_uint32(
    byterpc_byte_express_memory_pool_garbage_collection_in_conn_mgr_interval_ms,
    60000,
    "interval to recycle deferred free span in bemalloc; set to 0 to disable this feature");
// TODO(dpw): change name to `byterpc_memory_pool_bemalloc_enable_monitor_task`
DEFINE_bool(byterpc_init_bemalloc_monitor_task,
            true,
            "Enable init slow bemalloc monitor task, watchershould be guranteed to exist");
// TODO(dpw): change name to `byterpc_memory_pool_bemalloc_enable_api_level_mem_statistics`
DEFINE_bool(byterpc_enable_api_level_mem_statistics,
            false,
            "decide whether to enable the BE memory api level size statistics");

// watcher thread
// - slow polling detect task
DEFINE_bool(byterpc_init_slow_polling_detect_task,
            true,
            "Enable init slow polling detect task, watcher thread"
            "should be guranteed to exist");
DEFINE_bool_r(byterpc_enable_dump_stacktrace, false, "Enable dump stack trace for polling thread");
DEFINE_uint32_r(byterpc_slow_polling_oneshot_times_limit,
                3,
                "After dumping for limited times in process level, then disable it,"
                "0 by default, means no limitation");
DEFINE_uint32_r(byterpc_slow_polling_stack_skip_depth,
                3,
                "skip several levels from top of the printed stack");

DEFINE_uint32_r(byterpc_slow_polling_stack_max_print_depth,
                5,
                "max depth of the printed stack, which should be <= 64, otherwise automatically "
                "set to 64 if exceeds");

// log
DEFINE_int32(byterpc_log_buf_level, 0, "Obsolete. Of no use.");

DEFINE_int32(byterpc_log_min_level,
             0,
             "Any log at or above this level will be displayed. Anything below this level will be "
             "silently ignored. -1=DEBUG 0=INFO 1=WARNING 2=ERROR 3=FATAL");
DEFINE_validator(byterpc_log_min_level, &ValidateLogMinLevel);

// connection built-in service
DEFINE_bool_r(byterpc_enable_collecting_connection_info,
              false,
              "enable collecting connection statistic in connenction built-in service");

DEFINE_uint32_r(byterpc_collecting_period,
                5,
                "(should be >= 1) period of every connection statistic collection, "
                "changing period will clear current statistic");

// metric
DEFINE_bool(byterpc_metric_enable_log, false, "enable metric log");

DEFINE_int32(byterpc_metric_window_size_s,
             10,
             "metric window size in second for guage and histogram");

DEFINE_int32(byterpc_metric_report_interval_s, 10, "metric report interval in second");

DEFINE_string(byterpc_metric_log_file, "./metrics.LOG", "metric log file");

DEFINE_int32(byterpc_metric_log_max_file_size_mb, 50, "metric log max file size in MB");

DEFINE_int32(byterpc_metric_log_max_file_num, 200, "metric log max file num");

DEFINE_int32(byterpc_metric_log_level,
             3,
             "metric log level, 0(trace), 1(debug), 2(info), 3(warn), 4(err), 5(critical), 6(off)");

// fallback channel
DEFINE_uint64_r(byterpc_fallback_health_check_interval_s,
                60,
                "seconds between consecutive health-checking of unaccessible"
                " prefer channel inside FallbackChannel");

DEFINE_int32_r(byterpc_fallback_transport_switch_window_ms,
               30000,
               "prefer channel fallback switch window in milliseconds");

DEFINE_uint64_r(
    byterpc_fallback_dedup_failure_window_us,
    100,
    "take multiple failed rpcs with same error code inside dedup window as one failure");

DEFINE_uint32_r(byterpc_fallback_risky_max_failed_count,
                120,
                "switch to backup channel if failed count exceeds this value");

DEFINE_int32_r(byterpc_fallback_health_check_continuous_succ_time,
               3,
               "continuous success health check times before switching back to prefer channel");

DEFINE_uint64_r(byterpc_fallback_risky_conn_gc_interval_s,
                86400,
                "seconds between two round risky conneciton gc task");

// byte express
DEFINE_uint32_r(byterpc_byte_express_accept_schedule_policy,
                1,
                "1-RoundRobin 2-Random 3-LeastUtil");

DEFINE_uint32_r(byterpc_byte_express_max_wc_at_once,
                4096,
                "max number of wc processed in each loop");

DEFINE_int32(byterpc_byte_express_listen_backlog, 1024, "Backlog for rdma listen");

DEFINE_uint32(byterpc_byte_express_dispatcher_num,
              8,
              "number of dispatcher thread used to setup RDMA connections");

DEFINE_bool(byterpc_byte_express_use_res_allocator,
            true,
            "Use custom resource allocator to allocate RDMA resources");

DEFINE_uint32(byterpc_byte_express_flow_control_timeout_ms,
              20000,
              "flow control timeout in ms to detect potential deadlock");

DEFINE_uint32_r(byterpc_byte_express_slow_connect_us,
                0UL,
                "Print slow rdma connect log if connect delay exceeds this threshold");
DEFINE_uint32(byterpc_byte_express_max_inflight_connectors,
              15,
              "max number of inflight connect requests per thread");
DEFINE_uint32(byterpc_byte_express_keepalive_interval_ms,
              3600 * 1000UL,
              "keepalive interval in ms; used at both client and server side");
DEFINE_uint32(byterpc_byte_express_qp_max_send_wr, 128, "rdma queue pair max send wr");
DEFINE_uint32(byterpc_byte_express_qp_max_recv_wr, 128, "rdma queue pair max receive wr");
DEFINE_uint32(byterpc_byte_express_recv_buffer_size, 8192, "rdma receive queue buffer size");
DEFINE_uint32(byterpc_byte_express_qp_retry_cnt, 2, "rdma qp local retry count");
DEFINE_uint32(byterpc_byte_express_qp_timeout, 8, "rdma qp local ack timeout");
DEFINE_uint32(byterpc_byte_express_qp_mtu,
              255,
              "rdma qp active mtu:"
              "0: non-negotiation; 1: 256; 2: 512; 3: 1k; 4: 2k; 5: 4k; 255: follow device");
DEFINE_bool(byterpc_byte_express_use_infiniband, false, "interpret IPv6 address as IB address");

// tarzan
DEFINE_int32(byterpc_tarzan_worker_num, 1, "tarzan worker thread num");

DEFINE_int32_r(byterpc_tarzan_max_events_at_once, 64, "max tarzan events processed in each loop");

DEFINE_uint32_r(byterpc_tarzan_slow_connect_us,
                0UL,
                "Print slow tarzan connect log if connect delay exceeds this threshold");

// kernel tcp
DEFINE_bool_r(
    byterpc_prevent_tcp_self_connect,
    true,
    "Prevent tcp self-connection by binding client to kernel-assigned port before connection.");

DEFINE_uint32_r(byterpc_tcp_slow_connect_us,
                0UL,
                "Print slow tcp connect log if connect delay exceeds this threshold");

// Default value of SNDBUF is 2500000 on most machines.
DEFINE_int32(byterpc_socket_send_buffer_size,
             -1,
             "Set send buffer size of sockets if this value is positive");

DEFINE_int32(byterpc_socket_recv_buffer_size,
             -1,
             "Set the recv buffer size of socket if this value is positive");

DEFINE_int32(byterpc_socket_tcp_user_timeout_ms, 0, "tcp user timeout milli-seconds");

DEFINE_int32_r(byterpc_tcp_max_events_at_once, 32, "max tcp events processed in each loop");

// http
DEFINE_bool(byterpc_pb_enum_as_number,
            false,
            "Represents the enum form when converting pb to json, we set false by default to use "
            "string type (Modification NOT recommended). As for json to pb, it will first try "
            "number before string, both form is ok");

#ifndef NDEBUG
DEFINE_bool_r(byterpc_disable_rdma_traffic, false, "disable rdma traffic in debug mode");
DEFINE_bool_r(byterpc_disable_tarzan_traffic, false, "disable tarzan traffic in debug mode");
#endif

DEFINE_bool_r(byterpc_enable_remote_task_metrics, false, "enable process remote task metrics");
DEFINE_uint64(byterpc_process_remote_task_slow_us, 100, "process remote task slow time threshold");
DEFINE_uint64(byterpc_report_remote_task_metrics_interval_us,
              100000,
              "report remote task metrics interval time");
DEFINE_uint64_r(byterpc_inner_task_step, 0, "max task num of each round, default 0 means no limit");

// NAPI
DEFINE_uint32(byterpc_napi_event_to_polling_estimate_valid_cpu_utilization_interval_us,
              1000000U,
              "interval of valid cpu utilization estimation when in the event-driven mode of NAPI");
DEFINE_uint32(byterpc_napi_event_to_polling_valid_cpu_utilization_threshold,
              50U,  // 50%
              "Should be in range [0, 100]. When valid cpu utilization is over the threshold, "
              "switch to polling mode.");
DEFINE_uint32(byterpc_napi_polling_to_event_estimate_valid_cpu_utilization_interval_us,
              1000000U,
              "interval of cpu utilization estimation when in the polling mode of NAPI");
DEFINE_uint32(byterpc_napi_polling_to_event_valid_cpu_utilization_threshold,
              20U,  // 20%
              "Should be in range [0, 100]. When valid cpu utilization is below the threshold, "
              "switch to event-driven mode.");

}  // namespace byterpc
