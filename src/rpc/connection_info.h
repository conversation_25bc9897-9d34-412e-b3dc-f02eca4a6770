// Copyright (c) 2021, ByteDance Inc. All rights reserved.
#pragma once

#include <memory>
#include <mutex>  // NOLINT(build/c++11)
#include <vector>

#include "byterpc/exec_ctx.h"
#include "byterpc/rpc.h"
#include "byterpc/server.h"
#include "byterpc/util/container_wrapper.h"

namespace byterpc {

class ServerSocket;
class ClientSocket;
class GlobalThreadsInfo;
class ThreadConnectionInfo {
public:
    ThreadConnectionInfo();
    ~ThreadConnectionInfo();

    static void Init(loop_type_t type);
    // Singleton: return thread_local thread_connection_info
    static std::shared_ptr<ThreadConnectionInfo> GetInstancePtr();

    size_t GetServerSocketSize();
    size_t GetClientSocketSize();
    int GetThreadId() const;
    pthread_t GetNonLwpThreadId() const;
    int32_t GetTarzanWorkerId() const;
    std::vector<TransportType> GetTransportType() const;
    void AddTransportType(TransportType type);
    loop_type_t GetLoopType() const;

    void TraverseServerSocketSet(std::function<void(ServerSocket*)> func);
    void TraverseClientSocketSet(std::function<void(ClientSocket*)> func);
    void TraverseServerSet(std::function<void(Server*)> func);
    void AddServerSocket(ServerSocket* server_ptr);
    void AddClientSocket(ClientSocket* client_ptr);
    void AddServer(Server* server);
    void RemoveServerSocket(ServerSocket* server_ptr);
    void RemoveClientSocket(ClientSocket* client_ptr);
    void RemoveServer(Server* server);

private:
    int _thread_id;  // LWP thread id
    pthread_t _non_lwp_thread_id;
    int32_t* _tarzan_worker_id_ptr;
    std::weak_ptr<GlobalThreadsInfo> _global_info_ptr;

    util::HashSet<ServerSocket*> _server_socket_set;
    std::mutex _server_socket_set_mutex;
    util::HashSet<ClientSocket*> _client_socket_set;
    std::mutex _client_socket_set_mutex;
    util::HashSet<Server*> _server_set;
    std::mutex _server_set_mutex;

    // Record connection thread basic info, once set it can't be rolled back
    loop_type_t _loop_type;
    std::vector<TransportType> _trans_types;
};

struct ThreadInfoDisplay {
    int pid;
    std::vector<TransportType> transport_types;
    loop_type_t loop_type;
    size_t server_socket_num;
    size_t client_socket_num;
    int32_t tarzan_worker_id;
};

class GlobalThreadsInfo {
public:
    // Singleton: return global g_threads_info
    static std::shared_ptr<GlobalThreadsInfo> GetInstancePtr();
    // add current thread to global GlobalThreadsInfo's map
    void AddThreadInfo(int pid, const std::shared_ptr<ThreadConnectionInfo>& thread_ptr);
    // remove current thread from global GlobalThreadsInfo's map
    void RemoveThreadInfo(int pid);
    size_t GetThreadsMapSize();
    // get ThreadConnectionInfo by thread_id
    std::shared_ptr<ThreadConnectionInfo> GetThreadInfo(int pid);
    // called by built-in service thread for all threads' info
    void TraverseThreadsMap(std::function<void(ThreadConnectionInfo*)> func);

private:
    std::mutex _mutex;
    util::HashMap<int, std::weak_ptr<ThreadConnectionInfo>> _threads_map;
};

}  // namespace byterpc
