/**
 * ARIES - A Reliable and Impressive EB-level Storage.
 * Copyright (c) 2021 Baidu.com, Inc. All Rights Reserved.
**/

#include "baidu/inf/aries-api/proxy/shard_reader.h"

#include <baidu/feed/mlarch/babylon/lite/string.h>
#include <baidu/inf/aries-api/proxy/channel_cache.h>
#include <base/string_printf.h>

namespace aries {
DECLARE_bool(fast_range_get_use_privilege_dataport);
DECLARE_int32(max_network_flow_limit);

using ::baidu::feed::mlarch::babylon::resize_uninitialized;

int32_t BaseShardReader::process() {
    const uint64_t& log_id = _log_id;
    prepare_vlet_queue();
    int64_t remain_time_ms = _option.call_timeout_ms;
    int64_t start_time = base::cpuwide_time_ms();
    int32_t concurrency_limit = -1;
    if (_blob->blob_len_hint != 0) {
        concurrency_limit = std::max(1, 
            FLAGS_max_network_flow_limit / (int32_t)(1 + _blob->blob_len_hint / _volume->eco.param.k));
    }
    std::shared_ptr<aries::common::ConcurrencyLimitSyncPoint> sync_point(
                new aries::common::ConcurrencyLimitSyncPoint(_volume->eco.param.n, _volume->eco.param.n, concurrency_limit));
    RangeGetShard range;
    // 从_vlet_get_queue中取出vlet并发送get请求
    while (!_vlet_get_queue.empty()) {
        int req_num = calc_request_num();
        for (auto i = 0; i < req_num; i++) {
            if (_vlet_get_queue.empty()) {
                return AIE_FAIL;
            }
            int target = _vlet_get_queue.front();
            _vlet_get_queue.pop_front();

            std::shared_ptr<ShardReadContext> context(new ShardReadContext);
            const Shard& shard = _volume->shards[target];
            context->log_id = log_id;
            context->timeout_ms = remain_time_ms;
            context->start_time_us = base::gettimeofday_us();
            context->shard_index = shard.shard_index;
            context->shard_addr = shard.addr;
            context->sync_point = sync_point;
            context->request_error_info = _blob->request_error_info_for_copy;
            context->request_error_info_mutex = _blob->request_error_info_mutex;
            if (find_range_get_shard(shard.shard_index, &range)) {
                context->offset = range.offset;
                context->len = range.len;
                context->fast_range_get = range.fast_range_get;
            }
            get_shard(context);
            _sent_req_num++;
        }
        int64_t wait_time_ms = std::max((int64_t)_option.get_shard_min_timeout_ms,
            _get_vars->get_shard_most_timeout_ms.load());
        wait_time_ms = std::min(wait_time_ms, remain_time_ms);
        if ((size_t)_sent_req_num >= _volume->shards.size()) {
            wait_time_ms = remain_time_ms;
        }
        ARIES_RPC_VLOG(INFO) << name() << " get shards bid: " << bid2str(_blob->blob_id)
            << " k/m:" << _volume->eco.param.k << "/" << req_num
            << " sent_req_num:" << _sent_req_num << " max_req_num:" << _max_req_num
            << " wait_ms:" << wait_time_ms << " remain_ms:" << remain_time_ms;
        // 发送完一个批次的请求，开始等待请求结果
        int ret = wait_and_check(wait_time_ms);
        if (ret != AIE_AGAIN) {
            ARIES_RPC_VLOG(TRACE) << name() << " wait_and_check final result:" << ret;
            return ret;
        }
        remain_time_ms = _option.call_timeout_ms - (base::cpuwide_time_ms() - start_time);
        if (remain_time_ms < 1) {
            ARIES_RPC_LOG(NOTICE) << name() << " wait failed due to collect shard timeout, remain_time_ms:"
                << remain_time_ms;
            return AIE_TIMEOUT;
        }
    }
    ARIES_RPC_LOG(NOTICE) << name() << " wait failed due to all shards are tried out, remain_time_ms:"
        << remain_time_ms;
    return AIE_FAIL;
}

void BaseShardReader::get_shard(std::shared_ptr<ShardReadContext> ctx) {
    const auto& log_id = _log_id;
    aries::pb::ShardGetRequest req;
    req.set_token(_option.token);
    req.set_volume_id(bid2vid(_blob->blob_id));
    req.set_vbid(bid2vbid(_blob->blob_id));
    req.set_need_data(_blob->need_data);
    req.set_need_meta(_blob->need_meta);
    req.set_shard_index(ctx->shard_index);
    req.set_timeout_ms(ctx->timeout_ms);
    req.set_priority(_option.priority);
    req.set_network_qos(_option.network_qos);

    // support range get
    req.set_offset(ctx->offset);
    req.set_len(ctx->len);
    req.set_fast_range_get(ctx->fast_range_get);

    ShardGetRpcContext* rpc_ctx = new ShardGetRpcContext;
    rpc_ctx->shard = ctx;
    rpc_ctx->cntl.set_log_id(log_id);
    rpc_ctx->cntl.set_timeout_ms(ctx->timeout_ms);
    rpc_ctx->result_queue = _result_queue;
    rpc_ctx->get_vars = _get_vars;

    std::shared_ptr<baidu::rpc::Channel> channel;
    auto data_service_addr = common::get_data_service_addr_by_qos(ctx->shard_addr, _option.network_qos);
    if (FLAGS_fast_range_get_use_privilege_dataport && ctx->fast_range_get) {
        data_service_addr = common::get_data_service_addr_by_qos(ctx->shard_addr, ::aries::PRIVILEGE);
    }

    int ret = get_channel(data_service_addr, &channel);
    if (!(ret == AIE_OK && channel != nullptr)) {
        ARIES_RPC_LOG(WARNING) << "get shard failed due to init channel failed, ret:" << ret
            << " shard:" << ctx->shard_index << " network_qos:" << _option.network_qos
            << " node_addr:" << data_service_addr << " offset:" << ctx->offset << " len:" << ctx->len;;
        ctx->code = AIE_FAIL;
        _get_vars->get_sync_event->add_count();
        rpc_ctx->start_time_us = base::gettimeofday_us();
        get_shard_done(rpc_ctx);
        return;
    }

    ARIES_RPC_VLOG(TRACE) << "try get shard, shard:" << ctx->shard_index
        << " network_qos:" << _option.network_qos << " node_addr:" << data_service_addr
        << " offset:" << ctx->offset << " len:" << ctx->len;
    _get_vars->get_sync_event->add_count();
    if (ctx->sync_point) {
        ctx->sync_point->add_concurrency();
    }
    rpc_ctx->start_time_us = base::gettimeofday_us();
    aries::pb::DataNodeDataService_Stub stub(channel.get());
    stub.get(&rpc_ctx->cntl, &req, &rpc_ctx->res,
        ::google::protobuf::NewCallback(this, &BaseShardReader::get_shard_done, rpc_ctx));
}

void BaseShardReader::get_shard_done(ShardGetRpcContext* rpc_ctx) {
    std::unique_ptr<ShardGetRpcContext> guard(rpc_ctx);
    auto ctx = rpc_ctx->shard;
    auto& res = rpc_ctx->res;
    if (ctx->sync_point) {
        ctx->sync_point->signal(true);
    }
    baidu::rpc::Controller& cntl = rpc_ctx->cntl;
    int64_t cost_us = std::max(0L, base::gettimeofday_us() - rpc_ctx->start_time_us);
    if (cost_us > 1000) {
        g_stage_lantency_get_shard->put(cost_us);
    }
    uint64_t log_id = cntl.log_id();
    if (cntl.Failed()) {
        if (ctx->request_error_info_mutex) {
            ctx->request_error_info_mutex->lock();
            if (ctx->request_error_info) {
                auto shard_error_info = ctx->request_error_info->add_shard_error_info();
                shard_error_info->set_remote_addr(common::endpoint2str(cntl.remote_side()));
                shard_error_info->set_local_addr(common::endpoint2str(cntl.local_side()));
                shard_error_info->set_error_code(cntl.ErrorCode());
                shard_error_info->set_timestamp(base::gettimeofday_s());
            }
            ctx->request_error_info_mutex->unlock();
        }
        ARIES_RPC_LOG(WARNING) << "get shard failed, remote:" << cntl.remote_side() << " rpc_error:"
            << cntl.ErrorCode() << " (" << cntl.ErrorText() << ") shard:" << ctx->shard_index
            << " latency_us:" << cntl.latency_us();
        rpc_ctx->get_vars->client_get_shard_failed_adder << 1;
        ctx->code = AIE_FAIL;
    } else if (res.status().code() == AIE_OK) {
        ARIES_RPC_DEBUG_LOG(DEBUG) << "get shard success, remote:" << cntl.remote_side() << " rpc_status:"
            << common::pb2json(res.status()) << " shard:" << ctx->shard_index << " latency_us:" << cost_us
            << " server_process_us:" << cntl.server_process_time_us();
        rpc_ctx->get_vars->client_get_shard_latency << cost_us;
        ctx->code = AIE_OK;
        ctx->meta.reset(res.release_shard_meta());
        ctx->res_attachment.swap(cntl.response_attachment());
        if (res.has_vlet_version()) {
            ctx->vlet_version = res.vlet_version();
            ctx->membership.reset(res.release_membership());
        }
    } else {
        if (ctx->request_error_info_mutex) {
            ctx->request_error_info_mutex->lock();
            if (ctx->request_error_info) {
                auto shard_error_info = ctx->request_error_info->add_shard_error_info();
                shard_error_info->set_remote_addr(common::endpoint2str(cntl.remote_side()));
                shard_error_info->set_local_addr(common::endpoint2str(cntl.local_side()));
                shard_error_info->set_error_code(cntl.ErrorCode());
                shard_error_info->set_timestamp(base::gettimeofday_s());
            }
            ctx->request_error_info_mutex->unlock();
        }
        ARIES_RPC_LOG(WARNING) << "get shard failed, remote:" << cntl.remote_side() << " rpc_status:"
            << common::pb2json(res.status()) << " shard:" << ctx->shard_index << " latency_us:" << cost_us;
        if (res.status().code() == AIE_TIMEOUT) {
            rpc_ctx->get_vars->client_get_shard_timeout_adder << 1;
        } else {
            rpc_ctx->get_vars->client_get_shard_failed_adder << 1;
        }
        ctx->code = res.status().code();
        if (res.has_vlet_version()) {
            ctx->vlet_version = res.vlet_version();
            ctx->membership.reset(res.release_membership());
        }
    }
    rpc_ctx->result_queue->put(ctx);
    rpc_ctx->get_vars->get_sync_event->signal();
}

bool BaseShardReader::process_meta(const aries::pb::ShardMeta& shard_meta) {
    // 由于历史repair bug，meta部分可能有3种错误
    // 1.keymeta没有修复，是空的
    // 2.为了修复1，加上了keymeta，但漏了key_meta_crc没有修复，是0
    // 3.compresstype没有修复，是0
    // got_meta == got_keymeta && got_compresstype;
    const uint64_t& log_id = _log_id;
    if (!_blob->got_meta ||
        (shard_meta.has_key() && _blob->key.empty()) ||
        (shard_meta.has_compress_type() && shard_meta.compress_type() != 0 && _blob->compress_type == 0)) {

        _blob->shard_size = shard_meta.shard_len();
        _blob->blob_len = shard_meta.blob_len();
        _blob->blob_crc = shard_meta.blob_crc();
        _blob->create_time_us = shard_meta.create_time();

        if (_blob->need_meta && (!_blob->got_keymeta ||
            (shard_meta.has_key() && _blob->key.empty()))) {
            uint32_t key_meta_crc = 0;
            if (shard_meta.has_key()) {
                const std::string& key = shard_meta.key();
                _blob->key = key;
                key_meta_crc = base::crc32c::Extend(key_meta_crc, key.data(), key.size());
            }
            if (shard_meta.has_user_meta()) {
                const std::string& meta = shard_meta.user_meta();
                _blob->meta = meta;
                key_meta_crc = base::crc32c::Extend(key_meta_crc, meta.data(), meta.size());
            }
            if (shard_meta.has_key_meta_crc() && key_meta_crc != shard_meta.key_meta_crc()) {
                if (shard_meta.key_meta_crc() == 0) {
                    //忽略，认为这个0值是我们历史没修复key_meta_crc造成的
                } else {
                    ARIES_RPC_LOG(WARNING) << "check response meta crc error, "
                        << "expected:" << shard_meta.key_meta_crc() << " actual:" << key_meta_crc;
                    return false;
                }
            }
            _blob->meta_crc = key_meta_crc;
            if (!_blob->check_compress_type_info || !_blob->key.empty() || !_blob->meta.empty()) {
                _blob->got_keymeta = true;
            }
        }

        if (!_blob->got_compresstype ||
            (shard_meta.has_compress_type() && shard_meta.compress_type() != 0 && _blob->compress_type == 0)) {
            _blob->compress_type = shard_meta.compress_type() & 0x0000FFFF;
            _blob->compress_ratio = shard_meta.compress_ratio();
            _blob->compress_level = (shard_meta.compress_type() & 0xFFFF0000) >> 16;
            _blob->origin_blob_len = shard_meta.origin_blob_len();
            _blob->origin_blob_crc = shard_meta.origin_blob_crc();
            // 老集群有compress type缺失的shard，需要把check_compress_type_info这个开关打开
            if (!_blob->check_compress_type_info || _blob->got_keymeta || _blob->compress_type != 0) {
                _blob->got_compresstype = true;
            }
        }
        if (!_blob->got_meta) {
            _blob->got_meta = _blob->got_keymeta && _blob->got_compresstype;
        }
    }
    return true;
}

void BaseShardReader::try_allocate_memory(uint64_t api_data_size) {
    if (_blob->data == NULL) {
        if (_blob->api_data == NULL) {
            _blob->data = new char[api_data_size];
        } else {
            _blob->api_data->clear();
            resize_uninitialized(*_blob->api_data, api_data_size);
            _blob->data = &(*(_blob->api_data))[0];
        }
    }
}

bool BaseShardReader::find_range_get_shard(int shard_index, RangeGetShard* range) {
    return false;
}

int BaseShardReader::calc_request_num() {
    const aries::pb::SpaceInfo& space = _volume->space_info;
    if (_blob->need_data) {
        if (_volume->eco.param.k <= _success_num && (_blob->need_meta && !_blob->got_meta)) {
            // has got enough data, but not meta
            return std::min(1, _max_req_num - _sent_req_num);
        }
        int request_num = _volume->eco.param.k - _success_num + space.backup_get_shard_num();
        return std::min(request_num, _max_req_num - _sent_req_num);
    } else {
        return std::min((int)(1 + space.backup_get_meta_num()), _max_req_num - _sent_req_num);
    }
}

void BaseShardReader::prepare_vlet_queue() {
    int vlet_num = _volume->shards.size();
    int alive_vlet_num = vlet_num - _volume->dead_vlet_num;
    int k = _volume->eco.param.k;

    // for multi az and unbalanced node get optimiaztion
    std::set<int> self_az_node_normal_shards;
    std::set<int> self_az_node_unbalanced_shards;
    // need random shuffle for other az
    std::vector<int> other_az_node_normal_shards;
    std::set<int> other_az_node_unbalanced_shards;
    auto self_az_shards_size = [&]() {
        return self_az_node_normal_shards.size() + self_az_node_unbalanced_shards.size(); };
    auto other_az_shards_size = [&]() {
        return other_az_node_normal_shards.size() + other_az_node_unbalanced_shards.size(); };

    for (int i = 0; i < alive_vlet_num; ++i) {
        if (_volume->shards[i].az_name == _blob->az_name) {
            // self az
            if (_volume->shards[i].node_state == NODE_STATE_UNBALANCED) {
                self_az_node_unbalanced_shards.insert(i);
            } else {
                self_az_node_normal_shards.insert(i);
            }
        } else {
            // other az
            if (_volume->shards[i].node_state == NODE_STATE_UNBALANCED) {
                other_az_node_unbalanced_shards.insert(i);
            } else {
                other_az_node_normal_shards.push_back(i);
            }
        }
    }

    auto append_set = [&](std::set<int>& shards_set) {
        for (const auto& value : shards_set) {
            _vlet_get_queue.push_back(value);
        }
        return;
    };
    auto append_vector = [&](std::vector<int>& shards_vector) {
        std::random_shuffle(shards_vector.begin(), shards_vector.end());
        for (const auto& value : shards_vector) {
            _vlet_get_queue.push_back(value);
        }
        return;
    };
    if (self_az_shards_size() != 0 && other_az_shards_size() != 0) {
        assert(self_az_shards_size() + other_az_shards_size() == static_cast<size_t>(alive_vlet_num));
        // request shard in self az and normal first
        // selfaz+normal->otheraz+normal->selfaz+unbalanced->otheraz+unbalanced
        append_set(self_az_node_normal_shards);
        append_vector(other_az_node_normal_shards);
        append_set(self_az_node_unbalanced_shards);
        append_set(other_az_node_unbalanced_shards);
    } else {
        // for single az, request original shards first
        for (int i = 0; i < alive_vlet_num; ++i) {
            if (_volume->shards[i].node_state != NODE_STATE_UNBALANCED) {
                if (_volume->shards[i].shard_index < k) {
                    _vlet_get_queue.push_front(i);
                } else {
                    _vlet_get_queue.push_back(i);
                }
            }
        }
        // if unbalanced node existed, request those last
        append_set(self_az_node_unbalanced_shards);
        append_set(other_az_node_unbalanced_shards);
    }
    _max_req_num = _vlet_get_queue.size();
}

bool BaseShardReader::process_data(const aries::pb::ShardMeta& shard_meta, ShardReadContext* ctx) {
    return true;
}

void BaseShardReader::merge(ShardReadContext* ctx) {
    // 对返回的ShardReadContext进行处理，检查返回值
    const uint64_t& log_id = _log_id;
    ++_finish_num;
    int64_t cost = std::max(0L, base::gettimeofday_us() - ctx->start_time_us);
    _acc_shard_cost += cost;
    _max_shard_cost = std::max(_max_shard_cost, cost);
    if (ctx->code != AIE_OK) {
        if (ctx->code == AIE_REMOVED ||
            ctx->code == AIE_MARK_REMOVED) {
            _is_removed = true;
        } else if (ctx->code == AIE_BLOB_NOT_EXIST) {
            ++_not_found_num;
        } else if (ctx->code == AIE_BUSY) {
            ++_busy_num;
        }
        ++_failed_num;
        return;
    }

    if (!_blob->need_meta && !_blob->need_data) {
        ctx->shard_success = true;
        ++_success_num;
        return;
    }

    if ARIES_UNLIKELY(ctx->meta == nullptr) {
        ARIES_RPC_LOG(WARNING) << "get shard response has no meta, shard:" << ctx->shard_index;
        ++_failed_num;
        return;
    }
    auto& shard_meta = *ctx->meta;

    if (_blob->need_data && ctx->fast_range_get) {
        shard_meta.set_shard_len(_blob->shard_size);
    }

    bool data_ok = true;
    bool meta_ok = true;
    // 先来处理data
    if (_blob->need_data && shard_meta.shard_len() > 0) {
        data_ok = process_data(shard_meta, ctx);
    }

    // 再来处理meta
    if (!ctx->fast_range_get) {
        meta_ok = process_meta(shard_meta);
    }

    if (data_ok && meta_ok) {
        ctx->shard_success = true;
        ++_success_num;
    } else {
        // if process_data return success and process_meta return false, need to remove enc_data
        _blob->enc_data.erase(ctx->shard_index);
        ++_failed_num;
    }
}

int BaseShardReader::wait_and_check(int timeout_ms) {
    return AIE_FAIL;
}

int BaseShardReader::get_channel(const base::EndPoint &endpoint, std::shared_ptr<baidu::rpc::Channel>* channel) {
    baidu::rpc::ChannelOptions options;
    options.timeout_ms = FLAGS_call_timeout_ms;
    options.connect_timeout_ms = FLAGS_connect_timeout_ms;
    //options.use_rdma = baidu::rpc::FLAGS_enable_rdma;
    auto tmp_channel = std::make_shared<baidu::rpc::Channel>();
    int ret = tmp_channel->Init(endpoint, &options);
    if (ret != 0) {
        LOG(WARNING) << "channel init failed, ret:" << ret;
    } else {
        *channel = tmp_channel;
    }
    return ret;
}

void BaseShardReader::remove_channel_from_cache(ChannelCache* chan_cache, const base::EndPoint &endpoint) {
    chan_cache->remove_channel(endpoint);
}

int32_t SingleShardReader::process() {
    // 对range get只读一个shard的情况进行特定优化，进行同步读提升效率
    const uint64_t& log_id = _option.log_id;

    aries::pb::ShardGetRequest req;
    aries::pb::ShardGetResponse res;
    req.set_token(_option.token);
    req.set_volume_id(bid2vid(_blob->blob_id));
    req.set_vbid(bid2vbid(_blob->blob_id));
    req.set_need_data(_blob->need_data);
    req.set_need_meta(_blob->need_meta);
    req.set_shard_index(_blob->shard_first);
    req.set_timeout_ms(_option.shard_call_timeout_ms);
    req.set_priority(_option.priority);
    req.set_network_qos(_option.network_qos);
    req.set_offset(_blob->offset - _blob->shard_size * _blob->shard_first);
    req.set_len(_blob->len);
    req.set_fast_range_get(_blob->fast_range_get);

    int index = -1;
    for (size_t i = 0; i < _volume->shards.size(); ++i) {
        if (_volume->shards[i].shard_index == _blob->shard_first) {
            index = i;
            break;
        }
    }
    assert(index >= 0);

    std::shared_ptr<baidu::rpc::Channel> channel;
    auto data_service_addr = aries::common::get_data_service_addr_by_qos(
            _volume->shards[index].addr, _option.network_qos);
    if (FLAGS_fast_range_get_use_privilege_dataport && _blob->fast_range_get) {
        data_service_addr = aries::common::get_data_service_addr_by_qos(
            _volume->shards[index].addr, ::aries::PRIVILEGE);
    }

    int ret = get_channel(data_service_addr, &channel);
    if (!(ret == AIE_OK && channel != nullptr)) {
        ARIES_RPC_LOG(WARNING) << "get shard failed due to init channel failed, ret:" << ret
            << " shard:" << _blob->shard_first << " network_qos:" << _option.network_qos
            << " node_addr:" << data_service_addr << " offset:" << req.offset() << " len:" << req.len();
        return AIE_DEGRADED;
    }

    ARIES_RPC_VLOG(TRACE) << "try get shard, shard:" << _blob->shard_first
        << " network_qos:" << _option.network_qos << " node_addr:" << data_service_addr
        << " offset:" << req.offset() << " len:" << req.len();
    _get_vars->get_sync_event->add_count();
    int64_t start_time_us = base::gettimeofday_us();

    baidu::rpc::Controller cntl;
    cntl.set_log_id(_log_id);
    cntl.set_timeout_ms(_option.call_timeout_ms);
    aries::pb::DataNodeDataService_Stub stub(channel.get());
    stub.get(&cntl, &req, &res, nullptr);
    int64_t cost_us = std::max(0L, base::gettimeofday_us() - start_time_us);
    if (cntl.Failed()) {
        if (_blob->request_error_info_mutex) {
            _blob->request_error_info_mutex->lock();
            if (_blob->request_error_info_for_copy) {
                auto shard_error_info = _blob->request_error_info_for_copy->add_shard_error_info();
                shard_error_info->set_remote_addr(common::endpoint2str(cntl.remote_side()));
                shard_error_info->set_local_addr(common::endpoint2str(cntl.local_side()));
                shard_error_info->set_error_code(cntl.ErrorCode());
                shard_error_info->set_timestamp(base::gettimeofday_s());
            }
            _blob->request_error_info_mutex->unlock();
        }
        ARIES_RPC_LOG(WARNING) << "get shard failed, remote:" << cntl.remote_side() << " rpc_error:"
            << cntl.ErrorCode() << " (" << cntl.ErrorText() << ") shard:" << _blob->shard_first
            << " latency_us:" << cost_us;
        _get_vars->client_get_shard_failed_adder << 1;
    } else if (res.status().code() == AIE_OK) {
        ARIES_RPC_DEBUG_LOG(DEBUG) << "get shard success, remote:" << cntl.remote_side() << " rpc_status:"
            << common::pb2json(res.status()) << " shard:" << _blob->shard_first << " latency_us:" << cost_us
            << " server_process_us:" << cntl.server_process_time_us();
        _get_vars->client_get_shard_latency << cntl.latency_us();
        try_allocate_memory((uint64_t)_blob->len);
        cntl.response_attachment().copy_to(_blob->data, _blob->len);
        uint32_t crc = base::crc32c::Value(_blob->data, _blob->len);
        if (res.has_shard_meta() && res.shard_meta().has_shard_crc()
                && crc != res.shard_meta().shard_crc()) {
            ARIES_RPC_LOG(WARNING) << "shard crc not match, expected: "
                << res.shard_meta().shard_crc() << "actual: " << crc;
            if (_blob->api_data != NULL) {
                _blob->api_data->resize(0);
            } else {
                delete[] _blob->data;
            }
            _blob->data = NULL;
        } else {
            _blob->ret = AE_OK;
            _blob->reuse_buffer = true;
            ++_finish_num;
            _acc_shard_cost += cost_us;
            _max_shard_cost = std::max(_max_shard_cost, cost_us);
            _get_vars->get_sync_event->signal();
            return AIE_OK;
        }
    } else {
        if (_blob->request_error_info_mutex) {
            _blob->request_error_info_mutex->lock();
            if (_blob->request_error_info_for_copy) {
                auto shard_error_info = _blob->request_error_info_for_copy->add_shard_error_info();
                shard_error_info->set_remote_addr(common::endpoint2str(cntl.remote_side()));
                shard_error_info->set_local_addr(common::endpoint2str(cntl.local_side()));
                shard_error_info->set_error_code(cntl.ErrorCode());
                shard_error_info->set_timestamp(base::gettimeofday_s());
            }
            _blob->request_error_info_mutex->unlock();
        }
        ARIES_RPC_LOG(NOTICE) << "get shard failed, remote:" << cntl.remote_side() << " rpc_status:"
            << common::pb2json(res.status()) << " shard:" << _blob->shard_first << " latency_us:" << cost_us;
        if (res.status().code() == AIE_TIMEOUT) {
            _get_vars->client_get_shard_timeout_adder << 1;
        } else {
            _get_vars->client_get_shard_failed_adder << 1;
        }
    }
    _get_vars->get_sync_event->signal();
    return AIE_DEGRADED;
}

int MultiShardReader::check() {
    const uint64_t& log_id = _log_id;
    int32_t ec_k = _volume->eco.param.k;
    int32_t ec_n = _volume->eco.param.n;
    if (_is_removed) {
        return AIE_BLOB_NOT_EXIST;
    }

    if (_not_found_num > (ec_n - ec_k)) {
        // all return error code is valid, so must be not exist blob
        if (calc_membership_matches(log_id, _ctxs_map) >= ec_n) {
            ARIES_RPC_LOG(TRACE) << "bid:" << bid2str(_blob->blob_id) << " lost shards";
            return AIE_BLOB_NOT_EXIST;
        } else if (_finish_num < _max_req_num) {
            return AIE_AGAIN;
        } else if (_finish_num >= _max_req_num) {
            if (!_after_retry_membership) {
                return AIE_AGAIN;
            }
            return AIE_FAIL;
        }
    }

    if (_busy_num > (ec_n - ec_k)) {
        if (!_after_retry_membership) {
            return AIE_AGAIN;
        }
        ARIES_RPC_LOG(WARNING) << "bid:" << bid2str(_blob->blob_id) << " is refused by flow control";
        return AIE_BUSY;
    }

    if (_blob->check_exist) {
        if (_success_num >= ec_k) {
            return AIE_OK;
        }
    } else if (!_blob->need_meta || _blob->got_meta) {
        if (_success_num >= ec_k && _blob->got_compresstype) {
            return AIE_OK;
        }
        if (_success_num >= ec_k && _blob->fast_degrade_range_get) {
            return AIE_OK;
        }
        if (_success_num > 0 && _blob->blob_len == 0 && !_blob->fast_range_get) {
            return AIE_OK;
        }
    }
    // can not be vlet_num, because vlet_num <= ec_n
    // a not exist blob should return AIE_BLOB_NOT_EXIST other than AIE_FAIL,
    // when a vlet is not normal
    // http://newicafe.baidu.com/issue/aries-1671/show?from=page
    if ((ec_n - _failed_num + _retry_num + _not_found_num) < ec_k) {
        if (!_after_retry_membership) {
            return AIE_AGAIN;
        }
        ARIES_RPC_LOG(WARNING) << "bid:" << bid2str(_blob->blob_id)
            << " haven't collected enough enc_data, need:" << ec_k
            << ", success_num:" << _success_num;
        return AIE_FAIL;
    }
    return AIE_AGAIN;
}

bool MultiShardReader::process_data(const aries::pb::ShardMeta& shard_meta,
        ShardReadContext* ctx) {
    const uint64_t& log_id = _log_id;
    size_t size = ctx->res_attachment.size();
    if (size != shard_meta.shard_len()) {
        ARIES_RPC_LOG(WARNING) << "shard len not match, expected: " << shard_meta.shard_len()
            << " actual: " << size
            << " shard_index:" << ctx->shard_index;
        return false;
    }
    try_allocate_memory((uint64_t)_volume->eco.param.n * shard_meta.shard_len());
    char* shard_data = _blob->data + ctx->shard_index * shard_meta.shard_len();
    ctx->res_attachment.copy_to(shard_data);
    if (shard_meta.has_shard_crc()) {
        uint32_t crc = base::crc32c::Value(shard_data, size);
        if (crc != shard_meta.shard_crc()) {
            ARIES_RPC_LOG(WARNING) << "shard crc not match, expected: "
                << shard_meta.shard_crc() << ", actual: " << crc;
            return false;
        }
    }
    _blob->enc_data[ctx->shard_index] = shard_data;
    return true;
}

int MultiShardReader::wait_and_check(int timeout_ms) {
    const uint64_t& log_id = _log_id;
    timespec ts = base::milliseconds_from_now(timeout_ms);
    while (_finish_num < _sent_req_num) {
        std::shared_ptr<ShardReadContext> ctx;
        if (!_result_queue->take(&ctx, &ts)) {
            break;
        }

        // 注意我们可能会给同一个vlet发送多个请求，如果两个请求都返回则舍弃掉先发送的请求
        auto shard_ctx_iter = _ctxs_map.find(ctx->shard_index);
        if (shard_ctx_iter == _ctxs_map.end()) {
            _ctxs_map.insert(std::make_pair(ctx->shard_index, ctx));
        } else {
            if (shard_ctx_iter->second->start_time_us < ctx->start_time_us) {
                try_abandon_shard(ctx->shard_index);
                _ctxs_map.insert(std::make_pair(ctx->shard_index, ctx));
            } else {
                ctx->code = AIE_FAIL;
            }
        }

        _cache_monitor.update_membership_version(ctx->membership.get(),
            ctx->shard_index, ctx->vlet_version);
        merge(ctx.get());
        int ret = check();
        // success or give up
        if (ret != AIE_AGAIN) {
            return ret;
        }
        if (_blob->need_data &&
            _sent_req_num - _failed_num < _volume->eco.param.k &&
            _sent_req_num < _max_req_num) {
            // send more get request right away
            break;
        }
    }
    if (_finish_num == _max_req_num && !_after_retry_membership) {
        // check membership now
        ARIES_RPC_LOG(TRACE) << "update volume info";
        auto retry_num = _cache_monitor.update_volume_info(&_vlet_get_queue, &_volume);
        _after_retry_membership = true;
        if (retry_num == 0) {
            return check();
        }
        for (auto& pos : _vlet_get_queue) {
            // 舍弃从过期的vlet中获取的数据
            try_abandon_shard(_volume->shards[pos].shard_index);
        }
        _max_req_num += retry_num;
        _retry_num += retry_num;
    }
    return AIE_AGAIN;
}

void MultiShardReader::try_abandon_shard(int shard_index) {
    auto shard_ctx_iter = _ctxs_map.find(shard_index);
    if (shard_ctx_iter != _ctxs_map.end()) {
        if (shard_ctx_iter->second->shard_success) {
            --_success_num;
            _blob->enc_data.erase(shard_index);
            ++_failed_num;
        }
        if (shard_ctx_iter->second->code == AIE_BUSY) {
            --_busy_num;
        } else if (shard_ctx_iter->second->code == AIE_BLOB_NOT_EXIST) {
            --_not_found_num;
        }

        _ctxs_map.erase(shard_ctx_iter);
    }
}

int MetaShardReader::check() {
    const uint64_t& log_id = _log_id;
    if (_is_removed) {
        // has been removed
        return AIE_BLOB_NOT_EXIST;
    }

    if (_blob->got_meta) {
        return AIE_OK;
    }

    if (_finish_num >= _max_req_num) {
        if (_finish_num == _busy_num) {
            ARIES_RPC_LOG(WARNING) << "bid:" << bid2str(_blob->blob_id) << " is refused by flow control";
            return AIE_BUSY;
        }
        // all return error code is valid, so must be not exist blob
        if (_not_found_num >= _volume->eco.param.n) {
            ARIES_RPC_LOG(TRACE) << "bid:" << bid2str(_blob->blob_id) << " lost shards";
            return AIE_BLOB_NOT_EXIST;
        }
        return AIE_FAIL;
    }
    return AIE_AGAIN;
}

int MetaShardReader::wait_and_check(int timeout_ms) {
    timespec ts = base::milliseconds_from_now(timeout_ms);
    while (_finish_num < _sent_req_num) {
        std::shared_ptr<ShardReadContext> ctx;
        if (!_result_queue->take(&ctx, &ts)) {
            break;
        }
        merge(ctx.get());
        int ret = check();
        // success or give up
        if (ret != AIE_AGAIN) {
            return ret;
        }
    }
    return AIE_AGAIN;
}

int CrossShardReader::check() {
    const uint64_t& log_id = _log_id;
    if (_is_removed) {
        // has been removed
        return AIE_BLOB_NOT_EXIST;
    }
    if (_blob->got_compresstype && _blob->compress_type != 0) {
        ARIES_RPC_LOG(NOTICE) << "range get blob has compress type, compress_type: " 
            << _blob->compress_type;
        return AIE_DEGRADED;
    }
    if (!_blob->need_meta || _blob->got_meta) {
        if (_blob->is_match_range()) {
            return AIE_OK;
        }
    } else if (_finish_num == _max_req_num) {
        ARIES_RPC_LOG(TRACE) << "range get failed because meta has not been found";
        return AIE_DEGRADED;
    }
    if (_failed_num > 0) {
        ARIES_RPC_VLOG(TRACE) << "range get failed because we has failed to get some shards";
        return AIE_DEGRADED;
    }
    return AIE_AGAIN;
}

bool CrossShardReader::process_data(const aries::pb::ShardMeta& shard_meta, ShardReadContext* ctx) {
    const uint64_t& log_id = _log_id;
    size_t size = ctx->res_attachment.size();
    if (size != shard_meta.shard_len()) {
        if (size < shard_meta.shard_len() &&
            (ctx->shard_index == _blob->shard_first || ctx->shard_index == _blob->shard_last)) {
            _blob->partial_return_shard_indexs[ctx->shard_index] = true;
        } else {
            ARIES_RPC_LOG(WARNING) << "shard len not match, expected: " << shard_meta.shard_len()
                << " actual: " << size
                << " shard_index:" << ctx->shard_index;
            return false;
        }
    }
    try_allocate_memory((uint64_t)_volume->eco.param.n * shard_meta.shard_len());
    char* shard_data = _blob->data + ctx->shard_index * shard_meta.shard_len();
    if (size < shard_meta.shard_len() && ctx->shard_index == _blob->shard_first) {
        shard_data += _first_shard.offset;
    }
    ctx->res_attachment.copy_to(shard_data);
    if (shard_meta.has_shard_crc()) {
        uint32_t crc = base::crc32c::Value(shard_data, size);
        if (crc != shard_meta.shard_crc()) {
            ARIES_RPC_LOG(WARNING) << "shard crc not match, expected: "
                << shard_meta.shard_crc() << "actual: " << crc;
            return false;
        }
    }
    _blob->enc_data[ctx->shard_index] = shard_data;
    return true;
}

void CrossShardReader::prepare_vlet_queue() {
    // 和其他ShardReader不同，range_get的时候我们不关心az分布和unbalanced状态，只尝试读取所需的原始数据
    const uint64_t& log_id = _log_id;

    int vlet_num = _volume->shards.size();
    int index = -1;
    for (int i = 0; i < vlet_num; ++i) {
        if (_volume->shards[i].shard_index == _blob->shard_first) {
            index = i;
            break;
        }
    }
    assert(index >= 0);

    for (int i = index; i < index + _blob->range_shards_num(); ++i) {
        _vlet_get_queue.push_back(i);
    }
    //_blob->range_start_index = index;

    // only set first shard and last shard len
    _first_shard.shard_index = _blob->shard_first;
    _first_shard.fast_range_get = _blob->fast_range_get;
    _first_shard.offset = _blob->offset - _blob->shard_first * _blob->shard_size;
    if (_blob->len < _blob->shard_size - _first_shard.offset) {
        _first_shard.len = _blob->len;
    } else {
        _first_shard.len = _blob->shard_size - _first_shard.offset;
    }
    if (_blob->shard_last > _blob->shard_first) {
        _last_shard.shard_index = _blob->shard_last;
        _last_shard.fast_range_get = _blob->fast_range_get;
        _last_shard.offset = 0;
        _last_shard.len = _blob->len + _blob->offset - _blob->shard_last * _blob->shard_size;
    }
    _max_req_num = _vlet_get_queue.size();
    return;
}

int CrossShardReader::calc_request_num() {
    // 一次读取所有shard
    return _blob->range_shards_num();
}

int CrossShardReader::wait_and_check(int timeout_ms) {
    const uint64_t& log_id = _log_id;
    timespec ts = base::milliseconds_from_now(timeout_ms);
    if (_blob->need_data && _blob->fast_range_get) {
        // fast_range_get的时候使用用户提供的shard_size，可以提前allocate_memory
        try_allocate_memory((uint64_t)_volume->eco.param.n * _blob->shard_size);
    }
    while (_finish_num < _sent_req_num) {
        std::shared_ptr<ShardReadContext> ctx;
        if (!_result_queue->take(&ctx, &ts)) {
            break;
        }

        _ctxs_map[ctx->shard_index] = ctx;
        if (!_blob->fast_range_get) {
            if (ctx->meta != nullptr && _blob->shard_size != ctx->meta->shard_len()) {
                ARIES_RPC_LOG(NOTICE) << "range get shard len not match, user: " << _blob->shard_size
                    << ", actual: " << ctx->meta->shard_len();
                ++_finish_num;
                ++_failed_num;
                return AIE_DEGRADED;
            }
        }
        merge(ctx.get());
        int ret = check();
        // success or give up
        if (ret != AIE_AGAIN) {
            return ret;
        }
    }
    return AIE_DEGRADED;
}

bool CrossShardReader::find_range_get_shard(int shard_index, RangeGetShard* range) {
    if (shard_index == _first_shard.shard_index) {
        *range = _first_shard;
        return true;
    } else if (shard_index == _last_shard.shard_index) {
        *range = _last_shard;
        return true;
    }
    return false;
}

void DegradeShardReader::prepare_vlet_queue() {
    MultiShardReader::prepare_vlet_queue();
    if (_sent_req_num != 0) {
        // degrade from other shard reader
        // check and abandon 'range get' shard
        if (_blob->range_shards_num() == 1) {
            try_abandon_shard(_blob->shard_first);
            _max_req_num = _vlet_get_queue.size() + 1;
            _retry_num += 1;
        } else if (_blob->range_shards_num() >= 2) {
            try_abandon_shard(_blob->shard_first);
            try_abandon_shard(_blob->shard_last);
            int index = -1;
            for (size_t i = 0; i < _volume->shards.size(); ++i) {
                if (_volume->shards[i].shard_index == _blob->shard_first) {
                    index = i;
                    break;
                }
            }
            assert(index >= 0);
            _vlet_get_queue.erase(
                std::remove_if(_vlet_get_queue.begin(), _vlet_get_queue.end(),
                [&](int pos) { return pos > index && pos < (index + _blob->range_shards_num() - 1); }),
                _vlet_get_queue.end());
            _max_req_num = _vlet_get_queue.size() + _sent_req_num;
            _retry_num += 2;
        }

        for (auto& [shard_index, ctx] : _ctxs_map) {
            _cache_monitor.update_membership_version(ctx->membership.get(),
                ctx->shard_index, ctx->vlet_version);
        }
    }

    if (_blob->fast_degrade_range_get) {
        _shard_range.fast_range_get = _blob->fast_range_get;
        _shard_range.offset = _blob->offset - _blob->shard_first * _blob->shard_size;
        _shard_range.len = _blob->len;
    }
}

bool DegradeShardReader::process_data(const aries::pb::ShardMeta& shard_meta, ShardReadContext* ctx) {
    const uint64_t& log_id = _log_id;
    size_t size = ctx->res_attachment.size();
    // 根据是否为fast_degrade_range_get检查shard len
    if (_blob->fast_degrade_range_get && size != _shard_range.len) {
        ARIES_RPC_LOG(WARNING) << "shard len not match, expected: " << _shard_range.len
            << " actual: " << size
            << " shard_index:" << ctx->shard_index;
        return false;
    } else if (!_blob->fast_degrade_range_get && size != shard_meta.shard_len()) {
        ARIES_RPC_LOG(WARNING) << "shard len not match, expected: " << shard_meta.shard_len()
            << " actual: " << size
            << " shard_index:" << ctx->shard_index;
        return false;
    }
    try_allocate_memory((uint64_t)_volume->eco.param.n * shard_meta.shard_len());
    char* shard_data = _blob->data + ctx->shard_index * shard_meta.shard_len();
    ctx->res_attachment.copy_to(shard_data);
    if (shard_meta.has_shard_crc()) {
        uint32_t crc = base::crc32c::Value(shard_data, size);
        if (crc != shard_meta.shard_crc()) {
            ARIES_RPC_LOG(WARNING) << "shard crc not match, expected: "
                << shard_meta.shard_crc() << "actual: " << crc;
            return false;
        }
    }
    _blob->enc_data[ctx->shard_index] = shard_data;
    return true;
}

int DegradeShardReader::wait_and_check(int timeout_ms) {
    if (_blob->fast_degrade_range_get) {
        try_allocate_memory((uint64_t)_volume->eco.param.n * _blob->shard_size);
    }
    return MultiShardReader::wait_and_check(timeout_ms);
}

bool DegradeShardReader::find_range_get_shard(int shard_index, RangeGetShard* range) {
    if (_blob->fast_degrade_range_get) {
        *range = _shard_range;
        return true;
    }
    return false;
}

int CheckExistReader::calc_request_num() {
    const aries::pb::SpaceInfo& space = _volume->space_info;

    int request_num = _volume->eco.param.k - _success_num + space.backup_get_shard_num();
    return std::min(request_num, _max_req_num - _sent_req_num);
}

std::shared_ptr<BaseShardReader> ShardReaderFactory::get_shard_reader(VolumeInfoPtr volume,
        GetBlobInfo* blob_info, MetaHandler* meta_handler, ChannelCache* channel_cache,
        RequestOptions& option, std::shared_ptr<BlobOperGetVars> get_vars) {
    if (!blob_info->need_data && blob_info->need_meta) {
        return std::shared_ptr<MetaShardReader>(new MetaShardReader(
            volume, blob_info, channel_cache, option, get_vars));
    }
    if (blob_info->need_range && blob_info->shard_size != 0) {
        uint32_t end_pos = blob_info->offset + blob_info->len;
        blob_info->shard_first = blob_info->offset / blob_info->shard_size;
        blob_info->shard_last = std::min((int)volume->eco.param.k,
            (int)(end_pos / blob_info->shard_size + !!(end_pos % blob_info->shard_size))) - 1;

        if (blob_info->range_shards_num() <= volume->eco.param.k) {
            int index = -1;
            for (size_t i = 0; i < volume->shards.size(); ++i) {
                if (volume->shards[i].shard_index == blob_info->shard_first) {
                    index = i;
                    break;
                }
            }
            if (index >= 0) {
                int n = blob_info->range_shards_num();
                // make sure all vlets in range are alive
                if ((size_t)(n + index - 1) < volume->shards.size() &&
                    volume->shards[n + index - 1].shard_index == blob_info->shard_last) {
                    if (blob_info->shard_first == blob_info->shard_last && blob_info->fast_range_get) {
                        return std::shared_ptr<SingleShardReader>(new SingleShardReader(
                            volume, blob_info, channel_cache, option, get_vars));
                    } else {
                        return std::shared_ptr<CrossShardReader>(new CrossShardReader(
                            volume, blob_info, channel_cache, option, get_vars));
                    }
                }
            }
            // even if fast_range_get's vlet dead, we can still fast degrade range get
            blob_info->fast_degrade_range_get = blob_info->can_fast_degrade_range_get();
        }
    }
    if (blob_info->fast_degrade_range_get) {
        return std::shared_ptr<DegradeShardReader>(new DegradeShardReader(
            volume, blob_info, meta_handler, channel_cache, option, get_vars));
    }
    if (blob_info->check_exist) {
        return std::shared_ptr<CheckExistReader>(new CheckExistReader(
                volume, blob_info, meta_handler, channel_cache, option, get_vars));
    }
    return std::shared_ptr<MultiShardReader>(new MultiShardReader(
        volume, blob_info, meta_handler, channel_cache, option, get_vars));
}

std::shared_ptr<BaseShardReader> ShardReaderFactory::get_degrade_shard_reader(
        std::shared_ptr<BaseShardReader> shard_reader, MetaHandler* meta_handler) {
    shard_reader->_blob->fast_degrade_range_get = shard_reader->_blob->can_fast_degrade_range_get();
    if (shard_reader->name() == "CrossShardReader") {
        if (shard_reader->_blob->fast_degrade_range_get) {
            return std::shared_ptr<DegradeShardReader>(new DegradeShardReader(
                shard_reader->_volume, shard_reader->_blob, meta_handler, shard_reader->_channel_cache,
                shard_reader->_option, shard_reader->_get_vars));
        }
        std::shared_ptr<CrossShardReader> cross_shard_reader =
            std::dynamic_pointer_cast<CrossShardReader>(shard_reader);
        std::shared_ptr<DegradeShardReader> degrade_shard_reader =
            std::shared_ptr<DegradeShardReader>(new DegradeShardReader(
                shard_reader->_volume, shard_reader->_blob, meta_handler, shard_reader->_channel_cache,
                shard_reader->_option, shard_reader->_get_vars));
        degrade_shard_reader->_result_queue = cross_shard_reader->_result_queue;
        degrade_shard_reader->_ctxs_map = cross_shard_reader->_ctxs_map;
        degrade_shard_reader->_is_removed = cross_shard_reader->_is_removed;
        degrade_shard_reader->_finish_num = cross_shard_reader->_finish_num;
        degrade_shard_reader->_success_num = cross_shard_reader->_success_num;
        degrade_shard_reader->_not_found_num = cross_shard_reader->_not_found_num;
        degrade_shard_reader->_busy_num = cross_shard_reader->_busy_num;
        degrade_shard_reader->_failed_num = cross_shard_reader->_failed_num;
        degrade_shard_reader->_sent_req_num = cross_shard_reader->_sent_req_num;
        degrade_shard_reader->_max_shard_cost = cross_shard_reader->_max_shard_cost;
        degrade_shard_reader->_acc_shard_cost = cross_shard_reader->_acc_shard_cost;

        return degrade_shard_reader;
    } else if (shard_reader->name() == "SingleShardReader") {
        return std::shared_ptr<DegradeShardReader>(new DegradeShardReader(
            shard_reader->_volume, shard_reader->_blob, meta_handler, shard_reader->_channel_cache,
            shard_reader->_option, shard_reader->_get_vars));
    }
    assert(0);
    return nullptr;
}
}
