/**
 * ARIES - A Reliable and Impressive EB-level Storage.
 * Copyright (c) 2018 Baidu.com, Inc. All Rights Reserved.
 *
 * Author: <PERSON> <PERSON> (guo<PERSON>@baidu.com)
 * Date: 2018/02/05
 * Desciption: Declaration of dataagent's heartbeat
 *
 */

#ifndef BAIDU_INF_ARIES_DATAAGENT_HEARTBEAT_H
#define BAIDU_INF_ARIES_DATAAGENT_HEARTBEAT_H

#include <atomic>
#include <base/endpoint.h>
#include <bthread.h>
#include <bthread_unstable.h>
#include "baidu/inf/aries-api/proxy/proxy.h"
#include "baidu/inf/aries-api/common/heartbeat.h"
#include "baidu/inf/aries-api/common/master_tracker.h"
#include "baidu/inf/aries-api/common/proto/common.pb.h"
#include "baidu/inf/aries-api/common/proto/master.pb.h"

namespace aries {

class Proxy;

// has backgroud thread
class Heartbeat : public common::Heartbeat {
public:
    Heartbeat() : _master_tracker(nullptr) {};
    virtual ~Heartbeat();
    int init(ProxyConf* conf, Proxy* proxy);
    void reload(ProxyConf* conf);
    void stop();
    virtual void heartbeat_rpc();
    void heartbeat_done(aries::pb::DataAgentHeartbeatRequest* request,
                        aries::pb::AckResponse* response);
    ARIES_VIRTUAL base::EndPoint get_master_addr() {
        return _master_tracker->get_master();
    }
#ifdef _UNIT_TEST
protected:
    static bool is_heartbeat_done() {
        return _s_is_heartbeat_done;
    }
    static bool _s_is_heartbeat_done;
#endif

private:
    std::string _token;
    std::string _proxy_name;
    std::string _proxy_version;
    aries::common::MasterTracker* _master_tracker;
    Proxy* _proxy = nullptr;
};

}

#endif
