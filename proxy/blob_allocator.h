/***************************************************************************
 * 
 * Copyright (c) 2017 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/
 
 
 
/**
 * @file dataagent/blob_allocator.h
 * <AUTHOR>
 * @date 2017/03/05 13:59:35
 * @brief 
 *
 * In each put blob request, BlobAllocator does:
 *     allocate a blob in target space, determine shard size and shard location for storage
 *  
 **/

#ifndef BAIDU_INF_ARIES_DATAAGENT_BLOB_ALLOCATOR_H
#define BAIDU_INF_ARIES_DATAAGENT_BLOB_ALLOCATOR_H

#include "baidu/inf/aries-api/proxy/meta_handler.h"
#include "baidu/inf/aries-api/proxy/allocator_client.h"
#include "baidu/inf/aries-api/proxy/shard_util.h"
#include "baidu/inf/aries-api/common/proto/volume_service.pb.h"
#include "baidu/inf/aries-api/common/common.h"

namespace aries {

class MetaHandler;
class AllocatorClient;

class BlobAllocator {
public:
    static inline uint32_t calc_shard_size(uint32_t len, int k) {
        return len / k + !!(len % k);
    }
    static inline uint32_t calc_reserve(const PutBlobInfo &blob) {
        // cal reserved size to allocate vlet, contains key and meta
        uint32_t reserve_size = blob.shard_size + blob.key.size() + blob.meta.size();
        // align to 1KB
        uint32_t nkb = reserve_size >> 10;
        if ((nkb << 10) < reserve_size) {
            reserve_size = (nkb + 1) << 10;
        }
        return reserve_size;
    }
public:
    BlobAllocator(MetaHandler* meta_handler, AllocatorClient* allocator_client) : _timeout_ms(1000) {
        _meta_handler = meta_handler;
        _allocator_client = allocator_client;
    }
    ~BlobAllocator() {}
    BlobAllocator &set_log_id(uint64_t log_id) {
        _cntl.log_id = log_id;
        return *this;
    }
    BlobAllocator &set_timeout_ms(int timeout_ms) {
        _timeout_ms = timeout_ms;
        return *this;
    }
    const std::string &last_error() {
        return _last_error;
    }

    // set blob_id and volume ec options, shard locations
    int alloc(const std::string &space_name, PutBlobInfo &blob, VolumeInfo *volume);

private:
    int try_alloc(uint32_t size, __uint128_t *bid, VolumeInfo *volume);

private:
    int _timeout_ms;

    std::shared_ptr<aries::pb::SpaceInfo> _space;
    std::string _last_error;

    RpcControl _cntl;

    MetaHandler* _meta_handler;
    AllocatorClient* _allocator_client;
};

}

#endif

