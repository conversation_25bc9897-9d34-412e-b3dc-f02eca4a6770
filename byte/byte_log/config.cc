// Copyright (c) 2020-present, ByteDance Inc. All rights reserved.

#include <assert.h>
#include <vector>
#include "byte/byte_log/config.h"
#include "byte/include/assert.h"
#include "byte/string/algorithm.h"

namespace byte {

bool ConfigParser::Parse() {
    if (parsed_) {
        return true;
    }
    if (Parse(content_, &sections_)) {
        parsed_ = true;
    }
    return parsed_;
}

bool ConfigParser::ReadItem(
    const std::string& section,
    const std::string& key,
    std::string* value) {
    BYTE_ASSERT(parsed_);
    SectionIterator itr1 = SectionIterator(sections_);
    while (itr1.Valid()) {
        std::string s;
        itr1.Name(&s);
        if (s == section) {
            break;
        }
        itr1.Next();
    }

    if (itr1.Valid()) {
        ItemIterator itr2 = itr1.CreateItemIterator();
        while (itr2.Valid()) {
            std::string k;
            std::string v;
            itr2.Item(&k, &v);
            if (k == key) {
                value->swap(v);
                return true;
            }
            itr2.Next();
        }
    }
    return false;
}

ConfigParser::SectionIterator ConfigParser::CreateSectionIterator() {
    SectionIterator iterator(sections_);
    return iterator;
}

bool ConfigParser::Parse(const std::string& ini, std::list<Section>* sections) {
    sections->clear();

    std::vector<std::string> lines;
    SplitString(ini, "\n", &lines);
    std::list<std::string> line_list(lines.begin(), lines.end());
    while (true) {
        Section section;
        if (ParseSection(&line_list, &section)) {
            sections->push_back(section);
        } else {
            break;
        }
    }
    return !sections->empty() && line_list.empty();
}

bool ConfigParser::ParseSection(std::list<std::string>* lines, Section* section) {
    std::string name;
    while (!lines->empty()) {
        std::string line = lines->front();
        StringTrim(&line);
        if (!IsNullLine(line)) {
            if (ParseSectionName(line, &name)) {
                lines->pop_front();
                break;
            } else {
                return false;
            }
        }
        lines->pop_front();
    }

    if (name.empty()) {
        return false;
    }
    section->name = name;

    while (!lines->empty()) {
        std::string line = lines->front();
        StringTrim(&line);
        if (!IsNullLine(line)) {
            if (ParseSectionName(line, &name)) {
                break;
            }
            std::string key;
            std::string value;
            if (ParseItem(line, &key, &value)) {
                section->items.push_back(std::make_pair(key, value));
            } else {
                return false;
            }
        }
        lines->pop_front();
    }
    return true;
}

bool ConfigParser::IsNullLine(const std::string& line) {
    std::string output = StringTrim(line);
    if (output.empty()) {
        return true;
    }
    if (output[0] == ';') {
        return true;
    }
    return false;
}

bool ConfigParser::ParseSectionName(const std::string& line, std::string* name) {
    BYTE_ASSERT_FALSE(line.empty());
    name->clear();
    std::string::size_type size = line.size();
    if (line[0] == '[' && line[size - 1] == ']') {
        *name = line.substr(1, size - 2);
    }
    return !name->empty();
}

bool ConfigParser::ParseItem(const std::string& line,
                             std::string* key,
                             std::string* value) {
    std::string::size_type pos = line.find_first_of('=');
    if (pos == std::string::npos) {
        return false;
    }
    std::string k = line.substr(0, pos);
    std::string v = line.substr(pos + 1);
    *key = StringTrim(k);
    *value = StringTrim(v);
    return true;
}

}  // namespace byte
