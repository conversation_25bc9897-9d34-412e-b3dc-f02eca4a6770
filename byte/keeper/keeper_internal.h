// Copyright (c) 2018-present, ByteDance Inc. All rights reserved.

#pragma once

#include <memory>
#include <string>
#include <type_traits>
#include <unordered_map>
#include <vector>

#include "absl/synchronization/mutex.h"
#include "byte/base/singleton.h"
#include "byte/include/assert.h"
#include "byte/system/ip6_address.h"
#define THREADED
#if __has_include(<zookeeper/zookeeper.h>)
#include <zookeeper/zookeeper.h>
#include <zookeeper/zookeeper.jute.h>
#else
#include <zookeeper-client-c/generated/zookeeper.jute.h>
#include <zookeeper-client-c/include/zookeeper.h>
#endif

namespace byte {
namespace internal {

enum class IPType { kNotInit = 0, kIPv4Only = 1, kIPv6Only, kDualStack };

// GlobalKeeperConfig: singleton class to store the config of Keeper.
// The config includes:
// zk_session_expired_timeout_ms_:
//     zk seesion timeout expired time, default 6000ms;
// zk_log_path_: zk log path, default ./zk,log;
// dns_suffix_: dns suffix, default ".byted.org";
// port_: port, default 2181;
// zk_dns_refresh_interval_seconds_: dns refresh interval, default 1 second;
// zk_dns_max_refresh_interval_seconds_: dns refresh max interval, default 5 mins;
//   When all zk server ips are down, the client will wait at most
//   zk_dns_max_refresh_interval_seconds_;
// zk_init_waiting_base_interval_ms_:
//     max wait connect time after zk initialized, default 3 ms;
class GlobalKeeperConfig : protected byte::SingletonBase<GlobalKeeperConfig> {
public:
    void SetDomainAlias(const std::string& region, const std::string &domain) {
        absl::MutexLock locker(&mutex_);
        domain_alias_map_[region] = domain;
    }

    void SetHardCodeSockList(const std::string& region,
                             const std::vector<std::string>& socket_list) {
        absl::MutexLock locker(&mutex_);
        region_2_sock_list_[region] = socket_list;
    }

    static GlobalKeeperConfig *Singleton() {
        return byte::SingletonBase<GlobalKeeperConfig>::Instance();
    }

    std::string GetDomainAlias(const std::string& region) {
        absl::ReaderMutexLock locker(&mutex_);
        auto it = domain_alias_map_.find(region);
        if (it != domain_alias_map_.end()) {
            return it->second;
        }
        return "";
    }

    bool GetHardCodeSockList(const std::string& region, std::vector<std::string>* socket_list) {
        absl::ReaderMutexLock locker(&mutex_);
        auto it = region_2_sock_list_.find(region);
        if (it != region_2_sock_list_.end()) {
            *socket_list = it->second;
            return true;
        }
        return false;
    }

    void SetZKSessionExpiredTimeoutMS(uint64_t zk_session_expired_timeout_ms) {
        absl::MutexLock locker(&mutex_);
        zk_session_expired_timeout_ms_ = zk_session_expired_timeout_ms;
    }

    uint64_t GetZKSessionExpiredTimeoutMS() {
        absl::ReaderMutexLock locker(&mutex_);
        return zk_session_expired_timeout_ms_;
    }

    std::string GetLogFilePath() {
        absl::ReaderMutexLock locker(&mutex_);
        return zk_log_path_;
    }

    void SetLogFilePath(const std::string& zk_log_path) {
        absl::MutexLock locker(&mutex_);
        zk_log_path_ = zk_log_path;
    }

    std::string GetDNSSuffix() {
        absl::ReaderMutexLock locker(&mutex_);
        return dns_suffix_;
    }

    void SetDNSSuffix(const std::string& dns_suffix) {
        absl::MutexLock locker(&mutex_);
        dns_suffix_ = dns_suffix;
    }

    void SetPort(uint16_t port) {
        absl::MutexLock locker(&mutex_);
        port_ = port;
    }

    uint16_t GetPort() {
        absl::ReaderMutexLock locker(&mutex_);
        return port_;
    }

    uint64_t GetDNSRefreshTimeIntervalSeconds() {
        absl::ReaderMutexLock locker(&mutex_);
        return zk_dns_refresh_interval_seconds_;
    }

    void SetDNSRefreshTimeIntervalSeconds(uint64_t interval) {
        absl::MutexLock locker(&mutex_);
        zk_dns_refresh_interval_seconds_ = interval;
    }

    uint64_t GetDNSMaxRefreshIntervalSeconds() {
        absl::ReaderMutexLock locker(&mutex_);
        return zk_dns_max_refresh_interval_seconds_;
    }

    void SetDNSMaxRefreshIntervalSeconds(uint64_t interval) {
        absl::MutexLock locker(&mutex_);
        zk_dns_max_refresh_interval_seconds_ = interval;
    }

    uint64_t GetZkInitWaitingBaseIntervalMs() {
        absl::ReaderMutexLock locker(&mutex_);
        return zk_init_waiting_base_interval_ms_;
    }

    void SetZkInitWaitingBaseIntervalMs(uint64_t interval) {
        absl::MutexLock locker(&mutex_);
        zk_init_waiting_base_interval_ms_ = interval;
    }

    IPType GetLocalIPType() { return local_ip_type_; }

    void SetLocalIPType(IPType ip_type = IPType::kNotInit) {
        if (LIKELY(ip_type == IPType::kNotInit)) {
            byte::Ip6Address local_ipv4, local_ipv6;
            bool has_ipv4 = byte::Ip6Address::GetFirstPrivateAddress(&local_ipv4, /*v6=*/false);
            bool has_ipv6 = byte::Ip6Address::GetFirstPrivateAddress(&local_ipv6, /*v6=*/true);
            if (has_ipv4 && has_ipv6) {
                local_ip_type_ = IPType::kDualStack;
            } else if (has_ipv4) {
                local_ip_type_ = IPType::kIPv4Only;
            } else if (has_ipv6) {
                local_ip_type_ = IPType::kIPv6Only;
            }
        } else {
            local_ip_type_ = ip_type;
        }
        BYTE_ASSERT(local_ip_type_ >= IPType::kIPv4Only && local_ip_type_ <= IPType::kDualStack)
            << "Local Machine IP type is: " << static_cast<ssize_t>(local_ip_type_);
    }

private:
    GlobalKeeperConfig() {
        absl::MutexLock locker(&mutex_);
        zk_session_expired_timeout_ms_ = 6000;
        zk_log_path_ = "./zk,log";
        dns_suffix_ = ".byted.org";
        port_ = 2181;
        zk_dns_refresh_interval_seconds_ = 1;
        zk_dns_max_refresh_interval_seconds_ = 5 * 60;
        zk_init_waiting_base_interval_ms_ = 3;
        SetLocalIPType();
    }

    friend byte::SingletonBase<GlobalKeeperConfig>;

    absl::Mutex mutex_;

    uint64_t zk_session_expired_timeout_ms_;

    std::string zk_log_path_;

    std::string dns_suffix_;

    uint16_t port_;

    uint64_t zk_dns_refresh_interval_seconds_;

    uint64_t zk_dns_max_refresh_interval_seconds_;

    uint64_t zk_init_waiting_base_interval_ms_;

    std::unordered_map<std::string, std::string> domain_alias_map_;

    std::unordered_map<std::string, std::vector<std::string>> region_2_sock_list_;

    IPType local_ip_type_;
};

inline uint64_t GetZKSessionExpiredTimeoutMS() {
    return internal::GlobalKeeperConfig::Singleton()->GetZKSessionExpiredTimeoutMS();
}

inline uint64_t GetDNSRefreshTimeIntervalSeconds() {
    return internal::GlobalKeeperConfig::Singleton()->GetDNSRefreshTimeIntervalSeconds();
}

inline uint64_t GetDNSMaxRefreshIntervalSeconds() {
    return internal::GlobalKeeperConfig::Singleton()->GetDNSMaxRefreshIntervalSeconds();
}

inline uint64_t GetZkInitWaitingBaseIntervalMs() {
    return internal::GlobalKeeperConfig::Singleton()->GetZkInitWaitingBaseIntervalMs();
}

inline std::string GetDomainAlias(const std::string& region_name) {
    return internal::GlobalKeeperConfig::Singleton()->GetDomainAlias(region_name);
}

inline std::string GetDNSSuffix() {
    return internal::GlobalKeeperConfig::Singleton()->GetDNSSuffix();
}

inline bool GetHardCodeSockList(const std::string& region, std::vector<std::string>* socket_list) {
    return internal::GlobalKeeperConfig::Singleton()->GetHardCodeSockList(region, socket_list);
}

}  // namespace internal
}  // namespace byte
