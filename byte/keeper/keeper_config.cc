// Copyright (c) 2018-present, ByteDance Inc. All rights reserved.

#include "byte/keeper/keeper_config.h"

#include <string>

#include "byte/string/format/print.h"

namespace byte {

void default_acl_getter(const std::string &path, std::vector<struct ACL> *acl_vec) {
    std::vector<struct ::ACL> acls = {{ZOO_PERM_ALL, ZOO_AUTH_IDS},
                        {ZOO_PERM_READ, ZOO_ANYONE_ID_UNSAFE},
                        {ZOO_PERM_CREATE, ZOO_ANYONE_ID_UNSAFE}};
    *acl_vec = acls;
}

static const std::string& ZK_BYTESTORE_USER = "bytestore";

std::string bytestore_acl_cert(const std::string &path, const Stat &stat) {
    int64_t passwd = 0;
    passwd = stat.ctime;
    return ::byte::StringPrint("%s:%d", ZK_BYTESTORE_USER, passwd);
}

bool default_mock_client(const std::string&, std::shared_ptr<BaseMockKeeperClient> *) {
    return false;
}

}  // namespace byte
