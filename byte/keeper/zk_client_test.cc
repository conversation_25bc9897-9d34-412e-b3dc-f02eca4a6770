// Copyright (c) 2023, ByteDance Inc. All rights reserved.

#include "byte/keeper/zk_client.h"

#include <algorithm>
#include <cstddef>
#include <cstring>
#include <map>
#include <memory>
#include <utility>

#include "byte/include/macros.h"
#include "byte/keeper/keeper.h"
#include "byte/keeper/keeper_config.h"
#include "gmock/gmock.h"
#include "gtest/gtest.h"
#include "zookeeper-client-c/include/zookeeper.h"

namespace byte {
namespace {

bool test_mock_client(const std::string& name, std::shared_ptr<BaseMockKeeperClient>* client) {
    byte::KeeperClientConfig config;
    config.mock_creater_ = byte::test_mock_client;
    *client = std::make_shared<BaseMockKeeperClient>(BaseMockKeeperClient());
    return true;
}

TEST(ZKClientTest, InitEmptyAddrList) {
    byte::KeeperClientConfig config;
    config.mock_creater_ = byte::test_mock_client;
    std::unique_ptr<byte::ZKClient> zk_client(new byte::ZKClient(&config));
    EXPECT_EQ(
        ZK_ERR_Code::ZK_ERR_PARAM,
        zk_client->init("", /*watcher=*/nullptr, /*context=*/nullptr, /*log_callback=*/nullptr));
    zk_client->close();
}

TEST(ZKClientTest, InitWithAddrList) {
    byte::KeeperClientConfig config;
    config.mock_creater_ = byte::test_mock_client;
    std::unique_ptr<byte::ZKClient> zk_client(new byte::ZKClient(&config));
    EXPECT_EQ(ZK_ERR_Code::ZK_ERR_ZOO_FAILED,
              zk_client->init("InitWithAddList", /*watcher=*/nullptr, /*context=*/nullptr,
                              /*log_callback=*/nullptr));
    std::string test_node("test_Node");
    EXPECT_EQ(ZK_ERR_Code::ZK_ERR_PARAM, zk_client->node_set_acl(test_node));
    EXPECT_EQ(ZK_ERR_Code::ZK_ERR_PARAM, zk_client->add_auth("test"));
    std::string value("test_value");
    EXPECT_EQ(ZK_ERR_Code::ZK_ERR_PARAM, zk_client->node_create(test_node, value));
    int version(0);
    EXPECT_EQ(ZK_ERR_Code::ZK_ERR_PARAM,
              zk_client->node_set(test_node, value, &version, /*force=*/true));
    EXPECT_EQ(ZK_ERR_Code::ZK_ERR_PARAM,
              zk_client->node_set(test_node, value, &version, /*force=*/false));
    std::string buf("");
    EXPECT_EQ(ZK_ERR_Code::ZK_ERR_PARAM,
              zk_client->node_get(test_node, &buf, /*watcher=*/nullptr, /*context=*/nullptr,
                                  /*log_callback=*/nullptr));
    EXPECT_EQ(ZK_ERR_Code::ZK_ERR_PARAM, zk_client->node_exist(test_node, /*watch=*/0, nullptr));
    EXPECT_EQ(ZK_ERR_Code::ZK_ERR_PARAM,
              zk_client->node_delete(test_node, version, /*recursively=*/true));
    zk_client->close();
}

TEST(ZKClientTest, ConnectionFailureInZKClientInitDeadLockIssue) {
    byte::KeeperClientConfig config;
    config.mock_creater_ = byte::test_mock_client;
    std::unique_ptr<byte::MockInitNotConnectedZKClient> zk_client(
        new byte::MockInitNotConnectedZKClient(&config));
    EXPECT_EQ(ZK_ERR_Code::ZK_ERR_INIT_NOT_CONNECTED,
              zk_client->init("InitWithAddList", /*watcher=*/nullptr, /*context=*/nullptr,
                              /*log_callback=*/nullptr));
    EXPECT_EQ(byte::ZK_ERR_Code::ZK_ERR_INIT_NOT_CONNECTED,
              zk_client->init("test", /*watcher=*/nullptr, /*context=*/nullptr,
                              /*log_callback=*/nullptr));
    EXPECT_EQ(byte::ZK_ERR_Code::ZK_ERR_INIT_NOT_CONNECTED,
              zk_client->init("test", /*watcher=*/nullptr, /*context=*/nullptr,
                              /*log_callback=*/nullptr));
}

TEST(ZKClientTest, DefaultZKInitWatcherNullContextNotCoreDump) {
    byte::KeeperClientConfig config;
    config.mock_creater_ = byte::test_mock_client;
    std::unique_ptr<byte::MockInitConnectedZKClient> zk_client(
        new byte::MockInitConnectedZKClient(&config));
    EXPECT_EQ(ZK_ERR_Code::ZK_OK,
              zk_client->init("InitWithAddList", /*path=*/nullptr, /*context=*/nullptr, nullptr));
}

}  // namespace

// Same globle variable in zk_client.cc
constexpr int kZKDefaultValueSize = 1024;

class MockZooKeeperAPIWrapper : public internal::IZooKeeperAPIWrapper {
public:
    ~MockZooKeeperAPIWrapper() {}
    MOCK_METHOD7(WGet, int(zhandle_t*, const char*, watcher_fn, void*, char*, int*, struct Stat*));
    MOCK_METHOD7(Init, zhandle_t*(const char*, watcher_fn, int, const clientid_t*, void*, int,
                                  log_callback_fn));
    MOCK_METHOD1(Close, int(zhandle_t*));
    MOCK_METHOD8(Create, int(zhandle_t*, const char*, const char*, int, const struct ACL_vector*,
                             int, char*, int));
    MOCK_METHOD5(Set, int(zhandle_t*, const char*, const char*, int, int));
    MOCK_METHOD6(Set2, int(zhandle_t*, const char*, const char*, int, int, struct Stat*));
    MOCK_METHOD3(Delete, int(zhandle_t*, const char*, int));
    MOCK_METHOD4(Exists, int(zhandle_t*, const char*, int, struct Stat*));
    MOCK_METHOD6(AddAuth,
                 int(zhandle_t*, const char*, const char*, int, void_completion_t, const void*));
    MOCK_METHOD4(SetAcl, int(zhandle_t*, const char*, int, const struct ACL_vector*));
    MOCK_METHOD6(GetChildren,
                 int(zhandle_t*, const char*, int, struct String_vector*, watcher_fn, void*));
    MOCK_METHOD2(SetServers, int(zhandle_t*, const char*));
};

using ::testing::_;
using ::testing::Return;

class ZKClientFriendTest : public testing::Test {
public:
    std::unique_ptr<ZKClient> CreateZKClient(MockZooKeeperAPIWrapper* wrapper) {
        const byte::KeeperClientConfig config;
        // zhandle_t is a forward declaration struct from zookeeper.
        // sizeof(zhandle_t) = 1112;
        zhandle_t* zh = reinterpret_cast<zhandle_t*>(malloc(1112));
        memset(zh, 0, 1112);
        std::unique_ptr<ZKClient> zk_client(new ZKClient(&config));
        zk_client->zh_ = zh;
        zk_client->zookeeper_api_wrapper_.reset(wrapper);
        zh_ptr_ = zh;
        return zk_client;
    }
    void CleanupZKHandler(std::unique_ptr<ZKClient> zk_client) {
        free(zk_client->zh_);
        zk_client->zh_ = nullptr;
    }

    void CleanupZKHandlerPtr() {
        free(zh_ptr_);
        zh_ptr_ = nullptr;
    }
    void VerifyZKGetBufferSucceed(int buffer_len_in_zk, int value_str_init_length,
                                  bool is_stat_exist = true);

private:
    zhandle_t* zh_ptr_ = nullptr;
};

TEST_F(ZKClientFriendTest, ZKDefaultWatcherTriggerKeeperConnectedAndReConnectedSuccess) {
    MockZooKeeperAPIWrapper mock = {};
    watcher_fn init_watcher = nullptr;
    EXPECT_CALL(mock, Init(_, _, _, _, _, _, _))
        .WillOnce([&](const char* host, watcher_fn fn, int recv_timeout, const clientid_t* clientid,
                      void* context, int flags, log_callback_fn log_callback) {
            init_watcher = fn;
            return nullptr;
        });
    KeeperClientConfig config;
    std::unique_ptr<ZKClient> zk_client(new ZKClient(&config));
    zk_client->zookeeper_api_wrapper_.reset(&mock);
    zk_client->init("region list", /*watcher=*/nullptr, /*context=*/nullptr,
                    /*log_callback=*/nullptr);

    std::shared_ptr<BaseMockKeeperClient> mock_zk(new BaseMockKeeperClient);
    config.mock_creater_ = [&](const std::string&, std::shared_ptr<BaseMockKeeperClient>* ptr) {
        *ptr = mock_zk;
        return true;
    };
    std::unique_ptr<byte::Keeper> keeper(new byte::Keeper(config));
    keeper->inited_ = true;
    ASSERT_EQ(keeper->connected_, false);
    init_watcher(nullptr, ZOO_SESSION_EVENT, ZOO_CONNECTED_STATE, nullptr, keeper.get());
    ASSERT_EQ(keeper->connected_, true);
    init_watcher(nullptr, ZOO_SESSION_EVENT, ZOO_CONNECTING_STATE, nullptr, keeper.get());
    ASSERT_EQ(keeper->connected_, false);
    keeper->connected_ = true;
    init_watcher(nullptr, ZOO_SESSION_EVENT, ZOO_EXPIRED_SESSION_STATE, nullptr, keeper.get());
    ASSERT_EQ(keeper->connected_, false);

    zk_client->zookeeper_api_wrapper_.release();
}

constexpr const char* kLogMessage = "This is one log test message.";
void ValidateLogCallbackFunc(const char* message) { ASSERT_EQ(message, kLogMessage); }

TEST_F(ZKClientFriendTest, ZKInitLogCallbackSetCorrectly) {
    std::unique_ptr<MockZooKeeperAPIWrapper> mock(new MockZooKeeperAPIWrapper);

    EXPECT_CALL(*mock, Init(_, _, _, _, _, _, _))
        .WillOnce([&](const char*, watcher_fn, int, const clientid_t*, void*, int,
                      log_callback_fn log_callback) {
            EXPECT_EQ(log_callback, nullptr);
            return nullptr;
        });

    const byte::KeeperClientConfig config;
    std::unique_ptr<ZKClient> zk_client(new ZKClient(&config));
    zk_client->zookeeper_api_wrapper_.reset(mock.get());

    EXPECT_EQ(zk_client->init("ZKInitLogCallbackSetCorrectly", /*watcher=*/nullptr,
                              /*context=*/nullptr, /*log_callback=*/nullptr),
              ZK_ERR_Code::ZK_ERR_ZOO_FAILED);

    EXPECT_CALL(*mock, Init(_, _, _, _, _, _, _))
        .WillOnce([&](const char*, watcher_fn, int, const clientid_t*, void*, int,
                      log_callback_fn log_callback) {
            EXPECT_EQ(log_callback, ValidateLogCallbackFunc);
            ValidateLogCallbackFunc(kLogMessage);
            return nullptr;
        });

    EXPECT_EQ(zk_client->init("ZKInitLogCallbackSetCorrectly", /*watcher=*/nullptr,
                              /*context=*/nullptr, ValidateLogCallbackFunc),
              ZK_ERR_Code::ZK_ERR_ZOO_FAILED);
    zk_client->zookeeper_api_wrapper_.release();
}

TEST_F(ZKClientFriendTest, ZKCreateReturnsCorrectCodes) {
    std::map<int, ZK_ERR_Code> expected_result_map = {
        {ZOK, ZK_ERR_Code::ZK_OK},
        {ZNONODE, ZK_ERR_Code::ZK_ERR_ZOO_FAILED},
        {ZNOAUTH, ZK_ERR_Code::ZK_ERR_ZOO_AUTH_FAILED},
        {ZBADARGUMENTS, ZK_ERR_Code::ZK_ERR_PARAM},
        {ZINVALIDSTATE, ZK_ERR_Code::ZK_ERR_ZOO_FAILED},
        {ZMARSHALLINGERROR, ZK_ERR_Code::ZK_ERR_ZOO_FAILED},
        // 999 is one random input which falls through the default branch.
        {999, ZK_ERR_Code::ZK_ERR_ZOO_FAILED}};

    for (const auto& expected_result : expected_result_map) {
        std::unique_ptr<MockZooKeeperAPIWrapper> mock(new MockZooKeeperAPIWrapper);

        EXPECT_CALL(*mock, Create(_, _, _, _, _, _, _, _)).WillRepeatedly([&](...) {
            if (expected_result.first == ZNONODE) {
                // 999 is one random input which falls through the default branch.
                return 999;
            }
            return expected_result.first;
        });

        std::unique_ptr<ZKClient> zk_client = CreateZKClient(mock.release());

        EXPECT_EQ(zk_client->node_create("/parent_path/test", ""), expected_result.second);
        CleanupZKHandler(std::move(zk_client));
    }
}

TEST_F(ZKClientFriendTest, ZKNodeSetReturnsCorrectCodes) {
    std::map<int, ZK_ERR_Code> expected_result_map = {
        {ZOK, ZK_ERR_Code::ZK_OK},
        {ZNONODE, ZK_ERR_Code::ZK_OK},
        {ZBADVERSION, ZK_ERR_Code::ZK_ERR_BAD_VERSION},
        {ZNOAUTH, ZK_ERR_Code::ZK_ERR_ZOO_AUTH_FAILED},
        {ZBADARGUMENTS, ZK_ERR_Code::ZK_ERR_PARAM},
        {ZINVALIDSTATE, ZK_ERR_Code::ZK_ERR_ZOO_FAILED},
        {ZMARSHALLINGERROR, ZK_ERR_Code::ZK_ERR_ZOO_FAILED},
        // 999 is one random input which falls through the default branch.
        {999, ZK_ERR_Code::ZK_ERR_ZOO_FAILED}};

    int version = 0;
    for (const auto& expected_result : expected_result_map) {
        std::unique_ptr<MockZooKeeperAPIWrapper> mock(new MockZooKeeperAPIWrapper);

        EXPECT_CALL(*mock, Create(_, _, _, _, _, _, _, _)).WillRepeatedly([&](...) { return 0; });
        EXPECT_CALL(*mock, Set(_, _, _, _, _)).WillRepeatedly([&](...) {
            return expected_result.first;
        });
        EXPECT_CALL(*mock, Set2(_, _, _, _, _, _)).WillRepeatedly([&](...) {
            return expected_result.first;
        });

        std::unique_ptr<ZKClient> zk_client = CreateZKClient(mock.release());
        EXPECT_EQ(zk_client->node_set("/parent_path/test", "", &version, (++version) % 2),
                  expected_result.second);
        CleanupZKHandler(std::move(zk_client));
    }
}

TEST_F(ZKClientFriendTest, ZKNodeSetACLReturnsCorrectCodes) {
    std::map<int, ZK_ERR_Code> expected_result_map = {
        {ZOK, ZK_ERR_Code::ZK_OK},
        {ZNONODE, ZK_ERR_Code::ZK_ERR_ZOO_NOT_EXIST},
        {ZBADVERSION, ZK_ERR_Code::ZK_ERR_BAD_VERSION},
        {ZNOAUTH, ZK_ERR_Code::ZK_ERR_ZOO_AUTH_FAILED},
        {ZBADARGUMENTS, ZK_ERR_Code::ZK_ERR_PARAM},
        {ZINVALIDSTATE, ZK_ERR_Code::ZK_ERR_ZOO_FAILED},
        {ZMARSHALLINGERROR, ZK_ERR_Code::ZK_ERR_ZOO_FAILED},
        // 999 is one random input which falls through the default branch.
        {999, ZK_ERR_Code::ZK_ERR_ZOO_FAILED}};

    for (const auto& expected_result : expected_result_map) {
        std::unique_ptr<MockZooKeeperAPIWrapper> mock(new MockZooKeeperAPIWrapper);

        EXPECT_CALL(*mock, SetAcl(_, _, _, _)).WillRepeatedly([&](...) {
            return expected_result.first;
        });

        std::unique_ptr<ZKClient> zk_client = CreateZKClient(mock.release());
        EXPECT_EQ(zk_client->node_set_acl("/parent_path/test"), expected_result.second);
        CleanupZKHandler(std::move(zk_client));
    }
}

TEST_F(ZKClientFriendTest, ZKTryUpdateServerReturnsCorrectCodes) {
    std::map<int, bool> expected_result_map = {
        {ZOK, true},
        {ZNOAUTH, false},
        // 999 is one random input which falls through the default branch.
        {999, false}};

    for (const auto& expected_result : expected_result_map) {
        std::unique_ptr<MockZooKeeperAPIWrapper> mock(new MockZooKeeperAPIWrapper);

        EXPECT_CALL(*mock, SetServers(_, _)).WillRepeatedly([&](...) {
            return expected_result.first;
        });

        std::unique_ptr<ZKClient> zk_client = CreateZKClient(mock.release());
        EXPECT_EQ(zk_client->try_update_server("/parent_path/test"), expected_result.second);
        CleanupZKHandler(std::move(zk_client));
    }
}

TEST_F(ZKClientFriendTest, ZKDeleteReturnsCorrectCodes) {
    std::map<int, ZK_ERR_Code> expected_result_map = {
        {ZOK, ZK_ERR_Code::ZK_OK},
        {ZNOTEMPTY, ZK_ERR_Code::ZK_ERR_ZOO_NOTEMPTY},
        {ZBADVERSION, ZK_ERR_Code::ZK_ERR_BAD_VERSION},
        {ZNOAUTH, ZK_ERR_Code::ZK_ERR_ZOO_AUTH_FAILED},
        {ZBADARGUMENTS, ZK_ERR_Code::ZK_ERR_PARAM},
        {ZINVALIDSTATE, ZK_ERR_Code::ZK_ERR_ZOO_FAILED},
        {ZMARSHALLINGERROR, ZK_ERR_Code::ZK_ERR_ZOO_FAILED},
        // 999 is one random input which falls through the default branch.
        {999, ZK_ERR_Code::ZK_ERR_ZOO_FAILED}};

    for (const auto& expected_result : expected_result_map) {
        std::unique_ptr<MockZooKeeperAPIWrapper> mock(new MockZooKeeperAPIWrapper);

        EXPECT_CALL(*mock, Delete(_, _, _)).WillRepeatedly([&](...) {
            return expected_result.first;
        });

        std::unique_ptr<ZKClient> zk_client = CreateZKClient(mock.release());
        EXPECT_EQ(zk_client->node_delete("/parent_path/test", 0, false), expected_result.second);
        CleanupZKHandler(std::move(zk_client));
    }
}

TEST_F(ZKClientFriendTest, ZkExistReturnsCorrectCodes) {
    // Define a map to store expected results
    std::map<int, ZK_ERR_Code> expected_result_map = {
        {ZOK, ZK_ERR_Code::ZK_OK},
        {ZNONODE, ZK_ERR_Code::ZK_ERR_ZOO_NOT_EXIST},
        {ZNOAUTH, ZK_ERR_Code::ZK_ERR_ZOO_AUTH_FAILED},
        {ZBADARGUMENTS, ZK_ERR_Code::ZK_ERR_PARAM},
        {ZINVALIDSTATE, ZK_ERR_Code::ZK_ERR_ZOO_FAILED},
        {ZMARSHALLINGERROR, ZK_ERR_Code::ZK_ERR_ZOO_FAILED},
        // 999 is one random input which falls through the default branch.
        {999, ZK_ERR_Code::ZK_ERR_ZOO_FAILED}};

    // Try each call result
    for (const auto& expected_result : expected_result_map) {
        std::unique_ptr<MockZooKeeperAPIWrapper> mock(new MockZooKeeperAPIWrapper);

        EXPECT_CALL(*mock, Exists(_, _, _, _)).WillRepeatedly([&](...) {
            return expected_result.first;
        });

        std::unique_ptr<ZKClient> zk_client = CreateZKClient(mock.release());

        // Assert the state after the operation
        EXPECT_EQ(zk_client->node_exist("", 0, nullptr), expected_result.second);
        CleanupZKHandler(std::move(zk_client));
    }
}

TEST_F(ZKClientFriendTest, ZkAddAuthReturnsCorrectCodes) {
    // Define a map to store expected results
    std::map<int, ZK_ERR_Code> expected_result_map = {
        {ZOK, ZK_ERR_Code::ZK_OK},
        {ZBADARGUMENTS, ZK_ERR_Code::ZK_ERR_PARAM},
        {ZINVALIDSTATE, ZK_ERR_Code::ZK_ERR_ZOO_FAILED},
        {ZMARSHALLINGERROR, ZK_ERR_Code::ZK_ERR_ZOO_FAILED},
        // 999 is one random input which falls through the default branch.
        {999, ZK_ERR_Code::ZK_ERR_ZOO_FAILED}};

    // Try each call result
    for (const auto& expected_result : expected_result_map) {
        std::unique_ptr<MockZooKeeperAPIWrapper> mock(new MockZooKeeperAPIWrapper);

        EXPECT_CALL(*mock, Exists(_, _, _, _)).WillRepeatedly([&](...) { return 0; });

        EXPECT_CALL(*mock, AddAuth(_, _, _, _, _, _)).WillRepeatedly([&](...) {
            return expected_result.first;
        });

        std::unique_ptr<ZKClient> zk_client = CreateZKClient(mock.release());

        // Assert the state after the operation
        EXPECT_EQ(zk_client->add_auth(""), expected_result.second);
        CleanupZKHandler(std::move(zk_client));
    }
}

TEST_F(ZKClientFriendTest, ZKCloseFunc) {
    {
        std::unique_ptr<MockZooKeeperAPIWrapper> mock(new MockZooKeeperAPIWrapper);

        EXPECT_CALL(*mock, Close(_)).WillRepeatedly([&](...) { return 0; });

        std::unique_ptr<ZKClient> zk_client = CreateZKClient(mock.release());
        zk_client->close();
        CleanupZKHandlerPtr();
    }
    {
        std::unique_ptr<MockZooKeeperAPIWrapper> mock(new MockZooKeeperAPIWrapper);

        EXPECT_CALL(*mock, Close(_)).WillRepeatedly([&](...) { return -1; });

        std::unique_ptr<ZKClient> zk_client = CreateZKClient(mock.release());
        zk_client->close();
        CleanupZKHandlerPtr();
    }
}

void ZKClientFriendTest::VerifyZKGetBufferSucceed(int buffer_len_in_zk, int actual_value_init_len,
                                                  bool is_stat_exist) {
    std::string buffer_str_in_zk(buffer_len_in_zk, 'a');
    char* buffer_array_in_zk = &buffer_str_in_zk[0];
    int call_times = 0;
    struct Stat stat_in_zk;
    stat_in_zk.dataLength = buffer_str_in_zk.length();

    std::unique_ptr<MockZooKeeperAPIWrapper> mock(new MockZooKeeperAPIWrapper);
    EXPECT_CALL(*mock, WGet(_, _, _, _, _, _, _))
        .WillRepeatedly([&](zhandle_t* zh, const char* path, watcher_fn watcher, void* watcher_ctx,
                            char* buffer, int* buffer_len, struct Stat* stat) {
            // zoo_wget will fill the data at most buffer_len when it was called.
            *buffer_len = std::min(stat_in_zk.dataLength, *buffer_len);
            memcpy(buffer, buffer_array_in_zk, *buffer_len);
            *stat = stat_in_zk;
            ++call_times;
            return 0;
        });

    std::unique_ptr<ZKClient> zk_client = CreateZKClient(mock.release());

    std::string actual_value;
    if (actual_value_init_len > 0) {
        actual_value.resize(actual_value_init_len);
    }
    struct Stat actual_stat;
    LOG(INFO) << "actual_value_init_len:" << actual_value_init_len
              << ", buffer_len_in_zk: " << buffer_len_in_zk;

    call_times = 0;
    if (is_stat_exist) {
        ASSERT_EQ(ZK_ERR_Code::ZK_OK,
                  zk_client->node_get("/my_node", &actual_value, nullptr, nullptr, &actual_stat));
    } else {
        ASSERT_EQ(ZK_ERR_Code::ZK_OK,
                  zk_client->node_get("/my_node", &actual_value, /*watcher=*/nullptr,
                                      /*watcher_ctx=*/nullptr, /*stat=*/nullptr));
    }

    if (buffer_len_in_zk == 0 || actual_value_init_len >= buffer_len_in_zk ||
        (actual_value_init_len == 0 && buffer_len_in_zk <= kZKDefaultValueSize)) {
        // If expected buffer length value <= 0, caller passed buffer_len >= expected value length
        // or expected buffer length is smaller than value string buffer by default, we only call
        // the zoo_wget function once.
        ASSERT_EQ(call_times, 1);
    } else {
        // If caller passed buffer_len < expected value length, we only call the
        // zoo_wget function twice.
        ASSERT_EQ(call_times, 2);
    }
    ASSERT_EQ(buffer_array_in_zk, actual_value);
    if (is_stat_exist) {
        ASSERT_EQ(stat_in_zk.dataLength, actual_stat.dataLength);
    }
    CleanupZKHandler(std::move(zk_client));
}

TEST_F(ZKClientFriendTest, ZKGetUpTo10MBBufferSucceed) {
    // This function verifies that regardless of the initial value size, the actual value can obtain
    // accurate data when the buffer data in zk is within the range of 0 to 10MB in size.
    for (int buffer_len_in_zk :
         {0, kZKDefaultValueSize, kZKDefaultValueSize + 1, kZKDefaultValueSize - 1,
          /*10M=*/kZKDefaultValueSize * 10 * 1024}) {
        VerifyZKGetBufferSucceed(buffer_len_in_zk, /*value_str_init_length=*/0);
        VerifyZKGetBufferSucceed(buffer_len_in_zk, buffer_len_in_zk - 1);
        VerifyZKGetBufferSucceed(buffer_len_in_zk, buffer_len_in_zk);
        VerifyZKGetBufferSucceed(buffer_len_in_zk, buffer_len_in_zk + 1);
        VerifyZKGetBufferSucceed(buffer_len_in_zk, kZKDefaultValueSize - 1);
        VerifyZKGetBufferSucceed(buffer_len_in_zk, kZKDefaultValueSize);
        VerifyZKGetBufferSucceed(buffer_len_in_zk, kZKDefaultValueSize + 1);
    }
}

TEST_F(ZKClientFriendTest, ZKGetUpTo10MBBufferSucceedWithNullStat) {
    // This function verifies that regardless of the initial value size, the actual value can obtain
    // accurate data when the buffer data in zk is within the range of 0 to 10MB in size.
    for (int buffer_len_in_zk :
         {0, kZKDefaultValueSize, kZKDefaultValueSize + 1, kZKDefaultValueSize - 1,
          /*10M=*/kZKDefaultValueSize * 10 * 1024}) {
        VerifyZKGetBufferSucceed(buffer_len_in_zk, /*value_str_init_length=*/0,
                                 /*is_stat_exist*/ false);
        VerifyZKGetBufferSucceed(buffer_len_in_zk, buffer_len_in_zk - 1, /*is_stat_exist*/ false);
        VerifyZKGetBufferSucceed(buffer_len_in_zk, buffer_len_in_zk, /*is_stat_exist*/ false);
        VerifyZKGetBufferSucceed(buffer_len_in_zk, buffer_len_in_zk + 1, /*is_stat_exist*/ false);
        VerifyZKGetBufferSucceed(buffer_len_in_zk, kZKDefaultValueSize - 1,
                                 /*is_stat_exist*/ false);
        VerifyZKGetBufferSucceed(buffer_len_in_zk, kZKDefaultValueSize, /*is_stat_exist*/ false);
        VerifyZKGetBufferSucceed(buffer_len_in_zk, kZKDefaultValueSize + 1,
                                 /*is_stat_exist*/ false);
    }
}

TEST_F(ZKClientFriendTest, ZKGetBufferNegLengthFail) {
    std::string buffer_str_in_zk("abcdefg");
    int call_times = 0;
    struct Stat stat_in_zk;
    stat_in_zk.dataLength = buffer_str_in_zk.length();

    std::unique_ptr<MockZooKeeperAPIWrapper> mock(new MockZooKeeperAPIWrapper);
    EXPECT_CALL(*mock, WGet(_, _, _, _, _, _, _))
        .WillRepeatedly([&](zhandle_t* zh, const char* path, watcher_fn watcher, void* watcher_ctx,
                            char* buffer, int* buffer_len, struct Stat* stat) {
            // Simulate zoo_wget get NULL data.
            *buffer_len = -1;
            *stat = stat_in_zk;
            ++call_times;
            return 0;
        });

    std::unique_ptr<ZKClient> zk_client = CreateZKClient(mock.release());

    std::string actual_value;
    struct Stat acutal_stat;

    ASSERT_EQ(ZK_ERR_Code::ZK_OK,
              zk_client->node_get("/my_node", &actual_value, nullptr, nullptr, &acutal_stat));
    ASSERT_EQ("", actual_value);
    ASSERT_EQ(call_times, 1);
    ASSERT_EQ(stat_in_zk.dataLength, acutal_stat.dataLength);
    CleanupZKHandler(std::move(zk_client));
}

TEST_F(ZKClientFriendTest, ZKGetBufferErrnoFail) {
    ZK_ERR_Code error = ZK_ERR_Code::ZK_OK;

    std::unique_ptr<MockZooKeeperAPIWrapper> mock(new MockZooKeeperAPIWrapper);
    EXPECT_CALL(*mock, WGet(_, _, _, _, _, _, _))
        .WillRepeatedly([&](zhandle_t* zh, const char* path, watcher_fn watcher, void* watcher_ctx,
                            char* buffer, int* buffer_len, struct Stat* stat) {
            // Simulate the zoo_wget will return error codes;
            switch (error) {
                case ZK_ERR_Code::ZK_OK:
                    return ZOK;
                case ZK_ERR_Code::ZK_ERR_ZOO_ALREADY_EXIST:
                    return ZNODEEXISTS;
                case ZK_ERR_Code::ZK_ERR_ZOO_NOT_EXIST:
                    return ZNONODE;
                case ZK_ERR_Code::ZK_ERR_ZOO_AUTH_FAILED:
                    return ZNOAUTH;
                case ZK_ERR_Code::ZK_ERR_PARAM:
                    return ZBADARGUMENTS;
                case ZK_ERR_Code::ZK_ERR_ZOO_FAILED:
                    return ZMARSHALLINGERROR;
                default:
                    return ZSYSTEMERROR;
            }
        });

    std::unique_ptr<ZKClient> zk_client = CreateZKClient(mock.release());

    std::string actual_value;
    struct Stat actual_stat;

    for (ZK_ERR_Code error_code :
         {ZK_ERR_Code::ZK_ERR_ZOO_NOT_EXIST, ZK_ERR_Code::ZK_ERR_ZOO_AUTH_FAILED,
          ZK_ERR_Code::ZK_ERR_PARAM, ZK_ERR_Code::ZK_ERR_ZOO_FAILED}) {
        error = error_code;
        ASSERT_EQ(error,
                  zk_client->node_get("/my_node", &actual_value, nullptr, nullptr, &actual_stat));
    }
    CleanupZKHandler(std::move(zk_client));
}

TEST_F(ZKClientFriendTest, ZKGetBufferFirstCallFailSecondCallSucceed) {
    int call_times = 0;
    int buffer_len_in_zk = 10;
    std::string buffer_str_in_zk(buffer_len_in_zk, 'a');
    char* buffer_array_in_zk = &buffer_str_in_zk[0];
    struct Stat stat_in_zk;
    stat_in_zk.dataLength = buffer_str_in_zk.length();
    std::unique_ptr<MockZooKeeperAPIWrapper> mock(new MockZooKeeperAPIWrapper);

    EXPECT_CALL(*mock, WGet(_, _, _, _, _, _, _))
        .WillRepeatedly([&](zhandle_t* zh, const char* path, watcher_fn watcher, void* watcher_ctx,
                            char* buffer, int* buffer_len, struct Stat* stat) {
            ++call_times;
            if (call_times == 1) {
                // ZINVALIDSTATE means zk_get will retry.
                return ZINVALIDSTATE;
            } else {
                *buffer_len = std::min(stat_in_zk.dataLength, *buffer_len);
                memcpy(buffer, buffer_array_in_zk, *buffer_len);
                *stat = stat_in_zk;
                return ZOK;
            }
        });

    std::unique_ptr<ZKClient> zk_client = CreateZKClient(mock.release());

    std::string actual_value;
    struct Stat actual_stat;

    ASSERT_EQ(ZK_ERR_Code::ZK_OK,
              zk_client->node_get("/my_node", &actual_value, nullptr, nullptr, &actual_stat));

    ASSERT_EQ(call_times, 2);
    ASSERT_EQ(buffer_array_in_zk, actual_value);
    ASSERT_EQ(stat_in_zk.dataLength, actual_stat.dataLength);
    CleanupZKHandler(std::move(zk_client));
}

size_t& GetRefCnt() {
    static size_t ref_cnt = 0;
    return ref_cnt;
}

class MockIZKRefHolder final : public internal::IZKRefHolder {
public:
    explicit MockIZKRefHolder(zhandle_t*) { ++GetRefCnt(); }
    ~MockIZKRefHolder() override { --GetRefCnt(); }
};

TEST_F(ZKClientFriendTest, SetRefHolderOnce) {
    std::unique_ptr<MockZooKeeperAPIWrapper> mock_api(new MockZooKeeperAPIWrapper);
    std::unique_ptr<MockIZKRefHolder> mock(new MockIZKRefHolder(nullptr));
    EXPECT_CALL(*mock_api, Close(_)).Times(1);
    std::unique_ptr<ZKClient> zk_client = CreateZKClient(mock_api.release());
    zk_client->ref_holder_.reset(mock.release());
    ASSERT_EQ(GetRefCnt(), 1);
    zk_client->close();
    ASSERT_EQ(zk_client->is_closed_, true);
    ASSERT_EQ(GetRefCnt(), 1);
    CleanupZKHandler(std::move(zk_client));
    zk_client.reset();
    ASSERT_EQ(GetRefCnt(), 0);
}

TEST(ZKStateToStringTest, ReturnsCorrectStrings) {
    // Define a map to store expected results
    std::map<int, std::string> expected_state_strings = {
        {ZOO_CONNECTING_STATE, "CONNECTING_STATE"},
        {ZOO_ASSOCIATING_STATE, "ASSOCIATING_STATE"},
        {ZOO_CONNECTED_STATE, "CONNECTED_STATE"},
        {ZOO_EXPIRED_SESSION_STATE, "EXPIRED_SESSION_STATE"},
        {ZOO_AUTH_FAILED_STATE, "AUTH_FAILED_STATE"},
        // 999 is one random input which falls through the default branch.
        {999, "UNKNOWN_STATE"}};

    // Test ZKStateToString for all states in the map
    for (const auto& keyValue : expected_state_strings) {
        EXPECT_EQ(ZKStateToString(keyValue.first), keyValue.second);
    }
}

TEST(ZKTypeToStringTest, ReturnsCorrectStrings) {
    // Define a map to store expected results
    std::map<int, std::string> expected_type_strings = {
        {ZOO_CREATED_EVENT, "CREATED_EVENT"},
        {ZOO_DELETED_EVENT, "DELETED_EVENT"},
        {ZOO_CHANGED_EVENT, "CHANGED_EVENT"},
        {ZOO_CHILD_EVENT, "CHILD_EVENT"},
        {ZOO_SESSION_EVENT, "SESSION_EVENT"},
        {ZOO_NOTWATCHING_EVENT, "NOTWATCHING_EVENT"},
        // 999 is one random input which falls through the default branch.
        {999, "UNKNOWN_TYPE"}};

    // Test ZKTypeToString for all types in the map
    for (const auto& keyValue : expected_type_strings) {
        EXPECT_EQ(ZKTypeToString(keyValue.first), keyValue.second);
    }
}

}  // namespace byte
