// Copyright (c) 2018-present, ByteDance Inc. All rights reserved.
#include <pthread.h>

#include <vector>

#include "byte/memory/object_cache.h"
#include "byte/include/byte_log.h"
#include "byte/thread/this_thread.h"
#include "gtest/gtest.h"

namespace byte {

// sizeof(ObjectCacheTestA) = 16
class ObjectCacheTestA {
public:
    explicit ObjectCacheTestA(int32_t a = 0, int32_t b = 2, double c = 3.14)
        : a_(a), b_(b), c_(c) {
        LOG(INFO) <<  "ObjectCacheTestA construct";
    }
    ~ObjectCacheTestA() {
        LOG(INFO) <<  "ObjectCacheTestA destroy";
    }

private:
    int32_t a_;
    int32_t b_;
    double  c_;
};

// sizeof(ObjectCacheTestB) = 48
class ObjectCacheTestB {
public:
    ObjectCacheTestB() {}
    ~ObjectCacheTestB() {}

private:
    int64_t a_[6];
};

// sizeof(ObjectCacheTestC) = 4
class ObjectCacheTestC {
public:
    explicit ObjectCacheTestC(int32_t a = 0) : a_(a) {}
    ~ObjectCacheTestC() {}

private:
    int32_t a_;
};

// sizeof(ObjectCacheTestD) = 4104
class ObjectCacheTestD {
public:
    ObjectCacheTestD() {}
    ~ObjectCacheTestD() {}

private:
    uint64_t array[513];
};

void* ObjectCacheTestFunc1(void* args) {
    auto oc = static_cast<ObjectCache<ObjectCacheTestA>*>(args);
    std::vector<ObjectCacheTestA*> list;

    ObjectCacheOptions options;
    options.name_ = "multicache oc1";
    oc->Init(options);

    for (uint32_t i = 0; i < 255; i++) {
        auto p = oc->Alloc();
        EXPECT_TRUE(p);
        list.push_back(p);
    }

    EXPECT_EQ(oc->Size(), 8192);
    EXPECT_EQ(oc->EmptyListCnt(), 0);
    EXPECT_EQ(oc->FullListCnt(), 1);
    EXPECT_EQ(oc->PartialListCnt(), 1);

    return NULL;
}

void* ObjectCacheTestFunc2(void* args) {
    auto oc = static_cast<ObjectCache<ObjectCacheTestB>*>(args);
    std::vector<ObjectCacheTestB*> list;

    ObjectCacheOptions options;
    options.name_ = "multicache oc2";
    oc->Init(options);

    for (uint32_t i = 0; i < 169; i++) {
        auto p = oc->Alloc();
        EXPECT_TRUE(p);
        list.push_back(p);
    }

    EXPECT_EQ(oc->Size(), 12288);
    EXPECT_EQ(oc->EmptyListCnt(), 0);
    EXPECT_EQ(oc->FullListCnt(), 2);
    EXPECT_EQ(oc->PartialListCnt(), 1);
    return NULL;
}

void* ObjectCacheTestFunc3(void* args) {
    auto oc = static_cast<ObjectCache<ObjectCacheTestC>*>(args);
    std::vector<ObjectCacheTestC*> list;

    ObjectCacheOptions options;
    options.name_ = "multicache oc3";
    oc->Init(options);

    for (uint32_t i = 0; i < 10; i++) {
        auto p = oc->Alloc();
        EXPECT_TRUE(p);
        list.push_back(p);
    }

    EXPECT_EQ(oc->Size(), 4096);
    EXPECT_EQ(oc->EmptyListCnt(), 0);
    EXPECT_EQ(oc->FullListCnt(), 0);
    EXPECT_EQ(oc->PartialListCnt(), 1);

    return NULL;
}

TEST(ObjectCacheTest, BasicFunction) {
    ObjectCache<ObjectCacheTestA>  oc;
    std::vector<ObjectCacheTestA*> list;

    EXPECT_EQ(oc.Size(), 0);
    EXPECT_EQ(oc.EmptyListCnt(), 0);
    EXPECT_EQ(oc.FullListCnt(), 0);
    EXPECT_EQ(oc.PartialListCnt(), 0);

    for (uint32_t i = 0; i < 253; i++) {
        auto p = oc.Alloc();
        EXPECT_TRUE(p);
        list.push_back(p);
        EXPECT_EQ(oc.Size(), 4096);
        EXPECT_EQ(oc.EmptyListCnt(), 0);
        EXPECT_EQ(oc.FullListCnt(), 0);
        EXPECT_EQ(oc.PartialListCnt(), 1);
    }

    auto p = oc.Alloc();
    EXPECT_TRUE(p);
    list.push_back(p);
    EXPECT_EQ(oc.Size(), 4096);
    EXPECT_EQ(oc.EmptyListCnt(), 0);
    EXPECT_EQ(oc.FullListCnt(), 1);
    EXPECT_EQ(oc.PartialListCnt(), 0);

    p = oc.Alloc();
    EXPECT_TRUE(p);
    list.push_back(p);
    EXPECT_EQ(oc.Size(), 8192);
    EXPECT_EQ(oc.EmptyListCnt(), 0);
    EXPECT_EQ(oc.FullListCnt(), 1);
    EXPECT_EQ(oc.PartialListCnt(), 1);

    oc.Free(list[254]);
    EXPECT_EQ(oc.Size(), 8192);
    EXPECT_EQ(oc.EmptyListCnt(), 1);
    EXPECT_EQ(oc.FullListCnt(), 1);
    EXPECT_EQ(oc.PartialListCnt(), 0);

    for (uint32_t i = 1; i < list.size() - 1; i++) {
        oc.Free(list[i]);
        EXPECT_EQ(oc.Size(), 8192);
        EXPECT_EQ(oc.EmptyListCnt(), 1);
        EXPECT_EQ(oc.FullListCnt(), 0);
        EXPECT_EQ(oc.PartialListCnt(), 1);
    }

    oc.Free(list[0]);
    EXPECT_EQ(oc.Size(), 8192);
    EXPECT_EQ(oc.EmptyListCnt(), 2);
    EXPECT_EQ(oc.FullListCnt(), 0);
    EXPECT_EQ(oc.PartialListCnt(), 0);

    p = oc.New();
    EXPECT_TRUE(p);
    EXPECT_EQ(oc.Size(), 8192);
    EXPECT_EQ(oc.EmptyListCnt(), 1);
    EXPECT_EQ(oc.FullListCnt(), 0);
    EXPECT_EQ(oc.PartialListCnt(), 1);
    oc.Delete(p);
}

TEST(ObjectCacheTest, CleanEmptyPage) {
    ObjectCache<ObjectCacheTestB>  oc;
    std::vector<ObjectCacheTestB*> list;

    ObjectCacheOptions options;
    options.name_ = "clean empty page oc";
    options.empty_pages_limit_ = 3;
    oc.Init(options);

    for (uint32_t i = 0; i < 337; i++) {
        auto p = oc.Alloc();
        EXPECT_TRUE(p);
        list.push_back(p);
    }

    EXPECT_EQ(oc.Size(), 20480);
    EXPECT_EQ(oc.EmptyListCnt(), 0);
    EXPECT_EQ(oc.FullListCnt(), 4);
    EXPECT_EQ(oc.PartialListCnt(), 1);

    for (uint32_t i = 336; i >= 84; i--)
        oc.Free(list[i]);

    EXPECT_EQ(oc.Size(), 16384);
    EXPECT_EQ(oc.EmptyListCnt(), 3);
    EXPECT_EQ(oc.FullListCnt(), 1);
    EXPECT_EQ(oc.PartialListCnt(), 0);

    for (uint32_t i = 0; i < 84; i++)
        oc.Free(list[i]);

    EXPECT_EQ(oc.Size(), 12288);
    EXPECT_EQ(oc.EmptyListCnt(), 3);
    EXPECT_EQ(oc.FullListCnt(), 0);
    EXPECT_EQ(oc.PartialListCnt(), 0);
}

TEST(ObjectCacheTest, SmallObject) {
    ObjectCache<ObjectCacheTestC>  oc;
    std::vector<ObjectCacheTestC*> list;

    for (uint32_t i = 0; i < 508; i++) {
        auto p = oc.Alloc();
        EXPECT_TRUE(p);
        list.push_back(p);
    }

    EXPECT_EQ(oc.Size(), 4096);
    EXPECT_EQ(oc.EmptyListCnt(), 0);
    EXPECT_EQ(oc.FullListCnt(), 1);
    EXPECT_EQ(oc.PartialListCnt(), 0);

    auto p = oc.Alloc();
    EXPECT_TRUE(p);
    list.push_back(p);

    EXPECT_EQ(oc.Size(), 8192);
    EXPECT_EQ(oc.EmptyListCnt(), 0);
    EXPECT_EQ(oc.FullListCnt(), 1);
    EXPECT_EQ(oc.PartialListCnt(), 1);

    for (uint32_t i = 0; i < 509; i++)
        oc.Free(list[i]);

    EXPECT_EQ(oc.Size(), 8192);
    EXPECT_EQ(oc.EmptyListCnt(), 2);
    EXPECT_EQ(oc.FullListCnt(), 0);
    EXPECT_EQ(oc.PartialListCnt(), 0);
}

TEST(ObjectCacheTest, LargeObject) {
    ObjectCache<ObjectCacheTestD>  oc;
    std::vector<ObjectCacheTestD*> list;

    auto p = oc.Alloc();
    EXPECT_TRUE(p);
    list.push_back(p);
    EXPECT_EQ(oc.Size(), 8192);
    EXPECT_EQ(oc.EmptyListCnt(), 0);
    EXPECT_EQ(oc.FullListCnt(), 1);
    EXPECT_EQ(oc.PartialListCnt(), 0);

    for (int i = 0; i < 9; i++) {
        auto p = oc.Alloc();
        EXPECT_TRUE(p);
        list.push_back(p);
    }

    EXPECT_EQ(oc.Size(), 81920);
    EXPECT_EQ(oc.EmptyListCnt(), 0);
    EXPECT_EQ(oc.FullListCnt(), 10);
    EXPECT_EQ(oc.PartialListCnt(), 0);

    for (int i = 0; i < 10; i++) {
        oc.Free(list[i]);
        EXPECT_LE(oc.EmptyListCnt(), 2);
    }

    EXPECT_EQ(oc.Size(), 16384);
    EXPECT_EQ(oc.EmptyListCnt(), 2);
    EXPECT_EQ(oc.FullListCnt(), 0);
    EXPECT_EQ(oc.PartialListCnt(), 0);
}

TEST(ObjectCacheTest, OddPageSize) {
    ObjectCache<ObjectCacheTestA, 517>  oc1;
    std::vector<ObjectCacheTestA*> list1;

    for (int i = 0; i < 62; i++) {
        auto p = oc1.Alloc();
        EXPECT_TRUE(p);
        list1.push_back(p);
    }

    EXPECT_EQ(oc1.Size(), 1024);
    EXPECT_EQ(oc1.EmptyListCnt(), 0);
    EXPECT_EQ(oc1.FullListCnt(), 1);
    EXPECT_EQ(oc1.PartialListCnt(), 0);

    auto p = oc1.Alloc();
    EXPECT_TRUE(p);
    list1.push_back(p);
    EXPECT_EQ(oc1.Size(), 2048);
    EXPECT_EQ(oc1.EmptyListCnt(), 0);
    EXPECT_EQ(oc1.FullListCnt(), 1);
    EXPECT_EQ(oc1.PartialListCnt(), 1);

    ObjectCache<ObjectCacheTestB, 4097>  oc2;
    std::vector<ObjectCacheTestB*> list2;

    for (int i = 0; i < 170; i++) {
        auto p = oc2.Alloc();
        EXPECT_TRUE(p);
        list2.push_back(p);
    }

    EXPECT_EQ(oc2.Size(), 8192);
    EXPECT_EQ(oc2.EmptyListCnt(), 0);
    EXPECT_EQ(oc2.FullListCnt(), 1);
    EXPECT_EQ(oc2.PartialListCnt(), 0);

    auto q = oc2.Alloc();
    EXPECT_TRUE(q);
    list2.push_back(q);
    EXPECT_EQ(oc2.Size(), 16384);
    EXPECT_EQ(oc2.EmptyListCnt(), 0);
    EXPECT_EQ(oc2.FullListCnt(), 1);
    EXPECT_EQ(oc2.PartialListCnt(), 1);

    for (int i = 0; i < 63; i++)
        oc1.Free(list1[i]);

    for (int i = 0; i < 171; i++)
        oc2.Free(list2[i]);
}

TEST(ObjectCacheTest, MassiveObjects) {
    ObjectCache<ObjectCacheTestB> oc;
    std::vector<ObjectCacheTestB*> list;

    ObjectCacheOptions options;
    options.name_ = "massive object oc";

    oc.Init(options);

    for (int i = 0; i < 20000; i++) {
        auto p = oc.Alloc();
        EXPECT_TRUE(p);
        list.push_back(p);
        EXPECT_LE(oc.Size(), 4096 * (i / 84 + 1));
        EXPECT_EQ(oc.EmptyListCnt(), 0);
        EXPECT_LE(oc.FullListCnt() + oc.PartialListCnt(), i / 84 + 1);
    }

    for (int i = 0; i < 20000; i++) {
        oc.Free(list[i]);
        EXPECT_LE(oc.EmptyListCnt(), 2);
    }
}

TEST(ObjectCacheTest, MultiCacheTest) {
    pthread_t tid1, tid2, tid3;

    auto oc1 = new ObjectCache<ObjectCacheTestA>;
    auto oc2 = new ObjectCache<ObjectCacheTestB>;
    auto oc3 = new ObjectCache<ObjectCacheTestC>;

    pthread_create(&tid1, NULL, ObjectCacheTestFunc1, oc1);
    pthread_create(&tid2, NULL, ObjectCacheTestFunc2, oc2);
    pthread_create(&tid3, NULL, ObjectCacheTestFunc3, oc3);

    pthread_join(tid1, NULL);
    pthread_join(tid2, NULL);
    pthread_join(tid3, NULL);

    EXPECT_EQ(ObjectCacheImpl::TotalSize(), 24576);

    delete oc1;
    delete oc2;
    delete oc3;
}

/* ObjectCacheThreadLocal */
void* ObjectCacheThreadLocalTestFunc1(void* args) {
    auto oc = static_cast<ObjectCacheImpl*>(args);
    std::vector<ObjectCacheTestA*> list;

    ObjectCacheImplOptions options;
    options.name_ = "multicache oc1";
    oc->Init(options);

    for (uint32_t i = 0; i < 255; i++) {
        auto p = static_cast<ObjectCacheTestA*>(oc->Alloc());
        EXPECT_TRUE(p);
        list.push_back(p);
    }

    EXPECT_EQ(oc->Size(), 8192);
    EXPECT_EQ(oc->EmptyListCnt(), 0);
    EXPECT_EQ(oc->FullListCnt(), 1);
    EXPECT_EQ(oc->PartialListCnt(), 1);

    return NULL;
}

void* ObjectCacheThreadLocalTestFunc2(void* args) {
    auto oc = static_cast<ObjectCacheImpl*>(args);
    std::vector<ObjectCacheTestB*> list;

    ObjectCacheImplOptions options;
    options.name_ = "multicache oc2";
    oc->Init(options);

    for (uint32_t i = 0; i < 169; i++) {
        auto p = static_cast<ObjectCacheTestB*>(oc->Alloc());
        EXPECT_TRUE(p);
        list.push_back(p);
    }

    EXPECT_EQ(oc->Size(), 12288);
    EXPECT_EQ(oc->EmptyListCnt(), 0);
    EXPECT_EQ(oc->FullListCnt(), 2);
    EXPECT_EQ(oc->PartialListCnt(), 1);
    return NULL;
}

void* ObjectCacheThreadLocalTestFunc3(void* args) {
    auto oc = static_cast<ObjectCacheImpl*>(args);
    std::vector<ObjectCacheTestC*> list;

    ObjectCacheImplOptions options;
    options.name_ = "multicache oc3";
    oc->Init(options);

    for (uint32_t i = 0; i < 10; i++) {
        auto p = static_cast<ObjectCacheTestC*>(oc->Alloc());
        EXPECT_TRUE(p);
        list.push_back(p);
    }

    EXPECT_EQ(oc->Size(), 4096);
    EXPECT_EQ(oc->EmptyListCnt(), 0);
    EXPECT_EQ(oc->FullListCnt(), 0);
    EXPECT_EQ(oc->PartialListCnt(), 1);

    return NULL;
}

TEST(ObjectCacheThreadLocalTest, BasicFunction) {
    ObjectCacheImpl oc(sizeof(ObjectCacheTestA), 4096, ThisThread::GetThreadId());
    std::vector<ObjectCacheTestA*> list;

    EXPECT_EQ(oc.Size(), 0);
    EXPECT_EQ(oc.EmptyListCnt(), 0);
    EXPECT_EQ(oc.FullListCnt(), 0);
    EXPECT_EQ(oc.PartialListCnt(), 0);

    for (uint32_t i = 0; i < 252; i++) {
        auto p = static_cast<ObjectCacheTestA*>(oc.Alloc());
        EXPECT_TRUE(p);
        list.push_back(p);
        EXPECT_EQ(oc.Size(), 4096);
        EXPECT_EQ(oc.EmptyListCnt(), 0);
        EXPECT_EQ(oc.FullListCnt(), 0);
        EXPECT_EQ(oc.PartialListCnt(), 1);
    }

    auto p = static_cast<ObjectCacheTestA*>(oc.Alloc());
    EXPECT_TRUE(p);
    list.push_back(p);
    EXPECT_EQ(oc.Size(), 4096);
    EXPECT_EQ(oc.EmptyListCnt(), 0);
    EXPECT_EQ(oc.FullListCnt(), 1);
    EXPECT_EQ(oc.PartialListCnt(), 0);

    p = static_cast<ObjectCacheTestA*>(oc.Alloc());
    EXPECT_TRUE(p);
    list.push_back(p);
    EXPECT_EQ(oc.Size(), 8192);
    EXPECT_EQ(oc.EmptyListCnt(), 0);
    EXPECT_EQ(oc.FullListCnt(), 1);
    EXPECT_EQ(oc.PartialListCnt(), 1);

    oc.Free(list[253]);
    EXPECT_EQ(oc.Size(), 8192);
    EXPECT_EQ(oc.EmptyListCnt(), 1);
    EXPECT_EQ(oc.FullListCnt(), 1);
    EXPECT_EQ(oc.PartialListCnt(), 0);

    for (uint32_t i = 1; i < list.size() - 1; i++) {
        oc.Free(list[i]);
        EXPECT_EQ(oc.Size(), 8192);
        EXPECT_EQ(oc.EmptyListCnt(), 1);
        EXPECT_EQ(oc.FullListCnt(), 0);
        EXPECT_EQ(oc.PartialListCnt(), 1);
    }

    oc.Free(list[0]);
    EXPECT_EQ(oc.Size(), 8192);
    EXPECT_EQ(oc.EmptyListCnt(), 2);
    EXPECT_EQ(oc.FullListCnt(), 0);
    EXPECT_EQ(oc.PartialListCnt(), 0);

    p = static_cast<ObjectCacheTestA*>(oc.Alloc());
    EXPECT_TRUE(p);
    EXPECT_EQ(oc.Size(), 8192);
    EXPECT_EQ(oc.EmptyListCnt(), 1);
    EXPECT_EQ(oc.FullListCnt(), 0);
    EXPECT_EQ(oc.PartialListCnt(), 1);
    oc.Free(p);
}

TEST(ObjectCacheThreadLocalTest, CleanEmptyPage) {
    ObjectCacheImpl oc(sizeof(ObjectCacheTestB), 4096, ThisThread::GetThreadId());
    std::vector<ObjectCacheTestB*> list;

    ObjectCacheImplOptions options;
    options.name_ = "clean empty page oc";
    options.empty_pages_limit_ = 3;
    oc.Init(options);

    for (uint32_t i = 0; i < 337; i++) {
        auto p = static_cast<ObjectCacheTestB*>(oc.Alloc());
        EXPECT_TRUE(p);
        list.push_back(p);
    }

    EXPECT_EQ(oc.Size(), 20480);
    EXPECT_EQ(oc.EmptyListCnt(), 0);
    EXPECT_EQ(oc.FullListCnt(), 4);
    EXPECT_EQ(oc.PartialListCnt(), 1);

    for (uint32_t i = 336; i >= 84; i--)
        oc.Free(list[i]);

    EXPECT_EQ(oc.Size(), 16384);
    EXPECT_EQ(oc.EmptyListCnt(), 3);
    EXPECT_EQ(oc.FullListCnt(), 1);
    EXPECT_EQ(oc.PartialListCnt(), 0);

    for (uint32_t i = 0; i < 84; i++)
        oc.Free(list[i]);

    EXPECT_EQ(oc.Size(), 12288);
    EXPECT_EQ(oc.EmptyListCnt(), 3);
    EXPECT_EQ(oc.FullListCnt(), 0);
    EXPECT_EQ(oc.PartialListCnt(), 0);
}

TEST(ObjectCacheThreadLocalTest, SmallObject) {
    ObjectCacheImpl oc(sizeof(ObjectCacheTestC), 4096, ThisThread::GetThreadId());
    std::vector<ObjectCacheTestC*> list;

    for (uint32_t i = 0; i < 508; i++) {
        auto p = static_cast<ObjectCacheTestC*>(oc.Alloc());
        EXPECT_TRUE(p);
        list.push_back(p);
    }

    EXPECT_EQ(oc.Size(), 8192);
    EXPECT_EQ(oc.EmptyListCnt(), 0);
    EXPECT_EQ(oc.FullListCnt(), 1);
    EXPECT_EQ(oc.PartialListCnt(), 1);

    auto p = static_cast<ObjectCacheTestC*>(oc.Alloc());
    EXPECT_TRUE(p);
    list.push_back(p);

    EXPECT_EQ(oc.Size(), 8192);
    EXPECT_EQ(oc.EmptyListCnt(), 0);
    EXPECT_EQ(oc.FullListCnt(), 1);
    EXPECT_EQ(oc.PartialListCnt(), 1);

    for (uint32_t i = 0; i < 509; i++)
        oc.Free(list[i]);

    EXPECT_EQ(oc.Size(), 8192);
    EXPECT_EQ(oc.EmptyListCnt(), 2);
    EXPECT_EQ(oc.FullListCnt(), 0);
    EXPECT_EQ(oc.PartialListCnt(), 0);
}

TEST(ObjectCacheThreadLocalTest, LargeObject) {
    ObjectCacheImpl oc(sizeof(ObjectCacheTestD), 4096, ThisThread::GetThreadId());
    std::vector<ObjectCacheTestD*> list;

    auto p = static_cast<ObjectCacheTestD*>(oc.Alloc());
    EXPECT_TRUE(p);
    list.push_back(p);
    EXPECT_EQ(oc.Size(), 8192);
    EXPECT_EQ(oc.EmptyListCnt(), 0);
    EXPECT_EQ(oc.FullListCnt(), 1);
    EXPECT_EQ(oc.PartialListCnt(), 0);

    for (int i = 0; i < 9; i++) {
        auto p = static_cast<ObjectCacheTestD*>(oc.Alloc());
        EXPECT_TRUE(p);
        list.push_back(p);
    }

    EXPECT_EQ(oc.Size(), 81920);
    EXPECT_EQ(oc.EmptyListCnt(), 0);
    EXPECT_EQ(oc.FullListCnt(), 10);
    EXPECT_EQ(oc.PartialListCnt(), 0);

    for (int i = 0; i < 10; i++) {
        oc.Free(list[i]);
        EXPECT_LE(oc.EmptyListCnt(), 2);
    }

    EXPECT_EQ(oc.Size(), 16384);
    EXPECT_EQ(oc.EmptyListCnt(), 2);
    EXPECT_EQ(oc.FullListCnt(), 0);
    EXPECT_EQ(oc.PartialListCnt(), 0);
}

TEST(ObjectCacheThreadLocalTest, OddPageSize) {
    ObjectCacheImpl oc1(sizeof(ObjectCacheTestA), 517, ThisThread::GetThreadId());
    std::vector<ObjectCacheTestA*> list1;

    for (int i = 0; i < 62; i++) {
        auto p = static_cast<ObjectCacheTestA*>(oc1.Alloc());
        EXPECT_TRUE(p);
        list1.push_back(p);
    }

    EXPECT_EQ(oc1.Size(), 2048);
    EXPECT_EQ(oc1.EmptyListCnt(), 0);
    EXPECT_EQ(oc1.FullListCnt(), 1);
    EXPECT_EQ(oc1.PartialListCnt(), 1);

    auto p = static_cast<ObjectCacheTestA*>(oc1.Alloc());
    EXPECT_TRUE(p);
    list1.push_back(p);
    EXPECT_EQ(oc1.Size(), 2048);
    EXPECT_EQ(oc1.EmptyListCnt(), 0);
    EXPECT_EQ(oc1.FullListCnt(), 1);
    EXPECT_EQ(oc1.PartialListCnt(), 1);

    ObjectCacheImpl oc2(sizeof(ObjectCacheTestB), 4097, ThisThread::GetThreadId());
    std::vector<ObjectCacheTestB*> list2;

    for (int i = 0; i < 170; i++) {
        auto p = static_cast<ObjectCacheTestB*>(oc2.Alloc());
        EXPECT_TRUE(p);
        list2.push_back(p);
    }

    EXPECT_EQ(oc2.Size(), 16384);
    EXPECT_EQ(oc2.EmptyListCnt(), 0);
    EXPECT_EQ(oc2.FullListCnt(), 1);
    EXPECT_EQ(oc2.PartialListCnt(), 1);

    auto q = static_cast<ObjectCacheTestB*>(oc2.Alloc());
    EXPECT_TRUE(q);
    list2.push_back(q);
    EXPECT_EQ(oc2.Size(), 16384);
    EXPECT_EQ(oc2.EmptyListCnt(), 0);
    EXPECT_EQ(oc2.FullListCnt(), 1);
    EXPECT_EQ(oc2.PartialListCnt(), 1);

    for (int i = 0; i < 63; i++)
        oc1.Free(list1[i]);

    for (int i = 0; i < 171; i++)
        oc2.Free(list2[i]);
}

TEST(ObjectCacheThreadLocalTest, MassiveObjects) {
    ObjectCacheImpl oc(sizeof(ObjectCacheTestB), 4096, ThisThread::GetThreadId());
    std::vector<ObjectCacheTestB*> list;

    ObjectCacheImplOptions options;
    options.name_ = "massive object oc";
    options.empty_pages_limit_ = 10000;

    oc.Init(options);

    for (int i = 0; i < 20000; i++) {
        auto p = static_cast<ObjectCacheTestB*>(oc.Alloc());
        EXPECT_TRUE(p);
        list.push_back(p);
        EXPECT_LE(oc.Size(), 4096 * (i / 84 + 1));
        EXPECT_EQ(oc.EmptyListCnt(), 0);
        EXPECT_LE(oc.FullListCnt() + oc.PartialListCnt(), i / 84 + 1);
    }

    for (int i = 0; i < 20000; i++) {
        oc.Free(list[i]);
        EXPECT_LE(oc.EmptyListCnt(), 239);
    }
}

TEST(ObjectCacheThreadLocalTest, MultiCacheTest) {
    pthread_t tid1, tid2, tid3;

    auto oc1 = new ObjectCacheImpl(sizeof(ObjectCacheTestA), 4096, ThisThread::GetThreadId());
    auto oc2 = new ObjectCacheImpl(sizeof(ObjectCacheTestB), 4096, ThisThread::GetThreadId());
    auto oc3 = new ObjectCacheImpl(sizeof(ObjectCacheTestC), 4096, ThisThread::GetThreadId());

    pthread_create(&tid1, NULL, ObjectCacheTestFunc1, oc1);
    pthread_create(&tid2, NULL, ObjectCacheTestFunc2, oc2);
    pthread_create(&tid3, NULL, ObjectCacheTestFunc3, oc3);

    pthread_join(tid1, NULL);
    pthread_join(tid2, NULL);
    pthread_join(tid3, NULL);

    EXPECT_EQ(ObjectCacheImpl::TotalSize(), 24576);

    delete oc1;
    delete oc2;
    delete oc3;
}

}  // namespace byte
