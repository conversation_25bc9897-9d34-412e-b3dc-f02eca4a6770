// Copyright (c) 2018-present, ByteDance Inc. All rights reserved.

#include "byte/string/string_piece.h"
#include "gtest/gtest.h"
namespace byte {
TEST(StringPiece, Correction) {
    char string1[] = "FooBar";
    char string2[] = "FooBaz";

    StringPiece s1(string1, sizeof(string1) - 1);
    StringPiece s2(string2, sizeof(string2) - 1);
    StringPiece sp1(string1, sizeof(string1) - 1);
    StringPiece sp2(string2, sizeof(string2) - 1);

    EXPECT_FALSE(s1 == s2);
    EXPECT_FALSE(sp1 == sp2);
    EXPECT_TRUE(s1 != s2);
    EXPECT_TRUE(sp1 != sp2);
    EXPECT_TRUE(s1 < s2);
    EXPECT_TRUE(sp1 < sp2);
    EXPECT_TRUE(s1 <= s2);
    EXPECT_TRUE(sp1 <= sp2);
    EXPECT_FALSE(s1 > s2);
    EXPECT_FALSE(sp1 > sp2);
    EXPECT_FALSE(s1 >= s2);
    EXPECT_FALSE(sp1 >= sp2);

    std::string s = "foo";
    StringPiece sp3(s);
    StringPiece sp4(s.c_str());
    EXPECT_TRUE(sp3 == sp4);
}

}  // namespace byte
