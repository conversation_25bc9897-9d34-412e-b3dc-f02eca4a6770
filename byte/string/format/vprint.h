// Copyright (c) 2013, The TOFT Authors.
// All rights reserved.
//
// Author: <PERSON><PERSON> <<EMAIL>>
// Created: 2013-02-07

#pragma once

#include <string>

#include "byte/string/format/print_arg.h"

namespace byte {

int VFormatPrint(FormatPrintTarget* target, const char* format,
                 const FormatPrintArg** args, int nargs);

int StringVPrintAppend(std::string* out, const char* format,
                       const FormatPrintArg** args, int argc);

int StringVPrintTo(std::string* out, const char* format,
                   const FormatPrintArg** args, int argc);

std::string StringVPrint(const char* format, const FormatPrintArg** args,
                         int argc);

}  // namespace byte
