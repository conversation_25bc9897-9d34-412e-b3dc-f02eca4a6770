// Copyright (c) 2018-present, ByteDance Inc. All rights reserved.

#include <string.h>
#include "byte/system/timestamp.h"

namespace byte {

// e.g: 20181106095438
bool CheckTimeStamp(const StringPiece& timestamp) {
    if (timestamp.length() != 14) return false;
    for (uint32_t i = 0; i < 14; ++i) {
        if (!isdigit(timestamp[i])) return false;
    }
    // Date range
    static const uint8_t kMin[7] = { 0,  0,  1,  1,  0,  0,  0 };
    static const uint8_t kMax[7] = { 99, 99, 12, 31, 23, 59, 59 };
    // ASCII to decimal
    uint8_t part[7];
    for (uint32_t i = 0; i < 7; ++i) {
        part[i] = (timestamp[i * 2] & 0x0F) * 10 + (timestamp[i * 2 + 1] & 0x0F);
        if (part[i] > kMax[i] || part[i] < kMin[i]) return false;
    }
    // To keep this timestamp can be converted to int32 type.
    // Range from 19700101000000 to 20380119031407 is valid.
    // So we keep the year from 1970-2037 valid. just ignore date after
    // 2038A.D Jan 1st.
    const uint32_t year = part[0] * 100 + part[1];
    if (year < 1970 || year > 2037) return false;
    // Check day
    uint8_t maxday = kMax[3];
    if (part[2] == 2) {   // February
        maxday = 28;      // non-leap year
        if ((!(part[1] & 0x03) && part[1]) || (!(part[0] & 0x03) && !part[1])) {
            maxday = 29;  // leap year
        }
    } else if (part[2] == 4 || part[2] == 6 || part[2] == 9 || part[2] == 11) {
        maxday = 30;      // 30-day months
    }
    if (part[3] > maxday) return false;
    return true;
}

}  // namespace byte
