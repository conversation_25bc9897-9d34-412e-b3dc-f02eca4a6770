// Copyright (c) 2018-present, ByteDance Inc. All rights reserved.

#include "byte/system/ip6_address.h"
#include <unistd.h>                            // gethostname
#include <arpa/inet.h>                         // inet_pton, inet_ntop
#include <netdb.h>                             // getaddrinfo
#include <string.h>
#include <stdexcept>                           // throw

namespace byte {

Ip6Address::Ip6Address(const char* src) {
    if (!Assign(src))
        throw std::invalid_argument(std::string("Invalid IPv6 Address: ") + src);
}

Ip6Address::Ip6Address(const std::string& src) {
    if (!Assign(src))
        throw std::invalid_argument("Invalid IPv6 Address: " + src);
}

bool Ip6Address::Assign(const char* src) {
    if (src == nullptr) {
        return false;
    }

    for (; isspace(*src); ++src) {}
    if (inet_pton(AF_INET6, src, &ip_) > 0) {
        // IPv6 address
        return true;
    }

    struct in_addr ip4;
    if (inet_pton(AF_INET, src, &ip4) > 0) {
        // IPv4 address
        Assign(ip4);
        return true;
    }
    return false;
}

void Ip6Address::ToString(std::string* str) const {
    char text[INET6_ADDRSTRLEN];
    if (IsIpv4MappedIpv6()) {
        struct in_addr ip4;
        Ip6ToIp4(&ip4);
        if (inet_ntop(AF_INET, &ip4, text, INET_ADDRSTRLEN) == NULL) {
            throw std::logic_error("Invalid IPv6 Address");
        }
    } else {
        if (inet_ntop(AF_INET6, &ip_, text, INET6_ADDRSTRLEN) == NULL) {
            throw std::logic_error("Invalid IPv6 Address");
        }
    }
    str->assign(text, strlen(text));
}

bool Ip6Address::IsLoopback() const {
    if (IsIpv4MappedIpv6()) {
        struct in_addr ip4;
        Ip6ToIp4(&ip4);
        return (ip4.s_addr & htonl(0xff000000)) == htonl((uint32_t)127 << 24);
    } else {
        return ip_.s6_addr32[0] == 0UL &&
               ip_.s6_addr32[1] == 0UL &&
               ip_.s6_addr32[2] == 0UL &&
               ip_.s6_addr32[3] == htonl(1UL);
    }
}

bool Ip6Address::IsBroadcast() const {
    if (IsIpv4MappedIpv6()) {
        struct in_addr ip4;
        Ip6ToIp4(&ip4);
        return (ip4.s_addr & htonl(0x000000ff)) == htonl((uint32_t)255);
    } else {
        // IPv6 does not support broadcast
        return false;
    }
}

bool Ip6Address::IsToBPrivateEnv() const {
    char* tob_private_env = getenv("BDC_PRIVATE_CLOUD");
    if (tob_private_env == nullptr) {
        return false;
    }

    if (strcmp(tob_private_env, "True") != 0) {
        return false;
    }
    return true;
}

bool Ip6Address::IsPrivate() const {
    if (IsToBPrivateEnv()) {
        return true;
    }

    if (IsIpv4MappedIpv6()) {
        struct in_addr ip4;
        Ip6ToIp4(&ip4);
        unsigned char bytes[4];
        memcpy(bytes, reinterpret_cast<char*>(&ip4), 4);
        return bytes[0] == 10 || bytes[0] == 11 || bytes[0] == 33 ||
               (bytes[0] == 172 && bytes[1] >= 16 && bytes[1] <= 31) ||
               (bytes[0] == 192 && bytes[1] == 168) ||
               (bytes[0] == 100 && bytes[1] >= 64 && bytes[1] <= 127);
    } else {
        // NOTE(suzanwen): refer to https://bytedance.feishu.cn/docs/doccngezNGT2NunNqJSkCJsv0Oc.
        return (ip_.s6_addr16[0] & htons(0xfe00)) == htons(0xfc00) ||
               (ip_.s6_addr32[0] == htonl(0x26050340) &&
                (ip_.s6_addr16[2] & htons(0xff00)) == htons(0xcd00));
    }
}

bool Ip6Address::IsLinkLocal() const {
    if (IsIpv4MappedIpv6()) {
        struct in_addr ip4;
        Ip6ToIp4(&ip4);
        return (ip4.s_addr & htonl(0xffff0000)) == htonl(0xa9fe0000);
    } else {
        return (ip_.s6_addr32[0] & htonl(0xffc00000)) == htonl(0xfe800000);
    }
}

bool Ip6Address::IsIpv4MappedIpv6() const {
    if (htonl(ip_.s6_addr32[2]) != 0x0000ffff) {
        return false;
    }
    if (ip_.s6_addr32[0] != 0 || ip_.s6_addr32[1] != 0) {
        return false;
    }
    return true;
}

void Ip6Address::ToUints(std::vector<uint32_t>* v) const {
    for (int i = 0; i < 4; ++i) {
        v->push_back(ip_.s6_addr32[i]);
    }
}

bool Ip6Address::GetLocalIpFromEnv(Ip6Address* ip, bool v6) {
    char* ip_str = NULL;
    if (v6) {
        ip_str = getenv("MY_HOST_IPV6");
    } else {
        ip_str = getenv("MY_HOST_IP");
    }
    if (ip_str && ip->Assign(ip_str)) {
        return true;
    }
    return false;
}

bool Ip6Address::GetLocalIpList(std::vector<Ip6Address>* v, bool v6) {
    v->clear();
    Ip6Address env_ip;
    bool getenv_succ = GetLocalIpFromEnv(&env_ip, v6);
    if (getenv_succ) {
        v->push_back(env_ip);
    }

    char buf[256];
    if (gethostname(buf, sizeof(buf)) < 0) {
        return getenv_succ;
    }
    struct addrinfo* result = NULL;
    struct addrinfo* cur = NULL;
    struct addrinfo hints;
    memset(&hints, 0, sizeof(struct addrinfo));
    hints.ai_socktype = SOCK_STREAM;
    hints.ai_flags = AI_PASSIVE;
    hints.ai_family = v6 ? AF_INET6 : AF_INET;
    if (getaddrinfo(buf, NULL, &hints, &result) != 0) {
        return getenv_succ;
    }
    for (cur = result; cur != NULL; cur = cur->ai_next) {
        Ip6Address ip;
        if (v6) {
            struct sockaddr_in6* in6 = (struct sockaddr_in6*)cur->ai_addr;
            ip.Assign(in6->sin6_addr);
        } else {
            struct sockaddr_in* in = (struct sockaddr_in*)cur->ai_addr;
            ip.Assign(in->sin_addr);
        }
        if (!ip.IsLoopback() && !ip.IsLinkLocal() &&
                (!getenv_succ || ip != env_ip)) {
            v->push_back(ip);
        }
    }
    freeaddrinfo(result);

    return (v->size() > 0) ? true : false;
}

bool Ip6Address::GetPrivateList(std::vector<Ip6Address>* v, bool v6) {
    if (!GetLocalIpList(v, v6)) {
        return false;
    }

    std::vector<Ip6Address>::iterator iter = v->begin();
    while (iter != v->end()) {
        if (!iter->IsPrivate()) {
            iter = v->erase(iter);
        } else {
            ++iter;
        }
    }
    if (v->empty()) {
        return false;
    }
    return true;
}

bool Ip6Address::GetPublicList(std::vector<Ip6Address>* v, bool v6) {
    if (!GetLocalIpList(v, v6))
        return false;

    std::vector<Ip6Address>::iterator iter = v->begin();
    while (iter != v->end()) {
        if (!iter->IsPublic()) {
            iter = v->erase(iter);
        } else {
            ++iter;
        }
    }
    if (v->empty()) {
        return false;
    }
    return true;
}

bool Ip6Address::GetFirstLocalAddress(Ip6Address* a, bool v6) {
    std::vector<Ip6Address> v;
    if (GetLocalIpList(&v, v6)) {
        *a = v[0];
        return true;
    }
    return false;
}

bool Ip6Address::GetFirstPrivateAddress(Ip6Address* a, bool v6) {
    std::vector<Ip6Address> v;
    if (GetPrivateList(&v, v6)) {
        *a = v[0];
        return true;
    }
    return false;
}

bool Ip6Address::GetFirstPublicAddress(Ip6Address* a, bool v6) {
    std::vector<Ip6Address> v;
    if (GetPublicList(&v, v6)) {
        *a = v[0];
        return true;
    }
    return false;
}

}  // namespace byte
