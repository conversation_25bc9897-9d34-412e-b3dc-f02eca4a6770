// Copyright (c) 2018-present, ByteDance Inc. All rights reserved.

#pragma once

#include <sys/time.h>
#include <time.h>
#include <chrono>  // NOLINT(build/c++11)
#include "byte/string/string_piece.h"

namespace byte {

inline int64_t GetCurrentTimeInUs() {
    struct timeval tv;
    gettimeofday(&tv, 0);
    return tv.tv_sec * 1000000LL + tv.tv_usec;
}

inline int64_t GetCurrentTimeInMs() {
    struct timeval tv;
    gettimeofday(&tv, 0);
    return tv.tv_sec * 1000LL + tv.tv_usec / 1000;
}

inline int32_t GetCurrentTime() {
    return time(NULL);
}

// In nano seconds.
inline int64_t GetCurrentTimeInNs() {
    std::chrono::time_point<std::chrono::system_clock> now = std::chrono::system_clock::now();
    return now.time_since_epoch().count();
}

inline timespec TimeInUsToTimespec(int64_t timestamp) {
    timespec ts;
    ts.tv_sec = timestamp / 1000000;
    ts.tv_nsec = (timestamp % 1000000) * 1000;
    return ts;
}

inline timespec GetCurrentTimeToTimespec() {
    return TimeInUsToTimespec(GetCurrentTimeInUs());
}

inline int64_t GetRelativeTimeToCurrentInUs(int64_t relative_time_in_us) {
    return GetCurrentTimeInUs() + relative_time_in_us;
}

inline timespec GetRelativeTimespecToCurrentInUs(int64_t relative_time_in_us) {
    return TimeInUsToTimespec(GetCurrentTimeInUs() + relative_time_in_us);
}

bool CheckTimeStamp(const StringPiece& timestamp);

inline int64_t GetCurrentMonotonicTimeInMs() {
    struct timespec ts;
    clock_gettime(CLOCK_MONOTONIC, &ts);
    return ts.tv_sec * 1000LL + ts.tv_nsec / (1000LL * 1000LL);
}

inline int64_t GetCurrentMonotonicTimeInUs() {
    struct timespec ts;
    clock_gettime(CLOCK_MONOTONIC, &ts);
    return ts.tv_sec * 1000LL * 1000LL + ts.tv_nsec / (1000LL);
}

inline timespec GetRelativeTimespecToCurrentMonotonicInUs(int64_t relative_time_in_us) {
    return TimeInUsToTimespec(GetCurrentMonotonicTimeInUs() + relative_time_in_us);
}

}  // namespace byte
