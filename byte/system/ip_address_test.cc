// Copyright (c) 2011, The Toft Authors.
// All rights reserved.
//
// Author: <PERSON><PERSON> <PERSON> <<EMAIL>>
// Created: 09/30/11
// Description:

#include "byte/system/ip_address.h"
#include "gtest/gtest.h"

namespace byte {

TEST(<PERSON><PERSON><PERSON><PERSON><PERSON>, Constructor) {
    EXPECT_NO_THROW(IpAddress a("*******"));
    EXPECT_NO_THROW(IpAddress a(std::string("*******")));
    EXPECT_ANY_THROW(IpAddress a("1.256.3.4"));
    EXPECT_ANY_THROW(IpAddress a(std::string("*******x")));
}

TEST(IpAddress, Parse) {
    EXPECT_TRUE(IpAddress("*******") == IpAddress(1, 2, 3, 4));
    IpAddress address;
    EXPECT_FALSE(address.Assign("-*******"));
    EXPECT_FALSE(address.Assign("*******x"));
    EXPECT_FALSE(address.Assign("1.256.3.4"));
    EXPECT_TRUE(address.Assign("77.220.81.0017"));
    EXPECT_EQ("************", address.ToString());
}

TEST(IpAddress, Bytes) {
    IpAddress a("*******");
    EXPECT_EQ(1, a.Bytes()[0]);
    EXPECT_EQ(2, a.Bytes()[1]);
    EXPECT_EQ(3, a.Bytes()[2]);
    EXPECT_EQ(4, a.Bytes()[3]);
}

TEST(IpAddress, ByteOrder) {
    IpAddress a("***********");
    EXPECT_EQ(static_cast<in_addr_t>(192 + (168 << 8) + (0 << 16) + (1 << 24)), a.ToInt());
    EXPECT_EQ(static_cast<in_addr_t>((192 << 24)+ (168 << 16) + (0 << 8) + 1), a.ToLocalInt());
}

TEST(IpAddress, CompareToSystemParsing) {
    IpAddress a("***********");
    EXPECT_EQ(inet_addr("***********"), a.ToInt());
}

TEST(IpAddress, WellKnown) {
    EXPECT_TRUE(IpAddress("0.0.0.0") == IpAddress::Any());
    EXPECT_TRUE(IpAddress("127.0.0.1") == IpAddress::Loopback());
    EXPECT_TRUE(IpAddress("***************") == IpAddress::Broadcast());
    EXPECT_TRUE(IpAddress("***************") == IpAddress::None());
}

TEST(IpAddress, Compare) {
    EXPECT_TRUE(IpAddress("0.0.0.0") == IpAddress("0.0.0.0"));
    EXPECT_TRUE(IpAddress("0.0.0.0") != IpAddress("*******"));
    EXPECT_TRUE(IpAddress("0.0.0.0") < IpAddress("*******"));
    EXPECT_TRUE(IpAddress("*******") > IpAddress("*******"));

    EXPECT_TRUE(IpAddress("*******") >= IpAddress("0.0.0.0"));
    EXPECT_TRUE(IpAddress("0.0.0.0") >= IpAddress("0.0.0.0"));
    EXPECT_TRUE(IpAddress("0.0.0.0") >= IpAddress("0.0.0.0"));

    EXPECT_TRUE(IpAddress("0.0.0.0") <= IpAddress("0.0.0.0"));
    EXPECT_TRUE(IpAddress("0.0.0.0") <= IpAddress("*******"));
}

TEST(IpAddress, Type) {
    EXPECT_TRUE(IpAddress("*********").IsBroadcast());
    EXPECT_TRUE(!IpAddress("*********").IsBroadcast());

    EXPECT_TRUE(IpAddress("127.0.0.1").IsLoopback());
    EXPECT_TRUE(IpAddress("*********").IsLoopback());
    EXPECT_TRUE(!IpAddress("*********").IsLoopback());

    EXPECT_TRUE(IpAddress("1*******").IsPrivate());
    EXPECT_TRUE(IpAddress("***********").IsPrivate());
    EXPECT_TRUE(IpAddress("**********").IsPrivate());
    EXPECT_TRUE(IpAddress("**********").IsPrivate());
    EXPECT_TRUE(!IpAddress("**********").IsPrivate());
    EXPECT_TRUE(!IpAddress("**********").IsPrivate());
    EXPECT_TRUE(!IpAddress("**********").IsPrivate());
    EXPECT_TRUE(IpAddress("**********").IsPrivate());
}

TEST(IpAddress, GetAddressList) {
    std::vector<IpAddress> v;
    IpAddress address;
    EXPECT_EQ(IpAddress::GetLocalIpList(&v), IpAddress::GetFirstLocalAddress(&address));
    EXPECT_EQ(IpAddress::GetPublicList(&v), IpAddress::GetFirstPublicAddress(&address));
    EXPECT_EQ(IpAddress::GetPrivateList(&v), IpAddress::GetFirstPrivateAddress(&address));
}

}  // namespace byte
