// Copyright (c) 2019-present, ByteDance Inc. All rights reserved.

#pragma once

#include <stdint.h>
#include <deque>
#include <sstream>
#include <string>
#include "byte/include/macros.h"

namespace byte {

template <typename T>
struct Range {
public:
    Range() : begin_(0), end_(0) {}
    // Carefully use 2nd parameter. It IS 'size' NOT 'end' because we
    // do NOT want to CHECK (end < begin) here.
    Range(T begin, T size) : begin_(begin), end_(begin + size) {}
    bool Empty() const { return !(end_ - begin_); }
    T Size() const { return end_ - begin_; }

public:
    T begin_;
    T end_;
};

struct Range32 : public Range<uint32_t> {
    Range32() : Range<uint32_t>() {}
    Range32(uint32_t begin, uint32_t size) : Range<uint32_t>(begin, size) {}
};

struct Range64 : public Range<uint64_t> {
    Range64() : Range<uint64_t>() {}
    Range64(uint64_t begin, uint64_t size) : Range<uint64_t>(begin, size) {}
};

static const uint64_t kLargestLogOffset = UINT64_MAX;

struct DualOffset {
    DualOffset() : entry_offset_(0), log_offset_(kLargestLogOffset) {}
    DualOffset(uint32_t entry_offset, uint64_t log_offset)
        : entry_offset_(entry_offset), log_offset_(log_offset) {}

    uint32_t entry_offset_;
    uint64_t log_offset_;
};

struct EntryOffsetLessThan {
    bool operator()(const DualOffset& lhs, const DualOffset& rhs) {
        return lhs.entry_offset_ < rhs.entry_offset_;
    }
};

struct LogOffsetLessThan {
    bool operator()(const DualOffset& lhs, const DualOffset& rhs) {
        // Update offsets are always larger than bases'. So use raw value here.
        return lhs.log_offset_ < rhs.log_offset_;
    }
};

struct RangePosition {
    uint32_t entry_offset_;
    uint32_t length_;
    uint64_t log_offset_;

    RangePosition() : entry_offset_(0), length_(0), log_offset_(kLargestLogOffset) {}
    RangePosition(uint32_t entry_offset,
                  uint32_t length,
                  uint64_t log_offset)
        : entry_offset_(entry_offset),
          length_(length),
          log_offset_(log_offset) {}

    bool operator==(const RangePosition &other) const {
        return (entry_offset_ == other.entry_offset_ &&
                length_ == other.length_ &&
                log_offset_ == other.log_offset_);
    }
    inline uint32_t GetRangeEnd() const { return entry_offset_ + length_; }

    std::string ToString() const {
        std::stringstream ss;
        ss << " EntryOffset = " << entry_offset_
           << " Length = " << length_
           << " LogOffset = " << log_offset_;
        return ss.str();
    }
};

void MergeRanges(const std::deque<Range32>* bases,
                 const std::deque<Range32>* updates,
                 std::deque<Range32>* joins);

// Mere range positions from bases and updates into joins.
// The log_id in `joins' SHOULD NOT be less than min_log_id.
void MergeRangePositions(const std::deque<RangePosition>* bases,
                         const std::deque<RangePosition>* updates,
                         std::deque<RangePosition>* joins);

void PushRangePosition(const RangePosition& range_pos,
                       std::deque<Range32>* bases,
                       std::deque<RangePosition>* updates);

}  // namespace byte
