// Copyright (c) 2019-present, ByteDance Inc. All rights reserved.

#include "byte/block/entry.h"
#include "gtest/gtest.h"

namespace {
static const uint32_t k_max_entry_size = 64 * 1024 * 1024;
}  // namespace

namespace byte {

class EntryTest : public testing::Test {
public:
    EntryTest() : allocator_(nullptr), entry_(nullptr) {}

    virtual ~EntryTest() {
        delete entry_;
        delete allocator_;
    }

    void SetType(int flag) {
        if (flag == 0) {
            entry_ = new BlockEntry(k_max_entry_size);
        } else {
            allocator_ = new MemoryPoolLiteAllocator<std::pair<const uint32_t, uint64_t>>;
            entry_ = new BlockEntry(k_max_entry_size, allocator_);
        }
    }

    void RunCase() {
        for (uint32_t i = 0; i < 100; ++i) {
            RangePosition position(i * 8192, 4096, i * 4096 + 1);
            entry_->AddRange(position);
        }
        std::deque<Range32> bases;
        std::deque<RangePosition> updates;
        Range32 full_range(0, k_max_entry_size);
        entry_->GetRanges(full_range, &bases, &updates);
        for (uint32_t i = 0; i < 100; ++i) {
            const RangePosition& position = updates[i];
            EXPECT_EQ(i * 8192, position.entry_offset_);
            EXPECT_EQ(i * 4096 + 1, position.log_offset_);
            EXPECT_EQ(4096, position.length_);
            const Range32& range = bases[i];
            EXPECT_EQ(4096 + i * 8192, range.begin_);
            if (i != 99) {
                EXPECT_EQ(8192 + i * 8192, range.end_);
            } else {
                EXPECT_EQ(k_max_entry_size, range.end_);
            }
        }
        for (uint32_t i = 0; i < 100; ++i) {
            RangePosition position(i * 8192 + 4094, 4100, (i + 100) * 4096 + 1);
            entry_->AddRange(position);
        }
        bases.clear();
        updates.clear();
        entry_->GetRanges(full_range, &bases, &updates);
        EXPECT_EQ(200, updates.size());
        for (uint32_t i = 0; i < 200; ++i) {
            const RangePosition& position = updates[i];
            if (i % 2 == 0) {
                if (i != 0) {
                    EXPECT_EQ(i * 4096 + 2, position.entry_offset_);
                    EXPECT_EQ(4092, position.length_);
                    EXPECT_EQ(i * 2048 + 3, position.log_offset_);
                } else {
                    EXPECT_EQ(0, position.entry_offset_);
                    EXPECT_EQ(4094, position.length_);
                    EXPECT_EQ(1, position.log_offset_);
                }
            } else {
                EXPECT_EQ((i - 1) * 4096 + 4094, position.entry_offset_);
                EXPECT_EQ(4100, position.length_);
                EXPECT_EQ((i - 1) * 2048 + 409601, position.log_offset_);
            }
        }
        EXPECT_EQ(1, bases.size());
        EXPECT_EQ(200 * 4096 + 2, bases[0].begin_);
        EXPECT_EQ(k_max_entry_size, bases[0].end_);
        EXPECT_EQ(202, entry_->RangeSize());  // plus head and tail
        // Iterator
        RangeIterator* range_iter = entry_->NewRangeIterator();
        uint32_t index = 0;
        while (range_iter->Valid()) {
            std::pair<uint32_t, uint64_t> range = range_iter->Current();
            if (index == 0) {
                EXPECT_EQ(0, range.first);
                EXPECT_EQ(1, range.second);
            } else if (index % 2 == 0) {
                EXPECT_EQ(4096 * index + 2, range.first);
                EXPECT_EQ(index * 2048 + 3, range.second);
            } else {
                EXPECT_EQ(4096 * index - 2, range.first);
                EXPECT_EQ((index - 1) * 2048 + 409601, range.second);
            }
            range_iter->Next();
            ++index;
        }
        EXPECT_EQ(200, index);
        delete range_iter;
        bases.clear();
        updates.clear();
        entry_->GetRanges(Range32(4096, 40), &bases, &updates);
        EXPECT_EQ(1, updates.size());
        EXPECT_EQ(0, bases.size());
        // Overwrite
        entry_->AddRange(RangePosition(0, 32 * 1024 * 1024, 2000000));
        bases.clear();
        updates.clear();
        entry_->GetRanges(full_range, &bases, &updates);
        EXPECT_EQ(1, updates.size());
        EXPECT_EQ(1, bases.size());
        // Existent
        EXPECT_TRUE(entry_->GetExistent());
        entry_->SetExistent(false);
        EXPECT_FALSE(entry_->GetExistent());
    }

private:
    MemoryPoolLiteAllocator<std::pair<const uint32_t, uint64_t>>* allocator_;
    BlockEntry* entry_;
};

TEST_F(EntryTest, BlockEntry) {
    SetType(0);
    RunCase();
}

TEST_F(EntryTest, BlockEntryMemPool) {
    SetType(1);
    RunCase();
}

}  // namespace byte
