cc_library(
  name = "source_location",
  onlyinc = [
    "../../",
  ],
  export_incs = [
    "../../"
  ],
)

cc_test(
  name = "source_location_test",
  srcs = ["source_location_test.cc"],
  deps = [
    ":source_location",
    "basis/cpp.src.base.common:master@//cpp.src.base.common/test/googletest:gtest_main",
    "basis/cpp.src.base.common:master@//cpp.src.base.common/test/googletest:gtest",
    "basis/cpp.src.base.common:master@//cpp.src.base.common/test/googletest:gmock",
    "basis/cpp.src.base.common:master@//cpp.src.base.common/app/abseil:absl",
    "#pthread",
  ],
)

cc_library(
  name = "status_builder",
  srcs = ["status_builder.cc"],
  incs = ["../../"],
  export_incs = [
    "../../"
  ],
  deps = [
    ":source_location",
    "basis/cpp.src.base.common:master@//cpp.src.base.common/app/abseil:absl",
  ],
)

cc_test(
  name = "status_builder_test",
  srcs = ["status_builder_test.cc"],
  incs = ["../../"],
  deps = [
    ":status_builder",
    "//byte/byte/absl_util/testing:status_matchers",
    "basis/cpp.src.base.common:master@//cpp.src.base.common/app/abseil:absl",
    "basis/cpp.src.base.common:master@//cpp.src.base.common/test/googletest:gtest_main",
    "basis/cpp.src.base.common:master@//cpp.src.base.common/test/googletest:gtest",
    "basis/cpp.src.base.common:master@//cpp.src.base.common/test/googletest:gmock",
    "#pthread",
  ],
)

cc_library(
  name = "status_macros",
  onlyinc = [
    "../../"
  ],
  deps = [
    ":status_builder",
    "basis/cpp.src.base.common:master@//cpp.src.base.common/app/abseil:absl",
  ],
)

cc_test(
  name = "status_macros_test",
  srcs = ["status_macros_test.cc"],
  incs = ["../../"],
  deps = [
    ":status_macros",
    "//byte/byte/absl_util/testing:status_matchers",
    "basis/cpp.src.base.common:master@//cpp.src.base.common/app/abseil:absl",
    "basis/cpp.src.base.common:master@//cpp.src.base.common/test/googletest:gtest_main",
    "basis/cpp.src.base.common:master@//cpp.src.base.common/test/googletest:gtest",
    "basis/cpp.src.base.common:master@//cpp.src.base.common/test/googletest:gmock",
    "#pthread",
  ],
)
