// Copyright (c) 2024-present, ByteDance Inc. All rights reserved.

#include "byte/util/bind_callback.h"

#include "gtest/gtest.h"

namespace byte {

int global_i1 = 0, global_i2 = 0;
char global_c = 'a';

// Example 1: Bind to member methods of class
class Clazz {
public:
    void M0() {
        global_i1 = 1;
        global_i2 = 2;
    }
    void M1(int i1) {
        global_i1 = i1;
        global_i2 = i1 + 1;
    }
    void M2(int i1, int i2) {
        global_i1 = i1;
        global_i2 = i2;
    }
};

TEST(BindCallback, MemberMethodsOfClass) {
    Clazz obj;

    BindCallbackM0<Clazz> m0;
    m0.Bind(&obj, &Clazz::M0);
    m0.Run();  // calls obj.M0()
    EXPECT_EQ(global_i1, 1);
    EXPECT_EQ(global_i2, 2);

    BindCallbackM1<Clazz, int> m1;
    m1.Bind(&obj, &Clazz::M1, 10);
    m1.Run();  // calls obj.M1(10);
    EXPECT_EQ(global_i1, 10);
    EXPECT_EQ(global_i2, 11);

    BindCallbackM2<Clazz, int, int> m2;
    m2.Bind(&obj, &Clazz::M2, 10, 20);
    m2.Run();  // calls obj.M2(10, 20);
    EXPECT_EQ(global_i1, 10);
    EXPECT_EQ(global_i2, 20);
}

// Example 2: Bind to global functions
void F0() {
    global_i1 = 11;
    global_i2 = 12;
}
void F1(int i1) {
    global_i1 = i1;
    global_i2 = i1 + 1;
}
void F2(int i1, int i2) {
    global_i1 = i1;
    global_i2 = i2;
}

TEST(BindCallback, GlobalFunctions) {
    BindCallbackF0 f0;
    f0.Bind(&F0);
    f0.Run();  // calls F0()
    EXPECT_EQ(global_i1, 11);
    EXPECT_EQ(global_i2, 12);

    BindCallbackF1<int> f1;
    f1.Bind(&F1, 100);
    f1.Run();  // calls F1(100)
    EXPECT_EQ(global_i1, 100);
    EXPECT_EQ(global_i2, 101);

    BindCallbackF2<int, int> f2;
    f2.Bind(&F2, 200, 300);
    f2.Run();  // calls F2(200, 300)
    EXPECT_EQ(global_i1, 200);
    EXPECT_EQ(global_i2, 300);
}

// Example 3: Callback with arguments
class Clazz2 {
public:
    void M1R2(int arg0, char result0, int result1) {
        global_i1 = arg0;
        global_i2 = result1;
        global_c = result0;
    }
};

TEST(BindCallback, CallbackWithArguments) {
    Clazz2 obj;
    BindCallbackM1R2<Clazz2, int /* arg0 */, char /* result0 */, int /* result1 */> callback;
    callback.Bind(&obj, &Clazz2::M1R2, 1000 /* arg0 */);
    callback.SetResult0('c');
    callback.SetResult1(2000);
    callback.Run();  // calls Clazz::M1R2(1000, 2000, 'c')
    EXPECT_EQ(global_i1, 1000);
    EXPECT_EQ(global_i2, 2000);
    EXPECT_EQ(global_c, 'c');
}

}  // namespace byte
