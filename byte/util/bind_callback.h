// Copyright (c) 2024-present, ByteDance Inc. All rights reserved.

/////////////////////////////////////////////////////
// DO NOT EDIT!!!
// This header file is auto generated by bind_callback.py
/////////////////////////////////////////////////////

// GLOBAL_NOLINT

#pragma once

#include "byte/base/closure.h"
#include "byte/include/assert.h"

namespace byte {

namespace bind_callback_internal {

const uint64_t BIND_CALLBACK_ALIVE = 0x4c4c41434556494cULL;  // "LIVECALL"
const uint64_t BIND_CALLBACK_FREED = 0x4c4c414345455246ULL;  // "FREECALL"

}  // namespace bind_callback_internal

// Here defines a family of BindCallback templates.
//
// BindCallbackR{0-3}: total 4
// BindCallbackM{0-12}R{0-3}: total 52
// BindCallbackF{0-12}R{0-3}: total 52
//
// These templates implement interface Closure.  Unlike
// NewClosure, BindMethod does no memory allocation or free at all, which
// is useful in latency-sensitive scenarios.  A user owns the full  life-
// cycle.  It is recommended to use it with a memory pool.
//
// Example 1: Bind to member methods of class.
//
//     class Clazz
//     {
//     public:
//          void M0();
//          void M1(int);
//          void M2(int, int);
//     };
//
//     Clazz obj;
//
//     BindCallbackM0<Clazz> m0;
//     m0.Bind(&obj, &Clazz::M0);
//     m0.Run();  // calls obj.M0()
//
//     BindCallbackM1<Clazz, int> m1;
//     m1.Bind(&obj, &Clazz::M1, 10);
//     m1.Run();  // calls obj.M1(10);
//
//     BindCallbackM2<Clazz, int, int> m2;
//     m2.Bind(&obj, &Clazz::M2, 10, 20);
//     m2.Run();  // calls obj.M2(10, 20);
//
// Example 2: Bind to global functions.
//
//     void F0();
//     void F1(int);
//     void F2(int, int);
//
//     BindCallbackF0 f0;
//     f0.Bind(&F0);
//     f0.Run();  // calls F0()
//
//     BindCallbackF1<int> f1;
//     f1.Bind(&F1, 10);
//     f1.Run();  // calls F1(10)
//
//     BindCallbackF2<int, int> f2;
//     f2.Bind(&F2, 10, 20);
//     f2.Run();  // calls F2(10, 20)
//
// Example 3: Callback with arguments
//
//     class Clazz
//     {
//     public:
//          void M1R2(int arg0, const std::string& result0, int result1);
//     };
//
//     Clazz obj;
//     BindCallbackM1R2<Clazz,
//         int /* arg0 */,
//         std::string /* result0 */,
//         int /* result1 */> callback;
//     callback.Bind(&obj, &Clazz::M1R2, 100 /* arg0 */);
//     callback.SetResult0(200);
//     callback.SetResult1("hello");
//     callback.Run();  // calls Clazz::M1R2(100, 200, "hello")

class BindCallbackR0 : public Closure<void> {
public:
    BindCallbackR0() {}
    virtual ~BindCallbackR0() {}

    bool IsSelfDelete() const override { return false; }

protected:
};

template <typename Result0>
class BindCallbackR1 : public Closure<void> {
public:
    BindCallbackR1() : result0_(Result0()) {}
    virtual ~BindCallbackR1() {}
    Result0 GetResult0() const { return result0_; }
    void SetResult0(Result0 r) { result0_ = r; }
    Result0* MutableResult0() { return &result0_; }

    bool IsSelfDelete() const override { return false; }

protected:
    Result0 result0_;
};

template <typename Result0, typename Result1>
class BindCallbackR2 : public Closure<void> {
public:
    BindCallbackR2() : result0_(Result0()), result1_(Result1()) {}
    virtual ~BindCallbackR2() {}
    Result0 GetResult0() const { return result0_; }
    void SetResult0(Result0 r) { result0_ = r; }
    Result0* MutableResult0() { return &result0_; }

    Result1 GetResult1() const { return result1_; }
    void SetResult1(Result1 r) { result1_ = r; }
    Result1* MutableResult1() { return &result1_; }

    bool IsSelfDelete() const override { return false; }

protected:
    Result0 result0_;
    Result1 result1_;
};

template <typename Result0, typename Result1, typename Result2>
class BindCallbackR3 : public Closure<void> {
public:
    BindCallbackR3() : result0_(Result0()), result1_(Result1()), result2_(Result2()) {}
    virtual ~BindCallbackR3() {}
    Result0 GetResult0() const { return result0_; }
    void SetResult0(Result0 r) { result0_ = r; }
    Result0* MutableResult0() { return &result0_; }

    Result1 GetResult1() const { return result1_; }
    void SetResult1(Result1 r) { result1_ = r; }
    Result1* MutableResult1() { return &result1_; }

    Result2 GetResult2() const { return result2_; }
    void SetResult2(Result2 r) { result2_ = r; }
    Result2* MutableResult2() { return &result2_; }

    bool IsSelfDelete() const override { return false; }

protected:
    Result0 result0_;
    Result1 result1_;
    Result2 result2_;
};

template <typename Class>
class BindCallbackM0 final : public BindCallbackR0 {
public:
    typedef void (Class::*Method)();

    BindCallbackM0()
        : magic_(bind_callback_internal::BIND_CALLBACK_ALIVE), object_(NULL), method_(NULL) {}

    ~BindCallbackM0() {
#ifndef NDEBUG
        BYTE_ASSERT(magic_ == bind_callback_internal::BIND_CALLBACK_ALIVE);
#endif  // NDEBUG
        magic_ = bind_callback_internal::BIND_CALLBACK_FREED;
    }

    void Bind(Class* object, Method method) {
#ifndef NDEBUG
        BYTE_ASSERT(magic_ == bind_callback_internal::BIND_CALLBACK_ALIVE);
#endif  // NDEBUG
        object_ = object;
        method_ = method;
    }

    /* override */ void Run() {
#ifndef NDEBUG
        BYTE_ASSERT(magic_ == bind_callback_internal::BIND_CALLBACK_ALIVE);
#endif  // NDEBUG
        (object_->*method_)();
    }

private:
    uint64_t magic_;
    Class* object_;
    Method method_;
};

template <typename Class, typename Result0>
class BindCallbackM0R1 final : public BindCallbackR1<Result0> {
public:
    typedef void (Class::*Method)(Result0);

    BindCallbackM0R1()
        : magic_(bind_callback_internal::BIND_CALLBACK_ALIVE), object_(NULL), method_(NULL) {}

    ~BindCallbackM0R1() {
#ifndef NDEBUG
        BYTE_ASSERT(magic_ == bind_callback_internal::BIND_CALLBACK_ALIVE);
#endif  // NDEBUG
        magic_ = bind_callback_internal::BIND_CALLBACK_FREED;
    }

    void Bind(Class* object, Method method) {
#ifndef NDEBUG
        BYTE_ASSERT(magic_ == bind_callback_internal::BIND_CALLBACK_ALIVE);
#endif  // NDEBUG
        object_ = object;
        method_ = method;
    }

    /* override */ void Run() {
#ifndef NDEBUG
        BYTE_ASSERT(magic_ == bind_callback_internal::BIND_CALLBACK_ALIVE);
#endif  // NDEBUG
        (object_->*method_)(this->result0_);
    }

private:
    uint64_t magic_;
    Class* object_;
    Method method_;
};

template <typename Class, typename Result0, typename Result1>
class BindCallbackM0R2 final : public BindCallbackR2<Result0, Result1> {
public:
    typedef void (Class::*Method)(Result0, Result1);

    BindCallbackM0R2()
        : magic_(bind_callback_internal::BIND_CALLBACK_ALIVE), object_(NULL), method_(NULL) {}

    ~BindCallbackM0R2() {
#ifndef NDEBUG
        BYTE_ASSERT(magic_ == bind_callback_internal::BIND_CALLBACK_ALIVE);
#endif  // NDEBUG
        magic_ = bind_callback_internal::BIND_CALLBACK_FREED;
    }

    void Bind(Class* object, Method method) {
#ifndef NDEBUG
        BYTE_ASSERT(magic_ == bind_callback_internal::BIND_CALLBACK_ALIVE);
#endif  // NDEBUG
        object_ = object;
        method_ = method;
    }

    /* override */ void Run() {
#ifndef NDEBUG
        BYTE_ASSERT(magic_ == bind_callback_internal::BIND_CALLBACK_ALIVE);
#endif  // NDEBUG
        (object_->*method_)(this->result0_, this->result1_);
    }

private:
    uint64_t magic_;
    Class* object_;
    Method method_;
};

template <typename Class, typename Result0, typename Result1, typename Result2>
class BindCallbackM0R3 final : public BindCallbackR3<Result0, Result1, Result2> {
public:
    typedef void (Class::*Method)(Result0, Result1, Result2);

    BindCallbackM0R3()
        : magic_(bind_callback_internal::BIND_CALLBACK_ALIVE), object_(NULL), method_(NULL) {}

    ~BindCallbackM0R3() {
#ifndef NDEBUG
        BYTE_ASSERT(magic_ == bind_callback_internal::BIND_CALLBACK_ALIVE);
#endif  // NDEBUG
        magic_ = bind_callback_internal::BIND_CALLBACK_FREED;
    }

    void Bind(Class* object, Method method) {
#ifndef NDEBUG
        BYTE_ASSERT(magic_ == bind_callback_internal::BIND_CALLBACK_ALIVE);
#endif  // NDEBUG
        object_ = object;
        method_ = method;
    }

    /* override */ void Run() {
#ifndef NDEBUG
        BYTE_ASSERT(magic_ == bind_callback_internal::BIND_CALLBACK_ALIVE);
#endif  // NDEBUG
        (object_->*method_)(this->result0_, this->result1_, this->result2_);
    }

private:
    uint64_t magic_;
    Class* object_;
    Method method_;
};

template <typename Class, typename Arg0>
class BindCallbackM1 final : public BindCallbackR0 {
public:
    typedef void (Class::*Method)(Arg0);

    BindCallbackM1()
        : magic_(bind_callback_internal::BIND_CALLBACK_ALIVE),
          object_(NULL),
          method_(NULL),
          arg0_(Arg0()) {}

    ~BindCallbackM1() {
#ifndef NDEBUG
        BYTE_ASSERT(magic_ == bind_callback_internal::BIND_CALLBACK_ALIVE);
#endif  // NDEBUG
        magic_ = bind_callback_internal::BIND_CALLBACK_FREED;
    }

    void Bind(Class* object, Method method, Arg0 arg0) {
#ifndef NDEBUG
        BYTE_ASSERT(magic_ == bind_callback_internal::BIND_CALLBACK_ALIVE);
#endif  // NDEBUG
        object_ = object;
        method_ = method;
        arg0_ = arg0;
    }

    /* override */ void Run() {
#ifndef NDEBUG
        BYTE_ASSERT(magic_ == bind_callback_internal::BIND_CALLBACK_ALIVE);
#endif  // NDEBUG
        (object_->*method_)(arg0_);
    }

private:
    uint64_t magic_;
    Class* object_;
    Method method_;
    Arg0 arg0_;
};

template <typename Class, typename Arg0, typename Result0>
class BindCallbackM1R1 final : public BindCallbackR1<Result0> {
public:
    typedef void (Class::*Method)(Arg0, Result0);

    BindCallbackM1R1()
        : magic_(bind_callback_internal::BIND_CALLBACK_ALIVE),
          object_(NULL),
          method_(NULL),
          arg0_(Arg0()) {}

    ~BindCallbackM1R1() {
#ifndef NDEBUG
        BYTE_ASSERT(magic_ == bind_callback_internal::BIND_CALLBACK_ALIVE);
#endif  // NDEBUG
        magic_ = bind_callback_internal::BIND_CALLBACK_FREED;
    }

    void Bind(Class* object, Method method, Arg0 arg0) {
#ifndef NDEBUG
        BYTE_ASSERT(magic_ == bind_callback_internal::BIND_CALLBACK_ALIVE);
#endif  // NDEBUG
        object_ = object;
        method_ = method;
        arg0_ = arg0;
    }

    /* override */ void Run() {
#ifndef NDEBUG
        BYTE_ASSERT(magic_ == bind_callback_internal::BIND_CALLBACK_ALIVE);
#endif  // NDEBUG
        (object_->*method_)(arg0_, this->result0_);
    }

private:
    uint64_t magic_;
    Class* object_;
    Method method_;
    Arg0 arg0_;
};

template <typename Class, typename Arg0, typename Result0, typename Result1>
class BindCallbackM1R2 final : public BindCallbackR2<Result0, Result1> {
public:
    typedef void (Class::*Method)(Arg0, Result0, Result1);

    BindCallbackM1R2()
        : magic_(bind_callback_internal::BIND_CALLBACK_ALIVE),
          object_(NULL),
          method_(NULL),
          arg0_(Arg0()) {}

    ~BindCallbackM1R2() {
#ifndef NDEBUG
        BYTE_ASSERT(magic_ == bind_callback_internal::BIND_CALLBACK_ALIVE);
#endif  // NDEBUG
        magic_ = bind_callback_internal::BIND_CALLBACK_FREED;
    }

    void Bind(Class* object, Method method, Arg0 arg0) {
#ifndef NDEBUG
        BYTE_ASSERT(magic_ == bind_callback_internal::BIND_CALLBACK_ALIVE);
#endif  // NDEBUG
        object_ = object;
        method_ = method;
        arg0_ = arg0;
    }

    /* override */ void Run() {
#ifndef NDEBUG
        BYTE_ASSERT(magic_ == bind_callback_internal::BIND_CALLBACK_ALIVE);
#endif  // NDEBUG
        (object_->*method_)(arg0_, this->result0_, this->result1_);
    }

private:
    uint64_t magic_;
    Class* object_;
    Method method_;
    Arg0 arg0_;
};

template <typename Class, typename Arg0, typename Result0, typename Result1, typename Result2>
class BindCallbackM1R3 final : public BindCallbackR3<Result0, Result1, Result2> {
public:
    typedef void (Class::*Method)(Arg0, Result0, Result1, Result2);

    BindCallbackM1R3()
        : magic_(bind_callback_internal::BIND_CALLBACK_ALIVE),
          object_(NULL),
          method_(NULL),
          arg0_(Arg0()) {}

    ~BindCallbackM1R3() {
#ifndef NDEBUG
        BYTE_ASSERT(magic_ == bind_callback_internal::BIND_CALLBACK_ALIVE);
#endif  // NDEBUG
        magic_ = bind_callback_internal::BIND_CALLBACK_FREED;
    }

    void Bind(Class* object, Method method, Arg0 arg0) {
#ifndef NDEBUG
        BYTE_ASSERT(magic_ == bind_callback_internal::BIND_CALLBACK_ALIVE);
#endif  // NDEBUG
        object_ = object;
        method_ = method;
        arg0_ = arg0;
    }

    /* override */ void Run() {
#ifndef NDEBUG
        BYTE_ASSERT(magic_ == bind_callback_internal::BIND_CALLBACK_ALIVE);
#endif  // NDEBUG
        (object_->*method_)(arg0_, this->result0_, this->result1_, this->result2_);
    }

private:
    uint64_t magic_;
    Class* object_;
    Method method_;
    Arg0 arg0_;
};

template <typename Class, typename Arg0, typename Arg1>
class BindCallbackM2 final : public BindCallbackR0 {
public:
    typedef void (Class::*Method)(Arg0, Arg1);

    BindCallbackM2()
        : magic_(bind_callback_internal::BIND_CALLBACK_ALIVE),
          object_(NULL),
          method_(NULL),
          arg0_(Arg0()),
          arg1_(Arg1()) {}

    ~BindCallbackM2() {
#ifndef NDEBUG
        BYTE_ASSERT(magic_ == bind_callback_internal::BIND_CALLBACK_ALIVE);
#endif  // NDEBUG
        magic_ = bind_callback_internal::BIND_CALLBACK_FREED;
    }

    void Bind(Class* object, Method method, Arg0 arg0, Arg1 arg1) {
#ifndef NDEBUG
        BYTE_ASSERT(magic_ == bind_callback_internal::BIND_CALLBACK_ALIVE);
#endif  // NDEBUG
        object_ = object;
        method_ = method;
        arg0_ = arg0;
        arg1_ = arg1;
    }

    /* override */ void Run() {
#ifndef NDEBUG
        BYTE_ASSERT(magic_ == bind_callback_internal::BIND_CALLBACK_ALIVE);
#endif  // NDEBUG
        (object_->*method_)(arg0_, arg1_);
    }

private:
    uint64_t magic_;
    Class* object_;
    Method method_;
    Arg0 arg0_;
    Arg1 arg1_;
};

template <typename Class, typename Arg0, typename Arg1, typename Result0>
class BindCallbackM2R1 final : public BindCallbackR1<Result0> {
public:
    typedef void (Class::*Method)(Arg0, Arg1, Result0);

    BindCallbackM2R1()
        : magic_(bind_callback_internal::BIND_CALLBACK_ALIVE),
          object_(NULL),
          method_(NULL),
          arg0_(Arg0()),
          arg1_(Arg1()) {}

    ~BindCallbackM2R1() {
#ifndef NDEBUG
        BYTE_ASSERT(magic_ == bind_callback_internal::BIND_CALLBACK_ALIVE);
#endif  // NDEBUG
        magic_ = bind_callback_internal::BIND_CALLBACK_FREED;
    }

    void Bind(Class* object, Method method, Arg0 arg0, Arg1 arg1) {
#ifndef NDEBUG
        BYTE_ASSERT(magic_ == bind_callback_internal::BIND_CALLBACK_ALIVE);
#endif  // NDEBUG
        object_ = object;
        method_ = method;
        arg0_ = arg0;
        arg1_ = arg1;
    }

    /* override */ void Run() {
#ifndef NDEBUG
        BYTE_ASSERT(magic_ == bind_callback_internal::BIND_CALLBACK_ALIVE);
#endif  // NDEBUG
        (object_->*method_)(arg0_, arg1_, this->result0_);
    }

private:
    uint64_t magic_;
    Class* object_;
    Method method_;
    Arg0 arg0_;
    Arg1 arg1_;
};

template <typename Class, typename Arg0, typename Arg1, typename Result0, typename Result1>
class BindCallbackM2R2 final : public BindCallbackR2<Result0, Result1> {
public:
    typedef void (Class::*Method)(Arg0, Arg1, Result0, Result1);

    BindCallbackM2R2()
        : magic_(bind_callback_internal::BIND_CALLBACK_ALIVE),
          object_(NULL),
          method_(NULL),
          arg0_(Arg0()),
          arg1_(Arg1()) {}

    ~BindCallbackM2R2() {
#ifndef NDEBUG
        BYTE_ASSERT(magic_ == bind_callback_internal::BIND_CALLBACK_ALIVE);
#endif  // NDEBUG
        magic_ = bind_callback_internal::BIND_CALLBACK_FREED;
    }

    void Bind(Class* object, Method method, Arg0 arg0, Arg1 arg1) {
#ifndef NDEBUG
        BYTE_ASSERT(magic_ == bind_callback_internal::BIND_CALLBACK_ALIVE);
#endif  // NDEBUG
        object_ = object;
        method_ = method;
        arg0_ = arg0;
        arg1_ = arg1;
    }

    /* override */ void Run() {
#ifndef NDEBUG
        BYTE_ASSERT(magic_ == bind_callback_internal::BIND_CALLBACK_ALIVE);
#endif  // NDEBUG
        (object_->*method_)(arg0_, arg1_, this->result0_, this->result1_);
    }

private:
    uint64_t magic_;
    Class* object_;
    Method method_;
    Arg0 arg0_;
    Arg1 arg1_;
};

template <typename Class, typename Arg0, typename Arg1, typename Result0, typename Result1,
          typename Result2>
class BindCallbackM2R3 final : public BindCallbackR3<Result0, Result1, Result2> {
public:
    typedef void (Class::*Method)(Arg0, Arg1, Result0, Result1, Result2);

    BindCallbackM2R3()
        : magic_(bind_callback_internal::BIND_CALLBACK_ALIVE),
          object_(NULL),
          method_(NULL),
          arg0_(Arg0()),
          arg1_(Arg1()) {}

    ~BindCallbackM2R3() {
#ifndef NDEBUG
        BYTE_ASSERT(magic_ == bind_callback_internal::BIND_CALLBACK_ALIVE);
#endif  // NDEBUG
        magic_ = bind_callback_internal::BIND_CALLBACK_FREED;
    }

    void Bind(Class* object, Method method, Arg0 arg0, Arg1 arg1) {
#ifndef NDEBUG
        BYTE_ASSERT(magic_ == bind_callback_internal::BIND_CALLBACK_ALIVE);
#endif  // NDEBUG
        object_ = object;
        method_ = method;
        arg0_ = arg0;
        arg1_ = arg1;
    }

    /* override */ void Run() {
#ifndef NDEBUG
        BYTE_ASSERT(magic_ == bind_callback_internal::BIND_CALLBACK_ALIVE);
#endif  // NDEBUG
        (object_->*method_)(arg0_, arg1_, this->result0_, this->result1_, this->result2_);
    }

private:
    uint64_t magic_;
    Class* object_;
    Method method_;
    Arg0 arg0_;
    Arg1 arg1_;
};

template <typename Class, typename Arg0, typename Arg1, typename Arg2>
class BindCallbackM3 final : public BindCallbackR0 {
public:
    typedef void (Class::*Method)(Arg0, Arg1, Arg2);

    BindCallbackM3()
        : magic_(bind_callback_internal::BIND_CALLBACK_ALIVE),
          object_(NULL),
          method_(NULL),
          arg0_(Arg0()),
          arg1_(Arg1()),
          arg2_(Arg2()) {}

    ~BindCallbackM3() {
#ifndef NDEBUG
        BYTE_ASSERT(magic_ == bind_callback_internal::BIND_CALLBACK_ALIVE);
#endif  // NDEBUG
        magic_ = bind_callback_internal::BIND_CALLBACK_FREED;
    }

    void Bind(Class* object, Method method, Arg0 arg0, Arg1 arg1, Arg2 arg2) {
#ifndef NDEBUG
        BYTE_ASSERT(magic_ == bind_callback_internal::BIND_CALLBACK_ALIVE);
#endif  // NDEBUG
        object_ = object;
        method_ = method;
        arg0_ = arg0;
        arg1_ = arg1;
        arg2_ = arg2;
    }

    /* override */ void Run() {
#ifndef NDEBUG
        BYTE_ASSERT(magic_ == bind_callback_internal::BIND_CALLBACK_ALIVE);
#endif  // NDEBUG
        (object_->*method_)(arg0_, arg1_, arg2_);
    }

private:
    uint64_t magic_;
    Class* object_;
    Method method_;
    Arg0 arg0_;
    Arg1 arg1_;
    Arg2 arg2_;
};

template <typename Class, typename Arg0, typename Arg1, typename Arg2, typename Result0>
class BindCallbackM3R1 final : public BindCallbackR1<Result0> {
public:
    typedef void (Class::*Method)(Arg0, Arg1, Arg2, Result0);

    BindCallbackM3R1()
        : magic_(bind_callback_internal::BIND_CALLBACK_ALIVE),
          object_(NULL),
          method_(NULL),
          arg0_(Arg0()),
          arg1_(Arg1()),
          arg2_(Arg2()) {}

    ~BindCallbackM3R1() {
#ifndef NDEBUG
        BYTE_ASSERT(magic_ == bind_callback_internal::BIND_CALLBACK_ALIVE);
#endif  // NDEBUG
        magic_ = bind_callback_internal::BIND_CALLBACK_FREED;
    }

    void Bind(Class* object, Method method, Arg0 arg0, Arg1 arg1, Arg2 arg2) {
#ifndef NDEBUG
        BYTE_ASSERT(magic_ == bind_callback_internal::BIND_CALLBACK_ALIVE);
#endif  // NDEBUG
        object_ = object;
        method_ = method;
        arg0_ = arg0;
        arg1_ = arg1;
        arg2_ = arg2;
    }

    /* override */ void Run() {
#ifndef NDEBUG
        BYTE_ASSERT(magic_ == bind_callback_internal::BIND_CALLBACK_ALIVE);
#endif  // NDEBUG
        (object_->*method_)(arg0_, arg1_, arg2_, this->result0_);
    }

private:
    uint64_t magic_;
    Class* object_;
    Method method_;
    Arg0 arg0_;
    Arg1 arg1_;
    Arg2 arg2_;
};

template <typename Class, typename Arg0, typename Arg1, typename Arg2, typename Result0,
          typename Result1>
class BindCallbackM3R2 final : public BindCallbackR2<Result0, Result1> {
public:
    typedef void (Class::*Method)(Arg0, Arg1, Arg2, Result0, Result1);

    BindCallbackM3R2()
        : magic_(bind_callback_internal::BIND_CALLBACK_ALIVE),
          object_(NULL),
          method_(NULL),
          arg0_(Arg0()),
          arg1_(Arg1()),
          arg2_(Arg2()) {}

    ~BindCallbackM3R2() {
#ifndef NDEBUG
        BYTE_ASSERT(magic_ == bind_callback_internal::BIND_CALLBACK_ALIVE);
#endif  // NDEBUG
        magic_ = bind_callback_internal::BIND_CALLBACK_FREED;
    }

    void Bind(Class* object, Method method, Arg0 arg0, Arg1 arg1, Arg2 arg2) {
#ifndef NDEBUG
        BYTE_ASSERT(magic_ == bind_callback_internal::BIND_CALLBACK_ALIVE);
#endif  // NDEBUG
        object_ = object;
        method_ = method;
        arg0_ = arg0;
        arg1_ = arg1;
        arg2_ = arg2;
    }

    /* override */ void Run() {
#ifndef NDEBUG
        BYTE_ASSERT(magic_ == bind_callback_internal::BIND_CALLBACK_ALIVE);
#endif  // NDEBUG
        (object_->*method_)(arg0_, arg1_, arg2_, this->result0_, this->result1_);
    }

private:
    uint64_t magic_;
    Class* object_;
    Method method_;
    Arg0 arg0_;
    Arg1 arg1_;
    Arg2 arg2_;
};

template <typename Class, typename Arg0, typename Arg1, typename Arg2, typename Result0,
          typename Result1, typename Result2>
class BindCallbackM3R3 final : public BindCallbackR3<Result0, Result1, Result2> {
public:
    typedef void (Class::*Method)(Arg0, Arg1, Arg2, Result0, Result1, Result2);

    BindCallbackM3R3()
        : magic_(bind_callback_internal::BIND_CALLBACK_ALIVE),
          object_(NULL),
          method_(NULL),
          arg0_(Arg0()),
          arg1_(Arg1()),
          arg2_(Arg2()) {}

    ~BindCallbackM3R3() {
#ifndef NDEBUG
        BYTE_ASSERT(magic_ == bind_callback_internal::BIND_CALLBACK_ALIVE);
#endif  // NDEBUG
        magic_ = bind_callback_internal::BIND_CALLBACK_FREED;
    }

    void Bind(Class* object, Method method, Arg0 arg0, Arg1 arg1, Arg2 arg2) {
#ifndef NDEBUG
        BYTE_ASSERT(magic_ == bind_callback_internal::BIND_CALLBACK_ALIVE);
#endif  // NDEBUG
        object_ = object;
        method_ = method;
        arg0_ = arg0;
        arg1_ = arg1;
        arg2_ = arg2;
    }

    /* override */ void Run() {
#ifndef NDEBUG
        BYTE_ASSERT(magic_ == bind_callback_internal::BIND_CALLBACK_ALIVE);
#endif  // NDEBUG
        (object_->*method_)(arg0_, arg1_, arg2_, this->result0_, this->result1_, this->result2_);
    }

private:
    uint64_t magic_;
    Class* object_;
    Method method_;
    Arg0 arg0_;
    Arg1 arg1_;
    Arg2 arg2_;
};

template <typename Class, typename Arg0, typename Arg1, typename Arg2, typename Arg3>
class BindCallbackM4 final : public BindCallbackR0 {
public:
    typedef void (Class::*Method)(Arg0, Arg1, Arg2, Arg3);

    BindCallbackM4()
        : magic_(bind_callback_internal::BIND_CALLBACK_ALIVE),
          object_(NULL),
          method_(NULL),
          arg0_(Arg0()),
          arg1_(Arg1()),
          arg2_(Arg2()),
          arg3_(Arg3()) {}

    ~BindCallbackM4() {
#ifndef NDEBUG
        BYTE_ASSERT(magic_ == bind_callback_internal::BIND_CALLBACK_ALIVE);
#endif  // NDEBUG
        magic_ = bind_callback_internal::BIND_CALLBACK_FREED;
    }

    void Bind(Class* object, Method method, Arg0 arg0, Arg1 arg1, Arg2 arg2, Arg3 arg3) {
#ifndef NDEBUG
        BYTE_ASSERT(magic_ == bind_callback_internal::BIND_CALLBACK_ALIVE);
#endif  // NDEBUG
        object_ = object;
        method_ = method;
        arg0_ = arg0;
        arg1_ = arg1;
        arg2_ = arg2;
        arg3_ = arg3;
    }

    /* override */ void Run() {
#ifndef NDEBUG
        BYTE_ASSERT(magic_ == bind_callback_internal::BIND_CALLBACK_ALIVE);
#endif  // NDEBUG
        (object_->*method_)(arg0_, arg1_, arg2_, arg3_);
    }

private:
    uint64_t magic_;
    Class* object_;
    Method method_;
    Arg0 arg0_;
    Arg1 arg1_;
    Arg2 arg2_;
    Arg3 arg3_;
};

template <typename Class, typename Arg0, typename Arg1, typename Arg2, typename Arg3,
          typename Result0>
class BindCallbackM4R1 final : public BindCallbackR1<Result0> {
public:
    typedef void (Class::*Method)(Arg0, Arg1, Arg2, Arg3, Result0);

    BindCallbackM4R1()
        : magic_(bind_callback_internal::BIND_CALLBACK_ALIVE),
          object_(NULL),
          method_(NULL),
          arg0_(Arg0()),
          arg1_(Arg1()),
          arg2_(Arg2()),
          arg3_(Arg3()) {}

    ~BindCallbackM4R1() {
#ifndef NDEBUG
        BYTE_ASSERT(magic_ == bind_callback_internal::BIND_CALLBACK_ALIVE);
#endif  // NDEBUG
        magic_ = bind_callback_internal::BIND_CALLBACK_FREED;
    }

    void Bind(Class* object, Method method, Arg0 arg0, Arg1 arg1, Arg2 arg2, Arg3 arg3) {
#ifndef NDEBUG
        BYTE_ASSERT(magic_ == bind_callback_internal::BIND_CALLBACK_ALIVE);
#endif  // NDEBUG
        object_ = object;
        method_ = method;
        arg0_ = arg0;
        arg1_ = arg1;
        arg2_ = arg2;
        arg3_ = arg3;
    }

    /* override */ void Run() {
#ifndef NDEBUG
        BYTE_ASSERT(magic_ == bind_callback_internal::BIND_CALLBACK_ALIVE);
#endif  // NDEBUG
        (object_->*method_)(arg0_, arg1_, arg2_, arg3_, this->result0_);
    }

private:
    uint64_t magic_;
    Class* object_;
    Method method_;
    Arg0 arg0_;
    Arg1 arg1_;
    Arg2 arg2_;
    Arg3 arg3_;
};

template <typename Class, typename Arg0, typename Arg1, typename Arg2, typename Arg3,
          typename Result0, typename Result1>
class BindCallbackM4R2 final : public BindCallbackR2<Result0, Result1> {
public:
    typedef void (Class::*Method)(Arg0, Arg1, Arg2, Arg3, Result0, Result1);

    BindCallbackM4R2()
        : magic_(bind_callback_internal::BIND_CALLBACK_ALIVE),
          object_(NULL),
          method_(NULL),
          arg0_(Arg0()),
          arg1_(Arg1()),
          arg2_(Arg2()),
          arg3_(Arg3()) {}

    ~BindCallbackM4R2() {
#ifndef NDEBUG
        BYTE_ASSERT(magic_ == bind_callback_internal::BIND_CALLBACK_ALIVE);
#endif  // NDEBUG
        magic_ = bind_callback_internal::BIND_CALLBACK_FREED;
    }

    void Bind(Class* object, Method method, Arg0 arg0, Arg1 arg1, Arg2 arg2, Arg3 arg3) {
#ifndef NDEBUG
        BYTE_ASSERT(magic_ == bind_callback_internal::BIND_CALLBACK_ALIVE);
#endif  // NDEBUG
        object_ = object;
        method_ = method;
        arg0_ = arg0;
        arg1_ = arg1;
        arg2_ = arg2;
        arg3_ = arg3;
    }

    /* override */ void Run() {
#ifndef NDEBUG
        BYTE_ASSERT(magic_ == bind_callback_internal::BIND_CALLBACK_ALIVE);
#endif  // NDEBUG
        (object_->*method_)(arg0_, arg1_, arg2_, arg3_, this->result0_, this->result1_);
    }

private:
    uint64_t magic_;
    Class* object_;
    Method method_;
    Arg0 arg0_;
    Arg1 arg1_;
    Arg2 arg2_;
    Arg3 arg3_;
};

template <typename Class, typename Arg0, typename Arg1, typename Arg2, typename Arg3,
          typename Result0, typename Result1, typename Result2>
class BindCallbackM4R3 final : public BindCallbackR3<Result0, Result1, Result2> {
public:
    typedef void (Class::*Method)(Arg0, Arg1, Arg2, Arg3, Result0, Result1, Result2);

    BindCallbackM4R3()
        : magic_(bind_callback_internal::BIND_CALLBACK_ALIVE),
          object_(NULL),
          method_(NULL),
          arg0_(Arg0()),
          arg1_(Arg1()),
          arg2_(Arg2()),
          arg3_(Arg3()) {}

    ~BindCallbackM4R3() {
#ifndef NDEBUG
        BYTE_ASSERT(magic_ == bind_callback_internal::BIND_CALLBACK_ALIVE);
#endif  // NDEBUG
        magic_ = bind_callback_internal::BIND_CALLBACK_FREED;
    }

    void Bind(Class* object, Method method, Arg0 arg0, Arg1 arg1, Arg2 arg2, Arg3 arg3) {
#ifndef NDEBUG
        BYTE_ASSERT(magic_ == bind_callback_internal::BIND_CALLBACK_ALIVE);
#endif  // NDEBUG
        object_ = object;
        method_ = method;
        arg0_ = arg0;
        arg1_ = arg1;
        arg2_ = arg2;
        arg3_ = arg3;
    }

    /* override */ void Run() {
#ifndef NDEBUG
        BYTE_ASSERT(magic_ == bind_callback_internal::BIND_CALLBACK_ALIVE);
#endif  // NDEBUG
        (object_->*method_)(arg0_, arg1_, arg2_, arg3_, this->result0_, this->result1_,
                            this->result2_);
    }

private:
    uint64_t magic_;
    Class* object_;
    Method method_;
    Arg0 arg0_;
    Arg1 arg1_;
    Arg2 arg2_;
    Arg3 arg3_;
};

template <typename Class, typename Arg0, typename Arg1, typename Arg2, typename Arg3, typename Arg4>
class BindCallbackM5 final : public BindCallbackR0 {
public:
    typedef void (Class::*Method)(Arg0, Arg1, Arg2, Arg3, Arg4);

    BindCallbackM5()
        : magic_(bind_callback_internal::BIND_CALLBACK_ALIVE),
          object_(NULL),
          method_(NULL),
          arg0_(Arg0()),
          arg1_(Arg1()),
          arg2_(Arg2()),
          arg3_(Arg3()),
          arg4_(Arg4()) {}

    ~BindCallbackM5() {
#ifndef NDEBUG
        BYTE_ASSERT(magic_ == bind_callback_internal::BIND_CALLBACK_ALIVE);
#endif  // NDEBUG
        magic_ = bind_callback_internal::BIND_CALLBACK_FREED;
    }

    void Bind(Class* object, Method method, Arg0 arg0, Arg1 arg1, Arg2 arg2, Arg3 arg3, Arg4 arg4) {
#ifndef NDEBUG
        BYTE_ASSERT(magic_ == bind_callback_internal::BIND_CALLBACK_ALIVE);
#endif  // NDEBUG
        object_ = object;
        method_ = method;
        arg0_ = arg0;
        arg1_ = arg1;
        arg2_ = arg2;
        arg3_ = arg3;
        arg4_ = arg4;
    }

    /* override */ void Run() {
#ifndef NDEBUG
        BYTE_ASSERT(magic_ == bind_callback_internal::BIND_CALLBACK_ALIVE);
#endif  // NDEBUG
        (object_->*method_)(arg0_, arg1_, arg2_, arg3_, arg4_);
    }

private:
    uint64_t magic_;
    Class* object_;
    Method method_;
    Arg0 arg0_;
    Arg1 arg1_;
    Arg2 arg2_;
    Arg3 arg3_;
    Arg4 arg4_;
};

template <typename Class, typename Arg0, typename Arg1, typename Arg2, typename Arg3, typename Arg4,
          typename Result0>
class BindCallbackM5R1 final : public BindCallbackR1<Result0> {
public:
    typedef void (Class::*Method)(Arg0, Arg1, Arg2, Arg3, Arg4, Result0);

    BindCallbackM5R1()
        : magic_(bind_callback_internal::BIND_CALLBACK_ALIVE),
          object_(NULL),
          method_(NULL),
          arg0_(Arg0()),
          arg1_(Arg1()),
          arg2_(Arg2()),
          arg3_(Arg3()),
          arg4_(Arg4()) {}

    ~BindCallbackM5R1() {
#ifndef NDEBUG
        BYTE_ASSERT(magic_ == bind_callback_internal::BIND_CALLBACK_ALIVE);
#endif  // NDEBUG
        magic_ = bind_callback_internal::BIND_CALLBACK_FREED;
    }

    void Bind(Class* object, Method method, Arg0 arg0, Arg1 arg1, Arg2 arg2, Arg3 arg3, Arg4 arg4) {
#ifndef NDEBUG
        BYTE_ASSERT(magic_ == bind_callback_internal::BIND_CALLBACK_ALIVE);
#endif  // NDEBUG
        object_ = object;
        method_ = method;
        arg0_ = arg0;
        arg1_ = arg1;
        arg2_ = arg2;
        arg3_ = arg3;
        arg4_ = arg4;
    }

    /* override */ void Run() {
#ifndef NDEBUG
        BYTE_ASSERT(magic_ == bind_callback_internal::BIND_CALLBACK_ALIVE);
#endif  // NDEBUG
        (object_->*method_)(arg0_, arg1_, arg2_, arg3_, arg4_, this->result0_);
    }

private:
    uint64_t magic_;
    Class* object_;
    Method method_;
    Arg0 arg0_;
    Arg1 arg1_;
    Arg2 arg2_;
    Arg3 arg3_;
    Arg4 arg4_;
};

template <typename Class, typename Arg0, typename Arg1, typename Arg2, typename Arg3, typename Arg4,
          typename Result0, typename Result1>
class BindCallbackM5R2 final : public BindCallbackR2<Result0, Result1> {
public:
    typedef void (Class::*Method)(Arg0, Arg1, Arg2, Arg3, Arg4, Result0, Result1);

    BindCallbackM5R2()
        : magic_(bind_callback_internal::BIND_CALLBACK_ALIVE),
          object_(NULL),
          method_(NULL),
          arg0_(Arg0()),
          arg1_(Arg1()),
          arg2_(Arg2()),
          arg3_(Arg3()),
          arg4_(Arg4()) {}

    ~BindCallbackM5R2() {
#ifndef NDEBUG
        BYTE_ASSERT(magic_ == bind_callback_internal::BIND_CALLBACK_ALIVE);
#endif  // NDEBUG
        magic_ = bind_callback_internal::BIND_CALLBACK_FREED;
    }

    void Bind(Class* object, Method method, Arg0 arg0, Arg1 arg1, Arg2 arg2, Arg3 arg3, Arg4 arg4) {
#ifndef NDEBUG
        BYTE_ASSERT(magic_ == bind_callback_internal::BIND_CALLBACK_ALIVE);
#endif  // NDEBUG
        object_ = object;
        method_ = method;
        arg0_ = arg0;
        arg1_ = arg1;
        arg2_ = arg2;
        arg3_ = arg3;
        arg4_ = arg4;
    }

    /* override */ void Run() {
#ifndef NDEBUG
        BYTE_ASSERT(magic_ == bind_callback_internal::BIND_CALLBACK_ALIVE);
#endif  // NDEBUG
        (object_->*method_)(arg0_, arg1_, arg2_, arg3_, arg4_, this->result0_, this->result1_);
    }

private:
    uint64_t magic_;
    Class* object_;
    Method method_;
    Arg0 arg0_;
    Arg1 arg1_;
    Arg2 arg2_;
    Arg3 arg3_;
    Arg4 arg4_;
};

template <typename Class, typename Arg0, typename Arg1, typename Arg2, typename Arg3, typename Arg4,
          typename Result0, typename Result1, typename Result2>
class BindCallbackM5R3 final : public BindCallbackR3<Result0, Result1, Result2> {
public:
    typedef void (Class::*Method)(Arg0, Arg1, Arg2, Arg3, Arg4, Result0, Result1, Result2);

    BindCallbackM5R3()
        : magic_(bind_callback_internal::BIND_CALLBACK_ALIVE),
          object_(NULL),
          method_(NULL),
          arg0_(Arg0()),
          arg1_(Arg1()),
          arg2_(Arg2()),
          arg3_(Arg3()),
          arg4_(Arg4()) {}

    ~BindCallbackM5R3() {
#ifndef NDEBUG
        BYTE_ASSERT(magic_ == bind_callback_internal::BIND_CALLBACK_ALIVE);
#endif  // NDEBUG
        magic_ = bind_callback_internal::BIND_CALLBACK_FREED;
    }

    void Bind(Class* object, Method method, Arg0 arg0, Arg1 arg1, Arg2 arg2, Arg3 arg3, Arg4 arg4) {
#ifndef NDEBUG
        BYTE_ASSERT(magic_ == bind_callback_internal::BIND_CALLBACK_ALIVE);
#endif  // NDEBUG
        object_ = object;
        method_ = method;
        arg0_ = arg0;
        arg1_ = arg1;
        arg2_ = arg2;
        arg3_ = arg3;
        arg4_ = arg4;
    }

    /* override */ void Run() {
#ifndef NDEBUG
        BYTE_ASSERT(magic_ == bind_callback_internal::BIND_CALLBACK_ALIVE);
#endif  // NDEBUG
        (object_->*method_)(arg0_, arg1_, arg2_, arg3_, arg4_, this->result0_, this->result1_,
                            this->result2_);
    }

private:
    uint64_t magic_;
    Class* object_;
    Method method_;
    Arg0 arg0_;
    Arg1 arg1_;
    Arg2 arg2_;
    Arg3 arg3_;
    Arg4 arg4_;
};

template <typename Class, typename Arg0, typename Arg1, typename Arg2, typename Arg3, typename Arg4,
          typename Arg5>
class BindCallbackM6 final : public BindCallbackR0 {
public:
    typedef void (Class::*Method)(Arg0, Arg1, Arg2, Arg3, Arg4, Arg5);

    BindCallbackM6()
        : magic_(bind_callback_internal::BIND_CALLBACK_ALIVE),
          object_(NULL),
          method_(NULL),
          arg0_(Arg0()),
          arg1_(Arg1()),
          arg2_(Arg2()),
          arg3_(Arg3()),
          arg4_(Arg4()),
          arg5_(Arg5()) {}

    ~BindCallbackM6() {
#ifndef NDEBUG
        BYTE_ASSERT(magic_ == bind_callback_internal::BIND_CALLBACK_ALIVE);
#endif  // NDEBUG
        magic_ = bind_callback_internal::BIND_CALLBACK_FREED;
    }

    void Bind(Class* object, Method method, Arg0 arg0, Arg1 arg1, Arg2 arg2, Arg3 arg3, Arg4 arg4,
              Arg5 arg5) {
#ifndef NDEBUG
        BYTE_ASSERT(magic_ == bind_callback_internal::BIND_CALLBACK_ALIVE);
#endif  // NDEBUG
        object_ = object;
        method_ = method;
        arg0_ = arg0;
        arg1_ = arg1;
        arg2_ = arg2;
        arg3_ = arg3;
        arg4_ = arg4;
        arg5_ = arg5;
    }

    /* override */ void Run() {
#ifndef NDEBUG
        BYTE_ASSERT(magic_ == bind_callback_internal::BIND_CALLBACK_ALIVE);
#endif  // NDEBUG
        (object_->*method_)(arg0_, arg1_, arg2_, arg3_, arg4_, arg5_);
    }

private:
    uint64_t magic_;
    Class* object_;
    Method method_;
    Arg0 arg0_;
    Arg1 arg1_;
    Arg2 arg2_;
    Arg3 arg3_;
    Arg4 arg4_;
    Arg5 arg5_;
};

template <typename Class, typename Arg0, typename Arg1, typename Arg2, typename Arg3, typename Arg4,
          typename Arg5, typename Result0>
class BindCallbackM6R1 final : public BindCallbackR1<Result0> {
public:
    typedef void (Class::*Method)(Arg0, Arg1, Arg2, Arg3, Arg4, Arg5, Result0);

    BindCallbackM6R1()
        : magic_(bind_callback_internal::BIND_CALLBACK_ALIVE),
          object_(NULL),
          method_(NULL),
          arg0_(Arg0()),
          arg1_(Arg1()),
          arg2_(Arg2()),
          arg3_(Arg3()),
          arg4_(Arg4()),
          arg5_(Arg5()) {}

    ~BindCallbackM6R1() {
#ifndef NDEBUG
        BYTE_ASSERT(magic_ == bind_callback_internal::BIND_CALLBACK_ALIVE);
#endif  // NDEBUG
        magic_ = bind_callback_internal::BIND_CALLBACK_FREED;
    }

    void Bind(Class* object, Method method, Arg0 arg0, Arg1 arg1, Arg2 arg2, Arg3 arg3, Arg4 arg4,
              Arg5 arg5) {
#ifndef NDEBUG
        BYTE_ASSERT(magic_ == bind_callback_internal::BIND_CALLBACK_ALIVE);
#endif  // NDEBUG
        object_ = object;
        method_ = method;
        arg0_ = arg0;
        arg1_ = arg1;
        arg2_ = arg2;
        arg3_ = arg3;
        arg4_ = arg4;
        arg5_ = arg5;
    }

    /* override */ void Run() {
#ifndef NDEBUG
        BYTE_ASSERT(magic_ == bind_callback_internal::BIND_CALLBACK_ALIVE);
#endif  // NDEBUG
        (object_->*method_)(arg0_, arg1_, arg2_, arg3_, arg4_, arg5_, this->result0_);
    }

private:
    uint64_t magic_;
    Class* object_;
    Method method_;
    Arg0 arg0_;
    Arg1 arg1_;
    Arg2 arg2_;
    Arg3 arg3_;
    Arg4 arg4_;
    Arg5 arg5_;
};

template <typename Class, typename Arg0, typename Arg1, typename Arg2, typename Arg3, typename Arg4,
          typename Arg5, typename Result0, typename Result1>
class BindCallbackM6R2 final : public BindCallbackR2<Result0, Result1> {
public:
    typedef void (Class::*Method)(Arg0, Arg1, Arg2, Arg3, Arg4, Arg5, Result0, Result1);

    BindCallbackM6R2()
        : magic_(bind_callback_internal::BIND_CALLBACK_ALIVE),
          object_(NULL),
          method_(NULL),
          arg0_(Arg0()),
          arg1_(Arg1()),
          arg2_(Arg2()),
          arg3_(Arg3()),
          arg4_(Arg4()),
          arg5_(Arg5()) {}

    ~BindCallbackM6R2() {
#ifndef NDEBUG
        BYTE_ASSERT(magic_ == bind_callback_internal::BIND_CALLBACK_ALIVE);
#endif  // NDEBUG
        magic_ = bind_callback_internal::BIND_CALLBACK_FREED;
    }

    void Bind(Class* object, Method method, Arg0 arg0, Arg1 arg1, Arg2 arg2, Arg3 arg3, Arg4 arg4,
              Arg5 arg5) {
#ifndef NDEBUG
        BYTE_ASSERT(magic_ == bind_callback_internal::BIND_CALLBACK_ALIVE);
#endif  // NDEBUG
        object_ = object;
        method_ = method;
        arg0_ = arg0;
        arg1_ = arg1;
        arg2_ = arg2;
        arg3_ = arg3;
        arg4_ = arg4;
        arg5_ = arg5;
    }

    /* override */ void Run() {
#ifndef NDEBUG
        BYTE_ASSERT(magic_ == bind_callback_internal::BIND_CALLBACK_ALIVE);
#endif  // NDEBUG
        (object_->*method_)(arg0_, arg1_, arg2_, arg3_, arg4_, arg5_, this->result0_,
                            this->result1_);
    }

private:
    uint64_t magic_;
    Class* object_;
    Method method_;
    Arg0 arg0_;
    Arg1 arg1_;
    Arg2 arg2_;
    Arg3 arg3_;
    Arg4 arg4_;
    Arg5 arg5_;
};

template <typename Class, typename Arg0, typename Arg1, typename Arg2, typename Arg3, typename Arg4,
          typename Arg5, typename Result0, typename Result1, typename Result2>
class BindCallbackM6R3 final : public BindCallbackR3<Result0, Result1, Result2> {
public:
    typedef void (Class::*Method)(Arg0, Arg1, Arg2, Arg3, Arg4, Arg5, Result0, Result1, Result2);

    BindCallbackM6R3()
        : magic_(bind_callback_internal::BIND_CALLBACK_ALIVE),
          object_(NULL),
          method_(NULL),
          arg0_(Arg0()),
          arg1_(Arg1()),
          arg2_(Arg2()),
          arg3_(Arg3()),
          arg4_(Arg4()),
          arg5_(Arg5()) {}

    ~BindCallbackM6R3() {
#ifndef NDEBUG
        BYTE_ASSERT(magic_ == bind_callback_internal::BIND_CALLBACK_ALIVE);
#endif  // NDEBUG
        magic_ = bind_callback_internal::BIND_CALLBACK_FREED;
    }

    void Bind(Class* object, Method method, Arg0 arg0, Arg1 arg1, Arg2 arg2, Arg3 arg3, Arg4 arg4,
              Arg5 arg5) {
#ifndef NDEBUG
        BYTE_ASSERT(magic_ == bind_callback_internal::BIND_CALLBACK_ALIVE);
#endif  // NDEBUG
        object_ = object;
        method_ = method;
        arg0_ = arg0;
        arg1_ = arg1;
        arg2_ = arg2;
        arg3_ = arg3;
        arg4_ = arg4;
        arg5_ = arg5;
    }

    /* override */ void Run() {
#ifndef NDEBUG
        BYTE_ASSERT(magic_ == bind_callback_internal::BIND_CALLBACK_ALIVE);
#endif  // NDEBUG
        (object_->*method_)(arg0_, arg1_, arg2_, arg3_, arg4_, arg5_, this->result0_,
                            this->result1_, this->result2_);
    }

private:
    uint64_t magic_;
    Class* object_;
    Method method_;
    Arg0 arg0_;
    Arg1 arg1_;
    Arg2 arg2_;
    Arg3 arg3_;
    Arg4 arg4_;
    Arg5 arg5_;
};

template <typename Class, typename Arg0, typename Arg1, typename Arg2, typename Arg3, typename Arg4,
          typename Arg5, typename Arg6>
class BindCallbackM7 final : public BindCallbackR0 {
public:
    typedef void (Class::*Method)(Arg0, Arg1, Arg2, Arg3, Arg4, Arg5, Arg6);

    BindCallbackM7()
        : magic_(bind_callback_internal::BIND_CALLBACK_ALIVE),
          object_(NULL),
          method_(NULL),
          arg0_(Arg0()),
          arg1_(Arg1()),
          arg2_(Arg2()),
          arg3_(Arg3()),
          arg4_(Arg4()),
          arg5_(Arg5()),
          arg6_(Arg6()) {}

    ~BindCallbackM7() {
#ifndef NDEBUG
        BYTE_ASSERT(magic_ == bind_callback_internal::BIND_CALLBACK_ALIVE);
#endif  // NDEBUG
        magic_ = bind_callback_internal::BIND_CALLBACK_FREED;
    }

    void Bind(Class* object, Method method, Arg0 arg0, Arg1 arg1, Arg2 arg2, Arg3 arg3, Arg4 arg4,
              Arg5 arg5, Arg6 arg6) {
#ifndef NDEBUG
        BYTE_ASSERT(magic_ == bind_callback_internal::BIND_CALLBACK_ALIVE);
#endif  // NDEBUG
        object_ = object;
        method_ = method;
        arg0_ = arg0;
        arg1_ = arg1;
        arg2_ = arg2;
        arg3_ = arg3;
        arg4_ = arg4;
        arg5_ = arg5;
        arg6_ = arg6;
    }

    /* override */ void Run() {
#ifndef NDEBUG
        BYTE_ASSERT(magic_ == bind_callback_internal::BIND_CALLBACK_ALIVE);
#endif  // NDEBUG
        (object_->*method_)(arg0_, arg1_, arg2_, arg3_, arg4_, arg5_, arg6_);
    }

private:
    uint64_t magic_;
    Class* object_;
    Method method_;
    Arg0 arg0_;
    Arg1 arg1_;
    Arg2 arg2_;
    Arg3 arg3_;
    Arg4 arg4_;
    Arg5 arg5_;
    Arg6 arg6_;
};

template <typename Class, typename Arg0, typename Arg1, typename Arg2, typename Arg3, typename Arg4,
          typename Arg5, typename Arg6, typename Result0>
class BindCallbackM7R1 final : public BindCallbackR1<Result0> {
public:
    typedef void (Class::*Method)(Arg0, Arg1, Arg2, Arg3, Arg4, Arg5, Arg6, Result0);

    BindCallbackM7R1()
        : magic_(bind_callback_internal::BIND_CALLBACK_ALIVE),
          object_(NULL),
          method_(NULL),
          arg0_(Arg0()),
          arg1_(Arg1()),
          arg2_(Arg2()),
          arg3_(Arg3()),
          arg4_(Arg4()),
          arg5_(Arg5()),
          arg6_(Arg6()) {}

    ~BindCallbackM7R1() {
#ifndef NDEBUG
        BYTE_ASSERT(magic_ == bind_callback_internal::BIND_CALLBACK_ALIVE);
#endif  // NDEBUG
        magic_ = bind_callback_internal::BIND_CALLBACK_FREED;
    }

    void Bind(Class* object, Method method, Arg0 arg0, Arg1 arg1, Arg2 arg2, Arg3 arg3, Arg4 arg4,
              Arg5 arg5, Arg6 arg6) {
#ifndef NDEBUG
        BYTE_ASSERT(magic_ == bind_callback_internal::BIND_CALLBACK_ALIVE);
#endif  // NDEBUG
        object_ = object;
        method_ = method;
        arg0_ = arg0;
        arg1_ = arg1;
        arg2_ = arg2;
        arg3_ = arg3;
        arg4_ = arg4;
        arg5_ = arg5;
        arg6_ = arg6;
    }

    /* override */ void Run() {
#ifndef NDEBUG
        BYTE_ASSERT(magic_ == bind_callback_internal::BIND_CALLBACK_ALIVE);
#endif  // NDEBUG
        (object_->*method_)(arg0_, arg1_, arg2_, arg3_, arg4_, arg5_, arg6_, this->result0_);
    }

private:
    uint64_t magic_;
    Class* object_;
    Method method_;
    Arg0 arg0_;
    Arg1 arg1_;
    Arg2 arg2_;
    Arg3 arg3_;
    Arg4 arg4_;
    Arg5 arg5_;
    Arg6 arg6_;
};

template <typename Class, typename Arg0, typename Arg1, typename Arg2, typename Arg3, typename Arg4,
          typename Arg5, typename Arg6, typename Result0, typename Result1>
class BindCallbackM7R2 final : public BindCallbackR2<Result0, Result1> {
public:
    typedef void (Class::*Method)(Arg0, Arg1, Arg2, Arg3, Arg4, Arg5, Arg6, Result0, Result1);

    BindCallbackM7R2()
        : magic_(bind_callback_internal::BIND_CALLBACK_ALIVE),
          object_(NULL),
          method_(NULL),
          arg0_(Arg0()),
          arg1_(Arg1()),
          arg2_(Arg2()),
          arg3_(Arg3()),
          arg4_(Arg4()),
          arg5_(Arg5()),
          arg6_(Arg6()) {}

    ~BindCallbackM7R2() {
#ifndef NDEBUG
        BYTE_ASSERT(magic_ == bind_callback_internal::BIND_CALLBACK_ALIVE);
#endif  // NDEBUG
        magic_ = bind_callback_internal::BIND_CALLBACK_FREED;
    }

    void Bind(Class* object, Method method, Arg0 arg0, Arg1 arg1, Arg2 arg2, Arg3 arg3, Arg4 arg4,
              Arg5 arg5, Arg6 arg6) {
#ifndef NDEBUG
        BYTE_ASSERT(magic_ == bind_callback_internal::BIND_CALLBACK_ALIVE);
#endif  // NDEBUG
        object_ = object;
        method_ = method;
        arg0_ = arg0;
        arg1_ = arg1;
        arg2_ = arg2;
        arg3_ = arg3;
        arg4_ = arg4;
        arg5_ = arg5;
        arg6_ = arg6;
    }

    /* override */ void Run() {
#ifndef NDEBUG
        BYTE_ASSERT(magic_ == bind_callback_internal::BIND_CALLBACK_ALIVE);
#endif  // NDEBUG
        (object_->*method_)(arg0_, arg1_, arg2_, arg3_, arg4_, arg5_, arg6_, this->result0_,
                            this->result1_);
    }

private:
    uint64_t magic_;
    Class* object_;
    Method method_;
    Arg0 arg0_;
    Arg1 arg1_;
    Arg2 arg2_;
    Arg3 arg3_;
    Arg4 arg4_;
    Arg5 arg5_;
    Arg6 arg6_;
};

template <typename Class, typename Arg0, typename Arg1, typename Arg2, typename Arg3, typename Arg4,
          typename Arg5, typename Arg6, typename Result0, typename Result1, typename Result2>
class BindCallbackM7R3 final : public BindCallbackR3<Result0, Result1, Result2> {
public:
    typedef void (Class::*Method)(Arg0, Arg1, Arg2, Arg3, Arg4, Arg5, Arg6, Result0, Result1,
                                  Result2);

    BindCallbackM7R3()
        : magic_(bind_callback_internal::BIND_CALLBACK_ALIVE),
          object_(NULL),
          method_(NULL),
          arg0_(Arg0()),
          arg1_(Arg1()),
          arg2_(Arg2()),
          arg3_(Arg3()),
          arg4_(Arg4()),
          arg5_(Arg5()),
          arg6_(Arg6()) {}

    ~BindCallbackM7R3() {
#ifndef NDEBUG
        BYTE_ASSERT(magic_ == bind_callback_internal::BIND_CALLBACK_ALIVE);
#endif  // NDEBUG
        magic_ = bind_callback_internal::BIND_CALLBACK_FREED;
    }

    void Bind(Class* object, Method method, Arg0 arg0, Arg1 arg1, Arg2 arg2, Arg3 arg3, Arg4 arg4,
              Arg5 arg5, Arg6 arg6) {
#ifndef NDEBUG
        BYTE_ASSERT(magic_ == bind_callback_internal::BIND_CALLBACK_ALIVE);
#endif  // NDEBUG
        object_ = object;
        method_ = method;
        arg0_ = arg0;
        arg1_ = arg1;
        arg2_ = arg2;
        arg3_ = arg3;
        arg4_ = arg4;
        arg5_ = arg5;
        arg6_ = arg6;
    }

    /* override */ void Run() {
#ifndef NDEBUG
        BYTE_ASSERT(magic_ == bind_callback_internal::BIND_CALLBACK_ALIVE);
#endif  // NDEBUG
        (object_->*method_)(arg0_, arg1_, arg2_, arg3_, arg4_, arg5_, arg6_, this->result0_,
                            this->result1_, this->result2_);
    }

private:
    uint64_t magic_;
    Class* object_;
    Method method_;
    Arg0 arg0_;
    Arg1 arg1_;
    Arg2 arg2_;
    Arg3 arg3_;
    Arg4 arg4_;
    Arg5 arg5_;
    Arg6 arg6_;
};

template <typename Class, typename Arg0, typename Arg1, typename Arg2, typename Arg3, typename Arg4,
          typename Arg5, typename Arg6, typename Arg7>
class BindCallbackM8 final : public BindCallbackR0 {
public:
    typedef void (Class::*Method)(Arg0, Arg1, Arg2, Arg3, Arg4, Arg5, Arg6, Arg7);

    BindCallbackM8()
        : magic_(bind_callback_internal::BIND_CALLBACK_ALIVE),
          object_(NULL),
          method_(NULL),
          arg0_(Arg0()),
          arg1_(Arg1()),
          arg2_(Arg2()),
          arg3_(Arg3()),
          arg4_(Arg4()),
          arg5_(Arg5()),
          arg6_(Arg6()),
          arg7_(Arg7()) {}

    ~BindCallbackM8() {
#ifndef NDEBUG
        BYTE_ASSERT(magic_ == bind_callback_internal::BIND_CALLBACK_ALIVE);
#endif  // NDEBUG
        magic_ = bind_callback_internal::BIND_CALLBACK_FREED;
    }

    void Bind(Class* object, Method method, Arg0 arg0, Arg1 arg1, Arg2 arg2, Arg3 arg3, Arg4 arg4,
              Arg5 arg5, Arg6 arg6, Arg7 arg7) {
#ifndef NDEBUG
        BYTE_ASSERT(magic_ == bind_callback_internal::BIND_CALLBACK_ALIVE);
#endif  // NDEBUG
        object_ = object;
        method_ = method;
        arg0_ = arg0;
        arg1_ = arg1;
        arg2_ = arg2;
        arg3_ = arg3;
        arg4_ = arg4;
        arg5_ = arg5;
        arg6_ = arg6;
        arg7_ = arg7;
    }

    /* override */ void Run() {
#ifndef NDEBUG
        BYTE_ASSERT(magic_ == bind_callback_internal::BIND_CALLBACK_ALIVE);
#endif  // NDEBUG
        (object_->*method_)(arg0_, arg1_, arg2_, arg3_, arg4_, arg5_, arg6_, arg7_);
    }

private:
    uint64_t magic_;
    Class* object_;
    Method method_;
    Arg0 arg0_;
    Arg1 arg1_;
    Arg2 arg2_;
    Arg3 arg3_;
    Arg4 arg4_;
    Arg5 arg5_;
    Arg6 arg6_;
    Arg7 arg7_;
};

template <typename Class, typename Arg0, typename Arg1, typename Arg2, typename Arg3, typename Arg4,
          typename Arg5, typename Arg6, typename Arg7, typename Result0>
class BindCallbackM8R1 final : public BindCallbackR1<Result0> {
public:
    typedef void (Class::*Method)(Arg0, Arg1, Arg2, Arg3, Arg4, Arg5, Arg6, Arg7, Result0);

    BindCallbackM8R1()
        : magic_(bind_callback_internal::BIND_CALLBACK_ALIVE),
          object_(NULL),
          method_(NULL),
          arg0_(Arg0()),
          arg1_(Arg1()),
          arg2_(Arg2()),
          arg3_(Arg3()),
          arg4_(Arg4()),
          arg5_(Arg5()),
          arg6_(Arg6()),
          arg7_(Arg7()) {}

    ~BindCallbackM8R1() {
#ifndef NDEBUG
        BYTE_ASSERT(magic_ == bind_callback_internal::BIND_CALLBACK_ALIVE);
#endif  // NDEBUG
        magic_ = bind_callback_internal::BIND_CALLBACK_FREED;
    }

    void Bind(Class* object, Method method, Arg0 arg0, Arg1 arg1, Arg2 arg2, Arg3 arg3, Arg4 arg4,
              Arg5 arg5, Arg6 arg6, Arg7 arg7) {
#ifndef NDEBUG
        BYTE_ASSERT(magic_ == bind_callback_internal::BIND_CALLBACK_ALIVE);
#endif  // NDEBUG
        object_ = object;
        method_ = method;
        arg0_ = arg0;
        arg1_ = arg1;
        arg2_ = arg2;
        arg3_ = arg3;
        arg4_ = arg4;
        arg5_ = arg5;
        arg6_ = arg6;
        arg7_ = arg7;
    }

    /* override */ void Run() {
#ifndef NDEBUG
        BYTE_ASSERT(magic_ == bind_callback_internal::BIND_CALLBACK_ALIVE);
#endif  // NDEBUG
        (object_->*method_)(arg0_, arg1_, arg2_, arg3_, arg4_, arg5_, arg6_, arg7_, this->result0_);
    }

private:
    uint64_t magic_;
    Class* object_;
    Method method_;
    Arg0 arg0_;
    Arg1 arg1_;
    Arg2 arg2_;
    Arg3 arg3_;
    Arg4 arg4_;
    Arg5 arg5_;
    Arg6 arg6_;
    Arg7 arg7_;
};

template <typename Class, typename Arg0, typename Arg1, typename Arg2, typename Arg3, typename Arg4,
          typename Arg5, typename Arg6, typename Arg7, typename Result0, typename Result1>
class BindCallbackM8R2 final : public BindCallbackR2<Result0, Result1> {
public:
    typedef void (Class::*Method)(Arg0, Arg1, Arg2, Arg3, Arg4, Arg5, Arg6, Arg7, Result0, Result1);

    BindCallbackM8R2()
        : magic_(bind_callback_internal::BIND_CALLBACK_ALIVE),
          object_(NULL),
          method_(NULL),
          arg0_(Arg0()),
          arg1_(Arg1()),
          arg2_(Arg2()),
          arg3_(Arg3()),
          arg4_(Arg4()),
          arg5_(Arg5()),
          arg6_(Arg6()),
          arg7_(Arg7()) {}

    ~BindCallbackM8R2() {
#ifndef NDEBUG
        BYTE_ASSERT(magic_ == bind_callback_internal::BIND_CALLBACK_ALIVE);
#endif  // NDEBUG
        magic_ = bind_callback_internal::BIND_CALLBACK_FREED;
    }

    void Bind(Class* object, Method method, Arg0 arg0, Arg1 arg1, Arg2 arg2, Arg3 arg3, Arg4 arg4,
              Arg5 arg5, Arg6 arg6, Arg7 arg7) {
#ifndef NDEBUG
        BYTE_ASSERT(magic_ == bind_callback_internal::BIND_CALLBACK_ALIVE);
#endif  // NDEBUG
        object_ = object;
        method_ = method;
        arg0_ = arg0;
        arg1_ = arg1;
        arg2_ = arg2;
        arg3_ = arg3;
        arg4_ = arg4;
        arg5_ = arg5;
        arg6_ = arg6;
        arg7_ = arg7;
    }

    /* override */ void Run() {
#ifndef NDEBUG
        BYTE_ASSERT(magic_ == bind_callback_internal::BIND_CALLBACK_ALIVE);
#endif  // NDEBUG
        (object_->*method_)(arg0_, arg1_, arg2_, arg3_, arg4_, arg5_, arg6_, arg7_, this->result0_,
                            this->result1_);
    }

private:
    uint64_t magic_;
    Class* object_;
    Method method_;
    Arg0 arg0_;
    Arg1 arg1_;
    Arg2 arg2_;
    Arg3 arg3_;
    Arg4 arg4_;
    Arg5 arg5_;
    Arg6 arg6_;
    Arg7 arg7_;
};

template <typename Class, typename Arg0, typename Arg1, typename Arg2, typename Arg3, typename Arg4,
          typename Arg5, typename Arg6, typename Arg7, typename Result0, typename Result1,
          typename Result2>
class BindCallbackM8R3 final : public BindCallbackR3<Result0, Result1, Result2> {
public:
    typedef void (Class::*Method)(Arg0, Arg1, Arg2, Arg3, Arg4, Arg5, Arg6, Arg7, Result0, Result1,
                                  Result2);

    BindCallbackM8R3()
        : magic_(bind_callback_internal::BIND_CALLBACK_ALIVE),
          object_(NULL),
          method_(NULL),
          arg0_(Arg0()),
          arg1_(Arg1()),
          arg2_(Arg2()),
          arg3_(Arg3()),
          arg4_(Arg4()),
          arg5_(Arg5()),
          arg6_(Arg6()),
          arg7_(Arg7()) {}

    ~BindCallbackM8R3() {
#ifndef NDEBUG
        BYTE_ASSERT(magic_ == bind_callback_internal::BIND_CALLBACK_ALIVE);
#endif  // NDEBUG
        magic_ = bind_callback_internal::BIND_CALLBACK_FREED;
    }

    void Bind(Class* object, Method method, Arg0 arg0, Arg1 arg1, Arg2 arg2, Arg3 arg3, Arg4 arg4,
              Arg5 arg5, Arg6 arg6, Arg7 arg7) {
#ifndef NDEBUG
        BYTE_ASSERT(magic_ == bind_callback_internal::BIND_CALLBACK_ALIVE);
#endif  // NDEBUG
        object_ = object;
        method_ = method;
        arg0_ = arg0;
        arg1_ = arg1;
        arg2_ = arg2;
        arg3_ = arg3;
        arg4_ = arg4;
        arg5_ = arg5;
        arg6_ = arg6;
        arg7_ = arg7;
    }

    /* override */ void Run() {
#ifndef NDEBUG
        BYTE_ASSERT(magic_ == bind_callback_internal::BIND_CALLBACK_ALIVE);
#endif  // NDEBUG
        (object_->*method_)(arg0_, arg1_, arg2_, arg3_, arg4_, arg5_, arg6_, arg7_, this->result0_,
                            this->result1_, this->result2_);
    }

private:
    uint64_t magic_;
    Class* object_;
    Method method_;
    Arg0 arg0_;
    Arg1 arg1_;
    Arg2 arg2_;
    Arg3 arg3_;
    Arg4 arg4_;
    Arg5 arg5_;
    Arg6 arg6_;
    Arg7 arg7_;
};

template <typename Class, typename Arg0, typename Arg1, typename Arg2, typename Arg3, typename Arg4,
          typename Arg5, typename Arg6, typename Arg7, typename Arg8>
class BindCallbackM9 final : public BindCallbackR0 {
public:
    typedef void (Class::*Method)(Arg0, Arg1, Arg2, Arg3, Arg4, Arg5, Arg6, Arg7, Arg8);

    BindCallbackM9()
        : magic_(bind_callback_internal::BIND_CALLBACK_ALIVE),
          object_(NULL),
          method_(NULL),
          arg0_(Arg0()),
          arg1_(Arg1()),
          arg2_(Arg2()),
          arg3_(Arg3()),
          arg4_(Arg4()),
          arg5_(Arg5()),
          arg6_(Arg6()),
          arg7_(Arg7()),
          arg8_(Arg8()) {}

    ~BindCallbackM9() {
#ifndef NDEBUG
        BYTE_ASSERT(magic_ == bind_callback_internal::BIND_CALLBACK_ALIVE);
#endif  // NDEBUG
        magic_ = bind_callback_internal::BIND_CALLBACK_FREED;
    }

    void Bind(Class* object, Method method, Arg0 arg0, Arg1 arg1, Arg2 arg2, Arg3 arg3, Arg4 arg4,
              Arg5 arg5, Arg6 arg6, Arg7 arg7, Arg8 arg8) {
#ifndef NDEBUG
        BYTE_ASSERT(magic_ == bind_callback_internal::BIND_CALLBACK_ALIVE);
#endif  // NDEBUG
        object_ = object;
        method_ = method;
        arg0_ = arg0;
        arg1_ = arg1;
        arg2_ = arg2;
        arg3_ = arg3;
        arg4_ = arg4;
        arg5_ = arg5;
        arg6_ = arg6;
        arg7_ = arg7;
        arg8_ = arg8;
    }

    /* override */ void Run() {
#ifndef NDEBUG
        BYTE_ASSERT(magic_ == bind_callback_internal::BIND_CALLBACK_ALIVE);
#endif  // NDEBUG
        (object_->*method_)(arg0_, arg1_, arg2_, arg3_, arg4_, arg5_, arg6_, arg7_, arg8_);
    }

private:
    uint64_t magic_;
    Class* object_;
    Method method_;
    Arg0 arg0_;
    Arg1 arg1_;
    Arg2 arg2_;
    Arg3 arg3_;
    Arg4 arg4_;
    Arg5 arg5_;
    Arg6 arg6_;
    Arg7 arg7_;
    Arg8 arg8_;
};

template <typename Class, typename Arg0, typename Arg1, typename Arg2, typename Arg3, typename Arg4,
          typename Arg5, typename Arg6, typename Arg7, typename Arg8, typename Result0>
class BindCallbackM9R1 final : public BindCallbackR1<Result0> {
public:
    typedef void (Class::*Method)(Arg0, Arg1, Arg2, Arg3, Arg4, Arg5, Arg6, Arg7, Arg8, Result0);

    BindCallbackM9R1()
        : magic_(bind_callback_internal::BIND_CALLBACK_ALIVE),
          object_(NULL),
          method_(NULL),
          arg0_(Arg0()),
          arg1_(Arg1()),
          arg2_(Arg2()),
          arg3_(Arg3()),
          arg4_(Arg4()),
          arg5_(Arg5()),
          arg6_(Arg6()),
          arg7_(Arg7()),
          arg8_(Arg8()) {}

    ~BindCallbackM9R1() {
#ifndef NDEBUG
        BYTE_ASSERT(magic_ == bind_callback_internal::BIND_CALLBACK_ALIVE);
#endif  // NDEBUG
        magic_ = bind_callback_internal::BIND_CALLBACK_FREED;
    }

    void Bind(Class* object, Method method, Arg0 arg0, Arg1 arg1, Arg2 arg2, Arg3 arg3, Arg4 arg4,
              Arg5 arg5, Arg6 arg6, Arg7 arg7, Arg8 arg8) {
#ifndef NDEBUG
        BYTE_ASSERT(magic_ == bind_callback_internal::BIND_CALLBACK_ALIVE);
#endif  // NDEBUG
        object_ = object;
        method_ = method;
        arg0_ = arg0;
        arg1_ = arg1;
        arg2_ = arg2;
        arg3_ = arg3;
        arg4_ = arg4;
        arg5_ = arg5;
        arg6_ = arg6;
        arg7_ = arg7;
        arg8_ = arg8;
    }

    /* override */ void Run() {
#ifndef NDEBUG
        BYTE_ASSERT(magic_ == bind_callback_internal::BIND_CALLBACK_ALIVE);
#endif  // NDEBUG
        (object_->*method_)(arg0_, arg1_, arg2_, arg3_, arg4_, arg5_, arg6_, arg7_, arg8_,
                            this->result0_);
    }

private:
    uint64_t magic_;
    Class* object_;
    Method method_;
    Arg0 arg0_;
    Arg1 arg1_;
    Arg2 arg2_;
    Arg3 arg3_;
    Arg4 arg4_;
    Arg5 arg5_;
    Arg6 arg6_;
    Arg7 arg7_;
    Arg8 arg8_;
};

template <typename Class, typename Arg0, typename Arg1, typename Arg2, typename Arg3, typename Arg4,
          typename Arg5, typename Arg6, typename Arg7, typename Arg8, typename Result0,
          typename Result1>
class BindCallbackM9R2 final : public BindCallbackR2<Result0, Result1> {
public:
    typedef void (Class::*Method)(Arg0, Arg1, Arg2, Arg3, Arg4, Arg5, Arg6, Arg7, Arg8, Result0,
                                  Result1);

    BindCallbackM9R2()
        : magic_(bind_callback_internal::BIND_CALLBACK_ALIVE),
          object_(NULL),
          method_(NULL),
          arg0_(Arg0()),
          arg1_(Arg1()),
          arg2_(Arg2()),
          arg3_(Arg3()),
          arg4_(Arg4()),
          arg5_(Arg5()),
          arg6_(Arg6()),
          arg7_(Arg7()),
          arg8_(Arg8()) {}

    ~BindCallbackM9R2() {
#ifndef NDEBUG
        BYTE_ASSERT(magic_ == bind_callback_internal::BIND_CALLBACK_ALIVE);
#endif  // NDEBUG
        magic_ = bind_callback_internal::BIND_CALLBACK_FREED;
    }

    void Bind(Class* object, Method method, Arg0 arg0, Arg1 arg1, Arg2 arg2, Arg3 arg3, Arg4 arg4,
              Arg5 arg5, Arg6 arg6, Arg7 arg7, Arg8 arg8) {
#ifndef NDEBUG
        BYTE_ASSERT(magic_ == bind_callback_internal::BIND_CALLBACK_ALIVE);
#endif  // NDEBUG
        object_ = object;
        method_ = method;
        arg0_ = arg0;
        arg1_ = arg1;
        arg2_ = arg2;
        arg3_ = arg3;
        arg4_ = arg4;
        arg5_ = arg5;
        arg6_ = arg6;
        arg7_ = arg7;
        arg8_ = arg8;
    }

    /* override */ void Run() {
#ifndef NDEBUG
        BYTE_ASSERT(magic_ == bind_callback_internal::BIND_CALLBACK_ALIVE);
#endif  // NDEBUG
        (object_->*method_)(arg0_, arg1_, arg2_, arg3_, arg4_, arg5_, arg6_, arg7_, arg8_,
                            this->result0_, this->result1_);
    }

private:
    uint64_t magic_;
    Class* object_;
    Method method_;
    Arg0 arg0_;
    Arg1 arg1_;
    Arg2 arg2_;
    Arg3 arg3_;
    Arg4 arg4_;
    Arg5 arg5_;
    Arg6 arg6_;
    Arg7 arg7_;
    Arg8 arg8_;
};

template <typename Class, typename Arg0, typename Arg1, typename Arg2, typename Arg3, typename Arg4,
          typename Arg5, typename Arg6, typename Arg7, typename Arg8, typename Result0,
          typename Result1, typename Result2>
class BindCallbackM9R3 final : public BindCallbackR3<Result0, Result1, Result2> {
public:
    typedef void (Class::*Method)(Arg0, Arg1, Arg2, Arg3, Arg4, Arg5, Arg6, Arg7, Arg8, Result0,
                                  Result1, Result2);

    BindCallbackM9R3()
        : magic_(bind_callback_internal::BIND_CALLBACK_ALIVE),
          object_(NULL),
          method_(NULL),
          arg0_(Arg0()),
          arg1_(Arg1()),
          arg2_(Arg2()),
          arg3_(Arg3()),
          arg4_(Arg4()),
          arg5_(Arg5()),
          arg6_(Arg6()),
          arg7_(Arg7()),
          arg8_(Arg8()) {}

    ~BindCallbackM9R3() {
#ifndef NDEBUG
        BYTE_ASSERT(magic_ == bind_callback_internal::BIND_CALLBACK_ALIVE);
#endif  // NDEBUG
        magic_ = bind_callback_internal::BIND_CALLBACK_FREED;
    }

    void Bind(Class* object, Method method, Arg0 arg0, Arg1 arg1, Arg2 arg2, Arg3 arg3, Arg4 arg4,
              Arg5 arg5, Arg6 arg6, Arg7 arg7, Arg8 arg8) {
#ifndef NDEBUG
        BYTE_ASSERT(magic_ == bind_callback_internal::BIND_CALLBACK_ALIVE);
#endif  // NDEBUG
        object_ = object;
        method_ = method;
        arg0_ = arg0;
        arg1_ = arg1;
        arg2_ = arg2;
        arg3_ = arg3;
        arg4_ = arg4;
        arg5_ = arg5;
        arg6_ = arg6;
        arg7_ = arg7;
        arg8_ = arg8;
    }

    /* override */ void Run() {
#ifndef NDEBUG
        BYTE_ASSERT(magic_ == bind_callback_internal::BIND_CALLBACK_ALIVE);
#endif  // NDEBUG
        (object_->*method_)(arg0_, arg1_, arg2_, arg3_, arg4_, arg5_, arg6_, arg7_, arg8_,
                            this->result0_, this->result1_, this->result2_);
    }

private:
    uint64_t magic_;
    Class* object_;
    Method method_;
    Arg0 arg0_;
    Arg1 arg1_;
    Arg2 arg2_;
    Arg3 arg3_;
    Arg4 arg4_;
    Arg5 arg5_;
    Arg6 arg6_;
    Arg7 arg7_;
    Arg8 arg8_;
};

template <typename Class, typename Arg0, typename Arg1, typename Arg2, typename Arg3, typename Arg4,
          typename Arg5, typename Arg6, typename Arg7, typename Arg8, typename Arg9>
class BindCallbackM10 final : public BindCallbackR0 {
public:
    typedef void (Class::*Method)(Arg0, Arg1, Arg2, Arg3, Arg4, Arg5, Arg6, Arg7, Arg8, Arg9);

    BindCallbackM10()
        : magic_(bind_callback_internal::BIND_CALLBACK_ALIVE),
          object_(NULL),
          method_(NULL),
          arg0_(Arg0()),
          arg1_(Arg1()),
          arg2_(Arg2()),
          arg3_(Arg3()),
          arg4_(Arg4()),
          arg5_(Arg5()),
          arg6_(Arg6()),
          arg7_(Arg7()),
          arg8_(Arg8()),
          arg9_(Arg9()) {}

    ~BindCallbackM10() {
#ifndef NDEBUG
        BYTE_ASSERT(magic_ == bind_callback_internal::BIND_CALLBACK_ALIVE);
#endif  // NDEBUG
        magic_ = bind_callback_internal::BIND_CALLBACK_FREED;
    }

    void Bind(Class* object, Method method, Arg0 arg0, Arg1 arg1, Arg2 arg2, Arg3 arg3, Arg4 arg4,
              Arg5 arg5, Arg6 arg6, Arg7 arg7, Arg8 arg8, Arg9 arg9) {
#ifndef NDEBUG
        BYTE_ASSERT(magic_ == bind_callback_internal::BIND_CALLBACK_ALIVE);
#endif  // NDEBUG
        object_ = object;
        method_ = method;
        arg0_ = arg0;
        arg1_ = arg1;
        arg2_ = arg2;
        arg3_ = arg3;
        arg4_ = arg4;
        arg5_ = arg5;
        arg6_ = arg6;
        arg7_ = arg7;
        arg8_ = arg8;
        arg9_ = arg9;
    }

    /* override */ void Run() {
#ifndef NDEBUG
        BYTE_ASSERT(magic_ == bind_callback_internal::BIND_CALLBACK_ALIVE);
#endif  // NDEBUG
        (object_->*method_)(arg0_, arg1_, arg2_, arg3_, arg4_, arg5_, arg6_, arg7_, arg8_, arg9_);
    }

private:
    uint64_t magic_;
    Class* object_;
    Method method_;
    Arg0 arg0_;
    Arg1 arg1_;
    Arg2 arg2_;
    Arg3 arg3_;
    Arg4 arg4_;
    Arg5 arg5_;
    Arg6 arg6_;
    Arg7 arg7_;
    Arg8 arg8_;
    Arg9 arg9_;
};

template <typename Class, typename Arg0, typename Arg1, typename Arg2, typename Arg3, typename Arg4,
          typename Arg5, typename Arg6, typename Arg7, typename Arg8, typename Arg9,
          typename Result0>
class BindCallbackM10R1 final : public BindCallbackR1<Result0> {
public:
    typedef void (Class::*Method)(Arg0, Arg1, Arg2, Arg3, Arg4, Arg5, Arg6, Arg7, Arg8, Arg9,
                                  Result0);

    BindCallbackM10R1()
        : magic_(bind_callback_internal::BIND_CALLBACK_ALIVE),
          object_(NULL),
          method_(NULL),
          arg0_(Arg0()),
          arg1_(Arg1()),
          arg2_(Arg2()),
          arg3_(Arg3()),
          arg4_(Arg4()),
          arg5_(Arg5()),
          arg6_(Arg6()),
          arg7_(Arg7()),
          arg8_(Arg8()),
          arg9_(Arg9()) {}

    ~BindCallbackM10R1() {
#ifndef NDEBUG
        BYTE_ASSERT(magic_ == bind_callback_internal::BIND_CALLBACK_ALIVE);
#endif  // NDEBUG
        magic_ = bind_callback_internal::BIND_CALLBACK_FREED;
    }

    void Bind(Class* object, Method method, Arg0 arg0, Arg1 arg1, Arg2 arg2, Arg3 arg3, Arg4 arg4,
              Arg5 arg5, Arg6 arg6, Arg7 arg7, Arg8 arg8, Arg9 arg9) {
#ifndef NDEBUG
        BYTE_ASSERT(magic_ == bind_callback_internal::BIND_CALLBACK_ALIVE);
#endif  // NDEBUG
        object_ = object;
        method_ = method;
        arg0_ = arg0;
        arg1_ = arg1;
        arg2_ = arg2;
        arg3_ = arg3;
        arg4_ = arg4;
        arg5_ = arg5;
        arg6_ = arg6;
        arg7_ = arg7;
        arg8_ = arg8;
        arg9_ = arg9;
    }

    /* override */ void Run() {
#ifndef NDEBUG
        BYTE_ASSERT(magic_ == bind_callback_internal::BIND_CALLBACK_ALIVE);
#endif  // NDEBUG
        (object_->*method_)(arg0_, arg1_, arg2_, arg3_, arg4_, arg5_, arg6_, arg7_, arg8_, arg9_,
                            this->result0_);
    }

private:
    uint64_t magic_;
    Class* object_;
    Method method_;
    Arg0 arg0_;
    Arg1 arg1_;
    Arg2 arg2_;
    Arg3 arg3_;
    Arg4 arg4_;
    Arg5 arg5_;
    Arg6 arg6_;
    Arg7 arg7_;
    Arg8 arg8_;
    Arg9 arg9_;
};

template <typename Class, typename Arg0, typename Arg1, typename Arg2, typename Arg3, typename Arg4,
          typename Arg5, typename Arg6, typename Arg7, typename Arg8, typename Arg9,
          typename Result0, typename Result1>
class BindCallbackM10R2 final : public BindCallbackR2<Result0, Result1> {
public:
    typedef void (Class::*Method)(Arg0, Arg1, Arg2, Arg3, Arg4, Arg5, Arg6, Arg7, Arg8, Arg9,
                                  Result0, Result1);

    BindCallbackM10R2()
        : magic_(bind_callback_internal::BIND_CALLBACK_ALIVE),
          object_(NULL),
          method_(NULL),
          arg0_(Arg0()),
          arg1_(Arg1()),
          arg2_(Arg2()),
          arg3_(Arg3()),
          arg4_(Arg4()),
          arg5_(Arg5()),
          arg6_(Arg6()),
          arg7_(Arg7()),
          arg8_(Arg8()),
          arg9_(Arg9()) {}

    ~BindCallbackM10R2() {
#ifndef NDEBUG
        BYTE_ASSERT(magic_ == bind_callback_internal::BIND_CALLBACK_ALIVE);
#endif  // NDEBUG
        magic_ = bind_callback_internal::BIND_CALLBACK_FREED;
    }

    void Bind(Class* object, Method method, Arg0 arg0, Arg1 arg1, Arg2 arg2, Arg3 arg3, Arg4 arg4,
              Arg5 arg5, Arg6 arg6, Arg7 arg7, Arg8 arg8, Arg9 arg9) {
#ifndef NDEBUG
        BYTE_ASSERT(magic_ == bind_callback_internal::BIND_CALLBACK_ALIVE);
#endif  // NDEBUG
        object_ = object;
        method_ = method;
        arg0_ = arg0;
        arg1_ = arg1;
        arg2_ = arg2;
        arg3_ = arg3;
        arg4_ = arg4;
        arg5_ = arg5;
        arg6_ = arg6;
        arg7_ = arg7;
        arg8_ = arg8;
        arg9_ = arg9;
    }

    /* override */ void Run() {
#ifndef NDEBUG
        BYTE_ASSERT(magic_ == bind_callback_internal::BIND_CALLBACK_ALIVE);
#endif  // NDEBUG
        (object_->*method_)(arg0_, arg1_, arg2_, arg3_, arg4_, arg5_, arg6_, arg7_, arg8_, arg9_,
                            this->result0_, this->result1_);
    }

private:
    uint64_t magic_;
    Class* object_;
    Method method_;
    Arg0 arg0_;
    Arg1 arg1_;
    Arg2 arg2_;
    Arg3 arg3_;
    Arg4 arg4_;
    Arg5 arg5_;
    Arg6 arg6_;
    Arg7 arg7_;
    Arg8 arg8_;
    Arg9 arg9_;
};

template <typename Class, typename Arg0, typename Arg1, typename Arg2, typename Arg3, typename Arg4,
          typename Arg5, typename Arg6, typename Arg7, typename Arg8, typename Arg9,
          typename Result0, typename Result1, typename Result2>
class BindCallbackM10R3 final : public BindCallbackR3<Result0, Result1, Result2> {
public:
    typedef void (Class::*Method)(Arg0, Arg1, Arg2, Arg3, Arg4, Arg5, Arg6, Arg7, Arg8, Arg9,
                                  Result0, Result1, Result2);

    BindCallbackM10R3()
        : magic_(bind_callback_internal::BIND_CALLBACK_ALIVE),
          object_(NULL),
          method_(NULL),
          arg0_(Arg0()),
          arg1_(Arg1()),
          arg2_(Arg2()),
          arg3_(Arg3()),
          arg4_(Arg4()),
          arg5_(Arg5()),
          arg6_(Arg6()),
          arg7_(Arg7()),
          arg8_(Arg8()),
          arg9_(Arg9()) {}

    ~BindCallbackM10R3() {
#ifndef NDEBUG
        BYTE_ASSERT(magic_ == bind_callback_internal::BIND_CALLBACK_ALIVE);
#endif  // NDEBUG
        magic_ = bind_callback_internal::BIND_CALLBACK_FREED;
    }

    void Bind(Class* object, Method method, Arg0 arg0, Arg1 arg1, Arg2 arg2, Arg3 arg3, Arg4 arg4,
              Arg5 arg5, Arg6 arg6, Arg7 arg7, Arg8 arg8, Arg9 arg9) {
#ifndef NDEBUG
        BYTE_ASSERT(magic_ == bind_callback_internal::BIND_CALLBACK_ALIVE);
#endif  // NDEBUG
        object_ = object;
        method_ = method;
        arg0_ = arg0;
        arg1_ = arg1;
        arg2_ = arg2;
        arg3_ = arg3;
        arg4_ = arg4;
        arg5_ = arg5;
        arg6_ = arg6;
        arg7_ = arg7;
        arg8_ = arg8;
        arg9_ = arg9;
    }

    /* override */ void Run() {
#ifndef NDEBUG
        BYTE_ASSERT(magic_ == bind_callback_internal::BIND_CALLBACK_ALIVE);
#endif  // NDEBUG
        (object_->*method_)(arg0_, arg1_, arg2_, arg3_, arg4_, arg5_, arg6_, arg7_, arg8_, arg9_,
                            this->result0_, this->result1_, this->result2_);
    }

private:
    uint64_t magic_;
    Class* object_;
    Method method_;
    Arg0 arg0_;
    Arg1 arg1_;
    Arg2 arg2_;
    Arg3 arg3_;
    Arg4 arg4_;
    Arg5 arg5_;
    Arg6 arg6_;
    Arg7 arg7_;
    Arg8 arg8_;
    Arg9 arg9_;
};

template <typename Class, typename Arg0, typename Arg1, typename Arg2, typename Arg3, typename Arg4,
          typename Arg5, typename Arg6, typename Arg7, typename Arg8, typename Arg9, typename Arg10>
class BindCallbackM11 final : public BindCallbackR0 {
public:
    typedef void (Class::*Method)(Arg0, Arg1, Arg2, Arg3, Arg4, Arg5, Arg6, Arg7, Arg8, Arg9,
                                  Arg10);

    BindCallbackM11()
        : magic_(bind_callback_internal::BIND_CALLBACK_ALIVE),
          object_(NULL),
          method_(NULL),
          arg0_(Arg0()),
          arg1_(Arg1()),
          arg2_(Arg2()),
          arg3_(Arg3()),
          arg4_(Arg4()),
          arg5_(Arg5()),
          arg6_(Arg6()),
          arg7_(Arg7()),
          arg8_(Arg8()),
          arg9_(Arg9()),
          arg10_(Arg10()) {}

    ~BindCallbackM11() {
#ifndef NDEBUG
        BYTE_ASSERT(magic_ == bind_callback_internal::BIND_CALLBACK_ALIVE);
#endif  // NDEBUG
        magic_ = bind_callback_internal::BIND_CALLBACK_FREED;
    }

    void Bind(Class* object, Method method, Arg0 arg0, Arg1 arg1, Arg2 arg2, Arg3 arg3, Arg4 arg4,
              Arg5 arg5, Arg6 arg6, Arg7 arg7, Arg8 arg8, Arg9 arg9, Arg10 arg10) {
#ifndef NDEBUG
        BYTE_ASSERT(magic_ == bind_callback_internal::BIND_CALLBACK_ALIVE);
#endif  // NDEBUG
        object_ = object;
        method_ = method;
        arg0_ = arg0;
        arg1_ = arg1;
        arg2_ = arg2;
        arg3_ = arg3;
        arg4_ = arg4;
        arg5_ = arg5;
        arg6_ = arg6;
        arg7_ = arg7;
        arg8_ = arg8;
        arg9_ = arg9;
        arg10_ = arg10;
    }

    /* override */ void Run() {
#ifndef NDEBUG
        BYTE_ASSERT(magic_ == bind_callback_internal::BIND_CALLBACK_ALIVE);
#endif  // NDEBUG
        (object_->*method_)(arg0_, arg1_, arg2_, arg3_, arg4_, arg5_, arg6_, arg7_, arg8_, arg9_,
                            arg10_);
    }

private:
    uint64_t magic_;
    Class* object_;
    Method method_;
    Arg0 arg0_;
    Arg1 arg1_;
    Arg2 arg2_;
    Arg3 arg3_;
    Arg4 arg4_;
    Arg5 arg5_;
    Arg6 arg6_;
    Arg7 arg7_;
    Arg8 arg8_;
    Arg9 arg9_;
    Arg10 arg10_;
};

template <typename Class, typename Arg0, typename Arg1, typename Arg2, typename Arg3, typename Arg4,
          typename Arg5, typename Arg6, typename Arg7, typename Arg8, typename Arg9, typename Arg10,
          typename Result0>
class BindCallbackM11R1 final : public BindCallbackR1<Result0> {
public:
    typedef void (Class::*Method)(Arg0, Arg1, Arg2, Arg3, Arg4, Arg5, Arg6, Arg7, Arg8, Arg9, Arg10,
                                  Result0);

    BindCallbackM11R1()
        : magic_(bind_callback_internal::BIND_CALLBACK_ALIVE),
          object_(NULL),
          method_(NULL),
          arg0_(Arg0()),
          arg1_(Arg1()),
          arg2_(Arg2()),
          arg3_(Arg3()),
          arg4_(Arg4()),
          arg5_(Arg5()),
          arg6_(Arg6()),
          arg7_(Arg7()),
          arg8_(Arg8()),
          arg9_(Arg9()),
          arg10_(Arg10()) {}

    ~BindCallbackM11R1() {
#ifndef NDEBUG
        BYTE_ASSERT(magic_ == bind_callback_internal::BIND_CALLBACK_ALIVE);
#endif  // NDEBUG
        magic_ = bind_callback_internal::BIND_CALLBACK_FREED;
    }

    void Bind(Class* object, Method method, Arg0 arg0, Arg1 arg1, Arg2 arg2, Arg3 arg3, Arg4 arg4,
              Arg5 arg5, Arg6 arg6, Arg7 arg7, Arg8 arg8, Arg9 arg9, Arg10 arg10) {
#ifndef NDEBUG
        BYTE_ASSERT(magic_ == bind_callback_internal::BIND_CALLBACK_ALIVE);
#endif  // NDEBUG
        object_ = object;
        method_ = method;
        arg0_ = arg0;
        arg1_ = arg1;
        arg2_ = arg2;
        arg3_ = arg3;
        arg4_ = arg4;
        arg5_ = arg5;
        arg6_ = arg6;
        arg7_ = arg7;
        arg8_ = arg8;
        arg9_ = arg9;
        arg10_ = arg10;
    }

    /* override */ void Run() {
#ifndef NDEBUG
        BYTE_ASSERT(magic_ == bind_callback_internal::BIND_CALLBACK_ALIVE);
#endif  // NDEBUG
        (object_->*method_)(arg0_, arg1_, arg2_, arg3_, arg4_, arg5_, arg6_, arg7_, arg8_, arg9_,
                            arg10_, this->result0_);
    }

private:
    uint64_t magic_;
    Class* object_;
    Method method_;
    Arg0 arg0_;
    Arg1 arg1_;
    Arg2 arg2_;
    Arg3 arg3_;
    Arg4 arg4_;
    Arg5 arg5_;
    Arg6 arg6_;
    Arg7 arg7_;
    Arg8 arg8_;
    Arg9 arg9_;
    Arg10 arg10_;
};

template <typename Class, typename Arg0, typename Arg1, typename Arg2, typename Arg3, typename Arg4,
          typename Arg5, typename Arg6, typename Arg7, typename Arg8, typename Arg9, typename Arg10,
          typename Result0, typename Result1>
class BindCallbackM11R2 final : public BindCallbackR2<Result0, Result1> {
public:
    typedef void (Class::*Method)(Arg0, Arg1, Arg2, Arg3, Arg4, Arg5, Arg6, Arg7, Arg8, Arg9, Arg10,
                                  Result0, Result1);

    BindCallbackM11R2()
        : magic_(bind_callback_internal::BIND_CALLBACK_ALIVE),
          object_(NULL),
          method_(NULL),
          arg0_(Arg0()),
          arg1_(Arg1()),
          arg2_(Arg2()),
          arg3_(Arg3()),
          arg4_(Arg4()),
          arg5_(Arg5()),
          arg6_(Arg6()),
          arg7_(Arg7()),
          arg8_(Arg8()),
          arg9_(Arg9()),
          arg10_(Arg10()) {}

    ~BindCallbackM11R2() {
#ifndef NDEBUG
        BYTE_ASSERT(magic_ == bind_callback_internal::BIND_CALLBACK_ALIVE);
#endif  // NDEBUG
        magic_ = bind_callback_internal::BIND_CALLBACK_FREED;
    }

    void Bind(Class* object, Method method, Arg0 arg0, Arg1 arg1, Arg2 arg2, Arg3 arg3, Arg4 arg4,
              Arg5 arg5, Arg6 arg6, Arg7 arg7, Arg8 arg8, Arg9 arg9, Arg10 arg10) {
#ifndef NDEBUG
        BYTE_ASSERT(magic_ == bind_callback_internal::BIND_CALLBACK_ALIVE);
#endif  // NDEBUG
        object_ = object;
        method_ = method;
        arg0_ = arg0;
        arg1_ = arg1;
        arg2_ = arg2;
        arg3_ = arg3;
        arg4_ = arg4;
        arg5_ = arg5;
        arg6_ = arg6;
        arg7_ = arg7;
        arg8_ = arg8;
        arg9_ = arg9;
        arg10_ = arg10;
    }

    /* override */ void Run() {
#ifndef NDEBUG
        BYTE_ASSERT(magic_ == bind_callback_internal::BIND_CALLBACK_ALIVE);
#endif  // NDEBUG
        (object_->*method_)(arg0_, arg1_, arg2_, arg3_, arg4_, arg5_, arg6_, arg7_, arg8_, arg9_,
                            arg10_, this->result0_, this->result1_);
    }

private:
    uint64_t magic_;
    Class* object_;
    Method method_;
    Arg0 arg0_;
    Arg1 arg1_;
    Arg2 arg2_;
    Arg3 arg3_;
    Arg4 arg4_;
    Arg5 arg5_;
    Arg6 arg6_;
    Arg7 arg7_;
    Arg8 arg8_;
    Arg9 arg9_;
    Arg10 arg10_;
};

template <typename Class, typename Arg0, typename Arg1, typename Arg2, typename Arg3, typename Arg4,
          typename Arg5, typename Arg6, typename Arg7, typename Arg8, typename Arg9, typename Arg10,
          typename Result0, typename Result1, typename Result2>
class BindCallbackM11R3 final : public BindCallbackR3<Result0, Result1, Result2> {
public:
    typedef void (Class::*Method)(Arg0, Arg1, Arg2, Arg3, Arg4, Arg5, Arg6, Arg7, Arg8, Arg9, Arg10,
                                  Result0, Result1, Result2);

    BindCallbackM11R3()
        : magic_(bind_callback_internal::BIND_CALLBACK_ALIVE),
          object_(NULL),
          method_(NULL),
          arg0_(Arg0()),
          arg1_(Arg1()),
          arg2_(Arg2()),
          arg3_(Arg3()),
          arg4_(Arg4()),
          arg5_(Arg5()),
          arg6_(Arg6()),
          arg7_(Arg7()),
          arg8_(Arg8()),
          arg9_(Arg9()),
          arg10_(Arg10()) {}

    ~BindCallbackM11R3() {
#ifndef NDEBUG
        BYTE_ASSERT(magic_ == bind_callback_internal::BIND_CALLBACK_ALIVE);
#endif  // NDEBUG
        magic_ = bind_callback_internal::BIND_CALLBACK_FREED;
    }

    void Bind(Class* object, Method method, Arg0 arg0, Arg1 arg1, Arg2 arg2, Arg3 arg3, Arg4 arg4,
              Arg5 arg5, Arg6 arg6, Arg7 arg7, Arg8 arg8, Arg9 arg9, Arg10 arg10) {
#ifndef NDEBUG
        BYTE_ASSERT(magic_ == bind_callback_internal::BIND_CALLBACK_ALIVE);
#endif  // NDEBUG
        object_ = object;
        method_ = method;
        arg0_ = arg0;
        arg1_ = arg1;
        arg2_ = arg2;
        arg3_ = arg3;
        arg4_ = arg4;
        arg5_ = arg5;
        arg6_ = arg6;
        arg7_ = arg7;
        arg8_ = arg8;
        arg9_ = arg9;
        arg10_ = arg10;
    }

    /* override */ void Run() {
#ifndef NDEBUG
        BYTE_ASSERT(magic_ == bind_callback_internal::BIND_CALLBACK_ALIVE);
#endif  // NDEBUG
        (object_->*method_)(arg0_, arg1_, arg2_, arg3_, arg4_, arg5_, arg6_, arg7_, arg8_, arg9_,
                            arg10_, this->result0_, this->result1_, this->result2_);
    }

private:
    uint64_t magic_;
    Class* object_;
    Method method_;
    Arg0 arg0_;
    Arg1 arg1_;
    Arg2 arg2_;
    Arg3 arg3_;
    Arg4 arg4_;
    Arg5 arg5_;
    Arg6 arg6_;
    Arg7 arg7_;
    Arg8 arg8_;
    Arg9 arg9_;
    Arg10 arg10_;
};

template <typename Class, typename Arg0, typename Arg1, typename Arg2, typename Arg3, typename Arg4,
          typename Arg5, typename Arg6, typename Arg7, typename Arg8, typename Arg9, typename Arg10,
          typename Arg11>
class BindCallbackM12 final : public BindCallbackR0 {
public:
    typedef void (Class::*Method)(Arg0, Arg1, Arg2, Arg3, Arg4, Arg5, Arg6, Arg7, Arg8, Arg9, Arg10,
                                  Arg11);

    BindCallbackM12()
        : magic_(bind_callback_internal::BIND_CALLBACK_ALIVE),
          object_(NULL),
          method_(NULL),
          arg0_(Arg0()),
          arg1_(Arg1()),
          arg2_(Arg2()),
          arg3_(Arg3()),
          arg4_(Arg4()),
          arg5_(Arg5()),
          arg6_(Arg6()),
          arg7_(Arg7()),
          arg8_(Arg8()),
          arg9_(Arg9()),
          arg10_(Arg10()),
          arg11_(Arg11()) {}

    ~BindCallbackM12() {
#ifndef NDEBUG
        BYTE_ASSERT(magic_ == bind_callback_internal::BIND_CALLBACK_ALIVE);
#endif  // NDEBUG
        magic_ = bind_callback_internal::BIND_CALLBACK_FREED;
    }

    void Bind(Class* object, Method method, Arg0 arg0, Arg1 arg1, Arg2 arg2, Arg3 arg3, Arg4 arg4,
              Arg5 arg5, Arg6 arg6, Arg7 arg7, Arg8 arg8, Arg9 arg9, Arg10 arg10, Arg11 arg11) {
#ifndef NDEBUG
        BYTE_ASSERT(magic_ == bind_callback_internal::BIND_CALLBACK_ALIVE);
#endif  // NDEBUG
        object_ = object;
        method_ = method;
        arg0_ = arg0;
        arg1_ = arg1;
        arg2_ = arg2;
        arg3_ = arg3;
        arg4_ = arg4;
        arg5_ = arg5;
        arg6_ = arg6;
        arg7_ = arg7;
        arg8_ = arg8;
        arg9_ = arg9;
        arg10_ = arg10;
        arg11_ = arg11;
    }

    /* override */ void Run() {
#ifndef NDEBUG
        BYTE_ASSERT(magic_ == bind_callback_internal::BIND_CALLBACK_ALIVE);
#endif  // NDEBUG
        (object_->*method_)(arg0_, arg1_, arg2_, arg3_, arg4_, arg5_, arg6_, arg7_, arg8_, arg9_,
                            arg10_, arg11_);
    }

private:
    uint64_t magic_;
    Class* object_;
    Method method_;
    Arg0 arg0_;
    Arg1 arg1_;
    Arg2 arg2_;
    Arg3 arg3_;
    Arg4 arg4_;
    Arg5 arg5_;
    Arg6 arg6_;
    Arg7 arg7_;
    Arg8 arg8_;
    Arg9 arg9_;
    Arg10 arg10_;
    Arg11 arg11_;
};

template <typename Class, typename Arg0, typename Arg1, typename Arg2, typename Arg3, typename Arg4,
          typename Arg5, typename Arg6, typename Arg7, typename Arg8, typename Arg9, typename Arg10,
          typename Arg11, typename Result0>
class BindCallbackM12R1 final : public BindCallbackR1<Result0> {
public:
    typedef void (Class::*Method)(Arg0, Arg1, Arg2, Arg3, Arg4, Arg5, Arg6, Arg7, Arg8, Arg9, Arg10,
                                  Arg11, Result0);

    BindCallbackM12R1()
        : magic_(bind_callback_internal::BIND_CALLBACK_ALIVE),
          object_(NULL),
          method_(NULL),
          arg0_(Arg0()),
          arg1_(Arg1()),
          arg2_(Arg2()),
          arg3_(Arg3()),
          arg4_(Arg4()),
          arg5_(Arg5()),
          arg6_(Arg6()),
          arg7_(Arg7()),
          arg8_(Arg8()),
          arg9_(Arg9()),
          arg10_(Arg10()),
          arg11_(Arg11()) {}

    ~BindCallbackM12R1() {
#ifndef NDEBUG
        BYTE_ASSERT(magic_ == bind_callback_internal::BIND_CALLBACK_ALIVE);
#endif  // NDEBUG
        magic_ = bind_callback_internal::BIND_CALLBACK_FREED;
    }

    void Bind(Class* object, Method method, Arg0 arg0, Arg1 arg1, Arg2 arg2, Arg3 arg3, Arg4 arg4,
              Arg5 arg5, Arg6 arg6, Arg7 arg7, Arg8 arg8, Arg9 arg9, Arg10 arg10, Arg11 arg11) {
#ifndef NDEBUG
        BYTE_ASSERT(magic_ == bind_callback_internal::BIND_CALLBACK_ALIVE);
#endif  // NDEBUG
        object_ = object;
        method_ = method;
        arg0_ = arg0;
        arg1_ = arg1;
        arg2_ = arg2;
        arg3_ = arg3;
        arg4_ = arg4;
        arg5_ = arg5;
        arg6_ = arg6;
        arg7_ = arg7;
        arg8_ = arg8;
        arg9_ = arg9;
        arg10_ = arg10;
        arg11_ = arg11;
    }

    /* override */ void Run() {
#ifndef NDEBUG
        BYTE_ASSERT(magic_ == bind_callback_internal::BIND_CALLBACK_ALIVE);
#endif  // NDEBUG
        (object_->*method_)(arg0_, arg1_, arg2_, arg3_, arg4_, arg5_, arg6_, arg7_, arg8_, arg9_,
                            arg10_, arg11_, this->result0_);
    }

private:
    uint64_t magic_;
    Class* object_;
    Method method_;
    Arg0 arg0_;
    Arg1 arg1_;
    Arg2 arg2_;
    Arg3 arg3_;
    Arg4 arg4_;
    Arg5 arg5_;
    Arg6 arg6_;
    Arg7 arg7_;
    Arg8 arg8_;
    Arg9 arg9_;
    Arg10 arg10_;
    Arg11 arg11_;
};

template <typename Class, typename Arg0, typename Arg1, typename Arg2, typename Arg3, typename Arg4,
          typename Arg5, typename Arg6, typename Arg7, typename Arg8, typename Arg9, typename Arg10,
          typename Arg11, typename Result0, typename Result1>
class BindCallbackM12R2 final : public BindCallbackR2<Result0, Result1> {
public:
    typedef void (Class::*Method)(Arg0, Arg1, Arg2, Arg3, Arg4, Arg5, Arg6, Arg7, Arg8, Arg9, Arg10,
                                  Arg11, Result0, Result1);

    BindCallbackM12R2()
        : magic_(bind_callback_internal::BIND_CALLBACK_ALIVE),
          object_(NULL),
          method_(NULL),
          arg0_(Arg0()),
          arg1_(Arg1()),
          arg2_(Arg2()),
          arg3_(Arg3()),
          arg4_(Arg4()),
          arg5_(Arg5()),
          arg6_(Arg6()),
          arg7_(Arg7()),
          arg8_(Arg8()),
          arg9_(Arg9()),
          arg10_(Arg10()),
          arg11_(Arg11()) {}

    ~BindCallbackM12R2() {
#ifndef NDEBUG
        BYTE_ASSERT(magic_ == bind_callback_internal::BIND_CALLBACK_ALIVE);
#endif  // NDEBUG
        magic_ = bind_callback_internal::BIND_CALLBACK_FREED;
    }

    void Bind(Class* object, Method method, Arg0 arg0, Arg1 arg1, Arg2 arg2, Arg3 arg3, Arg4 arg4,
              Arg5 arg5, Arg6 arg6, Arg7 arg7, Arg8 arg8, Arg9 arg9, Arg10 arg10, Arg11 arg11) {
#ifndef NDEBUG
        BYTE_ASSERT(magic_ == bind_callback_internal::BIND_CALLBACK_ALIVE);
#endif  // NDEBUG
        object_ = object;
        method_ = method;
        arg0_ = arg0;
        arg1_ = arg1;
        arg2_ = arg2;
        arg3_ = arg3;
        arg4_ = arg4;
        arg5_ = arg5;
        arg6_ = arg6;
        arg7_ = arg7;
        arg8_ = arg8;
        arg9_ = arg9;
        arg10_ = arg10;
        arg11_ = arg11;
    }

    /* override */ void Run() {
#ifndef NDEBUG
        BYTE_ASSERT(magic_ == bind_callback_internal::BIND_CALLBACK_ALIVE);
#endif  // NDEBUG
        (object_->*method_)(arg0_, arg1_, arg2_, arg3_, arg4_, arg5_, arg6_, arg7_, arg8_, arg9_,
                            arg10_, arg11_, this->result0_, this->result1_);
    }

private:
    uint64_t magic_;
    Class* object_;
    Method method_;
    Arg0 arg0_;
    Arg1 arg1_;
    Arg2 arg2_;
    Arg3 arg3_;
    Arg4 arg4_;
    Arg5 arg5_;
    Arg6 arg6_;
    Arg7 arg7_;
    Arg8 arg8_;
    Arg9 arg9_;
    Arg10 arg10_;
    Arg11 arg11_;
};

template <typename Class, typename Arg0, typename Arg1, typename Arg2, typename Arg3, typename Arg4,
          typename Arg5, typename Arg6, typename Arg7, typename Arg8, typename Arg9, typename Arg10,
          typename Arg11, typename Result0, typename Result1, typename Result2>
class BindCallbackM12R3 final : public BindCallbackR3<Result0, Result1, Result2> {
public:
    typedef void (Class::*Method)(Arg0, Arg1, Arg2, Arg3, Arg4, Arg5, Arg6, Arg7, Arg8, Arg9, Arg10,
                                  Arg11, Result0, Result1, Result2);

    BindCallbackM12R3()
        : magic_(bind_callback_internal::BIND_CALLBACK_ALIVE),
          object_(NULL),
          method_(NULL),
          arg0_(Arg0()),
          arg1_(Arg1()),
          arg2_(Arg2()),
          arg3_(Arg3()),
          arg4_(Arg4()),
          arg5_(Arg5()),
          arg6_(Arg6()),
          arg7_(Arg7()),
          arg8_(Arg8()),
          arg9_(Arg9()),
          arg10_(Arg10()),
          arg11_(Arg11()) {}

    ~BindCallbackM12R3() {
#ifndef NDEBUG
        BYTE_ASSERT(magic_ == bind_callback_internal::BIND_CALLBACK_ALIVE);
#endif  // NDEBUG
        magic_ = bind_callback_internal::BIND_CALLBACK_FREED;
    }

    void Bind(Class* object, Method method, Arg0 arg0, Arg1 arg1, Arg2 arg2, Arg3 arg3, Arg4 arg4,
              Arg5 arg5, Arg6 arg6, Arg7 arg7, Arg8 arg8, Arg9 arg9, Arg10 arg10, Arg11 arg11) {
#ifndef NDEBUG
        BYTE_ASSERT(magic_ == bind_callback_internal::BIND_CALLBACK_ALIVE);
#endif  // NDEBUG
        object_ = object;
        method_ = method;
        arg0_ = arg0;
        arg1_ = arg1;
        arg2_ = arg2;
        arg3_ = arg3;
        arg4_ = arg4;
        arg5_ = arg5;
        arg6_ = arg6;
        arg7_ = arg7;
        arg8_ = arg8;
        arg9_ = arg9;
        arg10_ = arg10;
        arg11_ = arg11;
    }

    /* override */ void Run() {
#ifndef NDEBUG
        BYTE_ASSERT(magic_ == bind_callback_internal::BIND_CALLBACK_ALIVE);
#endif  // NDEBUG
        (object_->*method_)(arg0_, arg1_, arg2_, arg3_, arg4_, arg5_, arg6_, arg7_, arg8_, arg9_,
                            arg10_, arg11_, this->result0_, this->result1_, this->result2_);
    }

private:
    uint64_t magic_;
    Class* object_;
    Method method_;
    Arg0 arg0_;
    Arg1 arg1_;
    Arg2 arg2_;
    Arg3 arg3_;
    Arg4 arg4_;
    Arg5 arg5_;
    Arg6 arg6_;
    Arg7 arg7_;
    Arg8 arg8_;
    Arg9 arg9_;
    Arg10 arg10_;
    Arg11 arg11_;
};

class BindCallbackF0 final : public BindCallbackR0 {
public:
    typedef void (*Function)();

    BindCallbackF0() : function_(NULL) {}

    void Bind(Function function) { function_ = function; }

    /* override */ void Run() { function_(); }

private:
    Function function_;
};

template <typename Result0>
class BindCallbackF0R1 final : public BindCallbackR1<Result0> {
public:
    typedef void (*Function)(Result0);

    BindCallbackF0R1() : function_(NULL) {}

    void Bind(Function function) { function_ = function; }

    /* override */ void Run() { function_(this->result0_); }

private:
    Function function_;
};

template <typename Result0, typename Result1>
class BindCallbackF0R2 final : public BindCallbackR2<Result0, Result1> {
public:
    typedef void (*Function)(Result0, Result1);

    BindCallbackF0R2() : function_(NULL) {}

    void Bind(Function function) { function_ = function; }

    /* override */ void Run() { function_(this->result0_, this->result1_); }

private:
    Function function_;
};

template <typename Result0, typename Result1, typename Result2>
class BindCallbackF0R3 final : public BindCallbackR3<Result0, Result1, Result2> {
public:
    typedef void (*Function)(Result0, Result1, Result2);

    BindCallbackF0R3() : function_(NULL) {}

    void Bind(Function function) { function_ = function; }

    /* override */ void Run() { function_(this->result0_, this->result1_, this->result2_); }

private:
    Function function_;
};

template <typename Arg0>
class BindCallbackF1 final : public BindCallbackR0 {
public:
    typedef void (*Function)(Arg0);

    BindCallbackF1() : function_(NULL), arg0_(Arg0()) {}

    void Bind(Function function, Arg0 arg0) {
        function_ = function;
        arg0_ = arg0;
    }

    /* override */ void Run() { function_(arg0_); }

private:
    Function function_;
    Arg0 arg0_;
};

template <typename Arg0, typename Result0>
class BindCallbackF1R1 final : public BindCallbackR1<Result0> {
public:
    typedef void (*Function)(Arg0, Result0);

    BindCallbackF1R1() : function_(NULL), arg0_(Arg0()) {}

    void Bind(Function function, Arg0 arg0) {
        function_ = function;
        arg0_ = arg0;
    }

    /* override */ void Run() { function_(arg0_, this->result0_); }

private:
    Function function_;
    Arg0 arg0_;
};

template <typename Arg0, typename Result0, typename Result1>
class BindCallbackF1R2 final : public BindCallbackR2<Result0, Result1> {
public:
    typedef void (*Function)(Arg0, Result0, Result1);

    BindCallbackF1R2() : function_(NULL), arg0_(Arg0()) {}

    void Bind(Function function, Arg0 arg0) {
        function_ = function;
        arg0_ = arg0;
    }

    /* override */ void Run() { function_(arg0_, this->result0_, this->result1_); }

private:
    Function function_;
    Arg0 arg0_;
};

template <typename Arg0, typename Result0, typename Result1, typename Result2>
class BindCallbackF1R3 final : public BindCallbackR3<Result0, Result1, Result2> {
public:
    typedef void (*Function)(Arg0, Result0, Result1, Result2);

    BindCallbackF1R3() : function_(NULL), arg0_(Arg0()) {}

    void Bind(Function function, Arg0 arg0) {
        function_ = function;
        arg0_ = arg0;
    }

    /* override */ void Run() { function_(arg0_, this->result0_, this->result1_, this->result2_); }

private:
    Function function_;
    Arg0 arg0_;
};

template <typename Arg0, typename Arg1>
class BindCallbackF2 final : public BindCallbackR0 {
public:
    typedef void (*Function)(Arg0, Arg1);

    BindCallbackF2() : function_(NULL), arg0_(Arg0()), arg1_(Arg1()) {}

    void Bind(Function function, Arg0 arg0, Arg1 arg1) {
        function_ = function;
        arg0_ = arg0;
        arg1_ = arg1;
    }

    /* override */ void Run() { function_(arg0_, arg1_); }

private:
    Function function_;
    Arg0 arg0_;
    Arg1 arg1_;
};

template <typename Arg0, typename Arg1, typename Result0>
class BindCallbackF2R1 final : public BindCallbackR1<Result0> {
public:
    typedef void (*Function)(Arg0, Arg1, Result0);

    BindCallbackF2R1() : function_(NULL), arg0_(Arg0()), arg1_(Arg1()) {}

    void Bind(Function function, Arg0 arg0, Arg1 arg1) {
        function_ = function;
        arg0_ = arg0;
        arg1_ = arg1;
    }

    /* override */ void Run() { function_(arg0_, arg1_, this->result0_); }

private:
    Function function_;
    Arg0 arg0_;
    Arg1 arg1_;
};

template <typename Arg0, typename Arg1, typename Result0, typename Result1>
class BindCallbackF2R2 final : public BindCallbackR2<Result0, Result1> {
public:
    typedef void (*Function)(Arg0, Arg1, Result0, Result1);

    BindCallbackF2R2() : function_(NULL), arg0_(Arg0()), arg1_(Arg1()) {}

    void Bind(Function function, Arg0 arg0, Arg1 arg1) {
        function_ = function;
        arg0_ = arg0;
        arg1_ = arg1;
    }

    /* override */ void Run() { function_(arg0_, arg1_, this->result0_, this->result1_); }

private:
    Function function_;
    Arg0 arg0_;
    Arg1 arg1_;
};

template <typename Arg0, typename Arg1, typename Result0, typename Result1, typename Result2>
class BindCallbackF2R3 final : public BindCallbackR3<Result0, Result1, Result2> {
public:
    typedef void (*Function)(Arg0, Arg1, Result0, Result1, Result2);

    BindCallbackF2R3() : function_(NULL), arg0_(Arg0()), arg1_(Arg1()) {}

    void Bind(Function function, Arg0 arg0, Arg1 arg1) {
        function_ = function;
        arg0_ = arg0;
        arg1_ = arg1;
    }

    /* override */ void Run() {
        function_(arg0_, arg1_, this->result0_, this->result1_, this->result2_);
    }

private:
    Function function_;
    Arg0 arg0_;
    Arg1 arg1_;
};

template <typename Arg0, typename Arg1, typename Arg2>
class BindCallbackF3 final : public BindCallbackR0 {
public:
    typedef void (*Function)(Arg0, Arg1, Arg2);

    BindCallbackF3() : function_(NULL), arg0_(Arg0()), arg1_(Arg1()), arg2_(Arg2()) {}

    void Bind(Function function, Arg0 arg0, Arg1 arg1, Arg2 arg2) {
        function_ = function;
        arg0_ = arg0;
        arg1_ = arg1;
        arg2_ = arg2;
    }

    /* override */ void Run() { function_(arg0_, arg1_, arg2_); }

private:
    Function function_;
    Arg0 arg0_;
    Arg1 arg1_;
    Arg2 arg2_;
};

template <typename Arg0, typename Arg1, typename Arg2, typename Result0>
class BindCallbackF3R1 final : public BindCallbackR1<Result0> {
public:
    typedef void (*Function)(Arg0, Arg1, Arg2, Result0);

    BindCallbackF3R1() : function_(NULL), arg0_(Arg0()), arg1_(Arg1()), arg2_(Arg2()) {}

    void Bind(Function function, Arg0 arg0, Arg1 arg1, Arg2 arg2) {
        function_ = function;
        arg0_ = arg0;
        arg1_ = arg1;
        arg2_ = arg2;
    }

    /* override */ void Run() { function_(arg0_, arg1_, arg2_, this->result0_); }

private:
    Function function_;
    Arg0 arg0_;
    Arg1 arg1_;
    Arg2 arg2_;
};

template <typename Arg0, typename Arg1, typename Arg2, typename Result0, typename Result1>
class BindCallbackF3R2 final : public BindCallbackR2<Result0, Result1> {
public:
    typedef void (*Function)(Arg0, Arg1, Arg2, Result0, Result1);

    BindCallbackF3R2() : function_(NULL), arg0_(Arg0()), arg1_(Arg1()), arg2_(Arg2()) {}

    void Bind(Function function, Arg0 arg0, Arg1 arg1, Arg2 arg2) {
        function_ = function;
        arg0_ = arg0;
        arg1_ = arg1;
        arg2_ = arg2;
    }

    /* override */ void Run() { function_(arg0_, arg1_, arg2_, this->result0_, this->result1_); }

private:
    Function function_;
    Arg0 arg0_;
    Arg1 arg1_;
    Arg2 arg2_;
};

template <typename Arg0, typename Arg1, typename Arg2, typename Result0, typename Result1,
          typename Result2>
class BindCallbackF3R3 final : public BindCallbackR3<Result0, Result1, Result2> {
public:
    typedef void (*Function)(Arg0, Arg1, Arg2, Result0, Result1, Result2);

    BindCallbackF3R3() : function_(NULL), arg0_(Arg0()), arg1_(Arg1()), arg2_(Arg2()) {}

    void Bind(Function function, Arg0 arg0, Arg1 arg1, Arg2 arg2) {
        function_ = function;
        arg0_ = arg0;
        arg1_ = arg1;
        arg2_ = arg2;
    }

    /* override */ void Run() {
        function_(arg0_, arg1_, arg2_, this->result0_, this->result1_, this->result2_);
    }

private:
    Function function_;
    Arg0 arg0_;
    Arg1 arg1_;
    Arg2 arg2_;
};

template <typename Arg0, typename Arg1, typename Arg2, typename Arg3>
class BindCallbackF4 final : public BindCallbackR0 {
public:
    typedef void (*Function)(Arg0, Arg1, Arg2, Arg3);

    BindCallbackF4()
        : function_(NULL), arg0_(Arg0()), arg1_(Arg1()), arg2_(Arg2()), arg3_(Arg3()) {}

    void Bind(Function function, Arg0 arg0, Arg1 arg1, Arg2 arg2, Arg3 arg3) {
        function_ = function;
        arg0_ = arg0;
        arg1_ = arg1;
        arg2_ = arg2;
        arg3_ = arg3;
    }

    /* override */ void Run() { function_(arg0_, arg1_, arg2_, arg3_); }

private:
    Function function_;
    Arg0 arg0_;
    Arg1 arg1_;
    Arg2 arg2_;
    Arg3 arg3_;
};

template <typename Arg0, typename Arg1, typename Arg2, typename Arg3, typename Result0>
class BindCallbackF4R1 final : public BindCallbackR1<Result0> {
public:
    typedef void (*Function)(Arg0, Arg1, Arg2, Arg3, Result0);

    BindCallbackF4R1()
        : function_(NULL), arg0_(Arg0()), arg1_(Arg1()), arg2_(Arg2()), arg3_(Arg3()) {}

    void Bind(Function function, Arg0 arg0, Arg1 arg1, Arg2 arg2, Arg3 arg3) {
        function_ = function;
        arg0_ = arg0;
        arg1_ = arg1;
        arg2_ = arg2;
        arg3_ = arg3;
    }

    /* override */ void Run() { function_(arg0_, arg1_, arg2_, arg3_, this->result0_); }

private:
    Function function_;
    Arg0 arg0_;
    Arg1 arg1_;
    Arg2 arg2_;
    Arg3 arg3_;
};

template <typename Arg0, typename Arg1, typename Arg2, typename Arg3, typename Result0,
          typename Result1>
class BindCallbackF4R2 final : public BindCallbackR2<Result0, Result1> {
public:
    typedef void (*Function)(Arg0, Arg1, Arg2, Arg3, Result0, Result1);

    BindCallbackF4R2()
        : function_(NULL), arg0_(Arg0()), arg1_(Arg1()), arg2_(Arg2()), arg3_(Arg3()) {}

    void Bind(Function function, Arg0 arg0, Arg1 arg1, Arg2 arg2, Arg3 arg3) {
        function_ = function;
        arg0_ = arg0;
        arg1_ = arg1;
        arg2_ = arg2;
        arg3_ = arg3;
    }

    /* override */ void Run() {
        function_(arg0_, arg1_, arg2_, arg3_, this->result0_, this->result1_);
    }

private:
    Function function_;
    Arg0 arg0_;
    Arg1 arg1_;
    Arg2 arg2_;
    Arg3 arg3_;
};

template <typename Arg0, typename Arg1, typename Arg2, typename Arg3, typename Result0,
          typename Result1, typename Result2>
class BindCallbackF4R3 final : public BindCallbackR3<Result0, Result1, Result2> {
public:
    typedef void (*Function)(Arg0, Arg1, Arg2, Arg3, Result0, Result1, Result2);

    BindCallbackF4R3()
        : function_(NULL), arg0_(Arg0()), arg1_(Arg1()), arg2_(Arg2()), arg3_(Arg3()) {}

    void Bind(Function function, Arg0 arg0, Arg1 arg1, Arg2 arg2, Arg3 arg3) {
        function_ = function;
        arg0_ = arg0;
        arg1_ = arg1;
        arg2_ = arg2;
        arg3_ = arg3;
    }

    /* override */ void Run() {
        function_(arg0_, arg1_, arg2_, arg3_, this->result0_, this->result1_, this->result2_);
    }

private:
    Function function_;
    Arg0 arg0_;
    Arg1 arg1_;
    Arg2 arg2_;
    Arg3 arg3_;
};

template <typename Arg0, typename Arg1, typename Arg2, typename Arg3, typename Arg4>
class BindCallbackF5 final : public BindCallbackR0 {
public:
    typedef void (*Function)(Arg0, Arg1, Arg2, Arg3, Arg4);

    BindCallbackF5()
        : function_(NULL),
          arg0_(Arg0()),
          arg1_(Arg1()),
          arg2_(Arg2()),
          arg3_(Arg3()),
          arg4_(Arg4()) {}

    void Bind(Function function, Arg0 arg0, Arg1 arg1, Arg2 arg2, Arg3 arg3, Arg4 arg4) {
        function_ = function;
        arg0_ = arg0;
        arg1_ = arg1;
        arg2_ = arg2;
        arg3_ = arg3;
        arg4_ = arg4;
    }

    /* override */ void Run() { function_(arg0_, arg1_, arg2_, arg3_, arg4_); }

private:
    Function function_;
    Arg0 arg0_;
    Arg1 arg1_;
    Arg2 arg2_;
    Arg3 arg3_;
    Arg4 arg4_;
};

template <typename Arg0, typename Arg1, typename Arg2, typename Arg3, typename Arg4,
          typename Result0>
class BindCallbackF5R1 final : public BindCallbackR1<Result0> {
public:
    typedef void (*Function)(Arg0, Arg1, Arg2, Arg3, Arg4, Result0);

    BindCallbackF5R1()
        : function_(NULL),
          arg0_(Arg0()),
          arg1_(Arg1()),
          arg2_(Arg2()),
          arg3_(Arg3()),
          arg4_(Arg4()) {}

    void Bind(Function function, Arg0 arg0, Arg1 arg1, Arg2 arg2, Arg3 arg3, Arg4 arg4) {
        function_ = function;
        arg0_ = arg0;
        arg1_ = arg1;
        arg2_ = arg2;
        arg3_ = arg3;
        arg4_ = arg4;
    }

    /* override */ void Run() { function_(arg0_, arg1_, arg2_, arg3_, arg4_, this->result0_); }

private:
    Function function_;
    Arg0 arg0_;
    Arg1 arg1_;
    Arg2 arg2_;
    Arg3 arg3_;
    Arg4 arg4_;
};

template <typename Arg0, typename Arg1, typename Arg2, typename Arg3, typename Arg4,
          typename Result0, typename Result1>
class BindCallbackF5R2 final : public BindCallbackR2<Result0, Result1> {
public:
    typedef void (*Function)(Arg0, Arg1, Arg2, Arg3, Arg4, Result0, Result1);

    BindCallbackF5R2()
        : function_(NULL),
          arg0_(Arg0()),
          arg1_(Arg1()),
          arg2_(Arg2()),
          arg3_(Arg3()),
          arg4_(Arg4()) {}

    void Bind(Function function, Arg0 arg0, Arg1 arg1, Arg2 arg2, Arg3 arg3, Arg4 arg4) {
        function_ = function;
        arg0_ = arg0;
        arg1_ = arg1;
        arg2_ = arg2;
        arg3_ = arg3;
        arg4_ = arg4;
    }

    /* override */ void Run() {
        function_(arg0_, arg1_, arg2_, arg3_, arg4_, this->result0_, this->result1_);
    }

private:
    Function function_;
    Arg0 arg0_;
    Arg1 arg1_;
    Arg2 arg2_;
    Arg3 arg3_;
    Arg4 arg4_;
};

template <typename Arg0, typename Arg1, typename Arg2, typename Arg3, typename Arg4,
          typename Result0, typename Result1, typename Result2>
class BindCallbackF5R3 final : public BindCallbackR3<Result0, Result1, Result2> {
public:
    typedef void (*Function)(Arg0, Arg1, Arg2, Arg3, Arg4, Result0, Result1, Result2);

    BindCallbackF5R3()
        : function_(NULL),
          arg0_(Arg0()),
          arg1_(Arg1()),
          arg2_(Arg2()),
          arg3_(Arg3()),
          arg4_(Arg4()) {}

    void Bind(Function function, Arg0 arg0, Arg1 arg1, Arg2 arg2, Arg3 arg3, Arg4 arg4) {
        function_ = function;
        arg0_ = arg0;
        arg1_ = arg1;
        arg2_ = arg2;
        arg3_ = arg3;
        arg4_ = arg4;
    }

    /* override */ void Run() {
        function_(arg0_, arg1_, arg2_, arg3_, arg4_, this->result0_, this->result1_,
                  this->result2_);
    }

private:
    Function function_;
    Arg0 arg0_;
    Arg1 arg1_;
    Arg2 arg2_;
    Arg3 arg3_;
    Arg4 arg4_;
};

template <typename Arg0, typename Arg1, typename Arg2, typename Arg3, typename Arg4, typename Arg5>
class BindCallbackF6 final : public BindCallbackR0 {
public:
    typedef void (*Function)(Arg0, Arg1, Arg2, Arg3, Arg4, Arg5);

    BindCallbackF6()
        : function_(NULL),
          arg0_(Arg0()),
          arg1_(Arg1()),
          arg2_(Arg2()),
          arg3_(Arg3()),
          arg4_(Arg4()),
          arg5_(Arg5()) {}

    void Bind(Function function, Arg0 arg0, Arg1 arg1, Arg2 arg2, Arg3 arg3, Arg4 arg4, Arg5 arg5) {
        function_ = function;
        arg0_ = arg0;
        arg1_ = arg1;
        arg2_ = arg2;
        arg3_ = arg3;
        arg4_ = arg4;
        arg5_ = arg5;
    }

    /* override */ void Run() { function_(arg0_, arg1_, arg2_, arg3_, arg4_, arg5_); }

private:
    Function function_;
    Arg0 arg0_;
    Arg1 arg1_;
    Arg2 arg2_;
    Arg3 arg3_;
    Arg4 arg4_;
    Arg5 arg5_;
};

template <typename Arg0, typename Arg1, typename Arg2, typename Arg3, typename Arg4, typename Arg5,
          typename Result0>
class BindCallbackF6R1 final : public BindCallbackR1<Result0> {
public:
    typedef void (*Function)(Arg0, Arg1, Arg2, Arg3, Arg4, Arg5, Result0);

    BindCallbackF6R1()
        : function_(NULL),
          arg0_(Arg0()),
          arg1_(Arg1()),
          arg2_(Arg2()),
          arg3_(Arg3()),
          arg4_(Arg4()),
          arg5_(Arg5()) {}

    void Bind(Function function, Arg0 arg0, Arg1 arg1, Arg2 arg2, Arg3 arg3, Arg4 arg4, Arg5 arg5) {
        function_ = function;
        arg0_ = arg0;
        arg1_ = arg1;
        arg2_ = arg2;
        arg3_ = arg3;
        arg4_ = arg4;
        arg5_ = arg5;
    }

    /* override */ void Run() {
        function_(arg0_, arg1_, arg2_, arg3_, arg4_, arg5_, this->result0_);
    }

private:
    Function function_;
    Arg0 arg0_;
    Arg1 arg1_;
    Arg2 arg2_;
    Arg3 arg3_;
    Arg4 arg4_;
    Arg5 arg5_;
};

template <typename Arg0, typename Arg1, typename Arg2, typename Arg3, typename Arg4, typename Arg5,
          typename Result0, typename Result1>
class BindCallbackF6R2 final : public BindCallbackR2<Result0, Result1> {
public:
    typedef void (*Function)(Arg0, Arg1, Arg2, Arg3, Arg4, Arg5, Result0, Result1);

    BindCallbackF6R2()
        : function_(NULL),
          arg0_(Arg0()),
          arg1_(Arg1()),
          arg2_(Arg2()),
          arg3_(Arg3()),
          arg4_(Arg4()),
          arg5_(Arg5()) {}

    void Bind(Function function, Arg0 arg0, Arg1 arg1, Arg2 arg2, Arg3 arg3, Arg4 arg4, Arg5 arg5) {
        function_ = function;
        arg0_ = arg0;
        arg1_ = arg1;
        arg2_ = arg2;
        arg3_ = arg3;
        arg4_ = arg4;
        arg5_ = arg5;
    }

    /* override */ void Run() {
        function_(arg0_, arg1_, arg2_, arg3_, arg4_, arg5_, this->result0_, this->result1_);
    }

private:
    Function function_;
    Arg0 arg0_;
    Arg1 arg1_;
    Arg2 arg2_;
    Arg3 arg3_;
    Arg4 arg4_;
    Arg5 arg5_;
};

template <typename Arg0, typename Arg1, typename Arg2, typename Arg3, typename Arg4, typename Arg5,
          typename Result0, typename Result1, typename Result2>
class BindCallbackF6R3 final : public BindCallbackR3<Result0, Result1, Result2> {
public:
    typedef void (*Function)(Arg0, Arg1, Arg2, Arg3, Arg4, Arg5, Result0, Result1, Result2);

    BindCallbackF6R3()
        : function_(NULL),
          arg0_(Arg0()),
          arg1_(Arg1()),
          arg2_(Arg2()),
          arg3_(Arg3()),
          arg4_(Arg4()),
          arg5_(Arg5()) {}

    void Bind(Function function, Arg0 arg0, Arg1 arg1, Arg2 arg2, Arg3 arg3, Arg4 arg4, Arg5 arg5) {
        function_ = function;
        arg0_ = arg0;
        arg1_ = arg1;
        arg2_ = arg2;
        arg3_ = arg3;
        arg4_ = arg4;
        arg5_ = arg5;
    }

    /* override */ void Run() {
        function_(arg0_, arg1_, arg2_, arg3_, arg4_, arg5_, this->result0_, this->result1_,
                  this->result2_);
    }

private:
    Function function_;
    Arg0 arg0_;
    Arg1 arg1_;
    Arg2 arg2_;
    Arg3 arg3_;
    Arg4 arg4_;
    Arg5 arg5_;
};

template <typename Arg0, typename Arg1, typename Arg2, typename Arg3, typename Arg4, typename Arg5,
          typename Arg6>
class BindCallbackF7 final : public BindCallbackR0 {
public:
    typedef void (*Function)(Arg0, Arg1, Arg2, Arg3, Arg4, Arg5, Arg6);

    BindCallbackF7()
        : function_(NULL),
          arg0_(Arg0()),
          arg1_(Arg1()),
          arg2_(Arg2()),
          arg3_(Arg3()),
          arg4_(Arg4()),
          arg5_(Arg5()),
          arg6_(Arg6()) {}

    void Bind(Function function, Arg0 arg0, Arg1 arg1, Arg2 arg2, Arg3 arg3, Arg4 arg4, Arg5 arg5,
              Arg6 arg6) {
        function_ = function;
        arg0_ = arg0;
        arg1_ = arg1;
        arg2_ = arg2;
        arg3_ = arg3;
        arg4_ = arg4;
        arg5_ = arg5;
        arg6_ = arg6;
    }

    /* override */ void Run() { function_(arg0_, arg1_, arg2_, arg3_, arg4_, arg5_, arg6_); }

private:
    Function function_;
    Arg0 arg0_;
    Arg1 arg1_;
    Arg2 arg2_;
    Arg3 arg3_;
    Arg4 arg4_;
    Arg5 arg5_;
    Arg6 arg6_;
};

template <typename Arg0, typename Arg1, typename Arg2, typename Arg3, typename Arg4, typename Arg5,
          typename Arg6, typename Result0>
class BindCallbackF7R1 final : public BindCallbackR1<Result0> {
public:
    typedef void (*Function)(Arg0, Arg1, Arg2, Arg3, Arg4, Arg5, Arg6, Result0);

    BindCallbackF7R1()
        : function_(NULL),
          arg0_(Arg0()),
          arg1_(Arg1()),
          arg2_(Arg2()),
          arg3_(Arg3()),
          arg4_(Arg4()),
          arg5_(Arg5()),
          arg6_(Arg6()) {}

    void Bind(Function function, Arg0 arg0, Arg1 arg1, Arg2 arg2, Arg3 arg3, Arg4 arg4, Arg5 arg5,
              Arg6 arg6) {
        function_ = function;
        arg0_ = arg0;
        arg1_ = arg1;
        arg2_ = arg2;
        arg3_ = arg3;
        arg4_ = arg4;
        arg5_ = arg5;
        arg6_ = arg6;
    }

    /* override */ void Run() {
        function_(arg0_, arg1_, arg2_, arg3_, arg4_, arg5_, arg6_, this->result0_);
    }

private:
    Function function_;
    Arg0 arg0_;
    Arg1 arg1_;
    Arg2 arg2_;
    Arg3 arg3_;
    Arg4 arg4_;
    Arg5 arg5_;
    Arg6 arg6_;
};

template <typename Arg0, typename Arg1, typename Arg2, typename Arg3, typename Arg4, typename Arg5,
          typename Arg6, typename Result0, typename Result1>
class BindCallbackF7R2 final : public BindCallbackR2<Result0, Result1> {
public:
    typedef void (*Function)(Arg0, Arg1, Arg2, Arg3, Arg4, Arg5, Arg6, Result0, Result1);

    BindCallbackF7R2()
        : function_(NULL),
          arg0_(Arg0()),
          arg1_(Arg1()),
          arg2_(Arg2()),
          arg3_(Arg3()),
          arg4_(Arg4()),
          arg5_(Arg5()),
          arg6_(Arg6()) {}

    void Bind(Function function, Arg0 arg0, Arg1 arg1, Arg2 arg2, Arg3 arg3, Arg4 arg4, Arg5 arg5,
              Arg6 arg6) {
        function_ = function;
        arg0_ = arg0;
        arg1_ = arg1;
        arg2_ = arg2;
        arg3_ = arg3;
        arg4_ = arg4;
        arg5_ = arg5;
        arg6_ = arg6;
    }

    /* override */ void Run() {
        function_(arg0_, arg1_, arg2_, arg3_, arg4_, arg5_, arg6_, this->result0_, this->result1_);
    }

private:
    Function function_;
    Arg0 arg0_;
    Arg1 arg1_;
    Arg2 arg2_;
    Arg3 arg3_;
    Arg4 arg4_;
    Arg5 arg5_;
    Arg6 arg6_;
};

template <typename Arg0, typename Arg1, typename Arg2, typename Arg3, typename Arg4, typename Arg5,
          typename Arg6, typename Result0, typename Result1, typename Result2>
class BindCallbackF7R3 final : public BindCallbackR3<Result0, Result1, Result2> {
public:
    typedef void (*Function)(Arg0, Arg1, Arg2, Arg3, Arg4, Arg5, Arg6, Result0, Result1, Result2);

    BindCallbackF7R3()
        : function_(NULL),
          arg0_(Arg0()),
          arg1_(Arg1()),
          arg2_(Arg2()),
          arg3_(Arg3()),
          arg4_(Arg4()),
          arg5_(Arg5()),
          arg6_(Arg6()) {}

    void Bind(Function function, Arg0 arg0, Arg1 arg1, Arg2 arg2, Arg3 arg3, Arg4 arg4, Arg5 arg5,
              Arg6 arg6) {
        function_ = function;
        arg0_ = arg0;
        arg1_ = arg1;
        arg2_ = arg2;
        arg3_ = arg3;
        arg4_ = arg4;
        arg5_ = arg5;
        arg6_ = arg6;
    }

    /* override */ void Run() {
        function_(arg0_, arg1_, arg2_, arg3_, arg4_, arg5_, arg6_, this->result0_, this->result1_,
                  this->result2_);
    }

private:
    Function function_;
    Arg0 arg0_;
    Arg1 arg1_;
    Arg2 arg2_;
    Arg3 arg3_;
    Arg4 arg4_;
    Arg5 arg5_;
    Arg6 arg6_;
};

template <typename Arg0, typename Arg1, typename Arg2, typename Arg3, typename Arg4, typename Arg5,
          typename Arg6, typename Arg7>
class BindCallbackF8 final : public BindCallbackR0 {
public:
    typedef void (*Function)(Arg0, Arg1, Arg2, Arg3, Arg4, Arg5, Arg6, Arg7);

    BindCallbackF8()
        : function_(NULL),
          arg0_(Arg0()),
          arg1_(Arg1()),
          arg2_(Arg2()),
          arg3_(Arg3()),
          arg4_(Arg4()),
          arg5_(Arg5()),
          arg6_(Arg6()),
          arg7_(Arg7()) {}

    void Bind(Function function, Arg0 arg0, Arg1 arg1, Arg2 arg2, Arg3 arg3, Arg4 arg4, Arg5 arg5,
              Arg6 arg6, Arg7 arg7) {
        function_ = function;
        arg0_ = arg0;
        arg1_ = arg1;
        arg2_ = arg2;
        arg3_ = arg3;
        arg4_ = arg4;
        arg5_ = arg5;
        arg6_ = arg6;
        arg7_ = arg7;
    }

    /* override */ void Run() { function_(arg0_, arg1_, arg2_, arg3_, arg4_, arg5_, arg6_, arg7_); }

private:
    Function function_;
    Arg0 arg0_;
    Arg1 arg1_;
    Arg2 arg2_;
    Arg3 arg3_;
    Arg4 arg4_;
    Arg5 arg5_;
    Arg6 arg6_;
    Arg7 arg7_;
};

template <typename Arg0, typename Arg1, typename Arg2, typename Arg3, typename Arg4, typename Arg5,
          typename Arg6, typename Arg7, typename Result0>
class BindCallbackF8R1 final : public BindCallbackR1<Result0> {
public:
    typedef void (*Function)(Arg0, Arg1, Arg2, Arg3, Arg4, Arg5, Arg6, Arg7, Result0);

    BindCallbackF8R1()
        : function_(NULL),
          arg0_(Arg0()),
          arg1_(Arg1()),
          arg2_(Arg2()),
          arg3_(Arg3()),
          arg4_(Arg4()),
          arg5_(Arg5()),
          arg6_(Arg6()),
          arg7_(Arg7()) {}

    void Bind(Function function, Arg0 arg0, Arg1 arg1, Arg2 arg2, Arg3 arg3, Arg4 arg4, Arg5 arg5,
              Arg6 arg6, Arg7 arg7) {
        function_ = function;
        arg0_ = arg0;
        arg1_ = arg1;
        arg2_ = arg2;
        arg3_ = arg3;
        arg4_ = arg4;
        arg5_ = arg5;
        arg6_ = arg6;
        arg7_ = arg7;
    }

    /* override */ void Run() {
        function_(arg0_, arg1_, arg2_, arg3_, arg4_, arg5_, arg6_, arg7_, this->result0_);
    }

private:
    Function function_;
    Arg0 arg0_;
    Arg1 arg1_;
    Arg2 arg2_;
    Arg3 arg3_;
    Arg4 arg4_;
    Arg5 arg5_;
    Arg6 arg6_;
    Arg7 arg7_;
};

template <typename Arg0, typename Arg1, typename Arg2, typename Arg3, typename Arg4, typename Arg5,
          typename Arg6, typename Arg7, typename Result0, typename Result1>
class BindCallbackF8R2 final : public BindCallbackR2<Result0, Result1> {
public:
    typedef void (*Function)(Arg0, Arg1, Arg2, Arg3, Arg4, Arg5, Arg6, Arg7, Result0, Result1);

    BindCallbackF8R2()
        : function_(NULL),
          arg0_(Arg0()),
          arg1_(Arg1()),
          arg2_(Arg2()),
          arg3_(Arg3()),
          arg4_(Arg4()),
          arg5_(Arg5()),
          arg6_(Arg6()),
          arg7_(Arg7()) {}

    void Bind(Function function, Arg0 arg0, Arg1 arg1, Arg2 arg2, Arg3 arg3, Arg4 arg4, Arg5 arg5,
              Arg6 arg6, Arg7 arg7) {
        function_ = function;
        arg0_ = arg0;
        arg1_ = arg1;
        arg2_ = arg2;
        arg3_ = arg3;
        arg4_ = arg4;
        arg5_ = arg5;
        arg6_ = arg6;
        arg7_ = arg7;
    }

    /* override */ void Run() {
        function_(arg0_, arg1_, arg2_, arg3_, arg4_, arg5_, arg6_, arg7_, this->result0_,
                  this->result1_);
    }

private:
    Function function_;
    Arg0 arg0_;
    Arg1 arg1_;
    Arg2 arg2_;
    Arg3 arg3_;
    Arg4 arg4_;
    Arg5 arg5_;
    Arg6 arg6_;
    Arg7 arg7_;
};

template <typename Arg0, typename Arg1, typename Arg2, typename Arg3, typename Arg4, typename Arg5,
          typename Arg6, typename Arg7, typename Result0, typename Result1, typename Result2>
class BindCallbackF8R3 final : public BindCallbackR3<Result0, Result1, Result2> {
public:
    typedef void (*Function)(Arg0, Arg1, Arg2, Arg3, Arg4, Arg5, Arg6, Arg7, Result0, Result1,
                             Result2);

    BindCallbackF8R3()
        : function_(NULL),
          arg0_(Arg0()),
          arg1_(Arg1()),
          arg2_(Arg2()),
          arg3_(Arg3()),
          arg4_(Arg4()),
          arg5_(Arg5()),
          arg6_(Arg6()),
          arg7_(Arg7()) {}

    void Bind(Function function, Arg0 arg0, Arg1 arg1, Arg2 arg2, Arg3 arg3, Arg4 arg4, Arg5 arg5,
              Arg6 arg6, Arg7 arg7) {
        function_ = function;
        arg0_ = arg0;
        arg1_ = arg1;
        arg2_ = arg2;
        arg3_ = arg3;
        arg4_ = arg4;
        arg5_ = arg5;
        arg6_ = arg6;
        arg7_ = arg7;
    }

    /* override */ void Run() {
        function_(arg0_, arg1_, arg2_, arg3_, arg4_, arg5_, arg6_, arg7_, this->result0_,
                  this->result1_, this->result2_);
    }

private:
    Function function_;
    Arg0 arg0_;
    Arg1 arg1_;
    Arg2 arg2_;
    Arg3 arg3_;
    Arg4 arg4_;
    Arg5 arg5_;
    Arg6 arg6_;
    Arg7 arg7_;
};

template <typename Arg0, typename Arg1, typename Arg2, typename Arg3, typename Arg4, typename Arg5,
          typename Arg6, typename Arg7, typename Arg8>
class BindCallbackF9 final : public BindCallbackR0 {
public:
    typedef void (*Function)(Arg0, Arg1, Arg2, Arg3, Arg4, Arg5, Arg6, Arg7, Arg8);

    BindCallbackF9()
        : function_(NULL),
          arg0_(Arg0()),
          arg1_(Arg1()),
          arg2_(Arg2()),
          arg3_(Arg3()),
          arg4_(Arg4()),
          arg5_(Arg5()),
          arg6_(Arg6()),
          arg7_(Arg7()),
          arg8_(Arg8()) {}

    void Bind(Function function, Arg0 arg0, Arg1 arg1, Arg2 arg2, Arg3 arg3, Arg4 arg4, Arg5 arg5,
              Arg6 arg6, Arg7 arg7, Arg8 arg8) {
        function_ = function;
        arg0_ = arg0;
        arg1_ = arg1;
        arg2_ = arg2;
        arg3_ = arg3;
        arg4_ = arg4;
        arg5_ = arg5;
        arg6_ = arg6;
        arg7_ = arg7;
        arg8_ = arg8;
    }

    /* override */ void Run() {
        function_(arg0_, arg1_, arg2_, arg3_, arg4_, arg5_, arg6_, arg7_, arg8_);
    }

private:
    Function function_;
    Arg0 arg0_;
    Arg1 arg1_;
    Arg2 arg2_;
    Arg3 arg3_;
    Arg4 arg4_;
    Arg5 arg5_;
    Arg6 arg6_;
    Arg7 arg7_;
    Arg8 arg8_;
};

template <typename Arg0, typename Arg1, typename Arg2, typename Arg3, typename Arg4, typename Arg5,
          typename Arg6, typename Arg7, typename Arg8, typename Result0>
class BindCallbackF9R1 final : public BindCallbackR1<Result0> {
public:
    typedef void (*Function)(Arg0, Arg1, Arg2, Arg3, Arg4, Arg5, Arg6, Arg7, Arg8, Result0);

    BindCallbackF9R1()
        : function_(NULL),
          arg0_(Arg0()),
          arg1_(Arg1()),
          arg2_(Arg2()),
          arg3_(Arg3()),
          arg4_(Arg4()),
          arg5_(Arg5()),
          arg6_(Arg6()),
          arg7_(Arg7()),
          arg8_(Arg8()) {}

    void Bind(Function function, Arg0 arg0, Arg1 arg1, Arg2 arg2, Arg3 arg3, Arg4 arg4, Arg5 arg5,
              Arg6 arg6, Arg7 arg7, Arg8 arg8) {
        function_ = function;
        arg0_ = arg0;
        arg1_ = arg1;
        arg2_ = arg2;
        arg3_ = arg3;
        arg4_ = arg4;
        arg5_ = arg5;
        arg6_ = arg6;
        arg7_ = arg7;
        arg8_ = arg8;
    }

    /* override */ void Run() {
        function_(arg0_, arg1_, arg2_, arg3_, arg4_, arg5_, arg6_, arg7_, arg8_, this->result0_);
    }

private:
    Function function_;
    Arg0 arg0_;
    Arg1 arg1_;
    Arg2 arg2_;
    Arg3 arg3_;
    Arg4 arg4_;
    Arg5 arg5_;
    Arg6 arg6_;
    Arg7 arg7_;
    Arg8 arg8_;
};

template <typename Arg0, typename Arg1, typename Arg2, typename Arg3, typename Arg4, typename Arg5,
          typename Arg6, typename Arg7, typename Arg8, typename Result0, typename Result1>
class BindCallbackF9R2 final : public BindCallbackR2<Result0, Result1> {
public:
    typedef void (*Function)(Arg0, Arg1, Arg2, Arg3, Arg4, Arg5, Arg6, Arg7, Arg8, Result0,
                             Result1);

    BindCallbackF9R2()
        : function_(NULL),
          arg0_(Arg0()),
          arg1_(Arg1()),
          arg2_(Arg2()),
          arg3_(Arg3()),
          arg4_(Arg4()),
          arg5_(Arg5()),
          arg6_(Arg6()),
          arg7_(Arg7()),
          arg8_(Arg8()) {}

    void Bind(Function function, Arg0 arg0, Arg1 arg1, Arg2 arg2, Arg3 arg3, Arg4 arg4, Arg5 arg5,
              Arg6 arg6, Arg7 arg7, Arg8 arg8) {
        function_ = function;
        arg0_ = arg0;
        arg1_ = arg1;
        arg2_ = arg2;
        arg3_ = arg3;
        arg4_ = arg4;
        arg5_ = arg5;
        arg6_ = arg6;
        arg7_ = arg7;
        arg8_ = arg8;
    }

    /* override */ void Run() {
        function_(arg0_, arg1_, arg2_, arg3_, arg4_, arg5_, arg6_, arg7_, arg8_, this->result0_,
                  this->result1_);
    }

private:
    Function function_;
    Arg0 arg0_;
    Arg1 arg1_;
    Arg2 arg2_;
    Arg3 arg3_;
    Arg4 arg4_;
    Arg5 arg5_;
    Arg6 arg6_;
    Arg7 arg7_;
    Arg8 arg8_;
};

template <typename Arg0, typename Arg1, typename Arg2, typename Arg3, typename Arg4, typename Arg5,
          typename Arg6, typename Arg7, typename Arg8, typename Result0, typename Result1,
          typename Result2>
class BindCallbackF9R3 final : public BindCallbackR3<Result0, Result1, Result2> {
public:
    typedef void (*Function)(Arg0, Arg1, Arg2, Arg3, Arg4, Arg5, Arg6, Arg7, Arg8, Result0, Result1,
                             Result2);

    BindCallbackF9R3()
        : function_(NULL),
          arg0_(Arg0()),
          arg1_(Arg1()),
          arg2_(Arg2()),
          arg3_(Arg3()),
          arg4_(Arg4()),
          arg5_(Arg5()),
          arg6_(Arg6()),
          arg7_(Arg7()),
          arg8_(Arg8()) {}

    void Bind(Function function, Arg0 arg0, Arg1 arg1, Arg2 arg2, Arg3 arg3, Arg4 arg4, Arg5 arg5,
              Arg6 arg6, Arg7 arg7, Arg8 arg8) {
        function_ = function;
        arg0_ = arg0;
        arg1_ = arg1;
        arg2_ = arg2;
        arg3_ = arg3;
        arg4_ = arg4;
        arg5_ = arg5;
        arg6_ = arg6;
        arg7_ = arg7;
        arg8_ = arg8;
    }

    /* override */ void Run() {
        function_(arg0_, arg1_, arg2_, arg3_, arg4_, arg5_, arg6_, arg7_, arg8_, this->result0_,
                  this->result1_, this->result2_);
    }

private:
    Function function_;
    Arg0 arg0_;
    Arg1 arg1_;
    Arg2 arg2_;
    Arg3 arg3_;
    Arg4 arg4_;
    Arg5 arg5_;
    Arg6 arg6_;
    Arg7 arg7_;
    Arg8 arg8_;
};

template <typename Arg0, typename Arg1, typename Arg2, typename Arg3, typename Arg4, typename Arg5,
          typename Arg6, typename Arg7, typename Arg8, typename Arg9>
class BindCallbackF10 final : public BindCallbackR0 {
public:
    typedef void (*Function)(Arg0, Arg1, Arg2, Arg3, Arg4, Arg5, Arg6, Arg7, Arg8, Arg9);

    BindCallbackF10()
        : function_(NULL),
          arg0_(Arg0()),
          arg1_(Arg1()),
          arg2_(Arg2()),
          arg3_(Arg3()),
          arg4_(Arg4()),
          arg5_(Arg5()),
          arg6_(Arg6()),
          arg7_(Arg7()),
          arg8_(Arg8()),
          arg9_(Arg9()) {}

    void Bind(Function function, Arg0 arg0, Arg1 arg1, Arg2 arg2, Arg3 arg3, Arg4 arg4, Arg5 arg5,
              Arg6 arg6, Arg7 arg7, Arg8 arg8, Arg9 arg9) {
        function_ = function;
        arg0_ = arg0;
        arg1_ = arg1;
        arg2_ = arg2;
        arg3_ = arg3;
        arg4_ = arg4;
        arg5_ = arg5;
        arg6_ = arg6;
        arg7_ = arg7;
        arg8_ = arg8;
        arg9_ = arg9;
    }

    /* override */ void Run() {
        function_(arg0_, arg1_, arg2_, arg3_, arg4_, arg5_, arg6_, arg7_, arg8_, arg9_);
    }

private:
    Function function_;
    Arg0 arg0_;
    Arg1 arg1_;
    Arg2 arg2_;
    Arg3 arg3_;
    Arg4 arg4_;
    Arg5 arg5_;
    Arg6 arg6_;
    Arg7 arg7_;
    Arg8 arg8_;
    Arg9 arg9_;
};

template <typename Arg0, typename Arg1, typename Arg2, typename Arg3, typename Arg4, typename Arg5,
          typename Arg6, typename Arg7, typename Arg8, typename Arg9, typename Result0>
class BindCallbackF10R1 final : public BindCallbackR1<Result0> {
public:
    typedef void (*Function)(Arg0, Arg1, Arg2, Arg3, Arg4, Arg5, Arg6, Arg7, Arg8, Arg9, Result0);

    BindCallbackF10R1()
        : function_(NULL),
          arg0_(Arg0()),
          arg1_(Arg1()),
          arg2_(Arg2()),
          arg3_(Arg3()),
          arg4_(Arg4()),
          arg5_(Arg5()),
          arg6_(Arg6()),
          arg7_(Arg7()),
          arg8_(Arg8()),
          arg9_(Arg9()) {}

    void Bind(Function function, Arg0 arg0, Arg1 arg1, Arg2 arg2, Arg3 arg3, Arg4 arg4, Arg5 arg5,
              Arg6 arg6, Arg7 arg7, Arg8 arg8, Arg9 arg9) {
        function_ = function;
        arg0_ = arg0;
        arg1_ = arg1;
        arg2_ = arg2;
        arg3_ = arg3;
        arg4_ = arg4;
        arg5_ = arg5;
        arg6_ = arg6;
        arg7_ = arg7;
        arg8_ = arg8;
        arg9_ = arg9;
    }

    /* override */ void Run() {
        function_(arg0_, arg1_, arg2_, arg3_, arg4_, arg5_, arg6_, arg7_, arg8_, arg9_,
                  this->result0_);
    }

private:
    Function function_;
    Arg0 arg0_;
    Arg1 arg1_;
    Arg2 arg2_;
    Arg3 arg3_;
    Arg4 arg4_;
    Arg5 arg5_;
    Arg6 arg6_;
    Arg7 arg7_;
    Arg8 arg8_;
    Arg9 arg9_;
};

template <typename Arg0, typename Arg1, typename Arg2, typename Arg3, typename Arg4, typename Arg5,
          typename Arg6, typename Arg7, typename Arg8, typename Arg9, typename Result0,
          typename Result1>
class BindCallbackF10R2 final : public BindCallbackR2<Result0, Result1> {
public:
    typedef void (*Function)(Arg0, Arg1, Arg2, Arg3, Arg4, Arg5, Arg6, Arg7, Arg8, Arg9, Result0,
                             Result1);

    BindCallbackF10R2()
        : function_(NULL),
          arg0_(Arg0()),
          arg1_(Arg1()),
          arg2_(Arg2()),
          arg3_(Arg3()),
          arg4_(Arg4()),
          arg5_(Arg5()),
          arg6_(Arg6()),
          arg7_(Arg7()),
          arg8_(Arg8()),
          arg9_(Arg9()) {}

    void Bind(Function function, Arg0 arg0, Arg1 arg1, Arg2 arg2, Arg3 arg3, Arg4 arg4, Arg5 arg5,
              Arg6 arg6, Arg7 arg7, Arg8 arg8, Arg9 arg9) {
        function_ = function;
        arg0_ = arg0;
        arg1_ = arg1;
        arg2_ = arg2;
        arg3_ = arg3;
        arg4_ = arg4;
        arg5_ = arg5;
        arg6_ = arg6;
        arg7_ = arg7;
        arg8_ = arg8;
        arg9_ = arg9;
    }

    /* override */ void Run() {
        function_(arg0_, arg1_, arg2_, arg3_, arg4_, arg5_, arg6_, arg7_, arg8_, arg9_,
                  this->result0_, this->result1_);
    }

private:
    Function function_;
    Arg0 arg0_;
    Arg1 arg1_;
    Arg2 arg2_;
    Arg3 arg3_;
    Arg4 arg4_;
    Arg5 arg5_;
    Arg6 arg6_;
    Arg7 arg7_;
    Arg8 arg8_;
    Arg9 arg9_;
};

template <typename Arg0, typename Arg1, typename Arg2, typename Arg3, typename Arg4, typename Arg5,
          typename Arg6, typename Arg7, typename Arg8, typename Arg9, typename Result0,
          typename Result1, typename Result2>
class BindCallbackF10R3 final : public BindCallbackR3<Result0, Result1, Result2> {
public:
    typedef void (*Function)(Arg0, Arg1, Arg2, Arg3, Arg4, Arg5, Arg6, Arg7, Arg8, Arg9, Result0,
                             Result1, Result2);

    BindCallbackF10R3()
        : function_(NULL),
          arg0_(Arg0()),
          arg1_(Arg1()),
          arg2_(Arg2()),
          arg3_(Arg3()),
          arg4_(Arg4()),
          arg5_(Arg5()),
          arg6_(Arg6()),
          arg7_(Arg7()),
          arg8_(Arg8()),
          arg9_(Arg9()) {}

    void Bind(Function function, Arg0 arg0, Arg1 arg1, Arg2 arg2, Arg3 arg3, Arg4 arg4, Arg5 arg5,
              Arg6 arg6, Arg7 arg7, Arg8 arg8, Arg9 arg9) {
        function_ = function;
        arg0_ = arg0;
        arg1_ = arg1;
        arg2_ = arg2;
        arg3_ = arg3;
        arg4_ = arg4;
        arg5_ = arg5;
        arg6_ = arg6;
        arg7_ = arg7;
        arg8_ = arg8;
        arg9_ = arg9;
    }

    /* override */ void Run() {
        function_(arg0_, arg1_, arg2_, arg3_, arg4_, arg5_, arg6_, arg7_, arg8_, arg9_,
                  this->result0_, this->result1_, this->result2_);
    }

private:
    Function function_;
    Arg0 arg0_;
    Arg1 arg1_;
    Arg2 arg2_;
    Arg3 arg3_;
    Arg4 arg4_;
    Arg5 arg5_;
    Arg6 arg6_;
    Arg7 arg7_;
    Arg8 arg8_;
    Arg9 arg9_;
};

template <typename Arg0, typename Arg1, typename Arg2, typename Arg3, typename Arg4, typename Arg5,
          typename Arg6, typename Arg7, typename Arg8, typename Arg9, typename Arg10>
class BindCallbackF11 final : public BindCallbackR0 {
public:
    typedef void (*Function)(Arg0, Arg1, Arg2, Arg3, Arg4, Arg5, Arg6, Arg7, Arg8, Arg9, Arg10);

    BindCallbackF11()
        : function_(NULL),
          arg0_(Arg0()),
          arg1_(Arg1()),
          arg2_(Arg2()),
          arg3_(Arg3()),
          arg4_(Arg4()),
          arg5_(Arg5()),
          arg6_(Arg6()),
          arg7_(Arg7()),
          arg8_(Arg8()),
          arg9_(Arg9()),
          arg10_(Arg10()) {}

    void Bind(Function function, Arg0 arg0, Arg1 arg1, Arg2 arg2, Arg3 arg3, Arg4 arg4, Arg5 arg5,
              Arg6 arg6, Arg7 arg7, Arg8 arg8, Arg9 arg9, Arg10 arg10) {
        function_ = function;
        arg0_ = arg0;
        arg1_ = arg1;
        arg2_ = arg2;
        arg3_ = arg3;
        arg4_ = arg4;
        arg5_ = arg5;
        arg6_ = arg6;
        arg7_ = arg7;
        arg8_ = arg8;
        arg9_ = arg9;
        arg10_ = arg10;
    }

    /* override */ void Run() {
        function_(arg0_, arg1_, arg2_, arg3_, arg4_, arg5_, arg6_, arg7_, arg8_, arg9_, arg10_);
    }

private:
    Function function_;
    Arg0 arg0_;
    Arg1 arg1_;
    Arg2 arg2_;
    Arg3 arg3_;
    Arg4 arg4_;
    Arg5 arg5_;
    Arg6 arg6_;
    Arg7 arg7_;
    Arg8 arg8_;
    Arg9 arg9_;
    Arg10 arg10_;
};

template <typename Arg0, typename Arg1, typename Arg2, typename Arg3, typename Arg4, typename Arg5,
          typename Arg6, typename Arg7, typename Arg8, typename Arg9, typename Arg10,
          typename Result0>
class BindCallbackF11R1 final : public BindCallbackR1<Result0> {
public:
    typedef void (*Function)(Arg0, Arg1, Arg2, Arg3, Arg4, Arg5, Arg6, Arg7, Arg8, Arg9, Arg10,
                             Result0);

    BindCallbackF11R1()
        : function_(NULL),
          arg0_(Arg0()),
          arg1_(Arg1()),
          arg2_(Arg2()),
          arg3_(Arg3()),
          arg4_(Arg4()),
          arg5_(Arg5()),
          arg6_(Arg6()),
          arg7_(Arg7()),
          arg8_(Arg8()),
          arg9_(Arg9()),
          arg10_(Arg10()) {}

    void Bind(Function function, Arg0 arg0, Arg1 arg1, Arg2 arg2, Arg3 arg3, Arg4 arg4, Arg5 arg5,
              Arg6 arg6, Arg7 arg7, Arg8 arg8, Arg9 arg9, Arg10 arg10) {
        function_ = function;
        arg0_ = arg0;
        arg1_ = arg1;
        arg2_ = arg2;
        arg3_ = arg3;
        arg4_ = arg4;
        arg5_ = arg5;
        arg6_ = arg6;
        arg7_ = arg7;
        arg8_ = arg8;
        arg9_ = arg9;
        arg10_ = arg10;
    }

    /* override */ void Run() {
        function_(arg0_, arg1_, arg2_, arg3_, arg4_, arg5_, arg6_, arg7_, arg8_, arg9_, arg10_,
                  this->result0_);
    }

private:
    Function function_;
    Arg0 arg0_;
    Arg1 arg1_;
    Arg2 arg2_;
    Arg3 arg3_;
    Arg4 arg4_;
    Arg5 arg5_;
    Arg6 arg6_;
    Arg7 arg7_;
    Arg8 arg8_;
    Arg9 arg9_;
    Arg10 arg10_;
};

template <typename Arg0, typename Arg1, typename Arg2, typename Arg3, typename Arg4, typename Arg5,
          typename Arg6, typename Arg7, typename Arg8, typename Arg9, typename Arg10,
          typename Result0, typename Result1>
class BindCallbackF11R2 final : public BindCallbackR2<Result0, Result1> {
public:
    typedef void (*Function)(Arg0, Arg1, Arg2, Arg3, Arg4, Arg5, Arg6, Arg7, Arg8, Arg9, Arg10,
                             Result0, Result1);

    BindCallbackF11R2()
        : function_(NULL),
          arg0_(Arg0()),
          arg1_(Arg1()),
          arg2_(Arg2()),
          arg3_(Arg3()),
          arg4_(Arg4()),
          arg5_(Arg5()),
          arg6_(Arg6()),
          arg7_(Arg7()),
          arg8_(Arg8()),
          arg9_(Arg9()),
          arg10_(Arg10()) {}

    void Bind(Function function, Arg0 arg0, Arg1 arg1, Arg2 arg2, Arg3 arg3, Arg4 arg4, Arg5 arg5,
              Arg6 arg6, Arg7 arg7, Arg8 arg8, Arg9 arg9, Arg10 arg10) {
        function_ = function;
        arg0_ = arg0;
        arg1_ = arg1;
        arg2_ = arg2;
        arg3_ = arg3;
        arg4_ = arg4;
        arg5_ = arg5;
        arg6_ = arg6;
        arg7_ = arg7;
        arg8_ = arg8;
        arg9_ = arg9;
        arg10_ = arg10;
    }

    /* override */ void Run() {
        function_(arg0_, arg1_, arg2_, arg3_, arg4_, arg5_, arg6_, arg7_, arg8_, arg9_, arg10_,
                  this->result0_, this->result1_);
    }

private:
    Function function_;
    Arg0 arg0_;
    Arg1 arg1_;
    Arg2 arg2_;
    Arg3 arg3_;
    Arg4 arg4_;
    Arg5 arg5_;
    Arg6 arg6_;
    Arg7 arg7_;
    Arg8 arg8_;
    Arg9 arg9_;
    Arg10 arg10_;
};

template <typename Arg0, typename Arg1, typename Arg2, typename Arg3, typename Arg4, typename Arg5,
          typename Arg6, typename Arg7, typename Arg8, typename Arg9, typename Arg10,
          typename Result0, typename Result1, typename Result2>
class BindCallbackF11R3 final : public BindCallbackR3<Result0, Result1, Result2> {
public:
    typedef void (*Function)(Arg0, Arg1, Arg2, Arg3, Arg4, Arg5, Arg6, Arg7, Arg8, Arg9, Arg10,
                             Result0, Result1, Result2);

    BindCallbackF11R3()
        : function_(NULL),
          arg0_(Arg0()),
          arg1_(Arg1()),
          arg2_(Arg2()),
          arg3_(Arg3()),
          arg4_(Arg4()),
          arg5_(Arg5()),
          arg6_(Arg6()),
          arg7_(Arg7()),
          arg8_(Arg8()),
          arg9_(Arg9()),
          arg10_(Arg10()) {}

    void Bind(Function function, Arg0 arg0, Arg1 arg1, Arg2 arg2, Arg3 arg3, Arg4 arg4, Arg5 arg5,
              Arg6 arg6, Arg7 arg7, Arg8 arg8, Arg9 arg9, Arg10 arg10) {
        function_ = function;
        arg0_ = arg0;
        arg1_ = arg1;
        arg2_ = arg2;
        arg3_ = arg3;
        arg4_ = arg4;
        arg5_ = arg5;
        arg6_ = arg6;
        arg7_ = arg7;
        arg8_ = arg8;
        arg9_ = arg9;
        arg10_ = arg10;
    }

    /* override */ void Run() {
        function_(arg0_, arg1_, arg2_, arg3_, arg4_, arg5_, arg6_, arg7_, arg8_, arg9_, arg10_,
                  this->result0_, this->result1_, this->result2_);
    }

private:
    Function function_;
    Arg0 arg0_;
    Arg1 arg1_;
    Arg2 arg2_;
    Arg3 arg3_;
    Arg4 arg4_;
    Arg5 arg5_;
    Arg6 arg6_;
    Arg7 arg7_;
    Arg8 arg8_;
    Arg9 arg9_;
    Arg10 arg10_;
};

template <typename Arg0, typename Arg1, typename Arg2, typename Arg3, typename Arg4, typename Arg5,
          typename Arg6, typename Arg7, typename Arg8, typename Arg9, typename Arg10,
          typename Arg11>
class BindCallbackF12 final : public BindCallbackR0 {
public:
    typedef void (*Function)(Arg0, Arg1, Arg2, Arg3, Arg4, Arg5, Arg6, Arg7, Arg8, Arg9, Arg10,
                             Arg11);

    BindCallbackF12()
        : function_(NULL),
          arg0_(Arg0()),
          arg1_(Arg1()),
          arg2_(Arg2()),
          arg3_(Arg3()),
          arg4_(Arg4()),
          arg5_(Arg5()),
          arg6_(Arg6()),
          arg7_(Arg7()),
          arg8_(Arg8()),
          arg9_(Arg9()),
          arg10_(Arg10()),
          arg11_(Arg11()) {}

    void Bind(Function function, Arg0 arg0, Arg1 arg1, Arg2 arg2, Arg3 arg3, Arg4 arg4, Arg5 arg5,
              Arg6 arg6, Arg7 arg7, Arg8 arg8, Arg9 arg9, Arg10 arg10, Arg11 arg11) {
        function_ = function;
        arg0_ = arg0;
        arg1_ = arg1;
        arg2_ = arg2;
        arg3_ = arg3;
        arg4_ = arg4;
        arg5_ = arg5;
        arg6_ = arg6;
        arg7_ = arg7;
        arg8_ = arg8;
        arg9_ = arg9;
        arg10_ = arg10;
        arg11_ = arg11;
    }

    /* override */ void Run() {
        function_(arg0_, arg1_, arg2_, arg3_, arg4_, arg5_, arg6_, arg7_, arg8_, arg9_, arg10_,
                  arg11_);
    }

private:
    Function function_;
    Arg0 arg0_;
    Arg1 arg1_;
    Arg2 arg2_;
    Arg3 arg3_;
    Arg4 arg4_;
    Arg5 arg5_;
    Arg6 arg6_;
    Arg7 arg7_;
    Arg8 arg8_;
    Arg9 arg9_;
    Arg10 arg10_;
    Arg11 arg11_;
};

template <typename Arg0, typename Arg1, typename Arg2, typename Arg3, typename Arg4, typename Arg5,
          typename Arg6, typename Arg7, typename Arg8, typename Arg9, typename Arg10,
          typename Arg11, typename Result0>
class BindCallbackF12R1 final : public BindCallbackR1<Result0> {
public:
    typedef void (*Function)(Arg0, Arg1, Arg2, Arg3, Arg4, Arg5, Arg6, Arg7, Arg8, Arg9, Arg10,
                             Arg11, Result0);

    BindCallbackF12R1()
        : function_(NULL),
          arg0_(Arg0()),
          arg1_(Arg1()),
          arg2_(Arg2()),
          arg3_(Arg3()),
          arg4_(Arg4()),
          arg5_(Arg5()),
          arg6_(Arg6()),
          arg7_(Arg7()),
          arg8_(Arg8()),
          arg9_(Arg9()),
          arg10_(Arg10()),
          arg11_(Arg11()) {}

    void Bind(Function function, Arg0 arg0, Arg1 arg1, Arg2 arg2, Arg3 arg3, Arg4 arg4, Arg5 arg5,
              Arg6 arg6, Arg7 arg7, Arg8 arg8, Arg9 arg9, Arg10 arg10, Arg11 arg11) {
        function_ = function;
        arg0_ = arg0;
        arg1_ = arg1;
        arg2_ = arg2;
        arg3_ = arg3;
        arg4_ = arg4;
        arg5_ = arg5;
        arg6_ = arg6;
        arg7_ = arg7;
        arg8_ = arg8;
        arg9_ = arg9;
        arg10_ = arg10;
        arg11_ = arg11;
    }

    /* override */ void Run() {
        function_(arg0_, arg1_, arg2_, arg3_, arg4_, arg5_, arg6_, arg7_, arg8_, arg9_, arg10_,
                  arg11_, this->result0_);
    }

private:
    Function function_;
    Arg0 arg0_;
    Arg1 arg1_;
    Arg2 arg2_;
    Arg3 arg3_;
    Arg4 arg4_;
    Arg5 arg5_;
    Arg6 arg6_;
    Arg7 arg7_;
    Arg8 arg8_;
    Arg9 arg9_;
    Arg10 arg10_;
    Arg11 arg11_;
};

template <typename Arg0, typename Arg1, typename Arg2, typename Arg3, typename Arg4, typename Arg5,
          typename Arg6, typename Arg7, typename Arg8, typename Arg9, typename Arg10,
          typename Arg11, typename Result0, typename Result1>
class BindCallbackF12R2 final : public BindCallbackR2<Result0, Result1> {
public:
    typedef void (*Function)(Arg0, Arg1, Arg2, Arg3, Arg4, Arg5, Arg6, Arg7, Arg8, Arg9, Arg10,
                             Arg11, Result0, Result1);

    BindCallbackF12R2()
        : function_(NULL),
          arg0_(Arg0()),
          arg1_(Arg1()),
          arg2_(Arg2()),
          arg3_(Arg3()),
          arg4_(Arg4()),
          arg5_(Arg5()),
          arg6_(Arg6()),
          arg7_(Arg7()),
          arg8_(Arg8()),
          arg9_(Arg9()),
          arg10_(Arg10()),
          arg11_(Arg11()) {}

    void Bind(Function function, Arg0 arg0, Arg1 arg1, Arg2 arg2, Arg3 arg3, Arg4 arg4, Arg5 arg5,
              Arg6 arg6, Arg7 arg7, Arg8 arg8, Arg9 arg9, Arg10 arg10, Arg11 arg11) {
        function_ = function;
        arg0_ = arg0;
        arg1_ = arg1;
        arg2_ = arg2;
        arg3_ = arg3;
        arg4_ = arg4;
        arg5_ = arg5;
        arg6_ = arg6;
        arg7_ = arg7;
        arg8_ = arg8;
        arg9_ = arg9;
        arg10_ = arg10;
        arg11_ = arg11;
    }

    /* override */ void Run() {
        function_(arg0_, arg1_, arg2_, arg3_, arg4_, arg5_, arg6_, arg7_, arg8_, arg9_, arg10_,
                  arg11_, this->result0_, this->result1_);
    }

private:
    Function function_;
    Arg0 arg0_;
    Arg1 arg1_;
    Arg2 arg2_;
    Arg3 arg3_;
    Arg4 arg4_;
    Arg5 arg5_;
    Arg6 arg6_;
    Arg7 arg7_;
    Arg8 arg8_;
    Arg9 arg9_;
    Arg10 arg10_;
    Arg11 arg11_;
};

template <typename Arg0, typename Arg1, typename Arg2, typename Arg3, typename Arg4, typename Arg5,
          typename Arg6, typename Arg7, typename Arg8, typename Arg9, typename Arg10,
          typename Arg11, typename Result0, typename Result1, typename Result2>
class BindCallbackF12R3 final : public BindCallbackR3<Result0, Result1, Result2> {
public:
    typedef void (*Function)(Arg0, Arg1, Arg2, Arg3, Arg4, Arg5, Arg6, Arg7, Arg8, Arg9, Arg10,
                             Arg11, Result0, Result1, Result2);

    BindCallbackF12R3()
        : function_(NULL),
          arg0_(Arg0()),
          arg1_(Arg1()),
          arg2_(Arg2()),
          arg3_(Arg3()),
          arg4_(Arg4()),
          arg5_(Arg5()),
          arg6_(Arg6()),
          arg7_(Arg7()),
          arg8_(Arg8()),
          arg9_(Arg9()),
          arg10_(Arg10()),
          arg11_(Arg11()) {}

    void Bind(Function function, Arg0 arg0, Arg1 arg1, Arg2 arg2, Arg3 arg3, Arg4 arg4, Arg5 arg5,
              Arg6 arg6, Arg7 arg7, Arg8 arg8, Arg9 arg9, Arg10 arg10, Arg11 arg11) {
        function_ = function;
        arg0_ = arg0;
        arg1_ = arg1;
        arg2_ = arg2;
        arg3_ = arg3;
        arg4_ = arg4;
        arg5_ = arg5;
        arg6_ = arg6;
        arg7_ = arg7;
        arg8_ = arg8;
        arg9_ = arg9;
        arg10_ = arg10;
        arg11_ = arg11;
    }

    /* override */ void Run() {
        function_(arg0_, arg1_, arg2_, arg3_, arg4_, arg5_, arg6_, arg7_, arg8_, arg9_, arg10_,
                  arg11_, this->result0_, this->result1_, this->result2_);
    }

private:
    Function function_;
    Arg0 arg0_;
    Arg1 arg1_;
    Arg2 arg2_;
    Arg3 arg3_;
    Arg4 arg4_;
    Arg5 arg5_;
    Arg6 arg6_;
    Arg7 arg7_;
    Arg8 arg8_;
    Arg9 arg9_;
    Arg10 arg10_;
    Arg11 arg11_;
};

}  // namespace byte
