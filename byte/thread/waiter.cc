// Copyright (c) 2018-present, ByteDance Inc. All rights reserved.
#include "byte/thread/waiter.h"

#include <absl/time/clock.h>
#include <absl/time/time.h>

namespace byte {

Waiter::Waiter(uint64_t timeout) : timeout_(timeout), completed_(false) {}

Waiter::~Waiter() {}

Status Waiter::Wait(uint64_t timeout_ms) {
    std::unique_lock<bthread::Mutex> lk(mutex_);
    if (completed_) {
        return status_;
    }
    uint64_t timeout = 0;
    if (timeout_ms != 0) {
        timeout = timeout_ms;
    } else {
        if (timeout_ != 0) {
            timeout = timeout_;
        }
    }

    // based comments of brpc/src/bthread/conditional_variable.cpp:bthread_cond_wait,
    // a cond may be return from wait event it's not signaled
    if (timeout) {
        uint64_t deadline = absl::ToUnixMillis(absl::Now()) + timeout;
        while (!completed_) {
            uint64_t now = absl::ToUnixMillis(absl::Now());
            if (now >= deadline) {
                return Status::Timeout("waiter timeout!");
            }
            cond_.wait_for(lk, (deadline - now) * 1000);
        }
    } else {
        while (!completed_) {
            cond_.wait(lk);
        }
    }
    return status_;
}

void Waiter::Signal(Status status) {
    std::unique_lock<bthread::Mutex> lk(mutex_);
    completed_ = true;
    status_ = status;
    cond_.notify_one();
}

void Waiter::SignalAll(byte::Status status) {
    std::unique_lock<bthread::Mutex> lk(mutex_);
    completed_ = true;
    status_ = status;
    cond_.notify_all();
}

}  // namespace byte
