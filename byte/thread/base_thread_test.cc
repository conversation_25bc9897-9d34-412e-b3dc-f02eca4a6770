// Copyright (c) 2011, The Toft Authors.
// All rights reserved.
//
// Author: <PERSON><PERSON> <PERSON> <<EMAIL>>
// Created: 05/13/11
// Description:

#include <atomic>
#include <string>
#include <vector>

#include "gtest/gtest.h"
#include "byte/thread/base_thread.h"
#include "byte/thread/detail/thread_types.h"
#include "byte/thread/this_thread.h"

namespace byte {
namespace {

void DoNothing() {}

void BaseThreadCallback(int* p) { ++*p; }

TEST(BaseThread, Construct) {
    int n = 0;
    BaseThread thread(std::bind(BaseThreadCallback, &n));
    thread.Join();
    EXPECT_EQ(1, n);
}

TEST(BaseThread, Start) {
    int n = 0;
    BaseThread thread;
    thread.Start(std::bind(BaseThreadCallback, &n));
    thread.Join();
    EXPECT_EQ(1, n);
}

void VerifyName(const std::string& name) {
  char buf[100];
  ASSERT_EQ(0, pthread_getname_np(pthread_self(), buf, sizeof(buf)));
  EXPECT_EQ(std::string(buf), name);
}

TEST(BaseThread, SetName) {
  BaseThread thread;
  ThreadAttributes attrs;
  std::string name = "byte_name";
  attrs.SetName(name);
  thread.SetThreadAttributes(attrs);
  thread.Start(std::bind(VerifyName, name));
  thread.Join();
}

TEST(BaseThread, Restart) {
    BaseThread thread;
    for (int i = 0; i < 10; ++i) {
        thread.Start(DoNothing);
        int tid1 = thread.GetId();
        thread.Join();

        thread.Start(DoNothing);
        int tid2 = thread.GetId();
        EXPECT_NE(tid1, tid2);
        thread.Join();
    }
}

TEST(BaseThread, Reinitialize) {
    BaseThread thread;
    thread.Start(DoNothing);
    thread.Join();
    thread.Start(DoNothing);
    thread.Join();
}

static void IsAliveTestThread(volatile const bool* stop) {
    while (!*stop) {
        ThisThread::Sleep(1);
    }
}

TEST(BaseThread, IsAlive) {
    bool stop = false;
    BaseThread thread(std::bind(IsAliveTestThread, &stop));
    for (int i = 0; i < 1000; ++i) {
        if (!thread.IsAlive()) {
            ThisThread::Sleep(1);
        }
    }
    stop = true;
    thread.Join();
    EXPECT_FALSE(thread.IsAlive());
}

TEST(BaseThread, MoveConstructable) {
    std::atomic<size_t> counter{0};
    std::vector<BaseThread> threads;
    const size_t kNumWorker = 4;

    for (size_t i = 0; i < kNumWorker; ++i) {
        threads.emplace_back([&] () {
            counter++;
            ThisThread::SleepInMs(10);
        });
    }
    for (auto& thread : threads) {
        thread.Join();
    }
    EXPECT_EQ(counter.load(), kNumWorker);
}
}  // namespace
}  // namespace byte
