// Copyright (c) 2018-present, ByteDance Inc. All rights reserved.

#pragma once

#include <pthread.h>
#include <unistd.h>
#include <sys/syscall.h>

#include <string>

#include "byte/include/macros.h"

namespace byte {

// A wrapper over pthread that provides different static util methods.
class ThisThread {
public:
    static void SleepInSeconds(int seconds) {
        sleep(seconds);
    }
    static void SleepInMs(int ms) {
        usleep(ms * 1000);
    }
    static void SleepInUs(int us) {
        usleep(us);
    }
    static void Sleep(int seconds) {
        SleepInSeconds(seconds);
    }
    static void Yield() {
        ::sched_yield();
    }

    // See https://stackoverflow.com/questions/6372102/what-is-the-difference-between-pthread-self-and-gettid-which-one-should-i-u
    // for difference bewtween `gettid` and `pthread_self`.
    static int GetId() {
        thread_local pid_t tid = 0;
        if (tid == 0) {
            tid = ::syscall(SYS_gettid);
        }
        return tid;
    }

    static pthread_t GetThreadId() {
        return ::pthread_self();
    }

    static void SetThreadName(const std::string& name) {
        if (!name.empty()) {
            // Set thread name for easy debugging.
#if __GLIBC__ > 2 || __GLIBC__ == 2 && __GLIBC_MINOR__ >= 12
            pthread_setname_np(pthread_self(), name.c_str());
#else
            prctl(PR_SET_NAME, name.c_str(), 0, 0, 0);
#endif
        }
    }

private:
    DISALLOW_COPY_AND_ASSIGN(ThisThread);
};

}  // namespace byte
