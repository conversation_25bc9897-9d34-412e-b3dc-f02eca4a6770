// Copyright (c) 2019-present, ByteDance Inc. All rights reserved.

#pragma once

#include <linux/aio_abi.h>

#include <atomic>
#include <functional>
#include <list>
#include <string>
#include <vector>
#include <memory>

#include "libev/ev.h"

#include "byte/base/closure.h"
#include "byte/concurrent/count_down_latch.h"
#include "byte/concurrent/spinlock.h"
#include "byte/container/intrusive_list.h"
#include "byte/container/lockfree_queue.h"
#include "byte/include/macros.h"
#include "byte/memory/object_cache.h"
#include "byte/util/bind_callback.h"


namespace byte {

class Timer;
class AsyncThreadPool;

class AsyncThread {
public:
    AsyncThread();
    ~AsyncThread();

    bool Init(AsyncThreadPool* tp, int index);
    bool Init(const std::string& name, bool polling = false);

    bool SetAIO(int nr_queue, int nr_batch, int nr_running);
    bool Start();
    bool Stop();

    /*
     * RegisterIdle is to register callback which would be invoked when your process
     * is idle (or only lower-priority watchers are pending), the idle watchers are
     * being called once per event loop iteration - until stopped
     * Note: it's caller's responsibilty to delete closure. The closure should be a
     * "permanent" closure.
     */
    bool RegisterIdle(Closure<void>* callback);
    void Invoke(Closure<void>* callback);
    void InvokeLater(uint64_t delayUs, Closure<void>* callback);

    /*
     * BindCallbackR2 is the wrapper of Closure<void>. The return value of the callback
     * doesn't make sense since the callback always invoked in an asynchronous context.
     * So BindCallbackR2 is the recommended way to call Read/Write of AIO family.
     */
    int AIOPRead(int fd, void* data, uint64_t length, uint64_t offset,
                 Closure<void, int, int>* done);
    int AIOPRead(int fd, void* data, uint64_t length, uint64_t offset,
                 BindCallbackR2<int, int>* done);
    int AIOPWrite(int fd, void* data, uint64_t length, uint64_t offset,
                  Closure<void, int, int>* done);
    int AIOPWrite(int fd, const void* data, uint64_t length, uint64_t offset,
                  BindCallbackR2<int, int>* done);

    /*
     * Linux libaio provided the solution that just to submit fsync/fdsync
     * request to a scheduled workqueue, for delayed execution.
     * https://patchwork.kernel.org/project/linux-fsdevel/patch/<EMAIL>/
     */
    int AIOSync(int fd, BindCallbackR2<int, int>* done);
    int AIODataSync(int fd, BindCallbackR2<int, int>* done);
    Timer* CreateTimer();

    // These are ev helper functions.
    void OnStart();
    void OnWakeup();
    void OnInvoke();
    void OnIdle();
    void OnAIOCallback(int revents);
    void OnAsyncLite(Closure<void>* done);
    struct ev_loop* EvLoop() { return loop_; }

    int GetThreadIndex() const;

private:
    struct AsyncTask {
        intrusive_list_node link;
        Closure<void>*      callback = nullptr;
    };

    struct AsyncIOTask {
        intrusive_list_node link;
        struct iocb         cb;
        // 'res' and 'res2' returned from 'struct event'.
        // 'res' means number of bytes read/write.
        // 'res2' means the errorcode returned from read/write
        Closure<void, int /*res*/, int /*res2*/>* callback = nullptr;
        BindCallbackR2<int /*res*/, int /*res2*/>* bind_callback = nullptr;

        AsyncIOTask() { memset(&cb, 0, sizeof(struct iocb)); }
    };

    struct AsyncIOContext {
        int           event_fd;
        aio_context_t aio_ctx;   // defined in <linux/aio_abi.h>
        ev_io*        io_watcher;
        int           max_queue_size;   // max number of io requests can be queued
        int           max_batch_size;   // max number of io requests can be submitted once a time
        int           max_submit_size;  // max number of io requests submitted
        int           queue_size;   // number of requests waiting to be submitted
        int           submit_size;  // number of requests already submitted but not returned
        intrusive_list<AsyncIOTask> pending_list;  // requests waiting to be submitted

        AsyncIOContext();
        ~AsyncIOContext();
    };

    enum class ThreadState {
        SPAWN = 0,
        INIT,
        RUNNING,
        STOPPING,  // STOPPING is the transition state, which waiting for flush() to be done
        STOPPED
    };

private:
    int runAsyncIOTask(AsyncIOTask* task);
    int aioSubmit();
    void invoke(Closure<void>* callback, bool force);
    void flush();
    void flushDone(byte::CountDownLatch* latch);

private:
    pthread_t            tid_;
    int                  index_;
    bool                 stopped_;
    std::string          name_;
    struct ev_loop*      loop_;
    ev_async*            async_watcher_;
    ev_idle*             idle_watcher_;

    AsyncThreadPool*     thread_pool_;
    AsyncIOContext*      aio_;

    // lock_ protects pending_tasks_ and task_pool_
    SpinLock                        lock_;
    std::atomic<int64_t>            pending_tasks_count_;
    intrusive_list<AsyncTask>       pending_tasks_;

    ObjectCache<AsyncTask>          task_pool_;
    ObjectCache<AsyncIOTask>        io_task_pool_;  // Does not need any lock

    std::vector<Closure<void>*>     idle_callbacks_;
    std::atomic<ThreadState>        state_;

    DISALLOW_COPY_AND_ASSIGN(AsyncThread);
};

struct DelayTask {
    std::unique_ptr<Timer> timer_;
    BindCallbackM0<DelayTask> on_timer_;
    Closure<void>* user_callback_;

    explicit DelayTask(AsyncThread* thread);
    void Init(uint64_t delayUs, Closure<void>* cb);
    void Start();
    void Stop();
    void onTimer();
    ~DelayTask();
};

class AsyncThreadFactory {
public:
    virtual ~AsyncThreadFactory() {}
    virtual AsyncThread* CreateThread() {
        return new AsyncThread();
    }
};

struct AsyncThreadPoolOptions {
    std::string name_;
    int         thread_num_;

    bool        enable_aio_;
    int         aio_max_queue_size_;  // queue size for each AsyncThread
    int         aio_max_batch_size_;  // batch size for each AsyncThread
    int         aio_max_submit_size_;  // concurrent size pass to io_setup

    bool        enable_polling_;
    AsyncThreadFactory* thread_factory_;

    AsyncThreadPoolOptions();
};

class AsyncThreadPool {
public:
    AsyncThreadPool();
    ~AsyncThreadPool();

    bool Init(const AsyncThreadPoolOptions& options);
    bool Start();
    bool Stop();

    AsyncThread* BaseThread();
    AsyncThread* KthThread(int kth);
    int ThreadNum() const;
    std::string Name() const;

    // Run 'task' in this thread pool.
    // Pick an random AsyncThread and invoke task on it.
    // If someone want to run task at specified thread, please use
    // Invoke*() functions on a thread get by KthThread(int).
    int PushTask(Closure<void>* task, uint64_t delayUs = 0);

    // Run 'task' in this thread pool.
    // Pick an specified AsyncThread and invoke task on it.
    int PushTask(uint32_t dispatch_key, Closure<void>* task, uint64_t delayUs = 0);

private:
    enum class ThreadPoolState {
        SPAWN = 0,
        INIT,
        RUNNING,
        STOPPED
    };

private:
    AsyncThreadPoolOptions                      options_;
    std::vector<std::unique_ptr<AsyncThread>>   threads_;
    std::atomic<ThreadPoolState>                state_;
    std::atomic<uint64_t>                       candiate_index_;

    DISALLOW_COPY_AND_ASSIGN(AsyncThreadPool);
};

/**
 * Get current async thread in a async thread context. Usually used to invoke
 * something to current thread.
 *
 * Will return nullptr if this function is called in a common thread,
 */
AsyncThread* GetCurrentThread();

void SetCurrentThread(AsyncThread* thread);

struct ev_loop* GetCurrentLoop();

/**
 * Invoke 'callback' in current async thread.
 * This 'callback' will be run as soon as possible.
 */
void InvokeInCurrentThread(Closure<void>* callback);

/**
 * Invoke 'callback' in current async thread after 'delayUs' microseconds.
 */
void InvokeLaterInCurrentThread(uint64_t delayUs, Closure<void>* callback);

/**
 * Return thread index when in a async thread context,
 * return -1 in common thread
 */
int GetCurrentThreadIndex();

}  // namespace byte
