// Copyright (c) 2020-present, ByteDance Inc. All rights reserved.

#include "gtest/gtest.h"
#include "byte/thread/dynamic_thread_pool.h"
#include "byte/thread/this_thread.h"

namespace {
class Foo {
public:
    Foo() : value_(0) {}
    void Add(int k) {
        byte::AtomicAdd(&value_, k);
        byte::ThisThread::SleepInUs(1000);
    }
    int Sum() const {
        return byte::AtomicGet(&value_);
    }
private:
    int value_;
};
}  // namespace

namespace byte {

TEST(DynamicThreadPoolTest, SumTest) {
    for (int num_threads : {-1, 0, 10, 256, 1000}) {
        DynamicThreadPool threadpool(num_threads, "testing");

        Foo foo;
        int count = 20;
        int sum = 0;
        for (int k = 0; k < count; ++k) {
            Closure<void>* task = NewClosure(&foo, &Foo::Add, k);
            threadpool.AddTask(task);
            sum += k;
        }

        threadpool.WaitForIdle();
        EXPECT_EQ(sum, foo.Sum()) << "failed with max " << num_threads << " threads in the pool.";
    }
}

}  // namespace byte
