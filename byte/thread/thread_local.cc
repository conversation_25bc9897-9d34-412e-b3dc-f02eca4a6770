// Copyright (c) 2018-present, ByteDance Inc. All rights reserved.
// Modified from facebook folly.

/*
 * Copyright 2015 Facebook, Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#include <utility>
#include "byte/thread/thread_local.h"

namespace byte {
namespace threadlocal_detail {

// IMPLEMENTATION OF StaticMeta
__thread ThreadEntry StaticMeta::threadEntry_;
StaticMeta* StaticMeta::inst_ = NULL;

StaticMeta* StaticMeta::instance() {
  // Leak it on exit, there's only one per process and we don't have to
  // worry about synchronization with exiting threads.
  static bool constructed = (inst_ = new StaticMeta());
  (void)constructed;  // suppress unused warning
  return inst_;
}

void StaticMeta::push_back(ThreadEntry* t) {
  __list_add(&t->node, &head_, head_.next);
}

void StaticMeta::erase(ThreadEntry* t) {
  __list_del(t->node.prev, t->node.next);
}

StaticMeta::StaticMeta() : nextId_(1) {
  INIT_LIST_HEAD(&head_);
  int ret = pthread_key_create(&pthreadKey_, &onThreadExit);
  BYTE_ASSERT(ret == 0);
}

StaticMeta::~StaticMeta() {
  BYTE_ASSERT(false);  // StaticMeta lives forever!
}

void StaticMeta::onThreadExit(void* ptr) {
  StaticMeta* meta = instance();
  ThreadEntry* threadEntry = getThreadEntry();

  BYTE_ASSERT_DEBUG(ptr == meta);
  BYTE_ASSERT_DEBUG(threadEntry->elementsCapacity > 0);
  {
    Mutex::Locker g(&meta->lock_);
    meta->erase(threadEntry);
    // No need to hold the lock any longer; the ThreadEntry is private to this
    // thread now that it's been removed from meta.
  }
  // NOTE: User-provided deleter / object dtor itself may be using ThreadLocal
  // with the same Tag, so dispose() calls below may (re)create some of the
  // elements or even increase elementsCapacity, thus multiple cleanup rounds
  // may be required.
  for (bool shouldRun = true; shouldRun; ) {
    shouldRun = false;
    for (size_t i = 0; i < threadEntry->elementsCapacity; i++) {
      if (threadEntry->elements[i].dispose(TLP_DESTRUCTION_THIS_THREAD)) {
        shouldRun = true;
      }
    }
  }
  free(threadEntry->elements);
  threadEntry->elements = NULL;
  pthread_setspecific(meta->pthreadKey_, NULL);
}

uint32_t StaticMeta::create() {
  uint32_t id;
  StaticMeta* meta = instance();
  Mutex::Locker g(&meta->lock_);
  if (!meta->freeIds_.empty()) {
    id = meta->freeIds_.back();
    meta->freeIds_.pop_back();
  } else {
    id = meta->nextId_++;
  }
  return id;
}

void StaticMeta::destroy(uint32_t id) {
  StaticMeta* meta = instance();
  // Elements in other threads that use this id.
  std::vector<ElementWrapper> elements;
  {
    Mutex::Locker g(&meta->lock_);
    ThreadEntry* e;
    for (struct list_head* node = meta->head_.next; node != &meta->head_; node = node->next) {
      e = container_of(node, threadlocal_detail::ThreadEntry, node);
      if (id < e->elementsCapacity && e->elements[id].ptr) {
        elements.push_back(e->elements[id]);

        /*
         * Writing another thread's ThreadEntry from here is fine;
         * the only other potential reader is the owning thread --
         * from onThreadExit (which grabs the lock, so is properly
         * synchronized with us) or from get(), which also grabs
         * the lock if it needs to resize the elements vector.
         *
         * We can't conflict with reads for a get(id), because
         * it's illegal to call get on a thread local that's
         * destructing.
         */
        e->elements[id].ptr = NULL;
        e->elements[id].deleter = NULL;
        e->elements[id].ownsDeleter = false;
      }
    }
    meta->freeIds_.push_back(id);
  }
  // Delete elements outside the lock
  for (std::vector<ElementWrapper>::iterator it = elements.begin(); it != elements.end(); ++it) {
    it->dispose(TLP_DESTRUCTION_ALL_THREADS);
  }
}

void StaticMeta::reserve(uint32_t id) {
  StaticMeta* meta = instance();
  ThreadEntry* threadEntry = getThreadEntry();
  size_t prevCapacity = threadEntry->elementsCapacity;
  // Growth factor < 2, see folly/docs/FBVector.md; + 5 to prevent
  // very slow start.
  size_t newCapacity = static_cast<size_t>((id + 5) * 1.7);
  BYTE_ASSERT_DEBUG(newCapacity > prevCapacity);
  ElementWrapper* reallocated = NULL;

  // Need to grow. Note that we can't call realloc, as elements is
  // still linked in meta, so another thread might access invalid memory
  // after realloc succeeds. We'll copy by hand and update our ThreadEntry
  // under the lock.

  // calloc() is simpler than malloc() followed by memset(), and
  // potentially faster when dealing with a lot of memory, as it can get
  // already-zeroed pages from the kernel.
  reallocated = static_cast<ElementWrapper*>(
      calloc(newCapacity, sizeof(ElementWrapper)));
  if (!reallocated) {
    throw std::bad_alloc();
  }

  // Success, update the entry
  {
    Mutex::Locker g(&meta->lock_);

    if (prevCapacity == 0) {
      meta->push_back(threadEntry);
    }

    if (reallocated) {
     /*
      * Note: we need to hold the meta lock when copying data out of
      * the old vector, because some other thread might be
      * destructing a ThreadLocal and writing to the elements vector
      * of this thread.
      */
      memcpy(reallocated, threadEntry->elements,
             sizeof(ElementWrapper) * prevCapacity);
      using std::swap;
      swap(reallocated, threadEntry->elements);
    }
    threadEntry->elementsCapacity = newCapacity;
  }

  free(reallocated);

  if (prevCapacity == 0) {
    pthread_setspecific(meta->pthreadKey_, meta);
  }
}

// IMPLEMENTATION OF ElementWrapper
bool ElementWrapper::dispose(TLPDestructionMode mode) {
  if (ptr == NULL) {
    return false;
  }

  BYTE_ASSERT_DEBUG(deleter != NULL);
  deleter->dispose(ptr, mode);
  cleanup();
  return true;
}

void* ElementWrapper::release() {
  void* retPtr = ptr;

  if (ptr != NULL) {
    cleanup();
  }

  return retPtr;
}

void ElementWrapper::cleanup() {
  if (ownsDeleter) {
    delete deleter;
  }
  ptr = NULL;
  deleter = NULL;
  ownsDeleter = false;
}

}  // namespace threadlocal_detail
}  // namespace byte
