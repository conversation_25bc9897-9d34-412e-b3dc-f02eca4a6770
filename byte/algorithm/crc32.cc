// Copyright (c) 2018-present, ByteDance Inc. All rights reserved.

#include <string>
#include "byte/algorithm/crc32.h"

#ifdef HAVE_PCLMULQDQ
#ifdef HAVE_SSE4_2
#include <immintrin.h>
#endif
#endif

namespace byte {
const uint32_t CRCUtil::kCRC32Table[256] = {
    0x00000000L, 0xF26B8303L, 0xE13B70F7L, 0x1350F3F4L,
    0xC79A971FL, 0x35F1141CL, 0x26A1E7E8L, 0xD4CA64EBL,
    0x8AD958CFL, 0x78B2DBCCL, 0x6BE22838L, 0x9989AB3BL,
    0x4D43CFD0L, 0xBF284CD3L, 0xAC78BF27L, 0x5E133C24L,
    0x105EC76FL, 0xE235446CL, 0xF165B798L, 0x030E349BL,
    0xD7C45070<PERSON>, 0x25AFD373L, 0x36FF2087L, 0xC494A384L,
    0x9A879FA0L, 0x68EC1CA3L, 0x7BBCEF57L, 0x89D76C54L,
    0x5D1D08BFL, 0xAF768BBCL, 0xBC267848L, 0x4E4DFB4BL,
    0x20BD8EDEL, 0xD2D60DDDL, 0xC186FE29L, 0x33ED7D2AL,
    0xE72719C1L, 0x154C9AC2L, 0x061C6936L, 0xF477EA35L,
    0xAA64D611L, 0x580F5512L, 0x4B5FA6E6L, 0xB93425E5L,
    0x6DFE410EL, 0x9F95C20DL, 0x8CC531F9L, 0x7EAEB2FAL,
    0x30E349B1L, 0xC288CAB2L, 0xD1D83946L, 0x23B3BA45L,
    0xF779DEAEL, 0x05125DADL, 0x1642AE59L, 0xE4292D5AL,
    0xBA3A117EL, 0x4851927DL, 0x5B016189L, 0xA96AE28AL,
    0x7DA08661L, 0x8FCB0562L, 0x9C9BF696L, 0x6EF07595L,
    0x417B1DBCL, 0xB3109EBFL, 0xA0406D4BL, 0x522BEE48L,
    0x86E18AA3L, 0x748A09A0L, 0x67DAFA54L, 0x95B17957L,
    0xCBA24573L, 0x39C9C670L, 0x2A993584L, 0xD8F2B687L,
    0x0C38D26CL, 0xFE53516FL, 0xED03A29BL, 0x1F682198L,
    0x5125DAD3L, 0xA34E59D0L, 0xB01EAA24L, 0x42752927L,
    0x96BF4DCCL, 0x64D4CECFL, 0x77843D3BL, 0x85EFBE38L,
    0xDBFC821CL, 0x2997011FL, 0x3AC7F2EBL, 0xC8AC71E8L,
    0x1C661503L, 0xEE0D9600L, 0xFD5D65F4L, 0x0F36E6F7L,
    0x61C69362L, 0x93AD1061L, 0x80FDE395L, 0x72966096L,
    0xA65C047DL, 0x5437877EL, 0x4767748AL, 0xB50CF789L,
    0xEB1FCBADL, 0x197448AEL, 0x0A24BB5AL, 0xF84F3859L,
    0x2C855CB2L, 0xDEEEDFB1L, 0xCDBE2C45L, 0x3FD5AF46L,
    0x7198540DL, 0x83F3D70EL, 0x90A324FAL, 0x62C8A7F9L,
    0xB602C312L, 0x44694011L, 0x5739B3E5L, 0xA55230E6L,
    0xFB410CC2L, 0x092A8FC1L, 0x1A7A7C35L, 0xE811FF36L,
    0x3CDB9BDDL, 0xCEB018DEL, 0xDDE0EB2AL, 0x2F8B6829L,
    0x82F63B78L, 0x709DB87BL, 0x63CD4B8FL, 0x91A6C88CL,
    0x456CAC67L, 0xB7072F64L, 0xA457DC90L, 0x563C5F93L,
    0x082F63B7L, 0xFA44E0B4L, 0xE9141340L, 0x1B7F9043L,
    0xCFB5F4A8L, 0x3DDE77ABL, 0x2E8E845FL, 0xDCE5075CL,
    0x92A8FC17L, 0x60C37F14L, 0x73938CE0L, 0x81F80FE3L,
    0x55326B08L, 0xA759E80BL, 0xB4091BFFL, 0x466298FCL,
    0x1871A4D8L, 0xEA1A27DBL, 0xF94AD42FL, 0x0B21572CL,
    0xDFEB33C7L, 0x2D80B0C4L, 0x3ED04330L, 0xCCBBC033L,
    0xA24BB5A6L, 0x502036A5L, 0x4370C551L, 0xB11B4652L,
    0x65D122B9L, 0x97BAA1BAL, 0x84EA524EL, 0x7681D14DL,
    0x2892ED69L, 0xDAF96E6AL, 0xC9A99D9EL, 0x3BC21E9DL,
    0xEF087A76L, 0x1D63F975L, 0x0E330A81L, 0xFC588982L,
    0xB21572C9L, 0x407EF1CAL, 0x532E023EL, 0xA145813DL,
    0x758FE5D6L, 0x87E466D5L, 0x94B49521L, 0x66DF1622L,
    0x38CC2A06L, 0xCAA7A905L, 0xD9F75AF1L, 0x2B9CD9F2L,
    0xFF56BD19L, 0x0D3D3E1AL, 0x1E6DCDEEL, 0xEC064EEDL,
    0xC38D26C4L, 0x31E6A5C7L, 0x22B65633L, 0xD0DDD530L,
    0x0417B1DBL, 0xF67C32D8L, 0xE52CC12CL, 0x1747422FL,
    0x49547E0BL, 0xBB3FFD08L, 0xA86F0EFCL, 0x5A048DFFL,
    0x8ECEE914L, 0x7CA56A17L, 0x6FF599E3L, 0x9D9E1AE0L,
    0xD3D3E1ABL, 0x21B862A8L, 0x32E8915CL, 0xC083125FL,
    0x144976B4L, 0xE622F5B7L, 0xF5720643L, 0x07198540L,
    0x590AB964L, 0xAB613A67L, 0xB831C993L, 0x4A5A4A90L,
    0x9E902E7BL, 0x6CFBAD78L, 0x7FAB5E8CL, 0x8DC0DD8FL,
    0xE330A81AL, 0x115B2B19L, 0x020BD8EDL, 0xF0605BEEL,
    0x24AA3F05L, 0xD6C1BC06L, 0xC5914FF2L, 0x37FACCF1L,
    0x69E9F0D5L, 0x9B8273D6L, 0x88D28022L, 0x7AB90321L,
    0xAE7367CAL, 0x5C18E4C9L, 0x4F48173DL, 0xBD23943EL,
    0xF36E6F75L, 0x0105EC76L, 0x12551F82L, 0xE03E9C81L,
    0x34F4F86AL, 0xC69F7B69L, 0xD5CF889DL, 0x27A40B9EL,
    0x79B737BAL, 0x8BDCB4B9L, 0x988C474DL, 0x6AE7C44EL,
    0xBE2DA0A5L, 0x4C4623A6L, 0x5F16D052L, 0xAD7D5351L
};

static const uint32_t kCRC32X64NTable[64 * 6] = {
    0x80000000, 0x00800000, 0x00008000, 0x00000080,
    0x82f63b78, 0xfbc3faf9, 0x8b277743, 0x52a0c93f,
    0x6ea2d55c, 0x1c08b7d6, 0xf56e0ef4, 0x34019664,
    0xa66805eb, 0x7a1f6b24, 0xe75d06aa, 0xc94ec098,
    0x18b8ea18, 0x9a9f274a, 0x2a03aaa3, 0xb13145f8,
    0x790606ff, 0xad045557, 0x8542ba6d, 0xde6b9d0b,
    0x9957c0a6, 0x8473058e, 0x2e0af75a, 0x3ae9f81c,
    0x5d27e147, 0x95ec5eb6, 0x9421797f, 0x2f1f4950,
    0x510ac59a, 0xf91bdeea, 0x882b9bfc, 0xbea58b3e,
    0x9c25531d, 0xafeaaeef, 0xbd8c7e90, 0x92157069,
    0x19e65dde, 0x7fb2b8d1, 0x21c7d010, 0x107f00bf,
    0xec1631ed, 0x5cf4f2f8, 0x79ebc348, 0xcbdbaeb0,
    0xb2dea967, 0xb5be2920, 0x200830f7, 0x278403ae,
    0x0e148e82, 0x63c35f01, 0xf208405c, 0x1c941d43,
    0x52377a55, 0x6486f9b5, 0x8780e02c, 0x6d79c1ee,
    0x4f256efc, 0xbe6285cb, 0x5abaef7a, 0x1a20c6da,
    0x80000000, 0xb82be955, 0xb8fdb1e7, 0x18e4a304,
    0x88e56f72, 0x9030a49c, 0x67db2c4a, 0xe744dcb2,
    0x74c360a4, 0x8e9906a7, 0xb469724f, 0x24d84074,
    0x631bb273, 0xee2a2d56, 0x654f84e7, 0x629069c6,
    0xe4172b16, 0xd3cb2137, 0x4f47bf52, 0x35202632,
    0x71892b1b, 0xd27285db, 0x69c2af09, 0xd271158e,
    0x835305c9, 0xeaa500ac, 0x0e36a726, 0x46951631,
    0x196b1eae, 0x584ea70d, 0x15e8653c, 0xb5a65826,
    0x0d65762a, 0xebfcce28, 0xe6aef08f, 0x013ee85b,
    0xafc81338, 0x61c30b85, 0xe31e7f5b, 0x5e3750cd,
    0xb5a50ab7, 0x1977c75d, 0x46bf959e, 0xe78cd046,
    0xf373c3ac, 0x950979dd, 0xe31b4008, 0xc726c863,
    0x5f60970f, 0xf977f383, 0x9e7b1c10, 0xea2110f8,
    0xd46d3063, 0xe2220529, 0xebd59d26, 0xedbe69a1,
    0x3a5275ea, 0x1a2bbe21, 0xd597ad7e, 0xcb8c45a6,
    0x02331c01, 0xe8639712, 0xa957c550, 0xbcfda51c,
    0x80000000, 0x35d73a62, 0x28461564, 0x43eefc9f,
    0xbf455269, 0x5edcdeb9, 0x65059450, 0x4e6f41a4,
    0xe2ea32dc, 0xf5b95b32, 0x695a4c87, 0xf204502c,
    0x9a4f01b6, 0x941a0916, 0x4303cb97, 0x4c3d3d65,
    0xfe7740e6, 0xf0cb95d2, 0xab4c722a, 0xba35e2e1,
    0x99569602, 0x294060e6, 0xd65a1d78, 0x18103ff9,
    0x78b57ca2, 0x97b3a098, 0x30a8edff, 0x3fb4e611,
    0x999dcda1, 0xd9dd2e27, 0x5918f954, 0x8ee73513,
    0xf946610b, 0xa5f4742d, 0x424b8555, 0xd017c143,
    0xc274d1e2, 0x0280b04e, 0xd25bb5e0, 0x552a0918,
    0xf5942afb, 0x55d7a3f8, 0xd1b533ee, 0x9fa8def5,
    0x24c42589, 0x5988dbb3, 0xa7e1cab4, 0xebaee1b4,
    0xa0a51f5f, 0xbd1a9096, 0x1ced05cc, 0x6748e206,
    0x506431b0, 0x6c399e24, 0x30af367f, 0x129ee2f5,
    0x8fd57416, 0x85ee5ed0, 0x97504a07, 0x52e710c7,
    0x4ea3e89b, 0xde8567b5, 0xb5c84194, 0x80526793,
    0x80000000, 0x3c204f8f, 0x538586e3, 0x683988b2,
    0x59726915, 0x30c6966d, 0x5be8b36e, 0x42c5623c,
    0x734d5309, 0x1084fed0, 0x68a5f0f9, 0x1573fe60,
    0xdb1b3315, 0xe0a82539, 0xe5c304f7, 0xce8068d1,
    0xbc1ac763, 0x47514f16, 0xf7f919a7, 0x5005a217,
    0x11b31f9d, 0x496f21b8, 0x0bc2a806, 0xbba39916,
    0x5414bce3, 0x401f0313, 0xe0ee85d5, 0x4fb6f7ac,
    0x54f2f77b, 0x738279cb, 0x6b645133, 0x05a44de1,
    0x7d0722cc, 0x8ca9c7c7, 0x50806311, 0x58ba16a0,
    0xbae70d08, 0xde3a8229, 0x6652a6cc, 0x36b6490b,
    0x238acb2b, 0xe3f9a08b, 0x23a7923f, 0xb0609b9b,
    0xd2074e2b, 0x342f6e39, 0x5bcf2cf2, 0x8096907d,
    0x546dfa6e, 0x75337ed4, 0x9c1de396, 0x91e7afe7,
    0x3f76a96c, 0x00032b5e, 0x1a2a93d0, 0x3b0c8728,
    0xa3aa3946, 0x9b170192, 0xcc33e9f6, 0x4152bdb8,
    0xf054ca48, 0x0369fe6a, 0x6ec7bef8, 0x3fe8b9ff,
    0x80000000, 0xd289cabe, 0xe94ca9bc, 0x6ac5538f,
    0x05b74f3f, 0x9e0b2298, 0x14de47cd, 0x3a9fb07d,
    0xa51e1f42, 0xfe578179, 0x038d778c, 0x5fe3cf44,
    0xb7c64e40, 0x20cbb8e3, 0x4f04f803, 0x56745358,
    0x40000000, 0x6944e55f, 0x74a654de, 0xb79492bf,
    0x802d9ce7, 0x4f05914c, 0x8899189e, 0x9fb9e346,
    0x528f0fa1, 0xfdddfbc4, 0x01c6bbc6, 0x2ff1e7a2,
    0x5be32720, 0x9293e709, 0xa5744779, 0x2b3a29ac,
    0x20000000, 0xb65449d7, 0x3a532a6f, 0xd93c7227,
    0xc2e0f50b, 0x2782c8a6, 0x444c8c4f, 0x4fdcf1a3,
    0xabb1bca8, 0x7eeefde2, 0x00e35de3, 0x17f8f3d1,
    0x2df19390, 0xcbbfc8fc, 0xd04c18c4, 0x159d14d6,
    0x10000000, 0xd9dc1f93, 0x9fdfae4f, 0xee68026b,
    0xe38641fd, 0x13c16453, 0xa0d07d5f, 0xa51843a9,
    0x55d8de54, 0x3f777ef1, 0x82879589, 0x890a4290,
    0x16f8c9c8, 0x65dfe47e, 0x68260c62, 0x0ace8a6b,
    0x80000000, 0x08000000, 0x00800000, 0x00080000,
};

uint32_t CRCUtil::ComputeCRC32_Lookup(uint32_t init_crc, const char* buf, size_t size) {
    uint32_t x = init_crc;
    for (size_t i = 0; i < size; ++i) {
        unsigned char c = buf[i];
        x = kCRC32Table[(x ^ c) & 0xff] ^ (x >> 8);
    }
    return x;
}

uint32_t CRCUtil::ComputeCRC32_Accelerate(uint32_t init_crc, const char* buf, size_t size) {
    uint32_t crc = init_crc;
    const uint64_t* p = (const uint64_t*)buf;
    const uint64_t* end = p + (size >> 3);
    uint64_t crc64 = crc;
    while (p < end) {
#if defined(__x86_64__)
        asm volatile(
            "crc32q %[buf], %[crc]\n\t"
            :[crc]"=r"(crc64)
            :"0"(crc64), [buf]"r"(*p)
        );
#elif __aarch64__
        asm volatile(
            "crc32cx %w[c], %w[c], %x[v]"
            :[c]"=r"(crc64)
            :"0"(crc64), [v]"r"(*p)
        );
#else
        #error "Unknown Architecture"
#endif
        ++p;
    }
    crc = (uint32_t)crc64;
    const char* buf_end = buf + size;
    buf = (const char*)p;
    while (buf < buf_end) {
#if defined(__x86_64__)
        asm volatile(
            "crc32b %[buf], %[crc]\n\t"
            :[crc]"=r"(crc)
            :"0"(crc), [buf]"r"(*buf)
        );
#elif __aarch64__
        asm volatile(
            "crc32cb %w[c], %w[c], %w[v]"
            :[c]"=r"(crc)
            :"0"(crc), [v]"r"(*buf)
        );
#else
        #error "Unknown Architecture"
#endif
        ++buf;
    }
    return crc;
}

typedef uint32_t (*ComputeCRC32_Impl)(uint32_t, const char*, size_t);

ComputeCRC32_Impl CRCUtil::Get_ComputeCRC32_Impl() {
#if defined(__x86_64__)
    static const uint32_t k_SSE4_2 = (1 << 20);
    int level = 1;
    uint64_t a, b, c, d;
    asm ("xchg %%rbx, %1\n\t"
         "cpuid\n\t"
         "xchg %%rbx, %1\n\t"
        :"=a"(a), "=r"(b), "=c"(c), "=d"(d)
        :"0"(level));
    return (c & k_SSE4_2) ? &CRCUtil::ComputeCRC32_Accelerate : &CRCUtil::ComputeCRC32_Lookup;
#elif __aarch64__
    // TODO(xuxunzhi) check if processor support crrc32 instruction
    return &CRCUtil::ComputeCRC32_Accelerate;
#else
    return &CRCUtil::ComputeCRC32_Lookup;
#endif
}

uint32_t CRCUtil::ComputeCRC32(uint32_t init_crc, const char* buf, size_t size) {
    static ComputeCRC32_Impl compute_crc32_impl = Get_ComputeCRC32_Impl();
    return compute_crc32_impl(init_crc, buf, size);
}

uint32_t CRCUtil::ComputeCRC32(uint32_t init_crc, const std::string& buf) {
    return ComputeCRC32(init_crc, buf.data(), buf.size());
}

uint32_t CRCUtil::ComputeCRC32(const char* buf, size_t size) {
    uint32_t crc = ~0U;
    crc = ComputeCRC32(crc, buf, size);
    return ~crc;
}

uint32_t CRCUtil::ComputeCRC32(const std::string& buf) {
    return ComputeCRC32(buf.data(), buf.size());
}

uint32_t CRCUtil::CRC32CombineGen(size_t len) {
    const uint32_t m = 1 << 31;
    uint32_t p = 1 << 31;
    int i = 0;
    if (m <= len) {
        len = (len & (m - 1)) + (len >> 31);
        if (m <= len) {
            len = (len & (m - 1)) + (len >> 31);
        }
    }
    if (len == 0) {
        return p;
    }
    for (; !(len & 63); len >>= 6, i++) {}
    p = kCRC32X64NTable[i * 64 + (len & 63)];
    for (len >>= 6, i++; len; len >>= 6, i++) {
        if (len & 63) {
            p = crc32Mult(kCRC32X64NTable[i * 64 + (len & 63)], p);
        }
    }
    return p;
}

uint32_t CRCUtil::CRC32CombineOp(uint32_t crc1, uint32_t crc2, uint32_t op) {
    return crc32Mult(op, crc1) ^ crc2;
}

#ifdef HAVE_PCLMULQDQ
#ifdef HAVE_SSE4_2
#ifdef __clang__
#pragma clang attribute push (__attribute__((target("pclmul, sse4.2"))), apply_to=function)  // NOLINT
#else
#pragma GCC push_options
#pragma GCC target("pclmul", "sse4.2")
#endif
uint32_t crc32mul_pclmul(uint32_t a, uint32_t b) {
    __m128i va = _mm_set_epi32(0, 0, 0, a);
    __m128i vb = _mm_set_epi32(0, 0, 0, b);
    __m128i v = _mm_clmulepi64_si128(va, vb, 0);
    uint64_t m = _mm_extract_epi64(v, 0);
    uint32_t lo = m >> 31;
    uint32_t hi = (m & ((1U << 31) - 1)) << 1;

    return _mm_crc32_u32(hi, 0) ^ lo;
}

uint32_t crc32ff_pclmul(uint32_t crc, size_t len) {
    const uint32_t m = 1 << 31;
    if (m <= len) {
        len = (len & (m - 1)) + (len >> 31);
        if (m <= len) {
            len = (len & (m - 1)) + (len >> 31);
        }
    }
    if (len == 0) {
        return crc;
    }

    __m128i v = _mm_set_epi32(0, 0, 0, crc);
    __m128i k;
    __m128i p;
    int i = 0;
    int d = 0;

    for (; len; len >>= 6, i++) {
        if (len & 63) {
            int64_t c0 = kCRC32X64NTable[i * 64 + (len & 63)];
            int64_t c1 = 0;

            if (d < 2) {
                k = _mm_set_epi64x(0, c0 << 1);
                v = _mm_clmulepi64_si128(v, k, 0);
                d++;
                continue;
            }

            c1 = _mm_crc32_u64(c0, 0);
            k = _mm_set_epi64x(c1 << 1, c0 << 1);
            v = _mm_slli_si128(v, 4);

            p = _mm_clmulepi64_si128(v, k, 0x10);  // u0 * c * x65
            v = _mm_clmulepi64_si128(v, k, 0x01);  // u1 * c * x1

            v = _mm_xor_si128(v, p);
        }
    }

    // reduce to 32-bit
    if (d < 2) {
        uint64_t u0 = _mm_extract_epi64(v, 0);
        uint32_t lo = u0 >> 32;
        uint32_t hi = u0 & ~(uint32_t)0;

        return _mm_crc32_u32(hi, 0) ^ lo;
    } else {
        uint64_t u0 = _mm_extract_epi64(v, 0);
        uint64_t u1 = _mm_extract_epi64(v, 1);

        return _mm_crc32_u64(0, u0) ^ u1;
    }
}
#ifdef __clang__
#pragma clang attribute pop
#else
#pragma GCC pop_options
#endif
#endif
#endif

uint32_t CRCUtil::CRC32Combine(uint32_t crc1, uint32_t crc2, size_t size2) {
#ifdef HAVE_PCLMULQDQ
#ifdef HAVE_SSE4_2
    if (__builtin_cpu_supports("pclmul") &&
        __builtin_cpu_supports("sse4.2")) {
        return crc32ff_pclmul(crc1, size2) ^ crc2;
    }
#endif
#endif
    return CRC32CombineOp(crc1, crc2, CRC32CombineGen(size2));
}

uint32_t CRCUtil::crc32Mult(uint32_t a, uint32_t b) {
#ifdef HAVE_PCLMULQDQ
#ifdef HAVE_SSE4_2
    if (__builtin_cpu_supports("pclmul") &&
        __builtin_cpu_supports("sse4.2")) {
        return crc32mul_pclmul(a, b);
    }
#endif
#endif
    uint32_t m = 1 << 31;
    uint32_t p = 0;
    for (;;) {
        if (a & m) {
            p ^= b;
            if ((a & (m - 1)) == 0)
                break;
        }
        m >>= 1;
        b = b & 1 ? (b >> 1) ^ kCRC32PolyReflect : b >> 1;
    }
    return p;
}

}  // namespace byte
