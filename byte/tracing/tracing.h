// Copyright (c) 2020-present, ByteDance Inc. All rights reserved.

#pragma once

#include <stdarg.h>
#include <stdlib.h>
#include <map>
#include <memory>
#include <string>
#include <utility>
#include "byte/base/atomic.h"
#include "byte/byte_log/byte_log_impl.h"
#include "byte/concurrent/lite_lock.h"
#include "byte/include/byte_log.h"
#include "byte/include/macros.h"
#include "byte/string/concat.h"
#include "byte/string/format.h"
#include "byte/system/process_info.h"
#include "byte/system/timestamp.h"
#include "byte/thread/this_thread.h"
#include "byte/thread/thread_local.h"
#include "byte/tracing/tracing_pub_funcs.h"
#include "byte/tracing/tracing_collector.h"
#include "byte/tracing/tracing_dictionary.h"
#include "byte/tracing/tracing_config.h"
#include "byte/concurrent/spinlock.h"

static inline uint64_t GetTsc() {
#if defined(__x86_64__)
    uint64_t a, d;
    __asm__ volatile("rdtsc" : "=a"(a), "=d"(d));
    return (d << 32) | a;
#else
    return byte::GetCurrentTimeInNs();
#endif
}
#define TRACE_NS_TO_US (1000)

static inline double CalNanoToTscRatio() {
    #if defined(__x86_64__)
    uint64_t begin_ns = 0;
    uint64_t begin_tsc = 0;
    uint64_t cur_ns = 0;
    uint64_t cur_tsc = 0;
    double   tsc_to_ns_rate = -1.0f;
    static const uint64_t tsc_2ns_rate_gapns = 10000000;

    begin_ns = byte::GetCurrentTimeInNs();
    begin_tsc = GetTsc();

    while (1) {
        cur_ns = byte::GetCurrentTimeInNs();
        cur_tsc = GetTsc();
        if (cur_ns - begin_ns > tsc_2ns_rate_gapns) {
            tsc_to_ns_rate = static_cast<double>(cur_ns - begin_ns)/
                             static_cast<double>(cur_tsc-begin_tsc);
            break;
        }

        sched_yield();
    }

    return tsc_to_ns_rate;

    #else

    return 1.0;

    #endif
}

_ALIGNAS(64) static  double g_tsc_to_ns_rate = CalNanoToTscRatio();

static inline double GetNanoToTscRatio(bool force_cal) {
    double tsc_to_ns_rate = g_tsc_to_ns_rate;

    if (tsc_to_ns_rate > 0 && !force_cal) {
        return tsc_to_ns_rate;
    }

    return CalNanoToTscRatio();
}

static inline uint64_t GetNanoFromTsc(uint64_t tsc) {
    return tsc*GetNanoToTscRatio(false);
}

#define k_max_tracing_msg_size 4000U
#define k_extra_tracing_msg_size 32
#define k_max_tracing_name_size 100U
#define k_max_thread_cnt 256

#define TRACING_APPEND_STR_INTERNAL(span, str, len, c) \
    do { \
        if (UNLIKELY(len == 0)) { \
            break; \
        } \
        byte::TraceSpan* s = reinterpret_cast<byte::TraceSpan*>(span); \
        if (UNLIKELY(s->msg_full_)) { \
            break; \
        } \
        const uint32_t msg_size = s->message_size_; \
        s->message_size_ += (len + ((c == '\0') ? 0 : 2)); \
        if (LIKELY(s->message_size_ < k_max_tracing_msg_size)) { \
            static const char temp = c; \
            if (temp != '\0') { \
                s->span_message_[msg_size] = c; \
                memcpy(s->span_message_ + msg_size + 1, str, len); \
                s->span_message_[s->message_size_ - 1] = '\0'; \
            } else { \
                memcpy(s->span_message_ + msg_size, str, len); \
            } \
        } else { \
            s->msg_full_ = true; \
            s->message_size_ = msg_size; \
        } \
    } while (false)

#define TRACING_APPEND_STR_INTERNAL_EXTRA(span, str, len, c) \
    do { \
        if (len == 0) { \
            break; \
        } \
        byte::TraceSpan* s = reinterpret_cast<byte::TraceSpan*>(span); \
        const uint32_t msg_size = s->message_size_; \
        s->message_size_ += (len + ((c == '\0') ? 0 : 2)); \
        if (LIKELY(s->message_size_ < (k_max_tracing_msg_size + \
                k_extra_tracing_msg_size))) { \
            static const char temp = c; \
            if (temp != '\0') { \
                s->span_message_[msg_size] = c; \
                memcpy(s->span_message_ + msg_size + 1, str, len); \
                s->span_message_[s->message_size_ - 1] = '\0'; \
            } else { \
                memcpy(s->span_message_ + msg_size, str, len); \
            } \
        } else { \
            s->message_size_ = msg_size; \
        } \
    } while (false)

#define TRACING_APPEND_STR_INTERNAL_NO_STREND(span, str, len, c) \
    do { \
        if (len == 0) { \
            break; \
        } \
        byte::TraceSpan* s = reinterpret_cast<byte::TraceSpan*>(span); \
        if (UNLIKELY(s->msg_full_)) { \
            break; \
        } \
        const uint32_t msg_size = s->message_size_; \
        s->message_size_ += (len + ((c == '\0') ? 0 : 1)); \
        if (LIKELY(s->message_size_ < k_max_tracing_msg_size)) { \
            static const char temp = c; \
            if (temp != '\0') { \
                s->span_message_[msg_size] = c; \
                memcpy(s->span_message_ + msg_size + 1, str, len); \
            } else { \
                memcpy(s->span_message_ + msg_size, str, len); \
            } \
        } else { \
            s->msg_full_ = true; \
            s->message_size_ = msg_size; \
        } \
    } while (false)

#define TRACING_APPEND_DIC_ID(span, dic_id) \
    do { \
        byte::TraceSpan* s = reinterpret_cast<byte::TraceSpan*>(span); \
        const uint32_t msg_size = s->message_size_; \
        const ushort id = dic_id; \
        if (UNLIKELY(s->msg_full_)) { \
            break; \
        } \
        s->message_size_ += 2; \
        if (LIKELY(s->message_size_ < k_max_tracing_msg_size)) { \
                memcpy(s->span_message_ + msg_size, &id, 2); \
        } else { \
            s->msg_full_ = true; \
            s->message_size_ = msg_size; \
        } \
    } while (false)

#define TRACING_APPEND_STREND(span) \
    do { \
        byte::TraceSpan* s = reinterpret_cast<byte::TraceSpan*>(span); \
        if (UNLIKELY(s->msg_full_)) { \
            break; \
        } \
        const uint32_t msg_size = s->message_size_; \
        s->message_size_ += 1; \
        if (LIKELY(s->message_size_ < k_max_tracing_msg_size)) { \
                s->span_message_[msg_size] = '\0'; \
        } else { \
            s->msg_full_ = true; \
            s->message_size_ = msg_size; \
        } \
    } while (false)

#define TRACING_APPEND_STR_RAW(span, str, len) \
    TRACING_APPEND_STR_INTERNAL(span, str, len, '\0')

#define TRACING_APPEND_STR(span, str, len, c) \
    TRACING_APPEND_STR_INTERNAL(span, str, len, c)

#define TRACING_APPEND_LATENCY_INTERNAL(span, latency, current_ns, penalty, cmp) \
    do { \
        const uint32_t lat = (uint32_t)((latency < cmp) ? cmp : latency); \
        byte::TraceSpan* s = reinterpret_cast<byte::TraceSpan*>(span); \
        if (UNLIKELY(s->msg_full_)) { \
            break; \
        } \
        const uint32_t msg_size = s->message_size_; \
        s->message_size_ += (sizeof(lat) + sizeof(current_ns) + 2); \
        if (s->message_size_ < k_max_tracing_msg_size) { \
            s->span_message_[msg_size] = '#'; \
            s->span_message_[msg_size + 1] = '#'; \
            memcpy(s->span_message_ + msg_size + 2, &lat, sizeof(lat)); \
            int64_t tp_ns = current_ns;\
            memcpy(s->span_message_ + msg_size + 2 + sizeof(lat), &tp_ns, sizeof(tp_ns)); \
        } else { \
            s->msg_full_ = true; \
            s->message_size_ = msg_size; \
        } \
    } while (false)

#define TRACING_APPEND_LATENCY(span, latency, current_ns, penalty) \
    TRACING_APPEND_LATENCY_INTERNAL(span, latency, current_ns, penalty, 0)

#define TRACING_APPEND_NAME(span, name, size) \
    do {} while (false);

// API macro
#define DECLARE_TRACE(name) \
    namespace byte { _ALIGNAS(64) extern ThreadLocalPtr<TraceSpan> TRACE_##name; \
        extern int32_t TRACE_ID_##name; \
    } \
    using byte::TRACE_##name; \
    using byte::TRACE_ID_##name

// API macro
#define DEFINE_TRACE(name) \
    namespace byte { _ALIGNAS(64) ThreadLocalPtr<TraceSpan> TRACE_##name; \
        int32_t TRACE_ID_##name = byte::TracingDictionary::GetInstance() \
            ->RegisterDicItem(#name); \
    } \
    using byte::TRACE_##name; \
    using byte::TRACE_ID_##name

#define STAT_REC_LOG_CYCLE 100000L

namespace byte {
class TracingStatInfo {
public:
    TracingStatInfo():active_counter(0) {}
    uint64_t active_counter;
};
}

#define UPDATE_TLSSTAT(local_ptr) \
do { \
    if (local_ptr.Get() == nullptr) { \
        local_ptr.Reset(new byte::TracingStatInfo()); \
    } \
} while (false)

#define STAT_ACTIVE_COUNT_TLS(local_ptr, loc_name) \
do { \
    UPDATE_TLSSTAT(local_ptr); \
    byte::TracingStatInfo* stat_info = local_ptr.Get(); \
    uint64_t count = ++stat_info->active_counter; \
    if (UNLIKELY(count%STAT_REC_LOG_CYCLE == 0)) { \
        LOG(INFO) << "TRACING active_counter(loc:" << loc_name \
           << ",core:" << byte::GetCpuCoreId() << "):" << count; \
    } \
} while (false)

// API macro
#define DECLARE_TRACEPOINT(name) \
    namespace byte { _ALIGNAS(64) extern TracePoint TRACEPOINT_##name; \
        extern int32_t TRACEPOINT_ID_##name; \
    } \
    using byte::TRACEPOINT_##name; \
    using byte::TRACEPOINT_ID_##name

// API macro
#define DEFINE_TRACEPOINT(name) \
    namespace byte {_ALIGNAS(64) \
            TracePoint TRACEPOINT_##name(BaseNameOf(__FILE__), __LINE__); \
            int32_t TRACEPOINT_ID_##name = byte::TracingDictionary::GetInstance() \
                ->RegisterDicItem(#name); \
    } \
    using byte::TRACEPOINT_##name; \
    using byte::TRACEPOINT_ID_##name

// API macro
#define DECLARE_TRACEPOINT_PAIR(name) \
    namespace byte {_ALIGNAS(64) extern TracePoint TRACEPOINT_##name##_START; \
        extern int32_t TRACEPOINT_ID_##name##_START; \
    } \
    using byte::TRACEPOINT_##name##_START; \
    using byte::TRACEPOINT_ID_##name##_START; \
    namespace byte {_ALIGNAS(64) extern TracePoint TRACEPOINT_##name##_END; \
        extern int32_t TRACEPOINT_ID_##name##_END; \
    } \
    using byte::TRACEPOINT_##name##_END; \
    using byte::TRACEPOINT_ID_##name##_END

// API macro
#define REGISTER_DIC_ITEM(name) \
       byte::TracingDictionary::GetInstance()->RegisterDicItem(#name);

#define DEFINE_TRACEPOINT_PAIR(name) \
    namespace byte { _ALIGNAS(64) \
                    TracePoint TRACEPOINT_##name##_START(BaseNameOf(__FILE__), __LINE__); \
                    int32_t TRACEPOINT_ID_##name##_START = REGISTER_DIC_ITEM(name ## _START); \
            } \
    using byte::TRACEPOINT_##name##_START; \
    using byte::TRACEPOINT_ID_##name##_START; \
    namespace byte { _ALIGNAS(64) \
                    TracePoint TRACEPOINT_##name##_END(BaseNameOf(__FILE__), __LINE__); \
                    int32_t TRACEPOINT_ID_##name##_END = REGISTER_DIC_ITEM(name ## _END); \
            } \
    using byte::TRACEPOINT_##name##_END; \
    using byte::TRACEPOINT_ID_##name##_END;

#define DO_TRACE_RECORD_INTERNAL(span, CODE) \
    do { \
        if ((span)->active_) { \
            if (UNLIKELY(0 == (span)->start_time_)) { \
                (span)->start_time_ = byte::GetCurrentTimeInNs(); \
            } \
            int64_t now = (span)->start_time_; \
            const int64_t interval = now - (span)->parent_trace_point_timestamp_; \
            do { \
                CODE; \
            } while (false); \
            (span)->parent_trace_point_timestamp_ = now; \
            (span)->first_tracing_ = false; \
        } \
    } while (false)

#define DO_TRACE_RECORD_WITH_TRACE_POINT_DIC(span, trace_point, now) \
do { \
    DO_TRACE_RECORD_INTERNAL(span, \
        if (UNLIKELY(span->msg_full_)) { \
            break; \
        } \
        if (UNLIKELY((span->message_size_ + 20) >= k_max_tracing_msg_size)) { \
            span->msg_full_ = true; \
            break; \
        } \
        if (!(span)->first_tracing_) { \
            TRACING_APPEND_STR_RAW(span, ",", 1); \
        } \
        TRACING_APPEND_STR_INTERNAL_NO_STREND(span, "||", 2, '\0'); \
        TRACING_APPEND_DIC_ID(span, TRACEPOINT_ID_##trace_point); \
        TRACING_APPEND_LATENCY(span, interval, now, span->penalty_)); \
} while (false)

#define DO_TRACE_RECORD_STRING_SPAN(span, string_to_rec) \
do { \
    if (UNLIKELY(nullptr == span)) { \
        break; \
    } \
    if (span->active_) { \
        DO_TRACE_RECORD_INTERNAL(span, \
        if (UNLIKELY(span->msg_full_)) { \
            break; \
        } \
        if (UNLIKELY((span->message_size_ + strlen(string_to_rec) + 20) >= \
                    k_max_tracing_msg_size)) { \
            span->msg_full_ = true; \
            break; \
        } \
        if (!(span)->first_tracing_) { \
            TRACING_APPEND_STR_RAW(span, ",", 1); \
        } \
        TRACING_APPEND_STR_INTERNAL(span, string_to_rec, strlen(string_to_rec), '$'); \
        TRACING_APPEND_LATENCY(span, interval, byte::GetCurrentTimeInNs(), span->penalty_);); \
    } \
} while (false)

#define DO_TRACE_RECORD_STRING(name, string_to_rec) \
do { \
    UPDATE_SPAN(name); \
    byte::TraceSpan* span = TRACE_##name.Get(); \
    DO_TRACE_RECORD_STRING_SPAN(span, string_to_rec); \
} while (false)

#define DO_TRACE_RECORD_WITH_TRACE_POINT(span, trace_point, parent_trace, parent_trace_size) \
    DO_TRACE_RECORD_INTERNAL(span, \
        static const uint32_t len = strlen(#trace_point); \
        if (UNLIKELY(span->msg_full_)) { \
            break; \
        } \
        if (UNLIKELY((span->message_size_ + 20) >= k_max_tracing_msg_size)) { \
            span->msg_full_ = true; \
            break; \
        } \
        if (!(span)->first_tracing_) { \
            TRACING_APPEND_STR_RAW(span, ",", 1); \
        } \
        TRACING_APPEND_STR(span, #trace_point, len, '|'); \
        TRACING_APPEND_STR(span, parent_trace, parent_trace_size, '@'); \
        TRACING_APPEND_LATENCY(span, interval, byte::GetCurrentTimeInNs(), span->penalty_))

#define DO_TRACE_RECORD(span) \
do { \
    DO_TRACE_RECORD_INTERNAL(span, \
        static const char* k_file = byte::BaseNameOf(__FILE__); \
        static byte::TracePoint trace_point(k_file, __LINE__); \
        if (UNLIKELY(span->msg_full_)) { \
            break; \
        } \
        if (UNLIKELY((span->message_size_ + 30) >= k_max_tracing_msg_size)) { \
            span->msg_full_ = true; \
            break; \
        } \
        if (!(span)->first_tracing_) { \
            TRACING_APPEND_STR_RAW(span, ",", 1); \
        } \
        static const std::string& k_trace_point = \
            std::string(k_file) + ":" + std::to_string(__LINE__); \
        static ushort tp_dic_id = 0; \
        static byte::SpinLock dic_reg_lock; \
        if (UNLIKELY(0 == tp_dic_id)) { \
            dic_reg_lock.Lock(); \
            if (UNLIKELY(0 == tp_dic_id)) { \
                tp_dic_id = byte::TracingDictionary::GetInstance() \
                    ->RegisterDicItem(k_trace_point.c_str()); \
            } \
            dic_reg_lock.Unlock(); \
        } \
        TRACING_APPEND_STR_INTERNAL_NO_STREND(span, "||", 2, '\0'); \
        TRACING_APPEND_STR_RAW(span, &tp_dic_id, 2); \
        TRACING_APPEND_LATENCY(span, interval, span->start_time_, span->penalty_)); \
} while (false)


#define UPDATE_SPAN(name) \
    do { \
        if (TRACE_##name.Get() == nullptr) { \
            TRACE_##name.Reset(new byte::TraceSpan()); \
        } \
    } while (false)

#define INIT_SPAN_IF_ACTIVE(name) \
    do { \
        if ((TRACE_##name.Get() != nullptr) && \
                TRACE_##name.Get()->active_) { \
            TRACE_##name.Get()->Init(); \
        } \
    } while (false)

#define TRACING_RECORD_ABORT_FROM(name, match_key, tar_ip, lat_us, errcode) \
    do { \
        UPDATE_SPAN(name); \
        byte::TraceSpan* span = TRACE_##name.Get(); \
        if (span->active_) { \
            DO_TRACE_RECORD_INTERNAL(span, \
                char abort_info[256] = {0}; \
                snprintf(abort_info, sizeof(abort_info), \
                    "ABORTED FROM_%s, %s:%d, ADDR:%s, ERR_CODE:%d, LAT:%u us", \
                    match_key, byte::BaseNameOf(__FILE__), __LINE__, \
                    tar_ip, errcode, lat_us); \
                if (UNLIKELY(span->msg_full_)) { \
                    break; \
                } \
                if (UNLIKELY((span->message_size_ + strlen(abort_info) + \
                    strlen(tar_ip) + strlen(match_key) + 40) >= k_max_tracing_msg_size)) { \
                    span->msg_full_ = true; \
                    break; \
                } \
                DO_TRACE_RECORD_STRING(name, abort_info); \
                TRACING_APPEND_STR_RAW(span, ",", 1); \
                snprintf(abort_info, sizeof(abort_info), \
                    "%s-%s", tar_ip, match_key); \
                TRACING_APPEND_STR_RAW(span, "@", 1); \
                TRACING_APPEND_STR_INTERNAL(span, abort_info, strlen(abort_info), '@'); \
                TRACING_APPEND_LATENCY(span, 0, now, 0); \
                (void)interval;); \
        } \
    } while (false)

#define TRACING_RECORD_ABORT_TO(name, match_key, info, lat_us, errcode) \
    do { \
        UPDATE_SPAN(name); \
        byte::TraceSpan* span = TRACE_##name.Get(); \
        if (span->active_) { \
            DO_TRACE_RECORD_INTERNAL(span, \
                char abort_info[256] = {0}; \
                snprintf(abort_info, sizeof(abort_info), \
                    "ABORT_TO_%s, %s:%d, %s, ERR_CODE:%d, LAT:%u us", \
                    match_key, byte::BaseNameOf(__FILE__), __LINE__, \
                    info, errcode, lat_us); \
                DO_TRACE_RECORD_STRING(name, abort_info); \
                (void)interval;); \
        } \
    } while (false)

// API macro
#define TRACING(name) \
    do { \
        UPDATE_SPAN(name); \
        byte::TraceSpan* span = TRACE_##name.Get(); \
        if (span->active_) { \
            const int64_t start_time = byte::GetCurrentTimeInNs(); \
            span->start_time_ = start_time; \
            DO_TRACE_RECORD(span); \
        } \
    } while (false)

// API macro
#define TRACEPOINT_BY_SPAN(span, trace_point_name) \
    do { \
        if (span->active_) { \
            span->start_time_ = byte::GetCurrentTimeInNs(); \
        } \
        DO_TRACE_RECORD_WITH_TRACE_POINT_DIC(span, trace_point_name, span->start_time_); \
    } while (false)

#define TRACEPOINT(span_name, trace_point_name) \
    do { \
        UPDATE_SPAN(span_name); \
        byte::TraceSpan* span = TRACE_##span_name.Get(); \
        if (span->active_) { \
            span->start_time_ = byte::GetCurrentTimeInNs(); \
        } \
        DO_TRACE_RECORD_WITH_TRACE_POINT_DIC(span, trace_point_name, span->start_time_); \
    } while (false)

// API macro
#define TRACEPOINT_START(span_name, trace_point_name) \
    do { \
        UPDATE_SPAN(span_name); \
        byte::TraceSpan* span = TRACE_##span_name.Get(); \
        if (span->active_) { \
            const int64_t start_time = byte::GetCurrentTimeInNs(); \
            span->start_time_ = start_time; \
            DO_TRACE_RECORD_WITH_TRACE_POINT_DIC(span, trace_point_name ## _START, start_time); \
        } \
    } while (false)

// API macro
#define TRACEPOINT_END(span_name, trace_point_name) \
    do { \
        UPDATE_SPAN(span_name); \
        byte::TraceSpan* span = TRACE_##span_name.Get(); \
        if (span->active_) { \
            TRACEPOINT_BY_SPAN(span, trace_point_name ## _END); \
        } \
    } while (false)

#define SPAN_TRACEPOINT_START(span, trace_point_name) \
    do { \
        if ((nullptr != span) && span->active_) { \
            const int64_t start_time = byte::GetCurrentTimeInNs(); \
            span->start_time_ = start_time; \
            DO_TRACE_RECORD_WITH_TRACE_POINT_DIC(span, trace_point_name ## _START, start_time); \
        } \
    } while (false)

// API macro
#define SPAN_TRACEPOINT_END(span, trace_point_name) \
    do { \
        if ((nullptr != span) && span->active_) { \
            TRACEPOINT_BY_SPAN(span, trace_point_name ## _END); \
        } \
    } while (false)

// API macro
#define SCOPED_TRACEPOINT(span_name, trace_point_name) \
    TRACEPOINT_START(span_name, trace_point_name); \
    BYTE_DEFER(TRACEPOINT_END(span_name, trace_point_name));

typedef struct {
_ALIGNAS(64)
     char atrricutes_str[128];
     ushort attributes_dic_id;
     char thread_idx;  // 0-255;
     void* collector;
_ALIGNAS(64)
     uint32_t ocurrency;
} TRACING_OBJ;

void RegisterThreadIdxForTracingObj(TRACING_OBJ* obj);

// API macro
#define DEFINE_TRACING_OBJ(tracing_obj_name) \
    TRACING_OBJ tracing_obj_##tracing_obj_name;

// API macro
#define REGISTER_TRACING_OBJ(tracing_obj_name, attributes_str) \
    do { \
        void* buf = reinterpret_cast<void*>(&tracing_obj_##tracing_obj_name); \
        memset(buf, 0, sizeof(TRACING_OBJ)); \
        strncpy(tracing_obj_##tracing_obj_name.atrricutes_str, attributes_str, \
                sizeof(tracing_obj_##tracing_obj_name.atrricutes_str)); \
        tracing_obj_##tracing_obj_name.attributes_dic_id = \
            byte::TracingDictionary::GetInstance()->RegisterDicItem(attributes_str); \
        RegisterThreadIdxForTracingObj(&(tracing_obj_##tracing_obj_name)); \
    } while (false)

#define UNREGISTER_TRACING_OBJ(tracing_obj_name, attributes_str) \
    do { \
        byte::TracingDictionary::GetInstance() \
            ->UnRegisterDicItem(tracing_obj_##tracing_obj_name.attributes_dic_id, \
            attributes_str); \
    } while (false)

#define SET_ANNOT_ATTRIBUTES(annot, attributes) \
    do { \
        byte::Tracing_GetAnnotation_##annot()->SetAttributes(attributes); \
    } while (false)

// API macro
#define START_TRACING(annot, name, n) \
    do { \
        static const char* k_file = byte::BaseNameOf(__FILE__); \
        static byte::TracePoint trace_point(k_file, __LINE__); \
        static const uint32_t len = strlen(#name); \
        ushort span_dic_id = TRACE_ID_##name; \
        byte::Tracing_GetAnnotation_##annot()->Start(&TRACE_##name, #name, span_dic_id, \
                                    len, trace_point.GetId(), (n)); \
    } while (false)

#define START_TRACING_WITH_OBJ(annot, span_name, n, tracing_obj_name) \
    do { \
        static const uint32_t len = strlen(#span_name); \
        ushort span_dic_id = TRACE_ID_##span_name; \
        ushort tracing_obj_attri_dic_id = tracing_obj_##tracing_obj_name.attributes_dic_id; \
        uint32_t occur = ++(tracing_obj_##tracing_obj_name.ocurrency); \
        UPDATE_SPAN(span_name); \
        if ((n !=0) && (occur % n == 0)) { \
            byte::Tracing_GetAnnotation_##annot()-> \
                                    StartDirectly(&TRACE_##span_name, \
                                                    #span_name, span_dic_id, \
                                                    len, 0, \
                                                    (tracing_obj_attri_dic_id), \
                                                    (tracing_obj_##tracing_obj_name.thread_idx)); \
        } else { \
            TRACE_##span_name->active_ = false; \
        } \
    } while (false)

// API macro
#define FINISH_TRACING(LOGLEVEL, annot, name, latency_us) \
    do { \
        static const char* k_file = byte::BaseNameOf(__FILE__); \
        static byte::TracePoint trace_point(k_file, __LINE__); \
        byte::Tracing_GetAnnotation_##annot()->Finish(&TRACE_##name, k_file, __LINE__, \
                                                      #name, trace_point.GetId(), latency_us); \
        byte::TraceSpan* span = TRACE_##name.Get(); \
        if (nullptr != span) { \
            span->Init(); \
        } \
    } while (false)

// API macro
#define ABORT_TRACING_BY_SPAN(span_name, init_span) \
    do { \
        UPDATE_SPAN(span_name); \
        byte::TraceSpan* span = TRACE_##span_name.Get(); \
        if (span->active_) { \
            span->Abort(); \
            if (init_span) { \
                if (span->active_) { \
                    span->first_tracing_ = true; \
                    span->span_message_[0] = '\0'; \
                    span->msg_full_ = false; \
                    span->name_[0] = '\0'; \
                    span->message_.clear(); \
                    span->message_size_ = 0; \
                    span->name_size_ = 0; \
                } \
            } \
        } \
    } while (false)

// API macro
#define TRACING_APPEND_SPAN_HEADER(span_name) \
    do { \
        UPDATE_SPAN(span_name); \
        byte::TraceSpan* span = TRACE_##span_name.Get(); \
        if (span->active_) { \
            if (UNLIKELY(span->msg_full_)) { \
                break; \
            } \
            if (UNLIKELY((span->message_size_ + 20) >= k_max_tracing_msg_size)) { \
                span->msg_full_ = true; \
                break; \
            } \
            static ushort span_name_dic_id = TRACE_ID_##span_name; \
            if (span->is_v4_) { \
                TRACING_APPEND_STR_RAW(span, "%%", 2); \
                TRACING_APPEND_STR_RAW(span, &span_name_dic_id, 2); \
                TRACING_APPEND_STR_RAW(span, &(span->ipv4_addr_val_), 4); \
            } else { \
                TRACING_APPEND_STR_RAW(span, "%%%", 3); \
                TRACING_APPEND_STR_RAW(span, &span_name_dic_id, 2); \
                TRACING_APPEND_STR_RAW(span, span->ipv6_addr_val_, 16); \
            } \
            TRACING_APPEND_STR_RAW(span, "&&", 2); \
        } \
    } while (false)

#define TRACING_SWITCH(name, span) \
    do { \
        byte::TraceSpan* trace_span = TRACE_##name.Get(); \
        byte::TraceSpan temp(std::move(*span)); \
        *span = std::move(*trace_span); \
        *trace_span = std::move(temp); \
    } while (false)

#define TRACING_SWITCH_TO(name, span, CODE) \
    do { \
        UPDATE_SPAN(name); \
        if (TRACE_##name.Get()->active_ && (nullptr != span)) { \
            byte::TraceSpan* trace_span = TRACE_##name.Get(); \
            trace_span->start_time_ = byte::GetCurrentTimeInNs(); \
            /*DO_TRACE_RECORD(trace_span);*/ \
            span->Init(trace_span); \
            CODE; \
            TRACE_##name->Init(); \
        } else if (span != nullptr) { \
            span->active_ = false; \
        } \
    } while (false)

#define TRACING_SWITCH_FROM(name, span, CODE) \
    do { \
        if (span != nullptr) { \
            if (span->active_) { \
                span->start_time_ = byte::GetCurrentTimeInNs(); \
                do { \
                    CODE; \
                } while (false); \
                UPDATE_SPAN(name); \
                byte::TraceSpan* trace_span = TRACE_##name.Get(); \
                trace_span->Init(span); \
                span->Init(); \
                DO_TRACE_RECORD(trace_span); \
            } else { \
                UPDATE_SPAN(name); \
                TRACE_##name.Get()->active_ = false; \
            } \
        } else { \
             UPDATE_SPAN(name); \
             TRACE_##name.Get()->active_ = false; \
        } \
    } while (false)

// API macro
#define TRACING_SWITCH_SPAN_BY_NAMES(name1, name2) \
    do { \
        UPDATE_SPAN(name1); \
        UPDATE_SPAN(name2); \
        if (TRACE_##name1.Get()->active_) { \
            byte::TraceSpan* span_origin = TRACE_##name1.Get(); \
            byte::TraceSpan* span = TRACE_##name2.Get(); \
            span->Init(span_origin); \
            if (UNLIKELY(span->msg_full_)) { \
                break; \
            } \
            if (UNLIKELY((span->message_size_ + 20) >= k_max_tracing_msg_size)) { \
                span->msg_full_ = true; \
                break; \
            } \
            TRACING_APPEND_STR_RAW(span, "]},", 3); \
            static ushort span_name_dic_id = TRACE_ID_##name2; \
            if (span->is_v4_) { \
                TRACING_APPEND_STR_RAW(span, "%%", 2); \
                TRACING_APPEND_STR_RAW(span, &span_name_dic_id, 2); \
                TRACING_APPEND_STR_RAW(span, &(span->ipv4_addr_val_), 4); \
            } else { \
                TRACING_APPEND_STR_RAW(span, "%%%", 3); \
                TRACING_APPEND_STR_RAW(span, &span_name_dic_id, 2); \
                TRACING_APPEND_STR_RAW(span, span->ipv6_addr_val_, 16); \
            } \
            TRACING_APPEND_STR_RAW(span, "&&", 2); \
            span->first_tracing_ = true; \
        } else { \
            TRACE_##name2.Get()->active_ = false;\
        } \
    } while (false)

// API macro
#define TRACING_SWITCH_TO_SPAN_BY_NAMES(name1, name2) /* to name2 */ \
    do { \
        byte::TraceSpan* span1 = TRACE_##name1.Get(); \
        if (span1 != nullptr && span1->active_) { \
            TRACING_SWITCH_SPAN_BY_NAMES(name1, name2); \
            span1->Init(); \
        } else { \
            UPDATE_SPAN(name2); \
            TRACE_##name2.Get()->active_ = false; \
        }\
    } while (false)

// API macro
#define TRACING_SWITCH_FROM_SPAN_BY_NAMES(name1, name2) /* from name2 */ \
    do { \
        UPDATE_SPAN(name1); \
        UPDATE_SPAN(name2); \
        byte::TraceSpan* span = TRACE_##name2.Get(); \
        if (span->active_) { \
            TRACING_SWITCH_SPAN_BY_NAMES(name2, name1); \
            TRACE_##name2->Init(); \
        } else { \
            TRACE_##name1.Get()->active_ = false; \
        } \
    } while (false)

// API macro
#define TRACING_SWITCH_THREAD_TO(name, span) \
    do { \
        UPDATE_SPAN(name); \
        if (TRACE_##name->active_) { \
            span = new byte::TraceSpan(); \
            TRACING_SWITCH_TO(name, span, /**/); \
        } else { \
            if (nullptr != span) { \
                span->active_ = false; \
            } \
        } \
    } while (false)

// API macro
#define TRACING_SWITCH_THREAD_FROM(name, span) \
    do { \
        if (span != nullptr && span->active_) { \
            UPDATE_SPAN(name); \
            TRACING_SWITCH_FROM(name, span, /**/); \
            delete span; \
            span = nullptr; \
        } else { \
            UPDATE_SPAN(name); \
            TRACE_##name.Get()->active_ = false; \
        } \
    } while (false)

#define TRACING_MOVE_TO(name, span) \
    do { \
        UPDATE_SPAN(name); \
        if (TRACE_##name.Get()->active_) { \
            byte::TraceSpan* trace_span = TRACE_##name.Get(); \
            TRACE_##name.Release(); \
            if (span != nullptr) { \
                TRACE_##name.Reset(span); \
            } else { \
                TRACE_##name.Reset(new byte::TraceSpan); \
            } \
            span = trace_span; \
            TRACE_##name->Init(); \
        } else { \
            if (nullptr != span) { \
                span->active_ = false; \
            } \
        } \
    } while (false)

#define TRACING_MOVE_FROM(name, span) \
    do { \
        if (span != nullptr && span->active_) { \
            TRACE_##name.Reset(span); \
            span = nullptr; \
        } else { \
            UPDATE_SPAN(name); \
            TRACE_##name.Get()->active_ = false; \
        } \
    } while (false)

// API macro
#define TRACING_MOVE_THREAD_TO(name, span) \
    do { \
        UPDATE_SPAN(name); \
        if (TRACE_##name->active_) { \
            const int64_t start_time = byte::GetCurrentTimeInNs(); \
            TRACE_##name->start_time_ = start_time; \
            TRACING_MOVE_TO(name, span); \
            DO_TRACE_RECORD(span); \
        } else { \
            if (nullptr != span) { \
                span->active_ = false; \
            } \
        } \
    } while (false)

#define TRACING_MOVE_THREAD_FROM(name, span) \
    do { \
        if (span != nullptr && span->active_) { \
            const int64_t start_time = byte::GetCurrentTimeInNs(); \
            span->start_time_ = start_time; \
            TRACING_MOVE_FROM(name, span); \
            DO_TRACE_RECORD(TRACE_##name.Get()); \
        } else { \
            UPDATE_SPAN(name); \
            TRACE_##name.Get()->active_ = false; \
        } \
    } while (false)

// API macro
#define TRACING_SWITCH_THREAD_TO_EX(name, span) \
    do { \
        UPDATE_SPAN(name); \
        if (TRACE_##name->active_) { \
            TRACING_SWITCH_TO(name, span, /**/); \
        } else { \
            if (nullptr != span) { \
                span->active_ = false; \
            } \
        } \
    } while (false)

// API macro
#define TRACING_SWITCH_THREAD_FROM_EX(name, span) \
    do { \
        if (span != nullptr && span->active_) { \
            UPDATE_SPAN(name); \
            TRACING_SWITCH_FROM(name, span, /**/); \
        } else { \
            UPDATE_SPAN(name); \
            TRACE_##name.Get()->active_ = false; \
        } \
    } while (false)


#define TRACING_SWITCH_SPAN_TO_INTERNAL(name, span, penalty) \
    UPDATE_SPAN(name); \
    if (TRACE_##name.Get()->active_) { \
        TRACING_SWITCH_TO(name, span, \
            static const char* k_file_to = byte::BaseNameOf(__FILE__); \
            static byte::TracePoint trace_point_to(k_file_to, __LINE__); \
            /*span->first_tracing_ = true*/); \
    } else if (span != nullptr) { \
        span->active_ = false; \
    }

// API macro
#define TRACING_SWITCH_SPAN_TO(name, span) \
    do { \
        TRACING_SWITCH_SPAN_TO_INTERNAL(name, span, true); \
    } while (false)

#define TRACING_SWITCH_SPAN_FROM_INTERNAL(name, span, penalty) \
do { \
    if (span != nullptr && span->active_) { \
        TRACING_SWITCH_FROM(name, span, \
            static const char* k_file = byte::BaseNameOf(__FILE__); \
            static byte::TracePoint trace_point_from(k_file, __LINE__); \
            int64_t now = start_time; \
            const char* temp_name = "RPC_" #name; \
            static ushort span_dic_id = 0; \
            static byte::SpinLock dic_reg_lock; \
            if (UNLIKELY(0 == span_dic_id)) { \
                dic_reg_lock.Lock(); \
                if (UNLIKELY(0 == span_dic_id)) { \
                    span_dic_id = byte::TracingDictionary::GetInstance() \
                        ->RegisterDicItem(temp_name); \
                } \
                dic_reg_lock.Unlock(); \
            } \
            if (UNLIKELY(span->msg_full_)) { \
                break; \
            } \
            if (UNLIKELY((span->message_size_ + 256) >= k_max_tracing_msg_size)) { \
                span->msg_full_ = true; \
                break; \
            } \
            TRACING_APPEND_STR_RAW(span, "]},", 3); \
            if (span->is_v4_) { \
                TRACING_APPEND_STR_RAW(span, "%%", 2); \
                TRACING_APPEND_STR_RAW(span, &span_dic_id, 2); \
                TRACING_APPEND_STR_RAW(span, &(span->ipv4_addr_val_), 4); \
            } else { \
                TRACING_APPEND_STR_RAW(span, "%%%", 3); \
                TRACING_APPEND_STR_RAW(span, &span_dic_id, 2); \
                TRACING_APPEND_STR_RAW(span, span->ipv6_addr_val_, 16); \
            } \
            TRACING_APPEND_STR_RAW(span, "&&", 2); \
            static const std::string& k_trace_point = \
                std::string(k_file) + ":" + std::to_string(__LINE__); \
            static ushort tp_dic_id = 0; \
            if (UNLIKELY(0 == tp_dic_id)) { \
                dic_reg_lock.Lock(); \
                if (UNLIKELY(0 == tp_dic_id)) { \
                    tp_dic_id = byte::TracingDictionary::GetInstance() \
                        ->RegisterDicItem(k_trace_point.c_str()); \
                } \
                dic_reg_lock.Unlock(); \
            } \
            TRACING_APPEND_STR_RAW(span, "||", 2); \
            TRACING_APPEND_STR_RAW(span, &tp_dic_id, 2); \
            const int64_t temp_latency = now - span->parent_trace_point_timestamp_; \
            TRACING_APPEND_LATENCY_INTERNAL(span, temp_latency, span->start_time_, \
                                            span->penalty_, INT32_MIN); \
            TRACING_APPEND_STR_RAW(span, "]},", 3); \
            static ushort span_name_dic_id = TRACE_ID_##name; \
            if (span->is_v4_) { \
                TRACING_APPEND_STR_RAW(span, "%%", 2); \
                TRACING_APPEND_STR_RAW(span, &span_name_dic_id, 2); \
                TRACING_APPEND_STR_RAW(span, &(span->ipv4_addr_val_), 4); \
            } else { \
                TRACING_APPEND_STR_RAW(span, "%%%", 3); \
                TRACING_APPEND_STR_RAW(span, &span_name_dic_id, 2); \
                TRACING_APPEND_STR_RAW(span, span->ipv6_addr_val_, 16); \
            } \
            TRACING_APPEND_STR_RAW(span, "&&", 2); \
            span->parent_trace_point_timestamp_ = now; \
            span->span_start_timestamp_ = now); \
    } else { \
        UPDATE_SPAN(name); \
        TRACE_##name.Get()->active_ = false; \
    } \
} while (false)

// API macro
#define TRACING_SWITCH_SPAN_FROM(name, span) \
    do { \
        int64_t start_time = byte::GetCurrentTimeInNs(); \
        TRACING_SWITCH_SPAN_FROM_INTERNAL(name, span, true); \
    } while (false)

// For Group Commit:

// API macro
#define TRACING_SPAN_TO(span) \
    do { \
        byte::TraceSpan* ctx = reinterpret_cast<byte::TraceSpan*>(span); \
        if (ctx != nullptr && ctx->active_) { \
            const int64_t start_time = byte::GetCurrentTimeInNs(); \
            ctx->start_time_ = start_time;\
            DO_TRACE_RECORD(ctx); \
        } \
    } while (false)

// API macro
#define TRACING_SPAN_FROM(span) \
    do { \
        byte::TraceSpan* ctx = reinterpret_cast<byte::TraceSpan*>(span); \
        if (ctx != nullptr && ctx->active_) { \
            const int64_t start_time = byte::GetCurrentTimeInNs(); \
            ctx->start_time_ = start_time;\
            DO_TRACE_RECORD(ctx); \
        } \
    } while (false)

// API macro
#define TRACING_EXCHANGE_SPAN(name, span) \
    do { \
        byte::TraceSpan* ctx = reinterpret_cast<byte::TraceSpan*>(span); \
        if (ctx != nullptr) { \
            if (UNLIKELY(ctx->msg_full_)) { \
                break; \
            } \
            if (UNLIKELY((ctx->message_size_ + 24) >= k_max_tracing_msg_size)) { \
                ctx->msg_full_ = true; \
                break; \
            } \
            TRACING_APPEND_STR_RAW(ctx, "]},", 3); \
            static const uint32_t k_len = strlen(#name); \
            static ushort span_dic_id = TRACE_ID_##name; \
            if (span->is_v4_) { \
                TRACING_APPEND_STR_RAW(span, "%%", 2); \
                TRACING_APPEND_STR_RAW(span, &span_dic_id, 2); \
                TRACING_APPEND_STR_RAW(span, &(span->ipv4_addr_val_), 4); \
            } else { \
                TRACING_APPEND_STR_RAW(span, "%%%", 3); \
                TRACING_APPEND_STR_RAW(span, &span_dic_id, 2); \
                TRACING_APPEND_STR_RAW(span, span->ipv6_addr_val_, 16); \
            } \
            ctx->first_tracing_ = true; \
            UPDATE_SPAN(name); \
            TRACING_SWITCH(name, ctx); \
        } else { \
            UPDATE_SPAN(name); \
            TRACE_##name.Get()->active_ = false; \
        } \
    } while (false)

#define TRACING_EXCHANGE_SPAN_INTERNAL(name, span) \
    do { \
        if (UNLIKELY(span->msg_full_)) { \
                break; \
        } \
        if (UNLIKELY((span->message_size_ + 20) >= k_max_tracing_msg_size)) { \
            span->msg_full_ = true; \
            break; \
        } \
        TRACING_APPEND_STR_RAW(span, "]},", 3); \
        static ushort span_dic_id = TRACE_ID_##name; \
        if (span->is_v4_) { \
            TRACING_APPEND_STR_RAW(span, "%%", 2); \
            TRACING_APPEND_STR_RAW(span, &span_dic_id, 2); \
            TRACING_APPEND_STR_RAW(span, &(span->ipv4_addr_val_), 4); \
        } else { \
            TRACING_APPEND_STR_RAW(span, "%%%", 3); \
            TRACING_APPEND_STR_RAW(span, &span_dic_id, 2); \
            TRACING_APPEND_STR_RAW(span, span->ipv6_addr_val_, 16); \
        } \
        TRACING_APPEND_STR_RAW(span, "&&", 2); \
        span->first_tracing_ = true; \
    } while (false)

// API macro
#define TRACING_EXCHANGE_SPAN_TO(name, span, name2) \
    do { \
        UPDATE_SPAN(name); \
        byte::TraceSpan* ctx = reinterpret_cast<byte::TraceSpan*>(span); \
        if (ctx != nullptr && TRACE_##name.Get()->active_) { \
            const int64_t start_time = byte::GetCurrentTimeInNs(); \
            TRACE_##name.Get()->start_time_ = start_time; \
            ctx->Init(TRACE_##name.Get()); \
            TRACING_EXCHANGE_SPAN_INTERNAL(name2, ctx); \
            TRACE_##name.Get()->Init(); \
        } else if (ctx != nullptr) { \
            ctx->active_ = false; \
        }\
    } while (false)

// API macro
#define TRACING_EXCHANGE_SPAN_FROM(name, span) \
    do { \
        byte::TraceSpan* ctx = reinterpret_cast<byte::TraceSpan*>(span); \
        if (ctx != nullptr && ctx->active_) { \
            const int64_t start_time = byte::GetCurrentTimeInNs(); \
            ctx->start_time_ = start_time; \
            TRACING_EXCHANGE_SPAN_INTERNAL(name, ctx); \
            UPDATE_SPAN(name); \
            TRACE_##name.Get()->Init(ctx); \
        } else { \
            UPDATE_SPAN(name); \
            TRACE_##name.Get()->active_ = false; \
        } \
    } while (false)

// API macro
#define TRACING_SPAN_PREPARE_REQUEST_TO(span, message, CODE) \
    do { \
        if (UNLIKELY(nullptr == span)) { \
            break; \
        } \
        if (span->active_) { \
            /*TRACING_APPEND_STR_RAW(span, "]},", 3);*/ \
            message = std::string(span->span_message_, span->message_size_); \
            span->span_message_[0] = '\0'; \
            span->message_size_ = 0; \
            span->msg_full_ = false; \
            span->first_tracing_ = true; \
            static byte::ThreadLocalPtr<byte::TracingStatInfo> TLSSTAT_stat_req_to; \
            STAT_ACTIVE_COUNT_TLS(TLSSTAT_stat_req_to, "stat_req_to"); \
        } \
        CODE; \
        span->Init(); \
    } while (false)

#define TRACING_PREPARE_REQUEST_TO(name, message, CODE) \
    do { \
        UPDATE_SPAN(name); \
        byte::TraceSpan* span = TRACE_##name.Get(); \
        TRACING_SPAN_PREPARE_REQUEST_TO(span, message, CODE); \
    } while (false)

// API macro
#define TRACING_SPAN_PREPARE_REQUEST_FROM(span, span_name_dic_id, CODE) \
    do { \
        if (UNLIKELY(nullptr == span)) { \
            break; \
        } \
        span->Init(); \
        CODE; \
        if (span->active_) { \
            span->state_ = false; \
            TRACING_APPEND_STR_RAW(span, "]},", 3); \
            if (span->is_v4_) { \
                TRACING_APPEND_STR_RAW(span, "%%", 2); \
                TRACING_APPEND_STR_RAW(span, &span_name_dic_id, 2); \
                TRACING_APPEND_STR_RAW(span, &(span->ipv4_addr_val_), 4); \
            } else { \
                TRACING_APPEND_STR_RAW(span, "%%%", 3); \
                TRACING_APPEND_STR_RAW(span, &span_name_dic_id, 2); \
                TRACING_APPEND_STR_RAW(span, span->ipv6_addr_val_, 16); \
            } \
            TRACING_APPEND_STR_RAW(span, "&&", 2); \
            span->first_tracing_ = true; \
            span->parent_trace_point_timestamp_ = start_time; \
            span->span_start_timestamp_ = start_time; \
            static byte::ThreadLocalPtr<byte::TracingStatInfo> TLSSTAT_stat_req_from; \
            STAT_ACTIVE_COUNT_TLS(TLSSTAT_stat_req_from, "stat_req_from"); \
        } \
    } while (false)

#define TRACING_PREPARE_REQUEST_FROM(name, CODE) \
    do { \
        const int64_t start_time = byte::GetCurrentTimeInNs(); \
        UPDATE_SPAN(name); \
        byte::TraceSpan* span = TRACE_##name.Get(); \
        static ushort span_name_dic_id = TRACE_ID_##name; \
        TRACING_SPAN_PREPARE_REQUEST_FROM(span, span_name_dic_id, CODE); \
    } while (false)

// API macro
#define TRACING_SPAN_PREPARE_RESPONSE_FROM(span, span_name_dic_id, CODE) \
    do { \
        if (UNLIKELY(nullptr == span)) { \
            break; \
        } \
        span->Init(); \
        CODE; \
        if (span->active_) { \
            span->state_ = true; \
            TRACING_APPEND_STR_RAW(span, "]},", 3); \
            if (span->is_v4_) { \
                TRACING_APPEND_STR_RAW(span, "%%", 2); \
                TRACING_APPEND_STR_RAW(span, &span_name_dic_id, 2); \
                TRACING_APPEND_STR_RAW(span, &(span->ipv4_addr_val_), 4); \
            } else { \
                TRACING_APPEND_STR_RAW(span, "%%%", 3); \
                TRACING_APPEND_STR_RAW(span, &span_name_dic_id, 2); \
                TRACING_APPEND_STR_RAW(span, span->ipv6_addr_val_, 16); \
            } \
            TRACING_APPEND_STR_RAW(span, "&&", 2); \
            span->first_tracing_ = true; \
            span->parent_trace_point_timestamp_ = start_time; \
            span->span_start_timestamp_ = start_time; \
            static byte::ThreadLocalPtr<byte::TracingStatInfo> TLSSTAT_stat_resp_from; \
            STAT_ACTIVE_COUNT_TLS(TLSSTAT_stat_resp_from, "stat_resp_from"); \
        } \
    } while (false)

#define TRACING_PREPARE_RESPONSE_FROM(name, CODE) \
    do { \
        const int64_t start_time = byte::GetCurrentTimeInNs(); \
        UPDATE_SPAN(name); \
        byte::TraceSpan* span = TRACE_##name.Get(); \
        static ushort span_name_dic_id = TRACE_ID_##name; \
        TRACING_SPAN_PREPARE_RESPONSE_FROM(span, span_name_dic_id, CODE); \
    } while (false)

// API macro
#define TRACING_SPAN_PREPARE_RESPONSE_TO(span, CODE) \
    do { \
        if (UNLIKELY(nullptr == span)) { \
            break; \
        } \
        if (span->active_) { \
            span->first_tracing_ = true; \
            static byte::ThreadLocalPtr<byte::TracingStatInfo> TLSSTAT_stat_resp_to; \
            STAT_ACTIVE_COUNT_TLS(TLSSTAT_stat_resp_to, "stat_resp_to"); \
        } \
        CODE; \
        span->active_ = false; \
    } while (false)

#define TRACING_PREPARE_RESPONSE_TO(name, CODE) \
    do { \
        UPDATE_SPAN(name); \
        byte::TraceSpan* span = TRACE_##name.Get(); \
        TRACING_SPAN_PREPARE_RESPONSE_TO(span, CODE); \
    } while (false)

#define TRACING_ACTIVE_COUNT(name, counter_name) \
    do { \
        UPDATE_SPAN(name); \
        byte::TraceSpan* span = TRACE_##name.Get(); \
        TRACING_SPAN_ACTIVE_COUNT(span, counter_name); \
    } while (false)

#define TRACING_SPAN_ACTIVE_COUNT(span, counter_name) \
    do { \
        if (UNLIKELY(nullptr == span)) { \
            break; \
        } \
        if (span->active_) { \
            static byte::ThreadLocalPtr<byte::TracingStatInfo> TLSSTAT_##counter_name; \
            STAT_ACTIVE_COUNT_TLS(TLSSTAT_##counter_name, #counter_name); \
        } \
    } while (false)

#define TRACING_DEBUG_PRINT(name, debug_str) \
    do { \
        UPDATE_SPAN(name); \
        byte::TraceSpan* span = TRACE_##name.Get(); \
        TRACING_SPAN_DEBUG_PRINT(span, debug_str); \
    } while (false)

#define TRACING_SPAN_DEBUG_PRINT(span, debug_str) \
    do { \
        if (UNLIKELY(nullptr == span)) { \
            break; \
        } \
        if (span->active_) { \
            LOG(DEBUG) << "TRACE " << debug_str \
                       << " annotation_id_: " << span->annotation_id_ \
                       << ", active_: " << span->active_ \
                       << ", message_size_: " << span->message_size_; \
        } \
    } while (false)

// API macro
#define TRACE_PRINTF(name, severity, format ...) \
    do { \
        UPDATE_SPAN(name); \
        byte::TraceSpan* span = TRACE_##name.Get(); \
        if (span->active_) { \
            span->start_time_ = byte::GetCurrentTimeInNs();\
        } \
        DO_TRACE_RECORD(span); \
        if (span->active_ && byte::LOG_LEVEL_ ## severity >= byte::GetMinLogLevel()) { \
            static byte::TracePoint trace_point(__FILE__, __LINE__); \
            char* buffer = byte::GetLoggingSystem()->GetArena()->New(512 * sizeof(char)); \
            LOG(severity) \
                << byte::TracingFormat(buffer, 512, trace_point.GetId(), ## format); \
            byte::GetLoggingSystem()->GetArena()->Dispose(buffer); \
        } \
    } while (false)

// API macro
#define TRACE_POINT_STAT(trace_point, severity) \
    LOG(severity) << "TRACE POINT STAT: " \
                  << (trace_point)->file_ << ":" << (trace_point)->line_ << "\n" \
                  << (trace_point)->Stat()

// API macro
#define LAST_TRACE_POINT_STAT(severity, every_n) \
    do { \
        if (!byte::IsTraceStatEnable()) { \
            break; \
        } \
        static int occurrences_mod_n = 0; \
        int occurrences = byte::AtomicIncrement(&occurrences_mod_n); \
        while (occurrences >= every_n) { \
            if (!LIKELY(byte::AtomicCompareExchange(&occurrences_mod_n, \
                                                    occurrences, \
                                                    occurrences - every_n))) { \
                occurrences = byte::AtomicGet(&occurrences_mod_n); \
            } else { \
                occurrences -= every_n; \
            } \
        } \
        if (occurrences == 0) { \
            byte::TracePoint* trace_point = byte::TracePoint::LastTracePoint(); \
            if (trace_point != nullptr) { \
                TRACE_POINT_STAT(trace_point, severity); \
            } \
        } \
    } while (false)

// API macro
#define LAST_TRACE_POINT_EVERY_STAT(name, severity, every_n) \
    do { \
        UPDATE_SPAN(name); \
        byte::TraceSpan* span = TRACE_##name.Get(); \
        if (span->active_ && byte::LOG_LEVEL_ ## severity >= byte::GetMinLogLevel()) { \
            LAST_TRACE_POINT_STAT(severity, every_n); \
        } \
    } while (false)

namespace byte {
struct TracePointData;
class TracePointCollector;
class TracePoint {
public:
    TracePoint(const char* file, int line);

    ~TracePoint();

    inline uint64_t GetId() const { return id_; }

    inline TracePoint& Set(const char* file, int line) {
        file_ = file;
        line_ = line;
        return *this;
    }

    void Add(uint64_t annotation_id, uint64_t parent_trace_point_id, int64_t interval);

    std::string Stat();

    static TracePoint* LastTracePoint();

public:
    const char* file_;
    int line_;

protected:
    friend struct TracePointData;
    friend class TracePointCollector;

private:
    uint64_t id_;
    TracePointData* data_;
};

struct TraceSpan {
    TraceSpan();
    ~TraceSpan();

    void Init();
    void Init(TraceSpan *span);
    void Abort();
    void PrintSpanDetail(const char *file, int32_t line, const char *span_var_name);
    uint32_t AppendTracingHead(char* tar_buf, uint32_t offset, uint32_t buf_size,
                                ushort attris_dic_id, uint64_t now);
    void  AppendTailAndUpdateLen(uint32_t lat_us);

_ALIGNAS(64)
    uint64_t annotation_id_;
    char* ipv4_addr_;
    char* ipv6_addr_;
    std::string message_;
_ALIGNAS(64)
    int64_t parent_trace_point_timestamp_;
    int64_t trace_start_timestamp_;
    int64_t span_start_timestamp_;
    int64_t span_proc_start_timestamp_;
_ALIGNAS(64)
    int64_t penalty_;
    int64_t start_time_;
    uint32_t message_size_;
    uint32_t name_size_;
    int64_t user_member1_;
    int64_t user_member2_;
    int64_t user_member3_;
    int64_t user_member4_;
    int64_t user_member5_;
    int64_t user_member6_;
    int64_t user_member7_;
    int64_t user_member8_;
_ALIGNAS(64)
    uint32_t ipv4_addr_val_;
    uint8_t* ipv6_addr_val_;
_ALIGNAS(64)
    bool active_;
    bool first_tracing_;
    bool state_;  // false for request, true for response
    bool msg_full_;
    bool is_v4_;  // true for ipv4, false for ipv6
    char name_[k_max_tracing_name_size + 1];  // NOLINT(runtime/arrays)
_ALIGNAS(64)
    char span_message_[k_max_tracing_msg_size +
        k_extra_tracing_msg_size + 1];  // NOLINT(runtime/arrays)
}_ALIGNAS(64);

class Annotation {
public:
    Annotation(uint32_t* occurrences, const char* name);

    ~Annotation() {}

    void Start(ThreadLocalPtr<TraceSpan>* span,
               const char* name,
               ushort span_name_dic_id,
               uint32_t len,
               uint64_t parent_trace_point_id,
               uint32_t n);

    void Start(ThreadLocalPtr<TraceSpan>* span,
               const char* name,
               ushort span_name_dic_id,
               uint32_t len,
               uint64_t parent_trace_point_id,
               uint32_t n,
               ushort obj_attri_dic_id);

    void StartDirectly(ThreadLocalPtr<TraceSpan>* span,
               const char* name,
               ushort span_name_dic_id,
               uint32_t len,
               uint64_t parent_trace_point_id,
               ushort obj_attri_dic_id,
               char thread_idx);

    void Finish(ThreadLocalPtr<TraceSpan>* span,
                const char* file,
                int line,
                const char* name,
                uint64_t parent_trace_point_id,
                int64_t latency_threshold_us);

    static void Format(TraceSpan* span);

    void WriteBinaryBuffer2File(const char* buf,
                                uint32_t buf_len,
                                const char *file_path,
                                bool append);

    void SetAttributes(std::string attributes);

private:
    uint32_t* occurrences_;
    std::string name_;
    std::string attributes_;
    ushort attributes_dic_id_;
};

char* TracingFormat(char* buffer, uint32_t size, uint64_t trace_point_id, const char* format, ...);

void EnableTraceStat();
void DisableTraceStat();
bool IsTraceStatEnable();

void EnableTraceWriteToCollector();

void DisableTraceWriteToCollector();

bool IsDataWritetoCollectorEnable();

#define DECLARE_ANNOTATION(annot) \
    namespace byte { \
    extern byte::Annotation* Tracing_GetAnnotation_##annot(); \
    extern ThreadLocalPtr<Annotation> t_annotation_##annot; \
    } \
    using byte::t_annotation_##annot

#define DEFINE_ANNOTATION(annot) \
namespace byte { \
uint32_t g_occurrences_##annot = 0; \
ThreadLocalPtr<Annotation> t_annotation_##annot; \
Annotation* Tracing_GetAnnotation_##annot() { \
    if (t_annotation_##annot.Get() == nullptr) { \
        t_annotation_##annot.Reset(new Annotation(&g_occurrences_##annot, #annot)); \
    } \
    return t_annotation_##annot.Get(); \
} \
}  /* namespace byte */ \
using byte::t_annotation_##annot
}  // namespace byte
