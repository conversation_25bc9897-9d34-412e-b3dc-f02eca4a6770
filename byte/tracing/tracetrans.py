#!/usr/bin/python3
# -*- coding: utf-8 -*-
import sys
import os
import os.path
import time
from collections import OrderedDict
import ipaddress
#str
name_prefix = "      {\"name\":\""
parent_prefix = "\"parent\":\""
abort_prefix = "      {\"ABORTED\":\""
latency_prefix = "\"latency_ns\":"
current_prefix = "\"cur_ns\":"
penalty_prefix = "\"penalty_ns\":"
span_prefix = "  {\"span\":\""
phase_prefix = ",\"phases\":["
right_prefix = "}"
comma = ","
#const 
TIME_BYTES = 8
READ_LEN = 10240
LOG_LEN_MAX = 8192
LOG_BYTE_LEN = 4
SLEEP_TIME = 0.001
SHOW_INTERNAL = 5
DATE_MAX_LEN = 20
NS_TO_S = 1000000000

#print help info 
def help():
    print("input error!")
    print("please input command such as:python3 tracetrans.py /xx/xx.dat")
    sys.exit(1)
    return

#append str info
def append_info(outstr, strin, start, max):
    num = 0
    str = ''
    for i in range(start + 1, max):
        if ((chr(strin[i]) != '\0') and ((chr(strin[i]) != '|') and (chr(strin[i]) != '$'))):
            num += 1
            str += chr(strin[i])
        elif chr(strin[i]) == '|':
            num += 1
            outstr.append(str)
            return num,str    
        elif chr(strin[i]) == '$':
            num += 1
            outstr.append(str)
            return num,str 
        else:
            outstr.append(str)
            return num + 1,str
    outstr.append(str)
    return num + 1,str

#tran latency time
def trans_timestamp_littleend(outstr, strin, start, max):
    time = 0
    if start + TIME_BYTES >= max:
        print("len invalid:", start + TIME_BYTES, max)
        return
    for i in range(start + TIME_BYTES, start, -1):
        time = time * 256
        time += strin[i]
    outstr.append(str(time))
    return time
#tran given length integer
def read_integer_by_length(strin, start, len, max):
    val = 0
    if start + len >= max:
        print("len invalid:", start + 8, max)
        return 0
    for i in range(start + len, start, -1):
        val = val * 256
        val += strin[i]
    return val

def read_integer_by_length_v6(strin, start, len, max):
    v6_list = []
    val = 0
    if start + len >= max:
        print("len invalid:", start + 8, max)
        return 0
    for i in range(start + len, start, -1):
        val = val * 256
        v6_list.append(strin[i])
    return v6_list

#tran signle log len
def trans_log_len(strin, start, max):
    len = 0
    if start + LOG_BYTE_LEN >= max:
        print("len invalid:", start + LOG_BYTE_LEN, max)
        return 0
    i = start + LOG_BYTE_LEN - 1
    while i >= start:
        len = len * 256
        len += strin[i]
        #print(strin[i])
        i -= 1 
    return len

#get dict value time info
def get_value_time_info(value):
    str = '0'
    cur = 0
    for i in value:
        if i != '#':
            str += i
        else:
            cur = int(str)
            str ='0'
    penalty = int(str)
    return cur, penalty

#cal penaly time
def cal_penaly_time(dict, name):
    begin = False
    penlaty = 0
    tmp_time =0
    curtime = 0
    for key in list(dict):
        if (name in key) and begin:
            break
        if name in key:
            begin = True
            continue
        if begin:
            curtime, tmp_time = get_value_time_info(dict[key])
            penlaty += tmp_time
            #print(tmp_time)
    #print(penlaty)
    return penlaty

#gen layer delay info
def gen_layer_delay_info(dict):
    layer_str = ""
    for key in list(dict):
        start_time = 0
        end_time = 0
        #penalty_time = 0
        tmp_time = 0
        if "_START" in key:
            #print(key)
            name = key[0:(len(key) - len("_START"))]
            #print(name)
            start_time, tmp_time = get_value_time_info(dict[key])
            #penalty_time += tmp_time
            if dict.get(name +"_END","0") == "0":
                continue
            end_time, tmp_time = get_value_time_info(dict[name +"_END"])
            #penalty_time += tmp_time
            #penalty_time += cal_penaly_time(dict, name)
            #print(start_time, end_time, penalty_time)
            layer_str += name + " total_latency(ns):" + str(end_time - start_time) + '\n'
    return layer_str

#tran long date str(1xxxxx) to date info(xxxx-xx-xx xx:xx:xx)
def trans_date_info(outstr, strin, start, end):
    str = ''
    datestr = ''
    date = 0
    find = False
    if start + 8 <= end:
        if chr(strin[start + 1]) != "d":
            outstr.append(chr(strin[start]))
            return 1
        for i in range(start, start + 8):
            str += chr(strin[i])
        #print(strin[start:start+2])
        if str == "\"date\":\"":
            i = start + 8
            while i < end:
                if chr(strin[i]) != '"':
                    datestr += chr(strin[i])
                else:
                    find = True
                    break
                i += 1
            if find and len(datestr) < DATE_MAX_LEN:
                date = int(datestr)
                outstr.append(str)
                #TODO TRANS LOG
                str = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(date/NS_TO_S)) 
                #print(str)
                outstr.append(str)
                outstr.append("\"")
                return i + 1 - start
            else:
                outstr.append(chr(strin[start]))
                return 1
        else:
            outstr.append(chr(strin[start]))
            return 1
    else:
        outstr.append(chr(strin[start]))
        return 1

dic_items = [""]*65536
#read dic files and merge to a memory dictionary
def construct_dictionary_from_files():
    file_dir = './'
    file_list = os.listdir(file_dir)
    for cur_file in file_list: 
        path = os.path.join(file_dir, cur_file)
        if os.path.isfile(path):
            if '.dic'  in path:
                print(path+"\n", end=" ", flush = True)
                fin = open(path, "rb")
                while True:
                    line = fin.readline()
                    if not line: break
                    str_line = line.decode()

                    list1 = str_line.split('|')
                    idx = int(list1[0])
                    new_val = list1[1].strip()
                    old_val = dic_items[idx]
                    if len(old_val)==0:
                        dic_items[idx] = new_val
                    else:
                        need_add = True
                        sub_list = old_val.split('||')
                        for cur_item in sub_list:
                            if cur_item == new_val:
                                need_add = False
                                break
                        if need_add:
                            dic_items[idx] += '||' + new_val

def query_dictionary_item(key):
    if (key < 0) or (key >= 65536):
        return "invalid_item!" 
    val = dic_items[key]
    if val == "":
        return "empty_item!"
    return val

def int_ip(num):
    s = []
    for i in range(4):
        s.append(str(num % 256))
        num //=256
    return '.'.join(s[::1])

def ipv6_tostr(ipv6_list):
    out=""
    for i in range(len(ipv6_list)-1,-1,-2):
        ii = 2
        while(ii > 0):
            out += (hex(ipv6_list[i])[2:]).zfill(2)
            ii -= 1
            i -= 1
        out += ":"
    out = str(ipaddress.ip_address(out[:-1]))
    return out

#format_str log info
def format_str(fout, strin, start, end):
    outstr = []
    dict = OrderedDict()
    i = start
    length = end
    func_name = ""
    tmpstr = ""
    timestr = ""
    dic_id = 0
    while i < length:
        outlen = 0
        if chr(strin[i]) == '|':
            outstr.append("\n")
            outstr.append(name_prefix) 
            if chr(strin[i + 1]) != '|':
                outlen, func_name = append_info(outstr, strin, i, length)
                i += outlen
            else:
                i += 1
                dic_id = read_integer_by_length(strin, i, 2, length)
                val = query_dictionary_item(dic_id)
                func_name = val
                outstr.append(val)
                i += 2
                i += 1
            outstr.append("\"")
            outstr.append(comma)
        elif chr(strin[i]) == '$':
            outstr.append("\n")
            outstr.append(name_prefix) 
            outlen, func_name = append_info(outstr, strin, i, length)
            i += outlen
            outstr.append("\"")
            outstr.append(comma)
        elif chr(strin[i]) == '@':
            if chr(strin[i + 1]) != '@':
                outstr.append(parent_prefix)
                outlen, tmpstr = append_info(outstr, strin, i, length)
                i += outlen
                outstr.append("\"")
                outstr.append(comma)
            else:
                i += 1
                outstr.append("\n")
                outstr.append(abort_prefix)
                outlen, tmpstr = append_info(outstr, strin, i, length)
                i += outlen
                outstr.append("\",")
        elif chr(strin[i]) == '#':
            if chr(strin[i + 1]) != '#':
                outstr.append(latency_prefix)
                trans_timestamp_littleend(outstr, strin, i, length)
                i += TIME_BYTES
                outstr.append(comma)
                outstr.append(current_prefix)
                ctime = trans_timestamp_littleend(outstr, strin, i, length)
                i += TIME_BYTES
                outstr.append(comma)
                outstr.append(penalty_prefix)
                timestr = str(ctime) + "#"
                ctime = trans_timestamp_littleend(outstr, strin, i, length)
                i += TIME_BYTES
                outstr.append(right_prefix)
                timestr += str(ctime)
                dict[func_name] = timestr
            else:
                outstr.append(latency_prefix)
                i += 1
                #trans_timestamp_littleend(outstr, strin, i, length)
                lat = read_integer_by_length(strin, i, 4, length)
                i += 4
                outstr.append("\""+str(lat)+"\"")
                outstr.append(comma)
                outstr.append(current_prefix)
                tp = read_integer_by_length(strin, i, 8, length)
                outstr.append("\""+str(tp)+"\"")
                i += TIME_BYTES
                i += 1
                #outstr.append(comma)
                #outstr.append(penalty_prefix)
                #timestr = str(time) + "#"
                #time = trans_timestamp_littleend(outstr, strin, i, length)
                #i += TIME_BYTES
                outstr.append(right_prefix)
                timestr = str(tp) + "#"
                dict[func_name] = timestr
        elif chr(strin[i]) == '%':
            outstr.append("\n")
            outstr.append(span_prefix)
            isNoDic = chr(strin[i + 1]) != '%'
            if isNoDic:
                outlen, tmpstr = append_info(outstr, strin, i, length)
                i += outlen
            else:
                isv4 = chr(strin[i + 2]) != '%'
                if isv4:
                    i += 1
                    dic_id = read_integer_by_length(strin, i, 2, length)
                    i += 2
                    val = query_dictionary_item(dic_id)
                    outstr.append(val)
                    ip_int = read_integer_by_length(strin, i, 4, length)
                    i += 4
                    ip = int_ip(ip_int)
                    outstr.append("-"+ip)
                    #annotation_id = read_integer_by_length(strin, i, 8, length)
                    #outstr.append("-"+str(annotation_id))
                    #i += 8
                    i += 1
                else:
                    i += 2
                    dic_id = read_integer_by_length(strin, i, 2, length)
                    i += 2
                    val = query_dictionary_item(dic_id)
                    outstr.append(val)
                    ipv6_list = read_integer_by_length_v6(strin, i, 16, length)
                    i += 16
                    ipv6str = ipv6_tostr(ipv6_list)
                    outstr.append("-"+ipv6str)
                    i += 1
            #trace head not unify,it print special
            if chr(strin[i]) != '|':
                outstr.append("\"")
                if isNoDic:
                    outstr.append(comma)
        elif chr(strin[i]) == '&':
            if chr(strin[i + 1]) != '&':
                outstr.append(parent_prefix)
                outlen, tmpstr = append_info(outstr, strin, i, length)
                i += outlen
                i += 1
                outstr.append("\"")
                outstr.append(phase_prefix)
            else:
                i += 2
                outstr.append(phase_prefix)
        elif chr(strin[i]) == '"':
            i += trans_date_info(outstr, strin, i, length)
        elif chr(strin[i]) == '*':
            trace_id = read_integer_by_length(strin, i, 8, length)
            i += 8
            i += 1
            outstr.append("\"id\":\""+str(trace_id)+"\"") 
        elif chr(strin[i]) == '`':
            attri_id = read_integer_by_length(strin, i, 2, length)
            attribute = query_dictionary_item(attri_id)
            i += 2
            i += 1
            outstr.append(",\"attris\":\""+attribute+"\"")
        elif chr(strin[i]) == '~':
            timestamp = read_integer_by_length(strin, i, 8, length)
            i += 8
            i += 1
            timeArray = time.localtime(timestamp/1000000000)
            timeStr = time.strftime("%Y-%m-%d %H:%M:%S", timeArray)
            outstr.append(",\"date\":\""+timeStr+"\",\"traces\":[")
        elif chr(strin[i]) == '!':
            lat = read_integer_by_length(strin, i, 4, length)
            outstr.append(",\"latency_us\":")
            outstr.append("\""+str(lat)+"\"")
            i += 4
            i += 1
        elif chr(strin[i]) != '\0':
            outstr.append(chr(strin[i]))
            i += 1
        else:
            i += 1
    fout.write("|<---------------------chain delay---------------------->|\n")
    for index in range(len(outstr)):
        fout.write(outstr[index])
    fout.write("\n")
    fout.write("|<**********************layer delay*********************>|\n")
    layer_str = gen_layer_delay_info(dict)
    fout.write(layer_str)
    #print(layer_str)
    del dict
    return 0

#find log start position
def find_start_position(fin):
    strin = fin.read(READ_LEN)
    if (len(strin) >= 3):
        if (chr(strin[0]) + chr(strin[1]) + chr(strin[2]) == '$$$'):
            return 3
    i = 0
    while i + 5 < len(strin):
        if (chr(strin[i]) + chr(strin[i + 1]) + chr(strin[i + 2]) == '$$$'):
            if (chr(strin[i + 3]) + chr(strin[i + 4]) + chr(strin[i + 5]) == '$$$'):
                return i + 6
            elif chr(strin[i + 3]) != '$':
                return i + 3 
        i += 1
    return -1

#judge path info valid
def judge_path_valid(path):
    if os.access(path, os.R_OK):
        print(path,"File is accessible to read")
    else:
        print(path,"File can not accessible to read")
        help()
    return

#read log from file and trans
def read_and_trans(path):
    read_cnt = 0
    trans_log_cnt = 0
    skip_cnt = 0
    fin = open(path, "rb")
    fout = open("./trace_out.dat", "w")
    position = find_start_position(fin)
    if position < 0:
        print("can not find valid trace info")
        return trans_log_cnt, skip_cnt
    #print(position)
    while position >= 0:
        if read_cnt % SHOW_INTERNAL == 0:
            print("->>", end=" ", flush = True)
            time.sleep(SLEEP_TIME)
        read_cnt += 1
        fin.seek(position)
        strin = fin.read(READ_LEN)
        str_len = len(strin)
        i = 0
        need_find = False
        #print(strin)
        while i < str_len:
            loglen = trans_log_len(strin, i, str_len)
            #log len invalid
            if loglen >= LOG_LEN_MAX or loglen == 0:
                i += LOG_BYTE_LEN
                need_find = True
                skip_cnt += 1
                #print("loglen invalid", loglen)
                break
            nextposition = i + loglen + LOG_BYTE_LEN
            #print("loglen", loglen)
            if (nextposition < str_len):
                if chr(strin[nextposition]) == '$':
                    format_str(fout, strin, i + LOG_BYTE_LEN, nextposition)
                    trans_log_cnt += 1
                    i = nextposition + 6
                else:
                    #single trace log not integrate
                    i += LOG_BYTE_LEN
                    need_find = True
                    skip_cnt += 1
                    #print("not integrate")
                    break
            else:
                break
        #last read 
        if str_len < READ_LEN:
            break
        #update position
        position = fin.tell() - (str_len - i)
        #need find new position
        if need_find:
            fin.seek(position)
            tmp_position = find_start_position(fin)
            if tmp_position < 0:
                print("can not find valid trace info")
                return trans_log_cnt, skip_cnt
            #print(tmp_position)
            position += tmp_position
        #sys.exit(1)
    fin.close()
    fout.close()
    return trans_log_cnt, skip_cnt

#main 
if __name__=="__main__": 
    if len(sys.argv) <= 1:
        help()
    construct_dictionary_from_files()
    path = sys.argv[1]
    if os.path.exists(path) != 1:
        print("path file:", path, "not exist")
        help()
    judge_path_valid(path)

    print("######start log trans")
    trans_log_cnt = 0
    skip_cnt = 0
    trans_log_cnt, skip_cnt = read_and_trans(path)
    print('->>')
    print("######end log trans,trans file:trace_out.dat logcout:", trans_log_cnt, " skip:", skip_cnt)
