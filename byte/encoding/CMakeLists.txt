add_subdirectory(detail)

byte_cc_library(
  NAME
    base64
  HDRS
    "base64.h"
  SRCS
    "base64.cc"
  DEPS
    byte::string_piece
    byte::modp_b64
)

byte_cc_test(
  NAME
    base64_test
  SRCS
    "base64_test.cc"
  DEPS
    byte::base64
    gtest_main  
)


byte_cc_library(
  NAME
    bin2ascii
  HDRS
    "bin2ascii.h"
  SRCS
    "bin2ascii.cc"
)

byte_cc_test(
  NAME
    bin2ascii_test
  SRCS
    "bin2ascii_test.cc"
  DEPS
    byte::bin2ascii
    gtest_main  
)


byte_cc_library(
  NAME
    int128
  HDRS
    "int128.h"
  SRCS
    "int128.cc"
  DEPS
    byte::assert
)

byte_cc_test(
  NAME
    int128_test
  SRCS
    "int128_test.cc"
  DEPS
    byte::int128
    byte::macros
    gtest_main  
)

byte_cc_library(
  NAME
    json2pb
  HDRS
    "json2pb.h"
  SRCS
    "json2pb.cc"
  DEPS
    byte::bin2ascii
    byte::jansson_target
    byte::protobuf_map
    libprotobuf
)

if (BYTE_BUILD_TESTS)
    add_subdirectory(testproto)
endif()

byte_cc_test(
  NAME
    json2pb_test
  SRCS
    "json2pb_test.cc"
  DEPS
    byte::json2pb
    byte_test_proto
    gtest_main
)

byte_cc_library(
  NAME
    plain_buffer
  HDRS
    "plain_buffer.h"
  DEPS
    byte::assert
    byte::checksum
    byte::string_piece
    byte::variant_int
)

byte_cc_test(
  NAME
    plain_buffer_test
  SRCS
    "plain_buffer_test.cc"
  DEPS
    byte::plain_buffer
    gtest_main  
)

byte_cc_library(
  NAME
    variant_int
  HDRS
    "variant_int.h"
)

byte_cc_test(
  NAME
    variant_int_test
  SRCS
    "variant_int_test.cc"
  DEPS
    byte::variant_int
    gtest_main  
)
