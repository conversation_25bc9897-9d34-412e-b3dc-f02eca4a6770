// Copyright (c) 2018-present, ByteDance Inc. All rights reserved.

/*
 * Copyright (c) 2013 <PERSON> <<EMAIL>>
 *
 * json2pb is free software; you can redistribute it and/or modify
 * it under the terms of the MIT license. See LICENSE for details.
 */

#pragma once

#include <string>

namespace google {
namespace protobuf {
class Message;
}
}

namespace byte {

struct Json2PbOptions {
    Json2PbOptions();

    // Decode string in json using base64 decoding if the type of
    // corresponding field is bytes when this option is turned on.
    // Default: true
    bool base64_to_bytes;
};
void json2pb(google::protobuf::Message* msg, const char* buf, size_t size,
             const Json2PbOptions& options = Json2PbOptions());

// pb2json
enum EnumOption {
    OUTPUT_ENUM_BY_NAME = 0,    // Output enum by its name
    OUTPUT_ENUM_BY_NUMBER = 1,  // Output enum by its value
};
struct Pb2JsonOptions {
    Pb2JsonOptions();

    // Control how enum fields are output
    // Default: OUTPUT_ENUM_BY_NUMBER
    EnumOption enum_option;

    // Convert "repeated { required string key = 1; required string value = 2; }"
    // to a map object of json and vice versa when this option is turned on.
    // Default: false
    bool enable_protobuf_map;

    // Encode the field of type bytes to string in json using base64
    // encoding when this option is turned on.
    // Default: true
    bool bytes_to_base64;
};
std::string pb2json(const google::protobuf::Message& msg,
                    const Pb2JsonOptions& options = Pb2JsonOptions());

}  // namespace byte
