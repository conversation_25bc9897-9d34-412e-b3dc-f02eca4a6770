// Copyright (c) 2018-present, ByteDance Inc. All rights reserved.

#include "byte/container/variant_vector.h"

#include <gtest/gtest.h>

TEST(variant_vector, Basic) {
    variant_vector<int> queue;
    const uint32_t count = 10000;
    for (uint32_t i = 0; i < count; ++i) {
        queue.push_back(i);
    }

    int index = 0;
    for (variant_vector<int>::const_iterator i = queue.begin();
         i != queue.end(); ++i, ++index) {
        EXPECT_EQ(index, *i);
    }
    variant_vector<int>::const_iterator i = queue.end();
    for (--i, --index; i != queue.begin(); --i, --index) {
        EXPECT_EQ(index, *i);
    }
    EXPECT_EQ(index, *i);
    EXPECT_EQ(0, index);
    for (variant_vector<int>::iterator i = queue.begin();
         i != queue.end(); ++i, ++index) {
        *i = index * 2;
        EXPECT_EQ(index * 2, *i);
    }
    for (int32_t i = 0; i < static_cast<int32_t>(count); ++i) {
        queue[i] = i + 1;
        EXPECT_EQ(i + 1, queue[i]);
    }
    queue.resize(count / 2);
    EXPECT_EQ(count / 2, queue.size());
    variant_vector<int>::const_iterator begin = queue.begin();
    variant_vector<int>::const_iterator end = queue.end();
    EXPECT_TRUE(begin < end);
    EXPECT_FALSE(begin > end);
    variant_vector<int>::const_iterator tmp = begin;
    ++begin;
    EXPECT_TRUE(begin > tmp);
    queue.clear();
    EXPECT_TRUE(queue.empty());
    EXPECT_TRUE(queue.begin() == queue.end());

    queue.push_back(1);
    queue.push_back(2);
    queue.resize(1);
    EXPECT_EQ(1u, queue.size());
    EXPECT_EQ(1, queue[0]);

    queue.resize(5);
    EXPECT_EQ(5u, queue.size());
    for (int i = 0; i < 5; ++i) {
        queue[i] = i;
    }
    EXPECT_EQ(64u, queue.GetAllocatedSize());

    for (uint32_t j = 0; j < 100; ++j) {
        variant_vector<int> qq;
        qq.resize(j);
        EXPECT_EQ(j, qq.size());
        for (uint32_t i = 0; i < j; ++i) {
            EXPECT_EQ(0, qq[i]);
        }
    }

    variant_vector<int> q0(5, 2);
    for (uint32_t i = 0; i < 5; ++i) {
        EXPECT_EQ(2, q0[i]);
    }
    variant_vector<int> r;
    r.push_back(1);
    r = q0;
    EXPECT_EQ(5u, r.size());

    // operator test.
    variant_vector<int> q1;
    variant_vector<int> q2;
    for (int i = 0; i < 10; ++i) {
        q1.push_back(i);
        q2.push_back(i);
    }
    EXPECT_TRUE(q1 == q2);
    EXPECT_TRUE(q1 >= q2);
    EXPECT_TRUE(q1 <= q2);
    q2.push_back(100);
    EXPECT_FALSE(q1 == q2);
    EXPECT_TRUE(q1 != q2);
    EXPECT_TRUE(q1 < q2);
    EXPECT_TRUE(q1 <= q2);
    EXPECT_TRUE(q2 >= q1);
    EXPECT_FALSE(q1 > q2);
    q2.clear();
    q2.push_back(0);
    EXPECT_TRUE(q1 > q2);

    variant_vector<int> q3;
    variant_vector<int> q4;
    for (int i = 0; i < 256; ++i) {
        q3.push_back(i);
    }
    q3.swap(q4);
    int j = 0;
    for (variant_vector<int>::const_iterator i = q4.begin();
         i != q4.end(); ++i, ++j) {
        EXPECT_EQ(j, *i);
    }

    EXPECT_EQ(0u, q3.size());
    EXPECT_EQ(256u, q4.size());
}
