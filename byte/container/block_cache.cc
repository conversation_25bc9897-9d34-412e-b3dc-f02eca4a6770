// Copyright (c) 2018-present, ByteDance Inc. All rights reserved.

#include <memory>
#include <utility>
#include "byte/base/atomic.h"
#include "byte/base/mem_pool_lite.h"
#include "byte/concurrent/lite_lock.h"
#include "byte/container/block_cache.h"
#include "byte/include/assert.h"

extern "C" {
#if __has_include(<isa-l/crc.h>)
#include <isa-l/crc.h>
#else
#include <isa-l/include/crc.h>
#endif
}

namespace {
static const uint32_t k_cache_init_crc_value = ~0U;
struct CacheData : public byte::RefCount<CacheData> {
    byte::RadixTree* radix_;
    byte::MemoryPoolLite* mem_pool_;
    size_t capacity_;
    byte::Atomic<bool> stopping_;
    byte::LiteRWLock* lock_;
    bool enable_crc_;

    CacheData(size_t capacity, bool enable_crc) : capacity_(capacity), enable_crc_(enable_crc) {
        radix_ = new byte::RadixTree();
        mem_pool_ = new byte::MemoryPoolLite();
        lock_ = new byte::LiteRWLock();
        stopping_ = false;
    }


    ~CacheData() {
        {
            stopping_ = true;
            byte::ScopedLiteRWLock guard(lock_, 'w');
        }
        delete radix_;
        delete mem_pool_;
        delete lock_;
    }

    bool Lookup(uint32_t pos, byte::Slice* value) {
        if (stopping_.Value()) { return false; }
        byte::ScopedLiteRWLock guard(lock_, 'r');
        auto i = radix_->find(pos);
        if (i == radix_->end()) {
            return false;
        }
        // data length
        const size_t size = *reinterpret_cast<size_t*>(i.value());
        if (size < value->size()) {
            return false;
        }
        const char* data = reinterpret_cast<const char*>(i.value()) +
                           sizeof(size_t) + sizeof(uint32_t);
        // calculated crc value
        if (enable_crc_) {
            // recorded crc value
            const uint32_t recorded_crc = *reinterpret_cast<uint32_t*>(
                    reinterpret_cast<char*>(i.value()) + sizeof(size_t));
            const uint32_t calc_crc = crc32_iscsi((unsigned char*)data,
                                                  size,
                                                  k_cache_init_crc_value);
            if (recorded_crc != calc_crc) {
                // set cache size to 0, next insert will clear wrong cache data
                *reinterpret_cast<size_t*>(i.value()) = 0;
                return false;
            }
        }
        value->set(data, value->size());
        return true;
    }

    bool Assign(uint32_t pos, uint32_t length, iovec* vec, uint32_t iov_count) {
        if (stopping_.Value()) { return false; }
        byte::ScopedLiteRWLock guard(lock_, 'w');
        if (!IsOne()) {
            // ref is not 1, cache reader exist, do not modify
            return false;
        }
        auto i = radix_->find(pos);
        if (i != radix_->end()) {
            size_t size = *reinterpret_cast<size_t*>(i.value());
            if (size >= length) {
                return false;
            }
            radix_->erase(i);
        }
        if (mem_pool_->PoolUsage() > capacity_) {
            std::unique_ptr<byte::RadixTree> scoped_radix_tree;
            std::unique_ptr<byte::MemoryPoolLite> scoped_mem_pool;
            scoped_mem_pool.reset(mem_pool_);
            scoped_radix_tree.reset(radix_);
            mem_pool_ = new byte::MemoryPoolLite();
            radix_ = new byte::RadixTree();
        }
        const size_t size = length;
        uint32_t crc = 0;
        if (enable_crc_) {
            crc = k_cache_init_crc_value;
            for (uint32_t i = 0; i < iov_count; i++) {
                crc = crc32_iscsi((unsigned char*)(vec[i].iov_base),
                                  vec[i].iov_len,
                                  crc);
            }
        }
        char* data = mem_pool_->New(length + sizeof(size) + sizeof(crc));
        // record size
        *reinterpret_cast<size_t*>(data) = size;
        // record crc
        *reinterpret_cast<uint32_t *>(data + sizeof(size)) = crc;
        // record data, Deep copy
        uint32_t offset = 0;
        for (uint32_t i = 0; i < iov_count; i++) {
            memcpy(data + sizeof(size) + sizeof(crc) + offset, vec[i].iov_base, vec[i].iov_len);
            offset += vec[i].iov_len;
        }
        BYTE_ASSERT(radix_->insert(std::make_pair(pos, data)).second);
        return true;
    }
};
}  // namespace

namespace byte {

BlockCache::BlockCache(size_t total_capacity, size_t block_capacity) :
    BlockCache(total_capacity, block_capacity, true) {
}

BlockCache::BlockCache(size_t total_capacity, size_t block_capacity, bool enable_crc) {
    cache_ = reinterpret_cast<LRUCacheShard*>(
        malloc(sizeof(LRUCacheShard)));
    double high_pri_pool_ratio = 0.0;
    new (cache_) LRUCacheShard(total_capacity, false /*strict_capcity_limit*/,
        high_pri_pool_ratio);
    total_capacity_ = total_capacity;
    block_capacity_ = block_capacity;
    enable_crc_ = enable_crc;
}

BlockCache::~BlockCache() {
    cache_->~LRUCacheShard();
    free(cache_);
}

void BlockCache::Release(void* ref) {
    CacheData* cache_data = reinterpret_cast<CacheData*>(ref);
    cache_data->Release();
}

Status BlockCache::Lookup(const Slice& key, uint32_t hash, uint32_t pos,
                          Slice* value, void** ref) {
    Cache::Handle* handle = cache_->Lookup(key, hash);
    if (handle == nullptr) {
        return Status::NotFound("key not found");
    }
    CacheData* cache_data =
        reinterpret_cast<CacheData*>((reinterpret_cast<LRUHandle*>(handle))->value);
    BYTE_CHECK_NOTNULL(cache_data);
    BYTE_DEFER(cache_->Release(handle));
    cache_data->AddRef();
    if (!cache_data->Lookup(pos, value)) {
        cache_data->Release();
        return Status::NotFound("pos not found");
    }
    *ref = cache_data;
    return Status::OK();
}

Status BlockCache::Insert(const Slice& key, uint32_t hash, uint32_t pos, const Slice& value) {
      uint32_t length = value.size();
      char* data = const_cast<char*>(value.data());
      iovec vec = {.iov_base = data, .iov_len = length };
      return Insert(key, hash, pos, length, &vec, 1);
}

Status BlockCache::Insert(const Slice& key, uint32_t hash, uint32_t pos, uint32_t length,
                          iovec* vec, uint32_t iov_count) {
    Cache::Priority priority = Cache::Priority::HIGH;
    Cache::Handle* handle = cache_->Lookup(key, hash);
    BYTE_DEFER(cache_->Release(handle));
    CacheData* cache_data = nullptr;
    if (handle == nullptr) {
        cache_data = new CacheData(block_capacity_, enable_crc_);
        cache_data->AddRef();
        BYTE_ASSERT(cache_data->Assign(pos, length, vec, iov_count));
        Status status =
            cache_->Insert(key, hash, cache_data, 1,
                [](const Slice& /*key*/, void* val) -> void {
                    (reinterpret_cast<CacheData*>(val))->Release(); },
                &handle, priority);
        if (LIKELY(status.ok())) {
            return Status::OK();
        }
        cache_data->Release();
        // failed
        return status;
    }
    // already exist, modify cache data
    cache_data = reinterpret_cast<CacheData*>((reinterpret_cast<LRUHandle*>(handle))->value);
    BYTE_CHECK_NOTNULL(cache_data);
    // we carry ref of handle, cache_data is safe, no need to AddRef();
    if (!cache_data->Assign(pos, length, vec, iov_count)) {
        return Status::AlreadyExist("Position exists");
    }
    return Status::OK();
}

}  // namespace byte
