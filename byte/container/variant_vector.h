// Copyright (c) 2018-present, ByteDance Inc. All rights reserved.

#pragma once

#include <algorithm>
#include <utility>

#include "byte/include/assert.h"

// The block sizes are variable and the table increases its block size in a
// double, starting from 16.
static const uint32_t kVectorBlockSizeTable[] = {
    16, 16, 32, 64, 128, 256, 512, 1024, 2048, 4096, 8192, 16384, 32768,
    65536, 131072, 262144, 524288, 1048576, 2097152, 4194304, 8388608, 16777216,
    33554432, 67108864, 134217728, 268435456, 536870912, 1073741824, 2147483648
};

static const int kVectorNumBlocks =
    sizeof(kVectorBlockSizeTable) / sizeof(kVectorBlockSizeTable[0]);

template<typename T, typename Ref, typename Ptr>
struct variant_vector_iterator {
    typedef T value_type;
    typedef Ref reference;
    typedef Ptr pointer;
    typedef variant_vector_iterator self;

    value_type* cur_;
    value_type* block_begin_;
    value_type* block_end_;
    const variant_vector_iterator<T, T&, T*>* end_iter_;
    value_type** blocks_;
    uint32_t index_;

    variant_vector_iterator()
        : cur_(NULL), block_begin_(NULL), block_end_(NULL),
          end_iter_(NULL), blocks_(NULL), index_(0) {}

    variant_vector_iterator(const variant_vector_iterator<T, T&, T*>& x)
        : cur_(x.cur_), block_begin_(x.block_begin_), block_end_(x.block_end_),
          end_iter_(x.end_iter_), blocks_(x.blocks_), index_(x.index_) {}

    // Assignment operator overloading using copy-and-swap idiom
    variant_vector_iterator<T, T&, T*>& operator=(
        variant_vector_iterator<T, T&, T*> other) noexcept {
        // Copy-and-swap idiom for efficient assignment
        swap(*this, other);
        return *this;
    }

    // Swap function for efficient swapping of iterator data
    void swap(variant_vector_iterator<T, T&, T*>& lhs,
              variant_vector_iterator<T, T&, T*>& rhs) noexcept {
        std::swap(lhs.cur_, rhs.cur_);
        std::swap(lhs.block_begin_, rhs.block_begin_);
        std::swap(lhs.block_end_, rhs.block_end_);
        std::swap(lhs.end_iter_, rhs.end_iter_);
        std::swap(lhs.blocks_, rhs.blocks_);
        std::swap(lhs.index_, rhs.index_);
    }

    reference operator*() const { return *cur_; }
    pointer operator->() const { return cur_; }

    self& operator++() {
        ++cur_;
        if (cur_ >= block_end_ && cur_ != end_iter_->cur_) {
            cur_ = blocks_[++index_];
            block_begin_ = cur_;
            block_end_ = cur_ + kVectorBlockSizeTable[index_];
        }
        return *this;
    }

    self& operator++(int) {
        self& tmp = *this;
        ++*this;
        return tmp;
    }

    self& operator--() {
        if (--cur_ < block_begin_) {
            --index_;
            cur_ = blocks_[index_] + kVectorBlockSizeTable[index_] - 1;
            block_begin_ = blocks_[index_];
            block_end_ = cur_ + 1;
        }
        return *this;
    }

    self& operator--(int) {
        self& tmp = *this;
        --*this;
        return tmp;
    }
};

template<typename T, typename Ref, typename Ptr>
inline bool operator==(const variant_vector_iterator<T, Ref, Ptr>& x,
                       const variant_vector_iterator<T, Ref, Ptr>& y) {
    return x.cur_ == y.cur_;
}

template<typename T, typename RefL, typename PtrL, typename RefR, typename PtrR>
inline bool operator==(const variant_vector_iterator<T, RefL, PtrL>& x,
                       const variant_vector_iterator<T, RefR, PtrR>& y) {
    return x.cur_ == y.cur_;
}

template<typename T, typename Ref, typename Ptr>
inline bool operator!=(const variant_vector_iterator<T, Ref, Ptr>& x,
                       const variant_vector_iterator<T, Ref, Ptr>& y) {
    return !(x == y);
}

template<typename T, typename RefL, typename PtrL, typename RefR, typename PtrR>
inline bool operator!=(const variant_vector_iterator<T, RefL, PtrL>& x,
                       const variant_vector_iterator<T, RefR, PtrR>& y) {
    return !(x == y);
}

template<typename T, typename Ref, typename Ptr>
inline bool operator<(const variant_vector_iterator<T, Ref, Ptr>& x,
                      const variant_vector_iterator<T, Ref, Ptr>& y) {
    return (x.index_ == y.index_) ? (x.cur_ < y.cur_) : (x.index_ < y.index_);
}

template<typename T, typename RefL, typename PtrL, typename RefR, typename PtrR>
inline bool operator<(const variant_vector_iterator<T, RefL, PtrL>& x,
                      const variant_vector_iterator<T, RefR, PtrR>& y) {
    return (x.index_ == y.index_) ? (x.cur_ < y.cur_) : (x.index_ < y.index_);
}

template<typename T, typename Ref, typename Ptr>
inline bool operator>(const variant_vector_iterator<T, Ref, Ptr>& x,
                      const variant_vector_iterator<T, Ref, Ptr>& y) {
    return y < x;
}

template<typename T, typename RefL, typename PtrL, typename RefR, typename PtrR>
inline bool operator>(const variant_vector_iterator<T, RefL, PtrL>& x,
                      const variant_vector_iterator<T, RefR, PtrR>& y) {
    return y < x;
}

template<typename T, typename Ref, typename Ptr>
inline bool operator>=(const variant_vector_iterator<T, Ref, Ptr>& x,
                       const variant_vector_iterator<T, Ref, Ptr>& y) {
    return !(x < y);
}

template<typename T, typename RefL, typename PtrL, typename RefR, typename PtrR>
inline bool operator>=(const variant_vector_iterator<T, RefL, PtrL>& x,
                       const variant_vector_iterator<T, RefR, PtrR>& y) {
    return !(x < y);
}

template<typename T, typename Ref, typename Ptr>
inline bool operator<=(const variant_vector_iterator<T, Ref, Ptr>& x,
                       const variant_vector_iterator<T, Ref, Ptr>& y) {
    return !(y > x);
}

template<typename T, typename RefL, typename PtrL, typename RefR, typename PtrR>
inline bool operator<=(const variant_vector_iterator<T, RefL, PtrL>& x,
                       const variant_vector_iterator<T, RefR, PtrR>& y) {
    return !(y < x);
}

template<typename T>
class variant_vector {
public:
    typedef T value_type;
    typedef variant_vector_iterator<T, T&, T*> iterator;
    typedef variant_vector_iterator<T, const T&, const T*> const_iterator;

    variant_vector() {
        Initialize();
    }

    explicit variant_vector(size_t n, const value_type& x = value_type()) {
        Initialize();
        resize(n, x);
    }

    variant_vector(const variant_vector& x) {
        Initialize();
        for (const_iterator i = x.begin(); i != x.end(); ++i) {
            push_back(*i);
        }
    }

    ~variant_vector() {
        clear();
    }

    void push_back(const value_type& x) {
        BYTE_ASSERT(size_ != UINT32_MAX);
        if (++size_ > allocated_size_) {
            // Full! New a block for occupation.
            Construct();
        }
        new (blocks_[block_index_] + cur_in_block_) value_type(x);
        ++cur_in_block_;
        ++finish_.cur_;
    }

    void pop_back() {
        BYTE_ASSERT(size_ != 0);
        blocks_[block_index_][cur_in_block_].~value_type();
        if (cur_in_block_ == 0) {
            Deconstruct();
        }
        --cur_in_block_;
        --finish_;
        --size_;
    }

    value_type& operator[](uint32_t n) {
        BYTE_ASSERT(n < size_);
        uint32_t index = 0;
        uint32_t offset = 0;
        GetIndexAndOffset(n, &index, &offset);
        return blocks_[index][offset];
    }

    const value_type& operator[](uint32_t n) const {
        BYTE_ASSERT(n < size_);
        uint32_t index = 0;
        uint32_t offset = 0;
        GetIndexAndOffset(n, &index, &offset);
        return blocks_[index][offset];
    }

    variant_vector& operator=(const variant_vector<T>& x) {
        clear();
        Initialize();
        for (const_iterator i = x.begin(); i != x.end(); ++i) {
            push_back(*i);
        }
        return *this;
    }

    uint32_t size() const { return size_; }

    size_t max_size() const { return UINT32_MAX; }

    bool empty() const { return size_ == 0; }

    iterator begin() { return this->start_; }

    const_iterator begin() const { return this->start_; }

    iterator end() { return this->finish_; }

    const_iterator end() const { return this->finish_; }

    T& at(size_t n) { return (*this)[n]; }

    const T& at(size_t n) const { return (*this)[n]; }

    T& front() { return *begin(); }

    const T& front() const { return *begin(); }

    T& back() {
        iterator tmp = end();
        --tmp;
        return *tmp;
    }

    const T& back() const {
        const_iterator tmp = end();
        --tmp;
        return *tmp;
    }

    uint32_t GetAllocatedSize() const {
        return allocated_size_ * sizeof(value_type);
    }

    void swap(variant_vector& x) {
        std::swap(size_, x.size_);
        std::swap(allocated_size_, x.allocated_size_);
        std::swap(cur_in_block_, x.cur_in_block_);
        std::swap(block_index_, x.block_index_);
        const uint32_t max_num_blocks =
            std::max<int32_t>(block_index_, x.block_index_) + 1;
        for (uint32_t i = 0; i < max_num_blocks; ++i) {
            std::swap(blocks_[i], x.blocks_[i]);
        }
        std::swap(start_, x.start_);
        std::swap(finish_, x.finish_);
        // Reset some members of iter.
        SetIterMemberEndAndBlocks(&start_, this);
        SetIterMemberEndAndBlocks(&x.start_, &x);
        SetIterMemberEndAndBlocks(&finish_, this);
        SetIterMemberEndAndBlocks(&x.finish_, &x);
    }

    void clear() {
        resize(0);
    }

    void resize(size_t newSize, value_type x = value_type()) {
        if (newSize < size_) {
            uint32_t i = size_ - 1;
            GetIndexAndOffset(i,
                              reinterpret_cast<uint32_t*>(&block_index_),
                              &cur_in_block_);
            value_type* addr = blocks_[block_index_] + cur_in_block_;
            for (; ; --i, --addr, --cur_in_block_) {
                addr->~value_type();
                if (cur_in_block_ == 0) {
                    delete[] blocks_[block_index_];
                    blocks_[block_index_] = NULL;
                    allocated_size_ -= kVectorBlockSizeTable[block_index_];
                    if (block_index_--) {
                        cur_in_block_ = kVectorBlockSizeTable[block_index_];
                        addr = blocks_[block_index_] + cur_in_block_;
                    }
                }
                if (i == newSize) {
                    break;
                }
            }
            if (newSize == 0) {
                start_ = finish_ = iterator();
            } else {
                finish_.cur_ = addr;
                finish_.block_begin_ = blocks_[block_index_];
                finish_.block_end_ = finish_.block_begin_ +
                            kVectorBlockSizeTable[block_index_];
                finish_.end_iter_ = &finish_;
                finish_.index_ = block_index_;
            }
        } else if (newSize > size_) {
            if (size_ == 0) {
                ++size_;
                Construct();
                --size_;
            }
            uint32_t index = 0;
            GetIndexAndOffset(size_, &index, &cur_in_block_);
            block_index_ = index;
            uint32_t blockSize = kVectorBlockSizeTable[block_index_];
            value_type* addr = blocks_[block_index_] + cur_in_block_;
            for (uint32_t i = size_; i < newSize; ++i, ++addr, ++cur_in_block_) {
                if (cur_in_block_ == blockSize) {
                    ++block_index_;
                    blockSize = kVectorBlockSizeTable[block_index_];
                    addr = reinterpret_cast<value_type*>(
                            new char[blockSize * sizeof(value_type)]);
                    allocated_size_ += blockSize;
                    cur_in_block_ = 0;
                    blocks_[block_index_] = addr;
                }
                new (addr) value_type(x);
            }
            finish_.cur_ = addr;
            finish_.block_begin_ = blocks_[block_index_];
            finish_.block_end_ = finish_.block_begin_ +
                        kVectorBlockSizeTable[block_index_];
            finish_.end_iter_ = &finish_;
            finish_.index_ = block_index_;
        }
        size_ = newSize;
    }

private:
    // Most significant bits.
    // Find the log base 2 of an N-bit integer with multiply and lookup
    // `mostPowOf2': The most pow of 2 value which is less than or equal to v.
    static inline uint32_t FastMSB32(uint32_t v, uint32_t* mostPowOf2) {
        static const int kMultiplyDeBruijnBitPosition[32] = {
            0, 9, 1, 10, 13, 21, 2, 29, 11, 14, 16, 18, 22, 25, 3, 30,
            8, 12, 20, 28, 15, 17, 24, 7, 19, 27, 23, 6, 26, 5, 4, 31
        };
        v |= v >> 1;  // first round down to one less than a power of 2
        v |= v >> 2;
        v |= v >> 4;
        v |= v >> 8;
        v |= v >> 16;
        *mostPowOf2 = (v + 1) >> 1;
        return kMultiplyDeBruijnBitPosition[
            static_cast<uint32_t>(v * 0x07C4ACDDU) >> 27];
    }

    static inline void GetIndexAndOffset(
        uint32_t n,
        uint32_t* index,
        uint32_t* offset) {
        if (n < 16) {
            *index = 0;
            *offset = n;
        } else {
            uint32_t mostPowOf2 = 0;
            *index = FastMSB32(n, &mostPowOf2) - 3;
            *offset = n - mostPowOf2;
        }
    }

    void Initialize() {
        size_ = 0;
        allocated_size_ = 0;
        cur_in_block_ = 0;
        block_index_ = -1;
        for (int i = 0; i < kVectorNumBlocks; ++i) {
            blocks_[i] = NULL;
        }
    }

    void SetIterMemberEndAndBlocks(
        iterator* it,
        variant_vector<T>* dv) {
        it->end_iter_ = &dv->finish_;
        it->blocks_ = reinterpret_cast<value_type**>(dv->blocks_);
    }

    void SetIter(iterator* it, uint32_t index, uint32_t blockSize) {
        it->block_begin_ = blocks_[index];
        it->block_end_ = blocks_[index] + blockSize;
        it->cur_ = it->block_begin_;
        it->index_ = index;
        SetIterMemberEndAndBlocks(it, this);
    }

    void Construct() {
        ++block_index_;
        const uint32_t blockSize = kVectorBlockSizeTable[block_index_];
        blocks_[block_index_] = reinterpret_cast<value_type*>(
                                new char[blockSize * sizeof(value_type)]);
        allocated_size_ += blockSize;
        cur_in_block_ = 0;
        SetIter(&finish_, block_index_, blockSize);
        if (size_ == 1) {
            // The first push_back. Set start iter.
            start_ = finish_;
        }
    }

    void Deconstruct() {
        delete[] blocks_[block_index_];
        blocks_[block_index_] = NULL;
        allocated_size_ -= kVectorBlockSizeTable[block_index_];
        cur_in_block_ = kVectorBlockSizeTable[block_index_ - 1];
        --block_index_;
    }

    uint32_t size_;
    uint32_t allocated_size_;
    uint32_t cur_in_block_;
    int32_t block_index_;
    iterator start_;
    iterator finish_;
    // blocks arrays point to the memory which have sizes as:
    // 16, 16, 32, 64, ..., 2^31
    value_type* blocks_[kVectorNumBlocks];
};

template<typename T>
inline bool operator==(const variant_vector<T>& x, const variant_vector<T>& y) {
    if (x.size() != y.size()) {
        return false;
    }
    typename variant_vector<T>::const_iterator i = x.begin();
    typename variant_vector<T>::const_iterator j = y.begin();
    while (i != x.end()) {
        if (*i != *j) {
            return false;
        }
        ++i;
        ++j;
    }
    return true;
}

template<typename T>
inline bool operator<(const variant_vector<T>& x, const variant_vector<T>& y) {
    typename variant_vector<T>::const_iterator i = x.begin();
    typename variant_vector<T>::const_iterator j = y.begin();
    while (i != x.end() && j != y.end()) {
        if (*i < *j) {
            return true;
        } else if (*i > *j) {
            return false;
        }
        ++i;
        ++j;
    }
    return i == x.end() ? (j != y.end()) : false;
}

template<typename T>
inline bool operator!=(const variant_vector<T>& x, const variant_vector<T>& y) {
    return !(x == y);
}

template<typename T>
inline bool operator>(const variant_vector<T>& x, const variant_vector<T>& y) {
    return y < x;
}

template<typename T>
inline bool operator<=(const variant_vector<T>& x, const variant_vector<T>& y) {
    return !(y < x);
}

template<typename T>
inline bool operator>=(const variant_vector<T>& x, const variant_vector<T>& y) {
    return !(x < y);
}
