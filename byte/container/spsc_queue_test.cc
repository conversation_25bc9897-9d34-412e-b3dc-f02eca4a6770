// Copyright (c) 2023-present, ByteDance Inc. All rights reserved.
#include "byte/container/spsc_queue.h"

#include <memory>
#include <vector>

#include "byte/thread/thread.h"
#include "gtest/gtest.h"

namespace byte {

struct Node {
    size_t data;
    explicit Node(size_t a) { data = a; }
};

TEST(SPSC_QUEUE, Basic) {
    SPSCQueue<Node*> queue;
    constexpr size_t num = 1024 * 1024;
    std::vector<std::unique_ptr<Node>> nodes(num);
    for (size_t i = 0; i < nodes.size(); ++i) {
        nodes[i].reset(new Node(i));
    }
    byte::Thread t([&queue, &nodes]() {
        for (auto& each : nodes) {
            queue.Push(each.get());
        }
    });
    // Consume queue
    size_t index = 0;
    Node* n = nullptr;
    while (index < num) {
        bool r = queue.Pop(&n);
        if (r) {
            EXPECT_EQ(n->data, index++);
        }
    }
    t.Join();
    EXPECT_EQ(false, queue.Pop(&n));
}

}  // namespace byte
