add_subdirectory(btree)

byte_cc_library(
  NAME
    arena
  HDRS
    "arena.h"
  SRCS
    "arena.cc"
)


byte_cc_library(
  NAME
    autovector
  HDRS
    "autovector.h"
)

if (TARGET isal-static)
  byte_cc_library(
    NAME
      block_cache
    HDRS
      "block_cache.h"
    SRCS
      "block_cache.cc"
    DEPS
      byte::assert
      byte::atomic
      byte::crc32
      byte::lite_lock
      byte::lru_cache
      byte::mem_pool_lite
      byte::radix_tree
      isal-static
  )

  byte_cc_test(
    NAME
      block_cache_test
    SRCS
      "block_cache_test.cc"
    DEPS
      byte::block_cache
      gtest_main  
      isal-static
  )
endif()

byte_cc_library(
  NAME
    cache
  HDRS
    "cache.h"
  DEPS  
    byte::slice
    byte::status
)

byte_cc_library(
  NAME
    concurrent_hash_table
  HDRS
    "concurrent_hash_table.h"
  DEPS  
    byte::assert
    byte::atomic
    byte::btree_map
    byte::hash
    byte::macros
)

byte_cc_test(
  NAME
    concurrent_hash_table_test
  SRCS
    "concurrent_hash_table_test.cc"
  DEPS
    byte::concurrent_hash_table
    byte::random
    gtest_main  
)


byte_cc_library(
  NAME
    intrusive_list
  HDRS
    "intrusive_list.h"
)

byte_cc_test(
  NAME
    intrusive_list_test
  SRCS
    "intrusive_list_test.cc"
  DEPS
    byte::assert
    byte::intrusive_list
    gmock
    gtest_main  
)

byte_cc_library(
  NAME
    list
  HDRS
    "list.h"
)


byte_cc_library(
  NAME
    lockfree_queue
  HDRS
    "lockfree_queue.h"
)

byte_cc_test(
  NAME
    lockfree_queue_test
  SRCS
    "lockfree_queue_test.cc"
  DEPS
    byte::lockfree_queue
    byte::byte_log
    byte::cond
    byte::base_thread_group
    gtest_main  
)

byte_cc_library(
  NAME
    mpsc_queue
  HDRS
    "mpsc_queue.h"
)

byte_cc_test(
  NAME
    mpsc_queue_test
  SRCS
    "mpsc_queue_test.cc"
  DEPS
    byte::mpsc_queue
    byte::byte_log
    byte::cond
    byte::base_thread_group
    gtest_main
)

byte_cc_library(
  NAME
    lockfree_stack
  HDRS
    "lockfree_stack.h"
    "detail/lockfree_detail.h"
  DEPS
    byte::assert
    byte::atomic
)

byte_cc_test(
  NAME
    lockfree_stack_test
  SRCS
    "lockfree_stack_test.cc"
  DEPS
    byte::lockfree_stack
    gtest_main  
)

byte_cc_library(
  NAME
    spsc_queue
  HDRS
    "spsc_queue.h"
)

byte_cc_test(
  NAME
    spsc_queue_test
  SRCS
    "spsc_queue_test.cc"
  DEPS
    byte::spsc_queue
    byte::thread
    gtest_main
)

byte_cc_library(
  NAME
    lru_cache
  HDRS
    "lru_cache.h"
  SRCS
    "lru_cache.cc"
  DEPS
    byte::lite_lock
    byte::sharded_cache
    byte::autovector
)

byte_cc_test(
  NAME
    lru_cache_test
  SRCS
    "lru_cache_test.cc"
  DEPS
    byte::lru_cache
    gtest_main  
)

byte_cc_library(
  NAME
    priority_queue
  HDRS
    "priority_queue.h"
)

byte_cc_test(
  NAME
    priority_queue_test
  SRCS
    "priority_queue_test.cc"
  DEPS
    byte::priority_queue
    gtest_main  
)

byte_cc_library(
  NAME
    radix_tree
  HDRS
    "radix_tree.h"
  SRCS
    "radix_tree.cc"
  DEPS
    byte::assert
    byte::macros
)

byte_cc_test(
  NAME
    radix_tree_test
  SRCS
    "radix_tree_test.cc"
  DEPS
    byte::radix_tree
    gtest_main  
)

byte_cc_library(
  NAME
    rbtree
  HDRS
    "rbtree.h"
    "rbtree_augmented.h"
  SRCS
    "rbtree.cc"
)


byte_cc_library(
  NAME
    sharded_cache
  HDRS
    "sharded_cache.h"
  SRCS
    "sharded_cache.cc"
  DEPS
    byte::lite_lock
    byte::cache
    byte::hash
)

byte_cc_library(
  NAME
    skiplist
  HDRS
    "skiplist.h"
  DEPS
    byte::arena
    byte::random
)


byte_cc_library(
  NAME
    stl_util
  HDRS
    "stl_util.h"
)

byte_cc_test(
  NAME
    stl_util_test
  SRCS
    "stl_util_test.cc"
  DEPS
    byte::stl_util
    gtest_main  
)

byte_cc_library(
  NAME
    variant_vector
  HDRS
    "variant_vector.h"
  DEPS
    byte::assert
)

byte_cc_test(
  NAME
    variant_vector_test
  SRCS
    "variant_vector_test.cc"
  DEPS
    byte::variant_vector
    gtest_main  
)
