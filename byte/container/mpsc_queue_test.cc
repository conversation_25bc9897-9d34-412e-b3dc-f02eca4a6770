// Copyright (c) 2018-present, ByteDance Inc. All rights reserved.

#include "byte/concurrent/cond.h"
#include "byte/concurrent/mutex.h"
#include "byte/container/mpsc_queue.h"
#include "byte/thread/base_thread_group.h"
#include "gtest/gtest.h"

namespace byte {

struct ThreadContext {
    ThreadContext() : queue_(nullptr), num_(0) {}

    MpscQueue<int>* queue_;
    Mutex* mutex_;
    ConditionVariable* cond_;
    int num_;
    byte::Atomic<int>* count_;
};

static void ProducerThread(ThreadContext* context) {
    for (int i = 0; i < context->num_; ++i) {
        context->queue_->Push(i + 1);
    }
}

static void WorkerThread(ThreadContext* context) {
    int pop_num = 0;
    while (pop_num != context->num_) {
        int value = 0;
        if (context->queue_->Pop(&value)) {
            EXPECT_GT(value, 0);
            pop_num++;
        }
    }

    if (--*context->count_ == 0) {
        byte::MutexLocker locker(context->mutex_);
        context->cond_->Signal();
    }
}

TEST(MpscQueueTest, EmptyTest) {
    MpscQueue<int> queue;
}

TEST(MpscQueueTest, BasicTest) {
    MpscQueue<int> queue;
    for (int k = 0; k < 65536; ++k) {
        size_t ret = queue.Push(k);
        EXPECT_EQ(ret, (size_t)k + 1);
    }

    for (int k = 0; k < 65536; ++k) {
        int i;
        EXPECT_EQ(queue.Pop(&i), true);
        EXPECT_EQ(i, k);
    }

    int p;
    EXPECT_EQ(queue.Pop(&p), false);
    EXPECT_TRUE(queue.Empty());

    BaseThreadGroup producer_thread_group;
    BaseThreadGroup consumer_thread_group;
    ThreadContext pcontext;
    ThreadContext ccontext;

    ThreadContext* context = &pcontext;
    context->queue_ = &queue;
    context->num_ = 1;
    producer_thread_group.Add(std::bind(&ProducerThread, context));

    Mutex mutex;
    ConditionVariable cond(&mutex);
    byte::Atomic<int> count = 1;
    {
        byte::MutexLocker locker(&mutex);
        ThreadContext* context = &ccontext;
        context->num_ = 1;
        context->queue_ = &queue;
        context->count_ = &count;
        context->mutex_ = &mutex;
        context->cond_ = &cond;
        consumer_thread_group.Add(std::bind(&WorkerThread, context));
        cond.Wait();
    }
    EXPECT_TRUE(queue.Empty());
}

TEST(MpscQueueTest, SingleThreadTest) {
    const int THREAD_COUNT = 1;
    ThreadContext contexts[THREAD_COUNT];
    BaseThreadGroup thread_group;
    MpscQueue<int> queue;
    const int total_num = 100000;
    for (int k = 0; k < total_num; ++k) {
        size_t ret = queue.Push(k + 1);
        EXPECT_EQ(ret, (size_t)k + 1);
    }
    Mutex mutex;
    ConditionVariable cond(&mutex);
    byte::Atomic<int> count = THREAD_COUNT;
    {
        byte::MutexLocker locker(&mutex);
        for (int i = 0; i < THREAD_COUNT; i++) {
            ThreadContext* context = &contexts[i];
            context->num_ = total_num / THREAD_COUNT;
            context->queue_ = &queue;
            context->count_ = &count;
            context->mutex_ = &mutex;
            context->cond_ = &cond;
            thread_group.Add(std::bind(&WorkerThread, context));
        }
        cond.Wait();
    }
    EXPECT_TRUE(queue.Empty());
}

TEST(MpscQueueTest, MPSCTest) {
    const int PRODUCER_THREAD_COUNT = 50;
    const int CONSUMER_THREAD_COUNT = 1;
    BaseThreadGroup producer_thread_group;
    BaseThreadGroup consumer_thread_group;
    ThreadContext pcontexts[PRODUCER_THREAD_COUNT];
    ThreadContext ccontexts[CONSUMER_THREAD_COUNT];
    MpscQueue<int> queue;
    const int total_num = 100000;
    for (int i = 0; i < PRODUCER_THREAD_COUNT; ++i) {
        ThreadContext* context = &pcontexts[i];
        context->queue_ = &queue;
        context->num_ = total_num / PRODUCER_THREAD_COUNT;
        producer_thread_group.Add(std::bind(&ProducerThread, context));
    }

    Mutex mutex;
    ConditionVariable cond(&mutex);
    byte::Atomic<int> count = CONSUMER_THREAD_COUNT;
    {
        byte::MutexLocker locker(&mutex);
        for (int i = 0; i < CONSUMER_THREAD_COUNT; i++) {
            ThreadContext* context = &ccontexts[i];
            context->num_ = total_num / CONSUMER_THREAD_COUNT;
            context->queue_ = &queue;
            context->count_ = &count;
            context->mutex_ = &mutex;
            context->cond_ = &cond;
            consumer_thread_group.Add(std::bind(&WorkerThread, context));
        }
        cond.Wait();
    }
    EXPECT_TRUE(queue.Empty());
}

}  // namespace byte
