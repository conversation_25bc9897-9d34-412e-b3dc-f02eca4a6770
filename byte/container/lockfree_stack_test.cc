// Copyright (c) 2018-present, ByteDance Inc. All rights reserved.

#include "byte/container/lockfree_stack.h"
#include "gtest/gtest.h"

namespace {
struct Foo {
    int mValue;
};
}  // namespace

namespace byte {

TEST(LockFreeStackTest, EmptyTest) {
    LockFreeStack<Foo> stack(16);
}

TEST(LockFreeStackTest, BasicTest) {
    const int count = 16;
    LockFreeStack<int> stack(16);
    EXPECT_EQ(stack.Size(), 0u);
    int bar;
    EXPECT_TRUE(!stack.Pop(&bar));

    for (int k = 0; k < count; ++k) {
        EXPECT_TRUE(stack.Push(k));
    }
    EXPECT_EQ(stack.Size(), (size_t)count);

    for (int k = 0; k < count; ++k) {
        EXPECT_TRUE(stack.Pop(&bar));
        EXPECT_EQ(bar, count - 1 - k);
    }
    EXPECT_EQ(stack.Size(), 0u);
}

}  // namespace byte
