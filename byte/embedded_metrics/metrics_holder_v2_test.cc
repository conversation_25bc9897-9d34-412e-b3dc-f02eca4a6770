// Copyright (c) 2024-present, ByteDance Inc. All rights reserved.

#include "byte/embedded_metrics/metrics_holder_v2.h"

#include <memory>
#include <string>
#include <unordered_set>

#include "byte/embedded_metrics/metrics_holder.h"
#include "gtest/gtest.h"
#include "spdlog/sinks/stdout_color_sinks.h"

namespace byte {
namespace embedded_metrics {
namespace {

template <typename MetricClass>
class CreateMetricTest : public ::testing::Test {
protected:
    bool primary_metric_ = false;

    void SetUp() override {
        MetricsOptions options;
        options.enable_logging = true;
        options.counter_window_size = 4;
        options.report_interval_secs = 3600;
        options.mode = MetricsMode::kPush2;
        Tags reg_tags = {{"tag_key", "tag_value"}};
        std::shared_ptr<spdlog::logger> logger1 = spdlog::get("primary");
        if (!logger1) {
            logger1 = spdlog::stdout_color_mt("primary");
        }
        std::shared_ptr<spdlog::logger> logger2 = spdlog::get("second");
        if (!logger2) {
            logger2 = spdlog::stdout_color_mt("second");
        }
        MetricsRegistry::GetPrimaryRegistry().Initialize("primary", reg_tags, options, logger1);
        MetricsRegistry::GetSecondaryRegistry().Initialize("second", reg_tags, options, logger2);
    }

    void TearDown() override {
        MetricsRegistry* registry = primary_metric_ ? &MetricsRegistry::GetPrimaryRegistry()
                                                    : &MetricsRegistry::GetSecondaryRegistry();
        registry->Unregister(metric_->UniqName());
    }

    void InitializeMetric(const Metrics2TagKeys& tag_keys, const Tags& tags,
                          const Metrics2Fields& fields = {}, const std::string& field = "",
                          bool primary_metric = true) {
        static int i = 0;
        std::string name = "test_metric_" + std::string(typeid(MetricClass).name()) +
                           std::to_string(primary_metric) + std::to_string(++i);
        primary_metric_ = primary_metric;
        metric_ = CreateMetric<MetricClass>(name, tag_keys, tags, fields, field, primary_metric);
    }

    std::shared_ptr<MetricClass> metric_ = nullptr;
};

TYPED_TEST_CASE_P(CreateMetricTest);

TYPED_TEST_P(CreateMetricTest, RegisterAndRetrieveMetric) {
    Metrics2TagKeys tag_keys = {"tag1", "tag2"};
    Tags tags = {{"tag1", "value1"}, {"tag2", "value2"}};

    this->InitializeMetric(tag_keys, tags);
    ASSERT_NE(this->metric_, nullptr);
}

TYPED_TEST_P(CreateMetricTest, RegisterMetricWithFields) {
    Metrics2TagKeys tag_keys = {"tag1", "tag2"};
    Tags tags = {{"tag1", "value1"}, {"tag2", "value2"}};
    Metrics2Fields fields = {"field1", "field2"};

    this->InitializeMetric(tag_keys, tags, fields, "field1");
    ASSERT_NE(this->metric_, nullptr);
}

TYPED_TEST_P(CreateMetricTest, RegisterMetricWithDuplicateTags) {
    Metrics2TagKeys tag_keys = {"tag1", "tag2"};
    Tags tags = {{"tag1", "value1"}, {"tag1", "value3"}, {"tag2", "value2"}};

    this->InitializeMetric(tag_keys, tags);
    ASSERT_NE(this->metric_, nullptr);
}

REGISTER_TYPED_TEST_CASE_P(CreateMetricTest, RegisterAndRetrieveMetric, RegisterMetricWithFields,
                           RegisterMetricWithDuplicateTags);

typedef ::testing::Types<Guage, Counter, TlsCounter, Histogram, TlsHistogram, IntGauge>
    MetricClasses;
INSTANTIATE_TYPED_TEST_CASE_P(AllCreateMetricTests, CreateMetricTest, MetricClasses);

// Test fixture for MetricsPoolV2
template <typename MetricClass>
class MetricsPoolV2Test : public ::testing::Test {
protected:
    bool primary_metric_ = false;

    void SetUp() override {
        // Initialize shared_tags
        shared_tags = {{"tag2", "value2"}, {"tag1", "value1"}};
    }

    void TearDown() override {
        MetricsRegistry* registry = primary_metric_ ? &MetricsRegistry::GetPrimaryRegistry()
                                                    : &MetricsRegistry::GetSecondaryRegistry();
        for (auto& key_to_m_pair : pool_->key_to_metric_) {
            registry->Unregister(key_to_m_pair.second->UniqName());
        }
    }

    void InitializePool(const Metrics2TagKeys& tag_keys = {}, const Metrics2Fields& fields = {},
                        bool primary_metric = true) {
        primary_metric_ = primary_metric;
        static int i = 0;
        std::string name = "test_pool_" + std::string(typeid(MetricClass).name()) +
                           std::to_string(primary_metric) + std::to_string(++i);
        pool_ = std::unique_ptr<MetricsPoolV2<MetricClass>>(
            new MetricsPoolV2<MetricClass>(name, shared_tags, tag_keys, fields, primary_metric));
    }

    Tags shared_tags = {};
    std::unique_ptr<MetricsPoolV2<MetricClass>> pool_ = nullptr;
};

TYPED_TEST_CASE_P(MetricsPoolV2Test);

TYPED_TEST_P(MetricsPoolV2Test, GetMetricReturnsNonNullForRecognizedTagsWithDefaultMetric) {
    MetricsRegistry* default_registry = &MetricsRegistry::GetPrimaryRegistry();
    size_t initial_primary_metric_cnt = default_registry->metrics_.size();
    MetricsRegistry* high_precision_registry = &MetricsRegistry::GetSecondaryRegistry();
    size_t initial_secondary_metric_cnt = high_precision_registry->metrics_.size();
    this->InitializePool({"tag1", "tag2"}, /*fields=*/{}, /*primary_metric=*/true);
    ASSERT_NE(this->pool_->GetMetric(this->shared_tags), nullptr);
    ASSERT_TRUE(initial_primary_metric_cnt + 1 == default_registry->metrics_.size())
        << initial_primary_metric_cnt << " : " << default_registry->metrics_.size();
    ASSERT_EQ(initial_secondary_metric_cnt, high_precision_registry->metrics_.size());
}

TYPED_TEST_P(MetricsPoolV2Test, GetMetricReturnsNonNullForRecognizedTagsWithHighPrecisionMetric) {
    MetricsRegistry* default_registry = &MetricsRegistry::GetPrimaryRegistry();
    size_t initial_primary_metric_cnt = default_registry->metrics_.size();
    MetricsRegistry* high_precision_registry = &MetricsRegistry::GetSecondaryRegistry();
    size_t initial_secondary_metric_cnt = high_precision_registry->metrics_.size();
    this->InitializePool({"tag1", "tag2"}, /*fields=*/{}, /*primary_metric=*/false);
    ASSERT_NE(this->pool_->GetMetric(this->shared_tags), nullptr);
    ASSERT_EQ(initial_primary_metric_cnt, default_registry->metrics_.size());
    ASSERT_TRUE(initial_secondary_metric_cnt + 1 == high_precision_registry->metrics_.size())
        << initial_secondary_metric_cnt << " : " << high_precision_registry->metrics_.size();
}

TYPED_TEST_P(MetricsPoolV2Test, GetMetricReturnsNonNullForRecognizedTagKeysWithDefaultMetric) {
    MetricsRegistry* default_registry = &MetricsRegistry::GetPrimaryRegistry();
    size_t initial_primary_metric_cnt = default_registry->metrics_.size();
    MetricsRegistry* high_precision_registry = &MetricsRegistry::GetSecondaryRegistry();
    size_t initial_secondary_metric_cnt = high_precision_registry->metrics_.size();
    this->InitializePool({"tag1", "tag2"}, /*fields=*/{}, /*primary_metric=*/true);
    auto metrics1 = this->pool_->GetMetric({{"tag2", "value1"}, {"tag1", "value2"}});
    ASSERT_NE(metrics1, nullptr);
    auto metrics2 = this->pool_->GetMetric({{"tag2", "value3"}, {"tag1", "value4"}});
    ASSERT_NE(metrics2, nullptr);
    auto metrics3 = this->pool_->GetMetric({{"tag1", "value4"}, {"tag2", "value3"}});
    ASSERT_NE(metrics3, nullptr);
    auto metrics4 = this->pool_->GetMetric({{"tag2", "value3"}});
    ASSERT_NE(metrics4, nullptr);
    ASSERT_TRUE(initial_primary_metric_cnt + 3 == default_registry->metrics_.size())
        << initial_primary_metric_cnt << " : " << default_registry->metrics_.size();
    ASSERT_EQ(initial_secondary_metric_cnt, high_precision_registry->metrics_.size());
}

TYPED_TEST_P(MetricsPoolV2Test,
             GetMetricReturnsNonNullForRecognizedTagKeysWithHighPrecisionMetric) {
    MetricsRegistry* default_registry = &MetricsRegistry::GetPrimaryRegistry();
    size_t initial_primary_metric_cnt = default_registry->metrics_.size();
    MetricsRegistry* high_precision_registry = &MetricsRegistry::GetSecondaryRegistry();
    size_t initial_secondary_metric_cnt = high_precision_registry->metrics_.size();
    this->InitializePool({"tag1", "tag2"}, /*fields=*/{}, /*primary_metric=*/false);
    auto metrics1 = this->pool_->GetMetric({{"tag2", "value1"}, {"tag1", "value2"}});
    ASSERT_NE(metrics1, nullptr);
    auto metrics2 = this->pool_->GetMetric({{"tag2", "value3"}, {"tag1", "value4"}});
    ASSERT_NE(metrics2, nullptr);
    auto metrics3 = this->pool_->GetMetric({{"tag1", "value4"}, {"tag2", "value3"}});
    ASSERT_NE(metrics3, nullptr);
    auto metrics4 = this->pool_->GetMetric({{"tag2", "value3"}});
    ASSERT_NE(metrics4, nullptr);
    ASSERT_EQ(initial_primary_metric_cnt, default_registry->metrics_.size());
    ASSERT_TRUE(initial_secondary_metric_cnt + 3 == high_precision_registry->metrics_.size())
        << initial_secondary_metric_cnt << " : " << high_precision_registry->metrics_.size();
}

TYPED_TEST_P(MetricsPoolV2Test, GetMetricReturnsNullForUnrecognizedTagsWithDefaultMetric) {
    MetricsRegistry* default_registry = &MetricsRegistry::GetPrimaryRegistry();
    size_t initial_primary_metric_cnt = default_registry->metrics_.size();
    MetricsRegistry* high_precision_registry = &MetricsRegistry::GetSecondaryRegistry();
    size_t initial_secondary_metric_cnt = high_precision_registry->metrics_.size();
    this->InitializePool(/*tag_keys=*/{}, /*fields=*/{}, /*primary_metric=*/true);
    Tags unrecognized_tags = {{"tag", "value1"}};
    ASSERT_EQ(this->pool_->GetMetric(unrecognized_tags), nullptr);
    ASSERT_EQ(initial_primary_metric_cnt, default_registry->metrics_.size());
    ASSERT_EQ(initial_secondary_metric_cnt, high_precision_registry->metrics_.size());
}

TYPED_TEST_P(MetricsPoolV2Test, GetMetricReturnsNullForUnrecognizedTagsWithHighPrecisionMetric) {
    MetricsRegistry* default_registry = &MetricsRegistry::GetPrimaryRegistry();
    size_t initial_primary_metric_cnt = default_registry->metrics_.size();
    MetricsRegistry* high_precision_registry = &MetricsRegistry::GetSecondaryRegistry();
    size_t initial_secondary_metric_cnt = high_precision_registry->metrics_.size();
    this->InitializePool(/*tag_keys=*/{}, /*fields=*/{}, /*primary_metric=*/false);
    Tags unrecognized_tags = {{"tag", "value1"}};
    ASSERT_EQ(this->pool_->GetMetric(unrecognized_tags), nullptr);
    ASSERT_EQ(initial_primary_metric_cnt, default_registry->metrics_.size());
    ASSERT_EQ(initial_secondary_metric_cnt, high_precision_registry->metrics_.size());
}

TYPED_TEST_P(MetricsPoolV2Test, GetMetricReturnsNonNullForRecognizedFieldWithDefaultMetric) {
    MetricsRegistry* default_registry = &MetricsRegistry::GetPrimaryRegistry();
    size_t initial_primary_metric_cnt = default_registry->metrics_.size();
    MetricsRegistry* high_precision_registry = &MetricsRegistry::GetSecondaryRegistry();
    size_t initial_secondary_metric_cnt = high_precision_registry->metrics_.size();
    std::unordered_set<std::string> fields_info = {"f1", "f2", "f3", "f4"};
    this->InitializePool(/*tag_keys=*/{}, fields_info, /*primary_metric=*/true);
    for (const std::string& field : fields_info) {
        ASSERT_NE(this->pool_->GetMetric({}, field), nullptr);
    }
    ASSERT_EQ(initial_primary_metric_cnt + fields_info.size(), default_registry->metrics_.size());
    ASSERT_EQ(initial_secondary_metric_cnt, high_precision_registry->metrics_.size());
}

TYPED_TEST_P(MetricsPoolV2Test, GetMetricReturnsNonNullForRecognizedFieldWithHighPrecisionMetric) {
    MetricsRegistry* default_registry = &MetricsRegistry::GetPrimaryRegistry();
    size_t initial_primary_metric_cnt = default_registry->metrics_.size();
    MetricsRegistry* high_precision_registry = &MetricsRegistry::GetSecondaryRegistry();
    size_t initial_secondary_metric_cnt = high_precision_registry->metrics_.size();
    std::unordered_set<std::string> fields_info = {"f1", "f2", "f3", "f4"};
    this->InitializePool(/*tag_keys=*/{}, fields_info, /*primary_metric=*/false);
    for (const std::string& field : fields_info) {
        ASSERT_NE(this->pool_->GetMetric({}, field), nullptr);
    }
    ASSERT_EQ(initial_primary_metric_cnt, default_registry->metrics_.size());
    ASSERT_EQ(initial_secondary_metric_cnt + fields_info.size(),
              high_precision_registry->metrics_.size());
}

TYPED_TEST_P(MetricsPoolV2Test, GetMetricReturnsNullForUnrecognizedFieldWithDefaultMetric) {
    MetricsRegistry* default_registry = &MetricsRegistry::GetPrimaryRegistry();
    size_t initial_primary_metric_cnt = default_registry->metrics_.size();
    MetricsRegistry* high_precision_registry = &MetricsRegistry::GetSecondaryRegistry();
    size_t initial_secondary_metric_cnt = high_precision_registry->metrics_.size();
    std::unordered_set<std::string> fields_info = {"f1", "f2", "f3", "f4"};
    this->InitializePool(/*tag_keys=*/{}, fields_info, /*primary_metric=*/true);
    ASSERT_EQ(this->pool_->GetMetric({}, "field"), nullptr);
    ASSERT_EQ(initial_primary_metric_cnt, default_registry->metrics_.size());
    ASSERT_EQ(initial_secondary_metric_cnt, high_precision_registry->metrics_.size());
}

TYPED_TEST_P(MetricsPoolV2Test, GetMetricReturnsNullForUnrecognizedFieldWithHighPrecisionMetric) {
    MetricsRegistry* default_registry = &MetricsRegistry::GetPrimaryRegistry();
    size_t initial_primary_metric_cnt = default_registry->metrics_.size();
    MetricsRegistry* high_precision_registry = &MetricsRegistry::GetSecondaryRegistry();
    size_t initial_secondary_metric_cnt = high_precision_registry->metrics_.size();
    std::unordered_set<std::string> fields_info = {"f1", "f2", "f3", "f4"};
    this->InitializePool(/*tag_keys=*/{}, fields_info, /*primary_metric=*/false);
    ASSERT_EQ(this->pool_->GetMetric({}, "field"), nullptr);
    ASSERT_EQ(initial_primary_metric_cnt, default_registry->metrics_.size());
    ASSERT_EQ(initial_secondary_metric_cnt, high_precision_registry->metrics_.size());
}

TYPED_TEST_P(MetricsPoolV2Test, UnregisterMetricInPrimaryRegistrySuccess) {
    MetricsRegistry* default_registry = &MetricsRegistry::GetPrimaryRegistry();
    size_t initial_primary_metric_cnt = default_registry->metrics_.size();
    MetricsRegistry* high_precision_registry = &MetricsRegistry::GetSecondaryRegistry();
    size_t initial_secondary_metric_cnt = high_precision_registry->metrics_.size();
    this->InitializePool({"t1", "t2"}, {"f1", "f2", "f3", "f4"}, /*primary_metric=*/true);
    ASSERT_NE(this->pool_->GetMetric({{"t1", "v1"}}, "f1"), nullptr);
    ASSERT_EQ(initial_primary_metric_cnt + 1, default_registry->metrics_.size());
    ASSERT_EQ(initial_secondary_metric_cnt, high_precision_registry->metrics_.size());
    ASSERT_FALSE(this->pool_->UnregisterMetric({{"t1", "v2"}}, "f1"));
    ASSERT_FALSE(this->pool_->UnregisterMetric({{"t1", "v2"}}, "f2"));
    ASSERT_FALSE(this->pool_->UnregisterMetric({{"t2", "v1"}}, "f1"));
    ASSERT_FALSE(this->pool_->UnregisterMetric({}, "f1"));
    ASSERT_FALSE(this->pool_->UnregisterMetric({{"t1", "v1"}}));
    ASSERT_EQ(initial_primary_metric_cnt + 1, default_registry->metrics_.size());
    ASSERT_EQ(initial_secondary_metric_cnt, high_precision_registry->metrics_.size());
    ASSERT_TRUE(this->pool_->UnregisterMetric({{"t1", "v1"}}, "f1"));
    ASSERT_EQ(initial_primary_metric_cnt, default_registry->metrics_.size());
    ASSERT_EQ(initial_secondary_metric_cnt, high_precision_registry->metrics_.size());
}

TYPED_TEST_P(MetricsPoolV2Test, UnregisterMetricInSecondaryRegistrySuccess) {
    MetricsRegistry* default_registry = &MetricsRegistry::GetPrimaryRegistry();
    size_t initial_primary_metric_cnt = default_registry->metrics_.size();
    MetricsRegistry* high_precision_registry = &MetricsRegistry::GetSecondaryRegistry();
    size_t initial_secondary_metric_cnt = high_precision_registry->metrics_.size();
    this->InitializePool({"t1", "t2"}, {"f1", "f2", "f3", "f4"}, /*primary_metric=*/false);
    ASSERT_NE(this->pool_->GetMetric({{"t1", "v1"}}, "f1"), nullptr);
    ASSERT_EQ(initial_primary_metric_cnt, default_registry->metrics_.size());
    ASSERT_EQ(initial_secondary_metric_cnt + 1, high_precision_registry->metrics_.size());
    ASSERT_FALSE(this->pool_->UnregisterMetric({{"t1", "v2"}}, "f1"));
    ASSERT_FALSE(this->pool_->UnregisterMetric({{"t1", "v2"}}, "f2"));
    ASSERT_FALSE(this->pool_->UnregisterMetric({{"t2", "v1"}}, "f1"));
    ASSERT_FALSE(this->pool_->UnregisterMetric({}, "f1"));
    ASSERT_FALSE(this->pool_->UnregisterMetric({{"t1", "v1"}}));
    ASSERT_EQ(initial_primary_metric_cnt, default_registry->metrics_.size());
    ASSERT_EQ(initial_secondary_metric_cnt + 1, high_precision_registry->metrics_.size());
    ASSERT_TRUE(this->pool_->UnregisterMetric({{"t1", "v1"}}, "f1"));
    ASSERT_EQ(initial_primary_metric_cnt, default_registry->metrics_.size());
    ASSERT_EQ(initial_secondary_metric_cnt, high_precision_registry->metrics_.size());
}

REGISTER_TYPED_TEST_CASE_P(MetricsPoolV2Test,
                           GetMetricReturnsNonNullForRecognizedTagsWithDefaultMetric,
                           GetMetricReturnsNonNullForRecognizedTagsWithHighPrecisionMetric,
                           GetMetricReturnsNonNullForRecognizedTagKeysWithDefaultMetric,
                           GetMetricReturnsNonNullForRecognizedTagKeysWithHighPrecisionMetric,
                           GetMetricReturnsNullForUnrecognizedTagsWithDefaultMetric,
                           GetMetricReturnsNullForUnrecognizedTagsWithHighPrecisionMetric,
                           GetMetricReturnsNonNullForRecognizedFieldWithDefaultMetric,
                           GetMetricReturnsNonNullForRecognizedFieldWithHighPrecisionMetric,
                           GetMetricReturnsNullForUnrecognizedFieldWithDefaultMetric,
                           GetMetricReturnsNullForUnrecognizedFieldWithHighPrecisionMetric,
                           UnregisterMetricInPrimaryRegistrySuccess,
                           UnregisterMetricInSecondaryRegistrySuccess);

typedef ::testing::Types<Guage, Counter, TlsCounter, Histogram, TlsHistogram, IntGauge>
    MetricClasses;
INSTANTIATE_TYPED_TEST_CASE_P(AllMetricsPoolV2Test, MetricsPoolV2Test, MetricClasses);
}  // namespace
}  // namespace embedded_metrics
}  // namespace byte
