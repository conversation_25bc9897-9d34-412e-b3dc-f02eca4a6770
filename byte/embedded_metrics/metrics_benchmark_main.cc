// Copyright (c) 2023-present, ByteDance Inc. All rights reserved.
#include "benchmark/benchmark.h"
#include "byte/byte_log/byte_log_impl.h"
#include "byte/embedded_metrics/metrics_registry.h"

class MockMetricsRegistry : public byte::embedded_metrics::MetricsRegistry {};

const int kTotal = 10000000;
static void BMMultiThreadedTlsHistogram(benchmark::State& state) {  // NOLINT(runtime/references)
    byte::internal::ScopedSetLogLevel log_level(byte::LOG_LEVEL_ERROR);
    static std::shared_ptr<byte::embedded_metrics::TlsHistogram> tls_histogram;
    if (state.thread_index == 0) {
        MockMetricsRegistry metrics_registry;
        byte::embedded_metrics::MetricsOptions options;
        byte::embedded_metrics::Tags common_tags{std::make_pair("c_tagkey", "c_value")};
        metrics_registry.Initialize("MetricsTest", common_tags, options, nullptr, nullptr);
        byte::embedded_metrics::Tags tls_histogram_tags{
            std::make_pair("tls_histogram_tagkey", "tls")};
#ifdef BYTE_ENABLE_METRICS2
        metrics_registry.RegisterMetricName("tls_histogram",
                                            byte::embedded_metrics::MetricType::kTlsHistogram,
                                            {"tls_histogram_tagkey"});
        auto metric_base = metrics_registry.GetByteMetric(
            "tls_histogram", byte::embedded_metrics::MetricType::kTlsHistogram, tls_histogram_tags);
#else
        auto metric_base = metrics_registry.Register(
            "tls_histogram", tls_histogram_tags, byte::embedded_metrics::MetricType::kTlsHistogram);
#endif
        tls_histogram =
            std::dynamic_pointer_cast<byte::embedded_metrics::TlsHistogram>(metric_base);
    }
    for (auto _ : state) {
        std::chrono::duration<double> elapsed_seconds = std::chrono::duration<double>();
        for (int i = 0; i < kTotal; ++i) {
            auto start = std::chrono::high_resolution_clock::now();
            tls_histogram->Set(i);
            elapsed_seconds += (std::chrono::high_resolution_clock::now() - start);
        }
        state.SetIterationTime(elapsed_seconds.count());
    }
}

static void BMMultiThreadedHistogram(benchmark::State& state) {  // NOLINT(runtime/references)
    byte::internal::ScopedSetLogLevel log_level(byte::LOG_LEVEL_ERROR);
    static std::shared_ptr<byte::embedded_metrics::Histogram> histogram;
    if (state.thread_index == 0) {
        MockMetricsRegistry metrics_registry;
        byte::embedded_metrics::MetricsOptions options;
        byte::embedded_metrics::Tags common_tags{std::make_pair("c_tagkey", "c_value")};
        metrics_registry.Initialize("MetricsTest", common_tags, options, nullptr, nullptr);
        byte::embedded_metrics::Tags histogram_tags{std::make_pair("histogram_tagkey", "hist")};
#ifdef BYTE_ENABLE_METRICS2
        metrics_registry.RegisterMetricName(
            "histogram", byte::embedded_metrics::MetricType::kHistogram, {"histogram_tagkey"});
        auto metric_base = metrics_registry.GetByteMetric(
            "histogram", byte::embedded_metrics::MetricType::kHistogram, histogram_tags);
#else
        auto metric_base = metrics_registry.Register(
            "histogram", histogram_tags, byte::embedded_metrics::MetricType::kHistogram);
#endif
        histogram = std::dynamic_pointer_cast<byte::embedded_metrics::Histogram>(metric_base);
    }
    for (auto _ : state) {
        std::chrono::duration<double> elapsed_seconds = std::chrono::duration<double>();
        for (int i = 0; i < kTotal; ++i) {
            auto start = std::chrono::high_resolution_clock::now();
            histogram->Set(i);
            elapsed_seconds += (std::chrono::high_resolution_clock::now() - start);
        }
        state.SetIterationTime(elapsed_seconds.count());
    }
}

BENCHMARK(BMMultiThreadedTlsHistogram)->ThreadRange(1, 64)->UseManualTime();
BENCHMARK(BMMultiThreadedHistogram)->ThreadRange(1, 64)->UseManualTime();

// Run the benchmark
BENCHMARK_MAIN();
