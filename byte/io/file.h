// Copyright (c) 2018-present, ByteDance Inc. All rights reserved.

#pragma once

#include <stdint.h>
#include <time.h>
#include <string>

#include "byte/include/slice.h"
#include "byte/include/status.h"
#include "byte/include/macros.h"

namespace byte {

struct FileAttributes {
    time_t create_time_;
    time_t modify_time_;
    time_t access_time_;
    time_t change_time_;
};

// File type flags
enum FileType {
    FILE_NONE = 0,         // Nothing
    FILE_REGULAR = 1,      // Regular file
    FILE_DIRECTORY = 2,    // Directory
    FILE_LINK = 4,         // Symbolic Link
    FILE_ALL = FILE_REGULAR | FILE_DIRECTORY | FILE_LINK,
};

// File iteration entry.
struct FileEntry {
FileEntry(FileType type, const std::string& filename)
: type_(type), name_(filename) {}
    FileEntry() {}
    FileType type_;
    std::string name_;  // path name starting from cwd;
                        // eg: filepath is /a/b/c/filename,
                        // cwd is /a/b, then name_ is "c/filename"
};

// Iterate file/dir entries in a directory.
class FileIterator {
protected:
    FileIterator() {}
public:
    virtual ~FileIterator() {}

    // Return true if the next entry exists.
    // Return false if the iteration is complete.
    virtual bool GetNext(FileEntry* entry) = 0;
    virtual bool Seek(const FileEntry& entry) = 0;
};

class File {
public:
    virtual ~File();

    virtual Status Read(uint64_t length, Slice* result, char* scratch) = 0;

    virtual Status Write(const Slice& data) = 0;

    virtual Status PRead(uint64_t offset, uint64_t length, Slice* result, char* scratch) = 0;

    virtual Status PWrite(uint64_t offset, const Slice& data) = 0;

    virtual Status Prealloc(uint64_t offset, uint64_t length) = 0;

    virtual Status Truncate(uint64_t length) = 0;

    virtual Status FAdvise(uint64_t offset, uint64_t length, int advice) = 0;

    virtual Status Flush() = 0;

    virtual Status Sync() = 0;

    virtual Status Close() = 0;

    virtual Status Seek(uint64_t offset) = 0;

    virtual Status Tell(uint64_t* offset) = 0;

    virtual Status GetFileSize(uint64_t* size) = 0;

    virtual Status GetAttributes(FileAttributes* attrs) const = 0;

public:
    static FileIterator* NewIterater(const std::string& dir);

protected:
    File();  // Used ONLY by FileSystem Open

private:
    DISALLOW_COPY_AND_ASSIGN(File);
};

}  // namespace byte
