// Copyright (c) 2018-present, ByteDance Inc. All rights reserved.

#include <stdlib.h>
#include <time.h>
#include <memory>
#include <sstream>
#include <vector>

#include "byte/algorithm/random.h"
#include "byte/encoding/variant_int.h"
#include "byte/io/local_filesystem.h"
#include "byte/io/recordio.h"
#include "byte/string/string_piece.h"
#include "byte/system/timestamp.h"
#include "gtest/gtest.h"

namespace byte {
#define CheckRecords() CheckRecordsInternal(__LINE__)
#define CheckCorruptRecords() CheckCorruptRecordsInternal(__LINE__)

class RecordioTest : public testing::Test {
public:
    void SetUp() {
        stream_.reset(new std::stringstream());
        record_writer_.reset(new RecordWriter(stream_.get()));
        record_reader_.reset(new RecordReader(stream_.get()));
        stream_position = 0LL;
    }

    void TearDown() {
        record_writer_.reset();
        record_reader_.reset();
        stream_.reset();
    }

    std::string MakeTempFileName() {
        char temp_name[1024] = "/tmp/tmp_XXXXXX";
        const int fd = mkstemp(temp_name);
        EXPECT_GE(fd, 0);
        close(fd);
        return temp_name;
    }

    bool WriteRecord(const StringPiece& record) {
        if (!record_writer_->Write(record)) {
            return false;
        }
        std::unique_ptr<char[]> buffer(new char[record.size()]);
        memcpy(buffer.get(), record.data(), record.size());
        records_.push_back(std::string(buffer.get(), record.size()));
        return true;
    }

    bool Flush() {
        return record_writer_->Flush();
    }

    bool FlushAndUpdateSlices() {
        const bool result = Flush();
        stream_->seekp(stream_position, std::ios::beg);
        const std::string& str = stream_->str();
        stream_position = 0;
        slices_.header = str.substr(0U, 4U);
        uint32_t bodySize = 0;
        size_t offset = 0;
        stream_position += 4U;
        VariantInt::Decode<uint32_t>(
            str.substr(4U).data(),
            &bodySize,
            VariantInt::GetMaxCharsOfEncodedSize<uint32_t>(),
            &offset);
        slices_.encoded_data = str.substr(stream_position, offset);
        stream_position += offset;
        slices_.header_checksum = str.substr(stream_position, 2U);
        stream_position += 2U;
        slices_.body = str.substr(stream_position, bodySize);
        stream_position += bodySize;
        slices_.checksum = str.substr(stream_position, 4U);
        stream_position += 4;
        slices_.record_size = stream_position;
        return result;
    }

    void MergeSlices() {
        composed_slices_.clear();
        composed_slices_ += slices_.header;
        composed_slices_ += slices_.encoded_data;
        composed_slices_ += slices_.header_checksum;
        composed_slices_ += slices_.body;
        composed_slices_ += slices_.checksum;
    }

    void Switch() {
        stream_.reset(new std::stringstream(
                composed_slices_,
                std::ios::in | std::ios::binary));
        record_reader_.reset(new RecordReader(stream_.get()));
    }

    void RoilMagic(uint16_t magic) {
        slices_.header[0] = magic & 0x00ff;
        slices_.header[1] = magic >> 8;
    }

    void DuplicateMagic() {
        std::string header;
        header[0] = slices_.header[0];
        header[1] = slices_.header[1];
        slices_.header = header + slices_.header;
    }

    void WrongMagicAhead(uint16_t magic) {
        std::string header = slices_.header;
        slices_.header.resize(6);
        RoilMagic(magic);
        slices_.header[5] = header[3];
        slices_.header[4] = header[2];
        slices_.header[3] = header[1];
        slices_.header[2] = header[0];
    }

    void GarbageAhead(const std::string& garbage) {
        slices_.header = garbage + slices_.header;
    }

    void RoilHeaderChecksum(uint16_t checksum) {
        slices_.header_checksum[0] = checksum & 0x00ff;
        slices_.header_checksum[1] = checksum >> 8;
    }

    void RoilChecksum(uint32_t checksum) {
        slices_.checksum[0] =  checksum & 0x000000ffU;
        slices_.checksum[1] = (checksum & 0x0000ff00U) >> 8;
        slices_.checksum[2] = (checksum & 0x00ff0000U) >> 16;
        slices_.checksum[3] = (checksum & 0xff000000U) >> 24;
    }

    void Reset() {
        std::stringstream* ss = new std::stringstream();
        record_writer_.reset(new RecordWriter(ss));
        record_reader_.reset(new RecordReader(ss));
        stream_.reset(ss);
        records_.clear();
    }

    void CheckRecordsInternal(const int line) {
        Flush();
        StringPiece record;
        uint32_t index = 0;
        while (record_reader_->Read(&record)) {
            if (index < records_.size()) {
                EXPECT_EQ(records_[index], record.as_string())
                    << "Records mismatch at line " << line;
            }
            ++index;
        }
        EXPECT_EQ(index, records_.size())
            << "Num records mismatch at line " << line;
        EXPECT_EQ(0U, record_reader_->GetSkippedBytes())
            << "Non-zero skipped bytes at line " << line;
        EXPECT_EQ(0U, record_reader_->GetTotalSkippedBytes())
            << "Non-zero total skipped bytes at line " << line;
        EXPECT_FALSE(record_reader_->IsCorruptDetected())
            << "Corrupt detected at line " << line;
        Reset();
    }

    void CheckCorruptRecordsInternal(const int line) {
        MergeSlices();
        Switch();

        StringPiece record;
        uint32_t index = 0;
        while (record_reader_->Read(&record)) {
            if (index < records_.size()) {
                EXPECT_EQ(records_[index], record.as_string())
                    << "Records mismatch at line " << line;
            }
            ++index;
        }
        EXPECT_EQ(index, records_.size())
            << "Num records mismatch at line " << line;
        Reset();
    }

    // Split a full block into slices.
    struct RecordSlices {
        std::string header;
        std::string encoded_data;
        std::string header_checksum;
        std::string body;
        std::string checksum;
        uint32_t record_size;
    };

    std::unique_ptr<std::stringstream> stream_;
    std::unique_ptr<RecordWriter> record_writer_;
    std::unique_ptr<RecordReader> record_reader_;
    std::vector<std::string> records_;
    int64_t stream_position;
    RecordSlices slices_;
    std::string composed_slices_;
};

TEST_F(RecordioTest, NormalWriteDataTest) {
    // 1. No record.
    CheckRecords();
    // 2. Empty record.
    EXPECT_TRUE(WriteRecord(""));
    CheckRecords();
    // 3. One character record.
    EXPECT_TRUE(WriteRecord("!"));
    CheckRecords();
    // 4. One record.
    EXPECT_TRUE(WriteRecord("abcdefgh"));
    CheckRecords();
    // 5. Many empty record.
    for (int i = 0; i < 10; ++i) {
        EXPECT_TRUE(WriteRecord(""));
    }
    CheckRecords();
    // 6. Various records. 33 Bytes (larger than size_at_least(32 bytes))
    EXPECT_TRUE(WriteRecord("abcdefgh"));
    EXPECT_TRUE(WriteRecord(""));
    EXPECT_TRUE(WriteRecord("?"));
    EXPECT_TRUE(WriteRecord("ijklmnop"));
    CheckRecords();
    // 7. Triple flushes.
    EXPECT_TRUE(WriteRecord("abcde"));
    Flush();
    Flush();
    CheckRecords();
    // 8. Alternatives.
    for (int i = 0; i < 10; ++i) {
        EXPECT_TRUE(WriteRecord("abcde"));
        Flush();
    }
    CheckRecords();
    // 9. Large number of one-byte records.
    for (int i = 0; i < 100000; i++) {
        EXPECT_TRUE(WriteRecord("*"));
    }
    CheckRecords();
    // 10. Refill buffer every record.
    const uint32_t MAXSIZE = 100000;
    char* buffer = new char[MAXSIZE + 1];
    uint32_t size = 1;
    while (size < MAXSIZE) {
        memset(buffer, 'c', size);
        buffer[size] = '\0';
        EXPECT_TRUE(WriteRecord(StringPiece(buffer, size)));
        size *= 2;
    }
    CheckRecords();
    delete[] buffer;
}

TEST_F(RecordioTest, FileWriteDataTest) {
    const char kTestDirName[] = "./localtestio";
    const char kTestRandFileName[] = "./localtestio/localtestrandfile2";
    std::unique_ptr<LocalFileSystem> fs;
    fs.reset(new LocalFileSystem);
    CreateOptions dir_options;
    Status s = fs->CreateDir(kTestDirName, dir_options);
    if (!s.ok()) {
        EXPECT_TRUE(s.ok());
        return;
    }

    // Create
    OpenOptions file_options;
    std::unique_ptr<File> new_file;
    s = fs->Open(kTestRandFileName, &new_file,
                 FILE_FLAGS_CREATE_RANDRDWR, file_options);
    EXPECT_TRUE(s.ok());

    // Close
    s = new_file->Close();
    EXPECT_TRUE(s.ok());

    // Reopen empty file
    s = fs->Open(kTestRandFileName, &new_file, FILE_FLAGS_OPEN_SEQWRONLY, file_options);
    EXPECT_TRUE(s.ok());
    std::unique_ptr<File> new_rfile;
    s = fs->Open(kTestRandFileName, &new_rfile, FILE_FLAGS_READ, file_options);

    record_writer_.reset(new RecordWriter(new_file.get()));
    record_reader_.reset(new RecordReader(new_rfile.get()));

    EXPECT_TRUE(WriteRecord("abcdefgh"));
    EXPECT_TRUE(WriteRecord("xyz"));
    CheckRecords();

    // Close
    s = new_file->Close();
    EXPECT_TRUE(s.ok());
    s = new_rfile->Close();
    EXPECT_TRUE(s.ok());

    DeleteOptions delfile_options;
    s = fs->DeleteFile(kTestRandFileName, delfile_options);
    EXPECT_TRUE(s.ok());
}

TEST_F(RecordioTest, RandomWriteDataTest) {
    // Initialize rand seed.
    Random r(GetCurrentTime());
    #define FACTOR (static_cast<float>(r.Next() % 10000) / 10000.f)
    for (uint32_t i = 0; i < 1000; ++i) {
        char buffer[30];
        const uint32_t writeRepeats = static_cast<uint32_t>(FACTOR * 5 + 1);
        for (uint32_t j = 0; j < writeRepeats; ++j) {
            const uint32_t length = static_cast<uint32_t>(FACTOR * 20 + 1);
            for (uint32_t k = 0; k < length; ++k) {
                buffer[k] = r.Next() % 26 + 0x61;
            }
            EXPECT_TRUE(WriteRecord(StringPiece(buffer, length)));
        }
        EXPECT_TRUE(Flush());
    }
    CheckRecords();
    #undef FACTOR
}

TEST_F(RecordioTest, CorruptTest) {
    const uint16_t wrongMagic = 0x6f94;
    // 1. The magic is roiled. No records.
    EXPECT_TRUE(WriteRecord("0123456789"));
    EXPECT_TRUE(FlushAndUpdateSlices());
    RoilMagic(wrongMagic);
    records_.clear();
    CheckCorruptRecords();
    // 2. Duplicated magic number. Skip it.
    EXPECT_TRUE(WriteRecord("0123"));
    EXPECT_TRUE(WriteRecord("45678"));
    EXPECT_TRUE(WriteRecord("9"));
    EXPECT_TRUE(FlushAndUpdateSlices());
    DuplicateMagic();
    CheckCorruptRecords();
    // 3. Wrong magic is ahead. Skip it.
    EXPECT_TRUE(WriteRecord("0123456789"));
    EXPECT_TRUE(FlushAndUpdateSlices());
    WrongMagicAhead(wrongMagic);
    CheckCorruptRecords();
    // 4. Garbage message ahead. Skip it.
    EXPECT_TRUE(WriteRecord("0123456789"));
    EXPECT_TRUE(WriteRecord("0123456789"));
    EXPECT_TRUE(FlushAndUpdateSlices());
    GarbageAhead("abcdefghijklmnopqrstuvwxyz0123456789");
    CheckCorruptRecords();
    // 5. Header checksum is roiled. No records.
    EXPECT_TRUE(WriteRecord("0123456789"));
    EXPECT_TRUE(FlushAndUpdateSlices());
    RoilHeaderChecksum(wrongMagic);
    records_.clear();
    CheckCorruptRecords();
    // 6. Checksum is roiled. No records.
    EXPECT_TRUE(WriteRecord("0123456789"));
    EXPECT_TRUE(FlushAndUpdateSlices());
    RoilChecksum(0x12345678);
    records_.clear();
    CheckCorruptRecords();
}

#undef CheckCorruptRecords
#undef CheckRecords

}  // namespace byte
