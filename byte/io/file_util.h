// Copyright (c) 2018-present, ByteDance Inc. All rights reserved.
#pragma once

#include <stdio.h>

#include <sys/stat.h>
#include <sys/types.h>
#include <fcntl.h>
#include <string>
#include <stack>
#include <vector>
#include "byte/include/status.h"
#include "byte/io/file.h"
#include "byte/io/file_system.h"

namespace byte {

typedef struct stat stat_wrapper_t;

// Generic file enumerator for Posix file system. It enumerates when Next();
class GenericFileEnumerator {
private:
    uint64_t cur_entry_index_;
    std::string root_dir_;
    std::stack<std::string> visiting_paths_;
    std::vector<FileEntry> directory_entries_;
    FileType type_;

public:
    ~GenericFileEnumerator() {}
    explicit GenericFileEnumerator(const std::string& dir, FileType types);
    bool Next(FileEntry* entry);
};

int OpenFlagToOSFlag(OpenFlags flag);

Status IOError(const std::string& context, const std::string& file_name,
               int err_number);

bool IsOpenAppend(const int fd);

bool DirectoryExists(const std::string& dir_name);

Status CreateDirectoryRecursive(const std::string& path);

Status DeleteEmptyDirectory(const std::string& path, bool recursive);
Status DeleteDirectory(const std::string& path, bool recursive);

Status GetFileSizeinBytes(const std::string& path, uint64_t* file_size);

Status GetFileStatInfo(const std::string& path, FileAttributes* attrs);

Status ChangeWorkingDirectory(const std::string& dir_path);

std::string GetCurrentDirectory();

// Set the last access time and modification time.
// Status TouchFile(const std::string& path, const Time& last_accessed,
//               const Time& last_modified);
// Status CopyDirectory(const std::string& from, const std::string& to, bool recursive);
// Status CopyFile(const std::string& from, const std::string& to, bool force);
// Status ReadOneLine(uint64_t length, Slice* result, char* scratch);

}  // namespace byte
