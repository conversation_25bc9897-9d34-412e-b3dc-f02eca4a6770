// Copyright (c) 2018-present, ByteDance Inc. All rights reserved.
#include <unistd.h>
#include <memory>
#include <stack>

#include "byte/io/file_util.h"

#include "byte/include/assert.h"
#include "byte/include/byte_log.h"
#include "byte/include/status.h"
#include "byte/io/file_path.h"
#include "byte/io/local_file.h"
#include "absl/strings/str_split.h"

namespace byte {

bool IsOpenAppend(const int fd) {
    return (fcntl(fd, F_GETFL) & O_APPEND) != 0;
}

int OpenFlagToOSFlag(OpenFlags flag) {
    int osflags = 0;

    // Only enumerate possible combinations without 'FILE_FLAGS_CREATE'
    switch (flag & (0x0F & ~FILE_FLAGS_CREATE)) {
    case FILE_FLAGS_READ:
        osflags = O_RDONLY;
        break;
    case FILE_FLAGS_WRITE:
        osflags = O_WRONLY;
        break;
    case (FILE_FLAGS_READ | FILE_FLAGS_WRITE):
        osflags = O_RDWR;
        break;
    case (FILE_FLAGS_APPEND | FILE_FLAGS_WRITE):
        osflags = O_WRONLY | O_APPEND;
        break;
    case (FILE_FLAGS_READ | FILE_FLAGS_WRITE | FILE_FLAGS_APPEND):
        osflags = O_RDWR | O_APPEND;
        break;
    default:
        osflags = -1;
    }

    if (osflags == -1) {
        return osflags;
    }

    if (flag & FILE_FLAGS_CREATE) {
        osflags |= O_CREAT;
    }

    if (flag & FILE_FLAGS_DIRECT) {
        osflags |= O_DIRECT;
    }

    if (flag & FILE_FLAGS_SYNC) {
        osflags |= O_SYNC;
    }
    return osflags;
}

Status IOError(const std::string& context, const std::string& file_name,
               int err_number) {
    const std::string msg = file_name.empty() ? context : context + ": " + file_name;
    if (err_number == ENOSPC) {
        return Status::NoSpace(msg, strerror(err_number));
    }
    return Status::IOError(msg, strerror(err_number));
}

Status GetFileStatInfo(const std::string& path,  FileAttributes* attrs) {
    stat_wrapper_t file_stat;
    if (lstat(path.c_str(), &file_stat) != 0) {
        if (errno == ENOENT) {
            return Status::NotFound(path);
        }
        return IOError("While stat file ", path, errno);
    }
    attrs->create_time_ = file_stat.st_ctime;
    attrs->modify_time_ = file_stat.st_mtime;
    attrs->access_time_ = file_stat.st_atime;
    attrs->change_time_ = file_stat.st_mtime;

    return Status::OK();
}

bool DirectoryExists(const std::string& path) {
    stat_wrapper_t file_stat;
    if (lstat(path.c_str(), &file_stat) == 0)
        return S_ISDIR(file_stat.st_mode);
    return false;
}

Status GetFileSizeinBytes(const std::string& path, uint64_t* file_size) {
    stat_wrapper_t file_stat;
    if (lstat(path.c_str(), &file_stat) < 0) {
        if (errno == ENOENT) {
            return Status::NotFound(path);
        }
        return IOError("While stat file", path, errno);
    }
    *file_size = file_stat.st_size;
    return Status::OK();
}

Status CreateDirectoryRecursive(const std::string& path) {
    std::vector<std::string> paths = absl::StrSplit(path, absl::ByAnyChar("/\\"));
    std::string dir = "";
    if (!FilePath::IsAbsolute(path)) {
        dir = GetCurrentDirectory();
    }
    bool is_failed = false;
    for (const std::string &e : paths) {
        if (e.empty()) {
            //  absl's feature
            continue;
        }
        dir += '/' + e;
        if (mkdir(dir.c_str(), 0755) == 0) {
            continue;
        }

        if (errno == EEXIST && DirectoryExists(dir)) {
            errno = 0;
            continue;
        }
        is_failed = true;
        break;
    }

    if (!is_failed) {
        return Status::OK();
    }

    return IOError("While mkdir -p ", dir, errno);;
}


GenericFileEnumerator::GenericFileEnumerator(const std::string& root_dir,
                                             FileType type)
    : cur_entry_index_(0), root_dir_(root_dir), type_(type) {
    if (DirectoryExists(root_dir)) {
        visiting_paths_.push(root_dir);
    } else {
        BYTE_ASSERT_TRUE(visiting_paths_.empty());
    }
}

bool GenericFileEnumerator::Next(FileEntry *entry) {
    if (entry == nullptr) {
        return false;
    }

    ++cur_entry_index_;;
    while (cur_entry_index_ >= directory_entries_.size()) {
        if (visiting_paths_.empty()) {
            return false;
        }

        root_dir_ = visiting_paths_.top();
        visiting_paths_.pop();

        std::vector<FileEntry> entries;
        std::unique_ptr<LocalFileIterator> iter;
        iter.reset(new LocalFileIterator(root_dir_, type_, "*"));
        FileEntry current;

        while (iter->GetNext(&current)) {
            entries.push_back(current);
        }

        directory_entries_.clear();
        cur_entry_index_ = 0;
        for (auto i = entries.begin(); i != entries.end(); ++i) {
            std::string full_path = i->name_;
            if (i->type_ == FILE_DIRECTORY) {
                visiting_paths_.push(full_path);
            }
            directory_entries_.push_back(*i);
        }
    }

    entry->type_ = directory_entries_[cur_entry_index_].type_;
    entry->name_ = directory_entries_[cur_entry_index_].name_;
    return true;
}

Status DeleteDirectory(const std::string& root_path, bool recursive) {
    const char* root_path_str = root_path.c_str();

    if (!recursive) {
        if (rmdir(root_path_str) == 0) {
            return Status::OK();
        } else if (errno == ENOENT || errno == ENOTDIR) {
            return Status::NotFound(root_path);
        } else if (errno == ENOTEMPTY) {
            return Status::NotEmpty(root_path);
        }

        return IOError("while rmdir non-empty directory", root_path, errno);
    }

    bool result = true;
    std::stack<std::string> visit_dirs;
    visit_dirs.push(root_path);

    std::unique_ptr<GenericFileEnumerator> gen_iter;
    gen_iter.reset(new GenericFileEnumerator(root_path, FILE_ALL));

    FileEntry entry;
    while (result && gen_iter->Next(&entry)) {
        if (entry.type_ == FILE_DIRECTORY) {
            visit_dirs.push(entry.name_);
        } else {
            if (unlink(entry.name_.c_str()) == 0) {
                result = true;
            } else {
                result = false;
            }
        }
    }

    while (result && visit_dirs.size() > 0) {
        if (rmdir(visit_dirs.top().c_str()) == 0) {
            visit_dirs.pop();
            result = true;
        } else {
            result = false;
        }
    }

    if (result) {
        return Status::OK();
    }
    return IOError("while rmdir directory", root_path, errno);
}

Status DeleteEmptyDirectory(const std::string& path, bool recursive) {
    const char* path_str = path.c_str();
    stat_wrapper_t file_stat;
    int test = lstat(path_str, &file_stat);  // lstat instead of stat.
    if (test != 0) {
        if (errno == ENOENT || errno == ENOTDIR) {
            return Status::OK();
        }
        return IOError("while lstat directory", path, errno);
    }

    if (!recursive) {
        if (rmdir(path_str) == 0) {
            return Status::OK();
        }
        return IOError("while rmdir directory", path, errno);
    }

    bool success = true;
    std::stack<std::string> directories;
    directories.push(path);

    std::unique_ptr<LocalFileIterator> traversal;
    traversal.reset(new LocalFileIterator(path, FILE_DIRECTORY, "*"));

    FileEntry current;
    for (traversal->GetNext(&current); success && !current.type_;
         traversal->GetNext(&current)) {
        if (current.type_ == FILE_DIRECTORY) {
            directories.push(current.name_);
        } else {
            success = (unlink(current.name_.c_str()) == 0);
        }
    }

    while (success && !directories.empty()) {
        std::string dir = directories.top();
        directories.pop();
        success = (rmdir(dir.c_str()) == 0);
    }
    if (success) {
        return Status::OK();
    }
    return IOError("While remove directory", path, errno);
}

std::string GetCurrentDirectory() {
    stat_wrapper_t dir_stat;
    char cwd_buf[4096];
    std::string cwd = getcwd(cwd_buf, sizeof(cwd_buf));
    if (stat(cwd.c_str(), &dir_stat) == 0) {
        if (S_ISDIR(dir_stat.st_mode)) {
            return cwd;
        }
    }
    return "GetCwdFail";
}

Status ChangeWorkingDirectory(const std::string& dir) {
    if (chdir(dir.c_str()) == 0) {
        return Status::OK();
    }
    return IOError("while chdir to ", dir, errno);
}
}  // namespace byte
