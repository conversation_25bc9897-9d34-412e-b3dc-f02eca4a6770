// Copyright (c) 2018-present, ByteDance Inc. All rights reserved.

#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <algorithm>
#include <iostream>
#include <istream>
#include <ostream>
#include "byte/algorithm/bit.h"
#include "byte/algorithm/crc32.h"
#include "byte/encoding/variant_int.h"
#include "byte/include/assert.h"
#include "byte/include/slice.h"
#include "byte/io/file.h"
#include "byte/io/recordio.h"
#include "byte/string/string_piece.h"

namespace byte {

#pragma pack(1)
struct BlockHeader {
    uint16_t magic_;
    uint8_t  type_;
    uint8_t  reserved_;
    char     encoded_data_[0];
};

struct BlockBody {
    uint16_t header_checksum_;
    char     data_[0];
};

struct BlockTailer {
    uint32_t block_checksum_;
};
#pragma pack()

enum BlockType {
    SINGLE_RECORD = 0,
    CONSECUTIVE_RECORDS = 1,
};

static const uint16_t kMagicNumber = 0xdead;
static const uint32_t kInitCrcValue = 0x04c11db7;
static const uint32_t kBufferSize = 64 * 1024U;
static const uint32_t kSizeBufferSize = 4 * 1024U;
static const uint32_t kOverheadSize = 10U;

static inline uint16_t XorCrc32To16(uint32_t crc32) {
    return static_cast<uint16_t>((crc32 >> 16) ^ (crc32 & 0x0000ffffU));
}

struct BlockInfo {
    BlockInfo() {
        buffer = NULL;
        size_buffer = NULL;
        Reset();
    }
    ~BlockInfo() {
        delete[] buffer;
        delete[] size_buffer;
    }

    void Reset() {
        // Rewind buffer.
        type = SINGLE_RECORD;
        ptr = buffer;
        size_buffer_ptr = size_buffer;
        data = NULL;
        size = 0;
        records = 0;
        body_size = 0;
        capacity = 0;
        buffer_end = NULL;
        cur_read_ptr = NULL;
        checksum = kInitCrcValue;
        use_external = true;
    }

    static inline uint32_t GetBufferCapacity(uint32_t size_at_least) {
        static const uint32_t kInitBufferCapacity = 32U;
        if (size_at_least <= kInitBufferCapacity) {
            return kInitBufferCapacity;
        }
        return LeastPowerOfTwo(size_at_least);
    }

    // Transfer data to a larger buffer and fill it.
    void RefillBuffer(uint32_t size_at_least) {
        const uint32_t read_size = buffer_end - ptr;
        const uint32_t offset = cur_read_ptr - ptr;
        capacity = GetBufferCapacity(read_size + size_at_least);
        char* new_buffer = new char[capacity];
        if (read_size > 0) {
            memcpy(new_buffer, ptr, read_size);
        }
        delete[] buffer;
        buffer = new_buffer;
        ptr = buffer;
        buffer_end = buffer + read_size;
        cur_read_ptr = buffer + offset;
    }

    // Rewind ptr to the front of buffer and shift data.
    void RewindBuffer() {
        if (ptr > buffer) {
            const int32_t available_size = buffer_end - ptr;
            const int32_t offset = cur_read_ptr - ptr;
            if (available_size > 0) {
                memmove(buffer, ptr, available_size);
            }
            // Rewind it.
            ptr = buffer;
            buffer_end = buffer + available_size;
            cur_read_ptr = buffer + offset;
        }
    }

    BlockType type;
    char* buffer;
    char* size_buffer;
    // Internally derived.
    bool use_external;
    char* ptr;
    char* size_buffer_ptr;
    const char* data;
    uint32_t size;
    uint32_t records;
    uint32_t body_size;
    uint32_t capacity;
    char* buffer_end;
    char* cur_read_ptr;
    // Block checksum.
    uint32_t checksum;
};

struct AbstractFile {
    enum FileType {
        TYPE_LOCAL_FILE = 0,
        TYPE_INPUT_STREAM = 1,
        TYPE_OUTPUT_STREAM = 2,
        TYPE_FILE_DESCRIPTOR = 3,
        TYPE_FILE = 4,
    };
    union FileInfo {
        FILE* fp;
        std::istream* is;
        std::ostream* os;
        int fd;
        File* file;
    };
    explicit AbstractFile(FILE* local_file) {
        type_ = TYPE_LOCAL_FILE;
        file_.fp = local_file;
    }
    explicit AbstractFile(std::istream* input_stream) {
        type_ = TYPE_INPUT_STREAM;
        file_.is = input_stream;
    }
    explicit AbstractFile(std::ostream* output_stream) {
        type_ = TYPE_OUTPUT_STREAM;
        file_.os = output_stream;
    }
    explicit AbstractFile(int file_descriptor) {
        type_ = TYPE_FILE_DESCRIPTOR;
        file_.fd = file_descriptor;
    }
    explicit AbstractFile(File* file) {
        type_ = TYPE_FILE;
        file_.file = file;
    }

    // Return bytes read from abstract file.
    size_t Read(char* data, size_t size) {
        if (size == 0) {
            return 0U;
        }
        size_t actual_read_size = 0U;
        switch (type_) {
            case TYPE_LOCAL_FILE:
                actual_read_size = fread(data, 1, size, file_.fp);
                break;
            case TYPE_INPUT_STREAM:
                file_.is->read(data, size);
                actual_read_size = file_.is->gcount();
                break;
            case TYPE_OUTPUT_STREAM:
                BYTE_ASSERT(false);
                break;
            case TYPE_FILE_DESCRIPTOR:
                actual_read_size = std::max<int>(read(file_.fd, data, size), 0);
                break;
            case TYPE_FILE: {
                Slice result;
                if (!file_.file->Read(size, &result, data).ok()) {
                    return -1UL;
                }
                actual_read_size = result.size();
                break;
            }
        }
        return actual_read_size;
    }

    bool Write(const char* data, size_t size) {
        if (size == 0) {
            return true;
        }
        switch (type_) {
            case TYPE_LOCAL_FILE:
                return fwrite(data, 1, size, file_.fp) == size;
            case TYPE_INPUT_STREAM:
                break;
            case TYPE_OUTPUT_STREAM:
                file_.os->write(reinterpret_cast<const char*>(data), size);
                return file_.os->good();
            case TYPE_FILE_DESCRIPTOR:
                return write(file_.fd, data, size) == static_cast<int32_t>(size);
            case TYPE_FILE:
                return file_.file->Write(Slice(data, size)).ok();
        }
        BYTE_ASSERT(false);
        return false;
    }

    bool Flush() {
        switch (type_) {
            case TYPE_LOCAL_FILE:
                return fflush(file_.fp) == 0;
            case TYPE_OUTPUT_STREAM:
                file_.os->flush();
                return file_.os->good();
            case TYPE_FILE_DESCRIPTOR:
                return fsync(file_.fd) == 0;
            case TYPE_FILE:
                return file_.file->Flush().ok();
            default:
                break;
        }
        BYTE_ASSERT(false);
        return false;
    }

    FileType type_;
    FileInfo file_;
};

RecordReader::RecordReader(FILE* file, const RecordReaderOptions& options)
    : options_(options) {
    file_ = new AbstractFile(file);
    Initialize();
}

RecordReader::RecordReader(std::istream* stream,
                           const RecordReaderOptions& options)
    : options_(options) {
    file_ = new AbstractFile(stream);
    Initialize();
}

RecordReader::RecordReader(int fd, const RecordReaderOptions& options)
    : options_(options) {
    file_ = new AbstractFile(fd);
    Initialize();
}

RecordReader::RecordReader(File* file, const RecordReaderOptions& options)
    : options_(options) {
    file_ = new AbstractFile(file);
    Initialize();
}

RecordReader::~RecordReader() {
    delete block_;
    delete file_;
}

void RecordReader::Initialize() {
    skipped_bytes_ = 0;
    total_skipped_bytes_ = 0;
    size_data_ = NULL;
    raw_data_ = NULL;
    raw_data_end_ = NULL;
    has_consecutive_zero_ = false;
    reading_consecutive_zero_ = false;
    block_ = new BlockInfo;
}

bool RecordReader::Read(const char** data, size_t* size) {
    skipped_bytes_ = 0;
    uint64_t last_skipped_bytes = total_skipped_bytes_;
    if (raw_data_ >= raw_data_end_ && !reading_consecutive_zero_) {
        const bool ret = ReadBlock();
        skipped_bytes_ = total_skipped_bytes_ - last_skipped_bytes;
        if (!ret) {
            return false;
        }
    }
    if ((options_ & CORRUPTION_SENSITIVE) == CORRUPTION_SENSITIVE) {
        if (IsCorruptDetected()) {
            BYTE_ASSERT(false);
        }
    }
    if (block_->type == SINGLE_RECORD) {
        *data = raw_data_;
        *size = raw_data_end_ - raw_data_;
    } else {
        *data = raw_data_;
        size_t offset = 0;
        if (!VariantInt::Decode<size_t>(
                size_data_,
                size,
                static_cast<size_t>(raw_data_ - size_data_),
                &offset)) {
            return false;
        }
        size_data_ += offset;
        if (has_consecutive_zero_) {
            reading_consecutive_zero_ = (size_data_ < raw_data_);
        }
    }
    raw_data_ += *size;
    return true;
}

bool RecordReader::Read(StringPiece* s) {
    const char* data = NULL;
    size_t size = 0;
    if (!Read(&data, &size)) {
        return false;
    }
    s->set(data, size);
    return true;
}

bool RecordReader::LoadData(uint32_t size) {
    const uint32_t read_size = block_->buffer_end - block_->cur_read_ptr;
    const uint32_t offset = block_->cur_read_ptr - block_->ptr;
    if (size <= read_size) {
        block_->cur_read_ptr += size;
        return true;
    }
    const bool available = (offset + read_size + size <= block_->capacity);
    if (!available) {
        block_->RefillBuffer(size);
    } else {
        block_->RewindBuffer();
    }
    uint32_t actual_read_size = 0;
    uint32_t retry = 0;
    const uint32_t size_to_read = block_->capacity - read_size - offset;
    while (size_to_read > 0 && retry++ < 3 &&
           (actual_read_size = file_->Read(block_->buffer_end, size_to_read)) == 0) {
        usleep(10000);
    }
    block_->buffer_end += actual_read_size;
    const bool reach_the_end = (block_->buffer_end < block_->cur_read_ptr + size);
    block_->cur_read_ptr = reach_the_end ? block_->buffer_end : (block_->cur_read_ptr + size);
    return !reach_the_end;
}

bool RecordReader::ReadBlock() {
    block_->RewindBuffer();
    size_data_ = NULL;
    raw_data_ = NULL;
    raw_data_end_ = NULL;
    has_consecutive_zero_ = false;
    reading_consecutive_zero_ = false;

    while (true) {
        block_->checksum = kInitCrcValue;
        if (!LoadData(kOverheadSize)) {
            break;
        }
        BlockHeader* header = reinterpret_cast<BlockHeader*>(block_->ptr);
        uint32_t valid_bytes = 0;
        bool found_magic = false;
        while (block_->ptr + sizeof(header->magic_) < block_->cur_read_ptr) {
            if (header->magic_ == kMagicNumber) {
                // Matches.
                if (block_->ptr + kOverheadSize > block_->cur_read_ptr) {
                    if (!LoadData(
                        block_->ptr + kOverheadSize - block_->cur_read_ptr)) {
                        break;
                    }
                }
                block_->ptr += sizeof(header->magic_);
                valid_bytes += sizeof(header->magic_);
                found_magic = true;
                break;
            }
            ++block_->ptr;
            header = reinterpret_cast<BlockHeader*>(block_->ptr);
            ++total_skipped_bytes_;
        }
        if (!found_magic) {
            continue;
        }

        if (header->type_ != SINGLE_RECORD &&
            header->type_ != CONSECUTIVE_RECORDS) {
            total_skipped_bytes_ += sizeof(header->magic_);
            continue;
        }
        valid_bytes += sizeof(header->type_);
        block_->type = static_cast<BlockType>(header->type_);

        if (header->reserved_ != 0U) {
            total_skipped_bytes_ += sizeof(header->magic_);
            continue;
        }
        valid_bytes += sizeof(header->reserved_);
        block_->ptr += (sizeof(header->type_) + sizeof(header->reserved_));

        uint32_t body_size = 0U;
        size_t offset = 0;
        if (!VariantInt::Decode<uint32_t>(
                header->encoded_data_,
                &body_size,
                static_cast<size_t>(block_->buffer_end - block_->ptr),
                &offset) ||
            body_size > kMaxRecordSize) {
            total_skipped_bytes_ += sizeof(*header);
            continue;
        }

        block_->body_size = body_size;
        block_->ptr += offset;
        valid_bytes += offset;
        block_->checksum = CRCUtil::ComputeCRC32(block_->checksum,
                                                 block_->ptr - valid_bytes,
                                                 valid_bytes);
        const uint32_t sum_of_header_and_body_overhead =
            sizeof(*header) + offset + sizeof(BlockBody);
        uint32_t remaining_bytes_having_been_read = 0;
        if (sum_of_header_and_body_overhead > kOverheadSize) {
            BYTE_ASSERT(false);
        }
        remaining_bytes_having_been_read = kOverheadSize - sum_of_header_and_body_overhead;
        BlockBody* body = reinterpret_cast<BlockBody*>(block_->ptr);
        if (body->header_checksum_ != XorCrc32To16(block_->checksum)) {
            total_skipped_bytes_ += valid_bytes;
            continue;
        }

        valid_bytes += sizeof(*body);
        block_->ptr += sizeof(*body);

        if (block_->body_size > remaining_bytes_having_been_read) {
            if (!LoadData(block_->body_size - remaining_bytes_having_been_read)) {
                break;
            }
            body = reinterpret_cast<BlockBody*>(block_->ptr - sizeof(*body));
            remaining_bytes_having_been_read = 0;
        } else {
            remaining_bytes_having_been_read -= block_->body_size;
        }

        uint32_t size_buffer_len = 0;
        size_t decoded_length = 0;
        if (block_->type == CONSECUTIVE_RECORDS) {
            if (!VariantInt::Decode<uint32_t>(
                    body->data_,
                    &size_buffer_len,
                    block_->body_size,
                    &decoded_length) ||
                size_buffer_len > block_->body_size) {
                total_skipped_bytes_ += valid_bytes;
                continue;
            }
        }
        block_->checksum = CRCUtil::ComputeCRC32(block_->checksum,
                                                 block_->ptr,
                                                 block_->body_size);
        if (!LoadData(sizeof(BlockTailer) - remaining_bytes_having_been_read)) {
            break;
        }
        BlockTailer* tailer =
            reinterpret_cast<BlockTailer*>(block_->ptr + block_->body_size);
        if (block_->checksum != tailer->block_checksum_) {
            total_skipped_bytes_ += valid_bytes;
            continue;
        }

        if (block_->type == SINGLE_RECORD) {
            raw_data_ = block_->ptr;
            raw_data_end_ = block_->ptr + block_->body_size;
        } else {
            size_data_ = block_->ptr + decoded_length;
            raw_data_ = size_data_ + size_buffer_len;
            raw_data_end_ = block_->ptr + block_->body_size;
            if (raw_data_ == raw_data_end_) {
                has_consecutive_zero_ = true;
            }
        }
        block_->ptr += (block_->body_size + sizeof(*tailer));
        return true;
    }

    if ((options_ & RESUME_LAST_INCOMPLETE_BLOCK)
        != RESUME_LAST_INCOMPLETE_BLOCK) {
        total_skipped_bytes_ += block_->buffer_end - block_->ptr;
        block_->Reset();
        block_->ptr = block_->buffer_end;
    }
    return false;
}

// RecordWriter.

RecordWriter::RecordWriter(FILE* file, const RecordWriterOptions& options)
    : options_(options),
      buffer_size_(kBufferSize),
      size_buffer_size_(kSizeBufferSize) {
    file_ = new AbstractFile(file);
    block_ = new BlockInfo;
    block_->Reset();
}

RecordWriter::RecordWriter(std::ostream* stream,
                           const RecordWriterOptions& options)
    : options_(options),
      buffer_size_(kBufferSize),
      size_buffer_size_(kSizeBufferSize) {
    file_ = new AbstractFile(stream);
    block_ = new BlockInfo;
    block_->Reset();
}

RecordWriter::RecordWriter(int fd, const RecordWriterOptions& options)
    : options_(options),
      buffer_size_(kBufferSize),
      size_buffer_size_(kSizeBufferSize) {
    file_ = new AbstractFile(fd);
    block_ = new BlockInfo;
    block_->Reset();
}

RecordWriter::RecordWriter(File* file, const RecordWriterOptions& options)
    : options_(options),
      buffer_size_(kBufferSize),
      size_buffer_size_(kSizeBufferSize) {
    file_ = new AbstractFile(file);
    block_ = new BlockInfo;
    block_->Reset();
}

RecordWriter::~RecordWriter() {
    Flush();
    delete block_;
    delete file_;
}

void RecordWriter::SetBufferSize(const uint32_t buf_size,
                                 const uint32_t size_buf_size) {
    // This can only be set once.
    BYTE_ASSERT(buffer_size_ == kBufferSize && size_buffer_size_ == kSizeBufferSize);
    BYTE_ASSERT(buf_size <= kMaxRecordSize && size_buf_size <= kMaxRecordSize);
    BYTE_ASSERT(buf_size > 0 && size_buf_size > 0);
    buffer_size_ = buf_size;
    size_buffer_size_ = size_buf_size;
}

bool RecordWriter::Write(const char* data, size_t size) {
    if (data == NULL || size > kMaxRecordSize) {
        return false;
    }
    if (block_->buffer == NULL) {
        block_->buffer = new char[buffer_size_];
        block_->ptr = block_->buffer;
    }
    if (block_->size_buffer == NULL) {
        block_->size_buffer = new char[size_buffer_size_];
        block_->size_buffer_ptr = block_->size_buffer;
    }

    const char* end_ptr = block_->buffer + buffer_size_;
    const char* size_end_ptr = block_->size_buffer + size_buffer_size_;
    block_->data = data;
    block_->size = size;
    if (block_->records > 0 &&
        ((block_->ptr + size) >= end_ptr ||
         (block_->size_buffer_ptr + VariantInt::GetLength<size_t>(size)
          >= size_end_ptr))) {
        block_->type = (block_->records == 1) ? SINGLE_RECORD : CONSECUTIVE_RECORDS;
        block_->use_external = false;
        if (!WriteBlock()) {
            return false;
        }
        block_->data = data;
        block_->size = size;
    }

    block_->size_buffer_ptr += VariantInt::Encode<size_t>(block_->size_buffer_ptr, size);
    ++block_->records;

    if (block_->ptr + size >= end_ptr) {
        block_->use_external = true;
        block_->type = SINGLE_RECORD;
        if (!WriteBlock()) {
            return false;
        }
    } else {
        block_->use_external = false;
        block_->type = (block_->records == 1) ? SINGLE_RECORD : CONSECUTIVE_RECORDS;
        memcpy(block_->ptr, data, size);
        block_->ptr += size;
    }
    return true;
}

bool RecordWriter::Write(const StringPiece& s) {
    return Write(s.data(), s.size());
}

bool RecordWriter::Flush() {
    if (block_->records == 0) {
        return true;
    }
    if (!WriteBlock()) {
        return false;
    }
    return file_->Flush();
}

bool RecordWriter::WriteBlock() {
    const char* data = NULL;
    size_t size = 0;
    size_t body_size = 0;
    const size_t size_buffer_len = block_->size_buffer_ptr - block_->size_buffer;
    if (block_->type == SINGLE_RECORD) {
        if (block_->use_external) {
            data = block_->data;
            size = block_->size;
        } else {
            data = block_->buffer;
            size = block_->ptr - block_->buffer;
        }
        body_size = size;
    } else {
        data = block_->buffer;
        size = block_->ptr - block_->buffer;
        body_size = (VariantInt::GetLength<size_t>(size_buffer_len) + size_buffer_len + size);
    }

    BlockHeader header;
    header.magic_ = kMagicNumber;
    header.type_ = block_->type;
    header.reserved_ = 0U;
    if (!WriteToFile(
            reinterpret_cast<const char*>(&header), sizeof(header)) ||
        !WriteVarintToFile(body_size)) {
        return false;
    }

    uint16_t headerChecksum = XorCrc32To16(block_->checksum);
    if (!WriteChecksum16(headerChecksum)) {
        return false;
    }
    if (block_->type == CONSECUTIVE_RECORDS) {
        if (!WriteVarintToFile(size_buffer_len)) {
            return false;
        }
        if (!WriteToFile(block_->size_buffer, size_buffer_len)) {
            return false;
        }
    }
    if (!WriteToFile(data, size)) {
        return false;
    }
    if (!WriteChecksum32(block_->checksum)) {
        return false;
    }
    block_->Reset();
    return true;
}

bool RecordWriter::WriteToFile(const char* data, size_t size) {
    if (!file_->Write(data, size)) {
        return false;
    }
    block_->checksum = CRCUtil::ComputeCRC32(block_->checksum, data, size);
    return true;
}

bool RecordWriter::WriteChecksum16(uint16_t v) {
    return file_->Write(reinterpret_cast<const char*>(&v), sizeof(v));
}

bool RecordWriter::WriteChecksum32(uint32_t v) {
    return file_->Write(reinterpret_cast<const char*>(&v), sizeof(v));
}

bool RecordWriter::WriteVarintToFile(uint32_t v) {
    char buffer[VariantInt::GetMaxCharsOfEncodedSize<uint32_t>()];
    const size_t size = VariantInt::Encode<uint32_t>(buffer, v);
    return WriteToFile(buffer, size);
}

}  // namespace byte
