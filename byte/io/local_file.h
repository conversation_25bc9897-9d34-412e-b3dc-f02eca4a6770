// Copyright (c) 2018-present, ByteDance Inc. All rights reserved.
#pragma once
#include <dirent.h>
#include <fnmatch.h>
#include <stdio.h>
#include <sys/stat.h>
#include <sys/types.h>

#include <string>
#include <vector>

#include "byte/include/status.h"
#include "byte/io/file.h"

namespace byte {
////////////////////////////////////////////////////////////////////////////////
// Posix file for sequential write
class SequentialWrtFile : public File {
private:
    const std::string sf_name_;
    int sf_fd_;
    uint64_t sf_size_;
    // TODO(miaoyu.01) : For future usage
    // int sf_io_priority_;

public:
    // TODO(miaoyu.01): chunksystem options
    SequentialWrtFile(const std::string& fname, int fd);
    virtual ~SequentialWrtFile();

    Status Write(const Slice& data) override;
    Status Prealloc(uint64_t offset, uint64_t length) override;
    Status Truncate(uint64_t length) override;
    Status FAdvise(uint64_t offset, uint64_t length, int advice) override;
    Status Flush() override;
    Status Sync() override;
    Status Close() override;
    Status GetFileSize(uint64_t* size) override;
    Status GetAttributes(FileAttributes* attrs) const override;

    Status Seek(uint64_t offset) {
        return Status::NotSupported("Sequential write file, no seek support.");
    }
    Status Tell(uint64_t* offset) {
        return Status::NotSupported("Sequential write file, no seek support.");
    }
    Status PRead(uint64_t offset, uint64_t length, Slice* result, char* scratch) {
        return Status::NotSupported("Sequential write file, no pread support.");
    }
    Status Read(uint64_t length, Slice* result, char* scratch) {
        return Status::NotSupported("Sequentialwrite file, no read support.");
    }
    Status PWrite(uint64_t offset, const Slice& data) {
        return Status::NotSupported("Sequentialwrite file, no pwrite support.");
    }
};

////////////////////////////////////////////////////////////////////////////////
// Posix read-only file
class ReadableFile : public File {
private:
    std::string ro_name_;
    int ro_fd_;
    uint64_t ro_offset_;  // Read&Pread both call pread(), so no real offset change.

public:
    ReadableFile(const std::string& fname, int fd);
    virtual ~ReadableFile();

    Status Close() override;
    Status Read(uint64_t length, Slice* result, char* scratch) override;
    Status PRead(uint64_t offset, uint64_t length, Slice* result, char* scratch) override;
    Status Seek(uint64_t offset) override;
    Status Tell(uint64_t* offset) override;
    Status GetFileSize(uint64_t* size) override;
    Status GetAttributes(FileAttributes* attrs) const override;
    Status FAdvise(uint64_t offset, uint64_t length, int advice) override;

    Status Write(const Slice& data) {
        return Status::NotSupported("Read-only file, no write");
    }

    Status PWrite(uint64_t offset, const Slice& data) {
        return Status::NotSupported("Read-only file, no pwrite");
    }

    Status Prealloc(uint64_t offest, uint64_t length) {
        return Status::NotSupported("Read-only file, no pre-allocate");
    }

    Status Truncate(uint64_t length) {
        return Status::NotSupported("Read-only file, no truncate");
    }
    Status Flush() {
        return Status::NotSupported("Read-only file, no flush");
    }

    Status Sync() {
        return Status::NotSupported("Read-only file, no sync");
    }
};

////////////////////////////////////////////////////////////////////////////////
// Posix file for random read & sequential write
class RandomRWFile : public File {
private:
    const std::string rf_name_;
    int rf_fd_;
    // TODO(miaoyu.01) : For future usage
    // int sf_io_priority_;


public:
    RandomRWFile(const std::string& fname, int fd);
    virtual ~RandomRWFile();

    Status PRead(uint64_t offset, uint64_t length, Slice* result, char* scratch) override;
    Status PWrite(uint64_t offset, const Slice& data) override;
    Status Prealloc(uint64_t offset, uint64_t length) override;
    Status Truncate(uint64_t length) override;
    Status Sync() override;
    Status Close() override;
    Status GetFileSize(uint64_t* size) override;
    Status GetAttributes(FileAttributes* attrs) const override;
    Status FAdvise(uint64_t offset, uint64_t length, int advice) override;

    Status Read(uint64_t length, Slice* result, char* scratch) override {
        return Status::NotSupported("Random rdwr file, no read support; Use pread please.");
    }

    Status Write(const Slice& data) override {
        return Status::NotSupported("Random rdwr file, no write support; Use pwrite please.");
    }

    Status Seek(uint64_t offset) {
        return Status::NotSupported("Random rdwr file, no seek support.");
    }
    Status Tell(uint64_t* offset) {
        return Status::NotSupported("Random rdwr file, no tell support.");
    }
    Status Flush() {
        return Status::NotSupported("Random rdwr file, no flush support; Use sync please.");
    }
};

///////////////////////////////////////////////////////////////////////////////
//  Posix file iterator
//  Please note that this iterator is implemented for Posix Chunksystem only; if you want
//  to iterate your directory as in normal semantics, please turn to GenericFileEnumerator
//  in file_util.h
class LocalFileIterator : public FileIterator {
private:
    std::vector<FileEntry> dir_entries_;
    uint64_t dir_entries_count_;  // Should be equal to dir_entries.size();
    uint64_t dir_entry_current_;
    std::string root_dir_;
    FileType file_types_;
    std::string pattern_;

public:
    ~LocalFileIterator() {}
    explicit LocalFileIterator(const std::string& dir, FileType types,
                               const std::string& pattern);
    bool GetNext(FileEntry* entry) override;
    bool Seek(const FileEntry& target_entry) override;
    void SeekToFirst() { dir_entry_current_ = 0; }
};
}  // namespace byte
