// Copyright (c) 2024-present, ByteDance Inc. All rights reserved.

#pragma once

#include <cstddef>
#include <cstdint>
#include <string>
#include <vector>

#include "byte/shm_manager/shm_segment.h"

namespace byte {

// Function to force delete system v shared memroy by segment name, manager index and
// ShmSegmentConfig.
ShmErrorCode ForceDeleteSystemVShm(const std::string& segment_name, const int16_t manager_index,
                                   const ShmSegmentConfig& config);

// SystemVSegment class that inherits from ShmSegment
class SystemVSegment : public ShmSegment {
public:
    SystemVSegment() = default;
    ~SystemVSegment() override = default;

    /**
     * @brief Initialize the segment with the given config.
     *
     * @param segment_name The name of the segment.
     * @param config The configuration settings for the segment.
     * @return ShmErrorCode Error code indicating the status of the operation.
     *
     * NOTE:
     * - If config.manager_index < -1, system key generation falls back to using
     *   config.segment_index as the project ID in the ftok function,
     *   which may cause hash collisions.
     * - If config.manager_index >= -1, valid manager index range is [-1, 4095]
     *   and valid segment index range is [-1, 4095].
     *   The customized function GenSystemVUniqKeyByIndex ensures no hash collisions.
     */
    ShmErrorCode InitSegment(const std::string& segment_name,
                             const ShmSegmentConfig& config) override;

    /**
     * @brief Get the configuration of the segment (read-only).
     *
     * @return SegmentConfig Configuration of the segment.
     */
    const ShmSegmentConfig& GetSegmentConfig() const override;

    /**
     * @brief Get the context of the segment (read-only).
     *
     * @return SegmentContext Context of the segment.
     */
    const ShmSegmentContext& GetSegmentContext() const override;

    /**
     * @brief Shutdown the segment.
     *
     * @return ShmErrorCode Error code indicating the status of the operation.
     */
    ShmErrorCode ShutdownSegment() override;

    /**
     * @brief Delete the segment.
     *
     * @return ShmErrorCode Error code indicating the status of the operation.
     */
    ShmErrorCode DeleteSegment() override;

private:
    // System V shared memory ID
    int systemv_shmid_ = 0;
};
}  // namespace byte
