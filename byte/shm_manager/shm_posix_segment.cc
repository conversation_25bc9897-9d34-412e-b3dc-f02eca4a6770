// Copyright (c) 2024-present, ByteDance Inc. All rights reserved.

#include "byte/shm_manager/shm_posix_segment.h"

#include <fcntl.h>
#include <sys/mman.h>
#include <unistd.h>

#include "byte/include/byte_log.h"
#include "byte/io/file_path.h"
#include "byte/io/file_util.h"
#include "byte/shm_manager/shm_common.h"

namespace byte {

ShmErrorCode ForceDeletePosixShm(const std::string& segment_name) {
    int ret = unlink(segment_name.c_str());
    if (ret == -1 && errno != ENOENT) {
        LOG(ERROR) << "ForceDeletePosixShm: Failed to delete shared memory: " << strerror(errno)
                   << " : " << errno << ". name: " << segment_name;
        return ShmErrorCode::kErrOther;
    }
    Status delete_dir_ret = DeleteDirectory(FilePath::GetFileDir(segment_name), /*recursive=*/true);
    if (!delete_dir_ret.ok() && errno != ENOENT) {
        LOG(ERROR) << "DeleteSystemVShm: Failed to delete directory: " << strerror(errno) << " : "
                   << errno << ". name: " << segment_name;
    }
    LOG(DEBUG) << "ForceDeletePosixShm: Deleting the segment: " << segment_name;
    return ShmErrorCode::kOk;
}

// Initalize the segment with the specified context
ShmErrorCode PosixSegment::InitSegment(const std::string& segment_name,
                                       const ShmSegmentConfig& config) {
    if (inited_ == true) {
        LOG(ERROR) << "SystemVSegment::InitSegment: Segment has been initialized. name: "
                   << segment_name_ << ". config flags: " << config_.shm_segment_flags;
        return ShmErrorCode::kErrReInit;
    }
    config_ = config;
    segment_name_ = segment_name;

    if (config_.shm_segment_flags >= shm_flags::kInvalid) {
        LOG(ERROR) << "PosixSegment::InitSegment: name: " << segment_name_
                   << ". config flags: " << config_.shm_segment_flags;
        return ShmErrorCode::kErrParam;
    }

    std::string file_dir = FilePath::GetFileDir(segment_name_);
    // If the path does not exist and the creation flag is set, create the path.
    // And if the path does not exist and the creation flag is not set, return an error.
    if (!DirectoryExists(file_dir)) {
        if ((config_.shm_segment_flags & shm_flags::kCreate) == 0) {
            LOG(ERROR) << "SystemVSegment::InitSegment: Failed to read a non-existed segment: "
                       << strerror(errno) << " : " << errno << ". name: " << segment_name_
                       << ". config flags: " << config_.shm_segment_flags;
            return ShmErrorCode::kErrNoExist;
        }
        if (!CreateDirectoryRecursive(file_dir).ok()) {
            LOG(ERROR) << "SystemVSegment::InitSegment: Failed to create directory: "
                       << strerror(errno) << " : " << errno << ". name: " << segment_name_
                       << ". config flags: " << config_.shm_segment_flags
                       << "Deleting the segment...";
            return ShmErrorCode::kErrOther;
        }
    }

    int oflag = 0;
    int protflag = 0;
    int mapflags = MAP_SHARED;
    mode_t mode = 0;

    // Create new segment if kCreate flag is set
    if (config_.shm_segment_flags & shm_flags::kCreate) {
        oflag |= O_CREAT;
    }

    // Exclusive access if kExcl flag is set
    if (config_.shm_segment_flags & shm_flags::kExcl) {
        oflag |= O_EXCL;
    }

    // Read-only permission if kRdOnly flag is set
    if (config.shm_segment_flags & shm_flags::kRdOnly) {
        oflag |= O_RDONLY;
        protflag |= PROT_READ;
        // (U)ser / owner can read, can't write and can't execute.
        // (G)roup can read, can't write and can't execute.
        // (O)thers can read, can't write and can't execute.
        mode |= 0444;
    }

    // Write-only permission if kWrOnly flag is set
    if (config_.shm_segment_flags & shm_flags::kWrOnly) {
        oflag |= O_RDWR;
        protflag |= PROT_WRITE;
        // (U)ser / owner can write, can't read and can't execute.
        // (G)roup can't read, can't write and can't execute.
        // (O)thers can't read, can't write and can't execute.
        mode |= 0200;
    }

    // Read-write permission if kRdWr flag is set
    if (config_.shm_segment_flags & shm_flags::kRdWr) {
        oflag |= O_RDWR;
        protflag |= PROT_READ | PROT_WRITE;
        // (U)ser / owner can write and read, and can't execute.
        // (G)roup can read, can't write and can't execute.
        // (O)thers can read, can't write and can't execute.
        mode |= 0644;
    }

    // sysdeps/posix/shm_open.c
    oflag |= O_NOFOLLOW | O_CLOEXEC;

    /* Disable asynchronous cancellation.  */
    int state = 0;
    pthread_setcancelstate(PTHREAD_CANCEL_DISABLE, &state);

    fd_ = open(segment_name_.c_str(), oflag, mode);

    pthread_setcancelstate(state, NULL);

    if (fd_ == -1) {
        LOG(ERROR) << "PosixSegment::InitSegment: Failed to open segment: " << strerror(errno)
                   << " : " << errno << ". name: " << segment_name_
                   << ". config flags: " << config_.shm_segment_flags
                   << ". size: " << config_.segment_size << ". fd: " << fd_ << ". oflag: " << oflag
                   << ". protflag: " << protflag << ". mode: " << mode;
        if (errno == ENOENT) {
            return ShmErrorCode::kErrNoExist;
        } else {
            return ShmErrorCode::kErrOther;
        }
    }

    struct stat st = {};
    int ret = fstat(fd_, &st);
    if (ret == -1) {
        LOG(ERROR) << "PosixSegment::InitSegment: Failed to get segment size: " << strerror(errno)
                   << " : " << errno << ". name: " << segment_name_
                   << ". config flags: " << config_.shm_segment_flags
                   << ". size: " << config_.segment_size << ". fd: " << fd_ << ". oflag: " << oflag
                   << ". protflag: " << protflag << ". mode: " << mode;
        ForceDeletePosixShm(segment_name_);
        return ShmErrorCode::kErrOther;
    }

    if (st.st_size != 0 || config_.segment_size == 0) {
        if (config_.segment_size != 0 && st.st_size != static_cast<__off_t>(config_.segment_size)) {
            LOG(WARNING) << "PosixSegment::InitSegment: segment size diff - config segment_size: "
                         << config_.segment_size << " vs st segment size: " << st.st_size
                         << ". name: " << segment_name_
                         << ". config flags: " << config_.shm_segment_flags
                         << ". size: " << config_.segment_size << ". fd: " << fd_
                         << ". oflag: " << oflag << ". protflag: " << protflag << ". mode: " << mode
                         << ". Using segment size from existed posix shared memory...";
        }
        context_.segment_size = st.st_size;
    } else if (config_.segment_size != 0) {
        int ret = ftruncate(fd_, config_.segment_size);

        if (ret == -1) {
            LOG(ERROR) << "PosixSegment::InitSegment: Failed to truncate segment: "
                       << strerror(errno) << " : " << errno << ". name: " << segment_name_
                       << ". config flags: " << config_.shm_segment_flags
                       << ". size: " << config_.segment_size << ". size: " << config_.segment_size
                       << ". fd: " << fd_ << ". oflag: " << oflag << ". protflag: " << protflag
                       << ". mode: " << mode;
            ForceDeletePosixShm(segment_name_);
            return ShmErrorCode::kErrOther;
        }
        context_.segment_size = config_.segment_size;
    }

    if (config_.shm_segment_flags & shm_flags::kHugePage) {
        mapflags |= MAP_HUGETLB;
    }

    context_.address = mmap(NULL, context_.segment_size, protflag, mapflags, fd_, 0);

    if (context_.address == MAP_FAILED) {
        LOG(ERROR) << "PosixSegment::InitSegment: Failed to map segment: " << strerror(errno)
                   << " : " << errno << ". name: " << segment_name_
                   << ". config flags: " << config_.shm_segment_flags
                   << ". size: " << config_.segment_size << ". fd: " << fd_ << ". oflag: " << oflag
                   << ". protflag: " << protflag << ". mode: " << mode;
        ForceDeletePosixShm(segment_name_);
        return ShmErrorCode::kErrOther;
    }
    context_.segment_index = config_.segment_index;
    context_.status = shm_status::kInUse;

    inited_ = true;
    LOG(DEBUG) << "PosixSegment::InitSegment: Init segment success. name: " << segment_name_
               << ". config flags: " << config_.shm_segment_flags
               << ". size: " << config_.segment_size << ". fd: " << fd_ << ". oflag: " << oflag
               << ". protflag: " << protflag << ". mode: " << mode;
    return ShmErrorCode::kOk;
}

// Get the configuration of the segment (read-only)
const ShmSegmentConfig& PosixSegment::GetSegmentConfig() const { return config_; }

// Get the context of the segment (read-only)
const ShmSegmentContext& PosixSegment::GetSegmentContext() const { return context_; }

// Shutdown the segment
ShmErrorCode PosixSegment::ShutdownSegment() {
    if (context_.status != shm_status::kInUse) {
        LOG(ERROR) << "PosixSegment::ShutdownSegment: Failed to shutdown shared memory: "
                   << "segment is not in use. name: " << segment_name_
                   << ". flags: " << config_.shm_segment_flags
                   << ". size: " << config_.segment_size;
        return ShmErrorCode::kErrClose;
    }
    int unmap_ret = munmap(context_.address, context_.segment_size);
    if (unmap_ret == -1) {
        LOG(ERROR) << "PosixSegment::ShutdownSegment: Failed to shutdown shared memory: "
                   << strerror(errno) << " : " << errno << ". name: " << segment_name_
                   << ". flags: " << config_.shm_segment_flags
                   << ". size: " << config_.segment_size;
        return ShmErrorCode::kErrOther;
    }
    int close_ret = close(fd_);
    if (close_ret == -1) {
        LOG(ERROR) << "PosixSegment::ShutdownSegment: Failed to shutdown shared memory: "
                   << strerror(errno) << " : " << errno << ". name: " << segment_name_
                   << ". flags: " << config_.shm_segment_flags
                   << ". size: " << config_.segment_size;
        return ShmErrorCode::kErrOther;
    }
    fd_ = -1;
    context_.address = nullptr;
    context_.status = shm_status::kClosed;
    return ShmErrorCode::kOk;
}

// Delete the segment
ShmErrorCode PosixSegment::DeleteSegment() {
    if (context_.status != shm_status::kClosed) {
        LOG(ERROR) << "SystemVSegment::DeleteSegment: Failed to delete shared memory: "
                   << "segment is not closed. name: " << segment_name_
                   << ". flags: " << config_.shm_segment_flags
                   << ". size: " << config_.segment_size;
        return ShmErrorCode::kErrDelete;
    }
    ShmErrorCode ret = ForceDeletePosixShm(segment_name_);
    inited_ = false;
    context_ = ShmSegmentContext();
    return ret;
}
}  // namespace byte
