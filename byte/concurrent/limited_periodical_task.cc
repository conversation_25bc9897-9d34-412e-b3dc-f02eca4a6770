// Copyright (c) 2018-present, ByteDance Inc. All rights reserved.

#include "byte/concurrent/limited_periodical_task.h"
#include "byte/include/byte_log.h"

namespace byte {
void LimitedPeriodicalTask::ScheduleTask() {
    if (++loop_ >= max_loop_) {
        // Max loop reached.
        LOG(DEBUG) << "LimitedLoopTask::ScheduleTask: " << this
                   << ", loop_=" << loop_.Value()
                   << ", max_loop_=" << max_loop_;
        PeriodicalTask::Complete(0);
    } else {
        PeriodicalTask::ScheduleTask();
    }
}
}  // namespace byte
