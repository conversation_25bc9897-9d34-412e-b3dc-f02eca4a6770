// Copyright (c) 2013, The Toft Authors.
// All rights reserved.
//
// Author: <PERSON><PERSON> <<EMAIL>>

#include "byte/concurrent/spinlock.h"
#include "byte/include/assert.h"
#include "byte/thread/this_thread.h"

namespace byte {

SpinLock::SpinLock() {
    BYTE_ASSERT_ERRNO(pthread_spin_init(&lock_, 0));
    owner_ = 0;
}

SpinLock::~SpinLock() {
    BYTE_ASSERT_ERRNO(pthread_spin_destroy(&lock_));
    owner_ = -1;
}

void SpinLock::Lock() {
    BYTE_ASSERT_ERRNO(pthread_spin_lock(&lock_));
    owner_ = ThisThread::GetId();
}

bool SpinLock::TryLock() {
    const int error = pthread_spin_trylock(&lock_);
    CHECK_TRYLOCK_ERROR(error);
    owner_ = ThisThread::GetId();
    return true;
}

void SpinLock::Unlock() {
    owner_ = 0;
    BYTE_ASSERT_ERRNO(pthread_spin_unlock(&lock_));
}

}  // namespace byte
