// Copyright (c) 2018-present, ByteDance Inc. All rights reserved.

#include "byte/concurrent/timer_manager.h"

#include "byte/base/closure.h"
#include "byte/include/byte_log.h"
#include "byte/thread/base_thread.h"
#include "byte/thread/this_thread.h"
#include "gtest/gtest.h"

namespace {
/*
static int n1 = 0;
static int n2 = 0;
static int n3 = 0;
static int n4 = 0;
static int n5 = 0;

byte::TimerManager timer_manager;

void EchoCallBack(int *count, uint64_t timer_id) {
    (*count)++;
    LOG(INFO) << "echo callback, timer: " <<
        timer_id << ", count: " << *count;
}

void RemoveSelf(int *count, uint64_t timer_id) {
    (*count)++;
    LOG(INFO) << "remove self, timer: " << timer_id
        << ", count: " << *count;
    timer_manager.RemoveTimer(timer_id);
}

void GetTimerManagerInfo() {
    byte::TimerManager::Stats info;
    timer_manager.GetStats(&info);
    LOG(INFO) << "--------------------";
    LOG(INFO) << "oneshot: " << info.oneshot_timer_num;
    LOG(INFO) << "period:  " << info.period_timer_num;
    LOG(INFO) << "runover: " << info.estimate_runover_time;
    LOG(INFO) << "--------------------";
}
*/
}  // namespace

namespace byte {

TEST(Timer, EchoTimerTest) {
    // FIXME(dongchengyu): Failed with valgrind.
    /*
    Closure<void, uint64_t>* pcb1 = NewPermanentClosure(EchoCallBack, &n1);
    uint64_t id1 = timer_manager.AddPeriodTimer(500, pcb1);
    Closure<void, uint64_t>* pcb2 = NewClosure(EchoCallBack, &n2);
    timer_manager.AddOneshotTimer(14, pcb2);
    Closure<void, uint64_t>* pcb3 = NewPermanentClosure(EchoCallBack, &n3);
    uint64_t id3 = timer_manager.AddPeriodTimer(20, pcb3);
    Closure<void, uint64_t>* pcb4 = NewClosure(EchoCallBack, &n4);
    uint64_t id4 = timer_manager.AddOneshotTimer(1000000000000LL, pcb4);
    ThisThread::SleepInUs(1000);
    GetTimerManagerInfo();
    timer_manager.ModifyTimer(id3, 200);
    ThisThread::SleepInUs(1000);
    LOG(INFO) << "timer 3 will be modified to self delete:";
    pcb3 = NewPermanentClosure(RemoveSelf, &n3);
    timer_manager.ModifyTimer(id3, 200, pcb3);
    LOG(INFO) << "timer 4 will be modified to self delete:";
    pcb4 = NewClosure(RemoveSelf, &n4);
    timer_manager.ModifyTimer(id4, 1000, pcb4);
    ThisThread::SleepInUs(1000);
    GetTimerManagerInfo();
    Closure<void, uint64_t>* pcb5 = NewClosure(EchoCallBack, &n5);
    timer_manager.AddOneshotTimer(100000000LL, pcb5);
    timer_manager.RemoveTimer(id1);
    GetTimerManagerInfo();
    */
}

}  // namespace byte
