# concurrent 模块

## `blockingqueue.h`：阻塞式队列

## `cond.h`：条件变量
条件变量，对pthread库里面的cond做了c++封装，常用的接口有`Signal`和`Wait`。还实现了一个定时等待`TimedWait`。
```c++
void Signal();
void Broadcast();
void Wait();
bool TimedWait(int64_t timeout_in_ms);
```

## `event.h/.cc`：事件触发器

## `hashtable.h`：线程安全的hash表

## `lite_lock.h/.cc`：轻量级锁，用户态锁实现
思路来自于Facebook的folly，轻量级用户态锁，在特别轻量级加锁实现中，可以使用起来。
这里面有好多个类，都以`Lite`开头：`LiteLock`、`LiteReentrantLock`、`LiteRWLock`、`LiteRWLockPreferWrite`、`LiteLockVector`、`LiteRWLockRefCount`，`ScopedLiteLocker`系列等;
都有类似如下的接口
```c++
void Lock();
bool TryLock();
void UnLock();
bool IsLocked() const;
bool TimedLock(uint64_t timeout_in_us);
```

该 `lite` 系列将`mutex`、`spinlock`、`scoped_lock`、`rwlock`、`scoped_locker`等等都做了用户态的实现。
用户态锁在冲突的时候使用的是忙等的策略，因此只能保护轻量级内存操作，不能用于IO、网络等待。最好保护的临界区里面不要出现循环，或者很重的查找操作。

```c++
// impl of busy wait
 do {
     while (IsLocked()) {
         ThisThread::Yield();
     }
 } while (!AtomicCompareExchange(&lock, kLockOff, target_bits));
```


## `mutex.h/.cc`：内核mutex
互斥锁实现

## `rwlock.h/.cc`：内核读写锁
实现了读写锁的操作

## `scoped_locker.h`：范围锁
自动保护了锁的范围，用户不用在每个作用域的出口，显式的写上`UnLock`。`scoped_locker`析构的时候自动将锁释放。
```c++
{
    ScopedWriterLocker l(&locker_type);
    // ...
 }  // release the lock on exit
```

## `spinlock.h/.cc`：内核自旋锁
和`lite_lock`类似，冲突时进入盲等的操作
