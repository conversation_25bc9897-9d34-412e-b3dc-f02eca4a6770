// Copyright (c) 2018-present, ByteDance Inc. All rights reserved.

// GLOBAL_NOLINT(runtime/explicit)

#pragma once

#include <stdint.h>
#include <atomic>
#include <vector>

#include "byte/base/ref_count.h"

namespace byte {

class LiteLock {
public:
    LiteLock() : lock_(kLockOff) {}

    virtual ~LiteLock();

    void Lock();

    bool TryLock();

    void UnLock();

    bool IsLocked() const {
        return Payload()->load() != kLockOff;
    }

    bool TimedLock(uint64_t timeout_in_us);

    int GetLockOwner() const;

private:
    bool CAS(uint64_t compare, uint64_t new_val);

    std::atomic<uint64_t>* Payload() const {
        return reinterpret_cast<std::atomic<uint64_t>*>(&this->lock_);
    }

    static const uint64_t kLockOn = 1;
    static const uint64_t kLockOff = 0;
    static const uint64_t kLockInvalid = -1UL;
    mutable uint64_t lock_;

    DISALLOW_COPY_AND_ASSIGN(LiteLock);
};

class LiteReentrantLock {
public:
    LiteReentrantLock() : bits_(0) {}

    void Lock();

    void UnLock();

private:
    static const uint64_t kMask = ~0UL >> 32;

    bool TryLock(uint64_t me, uint64_t owner);

    // 32bit owner tid | 32bit refCount
    uint64_t bits_;

    DISALLOW_COPY_AND_ASSIGN(LiteReentrantLock);
};

// Non-fair read-write lock. Prefer read.
class LiteRWLock {
public:
    LiteRWLock() : bits_(0U) {}

    void ReadLock();

    void ReadUnLock();

    void WriteLock();

    void WriteUnLock();

    bool IsReadLocked() const;

    bool IsWriteLocked() const;

    bool IsLocked() const;

    bool TryReadLock();

    bool TryWriteLock();

    bool TimedReadLock(uint64_t timeout_in_us);

    bool TimedWriteLock(uint64_t timeout_in_us);

    int GetWriteLockOwner() const;

    LiteRWLock(LiteRWLock&& lock) = default;

    LiteRWLock& operator=(LiteRWLock&& lock) noexcept = default;

private:
    // uint32_t writerOwnerId | uint32_t readerCount
    static const uint64_t kReaderMask = 0x00000000FFFFFFFFULL;
    static const uint64_t kWriterMask = 0xFFFFFFFF00000000ULL;
    uint64_t bits_;

    DISALLOW_COPY_AND_ASSIGN(LiteRWLock);
};

class LiteRWLockPreferWrite {
public:
    LiteRWLockPreferWrite() : bits_(0U) {}

    void ReadLock();

    void ReadUnLock();

    void WriteLock();

    void WriteUnLock();

    bool IsReadLocked() const;

    bool IsWriteLocked() const;

    bool IsLocked() const;

    bool TimedReadLock(uint64_t timeout_in_us);

    bool TimedWriteLock(uint64_t timeout_in_us);

    int GetWriteLockOwner() const;

private:
    // 32bit writerOwnerId | 32bit readerCount
    static const uint64_t kWriterMask = 0xFFFFFFFF00000000ULL;
    static const uint64_t kReaderMask = 0x00000000FFFFFFFFULL;
    uint64_t bits_;

    DISALLOW_COPY_AND_ASSIGN(LiteRWLockPreferWrite);
};

class ScopedLiteLock {
public:
    explicit ScopedLiteLock(LiteLock* lock);

    // unlock when destruct
    ~ScopedLiteLock();

private:
    LiteLock* device_;

    DISALLOW_COPY_AND_ASSIGN(ScopedLiteLock);
};

class ScopedLiteReentrantLock {
public:
    explicit ScopedLiteReentrantLock(LiteReentrantLock* lock);

    // unlock when destruct
    ~ScopedLiteReentrantLock();

private:
    LiteReentrantLock* device_;

    DISALLOW_COPY_AND_ASSIGN(ScopedLiteReentrantLock);
};

class ScopedLiteRWLock {
public:
    // Use 'r' or 'w' as read/write mode.
    ScopedLiteRWLock(LiteRWLock* lock, const char mode);

    // Can only be called once for read locker, return false otherwise
    bool UpgradeToWriterLock();

    // unlock when destruct
    ~ScopedLiteRWLock();

private:
    LiteRWLock* device_;
    char mode_;
    bool upgraded_;

    DISALLOW_COPY_AND_ASSIGN(ScopedLiteRWLock);
};

class ScopedLiteRWLockPreferWrite {
public:
    // Use 'r' or 'w' as read/write mode.
    ScopedLiteRWLockPreferWrite(
            LiteRWLockPreferWrite* lock, const char mode);

    // Can only be called once for read locker, return false otherwise
    bool UpgradeToWriterLock();

    // unlock when destruct
    ~ScopedLiteRWLockPreferWrite();

private:
    LiteRWLockPreferWrite* device_;
    char mode_;
    bool upgraded_;

    DISALLOW_COPY_AND_ASSIGN(ScopedLiteRWLockPreferWrite);
};

class LiteLockVector {
public:
    explicit LiteLockVector(uint32_t size);

    void Lock(uint32_t index);

    bool TryLock(uint32_t index);

    void UnLock(uint32_t index);

    bool IsLocked(uint32_t index) const;

private:
    inline uint32_t Mask(uint32_t index) const { return 1UL << (index & 0x1f); }

    std::vector<uint32_t> bits_;

    DISALLOW_COPY_AND_ASSIGN(LiteLockVector);
};

class LiteRWLockRefCount
    : public LiteRWLock,
      public RefCount<LiteRWLockRefCount> {
public:
    enum LockType {
        READ_LOCK = 0,
        WRITE_LOCK = 1,
        RELEASE = 2,
    };

    explicit LiteRWLockRefCount(LockType type);

    ~LiteRWLockRefCount() {}

    void ReadLock();

    void ReadUnLock();

    void WriteLock();

    void WriteUnLock();

private:
    DISALLOW_COPY_AND_ASSIGN(LiteRWLockRefCount);
};

}  // namespace byte
