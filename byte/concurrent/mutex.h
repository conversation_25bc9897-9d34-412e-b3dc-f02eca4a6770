// Copyright (c) 2018-present, ByteDance Inc. All rights reserved.

// This file is modified from toft:
// https://github.com/chen3feng/toft/

// Copyright (c) 2011, The Toft Authors.
// All rights reserved.
// Author: <PERSON><PERSON> <PERSON> <<EMAIL>>

#pragma once

#include <string.h>
#include <stdexcept>
#include <string>

#include "byte/concurrent/scoped_locker.h"
#include "byte/include/assert.h"
#include "byte/include/macros.h"

namespace byte {

class ConditionVariable;

class MutexBase {
public:
    void Lock() {
        BYTE_ASSERT_ERRNO(pthread_mutex_lock(&mutex_));
        AssertLocked();
    }
    bool TryLock() {
        const int error = pthread_mutex_trylock(&mutex_);
        CHECK_TRYLOCK_ERROR(error);
        return true;
    }
    bool IsLocked() const {
        return mutex_.__data.__lock > 0;
    }
    void Unlock() {
        AssertLocked();
        BYTE_ASSERT_ERRNO(pthread_mutex_unlock(&mutex_));
    }

protected:
    explicit MutexBase(int type);
    ~MutexBase();

private:
    friend class ConditionVariable;

    void AssertLocked() const {
        BYTE_ASSERT(IsLocked());
    }

    mutable pthread_mutex_t mutex_;

    DISALLOW_COPY_AND_ASSIGN(MutexBase);
};


typedef ScopedLocker<MutexBase> MutexLocker;

// if same thread try to acquire the lock twice, deadlock would occur.
class Mutex : public MutexBase {
public:
    typedef ScopedLocker<Mutex> Locker;
    Mutex() : MutexBase(PTHREAD_MUTEX_DEFAULT) {}
};

// RecursiveMutex can be acquired by same thread multiple times, but slower than
// plain Mutex
class RecursiveMutex : public MutexBase {
public:
    typedef ScopedLocker<RecursiveMutex> Locker;
    RecursiveMutex() : MutexBase(PTHREAD_MUTEX_RECURSIVE) {}
};

// try to spin some time if can't acquire lock, if still can't acquire, wait.
class AdaptiveMutex : public MutexBase {
public:
    typedef ScopedLocker<AdaptiveMutex> Locker;
    AdaptiveMutex() : MutexBase(PTHREAD_MUTEX_ADAPTIVE_NP) {}
};

}  // namespace byte
