// Copyright (c) 2011, The Toft Authors.
// All rights reserved.
// Author: <PERSON><PERSON> <PERSON> <<EMAIL>>

#pragma once

#include "byte/include/macros.h"

#define CHECK_TRYLOCK_ERROR(e) \
    do { \
        if (UNLIKELY(e == EBUSY || e == EAGAIN)) { \
            return false; \
        } \
        BYTE_ASSERT_ERRNO(e); \
    } while (false)

namespace byte {
// Scoped locker, is just like scoped pointer
template <typename LockType>
class ScopedLocker {
public:
    explicit ScopedLocker(LockType* lock)
        : lock_(lock) {
        lock_->Lock();
    }
    ~ScopedLocker() {
        lock_->Unlock();
    }

private:
    LockType* lock_;

    DISALLOW_COPY_AND_ASSIGN(ScopedLocker);
};

template <typename LockType>
class ScopedTryLocker {
public:
    explicit ScopedTryLocker(LockType* lock)
        : lock_(lock) {
        locked_ = lock_->TryLock();
    }
    ~ScopedTryLocker() {
        if (locked_) {
            lock_->Unlock();
        }
    }
    bool IsLocked() const {
        return locked_;
    }

private:
    LockType* lock_;
    bool locked_;

    DISALLOW_COPY_AND_ASSIGN(ScopedTryLocker);
};

template <typename LockType>
class ScopedReaderLocker {
public:
    explicit ScopedReaderLocker(LockType* lock)
        : lock_(lock) {
        lock_->ReaderLock();
    }
    ~ScopedReaderLocker() {
        lock_->ReaderUnlock();
    }

private:
    LockType* lock_;

    DISALLOW_COPY_AND_ASSIGN(ScopedReaderLocker);
};

template <typename LockType>
class ScopedTryReaderLocker {
public:
    explicit ScopedTryReaderLocker(LockType* lock)
        : lock_(lock) {
        locked_ = lock_->TryReaderLock();
    }
    ~ScopedTryReaderLocker() {
        if (locked_) {
            lock_->ReaderUnlock();
        }
    }
    bool IsLocked() const {
        return locked_;
    }

private:
    LockType* lock_;
    bool locked_;

    DISALLOW_COPY_AND_ASSIGN(ScopedTryReaderLocker);
};

template <typename LockType>
class ScopedWriterLocker {
public:
    explicit ScopedWriterLocker(LockType* lock) : lock_(lock) {
        lock_->WriterLock();
    }
    ~ScopedWriterLocker() {
        lock_->WriterUnlock();
    }

private:
    LockType* lock_;

    DISALLOW_COPY_AND_ASSIGN(ScopedWriterLocker);
};

template <typename LockType>
class ScopedTryWriterLocker {
public:
    explicit ScopedTryWriterLocker(LockType* lock)
        : lock_(lock) {
        locked_ = lock_->TryWriterLock();
    }
    ~ScopedTryWriterLocker() {
        if (locked_) {
            lock_->WriterUnlock();
        }
    }
    bool IsLocked() const {
        return locked_;
    }

private:
    LockType* lock_;
    bool locked_;

    DISALLOW_COPY_AND_ASSIGN(ScopedTryWriterLocker);
};

}  // namespace byte
