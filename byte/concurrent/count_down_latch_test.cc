// Copyright (c) 2018-present, ByteDance Inc. All rights reserved.

#include "byte/concurrent/count_down_latch.h"

#include "byte/base/atomic.h"
#include "byte/concurrent/timer_manager.h"
#include "byte/include/byte_log.h"
#include "gtest/gtest.h"

namespace {
void EchoCallBack(int* n, byte::CountDownLatch* latch, uint64_t timer_id) {
    byte::AtomicDecrement(n);
    LOG(INFO) << "EchoCallBack " << latch->GetCount();
    latch->CountDown();
}
}  // namespace

namespace byte {

TEST(CountDownLatch, Basic) {
    byte::TimerManager timer;
    static const int count = 10;
    int n = count;
    byte::CountDownLatch latch(n);
    for (int i = 0; i < count; ++i) {
        timer.AddOneshotTimer(i * 10, NewClosure(EchoCallBack, &n, &latch));
    }
    latch.Wait();
    EXPECT_EQ(0, n);
}

TEST(CountDownLatch, TimedWait) {
    byte::CountDownLatch latch(1);
    int64_t start_us = byte::GetCurrentTimeInUs();
    bool res = latch.TimedWait(1000);
    int64_t wait_us = byte::GetCurrentTimeInUs() - start_us;
    ASSERT_FALSE(res);
    ASSERT_TRUE(wait_us >= 1000 * 1000);
}

}  // namespace byte
