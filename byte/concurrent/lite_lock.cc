// Copyright (c) 2018-present, ByteDance Inc. All rights reserved.

#include <assert.h>
#include <stdint.h>
#include <sys/time.h>
#include <string>

#include "byte/concurrent/lite_lock.h"
#include "byte/include/assert.h"
#include "byte/include/macros.h"
#include "byte/system/timestamp.h"
#include "byte/thread/this_thread.h"

namespace byte {

LiteLock::~LiteLock() {
    Payload()->store(kLockInvalid, std::memory_order_release);
}

void LiteLock::Lock() {
    uint64_t value = Payload()->load();
    BYTE_ASSERT(value != kLockInvalid) << "Lock might be deleted " << value;
    do {
        while (IsLocked()) {
            ThisThread::Yield();
        }
    } while (!TryLock());
}

bool LiteLock::TryLock() {
    return CAS(kLockOff, (static_cast<uint64_t>(ThisThread::GetId()) << 32) | kLockOn);
}

void LiteLock::UnLock() {
    uint64_t value = Payload()->load();
    BYTE_ASSERT(value != kLockInvalid && value != kLockOff) << "Lock might be deleted " << value;
    Payload()->store(kLockOff, std::memory_order_release);
}

bool LiteLock::CAS(uint64_t compare, uint64_t new_val) {
    return std::atomic_compare_exchange_strong_explicit(Payload(), &compare, new_val,
            std::memory_order_acquire, std::memory_order_relaxed);
}

bool LiteLock::TimedLock(uint64_t timeout_in_us) {
    uint64_t begin_time = GetCurrentTimeInUs();
    do {
        while (IsLocked()) {
            ThisThread::Yield();
            uint64_t now_time = GetCurrentTimeInUs();
            if (now_time >= begin_time && now_time - begin_time >= timeout_in_us) {
                // timeout
                return false;
            }
        }
    } while (!TryLock());
    return true;
}

int LiteLock::GetLockOwner() const {
    return static_cast<int>(AtomicGet(&lock_) >> 32);
}

void LiteReentrantLock::Lock() {
    const uint32_t me = static_cast<uint32_t>(ThisThread::GetId());
    uint64_t bits = AtomicGet(&bits_);
    while (me == (bits >> 32)) {
        if (AtomicCompareExchange(&bits_, bits, bits + 1)) {
            return;
        }
        bits = AtomicGet(&bits_);
    }

    do {
        bits = AtomicGet(&bits_);
        while ((bits & kMask) != 0U) {
            ThisThread::Yield();
            bits = AtomicGet(&bits_);
        }
    } while (!TryLock(me, bits >> 32));
}

bool LiteReentrantLock::TryLock(uint64_t me, uint64_t owner) {
    return AtomicCompareExchange(&bits_, owner << 32, (me << 32) | 1UL);
}

void LiteReentrantLock::UnLock() {
    BYTE_ASSERT((AtomicGet(&bits_) & kMask) != 0UL && (AtomicGet(&bits_) >> 32)
                == static_cast<uint64_t>(ThisThread::GetId()));
    AtomicDecrement(&bits_);
}

void LiteRWLock::ReadLock() {
    AtomicIncrement(&bits_);
    while (IsWriteLocked()) {
        ThisThread::Yield();
    }
}

void LiteRWLock::ReadUnLock() {
    const uint64_t bits = AtomicDecrement(&bits_);
    BYTE_ASSERT((bits & kWriterMask) == 0);
}

void LiteRWLock::WriteLock() {
    uint64_t target_bits =
        (static_cast<uint64_t>(ThisThread::GetId()) << 32) & kWriterMask;
    do {
        while (IsLocked()) {
            ThisThread::Yield();
        }
    } while (!AtomicCompareExchange(&bits_, 0UL, target_bits));
}

void LiteRWLock::WriteUnLock() {
    uint64_t value = AtomicGet(&bits_);
    while (!AtomicCompareExchange(&bits_, value, value & kReaderMask)) {
        value = AtomicGet(&bits_);
    }
}

bool LiteRWLock::IsReadLocked() const {
    return (AtomicGet(&bits_) & kReaderMask) != 0;
}

bool LiteRWLock::IsWriteLocked() const {
    return (AtomicGet(&bits_) & kWriterMask) != 0;
}

bool LiteRWLock::IsLocked() const {
    return AtomicGet(&bits_) != 0;
}

bool LiteRWLock::TryReadLock() {
    uint64_t value = AtomicGet(&bits_) & kReaderMask;
    return AtomicCompareExchange(&bits_, value, value + 1);
}

bool LiteRWLock::TryWriteLock() {
    uint64_t target_bits =
        (static_cast<uint64_t>(ThisThread::GetId()) << 32) & kWriterMask;
    return AtomicCompareExchange(&bits_, 0UL, target_bits);
}

bool LiteRWLock::TimedReadLock(uint64_t timeout_in_us) {
    uint64_t begin_time = GetCurrentTimeInUs();
    AtomicIncrement(&bits_);
    while (IsWriteLocked()) {
        uint64_t now_time = GetCurrentTimeInUs();
        if (now_time >= begin_time && now_time - begin_time >= timeout_in_us) {
            AtomicDecrement(&bits_);
            return false;
        }
        ThisThread::Yield();
    }
    return true;
}

bool LiteRWLock::TimedWriteLock(uint64_t timeout_in_us) {
    uint64_t begin_time = GetCurrentTimeInUs();
    uint64_t target_bits =
        (static_cast<uint64_t>(ThisThread::GetId()) << 32) & kWriterMask;

    do {
        while (IsLocked()) {
            uint64_t now_time = GetCurrentTimeInUs();
            if (now_time >= begin_time && now_time - begin_time >= timeout_in_us) {
                return false;
            }
            ThisThread::Yield();
        }
    } while (!AtomicCompareExchange(&bits_, 0UL, target_bits));
    return true;
}

int LiteRWLock::GetWriteLockOwner() const {
    return static_cast<int>((AtomicGet(&bits_) & kWriterMask) >> 32);
}

void LiteRWLockPreferWrite::ReadLock() {
    uint64_t value = 0;
    bool first_try = true;
    do {
        if (!first_try) {
            ThisThread::Yield();
        } else {
            first_try = false;
        }
        while (((value = AtomicGet(&bits_)) & kWriterMask) != 0) {
            ThisThread::Yield();
        }
    } while (!AtomicCompareExchange(&bits_, value, value + 1));
}

void LiteRWLockPreferWrite::ReadUnLock() {
    AtomicDecrement(&bits_);
}

void LiteRWLockPreferWrite::WriteLock() {
    uint64_t value = 0;
    // Set write lock bit firstly.
    uint64_t writer_owner = ThisThread::GetId();
    writer_owner = (writer_owner << 32) & kWriterMask;

    bool first_try = true;
    do {
        if (!first_try) {
            ThisThread::Yield();
        } else {
            first_try = false;
        }
        while (((value = AtomicGet(&bits_)) & kWriterMask) != 0) {
            ThisThread::Yield();
        }
    } while (!AtomicCompareExchange(&bits_, value, value | writer_owner));
    // Wait reads count down to 0.
    while ((AtomicGet(&bits_) & kReaderMask) != 0) {
        ThisThread::Yield();
    }
}

void LiteRWLockPreferWrite::WriteUnLock() {
    uint64_t value = AtomicGet(&bits_);
    while (!AtomicCompareExchange(&bits_, value, value & kReaderMask)) {
        value = AtomicGet(&bits_);
    }
}

bool LiteRWLockPreferWrite::IsReadLocked() const {
    return (AtomicGet(&bits_) & kReaderMask) != 0;
}

bool LiteRWLockPreferWrite::IsWriteLocked() const {
    // Same as read lock.
    return (AtomicGet(&bits_) & kWriterMask) != 0;
}

bool LiteRWLockPreferWrite::IsLocked() const {
    return AtomicGet(&bits_) != 0;
}

bool LiteRWLockPreferWrite::TimedReadLock(uint64_t timeout_in_us) {
    uint64_t begin_time = GetCurrentTimeInUs();
    uint64_t value = 0;
    bool first_try = true;
    do {
        if (!first_try) {
            ThisThread::Yield();
        } else {
            first_try = false;
        }
        while (((value = AtomicGet(&bits_)) & kWriterMask) != 0) {
            uint64_t now_time = GetCurrentTimeInUs();
            if (now_time >= begin_time && now_time - begin_time >= timeout_in_us) {
                return false;
            }
            ThisThread::Yield();
        }
    } while (!AtomicCompareExchange(&bits_, value, value + 1));
    return true;
}

bool LiteRWLockPreferWrite::TimedWriteLock(uint64_t timeout_in_us) {
    uint64_t begin_time = GetCurrentTimeInUs();
    uint64_t value = 0;
    // Set write lock bit firstly.
    uint64_t writer_owner = ThisThread::GetId();
    writer_owner = (writer_owner << 32) & kWriterMask;

    bool first_try = true;
    do {
        if (!first_try) {
            ThisThread::Yield();
        } else {
            first_try = false;
        }
        while (((value = AtomicGet(&bits_)) & kWriterMask) != 0) {
            uint64_t now_time = GetCurrentTimeInUs();
            if (now_time >= begin_time && now_time - begin_time >= timeout_in_us) {
                return false;
            }
            ThisThread::Yield();
        }
    } while (!AtomicCompareExchange(&bits_, value, value | writer_owner));
    // Wait reads count down to 0.
    while (((value = AtomicGet(&bits_)) & kReaderMask) != 0) {
        uint64_t now_time = GetCurrentTimeInUs();
        if (now_time >= begin_time && now_time - begin_time >= timeout_in_us) {
            // WriteUnLock.
            while (!AtomicCompareExchange(
                        &bits_, value, value & kReaderMask)) {
                value = AtomicGet(&bits_);
            }
            return false;
        }
        ThisThread::Yield();
    }
    return true;
}

int LiteRWLockPreferWrite::GetWriteLockOwner() const {
    return static_cast<int>(AtomicGet(&bits_) >> 32);
}

ScopedLiteLock::ScopedLiteLock(LiteLock* lock) : device_(lock)  {
    device_->Lock();
}

ScopedLiteLock::~ScopedLiteLock() {
    device_->UnLock();
}

ScopedLiteReentrantLock::ScopedLiteReentrantLock(
        LiteReentrantLock* lock) : device_(lock)  {
    device_->Lock();
}

ScopedLiteReentrantLock::~ScopedLiteReentrantLock() {
    device_->UnLock();
}

ScopedLiteRWLock::ScopedLiteRWLock(
        LiteRWLock* lock, const char mode)
    : device_(lock), mode_(mode), upgraded_(false) {
    if (mode_ == 'r' || mode_ == 'R') {
        device_->ReadLock();
    } else if (mode_ == 'w' || mode_ == 'W') {
        device_->WriteLock();
    } else {
        BYTE_ASSERT(false);
    }
}

bool ScopedLiteRWLock::UpgradeToWriterLock() {
    if ((mode_ == 'r' || mode_ == 'R') && !upgraded_) {
        device_->ReadUnLock();
        device_->WriteLock();
        upgraded_ = true;
        return true;
    } else {
        return false;
    }
}

ScopedLiteRWLock::~ScopedLiteRWLock() {
    if ((mode_ == 'r' || mode_ == 'R') && !upgraded_) {
        device_->ReadUnLock();
    } else if (mode_ == 'w' || mode_ == 'W' || upgraded_) {
        device_->WriteUnLock();
    }
}

ScopedLiteRWLockPreferWrite::ScopedLiteRWLockPreferWrite(
        LiteRWLockPreferWrite* lock, const char mode)
    : device_(lock), mode_(mode), upgraded_(false) {
    if (mode_ == 'r' || mode_ == 'R') {
        device_->ReadLock();
    } else if (mode_ == 'w' || mode_ == 'W') {
        device_->WriteLock();
    } else {
        BYTE_ASSERT(false);
    }
}

bool ScopedLiteRWLockPreferWrite::UpgradeToWriterLock() {
    if ((mode_ == 'r' || mode_ == 'R') && !upgraded_) {
        device_->ReadUnLock();
        device_->WriteLock();
        upgraded_ = true;
        return true;
    } else {
        return false;
    }
}

ScopedLiteRWLockPreferWrite::~ScopedLiteRWLockPreferWrite() {
    if ((mode_ == 'r' || mode_ == 'R') && !upgraded_) {
        device_->ReadUnLock();
    } else if (mode_ == 'w' || mode_ == 'W' || upgraded_) {
        device_->WriteUnLock();
    }
}

LiteLockVector::LiteLockVector(uint32_t size) {
    bits_.assign(((size + 31) >> 5), 0UL);
}

void LiteLockVector::Lock(uint32_t index) {
    const uint32_t mask = Mask(index);
    do {
        while ((AtomicGet(&bits_[index >> 5]) & mask) != 0) {
            ThisThread::Yield();
        }
    } while (!TryLock(index));
}

bool LiteLockVector::TryLock(uint32_t index) {
    const uint32_t mask = Mask(index);
    const uint32_t value = AtomicGet(&bits_[index >> 5]);
    return AtomicCompareExchange(
            &(bits_[index >> 5]), value & ~mask, value | mask);
}

void LiteLockVector::UnLock(uint32_t index) {
    const uint32_t mask = Mask(index);
    uint32_t value = AtomicGet(&bits_[index >> 5]);
    while ((value & mask) != 0 && !AtomicCompareExchange(
                &bits_[index >> 5], value, value & ~mask)) {
        ThisThread::Yield();
        value = AtomicGet(&bits_[index >> 5]);
    }
}

bool LiteLockVector::IsLocked(uint32_t index) const {
    return (AtomicGet(&bits_[index >> 5]) & Mask(index)) != 0;
}

LiteRWLockRefCount::LiteRWLockRefCount(LockType type) {
    AddRef();
    switch (type) {
        case READ_LOCK:
            ReadLock();
            break;
        case WRITE_LOCK:
            WriteLock();
            break;
        case RELEASE:
            BYTE_ASSERT(false);
    }
}

void LiteRWLockRefCount::ReadLock() {
    AddRef();
    LiteRWLock::ReadLock();
}

void LiteRWLockRefCount::ReadUnLock() {
    LiteRWLock::ReadUnLock();
    RefCount<LiteRWLockRefCount>::Release();
}

void LiteRWLockRefCount::WriteLock() {
    AddRef();
    LiteRWLock::WriteLock();
}

void LiteRWLockRefCount::WriteUnLock() {
    LiteRWLock::WriteUnLock();
    RefCount<LiteRWLockRefCount>::Release();
}

}  // namespace byte
