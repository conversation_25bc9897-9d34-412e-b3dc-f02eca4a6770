// Copyright (c) 2018-present, ByteDance Inc. All rights reserved.

#include "byte/base/iovec.h"
#include "gtest/gtest.h"

TEST(IoVec, Read) {
    const uint32_t capacity = 100;
    uint32_t size = 10;
    char* ptr1 = new char[capacity];
    char* ptr2 = new char[capacity];
    for (uint32_t i = 0; i < size; ++i) {
        ptr1[i] = 'A' + i;
        ptr2[i] = 'a' + i;
    }
    ptr1[size] = '\0';
    ptr2[size] = '\0';
    iovec* iov = new iovec[2];
    iov[0].iov_base = ptr1;
    iov[0].iov_len = size;
    iov[1].iov_base = ptr2;
    iov[1].iov_len = size;
    byte::IoVec iovec(iov, 2);
    char* buffer = new char[capacity];
    EXPECT_EQ(5, iovec.ReadBuffer(buffer, 5));
    buffer[5] = '\0';
    EXPECT_STREQ("ABCDE", buffer);
    EXPECT_EQ(10, iovec.ReadBuffer(buffer, 10));
    buffer[10] = '\0';
    EXPECT_STREQ("FGHIJabcde", buffer);
    EXPECT_EQ(5, iovec.ReadBuffer(buffer, 10));
    buffer[5] = '\0';
    EXPECT_STREQ("fghij", buffer);
    delete[] ptr1;
    delete[] ptr2;
    delete[] iov;
    delete[] buffer;
}

TEST(IoVec, Write) {
    uint32_t capacity = 100;
    iovec* iov = new iovec;
    iov->iov_base = new char[capacity];
    iov->iov_len = capacity;
    byte::IoVec iovec(iov, 1);
    EXPECT_EQ(5, iovec.WriteBuffer("ABCDE", 5));
    EXPECT_EQ(10, iovec.WriteBuffer("FGHIJabcde", 10));
    EXPECT_EQ(5, iovec.WriteBuffer("fghij", 5));
    iov->iov_len = 20;
    byte::IoVec iovec2(iov, 1);
    char read_buffer[30];
    const size_t read_size = iovec2.ReadBuffer(read_buffer, 25);
    EXPECT_EQ(20, read_size);
    read_buffer[read_size] = '\0';
    EXPECT_STREQ("ABCDEFGHIJabcdefghij", read_buffer);
    delete[] (reinterpret_cast<char*>(iov->iov_base));
    delete iov;
}
