// Copyright (c) 2018-present, ByteDance Inc. All rights reserved.

#pragma once

#include <stdint.h>
#include <stdlib.h>

namespace byte {

uint32_t MurmurHash(const char* ptr, size_t size);

uint32_t XXHash(const char* ptr, size_t size);

uint32_t Djb2Hash(const char* ptr, size_t size);

uint32_t NewXXHash(const char* ptr, size_t size);

uint32_t FNVHash(const char* ptr, size_t size);

}  // namespace byte
