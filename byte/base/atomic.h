// Copyright (c) 2018-present, ByteDance Inc. All rights reserved.

// This file is modified from toft:
// https://github.com/chen3feng/toft/tree/master/system/atomic

// Copyright (c) 2009, The Toft Authors.
// All rights reserved.
// Author: <PERSON><PERSON> <PERSON> <<EMAIL>>

#pragma once

namespace byte {

template <typename T>
T AtomicGet(const T* ptr) {
    __sync_synchronize();
    return *ptr;
}

template <typename T>
T AtomicSet(T* ptr, T value) {
    (void)__sync_lock_test_and_set(ptr, value);
    return value;
}

template <typename T>
T AtomicExchange(T* ptr, T value) {
    return __sync_lock_test_and_set(ptr, value);
}

template <typename T>
T AtomicAdd(T* ptr, T value) {
    return __sync_add_and_fetch(ptr, value);
}

template <typename T>
T AtomicSub(T* ptr, T value) {
    return __sync_sub_and_fetch(ptr, value);
}

template <typename T>
T AtomicOr(T* ptr, T value) {
    return __sync_or_and_fetch(ptr, value);
}

template <typename T>
T AtomicAnd(T* ptr, T value) {
    return __sync_and_and_fetch(ptr, value);
}

template <typename T>
T AtomicXor(T* ptr, T value) {
    return __sync_xor_and_fetch(ptr, value);
}

template <typename T>
T AtomicIncrement(T* ptr) {
    return __sync_add_and_fetch(ptr, 1);
}

template <typename T>
T AtomicDecrement(T* ptr) {
    return __sync_sub_and_fetch(ptr, 1);
}

template <typename T>
T AtomicExchangeAdd(T* ptr, T value) {
    return __sync_fetch_and_add(ptr, value);
}

template <typename T>
T AtomicExchangeSub(T* ptr, T value) {
    return __sync_fetch_and_sub(ptr, value);
}

template <typename T>
T AtomicExchangeOr(T* ptr, T value) {
    return __sync_fetch_and_or(ptr, value);
}

template <typename T>
T AtomicExchangeAnd(T* ptr, T value) {
    return __sync_fetch_and_and(ptr, value);
}

template <typename T>
T AtomicExchangeXor(T* ptr, T value) {
    return __sync_fetch_and_xor(ptr, value);
}

template <typename T>
bool AtomicCompareExchange(T* ptr, T compare, T exchange, T* old) {
    *old = *ptr;
    if (__sync_bool_compare_and_swap(ptr, compare, exchange)) {
        *old = compare;
        return true;
    }
    return false;
}

template <typename T>
bool AtomicCompareExchange(T* ptr, T compare, T exchange) {
    return __sync_bool_compare_and_swap(ptr, compare, exchange);
}

// Atomic integer
template <typename T>
class Atomic {
    typedef Atomic<T> ThisType;

public:
    Atomic() : value_() {}

    // implicit
    Atomic(T value) : value_(value) {}  // NOLINT(runtime/explicit)

    // generated copy ctor is ok
    operator T() const {
        return AtomicGet(&value_);
    }

    T Value() const {
        return AtomicGet(&value_);
    }

    ThisType& operator=(T value) {
        AtomicSet(&value_, value);
        return *this;
    }

    T operator++() {
        return Increment();
    }

    T operator++(int) {
        return ExchangeAddWith(1);
    }

    T operator--() {
        return Decrement();
    }

    T operator--(int) {
        return ExchangeSubWith(1);
    }

    T operator+=(T value) {
        return AddWith(value);
    }

    T operator-=(T value) {
        return SubWith(value);
    }

    T operator&=(T value) {
        return AndWith(value);
    }

    T operator|=(T value) {
        return OrWith(value);
    }

    T operator^=(T value) {
        return XorWith(value);
    }

public:
    T Exchange(const T value) {
        return AtomicExchange(&value_, value);
    }

    T Increment() {
        return AtomicIncrement(&value_);
    }

    T Decrement() {
        return AtomicDecrement(&value_);
    }

    T ExchangeAddWith(T value) {
        return AtomicExchangeAdd(&value_, value);
    }

    T ExchangeSubWith(T value) {
        return AtomicExchangeSub(&value_, value);
    }

    T ExchangeAndWith(T value) {
        return AtomicExchangeAnd(&value_, value);
    }

    T ExchangeOrWith(T value) {
        return AtomicExchangeOr(&value_, value);
    }

    T ExchangeXorWith(T value) {
        return AtomicExchangeXor(&value_, value);
    }

    T AddWith(T value) {
        return AtomicAdd(&value_, value);
    }

    T SubWith(T value) {
        return AtomicSub(&value_, value);
    }

    T AndWith(T value) {
        return AtomicAnd(&value_, value);
    }

    T OrWith(T value) {
        return AtomicOr(&value_, value);
    }

    T XorWith(T value) {
        return AtomicXor(&value_, value);
    }

    bool CompareExchange(T compare, T exchange, T* old) {
        return AtomicCompareExchange(&value_, compare, exchange, old);
    }

    bool CompareExchange(T compare, T exchange) {
        return AtomicCompareExchange(&value_, compare, exchange);
    }

private:
    T value_;
};

// Atomic pointer
template <typename T>
class Atomic<T*> {
    typedef Atomic<T*> ThisType;

public:
    Atomic() : value_() {}

    // implicit
    Atomic(T* value) : value_(value) {}  // NOLINT(runtime/explicit)

    // generated copy ctor is ok
    operator T*() const {
        return AtomicGet(&value_);
    }

    T* Value() const {
        return AtomicGet(&value_);
    }

    ThisType& operator=(T* value) {
        AtomicSet(&value_, value);
        return *this;
    }

    T* Exchange(T* exchange) {
        return AtomicExchange(&value_, exchange);
    }

    bool CompareExchange(T* compare, T* exchange, T** old) {
        return AtomicCompareExchange(&value_, compare, exchange, old);
    }

private:
    T* value_;
};

}  // namespace byte
