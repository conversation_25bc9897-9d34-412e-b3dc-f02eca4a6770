// Copyright (c) 2019, ByteDance Inc. All rights reserved.

#pragma once

#include <atomic>

#include "byte/include/macros.h"
#include "byte/base/closure.h"
#include "byte/thread/async_thread.h"
#include "byte/concurrent/event.h"

namespace byte {

class ByteTimer {
public:
    enum class Mode {
        ONCE,
        REPEATED
    };

    ByteTimer();
    ~ByteTimer();

    /**
     * Initialize this timer.
     *
     * The callback closure must be permanent, which means it may
     * be called zero, one or more times. It is recommended to
     * use permanent closures, because the user does not need to
     * maintain life-cycle of the closure objects.
     *
     * @param loop      the ev loop, must be in current thread
     * @param mode      ONCE or REPEATED
     * @param interval  interval in microseconds
     * @param callback  a closure that will be called when timer
     *                  is activated.
     */
    void Init(struct ev_loop* loop,
              Mode mode,
              uint64_t internvalInUs,
              Closure<void>* callback);

    void Start();
    void Stop();

    /**
     * Start or Stop in user spcified thread.
     */
    void StartInThread(AsyncThread* thread);
    void StopInThread(AsyncThread* thread);

    /**
     * The number of activation since last Init(), which is useful
     * in debugging and testing.
     */
    int LoopCount() const { return loopCount_; }

    /**
     * Test if there will be more pending callbacks
     *
     * If this is a ONE_SHOT timer, it will automatically turn to inactive
     * right *before* the callback is invoked.
     */
    bool IsActive() const { return ev_is_active(&ev_timer_); }

private:
    void onTimer();
    static void onTimerHelper(
        struct ev_loop* loop, ev_timer* timer,
        int revents);
    void doStart(AutoResetEvent* event);
    void doStop(AutoResetEvent* event);

private:
    enum class State {
        SPAWN,
        INIT,
        RUNNING,
        STOPPED
    };
    struct ev_loop*     ev_loop_;
    ev_timer*           ev_timer_;
    Mode                mode_;
    Closure<void>*      callback_;
    int                 loopCount_;
    std::atomic<State>  state_;

    DISALLOW_COPY_AND_ASSIGN(ByteTimer);
};

}  // namespace byte
