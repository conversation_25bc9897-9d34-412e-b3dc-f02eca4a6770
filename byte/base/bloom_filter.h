// Copyright (c) 2018-present, ByteDance Inc. All rights reserved.

#pragma once

#include <assert.h>
#include <stdint.h>
#include "byte/base/bit_vector.h"
#include "byte/base/hash.h"
#include "byte/include/assert.h"

namespace byte {

typedef uint32_t(*HashFuncs)(const char*, size_t);

// k: the hash functions
// m: size of bloom filter
// n: actual size of samples.
// The probability of a false positive is minimized for k = ln2 *(m / n)
// When k = 2, m = 2.88 * n is preferred.
template<typename Key>
class BloomFilter {
public:
    static const uint32_t kMaxNumFilters = 4;
    static const uint32_t kDefaultNumFilters = 2;

    // size: The estimated scale of samples. NOT capacity of bloom filters.
    explicit BloomFilter(uint32_t size,
                         uint32_t numFilters = kDefaultNumFilters)
        : num_filters_(numFilters),
          size_(0) {
        double kLn2 = 0.69314718;  // Ln(2)
        size_ = static_cast<uint32_t>((size * numFilters) / kLn2);
        BYTE_ASSERT(num_filters_ <= kMaxNumFilters);
    }

    void Put(const Key& key, BitVector* bits) const {
        for (uint32_t i = 0; i < num_filters_; ++i) {
            bits->SetTrue(Hash(key, i));
        }
    }

    void Put(const Key& key, uint32_t* bits) const {
        for (uint32_t i = 0; i < num_filters_; ++i) {
            BitBase::SetTrue(bits, Hash(key, i));
        }
    }

    bool Get(const Key& key, const BitVector& bits) const {
        for (uint32_t i = 0; i < num_filters_; ++i) {
            if (!bits.Get(Hash(key, i))) {
                return false;
            }
        }
        return true;
    }

    bool Get(const Key& key, const uint32_t* bits) const {
        for (uint32_t i = 0; i < num_filters_; ++i) {
            if (!BitBase::Get(bits, Hash(key, i))) {
                return false;
            }
        }
        return true;
    }

    inline uint32_t GetSize() const {
        return size_;
    }

    inline uint32_t GetBytes() const {
        return (size_ + 31) / 32 * 4;
    }

    inline uint32_t GetNumFilters() const {
        return num_filters_;
    }

public:
    static HashFuncs sHashFuncs[kMaxNumFilters];

private:
    uint32_t Hash(const Key& key, uint32_t index) const {
        return sHashFuncs[index](reinterpret_cast<const char*>(&key),
                                 sizeof(key)) % size_;
    }

    uint32_t num_filters_;
    uint32_t size_;
};

template<typename Key>
HashFuncs BloomFilter<Key>::sHashFuncs[BloomFilter<Key>::kMaxNumFilters] =
    { XXHash, MurmurHash, Djb2Hash, FNVHash };

}  // namespace byte
