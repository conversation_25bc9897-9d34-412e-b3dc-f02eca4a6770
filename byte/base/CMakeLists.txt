add_subdirectory(detail)

byte_cc_library(
  NAME
    atomic
  HDRS
    "atomic.h"
)

byte_cc_test(
  NAME
    atomic_test
  SRCS
    "atomic_test.cc"
  DEPS
    byte::atomic
    gtest_main  
)

byte_cc_library(
  NAME
    bit_vector
  HDRS
    "bit_vector.h"
)

byte_cc_test(
  NAME
    bit_vector_test
  SRCS
    "bit_vector_test.cc"
  DEPS
    byte::bit_vector
    gtest_main  
)

byte_cc_library(
  NAME
    bloom_filter
  HDRS
    "bloom_filer.h"
  DEPS
    byte::bit_vector
    byte::hash
    byte::assert
)

byte_cc_test(
  NAME
    bloom_filter_test
  SRCS
    "bloom_filter_test.cc"
  DEPS
    byte::bloom_filter
    byte::hash
    gtest_main  
)

if (BUILD_EV)
  byte_cc_library(
    NAME
      byte_timer
    HDRS
      "byte_timer.h"
    SRCS
      "byte_timer.cc"
    DEPS
      byte::macros
      byte::closure
      byte::async_thread
      byte::event
      ev-static
  )

  byte_cc_test(
    NAME
      byte_timer_test
    SRCS
      "byte_timer_test.cc"
    DEPS
      byte::byte_timer
      gtest_main  
  )
endif()

byte_cc_library(
  NAME
    closure
  HDRS
    "closure.h"
)

byte_cc_test(
  NAME
    closure_test
  SRCS
    "closure_test.cc"
  DEPS
    byte::closure
    gtest_main  
)

byte_cc_library(
  NAME
    endian
  HDRS
    "endian.h"
)

byte_cc_test(
  NAME
    endian_test
  SRCS
    "endian_test.cc"
  DEPS
    byte::endian
    gtest_main  
)


byte_cc_library(
  NAME
    hash
  HDRS
    "hash.h"
  SRCS
    "hash.cc"
  DEPS
    byte::xxhash
)

byte_cc_test(
  NAME
    hash_test
  SRCS
    "hash_test.cc"
  DEPS
    byte::hash
    byte::timestamp
    gtest_main  
)

byte_cc_library(
  NAME
    iovec
  HDRS
    "iovec.h"
  SRCS
    "iovec.cc"
  DEPS
    byte::assert
)

byte_cc_test(
  NAME
    iovec_test
  SRCS
    "iovec_test.cc"
  DEPS
    byte::iovec
    gtest_main  
)


byte_cc_library(
  NAME
    mem_pool_lite
  HDRS
    "mem_pool_lite.h"
  SRCS
    "mem_pool_lite.cc"
  DEPS
    byte::assert
    byte::lite_lock
    byte::scoped_ptr
)

byte_cc_test(
  NAME
    mem_pool_lite_test
  SRCS
    "mem_pool_lite_test.cc"
  DEPS
    byte::mem_pool_lite
    gtest_main  
)

byte_cc_library(
  NAME
    ref_count
  HDRS
    "ref_count.h"
)

byte_cc_test(
  NAME
    ref_count_test
  SRCS
    "ref_count_test.cc"
  DEPS
    byte::ref_count
    gtest_main  
)

byte_cc_library(
  NAME
    ring_buffer
  HDRS
    "ring_buffer.h"
)

byte_cc_test(
  NAME
    ring_buffer_test
  SRCS
    "ring_buffer_test.cc"
  DEPS
    byte::ring_buffer
    gtest_main  
)


byte_cc_library(
  NAME
    singleton
  HDRS
    "singleton.h"
)

byte_cc_test(
  NAME
    singleton_test
  SRCS
    "singleton_test.cc"
  DEPS
    byte::singleton
    gtest_main  
)

# only builds in x86_64
if (${CMAKE_SYSTEM_PROCESSOR} MATCHES "x86_64") 
byte_cc_library(
  NAME
    memcpy
  HDRS
    "memcpy.h"
  SRCS
    "memcpy.S"
)

byte_cc_test(
  NAME
    memcpy_test
  SRCS
    "memcpy_test.cc"
  DEPS
    gtest_main
    byte::memcpy
)
endif()