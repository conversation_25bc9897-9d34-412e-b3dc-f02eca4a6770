// Copyright (c) 2018-present, ByteDance Inc. All rights reserved.

#pragma once

#include <stdint.h>
#include <stdlib.h>
#include <sys/uio.h>

namespace byte {

class IoVec {
public:
    IoVec();

    IoVec(iovec* iov, uint32_t count);

    size_t ReadBuffer(void* buf, size_t length);
    size_t ReadIovec(iovec* iov, uint32_t count);

    size_t WriteBuffer(const void* buf, size_t length);
    size_t WriteIovec(const iovec* iov, uint32_t count);

private:
    enum OpType {
        TYPE_IOVEC_OP_READ = 0,
        TYPE_IOVEC_OP_WRITE = 1,
    };

    size_t CopyBuffer(void* buf, size_t length, OpType type);
    size_t CopyIovec(iovec* iov, uint32_t count, OpType type);

    iovec* iov_;
    iovec* end_;
    uint32_t count_;
    size_t offset_;
};

}  // namespace byte
