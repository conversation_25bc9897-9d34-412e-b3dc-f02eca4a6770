// Copyright (c) 2023-present, ByteDance Inc. All rights reserved.
#include "byte/fiber/fiber.h"

#include <mutex>  // NOLINT(build/c++11)

#include "byte/fiber/errno.h"
#include "byte/fiber/fiber_key.h"
#include "byte/include/macros.h"
#include "photon/common/alog.h"
#include "photon/photon.h"
#include "photon/thread/thread.h"

namespace byte {
namespace fiber {

struct PhotonFiber : Fiber {
    photon::thread* th;
    void* (*fn)(void*);
    void* arg;
    size_t stack_size;
    bool joinable;
    photon::join_handle* join_handler;
    void* fls{nullptr};

    ~PhotonFiber() {
        if (fls) {
            DeallocateFLS(&fls);
        }
    }
};

struct PhotonThreadCtx : ThreadCtx {
    photon::vcpu_base* vcpu;
    PhotonFiber* main_fiber;
};

namespace {
thread_local PhotonThreadCtx* thread_ctx = nullptr;
// GlobalFini also need the context of GlobalInit, since some options
// are necessary when exiting.
GlobalInitOption globalOpt;
std::once_flag global_init_flag;
bool is_global_init = false;
}  // namespace

int GlobalInit(const GlobalInitOption& opt) {
    globalOpt = opt;

    int ret = 0;
    std::call_once(global_init_flag, [&]() {
        // Init photon alog
        if (opt.enable_fiber_log) {
            ret = SetFiberInternalLogLevel(opt.fiber_log_level);
            if (0 != ret) {
                return;
            }
            // Use synchronous log now, we'll upgrade to asynchronous
            // log later depending on the situation.
            if (0 != log_output_file(opt.fiber_log_path.c_str())) {
                ret = EINTERNAL;
                return;
            }
        }
        is_global_init = true;
    });
    return ret;
}

void GlobalFini() {
    if (globalOpt.enable_fiber_log) {
        log_output_file_close();
    }
    is_global_init = false;
}

std::atomic<uint64_t> total_fiber_num(0);
std::atomic<uint64_t> total_fiber_stack_size(0);
GlobalStatistics GetGlobalStatistics() {
    return {total_fiber_num.load(std::memory_order_relaxed),
            total_fiber_stack_size.load(std::memory_order_relaxed)};
}

int ThreadInit(const ThreadInitOption& opt) {
    if (UNLIKELY(!is_global_init)) {
        return EUNINIT;
    }

    if (UNLIKELY(thread_ctx != nullptr)) {
        return 0;
    }

    uint64_t event_engine = 0;
    switch (opt.event_engine) {
        case ThreadInitOption::EPOLL:
            event_engine = photon::INIT_EVENT_EPOLL;
            break;
        case ThreadInitOption::IOURING:
            event_engine = photon::INIT_EVENT_IOURING;
            break;
        default:
            return EINVAL;
    }
    if (0 != photon::init(event_engine, photon::INIT_IO_NONE)) {
        return EINTERNAL;
    }

    thread_ctx = new PhotonThreadCtx();
    thread_ctx->vcpu = photon::get_vcpu();
    thread_ctx->main_fiber = new PhotonFiber();
    thread_ctx->main_fiber->th = photon::CURRENT;
    thread_ctx->main_fiber->joinable = false;
    return 0;
}

void ThreadFini() {
    if (LIKELY(thread_ctx != nullptr)) {
        photon::fini();
        delete thread_ctx->main_fiber;
        delete thread_ctx;
        thread_ctx = nullptr;
    }
}

ThreadCtx* GetThreadCtx() { return thread_ctx; }

int SetFiberInternalLogLevel(GlobalInitOption::FiberLogLevel level) {
    switch (level) {
        case GlobalInitOption::DEBUG:
            set_log_output_level(ALOG_DEBUG);
            break;
        case GlobalInitOption::INFO:
            set_log_output_level(ALOG_INFO);
            break;
        case GlobalInitOption::WARNING:
            set_log_output_level(ALOG_WARN);
            break;
        case GlobalInitOption::ERROR:
            set_log_output_level(ALOG_ERROR);
            break;
        default:
            return EINVAL;
    }
    return 0;
}

static void* fn_wrapper(void* arg) {
    PhotonFiber* photon_fiber = static_cast<PhotonFiber*>(arg);
    void* ret = photon_fiber->fn(photon_fiber->arg);
    if (!photon_fiber->joinable) {
        if (globalOpt.enable_global_statistics) {
            total_fiber_num.fetch_sub(1, std::memory_order_relaxed);
            total_fiber_stack_size.fetch_sub(photon_fiber->stack_size, std::memory_order_relaxed);
        }
        delete photon_fiber;
    }
    return ret;
}

Fiber* Create(const CreateOpt& opt, void* (*entry)(void*), void* args) {
    if (UNLIKELY(!thread_ctx)) {
        return nullptr;
    }
    if (UNLIKELY(opt.stack_size_ < 16 * 1024UL)) {
        return nullptr;
    }
    PhotonFiber* photon_fiber = new PhotonFiber();
    photon_fiber->arg = args;
    photon_fiber->fn = entry;
    photon_fiber->stack_size = opt.stack_size_;
    photon_fiber->joinable = opt.joinable_;
    photon_fiber->th =
        photon::thread_create(fn_wrapper, photon_fiber, opt.stack_size_, sizeof(PhotonFiber*));
    if (UNLIKELY(nullptr == photon_fiber->th)) {
        delete photon_fiber;
        return nullptr;
    }
    PhotonFiber** reserved_space =
        (photon::thread_reserved_space<PhotonFiber*>(photon_fiber->th, sizeof(PhotonFiber*)));
    *reserved_space = photon_fiber;
    photon_fiber->join_handler = photon::thread_enable_join(photon_fiber->th, opt.joinable_);
    if (globalOpt.enable_global_statistics) {
        total_fiber_num.fetch_add(1, std::memory_order_relaxed);
        total_fiber_stack_size.fetch_add(opt.stack_size_, std::memory_order_relaxed);
    }
    return photon_fiber;
}

Fiber* GetCurrentFiber() {
    if (UNLIKELY(!thread_ctx)) {
        return nullptr;
    }
    if (thread_ctx->main_fiber->th == photon::CURRENT) {
        return thread_ctx->main_fiber;
    }
    return *photon::thread_reserved_space<PhotonFiber*>(photon::CURRENT, sizeof(PhotonFiber*));
}

void** GetCurrentFiberFLS() {
    if (UNLIKELY(!thread_ctx)) {
        return nullptr;
    }
    return &(reinterpret_cast<PhotonFiber*>(GetCurrentFiber())->fls);
}

int Join(Fiber* fiber) {
    if (UNLIKELY(!thread_ctx)) {
        return EUNINIT;
    }

    PhotonFiber* photon_fiber = static_cast<PhotonFiber*>(fiber);
    if (photon_fiber->joinable) {
        photon::thread_join(photon_fiber->join_handler);
        if (globalOpt.enable_global_statistics) {
            total_fiber_num.fetch_sub(1, std::memory_order_relaxed);
            total_fiber_stack_size.fetch_sub(photon_fiber->stack_size, std::memory_order_relaxed);
        }
        delete photon_fiber;
    }
    return 0;
}

int Yield() {
    if (UNLIKELY(!thread_ctx)) {
        return EUNINIT;
    }
    photon::thread_yield();
    return 0;
}

int YieldTo(Fiber* fiber) {
    if (UNLIKELY(!thread_ctx)) {
        return EUNINIT;
    }
    PhotonFiber* photon_fiber = static_cast<PhotonFiber*>(fiber);
    photon::thread_yield_to(photon_fiber->th);
    return 0;
}

int Usleep(uint64_t useconds) {
    if (UNLIKELY(!thread_ctx)) {
        return EUNINIT;
    }
    photon::thread_usleep(useconds);
    return 0;
}

int Awaken(Fiber* fiber) {
    PhotonFiber* photon_fiber = static_cast<PhotonFiber*>(fiber);
    photon::thread_interrupt(photon_fiber->th);
    return 0;
}

int Migrate(Fiber* fiber, ThreadCtx* ctx) {
    if (UNLIKELY(!thread_ctx)) {
        return EUNINIT;
    }

    PhotonFiber* photon_fiber = static_cast<PhotonFiber*>(fiber);
    PhotonThreadCtx* thread_ctx = static_cast<PhotonThreadCtx*>(ctx);

    if (UNLIKELY(0 != photon::thread_migrate(photon_fiber->th, thread_ctx->vcpu))) {
        return EINVAL;
    }

    return 0;
}

}  // namespace fiber
}  // namespace byte
