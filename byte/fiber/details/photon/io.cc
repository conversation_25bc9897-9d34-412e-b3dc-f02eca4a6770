// Copyright (c) 2023-present, ByteDance Inc. All rights reserved.
#include "byte/fiber/io.h"

#include "byte/fiber/event.h"
#include "byte/include/macros.h"
#include "photon/io/iouring-wrapper.h"

namespace byte {
namespace fiber {

#ifdef FIBER_ENABLE_URING

ssize_t iouring_read(int fd, void* buf, size_t count, uint64_t timeout_us) {
    return photon::iouring_pread(fd, buf, count, 0, 0, timeout_us);
}

ssize_t iouring_write(int fd, const void* buf, size_t count, uint64_t timeout_us) {
    return photon::iouring_pwrite(fd, buf, count, 0, 0, timeout_us);
}

ssize_t iouring_readv(int fd, const iovec* iov, int iovcnt, uint64_t timeout_us) {
    return photon::iouring_preadv(fd, iov, iovcnt, 0, 0, timeout_us);
}

ssize_t iouring_writev(int fd, const iovec* iov, int iovcnt, uint64_t timeout_us) {
    return photon::iouring_pwritev(fd, iov, iovcnt, 0, 0, timeout_us);
}

ssize_t iouring_pread(int fd, void* buf, size_t count, off_t offset, uint64_t timeout_us) {
    return photon::iouring_pread(fd, buf, count, offset, 0, timeout_us);
}

ssize_t iouring_pwrite(int fd, const void* buf, size_t count, off_t offset, uint64_t timeout_us) {
    return photon::iouring_pwrite(fd, buf, count, offset, 0, timeout_us);
}

ssize_t iouring_preadv(int fd, const iovec* iov, int iovcnt, off_t offset, uint64_t timeout_us) {
    return photon::iouring_preadv(fd, iov, iovcnt, offset, 0, timeout_us);
}

ssize_t iouring_pwritev(int fd, const iovec* iov, int iovcnt, off_t offset, uint64_t timeout_us) {
    return photon::iouring_pwritev(fd, iov, iovcnt, offset, 0, timeout_us);
}

ssize_t iouring_recv(int fd, void* buf, size_t len, int flags, uint64_t timeout_us) {
    return photon::iouring_recv(fd, buf, len, flags, timeout_us);
}

ssize_t iouring_send(int fd, const void* buf, size_t len, int flags, uint64_t timeout_us) {
    return photon::iouring_send(fd, buf, len, flags, timeout_us);
}

ssize_t iouring_recvmsg(int fd, msghdr* msg, int flags, uint64_t timeout_us) {
    return photon::iouring_recvmsg(fd, msg, flags, timeout_us);
}

ssize_t iouring_sendmsg(int fd, const msghdr* msg, int flags, uint64_t timeout_us) {
    return photon::iouring_sendmsg(fd, msg, flags, timeout_us);
}

int iouring_accept(int fd, sockaddr* addr, socklen_t* addrlen, uint64_t timeout_us) {
    return photon::iouring_accept(fd, addr, addrlen, timeout_us);
}

int iouring_connect(int fd, const sockaddr* addr, socklen_t addrlen, uint64_t timeout_us) {
    return photon::iouring_connect(fd, addr, addrlen, timeout_us);
}

int iouring_fsync(int fd) { return photon::iouring_fsync(fd); }

int iouring_fdatasync(int fd) { return photon::iouring_fdatasync(fd); }

int iouring_open(const char* path, int flags, mode_t mode) {
    return photon::iouring_open(path, flags, mode);
}

int iouring_mkdir(const char* path, mode_t mode) { return photon::iouring_mkdir(path, mode); }

int iouring_close(int fd) { return photon::iouring_close(fd); }

#else  // FIBER_ENABLE_URING

ssize_t iouring_read(int fd, void* buf, size_t count, uint64_t timeout_us) {
    return -1;
}
ssize_t iouring_write(int fd, const void* buf, size_t count, uint64_t timeout_us) {
    return -1;
}
ssize_t iouring_readv(int fd, const iovec* iov, int iovcnt, uint64_t timeout_us) {
    return -1;
}
ssize_t iouring_writev(int fd, const iovec* iov, int iovcnt, uint64_t timeout_us) {
    return -1;
}
ssize_t iouring_pread(int fd, void* buf, size_t count, off_t offset, uint64_t timeout_us) {
    return -1;
}
ssize_t iouring_pwrite(int fd, const void* buf, size_t count, off_t offset,
                       uint64_t timeout_us) {
    return -1;
}
ssize_t iouring_preadv(int fd, const iovec* iov, int iovcnt, off_t offset,
                       uint64_t timeout_us) {
    return -1;
}
ssize_t iouring_pwritev(int fd, const iovec* iov, int iovcnt, off_t offset,
                        uint64_t timeout_us) {
    return -1;
}
ssize_t iouring_recv(int fd, void* buf, size_t len, int flags, uint64_t timeout_us) {
    return -1;
}
ssize_t iouring_send(int fd, const void* buf, size_t len, int flags, uint64_t timeout_us) {
    return -1;
}
ssize_t iouring_recvmsg(int fd, msghdr* msg, int flags, uint64_t timeout_us) {
    return -1;
}
ssize_t iouring_sendmsg(int fd, const msghdr* msg, int flags, uint64_t timeout_us) {
    return -1;
}
int iouring_accept(int fd, sockaddr* addr, socklen_t* addrlen, uint64_t timeout_us) {
    return -1;
}
int iouring_connect(int fd, const sockaddr* addr, socklen_t addrlen, uint64_t timeout_us) {
    return -1;
}
int iouring_fsync(int fd) {
    return -1;
}
int iouring_fdatasync(int fd) {
    return -1;
}
int iouring_open(const char* path, int flags, mode_t mode) {
    return -1;
}
int iouring_mkdir(const char* path, mode_t mode) {
    return -1;
}
int iouring_close(int fd) {
    return -1;
}

#endif


#define wait_and_doio(io_syscall, wait)                \
    while (true) {                                     \
        ssize_t ret = io_syscall;                      \
        if (ret >= 0) {                                \
            return ret;                                \
        }                                              \
        if (errno == EINTR) {                          \
            continue;                                  \
        }                                              \
        if (errno == EAGAIN || errno == EWOULDBLOCK) { \
            int err = wait;                            \
            if (err != 0) {                            \
                errno = err;                           \
                return -1;                             \
            }                                          \
        } else {                                       \
            return ret;                                \
        }                                              \
    }

ssize_t recv(int fd, void* buf, size_t len, int flags, uint64_t timeout_us) {
    wait_and_doio(::recv(fd, buf, len, flags), WaitForEvent(fd, EVENT_IN, timeout_us));
}

ssize_t send(int fd, const void* buf, size_t len, int flags, uint64_t timeout_us) {
    wait_and_doio(::send(fd, buf, len, flags), WaitForEvent(fd, EVENT_OUT, timeout_us));
}

ssize_t recvmsg(int fd, msghdr* msg, int flags, uint64_t timeout_us) {
    wait_and_doio(::recvmsg(fd, msg, flags), WaitForEvent(fd, EVENT_IN, timeout_us));
}

ssize_t sendmsg(int fd, const msghdr* msg, int flags, uint64_t timeout_us) {
    wait_and_doio(::sendmsg(fd, msg, flags), WaitForEvent(fd, EVENT_OUT, timeout_us));
}

int accept(int fd, sockaddr* addr, socklen_t* addrlen, uint64_t timeout_us) {
    wait_and_doio(::accept(fd, addr, addrlen), WaitForEvent(fd, EVENT_IN, timeout_us));
}

int connect(int fd, const sockaddr* addr, socklen_t addrlen, uint64_t timeout_us) {
    while (true) {
        int ret = ::connect(fd, addr, addrlen);
        if (ret == 0) {
            return 0;
        }
        if (errno == EINTR) {
            continue;
        }
        if (errno == EAGAIN || errno == EINPROGRESS) {
            int err = WaitForEvent(fd, EVENT_OUT, timeout_us);
            if (err != 0) {
                errno = err;
                return -1;
            }
            socklen_t n = sizeof(err);
            ret = getsockopt(fd, SOL_SOCKET, SO_ERROR, &err, &n);
            if (ret < 0) {
                return -1;
            }
            if (err != 0) {
                errno = err;
                return -1;
            }
        }
        return ret;
    }
}

}  // namespace fiber
}  // namespace byte
