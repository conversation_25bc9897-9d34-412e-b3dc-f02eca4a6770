/*
Copyright 2022 The Photon Authors

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/
#pragma once

#include <cstdint>
#include <cstdlib>

// Now for internal use ONLY, user should use fiber local by
// including `fiber_local.h`.
namespace byte {
namespace fiber {
constexpr size_t kFiberKeysMax = 1024;

struct FiberLocalStorage;

using FiberKeyType = uint64_t;

int FiberKeyCreate(FiberKeyType* key, void (*dtor)(void*));

void* FiberGetSpecific(FiberKeyType key);

int FiberSetSpecific(FiberKeyType key, const void* value);

int FiberKeyDelete(FiberKeyType key);

void DeallocateFLS(void** flsptr = nullptr);

}  // namespace fiber
}  // namespace byte
