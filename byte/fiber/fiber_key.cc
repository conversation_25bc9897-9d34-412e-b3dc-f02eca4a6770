/*
Copyright 2022 The Photon Authors

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/
#include "byte/fiber/fiber_key.h"

#include <cstring>

#include "byte/fiber/errno.h"
#include "byte/fiber/fiber.h"

namespace byte {
namespace fiber {

constexpr size_t kFiberKey2ndLevelSize = 32;
constexpr size_t kFiberKey1stLevelSize =
    (kFiberKeysMax + kFiberKey2ndLevelSize - 1) / kFiberKey2ndLevelSize;
constexpr size_t kFiberDestructorIterations = 4;

/* An even sequence number means the key slot is unused */
static inline bool KeyUnused(uint64_t key) { return (key & 1) == 0; }

/* A program would have to create and destroy a key 2^63 times to overflow the sequence */
static inline bool KeyUsable(uint64_t key) { return key < key + 2; }

struct FiberKeyData {
    uint64_t seq;
    void* data;
};

// Per thread storage for key data
struct FiberLocalStorage {
    FiberLocalStorage() { specific[0] = specific_1stblock; }
    FiberKeyData specific_1stblock[kFiberKey2ndLevelSize] = {};
    FiberKeyData* specific[kFiberKey1stLevelSize] = {};
    bool specific_used = false;
};

struct FiberKeyStruct {
    uint64_t seq;
    void (*dtor)(void*);
};

// Global keys
static FiberKeyStruct globalFiberKeys[kFiberKeysMax] = {};

int FiberKeyCreate(FiberKeyType* key, void (*destr)(void*)) {
    for (uint64_t index = 0; index < kFiberKeysMax; ++index) {
        /* Find a slot in globalFiberKeys which is unused. */
        uint64_t seq = globalFiberKeys[index].seq;
        if (KeyUnused(seq) && KeyUsable(seq) &&
            __sync_bool_compare_and_swap(&globalFiberKeys[index].seq, seq, seq + 1)) {
            globalFiberKeys[index].dtor = destr;
            *key = index;
            return 0;
        }
    }
    return EAGAIN;
}

void* FiberGetSpecific(FiberKeyType key) {
    auto fls = reinterpret_cast<FiberLocalStorage*>(*GetCurrentFiberFLS());
    if (!fls) {
        return nullptr;
    }
    FiberKeyData* data;
    if (key < kFiberKey2ndLevelSize) {
        /* Special case access to the first 2nd-level block. This is the usual case. */
        data = &fls->specific_1stblock[key];
    } else {
        if (key >= kFiberKeysMax) return nullptr;
        uint64_t idx1st = key / kFiberKey2ndLevelSize;
        uint64_t idx2nd = key % kFiberKey2ndLevelSize;
        FiberKeyData* level2 = fls->specific[idx1st];
        if (level2 == nullptr) /* Not allocated, therefore no data. */
            return nullptr;
        data = &level2[idx2nd];
    }
    // Check seq against the globalFiberKeys
    void* result = data->data;
    if (result != nullptr) {
        if (data->seq != globalFiberKeys[key].seq) result = data->data = nullptr;
    }
    return result;
}

int FiberSetSpecific(FiberKeyType key, const void* value) {
    if (key >= kFiberKeysMax) {
        return EINVAL;
    }
    uint64_t seq = globalFiberKeys[key].seq;
    if (KeyUnused(seq)) {
        return EINVAL;
    }

    /* If not in fiber context, the void** will return nullptr */
    auto* fls_ptr = GetCurrentFiberFLS();
    if (!fls_ptr) {
        return EINVAL;
    }
    auto& fls = reinterpret_cast<FiberLocalStorage*&>(*fls_ptr);
    if (fls == nullptr) {
        fls = new FiberLocalStorage;
    }

    FiberKeyData* level2;
    if (key < kFiberKey2ndLevelSize) {
        /* Special case access to the first 2nd-level block. This is the usual case. */
        level2 = &fls->specific_1stblock[key];
        if (value != nullptr) fls->specific_used = true;
    } else {
        /* This is the second level array.  Allocate it if necessary. */
        uint64_t idx1st = key / kFiberKey2ndLevelSize;
        uint64_t idx2nd = key % kFiberKey2ndLevelSize;
        level2 = fls->specific[idx1st];
        if (level2 == nullptr) {
            if (value == nullptr) return 0;
            level2 =
                reinterpret_cast<FiberKeyData*>(calloc(kFiberKey2ndLevelSize, sizeof(*level2)));
            if (level2 == nullptr) return ENOMEM;
            fls->specific[idx1st] = level2;
        }
        level2 = &level2[idx2nd];
        fls->specific_used = true;
    }

    level2->seq = seq;
    level2->data = const_cast<void*>(value);
    return 0;
}

int FiberKeyDelete(FiberKeyType key) {
    int result = EINVAL;
    if (key < kFiberKeysMax) {
        uint64_t seq = globalFiberKeys[key].seq;
        if (!KeyUnused(seq) &&
            __sync_bool_compare_and_swap(&globalFiberKeys[key].seq, seq, seq + 1))
            /* We deleted a valid key by making the seq even again */
            result = 0;
    }
    return result;
}

void DeallocateFLS(void** flsptr) {
    size_t round = 0;
    auto ptr = flsptr ? *flsptr : *GetCurrentFiberFLS();
    auto fls = reinterpret_cast<FiberLocalStorage*>(ptr);
    if (!fls) return;                       /* No key was ever created */
    if (!fls->specific_used) goto free_fls; /* No specific was ever set */

    do {
        uint64_t idx = 0;
        fls->specific_used = false;
        for (auto level2 : fls->specific) {
            if (level2 != nullptr) {
                for (uint64_t inner = 0; inner < kFiberKey2ndLevelSize; ++inner, ++idx) {
                    void* data = level2[inner].data;
                    if (data == nullptr) {
                        continue;
                    }
                    /* Always clear the data.  */
                    level2[inner].data = nullptr;
                    /* Make sure the data corresponds to a valid key. */
                    if (level2[inner].seq == globalFiberKeys[idx].seq &&
                        globalFiberKeys[idx].dtor != nullptr)
                        /* Call the user-provided destructor.  */
                        globalFiberKeys[idx].dtor(data);
                }
            } else {
                idx += kFiberKey1stLevelSize;
            }
        }
        if (!fls->specific_used) {
            /* Usually no data has been modified,
             * unless users have called thread_setspecific in the destructor */
            goto free_key;
        }
    } /* We only repeat the process a fixed number of times. */
    while (++round < kFiberDestructorIterations);  // NOLINT

    /* Just clear the memory of the first block for reuse.  */
    memset(&fls->specific_1stblock, 0, sizeof(fls->specific_1stblock));

free_key:
    /* Free the memory for the other blocks.  */
    for (uint64_t cnt = 1; cnt < kFiberKey1stLevelSize; ++cnt) {
        FiberKeyData* level2 = fls->specific[cnt];
        if (level2 != nullptr) {
            free(level2);
            fls->specific[cnt] = nullptr;
        }
    }
    fls->specific_used = false;

free_fls:
    delete fls;
    if (flsptr) *flsptr = nullptr;
}

}  // namespace fiber
}  // namespace byte
