/*
Copyright 2022 The Photon Authors

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/
#pragma once

#include <functional>
#include <limits>
#include <memory>
#include <tuple>
#include <utility>

#include "byte/fiber/fiber_key.h"
#include "byte/include/macros.h"

namespace byte {
namespace fiber {

// Implementation of some tuple auxiliary tool
namespace tuple_assistance {
template <class F>
struct callable;

// function pointer
template <class R, class... Args>
struct callable<R (*)(Args...)> : public callable<R(Args...)> {};

template <class R, class... Args>
struct callable<R(Args...)> {
    using return_type = R;

    using arguments = std::tuple<Args...>;
};

// member function pointer
template <class C, class R, class... Args>
struct callable<R (C::*)(Args...)> : public callable<R(C&, Args...)> {};

// const member function pointer
template <class C, class R, class... Args>
struct callable<R (C::*)(Args...) const> : public callable<R(C&, Args...)> {};

// member object pointer
template <class C, class R>
struct callable<R(C::*)> : public callable<R(C&)> {};

template <typename T>
struct __remove_first_type_in_tuple {};

template <typename T, typename... Ts>
struct __remove_first_type_in_tuple<std::tuple<T, Ts...>> {
    typedef std::tuple<Ts...> type;
};

// functor
template <class F>
struct callable {
    using call_type = callable<decltype(&F::operator())>;

    using return_type = typename call_type::return_type;

    using arguments = typename __remove_first_type_in_tuple<typename call_type::arguments>::type;
};

template <class F>
struct callable<F&> : public callable<F> {};

template <class F>
struct callable<F&&> : public callable<F> {};

#if __cplusplus < 201700
template <typename F, typename Tuple, std::size_t... I>
constexpr inline decltype(auto) apply_impl(F&& f, Tuple&& t, std::index_sequence<I...>) {
    using Args = typename callable<F>::arguments;
    return f(std::forward<typename std::tuple_element<I, Args>::type>(std::get<I>(t))...);
}

// Implementation of a simplified std::apply from C++17
template <typename F, typename Tuple>
constexpr inline decltype(auto) apply(F&& f, Tuple&& t) {
    return apply_impl(
        std::forward<F>(f), std::forward<Tuple>(t),
        std::make_index_sequence<std::tuple_size<std::remove_reference_t<Tuple>>::value>{});
}

#else
using std::apply;
#endif
}  // namespace tuple_assistance

// !!NOTE: Can ONLY be used in fiber context.
// !!NOTE: A maximum of *1024* fiber local variables can be created globally.
// Usage expamle:
// - Create fiber local Class with ctor param:
//      fiber::FiberLocalPtr<DummyClass, const char*> valClass("value");
// - Create fiber local pod value:
//      fiber::FiberLocalPtr<int> valPod1;
// - Create fiber local pod value with ctor param:
//      fiber::FiberLocalPtr<int, int> valPod2(3);
//
// It is common to create these variables in the global scope,
// and then use them in the fiber to exhibit the fiber local characteristic.
template <typename T, typename... ARGS>
class FiberLocalPtr {
public:
    explicit FiberLocalPtr(ARGS&&... args) : args_(std::forward<ARGS>(args)...) {
        int ret = FiberKeyCreate(&key_, &dtor);
        if (ret != 0) abort();
    }

    ~FiberLocalPtr() = default;

    T& operator*() { return *get(); }

    T* operator->() const { return get(); }

    // NOTE: Use carefully and be aware of the object's life cycle
    T* get() const __attribute__((const)) {
        void* data = FiberGetSpecific(key_);
        if (!data) {
            struct ctor {
                T* operator()(const ARGS&... args) { return new T(args...); }
            };
            data = tuple_assistance::apply(ctor(), args_);
            if (data == nullptr) {
                abort();
            }
            if (FiberSetSpecific(key_, data) != 0) abort();
        }
        return reinterpret_cast<T*>(data);
    }

private:
    static void dtor(void* data) { delete reinterpret_cast<T*>(data); }

    std::tuple<ARGS...> args_;
    FiberKeyType key_ = std::numeric_limits<FiberKeyType>::max();

    DISALLOW_COPY_AND_ASSIGN(FiberLocalPtr);
};

}  // namespace fiber
}  // namespace byte
