// Copyright (c) 2023-present, ByteDance Inc. All rights reserved.
#pragma once

#include <sys/socket.h>

#include <cstdint>

namespace byte {
namespace fiber {

/**
 * IO syscalls based on io_uring. Only works when event_engine is set to IOURING in ThreadInit.
 * The current fiber will block until:
 * 1. IO is successfully done or error happens, with the same returned value and errno as syscalls
 * of Standard C library.
 * 2. IO fails to be done within `timeout_us`, with -1 returned and errno = ETIMEDOUT.
 * 3. fiber is Awakened by user, with -1 returned and errno = EINTR. Note that in such case, it is
 * not sure whether IO is done or is canceled. User should be responsible for discarding the effect
 * of any result.
 */

ssize_t iouring_read(int fd, void* buf, size_t count, uint64_t timeout_us = -1);
ssize_t iouring_write(int fd, const void* buf, size_t count, uint64_t timeout_us = -1);
ssize_t iouring_readv(int fd, const iovec* iov, int iovcnt, uint64_t timeout_us = -1);
ssize_t iouring_writev(int fd, const iovec* iov, int iovcnt, uint64_t timeout_us = -1);
ssize_t iouring_pread(int fd, void* buf, size_t count, off_t offset, uint64_t timeout_us = -1);
ssize_t iouring_pwrite(int fd, const void* buf, size_t count, off_t offset,
                       uint64_t timeout_us = -1);
ssize_t iouring_preadv(int fd, const iovec* iov, int iovcnt, off_t offset,
                       uint64_t timeout_us = -1);
ssize_t iouring_pwritev(int fd, const iovec* iov, int iovcnt, off_t offset,
                        uint64_t timeout_us = -1);
ssize_t iouring_recv(int fd, void* buf, size_t len, int flags = 0, uint64_t timeout_us = -1);
ssize_t iouring_send(int fd, const void* buf, size_t len, int flags = 0, uint64_t timeout_us = -1);
ssize_t iouring_recvmsg(int fd, msghdr* msg, int flags = 0, uint64_t timeout_us = -1);
ssize_t iouring_sendmsg(int fd, const msghdr* msg, int flags = 0, uint64_t timeout_us = -1);
int iouring_accept(int fd, sockaddr* addr, socklen_t* addrlen, uint64_t timeout_us = -1);
int iouring_connect(int fd, const sockaddr* addr, socklen_t addrlen, uint64_t timeout_us = -1);
int iouring_fsync(int fd);
int iouring_fdatasync(int fd);
int iouring_open(const char* path, int flags, mode_t mode);
int iouring_mkdir(const char* path, mode_t mode);
int iouring_close(int fd);

/**
 * IO syscalls based on event-driven model. It works just like libc's blocking IO syscalls. Fd
 * should be non-blocking, otherwise the thread will block. These APIs are essentially implemented
 * by combining `WaitForEvent` with libc's IO syscalls, just for users' convenience.
 * The current fiber will block until:
 * 1. IO is successfully done or error happens, with the same returned value and errno as syscalls
 * of Standard C library.
 * 2. IO event is not ready yet in `timeout_us` (i.e. WaitForEvent gets time out), with -1 returned
 * and errno = ETIMEDOUT.
 * 3. fiber is Awakened by user when `WaitForEvent`ing, with -1 returned and errno = EINTR.
 * @warning It is not allowed that multi fibers do the same IO on the same fd simultaneously (Just
 * like `WaitForEvent`)
 */

ssize_t recv(int fd, void* buf, size_t len, int flags = 0, uint64_t timeout_us = -1);
ssize_t send(int fd, const void* buf, size_t len, int flags = 0, uint64_t timeout_us = -1);
ssize_t recvmsg(int fd, msghdr* msg, int flags = 0, uint64_t timeout_us = -1);
ssize_t sendmsg(int fd, const msghdr* msg, int flags = 0, uint64_t timeout_us = -1);
int accept(int fd, sockaddr* addr, socklen_t* addrlen, uint64_t timeout_us = -1);
int connect(int fd, const sockaddr* addr, socklen_t addrlen, uint64_t timeout_us = -1);

}  // namespace fiber
}  // namespace byte
