// Copyright (c) 2023-present, ByteDance Inc. All rights reserved.
#include "byte/fiber/fiber_local.h"

#include <string>

#include "byte/fiber/fiber.h"
#include "byte/fiber/fiber_pool.h"
#include "byte/util/defer.h"
#include "gtest/gtest.h"

namespace byte {
namespace fiber {

static int expected_times = 0;
// Finalizer is destructed after Value
struct Finalizer {
    ~Finalizer() {
        if (destruct_times != expected_times) {
            abort();
        }
    }
    int destruct_times = 0;
};

struct Value {
    Value() : empty(true) {}
    explicit Value(std::string s) : v(std::move(s)) {}
    ~Value() {
        static Finalizer f;
        if (empty) f.destruct_times++;
    }
    std::string v;
    bool empty = false;
};

static fiber::FiberLocalPtr<Value>& GetV1() {
    static fiber::FiberLocalPtr<Value> v1;
    return v1;
}

void* FuncFiberStructTest(void* args) {
    auto& v1 = GetV1();
    v1->v = "value";
    ++expected_times;
    return nullptr;
}

void* FuncFiberStructTest2(void* args) {
    auto& v1 = GetV1();
    EXPECT_TRUE(v1->empty);
    ++expected_times;
    return nullptr;
}

TEST(FiberLocal, Struct) {
    fiber::ThreadInitOption th_opt;
    EXPECT_EQ(0, ThreadInit(th_opt));

    // Get a static FiberLocalPtr form different fiber
    fiber::CreateOpt fiber_opt;
    fiber_opt.joinable_ = true;
    fiber::Fiber* fiber = Create(fiber_opt, FuncFiberStructTest, nullptr);
    EXPECT_NE(fiber, nullptr);

    EXPECT_EQ(Join(fiber), 0);

    fiber::Fiber* fiber2 = Create(fiber_opt, FuncFiberStructTest, nullptr);
    EXPECT_NE(fiber2, nullptr);

    EXPECT_EQ(Join(fiber2), 0);

    auto& v1 = GetV1();
    EXPECT_TRUE(v1->empty);
    ++expected_times;

    fiber::ThreadFini();
}

fiber::FiberLocalPtr<Value, const char*> v2("value");

void* FuncFiberStructWithParamTest(void* args) {
    EXPECT_FALSE(v2->v.empty());
    v2->v = "";
    EXPECT_TRUE(v2->v.empty());
    return nullptr;
}

void* FuncFiberPoolStructWithParamTest(void* args) {
    EXPECT_FALSE(v2->v.empty());
    int i = 1;
    for (; i < 100; ++i) {
        v2->v = std::string(i, 'a');
        fiber::Yield();
        EXPECT_EQ(v2->v, std::string(i, 'a'));
    }
    return nullptr;
}

TEST(FiberLocal, StructWithParam) {
    fiber::ThreadInitOption th_opt;
    EXPECT_EQ(0, ThreadInit(th_opt));

    // 1. Simple Fiber
    fiber::CreateOpt fiber_opt;
    fiber_opt.joinable_ = true;
    fiber::Fiber* fiber = Create(fiber_opt, FuncFiberStructWithParamTest, nullptr);
    EXPECT_NE(fiber, nullptr);

    EXPECT_EQ(Join(fiber), 0);

    EXPECT_FALSE(v2->v.empty());

    // 2. TlsFiberPool
    fiber::TlsFiberPool* tls_pool =
        new fiber::TlsFiberPool(10, 10, 8 * 1024 * 1024);
    for (int i = 0; i < 100; i++) {
        tls_pool->AddTask(FuncFiberPoolStructWithParamTest, nullptr);
    }
    tls_pool->WaitForAllTasksDone();
    EXPECT_FALSE(v2->v.empty());
    delete tls_pool;

    // 3. FiberPool
    fiber::FiberPool* pool =
        new fiber::FiberPool(10, 10, 8 * 1024 * 1024);
    for (int i = 0; i < 100; i++) {
        pool->AddTask(FuncFiberPoolStructWithParamTest, nullptr);
    }
    pool->WaitForAllTasksDone();
    EXPECT_FALSE(v2->v.empty());
    delete pool;

    fiber::ThreadFini();
}

static fiber::FiberLocalPtr<int, int> valPod1(3);
static fiber::FiberLocalPtr<int> valPod2;
void* FuncFiberPodTest(void* args) {
    EXPECT_EQ(3, *valPod1);
    *valPod1 = 4;
    EXPECT_EQ(4, *valPod1);
    EXPECT_EQ(0, *valPod2);
    return nullptr;
}

TEST(FiberLocal, POD) {
    fiber::ThreadInitOption th_opt;
    EXPECT_EQ(0, ThreadInit(th_opt));

    // 1. Simple Fiber
    fiber::CreateOpt fiber_opt;
    fiber_opt.joinable_ = true;
    fiber::Fiber* fiber = Create(fiber_opt, FuncFiberPodTest, nullptr);
    EXPECT_NE(fiber, nullptr);
    EXPECT_EQ(Join(fiber), 0);

    EXPECT_EQ(3, *valPod1);
    EXPECT_EQ(0, *valPod2);

    // 2. TlsFiberPool
    fiber::TlsFiberPool* tls_pool =
        new fiber::TlsFiberPool(1, 1, 8 * 1024 * 1024);
    for (int i = 0; i < 100; i++) {
        tls_pool->AddTask(FuncFiberPodTest, nullptr);
    }
    tls_pool->WaitForAllTasksDone();
    EXPECT_EQ(3, *valPod1);
    EXPECT_EQ(0, *valPod2);
    delete tls_pool;

    // 3. FiberPool
    fiber::FiberPool* pool =
        new fiber::FiberPool(1, 1, 8 * 1024 * 1024);
    for (int i = 0; i < 100; i++) {
        pool->AddTask(FuncFiberPodTest, nullptr);
    }
    pool->WaitForAllTasksDone();
    EXPECT_EQ(3, *valPod1);
    EXPECT_EQ(0, *valPod2);
    delete pool;

    fiber::ThreadFini();
}

}  // namespace fiber
}  // namespace byte

int main(int argc, char** argv) {
    testing::InitGoogleTest(&argc, argv);

    byte::fiber::GlobalInitOption opt;
    byte::fiber::GlobalInit(opt);

    auto global_fini_guard = byte::defer(byte::fiber::GlobalFini);

    return RUN_ALL_TESTS();
}
