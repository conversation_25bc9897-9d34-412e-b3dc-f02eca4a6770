// Copyright (c) 2023, ByteDance Inc. All rights reserved.
#include <atomic>
#include <condition_variable>  // NOLINT
#include <mutex>               // NOLINT
#include <string>
#include <thread>  // NOLINT

#include "benchmark/benchmark.h"
#include "byte/fiber/fiber.h"
#include "byte/fiber/synchronization.h"
#include "photon/thread/thread.h"

namespace byte {

const uint64_t kTestIteration = 5000000;

// common fiber SetUp/TearDown fixture
class FiberBench : public benchmark::Fixture {
public:
    void SetUp(const ::benchmark::State& state) override {
        fiber::ThreadInitOption opt;
        fiber::ThreadInit(opt);
    }

    void TearDown(const ::benchmark::State& state) override { fiber::ThreadFini(); }
};

// std::mutex
static void BM_Mutex(benchmark::State& state) {  // NOLINT(runtime/references)
    static std::mutex mtx;
    for (auto _ : state) {
        std::lock_guard<std::mutex> lock(mtx);
    }
}

BENCHMARK(BM_Mutex)->Threads(1)->Iterations(kTestIteration);
BENCHMARK(BM_Mutex)->Threads(2)->Iterations(kTestIteration);
BENCHMARK(BM_Mutex)->Threads(4)->Iterations(kTestIteration);
BENCHMARK(BM_Mutex)->Threads(8)->Iterations(kTestIteration);

// photon::mutex
BENCHMARK_DEFINE_F(FiberBench, PhotonMutexWithVCpu)
(benchmark::State& state) {  // NOLINT(runtime/references)
    static photon::mutex mtx;
    for (auto _ : state) {
        photon::locker<photon::mutex> lock(mtx);
    }
}

BENCHMARK_REGISTER_F(FiberBench, PhotonMutexWithVCpu)->Threads(1)->Iterations(kTestIteration);
BENCHMARK_REGISTER_F(FiberBench, PhotonMutexWithVCpu)->Threads(2)->Iterations(kTestIteration);
BENCHMARK_REGISTER_F(FiberBench, PhotonMutexWithVCpu)->Threads(4)->Iterations(kTestIteration);
BENCHMARK_REGISTER_F(FiberBench, PhotonMutexWithVCpu)->Threads(8)->Iterations(kTestIteration);

static void BM_PhotonMutexWithPthread(benchmark::State& state) {  // NOLINT(runtime/references)
    static photon::mutex mtx;
    for (auto _ : state) {
        photon::locker<photon::mutex> lock(mtx);
    }
}

BENCHMARK(BM_PhotonMutexWithPthread)->Threads(1)->Iterations(kTestIteration);
BENCHMARK(BM_PhotonMutexWithPthread)->Threads(2)->Iterations(kTestIteration);
BENCHMARK(BM_PhotonMutexWithPthread)->Threads(4)->Iterations(kTestIteration);
BENCHMARK(BM_PhotonMutexWithPthread)->Threads(8)->Iterations(kTestIteration);

// fiber::Mutex
BENCHMARK_DEFINE_F(FiberBench, Mutex)(benchmark::State& state) {  // NOLINT(runtime/references)
    static fiber::Mutex mtx;
    for (auto _ : state) {
        fiber::LockGuard<fiber::Mutex> lock(mtx);
    }
}

BENCHMARK_REGISTER_F(FiberBench, Mutex)->Threads(1)->Iterations(kTestIteration);
BENCHMARK_REGISTER_F(FiberBench, Mutex)->Threads(2)->Iterations(kTestIteration);
BENCHMARK_REGISTER_F(FiberBench, Mutex)->Threads(4)->Iterations(kTestIteration);
BENCHMARK_REGISTER_F(FiberBench, Mutex)->Threads(8)->Iterations(kTestIteration);

BENCHMARK_DEFINE_F(FiberBench, MutexCreate)
(benchmark::State& state) {  // NOLINT(runtime/references)
    for (auto _ : state) {
        auto* tmp = new fiber::Mutex;
        delete tmp;
    }
}

BENCHMARK_DEFINE_F(FiberBench, MutexDelete)
(benchmark::State& state) {  // NOLINT(runtime/references)
    std::vector<fiber::Mutex*> vec;
    vec.resize(kTestIteration);
    for (uint64_t i = 0; i < kTestIteration; ++i) {
        vec.emplace_back(new fiber::Mutex);
    }
    for (auto _ : state) {
        delete vec[state.iterations()];
    }
}

BENCHMARK_REGISTER_F(FiberBench, MutexCreate)->Iterations(kTestIteration);
BENCHMARK_REGISTER_F(FiberBench, MutexDelete)->Iterations(kTestIteration);

// std::condition_variable
static void BM_ConditionVariable(benchmark::State& state) {  // NOLINT(runtime/references)
    static std::mutex mutex;
    static std::condition_variable cond_var;
    static bool data_ready = false;

    for (auto _ : state) {
        // consumer
        state.PauseTiming();
        std::vector<std::thread> consumers;
        for (int i = 0; i < state.range(0); ++i) {
            consumers.emplace_back([&]() {
                std::unique_lock<std::mutex> lock(mutex);
                cond_var.wait(lock, [] { return data_ready; });
            });
        }
        state.ResumeTiming();

        // producer
        {
            std::lock_guard<std::mutex> lock(mutex);
            data_ready = true;
        }
        cond_var.notify_all();

        state.PauseTiming();
        for (auto&& th : consumers) {
            th.join();
        }
        // Reset the condition for the next iteration
        data_ready = false;
        state.ResumeTiming();
    }
}

BENCHMARK(BM_ConditionVariable)->Iterations(100000)->Arg(1);
BENCHMARK(BM_ConditionVariable)->Iterations(100000)->Arg(4);
BENCHMARK(BM_ConditionVariable)->Iterations(100000)->Arg(8);

// fiber::CondVar
// TODO(fza): There is some problem when reusing a mutex among iterations
BENCHMARK_DEFINE_F(FiberBench,
                   CondVar)(benchmark::State& state) {  // NOLINT(runtime/references)
    static fiber::Mutex* mutex = nullptr;
    static fiber::CondVar cond_var;
    static bool data_ready = false;
    static std::atomic<int> num{0};

    for (auto _ : state) {
        // consumer
        state.PauseTiming();
        mutex = new fiber::Mutex;
        std::vector<std::thread> consumers;
        for (int i = 0; i < state.range(0); ++i) {
            consumers.emplace_back([&]() {
                state.PauseTiming();
                fiber::ThreadInitOption opt;
                fiber::ThreadInit(opt);
                state.ResumeTiming();

                fiber::LockGuard<fiber::Mutex> lock(*mutex);
                while (!data_ready) {
                    ++num;
                    cond_var.Wait(*mutex);
                }

                state.PauseTiming();
                fiber::ThreadFini();
                state.ResumeTiming();
            });
        }
        state.ResumeTiming();

        state.PauseTiming();
        while (num.load(std::memory_order_relaxed) != state.range(0)) {
        }
        state.ResumeTiming();

        // producer
        {
            fiber::LockGuard<fiber::Mutex> lock(*mutex);
            data_ready = true;
        }
        cond_var.NotifyAll();

        state.PauseTiming();
        for (auto&& th : consumers) {
            th.join();
        }
        // Reset the condition for the next iteration
        data_ready = false;
        delete mutex;
        state.ResumeTiming();
    }
}

BENCHMARK_REGISTER_F(FiberBench, CondVar)->Iterations(10000)->Arg(1);
BENCHMARK_REGISTER_F(FiberBench, CondVar)->Iterations(100000)->Arg(4);
BENCHMARK_REGISTER_F(FiberBench, CondVar)->Iterations(100000)->Arg(8);

}  // namespace byte

//  Run the benchmark
void GlobalSetup() {
    byte::fiber::GlobalInitOption opt;
    opt.enable_fiber_log = true;
    byte::fiber::GlobalInit(opt);
}

void GlobalTeardown() { byte::fiber::GlobalFini(); }

int main(int argc, char** argv) {
    GlobalSetup();
    benchmark::Initialize(&argc, argv);
    if (benchmark::ReportUnrecognizedArguments(argc, argv)) {
        return 1;
    }
    benchmark::RunSpecifiedBenchmarks();
    GlobalTeardown();

    return 0;
}
