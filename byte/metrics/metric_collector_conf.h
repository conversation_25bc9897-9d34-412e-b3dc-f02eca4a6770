// Copyright (c) 2018-present, ByteDance Inc. All rights reserved.

#pragma once

#include <string>

namespace byte {
namespace metrics2 {

/**
 * MetricCollector 配置项示例:
 *  # 后端类型，支持stdout,file,opentsdb. 多个后端间逗号分隔. 默认为stdout
 *  metrics_enabled_backends: ganglia,file
 *
 *  # OpenTSDB服务端点，多个端点间逗号分隔
 *  metrics_backend_opentsdb_endpoints: *************:8400
 *
 *  # 汇报周期，单位为秒，默认值为10秒。
 *  # 约定同一个服务所有metrics使用统一的flush周期，
 *  # 暂不支持为每个metric设置不同的flush周期
 *  metrics_flush_interval: 10
 *
 *  # 定义本服务所有metric的公共前缀。所有api中name参数可以省略该前缀。默认为空
 *  metrics_namespace_prefix: bytedance.recommend.sort
 */
class MetricCollectorConf {
public:
    std::string namespace_prefix;
    std::string udp_server_ip;
    int udp_server_port;
    std::string sock_path;
    size_t send_batch_size;
    int auto_batch;
    int metrics1_retry;
    std::string psm;

    MetricCollectorConf()
        : udp_server_ip("127.0.0.1"),
          udp_server_port(9123),
          send_batch_size(1),
          auto_batch(0),
          metrics1_retry(0) {
        char* c_psm = std::getenv("LOAD_SERVICE_PSM");
        if (c_psm) {
            psm = std::string(c_psm);
        } else {
            psm = "data.default.alert";
        }
    }
};

} /* namespace metrics2 */
} /* namespace byte */
