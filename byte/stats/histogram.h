// Copyright (c) 2018-present, ByteDance Inc. All rights reserved.

//  Copyright (c) 2013, Facebook, Inc.  All rights reserved.
//  This source code is licensed under the BSD-style license found in the
//  LICENSE file in the root directory of this source tree. An additional grant
//  of patent rights can be found in the PATENTS file in the same directory.
//
// Copyright (c) 2011 The LevelDB Authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file. See the AUTHORS file for names of contributors.

#pragma once

#include <string.h>
#include <cassert>
#include <cstdint>
#include <string>
#include <vector>
#include <map>

namespace byte {

struct HistogramData {
  double median;
  double percentile95;
  double percentile99;
  double percentile999;
  double average;
  double standard_deviation;
  double max = 0.0;
  // zero-initialize new members since old Statistics::histogramData()
  // implementations won't write them.
  uint64_t count = 0;
  uint64_t sum = 0;
};

class HistogramBucketMapper {
public:
  explicit HistogramBucketMapper(bool direct_mapping = false);

  // converts a value to the bucket index.
  size_t IndexForValue(const uint64_t value) const;

  // number of buckets required.
  size_t BucketCount() const {
    return direct_mapping_ ? directValues_.size() : bucketValues_.size();
  }

  uint64_t LastValue() const {
    return maxBucketValue_;
  }

  uint64_t FirstValue() const {
    return minBucketValue_;
  }

  bool IsDirectMapping() const {
    return direct_mapping_;
  }

  uint64_t BucketLimit(const size_t bucketNumber) const {
    assert(bucketNumber < BucketCount());
    if (direct_mapping_) {
      return directValues_[bucketNumber];
    } else {
      return bucketValues_[bucketNumber];
    }
  }

private:
  std::vector<uint64_t> bucketValues_;
  std::vector<uint64_t> directValues_;
  uint64_t maxBucketValue_;
  uint64_t minBucketValue_;
  bool direct_mapping_;
  std::map<uint64_t, uint64_t> valueIndexMap_;
};

class HistogramImpl {
public:
  explicit HistogramImpl(bool direct_mapping = false);
  virtual void Clear();
  virtual bool Empty();
  virtual void Add(uint64_t value);
  void Merge(const HistogramImpl& other);

  virtual std::string ToString() const;

  virtual uint64_t Num() const;
  virtual double Median() const;
  virtual double Percentile(double p) const;
  virtual double Average() const;
  virtual double StandardDeviation() const;
  virtual void Data(HistogramData * const data) const;

  virtual ~HistogramImpl() {}

private:
  // To be able to use HistogramImpl as thread local variable, its constructor
  // has to be static. That's why we're using manually values from BucketMapper
  double min_ = 1000000000;  // this is BucketMapper:LastValue()
  double max_ = 0;
  double num_ = 0;
  double sum_ = 0;
  double sum_squares_ = 0;
  uint64_t buckets_[138];  // this is BucketMapper::BucketCount()
  const HistogramBucketMapper* bucketMapper_ = nullptr;
};

}  // namespace byte
