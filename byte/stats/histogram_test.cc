// Copyright (c) 2018-present, ByteDance Inc. All rights reserved.

//  Copyright (c) 2013, Facebook, Inc.  All rights reserved.
//  This source code is licensed under the BSD-style license found in the
//  LICENSE file in the root directory of this source tree. An additional grant
//  of patent rights can be found in the PATENTS file in the same directory.
//

#include "byte/stats/histogram.h"

#include <cstdint>
#include <limits>

#include "byte/system/timestamp.h"
#include "gflags/gflags.h"
#include "gtest/gtest-death-test.h"
#include "gtest/gtest.h"

namespace byte {
namespace {

class HistogramTest : public testing::Test {};

void CheckHistogramBasicOperations(bool is_direct_mapping) {
  // Default constructor will use is_direct_mapping = false
  HistogramImpl histogram(is_direct_mapping);
  for (uint64_t i = 0; i <= 100; i++) {
    histogram.Add(i);
  }

  // Median and percentiles are rough estimates of the exact values
  // They may be larger or smaller than their corresponding precise values
  // But the relative orders among percentiles are guarranteed
  // for example, min <= p50 <= p85 <= p99 <= p100 <= max
  double median = histogram.Median();
  double p50 = histogram.Percentile(50.0);
  ASSERT_DOUBLE_EQ(median, p50);
  double min = 0.0;
  ASSERT_GE(p50, min);
  double p85 = histogram.Percentile(85.0);
  double p99 = histogram.Percentile(99.0);
  double p100 = histogram.Percentile(100.0);
  ASSERT_GE(p85, p50);
  ASSERT_GE(p99, p85);
  ASSERT_GE(p100, p99);

  double average = histogram.Average();
  ASSERT_DOUBLE_EQ(average, 50.0);  // avg is accurately calculated.
  double standard_deviation = histogram.StandardDeviation();
  ASSERT_GT(standard_deviation, 0.0);

  HistogramData data;
  histogram.Data(&data);
  ASSERT_EQ(data.count, 101);
  ASSERT_EQ(data.sum, 5050);
  ASSERT_EQ(data.average, 50);
  ASSERT_GT(data.median, 0);
  ASSERT_GT(data.percentile95, 0);
  ASSERT_GT(data.percentile99, 0);
  ASSERT_GT(data.percentile999, 0);
  if (!is_direct_mapping) {
    ASSERT_LT(data.percentile999, 100);
  }
  ASSERT_GT(data.standard_deviation, 0);
}

void CheckHistogramP999(bool is_direct_mapping) {
  // Default constructor will use is_direct_mapping = false
  HistogramImpl histogram(is_direct_mapping);
  for (uint64_t i = 0; i <= 1000; i++) {
    histogram.Add(i);
  }

  HistogramData data;
  histogram.Data(&data);
  ASSERT_GT(data.percentile999, 0);
  if (!is_direct_mapping) {
    ASSERT_LT(data.percentile999, 1000);
  }
}

TEST_F(HistogramTest, BasicOperation) {
  CheckHistogramBasicOperations(false);
  CheckHistogramBasicOperations(true);
  CheckHistogramP999(false);
  CheckHistogramP999(true);
}

TEST_F(HistogramTest, EmptyHistogram) {
  HistogramImpl histogram;
  ASSERT_EQ(histogram.Median(), 0.0);
  ASSERT_EQ(histogram.Percentile(85.0), 0.0);
  ASSERT_EQ(histogram.Average(), 0.0);
}

TEST_F(HistogramTest, ClearHistogram) {
  HistogramImpl histogram;
  for (uint64_t i = 0; i <= 100; i++) {
    histogram.Add(i);
  }
  histogram.Clear();
  ASSERT_EQ(histogram.Median(), 0);
  ASSERT_EQ(histogram.Percentile(85.0), 0);
  ASSERT_EQ(histogram.Average(), 0);
}

TEST_F(HistogramTest, CrashWhenMergingDirectWithNonDirect) {
  HistogramImpl direct(/*direct_mapping=*/true);
  HistogramImpl non_direct(/*direct_mapping=*/false);
  gflags::FlagSaver saver;
  ::testing::FLAGS_gtest_death_test_style = "threadsafe";
  ASSERT_DEBUG_DEATH(direct.Merge(non_direct), "");
}

TEST(HistogramBucketMapperTest, IndexForValueReturnsCorrectlyInDirectMode) {
    HistogramBucketMapper direct_mapper(/*direct_mapping=*/true);
    ASSERT_EQ(direct_mapper.IndexForValue(std::numeric_limits<uint64_t>::max()),
              direct_mapper.BucketCount() - 1);
    // A meaningful mapping should at least guarantee the numbers below are mapped to a list
    // of increasing indexes.
    int n = 8;  // the max number is 10^(n-1)
    std::vector<uint64_t> vec(n);
    // generated a list of numbers with enough gaps among them
    uint64_t value = 1;
    for (int i = 0; i < n; ++i) {
        vec[i] = value;
        value *= 10;
    }
    size_t last_index = direct_mapper.IndexForValue(vec.front());
    for (int i = 1; i < n; ++i) {
        size_t cur_index = direct_mapper.IndexForValue(vec[i]);
        ASSERT_LT(last_index, cur_index);
        last_index = cur_index;
    }
}

TEST(HistogramBucketMapperTest, IndexForValueReturnsCorrectlyInBucketMode) {
    HistogramBucketMapper bucket_mapper(/*direct_mapping=*/false);
    ASSERT_EQ(bucket_mapper.IndexForValue(std::numeric_limits<uint64_t>::max()),
              bucket_mapper.BucketCount() - 1);
    // A meaningful mapping should at least guarantee the numbers below are mapped to a list
    // of increasing indexes.
    int n = 8;  // the max number is 10^(n-1)
    std::vector<uint64_t> vec(n);
    // generated a list of numbers with enough gaps among them
    uint64_t value = 1;
    for (int i = 0; i < n; ++i) {
        vec[i] = value;
        value *= 10;
    }
    size_t last_index = bucket_mapper.IndexForValue(vec.front());
    for (int i = 1; i < n; ++i) {
        size_t cur_index = bucket_mapper.IndexForValue(vec[i]);
        ASSERT_LT(last_index, cur_index);
        last_index = cur_index;
    }
}

// This test will likely fail if we add new values into
// HistogramBucketMapper::directValues_ or HistogramBucketMapper::bucketValues_
// but forgot to update the limits of HistogramBucketMapper::bucket_.
TEST(HistogramBucketMapperTest, LargestIndexIsBoundByMagicNumber) {
    HistogramBucketMapper direct_mapper(/*direct_mapping=*/true);
    // 132 is the magic number defining the upper bound of the bucket count in direct mode.
    // Unfortunately we are testing the internal implementation details here.
    ASSERT_LT(direct_mapper.IndexForValue(std::numeric_limits<uint64_t>::max()), 132);
    HistogramBucketMapper bucket_mapper(/*direct_mapping=*/false);
    // 138 is the magic number defining the upper bound of the bucket count in bucket mode.
    ASSERT_LT(direct_mapper.IndexForValue(std::numeric_limits<uint64_t>::max()), 138);
}

}  // namespace
}  // namespace byte
