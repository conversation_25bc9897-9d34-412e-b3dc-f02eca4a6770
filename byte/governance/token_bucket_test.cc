// Copyright (c) 2020, ByteDance Inc. All rights reserved.

#include <unistd.h>
#include <stdio.h>
#include <sys/time.h>

#include <thread>  // NOLINT
#include <atomic>
#include <vector>

#include "gtest/gtest.h"
#include "byte/governance/token_bucket.h"


namespace byte {

TEST(TokenBucketTest, TestBasic) {
    TokenBucket bucket1(200, 1000);
    EXPECT_TRUE(bucket1.Get(1000));
    usleep(5 * 1000 * 1000);
    bucket1.Generate();
    EXPECT_TRUE(bucket1.Get(1000));
    EXPECT_FALSE(bucket1.Get(1000));
    bucket1.Modify(10, 100);
    EXPECT_TRUE(bucket1.Get(100));
    EXPECT_FALSE(bucket1.Get(100));
}

TEST(TokenBucketTest, TestLimit) {
    TokenBucket bucket(3000, 8000);
    std::atomic<uint64_t> count{0};
    std::vector<std::thread> threads;
    struct timeval tv_begin;
    gettimeofday(&tv_begin, nullptr);
    for (size_t i = 0; i < 6; ++i) {
            threads.emplace_back([&]() {
                for (uint64_t i = 0; i < 1000000U; ++i) {
                    bucket.Generate();
                    if (bucket.Get()) {
                        ++count;
                    }
                }
            });
    }
    for (auto& t : threads) {
        t.join();
    }
    struct timeval tv_end;
    gettimeofday(&tv_end, nullptr);
    int64_t diff = tv_end.tv_sec * 1000L + tv_end.tv_usec / 1000 -
        tv_begin.tv_sec * 1000L - tv_begin.tv_usec / 1000;
    fprintf(stderr, "tokens %.3f per second\n", static_cast<double>(count) * 1000 / diff);
}

}  // namespace byte
