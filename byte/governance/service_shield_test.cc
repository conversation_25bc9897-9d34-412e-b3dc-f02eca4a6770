// Copyright (c) 2020, ByteDance Inc. All rights reserved.

#include "gtest/gtest.h"

#include "byte/governance/service_shield.h"


namespace byte {

TEST(ServiceShieldTest, TestBasic) {
    ServiceShieldOptions options;
    ServiceShield shield(options);
    shield.Start();
    EXPECT_FALSE(shield.ResourceOverloaded());
    EXPECT_TRUE(shield.CurrentLimited());
    shield.Stop();
}

}  // namespace byte
