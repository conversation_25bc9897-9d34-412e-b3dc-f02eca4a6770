// Copyright (c) 2020, ByteDance Inc. All rights reserved.

#include "byte/governance/token_bucket.h"

#include <sys/time.h>


namespace byte {

namespace {

class Locker {
public:
    Locker(absl::Mutex *mutex, bool enable)
        : enable_(enable),
          mutex_(mutex) {
        if (enable_) {
            mutex_->Lock();
        }
    }
    ~Locker() {
        if (enable_) {
            mutex_->Unlock();
        }
    }
private:
    bool enable_;
    absl::Mutex *mutex_;
};

}  // namespace

TokenBucket::TokenBucket(uint64_t tokens_per_second)
    : TokenBucket(tokens_per_second, tokens_per_second / 5) {
}

TokenBucket::TokenBucket(uint64_t tokens_per_second, uint64_t bucket_size)
    : TokenBucket(tokens_per_second, bucket_size, bucket_size, nullptr, true) {
}

TokenBucket::TokenBucket(uint64_t tokens_per_second,
                         uint64_t bucket_size,
                         uint64_t init_tokens,
                         const struct timeval* tv_now,
                         bool enable_lock)
    : tokens_per_second_(tokens_per_second),
      bucket_size_(bucket_size),
      token_count_(init_tokens),
      enable_lock_(enable_lock) {
    struct timeval tv;
    if (!tv_now) {
        gettimeofday(&tv, nullptr);
        tv_now = &tv;
    }
    last_gen_time_ = tv_now->tv_sec * 1000000 + tv_now->tv_usec;
    last_delta_ = 0;
}

bool TokenBucket::Get(uint64_t need_tokens) {
    int64_t token_count = token_count_.load(std::memory_order_relaxed);
    if (token_count < static_cast<int64_t>(need_tokens)) {
        return false;
    }
    if (enable_lock_) {
        int64_t prev_count = token_count_.fetch_sub(need_tokens);
        // overdraft at most ?
        if (prev_count <= 0) {
            token_count_.fetch_add(need_tokens);
            return false;
        }
    } else {
        token_count_.store(token_count - need_tokens, std::memory_order_relaxed);
    }
    return true;
}

void TokenBucket::Generate(const struct timeval* tv_now) {
    Locker locker(&mutex_, enable_lock_);
    struct timeval tv;
    if (!tv_now) {
        gettimeofday(&tv, nullptr);
        tv_now = &tv;
    }
    uint64_t us_now = tv_now->tv_sec * 1000000 + tv_now->tv_usec;
    if (us_now < last_gen_time_) {
        last_gen_time_ = us_now;
        return;
    }

    uint64_t us_past = us_now - last_gen_time_;
    uint64_t counts = tokens_per_second_ * us_past + last_delta_;
    uint64_t new_tokens = counts / 1000000;
    uint64_t delta = counts % 1000000;

    last_gen_time_ = us_now;
    last_delta_ = delta;

    int64_t cur_token_count = token_count_.load(std::memory_order_relaxed);
    int64_t new_token_count = cur_token_count + new_tokens;
    if (new_token_count > static_cast<int64_t>(bucket_size_)) {
        new_token_count = bucket_size_;
    }

    if (enable_lock_) {
        token_count_.fetch_add(new_token_count - cur_token_count, std::memory_order_relaxed);
    } else {
        token_count_.store(new_token_count, std::memory_order_relaxed);
    }
}

void TokenBucket::Modify(uint64_t tokens_per_second, uint64_t bucket_size) {
    Locker locker(&mutex_, enable_lock_);
    tokens_per_second_ = tokens_per_second;
    bucket_size_ = bucket_size;
    token_count_.store(bucket_size_, std::memory_order_relaxed);
}

void TokenBucket::Modify(uint64_t tokens_per_second, uint64_t bucket_size, uint64_t init_tokens) {
    Locker locker(&mutex_, enable_lock_);
    tokens_per_second_ = tokens_per_second;
    bucket_size_ = bucket_size;
    token_count_.store(init_tokens, std::memory_order_relaxed);
}

}  // namespace byte
