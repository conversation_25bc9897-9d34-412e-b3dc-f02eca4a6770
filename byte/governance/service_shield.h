// Copyright (c) 2020, ByteDance Inc. All rights reserved.

#pragma once

#include <stdint.h>

#include <memory>
#include <vector>
#include <atomic>
#include <string>
#include <unordered_map>


namespace byte {

class TokenBucket;
class TimerManager;
class BaseThreadPool;
class ResourceTrigger;

struct ServiceShieldOptions {
    bool enable_resource_trigger = true;
    bool enable_token_bucket = true;
    uint64_t tokens_per_second = 0;
    uint64_t bucket_size = 0;
    uint64_t init_tokens = 0;
    size_t shield_thread_num = 2;
    bool use_monotonic_clock = false;
};

struct TokenBucketOptions {
    uint64_t tokens_per_second = 0;
    uint64_t bucket_size = 0;
    uint64_t init_tokens = 0;
};

class ServiceShield {
public:
    explicit ServiceShield(const ServiceShieldOptions& options);
    ~ServiceShield();
    ServiceShield(const ServiceShield&) = delete;
    ServiceShield(ServiceShield&&) = delete;
    void operator = (const ServiceShield&) = delete;
    void operator = (ServiceShield&&) = delete;

    void Start();
    void Stop();

    byte::TimerManager* GetTimerManager() {
        return timer_mgr_.get();
    }

    byte::BaseThreadPool* GetThreadPool() {
        return thread_pool_.get();
    }

    virtual bool ResourceOverloaded();
    virtual bool ResourceOverloaded(const std::string& trigger_name);
    virtual bool CurrentLimited(uint64_t need_tokens = 1);
    virtual bool CurrentLimited(const std::string& bucket_name, uint64_t need_tokens = 1);

    void RegisterTrigger(std::unique_ptr<ResourceTrigger> trigger, bool shared = true);
    void AddTokenBucket(const std::string& bucket_name, const TokenBucketOptions& options);

private:
    ServiceShieldOptions options_;
    std::atomic<bool> running_;
    std::unique_ptr<TimerManager> timer_mgr_;
    std::unique_ptr<BaseThreadPool> thread_pool_;
    std::vector<std::unique_ptr<ResourceTrigger>> shared_trigger_list_;
    std::unordered_map<std::string, std::unique_ptr<ResourceTrigger>> excluded_trigger_map_;
    std::unique_ptr<TokenBucket> token_bucket_;
    std::unordered_map<std::string, std::unique_ptr<TokenBucket>> token_bucket_map_;
};

}  // namespace byte
