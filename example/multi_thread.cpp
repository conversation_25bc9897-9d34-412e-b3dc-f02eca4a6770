#include "baidu/inf/aries-api/aries.h"
#include <bthread.h>
#include <unistd.h>
#include <sys/time.h>
#include <sys/syscall.h>
#include <string.h>
#include <iostream>
#include <atomic>
#include <memory>
#include <vector>

std::atomic_long g_log_id(0);

DEFINE_string(token, "default_token", "token");
DEFINE_string(server, "bns://aries-test-dataagent.yun-object-qa.qd01", "server addr");
DEFINE_bool(use_lib, false, "default not use dataagent lib");
void *test_blob_worker(void *param) {
    aries::AriesClient *client = (aries::AriesClient *) param;
    std::string space_name = "test-table1";
    int64_t offset = 0;

    uint64_t log_id = 0;
    int tid = syscall(SYS_gettid);
    char key[1024];
    char message[1024];

    uint128_t bid = 0;
    int state = 0;
    int ret = 0;
    int count = 0;
    while (count < 10) {
        switch (state) {
        case 0: {
            log_id = g_log_id++;
            time_t t;
            struct tm now;
            time(&t);
            localtime_r(&t, &now);
            int n = strftime(message, sizeof(message), "%Y-%m-%d %H:%M:%S", &now);
            n += snprintf(message + n, sizeof(message) - n, " tid:%d offset:%ld", tid, offset);
            memset(message + n, '*', sizeof(message) - n);
            message[sizeof(message) - 1] = 0;

            snprintf(key, sizeof(key), "tid:%d;offset:%ld", tid, offset);
            offset += 1024;
            ++state;
        }
        case 1: {
            ret = client->put(log_id, space_name, key, "user custom", message, &bid);
            if (ret != aries::AE_OK) {
                std::cerr << "put blob failed" << std::endl;
                continue;
            }
            std::cout << "put blob succ, bid returned:" << bid << std::endl;
            ++state;
        }
        case 2: {
            std::string data;
            ret = client->get(log_id, bid, &data);
            if (ret != aries::AE_OK) {
                std::cerr << "get blob data failed" << std::endl;
                continue;
            }
            aries::BlobMeta meta;
            ret = client->get_meta(log_id, bid, &meta);
            if (ret != aries::AE_OK) {
                std::cerr << "get blob key&meta failed" << std::endl;
                continue;
            }
            std::cout << "get blob meta succ:" << meta.key << std::endl;
            std::cout << "get blob data succ:" << data << std::endl;
            ++state;
        }
        case 3: {
            ret = client->remove(log_id, bid);
            if (ret != aries::AE_OK) {
                std::cerr << "remove blob failed" << std::endl;
                continue;
            }
            std::cout << "remove blob succ" << std::endl;
            state = 0;

            ++count;
            sleep(1);
        }
        }
        usleep(100000);
    }
    return 0;
}

int main() {
    aries::ConfInfo conf_info;
    conf_info.token = FLAGS_token;
    conf_info.server = FLAGS_server;
    conf_info.use_proxy_lib = FLAGS_use_lib;
    conf_info.load_balancer = "la";
    conf_info.timeout_ms = 3000;
    conf_info.max_retry = 3;

    std::shared_ptr<aries::AriesClient> client(aries::AriesClient::new_aries_client(conf_info));
    if (client == nullptr) {
        std::cerr << "init failed, see log for details" << std::endl;
        return -1;
    }
    std::vector<bthread_t> tid(10);
    for (size_t i = 0; i < tid.size(); ++i) {
        bthread_start_background(&tid[i], NULL, test_blob_worker, client.get());
    }
    for (size_t i = 0; i < tid.size(); ++i) {
        bthread_join(tid[i], NULL);
    }
    return 0;
}
