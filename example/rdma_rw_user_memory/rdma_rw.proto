syntax="proto2";

package example;

option cc_generic_services = true;

message ReadRequest {
    optional uint64 block_num = 1;
    optional uint64 block_size = 2;
}

message ReadResponse {
    optional bool success = 1;
};

message AllocateRequest {
    optional uint64 block_num = 1;
    optional uint64 block_size = 2;
}

message AllocateResponse {
    optional bool success = 1;
};

message WriteRequest {
}

message WriteResponse {
    optional bool success = 1;
};

service RdmaService {
    rpc Read(ReadRequest) returns (ReadResponse);
    rpc Allocate(AllocateRequest) returns (AllocateResponse);
    rpc Write(WriteRequest) returns (WriteResponse);
}