add_custom_command(
    OUTPUT ${CMAKE_CURRENT_BINARY_DIR}/echo.pb.cc
    DEPENDS protoc ${CMAKE_CURRENT_SOURCE_DIR}/echo.proto
    COMMAND ${PROTOBUF_PROTOC_EXECUTABLE} ${CMAKE_CURRENT_SOURCE_DIR}/echo.proto
    --proto_path=${CMAKE_CURRENT_SOURCE_DIR}
    --cpp_out=${CMAKE_CURRENT_BINARY_DIR}
    )

set(PROTO_SRCS ${CMAKE_CURRENT_BINARY_DIR}/echo.pb.cc)

add_executable(async_service_echo_server async_service_echo_server.cpp ${PROTO_SRCS})
target_link_libraries(async_service_echo_server byterpc tcmalloc_fix-static)
target_include_directories(async_service_echo_server PUBLIC ${CMAKE_CURRENT_BINARY_DIR})
set_target_properties(async_service_echo_server PROPERTIES CXX_STANDARD 14)

add_executable(echo_client echo_client.cpp ${PROTO_SRCS})
target_link_libraries(echo_client byterpc tcmalloc_fix-static)
target_include_directories(echo_client PUBLIC ${CMAKE_CURRENT_BINARY_DIR})
set_target_properties(echo_client PROPERTIES CXX_STANDARD 14)