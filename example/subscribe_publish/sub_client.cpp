#include <gflags/gflags.h>

#include <cstddef>
#include <thread>

#include "byterpc/builder.h"
#include "byterpc/callback.h"
#include "byterpc/rpc.h"
#include "byterpc/server.h"
#include "byterpc/thread/priority_task.h"
#include "byterpc/util/closure_guard.h"
#include "byterpc/util/logging.h"
#include "hub.pb.h"

DEFINE_string(remote_addr,
              "127.0.0.1:18888",
              "Address of remote server who publishes status change");
DEFINE_string(observe_addr, "0.0.0.0:17777", "Client local server which listens to remote server");
DEFINE_int32(feed_id, 17, "Feed id the client subscribes to");

namespace byterpc {
struct SubsriberRunningContext {
    ThreadContext* thread_context;
    std::atomic<bool> ready_to_invoke;
    std::atomic<bool> sub_finish;
    std::atomic<bool> unsub_finish;
};

SubsriberRunningContext g_sub_ctx;

// RPC call sent from Subscribe/Client to Hub
class SubRpcCall {
public:
    SubRpcCall()
        : _builder(),
          _channel(),
          _controller(),
          _req(new example::SubRequest),
          _resp(new example::SubResponse) {
        _req->set_endpoint(FLAGS_observe_addr);
        _req->set_feed(FLAGS_feed_id);
    }

    ~SubRpcCall() {}

    void Init() {
        _channel = _builder.BuildChannel(FLAGS_remote_addr);
        BYTERPC_CHECK(_channel);
        _stub = std::make_unique<example::HubService_Stub>(_channel.get());
    }

    void Run() {
        _controller = _builder.CreateSessionController(PROTOCOL_BYTE_STD);
        BYTERPC_CHECK(_controller);
        google::protobuf::Closure* done =
            ::byterpc::NewCallback<SubRpcCall, SubRpcCall*>(this, &SubRpcCall::Reset);
        _stub->Subscribe(_controller, _req.get(), _resp.get(), done);
    }

    void Reset() {
        g_sub_ctx.sub_finish = true;
        std::string status = _controller->Failed() ? "Subscribe failed" : "Subscribe success";
        BYTERPC_LOG(INFO) << status << ": client addr=" << FLAGS_observe_addr
                          << ", server addr=" << FLAGS_remote_addr << ", feed=" << _req->feed();
    }

private:
    Builder _builder;
    std::shared_ptr<Builder::Channel> _channel;
    Controller* _controller;
    std::unique_ptr<example::HubService_Stub> _stub;
    std::unique_ptr<example::SubRequest> _req;
    std::unique_ptr<example::SubResponse> _resp;
};

// RPC call sent from Subscriber/Client to Hub
class UnSubRpcCall {
public:
    UnSubRpcCall()
        : _builder(),
          _channel(),
          _req(new example::UnSubRequest),
          _resp(new example::UnSubResponse) {
        _req->set_endpoint(FLAGS_observe_addr);
        _req->set_feed(FLAGS_feed_id);
    }

    ~UnSubRpcCall() {}

    void Init() {
        _channel = _builder.BuildChannel(FLAGS_remote_addr);
        BYTERPC_CHECK(_channel);
        _stub = std::make_unique<example::HubService_Stub>(_channel.get());
    }

    void Run() {
        _controller = _builder.CreateSessionController(PROTOCOL_BYTE_STD);
        BYTERPC_CHECK(_controller);
        google::protobuf::Closure* done =
            ::byterpc::NewCallback<UnSubRpcCall, UnSubRpcCall*>(this, &UnSubRpcCall::Reset);
        _stub->UnSubscribe(_controller, _req.get(), _resp.get(), done);
    }

    void Reset() {
        g_sub_ctx.unsub_finish = true;
        std::string status = _controller->Failed() ? "UnSubscribe failed" : "UnSubscribe success";
        BYTERPC_LOG(INFO) << status << ": client addr=" << FLAGS_observe_addr
                          << ", server addr=" << FLAGS_remote_addr << ", feed=" << _req->feed();
    }

private:
    Builder _builder;
    std::shared_ptr<Builder::Channel> _channel;
    Controller* _controller;
    std::unique_ptr<example::HubService_Stub> _stub;
    std::unique_ptr<example::UnSubRequest> _req;
    std::unique_ptr<example::UnSubResponse> _resp;
};

class ObserveServiceImpl : public example::ObserveService {
public:
    void Observe(::google::protobuf::RpcController* controller,
                 const example::FeedRequest* request,
                 example::FeedResponse* response,
                 ::google::protobuf::Closure* done) override {
        util::ClosureGuard done_guard(done);
        BYTERPC_LOG(INFO) << "server=" << request->source() << ", feed=" << request->feed()
                          << ", payload=" << request->payload();
    }
};

void* StartClient() {
    ExecCtx ctx(LOOP_UNTIL_QUIT);
    Server server;
    server.RegisterService(new ObserveServiceImpl);
    ServerOptions options(true);
    if (server.Start(FLAGS_observe_addr, options) < 0) {
        BYTERPC_LOG(ERROR) << "Fail to build client";
    }

    ExecCtx::GetOrNewThreadEventRegistry(byterpc::TYPE_KERNEL_TCP);
    g_sub_ctx.thread_context = ExecCtx::GetCurrentThreadContext();
    g_sub_ctx.ready_to_invoke = true;
    ExecCtx::LoopUntilQuit();
    return nullptr;
}

void Subscribe(SubRpcCall* sub_call) {
    sub_call->Init();
    sub_call->Run();
}

void UnSubscribe(UnSubRpcCall* unsub_call) {
    unsub_call->Init();
    unsub_call->Run();
}

void* SubscribeThenUnSubscribe() {
    // Do not Subscribe until client is ready to accept/Observe feed messages
    while (!g_sub_ctx.ready_to_invoke.load()) {
    };
    SubRpcCall sub_call{};
    g_sub_ctx.thread_context->Invoke(NewClosure(Subscribe, &sub_call));
    while (!g_sub_ctx.sub_finish.load()) {
    };

    // Ubsubcribe after a while (5 seconds)
    sleep(5);
    UnSubRpcCall unsub_call{};
    g_sub_ctx.thread_context->Invoke(NewClosure(UnSubscribe, &unsub_call));
    while (!g_sub_ctx.unsub_finish.load()) {
    };
    return nullptr;
}
}  // namespace byterpc

int main(int argc, char* argv[]) {
    GFLAGS_NAMESPACE::ParseCommandLineFlags(&argc, &argv, true);

    byterpc::InitOptions init_opt;
    if (byterpc::ExecCtx::Init(init_opt) < 0) {
        BYTERPC_LOG(ERROR) << "Fail to invoke ExecCtx::Init";
        return -1;
    }
    // Main thread running Observe service which listens to messages published from Hub
    std::unique_ptr<std::thread> observe_th(new std::thread(byterpc::StartClient));
    // Operator thread which inserts Subscribe and UnSubscibe RPC call to main thread
    std::unique_ptr<std::thread> operator_th(new std::thread(byterpc::SubscribeThenUnSubscribe));
    observe_th->join();
    operator_th->join();
    return 0;
}
