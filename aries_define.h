/**
 * ARIES - A Reliable and Impressive EB-level Storage.
 * Copyright (c) 2022 Baidu.com, Inc. All Rights Reserved.
 *
 */

#pragma once

#include <map>
#include <string>
#include <set>
#include <vector>
#include <memory>
#include <cmath>
#include <cstdint>
#include <optional>

#include "baidu/inf/aries-api/common/proto/common.pb.h"
#include "baidu/inf/aries-api/common/proto/enum.pb.h"
#include "baidu/inf/aries-api/common/proto/error_code.pb.h"
#include "baidu/inf/aries-api/common/rpc_call.h"

typedef unsigned __int128 uint128_t;

namespace aries {

enum Errno {
    AE_OK = 0,
    AE_FAIL = 1,
    AE_TIMEOUT = 2,
    AE_BUSY = 3,
    AE_EXIST = 4,
    AE_NOT_EXIST = 5,
    AE_INVALID_ARGUMENT = 12,
    AE_NOT_SUPPORT = 13,
    AE_PARTIAL = 14,
    AE_CHECKSUM = 21,
    AE_VOLUME_NOT_EXIST = 22,
    AE_BLOB_NOT_EXIST = 25,
    AE_NO_SPACE_LEFT = 28,
    AE_IO = 33,
    AE_AGAIN = 37,
    AE_INVALID_VOLUME_SESSION = 57,
    AE_VOLUME_IS_FULL = 58,
    // AE ONLY
    AE_BADF = 1000,
    AE_EOF = 1001,
    AE_INVALID_STREAM_VERSION = 1002
};

enum ApiConstant {
    MAX_BLOB_SIZE = 64 * 1024 * 1024,
    MAX_KEY_META_SIZE = 2 * 1024
};

enum CompressType {
    COMPRESS_TYPE_NONE = 0,
    COMPRESS_TYPE_LZ4 = 1,
    COMPRESS_TYPE_ZSTD = 2,
    // JPEG ONLY
    COMPRESS_TYPE_BRUNSLI = 3,
    // AUTO = BRUNSLI/ZSTD
    COMPRESS_TYPE_AUTO_OFFLINE = 4,
    COMPRESS_TYPE_SNAPPY = 5,
    COMPRESS_TYPE_BROTLI = 6,
    COMPRESS_TYPE_ISAL = 8
};

struct RpcControl {
    RpcControl() {
        log_id = 0;
        channel = nullptr;
    }

    uint64_t log_id;
    std::shared_ptr<baidu::rpc::Channel> channel;
};

struct AllocatorInfo {
    AllocatorInfo() : addr(0) {}
    AllocatorInfo(uint64_t address) : addr(address) {}

    uint64_t addr;
    bool is_alive = true;
    std::map<std::string, uint64_t> space_free_bytes;
};
typedef std::shared_ptr<AllocatorInfo> AllocatorInfoPtr;

typedef std::shared_ptr<aries::pb::SpaceInfo> SpaceInfoPtr;

struct CompressOption {
    virtual ~CompressOption() {}
    // base info
    uint64_t log_id = 0;
    uint32_t compress_type = 0;
    uint32_t compress_level = 0;
    float min_compress_ratio = 1.001; // only for compress
    uint32_t origin_data_len = 0; // only for decompress
    uint32_t thread_num = 4; // only for brunsli
};

struct RpcCompressOption : public CompressOption {
    int timeout_ms = 5000;
    uint32_t priority = 0;
    const char* token = "";
    baidu::rpc::Channel* compress_channel = nullptr;
    uint32_t compress_max_retry = 10;
};

struct RequestOption {
    uint64_t log_id = 0;
    int timeout_ms = -1;
    int retry_timeout_ms = -1;
    CompressType compress_type = COMPRESS_TYPE_NONE;
    uint32_t compress_level = 0;
    float min_compress_ratio = 1.001;
    ::aries::Qos priority = ::aries::HIGHEST;
    ::aries::NetworkQos network_qos = ::aries::NORAML;
    //put small_blob into small_blob_space
    std::string small_blob_space_name;
    uint32_t small_blob_extreme_length = 0; //less than or equal to MAX_BLOB_SIZE
    // fase range get without crc check, default false
    bool fast_range_get = false;

    // time to keep an item on a cache when put
    // <= 0 : don't admit item to cache when put, it is default
    // > 0 : admit item to cache when put, and item will not be evicted from cache
    //       during **cache_pin_ttl_secs** seconds
    int cache_pin_ttl_secs = 0;

    // 幂等性token，0表示无幂等性检查
    uint64_t rpc_token = 0;
    // sdk地址
    base::EndPoint term_addr = base::EndPoint{};
};

struct BlobMeta {
    std::string key;
    std::string meta;
    uint64_t create_time;
    uint32_t blob_len;
    uint32_t blob_crc;
    CompressType compress_type; // 0 means not compressed
    uint32_t compress_level;
    float compress_ratio;
};

struct BlobRange {
    uint32_t offset;    // offset of blob data to get
    uint32_t len;       // len of blob data to get
    uint32_t blob_len;  // blob size, will give a hint to server's strategy
};

struct ProxyConf {
    ProxyConf() : compressor_server("bns://compressor_server"),
        vs_load_balancer("rr"), cp_load_balancer("la"),
        bvar_service_port(8710), use_global_bvar_service(false), use_stream_mode(false),
        use_archive_volume(false), query_meta_timeout_ms(50),
        query_meta_max_retry(3), bvar_history_num(720),
        bvar_monitor_include("dataproxy*"),
        bvar_monitor_exclude("*80*;*9999*;*cdf*;*percentiles*"),
        meta_cache_size(200 * 1024 * 1024),
        max_blob_size_mb(1024),
        connect_timeout_ms(3000), shard_call_timeout_ms(15000),
        allocate_bid_max_retry(3), allocate_prior_threshold(1024UL * 1024 * 1024 * 1024),
        allocate_min_free_size_threshold(1024UL * 1024 * 500),
        allocator_call_timeout_ms(1000), allocator_refresh_ms(60000),
        remove_max_retry(2), put_blob_max_retry(2),
        put_blob_retry_interval_ms(100), get_shard_min_timeout_ms(100),
        get_shard_timeout_quantile(80), heartbeat_interval_second(30),
        refresh_master_interval_second(30), check_compress_type_info(true),
        key_meta_max_size(2048), recheck_compress(false),
        recheck_ec(false),
        recheck_ec_async(false),
        enable_prealloc_bid(false),
        async_alloc_bid_timeout_ms(1200),
        async_alloc_bid_expire_s(300),
        max_size_async_alloc_bid(12),
        gap_size_for_max_blob((1024 + 1024) * 20), // (align_1kb + meta_len) * k
        bvar_monitor_interval_second(60),
        bvar_max_monitor_num(100),
        get_bvar_max_num(60),
        compress_max_retry(10),
        default_zstd_compress_level(10),
        cache_timeout_s(60 * 30),
        max_async_request_num(1000),
        channel_max_cache_num(10 * 1024),
        channel_cache_timeout_ms(10 * 60 * 1000) {}

    ~ProxyConf() = default;

    ProxyConf& operator=(const ProxyConf& conf) {
        proxy_name = conf.proxy_name;
        proxy_version = conf.proxy_version;
        token = conf.token;
        ms_server = conf.ms_server;
        vs_server = conf.vs_server;
        tc_server = conf.tc_server;
        allocator_server = conf.allocator_server;
        compressor_server = conf.compressor_server;
        vs_load_balancer = conf.vs_load_balancer;
        cp_load_balancer = conf.cp_load_balancer;
        query_meta_timeout_ms = conf.query_meta_timeout_ms;
        query_meta_max_retry = conf.query_meta_max_retry;
        bvar_history_num = conf.bvar_history_num; 
        bvar_service_port = conf.bvar_service_port;
        cache_space_name = conf.cache_space_name;
        meta_cache_size = conf.meta_cache_size;
        max_blob_size_mb = conf.max_blob_size_mb;
        channel_max_cache_num.store(conf.channel_max_cache_num.load());
        use_global_bvar_service = conf.use_global_bvar_service;
        use_archive_volume = conf.use_archive_volume;
        use_stream_mode = conf.use_stream_mode;
        stateservice_server = conf.stateservice_server;
        stateservice_raft_group_count = conf.stateservice_raft_group_count;
        streamservice_server = conf.streamservice_server;
        reload(conf);
        return *this;
    }

    void reload(const ProxyConf& conf) {
        connect_timeout_ms.store(conf.connect_timeout_ms.load());
        shard_call_timeout_ms.store(conf.shard_call_timeout_ms.load());
        allocate_bid_max_retry.store(conf.allocate_bid_max_retry.load());
        allocate_prior_threshold.store(conf.allocate_prior_threshold.load());
        allocate_min_free_size_threshold.store(conf.allocate_min_free_size_threshold.load());
        allocator_call_timeout_ms.store(conf.allocator_call_timeout_ms.load());
        allocator_refresh_ms.store(conf.allocator_refresh_ms.load());
        remove_max_retry.store(conf.remove_max_retry.load());
        put_blob_max_retry.store(conf.put_blob_max_retry.load());
        put_blob_retry_interval_ms.store(conf.put_blob_retry_interval_ms.load());
        get_shard_min_timeout_ms.store(conf.get_shard_min_timeout_ms.load());
        get_shard_timeout_quantile.store(conf.get_shard_timeout_quantile.load());
        heartbeat_interval_second.store(conf.heartbeat_interval_second.load());
        refresh_master_interval_second.store(conf.refresh_master_interval_second.load());
        check_compress_type_info.store(conf.check_compress_type_info.load());
        key_meta_max_size.store(conf.key_meta_max_size.load());
        recheck_compress.store(conf.recheck_compress.load());
        recheck_ec.store(conf.recheck_ec.load());
        recheck_ec_async.store(conf.recheck_ec_async.load());
        enable_prealloc_bid.store(conf.enable_prealloc_bid.load());
        async_alloc_bid_timeout_ms.store(conf.async_alloc_bid_timeout_ms.load());
        async_alloc_bid_expire_s.store(conf.async_alloc_bid_expire_s.load());
        max_size_async_alloc_bid.store(conf.max_size_async_alloc_bid.load());
        gap_size_for_max_blob.store(conf.gap_size_for_max_blob.load());
        bvar_monitor_interval_second.store(conf.bvar_monitor_interval_second.load());
        bvar_max_monitor_num.store(conf.bvar_max_monitor_num.load());
        get_bvar_max_num.store(conf.get_bvar_max_num.load());
        if (conf.bvar_monitor_include.empty()) {
            bvar_monitor_include = proxy_name + "*";
        } else {
            bvar_monitor_include = conf.bvar_monitor_include; 
        }
        if (conf.bvar_monitor_exclude.empty()) {
            bvar_monitor_exclude = "*80*;*9999*;*cdf*;*percentiles*"; 
        } else {
            bvar_monitor_exclude = conf.bvar_monitor_exclude;
        }
        compress_max_retry.store(conf.compress_max_retry.load());
        default_zstd_compress_level.store(conf.default_zstd_compress_level.load());
        cache_timeout_s.store(conf.cache_timeout_s);
        max_async_request_num.store(conf.max_async_request_num);
        channel_max_cache_num.store(conf.channel_max_cache_num);
        channel_cache_timeout_ms.store(conf.channel_cache_timeout_ms);
    }

    // need to init
    std::string proxy_name;
    std::string proxy_version;
    std::string token;
    std::string ms_server;  // master
    std::string vs_server;  // volumeservice
    std::string tc_server;  // tapecenter
    std::string allocator_server; // allocator
    std::string compressor_server; // brunsli compress
    std::string vs_load_balancer;
    std::string cp_load_balancer;
    int32_t bvar_service_port;
    bool use_global_bvar_service;
    bool use_stream_mode;
    bool use_archive_volume;
    std::string stateservice_server;
    uint64_t stateservice_raft_group_count;
    std::string streamservice_server;
    // below has default value
    int32_t query_meta_timeout_ms;
    int32_t query_meta_max_retry;
    int32_t bvar_history_num;
    std::string bvar_monitor_include;
    std::string bvar_monitor_exclude;
    std::string cache_space_name;
    uint64_t meta_cache_size;
    uint64_t max_blob_size_mb;
    // below support reload
    std::atomic<int32_t> connect_timeout_ms;
    std::atomic<int32_t> shard_call_timeout_ms;
    std::atomic<int32_t> allocate_bid_max_retry;
    std::atomic<uint64_t> allocate_prior_threshold;
    std::atomic<uint64_t> allocate_min_free_size_threshold;
    std::atomic<int32_t> allocator_call_timeout_ms;
    std::atomic<int32_t> allocator_refresh_ms;
    std::atomic<int32_t> remove_max_retry;
    std::atomic<int32_t> put_blob_max_retry;
    std::atomic<int32_t> put_blob_retry_interval_ms;
    std::atomic<int32_t> get_shard_min_timeout_ms;
    std::atomic<int32_t> get_shard_timeout_quantile;
    std::atomic<int32_t> heartbeat_interval_second;
    std::atomic<int32_t> refresh_master_interval_second;
    std::atomic<bool> check_compress_type_info;
    std::atomic<int32_t> key_meta_max_size;
    std::atomic<bool> recheck_compress;
    std::atomic<bool> recheck_ec;
    std::atomic<bool> recheck_ec_async;
    std::atomic<bool> enable_prealloc_bid;
    std::atomic<int32_t> async_alloc_bid_timeout_ms;
    std::atomic<int32_t> async_alloc_bid_expire_s;
    std::atomic<int32_t> max_size_async_alloc_bid;
    std::atomic<int32_t> gap_size_for_max_blob;
    std::atomic<int32_t> bvar_monitor_interval_second;
    std::atomic<int32_t> bvar_max_monitor_num;
    std::atomic<int32_t> get_bvar_max_num;
    std::atomic<int32_t> compress_max_retry;
    std::atomic<int32_t> default_zstd_compress_level;
    std::atomic<uint32_t> cache_timeout_s;
    std::atomic<uint32_t> max_async_request_num;
    std::atomic<uint32_t> channel_max_cache_num;
    std::atomic<uint32_t> channel_cache_timeout_ms;
};

struct ConfInfo {
    ConfInfo() : load_balancer("la"), timeout_ms(10000), max_retry(3), retry_timeout_ms(0),
        local_port(-1), use_proxy_lib(false), use_shared_proxy(false), is_aries_internal_user(false) {}
    std::string token;//token
    std::string server;//data agent BNS addr
    std::string load_balancer;//
    int32_t timeout_ms;
    int32_t max_retry;//not include first time

    // timeout for each retry; default is 0 and will use same value as timeout_ms
    int32_t retry_timeout_ms;
    // prior to access localhost port if turned on, set -1 to disable
    int local_port;
    bool use_proxy_lib;
    // allow multiple client share same proxy
    bool use_shared_proxy;
    ProxyConf proxy_conf;

    bool is_aries_internal_user;
};

struct BlobGroup {
    std::string tag;    
    std::vector<uint128_t> blob_ids;
};
 
struct RetrieveOption {
    std::string task_id;    
    uint64_t task_deadline; 
    std::vector<BlobGroup> blob_groups; 
};

struct RetrieveTaskStatus {
    RetrieveTaskState task_state;
    uint32_t progress;
    uint64_t remain_time;
};

constexpr uint64_t TRUNCATE_QUORUM_SIZE = (uint64_t)-1;
constexpr uint64_t TRUNCATE_SYNC_SIZE = (uint64_t)-2;

struct StreamStatus {
    pb::StreamMeta stream_meta;
    uint32_t reader_count;
    uint32_t writer_count;
    pb::StreamSessionLeaseInfo writer_session;
    StreamStatus() = default;
};

struct CreateStreamOptions {
    uint64_t log_id = 0;
    // 总超时时间
    uint32_t timeout_ms = 10000;
    // 流所属的space
    std::string space_name;
    // 只允许指定特定size
    BlockSizeType block_size_type = BLOCK_SIZE_TYPE_1GB;
    // 幂等性token，0表示无幂等性检查
    uint64_t rpc_token = 0;
    // sdk地址
    base::EndPoint term_addr = base::EndPoint{};

    CreateStreamOptions() {
        log_id = base::fast_rand();
    }
};

struct StreamWriterOptions {
    // 流ID
    uint128_t stream_id = 0;
    // Writer内部的缓存大小，设置为0代表无缓存，同步模式
    // buffer_size如果不为0但小于最大blob大小(最大16MB)，视为无缓存模式
    uint32_t buffer_size = 0;
    // 这个选项指示在经过多少次不成功的put之后seal掉当前的block
    uint32_t retry_times_before_seal_block = 3;
    // 单个rpc的超时时间
    uint32_t timeout_ms = 3000;
    // 向stateservice更新size之后回调，返回sync的size，客户端可能多次调用该函数，默认为空
    std::function<void(uint64_t)> sync_offset_callback;
    // 打开Writer时的log_id
    uint64_t log_id = 0;
    // 建议压缩类型，默认不压缩
    CompressType compress_type = COMPRESS_TYPE_NONE;
    // 压缩级别
    uint32_t compress_level = 0;
    // 最低压缩比
    float min_compress_ratio = 1.001;
    // 可选，session标记
    std::optional<uint64_t> session_client_tag = std::nullopt;
    // force_open时可选，流version
    std::optional<uint64_t> stream_version = std::nullopt;
    // force_open时可选，重试时的tail offset
    std::optional<uint64_t> truncate_tail_offset = std::nullopt;
    // sdk地址
    base::EndPoint term_addr = base::EndPoint{};

    StreamWriterOptions() {
        log_id = base::fast_rand();
    }
};

struct StreamReaderOptions {
    // 流ID
    uint128_t stream_id = 0;
    // Reader内部的缓存大小，设置为0代表无缓存，同步模式
    // buffer_size如果不为0，则不能小于最大blob大小
    uint32_t buffer_size = 0;
    // 单个rpc的超时时间
    uint32_t timeout_ms = 3000;
    // 单个读请求的超时时间
    uint32_t read_timeout_ms = 30000;
    // 这个选项指示在读到流末尾时Reader是否向StateService重复重新获取size
    bool tailing = false;
    // 打开Reader时的log_id
    uint64_t log_id = 0;
    // sdk地址
    base::EndPoint term_addr = base::EndPoint{};

    StreamReaderOptions() {
        log_id = base::fast_rand();
    }
};

struct StreamCacheOptions {
    uint64_t log_id = 0;
    // 总超时时间
    uint32_t timeout_ms = 10000;
    // 流ID
    uint128_t stream_id = 0;
    // 起始位置
    uint64_t offset = 0;
    // 长度
    uint64_t length = 0;
    // 缓存时间
    uint32_t cache_pin_ttl_secs = 0;
    // sdk地址
    base::EndPoint term_addr = base::EndPoint{};

    StreamCacheOptions() {
        log_id = base::fast_rand();
    }
};

}
