---
# Google coding style is used as the basic rule. Custome rules for each
# language can be set in sections below.
BasedOnStyle: Google
---
Language: Cpp
Standard: c++17
IndentWidth: 2
AccessModifierOffset: -1
IncludeBlocks: Regroup
DerivePointerAlignment: false
PointerAlignment: Left
AllowShortCaseLabelsOnASingleLine: true
AllowShortLambdasOnASingleLine: Empty
AllowShortFunctionsOnASingleLine: Empty
