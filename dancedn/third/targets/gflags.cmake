if (NOT TARGET gflags)
  find_package(gflags REQUIRED)
  if(TARGET gflags)
    message(STATUS "found target gflags")
    set(GFLAGS_LIBRARY gflags)
    set(GFLAGS_INCLUDE_PATH ${GFLAGS_INCLUDE_DIR})
  else()
    message(FATAL_ERROR "undefined library gflags")
  endif()
endif()

if (NOT TARGET gflags_nothreads)
  add_library(gflags_nothreads STATIC IMPORTED GLOBAL)
  set_target_properties(gflags_nothreads PROPERTIES
    IMPORTED_LOCATION ${CMAKE_LIBRARY_PATH}/libgflags_nothreads.a
    INTERFACE_INCLUDE_DIRECTORIES ${GFLAGS_INCLUDE_PATH})
endif()