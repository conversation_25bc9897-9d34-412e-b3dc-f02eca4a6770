set(lib_name byterpc)
ExternalProject_Add(
  ${lib_name}
  GIT_REPOSITORY ******************:storage/byterpc.git
  # commit id for branch release-v1.2.1
  GIT_TAG 8ea8b17b4738f8eb916bee342b3a5a6cc3714838
  GIT_SUBMODULES third/tarzan third/byte_express
  # GIT_SHALLOW TRUE
  PREFIX ${CMAKE_CURRENT_BINARY_DIR}/${lib_name}
  DOWNLOAD_DIR ${DOWNLOAD_DIR}
  PATCH_COMMAND
    git checkout -- . && git clean -f && patch -p1 < ${CMAKE_SOURCE_DIR}/patches/byterpc.patch
  BUILD_IN_SOURCE FALSE
  CMAKE_ARGS
    ${common_cmake_args}
    "-DCMAKE_CXX_FLAGS=${CXX_COMMON_FLAGS} -Wno-deprecated-declarations -Wno-address -Wno-class-memaccess"
    -DBYTERPC_ENABLE_BYTE_EXPRESS=ON
    -DBYTERPC_ENABLE_LAVATRANS=OFF
    -DBYTERPC_WITH_LIBUNWIND=ON
    -DBYTERPC_WITH_NUMA=OFF
    -DBYTERPC_USE_INTERNAL_BOOST_LIBRARY=OFF
    -DBYTERPC_ENABLE_LTO_OPTIMIZATION=OFF
    -DBYTERPC_BUILD_TOOLS=OFF
    -DBYTERPC_BUILD_PERF=OFF
    -DBYTERPC_ENABLE_IOBUF_MTHREADS=ON
    -DBYTERPC_ENABLE_ASAN=OFF
    -DSPDLOG_FMT_EXTERNAL=OFF
  BUILD_COMMAND make -s -j${NUM_JOBS_PER_LIB}
  INSTALL_COMMAND make -s install
  LOG_CONFIGURE TRUE
  LOG_BUILD TRUE
  LOG_INSTALL TRUE
  LOG_OUTPUT_ON_FAILURE TRUE
  )