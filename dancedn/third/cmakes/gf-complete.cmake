set(lib_name gf-complete)
ExternalProject_Add(
  ${lib_name}
  DOWNLOAD_COMMAND ${CMAKE_COMMAND} -E echo "skip download"
  SOURCE_DIR ${CMAKE_SOURCE_DIR}/build/byte-thirdparty/src/byte-thirdparty
  SOURCE_SUBDIR ${lib_name}
  PREFIX ${CMAKE_CURRENT_BINARY_DIR}/${lib_name}
  DOWNLOAD_DIR ${DOWNLOAD_DIR}
  BUILD_IN_SOURCE TRUE
  CONFIGURE_COMMAND
    ${common_configure_envs}
    ./configure
        --prefix=${CMAKE_INSTALL_PREFIX}
  BUILD_COMMAND make -s -j${NUM_JOBS_PER_LIB}
  INSTALL_COMMAND make -s install
  LOG_CONFIGURE TRUE
  LOG_BUILD TRUE
  LOG_INSTALL TRUE
)


ExternalProject_Add_Step(${lib_name} autoreconf
  DEPENDEES download
  DEPENDERS configure
  ALWAYS FALSE
  COMMAND autoreconf --force --install -I m4 .
  WORKING_DIRECTORY <SOURCE_DIR>/${lib_name}
)