set(lib_name bdthread)
ExternalProject_Add(
  ${lib_name}
  DOWNLOAD_COMMAND
    <NAME_EMAIL>:storage/bdthread.git ${CMAKE_CURRENT_BINARY_DIR}/${lib_name}/src/${lib_name}
  UPDATE_COMMAND
    git checkout 45d77d8a2a4a47e500c49085c824339b4a4182bf
  PREFIX ${CMAKE_CURRENT_BINARY_DIR}/${lib_name}
  DOWNLOAD_DIR ${DOWNLOAD_DIR}
  BUILD_IN_SOURCE FALSE
  CMAKE_ARGS
    ${common_cmake_args}
  BUILD_COMMAND make -s -j${NUM_JOBS_PER_LIB}
  INSTALL_COMMAND ${CMAKE_COMMAND}
    -DINSTALL_DIR=${CMAKE_INSTALL_PREFIX}
    -DSOURCE_DIR=${CMAKE_CURRENT_BINARY_DIR}/${lib_name}/src/${lib_name}
    -DBUILD_DIR=${CMAKE_CURRENT_BINARY_DIR}/${lib_name}/src/${lib_name}-build
    -P ${CMAKE_SOURCE_DIR}/cmakes/${lib_name}-install.cmake
  LOG_CONFIGURE TRUE
  LOG_BUILD TRUE
  LOG_INSTALL TRUE
)
