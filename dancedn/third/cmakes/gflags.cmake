set(lib_name gflags)
ExternalProject_Add(
  ${lib_name}
  DOWNLOAD_COMMAND ${CMAKE_COMMAND} -E echo "skip download"
  SOURCE_DIR ${CMAKE_SOURCE_DIR}/build/byte-thirdparty/src/byte-thirdparty
  SOURCE_SUBDIR ${lib_name}
  PREFIX ${CMAKE_CURRENT_BINARY_DIR}/${lib_name}
  DOWNLOAD_DIR ${DOWNLOAD_DIR}
  BUILD_IN_SOURCE FALSE
  CMAKE_ARGS
    ${common_cmake_args}
     -DREGISTER_INSTALL_PREFIX=OFF
     -DBUILD_gflags_nothreads_LIB=ON
     -DBUILD_gflags_LIB=ON
  BUILD_COMMAND make -s -j${NUM_JOBS_PER_LIB}
  INSTALL_COMMAND make -s install
  LOG_CONFIGURE TRUE
  LOG_BUILD TRUE
  LOG_INSTALL TRUE
)
