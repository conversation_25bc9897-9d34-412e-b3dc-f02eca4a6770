set(lib_name lavakv-metrics)
ExternalProject_Add(
  ${lib_name}
  DOWNLOAD_COMMAND
    git clone -b 1.0.0 ******************:storage/metrics2-cmake.git ${CMAKE_CURRENT_BINARY_DIR}/${lib_name}/src/${lib_name}
  UPDATE_COMMAND
    git checkout 45028f8c83115fa63f73da52d8c283fe464d144c
  PREFIX ${CMAKE_CURRENT_BINARY_DIR}/${lib_name}
  BUILD_IN_SOURCE FALSE
  CMAKE_ARGS
    ${common_cmake_args}
    "-DCMAKE_PROJECT_INCLUDE=${CMAKE_SOURCE_DIR}/cmakes/${lib_name}-include.cmake"
  BUILD_COMMAND make -s -j${NUM_JOBS_PER_LIB}
  INSTALL_COMMAND ${CMAKE_COMMAND}
    -DINSTALL_DIR=${CMAKE_INSTALL_PREFIX}
    -DSOURCE_DIR=${CMAKE_CURRENT_BINARY_DIR}/${lib_name}/src/${lib_name}
    -DBUILD_DIR=${CMAKE_CURRENT_BINARY_DIR}/${lib_name}/src/${lib_name}-build
    -P ${CMAKE_SOURCE_DIR}/cmakes/${lib_name}-install.cmake
  LOG_CONFIGURE TRUE
  LOG_BUILD TRUE
  LOG_INSTALL TRUE
)

