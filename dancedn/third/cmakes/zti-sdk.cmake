set(lib_name zti-sdk)
ExternalProject_Add(
  ${lib_name}
  GIT_REPOSITORY ******************:security/zti-sdk.git
  GIT_TAG 07d311cf712efbf4dcf8db1b4a33fd4eff004117
  GIT_SUBMODULES ""
  PATCH_COMMAND
    git checkout -- . && git clean -f && patch -p1 < ${CMAKE_SOURCE_DIR}/patches/zti-sdk.patch
  PREFIX ${CMAKE_CURRENT_BINARY_DIR}/${lib_name}
  SOURCE_SUBDIR cpp
  DOWNLOAD_DIR ${DOWNLOAD_DIR}
  BUILD_IN_SOURCE FALSE
  CMAKE_ARGS
    ${common_cmake_args}
    -DCMAKE_CXX_STANDARD=${CMAKE_CXX_STANDARD}
    -DCMAKE_CXX_STANDARD_REQUIRED=${CMAKE_CXX_STANDARD_REQUIRED}
    -DCMAKE_CXX_EXTENSIONS=${CMAKE_CXX_EXTENSIONS}
  BUILD_COMMAND make -s -j${NUM_JOBS_PER_LIB}
  INSTALL_COMMAND make -s install
  LOG_CONFIGURE TRUE
  LOG_BUILD TRUE
  LOG_INSTALL TRUE
  )
