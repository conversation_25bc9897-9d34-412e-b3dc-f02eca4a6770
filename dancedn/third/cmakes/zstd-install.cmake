message(STATUS "install zstd")


file(MAKE_DIRECTORY ${INSTALL_DIR}/include)
file(MAKE_DIRECTORY ${INSTALL_DIR}/include/zstd)
file(MAKE_DIRECTORY ${INSTALL_DIR}/lib)

set(HEADER_DIR ${SOURCE_DIR}/lib)
set(INCLUDE_DIR ${INSTALL_DIR}/include/zstd)

file(GLOB_RECURSE HEADER_FILES
  "${HEADER_DIR}/*.h"
  "${HEADER_DIR}/*.hpp")

foreach(HEADER IN LISTS HEADER_FILES)
  file(RELATIVE_PATH rel_path "${HEADER_DIR}" ${HEADER})
  get_filename_component(rel_dir ${rel_path} DIRECTORY)
  file(COPY ${HEADER} DESTINATION "${INCLUDE_DIR}/${rel_dir}")
  message(STATUS "install ${HEADER} into ${INCLUDE_DIR}/${rel_dir}")
endforeach()

file(COPY ${BUILD_DIR}/lib/libzstd.a DESTINATION ${INSTALL_DIR}/lib)
message(STATUS "install ${BUILD_DIR}/lib/libzstd.a into ${INSTALL_DIR}/lib")

