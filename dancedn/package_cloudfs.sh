#!/bin/bash

set -e -o pipefail
set -x

CUR_DIR=$(dirname "$0")
DANCEDN_DIR=$(cd "${CUR_DIR}"; pwd)
BDS_DIR=$(cd ${DANCEDN_DIR}/..; pwd)

rm -rf ${BDS_DIR}/output
mkdir -p ${BDS_DIR}/output

${DANCEDN_DIR}/build_cloudfs.sh --build-unit-test OFF

cp -r ${DANCEDN_DIR}/package/cloudfs/* ${BDS_DIR}/output/
cp ${DANCEDN_DIR}/build/src/cloudfs/dancedn ${BDS_DIR}/output/bin/
cp ${DANCEDN_DIR}/third/install/current_revision ${BDS_DIR}/output/thirdparty_current_revision
cp ${DANCEDN_DIR}/third/install/lib/libhdfs_client.so ${BDS_DIR}/output/lib/

rm -rf $SOURCEROOT/output/tools/.gitkeep
rm -rf $SOURCEROOT/output/lib/.gitkeep
