// Copyright (c) 2019-present, ByteDance Inc. All rights reserved.

#include "cloudfs/replica_map.h"

#include <cstdint>
#include <utility>

namespace bds::dancedn::cloudfs {

ReplicaMap::ReplicaMap() : add_callback_(nullptr), remove_callback_(nullptr) {}

ReplicaMap::~ReplicaMap() {
  // clear replica map by shared_ptr
  if (add_callback_ != nullptr) {
    delete add_callback_;
  }
  if (remove_callback_ != nullptr) {
    delete remove_callback_;
  }
}

ReplicaInfoPtr ReplicaMap::Get(const std::string& blockpool_id,
                               const uint64_t& block_id) const {
  byte::ScopedLiteRWLock locker(&bp_map_mutex_, 'r');
  auto iter = map_.find(blockpool_id);
  if (iter != map_.end()) {
    ReplicaInfoPtr result;
    if (iter->second.Get(block_id, result)) {
      return result;
    }
  }
  return nullptr;
}

ReplicaInfoPtr ReplicaMap::Get(const std::string& blockpool_id,
                               const Block& block) const {
  ReplicaInfoPtr replica_info = Get(blockpool_id, block.GetBlockID());
  if (replica_info != nullptr && block.GetGS() == replica_info->GetGS()) {
    return replica_info;
  }
  return nullptr;
}

bool ReplicaMap::AddIfNotExist(const std::string& blockpool_id,
                               ReplicaInfoPtr replica_info) {
  if (blockpool_id.empty() || replica_info == nullptr) {
    return false;
  }
  byte::ScopedLiteRWLock locker(&bp_map_mutex_, 'r');
  bool res;
  auto iter = map_.find(blockpool_id);
  if (iter == map_.end()) {
    locker.UpgradeToWriterLock();
    iter = map_.find(blockpool_id);
    if (iter == map_.end()) {
      HashTable<uint64_t, ReplicaInfoPtr> block_map;
      res = block_map.PutIfNotExists(replica_info->GetBlockID(), replica_info);
      map_.emplace(blockpool_id, std::move(block_map));
    } else {
      res =
          iter->second.PutIfNotExists(replica_info->GetBlockID(), replica_info);
    }
  } else {
    res = iter->second.PutIfNotExists(replica_info->GetBlockID(), replica_info);
  }
  if (res) {
    AddReplicaCallback(replica_info);
  }
  return res;
}

void ReplicaMap::Add(const std::string& blockpool_id,
                     ReplicaInfoPtr replica_info) {
  if (blockpool_id.empty() || replica_info == nullptr) {
    return;
  }
  byte::ScopedLiteRWLock locker(&bp_map_mutex_, 'r');
  auto iter = map_.find(blockpool_id);
  if (iter == map_.end()) {
    locker.UpgradeToWriterLock();
    iter = map_.find(blockpool_id);
    if (iter == map_.end()) {
      HashTable<uint64_t, ReplicaInfoPtr> block_map;
      block_map.Put(replica_info->GetBlockID(), replica_info);
      map_.emplace(blockpool_id, std::move(block_map));
    } else {
      iter->second.Put(replica_info->GetBlockID(), replica_info);
    }
  } else {
    iter->second.Put(replica_info->GetBlockID(), replica_info);
  }
  AddReplicaCallback(replica_info);
  return;
}

ReplicaInfoPtr ReplicaMap::Remove(const std::string& blockpool_id,
                                  const Block* block) {
  if (blockpool_id.empty()) {
    return nullptr;
  }
  byte::ScopedLiteRWLock locker(&bp_map_mutex_, 'r');
  auto iter = map_.find(blockpool_id);
  if (iter != map_.end()) {
    ReplicaInfoPtr replica_info;
    auto& hash_table = iter->second;
    if (hash_table.Get(block->GetBlockID(), replica_info) &&
        replica_info != nullptr && replica_info->GetGS() == block->GetGS()) {
      hash_table.Remove(block->GetBlockID());
      RemoveReplicaCallback(replica_info);
      return replica_info;
    }
  }
  return nullptr;
}

ReplicaInfoPtr ReplicaMap::Remove(const std::string& blockpool_id,
                                  const uint64_t& block_id) {
  if (blockpool_id.empty()) {
    return nullptr;
  }
  byte::ScopedLiteRWLock locker(&bp_map_mutex_, 'r');
  auto iter = map_.find(blockpool_id);
  if (iter != map_.end()) {
    ReplicaInfoPtr replica_info;
    auto& hash_table = iter->second;
    if (hash_table.Get(block_id, replica_info)) {
      hash_table.Remove(block_id);
      RemoveReplicaCallback(replica_info);
      return replica_info;
    }
  }
  return nullptr;
}

void ReplicaMap::RemoveBlockPool(const std::string& blockpool_id) {
  if (blockpool_id.empty()) {
    return;
  }
  // TODO(zhouhan): use readlock and UpgradeToWriterLock
  byte::ScopedLiteRWLock locker(&bp_map_mutex_, 'w');
  auto iter_map = map_.find(blockpool_id);
  if (iter_map != map_.end()) {
    iter_map->second.ForEach([this](auto&&, auto&& r) {
      this->RemoveReplicaCallback(r);
    });
    iter_map->second.Clear();
    map_.erase(iter_map);
  }
  return;
}

void ReplicaMap::GetReplicasFromBlockPool(
    const std::string& bpid, std::vector<ReplicaInfoPtr>* replicas) const {
  if (bpid.empty()) {
    return;
  }
  byte::ScopedLiteRWLock locker(&bp_map_mutex_, 'r');
  auto iter = const_cast<ReplicaMap*>(this)->map_.find(bpid);
  if (iter != map_.end()) {
    iter->second.ForEach([&replicas](auto&&, auto&& r) {
      replicas->push_back(r);
    });
  }
  return;
}

std::vector<ReplicaInfoPtr> ReplicaMap::RemoveByDiskId(const std::string& bpid,
                                                       uint32_t disk_id) {
  std::vector<ReplicaInfoPtr> removing;
  if (bpid.empty()) {
    return removing;
  }
  byte::ScopedLiteRWLock locker(&bp_map_mutex_, 'r');
  auto iter = map_.find(bpid);
  if (iter != map_.end()) {
    auto& hash_table = iter->second;
    hash_table.ForEach([&removing, &disk_id](auto&& id, auto&& replica) {
      if (replica->GetDiskId() == disk_id) {
        removing.push_back(replica);
      }
    });
    for (const auto& replica : removing) {
      hash_table.Remove(replica->GetBlockID());
      RemoveReplicaCallback(replica);
    }
  }
  return removing;
}

void ReplicaMap::GetBlockPoolList(
    std::vector<std::string>* block_pool_list) const {
  byte::ScopedLiteRWLock locker(&bp_map_mutex_, 'r');
  for (auto& bp_map : map_) {
    block_pool_list->push_back(bp_map.first);
  }
  return;
}

void ReplicaMap::SetCallback(
    Closure<void, std::shared_ptr<ReplicaInfo>>* add_callback,
    Closure<void, std::shared_ptr<ReplicaInfo>>* remove_callback) {
  add_callback_ = add_callback;
  BYTE_ASSERT(add_callback_ == nullptr || (!add_callback_->IsSelfDelete()));
  remove_callback_ = remove_callback;
  BYTE_ASSERT(remove_callback_ == nullptr ||
              (!remove_callback_->IsSelfDelete()));
}

void ReplicaMap::AddReplicaCallback(
    const std::shared_ptr<ReplicaInfo>& replica) {
  if (add_callback_ != nullptr) {
    add_callback_->Run(replica);
  }
}

void ReplicaMap::RemoveReplicaCallback(
    const std::shared_ptr<ReplicaInfo> replica) {
  if (remove_callback_ != nullptr) {
    remove_callback_->Run(replica);
  }
}

void ReplicaMap::ForEach(
    const std::function<void(const uint64_t&,
                             const std::shared_ptr<ReplicaInfo>&)>& func) {
  byte::ScopedLiteRWLock locker(&bp_map_mutex_, 'r');
  for (auto& bp_map : map_) {
    bp_map.second.ForEach(func);
  }
}

}  // namespace bds::dancedn::cloudfs
