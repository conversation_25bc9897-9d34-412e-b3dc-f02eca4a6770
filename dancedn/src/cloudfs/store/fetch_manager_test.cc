// copyright (c) 2021-present, bytedance inc. all rights reserved.

#include "cloudfs/store/fetch_manager.h"

#include <iostream>
#include <memory>

#include "cloudfs/datanode.h"
#include "cloudfs/exceptions.h"
#include "cloudfs/extended_block.h"
#include "cloudfs/io/io_buf.h"
#include "cloudfs/mocks/mock_datanode.h"
#include "cloudfs/mocks/mock_store.h"
#include "cloudfs/namespace_info.h"
#include "cloudfs/replica_in_pipeline.h"
#include "cloudfs/store/chunkserver/chunkserver_store.h"
#include "cloudfs/store/ufs/tos_store.h"
#include "common/metrics.h"
#include "gmock/gmock-actions.h"
#include "gmock/gmock-cardinalities.h"
#include "gmock/gmock-generated-actions.h"
#include "gmock/gmock-spec-builders.h"
#include "gtest/gtest.h"
#include "thread/this_thread.h"
#include "util/scope_guard.h"

DECLARE_uint64(bytestore_cfs_fetch_max_wait_size);
DECLARE_uint64(bytestore_cfs_fetch_max_wait_number);
DECLARE_uint64(bytestore_cfs_fetch_cache_timeout);

namespace bds::dancedn::cloudfs {

ACTION_P(CountDownLatch, latch) {
  latch->CountDown();
}
using Context = FetchManager::Context;

class FetchManagerTest : public ::testing::Test {
 public:
  void SetUp() override {
    bytestore::metrics_internal::InitFastMetrics();
    dn_ = new MockDataNode();
    store_ = new MockStore();
    EXPECT_CALL(*dn_, GetStorage())
        .Times(::testing::AnyNumber())
        .WillRepeatedly(::testing::Return(store_));
    EXPECT_CALL(*store_, ReadedBlock(::testing::_))
        .Times(::testing::AnyNumber())
        .WillRepeatedly(::testing::Return());
    chunkserver_store_ = new MockChunkserverStore();
    tos_store_ = new MockTosStore();
    fetch_mgr_ = new FetchManager(dn_, chunkserver_store_, tos_store_);
  }

  void TearDown() override {
    store_->StopMgr();
    delete fetch_mgr_;
    delete tos_store_;
    delete chunkserver_store_;
    delete store_;
    delete dn_;
  }
  static void SetUpTestCase() {}

  static void TearDownTestCase() {
    byte::GetLoggingSystem()->Shutdown();
  }

 public:
  MockDataNode* dn_;
  MockStore* store_;
  MockChunkserverStore* chunkserver_store_;
  MockTosStore* tos_store_;
  FetchManager* fetch_mgr_;
};

TEST_F(FetchManagerTest, CompareContext) {
  std::priority_queue<
      std::shared_ptr<Context>, std::vector<std::shared_ptr<Context>>,
      std::function<bool(std::shared_ptr<Context>, std::shared_ptr<Context>)>>
      queue(FetchManager::CompareContext);
  ExtendedBlock block1("BP-1-1", 1, 1234, 123, false);
  std::shared_ptr<Context> ctx1 = std::make_shared<Context>(
      std::shared_ptr<ExtendedBlock>(block1.Clone()),
      FetchManager::CacheType::SYNC, FetchManager::Source::CLIENT);
  ctx1->current_offset_ = 1024;

  ExtendedBlock block2("BP-1-1", 2, 1235, 123, false);
  std::shared_ptr<Context> ctx2 = std::make_shared<Context>(
      std::shared_ptr<ExtendedBlock>(block2.Clone()),
      FetchManager::CacheType::SYNC, FetchManager::Source::CLIENT);
  ctx2->current_offset_ = 1024;

  ExtendedBlock block3("BP-1-1", 3, 1235, 123, false);
  std::shared_ptr<Context> ctx3 = std::make_shared<Context>(
      std::shared_ptr<ExtendedBlock>(block3.Clone()),
      FetchManager::CacheType::SYNC, FetchManager::Source::CLIENT);
  ctx3->current_offset_ = 2048;

  queue.emplace(ctx1);
  queue.emplace(ctx2);
  queue.emplace(ctx3);

  std::shared_ptr<Context> ctx = queue.top();
  ASSERT_EQ(ctx->block_->GetBlockID(), 3);
  queue.pop();
  ctx = queue.top();
  ASSERT_EQ(ctx->block_->GetBlockID(), 1);
  queue.pop();
  ctx = queue.top();
  ASSERT_EQ(ctx->block_->GetBlockID(), 2);
  queue.pop();
}

TEST_F(FetchManagerTest, AsyncCacheBlock) {
  auto&& queue = fetch_mgr_->GetQueue();
  FLAGS_bytestore_cfs_fetch_max_wait_size = 1;
  ExtendedBlock block1("BP-1-1", 1, 1234, 123, false);
  fetch_mgr_->AsyncCacheBlock(&block1, FetchManager::Source::CLIENT);
  ASSERT_TRUE(queue.empty());

  FLAGS_bytestore_cfs_fetch_max_wait_size = 1L * 1024 * 1024 * 1024;
  FLAGS_bytestore_cfs_fetch_max_wait_number = 0;
  fetch_mgr_->AsyncCacheBlock(&block1, FetchManager::Source::CLIENT);
  ASSERT_EQ(queue.size(), 0);
  FLAGS_bytestore_cfs_fetch_max_wait_number = 1000;
  fetch_mgr_->AsyncCacheBlock(&block1, FetchManager::Source::CLIENT);
  ASSERT_EQ(queue.size(), 1);
  fetch_mgr_->AsyncCacheBlock(&block1, FetchManager::Source::CLIENT);
  ASSERT_EQ(queue.size(), 1);
}

TEST_F(FetchManagerTest, StartSyncCacheBlock) {
  ExtendedBlock block1("BP-1-1", 1, 1234, 123, false);
  EXPECT_CALL(*chunkserver_store_, CreateTemporary(::testing::_, ::testing::_,
                                                   ::testing::_, ::testing::_))
      .Times(2)
      .WillOnce(
          ::testing::Return(exceptions::Exception(exceptions::kIOException)))
      .WillOnce(::testing::DoAll(
          ::testing::SetArgPointee<2>(std::make_shared<ReplicaInPipeline>(
              block1, "", false, false, 0, false)),
          ::testing::Return(exceptions::Exception(exceptions::kNoException))));
  // Create temporary failed
  std::shared_ptr<Context> ctx = fetch_mgr_->StartSyncCacheBlock(&block1);
  ASSERT_EQ(ctx, nullptr);
  // Create successfully
  ctx = fetch_mgr_->StartSyncCacheBlock(&block1);
  ASSERT_NE(ctx, nullptr);
  // contexts_ repeated
  ASSERT_EQ(fetch_mgr_->StartSyncCacheBlock(&block1), nullptr);
}

TEST_F(FetchManagerTest, WriteData) {
  ExtendedBlock block1("BP-1-1", 1, 32 * 1024 * 1024, 123, false);
  EXPECT_CALL(*chunkserver_store_, CreateTemporary(::testing::_, ::testing::_,
                                                   ::testing::_, ::testing::_))
      .Times(::testing::AnyNumber())
      .WillRepeatedly(::testing::DoAll(
          ::testing::SetArgPointee<2>(std::make_shared<ReplicaInPipeline>(
              block1, "", false, false, 0, false)),
          ::testing::Return(exceptions::Exception(exceptions::kNoException))));
  std::shared_ptr<Context> ctx1 = std::make_shared<Context>(
      std::shared_ptr<ExtendedBlock>(block1.Clone()),
      FetchManager::CacheType::SYNC, FetchManager::Source::CLIENT);
  uint8_t* data = new uint8_t[16 * 1024 * 1024];
  byte::ScopeGuard data_deleter([data]() {
    delete[] data;
  });
  ctx1->current_offset_ = 0;
  ctx1->chunk_ = new io::IOChunk(16 * 1024 * 1024);
  ctx1->chunk_->WriteBytes(data, 16 * 1024 * 1024);
  ASSERT_FALSE(fetch_mgr_->WriteData(ctx1).OK());  // contexts is emptry
  ctx1->chunk_->AlwaysDestroy();

  ctx1 = fetch_mgr_->StartSyncCacheBlock(&block1);
  ASSERT_NE(ctx1, nullptr);
  ctx1->chunk_ = new io::IOChunk(16 * 1024 * 1024);
  ctx1->chunk_->WriteBytes(data, 16 * 1024 * 1024);

  EXPECT_CALL(*chunkserver_store_,
              WriteBlock(::testing::_, ::testing::_, ::testing::_, ::testing::_,
                         ::testing::_, ::testing::_))
      .Times(64)
      .WillRepeatedly(
          ::testing::Return(exceptions::Exception(exceptions::kNoException)));
  ASSERT_TRUE(fetch_mgr_->WriteData(ctx1).OK());
  // ASSERT_EQ(ctx1->current_offset_, 16 * 1024 * 1024);
  ctx1->chunk_->AlwaysDestroy();
}

TEST_F(FetchManagerTest, FinishSyncCacheBlock) {
  ExtendedBlock block1("BP-1-1", 1, 32 * 1024 * 1024, 123, false);
  EXPECT_CALL(*chunkserver_store_, CreateTemporary(::testing::_, ::testing::_,
                                                   ::testing::_, ::testing::_))
      .Times(::testing::AnyNumber())
      .WillRepeatedly(::testing::DoAll(
          ::testing::SetArgPointee<2>(std::make_shared<ReplicaInPipeline>(
              block1, "", false, false, 0, false)),
          ::testing::Return(exceptions::Exception(exceptions::kNoException))));
  EXPECT_CALL(*chunkserver_store_,
              WriteBlock(::testing::_, ::testing::_, ::testing::_, ::testing::_,
                         ::testing::_, ::testing::_))
      .Times(::testing::AnyNumber())
      .WillRepeatedly(
          ::testing::Return(exceptions::Exception(exceptions::kNoException)));
  uint8_t* data = new uint8_t[16 * 1024 * 1024];
  byte::ScopeGuard data_deleter([data]() {
    delete[] data;
  });

  std::shared_ptr<Context> ctx1 = fetch_mgr_->StartSyncCacheBlock(&block1);
  ASSERT_NE(ctx1, nullptr);
  ctx1->chunk_ = new io::IOChunk(16 * 1024 * 1024);
  ctx1->chunk_->WriteBytes(data, 16 * 1024 * 1024);

  ctx1->chunk_->MarkRead();
  ASSERT_TRUE(fetch_mgr_->WriteData(ctx1).OK());
  ASSERT_EQ(ctx1->current_offset_, 16 * 1024 * 1024);
  ctx1->chunk_->RollbackRead();

  auto&& queue = fetch_mgr_->GetQueue();
  ASSERT_EQ(queue.size(), 0);
  EXPECT_CALL(*chunkserver_store_,
              Invalidate(::testing::_, ::testing::_, ::testing::_))
      .Times(1)
      .WillOnce(::testing::Return(exceptions::Exception()));
  FLAGS_bytestore_cfs_fetch_max_wait_number = 0;
  ctx1->chunk_->AlwaysDestroy();
  fetch_mgr_->FinishSyncCacheBlock(ctx1);
  ASSERT_EQ(queue.size(), 0);

  FLAGS_bytestore_cfs_fetch_max_wait_number = 1000;
  ctx1 = fetch_mgr_->StartSyncCacheBlock(&block1);
  ASSERT_NE(ctx1, nullptr);
  ctx1->chunk_ = new io::IOChunk(16 * 1024 * 1024);
  ctx1->chunk_->WriteBytes(data, 16 * 1024 * 1024);

  ctx1->chunk_->MarkRead();
  ASSERT_TRUE(fetch_mgr_->WriteData(ctx1).OK());
  ASSERT_EQ(ctx1->current_offset_, 16 * 1024 * 1024);
  ctx1->chunk_->RollbackRead();
  fetch_mgr_->FinishSyncCacheBlock(ctx1);
  ASSERT_EQ(queue.size(), 1);

  auto&& e = fetch_mgr_->WriteData(ctx1);
  std::cerr << e.ToString() << std::endl;
  ASSERT_TRUE(e.OK());
  ASSERT_EQ(ctx1->current_offset_, 32 * 1024 * 1024);
  ctx1->chunk_->AlwaysDestroy();

  EXPECT_CALL(*chunkserver_store_,
              UpdateReplicaXAttr(::testing::_, ::testing::_))
      .Times(1)
      .WillOnce(::testing::Return(exceptions::Exception()));
  EXPECT_CALL(*chunkserver_store_, FinalizeBlock(::testing::_, ::testing::_))
      .Times(1)
      .WillOnce(
          ::testing::Return(exceptions::Exception(exceptions::kNoException)));
  EXPECT_CALL(*dn_, NotifyNamenodeReceivedBlock(::testing::_, ::testing::_,
                                                ::testing::_))
      .Times(1)
      .WillOnce(::testing::Return());
  fetch_mgr_->FinishSyncCacheBlock(ctx1);
}

TEST_F(FetchManagerTest, FillUpBlock) {
  ExtendedBlock block1("BP-1-1", 1, 32 * 1024 * 1024, 123, false);
  // std::shared_ptr<Context> ctx1 = std::make_shared<Context>(
  // std::shared_ptr<ExtendedBlock>(block1.Clone()),
  // FetchManager::CacheType::ASYNC);
  byte::CountDownLatch latch(1);
  FLAGS_bytestore_cfs_fetch_cache_timeout = 500;
  EXPECT_CALL(*chunkserver_store_, CreateTemporary(::testing::_, ::testing::_,
                                                   ::testing::_, ::testing::_))
      .Times(2)
      .WillRepeatedly(::testing::DoAll(
          ::testing::SetArgPointee<2>(std::make_shared<ReplicaInPipeline>(
              block1, "", false, false, 0, false)),
          ::testing::Return(exceptions::Exception(exceptions::kNoException))));

  EXPECT_CALL(*chunkserver_store_,
              Invalidate(::testing::_, ::testing::_, ::testing::_))
      .Times(1)
      .WillOnce(::testing::Return(exceptions::Exception()));

  auto&& ctx1 = fetch_mgr_->StartSyncCacheBlock(&block1);
  fetch_mgr_->FinishSyncCacheBlock(ctx1);
  auto&& queue = fetch_mgr_->GetQueue();
  ASSERT_EQ(queue.size(), 1);

  byte::ThisThread::SleepInMs(1000);
  fetch_mgr_->Start();
  byte::ThisThread::SleepInMs(1000);
  EXPECT_CALL(*tos_store_, ReadBlock(::testing::_, ::testing::_, ::testing::_,
                                     ::testing::_, ::testing::_))
      .Times(2)
      .WillRepeatedly(
          ::testing::Return(exceptions::Exception(exceptions::kNoException)));
  EXPECT_CALL(*chunkserver_store_,
              WriteBlock(::testing::_, ::testing::_, ::testing::_, ::testing::_,
                         ::testing::_, ::testing::_))
      .Times(128)
      .WillRepeatedly(
          ::testing::Return(exceptions::Exception(exceptions::kNoException)));
  EXPECT_CALL(*chunkserver_store_,
              UpdateReplicaXAttr(::testing::_, ::testing::_))
      .Times(1)
      .WillOnce(::testing::Return(exceptions::Exception()));
  EXPECT_CALL(*chunkserver_store_, FinalizeBlock(::testing::_, ::testing::_))
      .Times(1)
      .WillOnce(
          ::testing::Return(exceptions::Exception(exceptions::kNoException)));
  EXPECT_CALL(*dn_, NotifyNamenodeReceivedBlock(::testing::_, ::testing::_,
                                                ::testing::_))
      .Times(1)
      .WillOnce(::testing::DoAll(CountDownLatch(&latch), ::testing::Return()));
  fetch_mgr_->AsyncCacheBlock(&block1, FetchManager::Source::CLIENT);
  latch.Wait();
}

}  // namespace bds::dancedn::cloudfs
