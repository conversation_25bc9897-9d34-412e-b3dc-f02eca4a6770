// copyright (c) 2021-present, bytedance inc. all rights reserved.

#pragma once

#include <cstdint>
#include <deque>
#include <map>
#include <memory>
#include <queue>
#include <string>
#include <unordered_set>
#include <utility>
#include <vector>

#include "byte/base/atomic.h"
#include "byte/concurrent/rwlock.h"
#include "byte/thread/dynamic_thread_pool.h"
#include "cloudfs/extended_block.h"
#include "cloudfs/store/chunkserver/chunkserver_store.h"
#include "cloudfs/store/ufs/remote_store.h"
#include "cloudfs/thread.h"

namespace bds::dancedn::cloudfs {

class RecentUploadedCache {
 public:
  RecentUploadedCache(size_t max_size, uint32_t max_duration_ms)
      : max_size_(max_size), max_duration_ms_(max_duration_ms) {}

  void Insert(const std::string& key);
  bool Contains(const std::string& key);

 private:
  void CleanUp();

 private:
  size_t max_size_;
  uint32_t max_duration_ms_;
  std::deque<std::pair<std::string, uint64_t>> uploaded_queue_;
  std::unordered_set<std::string> uploaded_set_;
  mutable byte::Mutex mutex_;
};

class UploadTosMgr : public Thread {
 public:
  enum EventType { UPLOAD, MULTIPART_UPLOAD, APPEND };

  struct Event {
    Event(EventType type, const std::shared_ptr<ExtendedBlock> block,
          uint64_t next_trigger_time)
        : type_(type),
          next_trigger_time_(next_trigger_time),
          clock_(0),
          block_(block) {}

    Event(EventType type, const std::shared_ptr<ExtendedBlock> block,
          uint64_t next_trigger_time, const std::string& upload_id)
        : type_(type),
          next_trigger_time_(next_trigger_time),
          clock_(0),
          block_(block),
          upload_id_(upload_id) {}

    Event(EventType type, const std::shared_ptr<ExtendedBlock> block,
          uint64_t next_trigger_time, const std::string& object_key,
          const std::string& upload_id, const std::vector<uint32_t>& part_nums,
          const std::vector<LocatedBlock*>& prev_blocks)
        : type_(type),
          next_trigger_time_(next_trigger_time),
          clock_(0),
          block_(block),
          object_key_(object_key),
          upload_id_(upload_id),
          part_nums_(part_nums),
          prev_blocks_(prev_blocks) {}
    EventType type_;
    uint64_t next_trigger_time_;
    uint64_t clock_;

    std::shared_ptr<ExtendedBlock> block_;
    std::string object_key_;
    std::string upload_id_;
    std::vector<uint32_t> part_nums_;
    std::vector<LocatedBlock*> prev_blocks_;

    const std::string& GetBlockPoolId() const {
      return block_->GetBlockPoolID();
    }

    uint64_t GetBlockId() const {
      return block_->GetBlockID();
    }

    uint64_t GetNumBytes() {
      return block_->GetNumBytes();
    }

    std::string ToString() const {
      std::stringstream ss;
      ss << "Event type " << type_ << " block " << block_->ToString()
         << " clock " << clock_ << " next_trigger_time " << next_trigger_time_;
      return ss.str();
    }
  };

  struct EventCompare {
    bool operator()(const std::shared_ptr<Event>& lhs,
                    const std::shared_ptr<Event>& rhs) const {
      if (lhs->next_trigger_time_ != rhs->next_trigger_time_) {
        return lhs->next_trigger_time_ > rhs->next_trigger_time_;
      } else {
        return lhs->clock_ > rhs->clock_;
      }
    }
  };

 public:
  // for unit test
  UploadTosMgr();
  UploadTosMgr(DataNode* datanode, ChunkServerStore* chunkserver_store,
               RemoteStore* remote_store);
  virtual ~UploadTosMgr();

  virtual void TriggerUpload(ExtendedBlock* block,
                             const std::string& upload_id);
  virtual void TriggerMultipartUpload(
      ExtendedBlock* block, const std::string& object_key,
      const std::string& upload_id, const std::vector<uint32_t>& part_nums,
      const std::vector<LocatedBlock*>& prev_blocks);
  virtual void TriggerUploadAppend(ExtendedBlock* block);

  inline std::shared_ptr<Event> Front() {
    byte::MutexLocker guard(&qmutex_);
    if (!queue_.empty()) {
      return queue_.top();
    }
    return nullptr;
  }

  inline std::shared_ptr<Event> Pop() {
    byte::MutexLocker guard(&qmutex_);
    if (!queue_.empty()) {
      std::shared_ptr<Event> event = queue_.top();
      queue_.pop();
      return event;
    }
    return nullptr;
  }

 private:
  void Run() override;
  bool IsEmpty();
  inline void Enqueue(const std::shared_ptr<Event>& event);
  bool CheckEventValid(const std::shared_ptr<Event>& event);
  void HandleApplyUploadEvent(std::shared_ptr<Event> event);
  void HandleUploadEvent(std::shared_ptr<Event> event);
  void HandleMultipartUploadEvent(std::shared_ptr<Event> event);
  void HandleAppendEvent(std::shared_ptr<Event> event);

  void AccMultipartUpload(std::shared_ptr<Event> event);
  void MultipartUpload(std::shared_ptr<Event> event);
  // void PutObject(std::shared_ptr<Event> event);
  void AppendObject(std::shared_ptr<Event> event);

 private:
  DataNode* datanode_;
  ChunkServerStore* chunkserver_store_;
  RemoteStore* remote_store_;
  ConcurrentBlockMap<std::shared_ptr<Event>> processing_;
  byte::Mutex qmutex_;
  std::priority_queue<std::shared_ptr<Event>,
                      std::vector<std::shared_ptr<Event>>, EventCompare>
      queue_;
  std::atomic<int32_t> thread_pool_queue_number_;
  std::map<uint64_t, std::unique_ptr<byte::DynamicThreadPool>> thread_pools_;
  RecentUploadedCache cache_;
};

}  // namespace bds::dancedn::cloudfs
