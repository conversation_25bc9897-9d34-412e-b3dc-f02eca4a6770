// copyright (c) 2021-present, bytedance inc. all rights reserved.

#include "cloudfs/store/upload_tos_mgr.h"

#include <cstdint>
#include <iostream>
#include <memory>
#include <string>

#include "cloudfs/constants.h"
#include "cloudfs/exceptions.h"
#include "cloudfs/extended_block.h"
#include "cloudfs/mocks/mock_datanode.h"
#include "cloudfs/mocks/mock_store.h"
#include "cloudfs/namespace_info.h"
#include "cloudfs/store/ufs/tos_info.h"
#include "gtest/gtest.h"
#include "system/timestamp.h"
#include "thread/this_thread.h"

namespace bds::dancedn::cloudfs {

static const uint64_t kBLOCK_SIZE = 32 * 1024 * 1024;

class UploadTosMgrTests : public ::testing::Test {
 public:
  void SetUp() override {
    bytestore::metrics_internal::InitFastMetrics();
    datanode_ = new MockDataNode();
    ns_info_ = new NameSpaceInfo(1, "", "", 2, "", "", 3, 4,
                                 NamespaceType::TOS_MANAGED);
    acc_ns_info_ =
        new NameSpaceInfo(1, "", "", 2, "", "", 3, 4, NamespaceType::ACC_TOS);
    chunkserver_store_ = new MockChunkserverStore();
    tos_store_ = new MockTosStore();
    upload_tos_mgr_ =
        new UploadTosMgr(datanode_, chunkserver_store_, tos_store_);
    bpid_ = "BP-3845026443837452998-1546064706393";
    acc_bpid_ = "BP-acc";
    storage_uuid_ = "DS-3cf64c29-d246-4cfc-990b-4e98f06045ac";
    EXPECT_CALL(*datanode_, GetNamespaceInfo(bpid_))
        .Times(::testing::AnyNumber())
        .WillRepeatedly(::testing::Return(ns_info_));
    EXPECT_CALL(*datanode_, GetNamespaceInfo(acc_bpid_))
        .Times(::testing::AnyNumber())
        .WillRepeatedly(::testing::Return(acc_ns_info_));
    upload_tos_mgr_->Start();
  }

  void TearDown() override {
    delete upload_tos_mgr_;
    delete datanode_;
    delete chunkserver_store_;
    delete tos_store_;
    delete ns_info_;
    delete acc_ns_info_;
  }

  static void SetUpTestCase() {}

  static void TearDownTestCase() {
    byte::GetLoggingSystem()->Shutdown();
  }

 public:
  MockDataNode* datanode_;
  MockChunkserverStore* chunkserver_store_;
  MockTosStore* tos_store_;
  UploadTosMgr* upload_tos_mgr_;
  std::string bpid_;
  std::string acc_bpid_;
  std::string storage_uuid_;
  NameSpaceInfo* ns_info_;
  NameSpaceInfo* acc_ns_info_;
};

TEST_F(UploadTosMgrTests, UploadMultipartUpload) {
  ExtendedBlock block(bpid_, 1, kBLOCK_SIZE, 1, false);
  EXPECT_CALL(*chunkserver_store_, GetReplica(::testing::_, ::testing::_))
      .Times(::testing::AnyNumber())
      .WillRepeatedly(::testing::Return(std::make_shared<FinalizedReplica>(
          &block, "", false, false,
          static_cast<int>(NamespaceType::TOS_MANAGED), false)));
  EXPECT_CALL(*tos_store_, UploadBlock(::testing::_, ::testing::_, ::testing::_,
                                       ::testing::_))
      .Times(2)
      .WillOnce(
          ::testing::Return(exceptions::Exception(exceptions::kIOException)))
      .WillOnce(::testing::Return(exceptions::Exception()));
  EXPECT_CALL(*datanode_, NotifyNamenodeUploaded(::testing::_, ::testing::_,
                                                 ::testing::_, ::testing::_))
      .Times(1)
      .WillOnce(::testing::Return());

  upload_tos_mgr_->TriggerUpload(&block, "");
  byte::ThisThread::SleepInMs(100);
  auto event = upload_tos_mgr_->Pop();
  ASSERT_EQ(event, nullptr);

  upload_tos_mgr_->TriggerUpload(&block, "");
  byte::ThisThread::SleepInMs(100);
  event = upload_tos_mgr_->Pop();
  ASSERT_EQ(event, nullptr);
}

TEST_F(UploadTosMgrTests, AccMultipartUpload) {
  ExtendedBlock block(bpid_, 1, kBLOCK_SIZE, 1, false);
  std::vector<uint32_t> nums;
  std::vector<LocatedBlock*> prevs;
  EXPECT_CALL(*chunkserver_store_, GetReplica(::testing::_, ::testing::_))
      .Times(::testing::AnyNumber())
      .WillRepeatedly(::testing::Return(std::make_shared<FinalizedReplica>(
          &block, "", false, false,
          static_cast<int>(NamespaceType::TOS_MANAGED), false)));
  EXPECT_CALL(*tos_store_,
              AccMultipartUploadBlock(::testing::_, ::testing::_, ::testing::_,
                                      ::testing::_, ::testing::_, ::testing::_))
      .Times(2)
      .WillOnce(
          ::testing::Return(exceptions::Exception(exceptions::kIOException)))
      .WillOnce(::testing::Return(exceptions::Exception()));
  EXPECT_CALL(*datanode_, NotifyNamenodeUploaded(::testing::_, ::testing::_,
                                                 ::testing::_, ::testing::_))
      .Times(1)
      .WillOnce(::testing::Return());
  EXPECT_CALL(*datanode_, FetchBlocksForMultipartUpload(
                              ::testing::_, ::testing::_, ::testing::_))
      .Times(3)
      .WillOnce(
          ::testing::Return(exceptions::Exception(exceptions::kIOException)))
      .WillRepeatedly(::testing::Return(exceptions::Exception()));

  upload_tos_mgr_->TriggerMultipartUpload(&block, "", "uid", nums, prevs);
  byte::ThisThread::SleepInMs(100);
  auto event = upload_tos_mgr_->Pop();
  ASSERT_EQ(event, nullptr);

  upload_tos_mgr_->TriggerMultipartUpload(&block, "", "uid", nums, prevs);
  byte::ThisThread::SleepInMs(100);
  event = upload_tos_mgr_->Pop();
  ASSERT_EQ(event, nullptr);

  upload_tos_mgr_->TriggerMultipartUpload(&block, "", "uid", nums, prevs);
  byte::ThisThread::SleepInMs(100);
  event = upload_tos_mgr_->Pop();
  ASSERT_EQ(event, nullptr);
}

TEST_F(UploadTosMgrTests, Append) {
  ExtendedBlock block(bpid_, 1, kBLOCK_SIZE, 1, false);
  EXPECT_CALL(*chunkserver_store_, GetReplica(::testing::_, ::testing::_))
      .Times(::testing::AnyNumber())
      .WillRepeatedly(::testing::Return(std::make_shared<FinalizedReplica>(
          &block, "", false, false,
          static_cast<int>(NamespaceType::TOS_MANAGED), false)));
  EXPECT_CALL(*tos_store_,
              AppendBlock(::testing::_, ::testing::_, ::testing::_))
      .Times(2)
      .WillOnce(
          ::testing::Return(exceptions::Exception(exceptions::kIOException)))
      .WillOnce(::testing::Return(exceptions::Exception()));
  EXPECT_CALL(*datanode_, NotifyNamenodeUploaded(::testing::_, ::testing::_,
                                                 ::testing::_, ::testing::_))
      .Times(1)
      .WillOnce(::testing::Return());

  upload_tos_mgr_->TriggerUploadAppend(&block);
  byte::ThisThread::SleepInMs(100);
  auto event = upload_tos_mgr_->Pop();
  ASSERT_EQ(event, nullptr);

  upload_tos_mgr_->TriggerUploadAppend(&block);
  byte::ThisThread::SleepInMs(100);
  event = upload_tos_mgr_->Pop();
  ASSERT_EQ(event, nullptr);
}

TEST_F(UploadTosMgrTests, InvalidEvent) {
  ExtendedBlock block(bpid_, 1, kBLOCK_SIZE, 1, false);
  ExtendedBlock err_block1(bpid_, 1, kBLOCK_SIZE, 2, false);
  ExtendedBlock err_block2(bpid_, 1, kBLOCK_SIZE - 1, 1, false);
  EXPECT_CALL(*chunkserver_store_, GetReplica(::testing::_, ::testing::_))
      .Times(5)
      .WillOnce(::testing::Return(nullptr))
      .WillOnce(::testing::Return(std::make_shared<ReplicaBeingWritten>(
          block, "", false, false, static_cast<int>(NamespaceType::TOS_MANAGED),
          false)))
      .WillOnce(::testing::Return(std::make_shared<FinalizedReplica>(
          &err_block1, "", false, false,
          static_cast<int>(NamespaceType::TOS_MANAGED), false)))
      .WillOnce(::testing::Return(std::make_shared<FinalizedReplica>(
          &err_block2, "", false, false,
          static_cast<int>(NamespaceType::TOS_MANAGED), false)))
      .WillRepeatedly(::testing::Return(std::make_shared<FinalizedReplica>(
          &block, "", false, false,
          static_cast<int>(NamespaceType::TOS_MANAGED), false)));
  EXPECT_CALL(*datanode_,
              NotifyNamenodeFailed(::testing::_, ::testing::_, ::testing::_))
      .Times(::testing::AnyNumber())
      .WillRepeatedly(::testing::Invoke(
          [&](ExtendedBlock* block, uint8_t status, const std::string& msg) {
            LOG(WARNING) << "notify namenode failed, "
                         << "block: " << block->ToString()
                         << ", error msg: " << msg << ", status: " << status;
          }));
  EXPECT_CALL(*tos_store_,
              AppendBlock(::testing::_, ::testing::_, ::testing::_))
      .Times(::testing::AnyNumber())
      .WillOnce(::testing::Return(exceptions::Exception()));
  EXPECT_CALL(*datanode_, NotifyNamenodeUploaded(::testing::_, ::testing::_,
                                                 ::testing::_, ::testing::_))
      .Times(::testing::AnyNumber())
      .WillOnce(::testing::Return());

  // replica nullptr
  upload_tos_mgr_->TriggerUploadAppend(&block);
  byte::ThisThread::SleepInMs(100);
  auto event = upload_tos_mgr_->Pop();
  ASSERT_EQ(event, nullptr);

  // replica not finalized
  upload_tos_mgr_->TriggerUploadAppend(&block);
  byte::ThisThread::SleepInMs(100);
  event = upload_tos_mgr_->Pop();
  ASSERT_EQ(event, nullptr);

  // replica gs mismatch
  upload_tos_mgr_->TriggerUploadAppend(&block);
  byte::ThisThread::SleepInMs(100);
  event = upload_tos_mgr_->Pop();
  ASSERT_EQ(event, nullptr);

  // replica len mismatch
  upload_tos_mgr_->TriggerUploadAppend(&block);
  byte::ThisThread::SleepInMs(100);
  event = upload_tos_mgr_->Pop();
  ASSERT_EQ(event, nullptr);

  // skip processing event
  upload_tos_mgr_->TriggerUploadAppend(&block);
  upload_tos_mgr_->TriggerUploadAppend(&block);
  byte::ThisThread::SleepInMs(100);
  event = upload_tos_mgr_->Pop();
  ASSERT_EQ(event, nullptr);

  // skip uploaded event
  upload_tos_mgr_->TriggerUploadAppend(&block);
  byte::ThisThread::SleepInMs(100);
  event = upload_tos_mgr_->Pop();
  ASSERT_EQ(event, nullptr);
}

}  // namespace bds::dancedn::cloudfs
