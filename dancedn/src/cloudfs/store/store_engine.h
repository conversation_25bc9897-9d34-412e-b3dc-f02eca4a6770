// copyright (c) 2025-present, bytedance inc. all rights reserved.
#pragma once

#include <memory>
#include <string>
#include <vector>

#include "byte/include/macros.h"
#include "cloudfs/exceptions.h"
#include "google/protobuf/service.h"
#include "google/protobuf/stubs/callback.h"

namespace cacheengine {
class KVStore;
class ChunkStore;
}  // namespace cacheengine

namespace bds::dancedn::cloudfs {

class StoreEngine {
 public:
  struct InitOptions {
    std::vector<std::string> kv_hash_engines_;
    std::vector<std::string> kv_sorted_engines_;
  };
  struct CreateFileContext {
    google::protobuf::Closure* done_;
    std::string path_;
  };
  struct CreateDirContext {
    google::protobuf::Closure* done_;
    std::string path_;
  };

 public:
  explicit StoreEngine(const std::string& work_dir);
  virtual ~StoreEngine();
  exceptions::Exception Init(const InitOptions& options);
  exceptions::Exception Start();
  void Stop();

  //   virtual void CreateFileAsync(CreateFileContext* context);
  //   virtual void CreateDirAsync(CreateDirContext* context);

 private:
  exceptions::Exception InitKVStore(
      const std::vector<std::string>& hash_engines,
      const std::vector<std::string>& sorted_engines);

 private:
  std::string work_dir_;
  cacheengine::KVStore* kv_store_;
  cacheengine::ChunkStore* chunk_store_;

 private:
  DISALLOW_COPY_AND_ASSIGN(StoreEngine);
};
}  // namespace bds::dancedn::cloudfs
