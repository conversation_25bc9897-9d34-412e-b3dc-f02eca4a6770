// copyright (c) 2024-present, bytedance inc. all rights reserved.

#pragma once

#include <array>
#include <cstdint>
#include <memory>
#include <unordered_map>

#include "butil/containers/hash_tables.h"
#include "butil/macros.h"
#include "cloudfs/block_lock.h"
#include "cloudfs/constants.h"
#include "cloudfs/extended_block.h"
#include "cloudfs/store/chunkserver/partial_block.h"
#include "concurrent/hashtable.h"
#include "concurrent/lite_lock.h"
#include "concurrent/mutex.h"
#include "concurrent/rwlock.h"
#include "encoding/int128.h"

namespace bds::dancedn::cloudfs {

class PartialBlockLocks {
 public:
  PartialBlockLocks();
  ~PartialBlockLocks() = default;

  byte::LiteRWLock* GetBlockLock(const uint128_t_pod& key);
  void ReleaseBlockLock(const uint128_t_pod& key);

  byte::LiteLock* GetPartialBlockLock(const PartialBlockId& id);
  void ReleasePartialBlockLock(const PartialBlockId& id);

 public:
  uint32_t TestGetBlockLocksSize(int bucket) {
    return block_mutexs_[bucket].size();
  }

  uint32_t TestGetPartialBlockLocksSize(int bucket) {
    return partial_block_mutexs_[bucket].size();
  }

 private:
  template <typename T>
  struct LockWithRef {
    LockWithRef() : lock_(), ref_count_(0) {}
    T lock_;
    int ref_count_;
  };

 private:
  std::array<byte::LiteLock, BLOCK_BUCKET_SIZE> locks_;
  std::array<std::unordered_map<uint128_t_pod, LockWithRef<byte::LiteRWLock>*,
                                ExtendedBlockKeyHash>,
             BLOCK_BUCKET_SIZE>
      block_mutexs_;
  std::array<std::unordered_map<PartialBlockId, LockWithRef<byte::LiteLock>*,
                                PartialBlockIdHash>,
             BLOCK_BUCKET_SIZE>
      partial_block_mutexs_;

 private:
  DISALLOW_COPY_AND_ASSIGN(PartialBlockLocks);
};

}  // namespace bds::dancedn::cloudfs
