// Copyright (c) 2022, ByteDance Inc. All rights reserved.

#include "cloudfs/store/chunkserver/disk_usage.h"

#include <cstdint>
#include <memory>
#include <string>
#include <tuple>
#include <unordered_map>
#include <utility>
#include <vector>

#include "byte/include/macros.h"
#include "cloudfs/cfs/storage_class.h"
#include "cloudfs/extended_block.h"
#include "cloudfs/metrics.h"
#include "cloudfs/namespace_info.h"
#include "cloudfs/replica_info.h"
#include "cloudfs/replica_state.h"
#include "cloudfs/store/chunkserver/partial_block.h"
#include "common/metrics.h"
#include "concurrent/spinlock.h"
#include "string/algorithm.h"
#include "string/format/print.h"

namespace bds::dancedn::cloudfs {

static const char* BlockTypeTagString(DanceDNChunkType type) {
  switch (type) {
    case DanceDNChunkType::BLOCK: return "full";
    case DanceDNChunkType::PARTIAL_BLOCK: return "partial";
    default: return "unknown";
  }
}

void DiskUsage::InitDiskIdToTypes(
    std::unordered_map<uint32_t, std::string> diskid_type_map) {
  diskid_type_map_ = std::move(diskid_type_map);
}

void DiskUsage::IncReplica(const std::shared_ptr<ReplicaInfo>& replica) {
  if (replica->GetState() != ReplicaState::FINALIZED) {
    return;
  }
  DanceDNChunkType type = DanceDNChunkType::BLOCK;
  auto&& usage = GetUsage(replica->GetDiskId(), replica->GetBlock()->GetNsId());
  usage->Inc(replica->IsEvictable(), replica->GetStorageClass(),
             replica->GetBlock()->IsPin(), type, replica->GetBytesOnDisk());
  bytestore::Tags tags = {
      {"disk_id", std::to_string(replica->GetDiskId())},
      {"disk_type", diskid_type_map_[replica->GetDiskId()]},
      {"bpid", std::to_string(replica->GetBlock()->GetNsId())},
      {"evictable", replica->IsEvictable() ? "true" : "false"},
      {"storage_class", StorageClassToString(replica->GetStorageClass())},
      {"pin", replica->GetBlock()->IsPin() ? "true" : "false"},
      {"type", BlockTypeTagString(type)}};
  METRICS_disk_usage_v2_num->GetMetric(tags)->Add(1);
  METRICS_disk_usage_v2_size->GetMetric(tags)->Add(replica->GetBytesOnDisk());

  auto&& disk_usage = GetDiskUsage(replica->GetDiskId());
  disk_usage->Inc(replica->IsEvictable(), replica->GetStorageClass(),
                  replica->GetBlock()->IsPin(), type,
                  replica->GetBytesOnDisk());
}

void DiskUsage::DecReplica(const std::shared_ptr<ReplicaInfo>& replica) {
  if (replica->GetState() != ReplicaState::FINALIZED) {
    return;
  }
  DanceDNChunkType type = DanceDNChunkType::BLOCK;
  auto&& usage = GetUsage(replica->GetDiskId(), replica->GetBlock()->GetNsId());
  usage->Dec(replica->IsEvictable(), replica->GetStorageClass(),
             replica->GetBlock()->IsPin(), type, replica->GetBytesOnDisk());
  bytestore::Tags tags = {
      {"disk_id", std::to_string(replica->GetDiskId())},
      {"disk_type", diskid_type_map_[replica->GetDiskId()]},
      {"bpid", std::to_string(replica->GetBlock()->GetNsId())},
      {"evictable", replica->IsEvictable() ? "true" : "false"},
      {"storage_class", StorageClassToString(replica->GetStorageClass())},
      {"pin", replica->GetBlock()->IsPin() ? "true" : "false"},
      {"type", BlockTypeTagString(type)}};
  METRICS_disk_usage_v2_num->GetMetric(tags)->Minus(1);
  METRICS_disk_usage_v2_size->GetMetric(tags)->Minus(replica->GetBytesOnDisk());

  auto&& disk_usage = GetDiskUsage(replica->GetDiskId());
  disk_usage->Dec(replica->IsEvictable(), replica->GetStorageClass(),
                  replica->GetBlock()->IsPin(), type,
                  replica->GetBytesOnDisk());
}

void DiskUsage::IncPartialBlock(
    const std::shared_ptr<BlockV2>& block,
    const std::shared_ptr<PartialBlock>& partial_block) {
  DanceDNChunkType type = DanceDNChunkType::PARTIAL_BLOCK;
  auto&& usage = GetUsage(block->GetDiskId(), block->GetNsId());
  usage->Inc(true, block->GetStorageClass(), block->IsPin(), type,
             partial_block->Numbytes());
  bytestore::Tags tags = {
      {"disk_id", std::to_string(block->GetDiskId())},
      {"disk_type", diskid_type_map_[block->GetDiskId()]},
      {"bpid", std::to_string(block->GetNsId())},
      {"evictable", "true"},
      {"storage_class", StorageClassToString(block->GetStorageClass())},
      {"pin", block->IsPin() ? "true" : "false"},
      {"type", BlockTypeTagString(type)}};
  METRICS_disk_usage_v2_num->GetMetric(tags)->Add(1);
  METRICS_disk_usage_v2_size->GetMetric(tags)->Add(partial_block->Numbytes());

  auto&& disk_usage = GetDiskUsage(block->GetDiskId());
  disk_usage->Inc("true", block->GetStorageClass(), block->IsPin(), type,
                  partial_block->Numbytes());
}

void DiskUsage::DecPartialBlock(
    const std::shared_ptr<BlockV2>& block,
    const std::shared_ptr<PartialBlock>& partial_block) {
  DanceDNChunkType type = DanceDNChunkType::PARTIAL_BLOCK;
  auto&& usage = GetUsage(block->GetDiskId(), block->GetNsId());
  usage->Dec(true, block->GetStorageClass(), block->IsPin(), type,
             partial_block->Numbytes());
  bytestore::Tags tags = {
      {"disk_id", std::to_string(block->GetDiskId())},
      {"disk_type", diskid_type_map_[block->GetDiskId()]},
      {"bpid", std::to_string(block->GetNsId())},
      {"evictable", "true"},
      {"storage_class", StorageClassToString(block->GetStorageClass())},
      {"pin", block->IsPin() ? "true" : "false"},
      {"type", BlockTypeTagString(type)}};
  METRICS_disk_usage_v2_num->GetMetric(tags)->Minus(1);
  METRICS_disk_usage_v2_size->GetMetric(tags)->Minus(partial_block->Numbytes());

  auto&& disk_usage = GetDiskUsage(block->GetDiskId());
  disk_usage->Dec("true", block->GetStorageClass(), block->IsPin(), type,
                  partial_block->Numbytes());
}

DiskUsage::DiskUsage(const std::set<uint32_t>& disks)
    : mutex_(), disk_usages_(), usages_() {
  auto&& iter = disks.begin();
  while (iter != disks.end()) {
    usages_.emplace(*iter,
                    std::unordered_map<uint64_t, std::shared_ptr<Usage>>());
    disk_usages_[*iter] = std::make_shared<Usage>();
    iter++;
  }
}

DiskUsage::~DiskUsage() {}

const std::shared_ptr<DiskUsage::Usage>& DiskUsage::GetUsage(uint32_t disk_id,
                                                             uint64_t nsid) {
  byte::SpinLock::Locker guard(&mutex_);
  auto&& iter = usages_[disk_id].find(nsid);
  if (UNLIKELY(iter == usages_[disk_id].end())) {
    usages_[disk_id][nsid] = std::make_shared<Usage>();
    iter = usages_[disk_id].find(nsid);
  }
  return iter->second;
}

const std::shared_ptr<DiskUsage::Usage>& DiskUsage::GetDiskUsage(
    uint32_t disk_id) {
  byte::SpinLock::Locker guard(&mutex_);
  auto&& iter = disk_usages_.find(disk_id);
  if (UNLIKELY(iter == disk_usages_.end())) {
    disk_usages_[disk_id] = std::make_shared<Usage>();
    iter = disk_usages_.find(disk_id);
  }
  return iter->second;
}

void DiskUsage::UpdateBySnapshotOfDisk(
    uint32_t disk_id,
    const std::unordered_map<uint64_t, std::shared_ptr<Usage>>& snapshot) {
  std::unordered_map<uint64_t, std::shared_ptr<Usage>> ns_usages;
  std::shared_ptr<Usage> disk_usages = std::make_shared<Usage>();
  for (auto&& s : snapshot) {
    ns_usages.emplace(s.first, s.second->Copy());
    disk_usages->Merge(*(s.second));
  }
  for (auto&& usage : ns_usages) {
    for (int evictable = 0; evictable <= 1; evictable++) {
      for (int storage_class = 0;
           storage_class < static_cast<int>(StorageClass::BOUNDARY);
           storage_class++) {
        for (int pin = 0; pin <= 1; pin++) {
          for (int type = 0; type <= DanceDNChunkType::PARTIAL_BLOCK; type++) {
            DanceDNChunkType type_enum = static_cast<DanceDNChunkType>(type);
            bytestore::Tags tags = {
                {"disk_id", std::to_string(disk_id)},
                {"disk_type", diskid_type_map_[disk_id]},
                {"bpid", std::to_string(usage.first)},
                {"evictable", evictable ? "true" : "false"},
                {"storage_class",
                 StorageClassToString(
                     static_cast<StorageClass>(storage_class))},
                {"pin", pin ? "true" : "false"},
                {"type", BlockTypeTagString(type_enum)}};
            std::pair<Usage::Number, Usage::Size> value =
                usage.second->GetValue(evictable,
                                       static_cast<StorageClass>(storage_class),
                                       pin, type_enum);
            LOG(INFO) << byte::StringPrint(
                "Set disk_usage (%d, %s, %llu, %s, %s, %s, %s) values(%lld, "
                "%lld)",
                disk_id, diskid_type_map_[disk_id], usage.first,
                evictable ? "true" : "false",
                StorageClassToString(static_cast<StorageClass>(storage_class)),
                pin ? "true" : "false", BlockTypeTagString(type_enum),
                value.first, value.second);
            METRICS_disk_usage_v2_num->GetMetric(tags)->SetValue(value.first);
            METRICS_disk_usage_v2_size->GetMetric(tags)->SetValue(value.second);
          }
        }
      }
    }
  }

  byte::SpinLock::Locker guard(&mutex_);
  usages_[disk_id] = std::move(ns_usages);
  disk_usages_[disk_id] = std::move(disk_usages);
}

}  // namespace bds::dancedn::cloudfs
