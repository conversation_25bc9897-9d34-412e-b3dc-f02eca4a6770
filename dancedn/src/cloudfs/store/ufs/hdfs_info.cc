// copyright (c) 2021-present, bytedance inc. all rights reserved.

#include "cloudfs/store/ufs/hdfs_info.h"

namespace bds::dancedn::cloudfs {

HDFSInfo::HDFSInfo(const std::string& nn, uint16_t port,
                   const std::string& user, const std::string& prefix,
                   const std::string& consul, const std::string& sec_token)
    : nn_(nn),
      port_(port),
      user_(user),
      prefix_(prefix),
      consul_(consul),
      sec_token_(sec_token) {
  type = RemoteBlockInfo::HDFS;
}

std::string HDFSInfo::ToString() const {
  return byte::StringPrint("nn_addr=%s;port=%d;user=%s;prefix=%s;consul=%s",
                           nn_, port_, user_, prefix_, consul_);
}

std::string HDFSInfo::SecString() const {
  return byte::StringPrint(
      "nn_addr=%s;port=%d;user=%s;prefix=%s;consul=%s,sec_token:%s", nn_, port_,
      user_, prefix_, consul_, sec_token_);
}

bool HDFSInfo::operator==(const HDFSInfo& another) {
  return nn_ == another.nn_ && port_ == another.port_ &&
         user_ == another.user_ && prefix_ == another.prefix_ &&
         consul_ == another.consul_ && sec_token_ == another.sec_token_;
}

HDFSInfo* HDFSInfo::ParseProto(const ::cloudfs::HDFSInfoProto& proto) {
  LOG(DEBUG) << "Fetched from proto: "
             << "nn_addr: " << proto.nn() << " nn_port: " << proto.port()
             << " user: " << proto.user() << " consul: " << proto.consul()
             << " sec token: " << proto.sectoken();
  return new HDFSInfo(proto.nn(), proto.port(), proto.user(), proto.prefix(),
                      proto.consul(), proto.sectoken());
}

}  // namespace bds::dancedn::cloudfs
