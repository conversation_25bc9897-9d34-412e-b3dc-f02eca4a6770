// copyright (c) 2021-present, bytedance inc. all rights reserved.

#include "cloudfs/store/ufs/aws_credentials_provider.h"

#include <cassert>
#include <memory>

#include "aws/core/auth/AWSCredentials.h"
#include "aws/core/auth/AWSCredentialsProvider.h"
#include "aws/core/auth/AWSCredentialsProviderChain.h"
#include "aws/core/utils/threading/ReaderWriterLock.h"
#include "byte/include/assert.h"
#include "byte/include/byte_log.h"
#include "gflags/gflags_declare.h"

namespace bds::dancedn::cloudfs {

AwsCredentialsProviderImpl::AwsCredentialsProviderImpl()
    : credentials_(Aws::Auth::AWSCredentials()) {}

AwsCredentialsProviderImpl::~AwsCredentialsProviderImpl() {}

void AwsCredentialsProviderImpl::UpdateCredetials(
    const Aws::Auth::AWSCredentials& credentials) {
  Aws::Utils::Threading::WriterLockGuard guard(m_reloadLock);
  credentials_ = credentials;
}

Aws::Auth::AWSCredentials AwsCredentialsProviderImpl::GetAWSCredentials() {
  Aws::Utils::Threading::ReaderLockGuard guard(m_reloadLock);
  return credentials_;
}

}  // namespace bds::dancedn::cloudfs
