// copyright (c) 2021-present, bytedance inc. all rights reserved.

#pragma once

#include <memory>
#include <string>

#include "aws/core/utils/logging/LogLevel.h"
#include "aws/core/utils/logging/LogSystemInterface.h"

namespace bds::dancedn::cloudfs {

class AwsLogSystemInterfaceImpl
    : public Aws::Utils::Logging::LogSystemInterface {
 public:
  explicit AwsLogSystemInterfaceImpl(Aws::Utils::Logging::LogLevel level);
  virtual ~AwsLogSystemInterfaceImpl();
  /**
   * Gets the currently configured log level for this logger.
   */
  Aws::Utils::Logging::LogLevel GetLogLevel(void) const override;
  /**
   * Does a printf style output to the output stream. Don't use this, it's
   * unsafe. See LogStream
   */
  void Log(Aws::Utils::Logging::LogLevel logLevel, const char* tag,
           const char* formatStr, ...) override;
  /**
   * Writes the stream to the output stream.
   */
  void LogStream(Aws::Utils::Logging::LogLevel logLevel, const char* tag,
                 const Aws::OStringStream& messageStream) override;
  /**
   * Writes any buffered messages to the underlying device if the logger
   * supports buffering.
   */
  void Flush() override {}

 public:
  static std::shared_ptr<Aws::Utils::Logging::LogSystemInterface> Build(
      Aws::Utils::Logging::LogLevel level);

 private:
  Aws::Utils::Logging::LogLevel level_;
};

}  // namespace bds::dancedn::cloudfs
