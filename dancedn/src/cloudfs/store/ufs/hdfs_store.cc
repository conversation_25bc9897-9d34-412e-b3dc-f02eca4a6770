// copyright (c) 2021-present, bytedance inc. all rights reserved.

#include "cloudfs/store/ufs/hdfs_store.h"

#include <fcntl.h>

#include <algorithm>
#include <cstdint>
#include <string>
#include <utility>
#include <vector>

#include "aws/s3/model/CompletedPart.h"
#include "butil/md5.h"
#include "byte/io/file_path.h"
#include "byte/system/uuid.h"
#include "cloudfs/block_pool_manager.h"
#include "cloudfs/cfs/io_priority.h"
#include "cloudfs/datanode.h"
#include "cloudfs/exceptions.h"
#include "cloudfs/metrics.h"
#include "cloudfs/namespace_info.h"
#include "cloudfs/opstats/op_stats.h"
#include "cloudfs/store.h"
#include "cloudfs/store/ufs/hdfs_info.h"
#include "cloudfs/store/ufs/tos_acquire.h"
#include "concurrent/rwlock.h"
#include "util/scope_guard.h"

DECLARE_uint64(bytestore_chunkserver_hdfs_client_reuse_limit);
DECLARE_int32(bytestore_chunkserver_hdfs_client_retry_count);

#define RECORD_METRICS_BEFORE(operation, bpid)                                \
  METRICS_hdfs_store_##operation##_num->GetMetric({{"bpid", (bpid)}})         \
      ->Increment();                                                          \
  METRICS_hdfs_store_##operation##_ongoing_num->GetMetric({{"bpid", (bpid)}}) \
      ->Increment();                                                          \
  DURATION_START(record_metrics);

#define RECORD_METRICS_AFTER(operation, bpid, outcome)                        \
  METRICS_hdfs_store_##operation##_latency->GetMetric({{"bpid", (bpid)}})     \
      ->Set(DURATION_END(record_metrics));                                    \
  METRICS_hdfs_store_##operation##_ongoing_num->GetMetric({{"bpid", (bpid)}}) \
      ->Decrement();                                                          \
  if (outcome == -1) {                                                        \
    METRICS_hdfs_store_##operation##_error_num->GetMetric({{"bpid", (bpid)}}) \
        ->Increment();                                                        \
  }

#define RECORD_METRICS_WITH_THROUGHPUT_AFTER(operation, bpid, outcome, size)   \
  METRICS_hdfs_store_##operation##_latency->GetMetric({{"bpid", (bpid)}})      \
      ->Set(DURATION_END(record_metrics));                                     \
  METRICS_hdfs_store_##operation##_ongoing_num->GetMetric({{"bpid", (bpid)}})  \
      ->Decrement();                                                           \
  if (outcome == -1) {                                                         \
    METRICS_hdfs_store_##operation##_error_num->GetMetric({{"bpid", (bpid)}})  \
        ->Increment();                                                         \
  } else {                                                                     \
    METRICS_hdfs_store_##operation##_throughput->GetMetric({{"bpid", (bpid)}}) \
        ->Add((size));                                                         \
  }

namespace bds::dancedn::cloudfs {

static const char CONSUL_NAME_KEY[] = "dfs.client.nnproxy.consul.name";
static const char CONSUL_MODE_KEY[] = "dfs.client.nnproxy.consul.enablemode";
static const char CUSTOM_TOKEN_KEY[] = "ipc.client.custom_token";
const char HdfsStore::kHdfsEtag[] = "ACC_HDFS";

const int HdfsClient::RETRY_CNT =
    FLAGS_bytestore_chunkserver_hdfs_client_retry_count;

HdfsClient::HdfsFile::~HdfsFile() {
  if (file == nullptr) {
    return;
  }

  int ret_code = hdfsCloseFile(fs, file);
  if (ret_code == -1) {
    LOG(ERROR) << "Failed to close hdfs file, " << hdfsGetLastError();
  }
}

HdfsClient::HdfsClient(const HDFSInfoPtr& hdfs_info) {
  hdfs_info_ = hdfs_info;
  builder = hdfsNewBuilder();
  auto&& consul = hdfs_info->GetConsul();
  if (!consul.empty()) {
    hdfsBuilderConfSetStr(builder, CONSUL_NAME_KEY, consul.c_str());
    hdfsBuilderConfSetStr(builder, CONSUL_MODE_KEY, "try");
  } else {
    auto&& nn = hdfs_info->GetNameNodeAddr();
    if (!nn.empty()) {
      hdfsBuilderSetNameNode(builder, nn.c_str());
    }
    auto port = hdfs_info->GetNameNodePort();
    if (port > 0) {
      hdfsBuilderSetNameNodePort(builder, port);
    }
    auto&& user = hdfs_info->GetUser();
    if (!user.empty()) {
      hdfsBuilderSetUserName(builder, user.c_str());
    }
    hdfsBuilderConfSetStr(builder, CONSUL_MODE_KEY, "off");
  }

  if (hdfs_info->GetSecToken().empty()) {
    LOG(INFO) << "HDFS token is empty, use default token."
              << hdfs_info->ToString();
  } else {
    hdfsBuilderSetToken(builder, hdfs_info->GetSecToken().c_str());
    hdfsBuilderConfSetStr(builder, CUSTOM_TOKEN_KEY,
                          hdfs_info->GetSecToken().c_str());
    LOG(INFO) << "Set hdfs token " << hdfs_info->ToString();
  }
  fs = hdfsBuilderConnect(builder);
}
HdfsClient::~HdfsClient() {
  if (fs != nullptr) {
    int ret_code = hdfsDisconnect(fs);
    if (ret_code == -1) {
      LOG(ERROR) << "Failed to disconnect hdfs, " << hdfsGetLastError();
    }
  }
  if (builder != nullptr) {
    hdfsFreeBuilder(builder);
  }
}

std::shared_ptr<HdfsClient::HdfsFile> HdfsClient::Open(const std::string& path,
                                                       int buffer_size,
                                                       int flags) {
  hdfsFile file;
  Call([&]() {
    if (flags == 0) {
      file = hdfsOpenFileV2(fs, path.c_str(), buffer_size, nullptr);
    } else {
      file = hdfsOpenFile(fs, path.c_str(), flags, buffer_size, 0, 0);
    }

    return file;
  });
  if (file == nullptr) {
    return nullptr;
  }

  return std::make_shared<HdfsClient::HdfsFile>(fs, file);
}

int32_t HdfsClient::Pread(hdfsFile file, int64_t offset, void* buffer,
                          int32_t length) {
  return Call([&]() {
    return hdfsPread(fs, file, offset, buffer, length);
  });
}

int32_t HdfsClient::Write(hdfsFile file, void* buffer, int32_t length) {
  return hdfsWrite(fs, file, buffer, length);
}

int32_t HdfsClient::Flush(hdfsFile file) {
  return Call([&]() {
    return hdfsFlush(fs, file);
  });
}

int32_t HdfsClient::Delete(const std::string& path) {
  return Call([&]() {
    return hdfsDelete(fs, path.c_str(), 0);
  });
}

int64_t HdfsClient::GetVisibleLength(hdfsFile file) {
  return Call([&]() {
    return hdfsGetVisibleLength(fs, file);
  });
}

hdfsFileInfo* HdfsClient::GetPathInfo(const std::string& path) {
  hdfsFileInfo* file_info;
  Call([&]() {
    file_info = hdfsGetPathInfo(fs, path.c_str());
    return file_info;
  });
  return file_info;
}

void HdfsClient::FreeFileInfo(hdfsFileInfo* file_info, int num_entries) {
  hdfsFreeFileInfo(file_info, num_entries);
}

int32_t HdfsClient::RecoverLease(const std::string& path) {
  return Call([&]() {
    return hdfsRecoverLease(fs, path.c_str());
  });
}

bool HdfsClient::Exists(const std::string& path) {
  return Call([&]() {
           return hdfsExists(fs, path.c_str());
         }) == 0;
}

HdfsStore::HdfsStore(Store* store, DataNode* dn)
    : RemoteStore(store, dn), hdfs_acquire_(new HdfsAcquire(dn)) {}

HdfsClient* HdfsStore::AcquireHdfsClient(const std::string& bpid,
                                         const HDFSInfoPtr& info) {
  {
    byte::RwLock::ReaderLocker guard(&lock_);
    auto iter = client_pool_.find(bpid);
    if (iter != client_pool_.end()) {
      return iter->second.Get(info);
    }
  }
  {
    byte::RwLock::WriterLocker guard(&lock_);
    auto iter = client_pool_.find(bpid);
    if (iter != client_pool_.end()) {
      return iter->second.Get(info);
    }

    client_pool_.emplace(bpid,
                         FLAGS_bytestore_chunkserver_hdfs_client_reuse_limit);
    iter = client_pool_.find(bpid);
    BYTE_ASSERT(iter != client_pool_.end());
    return iter->second.Get(info);
  }
}

void HdfsStore::ReleaseHdfsClient(const std::string& bpid, HdfsClient* client) {
  byte::RwLock::ReaderLocker guard(&lock_);
  auto iter = client_pool_.find(bpid);
  BYTE_ASSERT(iter != client_pool_.end());
  iter->second.Release(client);
}

HDFSInfoPtr HdfsStore::GetHdfsInfo(const std::string& bpid) {
  return std::dynamic_pointer_cast<HDFSInfo>(dn_->GetRemoteBlockInfo(bpid));
}

exceptions::Exception HdfsStore::UploadBlock(const ExtendedBlock& block,
                                             const std::string& upload_id,
                                             bytestore::IOPriority priority,
                                             CompletedPart* completed) {
  if (upload_id.empty()) {
    std::string err_msg = byte::StringPrint(
        "Cannot upload block %s with empty upload id", block.ToString());
    return exceptions::Exception(exceptions::kIllegalArgumentException,
                                 err_msg);
  }

  if (block.GetObjectKey().empty()) {
    std::string err_msg = byte::StringPrint(
        "Cannot upload block %s with empty object_key", block.ToString());
    return exceptions::Exception(exceptions::kIllegalArgumentException,
                                 err_msg);
  }

  auto&& bpid = block.GetBlockPoolID();

  auto&& hdfs_info = GetHdfsInfo(bpid);
  if (nullptr == hdfs_info) {
    std::string err_msg = byte::StringPrint(
        "Cannot find HDFS Info for block: %s.", block.ToString());
    return exceptions::Exception(exceptions::kFalseException, err_msg);
  }

  auto length = block.GetNumBytes();
  LOG(DEBUG) << "Start upload object: " << block.GetObjectKey()
             << ", block: " << block.ToString() << ", length: " << length;

  io::IOChunk* chunk = new io::IOChunk(length);

  DURATION_START(qos);
  LocalThroughputAcquireWrite(length, priority);
  hdfs_acquire_->ThroughputAcquireWrite(length, priority);
  hdfs_acquire_->QpsAcquire(Interface::PUT_OBJECT, priority);
  METRICS_hdfs_store_qos_write_latency
      ->GetMetric(
          {{"bpid", (bpid)},
           {"priority", IOPriorityConverter::IOPriorityToString(priority)}})
      ->Set(DURATION_END(qos));
  auto* client = AcquireHdfsClient(bpid, hdfs_info);
  if (client == nullptr) {
    chunk->AlwaysDestroy();
    return exceptions::Exception(exceptions::kFalseException,
                                 hdfsGetLastError());
  }
  byte::ScopeGuard releaser([&]() {
    chunk->AlwaysDestroy();
    ReleaseHdfsClient(bpid, client);
  });

  if (nullptr == chunk->Data()) {
    return exceptions::Exception(exceptions::kOutOfMemory,
                                 "Failed to allocate IOChunk's data buffer.");
  }

  exceptions::Exception e;
  e = ReadBlockFromLocal(const_cast<ExtendedBlock*>(&block), 0, length, chunk,
                         priority);
  if (!e.OK()) {
    return e;
  }

  BYTE_ASSERT((uint32_t)(chunk->Length()) == length);
  if ((uint32_t)(chunk->Length()) != length) {
    return exceptions::Exception(
        exceptions::kIOException,
        "Chunk data length not consistent with requested length.");
  }

  {
    // TODO(@max.chenxi): need idempotence guarantee
    auto&& file_ptr = client->Open(block.GetObjectKey(),
                                   std::min<int>(length, INT32_MAX), O_WRONLY);
    if (file_ptr == nullptr) {
      return exceptions::Exception(exceptions::kFileNotFoundException,
                                   "Open file: " + block.GetObjectKey() +
                                       " failed, " + hdfsGetLastError());
    }

    uint64_t total_write = 0;
    tSize write = 0;

    RECORD_METRICS_BEFORE(write, bpid);
    do {
      write = client->Write(file_ptr->file, chunk->Data() + total_write,
                            length - total_write);
      total_write += write;
    } while (write > 0 && total_write < length);
    RECORD_METRICS_WITH_THROUGHPUT_AFTER(write, bpid, write, length);

    if (write == -1) {
      return exceptions::Exception(exceptions::kIOException,
                                   "Write file: " + block.GetObjectKey() +
                                       " failed, " + hdfsGetLastError());
    }

    BYTE_ASSERT(total_write == length);

    if (client->Flush(file_ptr->file) == -1) {
      return exceptions::Exception(exceptions::kIOException,
                                   "Flush file: " + block.GetObjectKey() +
                                       " failed, " + hdfsGetLastError());
    }
  }
  completed->SetPartNumber(1);
  completed->SetETag(kHdfsEtag);

  return e;
}

exceptions::Exception HdfsStore::AppendBlock(const ExtendedBlock& block,
                                             bytestore::IOPriority priority,
                                             std::string& etag) {
  LOG(INFO) << "Start append object: " << block.GetObjectKey()
            << ", block: " << block.ToString()
            << ", offset: " << block.GetOffset()
            << ", length: " << block.GetNumBytes();
  auto&& bpid = block.GetBlockPoolID();
  NameSpaceInfo* ns_info = dn_->GetNamespaceInfo(bpid);
  if (ns_info == nullptr || ns_info->GetNsType() != NamespaceType::ACC_HDFS) {
    return exceptions::Exception(exceptions::kIllegalArgumentException,
                                 "unmatched namespace");
  }
  etag = "";
  auto&& hdfs_info = GetHdfsInfo(bpid);
  if (nullptr == hdfs_info) {
    std::string err_msg = byte::StringPrint(
        "Cannot find HDFS Info for block: %s.", block.ToString());
    return exceptions::Exception(exceptions::kFalseException, err_msg);
  }
  auto length = block.GetNumBytes();
  io::IOChunk* chunk = new io::IOChunk(length);

  DURATION_START(qos);
  hdfs_acquire_->ThroughputAcquireWrite(length, priority);
  hdfs_acquire_->QpsAcquire(Interface::APPEND_OBJECT, priority);
  METRICS_hdfs_store_qos_write_latency
      ->GetMetric(
          {{"bpid", (block.GetBlockPoolID())},
           {"priority", IOPriorityConverter::IOPriorityToString(priority)}})
      ->Set(DURATION_END(qos));
  auto* client = AcquireHdfsClient(bpid, hdfs_info);
  if (client == nullptr) {
    chunk->AlwaysDestroy();
    return exceptions::Exception(exceptions::kFalseException,
                                 hdfsGetLastError());
  }
  byte::ScopeGuard releaser([&]() {
    chunk->AlwaysDestroy();
    ReleaseHdfsClient(bpid, client);
  });

  exceptions::Exception e;
  e = ReadBlockFromLocal(const_cast<ExtendedBlock*>(&block), 0, length, chunk,
                         priority);
  if (!e.OK()) {
    LOG(ERROR) << "Read block from local store failed, cause: " << e.ToString();
    return e;
  }

  if ((uint32_t)(chunk->Length()) != length) {
    e = exceptions::Exception(
        exceptions::kIOException,
        "Chunk data length not consistent with requested length.");
    LOG(ERROR) << "Unpadding data offset for block " << block.ToString()
               << ", length: " << chunk->Length() << ", numbytes: " << length;
    return e;
  }

  {
    int flags = O_WRONLY | O_APPEND;
    int64_t cur_length = 0;
    auto* file_info = client->GetPathInfo(block.GetObjectKey());
    if (file_info == nullptr) {
      flags |= O_CREAT;
    } else {
      cur_length = file_info->mSize;
      client->FreeFileInfo(file_info);
    }

    if (static_cast<uint64_t>(cur_length) ==
        block.GetOffset() + chunk->Length()) {
      LOG(INFO)
          << "Skip appending due to block offset + chunk length = file length";
      etag = kHdfsEtag;
      return e;
    }

    if (static_cast<uint64_t>(cur_length) != block.GetOffset()) {
      LOG_EXCEPTION(ERROR, append_corrupt)
          << "file: " << block.GetObjectKey() << ", length:" << cur_length
          << " block offset: " << block.GetOffset();
      return exceptions::Exception(exceptions::kIOException,
                                   "fail to append block: " + block.ToString());
    }

    auto&& file_ptr = client->Open(block.GetObjectKey(),
                                   std::min<int>(length, INT32_MAX), flags);
    if (nullptr == file_ptr) {
      if (errno == ESTALE) {
        // recover lease for next run.
        auto ret_code = client->RecoverLease(block.GetObjectKey());
        if (ret_code == -1) {
          LOG(ERROR) << "Recover Lease with path: " << block.GetObjectKey()
                     << " failed, " << hdfsGetLastError();
        }
      }
      return exceptions::Exception(exceptions::kFileNotFoundException,
                                   "Open file: " + block.GetObjectKey() +
                                       " failed, " + hdfsGetLastError());
    }

    uint64_t total_write = 0;
    tSize write = 0;

    RECORD_METRICS_BEFORE(write, block.GetBlockPoolID());
    do {
      write = client->Write(file_ptr->file, chunk->Data() + total_write,
                            length - total_write);
      total_write += write;
    } while (write > 0 && total_write < length);
    RECORD_METRICS_WITH_THROUGHPUT_AFTER(write, block.GetBlockPoolID(), write,
                                         length);

    if (write == -1) {
      return exceptions::Exception(exceptions::kIOException,
                                   "Write file: " + block.GetObjectKey() +
                                       " failed, " + hdfsGetLastError());
    }

    BYTE_ASSERT(total_write == length);

    if (client->Flush(file_ptr->file) == -1) {
      return exceptions::Exception(exceptions::kIOException,
                                   "Flush file: " + block.GetObjectKey() +
                                       " failed, " + hdfsGetLastError());
    }
  }

  etag = kHdfsEtag;

  return e;
}

exceptions::Exception HdfsStore::AccMultipartUploadBlock(
    const std::vector<ExtendedBlock>& blocks, const std::string& object_key,
    const std::string& upload_id, const std::vector<uint32_t> part_nums,
    bytestore::IOPriority priority, CompletedPart* completed) {
  DURATION_START(acc_mpu);
  DURATION_START(init_mpu);
  if (upload_id.empty()) {
    std::string err_msg =
        byte::StringPrint("Cannot upload with empty upload id");
    return exceptions::Exception(exceptions::kIllegalArgumentException,
                                 err_msg);
  }

  if (object_key.empty()) {
    std::string err_msg =
        byte::StringPrint("Cannot upload with empty object_key");
    return exceptions::Exception(exceptions::kIllegalArgumentException,
                                 err_msg);
  }

  if (part_nums.size() == 0) {
    std::string err_msg =
        byte::StringPrint("Cannot upload with empty part_nums");
    return exceptions::Exception(exceptions::kIllegalArgumentException,
                                 err_msg);
  }

  uint32_t part_num = part_nums[0];
  if (part_nums.size() > 1) {
    LOG(WARNING) << "part_nums size is " << part_nums.size()
                 << ", use first part_num only";
  }

  BYTE_ASSERT(blocks.size() > 0);
  auto&& hdfs_info = GetHdfsInfo(blocks.back().GetBlockPoolID());
  if (nullptr == hdfs_info) {
    std::string err_msg = byte::StringPrint(
        "Cannot find HDFS Info for block: %s.", blocks.back().ToString());
    return exceptions::Exception(exceptions::kFalseException, err_msg);
  }
  auto&& bpid = blocks.back().GetBlockPoolID();
  std::vector<std::string> block_strs;
  for (auto& block : blocks) {
    block_strs.emplace_back(block.ToString());
  }

  uint32_t total_length = 0;
  for (auto& block : blocks) {
    total_length += block.GetNumBytes();
  }

  // LOG(INFO) << "Start multipart upload blocks [" <<
  // byte::JoinStrings(block_strs, ",")
  //           << "], object: " << object_key << ", upload id: " << upload_id
  //           << ", part num: " << part_num << ", size: " << total_length << "
  //           bytes";

  DURATION_START(qos);
  LocalThroughputAcquireWrite(total_length, priority);
  hdfs_acquire_->ThroughputAcquireWrite(total_length, priority);
  hdfs_acquire_->QpsAcquire(Interface::UPLOAD_PART, priority);
  METRICS_hdfs_store_qos_write_latency
      ->GetMetric(
          {{"bpid", (bpid)},
           {"priority", IOPriorityConverter::IOPriorityToString(priority)}})
      ->Set(DURATION_END(qos));
  auto* client = AcquireHdfsClient(bpid, hdfs_info);
  if (client == nullptr) {
    return exceptions::Exception(exceptions::kFalseException,
                                 hdfsGetLastError());
  }
  byte::ScopeGuard releaser([&]() {
    ReleaseHdfsClient(bpid, client);
  });
  auto init_time = DURATION_END(init_mpu);

  DURATION_START(check_dir);
  size_t pos = object_key.find_last_of("/");
  BYTE_ASSERT(pos != std::string::npos && pos != object_key.size() - 1);
  if (!client->Exists(object_key.substr(0, pos))) {
    return exceptions::Exception(exceptions::kFileNotFoundException,
                                 "Upload dir not exists");
  }
  auto check_dir_time = DURATION_END(check_dir);

  // path: /${prefix}/.tmp/upload/${ns_id}/${upload_id}/${part_num}
  DURATION_START(get_file_info);
  int64_t cur_length = 0;
  auto* file_info = client->GetPathInfo(object_key);
  if (nullptr != file_info) {
    cur_length = file_info->mSize;
    client->FreeFileInfo(file_info);

    if (static_cast<uint64_t>(cur_length) == total_length) {
      LOG(INFO) << "Skip upload " << object_key
                << " due to total_length = file length"
                << " init time: " << init_time
                << " check dir time: " << check_dir_time
                << " get file info time: " << DURATION_END(get_file_info);
      completed->SetPartNumber(part_num);
      completed->SetETag(kHdfsEtag);
      return exceptions::Exception();
    }
  }
  auto get_file_info_time = DURATION_END(get_file_info);

  DURATION_START(open_file);
  LOG(DEBUG) << "Upload block: " << object_key;
  auto&& file_ptr = client->Open(
      object_key, std::min<int>(total_length, INT32_MAX), O_WRONLY);
  if (nullptr == file_ptr) {
    if (errno == ESTALE) {
      // recover lease for next run.
      LOG(INFO) << "Recover Lease with path: " << object_key;
      auto ret_code = client->RecoverLease(object_key);
      if (ret_code == -1) {
        LOG(ERROR) << "Recover Lease with path: " << object_key << " failed, "
                   << hdfsGetLastError();
      }
    }
    return exceptions::Exception(
        exceptions::kFileNotFoundException,
        "Open file: " + object_key + " failed, " + hdfsGetLastError());
  }
  auto open_file_time = DURATION_END(open_file);

  DURATION_START(read_local);
  io::ChunkPtr chunk(new io::IOChunk(total_length), io::IOChunk::Destroy);
  if (nullptr == chunk->Data()) {
    return exceptions::Exception(exceptions::kOutOfMemory,
                                 "Failed to allocate IOChunk's data buffer.");
  }

  exceptions::Exception e;
  for (auto& block : blocks) {
    e = ReadBlockFromLocal(const_cast<ExtendedBlock*>(&block), 0,
                           block.GetNumBytes(), chunk.get(), priority);
    if (!e.OK()) {
      return e;
    }
  }
  BYTE_ASSERT((uint32_t)(chunk->Length()) == total_length);
  auto read_local_time = DURATION_END(read_local);

  DURATION_START(write_file);
  RECORD_METRICS_BEFORE(write, bpid);
  uint64_t total_write = 0;
  tSize write = 0;
  do {
    write = client->Write(file_ptr->file, chunk->Data() + total_write,
                          total_length - total_write);
    total_write += write;
  } while (write > 0 && total_write < total_length);
  RECORD_METRICS_WITH_THROUGHPUT_AFTER(write, bpid, write, total_length);

  if (write == -1) {
    return exceptions::Exception(
        exceptions::kIOException,
        "Write file: " + object_key + " failed, " + hdfsGetLastError());
  }
  BYTE_ASSERT(total_write == total_length);

  if (client->Flush(file_ptr->file) == -1) {
    return exceptions::Exception(
        exceptions::kIOException,
        "Flush file: " + object_key + " failed, " + hdfsGetLastError());
  }
  auto write_file_time = DURATION_END(write_file);
  completed->SetPartNumber(part_num);
  completed->SetETag(kHdfsEtag);
  auto acc_mpu_time = DURATION_END(acc_mpu);

  LOG(INFO) << "Multipart upload finished. blocks ["
            << byte::JoinStrings(block_strs, ",") << "], object: " << object_key
            << ", upload id: " << upload_id << ", part num: " << part_num
            << ", size: " << total_length << " bytes."
            << " cost in ms: init " << init_time << ", check dir "
            << check_dir_time << ", get file info " << get_file_info_time
            << ", open " << open_file_time << ", read local " << read_local_time
            << ", write " << write_file_time << ". total: " << acc_mpu_time;
  return exceptions::Exception();
}

exceptions::Exception HdfsStore::ReadBlock(const ExtendedBlock& block,
                                           uint64_t data_offset,
                                           uint32_t data_len,
                                           bytestore::IOPriority priority,
                                           io::IOChunk* chunk) {
  const std::string& bpid = block.GetBlockPoolID();
  const std::string& path = block.GetObjectKey();
  if (bpid.empty()) {
    return exceptions::Exception(
        exceptions::kIllegalArgumentException,
        byte::StringPrint("ReadBlock: Cannot read block %s with empty bpid",
                          bpid));
  }

  if (path.empty()) {
    return exceptions::Exception(
        exceptions::kIllegalArgumentException,
        byte::StringPrint("ReadBlock: Cannot read block %s with empty path",
                          path));
  }

  if (data_len == 0) {
    return exceptions::Exception(
        exceptions::kIllegalArgumentException,
        byte::StringPrint("ReadBlock: invalid data_len: %u", data_len));
  }

  if ((uint32_t)(chunk->UnusedLength()) < data_len) {
    return exceptions::Exception(
        exceptions::kIllegalArgumentException,
        byte::StringPrint("ReadBlock: chunk doesn't have enough space for "
                          "data. data_len: %u, unused: %d",
                          data_len, chunk->UnusedLength()));
  }

  auto&& hdfs_info = GetHdfsInfo(bpid);
  if (nullptr == hdfs_info) {
    std::string err_msg =
        byte::StringPrint("Cannot find HDFS Info for block: %s.", bpid.c_str());
    return exceptions::Exception(exceptions::kIllegalArgumentException,
                                 err_msg);
  }

  DURATION_START(qos);
  LocalThroughputAcquireRead(data_len, priority);
  hdfs_acquire_->QpsAcquire(Interface::GET_OBJECT, priority);
  hdfs_acquire_->ThroughputAcquireRead(data_len, priority);
  METRICS_hdfs_store_qos_read_latency
      ->GetMetric(
          {{"bpid", (bpid)},
           {"priority", IOPriorityConverter::IOPriorityToString(priority)}})
      ->Set(DURATION_END(qos));
  auto* client = AcquireHdfsClient(bpid, hdfs_info);
  if (client == nullptr) {
    return exceptions::Exception(exceptions::kFalseException,
                                 hdfsGetLastError());
  }
  byte::ScopeGuard releaser([&]() {
    ReleaseHdfsClient(bpid, client);
  });

  {
    RECORD_METRICS_BEFORE(pread, bpid);
    auto&& file_ptr = client->Open(path, std::min<int>(data_len, INT32_MAX));
    if (file_ptr == nullptr) {
      return exceptions::Exception(
          exceptions::kFileNotFoundException,
          "Open file: " + path + " failed, " + hdfsGetLastError());
    }
    uint64_t total_read = 0;
    tSize read = 0;
    do {
      read = client->Pread(file_ptr->file, data_offset + total_read,
                           chunk->UnusedData() + total_read,
                           data_len - total_read);
      total_read += read;
    } while (read > 0 && total_read < data_len);
    RECORD_METRICS_WITH_THROUGHPUT_AFTER(pread, bpid, read, data_len);

    if (read == -1) {
      return exceptions::Exception(
          exceptions::kIOException,
          "Pread file: " + path + " failed, " + hdfsGetLastError());
    }

    if (read == 0) {
      LOG(WARNING) << "unexpect EOF while read path: " << path;
    }
    uint64_t ns_id = BlockPoolManager::ParseNsIdFromBpid(bpid);
    OpKey key = OpKey::New(0, ns_id, block.GetBlockID(), "", "", "", "", path,
                           Operation::ReadRemoteBlock);
    OpStats::GetInstance().Record(
        key, data_offset, read, record_metrics_start / 1000,
        byte::GetCurrentTimeInUs() - record_metrics_start);
    chunk->IncrLength(data_len);
  }

  return exceptions::Exception();
}

exceptions::Exception HdfsStore::DeleteBlock(const std::string& bpid,
                                             const std::string& object_key,
                                             bytestore::IOPriority priority) {
  RECORD_METRICS_BEFORE(delete_object, bpid);
  RECORD_METRICS_AFTER(delete_object, bpid, -1);
  return exceptions::Exception(exceptions::kUnsupportedOperationException);
}

exceptions::Exception HdfsStore::DeleteBlocks(
    const std::string& bpid, const std::vector<std::string>& keys,
    bytestore::IOPriority priority, std::vector<std::string>* deleted_keys) {
  RECORD_METRICS_BEFORE(delete_object, bpid);
  RECORD_METRICS_AFTER(delete_object, bpid, -1);
  return exceptions::Exception(exceptions::kUnsupportedOperationException);
}

void HdfsStore::LocalThroughputAcquireRead(uint64_t num,
                                           bytestore::IOPriority priority) {
  // disk_id is useless for remote store
  dn_->AcquireLocalToken(0, LimitObject::kRemoteUpload, num, priority);
}

void HdfsStore::LocalThroughputAcquireWrite(uint64_t num,
                                            bytestore::IOPriority priority) {
  dn_->AcquireLocalToken(0, LimitObject::kRemoteUpload, num, priority);
}

}  // namespace bds::dancedn::cloudfs
