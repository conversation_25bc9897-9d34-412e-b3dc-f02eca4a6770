// copyright (c) 2021-present, bytedance inc. all rights reserved.

#include "cloudfs/store/fetch_manager.h"

#include <algorithm>
#include <cstdint>
#include <memory>

#include "base/closure.h"
#include "cloudfs/block_pool_manager.h"
#include "cloudfs/constants.h"
#include "cloudfs/exceptions.h"
#include "cloudfs/extended_block.h"
#include "cloudfs/io/io_buf.h"
#include "cloudfs/metrics.h"
#include "cloudfs/receive_deleted_block_info.h"
#include "cloudfs/replica_in_pipeline.h"
#include "cloudfs/replica_state.h"
#include "cloudfs/store/chunkserver/chunkserver_store.h"
#include "cloudfs/store/ufs/tos_store.h"
#include "cloudfs/store/unified_block_store.h"
#include "cloudfs/thread.h"
#include "concurrent/cond.h"
#include "concurrent/count_down_latch.h"
#include "concurrent/mutex.h"
#include "gflags/gflags_declare.h"
#include "string/format/print.h"
#include "system/timestamp.h"
#include "thread/base_thread_pool.h"
#include "thread/this_thread.h"
#include "util/scope_guard.h"

DECLARE_int32(bytestore_cfs_fetch_pool_size);
DECLARE_uint64(bytestore_cfs_fetch_max_wait_size);
DECLARE_uint64(bytestore_cfs_fetch_max_wait_number);
DECLARE_uint64(bytestore_cfs_fetch_cache_timeout);
DECLARE_uint32(bytestore_cfs_fetch_write_unit_size);
DECLARE_uint64(bytestore_cfs_remote_store_download_part_size);
DECLARE_uint64(bytestore_cfs_remote_store_upload_part_size);

namespace bds::dancedn::cloudfs {

FetchManager::FetchManager()
    : datanode_(nullptr),
      chunkserver_store_(nullptr),
      remote_store_(nullptr),
      contexts_(),
      queue_mutex_(),
      queue_(FetchManager::CompareContext) {}

FetchManager::FetchManager(DataNode* datanode,
                           ChunkServerStore* chunkserver_store,
                           RemoteStore* remote_store)
    : datanode_(datanode),
      chunkserver_store_(chunkserver_store),
      remote_store_(remote_store),
      contexts_(),
      queue_mutex_(),
      queue_(FetchManager::CompareContext) {}

FetchManager::~FetchManager() {
  Stop();
  Join();
}

void FetchManager::AsyncCacheBlock(ExtendedBlock* block, Source source) {
  std::shared_ptr<Context> ctx = std::make_shared<Context>(
      std::shared_ptr<ExtendedBlock>(block->Clone()), CacheType::ASYNC, source);
  if (!contexts_.Add(block->GetBlockPoolID(), block->GetBlockID(), ctx)) {
    LOG(INFO) << "Another thread is caching the same block, skip "
              << block->ToString();
    return;
  } else {
    LOG(INFO) << "Add block " << block->ToString() << " into cache queue.";
    bool ok = PushQueue(ctx);
    if (!ok) {
      LOG(INFO) << "Too many blocks wait to be cache, skip "
                << block->ToString();
      contexts_.Remove(block->GetBlockPoolID(), block->GetBlockID());
    } else {
      METRICS_fetch_tos_mgr_async_cache_num->Increment();
    }
  }
}

std::shared_ptr<FetchManager::Context> FetchManager::StartSyncCacheBlock(
    ExtendedBlock* block) {
  std::shared_ptr<Context> ctx =
      std::make_shared<Context>(std::shared_ptr<ExtendedBlock>(block->Clone()),
                                CacheType::SYNC, Source::CLIENT);
  if (!contexts_.Add(block->GetBlockPoolID(), block->GetBlockID(), ctx)) {
    LOG(INFO) << "Another thread is caching the same block, skip "
              << block->ToString();
    return nullptr;
  }
  exceptions::Exception e = chunkserver_store_->CreateTemporary(
      StorageType::SSD, ctx->block_.get(), &(ctx->replica_), fetch_priority_);
  if (e.OK()) {
    METRICS_fetch_tos_mgr_sync_cache_num->Increment();
    ctx->replica_->SetEvictable(true);
  } else {
    LOG(ERROR) << "Create replica for block " << block->ToString()
               << " failed, cause: " << e.ToString();
    contexts_.Remove(block->GetBlockPoolID(), block->GetBlockID());
    return nullptr;
  }
  return ctx;
}

exceptions::Exception FetchManager::WriteData(
    const std::shared_ptr<Context>& context) {
  if (contexts_.Exist(context->block_->GetBlockPoolID(),
                      context->block_->GetBlockID())) {
    LOG(DEBUG) << "Write data into disk for block "
               << context->block_->ToString()
               << ", offset: " << context->current_offset_ << ", length "
               << context->chunk_->UsedLength();
    uint32_t length = context->chunk_->UsedLength();
    auto&& e = WriteLocalData(context->block_.get(), context->chunk_,
                              context->current_offset_, length);
    if (!e.OK()) {
      if (context->source_ == LOAD_COMMAND) {
        datanode_->NotifyNamenodeFailed(context->block_.get(),
                                        BlockStatus::LOAD_FAILED, e.ToString());
      }
      LOG(ERROR) << "Write data into disk failed, cause: " << e.ToString();
      return e;
    }
    METRICS_fetch_tos_mgr_sync_cache_write_size->Add(length);
    context->current_offset_ += length;
    RETURN_NO_EXCEPTION();
  } else {
    LOG(ERROR) << "Cannot find block " << context->block_->ToString()
               << " in context.";
    if (context->source_ == LOAD_COMMAND) {
      datanode_->NotifyNamenodeFailed(context->block_.get(),
                                      BlockStatus::LOAD_FAILED,
                                      "unexpect exception");
    }
    return exceptions::Exception(
        exceptions::kIOException,
        "Cannot find block " + context->block_->ToString() + " in context.");
  }
}

void FetchManager::FinishSyncCacheBlock(
    const std::shared_ptr<Context>& context) {
  if (contexts_.Exist(context->block_->GetBlockPoolID(),
                      context->block_->GetBlockID())) {
    if (context->current_offset_ == context->block_->GetNumBytes()) {
      context->replica_->SetNumBytes(context->block_->GetNumBytes());
      auto e = chunkserver_store_->UpdateReplicaXAttr(context->replica_,
                                                      fetch_priority_);
      if (!e.OK()) {
        chunkserver_store_->Invalidate(context->block_->GetBlockPoolID(),
                                       {context->block_->GetBlock()},
                                       fetch_priority_);
        LOG(WARNING) << byte::StringPrint("Update XAttr for replica %s failed",
                                          context->block_->ToString())
                     << e.ToString();
        return;
      }
      e = chunkserver_store_->FinalizeBlock(context->block_.get(),
                                            fetch_priority_);
      if (!e.OK()) {
        LOG(ERROR) << byte::StringPrint("Finalize block %s failed, cause: %s",
                                        context->block_->ToString(),
                                        e.ToString());
        chunkserver_store_->Invalidate(context->block_->GetBlockPoolID(),
                                       {context->block_->GetBlock()},
                                       fetch_priority_);
      } else {
        datanode_->GetStorage()->ReadedBlock(context->block_->GetKey());
        datanode_->NotifyNamenodeReceivedBlock(
            context->block_.get(), "", context->replica_->GetStorageUuid());
      }
      contexts_.Remove(context->block_->GetBlockPoolID(),
                       context->block_->GetBlockID());
    } else {
      LOG(INFO) << "Add block " << context->block_->ToString()
                << " into cache queue.";
      bool ok = PushQueue(context);
      if (ok) {
        METRICS_fetch_tos_mgr_fill_up_sync_cache_num->Increment();
      } else {
        LOG(INFO) << "Too many blocks wait to be cache, skip "
                  << context->block_->ToString();
        chunkserver_store_->Invalidate(context->block_->GetBlockPoolID(),
                                       {context->block_->GetBlock()},
                                       fetch_priority_);
        contexts_.Remove(context->block_->GetBlockPoolID(),
                         context->block_->GetBlockID());
      }
    }
  } else {
    LOG(ERROR) << "Cannot find block " << context->block_->ToString()
               << " in context.";
    return;
  }
}

bool FetchManager::CompareContext(std::shared_ptr<Context> c1,
                                  std::shared_ptr<Context> c2) {
  if (c1->current_offset_ != c2->current_offset_) {
    return c1->current_offset_ < c2->current_offset_;
  } else if (c1->current_offset_ == 0) {
    return true;
  } else {
    return c1->block_->GetNumBytes() > c2->block_->GetNumBytes();
  }
}

void FetchManager::Run() {
  while (!IsStopped()) {
    WaitUtil(
        [this]() {
          return !IsQueueEmpty() || IsStopped();
        },
        0);
    if (IsStopped()) {
      break;
    }

    std::shared_ptr<Context> context = PopQueue();
    BYTE_ASSERT(context != nullptr);

    uint64_t ns_id =
        BlockPoolManager::ParseNsIdFromBpid(context->block_->GetBlockPoolID());
    if (thread_pools_.find(ns_id) == thread_pools_.end()) {
      thread_pools_[ns_id] = std::make_unique<byte::DynamicThreadPool>(
          FLAGS_bytestore_cfs_fetch_pool_size,
          "fetch_" + std::to_string(ns_id & 0xff));
      LOG(INFO) << "create thread pool for ns_id: " << ns_id;
    }
    thread_pools_[ns_id]->AddTask(
        NewClosure(this, &FetchManager::FillUpBlock, context));
  }
}

void FetchManager::FillUpBlock(std::shared_ptr<Context> context) {
  byte::ScopeGuard clean_work([this, &context]() {
    contexts_.Remove(context->block_->GetBlockPoolID(),
                     context->block_->GetBlockID());
    fetching_bytes_ -= context->fetch_len_;
    fetching_num_--;
    METRICS_fetch_tos_mgr_queue_size->Minus(context->fetch_len_);
    METRICS_fetch_tos_mgr_queue_num->Decrement();
    METRICS_fetch_tos_mgr_thread_pool_active_num->Decrement();
    Signal();
  });
  METRICS_fetch_tos_mgr_thread_pool_active_num->Increment();
  const std::shared_ptr<ExtendedBlock>& block = context->block_;
  LOG(INFO) << "Start to fill up block " << block->ToString()
            << ", offset: " << context->current_offset_;
  if (byte::GetCurrentTimeInMs() - context->queue_time_ >=
      FLAGS_bytestore_cfs_fetch_cache_timeout) {
    LOG(ERROR) << "Cache block timeout, try to invalidate "
               << block->ToString();
    if (context->source_ == LOAD_COMMAND) {
      datanode_->NotifyNamenodeFailed(context->block_.get(),
                                      BlockStatus::LOAD_FAILED, "timeout");
    }
    if (context->type_ == SYNC) {
      METRICS_fetch_tos_mgr_fill_up_sync_cache_fail_num->Increment();
    } else {
      METRICS_fetch_tos_mgr_fill_up_async_cache_fail_num->Increment();
    }
    if (context->type_ == SYNC) {
      chunkserver_store_->Invalidate(block->GetBlockPoolID(),
                                     {block->GetBlock()}, fetch_priority_);
    }
    return;
  }
  if (context->type_ == ASYNC) {
    exceptions::Exception e = chunkserver_store_->CreateTemporary(
        StorageType::SSD, block.get(), &(context->replica_), fetch_priority_);
    if (!e.OK()) {
      if (context->source_ == LOAD_COMMAND) {
        datanode_->NotifyNamenodeFailed(context->block_.get(),
                                        BlockStatus::LOAD_FAILED, e.ToString());
      }
      LOG(ERROR) << "Create temporary replica for block " << block->ToString()
                 << " failed, cause: " << e.ToString();
      METRICS_fetch_tos_mgr_fill_up_async_cache_fail_num->Increment();
      return;
    }
    context->replica_->SetEvictable(true);
  }
  uint32_t block_size = block->GetNumBytes();
  io::IOChunk* chunk = nullptr;
  if (block_size - context->current_offset_ >
      FLAGS_bytestore_cfs_remote_store_upload_part_size) {
    chunk =
        new io::IOChunk(FLAGS_bytestore_cfs_remote_store_download_part_size);
  } else {
    chunk = new io::IOChunk(block_size - context->current_offset_);
  }
  byte::ScopeGuard chunk_deleter([chunk]() {
    if (chunk != nullptr) {
      chunk->AlwaysDestroy();
    }
  });
  bool failed = false;
  while (context->current_offset_ != block_size) {
    chunk->Reset();
    uint32_t length = (block_size - context->current_offset_ <
                       FLAGS_bytestore_cfs_remote_store_download_part_size)
                          ? block_size - context->current_offset_
                          : FLAGS_bytestore_cfs_remote_store_download_part_size;
    auto&& e = remote_store_->ReadBlock(
        *block, context->current_offset_ + block->GetOffset(), length,
        fetch_priority_, chunk);
    if (!e.OK()) {
      LOG(ERROR) << byte::StringPrint("Read block %s failed, cause: %s",
                                      block->ToString(), e.ToString());
      failed = true;
      break;
    }
    e = WriteLocalData(block.get(), chunk, context->current_offset_, length);
    if (!e.OK()) {
      LOG(ERROR) << byte::StringPrint(
          "Write block %s into disk failed, cause: %s.", block->ToString(),
          e.ToString());
      failed = true;
      break;
    }
    context->current_offset_ += length;
    if (context->type_ == SYNC) {
      METRICS_fetch_tos_mgr_sync_cache_fill_up_size->Add(length);
    } else {
      METRICS_fetch_tos_mgr_async_cache_fill_up_size->Add(length);
    }
  }
  if (failed) {
    if (context->source_ == LOAD_COMMAND) {
      datanode_->NotifyNamenodeFailed(context->block_.get(),
                                      BlockStatus::LOAD_FAILED, "cache failed");
    }
    LOG(ERROR) << "Cache block failed, invalidate " << block->ToString();
    if (context->type_ == SYNC) {
      METRICS_fetch_tos_mgr_fill_up_sync_cache_fail_num->Increment();
    } else {
      METRICS_fetch_tos_mgr_fill_up_async_cache_fail_num->Increment();
    }
    chunkserver_store_->Invalidate(block->GetBlockPoolID(), {block->GetBlock()},
                                   fetch_priority_);
  } else {
    context->replica_->SetNumBytes(context->block_->GetNumBytes());
    auto e = chunkserver_store_->UpdateReplicaXAttr(context->replica_,
                                                    fetch_priority_);
    if (!e.OK()) {
      if (context->source_ == LOAD_COMMAND) {
        datanode_->NotifyNamenodeFailed(context->block_.get(),
                                        BlockStatus::LOAD_FAILED, e.ToString());
      }
      chunkserver_store_->Invalidate(context->block_->GetBlockPoolID(),
                                     {context->block_->GetBlock()},
                                     fetch_priority_);
      LOG(WARNING) << byte::StringPrint("Update XAttr for replica %s failed",
                                        context->block_->ToString())
                   << e.ToString();
      return;
    }
    e = chunkserver_store_->FinalizeBlock(context->block_.get(),
                                          fetch_priority_);

    if (!e.OK()) {
      if (context->source_ == LOAD_COMMAND) {
        datanode_->NotifyNamenodeFailed(context->block_.get(),
                                        BlockStatus::LOAD_FAILED, e.ToString());
      }
      LOG(ERROR) << byte::StringPrint("Finalize block %s failed, cause: %s",
                                      context->block_->ToString(),
                                      e.ToString());
      chunkserver_store_->Invalidate(context->block_->GetBlockPoolID(),
                                     {context->block_->GetBlock()},
                                     fetch_priority_);
    } else {
      datanode_->NotifyNamenodeReceivedBlock(
          context->block_.get(), "", context->replica_->GetStorageUuid());
      LOG(INFO) << "Fill up block " << block->ToString() << " succeed";
    }
  }
}

bool FetchManager::PushQueue(const std::shared_ptr<Context>& context) {
  uint64_t current_time = byte::GetCurrentTimeInMs();
  if (context->block_->GetNumBytes() < context->current_offset_) {
    LOG(ERROR) << "Illegal argument for block " << context->block_->ToString()
               << ", numbytes: " << context->block_->GetNumBytes()
               << ", current_offset: " << context->current_offset_;
    return false;
  }
  uint64_t size = context->block_->GetNumBytes() - context->current_offset_;
  {
    byte::MutexLocker guard(&queue_mutex_);
    if (fetching_bytes_.Value() + size >
            FLAGS_bytestore_cfs_fetch_max_wait_size ||
        fetching_num_.Value() + 1 > FLAGS_bytestore_cfs_fetch_max_wait_number) {
      return false;
    } else {
      context->queue_time_ = current_time;
      context->fetch_len_ = size;
      queue_.emplace(context);
    }
  }

  fetching_bytes_ += size;
  fetching_num_++;
  METRICS_fetch_tos_mgr_queue_size->Add(size);
  METRICS_fetch_tos_mgr_queue_num->Increment();
  Signal();
  return true;
}

std::shared_ptr<FetchManager::Context> FetchManager::PopQueue() {
  byte::MutexLocker guard(&queue_mutex_);
  if (!queue_.empty()) {
    std::shared_ptr<Context> ret = queue_.top();
    queue_.pop();
    return ret;
  }
  return nullptr;
}

bool FetchManager::IsQueueEmpty() {
  byte::MutexLocker guard(&queue_mutex_);
  return queue_.empty();
}

exceptions::Exception FetchManager::WriteLocalData(ExtendedBlock* block,
                                                   io::IOChunk* chunk,
                                                   uint64_t offset,
                                                   uint32_t length) {
  exceptions::Exception e;
  uint32_t written = 0;
  const uint32_t max_unit_size = FLAGS_bytestore_cfs_fetch_write_unit_size;
  while (written < length) {
    uint32_t unit = std::min(max_unit_size, length - written);
    e = chunkserver_store_->WriteBlock(block, chunk, offset + written, unit,
                                       false, fetch_priority_);
    if (!e.OK()) {
      break;
    }
    written += unit;
  }
  return e;
}

}  // namespace bds::dancedn::cloudfs
