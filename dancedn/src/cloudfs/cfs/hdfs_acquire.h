// copyright (c) 2024-present, bytedance inc. all rights reserved.

#pragma once

#include <memory>
#include <vector>

#include "cloudfs/cfs/qos_acquire.h"
#include "cloudfs/store/ufs/tos_acquire.h"
#include "cloudfs_proto/DatanodeProtocol.pb.h"

namespace bds::dancedn::cloudfs {

class DataNode;

class HdfsAcquire {
 public:
  explicit HdfsAcquire(DataNode* dn);
  ~HdfsAcquire() {}

  void ThroughputAcquireRead(int64_t num, bytestore::IOPriority priority);
  void ThroughputAcquireWrite(int64_t num, bytestore::IOPriority priority);
  void QpsAcquire(Interface method_id, bytestore::IOPriority priority,
                  int64_t qps = 1);

 private:
  inline ProtoPriority ConvertToProtoPriority(bytestore::IOPriority pri);

 private:
  constexpr static uint32_t kAcquirePriorityNum =
      ::cloudfs::datanode::AcquireRequest_Priority_Priority_MAX + 1;
  std::unique_ptr<HdfsThroughputAcquire> read_acquire_[kAcquirePriorityNum];
  std::unique_ptr<HdfsThroughputAcquire> write_acquire_[kAcquirePriorityNum];
  std::vector<std::unique_ptr<HdfsQPSAcquire>>
      qps_acquires_[kAcquirePriorityNum];
};

}  // namespace bds::dancedn::cloudfs
