// copyright (c) 2021-present, bytedance inc. all rights reserved.

#include "cloudfs/cfs/namespace_info_refresher.h"

#include "byte/system/timestamp.h"
#include "cloudfs/block_pool_manager.h"
#include "cloudfs/cfs/system_utils.h"
#include "cloudfs/datanode.h"
#include "cloudfs/metrics.h"
#include "cloudfs/namenode_client.h"
#include "gflags/gflags.h"

DECLARE_string(bytestore_cfs_load_namespace_config);
DECLARE_uint32(bytestore_cfs_refresh_namespace_config_interval);
DECLARE_string(bytestore_hdfs_config_file);
DECLARE_string(bytestore_hdfs_nn_config_file);
DECLARE_string(bytestore_hdfs_nic_config_file);

namespace bds::dancedn::cloudfs {

NamespaceInfoRefresher::NamespaceInfoRefresher(DataNode* dn,
                                               BlockPoolManager* bp_mgr)
    : dn_(dn),
      bp_mgr_(bp_mgr),
      last_refresh_time_(0),
      client_(nullptr),
      current_version_(),
      last_modified_time_(0),
      last_changed_time_(0) {}

NamespaceInfoRefresher::~NamespaceInfoRefresher() {
  Stop();
  Join();
}

exceptions::Exception NamespaceInfoRefresher::StartRefresh() {
  if (FLAGS_bytestore_cfs_load_namespace_config == "dnproxy") {
    if (dn_->GetDataNodeConfig()->GetDNProxy().empty()) {
      return exceptions::Exception(exceptions::kIllegalArgumentException,
                                   "dnproxy address is empty");
    }
    client_.reset(dn_->ConnectToServer(dn_->GetDataNodeConfig()->GetDNProxy()));
  }
  Start();
  return exceptions::Exception();
}

std::string NamespaceInfoRefresher::NamespacesToString(
    const DataNodeConfig::NamespaceMap& namespaces) {
  std::ostringstream os;
  auto itr = namespaces.begin();
  if (itr != namespaces.end()) {
    os << itr->first << ": {";
    auto it = itr->second.begin();
    if (it != itr->second.end()) {
      os << *it;
      it++;
    }
    for (; it != itr->second.end(); it++) {
      os << ", " << *it;
    }
    os << "}";
    itr++;
  }
  for (; itr != namespaces.end(); itr++) {
    os << ", " << itr->first << ": {";
    auto it = itr->second.begin();
    if (it != itr->second.end()) {
      os << *it;
      it++;
    }
    for (; it != itr->second.end(); it++) {
      os << ", " << *it;
    }
    os << "}";
  }
  return os.str();
}

void NamespaceInfoRefresher::Run() {
  while (!IsStopped()) {
    if (byte::GetCurrentTimeInMs() - last_refresh_time_ >
        FLAGS_bytestore_cfs_refresh_namespace_config_interval) {
      exceptions::Exception e = Refresh();
      if (!e.OK() && e.GetE() != exceptions::kStaleVersionException) {
        LOG(WARNING) << "Refresh namespace config failed, reason: "
                     << e.ToString();
      }
    }
    WaitFor(1000);  // if last refresh failed, we need to retry after 1sec
  }
}

exceptions::Exception NamespaceInfoRefresher::Refresh() {
  LOG(DEBUG) << "Start to refresh namespace info.";
  DataNodeConfig::NamespaceMap namespaces;
  exceptions::Exception e;
  if (FLAGS_bytestore_cfs_load_namespace_config == "file") {
    e = LoadConfigFromFile(&namespaces);
  } else if (FLAGS_bytestore_cfs_load_namespace_config == "dnproxy" &&
             !dn_->GetDataNodeConfig()->GetDNProxy().empty()) {
    e = LoadConfigFromDnproxy(&namespaces);
  } else {
    return exceptions::Exception(
        exceptions::kIllegalArgumentException,
        "Namespace config can only be loaded from file or dnproxy");
  }
  if (!e.OK()) {
    if (e.GetE() == exceptions::kStaleVersionException) {
      last_refresh_time_ = byte::GetCurrentTimeInMs();
      LOG(DEBUG) << "Do not need to update namespace info.";
    }
    return e;
  }

  LOG(INFO) << "Refresh namespaces for conf:" << NamespacesToString(namespaces);

  e = bp_mgr_->DoRefreshNamenodes(namespaces);
  if (!e.OK()) {
    return e;
  }
  LOG(INFO) << "Refresh namespaces info successfully.";
  last_refresh_time_ = byte::GetCurrentTimeInMs();
  return e;
}

exceptions::Exception NamespaceInfoRefresher::LoadConfigFromFile(
    DataNodeConfig::NamespaceMap* namespaces) {
  uint64_t access_time;
  uint64_t modify_time;
  uint64_t change_time;
  if (GetStatTime(FLAGS_bytestore_hdfs_nn_config_file.c_str(), &access_time,
                  &modify_time, &change_time) != 0) {
    return exceptions::Exception(exceptions::kIOException,
                                 "Get modify_time of " +
                                     FLAGS_bytestore_hdfs_nn_config_file +
                                     " failed.");
  }
  if (last_modified_time_ == modify_time && last_changed_time_ == change_time) {
    return exceptions::Exception(exceptions::kStaleVersionException);
  }
  LOG(INFO) << "namenode config file changed: "
            << FLAGS_bytestore_hdfs_nn_config_file;
  DataNodeConfig dn_conf;
  if (!dn_conf.Parse(FLAGS_bytestore_hdfs_config_file,
                     FLAGS_bytestore_hdfs_nn_config_file,
                     FLAGS_bytestore_hdfs_nic_config_file)) {
    return exceptions::Exception(exceptions::kIOException,
                                 "parse config file failed");
  }
  *namespaces = dn_conf.GetNNServiceRpcAddressesForCluster();
  last_modified_time_ = modify_time;
  last_changed_time_ = change_time;
  METRICS_namespace_info_refresher_version->SetValue(last_modified_time_);
  return exceptions::Exception();
}

exceptions::Exception NamespaceInfoRefresher::LoadConfigFromDnproxy(
    DataNodeConfig::NamespaceMap* namespaces) {
  BYTE_ASSERT(client_ != nullptr);
  uint64_t version;
  exceptions::Exception e = client_->GetNamespaces(&version, namespaces);
  if (!e.OK()) {
    return e;
  }
  if (version > current_version_) {
    current_version_ = version;
    METRICS_namespace_info_refresher_version->SetValue(current_version_);
    return exceptions::Exception();
  } else {
    std::string msg =
        byte::StringPrint("unexpected version: %d, current version: %d",
                          version, current_version_);
    return exceptions::Exception(exceptions::kStaleVersionException, msg);
  }
}

}  // namespace bds::dancedn::cloudfs
