// copyright (c) 2021-present, bytedance inc. all rights reserved.

#pragma once

#include <netinet/in.h>

#include <string>
#include <vector>

namespace bds::dancedn::cloudfs {

int GetLocalIps(std::vector<std::string>* ips);
bool IsPrivateIpv6(const struct sockaddr_in6* addr6);

/**
 * Convert hostname or ip to in_addr.
 * Assume that:
 *   1. The desired address family is AF_INET
 *   2. The preferred socket type can be any
 *   3. Thr protocol can be any
 *
 * If more than one addrinfo structures are returned when call getaddrinfo,
 * it will take the first one.
 */
int ParseHostname(const char* hostname, in_addr* addr);

int GetStatTime(const char* filename, uint64_t* access_time,
                uint64_t* modify_time, uint64_t* change_time);

}  // namespace bds::dancedn::cloudfs
