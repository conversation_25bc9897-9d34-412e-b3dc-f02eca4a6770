// copyright (c) 2023-present, bytedance inc. all rights reserved.

#include "cloudfs/cfs/qos_acquire.h"

#include <atomic>
#include <thread>  // NOLINT
#include <vector>

#include "byte/system/timestamp.h"
#include "byte/thread/this_thread.h"
#include "cloudfs/mocks/mock_namenode_client.h"
#include "cloudfs/namenode_client.h"
#include "gflags/gflags.h"
#include "gtest/gtest.h"

DECLARE_int32(bytestore_cfs_qos_acquire_max_magnification_times);
DECLARE_int64(bytestore_cfs_qos_slow_request_ms);
DECLARE_string(bytestore_cfs_qos_force_fallback_policy);
DECLARE_int64(bytestore_cfs_qos_fallback_sample_interval_s);

namespace bds::dancedn::cloudfs {

class QosAcquireTest : public ::testing::Test {
 public:
  void SetUp() override {
    // namenode_client is managed inside of BaseAcquire, no need to manually
    // delete
    namenode_client_ = new MockNamenodeClient();
  }
  void TearDown() override {}
  static void SetUpTestCase() {}
  static void TearDownTestCase() {
    byte::GetLoggingSystem()->Shutdown();
  }

 public:
  MockNamenodeClient* namenode_client_;
};

TEST_F(QosAcquireTest, MultiThreadAcquireTest) {
  EXPECT_CALL(*namenode_client_,
              Acquire(::testing::_, ::testing::_, ::testing::_))
      .WillRepeatedly(
          ::testing::Invoke([&](BaseAcquire* ac, int64_t num, int64_t* res) {
            *res = num;
            return exceptions::Exception();
          }));
  TosThroughputAcquire qos(namenode_client_, AcquireType::GET_RATE,
                           BaseAcquire::HighPriority());
  int64_t base_num = 1024 * 1024;
  qos.SetAcquireNum(8 * base_num);
  std::vector<std::thread> threads;
  std::atomic<int64_t> sum(0);
  for (int i = 0; i < 100; i++) {
    threads.push_back(std::thread([&]() {
      int64_t acquired = qos.Acquire(base_num);
      sum.fetch_add(acquired);
      // printf("Acquired %ld token.\n", acquired);
    }));
  }
  for (auto& t : threads) {
    t.join();
  }
  ASSERT_LE(sum, 100 * base_num);  // some threads need request
}

TEST_F(QosAcquireTest, DynamicAcquire) {
  FLAGS_bytestore_cfs_qos_acquire_max_magnification_times = 3;
  EXPECT_CALL(*namenode_client_,
              Acquire(::testing::_, ::testing::_, ::testing::_))
      .WillRepeatedly(
          ::testing::Invoke([&](BaseAcquire* ac, int64_t num, int64_t* res) {
            *res = num;
            return exceptions::Exception();
          }));
  TosThroughputAcquire qos(namenode_client_, AcquireType::GET_RATE,
                           BaseAcquire::HighPriority());
  int64_t base_num = 1024 * 1024;
  qos.SetAcquireNum(8 * base_num);

  ASSERT_EQ(qos.Acquire(5 * base_num), 5 * base_num);
  ASSERT_EQ(qos.Acquire(5 * base_num), 3 * base_num);  // consume remain token
  ASSERT_EQ(qos.Acquire(50 * base_num), 16 * base_num);
  ASSERT_EQ(qos.Acquire(50 * base_num), 24 * base_num);

  ASSERT_EQ(qos.Acquire(1 * base_num), 1 * base_num);
  byte::ThisThread::SleepInMs(1200);
  ASSERT_EQ(qos.Acquire(50 * base_num), 12 * base_num);
}

TEST_F(QosAcquireTest, PartialAcquire) {
  EXPECT_CALL(*namenode_client_,
              Acquire(::testing::_, ::testing::_, ::testing::_))
      .WillRepeatedly(
          ::testing::Invoke([&](BaseAcquire* ac, int64_t num, int64_t* res) {
            *res = num / 2;
            return exceptions::Exception();
          }));
  TosThroughputAcquire qos(namenode_client_, AcquireType::GET_RATE,
                           BaseAcquire::HighPriority());
  int64_t base_num = 1024 * 1024;
  qos.SetAcquireNum(8 * base_num);

  ASSERT_EQ(qos.Acquire(6 * base_num),
            4 * base_num);  // acquire 8, return 4, consume 4
  ASSERT_EQ(qos.Acquire(2 * base_num),
            2 * base_num);  // acquire 16, return 8, consume 2
  ASSERT_EQ(qos.Acquire(50 * base_num), 6 * base_num);  // comsume 6
  ASSERT_EQ(qos.Acquire(50 * base_num),
            12 * base_num);  // acquire 24, return 12, consume 12
}

TEST_F(QosAcquireTest, TaskAcquire) {
  EXPECT_CALL(*namenode_client_,
              Acquire(::testing::_, ::testing::_, ::testing::_))
      .WillRepeatedly(
          ::testing::Invoke([&](BaseAcquire* ac, int64_t num, int64_t* res) {
            *res = num / 2;
            return exceptions::Exception();
          }));
  TaskThroughputAcquire qos(namenode_client_, "task", AcquireType::GET_RATE,
                            BaseAcquire::HighPriority());
  int64_t base_num = 1024 * 1024;
  qos.SetAcquireNum(8 * base_num);

  ASSERT_EQ(qos.Acquire(6 * base_num),
            4 * base_num);  // acquire 8, return 4, consume 4
  ASSERT_EQ(qos.Acquire(2 * base_num),
            2 * base_num);  // acquire 16, return 8, consume 2
  ASSERT_EQ(qos.Acquire(50 * base_num), 6 * base_num);  // comsume 6
  ASSERT_EQ(qos.Acquire(50 * base_num),
            12 * base_num);  // acquire 24, return 12, consume 12
}

TEST_F(QosAcquireTest, FallbackAcquireTest) {
  FLAGS_bytestore_cfs_qos_force_fallback_policy = "local";
  FLAGS_bytestore_cfs_qos_fallback_sample_interval_s = 1;
  namespace be = byte::embedded_metrics;
  be::MetricsOptions metric_opt;
  metric_opt.mode = be::MetricsMode::kPull;
  std::shared_ptr<spdlog::logger> metric_logger = nullptr;
  FLAGS_bytestore_cfs_qos_slow_request_ms = 100;
  const int64_t TEST_TIME_MS = 5000;
  int64_t start_ms = byte::GetCurrentTimeInMs();
  MockNamenodeClient* namenode_client[2];
  for (uint32_t i = 0; i < 2; i++) {
    namenode_client[i] = new MockNamenodeClient();
    EXPECT_CALL(*namenode_client[i],
                Acquire(::testing::_, ::testing::_, ::testing::_))
        .WillRepeatedly(
            ::testing::Invoke([&](BaseAcquire* ac, int64_t num, int64_t* res) {
              // Return immediately before 1s
              if (byte::GetCurrentTimeInMs() - start_ms < 1000) {
                byte::ThisThread::SleepInMs(1);
                *res = num;
              } else {
                byte::ThisThread::SleepInMs(500);
                *res = num;
              }
              return exceptions::Exception();
            }));
  }
  TosThroughputAcquire high_priority_acquire_client(
      namenode_client[0], AcquireType::GET_RATE, BaseAcquire::HighPriority());
  TosThroughputAcquire low_priority_acquire_client(
      namenode_client[1], AcquireType::GET_RATE, BaseAcquire::LowPriority());
  double start_h_fallback_num =
      high_priority_acquire_client.GetFallbackNumMetric()->GetValue();
  double start_l_fallback_num =
      low_priority_acquire_client.GetFallbackNumMetric()->GetValue();
  std::vector<std::thread> threads;
  for (int i = 0; i < 2; i++) {
    threads.push_back(std::thread([&]() {
      while (byte::GetCurrentTimeInMs() - start_ms < TEST_TIME_MS) {
        high_priority_acquire_client.Acquire(1024L * 1024L);
      }
    }));
    threads.push_back(std::thread([&]() {
      while (byte::GetCurrentTimeInMs() - start_ms < TEST_TIME_MS) {
        low_priority_acquire_client.Acquire(1024L * 1024L);
      }
    }));
  }
  for (auto& t : threads) {
    t.join();
  }
  double cur_h_fallback_num =
      high_priority_acquire_client.GetFallbackNumMetric()->GetValue();
  double cur_l_fallback_num =
      low_priority_acquire_client.GetFallbackNumMetric()->GetValue();
  ASSERT_GT(cur_h_fallback_num - start_h_fallback_num, 2000.0);
  ASSERT_LT(cur_l_fallback_num - start_l_fallback_num, 1.0);
}

}  // namespace bds::dancedn::cloudfs
