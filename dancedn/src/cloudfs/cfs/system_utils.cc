// copyright (c) 2021-present, bytedance inc. all rights reserved.

#include "cloudfs/cfs/system_utils.h"

#include <arpa/inet.h>
#include <ifaddrs.h>
#include <netdb.h>
#include <netinet/in.h>
#include <sys/stat.h>
#include <sys/types.h>

#include "byte/include/byte_log.h"
#include "gflags/gflags.h"
#include "string/algorithm.h"
#include "system/ip6_address.h"

DECLARE_string(bytestore_cfs_net_interface_prefix);

namespace bds::dancedn::cloudfs {

// https://www.iana.org/assignments/ipv6-address-space/ipv6-address-space.xml
bool IsPrivateIpv6(const struct sockaddr_in6* addr6) {
  const auto& ip = addr6->sin6_addr.s6_addr;
  if ((ip[0] == 0xfe) && (ip[1] == 0x80)) {
    // link-local address
    return false;
  }
  if ((ip[0] == 0xfe) && (ip[1] == 0xc0)) {
    // site-local address
    return false;
  }
  if ((ip[0] == 0xfc) && (ip[1] == 0x00)) {
    // unique-local address
    return false;
  }
  return true;
}

int GetLocalIps(std::vector<std::string>* ips) {
  ips->clear();
  struct ifaddrs* if_addrs = NULL;
  if (getifaddrs(&if_addrs) < 0) {
    LOG(ERROR) << "call getifaddrs failed, errno = "
               << std::string(strerror(errno));
    return -1;
  }
  struct ifaddrs* tmp = NULL;
  char buffer[INET_ADDRSTRLEN];
  char buffer_v6[INET6_ADDRSTRLEN];
  for (tmp = if_addrs; tmp != NULL; tmp = tmp->ifa_next) {
    // check if starts with specified prefix.
    if (tmp->ifa_addr == nullptr ||
        !byte::StringStartsWith(tmp->ifa_name,
                                FLAGS_bytestore_cfs_net_interface_prefix)) {
      continue;
    }
    if (tmp->ifa_addr->sa_family == AF_INET) {  // check if is ipv4
      void* addr = &((struct sockaddr_in*)tmp->ifa_addr)->sin_addr;  // NOLINT
      inet_ntop(AF_INET, addr, buffer, INET_ADDRSTRLEN);
      ips->emplace_back(std::string(buffer));
    } else if (tmp->ifa_addr->sa_family == AF_INET6) {  // check if is ipv6
      void* addr = &((struct sockaddr_in6*)tmp->ifa_addr)->sin6_addr;  // NOLINT
      if (!IsPrivateIpv6(
              reinterpret_cast<struct sockaddr_in6*>(tmp->ifa_addr))) {
        continue;
      }
      inet_ntop(AF_INET6, addr, buffer_v6, INET6_ADDRSTRLEN);
      ips->emplace_back(std::string(buffer_v6));
    }
  }
  freeifaddrs(if_addrs);
  if (ips->empty()) {
    byte::Ip6Address ipv4, ipv6;
    std::string ip_str;
    if (byte::Ip6Address::GetFirstPrivateAddress(&ipv4, false)) {
      ip_str = ipv4.ToString();
    }
    if (ip_str.empty() &&
        byte::Ip6Address::GetFirstPrivateAddress(&ipv6, true)) {
      ip_str = ipv6.ToString();
    }
    ips->emplace_back(ip_str);
  }
  return 0;
}

int ParseHostname(const char* hostname, in_addr* addr) {
  struct addrinfo hints;
  struct addrinfo* result;
  memset(&hints, 0, sizeof(hints));
  hints.ai_family = AF_INET;
  int ret = getaddrinfo(hostname, NULL, &hints, &result);
  if (ret != 0) {
    LOG(ERROR) << "Parse hostname " << hostname
               << " failed, error msg: " << gai_strerror(ret);
    return -1;
  }
  *addr = ((struct sockaddr_in*)(result->ai_addr))->sin_addr;
  freeaddrinfo(result);
  return 0;
}

int GetStatTime(const char* filename, uint64_t* access_time,
                uint64_t* modify_time, uint64_t* change_time) {
  struct stat file_stat;
  int ret = stat(filename, &file_stat);
  if (ret != 0) {
    LOG(ERROR) << "Get file " << filename << " stat failed, error: " << errno
               << ", msg: " << strerror(errno);
    return ret;
  }
  *access_time =
      file_stat.st_atim.tv_sec * 1000000LL + file_stat.st_atim.tv_nsec;
  *modify_time =
      file_stat.st_mtim.tv_sec * 1000000LL + file_stat.st_mtim.tv_nsec;
  *change_time =
      file_stat.st_ctim.tv_sec * 1000000LL + file_stat.st_ctim.tv_nsec;

  return 0;
}

}  // namespace bds::dancedn::cloudfs
