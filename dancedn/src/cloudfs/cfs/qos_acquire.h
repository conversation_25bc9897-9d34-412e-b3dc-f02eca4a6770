// copyright (c) 2021-present, bytedance inc. all rights reserved.

#pragma once

#include <cstdint>
#include <memory>
#include <string>

#include "byte/concurrent/mutex.h"
#include "cloudfs/cfs/rate_limiter.h"
#include "cloudfs/cfs/sample_rate.h"
#include "cloudfs/namenode_client.h"
#include "cloudfs_proto/DatanodeProtocol.pb.h"

namespace byte {
namespace embedded_metrics {
class IntGauge;
}
}  // namespace byte

namespace bds::dancedn::cloudfs {

static const int MIN_ACQUIRE_WAIT_TIME_US = 10;
static const int MAX_ACQUIRE_WAIT_TIME_US = 10 * 1000;
using ProtoPriority = ::cloudfs::datanode::AcquireRequest_Priority;

enum class AcquireType : uint8_t {
  GET_RATE = 0,
  PUT_RATE = 1,
  GET = 2,
  PUT = 3,
};

class BaseAcquire {
 public:
  BaseAcquire(NamenodeClient* client, AcquireType type, ProtoPriority priority);

  virtual ~BaseAcquire();

  int64_t Acquire(int64_t num);

  virtual const std::string& GetKey() const = 0;

  inline AcquireType GetType() const {
    return type_;
  }

  void SetAcquireNum(int64_t acquire_num) {
    base_acquire_num_ = acquire_num;
    dyn_acquire_num_.store(acquire_num);
  }
  int64_t DEBUG_GetAcquireNum() {
    return dyn_acquire_num_.load();
  }

  inline ProtoPriority GetPriority() {
    return priority_;
  }

  static inline ProtoPriority HighPriority() {
    return ::cloudfs::datanode::AcquireRequest_Priority_Level0;
  }
  static inline ProtoPriority LowPriority() {
    return ::cloudfs::datanode::AcquireRequest_Priority_Level1;
  }
  byte::embedded_metrics::IntGauge* GetApplyNumMetric();
  byte::embedded_metrics::IntGauge* GetAllocateNumMetric();
  byte::embedded_metrics::IntGauge* GetFallbackNumMetric();
  byte::embedded_metrics::IntGauge* GetFallbackRateMetric();

 private:
  int64_t SendRequest();
  inline void AdjustAcquireNum(int64_t elapsed);
  inline int64_t DoAcquire(int64_t num);
  void UpdateAllocateSample(int64_t num);
  int64_t FallbackAcquireIfPossible(int64_t num);

  AcquireType type_;
  ProtoPriority priority_;
  // Acquired num from NamenodeClient last time
  int64_t acquired_;
  int64_t acquired_at_;
  int64_t used_;
  // If tokens are exhausted in NamenodeClient side, retry after
  // `retry_interval_`(ms).
  int64_t retry_interval_;
  // If tokens are not consumed in `windows_len_`(ms), discard them and
  // re-acquire from client.
  int64_t windows_len_;
  int64_t base_acquire_num_;
  // Num per request is dynamically adjusted according to use rate.
  std::atomic<int64_t> dyn_acquire_num_;
  std::atomic<bool> requesting_;
  std::atomic<int64_t> request_start_time_;
  std::atomic<uint64_t> acquire_idx_;
  std::atomic<bool> request_err_{false};
  SampleRate allocate_sample_;
  RateLimiter fallback_limiter_;
  std::atomic<bool> first_fallback_;
  byte::Mutex request_lock_;
  std::atomic<byte::embedded_metrics::IntGauge*> apply_num_metric_{nullptr};
  std::atomic<byte::embedded_metrics::IntGauge*> allocate_num_metric_{nullptr};
  std::atomic<byte::embedded_metrics::IntGauge*> fallback_num_metric_{nullptr};
  std::atomic<byte::embedded_metrics::IntGauge*> fallback_rate_metric_{nullptr};
  std::unique_ptr<NamenodeClient> client_;
};

class NamespaceThroughputAcquire : public BaseAcquire {
 public:
  NamespaceThroughputAcquire(NamenodeClient* client, const std::string& ns_id,
                             AcquireType type, ProtoPriority priority);

  ~NamespaceThroughputAcquire();

  const std::string& GetKey() const override {
    return key_;
  };

 private:
  const std::string key_;
};

class TaskThroughputAcquire : public BaseAcquire {
 public:
  TaskThroughputAcquire(NamenodeClient* client, const std::string& task_id,
                        AcquireType type, ProtoPriority priority);

  ~TaskThroughputAcquire();

  const std::string& GetKey() const override {
    return key_;
  };

 private:
  const std::string key_;
};

class TosThroughputAcquire : public BaseAcquire {
 public:
  TosThroughputAcquire(NamenodeClient* client, AcquireType type,
                       ProtoPriority priority);

  ~TosThroughputAcquire();

  const std::string& GetKey() const override {
    return key_;
  };

 private:
  const std::string key_;
};

class HdfsThroughputAcquire : public BaseAcquire {
 public:
  HdfsThroughputAcquire(NamenodeClient* client, AcquireType type,
                        ProtoPriority priority);

  ~HdfsThroughputAcquire();

  const std::string& GetKey() const override {
    return key_;
  };

 private:
  const std::string key_;
};

class TosThroughputAcquireByNamespace : public BaseAcquire {
 public:
  TosThroughputAcquireByNamespace(NamenodeClient* client, uint64_t ns_id,
                                  AcquireType type, ProtoPriority priority);

  ~TosThroughputAcquireByNamespace();

  const std::string& GetKey() const override {
    return key_;
  };

 private:
  const std::string key_;
};

class TosQPSAcquire : public BaseAcquire {
 public:
  TosQPSAcquire(NamenodeClient* client, AcquireType type,
                ProtoPriority priority, const std::string& interface_name);
  ~TosQPSAcquire();
  const std::string& GetKey() const override {
    return key_;
  };

 private:
  const std::string key_;
};

class HdfsQPSAcquire : public BaseAcquire {
 public:
  HdfsQPSAcquire(NamenodeClient* client, AcquireType type,
                 ProtoPriority priority, const std::string& interface_name);
  ~HdfsQPSAcquire();
  const std::string& GetKey() const override {
    return key_;
  };

 private:
  const std::string key_;
};

}  // namespace bds::dancedn::cloudfs
