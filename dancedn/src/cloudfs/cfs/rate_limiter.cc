// copyright (c) 2021-present, bytedance inc. all rights reserved.

#include "cloudfs/cfs/rate_limiter.h"

#include <algorithm>
#include <cstdint>
#include <iostream>

#include "byte/system/timestamp.h"
#include "byte/thread/this_thread.h"
#include "concurrent/mutex.h"

namespace bds::dancedn::cloudfs {

static uint32_t MICRO_SECONDS_PER_SECOND = 1000000;
static uint64_t MAX_SLEEP_DURATION_US = 100 * 1000;  // 100ms

RateLimiter::RateLimiter(double permits_per_second, double max_burst_seconds)
    : lock_(),
      stored_permits_(0),
      next_free_ticket_micros_(0),
      stopping_(false) {
  if (permits_per_second > 1.0) {
    enabled_.store(true);
    max_permits_ = max_burst_seconds * permits_per_second;
    max_burst_seconds_ = max_burst_seconds;
    stable_interval_micros_ =
        1.0 * MICRO_SECONDS_PER_SECOND / permits_per_second;
  } else {
    enabled_.store(false);
  }
}

RateLimiter::~RateLimiter() {}

void RateLimiter::QuickStop() {
  stopping_.store(true);
}

void RateLimiter::UpdateRate(double permits_per_second) {
  byte::MutexLocker guard(&lock_);
  if (permits_per_second > 1.0) {
    enabled_.store(true);
    max_permits_ = max_burst_seconds_ * permits_per_second;
    stable_interval_micros_ =
        1.0 * MICRO_SECONDS_PER_SECOND / permits_per_second;
  } else {
    enabled_.store(false);
  }
}

double RateLimiter::Acquire(uint32_t permits, bytestore::IOPriority pri,
                            uint64_t timeout_us) {
  if (!enabled_.load()) {
    return 0.0;
  }
  uint64_t micros_to_wait = Reserve(permits);
  uint64_t duration_left = micros_to_wait;
  while (duration_left > 0) {
    if (stopping_.load() || !enabled_.load()) {
      return 0.0;
    }
    if (micros_to_wait - duration_left > timeout_us) {
      return 0.0;
    }
    uint64_t duration = std::min(MAX_SLEEP_DURATION_US, duration_left);
    byte::ThisThread::SleepInUs(duration);
    duration_left -= duration;
  }
  return 1.0 * micros_to_wait / MICRO_SECONDS_PER_SECOND;
}

uint64_t RateLimiter::Reserve(uint32_t permits) {
  byte::MutexLocker guard(&lock_);
  uint64_t now_micros = byte::GetCurrentTimeInUs();
  if (now_micros > next_free_ticket_micros_) {
    double new_permits =
        (now_micros - next_free_ticket_micros_) / stable_interval_micros_;
    stored_permits_ = std::min(max_permits_, stored_permits_ + new_permits);
    next_free_ticket_micros_ = now_micros;
  }
  uint64_t free_ticket_micros = next_free_ticket_micros_;
  double stored_permits_to_spend = std::min(1.0 * permits, stored_permits_);
  double fresh_permits = permits - stored_permits_to_spend;
  int64_t wait_micros = fresh_permits * stable_interval_micros_;
  next_free_ticket_micros_ += wait_micros;
  stored_permits_ -= stored_permits_to_spend;
  return free_ticket_micros > now_micros ? free_ticket_micros - now_micros : 0;
}

double RateLimiter::GetRate() {
  return MICRO_SECONDS_PER_SECOND / stable_interval_micros_;
}

}  // namespace bds::dancedn::cloudfs
