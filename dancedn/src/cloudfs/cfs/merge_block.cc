// copyright (c) 2021-present, bytedance inc. all rights reserved.

#include "cloudfs/cfs/merge_block.h"

#include <cstdint>
#include <functional>
#include <memory>

#include "butil/crc32c.h"
#include "cloudfs/client_io.h"
#include "cloudfs/constants.h"
#include "cloudfs/datanode.h"
#include "cloudfs/datanode_id.h"
#include "cloudfs/datanode_info.h"
#include "cloudfs/datanode_info_with_storage.h"
#include "cloudfs/exceptions.h"
#include "cloudfs/inter_datanode_client.h"
#include "cloudfs/inter_datanode_rpc.h"
#include "cloudfs/io/address.h"
#include "cloudfs/io/io_buf.h"
#include "cloudfs/located_block.h"
#include "cloudfs/store/ufs/remote_store.h"
#include "cloudfs/store/unified_block_store.h"
#include "cloudfs/util.h"
#include "include/byte_log.h"
#include "util/scope_guard.h"

DECLARE_uint32(bytestore_inter_datanode_socket_timeout);

namespace bds::dancedn::cloudfs {

MergeBlock::MergeBlock(
    DataNode* dn, const std::shared_ptr<ExtendedBlock>& block,
    const std::shared_ptr<std::vector<std::shared_ptr<LocatedBlock>>>& olds)
    : dn_(dn), block_(block), olds_(olds) {}

MergeBlock::~MergeBlock() {}
exceptions::Exception MergeBlock::ReadData(
    const std::shared_ptr<LocatedBlock>& block, uint32_t offset,
    uint32_t length, io::IOChunk* chunk, uint32_t* readed) {
  std::function<exceptions::Exception(InterDatanodeClient*)> func =
      [&chunk, &offset, &length, &block](InterDatanodeClient* client) {
        return client->ReadBlockFromLocal(*(block->GetBlock()), offset, length,
                                          chunk);
      };
  std::function<exceptions::Exception()> try_local_func = [this, &block, &chunk,
                                                           &offset, &length]() {
    return dn_->GetStorage()->ReadBlock(block->GetBlock(), chunk, offset,
                                        length, true,
                                        bytestore::PRIORITY_BEST_EFFORT);
  };
  uint32_t used = chunk->UsedLength();

  auto&& e = CallDst(block, func, try_local_func);
  if (!e.OK()) {
    LOG(ERROR) << "Read block " << block->ToString()
               << " failed, cause: " << e.ToString();
    return e;
  }
  *readed = chunk->UsedLength() - used;
  return e;
}

exceptions::Exception MergeBlock::CallDst(
    const std::shared_ptr<LocatedBlock>& block,
    const std::function<exceptions::Exception(InterDatanodeClient*)>& func,
    const std::function<exceptions::Exception()>& try_local_func) {
  std::shared_ptr<DatanodeRegistration> dnreg;
  exceptions::Exception e =
      dn_->GetDNRegistrationForBP(block_->GetBlockPoolID(), &dnreg);
  if (!e.OK()) {
    return e;
  }
  for (auto&& loc : block->GetLocations()) {
    std::shared_ptr<DatanodeID> target =
        loc->GetDatanodeInfo()->GetDatanodeID();
    InterDatanodeClient* client;
    if (target->Equals(dnreg->GetDatanodeID())) {
      client = NewLocalInterDatanodeClient(dn_);
    } else {
      io::IPAddress addr = target->GetIPCAddr();
      InterDatanodeRpc* rpc = new InterDatanodeRpc(
          new ClientIO(), addr, FLAGS_bytestore_inter_datanode_socket_timeout);
      client = NewRemoteInterDatanodeClient(rpc);
    }
    std::unique_ptr<InterDatanodeClient> client_ptr(client);
    e = func(client);
    if (!e.OK()) {
      LOG(ERROR) << "Call " << target->ToString()
                 << " failed, cause: " << e.ToString()
                 << ", block: " << block->GetBlock()->ToString();
    } else {
      return e;
    }
  }
  return try_local_func();
}

void MergeBlock::Run() {
  {
    std::string msg;
    for (auto&& block : *olds_) {
      msg += block->ToString() + ", ";
    }
    LOG(INFO) << "Merge block " << block_->ToString() << ", olds: " << msg;
  }
  {
    uint64_t total = 0;
    for (auto&& old : (*olds_)) {
      total += old->GetBlockSize();
    }
    if (total != block_->GetNumBytes()) {
      LOG(ERROR) << "numbytes not equal, abort merge for "
                 << block_->ToString();
      return;
    }
  }
  exceptions::Exception e = InitMerge();
  if (!e.OK()) {
    LOG(ERROR) << "Failed to init merge, cause: " << e.ToString();
    return;
  }
  io::IOChunk* chunk = new io::IOChunk(MERGE_PART_SIZE);
  byte::ScopeGuard chunk_deleter([&chunk]() {
    chunk->AlwaysDestroy();
  });
  uint32_t part_num = 1;
  for (auto&& block : (*olds_)) {
    LOG(INFO) << "Merge block " << block->ToString() << " into "
              << block_->ToString();
    uint32_t block_length = block->GetBlockSize();
    uint32_t offset = 0;
    while (true) {
      uint32_t length = std::min(static_cast<uint32_t>(chunk->UnusedLength()),
                                 block_length - offset);
      uint32_t readed = 0;
      e = ReadData(block, offset, length, chunk, &readed);
      if (!e.OK()) {
        LOG(ERROR) << "Failed to read data of block " << block->ToString()
                   << ", cause: " << e.ToString();
        CleanWhenFailed();
        return;
      }
      if (readed != length) {
        LOG(ERROR) << "Unexpected readed length: " << readed
                   << ", expected: " << length;
        CleanWhenFailed();
        return;
      }
      if (chunk->UsedLength() == MERGE_PART_SIZE) {
        e = WriteData(chunk, part_num);
        if (!e.OK()) {
          LOG(ERROR) << "Failed to write data into block " << block_->ToString()
                     << ", cause: " << e.ToString();
          CleanWhenFailed();
          return;
        }
        part_num++;
        chunk->Reset();
      }
      offset += readed;
      if (offset == block_length) {
        break;
      }
    }
  }
  if (chunk->UsedLength() > 0) {
    e = WriteData(chunk, part_num);
    if (!e.OK()) {
      LOG(ERROR) << "Failed to write data into block " << block_->ToString()
                 << ", cause: " << e.ToString();
      CleanWhenFailed();
      return;
    }
    part_num++;
  }
  e = FinishMerge();
  if (!e.OK()) {
    LOG(ERROR) << "Failed to finish merge, cause: " << e.ToString();
    CleanWhenFailed();
    return;
  }
  LOG(INFO) << "Finish merge, block " << block_->ToString()
            << ", part_num: " << part_num;
  LOG(INFO) << "Check crc for " << block_->ToString();
  uint32_t offset = 0;
  for (auto&& block : (*olds_)) {
    uint32_t length = block->GetBlock()->GetNumBytes();
    uint32_t crc;
    exceptions::Exception e = CalculateMergedCRC(offset, length, &crc);
    if (!e.OK()) {
      LOG(ERROR) << "Failed to calculate crc for " << block->ToString()
                 << ", cause: " << e.ToString();
      return;
    }
    e = CheckCRC(block, crc);
    if (!e.OK()) {
      LOG(ERROR) << "Check crc failed for " << block->ToString()
                 << ", cause: " << e.ToString();
      return;
    }
    offset += length;
  }
  dn_->NotifyNamenodeMergedBlock(block_.get());
}

MergeBlockToTos::MergeBlockToTos(
    DataNode* dn, const std::shared_ptr<ExtendedBlock>& block,
    const std::shared_ptr<std::vector<std::shared_ptr<LocatedBlock>>>& olds,
    NamespaceType ns_type)
    : MergeBlock(dn, block, olds), dn_(dn), upload_context_(nullptr) {
  remote_store_ = dn_->GetStorage()->GetRemoteStore(ns_type);
}

MergeBlockToTos::~MergeBlockToTos() {}

exceptions::Exception MergeBlockToTos::InitMerge() {
  return remote_store_->CreateMultipartUploadV2(
      GetBlock(), &upload_context_, bytestore::PRIORITY_BEST_EFFORT);
}

exceptions::Exception MergeBlockToTos::WriteData(io::IOChunk* chunk,
                                                 uint32_t part_num) {
  return remote_store_->MultipartUploadPartV2(upload_context_, chunk, part_num);
}

exceptions::Exception MergeBlockToTos::FinishMerge() {
  return remote_store_->CompleteMultipartUploadV2(upload_context_);
}

void MergeBlockToTos::CleanWhenFailed() {
  if (upload_context_ != nullptr) {
    auto&& e = remote_store_->AbortMultipartUploadV2(upload_context_);

    if (!e.OK()) {
      LOG(ERROR) << "Failed to abort multipart upload: " << e.ToString();
    }
  }
}

exceptions::Exception MergeBlockToTos::CalculateMergedCRC(uint32_t offset,
                                                          uint32_t length,
                                                          uint32_t* crc) {
  io::IOChunk* chunk = new io::IOChunk(length);
  io::IOChunk::Deleter chunk_deleter(chunk);
  exceptions::Exception e = remote_store_->ReadBlock(
      *GetBlock(), offset, length, bytestore::PRIORITY_BEST_EFFORT, chunk);
  RETURN_IF_ERROR(e);
  *crc = butil::crc32c::Extend(
      0, reinterpret_cast<const char*>(chunk->UsedData()), chunk->UsedLength());
  RETURN_NO_EXCEPTION();
}

exceptions::Exception MergeBlockToTos::CheckCRC(
    std::shared_ptr<LocatedBlock> block, uint32_t crc) {
  uint32_t real_crc;
  std::function<exceptions::Exception(InterDatanodeClient*)> func =
      [&block, &real_crc](InterDatanodeClient* client) {
        return client->CalculateCrc(*(block->GetBlock()), &real_crc);
      };
  std::function<exceptions::Exception()> try_local_func = [this, &block,
                                                           &real_crc]() {
    io::IOChunk* chunk = new io::IOChunk(block->GetBlock()->GetNumBytes());
    io::IOChunk::Deleter chunk_deleter(chunk);
    exceptions::Exception e = remote_store_->ReadBlock(
        *block->GetBlock(), 0, block->GetBlock()->GetNumBytes(),
        bytestore::PRIORITY_BEST_EFFORT, chunk);
    RETURN_IF_ERROR(e);
    real_crc = butil::crc32c::Extend(
        0, reinterpret_cast<const char*>(chunk->UsedData()),
        chunk->UsedLength());
    RETURN_NO_EXCEPTION();
  };

  exceptions::Exception e = CallDst(block, func, try_local_func);
  RETURN_IF_ERROR(e);
  if (real_crc != crc) {
    LOG(ERROR) << "CRC check failed for block " << block->ToString()
               << ", crc: " << crc << ", real_crc: " << real_crc;
    return exceptions::Exception(exceptions::kIOException, "crc mismatch");
  }
  return e;
}

}  // namespace bds::dancedn::cloudfs
