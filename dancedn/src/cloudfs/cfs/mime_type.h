// copyright (c) 2024-present, bytedance inc. all rights reserved.

#pragma once

#include <string>
#include <unordered_map>

namespace bds::dancedn::cloudfs {

class MimeType {
 public:
  static std::string GetMimetypeByName(const std::string& name);

 private:
  static const std::unordered_map<std::string, std::string> mime_type_map;
  static const char* DEFAULT_MIMETYPE;
};

}  // namespace bds::dancedn::cloudfs
