// copyright (c) 2021-present, bytedance inc. all rights reserved.

#include "cloudfs/cfs/decommission.h"

#include <memory>
#include <vector>

#include "cloudfs/mocks/mock_datanode.h"
#include "cloudfs/mocks/mock_store.h"
#include "gtest/gtest.h"

DECLARE_int32(bytestore_cfs_decommission_check_interval_ms);
DECLARE_uint32(bytestore_cfs_decommission_wait_time_ms);

namespace bds::dancedn::cloudfs {

class DecommissionTest : public ::testing::Test {
 public:
  DecommissionTest() {}
  ~DecommissionTest() {}

  void SetUp() {
    bytestore::metrics_internal::InitFastMetrics();
    dn_ = new MockDataNode();
    store_ = new MockStore();
    local_store_ = new MockChunkserverStore();
    EXPECT_CALL(*dn_, GetStorage()).WillRepeatedly(::testing::Return(store_));
    EXPECT_CALL(*store_, GetLocalStore())
        .WillRepeatedly(::testing::Return(local_store_));
  }
  void TearDown() {
    store_->StopMgr();
    delete local_store_;
    delete store_;
    delete dn_;
  }

  MockDataNode* GetDN() {
    return dn_;
  }
  MockStore* GetStore() {
    return store_;
  }
  MockChunkserverStore* GetLocalStore() {
    return local_store_;
  }

 private:
  MockDataNode* dn_;
  MockStore* store_;
  MockChunkserverStore* local_store_;
};

TEST_F(DecommissionTest, TestDecommissionWorker) {
  FLAGS_bytestore_cfs_decommission_check_interval_ms = 20;
  FLAGS_bytestore_cfs_decommission_wait_time_ms = 500;
  uint32_t ibr_cnt = 0;
  EXPECT_CALL(*GetDN(), NotifyNamenodeNegoedUploadId(::testing::_, ::testing::_,
                                                     ::testing::_))
      .WillRepeatedly(testing::Invoke(
          [&ibr_cnt](ExtendedBlock* block, const std::string& storage_uuid,
                     const std::string& upload_id) {
            ibr_cnt++;
          }));
  std::vector<std::shared_ptr<ReplicaInfo>> replicas;
  for (uint32_t i = 0; i < 10; i++) {
    std::shared_ptr<FinalizedReplica> replica =
        std::make_shared<FinalizedReplica>("bpid", i, 1024, 1024, false, "uuid",
                                           false, false, 0, false, 0);
    if (i % 2 == 0) {
      replica->SetEvictable(true);
    }
    replicas.push_back(replica);
  }
  EXPECT_CALL(*GetLocalStore(), GetAllReplicas())
      .WillRepeatedly(testing::Return(replicas));
  DecommissionMgr* decommission_mgr = new DecommissionMgr(GetDN());
  DecommissionWorker* worker =
      new DecommissionWorker(decommission_mgr, GetDN());
  worker->Start();
  byte::ThisThread::SleepInMs(1000);
  EXPECT_GE(ibr_cnt, 10);
  EXPECT_TRUE(DataNode::prevent_write_);
  worker->Stop();
  worker->Join();
  delete worker;
  delete decommission_mgr;
}

}  // namespace bds::dancedn::cloudfs
