// copyright (c) 2021-present, bytedance inc. all rights reserved.

#include "cloudfs/cfs/storage_class.h"

#include <cassert>
#include <string>
#include <vector>

#include "byte/include/assert.h"

namespace bds::dancedn::cloudfs {

static std::vector<std::string> STORAGE_CLASS_STR = {"WARM", "HOT", "COLD"};

const std::string& StorageClassToString(StorageClass storage_class) {
  return STORAGE_CLASS_STR[static_cast<int>(storage_class)];
}

::cloudfs::StorageClassProto ConvertStorageClassToProto(
    StorageClass storage_class) {
  switch (storage_class) {
    case StorageClass::HOT: return ::cloudfs::StorageClassProto::HOT;
    case StorageClass::WARM: return ::cloudfs::StorageClassProto::WARM;
    case StorageClass::COLD: return ::cloudfs::StorageClassProto::COLD;
    default:
      BYTE_ASSERT(false) << "Unexpected storage_class value "
                         << StorageClassToString(storage_class);
      return ::cloudfs::StorageClassProto::WARM;
  }
}

}  // namespace bds::dancedn::cloudfs
