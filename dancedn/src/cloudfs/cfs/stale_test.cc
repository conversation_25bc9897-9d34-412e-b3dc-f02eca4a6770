// Copyright (c) 2022-present, ByteDance Inc. All rights reserved.

#include "cloudfs/cfs/stale.h"

#include <fstream>
#include <iostream>

#include "byte_log/byte_log_impl.h"
#include "cloudfs/exceptions.h"
#include "gflags/gflags_declare.h"
#include "gtest/gtest.h"

DECLARE_string(bytestore_cfs_stale_conf_path);
DECLARE_bool(bytestore_cfs_enable_stale);

namespace bds::dancedn::cloudfs {

using exceptions::Exception;

class StaleConfigTests : public ::testing::Test {
 public:
  ~StaleConfigTests() override = default;

  void SetUp() override {
    FLAGS_bytestore_cfs_stale_conf_path = "/tmp/stale_dn";
    FLAGS_bytestore_cfs_enable_stale = true;
    mgr_.reset(new StaleDnConfigMgr());
    refresher_.reset(new StaleDnRefreshTask(
        nullptr, nullptr, -1, FLAGS_bytestore_cfs_stale_conf_path, mgr_.get()));
  }

  void TearDown() override {}

  StaleDnConfigMgr* mgr() {
    return mgr_.get();
  }

  StaleDnRefreshTask* refresher() {
    return refresher_.get();
  }

  static void SetUpTestCase() {}
  static void TearDownTestCase() {
    byte::GetLoggingSystem()->Shutdown();
  }

 private:
  std::unique_ptr<StaleDnConfigMgr> mgr_;
  std::unique_ptr<StaleDnRefreshTask> refresher_;
};

Exception FillInContent(const std::string& path, const std::string& content) {
  std::ofstream tmp_file(path);
  BYTE_DEFER(if (tmp_file.is_open()) { tmp_file.close(); });
  if (tmp_file.is_open()) {
    tmp_file << content;
  }
  return Exception();
}

TEST_F(StaleConfigTests, TestReadConfig) {
  {
    auto e = FillInContent(FLAGS_bytestore_cfs_stale_conf_path, R"({a})");
    EXPECT_TRUE(e.OK());
    refresher()->ReadConfig();
    EXPECT_TRUE(mgr()->AmIStale("4.4.4.4").is_false());
  }
  {
    auto e = FillInContent(FLAGS_bytestore_cfs_stale_conf_path, R"({})");
    EXPECT_TRUE(e.OK());
    refresher()->ReadConfig();
    EXPECT_TRUE(mgr()->AmIStale("4.4.4.4").is_false());
  }
  {
    auto e = FillInContent(FLAGS_bytestore_cfs_stale_conf_path,
                           R"({"stale_dns":""})");
    EXPECT_TRUE(e.OK());
    refresher()->ReadConfig();
    EXPECT_TRUE(mgr()->AmIStale("4.4.4.4").is_false());
  }
  {
    auto e = FillInContent(FLAGS_bytestore_cfs_stale_conf_path,
                           R"({"stale_dns":"1.1.1.1"})");
    EXPECT_TRUE(e.OK());
    refresher()->ReadConfig();
    EXPECT_TRUE(mgr()->AmIStale("4.4.4.4").is_false());
    EXPECT_TRUE(mgr()->AmIStale("1.1.1.1").is_true());
  }

  {
    auto e = FillInContent(FLAGS_bytestore_cfs_stale_conf_path,
                           R"({"stale_dns":"1.1.1.1,2.2.2.2"})");
    EXPECT_TRUE(e.OK());
    refresher()->ReadConfig();
    EXPECT_TRUE(mgr()->AmIStale("4.4.4.4").is_false());
    EXPECT_TRUE(mgr()->AmIStale("1.1.1.1").is_true());
    EXPECT_TRUE(mgr()->AmIStale("2.2.2.2").is_true());
  }
}

}  // namespace bds::dancedn::cloudfs
