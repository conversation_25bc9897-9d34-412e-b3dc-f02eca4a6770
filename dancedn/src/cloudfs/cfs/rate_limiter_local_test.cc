// copyright (c) 2021-present, bytedance inc. all rights reserved.

#include "cloudfs/cfs/rate_limiter_local.h"

#include <cstdint>

#include "byte/thread/this_thread.h"
#include "cloudfs/metrics.h"
#include "gflags/gflags.h"
#include "gtest/gtest.h"

DECLARE_uint64(bytestore_cfs_net_in_throughput_limit_bytes);
DECLARE_uint64(bytestore_cfs_net_out_throughput_limit_bytes);

namespace bds::dancedn::cloudfs {

bool Eq(double val1, double val2, double delta) {
  if (val1 == val2) {
    return true;
  }
  if (val1 > val2 && val2 + delta > val1) {
    return true;
  }
  if (val2 > val1 && val1 + delta > val2) {
    return true;
  }
  std::cout << "val1:" << val1 << ", val2:" << val2 << std::endl;
  return false;
}

static double EPSILON = 0.01;

TEST(RateLimiterLocalTest, SimpleAcquire) {
  bytestore::metrics_internal::InitFastMetrics();
  FLAGS_bytestore_cfs_net_in_throughput_limit_bytes = 5;
  FLAGS_bytestore_cfs_net_out_throughput_limit_bytes = 5;
  std::set<uint32_t> disk_set{1, 2};
  RateLimiterLocal local_limiter(disk_set);
  ASSERT_TRUE(Eq(0.0,
                 local_limiter.Acquire(1, LimitObject::kClientWrite, 5,
                                       bytestore::PRIORITY_ELASTIC),
                 EPSILON));
  ASSERT_TRUE(Eq(0.0,
                 local_limiter.Acquire(1, LimitObject::kClientRead, 5,
                                       bytestore::PRIORITY_ELASTIC),
                 EPSILON));
  FLAGS_bytestore_cfs_net_in_throughput_limit_bytes = 10;
  FLAGS_bytestore_cfs_net_out_throughput_limit_bytes = 10;
  local_limiter.UpdateLimitRateByFlag();
  byte::ThisThread::SleepInMs(1500);
  ASSERT_TRUE(Eq(0.0,
                 local_limiter.Acquire(1, LimitObject::kClientWrite, 40,
                                       bytestore::PRIORITY_ELASTIC),
                 EPSILON));
  ASSERT_TRUE(Eq(0.0,
                 local_limiter.Acquire(1, LimitObject::kClientRead, 40,
                                       bytestore::PRIORITY_ELASTIC),
                 EPSILON));
}

}  // namespace bds::dancedn::cloudfs
