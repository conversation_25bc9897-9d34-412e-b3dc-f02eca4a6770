// Copyright (c) 2020-present, ByteDance Inc. All rights reserved.

#pragma once
#ifndef OP_STATS_H_
#define OP_STATS_H_

#include <cstdint>
#include <memory>
#include <string>
#include <vector>

#include "byte/container/lockfree_queue.h"
#include "cloudfs/opstats/op_key.h"
#include "cloudfs/thread.h"

namespace bds::dancedn::cloudfs {

struct OpStatsOper {
  OpKey op_key_;
  int64_t offset_;
  int64_t bytes_;
  int64_t start_ms_;
  int64_t cost_us_;
  bool success_;
  std::string msg_;

  OpStatsOper() {}
  OpStatsOper(const OpKey& op_key, const int64_t& offset, const int64_t& bytes,
              const int64_t& start_ms, const int64_t& cost_us, bool success,
              const std::string& msg)
      : op_key_(op_key),
        offset_(offset),
        bytes_(bytes),
        start_ms_(start_ms),
        cost_us_(cost_us),
        success_(success),
        msg_(msg) {}
  ~OpStatsOper() {}
};

class TraceEmitter {
 public:
  virtual int EmitTrace(
      const std::vector<std::shared_ptr<OpStatsOper>>& opers) = 0;
};

class OpStatsThread : public Thread {
 public:
  OpStatsThread();
  ~OpStatsThread() {}

  void Run();
  void Record(const OpKey& op_key, const int64_t& offset, const int64_t& bytes,
              const int64_t& start_ms, const int64_t& cost_us, bool success,
              const std::string& msg);
  // for test
  size_t GetQueueSize() {
    return op_queue_.Size();
  }

 private:
  void FlushTrace(bool ignore_failure);

 private:
  byte::LockFreeQueue<std::shared_ptr<OpStatsOper>> op_queue_;
  std::vector<std::shared_ptr<OpStatsOper>> flush_queue_;
  std::shared_ptr<TraceEmitter> emitter_;
};

class OpStats {
 public:
  void Init(size_t num_threads = 1, const std::string& host = "");

  static OpStats& GetInstance() {
    static OpStats op_stats;
    return op_stats;
  }

  void Record(const OpKey& op_key, const int64_t& offset, const int64_t& bytes,
              const int64_t& start_ms, const int64_t& cost_us,
              bool success = true, const std::string& msg = "");
  OpStats(const OpStats& op_stats) = delete;
  OpStats& operator=(const OpStats& op_stats) = delete;
  std::string GetHostIp() {
    return host_ip_;
  }

  // for test
  size_t GetQueueSize();

  void Stop();
  void Join();

 private:
  OpStats() {}
  std::vector<std::unique_ptr<OpStatsThread>> op_threads_;
  std::string host_ip_;
};

}  // namespace bds::dancedn::cloudfs

#endif
