// Copyright (c) 2018-present, ByteDance Inc. All rights reserved.

#pragma once

#include <deque>
#include <future>  // NOLINT
#include <memory>
#include <set>
#include <string>
#include <unordered_map>
#include <utility>
#include <vector>

#include "byte/base/atomic.h"
#include "byte/base/closure.h"
#include "byte/concurrent/mutex.h"
#include "chunkserver/common/chunkserver_types.h"
#include "chunkserver/common/io_request.h"
#include "cloudfs/scanner.h"
#include "common/memory_pool_repo.h"
#include "common/store_types.h"

namespace bytestore {
class AsyncThread;
class Timer;

namespace chunkserver {
class Disk;
}  // namespace chunkserver
}  // namespace bytestore

namespace bds::dancedn::cloudfs {

class Store;

class MigrationScanner : public Scanner<bytestore::chunkserver::Disk*> {
 public:
  explicit MigrationScanner(Store* storage);

  ~MigrationScanner();

  void Start() override;
  void Stop() override;

 private:
  void execute() override;
  void reschedule() override;
  bytestore::chunkserver::Disk* GetNextDisk() override;
  void ScanDisk(int index, int scan_depth) override;

 private:
  Store* storage_;
  std::unique_ptr<bytestore::AsyncThread> run_thread_;
  std::unique_ptr<Closure<void>> timer_callback_;
  std::unique_ptr<bytestore::Timer> timer_;
  std::unique_ptr<byte::Mutex> lock_;
  byte::Atomic<bool> stopping_;
  std::deque<bytestore::chunkserver::Disk*> migrated_taregt_disks_;
};

}  // namespace bds::dancedn::cloudfs
