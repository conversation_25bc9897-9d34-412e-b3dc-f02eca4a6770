// Copyright (c) 2019-present, ByteDance Inc. All rights reserved.

#pragma once

#include <memory>
#include <string>
#include <unordered_map>
#include <utility>
#include <vector>

#include "cloudfs/exceptions.h"
#include "cloudfs/io/address.h"
#include "cloudfs_proto/DatanodeProtocol.pb.h"
#include "cloudfs_proto/NamenodeProtocol.pb.h"
#include "cloudfs_proto/hdfs.pb.h"

namespace bds::dancedn::cloudfs {

class TOSInfo;
class RemoteBlockInfo;

}  // namespace bds::dancedn::cloudfs

namespace bds::dancedn::cloudfs {

extern const std::unordered_map<std::string, exceptions::E> ExceptionsMap;

class NameSpaceInfo;
class ClientIO;

class NamenodeRpc {
 public:
  NamenodeRpc() {}
  virtual ~NamenodeRpc() {}

  virtual exceptions::Exception Register(
      const ::cloudfs::datanode::RegisterDatanodeRequestProto* r,
      ::cloudfs::datanode::RegisterDatanodeResponseProto** res) = 0;

  virtual exceptions::Exception SendHeartbeat(
      const ::cloudfs::datanode::HeartbeatRequestProto* r,
      ::cloudfs::datanode::HeartbeatResponseProto** res) = 0;

  virtual exceptions::Exception BlockReport(
      const ::cloudfs::datanode::BlockReportRequestProto* r,
      ::cloudfs::datanode::BlockReportResponseProto** res) = 0;

  virtual exceptions::Exception BlockReceivedAndDeleted(
      const ::cloudfs::datanode::BlockReceivedAndDeletedRequestProto* r) = 0;

  virtual exceptions::Exception ErrorReport(
      const ::cloudfs::datanode::ErrorReportRequestProto* r) = 0;

  virtual exceptions::Exception VersionRequest(
      const ::cloudfs::VersionRequestProto* r, NameSpaceInfo** info,
      RemoteBlockInfo** remote_block_info) = 0;

  virtual exceptions::Exception ReportBadBlocks(
      const ::cloudfs::datanode::ReportBadBlocksRequestProto* r) = 0;

  virtual exceptions::Exception CommitBlockSynchronization(
      const ::cloudfs::datanode::CommitBlockSynchronizationRequestProto* r) = 0;

  virtual exceptions::Exception GetBlocks(
      const ::cloudfs::namenode::GetBlocksRequestProto* r,
      ::cloudfs::namenode::GetBlocksResponseProto** res) = 0;

  virtual exceptions::Exception Acquire(
      const ::cloudfs::datanode::AcquireRequest* r,
      ::cloudfs::datanode::AcquireResponse** res) = 0;

  virtual exceptions::Exception GetNamespaces(
      const ::cloudfs::datanode::GetNamespacesRequestProto* r,
      ::cloudfs::datanode::GetNamespacesResponseProto** res) = 0;

 public:
  static const std::unique_ptr<io::IPAddressCache>& GetIPAddressCache() {
    return ip_address_cache_;
  }

 public:
  const char* PROTOCOL_NAME =
      "org.apache.hadoop.hdfs.server.protocol.DatanodeProtocol";
  const char* NAMENODE_PROTOCOL_NAME =
      "org.apache.hadoop.hdfs.server.protocol.NamnodeProtocol";
  const uint32_t PROTOCOL_VERSION = 1;

 private:
  static std::unique_ptr<io::IPAddressCache> ip_address_cache_;
};

NamenodeRpc* NewNamenodeSyncRpc(std::unique_ptr<ClientIO> io,
                                const std::string& ip, int port,
                                uint32_t timeout);

NamenodeRpc* NewNamenodeSyncBrpc(std::unique_ptr<ClientIO> io,
                                 const std::string& ip, int port,
                                 uint32_t timeout);

}  // namespace bds::dancedn::cloudfs
