// copyright (c) 2021-present, bytedance inc. all rights reserved.

#include "cloudfs/lifecycle/sorted_block_entries.h"

#include <cassert>
#include <cstdint>
#include <iostream>
#include <map>
#include <memory>
#include <queue>
#include <thread>  // NOLINT
#include <unordered_map>
#include <vector>

#include "algorithm/random.h"
#include "base/closure.h"
#include "cloudfs/extended_block.h"
#include "cloudfs/finalized_replica.h"
#include "cloudfs/lifecycle/block_entry.h"
#include "cloudfs/replica_being_written.h"
#include "common/metrics.h"
#include "common/spin_lock.h"
#include "concurrent/count_down_latch.h"
#include "container/intrusive_list.h"
#include "gflags/gflags.h"
#include "gtest/gtest.h"
#include "string/format/print.h"
#include "system/timestamp.h"

DECLARE_uint64(arc_ghost_list_max_length);
DECLARE_uint64(arc_halve_after_visit_times);

namespace bds::dancedn::cloudfs {

class LRUSortedBlockEntriesTests : public ::testing::Test {
 public:
  void SetUp() override {
    bytestore::metrics_internal::InitFastMetrics();
    sorted_block_entries_ = new LRUSortedBlockEntries();
    block_orders_ = new std::unordered_map<uint64_t, uint64_t>();
    block_entries_ =
        new std::unordered_map<uint64_t, std::shared_ptr<BlockEntry>>();
    order_generator_ = 1;
  }
  void TearDown() override {
    delete sorted_block_entries_;
    delete block_orders_;
    delete block_entries_;
  }

 public:
  LRUSortedBlockEntries* sorted_block_entries_;
  std::unordered_map<uint64_t, uint64_t>* block_orders_;
  std::unordered_map<uint64_t, std::shared_ptr<BlockEntry>>* block_entries_;
  std::atomic<uint64_t> order_generator_;
};

TEST_F(LRUSortedBlockEntriesTests, SequentialAddSort) {
  LRUSortedBlockEntries sorted_entries;
  for (uint32_t i = 0; i < 1000; i++) {
    std::shared_ptr<ReplicaInfo> replica = std::make_shared<FinalizedReplica>(
        "BP-1-1", i, 1024, 1024, false, "storage_uuid", false, true,
        static_cast<int>(NamespaceType::TOS_MANAGED), 1);
    std::shared_ptr<BlockEntry> entry = std::make_shared<BlockEntry>(
        1, NamespaceType::TOS_MANAGED, nullptr, replica->GetBlockPoolID(),
        replica->GetBlockID());
    block_orders_->emplace(i, order_generator_.fetch_add(1));
    block_entries_->emplace(i, entry);
    sorted_entries.Emplace(entry);
  }
  auto&& iter = sorted_entries.Iterator();
  uint64_t last = 0;
  uint32_t count = 0;
  while (iter->Valid()) {
    auto&& entry = iter->Next();
    uint64_t order = block_orders_->at(entry->GetId().key3_);
    ASSERT_GT(order, last);
    count += 1;
    last = order;
  }
  sorted_entries.DestroyIterator(iter);
  ASSERT_EQ(count, 1000);
}

TEST_F(LRUSortedBlockEntriesTests, RandomAccess) {
  LRUSortedBlockEntries sorted_entries;
  for (uint32_t i = 0; i < 1000; i++) {
    std::shared_ptr<ReplicaInfo> replica = std::make_shared<FinalizedReplica>(
        "BP-1-1", i, 1024, 1024, false, "storage_uuid", false, true,
        static_cast<int>(NamespaceType::TOS_MANAGED), 1);
    std::shared_ptr<BlockEntry> entry = std::make_shared<BlockEntry>(
        1, NamespaceType::TOS_MANAGED, nullptr, replica->GetBlockPoolID(),
        replica->GetBlockID());
    block_orders_->emplace(i, order_generator_.fetch_add(1));
    block_entries_->emplace(i, entry);
    sorted_entries.Emplace(entry);
  }

  byte::Random64 random(byte::GetCurrentTimeInUs());

  for (uint32_t i = 0; i < 10000; i++) {
    uint32_t id = random.Next() % 1000;
    sorted_entries.Visit(block_entries_->at(id));
    block_orders_->at(id) = order_generator_.fetch_add(1);
  }
  auto&& iter = sorted_entries.Iterator();
  uint64_t last = 0;
  uint32_t count = 0;
  while (iter->Valid()) {
    auto&& entry = iter->Next();
    uint64_t order = block_orders_->at(entry->GetId().key3_);
    ASSERT_GT(order, last);
    count += 1;
    last = order;
  }
  sorted_entries.DestroyIterator(iter);
  ASSERT_EQ(count, 1000);
}

class ARCSortedBlockEntriesTests : public ::testing::Test {
 public:
  void SetUp() override {
    bytestore::metrics_internal::InitFastMetrics();
  }
  void TearDown() override {}
  void CheckGhostLinedBlockEntryList(GhostLinkedBaseEntryList* list,
                                     const std::vector<uint64_t>& data_ids,
                                     const std::vector<uint64_t>& ghost_ids) {
    list->DEBUG_print();
    std::vector<uint64_t> compare_data_ids;
    for (auto&& iter = list->begin(); iter != list->end(); iter = iter->next) {
      std::shared_ptr<BaseEntry> entry = list->ToEntry(iter);
      compare_data_ids.emplace_back(entry->GetId().key3_);
    }
    ASSERT_EQ(compare_data_ids, data_ids);
    for (uint64_t ghost_id : ghost_ids) {
      ASSERT_TRUE(list->FindInGhost(BlockEntry::ToEntryId("BP-1-1", ghost_id)));
    }
    std::cerr << "Pass check " << std::endl;
  }

 public:
  ARCSortedBlockEntries* sorted_block_entries_;
};

TEST_F(ARCSortedBlockEntriesTests, GhostLinkedBaseEntryList) {
  // Description:
  // data list | ghost list
  //
  // symbol | 's left side is ghost list, right side is data list.
  GhostLinkedBaseEntryList list;
  ASSERT_EQ(list.begin(), list.end());

  // 1 2 3 4 5 6 7 8 9 10 |
  for (uint32_t i = 1; i <= 10; i++) {
    std::shared_ptr<ReplicaInfo> replica = std::make_shared<FinalizedReplica>(
        "BP-1-1", i, 1024, 1024, false, "storage_uuid", false, true,
        static_cast<int>(NamespaceType::TOS_MANAGED), 1);
    std::shared_ptr<BlockEntry> entry = std::make_shared<BlockEntry>(
        1, NamespaceType::TOS_MANAGED, nullptr, replica->GetBlockPoolID(),
        replica->GetBlockID());
    list.Emplace(entry);
  }
  CheckGhostLinedBlockEntryList(&list, {1, 2, 3, 4, 5, 6, 7, 8, 9, 10}, {});

  // 4 5 6 7 8 9 10 | 3 2 1
  for (int i = 0; i < 3; i++) {
    auto&& node = list.begin();
    list.Delete(node);
  }
  CheckGhostLinedBlockEntryList(&list, {4, 5, 6, 7, 8, 9, 10}, {1, 2, 3});

  // 5 6 7 8 9 10 | 4 3
  FLAGS_arc_ghost_list_max_length = 2;
  {
    auto&& node = list.begin();
    std::cerr << "delete node " << node << std::endl;
    list.Delete(node);
  }
  CheckGhostLinedBlockEntryList(&list, {5, 6, 7, 8, 9, 10}, {3, 4});
  FLAGS_arc_ghost_list_max_length = 1000;

  // 6 7 8 9 10 5 | 4 3
  {
    auto&& node = list.begin();
    list.ReEnque(node);
  }
  CheckGhostLinedBlockEntryList(&list, {6, 7, 8, 9, 10, 5}, {3, 4});

  // 7 8 9 10 5 6 | 4 3
  {
    auto&& node = list.begin();
    list.Release(node);
    CheckGhostLinedBlockEntryList(&list, {7, 8, 9, 10, 5}, {3, 4});
    list.ReEnque(node);
    CheckGhostLinedBlockEntryList(&list, {7, 8, 9, 10, 5, 6}, {3, 4});
  }

  // 8 9 10 5 6 | 7
  FLAGS_arc_ghost_list_max_length = 2;
  {
    auto&& node = list.begin();
    list.Delete(node);
    CheckGhostLinedBlockEntryList(&list, {8, 9, 10, 5, 6}, {7});
  }
  FLAGS_arc_ghost_list_max_length = 1000000;
}

TEST_F(ARCSortedBlockEntriesTests, ARCSortedBlockEntries) {
  // Description:
  // fetch ghost list { fetch data list | active data list } active ghost list
  //
  // symbol | 's left side is fetch list, right side is active list.
  //
  // The item inside {} is data list and outside is ghost list.
  ARCSortedBlockEntries entries;
  {
    auto&& iter = entries.Iterator();
    ASSERT_FALSE(iter->Valid());
    entries.DestroyIterator(iter);
  }
  GhostLinkedBaseEntryList* fetch_list = entries.GetFetchList();
  GhostLinkedBaseEntryList* active_list = entries.GetActiveList();

  std::vector<std::shared_ptr<BlockEntry>> blocks;
  std::vector<std::shared_ptr<ReplicaInfo>> replicas;
  {
    std::shared_ptr<ReplicaInfo> replica = std::make_shared<FinalizedReplica>(
        "BP-1-1", 10000, 1024, 1024, false, "storage_uuid", false, true,
        static_cast<int>(NamespaceType::TOS_MANAGED), 1);
    std::shared_ptr<BlockEntry> entry = std::make_shared<BlockEntry>(
        1, NamespaceType::TOS_MANAGED, nullptr, replica->GetBlockPoolID(),
        replica->GetBlockID());
    blocks.emplace_back(entry);
    replicas.emplace_back(replica);
  }

  // { 1 2 3 4 5 6 | }
  for (uint32_t i = 1; i <= 6; i++) {
    std::shared_ptr<ReplicaInfo> replica = std::make_shared<FinalizedReplica>(
        "BP-1-1", i, 1024, 1024, false, "storage_uuid", false, true,
        static_cast<int>(NamespaceType::TOS_MANAGED), 1);
    std::shared_ptr<BlockEntry> entry = std::make_shared<BlockEntry>(
        1, NamespaceType::TOS_MANAGED, nullptr, replica->GetBlockPoolID(),
        replica->GetBlockID());
    entries.Emplace(entry);
    blocks.emplace_back(entry);
    replicas.emplace_back(replica);
  }
  CheckGhostLinedBlockEntryList(fetch_list, {1, 2, 3, 4, 5, 6}, {});
  CheckGhostLinedBlockEntryList(active_list, {}, {});

  // { 1 2 3 | 6 5 4}
  {
    entries.Visit(blocks[4]);
    entries.Visit(blocks[5]);
    entries.Visit(blocks[6]);
    CheckGhostLinedBlockEntryList(fetch_list, {1, 2, 3}, {});
    CheckGhostLinedBlockEntryList(active_list, {4, 5, 6}, {});
    ASSERT_EQ(entries.fetch_count(), 0);
    ASSERT_EQ(entries.active_count(), 0);
  }

  // { 1 2 3 | 5 6 4}
  {
    entries.Visit(blocks[5]);
    CheckGhostLinedBlockEntryList(fetch_list, {1, 2, 3}, {});
    CheckGhostLinedBlockEntryList(active_list, {4, 6, 5}, {});
    ASSERT_EQ(entries.fetch_count(), 0);
    ASSERT_EQ(entries.active_count(), 0);
  }

  // 3 { 1 2 | 5 6 } 4
  {
    entries.Remove(blocks[3]);
    entries.Remove(blocks[4]);
    CheckGhostLinedBlockEntryList(fetch_list, {1, 2}, {3});
    CheckGhostLinedBlockEntryList(active_list, {6, 5}, {4});
    ASSERT_EQ(entries.fetch_count(), 0);
    ASSERT_EQ(entries.active_count(), 0);
    entries.VisitMissing(BlockEntry::ToEntryId("BP-1-1", 3));
    entries.VisitMissing(BlockEntry::ToEntryId("BP-1-1", 4));
    CheckGhostLinedBlockEntryList(fetch_list, {1, 2}, {3});
    CheckGhostLinedBlockEntryList(active_list, {6, 5}, {4});
    ASSERT_EQ(entries.fetch_count(), 1);
    ASSERT_EQ(entries.active_count(), 1);
  }

  // { 1 2 3 | 3 5 6 } 4
  {
    blocks[3] = std::make_shared<BlockEntry>(
        1, NamespaceType::TOS_MANAGED, nullptr, replicas[3]->GetBlockPoolID(),
        replicas[3]->GetBlockID());
    entries.Emplace(blocks[3]);
    CheckGhostLinedBlockEntryList(fetch_list, {1, 2, 3}, {});
    CheckGhostLinedBlockEntryList(active_list, {6, 5}, {4});
    ASSERT_EQ(entries.fetch_count(), 1);
    ASSERT_EQ(entries.active_count(), 1);
  }

  // 8 { 1 2 3 7 9 10 | 5 6 } 4
  {
    for (uint32_t i = 7; i <= 10; i++) {
      std::shared_ptr<ReplicaInfo> replica = std::make_shared<FinalizedReplica>(
          "BP-1-1", i, 1024, 1024, false, "storage_uuid", false, true,
          static_cast<int>(NamespaceType::TOS_MANAGED), 1);
      std::shared_ptr<BlockEntry> entry = std::make_shared<BlockEntry>(
          1, NamespaceType::TOS_MANAGED, nullptr, replica->GetBlockPoolID(),
          replica->GetBlockID());
      entries.Emplace(entry);
      blocks.emplace_back(entry);
      replicas.emplace_back(replica);
    }
    entries.Remove(blocks[8]);
    entries.VisitMissing(BlockEntry::ToEntryId("BP-1-1", 8));
    CheckGhostLinedBlockEntryList(fetch_list, {1, 2, 3, 7, 9, 10}, {8});
    CheckGhostLinedBlockEntryList(active_list, {6, 5}, {4});
    ASSERT_EQ(entries.fetch_count(), 2);
    ASSERT_EQ(entries.active_count(), 1);
  }

  auto&& iter = entries.Iterator();
  {
    ASSERT_TRUE(iter->Valid());
    std::shared_ptr<BaseEntry> entry = iter->Next();
    ASSERT_EQ(entry->GetId().key3_, 6);

    ASSERT_TRUE(iter->Valid());
    entry = iter->Next();
    ASSERT_EQ(entry->GetId().key3_, 1);

    ASSERT_TRUE(iter->Valid());
    entry = iter->Next();
    ASSERT_EQ(entry->GetId().key3_, 5);

    ASSERT_TRUE(iter->Valid());
    entry = iter->Next();
    ASSERT_EQ(entry->GetId().key3_, 2);
    entries.Remove(entry);
    CheckGhostLinedBlockEntryList(fetch_list, {1, 3, 7, 9, 10}, {8, 2});
    CheckGhostLinedBlockEntryList(active_list, {6, 5}, {4});
    ASSERT_TRUE(iter->Valid());
    entry = iter->Next();
    ASSERT_EQ(entry->GetId().key3_, 3);
    ASSERT_TRUE(iter->Valid());
    entry = iter->Next();
    ASSERT_EQ(entry->GetId().key3_, 7);
    ASSERT_TRUE(iter->Valid());
    entry = iter->Next();
    ASSERT_EQ(entry->GetId().key3_, 9);
    ASSERT_TRUE(iter->Valid());
    entry = iter->Next();
    ASSERT_EQ(entry->GetId().key3_, 10);
    ASSERT_FALSE(iter->Valid());
  }
  entries.DestroyIterator(iter);
}

void TestSortedBlockEntriesConcurrentIO(SortedBlockEntries* sort_entries) {
  std::vector<std::shared_ptr<BlockEntry>> stub_entries;
  int entry_cnt = 10000;
  stub_entries.reserve(entry_cnt);
  for (int i = 0; i < entry_cnt; i++) {
    std::string bpid = byte::StringPrint(
        "BP-%llu-%llu", static_cast<uint64_t>(i), static_cast<uint64_t>(i));
    std::shared_ptr<ReplicaBeingWritten> replica =
        std::make_shared<ReplicaBeingWritten>(
            bpid, i, i, false, "storage_uuid" + std::to_string(i), true, true,
            0, false);
    std::shared_ptr<BlockEntry> entry = std::make_shared<BlockEntry>(
        1, NamespaceType::TOS_MANAGED, nullptr, replica->GetBlockPoolID(),
        replica->GetBlockID());
    stub_entries.push_back(entry);
  }
  for (int i = 0; i < entry_cnt; i++) {
    sort_entries->Emplace(stub_entries[i]);
  }
  std::vector<std::shared_ptr<BlockEntry>> new_stub_entries;
  for (int i = 0; i < 100; i++) {
    std::string bpid = byte::StringPrint(
        "BP-%llu-%llu", static_cast<uint64_t>(i), static_cast<uint64_t>(i + 1));
    std::shared_ptr<ReplicaBeingWritten> replica =
        std::make_shared<ReplicaBeingWritten>(
            bpid, i, i, false, "new_storage_uuid" + std::to_string(i), true,
            true, 0, false);
    std::shared_ptr<BlockEntry> entry = std::make_shared<BlockEntry>(
        1, NamespaceType::TOS_MANAGED, nullptr, replica->GetBlockPoolID(),
        replica->GetBlockID());
    stub_entries.push_back(entry);
  }
  std::vector<std::thread> threads;
  int test_cnt = 1000;
  bytestore::SpinLock remove_lock;
  std::set<int> removed;
  threads.push_back(std::thread([&sort_entries, &new_stub_entries]() {
    for (size_t i = 0; i < new_stub_entries.size(); i++) {
      sort_entries->Emplace(new_stub_entries[i]);
      byte::ThisThread::SleepInMs(10);
    }
  }));
  // Visit
  threads.push_back(std::thread([&sort_entries, &stub_entries, &test_cnt,
                                 &entry_cnt, &removed, &remove_lock]() {
    for (int i = 0; i < test_cnt; i++) {
      int idx = random() % entry_cnt;
      bool can_visit = false;
      {
        bytestore::ScopedSpinLock lock(&remove_lock);
        can_visit = (removed.find(idx) == removed.end());
      }
      if (can_visit) {
        sort_entries->Visit(stub_entries[idx]);
      }
      byte::ThisThread::SleepInMs(10);
    }
  }));
  // Remove
  threads.push_back(std::thread([&sort_entries, &stub_entries, &test_cnt,
                                 &entry_cnt, &removed, &remove_lock]() {
    for (int i = 0; i < test_cnt; i++) {
      int idx = random() % entry_cnt;
      {
        bytestore::ScopedSpinLock lock(&remove_lock);
        removed.insert(idx);
      }
      sort_entries->Remove(stub_entries[idx]);
      byte::ThisThread::SleepInMs(10);
    }
  }));
  // Iterator
  for (int i = 0; i < 10; i++) {
    threads.push_back(std::thread([&sort_entries]() {
      SortedBlockEntries::Iter* iter = sort_entries->Iterator();
      while (iter != nullptr && iter->Valid()) {
        std::shared_ptr<BaseEntry> entry = iter->Next();
        int sleep_ms = random() % 5;
        byte::ThisThread::SleepInMs(sleep_ms);
      }
      sort_entries->DestroyIterator(iter);
    }));
  }
  for (auto& t : threads) {
    t.join();
  }
}

// Test concurrent IO and no coredump
TEST(SortedBlockEntriesTest, ArcConcurrentIOCheck) {
  LRUSortedBlockEntries sort_entries;
  TestSortedBlockEntriesConcurrentIO(&sort_entries);
}

// Test concurrent IO and no coredump
TEST(SortedBlockEntriesTest, LruConcurrentIOCheck) {
  ARCSortedBlockEntries sort_entries;
  TestSortedBlockEntriesConcurrentIO(&sort_entries);
}

}  // namespace bds::dancedn::cloudfs
