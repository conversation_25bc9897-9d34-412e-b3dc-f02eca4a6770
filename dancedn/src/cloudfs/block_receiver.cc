// Copyright (c) 2019-present, ByteDance Inc. All rights reserved.

#include "cloudfs/block_receiver.h"

#include <cassert>
#include <cstdint>
#include <memory>
#include <utility>

#include "byte/base/atomic.h"
#include "byte/string/format/print.h"
#include "byte/system/timestamp.h"
#include "cloudfs/block.h"
#include "cloudfs/block_construction_stage.h"
#include "cloudfs/caching_strategy.h"
#include "cloudfs/constants.h"
#include "cloudfs/data_checksum.h"
#include "cloudfs/data_xceiver.h"
#include "cloudfs/datanode.h"
#include "cloudfs/datanode_config.h"
#include "cloudfs/datanode_id.h"
#include "cloudfs/datanode_info.h"
#include "cloudfs/datanode_registration.h"
#include "cloudfs/datanode_stream_server.h"
#include "cloudfs/exceptions.h"
#include "cloudfs/extended_block.h"
#include "cloudfs/io/address.h"
#include "cloudfs/io/connection.h"
#include "cloudfs/io/define.h"
#include "cloudfs/io/io_buf.h"
#include "cloudfs/metrics.h"
#include "cloudfs/opstats/op_stats.h"
#include "cloudfs/packet_header.h"
#include "cloudfs/packet_receiver.h"
#include "cloudfs/packet_responder.h"
#include "cloudfs/replica_in_pipeline.h"
#include "cloudfs/replica_info.h"
#include "cloudfs/store.h"
#include "cloudfs/store/unified_block_store.h"
#include "cloudfs/util.h"
#include "common/media_flags.h"
#include "common/metrics.h"
#include "gflags/gflags.h"
#include "gflags/gflags_declare.h"

DECLARE_uint32(bytestore_hdfs_socket_timeout);
DECLARE_MEDIA_FLAG_uint32(bytestore_hdfs_slow_io_warning_threshold_ms);
DECLARE_uint32(bytestore_hdfs_xceiver_stop_timeout_ms);
DECLARE_uint64(bytestore_cfs_idle_write_interval);
DECLARE_uint32(bytestore_hdfs_checksum_mode);
DECLARE_uint64(bytestore_hdfs_tiering_freeze_size);
DECLARE_string(bytestore_cfs_location_az);

namespace bds::dancedn::cloudfs {

DECLARE_HISTOGRAM_METRIC(hdfs_slow_log_send_packet_mirror_ms);
DECLARE_HISTOGRAM_METRIC(hdfs_slow_log_write_packet_ms);
DECLARE_COUNTER_METRIC(hdfs_sync_block_num);

BlockReceiver::BlockReceiver(
    DataXceiver* xceiver, ExtendedBlock* block, io::Connection* input_conn,
    const std::string& in_addr, const std::string& my_addr,
    const BlockConstructionStage stage, const std::string& client_name,
    DatanodeInfo* src_datanode, DataNode* datanode,
    DataChecksum* requested_checksum, bytestore::IOPriority priority,
    const TraceBaggage& trace_baggage)
    : xceiver_(xceiver->GetPtr()),
      block_(block),
      input_conn_(input_conn),
      mirror_conn_(nullptr),
      in_addr_(in_addr),
      my_addr_(my_addr),
      src_datanode_(src_datanode),
      dn_(datanode),
      client_name_(client_name),
      is_datanode_(false),
      stage_(stage),
      client_checksum_(requested_checksum),
      disk_checksum_(nullptr),
      need_checksum_translation_(false),
      mirror_error_(false),
      responder_(nullptr),
      packet_receiver_(nullptr),
      is_close_(false),
      op_key_(),
      disk_(nullptr),
      ref_(nullptr),
      priority_(priority),
      received_size_(0),
      trace_baggage_(trace_baggage),
      is_mirroring_(false),
      user_priority_(-1),
      wait_time_ms_for_throttle_(0) {}

BlockReceiver::~BlockReceiver() {
  // delete xceiver_ by XceiverManager
  // delete block_ by DataXceiver
  // delete input_conn_ by DataXceiver
  // delete mirror_conn_ by DataXceiver
  // delete reply_conn_ by DataXceiver
  // delete dn_ by ChunkServer
  // delete storage_ by DataNode
  // delete replica_info_ by Store
  // delete client_checksum_ by DataXceiver
  delete disk_checksum_;
  packet_receiver_->Stop();
  packet_receiver_->Join();
  delete packet_receiver_;
  delete responder_;
}

exceptions::Exception BlockReceiver::CreateNewBlockRecevier(
    DataXceiver* xceiver, ExtendedBlock* block, StorageType storage_type,
    int32_t resident_time, io::Connection* input_conn,
    const std::string& in_addr, const std::string& my_addr,
    const BlockConstructionStage stage, uint64_t new_gs,
    uint64_t min_bytes_rcvd, uint64_t max_bytes_rcvd,
    const std::string& client_name, DatanodeInfo* src_datanode,
    DataNode* datanode, DataChecksum* requested_checksum,
    CachingStrategy* caching_strategy, bool allow_lazy_persist,
    bytestore::IOPriority priority, std::string user, int user_priority,
    const TraceBaggage& trace_baggage, BlockReceiver** receiver) {
  if (DataNode::prevent_create_) {
    return exceptions::Exception(exceptions::kPreventWrite,
                                 "Prevent to receive new block.");
  }
  std::unique_ptr<BlockReceiver> res(new BlockReceiver(
      xceiver, block, input_conn, in_addr, my_addr, stage, client_name,
      src_datanode, datanode, requested_checksum, priority, trace_baggage));
  res->packet_receiver_ = new PacketReceiver();
  res->storage_ = datanode->GetStorage()->GetLocalStore();
  res->is_datanode_ = client_name.size() == 0;
  res->is_client_ = !res->is_datanode_;
  res->is_transfer_ = stage.Equal(BlockConstructionStage::TRANSFER_RBW) ||
                      stage.Equal(BlockConstructionStage::TRANSFER_FINALIZED);

  int64_t read_timeout = FLAGS_bytestore_hdfs_socket_timeout;
  res->response_interval_ = static_cast<int64_t>(read_timeout * 0.5);
  res->max_send_idle_time_ = static_cast<int64_t>(read_timeout * 0.9);
  res->last_sent_time_ = byte::GetCurrentTimeInMs();
  res->last_replace_time_ = byte::GetCurrentTimeInMs();
  res->user_priority_ = user_priority;
  LOG(DEBUG) << "block: " << block->ToString()
             << " isClient:" << res->is_client_ << " clientname:" << client_name
             << " isDataNode:" << res->is_datanode_ << " src_datanode:"
             << (src_datanode != nullptr ? src_datanode->ToString() : "")
             << " in_addr:" << in_addr << " my_addr:" << my_addr
             << " stage:" << unsigned(stage.GetStage());

  bool is_create = res->is_datanode_ || res->is_transfer_ ||
                   stage.Equal(BlockConstructionStage::PIPELINE_SETUP_CREATE);
  // local disk create file
  exceptions::Exception e;
  std::string media_str;
  LOG(INFO) << "Created block receiver: " << block->ToString()
            << " user_priority:" << byte::IntegerToString(res->user_priority_)
            << " is_datanode:" << res->IsDatanodeString()
            << " stage:" << stage.ToString();
  // local disk create file
  if (res->is_datanode_) {
    e = res->storage_->CreateTemporary(storage_type, block, &res->replica_info_,
                                       resident_time, priority);
    if (!e.OK()) goto FINISH;
    datanode->NotifyNamenodeReceivingBlock(
        block, res->replica_info_->GetStorageUuid());
  } else {
    switch (stage.GetStage()) {
      case Stage::PIPELINE_SETUP_CREATE:
        e = res->storage_->CreateRbw(storage_type, block, allow_lazy_persist,
                                     &res->replica_info_, resident_time,
                                     priority);
        if (!e.OK()) goto FINISH;
        datanode->NotifyNamenodeReceivingBlock(
            block, res->replica_info_->GetStorageUuid());
        break;
      case Stage::PIPELINE_SETUP_STREAMING_RECOVERY:
        e = res->storage_->RecoverRbw(block, new_gs, min_bytes_rcvd,
                                      max_bytes_rcvd, &res->replica_info_);
        if (!e.OK()) goto FINISH;
        block->SetGS(new_gs);
        break;
      // in order to speed init chunkserver
      case Stage::PIPELINE_SETUP_APPEND:
        e = res->storage_->Append(block, new_gs, min_bytes_rcvd,
                                  &res->replica_info_, priority);
        if (!e.OK()) goto FINISH;
        block->SetGS(new_gs);
        datanode->NotifyNamenodeReceivingBlock(
            block, res->replica_info_->GetStorageUuid());
        break;
      case Stage::PIPELINE_SETUP_APPEND_RECOVERY:
        e = res->storage_->RecoverAppend(block, new_gs, min_bytes_rcvd,
                                         &res->replica_info_);
        if (!e.OK()) goto FINISH;
        block->SetGS(new_gs);
        datanode->NotifyNamenodeReceivingBlock(
            block, res->replica_info_->GetStorageUuid());
        break;
      case Stage::TRANSFER_RBW:
      case Stage::TRANSFER_FINALIZED:
        e = res->storage_->CreateTemporary(
            storage_type, block, &res->replica_info_, resident_time, priority);
        if (!e.OK()) goto FINISH;
        datanode->NotifyNamenodeReceivingBlock(
            block, res->replica_info_->GetStorageUuid());
        break;
      default:
        std::string msg = byte::StringPrint(
            "Unsupported stage %s while receiving "
            "block %s from %s",
            stage.ToString(), block->ToString(), in_addr);
        e = exceptions::Exception(exceptions::E::kIOException, msg);
        goto FINISH;
    }
  }

  res->disk_ = datanode->GetStorage()->GetDisk(res->replica_info_->GetDiskId());

  if (res->disk_ == nullptr) {
    std::string msg =
        byte::StringPrint("Disk id not found while check files of %s",
                          res->replica_info_->GetBlock()->ToString());
    e = exceptions::Exception(exceptions::E::kIOException, msg);
    goto FINISH;
  }
  res->datanode_slow_log_threshold_ms_ = MFLAGS(
      bytestore_hdfs_slow_io_warning_threshold_ms, res->disk_->GetMediaType());
  media_str = MediaTypeToShortString(res->disk_->GetMediaType());
  res->tags_ = {{"user", res->user_},
                {"user_priority", byte::IntegerToString(res->user_priority_)},
                {"bpid", block->GetBlockPoolID()},
                {"is_datanode", res->IsDatanodeString()},
                {"stage", res->GetStageString()},
                {"media_type", media_str},
                {"disk_id", byte::IntegerToString(res->disk_->GetDiskId())}};
  res->ref_.reset(new bytestore::chunkserver::DiskRef(res->disk_));
  if (res->disk_->IsHangUp()) {
    std::string msg =
        byte::StringPrint("Disk Closed while check files of %s",
                          res->replica_info_->GetBlock()->ToString());
    e = exceptions::Exception(exceptions::E::kIOException, msg);
    goto FINISH;
  }
  e = res->replica_info_->ResetWriter(
      xceiver, FLAGS_bytestore_hdfs_xceiver_stop_timeout_ms);
  if (!e.OK()) {
    LOG(DEBUG) << "reset write failed";
    goto FINISH;
  }
  // res->drop_cache_behind_writes_ = caching_strategy->GetDropBehind();
  // res->sync_behind_writes_ =
  // datanode->GetDataNodeConfig()->GetSyncBehindWrites();
  // res->sync_behind_write_in_background_ =
  //     datanode->GetDataNodeConfig()->GetSyncBehindWriteInBackground();

  if (res->client_checksum_ == nullptr) {
    std::string msg = byte::StringPrint("Unsupported checksum when ParseProto");
    e = exceptions::Exception(exceptions::E::kChecksumException, msg);
    goto FINISH;
  }

  if (is_create) {
    res->disk_checksum_ = DataChecksum::NewDataChecksum(
        res->client_checksum_->GetType(),
        res->client_checksum_->GetBytesPerChecksum());
  } else {
    e = res->storage_->ReadDiskChecksum(block, &res->disk_checksum_);
    if (!e.OK()) goto FINISH;
  }
  res->should_write_checksum_ = res->replica_info_->ChecksumEnabled();
  if (res->should_write_checksum_ &&
      (res->disk_checksum_->GetBytesPerChecksum() !=
       res->client_checksum_->GetBytesPerChecksum())) {
    std::string msg = byte::StringPrint(
        "Client requested checksum %d when appending to an "
        "existing block with different chunk size: %d",
        res->client_checksum_->GetBytesPerChecksum(),
        res->disk_checksum_->GetBytesPerChecksum());
    e = exceptions::Exception(exceptions::E::kChecksumException, msg);
    goto FINISH;
  }
  res->need_checksum_translation_ =
      !res->client_checksum_->Equal(res->disk_checksum_);
  res->bytes_per_checksum_ = res->disk_checksum_->GetBytesPerChecksum();
  res->checksum_size_ = res->disk_checksum_->GetChecksumSize();
  if (is_create && res->should_write_checksum_) {
    e = res->storage_->WriteBlockMetaHeader(block, res->disk_checksum_,
                                            storage_type, resident_time);
    if (!e.OK()) goto FINISH;
  }

  *receiver = res.release();

FINISH:
  if (e.GetE() == exceptions::E::kIOException ||
      e.GetE() == exceptions::E::kChecksumException) {
    LOG(DEBUG) << "get IO exception";
    if (res->is_datanode_) {
      auto e2 = res->storage_->UnFinalizeBlock(block, priority);
      if (!e2.OK()) return e2;
    }
    if (!e.GetMessage().empty() &&
        e.GetMessage().substr(0, DISK_ERROR_LENGTH) == DISK_ERROR_STRING) {
      LOG(WARNING) << "IOException in BlockReceiver constructor. "
                   << "Cause is " << e.GetMessage();
      res->dn_->CheckDiskErrorAsync();
    }
  }
  return e;
}

exceptions::Exception BlockReceiver::ReceiveBlock(
    io::Connection* mirror_conn, io::Connection* reply_conn,
    const std::string& mirr_addr, std::vector<DatanodeInfo*> downstreams,
    bool is_replace_block) {
  mirror_conn_ = mirror_conn;
  reply_conn_ = reply_conn;
  mirror_addr_ = mirr_addr;
  is_replace_ = is_replace_block;

  bool responder_closed = false;

  if (is_client_ && !is_transfer_) {
    LOG(DEBUG) << "start packet responder";
    responder_ = new PacketResponder(this, reply_conn, mirror_conn, downstreams,
                                     trace_baggage_);
    responder_->Start();
  }
  packet_receiver_->Init(input_conn_, mirror_conn);
  packet_receiver_->Start();

  bool active = false;
  uint64_t last_write_time = -1;
  int64_t rlen = 0;
  exceptions::Exception e;
  uint64_t freeze_size = FLAGS_bytestore_hdfs_tiering_freeze_size;
  LOG(DEBUG) << "isClosed:" << IsClosed()
             << " xceiver_isStopped:" << xceiver_->IsStopped()
             << " xceiver_isInterrupted:" << xceiver_->IsInterrupted();
  while (!IsClosed() && !xceiver_->IsStopped() && !xceiver_->IsInterrupted() &&
         rlen >= 0) {
    if (DataNode::prevent_write_) {
      e = exceptions::Exception(exceptions::kPreventWrite,
                                "Prevent to receive new packet.");
      goto LogException;
    }
    e = ReceivePacket(&rlen, freeze_size);
    if (e.GetE() == exceptions::E::kReplicaFrozenException) {
      break;
    }
    if (is_client_ && rlen > 0 && mirror_conn_ == nullptr) {
      dn_->NamespaceAcquireWrite(block_->GetBlockPoolID(), rlen);
    }
    if (rlen > 0) {
      LimitObject limit_obj =
          is_datanode_ ? LimitObject::kTransferDest : LimitObject::kClientWrite;
      uint32_t disk_id = replica_info_->GetDiskId();
      bytestore::IOPriority pri = is_datanode_ ? bytestore::PRIORITY_BEST_EFFORT
                                               : bytestore::PRIORITY_ELASTIC;
      dn_->AcquireLocalToken(disk_id, limit_obj, rlen, pri);
      received_size_ += rlen;
    }

    if (rlen > 0) {
      if (!active) {
        active = true;
        DataXceiver::IncActiveWriteNumber();
      }
      last_write_time = byte::GetCurrentTimeInMs();
    } else if (active && byte::GetCurrentTimeInMs() - last_write_time >
                             FLAGS_bytestore_cfs_idle_write_interval) {
      active = false;
      DataXceiver::DecActiveWriteNumber();
    }
    if (!e.OK()) {
      LOG(ERROR) << "receiver packet failed, exception:" << e.ToString();
      goto LogException;
    }
  }

  if (xceiver_->IsStopped() || xceiver_->IsInterrupted()) {
    LOG(WARNING) << "xceiver is stopped or interrupted during receiving block:"
                 << block_->ToString();
    goto LogException;
  }

  if (responder_ != nullptr) {  // is_client_ && !is_transfer_
    // Wait for all acks responded or datanode restarting
    responder_->Close();
    responder_closed = true;
  } else {  // is_datanode_ || is_transfer_
    LOG(DEBUG) << "is_datanode:" << is_datanode_
               << " is_transfer:" << is_transfer_;
    block_->SetNumBytes(replica_info_->GetNumBytes());
    if (stage_.Equal(BlockConstructionStage::TRANSFER_RBW)) {
      std::shared_ptr<ReplicaInPipeline> replica_info;
      e = storage_->ConvertTemporaryToRbw(block_, &replica_info, priority_);
    } else {
      e = storage_->FinalizeBlock(block_, priority_);
      if (e.OK() && is_datanode_ && is_transfer_ &&
          block_->IsPersisted().is_true()) {
        storage_->SubmitMarkReplicaEvictableBG(
            block_->GetBlockPoolID(), block_->GetBlockID(),
            replica_info_->GetStorageClass(), block_->IsPin());
      }
    }
  }

LogException:
  LOG(DEBUG) << "exception:" << e.ToString();
  if (active) {
    DataXceiver::DecActiveWriteNumber();
  }
  if (e.GetE() == exceptions::E::kIOException ||
      e.GetE() == exceptions::E::kChecksumException) {
    LOG(ERROR) << e.ToString() << " for " << block_->ToString();
  }

  if (responder_closed) {
    LOG(DEBUG) << "responder is closed";
    goto ClearResponder;
  }

  if (is_datanode_) {
    LOG(DEBUG) << "unfinalize block:" << block_->ToString();
    storage_->UnFinalizeBlock(block_, priority_);
  }

ClearResponder:
  if (responder_ != nullptr) {
    // join() on the responder should timeout a bit earlier than the
    // configured deadline. Otherwise, the join() on this thread will
    // likely timeout as well.
    uint64_t join_timeout = FLAGS_bytestore_hdfs_xceiver_stop_timeout_ms;
    join_timeout = join_timeout > 1 ? join_timeout * 8 / 10 : join_timeout;
    LOG(DEBUG) << "join_timeout:" << join_timeout;
    responder_->Stop();
    bool is_timeout = !responder_->WaitFinished(join_timeout);
    if (is_timeout) {
      std::string msg = byte::StringPrint(
          "Join on responder thread %s timed out", responder_->ToString());
      LOG(WARNING) << msg;
      e = exceptions::Exception(exceptions::E::kIOException, msg);
    } else if (xceiver_->IsInterrupted() || xceiver_->IsStopped()) {
      LOG(DEBUG) << "xceiver isInterrupted:" << xceiver_->IsInterrupted()
                 << "xceiver isStopped:" << xceiver_->IsStopped();
      responder_->Stop();
      e = exceptions::Exception(exceptions::E::kIOException,
                                "Interrupted receiveBlock");
    }
  }
  return e;
}

exceptions::Exception BlockReceiver::ReceivePacket(int64_t* len,
                                                   uint64_t freeze_size) {
  PacketTimeTrace time_trace;
  DURATION_START_NS(receive_packet);
  auto e = packet_receiver_->ReceiveNextPacket();
  if (!e.OK()) {
    LOG(ERROR) << "receive next packet error, exception:" << e.ToString();
    input_conn_->ShutdownConnection();
    return e;
  }
  time_trace.recv_time_ns = DURATION_END_NS(receive_packet);
  const char* type = (mirror_conn_ != nullptr) ? "upstream" : "downstream";
  METRICS_block_receiver_receive_packet_latency->GetMetric({{"type", type}})
      ->Set(time_trace.recv_time_ns / 1000);
  int64_t start_time_ns = byte::GetCurrentTimeInNs();

  auto header = packet_receiver_->GetPacketHeader();
  LOG(DEBUG) << "Receiving one packet for block " << block_->ToString() << ": "
             << header->ToString();

  if ((uint64_t)header->GetOffsetInBlock() > replica_info_->GetNumBytes()) {
    std::string msg = byte::StringPrint(
        "Received an out-of-sequence packet for %s"
        "from %s at offset %ld . Expecting packet starting at %ld",
        block_->ToString(), in_addr_, header->GetOffsetInBlock(),
        replica_info_->GetNumBytes());
    return exceptions::Exception(exceptions::E::kIOException, msg);
  }
  if (header->GetDataLength() < 0) {
    std::string msg = byte::StringPrint(
        "Got wrong length during writeBlock(%s) from %s "
        "at offset %ld : %ld",
        block_->ToString(), in_addr_, header->GetOffsetInBlock(),
        header->GetDataLength());
    return exceptions::Exception(exceptions::E::kIOException, msg);
  }
  if (header->GetOffsetInBlock() < 0) {
    std::string msg = byte::StringPrint(
        "Got wrong offset in block during writeBlock(%s)"
        " from %s at offset %ld : %ld",
        block_->ToString(), in_addr_, header->GetOffsetInBlock(),
        header->GetDataLength());
    return exceptions::Exception(exceptions::E::kIOException, msg);
  }

  uint64_t offset_in_block = header->GetOffsetInBlock();
  int64_t seqno = header->GetSeqno();
  bool last_packet_in_block = header->IsLastPacketInBlock();
  uint32_t data_len = header->GetDataLength();
  bool sync_block = header->GetSyncBlock();
  bytestore::Tags tags =
      TraceBaggage::ToMetricTag(trace_baggage_.GetBaggages());
  tags.emplace_back(
      std::pair<std::string, std::string>("bpid", block_->GetBlockPoolID()));
  tags.emplace_back(std::pair<std::string, std::string>("rpc", "hrpc"));
  tags.emplace_back(std::pair<std::string, std::string>("type", "WriteBlock"));
  std::string src_az =
      source_location_.az().empty() ? "NA" : source_location_.az();
  std::string dst_az = FLAGS_bytestore_cfs_location_az;
  tags.emplace_back(std::pair<std::string, std::string>("srcAZ", src_az));
  tags.emplace_back(std::pair<std::string, std::string>("dstAZ", dst_az));
  METRICS_block_receiver_receive_bytes->GetMetric(tags)->Add(data_len);
  METRICS_dancedn_request_throughput->GetMetric(tags)->Add(data_len);

  uint64_t head_of_packet = offset_in_block;
  offset_in_block += data_len;
  // check if a frozen replica reply is required
  // iff responder exists
  // and this is not the last empty packet
  // and bytes on disk exceeds freeze_size
  bool need_reply_replica_frozen =
      responder_ != nullptr && !last_packet_in_block && data_len > 0 &&
      replica_info_->GetPlacementType() ==
          bytestore::PLM_STORAGE_TIERED_NVME_SSD_HDD &&
      freeze_size > 0 && replica_info_->GetBytesOnDisk() >= freeze_size;

  if (!need_reply_replica_frozen) {
    if (replica_info_->GetNumBytes() < offset_in_block) {
      replica_info_->SetNumBytes(offset_in_block);
    }

    if (responder_ != nullptr && !sync_block && !ShouldVerifyChecksum()) {
      responder_->Enqueue(seqno, last_packet_in_block, offset_in_block,
                          Status::SUCCESS, start_time_ns);
    }
  }

  // First, write packet to the mrrior
  if (mirror_conn_ != nullptr && !HasMirrorError()) {
    DURATION_START_NS(mirror_packet);
    byte::AtomicSet(&is_mirroring_, true);
    auto e = packet_receiver_->MirrorPacketTo(mirror_conn_);
    byte::AtomicSet(&is_mirroring_, false);
    if (!e.OK()) {
      e = HandleMirrorOutError(e);
      if (!e.OK()) {
        return e;
      }
    }
    time_trace.mirror_time_ns = DURATION_END_NS(mirror_packet);
    METRICS_block_receiver_mirror_packet_latency->Set(
        time_trace.mirror_time_ns / 1000);
    SetLastSentTime(byte::GetCurrentTimeInMs());
    if (static_cast<uint64_t>(time_trace.mirror_time_ns) / 1000 / 1000 >
        datanode_slow_log_threshold_ms_) {
      LOG(WARNING) << "Slow BlockReceiver write packet to mirror took "
                   << time_trace.mirror_time_ns / 1000 / 1000
                   << "ms (threshold=" << datanode_slow_log_threshold_ms_
                   << "ms)"
                   << ", block: " << block_->ToString() << ", to mirror "
                   << mirror_addr_;
    }
    LOG(DEBUG) << "send packet to next dn success";
  }

  if (need_reply_replica_frozen) {
    responder_->Enqueue(seqno, last_packet_in_block, offset_in_block,
                        Status::FREEZE, start_time_ns);
    std::string msg = byte::StringPrint(
        "bytes_on_disk exceeds freeze_size, block:%s", block_->ToString());
    return exceptions::Exception(exceptions::E::kReplicaFrozenException, msg);
  }

  auto data = packet_receiver_->GetDataSlice();
  auto checksum = packet_receiver_->GetChecksumSlice();
  if (last_packet_in_block || data_len == 0) {
    LOG(DEBUG) << "Receiving last packet in block " << block_->ToString();
    if (sync_block) {
      DURATION_START_NS(sync_block);
      METRICS_hdfs_sync_block_num->Increment();
      LOG(DEBUG) << "SyncBlock for " << block_->ToString();
      auto e = storage_->SyncBlock(block_, 0, priority_);
      if (!e.OK()) {
        return e;
      }
      time_trace.sync_time_ns = DURATION_END_NS(sync_block);
      METRICS_block_receiver_sync_block_latency->Set(time_trace.sync_time_ns /
                                                     1000);
    }
  } else {
    exceptions::Exception e;
    io::IOChunk* csum_calculated =
        nullptr;  // used when original checksum is invalid
    do {
      uint32_t checksum_len = client_checksum_->GetChecksumSize(data_len);
      uint32_t checksum_received_len = checksum->Length();

      if (checksum_received_len > 0 && checksum_received_len != checksum_len) {
        std::string msg = byte::StringPrint(
            "Invalid checksum length: "
            "received length is %d but expected length is %d",
            checksum_received_len, checksum_len);
        e = exceptions::Exception(exceptions::E::kIOException, msg);
        break;
      }

      if (checksum_received_len > 0 && ShouldVerifyChecksum()) {
        e = VerifyChecksum(data, checksum);
        if (!e.OK()) {
          if (responder_ != nullptr) {
            responder_->Enqueue(seqno, last_packet_in_block, offset_in_block,
                                Status::ERROR_CHECKSUM, start_time_ns);
            // Wait until the responder sends back the response
            // and interrupt this thread.
            byte::ThisThread::SleepInSeconds(3);
          }
          break;
        }

        // TODO(zhouhan): Test translate when DN support more checksums
        if (need_checksum_translation_ && should_write_checksum_) {
          csum_calculated =
              new io::IOChunk(disk_checksum_->GetChecksumSize(data_len));
          checksum = csum_calculated;
          TranslateChecksum(data, checksum);
        }
      }

      if (checksum_received_len == 0 && should_write_checksum_) {
        // checksum is missing, need to calculate it
        csum_calculated =
            new io::IOChunk(disk_checksum_->GetChecksumSize(data_len));
        checksum = csum_calculated;
        e = disk_checksum_->CalculateChecksum(data, checksum);
        if (!e.OK()) {
          if (responder_ != nullptr) {
            responder_->Enqueue(seqno, last_packet_in_block, offset_in_block,
                                Status::ERROR_CHECKSUM, start_time_ns);
            byte::ThisThread::SleepInSeconds(3);
          }
          break;
        }
      }

      // by this point, the data in the buffer uses the disk checksum
      uint32_t disk_len = replica_info_->GetBytesOnDisk();
      LOG(DEBUG) << "disk_len:" << disk_len
                 << " head_of_packet:" << head_of_packet
                 << " offset_in_block:" << offset_in_block;
      if (disk_len >= offset_in_block) break;

      // Normally the beginning of an incoming packet is aligned with the
      // existing data on disk. If the beginning packet data offset is not
      // checksum chunk aligned, the end of packet will not go beyond the
      // next chunk boundary.
      // When a failure-recovery is involved, the client state and the
      // the datanode state may not exactly agree. I.e. the client may
      // resend part of data that is already on disk. Correct number of
      // bytes should be skipped when writing the data and checksum
      // buffers out to disk.
      uint32_t partial_chunk_size = disk_len % bytes_per_checksum_;
      uint32_t last_chunk_boundary = disk_len - partial_chunk_size;
      bool aligned_on_disk = partial_chunk_size == 0;
      bool aligned_in_packet = head_of_packet % bytes_per_checksum_ == 0;

      // If the end of the on-disk data is not chunk-aligned, the last
      // checksum needs to be overwritten.
      bool overwrite_last_crc = !aligned_on_disk && should_write_checksum_;
      // If the starting offset of the packat data is at the last chunk
      // boundary of the data on disk, the partial checksum recalculation
      // can be skipped and the checksum supplied by the client can be used
      // instead. This reduces disk reads and cpu load.
      bool recalc_crc =
          overwrite_last_crc && (last_chunk_boundary != head_of_packet);

      // If this is a partial chunk, then verify that this is the only
      // chunk in the packet. If the starting offset is not chunk
      // aligned, the packet should terminate at or before the next
      // chunk boundary.
      if (should_write_checksum_ && !aligned_in_packet &&
          data_len > bytes_per_checksum_) {
        std::string msg = byte::StringPrint(
            "Unexpected packet data length for %s"
            "from %s: a partial chunk must be sent in an individual packet "
            "(data length = %d > bytesPerChecksum = %d)",
            block_->ToString(), in_addr_, data_len, bytes_per_checksum_);
        e = exceptions::Exception(exceptions::E::kIOException, msg);
        break;
      }

      uint32_t num_bytes = offset_in_block - disk_len;
      if (disk_len < head_of_packet) {
        LOG(ERROR) << "disk_len:" << disk_len
                   << " is shorter than head_of_packet:" << head_of_packet;
        break;
      }
      data->ReadBytes(disk_len - head_of_packet);
      LOG(DEBUG) << "write block to chunkserver, offset:" << disk_len
                 << " bytes:" << num_bytes << " sync_block:" << sync_block;
      DURATION_START_NS(write_block);
      e = storage_->WriteBlock(block_, data, disk_len, num_bytes, false,
                               priority_);
      if (!e.OK()) {
        LOG(ERROR) << "write block to chunkserver failed, exception:"
                   << e.ToString();
        break;
      }
      time_trace.write_time_ns = DURATION_END_NS(write_block);
      METRICS_block_receiver_write_packet_latency->Set(
          time_trace.write_time_ns / 1000);
      if (static_cast<uint64_t>(time_trace.write_time_ns) / 1000 / 1000 >
          datanode_slow_log_threshold_ms_) {
        LOG(WARNING) << "Slow BlockReceiver write data to disk cost:"
                     << time_trace.write_time_ns / 1000
                     << "us (threshold=" << datanode_slow_log_threshold_ms_
                     << "ms), block: " << block_->ToString()
                     << ", volume: " << storage_->GetVolumePath(replica_info_);
      }

      uint32_t offset_in_checksum =
          disk_len / bytes_per_checksum_ * checksum_size_;
      if (!should_write_checksum_) {
        LOG(DEBUG) << "Skip write checksum for block " << block_->ToString();
      } else {
        uint32_t skip = 0;
        if (recalc_crc) {
          // |<- head_of_packet ->|·······················->|<- num_bytes
          // ->｜····->|
          // |<-                 disk_len                 ->|
          //                         |<-partial_chunk_size->|<-recalc_read_bytes->|
          // |<-last_chunk_boundary->|<-           bytes_per_checksum_ ->|
          uint32_t recalc_read_bytes = bytes_per_checksum_ - partial_chunk_size;
          if (num_bytes < recalc_read_bytes) {
            recalc_read_bytes = num_bytes;
          }
          LOG(DEBUG) << "receivePacket for " << block_->ToString()
                     << ": previous write did not end at the chunk boundary."
                     << "Disk len =" << disk_len;
          std::unique_ptr<DataChecksum> partial_crc;
          e = ComputePartialChunkCrc(disk_len, offset_in_checksum, partial_crc);
          if (!e.OK()) {
            LOG(ERROR) << "compute partial crc failed, exception:"
                       << e.ToString();
            break;
          }
          partial_crc->Update(data->Data(), disk_len - head_of_packet,
                              recalc_read_bytes);
          io::ChunkPtr crc(new io::IOChunk(checksum_size_),
                           io::IOChunk::Destroy);
          if (checksum_size_ == 4) {
            crc->WriteFixed32BE(partial_crc->GetValue());
          } else if (checksum_size_ == 8) {
            crc->WriteFixed64BE(partial_crc->GetValue());
          } else {
            std::string msg = byte::StringPrint(
                "Unsupported checksum size: %d.", checksum_size_);
            e = exceptions::Exception(exceptions::E::kIOException, msg);
            break;
          }
          e = storage_->WriteChecksum(block_, crc.get(), offset_in_checksum,
                                      checksum_size_, false, priority_);
          if (!e.OK()) {
            LOG(ERROR) << "write checksum failed, exception:" << e.ToString();
            break;
          }
          std::stringstream stream;
          for (size_t i = 0; i < checksum_size_; i++) {
            stream << std::hex << static_cast<int>(crc->Data()[i]);
          }
          std::string hex_crc(stream.str());
          LOG(DEBUG) << "Writing out partial crc = 0x" << hex_crc
                     << ", offset_in_checksum = " << offset_in_checksum
                     << ", checksum_size = " << checksum_size_;
          skip++;
          offset_in_checksum += checksum_size_;
        }

        int32_t skipped = last_chunk_boundary - head_of_packet;
        if (skipped > 0) {
          skip +=
              skipped / bytes_per_checksum_ + skipped % bytes_per_checksum_ == 0
                  ? 0
                  : 1;
        }
        skip *= checksum_size_;

        LOG(DEBUG) << "checksum_len: " << checksum_len << ", skip: " << skip;
        // write the rest of checksum
        if (checksum_len > skip) {
          checksum->ReadBytes(skip);
          LOG(DEBUG) << "write checksum to chunkserver, offset:"
                     << offset_in_checksum << " bytes:" << checksum_len - skip;
          e = storage_->WriteChecksum(block_, checksum, offset_in_checksum,
                                      checksum_len - skip, false, priority_);
          if (!e.OK()) {
            LOG(ERROR) << "write checksum failed, exception:" << e.ToString();
            break;
          }
        }
      }

      if (sync_block) {
        DURATION_START_NS(sync_block);
        METRICS_hdfs_sync_block_num->Increment();
        LOG(DEBUG) << "SyncBlock for " << block_->ToString();
        e = storage_->SyncBlock(block_, 0, priority_);
        if (!e.OK()) {
          break;
        }
        time_trace.sync_time_ns = DURATION_END_NS(sync_block);
        METRICS_block_receiver_sync_block_latency->Set(time_trace.sync_time_ns /
                                                       1000);
      }
      replica_info_->SetDiskDataLen(offset_in_block);
      if (op_key_.GetOp() != Operation::ReplaceBlock) {
        OpStats::GetInstance().Record(
            op_key_, offset_in_block, data_len, start_time_ns / 1000 / 1000,
            (byte::GetCurrentTimeInNs() - start_time_ns) / 1000);
      }
    } while (0);
    io::IOChunk::Destroy(csum_calculated);
    if (!e.OK()) {
      LOG(DEBUG) << "check disk error because write block failed";
      dn_->CheckDiskErrorAsync();
      return e;
    }
  }
  SetPacketTimeTrace(seqno, time_trace);

  if (responder_ != nullptr && (sync_block || ShouldVerifyChecksum())) {
    responder_->Enqueue(seqno, last_packet_in_block, offset_in_block,
                        Status::SUCCESS, start_time_ns);
  }

  // HDFS-6247: Send in-progress responses for the replaceBlock() calls back to
  // caller to
  //            avoid timeouts due to balancer throttling.
  if (is_replace_ &&
      (byte::GetCurrentTimeInMs() - last_replace_time_ > response_interval_)) {
    LOG(DEBUG) << "send in progress response for replaceBlock";
    int flag = SendResponse(reply_conn_, Status::IN_PROGRESS);
    if (flag != IO_OK) {
      std::string msg =
          byte::StringPrint("BlockReceiver failed to send response to %s.",
                            reply_conn_->Address().ToString());
      return exceptions::Exception(exceptions::E::kIOException, msg);
    }
    last_replace_time_ = byte::GetCurrentTimeInMs();
  }
  *len = last_packet_in_block ? -1 : (int32_t)data_len;
  LOG(DEBUG) << "Received data length: " << *len;
  return exceptions::Exception();
}

bool BlockReceiver::ShouldVerifyChecksum() const {
  return mirror_conn_ == nullptr || is_datanode_ || need_checksum_translation_;
}

exceptions::Exception BlockReceiver::VerifyChecksum(io::IOChunk* data,
                                                    io::IOChunk* checksum) {
  return client_checksum_->VerifyChecksum(data, checksum);
}

void BlockReceiver::TranslateChecksum(io::IOChunk* data,
                                      io::IOChunk* checksum) {
  disk_checksum_->CalculateChecksum(data, checksum);
}

exceptions::Exception BlockReceiver::ComputePartialChunkCrc(
    uint32_t block_offset, uint32_t checksum_offset,
    std::unique_ptr<DataChecksum>& partial) {
  uint32_t partial_chunk_size = block_offset % bytes_per_checksum_;
  block_offset -= partial_chunk_size;
  LOG(DEBUG) << "ComputePartialChunkCrc for " << block_->ToString()
             << ": partial_chunk_size=" << partial_chunk_size
             << ", block offset=" << block_offset
             << ", metafile offset=" << checksum_offset;

  io::ChunkPtr chunk(new io::IOChunk(partial_chunk_size), io::IOChunk::Destroy);
  auto e = storage_->ReadBlock(block_, chunk.get(), block_offset,
                               partial_chunk_size, priority_);
  if (!e.OK()) return e;
  partial.reset(DataChecksum::NewDataChecksum(disk_checksum_->GetType(),
                                              bytes_per_checksum_));
  partial->Update(chunk->Data(), 0, partial_chunk_size);
  // Different from java version:
  // No need to verify that the pre-computed crc matches what we recalculated
  // just now. Chunkstore has ensured that stored data is correct and we will
  // overwrite last crc later
  return exceptions::Exception();
}

exceptions::Exception BlockReceiver::HandleMirrorOutError(
    exceptions::Exception ioe) {
  std::string bpid = block_->GetBlockPoolID();
  std::shared_ptr<DatanodeRegistration> dnreg;
  auto e = dn_->GetDNRegistrationForBP(bpid, &dnreg);
  if (!e.OK()) {
    return e;
  }
  LOG(INFO) << dnreg->ToString() << ":Exception writing " << block_->ToString()
            << " to mirror " << mirror_addr_ << ", " << ioe.ToString();
  if (xceiver_->IsInterrupted()) {
    e = ioe;
  } else {
    SetMirrorError(true);
  }
  return e;
}

std::string BlockReceiver::GetStorageUUID() {
  return replica_info_->GetStorageUuid();
}

bool BlockReceiver::HasMirrorError() const {
  return byte::AtomicGet(&mirror_error_);
}

bool BlockReceiver::PacketSentInTime() const {
  int64_t diff = byte::GetCurrentTimeInMs() - byte::AtomicGet(&last_sent_time_);
  if (diff > max_send_idle_time_) {
    LOG(INFO) << "A packet was last sent " << diff << " milliseconds ago.";
    return false;
  }
  return true;
}
bool BlockReceiver::IsMirrorPacket() const {
  return byte::AtomicGet(&is_mirroring_);
}

void BlockReceiver::SetLastSentTime(uint64_t now) {
  byte::AtomicSet(&last_sent_time_, now);
}

void BlockReceiver::SetMirrorError(bool value) {
  byte::AtomicSet(&mirror_error_, value);
}

bool BlockReceiver::IsClosed() const {
  return byte::AtomicGet(&is_close_);
}

void BlockReceiver::Close() {
  byte::AtomicSet(&is_close_, true);
}

exceptions::Exception BlockReceiver::FinalizeBlock(uint64_t start_time,
                                                   uint64_t end_time) {
  Close();
  block_->SetNumBytes(replica_info_->GetNumBytes());
  auto e = dn_->GetStorage()->FinalizeBlock(block_, priority_);
  if (!e.OK()) {
    LOG(ERROR) << "finalize Block failed, exception:" << e.ToString();
    return e;
  }
  dn_->CloseBlock(block_, "", replica_info_->GetStorageUuid());

  if (is_client_) {
    uint64_t duration = end_time - start_time;
    LOG(INFO) << "finalize block "
              << "src: " << in_addr_ << ", dest: " << my_addr_
              << ", bytes: " << block_->GetNumBytes() << ", op: HDFS_WRITE"
              << ", cliID: " << client_name_ << ", offset: " << 0
              << ", srvID: " << dn_->GetUUID()
              << ", blockid: " << block_->GetBlockID()
              << ", startTime: " << start_time << ", endTime: " << end_time
              << ", duration: " << duration << ", pin: " << block_->IsPin();
  } else {
    LOG(INFO) << "finalize block Received " << block_->ToString() << " size "
              << block_->GetNumBytes() << " from " << in_addr_;
  }
  return exceptions::Exception();
}

void BlockReceiver::StopResponder() {
  // the responder may be nullptr in ReplaceBlock process
  if (responder_ != nullptr) {
    responder_->Stop();
  }
}

// Notify client that current node is restarting
exceptions::Exception BlockReceiver::SendRestartOOB() {
  if (responder_ == nullptr) {
    return exceptions::Exception();
  }
  return responder_->SendOOB(Status::OOB_RESTART);
}

exceptions::Exception BlockReceiver::FlushOrSync(bool is_sync) {
  // TODO(cyt): flush or synchonize
  return exceptions::Exception();
}

exceptions::Exception BlockReceiver::SetEnablePacketCheck(
    bool enable_packet_check) {
  if (packet_receiver_ == nullptr) {
    return exceptions::Exception(exceptions::kIOException,
                                 "packet receiver is null");
  }
  packet_receiver_->SetEnablePacketCheck(enable_packet_check);
  RETURN_NO_EXCEPTION();
}

void BlockReceiver::SetPacketTimeTrace(int64_t seqno,
                                       PacketTimeTrace& time_trace) {
  bytestore::ScopedSpinLock lock(&packet_lock_);
  packet_time_trace_[seqno] = time_trace;
}
bool BlockReceiver::TakePacketTimeTrace(int64_t seqno,
                                        PacketTimeTrace* time_trace) {
  bytestore::ScopedSpinLock lock(&packet_lock_);
  auto iter = packet_time_trace_.find(seqno);
  if (iter == packet_time_trace_.end()) {
    return false;
  }
  *time_trace = iter->second;
  packet_time_trace_.erase(iter);
  return true;
}

}  // namespace bds::dancedn::cloudfs
