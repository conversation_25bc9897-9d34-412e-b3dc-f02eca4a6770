// Copyright (c) 2019-present, ByteDance Inc. All rights reserved.

#pragma once

#include <map>
#include <memory>
#include <string>
#include <vector>

#include "cloudfs/exceptions.h"
#include "cloudfs/io/address.h"

namespace cloudfs {

class DatanodeIDProto;
class LocationTag;

}  // namespace cloudfs

namespace bds::dancedn::cloudfs {

// org.apache.hadoop.hdfs.protocol.DatanodeID.java
class DatanodeID {
 public:
  DatanodeID();
  DatanodeID(const std::string& ip_addr, const std::string& hostname,
             const std::string& uuid, int xfer_port, int info_port,
             int ipc_port, const std::string& node_name,
             const std::vector<std::string>& opt_ip_addrs,
             const std::vector<std::string>& opt_ip_rdma_tags);
  DatanodeID(const DatanodeID& datanode);
  virtual ~DatanodeID();

  DatanodeID* Clone() const;

  // base
  std::string GetIPAddr() const {
    return ip_addr_;
  }
  std::string GetHostName() const {
    return hostname_;
  }
  std::string GetPeerHostName() const {
    return peer_hostname_;
  }
  int GetXferPort() const {
    return xfer_port_;
  }
  int GetInfoPort() const {
    return info_port_;
  }
  int GetIPCPort() const {
    return ipc_port_;
  }
  std::string GetUUID() const {
    return uuid_;
  }
  io::IPAddress GetXferAddr() const {
    return xfer_addr_;
  }
  io::IPAddress GetIPCAddr() const {
    return ipc_addr_;
  }
  io::IPAddress GetInfoAddr() const {
    return info_addr_;
  }
  std::string GetNodeName() const {
    return node_name_;
  }
  const std::vector<std::string>& GetOptIpAddrs() const {
    return opt_ip_addrs_;
  }
  const std::vector<std::string>& GetOptIpRdmaTags() const {
    return opt_ip_rdma_tags_;
  }
  const std::map<std::string, int> GetStoragePortMap() const {
    return storage_port_map_;
  }

  void SetPeerHostName(const std::string peer_hostname) {
    peer_hostname_ = peer_hostname;
  }

  void SetEth0Addr(const std::string eth0_addr) {
    eth0_addr_ = eth0_addr;
  }

  void SetStoragePortMap(const std::map<std::string, int>& storage_port_map) {
    storage_port_map_ = storage_port_map;
  }

  std::string ToString() const;
  bool Equals(const std::shared_ptr<DatanodeID>& rhs) const;

 public:
  // convert to protobuf method
  exceptions::Exception ToProto(::cloudfs::DatanodeIDProto* proto) const;

  static DatanodeID* ParseProto(const ::cloudfs::DatanodeIDProto* proto);

 private:
  void Init(const std::string& ip_addr, const std::string& hostname,
            const std::string& uuid, int xfer_port, int info_port, int ipc_port,
            const std::string& node_name,
            const std::vector<std::string>& opt_ip_addrs,
            const std::vector<std::string>& opt_ip_rdma_tags);
  DatanodeID& operator=(const DatanodeID&) = delete;

 private:
  std::string ip_addr_;        // prefer use ipv4 when dual-stack
  std::string hostname_;       // hostname
  std::string peer_hostname_;  // hostname from actual connection
  std::string eth0_addr_;      // ipv6 address of eth0
  int xfer_port_;              // data streaming port
  int info_port_;              // info server port
  int info_secure_port_;       // info secure server port
  int ipc_port_;               // IPC server port
  io::IPAddress xfer_addr_;
  io::IPAddress ipc_addr_;
  io::IPAddress info_addr_;
  std::string uuid_;
  std::string node_name_;  // node's name in k8s
  std::vector<std::string>
      opt_ip_addrs_;  // support multiple ip addresses in k8s
  std::vector<std::string>
      opt_ip_rdma_tags_;  // support multiple ip addresses in k8s
  std::map<std::string, int>
      storage_port_map_;  //  byterpc port for each storage uuid
  std::unique_ptr<::cloudfs::LocationTag> location_tag_;
};

}  // namespace bds::dancedn::cloudfs
