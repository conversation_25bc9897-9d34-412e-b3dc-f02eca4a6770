// Copyright (c) 2018, ByteDance Inc. All rights reserved.

#include "cloudfs/finalized_replica.h"

#include <sstream>
#include <string>

#include "byte/string/format/print.h"
#include "cloudfs/extended_block.h"
#include "cloudfs/replica_info.h"
#include "cloudfs/store.h"

namespace bds::dancedn::cloudfs {

FinalizedReplica::FinalizedReplica() : unlinked_(false) {}

FinalizedReplica::FinalizedReplica(const ExtendedBlock* block,
                                   const std::string& storage_uuid,
                                   bool is_transient, bool is_evictable,
                                   uint8_t namespace_type,
                                   bool checksum_enabled, uint16_t disk_id,
                                   bytestore::PlacementStorageType type)
    : ReplicaInfo(block, storage_uuid, is_transient, is_evictable,
                  namespace_type, checksum_enabled, disk_id, type),
      unlinked_(false) {}

FinalizedReplica::FinalizedReplica(const std::string& bpid, uint64_t block_id,
                                   uint64_t len, uint64_t gs, bool pin,
                                   const std::string& storage_uuid,
                                   bool is_transient, bool is_evictable,
                                   uint8_t namespace_type,
                                   bool checksum_enabled, uint16_t disk_id,
                                   bytestore::PlacementStorageType type)
    : ReplicaInfo(bpid, block_id, len, gs, pin, storage_uuid, is_transient,
                  is_evictable, namespace_type, checksum_enabled, disk_id,
                  type),
      unlinked_(false) {}

FinalizedReplica::FinalizedReplica(const FinalizedReplica& from)
    : ReplicaInfo(from), unlinked_(from.IsUnlinked()) {}

int64_t FinalizedReplica::GetVisibleLength() const {
  return GetBlock()->GetNumBytes();
}

uint64_t FinalizedReplica::GetBytesOnDisk() const {
  return GetBlock()->GetNumBytes();
}

std::string FinalizedReplica::ToString() const {
  std::string str;
  str = byte::StringPrint(
      "FinalizedReplica, %s, %s"
      "  getNumBytes()      = %ld"
      "  getBytesOnDisk()   = %ld"
      "  getVisibleLength() = %ld"
      "  getDiskId()        = %u"
      "  unlinked           = %b",
      GetBlock()->ToString(), GetStateString(), GetNumBytes(), GetBytesOnDisk(),
      GetVisibleLength(), GetDiskId(), unlinked_);
  return str;
}

}  // namespace bds::dancedn::cloudfs
