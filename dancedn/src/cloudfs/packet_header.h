// Copyright (c) 2019-present, ByteDance Inc. All rights reserved.

#pragma once

#include <cstdint>
#include <string>

#include "cloudfs/exceptions.h"
#include "cloudfs_proto/datatransfer.pb.h"

namespace bds::dancedn::cloudfs {

namespace io {
class IOChunk;
}

class PacketHeader {
 public:
  explicit PacketHeader(uint32_t packet_len);
  ~PacketHeader();
  std::string ToString() const;

  uint32_t GetSerializedSize() const;
  int32_t GetDataLength() const;
  bool IsLastPacketInBlock() const;
  int64_t GetSeqno() const;
  int64_t GetOffsetInBlock() const;
  bool GetSyncBlock() const;
  int GetCrc(int64_t* crc);

  void PutInChunk(io::IOChunk* chunk);

  int ReadFromChunk(io::IOChunk* chunk);

  static PacketHeader* CreatePacketHeader(uint32_t packet_len,
                                          int64_t offset_in_block,
                                          int64_t seqno,
                                          bool last_packet_in_block,
                                          int32_t datalen, bool syncblock);

  static uint32_t GetMaxProtoSize();

 public:
  static const uint32_t MAX_PROTO_SIZE;
  static const uint32_t PKT_LENGTHS_LEN;
  static const uint32_t PKT_MAX_HEADER_LEN;

 private:
  uint32_t packet_len_;
  ::cloudfs::PacketHeaderProto proto_;
};

}  // namespace bds::dancedn::cloudfs
