// Copyright (c) 2019-present, ByteDance Inc. All rights reserved.

#pragma once

#include <string>
#include <vector>

#include "cloudfs/exceptions.h"
#include "cloudfs_proto/datatransfer.pb.h"

namespace bds::dancedn::cloudfs {

namespace io {
class Connection;
}

using Status = ::cloudfs::Status;

struct PipelineAckTimeTrace {
  int64_t total_ack_time_ns{0};
  std::vector<uint64_t> per_peer_ack_time_ns;
  std::vector<uint64_t> per_peer_recv_time_ns;
  std::vector<uint64_t> per_peer_write_time_ns;
  std::vector<uint64_t> per_peer_mirror_time_ns;
};

class PipelineAck {
 public:
  PipelineAck() {}
  PipelineAck(int64_t seqno, std::vector<Status> replies,
              PipelineAckTimeTrace& time_trace);

  ~PipelineAck() {}

  int GetStatusSize() const;
  Status GetReply(int index) const;
  std::vector<Status> GetReplies() const;
  int64_t GetSeqno() const;
  bool AllSuccess() const;
  int FindOOB() const;
  uint64_t GetDownstreamAckTimeNanos() const;
  std::vector<uint64_t> GetPerPeerAckTimeNanos() const;
  std::vector<uint64_t> GetPerPeerRecvPktNanos() const;
  std::vector<uint64_t> GetPerPeerWritePktNanos() const;
  std::vector<uint64_t> GetPerPeerMirrorPktNanos() const;
  std::string ToString() const;

  int WriteTo(io::Connection* out);
  int ReadFrom(io::Connection* in);

  static bool IsValidOOB(Status status);
  static int64_t GetOOBTimeout(Status status);

  std::string DebugString() {
    return proto_.DebugString();
  }

 public:
  static const int64_t UNKOWN_SEQNO;
  static const int32_t OOB_START;
  static const int32_t OOB_END;
  static const int32_t NUM_OOB_TYPES;
  static const uint64_t OOB_TIMEOUT[];

 private:
  ::cloudfs::PipelineAckProto proto_;
};

}  // namespace bds::dancedn::cloudfs
