// Copyright (c) 2019-present, ByteDance Inc. All rights reserved.

#include "cloudfs/thread_manager.h"

#include <sys/prctl.h>

#include <functional>
#include <iostream>
#include <string>

#include "base/closure.h"
#include "byte/base/atomic.h"
#include "byte/include/assert.h"

namespace bds::dancedn::cloudfs {

ExtendedThreadManager::ExtendedThreadManager(
    const std::string& name, int fast_pool_size,
    std::function<int()> max_fast_num_func)
    : fast_thread_pool_(fast_pool_size, name + "-f"),
      slow_thread_pool_(),
      name_(name),
      fast_num_(0),
      max_fast_num_func_(max_fast_num_func),
      slow_num_(0) {
  slow_thread_pool_.SetName(name + "-s");
  slow_thread_pool_.Start();
}

void ExtendedThreadManager::AddTask(Closure<void>* task) {
  if (fast_num_.fetch_add(1) >= max_fast_num_func_()) {
    fast_num_--;
    slow_num_++;
    ClosureThread* t = new ClosureThread();
    t->SetName(name_ + "-t");
    t->Init(NewClosure(this, &ExtendedThreadManager::RunSlowTask, task, t));
    slow_thread_pool_.AddWorker(t);
  } else {
    fast_thread_pool_.AddTask(
        NewClosure(this, &ExtendedThreadManager::RunFastTask, task));
  }
}

void ExtendedThreadManager::AddTask(const std::function<void()>& task) {
  AddTask(
      NewClosure(this, &ExtendedThreadManager::FunctionClosureWrapper, task));
}

int ExtendedThreadManager::GetFastNum() {
  return fast_num_;
}

int ExtendedThreadManager::GetSlowNum() {
  return slow_num_;
}

void ExtendedThreadManager::RunSlowTask(Closure<void>* task,
                                        ClosureThread* thread) {
  task->Run();
  slow_num_--;
  slow_thread_pool_.DeleteWorker(thread);
}

void ExtendedThreadManager::RunFastTask(Closure<void>* task) {
  task->Run();
  fast_num_--;
}

void ExtendedThreadManager::FunctionClosureWrapper(std::function<void()> func) {
  func();
}

}  // namespace bds::dancedn::cloudfs
