// Copyright (c) 2019-present, ByteDance Inc. All rights reserved.

#include "cloudfs/router.h"

#include "byte/include/byte_log.h"

namespace bds::dancedn::cloudfs {

void Router::add_route(brpc::HttpMethod method, const std::string& path,
                       HandlerT&& handler) {
  LOG(INFO) << "Registering route:" << brpc::HttpMethod2Str(method)
            << " path:" << path;
  callbacks_.push_back(std::make_tuple(
      method, std::regex(path, std::regex_constants::icase), handler));
}

HandlerT Router::route_request(brpc::HttpMethod method,
                               const std::string& path) const {
  const auto match = std::find_if(
      callbacks_.begin(), callbacks_.end(),
      [&](const std::tuple<brpc::HttpMethod, std::regex, HandlerT>& entry) {
        std::cmatch m;
        const auto& entry_method = std::get<0>(entry);
        const auto& entry_regex = std::get<1>(entry);
        return method == entry_method &&
               std::regex_match(path.c_str(), m, entry_regex);
      });

  if (match == callbacks_.end()) {
    return NULL;
  }
  return std::get<2>(*match);
}

}  // namespace bds::dancedn::cloudfs
