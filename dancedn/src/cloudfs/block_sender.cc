// Copyright (c) 2019-present, ByteDance Inc. All rights reserved.

#include "cloudfs/block_sender.h"

#include <unistd.h>

#include <algorithm>
#include <cstdint>
#include <memory>
#include <utility>

#include "byte/include/assert.h"
#include "byte/string/format/print.h"
#include "byte/system/timestamp.h"
#include "cloudfs/block.h"
#include "cloudfs/block_stream.h"
#include "cloudfs/caching_strategy.h"
#include "cloudfs/data_checksum.h"
#include "cloudfs/datanode.h"
#include "cloudfs/datanode_registration.h"
#include "cloudfs/datanode_stream_server.h"
#include "cloudfs/exceptions.h"
#include "cloudfs/extended_block.h"
#include "cloudfs/generation_stamp.h"
#include "cloudfs/io/connection.h"
#include "cloudfs/io/define.h"
#include "cloudfs/io/io_buf.h"
#include "cloudfs/lifecycle/block_entry_mgr.h"
#include "cloudfs/metrics.h"
#include "cloudfs/namespace_info.h"
#include "cloudfs/opstats/op_stats.h"
#include "cloudfs/packet_header.h"
#include "cloudfs/replica.h"
#include "cloudfs/replica_being_written.h"
#include "cloudfs/replica_state.h"
#include "cloudfs/store.h"
#include "cloudfs/store/chunkserver/partial_block_stream.h"
#include "cloudfs/store/ufs/tos_store.h"
#include "cloudfs/store/unified_block_store.h"
#include "cloudfs_proto/hdfs.pb.h"
#include "common/media_flags.h"
#include "gflags/gflags.h"
#include "string/algorithm.h"

DECLARE_uint32(bytestore_hdfs_sender_wait_period);
DECLARE_uint32(bytestore_hdfs_io_file_buffer_size);
DECLARE_uint32(bytestore_hdfs_max_chunk_size);
DECLARE_uint32(bytestore_hdfs_checksum_mode);
DECLARE_bool(bytestore_cfs_enable_partial_block_read);
DECLARE_bool(bytestore_cfs_check_exist_before_read);
DECLARE_string(bytestore_cfs_location_az);

namespace bds::dancedn::cloudfs {

DECLARE_HISTOGRAM_METRICS_POOL(hdfs_send_throttler_wait_time_ms);

const uint64_t BlockSender::LONG_READ_THRESHOLD_BYTES = 256 * 1024;
const uint64_t BlockSender::META_FILE_HEADER_SIZE = 7;

BlockSender::BlockSender(ExtendedBlock* block, uint64_t start_offset,
                         uint64_t length, bool verify_checksum,
                         bool send_checksum, DataNode* datanode,
                         const TraceBaggage& trace_baggage, ReadSource source)
    : block_(block),
      sent_entire_byte_range_(false),
      verify_checksum_(verify_checksum),
      generate_checksum_(false),
      dn_(datanode),
      checksum_(nullptr),
      last_disk_data_length_(0),
      op_key_(),
      disk_(nullptr),
      stream_(nullptr),
      ref_(nullptr),
      wait_time_ms_for_throttle_(0),
      trace_baggage_(trace_baggage),
      source_(source) {}

BlockSender::~BlockSender() {
  if (stream_ != nullptr) {
    delete stream_;
    stream_ = nullptr;
  }
}

exceptions::Exception BlockSender::CreateBlockSender(
    const std::shared_ptr<Thread>& thread, ExtendedBlock* block,
    uint64_t start_offset, int64_t length, bool verify_checksum,
    bool send_checksum, DataNode* datanode, CachingStrategy* caching_strategy,
    bool force_read, const std::string& user, int user_priority, bool is_client,
    const TraceBaggage& trace_baggage, BlockSender** block_sender) {
  exceptions::Exception e;
  auto caching_mode = caching_strategy->GetCachingMode();
  if (caching_mode == CachingStrategy::THROUGH) {
    if (block->IsPersisted().is_false()) {
      return exceptions::Exception(
          exceptions::E::kIllegalArgumentException,
          "Cannot use caching_mode=THROUGH with non-persisted block");
    }
    e = CreateRemoteBlockSender(block, start_offset, length, verify_checksum,
                                send_checksum, datanode, caching_strategy,
                                trace_baggage, block_sender);
  } else if (FLAGS_bytestore_cfs_enable_partial_block_read && length > 0) {
    // PartialStore does not know the exact max length, need specify the length.
    // length < 0 only occurs in CopyBlock scenario.
    e = CreatePartialBlockSender(block, start_offset, length, verify_checksum,
                                 send_checksum, datanode, caching_strategy,
                                 trace_baggage, block_sender);

  } else {
    e = CreateLocalBlockSender(
        thread, block, start_offset, length, verify_checksum, send_checksum,
        datanode, caching_strategy, force_read, user, user_priority, is_client,
        trace_baggage, block_sender);
    bool local_only = caching_mode == CachingStrategy::LOCAL ||
                      block->IsPersisted().is_false();
    if (!e.OK() && !local_only) {
      e = CreateRemoteBlockSender(block, start_offset, length, verify_checksum,
                                  send_checksum, datanode, caching_strategy,
                                  trace_baggage, block_sender);
    }
  }
  if (e.OK() && FLAGS_bytestore_cfs_check_exist_before_read) {
    e = (*block_sender)->CheckExist();
    if (!e.OK()) {
      LOG(ERROR) << "Failed to check block existence, block:"
                 << block->ToString() << " e:" << e.ToString();
      delete *block_sender;
      *block_sender = nullptr;
    }
    return e;
  }
  return e;
}

exceptions::Exception BlockSender::CreateLocalBlockSender(
    const std::shared_ptr<Thread>& thread, ExtendedBlock* block,
    uint64_t start_offset, int64_t length, bool verify_checksum,
    bool send_checksum, DataNode* datanode, CachingStrategy* caching_strategy,
    bool force_read, const std::string& user, int user_priority, bool is_client,
    const TraceBaggage& trace_baggage, BlockSender** block_sender) {
  LOG(DEBUG) << "create block sender, block:" << block->ToString()
             << " start_offset:" << start_offset << " length:" << length
             << " verify_checksum:" << verify_checksum
             << " send_checksum:" << send_checksum;
  std::unique_ptr<BlockSender> sender(
      new BlockSender(block, start_offset, length, verify_checksum,
                      send_checksum, datanode, trace_baggage));
  sender->thread_ = thread;
  sender->user_ = user;
  sender->user_priority_ = user_priority;
  sender->is_client_ = is_client;

  if (sender->verify_checksum_ && !send_checksum) {
    return exceptions::Exception(
        exceptions::E::kIllegalArgumentException,
        "If verifying checksum, currently must also send it.");
  }
  std::shared_ptr<ReplicaInfo> replica;
  int64_t replica_visible_length = 0;
  std::string block_pool_id = block->GetBlockPoolID();
  uint64_t block_id = block->GetBlock()->GetBlockID();
  replica = datanode->GetStorage()->GetReplica(block_pool_id, block_id);
  if (replica == nullptr) {
    return exceptions::Exception(exceptions::E::kReplicaNotFoundException,
                                 block->ToString());
  }
  if (replica->GetBlock()->IsPin() != block->IsPin()) {
    datanode->GetStorage()->GetLocalStore()->SubmitMarkReplicaPinBG(
        block->GetBlockPoolID(), block->GetBlockID(), block->IsPin());
  }
  // TODO(livexmm) initialize cachingStrategy
  replica_visible_length = replica->GetVisibleLength();
  sender->ns_type_ = static_cast<NamespaceType>(replica->GetNamespaceType());

  bytestore::DiskId disk_id = replica->GetDiskId();
  sender->disk_ = datanode->GetStorage()->GetDisk(disk_id);
  if (sender->disk_ == nullptr) {
    std::string msg =
        byte::StringPrint("Disk id not found while check files of %s",
                          replica->GetBlock()->ToString());
    return exceptions::Exception(exceptions::E::kIOException, msg);
  }
  std::string media_str = MediaTypeToShortString(sender->disk_->GetMediaType());
  sender->tags_ = {
      {"user", sender->user_},
      {"user_priority", byte::IntegerToString(sender->user_priority_)},
      {"bpid", block->GetBlockPoolID()},
      {"media_type", media_str},
      {"disk_id", byte::IntegerToString(sender->disk_->GetDiskId())},
      {"isclient", is_client ? "true" : "false"}};
  bytestore::Tags baggage =
      TraceBaggage::ToMetricTag(trace_baggage.GetBaggages());
  sender->tags_.insert(sender->tags_.end(), baggage.begin(), baggage.end());
  sender->ref_.reset(new bytestore::chunkserver::DiskRef(sender->disk_));
  if (sender->disk_->IsHangUp()) {
    std::string msg = byte::StringPrint("Disk Closed while check files of %s",
                                        replica->GetBlock()->ToString());
    return exceptions::Exception(exceptions::E::kIOException, msg);
  }
  uint64_t last_disk_data_length = 0;
  if (replica->GetState() == ReplicaState::RBW) {
    LOG(INFO) << "RBW wait for Minlength start_offset:" << start_offset
              << " length:" << length;
    auto rbw = std::dynamic_pointer_cast<ReplicaBeingWritten>(replica);
    auto e = WaitForMinLength(rbw, start_offset + length);
    if (!e.OK()) {
      return e;
    }
    last_disk_data_length = rbw->GetDiskDataLen();
  }
  if (force_read) {
    replica_visible_length = replica->GetBytesOnDisk();
    last_disk_data_length = replica_visible_length;
  }
  if (replica->GetGS() < block->GetGS()) {
    std::string msg =
        "Replica genstamp < block genstamp, block=" + block->ToString() +
        ", replica=" + replica->ToString();
    return exceptions::Exception(exceptions::E::kIOException, msg);
  } else if (replica->GetGS() > block->GetGS()) {
    block->SetGS(replica->GetGS());
  }
  if (replica_visible_length < 0) {
    std::string msg = "Replica is not readable, block=" + block->ToString() +
                      ", replica=" + replica->ToString();
    return exceptions::Exception(exceptions::E::kIOException, msg);
  }
  /*
   * | checksum_mode | meta_file_exist | operation | |       0       |  True |
   * read checksum from file and send       | |       0       |  False | send
   * TYPE_NULL checksum                | |     Not 0     |  True           |
   * read checksum from file and send       | |     Not 0     |  False |
   * generate TYPE_CRC32C checksum and send |
   */
  std::unique_ptr<DataChecksum> csum;
  if ((verify_checksum || send_checksum) && replica->ChecksumEnabled()) {
    DataChecksum* ret_csum = nullptr;
    auto e = datanode->GetStorage()->ReadDiskChecksum(block, &ret_csum);
    csum.reset(ret_csum);
    if (csum == nullptr) {
      LOG(WARNING) << "Could not find metadata file for " << block->ToString();
    }
  }
  if (csum == nullptr) {
    // The number of bytes per checksum here determines the alignment
    // of reads: we always start reading at a checksum chunk boundary,
    // even if the checksum type is NULL. So, choosing too big of a value
    // would risk sending too much unnecessary data. 512 (1 disk sector)
    // is likely to result in minimal extra IO.
    sender->generate_checksum_ =
        FLAGS_bytestore_hdfs_checksum_mode > 0 && send_checksum;
    if (sender->generate_checksum_) {
      csum.reset(
          DataChecksum::NewDataChecksum(DataChecksum::TYPE_CRC32C, 4096));
    } else {
      csum.reset(DataChecksum::NewDataChecksum(DataChecksum::TYPE_NULL, 512));
    }
  }

  // If chunkSize is very large, then the metadata file is mostly corrupted.
  // It is almost impossible for DanceDN, so we just return error;
  auto size = csum->GetBytesPerChecksum();
  uint32_t limit_size = FLAGS_bytestore_hdfs_max_chunk_size;
  if (size > limit_size && size > replica_visible_length) {
    std::string msg =
        byte::StringPrint("invalid checksum %s", csum->ToString());
    return exceptions::Exception(exceptions::E::kIOException, msg);
  }
  sender->chunk_size_ = size;
  sender->checksum_ = std::move(csum);
  sender->checksum_size_ = sender->checksum_->GetChecksumSize();
  length = length < 0 ? replica_visible_length : length;

  uint64_t end = last_disk_data_length != 0 ? last_disk_data_length
                                            : replica->GetBytesOnDisk();
  LOG(DEBUG) << "chunk_size:" << size
             << " checksum_size:" << sender->checksum_size_
             << " length:" << length
             << " replica_bytes_on_disk:" << replica->GetBytesOnDisk()
             << " end:" << end << " start_offset:" << start_offset
             << " should generate checksum: " << sender->generate_checksum_;
  if (start_offset < 0 || start_offset > end || (length + start_offset) > end) {
    std::string msg = byte::StringPrint(
        "Offset %ld and lenght %ld don't match block %s ( blockLen %ld )",
        start_offset, length, block->ToString(), end);
    std::shared_ptr<DatanodeRegistration> reg;
    auto e = datanode->GetDNRegistrationForBP(block->GetBlockPoolID(), &reg);
    if (!e.OK()) {
      return e;
    }
    LOG(WARNING) << reg->ToString() << ":sendBlock() : " << msg;
    return exceptions::Exception(exceptions::E::kIOException, msg);
  }
  sender->offset_ = start_offset - (start_offset % sender->chunk_size_);
  if (length >= 0) {
    // Ensure end_offset points to end of chunk
    uint64_t tmp_len = start_offset + length;
    if (tmp_len % sender->chunk_size_ != 0) {
      tmp_len += (sender->chunk_size_ - tmp_len % sender->chunk_size_);
    }
    if (tmp_len < end) {
      // will use on-disk checksum here since the end is a stable chunk
      end = tmp_len;
    } else if (last_disk_data_length != 0) {
      // last chunk is changing
      sender->last_disk_data_length_ = last_disk_data_length;
    }
  }

  LOG(DEBUG) << "sender_offset:" << sender->offset_
             << " sender_end_offset:" << end << " start_offset:" << start_offset
             << " length:" << length << " chunk_size:" << sender->chunk_size_;

  sender->end_offset_ = end;
  sender->seqno_ = 0;

  // TODO(guojun): Optimize the code to depend on the Store interface only
  UnifiedBlockStore* unified_store = datanode->GetStorage();
  BlockStream* stream = nullptr;
  auto&& e = unified_store->GetLocalStore()->CreateBlockStream(
      block, sender->offset_, sender->end_offset_ - sender->offset_,
      *caching_strategy, &stream);
  if (!e.OK()) {
    LOG(ERROR) << "Failed to create local block stream. error: "
               << e.ToString();
    return e;
  }
  sender->stream_ = stream;

  METRICS_block_sender_read_disk_num->GetMetric(sender->tags_)->Increment();
  METRICS_block_sender_read_num->GetMetric(sender->tags_)->Increment();
  if (e.OK() && datanode->GetStorage()->GetBlockEntryMgr() != nullptr) {
    datanode->GetStorage()->GetBlockEntryMgr()->ReadBlock(block->GetKey());
  }
  if (!replica->IsEvictable()) {
    sender->source_ = ReadSource::WriteCache;
  } else if (replica->GetStorageClass() == StorageClass::HOT) {
    sender->source_ = ReadSource::HotCache;
    METRICS_block_sender_read_disk_pin_num->GetMetric(sender->tags_)
        ->Increment();
  } else if (replica->GetStorageClass() == StorageClass::WARM) {
    sender->source_ = ReadSource::WarmCache;
  } else if (replica->GetStorageClass() == StorageClass::WARM) {
    sender->source_ = ReadSource::ColdCache;
  }
  *block_sender = sender.release();
  unified_store->ReadedBlock(block->GetKey());
  return exceptions::Exception();
}

exceptions::Exception BlockSender::CreatePartialBlockSender(
    ExtendedBlock* block, uint64_t start_offset, int64_t length,
    bool verify_checksum, bool send_checksum, DataNode* datanode,
    CachingStrategy* caching_strategy, const TraceBaggage& trace_baggage,
    BlockSender** block_sender) {
  LOG(INFO) << "create partial block sender, block:" << block->ToString()
            << " tos block offset:" << block->GetOffset()
            << " start_offset:" << start_offset << " length:" << length
            << " verify_checksum:" << verify_checksum
            << " send_checksum:" << send_checksum;
  if (verify_checksum) {
    return exceptions::Exception(
        exceptions::E::kIllegalArgumentException,
        "Does not support checksum for partial store.");
  }
  std::unique_ptr<BlockSender> sender(
      new BlockSender(block, start_offset, length, verify_checksum,
                      send_checksum, datanode, trace_baggage, ReadSource::TOS));
  std::shared_ptr<ExtendedBlock> replica;
  std::string bpid = block->GetBlockPoolID();
  std::string object_key = block->GetObjectKey();
  bytestore::Tags tags = TraceBaggage::ToMetricTag(trace_baggage.GetBaggages());
  tags.emplace_back(std::pair<std::string, std::string>("bpid", bpid));
  sender->tags_ = tags;
  auto ns_info = datanode->GetNamespaceInfo(bpid);
  if (ns_info == nullptr) {
    std::string msg = byte::StringPrint(
        "Error: Failed to get namespace info of bpid %s", bpid);
    return exceptions::Exception(exceptions::kReplicaNotFoundException, msg);
  }
  sender->ns_type_ = ns_info->GetNsType();
  DataChecksum* csum =
      DataChecksum::NewDataChecksum(DataChecksum::TYPE_NULL, 512);
  // If chunkSize is very large, then the metadata file is mostly
  // corrupted. For now just truncate bytesPerchecksum to blockLength.
  auto size = csum->GetBytesPerChecksum();
  sender->chunk_size_ = size;
  sender->checksum_.reset(csum);
  sender->checksum_size_ = sender->checksum_->GetChecksumSize();

  uint64_t end = block->GetNumBytes();
  if (start_offset < 0 || start_offset > end || (length + start_offset) > end) {
    std::string msg = byte::StringPrint(
        "Offset %ld and length %ld don't match block %s ( blockLen %ld )",
        start_offset, length, block->ToString(), end);
    std::shared_ptr<DatanodeRegistration> reg;
    auto e = datanode->GetDNRegistrationForBP(block->GetBlockPoolID(), &reg);
    if (!e.OK()) {
      return e;
    }
    LOG(WARNING) << reg->ToString() << ":sendBlock() : " << msg;
    return exceptions::Exception(exceptions::E::kIOException, msg);
  }
  sender->offset_ = start_offset - (start_offset % sender->chunk_size_);
  if (length >= 0) {
    // Ensure end_offset points to end of chunk
    uint64_t tmp_len = start_offset + length;
    if (tmp_len % sender->chunk_size_ != 0) {
      tmp_len += (sender->chunk_size_ - tmp_len % sender->chunk_size_);
    }
    if (tmp_len < end) {
      // will use on-disk checksum here since the end is a stable chunk
      end = tmp_len;
    }
  }
  sender->end_offset_ = end;

  LOG(INFO) << "sender_offset:" << sender->offset_
            << " sender_end_offset:" << sender->end_offset_
            << " tos block offset:" << block->GetOffset()
            << " start_offset:" << start_offset << " length:" << length
            << " chunk_size:" << sender->chunk_size_;

  sender->stream_ = new PartialBlockStream(
      datanode, block, ns_info->GetNsType(), sender->offset_,
      sender->end_offset_ - sender->offset_);
  sender->source_ = ReadSource::PartialStore;

  sender->seqno_ = 0;
  *block_sender = sender.release();

  METRICS_block_sender_read_partial_num->GetMetric(tags)->Increment();
  METRICS_block_sender_read_num->GetMetric(tags)->Increment();
  return exceptions::Exception();
}

exceptions::Exception BlockSender::CreateRemoteBlockSender(
    ExtendedBlock* block, uint64_t start_offset, int64_t length,
    bool verify_checksum, bool send_checksum, DataNode* datanode,
    CachingStrategy* caching_strategy, const TraceBaggage& trace_baggage,
    BlockSender** block_sender) {
  LOG(DEBUG) << "create remote block sender, block:" << block->ToString()
             << " remote block offset:" << block->GetOffset()
             << " start_offset:" << start_offset << " length:" << length
             << " verify_checksum:" << verify_checksum
             << " send_checksum:" << send_checksum;
  if (verify_checksum && !send_checksum) {
    return exceptions::Exception(
        exceptions::E::kIllegalArgumentException,
        "If verifying checksum, currently must also send it.");
  }
  std::unique_ptr<BlockSender> sender(new BlockSender(
      block, start_offset, length, verify_checksum, send_checksum, datanode,
      trace_baggage, ReadSource::UNKNOWN));
  std::shared_ptr<ExtendedBlock> replica;
  UnifiedBlockStore* store = datanode->GetStorage();
  std::string bpid = block->GetBlockPoolID();
  std::string object_key = block->GetObjectKey();
  bytestore::Tags tags = TraceBaggage::ToMetricTag(trace_baggage.GetBaggages());
  tags.emplace_back(std::pair<std::string, std::string>("bpid", bpid));
  sender->tags_ = tags;
  auto ns_info = datanode->GetNamespaceInfo(bpid);
  if (ns_info == nullptr) {
    std::string msg = byte::StringPrint(
        "Error: Failed to get namespace info of bpid %s", bpid);
    return exceptions::Exception(exceptions::kReplicaNotFoundException, msg);
  }
  auto ns_type = ns_info->GetNsType();
  sender->ns_type_ = ns_type;
  if (!IsAccType(ns_type)) {
    auto remote_store = store->GetRemoteStore(ns_type);
    if (nullptr == remote_store) {
      std::string msg =
          byte::StringPrint("Error: Failed to get remote store %s",
                            NamespaceTypeToString(ns_type));
      return exceptions::Exception(exceptions::kIllegalStateException, msg);
    }
    exceptions::Exception e = remote_store->GetBlockInfo(
        bpid, object_key, bytestore::PRIORITY_ELASTIC, replica);
    if (!e.OK()) {
      LOG(ERROR) << "Failed to get block information of block %s",
          block->ToString();
      return e;
    }

    if (replica->GetGS() < block->GetGS()) {
      std::string msg =
          "Replica genstamp < block genstamp, block=" + block->ToString() +
          ", replica=" + replica->ToString();
      return exceptions::Exception(exceptions::E::kIOException, msg);
    } else if (replica->GetGS() > block->GetGS()) {
      block->SetGS(replica->GetGS());
    }

    uint64_t replica_length = replica->GetNumBytes();
    if (replica_length < 0) {
      std::string msg = "Replica is not readable, block=" + block->ToString() +
                        ", replica=" + replica->ToString();
      return exceptions::Exception(exceptions::E::kIOException, msg);
    }

    uint64_t replica_visible_length = block->GetNumBytes();
    if (replica_length != replica_visible_length) {
      std::string msg = byte::StringPrint(
          "Number of bytes in replica %s does not match block %s",
          replica->ToString(), block->ToString());
      return exceptions::Exception(exceptions::E::kIOException, msg);
    }
    length = length < 0 ? replica_visible_length : length;
  }

  DataChecksum* csum =
      DataChecksum::NewDataChecksum(DataChecksum::TYPE_NULL, 512);

  // If chunkSize is very large, then the metadata file is mostly
  // corrupted. For now just truncate bytesPerchecksum to blockLength.
  auto size = csum->GetBytesPerChecksum();
  sender->chunk_size_ = size;
  sender->checksum_.reset(csum);
  sender->checksum_size_ = sender->checksum_->GetChecksumSize();

  uint64_t end = block->GetNumBytes();
  if (start_offset < 0 || start_offset > end || (length + start_offset) > end) {
    std::string msg = byte::StringPrint(
        "Offset %ld and length %ld don't match block %s ( blockLen %ld )",
        start_offset, length, block->ToString(), end);
    std::shared_ptr<DatanodeRegistration> reg;
    auto e = datanode->GetDNRegistrationForBP(block->GetBlockPoolID(), &reg);
    if (!e.OK()) {
      return e;
    }
    LOG(WARNING) << reg->ToString() << ":sendBlock() : " << msg;
    return exceptions::Exception(exceptions::E::kIOException, msg);
  }
  sender->offset_ = start_offset - (start_offset % sender->chunk_size_);
  if (length >= 0) {
    // Ensure end_offset points to end of chunk
    uint64_t tmp_len = start_offset + length;
    if (tmp_len % sender->chunk_size_ != 0) {
      tmp_len += (sender->chunk_size_ - tmp_len % sender->chunk_size_);
    }
    if (tmp_len < end) {
      // will use on-disk checksum here since the end is a stable chunk
      end = tmp_len;
    }
  }
  sender->end_offset_ = end;

  LOG(INFO) << "sender_offset:" << sender->offset_
            << " sender_end_offset:" << sender->end_offset_
            << " remote block offset:" << block->GetOffset()
            << " start_offset:" << start_offset << " length:" << length
            << " chunk_size:" << sender->chunk_size_;

  // TODO(guojun): Optimize the code to depend on the Store interface only
  UnifiedBlockStore* unified_store = datanode->GetStorage();
  BlockStream* stream = nullptr;
  auto e = unified_store->CreateBlockStream(
      block, sender->offset_ + block->GetOffset(),
      sender->end_offset_ - sender->offset_, *caching_strategy, &stream);
  if (!e.OK()) {
    LOG(ERROR) << "Failed to create block stream. error: " << e.ToString();
    return e;
  }
  sender->stream_ = stream;
  if (ns_type == NamespaceType::ACC_HDFS) {
    sender->source_ = ReadSource::HDFS;
  } else {
    sender->source_ = ReadSource::TOS;
  }

  sender->seqno_ = 0;
  *block_sender = sender.release();

  if (ns_type == NamespaceType::ACC_HDFS) {
    METRICS_block_sender_read_hdfs_num->GetMetric(tags)->Increment();
  } else {
    METRICS_block_sender_read_tos_num->GetMetric(tags)->Increment();
  }
  METRICS_block_sender_read_num->GetMetric(tags)->Increment();
  if (e.OK() && datanode->GetStorage()->GetBlockEntryMgr() != nullptr) {
    datanode->GetStorage()->GetBlockEntryMgr()->ReadMissingBlock(
        block->GetKey(), ns_info->GetNsType());
  }
  return exceptions::Exception();
}

exceptions::Exception BlockSender::WaitForMinLength(
    const std::shared_ptr<ReplicaBeingWritten>& rbw, uint64_t len) {
  for (uint8_t i = 0; i < 30 && rbw->GetBytesOnDisk() < len; i++) {
    usleep(FLAGS_bytestore_hdfs_sender_wait_period);
  }
  uint64_t bytes_on_disk = rbw->GetBytesOnDisk();
  if (bytes_on_disk < len) {
    return exceptions::Exception(
        exceptions::E::kIOException,
        byte::StringPrint("Need %d bytes, but only %d bytes available", len,
                          bytes_on_disk));
  }
  return exceptions::Exception();
}

io::IOChunk* BlockSender::PreallocIOChunk(uint32_t max_chunks) {
  uint32_t datalen =
      std::min((uint32_t)(end_offset_ - offset_), chunk_size_ * max_chunks);
  uint32_t num_chunks = NumberOfChunks(datalen);
  uint32_t checksum_data_len = num_chunks * checksum_size_;
  uint32_t packet_len = datalen + checksum_data_len + 4;
  uint32_t total_len = packet_len + PacketHeader::PKT_MAX_HEADER_LEN;
  return new io::IOChunk(total_len, true);
}

exceptions::Exception BlockSender::SendBlock(io::Connection* conn,
                                             uint64_t* read_len,
                                             bool need_acquire) {
  if (conn == nullptr || conn->IsClosed()) {
    return exceptions::Exception(exceptions::E::kIOException,
                                 "out stream is null or close");
  }
  initial_offset_ = offset_;
  uint64_t total_read = 0;

  // TODO(caibingfeng) if (isLongRead() && blockinfd != nullptr)
  manageOsCache();

  uint32_t pkt_buf_size = PacketHeader::PKT_MAX_HEADER_LEN;
  uint32_t max_chunks_per_packet = std::max(
      (uint32_t)1, NumberOfChunks(FLAGS_bytestore_hdfs_io_file_buffer_size));
  pkt_buf_size += (chunk_size_ + checksum_size_) * max_chunks_per_packet;
  LOG(DEBUG) << "pkt_buf_size:" << pkt_buf_size
             << " PKT_MAX_HEADER_LEN:" << PacketHeader::PKT_MAX_HEADER_LEN
             << " max_chunks_per_packet:" << max_chunks_per_packet
             << " chunk_size:" << chunk_size_
             << " checksum_size:" << checksum_size_
             << " end_offset:" << end_offset_ << " offset:" << offset_;

  io::IOChunk* chunk = PreallocIOChunk(max_chunks_per_packet);

  while (end_offset_ > offset_) {
    manageOsCache();
    int64_t start_time = byte::GetCurrentTimeInUs();
    uint64_t len = 0;
    auto e = SendPacket(conn, max_chunks_per_packet, chunk, &len);
    if (!e.OK()) {
      LOG(ERROR) << "send packet failed block:" << block_->ToString()
                 << " send len:" << len << " offset:" << offset_
                 << " total_read:" << total_read << " seqno:" << seqno_
                 << " client_version: " << client_version_;
      chunk->AlwaysDestroy();
      return e;
    }
    if (need_acquire && len > 0) {
      dn_->NamespaceAcquireRead(block_->GetBlockPoolID(), len);
    }
    offset_ += len;
    total_read += len + (NumberOfChunks(len) * checksum_size_);
    seqno_++;
    LOG(DEBUG) << "send len:" << len << " offset:" << offset_
               << " total_read:" << total_read << " seqno:" << seqno_;
    if (!op_key_.isEmpty()) {
      OpStats::GetInstance().Record(op_key_, offset_, len, start_time / 1000,
                                    byte::GetCurrentTimeInUs() - start_time);
    }
  }

  uint64_t len = 0;
  auto e = SendPacket(conn, max_chunks_per_packet, chunk, &len);
  if (!e.OK()) {
    LOG(ERROR) << "send last packet failed block:" << block_->ToString()
               << " send len:" << len << " offset:" << offset_
               << " total_read:" << total_read << " seqno:" << seqno_;
    chunk->AlwaysDestroy();
    return e;
  }
  sent_entire_byte_range_ = true;

  // uint64_t end_time = byte::GetCurrentTimeInMs();
  // uint64_t use = end_time - start_time;
  // if (use > FLAGS_bytestore_hdfs_slow_io_warning_threshold_ms) {
  //     LOG(WARNING) << "Send block use time large than "
  //                  << FLAGS_bytestore_hdfs_slow_io_warning_threshold_ms
  //                  << "ms use: " << use;
  // }

  chunk->AlwaysDestroy();
  *read_len = total_read;
  return exceptions::Exception();
}

exceptions::Exception BlockSender::SendPacket(io::Connection* conn,
                                              uint32_t max_chunks,
                                              io::IOChunk* chunk,
                                              uint64_t* len) {
  uint32_t data_len =
      std::min((uint32_t)(end_offset_ - offset_), chunk_size_ * max_chunks);
  uint32_t num_chunks = NumberOfChunks(data_len);
  uint32_t checksum_data_len = num_chunks * checksum_size_;
  uint32_t packet_len = data_len + checksum_data_len + 4;
  uint32_t total_len = packet_len + PacketHeader::PKT_MAX_HEADER_LEN;
  bool last_data_packet = offset_ + data_len == end_offset_ && data_len > 0;
  LOG(DEBUG) << "date_len:" << data_len << " num_chunks:" << num_chunks
             << " checksum_data_len:" << checksum_data_len
             << " packet_len:" << packet_len << " total_len:" << total_len
             << " last_data_packet:" << last_data_packet;
  // The packet buffer is organized as follows:
  // _______HHHHCCCCD?D?D?D?
  //        ^   ^
  //        |   \ checksumOff
  //        \ headerOff
  // _ padding, since the header is variable-length
  // H = header and length prefixes
  // C = checksums
  // D? = data, if transferTo is false.
  int64_t start_time = byte::GetCurrentTimeInUs();
  uint32_t disk_id = disk_ != nullptr ? disk_->GetDiskId() : 0;
  LimitObject object =
      is_client_ ? LimitObject::kClientRead : LimitObject::kTransferSrc;
  bytestore::IOPriority pri = is_client_ ? bytestore::PRIORITY_ELASTIC
                                         : bytestore::PRIORITY_BEST_EFFORT;
  dn_->AcquireLocalToken(disk_id, object, total_len, pri);
  int64_t wait_time_ms = (byte::GetCurrentTimeInUs() - start_time) / 1000;
  wait_time_ms_for_throttle_ += wait_time_ms;
  METRICS_hdfs_send_throttler_wait_time_ms->GetMetric(tags_)->Set(wait_time_ms);

  io::IOBuf* buf = conn->WriteBuf();
  int head_len;
  auto e = WritePacketHeader(chunk, data_len, packet_len, &head_len);
  if (!e.OK()) {
    LOG(ERROR) << "WritePacketHeader error exceptions : " << e.ToString();
    return e;
  }
  int headerOff = chunk->Length() - head_len;
  uint32_t checksum_off = chunk->Length();
  uint64_t csum_file_off = (offset_ / chunk_size_) * checksum_size_;
  LOG(DEBUG) << "headeroff:" << headerOff << " head_len:" << head_len
             << " checksum_off:" << checksum_off
             << " csum_file_off:" << csum_file_off;
  if (generate_checksum_) {
    chunk->IncrLength(checksum_data_len);
  } else if (checksum_size_ > 0) {
    auto e = dn_->GetStorage()->ReadChecksum(block_, chunk, csum_file_off,
                                             checksum_data_len,
                                             bytestore::PRIORITY_ELASTIC);
    if (!e.OK()) {
      LOG(WARNING)
          << "Could not read or failed to veirfy checksum for data at offset "
          << offset_ << " for block " << block_->ToString() << e.ToString();
      return e;
    }
    // Different from java version:
    // To simplify the process of writing, we do not keep rbw's last checksum in
    // memory, so we need recalc last checksum instead of get it from memory
    // (see below)
  }
  uint32_t data_off = checksum_off + checksum_data_len;
  LOG(DEBUG) << "data_off:" << data_off << " offset:" << offset_
             << " data_len:" << data_len;

  // when sending last empty packet, no need to read from storage
  uint32_t count = 0;
  DURATION_START(read_packet);
  bytestore::Tags tags =
      TraceBaggage::ToMetricTag(trace_baggage_.GetBaggages());
  tags.emplace_back(
      std::pair<std::string, std::string>("bpid", block_->GetBlockPoolID()));
  tags.emplace_back(std::pair<std::string, std::string>("rpc", "hrpc"));
  tags.emplace_back(std::pair<std::string, std::string>("type", "ReadBlock"));
  std::string src_az =
      client_location_.az().empty() ? "NA" : client_location_.az();
  std::string dst_az = FLAGS_bytestore_cfs_location_az;
  tags.emplace_back(std::pair<std::string, std::string>("srcAZ", src_az));
  tags.emplace_back(std::pair<std::string, std::string>("dstAZ", dst_az));
  if (data_len > 0) {
    while (count < data_len) {
      uint32_t remain = data_len - count;
      uint32_t returned_size = 0;
      e = stream_->Read(remain, chunk, &returned_size);
      if (!e.OK()) {
        LOG(ERROR) << "Read block from storage failed error exceptions : "
                   << e.ToString();
        return e;
      }
      count += returned_size;
    }
  }
  if (stream_->GetType() == BlockStreamType::TOS) {
    METRICS_block_sender_read_tos_latency->Set(DURATION_END(read_packet));
    METRICS_block_sender_read_tos_bytes->GetMetric(tags_)->Add(data_len);
  } else if (stream_->GetType() == BlockStreamType::HDFS) {
    METRICS_block_sender_read_hdfs_latency->Set(DURATION_END(read_packet));
    METRICS_block_sender_read_hdfs_bytes->GetMetric(tags_)->Add(data_len);
  } else if (stream_->GetType() == BlockStreamType::PARTIAL_STORE) {
    METRICS_block_sender_read_partial_latency->Set(DURATION_END(read_packet));
    METRICS_block_sender_read_partial_bytes->GetMetric(tags_)->Add(data_len);
  } else {
    METRICS_block_sender_read_disk_latency->Set(DURATION_END(read_packet));
    METRICS_block_sender_read_disk_bytes->GetMetric(tags_)->Add(data_len);
    if (source_ == ReadSource::HotCache || block_->IsPin()) {
      METRICS_block_sender_read_pin_bytes->GetMetric(tags_)->Add(data_len);
    }
  }

  if (generate_checksum_ && checksum_size_ > 0 && data_len > 0) {
    LOG(DEBUG) << "Generating checksum for block " << block_->ToString()
               << " seqno " << seqno_;
    uint32_t gen_csum_off = checksum_off;
    uint64_t gen_data_off = data_off;
    for (uint32_t gen_seqno = 0; gen_seqno < num_chunks; gen_seqno++) {
      uint32_t gen_chunk_size =
          std::min(chunk_size_, (uint32_t)(chunk->Length() - gen_data_off));
      checksum_->Reset();
      checksum_->Update(chunk->Data(), gen_data_off, gen_chunk_size);
      chunk->ReplaceFixed32BE(gen_csum_off, checksum_->GetValue());
      gen_csum_off += checksum_size_;
      gen_data_off += gen_chunk_size;
    }
  }

  // update checksum for rbw whose last chunk is changing
  if (last_data_packet && last_disk_data_length_ != 0 && checksum_size_ > 0) {
    BYTE_ASSERT_EQ(checksum_size_, (uint32_t)4);
    uint32_t partial_chunk_size = last_disk_data_length_ % chunk_size_;
    uint32_t last_chunk_off = data_off + data_len - partial_chunk_size;
    checksum_->Reset();
    checksum_->Update(chunk->Data(), last_chunk_off, partial_chunk_size);
    uint32_t offset = checksum_off + checksum_data_len - checksum_size_;
    chunk->ReplaceFixed32BE(offset, checksum_->GetValue());
  }

  if (verify_checksum_) {
    auto e = checksum_->VerifyChecksum(chunk->Data() + data_off, data_len,
                                       chunk->Data() + checksum_off);
    if (!e.OK()) {
      LOG(ERROR) << "VerifyChecksum error exceptions is : " << e.ToString();
      return e;
    }
  }

  chunk->IncrOffset(headerOff);
  buf->Append(chunk);
  // TODO(caibingfeng)  if (transferto)
  DURATION_START(write_packet);
  auto flag = conn->Write(true);
  if (flag != IO_OK) {
    LOG(ERROR) << "write failed, flag:" << flag;
    return exceptions::Exception(exceptions::E::kIOException,
                                 "write packet failed");
  }
  METRICS_block_sender_write_packet_latency->Set(DURATION_END(write_packet));
  METRICS_block_sender_send_bytes->GetMetric(tags)->Add(data_len);
  METRICS_dancedn_request_throughput->GetMetric(tags)->Add(data_len);

  *len = data_len;
  return exceptions::Exception();
}

void BlockSender::manageOsCache() {
  // TODO(livexmm) manageOsCache
}

// bool BlockSender::IsLongRead() const {
//     return (end_offset_ - initial_offset_) > LONG_READ_THRESHOLD_BYTES;
// }

uint32_t BlockSender::NumberOfChunks(uint64_t datalen) {
  return static_cast<uint32_t>((datalen + chunk_size_ - 1) / chunk_size_);
}

exceptions::Exception BlockSender::WritePacketHeader(io::IOChunk* chunk,
                                                     uint32_t data_len,
                                                     uint32_t packet_len,
                                                     int* head_len) {
  auto header = PacketHeader::CreatePacketHeader(
      packet_len, offset_, seqno_, (data_len == 0), data_len, false);
  int size = header->GetSerializedSize();
  chunk->IncrLength(PacketHeader::PKT_MAX_HEADER_LEN - size);
  header->PutInChunk(chunk);
  delete header;
  *head_len = size;
  return exceptions::Exception();
}

exceptions::Exception BlockSender::CheckExist() {
  // TODO(haojinming) ACC_HDFS does not support GetBlockInfo
  if (ns_type_ == NamespaceType::ACC_TOS) {
    return stream_->CheckExist();
  }
  return exceptions::Exception();
}

}  // namespace bds::dancedn::cloudfs
