// Copyright (c) 2018-present, ByteDance Inc. All rights reserved.

#include "cloudfs/block_pool_manager.h"

#include <fstream>
#include <iostream>
#include <string>
#include <unordered_map>

#include "byte/io/local_filesystem.h"
#include "cloudfs/block_list_as_longs.h"
#include "cloudfs/block_pool_actor.h"
#include "cloudfs/block_pool_service.h"
#include "cloudfs/block_report_options.h"
#include "cloudfs/datanode.h"
#include "cloudfs/datanode_id.h"
#include "cloudfs/datanode_info.h"
#include "cloudfs/datanode_registration.h"
#include "cloudfs/exceptions.h"
#include "cloudfs/extended_block.h"
#include "cloudfs/namenode_client.h"
#include "cloudfs/namenode_rpc.h"
#include "cloudfs/namespace_info.h"
#include "cloudfs/nn_ha_status_heartbeat.h"
#include "cloudfs/replica_in_pipeline.h"
#include "cloudfs/replica_info.h"
#include "cloudfs/replica_state.h"
#include "cloudfs/rolling_upgrade_status.h"
#include "cloudfs/store.h"
#include "cloudfs/volume_failure_summary.h"
#include "gflags/gflags.h"
#include "gtest/gtest.h"

DECLARE_string(bytestore_hdfs_config_file);
DECLARE_string(bytestore_hdfs_nn_config_file);
namespace bds::dancedn::cloudfs {

// Prepare configuration contents; should be saved as an individual file in
// future.
static const std::string& k_test_content = R"(
{
    "BlockReportInterval": 60,
    "InitialBlockReportDelay": 300,
    "MinimunNameNodeVersion": "2.6.0",
    "DeleteReportInterval": 50,
    "HeartBeatInterval": 70,
    "FailoverBlockReportDelay": 80,
    "BlockReportSplitThreshold": 99,
    "SocketKeepaliveTimeout": 120,
    "SocketTimeout": 120,
    "IOFileBufferSize":4096,
    "EstimateBlockSize": 51200,
    "RestartReplicaExpiry": 333,
    "DatanodeSlowIoWarningThresholdMs": 334,
    "SyncBehindWrites": false,
    "GetSyncBehindWriteInBackground": true
})";

static const std::string& k_test_nn_content = R"(
{
    "Namenodes": {
        "ns0": ["nn1", "nn0"],
        "ns1": ["nn0"]
    },
    "DNProxy": "proxy:5061",
    "Version" : "1.0.0.1"
})";

static const std::string& k_test_nn_content1 = R"(
{
    "Namenodes": {
        "ns0": ["nn1", "nn0"],
    },
    "DNProxy": "proxy:5061",
    "Version" : "1.0.0.1"
})";

static const std::string& k_test_nn_content2 = R"(
{
    "Namenodes": {
        "ns0": ["nn1", "nn0"],
        "ns1": ["nn0"],
        "ns2": ["nn0"]
    },
    "DNProxy": "proxy:5061",
    "Version" : "1.0.0.1"
})";
static const std::string& k_test_nn_content3 = R"(
{
    "Namenodes": {
        "ns3": ["nn0"]
    },
    "DNProxy": "proxy:5061",
    "Version" : "1.0.0.1"
})";

static const std::string& k_test_nn_content4 = R"(
{
    "Namenodes": {},
    "DNProxy": "proxy:5061",
    "Version" : "1.0.0.1"
})";

static const char work_dir[] = "./BlockPoolManagerTests_conf/";
static const int namespace_id = 1004580134;
static const char cluster_id[] = "CID-9a628355-6954-473b-a66c-d34d7c2b3805";
static const char block_pool_id[] = "BP-3845026443837452998-1546064706393";
static const uint64_t create_time = 1546064706393;
static const char build_version[] = "2.6.0";
static const char software_version[] = "2.6.3";
static const uint64_t fs_id = 4013797767642215140;
static const uint64_t ns_id = 3845026443837452998;
static const NamespaceType ns_type = NamespaceType::TOS_LOCAL;

class BlockPoolManagerTests : public ::testing::Test {
 public:
  BlockPoolManagerTests() {
    dn_conf_ = nullptr;
    dn_ = nullptr;
  }
  ~BlockPoolManagerTests() override {
    ClearConfigDir();
    dn_->Stop();
    dn_->Join();
    dn_->Destory();
  }
  void SetUp() override {
    bytestore::metrics_internal::InitFastMetrics();
    dn_ = new DataNode();
    InitConfig();
    dn_->SetDataNodeConfig(dn_conf_);
  }

  static void SetUpTestCase() {}
  static void TearDownTestCase() {
    byte::GetLoggingSystem()->Shutdown();
  }

 public:
  void InitConfig() {
    byte::LocalFileSystem local_fs;
    byte::DeleteOptions delete_options;
    delete_options.recursively_ = true;
    if (local_fs.Exists(work_dir).ok()) {
      local_fs.DeleteDir(work_dir, delete_options);
    }
    local_fs.CreateDir(work_dir, byte::CreateOptions()).ok();
    const std::string test_conf_file =
        std::string(work_dir) + "/hdfsconfig.json";
    const std::string test_nn_conf_file =
        std::string(work_dir) + "/nnconfig.json";
    FLAGS_bytestore_hdfs_config_file = test_conf_file;
    FLAGS_bytestore_hdfs_nn_config_file = test_nn_conf_file;
    std::ofstream json_file(test_conf_file);
    json_file << k_test_content << std::endl;
    json_file.close();
    std::ofstream json_nn_file(test_nn_conf_file);
    json_nn_file << k_test_nn_content << std::endl;
    json_nn_file.close();
    dn_conf_ = new DataNodeConfig();

    dn_conf_->Parse(test_conf_file, test_nn_conf_file, "");
  }

  void ClearConfigDir() {
    byte::LocalFileSystem local_fs;
    byte::DeleteOptions delete_options;
    delete_options.recursively_ = true;
    local_fs.DeleteDir(work_dir, delete_options);
  }

  bool RefreshDnConfNNContent(const std::string& file_name,
                              const std::string& nn_content) {
    const std::string test_conf_file =
        std::string(work_dir) + "/hdfsconfig.json";
    const std::string nn_conf_file =
        std::string(work_dir) + "/" + file_name + ".json";
    std::ofstream json_nn1_file(nn_conf_file);
    json_nn1_file << nn_content << std::endl;
    json_nn1_file.close();
    return dn_conf_->Parse(test_conf_file, nn_conf_file, "");
  }

 public:
  DataNodeConfig* dn_conf_;
  DataNode* dn_;
};

TEST_F(BlockPoolManagerTests, UnLockedAddBlockPool) {
  auto block_pool_manager = new BlockPoolManager(dn_);
  block_pool_manager->StartActorManager();
  dn_->SetBlockPoolManager(block_pool_manager);
  std::unordered_map<std::string, std::string> nn_name_to_addr;
  nn_name_to_addr["active"] = "0.0.0.0";
  auto block_pool_service = std::make_shared<BlockPoolService>(
      nn_name_to_addr, dn_, "test_nns", block_pool_manager);
  block_pool_service->Start();
  auto ns_info =
      new NameSpaceInfo(namespace_id, cluster_id, block_pool_id, create_time,
                        build_version, software_version, fs_id, ns_id, ns_type);
  EXPECT_FALSE(
      block_pool_manager->UnlockedAddBlockPool(block_pool_service).OK());
  block_pool_service->SetNameSpaceInfo(ns_info);
  auto e = block_pool_manager->UnlockedAddBlockPool(block_pool_service);
  EXPECT_TRUE(e.OK());
  // here we suppose that @func Get will return reliable results
  auto bpos = block_pool_manager->Get(block_pool_id);
  EXPECT_EQ(block_pool_service, bpos);
  block_pool_service->Stop();
}

TEST_F(BlockPoolManagerTests, Get) {
  auto block_pool_manager = new BlockPoolManager(dn_);
  block_pool_manager->StartActorManager();
  dn_->SetBlockPoolManager(block_pool_manager);
  std::unordered_map<std::string, std::string> nn_name_to_addr;
  nn_name_to_addr["active"] = "0.0.0.0";
  auto block_pool_service = std::make_shared<BlockPoolService>(
      nn_name_to_addr, dn_, "test_nns", block_pool_manager);
  block_pool_service->Start();
  auto ns_info =
      new NameSpaceInfo(namespace_id, cluster_id, block_pool_id, create_time,
                        build_version, software_version, fs_id, ns_id, ns_type);
  block_pool_service->SetNameSpaceInfo(ns_info);
  auto e = block_pool_manager->UnlockedAddBlockPool(block_pool_service);
  EXPECT_TRUE(e.OK());
  EXPECT_TRUE(block_pool_manager->Get(block_pool_id) == block_pool_service);
  EXPECT_TRUE(block_pool_manager->Get("test Get") == nullptr);
  block_pool_service->Stop();
}

TEST_F(BlockPoolManagerTests, Remove) {
  auto block_pool_manager = new BlockPoolManager(dn_);
  block_pool_manager->StartActorManager();
  dn_->SetBlockPoolManager(block_pool_manager);
  std::unordered_map<std::string, std::string> nn_name_to_addr;
  nn_name_to_addr["active"] = "0.0.0.0";
  auto block_pool_service = std::make_shared<BlockPoolService>(
      nn_name_to_addr, dn_, "test_nns", block_pool_manager);
  block_pool_service->Start();
  auto ns_info =
      new NameSpaceInfo(namespace_id, cluster_id, block_pool_id, create_time,
                        build_version, software_version, fs_id, ns_id, ns_type);
  block_pool_service->SetNameSpaceInfo(ns_info);
  EXPECT_EQ(block_pool_service->GetNameSpaceInfo(), ns_info);
  EXPECT_EQ(block_pool_service->GetNameSpaceInfo()->GetBlockPoolID(),
            block_pool_id);
  auto e = block_pool_manager->UnlockedAddBlockPool(block_pool_service);
  EXPECT_TRUE(e.OK());
  EXPECT_EQ(block_pool_manager->Get(block_pool_id), block_pool_service);
  block_pool_manager->Remove(block_pool_service);
  EXPECT_TRUE(block_pool_manager->Get(block_pool_id) == nullptr);
  e = block_pool_manager->UnlockedAddBlockPool(block_pool_service);
  EXPECT_TRUE(e.OK());
  block_pool_manager->Remove(std::to_string(namespace_id),
                             std::string(block_pool_id));
  EXPECT_TRUE(block_pool_manager->Get(block_pool_id) == nullptr);
  block_pool_service->Stop();
}

TEST_F(BlockPoolManagerTests, GetAllNamenodeThreads) {
  auto block_pool_manager = new BlockPoolManager(dn_);
  block_pool_manager->StartActorManager();
  dn_->SetBlockPoolManager(block_pool_manager);
  // test when namespaces are empty
  EXPECT_TRUE(block_pool_manager->GetAllNamenodeThreads().empty());
  std::vector<std::string> nns{"ns0", "ns1"};
  auto&& e = block_pool_manager->StartRefreshNamespaceConfig();
  std::cerr << "2" << std::endl;
  EXPECT_TRUE(e.OK());
  sleep(1);
  // test when add new namespaces
  EXPECT_FALSE(block_pool_manager->GetAllNamenodeThreads().empty());
  EXPECT_EQ(block_pool_manager->GetAllNamenodeThreads().size(), 2);
}

TEST_F(BlockPoolManagerTests, GetBlockPoolID) {
  auto block_pool_manager = new BlockPoolManager(dn_);
  block_pool_manager->StartActorManager();
  dn_->SetBlockPoolManager(block_pool_manager);
  EXPECT_TRUE(block_pool_manager->GetBlockPoolID("-1").empty());
  std::unordered_map<std::string, std::string> nn_name_to_addr;
  nn_name_to_addr["active"] = "0.0.0.0";
  auto block_pool_service = std::make_shared<BlockPoolService>(
      nn_name_to_addr, dn_, "test_nns", block_pool_manager);
  block_pool_service->Start();
  auto ns_info =
      new NameSpaceInfo(namespace_id, cluster_id, block_pool_id, create_time,
                        build_version, software_version, fs_id, ns_id, ns_type);
  block_pool_service->SetNameSpaceInfo(ns_info);
  auto e = block_pool_manager->UnlockedAddBlockPool(block_pool_service);
  EXPECT_TRUE(e.OK());
  //  EXPECT_TRUE(block_pool_manager->GetBlockPoolID(
  //                  std::to_string(namespace_id)) ==
  //                  std::string(block_pool_id));
  EXPECT_TRUE(block_pool_manager->GetBlockPoolID("-123").empty());
  block_pool_service->Stop();
}

}  // namespace bds::dancedn::cloudfs
