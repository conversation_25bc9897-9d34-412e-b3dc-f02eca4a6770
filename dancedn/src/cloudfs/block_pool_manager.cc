// Copyright (c) 2019-present, ByteDance Inc. All rights reserved.

#include "cloudfs/block_pool_manager.h"

#include <cassert>
#include <cstdint>
#include <string>
#include <unordered_map>
#include <vector>

#include "byte/base/atomic.h"
#include "byte/string/algorithm.h"
#include "cloudfs/block_list_as_longs.h"
#include "cloudfs/block_pool_actor.h"
#include "cloudfs/block_pool_service.h"
#include "cloudfs/datanode.h"
#include "cloudfs/datanode_config.h"
#include "cloudfs/datanode_storage.h"
#include "cloudfs/exceptions.h"
#include "cloudfs/heartbeat_trigger.h"
#include "cloudfs/metrics.h"
#include "cloudfs/store.h"
#include "cloudfs/thread.h"
#include "cloudfs/util.h"

DECLARE_string(bytestore_cfs_load_namespace_config);

namespace bds::dancedn::cloudfs {

BlockPoolManager::BlockPoolManager()
    : dn_(nullptr),
      actor_manager_(nullptr),
      heartbeat_monitor_(),
      namespace_refresher_(nullptr),
      heartbeat_trigger_(new HeartbeatTrigger()) {}

BlockPoolManager::BlockPoolManager(DataNode* dn)
    : dn_(dn),
      heartbeat_monitor_(),
      namespace_refresher_(nullptr),
      heartbeat_trigger_(new HeartbeatTrigger()) {
  actor_manager_ = new ThreadManager();
}

BlockPoolManager::~BlockPoolManager() {
  delete actor_manager_;
  // Here was a conflict between clearing bpmap and destructing bp.
  // Force to erase shared bp_ptr first, then destruct the bp.
  /*
  for (auto iter = bpid_to_bps_.begin(); iter != bpid_to_bps_.end();) {
      bpid_to_bps_.erase(iter++);
  }
  */
  bpid_to_bps_.clear();
  nsid_to_bpid_.clear();
  /*
  for (auto iter = nsid_to_bps_.begin(); iter != nsid_to_bps_.end();) {
      nsid_to_bps_.erase(iter++);
  }
  */
  nsid_to_bps_.clear();
  // bps_.clear();
  // remove all information in bp_manager_report_ when destructed
  bp_manager_report_.block_pool_reports.clear();
  heartbeat_monitor_.Stop();
  heartbeat_monitor_.Join();
}

bool BlockPoolManager::StartActorManager() {
  bool ret = actor_manager_->Start();
  heartbeat_trigger_->Start();
  heartbeat_monitor_.Init(
      NewClosure<void>(this, &BlockPoolManager::CheckHeartbeat));
  heartbeat_monitor_.Start();
  return ret;
}

void BlockPoolManager::AddActor(Thread* actor) {
  actor_manager_->AddWorker(actor);
}

void BlockPoolManager::DeleteActor(Thread* actor) {
  actor_manager_->DeleteWorker(actor);
}

exceptions::Exception BlockPoolManager::UnlockedAddBlockPool(
    BPServiceSharedPtr bpos) {
  byte::MutexLocker guard(&mutex_);
  std::string bpid = bpos->GetBlockPoolIDUnlock();
  if (bpid.empty()) {
    return exceptions::Exception(exceptions::E::kIllegalArgumentException,
                                 "Null blockpool id");
  }
  uint64_t ns_id = bpos->GetNameSpaceInfo()->GetNsID();
  BYTE_ASSERT(ParseNsIdFromBpid(bpid) == ns_id) << "unexpected bpid " << bpid;
  auto itr = bpid_to_bps_.find(bpid);
  if (itr == bpid_to_bps_.end()) {
    bpid_to_bps_.emplace(bpid, bpos);
  } else {
    if (itr->second.get() != bpos.get()) {
      bpid_to_bps_.emplace(bpid, bpos);
    }
  }
  nsid_to_bpid_[ns_id] = bpid;

  return exceptions::Exception();
}

/*
bool BlockPoolManager::AddBlockPool(const std::string& namespace_id,
                                    const std::string& block_pool_id) {
    byte::MutexLocker guard(&mutex_);

    if (block_pool_id.empty()) {
        LOG(INFO) << "Null block pool id";
        return false;
    }

    auto itr = nsid_to_bps_.find(namespace_id);
    if (itr == nsid_to_bps_.end()) {
        LOG(INFO) << "Unknown BlockPoolService: " << namespace_id;
        return false;
    }

    bpid_to_bps_.emplace(block_pool_id, itr->second);
    return true;
}
*/

exceptions::Exception BlockPoolManager::StartRefreshNamespaceConfig() {
  LOG(INFO) << "Start to refresh namespace config automatically";
  namespace_refresher_.reset(new NamespaceInfoRefresher(dn_, this));
  return namespace_refresher_->StartRefresh();
}

std::vector<BlockPoolManager::BPServiceSharedPtr>
BlockPoolManager::GetAllNamenodeThreads() const {
  byte::MutexLocker guard(&mutex_);
  return ToBPServiceArray();
}

std::vector<BlockPoolManager::BPServiceSharedPtr>
BlockPoolManager::ToBPServiceArray() const {
  std::vector<BPServiceSharedPtr> services;
  for (auto& item : nsid_to_bps_) {
    services.emplace_back(item.second);
  }
  return services;
}

BlockPoolManager::BPServiceSharedPtr BlockPoolManager::Get(
    const std::string& bpid) {
  byte::MutexLocker guard(&mutex_);
  auto itr = bpid_to_bps_.find(bpid);
  if (itr != bpid_to_bps_.end()) {
    return itr->second;
  }
  return nullptr;
}

void BlockPoolManager::Remove(const BPServiceSharedPtr& bpos) {
  byte::MutexLocker guard(&mutex_);
  auto bpid = bpos->GetBlockPoolIDUnlock();
  if (!bpid.empty()) {
    bpid_to_bps_.erase(bpid);
  }

  BYTE_ASSERT(ParseNsIdFromBpid(bpid) == bpos->GetNameSpaceInfo()->GetNsID())
      << "unexpected bpid " << bpid;
  nsid_to_bpid_.erase(bpos->GetNameSpaceInfo()->GetNsID());
  std::string nsid;
  for (auto iter : nsid_to_bps_) {
    if (iter.second == bpos) {
      nsid = iter.first;
      break;
    }
  }

  if (!nsid.empty()) {
    nsid_to_bps_.erase(nsid);
    LOG(INFO) << "Removed " << bpos->ToString();
  }
  return;
}

void BlockPoolManager::Remove(const std::string& nsid,
                              const std::string& bpid) {
  byte::MutexLocker guard(&mutex_);
  nsid_to_bps_.erase(nsid);
  bpid_to_bps_.erase(bpid);
  nsid_to_bpid_.erase(ParseNsIdFromBpid(bpid));
}

BlockPoolManager::BPServiceSharedPtr BlockPoolManager::GetByNsID(
    const std::string& nsid) const {
  byte::MutexLocker guard(&mutex_);
  auto itr = nsid_to_bps_.find(nsid);
  if (itr != nsid_to_bps_.end()) {
    return itr->second;
  }
  return nullptr;
}

exceptions::Exception BlockPoolManager::DoRefreshNamenodes(
    const DataNodeConfig::NamespaceMap& name_service_map) {
  std::set<std::string> to_refresh;
  std::set<std::string> to_add;
  std::set<std::string> to_remove;
  const std::string& dnproxy = dn_->GetDataNodeConfig()->GetDNProxy();

  auto parse_nn_addr_item =
      [&](const std::string& addr_item, std::vector<std::string>* addrs,
          std::unordered_map<std::string, std::string>* nn_name_to_addr) {
        if (dnproxy.empty()) {
          std::vector<std::string> elems;
          SplitStringForNamenode(addr_item, ":", &elems);
          BYTE_ASSERT(elems.size() == 3);
          TrimIPString(&elems[1]);
          (*nn_name_to_addr)[elems[0]] = elems[1] + ":" + elems[2];
          addrs->push_back(elems[0]);
        } else {
          (*nn_name_to_addr)[addr_item] = dnproxy;
          addrs->push_back(addr_item);
        }
      };

  {
    byte::MutexLocker guard(&mutex_);

    // to_refresh/to_add
    for (auto& item : name_service_map) {
      if (nsid_to_bps_.find(item.first) != nsid_to_bps_.end()) {
        to_refresh.emplace(item.first);
      } else {
        to_add.emplace(item.first);
      }
    }

    for (auto& item : nsid_to_bps_) {
      if (name_service_map.find(item.first) == name_service_map.end()) {
        to_remove.emplace(item.first);
      }
    }

    if (!to_add.empty()) {
      LOG(INFO)
          << "DoRefreshNamenodes Starting BlockPoolServices for nameservces: "
          << Joiner(",").Join(to_add);

      for (auto& ns : to_add) {
        auto nns = name_service_map.find(ns)->second;
        std::vector<std::string> addrs;
        std::unordered_map<std::string, std::string> nn_name_to_addr;
        auto iter = nns.begin();
        while (iter != nns.end()) {
          parse_nn_addr_item(*iter, &addrs, &nn_name_to_addr);
          iter++;
        }
        LOG(INFO) << "add nn:" << ns
                  << " name:" << byte::JoinStrings(addrs, ",");
        auto bp =
            std::make_shared<BlockPoolService>(nn_name_to_addr, dn_, ns, this);
        nsid_to_bps_.emplace(ns, bp);
        if (!bp->Start()) {
          return exceptions::Exception(
              exceptions::kIOException,
              "Refresh namenodes failed when start bpos");
        }
      }
    }
  }

  if (!to_remove.empty()) {
    LOG(INFO) << "Stopping BlockPoolServices for nameservices: "
              << Joiner(",").Join(to_remove);

    for (auto& ns : to_remove) {
      std::shared_ptr<BlockPoolService> service = nullptr;
      {
        byte::MutexLocker guard(&mutex_);
        auto&& iter = nsid_to_bps_.find(ns);
        if (iter != nsid_to_bps_.end()) {
          service = iter->second;
        }
      }
      if (service != nullptr) {
        service->Stop();
      }
    }
  }

  if (!to_refresh.empty()) {
    LOG(INFO) << "Refreshing list of NNs for nameservices: "
              << Joiner(",").Join(to_refresh);

    for (auto& ns : to_refresh) {
      auto nns = name_service_map.find(ns)->second;
      std::vector<std::string> addrs;
      std::unordered_map<std::string, std::string> nn_name_to_addr;
      auto iter = nns.begin();
      while (iter != nns.end()) {
        parse_nn_addr_item(*iter, &addrs, &nn_name_to_addr);
        iter++;
      }
      std::shared_ptr<BlockPoolService> service = nullptr;
      {
        byte::MutexLocker guard(&mutex_);
        auto&& iter = nsid_to_bps_.find(ns);
        if (iter != nsid_to_bps_.end()) {
          service = iter->second;
        }
      }
      if (service != nullptr) {
        LOG(INFO) << "refresh nn:" << ns
                  << " name:" << byte::JoinStrings(addrs, ",");
        service->RefreshNNList(nn_name_to_addr, addrs);
      }
    }
  }
  return exceptions::Exception();
}

bool BlockPoolManager::StartAll(const std::vector<BPServiceSharedPtr>& bps) {
  for (size_t i = 0; i < bps.size(); i++) {
    if (!bps[i]->Start()) return false;
  }
  return true;
}

/*
void BlockPoolManager::StopAll() {
    const std::vector<BPServiceSharedPtr>& bps = GetAllNamenodeThreads();
    for (size_t i = 0; i < bps.size(); i++) {
        bps[i]->Stop();
    }
}
*/

void BlockPoolManager::ShutDownAll(const std::vector<BPServiceSharedPtr>& bps) {
  for (size_t i = 0; i < bps.size(); i++) {
    bps[i]->Stop();
  }
  // JoinAll(bps);
}

void BlockPoolManager::JoinAll(const std::vector<BPServiceSharedPtr>& bps) {
  for (size_t i = 0; i < bps.size(); i++) {
    bps[i]->Join();
  }
}

void BlockPoolManager::JoinAll() {
  JoinAll(GetAllNamenodeThreads());
}

bool BlockPoolManager::StartAll() {
  return StartAll(GetAllNamenodeThreads());
}

void BlockPoolManager::ShutDownAll() {
  ShutDownAll(GetAllNamenodeThreads());
}

exceptions::Exception BlockPoolManager::TriggerBlockReport(
    const BlockReportOptions& options) const {
  for (auto bps : GetAllNamenodeThreads()) {
    if (bps != nullptr) {
      auto e = bps->TriggerBlockReport(options);
      if (!e.OK()) return e;
    }
  }
  return exceptions::Exception();
}

exceptions::Exception BlockPoolManager::TriggerBlockReport(
    const std::string& nn_backend, const BlockReportOptions& options) const {
  auto bps = GetByNsID(nn_backend);
  if (bps == nullptr) {
    return exceptions::Exception(exceptions::kIOException,
                                 "nn_backend doesn't exist");
  }
  return bps->TriggerBlockReport(options);
}

std::string BlockPoolManager::GetBlockPoolID(const std::string& nsid) const {
  BPServiceSharedPtr bpservice = GetByNsID(nsid);
  if (bpservice == nullptr) {
    return std::string("");
  } else {
    return bpservice->GetBlockPoolID();
  }
}

void BlockPoolManager::ReportBlockPoolService(
    const std::string& ns_name,
    const BlockPoolServiceReport& bp_service_report) {
  byte::MutexLocker guard(&mutex_);
  bp_manager_report_.block_pool_reports[ns_name] = bp_service_report;
}

void BlockPoolManager::CheckHeartbeat() {
  while (!heartbeat_monitor_.IsStopped()) {
    {
      byte::MutexLocker guard(&mutex_);
      for (auto iter = nsid_to_bps_.begin(); iter != nsid_to_bps_.end();
           iter++) {
        auto&& actors = iter->second->GetActors();
        for (BlockPoolActor* actor : actors) {
          uint64_t last_heart_beat = actor->GetLastHeartbeat();
          uint64_t now = byte::GetCurrentTimeInMs();
          // sometimes others will set last_heart_beat = 0 to trigger heartbeat
          // immediately, and we should skip this case
          if (last_heart_beat != 0 && now > last_heart_beat + 60 * 1000) {
            bytestore::Tags tags = {{"id", actor->GetId()}};
            LOG_EXCEPTION_WITH_TAGS(ERROR, heartbeat_timeout, tags)
                << "Last heartbeat start on " << last_heart_beat
                << " while now is " << now << ", id: " << actor->GetId();
          }
        }
      }
    }
    heartbeat_monitor_.WaitFor(5000);
  }
}

exceptions::Exception BlockPoolManager::GetBpid(uint64_t nsid,
                                                std::string* bpid) {
  byte::MutexLocker guard(&mutex_);
  auto iter = nsid_to_bpid_.find(nsid);
  if (iter == nsid_to_bpid_.end()) {
    return exceptions::Exception(exceptions::E::kBpidNotFound);
  }
  *bpid = iter->second;
  return exceptions::Exception();
}

uint64_t BlockPoolManager::ParseNsIdFromBpid(const std::string& bpid) {
  size_t first_dash = bpid.find('-');
  size_t second_dash = bpid.find('-', first_dash + 1);
  if (UNLIKELY(first_dash == std::string::npos ||
               second_dash == std::string::npos)) {
    LOG(ERROR) << "Unexpected bpid " << bpid << std::endl;
    return 0;
  }
  return std::stoull(bpid.substr(first_dash + 1, second_dash - first_dash - 1));
}

std::string BlockPoolManager::ParseNsIdStrFromBpid(const std::string& bpid) {
  size_t first_dash = bpid.find('-');
  size_t second_dash = bpid.find('-', first_dash + 1);
  if (UNLIKELY(first_dash == std::string::npos ||
               second_dash == std::string::npos)) {
    LOG(ERROR) << "Unexpected bpid " << bpid << std::endl;
    return "";
  }
  return bpid.substr(first_dash + 1, second_dash - first_dash - 1);
}

}  // namespace bds::dancedn::cloudfs
