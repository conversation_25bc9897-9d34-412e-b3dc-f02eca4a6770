// Copyright (c) 2018-present, ByteDance Inc. All rights reserved.

#include "cloudfs/client_datanode_service_impl.h"

#include <cstring>
#include <deque>
#include <fstream>
#include <iostream>
#include <memory>
#include <set>
#include <unordered_map>
#include <vector>

#include "butil/crc32c.h"
#include "byte/io/local_filesystem.h"
#include "cloudfs/block.h"
#include "cloudfs/block_local_path_info.h"
#include "cloudfs/block_pool_actor.h"
#include "cloudfs/block_pool_manager.h"
#include "cloudfs/block_pool_service.h"
#include "cloudfs/caching_strategy.h"
#include "cloudfs/constants.h"
#include "cloudfs/datanode.h"
#include "cloudfs/datanode_config.h"
#include "cloudfs/datanode_id.h"
#include "cloudfs/datanode_info.h"
#include "cloudfs/datanode_registration.h"
#include "cloudfs/datanode_storage.h"
#include "cloudfs/directory_scanner.h"
#include "cloudfs/exceptions.h"
#include "cloudfs/extended_block.h"
#include "cloudfs/finalized_replica.h"
#include "cloudfs/hdfs_blocks_metadata.h"
#include "cloudfs/message/calculate_block_crc.h"
#include "cloudfs/message/get_block_local_path_info_message.h"
#include "cloudfs/message/get_blocks_message.h"
#include "cloudfs/message/get_hdfs_block_locations_message.h"
#include "cloudfs/message/get_replica_visible_length_message.h"
#include "cloudfs/message/get_replica_visible_length_v2_message.h"
#include "cloudfs/message/init_replica_recovery_message.h"
#include "cloudfs/message/read_block_message.h"
#include "cloudfs/message/refresh_namenodes_message.h"
#include "cloudfs/message/rpc_request_message.h"
#include "cloudfs/message/rpc_response_message.h"
#include "cloudfs/message/seal_block_message.h"
#include "cloudfs/message/shutdown_datanode_message.h"
#include "cloudfs/message/trigger_block_report_message.h"
#include "cloudfs/message/update_replica_under_recovery_message.h"
#include "cloudfs/mocks/mock_datanode.h"
#include "cloudfs/mocks/mock_store.h"
#include "cloudfs/namespace_info.h"
#include "cloudfs/recovering_block.h"
#include "cloudfs/replica_in_pipeline.h"
#include "cloudfs/replica_info.h"
#include "cloudfs/replica_recovery_info.h"
#include "cloudfs/replica_state.h"
#include "cloudfs/services/hdfs_io_service.h"
#include "cloudfs/store.h"
#include "cloudfs/store/unified_block_store.h"
#include "cloudfs_proto/InterDatanodeProtocol.pb.h"
#include "cloudfs_proto/ProtobufRpcEngine.pb.h"
#include "cloudfs_proto/RpcHeader.pb.h"
#include "cloudfs_proto/hdfs.pb.h"
#include "common/memory_pool.h"
#include "gmock/gmock-spec-builders.h"
#include "gmock/gmock.h"
#include "gtest/gtest.h"
#include "services/csioservice_impl.h"

DECLARE_string(bytestore_hdfs_config_file);
DECLARE_string(bytestore_hdfs_nn_config_file);

namespace bds::dancedn::cloudfs {
// Prepare configuration contents; should be saved as an individual file in
// future.
static const std::string& k_test_content = R"(
{
    "BlockReportInterval": 60,
    "InitialBlockReportDelay": 300,
    "MinimunNameNodeVersion": "2.6.0",
    "DeleteReportInterval": 50,
    "HeartBeatInterval": 70,
    "FailoverBlockReportDelay": 80,
    "BlockReportSplitThreshold": 99,
    "SocketKeepaliveTimeout": 120,
    "SocketTimeout": 120,
    "IOFileBufferSize":4096,
    "EstimateBlockSize": 51200,
    "RestartReplicaExpiry": 333,
    "DatanodeSlowIoWarningThresholdMs": 334,
    "SyncBehindWrites": false,
    "GetSyncBehindWriteInBackground": true
})";

static const std::string& k_test_nn_content = R"(
{
    "Namenodes": {
         "ns0": ["nn0", "nn1"],
         "ns1": ["nn3"]
    },
    "DNProxy": "dnproxy:5082",
    "Version": "1.0.0.1"
})";

static const char BLOCK_POOL_ID[] = "BP-3845026443837452998-1546064706393";
static const char EXCEPTION_BLOCK_POOL_ID[] =
    "BP-3845026443837452998-6666666666666";
static const char LOCAL_BLOCK_PATH[] = "/data01/yarn/dndata/subdir01";
static const char LOCAL_META_PATH[] = "/data01/yarn/dndata/meta";
static const char STORAGE_UUID[] = "1234565667";
static const char EXCEPTION_STORAGE_UUID[] = "666666666";
static const char DATANODE_UUID[] = "1234";

static const int32_t CALL_ID = 1;
static const char CLIENT_ID[] = "my client";
static const int32_t RETRY_COUNT = -1;

static const uint64_t FINALIZED_REPLICA_BLOCK_ID = 123456;
static const uint64_t TEMPORARY_REPLICA_BLOCK_ID = 123457;

static const int64_t FINALIZED_REPLICA_LENGTH = 128;
static const int64_t TEMPORARY_REPLICA_LENGTH = -1;

static const uint64_t IO_EXCEPTION_BLOCK_ID = 654321;
static const char IO_EXCEPTION_ERR[] = "generation stamp error";
static const char IO_EXCEPTION_ERROR_MESSAGE[] =
    "java.io.IOException: generation stamp error";
static const char IO_EXCEPTION_CLASS_NAME[] = "java.io.IOException";

static const uint64_t REPLICA_NOT_FOUND_EXCEPTION_BLOCK_ID = 654322;
static const char REPLICA_NOT_FOUND_EXCEPTION_ERR[] =
    "Cannot append to a non-existent replica";
static const char REPLICA_NOT_FOUND_EXCEPTION_ERROR_MESSAGE[] =
    "com.bytedance.cloudfs.hdfs.server.datanode.ReplicaNotFoundException: "
    "Cannot append to a non-existent replica";
static const char REPLICA_NOT_FOUND_EXCEPTION_CLASS_NAME[] =
    "com.bytedance.cloudfs.hdfs.server.datanode.ReplicaNotFoundException";

static const char NULLPOINT_EXCEPTION_CLASS_NAME[] =
    "java.lang.NullPointerException";
static const char STORAGE_NOT_INITIAL_ERROR_MESSAGE[] =
    "java.lang.NullPointerException: Storage not yet initialized";

static const char UNSUPPORTED_OPERATION_EXCEPTION_CLASS_NAME[] =
    "java.lang.UnsupportedOperationException";
static const char UNSUPPORTED_OPERATION_EXCEPTION_ERROR_MESSAGE[] =
    "java.lang.UnsupportedOperationException: "
    "Datanode#GetHdfsBlocksMetadata is not enabled in datanode config";

static const char ILLEGAL_ARGUMENT_EXCEPTION_CLASS_NAME[] =
    "java.lang.IllegalArgumentException";
static const char ILLEGAL_ARGUMENT_EXCEPTION_ERROR_MESSAGE[] =
    "java.lang.IllegalArgumentException: "
    "BUG: StorageType not found, type=7";

static const char RECOVERY_IN_PROGRESS_EXCEPTION_CLASS_NAME[] =
    "com.bytedance.cloudfs.hdfs.protocol.RecoveryInProgressException";

static const char CORRUPTED_VOLUME_MAP[] =
    "java.io.IOException: VolumeMap is corrupted";

static const char bp_id[] = "tbpi";
static const uint64_t blk_gs0 = 1;
static const uint64_t blk_gs1 = 2;
static const uint64_t blk_id = 20190828;
static const uint64_t blk_len = 4096;
std::unique_ptr<ExtendedBlock> test_block(new ExtendedBlock(bp_id, blk_id,
                                                            blk_len, blk_gs0,
                                                            false));

// DatanodeIDs
static const char ip_addr[] = "127.0.0.1";
static const char hostname[] = "localhost";
static const char uuid[] = "duuid001";
static const int xfer_port = 5060;
static const int info_port = 5061;
static const int ipc_port = 5062;
static const char node_name[] = "";
std::shared_ptr<DatanodeID> local_dn_id(new DatanodeID(
    ip_addr, hostname, uuid, xfer_port, info_port, ipc_port, node_name,
    std::vector<std::string>(), std::vector<std::string>()));

static const char ip_addr1[] = "127.0.0.1";
static const char hostname1[] = "remote";
static const char uuid1[] = "duuid002";
std::shared_ptr<DatanodeID> remote_dn_id1(new DatanodeID(
    ip_addr1, hostname1, uuid1, xfer_port, info_port, ipc_port, node_name,
    std::vector<std::string>(), std::vector<std::string>()));

static const char ip_addr2[] = "127.0.0.1";
static const char hostname2[] = "remote";
static const char uuid2[] = "duuid002";
std::shared_ptr<DatanodeID> remote_dn_id2(new DatanodeID(
    ip_addr2, hostname2, uuid2, xfer_port, info_port, ipc_port, node_name,
    std::vector<std::string>(), std::vector<std::string>()));

std::vector<DatanodeInfo*> dn_infos = {new DatanodeInfo(local_dn_id),
                                       new DatanodeInfo(remote_dn_id1),
                                       new DatanodeInfo(remote_dn_id2)};

std::unique_ptr<RecoveringBlock> rb(new RecoveringBlock(test_block->Clone(),
                                                        dn_infos, blk_gs1,
                                                        true));

static const char sid[] = "suuid";

}  // namespace bds::dancedn::cloudfs

namespace bds::dancedn::cloudfs {

class MockStorage : public UnifiedBlockStore {
 public:
  MockStorage() : uuid_(), storage_("mock") {}
  ~MockStorage() {}

  double GetDiskIoutil(const std::string& disk_dir) {
    return 0.0;
  }

  exceptions::Exception InitStorage(
      DataNode* dn, const bytestore::chunkserver::DiskIdConfMap* disk_config) {
    return exceptions::Exception();
  }

  exceptions::Exception AddDirAndBlock(const NameSpaceInfo* ns_info) {
    return exceptions::Exception();
  }

  std::string GetDatanodeUUID() const {
    return uuid_;
  }
  StorageInfo* GetBPStorage(const std::string& pool_id) const {
    return nullptr;
  }

  void GetVolumeReports(
      std::unordered_map<bytestore::DiskId,
                         std::shared_ptr<StorageToBlockPoolReport>>*
          disk_id_to_bp_used,
      std::unordered_map<uint64_t, uint64_t>* bp_to_bp_total_used) {
    return;
  }

  exceptions::Exception GetStorageReports(
      const std::string& pool_id, std::vector<StorageReport*>* reports) {
    return exceptions::Exception();
  }

  VolumeFailureSummary* GetVolumeFailureSummary() const {
    return nullptr;
  }

  DatanodeStorage GetStorage(const std::string& storage_uuid) const {
    return storage_;
  }

  void SetUUID(const std::string& uuid) {
    uuid_ = uuid;
  }

  exceptions::Exception GetReplicaVisibleLength(const ExtendedBlock& block,
                                                int64_t* res) const {
    uint64_t block_id = block.GetBlock()->GetBlockID();
    switch (block_id) {
      case IO_EXCEPTION_BLOCK_ID:
        return exceptions::Exception(exceptions::kIOException,
                                     std::string(IO_EXCEPTION_ERR));
      case REPLICA_NOT_FOUND_EXCEPTION_BLOCK_ID:
        return exceptions::Exception(
            exceptions::kReplicaNotFoundException,
            std::string(REPLICA_NOT_FOUND_EXCEPTION_ERR));
      case FINALIZED_REPLICA_BLOCK_ID: *res = FINALIZED_REPLICA_LENGTH; break;
      case TEMPORARY_REPLICA_BLOCK_ID: *res = TEMPORARY_REPLICA_LENGTH; break;
    }
    return exceptions::Exception();
  }

  exceptions::Exception GetReplicaVisibleLengthV2(
      const ExtendedBlock& block, int64_t* res, int32_t* replica_state) const {
    uint64_t block_id = block.GetBlock()->GetBlockID();
    switch (block_id) {
      case IO_EXCEPTION_BLOCK_ID:
        return exceptions::Exception(exceptions::kIOException,
                                     IO_EXCEPTION_ERR);
      case FINALIZED_REPLICA_BLOCK_ID:
        *res = FINALIZED_REPLICA_LENGTH;
        *replica_state = ReplicaState::FINALIZED;
        break;
    }
    return exceptions::Exception();
  }

  exceptions::Exception GetBlockLocalPathInfo(const ExtendedBlock& block,
                                              BlockLocalPathInfo* res) const {
    if (block.GetBlock()->GetBlockID() == IO_EXCEPTION_BLOCK_ID) {
      return exceptions::Exception(exceptions::kIOException,
                                   std::string(IO_EXCEPTION_ERR));
    }
    res->SetBlock(block);
    res->SetBlockPath(LOCAL_BLOCK_PATH);
    res->SetMetaPath(LOCAL_META_PATH);
    return exceptions::Exception();
  }

  exceptions::Exception GetHdfsBlocksMetadata(
      const std::string& pool_id, const std::vector<uint64_t>& block_ids,
      HdfsBlocksMetadata* res) const {
    if (block_ids.size() == 0) {
      return exceptions::Exception(exceptions::kIOException,
                                   std::string(IO_EXCEPTION_ERR));
    }
    res->SetBlockPoolId(pool_id);
    res->SetBlockIds(block_ids);
    std::vector<std::vector<uint8_t>> volume_ids;
    volume_ids.push_back({65, 66, 67});
    volume_ids.push_back({68, 69, 70});
    volume_ids.push_back({71, 72, 73});
    res->SetVolumeIds(volume_ids);

    std::vector<uint32_t> volume_indexes;
    for (size_t i = 0; i < block_ids.size(); i++) {
      volume_indexes.emplace_back(i % 3);
    }
    res->SetVolumeIndexes(volume_indexes);
    return exceptions::Exception();
  }

  std::vector<std::shared_ptr<FinalizedReplica>>
  GetFinalizedBlocksOnPersistentStorage(
      const std::string& block_pool_id) const {
    std::vector<std::shared_ptr<FinalizedReplica>> replicas;
    if (block_pool_id == EXCEPTION_BLOCK_POOL_ID) {
      for (int i = 0; i < 3; i++) {
        ExtendedBlock block(block_pool_id, 123456 + i, 128, 123456 + i, false);
        auto replica = std::make_shared<FinalizedReplica>(
            &block, EXCEPTION_STORAGE_UUID, false, false, 0, false);
        replicas.push_back(replica);
      }
      return replicas;
    }

    for (int i = 0; i < 3; i++) {
      ExtendedBlock block(block_pool_id, 123456 + i, 128, 123456 + i, false);
      auto replica = std::make_shared<FinalizedReplica>(&block, STORAGE_UUID,
                                                        false, false, 0, false);
      replicas.push_back(replica);
    }
    return replicas;
  }

  StorageType GetStorageType(const std::string& storage_uuid) const {
    if (storage_uuid == EXCEPTION_STORAGE_UUID) {
      return StorageType::NULLTYPE;
    }
    return StorageType::DISK;
  }

  std::shared_ptr<ReplicaInfo> GetReplica(const std::string& block_pool_id,
                                          uint64_t block_id) const {
    return std::make_shared<FinalizedReplica>(
        block_pool_id, block_id, FINALIZED_REPLICA_LENGTH, 10001, false, "",
        false, false, 0, false);
  }

  exceptions::Exception ReadChecksum(ExtendedBlock* block, io::IOChunk* chunk,
                                     uint32_t checksum_offset,
                                     uint32_t checksum_data_len) {
    return exceptions::Exception();
  }

  exceptions::Exception WriteChecksum(ExtendedBlock* block, io::IOChunk* chunk,
                                      uint32_t checksum_offset,
                                      uint32_t checksum_data_len, bool sync,
                                      CsIoPriority priority) {
    return exceptions::Exception();
  }

  exceptions::Exception ReadBlock(ExtendedBlock* block, io::IOChunk* chunk,
                                  uint32_t data_offset, uint32_t data_len) {
    return exceptions::Exception();
  }

  exceptions::Exception WriteBlock(ExtendedBlock* block, io::IOChunk* chunk,
                                   uint32_t data_offset, uint32_t data_len,
                                   bool sync, CsIoPriority priority) {
    return exceptions::Exception();
  }

  exceptions::Exception SyncBlock(ExtendedBlock* block, uint64_t written_len) {
    return exceptions::Exception();
  }

  exceptions::Exception RecoverClose(ExtendedBlock* block, uint64_t latest_gs,
                                     uint64_t min_bytes_rcvd,
                                     std::string* storage_uuid,
                                     DataXceiver* xceiver) {
    return exceptions::Exception();
  }

  exceptions::Exception CreateTemporary(
      StorageType storage_type, ExtendedBlock* block,
      std::shared_ptr<ReplicaInPipeline>* replica) {
    return exceptions::Exception();
  }

  exceptions::Exception CreateTemporary(
      StorageType storage_type, ExtendedBlock* block,
      std::shared_ptr<ReplicaInPipeline>* replica, int32_t resident_time) {
    return exceptions::Exception();
  }

  exceptions::Exception CreateRbw(StorageType storage_type,
                                  ExtendedBlock* block, bool allow_lazy_persist,
                                  std::shared_ptr<ReplicaInPipeline>* replica) {
    return exceptions::Exception();
  }

  exceptions::Exception CreateRbw(StorageType storage_type,
                                  ExtendedBlock* block, bool allow_lazy_persist,
                                  std::shared_ptr<ReplicaInPipeline>* replica,
                                  int32_t resident_time) {
    return exceptions::Exception();
  }

  exceptions::Exception RecoverRbw(
      ExtendedBlock* block, uint64_t new_gs, uint64_t min_bytes_rcvd,
      uint64_t max_bytes_rcvd, std::shared_ptr<ReplicaInPipeline>* replica) {
    return exceptions::Exception();
  }

  exceptions::Exception Append(ExtendedBlock* block, uint64_t new_gs,
                               uint64_t min_bytes_rcvd,
                               std::shared_ptr<ReplicaInPipeline>* replica) {
    return exceptions::Exception();
  }

  exceptions::Exception RecoverAppend(
      ExtendedBlock* block, uint64_t new_gs, uint64_t min_bytes_rcvd,
      std::shared_ptr<ReplicaInPipeline>* replica) {
    return exceptions::Exception();
  }

  exceptions::Exception ReadDiskChecksum(ExtendedBlock* block,
                                         DataChecksum** disk_checksum) {
    return exceptions::Exception();
  }

  exceptions::Exception WriteBlockMetaHeader(ExtendedBlock* block,
                                             DataChecksum* disk_checksum,
                                             StorageType storage_type,
                                             int32_t resident_time) {
    return exceptions::Exception();
  }

  exceptions::Exception FinalizeBlock(ExtendedBlock* block) {
    return exceptions::Exception();
  }

  exceptions::Exception UnFinalizeBlock(ExtendedBlock* block) {
    return exceptions::Exception();
  }

  exceptions::Exception ConvertTemporaryToRbw(
      ExtendedBlock* block, std::shared_ptr<ReplicaInPipeline>* replica) {
    return exceptions::Exception();
  }

  bool IsValidRbw(ExtendedBlock* block) const {
    return false;
  }

  bool IsValidBlock(ExtendedBlock* block) const {
    return false;
  }

  exceptions::Exception CheckBlock(ExtendedBlock* block, uint64_t min_len,
                                   ReplicaState state) const {
    return exceptions::Exception();
  }

  exceptions::Exception GetBlockLength(ExtendedBlock* block,
                                       uint64_t* length) const {
    *length = 0;
    return exceptions::Exception();
  }

  exceptions::Exception MoveBlockAcrossStorage(const ExtendedBlock* block,
                                               const StorageType& storage_type,
                                               ReplicaInfo** replica_info) {
    return exceptions::Exception();
  }

  exceptions::Exception InitReplicaRecovery(const RecoveringBlock* rblock,
                                            ReplicaRecoveryInfo** rinfo) {
    Block* blk = rblock->GetBlock()->GetBlock();
    *rinfo = new ReplicaRecoveryInfo(blk->GetBlockID(), blk->GetNumBytes(),
                                     blk->GetGS(), ReplicaState::FINALIZED);
    return exceptions::Exception();
  }

  exceptions::Exception UpdateReplicaUnderRecovery(
      const ExtendedBlock* old_block, uint64_t recovery_id, uint64_t new_length,
      std::string* storage_id) {
    *storage_id = sid;
    return exceptions::Exception();
  }

  exceptions::Exception Invalidate(const std::string& bpid,
                                   const std::vector<Block*>& blocks) {
    return exceptions::Exception();
  }

  std::set<std::string> CheckDataDir() {
    return std::set<std::string>();
  }

  exceptions::Exception CloseVolumes(
      const std::set<std::string>& volumes_to_close) {
    return exceptions::Exception();
  }

  exceptions::Exception RemoveVolumes(
      const std::set<std::string>& absolute_volume_paths) {
    return exceptions::Exception();
  }

  exceptions::Exception AcquireActivity(uint32_t disk_id,
                                        bool* available) const {
    return exceptions::Exception();
  }

  void GetCurrentActivities(std::unordered_map<std::string, int>* map) {
    return;
  }

  std::string GetVolumePath(const std::shared_ptr<ReplicaInfo>& replica) const {
    return "";
  }

  void CleanupBlockPool(const std::string& bpid) {
    return;
  }

  CsDisk* GetDisk(bytestore::DiskId disk_id) const {
    return nullptr;
  }

  const bytestore::chunkserver::DiskIdConfMap* GetDiskConfig() const {
    return nullptr;
  }

  exceptions::Exception TosUpload(ExtendedBlock* block,
                                  const std::string& object_key,
                                  const std::string& upload_id) {
    return exceptions::Exception();
  }

  exceptions::Exception CreateBlockStream(
      ExtendedBlock* block, uint64_t offset, uint32_t length,
      const CachingStrategy& caching_strategy,
      BlockStream** returned_stream) override {
    *returned_stream = nullptr;
    return exceptions::Exception();
  }

  std::deque<CsDisk*> GetMigratedTargetDisks() {
    std::deque<CsDisk*> deque;
    return deque;
  }

  std::deque<DiskDirectory> GetDisks() {
    std::deque<DiskDirectory> deque;
    return deque;
  }

  exceptions::Exception CheckReplicaForMigrationScanner(ExtendedBlock* block,
                                                        CsChunkId* chunk_id,
                                                        CsDisk* disk) {
    return exceptions::Exception();
  }

  exceptions::Exception UpdateMigratedReplica(ExtendedBlock* block,
                                              bytestore::DiskId dst_disk_id) {
    return exceptions::Exception();
  }

  exceptions::Exception CheckAndUpdate(ExtendedBlock* block,
                                       ReplicaState disk_replica_state,
                                       bytestore::DiskId dst_disk_id,
                                       bool corrupt) {
    return exceptions::Exception();
  }

 private:
  std::string uuid_;
  DatanodeStorage storage_;
};

class ClientDatanodeServiceImplTests : public ::testing::Test {
 public:
  ClientDatanodeServiceImplTests() {}
  ~ClientDatanodeServiceImplTests() {}

  ::cloudfs::RpcRequestHeaderProto* CreateRpcHeader() {
    // construct rpc request header proto
    auto rpc_header = new ::cloudfs::RpcRequestHeaderProto;
    rpc_header->set_callid(CALL_ID);
    rpc_header->set_clientid(CLIENT_ID);
    rpc_header->set_retrycount(RETRY_COUNT);
    auto baggage = rpc_header->add_baggages();
    baggage->set_name("user");
    baggage->set_value("name");
    return rpc_header;
  }

  void SetUp() {
    bytestore::metrics_internal::InitFastMetrics();
  }
  void TearDown() {}

  static void SetUpTestCase() {}
  static void TearDownTestCase() {
    byte::GetLoggingSystem()->Shutdown();
  }
};

TEST_F(ClientDatanodeServiceImplTests, GetReplicaVisibleLength) {
  ClientDatanodeServiceImpl impl;
  std::unique_ptr<DataNode> datanode(new DataNode());
  auto mock_storage = new MockStorage();
  datanode->SetStorage(mock_storage);
  impl.SetDataNode(datanode.get());

  // finalized replica
  // construct rpc request header proto
  auto rpc_header = ClientDatanodeServiceImplTests::CreateRpcHeader();
  auto request_header = new ::cloudfs::RequestHeaderProto();

  // construct getReplicaVisibleLengthRequestProto
  auto request_body = new ::cloudfs::GetReplicaVisibleLengthRequestProto();
  auto block = new ::cloudfs::ExtendedBlockProto();
  block->set_blockid(FINALIZED_REPLICA_BLOCK_ID);
  request_body->set_allocated_block(block);

  // construct RpcRequestMessage
  auto message = impl.NewRequest("getReplicaVisibleLength");
  auto m = static_cast<message::GetReplicaVisibleLengthMessage*>(message);
  m->RpcHeader(rpc_header);
  m->RequestHeader(request_header);
  m->SetRequestBody(request_body);
  auto response = impl.CallMethod(m);
  auto resp_header = response->RpcHeader();
  auto resp_body =
      static_cast<const ::cloudfs::GetReplicaVisibleLengthResponseProto*>(
          response->Body());

  EXPECT_EQ(resp_header->status(),
            ::cloudfs::RpcResponseHeaderProto_RpcStatusProto_SUCCESS);
  EXPECT_EQ(resp_header->callid(), CALL_ID);
  EXPECT_EQ(resp_header->clientid(), CLIENT_ID);
  EXPECT_EQ(resp_header->retrycount(), RETRY_COUNT);
  EXPECT_EQ(resp_header->serveripcversionnum(), RPC_CURRENT_VERSION);
  EXPECT_EQ(resp_body->length(), FINALIZED_REPLICA_LENGTH);

  delete response;

  // temporary replica
  // construct rpc request header proto
  rpc_header = ClientDatanodeServiceImplTests::CreateRpcHeader();
  request_header = new ::cloudfs::RequestHeaderProto();

  // construct getReplicaVisibleLengthRequestProto
  request_body = new ::cloudfs::GetReplicaVisibleLengthRequestProto();
  block = new ::cloudfs::ExtendedBlockProto();
  block->set_blockid(TEMPORARY_REPLICA_BLOCK_ID);
  request_body->set_allocated_block(block);

  // construct RpcRequestMessage
  message = impl.NewRequest("getReplicaVisibleLength");
  m = static_cast<message::GetReplicaVisibleLengthMessage*>(message);
  m->RpcHeader(rpc_header);
  m->RequestHeader(request_header);
  m->SetRequestBody(request_body);

  response = impl.CallMethod(m);
  resp_header = response->RpcHeader();
  resp_body =
      static_cast<const ::cloudfs::GetReplicaVisibleLengthResponseProto*>(
          response->Body());

  EXPECT_EQ(resp_header->status(),
            ::cloudfs::RpcResponseHeaderProto_RpcStatusProto_SUCCESS);
  EXPECT_EQ(resp_header->callid(), CALL_ID);
  EXPECT_EQ(resp_header->clientid(), CLIENT_ID);
  EXPECT_EQ(resp_header->retrycount(), RETRY_COUNT);
  EXPECT_EQ(resp_header->serveripcversionnum(), RPC_CURRENT_VERSION);
  auto len = resp_body->length();
  auto length = *reinterpret_cast<int64_t*>(&len);
  EXPECT_EQ(length, TEMPORARY_REPLICA_LENGTH);

  delete response;
}

TEST_F(ClientDatanodeServiceImplTests, GetReplicaVisibleLengthV2) {
  ClientDatanodeServiceImpl impl;
  std::unique_ptr<DataNode> datanode(new DataNode());
  auto mock_storage = new MockStorage();
  datanode->SetStorage(mock_storage);
  impl.SetDataNode(datanode.get());

  // finalized replica
  // construct rpc request header proto
  auto rpc_header = ClientDatanodeServiceImplTests::CreateRpcHeader();
  auto request_header = new ::cloudfs::RequestHeaderProto();

  // construct getReplicaVisibleLengthRequestProto
  auto request_body = new ::cloudfs::GetReplicaVisibleLengthV2RequestProto();
  auto block = new ::cloudfs::ExtendedBlockProto();
  block->set_blockid(FINALIZED_REPLICA_BLOCK_ID);
  request_body->set_allocated_block(block);

  // construct RpcRequestMessage
  auto message = impl.NewRequest("getReplicaVisibleLengthV2");
  auto m = static_cast<message::GetReplicaVisibleLengthV2Message*>(message);
  m->RpcHeader(rpc_header);
  m->RequestHeader(request_header);
  m->SetRequestBody(request_body);
  auto response = impl.CallMethod(m);
  auto resp_header = response->RpcHeader();
  auto resp_body =
      static_cast<const ::cloudfs::GetReplicaVisibleLengthV2ResponseProto*>(
          response->Body());

  EXPECT_EQ(resp_header->status(),
            ::cloudfs::RpcResponseHeaderProto_RpcStatusProto_SUCCESS);
  EXPECT_EQ(resp_header->callid(), CALL_ID);
  EXPECT_EQ(resp_header->clientid(), CLIENT_ID);
  EXPECT_EQ(resp_header->retrycount(), RETRY_COUNT);
  EXPECT_EQ(resp_header->serveripcversionnum(), RPC_CURRENT_VERSION);
  EXPECT_EQ(resp_body->length(), FINALIZED_REPLICA_LENGTH);
  EXPECT_EQ(resp_body->state(), ReplicaState::FINALIZED);

  delete response;
}

TEST_F(ClientDatanodeServiceImplTests, GetReplicaVisibleLengthWithException) {
  ClientDatanodeServiceImpl impl;
  std::unique_ptr<DataNode> datanode(new DataNode());
  auto mock_storage = new MockStorage();
  datanode->SetStorage(mock_storage);
  impl.SetDataNode(datanode.get());

  // ReplicaNotFoundException
  auto ex_rpc_header = ClientDatanodeServiceImplTests::CreateRpcHeader();

  auto ex_block = new ::cloudfs::ExtendedBlockProto();
  ex_block->set_blockid(REPLICA_NOT_FOUND_EXCEPTION_BLOCK_ID);
  auto ex_request_body = new ::cloudfs::GetReplicaVisibleLengthRequestProto();
  ex_request_body->set_allocated_block(ex_block);
  auto ex_message = impl.NewRequest("getReplicaVisibleLength");
  auto ex_m = static_cast<message::GetReplicaVisibleLengthMessage*>(ex_message);
  ex_m->RpcHeader(ex_rpc_header);
  ex_m->SetRequestBody(ex_request_body);
  auto ex_response = impl.CallMethod(ex_m);
  auto ex_resp_header = ex_response->RpcHeader();

  EXPECT_EQ(ex_resp_header->status(),
            ::cloudfs::RpcResponseHeaderProto_RpcStatusProto_ERROR);
  EXPECT_EQ(ex_resp_header->callid(), CALL_ID);
  EXPECT_EQ(ex_resp_header->clientid(), CLIENT_ID);
  EXPECT_EQ(ex_resp_header->retrycount(), RETRY_COUNT);

  EXPECT_EQ(ex_resp_header->serveripcversionnum(), RPC_CURRENT_VERSION);
  EXPECT_EQ(ex_resp_header->exceptionclassname(),
            REPLICA_NOT_FOUND_EXCEPTION_CLASS_NAME);
  EXPECT_EQ(ex_resp_header->errormsg(),
            REPLICA_NOT_FOUND_EXCEPTION_ERROR_MESSAGE);
  EXPECT_EQ(
      ex_resp_header->errordetail(),
      ::cloudfs::RpcResponseHeaderProto_RpcErrorCodeProto_ERROR_APPLICATION);
  EXPECT_EQ(ex_response->Body(), nullptr);

  delete ex_response;

  // IOException
  ex_rpc_header = ClientDatanodeServiceImplTests::CreateRpcHeader();

  ex_block = new ::cloudfs::ExtendedBlockProto();
  ex_block->set_blockid(IO_EXCEPTION_BLOCK_ID);
  ex_request_body = new ::cloudfs::GetReplicaVisibleLengthRequestProto();
  ex_request_body->set_allocated_block(ex_block);
  ex_message = impl.NewRequest("getReplicaVisibleLength");
  ex_m = static_cast<message::GetReplicaVisibleLengthMessage*>(ex_message);
  ex_m->RpcHeader(ex_rpc_header);
  ex_m->SetRequestBody(ex_request_body);

  ex_response = impl.CallMethod(ex_m);
  ex_resp_header = ex_response->RpcHeader();

  EXPECT_EQ(ex_resp_header->status(),
            ::cloudfs::RpcResponseHeaderProto_RpcStatusProto_ERROR);
  EXPECT_EQ(ex_resp_header->callid(), CALL_ID);
  EXPECT_EQ(ex_resp_header->clientid(), CLIENT_ID);
  EXPECT_EQ(ex_resp_header->retrycount(), RETRY_COUNT);

  EXPECT_EQ(ex_resp_header->serveripcversionnum(), RPC_CURRENT_VERSION);
  EXPECT_EQ(ex_resp_header->exceptionclassname(), IO_EXCEPTION_CLASS_NAME);
  EXPECT_EQ(ex_resp_header->errormsg(), IO_EXCEPTION_ERROR_MESSAGE);
  EXPECT_EQ(
      ex_resp_header->errordetail(),
      ::cloudfs::RpcResponseHeaderProto_RpcErrorCodeProto_ERROR_APPLICATION);
  EXPECT_EQ(ex_response->Body(), nullptr);

  delete ex_response;
}

TEST_F(ClientDatanodeServiceImplTests, GetReplicaVisibleLengthV2WithException) {
  ClientDatanodeServiceImpl impl;
  std::unique_ptr<DataNode> datanode(new DataNode());
  auto mock_storage = new MockStorage();
  datanode->SetStorage(mock_storage);
  impl.SetDataNode(datanode.get());

  // IOException
  auto ex_rpc_header = ClientDatanodeServiceImplTests::CreateRpcHeader();

  auto ex_block = new ::cloudfs::ExtendedBlockProto();
  ex_block->set_blockid(IO_EXCEPTION_BLOCK_ID);
  auto ex_request_body = new ::cloudfs::GetReplicaVisibleLengthV2RequestProto();
  ex_request_body->set_allocated_block(ex_block);
  auto ex_message = impl.NewRequest("getReplicaVisibleLengthV2");
  auto ex_m =
      static_cast<message::GetReplicaVisibleLengthV2Message*>(ex_message);
  ex_m->RpcHeader(ex_rpc_header);
  ex_m->SetRequestBody(ex_request_body);

  auto ex_response = impl.CallMethod(ex_m);
  auto ex_resp_header = ex_response->RpcHeader();

  EXPECT_EQ(ex_resp_header->status(),
            ::cloudfs::RpcResponseHeaderProto_RpcStatusProto_ERROR);
  EXPECT_EQ(ex_resp_header->callid(), CALL_ID);
  EXPECT_EQ(ex_resp_header->clientid(), CLIENT_ID);
  EXPECT_EQ(ex_resp_header->retrycount(), RETRY_COUNT);

  EXPECT_EQ(ex_resp_header->serveripcversionnum(), RPC_CURRENT_VERSION);
  EXPECT_EQ(ex_resp_header->exceptionclassname(), IO_EXCEPTION_CLASS_NAME);
  EXPECT_EQ(ex_resp_header->errormsg(), IO_EXCEPTION_ERROR_MESSAGE);
  EXPECT_EQ(
      ex_resp_header->errordetail(),
      ::cloudfs::RpcResponseHeaderProto_RpcErrorCodeProto_ERROR_APPLICATION);
  EXPECT_EQ(ex_response->Body(), nullptr);

  delete ex_response;
}

TEST_F(ClientDatanodeServiceImplTests, GetBlockLocalPathInfo) {
  ClientDatanodeServiceImpl impl;
  auto mock_storage = new MockStorage();
  std::unique_ptr<DataNode> datanode(new DataNode());
  datanode->SetStorage(mock_storage);
  impl.SetDataNode(datanode.get());

  // construct rpc request header
  auto rpc_header = CreateRpcHeader();

  // construct GetBlockLocalPathInfoRequestProto
  auto request_body = new ::cloudfs::GetBlockLocalPathInfoRequestProto();
  auto block = new ExtendedBlock(BLOCK_POOL_ID, 123456, 128, 341021, false);
  auto block_proto = new ::cloudfs::ExtendedBlockProto();
  block->ToProto(block_proto);
  request_body->set_allocated_block(block_proto);
  delete block;

  // construct RpcRequestMessage
  auto message = impl.NewRequest("getBlockLocalPathInfo");
  auto m = static_cast<message::GetBlockLocalPathInfoMessage*>(message);
  m->RpcHeader(rpc_header);
  m->SetRequestBody(request_body);

  auto response = impl.CallMethod(m);
  auto resp_header = response->RpcHeader();
  auto resp_body =
      static_cast<const ::cloudfs::GetBlockLocalPathInfoResponseProto*>(
          response->Body());
  auto resp_block = resp_body->block();

  EXPECT_EQ(resp_header->status(),
            ::cloudfs::RpcResponseHeaderProto_RpcStatusProto_SUCCESS);
  EXPECT_EQ(resp_header->callid(), CALL_ID);
  EXPECT_EQ(resp_header->clientid(), CLIENT_ID);
  EXPECT_EQ(resp_header->retrycount(), RETRY_COUNT);
  EXPECT_EQ(resp_header->serveripcversionnum(), RPC_CURRENT_VERSION);

  EXPECT_EQ(resp_block.poolid(), BLOCK_POOL_ID);
  EXPECT_EQ(resp_body->localpath(), LOCAL_BLOCK_PATH);
  EXPECT_EQ(resp_body->localmetapath(), LOCAL_META_PATH);

  delete response;
}

TEST_F(ClientDatanodeServiceImplTests, GetBlockLocalPathInfoWithException) {
  ClientDatanodeServiceImpl impl;
  std::unique_ptr<DataNode> datanode(new DataNode());
  impl.SetDataNode(datanode.get());

  // NullPointException
  auto ex_rpc_header = CreateRpcHeader();

  // construct GetBlockLocalPathInfoRequestProto
  auto ex_request_body = new ::cloudfs::GetBlockLocalPathInfoRequestProto();
  auto ex_block = new ExtendedBlock(BLOCK_POOL_ID, 123456, 128, 341021, false);
  auto ex_block_proto = new ::cloudfs::ExtendedBlockProto();
  ex_block->ToProto(ex_block_proto);
  ex_request_body->set_allocated_block(ex_block_proto);
  delete ex_block;

  // construct RpcRequestMessage
  auto ex_message = impl.NewRequest("getBlockLocalPathInfo");
  auto ex_m = static_cast<message::GetBlockLocalPathInfoMessage*>(ex_message);
  ex_m->RpcHeader(ex_rpc_header);
  ex_m->SetRequestBody(ex_request_body);

  auto ex_response = impl.CallMethod(ex_m);
  auto ex_resp_header = ex_response->RpcHeader();

  EXPECT_EQ(ex_resp_header->status(),
            ::cloudfs::RpcResponseHeaderProto_RpcStatusProto_ERROR);
  EXPECT_EQ(ex_resp_header->callid(), CALL_ID);
  EXPECT_EQ(ex_resp_header->clientid(), CLIENT_ID);
  EXPECT_EQ(ex_resp_header->retrycount(), RETRY_COUNT);
  EXPECT_EQ(ex_resp_header->serveripcversionnum(), RPC_CURRENT_VERSION);

  EXPECT_EQ(
      ex_resp_header->errordetail(),
      ::cloudfs::RpcResponseHeaderProto_RpcErrorCodeProto_ERROR_APPLICATION);
  EXPECT_EQ(ex_resp_header->exceptionclassname(),
            NULLPOINT_EXCEPTION_CLASS_NAME);
  EXPECT_EQ(ex_resp_header->errormsg(), STORAGE_NOT_INITIAL_ERROR_MESSAGE);
  EXPECT_EQ(ex_response->Body(), nullptr);

  delete ex_response;

  // IOException
  auto mock_storage = new MockStorage();
  datanode->SetStorage(mock_storage);

  // create rpc header
  ex_rpc_header = CreateRpcHeader();

  // construct GetBlockLocalPathInfoRequestProto
  ex_request_body = new ::cloudfs::GetBlockLocalPathInfoRequestProto();
  ex_block = new ExtendedBlock(BLOCK_POOL_ID, IO_EXCEPTION_BLOCK_ID, 128,
                               341021, false);
  ex_block_proto = new ::cloudfs::ExtendedBlockProto();
  ex_block->ToProto(ex_block_proto);
  ex_request_body->set_allocated_block(ex_block_proto);
  delete ex_block;

  // construct RpcRequestMessage
  ex_message = impl.NewRequest("getBlockLocalPathInfo");
  ex_m = static_cast<message::GetBlockLocalPathInfoMessage*>(ex_message);
  ex_m->RpcHeader(ex_rpc_header);
  ex_m->SetRequestBody(ex_request_body);

  ex_response = impl.CallMethod(ex_m);
  ex_resp_header = ex_response->RpcHeader();

  EXPECT_EQ(ex_resp_header->status(),
            ::cloudfs::RpcResponseHeaderProto_RpcStatusProto_ERROR);
  EXPECT_EQ(ex_resp_header->callid(), CALL_ID);
  EXPECT_EQ(ex_resp_header->clientid(), CLIENT_ID);
  EXPECT_EQ(ex_resp_header->retrycount(), RETRY_COUNT);
  EXPECT_EQ(ex_resp_header->serveripcversionnum(), RPC_CURRENT_VERSION);

  EXPECT_EQ(
      ex_resp_header->errordetail(),
      ::cloudfs::RpcResponseHeaderProto_RpcErrorCodeProto_ERROR_APPLICATION);
  EXPECT_EQ(ex_resp_header->exceptionclassname(), IO_EXCEPTION_CLASS_NAME);
  EXPECT_EQ(ex_resp_header->errormsg(), IO_EXCEPTION_ERROR_MESSAGE);
  EXPECT_EQ(ex_response->Body(), nullptr);

  delete ex_response;
}

TEST_F(ClientDatanodeServiceImplTests, GetHdfsBlockLocations) {
  ClientDatanodeServiceImpl impl;
  auto mock_storage = new MockStorage();
  std::unique_ptr<DataNode> datanode(new DataNode());
  datanode->SetStorage(mock_storage);
  datanode->SetGetHdfsBlockLocationsEnable(true);
  impl.SetDataNode(datanode.get());

  // construct rpc request header
  auto rpc_header = CreateRpcHeader();

  // construct GetHdfsBlocksMetadataProto
  auto request_body = new ::cloudfs::GetHdfsBlockLocationsRequestProto();
  request_body->add_blockids(123456);
  request_body->add_blockids(123457);
  request_body->add_blockids(123458);
  request_body->set_blockpoolid(BLOCK_POOL_ID);

  // construct RpcRequestMessage
  auto message = impl.NewRequest("getHdfsBlockLocations");
  auto m = static_cast<message::GetHdfsBlockLocationsMessage*>(message);
  m->RpcHeader(rpc_header);
  m->SetRequestBody(request_body);

  auto response = impl.CallMethod(m);
  auto resp_header = response->RpcHeader();
  auto resp_body =
      static_cast<const ::cloudfs::GetHdfsBlockLocationsResponseProto*>(
          response->Body());

  EXPECT_EQ(resp_header->status(),
            ::cloudfs::RpcResponseHeaderProto_RpcStatusProto_SUCCESS);
  EXPECT_EQ(resp_header->callid(), CALL_ID);
  EXPECT_EQ(resp_header->clientid(), CLIENT_ID);
  EXPECT_EQ(resp_header->retrycount(), RETRY_COUNT);

  EXPECT_TRUE(resp_body != nullptr);
  EXPECT_EQ(resp_body->volumeids(0), "ABC");
  EXPECT_EQ(resp_body->volumeids(1), "DEF");
  EXPECT_EQ(resp_body->volumeindexes(0), 0);
  EXPECT_EQ(resp_body->volumeindexes(1), 1);

  delete response;
}

TEST_F(ClientDatanodeServiceImplTests, GetHdfsBlockLocationsWithException) {
  ClientDatanodeServiceImpl impl;
  std::unique_ptr<DataNode> datanode(new DataNode());
  impl.SetDataNode(datanode.get());

  // NullPointException
  auto ex_rpc_header = CreateRpcHeader();

  // construct GetHdfsBlockLocationsResponseProto
  auto ex_request_body = new ::cloudfs::GetHdfsBlockLocationsRequestProto();

  // construct RpcRequestMessage
  auto ex_message = impl.NewRequest("getHdfsBlockLocations");
  auto ex_m = static_cast<message::GetHdfsBlockLocationsMessage*>(ex_message);
  ex_m->RpcHeader(ex_rpc_header);
  ex_m->SetRequestBody(ex_request_body);

  auto ex_response = impl.CallMethod(ex_m);
  auto ex_resp_header = ex_response->RpcHeader();

  EXPECT_EQ(ex_resp_header->status(),
            ::cloudfs::RpcResponseHeaderProto_RpcStatusProto_ERROR);
  EXPECT_EQ(ex_resp_header->callid(), CALL_ID);
  EXPECT_EQ(ex_resp_header->clientid(), CLIENT_ID);
  EXPECT_EQ(ex_resp_header->retrycount(), RETRY_COUNT);
  EXPECT_EQ(ex_resp_header->serveripcversionnum(), RPC_CURRENT_VERSION);

  EXPECT_EQ(
      ex_resp_header->errordetail(),
      ::cloudfs::RpcResponseHeaderProto_RpcErrorCodeProto_ERROR_APPLICATION);
  EXPECT_EQ(ex_resp_header->exceptionclassname(),
            NULLPOINT_EXCEPTION_CLASS_NAME);
  EXPECT_EQ(ex_resp_header->errormsg(), STORAGE_NOT_INITIAL_ERROR_MESSAGE);
  EXPECT_EQ(ex_response->Body(), nullptr);

  delete ex_response;

  // UnsupportedOperationException
  auto mock_storage = new MockStorage();
  datanode->SetStorage(mock_storage);

  ex_rpc_header = CreateRpcHeader();

  // construct GetHdfsBlockLocationsResponseProto
  ex_request_body = new ::cloudfs::GetHdfsBlockLocationsRequestProto();

  // construct RpcRequestMessage
  ex_message = impl.NewRequest("getHdfsBlockLocations");
  ex_m = static_cast<message::GetHdfsBlockLocationsMessage*>(ex_message);
  ex_m->RpcHeader(ex_rpc_header);
  ex_m->SetRequestBody(ex_request_body);

  ex_response = impl.CallMethod(ex_m);
  ex_resp_header = ex_response->RpcHeader();

  EXPECT_EQ(ex_resp_header->status(),
            ::cloudfs::RpcResponseHeaderProto_RpcStatusProto_ERROR);
  EXPECT_EQ(ex_resp_header->callid(), CALL_ID);
  EXPECT_EQ(ex_resp_header->clientid(), CLIENT_ID);
  EXPECT_EQ(ex_resp_header->retrycount(), RETRY_COUNT);
  EXPECT_EQ(ex_resp_header->serveripcversionnum(), RPC_CURRENT_VERSION);

  EXPECT_EQ(
      ex_resp_header->errordetail(),
      ::cloudfs::RpcResponseHeaderProto_RpcErrorCodeProto_ERROR_APPLICATION);
  EXPECT_EQ(ex_resp_header->exceptionclassname(),
            UNSUPPORTED_OPERATION_EXCEPTION_CLASS_NAME);
  EXPECT_EQ(ex_resp_header->errormsg(),
            UNSUPPORTED_OPERATION_EXCEPTION_ERROR_MESSAGE);
  EXPECT_EQ(ex_response->Body(), nullptr);

  delete ex_response;

  // IOException
  datanode->SetGetHdfsBlockLocationsEnable(true);

  ex_rpc_header = CreateRpcHeader();

  // construct GetHdfsBlockLocationsResponseProto
  ex_request_body = new ::cloudfs::GetHdfsBlockLocationsRequestProto();

  // construct RpcRequestMessage
  ex_message = impl.NewRequest("getHdfsBlockLocations");
  ex_m = static_cast<message::GetHdfsBlockLocationsMessage*>(ex_message);
  ex_m->RpcHeader(ex_rpc_header);
  ex_m->SetRequestBody(ex_request_body);

  ex_response = impl.CallMethod(ex_m);
  ex_resp_header = ex_response->RpcHeader();

  EXPECT_EQ(ex_resp_header->status(),
            ::cloudfs::RpcResponseHeaderProto_RpcStatusProto_ERROR);
  EXPECT_EQ(ex_resp_header->callid(), CALL_ID);
  EXPECT_EQ(ex_resp_header->clientid(), CLIENT_ID);
  EXPECT_EQ(ex_resp_header->retrycount(), RETRY_COUNT);
  EXPECT_EQ(ex_resp_header->serveripcversionnum(), RPC_CURRENT_VERSION);

  EXPECT_EQ(
      ex_resp_header->errordetail(),
      ::cloudfs::RpcResponseHeaderProto_RpcErrorCodeProto_ERROR_APPLICATION);
  EXPECT_EQ(ex_resp_header->exceptionclassname(), IO_EXCEPTION_CLASS_NAME);
  EXPECT_EQ(ex_resp_header->errormsg(), IO_EXCEPTION_ERROR_MESSAGE);
  EXPECT_EQ(ex_response->Body(), nullptr);

  delete ex_response;
}

TEST_F(ClientDatanodeServiceImplTests, ShutdwonDatanode) {
  ClientDatanodeServiceImpl impl;
  auto mock_storage = new MockStorage();
  std::unique_ptr<DataNode> datanode(new DataNode());
  datanode->SetStorage(mock_storage);
  impl.SetDataNode(datanode.get());

  // construct rpc request header
  auto rpc_header = CreateRpcHeader();

  // construct ShutdwonDatanodeRequestProto
  auto request_body = new ::cloudfs::ShutdownDatanodeRequestProto();
  request_body->set_forupgrade(true);

  // construct RpcRequestMessage
  auto message = impl.NewRequest("shutdownDatanode");
  auto m = static_cast<message::ShutdownDatanodeMessage*>(message);
  m->RpcHeader(rpc_header);
  m->SetRequestBody(request_body);

  auto response = impl.CallMethod(m);
  auto resp_header = response->RpcHeader();

  EXPECT_EQ(resp_header->status(),
            ::cloudfs::RpcResponseHeaderProto_RpcStatusProto_SUCCESS);
  EXPECT_EQ(resp_header->callid(), CALL_ID);
  EXPECT_EQ(resp_header->clientid(), CLIENT_ID);
  EXPECT_EQ(resp_header->retrycount(), RETRY_COUNT);

  delete response;
}

TEST_F(ClientDatanodeServiceImplTests, TriggerBlockReport) {
  const std::string work_dir = "./ClientDatanodeServiceTests_conf/";
  byte::LocalFileSystem local_fs;
  byte::DeleteOptions delete_options;
  delete_options.recursively_ = true;
  if (local_fs.Exists(work_dir).ok()) {
    local_fs.DeleteDir(work_dir, delete_options);
  }
  EXPECT_TRUE(local_fs.CreateDir(work_dir, byte::CreateOptions()).ok());
  const std::string test_conf_file = work_dir + "/hdfsconfig.json";
  FLAGS_bytestore_hdfs_config_file = test_conf_file;
  const std::string test_nn_conf_file = work_dir + "/nnconfig.json";
  FLAGS_bytestore_hdfs_nn_config_file = test_nn_conf_file;
  std::ofstream json_file(test_conf_file);
  json_file << k_test_content << std::endl;
  json_file.close();
  std::ofstream json_nn_file(test_nn_conf_file);
  json_nn_file << k_test_nn_content << std::endl;
  json_nn_file.close();
  auto conf = new DataNodeConfig();
  EXPECT_TRUE(conf->Parse(test_conf_file, test_nn_conf_file, ""));

  ClientDatanodeServiceImpl impl;
  DataNode* datanode = new DataNode();
  auto mock_storage = new MockStorage();
  auto bpm = new BlockPoolManager(datanode);
  bpm->StartActorManager();
  datanode->SetStorage(mock_storage);
  datanode->SetBlockPoolManager(bpm);
  datanode->SetDataNodeConfig(conf);

  impl.SetDataNode(datanode);
  bpm->StartRefreshNamespaceConfig();
  byte::ThisThread::SleepInMs(1000);
  auto threads = bpm->GetAllNamenodeThreads();
  EXPECT_EQ(threads.size(), 2);

  for (auto bps : threads) {
    for (auto actor : bps->GetActors()) {
      EXPECT_EQ(actor->GetSendImmediateIBR(), false);
    }
  }

  // construct rpc request header
  auto rpc_header = ClientDatanodeServiceImplTests::CreateRpcHeader();

  // construct TriggerBlockReportProto
  auto rpc_body = new ::cloudfs::TriggerBlockReportRequestProto();
  rpc_body->set_incremental(true);

  // construct RpcRequstMessage
  auto message = impl.NewRequest("triggerBlockReport");
  auto m = static_cast<message::TriggerBlockReportMessage*>(message);
  m->RpcHeader(rpc_header);
  m->SetRequestBody(rpc_body);

  auto response = impl.CallMethod(m);
  auto resp_header = response->RpcHeader();
  EXPECT_EQ(resp_header->status(),
            ::cloudfs::RpcResponseHeaderProto_RpcStatusProto_SUCCESS);
  EXPECT_EQ(resp_header->callid(), CALL_ID);
  EXPECT_EQ(resp_header->clientid(), CLIENT_ID);
  EXPECT_EQ(resp_header->retrycount(), RETRY_COUNT);

  for (auto bps : threads) {
    for (auto actor : bps->GetActors()) {
      EXPECT_EQ(actor->GetSendImmediateIBR(), true);
    }
  }

  datanode->Stop();
  datanode->Join();
  delete datanode;

  delete response;

  // delete test file
  local_fs.DeleteDir(work_dir, delete_options);
}

TEST_F(ClientDatanodeServiceImplTests, GetBlocks) {
  // construct datanode config
  gflags::FlagSaver saver;
  const std::string work_dir = "./ClientDatanodeServiceTests_conf/";
  byte::LocalFileSystem local_fs;
  byte::DeleteOptions delete_options;
  delete_options.recursively_ = true;
  if (local_fs.Exists(work_dir).ok()) {
    local_fs.DeleteDir(work_dir, delete_options);
  }
  EXPECT_TRUE(local_fs.CreateDir(work_dir, byte::CreateOptions()).ok());
  const std::string test_conf_file = work_dir + "/hdfsconfig.json";
  FLAGS_bytestore_hdfs_config_file = test_conf_file;
  const std::string test_nn_conf_file = work_dir + "/nnconfig.json";
  FLAGS_bytestore_hdfs_nn_config_file = test_nn_conf_file;
  std::ofstream json_file(test_conf_file);
  json_file << k_test_content << std::endl;
  json_file.close();
  std::ofstream json_nn_file(test_nn_conf_file);
  json_nn_file << k_test_nn_content << std::endl;
  json_nn_file.close();
  auto conf = new DataNodeConfig();
  EXPECT_TRUE(conf->Parse(test_conf_file, test_nn_conf_file, ""));

  ClientDatanodeServiceImpl impl;
  auto mock_storage = new MockStorage();
  DataNode* datanode = new DataNode();
  datanode->SetStorage(mock_storage);
  std::shared_ptr<DatanodeID> datanode_id = std::make_shared<DatanodeID>(
      "", "", DATANODE_UUID, 0, 0, 0, "", std::vector<std::string>(),
      std::vector<std::string>());
  datanode->SetDatanodeID(datanode_id);

  auto bpm = new BlockPoolManager(datanode);
  bpm->StartActorManager();
  datanode->SetBlockPoolManager(bpm);

  datanode->SetDataNodeConfig(conf);
  impl.SetDataNode(datanode);
  bpm->StartRefreshNamespaceConfig();
  byte::ThisThread::SleepInMs(1000);
  auto threads = bpm->GetAllNamenodeThreads();
  EXPECT_EQ(threads.size(), 2);

  // construct rpc request header
  auto rpc_header = CreateRpcHeader();

  // construct GetBlocksRequestProto
  auto request_body = new ::cloudfs::GetBlocksRequestProto();
  request_body->set_bpid(BLOCK_POOL_ID);
  request_body->set_size(257);

  // construct RpcRequestMessage
  auto message = impl.NewRequest("getBlocks");
  auto m = static_cast<message::GetBlocksMessage*>(message);
  m->RpcHeader(rpc_header);
  m->SetRequestBody(request_body);

  auto response = impl.CallMethod(m);
  auto resp_header = response->RpcHeader();
  auto resp_body =
      static_cast<const ::cloudfs::GetBlocksResponseProto*>(response->Body());

  EXPECT_EQ(resp_header->status(),
            ::cloudfs::RpcResponseHeaderProto_RpcStatusProto_SUCCESS);
  EXPECT_EQ(resp_header->callid(), CALL_ID);
  EXPECT_EQ(resp_header->clientid(), CLIENT_ID);
  EXPECT_EQ(resp_header->retrycount(), RETRY_COUNT);

  EXPECT_TRUE(resp_body != nullptr);
  auto blocks = resp_body->blocks();
  EXPECT_EQ(blocks.blocks_size(), 3);
  auto block = blocks.blocks(0);
  EXPECT_EQ(block.datanodeuuids(0), DATANODE_UUID);
  EXPECT_EQ(block.storageuuids(0), STORAGE_UUID);
  EXPECT_EQ(block.storagetypes(0), ::cloudfs::StorageTypeProto::DISK);
  auto b = block.block();
  EXPECT_EQ(b.numbytes(), 128);

  datanode->Stop();
  datanode->Join();
  delete datanode;

  delete response;

  // delete test file
  local_fs.DeleteDir(work_dir, delete_options);
}

TEST_F(ClientDatanodeServiceImplTests, GetBlocksWithException) {
  // construct datanode config
  gflags::FlagSaver saver;
  const std::string work_dir = "./ClientDatanodeServiceTests_conf/";
  byte::LocalFileSystem local_fs;
  byte::DeleteOptions delete_options;
  delete_options.recursively_ = true;
  if (local_fs.Exists(work_dir).ok()) {
    local_fs.DeleteDir(work_dir, delete_options);
  }
  EXPECT_TRUE(local_fs.CreateDir(work_dir, byte::CreateOptions()).ok());
  const std::string test_conf_file = work_dir + "/hdfsconfig.json";
  FLAGS_bytestore_hdfs_config_file = test_conf_file;
  const std::string test_nn_conf_file = work_dir + "/nnconfig.json";
  FLAGS_bytestore_hdfs_nn_config_file = test_nn_conf_file;
  std::ofstream json_file(test_conf_file);
  json_file << k_test_content << std::endl;
  json_file.close();
  std::ofstream json_nn_file(test_nn_conf_file);
  json_nn_file << k_test_nn_content << std::endl;
  json_nn_file.close();
  auto conf = new DataNodeConfig();
  EXPECT_TRUE(conf->Parse(test_conf_file, test_nn_conf_file, ""));

  ClientDatanodeServiceImpl impl;
  auto mock_storage = new MockStorage();
  DataNode* datanode = new DataNode();
  datanode->SetStorage(mock_storage);
  std::shared_ptr<DatanodeID> datanode_id = std::make_shared<DatanodeID>(
      "", "", DATANODE_UUID, 0, 0, 0, "", std::vector<std::string>(),
      std::vector<std::string>());
  datanode->SetDatanodeID(datanode_id);

  auto bpm = new BlockPoolManager(datanode);
  bpm->StartActorManager();
  datanode->SetBlockPoolManager(bpm);

  datanode->SetDataNodeConfig(conf);
  impl.SetDataNode(datanode);
  bpm->StartRefreshNamespaceConfig();
  byte::ThisThread::SleepInMs(1000);
  auto threads = bpm->GetAllNamenodeThreads();
  EXPECT_EQ(threads.size(), 2);

  // construct rpc request header
  auto ex_rpc_header = CreateRpcHeader();

  // construct GetBlocksRequestProto
  auto ex_request_body = new ::cloudfs::GetBlocksRequestProto();
  ex_request_body->set_bpid(EXCEPTION_BLOCK_POOL_ID);
  ex_request_body->set_size(257);

  // construct RpcRequestMessage
  auto ex_message = impl.NewRequest("getBlocks");
  auto ex_m = static_cast<message::GetBlocksMessage*>(ex_message);
  ex_m->RpcHeader(ex_rpc_header);
  ex_m->SetRequestBody(ex_request_body);

  // IllegalArgumentException
  datanode->SetGetHdfsBlockLocationsEnable(true);
  auto ex_response = impl.CallMethod(ex_m);
  auto ex_resp_header = ex_response->RpcHeader();

  EXPECT_EQ(ex_resp_header->status(),
            ::cloudfs::RpcResponseHeaderProto_RpcStatusProto_ERROR);
  EXPECT_EQ(ex_resp_header->callid(), CALL_ID);
  EXPECT_EQ(ex_resp_header->clientid(), CLIENT_ID);
  EXPECT_EQ(ex_resp_header->retrycount(), RETRY_COUNT);
  EXPECT_EQ(ex_resp_header->serveripcversionnum(), RPC_CURRENT_VERSION);

  EXPECT_EQ(
      ex_resp_header->errordetail(),
      ::cloudfs::RpcResponseHeaderProto_RpcErrorCodeProto_ERROR_APPLICATION);
  EXPECT_EQ(ex_resp_header->exceptionclassname(),
            ILLEGAL_ARGUMENT_EXCEPTION_CLASS_NAME);
  EXPECT_EQ(ex_resp_header->errormsg(),
            ILLEGAL_ARGUMENT_EXCEPTION_ERROR_MESSAGE);
  EXPECT_EQ(ex_response->Body(), nullptr);

  datanode->Stop();
  datanode->Join();
  delete datanode;

  local_fs.DeleteDir(work_dir, delete_options);

  delete ex_response;
}

TEST_F(ClientDatanodeServiceImplTests, InitReplicaRecovery) {
  ClientDatanodeServiceImpl impl;
  auto mock_storage = new MockStorage();
  std::unique_ptr<DataNode> datanode(new DataNode());
  datanode->SetStorage(mock_storage);
  impl.SetDataNode(datanode.get());

  // create rpc header
  auto ex_rpc_header = CreateRpcHeader();

  auto rblock = new ::cloudfs::RecoveringBlockProto();
  rb->ToProto(rblock);
  auto ex_request_body = new ::cloudfs::InitReplicaRecoveryRequestProto();
  ex_request_body->set_allocated_block(rblock);

  // construct RpcRequestMessage
  auto ex_message = impl.NewRequest("initReplicaRecovery");
  auto ex_m = static_cast<message::InitReplicaRecoveryMessage*>(ex_message);
  ex_m->RpcHeader(ex_rpc_header);
  ex_m->SetRequestBody(ex_request_body);

  auto response = impl.CallMethod(ex_m);
  auto resp_header = response->RpcHeader();
  auto resp_body =
      static_cast<const ::cloudfs::InitReplicaRecoveryResponseProto*>(
          response->Body());

  EXPECT_EQ(resp_header->status(),
            ::cloudfs::RpcResponseHeaderProto_RpcStatusProto_SUCCESS);
  EXPECT_EQ(resp_header->callid(), CALL_ID);
  EXPECT_EQ(resp_header->clientid(), CLIENT_ID);
  EXPECT_EQ(resp_header->retrycount(), RETRY_COUNT);
  EXPECT_EQ(resp_header->serveripcversionnum(), RPC_CURRENT_VERSION);

  EXPECT_NE(resp_body, nullptr);
  auto flag = resp_body->replicafound();
  EXPECT_TRUE(flag);
  auto state = resp_body->state();
  EXPECT_EQ(state, ::cloudfs::ReplicaStateProto::FINALIZED);
  auto block = resp_body->block();
  EXPECT_EQ(block.blockid(), blk_id);
  EXPECT_EQ(block.genstamp(), blk_gs0);
  EXPECT_EQ(block.numbytes(), blk_len);

  delete response;
}

TEST_F(ClientDatanodeServiceImplTests, ReplicaNotFound) {
  ClientDatanodeServiceImpl impl;
  MockStore* mock_storage = new MockStore();
  EXPECT_CALL(*mock_storage, InitReplicaRecovery(::testing::_, ::testing::_))
      .WillRepeatedly(
          ::testing::DoAll(::testing::SetArgPointee<1>(nullptr),
                           ::testing::Return(exceptions::Exception())));
  DataNode datanode;
  datanode.SetStorage(mock_storage);
  impl.SetDataNode(&datanode);

  // create rpc header
  auto ex_rpc_header = CreateRpcHeader();

  auto rblock = new ::cloudfs::RecoveringBlockProto();
  rb->ToProto(rblock);
  auto ex_request_body = new ::cloudfs::InitReplicaRecoveryRequestProto();
  ex_request_body->set_allocated_block(rblock);

  // construct RpcRequestMessage
  auto ex_message = impl.NewRequest("initReplicaRecovery");
  auto ex_m = static_cast<message::InitReplicaRecoveryMessage*>(ex_message);
  ex_m->RpcHeader(ex_rpc_header);
  ex_m->SetRequestBody(ex_request_body);

  auto response = impl.CallMethod(ex_m);
  auto resp_header = response->RpcHeader();
  auto resp_body =
      static_cast<const ::cloudfs::InitReplicaRecoveryResponseProto*>(
          response->Body());

  EXPECT_EQ(resp_header->status(),
            ::cloudfs::RpcResponseHeaderProto_RpcStatusProto_SUCCESS);
  EXPECT_EQ(resp_header->callid(), CALL_ID);
  EXPECT_EQ(resp_header->clientid(), CLIENT_ID);
  EXPECT_EQ(resp_header->retrycount(), RETRY_COUNT);
  EXPECT_EQ(resp_header->serveripcversionnum(), RPC_CURRENT_VERSION);

  EXPECT_NE(resp_body, nullptr);
  auto flag = resp_body->replicafound();
  EXPECT_FALSE(flag);

  delete response;
  delete ex_message;
}

TEST_F(ClientDatanodeServiceImplTests, RecoveryInProgressException) {
  ClientDatanodeServiceImpl impl;
  MockStore* mock_storage = new MockStore();
  EXPECT_CALL(*mock_storage, InitReplicaRecovery(::testing::_, ::testing::_))
      .WillRepeatedly(::testing::Return(
          exceptions::Exception(exceptions::E::kRecoveryInProgressException)));
  DataNode datanode;
  datanode.SetStorage(mock_storage);
  impl.SetDataNode(&datanode);

  // create rpc header
  auto ex_rpc_header = CreateRpcHeader();

  auto rblock = new ::cloudfs::RecoveringBlockProto();
  rb->ToProto(rblock);
  auto ex_request_body = new ::cloudfs::InitReplicaRecoveryRequestProto();
  ex_request_body->set_allocated_block(rblock);

  // construct RpcRequestMessage
  auto ex_message = impl.NewRequest("initReplicaRecovery");
  auto ex_m = static_cast<message::InitReplicaRecoveryMessage*>(ex_message);
  ex_m->RpcHeader(ex_rpc_header);
  ex_m->SetRequestBody(ex_request_body);

  auto response = impl.CallMethod(ex_m);
  auto resp_header = response->RpcHeader();
  auto resp_body =
      static_cast<const ::cloudfs::InitReplicaRecoveryResponseProto*>(
          response->Body());

  EXPECT_EQ(resp_header->status(),
            ::cloudfs::RpcResponseHeaderProto_RpcStatusProto_ERROR);
  EXPECT_EQ(resp_header->callid(), CALL_ID);
  EXPECT_EQ(resp_header->clientid(), CLIENT_ID);
  EXPECT_EQ(resp_header->retrycount(), RETRY_COUNT);
  EXPECT_EQ(resp_header->serveripcversionnum(), RPC_CURRENT_VERSION);

  EXPECT_EQ(
      resp_header->errordetail(),
      ::cloudfs::RpcResponseHeaderProto_RpcErrorCodeProto_ERROR_APPLICATION);
  EXPECT_EQ(resp_header->exceptionclassname(),
            RECOVERY_IN_PROGRESS_EXCEPTION_CLASS_NAME);
  EXPECT_EQ(resp_body, nullptr);

  delete response;
  delete ex_message;
}

TEST_F(ClientDatanodeServiceImplTests, UpdateReplicaUnderRecovery) {
  ClientDatanodeServiceImpl impl;
  MockStorage* mock_storage = new MockStorage();
  MockDataNode mock_dn;
  EXPECT_CALL(mock_dn, NotifyNamenodeReceivedBlock(::testing::_, "", sid))
      .WillRepeatedly(::testing::Return());
  mock_dn.SetStorage(mock_storage);
  impl.SetDataNode(&mock_dn);

  // create rpc header
  auto ex_rpc_header = CreateRpcHeader();

  auto eblock = new ::cloudfs::ExtendedBlockProto();
  test_block->ToProto(eblock);
  auto ex_request_body =
      new ::cloudfs::UpdateReplicaUnderRecoveryRequestProto();
  ex_request_body->set_allocated_block(eblock);
  ex_request_body->set_newlength(blk_len);
  ex_request_body->set_recoveryid(blk_gs1);

  // construct RpcRequestMessage
  auto ex_message = impl.NewRequest("updateReplicaUnderRecovery");
  auto ex_m =
      static_cast<message::UpdateReplicaUnderRecoveryMessage*>(ex_message);
  ex_m->RpcHeader(ex_rpc_header);
  ex_m->SetRequestBody(ex_request_body);

  auto response = impl.CallMethod(ex_m);
  auto resp_header = response->RpcHeader();
  auto resp_body =
      static_cast<const ::cloudfs::UpdateReplicaUnderRecoveryResponseProto*>(
          response->Body());

  EXPECT_EQ(resp_header->status(),
            ::cloudfs::RpcResponseHeaderProto_RpcStatusProto_SUCCESS);
  EXPECT_EQ(resp_header->callid(), CALL_ID);
  EXPECT_EQ(resp_header->clientid(), CLIENT_ID);
  EXPECT_EQ(resp_header->retrycount(), RETRY_COUNT);
  EXPECT_EQ(resp_header->serveripcversionnum(), RPC_CURRENT_VERSION);

  EXPECT_NE(resp_body, nullptr);
  auto storage_id = resp_body->storageuuid();
  EXPECT_EQ(storage_id, sid);

  delete response;
  delete ex_message;
}

TEST_F(ClientDatanodeServiceImplTests, CorruptedVolumeMap) {
  ClientDatanodeServiceImpl impl;
  MockStore* mock_storage = new MockStore();
  EXPECT_CALL(*mock_storage, UpdateReplicaUnderRecovery(::testing::_, blk_gs1,
                                                        blk_len, ::testing::_))
      .WillRepeatedly(
          ::testing::DoAll(::testing::SetArgPointee<3>(""),
                           ::testing::Return(exceptions::Exception())));
  DataNode datanode;
  datanode.SetStorage(mock_storage);
  impl.SetDataNode(&datanode);

  // create rpc header
  auto ex_rpc_header = CreateRpcHeader();

  auto eblock = new ::cloudfs::ExtendedBlockProto();
  test_block->ToProto(eblock);
  auto ex_request_body =
      new ::cloudfs::UpdateReplicaUnderRecoveryRequestProto();
  ex_request_body->set_allocated_block(eblock);
  ex_request_body->set_newlength(blk_len);
  ex_request_body->set_recoveryid(blk_gs1);

  // construct RpcRequestMessage
  auto ex_message = impl.NewRequest("updateReplicaUnderRecovery");
  auto ex_m =
      static_cast<message::UpdateReplicaUnderRecoveryMessage*>(ex_message);
  ex_m->RpcHeader(ex_rpc_header);
  ex_m->SetRequestBody(ex_request_body);

  auto response = impl.CallMethod(ex_m);
  auto resp_header = response->RpcHeader();
  auto resp_body =
      static_cast<const ::cloudfs::UpdateReplicaUnderRecoveryResponseProto*>(
          response->Body());

  EXPECT_EQ(resp_header->status(),
            ::cloudfs::RpcResponseHeaderProto_RpcStatusProto_ERROR);
  EXPECT_EQ(resp_header->callid(), CALL_ID);
  EXPECT_EQ(resp_header->clientid(), CLIENT_ID);
  EXPECT_EQ(resp_header->retrycount(), RETRY_COUNT);
  EXPECT_EQ(resp_header->serveripcversionnum(), RPC_CURRENT_VERSION);

  EXPECT_EQ(
      resp_header->errordetail(),
      ::cloudfs::RpcResponseHeaderProto_RpcErrorCodeProto_ERROR_APPLICATION);
  EXPECT_EQ(resp_header->exceptionclassname(), IO_EXCEPTION_CLASS_NAME);
  EXPECT_EQ(resp_header->errormsg(), CORRUPTED_VOLUME_MAP);
  EXPECT_EQ(resp_body, nullptr);

  delete response;
  delete ex_message;
}

TEST_F(ClientDatanodeServiceImplTests, ReadBlock) {
  ClientDatanodeServiceImpl impl;
  std::shared_ptr<MockStore> mock_storage = std::make_shared<MockStore>();
  std::shared_ptr<MockDataNode> mock_datanode =
      std::make_shared<MockDataNode>();
  EXPECT_CALL(*mock_datanode, GetStorage())
      .Times(::testing::AnyNumber())
      .WillRepeatedly(::testing::Return(mock_storage.get()));
  impl.SetDataNode(mock_datanode.get());

  // create rpc header
  auto ex_rpc_header = CreateRpcHeader();
  std::shared_ptr<ExtendedBlock> block =
      std::make_shared<ExtendedBlock>("bpid", 1, 128 * 1024 * 1024, 1, false);
  auto eblock = new ::cloudfs::ExtendedBlockProto();
  block->ToProto(eblock);
  auto ex_request_body = new ::cloudfs::ReadBlockRequestProto();
  ex_request_body->set_allocated_block(eblock);
  ex_request_body->set_offset(1024 * 1024);
  ex_request_body->set_length(1024 * 1024 * 6);
  ex_request_body->add_readpriority(::cloudfs::ReadBlockRequestProto::LOCAL);
  ex_request_body->add_readpriority(::cloudfs::ReadBlockRequestProto::TOS);

  // construct RpcRequestMessage
  auto ex_message = impl.NewRequest("readBlock");
  auto ex_m = static_cast<message::ReadBlockMessage*>(ex_message);
  ex_m->RpcHeader(ex_rpc_header);
  ex_m->SetRequestBody(ex_request_body);
  EXPECT_CALL(*mock_storage,
              ReadBlock(::testing::_, ::testing::_, ::testing::_, ::testing::_,
                        ::testing::_, ::testing::_))
      .Times(2)
      .WillOnce(::testing::Invoke([](ExtendedBlock* block, io::IOChunk* chunk,
                                     uint32_t offset, uint32_t length,
                                     bool from_tos, CsIoPriority priotity) {
        EXPECT_EQ(offset, 1024 * 1024);
        EXPECT_EQ(length, 6 * 1024 * 1024);
        return exceptions::Exception(exceptions::kReplicaNotFoundException);
      }))
      .WillOnce(::testing::Invoke([](ExtendedBlock* block, io::IOChunk* chunk,
                                     uint32_t offset, uint32_t length,
                                     bool from_tos, CsIoPriority priotity) {
        EXPECT_EQ(offset, 1024 * 1024);
        EXPECT_EQ(length, 6 * 1024 * 1024);
        EXPECT_TRUE(from_tos);
        chunk->IncrLength(6 * 1024 * 1024);
        return exceptions::Exception();
      }));
  std::unique_ptr<message::RpcResponseMessage> response(impl.CallMethod(ex_m));
  auto resp_header = response->RpcHeader();
  auto resp_body =
      static_cast<const ::cloudfs::ReadBlockResponseProto*>(response->Body());

  EXPECT_EQ(resp_header->status(),
            ::cloudfs::RpcResponseHeaderProto_RpcStatusProto_SUCCESS);
  EXPECT_EQ(resp_header->callid(), CALL_ID);
  EXPECT_EQ(resp_header->clientid(), CLIENT_ID);
  EXPECT_EQ(resp_header->retrycount(), RETRY_COUNT);
  EXPECT_EQ(resp_header->serveripcversionnum(), RPC_CURRENT_VERSION);
  EXPECT_EQ(resp_body->length(), 6 * 1024 * 1024);
  EXPECT_EQ(resp_body->data().size(), 6 * 1024 * 1024);

  // create rpc header
  auto ex_rpc_header2 = CreateRpcHeader();
  auto eblock2 = new ::cloudfs::ExtendedBlockProto();
  block->ToProto(eblock2);
  auto ex_request_body2 = new ::cloudfs::ReadBlockRequestProto();
  ex_request_body2->set_allocated_block(eblock2);
  ex_request_body2->set_offset(1024 * 1024);
  ex_request_body2->set_length(1024 * 1024 * 6);

  // construct RpcRequestMessage
  auto ex_message2 = impl.NewRequest("readBlock");
  auto ex_m2 = static_cast<message::ReadBlockMessage*>(ex_message2);
  ex_m2->RpcHeader(ex_rpc_header2);
  ex_m2->SetRequestBody(ex_request_body2);
  std::unique_ptr<message::RpcResponseMessage> response2(
      impl.CallMethod(ex_m2));
  auto resp_header2 = response2->RpcHeader();

  EXPECT_EQ(resp_header2->status(),
            ::cloudfs::RpcResponseHeaderProto_RpcStatusProto_ERROR);
  delete ex_message;
  delete ex_message2;
}

TEST_F(ClientDatanodeServiceImplTests, CalculateCrc) {
  ClientDatanodeServiceImpl impl;
  std::shared_ptr<MockStore> mock_storage = std::make_shared<MockStore>();
  std::shared_ptr<MockDataNode> mock_datanode =
      std::make_shared<MockDataNode>();
  EXPECT_CALL(*mock_datanode, GetStorage())
      .Times(::testing::AnyNumber())
      .WillRepeatedly(::testing::Return(mock_storage.get()));
  impl.SetDataNode(mock_datanode.get());

  char* data = new char[128 * 1024 * 1024];
  memset(data, 'a', 128 * 1024 * 1024);
  uint32_t real_crc = butil::crc32c::Extend(0, data, 128 * 1024 * 1024);
  delete[] data;

  // create rpc header
  auto ex_rpc_header = CreateRpcHeader();
  std::shared_ptr<ExtendedBlock> block =
      std::make_shared<ExtendedBlock>("bpid", 1, 128 * 1024 * 1024, 1, false);
  auto eblock = new ::cloudfs::ExtendedBlockProto();
  block->ToProto(eblock);
  auto ex_request_body = new ::cloudfs::CalculateBlockCrcRequestProto();
  ex_request_body->set_allocated_block(eblock);

  // construct RpcRequestMessage
  auto ex_message = impl.NewRequest("calculateBlockCrc");
  auto ex_m = static_cast<message::CalculateBlockCrcMessage*>(ex_message);
  ex_m->RpcHeader(ex_rpc_header);
  ex_m->SetRequestBody(ex_request_body);
  EXPECT_CALL(*mock_storage, CalculateCrc(::testing::_, ::testing::_))
      .Times(2)
      .WillOnce(::testing::Invoke(
          [](const std::shared_ptr<ExtendedBlock>& block, uint32_t* crc) {
            return exceptions::Exception(exceptions::kReplicaNotFoundException);
          }))
      .WillOnce(::testing::Invoke(
          [&real_crc](const std::shared_ptr<ExtendedBlock>& block,
                      uint32_t* crc) {
            *crc = real_crc;
            return exceptions::Exception();
          }));
  std::unique_ptr<message::RpcResponseMessage> response(impl.CallMethod(ex_m));
  auto resp_header = response->RpcHeader();

  EXPECT_EQ(resp_header->status(),
            ::cloudfs::RpcResponseHeaderProto_RpcStatusProto_ERROR);

  response.reset(impl.CallMethod(ex_m));
  resp_header = response->RpcHeader();
  auto resp_body =
      static_cast<const ::cloudfs::CalculateBlockCrcResponseProto*>(
          response->Body());
  EXPECT_EQ(resp_header->status(),
            ::cloudfs::RpcResponseHeaderProto_RpcStatusProto_SUCCESS);
  EXPECT_EQ(resp_header->callid(), CALL_ID);
  EXPECT_EQ(resp_header->clientid(), CLIENT_ID);
  EXPECT_EQ(resp_header->retrycount(), RETRY_COUNT);
  EXPECT_EQ(resp_header->serveripcversionnum(), RPC_CURRENT_VERSION);
  EXPECT_EQ(resp_body->crc(), real_crc);
  delete ex_message;
}

TEST_F(ClientDatanodeServiceImplTests, SealBlock) {
  ClientDatanodeServiceImpl impl;
  std::shared_ptr<MockStore> mock_storage = std::make_shared<MockStore>();
  std::shared_ptr<MockDataNode> mock_datanode =
      std::make_shared<MockDataNode>();
  EXPECT_CALL(*mock_datanode, GetStorage())
      .Times(::testing::AnyNumber())
      .WillRepeatedly(::testing::Return(mock_storage.get()));
  impl.SetDataNode(mock_datanode.get());

  // create rpc header
  auto ex_rpc_header = CreateRpcHeader();
  std::shared_ptr<ExtendedBlock> block =
      std::make_shared<ExtendedBlock>("bpid", 1, 128 * 1024 * 1024, 1, false);
  auto eblock = new ::cloudfs::ExtendedBlockProto();
  block->ToProto(eblock);
  auto ex_request_body = new ::cloudfs::SealBlockRequestProto();
  ex_request_body->mutable_header()->set_allocated_block(eblock);

  // construct RpcRequestMessage
  auto ex_message = impl.NewRequest("sealBlock");
  auto ex_m = static_cast<message::SealBlockMessage*>(ex_message);
  ex_m->RpcHeader(ex_rpc_header);
  ex_m->SetRequestBody(ex_request_body);
  EXPECT_CALL(*mock_storage, GetReplica(::testing::_, ::testing::_))
      .Times(testing::AnyNumber())
      .WillRepeatedly(testing::Return(nullptr));

  std::unique_ptr<message::RpcResponseMessage> response(impl.CallMethod(ex_m));
  auto resp_header = response->RpcHeader();
  auto resp_body =
      static_cast<const ::cloudfs::SealBlockResponseProto*>(response->Body());
  EXPECT_EQ(resp_header->status(),
            ::cloudfs::RpcResponseHeaderProto_RpcStatusProto_SUCCESS);
  EXPECT_EQ(resp_header->callid(), CALL_ID);
  EXPECT_EQ(resp_header->clientid(), CLIENT_ID);
  EXPECT_EQ(resp_header->retrycount(), RETRY_COUNT);
  EXPECT_EQ(resp_header->serveripcversionnum(), RPC_CURRENT_VERSION);
  EXPECT_EQ(resp_body->header().status(), ::cloudfs::ERROR_DN_BLOCK_NOT_EXIST);

  std::shared_ptr<ReplicaBeingWritten> replica_info(
      new ReplicaBeingWritten("bpid", 1, 123, 234, "storage_uuid", false, true,
                              1, true, 1, bytestore::PLM_STORAGE_NVME_SSD));
  replica_info->SetBytesAcked(1024);
  replica_info->SetDiskDataLen(2048);
  EXPECT_CALL(*mock_storage, GetReplica(testing::_, testing::_))
      .Times(testing::AnyNumber())
      .WillRepeatedly(testing::Return(replica_info));
  EXPECT_CALL(*mock_datanode, GetHdfsIOService(testing::_))
      .Times(::testing::AnyNumber())
      .WillRepeatedly(::testing::Return(nullptr));
  response.reset(impl.CallMethod(ex_m));
  resp_header = response->RpcHeader();
  resp_body =
      static_cast<const ::cloudfs::SealBlockResponseProto*>(response->Body());
  EXPECT_EQ(resp_header->status(),
            ::cloudfs::RpcResponseHeaderProto_RpcStatusProto_SUCCESS);
  EXPECT_EQ(resp_body->header().status(), ::cloudfs::ERROR_DN_INTERNAL_ERROR);

  bytestore::chunkserver::CSIOServiceOptions options;
  CsEnv env("./ClientDatanodeServiceImplTests");
  HdfsIOService hdfs_io_service(mock_datanode.get(), &env, options, nullptr);
  EXPECT_CALL(*mock_datanode, GetHdfsIOService(testing::_))
      .Times(::testing::AnyNumber())
      .WillRepeatedly(::testing::Return(&hdfs_io_service));

  EXPECT_CALL(*mock_storage,
              SealBlockAsync(::testing::_, ::testing::_, ::testing::_,
                             ::testing::_, ::testing::_, ::testing::_))
      .Times(::testing::AnyNumber())
      .WillRepeatedly(testing::Invoke(
          [&](CsIoService* io_service,
              google::protobuf::RpcController* controller,
              const ::cloudfs::SealBlockRequestProto* request,
              ::cloudfs::SealBlockResponseProto* response, CsMemPool* mem_pool,
              google::protobuf::Closure* done) {
            response->mutable_header()->set_status(::cloudfs::SUCCESS);
            response->set_blocklength(1000);
            done->Run();
          }));
  response.reset(impl.CallMethod(ex_m));
  resp_header = response->RpcHeader();
  resp_body =
      static_cast<const ::cloudfs::SealBlockResponseProto*>(response->Body());
  EXPECT_EQ(resp_header->status(),
            ::cloudfs::RpcResponseHeaderProto_RpcStatusProto_SUCCESS);
  EXPECT_EQ(resp_body->header().status(), ::cloudfs::SUCCESS);
  EXPECT_EQ(resp_body->blocklength(), 1000);

  delete ex_message;
}

}  // namespace bds::dancedn::cloudfs
