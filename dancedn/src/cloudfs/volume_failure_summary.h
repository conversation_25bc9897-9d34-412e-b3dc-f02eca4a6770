// Copyright (c) 2019-present, ByteDance Inc. All rights reserved.

#pragma once

#include <stdint.h>

#include <string>
#include <vector>

#include "cloudfs/exceptions.h"

namespace cloudfs {
namespace datanode {

class VolumeFailureSummaryProto;

}
}  // namespace cloudfs

namespace bds::dancedn::cloudfs {

class VolumeFailureSummary {
 public:
  VolumeFailureSummary(const std::vector<std::string>& failed_storage_locations,
                       uint64_t last_volume_failure_date,
                       uint64_t estimated_capacity_lost_total);
  ~VolumeFailureSummary() {}

  const std::vector<std::string>& GetFailedStorageLocations() const {
    return failed_storage_locations_;
  }

  uint64_t GetLastVolumeFailureDate() const {
    return last_volume_failure_date_;
  }

  uint64_t GetEstimatedCapacityLostTotal() const {
    return estimated_capacity_lost_total_;
  }

  exceptions::Exception ToProto(
      ::cloudfs::datanode::VolumeFailureSummaryProto* proto) const;

 private:
  std::vector<std::string> failed_storage_locations_;
  uint64_t last_volume_failure_date_;
  uint64_t estimated_capacity_lost_total_;
};

}  // namespace bds::dancedn::cloudfs
