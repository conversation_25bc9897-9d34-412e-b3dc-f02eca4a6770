// Copyright (c) 2023-present, ByteDance Inc. All rights reserved.

#pragma once

#include <memory>
#include <string>

#include "chunkserver/env.h"
#include "chunkserver/services/csioservice_impl.h"
#include "cloudfs/exceptions.h"
#include "cloudfs/opstats/op_key.h"
#include "cloudfs/security/block_token_identifier.h"
#include "cloudfs/security/token.h"
#include "cloudfs/services/byterpc_thread_ctx.h"
#include "cloudfs_proto/ClientDatanodeV2Protocol.pb.h"
#include "cloudfs_proto/InterDatanodeProtocol.pb.h"
#include "common/memory_pool.h"
#include "common/metrics.h"
#include "system/timestamp.h"

namespace bds::dancedn::cloudfs {

class DataNode;
class ExtendedBlock;

class HdfsIOService final : public ::cloudfs::ClientDatanodeIOService {
 public:
  HdfsIOService(DataNode* datanode, bytestore::chunkserver::Env* env,
                const bytestore::chunkserver::CSIOServiceOptions& opts,
                ByteRPCThreadCtx* run_thread);
  ~HdfsIOService();

  void getReplicaInfo(::google::protobuf::RpcController* controller,
                      const ::cloudfs::GetReplicaInfoRequestProto* request,
                      ::cloudfs::GetReplicaInfoResponseProto* response,
                      ::google::protobuf::Closure* done) override;
  void createBlock(::google::protobuf::RpcController* controller,
                   const ::cloudfs::CreateBlockRequestProto* request,
                   ::cloudfs::CreateBlockResponseProto* response,
                   ::google::protobuf::Closure* done) override;
  void writeBlock(::google::protobuf::RpcController* controller,
                  const ::cloudfs::WriteBlockRequestProto* request,
                  ::cloudfs::WriteBlockResponseProto* response,
                  ::google::protobuf::Closure* done) override;
  void readBlock(::google::protobuf::RpcController* controller,
                 const ::cloudfs::ClientReadBlockRequestProto* request,
                 ::cloudfs::ClientReadBlockResponseProto* response,
                 ::google::protobuf::Closure* done) override;
  void sealBlock(::google::protobuf::RpcController* controller,
                 const ::cloudfs::SealBlockRequestProto* request,
                 ::cloudfs::SealBlockResponseProto* response,
                 ::google::protobuf::Closure* done) override;
  void syncBlock(::google::protobuf::RpcController* controller,
                 const ::cloudfs::SyncBlockRequestProto* request,
                 ::cloudfs::SyncBlockResponseProto* response,
                 ::google::protobuf::Closure* done) override;
  void calcBlockChecksum(
      ::google::protobuf::RpcController* controller,
      const ::cloudfs::CalcBlockChecksumRequestProto* request,
      ::cloudfs::CalcBlockChecksumResponseProto* response,
      ::google::protobuf::Closure* done) override;
  void pingBlock(::google::protobuf::RpcController* controller,
                 const ::cloudfs::PingBlockRequestProto* request,
                 ::cloudfs::PingBlockResponseProto* response,
                 ::google::protobuf::Closure* done) override;
  void finalizeBlock(::google::protobuf::RpcController* controller,
                     const ::cloudfs::FinalizeBlockRequestProto* request,
                     ::cloudfs::FinalizeBlockResponseProto* response,
                     ::google::protobuf::Closure* done) override;

  virtual bytestore::CSIOService* GetCSIOService() {
    return cs_io_service_.get();
  }
  static void WaitFlyingIODone();

 private:
  template <class REQUEST, class RESPONSE>
  struct CallbackCtx {
    CallbackCtx(bytestore::MemoryPool* mem_pool,
                ::google::protobuf::RpcController* controller,
                const REQUEST* req, RESPONSE* res, const OpKey& op_key,
                const bytestore::Tags& tags, ::google::protobuf::Closure* done)
        : mem_pool_(mem_pool),
          controller_(controller),
          req_(req),
          res_(res),
          op_key_(op_key),
          tags_(tags),

          done_(done),
          start_time_us_(byte::GetCurrentTimeInUs()) {}
    bytestore::MemoryPool* mem_pool_;
    ::google::protobuf::RpcController* controller_;
    const REQUEST* req_;
    RESPONSE* res_;
    OpKey op_key_;
    bytestore::Tags tags_;
    ::google::protobuf::Closure* done_;
    int64_t start_time_us_;
  };

 private:
  exceptions::Exception CheckAccess(ExtendedBlock* block, const Token& token,
                                    const AccessMode& mode);

  void GetReplicaInfoCb(
      CallbackCtx<::cloudfs::GetReplicaInfoRequestProto,
                  ::cloudfs::GetReplicaInfoResponseProto>* ctx);
  void CreateBlockCb(CallbackCtx<::cloudfs::CreateBlockRequestProto,
                                 ::cloudfs::CreateBlockResponseProto>* ctx);
  void WriteBlockCb(CallbackCtx<::cloudfs::WriteBlockRequestProto,
                                ::cloudfs::WriteBlockResponseProto>* ctx);
  void ReadBlockCb(CallbackCtx<::cloudfs::ClientReadBlockRequestProto,
                               ::cloudfs::ClientReadBlockResponseProto>* ctx);
  void SealBlockCb(CallbackCtx<::cloudfs::SealBlockRequestProto,
                               ::cloudfs::SealBlockResponseProto>* ctx);
  void SyncBlockCb(CallbackCtx<::cloudfs::SyncBlockRequestProto,
                               ::cloudfs::SyncBlockResponseProto>* ctx);
  void CalcBlockChecksumCb(
      CallbackCtx<::cloudfs::CalcBlockChecksumRequestProto,
                  ::cloudfs::CalcBlockChecksumResponseProto>* ctx);
  void PingBlockCb(CallbackCtx<::cloudfs::PingBlockRequestProto,
                               ::cloudfs::PingBlockResponseProto>* ctx);
  void FinalizeBlockCb(CallbackCtx<::cloudfs::FinalizeBlockRequestProto,
                                   ::cloudfs::FinalizeBlockResponseProto>* ctx);
  void AcquireWriteTokenCb(
      CallbackCtx<::cloudfs::WriteBlockRequestProto,
                  ::cloudfs::WriteBlockResponseProto>* ctx);
  void AcquireReadTokenCb(
      CallbackCtx<::cloudfs::ClientReadBlockRequestProto,
                  ::cloudfs::ClientReadBlockResponseProto>* ctx);

  inline bytestore::MemoryPool* NewMemPool();

  template <class REQUEST>
  uint64_t ParseNsId(REQUEST* req);
  template <class REQUEST>
  OpKey OpKeyFromRequest(google::protobuf::RpcController* ctl, REQUEST* req,
                         Operation op);
  inline void RecordTrace(const OpKey& op_key, const int64_t& offset,
                          const int64_t& bytes, const int64_t& start_ms,
                          const int64_t& cost_us, bool success = true,
                          const std::string& msg = "");

 private:
  DataNode* datanode_;
  bytestore::chunkserver::Env* env_;
  ByteRPCThreadCtx* run_thread_;
  std::unique_ptr<bytestore::chunkserver::CSIOServiceImpl<
      bytestore::chunkserver::RpcType::ByteRPC>>
      cs_io_service_;
  bytestore::MemoryPoolRepo async_block_mem_pool_;
  std::atomic<uint16_t> mem_pool_index_;
};

}  // namespace bds::dancedn::cloudfs
