// Copyright (c) 2023-present, ByteDance Inc. All rights reserved.

#pragma once

#include "byterpc/controller.h"

namespace bds::dancedn::cloudfs {

// SimpleController should only be used in local environment.
class SimpleController : public byterpc::Controller {
 public:
  SimpleController() {}

  byterpc::util::EndPoint local_side() override {
    return byterpc::util::EndPoint();
  }

  byterpc::util::EndPoint remote_side() override {
    return byterpc::util::EndPoint();
  }

  void OnRecycle() override {}
};

}  // namespace bds::dancedn::cloudfs
