// Copyright (c) 2018-present, ByteDance Inc. All rights reserved.

#include "cloudfs/thread_manager.h"

#include <functional>
#include <memory>

#include "byte/concurrent/mutex.h"
#include "byte/system/timestamp.h"
#include "concurrent/count_down_latch.h"
#include "gtest/gtest.h"
#include "thread/this_thread.h"

namespace bds::dancedn::cloudfs {

class ThreadManagerTests : public ::testing::Test {
 public:
  void SetUp() {}
  void TearDown() {}
};

TEST_F(ThreadManagerTests, ExtendedThreadManager) {
  ExtendedThreadManager thread_pool("test", 1, []() {
    return 2;
  });
  byte::CountDownLatch latch1(1);
  byte::CountDownLatch latch2(1);
  byte::CountDownLatch latch3(1);
  EXPECT_EQ(thread_pool.GetFastNum(), 0);
  EXPECT_EQ(thread_pool.GetSlowNum(), 0);
  thread_pool.AddTask([&latch1]() {
    byte::ThisThread::SleepInSeconds(1);
    latch1.CountDown();
  });
  EXPECT_EQ(thread_pool.GetFastNum(), 1);
  EXPECT_EQ(thread_pool.GetSlowNum(), 0);
  thread_pool.AddTask([&latch2]() {
    byte::ThisThread::SleepInSeconds(2);
    latch2.CountDown();
  });
  EXPECT_EQ(thread_pool.GetFastNum(), 2);
  EXPECT_EQ(thread_pool.GetSlowNum(), 0);
  thread_pool.AddTask([&latch3]() {
    byte::ThisThread::SleepInSeconds(4);
    latch3.CountDown();
  });
  EXPECT_EQ(thread_pool.GetFastNum(), 2);
  EXPECT_EQ(thread_pool.GetSlowNum(), 1);
  latch1.Wait();
  // thread_pool run callback firstly then dec the num, sleep a while.
  byte::ThisThread::SleepInMs(100);
  EXPECT_EQ(thread_pool.GetFastNum(), 1);
  EXPECT_EQ(thread_pool.GetSlowNum(), 1);
  latch2.Wait();
  byte::ThisThread::SleepInMs(100);
  EXPECT_EQ(thread_pool.GetFastNum(), 0);
  EXPECT_EQ(thread_pool.GetSlowNum(), 1);
  latch3.Wait();
  byte::ThisThread::SleepInMs(100);
  EXPECT_EQ(thread_pool.GetFastNum(), 0);
  EXPECT_EQ(thread_pool.GetSlowNum(), 0);
}

}  // namespace bds::dancedn::cloudfs
