// copyright (c) 2019-present, bytedance inc. all rights reserved.

#pragma once

#include <arpa/inet.h>
#include <memory.h>
#include <stdio.h>
#include <sys/socket.h>
#include <sys/types.h>

#include <cassert>
#include <cstdint>
#include <sstream>
#include <string>

#include "chunkserver/common/chunkserver_types.h"
#include "common/store_types.h"
#include "string/format/print.h"

namespace bds::dancedn::cloudfs {

struct BlockMeta {
  uint64_t gs_;

  uint8_t fin_ : 1;
  uint8_t tmp_ : 1;
  uint8_t checksum_enabled_ : 1;
  uint8_t flags_ : 5;

  // if a block is evictable, the block has been uploaded to tos
  uint8_t is_evictable_ : 1;
  uint8_t namespace_type : 3;
  // Due to the old version of `cfs_reserved_` field not being set to 0, there
  // may be dirty data. It is recommended to never use `cfs_reserved_` to avoid
  // compatibility issues.
  uint8_t cfs_reserved_ : 4;

  // In the older versions, the pin_ field was used to mark the storage class
  // (hot or warm) of a replica. However, in the new version, this field has
  // been deprecated, and storage_class_ should be used instead. Moreover, the
  // field has been renamed to deprecated_pin_. The name pin_ will be used for
  // pin/unpin semantics.
  uint8_t deprecated_pin_ : 1;
  uint8_t sealed_ : 1;
  uint8_t storage_class_ : 4;
  uint8_t pin_ : 1;
  uint8_t cfs_flags_ : 1;

  uint32_t checksum_type_ : 4;
  uint32_t bytes_per_checksum_ : 28;

  char reserved_[17];

  BlockMeta()
      : gs_(0),
        fin_(0),
        tmp_(0),
        flags_(0),
        is_evictable_(0),
        namespace_type(0),
        cfs_reserved_(0),
        deprecated_pin_(0),
        sealed_(0),
        storage_class_(0),
        pin_(0),
        cfs_flags_(0) {
    memset(reserved_, 0, sizeof(reserved_));
  }

  BlockMeta(uint64_t gs, int fin, int tmp, uint8_t is_evictable,
            uint8_t ns_type)
      : gs_(gs),
        fin_(fin),
        tmp_(tmp),
        is_evictable_(is_evictable),
        namespace_type(ns_type),
        cfs_reserved_(0),
        deprecated_pin_(0),
        sealed_(0),
        storage_class_(0),
        pin_(0),
        cfs_flags_(0) {
    memset(reserved_, 0, sizeof(reserved_));
  }
} __attribute__((packed));

enum DanceDNChunkType { BLOCK = 0, PARTIAL_BLOCK = 1 };

struct ChunkIdMeta {
  uint64_t block_id_;
  uint64_t namespace_id_;
  uint64_t timestamp_ : 44;
  uint64_t type_ : 4;
  uint64_t is_checksum_ : 1;
  uint64_t reserved_ : 15;

  ChunkIdMeta()
      : block_id_(0),
        namespace_id_(0),
        timestamp_(0),
        type_(DanceDNChunkType::BLOCK),
        is_checksum_(0),
        reserved_(0) {}

  ChunkIdMeta(uint64_t block_id, uint64_t namespace_id, uint64_t timestamp)
      : block_id_(block_id),
        namespace_id_(namespace_id),
        timestamp_(timestamp),
        type_(DanceDNChunkType::BLOCK),
        is_checksum_(0),
        reserved_(0) {}

  inline std::string GetBlockPoolId() {
    return byte::StringPrint("BP-%llu-%llu", namespace_id_, timestamp_);
  }

  inline bytestore::ChunkId* ToChunkId() {
    return reinterpret_cast<bytestore::ChunkId*>(reinterpret_cast<char*>(this));
  }
};
static_assert(sizeof(ChunkIdMeta) == sizeof(bytestore::ChunkId),
              "size must be equal");

// Everything about the actual allocation details of bit-fields within the class
// object.
//   - For example, on some platforms, bit-fields don't straddle bytes, on
//   others they do.
//   - Also, on some platforms, bit-fields are packed left-to-right, on others
//   right-to-left.
// So, we need to use DeprecatedChunkIdMeta to check the compatibility issues.
struct DeprecatedChunkIdMeta {
  uint64_t block_id_;
  uint64_t namespace_id_;
  uint64_t timestamp_ : 48;
  uint64_t is_checksum_ : 1;
  uint64_t reserved_ : 15;

  static bool CheckChunkIdMetacompatibility() {
    DeprecatedChunkIdMeta deprecated;
    deprecated.block_id_ = 1081562020ULL;
    deprecated.namespace_id_ = 13882352884257380248ULL;
    deprecated.timestamp_ = 1546064706393ULL;
    deprecated.is_checksum_ = 1;
    deprecated.reserved_ = 0;
    ChunkIdMeta* meta = reinterpret_cast<ChunkIdMeta*>(&deprecated);
    if (deprecated.namespace_id_ != meta->namespace_id_) {
      return false;
    }
    if (deprecated.timestamp_ != meta->timestamp_) {
      return false;
    }
    if (deprecated.is_checksum_ != meta->is_checksum_) {
      return false;
    }
    if (deprecated.reserved_ != meta->reserved_) {
      return false;
    }
    if (meta->type_ != 0) {
      return false;
    }
    return true;
  }
};
static_assert(sizeof(DeprecatedChunkIdMeta) == sizeof(bytestore::ChunkId),
              "size must be equal");

union BpidFormat {
  struct {
    uint32_t random;
    uint32_t ip;
  } hdfs;
  uint64_t cfs;
};

struct PartialBlockKey {
  PartialBlockKey()
      : block_id_(0),
        ns_id_(0),
        reserved_(0),
        type_(DanceDNChunkType::PARTIAL_BLOCK),
        first_sector_offset_(0) {}
  PartialBlockKey(uint64_t block_id, uint64_t ns_id,
                  uint16_t first_sector_offset)
      : block_id_(block_id),
        ns_id_(ns_id),
        reserved_(0),
        type_(DanceDNChunkType::PARTIAL_BLOCK),
        first_sector_offset_(first_sector_offset) {}
  uint64_t block_id_;
  uint64_t ns_id_;
  uint64_t reserved_ : 44;
  uint64_t type_ : 4;
  uint64_t first_sector_offset_ : 16;
} __attribute__((packed));

static_assert(sizeof(PartialBlockKey) == sizeof(bytestore::ChunkId),
              "size must be equal");

struct PartialBlockXAttr {
  uint64_t gs_;
  uint32_t block_length_;
  uint32_t pin_ : 1;
  uint32_t storage_class_ : 4;
  uint32_t ns_type_ : 3;
  uint32_t reserved_flags_ : 24;
  char reserved_[16];
} __attribute__((packed));

static_assert(sizeof(PartialBlockXAttr) == XATTR_BYTES, "size must be equal");

}  // namespace bds::dancedn::cloudfs
