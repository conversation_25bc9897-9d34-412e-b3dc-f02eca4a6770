// Copyright (c) 2019-present, ByteDance Inc. All rights reserved.

#pragma once

#include <queue>
#include <string>
#include <vector>

#include "byte/base/atomic.h"
#include "cloudfs/exceptions.h"
#include "cloudfs/opstats/trace_baggage.h"
#include "cloudfs/pipeline_ack.h"
#include "cloudfs/thread.h"
#include "cloudfs_proto/datatransfer.pb.h"

namespace bds::dancedn::cloudfs {

namespace io {
class Connection;
}

class PacketAck;
class PipelineAck;
class BlockReceiver;
class DatanodeInfo;
class ConditionVariable;

using Status = ::cloudfs::Status;

enum PacketResponderType : uint8_t {
  NON_PIPELINE,
  LAST_IN_PIPELINE,
  HAS_DOWNSTREAM_IN_PIPELINE
};

class PacketResponder : public Thread {
 public:
  PacketResponder(BlockReceiver* receiver, io::Connection* upstream_out,
                  io::Connection* downstream_in,
                  std::vector<DatanodeInfo*> downstreams,
                  const TraceBaggage& trace_baggage);
  ~PacketResponder();

  void Enqueue(const int64_t seqno, const bool last_packet,
               const int64_t offset, const Status ack_status, int64_t time_ns);

  exceptions::Exception SendOOB(const Status ack_status);

  void Run();

  void Close();

  void Stop();

  std::string ToString() const;

 private:
  bool ShouldRun() const;
  bool IsRunning() const;
  void MarkStop();
  exceptions::Exception SendAck(PipelineAck* ack, int64_t seqno,
                                int64_t offset_in_block, Status cur_status,
                                PipelineAckTimeTrace& pipeline_time_trace);
  exceptions::Exception SendAckWithoutPending(
      PipelineAck* ack, int64_t seqno, int64_t offset_in_block,
      Status cur_status, PipelineAckTimeTrace& pipeline_time_trace);
  void RemoveAckHead();
  // wait for a packet with given seqno to be enqueued to ack_queue
  PacketAck WaitForAckHeader(int64_t seqno);
  exceptions::Exception FinalizeBlock(uint64_t start_time);

  void UpdatePipelineTimeTrace(int64_t seqno, const PipelineAck& downstream_ack,
                               int64_t ack_enqueue_nano_time,
                               int64_t ack_recv_nano_time,
                               PipelineAckTimeTrace& pipeline_time_trace);

 private:
  BlockReceiver* receiver_;
  io::Connection* upstream_;
  io::Connection* downstream_;
  // distinguish who stopped responder, itself or block_receiver?
  mutable bool running_;
  mutable bool sending_;
  std::queue<PacketAck> ack_queue_;
  byte::Mutex queue_mutex_;
  ConditionVariable queue_cv_;
  PacketResponderType type_;
  std::string details_;
  byte::Mutex send_mutex_;
  ConditionVariable send_cv_;
  bool has_stopped_;
  TraceBaggage trace_baggage_;
};

}  // namespace bds::dancedn::cloudfs
