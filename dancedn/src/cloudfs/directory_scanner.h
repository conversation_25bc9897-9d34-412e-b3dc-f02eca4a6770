// Copyright (c) 2018-present, ByteDance Inc. All rights reserved.

#pragma once

#include <deque>
#include <future>  // NOLINT
#include <memory>
#include <set>
#include <string>
#include <unordered_map>
#include <utility>
#include <vector>

#include "byte/base/atomic.h"
#include "byte/base/closure.h"
#include "byte/concurrent/mutex.h"
#include "chunkserver/common/chunkserver_types.h"
#include "chunkserver/common/io_request.h"
#include "cloudfs/scanner.h"
#include "cloudfs/storage_type.h"
#include "cloudfs/store/chunkserver/disk_usage.h"
#include "common/memory_pool_repo.h"
#include "common/store_types.h"

namespace bytestore {
namespace chunkserver {

class Disk;
class StorageDirectory;
}  // namespace chunkserver
}  // namespace bytestore
namespace bds::dancedn::cloudfs {

class PartialBlockStore;

class Store;

struct DiskDirectory {
  bytestore::chunkserver::Disk* disk_;
  StorageDirectory sd_;
};

class DirectoryScanner : public Scanner<DiskDirectory> {
 public:
  explicit DirectoryScanner(Store* storage, PartialBlockStore* partial_store);

  ~DirectoryScanner();

  void SetDiskUsage(const std::shared_ptr<DiskUsage>& disk_usage) {
    disk_usage_ = disk_usage;
  }

  void Start() override;
  void Stop() override;

 private:
  void execute() override;
  void reschedule() override;
  DiskDirectory GetNextDisk() override;
  void ScanDisk(int index, int scan_depth) override;

 private:
  Store* storage_;
  PartialBlockStore* partial_store_;
  std::unique_ptr<bytestore::AsyncThread> run_thread_;
  std::unique_ptr<Closure<void>> timer_callback_;
  std::unique_ptr<bytestore::Timer> timer_;
  std::unique_ptr<byte::Mutex> lock_;
  byte::Atomic<bool> stopping_;
  std::deque<DiskDirectory> disk_directory_queue_;
  std::shared_ptr<DiskUsage> disk_usage_;
};

}  // namespace bds::dancedn::cloudfs
