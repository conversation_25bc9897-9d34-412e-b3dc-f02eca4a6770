// Copyright (c) 2019-present, ByteDance Inc. All rights reserved.

#pragma once

#include <stdint.h>

namespace bds::dancedn::cloudfs {

class Checksum {
 public:
  Checksum() {}
  virtual ~Checksum() {}

  virtual void Update(uint32_t b) = 0;
  virtual void Update(const uint8_t* b, uint32_t off, uint32_t len) = 0;
  virtual uint64_t GetValue() const = 0;
  virtual void Reset() = 0;
};

}  // namespace bds::dancedn::cloudfs
