// Copyright (c) 2019-present, ByteDance Inc. All rights reserved.

#pragma once

#include <memory>
#include <string>
#include <vector>

#include "cloudfs/datanode_storage.h"

namespace bds::dancedn::cloudfs {

class ReceivedDeletedBlockInfo;

class StorageReceivedDeletedBlocks {
 public:
  StorageReceivedDeletedBlocks(
      const std::string& storage_id,
      const std::vector<std::shared_ptr<ReceivedDeletedBlockInfo>>& blocks);

  StorageReceivedDeletedBlocks(
      const DatanodeStorage& storage,
      const std::vector<std::shared_ptr<ReceivedDeletedBlockInfo>>& blocks);

  ~StorageReceivedDeletedBlocks();
  std::string GetStorageID() const {
    return storage_.GetStorageID();
  }
  DatanodeStorage GetStorage() const {
    return storage_;
  }
  std::vector<std::shared_ptr<ReceivedDeletedBlockInfo>> GetBlocks() const {
    return blocks_;
  }
  size_t GetBlocksNum() {
    return blocks_.size();
  }

 private:
  DatanodeStorage storage_;
  std::vector<std::shared_ptr<ReceivedDeletedBlockInfo>> blocks_;
};

}  // namespace bds::dancedn::cloudfs
