// Copyright (c) 2019-present, ByteDance Inc. All rights reserved.

#pragma once

#include "cloudfs/message/base_rpc_message.h"
#include "google/protobuf/message.h"

namespace cloudfs {
class RpcRequestHeaderProto;
class RequestHeaderProto;
}  // namespace cloudfs

namespace bds::dancedn::cloudfs {

namespace io {
class IOChunk;
}

namespace message {

class RpcRequestMessage : public BaseRpcMessage {
 public:
  RpcRequestMessage();

  virtual ~RpcRequestMessage();

  void RpcHeader(::cloudfs::RpcRequestHeaderProto* proto);
  void RequestHeader(::cloudfs::RequestHeaderProto* proto);

  ::cloudfs::RpcRequestHeaderProto* RpcHeader() const;
  ::cloudfs::RequestHeaderProto* RequestHeader() const;

  ::cloudfs::RpcRequestHeaderProto* ReleaseRpcHeader();
  ::cloudfs::RequestHeaderProto* ReleaseRequestHeader();

  virtual bool DecodeBody(cloudfs::io::IOChunk* chunk, int len) = 0;
  virtual int Encode(cloudfs::io::IOChunk* chunk);
  virtual google::protobuf::Message* Message() const = 0;

  static int DecodeRpcHeader(cloudfs::io::IOChunk* chunk, int len,
                             ::cloudfs::RpcRequestHeaderProto** proto);

  static int DecodeRequestHeader(cloudfs::io::IOChunk* chunk, int len,
                                 ::cloudfs::RequestHeaderProto** proto);

 protected:
  ::cloudfs::RpcRequestHeaderProto* rpc_header_;
  ::cloudfs::RequestHeaderProto* req_header_;
};

}  // namespace message
}  // namespace bds::dancedn::cloudfs
