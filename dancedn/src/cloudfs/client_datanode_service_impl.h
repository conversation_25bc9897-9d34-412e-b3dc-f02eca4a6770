// Copyright (c) 2019-present, ByteDance Inc. All rights reserved.

#pragma once

#include <functional>
#include <memory>
#include <string>
#include <unordered_map>
#include <vector>

#include "cloudfs/client_datanode_service.h"
#include "cloudfs/exceptions.h"
#include "cloudfs/opstats/op_key.h"
#include "cloudfs/opstats/operation.h"
#include "cloudfs_proto/ClientDatanodeProtocol.pb.h"
#include "common/memory_pool_repo.h"

namespace cloudfs {
class RpcRequestHeaderProto;
class RequestHeaderProto;
class RpcResponseHeaderProto;
}  // namespace cloudfs

namespace cloudfs {
class RefreshNamenodesResponseProto;
class ShutdownDatanodeResponseProto;
class TriggerBlockReportResponseProto;
}  // namespace cloudfs

namespace bds::dancedn::cloudfs {

class DataNode;

namespace message {
class RpcRequestMessage;
class RpcResponseMessage;
}  // namespace message

class ClientDatanodeServiceImpl final : public ClientDatanodeService {
 public:
  ClientDatanodeServiceImpl();
  explicit ClientDatanodeServiceImpl(DataNode* datanode);
  ~ClientDatanodeServiceImpl() {
    // delete datanode_ by DataNode
  }

  void SetDataNode(DataNode* datanode) {
    datanode_ = datanode;
  }

  message::RpcRequestMessage* NewRequest(const std::string& name);
  message::RpcResponseMessage* CallMethod(message::RpcRequestMessage* r);

  std::string GetProtocolName() {
    return ClientDatanodeServiceImpl::PROTOCOL_NAME;
  }

 public:
  static const char PROTOCOL_NAME[];
  static const uint32_t PROTOCOL_VERSION;

 private:
  using RpcResponse = message::RpcResponseMessage;
  using RpcRequest = message::RpcRequestMessage;
  using Method = RpcResponse* (ClientDatanodeServiceImpl::*)(RpcRequest*);

  typedef RpcRequest* (*Builder)();

  using Fn = std::function<RpcResponse*(RpcRequest*)>;

 private:
  OpKey BuildKey(message::RpcRequestMessage* r, const std::string& bpid,
                 uint64_t block_id, const Operation& op);

  void Initialize();

  ::cloudfs::RpcResponseHeaderProto* ProcessExceptionAfterCallMethod(
      const exceptions::Exception& e,
      const ::cloudfs::RpcRequestHeaderProto* request_header) const;

  void Register(const std::string& name, const Method& method,
                const Builder& builder);

  RpcResponse* getReplicaVisibleLength(RpcRequest* r);

  RpcResponse* getReplicaVisibleLengthV2(RpcRequest* r);

  RpcResponse* refreshNamenodes(RpcRequest* r);

  RpcResponse* deleteBlockPool(RpcRequest* r);

  RpcResponse* getBlockLocalPathInfo(RpcRequest* r);

  RpcResponse* getHdfsBlockLocations(RpcRequest* r);

  RpcResponse* shutdownDatanode(RpcRequest* r);

  RpcResponse* getDatanodeInfo(RpcRequest* r);

  RpcResponse* getReconfigurationStatus(RpcRequest* r);

  RpcResponse* startReconfiguration(RpcRequest* r);

  RpcResponse* listReconfigurableProperties(RpcRequest* r);

  RpcResponse* triggerBlockReport(RpcRequest* r);

  RpcResponse* getBlocks(RpcRequest* r);

  RpcResponse* initReplicaRecovery(RpcRequest* request);

  RpcResponse* updateReplicaUnderRecovery(RpcRequest* request);

  RpcResponse* sealBlock(RpcRequest* request);

  RpcResponse* readBlock(RpcRequest* request);

  RpcResponse* calculateCrc(RpcRequest* request);

 private:
  std::unordered_map<std::string, int> method_ids_;
  std::vector<ClientDatanodeServiceImpl::Method> methods_;
  std::vector<ClientDatanodeServiceImpl::Builder> builders_;
  DataNode* datanode_;
  bytestore::MemoryPoolRepo mem_pool_repo_;
};

}  // namespace bds::dancedn::cloudfs
