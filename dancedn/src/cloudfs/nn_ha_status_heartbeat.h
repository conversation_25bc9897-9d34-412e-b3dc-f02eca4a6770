// Copyright (c) 2019-present, ByteDance Inc. All rights reserved.

#pragma once

#include <string>

#include "cloudfs/exceptions.h"
#include "cloudfs/ha_service_state.h"
#include "cloudfs_proto/DatanodeProtocol.pb.h"

namespace bds::dancedn::cloudfs {

class NNHAStatusHeartbeat {
 public:
  explicit NNHAStatusHeartbeat(HAServiceState state, int64_t txid);
  NNHAStatusHeartbeat();
  ~NNHAStatusHeartbeat() {}
  HAServiceState GetState() const {
    return state_;
  }
  int64_t GetTxid() const {
    return txid_;
  }
  static NNHAStatusHeartbeat ParseProto(
      const ::cloudfs::datanode::NNHAStatusHeartbeatProto& proto);

 private:
  HAServiceState state_;
  int64_t txid_;
};

}  // namespace bds::dancedn::cloudfs
