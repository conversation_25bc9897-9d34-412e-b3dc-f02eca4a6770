// Copyright (c) 2019-present, ByteDance Inc. All rights reserved.

#pragma once

#include <memory>
#include <set>
#include <string>
#include <unordered_set>
#include <utility>
#include <vector>

#include "cloudfs/exceptions.h"
#include "cloudfs_proto/RpcHeader.pb.h"
#include "cloudfs_proto/datatransfer.pb.h"
#include "cloudfs_proto/hdfs.pb.h"
#include "google/protobuf/message.h"

namespace bds::dancedn::cloudfs {

namespace io {
class Connection;
}

class DatanodeInfo;
class DatanodeInfoWithStorage;
class LocatedBlock;

class Joiner {
 public:
  explicit Joiner(const std::string& separator) : separator_(separator) {}
  ~Joiner() {}

  std::string Join(const std::vector<std::string>& v);
  std::string Join(const std::set<std::string>& v);
  std::string Join(const std::unordered_set<std::string>& v);

 private:
  std::string separator_;
};

template <typename T>
class ObjectJoiner {
 public:
  explicit ObjectJoiner(const std::string& separator) : separator_(separator) {}
  ~ObjectJoiner() {}

  std::string Join(const std::vector<T*>& v);
  std::string Join(const std::vector<std::shared_ptr<T>>& v);

 private:
  std::string separator_;
};

// "*************:9208" -> ":" got "*************" and "9208"
// "[fdbd:dc02:103:73::162]:9209" -> ":" got "[fdbd:dc02:103:73::162]" and
// "9209" "fdbd:dc02:103:73::162:9209" -> ":" got "fdbd:dc02:103:73::162" and
// "9209"
void SplitStringForAddr(const std::string& str, const char* delimiter,
                        std::vector<std::string>* result);

// dancenn1:*************:9208 -> ":" got "dancenn1", "*************" and "9208"
// dancenn2:[fdbd:dc02:103:73::162]:9209 -> ":" got "dancenn2",
// "[fdbd:dc02:103:73::162]" and "9209" dancenn2:fdbd:dc02:103:73::162:9209 ->
// ":" got "dancenn2", "fdbd:dc02:103:73::162" and "9209"
void SplitStringForNamenode(const std::string& str, const char* delimiter,
                            std::vector<std::string>* result);

// trim '[' and ']'
void TrimIPString(std::string* ip_str);

template class ObjectJoiner<DatanodeInfo>;
template class ObjectJoiner<DatanodeInfoWithStorage>;
template class ObjectJoiner<LocatedBlock>;

class Random {
 public:
  static uint32_t Next();
  static uint32_t Next(uint32_t bound);
};

int64_t Now();

std::string GenerateClientID();

int Align8(int n);

exceptions::Exception ParseHeaderException(
    const ::cloudfs::RpcResponseHeaderProto* header);

// Read data_len(varint32) and data from conn, and transform data into proto.
// the user should make sure that len(varint32) + data_len >= 5
int ReadDelimitedFrom(io::Connection* conn, google::protobuf::Message* proto);

int ReadStatusProto(io::Connection* conn, google::protobuf::Message* proto);

// Write proto_len(varint32) and proto_data into conn.
int WriteDelimitedTo(io::Connection* conn,
                     const google::protobuf::Message& proto);

int SendResponse(io::Connection* conn, const ::cloudfs::Status& st,
                 const std::string& msg);

int SendResponse(io::Connection* conn, const ::cloudfs::Status& st);

std::string ResolveDc(const uint8_t* addr);

::cloudfs::RpcResponseHeaderProto* BuildResponseHeader(
    const ::cloudfs::RpcRequestHeaderProto* rpc_header);

::cloudfs::RpcResponseHeaderProto* BuildResponseHeaderWithException(
    const ::cloudfs::RpcRequestHeaderProto* rpc_header,
    const exceptions::Exception& e,
    const ::cloudfs::RpcResponseHeaderProto_RpcStatusProto& status,
    const ::cloudfs::RpcResponseHeaderProto_RpcErrorCodeProto& errcode);

::cloudfs::RpcResponseHeaderProto_RpcStatusProto ClassifyException(
    const exceptions::Exception& e);

::cloudfs::RpcResponseHeaderProto_RpcErrorCodeProto TranslateException(
    const exceptions::Exception& e);

}  // namespace bds::dancedn::cloudfs
