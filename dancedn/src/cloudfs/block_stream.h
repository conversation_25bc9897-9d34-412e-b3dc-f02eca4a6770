// Copyright (c) 2019-present, ByteDance Inc. All rights reserved.

#pragma once

#include <memory>
#include <string>

#include "cloudfs/cfs/storage_class.h"
#include "cloudfs/exceptions.h"
#include "cloudfs/extended_block.h"

namespace bds::dancedn::cloudfs {

namespace io {

class IOChunk;

}  // namespace io

enum BlockStreamType {
  TOS,
  DISK,
  HDFS,
  PARTIAL_STORE,
};

class BlockStream {
 public:
  BlockStream(ExtendedBlock* block, uint64_t offset, uint32_t length)
      : block_(block->Clone()),
        start_offset_(offset),
        length_(length),
        curr_offset_(offset) {}
  virtual ~BlockStream() {}
  virtual exceptions::Exception Read(uint32_t size, io::IOChunk* chunk,
                                     /* OUT */ uint32_t* returned_size) = 0;

  const std::shared_ptr<ExtendedBlock>& GetBlock() const {
    return block_;
  }
  uint64_t GetStartOffset() const {
    return start_offset_;
  }
  uint32_t GetLength() const {
    return length_;
  }
  uint64_t GetCurrentOffset() const {
    return curr_offset_;
  }

  virtual BlockStreamType GetType() const = 0;
  // Check if the existence of read range.
  virtual exceptions::Exception CheckExist() = 0;

 protected:
  void IncreaseCurrentOffset(uint32_t size) {
    curr_offset_ += size;
  }

 private:
  std::shared_ptr<ExtendedBlock> block_;
  uint64_t start_offset_;
  uint32_t length_;
  uint64_t curr_offset_;
};

}  // namespace bds::dancedn::cloudfs
