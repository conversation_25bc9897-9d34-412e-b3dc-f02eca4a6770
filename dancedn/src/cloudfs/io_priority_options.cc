// Copyright (c) 2019-present, ByteDance Inc. All rights reserved.

#include "cloudfs/io_priority_options.h"

namespace bds::dancedn::cloudfs {

IOPriorityOptions::IOPriorityOptions()
    : io_priority_class_(
          ::cloudfs::BaseHeaderProto_IOPriorityClass_BEST_EFFORT),
      io_priority_level_(4) {}

IOPriorityOptions::IOPriorityOptions(
    const ::cloudfs::BaseHeaderProto_IOPriorityClass& io_priority_class,
    uint32_t io_priority_level)
    : io_priority_class_(io_priority_class),
      io_priority_level_(io_priority_level) {}

::cloudfs::BaseHeaderProto_IOPriorityClass
IOPriorityOptions::GetIoPriorityClass() const {
  return io_priority_class_;
}

}  // namespace bds::dancedn::cloudfs
