// Copyright (c) 2020-present, ByteDance Inc. All rights reserved.

#include "cloudfs/security/block_key.h"

namespace bds::dancedn::cloudfs {

BlockKey BlockKey::ParseProto(const ::cloudfs::BlockKeyProto& block_key_proto) {
  uint32_t key_id = block_key_proto.keyid();
  uint64_t expire_date = block_key_proto.expirydate();
  std::string key_bytes;
  if (block_key_proto.has_keybytes()) {
    key_bytes = block_key_proto.keybytes();
  }
  return BlockKey(key_id, expire_date, key_bytes);
}

std::vector<BlockKey> BlockKey::ParseProtos(
    const ::google::protobuf::RepeatedPtrField<::cloudfs::BlockKeyProto>&
        block_key_protos) {
  std::vector<BlockKey> all_keys;
  for (const auto& block_key_proto : block_key_protos) {
    all_keys.emplace_back(Parse<PERSON>roto(block_key_proto));
  }
  return all_keys;
}

}  // namespace bds::dancedn::cloudfs
