// Copyright (c) 2020-present, ByteDance Inc. All rights reserved.
#ifndef BLOCK_TOKEN_IDENTIFIER_H_
#define BLOCK_TOKEN_IDENTIFIER_H_

#pragma once

#include <cstdint>
#include <string>
#include <utility>
#include <vector>

namespace bds::dancedn::cloudfs {

enum class AccessMode { READ, WRITE, COPY, REPLACE, UNKNOWN };

std::string AccessModeToStr(const AccessMode& access_mode);

class BlockTokenIdentifier {
 public:
  BlockTokenIdentifier() = default;

  BlockTokenIdentifier(std::string user_id, std::string bpid, uint64_t block_id,
                       const std::vector<AccessMode>& access_modes)
      : user_id_(std::move(user_id)),
        block_pool_id_(std::move(bpid)),
        block_id_(block_id) {
    for (const auto& mode : access_modes) {
      access_modes_.emplace_back(AccessModeToStr(mode));
    }
  }

  std::string GetKind() const {
    std::string res(KIND_NAME);
    return res;
  }

  int64_t GetExpireDate() const {
    return expire_date_;
  }

  void SetExpireDate(const int64_t& expire_date) {
    expire_date_ = expire_date;
  }

  std::string GetUserId() const {
    return user_id_;
  }

  void SetUserId(std::string user_id) {
    user_id_ = std::move(user_id);
  }

  std::string GetBpid() const {
    return block_pool_id_;
  }

  void SetBpid(std::string bpid) {
    block_pool_id_ = std::move(bpid);
  }

  std::vector<std::string> GetAccessModes() const {
    return access_modes_;
  }

  int32_t GetKeyId() const {
    return key_id_;
  }

  void SetKeyId(const int32_t& key_id) {
    key_id_ = key_id;
  }

  uint64_t GetBlockId() const {
    return block_id_;
  }

  std::string ToString() const;

  bool Equals(const BlockTokenIdentifier& block_token_identifier) const;

  bool ReadFields(const std::string& stream);

  std::string GetBytes() const;

  bool IsEmpty() const;

 private:
  static const char* KIND_NAME;

 private:
  int64_t expire_date_{0};
  int32_t key_id_{0};
  std::string user_id_{""};
  std::string block_pool_id_{""};
  uint64_t block_id_{0};
  std::vector<std::string> access_modes_;
};

}  // namespace bds::dancedn::cloudfs

#endif
