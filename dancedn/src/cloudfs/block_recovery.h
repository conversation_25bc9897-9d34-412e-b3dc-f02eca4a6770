// Copyright (c) 2018-present, ByteDance Inc. All rights reserved.

#pragma once

#include <memory>
#include <string>
#include <vector>

#include "cloudfs/exceptions.h"
#include "cloudfs/thread.h"

namespace bds::dancedn::cloudfs {

class Thread;
class DataNode;
class RecoveringBlock;
class BlockRecord;
class InterDatanodeClient;
class ExtendedBlock;
class DatanodeID;

class BlockRecoveryThread : public Thread {
 public:
  BlockRecoveryThread(DataNode* dn, const std::string& who,
                      const std::vector<RecoveringBlock*>& blocks);

  ~BlockRecoveryThread();

  void Run() override;

 protected:
  explicit BlockRecoveryThread(DataNode* dn);

  exceptions::Exception RecoverBlock(const RecoveringBlock* rb);

  // V2 is used for recover block in byterpc block protocol
  exceptions::Exception RecoverBlockV2(const RecoveringBlock* rb);

  exceptions::Exception CreateInterDatanodeClient(
      const std::string& bpid, const std::shared_ptr<DatanodeID>& target,
      InterDatanodeClient** client);

 private:
  void Cleanup();
  exceptions::Exception SyncBlock(const RecoveringBlock* rb,
                                  std::vector<BlockRecord*> sycn_list);
  exceptions::Exception SyncBlockV2(const RecoveringBlock* rb,
                                    std::vector<BlockRecord*> sycn_list);

 private:
  DataNode* dn_;
  std::string who_;
  std::vector<RecoveringBlock*> blocks_;
};

}  // namespace bds::dancedn::cloudfs
