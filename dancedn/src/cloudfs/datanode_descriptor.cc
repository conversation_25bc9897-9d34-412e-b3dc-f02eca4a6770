// Copyright (c) 2019-present, ByteDance Inc. All rights reserved.

#include "cloudfs/datanode_descriptor.h"

#include "byte/include/byte_log.h"
#include "byte/system/timestamp.h"
#include "cloudfs/block_report_context.h"
#include "cloudfs/datanode_id.h"
#include "cloudfs/datanode_info.h"
#include "cloudfs/datanode_storage.h"
#include "cloudfs/datanode_storage_info.h"
#include "cloudfs/disk_stat_summary.h"
#include "cloudfs/storage_report.h"
#include "cloudfs/volume_failure_summary.h"

namespace bds::dancedn::cloudfs {

DatanodeDescriptor::DatanodeDescriptor(
    std::shared_ptr<DatanodeID> datanode_id) {
  datanode_info_ = new DatanodeInfo(datanode_id);
  UpdateHeartbeatState(std::vector<StorageReport*>{}, 0, 0, nullptr, nullptr);
}

DatanodeDescriptor::DatanodeDescriptor(std::shared_ptr<DatanodeID> datanode_id,
                                       const std::string& network_location) {
  datanode_info_ = new DatanodeInfo(datanode_id, network_location);
  UpdateHeartbeatState(std::vector<StorageReport*>(), 0, 0, nullptr, nullptr);
}

DatanodeDescriptor::~DatanodeDescriptor() {
  delete datanode_info_;

  for (auto& item : failed_storage_infos_) {
    delete item.second;
  }
  for (auto& item : storage_map_) {
    delete item.second;
  }
}

void DatanodeDescriptor::UpdateHeartbeatState(
    const std::vector<StorageReport*>& reports, uint32_t xceiver_count,
    uint32_t volume_failures,
    const VolumeFailureSummary* volume_failure_summary,
    const DiskStatSummary* disk_stat_summary) {
  bool check_failed_storages = false;
  if (volume_failure_summary != nullptr && volume_failure_summary_ != nullptr) {
    uint64_t last = volume_failure_summary_->GetLastVolumeFailureDate();
    uint64_t curr = volume_failure_summary->GetLastVolumeFailureDate();
    check_failed_storages = last > curr;
  } else {
    check_failed_storages = (volume_failures > volume_failures_) ||
                            !heartheated_since_registration_;
  }

  if (check_failed_storages) {
    LOG(INFO) << "Number of failed storage changes from " << volume_failures_
              << " to " << volume_failures;
    for (auto& item : storage_map_) {
      failed_storage_infos_.emplace(item.first, item.second->Clone());
    }
  }

  datanode_info_->SetXceiverCount(xceiver_count);

  if (disk_stat_summary != nullptr && disk_stat_summary->num_devices_ != 0) {
    uint64_t avg_io_util_ms =
        disk_stat_summary->time_spent_io_ms_ / disk_stat_summary->num_devices_;
    datanode_info_->SetAvgIOUtilMs(avg_io_util_ms);
  } else {
    datanode_info_->SetAvgIOUtilMs(0);
  }
  datanode_info_->SetLastUpdate(byte::GetCurrentTimeInMs());
  volume_failures_ = volume_failures;
  volume_failure_summary_.reset(volume_failure_summary);
  uint64_t total_capacity = 0;
  uint64_t total_remaining = 0;
  uint64_t total_block_pool_used = 0;
  uint64_t total_dfs_used = 0;
  uint64_t total_non_dfs_used = 0;
  for (auto report : reports) {
    auto storage = UpdateStorage(report->GetStorage());
    if (check_failed_storages) {
      failed_storage_infos_.erase(storage->GetStorageID());
    }

    storage->ReceivedHeartbeat(report);
    total_capacity += report->GetCapacity();
    total_remaining += report->GetRemaining();
    total_block_pool_used += report->GetBlockPoolUsed();
    total_dfs_used += report->GetDfsUsed();
    total_non_dfs_used += report->GetNonDfsUsed();
  }
  // TODO(cyt): rollBlocksScheduled(getLastUpdate());
  datanode_info_->SetCapacity(total_capacity);
  datanode_info_->SetRemaining(total_remaining);
  datanode_info_->SetBlockPoolUsed(total_block_pool_used);
  datanode_info_->SetDfsUsed(total_dfs_used);
  datanode_info_->SetNonDfsUsed(total_non_dfs_used);
  if (check_failed_storages) {
    UpdateFailedStorage();
  }
  for (auto& item : failed_storage_infos_) {
    delete item.second;
  }
  failed_storage_infos_.clear();
  if (storage_map_.size() != reports.size()) {
    PruneStorageMap(reports);
  }
}

DatanodeStorageInfo* DatanodeDescriptor::UpdateStorage(
    const DatanodeStorage* storage) {
  byte::MutexLocker guard(&map_mutex_);
  DatanodeStorageInfo* storage_info = nullptr;
  auto iter = storage_map_.find(storage->GetStorageID());
  if (iter == storage_map_.end()) {
    LOG(INFO) << "Adding new storage ID " << storage->GetStorageID()
              << " for DN "
              << datanode_info_->GetDatanodeID()->GetXferAddr().ToString();
    storage_info = new DatanodeStorageInfo(this, storage);
    storage_map_.emplace(storage->GetStorageID(), storage_info);
    return storage_info;
  }
  storage_info = iter->second;
  if (storage_info->GetState() != storage->GetState() ||
      !storage_info->GetStorageType().Equals(storage->GetStorageType())) {
    storage_info->UpdateFromStorage(storage);
    storage_map_.emplace(storage->GetStorageID(), storage_info);
  }
  return storage_info;
}

void DatanodeDescriptor::UpdateFailedStorage() {
  for (auto& iter : failed_storage_infos_) {
    auto info = iter.second;
    if (info->GetState() != DatanodeStorage::State::FAILED) {
      LOG(INFO) << info->ToString() << "failed.";
      info->SetState(DatanodeStorage::State::FAILED);
    }
  }
}

void DatanodeDescriptor::PruneStorageMap(
    const std::vector<StorageReport*>& reports) {
  std::map<std::string, DatanodeStorageInfo*> excess_storages;
  byte::MutexLocker guard(&map_mutex_);
  excess_storages.insert(storage_map_.begin(), storage_map_.end());
  for (auto report : reports) {
    excess_storages.erase(report->GetStorage()->GetStorageID());
  }

  // For each remaining storage, remove it if there are no associated blocks.
  for (auto iter : excess_storages) {
    auto info = iter.second;
    if (info->GetNumBlocks() == 0) {
      storage_map_.erase(info->GetStorageID());
      LOG(INFO) << "Removed storage " << info->ToString() << " from DataNode "
                << datanode_info_->ToString();
    }
  }
}

}  // namespace bds::dancedn::cloudfs
