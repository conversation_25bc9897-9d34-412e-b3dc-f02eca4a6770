// Copyright (c) 2023-present, ByteDance Inc. All rights reserved.

#include "cloudfs/util.h"

#include <string>
#include <vector>

#include "gtest/gtest.h"

namespace bds::dancedn::cloudfs {

TEST(StringUtil, SplitStringForNamenode) {
  std::string str;
  std::vector<std::string> results;

  str = "";
  SplitStringForNamenode(str, ":", &results);
  EXPECT_EQ(results.size(), 1);
  EXPECT_EQ(results[0], "");

  str = ":";
  SplitStringForNamenode(str, ":", &results);
  EXPECT_EQ(results.size(), 2);
  EXPECT_EQ(results[0], "");
  EXPECT_EQ(results[1], "");

  str = "::";
  SplitStringForNamenode(str, ":", &results);
  EXPECT_EQ(results.size(), 3);
  EXPECT_EQ(results[0], "");
  EXPECT_EQ(results[1], "");
  EXPECT_EQ(results[2], "");

  str = "a:";
  SplitStringForNamenode(str, ":", &results);
  EXPECT_EQ(results.size(), 2);
  EXPECT_EQ(results[0], "a");
  EXPECT_EQ(results[1], "");

  str = ":b";
  SplitStringForNamenode(str, ":", &results);
  EXPECT_EQ(results.size(), 2);
  EXPECT_EQ(results[0], "");
  EXPECT_EQ(results[1], "b");

  str = "::c";
  SplitStringForNamenode(str, ":", &results);
  EXPECT_EQ(results.size(), 3);
  EXPECT_EQ(results[0], "");
  EXPECT_EQ(results[1], "");
  EXPECT_EQ(results[2], "c");

  str = "a::c";
  SplitStringForNamenode(str, ":", &results);
  EXPECT_EQ(results.size(), 3);
  EXPECT_EQ(results[0], "a");
  EXPECT_EQ(results[1], "");
  EXPECT_EQ(results[2], "c");

  str = "a:b:";
  SplitStringForNamenode(str, ":", &results);
  EXPECT_EQ(results.size(), 3);
  EXPECT_EQ(results[0], "a");
  EXPECT_EQ(results[1], "b");
  EXPECT_EQ(results[2], "");

  str = ":::";
  SplitStringForNamenode(str, ":", &results);
  EXPECT_EQ(results.size(), 3);
  EXPECT_EQ(results[0], "");
  EXPECT_EQ(results[1], ":");
  EXPECT_EQ(results[2], "");

  str = "a:b:c";
  SplitStringForNamenode(str, ":", &results);
  EXPECT_EQ(results.size(), 3);
  EXPECT_EQ(results[0], "a");
  EXPECT_EQ(results[1], "b");
  EXPECT_EQ(results[2], "c");

  str = "a:b:c:d";
  SplitStringForNamenode(str, ":", &results);
  EXPECT_EQ(results.size(), 3);
  EXPECT_EQ(results[0], "a");
  EXPECT_EQ(results[1], "b:c");
  EXPECT_EQ(results[2], "d");

  str = "dancenn1:10.128.54.142:9208";
  SplitStringForNamenode(str, ":", &results);
  EXPECT_EQ(results.size(), 3);
  EXPECT_EQ(results[0], "dancenn1");
  EXPECT_EQ(results[1], "10.128.54.142");
  EXPECT_EQ(results[2], "9208");

  str = "dancenn2:[fdbd:dc02:103:73::162]:9209";
  SplitStringForNamenode(str, ":", &results);
  EXPECT_EQ(results.size(), 3);
  EXPECT_EQ(results[0], "dancenn2");
  EXPECT_EQ(results[1], "[fdbd:dc02:103:73::162]");
  EXPECT_EQ(results[2], "9209");
}

TEST(StringUtil, SplitStringForAddr) {
  std::string str;
  std::vector<std::string> results;

  str = "";
  SplitStringForAddr(str, ":", &results);
  EXPECT_EQ(results.size(), 1);

  str = "a";
  SplitStringForAddr(str, ":", &results);
  EXPECT_EQ(results.size(), 1);
  EXPECT_EQ(results[0], "a");

  str = "a:";
  SplitStringForAddr(str, ":", &results);
  EXPECT_EQ(results.size(), 2);
  EXPECT_EQ(results[0], "a");
  EXPECT_EQ(results[1], "");

  str = ":b";
  SplitStringForAddr(str, ":", &results);
  EXPECT_EQ(results.size(), 2);
  EXPECT_EQ(results[0], "");
  EXPECT_EQ(results[1], "b");

  str = "a:b";
  SplitStringForAddr(str, ":", &results);
  EXPECT_EQ(results.size(), 2);
  EXPECT_EQ(results[0], "a");
  EXPECT_EQ(results[1], "b");

  str = "[fdbd:dc02:103:73::162]:9209";
  SplitStringForAddr(str, ":", &results);
  EXPECT_EQ(results.size(), 2);
  EXPECT_EQ(results[0], "[fdbd:dc02:103:73::162]");
  EXPECT_EQ(results[1], "9209");
}

}  // namespace bds::dancedn::cloudfs
