// Copyright (c) 2019-present, ByteDance Inc. All rights reserved.

#include "cloudfs/volume_failure_summary.h"

#include "cloudfs_proto/DatanodeProtocol.pb.h"

namespace bds::dancedn::cloudfs {

VolumeFailureSummary::VolumeFailureSummary(
    const std::vector<std::string>& failed_storage_locations,
    uint64_t last_volume_failure_date, uint64_t estimated_capacity_lost_total)
    : failed_storage_locations_(failed_storage_locations),
      last_volume_failure_date_(last_volume_failure_date),
      estimated_capacity_lost_total_(estimated_capacity_lost_total) {}

exceptions::Exception VolumeFailureSummary::ToProto(
    ::cloudfs::datanode::VolumeFailureSummaryProto* proto) const {
  for (auto loc : failed_storage_locations_) {
    proto->add_failedstoragelocations(loc);
  }
  proto->set_lastvolumefailuredate(last_volume_failure_date_);
  proto->set_estimatedcapacitylosttotal(estimated_capacity_lost_total_);
  return exceptions::Exception();
}

}  // namespace bds::dancedn::cloudfs
