// Copyright (c) 2019-present, ByteDance Inc. All rights reserved.

#pragma once

#include <cstdint>
#include <memory>
#include <string>
#include <unordered_map>
#include <vector>

#include "aws/core/auth/AWSCredentialsProvider.h"
#include "byte/concurrent/mutex.h"
#include "byte/concurrent/rwlock.h"
#include "cloudfs/block_pool_report.h"
#include "cloudfs/cfs/storage_class.h"
#include "cloudfs/exceptions.h"
#include "cloudfs/receive_deleted_block_info.h"
#include "cloudfs/storage_type.h"
#include "cloudfs/store/ufs/aws_credentials_provider.h"
#include "cloudfs/store/ufs/remote_store.h"

namespace bds::dancedn::cloudfs {

class TOSInfo;
class RemoteBlockInfo;

}  // namespace bds::dancedn::cloudfs

namespace bds::dancedn::cloudfs {

class TaskerGroup;
class BlockPoolActor;
class DatanodeRegistration;
class NameSpaceInfo;
class DataNode;
class RollingUpgradeStatus;
class NNHAStatusHeartbeat;
class DatanodeCommand;
class DatanodeInfo;
class DatanodeID;
class ExtendedBlock;
class BlockReportOptions;
class BlockPoolManager;

class BlockPoolService : public std::enable_shared_from_this<BlockPoolService> {
 public:
  using DNRegSharedPtr = std::shared_ptr<DatanodeRegistration>;

 public:
  // for unit test
  BlockPoolService();
  BlockPoolService(
      const std::unordered_map<std::string, std::string>& nn_name_to_addr,
      DataNode* dn, const std::string& namespace_name,
      BlockPoolManager* bp_manager);
  virtual ~BlockPoolService();

  std::string GetBlockPoolID() const;
  std::string GetBlockPoolIDUnlock() const;
  DataNode* GetDataNode() const {
    return dn_;
  }
  virtual const std::shared_ptr<RemoteBlockInfo>& GetRemoteBlockInfo() const;
  virtual const std::shared_ptr<Aws::Auth::AWSCredentialsProvider>&
  GetAwsCredentialsProvider();
  DNRegSharedPtr GetDatanodeRegistration() const;

  // UnitTest OK
  exceptions::Exception VerifyAndSetNamespaceInfo(NameSpaceInfo** nsInfo);

  exceptions::Exception SetRemoteBlockInfo(
      std::shared_ptr<RemoteBlockInfo> tos_info);

  // UnitTest OK
  exceptions::Exception RegistrationSucceeded(BlockPoolActor* actor,
                                              DatanodeRegistration* reg);
  // UnitTest OK
  void ShutDownActor(BlockPoolActor* actor);
  // UnitTest OK
  void RefreshNNList(
      const std::unordered_map<std::string, std::string>& nn_to_addr_name,
      const std::vector<std::string>& nns);
  // UnitTest OK   but comment the actor->ToString() rwlock is problem
  void UpdateActorStatesFromHeartbeat(BlockPoolActor* actor,
                                      const NNHAStatusHeartbeat& status);
  // UnitTest OK
  void ReportBadBlocks(ExtendedBlock* b, const std::string& storage_uuid,
                       const StorageType& storage_type);
  void ReportBadBlocksUnsafe(ExtendedBlock* b, const std::string& storage_uuid,
                             const StorageType& storage_type);
  // UnitTest OK
  virtual void NotifyNamenodeReceivedBlock(ExtendedBlock* b,
                                           const std::string& del_hint,
                                           const std::string& storage_uuid);
  void NotifyNamenodeMigratedBlock(ExtendedBlock* b,
                                   const std::string& src_storage_uuid,
                                   const std::string& dst_storage_uuid);
  // UnitTest OK
  void NotifyNamenodeDeletedBlock(ExtendedBlock* b,
                                  const std::string& storage_uuid);
  void NotifyNamenodeEvictBlock(ExtendedBlock* b,
                                const std::string& storage_uuid);
  // UnitTest OK
  void NotifyNamenodeReceivingBlock(ExtendedBlock* b,
                                    const std::string& storage_uuid);

  // UnitTest OK
  virtual void NotifyNamenodeNegoedUploadIdBlock(
      ExtendedBlock* b, const std::string& storage_uuid,
      const std::string& upload_id);
  // UnitTest OK
  void NotifyNamenodeUploadedBlock(ExtendedBlock* b,
                                   const std::string& storage_uuid,
                                   const std::string& upload_id,
                                   const std::string& etag);

  void NotifyNamenodeFailed(ExtendedBlock* b, uint8_t status,
                            const std::string& msg);

  void NotifyNamenodeDeletedTosBlock(ExtendedBlock* b);

  void NotifyNamenodeMergedBlock(ExtendedBlock* b);

  void NotifyNamenodeStorageClass(ExtendedBlock* b, StorageClass storage_class,
                                  const std::string& storage_uuid);

  // UnitTest OK
  void TrySendErrorReport(int32_t error_code, const std::string& error_msg);
  void TrySendErrorReportUnsafe(int32_t error_code,
                                const std::string& error_msg);
  // UnitTest OK
  void ReportRemoteBadBlock(DatanodeInfo* dn_info, ExtendedBlock* block);

  exceptions::Exception CommitBlockSynchronization(
      const ExtendedBlock* b, uint64_t new_gs, uint64_t new_length,
      bool close_file, bool delete_block,
      const std::vector<std::shared_ptr<DatanodeID>>& new_targets,
      const std::vector<std::string>& new_target_storages);

  void ScheduleBlockReport(uint64_t delay);
  void ScheduleBlockReportUnsafe(uint64_t delay);

  // UnitTest OK
  DatanodeRegistration* CreateRegistration();

  // UnitTest OK
  bool ShouldRetryInit();
  bool IsCompletelyRecovered() const {
    return completely_recovered_;
  }

  bool Start();
  void Stop();
  void Join();

  exceptions::Exception TriggerBlockReport(
      const BlockReportOptions& options) const;

  // unthreadsafe
  std::string ToString() const;
  std::string LockedToString() const;

  // unthreadsafe
  NameSpaceInfo* GetNameSpaceInfo() const;

  std::shared_ptr<BlockPoolService> GetPtr() {
    return shared_from_this();
  }
  void RecordHeartbeatTime(BlockPoolActor* bp_actor);

  const std::string& GetNamespaceName() const {
    return namespace_name_;
  }

  std::vector<std::string> GetActorNames();

  void ClearIBR(const std::string& name);

  exceptions::Exception RegisterHeartbeat(BlockPoolActor* actor);
  exceptions::Exception UnregisterHeartbeat(BlockPoolActor* actor);

 public:
  // only for unit test
  const std::vector<BlockPoolActor*>& GetActors() {
    return actors_;
  }
  const BlockPoolActor* GetBpServiceToActive() {
    return bp_service_to_active_;
  }
  void SetNameSpaceInfo(NameSpaceInfo* ns_info) {
    ns_info_ = ns_info;
  }
  void ReportActor(const std::string& nn_name,
                   const BlockPoolActorReport& actor_reports);
  void SetBlockPoolID(std::string bpid) {
    bpid_ = bpid;
  }
  uint64_t GetLastActiveClaimTxid() {
    return last_active_claim_txid_;
  }

 private:
  bool IsAlive();
  bool IsCompleteRecovered();
  bool HasBlockPoolID();
  void CheckBlock(ExtendedBlock* block);

  // use this method should overwrite operater ==
  template <typename T>
  exceptions::Exception CheckNsEquality(const T& left, const T& right,
                                        const std::string& message);

 private:
  bool completely_recovered_;

  mutable byte::RwLock rwlock_;

  // members
  std::vector<BlockPoolActor*> actors_;
  // std::unique_ptr<TaskerGroup> g_;
  BlockPoolManager* bp_manager_;
  NameSpaceInfo* ns_info_;
  std::shared_ptr<RemoteBlockInfo> remote_block_info_;
  BlockPoolActor* bp_service_to_active_;
  DNRegSharedPtr r_;
  DataNode* dn_;
  int64_t last_active_claim_txid_;
  int64_t init_at_;
  int64_t register_at_;
  int64_t heart_beat_at_;
  int64_t refresh_at_;
  std::string namespace_name_;
  BlockPoolServiceReport bp_service_report_;
  byte::Mutex bp_service_report_mutex_;
  std::string bpid_;
  std::unordered_map<std::string, std::string> nn_name_to_addr_;
  std::atomic<bool> removing_;
  std::shared_ptr<Aws::Auth::AWSCredentialsProvider> aws_credentials_provider_;
};

}  // namespace bds::dancedn::cloudfs
