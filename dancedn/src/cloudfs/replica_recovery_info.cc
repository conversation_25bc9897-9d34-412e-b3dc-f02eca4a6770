// Copyright (c) 2018, ByteDance Inc. All rights reserved.

#include "cloudfs/replica_recovery_info.h"

#include "cloudfs_proto/hdfs.pb.h"

namespace bds::dancedn::cloudfs {

ReplicaRecoveryInfo::ReplicaRecoveryInfo(uint64_t block_id, uint64_t disk_len,
                                         uint64_t gs, ReplicaState state)
    : Block(block_id, disk_len, gs), original_state_(state) {}

}  // namespace bds::dancedn::cloudfs
