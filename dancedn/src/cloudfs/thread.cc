// Copyright (c) 2019-present, ByteDance Inc. All rights reserved.

#include "cloudfs/thread.h"

#include <sys/prctl.h>

#include <iostream>
#include <string>

#include "byte/base/atomic.h"
#include "byte/include/assert.h"

namespace bds::dancedn::cloudfs {

namespace {
thread_local Thread* current_thread = nullptr;
}

Thread::Thread()
    : is_interrupt_(false),
      is_stop_(true),
      is_detached_(false),
      is_finished_(false),
      background_(0),
      mutex_(),
      cv_(&mutex_) {
  name_[0] = '\0';
  thread_pool_index_ = -1;
}

Thread::~Thread() {}

bool Thread::Start() {
  if (!byte::AtomicCompareExchange<bool>(&is_stop_, true, false)) return false;
  is_interrupt_ = false;
  int flag = pthread_create(&background_, nullptr, Thread::Run, this);
  BYTE_ASSERT(flag == 0) << "flag = " << flag;
  return true;
}

void Thread::Stop() {
  byte::AtomicSet(&is_stop_, true);
  Signal();
}

void Thread::Join() {
  if (background_ != 0 && !is_detached_) {
    pthread_join(background_, nullptr);
    background_ = 0;
  }
}

// void Thread::Detach() {
//     if (background_ != 0) {
//         pthread_detach(background_);
//         is_detached_ = true;
//     }
// }

void Thread::SetName(const std::string& name) {
  int len = 16;
  snprintf(name_, len, "%s", name.c_str());
}

void Thread::SetThreadPoolIndex(int index) {
  thread_pool_index_ = index;
}

int Thread::ThreadPoolIndex() const {
  return thread_pool_index_;
}

void Thread::WaitFor(int timeoutInMs) {
  byte::MutexLocker guard(&mutex_);
  if (!IsStopped()) {
    cv_.WaitUtil(
        [this]() {
          return byte::AtomicGet(&is_interrupt_) || IsStopped();
        },
        timeoutInMs);
    byte::AtomicSet(&is_interrupt_, false);
  }
}

bool Thread::WaitFinished(int timeoutInMs) {
  byte::MutexLocker guard(&mutex_);
  if (!IsFinished()) {
    return cv_.WaitUtil(
        [this]() {
          return IsFinished();
        },
        timeoutInMs);
  }
  return true;
}

bool Thread::WaitUtil(const std::function<bool(void)>& fn, int timeoutInMs) {
  byte::MutexLocker guard(&mutex_);
  if (!IsStopped()) {
    bool ret = cv_.WaitUtil(fn, timeoutInMs);
    byte::AtomicSet(&is_interrupt_, false);
    return ret;
  }
  return true;
}

void Thread::Signal() {
  byte::MutexLocker guard(&mutex_);
  byte::AtomicSet(&is_interrupt_, true);
  cv_.Signal();
}

void* Thread::Run(void* argv) {
  auto t = static_cast<Thread*>(argv);
  if (t->name_[0] != '\0') {
    prctl(PR_SET_NAME, t->name_, nullptr, nullptr);
  }
  current_thread = t;
  t->Run();
  byte::AtomicSet(&t->is_stop_, true);
  byte::AtomicSet(&t->is_finished_, true);
  t->Signal();
  return nullptr;
}

const Thread* Thread::ThisThread() {
  return current_thread;
}

bool Thread::IsInterrupted() const {
  return byte::AtomicGet(&is_interrupt_);
}

void ThreadManager::Run() {
  while (!IsStopped()) {
    WaitUtil(
        [this]() {
          return !worker_queue_.Empty() || IsStopped();
        },
        0);
    while (!worker_queue_.Empty()) {
      auto item = worker_queue_.Front();
      worker_queue_.Pop();

      Thread* worker = item.second;
      switch (item.first) {
        case ThreadManager::Type::ADD:
          worker->Start();
          workers_.emplace(worker);
          break;
        case ThreadManager::Type::DELETE:
          worker->Stop();
          worker->Join();
          workers_.erase(worker);
          delete worker;
          break;
      }
    }
  }
  Cleanup();
}

void ThreadManager::AddWorker(Thread* worker) {
  worker_queue_.Push(std::make_pair(ThreadManager::Type::ADD, worker));
  Signal();
}

void ThreadManager::DeleteWorker(Thread* worker) {
  worker_queue_.Push(std::make_pair(ThreadManager::Type::DELETE, worker));
  Signal();
}

void ThreadManager::Cleanup() {
  for (Thread* worker : workers_) {
    worker->Stop();
    worker->Join();
    worker_queue_.Push(std::make_pair(ThreadManager::Type::DELETE, worker));
  }
  std::set<Thread*> workers_to_delete;
  while (!worker_queue_.Empty()) {
    auto item = worker_queue_.Front();
    worker_queue_.Pop();
    workers_to_delete.insert(item.second);
  }
  std::set<Thread*>::iterator iter = workers_to_delete.begin();
  while (iter != workers_to_delete.end()) {
    delete (*iter);
    ++iter;
  }
}

}  // namespace bds::dancedn::cloudfs
