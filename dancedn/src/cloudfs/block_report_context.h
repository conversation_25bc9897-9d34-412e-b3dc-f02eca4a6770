// Copyright (c) 2019-present, ByteDance Inc. All rights reserved.

#pragma once

#include <sstream>
#include <string>

#include "cloudfs/exceptions.h"

namespace cloudfs {
namespace datanode {

class BlockReportContextProto;

}
}  // namespace cloudfs

namespace bds::dancedn::cloudfs {

class BlockReportContext {
 public:
  BlockReportContext(uint32_t total_rpcs, uint32_t current_rpc,
                     int64_t report_id)
      : total_rpcs_(total_rpcs),
        current_rpc_(current_rpc),
        report_id_(report_id) {}
  ~BlockReportContext() {}

  uint32_t GetTotalRPCs() const {
    return total_rpcs_;
  }
  uint32_t GetCurrentRPC() const {
    return current_rpc_;
  }
  int64_t GetReportID() const {
    return report_id_;
  }

  exceptions::Exception ToProto(
      ::cloudfs::datanode::BlockReportContextProto* proto) const;
  std::string ToString() const {
    std::stringstream ss;
    ss.imbue(std::locale::classic());
    ss << "BlockReportContext id=" << report_id_
       << " total_rpcs=" << total_rpcs_ << " current_rpc=" << current_rpc_;
    return ss.str();
  }

 private:
  uint32_t total_rpcs_;
  uint32_t current_rpc_;
  int64_t report_id_;
};

}  // namespace bds::dancedn::cloudfs
