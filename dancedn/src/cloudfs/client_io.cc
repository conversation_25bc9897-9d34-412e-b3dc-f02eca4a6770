// Copyright (c) 2019-present, ByteDance Inc. All rights reserved.

#include "cloudfs/client_io.h"

#include <memory>
#include <string>
#include <unordered_map>
#include <utility>

#include "brpc/channel.h"
#include "byte/string/format/print.h"
#include "byte/util/scope_guard.h"
#include "cloudfs/auth_protocol.h"
#include "cloudfs/brpc/hrpc_controller.h"
#include "cloudfs/constants.h"
#include "cloudfs/io/address.h"
#include "cloudfs/io/connection.h"
#include "cloudfs/io/context.h"
#include "cloudfs/io/define.h"
#include "cloudfs/io/handle_context.h"
#include "cloudfs/io/io.h"
#include "cloudfs/io/io_buf.h"
#include "cloudfs/io/net.h"
#include "cloudfs/io/wrapper.h"
#include "cloudfs/message/rpc_request_message.h"
#include "cloudfs/message/rpc_response_message.h"
#include "cloudfs/metrics.h"
#include "cloudfs/util.h"
#include "cloudfs_proto/IpcConnectionContext.pb.h"
#include "cloudfs_proto/ProtobufRpcEngine.pb.h"
#include "cloudfs_proto/RpcHeader.pb.h"
#include "cloudfs_proto/hdfs.pb.h"
#include "gflags/gflags.h"
#include "google/protobuf/arena.h"
#include "google/protobuf/service.h"
#include "include/macros.h"
#include "string/algorithm.h"

DECLARE_uint32(bytestore_hdfs_namenode_brpc_conn_timeout);

namespace bds::dancedn::cloudfs {

ClientIO::ClientIO(const std::map<std::string, std::string>& tags)
    : tags_(tags) {
  std::string tags_str;
  for (const auto& tag_pair : tags) {
    tags_str +=
        byte::StringPrint("(%s -> %s)", tag_pair.first, tag_pair.second);
  }
  LOG(INFO) << "Create client io with tags " << tags_str;
  client_id_ = GenerateClientID();
  call_id_ = 0;
}

ClientIO::~ClientIO() {}

exceptions::Exception ClientIO::SendViaBrpc(
    io::Method method, io::IPAddress* address, const char* const protocol,
    const ::google::protobuf::Message* request,
    ::google::protobuf::Message* response, uint32_t timeout) {
  brpc::Channel channel;
  // TODO(wangning) now single connection is not implemented, server doesn't
  // support so far.
  //                in future we could have a connection pool, instead of
  //                construct one per call.
  brpc::ChannelOptions options;

  // TODO(wangning) move those to const.
  options.protocol = "hrpc";
  options.connection_type = "short";
  options.timeout_ms = timeout;
  options.connect_timeout_ms = FLAGS_bytestore_hdfs_namenode_brpc_conn_timeout;
  options.max_retry = -1;
  if (channel.Init(address->ToString().c_str(), &options) != 0) {
    return exceptions::Exception(exceptions::kSocketTimeoutException,
                                 "Failed to connect to " + address->ToString());
  }
  HrpcController cntl(NextCallId(), client_id_, method, tags_, protocol);
  channel.CallMethod(nullptr, static_cast<brpc::Controller*>(&cntl), request,
                     response, nullptr);

  if (cntl.Failed()) {
    LOG(ERROR) << "send " << cntl.method_name()
               << " to namenode failed exception : " << cntl.ErrorText()
               << " address: " << address->ToString();
    return exceptions::Exception(exceptions::kSocketTimeoutException,
                                 "RPC Failed " + address->ToString());
  }

  return exceptions::Exception();
}

exceptions::Exception ClientIO::SendRequest(
    io::Method method, io::IPAddress* address,
    const ::google::protobuf::Message* request_head,
    const ::google::protobuf::Message* request,
    message::RpcResponseMessage** message, uint32_t timeout) {
  ::cloudfs::RpcRequestHeaderProto rpc_head = GenerateRpcHeader(NextCallId());
  return SendRequest(method, address, &rpc_head, request_head, request, message,
                     timeout);
}

exceptions::Exception ClientIO::SendRequest(
    io::Method method, io::IPAddress* address,
    const ::google::protobuf::Message* rpc_head,
    const ::google::protobuf::Message* request_head,
    const ::google::protobuf::Message* request,
    message::RpcResponseMessage** message, uint32_t timeout) {
  uint64_t start_time = byte::GetCurrentTimeInUs();
  bytestore::Tags metric_tags = {{"method", MethodToString(method)}};
  for (auto&& kv : tags_) {
    metric_tags.emplace_back(
        std::pair<std::string, std::string>(kv.first, kv.second));
  }
  METRICS_client_io_send_request_num->GetMetric(metric_tags)->Increment();
  METRICS_client_io_send_request_ongoing_num->GetMetric(metric_tags)
      ->Increment();
  BYTE_DEFER(METRICS_client_io_send_request_ongoing_num->GetMetric(metric_tags)
                 ->Decrement());
  io::IOBuf* buf = SerializeRequest(rpc_head, request_head, request);

  auto ctx = new io::Context(reinterpret_cast<void*>(&method), nullptr);
  byte::ScopeGuard guard([&ctx, &buf] {
    delete ctx;
    delete buf;
  });

  int r = WriteMessage(address, buf, timeout, ctx,
                       reinterpret_cast<void**>(message));

  if (r == IO_OK) {
    uint64_t end_time = byte::GetCurrentTimeInUs();
    METRICS_client_io_send_request_latency->GetMetric(metric_tags)
        ->Set(end_time - start_time);
    return exceptions::Exception();
  }
  METRICS_client_io_send_request_error_num->GetMetric(metric_tags)->Increment();
  if (r == IO_TIMEOUT) {  // request is timeout
    return exceptions::Exception(
        exceptions::kSocketTimeoutException,
        "Call to " + address->ToString() + " failed on socket timeout");
  }
  if (r == IO_CLOSE) {  // connection is closed
    return exceptions::Exception(
        exceptions::kSocketException,
        "Call to " + address->ToString() +
            " faiiled on connection exception: ConnectionRefused");
  }
  if (r == IO_STOP) {  // this process will stop
    return exceptions::Exception(exceptions::kFalseException,
                                 "IOEvent has been stopped");
  }

  std::string msg;
  if (r == IO_ERR) {  // invalid request, for example: duplicating call_id
    msg = "write message io err";
  } else {
    msg = byte::StringPrint("unknown exception code: %d", r);
  }
  return exceptions::Exception(exceptions::kRemoteException, msg);
}

std::string ClientIO::SerializeRPCHeader(int callid) {
  ::cloudfs::RpcRequestHeaderProto proto = GenerateRpcHeader(callid);
  // TODO(livexmm) support ByteTrace
  return proto.SerializeAsString();
}

std::string ClientIO::SerializeConnectionContext() {
  ::cloudfs::IpcConnectionContextProto proto;
  // no protocol, no connectionToken, no ugi
  return proto.SerializeAsString();
}

void ClientIO::WriteHandShake(io::IOBuf* buf) {
  io::IOChunk* chunk = new io::IOChunk(4 + 1 + 1 + 1);
  chunk->WriteBytes((const uint8_t*)"hrpc", 4);
  chunk->WriteFixed8BE(RPC_CURRENT_VERSION);
  chunk->WriteFixed8BE(RPC_SERVICE_CLASS_DEFAULT);
  // here we only support simple protocol,
  // sasl maybe will implement at future
  chunk->WriteFixed8BE(AuthProtocol::NONE);

  buf->Append(chunk);
}

void ClientIO::WriteIpcConnection(io::IOBuf* buf) {
  auto rpc_header = SerializeRPCHeader(CONNECTION_CONTEXT_CALL_ID);
  auto conn_context = SerializeConnectionContext();

  int body_len =
      io::IOChunk::CalcVarintSize(rpc_header.size()) + rpc_header.size() +
      io::IOChunk::CalcVarintSize(conn_context.size()) + conn_context.size();

  io::IOChunk* chunk = new io::IOChunk(4 + body_len);
  chunk->WriteFixed32BE(body_len);
  chunk->WriteVarint(rpc_header.size());
  chunk->WriteBytes((const uint8_t*)rpc_header.data(), rpc_header.size());
  chunk->WriteVarint(conn_context.size());
  chunk->WriteBytes((const uint8_t*)conn_context.data(), conn_context.size());
  buf->Append(chunk);
}

io::IOBuf* ClientIO::SerializeRequest(const ::google::protobuf::Message* rpch,
                                      const ::google::protobuf::Message* rh,
                                      const ::google::protobuf::Message* r) {
  std::string rpc_header = rpch->SerializeAsString();
  std::string request_header = rh->SerializeAsString();
  std::string request = r->SerializeAsString();

  int body_len = io::IOChunk::CalcVarintSize(rpc_header.size()) +
                 rpc_header.size() +
                 io::IOChunk::CalcVarintSize(request_header.size()) +
                 request_header.size() +
                 io::IOChunk::CalcVarintSize(request.size()) + request.size();

  io::IOChunk* chunk = new io::IOChunk(4 + body_len);

  chunk->WriteFixed32BE(body_len);
  chunk->WriteVarint(rpc_header.size());
  chunk->WriteBytes((const uint8_t*)rpc_header.data(), rpc_header.size());
  chunk->WriteVarint(request_header.size());
  chunk->WriteBytes((const uint8_t*)request_header.data(),
                    request_header.size());
  chunk->WriteVarint(request.size());
  chunk->WriteBytes((const uint8_t*)request.data(), request.size());

  io::IOBuf* buf = new io::IOBuf();
  buf->Append(chunk);
  return buf;
}

int ClientIO::DecodeRpcMessageSync(io::Connection* conn, io::Context* ctx,
                                   int total_len, io::Wrapper** w) {
  io::IOChunk* chunk = conn->ReadBuf()->Front();
  auto msg = new message::RpcResponseMessage();
  if (!msg->DecodeSync(ctx, chunk, total_len)) {
    delete msg;
    *w = nullptr;
    return IO_ERR;
  }

  if (msg->RpcHeader()->status() ==
      ::cloudfs::RpcResponseHeaderProto_RpcStatusProto_FATAL) {
    conn->MaskDelayClose();
  }

  *w = new io::Wrapper(msg, [](void* data) {
    delete static_cast<message::RpcResponseMessage*>(data);
  });

  return IO_OK;
}

int32_t ClientIO::NextCallId() {
  // 0 <= call <= INT32_MAX
  uint32_t call_id = __sync_fetch_and_add(&call_id_, 1);
  return call_id & 0x7fffffff;
}

int ClientIO::GetConnection(io::IPAddress* address, uint32_t timeout,
                            io::Connection** target_conn) {
  int fd = io::Net::CreateTcpSocket(address->IsIpv6());
  if (fd == -1) {
    LOG(ERROR) << "create tcp client socket failed";
    return IO_ERR;
  }
  io::Net::SetSynCnt(fd, io::Net::CalcSyncCnt(timeout));
  io::Net::SetRecvTimeout(fd, timeout);
  io::Net::SetSendTimeout(fd, timeout);

  io::NetFlags flags;
  flags.non_block = false;
  flags.no_delay = true;
  flags.Apply(fd);

  io::Connection* conn =
      io::NewClientConnection(address->Index(), fd, *address);

  int ret = conn->Connect();
  if (ret != IO_OK && ret != IO_AGAIN) {
    delete conn;
    return IO_ERR;
  }
  conn->OneReadChunk(true);
  *target_conn = conn;
  return IO_OK;
}

int ClientIO::WriteMessage(io::IPAddress* address, io::IOBuf* buf,
                           uint32_t timeout, io::Context* ctx, void** res) {
  io::Connection* conn = nullptr;
  io::Wrapper* w = nullptr;
  io::IOChunk* chunk = nullptr;
  uint32_t total_len;
  auto flag = GetConnection(address, timeout, &conn);
  if (flag != IO_OK) {
    return flag;
  }
  std::unique_ptr<io::Connection> conn_deleter(conn);

  // send connection header
  WriteHandShake(conn->WriteBuf());
  flag = conn->Write(true);
  if (flag != IO_OK) {
    return flag;
  }

  // TODO(cyt): setup sasl connection

  // send ipc connection context
  WriteIpcConnection(conn->WriteBuf());
  flag = conn->Write(true);
  if (flag != IO_OK) {
    return flag;
  }

  // send rpc request
  conn->WriteBuf()->Append(buf);
  flag = conn->Write(true);
  if (flag != IO_OK) {
    return flag;
  }

  // | 4B        | varint32  | proto             | varint32       | proto
  // | total len | proto len | RpcResponseHeader | user proto len | user proto
  flag = conn->ReadBytes(4);
  chunk = conn->ReadBuf()->Front();
  if (flag != IO_OK) {
    return flag;
  }

  total_len = chunk->ReadFixed32BE();
  flag = conn->ReadBytes(total_len);
  if (flag != IO_OK) {
    return flag;
  }

  flag = DecodeRpcMessageSync(conn, ctx, total_len, &w);
  if (flag != IO_OK) {
    return flag;
  }
  *res = w->Release();
  delete w;
  return flag;
}

::cloudfs::RpcRequestHeaderProto ClientIO::GenerateRpcHeader(int callid) {
  ::cloudfs::RpcRequestHeaderProto proto;
  proto.set_rpckind(::cloudfs::RpcKindProto::RPC_PROTOCOL_BUFFER);
  proto.set_rpcop(::cloudfs::RpcRequestHeaderProto_OperationProto ::
                      RpcRequestHeaderProto_OperationProto_RPC_FINAL_PACKET);
  proto.set_callid(callid);
  proto.set_retrycount(-1);
  proto.set_clientid(client_id_);
  proto.set_clientaddress("");
  for (auto iter = tags_.begin(); iter != tags_.end(); iter++) {
    ::cloudfs::TraceBaggageProto* trace_baggage_proto = proto.add_baggages();
    trace_baggage_proto->set_name(iter->first);
    trace_baggage_proto->set_value(iter->second);
  }
  return proto;
}

}  // namespace bds::dancedn::cloudfs
