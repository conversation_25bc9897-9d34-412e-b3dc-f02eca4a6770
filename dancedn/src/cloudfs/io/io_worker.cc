// Copyright (c) 2018-present, ByteDance Inc. All rights reserved.

#include "cloudfs/io/io_worker.h"

#include <arpa/inet.h>
#include <sys/prctl.h>
#include <unistd.h>

#include "byte/include/byte_log.h"
#include "cloudfs/io/address.h"
#include "cloudfs/io/connection.h"
#include "cloudfs/io/connection_manager.h"
#include "cloudfs/io/define.h"
#include "cloudfs/io/handle_context.h"
#include "cloudfs/io/io.h"
#include "cloudfs/io/io_buf.h"
#include "cloudfs/io/listener.h"
#include "cloudfs/io/net.h"
#include "cloudfs/io/wrapper.h"
#include "cloudfs/util.h"

#ifndef EPOLL_MAX_BASE
#define EPOLL_MAX_BASE 128
#endif

#ifndef EPOLL_MAX_MAX
#define EPOLL_MAX_MAX 65536
#endif

namespace bds::dancedn::cloudfs {
namespace io {

class EventIO;

IOWorker::IOWorker() : io_(nullptr), index_(-1), event_fd_(-1) {
  conn_manager_ = std::make_unique<ConnectionManager>();
}

IOWorker::IOWorker(EventIO* event_io, int index)
    : io_(event_io), index_(index), event_fd_(-1) {
  conn_manager_ = std::make_unique<ConnectionManager>();
}

IOWorker::~IOWorker() {
  if (event_fd_ > 0) {
    close(event_fd_);
  }
}

bool IOWorker::Init() {
  int event_fd = epoll_create1(EPOLL_CLOEXEC);
  if (event_fd < 0) {
    LOG(ERROR) << "epoll create event fail " << strerror(errno) << ":" << errno;
    return false;
  }
  event_fd_ = event_fd;
  return true;
}

int IOWorker::Listen(const IPAddress& address) {
  if (listener_ != nullptr) return IO_ERR;

  NetFlags flags;
  flags.non_block = true;
  flags.reuse_addr = true;
  flags.reuse_port = true;
  flags.no_delay = true;

  Listener* listener = Listener::Create(address, &flags);
  if (listener == nullptr) return IO_ERR;
  if (AddEvent(listener->Fd(), EPOLLIN, listener, AcceptIOHandle) != IO_OK) {
    delete listener;
    return IO_ERR;
  }
  listener_.reset(listener);
  return IO_OK;
}

void IOWorker::Run() {
  InstallName();

  int timeout = 100;
  int epoll_max = EPOLL_MAX_BASE;
  auto events = new struct epoll_event[epoll_max];
  // LOG(DEBUG) << "begin wait for events";
  while (!IsStopped()) {
    int count = WaitForEvents(events, epoll_max, timeout);
    if (UNLIKELY(count < 0)) goto ERR;
    // LOG(DEBUG) << "events cout:" << count;

    for (int i = 0; i < count; i++) {
      int fd = events[i].data.fd;
      EventContext* ctx = events_.GetOrNew(fd);
      if (ctx->mask_ & events[i].events & EPOLLIN) {
        // LOG(DEBUG) << "enter epoll in";
        if (ctx->read_fn_(this, fd, ctx->data_) < 0) {
          // return -1, mean ocurr error should close connection
          // so don't do epoll out any more
          continue;
        }
      }
      if (ctx->mask_ & events[i].events & EPOLLOUT) {
        // LOG(DEBUG) << "enter epoll out";
        ctx->write_fn_(this, fd, ctx->data_);
      }
    }

    if (UNLIKELY(count == epoll_max) && epoll_max < EPOLL_MAX_MAX) {
      epoll_max = epoll_max << 1;
      epoll_max = epoll_max > EPOLL_MAX_MAX ? EPOLL_MAX_MAX : epoll_max;
      delete[] events;
      events = new struct epoll_event[epoll_max];
    }
  }
ERR:
  delete[] events;
}

int IOWorker::AddEvent(int fd, uint32_t events, void* data,
                       IOHandler callback) {
  EventContext* ctx = events_.GetOrNew(fd);

  if ((events & ctx->mask_) != events) {
    uint32_t mask = ctx->mask_ | events;
    struct epoll_event ev;
    memset(&ev, 0, sizeof(struct epoll_event));
    ev.events = mask;
    ev.data.u64 = 0;
    ev.data.fd = fd;

    if (ctx->mask_ == 0) {
      if (epoll_ctl(event_fd_, EPOLL_CTL_ADD, fd, &ev) < 0) {
        LOG(ERROR) << "epoll add " << fd << " fail " << strerror(errno) << ":"
                   << errno;
        return IO_ERR;
      }
    } else {
      if (epoll_ctl(event_fd_, EPOLL_CTL_MOD, fd, &ev) < 0) {
        LOG(ERROR) << "epoll mod " << fd << " fail " << strerror(errno) << ":"
                   << errno;
        return IO_ERR;
      }
    }
    ctx->mask_ = mask;
  }

  if (events & EPOLLIN) {
    ctx->read_fn_ = callback;
    ctx->data_ = data;
  }
  if (events & EPOLLOUT) {
    ctx->write_fn_ = callback;
    ctx->data_ = data;
  }

  return IO_OK;
}

int IOWorker::RemoveEvent(int fd, uint32_t events) {
  EventContext* ctx = events_.Get(fd);
  if (ctx == nullptr || ctx->mask_ == 0) return IO_OK;

  uint32_t mask = ctx->mask_ & (~events);
  if (events == 0 || mask == 0) {
    if (epoll_ctl(event_fd_, EPOLL_CTL_DEL, fd, nullptr) < 0) {
      LOG(ERROR) << "epoll remove " << fd << " fail " << strerror(errno) << ":"
                 << errno;
      return IO_ERR;
    }
    ctx->mask_ = 0;
    ctx->data_ = nullptr;
    ctx->read_fn_ = nullptr;
    ctx->write_fn_ = nullptr;
    return IO_OK;
  }

  struct epoll_event ev;
  ev.events = mask;
  ev.data.ptr = nullptr;
  ev.data.fd = fd;
  if (epoll_ctl(event_fd_, EPOLL_CTL_MOD, fd, &ev) < 0) {
    LOG(ERROR) << "epoll mod " << fd << " fail " << strerror(errno) << ":"
               << errno;
    return IO_ERR;
  }
  ctx->mask_ = mask;

  if (!(ctx->mask_ & EPOLLIN)) {
    ctx->read_fn_ = nullptr;
  }
  if (!(ctx->mask_ & EPOLLOUT)) {
    ctx->write_fn_ = nullptr;
  }

  return IO_OK;
}

int IOWorker::WaitForEvents(struct epoll_event* events, int max_events,
                            int timeout) {
  int nfds = epoll_wait(event_fd_, events, max_events, timeout);
  if (nfds < 0) {
    if (errno == EINTR) return 0;
    LOG(ERROR) << "epoll wait fail " << strerror(errno) << ":" << errno;
    return IO_ERR;
  }

  for (int i = 0; i < nfds; i++) {
    if ((events[i].events & EPOLLERR) || (events[i].events & EPOLLHUP)) {
      events[i].events = events[i].events | EPOLLIN | EPOLLOUT;
    }
  }

  return nfds;
}

void IOWorker::InstallName() {
  char b[16];
  snprintf(b, sizeof(b), "iow-%d", index_);
  prctl(PR_SET_NAME, b, nullptr, nullptr, nullptr);
}

int IOWorker::AcceptIOHandle(int fd, void* data) {
  Connection* conn = nullptr;
  int ret = listener_->Accept(&conn, index_);
  if (ret != IO_OK) return ret;
  // LOG(DEBUG) << "accept success";
  NetFlags flags;
  flags.non_block = true;
  flags.no_delay = true;
  flags.Apply(conn->Fd());

  io_->ConnectionInitializer()(conn);
  if (conn->Handle()->ConnectedHandle(this, conn) == IO_OK &&
      AddEvent(conn->Fd(), EPOLLIN, conn, ReadIOHandle) >= 0) {
    conn_manager_->Add(conn);
    return IO_OK;
  }
  CloseConnection(conn, IO_ERR);
  return IO_ERR;
}

int IOWorker::AcceptIOHandle(IOWorker* worker, int fd, void* data) {
  return worker->AcceptIOHandle(fd, data);
}

int IOWorker::ReadIOHandle(int fd, void* data) {
  Connection* conn = reinterpret_cast<Connection*>(data);
  LOG(DEBUG) << "Into ReadIOHandle: " << this
             << " IsClient: " << conn->IsClient();

  int flag = conn->Read(io_->MaxChunkEachRead());
  if (flag == IO_ERR || flag == IO_CLOSE) {
    goto READ_CLOSE_CONNECTION;
  }

  while (true) {
    LOG(DEBUG) << "ReadIOHandle: " << this << " IsClient: " << conn->IsClient();
    Wrapper* w = nullptr;
    flag = conn->Decode(&w);
    LOG(DEBUG) << "flag: " << flag;
    if (flag == IO_ERR) goto READ_CLOSE_CONNECTION;
    if (flag == IO_AGAIN) break;

    if (w->Data() == nullptr) {
      delete w;
      continue;
    }

    if (conn->IsClient()) {
      flag = ReadClientIOHandle(conn, w);
    } else {
      flag = ReadServerIOHandle(conn, w);
    }
    // LOG(DEBUG) << "read io handle flag:" << flag;

    delete w;
    if (flag == IO_CLOSE || flag == IO_ERR || flag == IO_TIMEOUT) {
      LOG(DEBUG) << "Location 1";
      goto READ_CLOSE_CONNECTION;
    }
  }

  return IO_OK;

READ_CLOSE_CONNECTION:
  CloseConnectionAndUnRegEvent(conn, flag);
  return IO_CLOSE;
}

int IOWorker::ReadIOHandle(IOWorker* worker, int fd, void* data) {
  return worker->ReadIOHandle(fd, data);
}

int IOWorker::WriteIOHandle(int fd, void* data) {
  Connection* conn = reinterpret_cast<Connection*>(data);

  if (conn->Write(false) < 0) {
    goto WRITE_CLOSE_CONNECTION;
  }

  if (conn->WriteBuf()->Empty()) {
    RemoveEvent(conn->Fd(), EPOLLOUT);
    return IO_OK;
  }

  return IO_AGAIN;

WRITE_CLOSE_CONNECTION:
  CloseConnectionAndUnRegEvent(conn, IO_CLOSE);
  return IO_CLOSE;
}

int IOWorker::WriteIOHandle(IOWorker* worker, int fd, void* data) {
  return worker->WriteIOHandle(fd, data);
}

void IOWorker::DirectDisconnect(uint64_t conn_id, int status) {
  if (conn_id == Connection::INVALID_ID) return;
  Connection* conn = conn_manager_->Get(conn_id);
  BYTE_ASSERT_DEBUG(conn != nullptr);
  CloseConnectionAndUnRegEvent(conn, status);
}

int IOWorker::TryWriteImmediately(Connection* conn) {
  int flag = conn->Write(false);
  if (flag == IO_AGAIN) {
    AddEvent(conn->Fd(), EPOLLOUT, conn, WriteIOHandle);
  }
  // LOG(DEBUG) << "try write immediately flag:" << flag;
  return flag;
}

int IOWorker::ReadClientIOHandle(Connection* conn, Wrapper* w) {
  LOG(DEBUG) << "Read Client IO Handle";
  if (conn->IsClosed()) return IO_CLOSE;
  return TryWriteImmediately(conn);
}

int IOWorker::ReadServerIOHandle(Connection* conn, Wrapper* w) {
  LOG(DEBUG) << "Read Server IO Handle";
  if (conn->ReadHandle(this, w->Release()) < 0) {
    LOG(DEBUG) << "read handle failed";
    return IO_CLOSE;
  }

  if (conn->IsClosed()) {
    LOG(DEBUG) << "read server io closed";
    return IO_CLOSE;
  }
  return TryWriteImmediately(conn);
}

void IOWorker::CloseConnection(Connection* conn, int status) {
  delete conn;
}

void IOWorker::CloseConnectionAndUnRegEvent(Connection* conn, int status) {
  RemoveEvent(conn->Fd(), 0);
  conn_manager_->Remove(conn->ID());
  CloseConnection(conn, status);
}

Connection* IOWorker::DirectGetConnection(uint64_t conn_id) {
  return conn_manager_->Get(conn_id);
}

}  // namespace io
}  // namespace bds::dancedn::cloudfs
