// Copyright (c) 2018-present, ByteDance Inc. All rights reserved.

#include "cloudfs/io/concurrent_queue.h"

#include "gtest/gtest.h"

namespace bds::dancedn::cloudfs {
namespace io {

struct TaskItem {
  int id;
  int priority;
};

struct CompareTask {
  bool operator()(const std::pair<int, TaskItem>& t1,
                  const std::pair<int, TaskItem>& t2) {
    if (t1.second.priority == t2.second.priority) {
      return t1.second.id > t2.second.id;
    }
    return t1.second.priority > t2.second.priority;
  }
};

TEST(ConcurrentPriorityQueueTest, Basic) {
  ConcurrentPriorityQueue<std::pair<int, TaskItem>, CompareTask> test_queue;
  EXPECT_TRUE(test_queue.Empty());
  EXPECT_EQ(test_queue.Size(), 0);
  TaskItem items[] = {{8, 2}, {7, 2}, {9, 2}, {1, 1}, {2, 1},
                      {3, 1}, {6, 3}, {5, 3}, {4, 3}};
  const size_t count = sizeof(items) / sizeof(TaskItem);
  for (size_t i = 0; i < count; i++) {
    test_queue.Push(std::make_pair(1, items[i]));
  }
  EXPECT_FALSE(test_queue.Empty());
  EXPECT_EQ(test_queue.Size(), count);
  std::stringstream s_stream;
  for (size_t i = 0; i < count; i++) {
    s_stream << test_queue.Front().second.id << ",";
    test_queue.PopFront();
  }
  EXPECT_EQ(s_stream.str(), "1,2,3,7,8,9,4,5,6,");
  EXPECT_TRUE(test_queue.Empty());
}

}  // namespace io
}  // namespace bds::dancedn::cloudfs
