// copyright (c) 2021-present, bytedance inc. all rights reserved.

#include "cloudfs/io/define.h"

#define IO_UPGRADE 2
#define IO_AGAIN 1
#define IO_OK 0
#define IO_ERR -1
#define IO_CLOSE -2
#define IO_STOP -3
#define IO_TIMEOUT -4

const char* strioflag(int flag) {
  switch (flag) {
    case IO_UPGRADE: return "IO_UPGRADE";
    case IO_AGAIN: return "IO_AGAIN";
    case IO_OK: return "IO_OK";
    case IO_ERR: return "IO_ERR";
    case IO_CLOSE: return "IO_CLOSE";
    case IO_STOP: return "IO_STOP";
    case IO_TIMEOUT: return "IO_TIMEOUT";
    default: return "IO_UNKNOWN";
  }
}
