// Copyright (c) 2019-present, ByteDance Inc. All rights reserved.

#include "cloudfs/heartbeat_response.h"

#include "cloudfs/store/ufs/remote_store.h"

namespace bds::dancedn::cloudfs {

HeartbeatResponse::HeartbeatResponse(
    const std::vector<DatanodeCommand*>& cmds, NNHAStatusHeartbeat ha_status,
    RollingUpgradeStatus rolling_update_status,
    std::shared_ptr<RemoteBlockInfo> remote_block_info, bool allow_evict)
    : commands_(cmds),
      ha_status_(ha_status),
      rolling_update_status_(rolling_update_status),
      remote_block_info_(remote_block_info),
      allow_evict_(allow_evict) {}

HeartbeatResponse::~HeartbeatResponse() {
  for (auto cmd : commands_) {
    delete cmd;
  }
}

}  // namespace bds::dancedn::cloudfs
