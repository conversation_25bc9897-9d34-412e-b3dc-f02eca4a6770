// Copyright (c) 2025-present, ByteDance Inc. All rights reserved.

#pragma once

#include <string>
#include <vector>

#include "cloudfs/block_pool_manager.h"
#include "cloudfs/block_pool_service.h"
#include "gmock/gmock.h"
#include "proto/chunkserver.pb.h"

namespace bds::dancedn::cloudfs {
class MockBlockPoolService : BlockPoolService {
 public:
  MockBlockPoolService() {}
  ~MockBlockPoolService() {}
  MOCK_METHOD3(NotifyNamenodeReceivedBlock,
               void(ExtendedBlock* b, const std::string& del_hint,
                    const std::string& storage_uuid));
};

class MockBlockPoolManager : public BlockPoolManager {
 public:
  MockBlockPoolManager() {}
  explicit MockBlockPoolManager(DataNode* dn) : BlockPoolManager(dn) {}
  ~MockBlockPoolManager() {}
  MOCK_CONST_METHOD0(GetAllNamenodeThreads,
                     std::vector<BlockPoolManager::BPServiceSharedPtr>());
  MOCK_CONST_METHOD1(GetBlockPoolID, std::string(const std::string& nsid));
  MOCK_METHOD2(GetBpid,
               exceptions::Exception(uint64_t nsid, std::string* result));
  MOCK_METHOD1(Get, BPServiceSharedPtr(const std::string& bpid));
};

}  // namespace bds::dancedn::cloudfs
