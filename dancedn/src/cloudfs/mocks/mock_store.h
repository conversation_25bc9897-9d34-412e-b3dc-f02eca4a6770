// Copyright (c) 2018-present, ByteDance Inc. All rights reserved.

#pragma once

#include <cstdint>
#include <deque>
#include <map>
#include <memory>
#include <set>
#include <string>
#include <unordered_map>
#include <vector>

#include "aws/core/auth/AWSCredentialsProvider.h"
#include "chunkserver/disk.h"
#include "chunkserver/tiering/migration_placement.h"
#include "cloudfs/block.h"
#include "cloudfs/block_list_as_longs.h"
#include "cloudfs/block_local_path_info.h"
#include "cloudfs/block_pool_actor.h"
#include "cloudfs/block_pool_manager.h"
#include "cloudfs/block_pool_service.h"
#include "cloudfs/block_recovery.h"
#include "cloudfs/block_report_options.h"
#include "cloudfs/caching_strategy.h"
#include "cloudfs/cfs/qos_acquire.h"
#include "cloudfs/cfs/storage_class.h"
#include "cloudfs/constants.h"
#include "cloudfs/data_checksum.h"
#include "cloudfs/data_transfer.h"
#include "cloudfs/data_xceiver.h"
#include "cloudfs/datanode.h"
#include "cloudfs/datanode_config.h"
#include "cloudfs/datanode_id.h"
#include "cloudfs/datanode_storage.h"
#include "cloudfs/datanode_stream_server.h"
#include "cloudfs/directory_scanner.h"
#include "cloudfs/exceptions.h"
#include "cloudfs/extended_block.h"
#include "cloudfs/finalized_replica.h"
#include "cloudfs/hdfs_blocks_metadata.h"
#include "cloudfs/io/connection.h"
#include "cloudfs/io/define.h"
#include "cloudfs/io/io_buf.h"
#include "cloudfs/lifecycle/block_entry_mgr.h"
#include "cloudfs/namenode_client.h"
#include "cloudfs/namenode_rpc.h"
#include "cloudfs/namespace_info.h"
#include "cloudfs/replica_being_written.h"
#include "cloudfs/replica_info.h"
#include "cloudfs/replica_state.h"
#include "cloudfs/storage_info.h"
#include "cloudfs/storage_report.h"
#include "cloudfs/storage_type.h"
#include "cloudfs/store.h"
#include "cloudfs/store/chunkserver/chunkserver_store.h"
#include "cloudfs/store/chunkserver/partial_block.h"
#include "cloudfs/store/chunkserver/partial_block_store.h"
#include "cloudfs/store/ufs/hdfs_store.h"
#include "cloudfs/store/ufs/remote_store.h"
#include "cloudfs/store/ufs/tos_info.h"
#include "cloudfs/store/unified_block_store.h"
#include "cloudfs/store/upload_tos_mgr.h"
#include "cloudfs/volume_failure_summary.h"
#include "cloudfs_proto/DatanodeProtocol.pb.h"
#include "cloudfs_proto/hdfs.pb.h"
#include "common/memory_pool.h"
#include "common/store_types.h"
#include "encoding/int128.h"
#include "gflags/gflags.h"
#include "gmock/gmock.h"
#include "gtest/gtest.h"
#include "proto/chunkserver.pb.h"

namespace bds::dancedn::cloudfs {

class BlockEntryMgr;

class Store;
class UnifiedBlockStore;
class ReplicaBeingWritten;
class NameSpaceInfo;

class MockStore : public UnifiedBlockStore {
 public:
  MockStore() {}
  virtual ~MockStore() {}

  MOCK_METHOD1(GetDiskIoutil, double(const std::string& disk_dir));
  MOCK_METHOD2(InitStorage,
               exceptions::Exception(
                   DataNode* dn,
                   const bytestore::chunkserver::DiskIdConfMap* disk_config));
  MOCK_METHOD1(AddDirAndBlock,
               exceptions::Exception(const NameSpaceInfo* ns_info));
  MOCK_CONST_METHOD0(GetDatanodeUUID, std::string());
  MOCK_CONST_METHOD1(GetBPStorage, StorageInfo*(const std::string& pool_id));
  MOCK_METHOD2(
      GetVolumeReports,
      void(std::unordered_map<bytestore::DiskId,
                              std::shared_ptr<StorageToBlockPoolReport>>*
               disk_id_to_bp_used,
           std::unordered_map<uint64_t, uint64_t>* bp_to_bp_total_used));
  MOCK_METHOD2(GetStorageReports,
               exceptions::Exception(const std::string& pool_id,
                                     std::vector<StorageReport*>* reports));
  MOCK_CONST_METHOD0(GetVolumeFailureSummary, VolumeFailureSummary*());
  MOCK_CONST_METHOD2(GetReplicaVisibleLength,
                     exceptions::Exception(const ExtendedBlock& block,
                                           int64_t* res));
  MOCK_CONST_METHOD3(GetReplicaVisibleLengthV2,
                     exceptions::Exception(const ExtendedBlock& block,
                                           int64_t* res,
                                           int32_t* replica_state));
  MOCK_CONST_METHOD2(GetBlockLocalPathInfo,
                     exceptions::Exception(const ExtendedBlock& block,
                                           BlockLocalPathInfo* res));
  MOCK_CONST_METHOD3(
      GetHdfsBlocksMetadata,
      exceptions::Exception(const std::string& pool_id,
                            const std::vector<uint64_t>& block_ids,
                            HdfsBlocksMetadata* res));
  MOCK_CONST_METHOD1(GetFinalizedBlocksOnPersistentStorage,
                     std::vector<std::shared_ptr<FinalizedReplica>>(
                         const std::string& block_pool_id));
  MOCK_CONST_METHOD1(GetStorageType,
                     StorageType(const std::string& storage_uuid));
  MOCK_CONST_METHOD1(GetStorage,
                     DatanodeStorage(const std::string& storage_uuid));
  MOCK_CONST_METHOD2(
      GetReplica, std::shared_ptr<ReplicaInfo>(const std::string& block_pool_id,
                                               uint64_t block_id));
  MOCK_CONST_METHOD2(GetBlockV2,
                     exceptions::Exception(uint128_t_pod key,
                                           std::shared_ptr<BlockV2>& block));
  MOCK_METHOD5(ReadChecksum,
               exceptions::Exception(ExtendedBlock* block, io::IOChunk* chunk,
                                     uint32_t checksum_offset,
                                     uint32_t checksum_data_len,
                                     bytestore::IOPriority priority));
  MOCK_METHOD6(WriteChecksum,
               exceptions::Exception(ExtendedBlock* block, io::IOChunk* chunk,
                                     uint32_t checksum_offset,
                                     uint32_t checksum_data_len, bool sync,
                                     bytestore::IOPriority priority));
  MOCK_METHOD5(ReadBlock,
               exceptions::Exception(ExtendedBlock* block, io::IOChunk* chunk,
                                     uint32_t data_offset, uint32_t data_len,
                                     bytestore::IOPriority priority));
  MOCK_METHOD6(WriteBlock,
               exceptions::Exception(ExtendedBlock* block, io::IOChunk* chunk,
                                     uint32_t data_offset, uint32_t data_len,
                                     bool sync,
                                     bytestore::IOPriority priority));
  MOCK_METHOD6(ReadBlock,
               exceptions::Exception(ExtendedBlock* block, io::IOChunk* chunk,
                                     uint32_t data_offset, uint32_t data_len,
                                     bool from_tos,
                                     bytestore::IOPriority priority));
  MOCK_METHOD3(SyncBlock,
               exceptions::Exception(ExtendedBlock* block, uint64_t written_len,
                                     bytestore::IOPriority priority));
  MOCK_METHOD5(RecoverClose,
               exceptions::Exception(ExtendedBlock* block, uint64_t latest_gs,
                                     uint64_t min_bytes_rcvd,
                                     std::string* storage_uuid,
                                     DataXceiver* xceiver));
  MOCK_METHOD4(
      CreateTemporary,
      exceptions::Exception(StorageType storage_type, ExtendedBlock* block,
                            std::shared_ptr<ReplicaInPipeline>* replica,
                            bytestore::IOPriority priority));
  MOCK_METHOD5(CreateTemporary,
               exceptions::Exception(
                   StorageType storage_type, ExtendedBlock* block,
                   std::shared_ptr<ReplicaInPipeline>* replica,
                   int32_t resident_time, bytestore::IOPriority priority));
  MOCK_METHOD5(CreateRbw, exceptions::Exception(
                              StorageType storage_type, ExtendedBlock* block,
                              bool allow_lazy_persist,
                              std::shared_ptr<ReplicaInPipeline>* replica,
                              bytestore::IOPriority priority));
  MOCK_METHOD6(CreateRbw,
               exceptions::Exception(
                   StorageType storage_type, ExtendedBlock* block,
                   bool allow_lazy_persist,
                   std::shared_ptr<ReplicaInPipeline>* replica,
                   int32_t resident_time, bytestore::IOPriority priority));
  MOCK_METHOD5(RecoverRbw, exceptions::Exception(
                               ExtendedBlock* block, uint64_t new_gs,
                               uint64_t min_bytes_rcvd, uint64_t max_bytes_rcvd,
                               std::shared_ptr<ReplicaInPipeline>* replica));
  MOCK_METHOD5(
      Append, exceptions::Exception(ExtendedBlock* block, uint64_t new_gs,
                                    uint64_t min_bytes_rcvd,
                                    std::shared_ptr<ReplicaInPipeline>* replica,
                                    bytestore::IOPriority priority));
  MOCK_METHOD4(RecoverAppend, exceptions::Exception(
                                  ExtendedBlock* block, uint64_t new_gs,
                                  uint64_t min_bytes_rcvd,
                                  std::shared_ptr<ReplicaInPipeline>* replica));
  MOCK_METHOD2(ReadDiskChecksum,
               exceptions::Exception(ExtendedBlock* block,
                                     DataChecksum** disk_checksum));
  MOCK_METHOD4(WriteBlockMetaHeader,
               exceptions::Exception(ExtendedBlock* block,
                                     DataChecksum* disk_checksum,
                                     StorageType storage_type,
                                     int32_t resident_time));
  MOCK_METHOD2(FinalizeBlock,
               exceptions::Exception(ExtendedBlock* block,
                                     bytestore::IOPriority priority));
  MOCK_METHOD2(UnFinalizeBlock,
               exceptions::Exception(ExtendedBlock* block,
                                     bytestore::IOPriority priority));
  MOCK_METHOD3(
      ConvertTemporaryToRbw,
      exceptions::Exception(ExtendedBlock* block,
                            std::shared_ptr<ReplicaInPipeline>* replica,
                            bytestore::IOPriority priority));
  MOCK_CONST_METHOD1(IsValidRbw, bool(ExtendedBlock* block));
  MOCK_CONST_METHOD1(IsValidBlock, bool(ExtendedBlock* block));
  MOCK_CONST_METHOD3(CheckBlock, exceptions::Exception(ExtendedBlock* block,
                                                       uint64_t min_len,
                                                       ReplicaState state));
  MOCK_CONST_METHOD2(GetBlockLength, exceptions::Exception(ExtendedBlock* block,
                                                           uint64_t* length));
  MOCK_METHOD3(MoveBlockAcrossStorage,
               exceptions::Exception(const ExtendedBlock* block,
                                     const StorageType& storage_type,
                                     ReplicaInfo** replica_info));
  MOCK_METHOD2(InitReplicaRecovery,
               exceptions::Exception(const RecoveringBlock* rblock,
                                     ReplicaRecoveryInfo** rinfo));
  MOCK_METHOD4(UpdateReplicaUnderRecovery,
               exceptions::Exception(const ExtendedBlock* old_block,
                                     uint64_t recovery_id, uint64_t new_length,
                                     std::string* stroage_id));
  MOCK_METHOD3(Invalidate,
               exceptions::Exception(const std::string& bpid,
                                     const std::vector<Block*>& blocks,
                                     bytestore::IOPriority priority));
  MOCK_METHOD0(CheckDataDir, std::set<std::string>());
  MOCK_METHOD1(
      CloseVolumes,
      exceptions::Exception(const std::set<std::string>& volumes_to_close));
  MOCK_METHOD1(RemoveVolumes,
               exceptions::Exception(
                   const std::set<std::string>& absolute_volume_paths));
  MOCK_CONST_METHOD2(AcquireActivity,
                     exceptions::Exception(uint32_t disk_id, bool* available));
  MOCK_METHOD1(GetCurrentActivities,
               void(std::unordered_map<std::string, int>* map));
  MOCK_CONST_METHOD1(GetVolumePath,
                     std::string(const std::shared_ptr<ReplicaInfo>& replica));
  MOCK_METHOD1(CleanupBlockPool, void(const std::string& bpid));
  MOCK_CONST_METHOD1(GetDisk,
                     bytestore::chunkserver::Disk*(bytestore::DiskId disk_id));
  MOCK_CONST_METHOD0(GetDiskConfig,
                     const bytestore::chunkserver::DiskIdConfMap*());
  MOCK_CONST_METHOD1(GetRemoteStore, RemoteStore*(NamespaceType type));
  MOCK_CONST_METHOD0(GetLocalStore, ChunkServerStore*());
  MOCK_CONST_METHOD0(GetPartialBlockStore, PartialBlockStore*());
  MOCK_METHOD5(CreateBlockStream,
               exceptions::Exception(ExtendedBlock* block, uint64_t offset,
                                     uint32_t length,
                                     const CachingStrategy& strategy,
                                     BlockStream** returned_stream));
  MOCK_METHOD0(GetBlockEntryMgr, std::shared_ptr<BlockEntryMgr>());

  MOCK_METHOD1(StartUploadBlock, void(ExtendedBlock* block));
  MOCK_METHOD2(CalculateCrc,
               exceptions::Exception(
                   const std::shared_ptr<ExtendedBlock>& block, uint32_t* crc));

  // async method
  MOCK_METHOD0(GetDiskIdSdMap,
               std::unordered_map<uint32_t, StorageDirectory>*());
  MOCK_METHOD5(GetNextVolume,
               exceptions::Exception(const StorageType& storage_type,
                                     NamespaceType namespace_type,
                                     const std::string& bpid,
                                     uint64_t block_size, uint16_t* disk_id));
  MOCK_METHOD2(GetNamespaceType,
               bool(const std::string& bpid, NamespaceType* ns_type));
  MOCK_METHOD7(CreateBlockAsync,
               void(bytestore::DiskId disk_id,
                    bytestore::CSIOService* io_service,
                    google::protobuf::RpcController* controller,
                    const ::cloudfs::CreateBlockRequestProto* request,
                    ::cloudfs::CreateBlockResponseProto* response,
                    bytestore::MemoryPool* mem_pool,
                    google::protobuf::Closure* done));
  MOCK_METHOD6(WriteBlockAsync,
               void(bytestore::CSIOService* io_service,
                    google::protobuf::RpcController* controller,
                    const ::cloudfs::WriteBlockRequestProto* request,
                    ::cloudfs::WriteBlockResponseProto* response,
                    bytestore::MemoryPool* mem_pool,
                    google::protobuf::Closure* done));
  MOCK_METHOD6(ReadBlockAsync,
               void(bytestore::CSIOService* io_service,
                    google::protobuf::RpcController* controller,
                    const ::cloudfs::ClientReadBlockRequestProto* request,
                    ::cloudfs::ClientReadBlockResponseProto* response,
                    bytestore::MemoryPool* mem_pool,
                    google::protobuf::Closure* done));
  MOCK_METHOD6(ReadTosBlockAsync,
               void(bytestore::CSIOService* io_service,
                    google::protobuf::RpcController* controller,
                    const ::cloudfs::ClientReadBlockRequestProto* request,
                    ::cloudfs::ClientReadBlockResponseProto* response,
                    bytestore::MemoryPool* mem_pool,
                    google::protobuf::Closure* done));
  MOCK_METHOD6(SealBlockAsync,
               void(bytestore::CSIOService* io_service,
                    google::protobuf::RpcController* controller,
                    const ::cloudfs::SealBlockRequestProto* request,
                    ::cloudfs::SealBlockResponseProto* response,
                    bytestore::MemoryPool* mem_pool,
                    google::protobuf::Closure* done));
  MOCK_METHOD6(FinalizeBlockAsync,
               void(bytestore::CSIOService* io_service,
                    google::protobuf::RpcController* controller,
                    const ::cloudfs::FinalizeBlockRequestProto* request,
                    ::cloudfs::FinalizeBlockResponseProto* response,
                    bytestore::MemoryPool* mem_pool,
                    google::protobuf::Closure* done));
  MOCK_METHOD6(SyncBlockAsync,
               void(bytestore::CSIOService* io_service,
                    google::protobuf::RpcController* controller,
                    const ::cloudfs::SyncBlockRequestProto* request,
                    ::cloudfs::SyncBlockResponseProto* response,
                    bytestore::MemoryPool* mem_pool,
                    google::protobuf::Closure* done));
  MOCK_METHOD6(PingBlockAsync,
               void(bytestore::CSIOService* io_service,
                    google::protobuf::RpcController* controller,
                    const ::cloudfs::PingBlockRequestProto* request,
                    ::cloudfs::PingBlockResponseProto* response,
                    bytestore::MemoryPool* mem_pool,
                    google::protobuf::Closure* done));
  MOCK_METHOD2(GetStorageUuid, exceptions::Exception(bytestore::DiskId disk_id,
                                                     std::string* uuid));
  MOCK_METHOD4(SubmitInvalidateChunkAsync,
               void(bytestore::DiskId disk_id,
                    const bytestore::ChunkId& chunk_id,
                    bytestore::IOPriority priority,
                    Closure<void, exceptions::Exception>* cb));
  MOCK_METHOD1(ReadedBlock, void(const uint128_t_pod& key));
  MOCK_METHOD1(ReadedPartialBlock, void(const PartialBlockId& key));
};

class MockChunkserverStore : public ChunkServerStore {
 public:
  MockChunkserverStore() {}
  ~MockChunkserverStore() {}

  MOCK_METHOD1(GetDiskIoutil, double(const std::string& disk_dir));
  MOCK_METHOD2(InitStorage,
               exceptions::Exception(
                   DataNode* dn,
                   const bytestore::chunkserver::DiskIdConfMap* disk_config));
  MOCK_METHOD1(AddDirAndBlock,
               exceptions::Exception(const NameSpaceInfo* ns_info));
  MOCK_METHOD0(GetAllReplicas, std::vector<std::shared_ptr<ReplicaInfo>>());
  MOCK_CONST_METHOD0(GetDatanodeUUID, std::string());
  MOCK_CONST_METHOD1(GetBPStorage, StorageInfo*(const std::string& pool_id));
  MOCK_METHOD2(GetStorageReports,
               exceptions::Exception(const std::string& pool_id,
                                     std::vector<StorageReport*>* reports));
  MOCK_CONST_METHOD0(GetVolumeFailureSummary, VolumeFailureSummary*());
  MOCK_CONST_METHOD2(GetReplicaVisibleLength,
                     exceptions::Exception(const ExtendedBlock& block,
                                           int64_t* res));
  MOCK_CONST_METHOD2(GetBlockLocalPathInfo,
                     exceptions::Exception(const ExtendedBlock& block,
                                           BlockLocalPathInfo* res));
  MOCK_CONST_METHOD3(
      GetHdfsBlocksMetadata,
      exceptions::Exception(const std::string& pool_id,
                            const std::vector<uint64_t>& block_ids,
                            HdfsBlocksMetadata* res));
  MOCK_CONST_METHOD1(GetFinalizedBlocksOnPersistentStorage,
                     std::vector<std::shared_ptr<FinalizedReplica>>(
                         const std::string& block_pool_id));
  MOCK_CONST_METHOD1(GetStorageType,
                     StorageType(const std::string& storage_uuid));
  MOCK_CONST_METHOD3(
      GetBlockReportsV2,
      exceptions::Exception(
          std::unordered_map<DatanodeStorage, BlockListAsString*,
                             DatanodeStorageHash>* reports,
          const std::string& bpid,
          std::function<bool(const std::shared_ptr<ReplicaInfo>&)> filter));
  MOCK_CONST_METHOD1(GetStorage,
                     DatanodeStorage(const std::string& storage_uuid));
  MOCK_CONST_METHOD2(
      GetReplica, std::shared_ptr<ReplicaInfo>(const std::string& block_pool_id,
                                               uint64_t block_i));
  MOCK_METHOD3(GetDataChecksum,
               exceptions::Exception(const ExtendedBlock* block,
                                     bool corrupt_checksum_ok,
                                     DataChecksum** csum));
  MOCK_METHOD5(ReadChecksum,
               exceptions::Exception(ExtendedBlock* block, io::IOChunk* chunk,
                                     uint32_t checksum_offset,
                                     uint32_t checksum_data_len,
                                     bytestore::IOPriority priority));
  MOCK_METHOD5(ReadBlock,
               exceptions::Exception(ExtendedBlock* block, io::IOChunk* chunk,
                                     uint32_t data_offset, uint32_t data_len,
                                     bytestore::IOPriority priority));
  MOCK_METHOD6(WriteBlock,
               exceptions::Exception(ExtendedBlock* block, io::IOChunk* chunk,
                                     uint32_t data_offset, uint32_t data_len,
                                     bool sync,
                                     bytestore::IOPriority priority));
  MOCK_METHOD3(SyncBlock,
               exceptions::Exception(ExtendedBlock* block, uint64_t written_len,
                                     bytestore::IOPriority priority));
  MOCK_METHOD5(RecoverClose,
               exceptions::Exception(ExtendedBlock* block, uint64_t latest_gs,
                                     uint64_t min_bytes_rcvd,
                                     std::string* storage_uuid,
                                     DataXceiver* xceiver));
  MOCK_METHOD4(
      CreateTemporary,
      exceptions::Exception(StorageType storage_type, ExtendedBlock* block,
                            std::shared_ptr<ReplicaInPipeline>* replica,
                            bytestore::IOPriority priority));
  MOCK_METHOD5(CreateRbw, exceptions::Exception(
                              StorageType storage_type, ExtendedBlock* block,
                              bool allow_lazy_persist,
                              std::shared_ptr<ReplicaInPipeline>* replica,
                              bytestore::IOPriority priority));
  MOCK_METHOD5(RecoverRbw, exceptions::Exception(
                               ExtendedBlock* block, uint64_t new_gs,
                               uint64_t min_bytes_rcvd, uint64_t max_bytes_rcvd,
                               std::shared_ptr<ReplicaInPipeline>* replica));
  MOCK_METHOD4(Append, exceptions::Exception(
                           ExtendedBlock* block, uint64_t new_gs,
                           uint64_t min_bytes_rcvd,
                           std::shared_ptr<ReplicaInPipeline>* replica));
  MOCK_METHOD4(RecoverAppend, exceptions::Exception(
                                  ExtendedBlock* block, uint64_t new_gs,
                                  uint64_t min_bytes_rcvd,
                                  std::shared_ptr<ReplicaInPipeline>* replica));
  MOCK_METHOD2(ReadDiskChecksum,
               exceptions::Exception(ExtendedBlock* block,
                                     DataChecksum** disk_checksum));
  MOCK_METHOD2(WriteBlockMetaHeader,
               exceptions::Exception(ExtendedBlock* block,
                                     DataChecksum* disk_checksum));
  MOCK_METHOD2(FinalizeBlock,
               exceptions::Exception(ExtendedBlock* block,
                                     bytestore::IOPriority priority));
  MOCK_METHOD2(UnFinalizeBlock,
               exceptions::Exception(ExtendedBlock* block,
                                     bytestore::IOPriority priority));
  MOCK_METHOD2(
      ConvertTemporaryToRbw,
      exceptions::Exception(ExtendedBlock* block,
                            std::shared_ptr<ReplicaInPipeline>* replica));
  MOCK_CONST_METHOD1(IsValidRbw, bool(ExtendedBlock* block));
  MOCK_CONST_METHOD1(IsValidBlock, bool(ExtendedBlock* block));
  MOCK_CONST_METHOD3(CheckBlock, exceptions::Exception(ExtendedBlock* block,
                                                       uint64_t min_len,
                                                       ReplicaState state));
  MOCK_CONST_METHOD2(GetBlockLength, exceptions::Exception(ExtendedBlock* block,
                                                           uint64_t* length));
  MOCK_METHOD3(MoveBlockAcrossStorage,
               exceptions::Exception(const ExtendedBlock* block,
                                     const StorageType& storage_type,
                                     ReplicaInfo** replica_info));
  MOCK_METHOD2(InitReplicaRecovery,
               exceptions::Exception(const RecoveringBlock* rblock,
                                     ReplicaRecoveryInfo** rinfo));
  MOCK_METHOD4(UpdateReplicaUnderRecovery,
               exceptions::Exception(const ExtendedBlock* old_block,
                                     uint64_t recovery_id, uint64_t new_length,
                                     std::string* stroage_id));
  MOCK_METHOD3(Invalidate,
               exceptions::Exception(const std::string& bpid,
                                     const std::vector<Block*>& blocks,
                                     bytestore::IOPriority priority));
  MOCK_METHOD0(CheckDataDir, std::set<std::string>());
  MOCK_METHOD1(
      CloseVolumes,
      exceptions::Exception(const std::set<std::string>& volumes_to_close));
  MOCK_METHOD1(RemoveVolumes,
               exceptions::Exception(
                   const std::set<std::string>& absolute_volume_paths));
  MOCK_CONST_METHOD2(AcquireActivity,
                     exceptions::Exception(uint32_t disk_id, bool* available));
  MOCK_METHOD1(GetCurrentActivities,
               void(std::unordered_map<std::string, int>* map));
  MOCK_CONST_METHOD1(GetVolumePath,
                     std::string(const std::shared_ptr<ReplicaInfo>& replica));
  MOCK_METHOD1(CleanupBlockPool, void(const std::string& bpid));
  MOCK_CONST_METHOD1(GetDisk,
                     bytestore::chunkserver::Disk*(bytestore::DiskId disk_id));
  MOCK_CONST_METHOD0(GetDiskConfig,
                     const bytestore::chunkserver::DiskIdConfMap*());
  MOCK_METHOD2(
      UpdateReplicaXAttr,
      exceptions::Exception(const std::shared_ptr<ReplicaInfo>& replica,
                            bytestore::IOPriority priority));
  MOCK_METHOD3(CopyBlockAcrossFederation,
               exceptions::Exception(ExtendedBlock* src_block,
                                     ExtendedBlock* dst_block,
                                     const StorageType& target_storage_type));
  MOCK_METHOD3(SubmitMarkReplicaStorageClassBG,
               void(const std::string& bpid, uint64_t block_id,
                    StorageClass storage_class));
  MOCK_METHOD5(SubmitInvalidateReplicaBG,
               void(const std::string& bpid, uint64_t block_id, bool is_evict,
                    bytestore::IOPriority priority,
                    Closure<void, exceptions::Exception>* cb));
  MOCK_METHOD3(SubmitMarkReplicaEvictableBG,
               void(const std::string& bpid, uint64_t block_id,
                    StorageClass storage_class));
  MOCK_METHOD0(GetMigratedTargetDisks,
               std::deque<bytestore::chunkserver::Disk*>());
  MOCK_METHOD0(GetDisks, std::deque<DiskDirectory>());
  MOCK_METHOD3(CheckReplicaForMigrationScanner,
               exceptions::Exception(ExtendedBlock* block,
                                     bytestore::ChunkId* chunk_id,
                                     bytestore::chunkserver::Disk* disk));
  MOCK_METHOD2(UpdateMigratedReplica,
               exceptions::Exception(ExtendedBlock* block,
                                     bytestore::DiskId dst_disk_id));
  MOCK_METHOD4(CheckAndUpdate,
               exceptions::Exception(ExtendedBlock* block,
                                     ReplicaState disk_replica_state,
                                     bytestore::DiskId dst_disk_id,
                                     bool corrupt));

  MOCK_METHOD2(GetNamespaceType,
               bool(const std::string& bpid, NamespaceType* ns_type));
  //  Async IO methods
  MOCK_METHOD7(CreateBlockAsync,
               void(bytestore::DiskId disk_id,
                    bytestore::CSIOService* io_service,
                    google::protobuf::RpcController* controller,
                    const ::cloudfs::CreateBlockRequestProto* request,
                    ::cloudfs::CreateBlockResponseProto* response,
                    bytestore::MemoryPool* mem_pool,
                    google::protobuf::Closure* done));
  MOCK_METHOD6(WriteBlockAsync,
               void(bytestore::CSIOService* io_service,
                    google::protobuf::RpcController* controller,
                    const ::cloudfs::WriteBlockRequestProto* request,
                    ::cloudfs::WriteBlockResponseProto* response,
                    bytestore::MemoryPool* mem_pool,
                    google::protobuf::Closure* done));
  MOCK_METHOD6(ReadBlockAsync,
               void(bytestore::CSIOService* io_service,
                    google::protobuf::RpcController* controller,
                    const ::cloudfs::ClientReadBlockRequestProto* request,
                    ::cloudfs::ClientReadBlockResponseProto* response,
                    bytestore::MemoryPool* mem_pool,
                    google::protobuf::Closure* done));
  MOCK_METHOD6(ReadTosBlockAsync,
               void(bytestore::CSIOService* io_service,
                    google::protobuf::RpcController* controller,
                    const ::cloudfs::ClientReadBlockRequestProto* request,
                    ::cloudfs::ClientReadBlockResponseProto* response,
                    bytestore::MemoryPool* mem_pool,
                    google::protobuf::Closure* done));
  MOCK_METHOD6(SealBlockAsync,
               void(bytestore::CSIOService* io_service,
                    google::protobuf::RpcController* controller,
                    const ::cloudfs::SealBlockRequestProto* request,
                    ::cloudfs::SealBlockResponseProto* response,
                    bytestore::MemoryPool* mem_pool,
                    google::protobuf::Closure* done));
  MOCK_METHOD6(FinalizeBlockAsync,
               void(bytestore::CSIOService* io_service,
                    google::protobuf::RpcController* controller,
                    const ::cloudfs::FinalizeBlockRequestProto* request,
                    ::cloudfs::FinalizeBlockResponseProto* response,
                    bytestore::MemoryPool* mem_pool,
                    google::protobuf::Closure* done));
  MOCK_METHOD6(SyncBlockAsync,
               void(bytestore::CSIOService* io_service,
                    google::protobuf::RpcController* controller,
                    const ::cloudfs::SyncBlockRequestProto* request,
                    ::cloudfs::SyncBlockResponseProto* response,
                    bytestore::MemoryPool* mem_pool,
                    google::protobuf::Closure* done));
  MOCK_METHOD6(PingBlockAsync,
               void(bytestore::CSIOService* io_service,
                    google::protobuf::RpcController* controller,
                    const ::cloudfs::PingBlockRequestProto* request,
                    ::cloudfs::PingBlockResponseProto* response,
                    bytestore::MemoryPool* mem_pool,
                    google::protobuf::Closure* done));
};

class MockTosStore : public TosStore {
 public:
  MockTosStore() : TosStore(nullptr, nullptr) {}
  ~MockTosStore() {}

  MOCK_METHOD4(UploadBlock,
               exceptions::Exception(const ExtendedBlock& block,
                                     const std::string& upload_id,
                                     bytestore::IOPriority priority,
                                     CompletedPart* completed));
  MOCK_METHOD6(AccMultipartUploadBlock,
               exceptions::Exception(const std::vector<ExtendedBlock>& blocks,
                                     const std::string& object_key,
                                     const std::string& upload_id,
                                     const std::vector<uint32_t> part_nums,
                                     bytestore::IOPriority priority,
                                     CompletedPart* completed));
  // MOCK_METHOD2(PutBlock,
  //              exceptions::Exception(const ExtendedBlock& block,
  //              bytestore::IOPriority priority));
  MOCK_METHOD3(AppendBlock,
               exceptions::Exception(const ExtendedBlock& block,
                                     bytestore::IOPriority priority,
                                     std::string& etag));
  MOCK_METHOD5(ReadBlock,
               exceptions::Exception(const ExtendedBlock& block,
                                     uint64_t data_offset, uint32_t data_len,
                                     bytestore::IOPriority priority,
                                     io::IOChunk* chunk));
  MOCK_METHOD6(ReadBlockAsync,
               void(const ExtendedBlock& block, uint64_t data_offset,
                    uint32_t data_len, bytestore::IOPriority priority,
                    byterpc::IOBuf* buffer,
                    Closure<void, exceptions::Exception>* done));
  MOCK_METHOD3(DeleteBlock,
               exceptions::Exception(const std::string& bpid,
                                     const std::string& object_key,
                                     bytestore::IOPriority priority));
  MOCK_METHOD4(DeleteBlocks,
               exceptions::Exception(const std::string& bpid,
                                     const std::vector<std::string>& keys,
                                     bytestore::IOPriority priority,
                                     std::vector<std::string>* deleted_keys));
  MOCK_METHOD4(GetBlockInfo,
               exceptions::Exception(const std::string& bpid,
                                     const std::string& object_key,
                                     bytestore::IOPriority priority,
                                     std::shared_ptr<ExtendedBlock>& block));
  MOCK_METHOD3(
      CreateMultipartUploadV2,
      exceptions::Exception(const std::shared_ptr<ExtendedBlock>& block,
                            void** ctx, bytestore::IOPriority priority));
  MOCK_METHOD3(MultipartUploadPartV2,
               exceptions::Exception(void* ctx, io::IOChunk* chunk,
                                     uint32_t part_num));
  MOCK_METHOD1(CompleteMultipartUploadV2, exceptions::Exception(void* ctx));
  MOCK_METHOD1(AbortMultipartUploadV2, exceptions::Exception(void* ctx));
};

class MockHdfsStore : public HdfsStore {
 public:
  MockHdfsStore() : HdfsStore(nullptr, nullptr) {}
  ~MockHdfsStore() = default;

  MOCK_METHOD2(AcquireHdfsClient,
               HdfsClient*(const std::string& bpid, const HDFSInfoPtr& info));
  MOCK_METHOD2(ReleaseHdfsClient,
               void(const std::string& bpid, HdfsClient* client));
  MOCK_METHOD5(ReadBlock,
               exceptions::Exception(const ExtendedBlock& block,
                                     uint64_t data_offset, uint32_t data_len,
                                     bytestore::IOPriority priority,
                                     io::IOChunk* chunk));
  MOCK_METHOD4(GetBlockInfo,
               exceptions::Exception(const std::string& bpid,
                                     const std::string& object_key,
                                     bytestore::IOPriority priority,
                                     std::shared_ptr<ExtendedBlock>& block));
};

class MockFetchManager : public FetchManager {
 public:
  MockFetchManager() {}
  ~MockFetchManager() {}

  MOCK_METHOD2(AsyncCacheBlock,
               void(ExtendedBlock* block, FetchManager::Source source));
  MOCK_METHOD1(StartSyncCacheBlock,
               std::shared_ptr<Context>(ExtendedBlock* block));
  MOCK_METHOD1(WriteData,
               exceptions::Exception(const std::shared_ptr<Context>& context));
  MOCK_METHOD1(FinishSyncCacheBlock,
               void(const std::shared_ptr<Context>& context));
};

class MockUploadTosMgr : public UploadTosMgr {
 public:
  MockUploadTosMgr() {}
  ~MockUploadTosMgr() {}

  MOCK_METHOD2(TriggerUpload,
               void(ExtendedBlock* block, const std::string& upload_id));
};

}  // namespace bds::dancedn::cloudfs
