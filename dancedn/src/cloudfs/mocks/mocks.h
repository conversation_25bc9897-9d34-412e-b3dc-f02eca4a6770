// Copyright (c) 2018-present, ByteDance Inc. All rights reserved.

#pragma once

#include <cstdint>
#include <deque>
#include <map>
#include <memory>
#include <set>
#include <string>
#include <unordered_map>
#include <vector>

#include "aws/core/auth/AWSCredentialsProvider.h"
#include "chunkserver/disk.h"
#include "chunkserver/tiering/migration_placement.h"
#include "cloudfs/block.h"
#include "cloudfs/block_list_as_longs.h"
#include "cloudfs/block_local_path_info.h"
#include "cloudfs/block_pool_actor.h"
#include "cloudfs/block_pool_manager.h"
#include "cloudfs/block_pool_service.h"
#include "cloudfs/block_recovery.h"
#include "cloudfs/block_report_options.h"
#include "cloudfs/caching_strategy.h"
#include "cloudfs/cfs/qos_acquire.h"
#include "cloudfs/cfs/storage_class.h"
#include "cloudfs/constants.h"
#include "cloudfs/data_checksum.h"
#include "cloudfs/data_transfer.h"
#include "cloudfs/data_xceiver.h"
#include "cloudfs/datanode.h"
#include "cloudfs/datanode_config.h"
#include "cloudfs/datanode_id.h"
#include "cloudfs/datanode_storage.h"
#include "cloudfs/datanode_stream_server.h"
#include "cloudfs/directory_scanner.h"
#include "cloudfs/exceptions.h"
#include "cloudfs/extended_block.h"
#include "cloudfs/finalized_replica.h"
#include "cloudfs/hdfs_blocks_metadata.h"
#include "cloudfs/io/connection.h"
#include "cloudfs/io/define.h"
#include "cloudfs/io/io_buf.h"
#include "cloudfs/lifecycle/block_entry_mgr.h"
#include "cloudfs/namenode_client.h"
#include "cloudfs/namenode_rpc.h"
#include "cloudfs/namespace_info.h"
#include "cloudfs/replica_being_written.h"
#include "cloudfs/replica_info.h"
#include "cloudfs/replica_state.h"
#include "cloudfs/storage_info.h"
#include "cloudfs/storage_report.h"
#include "cloudfs/storage_type.h"
#include "cloudfs/store.h"
#include "cloudfs/store/chunkserver/chunkserver_store.h"
#include "cloudfs/store/chunkserver/partial_block.h"
#include "cloudfs/store/chunkserver/partial_block_store.h"
#include "cloudfs/store/ufs/remote_store.h"
#include "cloudfs/store/ufs/tos_info.h"
#include "cloudfs/store/unified_block_store.h"
#include "cloudfs/volume_failure_summary.h"
#include "cloudfs_proto/DatanodeProtocol.pb.h"
#include "cloudfs_proto/hdfs.pb.h"
#include "common/memory_pool.h"
#include "common/store_types.h"
#include "encoding/int128.h"
#include "gflags/gflags.h"
#include "gmock/gmock.h"
#include "gtest/gtest.h"
#include "proto/chunkserver.pb.h"

namespace bds::dancedn::cloudfs {

class BlockEntryMgr;

class Store;
class UnifiedBlockStore;
class ReplicaBeingWritten;
class NameSpaceInfo;

class MockBlockRecoveryThread : public BlockRecoveryThread {
 public:
  explicit MockBlockRecoveryThread(DataNode* dn) : BlockRecoveryThread(dn) {}
  MockBlockRecoveryThread(DataNode* dn, const std::string& who,
                          std::vector<RecoveringBlock*>& blocks)
      : BlockRecoveryThread(dn, who, blocks) {}
  ~MockBlockRecoveryThread() {}

  exceptions::Exception RecoverBlock(const RecoveringBlock* rb) {
    return BlockRecoveryThread::RecoverBlock(rb);
  }

  exceptions::Exception RecoverBlockV2(const RecoveringBlock* rb) {
    return BlockRecoveryThread::RecoverBlockV2(rb);
  }
};

class MockRuntimeMonitor : public io::RuntimeMonitor {
 public:
  MOCK_METHOD1(GetDiskIoutil, double(const std::string& dir));
};

class MockMigrationPlacement : public MigrationPlacement {
 public:
  MOCK_METHOD2(SelectDisk, Errorcode(const std::vector<bytestore::DiskId>&,
                                     bytestore::DiskId*));
};

class MockDataXceiver : public DataXceiver {
 public:
  explicit MockDataXceiver(io::Connection* conn, DataNode* dn,
                           DataNodeStreamServer* server,
                           ReplicaInPipeline* test_replica, std::string name)
      : DataXceiver(conn, dn, server) {
    test_replica_ = test_replica;
    SetName(name);
  }
  ~MockDataXceiver() {}

  void Run() {
    if (test_replica_ != nullptr) {
      test_replica_->ResetWriter(this, 5000);
    }
    byte::ThisThread::SleepInMs(500);
    GetServer()->DeleteXceiver(GetPtr());
  }

 private:
  ReplicaInPipeline* test_replica_;
  bool need_reset_write_;
};

class MockDataNodeStreamServer : public DataNodeStreamServer {
 public:
  MockDataNodeStreamServer(const std::string& ip, int port, DataNode* dn)
      : DataNodeStreamServer(ip, port, dn) {}

  void AddXceiver(io::Connection* conn) {
    DataNodeStreamServer::AddXceiver(
        std::make_shared<MockDataXceiver>(conn, dn_, this, nullptr, ""));
  }
};
}  // namespace bds::dancedn::cloudfs
