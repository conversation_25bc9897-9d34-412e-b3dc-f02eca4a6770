// Copyright (c) 2025-present, ByteDance Inc. All rights reserved.

#pragma once

#include <vector>

#include "cloudfs/data_transfer.h"
#include "gmock/gmock.h"
#include "gtest/gtest.h"

namespace bds::dancedn::cloudfs {
class MockDataTransferManager : public DataTransferManager {};

class MockDataTransfer : public DataTransfer {
 public:
  MockDataTransfer(std::vector<DatanodeInfo*>* targets,
                   const std::vector<StorageType>& target_storage_types,
                   ExtendedBlock* block, DataNode* dn)
      : DataTransfer(0, targets, target_storage_types, block,
                     BlockConstructionStage::PIPELINE_SETUP_CREATE, "", dn) {}
  ~MockDataTransfer() {}
  MOCK_METHOD0(MainRun, exceptions::Exception());
};
}  // namespace bds::dancedn::cloudfs
