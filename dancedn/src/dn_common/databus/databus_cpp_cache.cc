// Copyright 2018 Zhi<PERSON>uan Xiao <<EMAIL>>

#include "databus_cpp_cache.h"

#include <pthread.h>

#include <chrono>
#include <memory>

#include "byte/include/byte_log.h"
#include "client.h"

namespace cfs {
namespace databus_cpp_client {

bool DatabusCache::push(const collector::RequestPayload& payload) {
  return push(payload, 0);
}

bool DatabusCache::push(const collector::RequestPayload& payload,
                        int64_t timeout_ms) {
  DatabusData coming_data(payload);
  auto msg_count = payload.messages_size();
  std::unique_lock<std::mutex> gu(mutex_);
  auto start_time = std::chrono::system_clock::now();
  auto time_out_time =
      std::chrono::system_clock::now() + std::chrono::milliseconds(timeout_ms);
  if (max_cache_time_ms_ != 0 && !data_queue_.empty() &&
      start_time - data_queue_.front()->in_queue_time() >
          std::chrono::milliseconds(max_cache_time_ms_)) {
    // cache expire
    LOG(WARNING) << "fail push DatabusCache because expired, msg_count:"
                 << msg_count;
    return false;
  }
  if (timeout_ms <= 0) {  // no timeout, must return immediately
    if (cache_used_bytes_ + static_cast<int64_t>(coming_data.size()) >
        max_cache_bytes_) {
      // push into cache fail, cache full
      LOG(WARNING) << "fail push DatabusCache because cache full, msg_count:"
                   << msg_count;
      return false;
    } else {
      data_queue_.push_back(std::make_shared<DatabusData>(coming_data));
      cv_push_.notify_all();
      cache_used_bytes_ += coming_data.size();
      // cache push success
      LOG(DEBUG) << "succ push DatabusCache, msg_count:" << msg_count;
      return true;
    }
  }
  // with timeout, should retry until timeout
  auto current_time = std::chrono::system_clock::now();
  while (cache_used_bytes_ + static_cast<int64_t>(coming_data.size()) >
             max_cache_bytes_ &&
         time_out_time > current_time) {
    auto is_notified = cv_pop_.wait_until(gu, time_out_time);
    current_time = std::chrono::system_clock::now();
    if (is_notified == std::cv_status::no_timeout) {
      continue;
    } else {
      // cache fail metrics
      LOG(WARNING) << "fail push DatabusCache because retry timeout, msg_count:"
                   << msg_count;
      return false;
    }
  }
  if (current_time >= time_out_time) {
    // cache fail metrics
    LOG(WARNING) << "fail push DatabusCache because retry timeout, msg_count:"
                 << msg_count;
    return false;
  }
  data_queue_.push_back(std::make_shared<DatabusData>(coming_data));
  cv_push_.notify_all();
  cache_used_bytes_ += coming_data.size();
  // cache success metrics
  LOG(DEBUG) << "succ push DatabusCache, msg_count:" << msg_count;
  return true;
}

bool DatabusCache::flush(int64_t timeout_ms) {
  auto now = std::chrono::system_clock::now();
  auto time_out_time =
      std::chrono::system_clock::now() + std::chrono::milliseconds(timeout_ms);
  std::unique_lock<std::mutex> gu(mutex_);
  while (now < time_out_time) {
    if (data_queue_.empty()) {
      return true;
    }
    auto message = data_queue_.front();
    error_t sending_err;

    do {
      sending_err = databus_channel_->emit_payload(message->payload(), true);
      now = std::chrono::system_clock::now();
      if (sending_err == 0) {
        // emit metrics
        data_queue_.pop_front();
        cv_pop_.notify_all();
        cache_used_bytes_ -= message->size();
        break;
      } else {
        std::this_thread::sleep_for(std::chrono::milliseconds(1));
      }
    } while (now < time_out_time);
    now = std::chrono::system_clock::now();
  }
  auto len = data_queue_.size();
  data_queue_.clear();
  cache_used_bytes_ = 0;
  if (len > 0) {
    // emit metrics for lost
    return false;
  } else {
    return true;
  }
}

void DatabusCache::run() {
  pthread_setname_np(pthread_self(), "DatabusCacheFlush");

  std::shared_ptr<DatabusData> sending_data{nullptr};
  while (running_.load(std::memory_order_acquire)) {
    if (!sending_data) {
      std::unique_lock<std::mutex> gu(mutex_);
      if (!data_queue_.empty()) {
        sending_data = data_queue_.front();
        data_queue_.pop_front();
      } else {
        cv_push_.wait_for(gu, std::chrono::milliseconds(1000));
        continue;
      }
    }
    if (sending_data) {
      error_t sending_err =
          databus_channel_->emit_payload(sending_data->payload(), true);
      if (sending_err == 0) {
        std::lock_guard<std::mutex> gu(mutex_);
        cache_used_bytes_ -= sending_data->size();
        cv_pop_.notify_all();
        sending_data.reset();
      } else {
        std::this_thread::sleep_for(std::chrono::milliseconds(50));
      }
    }
  }
}

void DatabusCache::stop() {
  running_ = false;
  cv_push_.notify_all();
  if (send_thread_.joinable()) {
    send_thread_.join();
  }
}

}  // namespace databus_cpp_client
}  // namespace cfs
