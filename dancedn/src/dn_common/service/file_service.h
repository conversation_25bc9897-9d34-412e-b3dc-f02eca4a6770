// Copyright (c) 2025-present, ByteDance Inc. All rights reserved.

#pragma once

#include <memory>
#include <string>

#include "include/macros.h"
#include "include/proto/client_dancedn.pb.h"

namespace bds::dancedn {

class FileService : public ::bds::proto::DanceDNFileService {
 public:
  FileService() = default;
  virtual ~FileService() = default;

  void CreateDir(::google::protobuf::RpcController* controller,
                 const ::bds::proto::CreateDirectoryRequest* request,
                 ::bds::proto::CreateDirectoryResponse* response,
                 ::google::protobuf::Closure* done) override;
  void CreateFile(::google::protobuf::RpcController* controller,
                  const ::bds::proto::CreateFileRequest* request,
                  ::bds::proto::CreateFileResponse* response,
                  ::google::protobuf::Closure* done) override;
  void CreateDentry(::google::protobuf::RpcController* controller,
                    const ::bds::proto::CreateDentryRequest* request,
                    ::bds::proto::CreateDentryResponse* response,
                    ::google::protobuf::Closure* done) override;
  void CreateBlock(::google::protobuf::RpcController* controller,
                   const ::bds::proto::CreateBlockRequest* request,
                   ::bds::proto::CreateBlockResponse* response,
                   ::google::protobuf::Closure* done) override;
  void WriteBlock(::google::protobuf::RpcController* controller,
                  const ::bds::proto::WriteBlockRequest* request,
                  ::bds::proto::WriteBlockResponse* response,
                  ::google::protobuf::Closure* done) override;
  void ReadBlock(::google::protobuf::RpcController* controller,
                 const ::bds::proto::ReadBlockRequest* request,
                 ::bds::proto::ReadBlockResponse* response,
                 ::google::protobuf::Closure* done) override;
  void GetFileInfo(::google::protobuf::RpcController* controller,
                   const ::bds::proto::GetFileInfoRequest* request,
                   ::bds::proto::GetFileInfoResponse* response,
                   ::google::protobuf::Closure* done) override;
  void GetBlockInfo(::google::protobuf::RpcController* controller,
                    const ::bds::proto::GetBlockInfoRequest* request,
                    ::bds::proto::GetBlockInfoResponse* response,
                    ::google::protobuf::Closure* done) override;
  void ListDir(::google::protobuf::RpcController* controller,
               const ::bds::proto::ListDirRequest* request,
               ::bds::proto::ListDirResponse* response,
               ::google::protobuf::Closure* done) override;
  void RemoveDir(::google::protobuf::RpcController* controller,
                 const ::bds::proto::RemoveDirRequest* request,
                 ::bds::proto::RemoveDirResponse* response,
                 ::google::protobuf::Closure* done) override;
  void RemoveFile(::google::protobuf::RpcController* controller,
                  const ::bds::proto::RemoveFileRequest* request,
                  ::bds::proto::RemoveFileResponse* response,
                  ::google::protobuf::Closure* done) override;
  void RemoveBlock(::google::protobuf::RpcController* controller,
                   const ::bds::proto::RemoveBlockRequest* request,
                   ::bds::proto::RemoveBlockResponse* response,
                   ::google::protobuf::Closure* done) override;

 private:
  DISALLOW_COPY_AND_ASSIGN(FileService);
};
}  // namespace bds::dancedn
