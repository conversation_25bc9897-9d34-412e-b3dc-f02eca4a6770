#!/bin/bash
set -e -o pipefail
set -x

usage() {
  echo "
Usage: $0 <options>
  Optional options:
    --thirdparty-version <the-version-of-thirdparty>
      The scm version of thirdparty, if not specified, it will use the version
      into in file third/current_thirdparty_version.
    --build-type <Debug|Release|RelWithDebInfo>
      Set build type, default to RelWithDebInfo
    --jobs <number-of-parallel-jobs>
      The number of third parties to build in parallel. Default to the number of CPU.
    --build-unit-test <ON|OFF>
      If need to build unit test. Default to ON.
    --enable-gcov
      If need to enable gcov.
    -h, --help
      Print this usage.
  "
}

OPTS=$(getopt \
  -n $0 \
  -o 'h' \
  -l 'thirdparty-version:' \
  -l 'build-type:' \
  -l 'build-unit-test:' \
  -l 'enable-gcov' \
  -l 'jobs:' \
  -l 'help' \
  -- "$@")

if [ $? != 0 ] ; then
  usage
  exit 1
fi

eval set -- "$OPTS"

CUR_DIR=$(dirname "$0")
DANCEDN_DIR=$(cd "$CUR_DIR"; pwd)
THIRDPARTY_INSTALL_DIR=${DANCEDN_DIR}/third/install

THIRDPARTY_VERSION=$(grep CloudFS ${DANCEDN_DIR}/third/current_thirdparty_version | awk -F'=' '{print $NF}')
BUILD_TYPE="RelWithDebInfo"
BUILD_UNIT_TEST="ON"
ENABLE_GCOV="OFF"
JOBS=$(grep -c ^processor /proc/cpuinfo 2>/dev/null)

eval set -- "$OPTS"
while true; do
  case "$1" in
    --thirdparty-version)
      THIRDPARTY_VERSION="$2"
      shift 2
      ;;
    --build-type)
      BUILD_TYPE="$2"
      shift 2
      ;;
    --build-unit-test)
      BUILD_UNIT_TEST="$2"
      shift 2
      ;;
    --jobs)
      JOBS="$2"
      shift 2
      ;;
    --enable-gcov)
      ENABLE_GCOV="ON"
      shift 1
      ;;
    -h | --help)
      usage
      exit 0
      ;;
    --)
      shift ; break ;;
    *) usage; exit 1 ;;
  esac
done

if [ -f "${THIRDPARTY_INSTALL_DIR}/current_revision" ]; then
  # do update
  CURRENT_THIRDPARTY_VERSION=$(cat ${THIRDPARTY_INSTALL_DIR}/current_revision | grep version | awk -F':' '{print $2}')
  if [ "$CURRENT_THIRDPARTY_VERSION" == "$THIRDPARTY_VERSION" ]; then
    echo "The thirdparty version is the same as the current version, skip update."
  else
    pushd ${THIRDPARTY_INSTALL_DIR}
    echo "update thirdparty version to ${THIRDPARTY_VERSION}"
    bvc reset ${THIRDPARTY_VERSION}
    popd
  fi
else 
  # download thirdparty
  rm -rf ${THIRDPARTY_INSTALL_DIR}
  pushd ${DANCEDN_DIR}/third
  echo "download thirdparty version to ${THIRDPARTY_VERSION}"
  bvc clone inf/bds/dancedn_thirdparty install --version ${THIRDPARTY_VERSION}
  popd
fi

# update proto
pushd ${DANCEDN_DIR}/..
git submodule update --init cloudfs_proto
popd

# build dancedn
mkdir -p ${DANCEDN_DIR}/build
pushd ${DANCEDN_DIR}/build
cmake -DCMAKE_INSTALL_PREFIX=${THIRDPARTY_INSTALL_DIR} \
      -DCMAKE_PREFIX_PATH=${THIRDPARTY_INSTALL_DIR} \
      -DCMAKE_CXX_COMPILER=/usr/bin/c++ \
      -DCMAKE_C_COMPILER=/usr/bin/cc \
      -DCMAKE_INCLUDE_PATH=${THIRDPARTY_INSTALL_DIR}/include \
      -DCMAKE_LIBRARY_PATH=${THIRDPARTY_INSTALL_DIR}/lib \
      -DCMAKE_BUILD_TYPE=${BUILD_TYPE} \
      -DBUILD_UNIT_TEST=${BUILD_UNIT_TEST} \
      -DENABLE_CLOUDFS=ON \
      -DENABLE_GCOV=${ENABLE_GCOV} \
      ..
make -j ${JOBS}
popd

# do cpplint
pushd ${DANCEDN_DIR}
set +x
find src -path "src/dn_common/databus" -prune -o \( -name "*.h" -o -name "*.cc" \) -print | split -l 10 -d -a 3 && mv x??? ${DANCEDN_DIR}/build/
rm -f ${DANCEDN_DIR}/build/cpplint.result
for file in `ls ${DANCEDN_DIR}/build/x???`; do
  python cpplint.py --filter=-runtime/references \
    $(cat $file) 2>> ${DANCEDN_DIR}/build/cpplint.result & 
done
wait
rm -f ${DANCEDN_DIR}/build/x[0-9]??
set +e
grep 'Total errors found' ${DANCEDN_DIR}/build/cpplint.result
if [ $? -eq 0 ]; then
  echo "cpplint failed"
  cat ${DANCEDN_DIR}/build/cpplint.result | grep -v 'Done processing' | grep -v 'Total errors found'
  retcode=1
else
  echo "cpplint passed"
  retcode=0
fi
set -e
set -x
popd
if [ ${retcode} -ne 0 ]; then
  echo "build failed"
  exit ${retcode}
else
  echo "build succeed"
fi

