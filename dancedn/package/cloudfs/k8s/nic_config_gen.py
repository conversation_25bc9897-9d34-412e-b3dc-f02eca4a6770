#!/usr/bin/python3
import psutil
import requests
import json
import os
import sys
import socket

json_data = {}
nic_infos = []
addrs = psutil.net_if_addrs()
stats = psutil.net_if_stats()

vpc_ipv4 = requests.get('http://***********/volcstack/latest/private_ipv4')
vpc_id = requests.get('http://***********/volcstack/latest/vpc_id')
if vpc_ipv4.status_code == 200 and vpc_id.status_code == 200 and vpc_id.text.startswith('vpc-'):
    vpc_info = {}
    vpc_info['IP'] = vpc_ipv4.text
    vpc_info['ClusterId'] = vpc_id.text
    nic_infos.append(vpc_info)
else:
    print("Get vpc info failed, exit...")
    sys.exit(0)

try:
    rdma_tag = os.environ["MY_LOCATION_SWITCH"]
    for name, stat in stats.items():
        if stat.mtu > 1000 and stat.mtu < 10000 and stat.mtu != 1500:
            for info in addrs[name]:
                if info.family == socket.AF_INET:
                    rdma_info = {}
                    rdma_info['IP'] = info.address
                    rdma_info['ClusterId'] = 'rdma-' + rdma_tag
                    nic_infos.append(rdma_info)
                    break
except KeyError:
   print("write vpc info only since MY_LOCATION_SWITCH is not set")

json_data['NicConfig'] = nic_infos
# json_str = json.dumps(json_data)
# print(json_str)
with open('/opt/tiger/dancedn_deploy/conf/nic_config.json', 'w') as f:
    json.dump(json_data, f, indent=4)
