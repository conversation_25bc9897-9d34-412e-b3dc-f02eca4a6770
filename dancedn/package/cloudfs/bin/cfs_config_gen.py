#!/usr/bin/python3

import jinja2
import json
import os
import sys

def render(env, conf, template, result):
    print("Render jinja2 template {} to {}".format(template, result))
    template = env.get_template(template)
    f = open(result, 'w')
    f.write(template.render(conf))
    f.close()

def generate_disk_info(base_dir, prefix, media_type, max_num=100):
    global disk_id
    global disk_params
    max_id = disk_id + max_num
    directories = [f for f in os.listdir(base_dir) if not os.path.isfile(os.path.join(base_dir, f))]
    for directory in directories:
        if directory.startswith(prefix):
            disk_param = {
                "disk_id": disk_id,
                "media_type": media_type,
                "store_path": os.path.join(base_dir, directory, "dancedn")
            }
            disk_params.append(disk_param)
            disk_id += 1
            if disk_id == max_id:
                return

def get_ecs_type():
    file_path = '/opt/tiger/conf/ecs.info'
    try:
        if os.path.exists(file_path):
            with open(file_path, 'r') as f:
                ecs_info = json.load(f)
                return ecs_info['Result']['Instances'][0]['InstanceTypeId']
        else:
            print(f"Error: File '{file_path}' does not exist.")
            return "UNKNOWN"
    except Exception as e:
        print(f"An error occurred: {str(e)}")
        return "UNKNOWN"

def get_ecs_net_throughput():
    ecs_type = get_ecs_type()
    net_throughput_map = {
        "ecs.i2.2xlarge": 750000000,    # 6Gbps
        "ecs.i2.3xlarge": 1100000000,   # 8.8Gbps
        "ecs.i2.4xlarge": 1500000000,   # 12Gbps
        "ecs.i2.8xlarge": 3000000000,   # 24Gbps
        "ecs.i3s.3xlarge": 1125000000,  # 9Gbps
        "ecs.i3s.6xlarge": 2250000000,  # 18Gbps
        "ecs.i3s.12xlarge": 4500000000,  # 36Gbps
    }
    return net_throughput_map.get(ecs_type, 1000000000)

def calc_k8s_tmpfs_extra_size():  # k8s使用emtrydir+memory方案，有1.2倍的放大系数，reserved去掉额外系数
    mount_point = '/mem00'
    import psutil
    if os.path.ismount(mount_point):
        disk_usage = psutil.disk_usage(mount_point)
        total_size = disk_usage.total  # 总大小（字节）
        extra_size = int(0.167 * disk_usage.total)

        print(f"Total size of {mount_point} in bytes: {total_size}, reserved size: {extra_size}")
        return extra_size
    else:
        print(f"The mount point {mount_point} does not exist.")
        return 30000000

if __name__=='__main__':
    hostname = sys.argv[1]
    bvc_version = sys.argv[2]
    print("Load /opt/tiger/conf/config-dn.json")
    f = open("/opt/tiger/conf/config-dn.json", 'r')
    conf = json.load(f)
    f.close()

    conf["CFSFlags"]["bytestore_cfs_node_name"] = hostname
    conf["CFSFlags"]["bytestore_hdfs_bvc_version"] = bvc_version

    if "MY_LOCATION_AZ" in os.environ:
        conf["CFSFlags"]["bytestore_cfs_location_az"] = os.getenv("MY_LOCATION_AZ")
    if "MY_LOCATION_SWITCH" in os.environ:
        conf["CFSFlags"]["bytestore_cfs_location_switch"] = os.getenv("MY_LOCATION_SWITCH")
    if "MY_LOCATION_HOST" in os.environ:
        conf["CFSFlags"]["bytestore_cfs_location_host"] = os.getenv("MY_LOCATION_HOST")

    if "DEPLOY_ENV" in os.environ:
        deploy_env = os.getenv("DEPLOY_ENV")
        if deploy_env == "VKE":
            conf["CFSFlags"]["bytestore_cfs_decommission_stat_file"] = conf["CFSFlags"]["bytestore_log_dir"] + "/cfs_decommission_"
        elif deploy_env == "TCE_SIDECAR":
            conf["CFSFlags"]["bytestore_chunkserver_reserved_disk_size_for_ram_disk"] = calc_k8s_tmpfs_extra_size()
            conf["CFSFlags"]["bytestore_region_name"] = os.getenv("TCE_ZONE", "China-North")
            conf["CFSFlags"]["bytestore_cfs_filesystem_name"] = os.getenv("CLOUDNATIVE_APPLICATION_ID", "default")
        elif deploy_env == "ARNOLD" or deploy_env == "BERNARD":
            conf["CFSFlags"]["bytestore_cfs_decommission_stat_file"] = conf["CFSFlags"]["bytestore_log_dir"] + "/cfs_decommission_"
            conf["CFSFlags"]["bytestore_chunkserver_reserved_disk_size_for_ram_disk"] = calc_k8s_tmpfs_extra_size()
    else:  # deploy in volcengine full managed mode
        ecs_net_throughput = get_ecs_net_throughput()
        conf["CFSFlags"]["bytestore_cfs_net_in_throughput_limit_bytes"] = ecs_net_throughput
        conf["CFSFlags"]["bytestore_cfs_net_out_throughput_limit_bytes"] = ecs_net_throughput

    env = jinja2.Environment(loader=jinja2.FileSystemLoader("/opt/tiger/dancedn_deploy/conf"))

    # generate hdfs_config.json
    render(env, conf["HdfsConfig"], "hdfs_config.json.j2", "/opt/tiger/dancedn_deploy/conf/hdfs_config.json")
    # generate cfs_flags.conf
    render(env, conf, "cfs_flags.conf.j2",  "/opt/tiger/dancedn_deploy/conf/cfs_flags.conf")
    # generate namenode_config.json
    render(env, conf["NamenodeConfig"], "namenode_config.json.j2",  "/opt/tiger/dancedn_deploy/conf/namenode_config.json")

    # generate chunkserver_config.json
    disk_id = 1
    disk_params = []
    if not isinstance(conf["DiskConfig"], list):
        generate_disk_info(conf["DiskConfig"]["BaseDir"], conf["DiskConfig"]["Prefix"], conf["DiskConfig"]["MediaType"])
    else:
        for disk_config in conf["DiskConfig"]:
            if "MaxDiskNum" in disk_config:
                generate_disk_info(disk_config["BaseDir"], disk_config["Prefix"], disk_config["MediaType"], disk_config["MaxDiskNum"])
            else:
                generate_disk_info(disk_config["BaseDir"], disk_config["Prefix"], disk_config["MediaType"])
    conf_themes = {"disk_params": disk_params}
    conf = {"conf_themes": conf_themes}
    print(conf_themes)
    with open("/opt/tiger/dancedn_deploy/conf/chunkserver_config.json", 'w') as f:
        json.dump(conf, f, indent=2)
