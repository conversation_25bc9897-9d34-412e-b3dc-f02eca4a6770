{
   ignore_bvar_1
   Memcheck:Cond
   fun:round
   fun:inplace_divide
   fun:append_second
   fun:append
   fun:_ZN4bvar13PassiveStatusIiE13SeriesSampler11take_sampleEv
   fun:_ZN4bvar6detail16SamplerCollector3runEv
   fun:_ZN4bvar6detail16SamplerCollector15sampling_threadEPv
   fun:start_thread
   fun:clone
}

{
   ignore_bvar_2
   Memcheck:Cond
   fun:_ZNK4bvar9PerSecondINS_13PassiveStatusImEEE9get_valueEl
   fun:_ZN4bvar6detail10WindowBaseINS_13PassiveStatusImEELNS_15SeriesFrequencyE1EE13SeriesSampler11take_sampleEv
   fun:_ZN4bvar6detail16SamplerCollector3runEv
   fun:_ZN4bvar6detail16SamplerCollector15sampling_threadEPv
   fun:start_thread
   fun:clone
}

{
   ignore_bvar_3
   Memcheck:Cond
   fun:inplace_divide
   fun:append_second
   fun:append
   fun:_ZN4bvar6detail10WindowBaseINS_13PassiveStatusImEELNS_15SeriesFrequencyE1EE13SeriesSampler11take_sampleEv
   fun:_ZN4bvar6detail16SamplerCollector3runEv
   fun:_ZN4bvar6detail16SamplerCollector15sampling_threadEPv
   fun:start_thread
   fun:clone
}

{
   ignore_bvar_4
   Memcheck:Cond
   fun:round
   fun:_ZNK4bvar9PerSecondINS_13PassiveStatusImEEE9get_valueEl
   fun:_ZN4bvar6detail10WindowBaseINS_13PassiveStatusImEELNS_15SeriesFrequencyE1EE13SeriesSampler11take_sampleEv
   fun:_ZN4bvar6detail16SamplerCollector3runEv
   fun:_ZN4bvar6detail16SamplerCollector15sampling_threadEPv
   fun:start_thread
   fun:clone
}

{
   ignore_bvar_5
   Memcheck:Cond
   fun:round
   fun:inplace_divide
   fun:append_second
   fun:append
   fun:_ZN4bvar6detail10WindowBaseINS_13PassiveStatusImEELNS_15SeriesFrequencyE1EE13SeriesSampler11take_sampleEv
   fun:_ZN4bvar6detail16SamplerCollector3runEv
   fun:_ZN4bvar6detail16SamplerCollector15sampling_threadEPv
   fun:start_thread
   fun:clone
}

{
   ignore_unwind_1
   Memcheck:Param
   msync(start)
   obj:/lib/x86_64-linux-gnu/libpthread-2.24.so
   obj:/usr/lib/x86_64-linux-gnu/libunwind.so.8.0.1
   obj:/usr/lib/x86_64-linux-gnu/libunwind.so.8.0.1
   obj:/usr/lib/x86_64-linux-gnu/libunwind.so.8.0.1
   obj:/usr/lib/x86_64-linux-gnu/libunwind.so.8.0.1
   obj:/usr/lib/x86_64-linux-gnu/libunwind.so.8.0.1
   fun:_ULx86_64_step
   obj:/usr/lib/x86_64-linux-gnu/libunwind.so.8.0.1
   fun:backtrace
   fun:__static_initialization_and_destruction_0
   fun:_GLOBAL__sub_I_mutex.cpp
   fun:call_init.part.0
   fun:call_init
   fun:_dl_init
}

{
   ignore_unwind_2
   Memcheck:Param
   msync(start)
   obj:/lib/x86_64-linux-gnu/libpthread-2.24.so
   obj:/usr/lib/x86_64-linux-gnu/libunwind.so.8.0.1
   obj:/usr/lib/x86_64-linux-gnu/libunwind.so.8.0.1
   obj:/usr/lib/x86_64-linux-gnu/libunwind.so.8.0.1
   obj:/usr/lib/x86_64-linux-gnu/libunwind.so.8.0.1
   fun:_ULx86_64_step
   obj:/usr/lib/x86_64-linux-gnu/libunwind.so.8.0.1
   fun:backtrace
   fun:__static_initialization_and_destruction_0
   fun:_GLOBAL__sub_I_mutex.cpp
   fun:call_init.part.0
   fun:call_init
   fun:_dl_init
   obj:/lib/x86_64-linux-gnu/ld-2.24.so
}

{
   ignore_unwind_3
   Memcheck:Param
   msync(start)
   fun:__msync_nocancel
   obj:/usr/lib/x86_64-linux-gnu/libunwind.so.8.0.1
   obj:/usr/lib/x86_64-linux-gnu/libunwind.so.8.0.1
   obj:/usr/lib/x86_64-linux-gnu/libunwind.so.8.0.1
   obj:/usr/lib/x86_64-linux-gnu/libunwind.so.8.0.1
   fun:_ULx86_64_step
   fun:_ZL23GetStackTrace_libunwindPPvii
   fun:_Z13GetStackTracePPvii
   fun:RecordGrowth
   fun:_ZN8tcmalloc8PageHeap8GrowHeapEm
   fun:_ZN8tcmalloc8PageHeap3NewEm
   fun:_ZN8tcmalloc15CentralFreeList8PopulateEv
   fun:_ZN8tcmalloc15CentralFreeList21FetchFromOneSpansSafeEiPPvS2_
}

{
   ignore_unwind_4
   Memcheck:Param
   msync(start)
   obj:/lib/x86_64-linux-gnu/libpthread-2.19.so
   obj:/usr/lib/x86_64-linux-gnu/libunwind.so.8.0.1
   obj:/usr/lib/x86_64-linux-gnu/libunwind.so.8.0.1
   obj:/usr/lib/x86_64-linux-gnu/libunwind.so.8.0.1
   obj:/usr/lib/x86_64-linux-gnu/libunwind.so.8.0.1
   fun:_ULx86_64_step
   fun:_ZL23GetStackTrace_libunwindPPvii
   fun:_Z13GetStackTracePPvii
   fun:RecordGrowth
   fun:_ZN8tcmalloc8PageHeap8GrowHeapEm
   fun:_ZN8tcmalloc8PageHeap3NewEm
   fun:_ZN12_GLOBAL__N_1L15do_malloc_pagesEPN8tcmalloc11ThreadCacheEm
   fun:_ZN8tcmalloc29allocate_full_cpp_nothrow_oomEm
}

{
   ignore_unwind_5
   Memcheck:Param
   msync(start)
   obj:/lib/x86_64-linux-gnu/libpthread-2.19.so
   obj:/usr/lib/x86_64-linux-gnu/libunwind.so.8.0.1
   obj:/usr/lib/x86_64-linux-gnu/libunwind.so.8.0.1
   obj:/usr/lib/x86_64-linux-gnu/libunwind.so.8.0.1
   obj:/usr/lib/x86_64-linux-gnu/libunwind.so.8.0.1
   fun:_ULx86_64_step
   fun:_ZL23GetStackTrace_libunwindPPvii
   fun:_Z13GetStackTracePPvii
   fun:RecordGrowth
   fun:_ZN8tcmalloc8PageHeap8GrowHeapEm
   fun:_ZN8tcmalloc8PageHeap3NewEm
   fun:_ZN12_GLOBAL__N_1L15do_malloc_pagesEPN8tcmalloc11ThreadCacheEm
   fun:_ZN8tcmalloc24allocate_full_malloc_oomEm
}

{
   ignore_unwind_6
   Memcheck:Param
   msync(start)
   obj:/lib/x86_64-linux-gnu/libpthread-2.19.so
   obj:/usr/lib/x86_64-linux-gnu/libunwind.so.8.0.1
   obj:/usr/lib/x86_64-linux-gnu/libunwind.so.8.0.1
   obj:/usr/lib/x86_64-linux-gnu/libunwind.so.8.0.1
   obj:/usr/lib/x86_64-linux-gnu/libunwind.so.8.0.1
   fun:_ULx86_64_step
   fun:_ZL23GetStackTrace_libunwindPPvii
   fun:_Z13GetStackTracePPvii
   fun:RecordGrowth
   fun:_ZN8tcmalloc8PageHeap8GrowHeapEm
   fun:_ZN8tcmalloc8PageHeap3NewEm
   fun:_ZN8tcmalloc15CentralFreeList8PopulateEv
   fun:_ZN8tcmalloc15CentralFreeList21FetchFromOneSpansSafeEiPPvS2_
}

{
   ignore_brpc_keytable1
   Memcheck:Leak
   match-leak-kinds: definite
   fun:_Znwm
   fun:_ZN4brpc6Server13StartInternalERK7in_addrRKNS_9PortRangeEPKNS_13ServerOptionsE
   fun:_ZN4brpc6Server5StartERKN5butil8EndPointEPKNS_13ServerOptionsE
   fun:_ZN4brpc6Server5StartEPKcPKNS_13ServerOptionsE
   fun:_ZN9bytestore10rootserver28GetRebalanceChunk_Basic_Test8TestBodyEv
   fun:_ZN7testing8internal35HandleExceptionsInMethodIfSupportedINS_4TestEvEET0_PT_MS4_FS3_vEPKc
   fun:_ZN7testing4Test3RunEv
   obj:/gitrunner/builds/20031782/0/storage/bytestore/build/bytestore/rootserver/rebalance_controller_test
   obj:/gitrunner/builds/20031782/0/storage/bytestore/build/bytestore/rootserver/rebalance_controller_test
   fun:_ZN7testing8internal12UnitTestImpl11RunAllTestsEv
   fun:_ZN7testing8internal35HandleExceptionsInMethodIfSupportedINS0_12UnitTestImplEbEET0_PT_MS4_FS3_vEPKc
   fun:_ZN7testing8UnitTest3RunEv
}

{
   ignore_brpc_keytable2
   Memcheck:Leak
   match-leak-kinds: definite
   fun:_Znwm
   fun:_ZN4brpc6Server13StartInternalERK7in_addrRKNS_9PortRangeEPKNS_13ServerOptionsE
   fun:_ZN4brpc6Server5StartERKN5butil8EndPointEPKNS_13ServerOptionsE
   fun:_ZN4brpc6Server5StartEPKcPKNS_13ServerOptionsE
   fun:Start
   fun:_ZN9bytestore10rootserver28GetRebalanceChunk_Basic_Test8TestBodyEv
   fun:HandleSehExceptionsInMethodIfSupported<testing::Test, void>
   fun:_ZN7testing8internal35HandleExceptionsInMethodIfSupportedINS_4TestEvEET0_PT_MS4_FS3_vEPKc
   fun:_ZN7testing4Test3RunEv
   fun:_ZN7testing8TestInfo3RunEv.part.494
   fun:Run
   fun:_ZN7testing8TestCase3RunEv.part.495
   fun:Run
   fun:_ZN7testing8internal12UnitTestImpl11RunAllTestsEv
   fun:HandleSehExceptionsInMethodIfSupported<testing::internal::UnitTestImpl, bool>
   fun:_ZN7testing8internal35HandleExceptionsInMethodIfSupportedINS0_12UnitTestImplEbEET0_PT_MS4_FS3_vEPKc
   fun:_ZN7testing8UnitTest3RunEv
}

{
   ignore_brpc_keytable3
   Memcheck:Leak
   match-leak-kinds: definite
   fun:_Znwm
   fun:_ZN4brpc6Server13StartInternalERK7in_addrRKNS_9PortRangeEPKNS_13ServerOptionsE
   fun:_ZN4brpc6Server5StartERKN5butil8EndPointEPKNS_13ServerOptionsE
   fun:_ZN4brpc6Server5StartEPKcPKNS_13ServerOptionsE
   fun:Start
   fun:_ZN9bytestore10metaserver32ChunkCollector_BuildTestEnv_Test8TestBodyEv
   fun:HandleSehExceptionsInMethodIfSupported<testing::Test, void>
   fun:_ZN7testing8internal35HandleExceptionsInMethodIfSupportedINS_4TestEvEET0_PT_MS4_FS3_vEPKc
   fun:_ZN7testing4Test3RunEv
   fun:_ZN7testing8TestInfo3RunEv.part.494
   fun:Run
   fun:_ZN7testing8TestCase3RunEv.part.495
   fun:Run
   fun:_ZN7testing8internal12UnitTestImpl11RunAllTestsEv
   fun:HandleSehExceptionsInMethodIfSupported<testing::internal::UnitTestImpl, bool>
   fun:_ZN7testing8internal35HandleExceptionsInMethodIfSupportedINS0_12UnitTestImplEbEET0_PT_MS4_FS3_vEPKc
   fun:_ZN7testing8UnitTest3RunEv
}

{
   ignore_brpc_keytable4
   Memcheck:Leak
   match-leak-kinds: definite
   fun:_Znwm
   fun:_ZN4brpc6Server13StartInternalERK7in_addrRKNS_9PortRangeEPKNS_13ServerOptionsE
   fun:_ZN4brpc6Server5StartERKN5butil8EndPointEPKNS_13ServerOptionsE
   fun:_ZN4brpc6Server5StartEPKcPKNS_13ServerOptionsE
}

{
   ignore_brpc_keytable5
   Memcheck:Leak
   match-leak-kinds: definite
   fun:_Znwm
   fun:_ZN4brpc6Server13StartInternalERK8in6_addrRKNS_9PortRangeEPKNS_13ServerOptionsE
   fun:_ZN4brpc6Server5StartERKN5butil8EndPointEPKNS_13ServerOptionsE
   fun:_ZN4brpc6Server5StartEPKcPKNS_13ServerOptionsE
   fun:_ZN9bytestore10rootserver28GetRebalanceChunk_Basic_Test8TestBodyEv
   fun:_ZN7testing8internal35HandleExceptionsInMethodIfSupportedINS_4TestEvEET0_PT_MS4_FS3_vEPKc
   fun:_ZN7testing4Test3RunEv
   obj:/gitrunner/builds/20031782/0/storage/bytestore/build/bytestore/rootserver/rebalance_controller_test
   obj:/gitrunner/builds/20031782/0/storage/bytestore/build/bytestore/rootserver/rebalance_controller_test
   fun:_ZN7testing8internal12UnitTestImpl11RunAllTestsEv
   fun:_ZN7testing8internal35HandleExceptionsInMethodIfSupportedINS0_12UnitTestImplEbEET0_PT_MS4_FS3_vEPKc
   fun:_ZN7testing8UnitTest3RunEv
}

{
   ignore_brpc_keytable6
   Memcheck:Leak
   match-leak-kinds: definite
   fun:_Znwm
   fun:_ZN4brpc6Server13StartInternalERK8in6_addrRKNS_9PortRangeEPKNS_13ServerOptionsE
   fun:_ZN4brpc6Server5StartERKN5butil8EndPointEPKNS_13ServerOptionsE
   fun:_ZN4brpc6Server5StartEPKcPKNS_13ServerOptionsE
   fun:Start
   fun:_ZN9bytestore10rootserver28GetRebalanceChunk_Basic_Test8TestBodyEv
   fun:HandleSehExceptionsInMethodIfSupported<testing::Test, void>
   fun:_ZN7testing8internal35HandleExceptionsInMethodIfSupportedINS_4TestEvEET0_PT_MS4_FS3_vEPKc
   fun:_ZN7testing4Test3RunEv
   fun:_ZN7testing8TestInfo3RunEv
   fun:_ZN7testing8TestCase3RunEv
   fun:_ZN7testing8internal12UnitTestImpl11RunAllTestsEv
   fun:HandleSehExceptionsInMethodIfSupported<testing::internal::UnitTestImpl, bool>
   fun:_ZN7testing8internal35HandleExceptionsInMethodIfSupportedINS0_12UnitTestImplEbEET0_PT_MS4_FS3_vEPKc
   fun:_ZN7testing8UnitTest3RunEv
}

{
   ignore_brpc_keytable7
   Memcheck:Leak
   match-leak-kinds: definite
   fun:_Znwm
   fun:_ZN4brpc6Server13StartInternalERK8in6_addrRKNS_9PortRangeEPKNS_13ServerOptionsE
   fun:_ZN4brpc6Server5StartERKN5butil8EndPointEPKNS_13ServerOptionsE
   fun:_ZN4brpc6Server5StartEPKcPKNS_13ServerOptionsE
   fun:Start
   fun:_ZN9bytestore10metaserver32ChunkCollector_BuildTestEnv_Test8TestBodyEv
   fun:HandleSehExceptionsInMethodIfSupported<testing::Test, void>
   fun:_ZN7testing8internal35HandleExceptionsInMethodIfSupportedINS_4TestEvEET0_PT_MS4_FS3_vEPKc
   fun:_ZN7testing4Test3RunEv
   fun:_ZN7testing8TestInfo3RunEv
   fun:_ZN7testing8TestCase3RunEv
   fun:_ZN7testing8internal12UnitTestImpl11RunAllTestsEv
   fun:HandleSehExceptionsInMethodIfSupported<testing::internal::UnitTestImpl, bool>
   fun:_ZN7testing8internal35HandleExceptionsInMethodIfSupportedINS0_12UnitTestImplEbEET0_PT_MS4_FS3_vEPKc
   fun:_ZN7testing8UnitTest3RunEv
}

{
   ignore_brpc_keytable8
   Memcheck:Leak
   match-leak-kinds: definite
   fun:_Znwm
   fun:_ZN4brpc6Server13StartInternalERK8in6_addrRKNS_9PortRangeEPKNS_13ServerOptionsE
   fun:_ZN4brpc6Server5StartERKN5butil8EndPointEPKNS_13ServerOptionsE
   fun:_ZN4brpc6Server5StartEPKcPKNS_13ServerOptionsE
}

{
   ignore_aws0
   Memcheck:Leak
   match-leak-kinds: definite
   ...
   fun:s2n_init
   ...
}

{
   ignore_aws1
   Memcheck:Leak
   match-leak-kinds: indirect
   ...
   fun:s2n_init
   ...
}
{
   ignore_aws2
   Memcheck:Leak
   match-leak-kinds: indirect
   ...
   fun:s2n_openssl_compat_rand
   ...
}

{
   ignore_aws3
   Memcheck:Leak
   match-leak-kinds: definite
   ...
   fun:s2n_openssl_compat_rand
   ...
}