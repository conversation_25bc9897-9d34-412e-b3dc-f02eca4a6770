#!/bin/bash

set -ex

help_message="$(basename "$0") [-h] [--unittest] [--coverage] [--valgrind] [--all] [--ci]

where:
    -h|--help           Show this help message.
    --unittest          run unittest.
    --coverage          run code coverage, default will run unittest first.
    --valgrind          run valgrind.
    --all               run all the tests with coverage.
    --ci                Setup ci machine before test."

# default run_test.sh script will run unittest and valgrind.
unittest=true
coverage=false
valgrind=true
ci_test=false

for i in "$@"
do
    case $i in
        -h|--help)
            echo "$help_message"
            exit
            ;;
        --unittest)
            valgrind=false
            shift
            ;;
        --coverage)
            coverage=true
            valgrind=false
            shift
            ;;
        --valgrind)
            unittest=false
            valgrind=true
            shift
            ;;
        --all)
            coverage=true
            shift
            ;;
        --ci)
            ci_test=true
            shift
            ;;
        *)
            echo "unknown option ${i}"
            echo "$help_message"
            exit
            ;;
    esac
done

SOURCE_DIR=`pwd`
BUILD_DIR=$SOURCE_DIR/build

cd $BUILD_DIR/byte

echo "unittest: $unittest"
echo "coverage: $coverage"
echo "valgrind: $valgrind"
echo "ci_test: $ci_test"

function cleanup {
    if $ci_test ; then
        # copy all the logs to byte_test_logs folder.
        cp -rfu $BUILD_DIR/byte/*/*test.* $BUILD_DIR/byte/byte_test_logs/
    fi
}

if $ci_test ; then
    export MY_HOST_IP=`ip -4 address show | grep inet | grep -v 127.0.0 | awk '{print $2}' | cut -d'/' -f1 | grep "172"`
    export MY_HOST_IPV6=::1
    export BYTED_HOST_IP=$MY_HOST_IP
    # all logs folder
    mkdir -p $BUILD_DIR/byte/byte_test_logs
    rm -rf $BUILD_DIR/byte/byte_test_logs
    mkdir $BUILD_DIR/byte/byte_test_logs
    trap cleanup EXIT
fi

if $unittest ; then
    make test ARGS="-V"
fi

if $coverage ; then
    lcov -d $BUILD_DIR/byte -c -o cov_all.info
    lcov -e cov_all.info '*byte/byte/*' -o byte_cov.info
    lcov -r byte_cov.info '*_test.cc' -r byte_cov.info '*proto' -o byte_final.info
fi

if $valgrind ; then
    # params:
    num_workers=$(cat /proc/cpuinfo |grep processor -c)
    # we set number of test workers to be the cpu number;
    # 2 extra workers (used when tests are detected to be slow);
    # 1800 sec as worker timeout;
    # runs every test 3 times (gtest_repeat);
    # "none" means no blocked tests;
    # "-r" means we search all tests under current directory recursively.
    #
    # see scripts argv parsing logic for more details.
    python ../../scripts/parallel_testing.py ${num_workers} 2 1800 3 "none" -r "valgrind_ignore_byte.supp"
fi
