#!/bin/bash

set -x

export http_proxy=10.20.47.147:3128
export https_proxy=10.20.47.147:3128

WORK_DIR=$(dirname $(readlink -f $0))
SOURCE_DIR=${WORK_DIR}/byterpc

BRANCHES=("master")
NOW=`date "+%Y-%m-%d %H:%M:%S"`

for branch in ${BRANCHES[@]}
do
    echo "[${NOW}] Checkout branch ${branch}"
    cd $SOURCE_DIR && git checkout ${branch} && git pull

    echo "[${NOW}] Begin to build byterpc@${branch}"
    rm -rf build && ./build.sh --be --gcov --debug --iobuf_mthreads

    echo "[${NOW}] Begin to run unittest with root permission, otherwise tarzan test will error"
    ./run_test.sh true

    echo "[${NOW}] Begin to generate coverage analysis"
    cd ${WORK_DIR} && ./run_coverage.sh ${SOURCE_DIR} cov_${branch}

    echo "[${NOW}] Begin to copy coverage analysis result to web root"
    rm -rf /var/www/byterpc/coverage/${branch} && mv cov_${branch} /var/www/byterpc/coverage/${branch}
done
