//=============================================================================
// Author: <EMAIL>
// Data: 2017-01-17 16:45
//=============================================================================

#include "baidu/inf/aries/meta_replica/meta_data.h"

#include <string>
#include <algorithm>
#include <stdio.h>
#include <stdint.h>
#include <cassert>
#include <base/time.h>
#include <boost/archive/binary_iarchive.hpp>  
#include <boost/archive/binary_oarchive.hpp>  
#include <boost/archive/archive_exception.hpp>
#include <boost/serialization/map.hpp>
#include <boost/serialization/set.hpp>
#include <boost/serialization/vector.hpp>
#include <boost/serialization/split_member.hpp>
#include <boost/serialization/split_free.hpp>
#include <boost/serialization/throw_exception.hpp>
#include <boost/serialization/unordered_map.hpp>
#include <boost/serialization/shared_ptr.hpp>

#include <fstream>
#include <exception>

#include <baidu/rpc/server.h>
#include <baidu/rpc/channel.h>
#include <base/logging.h>
#include "baidu/inf/aries-api/common/utils.h"
#include "baidu/inf/aries-api/common/types.h"
#include "baidu/inf/aries-api/common/checksum.h"
#include "baidu/inf/aries/meta_replica/common.h"

namespace aries {
namespace meta_replica {

MetaData::MetaData() {}

MetaData::~MetaData() {}

void MetaData::set_space_info(const std::string &space_name,
                                       const aries::pb::SpaceInfo &space_info) {
    common::WriteLockGuard lock_guard(_rw_lock);
    auto space_id = space_info.space_id();

    _space[space_id].ptr = std::shared_ptr<aries::pb::SpaceInfo>(new aries::pb::SpaceInfo);
    _space[space_id].ptr->CopyFrom(space_info);
    _space_name2id[space_name] = space_id;

    LOG(TRACE) << "meta data set space_info for space_id:" << space_id
            << " space_name:" << space_name;

    if (_space_name2id.size() != _space.size()) {
        LOG(NOTICE) << "space name map size is not equal to space map size,"
                << " space_name_map_size:" << _space_name2id.size()
                << " space_map_size:" << _space.size();
    }
}

void MetaData::set_volume_info(const uint64_t volume_id,
                                        const aries::pb::MetaReplica::VolumeInfo &volume_info) {
    common::WriteLockGuard lock_guard(_rw_lock);

    auto space_id = volume_info.space_id();
    // take care!!!
    // should not provided that space sync before volume, volume may sync before space
    // if (_space.find(space_id) == _space.end()) {
         // drop volume, drop space, after master save snapshot
         // set_space_list, set volume, but set_space_list has delete space
    //     return;
    // }

    auto volume_iter = _volume.find(volume_id);
    if (volume_iter == _volume.end()) {
        _space[space_id].volume_id_list.push_back(volume_id);
    }

    auto volume_ptr = std::shared_ptr<aries::pb::MetaReplica::VolumeInfo>(new aries::pb::MetaReplica::VolumeInfo);
    volume_ptr->CopyFrom(volume_info);
    _volume[volume_id] = volume_ptr;
    LOG(TRACE) << "meta data set volume_info for volume_id:" << volume_id;
}

void MetaData::set_node_info(const uint64_t node_addr,
                                      const aries::pb::NodeInfo &node_info) {
    common::WriteLockGuard lock_guard(_rw_lock);

    _location_tree[node_info.az_name()][node_info.idc_name()][node_info.rack_name()].insert(node_addr);

    auto node_info_ptr = std::shared_ptr<aries::pb::NodeInfo>(new aries::pb::NodeInfo);
    node_info_ptr->CopyFrom(node_info);
    _node[node_addr] = node_info_ptr;
    LOG(TRACE) << "meta data set volume_node_addr for node_addr:" 
        << common::endpoint2str(common::int2endpoint(node_addr)).c_str();
}

void MetaData::set_allocator_list(
            const std::vector<aries::pb::AllocatorInfo> &allocator_list) {
    common::WriteLockGuard lock_guard(_rw_lock);
    _allocator = allocator_list;
    LOG(TRACE) << "meta data set allocator list";
}

void MetaData::set_tape_center_list(const std::vector<pb::TapeCenterInfo> &tape_center_list) {
    common::WriteLockGuard lock_guard(_rw_lock);
    _tape_center = tape_center_list;
    LOG(TRACE) << "meta data set tapecenter list";
}

void MetaData::set_tape_node_info(const uint64_t tape_node_addr, const aries::pb::TapeNodeInfo &tape_node_info) {
    common::WriteLockGuard lock_guard(_rw_lock);
    auto tape_node_info_ptr = std::make_shared<pb::TapeNodeInfo>();
    tape_node_info_ptr->CopyFrom(tape_node_info);
    _tape_node[tape_node_addr] = tape_node_info_ptr;
    LOG(TRACE) << "meta data set tape_node_addr for node_addr:" 
        << common::endpoint2str(common::int2endpoint(tape_node_addr)).c_str();
}

void MetaData::set_tinker_list(
            const std::vector<aries::pb::TinkerInfo> &tinker_list) {
    common::WriteLockGuard lock_guard(_rw_lock);
    _tinker = tinker_list;
    LOG(TRACE) << "meta data set tinker list";
}

void MetaData::set_space_id_list(const std::vector<int>& space_id_list) {
    std::map<int, bool> space_id_map;
    for (auto space_id : space_id_list) {
        space_id_map[space_id] = true;
    }

    common::WriteLockGuard lock_guard(_rw_lock);
    auto space_iter = _space.begin();
    while (space_iter != _space.end()) {
        auto space_id = space_iter->first;
        if (space_id_map.find(space_id) != space_id_map.end()) {
            ++space_iter;
            continue;
        }
        LOG(TRACE) << "meta data remove space_info for space_id:" << space_id;
        for (auto volume_id : space_iter->second.volume_id_list) {
            if (_volume.find(volume_id) != _volume.end()) {
                if (_volume[volume_id]->space_id() == space_id) {
                    assert(_volume.erase(volume_id));
                }
            }
        }
        _space.erase(space_iter++);
    }

    _space_name2id.clear();
    for (auto & space_pair : _space) {
        //ptr may be null, set volume first, set space second.
        if (!space_pair.second.ptr) {
            continue;
        }
        auto space_name = space_pair.second.ptr->space_name();
        bool ok = _space_name2id.insert(std::make_pair(space_name, space_pair.first)).second;
        assert(ok);
    }

    LOG(TRACE) << "meta data set space_id list";
}

void MetaData::set_node_addr_list(const std::vector<uint64_t>& node_addr_list) {
    std::map<uint64_t, bool> node_addr_map;
    for (auto node_addr : node_addr_list) {
        node_addr_map[node_addr] = true;
    }
    common::WriteLockGuard lock_guard(_rw_lock);
    for (auto node_iter = _node.begin(); node_iter != _node.end();) {
        auto iter = node_iter++;
        if (node_addr_map.find(iter->first) != node_addr_map.end()) {
            continue;
        }
        auto node_info_ptr = iter->second;
        _location_tree[node_info_ptr->az_name()][node_info_ptr->idc_name()][node_info_ptr->rack_name()]
            .erase(iter->first);
        assert(_node.erase(iter->first));
    }
    LOG(TRACE) << "meta data set node_addr list";
}

void MetaData::list_space(std::vector<std::string> *space_name) {
    common::ReadLockGuard lock_guard(_rw_lock);
    for (auto& space_iter : _space) {
        if (!space_iter.second.ptr) {
            continue;
        }
        space_name->push_back(space_iter.second.ptr->space_name());
    }
}

void MetaData::list_az(std::vector<std::string> *az_name) {
    common::ReadLockGuard lock_guard(_rw_lock);
     for (auto& az_iter : _location_tree) {
         if (az_iter.first.empty()) {
             continue;
         }
         az_name->push_back(az_iter.first);
     }
}

void MetaData::list_idc(std::vector<std::string> *idc_name) {
    common::ReadLockGuard lock_guard(_rw_lock);
    for (auto& az_iter : _location_tree) {
         if (az_iter.first.empty()) {
             continue;
         }
         for (auto& idc_iter : az_iter.second) {
             if (idc_iter.first.empty()) {
                 continue;
             }
             idc_name->push_back(idc_iter.first);
         }
     }
}

AriesErrno MetaData::list_idc_in_az(std::string& az_name, std::vector<std::string> *idc_name) {
    common::ReadLockGuard lock_guard(_rw_lock);
    if (az_name.empty()) {
        LOG(WARNING) << "list node failed due to az name is empty";
        return AIE_FAIL;
    }
    const auto az_iter = _location_tree.find(az_name);
    if (az_iter == _location_tree.end()) {
        return AIE_OK;
    }
    for (auto& idc_iter : az_iter->second) {
        if (idc_iter.first.empty()) {
            continue;
        }
        idc_name->push_back(idc_iter.first);
    }
    return AIE_OK;
}

void MetaData::list_volume(std::vector<uint64_t> *volume_id) {
    common::ReadLockGuard lock_guard(_rw_lock);
    for (auto& volume_iter : _volume) {
        volume_id->push_back(volume_iter.first);
    }
}

void MetaData::list_node(std::vector<uint64_t> *node_addr) {
    common::ReadLockGuard lock_guard(_rw_lock);
    for (auto& node_iter : _node) {
        node_addr->push_back(node_iter.first);
    }
}

void MetaData::list_group(std::vector<std::string> *group_name) {
    common::ReadLockGuard lock_guard(_rw_lock);
    std::set<std::string> group_set;
    for (auto& node_iter : _node) {
        group_set.insert(node_iter.second->group_name());
    }
    for (auto& group : group_set) {
        group_name->push_back(group);
    }
}

AriesErrno MetaData::list_node(const std::string &az_name,
                                  const std::string &idc_name,
                                  const std::string &rack_name,
                                  std::vector<uint64_t> *node_addr) {
    common::ReadLockGuard lock_guard(_rw_lock);

    if (az_name.empty()) {
        LOG(WARNING) << "list node failed due to az name is empty";
        return AIE_FAIL;
    }
    const auto az_iter = _location_tree.find(az_name);
    if (az_iter == _location_tree.end()) {
        return AIE_OK; 
    }

    return list_node_in_az(*az_iter, // an az
                           idc_name, rack_name, node_addr);
}

// no lock
AriesErrno MetaData::list_node_in_az(const LocationTreeType::value_type &az,
                                        const std::string &idc_name,
                                        const std::string &rack_name,
                                        std::vector<uint64_t> *node_addr) {
    const auto& idcs = az.second;
    if (!idc_name.empty()) {
        const auto idc_iter = idcs.find(idc_name);
        AriesErrno ret = AIE_OK;
        if (idc_iter != idcs.end()) {
            ret = list_node_in_idc(*idc_iter, // an idc
                                   rack_name, node_addr);
        }
        return ret;
    }

    auto idc_iter = idcs.begin();
    AriesErrno ret = AIE_OK;
    for (;idc_iter != idcs.end(); ++idc_iter) {
        ret = list_node_in_idc(*idc_iter, // an idc
                               rack_name, node_addr);
        if (ret != AIE_OK) {
            return ret;
        }
    }
    return ret;
}

// no lock
AriesErrno MetaData::list_node_in_idc(const LocationTreeType::mapped_type::value_type &idc,
                                         const std::string &rack_name,
                                         std::vector<uint64_t> *node_addr) {
    const auto& racks = idc.second;
    if (!rack_name.empty()) {
        const auto rack_iter = racks.find(rack_name);
        AriesErrno ret = AIE_OK;
        if (rack_iter != racks.end()) {
            ret = list_node_in_rack(*rack_iter, // a rack
                                    node_addr);
        }
        return ret;
    }

    auto rack_iter = racks.begin();
    AriesErrno ret = AIE_OK;
    for (;rack_iter != racks.end(); ++rack_iter) {
        ret = list_node_in_rack(*rack_iter, // a rack
                                node_addr);
        if (ret != AIE_OK) {
            return ret;
        }
    }
    return ret;
}

// no lock
AriesErrno MetaData::list_node_in_rack(
            const LocationTreeType::mapped_type::mapped_type::value_type &rack,
            std::vector<uint64_t> *node_addr) {
    const auto& nodes = rack.second;
    std::copy(nodes.cbegin(), nodes.cend(),
              std::back_inserter(*node_addr));
    return AIE_OK;
}

void MetaData::list_allocator(
            std::vector<aries::pb::AllocatorInfo> *allocator_list) {
    common::ReadLockGuard lock_guard(_rw_lock);
    *allocator_list = _allocator;
}

void MetaData::list_tinker(
            std::vector<aries::pb::TinkerInfo> *tinker_list) {
    common::ReadLockGuard lock_guard(_rw_lock);
    *tinker_list = _tinker;
}

void MetaData::list_tape_center(std::vector<aries::pb::TapeCenterInfo> *tape_center_list) {
    common::ReadLockGuard lock_guard(_rw_lock);
    *tape_center_list = _tape_center;
}

void MetaData::list_tape_node(std::vector<uint64_t> *tape_node_addr) {
    common::ReadLockGuard lock_guard(_rw_lock);
    for (auto& it : _tape_node) {
        tape_node_addr->push_back(it.first);
    }
}

std::shared_ptr<aries::pb::SpaceInfo> MetaData::get_space_info_ptr(
                                    const std::string &space_name) {
    common::ReadLockGuard lock_guard(_rw_lock);
    auto space_id_iter = _space_name2id.find(space_name);
    if (space_id_iter == _space_name2id.end()) {
        return nullptr;
    }
    auto space_id = space_id_iter->second;

    auto iter = _space.find(space_id);
    if (iter != _space.end()) {
        return iter->second.ptr;
    }
    return nullptr;
}

std::shared_ptr<aries::pb::MetaReplica::VolumeInfo> MetaData::get_volume_info_ptr(const uint64_t volume_id) {
    common::ReadLockGuard lock_guard(_rw_lock);

    auto iter = _volume.find(volume_id);
    if (iter != _volume.end()) {
        return iter->second;
    }
    return nullptr;
}

std::shared_ptr<aries::pb::NodeInfo> MetaData::get_node_info_ptr(const uint64_t node_addr) {
    common::ReadLockGuard lock_guard(_rw_lock);

    auto iter = _node.find(node_addr);
    if (iter != _node.end()) {
        return iter->second;
    }
    return nullptr;
}

std::shared_ptr<aries::pb::TapeNodeInfo> MetaData::get_tape_node_info_ptr(const uint64_t node_addr) {
    common::ReadLockGuard lock_guard(_rw_lock);
    auto iter = _tape_node.find(node_addr);
    if (iter != _tape_node.end()) {
        return iter->second;
    }
    return nullptr;
}

MetaData::MetaData(MetaData &another) {
    // Pay attention to the sequence of locking, relating to MetaData::set_node_info.
    common::ReadLockGuard lock_guard(another._rw_lock);

    _space     = another._space;
    _space_name2id = another._space_name2id;
    _volume    = another._volume;
    _node      = another._node;

    _allocator     = another._allocator;
    _tinker        = another._tinker;
    _location_tree = another._location_tree;
}

void MetaData::clear() {

    _space.clear();
    _volume.clear();
    _node.clear();         
    _allocator.clear();    
    _tinker.clear();
    _location_tree.clear();
}

}} // namespace

namespace boost {
namespace serialization {  

template<class Archive>  
static void save(Archive& ar, const ::google::protobuf::Message& pb, const unsigned int&) {  
    std::string data;
    if (!pb.SerializeToString(&data)) {
        std::string err_msg = "pb SerializeAsString failed, pb2json:";
        err_msg += aries::common::pb2json(pb);
        boost::serialization::throw_exception(
                boost::archive::archive_exception(boost::archive::archive_exception::other_exception,
                err_msg.c_str(), 
                err_msg.c_str())
        );
    }

    int32_t check_sum = aries::common::Crc32CheckSum::compute(data.c_str(), data.size(), 0);
    char* p = (char*)(&check_sum);
    data.append(p, 4);
    ar & data;  
}

template<class Archive>  
static void load(Archive& ar, ::google::protobuf::Message& pb, const unsigned int&) {  
    std::string data;
    ar & data;  

    if (data.size() < 4) {
        boost::serialization::throw_exception(
            boost::archive::archive_exception(boost::archive::archive_exception::other_exception,
            "lack of crc", 
            "lack of crc")
        );
    }
    int32_t check_sum_load = *((int*)(data.c_str() + (data.size() - 4)));
    data.resize(data.size() - 4);
    int32_t check_sum_cal = aries::common::Crc32CheckSum::compute(data.c_str(), data.size(), 0);
    if (check_sum_load != check_sum_cal) { boost::serialization::throw_exception(
            boost::archive::archive_exception(boost::archive::archive_exception::other_exception,
            "checksum error", 
            "checksum error")
        );
    }

    if (!pb.ParseFromString(data)) {
        std::string err_msg = "pb ParseFromString failed, string:";
        err_msg += data;
        boost::serialization::throw_exception(
                boost::archive::archive_exception(boost::archive::archive_exception::other_exception,
                err_msg.c_str(), 
                err_msg.c_str())
        );
    }
}

template<class Archive>
static void pb_serialize(Archive& ar, ::google::protobuf::Message& pb, const unsigned int version) {  
    split_free(ar, pb, version);
}

template<class Archive>
static void serialize(Archive& ar, aries::pb::SpaceInfo& pb, const unsigned int version) {  
    pb_serialize(ar, pb, version);
}
template<class Archive>
static void serialize(Archive& ar, aries::pb::NodeInfo& pb, const unsigned int version) {  
    pb_serialize(ar, pb, version);
}
template<class Archive>
static void serialize(Archive& ar, aries::pb::MetaReplica::VolumeInfo& pb, const unsigned int version) {  
    pb_serialize(ar, pb, version);
}
template<class Archive>
static void serialize(Archive& ar, aries::pb::AllocatorInfo& pb, const unsigned int version) {  
    pb_serialize(ar, pb, version);
}
template<class Archive>
static void serialize(Archive& ar, aries::pb::TinkerInfo& pb, const unsigned int version) {  
    pb_serialize(ar, pb, version);
}

template<class Archive>
static void serialize(Archive& ar, aries::meta_replica::MetaData::SpaceStruct& ss, 
        const unsigned int version) {  
    ar & ss.ptr;
    ar & ss.volume_id_list;
}
}} // namespace 

namespace aries {
namespace meta_replica {

// With 6400000 volume, save snapshot cost 60second, 99% cpu, 1.5GB disk file, can compact to 1.1GB
AriesErrno MetaData::save_snapshot(std::string &path_file_name, const JournalID journal_id) {
    try {

        std::ofstream ofs;

        uint64_t time_now = base::gettimeofday_us();
        std::string tmp_file = 
            base::string_printf("%s.%lu.tmp", path_file_name.data(), time_now);
        ofs.open(tmp_file, std::ios::binary);
        if (!ofs.is_open()) {
            LOG(WARNING) << "open snapshot file failed, file:" << tmp_file; 
            return AIE_FAIL;
        }
        ::boost::archive::binary_oarchive oa(ofs);
        oa & journal_id & _space & _volume & _node & _allocator & _tinker & _location_tree;
        ofs.flush();
        ofs.close();

        // TODO: use the same lib with IO lib
        int ret = ::rename(tmp_file.c_str(), path_file_name.c_str());
        if (ret != 0) {
            LOG(FATAL) << "rename snapshot file failed, tmp_file:" << tmp_file
                    << " file:" << path_file_name;
            return AIE_FAIL;
        }

        LOG(NOTICE) << "save snapshot succeeded, file:" << path_file_name
                    << " journal_id:" << PRINT_JOURNAL_ID(journal_id);
    } catch (std::exception &e) {
        LOG(FATAL) << "save snapshot excpetion:" << e.what();
        return AIE_FAIL;
    }
    return AIE_OK;
}

// With 6400000 volume, load snapshot cost 60second, 99% cpu, 1.5GB disk file, can compact to 1.1GB
AriesErrno MetaData::load_snapshot(std::string &path_file_name, JournalID* journal_id) {
    try {
        std::ifstream ifs;
        ifs.open(path_file_name, std::ios::binary);
        if (!ifs.is_open()) {
            LOG(WARNING) << "open snapshot file failed, file:" << path_file_name; 
            return AIE_FAIL;
        }

        VolumeMap volumes;
        NodeMap nodes;
        ::boost::archive::binary_iarchive ia(ifs);
        ia & (*journal_id) & _space & volumes & nodes & _allocator & _tinker & _location_tree;
        ifs.close();

        //boost::archive library will call rehash method during unordered_map loading but
        //unordered_map will usually use a larger bucket count than rehash parameter which will cause OOM finally.
        for (auto& pair : volumes) {
            _volume.emplace(pair.first, std::move(pair.second));
        }

        for (auto& pair : nodes) {
            _node.emplace(pair.first, std::move(pair.second));
        }

        _space_name2id.clear();
        for (auto& space_iter : _space) {
            //in normal snapshot ptr can't be null.
            assert(space_iter.second.ptr);
            auto space_name = space_iter.second.ptr->space_name();
            auto space_id = space_iter.first;
            _space_name2id[space_name] = space_id;
        }

        LOG(NOTICE) << "load snapshot succeeded, file:" << path_file_name
                    << " journal_id:" << PRINT_JOURNAL_ID(*journal_id);
    } catch (std::exception &e) {
        LOG(FATAL) << "load snapshot excpetion:" << e.what();
        _space.clear();
        _volume.clear();
        _node.clear();
        _allocator.clear();
        _tinker.clear();
        _location_tree.clear();
        _space_name2id.clear();
        return AIE_FAIL;
    }
    return AIE_OK;
}

}
}
