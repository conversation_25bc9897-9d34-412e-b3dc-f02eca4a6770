//=============================================================================
// Author: ya<PERSON><EMAIL>,<EMAIL>
// Data: 2017-01-17 16:45
//=============================================================================

#include "baidu/inf/aries/meta_replica/meta_subscriber.h"
#include "baidu/inf/aries/meta_replica/common.h"

#include <stdint.h>
#include <string.h>
#include <libgen.h>
#include <algorithm>

#include "baidu/inf/aries-api/common/utils.h"
#include "baidu/inf/aries-api/common/rpc_stub.h"
#include <base/logging.h>
#include <base/fast_rand.h>
#include "curl/curl.h"

namespace aries {
namespace meta_replica {

MetaSubscriber::MetaSubscriber() {
    _running         = false;
    _journal_id      = 0;
    _is_very_new     = false;
}

void MetaSubscriber::stop() {
    LOG(WARNING) << "stop meta subscriber";
    _running = false;
    if (_snapshot_thread_id != 0) {
        LOG(WARNING) << "start join snapshot thread";
        pthread_join(_snapshot_thread_id, NULL);
        LOG(WARNING) << "finish join snapshot thread";
    }
    if (_sync_meta_thread_id != 0) {
        LOG(WARNING) << "start join sync meta thread";
        pthread_join(_sync_meta_thread_id, NULL);
        LOG(WARNING) << "finish join sync meta thread";
    }
}

MetaSubscriber::~MetaSubscriber() {
}

AriesErrno MetaSubscriber::init(bool is_not_subscribe) {
    _running = true;

    std::string snapshot_file = FLAGS_meta_replica_snapshot_path_file;
    char *dirc = strndup(snapshot_file.c_str(), snapshot_file.size());
    char *dname = dirname(dirc);
    std::string snapshot_path = std::string(dname);
    free(dirc);
    if (!common::aries_mkdir(snapshot_path.c_str(), true)) {
        LOG(FATAL) << "init snapshot path failed, snapshot_path:" << snapshot_path;
        return AIE_FAIL;
    }

    /* step1 : load snapshot */
    JournalID journal_id = 0;
    AriesErrno ret = _meta.load_snapshot(snapshot_file, &journal_id); 
    if (ret == AIE_OK) {
        _journal_id = journal_id;
    }

    if (is_not_subscribe) {
        if (ret == AIE_OK) {
            LOG(WARNING) << "init meta subscriber succeeded with local snapshot,"
                    << " but will not subcribe from master";
        } else {
            LOG(FATAL) << "init meta subscriber failed, and will not subcribe from master";
        }
        return ret;
    }

    //start sync meta log thread.
    if (pthread_create(&_snapshot_thread_id, NULL, &snapshot_thread_run, (void*)this) != 0) {
        LOG(FATAL) << "start snapshot thread failed";
        return AIE_FAIL;
    }
    //
    if (pthread_create(&_sync_meta_thread_id, NULL, &sync_meta_thread_run, (void*)this) != 0) {
        LOG(FATAL) << "start sync meta thread failed";
        return AIE_FAIL;
    }

    LOG(NOTICE) << "meta subscriber succeeded";

    _is_very_new = false;
    return AIE_OK;
}

void MetaSubscriber::list_space(std::vector<std::string> *space_name) {
    _meta.list_space(space_name);
}

void MetaSubscriber::list_az(std::vector<std::string> *az_name) {
    _meta.list_az(az_name);
}

void MetaSubscriber::list_idc(std::vector<std::string> *idc_name) {
    _meta.list_idc(idc_name);
}

AriesErrno MetaSubscriber::list_idc_in_az(std::string& az_name, std::vector<std::string> *idc_name) {
    return _meta.list_idc_in_az(az_name, idc_name);
}

void MetaSubscriber::list_volume(std::vector<uint64_t> *volume_id) {
    _meta.list_volume(volume_id);
}

void MetaSubscriber::list_group(std::vector<std::string> *group_name) {
    _meta.list_group(group_name);
}

void  MetaSubscriber::list_node(std::vector<uint64_t> *node_addr) {
    _meta.list_node(node_addr);
}

void MetaSubscriber::list_node(const std::string &az_name,
                                     const std::string &idc_name,
                                     const std::string &rack_name,
                                     std::vector<uint64_t> *node_addr) {
    _meta.list_node(az_name, idc_name, rack_name, node_addr);
}

void MetaSubscriber::list_allocator(
                std::vector<aries::pb::AllocatorInfo> *allocator_list) {
    _meta.list_allocator(allocator_list);
};

void MetaSubscriber::list_tinker(
                std::vector<aries::pb::TinkerInfo> *tinker_list) {
    _meta.list_tinker(tinker_list);
};

void MetaSubscriber::list_tape_center(std::vector<aries::pb::TapeCenterInfo> *tape_center_list) {
    _meta.list_tape_center(tape_center_list);
}

void MetaSubscriber::list_tape_node(std::vector<uint64_t> *tape_node_addr) {
    _meta.list_tape_node(tape_node_addr);
}

std::shared_ptr<aries::pb::MetaReplica::VolumeInfo> MetaSubscriber::get_volume_info_ptr(const uint64_t volume_id) {
    return _meta.get_volume_info_ptr(volume_id);
};

std::shared_ptr<aries::pb::NodeInfo> MetaSubscriber::get_node_info_ptr(const uint64_t node_addr) {
    return _meta.get_node_info_ptr(node_addr);
};

AriesErrno MetaSubscriber::get_node_info(const uint64_t node_addr,
                                   aries::pb::NodeInfo *node_info) {
    auto node_info_ptr = get_node_info_ptr(node_addr);
    if (!node_info_ptr) {
        LOG(WARNING) << "get node info failed, node addr:" 
            << common::endpoint2str(common::int2endpoint(node_addr));
        return AIE_NOT_EXIST;
    }
    node_info->CopyFrom(*node_info_ptr);
    return AIE_OK;
};

AriesErrno MetaSubscriber::get_tape_node_info(const uint64_t node_addr, aries::pb::TapeNodeInfo *tape_node_info) {
    auto tape_node_info_ptr = _meta.get_tape_node_info_ptr(node_addr);
    if (!tape_node_info_ptr) {
        LOG(WARNING) << "get tapenode info failed, node addr:" 
            << common::endpoint2str(common::int2endpoint(node_addr));
        return AIE_NOT_EXIST;
    }
    tape_node_info->CopyFrom(*tape_node_info_ptr);
    return AIE_OK;
}

AriesErrno MetaSubscriber::get_vlet_info(const uint64_t volume_id,
                                     const uint32_t shard_index,
                                     aries::pb::MetaReplica::VletInfo *vlet_info) {
    auto volume_info_ptr = get_volume_info_ptr(volume_id);
    if (!volume_info_ptr) {
        LOG(WARNING) << "get vlet info failed, volume not exist. volume_id:"
            << volume_id;
        return AIE_NOT_EXIST;
    }
    for (int i = 0; i < volume_info_ptr->vlet_info_size(); ++i) {
        if (volume_info_ptr->vlet_info(i).shard_index() == shard_index) {
            vlet_info->CopyFrom(volume_info_ptr->vlet_info(i));
            return AIE_OK;
        }
    }
    LOG(WARNING) << "get vlet info failed, shard_index not exist. volume_id:" 
        << volume_id << " shard_index:" << shard_index;
    return AIE_NOT_EXIST;
}

AriesErrno MetaSubscriber::get_volume_info(const uint64_t volume_id,
                                     aries::pb::MetaReplica::VolumeInfo *volume_info,
                                     aries::pb::SpaceInfo *space_info) {
    auto volume_info_ptr = get_volume_info_ptr(volume_id);
    if (!volume_info_ptr) {
        LOG(WARNING) << "get volume info failed, volume_id:" << volume_id;
        return AIE_NOT_EXIST;
    }
    auto space_name = volume_info_ptr->space_name();
    auto space_info_ptr = get_space_info_ptr(space_name);
    if (!space_info_ptr) {
        LOG(WARNING) << "get space info failed, volume_id:" << volume_id
            << " space_name:" << space_name;
        return AIE_NOT_EXIST;
    }
    volume_info->CopyFrom(*volume_info_ptr);
    space_info->CopyFrom(*space_info_ptr);
    return AIE_OK;
}

std::shared_ptr<aries::pb::SpaceInfo> MetaSubscriber::get_space_info_ptr(
        const std::string& space_name) {
    return _meta.get_space_info_ptr(space_name);
}

void MetaSubscriber::journal_process(const std::string &journal) {
    aries::pb::MetaReplica::JournalInfo journal_info;
    if (!journal_info.ParseFromString(journal)) {
        LOG(FATAL) << "journal parse string to pb failed";
        return;
    }
    auto& pb_jid = journal_info.journal_id();
    auto journal_id = make_journal_id(pb_jid.raft_index(),
            pb_jid.snapshot_index(),
            pb_jid.increase_index());
    if (journal_id <= _journal_id) {
        LOG(FATAL) << "meta subscriber get a req_journal_id <= mine, "
            << " req_journal_id:" << common::pb2json(pb_jid)
            << " mine_journal_id:" << PRINT_JOURNAL_ID(_journal_id);
        return;
    }

    _journal_id = journal_id;
    LOG(TRACE) << "update journal_id:" << PRINT_JOURNAL_ID(_journal_id);

    LOG(TRACE) << "meta subscriber process journal: " << common::pb2json(journal_info);

    switch (journal_info.op_code()) {
        case aries::pb::MetaReplica::META_REPLICA_SET_SPACE : {
            const std::string space_name = journal_info.space_info().space_name();
            _meta.set_space_info(space_name, journal_info.space_info());
            break;
        }
        case aries::pb::MetaReplica::META_REPLICA_SET_VOLUME: {
            const uint64_t volume_id = journal_info.volume_info().volume_id();
            _meta.set_volume_info(volume_id, journal_info.volume_info());
            break;
        }
        case aries::pb::MetaReplica::META_REPLICA_SET_NODE: {
            const uint64_t node_addr = journal_info.node_info().node_addr();
            _meta.set_node_info(node_addr, journal_info.node_info());
            break;
        }
        case aries::pb::MetaReplica::META_REPLICA_SET_ALLOCATOR_LIST: {
            std::vector<aries::pb::AllocatorInfo> allocator_list;
            for (int i = 0; i < journal_info.allocator_list_size(); ++i) {
                allocator_list.push_back(journal_info.allocator_list(i));
            }
            _meta.set_allocator_list(allocator_list);
            break;
        }
        case aries::pb::MetaReplica::META_REPLICA_SET_TINKER_LIST: {
            std::vector<aries::pb::TinkerInfo> tinker_list;
            for (int i = 0; i < journal_info.tinker_list_size(); ++i) {
                tinker_list.push_back(journal_info.tinker_list(i));
            }
            _meta.set_tinker_list(tinker_list);
            break;
        }

        case aries::pb::MetaReplica::META_REPLICA_SET_SPACE_ID_LIST: {
            std::vector<int> space_id_list;
            for (int i = 0; i < journal_info.space_id_list_size(); ++i) {
                space_id_list.push_back(journal_info.space_id_list(i));
            }
            _meta.set_space_id_list(space_id_list);
            break;
        }
        case aries::pb::MetaReplica::META_REPLICA_SET_NODE_ADDR_LIST: {
            std::vector<uint64_t> node_addr_list;
            for (int i = 0; i < journal_info.node_addr_list_size(); ++i) {
                node_addr_list.push_back(journal_info.node_addr_list(i));
            }
            _meta.set_node_addr_list(node_addr_list);
            break;
        }
        case aries::pb::MetaReplica::META_REPLICA_SET_TAPE_CENTER_LIST: {
            std::vector<aries::pb::TapeCenterInfo> tape_center_list;
            for (int i = 0; i < journal_info.tape_center_list_size(); ++i) {
                tape_center_list.push_back(journal_info.tape_center_list(i));
            }
            _meta.set_tape_center_list(tape_center_list);
            break;
        }
        case aries::pb::MetaReplica::META_REPLICA_SET_TAPE_NODE: {
            const uint64_t tape_node_addr = journal_info.tape_node_info().node_addr();
            _meta.set_tape_node_info(tape_node_addr, journal_info.tape_node_info());
            break;
        }

        default: {
            LOG(FATAL) << "meta subsriber encounter unknown opcode: " << journal_info.op_code();
            return;
        }
    }
}
void* MetaSubscriber::snapshot_thread_run(void* param) {
    MetaSubscriber* meta_subscriber = (MetaSubscriber*)(param);
    meta_subscriber->do_snapshot();
    return NULL;
}

void MetaSubscriber::save_snapshot() {
    MetaData *copy_meta = NULL;
    JournalID last_id = 0;
    {   // To keep snapshot and journal_id consistent.
        common::ScopedMutexLock scoped_lock(_sub_journal_and_snapshot_mutex);
        LOG(DEBUG) << "begin to save snapshot";
        copy_meta = new MetaData(_meta);
        last_id = _journal_id;
    }

    auto ret = copy_meta->save_snapshot(FLAGS_meta_replica_snapshot_path_file, last_id);
    delete copy_meta;
    if (ret != AIE_OK) {
        LOG(FATAL) << "meta service save snapshot failed";
    }
    LOG(NOTICE) << "meta service save snapshot succeeded";
}

void MetaSubscriber::do_snapshot() {
    const uint32_t min_check_interval_second = 1;
    uint32_t accu_time_second = 0;
    LOG(NOTICE) << "start snapshot routine thread";

    uint32_t check_interval_second = FLAGS_meta_replica_snapshot_interval_second;
    while (_running) {
        if (accu_time_second < check_interval_second) {
            // TODO: to run on bthread, and bthread sleep
            LOG(DEBUG) << "snapshot routine continue wait, accu_time:" << accu_time_second
                       << " check_interval_second:" << check_interval_second;
            bthread_usleep(min_check_interval_second * 1000000); 
            accu_time_second += min_check_interval_second;
            continue;
        }
        save_snapshot();
        accu_time_second = 0;
    }
    //save snapshot when stop.
    save_snapshot();
    LOG(NOTICE) << "finish snapshot routine thread";
}

void* MetaSubscriber::sync_meta_thread_run(void* param) {
    MetaSubscriber* meta_subscriber = (MetaSubscriber*)(param);
    meta_subscriber->do_sync_meta();
    return NULL;
}

AriesErrno MetaSubscriber::talk_with_master(const base::EndPoint& master_addr,
        const JournalID req_journal_id) {

    MetaReplicaStub stub;
    
    SynchronizedClosure closure;
    aries::pb::MetaReplica::JournalRequest request;
    aries::pb::MetaReplica::JournalResponse response;
    RpcCallOptions options;
    options.log_id = ::base::fast_rand();

    auto jid = request.mutable_req_journal_id();
    jid->set_raft_index(journal_id2raft_index(req_journal_id));
    jid->set_snapshot_index(journal_id2snapshot_index(req_journal_id));
    jid->set_increase_index(journal_id2increase_index(req_journal_id));


    stub.get(master_addr, &request, &response, &closure);
    closure.wait();
    AriesErrno ret = (AriesErrno)response.status().code();
    LOG(TRACE) << "meta replica get ret:" << ret
        << " req_journal_id:" << PRINT_JOURNAL_ID(req_journal_id);

    if (ret != AIE_CONTINUE && ret != AIE_OK) {
        LOG(WARNING) << "sync fail with master:" << common::endpoint2str(master_addr).c_str()
            << " log_id:" << options.log_id << " ret:" << ret;
        return ret;
    }
    auto master_start_time = response.start_time();
    if (_master_start_time != master_start_time) {
        LOG(NOTICE) << "meta_replica client change master process to:" 
            << master_start_time << " from old start time:"
            << _master_start_time;

        _journal_id = make_journal_id(journal_id2raft_index(_journal_id), 0, 0);
        _master_start_time = master_start_time;
        return AIE_CONTINUE;
    }

    LOG(TRACE) << "sync with master:" << common::endpoint2str(master_addr).c_str()
        << " log_id:" << options.log_id << " ret:" << ret
        << " journal data size:" << response.journal_data_size();

    for (int i = 0; i < response.journal_data_size(); ++i) {
        common::ScopedMutexLock lock(_sub_journal_and_snapshot_mutex);
        journal_process(response.journal_data(i));
    }
    return ret;
}

void MetaSubscriber::do_sync_meta() {

    while (_running) {
        auto master_addr = get_meta_replica_addr();

        if (master_addr != _master_addr) {

            LOG(NOTICE) << "meta_replica client change master ip to:" 
                << common::endpoint2str(master_addr) << " from old ip:"
                << common::endpoint2str(_master_addr);

            _journal_id = make_journal_id(journal_id2raft_index(_journal_id), 0, 0);
            _master_addr = master_addr;
        }
        auto ret = talk_with_master(_master_addr, _journal_id);

        if (ret == AIE_CONTINUE) {
            continue;
        } else if (ret == AIE_OK && !_is_very_new) {
            //after is very new , save snapshot.
            save_snapshot();
            _is_very_new = true;
        }
        sleep(FLAGS_sync_meta_interval_seconds);
    }
    LOG(NOTICE) << "finish sync meta routine thread";
}

void MetaSubscriber::wait_until_meta_very_new() {
    while (_running) {
        if (_is_very_new) {
            return;
        }
        bthread_usleep(1000000); 
    }
}

}}
