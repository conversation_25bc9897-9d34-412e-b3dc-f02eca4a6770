#include "baidu/inf/aries/meta_replica/src/meta_publisher.h"
#include "baidu/inf/aries/common/proto/error_code.pb.h"
#include "baidu/inf/aries/common/proto/meta_replica.pb.h"
#include <base/logging.h>
#include "baidu/inf/aries/common/utils.h"
#include <stdlib.h>
#include <stdio.h>
#include <gflags/gflags.h>


// ================= Demo see here =========================

// Server end, such Master use MetaPublisher
aries::meta_replica::MetaPublisher  g_meta_publisher;

/**
[Some GFLAG]:
First 2 flags should be set!!
FLAGS_meta_replica_port                        |  Port to listen      
FLAGS_meta_replica_snapshot_path_file          |  <dir>/<filename> to store MetaReplica snapshot

The following 3 flags may use the default value.
FLAGS_meta_replica_sub_journal_interval_second |  Interval to sub updating-meta, default 1 second
FLAGS_meta_replica_snapshot_interval_second    |  Interval to save snapshot, uint: second, default 1 hour
FLAGS_meta_replica_keep_journal_num            |  Num to keep journal in memory for other program to subcribe, default 100000

[Usage]:
1. #include "baidu/inf/aries/meta_replica/src/meta_publisher.h"
2. Just call init();
3. Function didn't change.
**/

uint64_t g_raft_index = 1;
int32_t g_space_id = 1;
std::string new_space_name() {
    char buf[20];
    snprintf(buf, 20, "space%d", g_space_id++);
    return buf;
}

void set_space() {
    {
        aries::pb::SpaceInfo s;
        auto az_name = s.add_az_name();
        *az_name = "az1";
        s.set_vlet_type(aries::VLET_TYPE_LINKED_16G_2M_512K);
        s.set_k(8);
        s.set_n(18);
        s.set_put_quorum(15);
        s.set_delete_quorum(15);
        s.set_allocator_collect_quorum(10);
        g_meta_publisher.set_space_info(new_space_name(), s, g_raft_index++);
        LOG(NOTICE) << "set space1";
    }
    {
        aries::pb::SpaceInfo s;
        auto az_name = s.add_az_name();
        *az_name = "az2";
        s.set_vlet_type(aries::VLET_TYPE_LINKED_16G_2M_512K);
        s.set_k(8);
        s.set_n(18);
        s.set_put_quorum(15);
        s.set_delete_quorum(15);
        s.set_allocator_collect_quorum(10);
        g_meta_publisher.set_space_info(new_space_name(), s, g_raft_index++);
        LOG(NOTICE) << "set space2";
    }
    {
        aries::pb::SpaceInfo s;
        auto az_name = s.add_az_name();
        *az_name = "az2";
        s.set_vlet_type(aries::VLET_TYPE_LINKED_16G_2M_512K);
        s.set_k(8);
        s.set_n(18);
        s.set_put_quorum(15);
        s.set_delete_quorum(15);
        s.set_allocator_collect_quorum(10);
        g_meta_publisher.set_space_info(new_space_name(), s, g_raft_index++);
        LOG(NOTICE) << "set space2";
    }
}

void set_volume() {
    {
        aries::pb::MetaReplica::VolumeInfo v;
        v.set_space_name("space1");
        for (auto i = 0; i < 18; ++i) {
            auto vv = v.add_vlet_info();
            vv->set_node_addr(i);
            vv->set_shard_index(i);
            vv->set_state(aries::VLET_STATE_NORMAL);
        }
        g_meta_publisher.set_volume_info(1, v, g_raft_index);
        LOG(NOTICE) << "set volume1";
    }
    {
        aries::pb::MetaReplica::VolumeInfo v;
        v.set_space_name("space2");
        for (auto i = 0; i < 18; ++i) {
            auto vv = v.add_vlet_info();
            vv->set_node_addr(i);
            vv->set_shard_index(i);
            vv->set_state(aries::VLET_STATE_NORMAL);
        }
        g_meta_publisher.set_volume_info(2, v, g_raft_index);
        LOG(NOTICE) << "set volume2";
    }
    {

        uint64_t r1 = rand();
        uint64_t r2 = rand();

        for (auto i = 0; i < 6400000; ++i) {
            aries::pb::MetaReplica::VolumeInfo v;
            v.set_space_name("space2");
            for (auto i = 0; i < 18; ++i) {
                auto vv = v.add_vlet_info();
                vv->set_node_addr(r1 | (r1 << 32));
                vv->set_shard_index(i);
                vv->set_state(aries::VLET_STATE_NORMAL);

                r1 = r2;
                r2 = rand();
            }

            g_meta_publisher.set_volume_info(i, v, rand());
        }

        LOG(NOTICE) << "set volume2";
    }
}

void set_vlet() {
    {
        aries::pb::MetaReplica::VletInfo v;
        v.set_node_addr(88);
        v.set_shard_index(1);
        v.set_state(aries::VLET_STATE_NORMAL);
        g_meta_publisher.set_vlet_info(1, 8, v, g_raft_index);
        LOG(NOTICE) << "set vlet1";
    }
    {
        aries::pb::MetaReplica::VletInfo v;
        v.set_node_addr(88);
        v.set_shard_index(2);
        v.set_state(aries::VLET_STATE_NORMAL);
        g_meta_publisher.set_vlet_info(2, 8, v, g_raft_index);
        LOG(NOTICE) << "set vlet2";
    }
    {
        aries::pb::MetaReplica::VletInfo v;
        v.set_node_addr(88);
        v.set_shard_index(3);
        v.set_state(aries::VLET_STATE_NORMAL);
        g_meta_publisher.set_vlet_info(2, 8, v, g_raft_index);
        LOG(NOTICE) << "set vlet2";
    }
}

void set_node() {
    {
        aries::pb::NodeInfo n;
        n.set_az_name("az1");
        n.set_idc_name("idc1");
        n.set_rack_name("rack1");
        n.set_group_name("group1");
        n.set_is_alive(true);
        n.set_state(aries::NODE_STATE_NORMAL);
        g_meta_publisher.set_node_info(1, n, g_raft_index);
        LOG(NOTICE) << "set node1";
    }
    {
        aries::pb::NodeInfo n;
        n.set_az_name("az2");
        n.set_idc_name("idc2");
        n.set_rack_name("rack2");
        n.set_group_name("group2");
        n.set_is_alive(true);
        n.set_state(aries::NODE_STATE_NORMAL);
        g_meta_publisher.set_node_info(2, n, g_raft_index);
        LOG(NOTICE) << "set node2";
    }
    {
        aries::pb::NodeInfo n;
        n.set_az_name("az2");
        n.set_idc_name("idc2");
        n.set_rack_name("rack2");
        n.set_group_name("group2");
        n.set_is_alive(true);
        n.set_state(aries::NODE_STATE_NORMAL);
        g_meta_publisher.set_node_info(2, n, g_raft_index);
        LOG(NOTICE) << "set node2";
    }
}

void set_allocator() {
    {
        std::vector<uint64_t> allocator_addr;
        allocator_addr.push_back(1);
        allocator_addr.push_back(2);
        g_meta_publisher.set_allocator_list(allocator_addr, g_raft_index);
        LOG(NOTICE) << "set allocator_addr";
    }
    {
        std::vector<uint64_t> allocator_addr;
        allocator_addr.push_back(1);
        allocator_addr.push_back(2);
        g_meta_publisher.set_allocator_list(allocator_addr, g_raft_index);
        LOG(NOTICE) << "set allocator_addr";
    }
}


volatile bool running = true;

int main() {

    aries::FLAGS_meta_replica_port = 8092;
    aries::FLAGS_meta_replica_snapshot_interval_second = 1800;
    if (g_meta_publisher.init() != aries::AIE_OK) {
        LOG(FATAL) << "meta_publisher init fail";
        return -1;
    }

    set_space();
    sleep(1);
    set_volume();
    sleep(1);
    set_vlet();
    sleep(1);
    set_node();
    sleep(1);
    set_allocator();
    g_meta_publisher.do_save_snapshot();

    getchar();
    getchar();



    return 0;
}
