//=============================================================================
// Author: ya<PERSON><EMAIL>
// Data: 2017-01-17 16:45
//=============================================================================

#include "baidu/inf/aries/meta_replica/common.h"

namespace aries {

DEFINE_int32(meta_replica_port, 0, "MetaReplica Port");
DEFINE_int32(meta_replica_snapshot_interval_second, 60*60, "Snapshot interval time in second");
DEFINE_int32(meta_replica_keep_journal_num_threshold, 1000000, "When exceed, cut into half");
DEFINE_int32(meta_replica_sub_journal_interval_second, 1, "Sub journal interval time in second");
DEFINE_string(meta_replica_snapshot_path_file, "./meta/meta.snapshot", "File path to store meta snapshot");
DEFINE_int32(meta_replica_snapshot_download_speed_limit, 10*1024*1024, "Snapshot download speed limit with bytes in second");
DEFINE_int32(meta_replica_sub_journal_num, 100, "meta replica get journal log num per time");
DEFINE_int32(sync_meta_interval_seconds, 1, "meta replica sync meta interval seconds");

namespace meta_replica {

base::EndPoint get_meta_replica_addr() {
    base::EndPoint master_addr = g_master_tracker->get_master();
    LOG(DEBUG) << "master_tracker, get ip:" << master_addr.ip
               << " port:" << master_addr.port;

    base::EndPoint &meta_replica_addr = master_addr;
    if (FLAGS_meta_replica_port == 0) {
        meta_replica_addr.port += 1;
    } else {
        meta_replica_addr.port = FLAGS_meta_replica_port;
    }
    return meta_replica_addr;
}

}}
