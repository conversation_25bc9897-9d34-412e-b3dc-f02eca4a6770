//=============================================================================
// Author: <EMAIL>
// Data: 2017-01-17 16:45
//=============================================================================

#ifndef BAIDU_INF_ARIES_META_REPLICA_META_SUBSCRIBER_H
#define BAIDU_INF_ARIES_META_REPLICA_META_SUBSCRIBER_H

#include <map>
#include <set>
#include <deque>
#include <atomic>
#include <vector>
#include <string>
#include <thread>
#include <chrono>
#include <utility>
#include <stdint.h>
#include <functional>
#include <atomic>

#include <bthread.h>
#include <gflags/gflags.h>
#include "baidu/inf/aries-api/common/proto/error_code.pb.h"
#include "baidu/inf/aries-api/common/lock.h"
#include "baidu/inf/aries-api/common/rpc_stub.h"
#include "baidu/inf/aries/meta_replica/meta_data.h"

namespace aries {
namespace meta_replica {

class MetaSubscriber {
    public:
        MetaSubscriber();
        virtual ~MetaSubscriber();

        void stop();

        ARIES_VIRTUAL AriesErrno init(bool is_not_subscribe = false);

        ARIES_VIRTUAL void list_space(std::vector<std::string> *space_name);
        ARIES_VIRTUAL void list_az(std::vector<std::string> *az_name);
        ARIES_VIRTUAL void list_idc(std::vector<std::string> *idc_name);
        ARIES_VIRTUAL AriesErrno list_idc_in_az(std::string& az_name, std::vector<std::string> *idc_name);
        ARIES_VIRTUAL void list_volume(std::vector<uint64_t> *volume_id);
        ARIES_VIRTUAL void list_group(std::vector<std::string> *group_name);
        ARIES_VIRTUAL void list_node(std::vector<uint64_t> *node_addr);

        ARIES_VIRTUAL void list_node(const std::string &az_name,
                                           const std::string &idc_name,
                                           const std::string &rack_name,
                                           std::vector<uint64_t> *node_addr);

        ARIES_VIRTUAL void list_allocator(
                            std::vector<aries::pb::AllocatorInfo> *allocator_list);
        ARIES_VIRTUAL void list_tinker(
                            std::vector<aries::pb::TinkerInfo> *tinker_list);

        ARIES_VIRTUAL void list_tape_center(std::vector<aries::pb::TapeCenterInfo> *tape_center_list);

        ARIES_VIRTUAL void list_tape_node(std::vector<uint64_t> *tape_node_addr);

        ARIES_VIRTUAL std::shared_ptr<aries::pb::NodeInfo>
            get_node_info_ptr(const uint64_t node_addr);
        ARIES_VIRTUAL std::shared_ptr<aries::pb::MetaReplica::VolumeInfo>
            get_volume_info_ptr(const uint64_t volume_id);
        ARIES_VIRTUAL std::shared_ptr<aries::pb::SpaceInfo>
            get_space_info_ptr(const std::string& space_name);


        ARIES_VIRTUAL AriesErrno get_volume_info(const uint64_t volume_id,
                                   aries::pb::MetaReplica::VolumeInfo *volume_info,
                                   aries::pb::SpaceInfo *space_info);

        ARIES_VIRTUAL AriesErrno get_node_info(const uint64_t node_addr,
                                 aries::pb::NodeInfo *node_info);

        ARIES_VIRTUAL AriesErrno get_vlet_info(const uint64_t volume_id,
                                 const uint32_t shard_index,
                                 aries::pb::MetaReplica::VletInfo *vlet_info);

        ARIES_VIRTUAL AriesErrno get_tape_node_info(const uint64_t node_addr,
                                 aries::pb::TapeNodeInfo *tape_node_info);

        ARIES_VIRTUAL void wait_until_meta_very_new();

    private:
        static void* snapshot_thread_run(void* param);
        static void* sync_meta_thread_run(void* param);

        void journal_process(const std::string &journal);

        void save_snapshot();
        void do_snapshot();
        AriesErrno talk_with_master(const base::EndPoint& master_addr,
                const JournalID req_journal_id);
        void do_sync_meta();

    private:
        volatile bool _running;
        JournalID _journal_id = 0;
        common::MutexLock _sub_journal_and_snapshot_mutex;
        // AsyncThread: sub journal to update meta_service
        pthread_t _snapshot_thread_id = 0;
        pthread_t _sync_meta_thread_id = 0;
        base::EndPoint _master_addr;

        MetaData   _meta;
        volatile bool _is_very_new;
        uint64_t _master_start_time = 0;
};

}}

#endif
