# BDS 仓库：HDFS & CFS

这是 BDS 团队合一大仓库，HDFS 和 CFS 两个产品代码，以及各个组件都会在这个仓库完成合一。

## 使用规范

- 禁止提交二进制
- MR title和commit message必须符合规范，参见`.codebase/apps.yaml`中的`CommitLint`

## 目录结构
- `client`: BDS Client
- `client/fuse`: BDS FUSE
- `client/cfs-sdk`: CFS SDKv2
- `client/hdfs-sdk`: HDFS SDK

## 编译环境统一

- GCC：

## 三方库使用

- 禁止直接 submodule 三方库源码
- 三方库通过二进制方式引入，bvc clone 下来进行编译

- 三方库仓库：[inf/bds_thirdparty](https://code.byted.org/inf/bds_thirdparty)，三方库可以用 submodule 源码引入依赖，例如 bytes、byterpc 等
- 三方库 SCM：[inf/hdfs/bds_thirdparty_binaries](https://cloud.bytedance.net/scm/detail/429161/versions?x-resource-account=public)  
