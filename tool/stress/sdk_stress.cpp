#include <stdio.h>
#include <string>
#include <getopt.h>
#include <unistd.h>
#include <sys/types.h>
#include <sys/stat.h>
#include <sys/vfs.h>
#include "baidu/rpc/server.h"
#include "baidu/inf/aries-api/common/config.h"
#include <base/fast_rand.h>
#include "gflags/gflags.h"    // DECLARE_string
#include "baidu/rpc/channel.h"
#include <base/crc32c.h>
#include "baidu/inf/aries-api/aries.h"
#include <thread>
#include <atomic>
#include <chrono>
#include <regex>
#include <iostream>
#include <fstream>
#include <functional>
#include <bvar/bvar.h>
#include <base/logging.h>
#include <base/comlog_sink.h>
#include <base/string_printf.h>
#include "baidu/inf/aries-api/common/types.h"
#include "baidu/inf/aries-api/common/config.h"
#include "baidu/inf/aries-api/common/master_tracker.h"
#include "baidu/inf/aries-api/common/common.h"
#include "base/crc32c.h"
#include "baidu/inf/aries/main/main_utils.h"
#include "base/strings/string_util.h"
#include "baidu/inf/aries-api/common/speed_limit.h"

// for asan
//extern "C" const char *__asan_default_options() {
//    return "symbolize=1 detect_stack_use_after_return=1 verbosity=1 abort_on_error=1";
//}

namespace aries {
namespace common {
DECLARE_string(master_address);
DECLARE_int32(refresh_master_interval_second);
DECLARE_int32(heartbeat_interval_second);
}
}

// 1. for init  conf
DEFINE_bool(h, false, "usage info");

DEFINE_string(token, "default_token", "token");

DEFINE_string(space_name, "", "data space name");
DEFINE_string(key, "", "user key");
DEFINE_string(meta, "", "meta data");
DEFINE_string(data, "", "data path");
DEFINE_uint64(data_len, 0, "data length");

// -1=DEBUG 0=TRACE 1=NOTICE 2=WARNING 3=FATAL 4=FATAL
DEFINE_int32(log_level, 0, "min log level");
DEFINE_int32(compress_type, -1, "operation compress type");
DEFINE_double(min_compress_ratio, -1, "operation min compress ratio");
DEFINE_int32(compress_level, -1, "compress level");

DEFINE_int32(local_port, -1, "local_port");

DEFINE_uint64(offset, 0, "range get offset");
DEFINE_uint64(len, 0, "range get length");
DEFINE_int64(blob_len, -1, "range get blob length"); // -1，此时不做range读
DEFINE_uint64(log_id, 0, "operation log id");
DEFINE_string(dump_path, "./put_bid_list.txt", "data space name");

// for proxy
DEFINE_string(master_bns, "", "master server addr");
DEFINE_string(volume_service_bns, "", "BNS address of VolumeService");
DEFINE_string(allocator_bns, "", "BNS address of Allocator");
DEFINE_string(dataagent_bns, "", "BNS address of dataagent");
DEFINE_string(volume_service_load_balancer, "rr", "load balancer of accessing VolumeService");
DEFINE_int32(key_meta_max_size, 2048, "max size of user key and meta");
DEFINE_int32(put_blob_retry_times, 2, "if failed, put blob operation will retry that times");
DEFINE_int32(put_blob_retry_interval_ms, 100, "retry interval after a put blob failure");
DEFINE_int32(allocate_bid_max_tries, 10, "dataagent max try times to allocate bid, default is 10");
DEFINE_int32(remove_retry_times, 2, "if failed, remove operation will retry 2 times");
DEFINE_int32(allocate_bid_timeout_ms, 1000, "max time in millis for allocate bid");
DEFINE_uint64(allocate_prior_threshold, 1024UL * 1024 * 1024 * 1024, "threshold of user freespace diff to trigger allocate priority");
DEFINE_int32(query_meta_timeout_ms, 1000, "query volume service time out");
DEFINE_int32(query_meta_max_tries, 3, "dataagent max try times to query meta, default is 3");
DEFINE_int32(get_shard_min_timeout_ms, 100, "min wait time for getting shard in getting blob");
DEFINE_double(get_shard_timeout_quantile, 80, "percentile latency used for get shard timeout");
DEFINE_bool(recheck_ec, false, "recheck decode EC after encode EC shard");
DEFINE_int32(compress_max_retry, 1, "compress max retry");
DEFINE_int32(allocator_call_timeout_ms, 200, "allocator request timeout in millis");
DEFINE_int32(allocator_refresh_ms, 1000, "interval in millis to refresh allocator list");
DEFINE_bool(recheck_compress, false, "recheck decompressed data after compression");
DEFINE_bool(check_compress_type_info, true, "check meta has compressType field.");
// end proxy conf


// 2. for init env
DEFINE_bool(permit_data_offset_index, true, "fast range get when range get");
// 3. for put / get stress test
DEFINE_uint64(max_blob_num, 50000000, "");
DEFINE_int32(put_thread_num, 1, "");
DEFINE_int32(get_thread_num, 1, "");
// 4. shard
DEFINE_int32(get_len, 0, "for range get");
DEFINE_int32(put_len, 0, "len by 4k");
DEFINE_bool(fast_range_get, false, "fast range get when range get");
DEFINE_string(op, "", "Operation: write/read");

DEFINE_string(get_ioprio, "", "get threads ioprioty, such as 1:1,2:2,3:0, default thread_num:0");
DEFINE_string(put_ioprio, "", "put threads ioprioty, such as 1:1,2:2,3:0, default thread_num:0");


DEFINE_int32(total_duration_s, 36000, "how many seconds total press keep");
DEFINE_int32(one_duration_s, 36000, "how many seconds one press keep");

DEFINE_int32(add_get_qps, 0, "how many qps add after duration seconds");
DEFINE_int32(init_get_qps, 0, "how many qps when begin");

DEFINE_int32(add_put_qps, 0, "how many qps add after duration seconds");
DEFINE_int32(init_put_qps, 0, "how many qps when begin");

DEFINE_bool(enable_token_limit, false, "enable token limit");

void show_help() {
    fprintf(stdout, "Usage:  write, read or test\n"
                    "  --permit_data_offset_index=true      # support fast range get\n"
                    "  --get_ioprio=1:0,2:6,3:6             # thread's get request ioprio, default HIGHEST\n"
                    "  --get_len=4*1024                     # for range get shard, default 0\n"
                    "  --put_ioprio=1:0,2:6,3:6             # thread's put request ioprio, default HIGHEST\n"
                    "  --put_len=256*1024                   # put shard data len, default 0~256KB\n"
                    "  --get_thread_num=1                   # get thread num\n"
                    "  --put_thread_num=1                   # put thread num\n"
                    "  --init_get_qps=100                   # begin qps\n"
                    "  --add_get_qps=3                      # add 3 qps one duration time\n"
                    "  --init_put_qps=100                   # begin qps\n"
                    "  --add_put_qps=3                      # add 3 qps one duration time\n"
                    "  --one_duration_s=60                  # one press stage duration time\n"
                    "  --total_duration_s=60                # total press stages duration time\n"
                    "  --use_us_in_markdown_str=false       # use microseconds in markdown str\n"
                    "Report <NAME_EMAIL>\n\n");
}

class BthreadExecutor {
public:
    using Func = std::function<void()>;

    inline static BthreadExecutor &instance() noexcept {
        static BthreadExecutor bthread_executor;
        return bthread_executor;
    }

    BthreadExecutor() {
        ::bthread_setconcurrency(24);
    }

    virtual ~BthreadExecutor() = default;

    int add(Func func) {
        auto* args = new Func(::std::move(func));
        ::bthread_t th;
        if (0 != ::bthread_start_background(&th, NULL, bthread_run, args)) {
            LOG(WARNING) << "start bthread to execute failed";
            delete args;
            return -1;
        }
        return 0;
    }

    int add_urgent(Func func) {
        auto* args = new Func(::std::move(func));
        ::bthread_t th;
        if (0 != ::bthread_start_urgent(&th, NULL, bthread_run, args)) {
            LOG(WARNING) << "start bthread to execute failed";
            delete args;
            return -1;
        }
        return 0;
    }

private:
    inline static void* bthread_run(void* args) noexcept {
        auto function = reinterpret_cast<Func*>(args);
        (*function)();
        delete function;
        return nullptr;
    }
};


// each stage should last more than 20 seconds
class SelectiveBvarMonitor {
public:
    SelectiveBvarMonitor(): _stop(true), _check_times(0) {

    }
    ~SelectiveBvarMonitor() {
        stop();
    }
    void start() {
        _stop = false;
        _timer_thread = std::thread(&SelectiveBvarMonitor::update, this);
    }
    void stop() {
        _stop = true;
        if (_timer_thread.joinable()) {
            _timer_thread.join();
        }
    }
    void reset() {
        _check_times = 0;
        cpu_recorder.reset();
        mem_recorder.reset();
    }
    void print_result() {
        LOG(WARNING) << "one stage complete, duration_s:" << FLAGS_one_duration_s;
        if (FLAGS_put_thread_num != 0) {
            LOG(WARNING) << "put_len:" << FLAGS_put_len;
        }
        if (FLAGS_get_thread_num != 0) {
            LOG(WARNING) << "get_len:" << FLAGS_get_len;
        }
        if (_check_times != 0) {
            LOG(WARNING) << "avg process cpu usage:" << cpu_recorder.get_value() / _check_times;
            LOG(WARNING) << "avg process mem usage:" << mem_recorder.get_value() / _check_times;
        }
    }


private:
    void update() {
        // skip the first few seconds
        sleep(10);
        check_bvar_by_name();
        while (!_stop) {
            auto start = std::chrono::steady_clock::now();
            collect_bvar_by_name();
            _check_times++;
            std::this_thread::sleep_until(start + std::chrono::seconds(1));
        }
    }

    void check_bvar_by_name() {
        std::vector<std::string> names;
        bvar::Variable::list_exposed(&names);
        std::regex ioutil_regex(R"(datanode_disk[\d]+_ioutil)");
        std::regex riops_regex(R"(datanode_disk[\d]+_physical_read_iops)");
        std::regex rthroughput_regex(R"(datanode_disk[\d]+_physical_read_throughput)");
        std::regex wiops_regex(R"(datanode_disk[\d]+_physical_write_iops)");
        std::regex wthroughput_regex(R"(datanode_disk[\d]+_physical_write_throughput)");
        std::smatch base_match;
        for (const auto& name : names) {
            if (std::regex_match(name, base_match, ioutil_regex)) {
                disk_io_bvar_names["ioutil"].push_back(name);
                continue;
            }
            if (std::regex_match(name, base_match, riops_regex)) {
                disk_io_bvar_names["read_iops"].push_back(name);
                continue;
            }
            if (std::regex_match(name, base_match, rthroughput_regex)) {
                disk_io_bvar_names["read_throughput"].push_back(name);
                continue;
            }
            if (std::regex_match(name, base_match, wiops_regex)) {
                disk_io_bvar_names["write_iops"].push_back(name);
                continue;
            }
            if (std::regex_match(name, base_match, wthroughput_regex)) {
                disk_io_bvar_names["write_throughput"].push_back(name);
                continue;
            }
        }
    }

    void collect_bvar_by_name() {
        cpu_recorder << std::stof(bvar::Variable::describe_exposed("process_cpu_usage"));
        mem_recorder << std::stoll(bvar::Variable::describe_exposed("process_memory_resident"));
        boost::any value;
        for (const auto& kv : disk_io_bvar_names) {
            for (const auto& bvar_name : kv.second) {
                if (bvar::Variable::get_exposed(bvar_name, &value) == 0) {
                    disk_io_recorder[kv.first] << boost::any_cast<int32_t>(value);
                }
            }
        }
    }

    bvar::Adder<double> cpu_recorder;
    bvar::Adder<int64_t> mem_recorder;
    std::map<std::string, bvar::Adder<int64_t>> diskstress_get_recorder;
    std::map<std::string, bvar::Adder<int64_t>> diskstress_put_recorder;
    std::map<std::string, bvar::Adder<int64_t>> diskstress_remove_recorder;
    std::map<std::string, bvar::Adder<int64_t>> datanode_store_read_recorder;
    std::map<std::string, bvar::Adder<int64_t>> datanode_store_write_recorder;
    std::map<std::string, bvar::Adder<int64_t>> disk_io_recorder;
    std::map<std::string, std::vector<std::string>> disk_io_bvar_names;

    std::thread _timer_thread;
    std::atomic<bool> _stop;
    std::atomic<int> _check_times;
};

SelectiveBvarMonitor g_test_bvar_monitor;

typedef __uint128_t BlodId;

class AriesApiOp {
public:
    AriesApiOp() = default;

    ~AriesApiOp() = default;

    int init();

    int put(uint64_t log_id,
            int32_t compress_type,
            float min_compress_ratio,
            int32_t compress_level,
            const std::string &space_name,
            const std::string &key,
            const std::string &meta,
            const std::string &data,
            BlodId &bid);

    int get(uint64_t log_id, BlodId bid, std::string &data);

    int range_get(uint64_t log_id, BlodId bid, int32_t blob_len,
                  int32_t offset, int32_t len, std::string &data);

    int fast_range_get(uint64_t log_id, BlodId bid, int32_t blob_len,
                       int32_t offset, int32_t len, std::string &data);

    int remove(uint64_t log_id, BlodId bid);

private:
    int _base_put(uint64_t log_id,
                  int32_t compress_type,
                  float min_compress_ratio,
                  int32_t compress_level,
                  const std::string &space_name,
                  const std::string &key,
                  const std::string &meta,
                  const std::string &data,
                  BlodId &bid);

    int _base_get(uint64_t log_id, BlodId bid,
                  int32_t blob_len, int32_t offset, int32_t len, bool fast_range_get, std::string &data);

    aries::ConfInfo _conf_info;
    std::string _test_case_name;
    aries::AriesClient *_ariesapi;
};

int AriesApiOp::init() {
    _conf_info.token = FLAGS_token;
    _conf_info.server = std::string(FLAGS_dataagent_bns);
    _conf_info.load_balancer = "la";
    _conf_info.timeout_ms = 100;
    _conf_info.retry_timeout_ms = 50;
    _conf_info.max_retry = 3;
    _conf_info.local_port = FLAGS_local_port;

    //proxy
    // use proxy
    _conf_info.use_proxy_lib = true;
    _conf_info.proxy_conf.proxy_name = "dataproxy";
    _conf_info.proxy_conf.bvar_service_port = 8577;
    _conf_info.proxy_conf.token = FLAGS_token;
    _conf_info.proxy_conf.ms_server = std::string(FLAGS_master_bns);
    _conf_info.proxy_conf.allocator_server = std::string(FLAGS_allocator_bns);
    _conf_info.proxy_conf.vs_server = std::string(FLAGS_volume_service_bns);
    _conf_info.proxy_conf.vs_load_balancer = FLAGS_volume_service_load_balancer;
    _conf_info.proxy_conf.query_meta_max_retry = FLAGS_query_meta_max_tries;
    _conf_info.proxy_conf.query_meta_timeout_ms = FLAGS_query_meta_timeout_ms;
    _conf_info.proxy_conf.connect_timeout_ms = aries::FLAGS_connect_timeout_ms;
    _conf_info.proxy_conf.shard_call_timeout_ms = aries::FLAGS_call_timeout_ms;
    _conf_info.proxy_conf.allocate_bid_max_retry = FLAGS_allocate_bid_max_tries;
    _conf_info.proxy_conf.allocate_prior_threshold = FLAGS_allocate_prior_threshold;
    _conf_info.proxy_conf.allocator_call_timeout_ms = FLAGS_allocator_call_timeout_ms;
    _conf_info.proxy_conf.allocator_refresh_ms = FLAGS_allocator_refresh_ms;
    _conf_info.proxy_conf.remove_max_retry = FLAGS_remove_retry_times;
    _conf_info.proxy_conf.put_blob_max_retry = FLAGS_put_blob_retry_times;
    _conf_info.proxy_conf.put_blob_retry_interval_ms = FLAGS_put_blob_retry_interval_ms;
    _conf_info.proxy_conf.get_shard_min_timeout_ms = FLAGS_get_shard_min_timeout_ms;
    _conf_info.proxy_conf.get_shard_timeout_quantile = FLAGS_get_shard_timeout_quantile;
    _conf_info.proxy_conf.heartbeat_interval_second = aries::common::FLAGS_heartbeat_interval_second;
    _conf_info.proxy_conf.check_compress_type_info = FLAGS_check_compress_type_info;
    _conf_info.proxy_conf.key_meta_max_size = FLAGS_key_meta_max_size;
    _conf_info.proxy_conf.recheck_compress = FLAGS_recheck_compress;
    _conf_info.proxy_conf.recheck_ec = FLAGS_recheck_ec;
    _conf_info.proxy_conf.compress_max_retry = FLAGS_compress_max_retry;
    _conf_info.proxy_conf.cache_space_name = FLAGS_space_name;


    _ariesapi = aries::AriesClient::new_aries_client(_conf_info);
    if (_ariesapi == NULL) {
        return 1;
    } else {
        return 0;
    }
}

int AriesApiOp::_base_put(uint64_t log_id, int32_t compress_type, float min_compress_ratio,
                          int32_t compress_level,
                          const std::string& space_name,
                          const std::string& key, const std::string& meta, const std::string& data, BlodId &bid) {
    int ret = _ariesapi->put(log_id, space_name, key, meta, data, &bid);
    return ret;
}

int AriesApiOp::put(uint64_t log_id, int32_t compress_type, float min_compress_ratio,
                    int32_t compress_level,
                    const std::string& space_name,
                    const std::string& key, const std::string& meta, const std::string& data, BlodId &bid) {
    return _base_put(log_id, compress_type, min_compress_ratio, compress_level, space_name, key, meta, data, bid);
}

int AriesApiOp::_base_get(uint64_t log_id, BlodId bid,
                          int32_t blob_len, int32_t offset, int32_t len, bool fast_range_get, std::string &data) {
    aries::BlobRange blob_range;
    blob_range.offset = offset;
    blob_range.len = len;
    blob_range.blob_len = blob_len;

    aries::BlobMeta blob_meta;

    int ret = -1;
    if (blob_len == -1) {  // read blob
        ret = _ariesapi->get(log_id, bid, &data, &blob_meta);
    } else if (fast_range_get){  // fast range get
        ret = _ariesapi->fast_get(log_id, bid, &blob_range, &data);
    } else {  // range get
        ret = _ariesapi->get(log_id, bid, &blob_range, &data, &blob_meta);
    }

    return ret;
}

int AriesApiOp::get(uint64_t log_id, BlodId bid, std::string &data) {
    return _base_get(log_id, bid, -1, 0, 0, false, data);
}

int AriesApiOp::range_get(uint64_t log_id, BlodId bid, int32_t blob_len,
                          int32_t offset, int32_t len, std::string &data) {
    return _base_get(log_id, bid, blob_len, offset, len , false, data);
}

int AriesApiOp::fast_range_get(uint64_t log_id, BlodId bid, int32_t blob_len,
                               int32_t offset, int32_t len, std::string &data) {
    return _base_get(log_id, bid, blob_len, offset, len , true, data);
}

int AriesApiOp::remove(uint64_t log_id, BlodId bid) {
    int ret = _ariesapi->remove(log_id, bid);
    return ret;
}



AriesApiOp g_client;

// golable info
aries::common::TokenPool g_put_token_pool(0.1, 1);
aries::common::TokenPool g_get_token_pool(0.1, 1);

uint64_t g_blob_num;
std::vector<BlodId> g_blob_ids;
::aries::common::MutexLock g_blob_ids_lock;

struct Option {
    ::aries::Qos priority = ::aries::HIGHEST;
    ::aries::NetworkQos network_qos = ::aries::NORAML;
    // for put
    int32_t put_len = 0;
    // for get
    bool is_fast_range_get = false;
    int32_t get_len = 0;
};

std::vector<Option> g_get_option_vec;
std::vector<Option> g_put_option_vec;

// bvar
std::vector<bvar::LatencyRecorder> g_client_get_latency_vec;
std::vector<bvar::LatencyRecorder> g_client_put_latency_vec;

bvar::LatencyRecorder g_client_get_latency_all("user", "get_latency");
bvar::LatencyRecorder g_client_put_latency_all("user", "put_latency");
::bvar::Adder<size_t> err_get_count;
::bvar::PerSecond<::bvar::Adder<size_t>> err_get_qps(&err_get_count);
::bvar::Adder<size_t> err_put_count;
::bvar::PerSecond<::bvar::Adder<size_t>> err_put_qps(&err_put_count);


bool get_a_blob_id(BlodId* bid) {
    g_blob_ids_lock.lock();
    if (g_blob_ids.size() != 0) {
        auto random = base::fast_rand() % g_blob_ids.size();
        *bid = g_blob_ids[random];
        g_blob_ids_lock.unlock();
        return true;
    } else {
        g_blob_ids_lock.unlock();
        return false;
    }
}

bool is_valid(const std::string &result) {
//    for (auto &c : result) {
//        if (c != 'a') {
//            return false;
//        }
//    }
    return true;
}

int get_blob(Option option, int thread_num, const BlodId& bid) {
    if (!g_get_token_pool.take(1)) {
        bthread_usleep(1000 * 1000);
        return -1;
    }

    uint64_t start = base::gettimeofday_us();
    uint64_t log_id = base::fast_rand();
    int ret = 0;
    std::string data = "";
    if (option.is_fast_range_get) {
        if (FLAGS_blob_len % option.get_len != 0) {
            abort();
        }
        int32_t offset = (base::fast_rand_in<int32_t>(0, (int32_t)(FLAGS_blob_len / option.get_len) - 1) * option.get_len);
        ret = g_client.fast_range_get(log_id, bid, FLAGS_blob_len, offset, option.get_len, data);
    } else {
        ret = g_client.get(log_id, bid, data);
    }

    if (ret == aries::AIE_OK && is_valid(data)) {
        auto cost = base::gettimeofday_us() - start;
        uint64_t vid = bid >> 64;
        uint64_t vbid = bid & ~0L;
        LOG(NOTICE) << "get blob succ:" << vid << " " << vbid;
        g_client_get_latency_all << cost;
        g_client_get_latency_vec[thread_num] << cost;
    } else {
        err_get_count << 1;
        LOG(WARNING) << "get blob fail" << ret;
    }
    return ret;
}

void f_get(int thread_num) {
    std::cout << "start get thread " << thread_num << std::endl;
    while (true) {
        BlodId bid;
        bool succ = get_a_blob_id(&bid);
        if (succ) {
           get_blob(g_get_option_vec[thread_num], thread_num, bid);
        } else {
            bthread_usleep(1000 * 1000);
        }
    }
}

int write_blob(Option option, int thread_num, BlodId& bid) {
    if (g_blob_num >= FLAGS_max_blob_num) {
        LOG(WARNING) << "beyony max num";
        return -1;
    }

    if (!g_put_token_pool.take(1)) {
        LOG(WARNING) << "take token fail";
        bthread_usleep(1000 * 1000);
        return -1;
    }

    if (FLAGS_put_len != FLAGS_blob_len) {
        abort();
    }
    
    uint64_t start = base::gettimeofday_us();

    int shard_len = 0;
    if (option.put_len != 0) {
        shard_len = option.put_len;
    } else {
        abort();
    }
    
    std::string data(shard_len, 'a');
    uint64_t log_id = base::fast_rand();
    int32_t compress_type = 0;
    float min_compress_ratio = 1.01;
    int32_t compress_level = 1;
    std::string space_name = FLAGS_space_name;
    std::string key = "KEY_TEST";
    std::string meta = "KEY_META";

    int ret = g_client.put(log_id, compress_type, min_compress_ratio, compress_level, space_name, key, meta, data, bid);

    if (ret == aries::AIE_OK) {
        auto cost = base::gettimeofday_us() - start;
        uint64_t vid = bid >> 64;
        uint64_t vbid = bid & ~0L;
        LOG(NOTICE) << "put blob succ:" << vid << " " << vbid;
        g_client_put_latency_all << cost;
        g_client_put_latency_vec[thread_num] << cost;
    } else {
        err_put_count << 1;
        LOG(WARNING) << "put blob fail" << ret;
    }

    return ret;
}

bool put_a_blob_id(BlodId bid) {
    if (g_blob_num >= FLAGS_max_blob_num) {
        return false;
    }
    g_blob_ids_lock.lock();
    g_blob_ids.emplace_back(bid);
    g_blob_num++;
    g_blob_ids_lock.unlock();
    return true;
}

void f_put(int thread_num) {
    std::cout << "start put thread " << thread_num << std::endl;
    while (true) {
        BlodId bid;
        int ret = write_blob(g_put_option_vec[thread_num], thread_num, bid);
        if (ret == 0) {
            put_a_blob_id(bid);
        } else {
            bthread_usleep(1000 * 1000);
        }
    }
}

bool dump(const std::string& filename) {
    std::ofstream fs;

    if (filename.empty()) {
        return false;
    }

    fs.open(filename, std::ofstream::out);
    if (!fs.is_open()) {
        std::cout << "Error: can't open file " << filename << "!" << std::endl;
        return false;
    }


    if (!fs.good()) {
        fs.close();
        std::cout << "Error: dumping skip list failed!" << std::endl;
        return false;
    }

    g_blob_ids_lock.lock();
    fs << g_blob_ids.size() << std::endl;

    for (auto &bid : g_blob_ids) {
        uint64_t vid = bid >> 64;
        uint64_t vbid = bid & ~0L;
        fs << vid << "  " << vbid << std::endl;
        if (!fs.good()) {
            g_blob_ids_lock.unlock();
            fs.close();
            std::cout << "Error: dumping list failed!" << std::endl;
            return false;
        }
    }

    g_blob_ids_lock.unlock();
    fs.close();
    return true;
}

bool load(const std::string& filename) {
    std::ifstream fs;
    int bid_num = 0;
    int i = 0;
    int j = 0;

    if (filename.empty()) {
        return false;
    }

    fs.open(filename, std::ifstream::in);
    if (!fs.is_open()) {
        LOG(WARNING) << "Error: can't open file " << filename << "!";
        return false;
    }

    fs >> bid_num;
    if (!fs.good()) {
        fs.close();
        LOG(WARNING) << "Error: loading skip list failed!";
        return false;
    }

    g_blob_ids_lock.lock();
    g_blob_ids.clear();
    for (i = 0; i < bid_num; i++) {
        uint64_t vid = 0;
        uint64_t vbid = 0;

        fs >> vid >> vbid;
        BlodId bid = ((BlodId) vid << 64) | vbid;
        if (!fs.good()) {
            g_blob_ids_lock.unlock();
            fs.close();
            std::cout << "Error: loading skip list failed!" << std::endl;
            return false;
        } else {
            g_blob_ids.push_back(bid);
        }
    }

    g_blob_ids_lock.unlock();
    fs.close();
    return true;
}

void start_timer() {
    g_put_token_pool.start();
    g_get_token_pool.start();

    if (FLAGS_total_duration_s < 30) {
        FLAGS_total_duration_s = 30;
    }

    if (FLAGS_one_duration_s < 1) {
        FLAGS_one_duration_s = 1;
    }

    if (FLAGS_one_duration_s > FLAGS_total_duration_s) {
        FLAGS_one_duration_s = FLAGS_total_duration_s;
    }

    std::thread* timer = new std::thread([]() {
        int times = 0;
        while (true) {
            int put_qps = FLAGS_init_put_qps + FLAGS_add_put_qps * times;
            int get_qps = FLAGS_init_get_qps + FLAGS_add_get_qps * times;
            g_put_token_pool.reset(put_qps, put_qps);
            g_get_token_pool.reset(get_qps, get_qps);
            LOG(WARNING) << "start_timer  " << ", limit put_qps:"
                         << put_qps << " get_qps:" << get_qps;

            sleep(FLAGS_one_duration_s);
            times++;
            LOG(WARNING) << "one duration finished, times:" << times << ", limit put_qps:"
                         << put_qps << " get_qps:" << get_qps;
            g_test_bvar_monitor.stop();
            g_test_bvar_monitor.print_result();
            g_test_bvar_monitor.reset();
            if (times * FLAGS_one_duration_s >= FLAGS_total_duration_s) {
                if (FLAGS_op == "write" || FLAGS_op == "test") {
                    dump(FLAGS_dump_path);
                }

                LOG(WARNING) << "total duration finished, limit put_qps:"
                             << put_qps << " get_qps:" << get_qps;

                // wait async log
                sleep(1);
                _exit(0);
            }
        }
    });
}

void do_read() {
    auto& executor = BthreadExecutor::instance();
    for (int i = 0; i < FLAGS_get_thread_num; ++i) {
        executor.add([i](){
            f_get(i);
        });
    }
}

void do_write() {
    auto& executor = BthreadExecutor::instance();
    for (int i = 0; i < FLAGS_put_thread_num; ++i) {
        executor.add([i](){
            f_put(i);
        });
    }
}

void init_bvar() {
    err_put_qps.expose("user_err_put_qps");
    err_get_qps.expose("user_err_get_qps");

    std::vector<bvar::LatencyRecorder> tmp_get_latency_vec(FLAGS_get_thread_num);
    std::vector<bvar::LatencyRecorder> tmp_put_latency_vec(FLAGS_put_thread_num);

    g_client_get_latency_vec.swap(tmp_get_latency_vec);
    g_client_put_latency_vec.swap(tmp_put_latency_vec);

    for (int i = 0; i < FLAGS_get_thread_num; ++i) {
        std::string bvar_name = base::string_printf("get_thread%d__latency", i + 1);
        g_client_get_latency_vec[i].expose(bvar_name);
    }
    for (int i = 0; i < FLAGS_put_thread_num; ++i) {
        std::string bvar_name = base::string_printf("put_thread%d_latency", i + 1);
        g_client_put_latency_vec[i].expose(bvar_name);
    }
    LOG(TRACE) << "init bvar succ";
}

void parse_parameters() {
    std::vector<Option> tmp_get_ioprio_vec(FLAGS_get_thread_num);
    std::vector<Option> tmp_put_ioprio_vec(FLAGS_put_thread_num);

    g_get_option_vec.swap(tmp_get_ioprio_vec);
    g_put_option_vec.swap(tmp_put_ioprio_vec);
    // parse ioprio parameter
    {
        std::vector<std::string> tmp_get_vec;
        std::vector<std::string> tmp_put_vec;
        std::string split(",");
        Tokenize(FLAGS_get_ioprio, split, &tmp_get_vec);
        Tokenize(FLAGS_put_ioprio, split, &tmp_put_vec);
        std::vector<std::string> tmp_vec;
        split = ":";
        for (auto str : tmp_get_vec) {
            Tokenize(str, split, &tmp_vec);
            int32_t pos = std::stoi(tmp_vec[0]) - 1;
            int32_t value = std::stoi(tmp_vec[1]);
            if (pos < FLAGS_get_thread_num) {
                g_get_option_vec[pos].priority = (aries::Qos)value;
            }
        }
        for (auto str : tmp_put_vec) {
            Tokenize(str, split, &tmp_vec);
            int32_t pos = std::stoi(tmp_vec[0]) - 1;
            int32_t value = std::stoi(tmp_vec[1]);
            if (pos < FLAGS_put_thread_num) {
                g_put_option_vec[pos].priority = (aries::Qos)value;
            }
        }
    }
    for (auto& option : g_get_option_vec) {
        option.get_len = FLAGS_get_len;
        option.is_fast_range_get = FLAGS_fast_range_get;
    }
    for (auto& option : g_put_option_vec) {
        option.put_len = FLAGS_put_len;
    }
    LOG(TRACE) << "init parameters succ";
}

int init_client() {
    int ret = g_client.init();
    if (0 != ret) {
        LOG(TRACE) << "init client fail";
        abort();
    }
    return ret;
}

int init_my_comlog() {
    logging::ComlogSinkOptions options;
    options.async = true;
    options.shorter_log_level = false;
    options.log_dir = "log";
    options.process_name = "blob_presser";
    options.print_vlog_as_warning = false;
    // -1=DEBUG 0=TRACE 1=NOTICE 2=WARNING 3=FATAL 4=FATAL
    options.min_log_level = FLAGS_log_level;
    options.split_type = (logging::ComlogSplitType)1;
    options.quota_size = 20480 * 1024;
    options.quota_day = 1;
    options.quota_hour = 0;
    options.enable_wf_device = true;

    if (logging::ComlogSink::GetInstance()->Setup(&options) != 0) {
        LOG(ERROR) << "fail to setup comlog";
        return -1;
    }
    logging::SetLogSink(logging::ComlogSink::GetInstance());

    return 0;
}

int main(int argc, char* argv[]) {
    // init log
    if (0 != init_my_comlog()) {
        LOG(FATAL) << "fail to start  due to init comlog failed";
        return -1;
    }

    google::ParseCommandLineFlags(&argc, &argv, false);

    SignalHandler::set_module_name("sdk_stress");
    SignalHandler::register_signal_handler();

    baidu::rpc::FLAGS_defer_close_second = 120 * 60;

    g_blob_num = 0;
    g_blob_ids.clear();

    auto op = FLAGS_op;
    if (op == "h" || op == "help" || op.empty()) {
        show_help();
    }  else {
        //init client proxy
        init_client();

        parse_parameters();

        init_bvar();

        start_timer();

        if (op == "write") {
            do_write();
        } else if (op == "read") {
            load(FLAGS_dump_path);
            do_read();
        } else if (op == "test") {
            do_write();
            do_read();
        } else {
            fprintf(stderr, "Unkonwn --op: %s\n", op.c_str());
            show_help();
            return -1;
        }
        while (true) {
            sleep(1);
        }
    }
    return 0;
}
