// Copyright (c) 2019 Baidu.com, Inc. All Rights Reserved.
//
// Author: <PERSON> (guo<PERSON>@baidu.com)
// Date: 2019-04-15

#include <sstream>
#include <json/json.h>
#include <fstream>
#include <time.h>
#include <bitset>
#include <boost/filesystem.hpp>
#include <base/crc32c.h>
#include <base/iobuf.h>
#include <baidu/rpc/policy/hasher.h>
#include <baidu/rpc/controller.h>
#include "base/strings/string_util.h"
#include "base/file_util.h"
#include "baidu/inf/aries-api/aries.h"
#include "baidu/inf/aries-api/common/common.h"
#include "baidu/inf/aries-api/common/types.h"
#include "baidu/inf/aries-api/common/lock.h"
#include "baidu/inf/aries-api/common/rpc_stub.h"
#include "baidu/inf/aries-api/common/utils.h"
#include "baidu/inf/aries-api/common/master_tracker.h"
#include "baidu/inf/aries-api/common/config.h"
#include "baidu/inf/aries-api/common/proto/balancer.pb.h"
#include "baidu/inf/aries-api/common/consistent_hashing_checker.h"
#include "baidu/inf/aries-api/common/erasure_code_factory.h"
#include "baidu/inf/aries/common/threadpool/threadpool_with_task_group.h"
#include "baidu/inf/aries-api/proxy/util.h"
#include "baidu/inf/aries-api/proxy/shard_util.h"
#include "baidu/inf/aries/tool/shell/shell.h"
#include "baidu/rpc/policy/hasher.h"
#include <json/json.h>
#include <fstream>
#include <time.h>
#include <bitset>
#include <boost/filesystem.hpp>

namespace fs = boost::filesystem;


namespace aries {
namespace shell {

DEFINE_string(token, "default_token", "cds admin token");
DEFINE_bool(print_msg, true, "print msg as pb");
DEFINE_bool(print_json, false, "print msg as json");
DEFINE_string(node_addr, "", "the node addr: ********:8000");
DEFINE_uint64(volume_id, 0, "the volume id to operate");
DEFINE_uint64(vbid, 0, "the vbid to operate");
DEFINE_string(bid, "", "the bid in string format");
DEFINE_int32(shard_index, -1, "the shard id to operate");
DEFINE_bool(save_shards, false, "Whether save shards when get blob");
DEFINE_bool(show_sep, true, "Whether show ther seperator");
DEFINE_uint64(log_id, UINT64_MAX, "log id for cli command");
DEFINE_string(volume_service_address, "bns://", "volume service address");
DEFINE_string(file_path, "", "a file include some info for command");
DEFINE_int32(thread_num, 1, "thread num");
DEFINE_string(shard_indexs, "", "shard index to get data");
DEFINE_string(space_name, "", "space name");
DEFINE_string(dir_path, "", "dir path");
DEFINE_int32(n, 0, "n");
DEFINE_int32(k, 0, "k");
DEFINE_int32(blob_len, 0, "blob_len");

static void print_msg(const google::protobuf::Message& msg, FILE* out) {
    // can also try "PrintToString()"
    std::string msg_str = msg.DebugString();
    if (FLAGS_print_json) {
        std::string json;
        std::string error;
        bool ok = common::pb2json(msg, &json, &error);
        if (ok) {
            fprintf(out, "%s\n", json.c_str());
        } else {
            fprintf(out, "%s\n", error.c_str());
        }
    } else if (FLAGS_print_msg) {
        fprintf(out, "%s\n", msg_str.c_str());
    }
}

#define ARIES_SHELL_CHECK_TOKEN() \
    if (FLAGS_token.empty()) { \
        LOG(WARNING) << __FUNCTION__ << ": token is empty"; \
        return -1; \
    }

#define ARIES_SHELL_CHECK_STRING(VAR) \
    if (FLAGS_##VAR.empty()) { \
        LOG(WARNING) << __FUNCTION__ << ": "#VAR" is empty"; \
        return -1; \
    }

#define ARIES_SHELL_CHECK_INT_LE_0(VAR) \
    if (FLAGS_##VAR <= 0) { \
        LOG(WARNING) << __FUNCTION__ << ": "#VAR" " << FLAGS_##VAR << " is invalid"; \
        return -1; \
    }

#define ARIES_SHELL_CHECK_INT_LT_0(VAR) \
    if (FLAGS_##VAR < 0) { \
        LOG(WARNING) << __FUNCTION__ << ": "#VAR" " << FLAGS_##VAR << " is invalid"; \
        return -1; \
    }

#define ARIES_SHELL_CHECK_ADDR(VAR) \
    ARIES_SHELL_CHECK_STRING(VAR); \
    base::EndPoint VAR; \
    if (base::str2endpoint(FLAGS_##VAR.c_str(), &VAR)) { \
        LOG(WARNING) << __FUNCTION__ << ": "#VAR" " << FLAGS_##VAR << " is invalid"; \
        return -1; \
    }

#define SHELL_CHECK_AND_PRINT_STATUS() \
    if (need_print_stderr(out)) { \
        print_msg(response.status(), stderr); \
    } \
    if (response.status().code() != 0) { \
        return response.status().code(); \
    }

#define SHELL_CHECK_AND_PRINT_JSON() \
    if (FLAGS_print_json) { \
        print_msg(response, out); \
        return response.status().code(); \
    }

#define SHELL_CHECK_AND_PRINT_CURSOR_TABLE_HEADER() \
    if (need_print_stderr(out)) { \
        fprintf(stderr, "Results of %s:\n", response.GetTypeName().c_str()); \
    }

#define SHELL_CHECK_AND_SET(VAR, VAL) \
    if (google::GetCommandLineFlagInfoOrDie(#VAR).is_default) { \
        FLAGS_##VAR = VAL; \
    }

static bool need_print_stderr(FILE* out) {
    return isatty(fileno(stderr));
}

static bool need_show_sep(FILE* out) {
    return isatty(fileno(out)) ? FLAGS_show_sep : false;
}

void ShellCmdRegistry::init() {
    // register classes
    register_class("blob", CMD_CLASS_BLOB);
    register_class("space", CMD_CLASS_SPACE);

    // register commands
    register_command("get_blob", CMD_CLASS_SHOW | CMD_CLASS_BLOB, Shell::get_blob,
                    {"--volume_id=1234",
                     "--vbid=5678",
                     "--save_shards=false",});

    register_command("remove_blob", CMD_CLASS_SHOW | CMD_CLASS_BLOB, Shell::remove_blob,
                    {"--volume_id=1234",
                     "--vbid=5678",});

    register_command("check_volume_hole_size", CMD_CLASS_SHOW | CMD_CLASS_SPACE, Shell::check_volume_hole_size,
                    {"--space_name=blob_a",
                     "--thread_num=1(optional)",});

    register_command("check_blob", CMD_CLASS_SHOW | CMD_CLASS_BLOB, Shell::check_blob,
                    {"--volume_id=1234",
                     "--vbid=5678",
                     "--save_shards=false",
                     "--thread_num=1(optional)",
                     "--file_path(optional)",});

    register_command("repair_blob", CMD_CLASS_SHOW | CMD_CLASS_BLOB, Shell::repair_blob,
                    {"--volume_id=1234",
                     "--vbid=5678",
                     "--shard_indexs=0,1,2,3,4,5,6,7,8",
                     "--thread_num=1(optional)",
                     "--file_path(optional)",});
    register_command("local_repair_blob", CMD_CLASS_SHOW | CMD_CLASS_BLOB, Shell::local_repair_blob,
                    {"--dir_path=(contains all shards, each shard name is vlet shard index)",
                     "--n=6",
                     "--k=2",
                     "--blob_len=2000",});
    register_command("remove_append_orphaned_shards", CMD_CLASS_SHOW | CMD_CLASS_BLOB, Shell::remove_append_orphaned_shards,
                     {"--dir_path=(contains all shards, each shard name is vlet shard index)",
                     "--thread_num=2(optional)",
                     "--file_path(optional)",});
}

baidu::rpc::Channel g_vs_channel;

int Shell::init() {
    if (0 != common::start_master_tracker(base::EndPoint(), FLAGS_token)) {
        LOG(WARNING) << "fail to start master tracker with --master_address="
                << common::FLAGS_master_address;
        return -1;
    }
    if (FLAGS_log_id == UINT64_MAX) {
        FLAGS_log_id = base::fast_rand();
    }
    common::ErasureCodeFactory::init();
    // vs_channel
    baidu::rpc::ChannelOptions options;
    options.timeout_ms = 500;
    options.backup_request_ms = 500 / 2;
    options.max_retry = 3;
    int ret = g_vs_channel.Init(FLAGS_volume_service_address.c_str(), "la", &options);
    if (ret != 0) {
        LOG(WARNING) << "init volume service channel failed: " << ret;
        return -1;
    }
    return 0;
}

static int show_volume(FILE* out) {
    ARIES_SHELL_CHECK_TOKEN();
    ARIES_SHELL_CHECK_INT_LE_0(volume_id);

    aries::pb::ShowVolumeResponse response;
    aries::pb::ShowVolumeRequest request;
    request.set_token(FLAGS_token);
    request.set_volume_id(FLAGS_volume_id);
    SHELL_MASTER_RPC_CALL(show_volume);

    SHELL_CHECK_AND_PRINT_JSON();
    SHELL_CHECK_AND_PRINT_STATUS();
    return 0;
}

static std::string gen_shard_file_name(int shard_index, int n) {
    if (n <= 10) {
        return std::to_string(shard_index);
    } else {
        char buf[4] = {0};
        snprintf(buf, 3, "%02d", shard_index);
        return std::string(buf);
    }
}

static bool write_to_file(const std::string& dir, const std::string& file, const char* data, int size) {
    int ret = ::mkdir(dir.c_str(), 0755);
    if (ret != 0 && errno != EEXIST) {
        LOG(WARNING) << "mkdir failed, dir:" << dir << " errno:" << errno;
        return false;
    }
    std::string path = dir + "/" + file;
    int fd = ::open(path.c_str(), O_CREAT | O_TRUNC | O_RDWR, 0644);
    if (fd == -1) {
        LOG(WARNING) << "open file failed, path:" << path << " errno:" << errno;
        return false;
    }
    int nw = ::write(fd, data, size);
    if (nw != size) {
        LOG(WARNING) << "write file failed, path:" << path << " size:" << size
                << " written:" << nw << " errno:" << errno;
        close(fd);
        return false;
    }
    close(fd);
    return true;
}

struct GetShardsMetaContext {
    struct ShardCtx {
        base::EndPoint addr;
        aries::pb::ShardGetRequest* request;
        aries::pb::ShardGetResponse* response;
        aries::pb::VletInfo* vlet_info;
        std::string data;
        ShardCtx() : request(NULL), response(NULL), vlet_info(NULL) {}
        ~ShardCtx() { delete request; delete response; delete vlet_info; }
    };
    common::MutexLock mutex;
    std::shared_ptr<common::SyncPoint> sync_point;
    std::map<uint32_t, std::shared_ptr<ShardCtx>> shards; // shard_index -> shard_ctx
    GetShardsMetaContext() {}
    ~GetShardsMetaContext() {
        for (auto & pair : shards) { pair.second.reset(); }
        sync_point.reset();
    }
};

struct GetShardsContext {
    struct ShardCtx {
        base::EndPoint addr;
        aries::pb::ShardGetRequest* request;
        aries::pb::ShardGetResponse* response;
        baidu::rpc::Controller cntl;
        aries::pb::volumeservice::VletDetail* vlet_info;
        std::string data;
        ShardCtx() : request(NULL), response(NULL), vlet_info(NULL) {}
        ~ShardCtx() { delete request; delete response; delete vlet_info; }
    };
    common::MutexLock mutex;
    std::shared_ptr<common::SyncPoint> sync_point;
    std::map<uint32_t, std::shared_ptr<ShardCtx>> shards; // shard_index -> shard_ctx
    GetShardsContext() {}
    ~GetShardsContext() {
        for (auto & pair : shards) { pair.second.reset(); }
        sync_point.reset();
    }
};

struct CheckBlobResult {
    std::map<int, base::EndPoint> shard_addrs;
    std::vector<int> right_index_vec;
    std::vector<int> error_index_vec;
    std::vector<int> null_index_vec;
};

void get_shards_meta_done(aries::pb::ShardGetRequest* request,
                          aries::pb::ShardGetResponse* response,
                          GetShardsMetaContext* context) {
    {
        common::ScopedMutexLock lock(context->mutex);
    }
    context->sync_point->signal();
}

int Shell::get_blob(FILE* out) {
    ARIES_SHELL_CHECK_TOKEN();
    ARIES_SHELL_CHECK_INT_LE_0(volume_id);
    ARIES_SHELL_CHECK_INT_LE_0(vbid);

    aries::pb::ShowVolumeResponse response;
    aries::pb::ShowVolumeRequest request;
    request.set_token(FLAGS_token);
    request.set_volume_id(FLAGS_volume_id);
    SHELL_MASTER_RPC_CALL(show_volume);

    if (response.status().code() != 0) {
        fprintf(stderr, "show volume failed, vid:%lu code:%d errmsg:%s\n",
                FLAGS_volume_id, response.status().code(), response.status().msg().c_str());
        return response.status().code();
    }

    GetShardsMetaContext context;
    std::map<int, char*> enc_data;
    uint32_t blob_len = 0;
    uint32_t blob_crc = 0;
    uint32_t shard_len = 0;
    std::string dir = std::to_string(FLAGS_volume_id) + "_" + std::to_string(FLAGS_vbid);
    pb::ShardMeta shard_meta;

    auto & volume = response.volume();

    for (int i = 0; i < volume.vlets_size(); ++i) {
        auto & vlet = volume.vlets(i);
        if (vlet.node_addr() == 0) {
            continue;
        }
        base::EndPoint addr = common::int2endpoint(vlet.node_addr());
        uint32_t shard_index = vlet.shard_index();
        auto shardctx = std::shared_ptr<GetShardsMetaContext::ShardCtx>(new GetShardsMetaContext::ShardCtx);
        bool ok = context.shards.insert(std::make_pair(shard_index, shardctx)).second;
        assert(ok);
        shardctx->addr = addr;
        shardctx->vlet_info = new aries::pb::VletInfo;
        shardctx->vlet_info->CopyFrom(vlet);
        shardctx->response = new aries::pb::ShardGetResponse;
        shardctx->request = new aries::pb::ShardGetRequest;
        shardctx->request->set_token(FLAGS_token);
        shardctx->request->set_volume_id(FLAGS_volume_id);
        shardctx->request->set_vbid(FLAGS_vbid);
        shardctx->request->set_shard_index(shard_index);
        shardctx->request->set_need_meta(true);
        shardctx->request->set_need_data(true);

#if defined(_UNITTEST) || defined(_UNIT_TEST)
        base::EndPoint data_addr = addr;
#else
        base::EndPoint data_addr = common::get_data_service_addr(addr);
#endif

        baidu::rpc::Controller cntl;
        cntl.set_log_id(FLAGS_log_id);
        cntl.set_timeout_ms(FLAGS_call_timeout_ms);
        baidu::rpc::ChannelOptions options;
        options.timeout_ms = FLAGS_call_timeout_ms;
        options.connect_timeout_ms = FLAGS_connect_timeout_ms;
        baidu::rpc::Channel channel;
        int ret = channel.Init(data_addr, &options);
        if (ret != 0) {
            LOG(WARNING) << std::dec << "init channel failed, shard_index:" << shard_index;
            continue;
        } else {
            LOG(NOTICE) << std::dec << "will get shard from " << common::endpoint2str(data_addr)
                    << " vid:" << FLAGS_volume_id << " vbid:" << FLAGS_vbid
                    << " shard_index:" << shard_index;
        }

        aries::pb::DataNodeDataService_Stub stub(&channel);
        SynchronizedClosure closure;
        stub.get(&cntl, shardctx->request, shardctx->response, &closure);
        closure.wait();
        const auto & status = shardctx->response->status();
        if (status.code() != AIE_OK) {
            LOG(WARNING) << "get shard failed from addr "
                         << common::endpoint2str(addr)
                         << " vid:" << FLAGS_volume_id << " vbid:" << FLAGS_vbid
                         << " shard_index:" << shard_index
                         << " error:" << status.code() << " (" << status.msg() << ")";
            continue;
        }
        if (!cntl.response_attachment().empty()) {
            shardctx->data = cntl.response_attachment().to_string();
            std::string file = gen_shard_file_name(shard_index, volume.n());
            if (FLAGS_save_shards && !write_to_file(dir, file, shardctx->data.data(), shardctx->data.size())) {
                LOG(NOTICE) << "write shard data to file failed,"
                        << " vid:" << FLAGS_volume_id << " vbid:" << FLAGS_vbid
                        << " shard_index:" << shard_index;
            }

            uint32_t checksum = base::crc32c::Value(shardctx->data.c_str(), shardctx->data.size());
            if (checksum != shardctx->response->shard_meta().shard_crc()) {
                LOG(WARNING) << "shard data crc error, "
                        << " vid:" << FLAGS_volume_id << " vbid:" << FLAGS_vbid
                        << " shard_index:" << shardctx->request->shard_index()
                        << " expect_crc:" << shardctx->response->shard_meta().shard_crc()
                        << " actual_crc:" << checksum;
                std::string file = gen_shard_file_name(shard_index, volume.n());;
                write_to_file(dir, file + ".error", shardctx->data.data(), shardctx->data.size());
            } else {
                if (i == 0) {
                    shard_meta.CopyFrom(shardctx->response->shard_meta());
                }
                char *buf = new char[shardctx->data.size()];
                memcpy(buf, shardctx->data.data(), shardctx->data.size());
                enc_data[shard_index] = buf;
                if (blob_len == 0) {
                    blob_len = shardctx->response->shard_meta().blob_len();
                } else if (blob_len != shardctx->response->shard_meta().blob_len()) {
                    LOG(FATAL) << "blob len not match, expect_blob_len:" << blob_len
                            << " curr_blob_len:" << shardctx->response->shard_meta().blob_len()
                            << " curr_shard_index:" << shard_index;
                    return -1;
                }
                if (blob_crc == 0) {
                    blob_crc = shardctx->response->shard_meta().blob_crc();
                } else if (blob_crc != shardctx->response->shard_meta().blob_crc()) {
                    LOG(FATAL) << "blob crc not match, expect_blob_crc:" << blob_crc
                            << " curr_blob_crc:" << shardctx->response->shard_meta().blob_crc()
                            << " curr_shard_index:" << shard_index;
                    return -1;
                }
                if (shard_len == 0) {
                    shard_len = shardctx->response->shard_meta().shard_len();
                } else if (shard_len != shardctx->response->shard_meta().shard_len()) {
                    LOG(FATAL) << "shard len not match, expect_shard_len:" << shard_len
                            << " curr_shard_len:" << shardctx->response->shard_meta().shard_len()
                            << " curr_shard_index:" << shard_index;
                    return -1;
                }
            }
        } else {
            assert(false);
        }
    }

    if (shard_len == 0) {
        LOG(WARNING) << "shard_len is 0";
        return 0;
    }

    ECOption eco;
    eco.type = (aries::ECType)volume.ec_type();
    eco.param.k = volume.k();
    eco.param.n = volume.n();
    common::ECPtr ec = common::ErasureCodeFactory::get_erasure_code(eco);
    char* final_result = new char[volume.n() * shard_len];
    int ret = ec->decode(enc_data, final_result, shard_len);

    write_to_file(dir, std::string("data"), final_result, blob_len);
    uint32_t new_crc = base::crc32c::Value(final_result, blob_len);
    if (new_crc != blob_crc) {
        LOG(FATAL) << "blob crc error, expect_crc:" << blob_crc << " actual_crc:" << new_crc;
        fprintf(stderr, "blob crc error, vid:%lu vbid:%lu expect_crc:%u actual_crc:%u\n",
                FLAGS_volume_id, FLAGS_vbid, blob_crc, new_crc);
        write_to_file(dir, std::string("data.error"), final_result, blob_len);
        return -1;
    } else {
        LOG(NOTICE) << "blob crc right!";
    }
    for (auto i = 0; i < (int)volume.n(); ++i) { enc_data[i] = NULL; }

    for (int i = 0; i < (int)volume.n(); ++i) {
        if (enc_data[i] == NULL) { enc_data[i] = new char[shard_len]; }
        memset(enc_data[i], 0, shard_len);
    }
    int save_shard_num = blob_len / shard_len;
    int end_shard_size = blob_len % shard_len;
    int32_t align_end_shards_size = (eco.param.k  - save_shard_num) * shard_len;
    char* last_alloc_shards = NULL;
    if (align_end_shards_size > 0) {
        last_alloc_shards = new char[align_end_shards_size];
        memset(last_alloc_shards, 0, align_end_shards_size);
        if (end_shard_size != 0) {
            memcpy(last_alloc_shards, 
                final_result + save_shard_num * shard_len, end_shard_size);
        }
        enc_data[eco.param.k - 1] = last_alloc_shards;
    }
    for (int i = 0; i < eco.param.k; ++i) {
        if (i < save_shard_num) {
            enc_data[i] = final_result + i * shard_len;
        } else {
            enc_data[i] = last_alloc_shards + (i - save_shard_num) * shard_len;
        }
    }
    ec->encode(final_result, eco.param.k, last_alloc_shards, enc_data, shard_len);

    for (int i = 0; i < (int)volume.n(); ++i) {
        const std::string& data = context.shards[i]->data;
        if (data.size() == 0) {
            LOG(WARNING) << "shard data size is zero";
            continue;
        }
        int ret = memcmp(enc_data[i], data.data(), data.size());
        if (ret != 0) {
            LOG(WARNING) << "shard data error,"
                    << " vid:" << FLAGS_volume_id << " vbid:" << FLAGS_vbid
                    << " shard_index:" << i;
            fprintf(stderr, "shard data error, vid:%lu vbid:%lu shard_index:%d\n",
                    FLAGS_volume_id, FLAGS_vbid, i);
            std::string file = gen_shard_file_name(i, volume.n());;
            if (i > (int)volume.k()) {
                write_to_file(dir, file + ".error", enc_data[i], shard_len);
                if (FLAGS_save_shards) {
                    write_to_file(dir, file, data.data(), data.size());
                }
            }
        }
    }

    //decompress
    if (shard_meta.compress_type() != 0) {
        GetBlobInfo blob_info;
        blob_info.log_id = 0;
        blob_info.origin_blob_len = shard_meta.origin_blob_len();
        blob_info.origin_blob_crc = shard_meta.origin_blob_crc();
        blob_info.data = final_result;
        blob_info.blob_len = shard_meta.blob_len();
        base::Status compress_status;
        auto origin_len = shard_meta.origin_blob_len();
        // decompress option init
        RpcCompressOption compress_option;
        compress_option.log_id = 0;
        compress_option.compress_type = shard_meta.compress_type();
        compress_option.compress_level = 0;
        //compress_option.token = _conf.token.c_str();
        //compress_option.compress_channel = &_compress_channel;
        //compress_option.compress_max_retry = _conf.compress_max_retry;
        //compress_option.timeout_ms = blob_info->cost_tracker->calc_timeout_ms(blob_info->timeout_ms);
        compress_option.origin_data_len = origin_len;
        compress_option.priority = PriorityLevel::PL_HIGH;
        compress_status = decompress(&compress_option, &blob_info);
        if (!compress_status.ok()) {
            LOG(WARNING) << "decompress failed";
        } else {
            LOG(NOTICE) << "decompress data succ";
            write_to_file(dir, std::string("data.decompress"), blob_info.data, origin_len);
        }

    } else {
        LOG(WARNING) << "data is not compressed";
    }

    return 0;
}

int Shell::remove_blob(FILE* out) {
    ARIES_SHELL_CHECK_TOKEN();
    ARIES_SHELL_CHECK_INT_LE_0(volume_id);
    ARIES_SHELL_CHECK_INT_LE_0(vbid);

    aries::ConfInfo options;

    options.token = FLAGS_token;
    options.timeout_ms = 10000;
    options.max_retry = 2;
    options.use_proxy_lib = true;
    options.proxy_conf.token = FLAGS_token;
    options.proxy_conf.ms_server = aries::common::FLAGS_master_address;
    options.proxy_conf.vs_server = FLAGS_volume_service_address;
    options.proxy_conf.vs_load_balancer = "la";
    options.proxy_conf.bvar_service_port = 59400;
    options.proxy_conf.query_meta_timeout_ms = 5000;
    options.proxy_conf.get_shard_min_timeout_ms = 500;

    std::unique_ptr<aries::AriesClient> client; 
    client.reset(aries::AriesClient::new_aries_client(options));
    aries::RequestOption request_option;
    request_option.log_id = FLAGS_log_id;
    request_option.timeout_ms = 6000;
    request_option.retry_timeout_ms = 1000;
    __uint128_t bid = make_bid(FLAGS_volume_id, FLAGS_vbid);
    std::string data;
    aries::BlobMeta meta;
    int ret = client->get(request_option, bid, NULL, &data, &meta);
    
    if (ret == aries::AIE_BLOB_NOT_EXIST) {
        // not exist
        int ret = client->remove(request_option, bid);
        if (ret == 0) {
            LOG(TRACE) << "volume_id:" << FLAGS_volume_id << " vbid:" << FLAGS_vbid
                << " remove blob successed";
        } else {
            LOG(TRACE) << "remove blob failed, return code:" << ret;
        }
    } else {
        LOG(TRACE) << "volume_id:" << FLAGS_volume_id << " vbid:" << FLAGS_vbid
            << "blob may be exist, can not remove it, return code:" << ret;
    }
    return 0;
}

void get_shard_done(aries::pb::ShardGetRequest* request, 
                    aries::pb::ShardGetResponse* response, 
                    GetShardsContext* context) {
    {
        common::ScopedMutexLock lock(context->mutex);
    }
    context->sync_point->signal();
}

int get_volume(const aries::pb::volumeservice::GetVolumeInfoRequest& request, 
           aries::pb::volumeservice::GetVolumeInfoResponse* response) {
    uint64_t log_id = FLAGS_log_id;
    baidu::rpc::Controller cntl;
    cntl.set_log_id(log_id);
    aries::pb::volumeservice::VolumeService_Stub vs_client(&g_vs_channel);
    vs_client.get_volume_info(&cntl, &request, response, NULL);
    // if fail
    if (cntl.Failed() || response->status().code() != aries::AIE_OK) {
        ARIES_RPC_LOG(WARNING) << "get volume " << request.volume_id() << " failed, last server:"
            << base::endpoint2str(cntl.remote_side()) << " " << noflush;
        if (cntl.Failed()) {
            LOG(WARNING) <<  "rpc error: " << cntl.ErrorCode()
                << " (" << cntl.ErrorText() << ")";
            return AIE_FAIL;
        } else if (response->status().code() != aries::AIE_OK) {
            LOG(WARNING) <<  "request error: " << response->status().code()
                << " (" << response->status().msg() << ")";
            return response->status().code();
        }
    }
    return 0;
}

int check_blob_data(const uint64_t log_id, 
        const uint64_t volume_id, const uint64_t vbid,
        uint32_t blob_len, uint32_t blob_crc, uint32_t shard_len,
        const aries::pb::volumeservice::GetVolumeInfoResponse& volume,
        std::map<int, char*>& enc_data,
        CheckBlobResult* result) {
    // free space
    // context.reset();
    // check blob
    int ret = 0;
    std::string dir = std::to_string(volume_id) + "_" + std::to_string(vbid);
    std::vector<int> right_index_vec;
    std::vector<int> error_index_vec;
    std::vector<int> null_index_vec;
    std::bitset<100> right_shard_set;
    bool shard_index_vec[volume.n()];
    memset(shard_index_vec, 0, volume.n());
    memset(shard_index_vec, 1, volume.k());
    bool first = true;
    std::unique_ptr<char[]> blob_guard(new char[volume.k() * shard_len]);
    memset(blob_guard.get(), 0, volume.k() * shard_len);
    while(first || std::prev_permutation(shard_index_vec, shard_index_vec + volume.n())) {
        first = false;
        right_shard_set.reset();
        std::map<int, char*> ori_enc_data;
        for (size_t i = 0; i < volume.n(); ++i) {
            if (shard_index_vec[i] && enc_data[i] == NULL) {
                break;
            }
            if (shard_index_vec[i] && enc_data[i] != NULL) {
                ori_enc_data[i] = enc_data[i];
                right_shard_set[i] = true;
            }
        }
        //LOG(TRACE) << "vid:" << volume_id << " vbid:" << vbid << " try_indexs:" << right_shard_set.to_string();
        if (ori_enc_data.size() != volume.k()) {
            LOG(TRACE) << "vid:" << volume_id << " vbid:" << vbid << " the index:" << right_shard_set.to_string() << " less than k";
            continue;
        }
        ECOption eco;
        eco.type = (aries::ECType)volume.ec_type();
        eco.param.k = volume.k();
        eco.param.n = volume.n();
        common::ECPtr ec = common::ErasureCodeFactory::get_erasure_code(eco);
        char* final_result = blob_guard.get();
        ret = ec->decode(ori_enc_data, final_result, shard_len);
        if (ret != 0) {
            LOG(TRACE) << "ec decode failed" << " vid:" << volume_id 
                << " vbid:" << vbid << " shard_set:" << right_shard_set.to_string();
            memset(final_result, 0, volume.k() * shard_len);
            continue;
        }
        uint32_t new_crc = base::crc32c::Value(final_result, blob_len);
        if (new_crc != blob_crc) {
            LOG(TRACE) << "blob calc_crc:" << new_crc << " blob_crc:" << blob_crc;
            memset(final_result, 0, volume.k() * shard_len);
            continue;
        }
        for (auto iter : ori_enc_data) {
            right_index_vec.push_back(iter.first);
        }
        std::unique_ptr<char[]> recover_data_guard[100];
        std::map<int, char*> recover_data;
        for (size_t i = 0; i < volume.n(); ++i) {
            if (right_shard_set[i] == false) {
                recover_data[i] = new char[shard_len];
                recover_data_guard[i].reset(recover_data[i]);
            }
        }
        ret = ec->recover(ori_enc_data, recover_data, shard_len);
        if (ret != 0) {
            LOG(FATAL) << "recover failed";
        }
        for (size_t i = 0; i < volume.n(); ++i) {
            if (right_shard_set[i] == false) {
                if (enc_data[i] == NULL) {
                    null_index_vec.push_back(i);
                } else {
                    if (memcmp(enc_data[i], recover_data[i], shard_len) == 0) {
                        right_shard_set[i] = true;
                        right_index_vec.push_back(i);
                    } else {
                        error_index_vec.push_back(i);
                        std::string file = gen_shard_file_name(i, volume.n());
                        if (FLAGS_save_shards) {
                            write_to_file(dir, file + ".error", enc_data[i], shard_len);
                            write_to_file(dir, file, enc_data[i], shard_len);
                        }
                    }
                }
            }
        }
        break;
    }
    if (right_index_vec.size() < volume.k()) {
        LOG(FATAL) << "vid:" << volume_id << " vbid:" << vbid << " n:" 
            << volume.n() << " k:" << volume.k() << " check failed, right shards less k";
        std::cout << "vid:" << volume_id << " vbid:" << vbid << " n:" 
            << volume.n() << " k:" << volume.k() << " check failed, right shards less k" << std::endl;
        return -1;
    }
    result->right_index_vec = right_index_vec;
    result->error_index_vec = error_index_vec;
    result->null_index_vec = null_index_vec;
    for (auto index : error_index_vec) {
        auto & vlet = volume.vlets(index);
        base::EndPoint addr = common::int2endpoint(vlet.node_addr());
        result->shard_addrs[index] = addr;
    }
    LOG(TRACE) << "vid:" << volume_id << " vbid:" << vbid << " check successed";
    
    if (error_index_vec.empty()) {
        if (!null_index_vec.empty()) {
            LOG(WARNING) << "vid:" << volume_id << " vbid:" << vbid  << " n:" << volume.n() << " k:" << volume.k() 
                << " maybe_error_shard, right_cnt:" << right_index_vec.size() << " error_cnt:" << error_index_vec.size() << " null_cnt:" << null_index_vec.size();
        } else {
            LOG(NOTICE) << "vid:" << volume_id << " vbid:" << vbid  << " n:" << volume.n() << " k:" << volume.k() 
                << " no_error_shard, right_cnt:" << right_index_vec.size() << " error_cnt:" << error_index_vec.size() << " null_cnt:" << null_index_vec.size();
        }
     } else {
        std::cout << "vid:" << volume_id << " vbid:" << vbid  << " n:" << volume.n() << " k:" << volume.k() 
            << "has_error_shard, right_cnt:" << right_index_vec.size() << " error_cnt:" << error_index_vec.size() << " null_cnt:" << null_index_vec.size();
        LOG(FATAL) << "vid:" << volume_id << " vbid:" << vbid  << " n:" << volume.n() << " k:" << volume.k()
            << "has_error_shard, right_cnt:" << right_index_vec.size() << " error_cnt:" << error_index_vec.size() << " null_cnt:" << null_index_vec.size() << noflush;
    
        // right
        std::cout << " right_index: ";
        LOG(FATAL) << " right_index: " << noflush;
        for (auto index : right_index_vec) {
            std::cout << index << ",";
            LOG(FATAL) << index << "," << noflush;
        }
        // error
        std::cout << " error_index: ";
        LOG(FATAL) << " error_index: " << noflush;
        for (auto index : error_index_vec) {
            std::cout << index << ",";
            LOG(FATAL) << index << "," << noflush;
        }
        // null
        std::cout << " null_index: ";
        LOG(FATAL) << " null_index: " << noflush;
        for (auto index : null_index_vec) {
            std::cout << index << ",";
            LOG(FATAL) << index << "," << noflush;
        }
        std::cout << " right_shard-k:" <<  right_index_vec.size() - volume.k() << std::endl;
        LOG(FATAL) << " right_shard-k:" <<  right_index_vec.size() - volume.k();
    }
    return 0;
}

int check_one_blob(const uint64_t log_id, 
    const uint64_t volume_id, const uint64_t vbid, CheckBlobResult* result) {
    int ret = 0;
    aries::pb::volumeservice::GetVolumeInfoRequest request;
    aries::pb::volumeservice::GetVolumeInfoResponse response;
    request.set_volume_id(volume_id);
    ret = get_volume(request, &response);
    if (ret != 0) {
        LOG(WARNING) << "log_id:" << log_id << " vid:" << volume_id << " vbid:" << vbid << "get volume failed";
        return ret;
    }
    uint32_t blob_len = 0;
    uint32_t blob_crc = 0;
    uint32_t shard_len = 0;
    
    std::map<int, char*> enc_data;
    std::unique_ptr<char[]> enc_data_guard[100];
    std::string dir = std::to_string(volume_id) + "_" + std::to_string(vbid);
    
    auto & volume = response;
    for (auto i = 0; i < (int)volume.n(); ++i) { enc_data[i] = NULL; }
    std::unique_ptr<GetShardsContext> context(new GetShardsContext);
    context->mutex.lock();
    for (int i = 0; i < volume.vlets_size(); ++i) {
        auto & vlet = volume.vlets(i);
        if (vlet.node_addr() == 0) {
            continue;
        }
        base::EndPoint addr = common::int2endpoint(vlet.node_addr());
        uint32_t shard_index = i;
        auto shardctx = std::shared_ptr<GetShardsContext::ShardCtx>(new GetShardsContext::ShardCtx);
        bool ok = context->shards.insert(std::make_pair(shard_index, shardctx)).second;
        assert(ok);
        shardctx->addr = addr;
        shardctx->vlet_info = new aries::pb::volumeservice::VletDetail;
        shardctx->vlet_info->CopyFrom(vlet);
        shardctx->response = new aries::pb::ShardGetResponse;
        shardctx->request = new aries::pb::ShardGetRequest;
        shardctx->request->set_token(FLAGS_token);
        shardctx->request->set_volume_id(volume_id);
        shardctx->request->set_vbid(vbid);
        shardctx->request->set_shard_index(shard_index);
        shardctx->request->set_need_meta(true);
        shardctx->request->set_need_data(true);

#if defined(_UNITTEST) || defined(_UNIT_TEST)
        base::EndPoint data_addr = addr;
#else
        base::EndPoint data_addr = common::get_data_service_addr(addr);
#endif
        context->shards[shard_index]->cntl.set_log_id(FLAGS_log_id);
        context->shards[shard_index]->cntl.set_timeout_ms(FLAGS_call_timeout_ms);
        baidu::rpc::ChannelOptions options;
        options.timeout_ms = FLAGS_call_timeout_ms;
        options.connect_timeout_ms = FLAGS_connect_timeout_ms;
        baidu::rpc::Channel channel;
        ret = channel.Init(data_addr, &options);
        if (ret != 0) {
            LOG(WARNING) << std::dec << "init channel failed, shard_index:" << shard_index;
            continue;
        } else {
            LOG(NOTICE) << std::dec << "will get shard from " << common::endpoint2str(data_addr)
                    << " vid:" << volume_id << " vbid:" << vbid
                    << " shard_index:" << shard_index;
        }
        ::google::protobuf::Closure* done = ::baidu::rpc::NewCallback(
            &get_shard_done, shardctx->request, shardctx->response, context.get());
        aries::pb::DataNodeDataService_Stub stub(&channel);
        stub.get(&context->shards[shard_index]->cntl, shardctx->request, shardctx->response, done);
    }
    context->sync_point = std::shared_ptr<common::SyncPoint>(
                    new common::SyncPoint(context->shards.size()));
    context->mutex.unlock();
    context->sync_point->wait();

    for (auto it : context->shards) {
        auto shard_index = it.first;
        auto shardctx = it.second;
        auto& vlet = volume.vlets(shard_index);
        base::EndPoint addr = common::int2endpoint(vlet.node_addr());
        result->shard_addrs[shard_index] = addr;
        if (!shardctx->cntl.response_attachment().empty()) {
            shardctx->data = shardctx->cntl.response_attachment().to_string();
            std::string file = gen_shard_file_name(shard_index, volume.n());
            if (FLAGS_save_shards && !write_to_file(dir, file, shardctx->data.data(), shardctx->data.size())) {
                LOG(NOTICE) << "write shard data to file failed,"
                        << " vid:" << volume_id << " vbid:" << vbid
                        << " shard_index:" << shard_index;
            }
            char *buf = new char[shardctx->data.size()];
            enc_data_guard[shard_index].reset(buf);
            memcpy(buf, shardctx->data.data(), shardctx->data.size());
            enc_data[shard_index] = buf;
            uint32_t checksum = base::crc32c::Value(shardctx->data.c_str(), shardctx->data.size());
            if (checksum != shardctx->response->shard_meta().shard_crc()) {
                LOG(WARNING) << "shard data crc error, "
                        << " vid:" << volume_id << " vbid:" << vbid
                        << " shard_index:" << shardctx->request->shard_index()
                        << " expect_crc:" << shardctx->response->shard_meta().shard_crc()
                        << " actual_crc:" << checksum;
            } else {
                if (blob_len == 0) {
                    blob_len = shardctx->response->shard_meta().blob_len();
                } else if (blob_len != shardctx->response->shard_meta().blob_len()) {
                    LOG(FATAL) << "blob len not match, expect_blob_len:" << blob_len
                            << " curr_blob_len:" << shardctx->response->shard_meta().blob_len()
                            << " curr_shard_index:" << shard_index;
                    return -1;
                }
                if (blob_crc == 0) {
                    blob_crc = shardctx->response->shard_meta().blob_crc();
                } else if (blob_crc != shardctx->response->shard_meta().blob_crc()) {
                    LOG(FATAL) << "blob crc not match, expect_blob_crc:" << blob_crc
                            << " curr_blob_crc:" << shardctx->response->shard_meta().blob_crc()
                            << " curr_shard_index:" << shard_index;
                    return -1;
                }
                if (shard_len == 0) {
                    shard_len = shardctx->response->shard_meta().shard_len();
                } else if (shard_len != shardctx->response->shard_meta().shard_len()) {
                    LOG(FATAL) << "shard len not match, expect_shard_len:" << shard_len
                            << " curr_shard_len:" << shardctx->response->shard_meta().shard_len()
                            << " curr_shard_index:" << shard_index;
                    return -1;
                }
            }
        } else {
            LOG(NOTICE) << "no data from blob, vid:" << volume_id << " vbid:" << vbid
                    << " shard_index:" << shard_index;
        }
    }
    size_t shard_cnt = 0;
    for (auto iter : enc_data) {
        if (iter.second != NULL) {
            ++shard_cnt;
        }
    }
    if (shard_cnt < volume.k()) {
        LOG(FATAL) << "shard from blob less than k,cant check, vid:" << volume_id << " vbid:" << vbid;
        return -1;
    }
    ret = check_blob_data(log_id, 
        volume_id, vbid, 
        blob_len, blob_crc, shard_len,
        volume, enc_data, result);
    return ret;;
}

int remove_bad_shards(const uint64_t log_id, 
    uint64_t volume_id, uint64_t vbid, std::vector<int> error_index_vec) {
    int ret = 0;
    std::unique_ptr<CheckBlobResult> result(new CheckBlobResult);
    int check_ret = check_one_blob(log_id, volume_id, vbid, result.get());
    while (!error_index_vec.empty()) {
        int pos = error_index_vec.size() - 1;
        int shard_index = error_index_vec[pos];
        if (check_ret == 0) {
            bool flag = false;
            for (auto& e : result->error_index_vec) {
                if (shard_index == e) {
                    flag = true;
                    break;
                }
            }
            if (!flag) {
                LOG(FATAL) << "log_id:" << log_id << "vid:" << volume_id << " vbid:" << vbid 
                    << " check blob error_indexs is not same as given error_index," << noflush;
                ret = -1;
                break;
            }
        }
        LOG(TRACE) << "log_id:" << "vid:" << volume_id << " vbid:" << vbid 
                << " begin remove shard_index:" << shard_index;
        aries::pb::ShardRemoveRequest request;
        aries::pb::AckResponse response;
        request.set_token(FLAGS_token);
        request.set_volume_id(volume_id);
        request.set_vbid(vbid);
        request.set_shard_index(shard_index);
        request.set_need_mark_delete(true);
        request.set_force(true);
        base::EndPoint addr = result->shard_addrs[shard_index];
        RpcCallOptions option;
        option.log_id = FLAGS_log_id;
        SynchronizedClosure rpc_waiter;
        DataNodeStub stub;
#if defined(_UNITTEST) || defined(_UNIT_TEST)
        base::EndPoint data_addr = addr;
#else
        base::EndPoint data_addr = common::get_data_service_addr(addr);
#endif
        stub.remove(data_addr, &request, &response, &rpc_waiter, &option);
        rpc_waiter.wait();
        if (response.status().code() != AIE_OK) {
            LOG(WARNING) << "log_id:" << "vid:" << volume_id << " vbid:" << vbid 
                << " remove failed shard_index:" << shard_index;
            ret = response.status().code();
            break;
        }
        error_index_vec.pop_back();
    }
    if (ret == 0) {
        std::cout << "repair blob successed, vid:" << volume_id << " vbid:" << vbid;
        LOG(TRACE) << "log_id:" << "repair blob successed, vid:" << volume_id << " vbid:" << vbid;
    } else {
        std::cout << "repair blob failed, vid:" << volume_id << " vbid:" << vbid;
        LOG(FATAL) << "log_id:" << "repair blob failed, vid:" << volume_id << " vbid:" << vbid << noflush;
        LOG(FATAL) << " given_error_index: " << noflush;
        for (auto index : error_index_vec) {
            LOG(FATAL) << index << "," << noflush;
        }
        LOG(FATAL);
        
    }
    return 0;
}

int Shell::check_blob(FILE* out) {
    ARIES_SHELL_CHECK_TOKEN();
    int ret = 0;
    /* 
    file_path.txt
    [vid] [vbid]
     10     12
    */
    if (FLAGS_file_path.empty()) {
        ARIES_SHELL_CHECK_INT_LE_0(volume_id);
        ARIES_SHELL_CHECK_INT_LE_0(vbid);
        uint64_t log_id = base::fast_rand();
        LOG(TRACE) << "log_id:" << "begin check blob, vid:" << FLAGS_volume_id << " vbid:" << FLAGS_vbid;
        std::unique_ptr<CheckBlobResult> result(new CheckBlobResult);
        ret = check_one_blob(log_id, FLAGS_volume_id, FLAGS_vbid, result.get());
    } else {
        ARIES_SHELL_CHECK_INT_LE_0(thread_num);
        auto threadpool = std::unique_ptr<common::threadpool::ThreadpoolWithTaskGroup>(
            new common::threadpool::ThreadpoolWithTaskGroup);
        common::threadpool::ThreadpoolWithTaskGroup::StartOption option;
        option.thread_num = FLAGS_thread_num;
        auto st = threadpool->start(option);

        std::ifstream ifile;
        ifile.open(FLAGS_file_path);
        if (!ifile.is_open()) {
            LOG(TRACE) << "failed to open file, file_path:" << FLAGS_file_path;
        } else {
            uint64_t volume_id;
            uint64_t vbid;
            while (ifile >> volume_id >> vbid) {
                auto callable = [volume_id, vbid]() {
                    std::unique_ptr<CheckBlobResult> result(new CheckBlobResult);
                    uint64_t log_id = base::fast_rand();
                    LOG(TRACE) << "log_id:" << "begin check blob, vid:" << volume_id << " vbid:" << vbid;
                    check_one_blob(log_id, volume_id, vbid, result.get());
                };
                common::threadpool::ThreadpoolWithTaskGroup::TaskOption option;
                option.group_id = volume_id;
                auto st = threadpool->submit(std::move(callable), option);
                assert(st.error_code() == 0);
                
            }
        }
        ifile.close();
        threadpool->sync();
        threadpool->stop();
    }
    return ret;
}

int Shell::repair_blob(FILE* out) {
    ARIES_SHELL_CHECK_TOKEN();
    int ret = 0;
    /* 
    file_path.txt
    [vid] [vbid] [error_indexs]
     10     12      2,3,4,5
    */
    if (FLAGS_file_path.empty()) {
        ARIES_SHELL_CHECK_INT_LE_0(volume_id);
        ARIES_SHELL_CHECK_INT_LE_0(vbid);
        ARIES_SHELL_CHECK_STRING(shard_indexs);
        LOG(TRACE) << "log_id:" << "begin remove error shards, vid:" << FLAGS_volume_id << " vbid:" << FLAGS_vbid;
        std::vector<std::string> read_error_indexs_str_vec;
        std::string split(",");
        Tokenize(FLAGS_shard_indexs, split, &read_error_indexs_str_vec);
        std::vector<int> read_error_indexs_vec;
        for (auto index : read_error_indexs_str_vec) {
            read_error_indexs_vec.push_back(std::atoi(index.c_str()));
        }
        uint64_t log_id = base::fast_rand();
        ret = remove_bad_shards(log_id, FLAGS_volume_id, FLAGS_vbid, read_error_indexs_vec);
    } else {
        ARIES_SHELL_CHECK_INT_LE_0(thread_num);
        auto threadpool = std::unique_ptr<common::threadpool::ThreadpoolWithTaskGroup>(
            new common::threadpool::ThreadpoolWithTaskGroup);
        common::threadpool::ThreadpoolWithTaskGroup::StartOption option;
        option.thread_num = FLAGS_thread_num;
        auto st = threadpool->start(option);

        std::ifstream ifile;
        ifile.open(FLAGS_file_path);
        if (!ifile.is_open()) {
            LOG(TRACE) << "failed to open file, file_path:" << FLAGS_file_path;
        } else {
            uint64_t volume_id;
            uint64_t vbid;
            std::string shard_indexs;
            while (ifile >> volume_id >> vbid >> shard_indexs) {
                std::vector<std::string> read_error_indexs_str_vec;
                std::string split(",");
                Tokenize(shard_indexs, split, &read_error_indexs_str_vec);
                std::vector<int> read_error_indexs_vec;
                for (auto index : read_error_indexs_str_vec) {
                    read_error_indexs_vec.push_back(std::atoi(index.c_str()));
                }
                auto callable = [volume_id, vbid, read_error_indexs_vec]() {
                    uint64_t log_id = base::fast_rand();
                    LOG(TRACE) << "log_id:" << "begin remove error shards, vid:" 
                        << volume_id << " vbid:" << vbid;
                    int ret = remove_bad_shards(log_id, volume_id, vbid, read_error_indexs_vec);
                };
                common::threadpool::ThreadpoolWithTaskGroup::TaskOption option;
                option.group_id = volume_id;
                auto st = threadpool->submit(std::move(callable), option);
                assert(st.error_code() == 0);
            }
        }
        ifile.close();
        threadpool->sync();
        threadpool->stop();
    }
    return ret;
}

int check_one_volume(uint64_t volume_id) {
    aries::pb::volumeservice::GetVolumeInfoRequest request;
    aries::pb::volumeservice::GetVolumeInfoResponse response;
    request.set_volume_id(volume_id);
    int ret = get_volume(request, &response);
    if (ret != 0) {
        LOG(WARNING) << "fail to check volume hole, volume_id:" << volume_id;
        return ret;
    }

    auto & volume = response;
    uint64_t min_free = 1024 * 1024 * 1024 * 1024ull;
    uint64_t total_size = 0;
    for (int i = 0; i < volume.vlets_size(); ++i) {
        auto & vlet = volume.vlets(i);
        if (vlet.node_addr() == 0) {
            continue;
        }

        base::EndPoint node_addr = common::int2endpoint(vlet.node_addr());
        base::EndPoint data_addr = common::get_data_service_addr(node_addr);
        aries::pb::GetVletInfoResponse res;
        aries::pb::GetVletInfoRequest req;
        req.set_token(FLAGS_token);
        req.set_volume_id(volume_id);
        req.set_shard_index(i);
        RpcCallOptions option;
        SynchronizedClosure closure;
        DataNodeStub stub;
        stub.get_vlet_info(data_addr, &req, &res, &closure, &option);
        closure.wait();

        if (res.status().code() != AIE_OK) {
            continue;
        }

        if (res.vlet_info_list(0).free_size() < min_free) {
            min_free = res.vlet_info_list(0).free_size();
        }
        total_size = res.vlet_info_list(0).total_size();
    }

    LOG(TRACE) << "check volume hole, volume_id:" << volume_id << " k:" << volume.k()
        << " n:" << volume.n() << " free_vlet_size:" << min_free
        << " total_vlet_size:" << total_size << " total_free_size:"
        << volume.k() * min_free << " total_volume_size:" << total_size * volume.k();
    return ret;
}

int Shell::check_volume_hole_size(FILE* out) {
    ARIES_SHELL_CHECK_TOKEN();
    ARIES_SHELL_CHECK_STRING(space_name);
    ARIES_SHELL_CHECK_INT_LE_0(thread_num);
    int ret = 0;
    auto threadpool = std::unique_ptr<common::threadpool::ThreadpoolWithTaskGroup>(
            new common::threadpool::ThreadpoolWithTaskGroup);
    common::threadpool::ThreadpoolWithTaskGroup::StartOption option;
    option.thread_num = FLAGS_thread_num;
    auto st = threadpool->start(option);

    LOG(TRACE) << "begin check space, space:" << FLAGS_space_name;

    aries::pb::ListVolumeRequest request;
    aries::pb::ListVolumeResponse response;
    request.set_token(FLAGS_token);
    request.set_space_name(FLAGS_space_name);
    SHELL_MASTER_RPC_CALL(list_volume);

    for (int i = 0; i < response.volume_list_size(); ++i) {
        auto volume = response.volume_list(i);
        auto volume_id = volume.volume_id();

        auto callable = [volume_id]() {
            check_one_volume(volume_id);
        };
        common::threadpool::ThreadpoolWithTaskGroup::TaskOption option;
        option.group_id = volume_id;
        auto st = threadpool->submit(std::move(callable), option);
        assert(st.error_code() == 0);
    }

    threadpool->sync();
    threadpool->stop();
    return ret;
}

int Shell::local_repair_blob(FILE* out) {
    ARIES_SHELL_CHECK_TOKEN();
    ARIES_SHELL_CHECK_INT_LE_0(n);
    ARIES_SHELL_CHECK_INT_LE_0(k);
    ARIES_SHELL_CHECK_INT_LE_0(blob_len);
    if (FLAGS_dir_path.empty()) {
        LOG(WARNING) << "local_repair_blob FLAGS_dir_path is empty";
        return -1;
    }
    std::map<int, std::string> data_array;
    std::map<int, char*> p_array;
    for (const auto& entry : fs::directory_iterator(FLAGS_dir_path)) {
        int shard_index = 0;
        try {
            shard_index = std::stoi(entry.path().filename().string());
        } catch(...) {
            continue;
        }
        if (!base::ReadFileToString(base::FilePath(entry.path().string()),
                &data_array[shard_index])) {
            LOG(WARNING) << "read shard file failed, filename:" << entry.path().filename();
            return -1;
        } else {
            LOG(NOTICE) << "read shard file success, filename:" << entry.path().filename();
        }
    }
    ECOption eco;
    eco.param.k = FLAGS_k;
    eco.param.n = FLAGS_n;
    LOG(NOTICE) << "k:" << std::dec << FLAGS_k;
    LOG(NOTICE) << "n:" << std::dec << FLAGS_n;
    common::ECPtr ec = common::ErasureCodeFactory::get_erasure_code(eco);
    int shard_len = 0;
    for (auto& kv : data_array) {
        p_array[kv.first] = (char*)kv.second.c_str();
        if((shard_len != 0) && (shard_len != (int)kv.second.size())) {
            LOG(WARNING) << "shard size not equal, shard_index:" << kv.first;
            return -1;
        }
        shard_len = (int)kv.second.size();
    }
    LOG(NOTICE) << std::dec << "shard_len:" << shard_len;
    char* final_result = new char[FLAGS_n * shard_len];
    int ret = ec->decode(p_array, final_result, shard_len);
    write_to_file(FLAGS_dir_path, std::string("data"), final_result, FLAGS_blob_len);
    if (ret != 0) {
        LOG(WARNING) << "decode failed:" << ret;
        return -1;
    }
    uint32_t new_crc = base::crc32c::Value(final_result, FLAGS_blob_len);
    LOG(WARNING) << "blob crc:" << std::to_string(new_crc);
    return 0;
}

int Shell::remove_append_orphaned_shards(FILE* out) {
    ARIES_SHELL_CHECK_TOKEN();

    auto is_append_vlet = [](uint32_t vlet_type) {
        switch (vlet_type) {
        case VLET_TYPE_APPEND_32G_256M_4K:
        case VLET_TYPE_APPEND_16G_256M_4K:
        case VLET_TYPE_APPEND_64G_256M_4K:
        case VLET_TYPE_APPEND_2G_256M_4K:
        case VLET_TYPE_APPEND_4G_256M_4K:
        case VLET_TYPE_APPEND_8G_256M_4K:
        case VLET_TYPE_APPEND_2G_64M_4K:
        case VLET_TYPE_APPEND_4G_64M_4K:
        case VLET_TYPE_APPEND_VARIENT:
            return true;
        }
        return false;
    };

    aries::ConfInfo options;
    options.token = FLAGS_token;
    options.timeout_ms = 10000;
    options.max_retry = 2;
    options.use_proxy_lib = true;
    options.proxy_conf.token = FLAGS_token;
    options.proxy_conf.ms_server = aries::common::FLAGS_master_address;
    options.proxy_conf.vs_server = FLAGS_volume_service_address;
    options.proxy_conf.vs_load_balancer = "la";
    options.proxy_conf.bvar_service_port = 59400;
    options.proxy_conf.query_meta_timeout_ms = 5000;
    options.proxy_conf.get_shard_min_timeout_ms = 500;
    std::unique_ptr<aries::AriesClient> client;
    client.reset(aries::AriesClient::new_aries_client(options));

    auto remove_blob = [&](uint64_t volume_id, uint64_t vbid) ->int {
        aries::RequestOption request_option;
        request_option.log_id = FLAGS_log_id;
        request_option.timeout_ms = 6000;
        request_option.retry_timeout_ms = 1000;
        __uint128_t bid = make_bid(volume_id, vbid);
        std::string data;
        aries::BlobMeta meta;
        int ret = client->get(request_option, bid, NULL, &data, &meta);

        if (ret == aries::AIE_BLOB_NOT_EXIST) {
            // not exist
            int ret = client->remove(request_option, bid);
            if (ret == 0) {
                LOG(TRACE) << "volume_id:" << volume_id << " vbid:" << vbid
                           << " remove blob successed";
            } else {
                LOG(TRACE) << "remove blob failed, return code:" << ret;
            }
        } else {
            LOG(TRACE) << "volume_id:" << volume_id << " vbid:" << vbid
                       << "blob may be exist, can not remove it, return code:" << ret;
        }
        return 0;
    };

    auto check_blob = [&](uint64_t volume_id, uint64_t vbid) ->void {
        aries::pb::ShowVolumeResponse response;
        aries::pb::ShowVolumeRequest request;
        request.set_token(FLAGS_token);
        request.set_volume_id(volume_id);
        SHELL_MASTER_RPC_CALL(show_volume);

        GetShardsMetaContext context;
        context.mutex.lock();
        auto& volume = response.volume();
        for (int i = 0; i < volume.vlets_size(); ++i) {
            auto & vlet = volume.vlets(i);
            if (vlet.node_addr() == 0) {
                continue;
            }
            base::EndPoint addr = common::int2endpoint(vlet.node_addr());
            uint32_t shard_index = vlet.shard_index();
            auto shardctx = std::shared_ptr<GetShardsMetaContext::ShardCtx>(new GetShardsMetaContext::ShardCtx);
            bool ok = context.shards.insert(std::make_pair(shard_index, shardctx)).second;
            assert(ok);
            shardctx->addr = addr;
            shardctx->vlet_info = new aries::pb::VletInfo;
            shardctx->vlet_info->CopyFrom(vlet);
            shardctx->response = new aries::pb::ShardGetResponse;
            shardctx->request = new aries::pb::ShardGetRequest;
            shardctx->request->set_token(FLAGS_token);
            shardctx->request->set_volume_id(volume_id);
            shardctx->request->set_vbid(vbid);
            shardctx->request->set_shard_index(shard_index);
            shardctx->request->set_need_meta(true);
            shardctx->request->set_need_data(false);

            ::google::protobuf::Closure* done = ::baidu::rpc::NewCallback(
                    &get_shards_meta_done, shardctx->request, shardctx->response, &context);
            RpcCallOptions option;
            option.log_id = FLAGS_log_id;
            DataNodeStub stub;

            base::EndPoint data_addr = common::get_data_service_addr(addr);
            stub.get(data_addr, shardctx->request, shardctx->response, done, &option);
        }
        context.sync_point = std::shared_ptr<common::SyncPoint>(
                new common::SyncPoint(context.shards.size()));
        context.mutex.unlock();
        context.sync_point->wait();

        uint32_t succ_count = 0;
        bool is_all_append_store = true;
        for (auto & pair : context.shards) {
            auto shard = pair.second;
            if (shard->response->status().code() == AIE_OK) {
                succ_count++;
                if (!is_append_vlet(shard->vlet_info->vlet_type())) {
                    return;
                }
            }
        }

        if (succ_count >= volume.k()) {
            LOG(WARNING) << "not orphaned blob, " << volume_id << ":" << vbid;
            return;
        }
        if (succ_count == 0) {
            return;
        }

        LOG(WARNING) << "aries_cli pcs xa02 -op show_blob --volume_id " << volume_id << " --vbid " << vbid;
        remove_blob(volume_id, vbid);
    };

    ARIES_SHELL_CHECK_INT_LE_0(thread_num);
    auto threadpool = std::unique_ptr<::aries::common::threadpool::ThreadpoolWithTaskGroup>(
            new ::aries::common::threadpool::ThreadpoolWithTaskGroup);
    ::aries::common::threadpool::ThreadpoolWithTaskGroup::StartOption option;
    option.thread_num = FLAGS_thread_num;
    auto st = threadpool->start(option);

    int count = 0;
    std::ifstream ifile;
    ifile.open(FLAGS_file_path);
    if (!ifile.is_open()) {
        LOG(TRACE) << "failed to open file, file_path:" << FLAGS_file_path;
    } else {
        uint64_t volume_id;
        uint64_t vbid;
        std::string line;
        while (std::getline(ifile, line)) {
            std::istringstream iss(line);
            std::string number_str;
            std::getline(iss, number_str, '_');
            std::string first_number_str = number_str;
            std::getline(iss, number_str);
            std::string second_number_str = number_str;

            volume_id = std::stoull(first_number_str);
            vbid = std::stoull(second_number_str);
            count++;
            auto callable = [&, volume_id, vbid, count]() ->void {
                LOG(TRACE) << "log_id:" << "begin check blob, vid:" << volume_id << " vbid:" << vbid << " count:" << count;
                check_blob(volume_id, vbid);
            };
            ::aries::common::threadpool::ThreadpoolWithTaskGroup::TaskOption option;
            option.group_id = volume_id;
            auto st = threadpool->submit(std::move(callable), option);
            assert(st.error_code() == 0);
        }
    }
    ifile.close();

    threadpool->sync();
    threadpool->stop();

    return 0;
}

}
}
