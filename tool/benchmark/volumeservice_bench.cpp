#include <string.h>
#include <iostream>
#include <atomic>
#include <memory>
#include <vector>

#include <bthread.h>
#include <base/logging.h>
#include <base/comlog_sink.h>
#include <base/crc32c.h>
#include <base/string_printf.h>
#include <base/file_util.h>
#include <gflags/gflags.h>

#include "baidu/inf/aries/common/proto/volume_service.pb.h"
#include <baidu/rpc/channel.h>
#include <baidu/rpc/controller.h>

std::atomic_long g_log_id(0);

DEFINE_string(address, "http://bjyz-bce-mar-y02-for-bos-000.bjyz:8400", "volumeservice address or BNS");

DEFINE_int32(sleep_ms, 0, "sleep interval milliseconds per request/concurrency");
DEFINE_int32(timeout_ms, 6000, "timeout of request");

DEFINE_int32(get_volume, 1, "concurrency of get_volume");
DEFINE_int32(volume_start, 501, "start volume id");
DEFINE_int32(volume_end, 1000, "start volume id");

bool g_running = true;
baidu::rpc::Channel g_channel;
aries::pb::volumeservice::VolumeService_Stub g_client(&g_channel);

void stop_press(int sig) {
    LOG(NOTICE) << "received signal " << sig << ", try stop";
    g_running = false;
}

void dummy(baidu::rpc::Controller* cntl, ::aries::pb::volumeservice::GetVolumeInfoResponse *response) {
    delete cntl;
    delete response;
}

void *test_get_volume(void *) {
    int vnum = (FLAGS_volume_end - FLAGS_volume_start + 1);
    while (g_running) {
        baidu::rpc::Controller *cntl = new baidu::rpc::Controller;
        ::aries::pb::volumeservice::GetVolumeInfoRequest request;
        request.set_volume_id(FLAGS_volume_start + base::fast_rand() % vnum);

        ::aries::pb::volumeservice::GetVolumeInfoResponse *response = new ::aries::pb::volumeservice::GetVolumeInfoResponse;

        g_client.get_volume_info(cntl, &request, response, baidu::rpc::NewCallback(&dummy, cntl, response));
        if (FLAGS_sleep_ms > 0) {
            bthread_usleep(FLAGS_sleep_ms * 1000);
        }
    }
    return 0;
}

int main(int argc, char *argv[]) {
    // set version
    google::SetVersionString(base::string_printf("Version: %s\nRepoURL: %s\nBuild: %s %s\n",
            __ARIES_VERSION_ID__, __ARIES_REPO_URL__,
            __DATE__ " " __TIME__, __ARIES_BUILDHOST__));
    google::SetUsageMessage(base::string_printf("Usage: %s [GFlags OPTIONS]", argv[0]));

    google::ParseCommandLineFlags(&argc, &argv, false);

    logging::ComlogSinkOptions log_options;
    log_options.async = false;
    log_options.shorter_log_level = false;
    log_options.log_dir = "log";
    log_options.process_name = "blob_presser";
    log_options.print_vlog_as_warning = false;
    log_options.split_type = (logging::ComlogSplitType) 1;
    log_options.quota_day = 1;
    // MB
    log_options.quota_size = 4 * 1024;

    if (logging::ComlogSink::GetInstance()->Setup(&log_options) != 0) {
        LOG(ERROR) << "fail to setup comlog";
        return -1;
    }
    logging::SetLogSink(logging::ComlogSink::GetInstance());

    srand(time(NULL));

    baidu::rpc::ChannelOptions options;
    options.timeout_ms = FLAGS_timeout_ms;
    g_channel.Init(FLAGS_address.c_str(), "rr", &options);

    std::vector<bthread_t> tids;
    for (int i = 0; i < FLAGS_get_volume; ++i) {
        bthread_t tid;
        bthread_start_background(&tid, NULL, test_get_volume, NULL);
        tids.push_back(tid);
    }
    signal(SIGINT, stop_press);
    signal(SIGTERM, stop_press);
    for (size_t i = 0; i < tids.size(); ++i) {
        bthread_join(tids[i], NULL);
    }
    return 0;
}
