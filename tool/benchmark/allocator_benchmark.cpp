#include <string>
#include <gflags/gflags.h>
#include <base/time.h>
#include <signal.h>
#include <sys/types.h>
#include <linux/unistd.h>
#include <base/fast_rand.h>
#include <base/file_util.h>
#include "allocator/blob_allocator.h"
#include <base/logging.h>
#include <base/comlog_sink.h>
#if !(defined(gettid))
#define gettid()    syscall(__NR_gettid)
#endif

DECLARE_string(flagfile);
DEFINE_bool(h, false, "print help information");
DEFINE_bool(v, false, "print version information");
DEFINE_bool(auto_reload, false, "auto reload conf");
DEFINE_string(host, "************:8200", "server hosts");
DEFINE_string(spacename, "space1", "allocator from which space");
DEFINE_int32(threads, 1, "thread num");
DEFINE_int32(sleep_ms, 10, "sleep ms in a thread between query");
DEFINE_int32(len, 100, "allocate data len for each time");
DEFINE_int32(ops, 10000, "request ops");

static const char *s_proc_name = NULL;
#if defined( __DATE__ ) && defined ( __TIME__ )
static const char *s_build_time = __DATE__ " " __TIME__;
#else
static const char *s_build_time = "unknown";
#endif

#ifndef __ARIES_VERSION_ID__
#define __ARIES_VERSION_ID__ "unknown"
#endif

#ifndef __ARIES_REVISION_ID__
#define __ARIES_REVISION_ID__ "unknown"
#endif

#ifndef __ARIES_REPO_URL__
#define __ARIES_REPO_URL__   "unknown"
#endif

#ifndef __ARIES_BUILDHOST__
#define __ARIES_BUILDHOST__  "unknown"
#endif


bool reload_conf() {
    if (FLAGS_flagfile.empty()) {
        return false;
    }

    std::string data;
    if (!base::ReadFileToString(base::FilePath(FLAGS_flagfile), &data)) {
        LOG(WARNING) << "read file error, filename is " << FLAGS_flagfile;
        return false;
    }

    if (!google::ReadFlagsFromString(data, nullptr, false)) {
        LOG(WARNING) << "relaod configure error, gflags parse error";
        return false;
    }

    return true;
}



void sig_handler(int signum){
    bool reload_ret = reload_conf();
    LOG(NOTICE) << "relo conf conf, ret:" << (reload_ret ? "succ":"fail");
}


void dummy(baidu::rpc::Controller* cntl, ::aries::pb::AllocateBlobResponse* response) {
    delete cntl;
    delete response;
}

void* thread_func(void* arg) {
    signal(SIGUSR1, sig_handler);
    baidu::rpc::Channel channel;
    baidu::rpc::ChannelOptions options;
    base::EndPoint ep;
    base::str2endpoint(FLAGS_host.c_str(), &ep);
    int rc = channel.Init(ep, &options);
    assert(rc == 0);
    aries::pb::AllocateBlobRequest request;
    request.set_space_name(FLAGS_spacename);
    request.set_data_len(FLAGS_len);
    while (true) {
   // for (int i = 0; i < FLAGS_ops; i++) {
        if (FLAGS_auto_reload) {
            bool reload_ret = reload_conf();
            LOG(NOTICE) << "relo conf conf, ret:" << (reload_ret ? "succ":"fail");
        }
        baidu::rpc::Controller *cntl = new baidu::rpc::Controller;
        auto log_id = base::fast_rand();
        cntl->set_log_id(log_id);
        aries::pb::BlobAllocatorClientService_Stub stub(&channel);
        aries::pb::AllocateBlobResponse* response = new aries::pb::AllocateBlobResponse;
        
        stub.allocate_blob(cntl, &request, response, baidu::rpc::NewCallback(&dummy, cntl, response));
        if (FLAGS_sleep_ms > 0) {
            bthread_usleep(FLAGS_sleep_ms * 1000);
        }

        /*int rpc_code = cntl.ErrorCode();
        int error_code = response.status().code();
        if (rpc_code != 0 || error_code != 0) {
            LOG(WARNING) << "allocate failed, rpc_code:" << rpc_code
                << " error_code:" << error_code 
                << " log_id:" << log_id;
            continue;
        }

        const aries::pb::Bid bid = response.bid();
        uint64_t vid = bid.vid();
        uint64_t vbid = bid.vbid();
        LOG(NOTICE) << " allocate success, vid:" << vid
            << " vbid:" << vbid
            << " log_id:" << log_id;*/
    }
    
    return NULL;
}
int main(int argc, char* argv[]) {

    // set version
    std::string version_str;
    base::string_printf(&version_str,
                        "Version: %s\n"
                        "Revision: %s\n"
                        "RepoURL: %s\n"
                        "Build: %s %s\n",
                        __ARIES_VERSION_ID__, __ARIES_REVISION_ID__, __ARIES_REPO_URL__,
                        s_build_time, __ARIES_BUILDHOST__);
    google::SetVersionString(version_str);

    // set usage
    s_proc_name = strrchr(argv[0], '/');
    if (s_proc_name == NULL) {
        s_proc_name = argv[0];
    } else {
        ++s_proc_name;
    }
    std::string help_str;
    base::string_printf(&help_str,
                        "Usage: %s [OPTIONS...]\n"
                        "Options:\n"
                        "  -h              Print this help message.\n"
                        "  -v              Print version number.\n"
                        "  -stop           stop gc.\n"
                        "  -flagfile=$path Load flags from file.",
                        s_proc_name);
    google::SetUsageMessage(help_str);

    struct stat st;
    if (0 == stat("conf/allocator_benchmark.conf", &st) && FLAGS_flagfile.empty()) {
        FLAGS_flagfile = "conf/allocator_benchmark.conf";
    }
    google::ParseCommandLineFlags(&argc, &argv, true);
   
    if (FLAGS_v) {
        fprintf(stderr, "%s\n", version_str.c_str());
        _exit(0);
    }
    if (FLAGS_h) {
        fprintf(stderr, "%s\n", help_str.c_str());
        _exit(0);
    }
    // init log
    logging::ComlogSinkOptions log_options;
    log_options.async = false;
    log_options.shorter_log_level = false;
    log_options.log_dir = "log";
    log_options.process_name = "blob_presser";
    log_options.print_vlog_as_warning = false;
    log_options.split_type = (logging::ComlogSplitType) 1;
    log_options.quota_day = 1;
    // MB
    log_options.quota_size = 4 * 1024;

    if (logging::ComlogSink::GetInstance()->Setup(&log_options) != 0) {
        LOG(ERROR) << "fail to setup comlog";
        return -1;
    }
    logging::SetLogSink(logging::ComlogSink::GetInstance());

    std::vector<pthread_t> pids;
    int64_t start = base::gettimeofday_ms();
    for (int i = 0; i < FLAGS_threads; i++) {
        pthread_t pid;
        pthread_create(&pid, NULL, thread_func, NULL);
        pids.push_back(pid);
    }

    for (int i = 0; i < FLAGS_threads; i++) {
        pthread_join(pids[i], NULL);
    }
    int64_t end = base::gettimeofday_ms();
    int64_t call_num = FLAGS_threads * FLAGS_ops;
    int64_t call_ms = end - start;
    LOG(WARNING) << "rpc cnt: " << call_num << " time/ms: " << call_ms <<
        " latency/ms: " << ((call_num > 0) ? (static_cast<double>(call_ms) / call_num) : 0)
        << " qps: " << (static_cast<double>(call_num) * 1000/ call_ms);
    return 0;
}

