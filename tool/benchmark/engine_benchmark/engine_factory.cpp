// Copyright (c) 2021 Baidu.com, Inc. All Rights Reserved
// Author la<PERSON><PERSON><PERSON>(laizich<PERSON>@baidu.com)
// Date : Wed Mar 24 11:23:24 CST 2021

#include <base/time.h>

#include "datanode/vlet/append_vlet.h"
#include "datanode/vlet/linked_vlet.h"

#include "engine_factory.h"

namespace aries {
namespace tools {
namespace benchmark {

std::string VletFactory::benchmark_test_space_name = "benchmark_test_space";

aries::datanode::VletPtr VletFactory::create_vlet(const uint64_t volume_id,
                                                const uint32_t shard_index,
                                                const uint32_t vlet_type) {
    aries::datanode::VletPtr vlet;
    if (nullptr == _disk_agent) {
        LOG(FATAL) << "failed to create vlet with disk_agent is nullptr";
        return vlet;
    }

    //prepare vlet_info;
    aries::pb::SpaceInfo space_info;
    aries::pb::VletInfo vlet_info;
    space_info.set_space_name(VletFactory::benchmark_test_space_name.c_str());
    space_info.add_az_name("az0");
    //....more space info need to set but make no sense on benchmark;

    vlet_info.set_volume_id(volume_id);
    vlet_info.set_shard_index(shard_index);
    vlet_info.set_vlet_version(0);  /*all vlet vlet_version is set to 1*/
    vlet_info.set_vlet_type(static_cast<aries::VletType>(vlet_type));
    vlet_info.set_disk_id(_disk_agent->disk_id());
    vlet_info.set_create_time(base::gettimeofday_us());
    vlet_info.set_state(aries::VletState::VLET_STATE_NORMAL);
    vlet_info.mutable_membership()->set_volume_version(1);
    vlet_info.set_update_time(base::gettimeofday_us());
    vlet_info.set_max_holes_size_for_fast_remove(0);
    vlet_info.set_min_record_size_for_fast_remove(4*1024*1024);
    vlet_info.mutable_vlet_engine_options();
    vlet_info.set_append_zone_rewrite_rate(30);
    vlet_info.set_daily_rewrite_start_time(0);
    vlet_info.set_daily_rewrite_duration_second(0);
    vlet_info.set_permit_data_offset_index(false);
    //...more vlet_info need to be set for more various performance behavior

    int ret = aries::datanode::Vlet::new_vlet_from_info(_disk_agent, vlet_info, space_info, vlet, nullptr);

    if (ret != 0) {
        LOG(WARNING) << "failed to create volume vlet with volume_id:" << volume_id << " shard_index:" << shard_index;
        vlet.reset(static_cast<aries::datanode::Vlet*>(nullptr));
    } else {
        LOG(NOTICE) << "volume vlet create succeed with name:" << vlet->name();
    }

    return vlet;
}

aries::datanode::VletPtr VletFactory::open_vlet(const std::string& vlet_path) {
    aries::datanode::VletPtr vlet;
    base::FilePath file_path(vlet_path);

    if (base::PathExists(file_path) == false) {
        LOG(WARNING) << "vlet path:" << vlet_path << " do not exist on local filesystem";
        return vlet;
    }

    std::string filename = file_path.BaseName().value();

    if (filename.compare(0, 2, "L_") == 0) {
        vlet.reset(new aries::datanode::LinkedVlet(_disk_agent));	
    } else if (filename.compare(0, 2, "A_") == 0) {
        vlet.reset(new aries::datanode::AppendVlet(_disk_agent));
    } else {
        LOG(WARNING) << "unknown vlet type and can not open vlet";
        return vlet;
    }

    int ret = vlet->open(vlet_path);
    if (ret != 0) {
        LOG(WARNING) << "failed to open vlet on path:" << vlet_path;
        vlet.reset(static_cast<aries::datanode::Vlet*>(nullptr));
    }
    return vlet;	
}

}
}
}
