
#include "flags.h"

namespace aries {
namespace tools {
namespace benchmark {

DEFINE_int32(dummy_port, 8888, "baidu-rpc dummy server listen port on benchmark");

DEFINE_uint64(start_volume_id, 1000, "vlet start volume_id on benchmark");
DEFINE_int32(volume_count, 10, "vlet num to create during benchmark running");
DEFINE_int32(shard_index, 0, "vlet shard_index to create");
DEFINE_int32(vlet_type, 80, "vlet type to create");

DEFINE_int32(put_thread_num, 0, "concurrent put thread num");
DEFINE_int32(get_thread_num, 0, "concurrent get thread num");
DEFINE_int32(remove_thread_num, 0, "concurrent remove thread num");
DEFINE_int32(put_shard_len, 4096, "put shard_len for put thread");

DEFINE_string(disk_path, "./aries_data", "disk path to disk_agent");
DEFINE_string(disk_type, "SSD", "disk media type, such as SSD/HDD/SMR");
DEFINE_uint64(rocksdb_block_cache_size_in_mb, 10240, "rocksdb block cache size in mb");

}
}
}
