/**
 * @brief  SSD-cache-bench: evaluating cache performance & correctness
 * <AUTHOR>
 * @date   <2022-06-28 Tue>
 */

// see wiki: https://ku.baidu-int.com/d/bdrEILBaQSNk2e

#include <iostream>
#include <fstream>
#include <vector>
#include <string>
#include <set>
#include <mutex>
#include <numeric>
#include <algorithm>
#include <thread>

#include "gflags/gflags.h"
#include "base/fast_rand.h"
#include <bvar/bvar.h>
#include <base/logging.h>
#include <base/comlog_sink.h>
#include <base/string_printf.h>
#include "baidu/inf/aries-api/common/common.h"
#include "baidu/inf/aries-api/common/config.h"
#include "baidu/inf/aries-api/common/proto/datanode.pb.h"
#include "baidu/inf/aries/common/common.h"
#include "baidu/inf/aries/datanode/flags.h"
#include "baidu/inf/aries/datanode/util/util.h"
#include "baidu/inf/aries/datanode/cache/cache_manager.h"
#include "baidu/inf/aries/datanode/test/common/buffer_gen.h"
#include "baidu/inf/aries/tool/benchmark/ssd_cache_bench/workload_generator.hpp"

constexpr uint64_t VOLUME_ID = 1;
constexpr uint64_t SHARD_INDEX = 2;

DECLARE_string(flagfile);
DEFINE_uint64(num_thread, 1, "number of stress thread");
DEFINE_int32(cache_capacity, 1024, "ssd cache capacity (in MB)");
DEFINE_string(cache_path, "", "cache disk path");
DEFINE_bool(validate, false, "whether to validate get result");
DEFINE_bool(bench_use_default_shard_size, false, "ignore `shard_size' in requests to minimize memory usage");

namespace dn = aries::datanode;

namespace bench = aries::datanode::cache_bench;

namespace aries {
namespace datanode {
namespace cache_bench {

bool is_meta_identical(const pb::ShardMeta& meta1, const pb::ShardMeta& meta2) {
    bool identical = (meta1.shard_len() == meta2.shard_len()) && (meta1.blob_len() == meta2.blob_len()) &&
            (meta1.blob_crc() == meta2.blob_crc()) && (meta1.shard_crc() == meta2.shard_crc());

    if (!identical) {
        LOG(FATAL) << "meta not identical: "
                   << "shard len: " << meta1.shard_len() << ", " << meta2.shard_len() << "; "
                   << "blob len: " << meta1.blob_len() << ", " << meta2.blob_len() << "; "
                   << "blob_crc: " << meta1.blob_crc() << ", " << meta2.blob_crc() << "; "
                   << "shard_crc " << meta1.shard_crc() << ", " << meta2.shard_crc() << "; ";
    }
    return identical;
}

bool is_data_correct(const pb::ShardMeta& meta, const base::IOBuf& data) {
    uint32_t data_crc = dn::build_crc(data);
    uint32_t meta_crc = meta.shard_crc();
    bool correct = (data_crc == meta_crc);
    return correct;
}

class SsdCacheStress {
public:
    SsdCacheStress(std::shared_ptr<dn::CacheManager>& cache_mgr) :
            _hit_count(0),
            _total_requests(0),
            _total_size(0),
            _request_count(static_cast<int>(bench::ReqType::NUM_REQUESTS), 0),
            _generator{bench::make_workload_generator(FLAGS_generator_type, true)},
            _cache_mgr(cache_mgr),
            _start_timestamp(0),
            _finish_timestamp(0),
            _volume_id(VOLUME_ID),
            _shard_index(SHARD_INDEX) {}

    void set_volume_id(uint64_t vid) {
        _volume_id = vid;
    }

    void set_shard_index(uint64_t sidx) {
        _shard_index = sidx;
    }

    size_t hit_count() {
        return _hit_count;
    }

    size_t total_requests() {
        return _total_requests;
    }

    size_t total_size() {
        return _total_size;
    }

    double hit_ratio() {
        return (double)_hit_count / (double)_request_count[static_cast<int>(ReqType::GET)];
    }

    // display test results
    void report() {
        // total IO requests;
        size_t _total_requests = std::accumulate(_request_count.begin(), _request_count.end(), 0);
        std::cout << "requests: " << _total_requests << std::endl;
        // each type
        for (int i = 0; i < _request_count.size(); i++) {
            std::cout << " " << req_type_to_string(static_cast<ReqType>(i)) << " " << _request_count[i] << std::endl;
        }

        // hit ratio
        double hit_ratio = (double)_hit_count / (double)_request_count[static_cast<int>(ReqType::GET)];
        std::cout << "hits ratio: " << hit_ratio << std::endl;

        // throughput
        uint64_t time_span = _finish_timestamp - _start_timestamp;
        std::cout << "test duration: " << time_span << "us" << std::endl;
        double throughput = (double)_total_requests / (double)time_span * 1000000;
        std::cout << "throughput: " << throughput << "iops" << std::endl;

        // latency
        double latency = 1000000.0 / throughput;  // us per request
        std::cout << "latency: " << latency << "us" << std::endl;
    }

    void init_blobs() {
        bench::IoRequest req;

        while (_generator->produce(&req) == 0 && req.req.type == ReqType::PUT) {
            process_put(req);
            _total_size += req.meta.shard_len();
        }
    }

    // execute requests
    void stress() {
        bench::IoRequest req;

        _start_timestamp = base::gettimeofday_us();
        while (true) {
            if (_generator->produce(&req) < 0) {
                break;
            }
            _total_requests++;

            auto req_type = req.req.type;
            switch (req_type) {
            case bench::ReqType::GET:
                process_get(req);
                break;
            case bench::ReqType::PUT:
                process_put(req);
                break;
            case bench::ReqType::REMOVE:
                process_remove(req);
                break;
            default:
                assert(0);
                break;
            }

            // update request count
            _request_count[static_cast<int>(req_type)]++;
        }
        _finish_timestamp = base::gettimeofday_us();
    }

private:
    void on_hit(uint64_t vbid, const pb::ShardMeta& meta, const base::IOBuf& buf) {
        _hit_count++;

        LOG(TRACE) << "get bid " << vbid << " hits";
    }

    void process_get(bench::IoRequest& req) {
        uint64_t vbid = req.req.key;
        auto shard_id = ShardId{_volume_id, vbid, _shard_index};
        LOG(TRACE) << "process get blob " << vbid;

        // try get from cache
        bool is_hit = false;
        bool shard_exist = false;
        int ret = _cache_mgr->exist(shard_id, &shard_exist);
        if (ret != AIE_OK || !shard_exist) {
            is_hit = false;
        } else {
            auto s = _cache_mgr->lookup(shard_id, {}, &req.meta, &req.data);
            is_hit = (s == AIE_OK);
        }

        if (is_hit) {
            // validate get result
            if (FLAGS_validate) {
                if (FLAGS_bench_use_default_shard_size) {
                    assert(req.meta.shard_crc() == _default_crc);
                    assert(req.meta.shard_len() == _default_len);
                    assert(is_data_correct(req.meta, req.data));
                } else {
                    auto& meta = _blob_meta_container[vbid];
                    assert(is_meta_identical(req.meta, meta));
                    assert(is_data_correct(req.meta, req.data));
                }
            }
            on_hit(vbid, req.meta, req.data);
        } else {
            // try put to cache
            auto fetch_func = [this, vbid](const ShardId& id, pb::ShardMeta* meta, base::IOBuf* data) -> int {
                if (FLAGS_bench_use_default_shard_size) {
                    meta->set_shard_len(_default_len);
                    meta->set_shard_crc(_default_crc);
                    *data = _default_data;
                } else {
                    size_t len = _blob_meta_container[vbid].shard_len();
                    *meta = _blob_meta_container[vbid];
                    *data = _data_gallery[len];
                }
                return AIE_OK;
            };

            fetch(shard_id, std::move(fetch_func), true, &req.meta, &req.data);
        }
    }

    int fetch(const ShardId& shard_id,
                            std::optional<FetchDiskFunc> get_from_disk,
                            bool is_try_admit,
                            pb::ShardMeta* meta,
                            base::IOBuf* data) {
        int res = AIE_OK;

        //1. lookup in cache
        LookupOptions options;
        options.prefetch = get_from_disk;
        res = _cache_mgr->lookup(shard_id, std::move(options), meta, data);
        if (res == AIE_OK) {
            return res;
        } else if (res == AIE_INPROGRESS) {
            // data is fetching, get data from secondary storage this time, and don't admit to cache
            is_try_admit = false;
        }

        //2. failed to get data from cache(any error, include NOT_EXIST, AIE_INPROGRESS),
        //    get from disk directly
        if (get_from_disk.has_value()) {
            res = (*get_from_disk)(shard_id, meta, data);
            if (res != AIE_OK) {
                LOG(WARNING) << "get shard failed from disk, error:" << res;
                return res;
            } else {
                //3. admit to cache if adequate
                if (is_try_admit) {
                    (void)admit(shard_id, *meta, *data);
                }
            }
        }

        return res;
    }

    int admit(const ShardId& shard_id, const pb::ShardMeta& meta, const base::IOBuf& data) {
        int res = AIE_OK;

        bool is_shard_tracked = _cache_mgr->is_tracked(shard_id);
        if (is_shard_tracked) {
            res = _cache_mgr->insert(shard_id, meta, data);
        } else {
            _cache_mgr->_access_frequency_mgr->update_frequency(shard_id);
            if (!_cache_mgr->_admission_policy->accept(shard_id)) {
                return res;
            }

            res = _cache_mgr->insert(shard_id, meta, data);
            if (res == AIE_OK) {
                _cache_mgr->_access_frequency_mgr->reset_frequency(shard_id);
            }
        }
        return res;
    }

    void process_put(bench::IoRequest& req) {
        LOG(TRACE) << "process put bid " << req.req.key;
        if (!FLAGS_validate) {
           return;
        }

        if (!FLAGS_bench_use_default_shard_size) {
            size_t len = req.meta.shard_len();
            if (_data_gallery.count(len) == 0) {
                // generate data
                _data_gallery[len] = dn::BufferGen::gen(len);
                _crc_gallery[len] = dn::build_crc(_data_gallery[len]);
            }

            req.meta.set_shard_crc(_crc_gallery[len]);
            _blob_meta_container[req.req.key] = req.meta;
        }
    }

    void process_remove(bench::IoRequest& req) {
        auto vbid = req.req.key;
        _cache_mgr->remove(dn::ShardId(_volume_id, vbid, _shard_index));
        if (FLAGS_validate && !FLAGS_bench_use_default_shard_size) {
            _blob_meta_container.erase(vbid);
        }
    }

private:
    size_t _hit_count;       // number of get hit
    size_t _total_requests;  // total number of GET/PUT/REMOVE request
    size_t _total_size;      // size of the underlying virtual VLET
    std::vector<size_t> _request_count;

    std::unique_ptr<bench::WorkloadGenerator> _generator;
    std::shared_ptr<dn::CacheManager> _cache_mgr;
    uint64_t _start_timestamp;   // timestamp when initial blobs put
    uint64_t _finish_timestamp;  // timestamp all request finish

    uint64_t _volume_id;
    int32_t _shard_index;

    std::unordered_map<uint64_t, pb::ShardMeta> _blob_meta_container;
    std::unordered_map<size_t, base::IOBuf> _data_gallery;  // to minimize cost of data generation
    std::unordered_map<size_t, uint32_t> _crc_gallery;

    uint64_t _default_len{FLAGS_default_shard_size};
    base::IOBuf _default_data{dn::BufferGen::gen(_default_len)};
    uint32_t _default_crc{dn::build_crc(_default_data)};
};

void run_cache_stress_test() {
    std::vector<std::thread> threads;
    std::vector<std::shared_ptr<SsdCacheStress>> testers;
    uint64_t start_timestamp;   // timestamp after initial blobs loaded
    uint64_t finish_timestamp;  // timestamp all request finish
    CacheDisksConf conf;

    if (FLAGS_num_thread == 0) {
        return;
    }

    aries::pb::DiskConfigure* disk_conf  = conf.add_disk_list();
    disk_conf->set_disk_path(FLAGS_cache_path);
    disk_conf->set_capacity_mb(FLAGS_cache_capacity);

    std::shared_ptr<dn::CacheManager> cache_mgr = std::make_shared<dn::CacheManager>();
    cache_mgr->set_cache_disks_conf(conf);
    auto ret = cache_mgr->start(FLAGS_cache_engine_type, FLAGS_cache_evict_policy_type);
    assert(ret == 0);

    for (int i = 0; i < FLAGS_num_thread; i++) {
        testers.emplace_back(std::make_shared<SsdCacheStress>(cache_mgr));
        testers[i]->set_volume_id(i + 1);  // each thread use different volume_id
        testers[i]->set_shard_index(i + 1);
        testers[i]->init_blobs();  // load initial blobs
    }

    // start test threads
    start_timestamp = base::gettimeofday_us();
    for (int i = 0; i < FLAGS_num_thread; i++) {
        threads.emplace_back([=] { testers[i]->stress(); });
        LOG(TRACE) << "test thread " << i << " started";
    }

    for (int i = 0; i < FLAGS_num_thread; i++) {
        threads[i].join();
        LOG(TRACE) << "test thread " << i << " joined";
    }
    finish_timestamp = base::gettimeofday_us();

    // report result
    // cache ratio:
    size_t total_data_size = 0;
    for (int i = 0; i < FLAGS_num_thread; i++) {
        total_data_size += testers[i]->total_size();
    }
    size_t total_cache_size = FLAGS_cache_capacity * common::MB;
    double cache_ratio = (double)total_cache_size / (double)total_data_size;
    std::cout << "cache ratio: " << cache_ratio << "\n"
                << "    cache size: " << total_cache_size << "\n"
                << "    vlet size: " << total_data_size << std::endl;

    // total IO requests;
    size_t total_requests = testers[0]->total_requests() * FLAGS_num_thread;
    std::cout << "requests: " << total_requests << std::endl;

    // hit ratio:
    double hit_ratio = .0;
    for (int i = 0; i < FLAGS_num_thread; i++) {
        hit_ratio += testers[i]->hit_ratio();
    }
    hit_ratio /= FLAGS_num_thread;

    std::cout << "hits ratio: " << hit_ratio << std::endl;

    uint64_t time_span = finish_timestamp - start_timestamp;
    std::cout << "test duration: " << time_span << "us" << std::endl;

    // through put:
    // throughput = num_total_request / (measured_time + num_miss_request * L)
    // L stands for HDD record read latency (in us).
    std::string throughput = base::string_printf("(%lu / (%lf + %lf * L))", total_requests, (double)time_span / 1000000.0, (1.0 - hit_ratio) * (double)total_requests / 1000000.0);
    std::cout << "throughput: " << throughput << "iops" << std::endl;

    // latency:
    std::string latency = base::string_printf("(%lf + %lf * L)",
                (double)time_span * (double)FLAGS_num_thread / (double)total_requests,
                (double)(1.0 - hit_ratio) * (double)FLAGS_num_thread);
    std::cout << "latency: " << latency << "us" << std::endl;

}

}  // namespace cache_bench
}  // namespace datanode
}  // namespace aries

int main(int argc, char* argv[]) {
    google::ParseCommandLineFlags(&argc, &argv, false);
    aries::common::init_comlog();

    auto reloader = std::make_unique<aries::common::ConfigReloader>(FLAGS_flagfile);
    aries::common::g_config_reloader = reloader.get();

    bench::run_cache_stress_test();

    return 0;
}
