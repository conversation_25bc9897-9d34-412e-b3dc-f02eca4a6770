#!/usr/bin/env python
# -*- coding: utf-8 -*-
########################################################################
# 
# Copyright (c) 2021 Baidu.com, Inc. All Rights Reserved
# 
########################################################################
 
"""
File: config.py
Author: houjinchao(<EMAIL>)
Date: 2021/06/22 08:21:31
"""

region = [
    'bdbl',
    'bj02',
    'dxm-bjdd',
    'dxm-gzbh',
    'cq02',
    'gzhxy',
    'hkg03',
    'sin03',
    'szth',
    'whgg',
    'yq01'
]

receiver = "<EMAIL>"

server_url = "http://10.228.2.35:8777/ConsoleService/run_cmd?cluster=%s&op=list_node&file=%s"
hi_robot_url = "http://apiin.im.baidu.com/api/msg/groupmsgsend?access_token=d97d1cac7520385a6ca2a1eea2b42d37a"