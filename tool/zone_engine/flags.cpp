#include "flags.h"

namespace aries {
namespace datanode {
namespace zone_engine_measure {

// data generate
DEFINE_uint64(data_total_number, 1000000, "total number of generated data");
DEFINE_bool(random_io_size, false, "whether the test io size is randomly generated");
DEFINE_string(random_io_size_interval, "", "Interval of random io size (KB), closed interval");
DEFINE_string(io_size_and_category, "63,126,128", "IO size (KB) and classification of test data");
DEFINE_string(io_category_percentage, "20,60,20", "proportion of various data (%)");
DEFINE_string(lifespan_distribution, "60,3600,3600,86400,86400", "lifespan distribution of test data (s), "
                                                                 "every two numbers are an interval, left closed and right open");
DEFINE_string(lifespan_distribution_percentage, "60,30,10" , "number percentage of test data corresponding to each lifespan interval");

// operation execution
// ### execute according to duration ###
DEFINE_bool(put_until_time_runs_out, true, "whether to execute put operation continuously until the time runs out");
DEFINE_bool(get_until_time_runs_out, true, "whether to execute get operation continuously until the time runs out");
DEFINE_bool(remove_until_time_runs_out, true, "whether to execute remove operation continuously until the time runs out");
DEFINE_uint64(put_duration_time, 1000000, "execution time of put operation (s)");
DEFINE_uint64(get_duration_time, 1000000, "execution time of get operation (s)");
DEFINE_uint64(remove_duration_time, 1000000, "execution time of remove operation (s)");
// ### executed by times ###
DEFINE_uint64(put_times, 1000000, "the number of times the put operation is executed");
DEFINE_uint64(get_times, 1000000, "the number of times the get operation is executed");
DEFINE_uint64(remove_times, 1000000, "the number of times the remove operation is executed");
// ### number of operations between two check operations ###
DEFINE_uint64(put_check_gap, 1, "number of put operations between two check operations");
DEFINE_uint64(get_check_gap, 1, "number of get operations between two check operations");
DEFINE_uint64(remove_check_gap, 1, "number of remove operations between two check operations");

// ### check quit switch ###
DEFINE_bool(check_put_quit, false, "check whether the put operation should exit rather than be controlled by the total duration");
DEFINE_bool(check_get_quit, false, "check whether the get operation should exit rather than be controlled by the total duration");
DEFINE_bool(check_remove_quit, false, "check whether the remove operation should exit rather than be controlled by the total duration");

// ### single time op num ###
DEFINE_uint64(single_time_put_num, 1, "number of blobs in a single put");
DEFINE_uint64(single_time_get_num, 1, "number of blobs in a single get");

// ### make sure to get the data that has been put ###
DEFINE_uint64(get_delay_time_lower_bound, 5, "lower bound of get operation delay time");
DEFINE_uint64(get_delay_time_upper_bound, 10, "upper bound of delay time of get operation");
DEFINE_uint64(get_retry_times, 10, "maximum number of retries for get operation");
DEFINE_uint64(min_blob_num_same_time, 10, "the minimum number of blobs allowed for get operation at the same time");

// ### IO distribution type ###
DEFINE_int32(io_dist_type, 1, "IO distribution type,0:seq; 1:random; 2:zipf");

// rewrite and latency
// ### rewrite stats ###
DEFINE_uint64(print_or_dump_stats_time_gap, 10, "the time gap for print or dump rewrite stats");
DEFINE_bool(display_in_mb, false, "show the number of bytes written in mb");
DEFINE_string(disk_stats_dump_path, "./disk_stats_dump.md", "disk stats dump path");
DEFINE_string(dn_stats_dump_path, "./dn_stats_dump.md", "datanode stats dump path");
DEFINE_bool(dump_or_print_disk_stats, false, "if dump/print stats of disk");

// ### rewrite control ###
DEFINE_double(valid_data_percent_in_disk, 70, "percentage of disk valid data at the time of rewrite test");
DEFINE_double(exceed_value_of_valid_data_percent_in_disk, 0.4, "exceeded value of valid data percentage,"
                                                                "ensure that the percentage of valid data is maintained at a reasonable level");
DEFINE_double(valid_data_percent_upper_bound, 72.0, "upper bound of the range for the percentage of valid data on the disk");
DEFINE_uint64(need_add_or_sub_qps_disk_percent, 100, "when the percentage of the number of disks in the total number of disks reaches this value, "
                                                     "need to increase or decrease qps");
DEFINE_uint64(max_qps_for_rewrite_put, 100000, "maximum qps of put operation");
DEFINE_double(min_valid_data_growth_percent_per_duration, 2.0, "the minimum effective data growth percentage in each cycle, "
                                                               "if it is less than this value, you need to increase qps, otherwise it will not change");
DEFINE_uint64(max_qps_duration, 3600, "duration after reaching the maximum qps");
DEFINE_uint64(update_put_with_rewrite_qps_time_gap, 1, "time interval to update put qps");
DEFINE_uint64(zone_size_mb, 1024, "size of zone (mb)");
DEFINE_double(max_disk_usage_for_rewrite, 95.0, "maximum disk usage during rewrite testing");
DEFINE_double(exceed_value_of_max_disk_usage, 1.0, "the maximum disk usage allowed during the rewrite operation");
DEFINE_double(unreaches_value_to_max_disk_usage, 1.0, "when the downward offset of the maximum disk usage is within this range, "
                                                      "there is no need to modify the qps");
DEFINE_bool(adjust_qps_for_wa_test, false, "whether it is overwrite for wa test");

// ### latency value ###
DEFINE_uint64(print_or_dump_latency_time_gap, 10, "the time gap for print or dump latency values");
DEFINE_string(latency_value_dump_path, "./latency.md", "latency value dump path");
DEFINE_bool(avg_latency, false, "whether to count average latency");

// ### just dump markdown ###
DEFINE_bool(just_dump_markdown, true, "is it only dump markdown");

// ### wait until rpc callback completed ###
DEFINE_int32(wait_rpc_callback_completed_timespan_s, 3, "time to wait for rpc callback completion");

} // namespace aries
} // namespace datanode
} // zone_engine_measure