// Copyright (c) 2024, ByteDance Inc. All rights reserved.

#pragma once

#include <atomic>
#include <functional>
#include <memory>
#include <unordered_map>
#include <vector>

#include "byte/thread/timer_thread.h"

#include "byterpc_agent/errno.h"

namespace byterpc_agent {

class ServerRpcTask {
public:
    virtual ~ServerRpcTask() {}
    virtual void SetFailed(int error_code, const std::string& error_text) = 0;
};

class ServerRpcSessionManager;
class ServerRpcSession : public std::enable_shared_from_this<ServerRpcSession> {
public:
    static std::shared_ptr<ServerRpcSession> Create(uint64_t session_id,
                                                    ServerRpcSessionManager* manager) {
        return std::shared_ptr<ServerRpcSession>(new ServerRpcSession(session_id, manager));
    }

    ~ServerRpcSession() {
        for (std::unique_ptr<ServerRpcTask>& task : _tasks) {
            if (task) {
                task->SetFailed(-1, "Session destructs");
                task.reset();
            }
        }
    }

    uint64_t SessionId() const {
        return _session_id;
    }

    int Init(uint64_t task_num,
             const std::function<void(std::vector<std::unique_ptr<ServerRpcTask>>&&)>&
                 finish_session_callback,
             uint32_t session_timeout_ms,
             byte::TimerThread* timer_thread);

    void FinishTask(uint64_t task_id, std::unique_ptr<ServerRpcTask>&& task);

private:
    ServerRpcSession(uint64_t session_id, ServerRpcSessionManager* manager)
        : _session_id(session_id),
          _manager(manager),
          _pending_task_num(0),
          _error_code(ErrorCode::EOK),
          _error_text(),
          _mutex(),
          _timer_id(0),
          _tasks(),
          _finish_session_callback(),
          _timer_thread(nullptr) {}

    void HandleTimeout();

    void RemoveFromManager();

private:
    uint64_t _session_id;
    ServerRpcSessionManager* _manager;
    uint64_t _pending_task_num;
    int _error_code;
    std::string _error_text;
    std::mutex _mutex;
    uint64_t _timer_id;
    std::vector<std::unique_ptr<ServerRpcTask>> _tasks;
    std::function<void(std::vector<std::unique_ptr<ServerRpcTask>>&&)> _finish_session_callback;
    byte::TimerThread* _timer_thread;
};

class ServerRpcSessionManager {
public:
    ServerRpcSessionManager() : _session_id(0), _session_map(), _mutex() {}

    std::shared_ptr<ServerRpcSession> CreateSession();

    void RemoveSession(uint64_t session_id);

    std::shared_ptr<ServerRpcSession> GetSession(uint64_t session_id);

private:
    std::atomic<uint64_t> _session_id;
    std::unordered_map<uint64_t, std::shared_ptr<ServerRpcSession>> _session_map;
    std::mutex _mutex;
};

}  // namespace byterpc_agent
