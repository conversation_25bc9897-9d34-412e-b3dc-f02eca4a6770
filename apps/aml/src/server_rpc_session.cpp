// Copyright (c) 2024, ByteDance Inc. All rights reserved.

#include "server_rpc_session.h"

#include "byterpc/util/logging.h"

namespace byterpc_agent {

int ServerRpcSession::Init(uint64_t task_num,
                           const std::function<void(std::vector<std::unique_ptr<ServerRpcTask>>&&)>&
                               finish_session_callback,
                           uint32_t session_timeout_ms,
                           byte::TimerThread* timer_thread) {
    BYTERPC_CHECK(finish_session_callback);
    BYTERPC_CHECK(task_num);
    if (BYTERPC_LIKELY(timer_thread)) {
        byte::Status status = timer_thread->ScheduleOneShot(
            [shared_this = shared_from_this()] { shared_this->HandleTimeout(); },
            session_timeout_ms,
            &_timer_id);
        if (BYTERPC_UNLIKELY(!status.ok())) {
            BYTERPC_LOG(ERROR) << "Fail to schedule timeout for session " << _session_id
                               << ", error: " << status.ToString();
            return -1;
        }
        _timer_thread = timer_thread;
    }
    _tasks.resize(task_num);
    _pending_task_num = task_num;
    _finish_session_callback = finish_session_callback;
    return 0;
}

void ServerRpcSession::FinishTask(uint64_t task_id, std::unique_ptr<ServerRpcTask>&& task) {
    BYTERPC_CHECK_LT(task_id, _tasks.size());
    BYTERPC_CHECK(!_tasks[task_id]);
    BYTERPC_CHECK_GT(_pending_task_num, 0U);
    std::unique_lock<std::mutex> lock(_mutex);
    // Others have failed this session, probably due to session timeout. We just need to follow
    // the same error to handle current task.
    if (BYTERPC_UNLIKELY(_error_code != ErrorCode::EOK)) {
        lock.unlock();
        task->SetFailed(_error_code, _error_text);
        return;
    }
    _tasks[task_id] = std::move(task);
    // we have finished all tasks and then call the `_finish_session_callback` callback
    if (--_pending_task_num == 0) {
        lock.unlock();
        if (BYTERPC_LIKELY(_timer_thread)) {
            _timer_thread->Unschedule(_timer_id);
        }
        _finish_session_callback(std::move(_tasks));
        _finish_session_callback = nullptr;
        RemoveFromManager();
    }
}

void ServerRpcSession::HandleTimeout() {
    std::unique_lock<std::mutex> lock(_mutex);

    if (BYTERPC_LIKELY(_pending_task_num > 0)) {
        _error_code = ETIMEDOUT;
        _error_text = "Session timeout on server side, session id: " + std::to_string(_session_id);
        RemoveFromManager();
    }
    lock.unlock();

    // Set failed to all finished tasks
    for (std::unique_ptr<ServerRpcTask>& task : _tasks) {
        if (task) {
            task->SetFailed(_error_code, _error_text);
            task.reset();
        }
    }
}

void ServerRpcSession::RemoveFromManager() {
    if (BYTERPC_LIKELY(_manager)) {
        _manager->RemoveSession(_session_id);
        _manager = nullptr;
    }
}

std::shared_ptr<ServerRpcSession> ServerRpcSessionManager::CreateSession() {
    std::shared_ptr<ServerRpcSession> session =
        ServerRpcSession::Create(_session_id.fetch_add(1, std::memory_order_relaxed), this);
    std::unique_lock<std::mutex> lock(_mutex);
    if (BYTERPC_UNLIKELY(!_session_map.emplace(session->SessionId(), session).second)) {
        BYTERPC_LOG(ERROR) << "Fail to create session " << session->SessionId()
                           << " because session id conflicts";
        return nullptr;
    }
    return session;
}

void ServerRpcSessionManager::RemoveSession(uint64_t session_id) {
    std::unique_lock<std::mutex> lock(_mutex);
    _session_map.erase(session_id);
}

std::shared_ptr<ServerRpcSession> ServerRpcSessionManager::GetSession(uint64_t session_id) {
    std::unique_lock<std::mutex> lock(_mutex);
    auto it = _session_map.find(session_id);
    if (BYTERPC_UNLIKELY(it == _session_map.end())) {
        return nullptr;
    }
    return it->second;
}

}  // namespace byterpc_agent
