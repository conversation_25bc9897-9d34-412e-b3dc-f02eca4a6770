// Copyright (c) 2024, ByteDance Inc. All rights reserved.

#include "data_transfer_agent.h"

#include "byterpc/callback.h"
#include "byterpc/util/logging.h"
#include "byterpc/util/timestamp.h"

#include "client_rpc_session.h"
#include "util.h"

namespace byterpc_agent {

static void FreeWrapper(void* addr, void* block_ref) {
    if (block_ref) {
        delete static_cast<byterpc::IOBlockRef*>(block_ref);
    } else {
        free(addr);
    }
}

std::shared_ptr<RpcFutureImpl> DataTransferAgent::SendRpcImpl(
    std::shared_ptr<Channel>&& channel,
    const std::vector<RpcIovec>& send_data,
    const std::vector<RpcIovec>& send_data_zc,
    const std::vector<RpcIovec>& recv_buf_zc,
    uint32_t timeout_ms,
    uint32_t data_worker_num) {
    if (data_worker_num == 0) {
        data_worker_num = _data_worker_threads.size();
    }
    std::vector<Channel::Match> matches = channel->SelectMatches(data_worker_num);
    std::shared_ptr<ClientRpcSession> session = std::make_shared<ClientRpcSession>(data_worker_num);
    _data_worker_threads[matches[0].data_worker_id]->Invoke([=]() {
        if (data_worker_num > 1 || !send_data_zc.empty()) {
            // If there are more than one data worker or this is a rdma write request, we need to do
            // preparation first.
            this->SendPrepareRequest(session,
                                     matches,
                                     _log_id.fetch_add(1, std::memory_order_relaxed),
                                     send_data,
                                     send_data_zc,
                                     recv_buf_zc,
                                     timeout_ms);

        } else {
            this->SendDataTransferRequest(session,
                                          false,
                                          0,
                                          0,
                                          matches[0],
                                          _log_id.fetch_add(1, std::memory_order_relaxed),
                                          send_data,
                                          {},
                                          {},
                                          recv_buf_zc,
                                          timeout_ms);
        }
    });
    return session;
}

void DataTransferAgent::SendPrepareRequest(std::shared_ptr<ClientRpcSession> session,
                                           const std::vector<Channel::Match>& channel_matches,
                                           uint64_t log_id,
                                           const std::vector<RpcIovec>& send_data,
                                           const std::vector<RpcIovec>& send_data_zc,
                                           const std::vector<RpcIovec>& recv_buf_zc,
                                           uint32_t timeout_ms) {
    byterpc::Builder builder;
    byterpc::Controller* controller =
        builder.CreateSessionController(byterpc::PROTOCOL_PROTOBUF_BYTE_STD, timeout_ms * 1000);
    controller->SetLogId(log_id);

    proto::PrepareRequest request;
    request.set_task_num(channel_matches.size());
    request.set_timeout_ms(2 * timeout_ms);
    for (const RpcIovec& data : send_data_zc) {
        request.add_prepare_data_zc_lengths(data.len);
    }

    uint64_t start_time = byterpc::util::GetCurrentTimeInMs();
    proto::PrepareResponse* resp = new proto::PrepareResponse();
    google::protobuf::Closure* done =
        byterpc::NewCallback(this,
                             &DataTransferAgent::HandlePrepareResponse,
                             session,
                             channel_matches,
                             send_data,
                             send_data_zc,
                             recv_buf_zc,
                             controller,
                             resp,
                             start_time,
                             timeout_ms);
    proto::DataTransferService_Stub stub(channel_matches[0].byterpc_channel.get());
    stub.Prepare(controller, &request, resp, done);
}

void DataTransferAgent::HandlePrepareResponse(std::shared_ptr<ClientRpcSession> session,
                                              std::vector<Channel::Match> channel_matches,
                                              std::vector<RpcIovec> send_data,
                                              std::vector<RpcIovec> send_data_zc,
                                              std::vector<RpcIovec> recv_buf_zc,
                                              byterpc::Controller* controller,
                                              proto::PrepareResponse* resp,
                                              uint64_t start_time_in_ms,
                                              uint32_t timeout_ms) {
    BYTE_DEFER(delete resp);
    if (BYTERPC_UNLIKELY(controller->Failed())) {
        int err_code = controller->ErrorCode();
        BYTERPC_DLOG(WARNING) << "Prepare rpc fails, error code: " << controller->ErrorCode()
                              << ", error text: " << controller->ErrorText()
                              << ", log_id: " << controller->LogId();
        session->FinishTask(0, {err_code, controller->ErrorText(), {}, {}});
        ReleaseRpcIovecs(send_data);
        ReleaseRpcIovecs(send_data_zc);
        ReleaseRpcIovecs(recv_buf_zc);
        return;
    }

    uint64_t prepare_used_time = byterpc::util::GetCurrentTimeInMs() - start_time_in_ms;
    // If we have already run time out, we just set a small timeout (i.e. 1us) for following rpc,
    // which will lead to timeout failure.
    uint64_t left_time = timeout_ms > prepare_used_time ? timeout_ms - prepare_used_time : 1UL;
    std::vector<byterpc::RdmaBufInfo> remote_recv_buf_infos =
        controller->GetRemoteRdmaRecvBufInfos();

    // split request
    uint64_t request_session_id = resp->session_id();
    size_t send_quotient = send_data.size() / channel_matches.size();
    size_t send_remainder = send_data.size() % channel_matches.size();
    auto send_start_iter = send_data.begin();
    for (size_t i = 0; i < channel_matches.size(); ++i) {
        size_t send_iovec_num = i < send_remainder ? send_quotient + 1 : send_quotient;
        std::vector<RpcIovec> partial_send_data(send_start_iter, send_start_iter + send_iovec_num);
        Channel::Match match = channel_matches[i];
        uint64_t log_id = controller->LogId();

        // rdma write/read request is assigned only to the first data worker
        const std::vector<RpcIovec>& partial_send_data_zc =
            (i == 0) ? send_data_zc : std::vector<RpcIovec>();
        const std::vector<RpcIovec>& partial_recv_data_zc =
            (i == 0) ? recv_buf_zc : std::vector<RpcIovec>();
        const std::vector<byterpc::RdmaBufInfo>& partial_remote_recv_buf_infos =
            (i == 0) ? remote_recv_buf_infos : std::vector<byterpc::RdmaBufInfo>();

        _data_worker_threads[match.data_worker_id]->Invoke([=]() {
            this->SendDataTransferRequest(session,
                                          true,
                                          request_session_id,
                                          i,
                                          match,
                                          log_id,
                                          partial_send_data,
                                          partial_send_data_zc,
                                          partial_remote_recv_buf_infos,
                                          partial_recv_data_zc,
                                          left_time);
        });
        send_start_iter += send_iovec_num;
    }
}

void DataTransferAgent::SendDataTransferRequest(
    std::shared_ptr<ClientRpcSession> session,
    bool has_session_id,
    uint64_t request_session_id,
    uint32_t task_id,
    const Channel::Match& channel_match,
    uint64_t log_id,
    const std::vector<RpcIovec>& send_data,
    const std::vector<RpcIovec>& send_data_zc,
    const std::vector<byterpc::RdmaBufInfo>& remote_recv_buf_infos,
    const std::vector<RpcIovec>& recv_buf_zc,
    uint32_t timeout_ms) {
    byterpc::Builder builder;
    byterpc::Controller* controller =
        builder.CreateSessionController(byterpc::PROTOCOL_PROTOBUF_BYTE_STD, timeout_ms * 1000);
    controller->SetLogId(log_id);

    proto::DataTransferRequest request;
    if (has_session_id) {
        request.set_session_id(request_session_id);
    }
    request.set_task_id(task_id);

    // install outcoming attachment (send_data)
    for (const RpcIovec& iovec : send_data) {
        request.add_send_data_lengths(iovec.len);
    }
    controller->InstallOutgoingAttachment(
        RpcIovecToIOBuf(send_data, (_agent_options.trans_type != TransportType::TYPE_KERNEL_TCP)));

    // install rdma send buf (send_data_zc)
    BYTERPC_CHECK_EQ(send_data_zc.size(), remote_recv_buf_infos.size());
    for (size_t i = 0; i < send_data_zc.size(); ++i) {
        byterpc::MemoryDescriptor descriptor;
        descriptor.memory_id = byterpc::MemoryId::UNIFY_ALLOCATED;
        descriptor.virtual_address = send_data_zc[i].addr;
        descriptor.length = send_data_zc[i].len;
        descriptor.param = send_data_zc[i].deleter_param;
        byterpc::IOBuf buf;
        buf.append(
            std::move(byterpc::IOBlockRef::TakeOwnership(descriptor, send_data_zc[i].deleter)));
        BYTERPC_CHECK_LE(buf.size(), remote_recv_buf_infos[i].size);
        controller->InstallRdmaSendBuf(buf, remote_recv_buf_infos[i]);
    }

    // install rdma recv buf (recv_buf_zc)
    for (size_t i = 0; i < recv_buf_zc.size(); ++i) {
        byterpc::MemoryDescriptor descriptor;
        descriptor.memory_id = byterpc::MemoryId::UNIFY_ALLOCATED;
        descriptor.virtual_address = recv_buf_zc[i].addr;
        descriptor.length = recv_buf_zc[i].len;
        descriptor.param = recv_buf_zc[i].deleter_param;
        controller->InstallRdmaRecvBuf(
            byterpc::IOBlockRef::TakeOwnership(descriptor, recv_buf_zc[i].deleter));
    }
    proto::DataTransferResponse* resp = new proto::DataTransferResponse();
    google::protobuf::Closure* done =
        byterpc::NewCallback(this,
                             &DataTransferAgent::HandleDataTransferResponse,
                             session,
                             task_id,
                             channel_match,
                             controller,
                             resp);
    proto::DataTransferService_Stub stub(channel_match.byterpc_channel.get());
    stub.DataTransfer(controller, &request, resp, done);
}

void DataTransferAgent::HandleDataTransferResponse(std::shared_ptr<ClientRpcSession> session,
                                                   uint32_t task_id,
                                                   Channel::Match channel_match,
                                                   byterpc::Controller* controller,
                                                   proto::DataTransferResponse* resp) {
    BYTE_DEFER(delete resp);
    if (BYTERPC_UNLIKELY(controller->Failed())) {
        int err_code = controller->ErrorCode();
        BYTERPC_DLOG(WARNING) << "DataTransfer rpc fails, error code: " << controller->ErrorCode()
                              << ", error text: " << controller->ErrorText()
                              << ", log_id: " << controller->LogId();
        session->FinishTask(task_id, {err_code, controller->ErrorText(), {}, {}});
        return;
    }

    // handle incoming attachment (recv_data)
    std::vector<RpcIovec> recv_data;
    if (controller->HasIncomingAttachment()) {
        recv_data.reserve(resp->send_data_lengths_size());
        byterpc::IOBuf in_attachment;
        controller->MoveIncomingAttachment(&in_attachment);
        for (size_t i = 0; i < resp->send_data_lengths_size(); ++i) {
            size_t data_length = resp->send_data_lengths(i);
            if (in_attachment.front_ref().size() >= data_length) {
                byterpc::IOBlockRef* ref = new byterpc::IOBlockRef(in_attachment.front_ref());
                ref->pop_back(ref->size() - data_length);
                in_attachment.pop_front(data_length);
                recv_data.push_back({ref->data(), data_length, &FreeWrapper, ref});
            } else {
                void* addr = malloc(data_length);
                size_t len = in_attachment.cut_to(static_cast<char*>(addr), data_length);
                BYTERPC_CHECK_EQ(len, data_length);
                recv_data.push_back({addr, data_length, &FreeWrapper, nullptr});
            }
        }
        BYTERPC_CHECK_EQ(in_attachment.size(), 0);
    } else {
        BYTERPC_CHECK_EQ(resp->send_data_lengths_size(), 0);
    }

    // handle rdma recv data (recv_data_zc)
    std::vector<byterpc::IOBlockRef> refs = controller->ReleaseAllRdmaReceivedBufs();
    std::vector<RpcIovec> recv_data_zc(refs.size());
    for (size_t i = 0; i < recv_data_zc.size(); ++i) {
        byterpc::IOBlockRef* ref = new byterpc::IOBlockRef(refs[i]);
        refs[i].reset();
        recv_data_zc[i] = {ref->data(), ref->size(), &FreeWrapper, ref};
    }

    session->FinishTask(task_id, {ErrorCode::EOK, {}, recv_data, recv_data_zc});
}

}  // namespace byterpc_agent