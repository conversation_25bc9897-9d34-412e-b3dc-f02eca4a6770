// Copyright (c) 2024, ByteDance Inc. All rights reserved.

#pragma once

#include "basic_agent.h"

#include "byterpc/builder.h"
#include "byterpc/controller.h"

#include "proto/byterpc_agent.pb.h"

namespace byterpc_agent {

class DataTransferAgent : public BasicAgent {
public:
    DataTransferAgent() : _log_id(0) {}
    ~DataTransferAgent() {}

    std::shared_ptr<RpcFutureImpl> SendRpcImpl(std::shared_ptr<Channel>&& channel,
                                               const std::vector<RpcIovec>& send_data,
                                               const std::vector<RpcIovec>& send_data_zc,
                                               const std::vector<RpcIovec>& recv_buf_zc,
                                               uint32_t timeout_ms,
                                               uint32_t data_worker_num) override;

    void SendPrepareRequest(std::shared_ptr<ClientRpcSession> session,
                            const std::vector<Channel::Match>& channel_matches,
                            uint64_t log_id,
                            const std::vector<RpcIovec>& send_data,
                            const std::vector<RpcIovec>& send_data_zc,
                            const std::vector<RpcIovec>& recv_buf_zc,
                            uint32_t timeout_ms);

    void HandlePrepareResponse(std::shared_ptr<ClientRpcSession> session,
                               std::vector<Channel::Match> channel_matches,
                               std::vector<RpcIovec> send_data,
                               std::vector<RpcIovec> send_data_zc,
                               std::vector<RpcIovec> recv_buf_zc,
                               byterpc::Controller* controller,
                               proto::PrepareResponse* resp,
                               uint64_t start_time_in_ms,
                               uint32_t timeout_ms);

    void SendDataTransferRequest(std::shared_ptr<ClientRpcSession> session,
                                 bool has_session_id,
                                 uint64_t request_session_id,
                                 uint32_t task_id,
                                 const Channel::Match& channel_match,
                                 uint64_t log_id,
                                 const std::vector<RpcIovec>& send_data,
                                 const std::vector<RpcIovec>& send_data_zc,
                                 const std::vector<byterpc::RdmaBufInfo>& remote_recv_buf_infos,
                                 const std::vector<RpcIovec>& recv_buf_zc,
                                 uint32_t timeout_ms);

    void HandleDataTransferResponse(std::shared_ptr<ClientRpcSession> session,
                                    uint32_t task_id,
                                    Channel::Match channel_match,
                                    byterpc::Controller* controller,
                                    proto::DataTransferResponse* resp);

private:
    std::atomic<uint64_t> _log_id;
};

}  // namespace byterpc_agent