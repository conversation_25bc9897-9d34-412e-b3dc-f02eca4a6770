// Copyright (c) 2024, ByteDance Inc. All rights reserved.

#include "util.h"

#include <atomic>

#include "gtest/gtest.h"

namespace byterpc_agent {

std::atomic<uint64_t> g_allocated_iovec_size{0};

void FreeWrapper(void* addr, void* length) {
    free(addr);
    g_allocated_iovec_size.fetch_sub(reinterpret_cast<size_t>(length), std::memory_order_relaxed);
}

void* MallocWrapper(size_t length) {
    g_allocated_iovec_size.fetch_add(length, std::memory_order_relaxed);
    return malloc(length);
}

TEST(UtilTest, ReleaseRpcIovecs) {
    std::vector<RpcIovec> iovecs = {
        {MallocWrapper(1), 1, &FreeWrapper, reinterpret_cast<void*>(1)},
        {MallocWrapper(2), 2, &FreeWrapper, reinterpret_cast<void*>(2)},
        {MallocWrapper(3), 3, &FreeWrapper, reinterpret_cast<void*>(3)},
    };
    ASSERT_EQ(g_allocated_iovec_size.load(), 1 + 2 + 3);
    ReleaseRpcIovecs(iovecs);
    ASSERT_EQ(g_allocated_iovec_size.load(), 0);
}

TEST(UtilTest, RpcIovecToIOBuf) {
    {
        std::vector<RpcIovec> iovecs = {
            {MallocWrapper(1), 1, &FreeWrapper, reinterpret_cast<void*>(1)},
            {MallocWrapper(4 * 1024 * 1024UL),
             4 * 1024 * 1024UL,
             &FreeWrapper,
             reinterpret_cast<void*>(4 * 1024 * 1024UL)},
        };
        std::unique_ptr<byterpc::IOBuf> IOBuf = RpcIovecToIOBuf(iovecs, false);
        ASSERT_EQ(IOBuf->size(), 4 * 1024 * 1024UL + 1);
        ASSERT_EQ(IOBuf->block_num(), 2);
        ASSERT_EQ(g_allocated_iovec_size.load(), IOBuf->size());
    }

    {
        std::vector<RpcIovec> iovecs = {
            {MallocWrapper(1), 1, &FreeWrapper, reinterpret_cast<void*>(1)},
            {MallocWrapper(4 * 1024 * 1024UL),
             4 * 1024 * 1024UL,
             &FreeWrapper,
             reinterpret_cast<void*>(4 * 1024 * 1024UL)},
        };
        std::unique_ptr<byterpc::IOBuf> IOBuf = RpcIovecToIOBuf(iovecs, true);
        ASSERT_EQ(IOBuf->size(), 4 * 1024 * 1024UL + 1);
        ASSERT_EQ(IOBuf->block_num(), 3);
        ASSERT_EQ(g_allocated_iovec_size.load(), IOBuf->size());
    }

    ASSERT_EQ(g_allocated_iovec_size.load(), 0);
}

}  // namespace byterpc_agent

int main(int argc, char** argv) {
    testing::InitGoogleTest(&argc, argv);
    return RUN_ALL_TESTS();
}