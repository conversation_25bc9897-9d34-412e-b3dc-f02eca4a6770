// Copyright (c) 2024, ByteDance Inc. All rights reserved.

#include "data_transfer_service.h"

#include <thread>

#include "byterpc/controller.h"
#include "byterpc/thread/ev_thread_helper.h"
#include "byterpc/util/logging.h"
#include "gtest/gtest.h"

#include "byterpc_agent/byterpc.h"

namespace byterpc_agent {

class SimpleDone : public google::protobuf::Closure {
public:
    SimpleDone(byte::CountDownLatch* latch) : _latch(latch) {}
    ~SimpleDone() {}
    void Run() override {
        if (_latch) {
            _latch->CountDown();
        }
        delete this;
    }

private:
    byte::CountDownLatch* _latch;
};

class SimpleController : public byterpc::Controller {
public:
    SimpleController()
        : _send_bufs(), _to_recv_bufs(), _received_bufs(), _remote_recv_buf_infos() {}
    ~SimpleController() {}

    byterpc::util::EndPoint remote_side() override {
        return {};
    }

    byterpc::util::EndPoint local_side() override {
        return {};
    }

    void OnRecycle() override {}

    void DoRecycle() override {}

    void InstallRdmaSendBuf(const byterpc::IOBuf& send_buf,
                            const byterpc::RdmaBufInfo& remote_recv_buf_info) override {
        _send_bufs.push_back(std::make_pair(send_buf, remote_recv_buf_info));
    }

    void InstallRdmaRecvBuf(const byterpc::IOBlockRef& recv_buf) override {
        _to_recv_bufs.push_back(recv_buf);
    }

    std::vector<byterpc::IOBlockRef> ReleaseAllRdmaReceivedBufs() override {
        return std::move(_received_bufs);
    }

    std::vector<byterpc::RdmaBufInfo> GetRemoteRdmaRecvBufInfos() const override {
        return _remote_recv_buf_infos;
    }

public:
    std::vector<std::pair<byterpc::IOBuf, byterpc::RdmaBufInfo>> _send_bufs;
    std::vector<byterpc::IOBlockRef> _to_recv_bufs;
    std::vector<byterpc::IOBlockRef> _received_bufs;
    std::vector<byterpc::RdmaBufInfo> _remote_recv_buf_infos;
};

std::atomic<size_t> g_allocated_iovec_num(0);

void FreeWrapper(void* addr, void* useless) {
    free(addr);
    g_allocated_iovec_num.fetch_sub(1, std::memory_order_relaxed);
}

void* MallocWrapper(size_t length) {
    g_allocated_iovec_num.fetch_add(1, std::memory_order_relaxed);
    return malloc(length);
}

void MakeAttachment(byterpc::IOBuf* attachment, uint64_t iovec_num, uint64_t iovec_size) {
    for (uint64_t i = 0; i < iovec_num; ++i) {
        byterpc::MemoryDescriptor descriptor;
        descriptor.memory_id = byterpc::MemoryId::DEFAULT_ALLOCATED;
        descriptor.virtual_address = MallocWrapper(iovec_size);
        descriptor.length = iovec_size;
        attachment->append(byterpc::IOBlockRef::TakeOwnership(descriptor, &FreeWrapper));
    }
}

void MakeRpcIovec(std::vector<RpcIovec>* iovecs, uint64_t iovec_num, uint64_t iovec_size) {
    for (uint64_t i = 0; i < iovec_num; ++i) {
        iovecs->push_back({MallocWrapper(iovec_size), iovec_size, &FreeWrapper, nullptr});
    }
}

class DataTransferServiceTest : public testing::Test {
public:
    DataTransferServiceTest() {}

    static void SetUpTestCase() {
        _session_manager = std::make_unique<ServerRpcSessionManager>();
        _timer_thread = std::make_unique<byte::TimerThread>("timer_thread");
        ASSERT_TRUE(_timer_thread->Start().ok());
        _service_worker_thread_pool = std::make_unique<ServiceThreadPool>();
        ASSERT_EQ(0, _service_worker_thread_pool->Init(4, "sw_"));
        for (size_t i = 0; i < 4; ++i) {
            byterpc::ThreadOptions thread_option{._thread_name = "dw_" + std::to_string(i),
                                                 ._cpu_id = -1,
                                                 ._transport = byterpc::TYPE_KERNEL_TCP,
                                                 ._enable_napi = false,
                                                 ._enable_fiber = false,
                                                 ._fiber_mode_option = {},
                                                 ._init_function = nullptr,
                                                 ._fini_function = nullptr,
                                                 ._use_default_poller = true};
            _data_worker_threads.emplace_back(new byterpc::EvThreadHelper());
            ASSERT_TRUE(_data_worker_threads.back()->Init(thread_option));
            ASSERT_TRUE(_data_worker_threads.back()->Start());
        }
    }

    static void TearDownTestCase() {
        for (auto& thread : _data_worker_threads) {
            ASSERT_TRUE(thread->Stop());
            ASSERT_TRUE(thread->Join());
            delete thread;
        }
        _data_worker_threads.clear();
        ASSERT_TRUE(_timer_thread->Stop().ok());
        _timer_thread.reset();
        _session_manager.reset();
        _service_worker_thread_pool.reset();
    }

    static std::unique_ptr<ServiceThreadPool> _service_worker_thread_pool;
    static std::unique_ptr<ServerRpcSessionManager> _session_manager;
    static std::unique_ptr<byte::TimerThread> _timer_thread;
    static std::vector<byterpc::ThreadBase*> _data_worker_threads;
};

std::unique_ptr<ServiceThreadPool> DataTransferServiceTest::_service_worker_thread_pool = nullptr;
std::unique_ptr<ServerRpcSessionManager> DataTransferServiceTest::_session_manager = nullptr;
std::unique_ptr<byte::TimerThread> DataTransferServiceTest::_timer_thread = nullptr;
std::vector<byterpc::ThreadBase*> DataTransferServiceTest::_data_worker_threads;

void GeneralCase(bool do_prepare,
                 int expected_prepare_err_code,
                 int expected_data_transfer_err_code,
                 uint64_t client_send_data_iovec_num_per_thread,
                 uint64_t client_send_data_iovec_size,
                 uint64_t server_send_data_iovec_num_per_thread,
                 uint64_t server_send_data_iovec_size,
                 uint64_t client_send_data_zc_iovec_num,
                 uint64_t client_send_data_zc_iovec_size,
                 uint64_t client_recv_data_zc_iovec_num,
                 uint64_t client_recv_data_zc_iovec_size,
                 uint64_t server_send_data_zc_iovec_num,
                 uint64_t server_send_data_zc_iovec_size,
                 uint64_t server_recv_data_zc_iovec_num,
                 uint64_t server_recv_data_zc_iovec_size,
                 TransportType transport_type,
                 ServerRpcSessionManager* session_manager,
                 byte::TimerThread* timer_thread,
                 std::vector<byterpc::ThreadBase*> data_worker_threads,
                 ServiceThreadPool* service_worker_thread_pool) {
    uint64_t thread_num = data_worker_threads.size();

    auto user_allocate_func = [=](const std::vector<size_t>& lens) -> std::vector<RpcIovec> {
        EXPECT_EQ(lens.size(), client_send_data_zc_iovec_num);
        for (size_t i = 0; i < client_send_data_zc_iovec_num; ++i) {
            EXPECT_EQ(client_send_data_zc_iovec_size, lens[i]);
        }
        std::vector<RpcIovec> iovecs;
        MakeRpcIovec(&iovecs, server_recv_data_zc_iovec_num, server_recv_data_zc_iovec_size);
        return iovecs;
    };

    auto user_service_func = [=](const std::vector<RpcIovec>& recv_data,
                                 const std::vector<RpcIovec>& recv_data_zc,
                                 std::vector<RpcIovec>* send_data,
                                 std::vector<RpcIovec>* send_data_zc) -> void {
        // check recv_data
        EXPECT_EQ(thread_num * client_send_data_iovec_num_per_thread, recv_data.size());
        for (size_t j = 0; j < thread_num * client_send_data_iovec_num_per_thread; ++j) {
            EXPECT_EQ(client_send_data_iovec_size, recv_data[j].len);
        }
        ReleaseRpcIovecs(recv_data);

        // check recv_data_zc
        EXPECT_EQ(server_recv_data_zc_iovec_num, recv_data_zc.size());
        for (size_t j = 0; j < server_recv_data_zc_iovec_num; ++j) {
            EXPECT_EQ(server_recv_data_zc_iovec_size, recv_data_zc[j].len);
        }
        ReleaseRpcIovecs(recv_data_zc);

        MakeRpcIovec(send_data,
                     thread_num * server_send_data_iovec_num_per_thread,
                     server_send_data_iovec_size);
        MakeRpcIovec(send_data_zc, server_send_data_zc_iovec_num, server_send_data_zc_iovec_size);
    };

    DataTransferService service(transport_type,
                                user_allocate_func,
                                user_service_func,
                                session_manager,
                                timer_thread,
                                service_worker_thread_pool);

    uint64_t session_id = 0;
    std::vector<byterpc::IOBlockRef> server_recv_bufs;
    // prepare
    if (do_prepare) {
        byte::CountDownLatch latch(1);
        SimpleController controller;
        proto::PrepareRequest request;
        proto::PrepareResponse response;
        SimpleDone* done = new SimpleDone(&latch);

        // set request
        request.set_task_num(thread_num);
        request.set_timeout_ms(10000);
        for (size_t i = 0; i < client_send_data_zc_iovec_num; ++i) {
            request.add_prepare_data_zc_lengths(client_send_data_zc_iovec_size);
        }

        // invoke prepare
        data_worker_threads[0]->Invoke([&service, &controller, &request, &response, &done] {
            service.Prepare(&controller, &request, &response, done);
        });
        latch.Wait();

        // check error code
        ASSERT_EQ(controller.ErrorCode(), expected_prepare_err_code);
        if (expected_prepare_err_code != ErrorCode::EOK) {
            ASSERT_EQ(0, g_allocated_iovec_num.load());
            return;
        }

        // check response
        ASSERT_TRUE(response.has_session_id());
        ASSERT_EQ(1, session_manager->_session_map.size());
        session_id = response.session_id();
        server_recv_bufs = std::move(controller._to_recv_bufs);
        ASSERT_EQ(server_recv_data_zc_iovec_num, server_recv_bufs.size());
        for (size_t i = 0; i < server_recv_data_zc_iovec_num; ++i) {
            ASSERT_EQ(server_recv_data_zc_iovec_size, server_recv_bufs[i].size());
        }
    }

    byte::CountDownLatch session_latch(thread_num);
    std::vector<SimpleController> controllers(thread_num);
    std::vector<proto::DataTransferRequest> requests(thread_num);
    std::vector<proto::DataTransferResponse> responses(thread_num);
    std::vector<SimpleDone*> dones(thread_num);

    // mock client send data
    controllers[0]._received_bufs = std::move(server_recv_bufs);

    // mock client recv buf
    for (size_t i = 0; i < client_recv_data_zc_iovec_num; ++i) {
        controllers[0]._remote_recv_buf_infos.push_back({0, client_recv_data_zc_iovec_size, 0, 0});
    }

    // data transfer
    for (size_t i = 0; i < thread_num; ++i) {
        // install send_data
        byterpc::IOBuf* attachment = new byterpc::IOBuf;
        MakeAttachment(
            attachment, client_send_data_iovec_num_per_thread, client_send_data_iovec_size);
        controllers[i]._incoming_attachment.reset(attachment);

        if (do_prepare) {
            requests[i].set_session_id(session_id);
        }
        requests[i].set_task_id(i);
        for (size_t j = 0; j < client_send_data_iovec_num_per_thread; ++j) {
            requests[i].add_send_data_lengths(client_send_data_iovec_size);
        }

        dones[i] = new SimpleDone(&session_latch);

        data_worker_threads[i]->Invoke([&service, &controllers, &requests, &responses, &dones, i] {
            service.DataTransfer(&controllers[i], &requests[i], &responses[i], dones[i]);
        });
    }

    session_latch.Wait();

    // check response
    for (size_t i = 0; i < thread_num; ++i) {
        ASSERT_EQ(controllers[i].ErrorCode(), expected_data_transfer_err_code);
        if (expected_data_transfer_err_code == ErrorCode::EOK) {
            ASSERT_FALSE(controllers[i].Failed());

            // check server send_data
            ASSERT_EQ(server_send_data_iovec_num_per_thread, responses[i].send_data_lengths_size());
            for (size_t j = 0; j < server_send_data_iovec_num_per_thread; ++j) {
                ASSERT_EQ(server_send_data_iovec_size, responses[i].send_data_lengths(j));
            }
            ASSERT_EQ(server_send_data_iovec_num_per_thread * server_send_data_iovec_size,
                      controllers[i]._outgoing_attachment->size());

            // check server send_data_zc
            if (i == 0) {
                ASSERT_EQ(server_send_data_zc_iovec_num, controllers[i]._send_bufs.size());
                for (size_t j = 0; j < server_send_data_zc_iovec_num; ++j) {
                    ASSERT_EQ(server_send_data_zc_iovec_size,
                              controllers[i]._send_bufs[j].first.size());
                }
            }
        }
    }

    controllers.clear();
    ASSERT_EQ(0, g_allocated_iovec_num.load());
}

TEST_F(DataTransferServiceTest, CopyDataTransferTest) {
    uint64_t client_send_data_iovec_num_per_thread = 2UL;
    uint64_t client_send_data_iovec_size = 6UL * 1024 * 1024;
    uint64_t server_send_data_iovec_num_per_thread = 3UL;
    uint64_t server_send_data_iovec_size = 4UL * 1024 * 1024;
    {
        SCOPED_TRACE("SingleThreadTcp");
        GeneralCase(false,
                    ErrorCode::EOK,
                    ErrorCode::EOK,
                    client_send_data_iovec_num_per_thread,
                    client_send_data_iovec_size,
                    server_send_data_iovec_num_per_thread,
                    server_send_data_iovec_size,
                    0,
                    0,
                    0,
                    0,
                    0,
                    0,
                    0,
                    0,
                    TransportType::TYPE_KERNEL_TCP,
                    _session_manager.get(),
                    _timer_thread.get(),
                    {_data_worker_threads[0]},
                    _service_worker_thread_pool.get());
    }
    {
        SCOPED_TRACE("SingleThreadRdma");
        GeneralCase(false,
                    ErrorCode::EOK,
                    ErrorCode::EOK,
                    client_send_data_iovec_num_per_thread,
                    client_send_data_iovec_size,
                    server_send_data_iovec_num_per_thread,
                    server_send_data_iovec_size,
                    0,
                    0,
                    0,
                    0,
                    0,
                    0,
                    0,
                    0,
                    TransportType::TYPE_RDMA,
                    _session_manager.get(),
                    _timer_thread.get(),
                    {_data_worker_threads[0]},
                    _service_worker_thread_pool.get());
    }
    {
        SCOPED_TRACE("MultiThreadTcp");
        GeneralCase(true,
                    ErrorCode::EOK,
                    ErrorCode::EOK,
                    client_send_data_iovec_num_per_thread,
                    client_send_data_iovec_size,
                    server_send_data_iovec_num_per_thread,
                    server_send_data_iovec_size,
                    0,
                    0,
                    0,
                    0,
                    0,
                    0,
                    0,
                    0,
                    TransportType::TYPE_KERNEL_TCP,
                    _session_manager.get(),
                    _timer_thread.get(),
                    _data_worker_threads,
                    _service_worker_thread_pool.get());
    }
    {
        SCOPED_TRACE("MultiThreadRdma");
        GeneralCase(true,
                    ErrorCode::EOK,
                    ErrorCode::EOK,
                    client_send_data_iovec_num_per_thread,
                    client_send_data_iovec_size,
                    server_send_data_iovec_num_per_thread,
                    server_send_data_iovec_size,
                    0,
                    0,
                    0,
                    0,
                    0,
                    0,
                    0,
                    0,
                    TransportType::TYPE_RDMA,
                    _session_manager.get(),
                    _timer_thread.get(),
                    _data_worker_threads,
                    _service_worker_thread_pool.get());
    }
}

TEST_F(DataTransferServiceTest, ZeroCopyDataTransferTest) {
    uint64_t client_send_data_iovec_num_per_thread = 4UL;
    uint64_t client_send_data_iovec_size = 32UL;
    uint64_t server_send_data_iovec_num_per_thread = 2UL;
    uint64_t server_send_data_iovec_size = 64UL;
    uint64_t client_send_data_zc_iovec_num = 4UL;
    uint64_t client_send_data_zc_iovec_size = 64UL;
    uint64_t client_recv_data_zc_iovec_num = 2UL;
    uint64_t client_recv_data_zc_iovec_size = 32UL;
    uint64_t server_send_data_zc_iovec_num = client_recv_data_zc_iovec_num;
    uint64_t server_send_data_zc_iovec_size = client_recv_data_zc_iovec_size;
    uint64_t server_recv_data_zc_iovec_num = client_send_data_zc_iovec_num;
    uint64_t server_recv_data_zc_iovec_size = client_send_data_zc_iovec_size;

    {
        SCOPED_TRACE("ServerBufExactTheSameAsClient");
        GeneralCase(true,
                    ErrorCode::EOK,
                    ErrorCode::EOK,
                    client_send_data_iovec_num_per_thread,
                    client_send_data_iovec_size,
                    server_send_data_iovec_num_per_thread,
                    server_send_data_iovec_size,
                    client_send_data_zc_iovec_num,
                    client_send_data_zc_iovec_size,
                    client_recv_data_zc_iovec_num,
                    client_recv_data_zc_iovec_size,
                    server_send_data_zc_iovec_num,
                    server_send_data_zc_iovec_size,
                    server_recv_data_zc_iovec_num,
                    server_recv_data_zc_iovec_size,
                    TransportType::TYPE_RDMA,
                    _session_manager.get(),
                    _timer_thread.get(),
                    _data_worker_threads,
                    _service_worker_thread_pool.get());
    }
    {
        SCOPED_TRACE("ServerRecvBufNumNotEnough");
        GeneralCase(true,
                    ErrorCode::EALLOC,
                    -1,
                    client_send_data_iovec_num_per_thread,
                    client_send_data_iovec_size,
                    server_send_data_iovec_num_per_thread,
                    server_send_data_iovec_size,
                    client_send_data_zc_iovec_num,
                    client_send_data_zc_iovec_size,
                    client_recv_data_zc_iovec_num,
                    client_recv_data_zc_iovec_size,
                    server_send_data_zc_iovec_num,
                    server_send_data_zc_iovec_size,
                    server_recv_data_zc_iovec_num - 1,
                    server_recv_data_zc_iovec_size,
                    TransportType::TYPE_RDMA,
                    _session_manager.get(),
                    _timer_thread.get(),
                    _data_worker_threads,
                    _service_worker_thread_pool.get());
    }
    {
        SCOPED_TRACE("ServerRecvBufNumExceed");
        GeneralCase(true,
                    ErrorCode::EALLOC,
                    -1,
                    client_send_data_iovec_num_per_thread,
                    client_send_data_iovec_size,
                    server_send_data_iovec_num_per_thread,
                    server_send_data_iovec_size,
                    client_send_data_zc_iovec_num,
                    client_send_data_zc_iovec_size,
                    client_recv_data_zc_iovec_num,
                    client_recv_data_zc_iovec_size,
                    server_send_data_zc_iovec_num,
                    server_send_data_zc_iovec_size,
                    server_recv_data_zc_iovec_num + 1,
                    server_recv_data_zc_iovec_size,
                    TransportType::TYPE_RDMA,
                    _session_manager.get(),
                    _timer_thread.get(),
                    _data_worker_threads,
                    _service_worker_thread_pool.get());
    }
    {
        SCOPED_TRACE("ServerRecvBufSizeNotEnough");
        GeneralCase(true,
                    ErrorCode::EALLOC,
                    -1,
                    client_send_data_iovec_num_per_thread,
                    client_send_data_iovec_size,
                    server_send_data_iovec_num_per_thread,
                    server_send_data_iovec_size,
                    client_send_data_zc_iovec_num,
                    client_send_data_zc_iovec_size,
                    client_recv_data_zc_iovec_num,
                    client_recv_data_zc_iovec_size,
                    server_send_data_zc_iovec_num,
                    server_send_data_zc_iovec_size,
                    server_recv_data_zc_iovec_num,
                    server_recv_data_zc_iovec_size - 1,
                    TransportType::TYPE_RDMA,
                    _session_manager.get(),
                    _timer_thread.get(),
                    _data_worker_threads,
                    _service_worker_thread_pool.get());
    }
    {
        SCOPED_TRACE("ServerRecvBufSizeExceed");
        GeneralCase(true,
                    ErrorCode::EALLOC,
                    -1,
                    client_send_data_iovec_num_per_thread,
                    client_send_data_iovec_size,
                    server_send_data_iovec_num_per_thread,
                    server_send_data_iovec_size,
                    client_send_data_zc_iovec_num,
                    client_send_data_zc_iovec_size,
                    client_recv_data_zc_iovec_num,
                    client_recv_data_zc_iovec_size,
                    server_send_data_zc_iovec_num,
                    server_send_data_zc_iovec_size,
                    server_recv_data_zc_iovec_num,
                    server_recv_data_zc_iovec_size + 1,
                    TransportType::TYPE_RDMA,
                    _session_manager.get(),
                    _timer_thread.get(),
                    _data_worker_threads,
                    _service_worker_thread_pool.get());
    }
    {
        SCOPED_TRACE("ServerSendBufNumNotEnough");
        GeneralCase(true,
                    ErrorCode::EOK,
                    ErrorCode::EOK,
                    client_send_data_iovec_num_per_thread,
                    client_send_data_iovec_size,
                    server_send_data_iovec_num_per_thread,
                    server_send_data_iovec_size,
                    client_send_data_zc_iovec_num,
                    client_send_data_zc_iovec_size,
                    client_recv_data_zc_iovec_num,
                    client_recv_data_zc_iovec_size,
                    server_send_data_zc_iovec_num - 1,
                    server_send_data_zc_iovec_size,
                    server_recv_data_zc_iovec_num,
                    server_recv_data_zc_iovec_size,
                    TransportType::TYPE_RDMA,
                    _session_manager.get(),
                    _timer_thread.get(),
                    _data_worker_threads,
                    _service_worker_thread_pool.get());
    }
    {
        SCOPED_TRACE("ServerSendBufNumExceed");
        GeneralCase(true,
                    ErrorCode::EOK,
                    ErrorCode::ERESPONSE,
                    client_send_data_iovec_num_per_thread,
                    client_send_data_iovec_size,
                    server_send_data_iovec_num_per_thread,
                    server_send_data_iovec_size,
                    client_send_data_zc_iovec_num,
                    client_send_data_zc_iovec_size,
                    client_recv_data_zc_iovec_num,
                    client_recv_data_zc_iovec_size,
                    server_send_data_zc_iovec_num + 1,
                    server_send_data_zc_iovec_size,
                    server_recv_data_zc_iovec_num,
                    server_recv_data_zc_iovec_size,
                    TransportType::TYPE_RDMA,
                    _session_manager.get(),
                    _timer_thread.get(),
                    _data_worker_threads,
                    _service_worker_thread_pool.get());
    }
    {
        SCOPED_TRACE("ServerSendBufSizeNotEnough");
        GeneralCase(true,
                    ErrorCode::EOK,
                    ErrorCode::EOK,
                    client_send_data_iovec_num_per_thread,
                    client_send_data_iovec_size,
                    server_send_data_iovec_num_per_thread,
                    server_send_data_iovec_size,
                    client_send_data_zc_iovec_num,
                    client_send_data_zc_iovec_size,
                    client_recv_data_zc_iovec_num,
                    client_recv_data_zc_iovec_size,
                    server_send_data_zc_iovec_num,
                    server_send_data_zc_iovec_size - 1,
                    server_recv_data_zc_iovec_num,
                    server_recv_data_zc_iovec_size,
                    TransportType::TYPE_RDMA,
                    _session_manager.get(),
                    _timer_thread.get(),
                    _data_worker_threads,
                    _service_worker_thread_pool.get());
    }
    {
        SCOPED_TRACE("ServerSendBufSizeExceed");
        GeneralCase(true,
                    ErrorCode::EOK,
                    ErrorCode::ERESPONSE,
                    client_send_data_iovec_num_per_thread,
                    client_send_data_iovec_size,
                    server_send_data_iovec_num_per_thread,
                    server_send_data_iovec_size,
                    client_send_data_zc_iovec_num,
                    client_send_data_zc_iovec_size,
                    client_recv_data_zc_iovec_num,
                    client_recv_data_zc_iovec_size,
                    server_send_data_zc_iovec_num,
                    server_send_data_zc_iovec_size + 1,
                    server_recv_data_zc_iovec_num,
                    server_recv_data_zc_iovec_size,
                    TransportType::TYPE_RDMA,
                    _session_manager.get(),
                    _timer_thread.get(),
                    _data_worker_threads,
                    _service_worker_thread_pool.get());
    }
}

TEST_F(DataTransferServiceTest, DataTransferTimeout) {
    uint64_t client_send_data_iovec_num_per_thread = 2UL;
    uint64_t client_send_data_iovec_size = 64UL;
    uint64_t thread_num = _data_worker_threads.size();
    uint32_t timeout_ms = 2000U;

    auto user_service_func = [](const std::vector<RpcIovec>& recv_data,
                                const std::vector<RpcIovec>& recv_data_zc,
                                std::vector<RpcIovec>* send_data,
                                std::vector<RpcIovec>* send_data_zc) -> void {
        EXPECT_TRUE(false);
    };
    DataTransferService service(TransportType::TYPE_KERNEL_TCP,
                                nullptr,
                                user_service_func,
                                _session_manager.get(),
                                _timer_thread.get(),
                                _service_worker_thread_pool.get());

    uint64_t session_id = 0;
    // prepare
    {
        SimpleController controller;
        proto::PrepareRequest request;
        proto::PrepareResponse response;
        SimpleDone* done = new SimpleDone(nullptr);

        request.set_task_num(thread_num);
        request.set_timeout_ms(timeout_ms);
        service.Prepare(&controller, &request, &response, done);

        ASSERT_TRUE(response.has_session_id());
        ASSERT_EQ(1, _session_manager->_session_map.size());
        session_id = response.session_id();
    }

    // data transfer
    byte::CountDownLatch session_latch(thread_num);
    std::vector<SimpleController> controllers(thread_num);
    std::vector<proto::DataTransferRequest> requests(thread_num);
    std::vector<proto::DataTransferResponse> responses(thread_num);
    std::vector<SimpleDone*> dones(thread_num);
    for (size_t i = 0; i < thread_num; ++i) {
        // sleep until timeout for the latter half of threads
        if (i == thread_num / 2) {
            std::this_thread::sleep_for(std::chrono::milliseconds(timeout_ms + 1000));
        }
        byterpc::IOBuf* attachment = new byterpc::IOBuf;
        MakeAttachment(
            attachment, client_send_data_iovec_num_per_thread, client_send_data_iovec_size);
        controllers[i]._incoming_attachment.reset(attachment);

        requests[i].set_session_id(session_id);
        requests[i].set_task_id(i);
        for (size_t j = 0; j < client_send_data_iovec_num_per_thread; ++j) {
            requests[i].add_send_data_lengths(client_send_data_iovec_size);
        }

        dones[i] = new SimpleDone(&session_latch);

        _data_worker_threads[i]->Invoke([&service, &controllers, &requests, &responses, &dones, i] {
            service.DataTransfer(&controllers[i], &requests[i], &responses[i], dones[i]);
        });
    }

    session_latch.Wait();

    // check response
    for (size_t i = 0; i < thread_num; ++i) {
        if (i < thread_num / 2) {
            ASSERT_EQ(controllers[i].ErrorCode(), ETIMEDOUT);
        } else {
            ASSERT_EQ(controllers[i].ErrorCode(), EINTERNAL);
        }
    }

    controllers.clear();
    ASSERT_EQ(0, g_allocated_iovec_num.load());
}

}  // namespace byterpc_agent

int main(int argc, char** argv) {
    testing::InitGoogleTest(&argc, argv);

    byterpc_agent::ByterpcInitOptions options;
    options.enable_ktcp = true;
    options.enable_rdma = true;
    if (0 != byterpc_agent::ByterpcInit(options)) {
        BYTERPC_LOG(FATAL) << "Fail to init byterpc";
    }
    BYTE_DEFER(byterpc_agent::ByterpcShutDown());

    return RUN_ALL_TESTS();
}