// Copyright (c) 2024, ByteDance Inc. All rights reserved.

#include "server_rpc_session.h"

#include <atomic>
#include <thread>

#include "byte/thread/timer_thread.h"
#include "byterpc/thread/ev_thread_helper.h"
#include "gtest/gtest.h"

#include "util.h"

namespace byterpc_agent {

std::atomic<size_t> g_allocated_iovec_num(0);

class SimpleTask : public ServerRpcTask {
public:
    SimpleTask(size_t iovec_num) : _iovec_num(iovec_num) {
        g_allocated_iovec_num.fetch_add(_iovec_num, std::memory_order_relaxed);
    }
    void SetFailed(int error_code, const std::string& error_text) override {
        g_allocated_iovec_num.fetch_sub(_iovec_num, std::memory_order_relaxed);
    }
    void ReleaseIovecs() {
        g_allocated_iovec_num.fetch_sub(_iovec_num, std::memory_order_relaxed);
    }

private:
    size_t _iovec_num;
};

void SessionDone(std::vector<std::unique_ptr<ServerRpcTask>>&& tasks) {
    std::vector<std::unique_ptr<ServerRpcTask>> tmp(std::move(tasks));
    for (auto& task : tmp) {
        static_cast<SimpleTask*>(task.get())->ReleaseIovecs();
    }
}

class ServerRpcSessionTest : public testing::Test {
protected:
    ServerRpcSessionTest() {}

    static void SetUpTestCase() {
        _timer_thread = std::make_unique<byte::TimerThread>("timer_thread");
        ASSERT_TRUE(_timer_thread->Start().ok());

        for (size_t i = 0; i < 5; ++i) {
            byterpc::ThreadOptions thread_option{._thread_name = "worker_" + std::to_string(i),
                                                 ._cpu_id = -1,
                                                 ._transport = byterpc::TYPE_KERNEL_TCP,
                                                 ._enable_napi = false,
                                                 ._enable_fiber = false,
                                                 ._fiber_mode_option = {},
                                                 ._init_function = nullptr,
                                                 ._fini_function = nullptr,
                                                 ._use_default_poller = true};
            _data_worker_threads.emplace_back(new byterpc::EvThreadHelper());
            ASSERT_TRUE(_data_worker_threads.back()->Init(thread_option));
            ASSERT_TRUE(_data_worker_threads.back()->Start());
        }
    }

    static void TearDownTestCase() {
        for (auto& thread : _data_worker_threads) {
            ASSERT_TRUE(thread->Stop());
            ASSERT_TRUE(thread->Join());
        }
        _data_worker_threads.clear();

        ASSERT_TRUE(_timer_thread->Stop().ok());
        _timer_thread.reset();
    }

    static std::unique_ptr<byte::TimerThread> _timer_thread;
    static std::vector<std::unique_ptr<byterpc::ThreadBase>> _data_worker_threads;
};

std::unique_ptr<byte::TimerThread> ServerRpcSessionTest::_timer_thread(nullptr);
std::vector<std::unique_ptr<byterpc::ThreadBase>> ServerRpcSessionTest::_data_worker_threads;

TEST_F(ServerRpcSessionTest, SessionSucceed) {
    size_t session_id = 123;
    size_t thread_num = _data_worker_threads.size();
    size_t task_num_per_thread = 40;
    size_t iovec_per_task = 3;

    // without timer
    {
        std::shared_ptr<ServerRpcSession> session = ServerRpcSession::Create(session_id, nullptr);
        ASSERT_TRUE(session);

        ASSERT_EQ(0, session->Init(thread_num * task_num_per_thread, SessionDone, 0U, nullptr));
        ASSERT_EQ(session_id, session->SessionId());

        byte::CountDownLatch latch(thread_num);
        for (size_t i = 0; i < thread_num; ++i) {
            _data_worker_threads[i]->Invoke([=, &session, &latch]() {
                for (size_t j = 0; j < task_num_per_thread; ++j) {
                    session->FinishTask(i * task_num_per_thread + j,
                                        std::make_unique<SimpleTask>(iovec_per_task));
                }
                latch.CountDown();
            });
        }

        latch.Wait();
        ASSERT_EQ(ErrorCode::EOK, session->_error_code);
        ASSERT_EQ(0, g_allocated_iovec_num.load());
    }

    // with timer
    {
        std::shared_ptr<ServerRpcSession> session = ServerRpcSession::Create(session_id, nullptr);
        ASSERT_TRUE(session);

        ASSERT_EQ(0,
                  session->Init(
                      thread_num * task_num_per_thread, SessionDone, 100000U, _timer_thread.get()));
        ASSERT_EQ(session_id, session->SessionId());

        byte::CountDownLatch latch(thread_num);
        for (size_t i = 0; i < thread_num; ++i) {
            _data_worker_threads[i]->Invoke([=, &session, &latch]() {
                for (size_t j = 0; j < task_num_per_thread; ++j) {
                    session->FinishTask(i * task_num_per_thread + j,
                                        std::make_unique<SimpleTask>(iovec_per_task));
                }
                latch.CountDown();
            });
        }

        latch.Wait();
        ASSERT_EQ(ErrorCode::EOK, session->_error_code);
        ASSERT_EQ(0, g_allocated_iovec_num.load());
    }
}

TEST_F(ServerRpcSessionTest, SessionTimeout) {
    size_t session_id = 123;
    size_t thread_num = _data_worker_threads.size();
    size_t task_num_per_thread = 40;
    size_t iovec_per_task = 3;

    std::shared_ptr<ServerRpcSession> session = ServerRpcSession::Create(session_id, nullptr);
    ASSERT_TRUE(session);

    ASSERT_EQ(
        0, session->Init(thread_num * task_num_per_thread, SessionDone, 100U, _timer_thread.get()));
    ASSERT_EQ(session_id, session->SessionId());

    byte::CountDownLatch latch(thread_num);
    for (size_t i = 0; i < thread_num; ++i) {
        _data_worker_threads[i]->Invoke([=, &session, &latch]() {
            for (size_t j = 0; j < task_num_per_thread / 2; ++j) {
                session->FinishTask(i * task_num_per_thread + j,
                                    std::make_unique<SimpleTask>(iovec_per_task));
            }
            while (session->_error_code == ErrorCode::EOK) {
                std::this_thread::sleep_for(std::chrono::milliseconds(1));
            }
            for (size_t j = task_num_per_thread / 2; j < task_num_per_thread; ++j) {
                session->FinishTask(i * task_num_per_thread + j,
                                    std::make_unique<SimpleTask>(iovec_per_task));
            }
            latch.CountDown();
        });
    }

    latch.Wait();

    ASSERT_NE(ErrorCode::EOK, session->_error_code);
    ASSERT_EQ(0, g_allocated_iovec_num.load());
}

TEST_F(ServerRpcSessionTest, SessionManagerCreateSession) {
    size_t thread_num = 5;
    size_t session_num = 1000;

    ServerRpcSessionManager manager;

    byte::CountDownLatch latch(thread_num);
    for (size_t i = 0; i < thread_num; ++i) {
        _data_worker_threads[i]->Invoke([=, &manager, &latch]() {
            for (size_t j = 0; j < session_num; ++j) {
                std::shared_ptr<ServerRpcSession> session = manager.CreateSession();
                ASSERT_TRUE(session);
                uint64_t session_id = session->SessionId();
                ASSERT_EQ(session.get(), manager.GetSession(session_id).get());
                manager.RemoveSession(session_id);
                ASSERT_FALSE(manager.GetSession(session_id));
            }
            latch.CountDown();
        });
    }

    latch.Wait();
}

void CreateSessionAndDispatchTask(
    byte::CountDownLatch* latch,
    ServerRpcSessionManager* manager,
    std::vector<std::unique_ptr<byterpc::ThreadBase>>* data_worker_threads,
    size_t finish_task_num_per_thread,
    size_t total_task_num_per_thread,
    size_t iovec_per_task,
    uint32_t timeout_ms,
    byte::TimerThread* timer_thread) {
    size_t thread_num = data_worker_threads->size();
    std::shared_ptr<ServerRpcSession> session = manager->CreateSession();
    ASSERT_TRUE(session);
    uint64_t session_id = session->SessionId();
    session->Init(thread_num * total_task_num_per_thread, SessionDone, timeout_ms, timer_thread);
    for (size_t i = 0; i < finish_task_num_per_thread; ++i) {
        for (size_t j = 0; j < thread_num; ++j) {
            size_t task_id = i * thread_num + j;
            (*data_worker_threads)[j]->Invoke(
                [manager, session_id, iovec_per_task, task_id, latch]() {
                    std::shared_ptr<ServerRpcSession> session = manager->GetSession(session_id);
                    EXPECT_TRUE(session);
                    if (session) {
                        session->FinishTask(task_id, std::make_unique<SimpleTask>(iovec_per_task));
                    }
                    latch->CountDown();
                });
        }
    }
}

TEST_F(ServerRpcSessionTest, SessionManagerSessionSucceed) {
    size_t thread_num = _data_worker_threads.size();
    size_t session_num = 100;
    size_t task_num_per_thread = 40;
    size_t iovec_per_task = 3;

    ServerRpcSessionManager manager;

    byte::CountDownLatch latch(session_num * thread_num * task_num_per_thread);
    for (size_t i = 0; i < session_num; ++i) {
        _data_worker_threads[i % thread_num]->Invoke(NewClosure(&CreateSessionAndDispatchTask,
                                                                &latch,
                                                                &manager,
                                                                &_data_worker_threads,
                                                                task_num_per_thread,
                                                                task_num_per_thread,
                                                                iovec_per_task,
                                                                100000U,
                                                                _timer_thread.get()));
    }

    latch.Wait();
    ASSERT_EQ(0, g_allocated_iovec_num.load());
    ASSERT_EQ(0, manager._session_map.size());
}

TEST_F(ServerRpcSessionTest, SessionManagerSessionTimeout) {
    size_t thread_num = _data_worker_threads.size();
    size_t session_num = 30;
    size_t total_num_per_thread = 40;
    size_t finish_task_num_per_thread = total_num_per_thread / 2;
    size_t iovec_per_task = 3;
    uint32_t timeout_ms = 8000U;

    ServerRpcSessionManager manager;

    byte::CountDownLatch latch(session_num * thread_num * finish_task_num_per_thread);
    for (size_t i = 0; i < session_num; ++i) {
        _data_worker_threads[i % thread_num]->Invoke(NewClosure(&CreateSessionAndDispatchTask,
                                                                &latch,
                                                                &manager,
                                                                &_data_worker_threads,
                                                                finish_task_num_per_thread,
                                                                total_num_per_thread,
                                                                iovec_per_task,
                                                                timeout_ms,
                                                                _timer_thread.get()));
    }

    latch.Wait();
    ASSERT_EQ(session_num * thread_num * finish_task_num_per_thread * iovec_per_task,
              g_allocated_iovec_num.load());
    ASSERT_EQ(session_num, manager._session_map.size());

    std::this_thread::sleep_for(std::chrono::milliseconds(timeout_ms + 100));
    ASSERT_EQ(0, g_allocated_iovec_num.load());
    ASSERT_EQ(0, manager._session_map.size());
}

}  // namespace byterpc_agent

int main(int argc, char** argv) {
    testing::InitGoogleTest(&argc, argv);
    return RUN_ALL_TESTS();
}