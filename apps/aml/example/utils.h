// Copyright (c) 2024, ByteDance Inc. All rights reserved.

#pragma once

#include <cstddef>

void* MemAlloc(size_t size, bool is_device);

int MemFree(void* addr, bool is_device);

int MemSet(void* addr, bool is_device, int val, size_t size);

int MemCpy(void* dst, bool dst_is_device, const void* src, bool src_is_device, size_t size);

int MemCmp(
    const void* addr1, bool addr1_is_device, const void* addr2, bool addr2_is_device, size_t size);

int SetCudaDevice(int device_id);
