// Copyright (c) 2024, ByteDance Inc. All rights reserved.

#include "simple_memory_pool.h"

#include <chrono>
#include <thread>

#include "byte/include/byte_log.h"
#include "byte/string/algorithm.h"
#include "byterpc_agent/agent.h"
#include "byterpc_agent/byterpc.h"
#include "gflags/gflags.h"

DEFINE_uint32(data_worker_num, 2, "Number of data worker threads in agent");
DEFINE_uint32(service_worker_num, 2, "Number of service worker threads in agent");
DEFINE_string(listen_ips, "0.0.0.0", "listen addresses, split by comma");
DEFINE_int32(listen_port, 18888, "listen port");
DEFINE_bool(use_rdma, true, "use rdma");
DEFINE_bool(use_tcp, true, "use tcp");
DEFINE_bool(register_send_data, false, "Whether to register user's send_data to rdma.");
DEFINE_bool(use_cuda_mem, false, "Use cuda device memory instead for zero-copy data.");
DEFINE_uint32(cuda_device_id, 0, "Set cuda device id.");
DEFINE_uint64(send_data_block_num, 0, "Number of memory block of send_data.");
DEFINE_uint64(send_data_block_size, 1 * 1024, "Size of memory block of send_data.");
DEFINE_uint64(
    send_data_zc_block_num,
    0,
    "Number of memory block of send_data_zc. Must be the same as client's recv_data_zc_block_num.");
DEFINE_uint64(
    send_data_zc_block_size,
    1 * 1024,
    "Size of memory block of send_data_zc. Must be the same as client's recv_data_zc_block_size.");
DEFINE_bool(validate_data, false, "check data correctness");

SimpleMemoryPool* send_data_mem_pool = nullptr;
SimpleMemoryPool* send_data_zc_mem_pool = nullptr;
SimpleMemoryPool* recv_data_zc_mem_pool = nullptr;

void FreeWrapper(void* addr, void* mem_pool) {
    SimpleMemoryPool* mem_pool_ptr = reinterpret_cast<SimpleMemoryPool*>(mem_pool);
    static_cast<SimpleMemoryPool*>(mem_pool)->FreeBlock(addr);
}

std::vector<byterpc_agent::RpcIovec> PrepareBuf(SimpleMemoryPool* mem_pool,
                                                size_t block_size,
                                                size_t block_num) {
    std::vector<byterpc_agent::RpcIovec> iovecs(block_num);
    for (size_t i = 0; i < iovecs.size(); ++i) {
        void* addr = mem_pool->AllocBlock();
        if (!addr) {
            LOG(FATAL) << "Fail to alloc block";
        }
        iovecs[i] = {addr, block_size, &FreeWrapper, mem_pool};
    }
    return iovecs;
}

void Service(const std::vector<byterpc_agent::RpcIovec>& recv_data,
             const std::vector<byterpc_agent::RpcIovec>& recv_data_zc,
             std::vector<byterpc_agent::RpcIovec>* send_data,
             std::vector<byterpc_agent::RpcIovec>* send_data_zc) {
    // check recv data and free it
    for (size_t i = 0; i < recv_data.size(); ++i) {
        if (FLAGS_validate_data) {
            if (0 != MemCmp(recv_data[i].addr, false, "DEADBEAF", false, 8)) {
                LOG(ERROR) << "Data check fails";
            }
        }
        recv_data[i].deleter(recv_data[i].addr, recv_data[i].deleter_param);
    }

    // check zero-copy recv data and free it
    for (size_t i = 0; i < recv_data_zc.size(); ++i) {
        if (FLAGS_validate_data) {
            if (0 != MemCmp(recv_data_zc[i].addr, FLAGS_use_cuda_mem, "DEADBEAF", false, 8)) {
                LOG(ERROR) << "Data check fails";
            }
        }
        recv_data_zc[i].deleter(recv_data_zc[i].addr, recv_data_zc[i].deleter_param);
    }

    // prepare send data
    *send_data =
        PrepareBuf(send_data_mem_pool, FLAGS_send_data_block_size, FLAGS_send_data_block_num);
    if (FLAGS_validate_data) {
        for (const auto& iovec : *send_data) {
            MemCpy(iovec.addr, false, "DEADBEAF", false, 8);
        }
    }

    // prepare zero-copy send data
    *send_data_zc = PrepareBuf(
        send_data_zc_mem_pool, FLAGS_send_data_zc_block_size, FLAGS_send_data_zc_block_num);
    if (FLAGS_validate_data) {
        for (const auto& iovec : *send_data_zc) {
            MemCpy(iovec.addr, FLAGS_use_cuda_mem, "DEADBEAF", false, 8);
        }
    }
}

std::vector<byterpc_agent::RpcIovec> Allocate(const std::vector<size_t>& lens) {
    if (!recv_data_zc_mem_pool) {
        recv_data_zc_mem_pool = new SimpleMemoryPool(lens[0], true, FLAGS_use_cuda_mem);
    }
    return PrepareBuf(recv_data_zc_mem_pool, lens[0], lens.size());
}

int main(int argc, char* argv[]) {
    GFLAGS_NAMESPACE::ParseCommandLineFlags(&argc, &argv, true);

    if (!FLAGS_use_rdma && (FLAGS_send_data_zc_block_num > 0)) {
        LOG(ERROR) << "send_data_zc is only supported in rdma mode";
        return -1;
    }

    if (FLAGS_use_cuda_mem) {
        if (0 != SetCudaDevice(FLAGS_cuda_device_id)) {
            LOG(ERROR) << "Fail to set cuda device";
            return -1;
        }
    }

    send_data_mem_pool =
        new SimpleMemoryPool(FLAGS_send_data_block_size, FLAGS_register_send_data, false);
    send_data_zc_mem_pool =
        new SimpleMemoryPool(FLAGS_send_data_zc_block_size, true, FLAGS_use_cuda_mem);

    byterpc_agent::ByterpcInitOptions init_option{.enable_ktcp = FLAGS_use_tcp,
                                                  .enable_rdma = FLAGS_use_rdma,
                                                  .log_dir = "",
                                                  .log_name = "server_agent_byterpc"};
    if (0 != byterpc_agent::ByterpcInit(init_option)) {
        LOG(ERROR) << "Fail to init byterpc";
        return -1;
    }

    std::vector<std::string> listen_ips;
    byte::SplitString(FLAGS_listen_ips, ",", &listen_ips);

    std::vector<byterpc_agent::TransportType> trans_types;
    if (FLAGS_use_rdma) {
        trans_types.push_back(byterpc_agent::TYPE_RDMA);
    }
    if (FLAGS_use_tcp) {
        trans_types.push_back(byterpc_agent::TYPE_KERNEL_TCP);
    }

    std::vector<std::unique_ptr<byterpc_agent::Agent>> agents;
    for (const byterpc_agent::TransportType& trans_type : trans_types) {
        std::string agent_name = "server_" + (trans_type);
        byterpc_agent::AgentOptions agent_option{
            .agent_name = agent_name,
            .data_worker_num = FLAGS_data_worker_num,
            .trans_type = trans_type,
            .server_option =
                {
                    .listen_ips = listen_ips,
                    .port = FLAGS_listen_port,
                    .service_fn = &Service,
                    .allocate_fn = &Allocate,
                    .service_worker_num = FLAGS_service_worker_num,
                },
        };

        std::unique_ptr<byterpc_agent::Agent> agent =
            byterpc_agent::Agent::CreateAgent(agent_option);
        if (!agent) {
            LOG(ERROR) << "Fail to create agent";
            return -1;
        }

        agents.push_back(std::move(agent));
    }

    while (true) {
        std::this_thread::sleep_for(std::chrono::seconds(100));
    }

    agents.clear();

    delete send_data_mem_pool;
    delete send_data_zc_mem_pool;
    if (recv_data_zc_mem_pool) {
        delete recv_data_zc_mem_pool;
    }

    return 0;
}