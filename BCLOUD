#edit-mode: -*- python -*-

WORKROOT('../../../')

COMPILER('gcc12')
COVERAGE_VERSION("8.25.25")

VERSION="5.0.0"

GLOB_CPPFLAGS_STR = '-D_GNU_SOURCE -D__STDC_LIMIT_MACROS -D__STDC_FORMAT_MACROS -DBAIDU_INTERNAL'
GLOB_CPPFLAGS_STR += r' -D__ARIES_VERSION_ID__=\"%s.%s\"' % (VERSION, REPO_CHANGESET())
GLOB_CPPFLAGS_STR += r' -D__ARIES_BUILDHOST__=\"`hostname`\"'
GLOB_CPPFLAGS_STR += r' -D__ARIES_REPO_URL__=\"%s\"' % REPO_URL()
CPPFLAGS(GLOB_CPPFLAGS_STR)
GLOB_CPPFLAGS_STR_FOR_TEST = GLOB_CPPFLAGS_STR + ' -DGTEST -D_UNIT_TEST'

GLOB_CXXFLAGS_STR = '-std=c++17 -O2 -g -pipe -W -Wall -Werror=sign-compare -fPIC -faligned-new -fno-omit-frame-pointer'
GLOB_CXXFLAGS_STR += ' -Wno-unused-variable -Wno-unused-parameter -Wno-unused-function'
CXXFLAGS(GLOB_CXXFLAGS_STR)
GLOB_CXXFLAGS_STR_FOR_TEST = GLOB_CXXFLAGS_STR + ' -fno-access-control -fpermissive'
GLOB_CXXFLAGS_STR_FOR_TEST += ' -Wno-sign-compare -Wno-write-strings -Wno-unused-local-typedefs -O0'

INCPATHS(". ../../.. $INC $OUT_ROOT $OUT_ROOT/baidu/third-party/brunsli/output/include/ $OUT_ROOT/baidu/third-party/lz4/include \
    $OUT_ROOT/baidu/third-party/zstd/include $OUT_ROOT/baidu/third-party/isa-l/output/include/ $OUT_ROOT/baidu/third-party/rocksdb/output/include")

LDFLAGS('-pthread -lrt -ldl -lz -lbfd -lopcodes -liberty -Wl,-rpath=/opt/compiler/gcc-12/lib64:/usr/lib64:/lib64')

#Just for debug with address sanitizer
#CXXFLAGS('-fsanitize=address -fno-omit-frame-pointer')
#LDFLAGS('-lasan')

ENABLE_GLOBAL_FLAGS()

CONFIGS('baidu/base/baidu-rpc@stable')
CONFIGS('baidu/inf/sfl@stable')
CONFIGS('baidu/feed-mlarch/babylon-lite@stable')
CONFIGS('baidu/third-party/lz4@lz4_V1.9.1.1_GCC820_4U3_K3_GEN_PD_BL@git_tag')
CONFIGS('baidu/third-party/zstd@zstd_V1.4.4.1_GCC820_6U3_K2_GEN_PD_BL@git_tag')
CONFIGS('baidu/third-party/snappy@snappy_V1.7.1.2_GCC482_6U3_K2_GEN_PD_BL@git_tag')
CONFIGS('baidu/third-party/brotli@master@git_branch')
CONFIGS('baidu/third-party/brunsli@truncated@git_branch')
CONFIGS('baidu/third-party/json-cpp@json-cpp_V1.8.4.11_GCC482_4U3_K3_GEN_PD_BL@git_tag')
CONFIGS("baidu/third-party/protobuf@protobuf_V2.6.1.1_GCC820_4U3_K3_GEN_PD_BL@git_tag")
CONFIGS("baidu/third-party/boost@boost_V1.70.0.3_GCC482_4U3_K3_GEN_PD_BL@git_tag")
CONFIGS('baidu/third-party/isa-l@2-30-0-baidu-inf-aries@git_branch')

CONFIGS('baidu/base/bmock@stable')
CONFIGS('baidu/base/fault@stable')
CONFIGS("baidu/third-party/gtest@gtest_V1.6.0.1_GCC820_4U3_K3_GEN_PD_BL@git_tag")
CONFIGS('baidu/third-party/uuid@uuid_V1.0.3.1_r2_GCC12_4U3_K3_GEN_PD_BL@git_tag')
CONFIGS('baidu/third-party/libevent@libevent_V2.1.12_GCC12_GEN_PD_BL@git_tag')
CONFIGS("baidu/bos/transmitter@master@git_branch")

api_sources = GLOB('*.cpp proxy/*.cpp common/*.cpp common/journal/*.cpp common/proto/*.proto common/proto/*.cc')

StaticLibrary('aries', Sources(api_sources))
HEADERS('aries.h', '$INC/baidu/inf/aries-api/')
HEADERS('aries_define.h', '$INC/baidu/inf/aries-api/')
HEADERS('common/*.h', '$INC/baidu/inf/aries-api/common/')
HEADERS('common/proto/*.h', '$INC/baidu/inf/aries-api/common/proto/')

example_sources = GLOB('./example/*.cpp').split()
for example in example_sources:
    bin = example[:-4]
    Application(bin, Sources(example), Libraries('$OUT/lib/libaries.a'))

# UT
test_sources = GLOB('./test/test_*.cpp').split()
for test_file in test_sources:
    test_name = os.path.basename(test_file).split(".")[0]
    UTApplication(test_name, Sources(test_file, CppFlags(GLOB_CPPFLAGS_STR_FOR_TEST), CxxFlags(GLOB_CXXFLAGS_STR_FOR_TEST)) + Sources(api_sources, CppFlags(GLOB_CPPFLAGS_STR_FOR_TEST), CxxFlags(GLOB_CXXFLAGS_STR_FOR_TEST)), UTOnServer(False))
