
# we need decide:
# - compile options:
#   will be used for compiling our source. (*.cpp inside src)
#   by set `target_compile_option` on `byterpc_compile_option`
#
# - user(include our ut) compile options:
#   when user use byterpc, what compile options they need include (to compile their *.cpp)
#   by set `target_compile_option INTERFACE` on `byterpc` 
#   eg:  -include 'byterpc/util/rpf_conf.h'
#
# - user(include our ut) link options:
#   when user link our library, what link option they need include,
#   by set `target_link_libraries INTERFACE` on `byterpc`
#   ps: when newer verion cmake have `target_link_options`
#   eg: -lpthread  -lunwind or `asan`


# user always should with our defniation.
target_compile_options(byterpc_compile_option INTERFACE 
    -include "byterpc/util/rpc_conf.h"
)
target_link_libraries(byterpc_compile_option INTERFACE byterpc_include)

target_compile_options(byterpc_user_compile_option INTERFACE 
    -include "byterpc/util/rpc_conf.h"
)
target_link_libraries(byterpc_user_compile_option INTERFACE byterpc_include)

target_compile_options(byterpc_compile_option INTERFACE
    -Wall -Werror -fPIC -ggdb3 -fno-omit-frame-pointer
)

if (BYTERPC_ENABLE_DEPRECATED_API)
    message(STATUS "enable deprecated API for byterpc")
    target_compile_options(byterpc_compile_option INTERFACE -Wno-error=deprecated-declarations)
endif()

if(LOCAL_DEBUG)
    message(STATUS "byterpc build in local_debug mode")
    target_compile_options(byterpc_compile_option INTERFACE "-O0")
else() 
    message(STATUS "byterpc build in release mode")
    target_compile_definitions(byterpc_compile_option INTERFACE "NDEBUG")
endif()

if (BYTERPC_ENABLE_ASAN)
    message(STATUS "build byterpc with address sanitizer")
    # we need compile with asan open
    target_compile_options(byterpc_compile_option INTERFACE -fsanitize=address)
    # and user also need to link asan with.
    target_link_libraries(byterpc_user_compile_option INTERFACE -fsanitize=address)
endif()


if (BYTERPC_ENABLE_LTO_OPTIMIZATION)
    include(CheckIPOSupported)
    check_ipo_supported(RESULT lto_supported OUTPUT error)
    if (lto_supported)
        message(STATUS "LTO enabled")
    else()
        message(STATUS "LTO not supported: <${error}>")
    endif()

    set_property(TARGET byterpc_proto PROPERTY INTERPROCEDURAL_OPTIMIZATION TRUE)
    set_property(TARGET byterpc_builtin PROPERTY INTERPROCEDURAL_OPTIMIZATION TRUE)
    set_property(TARGET byterpc_protocol PROPERTY INTERPROCEDURAL_OPTIMIZATION TRUE)
    set_property(TARGET byterpc_transport PROPERTY INTERPROCEDURAL_OPTIMIZATION TRUE)
    set_property(TARGET byterpc_thread_helper PROPERTY INTERPROCEDURAL_OPTIMIZATION TRUE)
    set_property(TARGET byterpc_background_task PROPERTY INTERPROCEDURAL_OPTIMIZATION TRUE)
    set_property(TARGET byterpc_util PROPERTY INTERPROCEDURAL_OPTIMIZATION TRUE)
    set_property(TARGET byterpc_mem PROPERTY INTERPROCEDURAL_OPTIMIZATION TRUE)
    set_property(TARGET byterpc_metrics PROPERTY INTERPROCEDURAL_OPTIMIZATION TRUE)
    set_property(TARGET byterpc_iobuf PROPERTY INTERPROCEDURAL_OPTIMIZATION TRUE)
    set_property(TARGET byterpc_rpc PROPERTY INTERPROCEDURAL_OPTIMIZATION TRUE)
    set_property(TARGET byterpc PROPERTY INTERPROCEDURAL_OPTIMIZATION TRUE)
    if(TARGET byte_express)
        set_property(TARGET byte_express PROPERTY INTERPROCEDURAL_OPTIMIZATION TRUE)
    endif()
endif()


if (BYTERPC_ENABLE_GCOV)
    message(STATUS "enable coverage analysis with gcov")
    #set(CMAKE_CXX_OUTPUT_EXTENSION_REPLACE ON)
    # https://gcc.gnu.org/onlinedocs/gcc/Instrumentation-Options.html#index-gcov
    target_compile_options(byterpc_compile_option INTERFACE --coverage)
    target_link_libraries(byterpc_user_compile_option INTERFACE --coverage)
endif()
