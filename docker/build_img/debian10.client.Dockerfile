FROM hub.byted.org/base/debian.buster.base:32d1097ad9cbb0a55820d7b86a5e9257

ENV PATH ${PATH}:/opt/tiger/typhoon-blade

RUN apt-get update \
    && apt-get install -y --no-install-recommends \
    git clang-format-13 \
    gcc g++ ssh cmake make autoconf automake libtool pkg-config bison patch \
    libiberty-dev libnuma-dev lcov \
# used build libfuse3
    meson libnl-genl-3-dev libnl-3-dev libglib2.0-dev udev \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/* \
    && rm -rf /var/log/dpkg.log /var/log/apt/* \
    && ln -s /usr/bin/clang-format-13 /usr/bin/clang-format

