# https://cloud.bytedance.net/image/512648
FROM hub.byted.org/base/ci_debian_stretch

RUN apt-get update && apt-get install -y libaio-dev build-essential autoconf libtool lcov libcppunit-dev libz-dev netcat libc6-dbg

# set https_proxy when compile, since some modern thirdparty libs may fetch dependency dynamically
ENV https_proxy=sys-proxy-rd-relay.byted.org:8118
# install cmake-3.20.4
RUN wget https://cmake.org/files/v3.20/cmake-3.20.4-linux-x86_64.tar.gz -O /tmp/cmake.tar.gz && tar -zxf /tmp/cmake.tar.gz -C /tmp && mv /tmp/cmake-3.20.4-linux-x86_64/bin/cmake /usr/local/bin/cmake && mv /tmp/cmake-3.20.4-linux-x86_64/bin/ctest /usr/local/bin/ctest && mv /tmp/cmake-3.20.4-linux-x86_64/share/* /usr/local/share/ && rm -rf /tmp/cmake*

# Download, build, and install NASM 2.16.03
ENV NASM_VERSION 2.16.03
RUN wget https://www.nasm.us/pub/nasm/stable/nasm-$NASM_VERSION.tar.bz2 && \
    tar -xjf nasm-$NASM_VERSION.tar.bz2 && \
    cd nasm-$NASM_VERSION && \
    ./configure && \
    make && \
    make install && \
    cd .. && \
    rm -rf nasm-$NASM_VERSION nasm-$NASM_VERSION.tar.bz2

# Download, build, and install Valgrind 3.24
ENV VALGRIND_VERSION 3.24.0
RUN wget https://sourceware.org/pub/valgrind/valgrind-$VALGRIND_VERSION.tar.bz2 && \
    tar -xjf valgrind-$VALGRIND_VERSION.tar.bz2 && \
    cd valgrind-$VALGRIND_VERSION && \
    ./configure && \
    make && \
    make install && \
    cd .. && \
    rm -rf valgrind-$VALGRIND_VERSION valgrind-$VALGRIND_VERSION.tar.bz2

ENV https_proxy=""
