# ICM link: https://cloud.bytedance.net/icm/detail/677003/versions?x-resource-account=public
FROM hub.byted.org/third/velinux:lyra

RUN apt-get -q update \
  && apt-get -q install -y --no-install-recommends \
  wget yasm build-essential automake libtool zlib1g-dev python3

# install cmake
RUN export http_proxy=sys-proxy-rd-relay.byted.org:8118 https_proxy=sys-proxy-rd-relay.byted.org:8118 \
  && wget -q https://cmake.org/files/v3.20/cmake-3.20.4-linux-x86_64.tar.gz -O /tmp/cmake-3.20.4-linux-x86_64.tar.gz \
  && tar -zxf /tmp/cmake-3.20.4-linux-x86_64.tar.gz -C /usr/local \
  && ln -s /usr/local/cmake-3.20.4-linux-x86_64/bin/cmake /usr/local/bin/cmake \
  && ln -s /usr/local/cmake-3.20.4-linux-x86_64/bin/ctest /usr/local/bin/ctest \
  && ln -s /usr/bin/python3 /usr/bin/python \
  && rm -rf /tmp/*

