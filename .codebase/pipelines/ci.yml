name: Byte Lib CI Pipeline
trigger:
  # Run ci in cron job
  # https://bytedance.feishu.cn/wiki/wikcnbcXWzzXVoXYCvxbPZnxqQh
  cron:
    notification:
      when: [failure]
      # Byte Lib Team
      to: ["7165824210764546050"]
  # Run merge request pipeline automatically
  # https://bytedance.feishu.cn/wiki/wikcn0XxotllYX3nzz7AEv1h07d#1gAE9h
  change:
  # https://bytedance.feishu.cn/wiki/wikcnmGR04aP3KhZmqr4qxDMNfb
  manual:
jobs:
  Stretch:
    name: Stretch (GCC 6.3, C++14, DebianGNU/Linux9)
    image: hub.byted.org/compile/byte_stretch:6718a8d721e83358195f1b44828627cc
    working-directory: byte
    steps:
      - name: Build
        commands:
          - bash build.sh --ci --metrics2
      - name: UnittestAndValgrind
        id: run_test
        commands:
          - bash run_test.sh --ci
      - name: Upload Artifact
        uses: actions/upload-artifact
        if: ${{ Steps.run_test.Conclusion != "success" }}
        inputs:
          name: byte_unittest_and_valgrind_log
          path: build/byte/byte_test_logs
  Stretch11:
    name: Stretch11 (GCC 6.3, C++11, DebianGNU/Linux9)
    image: hub.byted.org/compile/byte_stretch:6718a8d721e83358195f1b44828627cc
    working-directory: byte
    steps:
      - name: Build with metrics 1.0
        commands:
          - bash build.sh --ci --cpp11
      - name: Unittest
        id: run_test1
        commands:
          - bash run_test.sh --ci --unittest
      - name: Upload Artifact
        uses: actions/upload-artifact
        if: ${{ Steps.run_test1.Conclusion != "success" }}
        inputs:
          name: byte_unittest_11_metrics1
          path: build/byte/byte_test_logs
      - name: Build with metrics 2.0
        commands:
          - rm -rf build && bash build.sh --ci --cpp11 --metrics2
      - name: Unittest
        id: run_test2
        commands:
          - bash run_test.sh --ci --unittest
      - name: Upload Artifact
        uses: actions/upload-artifact
        if: ${{ Steps.run_test1.Conclusion == "success" && Steps.run_test2.Conclusion != "success" }}
        inputs:
          name: byte_unittest_11_metrics2
          path: build/byte/byte_test_logs
  Buster11:
    name: Buster11 (GCC 8.3, C++11, DebianGNU/Linux9)
    image: hub.byted.org/compile/byte_buster:2f1414e0104c77ea6cdfe68d759cdd27
    working-directory: byte
    steps:
      - name: Build
        commands:
          - bash build.sh --ci --cpp11 --metrics2
  Buster:
    name: Buster (GCC 8.3, C++17, DebianGNU/Linux10)
    image: hub.byted.org/compile/byte_buster:2f1414e0104c77ea6cdfe68d759cdd27
    working-directory: byte
    steps:
      - name: Build
        commands:
          - bash build.sh --coverage --ci --metrics2
      - name: Unittest
        id: run_test
        commands:
          - bash run_test.sh --ci --coverage
      - name: Generate code coverage
        uses: actions/codecov
        if: ${{ Steps.run_test.Conclusion == "success" }}
        inputs:
          file: ./build/byte/byte_final.info
      - name: Upload Artifact
        uses: actions/upload-artifact
        if: ${{ Steps.run_test.Conclusion != "success" }}
        inputs:
          name: byte_unittest_and_valgrind_log
          path: build/byte/byte_test_logs
  Buster_Minimal_Build:
    name: Buster with minimal build (GCC 8.3, C++17, DebianGNU/Linux10)
    image: hub.byted.org/compile/byte_buster:2f1414e0104c77ea6cdfe68d759cdd27
    working-directory: byte
    steps:
      - name: Build with minimal feature set
        commands:
          - bash build.sh --coverage --ci --minimal --metrics2
  Asan_UBsan_Buster:
    name: Asan_UBsan_Buster (GCC 8.3, C++17, DebianGNU/Linux10)
    image: hub.byted.org/compile/byte_buster:2f1414e0104c77ea6cdfe68d759cdd27
    working-directory: byte
    steps:
      - name: Build Asan and UBsan
        id: run_build
        commands:
          - bash build.sh --ci --asan --ubsan --mode=relwithdebinfo --metrics2
      - name: Asan and UBsan
        id: run_asan
        if: ${{ Steps.run_build.Conclusion == "success"}}
        commands:
          - bash run_test.sh --ci --unittest
      - name: Upload Artifact
        uses: actions/upload-artifact
        if: ${{ Steps.run_asan.Conclusion != "success" && Steps.run_build.Conclusion == "success"}}
        inputs:
          name: byte_asan_and_ubsan_log
          path: build/byte/byte_test_logs
  Buster_LLVM11:
    name: Buster (Clang 11, C++17, DebianGNU/Linux10)
    image: hub.byted.org/compile/byte_buster_llvm11:a0e4fad3c413332b89b4143618a7dc56
    working-directory: byte
    steps:
      - name: Build
        commands:
          - bash build.sh --ci --metrics2
      - name: Unittest
        id: run_test
        commands:
          - bash run_test.sh --ci --unittest
      - name: Upload Artifact
        uses: actions/upload-artifact
        if: ${{ Steps.run_test.Conclusion != "success" }}
        inputs:
          name: byte_unittest_and_valgrind_log
          path: build/byte/byte_test_logs
  Buster_LLVM16:
    name: Buster (Clang 16, C++17, DebianGNU/Linux10)
    image: hub.byted.org/compile/byte_buster_llvm16:294de30a00c21d9e44a3b8285a84380e
    working-directory: byte
    steps:
      - name: Build
        commands:
          - bash build.sh --ci --metrics2
      - name: Unittest
        id: run_test
        commands:
          - bash run_test.sh --ci --unittest
      - name: Upload Artifact
        uses: actions/upload-artifact
        if: ${{ Steps.run_test.Conclusion != "success" }}
        inputs:
          name: byte_unittest_and_valgrind_log
          path: build/byte/byte_test_logs
  Bullseye:
    name: Bullseye (GCC 10.2, C++17, DebianGNU/Linux11)
    image: hub.byted.org/compile/byte_bullseye:c4427d6179f416caba7e3c9caab294a4
    working-directory: byte
    steps:
      - name: Build
        commands:
          - bash build.sh --ci --metrics2
      - name: Unittest
        id: run_test
        commands:
          - bash run_test.sh --ci --unittest
      - name: Upload Artifact
        uses: actions/upload-artifact
        if: ${{ Steps.run_test.Conclusion != "success" }}
        inputs:
          name: byte_unittest_and_valgrind_log
          path: build/byte/byte_test_logs
  Bullseye_LLVM16:
    name: Bullseye (Clang 16, C++17, DebianGNU/Linux11)
    image: hub.byted.org/compile/byte_bullseye_llvm_16:3aedec278f4e6c8c586f383ef0573240
    working-directory: byte
    steps:
      - name: Build
        commands:
          - bash build.sh --ci --metrics2
      - name: Unittest
        id: run_test
        commands:
          - bash run_test.sh --ci --unittest
      - name: Upload Artifact
        uses: actions/upload-artifact
        if: ${{ Steps.run_test.Conclusion != "success" }}
        inputs:
          name: byte_unittest_and_valgrind_log
          path: build/byte/byte_test_logs
  Blade_Buster:
    name: Blade_Buster (GCC 8.3, C++17, DebianGNU/Linux10)
    image: hub.byted.org/compile/byte_buster:2f1414e0104c77ea6cdfe68d759cdd27
    working-directory: byte
    steps:
      - name: Build
        commands:
          - cd .. && blade init && cd -
          - blade build ./... --toolchain='x86_64-gcc830' -p debug -o false --update-deps --cxxflags="-std=c++17"
      - name: Test
        commands:
          - blade test -p debug --toolchain x86_64-gcc830 --cxxflags="-std=c++17 -DBYTE_CI_ONLY=ON" --full-test -t4
  buster_aarch64:
    name: Aarch64 Buster (GCC 8.3, C++17, DebianGNU/Linux10)
    image: hub.byted.org/compile/byte_buster:2f1414e0104c77ea6cdfe68d759cdd27
    runs-on:
      arch: arm64
    working-directory: byte
    steps:
      - name: Build
        commands:
            - bash build.sh --ci --metrics2
  veLinux2:
    name: veLinux2 (GCC 12)
    image: hub.byted.org/compile/byte_velinux_2:1.0.0.16
    working-directory: byte
    steps:
      - name: Build
        commands:
          - bash build.sh --ci --metrics2
      - name: Unittest
        id: run_test
        commands:
          - bash run_test.sh --ci --unittest
      - name: Upload Artifact
        uses: actions/upload-artifact
        if: ${{ Steps.run_test.Conclusion != "success" }}
        inputs:
          name: byte_unittest_and_valgrind_log
          path: build/byte/byte_test_logs
