name: bds-client-cfs-sdk-ci
trigger:
  manual:
jobs:
  build_test_cfs_sdk_v2:
    name: build_test_cfs_sdk
    image: hub.byted.org/compile/bds.build.debian10:1.0.0.5
    runs-on:
      arch: amd64
    steps:
      - id: git_shallow_clone
        uses: actions/checkout
        inputs:
          depth: 1
          submodules: false
      - id: build_O3_noasan_nomock
        commands:
          - cd ${{Envs["CI_REPO_WORKSPACE"]}}/client/cfs_sdk && ./build.sh --build-type RelWithDebInfo --hide-thirdparty-symbol --enable-benchmark --disable-jemalloc --enable-it
      - id: build_O0_asan_mock
        commands:
          - cd ${{Envs["CI_REPO_WORKSPACE"]}}/client/cfs_sdk && ./build.sh --build-type Debug --enable-asan --enable-gcov --build-ut --enable-mock
      - id: unit_test
        commands:
          - cd ${{Envs["CI_REPO_WORKSPACE"]}}/client/cfs_sdk && ./ci.sh
