name: bds-dancedn-ci
trigger:
  manual:
jobs:
  dancedn_cloudfs:
    name: dancedn_cloudfs
    image: debian_buster:1.0.0
    runs-on:
      linux: {}
      spec: m1.8xlarge
    steps:
      - id: git-shallow-clone
        uses: actions/checkout
        inputs:
          depth: 1
          submodules: false
      - id: git-fetch-submodule
        commands:
          - cd ${{Envs["CI_REPO_WORKSPACE"]}}
          - git submodule update --init cloudfs_proto
      - id: start-docker
        commands:
          - docker run --rm -d -it --name ut -v /dev:/dev:ro -v /home/<USER>/home/<USER>/home/<USER>/compile/bds.build.debian10:1.0.0.10
      - id: build
        commands:
          - docker exec ut bash dancedn/build_cloudfs.sh --enable-gcov
      - id: run-ut
        commands:
          - docker exec ut python3 dancedn/unittest.py
      - name: gen-coverage-report
        commands:
          - docker exec ut bash dancedn/run_cloudfs_coverage.sh
      - name: run-codecov
        uses: actions/codecov
        inputs:
          fail_ci_if_error: true
          file: dancedn/build/lcov/coverage.info
          config:
            status:
              project:
                commission-tool-repository:
                  minimum_coverage: 60%
                  paths:
                    - "dancedn/src/cloudfs"
                    - "dancedn/src/dn_common"
              diffcommission-tool-repository:
                  minimum_coverage: 60%
                  line_limit: 20
                  paths:
                    - "dancedn/src/cloudfs"
                    - "dancedn/src/dn_common"
                