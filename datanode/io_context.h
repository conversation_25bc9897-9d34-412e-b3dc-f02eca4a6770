// Copyright (c) 2016 Baidu.com, Inc. All Rights Reserved
// Author <PERSON><PERSON><PERSON><PERSON><PERSON>(chen<PERSON><PERSON><PERSON>@baidu.com)
// Date: Sun Oct  9 10:03:50 CST 2016

#pragma once

#include <base/logging.h>
#include <bvar/bvar.h>
#include <algorithm>

#include "baidu/inf/aries/datanode/util/kylin.h"
#include "baidu/inf/aries/datanode/disk_manager.h"
#include "baidu/inf/aries/datanode/vlet_manager.h"
#include "baidu/inf/aries/datanode/disk_agent.h"
#include "baidu/inf/aries/datanode/storage/append/append_record.h"
#include "baidu/inf/aries/datanode/storage/random/linked_shard_record.h"
#include "baidu/inf/aries-api/common/proto/datanode.pb.h"
#include "baidu/inf/aries-api/common/lock.h"
#include "baidu/inf/aries/datanode/flags.h"
#include "baidu/inf/aries/common/common.h"

namespace aries {
namespace datanode {

extern bvar::LatencyRecorder g_datanode_request_read_latency;
extern bvar::LatencyRecorder g_datanode_request_write_latency;
extern bvar::LatencyRecorder g_datanode_read_request_enqueue_latency;
extern bvar::LatencyRecorder g_datanode_write_request_enqueue_latency;

class DiskAgent;

struct RewriteOptions {
    uint64_t last_rewrite_time = 0;
    uint32_t append_zone_rewrite_rate = 0;
    uint32_t daily_rewrite_start_time = 0;
    uint32_t daily_rewrite_duration_second = 0;
    bool     is_force_rewrite = false;
};

struct RewriteInfo {
    uint64_t before_rewrite_size = 0;
    uint64_t after_rewrite_size = 0;
    uint64_t writable_size = 0;
    uint64_t last_finish_rewrite_time = 0;
    uint64_t vlet_force_rewrite_duration_second = 0;
};

struct IoContext : public DatanodeContext {
    uint32_t timeout_ms;
    uint64_t en_queue_time;
    uint64_t en_queue_time_us;
    uint32_t priority = 0;
    double speed_coefficient = 1.0;
    DatanodeAction action;
    VletPtr vlet_ptr = nullptr;
    __uint128_t key = 0;

    ~IoContext() {
        if (vlet_ptr) {
            ARIES_RPC_DEBUG_LOG(TRACE) << "vid:" << vid << " total ref:" << vlet_ptr.use_count();
        }
    }

    virtual aries::pb::Status* mutable_status() {
        return nullptr;
    }
};

struct PutContext : public IoContext {
    PutContext() {
        cmd = "put";
    }

    ~PutContext() {
        g_datanode_request_write_latency << base::gettimeofday_us() - en_queue_time_us;
    }

    // life not owned by me
    const ::aries::pb::ShardPutRequest*     request = nullptr;
    ::aries::pb::ShardPutResponse*          response = nullptr;
    baidu::rpc::Controller*                 cntl = nullptr;
    virtual aries::pb::Status* mutable_status() {
        return response->mutable_status();
    }
};

struct UpdateMetaContext : public IoContext {
    UpdateMetaContext() {
        cmd = "updateMeta";
    }

    // life not owned by me
    const ::aries::pb::ShardUpdateMetaRequest*     request = nullptr;
    ::aries::pb::AckResponse*          response = nullptr;
    baidu::rpc::Controller*                 cntl = nullptr;
    virtual aries::pb::Status* mutable_status() {
        return response->mutable_status();
    }
};

struct UpdateShardTtlContext : public IoContext {
    UpdateShardTtlContext() {
        cmd = "updateshardttl";
    }

    // life not owned by me
    const ::aries::pb::UpdateShardTtlRequest*     request = nullptr;
    ::aries::pb::AckResponse*          response = nullptr;
    baidu::rpc::Controller*                 cntl = nullptr;
    virtual aries::pb::Status* mutable_status() {
        return response->mutable_status();
    }
};

struct CopyVletContext : public IoContext {
    CopyVletContext() {
        cmd = "copy_vlet";
    }

    // life not owned by me
    const ::google::protobuf::Message*     request = nullptr;
    ::google::protobuf::Message*          response = nullptr;
    baidu::rpc::Controller*                 cntl = nullptr;
    int                                   vlet_type = ENGINE_LINKED;
    virtual aries::pb::Status* mutable_status() {
        if (vlet_type == ENGINE_LINKED) {
            auto res = static_cast<::aries::pb::CopyLinkedVletResponse*>(response);
            return res->mutable_status();
        } else if (vlet_type == ENGINE_APPEND) {
            auto res = static_cast<::aries::pb::CopyAppendVletResponse*>(response);
            return res->mutable_status();
        } else if (vlet_type == ENGINE_ZONE) {
            auto res = static_cast<::aries::pb::CopyZoneVletResponse*>(response);
            return res->mutable_status();
        }
        return nullptr;
    }
};

struct GetRewriteInfoContext : public IoContext {
    GetRewriteInfoContext() {
        cmd = "get_rewrite_info";
    }

    void init_state() {
        total_free_size = info.before_rewrite_size - info.after_rewrite_size;

        if (info.before_rewrite_size == 0) {
            hole_rate = 0;
        } else {
            hole_rate = total_free_size * 100 / info.before_rewrite_size;
        }

        uint64_t now = base::gettimeofday_s();
        //check if need force rewrite
        if (info.vlet_force_rewrite_duration_second != 0
                && now - info.last_finish_rewrite_time > info.vlet_force_rewrite_duration_second) {
            //need rewrite vlet all data;
            rewrite_ignore_hole_rate = true;
        }
    }

    bool operator< (const GetRewriteInfoContext& other) const {
        if (hole_rate == other.hole_rate) {
            return total_free_size > other.total_free_size;
        }

        return hole_rate > other.hole_rate;
    }

    RewriteInfo info;
    RewriteOptions  options;
    uint64_t total_free_size = 0;
    uint32_t hole_rate = 0;
    bool can_be_rewritten = true;
    bool rewrite_ignore_hole_rate = false;    
};

using GetRewriteInfoContextPtr = std::shared_ptr<GetRewriteInfoContext>;

class RewriteDetailInfo {
public:
    RewriteDetailInfo() {}
    virtual ~RewriteDetailInfo() {}    
};

using RewriteDetailInfoPtr = std::shared_ptr<RewriteDetailInfo>;

struct RewriteVletContext : public IoContext {
    RewriteVletContext() {
        cmd = "rewrite_vlet";
        is_finish = false;
    }

    ~RewriteVletContext() {
    }

    aries::pb::Status* mutable_status() {
        return &st;
    }

    RewriteOptions  options;    
    bool is_finish;
    aries::pb::Status st;
    uint64_t rewrite_task_start_time = 0;
    uint64_t rewrite_data_len = 0;
    GetRewriteInfoContextPtr rewrite_info_context_ptr;
    bool init_rewrite_context = false;
    RewriteDetailInfoPtr rewrite_detail_info_ptr;
};

struct ZonefilesDetailContext : public IoContext {
    ZonefilesDetailContext() {
        cmd = "get_zonefiles_detail";
    }
    ~ZonefilesDetailContext() {
        if (zonefiles_detail != nullptr) {
            delete zonefiles_detail;
            zonefiles_detail = nullptr;
        }
    }
    std::shared_ptr<common::SyncPoint> sync_point = nullptr;
    aries::pb::ZonefilesDetail* zonefiles_detail = nullptr;
};

struct AcquireRewriteIOTokenContext : public IoContext {
    AcquireRewriteIOTokenContext() {
        cmd = "acquire_rewrite_io_token";
    }

    ~AcquireRewriteIOTokenContext() {
        if (sync_point != nullptr) {
            sync_point->signal();
        }
    }

    virtual aries::pb::Status* mutable_status() {
        return &st;
    }

    aries::pb::Status st;
};

struct GetContext : public IoContext {
    GetContext() {
        cmd = "get";
    }

    ~GetContext();

    virtual aries::pb::Status* mutable_status() {
        return response->mutable_status();
    }

    // life not owned by me
    const ::aries::pb::ShardGetRequest* request = nullptr;
    ::aries::pb::ShardGetResponse* response = nullptr;
    baidu::rpc::Controller* cntl = nullptr;
    bool skip_admit_to_cache = false;
};

struct GetFromCacheContext : public IoContext {
    GetFromCacheContext() {
        cmd = "cache_get";
    }

    // life not owned by me
    const ::aries::pb::ShardGetRequest* request = nullptr;
    ::aries::pb::ShardGetResponse* response = nullptr;
    baidu::rpc::Controller* cntl = nullptr;

    virtual aries::pb::Status* mutable_status() {
        return response->mutable_status();
    }
};

struct BatchGetContext : public IoContext {
    BatchGetContext() {
        cmd = "batch_get";
    }

    baidu::rpc::Controller*                 cntl = nullptr;
    const ::aries::pb::ShardBatchGetRequest*      request = nullptr;
    ::aries::pb::ShardBatchGetResponse*     response = nullptr;
    DiskAgent*                              disk_agent = nullptr;
    uint32_t                                real_io_count = 0;

    virtual aries::pb::Status* mutable_status() {
        return response->mutable_status();
    }
    ~BatchGetContext();
};

struct GetRecordIndexInfoContext : public IoContext {
    GetRecordIndexInfoContext() {
        cmd = "get_record_index_info";
    }

    baidu::rpc::Controller*                 cntl = nullptr;
    const ::aries::pb::GetRecordIndexInfoRequest*      request = nullptr;
    ::aries::pb::GetRecordIndexInfoResponse*     response = nullptr;

    virtual aries::pb::Status* mutable_status() {
        return response->mutable_status();
    }
    ~GetRecordIndexInfoContext() {};
};

struct GetSegmentContext : public IoContext {
    GetSegmentContext() {
        cmd = "get_segment";
    }

    baidu::rpc::Controller*                 cntl = nullptr;
    std::shared_ptr<ContinuousSegment>         segment = nullptr;
    const ::aries::pb::ShardBatchGetRequest*      request = nullptr;
    ::aries::pb::ShardBatchGetResponse*     response = nullptr;
    DiskAgent*                              disk_agent = nullptr;

    virtual aries::pb::Status* mutable_status() {
        return response->mutable_status();
    }
};

struct RemoveContext : public IoContext {
    RemoveContext() {
        cmd = "remove";
    }

    // life not owned by me
    const ::aries::pb::ShardRemoveRequest*     request = nullptr;
    ::aries::pb::AckResponse*          response = nullptr;
    baidu::rpc::Controller*                 cntl = nullptr;
    virtual aries::pb::Status* mutable_status() {
        return response->mutable_status();
    }
};

struct RestoreContext : public IoContext {
    RestoreContext() {
        cmd = "restore";
    }

    // life not owned by me
    const ::aries::pb::ShardRestoreRequest*     request = nullptr;
    ::aries::pb::AckResponse*          response = nullptr;
    baidu::rpc::Controller*                 cntl = nullptr;
    virtual aries::pb::Status* mutable_status() {
        return response->mutable_status();
    }
};

struct PurgeContext : public IoContext {
    PurgeContext() {
        cmd = "purge";
    }
    ~PurgeContext() {
        if (periodic_purge) {
            ARIES_RPC_DONE_LOG(NOTICE) << " cmd:" << cmd
                << " code:" << status->code() << " msg:" << status->msg()
                << " cost_us:[total=" << base::gettimeofday_us() - en_queue_time_us << "]";
            status = nullptr;
            delete request;
            delete response;
        }
    }

    bool purge_blob_without_ttl = false;
    bool periodic_purge = false;
    // if periodic_purge life owned by me
    const ::aries::pb::PurgeVletRequest* request = nullptr;
    ::aries::pb::AckResponse* response = nullptr;
    baidu::rpc::Controller* cntl = nullptr;
    virtual aries::pb::Status* mutable_status() {
        return response->mutable_status();
    }
};

struct UpdateVletContext : public IoContext {
    UpdateVletContext() {
        cmd = "update_membership";
    }

    aries::pb::Status* mutable_status() {
        return response->mutable_status();
    }

    uint64_t    max_holes_size_for_fast_remove = 0;
    uint64_t    min_record_size_for_fast_remove = 0;
    bool        permit_fast_remove = false;
    uint32_t    append_zone_rewrite_rate = 5;
    uint32_t    daily_rewrite_start_time = 0;
    uint32_t    daily_rewrite_duration_second = 24 * 3600;
    aries::pb::ShardCompressOption shard_compress_option;
    // life not owned by me
    const ::aries::pb::UpdateMembershipRequest*     request = nullptr;
    ::aries::pb::AckResponse*                       response = nullptr;
    baidu::rpc::Controller*                         cntl = nullptr;
};

struct UpdateVletReq : public UpdateVletContext {
    UpdateVletReq(std::shared_ptr<common::SyncPoint> sync_point) {
        request = &pb_req;
        response = &pb_res;
        this->sync_point = sync_point;
    }

    aries::pb::UpdateMembershipRequest      pb_req;
    aries::pb::AckResponse                  pb_res;
};

#if defined(_CHECK_TEST) || defined(_UNIT_TEST)
struct ChangeShardContext : public IoContext {
    ChangeShardContext() {
        InitAsyncContext(this);
    }

    virtual aries::pb::Status* mutable_status() {
        return res->mutable_status();
    }

    uint64_t                vbid;
    int32_t                 op_code;

    baidu::rpc::Controller*                 cntl = nullptr;
    aries::pb::AckResponse*                        res = nullptr;
};
#endif

struct GetVletInfoContext : public AsyncContext {
    GetVletInfoContext() {
        InitAsyncContext(this);
    }

    Vlet*                   vlet_ptr = nullptr;
    aries::pb::DatanodeVletInfo*   vlet_info = nullptr;
    common::SyncPoint*      sync_point = nullptr;
};

struct ShardCheckContext : public IoContext {
    ShardCheckContext() {
        cmd = "blob_check";
    }
    virtual aries::pb::Status* mutable_status() {
        return response->mutable_status();
    }
    std::shared_ptr<::aries::pb::CheckVletShardsRequest> request;
    ::aries::pb::AckResponse* response = nullptr;
    std::shared_ptr<::aries::pb::ReportCheckVletShardsRequest> report;
    std::shared_ptr<common::TokenPool> blob_check_token_pool;
    std::shared_ptr<common::TokenPool> block_read_token_pool;
    bool* interrupt;
};

struct VbidLocation {
    uint64_t vbid = 0;
    RecordLocation loc;
    bool operator < (const VbidLocation& rhs) const {
        return loc < rhs.loc;
    }
};

struct AppendVbidLocation {
    uint64_t vbid = 0;
    AppendRecordLocation loc;
    bool operator < (const AppendVbidLocation& rhs) const {
        return loc < rhs.loc;
    }
};

struct ZoneVbidLocation {
    uint64_t vbid = 0;
    ZoneRecordLocation location;

    bool operator < (const ZoneVbidLocation& rhs) const {
        return location < rhs.location;
    }
};

struct GetVbidLocationContext : public AsyncContext {
    GetVbidLocationContext() {
        InitAsyncContext(this);
    }

    baidu::rpc::ClosureGuard       done_guard;
    VletPtr                        vlet_ptr;

    std::shared_ptr<common::SyncPoint>   sync_point = nullptr;
    const aries::pb::ShardBatchGetRequest*        request = nullptr;
    aries::pb::ShardBatchGetResponse*       response = nullptr;
    baidu::rpc::Controller*        cntl = nullptr;
    std::vector<VbidLocation> locations;
    std::vector<AppendVbidLocation> append_locations;
};

void fill_error_code_and_ack(IoContext* ctx, AriesErrno code, const std::string& msg);

} // end namespace of datanode
} // end namespace of aries
