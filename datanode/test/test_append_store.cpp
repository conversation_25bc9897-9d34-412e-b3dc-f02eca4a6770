// Copyright 2019 Baidu Inc. All Rights Reserved.
// @Author: <EMAIL>
// @Created Time : Fri 19 Apr 2019 03:56:51 PM CST
// @File Name: test_append_store.cpp
// @Description:

#include "baidu/inf/aries/common/aries_ut_pread.h"

#include <random>

#include <gtest/gtest.h>
#include <base/crc32c.h>
#include "rocksdb/db.h"
#include "rocksdb/table.h"
#include "rocksdb/slice.h"
#include "rocksdb/options.h"
#include "rocksdb/cache.h"
#include "rocksdb/status.h"
#include "baidu/inf/aries-api/common/utils.h"
#include "baidu/inf/aries/datanode/storage/append/append_store.h"
#include "baidu/inf/aries/datanode/vlet/append_vlet.h"
#include "baidu/inf/aries/datanode/storage/random/linked_shard_record.h"
#include "baidu/inf/aries/datanode/copy_vlet.h"
#include "baidu/inf/aries/common/bmock_util.h"
#include "baidu/inf/aries/common/common.h"
#include "baidu/inf/aries/datanode/storage/append/append_shard_scanner.h"
#include "baidu/inf/aries/datanode/test/common/buffer_gen.h"

namespace aries {
namespace datanode {

using ::testing::Return;
using ::testing::DoAll;
using ::testing::SetArgPointee;
using ::testing::_;
using ::testing::SetErrnoAndReturn;

DECLARE_BMOCK_METHOD(4, aries_ut_pread, ssize_t(int, void *, size_t, off_t));
DEFINE_BMOCK_METHOD(4, aries_ut_pread, ssize_t(int, void *, size_t, off_t));

MAKE_BMOCK_NS_CLASS_METHOD(0, aries::datanode, AppendShardScanner, restore,
        Status());
MAKE_BMOCK_NS_CLASS_METHOD(0, aries::datanode, AppendShardScanner, vlet_info, 
        aries::pb::AppendVletInfo());
MAKE_BMOCK_NS_CLASS_METHOD(6, aries::common, StandardRecordSerializer, deserialize, 
        Status(const common::Buffer&, uint32_t*, uint64_t*, aries::pb::SliceDescMeta*, 
               pb::ShardMeta*, base::IOBuf*));
BMOCK_NS_CLASS_METHOD1(aries::datanode, AppendStore, alloc_zonefile,  
        Status(const bool));
BMOCK_NS_CLASS_METHOD0(aries::datanode, AppendStore, rebuild_zonefile_map,
        Status());
BMOCK_NS_CLASS_METHOD3(rocksdb, DB, Put,
        rocksdb::Status(const rocksdb::WriteOptions&, const rocksdb::Slice&,
            const rocksdb::Slice&));
BMOCK_NS_CLASS_METHOD1(aries::datanode, Zonefile, get_physical_location,
        Status(uint64_t* ret));
BMOCK_NS_CLASS_METHOD3(aries::datanode, AppendStore, batch_write_record,
        Status(const std::vector<AppendRecordLocation>&, base::IOBuf&, const bool));
BMOCK_NS_CLASS_METHOD3(aries::datanode, AppendStore, batch_get_record,
        Status(AppendRecordLocation&, std::vector<AppendRecordLocation>*, base::IOBuf*));
BMOCK_NS_CLASS_METHOD4(aries::datanode, AppendStore, locate_zonefile,
        Status(uint64_t, AppendRecordLocation*, ZonefilePtr*, uint32_t*));
BMOCK_NS_CLASS_METHOD0(aries::datanode, AppendRecordLocation, is_expired,
        bool());

BMOCK_NS_METHOD2(base::crc32c, Value, uint32_t(const char* data, size_t n));

class AppendStoreIndexerTest : public ::testing::Test {
protected:

    AppendStoreIndexerTest() {
        ::system(" rm -rf ./test_db");
        rocksdb::DB* tmp;
        rocksdb::Options options;
        options.create_if_missing = true;
        Status s = rocksdb::DB::Open(options, "./test_db", &tmp);
        assert(s.ok());
        _db.reset(tmp);
        BMOCK_NS_STOP(base::crc32c, Value, uint32_t(const char* data, size_t n));
    }

    virtual ~AppendStoreIndexerTest() {
        _db.reset();
        _db = nullptr;
        ::system("rm -rf ./test_db");
    }
private:
    std::shared_ptr<rocksdb::DB> _db;
};

static bool operator==(const AppendRecordLocation& l, const AppendRecordLocation& r) {
        return (l.file_index == r.file_index
                && l.offset == r.offset
                && l.len == r.len
                && l.delete_time == r.delete_time);
}

TEST_F(AppendStoreIndexerTest, test_get_destroy_put) {
    AppendStoreIndexer indexer(1, 1, _db);
    AppendRecordLocation loc;
    auto status = indexer.get(1, &loc);
    EXPECT_EQ(status.code(), AIE_BLOB_NOT_EXIST);

    RecordKey key(1, 1);
    std::string value("data");
    _db->Put(rocksdb::WriteOptions(), key.to_slice(), rocksdb::Slice(value));

    status = indexer.get(1, &loc);
    EXPECT_EQ(status.code(), AIE_FAIL);

    loc.delete_time = 100;
    status = indexer.put(1, loc);
    EXPECT_EQ(status.code(), AIE_OK);

    status = indexer.get(1, &loc);
    EXPECT_EQ(status.code(), AIE_MARK_REMOVED);

    loc.delete_time = 0;
    status = indexer.put(1, loc);
    EXPECT_EQ(status.code(), AIE_OK);

    AppendRecordLocation ret_loc;
    status = indexer.get(1, &ret_loc);
    EXPECT_EQ(status.code(), AIE_OK);
    EXPECT_EQ(ret_loc, loc);

    status = indexer.destroy();
    EXPECT_EQ(status.code(), AIE_OK);

    status = indexer.get(1, &loc);
    EXPECT_EQ(status.code(), AIE_BLOB_NOT_EXIST);
}

TEST_F(AppendStoreIndexerTest, test_put_batch) {
    AppendStoreIndexer indexer(1, 1, _db);
    AppendRecordLocation loc;

    std::vector<uint64_t> vbid_list;
    std::vector<AppendRecordLocation> loc_list;
    vbid_list.push_back(1);
    loc_list.push_back(loc);
    vbid_list.push_back(2);
    loc_list.push_back(loc);

    auto status = indexer.put_batch(vbid_list, loc_list);

    AppendRecordLocation ret_loc;
    status = indexer.get(2, &ret_loc);
    EXPECT_EQ(status.code(), AIE_OK);
    EXPECT_EQ(ret_loc, loc);
}

TEST_F(AppendStoreIndexerTest, test_rewrite) {
    // rewrite when not exist
    AppendStoreIndexer indexer(1, 1, _db);
    AppendRecordLocation old_loc;
    old_loc.file_index = 1;
    old_loc.offset = 3;
    old_loc.len = 4;
    AppendRecordLocation new_loc;
    new_loc.file_index = 2;
    new_loc.offset = 3;
    new_loc.len = 4;
    auto status = indexer.rewrite(1, old_loc, new_loc);
    EXPECT_EQ(status.code(), 0);

    //can't parse pb
    RecordKey key(1, 1);
    std::string value("data");
    _db->Put(rocksdb::WriteOptions(), key.to_slice(), rocksdb::Slice(value));
    status = indexer.rewrite(1, old_loc, new_loc);
    EXPECT_NE(status.code(), 0);

    //old loc not equal.
    AppendRecordLocation loc;
    loc.file_index = 3;
    loc.offset = 3;
    loc.len = 4;
    loc.delete_time = 0;
    status = indexer.put(2, loc);
    EXPECT_EQ(status.code(), AIE_OK);
    status = indexer.rewrite(2, old_loc, new_loc);
    EXPECT_EQ(status.code(), AIE_OK);

    loc.file_index = 1;
    loc.offset = 2;
    status = indexer.put(20, loc);
    EXPECT_EQ(status.code(), AIE_OK);
    status = indexer.rewrite(20, old_loc, new_loc);
    EXPECT_EQ(status.code(), AIE_OK);

    loc.offset = 3;
    loc.len = 5;
    status = indexer.put(200, loc);
    EXPECT_EQ(status.code(), AIE_OK);
    status = indexer.rewrite(200, old_loc, new_loc);
    EXPECT_EQ(status.code(), AIE_OK);
    loc.len = 4;

    //succ
    loc.file_index = 1;
    status = indexer.put(3, loc);
    EXPECT_EQ(status.code(), AIE_OK);
    status = indexer.rewrite(3, old_loc, new_loc);
    EXPECT_EQ(status.code(), AIE_OK);

    AppendRecordLocation get_loc;
    status = indexer.get(3, &get_loc);
    EXPECT_EQ(status.code(), AIE_OK);
    EXPECT_EQ(get_loc.file_index, new_loc.file_index);
    EXPECT_EQ(get_loc.offset, new_loc.offset);
    EXPECT_EQ(get_loc.len, new_loc.len);

    //rewrite with delete time update.
    loc.delete_time = 1;
    status = indexer.put(4, loc);
    EXPECT_EQ(status.code(), AIE_OK);
    status = indexer.rewrite(4, old_loc, new_loc);
    EXPECT_EQ(status.code(), AIE_OK);
}

TEST_F(AppendStoreIndexerTest, test_remove) {
    AppendStoreIndexer indexer(1, 1, _db);
    AppendRecordLocation loc;
    auto status = indexer.put(1, loc);
    EXPECT_EQ(status.code(), AIE_OK);

    status = indexer.remove(1);
    EXPECT_EQ(status.code(), AIE_OK);

    status = indexer.get(1, &loc);
    EXPECT_EQ(status.code(), AIE_BLOB_NOT_EXIST);
}

class AppendStoreTest : public ::testing::TestWithParam<bool> {
protected:

    AppendStoreTest() {
        FLAGS_print_debug_log = true;
        ::system(" rm -rf ./test");
        sleep(10);
        ::system(" mkdir -p ./test/db ");
        StoreOptions opts;
        _store.reset(new AppendStore(opts));
        open_db();
        LOG(TRACE) << "begin AppendStoreTest()";
        BMOCK_NS_STOP(base::crc32c, Value, uint32_t(const char* data, size_t n));
    }

    virtual ~AppendStoreTest() {
      _db.reset();
      _db = nullptr;
      ::system(" rm -rf ./test ");
      LOG(TRACE) << "begin ~AppendStoreTest()";
    }

    void prepare_store(bool use_standard_record_layout);
    void prepare_data();
    void clear_data();
protected:
    void open_db() {
        rocksdb::Options options;
        rocksdb::BlockBasedTableOptions table_options;
        // Optimize RocksDB. This is the easiest way to get RocksDB to perform well
        // options.IncreaseParallelism();
        // options.OptimizeLevelStyleCompaction();
        // create the DB if it's not already present
        options.create_if_missing = true;
        options.create_missing_column_families = true;
        options.max_open_files = FLAGS_db_max_open_file;
        options.target_file_size_base =  16 * 1024 * 1024; // 16M

        options.use_direct_reads = false;
        options.use_direct_io_for_flush_and_compaction = false;
        table_options.cache_index_and_filter_blocks = true;
        table_options.pin_l0_filter_and_index_blocks_in_cache = true;
        table_options.block_size = 4 * 1024;

        //table_options.filter_policy.reset(NewBloomFilterPolicy(20));
        options.table_factory.reset(rocksdb::NewBlockBasedTableFactory(table_options));

        // open DB with two column families
        std::vector<rocksdb::ColumnFamilyDescriptor> column_families;
        // have to open default column family

        auto default_cf_options = rocksdb::ColumnFamilyOptions();
        default_cf_options.target_file_size_base =  16 * 1024 * 1024; // 16M
        default_cf_options.table_factory.reset(
            rocksdb::NewBlockBasedTableFactory(table_options));

        column_families.push_back(rocksdb::ColumnFamilyDescriptor(
                rocksdb::kDefaultColumnFamilyName, default_cf_options));

        // open the new one, too
        rocksdb::ColumnFamilyOptions data_cf_options;
        column_families.push_back(rocksdb::ColumnFamilyDescriptor("data",
                data_cf_options));

        std::vector<rocksdb::ColumnFamilyHandle*> handles;
        rocksdb::DB* tmp = nullptr;
        std::string db_path = "./test/db";
        rocksdb::Status s = rocksdb::DB::Open(options, db_path, column_families, &handles, &tmp);
        Status status(s);
        LOG(TRACE) << s.code() << " status:" << status;
        assert(s.ok());
        _db.reset(tmp);
    }
private:
    std::shared_ptr<rocksdb::DB> _db;
    std::unique_ptr<AppendStore> _store;
};

void AppendStoreTest::prepare_store(bool use_standard_record_layout) {
    //prepare env.
    g_fs->register_disk("./test/", "", 0, 0);
    StoreOptions opts;
    _store = std::unique_ptr<AppendStore>(new AppendStore(opts));
    aries::pb::AppendVletInfo vlet_info;
    vlet_info.set_volume_id(1);
    vlet_info.set_vlet_type(81);
    vlet_info.set_align_size(4096);
    vlet_info.mutable_space_info()->set_k(18);
    vlet_info.set_shard_index(12);
    vlet_info.set_use_standard_record_layout(use_standard_record_layout);
    auto status = _store->create(vlet_info, _db, "./test/A_1_1/");
    EXPECT_EQ(status.code(), AIE_OK);
}

void AppendStoreTest::prepare_data() {
    pb::ShardMeta meta;
    meta.set_blob_len(1);
    meta.set_blob_crc(2);
    for (int i = 10; i < 256 * 8 * 4 + 10; ++i) {
        ::base::IOBuf io_buf;
        std::string str(128*1024, 'c');
        io_buf.append(str);
        meta.set_shard_len(str.size());
        meta.set_shard_crc(common::build_crc(io_buf));
        auto status = _store->put(i, meta, io_buf);
        EXPECT_EQ(status.code(), AIE_OK);
    }
}

void AppendStoreTest::clear_data() {
    for (int i = 10; i < 256 * 8 * 4 + 10; ++i) {
        auto status = _store->remove(i, 1);
        EXPECT_EQ(status.code(), AIE_OK);
        status = _store->remove(i, 0);
        EXPECT_EQ(status.code(), AIE_OK);
    }
}

TEST_F(AppendStoreTest, test_create) {
    EXPECT_CALL(BMOCK_NS_CLASS_OBJECT(aries::datanode, AppendStore, alloc_zonefile),
            alloc_zonefile(_))
            .WillOnce(Return(Status(AIE_FAIL)))
            .WillRepeatedly(Return(Status()));
    EXPECT_CALL(BMOCK_NS_CLASS_OBJECT(rocksdb, DB, Put),
            Put(_, _, _))
            .WillRepeatedly(Return(rocksdb::Status::TimedOut()));

    //create a illegal path.
    StoreOptions opts;
    aries::pb::AppendVletInfo vlet_info;
    vlet_info.set_align_size(4096);
    std::unique_ptr<AppendStore> store(new AppendStore(opts));
    auto status = store->create(vlet_info, _db, "/dev/unreachable_path");
    EXPECT_TRUE(status.msg().find("create directory failed") != std::string::npos);
    //alloc zonefile fail
    status = store->create(vlet_info, _db, "./test/A_1_1");
    EXPECT_EQ(status.code(), AIE_FAIL);
    status = store->create(vlet_info, _db, "./test/A_1_1");
    EXPECT_EQ(status.code(), AIE_OK);
    BMOCK_NS_CLASS_STOP(rocksdb, DB, Put, rocksdb::Status(const rocksdb::WriteOptions&, 
                const rocksdb::Slice&, const rocksdb::Slice&));

    status = store->create(vlet_info, _db, "./test/A_1_1");
    EXPECT_EQ(status.code(), AIE_OK);

    BMOCK_NS_CLASS_STOP(aries::datanode, AppendStore, alloc_zonefile, 
            Status(const bool));
}

TEST_F(AppendStoreTest, test_load_dir) {
    //regist disk
    g_fs->register_disk("./test/", "", 0, 0);

    //load empty dir
    ::system(" mkdir -p ./test/A_1_1 ");
    _store->_path = "./test/A_1_1";
    auto ret = _store->load_dir();
    EXPECT_FALSE(ret);

    //empty file.
    ::system(" touch ./test/A_1_1/trash_file ");
    ret = _store->load_dir();
    EXPECT_FALSE(ret);

    //wrong file.
    ::system(" echo 'hello world' > ./test/A_1_1/trash_file ");
    ret = _store->load_dir();
    EXPECT_FALSE(ret);
    ::system(" rm -rf ./test/A_1_1/trash_file ");

    //create a health store;
    StoreOptions opts;
    AppendStore store(opts);
    aries::pb::AppendVletInfo vlet_info;
    vlet_info.set_align_size(4096);
    auto status = store.create(vlet_info, _db, "./test/A_1_1/");
    EXPECT_EQ(status.code(), 0);

    ret = _store->load_dir();
    EXPECT_TRUE(ret);
}

TEST_P(AppendStoreTest, test_rebuild_zonefile_map) {
    //test normal rebuild.
    FLAGS_fast_recovery_mode = true;
    g_fs->register_disk("./test/", "", 0, 0);
    StoreOptions opts;
    AppendStore store(opts);
    aries::pb::AppendVletInfo vlet_info;
    vlet_info.set_align_size(4096);
    bool use_standard_record_layout = GetParam();
    vlet_info.set_use_standard_record_layout(use_standard_record_layout);
    auto status = store.create(vlet_info, _db, "./test/A_1_1/");
    EXPECT_EQ(status.code(), AIE_OK);

    pb::ShardMeta meta;
    meta.set_blob_len(0);
    meta.set_blob_crc(0);
    meta.set_shard_len(0);
    base::IOBuf data;
    status = store.put(1, meta, data);
    EXPECT_EQ(status.code(), AIE_OK);
    auto used_size = store.used_size();
    EXPECT_EQ(used_size, 4096 * 3);

    uint64_t vbid = 1;
    uint64_t finger = common::MurmurHash64A(&vbid, sizeof(uint64_t));
    ASSERT_EQ(finger, store.get_fingerprint_by_slot(1));
    ASSERT_EQ(1, store._record_num);
    ASSERT_EQ(1, store._max_vbid);

    AppendStore store2(opts);
    store2.open(_db, "./test/A_1_1");
    auto used_size2 = store2.used_size();
    EXPECT_EQ(used_size, used_size2);
    LOG(TRACE) << " used_size is :" << used_size;
    BMOCK_NS_CLASS_RESUME(aries::datanode, Zonefile, get_physical_location,
             Status(uint64_t*));
    EXPECT_CALL(BMOCK_NS_CLASS_OBJECT(aries::datanode, Zonefile, get_physical_location),
            get_physical_location(_))
            .WillOnce(DoAll(SetArgPointee<0>(4096 * 10), Return(Status(AIE_OK))));
    AppendStore store3(opts);
    store3.open(_db, "./test/A_1_1");
    EXPECT_EQ(store3._curr_write_file->write_location(), 2);
    EXPECT_EQ(store3._curr_write_file->is_seal(), false);

    BMOCK_NS_CLASS_STOP(aries::datanode, Zonefile, get_physical_location,
            Status(uint64_t*));

    //rebuild with multi zonefiles.
    store3.alloc_zonefile(0);
    store3.alloc_zonefile(1);
    store3.alloc_zonefile(1);
    EXPECT_EQ(store3._raw_zonefile_list.size(), 3);
    EXPECT_EQ(store3._rewrite_zonefile_list.size(), 2);

    AppendStore store4(opts);
    status = store4.open(_db, "./test/A_1_1");
    EXPECT_EQ(status.code(), AIE_OK);
    EXPECT_EQ(store4._raw_zonefile_list.size(), 3);
    EXPECT_EQ(store4._rewrite_zonefile_list.size(), 2);
}

TEST_P(AppendStoreTest, test_rebuild_zonefile_map_fail) {
    bool use_standard_record_layout = GetParam();
    prepare_store(use_standard_record_layout);
    prepare_data();
    _store->_max_rewrite_generation = 2;
    _store->alloc_zonefile(1);
    _store->_zonefile_map.clear();
    _store->_raw_zonefile_list.clear();
    _store->_rewrite_zonefile_list.clear();
    _store->_curr_write_file = nullptr;
    _store->_curr_rewrite_file = nullptr;

    BMOCK_NS_CLASS_RESUME(aries::datanode, Zonefile, get_physical_location,
            Status(uint64_t*));
    EXPECT_CALL(BMOCK_NS_CLASS_OBJECT(aries::datanode, Zonefile, get_physical_location),
            get_physical_location(_))
            .WillOnce(DoAll(SetArgPointee<0>(256 * 1024 * 1024), Return(Status(AIE_OK))))
            .WillOnce(Return(Status(AIE_CHECKSUM)))
            .WillOnce(Return(Status(AIE_CHECKSUM)));
    auto status = _store->rebuild_zonefile_map();
    EXPECT_EQ(status.code(), AIE_CHECKSUM);

    _store->_max_rewrite_generation = 0;
    _store->_zonefile_map.clear();
    _store->_raw_zonefile_list.clear();
    _store->_rewrite_zonefile_list.clear();
    _store->_curr_write_file = nullptr;
    _store->_curr_rewrite_file = nullptr;
    status = _store->rebuild_zonefile_map();
    EXPECT_EQ(status.code(), AIE_CHECKSUM);
    EXPECT_EQ(_store->_max_rewrite_generation, 2);

    BMOCK_NS_CLASS_STOP(aries::datanode, Zonefile, get_physical_location,
            Status(uint64_t*));

    _store->_max_rewrite_generation = 0;
    _store->_zonefile_map.clear();
    _store->_raw_zonefile_list.clear();
    _store->_rewrite_zonefile_list.clear();
    _store->_curr_write_file = nullptr;
    _store->_curr_rewrite_file = nullptr;
    status = _store->rebuild_zonefile_map();
    EXPECT_EQ(status.code(), AIE_OK);
    EXPECT_EQ(_store->_max_rewrite_generation, 2);

    _store->_max_rewrite_generation = 0;
    _store->_zonefile_map.clear();
    _store->_raw_zonefile_list.clear();
    _store->_rewrite_zonefile_list.clear();
    _store->_curr_write_file = nullptr;
    _store->_curr_rewrite_file = nullptr;
    ::system("rm -rf ./test/A_1_1/1");
    status = _store->rebuild_zonefile_map();
    EXPECT_NE(status.code(), AIE_OK);
}

TEST_F(AppendStoreTest, test_replay_data) {
    //test normal rebuild.
    g_fs->register_disk("./test/", "", 0, 0);
    StoreOptions opts;
    AppendStore store(opts);
    aries::pb::AppendVletInfo vlet_info;
    vlet_info.set_align_size(4096);
    auto status = store.create(vlet_info, _db, "./test/A_2_2/");
    EXPECT_EQ(status.code(), AIE_OK);

    for (int i = 0; i < 10; i++) {
        pb::ShardMeta meta;
        meta.set_blob_len(0);
        meta.set_blob_crc(0);
        meta.set_shard_len(5096);
        base::IOBuf data = BufferGen::gen(5096);
        meta.set_shard_crc(common::build_crc(data));
        status = store.put(1 + i, meta, data);
        EXPECT_EQ(status.code(), AIE_OK);
        auto used_size = store.used_size();
        EXPECT_EQ(used_size, 4096 * 2 * (2 + i));
        EXPECT_EQ(store._curr_write_file->write_location() * store._align_size, 4096 * 2 * (2 + i));
        LOG(INFO) << "used size:" << used_size;
    }

    auto write_location = store._curr_write_file->write_location();
    auto real_offset = write_location * store._align_size;

    BMOCK_NS_CLASS_RESUME(aries::datanode, Zonefile, get_physical_location,
                          Status(uint64_t*));
    EXPECT_CALL(BMOCK_NS_CLASS_OBJECT(aries::datanode, Zonefile, get_physical_location),
                get_physical_location(_))
            .WillOnce(DoAll(SetArgPointee<0>(real_offset), Return(Status(AIE_OK))))
            .WillOnce(DoAll(SetArgPointee<0>(real_offset - 4096 * 2), Return(Status(AIE_OK))))
            .WillOnce(DoAll(SetArgPointee<0>(real_offset), Return(Status(AIE_OK))))
            .WillOnce(DoAll(SetArgPointee<0>(real_offset - 4096), Return(Status(AIE_OK))));

    FLAGS_fast_recovery_mode = false;

    status = store.correct_write_off(store._curr_write_file);
    EXPECT_EQ(status.code(), AIE_OK);

    status = store.correct_write_off(store._curr_write_file);
    EXPECT_EQ(status.code(), AIE_CORRUPT);

    store._curr_write_file->set_write_location(write_location - 4);
    status = store.correct_write_off(store._curr_write_file);
    EXPECT_EQ(status.code(), AIE_OK);
    EXPECT_FALSE(store._curr_write_file->is_seal());
    EXPECT_EQ(store._curr_write_file->write_location(), write_location);
    EXPECT_EQ(store.used_size(), real_offset);

    store._curr_write_file->set_write_location(write_location - 4);
    status = store.correct_write_off(store._curr_write_file);
    EXPECT_EQ(status.code(), AIE_OK);
    EXPECT_TRUE(store._curr_write_file->is_seal());
    EXPECT_EQ(store._curr_write_file->write_location(), write_location - 2);
    EXPECT_EQ(store.used_size(), real_offset - 4096 * 2);

    BMOCK_NS_CLASS_STOP(aries::datanode, Zonefile, get_physical_location,
                        Status(uint64_t*));
}

TEST_F(AppendStoreTest, test_correct_write_off) {
    FLAGS_fast_recovery_mode = true;
    BMOCK_NS_CLASS_RESUME(aries::datanode, Zonefile, get_physical_location,
            Status(uint64_t*));
    EXPECT_CALL(BMOCK_NS_CLASS_OBJECT(aries::datanode, Zonefile, get_physical_location),
            get_physical_location(_))
            .WillOnce(DoAll(SetArgPointee<0>(4096), Return(Status(AIE_OK))))
            .WillOnce(DoAll(SetArgPointee<0>(4096 * 2), Return(Status(AIE_OK))))
            .WillOnce(DoAll(SetArgPointee<0>(4096 * 3), Return(Status(AIE_OK))))
            .WillOnce(Return(Status(AIE_NOTSUPPORT)));
    g_fs->register_disk("./test/", "", 0, 0);
    StoreOptions opts;
    AppendStore store(opts);
    aries::pb::AppendVletInfo vlet_info;
    vlet_info.set_align_size(4096);

    auto status = store.create(vlet_info, _db, "./test/A_1_1/");
    EXPECT_EQ(status.code(), AIE_OK);

    EXPECT_TRUE(store._curr_write_file != nullptr);
    auto zonefile_ptr = store._curr_write_file;

    status = store.correct_write_off(zonefile_ptr);
    EXPECT_EQ(status.code(), AIE_CORRUPT);

    status = store.correct_write_off(zonefile_ptr);
    EXPECT_EQ(status.code(), AIE_OK);
    EXPECT_EQ(zonefile_ptr->write_location(), 2);

    status = store.correct_write_off(zonefile_ptr);
    EXPECT_EQ(status.code(), AIE_OK);
    EXPECT_EQ(zonefile_ptr->write_location(), 2);
    EXPECT_EQ(zonefile_ptr->is_seal(), true);

    //not align
    //status = store.correct_write_off(zonefile_ptr);
    //EXPECT_EQ(status.code(), AIE_FAIL);

    //AIE_NOTSUPPORT
    status = store.correct_write_off(zonefile_ptr);
    EXPECT_EQ(status.code(), AIE_OK);
    EXPECT_EQ(zonefile_ptr->write_location(), 2);

    BMOCK_NS_CLASS_STOP(aries::datanode, Zonefile, get_physical_location,
            Status(uint64_t*));
}

TEST_F(AppendStoreTest, test_check_self) {
    StoreOptions opts;
    AppendStore store(opts);
    aries::pb::AppendVletInfo vlet_info;
    vlet_info.set_align_size(4096);
    auto status = store.create(vlet_info, _db, "./test/A_1_1/");
    EXPECT_EQ(status.code(), AIE_OK);

    auto& indexer = store._indexer;
    AppendRecordLocation loc;
    loc.file_index = 0;
    loc.offset = 2;
    loc.len = 2;
    status = indexer->put(1, loc);
    EXPECT_EQ(status.code(), AIE_OK);

    status = store.check_self();
    EXPECT_EQ(status.code(), AIE_OK);

    ZonefileInfo info1, info2;
    info1.file_sequence_id = 99999;
    info2.file_sequence_id = 99998;
    store._zonefile_map = {
        std::make_pair(1, std::make_shared<Zonefile>(info1)), 
        std::make_pair(2, std::make_shared<Zonefile>(info2))
    };
    ::system("touch ./test/A_1_1/99999");
    Status s = store.check_self();
    EXPECT_EQ(s.code(), AIE_CORRUPT);
    store._zonefile_map = {
        std::make_pair(1, std::make_shared<Zonefile>(info1))
    };
    s = store.check_self();
    EXPECT_EQ(s.code(), AIE_OK);
    store._zonefile_map.clear();
    ::system("rm ./test/A_1_1/99999");

    loc.file_index = 1;
    status = indexer->put(2, loc);
    EXPECT_EQ(status.code(), AIE_OK);
    status = store.check_self();
    EXPECT_EQ(status.code(), AIE_OK);

    loc.offset = 3;
    status = indexer->put(3, loc);
    EXPECT_EQ(status.code(), AIE_OK);
    status = store.check_self();
    EXPECT_EQ(status.code(), AIE_CORRUPT);
}

TEST_P(AppendStoreTest, test_put) {
    StoreOptions opts;
    AppendStore store(opts);
    aries::pb::AppendVletInfo vlet_info;
    vlet_info.set_align_size(4096);
    bool use_standard_record_layout = GetParam();
    vlet_info.set_use_standard_record_layout(use_standard_record_layout);
    auto status = store.create(vlet_info, _db, "./test/A_1_1/");
    EXPECT_EQ(status.code(), AIE_OK);
    AppendRecordLocation loc;
    store._indexer->put(1, loc);

    base::IOBuf data;
    std::string str_data("hello");
    pb::ShardMeta meta;
    data.append(str_data);
    meta.set_shard_len(data.size());
    meta.set_blob_len(4096);
    meta.set_blob_crc(0);
    meta.set_shard_crc(base::crc32c::Value(str_data.data(), str_data.size()));

    status = store.put(1, meta, data);
    EXPECT_EQ(status.code(), AIE_EXIST);
    EXPECT_EQ(store._curr_write_file->write_location(), 2);

    //write fail. because zonefile write fail.
//    EXPECT_CALL(BMOCK_NS_CLASS_OBJECT(aries::datanode, Zonefile, write), write(_, _, _))
//            .WillOnce(Return(Status(AIE_FAIL)));
    BMOCK_NS_CLASS_RESUME(rocksdb, DB, Put, rocksdb::Status(const rocksdb::WriteOptions&,
                const rocksdb::Slice&, const rocksdb::Slice&));
    EXPECT_CALL(BMOCK_NS_CLASS_OBJECT(rocksdb, DB, Put),
            Put(_, _, _))
            .WillOnce(Return(rocksdb::Status::TimedOut()));

//   status = store.put(2, meta, data);
//   EXPECT_EQ(status.code(), AIE_FAIL);
//   BMOCK_NS_CLASS_STOP(aries::datanode, AppendStore, write,
//        Status(const char*, const uint32_t, AppendRecordLocation*));

    //write faile. because db write fail.
    status = store.put(2, meta, data);
    EXPECT_EQ(status.code(), AIE_FAIL);

    BMOCK_NS_CLASS_STOP(rocksdb, DB, Put, rocksdb::Status(const rocksdb::WriteOptions&, 
                const rocksdb::Slice&, const rocksdb::Slice&));

    status = store.put(2, meta, data);
    EXPECT_EQ(status.code(), AIE_OK);
    EXPECT_EQ(store._curr_write_file->write_location(), 4);
}

TEST_P(AppendStoreTest, test_get_record_index_info) {
    bool use_standard_record_layout = GetParam();
    g_fs->register_disk("./test/", "", 0, 0);
    StoreOptions opts;
    AppendStore store(opts);
    aries::pb::AppendVletInfo vlet_info;
    vlet_info.set_volume_id(1);
    vlet_info.set_vlet_type(81);
    vlet_info.set_align_size(4096);
    vlet_info.mutable_space_info()->set_k(18);
    vlet_info.set_shard_index(12);
    vlet_info.set_use_standard_record_layout(use_standard_record_layout);
    auto status = store.create(vlet_info, _db, "./test/A_1_1");
    ASSERT_EQ(status.code(), AIE_OK);
    AppendRecordLocation loc;

    base::IOBuf data;
    std::string str_data("hello");
    pb::ShardMeta meta;
    data.append(str_data);
    meta.set_shard_len(data.size());
    meta.set_blob_len(4096);
    meta.set_blob_crc(0);
    meta.set_blob_ttl_timestamp(base::gettimeofday_s() + 86400);
    meta.set_shard_crc(base::crc32c::Value(str_data.data(), str_data.size()));

    status = store.put(1, meta, data);
    ASSERT_EQ(status.code(), AIE_OK);

    // get record index info succ
    pb::RecordIndexInfo record_index_info;
    status = store.get_record_index_info(1, &record_index_info);
    ASSERT_EQ(status.code(), AIE_OK);
    LOG(TRACE) << "record_index_info:" << common::pb2json(record_index_info);

    // locate_zonefile do not return AIE_OK
    // 1. AIE_MARK_REMOVED
    status = store.remove(1, true);
    ASSERT_EQ(status.code(), AIE_OK);
    status = store.get_record_index_info(1, &record_index_info);
    ASSERT_EQ(status.code(), AIE_MARK_REMOVED);
    LOG(TRACE) << "record_index_info:" << common::pb2json(record_index_info);
    status = store.remove(1, false);
    ASSERT_EQ(status.code(), AIE_OK);
    // 1.1 put again
    status = store.put(1, meta, data);
    ASSERT_EQ(status.code(), AIE_OK);
    // 2. AIE_FAIL
    BMOCK_NS_CLASS_RESUME(aries::datanode, AppendStore, locate_zonefile,
        Status(uint64_t, AppendRecordLocation*, ZonefilePtr*, uint32_t*));
    EXPECT_CALL(BMOCK_NS_CLASS_OBJECT(aries::datanode, AppendStore, locate_zonefile), 
            locate_zonefile(_, _, _, _)).WillOnce(Return(Status(AIE_FAIL)));
    status = store.get_record_index_info(1, &record_index_info);
    ASSERT_EQ(status.code(), AIE_FAIL);
    BMOCK_NS_CLASS_STOP(aries::datanode, AppendStore, locate_zonefile,
        Status(uint64_t, AppendRecordLocation*, ZonefilePtr*, uint32_t*));

    // shard has been expired
    BMOCK_NS_CLASS_RESUME(aries::datanode, AppendRecordLocation, is_expired,
        bool());
    EXPECT_CALL(BMOCK_NS_CLASS_OBJECT(aries::datanode, AppendRecordLocation, is_expired), 
            is_expired()).WillOnce(Return(true));
    status = store.get_record_index_info(1, &record_index_info);
    ASSERT_EQ(status.code(), AIE_BLOB_NOT_EXIST);
    BMOCK_NS_CLASS_STOP(aries::datanode, AppendRecordLocation, is_expired,
        bool());

    _db.reset();
    _db = nullptr;
    ::system(" rm -rf ./test ");
}

TEST_P(AppendStoreTest, test_rewrite) {
    bool use_standard_record_layout = GetParam();
    prepare_store(use_standard_record_layout);
    {
        //snapshot id not equal 0
        RewriteOptions opt;
        RewriteInfo info;
        opt.last_rewrite_time = ::base::gettimeofday_us();
        opt.append_zone_rewrite_rate = 5;
        _store->_snapshot.snapshot_id = 0;
        auto status = _store->get_rewrite_info(opt, &info);
        EXPECT_EQ(status.code(), AIE_OK);
        _store->_snapshot.snapshot_id = 0;
    }
    {
        //snapshot id not equal 0
        RewriteOptions opt;
        RewriteInfo info;
        opt.last_rewrite_time = ::base::gettimeofday_us();
        opt.append_zone_rewrite_rate = 5;
        _store->_snapshot.snapshot_id = 444;
        auto status = _store->get_rewrite_info(opt, &info);
        EXPECT_EQ(status.code(), AIE_OK);
        _store->_snapshot.snapshot_id = 0;
    }

    {
        //exceed rewrite info duration
        uint32_t rewrite_start_time = _store->_vlet_info.daily_rewrite_start_time();
        _store->_vlet_info.set_daily_rewrite_start_time(86400);
        uint32_t duration_second = _store->_vlet_info.daily_rewrite_duration_second();
        _store->_vlet_info.set_daily_rewrite_duration_second(0);
        RewriteOptions opt;
        RewriteInfo info;
        opt.last_rewrite_time = ::base::gettimeofday_us();
        _store->_last_update_snapshot_time = ::base::gettimeofday_us();
        _store->_snapshot.snapshot_id = 444;
        auto status = _store->get_rewrite_info(opt, &info);
        EXPECT_EQ(status.code(), AIE_EXCEED_LIMIT);
        _store->_snapshot.snapshot_id = 0;

        opt.is_force_rewrite = true;
        status = _store->get_rewrite_info(opt, &info);
        EXPECT_TRUE(status.ok());
        _store->_vlet_info.set_daily_rewrite_start_time(rewrite_start_time);
        _store->_vlet_info.set_daily_rewrite_duration_second(duration_second);	
    }
}

TEST_P(AppendStoreTest, get_zonefiles_detail) {
    bool use_standard_record_layout = GetParam();
    prepare_store(use_standard_record_layout);
    prepare_data();
    pb::ZonefilesDetail info;
    auto status = _store->get_zonefiles_detail(&info);
    EXPECT_EQ(status.code(), AIE_OK);
}

TEST_F(AppendStoreTest, test_alloc_zonefile) {
    FLAGS_fast_recovery_mode = true;
    g_fs->register_disk("./test/", "", 0, 0);
    StoreOptions opts;
    AppendStore store(opts);
    aries::pb::AppendVletInfo vlet_info;
    vlet_info.set_align_size(4096);
    auto status = store.create(vlet_info, _db, "./test/A_1_1/");
    EXPECT_EQ(status.code(), AIE_OK);
    EXPECT_EQ(store._max_file_sequence_id, 1);

    status = store.alloc_zonefile(false);
    EXPECT_EQ(status.code(), AIE_OK);
    EXPECT_EQ(store._max_file_sequence_id, 3);

    status = store.alloc_zonefile(true);
    EXPECT_EQ(status.code(), AIE_OK);
    EXPECT_EQ(store._max_file_sequence_id, 4);
    
    auto zonefile = store._zonefile_map[0];
    EXPECT_EQ(zonefile->_begin_position, 2);
    zonefile = store._zonefile_map[1];
    EXPECT_EQ(zonefile->_begin_position, 2);
    EXPECT_EQ(store._zonefile_map.size(), 3);
    EXPECT_EQ(store._rewrite_zonefile_list.size(), 1);
    EXPECT_EQ(store._raw_zonefile_list.size(), 2);

    //restart
    AppendStore store2(opts);
    status = store2.open(_db, "./test/A_1_1");
    EXPECT_EQ(status.code(), AIE_OK);
    zonefile = store._zonefile_map[0];
    EXPECT_EQ(zonefile->_begin_position, 2);
    zonefile = store._zonefile_map[2];
    EXPECT_EQ(zonefile->_begin_position, 2);

    {
        StoreOptions opts;
        AppendStore store(opts);
        aries::pb::AppendVletInfo vlet_info;
        vlet_info.set_align_size(4096);
        auto status = store.create(vlet_info, _db, "./test/A_1_2/");
        EXPECT_EQ(status.code(), AIE_OK);
        EXPECT_EQ(store._zonefile_map.size(), 1);

        //put db failed.
        BMOCK_NS_CLASS_RESUME(rocksdb, DB, Put,
            rocksdb::Status(const rocksdb::WriteOptions&, const rocksdb::Slice&,
                const rocksdb::Slice&));
        EXPECT_CALL(BMOCK_NS_CLASS_OBJECT(rocksdb, DB, Put),
                Put(_, _, _))
                .WillOnce(Return(rocksdb::Status::TimedOut()));

        store.alloc_zonefile(false);
        EXPECT_EQ(store._zonefile_map.size(), 1);

        BMOCK_NS_CLASS_STOP(rocksdb, DB, Put, rocksdb::Status(const rocksdb::WriteOptions&, 
                    const rocksdb::Slice&, const rocksdb::Slice&));
    }
}

TEST_F(AppendStoreTest, test_snapshot) {
    //test normal rebuild.
    g_fs->register_disk("./test/", "", 0, 0);
    StoreOptions opts;
    AppendStore store(opts);
    aries::pb::AppendVletInfo vlet_info;
    vlet_info.set_align_size(4096);
    auto status = store.create(vlet_info, _db, "./test/A_1_1/");
    EXPECT_EQ(status.code(), AIE_OK);

    status = store.alloc_zonefile(false);
    EXPECT_EQ(status.code(), AIE_OK);
    status = store.alloc_zonefile(false);
    EXPECT_EQ(status.code(), AIE_OK);
    //status = alloc_zonefile(false);
    //EXPECT_EQ(status.code(), AIE_OK);

    status = store.alloc_zonefile(true);
    EXPECT_EQ(status.code(), AIE_OK);
    status = store.alloc_zonefile(true);
    EXPECT_EQ(status.code(), AIE_OK);
    status = store.alloc_zonefile(true);
    EXPECT_EQ(status.code(), AIE_OK);

    store._zonefile_map[0]->_info.create_time = 100;
    store._zonefile_map[0]->_write_location = 100;
    store._zonefile_map[0]->_used_size = 0.75 * store._zonefile_size;

    store._zonefile_map[1]->_info.create_time = 200;
    store._zonefile_map[1]->_write_location = 100;
    store._zonefile_map[1]->_used_size = 0.5 * store._zonefile_size;

    store._zonefile_map[2]->_info.create_time = 300;
    store._zonefile_map[2]->_write_location = 100;
    store._zonefile_map[2]->_used_size = 75 * 4096;

    store._zonefile_map[3]->_info.create_time = 100;
    store._zonefile_map[3]->_write_location = 100;
    store._zonefile_map[3]->_used_size = 0.75 * store._zonefile_size;

    store._zonefile_map[4]->_info.create_time = 200;
    store._zonefile_map[4]->_write_location = 100;
    store._zonefile_map[4]->_used_size = 0.5 * store._zonefile_size;

    store._zonefile_map[5]->_info.create_time = 300;
    store._zonefile_map[5]->_write_location = 100;
    store._zonefile_map[5]->_used_size = 75 * 4096;

    store.init_snapshot(101, 0);
    EXPECT_EQ(store._snapshot.z_map.size(), 2);

    store.release_snapshot();
    store.init_snapshot(101, 30);
    EXPECT_EQ(store._snapshot.z_map.size(), 0);

    store.release_snapshot();
    store.init_snapshot(201, 30);
    EXPECT_EQ(store._snapshot.z_map.size(), 2);

    store.release_snapshot();
    store.init_snapshot(201, 0);
    EXPECT_EQ(store._snapshot.z_map.size(), 4);

    store.release_snapshot();
    store.init_snapshot(301, 0);
    EXPECT_EQ(store._snapshot.z_map.size(), 4);

    store.release_snapshot();
    store.init_snapshot(301, 100);
    EXPECT_EQ(store._snapshot.z_map.size(), 0);

    store.release_snapshot();
    store.init_snapshot(0, -1);
    EXPECT_EQ(store._snapshot.z_map.size(), 6);
}

TEST_F(AppendStoreTest, check_db) {
    _store->_indexer = std::unique_ptr<AppendStoreIndexer>(new AppendStoreIndexer(100, 123, _db));
    Status s = _store->check_db();
    EXPECT_EQ(s.code(), AIE_OK);

    VolumeKey key;
    key.set_volume_id(100);
    key.set_vlet_type(123);
    std::string data = "XXXX";
    rocksdb::Status ss = _db->Put(rocksdb::WriteOptions(), rocksdb::Slice((char*)&key, sizeof(key)), data);
    EXPECT_EQ(ss.ok(), true);
    s = _store->check_db();
    EXPECT_EQ(s.code(), AIE_FAIL);
    ss = _db->Delete(rocksdb::WriteOptions(), rocksdb::Slice((char*)&key, sizeof(key)));
    EXPECT_EQ(ss.ok(), true);

    RecordKey key2(100, 2012);
    ss = _db->Put(rocksdb::WriteOptions(), rocksdb::Slice((char*)&key2, sizeof(key2)), data);
    EXPECT_EQ(ss.ok(), true);
    s = _store->check_db();
    EXPECT_EQ(s.code(), AIE_FAIL);
    ss = _db->Delete(rocksdb::WriteOptions(), rocksdb::Slice((char*)&key2, sizeof(key2)));
    EXPECT_EQ(ss.ok(), true);

    data.clear();
    ss = _db->Put(rocksdb::WriteOptions(), rocksdb::Slice((char*)&key2, sizeof(key2)), data);
    EXPECT_EQ(ss.ok(), true);
    s = _store->check_db();
    EXPECT_EQ(s.code(), AIE_OK);

    ss = _db->Delete(rocksdb::WriteOptions(), rocksdb::Slice((char*)&key2, sizeof(key2)));
}

TEST_F(AppendStoreTest, recover_mode) {
    BMOCK_CLASS_MOCK_GUARD(AppendShardScanner, restore);
    BMOCK_CLASS_MOCK_GUARD(AppendShardScanner, vlet_info);
    EXPECT_CALL(BMOCK_NS_CLASS_OBJECT(aries::datanode, AppendShardScanner, restore),
                                restore())
        .WillRepeatedly(Return(Status(AIE_OK)));
    EXPECT_CALL(BMOCK_NS_CLASS_OBJECT(aries::datanode, AppendStore, rebuild_zonefile_map),
                                rebuild_zonefile_map())
        .WillRepeatedly(Return(Status(AIE_OK)));
    aries::pb::AppendVletInfo vlet_info;
    vlet_info.set_volume_id(1222);
    vlet_info.set_vlet_type(1);
    vlet_info.set_align_size(4096);
    EXPECT_CALL(BMOCK_NS_CLASS_OBJECT(aries::datanode, AppendShardScanner, vlet_info),
                                vlet_info())
        .WillRepeatedly(Return(vlet_info));
    g_fs->register_disk("./test/", "", 0, 0);
    StoreOptions opts;
    AppendStore store(opts);
    auto status = store.create(vlet_info, _db, "./test/A_1_1/");
    EXPECT_EQ(status.code(), AIE_OK);
    VolumeKey key;
    key.set_volume_id(1222);
    key.set_vlet_type(1);
    auto ss = _db->Delete(rocksdb::WriteOptions(), rocksdb::Slice((char*)&key, sizeof(key)));
    EXPECT_EQ(ss.ok(), true);

    opts.recover_on_open = true;
    AppendStore store2(opts);
    auto s = store2.open(_db, "./test/A_1_1");
    EXPECT_EQ(s.code(), AIE_OK);

    BMOCK_NS_CLASS_STOP(aries::datanode, AppendStore, rebuild_zonefile_map,
                        Status());
}

TEST_P(AppendStoreTest, batch_get_segment) {
    // prepare data
    // clear env
    FLAGS_create_vlet_file_only_by_write = false;
    _store.reset();
    _db.reset();
    ::system("rm -rf test_append_store_batch_get_opt");
    ::system("rm -rf test_batch_get_vlet_file");
    g_fs->register_disk("./test_append_store_batch_get_opt/", "", 0, 0);

    // init 32G vlet env
    StoreOptions opts;
    _store.reset(new AppendStore(opts));
    rocksdb::DB* tmp;
    rocksdb::Options options;
    options.create_if_missing = true;
    rocksdb::Status ss = rocksdb::DB::Open(options, "./test_append_store_batch_get_opt/db", &tmp);
    assert(ss.ok());
    _db.reset(tmp);
    tmp = nullptr;
    aries::pb::AppendVletInfo vlet_info;
    vlet_info.set_volume_id(2012);
    vlet_info.set_shard_index(12);
    vlet_info.set_vlet_type(VLET_TYPE_APPEND_16G_256M_4K);
    vlet_info.set_align_size(4096);
    vlet_info.set_zonefile_num(64);
    vlet_info.set_zonefile_size(1024*1024*256);
    bool use_standard_record_layout = GetParam();
    vlet_info.set_use_standard_record_layout(use_standard_record_layout);
    _store->create(vlet_info, _db, "./test_append_store_batch_get_opt/test_batch_get_vlet_file");

    Status s;
    // 1.put 
    LOG(NOTICE) << "prepare data";
    uint64_t vbid = 0;
    std::random_device rd;
    std::map<uint64_t, int32_t > shard_status;   // 0: not exist 1: exist 2: mark_deleted
    int i = 0;
    while (i < 4100) {
        uint32_t shard_len =  (63 * 1024);   // max_record = 512k
        std::unique_ptr<char []> shard_buffer(new char[shard_len]);
        aries::common::make_mock_data(shard_buffer.get(), shard_len);
        base::IOBuf data;
        data.append(shard_buffer.get(), shard_len);
        uint32_t shard_crc = base::crc32c::Value(shard_buffer.get(), shard_len);
        aries::pb::ShardMeta meta;

        meta.set_blob_len(shard_len);
        meta.set_blob_crc(shard_crc);
        meta.set_shard_len(shard_len);
        meta.set_shard_crc(shard_crc);
        auto shard_compress_option = _store->_vlet_info.mutable_shard_compress_option();
        shard_compress_option->set_compress_type(COMPRESS_TYPE_NONE);
        s = _store->put(++vbid, meta, data);
        if (s.ok()) {
            shard_status[vbid] = 1;
        }
        ++i;
    }
    while (i < 4200) {
        uint32_t shard_len =  (100 * 1024);   // max_record = 512k
        std::unique_ptr<char []> shard_buffer(new char[shard_len]);
        aries::common::make_mock_data(shard_buffer.get(), shard_len);
        base::IOBuf data;
        data.append(shard_buffer.get(), shard_len);
        uint32_t shard_crc = base::crc32c::Value(shard_buffer.get(), shard_len);
        aries::pb::ShardMeta meta;

        meta.set_blob_len(shard_len);
        meta.set_blob_crc(shard_crc);
        meta.set_shard_len(shard_len);
        meta.set_shard_crc(shard_crc);
        auto shard_compress_option = _store->_vlet_info.mutable_shard_compress_option();
        shard_compress_option->set_compress_type(COMPRESS_TYPE_NONE);
        s = _store->put(++vbid, meta, data);
        if (s.ok()) {
            shard_status[vbid] = 1;
        }
        ++i;
    }
    s = _store->check_self();
    EXPECT_EQ(s.ok(), true);

    // 2. batch_read
    LOG(NOTICE) << "batch read";

    aries::pb::ShardBatchGetRequest request;
    aries::pb::ShardBatchGetResponse response;

    GetSegmentContext ctx;
    baidu::rpc::Controller cntl;
    auto record = std::make_shared<ContinuousSegment>();
    auto ext = std::make_shared<AppendBatchRecord>();
    ext->file_index = 0;
    ext->offset = 2;
    ext->len = 6 * 16;
    record->ext = ext.get();
    ctx.segment = record;
    ctx.cntl = &cntl;
    ctx.response = &response;
    ctx.request = &request;
    ctx.sync_point = std::make_shared<common::SyncPoint>(1);
    // 2.1    1 - 6

    for (int i = 1; i <= 6; ++i) {
        ext->vbids[i] = true;
    }
    
    s = _store->batch_get_segment(&ctx);
    EXPECT_EQ(s.ok(), true);
    EXPECT_EQ(ext->vbids.size(), 0);
    EXPECT_EQ(record->real_io_count, 1);
    response.Clear();

    // 2.2 cross zonefile 4094 - 4100 
    GetSegmentContext ctx1;
    baidu::rpc::Controller cntl1;
    auto record1 = std::make_shared<ContinuousSegment>();
    auto ext1 = std::make_shared<AppendBatchRecord>();
    ext1->file_index = 0;
    ext1->offset = 65490;
    ext1->len = 6 * 16;
    record1->ext = ext1.get();
    ctx1.segment = record1;
    ctx1.cntl = &cntl1;
    ctx1.response = &response;
    ctx1.request = &request;
    ctx1.sync_point = std::make_shared<common::SyncPoint>(1);
    
    for (int i = 4094; i < 4100; ++i) {
        ext1->vbids[i] = true;
    }
    
    s = _store->batch_get_segment(&ctx1);
    EXPECT_EQ(s.ok(), true);
    EXPECT_EQ(ext1->vbids.size(), 0);
    EXPECT_EQ(record1->real_io_count, 5);
    response.Clear();

    // 2.3 more than record num 4200 - 4206 
    GetSegmentContext ctx2;
    baidu::rpc::Controller cntl2;
    auto record2 = std::make_shared<ContinuousSegment>();
    auto ext2 = std::make_shared<AppendBatchRecord>();
    ext2->file_index = 1;
    ext2->offset = 2602;
    ext2->len = 7 * 16;
    record2->ext = ext2.get();
    ctx2.segment = record2;
    ctx2.cntl = &cntl2;
    ctx2.response = &response;
    ctx2.request = &request;
    ctx2.sync_point = std::make_shared<common::SyncPoint>(1);
    
    for (int i = 4200; i <= 4206; ++i) {
        ext2->vbids[i] = true;
    }
    
    s = _store->batch_get_segment(&ctx2);
    EXPECT_EQ(s.ok(), true);
    EXPECT_EQ(ext2->vbids.size(), 0);
    EXPECT_EQ(record2->real_io_count, 7);
    response.Clear();

    // 2.4 not continuous 1, 3, 6, 8
    GetSegmentContext ctx3;
    baidu::rpc::Controller cntl3;
    auto record3 = std::make_shared<ContinuousSegment>();
    auto ext3 = std::make_shared<AppendBatchRecord>();
    ext3->file_index = 0;
    ext3->offset = 2;
    ext3->len = 8 * 16;
    record3->ext = ext3.get();
    ctx3.segment = record3;
    ctx3.cntl = &cntl3;
    ctx3.response = &response;
    ctx3.request = &request;
    ctx3.sync_point = std::make_shared<common::SyncPoint>(1);

    ext3->vbids[1] = true;
    ext3->vbids[3] = true;
    ext3->vbids[6] = true;
    ext3->vbids[8] = true;
    
    s = _store->batch_get_segment(&ctx3);
    EXPECT_EQ(s.ok(), true);
    EXPECT_EQ(ext3->vbids.size(), 0);
    EXPECT_EQ(record3->real_io_count, 1);
    EXPECT_EQ(ctx3.response->shard_info_size(), 4);
    response.Clear();

    // 3.random remove
    LOG(NOTICE) << "random remove";
    for (auto shard : shard_status) {
        auto vbid = shard.first;
        switch (shard.first % 3) {
            case 0:
                s = _store->remove(vbid, true);
                EXPECT_EQ(s.ok(), true);
                s = _store->remove(vbid, false);
                EXPECT_EQ(s.ok(), true);
                shard_status[vbid] = 0;
                break;
            case 1:
                s = _store->remove(vbid, true);
                EXPECT_EQ(s.ok(), true);
                shard_status[vbid] = 2;
                break;
            case 2:
                break;
        }
    }
    
    LOG(NOTICE) << "batch read again";
    // 4. batch_read
    GetSegmentContext ctx4;
    baidu::rpc::Controller cntl4;
    auto record4 = std::make_shared<ContinuousSegment>();
    auto ext4 = std::make_shared<AppendBatchRecord>();
    ext4->file_index = 0;
    ext4->offset = 2;
    ext4->len = 6 * 16;
    record4->ext = ext4.get();
    ctx4.segment = record4;
    ctx4.cntl = &cntl4;
    ctx4.response = &response;
    ctx4.request = &request;
    ctx4.sync_point = std::make_shared<common::SyncPoint>(1);

    // 4.1    1 - 32

    for (int i = 1; i <= 6; ++i) {
        ext4->vbids[i] = true;
    }
    
    s = _store->batch_get_segment(&ctx4);
    EXPECT_EQ(s.ok(), true);
    EXPECT_EQ(ext4->vbids.size(), 0);
    EXPECT_EQ(record4->real_io_count, 1);
    EXPECT_EQ(ctx4.response->shard_info_size(), 6);
    response.Clear();

    // 4.4 not continuous 1, 3, 6, 8
    GetSegmentContext ctx7;
    baidu::rpc::Controller cntl7;
    auto record7 = std::make_shared<ContinuousSegment>();
    auto ext7 = std::make_shared<AppendBatchRecord>();
    ext7->file_index = 0;
    ext7->offset = 2;
    ext7->len = 8 * 16;
    record7->ext = ext7.get();
    ctx7.segment = record7;
    ctx7.cntl = &cntl7;
    ctx7.response = &response;
    ctx7.request = &request;
    ctx7.sync_point = std::make_shared<common::SyncPoint>(1);

    ext7->vbids[1] = true;
    ext7->vbids[3] = true;
    ext7->vbids[6] = true;
    ext7->vbids[8] = true;
    
    s = _store->batch_get_segment(&ctx7);
    EXPECT_EQ(s.ok(), true);
    EXPECT_EQ(ext7->vbids.size(), 0);
    response.Clear();

    BMOCK_NS_RESUME(base::crc32c, Value, uint32_t(const char* data, size_t n));
    EXPECT_CALL(BMOCK_NS_OBJECT(base::crc32c, Value), Value(_,_))
        .WillRepeatedly(Return(0));
    // 4.5 has crc error 1, 3, 6, 8 has crc error
    GetSegmentContext ctx8;
    baidu::rpc::Controller cntl8;
    auto record8 = std::make_shared<ContinuousSegment>();
    auto ext8 = std::make_shared<AppendBatchRecord>();
    ext8->file_index = 0;
    ext8->offset = 2;
    ext8->len = 8 * 16;
    record8->ext = ext8.get();
    ctx8.segment = record8;
    ctx8.cntl = &cntl8;
    ctx8.response = &response;
    ctx8.request = &request;
    ctx8.sync_point = std::make_shared<common::SyncPoint>(1);

    ext8->vbids[1] = true;
    ext8->vbids[3] = true;
    ext8->vbids[6] = true;
    ext8->vbids[8] = true;
    
    EXPECT_EQ(s.ok(), true);
    s = _store->batch_get_segment(&ctx8);
    EXPECT_EQ(s.ok(), true);
    EXPECT_EQ(ext8->vbids.size(), 0);
    EXPECT_EQ(ctx8.response->shard_info_size(), 4);
    response.Clear();
    BMOCK_NS_STOP(base::crc32c, Value, uint32_t(const char* data, size_t n));

    // 4.6 io error  1 - 6
    BMOCK_MOCK_GUARD(aries_ut_pread);

    GetSegmentContext ctx9;
    baidu::rpc::Controller cntl9;
    auto record9 = std::make_shared<ContinuousSegment>();
    auto ext9 = std::make_shared<AppendBatchRecord>();
    ext9->file_index = 0;
    ext9->offset = 2;
    ext9->len = 6 * 16;
    record9->ext = ext9.get();
    ctx9.segment = record9;
    ctx9.cntl = &cntl9;
    ctx9.response = &response;
    ctx9.request = &request;
    ctx9.sync_point = std::make_shared<common::SyncPoint>(1); 
    for (int i = 1; i <= 6; ++i) {
        ext9->vbids[i] = true;
    }
    
    EXPECT_CALL(BMOCK_OBJECT(aries_ut_pread), aries_ut_pread(_,_,_,_))
        .WillRepeatedly(SetErrnoAndReturn(EBADF, -1));

    s = _store->batch_get_segment(&ctx9);
    EXPECT_EQ(s.ok(), true);
    EXPECT_EQ(ext9->vbids.size(), 0);
    EXPECT_EQ(record9->real_io_count, 7);
    EXPECT_EQ(ctx9.response->shard_info_size(), 6);
    response.Clear();
    BMOCK_STOP(aries_ut_pread, ssize_t(int, void *, size_t, off_t));

    FLAGS_create_vlet_file_only_by_write = true;
}

TEST_F(AppendStoreTest, test_init_rewrite_env) {
    g_fs->register_disk("./test/", "", 0, 0);
    StoreOptions opts;
    AppendStore store(opts);
    aries::pb::AppendVletInfo vlet_info;
    vlet_info.set_align_size(4096);
    auto status = store.create(vlet_info, _db, "./test/A_1_1/");
    EXPECT_EQ(status.code(), AIE_OK);
    EXPECT_EQ(store._max_file_sequence_id, 1);

    status = store.alloc_zonefile(false);
    EXPECT_EQ(status.code(), AIE_OK);
    EXPECT_EQ(store._max_file_sequence_id, 3);

    status = store.alloc_zonefile(true);
    EXPECT_EQ(status.code(), AIE_OK);
    EXPECT_EQ(store._max_file_sequence_id, 4);

    EXPECT_EQ(store._zonefile_map.size(), 3);
    auto zonefile = store._zonefile_map[0];
    EXPECT_EQ(zonefile->_begin_position, 2);
    zonefile->seal();
    zonefile = store._zonefile_map[1];
    EXPECT_EQ(zonefile->_begin_position, 2);
    EXPECT_EQ(store._rewrite_zonefile_list.size(), 1);
    EXPECT_EQ(store._raw_zonefile_list.size(), 2);

    //prepare rewrite
    EXPECT_EQ(0, store._max_rewrite_generation);

    RewriteVletContext* rewrite_vlet_context = new RewriteVletContext;
    GetRewriteInfoContextPtr info = std::make_shared<GetRewriteInfoContext>();
    info->rewrite_ignore_hole_rate = true;
    rewrite_vlet_context->rewrite_info_context_ptr = info;

    status = store.init_rewrite_context(rewrite_vlet_context);
    EXPECT_TRUE(status.ok());
    EXPECT_EQ(1, store._max_rewrite_generation);
    std::shared_ptr<AppendStoreRewriteDetailInfo> rewrite_detail_info =
        std::dynamic_pointer_cast<AppendStoreRewriteDetailInfo>(rewrite_vlet_context->rewrite_detail_info_ptr);
    EXPECT_TRUE(rewrite_detail_info != nullptr);
    EXPECT_EQ(1, rewrite_detail_info->get_zonefile_lists().size());
    const auto& file = rewrite_detail_info->get_zonefile_lists()[0];
    ASSERT_EQ(1, file.file_sequence_id);
    ASSERT_EQ(0, file.file_index);
    _db.reset();
}

TEST_P(AppendStoreTest, test_normal_put_and_rewrite) {

    //cleanup vlet data by above cases;
    _db.reset();
    _db = nullptr;
    ::system(" rm -rf ./test ");
    sleep(10);
    ::system(" mkdir -p ./test/db ");
    open_db();
    assert(_db != nullptr);

    //test normal rebuild.
    g_fs->register_disk("./test/", "", 0, 0);

    aries::pb::AppendVletInfo vlet_info;
    vlet_info.set_volume_id(1);
    vlet_info.set_shard_index(12);
    vlet_info.set_vlet_type(80);
    vlet_info.set_align_size(4096);
    bool use_standard_record_layout = GetParam();
    vlet_info.set_use_standard_record_layout(use_standard_record_layout);
    //vlet_info.set_zonefile_size(256 * 1024 * 1024);
    vlet_info.set_zonefile_num(1000);
    auto compress_option = vlet_info.mutable_shard_compress_option();
    compress_option->set_compress_type(COMPRESS_TYPE_NONE);
    std::shared_ptr<AppendVlet> append_vlet = std::make_shared<AppendVlet>(nullptr, vlet_info);
    AppendStore& store = *append_vlet->_store.get();

    auto status = store.create(vlet_info, _db, "./test/A_1_1/");
    EXPECT_EQ(status.code(), AIE_OK);

    pb::ShardMeta meta;
    meta.set_blob_len(1);
    meta.set_blob_crc(2);
    meta.set_shard_len(6);

    ::base::IOBuf io_buf1, io_buf2, io_buf3, io_buf4, io_buf5;
    io_buf1.append("hello.");
    io_buf2.append("world.");

    meta.set_shard_crc(common::build_crc(io_buf1));
    status = store.put(1, meta, io_buf1);
    ASSERT_EQ(status.code(), AIE_OK);
    meta.set_shard_crc(common::build_crc(io_buf2));
    status = store.put(2, meta, io_buf2);
    ASSERT_EQ(status.code(), AIE_OK);
    meta.set_shard_crc(common::build_crc(io_buf3));
    store.get(2, &meta, &io_buf3);
    std::string str3 = io_buf3.to_string();
    EXPECT_TRUE(str3 == "world.");
    LOG(NOTICE) << io_buf3;
    store.range_get(2, 1, 3, &meta, &io_buf5);
    std::string str5 = io_buf5.to_string();
    EXPECT_TRUE(str5 == "orl");
    LOG(NOTICE) << io_buf5;

    auto t1 = ::base::gettimeofday_ms();
    for (int i = 10; i < 256 * 8 * 4 + 10; ++i) {
        ::base::IOBuf io_buf;
        std::string str(128*1024, 'c');
        io_buf.append(str);
        meta.set_shard_len(str.size());
        uint32_t crc = common::build_crc(io_buf);
        meta.set_shard_crc(crc);
        auto status = store.put(i, meta, io_buf);
        LOG(TRACE) << " status:" << status;
    }

    for (int i = 10; i < 100 + 10; ++i) {
        store.remove(i, true);
        store.remove(i, false);
    }

    LOG(TRACE) << "total_size:" << store._total_size << " free_size:" << store.free_size();
    EXPECT_GE(store.free_size(), 123ULL * 256 * 1024 * 1024);
    EXPECT_GE(store.used_size(), 256ULL * 4 * 2 * 4 * 128 * 1024);
    EXPECT_GE(store.hole_size(), 100 * 128 * 1024);

    for (int i = 10; i < 256 * 8 * 4 + 10; ++i) {
        if (i % 2 == 0) {
            continue;
        }
        store.remove(i, true);
        store.remove(i, false);
    }

    for (int i = 10; i < 256 * 8 * 4 + 10; ++i) {
        if (i % 2 == 0) {
            continue;
        }
        ::base::IOBuf io_buf;
        std::string str(128*1024, 'c');
        io_buf.append(str);
        meta.set_shard_len(str.size());
        auto status = store.put(i, meta, io_buf);
        LOG(TRACE) << " status:" << status;
    }

    //alloc a new raw zonefile to make sure vlet rewrite all record;
    status = store.alloc_zonefile(false);
    ASSERT_TRUE(status.ok());
    auto rewrite_vlet_context = std::unique_ptr<RewriteVletContext>(new RewriteVletContext);
    rewrite_vlet_context->vlet_ptr = append_vlet;
    rewrite_vlet_context->is_finish = false;
    rewrite_vlet_context->rewrite_info_context_ptr = std::make_shared<GetRewriteInfoContext>();
    rewrite_vlet_context->rewrite_info_context_ptr->rewrite_ignore_hole_rate = true;
    rewrite_vlet_context->init_rewrite_context = false;

    //init rewrite env
    status = store.init_rewrite_context(rewrite_vlet_context.get());
    ASSERT_TRUE(status.ok());

    do {
        status = store.do_rewrite(rewrite_vlet_context.get());
        ASSERT_TRUE(status.ok());
        if (rewrite_vlet_context->is_finish) {
            break;
        }

    } while (true);

    //check rewrite result
    EXPECT_TRUE(rewrite_vlet_context->is_finish);
    EXPECT_GE(rewrite_vlet_context->rewrite_data_len, 384ULL * 8 * 4 * 128 * 1024);
    std::shared_ptr<AppendStoreRewriteDetailInfo> rewrite_detail_info =
            std::dynamic_pointer_cast<AppendStoreRewriteDetailInfo>(rewrite_vlet_context->rewrite_detail_info_ptr);
    EXPECT_TRUE(rewrite_detail_info != nullptr);
    const std::vector<ZonefileRewriteDetailInfo>& zonefile_lists = rewrite_detail_info->get_zonefile_lists();

    for (const auto& zonefile : zonefile_lists) {
        EXPECT_TRUE(zonefile.progress.finish_rewrite);
        EXPECT_GE(zonefile.progress.finished_rewrite_record_num, 0);
        EXPECT_GE(zonefile.progress.filter_record_num, 0);
    }

    for (int i = 10; i < 256 * 8 * 4 + 10; ++i) {
        ::base::IOBuf io_buf;
        auto status = store.get(i, &meta, &io_buf);
        if (i > 200) {
            EXPECT_EQ(status.code(), AIE_OK);
        }
    }

    for (int i = 10; i < 256 * 8 * 4 + 10; ++i) {
        if (i % 2 == 1) {
            continue;
        }
        store.remove(i, true);
        store.remove(i, false);
    }

    LOG(TRACE) << " zonefile map size:" << store._zonefile_map.size();
    for (auto& iter : store._zonefile_map) {
        LOG(NOTICE) << " zonefile :" << iter.second->to_string();
    }
    LOG(TRACE) << "used_size:" << store.used_size() << " free_size:" << store.free_size()
        << "hole_size:" << store.hole_size();
    EXPECT_GE(store.free_size(), 123ULL * 256 * 1024 * 1024);
    EXPECT_GE(store.hole_size(), 1 * 128 * 1024);

    //check zonfile map size after reload
    StoreOptions opts;
    AppendStore store2(opts);
    status = store2.open(_db, "./test/A_1_1");
    EXPECT_EQ(status.code(), AIE_OK);
    EXPECT_EQ(store2._zonefile_map.size(), store._zonefile_map.size());
}

TEST_F(AppendStoreTest, test_deserialize_record) {
    prepare_store(true);
    pb::ShardMeta meta;
    meta.set_blob_len(1);
    meta.set_blob_crc(2);
    for (int i = 10; i < 100; ++i) {
        ::base::IOBuf io_buf;
        std::string str(128*1024, 'c');
        io_buf.append(str);
        meta.set_shard_len(str.size());
        meta.set_shard_crc(common::build_crc(io_buf));
        auto status = _store->put(i, meta, io_buf);
        ASSERT_EQ(status.code(), AIE_OK);
    }

    // normal
    for (int i = 10; i < 100; ++i) {
        AppendRecordLocation location;
        ZonefilePtr zonefile;
        aries::pb::RecordLocationExtraInfo extra_info;
        Status s = _store->locate_zonefile(i, &location, &zonefile);
        ASSERT_EQ(s.code(), AIE_OK);
        aries::pb::ShardMeta meta;
        base::IOBuf data;
        s = _store->do_get(i, location, zonefile, &meta, &data);
        ASSERT_EQ(s.code(), AIE_OK);
    }

    // shard_index inconsistent
    BMOCK_NS_CLASS_RESUME(aries::common, StandardRecordSerializer, deserialize, 
            Status(const common::Buffer&, uint32_t*, uint64_t*, aries::pb::SliceDescMeta*, 
                   pb::ShardMeta*, base::IOBuf*));
    EXPECT_CALL(BMOCK_NS_CLASS_OBJECT(aries::common, StandardRecordSerializer, deserialize), 
            deserialize(_, _, _, _, _, _)).WillRepeatedly(Return(Status(AIE_SHARD_INDEX_INCONSISTENT)));
    for (int i = 10; i < 100; ++i) {
        AppendRecordLocation location;
        ZonefilePtr zonefile;
        aries::pb::RecordLocationExtraInfo extra_info;
        Status s = _store->locate_zonefile(i, &location, &zonefile);
        ASSERT_EQ(s.code(), AIE_OK);
        aries::pb::ShardMeta meta;
        base::IOBuf data;
        s = _store->do_get(i, location, zonefile, &meta, &data);
        ASSERT_EQ(s.code(), AIE_SHARD_INDEX_INCONSISTENT);
    }
    BMOCK_NS_CLASS_STOP(aries::common, StandardRecordSerializer, deserialize, 
            Status(const common::Buffer&, uint32_t*, uint64_t*, aries::pb::SliceDescMeta*, 
                   pb::ShardMeta*, base::IOBuf*));
}

TEST_P(AppendStoreTest, test_shard_compress_put_and_get) {
    bool use_standard_record_layout = GetParam();
    prepare_store(use_standard_record_layout);
    pb::ShardMeta meta;
    meta.set_blob_len(1);
    meta.set_blob_crc(2);
    common::Buffer buffer(128*1024);
    common::make_mock_data(buffer.buf(), 128*1024);
    for (int i = 10; i < 256 * 8 * 4 + 10; ++i) {
        ::base::IOBuf io_buf;
        io_buf.append(buffer.buf(), buffer.size());
        meta.set_shard_len(buffer.size());
        // use zstd shard compress
        auto shard_compress_option = _store->_vlet_info.mutable_shard_compress_option();
        shard_compress_option->set_compress_type(COMPRESS_TYPE_ZSTD);
        shard_compress_option->set_slice_split_size(4096);
        meta.set_shard_crc(common::build_crc(io_buf));
        auto status = _store->put(i, meta, io_buf);
        ASSERT_EQ(status.code(), AIE_OK);
    }
    // test get
    LOG(TRACE) << "begin get";
    for (int i = 10; i < 256 * 8 * 4 + 10; ++i) {
        pb::ShardMeta meta;
        ::base::IOBuf io_buf;
        auto status = _store->get(i, &meta, &io_buf);
        ASSERT_EQ(status.code(), AIE_OK);
        EXPECT_EQ(io_buf.size(), 128*1024);
        uint32_t get_crc = build_crc(io_buf);
        uint32_t origin_crc = base::crc32c::Value(buffer.buf(), buffer.size());
        EXPECT_EQ(get_crc, origin_crc);
        if (i == 2000) {
            break;
        }
    }
    LOG(TRACE) << "begin range get";
    // test range_get
    for (int i = 10; i < 256 * 8 * 4 + 10; ++i) {
        pb::ShardMeta meta;
        ::base::IOBuf io_buf;
        size_t offset_l = base::fast_rand() % (128*1024);
        size_t len = 3390;
        if (128*1024 - offset_l < len) {
            len = 128*1024 - offset_l;
        }

        LOG(TRACE) << offset_l << ":" <<  len;
        auto status = _store->range_get(i, offset_l, len, &meta, &io_buf);
        ASSERT_EQ(status.code(), AIE_OK);
        EXPECT_EQ(io_buf.size(), len);
        uint32_t get_crc = build_crc(io_buf);
        uint32_t origin_crc = base::crc32c::Value(buffer.buf() + offset_l, len);
        EXPECT_EQ(get_crc, origin_crc);
        if (i == 2000) {
            break;
        }
    }
}

TEST_P(AppendStoreTest, range_get_with_compress) {
    //prepare env.
    g_fs->register_disk("./test/", "", 0, 0);
    StoreOptions opts;
    _store = std::unique_ptr<AppendStore>(new AppendStore(opts));
    aries::pb::AppendVletInfo vlet_info;
    vlet_info.set_align_size(4096);
    vlet_info.set_shard_index(12);
    bool use_standard_record_layout = GetParam();
    vlet_info.set_use_standard_record_layout(use_standard_record_layout);
    //set compress_type;
    vlet_info.mutable_shard_compress_option()->set_compress_type(
            static_cast<uint32_t>(CompressType::COMPRESS_TYPE_LZ4));
    vlet_info.mutable_shard_compress_option()->set_compress_level(8);
    vlet_info.mutable_shard_compress_option()->set_slice_split_size(32768);
    vlet_info.mutable_shard_compress_option()->set_min_compress_ratio(1.0);
    //set space info;
    vlet_info.mutable_space_info()->set_space_name("test_space");
    vlet_info.mutable_space_info()->set_k(6);
    vlet_info.mutable_space_info()->set_n(10);
    auto status = _store->create(vlet_info, _db, "./test/A_1_1/");
    ASSERT_EQ(status.code(), AIE_OK);

    //prepare shard data;
    const uint64_t vbid = 1258999;
    const uint64_t shard_len = 229532;
    base::IOBuf buf;
    uint32_t len_1 = 20527;
    uint32_t len_2 = 32768 * 2 + 96;
    uint32_t len_3 = 229532 - len_1 - len_2;
    uint64_t crc_1 = 0;
    uint64_t crc_2 = 0;
    uint64_t crc_3 = 0;

    {
        std::string shard_data = "shard record begin now...";
        shard_data.resize(len_1);
        crc_1 = base::crc32c::Extend(0, shard_data.data(), shard_data.size());
        buf.append(shard_data);
    }


    {
        std::string shard_data = "shard record middle now...";
        shard_data.resize(len_2);
        crc_2 = base::crc32c::Extend(0, shard_data.data(), shard_data.size());
        buf.append(shard_data);
    }

    {
        std::string shard_data = "shard record end now...";
        shard_data.resize(len_3);
        crc_3 = base::crc32c::Extend(0, shard_data.data(), shard_data.size());
        buf.append(shard_data);
    }

    ASSERT_EQ(shard_len, buf.size());
    uint32_t crc = build_crc(buf);

    aries::pb::ShardMeta shard_meta;
    shard_meta.set_blob_len(1024* 1024);
    shard_meta.set_blob_crc(222222222);
    shard_meta.set_shard_len(buf.size());
    shard_meta.set_shard_crc(crc);

    status = _store->put(vbid, shard_meta, buf);
    ASSERT_TRUE(status.ok());

    //try to read again;
    aries::pb::ShardMeta read_shard_meta;
    base::IOBuf read_buf;
    status = _store->get(vbid, &read_shard_meta, &read_buf);
    ASSERT_TRUE(status.ok());
    ASSERT_EQ(read_shard_meta.shard_len(), shard_len);
    ASSERT_EQ(read_shard_meta.shard_crc(), crc);
    ASSERT_EQ(crc, build_crc(read_buf));

    //range-get
    {
        read_buf.clear();
        status = _store->range_get(vbid, 0, len_1, &read_shard_meta, &read_buf);
        ASSERT_TRUE(status.ok());
        //As fast-range-get can not retrive shard_meta,but we can check piece shard data crc still.
        ASSERT_EQ(0, read_shard_meta.blob_len());
        ASSERT_EQ(0, read_shard_meta.blob_crc());
        ASSERT_EQ(0, read_shard_meta.shard_len());
        ASSERT_EQ(len_1, read_buf.size());
        ASSERT_EQ(build_crc(read_buf), read_shard_meta.shard_crc());
        ASSERT_EQ(crc_1, build_crc(read_buf));
    }

    {
        read_buf.clear();
        status = _store->range_get(vbid, len_1, len_2, &read_shard_meta, &read_buf);
        ASSERT_TRUE(status.ok());
        //As fast-range-get can not retrive shard_meta,but we can check piece shard data crc still.
        ASSERT_EQ(0, read_shard_meta.blob_len());
        ASSERT_EQ(0, read_shard_meta.blob_crc());
        ASSERT_EQ(0, read_shard_meta.shard_len());
        ASSERT_EQ(len_2, read_buf.size());
        ASSERT_EQ(build_crc(read_buf), read_shard_meta.shard_crc());
        ASSERT_EQ(crc_2, build_crc(read_buf));
    }

    {
        read_buf.clear();
        status = _store->range_get(vbid, len_1 + len_2, len_3, &read_shard_meta, &read_buf);
        ASSERT_TRUE(status.ok());
        //As fast-range-get can not retrive shard_meta,but we can check piece shard data crc still.
        ASSERT_EQ(0, read_shard_meta.blob_len());
        ASSERT_EQ(0, read_shard_meta.blob_crc());
        ASSERT_EQ(0, read_shard_meta.shard_len());
        ASSERT_EQ(len_3, read_buf.size());
        ASSERT_EQ(build_crc(read_buf), read_shard_meta.shard_crc());
        ASSERT_EQ(crc_3, build_crc(read_buf));
    }
}

TEST_P(AppendStoreTest, recover) {
    bool use_standard_record_layout = GetParam();
    prepare_store(use_standard_record_layout);
    prepare_data();
    StoreOptions opts;
    opts.recover_on_open = true;
    AppendStore store(opts);
    rocksdb::WriteBatch batch;
    std::unique_ptr<rocksdb::Iterator> it(_db->NewIterator(rocksdb::ReadOptions()));
    RecordKey begin(0, 0);
    it->Seek(begin.to_slice());
    uint64_t key_count = 0;
    while (it->Valid()) {
        batch.Delete(it->key());
        it->Next();
        ++key_count;
    }
    rocksdb::Status s = _db->Write(rocksdb::WriteOptions(), &batch);
    auto st = store.open(_db, "./test/A_1_1/");
    EXPECT_EQ(st.code(), AIE_OK);
    it->Seek(begin.to_slice());
    uint64_t recover_key_count = 0;
    while (it->Valid()) {
        batch.Delete(it->key());
        it->Next();
        ++recover_key_count;
    }
    EXPECT_EQ(key_count, recover_key_count);
    pb::ShardMeta meta;
    base::IOBuf data;
    st = store.get(10, &meta, &data);
    EXPECT_EQ(st.code(), AIE_OK);
    data.clear();
    st = store.range_get(10, 100, 2321, &meta, &data);
    std::string str(128*1024, 'c');
    EXPECT_EQ(st.code(), AIE_OK);
    EXPECT_EQ(meta.shard_crc(), base::crc32c::Value(str.c_str() + 100, 2321));
}

INSTANTIATE_TEST_CASE_P(test_append_store, AppendStoreTest, testing::Values(false, true));

TEST_F(AppendStoreTest, test_purge) {

    //cleanup vlet data by above cases;
    _db.reset();
    _db = nullptr;
    ::system(" rm -rf ./test ");
    sleep(10);
    ::system(" mkdir -p ./test/db ");
    open_db();
    assert(_db != nullptr);

    //test normal rebuild.
    g_fs->register_disk("./test/", "", 0, 0);

    aries::pb::AppendVletInfo vlet_info;
    vlet_info.set_volume_id(1);
    vlet_info.set_shard_index(12);
    vlet_info.set_vlet_type(80);
    vlet_info.set_align_size(4096);
    //vlet_info.set_zonefile_size(256 * 1024 * 1024);
    vlet_info.set_zonefile_num(1000);
    vlet_info.set_check_blob_ttl(true);
    std::shared_ptr<AppendVlet> append_vlet = std::make_shared<AppendVlet>(nullptr, vlet_info);
    AppendStore& store = *append_vlet->_store.get();

    auto status = store.create(vlet_info, _db, "./test/A_1_1/");
    ASSERT_EQ(status.code(), AIE_OK);

    pb::ShardMeta meta;
    meta.set_blob_len(1);
    meta.set_blob_crc(2);
    meta.set_shard_len(6);

    ::base::IOBuf io_buf1, io_buf2, io_buf3, io_buf4;
    io_buf1.append("hello.");
    io_buf2.append("world.");
    io_buf3.append("00000.");

    // no ttl
    meta.set_shard_crc(common::build_crc(io_buf1));
    status = store.put(1, meta, io_buf1);
    ASSERT_EQ(status.code(), AIE_OK);
    // expired
    meta.set_blob_ttl_timestamp(10);
    meta.set_shard_crc(common::build_crc(io_buf2));
    status = store.put(2, meta, io_buf2);
    ASSERT_EQ(status.code(), AIE_OK);
    // normal
    meta.set_blob_ttl_timestamp(base::gettimeofday_s() + 100);
    meta.set_shard_crc(common::build_crc(io_buf3));
    status = store.put(3, meta, io_buf3);
    ASSERT_EQ(status.code(), AIE_OK);

    {
        status = store.get(1, &meta, &io_buf4);
        ASSERT_EQ(status.code(), AIE_OK);
        status = store.get(2, &meta, &io_buf4);
        ASSERT_NE(status.code(), AIE_OK);
        status = store.get(3, &meta, &io_buf4);
        ASSERT_EQ(status.code(), AIE_OK);
    }
    ASSERT_EQ(store._record_num, 3);
    status = store.purge(false);
    ASSERT_EQ(status.code(), AIE_OK);

    {
        status = store.get(1, &meta, &io_buf4);
        ASSERT_EQ(status.code(), AIE_OK);
        status = store.get(2, &meta, &io_buf4);
        ASSERT_NE(status.code(), AIE_OK);
        status = store.get(3, &meta, &io_buf4);
        ASSERT_EQ(status.code(), AIE_OK);
    }
    ASSERT_EQ(store._record_num, 2);

    status = store.purge(true);
    ASSERT_EQ(status.code(), AIE_OK);
    {
        status = store.get(1, &meta, &io_buf4);
        ASSERT_NE(status.code(), AIE_OK);
        status = store.get(2, &meta, &io_buf4);
        ASSERT_NE(status.code(), AIE_OK);
        status = store.get(3, &meta, &io_buf4);
        ASSERT_EQ(status.code(), AIE_OK);
    }
    ASSERT_EQ(store._record_num, 1);
}

}
}

/* vim: set ts=4 sw=4 sts=4 tw=100 */
