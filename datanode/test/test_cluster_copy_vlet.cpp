// Copyright (c) 2016 Baidu.com, Inc. All Rights Reserved
// Author ch<PERSON><PERSON>(<EMAIL>)

#include <stdio.h>
#include <unistd.h>
#include <fcntl.h>
#include <sys/types.h>
#include <sys/stat.h>
#include <sys/mman.h>
#include <sys/time.h>

#include "baidu/inf/aries/datanode/copy_vlet.h"
#include "baidu/inf/aries/datanode/vlet/linked_vlet.h"
#include "baidu/inf/aries/common/bmock_util.h"
#include "baidu/inf/aries/datanode/util/kylin.h"
#include "baidu/inf/aries-api/common/proto/datanode.pb.h"
#include "baidu/inf/aries-api/common/rpc_stub.h"

using ::testing::Return;
using ::testing::ReturnRef;
using ::testing::_;
using ::testing::SetArgPointee;
using ::testing::DoAll;

namespace aries {
namespace datanode {
DECLARE_BMOCK_METHOD(4, fallocate, int(int, int, off_t, off_t));
DEFINE_BMOCK_METHOD(4, fallocate, int(int, int, off_t, off_t));
BMOCK_NS_CLASS_METHOD0(aries::datanode, CopyVletWorker, get_vlet_info, int());
BMOCK_NS_CLASS_METHOD0(aries::datanode, CopyVletWorker, vlet_info, aries::pb::VletInfo&());
MAKE_BMOCK_NS_CLASS_METHOD(4, aries::pb, DataNodeDataService_Stub, get_vlet_info,
                    void(::google::protobuf::RpcController*,
                    const ::aries::pb::GetVletInfoRequest* request,
                    ::aries::pb::GetVletInfoResponse* response,
                    ::google::protobuf::Closure*));

MAKE_BMOCK_NS_CLASS_METHOD(4, aries::pb, DataNodeDataService_Stub, list_blob,
                    void(::google::protobuf::RpcController*,
                    const ::aries::pb::ListBlobRequest* request,
                    ::aries::pb::ListBlobResponse* response,
                    ::google::protobuf::Closure*));

MAKE_BMOCK_NS_CLASS_METHOD(1, aries::datanode, CAsyncClient, queue_exec, void(AsyncContext*));
MAKE_BMOCK_NS_CLASS_METHOD(1, aries::datanode, HeavyWorker, dispatch, void(AsyncContext*));


void *rpc_callback_sleep(void *param) {
    google::protobuf::Closure *done = (google::protobuf::Closure *) param;
    if (done != NULL) {
        done->Run();
    }
    return NULL;
}



void *local_callback_sleep(void *param) {
    google::protobuf::Closure* done = ((ListBlobContext*)param)->done_guard.release();
    auto* local_response = ((ListBlobContext*)param)->response;
    local_response->add_vbid_list(1);
    local_response->add_vbid_list(2);
    local_response->add_vbid_list(3);
    local_response->add_vbid_list(4);
    local_response->add_mark_deleted_vbid_list(1);
    local_response->mutable_status()->set_code(AIE_OK);
    if (done != NULL) {
        done->Run();
    }
    return NULL;
}
void mock_rpc_call(google::protobuf::RpcController *, const google::protobuf::Message *,
        google::protobuf::Message *, google::protobuf::Closure *done) {
    bthread_t tid;
    bthread_start_background(&tid, NULL, rpc_callback_sleep, done);
}

void mock_local_call(AsyncContext *ctx) { 
    bthread_t tid;
    bthread_start_background(&tid, NULL, local_callback_sleep, ctx);
}

char  cwd_path[1024];
int generator_disk_conf(const aries::pb::DatanodeDiskConf& datanode_disk_conf) {
    std::string json_data;
    std::string err_msg;
    struct Pb2JsonOptions options;
    options.pretty_json = true;
    options.enum_option = OUTPUT_ENUM_BY_NUMBER;

    if (!ProtoMessageToJson(datanode_disk_conf, &json_data, options, &err_msg)) {
        return -1;
    }

    VletManager vlet_mgr;
    ::system("echo {} > conf/disk.conf");
    DiskManager disk_manager(&vlet_mgr);
    disk_manager.load_disk_conf();

    return  disk_manager.update_disk_conf(json_data) == false;
}
void make_disk_conf(std::string type) {
    std::string path(cwd_path);
    aries::pb::DatanodeDiskConf disk_conf;

    auto disk_item = disk_conf.add_disk_list();

    disk_item->set_disk_id(0);
    disk_item->set_disk_path(path + "/disk0");
    disk_item->set_create_time(100);
    disk_item->set_capacity_mb(1024);
    disk_item->set_is_used(true);
    disk_item->set_disk_type(type);

    generator_disk_conf(disk_conf);
}

int mocked_fallocate(int fd, int mode, off_t offset, off_t len) {
    if (mode & FALLOC_FL_KEEP_SIZE) {
        return 0;
    }
    return ftruncate(fd, offset + len);
}

DiskAgent* disk_agent = nullptr;
class CopyVletTest : public ::testing::Test {
public:
    static void SetUpTestCase() {
        FLAGS_enable_cache = false;
        ::getcwd(cwd_path, sizeof(cwd_path));
        // for configure
        ::system("rm -rf conf;  mkdir -p conf");

        // data
        ::system("rm -rf disk0");
        ::system("mkdir -p disk0");

        make_disk_conf("HDD");

        g_datanode = new Datanode();
        g_datanode->_disk_manager->load_disk_conf();
        assert(g_datanode->_disk_manager->disk_number() == 1);
        assert(g_datanode->_disk_manager->find(0) != nullptr);
        disk_agent = g_datanode->_disk_manager->find(0);

        disk_agent->db_open();
        disk_agent->_is_used = true;
    }
    void SetUp() {
        FLAGS_enable_cache = false;
        CopyVletArgContext *ctx = new CopyVletArgContext();
        _worker = std::make_shared<CopyVletWorker>(ctx, &cntl, nullptr, &req, &res);
        ctx->worker = _worker;
        _worker->_disk_agent = g_datanode->disk_manager()->find(0);

        EXPECT_CALL(BMOCK_OBJECT(fallocate), fallocate(_,_,_,_))
            .WillRepeatedly(Invoke(mocked_fallocate));
    }
    void TearDown() {
        if (_worker != nullptr) {
            _worker = nullptr;
        }
    }
private:
    std::shared_ptr<CopyVletWorker> _worker;
    baidu::rpc::Controller cntl;
    aries::pb::BalanceVletRequest req;
    aries::pb::AckResponse res;
    BMOCK_MOCK_GUARD(fallocate);
};

TEST_F(CopyVletTest, test_init_copyworker) {
    // error token
    {
        ::aries::pb::BalanceVletRequest request;
        ::aries::pb::AckResponse response;
        ::google::protobuf::Closure* done = nullptr;
        baidu::rpc::Controller cntl;
        {
            request.set_purpose(COPY_SPACE);
            request.set_token("error_token");
        }
        CopyVletArgContext* context = new CopyVletArgContext(); 
        auto copy_worker = std::make_shared<CopyVletWorker>(context, &cntl, done, &request, &response);
        context->worker = copy_worker;
        int ret = copy_worker->init();
        EXPECT_EQ(response.status().code(), AIE_INVALID_TOKEN);
        LOG(TRACE) << "msg:" << response.status().msg();
    }
    // error disk
    {
        ::aries::pb::BalanceVletRequest request;
        ::aries::pb::AckResponse response;
        ::google::protobuf::Closure* done = nullptr;
        baidu::rpc::Controller cntl;
        {
            request.set_purpose(COPY_SPACE);
            request.set_token(FLAGS_token);
            request.set_target_disk(1);
        }
        CopyVletArgContext* context = new CopyVletArgContext(); 
        auto copy_worker = std::make_shared<CopyVletWorker>(context, &cntl, done, &request, &response);
        context->worker = copy_worker;
        int ret = copy_worker->init();
        EXPECT_EQ(response.status().code(), AIE_INVALID);
        LOG(TRACE) << "msg:" << response.status().msg();
    }
    // error disk type
    {
        ::aries::pb::BalanceVletRequest request;
        ::aries::pb::AckResponse response;
        ::google::protobuf::Closure* done = nullptr;
        baidu::rpc::Controller cntl;
        {
            request.set_purpose(COPY_SPACE);
            request.set_token(FLAGS_token);
            request.set_target_disk(0);
            request.set_disk_type("SSD");
        }
        CopyVletArgContext* context = new CopyVletArgContext(); 
        auto copy_worker = std::make_shared<CopyVletWorker>(context, &cntl, done, &request, &response);
        context->worker = copy_worker;
        int ret = copy_worker->init();
        EXPECT_EQ(response.status().code(), AIE_FAIL);
        DiskManager* disk_mgr = g_datanode->disk_manager();
        LOG(TRACE) << "msg:" << response.status().msg();
    }
    // get vlet info fail
    {
        BMOCK_CLASS_MOCK_GUARD(DataNodeDataService_Stub, get_vlet_info);
        ::aries::pb::GetVletInfoResponse res1;
        res1.mutable_status()->set_code(AIE_FAIL);
        res1.mutable_status()->set_msg("");
        EXPECT_CALL(BMOCK_NS_CLASS_OBJECT(aries::pb, DataNodeDataService_Stub, get_vlet_info),
            get_vlet_info(_, _, _, _))
        .WillRepeatedly(SetArgPointee<2>(res1));
        ::aries::pb::BalanceVletRequest request;
        ::aries::pb::AckResponse response;
        ::google::protobuf::Closure* done = nullptr;
        baidu::rpc::Controller cntl;
        {
            request.set_purpose(COPY_SPACE);
            request.set_token(FLAGS_token);
            request.set_target_disk(0);
            request.set_disk_type("HDD");
        }
        CopyVletArgContext* context = new CopyVletArgContext(); 
        auto copy_worker = std::make_shared<CopyVletWorker>(context, &cntl, done, &request, &response);
        context->worker = copy_worker;
        int ret = copy_worker->init();
        EXPECT_EQ(response.status().code(), AIE_FAIL);
        LOG(TRACE) << "msg:" << response.status().msg();
    }
    // vlet created
    {
        BMOCK_CLASS_MOCK_GUARD(DataNodeDataService_Stub, get_vlet_info);
        ::aries::pb::GetVletInfoResponse res1;
        auto info = res1.add_vlet_info_list();
        info->set_shard_index(1);
        info->set_vlet_type(11);
        info->set_volume_id(1);
        res1.mutable_status()->set_code(AIE_OK);
        res1.mutable_status()->set_msg("");
        EXPECT_CALL(BMOCK_NS_CLASS_OBJECT(aries::pb, DataNodeDataService_Stub, get_vlet_info),
            get_vlet_info(_, _, _, _))
        .WillRepeatedly(SetArgPointee<2>(res1));
        ::aries::pb::BalanceVletRequest request;
        ::aries::pb::AckResponse response;
        ::google::protobuf::Closure* done = nullptr;
        baidu::rpc::Controller cntl;
        {
            request.set_purpose(COPY_SPACE);
            request.set_token(FLAGS_token);
            request.set_target_disk(0);
            request.set_disk_type("HDD");
            request.mutable_vlet()->set_volume_id(1);
            request.mutable_vlet()->set_shard_index(1);
            request.mutable_space_info();
        }
        CopyVletArgContext* context = new CopyVletArgContext(); 
        auto copy_worker = std::make_shared<CopyVletWorker>(context, &cntl, done, &request, &response);
        context->worker = copy_worker;
        int ret = copy_worker->init();
        EXPECT_EQ(response.status().code(), AIE_OK);
        LOG(TRACE) << "msg:" << response.status().msg();
    }

    // succeed
    {
        BMOCK_CLASS_MOCK_GUARD(DataNodeDataService_Stub, get_vlet_info);
        ::aries::pb::GetVletInfoResponse res1;
        auto info = res1.add_vlet_info_list();
        info->set_shard_index(1);
        info->set_vlet_type(11);
        info->set_volume_id(1);
        res1.mutable_status()->set_code(AIE_OK);
        res1.mutable_status()->set_msg("");

        EXPECT_CALL(BMOCK_NS_CLASS_OBJECT(aries::pb, DataNodeDataService_Stub, get_vlet_info),
            get_vlet_info(_, _, _, _))
        .WillRepeatedly(SetArgPointee<2>(res1));

        ::aries::pb::BalanceVletRequest request;
        ::aries::pb::AckResponse response;
        ::google::protobuf::Closure* done = nullptr;
        baidu::rpc::Controller cntl;
        {
            request.set_purpose(COPY_SPACE);
            request.set_token(FLAGS_token);
            request.set_target_disk(0);
            request.set_disk_type("HDD");
            request.mutable_vlet()->set_volume_id(1);
            request.mutable_vlet()->set_shard_index(1);
            request.mutable_space_info();
        }
        CopyVletArgContext* context = new CopyVletArgContext(); 
        auto copy_worker = std::make_shared<CopyVletWorker>(context, &cntl, done, &request, &response);
        context->worker = copy_worker;
        int ret = copy_worker->init();
        EXPECT_EQ(response.status().code(), AIE_EXIST);
        LOG(TRACE) << "msg:" << response.status().msg();
    }
}

TEST_F(CopyVletTest, test_check_copy_vlet_index) {
    FLAGS_disk_hung_second = 1;
    ::aries::pb::BalanceVletRequest request;
    ::aries::pb::AckResponse response;
    ::google::protobuf::Closure* done = nullptr;
    baidu::rpc::Controller cntl;
    CopyVletArgContext* context = new CopyVletArgContext(); 
    auto copy_worker = std::make_shared<CopyVletWorker>(context, &cntl, done, &request, &response);
    // right
    {
        aries::pb::ListBlobResponse local_response;
        aries::pb::ListBlobResponse remote_response;
        // vbids same with local response
        remote_response.add_vbid_list(1);
        remote_response.add_vbid_list(2);
        remote_response.add_vbid_list(3);
        remote_response.add_vbid_list(4);
        remote_response.add_mark_deleted_vbid_list(1);
        remote_response.mutable_status()->set_code(AIE_OK);
        BMOCK_CLASS_MOCK_GUARD(DataNodeDataService_Stub, list_blob);
        EXPECT_CALL(BMOCK_NS_CLASS_OBJECT(aries::pb, DataNodeDataService_Stub, list_blob),
            list_blob(_, _, _, _))
            .WillOnce(DoAll(SetArgPointee<2>(remote_response), Invoke(mock_rpc_call)));

        ListBlobContext ctx;
        ctx.response = &local_response;
        BMOCK_CLASS_MOCK_GUARD(HeavyWorker, dispatch);
        EXPECT_CALL(BMOCK_NS_CLASS_OBJECT(aries::datanode, HeavyWorker, dispatch),
            dispatch(_))
        .WillOnce(DoAll(SetArgPointee<0>((AsyncContext)ctx), Invoke(mock_local_call)));
        LinkedVlet vlet(disk_agent, 1, 0);
        int ret = vlet.check_copy_vlet_index(copy_worker.get());
        EXPECT_EQ(0, ret);
    }
    // not right
    {
        aries::pb::ListBlobResponse local_response;
        aries::pb::ListBlobResponse remote_response; 
        // vbid 4 not exist 
        remote_response.add_vbid_list(1);
        remote_response.add_vbid_list(2);
        remote_response.add_vbid_list(3);
        remote_response.add_mark_deleted_vbid_list(2);
        remote_response.mutable_status()->set_code(AIE_OK);
        BMOCK_CLASS_MOCK_GUARD(DataNodeDataService_Stub, list_blob);
        EXPECT_CALL(BMOCK_NS_CLASS_OBJECT(aries::pb, DataNodeDataService_Stub, list_blob),
            list_blob(_, _, _, _))
        .WillOnce(DoAll(SetArgPointee<2>(remote_response), Invoke(mock_rpc_call)));
        
        ListBlobContext ctx;
        ctx.response = &local_response;
        BMOCK_CLASS_MOCK_GUARD(CAsyncClient, queue_exec);
        EXPECT_CALL(BMOCK_NS_CLASS_OBJECT(aries::datanode, CAsyncClient, queue_exec),
            queue_exec(_))
        .WillOnce(DoAll(SetArgPointee<0>((AsyncContext)ctx), Invoke(mock_local_call)));
        LinkedVlet vlet(disk_agent, 1, 0);
        int ret = vlet.check_copy_vlet_index(copy_worker.get());
        EXPECT_EQ(-1, ret);
    }
    // right
    {
        aries::pb::ListBlobResponse local_response;
        aries::pb::ListBlobResponse remote_response; 
        // mark delete not right but do not care
        remote_response.add_vbid_list(1);
        remote_response.add_vbid_list(2);
        remote_response.add_vbid_list(3);
        remote_response.add_vbid_list(4);
        remote_response.add_mark_deleted_vbid_list(2);
        remote_response.mutable_status()->set_code(AIE_OK);
        BMOCK_CLASS_MOCK_GUARD(DataNodeDataService_Stub, list_blob);
        EXPECT_CALL(BMOCK_NS_CLASS_OBJECT(aries::pb, DataNodeDataService_Stub, list_blob),
            list_blob(_, _, _, _))
        .WillOnce(DoAll(SetArgPointee<2>(remote_response), Invoke(mock_rpc_call)));

        ListBlobContext ctx;
        ctx.response = &local_response;
        BMOCK_CLASS_MOCK_GUARD(HeavyWorker, dispatch);
        EXPECT_CALL(BMOCK_NS_CLASS_OBJECT(aries::datanode, HeavyWorker, dispatch),
            dispatch(_))
        .WillOnce(DoAll(SetArgPointee<0>((AsyncContext)ctx), Invoke(mock_local_call)));

        LinkedVlet vlet(disk_agent, 1, 0);
        int ret = vlet.check_copy_vlet_index(copy_worker.get());
        EXPECT_EQ(0, ret);
    }
}

}
}
