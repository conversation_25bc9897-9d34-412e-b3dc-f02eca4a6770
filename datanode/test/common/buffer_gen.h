#pragma once

#include <base/iobuf.h>
#include "baidu/inf/aries-api/common/buffer.h"

namespace aries {
namespace datanode {

// Helper class that generates an arbitrarily long buffer
class BufferGen {
public:
    inline static constexpr char kAlphabet[65] =
            "ABCDEFGHIJKLMNOPQRSTUVWXYZ"
            "abcdefghijklmnopqrstuvwxyz"
            "0123456789=+";

    // @param size    size of the buffer to generate
    static base::IOBuf gen(uint32_t size) {
        base::IOBuf buf;
        for (uint32_t i = 0; i < size; i++) {
            buf.push_back(kAlphabet[base::fast_rand() % (sizeof(kAlphabet) - 1)]);
        }
        return buf;
    }

    // Randomly generate a buffer between [size_min, size_max)
    // @param size_min   mininum size of the new buffer
    // @param size_max   maximum size of the new buffer
    static base::IOBuf gen(uint32_t size_min, uint32_t size_max) {
        if (size_min == size_max) {
            return gen(size_min);
        } else {
            return gen(size_min + static_cast<uint32_t>(base::fast_rand() % (size_max - size_min)));
        }
    }

    static void gen(char* buf, size_t size) {
        for (size_t i = 0; i < size; i++) {
            buf[i] = kAlphabet[base::fast_rand() % (sizeof(kAlphabet) - 1)];
        }
    }

    static void gen(common::Buffer* buf) {
        gen(buf->buf(), buf->size());
    }
};

}
}
