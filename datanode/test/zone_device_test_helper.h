// Copyright (c) 2022 Baidu.com, Inc. All Rights Reserved.

#pragma once

#include "folly/String.h"
#include <base/fast_rand.h>
#include "base/file_util.h"
#include "base/files/file_enumerator.h"
#include "baidu/inf/aries/datanode/flags.h"
#include "baidu/inf/aries/datanode/io_context.h"
#include "baidu/inf/aries/datanode/test/common/buffer_gen.h"
#include "baidu/inf/aries/datanode/test/mem_zone_disk.h"
#include "baidu/inf/aries/datanode/vlet/zone_vlet.h"
#include "baidu/inf/aries/datanode/vlet_indexer.h"
#include "baidu/inf/aries/datanode/storage/zone/device/file_zone_device_backend.h"
#include "baidu/inf/aries/datanode/storage/zone/zone_record.h"
#include "baidu/inf/aries/datanode/storage/zone/zone_meta_persistor.h"
#include "baidu/inf/aries/datanode/storage/zone/channel/channel_writer.h"

namespace aries {
namespace datanode {

#define DISK_PATH "./ZONE_DISK_5c6c01914c8344"

extern uint32_t g_shard_index;

class FileZoneDeviceBackendTestHelper  {
public:
    FileZoneDeviceBackendTestHelper() {
        FLAGS_batch_create_zone_num = 10000;
        _disk_path = base::string_printf("./ZONE_DISK_%ld", base::gettimeofday_us());
        _rocksdb_path = base::string_printf("./test_db_%ld", base::gettimeofday_us());

        _opt.id = 3;
        _opt.zone_size = 1 * common::MB;
        _opt.capacity = (8 + FLAGS_rewrite_channel_count + FLAGS_max_rewrite_thread_num_per_disk) *
                _opt.zone_size;
        _opt.align_size = 4 * 1024;
        _opt.path = _disk_path;
        _opt.type = common::DT_ZONE_SSD;

        ::system(base::string_printf("mkdir %s", _disk_path.c_str()).c_str());
        ::system(base::string_printf(" rm -rf %s", _rocksdb_path.c_str()).c_str());
        rocksdb::DB* tmp;
        rocksdb::Options options;
        options.create_if_missing = true;
        Status s = rocksdb::DB::Open(options, _rocksdb_path, &tmp);
        assert(s.ok());
        _db.reset(tmp);

        _indexer = std::unique_ptr<ZoneRecordDBIndexer>(new ZoneRecordDBIndexer(0, g_shard_index, _db));

        _zone_indexer = std::make_shared<ZoneMetaDBIndexer>(_db);

        _record_serializer = std::unique_ptr<ZoneRecordSerializer>(new ZoneRecordSerializer(_opt.align_size));
    }

    ~FileZoneDeviceBackendTestHelper() {
        _db.reset();
        ::system(base::string_printf(" rm -rf %s", _rocksdb_path.c_str()).c_str());
        ::system(base::string_printf(" rm -rf %s", _disk_path.c_str()).c_str());
    }

    void put_index(uint64_t vbid, const ZoneRecordLocation& location, const aries::pb::SliceDescMeta& slice_desc_meta, uint64_t record_seq_no) {
        ZoneRecordIndexEntry new_index_entry;
        new_index_entry.location = location;
        new_index_entry.seq_no = record_seq_no;
        new_index_entry.slice_desc_meta.CopyFrom(slice_desc_meta);
        new_index_entry.mark_delete_time = 0;
        //update index to rocksdb;
        LOG(NOTICE) << "put record,"
                    << " location:" << location
                    << " index:" << ZoneRecordIndexKey(_indexer->_vid, vbid, _indexer->_shard_index);
        auto status = _indexer->put(vbid, new_index_entry);
        ASSERT_TRUE(status.ok());
    }

    int create_file(const std::string& file_path, uint64_t size, int* fd) {
        int flags{O_RDWR | O_CREAT};
        *fd = ::open(file_path.c_str(), flags | O_DIRECT, 0666);
        if (*fd < 0) {
#ifdef _UNIT_TEST
            // for unit testing using tmpfs
            if (errno == EINVAL) {
                LOG(WARNING) << "failed to open with o-direct, trying without. errno:(" << errno << ")" << strerror(errno);
                *fd = ::open(file_path.c_str(), flags, 0666);
                if (*fd < 0) {
                    LOG(WARNING) << "create file failed, file:" << file_path
                                << " errno:(" << errno << ")" << strerror(errno);
                    return AIE_FAIL;
                }
            } else
#endif
            {
                LOG(FATAL) << "create file failed, file:" << file_path
                           << " errno:(" << errno << ")" << strerror(errno);
                return AIE_FAIL;
            }
        }

        if (::fallocate(*fd, FALLOC_FL_CONVERT_AND_EXTEND, 0, size) != 0) {
            bool fallocate_failed = true;
            if (errno == EOPNOTSUPP) {
                LOG(NOTICE) << "fallocate failed due to unsupported mode:FALLOC_FL_CONVERT_AND_EXTEND, try 0";
                if (::fallocate(*fd, 0, 0, size) == 0) {
                    fallocate_failed = false;
                }
            }
            if (fallocate_failed) {
                LOG(FATAL) << "create file failed due to fallocate failed, file:" << file_path
                           << " errno:(" << errno << ")" << strerror(errno);
                ::close(*fd);
                ::unlink(file_path.c_str());
                return AIE_FAIL;
            }
        }

        LOG(NOTICE) << "create file succeeded, path:" << file_path;
        return AIE_OK;
    }

    int create_zone_file(std::string file_name, int64_t size = -1) {
        int fd{-1};
        std::string file_path = base::string_printf("%s/%s", DISK_PATH, file_name.c_str());
        int ret = create_file(file_path, (size >= 0 ? size : _opt.zone_size), &fd);
        if (ret != AIE_OK) {
            LOG(FATAL) << "create zone file failed, file:" << file_path
                       << " errno:(" << errno << ")" << strerror(errno);
            return ret;
        }
        ::close(fd);

        return AIE_OK;
    }

    std::vector<std::string> list_dev_path() {
        std::vector<std::string> v;
        base::FilePath dir(DISK_PATH);
        base::FileEnumerator f_enum(dir, false, base::FileEnumerator::FILES);
        for (auto name = f_enum.Next(); !name.empty(); name = f_enum.Next()) {
            v.push_back(name.value());
        }

        return v;
    }

    auto create_dev() {
        return std::make_unique<MemFileZoneDeviceBackend>(_db);
    }

    auto create_open_dev() {
        auto dev = create_dev();
        int err_code = dev->open(_opt);
        if (err_code != AIE_OK) {
            dev.reset();
        }
        return dev;
    }

    Status serialize_zone_meta_record(ZoneID zone_id,
                                      ChannelGroupID channel_group_id,
                                      ChannelID channel_id,
                                      ZoneState state,
                                      int64_t zone_size,
                                      common::Buffer* result) {
        ZoneRecordSerializer serializer(4096);
        aries::pb::ZoneMeta zone_meta;

        zone_meta.set_zone_id(zone_id);
        zone_meta.set_channel_group_id(channel_group_id);
        zone_meta.set_channel_id(channel_id);
        zone_meta.set_seq_no(_seq_no++);
        zone_meta.set_state((uint32_t) state);
        zone_meta.set_zone_size(zone_size);
        zone_meta.set_avg_create_time(0);

        result->reset(4096, 4096);

        auto status = serializer.serialize_meta_record(zone_meta, common::RecordType::RECORD_ZONE_META, result);
        if (status.failed()) {
            LOG(WARNING) << "serialize zone meta record failed due to serialization failed, "
                            "zone_id:" << zone_id << " status:" << status;
        }
        return status;
    }

    Status serialize_zone_nop_record(ZoneID zone_id,
                                     ChannelGroupID channel_group_id,
                                     ChannelID channel_id,
                                     int64_t nop_record_len,
                                     common::Buffer* result) {
        ZoneRecordSerializer serializer(4096);
        aries::pb::ZoneNopMeta zone_meta;
        zone_meta.set_zone_id(zone_id);
        zone_meta.set_channel_group_id(channel_group_id);
        zone_meta.set_channel_id(channel_id);
        zone_meta.set_seq_no(_seq_no++);

        result->reset(nop_record_len, 4096);
        auto status = serializer.serialize_meta_record(zone_meta, common::RecordType::RECORD_ZONE_NOP_META, result);
        if (status.failed()) {
            LOG(WARNING) << "write zone meta record failed due to serialization failed, "
                            "zone_id:" << zone_id << " status:" << status;
        }

        return status;
    }

    void make_data_record(uint32_t data_len, int64_t vbid, uint64_t seq_no, common::Buffer* record) {
        common::StandardRecordSerializer serializer(0, 4096, "");
        auto data = BufferGen::gen(data_len);
        auto data_crc = common::build_crc(data);

        aries::pb::ShardMeta shard_meta;
        shard_meta.set_shard_len(data_len);
        shard_meta.set_shard_crc(data_crc);
        shard_meta.set_blob_len(data_len);
        shard_meta.set_blob_crc(data_crc);
        aries::pb::ShardCompressOption compress_option;
        aries::pb::SliceDescMeta slice_desc_meta;
        common::SerializeArgs args;
        args.footer_len = sizeof(ZoneRecordFooter);
        auto s = serializer.serialize(vbid, g_shard_index, data, shard_meta, compress_option, record, &slice_desc_meta, &args);

        _record_serializer->fill_footer(record->buf(), record->size(), seq_no, 0);

        ASSERT_TRUE(s.ok());
    }

    void make_data_record(uint32_t data_len, int64_t vbid, common::Buffer* record) {
        make_data_record(data_len, vbid, _seq_no++, record);
    }

    void write_data_record(FileZoneDeviceBackend* dev, ZoneID zone_id, uint64_t offset, uint64_t data_len, uint64_t seq_no, int64_t zone_size) {
        common::Buffer record;
        make_data_record(data_len - 4096, _seq_no++, seq_no, &record);

        ZoneRecordLocation loc(zone_id, offset, data_len);
        int ret = dev->write(record, loc);
        ASSERT_EQ(ret, AIE_OK);
    }

    void make_data_record(uint32_t data_len, int64_t vbid, const ZoneRecordLocation& location, common::Buffer* record) {
        common::StandardRecordSerializer serializer(0, 4096, "");
        auto data = BufferGen::gen(data_len);
        auto data_crc = common::build_crc(data);

        aries::pb::ShardMeta shard_meta;
        shard_meta.set_shard_len(data_len);
        shard_meta.set_shard_crc(data_crc);
        shard_meta.set_blob_len(data_len);
        shard_meta.set_blob_crc(data_crc);
        aries::pb::ShardCompressOption compress_option;
        aries::pb::SliceDescMeta slice_desc_meta;
        common::SerializeArgs args;
        args.footer_len = sizeof(ZoneRecordFooter);
        auto s = serializer.serialize(vbid, g_shard_index, data, shard_meta, compress_option, record, &slice_desc_meta, &args);
        ASSERT_TRUE(s.ok());

        auto seq_no = _seq_no++;
        _record_serializer->fill_footer(record->buf(), record->size(), seq_no, 0);

        put_index(vbid, location, slice_desc_meta, seq_no);
    }

    void make_close_record(ZoneID zone_id, int64_t zone_size, common::Buffer* record) {
        auto s = serialize_zone_meta_record(zone_id, DEFAULT_CHANNEL_GROUP_ID, DEFAULT_CHANNEL_ID, ZoneState::CLOSED, zone_size, record);
        ASSERT_TRUE(s.ok());
    }
    void make_free_record(ZoneID zone_id, int64_t zone_size, common::Buffer* record) {
        auto s = serialize_zone_meta_record(zone_id, DEFAULT_CHANNEL_GROUP_ID, DEFAULT_CHANNEL_ID, ZoneState::FREE, zone_size, record);
        ASSERT_TRUE(s.ok());
    }

    void make_open_record(ZoneID zone_id, int64_t zone_size, common::Buffer* record) {
        auto s = serialize_zone_meta_record(zone_id, DEFAULT_CHANNEL_GROUP_ID, DEFAULT_CHANNEL_ID, ZoneState::OPEN, zone_size, record);
        ASSERT_TRUE(s.ok());
    }

    void write_open_record(FileZoneDeviceBackend* dev, ZoneID zone_id, int64_t zone_size) {
        common::Buffer open_record;
        make_open_record(zone_id, zone_size, &open_record);

        ZoneRecordLocation loc(zone_id, 0, 4096);
        int ret = dev->write(open_record, loc);
        ASSERT_EQ(ret, AIE_OK);
    }

    void write_open_record_v2(FileZoneDeviceBackend* dev, ZoneID zone_id, int64_t zone_size) {
        ZoneMetaRecordWriter writer(dev,  DEFAULT_CHANNEL_GROUP_ID, DEFAULT_CHANNEL_ID);
        int ret = writer.write_open_record(zone_id, zone_size, _seq_no++);
        ASSERT_EQ(ret, AIE_OK);
    }

    int write_open_record_v3(FileZoneDeviceBackend* dev, ZoneID* zone_id, int64_t* zone_size) {
        ZoneMetaPersistor writer(dev, _zone_indexer.get(), DEFAULT_CHANNEL_GROUP_ID, DEFAULT_CHANNEL_ID);
        ZoneMeta zone_meta;
        const ZoneFD* zone_fd = nullptr;
        int ret = writer.allocate_zone(&zone_meta, &zone_fd);
        if (ret != AIE_OK) {
            return ret;
        }

        *zone_id = zone_meta.zone_id;
        *zone_size = zone_meta.zone_size;
        return AIE_OK;
    }

    void write_close_record(FileZoneDeviceBackend* dev, ZoneID zone_id, int64_t zone_size) {
        common::Buffer record;
        make_close_record(zone_id, zone_size, &record);

        ZoneRecordLocation loc(zone_id, zone_size - 4096 * 2, 4096);
        int ret = dev->write(record, loc);
        ASSERT_EQ(ret, AIE_OK);
    }

    void write_close_record_v2(FileZoneDeviceBackend* dev, ZoneID zone_id, int64_t zone_size, std::optional<int64_t> nop_record = {}) {
        ZoneMetaRecordWriter writer(dev, DEFAULT_CHANNEL_GROUP_ID, DEFAULT_CHANNEL_ID);
        int ret = writer.write_close_record(zone_id, zone_size, _seq_no++, zone_size - (nop_record.has_value() ? nop_record.value() + ZONE_META_RECORD_SIZE * 2 : ZONE_META_RECORD_SIZE * 2));
        ASSERT_EQ(ret, AIE_OK);
    }

    int write_close_record_v3(FileZoneDeviceBackend* dev, ZoneID zone_id, int64_t zone_size, std::optional<int64_t> nop_record = {}) {
        ZoneMetaPersistor writer(dev, _zone_indexer.get(), DEFAULT_CHANNEL_GROUP_ID, DEFAULT_CHANNEL_ID);
        ZoneMeta zone;
        zone.zone_id = zone_id;
        zone.zone_size = zone_size;
        zone.channel_id = DEFAULT_CHANNEL_ID;
        zone.channel_group_id = DEFAULT_CHANNEL_GROUP_ID;
        int ret = writer.close_zone(zone_size - (nop_record.has_value() ? nop_record.value() + ZONE_META_RECORD_SIZE * 2 : ZONE_META_RECORD_SIZE * 2), &zone);
        return ret;
    }

    void write_free_record(FileZoneDeviceBackend* dev, ZoneID zone_id, int64_t zone_size) {
        common::Buffer record;
        make_free_record(zone_id, zone_size, &record);

        ZoneRecordLocation loc(zone_id, zone_size - 4096, 4096);
        int ret = dev->write(record, loc);
        ASSERT_EQ(ret, AIE_OK);
    }
    void write_free_record_v2(FileZoneDeviceBackend* dev, ZoneID zone_id, int64_t zone_size) {
        ZoneMetaRecordWriter writer(dev, DEFAULT_CHANNEL_GROUP_ID, DEFAULT_CHANNEL_ID);
        int ret = writer.write_free_record(zone_id, zone_size, _seq_no++);
        ASSERT_EQ(ret, AIE_OK);
    }
    int write_free_record_v3(FileZoneDeviceBackend* dev, ZoneID zone_id, int64_t zone_size) {
        ZoneMetaPersistor writer(dev, _zone_indexer.get(), DEFAULT_CHANNEL_GROUP_ID, DEFAULT_CHANNEL_ID);
        ZoneMeta zone;
        zone.zone_id = zone_id;
        zone.zone_size = zone_size;
        zone.channel_id = DEFAULT_CHANNEL_ID;
        zone.channel_group_id = DEFAULT_CHANNEL_GROUP_ID;
        int ret = writer.free_zone(zone);
        return ret;
    }

    void write_records(FileZoneDeviceBackend* dev, ZoneID zone_id, int64_t zone_size,
                       const std::map<uint32_t, uint32_t>& holes,
                       const std::map<uint32_t, uint32_t>& data_records,
                       const std::map<uint32_t, uint32_t>& nop_records,
                       int64_t start_offset) {
        common::Buffer records;
        ZoneRecordLocation loc(zone_id, start_offset, records.size());
        make_records(zone_id, zone_size, holes, data_records, nop_records, start_offset, loc, &records);
        loc.length = records.size();
        int ret = dev->write(records, loc);
        ASSERT_EQ(ret, AIE_OK);
    }

    void make_hole(uint32_t hole_len, common::Buffer* record) {
        record->reset(hole_len, 4096);
        memset(record->buf(), 0, hole_len);
    }

    void make_nop_record(ZoneID zone_id, uint32_t nop_len, common::Buffer* record) {
        auto s = serialize_zone_nop_record(zone_id, DEFAULT_CHANNEL_GROUP_ID, DEFAULT_CHANNEL_ID, nop_len, record);
        ASSERT_TRUE(s.ok());
    }

    void make_records(ZoneID zone_id, int64_t zone_size,
                      const std::map<uint32_t, uint32_t>& holes,
                      const std::map<uint32_t, uint32_t>& data_records,
                      const std::map<uint32_t, uint32_t>& nop_records,
                      int64_t start_offset,
                      const ZoneRecordLocation& location,
                      common::Buffer* result) {
        int32_t records_length = 0;
        for (const auto& [off, len] : holes) {
            records_length += len;
        }
        for (const auto& [off, len] : data_records) {
            records_length += len;
        }
        for (const auto& [off, len] : nop_records) {
            records_length += len;
        }

        result->reset(records_length, 4096);
        memset(result->buf(), 0, records_length);

        for (const auto& [off, len] : holes) {
            common::Buffer record;
            make_hole(len, &record);
            memcpy(result->buf() + off - start_offset, record.buf(), record.size());
        }
        for (const auto& [off, len] : data_records) {
            ASSERT_TRUE(len >= 8192);
            common::Buffer record;
            ZoneRecordLocation record_location;
            record_location.zone_id = location.zone_id;
            record_location.offset = off;
            record_location.length = len;
            make_data_record(len - 4096, _seq_no++, record_location, &record);
            ASSERT_EQ(record.size(), len);
            memcpy(result->buf() + off - start_offset, record.buf(), record.size());
        }
        for (const auto& [off, len] : nop_records) {
            common::Buffer record;
            make_nop_record(zone_id, len, &record);
            memcpy(result->buf() + off - start_offset, record.buf(), record.size());
        }
    }

    void make_records(ZoneID zone_id, int64_t zone_size,
                      const std::map<uint32_t, uint32_t>& holes,
                      const std::map<uint32_t, uint32_t>& data_records,
                      const std::map<uint32_t, uint32_t>& nop_records,
                      int64_t start_offset,
                      bool has_close_record, bool has_free_record,
                      common::Buffer* result) {
        int32_t records_length = 0;
        for (const auto& [off, len] : holes) {
            records_length += len;
        }
        for (const auto& [off, len] : data_records) {
            records_length += len;
        }
        for (const auto& [off, len] : nop_records) {
            records_length += len;
        }
        if (has_close_record) {
            records_length += 4096;
        }
        if (has_free_record) {
            records_length += 4096;
        }

        result->reset(records_length, 4096);
        memset(result->buf(), 0, records_length);

        for (const auto& [off, len] : holes) {
            common::Buffer record;
            make_hole(len, &record);
            memcpy(result->buf() + off - start_offset, record.buf(), record.size());
        }
        for (const auto& [off, len] : data_records) {
            ASSERT_TRUE(len >= 8192);
            common::Buffer record;
            make_data_record(len - ALIGN_SIZE, DEFAULT_VBID, &record);
            memcpy(result->buf() + off - start_offset, record.buf(), record.size());
        }
        for (const auto& [off, len] : nop_records) {
            common::Buffer record;
            make_nop_record(zone_id, len, &record);
            memcpy(result->buf() + off - start_offset, record.buf(), record.size());
        }
        if (has_close_record) {
            common::Buffer record;
            make_close_record(zone_id, zone_size, &record);
            memcpy(result->buf() + records_length - 4096 - (has_free_record ? 4096 : 0), record.buf(), record.size());
        }
        if (has_free_record) {
            common::Buffer record;
            make_free_record(zone_id, zone_size, &record);
            memcpy(result->buf() + records_length - 4096, record.buf(), record.size());
        }
    }

protected:
    std::shared_ptr<rocksdb::DB> _db;
    DeviceOpenOptions _opt;
    std::atomic<uint64_t> _seq_no{(uint64_t)base::gettimeofday_us()};
    ZoneRecordIndexerPtr _indexer;
    std::shared_ptr<ZoneMetaDBIndexer> _zone_indexer;
    std::unique_ptr<ZoneRecordSerializer> _record_serializer;
    std::string _disk_path;
    std::string _rocksdb_path;
};

class ZoneDiskAgentTestHelper {
public:
    ZoneDiskAgentTestHelper() {
        _zone_device_helper = std::make_shared<FileZoneDeviceBackendTestHelper>();
    }

    auto create_disk_agent() {
        aries::pb::DiskConfigure conf;
        conf.set_disk_id(0);
        conf.set_disk_path(".");
        auto* disk_agent = new DiskAgent(NULL, NULL, conf);

        auto dev = _zone_device_helper->create_open_dev();
        _dev = dev.get();

        disk_agent->_device = std::move(dev);
        disk_agent->_db = _zone_device_helper->_db;
        disk_agent->_vlet_manager = new VletManager;
        disk_agent->_zone_disk = std::make_unique<ZoneDisk>(_dev, _zone_device_helper->_db, disk_agent);
        disk_agent->_zone_disk->open();
        disk_agent->_vlet_indexer = std::make_unique<VletIndexer>(_zone_device_helper->_db, true);
        disk_agent->_zone_device_type = ZoneDeviceType::FILE;
        disk_agent->_bg_executor = std::make_unique<folly::CPUThreadPoolExecutor>(FLAGS_bg_threadpool_thread_num);

        FLAGS_is_open_ioprio = true;
        FLAGS_enable_index_check = false;
        FLAGS_enable_cache = false;

        disk_agent->_is_used = true;

        return disk_agent;
    }

    PutContext* new_put_context(aries::pb::ShardPutRequest *req,
                                       aries::pb::ShardPutResponse *res, baidu::rpc::Controller *cntl = NULL) {
        PutContext* ctx = new PutContext();
        ctx->request = req;
        ctx->response = res;
        ctx->cntl = cntl;
        ctx->en_queue_time = base::gettimeofday_ms();
        ctx->en_queue_time_us = base::gettimeofday_us();
        ctx->timeout_ms = 2000;
        ctx->vid = req->volume_id();
        ctx->vbid = req->vbid();
        return ctx;
    }

    GetContext* new_get_context(aries::pb::ShardGetRequest *req,
                                       aries::pb::ShardGetResponse *res, baidu::rpc::Controller *cntl = NULL) {
        GetContext* ctx = new GetContext();
        ctx->request = req;
        ctx->response = res;
        ctx->cntl = cntl;
        ctx->en_queue_time = base::gettimeofday_ms();
        ctx->en_queue_time_us = base::gettimeofday_us();
        ctx->timeout_ms = 2000;
        ctx->vid = req->volume_id();
        ctx->vbid = req->vbid();
        return ctx;
    }

    int put_record(ZoneVlet* vlet, uint64_t vid, uint64_t vbid, int64_t length) {
        aries::pb::ShardPutRequest put_req;
        aries::pb::ShardPutResponse put_res;
        baidu::rpc::Controller cntl;
        put_req.set_volume_id(vid);
        put_req.set_vbid(vbid);
        put_req.set_shard_index(g_shard_index);

        auto data = BufferGen::gen(length);
        auto data_crc = common::build_crc(data);

        aries::pb::ShardMeta* shard_meta = put_req.mutable_shard_meta();
        shard_meta->set_shard_len(length);
        shard_meta->set_shard_crc(data_crc);
        shard_meta->set_blob_len(length * 4);
        shard_meta->set_blob_crc(data_crc);

        auto& attachment_data = cntl.request_attachment();
        attachment_data.append(data);

        auto* context = new_put_context(&put_req, &put_res, &cntl);
        int ret = vlet->put(context);
        if (ret != 0) {
            return put_res.status().code();
        }
        return 0;
    }

    int get_record(ZoneVlet* vlet, uint64_t vid, uint64_t vbid, base::IOBuf* data, aries::pb::Status* status = nullptr) {
        aries::pb::ShardGetRequest get_req;
        aries::pb::ShardGetResponse get_res;
        baidu::rpc::Controller cntl;;

        get_req.set_volume_id(vid);
        get_req.set_vbid(vbid);
        get_req.set_shard_index(g_shard_index);
        get_req.set_need_data(true);
        get_req.set_need_meta(true);

        auto* context = new_get_context(&get_req, &get_res, &cntl);
        static int i = 0;
        i++;

        if (i % 3 == 0) {
            context->priority = aries::VERY_LOW;
        } else if (i % 3 == 1) {
            context->priority = aries::MIDDLE;
        } else {
            context->priority = aries::HIGHEST;
        }

        int ret = 0;
        if (i % 2 == 0) {
            ret = vlet->get(context);
            if (status != nullptr) {
                *status = get_res.status();
            }
        } else {
            ret = vlet->get_r(context);
            if (status != nullptr) {
                *status = get_res.status();
            }
        }

        if (ret != 0) {
            return ret;
        }
        LOG(NOTICE) << "get data succ, data_length:" << cntl.response_attachment().size();

        *data = cntl.response_attachment();

        return AIE_OK;
    }

    int get_record(ZoneVlet* vlet, uint64_t vid, uint64_t vbid, base::IOBuf* data, bool need_data, bool need_meta) {
        aries::pb::ShardGetRequest get_req;
        aries::pb::ShardGetResponse get_res;
        baidu::rpc::Controller cntl;;

        get_req.set_volume_id(vid);
        get_req.set_vbid(vbid);
        get_req.set_shard_index(g_shard_index);
        get_req.set_need_data(need_data);
        get_req.set_need_meta(need_meta);

        auto* context = new_get_context(&get_req, &get_res, &cntl);
        static int i = 0;
        i++;
        int ret = 0;
        if (i % 2 == 0) {
            ret = vlet->get(context);
        } else {
            ret = vlet->get_r(context);
        }

        if (ret != 0) {
            ret = get_res.status().code();
            return ret;
        }

        *data = cntl.response_attachment();

        return AIE_OK;
    }

    int get_record_for_check(ZoneVlet* vlet, uint64_t vid, uint64_t vbid) {
        int ret = vlet->get_for_check(vbid);
        return ret;
    }

    int range_get_record(ZoneVlet* vlet, uint64_t vid, uint64_t vbid,
                         uint64_t offset, uint64_t length,
                         base::IOBuf* data, aries::pb::Status* status = nullptr, bool fast_range = false) {
        aries::pb::ShardGetRequest get_req;
        aries::pb::ShardGetResponse get_res;
        baidu::rpc::Controller cntl;;

        get_req.set_volume_id(vid);
        get_req.set_vbid(vbid);
        get_req.set_shard_index(g_shard_index);
        get_req.set_need_data(true);
        get_req.set_need_meta(true);
        get_req.set_offset(offset);
        get_req.set_len(length);
        get_req.set_fast_range_get(fast_range);

        auto* context = new_get_context(&get_req, &get_res, &cntl);
        int ret = vlet->get(context);
        if (status != nullptr) {
            *status = get_res.status();
        }

        if (ret != 0) {
            return ret;
        }
        LOG(NOTICE) << "get data succ, data_length:" << cntl.response_attachment().size();

        *data = cntl.response_attachment();

        return AIE_OK;
    }

    RemoveContext *new_remove_ctx(aries::pb::ShardRemoveRequest *req,
                                         aries::pb::AckResponse *res) {
        RemoveContext* ctx = new RemoveContext();
        ctx->request = req;
        ctx->response = res;
        ctx->en_queue_time = base::gettimeofday_ms();
        ctx->en_queue_time_us = base::gettimeofday_us();
        ctx->timeout_ms = 2000;
        return ctx;
    }

    RestoreContext *new_restore_ctx(aries::pb::ShardRestoreRequest *req,
                                           aries::pb::AckResponse *res) {
        RestoreContext* ctx = new RestoreContext();
        ctx->request = req;
        ctx->response = res;
        ctx->en_queue_time = base::gettimeofday_ms();
        ctx->en_queue_time_us = base::gettimeofday_us();
        ctx->timeout_ms = 2000;
        return ctx;
    }

    int remove_record(ZoneVlet* vlet, uint64_t vid, uint64_t vbid, bool mark_delete, bool force = false) {
        aries::pb::ShardRemoveRequest remove_req;
        aries::pb::AckResponse remove_res;

        remove_req.set_volume_id(vid);
        remove_req.set_vbid(vbid);
        remove_req.set_shard_index(g_shard_index);
        remove_req.set_need_mark_delete(mark_delete);
        remove_req.set_force(force);

        auto* context = new_remove_ctx(&remove_req, &remove_res);
        int ret = vlet->remove(context);
        if (ret != 0) {
            return remove_res.status().code();
        }

        return AIE_OK;
    }

    int restore_record(ZoneVlet* vlet, uint64_t vid, uint64_t vbid) {
        aries::pb::ShardRestoreRequest restore_req;
        aries::pb::AckResponse remove_res;

        restore_req.set_volume_id(vid);
        restore_req.set_vbid(vbid);
        restore_req.set_shard_index(g_shard_index);

        auto* context = new_restore_ctx(&restore_req, &remove_res);
        int ret = vlet->restore(context);
        if (ret != 0) {
            return remove_res.status().code();
        }

        return AIE_OK;
    }

    void put_indexer_location(uint64_t vid, uint64_t vbid, const ZoneRecordLocation& location) {
        ZoneRecordDBIndexer indexer(vid, g_shard_index, _zone_device_helper->_db);

        ZoneRecordIndexEntry index_entry;
        index_entry.location = location;

        static int i = 0;

        Status s = indexer.put(vbid, index_entry);
        i++;
        ASSERT_TRUE(s.ok());
    }

    void modify_indexer_location(uint64_t vid, uint64_t vbid, const ZoneRecordLocation& location) {
        ZoneRecordDBIndexer indexer(vid, g_shard_index, _zone_device_helper->_db);

        ZoneRecordIndexEntry index_entry;
        index_entry.location = location;

        static int i = 0;

        Status s;
        if (i % 3 == 0) {
            s = indexer.update_location(*_zone_device_helper->_db, ZoneRecordIndexKey(vid, vbid, g_shard_index), index_entry);
        } else if (i % 3 == 1){
            s = indexer.update(vbid, index_entry);
        } else {
            s = indexer.put(vbid, index_entry);
        }
        i++;
        ASSERT_TRUE(s.ok());
    }

    void get_indexer_location(uint64_t vid, uint64_t vbid, ZoneRecordLocation* location) {
        ZoneRecordDBIndexer indexer(vid, g_shard_index, _zone_device_helper->_db);

        ZoneRecordIndexEntry index_entry;
        auto status = indexer.get(vbid, &index_entry);
        LOG(NOTICE) << "get location" << status;
        ASSERT_TRUE(status.ok());
        *location = index_entry.location;
    }

    std::shared_ptr<FileZoneDeviceBackendTestHelper> _zone_device_helper;
    MemFileZoneDeviceBackend* _dev;
};

}
}
