// Copyright (c) 2016 Baidu.com, Inc. All Rights Reserved
// Author ch<PERSON><PERSON><PERSON><PERSON>(chen<PERSON><PERSON><EMAIL>)
// Date: Wed Nov  9 20:14:03 CST 2016

#ifndef BAIDU_INF_ARIES_DATANODE_TEST_RPC_H
#define BAIDU_INF_ARIES_DATANODE_TEST_RPC_H

#include "base/endpoint.h"
#include "baidu/rpc/server.h"
#include "baidu/rpc/channel.h"
#include "baidu/inf/aries-api/common/proto/datanode.pb.h"
#include "baidu/inf/aries-api/common/closure.h"

namespace aries {
namespace datanode {

class Rpc {
public:
    Rpc(base::EndPoint endpoint) : _end_point(endpoint) {}

    int balance_vlet(const ::aries::pb::BalanceVletRequest* request,
            ::aries::pb::AckResponse* response) {
        int code = talk_with_node<aries::pb::BalanceVletRequest,
        aries::pb::AckResponse, aries::pb::DataNodeControlService_Stub>(
                &aries::pb::DataNodeControlService_Stub::balance_vlet,
                request,
                response,
                "balance_vlet"
                );
        return code;
    }

    int create_vlet(const ::aries::pb::CreateVletRequest* request,
            ::aries::pb::AckResponse* response) {
        int code = talk_with_node_and_wait_done<aries::pb::CreateVletRequest,
        aries::pb::AckResponse, aries::pb::DataNodeControlService_Stub>(
                &aries::pb::DataNodeControlService_Stub::create_vlet,
                request,
                response,
                "create_vlet"
                );
        return code;
    }

    int add_vlet(const ::aries::pb::AddVletRequest* request,
            ::aries::pb::AckResponse* response) {
        int code = talk_with_node<aries::pb::AddVletRequest,
        aries::pb::AckResponse, aries::pb::DataNodeControlService_Stub>(
                &aries::pb::DataNodeControlService_Stub::add_vlet,
                request,
                response,
                "add_vlet"
                );
        return code;
    }

    int drop_vlet(const ::aries::pb::DropVletRequest* request,
            ::aries::pb::AckResponse* response) {
        int code = talk_with_node<aries::pb::DropVletRequest,
        aries::pb::AckResponse, aries::pb::DataNodeControlService_Stub>(
                &aries::pb::DataNodeControlService_Stub::drop_vlet,
                request,
                response,
                "drop_vlet"
                );
        return code;
    }

    int drop_disk(const ::aries::pb::DropDiskRequest* request,
            ::aries::pb::AckResponse* response) {
        int code = talk_with_node<aries::pb::DropDiskRequest,
        aries::pb::AckResponse, aries::pb::DataNodeControlService_Stub>(
                &aries::pb::DataNodeControlService_Stub::drop_disk,
                request,
                response,
                "drop_disk"
                );
        return code;
    }

    int add_disk(const ::aries::pb::AddDiskRequest* request,
            ::aries::pb::AckResponse* response) {
        int code = talk_with_node<aries::pb::AddDiskRequest,
        aries::pb::AckResponse, aries::pb::DataNodeControlService_Stub>(
                &aries::pb::DataNodeControlService_Stub::add_disk,
                request,
                response,
                "add_disk"
                );
        return code;
    }

    int disk_join(const ::aries::pb::AddDiskWithDataRequest* request,
            ::aries::pb::AckResponse* response) {
        int code = talk_with_node<aries::pb::AddDiskWithDataRequest,
        aries::pb::AckResponse, aries::pb::DataNodeControlService_Stub>(
                &aries::pb::DataNodeControlService_Stub::disk_join,
                request,
                response,
                "disk_join"
                );
        return code;
    }

    int join_node(const ::aries::pb::JoinNodeRequest* request,
            ::aries::pb::AckResponse* response) {
        int code = talk_with_node<aries::pb::JoinNodeRequest,
        aries::pb::AckResponse, aries::pb::DataNodeControlService_Stub>(
                &aries::pb::DataNodeControlService_Stub::join_node,
                request,
                response,
                "disk_join"
                );
        return code;
    }

    int copy_linked_vlet(const ::aries::pb::CopyLinkedVletRequest* request,
            ::aries::pb::CopyLinkedVletResponse* response, std::string* data) {
        int code = talk_with_node<aries::pb::CopyLinkedVletRequest,
        aries::pb::CopyLinkedVletResponse, aries::pb::DataNodeDataService_Stub>(
                &aries::pb::DataNodeDataService_Stub::copy_linked_vlet,
                request,
                response,
                "copy_linked_vlet",
                data
                );
        return code;
    }

    int copy_append_vlet(const ::aries::pb::CopyAppendVletRequest* request,
            ::aries::pb::CopyAppendVletResponse* response, std::string* data) {
        int code = talk_with_node<pb::CopyAppendVletRequest,
        pb::CopyAppendVletResponse, pb::DataNodeDataService_Stub>(
                &pb::DataNodeDataService_Stub::copy_append_vlet,
                request,
                response,
                "copy_append_vlet",
                data
                );
        return code;
    }

    int put(const ::aries::pb::ShardPutRequest* request,
            ::aries::pb::ShardPutResponse* response, std::string* data) {
        int code = talk_with_node<aries::pb::ShardPutRequest,
        aries::pb::ShardPutResponse, aries::pb::DataNodeDataService_Stub>(
                &aries::pb::DataNodeDataService_Stub::put,
                request,
                response,
                "put",
                data
                );
        return code;
    }

    int update_meta(const ::aries::pb::ShardUpdateMetaRequest* request,
            ::aries::pb::AckResponse* response) {
        int code = talk_with_node<aries::pb::ShardUpdateMetaRequest,
        aries::pb::AckResponse, aries::pb::DataNodeDataService_Stub>(
                &aries::pb::DataNodeDataService_Stub::update_meta,
                request,
                response,
                "update_meta");
        return code;
    }

    int get(const ::aries::pb::ShardGetRequest* request,
            ::aries::pb::ShardGetResponse* response, std::string* data) {
        int code = talk_with_node<aries::pb::ShardGetRequest,
        aries::pb::ShardGetResponse, aries::pb::DataNodeDataService_Stub>(
                &aries::pb::DataNodeDataService_Stub::get,
                request,
                response,
                "get",
                data
                );
        return code;
    }

    int batch_get(const ::aries::pb::ShardBatchGetRequest* request,
                ::aries::pb::ShardBatchGetResponse* response, std::string* data) {
        int code = talk_with_node<aries::pb::ShardBatchGetRequest,
        aries::pb::ShardBatchGetResponse, aries::pb::DataNodeDataService_Stub>(
                &aries::pb::DataNodeDataService_Stub::batch_get,
                request,
                response,
                "batch_get",
                data
                );
        return code;
    }

    int get_record_index_info(const ::aries::pb::GetRecordIndexInfoRequest* request,
            ::aries::pb::GetRecordIndexInfoResponse* response) {
        int code = talk_with_node<aries::pb::GetRecordIndexInfoRequest,
        aries::pb::GetRecordIndexInfoResponse, aries::pb::DataNodeDataService_Stub>(
                &aries::pb::DataNodeDataService_Stub::get_record_index_info,
                request,
                response,
                "get_record_index_info");
        return code;
    }

    int remove(const ::aries::pb::ShardRemoveRequest* request,
            ::aries::pb::AckResponse* response) {
        int code = talk_with_node<aries::pb::ShardRemoveRequest,
        aries::pb::AckResponse, aries::pb::DataNodeDataService_Stub>(
                &aries::pb::DataNodeDataService_Stub::remove,
                request,
                response,
                "remove"
                );
        return code;
    }

    int restore(const ::aries::pb::ShardRestoreRequest* request,
            ::aries::pb::AckResponse* response) {
        int code = talk_with_node<aries::pb::ShardRestoreRequest,
        aries::pb::AckResponse, aries::pb::DataNodeDataService_Stub>(
                &aries::pb::DataNodeDataService_Stub::restore,
                request,
                response,
                "restore"
                );
        return code;
    }

    int purge_vlet(const ::aries::pb::PurgeVletRequest* request,
        ::aries::pb::AckResponse* response) {
        int code = talk_with_node<aries::pb::PurgeVletRequest,
            aries::pb::AckResponse, aries::pb::DataNodeDataService_Stub>(
                &aries::pb::DataNodeDataService_Stub::purge_vlet,
                request,
                response,
                "purge_vlet"
                );
        return code;
    }

    int list_finger_print(const ::aries::pb::ListFingerPrintRequest* request,
            ::aries::pb::ListFingerPrintResponse* response) {
        int code = talk_with_node<aries::pb::ListFingerPrintRequest,
        aries::pb::ListFingerPrintResponse, aries::pb::DataNodeDataService_Stub>(
                &aries::pb::DataNodeDataService_Stub::list_finger_print,
                request,
                response,
                "list_fingger"
                );
        return code;
    }

    int list_index(const ::aries::pb::ListIndexRequest* request,
            ::aries::pb::ListIndexResponse* response) {
        int code = talk_with_node<aries::pb::ListIndexRequest,
        aries::pb::ListIndexResponse, aries::pb::DataNodeDataService_Stub>(
                &aries::pb::DataNodeDataService_Stub::list_index,
                request,
                response,
                "list_index"
                );
        return code;
    }

    int get_location(const ::aries::pb::GetLocationRequest* request,
            ::aries::pb::GetLocationResponse* response) {
        int code = talk_with_node<aries::pb::GetLocationRequest,
        aries::pb::GetLocationResponse, aries::pb::DataNodeDataService_Stub>(
                &aries::pb::DataNodeDataService_Stub::get_location,
                request,
                response,
                "get_location"
                );
        return code;
    }

    int get_node_info(const ::aries::pb::GetNodeInfoRequest* request,
            ::aries::pb::GetNodeInfoResponse* response) {
        int code = talk_with_node<aries::pb::GetNodeInfoRequest,
        aries::pb::GetNodeInfoResponse, aries::pb::DataNodeDataService_Stub>(
                &aries::pb::DataNodeDataService_Stub::get_node_info,
                request,
                response,
                "get_vlet"
                );
        return code;
    }

    int get_vlet_info(const ::aries::pb::GetVletInfoRequest* request,
            ::aries::pb::GetVletInfoResponse* response) {
        int code = talk_with_node<aries::pb::GetVletInfoRequest,
        aries::pb::GetVletInfoResponse, aries::pb::DataNodeDataService_Stub>(
                &aries::pb::DataNodeDataService_Stub::get_vlet_info,
                request,
                response,
                "get_vlet"
                );
        return code;
    }

    int list_vlet_info(const ::aries::pb::ListVletInfoRequest* request,
            ::aries::pb::ListVletInfoResponse* response) {
        int code = talk_with_node<aries::pb::ListVletInfoRequest,
        aries::pb::ListVletInfoResponse, aries::pb::DataNodeDataService_Stub>(
                &aries::pb::DataNodeDataService_Stub::list_vlet_info,
                request,
                response,
                "list_vlet"
                );
        return code;
    }

    virtual int update_membership(const ::aries::pb::UpdateMembershipRequest* request,
                       ::aries::pb::AckResponse* response) {
        int code = talk_with_node<aries::pb::UpdateMembershipRequest,
        aries::pb::AckResponse, aries::pb::DataNodeControlService_Stub>(
                &aries::pb::DataNodeControlService_Stub::update_membership,
                request,
                response,
                "update_membership"
                );
        return code;
    }

    virtual int show_disk(const ::aries::pb::ShowDiskRequest* request,
                       ::aries::pb::ShowDiskResponse* response) {
        int code = talk_with_node<aries::pb::ShowDiskRequest,
        aries::pb::ShowDiskResponse, aries::pb::DataNodeControlService_Stub>(
                &aries::pb::DataNodeControlService_Stub::show_disk,
                request,
                response,
                "show_disk"
                );
        return code;
    }

    virtual int update_disk(const ::aries::pb::UpdateDiskRequest* request,
                       ::aries::pb::AckResponse* response) {
        _end_point.port = _end_point.port - 1;  // control port
        int code = talk_with_node<aries::pb::UpdateDiskRequest,
        aries::pb::AckResponse, aries::pb::DataNodeControlService_Stub>(
                &aries::pb::DataNodeControlService_Stub::update_disk,
                request,
                response,
                "update_disk"
                );
        _end_point.port = _end_point.port + 1;
        return code;
    }

    virtual int rewrite_vlet(const ::aries::pb::RewriteVletRequest* request,
                       ::aries::pb::AckResponse* response) {
        _end_point.port = _end_point.port - 1;  // control port
        int code = talk_with_node<aries::pb::RewriteVletRequest,
        aries::pb::AckResponse, aries::pb::DataNodeControlService_Stub>(
                &aries::pb::DataNodeControlService_Stub::rewrite_vlet,
                request,
                response,
                "rewrite_vlet"
                );
        _end_point.port = _end_point.port + 1;
        return code;
    }

    virtual int drop_cache_storage(const ::aries::pb::DropCacheStorageRequest* request,
                       ::aries::pb::AckResponse* response) {
        int code = talk_with_node<aries::pb::DropCacheStorageRequest,
        aries::pb::AckResponse, aries::pb::DataNodeControlService_Stub>(
                &aries::pb::DataNodeControlService_Stub::drop_cache_storage,
                request,
                response,
                "drop_cache_storage"
                );
        return code;
    }

protected:
    template <typename Request, typename Response, typename Stub>
    int talk_with_node(void (Stub::*method)(::google::protobuf::RpcController* controller,
            const Request*, Response*, ::google::protobuf::Closure* done),
            const Request* request,
            Response* response,
            const char* name,
            std::string* data = nullptr) {
        return talk_with_node_impl(method, request, response, name, false, data);
    }

    template <typename Request, typename Response, typename Stub>
    int talk_with_node_and_wait_done(void (Stub::*method)(::google::protobuf::RpcController* controller,
            const Request*, Response*, ::google::protobuf::Closure* done),
            const Request* request,
            Response* response,
            const char* name,
            std::string* data = nullptr) {
        return talk_with_node_impl(method, request, response, name, true, data);
    }

private:
    template <typename Request, typename Response, typename Stub>
    int talk_with_node_impl(void (Stub::*method)(::google::protobuf::RpcController* controller,
            const Request*, Response*, ::google::protobuf::Closure* done),
            const Request* request,
            Response* response,
            const char* name,
            bool wait_done,
            std::string* data) {
        baidu::rpc::Channel channel;

        if (channel.Init(_end_point, nullptr) != 0) {
            LOG(ERROR) << "fail to initialize channel, for EndPoint=" << _end_point;
            return -1;
        }

        Stub stub(&channel);

        baidu::rpc::Controller cntl;
        cntl.set_log_id(base::fast_rand());
        cntl.set_timeout_ms(1000000);

        if ((std::string(name) == std::string("put")) && data) {
            cntl.request_attachment().append(*data);
        } else if (data) {
            data->clear();
        }

        SynchronizedClosure _closure;
        SynchronizedClosure* closure = wait_done ? &_closure : nullptr;

        (stub.*method)(&cntl, request, response, closure);
        if (cntl.Failed()) {
            LOG(NOTICE) << name << " talk_with_node " << _end_point << ",log_id="
                << cntl.log_id() << " rpc failed, msg=" << cntl.ErrorText();
            return -1;
        }

        auto st = response->status();
        LOG(NOTICE) << name << " talk_with_node " << _end_point << ",log_id="
            << cntl.log_id() << " rpc success, code=" << st.code() << ",msg=" << st.msg();

        if ((std::string(name) == std::string("get")
                    || std::string(name) == std::string("copy_linked_vlet")
                    || std::string(name) == std::string("batch_get")) && data) {
            *data = cntl.response_attachment().to_string();
        }

        if (closure != nullptr) {
            closure->wait();
            LOG(NOTICE) << name << " talk_with_node " << _end_point << ",log_id="
                << cntl.log_id() << " wait done success";
        }
        return 0;
    }

private:
    base::EndPoint          _end_point;
};

} // end namespace of datanode
} // end namespace of aries

#endif //#ifndef BAIDU_INF_ARIES_DATANODE_TEST_RPC_H

/* vim: set ts=4 sw=4 sts=4 tw=100 */
