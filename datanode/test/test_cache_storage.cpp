#include <unistd.h>
#include <random>
#include <base/crc32c.h>
#include <gtest/gtest.h>
#include "bmock.h"

#include "baidu/inf/aries-api/common/utils.h"
#include "baidu/inf/aries/common/aries_ut_pread.h"
#include "baidu/inf/aries/common/bmock_util.h"
#include "baidu/inf/aries/common/bit_map_manager.h"
#include "baidu/inf/aries/common/aries_ut_pread.h"
#include "baidu/inf/aries/datanode/copy_vlet.h"
#include "baidu/inf/aries/datanode/storage/random/linked_shard_record.h"
#include "baidu/inf/aries/datanode/cache/storage/cache_linked_storage.h"

namespace aries {
namespace datanode {
using ::testing::Return;
using ::testing::DoAll;
using ::testing::SetArgPointee;
using ::testing::_;
using ::testing::SetArrayArgument;

MAKE_BMOCK_NS_CLASS_METHOD(4, aries::datanode, CacheLinkedStoreMemIndexer, get,
        Status(uint64_t, uint64_t, RecordLocation*, CacheRecordLocationExtraInfo*));
MAKE_BMOCK_NS_CLASS_METHOD(1, aries::datanode, CacheLinkedStoreMemIndexer, remove,
        Status(const BlobId&));
MAKE_BMOCK_NS_CLASS_METHOD(3, aries::datanode, CacheLinkedStoreMemIndexer, put,
        Status(const BlobId&, const RecordLocation&, CacheRecordLocationExtraInfo*));

MAKE_BMOCK_NS_CLASS_METHOD(4, aries::datanode, CacheLinkedStorage, read_linked_record,
        Status(const RecordLocation&, bool, char*, const BlobId&));
MAKE_BMOCK_NS_CLASS_METHOD(2, aries::datanode, CacheLinkedStorage, write_shard_record,
        Status(const RecordLocation&, const char*));
MAKE_BMOCK_NS_CLASS_METHOD(2, aries::datanode, CacheLinkedStorage, move_tail_record_to_fill,
        Status(const BlobId&, const RecordLocation&));
MAKE_BMOCK_NS_CLASS_METHOD(6, aries::datanode, CacheLinkedStorage, deserialize_record,
        Status(char*, uint64_t*, uint64_t*, aries::pb::ShardMeta*, base::IOBuf*, uint32_t*));
MAKE_BMOCK_NS_CLASS_METHOD(4, aries::datanode, CacheLinkedStorage, update_hole_info,
        Status(CacheLinkedStoreIndexer::WriteBatch*, uint32_t, BlobId, const CacheRecordLocationExtraInfo&));
MAKE_BMOCK_NS_CLASS_METHOD(2, aries::datanode, CacheLinkedStorage, fill_container_holes,
        Status(CacheLinkedStorage::FillHoleType, uint32_t));

MAKE_BMOCK_NS_CLASS_METHOD(2, aries::datanode, LinkedStore, read_shard_record_internal,
        Status(const RecordLocation &, char *));
MAKE_BMOCK_NS_CLASS_METHOD(3, aries::datanode, LinkedStore, write_shard_record_internal,
        Status(const RecordLocation&, const char*, const bool));
MAKE_BMOCK_NS_CLASS_METHOD(1, aries::datanode, LinkedStore, record_type_page_num,
        uint32_t(uint32_t));

MAKE_BMOCK_NS_CLASS_METHOD(3, aries::datanode, ShardRecord, deserialize_head,
        int(const char*, uint64_t, aries::pb::ShardMeta*));
MAKE_BMOCK_NS_CLASS_METHOD(4, aries::datanode, CacheLinkedStorage, validate_record,
        Status(uint32_t page_num, const char*, const uint32_t, const BlobId&));

BMOCK_NS_METHOD1(aries::datanode, create_access, int(const std::string&));

void storage_init_test_case_stat(CacheLinkedStorage &store, CacheStorageOptions &opt) {
    store._page_size = 4096;
    store._block_page_num = 1024;
    store._block_size = store._block_page_num * store._page_size;
    store._block_num = opt.storage_capacity / store._block_size;
    store._min_record_page_num = 3; // 12k
    store._max_record_page_num = 1024; // 4M
    store._record_gap_page_num = 4;
    store._max_record_type = 1 + common::calc_ceil(store._max_record_page_num - store._min_record_page_num,
                                    store._record_gap_page_num);
    store._min_record_size_for_fast_remove = (100 * 1024); // 100k
    store._max_holes_size_for_fast_remove = (20 * 1024 * 1024);  // 20M

    delete[] store._record_containers;
    delete[] store._record_container_hole_info;
    store._record_containers = new LinkedStore::RecordContainer[store._max_record_type + 1];
    store._record_container_hole_info = new CacheLinkedStorage::RecordContainerHoleInfo[store._max_record_type + 1];
    store._block_bitmap = common::BitMapManager();
    assert(0 == store._block_bitmap.init(store._block_num));
}

// Test CacheLinkedStorage.
// Much of the test cases come from linked_store_test.
class CacheStorageTest : public ::testing::Test {
protected:
    CacheStorageTest() = default;
    ~CacheStorageTest() = default;

    std::tuple<std::shared_ptr<CacheLinkedStorage>,
               std::shared_ptr<CacheMemDB>,
               CacheStorageOptions> make_store(bool permit_data_offset_index = true) {
        FLAGS_create_vlet_sleep_ms = 0;
        ::system("rm -rf  test_cache_647fbc9");
        ::system("mkdir  test_cache_647fbc9");
        CacheStorageOptions opt;
        opt.is_tmpfs = false;
        opt.disk_path = "test_cache_647fbc9";
        opt.file_prefix = "L_C";
        opt.storage_capacity = 4 * aries::common::GB;
        std::shared_ptr<CacheMemDB> db = std::make_shared<CacheMemDB>();
        std::shared_ptr<CacheLinkedStorage> store = std::make_shared<CacheLinkedStorage>();
        auto s = store->initialize(opt, db);
        assert(s.ok());

        storage_init_test_case_stat(*store, opt);
        store->_permit_data_offset_index = permit_data_offset_index;

        LOG(INFO) << "CacheLinkedStore created " << store->_filename;
        LOG(INFO) << "page_size " << store->_page_size;
        LOG(INFO) << "block_page_num " << store->_block_page_num;
        LOG(INFO) << "block_size " << store->_block_size;
        LOG(INFO) << "block_num " << store->_block_num;
        LOG(INFO) << "min_record_page_num " << store->_min_record_page_num;
        LOG(INFO) << "max_record_page_num " << store->_max_record_page_num;
        LOG(INFO) << "record_gap_page_num " << store->_record_gap_page_num;
        LOG(INFO) << "max_record_type " << store->_max_record_type;
        LOG(INFO) << "min_record_size_for_fast_remove " << store->_min_record_size_for_fast_remove;
        LOG(INFO) << "max_holes_size_for_fast_remove " << store->_max_holes_size_for_fast_remove;

        return {store, db, opt};
    }
};

TEST_F(CacheStorageTest, initialize) {
    auto [store, db, opt] = make_store(true);
    Status s;

    LOG(INFO) << "case: db is nullptr";
    s = store->initialize(opt, nullptr);
    ASSERT_EQ(s.code(), AIE_FAIL);

    LOG(INFO) << "case: create_access fail";
    EXPECT_CALL(BMOCK_NS_OBJECT(aries::datanode, create_access), create_access(_ ))
        .WillOnce(Return(-1)).WillRepeatedly(Return(0));
    s = store->initialize(opt, db);
    ASSERT_FALSE(s.ok());

    LOG(INFO) << "case: open fail";
    opt.disk_path = "/boot/";
    s = store->initialize(opt, db);
    ASSERT_FALSE(s.ok());
    opt.disk_path = "test_cache_647fbc9";

    LOG(INFO) << "case: preallocate_space fail";
    FLAGS_create_vlet_file_only_by_write = true;
    Interruptor interruptor(2012);
    interruptor.interrupt();
    store->_create_worker = &interruptor;
    s = store->initialize(opt, db);
    ASSERT_FALSE(s.ok());
    store->_create_worker = nullptr;

    s = store->initialize(opt, db);
    ASSERT_EQ(s.code(), AIE_OK);
    storage_init_test_case_stat(*store, opt);
    auto stat = store->get_storage_stat();
    ASSERT_EQ(stat.capacity, opt.storage_capacity);
    ASSERT_EQ(stat.free_size, stat.capacity);
    ASSERT_EQ(stat.record_count, 0);

    s = store->drop();
    ASSERT_EQ(s.code(), AIE_OK);
    ASSERT_EQ(store->_gate._stopped.load(), true);
    BMOCK_NS_STOP(aries::datanode, create_access, int(const std::string&));
}

TEST_F(CacheStorageTest, get_basic) {
    auto [store, db, opt] = make_store();
    Status s;
    aries::pb::ShardMeta meta;
    auto blob0 = BlobId{0, 0};
    auto blob1 = BlobId{1, 1};

    BMOCK_CLASS_MOCK_GUARD(CacheLinkedStoreMemIndexer, get);
    BMOCK_CLASS_MOCK_GUARD(CacheLinkedStorage, read_linked_record);
    BMOCK_CLASS_MOCK_GUARD(CacheLinkedStorage, deserialize_record);
    BMOCK_CLASS_MOCK_GUARD(LinkedStore, record_type_page_num);
    BMOCK_CLASS_MOCK_GUARD(ShardRecord, deserialize_head);

    LOG(INFO) << "case: gate closed";
    store->_gate._stopped.store(true);
    s = store->get(blob1, &meta, NULL);
    EXPECT_EQ(s.code(), AIE_FAIL);
    store->_gate._stopped.store(false);

    EXPECT_CALL(BMOCK_NS_CLASS_OBJECT(aries::datanode, LinkedStore, record_type_page_num),
            record_type_page_num(_)).WillRepeatedly(Return(0));

    LOG(INFO) << "case: get indexer AIE_REMOVED";
    EXPECT_CALL(BMOCK_NS_CLASS_OBJECT(aries::datanode, CacheLinkedStoreMemIndexer, get),
            get(_, _, _, _)).WillOnce(Return(Status(AIE_REMOVED)));
    s = store->get(blob1, &meta, NULL);
    EXPECT_EQ(s.code(), AIE_REMOVED);

    LOG(INFO) << "case: read_linked_record fail";
    EXPECT_CALL(BMOCK_NS_CLASS_OBJECT(aries::datanode, CacheLinkedStoreMemIndexer, get),
            get(_, _, _, _)).WillOnce(Return(Status(AIE_OK)));
    EXPECT_CALL(BMOCK_NS_CLASS_OBJECT(aries::datanode, CacheLinkedStorage, read_linked_record),
            read_linked_record(_, _, _, _)).WillOnce(Return(Status(AIE_IO_ERROR)));
    s = store->get(blob1, &meta, NULL);
    EXPECT_EQ(s.code(), AIE_IO_ERROR);

    LOG(INFO) << "case: read_linked_record & validate_record success, deserialize_record fail";
    EXPECT_CALL(BMOCK_NS_CLASS_OBJECT(aries::datanode, CacheLinkedStoreMemIndexer, get),
            get(_, _, _, _)).WillOnce(Return(Status(AIE_OK)));
    EXPECT_CALL(BMOCK_NS_CLASS_OBJECT(aries::datanode, CacheLinkedStorage, read_linked_record),
            read_linked_record(_, _, _, _)).WillOnce(Return(Status(AIE_OK)));
    EXPECT_CALL(BMOCK_NS_CLASS_OBJECT(aries::datanode, ShardRecord, deserialize_head),
            deserialize_head(_, _, _)).WillOnce(Return(-1));
    s = store->get(blob1, &meta, NULL);
    EXPECT_EQ(s.code(), AIE_CORRUPT);

    LOG(INFO) << "case: all steps success";
    EXPECT_CALL(BMOCK_NS_CLASS_OBJECT(aries::datanode, CacheLinkedStoreMemIndexer, get),
            get(_, _, _, _)).WillOnce(Return(Status(AIE_OK)));
    EXPECT_CALL(BMOCK_NS_CLASS_OBJECT(aries::datanode, CacheLinkedStorage, read_linked_record),
            read_linked_record(_, _, _, _)).WillOnce(Return(Status(AIE_OK)));
    EXPECT_CALL(BMOCK_NS_CLASS_OBJECT(aries::datanode, CacheLinkedStorage, deserialize_record),
            deserialize_record(_, _, _, _, _, _)).WillOnce(Return(0));
    s = store->get(blob0, &meta, NULL);
    EXPECT_EQ(s.code(), AIE_OK);

    LOG(INFO) << "case: vbid/vbid not match with index";
    EXPECT_CALL(BMOCK_NS_CLASS_OBJECT(aries::datanode, CacheLinkedStoreMemIndexer, get),
            get(_, _, _, _)).WillOnce(Return(Status(AIE_OK)));
    EXPECT_CALL(BMOCK_NS_CLASS_OBJECT(aries::datanode, CacheLinkedStorage, read_linked_record),
            read_linked_record(_, _, _, _)).WillOnce(Return(Status(AIE_OK)));
    EXPECT_CALL(BMOCK_NS_CLASS_OBJECT(aries::datanode, CacheLinkedStorage, deserialize_record),
            deserialize_record(_, _, _, _, _, _)).WillOnce(Return(0));
    s = store->get(blob1, &meta, NULL);
    EXPECT_EQ(s.code(), AIE_CORRUPT);
}

TEST_F(CacheStorageTest, remove_basic) {
    auto [store, db, opt] = make_store(true);
    BMOCK_CLASS_MOCK_GUARD(CacheLinkedStoreMemIndexer, get);
    BMOCK_CLASS_MOCK_GUARD(CacheLinkedStorage, move_tail_record_to_fill);
    BMOCK_CLASS_MOCK_GUARD(LinkedStore, record_type_page_num);

    Status s;
    auto blob0 = BlobId{0, 0};

    LOG(INFO) << "case: gate closed";
    store->_gate._stopped.store(true);
    s = store->remove(blob0);
    ASSERT_EQ(s.code(), AIE_FAIL);
    store->_gate._stopped.store(false);

    LOG(INFO) << "case: key is not exist/removed";
    EXPECT_CALL(BMOCK_NS_CLASS_OBJECT(aries::datanode, CacheLinkedStoreMemIndexer, get),
            get(_, _, _, _)).WillOnce(Return(Status(AIE_NOT_EXIST)));
    s = store->remove(blob0);
    EXPECT_EQ(s.code(), AIE_NOT_EXIST);

    LOG(INFO) << "case: index corruption";
    EXPECT_CALL(BMOCK_NS_CLASS_OBJECT(aries::datanode, CacheLinkedStoreMemIndexer, get),
            get(_, _, _, _)).WillOnce(Return(Status(AIE_FAIL)));
    s = store->remove(blob0);
    EXPECT_EQ(s.code(), AIE_FAIL);

    LOG(INFO) << "case: move_tail_record_to_fill fail";
    EXPECT_CALL(BMOCK_NS_CLASS_OBJECT(aries::datanode, CacheLinkedStoreMemIndexer, get),
            get(_, _, _, _)).WillOnce(Return(Status(AIE_OK)));
    EXPECT_CALL(BMOCK_NS_CLASS_OBJECT(aries::datanode, LinkedStore, record_type_page_num),
            record_type_page_num(_)).WillOnce(Return(0));
    EXPECT_CALL(BMOCK_NS_CLASS_OBJECT(aries::datanode, CacheLinkedStorage, move_tail_record_to_fill),
            move_tail_record_to_fill(_, _)).WillOnce(Return(Status(AIE_FAIL)));
    s = store->remove(blob0);
    EXPECT_EQ(s.code(), AIE_FAIL);
}

TEST_F(CacheStorageTest, move_linked_record) {
    auto [store, db, opt] = make_store(true);

    CacheRecordLocationExtraInfo extra_info;
    CacheRecordLocationExtraInfo extra_info_err;
    extra_info_err.set_record_data_offset(816233);

    RecordLocation loc;
    loc.record_type = 2;
    RecordLocation loc_err; // mock location inconsistency
    loc_err.record_type = 3;
    uint64_t vid = 0;
    uint64_t vbid = 0;
    CacheRecordLocationExtraInfo tmp;
    Status s;

    {
    LOG(INFO) << "case: read linked record fail";
    BMOCK_CLASS_MOCK_GUARD(LinkedStore, read_shard_record_internal);
    EXPECT_CALL(BMOCK_NS_CLASS_OBJECT(aries::datanode, LinkedStore, read_shard_record_internal),
            read_shard_record_internal(_, _)).WillOnce(Return(Status(AIE_FAIL)));
    s = store->move_linked_record(loc, loc, &vid, &vbid, &tmp);
    EXPECT_EQ(s.code(), AIE_FAIL);
    }

    {
    LOG(INFO) << "case: validate record fail";
    BMOCK_CLASS_MOCK_GUARD(LinkedStore, read_shard_record_internal);
    BMOCK_CLASS_MOCK_GUARD(CacheLinkedStorage, validate_record);
    EXPECT_CALL(BMOCK_NS_CLASS_OBJECT(aries::datanode, LinkedStore, read_shard_record_internal),
            read_shard_record_internal(_, _)).WillOnce(Return(Status(AIE_OK)));
    EXPECT_CALL(BMOCK_NS_CLASS_OBJECT(aries::datanode, CacheLinkedStorage, validate_record),
            validate_record(_, _, _, _)).WillOnce(Return(Status(AIE_FAIL)));
    s = store->move_linked_record(loc, loc, &vid, &vbid, &tmp);
    EXPECT_EQ(s.code(), AIE_FAIL);
    }

    {
    LOG(INFO) << "case: deserialize record fail";
    BMOCK_CLASS_MOCK_GUARD(CacheLinkedStorage, read_linked_record);
    BMOCK_CLASS_MOCK_GUARD(CacheLinkedStorage, deserialize_record);
    EXPECT_CALL(BMOCK_NS_CLASS_OBJECT(aries::datanode, CacheLinkedStorage, read_linked_record),
        read_linked_record(_, _, _, _)).WillOnce(Return(Status(AIE_OK)));
    EXPECT_CALL(BMOCK_NS_CLASS_OBJECT(aries::datanode, CacheLinkedStorage, deserialize_record),
        deserialize_record(_, _, _, _, _, _)).WillOnce(Return(Status(AIE_FAIL)));
    s = store->move_linked_record(loc, loc, &vid, &vbid, &tmp);
    EXPECT_EQ(s.code(), AIE_FAIL);
    }

    {
    LOG(INFO) << "case: index get fail";
    BMOCK_CLASS_MOCK_GUARD(CacheLinkedStorage, read_linked_record);
    BMOCK_CLASS_MOCK_GUARD(CacheLinkedStorage, deserialize_record);
    BMOCK_CLASS_MOCK_GUARD(CacheLinkedStoreMemIndexer, get);
    EXPECT_CALL(BMOCK_NS_CLASS_OBJECT(aries::datanode, CacheLinkedStorage, read_linked_record),
        read_linked_record(_, _, _, _)).WillOnce(Return(Status(AIE_OK)));
    EXPECT_CALL(BMOCK_NS_CLASS_OBJECT(aries::datanode, CacheLinkedStorage, deserialize_record),
        deserialize_record(_, _, _, _, _, _)).WillOnce(Return(Status(AIE_OK)));
    EXPECT_CALL(BMOCK_NS_CLASS_OBJECT(aries::datanode, CacheLinkedStoreMemIndexer, get),
            get(_, _, _, _)).WillOnce(Return(Status(AIE_FAIL)));
    s = store->move_linked_record(loc, loc, &vid, &vbid, &tmp);
    EXPECT_EQ(s.code(), AIE_FAIL);
    }

    {
    LOG(INFO) << "case: indexer get location inconsistency";
    BMOCK_CLASS_MOCK_GUARD(CacheLinkedStorage, read_linked_record);
    BMOCK_CLASS_MOCK_GUARD(CacheLinkedStorage, deserialize_record);
    BMOCK_CLASS_MOCK_GUARD(CacheLinkedStoreMemIndexer, get);
    EXPECT_CALL(BMOCK_NS_CLASS_OBJECT(aries::datanode, CacheLinkedStorage, read_linked_record),
        read_linked_record(_, _, _, _)).WillOnce(Return(Status(AIE_OK)));
    EXPECT_CALL(BMOCK_NS_CLASS_OBJECT(aries::datanode, CacheLinkedStorage, deserialize_record),
        deserialize_record(_, _, _, _, _, _)).WillOnce(Return(Status(AIE_OK)));
    EXPECT_CALL(BMOCK_NS_CLASS_OBJECT(aries::datanode, CacheLinkedStoreMemIndexer, get),
            get(_, _, _, _)).WillOnce(DoAll(SetArgPointee<2>(loc_err),
                SetArgPointee<3>(extra_info), Return(Status(AIE_OK))));
    s = store->move_linked_record(loc, loc, &vid, &vbid, &tmp);
    EXPECT_EQ(s.code(), AIE_RECORD_META_INCONSISTENT);
    }

    {
    LOG(INFO) << "case: indexer get data offset inconsistency";
    BMOCK_CLASS_MOCK_GUARD(CacheLinkedStorage, read_linked_record);
    BMOCK_CLASS_MOCK_GUARD(CacheLinkedStorage, deserialize_record);
    BMOCK_CLASS_MOCK_GUARD(CacheLinkedStoreMemIndexer, get);
    EXPECT_CALL(BMOCK_NS_CLASS_OBJECT(aries::datanode, CacheLinkedStorage, read_linked_record),
        read_linked_record(_, _, _, _)).WillOnce(Return(Status(AIE_OK)));
    EXPECT_CALL(BMOCK_NS_CLASS_OBJECT(aries::datanode, CacheLinkedStorage, deserialize_record),
        deserialize_record(_, _, _, _, _, _)).WillOnce(Return(Status(AIE_OK)));
    EXPECT_CALL(BMOCK_NS_CLASS_OBJECT(aries::datanode, CacheLinkedStoreMemIndexer, get),
            get(_, _, _, _)).WillOnce(DoAll(SetArgPointee<2>(loc),
                SetArgPointee<3>(extra_info_err), Return(Status(AIE_OK))));
    s = store->move_linked_record(loc, loc, &vid, &vbid, &tmp);
    EXPECT_EQ(s.code(), AIE_RECORD_META_INCONSISTENT);
    }

    {
    LOG(INFO) << "case: move_linked_record success";
    BMOCK_CLASS_MOCK_GUARD(CacheLinkedStoreMemIndexer, get);
    BMOCK_CLASS_MOCK_GUARD(CacheLinkedStorage, read_linked_record);
    BMOCK_CLASS_MOCK_GUARD(ShardRecord, deserialize_head);
    BMOCK_CLASS_MOCK_GUARD(LinkedStore, write_shard_record_internal);
    char* str = new char[4096];
    uint64_t *magic = reinterpret_cast<uint64_t*>(str + 4096 - sizeof(ShardRecordGuard));
    *magic = SHARD_RECORD_MAGIC;
    EXPECT_CALL(BMOCK_NS_CLASS_OBJECT(aries::datanode, CacheLinkedStorage, read_linked_record),
            read_linked_record(_, _, _, _)).WillOnce(DoAll(SetArrayArgument<2>(str, str + 4096), Return(Status(AIE_OK))));
    EXPECT_CALL(BMOCK_NS_CLASS_OBJECT(aries::datanode, ShardRecord, deserialize_head),
            deserialize_head(_, _, _)).WillOnce(Return(0));
    EXPECT_CALL(BMOCK_NS_CLASS_OBJECT(aries::datanode, LinkedStore, write_shard_record_internal),
            write_shard_record_internal(_, _, _)).WillOnce(Return(Status(AIE_OK)));
    EXPECT_CALL(BMOCK_NS_CLASS_OBJECT(aries::datanode, CacheLinkedStoreMemIndexer, get),
            get(_, _, _, _)).WillOnce(DoAll(SetArgPointee<2>(loc),
                SetArgPointee<3>(extra_info), Return(Status(AIE_OK))));
    s = store->move_linked_record(loc, loc, &vid, &vbid, &tmp);
    EXPECT_EQ(s.code(), AIE_OK);
    }
}

TEST_F(CacheStorageTest, move_deleted_record_to_hole) {
    auto [store, db, opt] = make_store(true);

    uint64_t record_bytes;
    uint64_t vid = 1;
    uint32_t shard_len = 2 * 1024 - sizeof(uint64_t); // record_type == 1
    std::unique_ptr<char []> shard_buffer(new char[shard_len]);
    aries::common::make_mock_data(shard_buffer.get(), shard_len);
    base::IOBuf data;
    data.append(shard_buffer.get(), shard_len);
    uint32_t shard_crc = base::crc32c::Value(shard_buffer.get(), shard_len);
    aries::pb::ShardMeta meta;
    meta.set_blob_len(shard_len);
    meta.set_blob_crc(shard_crc);
    meta.set_shard_len(shard_len);
    meta.set_shard_crc(shard_crc);
    Status s;

    ASSERT_EQ(1024, opt.storage_capacity / store->_block_size); // 4GB / 4MB = 1024 blocks
    s = store->put(BlobId{vid, 1}, meta, data, &record_bytes);
    EXPECT_EQ(s.ok(), true);
    EXPECT_EQ(store->_record_count, 1);
    EXPECT_EQ(store->_block_bitmap.free_pos_num(), 1023);
    s = store->put(BlobId{vid, 2}, meta, data, &record_bytes);
    EXPECT_EQ(s.ok(), true);
    EXPECT_EQ(store->_record_count, 2);
    EXPECT_EQ(store->_block_bitmap.free_pos_num(), 1023);
    s = store->put(BlobId{vid, 3}, meta, data, &record_bytes);
    EXPECT_EQ(s.ok(), true);
    EXPECT_EQ(store->_record_count, 3);
    EXPECT_EQ(store->_block_bitmap.free_pos_num(), 1023);

    auto tmp_min_record_size = store->_min_record_size_for_fast_remove.load();
    auto tmp_max_holes_size = store->_max_holes_size_for_fast_remove.load();
    store->_min_record_size_for_fast_remove = 0;
    store->_max_holes_size_for_fast_remove = 100 * 1024 * 1024;

    s = store->remove(BlobId{vid, 2});
    EXPECT_EQ(s.ok(), true);
    store->refresh_hole_info(1);
    EXPECT_EQ(store->_hole_record_num, 1);
    EXPECT_EQ(store->_block_bitmap.free_pos_num(), 1023);

    s = store->remove(BlobId{vid, 3});
    store->refresh_hole_info(1);
    EXPECT_EQ(s.ok(), true);
    EXPECT_EQ(store->_hole_record_num, 2);
    EXPECT_EQ(store->_block_bitmap.free_pos_num(), 1023);

    s = store->fill_container_holes(LinkedStore::FillHoleType::FREE_LAST_RECORD, 1);
    ASSERT_TRUE(s.ok());
    store->refresh_hole_info(1);
    EXPECT_EQ(s.ok(), true);
    EXPECT_EQ(store->_hole_record_num, 1);

    uint32_t normal_num = 0;
    uint32_t hole_num = 0;
    std::vector<std::pair<StorageItemKey, StorageItemRecordPtr>> index_items;
    store->_storage_indexer->travel([&](StorageItemKey key, StorageItemRecordPtr ptr) {
            index_items.push_back({key, ptr});
    });
    std::sort(index_items.begin(), index_items.end());
    ASSERT_EQ(index_items.size(), 2); // normal blob: {1}, hole {2};
    for (auto& kv : index_items) {
        auto& key = kv.first;
        auto& ptr = kv.second;
        auto& extra_info = ptr->extra_info;
        if (!extra_info.has_value()){
            normal_num++;
        } else {
            if (extra_info->has_prev_hole_blob_id()) {
                hole_num++;
            }
            if (extra_info->has_record_data_offset() && !extra_info->has_prev_hole_blob_id()) {
                normal_num++;
            }
        }
    }
    EXPECT_EQ(normal_num, 1);
    EXPECT_EQ(hole_num, store->_record_containers[1].hole_record_num);
    EXPECT_EQ(hole_num, 1);

    store->_min_record_size_for_fast_remove.store(tmp_min_record_size);
    store->_max_holes_size_for_fast_remove.store(tmp_max_holes_size);
}

TEST_F(CacheStorageTest, get_opt) {
    auto [store, db, opt] = make_store(true);

    uint32_t shard_len = 120 * 1024 - sizeof(uint64_t);  // 120K > min_record_size_for_fast_remove
    std::unique_ptr<char []> shard_buffer(new char[shard_len]);
    aries::common::make_mock_data(shard_buffer.get(), shard_len);
    base::IOBuf data;
    data.append(shard_buffer.get(), shard_len);
    uint32_t shard_crc = base::crc32c::Value(shard_buffer.get(), shard_len);
    uint64_t vid = 1;
    uint64_t vbid = 1;
    aries::pb::ShardMeta meta;
    meta.set_blob_len(shard_len);
    meta.set_blob_crc(shard_crc);
    meta.set_shard_len(shard_len);
    meta.set_shard_crc(shard_crc);
    CacheRecordLocationExtraInfo extra_info;
    base::IOBuf get_data;
    uint64_t record_bytes;
    RecordLocation location;

    Status s = store->put(BlobId{vid, vbid}, meta, data, &record_bytes);
    EXPECT_EQ(s.ok(), true);
    EXPECT_EQ(store->_record_count, 1);

    s = store->get(BlobId{vid, vbid}, &meta, &get_data);
    EXPECT_EQ(s.ok(), true);
    EXPECT_EQ(build_crc(get_data), shard_crc);

    s = store->remove(BlobId{vid, vbid});
    EXPECT_EQ(s.ok(), true);
    EXPECT_EQ(store->_record_count, 0);

    s = store->get(BlobId{vid, vbid}, &meta, &data);
    EXPECT_EQ(s.code(), AIE_BLOB_NOT_EXIST);
    store->_storage_indexer->get(BlobId{vid, vbid}, &location, &extra_info);
    EXPECT_EQ(extra_info.has_prev_hole_blob_id(), true);
    store->refresh_hole_info(1);
    EXPECT_EQ(store->get_storage_stat().free_size, (uint64_t)4 * 1024 * 1024 * 1024);
}

TEST_F(CacheStorageTest, put_opt) {
    auto [store, db, opt] = make_store(true);

    //
    // no remove opt
    // put -> remove -> put
    store->_min_record_size_for_fast_remove = 100 * 1024;
    store->_max_holes_size_for_fast_remove = 1024*1024*1024;

    // make mock data
    uint64_t record_bytes;
    Status s;
    RecordLocation location;
    CacheRecordLocationExtraInfo extra_info;
    uint32_t shard_len = 2 * 1024 - sizeof(uint64_t);  // 2K < min_record_size_for_fast_remove
    std::unique_ptr<char []> shard_buffer(new char[shard_len]);
    aries::common::make_mock_data(shard_buffer.get(), shard_len);
    base::IOBuf data;
    data.append(shard_buffer.get(), shard_len);
    uint32_t shard_crc = base::crc32c::Value(shard_buffer.get(), shard_len);
    aries::pb::ShardMeta meta;
    meta.set_blob_len(shard_len);
    meta.set_blob_crc(shard_crc);
    meta.set_shard_len(shard_len);
    meta.set_shard_crc(shard_crc);
    auto blob1 = BlobId{1, 1};

    // gate close
    EXPECT_EQ(store->_record_count, 0);
    store->_gate._stopped.store(true);
    s = store->put(blob1, meta, data, &record_bytes);
    ASSERT_EQ(s.code(), AIE_FAIL);
    store->_gate._stopped.store(false);
    EXPECT_EQ(store->_record_count, 0);

    // put success
    s = store->put(blob1, meta, data, &record_bytes);
    EXPECT_EQ(s.ok(), true);
    EXPECT_EQ(store->_record_count, 1);

    s = store->remove(blob1);
    EXPECT_EQ(s.ok(), true);
    EXPECT_EQ(store->_record_count, 0);
    s = store->_storage_indexer->get(blob1, &location, &extra_info);
    EXPECT_EQ(s.code(), AIE_NOT_EXIST);
    EXPECT_EQ(extra_info.has_prev_hole_blob_id(), false); // is not hole

    //
    // remove opt
    // put -> fast remove -> put fail(vbid is hole)
    auto blob2 = BlobId{2, 2};
    shard_len = 100 * 1024 - sizeof(uint64_t);  // 100K >= min_record_size_for_fast_remove
    shard_buffer.reset(new char[shard_len]);
    aries::common::make_mock_data(shard_buffer.get(), shard_len);
    data.clear();
    data.append(shard_buffer.get(), shard_len);
    shard_crc = base::crc32c::Value(shard_buffer.get(), shard_len);
    meta.set_blob_len(shard_len);
    meta.set_blob_crc(shard_crc);
    meta.set_shard_len(shard_len);
    meta.set_shard_crc(shard_crc);

    s = store->put(blob2, meta, data, &record_bytes);
    EXPECT_EQ(s.ok(), true);
    EXPECT_EQ(store->_record_count, 1);

    s = store->remove(blob2); // will do fast remove
    EXPECT_EQ(s.ok(), true);
    EXPECT_EQ(store->_record_count, 0);
    s = store->_storage_indexer->get(blob2, &location, &extra_info);
    EXPECT_EQ(s.code(), AIE_BLOB_NOT_EXIST); // is hole
    EXPECT_EQ(extra_info.has_prev_hole_blob_id(), true);

    s = store->put(blob2, meta, data, &record_bytes);
    EXPECT_EQ(s.ok(), false);
    EXPECT_EQ(s.code(), AIE_EXIST);

    // read vbid io error
    {
    BMOCK_CLASS_MOCK_GUARD(CacheLinkedStoreMemIndexer, get);
    EXPECT_CALL(BMOCK_NS_CLASS_OBJECT(aries::datanode, CacheLinkedStoreMemIndexer, get),
            get(_, _, _, _)).WillOnce(Return(Status(AIE_IO_ERROR)));
    auto blob_tmp = BlobId{816, 334};
    s = store->put(blob_tmp, meta, data, &record_bytes);
    EXPECT_FALSE(s.ok());
    EXPECT_EQ(s.code(), AIE_IO_ERROR);
    }

    // read hole io error
    {
    BMOCK_CLASS_MOCK_GUARD(CacheLinkedStoreMemIndexer, get);
    EXPECT_CALL(BMOCK_NS_CLASS_OBJECT(aries::datanode, CacheLinkedStoreMemIndexer, get),
            get(_, _, _, _)).WillOnce(Return(Status(AIE_BLOB_NOT_EXIST))).WillOnce(Return(Status(AIE_IO_ERROR)));
    auto blob_tmp = BlobId{816, 334};
    s = store->put(blob_tmp, meta, data, &record_bytes);
    EXPECT_FALSE(s.ok());
    EXPECT_EQ(s.code(), AIE_IO_ERROR);
    }

    // write io error
    {
    BMOCK_CLASS_MOCK_GUARD(LinkedStore, write_shard_record_internal);
    EXPECT_CALL(BMOCK_NS_CLASS_OBJECT(aries::datanode, LinkedStore, write_shard_record_internal),
            write_shard_record_internal(_, _, _)).WillOnce(Return(Status(AIE_IO_ERROR)));
    auto blob_tmp = BlobId{816, 334};
    s = store->put(blob_tmp, meta, data, &record_bytes);
    EXPECT_FALSE(s.ok());
    EXPECT_EQ(s.code(), AIE_IO_ERROR);
    }

    // update_hole_info fail
    {
    BMOCK_CLASS_MOCK_GUARD(CacheLinkedStorage, update_hole_info);
    EXPECT_CALL(BMOCK_NS_CLASS_OBJECT(aries::datanode, CacheLinkedStorage, update_hole_info),
            update_hole_info(_, _, _, _)).WillOnce(Return(Status(AIE_IO_ERROR)));
    auto blob_tmp = BlobId{816, 334};
    s = store->put(blob_tmp, meta, data, &record_bytes);
    EXPECT_FALSE(s.ok());
    EXPECT_EQ(s.code(), AIE_IO_ERROR);
    }

    // put shard to hole
    shard_len = 108 * 1024 - sizeof(uint64_t); // 108K > min_record_size_for_fast_remove
    shard_buffer.reset(new char[shard_len]);
    aries::common::make_mock_data(shard_buffer.get(), shard_len);
    data.clear();
    data.append(shard_buffer.get(), shard_len);
    shard_crc = base::crc32c::Value(shard_buffer.get(), shard_len);
    meta.set_blob_len(shard_len);
    meta.set_blob_crc(shard_crc);
    meta.set_shard_len(shard_len);
    meta.set_shard_crc(shard_crc);

    s = store->put(BlobId{3, 3}, meta, data, &record_bytes);
    EXPECT_EQ(s.ok(), true);
    s = store->put(BlobId{4, 4}, meta, data, &record_bytes);
    EXPECT_EQ(s.ok(), true);
    s = store->put(BlobId{5, 5}, meta, data, &record_bytes);
    EXPECT_EQ(s.ok(), true);
    s = store->put(BlobId{6, 6}, meta, data, &record_bytes);
    EXPECT_EQ(s.ok(), true);
    s = store->put(BlobId{7, 7}, meta, data, &record_bytes);
    EXPECT_EQ(s.ok(), true);

    s = store->remove(BlobId{4, 4});
    EXPECT_EQ(s.ok(), true);
    s = store->remove(BlobId{6, 6});
    EXPECT_EQ(s.ok(), true);

    store->refresh_hole_info(1);
    EXPECT_EQ(store->_record_count, 3); // {3, 5, 7}
    EXPECT_EQ(store->_hole_record_num, 3); // {2, 4, 6}
    EXPECT_EQ(store->_hole_size, 108 * 1024 + 124 * 1024 * 2);
    EXPECT_EQ(store->_can_free_hole_size, 4 * 1024 * 1024);
    EXPECT_EQ(store->_record_container_hole_info[7].head_hole_blob_id.vbid(), 2);
    EXPECT_EQ(store->_record_container_hole_info[7].tail_hole_blob_id.vbid(), 2);
    EXPECT_EQ(store->_record_container_hole_info[8].head_hole_blob_id.vbid(), 4);
    EXPECT_EQ(store->_record_container_hole_info[8].tail_hole_blob_id.vbid(), 6);

    s = store->put(BlobId{8, 8}, meta, data, &record_bytes);
    EXPECT_EQ(s.ok(), true);
    store->refresh_hole_info(1);
    EXPECT_EQ(store->_record_count, 4); // {3, 5, 7, 8}
    EXPECT_EQ(store->_hole_record_num, 2); // {2, 6}
    EXPECT_EQ(store->_hole_size, 108 * 1024 + 124 * 1024);
    EXPECT_EQ(store->_can_free_hole_size, 4 * 1024 * 1024);
    EXPECT_EQ(store->_record_container_hole_info[7].head_hole_blob_id.vbid(), 2);
    EXPECT_EQ(store->_record_container_hole_info[7].tail_hole_blob_id.vbid(), 2);
    EXPECT_EQ(store->_record_container_hole_info[8].head_hole_blob_id.vbid(), 6);
    EXPECT_EQ(store->_record_container_hole_info[8].tail_hole_blob_id.vbid(), 6);

    // no space , fill holes to free block
    store->_max_holes_size_for_fast_remove = (uint64_t)5 * 1024 * 1024 * 1024;  // 5G
    shard_len = 124 * 1024 - sizeof(uint64_t); // 124K > min_record_size_for_fast_remove
    shard_buffer.reset(new char[shard_len]);
    aries::common::make_mock_data(shard_buffer.get(), shard_len);
    data.clear();
    data.append(shard_buffer.get(), shard_len);
    shard_crc = base::crc32c::Value(shard_buffer.get(), shard_len);
    meta.set_blob_len(shard_len);
    meta.set_blob_crc(shard_crc);
    meta.set_shard_len(shard_len);
    meta.set_shard_crc(shard_crc);

    auto blob_tmp = BlobId{9, 9};
    s = store->put(blob_tmp, meta, data, &record_bytes);
    while (s.ok()) {
        ++blob_tmp._vbid;
        s = store->put(blob_tmp, meta, data, &record_bytes);
    }
    EXPECT_EQ(s.code(), AIE_VLET_FULL);
    store->refresh_hole_info(1);
    EXPECT_EQ(store->_record_count, 4 + (1023 * 4 * 1024) / 140);
    EXPECT_EQ(store->_hole_record_num, 1);
    EXPECT_EQ(store->_hole_size, 124 * 1024);
    EXPECT_EQ(store->_can_free_hole_size, 0);
    EXPECT_EQ(store->_record_container_hole_info[8].head_hole_blob_id.vbid(), 6);
    EXPECT_EQ(store->_record_container_hole_info[8].tail_hole_blob_id.vbid(), 6);

    // 140K * 30 > 4M
    for (uint64_t i = 9; i < 39; ++i) {
        s = store->remove(BlobId{9, i});
        EXPECT_EQ(s.ok(), true);
    }
    store->refresh_hole_info(1);
    EXPECT_EQ(store->_record_count, 4 + (1022 * 4 * 1024) / 140);
    EXPECT_EQ(store->_mark_deleted_record_num, 0);
    EXPECT_EQ(store->_hole_record_num, 1 + 30);
    EXPECT_EQ(store->_hole_size, 124 * 1024 + 140 * 1024 * 30);
    EXPECT_EQ(store->_can_free_hole_size, 4 * 1024 * 1024);
    EXPECT_EQ(store->_record_container_hole_info[8].head_hole_blob_id.vbid(), 6);
    EXPECT_EQ(store->_record_container_hole_info[8].tail_hole_blob_id.vbid(), 6);
    EXPECT_EQ(store->_record_container_hole_info[9].head_hole_blob_id.vbid(), 9);
    EXPECT_EQ(store->_record_container_hole_info[9].tail_hole_blob_id.vbid(), 38);

    shard_len = 140 * 1024 - sizeof(uint64_t); // 140K > min_record_size_for_fast_remove
    shard_buffer.reset(new char[shard_len]);
    aries::common::make_mock_data(shard_buffer.get(), shard_len);
    data.clear();
    data.append(shard_buffer.get(), shard_len);
    shard_crc = base::crc32c::Value(shard_buffer.get(), shard_len);
    meta.set_blob_len(shard_len);
    meta.set_blob_crc(shard_crc);
    meta.set_shard_len(shard_len);
    meta.set_shard_crc(shard_crc);

    // fill_holes failed
    {
    BMOCK_CLASS_MOCK_GUARD(CacheLinkedStorage, fill_container_holes);
    EXPECT_CALL(BMOCK_NS_CLASS_OBJECT(aries::datanode, CacheLinkedStorage, fill_container_holes),
            fill_container_holes(_, _)).WillOnce(Return(Status(AIE_IO_ERROR)));
    blob_tmp = BlobId{1000000000, 1000000000};
    s = store->put(blob_tmp, meta, data, &record_bytes);
    EXPECT_FALSE(s.ok());
    EXPECT_EQ(s.code(), AIE_VLET_FULL);
    }

    s = store->put(blob_tmp, meta, data, &record_bytes);
    EXPECT_TRUE(s.ok());
    store->refresh_hole_info(1);
    EXPECT_EQ(store->_can_free_hole_size, 0);
}

TEST_F(CacheStorageTest, remove_opt) {
    auto [store, db, opt] = make_store(true);

    // no fast_remove_opt
    uint32_t shard_len = 2 * 1024 - sizeof(uint64_t);  // 2K < min_record_size_for_fast_remove
    std::unique_ptr<char []> shard_buffer(new char[shard_len]);
    aries::common::make_mock_data(shard_buffer.get(), shard_len);
    base::IOBuf data;
    data.append(shard_buffer.get(), shard_len);
    uint32_t shard_crc = base::crc32c::Value(shard_buffer.get(), shard_len);
    uint64_t vid = 1;
    uint64_t vbid = 1;
    aries::pb::ShardMeta meta;
    uint64_t record_bytes;

    meta.set_blob_len(shard_len);
    meta.set_blob_crc(shard_crc);
    meta.set_shard_len(shard_len);
    meta.set_shard_crc(shard_crc);

    Status s = store->put(BlobId{vid, vbid}, meta, data, &record_bytes);
    EXPECT_EQ(s.ok(), true);
    s = store->remove(BlobId{vid, vbid});
    EXPECT_EQ(s.ok(), true);

    store->refresh_hole_info(1);
    EXPECT_EQ(store->_record_count, 0);
    EXPECT_EQ(store->_mark_deleted_record_num, 0);
    EXPECT_EQ(store->_block_bitmap.free_pos_num(), 1024);

    // fast_remove_opt
    vbid = 2;
    shard_len = 100 * 1024 - sizeof(uint64_t);  // 100K > min_record_size_for_fast_remove
    shard_buffer.reset(new char[shard_len]);
    aries::common::make_mock_data(shard_buffer.get(), shard_len);
    data.clear();
    data.append(shard_buffer.get(), shard_len);
    shard_crc = base::crc32c::Value(shard_buffer.get(), shard_len);
    meta.set_blob_len(shard_len);
    meta.set_blob_crc(shard_crc);
    meta.set_shard_len(shard_len);
    meta.set_shard_crc(shard_crc);

    s = store->put(BlobId{vid, vbid}, meta, data, &record_bytes);
    EXPECT_EQ(s.ok(), true);
    s = store->remove(BlobId{vid, vbid});    // add a hole
    EXPECT_EQ(s.ok(), true);

    store->refresh_hole_info(1);
    EXPECT_EQ(store->_record_count, 0);
    EXPECT_EQ(store->_hole_record_num, 1);
    EXPECT_EQ(store->_hole_size, 108 * 1024);
    EXPECT_EQ(store->_can_free_hole_size, 4 * 1024 * 1024);
    EXPECT_EQ(store->_record_container_hole_info[7].head_hole_blob_id.vbid(), vbid); // record_type = 7
    EXPECT_EQ(store->_record_container_hole_info[7].tail_hole_blob_id.vbid(), vbid);

    // min_record_size_for_fast_remove from small to big
    for (uint64_t i = 3; i < 3 + 38; ++i) {
        s = store->put(BlobId{vid, i}, meta, data, &record_bytes);
        EXPECT_EQ(s.ok(), true);
    }
    for (uint64_t i = 3; i < 3 + 37; ++i) {
        s = store->remove(BlobId{vid, i});
        EXPECT_EQ(s.ok(), true);
    }

    store->refresh_hole_info(1);
    EXPECT_EQ(store->_record_count, 1);
    EXPECT_EQ(store->_hole_record_num, 37);
    EXPECT_EQ(store->_hole_size, 108 * 1024 * 37);
    EXPECT_EQ(store->_can_free_hole_size, 4 * 1024 * 1024);
    EXPECT_EQ(store->_record_container_hole_info[7].head_hole_blob_id.vbid(), 3); // record_type = 7
    EXPECT_EQ(store->_record_container_hole_info[7].tail_hole_blob_id.vbid(), 39);

    store->_min_record_size_for_fast_remove = 109 * 1024;
    s = store->remove(BlobId{vid, 40});
    EXPECT_EQ(s.ok(), true);

    store->refresh_hole_info(1);
    EXPECT_EQ(store->_record_count, 0);
    EXPECT_EQ(store->_hole_record_num, 37);
    EXPECT_EQ(store->_hole_size, 108 * 1024 * 37);
    EXPECT_EQ(store->_can_free_hole_size, 4 * 1024 * 1024);
    EXPECT_EQ(store->_block_bitmap.free_pos_num(), 1023);

    // FREE_LAST_BLOCK skip fill last block
    for (uint64_t i = 1; i <= 41; ++i) {
        s = store->put(BlobId{vid, i}, meta, data, &record_bytes);
        EXPECT_EQ(s.ok(), true);
    }
    store->_min_record_size_for_fast_remove = 100 * 1024;
    s = store->remove(BlobId{vid, 39});
    EXPECT_EQ(s.ok(), true);
    s = store->remove(BlobId{vid, 37});
    EXPECT_EQ(s.ok(), true);
    s = store->remove(BlobId{vid, 36});
    EXPECT_EQ(s.ok(), true);
    store->_min_record_size_for_fast_remove = 109 * 1024;
    s = store->remove(BlobId{vid, 35});
    EXPECT_EQ(s.ok(), true);

    store->refresh_hole_info(1);
    EXPECT_EQ(store->_record_count, 37);
    EXPECT_EQ(store->_hole_record_num, 3);
    EXPECT_EQ(store->_can_free_hole_size, 4 * 1024 * 1024);

    // mock corrupt record hole
    s = store->put(BlobId{vid, 42}, meta, data, &record_bytes);
    EXPECT_EQ(s.ok(), true);
    s = store->put(BlobId{vid, 43}, meta, data, &record_bytes);
    EXPECT_EQ(s.ok(), true);

    store->_min_record_size_for_fast_remove = 100 * 1024;
    s = store->remove(BlobId{vid, 43});
    EXPECT_EQ(s.ok(), true);

    store->_min_record_size_for_fast_remove = 109 * 1024;
    aries::common::make_mock_data(shard_buffer.get(), shard_len); // corrupt record
    BMOCK_CLASS_MOCK_GUARD(ShardRecord, deserialize_head);
    EXPECT_CALL(BMOCK_NS_CLASS_OBJECT(aries::datanode, ShardRecord, deserialize_head),
            deserialize_head(_, _, _)).WillOnce(Return(0));
    BMOCK_CLASS_MOCK_GUARD(CacheLinkedStorage, read_linked_record);
    EXPECT_CALL(BMOCK_NS_CLASS_OBJECT(aries::datanode, CacheLinkedStorage, read_linked_record),
            read_linked_record(_, _, _, _)).WillOnce(DoAll(SetArgPointee<2>(*shard_buffer.get()), Return(Status(AIE_OK))));
    s = store->remove(BlobId{vid, 42});
    EXPECT_EQ(s.ok(), false); // deserialize error, vid not found

    store->refresh_hole_info(1);
    EXPECT_EQ(store->_record_count, 38);
    EXPECT_EQ(store->_hole_record_num, 2);
}

TEST_F(CacheStorageTest, remove_fuzzy) {
    auto [store, db, opt] = make_store(true);

    Status s;
    uint64_t record_bytes;
    BlobId blob{1, 0};
    auto& vid = blob._vid;
    auto& vbid = blob._vbid;
    std::random_device rd;
    while (s.ok()) {
        uint32_t shard_len = rd() % (3 * 1024 * 1024);
        std::unique_ptr<char []> shard_buffer(new char[shard_len]);
        aries::common::make_mock_data(shard_buffer.get(), shard_len);
        base::IOBuf data;
        data.append(shard_buffer.get(), shard_len);
        uint32_t shard_crc = base::crc32c::Value(shard_buffer.get(), shard_len);
        aries::pb::ShardMeta meta;

        meta.set_blob_len(shard_len);
        meta.set_blob_crc(shard_crc);
        meta.set_shard_len(shard_len);
        meta.set_shard_crc(shard_crc);
        ++vbid;
        s = store->put(blob, meta, data, &record_bytes);
    }
    EXPECT_EQ(s.code(), AIE_VLET_FULL);
    store->refresh_hole_info(1);
    EXPECT_EQ(store->_record_count, vbid - 1);
    EXPECT_EQ(store->_can_free_hole_size, 0);
    for (uint64_t i = 1; i < vbid; ++i) {
        s = store->remove(BlobId{vid, i});
        EXPECT_EQ(s.ok(), true);
    }

    store->refresh_hole_info(1);
    EXPECT_EQ(store->_hole_record_num != 0, true);

    //
    uint64_t start_vbid = vbid + 1;
    while (s.ok()) {
        uint32_t shard_len = rd() % (3 * 1024 * 1024);
        std::unique_ptr<char []> shard_buffer(new char[shard_len]);
        aries::common::make_mock_data(shard_buffer.get(), shard_len);
        base::IOBuf data;
        data.append(shard_buffer.get(), shard_len);
        uint32_t shard_crc = base::crc32c::Value(shard_buffer.get(), shard_len);
        aries::pb::ShardMeta meta;

        meta.set_blob_len(shard_len);
        meta.set_blob_crc(shard_crc);
        meta.set_shard_len(shard_len);
        meta.set_shard_crc(shard_crc);
        ++vbid;
        s = store->put(blob, meta, data, &record_bytes);
    }
    EXPECT_EQ(s.code(), AIE_VLET_FULL);

    store->_min_record_size_for_fast_remove = (uint64_t)4 * 1024 * 1024 * 1024;
    for (uint64_t i = start_vbid; i < vbid; ++i) {
        s = store->remove(BlobId{vid, i});
        EXPECT_EQ(s.ok(), true);
    }

    store->refresh_hole_info(1);
    for (uint32_t i = 1; i <= store->_max_record_type; ++i) {
        auto container = store->_record_containers[i];
        LOG(TRACE) << "record_type:" << i << " tatal_record_num:" << container.record_index
            << " hole_record_num:" << container.hole_record_num;
    }
    EXPECT_EQ(store->_record_count, 0);
    EXPECT_EQ(store->_hole_record_num, 0);
    EXPECT_EQ(store->_can_free_hole_size, 0);
}

TEST_F(CacheStorageTest, indexer) {
    auto [store, db, opt] = make_store(true);

    // cache_mem_db
    StorageItemKey key1(1, 2, 3), key2(1, 2, 4), key3(2, 2, 3), key4(1, 3, 3);
    EXPECT_TRUE(key1 == key1);
    EXPECT_FALSE(key1 != key1);
    EXPECT_TRUE(key1 != key2);
    EXPECT_FALSE(key1 == key2);
    EXPECT_TRUE(key1 < key2);
    EXPECT_TRUE(key1 < key4);

    db = std::make_shared<CacheMemDB>();
    EXPECT_EQ(db->remove(key1).found(), false);

    CacheMemDB::WriteBatch wb;
    wb.remove(key1);
    EXPECT_EQ(db->write_batch(wb), AIE_OK);

    MemWriteBatch mwb(1, db);
    RecordLocation loc, loc2, loc4check;
    loc.block_id = 1;
    loc.next_block_id = 2;
    loc.record_id = 3;
    loc.record_type = 4;

    loc2 = loc;
    loc2.record_id = 4;

    CacheRecordLocationExtraInfo info, info4check;
    info.set_next_hole_blob_id(BlobId(1, 2));
    info.prev_hole_blob_id = std::nullopt;
    info.record_data_offset = std::nullopt; // corrupt due to nullopt

    mwb.put(2, 3, loc, nullptr);
    mwb.put(2, 4, loc, &info);
    mwb.commit();

    CacheLinkedStoreMemIndexer indexer(1, db);
    Status s = indexer.get(2, 3, &loc4check, &info4check);
    EXPECT_EQ(s.code(), AIE_OK);
    EXPECT_TRUE(loc4check == loc);

    s = indexer.get(2, 4, &loc4check, &info4check);
    EXPECT_EQ(s.code(), AIE_FAIL); // index corruption

    auto iter = db->_map.find(key1);
    EXPECT_TRUE(iter != db->_map.end());
    iter->second = nullptr;
    s = indexer.get(2, 3, &loc4check, &info4check);
    EXPECT_EQ(s.code(), AIE_FAIL); // lr._record == nullptr
}

}
}
