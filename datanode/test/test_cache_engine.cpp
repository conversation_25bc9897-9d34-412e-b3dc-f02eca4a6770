#include "baidu/inf/aries/common/aries_ut_pread.h"

#include <random>
#include <gtest/gtest.h>
#include <base/crc32c.h>
#include "baidu/inf/aries-api/common/utils.h"
#include "baidu/inf/aries/common/bmock_util.h"
#include "baidu/inf/aries/common/string_util.h"
#include "baidu/inf/aries/datanode/flags.h"
#include "baidu/inf/aries/datanode/cache/cache_define.h"
#include "baidu/inf/aries/datanode/cache/engine/cache_linked_engine.h"
#include "baidu/inf/aries/datanode/cache/storage/cache_linked_storage.h"

#define CACHE_PATH "cache3a3bcf2b"

namespace aries {
namespace datanode {

class TestEnvironment : public ::testing::Environment {
public:
    void SetUp() {
        ::system("rm -rf " CACHE_PATH);
        ::system("mkdir " CACHE_PATH);
    }

    void TearDown() {
        ::system("rm -rf " CACHE_PATH);
    }
};

TEST(CacheLinkedEngine, add_disk) {
    CacheLinkedEngine engine;
    int ret;
    FLAGS_cache_storage_size_gb = 1;

    // cache_disk_path is a regular file
    ::system("rm -rf " CACHE_PATH);
    ::system("touch " CACHE_PATH);
    ret = engine.add_disk(1000, CACHE_PATH, aries::common::GB, false);
    ASSERT_NE(ret, AIE_OK);
    ::system("rm -rf " CACHE_PATH);

    // cache_disk_path not exist
    ::system("rm -rf " CACHE_PATH);
    ret = engine.add_disk(1000, CACHE_PATH, aries::common::GB, false);
    ASSERT_EQ(ret, AIE_OK);
    ::system("rm -rf " CACHE_PATH);

    // cache_disk_path delete substorage files
    ::system("mkdir " CACHE_PATH);
    ::system("touch " CACHE_PATH "/L_C_fxxx");
    struct stat statbuf;
    ASSERT_EQ(::lstat(CACHE_PATH "/L_C_fxxx", &statbuf), 0);
    ret = engine.add_disk(1001, CACHE_PATH, aries::common::GB, false);
    ASSERT_EQ(ret, AIE_OK);
    ASSERT_LT(::lstat(CACHE_PATH "/L_C_fxxx", &statbuf), 0);
}

TEST(CacheLinkedEngine, put) {
    CacheLinkedEngine engine;
    int ret;
    CacheEngineStat stat;
    FLAGS_cache_storage_size_gb = 1;
    auto shard1 = ShardId(1, 1, 1);

    ret = engine.add_disk(1002, CACHE_PATH, aries::common::GB, false);
    ASSERT_EQ(ret, AIE_OK);

    // make data , meta , shard info
    uint32_t shard_len = 2 * 1024;
    std::unique_ptr<char []> shard_buffer(new char[shard_len]);
    aries::common::make_mock_data(shard_buffer.get(), shard_len);
    base::IOBuf data;
    data.append(shard_buffer.get(), shard_len);
    uint32_t shard_crc = base::crc32c::Value(shard_buffer.get(), shard_len);
    aries::pb::ShardMeta meta;
    meta.set_blob_len(shard_len);
    meta.set_blob_crc(shard_crc);
    meta.set_shard_len(shard_len);
    meta.set_shard_crc(shard_crc);
    CacheShardInfo info;

    // gate is close
    engine._gate._stopped.store(true);
    ret = engine.put(shard1, meta, data, &info);
    EXPECT_EQ(ret, AIE_FAIL);
    engine._gate._stopped.store(false);

    // storage pick fail: storage could not hold shard
    std::vector<CacheStoragePtr> tmp_storage_list;
    std::swap(tmp_storage_list, engine._storage_list);
    EXPECT_TRUE(engine._storage_list.empty());
    ret = engine.put(shard1, meta, data, &info);
    EXPECT_EQ(ret, AIE_INVALID_ARGUMENT);
    std::swap(tmp_storage_list, engine._storage_list);

    // target storage put fail
    meta.set_shard_crc(0); // make crc corrupted
    ret = engine.put(shard1, meta, data, &info);
    EXPECT_EQ(ret, AIE_CHECKSUM);
    meta.set_shard_crc(shard_crc);

    // put success
    stat = engine.get_engine_stat();
    EXPECT_EQ(stat.total_shard_count, 0);
    ret = engine.put(shard1, meta, data, &info);
    EXPECT_EQ(ret, AIE_OK);
    stat = engine.get_engine_stat();
    EXPECT_EQ(stat.total_shard_count, 1);
}

TEST(CacheLinkedEngine, del) {
    CacheLinkedEngine engine;
    int ret;
    CacheEngineStat stat;
    FLAGS_cache_storage_size_gb = 1;
    auto shard1 = ShardId{1, 1, 1};
    auto shard2 = ShardId{2, 2, 2};

    ret = engine.add_disk(1003, CACHE_PATH, aries::common::GB, false);
    ASSERT_EQ(ret, AIE_OK);

    // make data , meta , shard info
    uint32_t shard_len = 2 * 1024;
    std::unique_ptr<char []> shard_buffer(new char[shard_len]);
    aries::common::make_mock_data(shard_buffer.get(), shard_len);
    base::IOBuf data;
    data.append(shard_buffer.get(), shard_len);
    uint32_t shard_crc = base::crc32c::Value(shard_buffer.get(), shard_len);
    aries::pb::ShardMeta meta;
    meta.set_blob_len(shard_len);
    meta.set_blob_crc(shard_crc);
    meta.set_shard_len(shard_len);
    meta.set_shard_crc(shard_crc);
    CacheShardInfo info;

    // put one shard
    ret = engine.put(shard1, meta, data, &info);
    EXPECT_EQ(ret, AIE_OK);

    // gate is closed
    engine._gate._stopped.store(true);
    ret = engine.del(shard1, info);
    EXPECT_EQ(ret, AIE_FAIL);
    engine._gate._stopped.store(false);

    // shard_info error: wrong storage_id
    int64_t tmp_storage_id = -1;
    std::swap(tmp_storage_id, info.storage_id);
    ret = engine.del(shard1, info);
    EXPECT_EQ(ret, AIE_INVALID_ARGUMENT);
    std::swap(tmp_storage_id, info.storage_id);

    tmp_storage_id = 19980928; // not in storage list
    std::swap(tmp_storage_id, info.storage_id);
    ret = engine.del(shard1, info);
    EXPECT_EQ(ret, AIE_FAIL);
    std::swap(tmp_storage_id, info.storage_id);

    // target storage remove fail
    ret = engine.del(shard2, info);
    EXPECT_EQ(ret, AIE_NOT_EXIST);

    // remove success
    stat = engine.get_engine_stat();
    EXPECT_EQ(stat.total_shard_count, 1);
    ret = engine.del(shard1, info);
    EXPECT_EQ(ret, AIE_OK);
    stat = engine.get_engine_stat();
    EXPECT_EQ(stat.total_shard_count, 0);
}

TEST(CacheLinkedEngine, get) {
    CacheLinkedEngine engine;
    int ret;
    CacheEngineStat stat;

    ret = engine.add_disk(1003, CACHE_PATH, aries::common::GB, false);
    ASSERT_EQ(ret, AIE_OK);

    FLAGS_cache_storage_size_gb = 1;
    auto shard1 = ShardId{1, 1, 1};
    auto shard2 = ShardId{2, 2, 2};

    // make data , meta , shard info
    uint32_t shard_len = 2 * 1024;
    std::unique_ptr<char []> shard_buffer(new char[shard_len]);
    aries::common::make_mock_data(shard_buffer.get(), shard_len);
    base::IOBuf data, data4check;
    data.append(shard_buffer.get(), shard_len);
    uint32_t shard_crc = base::crc32c::Value(shard_buffer.get(), shard_len);
    aries::pb::ShardMeta meta, meta4check;
    meta.set_blob_len(shard_len);
    meta.set_blob_crc(shard_crc);
    meta.set_shard_len(shard_len);
    meta.set_shard_crc(shard_crc);
    CacheShardInfo info;

    // put one shard
    ret = engine.put(shard1, meta, data, &info);
    EXPECT_EQ(ret, AIE_OK);

    // gate is closed
    engine._gate._stopped.store(true);
    ret = engine.get(shard1, info, &meta4check, &data4check);
    EXPECT_EQ(ret, AIE_FAIL);
    engine._gate._stopped.store(false);

    // shard_info error: wrong storage_id
    int64_t tmp_storage_id = -1;
    std::swap(tmp_storage_id, info.storage_id);
    ret = engine.get(shard1, info, &meta4check, &data4check);
    EXPECT_EQ(ret, AIE_INVALID_ARGUMENT);
    std::swap(tmp_storage_id, info.storage_id);

    tmp_storage_id = 19980928; // not in storage list
    std::swap(tmp_storage_id, info.storage_id);
    ret = engine.get(shard1, info, &meta4check, &data4check);
    EXPECT_EQ(ret, AIE_FAIL);
    std::swap(tmp_storage_id, info.storage_id);

    // target storage get fail
    ret = engine.get(shard2, info, &meta4check, &data4check);
    EXPECT_EQ(ret, AIE_NOT_EXIST);

    // get success
    stat = engine.get_engine_stat();
    EXPECT_EQ(stat.total_shard_count, 1);
    ret = engine.get(shard1, info, &meta4check, &data4check);
    EXPECT_EQ(ret, AIE_OK);
    EXPECT_EQ(meta4check.blob_len(), meta.blob_len());
    EXPECT_EQ(meta4check.blob_crc(), meta.blob_crc());
    EXPECT_EQ(meta4check.shard_len(), meta.shard_len());
    EXPECT_EQ(meta4check.shard_crc(), meta.shard_crc());
    EXPECT_TRUE(data4check == data);
}

TEST(CacheLinkedEngine, drop_storage) {
    CacheLinkedEngine engine;
    int ret;
    int64_t disk_size = 3 * aries::common::GB;
    FLAGS_cache_storage_size_gb = 1;
    auto storage_cnt = disk_size / aries::common::GB / FLAGS_cache_storage_size_gb;

    ret = engine.add_disk(1003, CACHE_PATH, disk_size, false);
    ASSERT_EQ(ret, AIE_OK);

    // check engine stat
    ASSERT_EQ(engine._storage_list.size(), storage_cnt);
    auto storage1_id = engine._storage_list.back()->get_storage_id();
    auto stat = engine.get_storage_stat(storage1_id);
    EXPECT_NE(stat, std::nullopt);
    EXPECT_EQ(stat->record_count, 0);
    EXPECT_EQ(stat->free_size, stat->capacity);
    EXPECT_EQ(stat->capacity, FLAGS_cache_storage_size_gb * aries::common::GB);
    stat = engine.get_storage_stat(19950116); // nonexist storage
    EXPECT_EQ(stat, std::nullopt);

    std::vector<CacheStorageStat> storage_list;
    engine.list_storage(&storage_list);
    EXPECT_EQ(storage_list.size(), storage_cnt);

    EXPECT_EQ(engine.get_engine_type(), CacheEngineType::LINKED);

    // drop nonexist storage
    ret = engine.drop_storage(20220812);
    EXPECT_EQ(ret, AIE_FAIL);

    // drop storage1
    ret = engine.drop_storage(storage1_id);
    EXPECT_EQ(ret, AIE_OK);
    stat = engine.get_storage_stat(storage1_id);
    EXPECT_EQ(stat, std::nullopt);
    EXPECT_EQ(engine._storage_list.size(), storage_cnt - 1);

    auto engine_stat = engine.get_engine_stat();
    EXPECT_EQ(engine_stat.total_capacity, (storage_cnt - 1) * aries::common::GB);

    storage_list.clear();
    engine.list_storage(&storage_list);
    EXPECT_EQ(storage_list.size(), storage_cnt - 1);
}

}
}

int main(int argc, char* argv[]) {
    ::testing::AddGlobalTestEnvironment(new aries::datanode::TestEnvironment());
    ::testing::InitGoogleTest(&argc, argv);
    return RUN_ALL_TESTS();
}
