
#include <stdio.h>
#include <unistd.h>
#include <fcntl.h>
#include <sys/types.h>
#include <sys/stat.h>
#include <sys/mman.h>
#include <sys/time.h>
#include <gtest/gtest.h>
#include "baidu/inf/aries/datanode/storage/zone/device/zone_manager.h"

using namespace folly;

namespace aries {
namespace datanode {

TEST(ZoneManager, test_clean_list_empty) {
    ZoneManager zone_mgr;
    ZoneFD* zone_fd = nullptr;
    EXPECT_EQ(zone_mgr.allocate_zone(&zone_fd), AIE_DISK_FULL);

    EXPECT_EQ(zone_mgr.active_io_zone_count(), 0);
    EXPECT_EQ(zone_mgr.open_io_zone_count(), 0);
    EXPECT_EQ(zone_mgr.invalid_io_zone_count(), 0);
}

TEST(ZoneManager, test_clean_list_less_then_retain_zone) {
    ZoneManager zone_mgr;
    ZoneFD* zone_fd = nullptr;
    for (int i=1; i<=5; ++i) {
        ZoneFD* new_zone = new ZoneFD(i, ZoneState::INVALID, 1024);
        zone_mgr.add_new_zone(new_zone);
    }
    EXPECT_EQ(zone_mgr.allocate_zone(&zone_fd, 4), AIE_OK);
    EXPECT_EQ(zone_mgr.allocate_zone(&zone_fd), AIE_OK);
    EXPECT_EQ(zone_mgr.allocate_zone(&zone_fd, 5), AIE_DISK_FULL);

    EXPECT_EQ(zone_mgr.active_io_zone_count(), 2);
    EXPECT_EQ(zone_mgr.open_io_zone_count(), 2);
    EXPECT_EQ(zone_mgr.invalid_io_zone_count(), 3);
}

TEST(ZoneManager, test_allocate_invalid_state) {
    ZoneManager zone_mgr;
    ZoneFD new_zone(1, ZoneState::FREE, 1024);
    zone_mgr._clean_zone_list.push_back(&new_zone);
    ZoneFD* zone_fd = nullptr;
    EXPECT_EQ(zone_mgr.allocate_zone(&zone_fd), AIE_FAIL);
}

TEST(ZoneManager, test_free) {
    ZoneManager zone_mgr;
    ZoneFD* new_zone = new ZoneFD(1, ZoneState::INVALID, 1024);
    zone_mgr.add_new_zone(new_zone);

    ZoneFD* new_zone2  = new ZoneFD(2, ZoneState::CLOSED, 1024);
    zone_mgr._zone_map[new_zone2->zone_id] = new_zone2;

    ZoneFD* new_zone3 = new ZoneFD(3, ZoneState::OPEN, 1024);
    zone_mgr._zone_map[new_zone3->zone_id] = new_zone3;

    ZoneFD* zone_fd = nullptr;
    EXPECT_EQ(zone_mgr.allocate_zone(&zone_fd), AIE_OK);
    EXPECT_EQ(zone_fd->zone_id, 1);

    EXPECT_EQ(zone_mgr.close_zone(1), AIE_OK);
    EXPECT_EQ(zone_fd->zone_id, 1);

    EXPECT_EQ(zone_mgr.free_zone(100), AIE_NOT_EXIST);

    EXPECT_EQ(zone_mgr.free_zone(1), AIE_OK);
    EXPECT_EQ(zone_mgr.free_zone(2), AIE_OK);
    EXPECT_EQ(zone_mgr.free_zone(3), AIE_FAIL);
}

TEST(ZoneManager, test_close) {
    ZoneManager zone_mgr;
    EXPECT_EQ(zone_mgr.close_zone(1), AIE_NOT_EXIST);

    ZoneFD* new_zone= new ZoneFD(1, ZoneState::CLOSED, 1024);
    zone_mgr._zone_map[new_zone->zone_id] = new_zone;

    EXPECT_EQ(zone_mgr.close_zone(1), AIE_OK);

    ZoneFD* new_zone2= new ZoneFD(2, ZoneState::INVALID, 1024);
    zone_mgr.add_new_zone(new_zone2);
    ZoneFD* zone_fd = nullptr;
    EXPECT_EQ(zone_mgr.allocate_zone(&zone_fd), AIE_OK);
    EXPECT_EQ(zone_fd->zone_id, 2);
    EXPECT_EQ(zone_mgr.close_zone(2), AIE_OK);
}

TEST(ZoneManager, test_reset) {
    ZoneManager zone_mgr;
    EXPECT_EQ(zone_mgr.reset_zone(1, ZoneState::CLOSED), AIE_NOT_EXIST);

    ZoneFD* new_zone = new ZoneFD(1, ZoneState::CLOSED, 1024);
    zone_mgr._zone_map[new_zone->zone_id] = new_zone;
    EXPECT_EQ(zone_mgr.reset_zone(1, ZoneState::CLOSED), AIE_FAIL);


    ZoneFD* new_zone2 = new ZoneFD(2, ZoneState::INVALID, 1024);
    zone_mgr._zone_map[new_zone2->zone_id] = new_zone2;
    EXPECT_EQ(zone_mgr.reset_zone(2, ZoneState::CLOSED), AIE_OK);

    ZoneFD* new_zone3 = new ZoneFD(3, ZoneState::INVALID, 1024);
    zone_mgr._zone_map[new_zone3->zone_id] = new_zone3;
    EXPECT_EQ(zone_mgr.reset_zone(3, ZoneState::OPEN), AIE_OK);

    ZoneFD* new_zone4 = new ZoneFD(3, ZoneState::INVALID, 1024);
    zone_mgr._zone_map[new_zone4->zone_id] = new_zone4;
    EXPECT_EQ(zone_mgr.reset_zone(3, ZoneState::FREE), AIE_FAIL);
}

} // end namespace of datanode
} // end namespace of aries
