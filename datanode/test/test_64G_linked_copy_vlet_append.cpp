// Copyright (c) 2016 Baidu.com, Inc. All Rights Reserved
// Author <PERSON><PERSON><PERSON>(<EMAIL>)

#include "baidu/inf/aries/common/bmock_util.h"
#include <stdio.h>
#include <unistd.h>
#include <fcntl.h>
#include <random>
#include <base/crc32c.h>
#include "baidu/inf/aries/datanode/datanode.h"
#include "baidu/inf/aries/datanode/test/mock_master.h"
#include "baidu/inf/aries/datanode/util/util.h"
#include "baidu/inf/aries-api/common/types.h"
#include "baidu/inf/aries-api/common/config.h"
#include "baidu/inf/aries-api/common/heartbeat.h"
#include "baidu/inf/aries/datanode/test/rpc.h"
#include "baidu/inf/aries-api/common/proto/error_code.pb.h"
#include "baidu/inf/aries-api/common/master_tracker.h"
#include "baidu/inf/aries/datanode/copy_vlet.h"
#include "baidu/inf/aries/datanode/vlet/linked_vlet.h"
#include "baidu/inf/aries/datanode/vlet/append_vlet.h"
#include "baidu/inf/aries/datanode/storage/random/linked_shard_record.h"
#include <base/rand_util.h>

using ::testing::Return;
using ::testing::ReturnRef;
using ::testing::_;
namespace aries {
namespace datanode {

BMOCK_NS_METHOD2(base::crc32c, Value, uint32_t(const char* data, size_t n));

BMOCK_NS_CLASS_METHOD0(aries::datanode, CopyVletWorker, get_vlet_info, int());
BMOCK_NS_CLASS_METHOD0(aries::datanode, CopyVletWorker, vlet_info, aries::pb::VletInfo&());
BMOCK_NS_CLASS_METHOD0(aries::datanode, CopyVletWorker, src_vlet_info, aries::pb::VletInfo&());
BMOCK_NS_CLASS_METHOD1(aries::datanode, VletManager, create, int(uint64_t));
BMOCK_NS_CLASS_METHOD2(aries::datanode, VletManager, add, bool(uint64_t, VletPtr&));

DECLARE_BMOCK_METHOD(4, fallocate, int(int, int, off_t, off_t));
DEFINE_BMOCK_METHOD(4, fallocate, int(int, int, off_t, off_t));

DECLARE_BMOCK_NS_CLASS_METHOD(2, aries::datanode, MasterCaller, list_node_vlet, 
        int(aries::pb::ListNodeVletRequest* request, aries::pb::ListNodeVletResponse* response)); 
DEFINE_BMOCK_NS_CLASS_METHOD(2, aries::datanode, MasterCaller, list_node_vlet, 
        int(aries::pb::ListNodeVletRequest* request, aries::pb::ListNodeVletResponse* response)); 
int mocked_list_node_vlet(aries::pb::ListNodeVletRequest* request, 
                    aries::pb::ListNodeVletResponse* response) {
    return AIE_FAIL;
}

int mocked_fallocate(int fd, int mode, off_t offset, off_t len) {
    LOG(FATAL) << "mocked fallocate";
    if (mode & FALLOC_FL_KEEP_SIZE) {
        return 0;
    }
    return ftruncate(fd, offset + len);
}

// int create(uint64_t volume_id);
constexpr uint32_t kNodePort = 59100;
constexpr uint32_t kNodeControlPort = kNodePort;
constexpr uint32_t kNodeDataPort = kNodePort + 1;
constexpr uint32_t record_page_num = 128;
char  cwd_path[1024];

int generator_disk_conf(const aries::pb::DatanodeDiskConf& datanode_disk_conf) {
    std::string json_data;
    std::string err_msg;
    struct Pb2JsonOptions options;
    options.pretty_json = true;
    options.enum_option = OUTPUT_ENUM_BY_NUMBER;

    if (!ProtoMessageToJson(datanode_disk_conf, &json_data, options, &err_msg)) {
        return -1;
    }

    VletManager vlet_mgr;
    ::system("echo {} > conf/disk.conf");
    DiskManager disk_manager(&vlet_mgr);
    disk_manager.load_disk_conf();

    return  disk_manager.update_disk_conf(json_data) == false;
}

void make_disk_conf() {
    std::string path(cwd_path);
    aries::pb::DatanodeDiskConf disk_conf;

    auto disk_item = disk_conf.add_disk_list();
    disk_item->set_disk_id(0);
    disk_item->set_disk_path(path + "/disk0");
    disk_item->set_capacity_mb(1024);
    disk_item->set_create_time(100);
    disk_item->set_is_used(true);

    disk_item = disk_conf.add_disk_list();
    disk_item->set_disk_id(1);
    disk_item->set_disk_path(path + "/disk1");
    disk_item->set_capacity_mb(1024);
    disk_item->set_create_time(100);
    disk_item->set_is_used(true);

    disk_item = disk_conf.add_disk_list();
    disk_item->set_disk_id(2);
    disk_item->set_disk_path(path + "/disk2");
    disk_item->set_capacity_mb(1024);
    disk_item->set_create_time(100);
    disk_item->set_is_used(true);

    generator_disk_conf(disk_conf);
}

static const char g_data_map[] = "0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ";
inline char g_map_int(uint32_t x) {
    uint32_t len = sizeof(g_data_map) - 1;
    return g_data_map[x % len];
}

void fill_put(aries::pb::ShardPutRequest* req, uint32_t vbid, uint32_t udata_len, uint32_t edata_len,
        std::string* data) {
    auto shard_meta = req->mutable_shard_meta();

    req->set_vbid(vbid);
    shard_meta->set_blob_len(1333);
    shard_meta->set_blob_crc(14444);
    shard_meta->set_create_time(155555);


    std::string udata(udata_len, g_map_int(vbid));
    uint32_t udata_crc = base::crc32c::Value(udata.data(), udata_len);
    shard_meta->set_shard_len(udata_len);
    shard_meta->set_shard_crc(udata_crc);

    std::string edata(edata_len, g_map_int(vbid));
    uint32_t edata_crc = base::crc32c::Value(edata.data(), edata_len);
    shard_meta->set_key("");
    shard_meta->set_user_meta(edata);
    shard_meta->set_key_meta_crc(edata_crc);

    *data = udata;
    LOG(DEBUG) << "put vbid=" << vbid
        << ",udata_len=" << udata_len << ",udata_crc" << udata_crc
        << ",edata_len=" << edata_len << ",edata_crc" << edata_crc;
}

std::map<uint32_t, uint32_t> g_blob_len_map_append;
class CopyVletTest : public ::testing::Test {
public:
    static void SetUpTestCase() {
        std::cout << "setup";
    }
    void SetUp() {
        auto disk_agent = g_datanode->_disk_manager->find(0);
        auto append_vlet = dynamic_cast<AppendVlet*>(disk_agent->_vlet_map[100].get());
        append_vlet->_balance_job_timestamp = 0;
    }
    static void TearDownTestCase() {
        std::cout << "teardown";
    }
    CopyVletTest() {
    }

    ~CopyVletTest() {
    }
};

class TestEnvironment : public ::testing::Environment {
public:

    void SetUp() {
        FLAGS_enable_cache = false;
        BMOCK_NS_STOP(base::crc32c, Value, uint32_t(const char* data, size_t n));
        BMOCK_CLASS_MOCK_GUARD(MasterCaller, list_node_vlet);
        EXPECT_CALL(BMOCK_NS_CLASS_OBJECT(aries::datanode, MasterCaller, list_node_vlet), 
                list_node_vlet(_,_))
            .WillRepeatedly(Invoke(mocked_list_node_vlet));
        ::getcwd(cwd_path, sizeof(cwd_path));
        // for configure
        ::system("rm -rf conf;  mkdir -p conf");

        FLAGS_balance_sleep_ms = 10;

        // data
        ::system("rm -rf disk0");
        ::system("rm -rf disk1");
        ::system("rm -rf disk2");
        ::system("mkdir -p disk0");
        ::system("mkdir -p disk1");
        ::system("mkdir -p disk2");

        common::FLAGS_port = kNodePort;
        FLAGS_total_token = 500000;

        FLAGS_gc_interval_second = 20;
        common::FLAGS_heartbeat_interval_second = 5;

        make_disk_conf();

        common::init_local_addr();

        Datanode* datanode = new Datanode();

        g_datanode = datanode;

        g_datanode->start();
        control_create_vlet();
        write();
    }

    void TearDown() {
        g_datanode->bury();
        ::system("rm -rf ./conf/");
        ::system("rm -rf ./disk0/");
        ::system("rm -rf ./disk1/");
        ::system("rm -rf ./disk2/");
    }

private:
    void control_create_vlet() {
        base::EndPoint endpoint = common::get_local_addr();
        Rpc control(endpoint);

        pb::CreateVletRequest req;
        pb::AckResponse ack;
        req.set_token("default_token");
        auto info = req.mutable_vlet_info();
        int  ret = 0;

        int64_t time = base::gettimeofday_us();

        info->set_vlet_type(VLET_TYPE_APPEND_64G_256M_4K);
        info->set_volume_id(100);
        info->set_shard_index(1);
        info->set_create_time(time);
        info->set_disk_id(0);
        info->set_vlet_version(1);
        auto vlet_engine_options = info->mutable_vlet_engine_options();
        aries::common::vlet_engine_by_vlet_type(VLET_TYPE_APPEND_64G_256M_4K, vlet_engine_options);

        ret = control.create_vlet(&req, &ack);
        assert(ret ==  0);
        assert(ack.status().code() ==  (int)AIE_OK);

    }
    void write() {
        base::EndPoint endpoint = common::get_local_addr();
        endpoint.port += 1;
        Rpc data(endpoint);

        pb::ShardPutRequest req;
        pb::ShardPutResponse ack;
        req.set_token("default_token");
        req.set_volume_id(100);
        req.set_vbid(0);
        req.set_shard_index(1);

        std::string tmp;
        std::random_device rd;
        uint32_t vbid = 1;
        {
            for (int i = 1; i <= (int) record_page_num; ++i) {
                for (int j = 0; j < 10; j++) {
                    uint32_t udata_len = (i-1) * 4096 + 1;
                    g_blob_len_map_append[vbid] = udata_len;
                    fill_put(&req, vbid, udata_len, 0, &tmp);
                    EXPECT_EQ(data.put(&req, &ack, &tmp), 0);
                    EXPECT_EQ(ack.status().code(), (int)AIE_OK);
                    vbid++;
                }
            }
        }
        LOG(NOTICE) << "generate max_vbid:" << vbid;
        // for test balance spread mark-deleted vbids
        pb::ShardRemoveRequest req2;
        pb::AckResponse res2;
        req2.set_token("default_token");
        req2.set_volume_id(100);
        req2.set_shard_index(1);
        req2.set_need_mark_delete(true);

        req2.set_vbid(1);
        EXPECT_EQ(data.remove(&req2, &res2), 0);
        EXPECT_EQ(res2.status().code(), (int)AIE_OK);
        req2.set_vbid(1000);
        EXPECT_EQ(data.remove(&req2, &res2), 0);
        EXPECT_EQ(res2.status().code(), (int)AIE_OK);
        req2.set_vbid(1279);
        EXPECT_EQ(data.remove(&req2, &res2), 0);
        EXPECT_EQ(res2.status().code(), (int)AIE_OK);
    }
private:
};

TEST_F(CopyVletTest, test_linked_from_append) {
    EXPECT_EQ(g_blob_len_map_append.size(), 128 *10);
    BMOCK_NS_CLASS_RESUME(aries::datanode, CopyVletWorker, get_vlet_info, int());
    BMOCK_NS_CLASS_RESUME(aries::datanode, CopyVletWorker, vlet_info, aries::pb::VletInfo&());
    BMOCK_NS_CLASS_RESUME(aries::datanode, VletManager, create, int());
    BMOCK_NS_CLASS_RESUME(aries::datanode, VletManager, add, bool(uint64_t, VletPtr&));
    EXPECT_CALL(BMOCK_NS_CLASS_OBJECT(aries::datanode, VletManager, add), add(_,_))
                 .WillRepeatedly(Return(true));
    EXPECT_CALL(BMOCK_NS_CLASS_OBJECT(aries::datanode, VletManager, create), create(_))
                 .WillRepeatedly(Return(0));
    base::EndPoint endpoint = common::get_local_addr();
    baidu::rpc::Controller cntl;
    google::protobuf::Closure* done = nullptr;
    aries::pb::BalanceVletRequest req;
    aries::pb::AckResponse res;
    req.set_target_disk(1);
    req.set_token(FLAGS_token);
    aries::pb::VletInfo src_info;
    src_info.set_volume_id(100ul);
    src_info.set_vlet_type(VLET_TYPE_APPEND_64G_256M_4K);
    src_info.set_shard_index(1);
    src_info.set_create_time(0);
    src_info.set_vlet_version(1);
    src_info.set_node_addr(common::endpoint2int(endpoint));
    auto vlet_info = req.mutable_vlet();
    vlet_info->set_volume_id(100ul);
    vlet_info->set_vlet_type(VLET_TYPE_LINKED_64G_8M_1M);
    vlet_info->set_shard_index(1);
    vlet_info->set_create_time(0);
    vlet_info->set_vlet_version(1);
    vlet_info->set_node_addr(common::endpoint2int(endpoint));
    EXPECT_CALL(BMOCK_NS_CLASS_OBJECT(aries::datanode, CopyVletWorker, src_vlet_info), 
                src_vlet_info())
                .WillRepeatedly(ReturnRef(src_info));
    std::shared_ptr<Vlet> ptr;
    aries::pb::SpaceInfo space_info;
    auto disk_agent = g_datanode->_disk_manager->find(1);
    int ret = Vlet::new_vlet_from_info(disk_agent, *vlet_info, space_info, ptr);
    EXPECT_EQ(ret, 0);
    CopyVletArgContext *ctx = new CopyVletArgContext();
    auto worker = std::make_shared<CopyVletWorker>(ctx, &cntl, done, &req, &res);
    ctx->worker = worker;
    ret = worker->init();
    LOG(NOTICE) << "worker init error msg:" << res.status().msg();
    EXPECT_EQ(ret, 0);
    ptr->set_db(disk_agent->_db);
    ret = ptr->copy_vlet(worker.get());
    ptr->release_db();
    EXPECT_EQ(ret, 0);
    
    //do assert by read;
    auto linked_vlet = dynamic_cast<LinkedVlet*>(ptr.get());
    EXPECT_EQ(linked_vlet->_store->record_num(), 1277);
    EXPECT_EQ(linked_vlet->_store->mark_deleted_record_num(), 3);
    for (int i = 1; i <= 128; ++i) {
        EXPECT_EQ(10, linked_vlet->_store->get_record_count(i));
    }

    linked_vlet->check_balance_vlet_task();
    EXPECT_EQ(linked_vlet->_balance_job_timestamp, 0);
    BMOCK_NS_CLASS_STOP(aries::datanode, CopyVletWorker, get_vlet_info, int());
    BMOCK_NS_CLASS_STOP(aries::datanode, CopyVletWorker, vlet_info, aries::pb::VletInfo&());
    BMOCK_NS_CLASS_STOP(aries::datanode, VletManager, create, int(uint64_t));
    BMOCK_NS_CLASS_STOP(aries::datanode, VletManager, add, bool(uint64_t, VletPtr&));
}

TEST_F(CopyVletTest, test_linked_from_append_on_compact) {
    EXPECT_EQ(g_blob_len_map_append.size(), 128 *10);
    BMOCK_NS_CLASS_RESUME(aries::datanode, CopyVletWorker, get_vlet_info, int());
    BMOCK_NS_CLASS_RESUME(aries::datanode, CopyVletWorker, vlet_info, aries::pb::VletInfo&());
    BMOCK_NS_CLASS_RESUME(aries::datanode, VletManager, create, int());
    BMOCK_NS_CLASS_RESUME(aries::datanode, VletManager, add, bool(uint64_t, VletPtr&));
    EXPECT_CALL(BMOCK_NS_CLASS_OBJECT(aries::datanode, VletManager, add), add(_,_))
                 .WillRepeatedly(Return(true));
    EXPECT_CALL(BMOCK_NS_CLASS_OBJECT(aries::datanode, VletManager, create), create(_))
                 .WillRepeatedly(Return(0));
    base::EndPoint endpoint = common::get_local_addr();
    baidu::rpc::Controller cntl;
    google::protobuf::Closure* done = nullptr;
    aries::pb::BalanceVletRequest req;
    aries::pb::AckResponse res;
    req.set_target_disk(2);
    req.set_token(FLAGS_token);
    aries::pb::VletInfo src_info;
    src_info.set_volume_id(100ul);
    src_info.set_vlet_type(VLET_TYPE_APPEND_VARIENT);
    src_info.set_shard_index(1);
    src_info.set_create_time(0);
    src_info.set_vlet_version(1);
    src_info.set_node_addr(common::endpoint2int(endpoint));
    auto src_vlet_engine_options = src_info.mutable_vlet_engine_options();
    src_vlet_engine_options->set_vlet_size(60 * aries::common::GB);
    src_vlet_engine_options->set_smr_zone_size(256 * aries::common::MB);
    src_vlet_engine_options->set_align_size(4 * aries::common::KB);

    auto vlet_info = req.mutable_vlet();
    vlet_info->set_volume_id(100ul);
    vlet_info->set_vlet_type(VLET_TYPE_LINKED_VARIENT);
    vlet_info->set_shard_index(1);
    vlet_info->set_create_time(0);
    vlet_info->set_vlet_version(1);
    vlet_info->set_node_addr(common::endpoint2int(endpoint));
    auto vlet_engine_options = vlet_info->mutable_vlet_engine_options();
    vlet_engine_options->set_vlet_size(56 * aries::common::GB);
    vlet_engine_options->set_block_size(8 * aries::common::MB);
    vlet_engine_options->set_max_record_size(1 * aries::common::MB);
    vlet_engine_options->set_min_record_size(4 * aries::common::KB);
    vlet_engine_options->set_record_gap_page_num(1);
    EXPECT_CALL(BMOCK_NS_CLASS_OBJECT(aries::datanode, CopyVletWorker, src_vlet_info), 
                src_vlet_info())
                .WillRepeatedly(ReturnRef(src_info));
    std::shared_ptr<Vlet> ptr;
    aries::pb::SpaceInfo space_info;
    auto disk_agent = g_datanode->_disk_manager->find(2);
    int ret = Vlet::new_vlet_from_info(disk_agent, *vlet_info, space_info, ptr);
    EXPECT_EQ(ret, 0);
    CopyVletArgContext *ctx = new CopyVletArgContext();
    auto worker = std::make_shared<CopyVletWorker>(ctx, &cntl, done, &req, &res);
    ctx->worker = worker;
    ret = worker->init();
    LOG(NOTICE) << "worker init error msg:" << res.status().msg();
    EXPECT_EQ(ret, 0);
    ptr->set_db(disk_agent->_db);
    ret = ptr->copy_vlet(worker.get());
    ptr->release_db();
    EXPECT_EQ(ret, 0);
    
    //do assert by read;
    auto linked_vlet = dynamic_cast<LinkedVlet*>(ptr.get());
    EXPECT_EQ(linked_vlet->_store->record_num(), 1277);
    EXPECT_EQ(linked_vlet->_store->mark_deleted_record_num(), 3);
    for (int i = 1; i <= 128; ++i) {
        EXPECT_EQ(10, linked_vlet->_store->get_record_count(i));
    }

    linked_vlet->check_balance_vlet_task();
    EXPECT_EQ(linked_vlet->_balance_job_timestamp, 0);
    BMOCK_NS_CLASS_STOP(aries::datanode, CopyVletWorker, get_vlet_info, int());
    BMOCK_NS_CLASS_STOP(aries::datanode, CopyVletWorker, vlet_info, aries::pb::VletInfo&());
    BMOCK_NS_CLASS_STOP(aries::datanode, VletManager, create, int(uint64_t));
    BMOCK_NS_CLASS_STOP(aries::datanode, VletManager, add, bool(uint64_t, VletPtr&));
}

TEST_F(CopyVletTest, read) {
    base::EndPoint endpoint = common::get_local_addr();
    endpoint.port += 1;
    Rpc data(endpoint);

    std::string tmp;

    aries::pb::ShardGetRequest req;
    req.set_token("default_token");
    req.set_need_data(true);
    req.set_need_meta(true);
    req.set_shard_index(1);

    aries::pb::ShardGetResponse res;

    for (auto& kv : g_blob_len_map_append) {
        uint32_t vbid = kv.first;
        uint32_t udata_len = kv.second;

        req.set_volume_id(100);
        req.set_vbid(vbid);

        req.set_need_data(vbid % 2);
        req.set_need_meta(vbid % 3);

        aries::pb::ShardGetResponse res;
        EXPECT_EQ(data.get(&req, &res, &tmp), 0);
        if (vbid == 1 || vbid == 1000 || vbid == 1279) {
            EXPECT_EQ(res.status().code(), (int)AIE_MARK_REMOVED);
            continue;
        }
        EXPECT_EQ(res.status().code(), (int)AIE_OK);

        auto shard_meta = res.shard_meta();
        if (!req.need_data() && !req.need_meta()) {
            EXPECT_EQ(shard_meta.blob_len(), 0);
            EXPECT_EQ(shard_meta.blob_crc(), 0);
            EXPECT_EQ(shard_meta.shard_len(), 0);
        } else {
            EXPECT_EQ(shard_meta.blob_len(), 1333);
            EXPECT_EQ(shard_meta.blob_crc(), 14444);
            EXPECT_EQ(shard_meta.create_time(), 155555);
            EXPECT_EQ(shard_meta.shard_len(), udata_len);
        }
    }
}

TEST_F(CopyVletTest, test_copy_vlet_fence_remove_from_append) {
    system("rm -rf disk1/L_100_1");
    auto disk_agent = g_datanode->_disk_manager->find(1);
    auto indexer = LinkedStoreIndexer::create(100, VLET_TYPE_LINKED_64G_8M_1M, disk_agent->_db);
    indexer->destroy();
    delete indexer;
    
    BMOCK_NS_CLASS_RESUME(aries::datanode, VletManager, add, bool(uint64_t, VletPtr&));
    EXPECT_CALL(BMOCK_NS_CLASS_OBJECT(aries::datanode, VletManager, add), add(_,_))
                 .WillRepeatedly(Return(true));
    BMOCK_NS_CLASS_RESUME(aries::datanode, CopyVletWorker, get_vlet_info, int());
    BMOCK_NS_CLASS_RESUME(aries::datanode, CopyVletWorker, vlet_info, aries::pb::VletInfo&());
    BMOCK_NS_CLASS_RESUME(aries::datanode, CopyVletWorker, src_vlet_info, aries::pb::VletInfo&());
    BMOCK_NS_CLASS_RESUME(aries::datanode, VletManager, create, int(uint64_t));

    aries::pb::VletInfo info;
    info.set_vlet_type(VLET_TYPE_LINKED_64G_8M_1M);
    info.set_volume_id(100ul);
    info.set_shard_index(1);
    info.set_create_time(0ul);
    info.set_disk_id(1);
    info.set_vlet_version(1);
    info.set_state(VLET_STATE_NORMAL);
    EXPECT_CALL(BMOCK_NS_CLASS_OBJECT(aries::datanode, CopyVletWorker, vlet_info), vlet_info())
                 .WillRepeatedly(ReturnRef(info));
    
    base::EndPoint endpoint = common::get_local_addr();
    endpoint.port += 1;
    Rpc data(endpoint);
    baidu::rpc::Controller cntl;
    google::protobuf::Closure* done = nullptr;
    aries::pb::BalanceVletRequest req;
    aries::pb::AckResponse res;
    req.set_target_disk(1);
    req.set_token(FLAGS_token);
    auto vlet_info = req.mutable_vlet();
    vlet_info->set_volume_id(100ul);
    vlet_info->set_vlet_type(VLET_TYPE_APPEND_64G_256M_4K);
    vlet_info->set_shard_index(1);
    vlet_info->set_create_time(100);
    vlet_info->set_vlet_version(1);
    vlet_info->set_node_addr(common::endpoint2int(common::get_local_addr()));
    EXPECT_CALL(BMOCK_NS_CLASS_OBJECT(aries::datanode, CopyVletWorker, src_vlet_info), 
                src_vlet_info())
                .WillRepeatedly(ReturnRef(*vlet_info));
    EXPECT_CALL(BMOCK_NS_CLASS_OBJECT(aries::datanode, VletManager, create), create(_))
                 .WillRepeatedly(Return(0));
    std::shared_ptr<Vlet> ptr;
    aries::pb::SpaceInfo space_info;
    //int ret = Vlet::new_vlet_from_info(disk_agent, *vlet_info, space_info, ptr);
    //EXPECT_EQ(ret, 0);
    CopyVletArgContext *ctx = new CopyVletArgContext();
    auto worker = std::make_shared<CopyVletWorker>(ctx, &cntl, done, &req, &res);
    ctx->worker = worker;
    int ret = worker->init();
    EXPECT_EQ(ret, 0);
    ret = worker->start_copy(ptr);
    EXPECT_EQ(ret, 0);
    usleep(10 * 1000 * 1000);
    
    aries::pb::ShardRemoveRequest req2;
    aries::pb::AckResponse res2;
    req2.set_token("default_token");
    req2.set_volume_id(100);
    req2.set_shard_index(1);
    req2.set_need_mark_delete(false);

    int max_i = 0;
    for (auto& kv : g_blob_len_map_append) {
        if (++max_i > 100) {
            break;
        }
        uint32_t vbid = kv.first;
        uint32_t udata_len = kv.second;

        req2.set_vbid(vbid);

        EXPECT_EQ(data.remove(&req2, &res2), 0);
        EXPECT_NE(res2.status().code(), (int)AIE_OK);
    }
    ptr = worker->_vlet_ptr;
    auto linked_vlet = dynamic_cast<LinkedVlet*>(ptr.get());
    auto tmp = g_datanode->_vlet_manager->find(100);
    tmp->check_balance_vlet_task();
    LOG(TRACE) << tmp->_name << " name";
    disk_agent = g_datanode->_disk_manager->find(0);
    auto append_vlet = dynamic_cast<AppendVlet*>(disk_agent->_vlet_map[100].get());
    LOG(TRACE) << append_vlet << " " << tmp;
    LOG(TRACE) << append_vlet->_balance_job_timestamp << " name";
    EXPECT_GT(tmp->_balance_job_timestamp, 0);
    LOG(TRACE) << "start to wait copy complete";
    sleep(100);
    
    BMOCK_NS_CLASS_STOP(aries::datanode, CopyVletWorker, get_vlet_info, int());
    BMOCK_NS_CLASS_STOP(aries::datanode, CopyVletWorker, vlet_info, aries::pb::VletInfo&());
    BMOCK_NS_CLASS_STOP(aries::datanode, CopyVletWorker, src_vlet_info, aries::pb::VletInfo&());
    BMOCK_NS_CLASS_STOP(aries::datanode, VletManager, create, int(uint64_t));
    BMOCK_NS_CLASS_STOP(aries::datanode, VletManager, add, bool(uint64_t, VletPtr&));
}

TEST_F(CopyVletTest, test_copy_vlet_fence_crc_from_append) {
    system("rm -rf disk1/L_100_1");
    auto disk_agent = g_datanode->_disk_manager->find(1);
    auto indexer = LinkedStoreIndexer::create(100, VLET_TYPE_LINKED_64G_8M_1M, disk_agent->_db);
    indexer->destroy();
    delete indexer;
    Status rets(AIE_CHECKSUM, "record corruption");
    
    BMOCK_NS_CLASS_RESUME(aries::datanode, VletManager, add, bool(uint64_t, VletPtr&));
    EXPECT_CALL(BMOCK_NS_CLASS_OBJECT(aries::datanode, VletManager, add), add(_,_))
                 .WillRepeatedly(Return(true));
    BMOCK_NS_RESUME(base::crc32c, Value, uint32_t(const char* data, size_t n));
    EXPECT_CALL(BMOCK_NS_OBJECT(base::crc32c, Value), Value(_,_))
        .WillRepeatedly(Return(0));
    BMOCK_NS_CLASS_RESUME(aries::datanode, CopyVletWorker, get_vlet_info, int());
    BMOCK_NS_CLASS_RESUME(aries::datanode, CopyVletWorker, vlet_info, aries::pb::VletInfo&());
    BMOCK_NS_CLASS_RESUME(aries::datanode, CopyVletWorker, src_vlet_info, aries::pb::VletInfo&());
    BMOCK_NS_CLASS_RESUME(aries::datanode, VletManager, create, int(uint64_t));

    aries::pb::VletInfo info;
    info.set_vlet_type(VLET_TYPE_LINKED_64G_8M_1M);
    info.set_volume_id(100ul);
    info.set_shard_index(1);
    info.set_create_time(0ul);
    info.set_disk_id(1);
    info.set_vlet_version(1);
    info.set_state(VLET_STATE_NORMAL);
    EXPECT_CALL(BMOCK_NS_CLASS_OBJECT(aries::datanode, CopyVletWorker, vlet_info), vlet_info())
                 .WillRepeatedly(ReturnRef(info));
    EXPECT_CALL(BMOCK_NS_CLASS_OBJECT(aries::datanode, VletManager, create), create(_))
                 .WillRepeatedly(Return(0));
    
    base::EndPoint endpoint = common::get_local_addr();
    endpoint.port += 1;
    Rpc data(endpoint);
    baidu::rpc::Controller cntl;
    google::protobuf::Closure* done = nullptr;
    aries::pb::BalanceVletRequest req;
    aries::pb::AckResponse res;
    req.set_target_disk(1);
    req.set_token(FLAGS_token);
    auto vlet_info = req.mutable_vlet();
    vlet_info->set_volume_id(100ul);
    vlet_info->set_vlet_type(VLET_TYPE_APPEND_64G_256M_4K);
    vlet_info->set_shard_index(1);
    vlet_info->set_create_time(100);
    vlet_info->set_vlet_version(1);
    vlet_info->set_node_addr(common::endpoint2int(common::get_local_addr()));
    EXPECT_CALL(BMOCK_NS_CLASS_OBJECT(aries::datanode, CopyVletWorker, src_vlet_info), 
                src_vlet_info())
                .WillRepeatedly(ReturnRef(*vlet_info));
    std::shared_ptr<Vlet> ptr;
    aries::pb::SpaceInfo space_info;
    //int ret = Vlet::new_vlet_from_info(disk_agent, *vlet_info, space_info, ptr);
    //EXPECT_EQ(ret, 0);
    CopyVletArgContext *ctx = new CopyVletArgContext();
    auto worker = std::make_shared<CopyVletWorker>(ctx, &cntl, done, &req, &res);
    ctx->worker = worker;
    int ret = worker->init();
    EXPECT_EQ(ret, 0);
    ret = worker->start_copy(ptr);
    EXPECT_EQ(ret, 0);
    usleep(40 * 1000 * 1000);
    
    ptr = worker->_vlet_ptr;
    auto linked_vlet = dynamic_cast<LinkedVlet*>(ptr.get());
    auto tmp = g_datanode->_vlet_manager->find(100);
    tmp->check_balance_vlet_task();
    EXPECT_GT(tmp->_balance_job_timestamp, 0);
    LOG(TRACE) << "start to wait copy complete";
    sleep(100);
    
    BMOCK_NS_CLASS_STOP(aries::datanode, CopyVletWorker, get_vlet_info, int());
    BMOCK_NS_CLASS_STOP(aries::datanode, CopyVletWorker, vlet_info, aries::pb::VletInfo&());
    BMOCK_NS_CLASS_STOP(aries::datanode, CopyVletWorker, src_vlet_info, aries::pb::VletInfo&());
    BMOCK_NS_CLASS_STOP(aries::datanode, VletManager, create, int(uint64_t));
    BMOCK_NS_CLASS_STOP(aries::datanode, VletManager, add, bool(uint64_t, VletPtr&));
    BMOCK_NS_STOP(base::crc32c, Value, uint32_t(const char* data, size_t n));
}

} // end namespace of datanode
} // end namespace of aries

int main(int argc, char* argv[]) {
    ::testing::AddGlobalTestEnvironment(new aries::datanode::TestEnvironment());
    ::testing::InitGoogleTest(&argc, argv);
    return RUN_ALL_TESTS();
}

