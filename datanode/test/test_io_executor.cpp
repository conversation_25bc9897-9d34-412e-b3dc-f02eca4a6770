/***************************************************************************
 *
 * Copyright (c) 2018 Baidu.com, Inc. All Rights Reserved
 * $Id$
 *
 **************************************************************************/

/**
* @file test_io_executor.cpp
* <AUTHOR>
* @date 2018/12/19 21:56:11
* @version $Revision$
* @brief
*  io_executor.h/cpp UT
**/

#include "baidu/inf/aries/datanode/disk_agent.h"
#include "baidu/inf/aries/datanode/io_context.h"
#include "baidu/inf/aries/datanode/util/kylin.h"
#include "baidu/inf/aries/common/bmock_util.h"
#include "baidu/inf/aries/datanode/util/util.h"
#include "baidu/inf/aries/datanode/vlet/zone_vlet.h"

namespace aries {
namespace datanode {

using ::testing::Return;
using ::testing::DoAll;
using ::testing::SetArgPointee;
using ::testing::SetArgReferee;
using ::testing::_;

DECLARE_BMOCK_NS_CLASS_METHOD(1, aries::datanode, ZoneVlet, put, int(PutContext*));
DEFINE_BMOCK_NS_CLASS_METHOD(1, aries::datanode, ZoneVlet, put, int(PutContext*));
DECLARE_BMOCK_NS_CLASS_METHOD(1, aries::datanode, ZoneVlet, restore, int(RestoreContext*));
DEFINE_BMOCK_NS_CLASS_METHOD(1, aries::datanode, ZoneVlet, restore, int(RestoreContext*));
DECLARE_BMOCK_NS_CLASS_METHOD(1, aries::datanode, ZoneVlet, change_shard, int(ChangeShardContext*));
DEFINE_BMOCK_NS_CLASS_METHOD(1, aries::datanode, ZoneVlet, change_shard, int(ChangeShardContext*));

DECLARE_BMOCK_NS_CLASS_METHOD(1, aries::datanode, ZoneVlet, do_batch_get, int(GetSegmentContext*));
DEFINE_BMOCK_NS_CLASS_METHOD(1, aries::datanode, ZoneVlet, do_batch_get, int(GetSegmentContext*));

DECLARE_BMOCK_NS_CLASS_METHOD(1, aries::datanode, ZoneVlet, remove, int(RemoveContext*));
DEFINE_BMOCK_NS_CLASS_METHOD(1, aries::datanode, ZoneVlet, remove, int(RemoveContext*));



static std::vector<PutContext*> g_dispatch_ctxs;
static std::mutex g_mutex;

int mock_put(PutContext* ctx) {
    g_mutex.lock();
    g_dispatch_ctxs.push_back(ctx);
    g_mutex.unlock();
    return 0;
}

int mock_restore(RestoreContext* ctx) {
    return 0;
}

int mock_change_shard(ChangeShardContext* ctx) {
    return 0;
}


class IOExecutorTest : public ::testing::TestWithParam<common::DiskType>  {
public:
    IOExecutorTest() {}
    ~IOExecutorTest(){}
    void clear() {
        g_dispatch_ctxs.clear();
    }
    void update_disk_conf(uint64_t ioutil) {
        conf.set_disk_id(0);
        conf.set_disk_path("./");
        conf.set_max_ioutil(ioutil);
        FLAGS_highprio_read_request_unordered = false;
    }

    aries::pb::DiskConfigure conf;
};

INSTANTIATE_TEST_CASE_P(test_io_executor, IOExecutorTest, testing::Values(common::DT_HDD, common::DT_SSD));

TEST_P(IOExecutorTest, dispatch_test) {
    // mock
    BMOCK_CLASS_MOCK_GUARD(ZoneVlet, put);
    BMOCK_CLASS_MOCK_GUARD(ZoneVlet, restore);
    BMOCK_CLASS_MOCK_GUARD(ZoneVlet, change_shard);
    EXPECT_CALL(BMOCK_NS_CLASS_OBJECT(aries::datanode, ZoneVlet, put),
                put(_)).WillRepeatedly(Invoke(mock_put));
    EXPECT_CALL(BMOCK_NS_CLASS_OBJECT(aries::datanode, ZoneVlet, restore),
                restore(_)).WillRepeatedly(Invoke(mock_restore));
    EXPECT_CALL(BMOCK_NS_CLASS_OBJECT(aries::datanode, ZoneVlet, change_shard),
                change_shard(_)).WillRepeatedly(Invoke(mock_change_shard));

    update_disk_conf(90);
    DiskAgent disk_agent(NULL, NULL, conf);
    disk_agent.init(0);
    disk_agent._disk_type = GetParam();
    disk_agent._is_used = true;

    IOExecutor io_executor(&disk_agent);
    io_executor.start(1);

    aries::pb::GenericVletInfo vlet_info;
    auto zns_vlet = std::make_shared<aries::datanode::ZoneVlet>(&disk_agent, vlet_info);

    {
        ::aries::pb::ShardPutRequest request;
        ::aries::pb::ShardPutResponse response;
        request.set_volume_id(1);
        request.set_vbid(2);
        request.set_shard_index(3);
        auto ctx = new aries::datanode::PutContext();
        ctx->request = &request;
        ctx->response = &response;
        ctx->vlet_ptr = zns_vlet;
        ctx->action = PUT_BLOB_ACTION;
        ctx->timeout_ms = 1000000;

        EXPECT_TRUE(io_executor.dispatch((void *)ctx, aries::LOWEST));
    }

    {
        auto restore_ctx = new aries::datanode::RestoreContext();
        ::aries::pb::ShardRestoreRequest request1;
        ::aries::pb::AckResponse response1;
        restore_ctx->request = &request1;
        restore_ctx->response = &response1;
        restore_ctx->vlet_ptr = zns_vlet;
        restore_ctx->action = RESTORE_BLOB_ACTION;
        restore_ctx->timeout_ms = 1000000;

        bool ret = io_executor.dispatch((void *)restore_ctx, aries::LOWEST);
        EXPECT_TRUE(ret);
    }

    {
        auto change_ctx = new aries::datanode::ChangeShardContext();
        ::aries::pb::ShardRestoreRequest request2;
        ::aries::pb::AckResponse response2;
        change_ctx->res = &response2;
        change_ctx->vlet_ptr = zns_vlet;
        change_ctx->sync_point = std::make_shared<common::SyncPoint>(1);
        change_ctx->action = CHANGE_SHARD_ACTION;
        change_ctx->timeout_ms = 1000000;

        bool ret = io_executor.dispatch((void *)change_ctx, aries::LOWEST);
        EXPECT_TRUE(ret);
    }

    {
        auto acquire_ctx = new aries::datanode::AcquireRewriteIOTokenContext();
        acquire_ctx->action = GET_REWRITE_IO_TOKEN_ACTION;
        acquire_ctx->sync_point = std::make_shared<common::SyncPoint>(1);
        acquire_ctx->timeout_ms = 1000000;

        bool ret = io_executor.dispatch((void *)acquire_ctx, aries::LOWEST);
        EXPECT_TRUE(ret);
    }

    {
        BMOCK_CLASS_MOCK_GUARD(ZoneVlet, do_batch_get);
        EXPECT_CALL(BMOCK_NS_CLASS_OBJECT(aries::datanode, ZoneVlet, do_batch_get),
                    do_batch_get(_)).WillRepeatedly(Return(0));

        ::aries::pb::ShardBatchGetRequest request;
        ::aries::pb::ShardBatchGetResponse response;
        auto ctx = new aries::datanode::GetSegmentContext();
        ctx->request = &request;
        ctx->response = &response;
        ctx->vlet_ptr = zns_vlet;
        ctx->action = BATCH_GET_ACTION;
        ctx->timeout_ms = 1000000;
        ctx->vid = 1;

        EXPECT_TRUE(io_executor.dispatch((void *)ctx, aries::LOWEST));
    }

    {
        BMOCK_CLASS_MOCK_GUARD(ZoneVlet, remove);
        EXPECT_CALL(BMOCK_NS_CLASS_OBJECT(aries::datanode, ZoneVlet, remove),
                    remove(_)).WillRepeatedly(Return(0));

        ::aries::pb::ShardRemoveRequest request;
        ::aries::pb::AckResponse response;
        auto ctx = new aries::datanode::RemoveContext();
        ctx->request = &request;
        ctx->response = &response;
        ctx->vlet_ptr = zns_vlet;
        ctx->action = REMOVE_BLOB_ACTION;
        ctx->timeout_ms = 1000000;
        ctx->vid = 1;

        EXPECT_TRUE(io_executor.dispatch((void *)ctx, aries::LOWEST));
    }

    {
        ::aries::pb::PurgeVletRequest request;
        ::aries::pb::AckResponse response;
        auto ctx = new aries::datanode::PurgeContext();
        ctx->request = &request;
        ctx->response = &response;
        ctx->vlet_ptr = zns_vlet;
        ctx->action = PURGE_VLET_ACTION;
        ctx->timeout_ms = 1000000;
        ctx->vid = 1;

        EXPECT_TRUE(io_executor.dispatch((void *)ctx, aries::LOWEST));
    }

    {
        ::aries::pb::UpdateMembershipRequest request;
        ::aries::pb::AckResponse response;
        auto ctx = new aries::datanode::UpdateVletContext();
        ctx->request = &request;
        ctx->response = &response;
        ctx->vlet_ptr = zns_vlet;
        ctx->action = UPDATE_VLET_ACTION;
        ctx->timeout_ms = 1000000;
        ctx->vid = 1;

        EXPECT_TRUE(io_executor.dispatch((void *)ctx, aries::LOWEST));
    }

    {
        ::aries::pb::ShardUpdateMetaRequest request;
        ::aries::pb::AckResponse response;
        auto ctx = new aries::datanode::UpdateMetaContext();
        ctx->request = &request;
        ctx->response = &response;
        ctx->vlet_ptr = zns_vlet;
        ctx->action = UPDATE_META_ACTION;
        ctx->timeout_ms = 1000000;
        ctx->vid = 1;

        EXPECT_TRUE(io_executor.dispatch((void *)ctx, aries::LOWEST));
    }

    {
        io_executor.stop();
        auto restore_ctx = new aries::datanode::RestoreContext();
        ::aries::pb::ShardRestoreRequest request1;
        ::aries::pb::AckResponse response1;
        restore_ctx->request = &request1;
        restore_ctx->response = &response1;
        restore_ctx->vlet_ptr = zns_vlet;
        restore_ctx->action = RESTORE_BLOB_ACTION;
        restore_ctx->timeout_ms = 1000000;

        auto acquire_ctx = new aries::datanode::AcquireRewriteIOTokenContext();
        acquire_ctx->action = GET_REWRITE_IO_TOKEN_ACTION;
        acquire_ctx->sync_point = std::make_shared<common::SyncPoint>(1);
        acquire_ctx->timeout_ms = 1000000;

        auto change_ctx = new aries::datanode::ChangeShardContext();
        ::aries::pb::ShardRestoreRequest request2;
        ::aries::pb::AckResponse response2;
        change_ctx->res = &response2;
        change_ctx->vlet_ptr = zns_vlet;
        change_ctx->sync_point = std::make_shared<common::SyncPoint>(1);
        change_ctx->action = CHANGE_SHARD_ACTION;
        change_ctx->timeout_ms = 1000000;

        bool ret = io_executor.dispatch((void*) restore_ctx, aries::LOWEST);
        EXPECT_TRUE(!ret);
        ret = io_executor.dispatch((void*) acquire_ctx, aries::LOWEST);
        EXPECT_TRUE(!ret);
        ret = io_executor.dispatch((void*) change_ctx, aries::LOWEST);
        EXPECT_TRUE(!ret);
        change_ctx->action = GET_BLOB_ACTION;
        ret = io_executor.dispatch((void*) change_ctx, aries::LOWEST);
        EXPECT_TRUE(!ret);
        change_ctx->action = BATCH_GET_ACTION;
        ret = io_executor.dispatch((void*) change_ctx, aries::LOWEST);
        EXPECT_TRUE(!ret);
        change_ctx->action = GET_RECORD_INDEX_INFO_ACTION;
        ret = io_executor.dispatch((void*) change_ctx, aries::LOWEST);
        EXPECT_TRUE(!ret);
        change_ctx->action = REMOVE_BLOB_ACTION;
        ret = io_executor.dispatch((void*) change_ctx, aries::LOWEST);
        EXPECT_TRUE(!ret);
        change_ctx->action = PURGE_VLET_ACTION;
        ret = io_executor.dispatch((void*) change_ctx, aries::LOWEST);
        EXPECT_TRUE(!ret);
        change_ctx->action = UPDATE_VLET_ACTION;
        ret = io_executor.dispatch((void*) change_ctx, aries::LOWEST);
        EXPECT_TRUE(!ret);
        change_ctx->action = UPDATE_META_ACTION;
        ret = io_executor.dispatch((void*) change_ctx, aries::LOWEST);
        EXPECT_TRUE(!ret);
        change_ctx->action = GET_REWRITE_IO_TOKEN_ACTION;
        ret = io_executor.dispatch((void*) change_ctx, aries::LOWEST);
        EXPECT_TRUE(!ret);
    }

    io_executor.join();

    clear();
}

int mock_put_with_sleep(PutContext* ctx) {
    g_dispatch_ctxs.push_back(ctx);
    ::usleep(200 * 1000);
    return 0;
}

TEST_P(IOExecutorTest, join_test) {
    // mock
    BMOCK_CLASS_MOCK_GUARD(ZoneVlet, put);

    update_disk_conf(90);
    DiskAgent disk_agent(NULL, NULL, conf);
    disk_agent.init(0);
    disk_agent._disk_type = GetParam();
    disk_agent._is_used = true;

    IOExecutor io_executor(&disk_agent);
    io_executor.start(1);

    aries::pb::GenericVletInfo vlet_info;
    auto zns_vlet = std::make_shared<aries::datanode::ZoneVlet>(&disk_agent, vlet_info);

    ::aries::pb::ShardPutRequest request;
    ::aries::pb::ShardPutResponse response;
    request.set_volume_id(1);
    request.set_vbid(2);
    request.set_shard_index(3);
    auto ctx = new aries::datanode::PutContext();
    ctx->request = &request;
    ctx->response = &response;
    ctx->vlet_ptr = zns_vlet;
    ctx->action = PUT_BLOB_ACTION;
    ctx->timeout_ms = 1000000;

    auto start = ::base::gettimeofday_us();
    EXPECT_CALL(BMOCK_NS_CLASS_OBJECT(aries::datanode, ZoneVlet, put),put(_))
            .WillOnce(Invoke(mock_put_with_sleep)).RetiresOnSaturation();

    io_executor.dispatch((void *)ctx, aries::LOWEST);

    EXPECT_CALL(BMOCK_NS_CLASS_OBJECT(aries::datanode, ZoneVlet, put),
                put(_)).Times(3).WillRepeatedly(Invoke(mock_put)).RetiresOnSaturation();
    io_executor.dispatch((void *)ctx, aries::LOWEST);
    io_executor.dispatch((void *)ctx, aries::LOWEST);
    io_executor.dispatch((void *)ctx, aries::LOWEST);

    io_executor.join();

    auto cost = ::base::gettimeofday_us() - start;
    EXPECT_EQ(g_dispatch_ctxs.size(), 4);

    EXPECT_GT(cost, 200 * 1000 - 50 * 1000);

    io_executor.stop();

    clear();
}

TEST_P(IOExecutorTest, order_test) {
    // mock
    BMOCK_CLASS_MOCK_GUARD(ZoneVlet, put);

    update_disk_conf(90);
    DiskAgent disk_agent(NULL, NULL, conf);
    disk_agent.init(0);
    disk_agent._disk_type = GetParam();
    disk_agent._is_used = true;

    IOExecutor io_executor(&disk_agent);
    io_executor.start(1);

    aries::pb::GenericVletInfo vlet_info;
    auto zns_vlet = std::make_shared<aries::datanode::ZoneVlet>(&disk_agent, vlet_info);

    ::aries::pb::ShardPutRequest request;
    ::aries::pb::ShardPutResponse response;
    request.set_volume_id(1);
    auto ctx = new aries::datanode::PutContext();
    ctx->request = &request;
    ctx->response = &response;
    ctx->vlet_ptr = zns_vlet;
    ctx->action = PUT_BLOB_ACTION;
    ctx->timeout_ms = 1000000;

    EXPECT_CALL(BMOCK_NS_CLASS_OBJECT(aries::datanode, ZoneVlet, put),put(_))
            .WillOnce(Invoke(mock_put_with_sleep)).RetiresOnSaturation();
    io_executor.dispatch((void *)ctx, aries::LOWEST);

    EXPECT_CALL(BMOCK_NS_CLASS_OBJECT(aries::datanode, ZoneVlet, put),
                put(_)).Times(2).WillRepeatedly(Invoke(mock_put)).RetiresOnSaturation();
    {
        ::aries::pb::ShardPutRequest* request1 = new ::aries::pb::ShardPutRequest();
        ::aries::pb::ShardPutResponse* response1 = new ::aries::pb::ShardPutResponse();
        request1->set_volume_id(2);
        auto ctx1 = new aries::datanode::PutContext();
        ctx1->request = request1;
        ctx1->response = response1;
        ctx1->vlet_ptr = zns_vlet;
        ctx1->action = PUT_BLOB_ACTION;
        ctx1->timeout_ms = 1000000;
        io_executor.dispatch((void *)ctx1, aries::LOWEST);
    }
    {
        ::aries::pb::ShardPutRequest* request1 = new ::aries::pb::ShardPutRequest();
        ::aries::pb::ShardPutResponse* response1 = new ::aries::pb::ShardPutResponse();
        request1->set_volume_id(3);
        auto ctx1 = new aries::datanode::PutContext();
        ctx1->request = request1;
        ctx1->response = response1;
        ctx1->vlet_ptr = zns_vlet;
        ctx1->action = PUT_BLOB_ACTION;
        ctx1->timeout_ms = 1000000;
        io_executor.dispatch((void *)ctx1, aries::LOWEST);
    }
    io_executor.join();

    EXPECT_EQ(g_dispatch_ctxs.size(), 3);
    EXPECT_EQ(g_dispatch_ctxs[0]->request->volume_id(), 1);
    EXPECT_EQ(g_dispatch_ctxs[1]->request->volume_id(), 2);
    EXPECT_EQ(g_dispatch_ctxs[2]->request->volume_id(), 3);

    clear();
}

TEST_P(IOExecutorTest, executor_priority_test) {
    // mock
    BMOCK_CLASS_MOCK_GUARD(ZoneVlet, put);

    update_disk_conf(90);
    DiskAgent disk_agent(NULL, NULL, conf);
    disk_agent.init(0);
    disk_agent._disk_type = GetParam();
    disk_agent._is_used = true;

    IOExecutor io_executor(&disk_agent);
    io_executor.start(1);

    aries::pb::GenericVletInfo vlet_info;
    auto zns_vlet = std::make_shared<aries::datanode::ZoneVlet>(&disk_agent, vlet_info);

    ::aries::pb::ShardPutRequest request;
    ::aries::pb::ShardPutResponse response;
    request.set_volume_id(1);
    auto ctx = new aries::datanode::PutContext();
    ctx->request = &request;
    ctx->response = &response;
    ctx->vlet_ptr = zns_vlet;
    ctx->action = PUT_BLOB_ACTION;
    ctx->timeout_ms = 1000000;

    EXPECT_CALL(BMOCK_NS_CLASS_OBJECT(aries::datanode, ZoneVlet, put),put(_))
            .WillOnce(Invoke(mock_put_with_sleep)).RetiresOnSaturation();
    io_executor.dispatch((void *)ctx, aries::LOWEST);
    ::usleep(100 * 1000);

    EXPECT_CALL(BMOCK_NS_CLASS_OBJECT(aries::datanode, ZoneVlet, put),
                put(_)).Times(3).WillRepeatedly(Invoke(mock_put)).RetiresOnSaturation();
    {
        ::aries::pb::ShardPutRequest* request1 = new ::aries::pb::ShardPutRequest();
        ::aries::pb::ShardPutResponse* response1 = new ::aries::pb::ShardPutResponse();
        request1->set_volume_id(2);
        auto ctx1 = new aries::datanode::PutContext();
        ctx1->request = request1;
        ctx1->response = response1;
        ctx1->vlet_ptr = zns_vlet;
        ctx1->action = PUT_BLOB_ACTION;
        ctx1->timeout_ms = 1000000;
        io_executor.dispatch((void *)ctx1, aries::LOWEST);
    }
    {
        ::aries::pb::ShardPutRequest* request1 = new ::aries::pb::ShardPutRequest();
        ::aries::pb::ShardPutResponse* response1 = new ::aries::pb::ShardPutResponse();
        request1->set_volume_id(3);
        auto ctx1 = new aries::datanode::PutContext();
        ctx1->request = request1;
        ctx1->response = response1;
        ctx1->vlet_ptr = zns_vlet;
        ctx1->action = PUT_BLOB_ACTION;
        ctx1->timeout_ms = 1000000;
        io_executor.dispatch((void *)ctx1, 6);
    }
    {
        ::aries::pb::ShardPutRequest* request1 = new ::aries::pb::ShardPutRequest();
        ::aries::pb::ShardPutResponse* response1 = new ::aries::pb::ShardPutResponse();
        request1->set_volume_id(4);
        auto ctx1 = new aries::datanode::PutContext();
        ctx1->request = request1;
        ctx1->response = response1;
        ctx1->vlet_ptr = zns_vlet;
        ctx1->action = PUT_BLOB_ACTION;
        ctx1->timeout_ms = 1000000;
        io_executor.dispatch((void *)ctx1, 0);
    }

    io_executor.join();

    EXPECT_EQ(g_dispatch_ctxs.size(), 4);
    EXPECT_EQ(g_dispatch_ctxs[0]->request->volume_id(), 1);
    EXPECT_EQ(g_dispatch_ctxs[1]->request->volume_id(), 4);
    EXPECT_EQ(g_dispatch_ctxs[2]->request->volume_id(), 3);
    EXPECT_EQ(g_dispatch_ctxs[3]->request->volume_id(), 2);

    clear();
}

TEST_P(IOExecutorTest, get_max_execution_time_test) {
    // mock
    BMOCK_CLASS_MOCK_GUARD(ZoneVlet, put);

    update_disk_conf(90);
    DiskAgent disk_agent(NULL, NULL, conf);
    disk_agent.init(0);
    disk_agent._disk_type = GetParam();
    disk_agent._is_used = true;

    IOExecutor io_executor(&disk_agent);
    io_executor.start(1);

    aries::pb::GenericVletInfo vlet_info;
    auto zns_vlet = std::make_shared<aries::datanode::ZoneVlet>(&disk_agent, vlet_info);

    ::aries::pb::ShardPutRequest request;
    ::aries::pb::ShardPutResponse response;
    request.set_volume_id(1);
    auto ctx = new aries::datanode::PutContext();
    ctx->request = &request;
    ctx->response = &response;
    ctx->vlet_ptr = zns_vlet;
    ctx->action = PUT_BLOB_ACTION;
    ctx->timeout_ms = 1000000;

    EXPECT_CALL(BMOCK_NS_CLASS_OBJECT(aries::datanode, ZoneVlet, put),put(_))
            .WillOnce(Invoke(mock_put_with_sleep)).RetiresOnSaturation();
    io_executor.dispatch((void *)ctx, aries::LOWEST);

    ::usleep(100 * 1000);

    EXPECT_TRUE(io_executor.check_hung(90 * 1000));

    io_executor.join();

    EXPECT_FALSE(io_executor.check_hung(10 * 1000));

    clear();
}

TEST_P(IOExecutorTest, concurrent_test) {
    // mock
    BMOCK_CLASS_MOCK_GUARD(ZoneVlet, put);
    EXPECT_CALL(BMOCK_NS_CLASS_OBJECT(aries::datanode, ZoneVlet, put),
                put(_)).WillRepeatedly(Invoke(mock_put));

    update_disk_conf(90);
    DiskAgent disk_agent(NULL, NULL, conf);
    disk_agent.init(0);
    disk_agent._disk_type = GetParam();
    disk_agent._is_used = true;

    IOExecutor io_executor(&disk_agent);
    io_executor.start(4);

    aries::pb::GenericVletInfo vlet_info;
    auto zns_vlet = std::make_shared<aries::datanode::ZoneVlet>(&disk_agent, vlet_info);

    for (int i = 0; i < 1000; i++) {
        ::aries::pb::ShardPutRequest* request1 = new ::aries::pb::ShardPutRequest();
        ::aries::pb::ShardPutResponse* response1 = new ::aries::pb::ShardPutResponse();
        request1->set_volume_id(4);
        auto ctx1 = new aries::datanode::PutContext();
        ctx1->request = request1;
        ctx1->response = response1;
        ctx1->vlet_ptr = zns_vlet;
        ctx1->action = PUT_BLOB_ACTION;
        ctx1->timeout_ms = 1000000;
        io_executor.dispatch((void *)ctx1, 0);
    }

    io_executor.join();
    EXPECT_EQ(g_dispatch_ctxs.size(), 1000);

    clear();
}

TEST_P(IOExecutorTest, reset_test) {
    // mock
    BMOCK_CLASS_MOCK_GUARD(ZoneVlet, put);

    update_disk_conf(90);
    DiskAgent disk_agent(NULL, NULL, conf);
    disk_agent.init(0);
    disk_agent._disk_type = GetParam();
    disk_agent._is_used = true;

    IOExecutor io_executor(&disk_agent);
    io_executor.start(16);

    aries::pb::GenericVletInfo vlet_info;
    auto zns_vlet = std::make_shared<aries::datanode::ZoneVlet>(&disk_agent, vlet_info);

    ::aries::pb::ShardPutRequest request;
    ::aries::pb::ShardPutResponse response;
    request.set_volume_id(1);
    request.set_vbid(2);
    request.set_shard_index(3);
    auto ctx = new aries::datanode::PutContext();
    ctx->request = &request;
    ctx->response = &response;
    ctx->vlet_ptr = zns_vlet;
    ctx->action = PUT_BLOB_ACTION;
    ctx->timeout_ms = 1000000;

    auto start = ::base::gettimeofday_us();
    EXPECT_CALL(BMOCK_NS_CLASS_OBJECT(aries::datanode, ZoneVlet, put),put(_))
            .WillOnce(Invoke(mock_put_with_sleep)).RetiresOnSaturation();

    io_executor.dispatch((void *)ctx, aries::LOWEST);

    EXPECT_CALL(BMOCK_NS_CLASS_OBJECT(aries::datanode, ZoneVlet, put),
                put(_)).Times(3).WillRepeatedly(Invoke(mock_put)).RetiresOnSaturation();
    io_executor.dispatch((void *)ctx, aries::LOWEST);
    io_executor.dispatch((void *)ctx, aries::LOWEST);
    io_executor.dispatch((void *)ctx, aries::LOWEST);

    io_executor.join();

    io_executor.start(16);
}

TEST_F(IOExecutorTest, invalid_test) {
    // mock
    BMOCK_CLASS_MOCK_GUARD(ZoneVlet, put);

    update_disk_conf(90);
    DiskAgent disk_agent(NULL, NULL, conf);
//    disk_agent._disk_type = 5;
    disk_agent.init(0);
    disk_agent._is_used = true;
//
//    {
//        IOExecutor io_executor(&disk_agent);
//        ASSERT_EQ(io_executor.start(16), -1);
//    }

    {
        disk_agent._disk_type = common::DiskType::DT_SSD;
        IOExecutor io_executor(&disk_agent);
        ASSERT_EQ(io_executor.start(16), AIE_OK);
        io_executor.stop();
    }
}

TEST_F(IOExecutorTest, group_test) {
    // mock
    BMOCK_CLASS_MOCK_GUARD(ZoneVlet, put);

    update_disk_conf(90);
    DiskAgent disk_agent(NULL, NULL, conf);
    disk_agent.init(0);
    disk_agent._disk_type = common::DiskType::DT_HDD;
    disk_agent._is_used = true;

    IOExecutor io_executor(&disk_agent);
    io_executor.start(16);

    aries::pb::GenericVletInfo vlet_info;
    auto zns_vlet = std::make_shared<aries::datanode::ZoneVlet>(&disk_agent, vlet_info);

    ::aries::pb::ShardPutRequest request;
    ::aries::pb::ShardPutResponse response;
    request.set_volume_id(1);
    request.set_vbid(2);
    request.set_shard_index(3);
    auto ctx = new aries::datanode::PutContext();
    ctx->request = &request;
    ctx->response = &response;
    ctx->vlet_ptr = zns_vlet;
    ctx->action = PUT_BLOB_ACTION;
    ctx->timeout_ms = 1000000;
    ctx->vid = 1;

    auto start = ::base::gettimeofday_us();

    EXPECT_CALL(BMOCK_NS_CLASS_OBJECT(aries::datanode, ZoneVlet, put),
                put(_)).Times(3).WillRepeatedly(Invoke(mock_put_with_sleep)).RetiresOnSaturation();
    io_executor.dispatch((void *)ctx, aries::LOWEST);
    io_executor.dispatch((void *)ctx, aries::LOWEST);
    io_executor.dispatch((void *)ctx, aries::LOWEST);

    io_executor.join();
    uint64_t cost = ::base::gettimeofday_us() - start;

    EXPECT_EQ(g_dispatch_ctxs.size(), 3);

    EXPECT_GT(cost, 200 * 1000 * 3 - 50 * 1000);
}

}
}

/* vim: set ts=4 sw=4 sts=4 tw=100 */
