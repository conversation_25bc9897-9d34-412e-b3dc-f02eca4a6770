// Copyright (c) 2017 Baidu.com, Inc. All Rights Reserved
// Author: chenjiang,<EMAIL>

#ifndef BAIDU_INF_ARIES_DATANODE_CLEANER_H
#define BAIDU_INF_ARIES_DATANODE_CLEANER_H

#include <atomic>
#include <sys/statvfs.h>
#include "baidu/inf/aries/datanode/util/kylin.h" // CAsyncClient, QueueExec, AsyncContext
#include "baidu//inf/aries-api/common/config.h"
#include "baidu/inf/aries/datanode/flags.h"
#include "baidu/inf/aries-api/common/types.h"
#include "baidu/inf/aries/datanode/util/util.h"
#include "baidu/inf/aries/datanode/disk_agent.h"
#include "baidu/inf/aries/datanode/master_caller.h"
#include "baidu/inf/aries/datanode/vlet.h"

#include "baidu/inf/aries/datanode/storage/append/zonefile.h"

namespace aries {
namespace datanode {

struct VletReportContext : public AsyncContext {
    VletReportContext() {
        InitAsyncContext(this);
    }
    void push_back(const aries::pb::DatanodeVletInfo& vlet_info) {
        vlets.push_back(vlet_info);
    }
    std::vector<aries::pb::DatanodeVletInfo> vlets;
};

enum CleanAction {
    CLEAN_TIMER_ACTION = 0,
    VLET_REPORT_ACTION = 1,
    JOIN_DISK_REPORT_ACTION = 2
};

class Cleaner : public CAsyncClient {
public:
    Cleaner() {
        InitAsyncContext(&_timer_context);
        _name = "Cleaner ";
    }

    virtual int Release() {
        // do noting
        return 1;
    }

    int init(int host_id, const std::map<int, DiskAgentPtr>& disk_agent_map) {
        _disk_agent_map = disk_agent_map;
        LOG(NOTICE) << _name << "init, host_id:" << host_id
                << "disk_num:" << disk_agent_map.size();
        for (auto& kv : _disk_agent_map) {
            _vec.push_back(new DelLeakFileContext(kv.second));
        }

        m_nHostId = host_id;
        return ++host_id;
    }

    void start() {
        _stopped = false;
        DelayExec(CLEAN_TIMER_ACTION, this, FLAGS_diff_leak_file_interval_s * 1000UL, &_timer_context);
    }

    void stop();

private:
    int do_diff() {
        std::shared_ptr<common::SyncPoint> sync_point(new common::SyncPoint(_disk_agent_map.size()));
        int i = 0;
        for (auto& kv : _disk_agent_map) {
            if (_vec[i]->running) {
                sync_point->signal();
                i++;
                continue;
            }
            _vec[i]->running = true;
            _vec[i]->reset();
            _vec[i]->sync_point = sync_point;
            _vec[i]->nAction = CLEAN_LEAK_FILE_ACTION;
            kv.second->heavy_worker()->dispatch(_vec[i]);
            i++;
        }
        sync_point->wait_ms(FLAGS_disk_hung_second * 1000);
        sync_point.reset();

        if (_stopped.load()) {  //make promise that ctx->vlets is not create by DiskAgent::on_suicide
            return 0;
        }

        for (auto& ctx  : _vec) {
            if (ctx->running) {
                continue;
            }
            //move to trash
            for (auto& vlet : ctx->vlets) {
                int ret = move_vlet_to_trash(vlet.c_str());
                if (ret == AIE_OK) {
                    LOG(WARNING) << "move vlet to trash succ, path" << vlet.c_str();
                } else {
                    LOG(WARNING) << "move vlet to trash failed, path" << vlet.c_str() << " with error code:" << ret;
                }
            }
            //delete trash 
            for (auto& vlet : ctx->trash_vlets) {
                g_fs->unlink(vlet.c_str());
                LOG(WARNING) << "delete path:" << vlet.c_str();
            }
            //delete zonefile(not use)
            for (auto& zonefile : ctx->zonefiles) {
                g_fs->unlink(zonefile.c_str());
                LOG(WARNING) << "delete path:" << zonefile.c_str();
            }
            //delete trash zone vlets
            for (const auto& volume_key : ctx->trash_zone_vlets) {
                auto* zone_disk = ctx->disk_agent->get_zone_disk();
                zone_disk->destroy_vlet(volume_key);
                LOG(WARNING) << "destroy trash zone vlet, vid:" << volume_key.volume_id();
            }
        }
        return 0;
    }

    virtual void OnCompletion(AsyncContext* pCtx);

    int move_vlet_to_trash(const std::string& src_path);

private:
    std::atomic<bool> _stopped{false};
    MasterCaller _master_caller;
    AsyncContext _timer_context;
    std::map<int, DiskAgentPtr> _disk_agent_map;
    std::vector<DelLeakFileContext*> _vec;

    std::string _name;
};

} // end namespace of datanode
} // end namespace of aries

#endif 

/* vim: set ts=4 sw=4 sts=4 tw=100 */
