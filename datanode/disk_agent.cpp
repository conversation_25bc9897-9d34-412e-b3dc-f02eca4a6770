// Copyright (c) 2016 Baidu.com, Inc. All Rights Reserved
// Author <PERSON><PERSON><PERSON><PERSON><PERSON>(chen<PERSON><PERSON><PERSON>@baidu.com)
// Date: Sun Oct  9 10:03:50 CST 2016

#include <assert.h>
#include <unistd.h>
#include <fcntl.h>
#include <sys/types.h>
#include <sys/stat.h>
#include <dirent.h>
#include <map>
#include <stack>
#include <memory>
#include <algorithm>

#include "base/crc32c.h"
#include "baidu/inf/aries/datanode/disk_agent.h"
#include "baidu/inf/aries/datanode/io_context.h"
#include "baidu/inf/aries/datanode/flags.h"
#include "baidu/inf/aries-api/common/common.h"

#include "baidu/inf/aries/common/common.h"
#include "baidu/inf/aries/common/sync_point.h"
#include "baidu/inf/aries/datanode/io_scheduler.h"
#include "baidu/inf/aries/datanode/throttle/flow_control.h"
#include "baidu/inf/aries/datanode/vlet.h"
#include "baidu/inf/aries/datanode/vlet/lsm_vlet.h"
#include "baidu/inf/aries/datanode/vlet/linked_vlet.h"
#include "baidu/inf/aries/datanode/vlet/append_vlet.h"
#include "baidu/inf/aries/datanode/vlet/zone_vlet.h"
#include "baidu/inf/aries/datanode/util/util.h"
#include "baidu/inf/aries/datanode/copy_vlet.h"
#include "baidu/inf/aries/datanode/vlet_indexer.h"

#include "rocksdb/db.h"
#include "rocksdb/table.h"
#include "rocksdb/slice.h"
#include "rocksdb/options.h"
#include "rocksdb/cache.h"
#include "rocksdb/convenience.h"
#include "rocksdb/filter_policy.h"
#include "base/files/file_enumerator.h"
#include "baidu/inf/aries-api/common/rpc_call.h"
#include "baidu/inf/aries-api/common/bvar_define.h"
#include "baidu/inf/aries/datanode/cleaner.h"
#include "baidu/inf/aries/common/common.h"
#include "baidu/inf/aries/datanode/storage/zone/device/file_zone_device_backend.h"
#include "baidu/inf/aries/datanode/storage/zone/channel/channel_bvar.h"
#include "baidu/inf/aries/datanode/storage/zone/channel/channel_group.h"
#include "baidu/inf/aries/datanode/storage/zone/zone_meta_persistor.h"

namespace aries {
namespace datanode {

ARIES_BVAR_COUNTER(datanode, disk_agent_queue_size);
common::BvarStatusWithTag<bool> g_monitor_enable_ioprio_bvar("datanode", "enable_ioprio");
common::BvarStatusWithTag<int32_t> g_bvar_disk_ioutil("datanode", "ioutil");
common::BvarStatusWithTag<int32_t> g_bvar_physical_disk_read_iops("datanode", "physical_read_iops");
common::BvarStatusWithTag<int32_t> g_bvar_physical_disk_write_iops("datanode", "physical_write_iops");
common::BvarStatusWithTag<int32_t> g_bvar_physical_disk_read_throughput("datanode", "physical_read_throughput");
common::BvarStatusWithTag<int32_t> g_bvar_physical_disk_write_throughput("datanode", "physical_write_throughput");
common::BvarStatusWithTag<int32_t> g_bvar_aries_disk_read_iops("datanode", "aries_read_iops");
common::BvarStatusWithTag<int32_t> g_bvar_aries_disk_write_iops("datanode", "aries_write_iops");
common::BvarStatusWithTag<int32_t> g_bvar_aries_disk_read_throughput("datanode", "aries_read_throughput");
common::BvarStatusWithTag<int32_t> g_bvar_aries_disk_write_throughput("datanode", "aries_write_throughput");
common::BvarStatusWithTag<uint64_t> g_bvar_aries_disk_logical_used_size("datanode", "aries_logical_used_size");

common::BvarStatusWithTag<uint64_t> g_bvar_aries_disk_user_channels_write_bytes("datanode", "user_channels_write_bytes");
common::BvarStatusWithTag<uint64_t> g_bvar_aries_disk_total_user_channels_write_bytes("datanode", "total_user_channels_write_bytes");
common::BvarStatusWithTag<uint64_t> g_bvar_aries_disk_total_user_channels_write_bytes_seconds_window("datanode", "total_user_channels_write_bytes_seconds_window");
common::BvarStatusWithTag<uint64_t> g_bvar_aries_disk_rewrite_channels_write_bytes("datanode", "rewrite_channels_write_bytes");
common::BvarStatusWithTag<uint64_t> g_bvar_aries_disk_total_rewrite_channels_write_bytes("datanode", "total_rewrite_channels_write_bytes");
common::BvarStatusWithTag<uint64_t> g_bvar_aries_disk_total_rewrite_channels_write_bytes_seconds_window("datanode", "total_rewrite_channels_write_bytes_seconds_window");
common::BvarStatusWithTag<uint64_t> g_bvar_aries_disk_total_channels_write_bytes("datanode", "total_channels_write_bytes");
common::BvarStatusWithTag<uint64_t> g_bvar_aries_disk_total_channels_write_bytes_seconds_window("datanode", "total_channels_write_bytes_seconds_window");
common::BvarStatusWithTag<uint64_t> g_bvar_aries_disk_rewrite_zone_used_size_accumulation("datanode", "rewrite_zone_used_size_accum");
common::BvarStatusWithTag<uint64_t> g_bvar_aries_disk_rewrite_zone_num("datanode", "rewrite_zone_num");
common::BvarStatusWithTag<double> g_bvar_aries_disk_valid_data_percent("datanode", "valid_data_percent");
common::BvarStatusWithTag<double> g_bvar_aries_disk_usage("datanode", "usage");
common::BvarStatusWithTag<double> g_bvar_aries_disk_write_amplification("datanode", "write_amplification");
common::BvarStatusWithTag<double> g_bvar_aries_disk_write_amplification_seconds_window("datanode", "write_amplification_seconds_window");

boost::regex _s_trash_pattern("[A-Z]_(\\d+)_(\\d+)\\..*");
std::shared_ptr<rocksdb::Cache>     DiskAgent::s_block_cache;
std::shared_ptr<rocksdb::Cache>     DiskAgent::s_block_cache_for_ssd;

CreateVletContext::~CreateVletContext() {
    if (worker) {
        worker = nullptr;
    }
}

inline uint64_t diff_point_time() {
    uint64_t current_time_us = base::gettimeofday_us();
    uint64_t diff_point_time = current_time_us - 
                    (FLAGS_disk_vlet_fresh_time_second * 1000 * 1000UL);

    return diff_point_time;
}

bool parse_vlet(std::string name, uint64_t* vid, uint32_t* shard) {
    size_t pos = name.rfind('/');
    if (pos != std::string::npos) {
        name = name.substr(pos + 1);
    }
    pos = name.find('_');
    if (pos == std::string::npos) {
        return false;
    }
    size_t pos1 = name.find('_', pos + 1);
    if (pos1 == std::string::npos) {
        return false;
    }
    std::string str = name.substr(pos + 1, pos1 - pos - 1);
    char *str_end = NULL;
    *vid = strtoull(str.c_str(), &str_end, 10);
    if (str_end != (str.c_str() + str.size())) {
        return false;
    }
    str = name.substr(pos1 + 1);
    str_end = NULL;
    *shard = strtol(str.c_str(), &str_end, 10);
    if (str_end != (str.c_str() + str.size())) {
        return false;
    }

    return true;
}

DiskAgent::~DiskAgent() {
    LOG(NOTICE) << "Diskagent released, name:" << _name;
    common::ScopedMutexLock lock(_mutex);
    _running_copy_works.clear();
    _running_create_works.clear();
}

int DiskAgent::set_block_cache(uint64_t capacity_mb) {
    s_block_cache = rocksdb::NewLRUCache(capacity_mb * 1024 * 1024, 5);
    return 0;
}

int DiskAgent::set_block_cache_for_ssd(uint64_t capacity_mb) {
    s_block_cache_for_ssd = rocksdb::NewLRUCache(capacity_mb * 1024 * 1024, 5);
    return 0;
}

DiskAgent::DiskAgent(DiskManager* disk_mgr,
        VletManager* vlet_mgr,
        const aries::pb::DiskConfigure& conf)
    : _disk_manager(disk_mgr),
    _vlet_manager(vlet_mgr),
    _disk_conf(conf),
    _io_executor(this) {

    InitAsyncContext(&_timer_context);
    InitAsyncContext(&_purge_timer_context);

    _purge_working = false;
    _is_used = false;
    _is_bad_disk = false;
    _is_stop = false;
    _used_size = 0;
    _disk_total_size = 0;
    _disk_free_size = 0;
    _aries_capacity = 0;
    _current_ioutil = 0;
    _current_svct = 0;
    _current_read_iops = 0;
    _current_read_throughput = 0;
    _current_write_iops = 0;
    _current_write_throughput = 0;
    _max_iops = conf.has_max_iops() ? conf.max_iops() : 100;
    _max_ioutil = conf.has_max_ioutil() ? conf.max_ioutil() : 100;
    _disk_type = common::string2disk_type(_disk_conf.disk_type());
    _shard_checker_ptr = std::make_shared<ShardChecker>();

    std::string device_type_str = _disk_conf.zone_device_type();
    std::transform(device_type_str.begin(), device_type_str.end(), device_type_str.begin(), ::toupper);
    auto device_type = string_to_enum(ZoneDeviceType, device_type_str);
    _zone_device_type = (device_type.has_value() ? device_type.value() : ZoneDeviceType::INVALID);

    base::string_printf(&_name, "DiskAgent disk_id:%d ", disk_id());
    LOG(NOTICE) << "new " << _name << "path:" << disk_path()
                << " disk_type:" << common::disk_type2string(_disk_type);
}

uint32_t DiskAgent::init(int hostid) {
    m_nHostId = hostid++;

    _io_thread_num = FLAGS_thread_per_disk;
    if (_disk_type == common::DT_SSD || _disk_type == common::DT_ZONE_SSD) {
        _io_thread_num = FLAGS_thread_per_ssd_disk;
    }
    assert(_io_thread_num > 0);

    // test support ioprio
    std::string dev_name = universalfs::get_mount_dev(disk_path());
    _is_support_ioprio = universalfs::is_support_ioprio(dev_name);
    g_monitor_enable_ioprio_bvar.put("disk" + std::to_string(disk_id()), _is_support_ioprio);

    LOG(NOTICE) << _name << "init host_id:" << m_nHostId;

    return hostid;
}

void DiskAgent::reload() {
    g_datanode_err_disk_num_adder << -1;
    _is_used = true;
    _is_bad_disk = false;
    _create_time = base::gettimeofday_us();
    {
        static common::MutexLock dump_lock;
        common::ScopedMutexLock dump_guard(dump_lock);

        _disk_conf.set_is_used(true);
        _disk_manager->dump_disk_conf();
    }
    // start io_executor and heavyworker;
    start_io_executor();
    start_heavyworker();
    init_ioutil_collector();

    if (_bg_executor == nullptr) {
        _bg_executor = std::make_unique<folly::CPUThreadPoolExecutor>(FLAGS_bg_threadpool_thread_num);
    }
    _compact_db_thread = std::thread{ std::bind(&DiskAgent::compact_db, this) };

    // test support ioprio
    std::string dev_name = universalfs::get_mount_dev(disk_path());
    _is_support_ioprio = universalfs::is_support_ioprio(dev_name);
    g_monitor_enable_ioprio_bvar.put("disk" + std::to_string(disk_id()), _is_support_ioprio);
    LOG(NOTICE) << _name << "reload host_id:" << m_nHostId;
}

void DiskAgent::reload_configure() {
    if (_flow_control) {
        _flow_control->reload_configure();
    }
}

void DiskAgent::on_drop_vlet(DropVletContext* ctx) {
    VletPtr vlet_ptr = ctx->vlet_ptr;
    auto response = ctx->response;
    auto status = response->mutable_status();

    if (!_is_used) {
        status->set_code(AIE_OK);
        status->set_msg("disk is offline");
        delete ctx;
        return;
    }

    // delete VolumeKey
    // Do not delete it directly here. Considering what will happen if:
    //   1. db->Delete(volume_key);
    //   2. [Power failure]
    //   3. [reboot datanode]
    // In this case, the VolumeKey is gone, but the index for records are
    // still in db. Only by scanning the db can we sweep out the residues.
    // Therefore, here we set VolumeKey to empty as a delete mark. It is
    // physically deleted after the records' index are cleared.
    // Currently this is useful for only ZoneDisk.
    VolumeKey volume_key(vlet_ptr->volume_id(), vlet_ptr->vlet_type());
    Status s = _vlet_indexer->remove(volume_key);
    if (!s.ok()) {
        LOG(WARNING) << _name << "drop_vlet failed due to delete db failed, status:" << s;
        status->set_code(AIE_OK);
        status->set_msg("disk is offline");

        // meta failed, drop this disk
        std::string msg = base::string_printf("drop vlet, db delete error, log_id: %lu", ctx->log_id);
        if (s.code() == AIE_IO_ERROR) {
            set_bad_disk(msg);
        }
        delete ctx;
        return;
    }

    // drop success
    drop_vlet(vlet_ptr);

    auto* cache_mgr = _disk_manager->cache_manager();
    if (FLAGS_enable_cache && cache_mgr->enabled()) {
        cache_mgr->drop_vlet(vlet_ptr->volume_id(), (int32_t)vlet_ptr->shard_index());
    }

    LOG(NOTICE) << _name << "drop_vlet succeeded, vlet:" << vlet_ptr->vlet_identify();
    {
        common::ScopedMutexLock lock(_mutex);
        _need_compact_db_vlets.push_back(vlet_ptr->volume_id());
    }
    // Note: do not dispatch drop_vlet request to IOExecutor because drop_vlet need
    // remove all vlet index from db which is very costly and leaving it to DiskAgent kylin thread.
    // It is safe to drop vlet here even disk will be droppped soon.
    const uint64_t log_id = ctx->log_id;
    auto ret = vlet_ptr->drop(ctx);

    if (ret < -1) {
        std::string msg = base::string_printf("drop vlet io error, log_id: %lu", log_id);
        set_bad_disk(msg);
    }
}

void DiskAgent::set_bad_disk(const std::string& msg) {
    // will be dropped by gc thread
    if (_is_bad_disk == false) {
        _is_bad_disk = true;
        auto tmp = new AsyncContext();
        InitAsyncContext(tmp);
        QueueExec(DIFF_DISK_ACTION, g_datanode->gc(), tmp);
    }
    LOG(WARNING) << _name << " set bad disk, reason: " << msg;
}

void DiskAgent::stop_for_error() {
    uint64_t log_id = base::fast_rand();
    LOG(WARNING) << _name << "drop_disk for stop disk agent for error, log_id:" << log_id;
    auto drop_disk_ctx = new DropDiskReq();

    drop_disk_ctx->log_id = log_id;

    QueueExecEmergent(DROP_DISK_ACTION, this, drop_disk_ctx);
}

void DiskAgent::on_drop_disk(DropDiskContext* ctx) {
    LOG(WARNING) << _name << " will be dropped";
    {
        std::unique_ptr<DropDiskContext> hold_ctx(ctx);

        auto response = ctx->response;
        auto status = response->mutable_status();

        if (!_is_used) {
            status->set_code(AIE_OK);
            status->set_msg("disk is offline");
            return;
        }

        // removed all vlet from vlet_manager
        _vlet_manager->erase(_vlet_map);

        auto* cache_mgr = _disk_manager->cache_manager();
        if (FLAGS_enable_cache && cache_mgr->enabled()) {
            for (const auto& kv : _vlet_map) {
                cache_mgr->drop_vlet(kv.first, (int32_t)kv.second->shard_index());
            }
        }

        _is_used = false;
        g_datanode_err_disk_num_adder << 1;
        {
            static common::MutexLock dump_lock;
            common::ScopedMutexLock dump_guard(dump_lock);

            _disk_conf.set_is_used(false);
            _disk_manager->dump_disk_conf();
        }
    }

    stop();
}

void DiskAgent::on_add_disk(AddDiskContext* ctx) {
    std::unique_ptr<AddDiskContext> hold_ctx(ctx);

    auto response = ctx->response;
    auto status = response->mutable_status();

    if (_is_used.load()) {
        if (_vlet_map.size()) {
            status->set_code(AIE_EXIST);
            status->set_msg("disk already added and has data");
        } else {
            status->set_code(AIE_OK);
            status->set_msg("disk already added");
        }
        return;
    } else {
        if (_vlet_map.size()) {
            status->set_code(AIE_EXIST);
            status->set_msg("disk has data, not allowed");
            return;
        }
    }

    if (_db) {
        status->set_code(AIE_FAIL);
        status->set_msg("disk is dropping");
        return;
    }

    // begin to open meta database
    if (db_open()) {
        // open db failed
        status->set_code(AIE_FAIL);
        status->set_msg("db opened failed");
        return;
    }

    auto iter = _db->NewIterator(rocksdb::ReadOptions());
    iter->SeekToFirst();

    bool db_ok = false;
    if (iter->Valid()) {
        LOG(WARNING) << _name << "add_disk failed due to db is not empty";
        status->set_code(AIE_FAIL);
        status->set_msg("disk not empty");
    } else if (iter->status().ok()) {
        db_ok = true;
    } else {
        LOG(WARNING) << _name << "add_disk failed due to db read failed";
        status->set_code(AIE_FAIL);
        status->set_msg("disk read error");
    }

    delete iter;

    if (!db_ok) {
        db_close();
        return;
    }

    if (_disk_type == common::DT_INVALID) {
        status->set_code(AIE_FAIL);
        status->set_msg("disk type error");
        db_close();
        LOG(FATAL) << " disk type illigle:" << _disk_conf.disk_type();
        return;
    }
    if ((_disk_type != common::DT_ZONE_SSD &&  _zone_device_type != ZoneDeviceType::INVALID)
        || (_disk_type == common::DT_ZONE_SSD && _zone_device_type == ZoneDeviceType::INVALID)) {
        status->set_code(AIE_FAIL);
        status->set_msg("added to non-zone disk");
        LOG(FATAL) << _name << "disk type is invalid, disk_type:" << _disk_conf.disk_type()
                   << " zone_device_type:" << enum_to_string(_zone_device_type);
        return;
    }

    //init ufs.
    auto s = g_fs->register_disk(disk_path().data(), _disk_conf.dev_path(), _disk_type, 0);
    if (!s.ok() && s.code() != AIE_EXIST) {
        LOG(WARNING) << "register disk failed, path:" << disk_path()
                     << " dev path:" << _disk_conf.dev_path() << " status:" << s;
        status->set_code(AIE_FAIL);
        status->set_msg("disk is dropping");
        db_close();
        return;
    }

    _flow_control.reset(new FlowControl(disk_id()));
    if (Status s = _flow_control->init(); !s.ok()) {
        LOG(WARNING) << "init flow control failed, disk_id:" << disk_id()
                << " status:" << s;
        status->set_code(AIE_FAIL);
        status->set_msg("init flow control failed");
        return;
    }

    if (is_zone_disk()) {
        int ret = open_zone_disk();
        unlikely_if (ret != AIE_OK) {
            LOG(FATAL) << "failed to open zone disk with disk_id:" << disk_id();
            status->set_code(AIE_FAIL);
            status->set_msg("open zone disk error");
            db_close();
            return;
        }

        check_zone_device_space();
        prepare_zone_resource();
    } else {
        //init ufs.
        auto s = g_fs->register_disk(disk_path().data(), _disk_conf.dev_path(), _disk_type, 0);
        if (!s.ok() && s.code() != AIE_EXIST) {
            LOG(WARNING) << "register disk failed, path:" << disk_path()
                         << " dev path:" << _disk_conf.dev_path() << " status:" << s;
            status->set_code(AIE_FAIL);
            status->set_msg("disk is dropping");
            db_close();
            return;
        }
    }

    reload();
}

void DiskAgent::on_diff_disk(DiffDiskContext* ctx) {
    ctx->is_hung = false;

    if (_is_stop) {
        ctx->sync_point->signal();
        ctx->running = false;;
        return;
    }

    // disk is offline
    if (!_is_used || _is_bad_disk) {
        if (ctx->is_used && _create_time < diff_point_time()) {
            // need to drop disk from master
            ctx->need_to_drop = true;
            LOG(WARNING) << "found a disk not used or bad but master still have it, name:" << _name
                << "is_used:" << _is_used << " is_bad_disk:" << _is_bad_disk;
        }
        ctx->sync_point->signal();
        ctx->running = false;;
        return ;
    }

    // disk is online
    if (!ctx->is_used) {
        if (_create_time < diff_point_time()) {
            // get gc disk token
            MasterCaller master_caller;
            aries::pb::GetGcDiskTokenRequest request;
            aries::pb::AckResponse ack;
            request.set_token(FLAGS_token);
            if (master_caller.get_gc_disk_token(&request, &ack) != AIE_OK) {
                LOG(WARNING) << "get gc disk token failed, disk:" << disk_id() << ", skip gc disk";
                ctx->sync_point->signal();
                ctx->running = false;;
                return;
            }

            // need to drop disk for myself
            // removed all vlet from vlet_manager
            _vlet_manager->erase(_vlet_map);

            _is_used = false;
            {
                static common::MutexLock dump_lock;
                common::ScopedMutexLock dump_guard(dump_lock);

                _disk_conf.set_is_used(false);
                _disk_manager->dump_disk_conf();
            }

            LOG(WARNING) << _name << " will be dropped";
            stop();
        }
        ctx->sync_point->signal();
        ctx->running = false;;
        return ;
    }

    // do vlet diff

    diff_vlet(ctx);
    ctx->sync_point->signal();
    ctx->running = false;;
}

int DiskAgent::diff_vlet(DiffDiskContext* ctx) {
    std::map<uint64_t, VletIdentifyWithMembership>& vlet_map = ctx->vlet_map;
    std::vector<VletIdentify>&   node_remove_vlet_list = ctx->node_remove_vlet_list;
    std::vector<VletIdentify>&   master_drop_vlet_list = ctx->master_drop_vlet_list;
    std::vector<uint64_t>&   need_update_vlet_list = ctx->need_update_vlet_list;
    need_update_vlet_list.clear();
    node_remove_vlet_list.clear();
    master_drop_vlet_list.clear();

    uint64_t diff_point = diff_point_time();

    for (auto& kv : vlet_map) {
        auto& vlet_id_with_membership = kv.second;
        auto& vlet_id = vlet_id_with_membership.vlet_id;
        auto iter = _vlet_map.find(vlet_id.volume_id);
        VletIdentify id;
        if (_vlet_map.end() != iter) {
            id = iter->second->vlet_identify();
            if (vlet_id == id) {
                continue;
            } else {
                LOG(WARNING) << "found different vlet, master_vlet:" << vlet_id << " datanode_vlet:" << id;
            }
        }

        if (vlet_id.create_time < diff_point) {
            master_drop_vlet_list.push_back(vlet_id);
            LOG(WARNING) << "need to drop vlet from master, master_vlet:" << vlet_id << " datanode_vlet:" << id;
        }
    }

    for (auto& kv : _vlet_map) {
        VletIdentify id = kv.second->vlet_identify();
        auto iter = vlet_map.find(id.volume_id);
        VletIdentify vlet_id;
        if (vlet_map.end() != iter) {
            auto& vlet_id_with_membership = iter->second;
            vlet_id = vlet_id_with_membership.vlet_id;
            if (vlet_id == id) {
                if (vlet_id.volume_version > id.volume_version ||
                    vlet_id.max_holes_size_for_fast_remove != id.max_holes_size_for_fast_remove ||
                    vlet_id.min_record_size_for_fast_remove != id.min_record_size_for_fast_remove ||
                    vlet_id.permit_fast_remove != id.permit_fast_remove ||
                    vlet_id.append_zone_rewrite_rate != id.append_zone_rewrite_rate ||
                    vlet_id.daily_rewrite_start_time != id.daily_rewrite_start_time ||
                    vlet_id.daily_rewrite_duration_second != id.daily_rewrite_duration_second ||
                    vlet_id.use_standard_record_layout != id.use_standard_record_layout ||
                    VletIdentify::has_diff_shard_compress_option(vlet_id.shard_compress_option, id.shard_compress_option)) {
                        need_update_vlet_list.push_back(id.volume_id);
                }
                continue;
            }
        }

        if (id.create_time < diff_point) {
            node_remove_vlet_list.push_back(id);
            LOG(WARNING) << _name << "need to remove vlet from node, master_vlet:" << vlet_id << " datanode_vlet:" << id;
        }
    }

    return 0;
}

void DiskAgent::on_show_disk(ShowDiskContext* ctx) {
    auto& ret_str = ctx->ret_str;
    ctx->is_used = _is_used;
    ctx->used_size = used_size();
    ctx->disk_total_size = _disk_total_size;
    ctx->disk_free_size = _disk_free_size;
    ctx->aries_capacity = _aries_capacity;
    if (ctx->disk_conf != nullptr) {
        ctx->disk_conf->CopyFrom(_disk_conf);
    }

    if (_is_used) {
        uint64_t disk_used_size = used_size();
        disk_used_size /= (1024 * 1024 * 1024);
        std::string max_iops = _max_iops != 100 ?
            base::string_printf("\tmax_iops=%lu", _max_iops) : "";
        std::string max_ioutil = _max_ioutil != 100 ?
            base::string_printf("\tmax_ioutil=%lu", _max_ioutil): "";
        base::string_printf(&ret_str,
                "disk_id=%d\tdisk_path=%s\tvlet_number=%lu\tused_size=%luGB\t%s%s\n",
                disk_id(), disk_path().c_str(), _vlet_map.size(), disk_used_size,
                max_iops.c_str(), max_ioutil.c_str());
    } else {
        base::string_printf(&ret_str, "disk_id=%d\tdisk_path=%s\toffline\n",
                disk_id(), disk_path().c_str());
    }
    ctx->sync_point->signal();
}

void DiskAgent::on_update_disk(UpdateDiskContext* ctx) {
    std::unique_ptr<UpdateDiskContext> hold_ctx(ctx);

    auto request = ctx->request;
    auto response = ctx->response;
    auto status = response->mutable_status();

    if (_is_used != (request->disk_conf()).is_used()) {
        status->set_code(AIE_FAIL);
        status->set_msg("can not update is_used conf");
        return;
    }
    {
        static common::MutexLock dump_lock;
        common::ScopedMutexLock dump_guard(dump_lock);

        _disk_conf.CopyFrom(request->disk_conf());
        _disk_manager->dump_disk_conf();
    }

    common::ScopedMutexLock lock(_mutex);
    _max_iops = _disk_conf.has_max_iops() ? _disk_conf.max_iops() : 100;
    _max_ioutil = _disk_conf.has_max_ioutil() ? _disk_conf.max_ioutil() : 100;

    init_ioutil_collector();

    if (_disk_qps_limiter) {
        _disk_qps_limiter->reset((double)_max_iops, (double)_max_iops);
    }

    status->set_code(AIE_OK);
    status->set_msg("update disk succ");
    return;
}

void DiskAgent::on_get_smr_disk_info(GetSmrDiskContext* ctx) {
    ctx->disk_info.set_is_used(_is_used);
    ctx->disk_info.set_disk_total_size(_disk_total_size);
    ctx->disk_info.set_aries_allocated_size(_disk_total_size - _disk_free_size);
    ctx->disk_info.set_is_bad_disk(_is_bad_disk);
    ctx->disk_info.set_dev_path(universalfs::get_mount_dev(disk_path()));

    uint64_t zone_size = 0;
    ctx->status = g_fs->get_zone_size(disk_path(), &zone_size);
    ctx->disk_info.set_zone_size(zone_size);

    ctx->sync_point->signal();
}

void DiskAgent::clear_vlet_map() {
    for (auto & kv : _vlet_map) {
        kv.second.reset();
    }
    _vlet_map.clear();
    _used_size = 0;
    {
        common::ScopedMutexLock lock(_mutex);
        _pending_queue.erase(std::bind(&DiskAgent::match_all, std::placeholders::_1));
        _running_volume_id.clear();
    }
}

std::map<uint64_t, VletPtr> DiskAgent::get_vlet_map() {
    return _vlet_map;
}

void DiskAgent::on_stop_agent() {
    LOG(FATAL) << "stop agent num:" << _stop_agent_wait;
    assert(_stop_agent_wait > 0);
    --_stop_agent_wait;
    LOG(NOTICE) << _name << "stop_agent, stop_agent_wait:" << _stop_agent_wait;

    std::unique_lock lock{acquire_rewrite_lock<true>()};

    if (_stop_agent_wait <= 0 && _db) {
        clear_vlet_map();
        g_fs->drop_disk(disk_path());
        // For ZNS-SSD, we need call close() explicitly;
        if (nullptr != _zone_disk) {
            _zone_disk->close();
            _zone_disk.reset(nullptr);
        }
        db_close();
    }
}

int DiskAgent::db_close() {
    LOG(NOTICE) << _name << "db_close, close rocksdb";
    _is_used = false;
    if (_index != nullptr) {
        delete _index;
        _index = nullptr;
    }
    if (_data != nullptr) {
        delete _data;
        _data = nullptr;
    }

    _vlet_indexer.reset();
    if (_db != nullptr) {
        _db->Close();
        _db = nullptr;
    }

    return 0;
}

void DiskAgent::on_suicide(SuicideContext* pCtx) {
    _is_stop = true;
    _total_creating_size = 0;
    stop_create_or_copy_workers();
    _shard_checker_ptr->stop();
    if (!_is_used) {
        pCtx->sync_point->signal();
        return;
    }
    _vlet_manager->erase(_vlet_map);

    // Stop heavyworker and ioexecutor, which may be slow
    _heavy_worker.stop();
    LOG(NOTICE) << _name << " heavyworker is stopped";
    _io_executor.join();
    LOG(NOTICE) << _name << " io_executor is stopped";

    _bg_executor.reset();
    LOG(NOTICE) << _name << " bg_executor is stopped";

    if (_compact_db_thread.joinable()) {
        _compact_db_thread.join();
    }

    // sync wal before process exit
    if (_db != nullptr && FLAGS_enable_zone_db_flush_wal) {
        _db->FlushWAL(true);
    }

    _vlet_map.clear();
    g_fs->drop_disk(disk_path());
    if (_zone_disk != nullptr) {
        _device->set_bg_executor(nullptr);
        _zone_disk->close();
        _zone_disk.reset(nullptr);
    }

    // Release DB and clear vlet_map;
    if (_db != nullptr) {
        db_close();
    }

    LOG(NOTICE) << _name << " stop is done";
    pCtx->sync_point->signal();
}

void DiskAgent::stop_create_or_copy_workers() {
    common::ScopedMutexLock lock(_mutex);
    auto size = _running_copy_works.size();
    LOG(NOTICE) << _name << " will interrupt and wait copy_vlet thread to join, wait_num:" << size;
    for (auto& worker : _running_copy_works) {
        worker->interrupt();
        if (worker->thread().joinable()) {
            worker->thread().join();
        }
    }
    size = _running_create_works.size();
    LOG(NOTICE) << _name << " will interrupt and wait create_vlet thread to join, wait_num:" << size;
    for (auto& worker : _running_create_works) {
        worker->interrupt();
        if (worker->thread().joinable()) {
            worker->thread().join();
        }
    }
}

void DiskAgent::check_balance_vlet_task() {
    for (auto& kv : _vlet_map) {
        kv.second->check_balance_vlet_task();
    }
}

void DiskAgent::update_svct_weight() {
    _svct_weight_map[_update_svct_cycle_index] = _svct_map[1] + _svct_map[2] * 2;
    LOG(NOTICE) << "cycle:" << _update_svct_cycle_index << ", collect svct weight:" << _svct_weight_map[_update_svct_cycle_index];
    _update_svct_cycle_index = (_update_svct_cycle_index + 1) % (FLAGS_disk_slow_check_second / FLAGS_disk_svct_update_second);
    for (auto& p : _svct_map) {
        p.second = 0;
    }
}

void DiskAgent::prepare_zone_resource() {
    Status status = _zone_disk->prepare_resources();
    if (!status.ok()) {
        // No return code. Disk full should not make open disk fail.
        LOG(WARNING) << _name << "zone disk prepare resource failed, maybe disk full, status:" << status;
    }
}

void DiskAgent::check_zone_device_space() {
    if (_device != nullptr) {
        assert(is_file_zone_disk());
        auto capacity = _device->disk_capacity();
        auto zone_size = _device->zone_size();
        auto curr_zone_count = _device->zone_count();
        auto max_zone_count = calc_floor(capacity, zone_size);
        ZoneDeviceZoneStatus status;
        _device->get_zone_status(&status);

        static uint32_t check_zone_file_index = 0;
        if (check_zone_file_index++ % 10 == 0) {
            auto file_count = get_zone_file_count();
            if (curr_zone_count != file_count) {
                LOG(WARNING) << _name << "zone num mismatch, zone manager count:" << curr_zone_count
                             << " real file count:" << file_count;
            }
        }

        uint32_t need_reserve_clean_zone_count = FLAGS_batch_create_zone_num +
                FLAGS_rewrite_channel_count + FLAGS_max_rewrite_thread_num_per_disk;
        if (status.invalid_zone_cnt >= 0 &&
            status.invalid_zone_cnt < need_reserve_clean_zone_count) {
            auto zone_count = std::min(
                    (int64_t)max_zone_count - curr_zone_count,
                    std::max(
                            (int64_t)need_reserve_clean_zone_count - status.invalid_zone_cnt,
                            (int64_t)FLAGS_batch_create_zone_num));
            zone_count = std::max(0L, zone_count);
            int ret = _device->create_new_zones(zone_count);
            if (ret != AIE_OK) {
                LOG(WARNING) << "zone device create zones failed with error:" << ret;
            }
            LOG(NOTICE) << "zone device create zone succ," << " disk_id:" << disk_id()
                        << " target create count:" << zone_count
                        << " before create zone_count:" << curr_zone_count
                        << " after create zone_count:" << _device->zone_count();
        }
    }
}

void DiskAgent::on_timer() {
    uint64_t log_id = base::fast_rand();
    ++_timer_counter;
    //LOG(TRACE) << _name << "on_timer" << _timer_counter;
    DelayExec(TIMER_ACTION, this, 1000UL, &_timer_context);

    if (!_is_used) {
        return;
    }

    if ((_timer_counter % FLAGS_balance_vlet_timeout_second) == 0) {
        check_balance_vlet_task();
    }

    if ((_timer_counter % FLAGS_disk_svct_update_second) == 0) {
        update_svct_weight();
    }

    if (_timer_counter % common::FLAGS_heartbeat_interval_second == 0) {
        uint64_t calc_used_size = 0;
        for (auto& kv : _vlet_map) {
            calc_used_size += kv.second->calc_total_size();
        }
        _calc_used_size = calc_used_size;
    }

    // zone device
    if (is_zone_disk()) {
        check_zone_device_space();
    }

    //ioutil, 1s
    common::ScopedMutexLock lock(_mutex);
    _ioutil_collector->collect_tot_ticks();
    double current_ioutil_rate = 0;
    if (!_ioutil_collector->get_ioutil_rate(current_ioutil_rate)) {
        LOG(WARNING) << "disk path:" << disk_path() << " get ioutil failed";
    }
    _current_ioutil = int(current_ioutil_rate * 100);
    g_bvar_disk_ioutil.put("disk" + std::to_string(disk_id()), _current_ioutil);
    
    // read/write iops/throughput
    {
        uint64_t current_read_iops = 0;
        if (!_ioutil_collector->get_current_read_iops(current_read_iops, 1)) {
            LOG(WARNING) << "disk path:" << disk_path() << " get read iops failed";
        }
        _current_read_iops = current_read_iops;
    }
    {
        uint64_t current_write_iops = 0;
        if (!_ioutil_collector->get_current_write_iops(current_write_iops, 1)) {
            LOG(WARNING) << "disk path:" << disk_path() << " get write iops failed";
        }
        _current_write_iops = current_write_iops;
    }
    {
        uint64_t current_read_throughput = 0;
        if (!_ioutil_collector->get_current_read_throughput(current_read_throughput, 1)) {
            LOG(WARNING) << "disk path:" << disk_path() << " get read throughput failed";
        }
        _current_read_throughput = current_read_throughput;
    }
    {
        uint64_t current_write_throughput = 0;
        if (!_ioutil_collector->get_current_write_throughput(current_write_throughput, 1)) {
            LOG(WARNING) << "disk path:" << disk_path() << " get write throughput failed";
        }
        _current_write_throughput = current_write_throughput;
    }
    g_bvar_physical_disk_read_iops.put("disk" + std::to_string(disk_id()), _current_read_iops);
    g_bvar_physical_disk_write_iops.put("disk" + std::to_string(disk_id()), _current_write_iops);
    g_bvar_physical_disk_read_throughput.put("disk" + std::to_string(disk_id()), _current_read_throughput);
    g_bvar_physical_disk_write_throughput.put("disk" + std::to_string(disk_id()), _current_write_throughput);

    //svct, 1s
    double current_svct_rate = 0.0;
    if (!_ioutil_collector->get_current_svct(current_svct_rate)) {
        LOG(WARNING) << "disk path:" << disk_path() << " get svct failed";
    }
    _current_svct = int(current_svct_rate);

    if (_current_svct < FLAGS_light_svct_threshold_on_slow_disk) {
        ++_svct_map[0];
    } else if (_current_svct < FLAGS_serious_svct_threshold_on_slow_disk) {
        ++_svct_map[1];
    } else {
        ++_svct_map[2];
    }

    LOG(TRACE) << "disk path:" << disk_path() << " ioutil:" << _current_ioutil << " svct:" << _current_svct;

    UnitIoStats disk_io_stats{};
    ChannelWriteStats disk_channel_write_stats{};
    uint64_t aries_logical_used_size = 0;
    if (is_zone_disk()) {
        disk_io_stats = _device->get_io_stat();
        aries_logical_used_size = _zone_disk->get_logical_used_size();
        disk_channel_write_stats = _zone_disk->channel_stats();
    } else {
        for (auto& vlet : _vlet_map) {
            disk_io_stats += vlet.second->get_io_stats();
            aries_logical_used_size += vlet.second->get_logical_used_size();
        }
    }
    // DiskAgent collect iostats every second
    g_bvar_aries_disk_read_iops.put("disk" + std::to_string(disk_id()), disk_io_stats.read_io);
    g_bvar_aries_disk_write_iops.put("disk" + std::to_string(disk_id()), disk_io_stats.write_io);
    g_bvar_aries_disk_read_throughput.put("disk" + std::to_string(disk_id()), disk_io_stats.read_throughput);
    g_bvar_aries_disk_write_throughput.put("disk" + std::to_string(disk_id()), disk_io_stats.write_throughput);

    // DiskAgent collect aries_used_size every second
    g_bvar_aries_disk_logical_used_size.put("disk" + std::to_string(disk_id()), aries_logical_used_size);

    if (is_zone_disk()) {
        // DiskAgent collect zone engine channel io stats every second
        // 1. user channel
        std::map<int32_t, uint64_t> user_channel_map = disk_channel_write_stats.user_channels_write_bytes;
        for (auto& [channel_id, bytes] : user_channel_map) {
            std::string tag = "disk" + std::to_string(disk_id()) + "_channel" + std::to_string(channel_id);
            g_bvar_aries_disk_user_channels_write_bytes.put(tag, bytes);
        }
        g_bvar_aries_disk_total_user_channels_write_bytes.put("disk" + std::to_string(disk_id()),
            disk_channel_write_stats.total_user_channels_write_bytes);
        g_bvar_aries_disk_total_user_channels_write_bytes_seconds_window.put("disk" + std::to_string(disk_id()),
            disk_channel_write_stats.total_user_channels_write_bytes_seconds_window);
        // 2. rewrite channel
        std::map<int32_t, uint64_t> rewrite_channel_map = disk_channel_write_stats.rewrite_channels_write_bytes;
        for (auto& [channel_id, bytes] : rewrite_channel_map) {
            std::string tag = "disk" + std::to_string(disk_id()) + "_channel" + std::to_string(channel_id);
            g_bvar_aries_disk_rewrite_channels_write_bytes.put(tag, bytes);
        }
        g_bvar_aries_disk_total_rewrite_channels_write_bytes.put("disk" + std::to_string(disk_id()),
            disk_channel_write_stats.total_rewrite_channels_write_bytes);
        g_bvar_aries_disk_total_rewrite_channels_write_bytes_seconds_window.put("disk" + std::to_string(disk_id()),
            disk_channel_write_stats.total_rewrite_channels_write_bytes_seconds_window);
        // 3. all channel
        g_bvar_aries_disk_total_channels_write_bytes.put("disk" + std::to_string(disk_id()),
            disk_channel_write_stats.total_channels_write_bytes);
        g_bvar_aries_disk_total_channels_write_bytes_seconds_window.put("disk" + std::to_string(disk_id()),
            disk_channel_write_stats.total_channels_write_bytes_seconds_window);
        // 4. rewrite zone
        g_bvar_aries_disk_rewrite_zone_used_size_accumulation.put("disk" + std::to_string(disk_id()),
            disk_channel_write_stats.rewrite_zone_used_size_accumulation);
        g_bvar_aries_disk_rewrite_zone_num.put("disk" + std::to_string(disk_id()),
            disk_channel_write_stats.rewrite_zone_num);
        // 5. wa
        double wa = static_cast<double>(disk_channel_write_stats.total_channels_write_bytes) /
            disk_channel_write_stats.total_user_channels_write_bytes;
        g_bvar_aries_disk_write_amplification.put("disk" + std::to_string(disk_id()), wa);
        double wa_window = static_cast<double>(disk_channel_write_stats.total_channels_write_bytes_seconds_window) /
            disk_channel_write_stats.total_user_channels_write_bytes_seconds_window;
        g_bvar_aries_disk_write_amplification_seconds_window.put("disk" + std::to_string(disk_id()), wa_window);
        // 6. disk valid data percent and usage
        uint64_t aries_total_size = 0, aries_free_size = 0;
        uint64_t logical_used_size = _zone_disk->get_logical_used_size();
        _zone_disk->get_aries_usage(&aries_total_size, &aries_free_size);
        double valid_data_percent = logical_used_size * 100.0 / aries_total_size;
        double disk_uasge = (aries_total_size - aries_free_size) * 100.0 / aries_total_size;
        g_bvar_aries_disk_valid_data_percent.put("disk" + std::to_string(disk_id()), valid_data_percent);
        g_bvar_aries_disk_usage.put("disk" + std::to_string(disk_id()), disk_uasge);
    }

    LOG(TRACE) << "disk path:" << disk_path()
               << " aries_read_iops:" << disk_io_stats.read_io
               << " aries_write_iops:" << disk_io_stats.write_io
               << " aries_read_throughput:" << disk_io_stats.read_throughput
               << " aries_write_throughput:" << disk_io_stats.write_throughput
               << " aries_logical_used_size:" << aries_logical_used_size;
}

void DiskAgent::on_purge_timer() {
    if (_purge_working) {
        DelayExec(TIMER_PURGE_VLET_ACTION, this, 1000UL, &_purge_timer_context);
        return;
    }
    uint64_t now = base::gettimeofday_s();
    if (_last_purge_timestamp + FLAGS_auto_purge_interval_s < now) {
        _last_purge_timestamp = now;
        _purged_vlets.clear();
    }

    for (auto& kv : _vlet_map) {
        auto vlet_ptr = kv.second;
        if (is_append_vlet(vlet_ptr->vlet_type())) {
            if (_purged_vlets.count(kv.first) == 0) {
                _purged_vlets.insert(kv.first);
                _purge_working = true;
                LOG(TRACE) << "try purge vlet, vid:" << kv.first;
                PurgeContext* ctx = new PurgeContext();
                baidu::rpc::ClosureGuard done_guard(nullptr);
                ctx->vlet_ptr = vlet_ptr;
                ctx->request = new ::aries::pb::PurgeVletRequest;
                ctx->response = new ::aries::pb::AckResponse;
                ctx->status = ctx->response->mutable_status();
                ctx->cntl = nullptr;
                ctx->log_id = base::fast_rand();
                ctx->vid = vlet_ptr->volume_id();
                ctx->done_guard.reset(done_guard.release());
                ctx->en_queue_time = base::gettimeofday_ms();
                ctx->en_queue_time_us = base::gettimeofday_us();
                ctx->timeout_ms = FLAGS_call_timeout_ms;
                ctx->priority = aries::VERY_HIGH;
                ctx->purge_blob_without_ttl = false;
                ctx->periodic_purge = true;
                ctx->action = PURGE_VLET_ACTION;
                base::get_leaky_singleton<IOScheduler>()->dispatch_task(static_cast<IoContext*>(ctx));
                break;
            }
        }
    }
    DelayExec(TIMER_PURGE_VLET_ACTION, this, 1000UL, &_purge_timer_context);
}

void DiskAgent::OnCompletion(AsyncContext* pCtx) {
    switch (pCtx->nAction) {
        case CREATE_VLET_ACTION:
        {
            on_create_vlet(static_cast<CreateVletContext*>(pCtx));
            break;
        }
        case ADD_VLET_ACTION:
        {
            on_add_vlet(static_cast<AddVletContext*>(pCtx));
            break;
        }
        case REBUILD_VLET_ACTION:
        {
            on_rebuild_vlet(static_cast<AddVletContext*>(pCtx));
            break;
        }
        case DROP_VLET_ACTION:
        {
            on_drop_vlet(static_cast<DropVletContext*>(pCtx));
            break;
        }
        case TIMER_ACTION:
        {
            on_timer();
            break;
        }
        case TIMER_PURGE_VLET_ACTION:
        {
            on_purge_timer();
            break;
        }
        case DIFF_DISK_ACTION:
        {
            on_diff_disk(static_cast<DiffDiskContext*>(pCtx));
            break;
        }
        case ADD_DISK_ACTION:
        {
            on_add_disk(static_cast<AddDiskContext*>(pCtx));
            break;
        }
        case DROP_DISK_ACTION:
        {
            on_drop_disk(static_cast<DropDiskContext*>(pCtx));
            break;
        }
        case AFTER_CREAT_VLET_FILE: {
            on_after_create_vlet_file(static_cast<CreateVletContext*>(pCtx));
            break;
        }
        case NEW_VLET_ACTION:
        {
            SET_SCOPED_TASK_LOG_ID(static_cast<CopyVletArgContext*>(pCtx)->log_id);
            on_new_vlet(static_cast<CopyVletArgContext*>(pCtx));
            break;
        }
        case SUBMIT_VLET_ACTION:
        {
            SET_SCOPED_TASK_LOG_ID(static_cast<CopyVletArgContext*>(pCtx)->log_id);
            on_submit_vlet(static_cast<CopyVletArgContext*>(pCtx));
            break;
        }
        case LOAD_DISK_ACTION:
        {
            on_load_disk(static_cast<LoadDiskContext*>(pCtx));
            break;
        }
        case ADD_DISK_WITHDATA_ACTION:
        {
            add_disk_withdata(static_cast<AddDiskWithDataContext*>(pCtx));
            break;
        }
        case SUICIDE_ACTION:
        {
            on_suicide(static_cast<SuicideContext*>(pCtx));
            break;
        }
        case SHOW_DISK_ACTION:
        {
            on_show_disk(static_cast<ShowDiskContext*>(pCtx));
            break;
        }
        case UPDATE_DISK_ACTION:
        {
            on_update_disk(static_cast<UpdateDiskContext*>(pCtx));
            break;
        }
        case GET_SMR_DISK_INFO_ACTION:
        {
            on_get_smr_disk_info(static_cast<GetSmrDiskContext*>(pCtx));
            break;
        }
        case STOP_AGENT_ACTION:
        {
            on_stop_agent();
            delete pCtx;
            break;
        }
        case CHECK_SLOW_ACTION:
        {
            auto ctx = static_cast<CheckSlowKylinContext*>(pCtx);
            on_check_slow(ctx->ctx);
            delete ctx;
            break;
        }
        case CHECK_HUNG_ACTION:
        {
            auto ctx = static_cast<CheckHungKylinContext*>(pCtx);
            on_check_hung(ctx->ctx);
            delete ctx;
            break;
        }
        case CHECK_OVERDUE_ACTION:
        {
            auto ctx = static_cast<CheckOverdueContext*>(pCtx);
            on_check_overdue(ctx);
            break;
        }
        case GET_VLET_MAP_ACTION:
        {
            auto ctx = static_cast<GetVletMapContext*>(pCtx);
            ctx->vlet_map = get_vlet_map();
            ctx->sync_point->signal();
            break;
        }
        case CHECK_SHARD_ACTION:
        {
            auto ctx = static_cast<ShardCheckContext*>(pCtx);
            on_check_shard(ctx);
            break;
        }
        default:
            assert(0);
    }
}

// for LINKED 和APPEND return physical space,
// for ZONE engine return the space occupied by the vlet
uint64_t DiskAgent::used_size() const {
        return _used_size;
}

uint64_t DiskAgent::calc_used_size() const {
    return _calc_used_size;
}

void DiskAgent::purge_done(uint64_t volume_id) {
    _purge_working = false;
    LOG(TRACE) << " purge done, vid:" << volume_id;
}

void DiskAgent::on_check_slow(std::shared_ptr<CheckSlowContext> ctx){
    // skip offline disk
    if (!_is_used) {
        LOG(TRACE) << _name << " disk is offline";
        ctx->sync_point->signal();
        return;
    }
    // judge is or not the slow disk
    u_int64_t hit_slow_disk_cnts = 0;
    for (auto& svct_weight : _svct_weight_map) {
        if (svct_weight.second > 0.2 * FLAGS_disk_svct_update_second) {
            ++hit_slow_disk_cnts;
        }
    }
    if (hit_slow_disk_cnts > 0.8 * FLAGS_disk_slow_check_second / FLAGS_disk_svct_update_second) {
        ctx->is_slow_disk = true;
    }
    ctx->sync_point->signal();
}

void DiskAgent::on_check_hung(std::shared_ptr<CheckHungContext> ctx) {

    // skip offline disk
    if (!_is_used) {
        ++(ctx->vlet_agent_num);
        LOG(TRACE) << _name << " disk is offline";
        ctx->sync_point->signal();
        return;
    }

    if (_io_executor.check_hung(FLAGS_disk_hung_second * 1000 * 1000)) {
        LOG(WARNING) << _name << " disk is hung.";
    } else {
        ++(ctx->vlet_agent_num);
    }

    ctx->sync_point->signal();
}

//rebuild index and load meta
int DiskAgent::load_stores() {
    uint64_t start = base::gettimeofday_s();
    base::FilePath dir(disk_path());

    uint32_t total_vlet_num = 0;
    {
        base::FileEnumerator f_enum(dir, false, base::FileEnumerator::FILES);
        for (base::FilePath name = f_enum.Next(); !name.empty(); name = f_enum.Next()) {
            std::string filename = name.BaseName().value();
            if (filename.compare(0, 2, "L_") == 0) {
                ++total_vlet_num;
            }
        }
        base::FileEnumerator d_enum(dir, false, base::FileEnumerator::DIRECTORIES);
        for (base::FilePath name = d_enum.Next(); !name.empty(); name = d_enum.Next()) {
            std::string filename = name.BaseName().value();
            if (filename.compare(0, 2, "A_") == 0) {
                ++total_vlet_num;
            }
        }
    }
    uint32_t successed_vlet_num = 0;
    base::FileEnumerator f_enum(dir, false, base::FileEnumerator::FILES);
    for (base::FilePath name = f_enum.Next(); !name.empty(); name = f_enum.Next()) {
        uint64_t vid;
        uint32_t shard_index;
        std::string filename = name.BaseName().value();
        if (filename.compare(0, 2, "L_") == 0) {
            LinkedVlet *lv = new LinkedVlet(this);
            std::shared_ptr<Vlet> vlet(lv);
            int ret = lv->open(name.value());
            if (ret == -10) {
                LOG(WARNING) << _name << " vlet file name:" << filename 
                    << " is corrupted, so skip it to load";
                continue;
            }
            if (ret == -2) {
                LOG(WARNING) << _name << " vlet file name:" << filename
                    << " is not in db, drop it";
                lv->set_drop_me(true);
                continue;
            }
            if (ret != 0) {
                return ret;
            }
            ret = db_add_vlet(vlet);
            if (ret != 0) {
                return ret;
            }
            ret = add_vlet_if_absent(vlet);
            if (ret != 0) {
                return ret;
            }
            ++successed_vlet_num;
            LOG(NOTICE) << _name << "load and add vlet successed, vlet_file_name:" 
                << filename << " load_ratio:" << successed_vlet_num << "/" << total_vlet_num;
        }
    }
    //load Append vlet
    base::FileEnumerator d_enum(dir, false, base::FileEnumerator::DIRECTORIES);
    for (base::FilePath name = d_enum.Next(); !name.empty(); name = d_enum.Next()) {
        uint64_t vid;
        uint32_t shard_index;
        std::string filename = name.BaseName().value();
        if (filename.compare(0, 2, "A_") == 0) {
            AppendVlet *av = new AppendVlet(this);
            std::shared_ptr<Vlet> vlet(av);
            int ret = av->open(name.value());
            if (ret == -10) {
                LOG(WARNING) << _name << " vlet file name:" << filename 
                    << " is corrupted, so skip it to load";
                continue;
            }
            if (ret == -2) {
                LOG(WARNING) << _name << " vlet file name:" << filename
                    << " is not in db, drop it";
                av->set_drop_me(true);
                continue;
            }
            if (ret != 0) {
                return ret;
            }
            ret = db_add_vlet(vlet);
            if (ret != 0) {
                return ret;
            }
            ret = add_vlet_if_absent(vlet);
            if (ret != 0) {
                return ret;
            }
        }
    }
    uint64_t end = base::gettimeofday_s();
    LOG(WARNING) << _name << " load stores on disk:" << disk_path() 
        << " finished, cost_s:" << (end - start);
    return 0;
}

int DiskAgent::load_stores_based_on_db() {
    uint64_t start = base::gettimeofday_us();
    int64_t vlet_count = 0;

    std::vector<aries::pb::GenericVletInfo> vlet_info_list;
    Status s = _vlet_indexer->list(&vlet_info_list);
    unlikely_if (!s.ok()) {
        LOG(WARNING) << "fail to load stores due to indexer list vlet info fail, status:" << s;
        return -1;
    }

    for (auto& vlet_info : vlet_info_list) {
        VolumeKey key(vlet_info.volume_id(), vlet_info.vlet_type());

        if (vlet_info.mutable_zone_vlet_info()->has_dropped_timestamp()) {
            if (FLAGS_enable_zone_vlet_delay_gc) {
                Status s = _zone_disk->add_dropped_vlet(vlet_info);
                if (!s.ok()) {
                    LOG(WARNING) << "zone disk failed to add dropped vlet, vid:"
                            << vlet_info.volume_id() << " status:" << s;
                    return s.code();
                }
            } else {
                Status s = _zone_disk->destroy_vlet(key);
                if (!s.ok()) {
                    LOG(WARNING) << "zone disk failed to destroy vlet, vid:"
                            << vlet_info.volume_id() << " status:" << s;
                }
                return s.code();
            }
            continue;
        }

        ZoneVlet* zone_vlet = new ZoneVlet(this, vlet_info);
        std::shared_ptr<Vlet> vlet(zone_vlet);

        int ret = vlet->open("" /*useless*/);
        unlikely_if (ret == -10) {
            LOG(WARNING) << _name <<" has vlet:" << key << " but corruped, skip load, disk_id:" << disk_id();
            continue;
        }

        unlikely_if (ret != AIE_OK) {
            LOG(WARNING) << "vlet open failed, vlet:" << key << " disk_id:" << disk_id() << " error:" << ret;
            return ret;
        }

        ret = db_add_vlet(vlet);
        unlikely_if (ret != AIE_OK) {
            LOG(WARNING) << "add vlet to db failed, vlet:" << key << " disk_id:" << disk_id() << " error:" << ret;
            return ret;
        }
        ret = add_vlet_if_absent(vlet);
        unlikely_if (ret != AIE_OK) {
            LOG(WARNING) << "add vlet to memory map failed, vlet:" << key << " disk_id:" << disk_id()
                         << " error:" << ret << " vlet_count:" << vlet_count;
            LOG(WARNING) << " vlet_info, volume_id:" << vlet_info.volume_id() << " shard_index:" << vlet_info.shard_index()
                        <<  " vlet_version:" << vlet_info.vlet_version() << " create_time：" << vlet_info.create_time()
                        << " vlet_type:" << vlet_info.vlet_type();
            return ret;
        }
        LOG(NOTICE) << " vlet_info, vlet:" << key << " volume_id:" << vlet_info.volume_id() << " shard_index:" << vlet_info.shard_index()
                     <<  " vlet_version:" << vlet_info.vlet_version() << " create_time:" << vlet_info.create_time()
                     << " vlet_type:" << vlet_info.vlet_type() <<  " vlet_count:" << vlet_count;
        vlet_count++;
    }

    uint64_t end = base::gettimeofday_us();
    LOG(NOTICE) << _name << " load stores finished, vlet_count:" << vlet_count << " cost_us:" << (end - start);
    return AIE_OK;
}

int DiskAgent::recover_zone_disk() {
    int ret{AIE_OK};

    if (FLAGS_recover_mode) {
        // In recover mode, all zones are scanned, then the zone_info,
        // vlet_info and record index are recovered

        ZoneDiskScanner zone_disk_scanner(_device.get());
        auto status = zone_disk_scanner.scan_disk(DEFAULT_SCAN_THREAD_NUM);
        unlikely_if (status.failed()) {
            LOG(WARNING) << _name << "scan zone disk failed, status:" << status;
            return status.code();
        }

        auto scan_zone_infos = zone_disk_scanner.yield_scan_zone_infos();
        ret = recover_zone_info(scan_zone_infos);
        unlikely_if (ret != AIE_OK) {
            LOG(WARNING) << _name << "recover zone info failed, error:" << ret;
            return ret;
        }

        std::unordered_set<std::pair<uint64_t, uint32_t>> vlets;
        ret = recover_node_vlet_info(&vlets);
        unlikely_if (ret != AIE_OK) {
            LOG(WARNING) << _name << "recover node vlet info failed, error:" << ret;
            return ret;
        }

        auto recovered_records = zone_disk_scanner.yield_scan_records();
        // filter out the records whose vlet is not exist
        for (auto it = recovered_records.begin(); it != recovered_records.end(); ++it) {
            auto& key = it->first;
            if (vlets.find({key.vid(), key.shard_index()}) == vlets.end()) {
                recovered_records.erase(it);
            }
        }
        ret = recover_record_index(recovered_records);
        unlikely_if (ret != AIE_OK) {
            LOG(WARNING) << _name << "recover record index failed, error:" << ret;
            return ret;
        }
    } else {
        // In fast_recover_mode, do not scan zone, only get zone_infos from db.
        // Otherwise, scan OPEN zones to recover records and zone_infos

        ZoneDiskRebuilder rebuilder(_db, _device.get(), !FLAGS_fast_recover_mode);
        Status s = rebuilder.rebuild();
        if (!s.ok()) {
            LOG(WARNING) << _name << "rebuild failed with status:" << s;
            return s.code();
        }

        auto rebuild_zone_infos = rebuilder.yield_rebuild_zone_infos();
        for (const auto& [zone_id, zone_info] : rebuild_zone_infos) {
            unlikely_if (zone_info.zone_meta.state == ZoneState::FREE || zone_info.zone_meta.state == ZoneState::INVALID) {
                // It is impossible to be free state:
                // 1. Zone state in db is impossible to be free state.
                // 2. Zone state scanned from device is impossible to be free(we don't scan close zone)
                //      because we only scan open zone, result in open or closed
                LOG(WARNING) << _name << "recover zone_info failed due to unexpected zone state in recovered zones,"
                            << " zone_meta:" << zone_info.zone_meta;
                return AIE_FAIL;
            }
        }
        int ret = recover_zone_info(rebuild_zone_infos);
        unlikely_if (ret != AIE_OK) {
            LOG(WARNING) << "recover zone info failed, error:" << ret;
            return ret;
        }

        if (!FLAGS_fast_recover_mode) {
            auto recovered_records = rebuilder.yield_rebuild_records();
            for (auto it = recovered_records.begin(); it != recovered_records.end(); ++it) {
                auto& index_key = it->first;
                auto& index_entry = it->second;
                ZoneRecordDBIndexer record_indexer(index_key.vid(), index_key.shard_index(), _db);
                ZoneRecordIndexEntry other_entry;
                auto status = record_indexer.get(index_key.vbid(), &other_entry);
                if (status.ok()) { // record exists, and is not mark delete
                    if (other_entry.seq_no >= index_entry.seq_no) {  // other one is newer, skip
                        recovered_records.erase(it);
                        continue;
                    }
                    LOG(NOTICE) << _name << "find duplicated record and replace the old one, key:" << index_key;
                } else if (status.code() == AIE_MARK_REMOVED) {  // mark delete record
                    if (other_entry.seq_no >= index_entry.seq_no) {  // other one is newer, skip
                        recovered_records.erase(it);
                        continue;
                    }
                    LOG(NOTICE) << _name << "find duplicated mark deleted record and replace the old one, key:" << index_key;
                    index_entry.mark_delete_time = other_entry.mark_delete_time;
                } else if (status.code() != AIE_BLOB_NOT_EXIST) {  // other error
                    LOG(WARNING) << _name << "recover records failed due to get record index entry from db failed,"
                                 << " key:" << index_key << " status:" << status;
                    return status.code();
                }
            } // end for

            int ret = recover_record_index(recovered_records);
            unlikely_if (ret != AIE_OK) {
                LOG(WARNING) << _name << "recover records failed due to recover record index failed, error:" << ret;
                return ret;
            }
        }
    }

    if (FLAGS_enable_zone_db_flush_wal) {
        Status status = _db->FlushWAL(true);
        unlikely_if (!status.ok()) {
            LOG(WARNING) << _name << "flush db wal failed, status:" << status;
            // not considered as error
        }
    }

    return AIE_OK;
}

int DiskAgent::recover_record_index(const std::map<ZoneRecordIndexKey, ZoneRecordIndexEntry>& record_infos) {
    rocksdb::WriteBatch batch;
    for (auto& [index_key, index_entry] : record_infos) {
        std::string index_entry_str;
        index_entry.serialize_to_str(&index_entry_str);
        batch.Put({}, index_key.to_slice(), index_entry_str);
    }

    Status s = _db->Write({}, &batch);
    unlikely_if (!s.ok()) {
        LOG(WARNING) << _name << "recover record index failed due to put to db failed, status:" << s;
        return s.code();
    }
    return AIE_OK;
}

int DiskAgent::recover_zone_info(const std::map<ZoneID, ScanZoneInfo>& zone_infos) {
    ZoneMetaDBIndexer zone_indexer(_db);
    for (const auto& [zone_id, zone_info] : zone_infos) {
        ZoneMeta meta(zone_info.zone_meta);
        assert(meta.zone_id == zone_id);

        if (meta.state == ZoneState::FREE) {
            // remove zone file
            int ret = _device->free_zone(meta.zone_id);
            if (ret != AIE_OK) {
                LOG(WARNING) << _name << "free zone failed, zone_info:" << meta;
                return ret;
            }
            _zone_disk->remove_zone_meta(meta.zone_id);
            continue;
        } else if (meta.state == ZoneState::INVALID) {
            continue;
        }

        int ret = _device->reset_zone(zone_id, meta.state);
        if (ret == AIE_NOT_EXIST && meta.state == ZoneState::CLOSED) {
            // Remove free zone meta( zone file has been deleted but db meta exist)
            Status s = zone_indexer.remove(zone_id);
            unlikely_if (!s.ok()) {
                LOG(WARNING) << _name << "recover zone info failed due to remove zone meta from db failed, status:" << s;
                return s.code();
            }
            _zone_disk->remove_zone_meta(zone_id);
            continue;
        } else if (ret != AIE_OK) {
            LOG(WARNING) << _name << "failed to reset zone state, zone:" << meta << " ret:" << ret;
            return ret;
        }

        if (meta.state == ZoneState::OPEN) {
            unlikely_if (zone_info.write_pointer < ZONE_META_RECORD_SIZE) {
                LOG(WARNING) << _name << "recover zone info failed due to invalid write pointer:" << zone_info.write_pointer;
                return AIE_FAIL;
            }

            ZoneMetaPersistor persistor(_device.get(), &zone_indexer, meta.channel_group_id, meta.channel_id);

            // close OPEN zone when restart if zone is open
            int ret = persistor.close_zone(zone_info.write_pointer, &meta);
            unlikely_if (ret != AIE_OK) {
                LOG(WARNING) << _name << "recover zone info failed due to close zone failed, zone:" << meta << " ret:" << ret;
                return ret;
            }

            // if open zone do not have any data, free it
            if (zone_info.write_pointer == ZONE_META_RECORD_SIZE) {
                LOG(NOTICE) << "find open state zone that do not contain data,"
                            << " zone:" << meta << " write_pointer:" << zone_info.write_pointer;
                ret = persistor.free_zone(meta);
                unlikely_if (ret != AIE_OK) {
                    LOG(WARNING) << "recover records failed due to free zone failed, zone:" << meta << " ret:" << ret;
                    return ret;
                }
                _zone_disk->remove_zone_meta(zone_id);
                continue;
            }
        }

        auto s = zone_indexer.put(zone_id, meta);
        unlikely_if (!s.ok()) {
            LOG(WARNING) << _name << "failed to put zone meta index to db, status:" << s;
            return s.code();
        }

        _zone_disk->update_zone_meta(meta);
    }
    return AIE_OK;
}

int DiskAgent::recover_node_vlet_info(std::unordered_set<std::pair<uint64_t, uint32_t>>* vlets) {
    // query node vlet info from master to recover vlet_info
    aries::pb::ListNodeVletRequest request;
    aries::pb::ListNodeVletResponse response;
    uint64_t node_addr = common::endpoint2int(common::get_local_addr());
    request.set_token(FLAGS_token);
    request.set_req_addr(node_addr);
    request.set_node_addr(node_addr);

    MasterCaller master_caller;
    int ret = master_caller.list_node_vlet(&request, &response);
    unlikely_if (ret != AIE_OK) {
        LOG(WARNING) << "failed to recover node vlet info with master caller list_node_vlet has error:" << ret;
        return ret;
    }

    for (int32_t index = 0; index < response.disk_vlet_list_size(); ++index) {
        if (response.disk_vlet_list(index).disk_id() != disk_id()) {
            continue;
        }

        const aries::pb::DiskVletList& disk_vlet_lists = response.disk_vlet_list(index);

        for (int32_t vlet_index = 0; vlet_index < disk_vlet_lists.vlet_list_size(); ++vlet_index) {
            const aries::pb::VletInfo& vlet_info = disk_vlet_lists.vlet_list(vlet_index);

            if (!is_zone_vlet(vlet_info.vlet_type())) {
                continue;
            }
            if (vlet_info.volume_id() == 0 && vlet_info.vlet_type() == 0) {
                abort();
            }

            aries::pb::GenericVletInfo zone_vlet_info;
            zone_vlet_info.set_volume_id(vlet_info.volume_id());
            zone_vlet_info.set_shard_index(vlet_info.shard_index());
            zone_vlet_info.set_vlet_version(vlet_info.vlet_version());
            zone_vlet_info.set_vlet_type(vlet_info.vlet_type());
            zone_vlet_info.set_create_time(vlet_info.create_time());
            zone_vlet_info.mutable_membership()->CopyFrom(vlet_info.membership());
            zone_vlet_info.mutable_zone_vlet_info()->set_package_protocol_version(common::STANDARD_RECORD_LAYOUT_VERSION_V0);
            zone_vlet_info.set_align_size(vlet_info.vlet_engine_options().align_size());
            zone_vlet_info.mutable_zone_vlet_info()->set_vlet_size(vlet_info.vlet_engine_options().vlet_size());
            zone_vlet_info.mutable_shard_compress_option()->CopyFrom(vlet_info.shard_compress_option());
            zone_vlet_info.set_use_standard_record_layout(vlet_info.use_standard_record_layout());

            Status status = _vlet_indexer->put(zone_vlet_info);
            unlikely_if (!status.ok()) {
                LOG(FATAL) << "write vlet info to db failed with status:" << status;
                return status.code();
            }

            vlets->insert({vlet_info.volume_id(), vlet_info.shard_index()});
            LOG(NOTICE) << "add zone vlet info to db, volume_id:"<< vlet_info.volume_id()
                        << " shard_index:" << vlet_info.shard_index()
                        << " disk_id:" << disk_id();
        }
    }

    return AIE_OK;
}

void DiskAgent::add_disk_withdata(AddDiskWithDataContext* ctx) {
    std::unique_ptr<AddDiskWithDataContext> hold_ctx(ctx);
    auto& res =  ctx->response;
    auto status = res->mutable_status();
    if (_is_used) {
        status->set_code(AIE_OK);
        status->set_msg("disk is already added");
        LOG(WARNING) << " add disk with data failed due to disk is already added";
        return ;
    }
    if (_db) {
        status->set_code(AIE_FAIL);
        status->set_msg("still dropping,try later");
        LOG(WARNING) << " add disk with data failed due to _db is not nullptr";
        return;
    }

    // begin to open meta database
    int ret = db_open();
    if (ret != AIE_OK) {
        // open db failed
        status->set_code(AIE_FAIL);
        status->set_msg("db opened failed");
        LOG(FATAL) << " add disk with data failed due to db opened failed";
        return;
    }

    if (_disk_type == common::DT_INVALID) {
        status->set_code(AIE_FAIL);
        status->set_msg("disk type error");
        LOG(FATAL) << " disk type illigle:" << _disk_conf.disk_type();
        db_close();
        return;
    }

    auto s = g_fs->register_disk(disk_path().data(), _disk_conf.dev_path(), _disk_type, 2);
    if (!s.ok() && s.code() != AIE_EXIST) {
        LOG(WARNING) << "register disk failed, path:" << disk_path()
                     << " dev path:" << _disk_conf.dev_path() << " status:" << s;
        status->set_code(AIE_FAIL);
        status->set_msg("load stores failed");
        db_close();
        return;
    }

    _flow_control.reset(new FlowControl(disk_id()));
    if (Status s = _flow_control->init(); !s.ok()) {
        LOG(WARNING) << "init flow control failed, disk_id:" << disk_id()
                << " status:" << s;
        status->set_code(AIE_FAIL);
        status->set_msg("init flow control failed");
        return;
    }

    if (is_zone_disk()) {
        // zone disk
        ret = open_zone_disk();
        unlikely_if (ret != AIE_OK) {
            LOG(FATAL) << "failed to open zone disk with disk_id:" << disk_id();
            status->set_code(AIE_FAIL);
            status->set_msg("open zone disk error");
            db_close();
            return;
        }

        // recover records before open vlets
        ret = recover_zone_disk();
        unlikely_if (ret != AIE_OK) {
            LOG(FATAL) << "failed to recover records, disk_id:" << disk_id() << " error:" << ret;
            status->set_code(ret);
            status->set_msg("recover zone records failed");
            db_close();
            return;
        }

        ret = load_stores_based_on_db();
        unlikely_if (ret != AIE_OK) {
            status->set_code(AIE_FAIL);
            status->set_msg("load stores failed");
            db_close();
            return;
        }

        check_zone_device_space();
        prepare_zone_resource();
    } else {
        ret = load_stores();
        unlikely_if (ret != 0) {
            status->set_code(AIE_FAIL);
            status->set_msg("load stores failed");
            db_close();
            return;
        }
    }

    JoinDiskContext* pCtx = new JoinDiskContext();
    pCtx->request = ctx->request;
    pCtx->response = res;
    pCtx->status = status;
    pCtx->cntl = ctx->cntl;
    pCtx->log_id = ctx->cntl->log_id();
    pCtx->disk_type = _disk_type;
    pCtx->done_guard.reset(hold_ctx->done_guard.release());

    for (auto& kv : _vlet_map) {
        auto vlet_ptr = kv.second;
        aries::pb::DatanodeVletInfo info;
        vlet_ptr->fill_vlet_info(&info);
        info.set_disk_id(disk_id());

        pCtx->push_back(info);
    }
    QueueExecEmergent(JOIN_DISK_REPORT_ACTION, g_datanode->cleaner(), pCtx);

    reload();
}

void DiskAgent::on_load_disk(LoadDiskContext* ctx) {
    LOG(NOTICE) << _name << "start load disk, disk_type:" << common::disk_type2string(_disk_type)
                << " zone_device_type:" << _zone_device_type;

    _is_used = false;
    g_datanode_err_disk_num_adder << 1;
    DelayExec(TIMER_ACTION, this, 1000UL, &_timer_context);
    DelayExec(TIMER_PURGE_VLET_ACTION, this, 10000UL, &_purge_timer_context);
    common::SyncPointGuard sync_point_guard(ctx->sync_point);

    if (!disk_conf().is_used()) {
        LOG(NOTICE) << "disk is not used, disk_id:" << disk_id();
        return;
    }

    if (_disk_type == common::DT_INVALID) {
        ctx->ret = -1;
        LOG(FATAL) << _name << "disk type is invalid, " << _disk_conf.disk_type();
        return;
    }
    if ((_disk_type != common::DT_ZONE_SSD &&  _zone_device_type != ZoneDeviceType::INVALID)
                || (_disk_type == common::DT_ZONE_SSD && _zone_device_type == ZoneDeviceType::INVALID)) {
        ctx->ret = -1;
        LOG(FATAL) << _name << "disk type is invalid, disk_type:" << common::disk_type2string(_disk_type)
                    << " zone_device_type:" << enum_to_string(_zone_device_type);
        return;
    }

    // begin to open meta database
    ctx->ret = db_open();
    if (ctx->ret != AIE_OK) {
        LOG(WARNING) << _name << "db_open failed, error:" << ctx->ret;
        return;
    }

    // register disk
    auto s = g_fs->register_disk(disk_path().data(), _disk_conf.dev_path(), _disk_type, 2);
    if (!s.ok() && s.code() != AIE_EXIST) {
        LOG(WARNING) << _name << "register disk failed, path:" << disk_path()
                     << " dev path:" << _disk_conf.dev_path() << " status:" << s;
        ctx->ret = -1;
        return;
    }

    _flow_control.reset(new FlowControl(disk_id()));
    if (Status s = _flow_control->init(); !s.ok()) {
        LOG(WARNING) << _name << "init flow control failed, status:" << s;
        ctx->ret = -1;
        return;
    }

    // ssd does not need iops limit.
    if (_disk_type == common::DT_HDD) {
        _disk_qps_limiter = std::make_shared<common::MultiSpeedTokenPool>((double)_max_iops, (double)_max_iops);
        _disk_qps_limiter->start();
    }

    //begin to open zone-based disk
    if (is_zone_disk()) {
        ctx->ret = open_zone_disk();
        unlikely_if (ctx->ret != AIE_OK) {
            LOG(WARNING) << _name << "open zone disk failed, error:" << ctx->ret;
            return;
        }

        ctx->ret = recover_zone_disk();
        unlikely_if (ctx->ret != AIE_OK) {
            LOG(WARNING) << _name << "recover zone disk failed, error:" << ctx->ret;
            return;
        }

        ctx->ret = load_stores_based_on_db();
        unlikely_if (ctx->ret != AIE_OK) {
            LOG(WARNING) << _name << "load_stores_based_on_db failed, error:" << ctx->ret;
            return;
        }

        check_zone_device_space();
        prepare_zone_resource();
    } else {
        ctx->ret = load_stores();
        unlikely_if (ctx->ret != AIE_OK) {
            return;
        }
    }

    g_datanode_err_disk_num_adder << -1;
    _is_used = true;
    _is_bad_disk = false;
    // start io_executor and heavyworker;
    start_io_executor();
    start_heavyworker();
    _create_time = base::gettimeofday_us();
    init_ioutil_collector();
    if (_bg_executor == nullptr) {
        _bg_executor = std::make_unique<folly::CPUThreadPoolExecutor>(FLAGS_bg_threadpool_thread_num);
    }
    _compact_db_thread = std::thread{ std::bind(&DiskAgent::compact_db, this) };
    LOG(NOTICE) << "load disk finished, name:" << _name;
}

void DiskAgent::on_create_vlet(CreateVletContext* ctx) {
    std::unique_ptr<CreateVletContext> hold_ctx(ctx);
    auto response = ctx->response;
    auto request = ctx->request;
    auto status = response->mutable_status();

    unlikely_if (!_is_used) {
        status->set_code(AIE_FAIL);
        status->set_msg("disk is offline");
        return ;
    }

    bool is_zone_disk_type = is_zone_disk();
    bool is_zone_vlet_type = is_zone_vlet(request->vlet_info().vlet_type());
    unlikely_if (is_zone_disk_type ^ is_zone_vlet_type) {
        status->set_code(AIE_FAIL);
        status->set_msg("invalid vlet type");
        LOG(WARNING) << "disk id:" << disk_id()
                     << " type:" << common::disk_type2string(disk_type())
                     << " is_zone_disk:" << (is_zone_disk_type ? "true" : "false")
                     << " vlet_type:" << request->vlet_info().vlet_type()
                     << " is_zone_vlet:" << (is_zone_vlet_type ? "true" : "false");
        return;
    }

    auto& vlet_info = request->vlet_info();
    auto& space_info = request->space_info();
    uint32_t disk_id = vlet_info.disk_id();
    uint64_t volume_id = vlet_info.volume_id();
    uint32_t shard_index = vlet_info.shard_index();
    uint64_t create_time = vlet_info.create_time();
    uint32_t vlet_type = vlet_info.vlet_type();
    uint64_t vlet_size = 0;
    if (vlet_info.has_vlet_engine_options()) {
        vlet_size = vlet_info.vlet_engine_options().vlet_size();
    } else {
        vlet_size = vlet_size_by_type(aries::VletType(vlet_type));
    }

    if (_vlet_manager->vlet_refed(volume_id, -1, -1)) {
        status->set_code(AIE_EXIST);
        status->set_msg("vlet already exist");
        return ;
    }

    // db may has dirty index, so we need to delete it before create
    VolumeKey volume_key(volume_id, vlet_type);
    if (is_zone_disk()) {
        _zone_disk->destroy_vlet(volume_key);
    } else {
        Status remove_status = _vlet_indexer->destroy(volume_key);
        if (!remove_status.ok()) {
            status->set_code(remove_status.code());
            status->set_msg(remove_status.msg());
            return;
        }
    }

    const std::string& path = disk_path();

    uint64_t disk_total_size = 0;
    uint64_t disk_free_size = 0;
    uint64_t disk_reserve_size = 0;

    // check disk free size
    auto ret_s = get_disk_usage(&disk_total_size, &disk_free_size);
    if (!ret_s.ok()) {
        status->set_code(AIE_FAIL);
        status->set_msg("get disk capacity failed");
        return;
    }
    if (request->has_disk_reserve_percent()) {
        disk_reserve_size = (disk_total_size * request->disk_reserve_percent()) / 100;
    } else {
        disk_reserve_size = (disk_total_size * FLAGS_disk_reserve_percent) / 100;
    }
    if (is_zone_disk()) {
        uint32_t reserved_zone_count_for_rewrite = FLAGS_rewrite_channel_count 
                + FLAGS_max_rewrite_thread_num_per_disk;
        disk_reserve_size += reserved_zone_count_for_rewrite * _device->zone_size();
    }

    uint64_t need_disk_size = vlet_size;
    LOG(NOTICE) << _name << "create_vlet, disk_total_size:" << size_GB(disk_total_size) << "GB"
            << " reserved_size:" << size_GB(disk_reserve_size) << "GB"
            << " free_disk_size:" << size_GB(disk_free_size) << "GB"
            << " need_disk_size:" << size_GB(need_disk_size) << "GB";

    if (need_disk_size <= 0) {
        status->set_code(AIE_INVALID_ARGUMENT);
        status->set_msg("vlet type is error");
        return ;
    }

    if (disk_free_size < (need_disk_size + disk_reserve_size + _total_creating_size)) {
        status->set_code(AIE_DISK_FULL);
        status->set_msg("disk full");
        return ;
    }

    int ret = _vlet_manager->create(volume_id);
    if (ret < 0) {
        status->set_code(AIE_EXIST);
        status->set_msg("vlet already exist");
        return ;
    }

    if (ret > 0) {
        status->set_code(AIE_INPROGRESS);
        status->set_msg("vlet is creating");
        return ;
    }
    hold_ctx.release();
    auto worker = std::make_shared<CreateVletFileWorker>(ctx, this);
    ctx->worker = worker;
    ctx->need_space = need_disk_size;
    worker->start_create();
    add_create_worker(worker);
    _total_creating_size += need_disk_size;
}

void DiskAgent::on_after_create_vlet_file(CreateVletContext* ctx) {
    del_create_worker(ctx->worker);
    _total_creating_size -= ctx->need_space;
    std::unique_ptr<CreateVletContext> hold_ctx(ctx);

    auto response = ctx->response;
    auto request = ctx->request;
    auto status = response->mutable_status();

    auto& vlet_info = request->vlet_info();
    auto& space_info = request->space_info();
    uint32_t disk_id = vlet_info.disk_id();
    uint64_t volume_id = vlet_info.volume_id();
    uint32_t shard_index = vlet_info.shard_index();
    uint64_t create_time = vlet_info.create_time();
    uint32_t vlet_type = vlet_info.vlet_type();

    if (!_is_used) {
        status->set_code(AIE_FAIL);
        status->set_msg("disk is offline");
        return ;
    }

    bool is_zone_disk_type = is_zone_disk();
    bool is_zone_vlet_type = is_zone_vlet(request->vlet_info().vlet_type());
    if (is_zone_disk_type ^ is_zone_vlet_type) {
        status->set_code(AIE_FAIL);
        status->set_msg("invalid vlet type");
        LOG(WARNING) << "disk id:" << disk_id
                     << " type:" << common::disk_type2string(disk_type())
                     << " is_zone_disk:" << (is_zone_disk_type ? "true" : "false")
                     << " vlet_type:" << request->vlet_info().vlet_type()
                     << " is_zone_vlet:" << (is_zone_vlet_type ? "true" : "false");
        return;
    }

    VletPtr& vlet_ptr = ctx->vlet_ptr;
    auto ret = ctx->create_vlet_file_ret;

    if (ret < 0) {
        status->set_code(AIE_INVALID_ARGUMENT);
        status->set_msg("vlet type is error");
        _vlet_manager->create_done(volume_id, vlet_ptr);
        return;
    }

    if (!vlet_ptr) {
        status->set_code(AIE_IO_ERROR);
        status->set_msg("vlet create data file error");
        _vlet_manager->create_done(volume_id, vlet_ptr);

        std::string msg = base::string_printf("create vlet io error, log_id: %lu", ctx->log_id);
        LOG(FATAL) << "create_vlet failed because of disk error:" << ret;
        return ;
    }

    LOG(NOTICE) << _name << "create_vlet succeeded, vid:" << volume_id
            << " shard_index:" << vlet_info.shard_index()
            << " vlet_version:" << vlet_info.vlet_version()
            << " log_id: " << ctx->log_id;

    // submit to db
    ret = db_add_vlet(vlet_ptr);
    if (ret) {
        status->set_code(AIE_IO_ERROR);
        status->set_msg("disk error");
        vlet_ptr->set_drop_me(true);
        vlet_ptr.reset();
        _vlet_manager->create_done(volume_id, vlet_ptr);
        std::string msg = base::string_printf("create vlet submit db error, log_id: %lu", ctx->log_id);
        if (ret == AIE_IO_ERROR) {
            set_bad_disk(msg);
        }
        return;
    }
    add_vlet(vlet_ptr);

    _vlet_manager->create_done(volume_id, vlet_ptr);
}

int DiskAgent::db_open() {
    rocksdb::Options options;
    rocksdb::BlockBasedTableOptions table_options;
    // Optimize RocksDB. This is the easiest way to get RocksDB to perform well
    // options.IncreaseParallelism();
    // options.OptimizeLevelStyleCompaction();
    // create the DB if it's not already present
    options.create_if_missing = true;
    options.create_missing_column_families = true;
    options.max_open_files = FLAGS_db_max_open_file;
    options.target_file_size_base =  16 * 1024 * 1024; // 16M

    // pmeme_dev -> don't open block cache
    // disk dev -> use block cache and page cache etc.
    if (db_on_pmem()) {
        options.use_direct_io_for_flush_and_compaction = true;
        table_options.cache_index_and_filter_blocks = false;
        table_options.pin_l0_filter_and_index_blocks_in_cache = false;
    } else if (_disk_type == common::DT_SSD || _disk_type == common::DT_ZONE_SSD) {
        // avoid to dirty cache and buffer
        options.use_direct_io_for_flush_and_compaction = true;
        table_options.cache_index_and_filter_blocks = true;
        table_options.pin_l0_filter_and_index_blocks_in_cache = true;
        table_options.block_size = 4 * 1024;
        table_options.block_cache = s_block_cache_for_ssd;
    } else {
        options.use_direct_io_for_flush_and_compaction = false;
        table_options.cache_index_and_filter_blocks = true;
        table_options.pin_l0_filter_and_index_blocks_in_cache = true;
        table_options.block_size = 4 * 1024;
        table_options.block_cache = s_block_cache;
    }

    // only zone engine using manual_wal_flush,
    // Process crash with manual WAL flush (DBOptions::manual_wal_flush=1), which loses writes
    // since the last memtable flush or manual WAL flush (FlushWAL())
    // System crash with manual WAL flush (DBOptions::manual_wal_flush=1), which loses writes since the last memtable
    // flush or synced manual WAL flush (FlushWAL(true /* sync */), or FlushWAL(false /* sync */) followed by WAL sync)
    if (is_zone_disk() && FLAGS_enable_zone_db_flush_wal) {
        options.manual_wal_flush = true; // manual control wal flush, be careful for this
        options.writable_file_max_buffer_size = 1024 * 1024;
    }
    if (FLAGS_db_use_direct_io) {
        options.use_direct_reads = true;
        options.use_direct_io_for_flush_and_compaction = true;
    }

    //mostly 100 million keys per disk, then will cost 0.12GB memory and disk space;
    table_options.filter_policy.reset(rocksdb::NewBloomFilterPolicy(10, false));
    options.table_factory.reset(rocksdb::NewBlockBasedTableFactory(table_options));

    // open DB with two column families
    std::vector<rocksdb::ColumnFamilyDescriptor> column_families;
    std::string db_path = disk_db_path();
    std::vector<std::string> column_family_names;
    std::set<std::string> column_family_set;

    column_family_set.insert("data");
    column_family_set.insert(rocksdb::kDefaultColumnFamilyName);

    rocksdb::DB::ListColumnFamilies(options, db_path, &column_family_names);
    column_family_set.insert(column_family_names.begin(),column_family_names.end());

    for (auto column_family_name : column_family_set) {
        rocksdb::ColumnFamilyOptions data_cf_options;
        if (column_family_name == rocksdb::kDefaultColumnFamilyName) {
            data_cf_options.target_file_size_base =  16 * 1024 * 1024; // 16M
        }
        data_cf_options.table_factory.reset(rocksdb::NewBlockBasedTableFactory(table_options));
        column_families.push_back(rocksdb::ColumnFamilyDescriptor(column_family_name, data_cf_options));
        LOG(NOTICE) << "open column_families, name:" << column_family_name;
    }

    std::vector<rocksdb::ColumnFamilyHandle*> handles;
    // open DB
    rocksdb::DB* tmp = nullptr;
    LOG(NOTICE) << "start open rocksdb, name:" << _name;
    Status s = rocksdb::DB::Open(options, db_path, column_families, &handles, &tmp);
    LOG(TRACE) << "open_meta return status: " << s.to_string();

    if (!s.ok()) {
        _db = nullptr;
        return s.code();
    }
    _db.reset(tmp);
    _index = handles[0];
    _data = handles[1];

    _vlet_indexer = std::make_unique<VletIndexer>(_db, is_zone_disk());
    return 0;
}

int DiskAgent::open_linked_vlet(uint64_t volume_id, uint32_t vlet_type, rocksdb::Iterator* iter) {
    VletPtr vlet(new LinkedVlet(this));

    int ret = vlet->open(iter);
    if (ret) {
        LOG(ERROR) << "Open volume failed, vid:" << volume_id << " disk:" << disk_path();
        return ret;
    }

    return add_vlet_if_absent(vlet);
}

int DiskAgent::db_add_vlet(const VletPtr& vlet_ptr) {
    // compatible with old load ways that scan vlet info from rocksdb rather than fs meta
    VolumeKey volume_key(vlet_ptr->volume_id(), vlet_ptr->vlet_type());
    unlikely_if (is_zone_disk() && volume_key.volume_id() == 0 && volume_key.vlet_type() == 0) {
        abort();
    }

    Status s = _vlet_indexer->exist(volume_key);
    if (s.code() == AIE_NOT_EXIST) {
        s = _vlet_indexer->put(*vlet_ptr);
    }
    if (!s.ok()) {
        LOG(FATAL) << _name << " write volume info to rocksdb failed, status:" << s;
        return s.code();
    }

    return AIE_OK;
}

int DiskAgent::add_vlet_if_absent(VletPtr& vlet_ptr) {
    uint64_t volume_id = vlet_ptr->volume_id();
    uint64_t total_size = vlet_ptr->total_size();

    LOG(NOTICE) << _name << "add_vlet vid:" << volume_id << " total_size:" << total_size;
    vlet_ptr->set_disk_agent(this);
    
    bool ret = _vlet_manager->add_if_absent(volume_id, vlet_ptr);
    if (!ret) {
        LOG(FATAL) << _name << "add_vlet failed, conflict with others vid:" << volume_id;
        return -1;
    }
    _vlet_map[volume_id] = vlet_ptr;
    _used_size += total_size;

    LOG(NOTICE) << _name << "add_vlet successed vid:" << volume_id;
    return 0;
}

int DiskAgent::add_vlet(VletPtr& vlet_ptr) {
    uint64_t volume_id = vlet_ptr->volume_id();
    uint64_t total_size = vlet_ptr->total_size();

    LOG(NOTICE) << _name << "add_vlet vid:" << volume_id << " total_size:" << total_size;
    vlet_ptr->set_disk_agent(this);

    assert(_vlet_manager->add(volume_id, vlet_ptr));
    _vlet_map[volume_id] = vlet_ptr;
    _used_size += total_size;

    return 0;
}

int DiskAgent::drop_vlet(VletPtr& vlet_ptr) {
    uint64_t volume_id = vlet_ptr->volume_id();
    uint64_t total_size = vlet_ptr->total_size();

    LOG(NOTICE) << _name << "drop_vlet vid:" << volume_id << " total_size:" << total_size;

    _vlet_map.erase(volume_id);
    _used_size -= vlet_ptr->total_size();

    return 0;
}

void DiskAgent::on_new_vlet(CopyVletArgContext* ctx) {
    std::shared_ptr<CopyVletWorker> copy_vlet_worker = ctx->worker;
    add_worker(copy_vlet_worker);

    DN_LOG(NOTICE) << _name << "new_vlet, CopyVletWorker:" << copy_vlet_worker.get();

    auto& vlet_info = copy_vlet_worker->vlet_info();
    auto& space_info = copy_vlet_worker->space_info();
    auto request = copy_vlet_worker->balance_requset();

    uint32_t target_disk_id = vlet_info.disk_id();
    assert(disk_id() == target_disk_id);

    uint64_t volume_id = vlet_info.volume_id();
    uint32_t shard_index = vlet_info.shard_index();
    uint64_t create_time = vlet_info.create_time();
    uint32_t vlet_type = vlet_info.vlet_type();
    uint64_t vlet_size = 0;
    if (vlet_info.has_vlet_engine_options()) {
        vlet_size = vlet_info.vlet_engine_options().vlet_size();
    } else {
        vlet_size = vlet_size_by_type(aries::VletType(vlet_type));
    }

    auto status = copy_vlet_worker->mutable_status();
    const std::string& path = disk_path();

    uint64_t disk_total_size = 0;
    uint64_t disk_free_size = 0;
    uint64_t disk_reserve_size = 0;

    VletPtr vlet_ptr = nullptr;

    while (1) {
        if (!_is_used) {
            status->set_code(AIE_FAIL);
            status->set_msg("disk is offline");
            break;
        }

        // check disk free size
        auto s = get_disk_usage(&disk_total_size, &disk_free_size);

        if (!s.ok()) {
            status->set_code(AIE_FAIL);
            status->set_msg("disk stat error");
            break;
        }
        if (request->has_disk_reserve_percent()) {
            disk_reserve_size = (disk_total_size * request->disk_reserve_percent()) / 100;
        } else {
            disk_reserve_size = (disk_total_size * FLAGS_disk_reserve_percent) / 100;
        }
        if (is_zone_disk()) {
            uint32_t reserved_zone_count_for_rewrite = FLAGS_rewrite_channel_count 
                    + FLAGS_max_rewrite_thread_num_per_disk;
            disk_reserve_size += reserved_zone_count_for_rewrite * _device->zone_size();
        }

        uint64_t need_disk_size = vlet_size;
        DN_LOG(TRACE) << _name << "new_vlet, disk_total_size:" << size_GB(disk_total_size) << "GB"
                      << " disk_reserve_size:" << size_GB(disk_reserve_size) << "GB"
                      << " disk_free_size:" << size_GB(disk_free_size) << "GB"
                      << " need_disk_size:" << size_GB(need_disk_size) << "GB";

        if (need_disk_size <= 0) {
            status->set_code(AIE_INVALID_ARGUMENT);
            status->set_msg("vlet type is error");
            break;
        }

        if (disk_free_size < (need_disk_size + disk_reserve_size)) {
            status->set_code(AIE_DISK_FULL);
            status->set_msg("disk full");
            break;
        }

        //db may has dirty index and try to remove
        Status remove_status = _vlet_indexer->destroy(VolumeKey(volume_id, vlet_type));
        if (!remove_status.ok()) {
            status->set_code(remove_status.code());
            status->set_msg(remove_status.msg());
        }

        break;
    } // end while

    copy_vlet_worker->start_copy(vlet_ptr);
}

void DiskAgent::on_submit_vlet(CopyVletArgContext* ctx) {
    std::shared_ptr<CopyVletWorker> copy_vlet_worker = ctx->worker;
    del_worker(copy_vlet_worker);
    std::unique_ptr<CopyVletArgContext> guard(ctx);
    VletPtr& vlet_ptr = copy_vlet_worker->vlet_ptr();
    if (!_is_used) {
       DN_LOG(NOTICE) << _name << "disk is offline, submit db failed, CopyVletWorker:" << copy_vlet_worker;
        if (vlet_ptr) {
            vlet_ptr->set_drop_me(true);
            vlet_ptr.reset();
        }
        return;
    }
    if (vlet_ptr) {
        DN_LOG(NOTICE) << _name << "submit_vlet succeeded, CopyVletWorker:" << copy_vlet_worker;
        // submit to db
        int ret = db_add_vlet(vlet_ptr);
        if (ret)  {
            DN_LOG(NOTICE) << _name << "submit_vlet failed, disk error, CopyVletWorker:" << copy_vlet_worker;
            vlet_ptr->set_drop_me(true);
            vlet_ptr.reset();
            std::string msg = base::string_printf("balance vlet submit db error");
            if (ret == AIE_IO_ERROR) {
                set_bad_disk(msg);
            }
            return;
        }
        add_vlet(vlet_ptr);
    } else {
        DN_LOG(NOTICE) << _name << "submit_vlet failed, CopyVletWorker:" << copy_vlet_worker;
    }
}

void DiskAgent::stop() {
    //stop background create/copy vlet worker;
    stop_create_or_copy_workers();

    // Stop heavyworker and ioexecutor, which may be slow
    _heavy_worker.stop();
    LOG(NOTICE) << _name << " heavyworker is stopped";
    _io_executor.join();
    LOG(NOTICE) << _name << " io_executor is stopped";
    _bg_executor.reset();
    LOG(NOTICE) << _name << " bg_executor is stopped";

    if (_compact_db_thread.joinable()) {
        _compact_db_thread.join();
    }

    _vlet_map.clear();
    g_fs->drop_disk(disk_path());
    if (nullptr != _zone_disk) {
        _device->set_bg_executor(nullptr);
        _zone_disk->close();
        _zone_disk.reset(nullptr);
    }

    // Release DB and clear vlet_map;
    if (_db) {
        db_close();
    }

    LOG(NOTICE) << _name << " stop is done";
}

void DiskAgent::on_check_overdue(CheckOverdueContext* ctx) {
    common::SyncPointGuard sync_point_guard(ctx->sync_point);

    common::ScopedMutexLock lock(_mutex);
    purge_overdue_call(&_pending_queue);
}

bool DiskAgent::match(IoContext* ctx) {
    uint64_t current_time = base::gettimeofday_ms();
    if ((current_time - ctx->en_queue_time) > ctx->timeout_ms) {
        fill_error_code_and_ack(ctx, AIE_BUSY, "call expired,discard");
        return true;
    }
    return false;
}

bool DiskAgent::match_all(IoContext* ctx) {
    fill_error_code_and_ack(ctx, AIE_NOT_EXIST, "vlet not exist,discard");
    return true;
}

void* DiskAgent::rebuild_vlet(void* arg) {
    auto* ctx = static_cast<AddVletContext*>(arg);
    VletPtr vlet_ptr = nullptr;

    auto on_rebuild_fail = [ctx](const std::string& msg) {
        auto* status = ctx->response->mutable_status();
        auto* vlet_mgr = ctx->disk_agent->vlet_manager();

        status->set_code(AIE_FAIL);
        status->set_msg(msg);
        vlet_mgr->create_done(ctx->vid, nullptr);
        delete ctx;
        return nullptr;
    };

    if (ctx->disk_agent->is_zone_disk()) {
        auto* zone_disk = ctx->disk_agent->get_zone_disk();

        aries::pb::GenericVletInfo vlet_info;
        Status s = zone_disk->pop_dropped_vlet(ctx->vid, &vlet_info);
        unlikely_if (!s.ok()) {
            LOG(WARNING) << "rebuild failed due to vlet not not exist in zone_disk, vid:" << ctx->vid
                         << " status:" << s;
            return on_rebuild_fail("vid not exist");
        }

        if (vlet_info.shard_index() != ctx->shard_index) {
            LOG(WARNING) << "rebuild failed due to shard index not match, vid:" << ctx->vid
                    << " request shard_index:" << ctx->shard_index
                    << " actual shard_index:" << vlet_info.shard_index();
            Status s = zone_disk->add_dropped_vlet(vlet_info);
            if (!s.ok()) {
                LOG(WARNING) << "zone disk failed to add dropped vlet, vid:" << vlet_info.volume_id()
                             << " status:" << s;
            }

            return on_rebuild_fail("shard index not match");
        }

        vlet_info.mutable_zone_vlet_info()->clear_dropped_timestamp();
        vlet_ptr = std::make_shared<ZoneVlet>(ctx->disk_agent, vlet_info);
    } else {
        base::FilePath file(ctx->file_name);
        if (file.BaseName().value().compare(0, 2, "L_") == 0) {
            vlet_ptr = std::make_shared<LinkedVlet>(ctx->disk_agent);
        } else if (file.BaseName().value().compare(0, 2, "A_") == 0) {
            vlet_ptr = std::make_shared<AppendVlet>(ctx->disk_agent);
        } else {
            LOG(WARNING) << "add a bad vlet name:" << ctx->file_name;
            return on_rebuild_fail("bad vlet name");
        }
    }

    int ret = vlet_ptr->open(ctx->file_name);
    if (ret == 0) {
        VletReportContext* pCtx = new VletReportContext();
        aries::pb::DatanodeVletInfo info;
        vlet_ptr->fill_vlet_info(&info);
        info.set_disk_id(ctx->disk_agent->disk_id());
        pCtx->push_back(info);
        QueueExec(VLET_REPORT_ACTION, g_datanode->cleaner(), pCtx);
    } else {
        vlet_ptr.reset();
    }
    ctx->vlet_ptr = vlet_ptr;
    QueueExec(ADD_VLET_ACTION, ctx->disk_agent, ctx);
    return nullptr;
}

void DiskAgent::on_add_vlet(AddVletContext* ctx) {
    std::unique_ptr<AddVletContext> hold_ctx(ctx);
 
    auto& status = ctx->status;
    auto& vlet_ptr = ctx->vlet_ptr;
    if (!_is_used) {
        status->set_code(AIE_FAIL);
        status->set_msg("disk is offline");
        _vlet_manager->create_done(ctx->vid, nullptr);
        vlet_ptr.reset();
        return;
    }
    if (vlet_ptr) {//add to _vlet_map && _vlet_manager
        int ret = db_add_vlet(vlet_ptr);
        if (ret) {
            status->set_code(AIE_FAIL);
            status->set_msg("disk error, submit db failed");
            vlet_ptr.reset();
            _vlet_manager->create_done(ctx->vid, nullptr);
            std::string msg = base::string_printf("add vlet submit db error, log_id: %lu", ctx->log_id);
            if (ret == AIE_IO_ERROR) {
                set_bad_disk(msg);
            }
            return;
        }

        add_vlet(vlet_ptr);// vlet_map add vlet
        status->set_code(AIE_OK);
    } else {//false,error ack
        status->set_code(AIE_FAIL);
        status->set_msg("open vlet failed, add_vlet failed");
    }

    _vlet_manager->create_done(ctx->vid, vlet_ptr);
    return;
}

void DiskAgent::on_rebuild_vlet(AddVletContext* ctx) {

    auto& res =  ctx->response;
    auto status = res->mutable_status();
    if (!_is_used) {
        status->set_code(AIE_FAIL);
        status->set_msg("disk is offline");
        delete ctx;
        return;
    }
    if (_vlet_manager->vlet_refed(ctx->vid, -1, -1)) {
        status->set_code(AIE_EXIST);
        status->set_msg("vlet exist");
        delete ctx;
        return;
    }

    int ret = _vlet_manager->create(ctx->vid);
    if (ret < 0) {
        status->set_code(AIE_EXIST);
        status->set_msg("vlet already exist");
        delete ctx;
        return ;
    }

    ctx->disk_agent = this;
    ctx->db = _db;
    bthread_t th1;
    if (bthread_start_background(&th1, NULL, DiskAgent::rebuild_vlet, ctx) != 0) {
        LOG(ERROR) << "Failed to create bthread for add_vlet, logid:" << ctx->log_id;
        status->set_code(AIE_FAIL);
        status->set_msg("start bthread failed");
        delete ctx;
        _vlet_manager->create_done(ctx->vid, nullptr);
        return ;
    }
}

void DiskAgent::on_check_shard(ShardCheckContext* ctx) {
    _shard_checker_ptr->queue_exec(CHECK_SHARD_ACTION, ctx->vlet_ptr->volume_id(), ctx);
    return;
}

int DiskAgent::open_zone_disk() {
    LOG(TRACE) << _name << "start open zone disk";
    _device = std::unique_ptr<ZoneDeviceBackend>(create_zone_device(_zone_device_type));
    if (_device == nullptr) {
        LOG(FATAL) << _name << "create zone device failed";
        return AIE_FAIL;
    }

    DeviceOpenOptions disk_options;
    disk_options.id = disk_id();
    disk_options.path = disk_path();
    disk_options.type = common::string2disk_type(_disk_conf.disk_type());
    disk_options.zone_size = _disk_conf.zone_size_mb() * common::MB;
    disk_options.capacity = _disk_conf.capacity_mb() * common::MB;

    uint64_t disk_total_size = 0;
    uint64_t disk_free_size = 0;
    auto ret_s = g_fs->get_disk_capacity(disk_path(), &disk_total_size, &disk_free_size);
    if (!ret_s.ok()) {
        LOG(WARNING) << _name << "failed to open zone device due to get disk usage failed,"
                     << " disk_id:" << disk_options.id << " disk_path:" << disk_options.path
                     << " error:" << ret_s;
        return ret_s.code();
    }
    uint64_t disk_reserve_size = (disk_total_size * FLAGS_disk_reserve_percent) / 100;
    disk_options.capacity = std::min((uint64_t)disk_options.capacity, disk_total_size - disk_reserve_size);
    disk_options.align_size = 4 * common::KB;

    if (_bg_executor == nullptr) {
        _bg_executor = std::make_unique<folly::CPUThreadPoolExecutor>(FLAGS_bg_threadpool_thread_num);
    }
    _device->set_bg_executor(_bg_executor.get());
    int ret = _device->open(disk_options);
    if (AIE_OK != ret) {
        LOG(WARNING) << _name << "failed to open zone device, disk_id:" << disk_options.id << " error:" << ret;
        return ret;
    }

    ZoneDisk* zone_disk = new ZoneDisk(_device.get(), _db, this);
    auto status = zone_disk->open();
    if (!status.ok()) {
        LOG(FATAL) << _name << "open disk failed, disk_id:" << disk_options.id << " return status:" << status;
        delete zone_disk;
        return AIE_FAIL;
    }

    _zone_disk.reset(zone_disk);
    return AIE_OK;
}

void DiskAgent::init_ioutil_collector() {
    IoutilCollector* ioutil_collector(new IoutilCollector(disk_path(), FLAGS_ioutil_window_second));

    assert(ioutil_collector != nullptr);
    ioutil_collector->init();
    _ioutil_collector.reset(ioutil_collector);
}

Status DiskAgent::get_disk_usage(uint64_t* disk_total_size,
                                 uint64_t* disk_free_size) {
    if (is_zone_disk()) {
        if (nullptr == _zone_disk) {
            return Status(AIE_FAIL, "zone_disk ptr is nullptr");
        }
        uint64_t real_disk_free_size = 0;
        auto status = g_fs->get_disk_capacity(disk_path(), disk_total_size, &real_disk_free_size);
        if (!status.ok()) {
            return status;
        }
        ZoneDeviceZoneStatus zone_status;
        _zone_disk->get_zone_status(&zone_status);
        uint64_t total_zone_size = zone_status.zone_cnt * zone_status.zone_size;
        uint64_t total_vlet_size = used_size();
        if (total_zone_size > total_vlet_size) {
            *disk_free_size = real_disk_free_size + (total_zone_size - total_vlet_size);
        } else {
            *disk_free_size = real_disk_free_size - (total_vlet_size - total_zone_size);
        }
        return Status();
    } else {
        return g_fs->get_disk_capacity(disk_path(), disk_total_size, disk_free_size);
    }
}

void DiskAgent::start_io_executor() {
    _io_executor.start(_io_thread_num, FLAGS_io_executor_queue_capacity_per_priority);
}

void DiskAgent::start_heavyworker() {
    _heavy_worker.set_heavy_worker(this, db());
    _heavy_worker.start();
}

void DiskAgent::compact_db() {
    uint32_t wait_seconds = 0;
    while (!_is_stop.load() && _is_used.load()) {
        if (wait_seconds < 60) {
            std::this_thread::sleep_for(std::chrono::seconds(1));
            wait_seconds++;
            continue;
        }
        wait_seconds = 0;
        uint64_t vid = 0;
        {
            common::ScopedMutexLock lock(_mutex);
            if (_need_compact_db_vlets.empty()) {
                continue;
            }
            vid = *_need_compact_db_vlets.begin();
            if (!_vlet_manager || _vlet_manager->exist_in_doing_create(vid)) {
                continue;
            }
            _need_compact_db_vlets.erase(_need_compact_db_vlets.begin());
        }

        LOG(TRACE) << "try to CompactRange, db:" << _db->GetName() << " volume_id:" << vid;

        rocksdb::Status s;
        if (is_zone_disk()) {
            ZoneRecordIndexKey begin(vid, 0, 0), end(vid, UINT64_MAX, UINT32_MAX);
            rocksdb::Slice begin_key(begin.to_slice()), end_key(end.to_slice());
            s = _db->CompactRange(rocksdb::CompactRangeOptions(), &begin_key, &end_key);
        } else {
            RecordKey begin(vid, 0), end(vid, UINT64_MAX);
            rocksdb::Slice begin_key(begin.to_slice()), end_key(end.to_slice());
            s = _db->CompactRange(rocksdb::CompactRangeOptions(), &begin_key, &end_key);
        }

        if (!s.ok()) {
            // ignore error
            LOG(FATAL) << "CompactRange failed, db:" << _db->GetName() << " volume_id:" << vid;
        }
    }
}

bool DiskAgent::is_reach_qps_limit(IoContext* ctx) {
    // ssd will not limited by iops
    if (!FLAGS_enable_disk_iops_limit || !_disk_qps_limiter) {
        return false;
    } 

    // only linked vlet will be limited by iops
    auto vlet_type = ctx->vlet_ptr->vlet_type();
    if (common::vlet_type_by_vlet((aries::VletType)vlet_type) != ENGINE_LINKED) {
        return false;
    }

    // iops limit only work for repair
    if (ctx->priority == aries::HIGH) {
        // result is true means not reach iops limitation, should return false
        bool ret = _disk_qps_limiter->get(1, ctx->speed_coefficient);
        TEST_SYNC_POINT_CALLBACK("is_reach_qps_limit", &ret);
        return !ret;
    }
    return false;
} 

ZoneDeviceBackend* DiskAgent::create_zone_device(ZoneDeviceType zone_device_type) {
    if (zone_device_type == ZoneDeviceType::FILE) {
        return create_file_zone_device(_db);
    } else {
        LOG(WARNING) << "not supported zone device_type:" << zone_device_type;
        return nullptr;
    }
}

uint32_t DiskAgent::get_zone_file_count() const {
    uint32_t file_count = 0;
    base::FilePath dir(disk_path());
    base::FileEnumerator d_enum(dir, false, base::FileEnumerator::FILES);
    for (base::FilePath name = d_enum.Next(); !name.empty(); name = d_enum.Next()) {
        std::string filename = name.BaseName().value();
        if (filename.compare(0, 2, "Z_") == 0) {
            file_count++;
        }
    }
    return file_count;
}

VletIndexer*  DiskAgent::vlet_indexer() {
    return _vlet_indexer.get();
}
} // end namespace of datanode
} // end namespace of aries

/* vim: set ts=4 sw=4 sts=4 tw=100: */
