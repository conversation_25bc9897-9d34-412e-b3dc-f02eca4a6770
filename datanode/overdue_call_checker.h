// Copyright (c) 2017 Baidu.com, Inc. All Rights Reserved
// Author: chenjiang,<EMAIL>

#ifndef BAIDU_INF_ARIES_DATANODE_OVERDUE_CALL_CHECKER_H
#define BAIDU_INF_ARIES_DATANODE_OVERDUE_CALL_CHECKER_H

#include <sys/statvfs.h>
#include "baidu/inf/aries/datanode/util/kylin.h" // CAsyncClient, QueueExec, AsyncContext
#include "baidu//inf/aries-api/common/config.h"
#include "baidu/inf/aries/datanode/flags.h"
#include "baidu/inf/aries-api/common/types.h"
#include "baidu/inf/aries/datanode/util/util.h"
#include "baidu/inf/aries/datanode/disk_agent.h"

namespace aries {
namespace datanode {

class OverdueCallChecker: public CAsyncClient {
public:
    OverdueCallChecker() {
        InitAsyncContext(&_timer_context);
        _name = "overdue_call";
    }

    int init(int host_id, const std::map<int, DiskAgentPtr>& disk_agent_map) {
        _disk_agent_map = disk_agent_map;
        LOG(NOTICE) << _name << "init, host_id:" << host_id
                << ", disk_num:" << disk_agent_map.size();

        m_nHostId = host_id;
        return ++host_id;
    }

    void start() {
        DelayExec(0, this, FLAGS_check_timeout_interval_s * 1000UL, &_timer_context);
    }

private:

    int check_overdue() {
        std::vector<CheckOverdueContext> vec; 
        vec.resize(_disk_agent_map.size());
        common::SyncPoint sync(_disk_agent_map.size());
        int i = 0;
        for (auto& kv : _disk_agent_map) {
            vec[i].sync_point = &sync;
            QueueExec(CHECK_OVERDUE_ACTION, kv.second, &vec[i]);
            i++;
        }
        sync.wait();
        return 0;
    }

    virtual void OnCompletion(AsyncContext* pCtx) {
        DelayExec(0, this, FLAGS_check_timeout_interval_s * 1000UL, &_timer_context);
        check_overdue();
    }
    
    AsyncContext                    _timer_context;
    std::map<int, DiskAgentPtr>     _disk_agent_map;

    std::string                     _name;
};

} // end namespace of datanode
} // end namespace of aries

#endif 

/* vim: set ts=4 sw=4 sts=4 tw=100 */
