#pragma once

#include <functional>
#include <optional>
#include <mutex>
#include <bthread_unstable.h>
#include "baidu/inf/aries/datanode/flags.h"
#include "baidu/inf/aries/datanode/disk_manager.h"
#include "baidu/inf/aries/datanode/cache/cache_define.h"
#include "baidu/inf/aries/datanode/cache/entry_cache.h"
#include "baidu/inf/aries/datanode/cache/admission_policy/frequency_admission_policy.h"
#include "baidu/inf/aries/datanode/cache/cache_io_executor.h"
#include "folly/executors/CPUThreadPoolExecutor.h"
#include "baidu/inf/aries/common/gate.h"

namespace aries {
namespace datanode {

class CacheStatistics {
public:
    CacheStatistics() : _is_started(false),
            _is_started_bvar{"datanode_cache_enabled", false},
            _hit_percent_window_bvar{"datanode_cache_hit_percent", &_hit_percent_bvar, 60},
            _hit_count_bvar{"datanode_cache_hit_count"},
            _hit_counter_per_minute_bvar{"datanode_cache_hit_per_minute", &_hit_count_bvar, 60},
            _miss_count_bvar{"datanode_cache_miss_count"},
            _miss_counter_per_minute_bvar{"datanode_cache_miss_per_minute", &_miss_count_bvar, 60}
    {}

    void on_start() {
        _hit_count = 0;
        _miss_count = 0;

        _is_started = true;
        _is_started_bvar.set_value(true);

        update_bvar(this);
    }

    void on_stop() {
        _is_started = false;
        _is_started_bvar.set_value(false);
        bthread_timer_del(_update_bvar_timer);
    }

    void on_cache_hit(aries::Qos priority) {
        _hit_count.fetch_add(1, std::memory_order_relaxed);
    }

    void on_cache_miss(aries::Qos priority) {
        _miss_count.fetch_add(1, std::memory_order_relaxed);
    }

private:
    static constexpr time_t BVAR_UPDATE_INTERVAL_SEC{1}; // update bvar every 1s

    static void update_bvar(void* statistics) {
        auto& self = *reinterpret_cast<CacheStatistics*>(statistics);
        if (! self._is_started) {
            return;
        }

        int64_t hit = self._hit_count.exchange(0);
        int64_t all = self._miss_count.exchange(0) + hit;
        double hit_percent = (hit > all || hit <= 0) ? 0.0 : 100.0 * hit / all;

        self._hit_count_bvar << hit;
        self._miss_count_bvar << all - hit;
        self._hit_percent_bvar << static_cast<int64_t>(hit_percent);

        bthread_timer_add(&self._update_bvar_timer,
                base::seconds_from_now(BVAR_UPDATE_INTERVAL_SEC),
                update_bvar,
                &self);
    }

    bthread_timer_t _update_bvar_timer;
    std::atomic<int64_t> _hit_count{0};
    std::atomic<int64_t> _miss_count{0};

    std::atomic<bool> _is_started;
    bvar::Status<bool> _is_started_bvar;

    bvar::IntRecorder _hit_percent_bvar;
    bvar::Window<bvar::IntRecorder> _hit_percent_window_bvar;

    bvar::Adder<int64_t> _hit_count_bvar;
    bvar::Window<bvar::Adder<int64_t>> _hit_counter_per_minute_bvar;

    bvar::Adder<int64_t> _miss_count_bvar;
    bvar::Window<bvar::Adder<int64_t>> _miss_counter_per_minute_bvar;
};

extern CacheStatistics g_datanode_cache_statistics;

// CacheManager: control and manage (ssd)cache
class CacheManager {
public:
    CacheManager() {
        _entry_cache = std::make_unique<EntryCache>();
        _cache_io_executor = std::make_unique<CacheIOExecutor>(this);
        _gate.reset(true);
    }

    ~CacheManager() = default;

    int start(const std::string& engine_type, const std::string& evict_type);

    void stop();

    // check cache is enabled and started
    bool enabled() const { return !_gate.is_closed(); }

    // cache
    int remove(const ShardId& shard_id);

    int exist(const ShardId& shard_id, bool* exist);

    std::vector<ShardId> visit(std::function<bool(const CacheItemRecord&)>&& filter);

    bool is_tracked(const ShardId& shard_id);

    int insert(const ShardId& shard_id, const pb::ShardMeta& meta,
               const base::IOBuf& data, const uint64_t pin_ttl_secs = 0);

    int lookup(const ShardId& shard_id, LookupOptions&& options,
               pb::ShardMeta* meta, base::IOBuf* data);

    IndexLookupResult lookup_index(const ShardId& key) const;

    void lookup_index(const std::vector<ShardId>& key_list, std::vector<IndexLookupResult>* key_infos) const;

    CacheEngineType get_engine_type() const;

    void list_storage(std::vector<CacheStorageStat>* stats) const;

    CacheEngineStat get_engine_stat() const;

    // executor task
    int dispatch_add_to_cache_task(AddRecordToCacheContext* ctx);

    int dispatch_remove_from_cache_task(RemoveRecordFromCacheContext* ctx);

    int dispatch_get_task(GetFromCacheContext* ctx);

    int dispatch_admit_task(const ShardId& shard_id, const aries::pb::ShardMeta& meta,
                            const base::IOBuf& data, uint32_t cache_pin_ttl_secs = 0);

    // DO set_disk_conf before start !!!
    void set_cache_disks_conf(const CacheDisksConf& conf);
    CacheDisksConf* cache_disks_conf() const { return _disks_conf.get(); }

    // cache control
    int add_cache_disk(uint32_t disk_id);

    int load_cache_disk(uint32_t disk_id);

    int drop_cache_disk(uint32_t disk_id);

    void drop_vlet(uint64_t volume_id, int32_t shard_index);

    int drop_storage(uint64_t storage_id);

    int update_disk(const aries::pb::DiskConfigure& disk_conf);

    static bool check_cache_path(const std::string& path);

    aries::pb::DiskConfigure* get_cache_disk_conf(uint32_t disk_id) const;

    int load_cache_disks();

    friend class CacheIOExecutor;

private:
    // Called when cache size may change, e.g. when drop_disk,
    // add_cache_disk, drop_storage, etc.
    // REQUIRES: _gate not entered.
    void on_storage_list_size_update();

private:
    // to protect member to access safely
    mutable common::Gate _gate;
    std::mutex _gate_state_change_lock; // hold when _gate state change (open / close)
    std::unique_ptr<CacheDisksConf> _disks_conf{nullptr};
    std::unique_ptr<EntryCache> _entry_cache{nullptr};
    std::unique_ptr<AdmissionPolicy> _admission_policy{nullptr};
    std::unique_ptr<CacheIOExecutor> _cache_io_executor{nullptr};
    std::shared_ptr<AccessFrequencyManager> _access_frequency_mgr{nullptr};
    std::shared_ptr<folly::CPUThreadPoolExecutor> _thread_pool{nullptr};
    common::ConfigReloader::Handle _fool_proofing_handle{-1};
};

}
}
