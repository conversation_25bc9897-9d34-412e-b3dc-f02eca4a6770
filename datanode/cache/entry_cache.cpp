#include <algorithm>
#include <bthread.h>
#include <bthread_unstable.h>
#include <bthread.h>
#include "base/string_printf.h"
#include "baidu/inf/aries-api/common/bthread_executor.h"
#include "baidu/inf/aries/datanode/cache/entry_cache.h"
#include "baidu/inf/aries/datanode/cache/eviction_policy/lru_policy.h"
#include "baidu/inf/aries/datanode/cache/eviction_policy/arc_policy.h"
#include "baidu/inf/aries/datanode/cache/engine/cache_linked_engine.h"
#include "baidu/inf/aries-api/common/rpc_stub.h"
#include "baidu/inf/aries/datanode/util/util.h"

namespace aries {
namespace datanode {

ARIES_BVAR_LATENCY(datanode_cache, read_succ);
ARIES_BVAR_LATENCY(datanode_cache, write_succ);
ARIES_BVAR_LATENCY(datanode_cache, remove_succ);

ARIES_BVAR_COUNTER_MIN(datanode_cache, get_error);
ARIES_BVAR_COUNTER_MIN(datanode_cache, put_error);

ARIES_BVAR_ADDER(datanode_cache, entry_count);

ARIES_BVAR_RECORDER(datanode_cache, engine_entry_count);
ARIES_BVAR_RECORDER(datanode_cache, engine_capacity_size_mb);
ARIES_BVAR_RECORDER(datanode_cache, engine_free_size_mb);
ARIES_BVAR_RECORDER(datanode_cache, cache_overflow_size_mb);

common::BvarStatusWithTag<int32_t> g_datanode_cache_size_distribution("datanode_cache_item_size", "");
common::BvarStatusWithTag<int32_t> g_datanode_cache_hits_distribution("datanode_cache_hits", "");
common::BvarStatusWithTag<int32_t> g_datanode_cache_eviction_age_distribution("datanode_cache_eviction_age_min", "");

EntryCache::~EntryCache() {
    stop();
}

int EntryCache::start(std::unique_ptr<CacheEngineInterface> engine,
                      std::unique_ptr<EvictionPolicyInterface> policy,
                      std::shared_ptr<folly::CPUThreadPoolExecutor> thread_pool) {
    if (ARIES_UNLIKELY(engine == nullptr || policy == nullptr)) {
        LOG(WARNING) << "engine or policy is nullptr";
        return AIE_INVALID_ARGUMENT;
    }

    _thread_pool = folly::SerialExecutor::create(thread_pool.get());
    _policy = std::move(policy);
    _engine = std::move(engine);

    init_distribute_stat();

    add_handle_inactive_shards_timer();
    add_update_monitor_stat_timer();

    _is_stopped = false;

    _pending_reclaim_task_count = 0;
    return AIE_OK;
}

int EntryCache::start(const std::string& engine_type, const std::string& evict_type,
                      std::shared_ptr<folly::CPUThreadPoolExecutor> thread_pool) {
    if (!_is_stopped) {
        LOG(WARNING) << "cache start failed, repeated start";
        return AIE_FAIL;
    }

    std::string engine_type_str = engine_type;
    std::transform(engine_type_str.begin(), engine_type_str.end(), engine_type_str.begin(), ::toupper);
    auto engine_type_optional = string_to_enum(CacheEngineType, engine_type_str);
    if (!engine_type_optional.has_value() || engine_type_optional.value() == CacheEngineType::INVALID) {
        return AIE_INVALID_ARGUMENT;
    }

    std::string evict_policy_type_str = evict_type;
    std::transform(evict_policy_type_str.begin(), evict_policy_type_str.end(), evict_policy_type_str.begin(), ::toupper);
    auto eviction_type_optional = string_to_enum(EvictionPolicyType, evict_policy_type_str);
    if (!eviction_type_optional.has_value() || *eviction_type_optional == EvictionPolicyType::INVALID) {
        return AIE_INVALID_ARGUMENT;
    }

    return start(*engine_type_optional, *eviction_type_optional, thread_pool);
}

int EntryCache::start(CacheEngineType engine_type, EvictionPolicyType evict_type,
                      std::shared_ptr<folly::CPUThreadPoolExecutor> thread_pool) {
    std::unique_ptr<CacheEngineInterface> engine;
    std::unique_ptr<EvictionPolicyInterface> policy;
    LOG(NOTICE) << "cache start, engine:" << engine_type << " eviction policy:" << evict_type;

    if (engine_type == CacheEngineType::LINKED) {
        auto linked_engine = std::make_unique<CacheLinkedEngine>(std::make_unique<FreeSizeAwarePickPolicy>());
        engine = std::move(linked_engine);
    } else {
        LOG(WARNING) << "cache start failed, engine type is not supported, engine_type:" << engine_type;
        return AIE_NOTSUPPORT;
    }

    if (_disks_conf == nullptr) {
        LOG(WARNING) << "cache start failed due to miss disk conf";
        return AIE_FAIL;
    }

    // using disk configure to estimate the capacity of cache
    int64_t capacity = get_conf_total_capacity(_disks_conf);
    if (evict_type == EvictionPolicyType::LRU) {
        policy = std::make_unique<LruPolicy>([this]() -> uint64_t {
                {
                    folly::SharedMutex::WriteHolder w{&_engine_stat_mutex};
                    _engine_stat = _engine->get_engine_stat();
                }
                return get_overflow_size();
        });
    } else if(evict_type == EvictionPolicyType::ARC) {
        auto available_space = [this]() mutable {
            {
                folly::SharedMutex::WriteHolder w{&_engine_stat_mutex};
                _engine_stat = _engine->get_engine_stat();;
            }
            return get_available_size();
        };
        policy = std::make_unique<ArcPolicy>(capacity, std::move(available_space));
    } else {
        LOG(WARNING) << "cache start failed, eviction policy is not supported, engine_type:" << evict_type;
        return AIE_NOTSUPPORT;
    }

    auto ret = start(std::move(engine), std::move(policy), thread_pool);
    if (ret != AIE_OK) {
        LOG(WARNING) << "cache start failed, evict_type:" << evict_type << " engine_type:"  << engine_type;
        return ret;
    }

    LOG(NOTICE) << "cache start succeeded, evict_type:" << evict_type << " engine_type:"  << engine_type  << std::dec
                << " capacity_gb:" << capacity / common::GB;
    return AIE_OK;
}

void EntryCache::stop() {
    if (_is_stopped) {
        return;
    }

    LOG(NOTICE) << "cache is stopping";
    _is_stopped = true;

    bthread_timer_del(_handle_inactive_shard_timer);
    bthread_timer_del(_update_monitor_stat_timer);

    _thread_pool.reset();
    _policy.reset();   // destroy  _policy
    _engine.reset();
}

int EntryCache::restart(const std::string& engine_type, const std::string& evict_type) {
    if (!_is_stopped) {
        stop();
    }
    return start(engine_type, evict_type);
}

int EntryCache::insert(const ShardId& shard_id, const pb::ShardMeta& meta,
                       const base::IOBuf& data, const uint64_t pin_ttl_secs) {
    uint64_t current_time_us = base::gettimeofday_us();
    if (_is_stopped) {
        LOG(WARNING) << "insert shard failed due to cache has been stopped, " << shard_id;
        return AIE_FAIL;
    }

    if (ARIES_UNLIKELY(!shard_id.valid())) {
        LOG(WARNING) << "insert shard failed due to shard id is invalid, " << shard_id;
        return AIE_INVALID_ARGUMENT;
    }

    // Add reclaim task if it is overflow. Ensure that only one eviction task is running at a time
    if (_pending_reclaim_task_count.load(std::memory_order_relaxed) == 0 &&
                get_overflow_size() > 0 && _thread_pool) {
        bool do_reclaim = true;
        if (_pending_reclaim_task_count.fetch_add(1) != 0) {
            _pending_reclaim_task_count--;
            do_reclaim = false;
        }

        if (do_reclaim) {
            try {
                _thread_pool->addWithPriority([this]() {  // executor in serial order
                    this->start_reclaim();
                    _pending_reclaim_task_count--;
                }, folly::Executor::HI_PRI);
            } catch (folly::QueueFullException& e) {
                LOG(WARNING) << "priority queue is full, queue: " << std::to_string(aries::DEGRADE_HIGH) << ", "
                             << typeid(e).name() << " exception: " << e.what();
            } catch (const std::exception& e) {
                LOG(WARNING) << "addWithPriority threw std::exception"
                             << typeid(e).name() << " exception: " << e.what();
            } catch (...) {
                LOG(WARNING) << "ThreadPoolExecutor: func threw unhandled non-exception object";
            }
        }
    }

    CacheShardInfo shard_info;
    int ret = _engine->put(shard_id, meta, data, &shard_info);
    if (ret != AIE_OK) {
        g_datanode_cache_put_error_counter << 1;
        if (ret == AIE_EXIST) {
            LOG(NOTICE) << "write shard failed, due to shard exist, shard_id:" << shard_id;
        } else {
            LOG(WARNING) << "write shard failed, " << shard_id << ", ret: " << ret;
        }
        return ret;
    }

    // do not pin if too many pinned data
    if (pin_ttl_secs != 0 && _curr_pinned_size + shard_info.occurred_size <= max_pinned_size()) {
        _curr_pinned_size += shard_info.occurred_size;
        shard_info.pin_ttl_secs = pin_ttl_secs;
    } else if (pin_ttl_secs != 0) {
        LOG(WARNING) << "discards pin_ttl_secs when insert shard to cache due to too many pinned data,"
                     << " shard:" << shard_id
                     << " curr_pinned_size:" << _curr_pinned_size
                     << " max_pinned_size:" << max_pinned_size();
    }

    bool succ = _policy->track(shard_id, shard_info);
    if (!succ) {
        LOG(NOTICE) << "shard needs to be deleted from storage due to track failed, shard:" << shard_info;
        if (int ret = _engine->del(shard_id, shard_info); ret != AIE_OK) {
            LOG(WARNING) << "delete shard from storage failed, ret:" << ret << " shard:" << shard_info;
        }
        return AIE_FAIL;
    }

    g_datanode_cache_write_succ_latency <<  base::gettimeofday_us() - current_time_us;
    g_datanode_cache_entry_count_adder << 1;
    _put_size_dist->add((int64_t)shard_info.occurred_size);

    return AIE_OK;
}

int EntryCache::lookup(const ShardId& shard_id,
                       LookupOptions&& options,
                       pb::ShardMeta* meta,
                       base::IOBuf* data) {
    uint64_t current_time_us = base::gettimeofday_us();
    if (_is_stopped) {
        LOG(WARNING) << "lookup shard failed due to cache has been stopped, " << shard_id;
        return AIE_FAIL;
    }

    if (ARIES_UNLIKELY(!shard_id.valid())) {
        LOG(WARNING) << "lookup shard failed due to invalid shard_id, " << shard_id;
        return AIE_INVALID_ARGUMENT;
    }

    const auto lr = _policy->lookup_index(shard_id);
    if (!lr.found()) {
        return AIE_NOT_EXIST;
    } else {
        auto shard_info = lr.record().shard_info;
        if (!shard_info.is_valid()) {
            g_datanode_cache_get_error_counter << 1;
            LOG(WARNING) << "lookup shard failed due to invalid cache shard info, shard info:" << shard_info;
            return AIE_INVALID;
        } else if (meta == nullptr && data == nullptr) {
            return AIE_OK; // just querying the index
        } else {
            int ret = AIE_OK;
            if (!lr.has_data()) {
                if (options.prefetch.has_value()) {
                    // if don't have data, fetch data from secondary storage, and put into cache storage
                    ret = prefetch(shard_id, std::move(*options.prefetch), &shard_info);
                    if (ret != AIE_OK) {
                        if (ret == AIE_REMOVED) {
                            // data is put to cache storage, meanwhile its index is removed.
                            // need to remove data from storage
                            if (_engine->del(shard_id, shard_info) == AIE_OK && shard_info.pin_ttl_secs != 0) {
                                auto shard_size = shard_info.occurred_size;
                                if (UNLIKELY(_curr_pinned_size.fetch_sub(shard_size) < (int64_t)shard_size)) { // overflow
                                    _curr_pinned_size = 0;
                                }
                            }
                        }
                        return ret;
                    }

                    // shard_info is updated after prefetch
                    if (!shard_info.is_valid()) {
                        return AIE_FAIL;
                    }
                } else {
                    return AIE_FAIL;
                }
            }
            // get from cache storage
            ret = _engine->get(shard_id, shard_info, meta, data);
            if (ret != AIE_OK) {
                g_datanode_cache_get_error_counter << 1;
            } else {
                _policy->touch(shard_id);
                g_datanode_cache_read_succ_latency << base::gettimeofday_us() - current_time_us;
            }
            return ret;
        }
    }
}

int EntryCache::remove(const ShardId& shard_id, bool sync) {
    uint64_t current_time_us = base::gettimeofday_us();
    if (_is_stopped) {
        LOG(WARNING) << "remove shard failed due to cache has been stopped, " << shard_id;
        return AIE_FAIL;
    }

    if (ARIES_UNLIKELY(!shard_id.valid())) {
        LOG(WARNING) << "remove shard failed due shard id is invalid, " << shard_id;
        return AIE_INVALID_ARGUMENT;
    }

    if (ARIES_UNLIKELY(sync)) {
        CacheItemRecord record;
        int ret = _policy->evict(shard_id, &record);
        if (ret != AIE_OK) { // shard not exist
            return ret;
        }

        ret = _engine->del(shard_id, record.shard_info);
        if (ret == AIE_OK && record.shard_info.pin_ttl_secs != 0) {
            auto shard_size = record.shard_info.occurred_size;
            ARIES_UNLIKELY_IF (_curr_pinned_size.fetch_sub(shard_size) < shard_size) { // overflow
                _curr_pinned_size = 0;
            }
        }
        return ret;
    } else {
        // mark delete in index, then delete it from cache
        // after it is evicted by eviction policy
        auto lr = _policy->mark_delete(shard_id);
        if (!lr.found()) {
            return AIE_NOT_EXIST;
        } else {
            g_datanode_cache_remove_succ_latency <<  base::gettimeofday_us() - current_time_us;
        }
    }

    return AIE_OK;
}

void EntryCache::reset() {
    _policy.reset();
    _engine.reset();
    _is_stopped = false;
}

// For ARC, entry in ARC ghost is tracked, but is not exist
// For LRU, equal to exist
bool EntryCache::is_tracked(const ShardId& shard_id) {
    return _policy->is_tracked(shard_id);
}

bool EntryCache::exist(const ShardId& shard_id) {
    auto lr = _policy->lookup_index(shard_id);
    return lr.found() && lr.has_data();
}

void EntryCache::init_distribute_stat() {
    {
        int64_t lower = PAGE_SIZE_4K;
        int64_t upper = PAGE_SIZE_4K * 512;
        std::vector<common::Distribution::Range> size_list;
        size_list.emplace_back(common::Distribution::Range(0, false, lower, true));
        while (lower < upper) {
            auto tmp = static_cast<int64_t>(lower * 2);
            tmp = std::min(tmp, upper);
            size_list.emplace_back(common::Distribution::Range(lower, false, tmp, true));
            lower = tmp;
        }
        size_list.emplace_back(common::Distribution::Range(upper, false, common::GB, true));
        _put_size_dist = std::make_unique<common::Distribution>(size_list);
    }

    {
        std::initializer_list<common::Distribution::Range> ranges{
                common::Distribution::Range(1),
                common::Distribution::Range(2),
                common::Distribution::Range(3),
                common::Distribution::Range(4),
                common::Distribution::Range(5),
                common::Distribution::Range(6, true, 10, true),
                common::Distribution::Range(11, true, 20, true),
                common::Distribution::Range(21, true, 30, true),
                common::Distribution::Range(31, true, 50, true),
                common::Distribution::Range(51, true, 100, true),
                common::Distribution::Range(101, true, 1000, true),
                common::Distribution::Range(1001, true, 5000, true),
                common::Distribution::Range(5001, true, std::numeric_limits<int>::max(), false)};
        _hit_count_dist = std::make_unique<common::Distribution>(ranges);
    }

    {
        std::initializer_list<common::Distribution::Range> ranges{
                common::Distribution::Range(0, true, TIME_MIN, true),
                common::Distribution::Range(TIME_MIN, false, 5 * TIME_MIN, true),
                common::Distribution::Range(5 * TIME_MIN, false, 10 * TIME_MIN, true),
                common::Distribution::Range(10 * TIME_MIN, false, 15 * TIME_MIN, true),
                common::Distribution::Range(15 * TIME_MIN, false, 20 * TIME_MIN, true),
                common::Distribution::Range(20 * TIME_MIN, false, 30 * TIME_MIN, true),
                common::Distribution::Range(30 * TIME_MIN, false, 60 * TIME_MIN, true),
                common::Distribution::Range(60 * TIME_MIN, false, std::numeric_limits<int>::max(), false)};

        _eviction_age_secs_dist = std::make_unique<common::Distribution>(ranges);
    }
}

void EntryCache::do_evict_shards() {
    auto evicted_shard_list = _policy->evict();
    if (evicted_shard_list.empty()) {
        ARIES_DEBUG_LOG(TRACE) << "no need to do evict";
        return;
    }

    LOG(NOTICE) << "cache will evict " << evicted_shard_list.size() << " shards";
    for (const auto& record : evicted_shard_list) {
        _hit_count_dist->add(record.hits);
        _eviction_age_secs_dist->add((int64_t)record.since_access_us() / (1000 * 1000 * 60));

        uint64_t shard_size = record.shard_info.occurred_size;
        if (record.shard_info.pin_ttl_secs != 0) {
            ARIES_UNLIKELY_IF(_curr_pinned_size.fetch_sub(shard_size) < shard_size) { // overflow
                _curr_pinned_size = 0;
            }
        }

        // if shard is deleted by *remove* interface, delete from disk directly
        // if shard is deleted by storage drop, it will return error
        int res = _engine->del(record.shard_info.shard_id, record.shard_info);
        if (res == AIE_OK) {
            g_datanode_cache_entry_count_adder << -1;
        } else if (res == AIE_AGAIN) {
            bthread_usleep(1000);   //wait a moment, 1ms
        }
    }
}

void EntryCache::start_reclaim_force() {
    do_evict_shards();
}

void EntryCache::start_reclaim() {
    {
        folly::SharedMutex::WriteHolder w{&_engine_stat_mutex};
        _engine_stat = _engine->get_engine_stat();;
    }

    while (get_overflow_size() > 0) {
        do_evict_shards();
    }
}

//static
void EntryCache::handle_inactive_shards_on_timer(void* args) {
    // Register next period timer;
    auto *cache = static_cast<EntryCache*>(args);
    cache->add_handle_inactive_shards_timer();
    cache->handle_inactive_shards();
}

void EntryCache::add_handle_inactive_shards_timer() {
    bthread_timer_add(&_handle_inactive_shard_timer,
                      base::milliseconds_from_now(FLAGS_cache_check_inactive_interval_second * 1000),
                      &EntryCache::handle_inactive_shards_on_timer,
                      static_cast<void*>(this));
}

void EntryCache::handle_inactive_shards() {
    auto expired_shard_filter = [](const CacheItemRecord& record) {
        return record.since_access_us() > (uint64_t)FLAGS_cache_max_inactive_interval_second * 1000 * 1000;
    };
    auto del_list = _policy->visit(std::move(expired_shard_filter));

    for (auto& shard: del_list) {
        (void)remove(shard);  // mark delete this shard, and evict later
    }
}

//static
void EntryCache::update_monitor_stat_on_timer(void* args) {
    auto *cache = static_cast<EntryCache*>(args);
    cache->add_update_monitor_stat_timer();
    cache->update_monitor_stat();
}

void EntryCache::add_update_monitor_stat_timer() {
    bthread_timer_add(&_update_monitor_stat_timer, base::milliseconds_from_now(1000),
                      &EntryCache::update_monitor_stat_on_timer,
                      static_cast<void*>(this));
}

void EntryCache::update_monitor_stat() {
    {
        folly::SharedMutex::WriteHolder w{&_engine_stat_mutex};
        _engine_stat = _engine->get_engine_stat();;
    }

    uint64_t overflow_size = get_overflow_size();
    g_datanode_cache_engine_entry_count_recorder.set_value(_engine_stat.total_shard_count);
    g_datanode_cache_engine_capacity_size_mb_recorder.set_value(_engine_stat.total_capacity / common::MB);
    g_datanode_cache_engine_free_size_mb_recorder.set_value(_engine_stat.total_free_size / common::MB);
    g_datanode_cache_cache_overflow_size_mb_recorder.set_value(overflow_size / common::MB);

    {
        auto snapshot = _put_size_dist->get_snapshot();
        for (auto &kv : snapshot) {
            g_datanode_cache_size_distribution.put(to_string(kv.first), kv.second);
        }
    }

    {
        auto snapshot = _hit_count_dist->get_snapshot();
        for (auto &kv : snapshot) {
            g_datanode_cache_hits_distribution.put(to_string(kv.first), kv.second);
        }
    }

    {
        auto snapshot = _eviction_age_secs_dist->get_snapshot();
        for (auto &kv : snapshot) {
            g_datanode_cache_eviction_age_distribution.put(to_string(kv.first), kv.second);
        }
    }
}

uint64_t EntryCache::get_overflow_size() const {
    folly::SharedMutex::ReadHolder r{&_engine_stat_mutex};
    uint64_t reserve_free_size = (uint64_t) (_engine_stat.total_capacity * FLAGS_cache_cap_reserve_percent) / 100;
    return (_engine_stat.total_free_size >= reserve_free_size) ? 0 : (reserve_free_size - _engine_stat.total_free_size);
}

uint64_t EntryCache::get_available_size() const {
    folly::SharedMutex::ReadHolder r{&_engine_stat_mutex};
    uint64_t reserve_free_size = (uint64_t) (_engine_stat.total_capacity * FLAGS_cache_cap_reserve_percent) / 100;
    return (_engine_stat.total_free_size <= reserve_free_size) ? 0 : (_engine_stat.total_free_size - reserve_free_size);
}

int64_t EntryCache::get_shard_occurred_size(const ShardId& shard_id) const {
    const auto lr = _policy->lookup_index(shard_id);
    if (!lr.found()) {
        return -1;
    } else {
        return lr.record().shard_info.occurred_size;
    }
}

int EntryCache::prefetch(const ShardId& shard_id, FetchDiskFunc&& fetch_disk_func, CacheShardInfo* shard_info) {
    auto prefetch_func = [this, fetch_func = std::move(fetch_disk_func)](const ShardId& id, CacheShardInfo* shard_info) mutable {
        return fetch_and_add_to_cache(id, shard_info, std::move(fetch_func));
    };
    return _policy->prefetch(shard_id, std::move(prefetch_func), shard_info);
}

// fetch shard from disk and put into cache engine
int EntryCache::fetch_and_add_to_cache(const ShardId& shard_id,
                                       CacheShardInfo* shard_info,
                                       FetchDiskFunc&& fetch_func) {
    pb::ShardMeta meta;
    base::IOBuf data;
    int ret = fetch_func(shard_id, &meta, &data);
    if (ret == AIE_OK) {
        int ret = _engine->put(shard_id, meta, data, shard_info);
        if (ret != AIE_OK) {
            // failed, handle not exist error
            LOG(WARNING) << "put shard to cache engine failed, " << shard_id;
            return ret;
        } else {
            g_datanode_cache_entry_count_adder << 1;
        }
    } else {
        LOG(WARNING) << "get shard from disk failed, " << shard_id;
        return ret;
    }
    return AIE_OK;
}

int64_t EntryCache::get_conf_total_capacity(CacheDisksConf* conf) {
    int64_t total_capacity = 0;
    for (auto& disk_conf : conf->disks) {
        total_capacity += disk_conf.capacity_mb() * common::MB;
    }
    return total_capacity;
}

}
}
