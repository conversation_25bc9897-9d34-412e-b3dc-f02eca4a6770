#include <base/fast_rand.h>
#include "baidu/inf/aries/datanode/cache/cache_io_executor.h"
#include "baidu/inf/aries/datanode/disk_agent.h"
#include "baidu/inf/aries/datanode/vlet.h"
#include "baidu/inf/aries/datanode/cache/cache_manager.h"
#include "baidu/inf/aries/datanode/io_context.h"
#include "baidu/inf/aries/datanode/io_scheduler.h"
#include "baidu/inf/aries/datanode/datanode.h"

namespace aries {
namespace datanode {

CacheIOExecutor::CacheIOExecutor(CacheManager* cache_manager) : _thread_pool(nullptr), _cache_mgr(cache_manager) {
}

int CacheIOExecutor::start(std::shared_ptr<folly::CPUThreadPoolExecutor> thread_pool) {
    _thread_pool = thread_pool;
    _priority_level = thread_pool->getNumPriorities();
    _highest_level = 0;
    _lowest_level = _priority_level - 1;
    return AIE_OK;
}

void CacheIOExecutor::stop() {
    _thread_pool.reset();
}

int CacheIOExecutor::dispatch_get_task(GetFromCacheContext* ctx) {
    auto get_task = [this, ctx]() {
        int ret = this->do_get(ctx);
        if (ret != AIE_OK) {
            LOG(WARNING) << "cache get failed with error:" << ret;
        }
    };
    return add_task(std::move(get_task), (int8_t) ctx->priority);
}

int CacheIOExecutor::dispatch_add_to_cache_task(AddRecordToCacheContext* ctx) {
    auto* status = ctx->mutable_status();
    if (!FLAGS_enable_cache || _cache_mgr == nullptr || !_cache_mgr->enabled()) {
        status->set_code(AIE_FAIL);
        status->set_msg("cache is not enabled or started");
        return AIE_FAIL;
    }

    if (_cache_mgr->_entry_cache->exist(ctx->shard_id())) {
        status->set_code(AIE_FAIL);
        status->set_msg("record is already in cache");
        return AIE_FAIL;
    }

    auto add_to_cache_task = [this, ctx] {
        std::unique_ptr<AddRecordToCacheContext> context_holder(ctx);
        aries::pb::ShardMeta meta;
        base::IOBuf data;
        int ret = get_data_from_disk(ctx->shard_id(), &meta, &data);
        if (ret != AIE_OK) {
            ctx->mutable_status()->set_code(ret);
            ctx->mutable_status()->set_msg("get data from disk failed");
            LOG(WARNING) << "add_to_cache_task failed due to get data from disk failed, shard_id:" << ctx->shard_id();
            return;
        }

        ret = _cache_mgr->_entry_cache->insert(
                    ctx->shard_id(), meta, data, ctx->pin_ttl_secs());
        if (ret == AIE_OK) {
            _cache_mgr->_access_frequency_mgr->reset_frequency(ctx->shard_id());
        } else {
            LOG(WARNING) << "add_to_cache_task failed due to insert into cache failed";
        }

        ctx->mutable_status()->set_code(ret);
    };

    return add_task(std::move(add_to_cache_task), aries::DEGRADE_HIGH);
}

int CacheIOExecutor::dispatch_remove_from_cache_task(RemoveRecordFromCacheContext* ctx) {
    auto* status = ctx->mutable_status();
    if (!FLAGS_enable_cache || _cache_mgr == nullptr || !_cache_mgr->enabled()) {
        status->set_code(AIE_FAIL);
        status->set_msg("cache is not enabled or started");
        LOG(WARNING) << "dispatch_remove_from_cache_task failed due to cache is not enabled, shard_id:" << ctx->shard_id();
        return AIE_FAIL;
    }

    if (!_cache_mgr->_entry_cache->is_tracked(ctx->shard_id())) {
        status->set_code(AIE_FAIL);
        status->set_msg("record is not tracked in cache");
        LOG(WARNING) << "dispatch_remove_from_cache_task failed due to record is not tracked in cache, shard_id:" << ctx->shard_id();
        return AIE_FAIL;
    }

    auto remove_from_cache_task = [this, ctx] {
        int ret = _cache_mgr->_entry_cache->remove(ctx->shard_id(), true);
        ctx->mutable_status()->set_code(ret);
        delete ctx;
    };

    return add_task(std::move(remove_from_cache_task), aries::DEGRADE_HIGH);
}

int CacheIOExecutor::dispatch_admit_task(const ShardId& shard_id,
                                         const aries::pb::ShardMeta& meta,
                                         const base::IOBuf& data,
                                         uint32_t cache_pin_ttl_secs) {
    if (!FLAGS_enable_cache || _cache_mgr == nullptr || !_cache_mgr->enabled()) {
        return AIE_OK;
    }

    bool is_shard_tracked = _cache_mgr->_entry_cache->is_tracked(shard_id);
    if (!is_shard_tracked) {
        if (cache_pin_ttl_secs == 0) {
            _cache_mgr->_access_frequency_mgr->update_frequency(shard_id);
            if (!_cache_mgr->_admission_policy->accept(shard_id)) {
                return AIE_OK;
            }
        } else {
            cache_pin_ttl_secs = std::min(cache_pin_ttl_secs, (uint32_t)FLAGS_cache_max_inactive_interval_second);
            LOG(TRACE) << "add shard into cache, shard_id:" << shard_id << " pin_ttl_s:" << cache_pin_ttl_secs << " data_size:" << data.size();
        }

        auto insert_task = [this, shard_id, meta, data, cache_pin_ttl_secs]() {
            int res = _cache_mgr->_entry_cache->insert(shard_id, meta, data, cache_pin_ttl_secs);
            if (res == AIE_OK && cache_pin_ttl_secs == 0) {
                _cache_mgr->_access_frequency_mgr->reset_frequency(shard_id);
            }
        };
        int ret = add_task(std::move(insert_task), aries::DEGRADE_HIGH);
        if (ret != AIE_OK) {
            LOG(WARNING) << "add task into cache io executor failed, shard_id:" << shard_id << "error:" << ret ;
            return ret;
        }
    }
    return AIE_OK;
}

int CacheIOExecutor::add_task(folly::Func&& func, int8_t aries_priority) {
    auto priority = translate_priority(aries_priority);
    try {
        _thread_pool->addWithPriority(std::move(func), priority);
    } catch (folly::QueueFullException& e) {
        LOG(WARNING) << "cache thread pool queue is full, priority:" << std::to_string(priority)
                     << ", " << typeid(e).name() << " exception: " << e.what();
        return AIE_FAIL;
    } catch (const std::exception& e) {
        LOG(WARNING) << "cache thread pool addWithPriority threw std::exception "
                     << typeid(e).name() << " exception: " << e.what();
        return AIE_FAIL;
    } catch (...) {
        LOG(WARNING) << "cache thread pool func threw unhandled non-exception object";
        return AIE_FAIL;
    }
    return AIE_OK;
}

int CacheIOExecutor::do_get(GetFromCacheContext* ctx) {
    std::unique_ptr<GetFromCacheContext> hold_ctx(ctx);
    auto request = ctx->request;
    auto response = ctx->response;
    auto status = response->mutable_status();
    ShardId shard_id(request->volume_id(), request->vbid(), request->shard_index());

    ctx->add_point("cache_queue=");
    int ret = AIE_OK;
    if (request->need_data()) {
        if (request->len() != 0 && request->fast_range_get()) {
            ret = range_get_from_cache(shard_id, request->offset(), request->len(),
                                           response->mutable_shard_meta(),
                                           &ctx->cntl->response_attachment());
        } else {
            ret = get_from_cache(shard_id, response->mutable_shard_meta(), &ctx->cntl->response_attachment());
            if (ret == AIE_OK && response->shard_meta().compress_type() == 0 && request->len() != 0) {
                base::IOBuf buf;
                ctx->cntl->response_attachment().append_to(&buf, request->len(), request->offset());
                ctx->cntl->response_attachment().clear();
                ctx->cntl->response_attachment().append(buf);
                uint32_t shard_crc = base::crc32c::Value(buf.to_string().data(), buf.size());
                response->mutable_shard_meta()->set_shard_crc(shard_crc);
            }
        }
    } else if (request->need_meta()) {
        ret = get_from_cache(shard_id, response->mutable_shard_meta(), nullptr);
    } else {
        ret = get_from_cache(shard_id, nullptr, nullptr);
        if (ret == AIE_OK) {
            auto* meta = response->mutable_shard_meta();
            meta->set_blob_len(0);
            meta->set_blob_crc(0);
            meta->set_shard_len(0);
        }
    }

    ctx->add_point("cache_get=");
    if (ret != AIE_OK) {
        // if get failed from cache, transfer request to secondary storage,
        // don't admit reading blob from secondary storage to cache if return AIE_INPROGRESS
        g_datanode_cache_statistics.on_cache_miss(static_cast<aries::Qos>(ctx->priority));
        bool skip_admit_to_cache = (ret == AIE_INPROGRESS);
        transfer_request(ctx, skip_admit_to_cache);
        return AIE_OK;
    }

    g_datanode_cache_statistics.on_cache_hit(static_cast<aries::Qos>(ctx->priority));
    status->set_code(AIE_OK);

    return AIE_OK;
}

int CacheIOExecutor::get_from_cache(const ShardId& shard_id,
                                    pb::ShardMeta* meta,
                                    base::IOBuf* data) {
    // is only used in ARC, reload data from disk when entry is in ghost list
    auto prefetch = [this](const ShardId& shard_id, pb::ShardMeta* meta, base::IOBuf* data) {
        return this->get_data_from_disk(shard_id, meta, data);
    };
    LookupOptions options(std::move(prefetch));
    int res = _cache_mgr->_entry_cache->lookup(shard_id, std::move(options), meta, data);
    if (res != AIE_OK && res != AIE_INPROGRESS && res != AIE_NOT_EXIST) {
        LOG(WARNING) << "get data from cache failed with shard_id:" << shard_id << " error:" << res;
    }
    return res;
}

int CacheIOExecutor::range_get_from_cache(const ShardId& shard_id,
                                          const int64_t offset,
                                          const int64_t len,
                                          aries::pb::ShardMeta* meta,
                                          base::IOBuf* data) {
    base::IOBuf buf;
    int res = get_from_cache(shard_id, meta, &buf);
    if (res != AIE_OK) {
        LOG(WARNING) << "range get from cache failed, shard_id:" << shard_id << " error:" << res;
        return res;
    } else {
        buf.append_to(data, len, offset);
        uint32_t shard_crc = build_crc(*data);
        meta->set_shard_crc(shard_crc);
        meta->set_shard_len(0);
        meta->set_blob_crc(0);
        meta->set_blob_len(0);
    }
    return AIE_OK;
}

void CacheIOExecutor::transfer_request(GetFromCacheContext* cache_ctx, bool skip_admit) {
    GetContext* ctx = new GetContext();
    cache_ctx->response->Clear();
    ctx->key = ::aries::make_bid(cache_ctx->request->volume_id(), cache_ctx->request->vbid());
    ctx->vlet_ptr = std::move(cache_ctx->vlet_ptr);
    cache_ctx->vlet_ptr = nullptr;
    ctx->request = cache_ctx->request;
    ctx->response = cache_ctx->response;
    ctx->status = ctx->response->mutable_status();
    ctx->cntl = cache_ctx->cntl;
    ctx->log_id = cache_ctx->log_id;
    ctx->vid = ctx->request->volume_id();
    ctx->vbid = ctx->request->vbid();
    ctx->done_guard.reset(cache_ctx->done_guard.release());// transfer done to get context
    ctx->en_queue_time = base::gettimeofday_ms();
    ctx->en_queue_time_us = base::gettimeofday_us();
    ctx->timeout_ms = cache_ctx->timeout_ms;
    ctx->priority = cache_ctx->priority;
    ctx->skip_admit_to_cache = skip_admit;
    ::memcpy(&ctx->event_trace, &cache_ctx->event_trace, sizeof(ctx->event_trace)); // transfer the event traces too

    cache_ctx->status = nullptr;  // must reset status because will access when cache_ctx destruct

    ctx->action = GET_BLOB_ACTION;
    base::get_leaky_singleton<IOScheduler>()->dispatch_task(ctx);
}

// NOTE: this function is only used in ARC, reload data from disk when entry is in ghost list
int CacheIOExecutor::get_data_from_disk(const ShardId& shard_id,
                                        pb::ShardMeta* meta,
                                        base::IOBuf* data) {
    VletPtr vlet_ptr = g_datanode->vlet_manager()->find(shard_id.vid());
    if (!vlet_ptr || vlet_ptr->shard_index() != (uint32_t)shard_id.shard_index()) {
        LOG(WARNING) << "target vlet not found, shard_id:" << shard_id;
        return AIE_NOT_EXIST;
    }

    GetContext* ctx = new GetContext();
    ::aries::pb::ShardGetRequest request;
    ::aries::pb::ShardGetResponse response;
    baidu::rpc::Controller cntl;
    std::shared_ptr<common::SyncPoint> sync_point(new common::SyncPoint(1));

    request.set_volume_id(shard_id.vid());
    request.set_vbid(shard_id.vbid());
    request.set_shard_index(shard_id.shard_index());
    request.set_need_data(true);
    request.set_need_meta(true);
    request.set_timeout_ms(FLAGS_call_timeout_ms);
    request.set_priority(aries::DEGRADE_HIGH);

    ctx->key = ::aries::make_bid(shard_id.vid(), shard_id.vbid());
    ctx->vlet_ptr = vlet_ptr;
    ctx->request = &request;
    ctx->response = &response;
    ctx->status = nullptr;  // don't print response info
    ctx->cntl = &cntl;
    ctx->vid = shard_id.vid();
    ctx->vbid = shard_id.vbid();
    ctx->log_id = base::fast_rand();
    ctx->en_queue_time = base::gettimeofday_ms();
    ctx->en_queue_time_us = ctx->en_queue_time;
    ctx->timeout_ms = FLAGS_call_timeout_ms;
    ctx->priority = aries::DEGRADE_HIGH;
    ctx->skip_admit_to_cache = true;
    ctx->sync_point = sync_point;

    ctx->action = GET_BLOB_ACTION;
    base::get_leaky_singleton<IOScheduler>()->dispatch_task(ctx);
    sync_point->wait();

    if (response.status().code() == AIE_OK) {
        meta->CopyFrom(response.shard_meta());
        data->append(cntl.response_attachment());
    }

    return response.status().code();
}

//translate aries priority to  folly priority,
// for example,translate  aries (12, 11 ... 1, 0) to folly(-6, -5 ... 5, 6)
int8_t CacheIOExecutor::translate_priority(int8_t const aries_priority) const {
    auto src_priority = (aries_priority < _highest_level ? _highest_level :
                         (aries_priority > _lowest_level ? _lowest_level : aries_priority));
    auto hi = (_priority_level + 1) / 2 - 1;
    return hi - src_priority;
}

}

}
