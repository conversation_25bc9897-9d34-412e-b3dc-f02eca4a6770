#pragma once

#include <vector>
#include <optional>
#include "baidu/inf/aries/datanode/cache/cache_define.h"
#include "baidu/inf/aries/datanode/cache/storage/cache_storage_interface.h"

namespace aries {
namespace datanode {

struct CacheDiskStat {
    CacheDiskStat() : disk_id(0), capacity(0), free_size(0), shard_count(0) {}

    uint32_t disk_id{0};
    uint64_t capacity{0};
    uint64_t free_size{0};
    uint64_t shard_count{0};
};

struct CacheEngineStat {
    CacheEngineStat() : total_free_size(0), total_capacity(0), total_shard_count(0) {}

    uint64_t total_free_size{0};
    uint64_t total_capacity{0};
    uint64_t total_shard_count{0};

    std::vector<CacheDiskStat> disk_stats;
};

// Abstract base class of engine.
class CacheEngineInterface {
public:
    explicit CacheEngineInterface() = default;

    virtual ~CacheEngineInterface() = default;

    virtual int put(const ShardId& shard_id,
                    const pb::ShardMeta& meta,
                    const base::IOBuf& data,
                    CacheShardInfo* shard_info) = 0;

    virtual int get(const ShardId& shard_id,
                    const CacheShardInfo& shard_info,
                    pb::ShardMeta* meta,
                    base::IOBuf* value) = 0;

    virtual int del(const ShardId& shard_id, const CacheShardInfo& shard_info) = 0;

    virtual std::optional<CacheStorageStat> get_storage_stat(uint64_t storage_id) const = 0;

    virtual int drop_storage(uint64_t storage_id) = 0;

    virtual void list_storage(std::vector<CacheStorageStat>* stats) const = 0;

    virtual CacheEngineStat get_engine_stat() const = 0;

    virtual CacheEngineType get_engine_type() const = 0;

    virtual int add_disk(uint32_t disk_id, const std::string& cache_disk_path,
                         uint64_t disk_capacity, bool is_tmpfs) = 0;

    virtual int drop_disk(uint32_t disk_id)  = 0;
};

}
}
