#pragma once

#include <cstring>
#include <vector>
#include <map>
#include "baidu/inf/aries-api/common/bvar_define.h"
#include "baidu/inf/aries-api/common/proto/datanode.pb.h"
#include "baidu/inf/aries/common/gate.h"
#include "baidu/inf/aries/datanode/flags.h"
#include "baidu/inf/aries/datanode/disk_agent.h"
#include "baidu/inf/aries/datanode/storage/store_base.h"
#include "baidu/inf/aries/datanode/storage/random/linked_store.h"
#include "baidu/inf/aries/datanode/cache/storage/cache_linked_storage_indexer.h"
#include "baidu/inf/aries/datanode/cache/storage/cache_storage_interface.h"

namespace aries {
namespace datanode {

class CacheLinkedStorage : public LinkedStore, public CacheStorageInterface {
public:
    struct RecordContainerHoleInfo {
        RecordContainerHoleInfo() : head_hole_blob_id(), tail_hole_blob_id(){}
        // remove opt
        BlobId head_hole_blob_id{0, 0}; // pop hole
        BlobId tail_hole_blob_id{0, 0}; // push hole
    };

    using RecordInfo = std::pair<BlobId, CacheRecordLocationExtraInfo>;

    CacheLinkedStorage()
            : LinkedStore(StoreOptions()),
              _storage_capacity(0),
              _storage_indexer(nullptr),
              _storage_id(0) {};

    ~CacheLinkedStorage();

    DISABLE_COPY_AND_MOVE(CacheLinkedStorage);

    Status initialize(const CacheStorageOptions& option,
                      std::shared_ptr<CacheMemDB> db) override;

    Status put(const BlobId& blob_id,
               const pb::ShardMeta& meta,
               const base::IOBuf& data,
               uint64_t* record_bytes) override;

    Status get(const BlobId& blob_id,
               aries::pb::ShardMeta* meta,
               base::IOBuf* data) override;

    // Always finally remove;
    Status remove(const BlobId& blob_id) override;

    int64_t get_storage_id() const override {
        return _storage_id;
    }

    uint32_t get_disk_id() const override {
        return _disk_id;
    }

    Status drop() override;

    CacheStorageStat get_storage_stat() const override;

    Status destroy();

private:
    void init_store_meta();

    Status move_tail_record_to_fill(const BlobId& blob_id,
                                    const RecordLocation& location);

    Status mark_record_as_hole(const BlobId& blob_id,
                               const RecordLocation& location,
                               const CacheRecordLocationExtraInfo& extra_info);

    Status put_record_to_hole(const BlobId& blob_id, uint32_t record_type, char* buf, uint32_t record_data_offset);

    Status put_record_to_tail(const BlobId& blob_id, uint32_t record_type, char* buf, uint32_t record_data_offset);

    Status update_hole_info(CacheLinkedStoreIndexer::WriteBatch* batch,
                            uint32_t record_type,
                            BlobId hole_blob_id,
                            const CacheRecordLocationExtraInfo& hole_info);

    Status move_linked_record(const RecordLocation& from, const RecordLocation& to,
                              uint64_t* from_vid, uint64_t* from_vbid,
                              CacheRecordLocationExtraInfo* from_extra_info);

    Status read_linked_record(const RecordLocation& location,
                              bool validate_flag,
                              char* buffer,
                              const BlobId& blob_id = NULL_BLOB_ID /* for validation */);

    Status write_linked_record(const RecordLocation &location, char *buf);

    Status validate_record(uint32_t record_page_num, const char *buf, uint32_t page_size, const BlobId& blob_id = NULL_BLOB_ID /* for validation */);

    Status serialize_record(const BlobId& blob_id, const aries::pb::ShardMeta& meta, const base::IOBuf& data,
                            char* buf, uint32_t record_bytes, uint32_t* record_data_offset);

    Status deserialize_record(char* buffer,
                              uint64_t* vid, uint64_t* vbid,
                              aries::pb::ShardMeta* meta,
                              base::IOBuf* data,
                              uint32_t* record_data_offset);

    Status get_extra_info_by_blob_id(uint64_t vid, uint64_t vbid,
                                     const RecordLocation& expected_loc,
                                     uint32_t record_data_offset,
                                     CacheRecordLocationExtraInfo* extra_info);

    Status fill_container_holes(FillHoleType fill_hole_type, uint32_t record_type);

    Status alloc_location_for_put(uint32_t record_type,
                                  RecordLocation* new_location);

    Status get_record_info(const RecordLocation& loc,
                           char* buf,
                           uint64_t* vid,
                           uint64_t* vbid,
                           CacheRecordLocationExtraInfo* extra_info);

    Status get_hole_record_info_by_location(const RecordLocation& loc,
                                            std::unordered_map<uint64_t, RecordInfo>& hole_loc_info_map,
                                            uint64_t* vid,
                                            uint64_t* vbid,
                                            CacheRecordLocationExtraInfo* extra_info);

    static bool is_null_blob_id(const BlobId& blob_id);

private:
    inline static const BlobId NULL_BLOB_ID = BlobId(0, 0);
    uint64_t _storage_capacity{0};
    CacheLinkedStoreIndexer* _storage_indexer{nullptr};
    int64_t _storage_id{0};
    uint32_t _disk_id{0};
    std::atomic<int64_t> _record_count{0};  // exclude hole records
    common::Gate _gate;
    RecordContainerHoleInfo* _record_container_hole_info{nullptr};  // container hole info list
};

}
}
