#include "baidu/inf/aries/datanode/cache/storage/cache_mem_db.h"
#include "baidu/inf/aries/datanode/cache/storage/cache_linked_storage_indexer.h"

namespace aries {
namespace datanode {

void CacheMemDB::WriteBatch::put(const StorageItemKey& key, StorageItemRecordPtr value) {
    WriteOp op;
    op.type = WriteType::Put;
    op.key = key;
    op.value = std::move(value);
    op_array.emplace_back(std::move(op));
}

void CacheMemDB::WriteBatch::remove(const StorageItemKey& key) {
    WriteOp op;
    op.type = WriteType::Remove;
    op.key = key;
    op_array.emplace_back(op);
}

int CacheMemDB::put(const StorageItemKey& key, StorageItemRecordPtr value) {
    folly::SharedMutex::WriteHolder w{&_map_lock};
    _map.insert_or_assign(key, std::move(value));
    return AIE_OK;
}

CacheMemDB::LookupResult CacheMemDB::get(const StorageItemKey& key) {
    LookupResult lr;
    folly::SharedMutex::ReadHolder r{&_map_lock};
    auto it = _map.find(key);
    if (it == _map.end()) {
        lr._found = false;
    } else {
        lr._found = true;
        lr._record = it->second;
    }
    return lr;
}

CacheMemDB::LookupResult CacheMemDB::remove(const StorageItemKey& key) {
    LookupResult lr;
    folly::SharedMutex::WriteHolder w{&_map_lock};
    auto it = _map.find(key);
    if (it != _map.end()) {
        lr._found = true;
        lr._record = it->second;
        _map.erase(it);
    }
    return lr;
}

void CacheMemDB::travel(std::function<void(StorageItemKey, StorageItemRecordPtr)>&& op_func) {
    folly::SharedMutex::ReadHolder r{&_map_lock};
    for (auto& kv : _map) {
        op_func(kv.first, kv.second);
    }
}

int CacheMemDB::write_batch(const WriteBatch& batch) {
    folly::SharedMutex::WriteHolder w{&_map_lock};
    for (auto& op: batch.op_array) {
        if (op.type == WriteType::Put) {
            _map.insert_or_assign(op.key, op.value);
        } else if (op.type == WriteType::Remove) {
            auto it = _map.find(op.key);
            if (it != _map.end()) {
                _map.erase(it);
            }
        }
    }

    return AIE_OK;
}

void CacheMemDB::drop(const uint64_t& storage_id) {
    std::vector<StorageItemKey> key_list;
    folly::SharedMutex::WriteHolder w{&_map_lock};
    for (auto &kv : _map) {
        if (kv.first.storage_id == storage_id) {
            key_list.emplace_back(kv.first);
        }
    }
    for (auto &key : key_list) {
        _map.erase(key);
    }
}

void CacheMemDB::clear() {
    folly::SharedMutex::WriteHolder w{&_map_lock};
    _map.clear();
}

}
}