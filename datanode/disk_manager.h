// Copyright (c) 2016 Baidu.com, Inc. All Rights Reserved
// Author ch<PERSON><PERSON><PERSON><PERSON>(chen<PERSON><PERSON><EMAIL>)
// Date: Sat Oct  8 17:18:57 CST 2016

#ifndef BAIDU_INF_ARIES_DATANODE_DISK_MANAGER_H
#define BAIDU_INF_ARIES_DATANODE_DISK_MANAGER_H

#include <map>
#include <string>
#include "baidu/inf/aries/datanode/vlet_manager.h"
#include "baidu/inf/aries/datanode/master_caller.h"
#include "baidu/inf/aries/common/status.h"
#include "baidu/inf/aries-api/common/proto/master.pb.h"
#include "baidu/inf/aries-api/common/proto/datanode.pb.h"
#include "baidu/inf/aries-api/common/lock.h"
#include "baidu/inf/aries-api/common/types.h"
#include "baidu/inf/aries/datanode/cache/cache_manager.h"
#include "base/fd_guard.h"

namespace aries {
namespace datanode {

extern bvar::Adder<int64_t> g_datanode_err_disk_num_adder;
class DiskAgent;
class CacheManager;

typedef DiskAgent* DiskAgentPtr;

class DiskManager {
public:
    explicit DiskManager(VletManager* vlet_mgr);

    int init(int host_id);

    void load_disk_conf();

    int disk_number() const {
        return _disk_agent_map.size();
    }

    int used_disk_number();

    bool dump_disk_conf();

    DiskAgentPtr find(int disk_id);

    std::string to_string();

    void start();

    const std::string&  disk_conf_name() const {
        return _disk_conf_name;
    }

    const std::map<int, DiskAgentPtr>& disk_agent_map() const {
        return _disk_agent_map;
    }

    CacheManager* cache_manager() { return _cache_mgr.get(); }

private:
    bool update_disk_conf(const std::string& json_data);

public:
    // immutable data struct after start
    // not use mutex to read map
    VletManager* _vlet_manager;
    std::string _disk_conf_name;
    std::map<int, DiskAgentPtr> _disk_agent_map;
    std::unique_ptr<CacheManager> _cache_mgr{nullptr};
};

} // end namespace of datanode
} // end namespace of aries

#endif //#ifndef BAIDU_INF_ARIES_DATANODE_DISK_MANAGER_H

/* vim: set ts=4 sw=4 sts=4 tw=100 */
