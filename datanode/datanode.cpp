// Copyright (c) 2016 Baidu.com, Inc. All Rights Reserved
// Author ch<PERSON><PERSON><PERSON><PERSON>(chen<PERSON><PERSON><PERSON>@baidu.com)
// Date: Tue Oct 11 17:11:32 CST 2016

#include <algorithm>
#include "baidu/inf/aries/datanode/datanode.h"
#include "baidu/inf/aries/datanode/flags.h"
#include "baidu/inf/aries/datanode/service/data_service_impl.h"
#include "baidu/inf/aries/datanode/service/control_service_impl.h"
#include "baidu/inf/aries/datanode/service/monitor_service_impl.h"
#include "baidu/inf/aries-api/common/types.h"
#include "baidu/inf/aries-api/common/bvar_service.h"
#include "baidu/inf/aries-api/common/config.h"
#include "baidu/inf/aries-api/common/master_tracker.h"
#include "baidu/inf/aries/datanode/util/kylin.h"
#include "baidu/inf/aries/datanode/io_scheduler.h"

#ifndef __ARIES_VERSION_ID__
#define __ARIES_VERSION_ID__ "unknown"
#endif

DECLARE_string(flagfile);
using aries::common::ConfigReloader;
using aries::common::g_config_reloader;

int g_main_thread_id = 0;

namespace aries {
namespace datanode {

Datanode* g_datanode = new Datanode;

Datanode::Datanode() {
    _vlet_manager = new VletManager;
    _disk_manager = new DiskManager(_vlet_manager);
    _gc = new Gc;
    _cleaner = new Cleaner;
    _checker = new OverdueCallChecker;
    _data_checker = new DataChecker();
    _rewrite_manager = new RewriteManager();
    _bvar_monitor = std::make_shared<common::BvarMonitor>();
    _flow_limiter = std::make_shared<FlowLimiter>();
}

Datanode::~Datanode() {
    delete _rewrite_manager;
    delete _data_checker;
    delete _vlet_manager;
    delete _disk_manager;
    delete _gc;
    delete _cleaner;
    delete _checker;
}

void Datanode::start() {
    // init disk_manager
    init();

    if (!common::is_master_tracker_started()) {
        base::EndPoint local_addr = common::get_local_addr();
        assert(0 == common::start_master_tracker(local_addr, FLAGS_token));
    }

    _disk_manager->start();
    _flow_limiter->start();

    //start IOScheduler timer before baidu-rpc server;
    base::get_leaky_singleton<IOScheduler>()->add_reset_timer();

    uint32_t port = common::FLAGS_port;
    // start control_service or die
    start_control_service(port);

    // start data_service or die
    start_data_service(port + 1);

    // start privilege data_service or die
    start_privilege_data_service(port + 3);

    start_monitor_service(port + 2);

    _gc->start();
    _heartbeat.start();
    _cleaner->start();
    _checker->start();
    _data_checker->start();
    _bvar_monitor->start();

    if (FLAGS_enable_rewrite) {
        _rewrite_manager->start();
    }
    if (FLAGS_full_self_check_on_startup) {
        g_config_reloader->update_config_file("full_self_check_on_startup", "false");
    }

    LOG(WARNING) << "datanode startup, version:" << __ARIES_VERSION_ID__;
}

void Datanode::join() {
    _control_server.Join();
    _data_server.Join();
    _privilege_data_server.Join();
    _monitor_server.Join();

}

void Datanode::stop() {
    _heartbeat.stop();
    _control_server.Stop(0);
    _data_server.Stop(0);
    _privilege_data_server.Stop(0);
    _monitor_server.Stop(0);
    if (_bvar_monitor != nullptr) {
        _bvar_monitor->stop();
    }

    if (FLAGS_enable_rewrite) {
        _rewrite_manager->stop();
    }

    //common::stop_master_tracker();
}

int Datanode::reload_configure() {
    data_checker()->update_conf();
    common::BvarMonitorConf conf;
    common::init_bvar_monitor_conf(&conf, nullptr);
    _bvar_monitor->reload(conf);
    if (g_list_index_token_pool) {
        g_list_index_token_pool->reset(FLAGS_max_list_index_num_per_second,
            FLAGS_max_list_index_num_per_second);
    }
    const std::map<int, DiskAgentPtr>& disk_agent_map = _disk_manager->disk_agent_map();
    for (auto& kv : disk_agent_map) {
        kv.second->reload_configure();
    }
    return 0;
}

void Datanode::bury() {
    LOG(NOTICE) << "datanode begin bury myself";

    stop();
    LOG(NOTICE) << "datanode stop rpc server succeeded";

    const std::map<int, DiskAgentPtr>& disk_agent_map = _disk_manager->disk_agent_map();
    int size = disk_agent_map.size();

    common::SyncPoint sync_point(size);

    std::vector<SuicideContext> ctx_list;
    ctx_list.resize(size);

    // fix bug: clean leak file task and on_suicide task(earse vlet list from vlet_manager) execute concurrently
    // cause earsed vlets is delete by clean leak file task directly
    _cleaner->stop();

    int i = 0;
    for (auto& kv : disk_agent_map) {
        ctx_list[i].sync_point = &sync_point;
        QueueExec(SUICIDE_ACTION, kv.second, &(ctx_list[i]));
        ++i;
    }

    sync_point.wait_ms(FLAGS_disk_hung_second * 1000 + 1000);
    StopKylin();
    LOG(NOTICE) << "datanode bury myself succeeded";
}

//private method
void Datanode::init() {
    uint32_t host_id = 0;

    g_config_reloader = new ConfigReloader(FLAGS_flagfile);
    g_config_reloader->set_token(FLAGS_token);

    _disk_manager->load_disk_conf();

    host_id = _flow_limiter->init(host_id);

    host_id = _disk_manager->init(host_id);

    // new Gc CAsyncClient
    host_id = _gc->init(host_id, _disk_manager->disk_agent_map());

    // new HeartBeat CAsyncClient
    host_id = _heartbeat.init(host_id, _disk_manager->disk_agent_map());

    host_id = _cleaner->init(host_id, _disk_manager->disk_agent_map());

    host_id = _checker->init(host_id, _disk_manager->disk_agent_map());

    uint32_t thread_num = host_id;

    //InitKylin
    InitKylin(thread_num); // 0.2 seconds precision

    // init bvar monitor
    common::BvarMonitorConf conf;
    common::init_bvar_monitor_conf(&conf, nullptr);
    _bvar_monitor->init(conf);
    LOG(NOTICE) << "init datanode succeeded, thread_num:" << thread_num
        << " time_precision_ms:" << 200;
}

void Datanode::start_control_service(uint32_t port) {
    // Start Control Server
    baidu::rpc::ServerOptions options;
    options.idle_timeout_sec = common::FLAGS_idle_timeout_s;
    std::string version = google::VersionString();
    for (auto& ch : version) {
        ARIES_UNLIKELY_IF (ch == '\n') {
            ch = ' ';

        }
    }
    if (g_config_reloader == nullptr) {
        g_config_reloader = new ConfigReloader(FLAGS_flagfile);
        g_config_reloader->set_token(FLAGS_token);
    }

    _control_server.set_version(version);

    auto control_impl = new DataNodeControlServiceImpl();
    ARIES_UNLIKELY_IF (_control_server.AddService(control_impl, baidu::rpc::SERVER_OWNS_SERVICE)) {
        GO_TO_DIE("add control server failed");
    }

    if (_control_server.AddService(g_config_reloader, baidu::rpc::SERVER_OWNS_SERVICE)) {
        GO_TO_DIE("add config reloader server failed");
    }

    ARIES_UNLIKELY_IF (_control_server.Start(port, &options)) {
        GO_TO_DIE("start cntrol server failed");
    }
}

void Datanode::start_data_service(uint32_t port) {
    // Start list index token pool
    g_list_index_token_pool = std::make_shared<common::TokenPool>(FLAGS_max_list_index_num_per_second,
        FLAGS_max_list_index_num_per_second);
    g_list_index_token_pool->start();

    // Start Data Server
    baidu::rpc::ServerOptions options;
    options.idle_timeout_sec = common::FLAGS_idle_timeout_s;
    //options.use_rdma = baidu::rpc::FLAGS_enable_rdma;
    std::string version = google::VersionString();
    for (auto& ch : version) {
        ARIES_UNLIKELY_IF (ch == '\n') {
            ch = ' ';
        }
    }
    _data_server.set_version(version);

    auto data_impl = new DataNodeDataServiceImpl();
    ARIES_UNLIKELY_IF (_data_server.AddService(data_impl, baidu::rpc::SERVER_OWNS_SERVICE)) {
        GO_TO_DIE("add data server failed");
    }

    ARIES_UNLIKELY_IF (_data_server.Start(port, &options)) {
        GO_TO_DIE("start data server failed");
    }
}

void Datanode::start_privilege_data_service(uint32_t port) {
    // Start Data Server
    baidu::rpc::ServerOptions options;
    options.idle_timeout_sec = common::FLAGS_idle_timeout_s;
    //options.use_rdma = baidu::rpc::FLAGS_enable_rdma;
    std::string version = google::VersionString();
    for (auto& ch : version) {
        ARIES_UNLIKELY_IF (ch == '\n') {
            ch = ' ';
        }
    }
    _privilege_data_server.set_version(version);

    auto data_impl = new DataNodeDataServiceImpl();
    ARIES_UNLIKELY_IF (_privilege_data_server.AddService(data_impl, baidu::rpc::SERVER_OWNS_SERVICE)) {
        GO_TO_DIE("add privilege data server failed");
    }

    ARIES_UNLIKELY_IF (_privilege_data_server.Start(port, &options)) {
        GO_TO_DIE("start privilege data server failed");
    }
}

void Datanode::start_monitor_service(uint32_t port) {
    // Start Control Server
    baidu::rpc::ServerOptions options;
    options.idle_timeout_sec = common::FLAGS_idle_timeout_s;
    std::string version = google::VersionString();
    for (auto& ch : version) {
        ARIES_UNLIKELY_IF (ch == '\n') {
            ch = ' ';
        }
    }
    _monitor_server.set_version(version);

    auto monitor_impl = new DataNodeMonitorServiceImpl();
    ARIES_UNLIKELY_IF (_monitor_server.AddService(monitor_impl, baidu::rpc::SERVER_OWNS_SERVICE,
                "/list_disk/* => list_disk,"
                "/list_vlet/* => list_vlet,"
                "/list_vlet_zonefile/* => list_vlet_zonefile")) {
        GO_TO_DIE("add monitor server failed");
    }
    auto bvar_service = new common::BvarMonitorServiceImpl();
    bvar_service->set_bvar_monitor(_bvar_monitor);
    ARIES_UNLIKELY_IF (_monitor_server.AddService(bvar_service, baidu::rpc::SERVER_OWNS_SERVICE)) {
        GO_TO_DIE("add bvar monitor server failed");
    }
    ARIES_UNLIKELY_IF (_monitor_server.Start(port, &options)) {
        GO_TO_DIE("start monitor server failed");
    }
}


} // end namespace of datanode
} // end namespace of aries

/* vim: set ts=4 sw=4 sts=4 tw=100 */
