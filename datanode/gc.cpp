// Copyright (c) 2016 Baidu.com, Inc. All Rights Reserved
// Author <PERSON><PERSON><PERSON><PERSON><PERSON>(chen<PERSON><PERSON><EMAIL>)
// Date: Tue Nov 15 10:39:06 CST 2016


#include "baidu/inf/aries/datanode/gc.h"
#include "baidu/inf/aries/datanode/vlet.h"
#include "baidu/inf/aries/datanode/datanode.h"
#include "baidu/inf/aries-api/common/types.h"
#include "baidu//inf/aries-api/common/config.h"
#include "baidu/inf/aries-api/common/proto/balancer.pb.h"
#include "baidu/rpc/server.h"
#include "baidu/rpc/channel.h"
#include "base/fast_rand.h"
#include "base/time.h"
#include "baidu/inf/aries/common/common.h"
#include "baidu/inf/aries/datanode/util/util.h"
#include "baidu/inf/aries/datanode/io_scheduler.h"

namespace aries {
namespace datanode {

int Gc::do_diff() {
    diff_disk_vlets();
    return 0;
}

int Gc::report_bad_disk(const DiskAgentPtr disk_agent) {
    uint64_t disk_id = disk_agent->disk_id();
    if (FLAGS_readonly_mode) {
        LOG(NOTICE) << "read only mode, stop report bad disk, disk_id:" << disk_id;
        return -1;
    }
    LOG(NOTICE) << "report bad disk, disk_id:" << disk_id;
    aries::pb::ReportBadDiskRequest        request;
    aries::pb::AckResponse                 ack;

    uint64_t node_addr = common::endpoint2int(common::get_local_addr());
    request.set_token(FLAGS_token);
    request.set_req_addr(node_addr);
    request.set_disk_id(disk_id);

    if (_master_caller.report_bad_disk(&request, &ack)) {
        LOG(FATAL) << disk_agent->name() << " drop disk failed";
        return -1;
    } else {
        LOG(WARNING) << disk_agent->name() << " drop disk success";
    }

    return 0;
}

int Gc::get_gc_vlet_token(const VletIdentify& info) {
    LOG(NOTICE) << "get gc vlet token, vlet:" << info;
    aries::pb::GetGcVletTokenRequest        request;
    aries::pb::AckResponse                 ack;

    request.set_token(FLAGS_token);

    return _master_caller.get_gc_vlet_token(&request, &ack);
}

int Gc::diff_disk_vlets() {
    if (FLAGS_readonly_mode) {
        LOG(NOTICE) << "read only mode, stop diff disk vlets";
        return 0;
    }
    // get datanode meta from master
    aries::pb::ListNodeVletRequest request;
    aries::pb::ListNodeVletResponse response;

    uint64_t node_addr = common::endpoint2int(common::get_local_addr());
    uint64_t log_id = base::fast_rand();
    request.set_token(FLAGS_token);
    request.set_req_addr(node_addr);
    request.set_node_addr(node_addr);

    LOG(NOTICE) << _name << "diff disk vlets will list disk vlet from master";

    if (_master_caller.list_node_vlet(&request, &response)) {
        LOG(WARNING) << "list node vlet failed from master.";
        return -1;
    }
    _master_caller.set_s_primary_start_time(response.primary_start_time());

    int disk_num = response.disk_vlet_list_size();
    LOG(NOTICE) << _name << "diff disk vlets finish list disk vlet from master, " << noflush;
    for (int i = 0; i < disk_num; ++i) {
        LOG(NOTICE) << "(id:" << response.disk_vlet_list(i).disk_id()
            << " disk_type:" << response.disk_vlet_list(i).disk_type() << ")," << noflush;
    }
    LOG(NOTICE) << " used_disk_num:" << disk_num;

    // try to start disk_agent
    int disk_number = response.disk_vlet_list_size();

    std::set<int> check_disks;
    for (auto& kv : _diff_context_map) {
        auto& diff_disk_context = kv.second;
        if (diff_disk_context.running) {
            continue;
        }
        check_disks.insert(kv.first);
        kv.second.reset();
    }

    int req_number = 0;
    for (int i = 0; i < disk_number; ++i) {
        const auto& disk_conf = response.disk_vlet_list(i);
        int disk_id = disk_conf.disk_id();
        auto iter = _diff_context_map.find(disk_id);
        if (iter == _diff_context_map.end()) {
            LOG(WARNING) << _name << "invalid disk_id in GetDatanodeMetaResponse, check disk.conf";
            break;
        }

        auto& diff_disk_context = iter->second;
        if (diff_disk_context.is_used) {
            LOG(WARNING) << _name << "duplicated disk_id in GetDatanodeMetaResponse";
            break;
        }

        auto disk_type = common::string2disk_type(disk_conf.disk_type());
        if (disk_type != diff_disk_context.disk_agent->disk_type()) {
            LOG(WARNING) << _name << "disk id: " << disk_id << " disk_type is inconsistent"
                << " master disk_type:" << common::disk_type2string(disk_type)
                << " disk_agent disk_type:" << common::disk_type2string(diff_disk_context.disk_agent->disk_type());
        }

        diff_disk_context.is_used = true;
        //diff_disk_context.create_time
        auto& vlet_map = diff_disk_context.vlet_map;
        int vlet_number = disk_conf.vlet_list_size();
        for (int j = 0; j < vlet_number; ++j) {
            const auto& vlet_info = disk_conf.vlet_list(j);
            VletIdentifyWithMembership vlet_id_with_membership;

            VletIdentify& vlet_id = vlet_id_with_membership.vlet_id;
            vlet_id.volume_id = vlet_info.volume_id();
            vlet_id.shard_index = vlet_info.shard_index();
            vlet_id.create_time = vlet_info.create_time();
            vlet_id.vlet_version = vlet_info.vlet_version();

            vlet_id.volume_version = vlet_info.membership().volume_version();
            vlet_id_with_membership.vlet_membership.CopyFrom(vlet_info.membership());
            vlet_id.max_holes_size_for_fast_remove = vlet_info.max_holes_size_for_fast_remove();
            vlet_id.min_record_size_for_fast_remove = vlet_info.min_record_size_for_fast_remove();
            vlet_id.permit_fast_remove = vlet_info.permit_fast_remove();
            vlet_id.append_zone_rewrite_rate = vlet_info.append_zone_rewrite_rate();
            vlet_id.daily_rewrite_start_time = vlet_info.daily_rewrite_start_time();
            vlet_id.daily_rewrite_duration_second = vlet_info.daily_rewrite_duration_second();
            vlet_id.shard_compress_option.CopyFrom(vlet_info.shard_compress_option());
            vlet_id.use_standard_record_layout = vlet_info.use_standard_record_layout();

            vlet_map[vlet_id.volume_id] = vlet_id_with_membership;
        }
        ++req_number;
    }

    if (req_number != disk_number) {
        return -1;
    }

    std::shared_ptr<common::SyncPoint> sync_point(new common::SyncPoint(check_disks.size()));
    for (auto& kv : _diff_context_map) {
        if (check_disks.count(kv.first) == 0) {
            continue;
        }
        auto& diff_disk_context = kv.second;
        diff_disk_context.running = true;
        diff_disk_context.sync_point = sync_point;
        DiskAgent* disk_agent = diff_disk_context.disk_agent;
        QueueExec(DIFF_DISK_ACTION, disk_agent, &diff_disk_context);
    }
    sync_point->wait_ms(FLAGS_disk_hung_second * 1000);
    sync_point.reset();

    uint64_t report_bad_disk_num = 0;
    uint64_t report_bad_vlet_num = 0;
    uint64_t drop_bad_vlet_num = 0;
    uint64_t update_vlet_num = 0;

    for (auto& kv : _diff_context_map) {
        if (check_disks.count(kv.first) == 0) {
            continue;
        }
        auto& diff_disk_context = kv.second;
        if (diff_disk_context.running) {
            continue;
        }
        DiskAgent* disk_agent = diff_disk_context.disk_agent;
        if (diff_disk_context.need_to_drop) {
            LOG(FATAL) << disk_agent->name() << " found a differed disk, will drop disk";
            if (report_bad_disk(disk_agent) == 0) {
                disk_agent->stop_for_error();
            }
            report_bad_disk_num += 1;
            continue;
        }
        if (diff_disk_context.is_hung) {
#ifdef _UNIT_TEST
            g_hunging = true;
#endif
            LOG(FATAL) << disk_agent->name() <<
                    " disk is hunging, will skip diff";
            continue;
        }

        VletManager* vlet_manager = disk_agent->vlet_manager();
        std::vector<VletIdentify>&   node_remove_vlet_list =
            diff_disk_context.node_remove_vlet_list;

        for (auto& id : node_remove_vlet_list) {
            VletPtr vlet_ptr = vlet_manager->find(id.volume_id);
            if (vlet_ptr) {
                if (vlet_ptr->vlet_identify() == id) {
                    // get gc vlet token
                    if (get_gc_vlet_token(id) != AIE_OK) {
                        LOG(WARNING) << "get gc vlet token failed, vlet:" << id << ", skip gc vlet";
                        break;
                    }
                    std::shared_ptr<common::SyncPoint> sync_point(new common::SyncPoint(1));
                    vlet_manager->remove(vlet_ptr->volume_id());
                    DropVletReq* req = new DropVletReq(sync_point);
                    req->vlet_ptr = vlet_ptr;
                    QueueExec(DROP_VLET_ACTION, disk_agent, req);
                    if (0 == sync_point->wait_ms(FLAGS_disk_hung_second * 1000)) {
                        drop_bad_vlet_num += 1;
                    } else {
#ifdef _UNIT_TEST
                        g_hunging = true;
#endif
                        LOG(FATAL) << disk_agent->name() <<
                            " disk is hunging, drop vlet maybe hung";
                    }
                    sync_point.reset();
                }
            }
        }

        std::vector<VletIdentify>&   master_drop_vlet_list =
            diff_disk_context.master_drop_vlet_list;

        for (auto& vlet_id : master_drop_vlet_list) {
            aries::pb::VletInfo info;
            info.set_volume_id(vlet_id.volume_id);
            info.set_shard_index(vlet_id.shard_index);
            info.set_vlet_version(vlet_id.vlet_version);
            info.set_node_addr(common::endpoint2int(common::get_local_addr()));
            info.set_disk_id(disk_agent->disk_id());
            info.set_create_time(vlet_id.create_time);

            report_bad_vlet(log_id, info);
            report_bad_vlet_num += 1;
        }

        std::vector<uint64_t>&  need_update_vlet_list = diff_disk_context.need_update_vlet_list;

        auto& vlet_map = diff_disk_context.vlet_map;

        for (auto& volume_id : need_update_vlet_list) {
            VletPtr vlet_ptr = vlet_manager->find(volume_id);
            if (!vlet_ptr) {
                continue;
            }

            VletIdentifyWithMembership& vlet_id_with_membership = vlet_map[volume_id];
            VletIdentify& vlet_id = vlet_id_with_membership.vlet_id;

            if (vlet_ptr->vlet_identify() == vlet_id) {
                std::shared_ptr<common::SyncPoint> sync_point(new common::SyncPoint(1));
                UpdateVletReq* req = new UpdateVletReq(sync_point);
                req->pb_req.mutable_membership()->CopyFrom(vlet_id_with_membership.vlet_membership);
                req->max_holes_size_for_fast_remove = vlet_id.max_holes_size_for_fast_remove;
                req->min_record_size_for_fast_remove = vlet_id.min_record_size_for_fast_remove;
                req->permit_fast_remove = vlet_id.permit_fast_remove;
                req->append_zone_rewrite_rate = vlet_id.append_zone_rewrite_rate;
                req->daily_rewrite_start_time = vlet_id.daily_rewrite_start_time;
                req->daily_rewrite_duration_second = vlet_id.daily_rewrite_duration_second;
                req->shard_compress_option.CopyFrom(vlet_id.shard_compress_option);
                req->vlet_ptr = vlet_ptr;
                req->action = UPDATE_VLET_ACTION;
                base::get_leaky_singleton<IOScheduler>()->dispatch_task(static_cast<IoContext*>(req));

                if (0 == sync_point->wait_ms(FLAGS_disk_hung_second * 1000)) {
                    update_vlet_num += 1;
                } else {
#ifdef _UNIT_TEST
                    g_hunging = true;
#endif
                    LOG(FATAL) << disk_agent->name() <<
                        " disk is hunging, update vlet maybe hung";
                }
            }
        }
    }

    LOG(NOTICE) << _name << "diff disk vlets finished,"
            << " report_bad_disk_num:" << report_bad_disk_num
            << " report_bad_vlet_num:" << report_bad_vlet_num
            << " drop_bad_vlet_num:" << drop_bad_vlet_num
            << " update_vlet_num:" << update_vlet_num;

    return 0;
}


int report_balancer(base::EndPoint balancer_addr, aries::pb::ConfirmReplaceRequest& request, uint64_t log_id) {
    baidu::rpc::Channel     rpc_channel;

    if (rpc_channel.Init(balancer_addr, nullptr) != 0) {
        return -1;
    }

    baidu::rpc::Controller  cntl;
    cntl.set_timeout_ms(10000);
    cntl.set_log_id(log_id);

    aries::pb::BalancerService_Stub stub(&rpc_channel);

    aries::pb::AckResponse response;
    stub.confirm_replace_vlet(&cntl, &request, &response, nullptr);

    if (cntl.Failed()) {
        ARIES_RPC_LOG(NOTICE) << "report balancer failed to " << balancer_addr
                << " req:" << common::pb2json(request)
                << " error:" << cntl.ErrorText();
        return -1;
    } else {
        ARIES_RPC_LOG(NOTICE) << "report balancer succeeded to " << balancer_addr
                << " req:" << common::pb2json(request);
        return 0;
    }
}

void Gc::do_check_slowdisk() {    
    LOG(NOTICE) << "Heartbeat start do_check_slowdisk";
    std::shared_ptr<common::SyncPoint> sync_point(new common::SyncPoint(
                _diff_context_map.size()));
    std::vector<std::shared_ptr<CheckSlowContext>> ctxs;
    for (auto& kv : _diff_context_map) {
        if (!kv.second.is_used) {
            sync_point->signal();
            continue;
        }
        std::shared_ptr<CheckSlowContext> ctx(new CheckSlowContext());
        ctx->sync_point = sync_point;
        ctx->disk_agent = kv.second.disk_agent;
        ctx->is_slow_disk = false;
        ctxs.push_back(ctx);
        CheckSlowKylinContext* kylin_ctx = new CheckSlowKylinContext();
        kylin_ctx->ctx = ctx;

        QueueExecEmergent(CHECK_SLOW_ACTION, ctx->disk_agent, kylin_ctx);
    }
    
    sync_point->wait();
    for (auto& ctx : ctxs) {
        if (ctx->is_slow_disk == true) {
            LOG(FATAL) << ctx->disk_agent->name() << " disk is slow, will drop disk";
                
#ifdef _UNIT_TEST
        g_slowing = true;
#endif

            // master drop disk and trigger recover
            if (report_bad_disk(ctx->disk_agent) == 0) {
                // datanode drop disk
                ctx->disk_agent->stop_for_error();
            }
        } else {
            LOG(NOTICE) << ctx->disk_agent->name() <<
                " check_slow_disk ok, disk is healthy";
        }
        ctx.reset();
    }
    sync_point.reset();
}

void Gc::do_check_hung() {

    LOG(NOTICE) << "Heartbeat start do_check_hung";
    std::shared_ptr<common::SyncPoint> sync_point(new common::SyncPoint(
                _diff_context_map.size()));
    std::vector<std::shared_ptr<CheckHungContext>> ctxs;
    for (auto& kv : _diff_context_map) {
        if (!kv.second.is_used) {
            sync_point->signal();
            continue;
        }
        std::shared_ptr<CheckHungContext> ctx(new CheckHungContext());
        ctx->sync_point = sync_point;
        ctx->disk_agent = kv.second.disk_agent;
        ctxs.push_back(ctx);
        CheckHungKylinContext* kylin_ctx = new CheckHungKylinContext();
        kylin_ctx->ctx = ctx;

        QueueExecEmergent(CHECK_HUNG_ACTION, ctx->disk_agent, kylin_ctx);
    }

    sync_point->wait_ms(FLAGS_disk_hung_second * 1000);
    for (auto& ctx : ctxs) {
        assert(ctx->vlet_agent_num <= 1);
        if (ctx->vlet_agent_num == 0) {
#ifdef _UNIT_TEST
            g_hunging = true;
#endif
            LOG(FATAL) << ctx->disk_agent->name() << " disk is hunging, will drop disk";
            // master drop disk and trigger recover
            if (report_bad_disk(ctx->disk_agent) == 0) {
                // datanode drop disk
                ctx->disk_agent->stop_for_error();
            }
        }
    }

    sync_point.reset();
    for (auto& ctx : ctxs) {
        if (ctx->vlet_agent_num == 1) {
            LOG(NOTICE) << ctx->disk_agent->name() <<
                " check_hung ok, disk is healthy";
        }
        ctx.reset();
    }
}

} // end namespace of datanode
} // end namespace of aries

/* vim: set ts=4 sw=4 sts=4 tw=100 */
