#include "baidu/inf/aries/datanode/vlet_indexer.h"
#include "baidu/inf/aries/datanode/vlet.h"
namespace aries {
namespace datanode {

namespace {
static inline rocksdb::Slice slice(const VolumeKey& key) {
    return rocksdb::Slice((const char*)&key, kVolumeKeyLen);
}
}

Status VletIndexer::exist(const VolumeKey& key) {
    Status s;
    if (_is_zone_disk) {
        aries::pb::GenericVletInfo vlet_info;
        s = get(key, &vlet_info);
        if (s.code() == AIE_BLOB_NOT_EXIST || (s.ok() && vlet_info.zone_vlet_info().has_dropped_timestamp())) {
            return Status(AIE_NOT_EXIST);
        }
    } else {
        std::string value;
        s = _db->Get(rocksdb::ReadOptions(), slice(key), &value);
        if (s.code() == AIE_BLOB_NOT_EXIST) {
            return Status(AIE_NOT_EXIST);
        }
    }

    return s;
}

Status VletIndexer::destroy(const VolumeKey& key, std::map<aries::datanode::ZoneID, int64_t>* used_size_map) {
    auto volume_id = key.volume_id();
    Status s;
    // 1. remove shard index
    if (_is_zone_disk) {
        s = ZoneRecordDBIndexer::destroy(volume_id, *_db, used_size_map);
    } else {
        RecordKey begin(volume_id, 0);
        rocksdb::WriteBatch batch;
        std::unique_ptr<rocksdb::Iterator> it(_db->NewIterator(rocksdb::ReadOptions()));
        it->Seek(begin.to_slice());
        while (it->Valid()) {
            if (it->key().size() != sizeof(RecordKey)) {
                it->Next();
                continue;
            }

            RecordKey *key = (RecordKey*) it->key().data();
            if (key->vid() != volume_id) {
                break;
            }
            batch.Delete(it->key());
            it->Next();
        }

        s = _db->Write(rocksdb::WriteOptions(), &batch);
    }
    if (!s.ok()) {
        LOG(WARNING) << "delete residual records fail, vid:" << key.volume_id() << " status:" << s;
        return s;
    }

    // 2. remove vlet index
    s = _db->Delete(rocksdb::WriteOptions(), slice(key));
    if (!s.ok()) {
        LOG(WARNING) << "delete residual volume key fail, vid:" << key.volume_id() << " status:" << s;
        return s;
    }

    if (s.ok()) {
        s = flush();
    }

    return s;
}

Status VletIndexer::remove(const VolumeKey& key) {
    Status s = exist(key);
    if (!s.ok()) {
        return s;
    }

    // only mark remove
    if (_is_zone_disk) {
        aries::pb::GenericVletInfo vlet_info;
        s = get(key, &vlet_info);
        if (s.ok()) {
            vlet_info.mutable_zone_vlet_info()->set_dropped_timestamp(base::gettimeofday_s()); // for delay gc
            s = put(vlet_info);
        }
    } else {
        s = _db->Delete(rocksdb::WriteOptions(), slice(key));
    }
    if (s.ok()) {
        s = flush();
    }
    return s;
}

Status VletIndexer::put(const Vlet& vlet) {
    std::string info_str = vlet.info();
    VolumeKey volume_key(vlet.volume_id(), vlet.vlet_type());
    return put(slice(volume_key), info_str);
}

Status VletIndexer::put(const rocksdb::Slice& key, const rocksdb::Slice& value) {
    rocksdb::WriteOptions opts;
    opts.sync = true;
    return _db->Put(opts, key, value);
}

Status VletIndexer::flush() {
    if (_is_zone_disk && FLAGS_enable_zone_db_flush_wal) {
        return _db->FlushWAL(true);
    }
    return Status();
}

}
}
