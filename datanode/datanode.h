// Copyright (c) 2016 Baidu.com, Inc. All Rights Reserved
// Author ch<PERSON><PERSON><PERSON><PERSON>(chen<PERSON><PERSON><PERSON>@baidu.com)
// Date: Tue Oct 11 17:11:32 CST 2016

#ifndef BAIDU_INF_ARIES_DATANODE_DATANODE_H
#define BAIDU_INF_ARIES_DATANODE_DATANODE_H

#include <atomic>
#include "baidu/rpc/server.h"
#include "baidu/inf/aries/datanode/disk_manager.h"
#include "baidu/inf/aries/datanode/vlet_manager.h"
#include "baidu/inf/aries/datanode/heartbeat.h"
#include "baidu/inf/aries/datanode/gc.h"
#include "baidu/inf/aries/datanode/cleaner.h"
#include "baidu/inf/aries/datanode/checker.h"
#include "baidu/inf/aries/datanode/overdue_call_checker.h"
#include "baidu/inf/aries-api/common/bvar_monitor.h"
#include "baidu/inf/aries/datanode/rewrite_vlet.h"
#include "baidu/inf/aries/datanode/throttle/flow_limiter.h"

extern bool g_hunging;

namespace aries {
namespace datanode {

class Datanode;

extern Datanode* g_datanode;

class Datanode {
public:
    Datanode();

    ~Datanode();

    void start();

    void stop();

    void join();

    void bury();

    //
    int reload_configure();

    DiskManager* disk_manager() {
        return _disk_manager;
    }

    CacheManager* cache_manager() {
        return _disk_manager->cache_manager();
    }

    VletManager* vlet_manager() {
        return _vlet_manager;
    }

    DataChecker* data_checker() {
        return _data_checker;
    }

    std::shared_ptr<FlowLimiter> flow_limiter() {
        return _flow_limiter;
    }

    Gc* gc() {
        return _gc;
    }
    Cleaner* cleaner() {
        return _cleaner;
    }
    RewriteManager* rewrite_manager() {
        return _rewrite_manager;
    }
private:
    void init();

    void start_control_service(uint32_t port);

    void start_data_service(uint32_t port);

    void start_privilege_data_service(uint32_t port);

    void start_monitor_service(uint32_t port);

private:
    DiskManager                     *_disk_manager;
    VletManager                     *_vlet_manager;
    DataChecker                     *_data_checker;
    RewriteManager                  *_rewrite_manager;

    Gc                              *_gc;
    Cleaner                         *_cleaner;
    OverdueCallChecker              *_checker;

    Heartbeat                       _heartbeat;
    baidu::rpc::Server              _control_server;
    baidu::rpc::Server              _data_server;
    baidu::rpc::Server              _privilege_data_server;
    baidu::rpc::Server              _monitor_server;
    std::shared_ptr<common::BvarMonitor> _bvar_monitor;
    std::shared_ptr<FlowLimiter> _flow_limiter;
};

} // end namespace of datanode
} // end namespace of aries

#endif //#ifndef BAIDU_INF_ARIES_DATANODE_DATANODE_H

/* vim: set ts=4 sw=4 sts=4 tw=100 */
