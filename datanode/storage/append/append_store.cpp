// @Author: liu<PERSON><EMAIL>
// @Created Time : Thu 11 Apr 2019 02:16:13 PM CST
// @File Name: append_store.cpp
// @Description:

#include <base/crc32c.h>
#include <memory>
#include <malloc.h>
#include <fcntl.h>

#include "base/files/file_enumerator.h"
#include "base/files/file.h"

#include "baidu/inf/aries/common/common.h"
#include "baidu/inf/aries/datanode/copy_vlet.h"
#include "baidu/inf/aries/datanode/flags.h"
#include "baidu/inf/aries/datanode/storage/append/append_shard_scanner.h"
#include "baidu/inf/aries/datanode/storage/append/append_store.h"
#include "baidu/inf/aries/datanode/storage/random/linked_store_indexer.h"
#include "baidu/inf/aries/datanode/storage/random/linked_shard_record.h"
#include "baidu/inf/aries/datanode/storage/record/append_record_serializer.h"
#include "baidu/inf/aries/common/record/record_data_compressor.h"
#include "baidu/inf/aries/common/record/standard_record.h"
#include "baidu/inf/aries-api/common/bvar_define.h"
#include "baidu/inf/aries-api/common/lock.h"
#include "baidu/inf/aries-api/common/utils.h"

namespace aries {
namespace datanode {

ARIES_BVAR_ADDER(engine, rewrite_record_num);
ARIES_BVAR_ADDER(engine, rewrite_delete_record_num);
ARIES_BVAR_ADDER(engine, store_alloc_zonefile_num);
ARIES_BVAR_LATENCY(engine, store_indexer_cas_update);

bvar::LatencyRecorder g_engine_diskio_read_latency("engine", "diskio_read");
bvar::LatencyRecorder g_engine_diskio_write_latency("engine", "diskio_write");
bvar::LatencyRecorder g_engine_record_read_latency("engine", "record_read");
bvar::LatencyRecorder g_engine_record_write_latency("engine", "record_write");
bvar::LatencyRecorder g_engine_store_read_latency("engine", "store_read");
bvar::LatencyRecorder g_engine_store_write_latency("engine", "store_write");
bvar::LatencyRecorder g_rocksdb_engine_read_index_latency("rocksdb_engine", "read_index");
bvar::LatencyRecorder g_rocksdb_engine_write_index_latency("rocksdb_engine", "write_index");
bvar::LatencyRecorder g_engine_validate_record_latency("engine", "validate_record");
bvar::LatencyRecorder g_engine_serialize_record_latency("engine", "serialize_record");
bvar::LatencyRecorder g_engine_deserialize_record_latency("engine", "deserialize_record");
common::BvarReducer<bvar::Adder, int64_t> g_engine_append_write_size_adder("engine", "append_writen_size");
common::BvarReducer<bvar::Adder, int64_t> g_engine_append_rewrite_size_adder("engine", "append_rewriten_size");
double get_rewrite_wa_rate(void* arg) {
    return g_engine_append_rewrite_size_adder.get() * 1.0 / g_engine_append_write_size_adder.get(); 
}
bvar::PassiveStatus<double> g_engine_append_rewrite_wa("engine_append_rewrite_wa_rate", get_rewrite_wa_rate, nullptr);
Status AppendStoreIndexer::destroy() {
    RecordKey begin(_vid, 0);
    RecordKey end(_vid, UINT64_MAX);

    rocksdb::WriteBatch batch;
    std::unique_ptr<rocksdb::Iterator> it(_db->NewIterator(rocksdb::ReadOptions()));
    it->Seek(begin.to_slice());
    while (it->Valid()) {
        if (it->key().size() != sizeof(RecordKey)) {
            it->Next();
            continue;
        }
        RecordKey *key = (RecordKey *) it->key().data();
        if (key->vid() != _vid) {
            break;
        }
        batch.Delete(it->key());
        it->Next();
    }
    rocksdb::WriteOptions opts;
    if (FLAGS_db_use_sync) {
        opts.sync = true;
    }
    rocksdb::Status s = _db->Write(opts, &batch);
    if (!s.ok()) {
        return s;
    }
    return Status();
}

Status AppendStoreIndexer::get(const uint64_t vbid, AppendRecordLocation *loc) {
    RecordKey key(_vid, vbid);
    std::string data;
    rocksdb::Status s;

    {
        common::TimeMeasure time_measure(&g_rocksdb_engine_read_index_latency);
        s = _db->Get(rocksdb::ReadOptions(), key.to_slice(), &data);
    }

    if (s.ok()) {
        if (data.empty()) {
            return Status(AIE_BLOB_NOT_EXIST);
        }

        ::aries::pb::AppendRecordLocationInfo pb_loc;
        auto ret = pb_loc.ParseFromString(data);
        if (!ret) {
            LOG(WARNING) << "get failed due to parse location from AppendRecordLocationInfo failed";
            return Status(AIE_FAIL, "deserialize protobuf failed");
        }
        loc->init(pb_loc);
        if (loc->is_delete()) {
            return Status(AIE_MARK_REMOVED);
        }
        return Status();
    }
    return s;
}

Status AppendStoreIndexer::put(const uint64_t vbid, const AppendRecordLocation& loc) {
    RecordKey key(_vid, vbid);
    std::string value;

    ::aries::pb::AppendRecordLocationInfo pb_loc;
    loc.serialize2pb(&pb_loc);
    pb_loc.SerializeToString(&value);
    Status st;

    {
        common::TimeMeasure time_measure(&g_rocksdb_engine_write_index_latency);
        rocksdb::WriteOptions opts;
        if (FLAGS_db_use_sync) {
            opts.sync = true;
        }
        st = _db->Put(opts, key.to_slice(), rocksdb::Slice(value));
    }
    return st;
}

Status AppendStoreIndexer::put_batch(const std::vector<uint64_t>& vbid_list, 
        const std::vector<AppendRecordLocation>& loc_list) {

    assert(vbid_list.size() == loc_list.size());
    rocksdb::WriteBatch batch;
    for (size_t i = 0; i < vbid_list.size(); ++i) {
        auto& loc = loc_list[i];
        auto& vbid = vbid_list[i];

        RecordKey key(_vid, vbid);
        std::string value;
        ::aries::pb::AppendRecordLocationInfo pb_loc;
        loc.serialize2pb(&pb_loc);
        pb_loc.SerializeToString(&value);
        batch.Put(key.to_slice(), rocksdb::Slice(value));
    }
    return _db->Write(rocksdb::WriteOptions(), &batch);
}

Status AppendStoreIndexer::rewrite(const uint64_t vbid,
        const AppendRecordLocation& old_loc,
        const AppendRecordLocation& new_loc) {
    assert(old_loc.len == new_loc.len);

    RecordKey key(_vid, vbid);
    std::string data;
    rocksdb::Status s = _db->Get(rocksdb::ReadOptions(), key.to_slice(), &data);
    AppendRecordLocation curr_loc;
    AppendRecordLocation rewrite_loc;
    if (s.ok()) {
        ::aries::pb::AppendRecordLocationInfo pb_loc;
        auto ret = pb_loc.ParseFromString(data);
        if (!ret) {
            LOG(WARNING) << "rewrite failed due to parse location from AppendRecordLocationInfo failed";
            return Status(AIE_FAIL, "deserialize protobuf failed");
        }
        curr_loc.init(pb_loc);
    } else if (s.code() == rocksdb::Status::kNotFound) {
        return Status(AIE_OK);
    } else {
        return s;
    }

    //if curr_loc not init, file_index == UINT16_MAX
    if (curr_loc.file_index != old_loc.file_index 
            || curr_loc.offset != old_loc.offset
            || curr_loc.len != old_loc.len) {
        LOG(WARNING) << "rewrite failed due to record location has been changed,"
                << " vid:" << _vid << " vbid:" << vbid
                << " old_loc:" << old_loc.loc_to_string()
                << " curr_loc:" << curr_loc.loc_to_string();
        return Status(AIE_OK);
    }
    rewrite_loc = new_loc;
    rewrite_loc.delete_time = curr_loc.delete_time;
    rewrite_loc.blob_ttl_timestamp = curr_loc.blob_ttl_timestamp;
    std::string value;
    ::aries::pb::AppendRecordLocationInfo rewrite_pb_loc;
    rewrite_loc.serialize2pb(&rewrite_pb_loc);
    rewrite_pb_loc.SerializeToString(&value);
    rocksdb::WriteOptions opts;
    if (FLAGS_db_use_sync) {
        opts.sync = true;
    }
    return _db->Put(opts, key.to_slice(), rocksdb::Slice(value));
}

Status AppendStoreIndexer::remove(uint64_t vbid) {
    RecordKey key(_vid, vbid);
    return _db->Delete(rocksdb::WriteOptions(), key.to_slice());
}

AppendStore::AppendStore(const StoreOptions &opts) {
    _opts = opts;
}

AppendStore::~AppendStore() {
}

void AppendStore::init_meta(const ::aries::pb::AppendVletInfo& vlet_info) {
    _vlet_info = vlet_info;
    _volume_id = vlet_info.volume_id();
    _shard_index = vlet_info.shard_index();
    _vlet_type = vlet_info.vlet_type();
    _align_size = vlet_info.align_size();
    _zonefile_size = vlet_info.zonefile_size();
    _total_size = _zonefile_size * vlet_info.zonefile_num();
    _use_standard_record_layout = vlet_info.use_standard_record_layout();
    base::string_printf(&_name, "A_%lu_%d", _volume_id, _shard_index);
}

Status AppendStore::create(const ::aries::pb::AppendVletInfo& vlet_info, std::shared_ptr<rocksdb::DB> db,
                           const std::string &path) {
    _path = path;
    init_meta(vlet_info);
    if (create_dir(_path) != 0) {
        LOG(WARNING) << "create AppendStore failed due to create vlet directory failed,"
                     << " path:" << _path << " errno:(" << errno << ")" << strerror(errno);
        return Status::by_errno("create directory failed");
    }
    _db = db;
    _indexer = std::unique_ptr<AppendStoreIndexer>(new AppendStoreIndexer(_volume_id, _vlet_type, _db));
    _append_record_serializer.reset(new AppendRecordSerializer(_volume_id, _align_size, _path));
    _standard_record_serializer.reset(new common::StandardRecordSerializer(_volume_id, _align_size, _path));

    auto status = alloc_zonefile(false);
    if (!status.ok()) {
        LOG(WARNING) << "create AppendStore failed due to alloc new zonefile failed,"
                     << " path:" << _path << " error:" << status;
        return status;
    }

    LOG(NOTICE) << "create AppendStore succeeded, path:" << _path << " db:" << _db->GetName()
                << " vlet_info:" << common::pb2json(vlet_info);
    return Status(AIE_OK);
}

bool AppendStore::load_dir() {

    base::FilePath dir(_path);
    base::FileEnumerator file_enum(dir, false, base::FileEnumerator::FILES);
    for (base::FilePath file_path = file_enum.Next(); !file_path.empty();
            file_path = file_enum.Next()) {

        base::File file(file_path, base::File::FLAG_READ | base::File::FLAG_OPEN);
        auto len = file.GetLength();
        //smr meta file may > 0, but has a very small size.
        if (len > 0) {
            // read zonefile head.
            ZonefileHead head;
            auto status = Zonefile::read_zonefile_head(file_path.value(), &head);
            if (!status.ok()) {
                LOG(WARNING) << "read file head failed, path:" << file_path.value()
                    << " error:" << status;
                continue;
            }
            _volume_id = head.volume_id;
            _vlet_type = head.vlet_type;
            _align_size = head.align_size;

            LOG(TRACE) << "load append vlet dir get volume_id:" << _volume_id
                << " path:" << file_path.value() << " vlet_type:" << _vlet_type;
            return true;
        } else {
            LOG(WARNING) << "read an empty file, path:" << file_path.value();
        }
    }
    return false;
}

ZonefilePtr AppendStore::regenerate_zonefile(
        const ::aries::pb::AppendVletInfo::ZonefileInfo& pb_info) {

    uint64_t sequence_id = pb_info.file_sequence_id();
    std::string zonefile_name = base::string_printf("%" PRId64 "", sequence_id);
    std::string zonefile_path = _path + "/" + zonefile_name;
    ZonefileInfo info;
    info.file_index = pb_info.file_index();
    info.file_sequence_id = sequence_id;
    info.rewrite_generation = pb_info.rewrite_generation();
    info.volume_id = _volume_id;
    info.align_size = _align_size;
    info.create_time = pb_info.create_time();
    info.total_file_size = _zonefile_size;
    info.path = zonefile_path;
    info.vlet_type = _vlet_type;
    ZonefilePtr zonefile = (ZonefilePtr)(new Zonefile(info));
    zonefile->set_begin_position(pb_info.begin_position());
    zonefile->set_write_location(pb_info.begin_position());
    zonefile->add_used_size(_align_size * pb_info.begin_position());
    return zonefile;
}

Status AppendStore::rebuild_zonefile_map() {
    for (size_t i = 0; i < (size_t)_vlet_info.raw_zonefile_list_size(); ++i) {
        auto& pb_info = _vlet_info.raw_zonefile_list(i);
        auto file_index = pb_info.file_index();

        auto zonefile = regenerate_zonefile(pb_info);
        auto s = zonefile->open(_opts);
        if (!s.ok()) {
            LOG(WARNING) << "open zonefile failed, file:" << zonefile->file_path()
                         << " file_info:" << zonefile->to_string() << " error:" << s;
            return s;
        }
        if (i == (size_t)_vlet_info.raw_zonefile_list_size() - 1) {
            _curr_write_file = zonefile;
            LOG(TRACE) << "init curr write file succeeded, file:" << zonefile->file_path()
                       << " file_info:" << zonefile->to_string();
        } else {
            zonefile->seal();
            LOG(TRACE) << "seal zonefile, file:" << zonefile->file_path()
                       << " file_info:" << zonefile->to_string();
        }
        auto ret = _zonefile_map.insert({file_index, zonefile}).second;
        assert(ret == true);
        _raw_zonefile_list.push_back(zonefile);

        LOG(TRACE) << "add raw zonefile, file:" << zonefile->file_path();
    }

    for (size_t i = 0; i < (size_t)_vlet_info.rewrite_zonefile_list_size(); ++i) {
        auto& pb_info = _vlet_info.rewrite_zonefile_list(i);
        auto file_index = pb_info.file_index();
        auto zonefile = regenerate_zonefile(pb_info);
        auto s = zonefile->open(_opts);
        if (!s.ok()) {
            LOG(WARNING) << "open zonefile failed, file:" << zonefile->file_path()
                         << " file_info:" << zonefile->to_string() << " error:" << s;
            return s;
        }
        if (i == (size_t)_vlet_info.rewrite_zonefile_list_size() - 1) {
            _curr_rewrite_file = zonefile;
        } else {
            zonefile->seal();
        }
        if (zonefile->rewrite_generation() > _max_rewrite_generation) {
            _max_rewrite_generation = zonefile->rewrite_generation();
        }

        auto ret = (_zonefile_map.insert({file_index, zonefile}).second);
        assert(ret == true);
        _rewrite_zonefile_list.push_back(zonefile);
        LOG(TRACE) << "add rewrite zonefile, file:" << zonefile->file_path()
                   << " file_info:" << zonefile->to_string();
    }

    //get write_location and every zonefile used size;
    std::shared_ptr<rocksdb::Iterator> iter(_db->NewIterator(rocksdb::ReadOptions()));
    auto key = rocksdb::Slice((char *) &_volume_id, sizeof(_volume_id));

    for (iter->Seek(key); iter->Valid(); iter->Next()) {
        if (iter->key().size() != sizeof(RecordKey)) {
            continue;
        }
        RecordKey *key = (RecordKey *) iter->key().data();
        if (key->vid() != _volume_id) {
            break;
        }

        if (iter->value().size() == 0) {
            continue;
        }

        AppendRecordLocation loc;
        loc.parse_from_array((const void*)iter->value().data(), iter->value().size());

        if (_zonefile_map.find(loc.file_index) == _zonefile_map.end()) {
            LOG(WARNING) << _name << " could not find zone_file for record,"
                    << " vid:" << key->vid() << " vbid:" << key->vbid()
                    << " file_index:" << loc.file_index;
            continue;
        }

        auto& zonefile = _zonefile_map[loc.file_index];
        zonefile->add_used_size(loc.len * _align_size);
        if (zonefile->write_location() < loc.offset + loc.len) {
            zonefile->set_write_location(loc.offset + loc.len);
        }
        ARIES_DEBUG_LOG(TRACE) << _name << " file_index:" << loc.file_index 
                << " used_size:" << zonefile->used_size() << " add_used_size:" << loc.len * _align_size
                << " write_location:" << zonefile->write_location() << " location:" << loc.offset + loc.len;
        // update some statistics
        update_stat(loc, key->vbid());
    }

    //get write location from smr, double check;
    assert(_curr_write_file);
    auto status = correct_write_off(_curr_write_file);
    if (status.code() != AIE_OK) {
        LOG(FATAL) << "correct write offset failed for curr_write_file:"
                   << _curr_write_file->file_path();
        return status;
    }

    if (_curr_rewrite_file) {
        status = correct_write_off(_curr_rewrite_file);
        if (status.code() != AIE_OK) {
            LOG(FATAL) << "correct write offset failed for curr_rewrite_file:"
                       << _curr_rewrite_file->file_path();
            return status;
        }
    }

    //get max file_sequence_id;
    for (auto& zonefile : _zonefile_map) {
        if (zonefile.second->file_sequence_id() > _max_file_sequence_id) {
            _max_file_sequence_id = zonefile.second->file_sequence_id();
        }
    }
    if (_curr_write_file->is_seal()) {
        alloc_zonefile(false);
    }
    if (_curr_rewrite_file && _curr_rewrite_file->is_seal()) {
        alloc_zonefile(true);
    }
    return Status(AIE_OK);
}

Status AppendStore::correct_write_off(ZonefilePtr zonefile) {
    uint64_t offset = 0;
    auto status = zonefile->get_physical_location(&offset);
    if (status.ok()) {
        assert(offset % _align_size == 0);
        if (offset > zonefile->write_location() * _align_size) {
            LOG(WARNING) << _name << " file:" << zonefile->file_path()
                << " physical write offset:" << offset
                << " is larger than db indexer offset:"
                << zonefile->write_location() * _align_size;
            if (!FLAGS_fast_recovery_mode) {
                // try to replay some record from disk
                status = replay_lost_records(zonefile, zonefile->write_location() * _align_size, offset);
                if (status.code() != AIE_OK) {
                    zonefile->seal();
                    LOG(WARNING) << " replay lost records failed,  seal file, status:" << status;
                }
            } else {
                // seal old zone when write location mismatch
                zonefile->seal();
            }
        } else if (offset < zonefile->write_location() * _align_size) {
            LOG(FATAL) << _name << " file:" << zonefile->file_path() << " data may corrupt"
                << " physical write offset:" << offset
                << " is smaller than db indexer offset:"
                << zonefile->write_location() * _align_size;
            return Status(AIE_CORRUPT, "physical write offset smaller than db indexer offset");
        }
    } else if (status.code() != AIE_NOTSUPPORT) {
        LOG(FATAL) << _name << " get zonefile physical location failed,"
                   << " file:" << zonefile->file_path() << " error:" << status;
        return status;
    }
    return Status(AIE_OK);
}

Status AppendStore::replay_lost_records(ZonefilePtr zonefile, uint64_t start_offset, uint64_t end_offset) {
    // 1. read from disk
    int64_t data_len = end_offset - start_offset;
    common::Buffer buffer(data_len, _align_size);
    auto s = zonefile->read_range(start_offset / _align_size, data_len / _align_size, &buffer);
    if (!s.ok()) {
        ARIES_FUNC_LOG(WARNING) << "read range fail. path:" << _path << " status:" << s;
        return s;
    }
    ARIES_FUNC_LOG(TRACE) << "read range succ, total_len:" << buffer.size() << " path:" << _path;

    // 2. replay records and update memory meta
    uint32_t cur_off = 0;
    while (cur_off < buffer.size()) {
        AppendRecordHead head = *((AppendRecordHead*)(buffer.buf() + cur_off));
        if (head.record_magic == APPEND_RECORD_MAGIC) {
            auto len = head.record_len;
            cur_off += len;
            if (buffer.size() < cur_off) {  // not a complete record
                ARIES_FUNC_LOG(WARNING) << "replay record, found a incomplete record,  path:" << _path;
                return AIE_FAIL;
            }

            //check crc
            uint32_t* record_crc = (uint32_t*)(buffer.buf() + cur_off - sizeof(uint32_t));
            uint32_t calc_crc = base::crc32c::Value(buffer.buf() + cur_off - len, len - sizeof(uint32_t));
            if (*record_crc != calc_crc) {
                ARIES_FUNC_LOG(WARNING) << "replay record, crc inconsistent path:" << _path
                                      << "record_crc:" << *record_crc << " calc_crc:" << calc_crc;
                return AIE_FAIL;
            }
            if (head.record_type == APPEND_RECORD_DATA) {
                ::aries::pb::AppendVletRecordMeta meta;
                base::IOBuf data;
                common::Buffer cur_record(buffer.buf() + cur_off - len, len);
                s = parse_data_record_from_buffer(cur_record, &meta, &data);
                // need to release management of the buf
                cur_record.release();
                if (!s.ok()) {
                    ARIES_FUNC_LOG(WARNING) << "parse data error, path:" << _path << " status" << s;
                    return AIE_FAIL;
                }

                // update zone file  meta, add to db
                AppendRecordLocation location;
                location.file_index = zonefile->file_index();
                location.offset = (start_offset + cur_off - len) / _align_size;
                location.len = len / _align_size;
                if (meta.shard_meta().has_blob_ttl_timestamp()) {
                    location.blob_ttl_timestamp = meta.shard_meta().blob_ttl_timestamp();
                }
                s = _indexer->put(meta.vbid(), location);
                if (!s.ok()) {
                    ARIES_FUNC_LOG(WARNING) << "update indexer failed, path:" << _path << " error:" << s;
                    return s;
                }
                if (zonefile->write_location() < location.offset + location.len) {
                    zonefile->set_write_location(location.offset + location.len);
                }
                zonefile->add_used_size(len);
                update_stat(location, meta.vbid());
                LOG(NOTICE) << "replay one record, vbid:" <<  meta.vbid()  << " path:" << _path
                            << " offset:" << location.offset << " len:" << location.len;
            }
        } else {
            cur_off += _align_size;
        }
    }

    return Status(AIE_OK);
}

Status AppendStore::open(std::shared_ptr<rocksdb::DB> db, const std::string &path) {
    _db = db;
    _path = path;

    auto ret = load_dir();
    if (!ret) {
        LOG(WARNING) << "could not parse volume_id from path:" << path;
        return Status(AIE_FAIL);
    }

    _indexer = std::unique_ptr<AppendStoreIndexer>(new AppendStoreIndexer(_volume_id, _vlet_type, _db));
    _append_record_serializer.reset(new AppendRecordSerializer(_volume_id, _align_size, _path));
    _standard_record_serializer.reset(new common::StandardRecordSerializer(_volume_id, _align_size, _path));

    if (_opts.recover_on_open) {
        auto s = check_db();
        if (!s.ok()) {
            LOG(WARNING) << "open AppendStore failed due to db has vlet data, path:" << _path
                << " error:" << s.to_string();
            return s;
        }
        s = recover_index();
        if (!s.ok()) {
            LOG(WARNING) << "open AppendStore failed due to recover index failed, path:" << _path
                << " error:" << s.to_string();
            return s;
        }
        //recover vlet info
        VolumeKey volume_key;
        volume_key.set_volume_id(_vlet_info.volume_id());
        volume_key.set_vlet_type(_vlet_type);
        rocksdb::Slice key((const char*)&volume_key, kVolumeKeyLen);
        std::string string_pb;
        assert(_vlet_info.SerializeToString(&string_pb));
        rocksdb::Slice value(string_pb);
        rocksdb::WriteOptions opts;
        if (FLAGS_db_use_sync) {
            opts.sync = true;
        }
        s = _db->Put(opts, key, value);
        if (!s.ok()) {
            LOG(WARNING) << "recover AppendStore failed due to write db failed, path:" << _path
                << " error:" << s;
            return s;
        }
    }

    VolumeKey volume_key;
    volume_key.set_volume_id(_volume_id);
    volume_key.set_vlet_type(_vlet_type);
    ::aries::pb::AppendVletInfo vlet_info;
    rocksdb::Slice key((const char*)&volume_key, kVolumeKeyLen);
    std::string info_string;
    Status s = _db->Get(rocksdb::ReadOptions(), key, &info_string);
    if (!s.ok()) {
        LOG(WARNING) << "open AppendStore failed due to read db failed,"
                << " path:" << path << " error:" << s;
        return s;
    }
    assert(vlet_info.ParseFromString(info_string));
    init_meta(vlet_info);

    //rebuild zonefile map;
    auto status = rebuild_zonefile_map();
    if (!status.ok()) {
        LOG(WARNING) << "open AppendStore failed due to rebuild zonefile map failed,"
                << " path:" << _path << " error:" << status;
        return Status(AIE_FAIL);
    }
    LOG(NOTICE) << "open AppendStore succeeded, path:" << _path;
    return Status(AIE_OK);
}

Status AppendStore::destroy() {
    std::string dir = ".";
    size_t pos = _path.rfind('/');
    if (pos != std::string::npos) {
        dir = _path.substr(0, pos);
    }
    int64_t timestamp = base::gettimeofday_us();
    time_t now = timestamp / 1000000;
    struct tm t;
    char buf[128];
    localtime_r(&now, &t);
    strftime(buf, sizeof(buf), "%Y%m%d-%H:%M:%S", &t); 
    char trash_path[_path.size() + 128];
    snprintf(trash_path, sizeof(trash_path), "%s/trash/%s.%s-%06luZ", dir.c_str(), _name.c_str(),
            buf, (uint64_t)timestamp % 1000000);
    if (create_access(trash_path) != 0) {
        LOG(WARNING) << "destroy AppendStore failed due to create trash directory failed,"
                     << " trash_path:" << trash_path;
        return Status::by_errno("create trash directory failed");
    }
    if (rename(_path.c_str(), trash_path) != 0) {
        if (errno != ENOENT) {
            Status s = Status::by_errno("rename failed");
            LOG(WARNING) << "destroy AppendStore failed due to move vlet into trash failed,"
                         << " from:" << _path << " to:" << trash_path
                         << " error:" << s.to_string();
            return s;
        }
    }
    if (_indexer != nullptr) {
        _indexer->destroy();
    }
    LOG(WARNING) << "destroy AppendStore succeeded, path:" << _path;
    return Status();
}

uint16_t AppendStore::get_a_free_file_index() {
    //Must hold _meta_mutex;
    for (uint16_t i = 0; i < UINT16_MAX; ++i) {
        if (_zonefile_map.find(i) == _zonefile_map.end()) {
            return i;
        }
    }
    LOG(FATAL) << _path << ": all the file indexes have been allocated ever, the process can only be to abort!";
    ::abort();
    return UINT16_MAX;
}

uint64_t AppendStore::get_next_file_sequence_id(bool is_rewrite) {
    uint64_t next_file_sequence_id = _max_file_sequence_id + 1;

    if (is_rewrite && next_file_sequence_id % 2 != 0) {
        ++next_file_sequence_id;
    } else if (!is_rewrite && next_file_sequence_id % 2 == 0) {
        ++next_file_sequence_id;
    }

    return next_file_sequence_id;
}

Status AppendStore::alloc_zonefile(bool is_rewrite) {
     //race condition with rewrite thread;
    std::unique_lock<std::mutex> lock_guard(_meta_mutex);

    uint16_t file_index = get_a_free_file_index();
    uint64_t sequence_id = get_next_file_sequence_id(is_rewrite);
    std::string zonefile_name = base::string_printf("%" PRId64 "", sequence_id);
    std::string zonefile_path = _path + "/" + zonefile_name;

    ZonefileInfo info;
    info.file_index = file_index;
    info.file_sequence_id = sequence_id;
    if (is_rewrite) {
        info.rewrite_generation = _max_rewrite_generation;
    }
    info.align_size = _align_size;
    info.volume_id = _volume_id;
    info.create_time = ::base::gettimeofday_us();
    info.path = zonefile_path;
    info.total_file_size = _zonefile_size;
    info.vlet_type = _vlet_type;
    ZonefilePtr zonefile = std::make_shared<Zonefile>(info);

    if (::access(zonefile_path.data(), F_OK) == 0) {
        LOG(WARNING) << "zonefile already exists, it will be deleted before allocate again,"
                     << " file:" << zonefile_path;
        g_fs->unlink(zonefile_path.data());
        //no need check return.
    }

    LOG(TRACE) << "start create new write file, is_rewirte:" 
               << is_rewrite << " file:" << zonefile->file_path();

    auto status = zonefile->create(_opts);
    if (status.code() != AIE_OK) {
        LOG(WARNING) << "alloc zonefile failed due to create zonefile failed,"
                     << " file:" << zonefile->file_path() << " error:" << status;
        return status;
    }

    //update vlet info;
    ::aries::pb::AppendVletInfo vlet_info;
    vlet_info.CopyFrom(_vlet_info);
    ::aries::pb::AppendVletInfo_ZonefileInfo* zonefile_pb_ptr = nullptr;
    if (!is_rewrite) {
        zonefile_pb_ptr = vlet_info.add_raw_zonefile_list();
    } else {
        zonefile_pb_ptr = vlet_info.add_rewrite_zonefile_list();
    }
    zonefile->serialice2pb(zonefile_pb_ptr);

    //write vlet info to zonefile.
    common::Buffer vlet_info_buffer;
    if (_use_standard_record_layout) {
        _standard_record_serializer->serialize_only_meta_record(vlet_info, common::RECORD_VLET_INFO, _shard_index, &vlet_info_buffer);
    } else {
        _append_record_serializer->serialize_only_meta_record(vlet_info, common::RECORD_VLET_INFO, &vlet_info_buffer);
    }
    uint32_t position;
    AppendRecordLocation loc;
    status = zonefile->write(vlet_info_buffer.buf(), vlet_info_buffer.size(), &loc);
    if (!status.ok()) {
        LOG(WARNING) << "alloc zonefile failed due to write vlet info failed,"
                     << " file:" << zonefile->file_path() << " error:" << status;
        return status;
    }
    position = loc.offset + loc.len;
    zonefile->add_used_size(position * _align_size);
    zonefile->set_begin_position(position);
    zonefile_pb_ptr->set_begin_position(position);
 
    //dump vlet info to db
    status = dump_vlet_info(vlet_info);
    if (!status.ok()) {
        LOG(WARNING) << "alloc zonefile failed due to dump vlet info to db failed,"
                     << " file:" << zonefile->file_path() << " error:" << status;
        return status;
    }

    //update memory map.
    _vlet_info = vlet_info;
    set_max_file_sequence_id(sequence_id);
    bool ok = _zonefile_map.insert({file_index, zonefile}).second;
    assert(ok);
    if (is_rewrite) {
        check_seal(_curr_rewrite_file);
        _curr_rewrite_file = zonefile;
        _rewrite_zonefile_list.push_back(zonefile);
    } else {
        check_seal(_curr_write_file);
        _curr_write_file = zonefile;
        _raw_zonefile_list.push_back(zonefile);
    }

    // check used_size
    if (get_used_size_without_lock() > total_size()) {
        LOG(WARNING) << _name << " vlet used size is more than total size,"
                     << " used_size: " << get_used_size_without_lock()
                     << " total_size: " << total_size()
                     << " alloc_zonefile:" << zonefile->file_path();
    }

    g_engine_store_alloc_zonefile_num_adder << 1;
    return Status(AIE_OK);
}

Status AppendStore::write_record(const common::Buffer& record_buf, AppendRecordLocation* loc) {
    uint64_t start1 = base::gettimeofday_us();
    uint64_t start2;
    auto need_free_size = record_buf.size();
    Status write_status;
    {
        if (_curr_write_file == nullptr
                || !_curr_write_file->has_space2write(need_free_size)) {
            auto status = alloc_zonefile(false);
            if (status.code() != AIE_OK) {
                LOG(WARNING) << "write record failed due to alloc new write file failed,"
                             << " path:" << _path << " error:" << status;
                return status;
            }
        }
        start2 = base::gettimeofday_us();
        write_status = _curr_write_file->write(record_buf.buf(), record_buf.size(), loc);
        ARIES_LIKELY_IF(write_status.code() == AIE_OK) {
            _curr_write_file->add_used_size(record_buf.size());
        } else {
            LOG(WARNING) << "write record failed, file:" << _curr_write_file->file_path()
                         << " error:" << write_status;
            return write_status;
        }
    }
    uint64_t now = base::gettimeofday_us();
    g_engine_diskio_write_latency << now - start2;
    g_engine_record_write_latency << now - start1;
    return Status(AIE_OK);
}

Status AppendStore::update_meta(const ::aries::pb::AppendVletInfo& vlet_info) {
    std::unique_lock<std::mutex> lock_guard(_meta_mutex);;
    ::aries::pb::AppendVletInfo info;
    info.CopyFrom(_vlet_info);
    auto membership = info.mutable_membership();
    membership->CopyFrom(vlet_info.membership());
    info.set_rewrite_rate(vlet_info.rewrite_rate());
    info.set_daily_rewrite_start_time(vlet_info.daily_rewrite_start_time());
    info.set_daily_rewrite_duration_second(vlet_info.daily_rewrite_duration_second());
    info.set_last_finish_rewrite_time(vlet_info.last_finish_rewrite_time());
    info.mutable_shard_compress_option()->CopyFrom(vlet_info.shard_compress_option());
    auto status = dump_vlet_info(info);
    if (!status.ok()) {
        LOG(WARNING) << "update AppendStore meta failed due to dump vlet info failed,"
                     << " path:" << _path << " error:" << status;
        return status;
    } else {
        LOG(NOTICE) << "update AppendStore meta succeeded, path:" << _path;
    }
    _vlet_info = info;
    return status;
}

Status AppendStore::dump_vlet_info(const ::aries::pb::AppendVletInfo& vlet_info) {
    // write vlet info to db.
    VolumeKey volume_key;
    volume_key.set_volume_id(_volume_id);
    volume_key.set_vlet_type(_vlet_type);

    rocksdb::Slice key((const char*)&volume_key, kVolumeKeyLen);
    std::string string_pb;
    assert(vlet_info.SerializeToString(&string_pb));
    rocksdb::Slice value(string_pb);
    // dump_vlet_info can happen in 3 scenarios:
    // 1.alloc zonefile
    // 2.update meta
    // 3.remove zonefile
    // only impactful latencty growth occur in new zonefile allocation caused by user write
    rocksdb::WriteOptions opts;
    opts.sync = true;
    Status s = _db->Put(opts, key, value);
    if (!s.ok()) {
        LOG(WARNING) << "dump vlet info failed due to write db failed,"
                     << " path:" << _path << " error:" << s;
        return s;
    }
    return Status(AIE_OK);
}

void AppendStore::get_serialize_config_with_lock(
        aries::pb::ShardCompressOption* compress_option) {
    std::unique_lock<std::mutex> lock_guard(_meta_mutex);
    if (_shard_index < _vlet_info.space_info().k()) {
        compress_option->CopyFrom(_vlet_info.shard_compress_option());
    } else {
        compress_option->set_slice_split_size(_vlet_info.shard_compress_option().slice_split_size());
    }
}

Status AppendStore::do_serialize_record(
        const uint64_t vbid,
        const base::IOBuf& data,
        const aries::pb::ShardMeta& meta,
        const aries::pb::ShardCompressOption& compress_option,
        common::Buffer* result,
        aries::pb::SliceDescMeta* slice_desc_meta,
        void* args) {
    common::TimeMeasure time_measure(&g_engine_serialize_record_latency);
    Status s;
    if (_use_standard_record_layout) {
        common::SerializeArgs serialize_args;
        s = _standard_record_serializer->serialize(
                vbid,
                _shard_index,
                data,
                meta,
                compress_option,
                result,
                slice_desc_meta,
                reinterpret_cast<void*>(&serialize_args));
    } else {
        s = _append_record_serializer->serialize(
                vbid, data, meta, compress_option, result, slice_desc_meta, nullptr);
    }
    return s;
}

Status AppendStore::put(const uint64_t vbid, const pb::ShardMeta &meta, const base::IOBuf &data) {
    common::ScopedMutexLock lock(_write_record_mutex);
    uint64_t start = base::gettimeofday_us();
    //check has curr_write_file;
    AppendRecordLocation location;
    auto s = _indexer->get(vbid, &location);
    if (s.ok()) {
        //todo check crc.
        if (meta.has_blob_ttl_timestamp()) {
            auto status = update_ttl(vbid, meta.blob_ttl_timestamp());
            if (!status.ok()) {
                LOG(WARNING) << "put shard failed due to update blob ttl failed,"
                             << " vid:" << _volume_id << " vbid:" << vbid
                             << " shard_index:" << _shard_index << " error:" << status;
            }
        }
        LOG(WARNING) << "put shard failed due to vbid already exists, vid:" << _volume_id
                     << " vbid:" << " shard_index:" << _shard_index << vbid << " error:" << s;
        return Status(AIE_EXIST, "record already exist");
    }
    if (s.code() != AIE_BLOB_NOT_EXIST) {
        LOG(WARNING) << "put shard failed due to read indexer failed, vid:" << _volume_id
                     << " vbid:" << vbid << " shard_index:" << _shard_index << " error:" << s;
        return s;
    }

    aries::pb::ShardCompressOption compress_option;
    get_serialize_config_with_lock(&compress_option);

    //serialize record
    common::Buffer result;
    aries::pb::SliceDescMeta slice_desc_meta;
    s = do_serialize_record(vbid, data, meta, compress_option, &result, &slice_desc_meta, nullptr);
    if (!s.ok()) {
        LOG(WARNING) << "put shard failed due to serialize record failed,"
                     << " vid:" << _volume_id << " vbid:" << vbid
                     << " shard_index:" << _shard_index << " error:"<< s;
        return s;
    }

    s = do_put(vbid, meta, slice_desc_meta, result);
    if (!s.ok()) {
        return s;
    }

    g_engine_store_write_latency << base::gettimeofday_us() - start;

    return Status(AIE_OK);
}

Status AppendStore::do_put(
        const uint64_t vbid,
        const pb::ShardMeta &meta,
        const aries::pb::SliceDescMeta& slice_desc_meta,
        common::Buffer& record_buf) {
    if (record_buf.size() > FLAGS_max_append_data_size) {
        return Status(AIE_INVALID_ARGUMENT, "data too large");
    }

    AppendRecordLocation location;
    auto status = write_record(record_buf, &location);
    if (meta.has_blob_ttl_timestamp()) {
        location.blob_ttl_timestamp = meta.blob_ttl_timestamp();
    }

    if (!status.ok()) {
        LOG(WARNING) << "put shard failed due to write disk failed,"
                     << " vid:" << _volume_id << " vbid:" << vbid
                     << " shard_index:" << _shard_index << " error:" << status;
        return status;
    }
    location.slice_desc_meta.CopyFrom(slice_desc_meta);
    status = _indexer->put(vbid, location);
    if (!status.ok()) {
        LOG(WARNING) << "put shard faield due to update indexer failed,"
                     << " vid:" << _volume_id << " vbid:" << vbid
                     << " shard_index:" << _shard_index << " error:" << status;
        return status;
    }
    LOG(TRACE) << "put shard succeeded, vid:" << _volume_id << " vbid:" << vbid
               << " shard_index:" << _shard_index << " loc:" << location.loc_to_string();
               
    insert_snapshot_new_data(location);

    update_fingerprint(vbid);
    update_max_vbid(vbid);
    add_atomic_num(_record_num);
    g_engine_append_write_size_adder.put(record_buf.size());

    return Status(AIE_OK);
}

Status AppendStore::locate_zonefile(
        uint64_t vbid,
        AppendRecordLocation* location,
        ZonefilePtr* zonefile) {
    Status s = _indexer->get(vbid, location);
    if (!s.ok()) {
        if (s.code() == AIE_MARK_REMOVED) {
            LOG(DEBUG) << "locate zonefile failed due to vbid has been mark deleted,"
                       << " vid:" << _volume_id << " vbid:" << vbid;
        }

        LOG(WARNING) << "locate zonefile failed,"
                     << " vid:" << _volume_id << " vbid:" << vbid << " error:" << s;
        return s;
    }

    std::unique_lock<std::mutex> lock_guard(_meta_mutex);
    auto iter = _zonefile_map.find(location->file_index);
    // if delete zonefile after get_index,get index again
    if (iter == _zonefile_map.end() || !iter->second) {
        // get_index in readlock guarantee zonefile exist
        Status s = _indexer->get(vbid, location);
        if (!s.ok()) {
            LOG(WARNING) << "locate zonefile failed,"
                         << " vid:" << _volume_id << " vbid:" << vbid << " error:" << s;
            return s;
        }
        auto iter = _zonefile_map.find(location->file_index);
        assert(iter != _zonefile_map.end());
        assert(iter->second);
    }
    *zonefile = iter->second;
    return s;
}

Status AppendStore::parse_vlet_info(const common::Buffer& record_buf, aries::pb::AppendVletInfo* info) {
    uint64_t record_magic = *reinterpret_cast<uint64_t*>(record_buf.buf());
    if (record_magic == APPEND_RECORD_MAGIC) {
        return _append_record_serializer->parse_only_meta_record(record_buf, common::RECORD_VLET_INFO, info);
    } else if (record_magic == common::STANDARD_RECORD_MAGIC) {
        return _standard_record_serializer->parse_only_meta_record(record_buf, common::RECORD_VLET_INFO, nullptr, info);
    } else {
        LOG(FATAL) << "parse vlet info failed due to unknown record magic number,"
                   << " vid:" << _volume_id << " record_magic:" << record_magic;
        return Status(AIE_CORRUPT, "unknown record magic");
    }
}

Status AppendStore::do_deserialize_record(
        const common::Buffer& record_buf,
        uint64_t* vbid,
        aries::pb::SliceDescMeta* slice_desc_meta,
        pb::ShardMeta* meta,
        base::IOBuf* data) {
    common::TimeMeasure time_measure(&g_engine_deserialize_record_latency);
    uint64_t record_magic = *reinterpret_cast<uint64_t*>(record_buf.buf());
    Status s;
    if (record_magic == APPEND_RECORD_MAGIC) {
        s = _append_record_serializer->deserialize(record_buf, vbid, slice_desc_meta, meta, data);
    } else if (record_magic == common::STANDARD_RECORD_MAGIC) {
        uint32_t expected_shard_index = _shard_index;
        uint32_t actual_shard_index = -1;
        s = _standard_record_serializer->deserialize(record_buf, &actual_shard_index, vbid, slice_desc_meta, meta, data);
        if (actual_shard_index != expected_shard_index) {
            LOG(WARNING) << "deserialize record failed due to shard index inconsistent,"
                         << " vid:" << _volume_id << " expected_shard_index:" << expected_shard_index
                         << " actual_shard_index:" << actual_shard_index;
            return Status(AIE_SHARD_INDEX_INCONSISTENT, "shard index inconsistent");
        }
    } else {
        LOG(FATAL) << "deserialize record failed due to unknown record magic number,"
                   << " vid:" << _volume_id << " record_magic:" << record_magic;
        return Status(AIE_CORRUPT, "unknown record magic");
    }
    return s;
}

Status AppendStore::get(const uint64_t vbid, pb::ShardMeta *meta, base::IOBuf *data) {
    uint64_t start1 = base::gettimeofday_us();
    AppendRecordLocation location;
    ZonefilePtr zonefile;
    Status s = locate_zonefile(vbid, &location, &zonefile);
    if (!s.ok()) {
        return s;
    }
    if (location.is_expired()) {
        LOG(TRACE) << "get shard failed due to shard has been expired,"
                   << " vid:" << _volume_id << " vbid:" << vbid << " shard_index:" << _shard_index;
        return Status(AIE_BLOB_NOT_EXIST);
    }

    // only check location exist
    if (meta == NULL) {
        assert(data == NULL);
        return Status(AIE_OK);
    }

    s = do_get(vbid, location, zonefile, meta, data);
    g_engine_store_read_latency << base::gettimeofday_us() - start1;

    return s;
}

Status AppendStore::do_get(uint64_t vbid, AppendRecordLocation& location,
                           ZonefilePtr zonefile, pb::ShardMeta *meta, base::IOBuf *data) {
    // read record from zonefile
    auto size = location.len * _align_size;
    common::Buffer record_buf;
    record_buf.reset(size, _align_size);
    uint64_t start2 = base::gettimeofday_us();
    auto s = zonefile->read(location, &record_buf);
    if (!s.ok()) {
        LOG(WARNING) << "get shard failed due to read disk failed,"
                     << " vid:" << _volume_id << " vbid:" << vbid
                     << " shard_index:" << _shard_index << " error:" << s;
        return s;
    }
    uint64_t now = base::gettimeofday_us();
    g_engine_diskio_read_latency << now - start2;
    g_engine_record_read_latency << now - start2;

    // deserialize record
    uint64_t record_vbid = 0;
    s = do_deserialize_record(record_buf, &record_vbid, nullptr, meta, data);
    if (!s.ok()) {
        LOG(FATAL) << "get shard failed due to deserialize record failed,"
                   << " vid:" << _volume_id << " vbid:" << vbid
                   << " shard_index:" << _shard_index << " error:" << s;
        return s;
    }

    if (vbid != record_vbid) {
        LOG(FATAL) << "get shard failed due to record vbid inconsistent,"
                   << " vid:" << _volume_id << " shard_index:" << _shard_index
                   << " expected_vbid:" << vbid << " actual_vbid:" << record_vbid
                   << " loc:" << location << " file:" << zonefile->file_path();
        return Status(AIE_RECORD_META_INCONSISTENT, "record vbid inconsistent");
    }

    LOG(TRACE) << "get shard succeeded, vid:" << _volume_id << " vbid:" << vbid
               << " shard_index:" << _shard_index << " loc:" << location.loc_to_string();
    return Status(AIE_OK);
}

Status AppendStore::range_get(
            const uint64_t vbid,
            const uint32_t offset,
            const uint32_t len,
            aries::pb::ShardMeta* meta,
            base::IOBuf* data) {
    common::TimeMeasure time_measure(&g_engine_store_read_latency);

    common::Buffer slice_buf;
    aries::pb::SliceDescMeta slice_desc_meta;
    uint32_t data_offset_in_buf;
    auto s = do_range_get(vbid, offset, len, &slice_buf, &slice_desc_meta, &data_offset_in_buf);
    if (!s.ok()) {
        LOG(WARNING) << "range get shard failed,"
                     << " vid:" << _volume_id << " vbid:" << vbid
                     << " shard_index:" << _shard_index << " error:" << s;
        return s;
    }

    // has_slice_meta == true means standard layout is being using
    if (slice_desc_meta.has_slice_meta()) {
        s = _standard_record_serializer->range_deserialize(
                slice_buf, slice_desc_meta, data_offset_in_buf, offset, len, meta, data);
    } else {
        s = _append_record_serializer->range_deserialize(
                slice_buf, slice_desc_meta, data_offset_in_buf, offset, len, meta, data);
    }
    if (!s.ok()) {
        LOG(WARNING) << "range get shard failed due to range deserialize record failed,"
                     << " vid:" << _volume_id << " vbid:" << vbid << " shard_index:" << _shard_index
                     << " offset:" << offset << " length:" << len << " error:" << s;
        return s;
    }

    LOG(TRACE) << "range get shard succeeded, vid:" << _volume_id
               << " vbid:" << vbid << " shard_index:" << _shard_index
               << " offset:" << offset << " length:" << len;
    return Status(AIE_OK);
}

Status AppendStore::get_record_index_info(uint64_t vbid, aries::pb::RecordIndexInfo *record_index_info) {
    // set engine type
    record_index_info->set_engine_type(ENGINE_APPEND);
    // get record index info
    AppendRecordLocation location;
    ZonefilePtr zonefile;
    Status s = locate_zonefile(vbid, &location, &zonefile);
    if (!s.ok()) {
        if (s.code() == AIE_MARK_REMOVED) {
            LOG(DEBUG) << "get record index info failed due to it has been mark deleted,"
                       << " vid:" << _volume_id << " vbid:" << vbid;
            record_index_info->mutable_append_record_index_info()->set_delete_time(location.delete_time);
            return s;
        }

        LOG(WARNING) << "get record index info failed,"
                     << " vid:" << _volume_id << " vbid:" << vbid << " error:" << s;
        return s;
    }

    if (location.is_expired()) {
        LOG(TRACE) << "get record index info failed due to shard has been expired,"
                   << " vid:" << _volume_id << " vbid:" << vbid << " shard_index:" << _shard_index;
        return Status(AIE_BLOB_NOT_EXIST);
    }
    // set record index info
    auto pb_loc = record_index_info->mutable_append_record_index_info();
    location.serialize2pb(pb_loc);

    return s;
}

Status AppendStore::do_range_get(
        uint64_t vbid,
        uint32_t offset,
        uint32_t len,
        common::Buffer* range_get_buf,
        aries::pb::SliceDescMeta* slice_desc_meta,
        uint32_t* data_offset_in_buf) {
    AppendRecordLocation location;
    ZonefilePtr zonefile;
    auto s = locate_zonefile(vbid, &location, &zonefile);
    if (!s.ok()) {
        LOG(WARNING) << "range get shard failed due to locate zonefile failed,"
                     << " vid:" << _volume_id << " vbid:" << vbid
                     << " shard_index:" << _shard_index << " error:" << s;
        return s;
    }

    if (location.is_expired()) {
        LOG(TRACE) << "range get shard failed due to shard has been expired,"
                   << " vid:" << _volume_id << " vbid:" << vbid << " shard_index:" << _shard_index;
        return Status(AIE_BLOB_NOT_EXIST);
    }

    uint32_t header_len = sizeof(AppendRecordHead);
    slice_desc_meta->CopyFrom(location.slice_desc_meta);
    if (slice_desc_meta->has_layout_version()) {
        // since record data has been segmented and compressed, need to calculate offset and len
        header_len = sizeof(common::StandardRecordHeader);
        if (slice_desc_meta->record_data_len() == 0) {
            return Status(AIE_INVALID_ARGUMENT, "blob len is 0");
        }
        auto& slice_meta = slice_desc_meta->slice_meta();
        auto index_range = common::slice_index_range(slice_meta.split_size(), offset, len);
        if (index_range.second > (size_t)slice_meta.slice_num()) {
            return Status(AIE_INVALID_ARGUMENT, "invalid range");
        }
        if (slice_desc_meta->compress_type() == static_cast<int>(COMPRESS_TYPE_NONE)) {
            uint32_t slice_size =
                    sizeof(common::SliceHeader) + slice_meta.split_size() + sizeof(uint32_t);
            uint32_t end_offset;
            if (index_range.second == (size_t)slice_meta.slice_num()) {
                uint32_t origin_data_len = slice_desc_meta->record_data_len() -
                    (sizeof(common::SliceHeader) + sizeof(uint32_t)) * slice_meta.slice_num();
                if (offset + len > origin_data_len) {
                    LOG(WARNING) << "range get shard failed due to the range exceeds the original length,"
                                 << " vid:" << _volume_id << " shard_index:" << _shard_index
                                 << " offset:" << offset << " length:" << len
                                 << " origin_data_length:" << origin_data_len;
                    return Status(AIE_INVALID_ARGUMENT, "invalid non-compress record range");
                }
                end_offset = slice_desc_meta->record_data_len();
            } else {
                end_offset = slice_size * index_range.second;
            }
            offset = index_range.first * slice_size;
            len = end_offset - offset;
        } else {
            offset = slice_meta.sub_slices_offsets(index_range.first);
            len = slice_meta.sub_slices_offsets(index_range.second) - offset;
        }
    }

    //basic check if request range is valid;
    uint32_t data_range_end = header_len + offset + len;
    if (data_range_end > (location.len * _align_size - sizeof(uint32_t))) {
        LOG(WARNING) << "range get shard failed due to invalid range,"
                     << " vid:" << _volume_id << " vbid:" << vbid << " shard_index:" << _shard_index
                     << " offset:" << offset << " length:" << len;
        return Status(AIE_INVALID_ARGUMENT, "invalid range");
    }

    uint32_t data_offset = location.offset * _align_size + header_len + offset;
    uint32_t align_offset = data_offset / _align_size * _align_size;
    uint32_t data_end_offset = data_offset + len;
    uint32_t align_data_end_offset =
            (data_end_offset + _align_size - 1) / _align_size * _align_size;
    uint32_t read_len = align_data_end_offset - align_offset;

    uint64_t start = base::gettimeofday_us();
    range_get_buf->reset(read_len, _align_size);
    auto st = zonefile->read_range(
            align_offset / _align_size, read_len / _align_size, range_get_buf);
    if (!st.ok()) {
        LOG(WARNING) << "range get shard failed due to read zonefile failed,"
                     << " vid:" << _volume_id << " vbid:" << vbid << " shard_index:" << _shard_index
                     << " loc:" << location << " offset:" << align_offset
                     << " length:" << read_len << " error:" << st;
        return st;
    }
    *data_offset_in_buf = data_offset - align_offset;
    uint64_t now = base::gettimeofday_us();
    g_engine_diskio_read_latency << now - start;
    g_engine_record_read_latency << now - start;

    return st;
}

Status AppendStore::remove(uint64_t vbid, bool mark_delete) {
    uint32_t now = static_cast<uint32_t>(base::gettimeofday_s());
    return remove(vbid, mark_delete, now);
}

Status AppendStore::remove(uint64_t vbid, bool mark_delete, uint32_t mark_delete_timestamp) {
    base::get_leaky_singleton<RowLockManager>()->lock_row(_volume_id, vbid);

    AppendRecordLocation location;
    std::unique_lock<std::mutex> lock_guard(_meta_mutex);
    Status s = _indexer->get(vbid, &location);
    switch (s.code()) {
        case AIE_BLOB_NOT_EXIST: {
            s = Status();
            break;
        }

        case AIE_MARK_REMOVED: {
            if (mark_delete) {
                s = Status();
                break;
            }
            s = _indexer->remove(vbid);
            if (!s.ok()) {
                LOG(WARNING) << "finally delete shard failed due to remove indexer failed,"
                             << " vid:" << _volume_id << " vbid:" << vbid
                             << " shard_index:" << _shard_index << " error:" << s;
                break;
            }

            ZonefilePtr zonefile;
            {
                auto zonefile_iter = _zonefile_map.find(location.file_index);
                //TODO may happen when rewriting removes the zone file, should be fixed later
                if (zonefile_iter == _zonefile_map.end()) {
                    ARIES_FUNC_LOG(FATAL) << "remove vbid failed, zonefile is not found. vid:" << _volume_id
                            << " vbid:" << vbid;
                    return Status(AIE_FAIL);
                }
                assert(zonefile_iter != _zonefile_map.end());
                zonefile = zonefile_iter->second;
            }

            zonefile->del_used_size(location.len * _align_size);
            sub_atomic_num(_mark_deleted_record_num);
            s = Status();
            LOG(TRACE) << "finally delete shard succeeded, vid:" << _volume_id
                       << " vbid:" << vbid << " shard_index:" << _shard_index;
            break;
        }

        case AIE_OK: {
            if (!mark_delete) {
                s.set_code(AIE_FAIL, "could not finally delete normal shard");
                break;
            }

            location.delete_time = mark_delete_timestamp;
            s = _indexer->put(vbid, location);
            if (!s.ok()) {
                LOG(WARNING) << "mark delete shard failed due to update indexer failed,"
                             << " vid:" << _volume_id << " vbid:" << vbid
                             << " shard_index:" << _shard_index << " error:" << s;
            } else {
                update_fingerprint(vbid);
                sub_atomic_num(_record_num);
                add_atomic_num(_mark_deleted_record_num);
                LOG(TRACE) << "mark delete shard succeeded, vid:" << _volume_id
                           << " vbid:" << vbid << " shard_index:" << _shard_index;
            }
            break;
        }

        default:
            break;
    }

    base::get_leaky_singleton<RowLockManager>()->unlock_row(_volume_id, vbid);
    return s;
}

void AppendStore::update_fingerprint(uint64_t vbid) {
    _fingerprint_manager.update_fingerprint(vbid);
}

uint64_t AppendStore::get_fingerprint_by_slot(uint32_t slot_id) {
    return _fingerprint_manager.get_fingerprint_by_slot(slot_id);
}

Status AppendStore::check_self() {
    std::shared_ptr<rocksdb::Iterator> rocksdb_iter(_db->NewIterator(rocksdb::ReadOptions()));
    auto key = rocksdb::Slice((char *) &_volume_id, sizeof(_volume_id));

    std::vector<AppendRecordLocation> loc_list;
    for (rocksdb_iter->Seek(key); rocksdb_iter->Valid(); rocksdb_iter->Next()) {
        if (rocksdb_iter->key().size() != sizeof(RecordKey)) {
            continue;
        }
        RecordKey *key = (RecordKey *) rocksdb_iter->key().data();
        if (key->vid() != _volume_id) {
            break;
        }

        if (rocksdb_iter->value().size() == 0) {
            continue;
        }

        AppendRecordLocation loc;
        loc.parse_from_array((const void*)rocksdb_iter->value().data(), 
                rocksdb_iter->value().size());
        loc_list.push_back(loc);
    }
    auto cmp = [](const AppendRecordLocation& l, const AppendRecordLocation& r){
        if (l.file_index == r.file_index) {
            return l.offset < r.offset;
        }
        return l.file_index < r.file_index;
    };
    std::sort(loc_list.begin(), loc_list.end(), cmp);
    for (size_t i = 1; i < loc_list.size(); ++i) {
        auto& last_loc = loc_list[i - 1];
        auto& next_loc = loc_list[i];

        if (last_loc.file_index == next_loc.file_index) {
            if (last_loc.offset + last_loc.len > next_loc.offset) {
                LOG(FATAL) << "check self failed due to impossible location,"
                           << " last_loc:" << last_loc.loc_to_string()
                           << " next_loc:" << next_loc.loc_to_string()
                           << " path:" << _path;
                return Status(AIE_CORRUPT, "location list is overlapped");
            }
        }
    }
    
    std::set<uint64_t> disk_file_sequence_ids;
    base::FilePath dir(_path);
    base::FileEnumerator f_enum(dir, false, base::FileEnumerator::FILES);
    for (base::FilePath zonefile = f_enum.Next(); !zonefile.empty(); zonefile = f_enum.Next()) {
        std::string zonefile_basename = zonefile.BaseName().value();
        char *str_end = NULL;
        uint64_t file_sequence_id = strtoull(zonefile_basename.c_str(), &str_end, 10);
        if (str_end != (zonefile_basename.c_str() + zonefile_basename.size())) {
            LOG(TRACE) << "skip a non-zonefile, path:" << zonefile.value();
            continue;
        }
        disk_file_sequence_ids.insert(file_sequence_id);
    }

    {
        std::unique_lock<std::mutex> lock_guard(_meta_mutex);
        for (const auto& zonefile : _zonefile_map) {
            auto id = zonefile.second->file_sequence_id();
            if (disk_file_sequence_ids.find(id) == disk_file_sequence_ids.end()) {
                return Status(AIE_CORRUPT, "zonefile exists in meta but not on fs");
            }
        }
    }

    return Status();
}

Status AppendStore::update_ttl(uint64_t vbid, uint64_t blob_ttl_timestamp) {
    //common::ScopedMutexLock lock();
    AppendRecordLocation location;
    auto status = _indexer->get(vbid, &location);
    if (status.code() != AIE_OK) {
        LOG(WARNING) << "update shard ttl failed due to read indexer failed,"
                     << " vid:" << _volume_id << " vbid:" << vbid
                     << " shard_index:" << _shard_index << " error:" << status;
        return status;
    }

    location.blob_ttl_timestamp = blob_ttl_timestamp;
    status = _indexer->put(vbid, location);
    if (!status.ok()) {
        LOG(WARNING) << "update shard ttl failed due to write indexer failed,"
                     << " vid:" << _volume_id << " vbid:" << vbid
                     << " shard_index:" << _shard_index << " error:" << status;
    }
    return status;
}

void AppendStore::init_snapshot(uint64_t last_time, int rewrite_rate) {
    {
        std::unique_lock<std::mutex> lock_guard(_meta_mutex);;
        for (auto& zonefile : _zonefile_map) {
            if (rewrite_rate >= 0) {
                if (!zonefile.second->is_seal()) {
                    continue;
                }
                if (zonefile.second->create_time() > last_time) {
                    continue;
                }
                auto hole_size = zonefile.second->hole_size();
                int rate = hole_size * 100 / _zonefile_size;
                if (rate < rewrite_rate) {
                    continue;
                }
            }

            auto file_index = zonefile.second->file_index();
            auto sequence_id = zonefile.second->file_sequence_id();
            auto rewrite_generation = zonefile.second->rewrite_generation();

            auto zl_ptr = ZonefileLocationPtr(new ZonefileLocation(sequence_id,
                        rewrite_generation, file_index));
            auto ok = _snapshot.z_map.insert({file_index, zl_ptr}).second;
            assert(ok);
            _snapshot.z_vector.push_back(zl_ptr);
        }
    }

    auto cmp = [](const ZonefileLocationPtr& l, const ZonefileLocationPtr& r){
        if (l->rewrite_generation == r->rewrite_generation) {
            return l->file_sequence_id < r->file_sequence_id;
        }
        return l->rewrite_generation < r->rewrite_generation;
    };

    std::sort(_snapshot.z_vector.begin(), _snapshot.z_vector.end(), cmp);

    //scan db
    std::shared_ptr<rocksdb::Iterator> rocksdb_iter(_db->NewIterator(rocksdb::ReadOptions()));
    auto key = rocksdb::Slice((char *) &_volume_id, sizeof(_volume_id));

    for (rocksdb_iter->Seek(key); rocksdb_iter->Valid(); rocksdb_iter->Next()) {
        if (rocksdb_iter->key().size() != sizeof(RecordKey)) {
            continue;
        }
        RecordKey *key = (RecordKey *) rocksdb_iter->key().data();
        if (key->vid() != _volume_id) {
            break;
        }

        if (rocksdb_iter->value().size() == 0) {
            continue;
        }

        AppendRecordLocation loc;
        loc.parse_from_array((const void*)rocksdb_iter->value().data(),
                rocksdb_iter->value().size());

        auto z_iter = _snapshot.z_map.find(loc.file_index);
        if (z_iter == _snapshot.z_map.end()) {
            LOG(TRACE) << "skip a zonefile due to its index is not in snapshot,"
                       << " file_index:" << loc.file_index << " vid:" << _volume_id;
            continue;
        }
        auto ok = (z_iter->second->offset_map.insert({loc.offset, loc})).second;
        assert(ok);
    }
    LOG(NOTICE) << "finish init snapshot, vid:" << _volume_id << " path:" << _path;
}

Status AppendStore::get_first_loc_from_req(const AppendRecordLocation& req_loc,
                              AppendRecordLocation* first_loc) {
    const std::vector<ZonefileLocationPtr>& z_vector = _snapshot.z_vector;
    auto zvector_size = z_vector.size();
    *first_loc = req_loc;
    auto& begin_file_index = first_loc->file_index;
    if (zvector_size == 0) {
        return Status(AIE_EMPTY, "zvector null");
    }
    if (first_loc->file_index == UINT16_MAX) {
        first_loc->file_index = z_vector.front()->file_index;
    }
    bool is_last_location = false;
    for (size_t i = 0; i < zvector_size; ++i) {
        auto& zonefile_location = z_vector[i];
        if (zonefile_location->file_index == begin_file_index) {
            auto iter = zonefile_location->offset_map.upper_bound(first_loc->offset);
            if (iter == zonefile_location->offset_map.end()) {
                if (i + 1 < zvector_size) {
                    begin_file_index = z_vector[i + 1]->file_index;
                    first_loc->offset = 0;
                    continue;
                }
                is_last_location = true;
                break;
            }
            first_loc->offset = iter->second.offset;
            return Status(AIE_OK);
        }
    }
    if (is_last_location) {
        return Status(AIE_EMPTY);
    } else {
        return Status(AIE_INVALID, "location not found");
    }
}

Status AppendStore::batch_get_record(const AppendRecordLocation& req_loc, 
                        std::vector<AppendRecordLocation>* loc_list,
                        base::IOBuf *data) {
    // keep snapshot valid
    auto index = req_loc.file_index;
    AppendRecordLocation first_loc;
    //find real first_loc;
    Status s;
    uint16_t file_index = 0;
    bool is_last_finish = false;
    bool is_partial_finish = false;
    {
        common::ScopedMutexLock snapshot_lock(_snapshot_mutex);
        const std::vector<ZonefileLocationPtr>& z_vector = _snapshot.z_vector;
        update_snapshot_time();
        s = get_first_loc_from_req(req_loc, &first_loc);

        if (s.code() == AIE_EMPTY) {
            LOG(NOTICE) << "batch get record failed due to could not"
                        << " find locations after requested location,"
                        << " vid:" << _volume_id << " loc:" << req_loc
                        << " path:" << _path << " error:" << s;
            return s;
        } else if (s.code() == AIE_INVALID) {
            LOG(NOTICE) << "batch get record failed due to requested"
                        << " location is invalid,"
                        << " vid:" << _volume_id << " loc:" << req_loc
                        << " path:" << _path << " error:" << s;
            return s;
        }
        
        file_index = first_loc.file_index;
        
        auto end_offset = first_loc.offset + 
            (FLAGS_append_vlet_batch_get_kb * 1024 + _align_size - 1) / _align_size;
        end_offset += (end_offset == first_loc.offset);
        for (auto& zonefile_location : z_vector) {
            if (zonefile_location->file_index == first_loc.file_index) {
                auto offset = first_loc.offset;
                auto& offset_map = zonefile_location->offset_map;
                auto iter = offset_map.lower_bound(offset);
                while (offset < end_offset && iter != offset_map.end()) {
                    offset = iter->second.offset;
                    loc_list->push_back(iter->second);
                    ++iter;
                }
                if (iter == offset_map.end()) {
                    is_partial_finish = true;
                }
                break;
            }
        }
        if (is_partial_finish && file_index == z_vector.back()->file_index) {
            is_last_finish = true;
        }
    }

    ZonefilePtr zonefile;
    {
        std::unique_lock<std::mutex> lock_guard(_meta_mutex);
        auto zonefile_iter = _zonefile_map.find(file_index);
        if (zonefile_iter == _zonefile_map.end()) {
            LOG(FATAL) << "batch get record failed due to zonefile index could not be found,"
                       << " vid:" << _volume_id << " file_index:" << file_index << " path:" << _path;
            return Status(AIE_INVALID);
        }
        zonefile = zonefile_iter->second;
    }

    s = zonefile->read_batch(*loc_list, data);
    if (!s.ok()) {
        LOG(WARNING) << "batch get record failed due to read zonefile failed,"
                     << " vid:" << _volume_id << " file:" << zonefile->file_path() << " error:" << s;
        return Status(AIE_IO_ERROR);
    }
    LOG(TRACE) << "batch get record succeeded, vid:" << _volume_id
               << " total_len:" << data->size() << " path:" << _path;
    if (is_last_finish) {
        return Status(AIE_EMPTY);
    } else if (is_partial_finish) {
        return Status(AIE_PARTIAL_EMPTY);
    }
    return Status(AIE_CONTINUE);
}

Status AppendStore::write_batch_in_a_zonefile(
            const std::vector<std::pair<uint64_t, AppendRecordLocation>>& loc_list,
            const common::Buffer& buffer, const bool is_rewrite) {
    LOG(TRACE) << "write batch in one zonefile, buffer_size:" << buffer.size()
               << " is_rewrite:" << is_rewrite << " path:" << _path;
    std::vector<AppendRecordLocation> new_loc_list;
    std::vector<uint64_t> vbid_list;

    if (!is_rewrite) {
        auto status = _curr_write_file->write_batch(loc_list, buffer, &new_loc_list, &vbid_list);
        if (!status.ok()) {
            LOG(WARNING) << "write batch failed due to write zonefile failed,"
                    << " is_rewrite:" << is_rewrite << " file:" << _curr_write_file->file_path()
                    << " error:" << status;
            return status;
        }
        _curr_write_file->add_used_size(buffer.size());
    } else {
        auto status = _curr_rewrite_file->write_batch(loc_list, buffer, &new_loc_list, &vbid_list);
        if (!status.ok()) {
            LOG(WARNING) << "write batch failed due to write zonefile failed,"
                    << " is_rewrite:" << is_rewrite << " file:" << _curr_rewrite_file->file_path()
                    << " error:" << status;
            return status;
        }
        _curr_rewrite_file->add_used_size(buffer.size());
    }

    assert(new_loc_list.size() == vbid_list.size());

    if (!is_rewrite) {
        auto status = _indexer->put_batch(vbid_list, new_loc_list);
        if (!status.ok()) {
            LOG(WARNING) << "write batch failed due to write indexer failed,"
                    << " is_rewrite:" << is_rewrite << " path:" << _path << " error:" << status;
            return status;
        }
        for (size_t i = 0; i < vbid_list.size(); ++i) {
            update_stat(new_loc_list[i], vbid_list[i]);
        }
    } else {
        // Make sure this is a useless branch
        assert(0);
        for (size_t i = 0; i < vbid_list.size(); ++i) {
            auto status = _indexer->rewrite(vbid_list[i], loc_list[i].second, new_loc_list[i]);
            if (status.code() != AIE_OK) {
                return status;
            }
        }
    }
    return Status();
}

Status AppendStore::batch_put_record(const std::vector<aries::pb::CopyVletRecordMeta>& record_meta_list,
            base::IOBuf* iobuf) {
    aries::pb::ShardCompressOption compress_option;
    get_serialize_config_with_lock(&compress_option);

    std::vector<std::pair<uint64_t, AppendRecordLocation>> loc_list;
    base::IOBuf new_buf;
    for (const auto& record_meta : record_meta_list) {
        base::IOBuf data;
        auto& meta = record_meta.shard_meta();
        assert(iobuf->cutn(&data, meta.shard_len()) == meta.shard_len());
        common::Buffer result;
        aries::pb::SliceDescMeta slice_desc_meta;
        auto s = do_serialize_record(record_meta.vbid(),
                    data,
                    meta,
                    compress_option,
                    &result,
                    &slice_desc_meta,
                    nullptr);
        if (!s.ok()) {
            LOG(WARNING) << "batch put record failed due to serialize record failed,"
                    << " vid:" << _volume_id << " vbid:" << record_meta.vbid()
                    << " shard_index:" << _shard_index << " error:" << s;
            return s;
        }
        AppendRecordLocation loc;
        loc.len = result.size() / _align_size;
        if (record_meta.has_mark_deleted_timestamp() && record_meta.mark_deleted_timestamp() > 0) {
            loc.delete_time = record_meta.mark_deleted_timestamp();
        }
        loc.slice_desc_meta.CopyFrom(slice_desc_meta);
        loc_list.emplace_back(record_meta.vbid(), std::move(loc));
        new_buf.append(result.buf(), result.size());
    }

    return batch_write_record(loc_list, new_buf, false);
}

Status AppendStore::batch_write_record(
        const std::vector<std::pair<uint64_t, AppendRecordLocation>>& loc_list,
        base::IOBuf& data,
        const bool is_rewrite) {
    common::ScopedMutexLock lock(_write_record_mutex);
    int end = loc_list.size();
    int len = 0;
    auto left_size = get_free_size(is_rewrite);
    int pos = 0;

    for (size_t i = 0; i < loc_list.size(); ++i) {
        if (left_size < (len + loc_list[i].second.len) * _align_size) {
            assert(pos == 0);
            pos = i;
            if (pos > 0) {
                std::vector<std::pair<uint64_t, AppendRecordLocation>> sub_loc_list(loc_list.begin(),
                        loc_list.begin() + pos);
                common::Buffer buffer(len * _align_size, _align_size);
                assert(data.cutn(buffer.buf(), buffer.size()) == buffer.size());
                auto status = write_batch_in_a_zonefile(sub_loc_list, buffer, is_rewrite);
                if (!status.ok()) {
                    LOG(WARNING) << "batch put record failed due to write batch failed,"
                                 << " vid:" << _volume_id << " shard_index:" << _shard_index << " error:" << status;
                    return status;
                }
            }

            auto status = alloc_zonefile(is_rewrite);
            if (status.code() != AIE_OK) {
                LOG(WARNING) << "batch put record failed due to alloc new write file failed,"
                             << " vid:" << _volume_id << " shard_index:" << _shard_index << " error:" << status;
                return status;
            }
            left_size = get_free_size(is_rewrite);
            len = 0;
        }
        len += loc_list[i].second.len;
    }

    std::vector<std::pair<uint64_t, AppendRecordLocation>> sub_loc_list(loc_list.begin() + pos,
            loc_list.end());
    common::Buffer buffer(len * _align_size, _align_size);
    assert(data.cutn(buffer.buf(), buffer.size()) == buffer.size());

    auto status = write_batch_in_a_zonefile(sub_loc_list, buffer, is_rewrite);
    if (!status.ok()) {
        LOG(WARNING) << "batch put record failed due to write batch failed,"
                     << " vid:" << _volume_id << " shard_index:" << _shard_index << " error:" << status;
        return status;
    }

    LOG(TRACE) << "batch put record succeeded, vid:" << _volume_id
               << " shard_index:" << _shard_index << " buffer_size:" << buffer.size();
    return Status();
}

Status AppendStore::restore(uint64_t vbid) {
    //common::ScopedMutexLock lock(_meta_mutex);

    base::get_leaky_singleton<RowLockManager>()->lock_row(_volume_id, vbid);
    AppendRecordLocation location;
    auto status = _indexer->get(vbid, &location);

    if (status.code() != AIE_MARK_REMOVED) {
        if (status.code() != AIE_OK) {
            LOG(WARNING) << "restore shard failed due to shard is not mark deleted,"
                         << " vid:" << _volume_id << " vbid:" << vbid
                         << " shard_index:" << _shard_index << " error:" << status;
        }
        base::get_leaky_singleton<RowLockManager>()->unlock_row(_volume_id, vbid);
        return status;
    }

    location.delete_time = 0;
    status = _indexer->put(vbid, location);
    if (!status.ok()) {
        LOG(WARNING) << "restore shard failed due to update indexer failed,"
                     << " vid:" << _volume_id << " vbid:" << vbid
                     << " shard_index:" << _shard_index << " error:" << status;
    } else {
        update_fingerprint(vbid);
        update_max_vbid(vbid);
    }

    base::get_leaky_singleton<RowLockManager>()->unlock_row(_volume_id, vbid);

    // RestoreShard is a kind of rare operation, here we print a WARNING log.
    LOG(WARNING) << "restore shard succeeded, vid:" << _volume_id << " vbid:" << vbid
                 << " shard_index:" << _shard_index << " loc:" << location.loc_to_string();
    return status;
}

void AppendStore::remove_zonefile(uint16_t file_index) {
    std::string path;

    {
        std::unique_lock<std::mutex> lock_guard(_meta_mutex);;
        auto zonefile_iter = _zonefile_map.find(file_index);
        assert(zonefile_iter != _zonefile_map.end());
        auto zonefile = zonefile_iter->second;
        path = zonefile->file_path();
        LOG(TRACE) << " remove file:" << path << " file_index:" << file_index;

        ::aries::pb::AppendVletInfo vlet_info;
        vlet_info.CopyFrom(_vlet_info);
        vlet_info.clear_raw_zonefile_list();
        vlet_info.clear_rewrite_zonefile_list();

        std::vector<ZonefilePtr> new_raw_zonefile_list;
        for (auto& iter : _raw_zonefile_list) {
            if (iter->file_index() != file_index) {
                new_raw_zonefile_list.push_back(iter);
                auto raw_zonefile_info = vlet_info.add_raw_zonefile_list();
                iter->serialice2pb(raw_zonefile_info);
            }
        }
        std::vector<ZonefilePtr> new_rewrite_zonefile_list;
        for (auto& iter : _rewrite_zonefile_list) {
            if (iter->file_index() != file_index) {
                new_rewrite_zonefile_list.push_back(iter);
                auto rewrite_zonefile_info = vlet_info.add_rewrite_zonefile_list();
                iter->serialice2pb(rewrite_zonefile_info);
            }
        }

        //write db first.
        auto status = dump_vlet_info(vlet_info);
        if (status.code() != AIE_OK) {
            LOG(WARNING) << "dump vlet info failed when remove zonefile:" << path
                         << " error:" << status;
            return;
        }
        _vlet_info = vlet_info;
        zonefile->set_should_remove(true);       
        _zonefile_map.erase(file_index);
        _raw_zonefile_list.swap(new_raw_zonefile_list);
        _rewrite_zonefile_list.swap(new_rewrite_zonefile_list);
    }

}

//TODO add UT
Status AppendStore::get_rewrite_info(const RewriteOptions& opt, RewriteInfo* info) {
    Status status;
    uint64_t log_id = base::fast_rand();

    aries::pb::AppendVletInfo vlet_info;
    {
        std::unique_lock<std::mutex> lock_guard(_meta_mutex);;
        vlet_info.CopyFrom(_vlet_info);
    }

    if (!opt.is_force_rewrite) {
        time_t now_timestamp = time(nullptr);
        struct tm time_tm;
        localtime_r(&now_timestamp, &time_tm);
        uint32_t daily_time_second = time_tm.tm_sec 
                    + time_tm.tm_min * common::MIN_SECOND 
                    + time_tm.tm_hour * common::HOUR_SECOND;
        if (daily_time_second < vlet_info.daily_rewrite_start_time()) {
            daily_time_second += common::DAY_SECOND;
        }
        uint32_t daily_rewrite_duration_second = std::max(vlet_info.daily_rewrite_duration_second(), opt.daily_rewrite_duration_second);
        
        if (daily_time_second - vlet_info.daily_rewrite_start_time() >= daily_rewrite_duration_second) {
            ARIES_RPC_DEBUG_LOG(TRACE) << "skip get rewrite info, vid:" << _volume_id
                    << " daily_rewrite_duration_second:" << daily_rewrite_duration_second 
                    << " daily_time_second:" << daily_time_second 
                    << " daily_rewrite_start_time:" << vlet_info.daily_rewrite_start_time()
                    << " vlet_info_daily_rewrite_duration_second:" << vlet_info.daily_rewrite_duration_second();
            return Status(AIE_EXCEED_LIMIT);
        }
    }


    uint64_t before_size = 0;
    uint64_t after_size = 0;

    uint64_t vlet_rewrite_rate = 0;
    {
        std::unique_lock<std::mutex> lock_guard(_meta_mutex);
        vlet_rewrite_rate = vlet_info.rewrite_rate();
        for (auto& iter : _zonefile_map) {
            auto& zonefile = iter.second;

            if (!zonefile->is_seal()) {
                continue;
            }

            auto hole_size = zonefile->hole_size();
            uint32_t rate = hole_size * 100 / _zonefile_size;
            if (rate < std::max(vlet_info.rewrite_rate(), opt.append_zone_rewrite_rate)) {
                ARIES_RPC_DEBUG_LOG(TRACE) << "skip zone, zone_seq_id:" << zonefile->file_sequence_id()
                    << " hole:" << hole_size << " rate:" << rate << " vlet_rate:" << vlet_info.rewrite_rate();
                continue;
            }
            before_size += _zonefile_size;
            assert(_zonefile_size >= hole_size);
            after_size += (_zonefile_size - hole_size);
        }
    }
 
    info->before_rewrite_size = before_size;
    info->after_rewrite_size = common::calc_align(after_size, _zonefile_size);
    info->writable_size = free_size();
    info->last_finish_rewrite_time = vlet_info.last_finish_rewrite_time();
    info->vlet_force_rewrite_duration_second = vlet_info.force_rewrite_interval_second();

    ARIES_RPC_DEBUG_LOG(TRACE) << "get rewrite info done, vid:" << _volume_id
               <<" writable_size:" << free_size()
               <<" after:" << info->after_rewrite_size
               <<" before:" << info->before_rewrite_size
               <<" last_finished_rewrite_ts:" << info->last_finish_rewrite_time
               <<" is_force_rewrite:" << opt.is_force_rewrite
               <<" rewrite_rate:" << vlet_rewrite_rate
               <<" opt_rewrite_rate:" << opt.append_zone_rewrite_rate;

    return AIE_OK;
}

Status AppendStore::get_zonefiles_detail(pb::ZonefilesDetail* info) {
    std::unique_lock<std::mutex> lock_guard(_meta_mutex);
    for (auto& iter : _zonefile_map) {
        auto zonefile_info = info->add_zonefile_infos();
        auto& zonefile = iter.second;
        zonefile_info->set_is_seal(zonefile->is_seal());
        zonefile_info->set_hole_size(zonefile->hole_size());
        zonefile_info->set_used_size(zonefile->used_size());
        zonefile_info->set_total_size(_zonefile_size);
        zonefile_info->set_create_time(zonefile->create_time());
        zonefile_info->set_file_path(zonefile->file_path());
        zonefile_info->set_file_index(zonefile->file_index());
        zonefile_info->set_file_sequence_id(zonefile->file_sequence_id());
        zonefile_info->set_rewrite_generation(zonefile->rewrite_generation());
        zonefile_info->set_write_location(zonefile->write_location());
        LOG(TRACE) << "get zone info:" << zonefile->to_string();
    }
    return Status();
}

Status AppendStore::check_db() {
    if (_indexer->has_volume_key()) {
        return Status(AIE_FAIL, "has volume key");
    }
    if (_indexer->has_shard()) {
        return Status(AIE_FAIL, "has shard key");
    }
    return Status();
}

Status AppendStore::recover_index() {
    AppendShardScanner sc(this);
    auto s = sc.restore();
    if (s.ok()) {
        _vlet_info.CopyFrom(sc.vlet_info());
    }
    return s;
}

bool AppendStoreIndexer::has_volume_key() {
    VolumeKey volume_key;
    volume_key.set_volume_id(_vid);
    volume_key.set_vlet_type(_type);
    rocksdb::Slice key((const char*)&volume_key, kVolumeKeyLen);
    std::string info;
    rocksdb::Status s = _db->Get(rocksdb::ReadOptions(), key, &info);
    return s.ok();
}

bool AppendStoreIndexer::has_shard() {
    std::shared_ptr<rocksdb::Iterator> iter(_db->NewIterator(rocksdb::ReadOptions()));
    IndexKey begin;
    begin.set_volume_id(_vid);
    begin.set_vbid(0);
    rocksdb::Slice begin_key((char*)&begin, kIndexKeyLen);
    iter->Seek(begin_key);

    for (; iter->Valid(); iter->Next()) {
        if (iter->key().size() != sizeof(IndexKey)) {
            continue;
        }

        auto key = *(IndexKey*)(iter->key().data());
        if (key.volume_id() != _vid) {
            break;
        }	

        if (iter->value().size() == 0) {
            continue;
        }

        return true;
    }

    return false;
}

void AppendStore::insert_snapshot_new_data(AppendRecordLocation& loc) {
    if (_snapshot.snapshot_id != 0) {
        common::ScopedMutexLock lock(_snapshot_mutex);
        auto itr = _snapshot.z_map.find(loc.file_index);
        ZonefileLocationPtr ptr = nullptr;
        if (itr == _snapshot.z_map.end()) {
            auto findex = loc.file_index;
            std::unique_lock<std::mutex> lock_guard(_meta_mutex);
            auto zfile = _zonefile_map.find(findex);
            assert(zfile != _zonefile_map.end());
            auto zl_ptr = ZonefileLocationPtr(new ZonefileLocation(zfile->second->file_sequence_id(),
                        zfile->second->rewrite_generation(), zfile->second->file_index()));
            auto ok = (_snapshot.z_map.insert({findex, zl_ptr})).second;
            assert(ok);
            _snapshot.z_vector.push_back(zl_ptr);
            ptr = zl_ptr;
        } else {
            ptr = itr->second;
        }
        auto ok = (ptr->offset_map.insert({loc.offset, loc})).second;
        assert(ok);
    }
}

Status AppendStore::batch_get_segment(GetSegmentContext* ctx) {
    auto response = ctx->response;
    auto segment = ctx->segment;
    auto log_id = ctx->log_id;
    auto batch_info = (AppendBatchRecord*)(segment->ext);
    auto file_index = batch_info->file_index;
    AppendRecordLocation loc;
    ZonefilePtr zonefile;
    do {
        {
            std::unique_lock<std::mutex> lock_guard(_meta_mutex);
            auto zonefile_iter = _zonefile_map.find(file_index);
            if (zonefile_iter == _zonefile_map.end()) {
                break;
            }
            zonefile = zonefile_iter->second;
        }

        ++segment->real_io_count;
        auto begin = batch_info->offset;
        auto len = batch_info->len;
        if ((begin + len) * _align_size > zonefile->total_file_size()) {
            len = (zonefile->total_file_size() - begin * _align_size) / _align_size;
        }
        common::Buffer buffer(len * _align_size, _align_size);
        auto s = zonefile->read_range(begin, len, &buffer);
        if (!s.ok()) {
            LOG(WARNING) << "batch get segment failed due to range read zonefile failed,"
                         << " vid:" << _volume_id << " shard_index:" << _shard_index
                         << " offset:" << begin << " length:" << len 
                         << " file:" << zonefile->file_path() << " error:" << s;
            // maybe zonefile is changed after rewrite, so just try get vbid one by one
            break;
        }

        LOG(TRACE) << "batch get segment succeeded, vid:" << _volume_id
                   << " shard_index:" << _shard_index
                   << " total_len:" << buffer.size()
                   << " file:" << zonefile->file_path();
        uint32_t curr_off = 0;
        while (curr_off < buffer.size()) {
            bool is_record_data_type = true;
            uint32_t record_len;
            uint64_t record_magic = *reinterpret_cast<uint64_t*>(buffer.buf() + curr_off);
            if (record_magic == APPEND_RECORD_MAGIC) {
                const AppendRecordHead& header = *reinterpret_cast<AppendRecordHead*>(buffer.buf() + curr_off);
                if (header.record_type !=  common::RECORD_DATA) {
                    is_record_data_type = false;
                }
                record_len = header.record_len;
            } else if (record_magic == common::STANDARD_RECORD_MAGIC) {
                const common::StandardRecordHeader& header = *reinterpret_cast<common::StandardRecordHeader*>(buffer.buf() + curr_off);
                uint32_t raw_size = sizeof(common::StandardRecordHeader) + header.record_body_desc_info.record_meta_len
                        + header.record_body_desc_info.record_data_len + sizeof(uint32_t);
                record_len = common::calc_align(raw_size, header.record_align_size());
            } else {
                curr_off += _align_size;
                continue;
            }
            curr_off += record_len;
            if (buffer.size() < curr_off) {
                // not a complete record
                break;
            }
            //check crc
            uint32_t* record_crc =
                (uint32_t*)(buffer.buf() + curr_off - sizeof(uint32_t));
            uint32_t calc_crc =
            base::crc32c::Value(buffer.buf() + curr_off - record_len, record_len - sizeof(uint32_t));
            if (*record_crc != calc_crc) {
                LOG(FATAL) << "batch get segment failed due to crc inconsisitency detected,"
                           << " vid:" << _volume_id << " shard_index:" << _shard_index
                           << " expected_crc:" << *record_crc << " actual_crc:" << calc_crc
                           << " file:" << zonefile->file_path();
                segment->has_checksum_error = true;
                continue;
            }
            if (is_record_data_type) {
                common::Buffer curr_record(buffer.buf() + curr_off - record_len, record_len);
                uint64_t vbid = 0;
                ::aries::pb::ShardMeta meta;
                base::IOBuf data;
                s = do_deserialize_record(curr_record, &vbid, nullptr, &meta, &data);
                // need to release management of the buf
                curr_record.release();
                if (!s.ok()) {
                    LOG(FATAL) << "batch get segment failed due to deserialize record failed,"
                               << " vid:" << _volume_id << " shard_index:" << _shard_index
                               << " file:" << zonefile->file_path() << " error:" << s;
                    segment->has_checksum_error = true;
                    continue;
                }
                if (batch_info->vbids.find(vbid) == batch_info->vbids.end()) {
                    // not need the vbid
                    continue;
                }
                auto shard_info = response->add_shard_info();
                shard_info->set_vbid(vbid);
                shard_info->mutable_status()->set_code(AIE_OK);
                shard_info->mutable_shard_meta()->CopyFrom(meta);
                ctx->cntl->response_attachment().append(data);
                batch_info->vbids.erase(vbid);
                segment->data_size += meta.shard_len();
            }
        }
    } while (false);

    // get left vbid
    for (auto iter = batch_info->vbids.begin(); iter != batch_info->vbids.end();) {
        auto vbid = iter->first;
        aries::pb::ShardMeta meta;
        base::IOBuf data;
        auto s = get(vbid, &meta, &data);
        ++segment->real_io_count;
        if (s.code() == AIE_IO_ERROR) {
            response->mutable_status()->set_code(AIE_IO_ERROR);
            return s;
        } else if (s.code() == AIE_OK) {
            auto shard_info = response->add_shard_info();
            shard_info->set_vbid(vbid);
            shard_info->mutable_status()->set_code(AIE_OK);
            shard_info->mutable_shard_meta()->CopyFrom(meta);
            ctx->cntl->response_attachment().append(data);
            batch_info->vbids.erase(iter++);
            segment->data_size += meta.shard_len();
        } else if (s.code() == AIE_SHARD_INDEX_INCONSISTENT) {
            batch_info->vbids.erase(iter++);
        } else {
            auto shard_info = response->add_shard_info();
            shard_info->set_vbid(vbid);
            shard_info->mutable_status()->set_code(s.code());
            batch_info->vbids.erase(iter++);
            if (s.code() == AIE_CHECKSUM) {
                segment->has_checksum_error = true;
            }
        }
    }
    return Status();
}

void AppendStore::update_stat(AppendRecordLocation& loc, uint64_t vbid) {
    if (!loc.is_delete()) {
        update_fingerprint(vbid);
        add_atomic_num(_record_num);
    } else {
        add_atomic_num(_mark_deleted_record_num);
    }
    update_max_vbid(vbid);
}

#if defined(_CHECK_TEST) || defined(_UNIT_TEST)
std::string AppendStore::rand_str_for_test(const int len) {
    std::string ret;
    for (int i = 0; i < len; ++i) {
        ret += char(base::fast_rand() % 256);
    }
    return ret;
}

Status AppendStore::put_for_test(uint64_t vbid, const pb::ShardMeta &meta, base::IOBuf &data, int op) {
    //check has curr_write_file;
    AppendRecordLocation location;
    if (data.size() != meta.shard_len()) {
        LOG(WARNING) << "put shard failed due to data size not match,"
                << " vid:" << _volume_id << " vbid:" << vbid
                << " shard_index:" << _shard_index
                << " actual_data_size:" << data.size()
                << " meta_shard_len:" << meta.shard_len();
        return Status(AIE_INVALID_ARGUMENT, "size not match");
    }

    aries::pb::ShardCompressOption compress_option;
    get_serialize_config_with_lock(&compress_option);
    aries::pb::SliceDescMeta slice_desc_meta;

    common::Buffer result;
    Status status = do_serialize_record(
                    vbid,
                    data,
                    meta,
                    compress_option,
                    &result,
                    &slice_desc_meta,
                    nullptr);
    if (!status.ok()) {
        LOG(WARNING) << "put shard failed due to serialize record failed,"
                     << " vid:" << _volume_id << " vbid:" << vbid
                     << " shard_index:" << _shard_index << " error:" << status;
        return status;
    }

    if (result.size() > FLAGS_max_append_data_size) {
        return Status(AIE_INVALID_ARGUMENT, "data too large");
    }

    uint32_t header_size = _use_standard_record_layout ? sizeof(common::StandardRecordHeader) : sizeof(AppendRecordHead);
    if (op == 0) {
        auto buf = const_cast<char*>(result.buf() + header_size);
        *(buf + 2) += char(1);
    } else if (op == 1) {
        auto buf = const_cast<char*>(result.buf() + header_size);
        *(buf - 2) += char(1);
    } else if (op == 7) {
        for (int i = 0; i < 10; ++i) {
            *(result.buf() + header_size + i) += 1;
        }
    }

    status = write_record(result, &location);
    if (!status.ok()) {
        LOG(WARNING) << "put shard failed due to write zonefile failed,"
                     << " vid:" << _volume_id << " vbid:" << vbid
                     << " shard_index:" << _shard_index << " error:" << status;
        return status;
    }

    location.slice_desc_meta.CopyFrom(slice_desc_meta);
    status = _indexer->put(vbid, location);
    if (!status.ok()) {
        LOG(WARNING) << "put shard failed due to update indexer failed,"
                     << " vid:" << _volume_id << " vbid:" << vbid
                     << " shard_index:" << _shard_index << " error:" << status;
        return status;
    }

    LOG(TRACE) << "put shard succeeded, vid:" << _volume_id << " vbid:" << vbid
               << " shard_index:" << _shard_index << " loc:" << location.loc_to_string();

    return Status(AIE_OK);
}

Status AppendStore::change_shard(uint64_t vbid, int op) {
    LOG(NOTICE) << "change shard for vid:" << _volume_id << " vbid:" << vbid
                << " shard_index:" << _shard_index << " op:" << op;
    aries::pb::ShardMeta meta;
    base::IOBuf data;
    std::string tmp = "";
    auto s = get(vbid, &meta, &data);
    char c = '\0';
    uint32_t check_sum = 0;
    uint32_t ori_sum = 0;
    if (!s.ok() && !(s.code() == AIE_MARK_REMOVED)) {
        return s;
    }
    switch (op) {
        case 0: // change shard
            break;
        case 7: // change shard and footer crc
            break;
        case 1: // change meta
            break;
        case 8: // change meta and footer crc
            if (meta.key().length() != 0) {
                auto key = rand_str_for_test(meta.key().length());
                meta.set_key(key);
            } else if (meta.user_meta().length() != 0) {
                auto user_meta = rand_str_for_test(meta.user_meta().length());
                meta.set_user_meta(user_meta);
            } else {
                LOG(WARNING) << " vbid:" << vbid << " doesn't has meta";
            }
            break;
        case 2: // change data and crc
            data.pop_back(10);
            tmp = rand_str_for_test(10);
            data.append(tmp);
            check_sum = base::crc32c::Value(data.to_string().c_str(), data.to_string().size());
            meta.set_shard_crc(check_sum);
            break;
        case 3: // change meta and crc
            if (meta.key().length() != 0) {
                auto key = rand_str_for_test(meta.key().length());
                meta.set_key(key);
            } else if (meta.user_meta().length() != 0) {
                auto user_meta = rand_str_for_test(meta.user_meta().length());
                meta.set_user_meta(user_meta);
            } else {
                LOG(WARNING) << " vbid:" << vbid << " doesn't has meta";
            }
            check_sum = base::crc32c::Value(meta.key().data(), meta.key().size());
            check_sum = base::crc32c::Extend(check_sum, meta.user_meta().c_str(), meta.user_meta().size());
            meta.set_key_meta_crc(check_sum);
            break;
        case 4: // change location
            return Status(AIE_FAIL, "not support op");
        case 5: // delete shard
            s = remove(vbid, true);
            return s;
        case 6: // restore shard
            s = restore(vbid);
            return s;
        default:
            return Status(AIE_FAIL, "not support op");
    }
    s = put_for_test(vbid, meta, data, op);
    return s;
}
#endif

Status AppendStore::init_rewrite_context(RewriteVletContext* rewrite_vlet_context) {
    LOG(NOTICE) << _name << " int vlet rewrite context with vid:" << rewrite_vlet_context->vid;
    GetRewriteInfoContextPtr rewrite_info_context = rewrite_vlet_context->rewrite_info_context_ptr;
    std::shared_ptr<AppendStoreRewriteDetailInfo> rewrite_detail_info = std::make_shared<AppendStoreRewriteDetailInfo>();

    {
        std::unique_lock<std::mutex> lock_guard(_meta_mutex);
        //increase rewrite generation
        ++_max_rewrite_generation;

        for (auto& pair : _zonefile_map) {
            ZonefilePtr zonefile = pair.second;

            if (!zonefile->is_seal()) {
                continue;
            }

            auto hole_size = zonefile->hole_size();
            uint32_t rate = hole_size * 100 / _zonefile_size;
            if (rate < std::max(_vlet_info.rewrite_rate(), rewrite_vlet_context->options.append_zone_rewrite_rate)
                    && !rewrite_info_context->rewrite_ignore_hole_rate) {
                continue;
            }

            ZonefileRewriteDetailInfo detail_info(zonefile->file_sequence_id(), zonefile->rewrite_generation(),
                              zonefile->file_index(), rate);
            detail_info.progress.zonefile_size = zonefile->total_file_size();
            rewrite_detail_info->append_zonefile(std::move(detail_info));
        }
    }


    auto cmp = [](const ZonefileRewriteDetailInfo& l, const ZonefileRewriteDetailInfo& r) {
        return l.hole_rate > r.hole_rate;
    };

    std::sort(rewrite_detail_info->get_zonefile_lists().begin(),
              rewrite_detail_info->get_zonefile_lists().end(),
              cmp);

    rewrite_vlet_context->rewrite_detail_info_ptr =
            std::static_pointer_cast<RewriteDetailInfo>(rewrite_detail_info);

    return Status();
}

Status AppendStore::do_rewrite(RewriteVletContext* rewrite_vlet_context) {
    //acquire rewrite mutex to avoid start copy_vlet task during rewrite
    common::ScopedMutexLock lock(_snapshot_mutex);
    if (rewrite_vlet_context->vlet_ptr->has_snapshot()) {
        LOG(WARNING) << _name << " vlet has snapshot and can not rewrite, vid:" << _volume_id;
        return Status(AIE_BUSY, "vlet has snapshot");
    }

    std::shared_ptr<AppendStoreRewriteDetailInfo> rewrite_detail_info =
            std::dynamic_pointer_cast<AppendStoreRewriteDetailInfo>(rewrite_vlet_context->rewrite_detail_info_ptr);
    assert(rewrite_detail_info != nullptr);

    auto& rewrite_zonefile_lists = rewrite_detail_info->get_zonefile_lists();
    auto iter = rewrite_zonefile_lists.begin();

    for (; iter != rewrite_zonefile_lists.end(); ++iter) {
        auto& zonefile = *iter;

        if (zonefile.progress.finish_rewrite) {
            continue;
        }

        break;
    }

    if (iter == rewrite_zonefile_lists.end()) {
        LOG(NOTICE) << _name << " finish rewrite vlet, vid:" << _volume_id << " path:" << _path;
        update_finish_rewrite_timestamp();
        rewrite_vlet_context->is_finish = true;
        return Status();
    }

    Status status = do_rewrite_zonefile(rewrite_vlet_context, *iter);

    if (!status.ok()) {
        return status;
    }

    if ((*iter).progress.finish_rewrite) {
        auto next_iter = iter;
        ++next_iter;

        if (next_iter == rewrite_zonefile_lists.end()) {
            LOG(NOTICE) << _name << " finish rewrite vlet, vid:" << _volume_id << " path:" << _path;
            update_finish_rewrite_timestamp();	    
            rewrite_vlet_context->is_finish = true;
        }
    }

    return Status();
}

Status AppendStore::do_rewrite_zonefile(RewriteVletContext* rewrite_vlet_context,
                                        ZonefileRewriteDetailInfo& zonefile_rewrite_info) {
    Status status;
    base::Timer timer;
    timer.start();

    ZonefilePtr zonefile;
    {
        std::unique_lock<std::mutex> lock_guard(_meta_mutex);
        auto iter = _zonefile_map.find(zonefile_rewrite_info.file_index);

        if (iter == _zonefile_map.end()) {
            LOG(FATAL) << _name << " rewrite zonefile failed due to its file index could not be found,"
                       << " file_index:" << zonefile_rewrite_info.file_index
                       << " path:" << _path;
            return Status(AIE_INVALID);
        }

        zonefile = iter->second;
    }
    assert(zonefile != nullptr);

    //skip ZonefileHead if current rewrite offset is 0
    if (0 == zonefile_rewrite_info.progress.current_rewrite_offset) {
        uint32_t read_size = std::max((uint32_t)PAGE_SIZE_4K, align_size());
        common::Buffer buffer(read_size, align_size());
        status = zonefile->read_range(0, read_size / align_size(), &buffer);

        if (!status.ok()) {
            LOG(WARNING) << _name << " rewrite zonefile failed due to read zonefile head failed,"
                         << " file:" << zonefile->file_path() << " error:" << status;
            return status;
        }

        //check crc
        uint32_t get_crc = *reinterpret_cast<uint32_t*>(buffer.buf() + PAGE_SIZE_4K - sizeof(uint32_t));
        uint32_t crc = ::base::crc32c::Value(buffer.buf(), PAGE_SIZE_4K - sizeof(uint32_t));

        if (crc != get_crc) {
            LOG(FATAL) << _name << " rewrite zonefile failed due to zonefile head crc inconsistent,"
                       << " expected_crc:" << get_crc << " acutal_crc:" << crc
                       << " file:" << zonefile->file_path();
            return Status(AIE_CHECKSUM, "invalid ZonefileHead crc");
        }

        const ZonefileHead& head = *reinterpret_cast<ZonefileHead*>(buffer.buf());
        if (head.record_magic != ZONEFILE_MAGIC) {
            LOG(FATAL) << _name << " rewrite zonefile failed due to zonefile head magic is invalid,"
                       << " actual_magic:" << head.record_magic << " file:" << zonefile->file_path();
            return Status(AIE_CORRUPT, "invalid ZonefileHead magic");
        }

        zonefile_rewrite_info.progress.current_rewrite_offset = read_size / align_size();
        rewrite_vlet_context->rewrite_data_len += read_size;
    }

    uint64_t max_read_len = (FLAGS_append_vlet_batch_get_kb * 1024 + _align_size - 1) / _align_size;
    if (max_read_len == 0) {
        ++max_read_len;
    }

    common::Buffer buffer(max_read_len * align_size(), align_size());
    uint64_t read_len = 0;
    bool read_eof = false;
    const uint64_t current_rewrite_offset = zonefile_rewrite_info.progress.current_rewrite_offset;
    status = zonefile->batch_read(current_rewrite_offset,
                                  max_read_len,
                                  &buffer,
                                  &read_len);

    if (!status.ok()) {
        LOG(WARNING) << _name << " rewrite zonefile failed due to batch read zonefile failed,"
                     << " offset:" << current_rewrite_offset << " length:" << max_read_len
                     << " file:" << zonefile->file_path() << " error:" << status;
        return status;
    }

    if (read_len != max_read_len * align_size()) {
        read_eof = true;
    }

    std::vector<std::pair<uint64_t, AppendRecordLocation>> loc_list;
    base::IOBuf record_data_buf;
    status = batch_deserialize_append_record(zonefile_rewrite_info,
                                             buffer,
                                             read_len,
                                             &loc_list,
                                             read_eof,
                                             &record_data_buf);
    if (!status.ok()) {
        LOG(WARNING) << _name << " rewrite zonefile failed due to batch deserialize record failed,"
                     << " file:" << zonefile->file_path() << " error:" << status;
        return status;
    }

    const uint32_t record_data_buf_len = record_data_buf.size();

    //batch put
    if (!loc_list.empty()) {
        status = batch_put_rewrite_record(loc_list, record_data_buf, zonefile_rewrite_info);

        if (!status.ok()) {
            LOG(WARNING) << _name << " rewrite zonefile failed due to batch put record failed,"
                         << " record_num:" << loc_list.size() << " buffer_size:" << record_data_buf.size()
                         << " file:" << zonefile->file_path() << " error:" << status;
            return status;
        }
    } else {
        if (read_eof) {
            LOG(WARNING) << _name << " could not read any valid record during rewrite zonefile"
                         << " since the fd is eof,"
                         << " file:" << zonefile->file_path();

        } else {
            LOG(WARNING) << _name << "could not read any valid record during rewrite zonefile,"
                         << " maybe the buffer size is too small,"
                         << " batch_read_offset:" << current_rewrite_offset * align_size()
                         << " read_size:" << read_len
                         << " file:" << zonefile->file_path();
        }
    }

    const uint64_t rewrite_data_len =
            (zonefile_rewrite_info.progress.current_rewrite_offset - current_rewrite_offset) * align_size();

    rewrite_vlet_context->rewrite_data_len += rewrite_data_len;

    LOG(TRACE) << _name << " rewrite zonefile is partly finished, vid:" << rewrite_vlet_context->vid
               << " rewrite_record_num:" << loc_list.size()
               << " record_buf_size:" << record_data_buf_len
               << " cost_time_us:" << timer.u_elapsed()
               << " file:" << zonefile->file_path();

    if (read_eof) {
        LOG(NOTICE) << _name << " rewrite zonefile is wholy finished, vid:" << rewrite_vlet_context->vid
                    << " rewrite_record_num:" << zonefile_rewrite_info.progress.finished_rewrite_record_num
                    << " deleted_record_num:" << zonefile_rewrite_info.progress.filter_record_num
                    << " file:" << zonefile->file_path();
        g_engine_rewrite_record_num_adder << zonefile_rewrite_info.progress.finished_rewrite_record_num;
        g_engine_rewrite_delete_record_num_adder << zonefile_rewrite_info.progress.filter_record_num;
        zonefile_rewrite_info.progress.finish_rewrite = true;
        //zonefile all data has been rewrite, can remove now;
        remove_zonefile(zonefile_rewrite_info.file_index);
    }

    timer.stop();
    return Status();
}

Status AppendStore::batch_deserialize_append_record(ZonefileRewriteDetailInfo& zonefile_rewrite_info,
                                                    common::Buffer& buffer,
                                                    uint64_t buf_len,
                                                    std::vector<std::pair<uint64_t, AppendRecordLocation>>* loc_list,
                                                    bool end_of_file,
                                                    base::IOBuf* record_data_buf) {
    uint64_t offset = 0;
    uint64_t zonefile_rewrite_offset = zonefile_rewrite_info.progress.current_rewrite_offset;
    uint64_t min_head_size = std::min(sizeof(AppendRecordHead), sizeof(common::StandardRecordHeader));

    while (offset < buf_len) {
        const uint64_t left_len = buf_len - offset;

        if (left_len < min_head_size) {
            break;
        }

        bool is_record_data_type = true;
        uint32_t record_len;
        uint64_t record_magic = *reinterpret_cast<uint64_t*>(buffer.buf() + offset);
        if (record_magic == APPEND_RECORD_MAGIC) {
            const AppendRecordHead& header = *reinterpret_cast<AppendRecordHead*>(buffer.buf() + offset);
            if (header.record_type !=  common::RECORD_DATA) {
                is_record_data_type = false;
            }
            record_len = header.record_len;
        } else if (record_magic == common::STANDARD_RECORD_MAGIC) {
            const common::StandardRecordHeader& header = *reinterpret_cast<common::StandardRecordHeader*>(buffer.buf() + offset);
            if (header.record_body_desc_info.record_type !=  common::RECORD_DATA) {
                is_record_data_type = false;
            }
            uint32_t raw_size = sizeof(common::StandardRecordHeader) + header.record_body_desc_info.record_meta_len
                    + header.record_body_desc_info.record_data_len + sizeof(uint32_t);
            record_len = common::calc_align(raw_size, _align_size);
        } else {
            if (!end_of_file) {
                //invalid append record header, skip this page;
                LOG(WARNING) << "skip one page with invalid append record header,"
                             << " file_sequence_id:" << zonefile_rewrite_info.file_sequence_id
                             << " offset:" << zonefile_rewrite_offset * align_size()
                             << " path:" << _path;
            }
            offset += align_size();
            zonefile_rewrite_offset += 1;
            continue;
        }

        //maybe a append record but need double check crc
        if (offset + record_len > buf_len) {
            break;
        }

        //verify record crc
        uint32_t* saved_crc = reinterpret_cast<uint32_t*>(buffer.buf() + offset + record_len - sizeof(uint32_t));
        uint32_t crc = base::crc32c::Value(buffer.buf() + offset, record_len - sizeof(uint32_t));

        if (*saved_crc != crc) {
            //corrupted record or offset location is not the begin of a valid record
            //but anyway, skip this page;
            LOG(WARNING) << "skip one page with inconsistent crc,"
                         << " file_sequence_id:" << zonefile_rewrite_info.file_sequence_id
                         << " offset:" << zonefile_rewrite_offset * align_size()
                         << " path:" << _path;
            offset += align_size();
            zonefile_rewrite_offset += 1;
            continue;
        }

        //found a valid record;
        if (is_record_data_type) {
            ::aries::pb::AppendVletRecordMeta record_meta;
            common::Buffer record_buffer(buffer.buf() + offset, record_len);
            uint64_t vbid = 0;
            auto s = do_deserialize_record(record_buffer, &vbid, nullptr, nullptr, nullptr);
            record_buffer.release();
            if (!s.ok()) {
                return s;
            }
            AppendRecordLocation loc;
            loc.file_index = zonefile_rewrite_info.file_index;
            loc.offset = zonefile_rewrite_offset;
            loc.len = record_len / align_size();
            loc_list->emplace_back(vbid, std::move(loc));
            record_data_buf->append(buffer.buf() + offset, record_len);
        }

        offset += record_len;
        zonefile_rewrite_offset += record_len / align_size();
    }

    //update zonefile rewrite detail info progress;
    zonefile_rewrite_info.progress.current_rewrite_offset = zonefile_rewrite_offset;
    return Status();
}

Status AppendStore::batch_put_rewrite_record(const std::vector<std::pair<uint64_t, AppendRecordLocation>>& loc_list,
                                             base::IOBuf& buf,
                                             ZonefileRewriteDetailInfo& zonefile_rewrite_info) {
    Status status;

    auto iter = loc_list.begin();
    for (; iter != loc_list.end(); ++iter) {
        const uint64_t vbid = iter->first;
        const AppendRecordLocation& location = iter->second;
        common::Buffer record_buf(location.len * align_size(), align_size());
        size_t read_len = buf.cutn(static_cast<void*>(record_buf.buf()), location.len * align_size());
        assert(read_len == location.len * align_size());

        status = condition_put_record(vbid, location, record_buf, zonefile_rewrite_info);

        if (!status.ok()) {
            return status;
        }
    }

    return Status();
}

Status AppendStore::condition_put_record(const uint64_t vbid,
                                         const AppendRecordLocation& location,
                                         const common::Buffer& record_buf,
                                         ZonefileRewriteDetailInfo& zonefile_rewrite_info) {
    AppendRecordLocation index_loc;
    auto status = _indexer->get(vbid, &index_loc);

    if (status.ok() || status.code() == AIE_MARK_REMOVED) {

        if (index_loc.file_index != location.file_index
                || index_loc.offset != location.offset
                || index_loc.len != location.len) {
            //record is existed but with new data
            LOG(TRACE) << _name << " skip rewrite record due to its location has been changed,"
                       << " vid:" << _volume_id << " vbid:" << vbid
                       << " last_loc:" << location.loc_to_string()
                       << " curr_loc:" << index_loc.loc_to_string();
            ++zonefile_rewrite_info.progress.filter_record_num;
            return Status();
        }

    } else if (status.code() == AIE_BLOB_NOT_EXIST) {
        //record has already been deleted
        LOG(TRACE) << _name << " skip rewrite record due to it has been finally removed,"
                   << " vid:" << _volume_id << " vbid:" << vbid;
        ++zonefile_rewrite_info.progress.filter_record_num;
        return Status();
    } else {
        LOG(WARNING) << _name << " rewrite record failed due to read indexer failed,"
                     << " vid:" << _volume_id << " vbid:" << vbid << " error:" << status;
        return status;
    }

    //append record buf to zonefile
    auto left_size = get_free_size(true);
    if (left_size < location.len * align_size()) {
        status = alloc_zonefile(true);
        if (!status.ok()) {
            LOG(WARNING) << _name << " rewrite record failed due to alloc new zonefile failed,"
                         << " vid:" << _volume_id << " vbid:" << vbid << " error:" << status;
            return status;
        }
    }

    assert(_curr_rewrite_file != nullptr);
    AppendRecordLocation new_loc;

    status = _curr_rewrite_file->write(record_buf.buf(), record_buf.size(), &new_loc);
    if (!status.ok()) {
        LOG(WARNING) << _name << " rewrite record failed due to write zonefile failed,"
                     << " vid:" << _volume_id << " vbid:" << vbid
                     << " curr_rewrite_file:" << _curr_rewrite_file->file_path()
                     << " error:" << status;
        return status;
    }

    //acquire lock and check again if indexer is same;
    base::get_leaky_singleton<RowLockManager>()->lock_row(_volume_id, vbid);

    AppendRecordLocation current_loc;
    status = _indexer->get(vbid, &current_loc);
    if (status.ok() || status.code() == AIE_MARK_REMOVED) {
        if (current_loc.file_index != location.file_index
                || current_loc.offset != location.offset
                || current_loc.len != location.len) {
            //record exists but with new data
            LOG(TRACE) << _name << " skip rewrite record due to its location has been changed,"
                       << " vid:" << _volume_id << " vbid:" << vbid
                       << " last_loc:" << location.loc_to_string()
                       << " curr_loc:" << current_loc.loc_to_string();
            base::get_leaky_singleton<RowLockManager>()->unlock_row(_volume_id, vbid);
            return Status();
        }
    } else if (status.code() == AIE_BLOB_NOT_EXIST) {
        //record has already been deleted just now
        LOG(TRACE) << _name << " skip rewrite record due to it has been finally deleted,"
                   << " vid:" << _volume_id << " vbid:" << vbid;
        base::get_leaky_singleton<RowLockManager>()->unlock_row(_volume_id, vbid);
        return Status();
    } else {
        LOG(WARNING) << _name << " rewrite record failed due to read indexer failed,"
                     << " vid:" << _volume_id << " vbid:" << vbid << " error:" << status;
        base::get_leaky_singleton<RowLockManager>()->unlock_row(_volume_id, vbid);
        return status;
    }

    //safe to update indexer now
    new_loc.delete_time = current_loc.delete_time;
    new_loc.slice_desc_meta.CopyFrom(current_loc.slice_desc_meta);
    new_loc.blob_ttl_timestamp = current_loc.blob_ttl_timestamp;
    status = _indexer->put(vbid, new_loc);
    if (!status.ok()) {
        LOG(WARNING) << _name << " rewrite record failed due to write indexer failed,"
                     << " vid:" << _volume_id << " vbid:" << vbid << " error:" << status;
    } else {
        _curr_rewrite_file->add_used_size(record_buf.size());
        ++zonefile_rewrite_info.progress.finished_rewrite_record_num;
        LOG(TRACE) << _name << " rewrite record succeeded by perform cas_update"
                   << " from old_loc:" << index_loc.loc_to_string()
                   << " to new_loc:" << new_loc.loc_to_string()
                   << " vid:" << _volume_id << " vbid:" << vbid;
    }

    base::get_leaky_singleton<RowLockManager>()->unlock_row(_volume_id, vbid);
    return status;
}

Status AppendStore::update_finish_rewrite_timestamp() {
    std::unique_lock<std::mutex> lock_guard(_meta_mutex);;
    ::aries::pb::AppendVletInfo info;
    info.CopyFrom(_vlet_info);
    info.set_last_finish_rewrite_time(base::gettimeofday_s());

    auto status = dump_vlet_info(info);
    if (!status.ok()) {
        LOG(WARNING) << _name << " dump vlet info failed, path:" << _path;
        return status;
    }
    _vlet_info = info;
    return Status();
}

Status AppendStore::purge(bool purge_blob_without_ttl) {
    {
        std::unique_lock<std::mutex> lock_guard(_meta_mutex);;
        if (!_vlet_info.check_blob_ttl()) {
            return Status();
        }
    }
    RecordKey begin(_volume_id, 0);
    rocksdb::Slice begin_key = begin.to_slice();

    std::unique_ptr<rocksdb::Iterator> it(_db->NewIterator(rocksdb::ReadOptions()));
    std::set<uint64_t> purge_vbids;
    for (it->Seek(begin_key); it->Valid(); it->Next()) {
        if (it->key().size() != sizeof(RecordKey)) {
            continue;
        }
        RecordKey* key = (RecordKey*)it->key().data();
        if (key->vid() != _volume_id) {
            break;
        }
        if (it->value().size() == 0) {
            continue;
        }
        AppendRecordLocation loc;
        loc.parse_from_array((const void*)it->value().data(), it->value().size());

        if ((!loc.has_blob_ttl() && purge_blob_without_ttl) || loc.is_expired()) {
            purge_vbids.insert(key->vbid());
        }
    }
    for (auto& vbid : purge_vbids) {
        Status s = remove(vbid, true);
        if (!s.ok()) {
            LOG(WARNING) << "ignore the failure of mark delete when purge vlet,"
                         << " vid:" << _volume_id << " vbid:" << vbid
                         << " shard_index:" << _shard_index << " error:" << s;
            continue;
        }
        s = remove(vbid, false);
        if (!s.ok()) {
            LOG(WARNING) << "ignore the failure of final delete when purge vlet,"
                         << " vid:" << _volume_id << " vbid:" << vbid
                         << " shard_index:" << _shard_index << " error:" << s;
        }
    }
    return Status();
}

}//end of namespace
}//end of namespace
