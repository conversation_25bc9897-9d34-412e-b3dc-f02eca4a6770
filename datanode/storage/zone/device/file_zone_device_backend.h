// Copyright (c) 2022 Baidu.com, Inc. All Rights Reserved.

#pragma once

#include <memory>
#include "rocksdb/db.h"
#include "baidu/inf/aries/datanode/storage/zone/zone_disk.h"
#include "baidu/inf/aries/datanode/storage/zone/device/zone_device_backend.h"

#ifndef FALLOC_FL_CONVERT_AND_EXTEND
#define FALLOC_FL_CONVERT_AND_EXTEND 0x100
#endif

namespace aries {
namespace datanode {

// FileZoneDeviceBackend abstracts out zone read/write interface on conventional disks.
class FileZoneDeviceBackend : public ZoneDeviceBackend {
public:
    explicit FileZoneDeviceBackend(std::shared_ptr<rocksdb::DB> db);

#ifdef _UNIT_TEST
    explicit FileZoneDeviceBackend(std::shared_ptr<rocksdb::DB> db, 
                                   std::function<decltype(::pread)>&& pread_func, 
                                   std::function<decltype(::pwrite)>&& pwrite_func) 
                : FileZoneDeviceBackend(db) {
        _pread_func = std::move(pread_func);
        _pwrite_func = std::move(pwrite_func);
    }
#endif

    virtual ~FileZoneDeviceBackend();

    virtual int open(const DeviceOpenOptions& options) override;

    virtual  int close() override;

    virtual int allocate_zone(const ZoneFD** zone_fd, bool is_rerwite_zone = false) override;

    virtual int get_zone_fd(ZoneID zone_id, const ZoneFD** zone_fd) override;

    virtual int free_zone(const ZoneID& zone_id) override;

    virtual int close_zone(const ZoneID& zone_id) override;

    virtual int async_read(const ZoneRecordLocation& location, void* buf, std::function<void(int err)>&& done) override;

    int read(const ZoneRecordLocation& location, void* buf) override;

    int async_write(const common::Buffer& data,
                    const ZoneRecordLocation& location,
                    std::function<void(int err)>&& done) override;

    int write(const common::Buffer& data, const ZoneRecordLocation& location) override;

    int append(const common::Buffer& data, ZoneRecordLocation* location) override;

    int async_write(const ZoneFD* zone_fd,
                    const common::Buffer& data,
                    const ZoneRecordLocation& location,
                    std::function<void(int err)>&& done) override;

    int write(const ZoneFD* zone_fd, const common::Buffer& data, const ZoneRecordLocation& location) override;

    int append(const ZoneFD* zone_fd, const common::Buffer& data, ZoneRecordLocation* location) override;

    virtual void list_zones(std::vector<ZoneID>* zone_list) override;

    virtual int reset_zone(const ZoneID& zone_id, ZoneState state) override;

    uint32_t zone_count() const override;

    int create_new_zones(uint64_t create_count) override;
private:
    int create_new_zone();
    int create_zone_file(uint64_t zone_id, std::string* file_path, int* fd);
    int load_zone_file_infos();

private:
    static inline const std::string ZONE_FILE_PREFIX = "Z_";
    static inline const uint32_t ZONE_FILE_ZONE_ID_STRING_LENGTH = 12;

    std::string _path{""};   // zone file parent directory
    std::shared_ptr<rocksdb::DB> _db{nullptr};   // rocksdb实例，存储zone索引信息
    std::string _name; // show in log

#ifdef _UNIT_TEST
    // for mocking pread and pwrite
    std::function<decltype(::pread)> _pread_func{::pread};
    std::function<decltype(::pwrite)> _pwrite_func{::pwrite};
#endif
};

ZoneDeviceBackend* create_file_zone_device(
        std::shared_ptr<rocksdb::DB> db);

}
}
