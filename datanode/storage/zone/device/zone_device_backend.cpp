// Copyright (c) 2022 Baidu.com, Inc. All Rights Reserved.

#include "baidu/inf/aries/datanode/storage/zone/device/zone_device_backend.h"

namespace aries {
namespace datanode {

UnitIoStats ZoneDeviceBackend::get_io_stat() {
    return _io_stats_collector.get_and_reset();
}

void ZoneDeviceBackend::get_zone_status(ZoneDeviceZoneStatus* status) {
    _zone_manager->get_zone_status(status);
    status->zone_size = _zone_size;
}

void ZoneDeviceBackend::update_zone_metrics() {
    if (_zone_metrics == nullptr) {
        return;
    }
    ZoneDeviceZoneStatus status;
    get_zone_status(&status);
    _zone_metrics->zone_size.set_value(status.zone_size);
    _zone_metrics->zone_cnt.set_value(status.zone_cnt);
    _zone_metrics->invalid_zone_cnt.set_value(status.invalid_zone_cnt);
    _zone_metrics->used_zone_cnt.set_value(status.used_zone_cnt);
    _zone_metrics->open_zone_cnt.set_value(status.open_zone_cnt);
    _zone_metrics->closed_zone_cnt.set_value(status.used_zone_cnt - status.open_zone_cnt);
    _zone_metrics->free_zone_cnt.set_value(status.zone_cnt - status.used_zone_cnt - status.invalid_zone_cnt);
}

}
}
