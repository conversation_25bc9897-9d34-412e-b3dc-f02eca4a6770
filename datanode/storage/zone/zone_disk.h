// Copyright 2022 Baidu Inc. All Rights Reserved.

#pragma once

#include <cstring>
#include <vector>
#include <map>

#include <base/iobuf.h>

#include <folly/SharedMutex.h>
#include "baidu/inf/aries/common/common.h"
#include "baidu/inf/aries/common/status.h"
#include "baidu/inf/aries/common/record/record_serializer.h"
#include "baidu/inf/aries-api/common/buffer.h"
#include "baidu/inf/aries-api/common/lock.h"
#include "baidu/inf/aries/datanode/util/util.h"
#include "baidu/inf/aries/datanode/storage/zone/zone_indexer.h"
#include "baidu/inf/aries/datanode/storage/zone/device/zone_device_backend.h"
#include "baidu/inf/aries/datanode/storage/zone/channel/channel_group.h"
#include "baidu/inf/aries/datanode/storage/zone/channel/choose_rewrite_zone_strategy.h"

namespace aries {
namespace datanode {

struct VolumeKey;
struct ZoneDiskRewriteWorkerContext;
class ZoneMetaDBIndexer;
class VletIndexer;
class DiskAgent;

class ZoneDisk {
public:
    explicit ZoneDisk(ZoneDeviceBackend* device, std::shared_ptr<rocksdb::DB> db, DiskAgent* disk_agent);

    virtual ~ZoneDisk();

    Status open();

    Status close();

    Status prepare_resources();

    Status append(ZoneAppendContext* context);

    Status batch_append(ZoneBatchAppendContext* context);

    Status read(const ZoneRecordLocation& location, common::Buffer* data);

    Status rewrite(ZoneDiskRewriteWorkerContext* ctx);

    void get_zone_size(size_t* zone_size) const {
        *zone_size = _zone_size;
    }

    common::Gate* update_zone_meta(const ZoneMeta& zone_meta);

    void update_zone_meta(ZoneID zone_id, ZoneState zone_state);

    void remove_zone_meta(const ZoneID& zone_id);

    std::optional<ZoneMeta> get_zone_meta(ZoneID zone_id) const;

    common::Gate* get_zone_gate(ZoneID zone_id);

    void list_zone_meta(std::vector<ZoneMeta>* meta_list) const;

    int64_t update_zone_used_size(const ZoneID zone_id,
                               int64_t used_size_delta);

    void update_zone_age(const ZoneID zone_id,
                         int64_t delta,
                         uint64_t create_byte_time);

    VletIndexer* vlet_indexer();
    Status destroy_vlet(const VolumeKey& volume_key);
    Status add_dropped_vlet(const aries::pb::GenericVletInfo& vlet_info);
    Status pop_dropped_vlet(uint64_t volume_id, aries::pb::GenericVletInfo* vlet_info);
    void list_dropped_vlet(std::vector<aries::pb::GenericVletInfo>* vlet_info_list);

    int64_t update_zone_used_size_and_age(const ZoneID zone_id,
                                       int64_t delta,
                                       uint64_t create_byte_time);

    void get_aries_usage(uint64_t* aries_total_size, uint64_t* aries_free_size) const;

    void get_zone_status(ZoneDeviceZoneStatus* status) const;

    uint64_t get_logical_used_size() const;

    uint64_t align_size() const {
        return _device->align_size();
    }

    DiskAgent* disk_agent() const {
        return _disk_agent;
    }

    DiskID disk_id() const { return _disk_id; }

    common::DiskType disk_type() const { return _device->disk_type(); }

    ChannelWriteStats channel_stats() const;

protected:
    void reload_zone_metas();

    bool check_zone_rewritable(ZoneID zone_id) {
        folly::SharedMutex::ReadHolder r{_meta_lock};
        ZoneState zone_state = _zone_metas[zone_id].state;
        return (zone_state == ZoneState::CLOSED);
    }
protected:
    DiskID _disk_id;
    size_t _disk_size;
    size_t _zone_size;
    size_t _zone_capacity;

    mutable folly::SharedMutex _meta_lock;

    std::unordered_map<ZoneID, ZoneMeta> _zone_metas;

    /**
    Since some data(and meta data) may has NOT been persisted when the zone is
    CLOSED, it is necessary to protect the zone from being rewritten at this
    point . "zone_gate" is used for this purpose, by calling
    "zone_gate.try_enter()" when write data, and "zone_gate.leave()" when write
    finish. Before rewrite, "zone_gate.close()" is called to wait for writers
    finish. zone_gate is added to or removed from "_zone_gates" at the same time
    when zone_meta is added to or removed from "_zone_metas". Modification to
    "_zone_gates" and "_zone_metas" are protected by "_meta_lock". */
    std::unordered_map<ZoneID, common::Gate> _zone_gates;

    std::string _disk_name;
    common::MutexLock _rewrite_close_lock;
    common::MutexLock _rewriting_zone_set_lock;
    std::set<ZoneID> _rewriting_zone_set;

    std::unique_ptr<ZoneMetaDBIndexer> _zone_indexer{nullptr};
    ZoneDeviceBackend* _device{nullptr};
    std::shared_ptr<rocksdb::DB> _db{nullptr};
    DiskAgent* _disk_agent{nullptr};
    std::unique_ptr<ChannelGroup> _channel_group;
    std::unique_ptr<ChooseRewriteZoneStrategy> _cost_benefit_choose_strategy;
    std::unique_ptr<ChooseRewriteZoneStrategy> _greedy_choose_strategy;

    common::MutexLock _drop_vlet_lock;
    std::unordered_map<uint64_t, aries::pb::GenericVletInfo> _dropped_vlets;

    bool _is_disk_closed{false};
    std::atomic<int64_t> _pending_rewrite_task_count{0};

    DISABLE_COPY_AND_MOVE(ZoneDisk);
};

}
}

