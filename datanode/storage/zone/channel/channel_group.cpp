// Copyright 2022 Baidu Inc. All Rights Reserved.
// @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>@baidu.com
// @Created Time : Tue Sept 7 15:22:57 CST 2022
// @File Name: channel_group.cpp
// @Description: manage channels

#include "base/fast_rand.h"

#include "baidu/inf/aries/common/record/standard_record.h"
#include "baidu/inf/aries/datanode/disk_agent.h"
#include "baidu/inf/aries/datanode/storage/store_base.h"
#include "baidu/inf/aries/datanode/storage/zone/zone_disk.h"
#include "baidu/inf/aries/datanode/storage/zone/zone_meta_persistor.h"
#include "baidu/inf/aries/datanode/storage/zone/device/zone_device_backend.h"
#include "baidu/inf/aries/datanode/storage/zone/channel/channel_group.h"
#include "baidu/inf/aries/datanode/throttle/flow_control.h"

namespace aries {
namespace datanode {

bvar::LatencyRecorder g_engine_channel_rewrite_latency("engine_channel", "rewrite");

ChannelGroup::ChannelGroup(
        std::shared_ptr<ByteTimeGenerator> time_generator,
        ZoneDeviceBackend* device,
        ZoneDisk* zone_system,
        ZoneMetaDBIndexer* zone_indexer,
        std::shared_ptr<rocksdb::DB> db) :
        _time_generator(time_generator),
        _zone_device_backend(device),
        _zone_system(zone_system),
        _zone_indexer(zone_indexer),
        _db(db),
        _name(base::string_printf("ChannelGroup(disk_id:%d group:%d) ", zone_system->disk_id(), CHANNEL_GROUP_ID)) {
    assert(FLAGS_user_channel_count > 0 && FLAGS_rewrite_channel_count > 0);
    _record_serializer = new ZoneRecordSerializer(_zone_device_backend->align_size());

    for (int i = 0; i < FLAGS_user_channel_count; ++i) {
        _user_channel_writers.push_back(std::make_unique<FileChannelWriter>(
                device,
                zone_system,
                zone_indexer,
                this,
                CHANNEL_GROUP_ID + USER_CHANNEL_PREFIX + i,
                false));
        // the channel_id can be clearly used to identify the channel, channel type and
        // channel_group for user channel, channel1:10100, channel2:10101,... etc
    }

    for (int i = 0; i < FLAGS_rewrite_channel_count; ++i) {
        _rewrite_channel_writers.push_back(std::make_unique<FileChannelWriter>(
                device,
                zone_system,
                zone_indexer,
                this,
                CHANNEL_GROUP_ID + REWRITE_CHANNEL_PREFIX + i,
                true));
        // for rewrite channel, channel1:10200, channel2:10201,... etc
    }

    if (_zone_system->disk_agent() != nullptr) {
        _flow_control = _zone_system->disk_agent()->flow_control();
    }
    // init channel bvar
    _channel_write_stats_collector.total_user_channels_write_bytes_seconds_window.reset(
            new bvar::Window <bvar::Adder<uint64_t>>(
                    &_channel_write_stats_collector.total_user_channels_write_bytes,
                    FLAGS_channel_io_stats_window_s));
    _channel_write_stats_collector.total_rewrite_channels_write_bytes_seconds_window.reset(
            new bvar::Window <bvar::Adder<uint64_t>>(
                    &_channel_write_stats_collector.total_rewrite_channels_write_bytes,
                    FLAGS_channel_io_stats_window_s));
    _channel_write_stats_collector.total_channels_write_bytes_seconds_window.reset(
            new bvar::Window <bvar::Adder<uint64_t>>(
                    &_channel_write_stats_collector.total_channels_write_bytes,
                    FLAGS_channel_io_stats_window_s));
    _data_cold_hot_threshold.expose_as(
            "datanode",
            "disk" + std::to_string(_zone_device_backend->disk_id()) + "_data_cold_hot_threshold");
}

ChannelGroup::~ChannelGroup() {
    delete _record_serializer;
    _record_serializer = nullptr;
}

Status ChannelGroup::init() {
    Status s = 0;
    auto init_threshold = [&s](ChannelThreshold& channel_threshold) {
        s = channel_threshold.init();
        return s.ok();
    };
    _channel_threshold.Modify(init_threshold);
    return s;
}

int ChannelGroup::write(ZoneAppendContext* context, uint64_t age) {
    // Find the channel corresponding to different ages
    size_t channel_index = 0;
    if (FLAGS_user_channel_write_policy == RANDOM) {
        channel_index = base::fast_rand() % _user_channel_writers.size();
    } else if (FLAGS_user_channel_write_policy == CHANNEL_THRESHOLD) {
        base::DoublyBufferedData<ChannelThreshold>::ScopedPtr channel_threshold;
        _channel_threshold.Read(&channel_threshold);
        channel_index = channel_threshold->choose_user_channel(age);
    } else {
        DN_LOG(WARNING) << _name << "write failed due to unknown write policy, write_policy:"
                        << FLAGS_user_channel_write_policy;
        return AIE_INVALID;
    }
    context->create_byte_time = _time_generator->fetch_and_add(context->record_buf->size());

    int ret = _user_channel_writers[channel_index]->append(context);
    if (ret != AIE_OK) {
        DN_LOG(WARNING) << _name << "write failed due to channel append failed,"
                        << " channel_index:" << channel_index
                        << " channel_id:" << _user_channel_writers[channel_index]->channel_id()
                        << " ret:" << ret;
        return ret;
    }
    size_t record_len = context->record_buf->size();
    ChannelID channel_id = _user_channel_writers[channel_index]->channel_id();
    _channel_write_stats_collector.record_user_channel_io(channel_id, record_len);
    return ret;
}

int ChannelGroup::batch_write(ZoneBatchAppendContext* context, uint64_t age) {
    // Find the channel corresponding to different ages
    size_t channel_index = 0;
    if (FLAGS_user_channel_write_policy == RANDOM) {
        channel_index = base::fast_rand() % _user_channel_writers.size();
    } else if (FLAGS_user_channel_write_policy == CHANNEL_THRESHOLD) {
        base::DoublyBufferedData<ChannelThreshold>::ScopedPtr channel_threshold;
        _channel_threshold.Read(&channel_threshold);
        channel_index = channel_threshold->choose_user_channel(age);
    } else {
        DN_LOG(WARNING) << _name << "batch write failed due to unknown write policy,"
                << " write_policy:" << FLAGS_user_channel_write_policy;
        return AIE_INVALID;
    }
    int ret = _user_channel_writers[channel_index]->batch_append(context);
    if (ret != AIE_OK) {
        DN_LOG(WARNING) << _name << "batch write failed due to channel append failed,"
                << " channel_index:" << channel_index
                << " channel_id:" << _user_channel_writers[channel_index]->channel_id()
                << " ret:" << ret;
        return ret;
    }
    size_t record_len = context->record_buf->size();
    ChannelID channel_id = _user_channel_writers[channel_index]->channel_id();
    _channel_write_stats_collector.record_user_channel_io(channel_id, record_len);
    return ret;
}

Status ChannelGroup::try_acquire_rewrite_flow(size_t size, ReadWriteType read_write_type) {
    if (FLAGS_enable_ssd_flow_control && _flow_control != nullptr) {
        int tries = 0;
        while (!_flow_control->try_acquire(size, REWRITE_FLOW, read_write_type)) {
            if (++tries <= FLAGS_rewrite_acquire_flow_max_retry_times) {
                ::usleep(tries * FLAGS_rewrite_flow_control_sleep_ms * 1000);
            } else {
                return Status(AIE_BUSY, "acquire_flow retry time exhausted");
            }
        }
    }
    return Status();
}

Status ChannelGroup::rewrite(
        const ZoneMeta& zone_meta,
        uint64_t batch_get_size,
        uint64_t batch_put_size) {
    common::TimeMeasure time_measure(&g_engine_channel_rewrite_latency);
    const ZoneID rewritten_zone_id = zone_meta.zone_id;
    DN_LOG(NOTICE) << "start to rewrite zone, disk_id:" << _zone_device_backend->disk_id()
                << " zone_id:" << rewritten_zone_id;

    // When the first channel is overwritten, L needs to be updated. L is the hot and cold
    // threshold.
    bool is_first_channel = false;
    if ((FLAGS_user_channel_write_policy == RANDOM &&
         zone_meta.channel_id < CHANNEL_GROUP_ID + USER_CHANNEL_PREFIX + (int32_t) _user_channel_writers.size()) ||
        (FLAGS_user_channel_write_policy == CHANNEL_THRESHOLD &&
         zone_meta.channel_id == CHANNEL_GROUP_ID + USER_CHANNEL_PREFIX)) {
        is_first_channel = true;
        update_channel_threshold(_time_generator->get_time() - zone_meta.avg_create_time);
    }

    // Store different channel data to different items in the vector,
    // then flush them into the corresponding channel
    uint64_t now = _time_generator->get_time();
    std::vector<base::IOBuf> rewrite_bufs(_rewrite_channel_writers.size());
    std::vector<std::list<ZoneRecordInfo>> record_metas(_rewrite_channel_writers.size());

    const char* record_buf{nullptr};
    common::Buffer write_buffer(batch_put_size, _zone_device_backend->align_size());

    const size_t read_buffer_size = FLAGS_zone_rewrite_do_batch_read ? batch_get_size : MAX_RECORD_SIZE;
    ZoneRecordIterator iterator(FLAGS_zone_rewrite_do_batch_read, read_buffer_size,
                zone_meta, zone_meta.zone_size, _db, _zone_device_backend, _flow_control);
    iterator.seek(ZONE_META_RECORD_SIZE); // skip meta record
    while (true) {
        bool is_finish{false};
        ZoneRecordInfo record_meta;
        Status status = iterator.next_record(&record_buf, &is_finish, &record_meta.index_key, &record_meta.index_entry);
        if (status.ok()) {
            if (is_finish) {
                DN_LOG(TRACE) << _name << "rewrite find end position in zone, zone_id:" << zone_meta.zone_id
                           << " offset:" << iterator.offset();
                break;
            }

            // since iterator.next_record() has checked crc, so it's ok to do cast
            size_t record_size = record_meta.index_entry.record_length();
            auto* footer = reinterpret_cast<const ZoneRecordFooter*>(
                                record_buf + record_size - sizeof(ZoneRecordFooter));
            record_meta.create_byte_time = footer->create_byte_time;

            status = check_rewrite_index(record_meta);
            if (!status.ok()) {
                if (status.code() == AIE_BLOB_NOT_EXIST) {
                    continue;
                }
                return status;
            }

            uint64_t age = now - record_meta.create_byte_time;
            size_t rewrite_channel_index = 0;
            // If it is the first channel, write to the first channel of rewrite
            if (!is_first_channel || !FLAGS_enable_directly_rewrite_channel) {
                base::DoublyBufferedData<ChannelThreshold>::ScopedPtr channel_threshold;
                _channel_threshold.Read(&channel_threshold);
                rewrite_channel_index = channel_threshold->choose_rewrite_channel(age);
            }

            size_t curr_size = rewrite_bufs[rewrite_channel_index].size();
            if (curr_size + record_size > batch_put_size) {
                // If buffer size reaches the batch_put_size,
                // write records to rewritten zone then clear rewrite_bufs and record_metas
                write_buffer.set_size(curr_size);
                rewrite_bufs[rewrite_channel_index].copy_to(write_buffer.buf());

                status = flush_buf_to_disk(
                        write_buffer,
                        record_metas[rewrite_channel_index],
                        rewrite_channel_index);
                rewrite_bufs[rewrite_channel_index].clear();
                record_metas[rewrite_channel_index].clear();
                unlikely_if (!status.ok()) {
                    DN_LOG(WARNING) << "rewrite write records failed, status:" << status;
                    return status;
                }
            }

            rewrite_bufs[rewrite_channel_index].append(record_buf, record_size);
            record_metas[rewrite_channel_index].push_back(std::move(record_meta));
        } else {
            DN_LOG(WARNING) << _name << "rewrite find next record failed, zone_id:" << zone_meta.zone_id
                                    << " offset:" << iterator.offset()
                                    << " status:" << status;
            return status;
        }
    }

    // Handle records that have not been flushed
    for (size_t i = 0; i < rewrite_bufs.size(); ++i) {
        write_buffer.set_size(rewrite_bufs[i].size());
        rewrite_bufs[i].copy_to(write_buffer.buf());
        Status status = flush_buf_to_disk(write_buffer, record_metas[i], i);
        unlikely_if (!status.ok()) {
            DN_LOG(WARNING) << _name << "append records failed, status:" << status;
            return status;
        }
    }

    Status status = free_zone(rewritten_zone_id);
    unlikely_if (!status.ok()) {
        DN_LOG(WARNING) << _name << "rewrite zone failed due to free zone failed,"
                                << " zone_id:" << rewritten_zone_id
                                << " status:" << status;
        return status;
    }

    _channel_write_stats_collector.record_rewrite_zone(zone_meta.used_size);
    DN_LOG(NOTICE) << _name << "finish rewrite zone, zone_id:" << rewritten_zone_id;
    return Status();
}

ChannelWriteStats ChannelGroup::get_channel_stat() {
    return _channel_write_stats_collector.get();
}

uint64_t ChannelGroup::add_byte_time(size_t size) {
    return _time_generator->fetch_and_add(size);
}

int ChannelGroup::prepare_open_zones() {
    for (auto& channel : _user_channel_writers) {
        int ret = channel->prepare_open_zones();
        unlikely_if (ret != AIE_OK) {
            return ret;
        }
    }

    for (auto& channel : _rewrite_channel_writers) {
        int ret = channel->prepare_open_zones();
        unlikely_if (ret != AIE_OK) {
            return ret;
        }
    }

    return AIE_OK;
}

Status ChannelGroup::check_rewrite_index(const ZoneRecordInfo& record_meta) {
    const ZoneRecordIndexKey& key = record_meta.index_key;

    std::string index_entry_str;
    rocksdb::Status s = _db->Get({}, key.to_slice(), &index_entry_str);
    if (!s.ok()) {
        if (s.IsNotFound()) {
            // record may have been finally deleted during rewrite
        } else {
            DN_LOG(WARNING) << _name << "check rewrite index failed,"
                            << " key:" << key
                            << " status:" << Status(s);
        }
    }
    return Status(s);
}

Status ChannelGroup::flush_buf_to_disk(
        common::Buffer& buffer,
        const std::list<ZoneRecordInfo>& record_metas,
        const size_t rewrite_channel_index) {
    if (record_metas.empty()) {
        return Status();
    }

    Status s = try_acquire_rewrite_flow(buffer.size(), WRITE);
    unlikely_if (!s.ok()) {
        DN_LOG(WARNING) << _name << "rewrite write acquire_flow failed," << " size:" << buffer.size() << " status:" << s;
        return s;
    }

    std::vector<size_t> record_sizes;
    for (auto& record_meta : record_metas) {
        record_sizes.push_back(record_meta.index_entry.record_length());
    }
    ZoneBatchAppendContext context(&buffer, std::move(record_sizes));
    context.is_rewrite = true;
    int ret = _rewrite_channel_writers[rewrite_channel_index]->batch_append(&context);
    unlikely_if (ret != AIE_OK) {
        DN_LOG(WARNING) << _name << "rewrite channel batch append failed,"
                        << " rewrite_channel_index:" << rewrite_channel_index;
        return ret;
    }

    ChannelID channel_id = _rewrite_channel_writers[rewrite_channel_index]->channel_id();
    _channel_write_stats_collector.record_rewrite_channel_io(channel_id, buffer.size());

    auto curr_meta_iter = record_metas.begin();
    for (const auto& info : context.partial_infos) {
        const ZoneRecordLocation& write_loc = info.location;
        uint64_t total_create_time = 0;
        uint64_t total_len = 0;
        // update index
        ZoneRecordIndexEntry new_entry;
        new_entry.location = write_loc;

        int64_t zone_used_size = _zone_system->update_zone_used_size(write_loc.zone_id, write_loc.length);
        unlikely_if (zone_used_size < 0) {
            DN_LOG(FATAL) << _name << "update zone used size in rewrite make zone used size illegal,"
                          << " zone_id:" << write_loc.zone_id
                          << " delta:" << write_loc.length
                          << " used_size:" << zone_used_size;
        }
        SCOPE_EXIT {
            unlikely_if (total_len != write_loc.length) {
                int64_t delta = int64_t(total_len) - int64_t(write_loc.length);
                int64_t zone_used_size = _zone_system->update_zone_used_size(write_loc.zone_id, delta);
                unlikely_if (zone_used_size < 0) {
                    DN_LOG(FATAL) << _name << "update zone used size in rewrite make zone used size illegal,"
                                  << " zone_id:" << write_loc.zone_id
                                  << " delta:" << delta
                                  << " used_size:" << zone_used_size;
                }
            }
            if (total_create_time != 0 && total_len != 0) {
                _zone_system->update_zone_age(write_loc.zone_id, total_len, total_create_time / total_len);
            }
        };

        TEST_SYNC_POINT_LATENCY_US("ChannelGroup::rewrite::latency_before_update_index");

        for (size_t i = 0; i < info.num_record; ++i, ++curr_meta_iter) {
            auto& record_meta = *curr_meta_iter;
            const ZoneRecordIndexKey& key = record_meta.index_key;
            new_entry.location.length = record_meta.index_entry.record_length();
            new_entry.seq_no = info.seq_no;
            // update index(location only)
            base::get_leaky_singleton<RowLockManager>()->lock_row(key.vid(), key.vbid());
            Status status = ZoneRecordDBIndexer::update_location(*_db, key, new_entry);
            base::get_leaky_singleton<RowLockManager>()->unlock_row(key.vid(), key.vbid());

            if (status.code() == AIE_BLOB_NOT_EXIST) {
                // pass: record is removed
            } else if (!status.ok()) {
                DN_LOG(WARNING) << _name << "update record index entry failed in rewrite,"
                                << " key:" << key
                                << " old_index:" << record_meta.index_entry
                                << " status:" << status;
                return status;
            } else {
                // calculate zone average create time for rewrite choose
                total_create_time += record_meta.create_byte_time * new_entry.location.length;
                total_len += new_entry.location.length;
                DN_LOG(TRACE) << _name << "update record index entry success in rewrite,"
                              << " key:" << key
                              << " old_index:" << record_meta.index_entry
                              << " new_index:" << new_entry;
            }

            new_entry.location.offset += new_entry.location.length;
        }
    }
    return Status();
}

Status ChannelGroup::update_channel_threshold(const uint64_t zone_age) {
    uint64_t refresh_L = 0;
    auto update_threshold = [zone_age, &refresh_L](ChannelThreshold& channel_threshold) {
        refresh_L = channel_threshold.update_threshold(zone_age);
        return true;
    };
    _channel_threshold.Modify(update_threshold);
    _data_cold_hot_threshold.set_value(refresh_L);

    return Status();
}

Status ChannelGroup::free_zone(const ZoneID zone_id) {
    auto zone_meta_opt = _zone_system->get_zone_meta(zone_id);
    if (!zone_meta_opt.has_value()) {
        DN_LOG(WARNING) << _name << "free zone failed due to zone meta not exist,"
                        << " zone_id:" << zone_id;
        return Status(AIE_NOT_EXIST, "zone meta not exist");
    }

    ZoneMeta zone = zone_meta_opt.value();
    ZoneMetaPersistor persistor(_zone_device_backend, _zone_indexer,
                                zone.channel_group_id, zone.channel_id);
    int ret = persistor.free_zone(zone);
    unlikely_if (ret != AIE_OK) {
        DN_LOG(WARNING) << _name << "free zone failed, zone_id:" << zone_id << " error:" << ret;
        return Status(ret);
    }
    _zone_system->remove_zone_meta(zone_id);
    return Status();
}

}  // namespace datanode
}  // namespace aries
