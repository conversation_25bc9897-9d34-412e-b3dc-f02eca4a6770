// Copyright 2022 Baidu Inc. All Rights Reserved.

#pragma once

#include <memory>
#include <optional>
#include "folly/Executor.h"
#include "baidu/inf/aries/common/monotonic_clock.h"
#include "baidu/inf/aries/common/record/record_serializer.h"
#include "baidu/inf/aries/datanode/storage/zone/zone_fd.h"
#include "baidu/inf/aries/datanode/storage/zone/zone_record.h"

namespace aries {
namespace datanode {

class ZoneDisk;
class ZoneMetaPersistor;
class ZoneMetaDBIndexer;
class ZoneDeviceBackend;
class ChannelGroup;

struct OpenZoneInfo {
    const ZoneFD* zone_fd{nullptr};
    uint64_t zone_seq_no{0};
    uint64_t write_pointer{0};
    common::Gate* zone_gate;
};

class ChannelPreparedOpenZonePool {
public:
    explicit ChannelPreparedOpenZonePool(uint32_t max_open_zone_num = FLAGS_user_channel_prepared_open_zone_count)
        : _max_open_zone_num(max_open_zone_num) {}

    int push_zone(const OpenZoneInfo& zone_info);

    int pop_zone(OpenZoneInfo* zone_info);

    uint32_t remaining_zone_count() const;

private:
    uint32_t _max_open_zone_num{0};
    std::queue<OpenZoneInfo> _queue;
    folly::SharedMutex _mutex;
};


// Context with information used in zone append IO path.
// e.g. ZoneStore -> ZoneDisk -> ChannelGroup -> ChannelWriter.
// Note: the same ZoneAppendContext CANNOT be used for multiple append operations.
//
// `record_buf' SHOULD be in standard record format, and it will be modified!
// `record_seq_no' is generated by ChannelWriter before writing to disk.
struct ZoneAppendContext {
    ZoneAppendContext(common::Buffer* buffer, uint64_t vid, uint64_t vbid) :
            record_buf(buffer), vid(vid), vbid(vbid) {}

    ~ZoneAppendContext() {
        if (gate != nullptr) {
            gate->leave();
            gate = nullptr;
        }
    }

    common::Buffer* record_buf{nullptr};  // input
    uint64_t vid;                         // input
    uint64_t vbid;                        // input
    uint64_t record_seq_no{0};            // output
    ZoneRecordLocation location{};        // output
    uint64_t create_byte_time{0};         // output
    common::Gate* gate{nullptr};          // output

    DISALLOW_COPY_AND_ASSIGN(ZoneAppendContext);
};

// Context with information for batch append.
// Note: the same ZoneBatchAppendContext CANNOT be used for multiple append operations.
//
// Array `record_sizes' keeps the size of the records.  Notice the batch
// may be written to different zones due to current zone's left size is
// insufficient.  The output array `partial_infos' keeps the information
// (write location, num of record written, sequence number, create byte
// time) of each zone.
struct ZoneBatchAppendContext {
    ZoneBatchAppendContext(common::Buffer* buffer, std::vector<size_t>&& sizes)
                : record_buf(buffer), record_sizes(std::move(sizes)) {}

    ~ZoneBatchAppendContext() {
        for (auto& info : partial_infos) {
            if (info.gate != nullptr) {
                info.gate->leave();
                info.gate = nullptr;
            }
        }
    }

    struct PartialInfo {
        ZoneRecordLocation location{};
        size_t num_record{0};
        uint64_t seq_no{0};
        uint64_t create_byte_time{0};
        common::Gate* gate{nullptr};
    };

    common::Buffer* record_buf {nullptr};   // input
    std::vector<size_t> record_sizes;       // input
    bool is_rewrite{false};                 // input
    std::vector<PartialInfo> partial_infos; // output

    DISALLOW_COPY_AND_ASSIGN(ZoneBatchAppendContext);
};

class ChannelWriter {
public:
    ChannelWriter(ZoneDeviceBackend* device,
                  ZoneDisk* zone_system,
                  ZoneMetaDBIndexer* indexer,
                  ChannelGroup* channel_group,
                  ChannelID channel_id,
                  bool is_rewrite_channel);

    virtual ~ChannelWriter();

    /**
     *  Append write data asynchronously, after append write op had done, callback done
     *
     * @param  context [in+out] - 需要写入的数据
     * @param  done [in] - 写入数据完成后需要执行的回调函数，返回的location在done中处理
     * @return status
     */
    virtual int async_append(ZoneAppendContext* context, std::function<void(int err, const ZoneRecordLocation& location)>&& done) = 0;

    /**
     *  Append write data
     *
     * @param  context [in+out] - 需要写入的数据
     * @param  location [out] - 写入数据完成后返回的磁盘location
     * @return status
     */
    virtual int append(ZoneAppendContext* context) = 0;

    virtual int batch_append(ZoneBatchAppendContext* context) = 0;

    virtual int prepare_open_zones()  = 0;

    ChannelGroupID channel_group_id() const {
        return _channel_group_id;
    }

    ChannelID channel_id() const {
        return _channel_id;
    }

public:
    static uint64_t gen_record_seq_no() {
        return common::monotonic_gettimeofday_us();
    }

    uint64_t fill_footer(common::Buffer* record, uint64_t create_byte_time) {
        uint64_t seq_no = gen_record_seq_no();
        _record_serializer->fill_footer(record->buf(), record->size(), seq_no, create_byte_time);
        return seq_no;
    }

protected:
    ZoneDeviceBackend* _device{nullptr};
    ZoneDisk* _zone_system{nullptr};
    ZoneMetaDBIndexer* _indexer{nullptr};
    ChannelGroup* _channel_group;
    ChannelID _channel_id{-1};
    bool _is_rewrite_channel;
    ChannelGroupID _channel_group_id{-1};
    const std::string _name;

    OpenZoneInfo _curr_zone;

    ZoneRecordSerializer* _record_serializer{nullptr};
    ZoneMetaPersistor* _zone_meta_persistor{nullptr};
};

class FileChannelWriter : public ChannelWriter {
public:
    FileChannelWriter(ZoneDeviceBackend* device, ZoneDisk* zone_system, ZoneMetaDBIndexer* indexer,
                      ChannelGroup* channel_group, ChannelID channel_id, bool is_rewrite_channel);
    ~FileChannelWriter() = default;

    int async_append(ZoneAppendContext* context,
                     std::function<void(int err, const ZoneRecordLocation& location)>&& done) override;

    int append(ZoneAppendContext* context) override;

    int batch_append(ZoneBatchAppendContext* context) override;

    int prepare_open_zones() override;

private:
    size_t get_curr_zone_left_size();
    int allocate_write_location(size_t record_len, ZoneFD const** zone_fd, ZoneID* target_zone, uint64_t* offset, common::Gate** gate);
    int allocate_write_location(std::vector<size_t>::iterator record_sizes_iter_begin,
            std::vector<size_t>::iterator record_sizes_iter_end, ZoneFD const** zone_fd,
            ZoneID* target_zone, uint64_t* offset, size_t* num_record, size_t* sum_record_size, common::Gate** gate);
    int switch_to_new_zone();
    int allocate_new_zone(OpenZoneInfo* zone_info);
    bool is_record_size_valid(size_t record_len);

    int open_new_zone(OpenZoneInfo* zone_info);
    void async_open_new_zones();
    int close_zone(const ZoneID& zone_id, uint64_t write_pointer);
private:
    mutable common::MutexLock _alloc_lock;
    int64_t _max_record_length{0};
    ChannelPreparedOpenZonePool _prepared_open_pool;
    std::atomic<int32_t> _pending_bg_prepare_open_task_count{0};
};


}
}
