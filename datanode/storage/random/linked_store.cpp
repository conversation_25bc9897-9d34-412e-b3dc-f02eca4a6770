/***************************************************************************
 * 
 * Copyright (c) 2017 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/
/**
 * @file datanode/storage/linked_store.cpp
 * <AUTHOR>
 * @date 2017/10/21 16:05:30
 * @brief 
 *  
 **/

#include <memory>
#include <malloc.h>
#include <fcntl.h>

#include <base/crc32c.h>

#include "baidu/inf/aries/common/aries_ut_pread.h"
#include "baidu/inf/aries/datanode/copy_vlet.h"
#include "baidu/inf/aries/datanode/flags.h"
#include "baidu/inf/aries/datanode/storage/random/linked_store.h"
#include "baidu/inf/aries/datanode/storage/random/linked_shard_record.h"
#include "baidu/inf/aries/common/record/standard_record.h"
#include "baidu/inf/aries-api/common/bvar_define.h"
#include "baidu/inf/aries-api/common/lock.h"
#include "baidu/inf/aries-api/common/utils.h"

#ifndef FALLOC_FL_CONVERT_AND_EXTEND
#define FALLOC_FL_CONVERT_AND_EXTEND 0x100
#endif

namespace aries {
namespace datanode {

ARIES_BVAR_COUNTER(datanode, fast_remove);
ARIES_BVAR_COUNTER(datanode, holes);
ARIES_BVAR_COUNTER(datanode, filled_hole);
ARIES_BVAR_LATENCY(datanode, fill_holes);

bvar::LatencyRecorder g_engine_store_index_get_latency("engine", "index_get");

using aries::common::ScopedFutexLock;
const unsigned int sync_file_range_flag = SYNC_FILE_RANGE_WAIT_BEFORE | SYNC_FILE_RANGE_WRITE | SYNC_FILE_RANGE_WAIT_AFTER;

int LinkedStore::posix_write(int fd, const void *buf, int64_t size_bytes, int64_t offset_bytes) {
    ssize_t written = ::pwrite(fd, buf, size_bytes, offset_bytes);
    if (written == size_bytes) {
        return 0;
    }
    LOG(WARNING) << "write error, fd:" << fd << " buf:" << buf << " offset:" << offset_bytes
                 << " size:" << size_bytes << " written:" << written
                 << " errno:" << errno << " (" << strerror(errno) << ")";
    return errno;
}

int LinkedStore::posix_read(int fd, void *buf, int64_t size_bytes, int64_t offset_bytes) {
    uint64_t start = base::gettimeofday_us();
#ifdef _UNIT_TEST
    ssize_t bytes_read = ::aries_ut_pread(fd, buf, size_bytes, offset_bytes);
#else
    ssize_t bytes_read = ::pread(fd, buf, size_bytes, offset_bytes);
#endif
    if (bytes_read == size_bytes) {
        g_engine_diskio_read_latency << base::gettimeofday_us() - start;
        return 0;
    }
    LOG(WARNING) << "read error, fd:" << fd << " buf:" << buf << " offset:" << offset_bytes
                 << " size:" << size_bytes << " read:" << bytes_read
                 << " errno:" << errno << " (" << strerror(errno) << ")";
    return errno;
}

int LinkedStore::inline_posix_read(int fd, void* buf, int64_t size_bytes, int64_t offset_bytes) {
    _io_stats_collector.record_read(size_bytes);
    return posix_read(fd, buf, size_bytes, offset_bytes);
}
int LinkedStore::inline_posix_write(int fd, const void* buf, int64_t size_bytes, int64_t offset_bytes) {
    _io_stats_collector.record_write(size_bytes);
    return posix_write(fd, buf, size_bytes, offset_bytes);
}

int LinkedStore::preallocate_space(int fd, off_t size) {
    if (!FLAGS_create_vlet_file_only_by_write) {
        if (::fallocate(fd, FALLOC_FL_CONVERT_AND_EXTEND, 0, size) == 0) {
            return 0;
        }
        if (errno == EOPNOTSUPP) {
            LOG(NOTICE) << "fallocate failed due to unsupported mode:FALLOC_FL_CONVERT_AND_EXTEND"
                        << ", try without this mode, file:" << _filename;
            if (::fallocate(fd, 0, 0, size) == 0) {
                LOG(TRACE) << "preallocate succeeded, file:" << _filename;
                return 0;
            }
        }
        LOG(WARNING) << "fallocate failed, file:" << _filename << " size:" << size
                     << " errno:" << errno << " (" << strerror(errno) << ")";
    }
    // try add a large file.
    char *buffer = (char *) ::memalign(_page_size, 2 * 1024 * 1024);
    assert(buffer != NULL);
    std::unique_ptr<char, decltype(&::free)> buffer_guard(buffer, &::free);
    memset((void*)buffer, 0, 2 * 1024 * 1024);
    while (size) {
        if (_create_worker != nullptr && _create_worker->is_interrupted()) {
            LOG(TRACE) << "interrupt creating process, create vlet log_id:" <<
                _create_worker->log_id();
            return -1;
        }
        usleep(FLAGS_create_vlet_sleep_ms * 1000);
        int len = std::min((uint64_t)size, (uint64_t)(2 * 1024 * 1024));
        int ret = write(fd, buffer, len);
        if (ret != len) {
            break;
        }
        assert(size >= ret);
        size -= ret;
    }
    if (size == 0) {
        LOG(TRACE) << "preallocate succeeded by execute full write,"
                   << " file:" << _filename;
        return 0;
    }
    LOG(WARNING) << "preallocate failed, file:" << _filename << " size:" << size
                 << " errno:" << errno << " (" << strerror(errno) << ")";
    return -1;
}

LinkedStore::LinkedStore(const StoreOptions &opts) {
    _opts = opts;
    _indexer = NULL;
    _dio_fd = -1;
    _buffer_io_fd = -1;
    _max_vbid = 0;

    _min_record_page_num = 0;
    _max_record_page_num = 0;
    _record_gap_page_num = 0;
    _max_record_type = 0;
    _block_page_num = 0;
    _block_size = 0;
    _block_num = 0;
    _record_containers = NULL;
    _total_record_num = 0;
    _mark_deleted_record_num = 0;
    _hole_record_num = 0;
    _max_holes_size_for_fast_remove = 0;
    _min_record_size_for_fast_remove = 0;
    _permit_fast_remove = false;
    _permit_data_offset_index = false;
    _use_standard_record_layout = false;
    _page_size = 4096;
    _fsync_when_create_and_close = opts.fsync_when_create_and_close;
}

LinkedStore::~LinkedStore() {
    if (_indexer != NULL) {
        delete _indexer;
    }
    if (_record_containers != NULL) {
        delete[] _record_containers;
    }
    if (_buffer_io_fd >= 0) {
        if (_fsync_when_create_and_close) {
            fsync(_buffer_io_fd);
        }
        ::close(_buffer_io_fd);
    }
    if (_dio_fd >= 0) {
        // sync dio fd in case of disk cache open
        fsync(_dio_fd);
        ::close(_dio_fd);
    }
}

void LinkedStore::init_meta(const aries::pb::LinkedVletInfo& vlet_info) {
    _vlet_info = vlet_info;
    _min_record_page_num = vlet_info.min_record_page_num();
    _max_record_page_num = vlet_info.max_record_page_num();
    _record_gap_page_num = vlet_info.record_gap_page_num();

    _max_record_type = 1 + common::calc_ceil(_max_record_page_num - _min_record_page_num,
            _record_gap_page_num);

    _permit_fast_remove = vlet_info.permit_fast_remove();
    _max_holes_size_for_fast_remove = vlet_info.max_holes_size_for_fast_remove();
    _min_record_size_for_fast_remove = vlet_info.min_record_size_for_fast_remove();
    _permit_data_offset_index = vlet_info.permit_data_offset_index();
    _use_standard_record_layout = vlet_info.use_standard_record_layout();

    _page_size = vlet_info.page_size();
    _block_page_num = vlet_info.block_page_num();
    _block_size = _block_page_num * _page_size;
    _block_num = vlet_info.total_page_num() / _block_page_num;
    _record_containers = new RecordContainer[_max_record_type + 1];
    _block_bitmap.init(_block_num);
    std::ostringstream oss;
    oss << "L_" << vlet_info.volume_id() << "_" << vlet_info.shard_index();
    _name = oss.str();
    LOG(NOTICE) << "init meta, file name:" << _filename
                << "fsync_when_create_and_close" << _fsync_when_create_and_close;
}

Status LinkedStore::create(const aries::pb::LinkedVletInfo& vlet_info, std::shared_ptr<rocksdb::DB> db,
        const std::string &path) {
    base::Timer time_l;
    time_l.start();
    _filename = path;
    _volume_id = vlet_info.volume_id();
    _shard_index = vlet_info.shard_index();
    _indexer = LinkedStoreIndexer::create(vlet_info.volume_id(), vlet_info.vlet_type(), db);
    _linked_record_serializer.reset(new LinkedRecordSerializer(vlet_info, path));
    _standard_record_serializer.reset(new common::StandardRecordSerializer(vlet_info, path));
    init_meta(vlet_info);
    if (create_access(path) != 0) {
        LOG(WARNING) << "create LinkedStore failed due to create vlet directory failed, file:" << path
                     << " errno:" << errno << " (" << strerror(errno) << ")";
        return Status::by_errno("create directory failed");
    }
    int woflags = O_RDWR | O_CREAT | O_EXCL | O_DIRECT;
    _dio_fd = ::open(path.c_str(), woflags, 0644);
    if (_dio_fd < 0) {
        LOG(WARNING) << "create LinkedStore failed due to open vlet file failed, file:" << path
                     << " errno:" << errno << " (" << strerror(errno) << ")";
        return Status::by_errno("open vlet file failed");
    }
    off_t data_size = (off_t) _block_size * _block_num;
    // allocate a big file, ensure that space is really allocated in disk sectors
    if (preallocate_space(_dio_fd, data_size + PAGE_SIZE) != 0) {
        LOG(WARNING) << "create LinkedStore failed due to fallocate failed, file:" << path
                     << " errno:" << errno << " (" << strerror(errno) << ")";
        ::unlink(path.c_str());
        return Status::by_errno("preallocate failed");
    }
    Status s = write_footer(vlet_info);
    if (!s.ok()) {
        LOG(WARNING) << "create LinkedStore failed due to write footer failed, file:" << path
                     << " error:" << s;
        ::unlink(path.c_str());
        return s;
    }

    int roflags = O_RDWR;
    _buffer_io_fd = ::open(path.c_str(), roflags, 0644);
    if (_buffer_io_fd < 0) {
        LOG(WARNING) << "create LinkedStore failed due to open read-only fd failed, file:" << path
                     << " errno:" << errno << " (" << strerror(errno) << ")";
        ::unlink(path.c_str());
        return Status::by_errno("open vlet file failed");
    }
    if (_fsync_when_create_and_close) {
        auto fsync_start_time = base::gettimeofday_us();
        int ret = fsync(_buffer_io_fd);
        if (ret != 0) {
            LOG(WARNING) << "fsync LinkedStore failed, file:" << path
                        << " errno:(" << errno << ")" << strerror(errno);
            ::unlink(path.c_str());
            return Status::by_errno("open vlet failed due to fsync file failed");
        }
        LOG(NOTICE) << "fsync linked store file cost time:"
                    << base::gettimeofday_us() - fsync_start_time
                    << "path:" << path
                    << " db:" << db->GetName();
    }
    if (_fsync_when_create_and_close) {
        auto fsync_start_time = base::gettimeofday_us();
        int ret = fsync(_buffer_io_fd);
        if (ret != 0) {
            LOG(WARNING) << "fsync LinkedStore failed, file:" << path
                        << " errno:(" << errno << ")" << strerror(errno);
            ::unlink(path.c_str());
            return Status::by_errno("open vlet failed due to fsync file failed");
        }
        LOG(NOTICE) << "fsync linked store file cost time:"
                    << base::gettimeofday_us() - fsync_start_time
                    << "path:" << path
                    << " db:" << db->GetName();
    }
    time_l.stop();
    LOG(NOTICE) << "create LinkedStore succeeded, file:" << path
                << " db:" << db->GetName()
                << " cost:" << time_l.u_elapsed() << "us";
    return Status(AIE_OK);
}

Status LinkedStore::open(std::shared_ptr<rocksdb::DB> db, const std::string &path,
                         bool readonly) {
    _filename = path;
    int oflags = O_RDWR | O_DIRECT;

    if (readonly) {
        oflags = O_RDONLY | O_DIRECT;
    }
    _dio_fd = ::open(path.c_str(), oflags, 0644);
    if (_dio_fd < 0) {
        LOG(WARNING) << "open LinkedStore failed due to open vlet file failed, file:" << path
                     << " errno:" << errno << " (" << strerror(errno) << ")";
        return Status::by_errno("open linked store failed");
    }

    oflags = O_RDWR;
    if (readonly) {
        oflags = O_RDONLY;
    }
    _buffer_io_fd = ::open(path.c_str(), oflags, 0644);
    if (_buffer_io_fd < 0) {
        LOG(WARNING) << "open LinkedStore failed due to open vlet file failed, file:" << path
                     << " errno:" << errno << " (" << strerror(errno) << ")";
        return Status::by_errno("open linked store failed");
    }

    aries::pb::LinkedVletInfo footer;
    Status s = read_footer(&footer);
    if (!s.ok()) {
        LOG(WARNING) << "open LinkedStore failed due to read file footer failed,"
                     << " file:" << path << " error:" << s.to_string();
        return s;
    }
    init_meta(footer);
    _linked_record_serializer.reset(new LinkedRecordSerializer(footer, path));
    _standard_record_serializer.reset(new common::StandardRecordSerializer(footer, path));
    _indexer = LinkedStoreIndexer::create(footer.volume_id(), footer.vlet_type(), db);
    _volume_id = _vlet_info.volume_id();
    if (_opts.recover_on_open) {
        s = check_db();
        if (!s.ok()) {
            LOG(WARNING) << "open LinkedStore failed due to db has vlet's data, file:" << path
                << " error:" << s.to_string();
            return s;
        }
        s = recover_index();
        if (!s.ok()) {
            LOG(WARNING) << "open LinkedStore failed due to recover index failed, file:" << path
                << " error:" << s.to_string();
            return s;
        }
    }
    s = build_location_map();
    if (!s.ok()) {
        LOG(WARNING) << "open LinkedStore failed due to load index failed, file:" << path
            << " error:" << s.to_string();
        return s;
    }
    LOG(NOTICE) << "open LinkedStore succeeded, file:" << path << " db:" << db->GetName();
    return Status(AIE_OK);
}

Status LinkedStore::destroy() {
    if (_filename.empty()) {
        return Status();
    }
    std::string dir = ".";
    size_t pos = _filename.rfind('/');
    if (pos != std::string::npos) {
        dir = _filename.substr(0, pos);
    }
    int64_t timestamp = base::gettimeofday_us();
    time_t now = timestamp / 1000000;
    struct tm t;
    char buf[128];
    localtime_r(&now, &t);
    strftime(buf, sizeof(buf), "%Y%m%d-%H:%M:%S", &t); 
    char trash_path[_filename.size() + 128];
    snprintf(trash_path, sizeof(trash_path), "%s/trash/%s.%s-%06luZ", dir.c_str(), _name.c_str(),
            buf, (uint64_t)timestamp % 1000000);
    if (create_access(trash_path) != 0) {
        LOG(WARNING) << "destroy LinkedStore failed due to create trash directory failed,"
                     << " trash_path:" << trash_path << " errno:" << errno
                     << " (" << strerror(errno) << ")";
        return Status::by_errno("create trash directory failed");
    }
    if (_fsync_when_create_and_close) {
        auto fsync_start_time = base::gettimeofday_us();
        fsync(_buffer_io_fd);
        LOG(NOTICE) << "fsync linked store file cost time:"
                    << base::gettimeofday_us() - fsync_start_time
                    << "path:" << _filename;
        // call fsync before rename
    }

    if (rename(_filename.c_str(), trash_path) != 0) {
        if (errno != ENOENT) {
            Status s = Status::by_errno("rename failed");
            LOG(WARNING) << "destroy LinkedStore failed due to move vlet file into trash failed,"
                         << " from:" << _filename << " to:" << trash_path << " error:" << s.to_string();
            return s;
        }
    }

    if (_indexer != nullptr) {
        _indexer->destroy();
    }
    LOG(WARNING) << "destroy LinkedStore succeeded, file:" << _filename;
    return Status();
}

Status LinkedStore::write_footer(const aries::pb::LinkedVletInfo &footer) {
    char *buf = (char *) ::memalign(PAGE_SIZE, PAGE_SIZE);
    assert(buf != NULL);
    std::unique_ptr<char, decltype(&::free)> buffer(buf, &::free);
    memset(buf, 0, PAGE_SIZE);
    // serialize should success in any condition
    assert(footer.SerializeToArray(buf, PAGE_SIZE));

    if (::pwrite(_dio_fd, buf, PAGE_SIZE, _block_size * _block_num) != PAGE_SIZE) {
        LOG(WARNING) << "write footer failed, file:" << _filename
                     << " errno:" << errno << " (" << strerror(errno) << ")";
        return Status::by_errno("write footer failed");
    }
    return Status();
}

Status LinkedStore::read_footer(aries::pb::LinkedVletInfo *footer) {
    struct stat st;
    if (fstat(_dio_fd, &st) != 0) {
        LOG(WARNING) << "stat vlet file failed, file:" << _filename
                     << " errno:" << errno << " (" << strerror(errno) << ")";
        return Status::by_errno("stat vlet file error");
    }
    char *buf = (char *) ::memalign(PAGE_SIZE, PAGE_SIZE);
    assert(buf != NULL);
    std::unique_ptr<char, decltype(&::free)> buffer(buf, &::free);
    int ret = inline_posix_read(_dio_fd, buf, PAGE_SIZE, st.st_size - PAGE_SIZE);
    if (ret != 0) {
        LOG(WARNING) << "read vlet info from vlet file failed, file:" << _filename
                     << " offset:" << (st.st_size - PAGE_SIZE)
                     << " errno:" << errno << " (" << strerror(errno) << ")";
        return Status::by_errno(ret, "read vlet file failed");
    }
    // a trick way to be compatible with old format, which protobuf was written on page beginning
    if (buf[0] == 8) {
        int n = PAGE_SIZE;
        while (n > 0 && buf[n - 1] == 0) {
            --n;
        }
        while (!footer->ParseFromArray(buf, n)) {
            if (n > (int) PAGE_SIZE) {
                LOG(FATAL) << "vlet_info protobuf size may exceed the PAGE_SIZE which is " << PAGE_SIZE
                           << " file:" << _filename;
                return Status(AIE_CORRUPT, "vlet info corrupt");
            }
            ++n;
        }
        return Status();
    }
    return Status(AIE_CORRUPT, "invalid footer format");
}

void LinkedStore::update_meta(const aries::pb::LinkedVletInfo& vlet_info) {
    std::unique_lock<std::mutex> lock_guard(_meta_mutex);
    _permit_fast_remove = vlet_info.permit_fast_remove();
    _max_holes_size_for_fast_remove = vlet_info.max_holes_size_for_fast_remove();
    _min_record_size_for_fast_remove = vlet_info.min_record_size_for_fast_remove();
    LOG(NOTICE) << "update vlet info, max_holes_size_for_fast_remove:"
                << " vid:" << _volume_id << " shard_index:" << _shard_index
                << _max_holes_size_for_fast_remove << " min_record_size_for_fast_remove:"
                << _min_record_size_for_fast_remove << " permit_fast_remove:" << _permit_fast_remove;
    ::aries::pb::LinkedVletInfo info;
    info.CopyFrom(_vlet_info);
    auto membership = info.mutable_membership();
    membership->CopyFrom(vlet_info.membership());
    info.mutable_shard_compress_option()->CopyFrom(vlet_info.shard_compress_option());
    _vlet_info = info;
}

Status LinkedStore::build_location_map() {
    std::unique_lock<std::mutex> lock_guard(_location_mutex);
    CheckLinkedListContext check_ctx;
    aries::pb::RecordLocationExtraInfo extra_info;

    // scan db
    std::unique_ptr<LinkedStoreIndexer::Iterator> it(_indexer->seek(0));
    while (it->valid()) {
        extra_info.Clear();
        const RecordLocation *loc = it->value(&extra_info);
        // ignore mark-delete record
        if (loc != NULL) {
            if (extra_info.has_mark_deleted_timestamp() && extra_info.mark_deleted_timestamp() > 0) {
                ++_mark_deleted_record_num;
            } else if (!extra_info.has_prev_hole_vbid() && !extra_info.has_next_hole_vbid()){
                update_fingerprint(it->key());
            }
            put_location(it->key(), *loc, extra_info);
            update_max_vbid(it->key());
            // for check linket list
            check_ctx.check_rid_map[loc->record_type].push_back(loc->record_id);
            if (extra_info.has_prev_hole_vbid()) {
                check_ctx.check_hole_next_list_map[loc->record_type][extra_info.prev_hole_vbid()] = it->key();
                check_ctx.check_hole_prev_list_map[loc->record_type][extra_info.next_hole_vbid()] = it->key();
            }
        }
        it->next();
    }
    return check_linked_list(check_ctx);
}

bool LinkedStore::check_rid_continuity(std::vector<uint32_t>& rid_vector) {
    //check rid Continuity
    std::sort(rid_vector.begin(), rid_vector.end());
    uint32_t rid_ = 0;
    uint32_t prev_rid = 0;
    for (auto& rid : rid_vector) {
        if (rid != ++rid_) {
            LOG(FATAL) << "rid not continuous, rid:" << rid << " prev_rid:" << prev_rid;
            return false;
        }
        prev_rid = rid;
    }
    return true;
}

bool LinkedStore::check_hole_continuity(std::map<uint64_t, uint64_t>& hole_map) {
    int64_t count = hole_map.size();
    uint64_t key = 0;
    while (hole_map.find(key) != hole_map.end()) {
        key = hole_map[key];
        if (--count < 0) {
            break;
        }
    }
    return (count == 0);
}

bool LinkedStore::check_prev_next_hole_list_consistency(
        std::map<uint64_t, uint64_t> &prev_hole_map,
        std::map<uint64_t, uint64_t> &next_hole_map) {
    uint64_t vbid = 0;
    while(next_hole_map.count(vbid)) {
        uint64_t next_vbid = next_hole_map[vbid];
        if (vbid != prev_hole_map[next_vbid]) {
            return false;
        }
        vbid = next_hole_map[vbid];
    }

    return prev_hole_map[0] == vbid;
}

Status LinkedStore::recover_index() {
    ShardRecordScanner sc(this);
    return sc.restore();
}

// recover_mode rebuild index demand db has no volume data
Status LinkedStore::check_db() {
    if (_indexer->has_volume_key()) {
        return Status(AIE_FAIL, "volume key already exists");
    }
    std::unique_ptr<LinkedStoreIndexer::Iterator> it(_indexer->seek(0));
    if (it->valid()) {
        return Status(AIE_FAIL, "shard key already exists");
    }
    return Status();
}

void LinkedStore::put_location(uint64_t vbid, const RecordLocation &loc,
                               const aries::pb::RecordLocationExtraInfo &extra_info) {
    assert(loc.record_type <= _max_record_type);
    RecordContainer &container = _record_containers[loc.record_type];
    if (extra_info.has_prev_hole_vbid()) {
        if (extra_info.prev_hole_vbid() == 0) {
            container.head_hole_vbid = vbid;
        }
        if (extra_info.next_hole_vbid() == 0) {
            container.tail_hole_vbid = vbid;
        }
        ARIES_DEBUG_LOG(TRACE) << "hole vbid:" << vbid << " loc:" << loc << " pb:" << common::pb2json(extra_info);
        ++container.hole_record_num;
        g_datanode_holes_counter << 1;
    }
    container.record_index = std::max(loc.record_id, container.record_index);
    uint32_t cell_id = ((loc.record_id - 1) * record_type_page_num(loc.record_type)) / _block_page_num;
    uint32_t cell_num = cell_id + 1;
    if (loc.next_block_id != 0) {
        ++cell_num;
    }
    if (container.cells.size() < cell_num) {
        container.cells.resize(cell_num, 0);
    }
    container.cells[cell_id] = loc.block_id;
    _block_bitmap.set_pos_used(loc.block_id);
    if (loc.next_block_id != 0) {
        container.cells[cell_id + 1] = loc.next_block_id;
        _block_bitmap.set_pos_used(loc.next_block_id);
    }
    ++_total_record_num;
}

int LinkedStore::alloc_location(uint32_t record_type, RecordLocation *location) {
    RecordContainer &container = _record_containers[record_type];
    uint32_t record_page_num = record_type_page_num(record_type);
    uint32_t offset = (container.record_index * record_page_num);
    uint32_t end_in_cell = (offset + _block_page_num - 1) % _block_page_num + 1;

    uint32_t block_id = 0;
    uint32_t next_block_id = 0;

    if (end_in_cell < _block_page_num) {
        block_id = container.cells.back();
    }

    // if remain room in a cell is not suit for the record, then need a new cell
    if ((end_in_cell + record_page_num) > _block_page_num) {
        next_block_id = _block_bitmap.allocate_pos();
        if (!next_block_id) {
            return -1;
        }
        container.cells.push_back(next_block_id);
        if (end_in_cell == _block_page_num) {
            block_id = next_block_id;
            next_block_id = 0;
        }
    }
    location->record_type = record_type;
    location->record_id = ++container.record_index;
    location->block_id = block_id;
    location->next_block_id = next_block_id;
    ++_total_record_num;
    return 0;
}

void LinkedStore::free_location(uint32_t record_type) {
    RecordContainer &container = _record_containers[record_type];
    if (container.record_index == 0) {
        return;
    }
    container.record_index--;
    uint32_t record_page_num = record_type_page_num(record_type);
    uint32_t offset = (container.record_index) * record_page_num;
    uint32_t cell_index = offset / _block_page_num;
    uint32_t offset_in_cell = offset % _block_page_num;
    if (offset_in_cell == 0) {
        _block_bitmap.free_pos(container.cells[cell_index]);
        container.cells.pop_back();
    } else if (offset_in_cell + record_page_num > _block_page_num) {
        _block_bitmap.free_pos(container.cells[cell_index + 1]);
        container.cells.pop_back();
    }
    --_total_record_num;
}

RecordLocation LinkedStore::get_location(uint32_t record_type) {
    RecordLocation tail;
    tail.record_type = record_type;
    tail.record_id = 0;
    tail.block_id = 0;
    tail.next_block_id = 0;
    RecordContainer &container = _record_containers[record_type];
    if (container.record_index == 0) {
        return tail;
    }
    uint32_t record_page_num = record_type_page_num(record_type);
    tail.record_id = container.record_index;
    uint32_t offset = (container.record_index - 1) * record_page_num;
    uint32_t cell_index = offset / _block_page_num;
    uint32_t offset_in_cell = offset % _block_page_num;

    tail.block_id = container.cells[cell_index];
    if (offset_in_cell + record_page_num > _block_page_num) {
        tail.next_block_id = container.cells[cell_index + 1];
    }
    return tail;
}

int LinkedStore::read_block(uint32_t block_id, uint32_t next_block_id, char *buf, uint32_t size,
        uint32_t offset, bool need_clean_buf) {
    int64_t record_bytes = size * _page_size;
    uint64_t offset_bytes = _page_size * ((block_id - 1) * _block_page_num + offset);
    bool is_use_dio = (need_clean_buf || !_opts.use_page_cache) && (_page_size == PAGE_SIZE);  // dio need 4k align

    if (next_block_id == 0 || next_block_id == block_id + 1) {
        int ret = 0;
        if (is_use_dio) {
            ret = inline_posix_read(_dio_fd, buf, record_bytes, offset_bytes);
        } else {
            ret = inline_posix_read(_buffer_io_fd, buf, record_bytes, offset_bytes);
        }
        return ret;
    }
    int64_t first_bytes = _page_size * (_block_page_num - offset);
    int ret = 0;
    if (is_use_dio) {
        ret = inline_posix_read(_dio_fd, buf, first_bytes, offset_bytes);
    } else {
        ret = inline_posix_read(_buffer_io_fd, buf, first_bytes, offset_bytes);
    }
    if (ret != 0) {
        return ret;
    }
    offset_bytes = (next_block_id - 1) * _block_size;
    if (is_use_dio) {
        ret = inline_posix_read(_dio_fd, buf + first_bytes, record_bytes - first_bytes, offset_bytes);
    } else {
        ret = inline_posix_read(_buffer_io_fd, buf + first_bytes, record_bytes - first_bytes, offset_bytes);
    }
    return ret;
}

int LinkedStore::range_read_block(uint32_t block_id, uint32_t next_block_id, char *buf, uint32_t size,
                                  uint32_t offset, bool need_clean_buf) {
    int ret = -1;
    int64_t data_bytes = size * _page_size;
    bool is_use_dio = (need_clean_buf || !_opts.use_page_cache) && (_page_size == PAGE_SIZE);  // dio need 4k align

    // check across block_id
    if (offset > _block_page_num || offset + size > _block_page_num) {
        if (next_block_id == 0) {
            LOG(WARNING) << " range read block failed by invalid argument,"
                << " block_id:" << block_id << " next_block_id:" << next_block_id
                << " data_size:" << size << " data_offset:" << offset << " file:" << _filename;
            return -1;
        }
    }

    // range read data in first block
    if (offset + size <= _block_page_num) {
        uint64_t offset_bytes = _page_size * ((block_id - 1) * _block_page_num + offset);
        if (is_use_dio) {
            ret = inline_posix_read(_dio_fd, buf, data_bytes, offset_bytes);
        } else {
            ret = inline_posix_read(_buffer_io_fd, buf, data_bytes, offset_bytes);
        }
    } else if (offset > _block_page_num) {  // range read data in secord block
        uint64_t next_block_offset_bytes = (next_block_id - 1) * _block_size + (offset - _block_page_num) * _page_size;
        if (is_use_dio) {
            ret = inline_posix_read(_dio_fd, buf, data_bytes, next_block_offset_bytes);
        } else {
            ret = inline_posix_read(_buffer_io_fd, buf, data_bytes, next_block_offset_bytes);
        }
    } else {    // range read data in two block
        int64_t first_data_bytes = _page_size * (_block_page_num - offset);
        uint64_t first_block_offset_bytes = _page_size * ((block_id - 1) * _block_page_num + offset);
        if (is_use_dio) {
            ret = inline_posix_read(_dio_fd, buf, first_data_bytes, first_block_offset_bytes);
        } else {
            ret = inline_posix_read(_buffer_io_fd, buf, first_data_bytes, first_block_offset_bytes);
        }
        if (ret != 0) {
            return ret;
        }
        int64_t last_data_bytes = data_bytes - first_data_bytes;
        uint64_t last_block_offset_bytes = (next_block_id - 1) * _block_size;
        if (is_use_dio) {
            ret = inline_posix_read(_dio_fd, buf + first_data_bytes, last_data_bytes, last_block_offset_bytes);
        } else {
            ret = inline_posix_read(_buffer_io_fd, buf + first_data_bytes, last_data_bytes, last_block_offset_bytes);
        }
    }

    return ret;
}

int LinkedStore::read_location(const RecordLocation &location, char *buf) {
    uint32_t record_page_num = record_type_page_num(location.record_type);
    uint32_t offset = ((location.record_id - 1) * record_page_num) % _block_page_num;
    return read_block(location.block_id, location.next_block_id, buf, record_page_num, offset, false);
}

int LinkedStore::write_location(const RecordLocation &location, const char *buf) {
    uint32_t record_page_num = record_type_page_num(location.record_type);
    int64_t record_bytes = record_page_num * _page_size;
    uint32_t offset = ((location.record_id - 1) * record_page_num) % _block_page_num;
    uint64_t offset_bytes = _page_size * ((location.block_id - 1) * _block_page_num + offset);
    int ret = 0;
    if (location.next_block_id == 0 || location.next_block_id == location.block_id + 1) {
        return write_buffer_or_dio(buf, record_bytes, offset_bytes);
    }
    int64_t first_bytes = _page_size * (_block_page_num - offset);
    ret = write_buffer_or_dio(buf, first_bytes, offset_bytes);
    if (ret != 0) {
        return ret;
    }
    offset_bytes = _page_size * (location.next_block_id - 1) * _block_page_num;
    ret = write_buffer_or_dio(buf+first_bytes, record_bytes - first_bytes, offset_bytes);
    return ret;
}

int LinkedStore::write_buffer_or_dio(const char* buf, int64_t size_bytes, int64_t offset_bytes) {
    int ret = 0;
    uint64_t start = base::gettimeofday_us();
    if (!FLAGS_use_direct_io || _page_size != PAGE_SIZE) {
        ret = inline_posix_write(_buffer_io_fd, buf, size_bytes, offset_bytes);
        if (ret != 0) {
            return ret;
        }
        if (FLAGS_use_sync_file_range) {
            sync_file_range(_buffer_io_fd, offset_bytes, size_bytes, sync_file_range_flag);
        }
    } else {
        ret = inline_posix_write(_dio_fd, buf, size_bytes, offset_bytes);
    }
    if (ret == 0) {
        g_engine_diskio_write_latency << base::gettimeofday_us() - start;
    }
    return ret;
}

Status LinkedStore::write_shard_record_internal(
        const RecordLocation &location,
        const char *buf,
        const bool use_standard_record_layout) {
    assert(sizeof(ShardRecordGuard) == SHARD_RECORD_GUARD_SIZE);
    int64_t record_bytes = record_type_page_num(location.record_type) * _page_size;
    ShardRecordGuard *guard = (ShardRecordGuard *)(buf + record_bytes - sizeof(ShardRecordGuard));
    guard->record_magic =
            use_standard_record_layout ? common::STANDARD_RECORD_FOOTER_MAGIC : SHARD_RECORD_MAGIC;
    guard->record_timestamp = base::gettimeofday_us();
    guard->record_id = location.record_id;
    guard->prev_block_id = location.block_id;
    guard->record_type = location.record_type;
    guard->reserved = 0;
    guard->record_crc32 = base::crc32c::Value(buf, record_bytes - sizeof(uint32_t));
    int ret = write_location(location, buf);
    if (ret != 0) {
        return Status::by_errno(ret, "write shard record failed");
    }
    return Status(AIE_OK);
}

Status LinkedStore::write_shard_record(
        const RecordLocation &location,
        const char *buf,
        const bool use_standard_record_layout) {
    common::TimeMeasure time_measure(&g_engine_record_write_latency);
    return write_shard_record_internal(location, buf, use_standard_record_layout);
}

Status LinkedStore::read_shard_record_internal(const RecordLocation &location, char *buf) {
    assert(sizeof(ShardRecordGuard) == SHARD_RECORD_GUARD_SIZE);
    int ret = read_location(location, buf);
    if (ret != 0) {
        return Status::by_errno(ret, "read shard record failed");
    }
    return Status(AIE_OK);
}

Status LinkedStore::read_shard_record(const RecordLocation &location, char *buf) {
    common::TimeMeasure time_measure(&g_engine_record_read_latency);
    return read_shard_record_internal(location, buf);
}

Status LinkedStore::move_shard_record(const RecordLocation &from, const RecordLocation &to,
        uint64_t *vbid, aries::pb::RecordLocationExtraInfo* extra_info) {
    assert(from.record_type == to.record_type);
    int64_t record_bytes = record_type_page_num(from.record_type) * _page_size;
    char *buf = (char *) ::memalign(_page_size, record_bytes);
    assert(buf != NULL);
    std::unique_ptr<char, decltype(&::free)> buffer(buf, &::free);

    Status s = read_shard_record(from, buf);
    if (!s.ok()) {
        LOG(WARNING) << "move record failed due to read tail record failed,"
                     << " from:" << from << " to:" << to << " error:" << s
                     << " file:" << _filename;
        return s;
    }

    RecordLocationHelper loc_helper(this);
    bool is_standard_record_layout = false;
    s = loc_helper.get_record_info(from, buf, vbid, extra_info, &is_standard_record_layout);
    if (!s.ok()) {
        LOG(FATAL) << "move record failed due to parse tail record failed,"
                   << " from:" << from << " to:" << to << " error:" << s
                   << " file:" << _filename;
        return s;
    }

    s = write_shard_record(to, buf, is_standard_record_layout);
    if (!s.ok()) {
        LOG(WARNING) << "move record failed due to write disk failed,"
                     << " from:" << from << " to:" << to << " error:" << s
                     << " file:" << _filename;
    } else {
        LOG(TRACE) << "move record succeeded,"
                   << " from:" << from << " to:" << to << " file:" << _filename;
    }

    return s;
}

void LinkedStore::get_serialize_config_with_lock(
        aries::pb::ShardCompressOption* compress_option) {
    std::unique_lock<std::mutex> lock_guard(_meta_mutex);
    if (_vlet_info.shard_index() < _vlet_info.space_info().k()) {
        compress_option->CopyFrom(_vlet_info.shard_compress_option());
    } else {
        compress_option->set_slice_split_size(_vlet_info.shard_compress_option().slice_split_size());
    }
}

Status LinkedStore::do_serialize_record(
        const uint64_t vbid,
        const base::IOBuf& data,
        const aries::pb::ShardMeta& meta,
        const aries::pb::ShardCompressOption& compress_option,
        common::Buffer* result,
        aries::pb::SliceDescMeta* slice_desc_meta,
        void* args) {
    common::TimeMeasure time_measure(&g_engine_serialize_record_latency);
    Status s;
    if (_use_standard_record_layout) {
        uint32_t shard_index = _vlet_info.shard_index();
        s = _standard_record_serializer->serialize(
                vbid,
                shard_index,
                data,
                meta,
                compress_option,
                result,
                slice_desc_meta,
                args);
    } else {
        s = _linked_record_serializer->serialize(
                vbid, data, meta, compress_option, result, slice_desc_meta, args);
    }
    return s;
}

// tradeoff: rewrite holes on store loading or lock every write & free
Status LinkedStore::put(const uint64_t vbid, const pb::ShardMeta &meta, const base::IOBuf &data) {
    uint64_t start = base::gettimeofday_us();
    RecordLocation location;
    aries::pb::RecordLocationExtraInfo extra_info;
    std::unique_lock<std::mutex> lock_guard(_location_mutex);
    Status s = _indexer->get(vbid, &location, &extra_info);
    if (s.ok() || extra_info.has_prev_hole_vbid()) {
        return Status(AIE_EXIST, "record already exist");
    }
    if (s.code() != AIE_BLOB_NOT_EXIST) {
        LOG(WARNING) << "put shard failed due to read indexer failed,"
                     << " vid:" << _volume_id << " vbid:" << vbid
                     << " shard_index:" << _shard_index << " error:" << s;
        return s;
    }

    aries::pb::ShardCompressOption compress_option;
    bool use_standard_record_layout = false;
    get_serialize_config_with_lock(&compress_option);

    //serialize record
    common::Buffer result;
    aries::pb::SliceDescMeta slice_desc_meta;
    uint32_t record_type;
    uint32_t record_data_offset;
    common::SerializeArgs serialize_args(sizeof(ShardRecordGuard) - sizeof(uint32_t), &record_type, &record_data_offset);
    s = do_serialize_record(vbid, data, meta, compress_option, &result, &slice_desc_meta, &serialize_args);
    if (!s.ok()) {
        LOG(WARNING) << "put shard failed due to serialize record failed,"
                     << " vid:" << _volume_id << " vbid:" << vbid
                     << " shard_index:" << _shard_index << " error:" << s;
        return s;
    }

    s = do_put(vbid, record_type, record_data_offset, slice_desc_meta, result);
    if (!s.ok()) {
        LOG(WARNING) << "put shard failed, vid:" << _volume_id << " vbid:" << vbid
                     << " shard_index:" << _shard_index << " error:" << s;
        return s;
    }

    update_fingerprint(vbid);
    update_max_vbid(vbid);
    g_engine_store_write_latency << base::gettimeofday_us() - start;
    return s;
}

Status LinkedStore::do_put(
            const uint64_t vbid,
            const uint32_t record_type,
            const uint32_t record_data_offset,
            const aries::pb::SliceDescMeta& slice_desc_meta,
            const common::Buffer& data_buffer) {
    RecordLocation location;
    RecordContainer& container = _record_containers[record_type];
    bool use_hole = ((int)container.hole_record_num > 0);
    Status s;
    if (use_hole) {
        aries::pb::RecordLocationExtraInfo extra_info;
        uint64_t hole_vbid = container.head_hole_vbid;
        // get hole vbid location
        s = _indexer->get(hole_vbid, &location, &extra_info);
        if (s.code() == AIE_IO_ERROR) {
            LOG(WARNING) << "put shard failed due to get hole vbid location failed,"
                         << " vid:" << _volume_id << " vbid:" << vbid
                         << " shard_index:" << _shard_index << " hole_vbid:" << hole_vbid
                         << " loc:" << location << " error:" << s;
            return s;
        }
        assert(s.code() == AIE_BLOB_NOT_EXIST && extra_info.has_prev_hole_vbid());
        // write shard data to hole location
        s = write_shard_record(location, data_buffer.buf(), _use_standard_record_layout);
        if (!s.ok()) {
            LOG(WARNING) << "put shard failed due to write disk failed,"
                         << " vid:" << _volume_id << " vbid:" << vbid
                         << " shard_index:" << _shard_index << " use_hole:" << use_hole
                         << " loc:" << location << " error:" << s;
            return s;
        }
        // build extra info for new shard
        aries::pb::RecordLocationExtraInfo record_extra_info;
        if (_use_standard_record_layout) {
            record_extra_info.mutable_slice_desc_meta()->CopyFrom(slice_desc_meta);
        }
        if (_permit_data_offset_index) {
            record_extra_info.set_record_data_offset(record_data_offset);
        }
        // create a rocksdb batch to update indexer
        std::unique_ptr<LinkedStoreIndexer::WriteBatch> batch(_indexer->create_batch());
        batch->put(vbid, location, &record_extra_info);
        s = update_hole_meta(batch.get(), container, hole_vbid, extra_info);
        if (!s.ok()) {
            LOG(WARNING) << "put shard failed due to update hole meta failed,"
                         << " vid:" << _volume_id << " vbid:" << vbid << " shard_index:" << _shard_index
                         << " hole_vbid:" << hole_vbid << " error:" << s;
            if (s.msg().find("block checksum mismatch") != std::string::npos) {
                s.set_code(AIE_IO_ERROR, "block checksum mismatch");
            }
            return s;
        }

    } else {
        do {
            if (alloc_location(record_type, &location) == 0) {
                break;
            }
            FillHoleType fill_hole_type;
            uint32_t pick_record_type = 0;
            bool pick_succeed = pick_record_type_for_fill_holes(true, &fill_hole_type, &pick_record_type);
            if (!pick_succeed || fill_hole_type == FillHoleType::FREE_LAST_RECORD) {
                return Status(AIE_VLET_FULL, "vlet full");
            }
            s = fill_holes(fill_hole_type, pick_record_type);
            if (!s.ok()) {
                LOG(FATAL) << "put shard failed due to fill holes failed,"
                           << " vid:" << _volume_id << " vbid:" << vbid
                           << " shard_index:" << _shard_index << " error:" << s;
                return s;
            }
            assert(alloc_location(record_type, &location) == 0);
        } while (0);
        s = write_shard_record(location, data_buffer.buf(), _use_standard_record_layout);
        if (!s.ok()) {
            LOG(WARNING) << "put shard failed due to write disk failed,"
                         << " vid:" << _volume_id << " vbid:" << vbid << " shard_index:" << _shard_index
                         << " loc:" << location << " error:" << s;
            free_location(record_type);
            return s;
        }
        aries::pb::RecordLocationExtraInfo record_extra_info;
        if (_use_standard_record_layout) {
            record_extra_info.mutable_slice_desc_meta()->CopyFrom(slice_desc_meta);
        }
        if (_permit_data_offset_index) {
            record_extra_info.set_record_data_offset(record_data_offset);
        }
        s = _indexer->put(vbid, location, &record_extra_info);
        if (!s.ok()) {
            LOG(WARNING) << "put shard failed due to update indexer failed,"
                         << " vid:" << _volume_id << " vbid:" << vbid
                         << " shard_index:" << _shard_index << " error:" << s;
            free_location(record_type);
            if (s.msg().find("block checksum mismatch") != std::string::npos) {
                s.set_code(AIE_IO_ERROR, "block checksum mismatch");
            }
            return s;
        }
    }

    LOG(TRACE) << "put shard succeeded, vid:" << _volume_id << " vbid:" << vbid
               << " shard_index:" << _shard_index << " loc:" << location << " use_hole:" << use_hole
               << " slice_desc_meta:" << common::pb2json(slice_desc_meta);
    return Status(AIE_OK);
}

Status LinkedStore::update_meta(uint64_t vbid, const aries::pb::ShardMeta &meta) {
    RecordLocation location;
    aries::pb::RecordLocationExtraInfo extra_info;
    Status s = _indexer->get(vbid, &location, &extra_info);
    if (!s.ok()) {
        return s;
    }

    aries::pb::ShardMeta old_meta;
    base::IOBuf data;
    s = do_get(vbid, location, &old_meta, &data);
    if (!s.ok()) {
        return s;
    }

    // check new meta is right
    if (!(meta.shard_len() == old_meta.shard_len()
            && meta.shard_crc() == old_meta.shard_crc()
            && meta.blob_len() == old_meta.blob_len()
            && meta.blob_crc() == old_meta.blob_crc())) {
        return Status(AIE_FAIL, "new meta is not right");
    }

    {
        // 0. check old dummy record
        {
            RecordLocation location;
            Status s = _indexer->get(0, &location, NULL);
            if (s.ok()) {
                return Status(AIE_EXIST, "dummy record 0 exist");
            }
        }

        // 1. put new data
        aries::pb::ShardCompressOption compress_option;
        get_serialize_config_with_lock(&compress_option);

        common::Buffer buffer;
        aries::pb::SliceDescMeta slice_desc_meta;
        uint32_t record_type;
        uint32_t record_data_offset;
        common::SerializeArgs serialize_args(sizeof(ShardRecordGuard) - sizeof(uint32_t), &record_type, &record_data_offset);
        s = do_serialize_record(vbid, data, meta, compress_option, &buffer, &slice_desc_meta, &serialize_args);
        if (!s.ok()) {
            LOG(WARNING) << "update shard meta failed due to serialize record failed,"
                         << " vid:" << _volume_id << " vbid:" << vbid
                         << " shard_index:" << _shard_index << " error:"<< s;
            return s;
        }

        std::unique_lock<std::mutex> lock_guard(_location_mutex);
        RecordLocation new_location;
        do {
            if (alloc_location(record_type, &new_location) == 0) {
                break;
            }
            FillHoleType fill_hole_type;
            uint32_t pick_record_type = 0;
            bool pick_succeed = pick_record_type_for_fill_holes(true, &fill_hole_type, &pick_record_type);
            if (!pick_succeed || fill_hole_type == FillHoleType::FREE_LAST_RECORD) {
                LOG(WARNING) << "update shard meta failed due to vlet full,"
                             << " vid:" << _volume_id << " vbid:" << vbid
                             << " shard_index:" << _shard_index << " error:"<< s;
                return Status(AIE_VLET_FULL, "vlet full");
            }
            s = fill_holes(fill_hole_type, pick_record_type);
            if (!s.ok()) {
                LOG(FATAL) << "update shard meta failed due to fill holes failed,"
                           << " vid:" << _volume_id << " vbid:" << vbid
                           << " shard_index:" << _shard_index << " error:" << s;
                return s;
            }
            assert(alloc_location(record_type, &new_location) == 0);
        } while (0);

        s = write_shard_record(new_location, buffer.buf(), _use_standard_record_layout);
        if (!s.ok()) {
            LOG(WARNING) << "update shard meta failed due to write disk failed,"
                         << " vid:" << _volume_id << " vbid:" << vbid << " shard_index:" << _shard_index
                         << " loc:" << location << " error:" << s;
            free_location(record_type);
            return s;
        }

        // 2. change old record to dummy record 0
        // change vbid to new location
        std::unique_ptr<LinkedStoreIndexer::WriteBatch> batch(_indexer->create_batch());
        if (_use_standard_record_layout) {
            extra_info.mutable_slice_desc_meta()->CopyFrom(slice_desc_meta);
        }
        if (_permit_data_offset_index) {
            extra_info.set_record_data_offset(record_data_offset);
        }
        batch->put(vbid, new_location, &extra_info);
        batch->put(0, location); // dummy record, will be deleted later

        s = batch->commit();
        if (!s.ok()) {
            LOG(WARNING) << "update shard meta failed due to update indexer failed,"
                         << " vid:" << _volume_id << " vbid:" << vbid
                         << " shard_index:" << _shard_index << " error:" << s;
            free_location(record_type);
            return s;
        }

        // 3. clear dummy record
        {
            Status s;
            std::unique_ptr<LinkedStoreIndexer::WriteBatch> batch(_indexer->create_batch());

            auto record_bytes = record_type_page_num(location.record_type) * _page_size;

            batch->remove(0, false);
            RecordLocation tail = get_location(location.record_type);
            // need move to plugg the gap
            // if encounters move success and index failure, dirty record had written to disk,
            //   another read to the record would check vbid and found the corruption
            if (tail.record_id != location.record_id) {
                uint64_t tail_vbid = 0;
                aries::pb::RecordLocationExtraInfo tmp_extra_info;
                s = move_shard_record(tail, location, &tail_vbid, &tmp_extra_info);
                if (s.ok()) {
                    batch->put(tail_vbid, location, &tmp_extra_info);
                    s = batch->commit();
                }
            } else {
                s = batch->commit();
            }
            if (s.ok()) {
                free_location(location.record_type);
            }
            if (!s.ok()) {
                LOG(FATAL) << "update shard meta failed due to finally delete dummy record failed,"
                           << " vid:" << _volume_id << " vbid:" << vbid
                           << " shard_index:" << _shard_index << " error:" << s;
                return s;
            }
        }
    }

    LOG(TRACE) << "update shard meta succeeded, vid:" << _volume_id << " vbid:" << vbid
               << " shard_index:" << _shard_index << " loc:" << location;
    return s;
}

// only fast range get do this
Status LinkedStore::range_get(uint64_t vbid, uint32_t offset, uint32_t len, aries::pb::ShardMeta *meta, 
                              base::IOBuf *data) {
    common::TimeMeasure time_measure(&g_engine_store_read_latency);
    Status s;
    uint32_t max_retry_num = 3;
    for (uint32_t retry = 1; retry <= max_retry_num; ++retry) {
        RecordLocation loc;
        aries::pb::RecordLocationExtraInfo extra_info;
        // get shard location
        Status s = _indexer->get(vbid, &loc, &extra_info);
        if (!s.ok()) {
            LOG(WARNING) << "range get shard failed due to read indexer failed,"
                         << " vid:" << _volume_id << " vbid:" << vbid
                         << " shard_index:" << _shard_index << " error:" << s;
            return s;
        }

        // As standard record
        if (extra_info.has_slice_desc_meta()) {
            RangeGetContext range_get_cxt;
            range_get_cxt.record_data_offset = sizeof(common::StandardRecordHeader);
            if (extra_info.slice_desc_meta().record_data_len() == 0) {
                return Status(AIE_INVALID_ARGUMENT, "blob len is 0");
            }
            auto& slice_meta = extra_info.slice_desc_meta().slice_meta();
            auto index_range = common::slice_index_range(slice_meta.split_size(), offset, len);
            if (index_range.second > (size_t)slice_meta.slice_num()) {
                LOG(WARNING) << "invalid range, file:" << _filename << " vbid:" << vbid
                            << " user_offset:" << offset << " user_len:" << len << " record_data_offset:"
                            << range_get_cxt.record_data_offset << " loc:" << loc;
                return Status(AIE_INVALID_ARGUMENT, "invalid index range");
            }
            if (extra_info.slice_desc_meta().compress_type() == static_cast<int>(COMPRESS_TYPE_NONE)) {
                uint32_t slice_size =
                        sizeof(common::SliceHeader) + slice_meta.split_size() + sizeof(uint32_t);
                uint32_t end_offset;
                if (index_range.second == (size_t)slice_meta.slice_num()) {
                    uint32_t origin_data_len = extra_info.slice_desc_meta().record_data_len() -
                        (sizeof(common::SliceHeader) + sizeof(uint32_t)) * slice_meta.slice_num();
                    if (offset + len > origin_data_len) {
                        LOG(WARNING) << "invalid range, file:" << _filename << " vbid:" << vbid
                            << " user_offset:" << offset << " user_len:" << len << " disk_range:(offset:" << offset
                            << " record_data_offset:" << range_get_cxt.record_data_offset << ") loc:" << loc;
                        return Status(AIE_INVALID_ARGUMENT, "invalid non-compress record range");
                    }
                    end_offset = extra_info.slice_desc_meta().record_data_len();
                } else {
                    end_offset = slice_size * index_range.second;
                }
                range_get_cxt.offset = index_range.first * slice_size;
                range_get_cxt.len = end_offset - range_get_cxt.offset;
            } else {
                range_get_cxt.offset = slice_meta.sub_slices_offsets(index_range.first);
                range_get_cxt.len = slice_meta.sub_slices_offsets(index_range.second) - range_get_cxt.offset;
            }

            common::Buffer range_get_buffer;
            uint32_t data_offset_in_buf = 0;
            s = do_range_get(vbid, loc, range_get_cxt, &range_get_buffer, &data_offset_in_buf);
            if (!s.ok()) {
                LOG(TRACE) << "range get data failed, vbid:" << vbid
                    << " offset:" << offset << " length:" << len;
                return s;
            }

            const aries::pb::SliceDescMeta& slice_desc_meta = extra_info.slice_desc_meta();
            assert(slice_desc_meta.has_slice_meta());
            uint64_t slice_vbid = 0;
            s = _standard_record_serializer->range_deserialize(
                range_get_buffer, slice_desc_meta, data_offset_in_buf, offset, len, meta, data, &slice_vbid);
            if (!s.ok()) {
                LOG(TRACE) << "range get data deserialize failed, vid:" << _vlet_info.volume_id() 
                    << " vbid:" << vbid << " offset:" << offset << " len:" << len;
                return s;
            }
            if (slice_vbid != vbid) {
                meta->Clear();
                data->clear();
                if (retry >= max_retry_num) {
                    LOG(FATAL) << "range get shard failed due to record vbid or location inconsistent"
                               << " since tries out,  vid:" << _volume_id << " vbid:" << vbid
                               << " shard_index:" << _shard_index << " loc:" << loc;
                    return Status(AIE_RECORD_META_INCONSISTENT, "record vbid inconsistent");
                } else {
                    LOG(NOTICE) << "record vbid or location inconsistent when range get shard, just retry,"
                                    << " vid:" << _volume_id << " vbid:" << vbid << " shard_index:" << _shard_index
                                    << " loc:" << loc << " retry_times:" << retry;
                    continue;
                }
            }

            return s;
        }

        // As traditional record
        else if (extra_info.has_record_data_offset()) {
            RangeGetContext range_get_cxt;
            range_get_cxt.offset = offset;
            range_get_cxt.len = len;
            range_get_cxt.record_data_offset = extra_info.record_data_offset();

            common::Buffer range_get_buffer;
            uint32_t data_offset_in_buf = 0;
            s = do_range_get(vbid, loc, range_get_cxt, &range_get_buffer, &data_offset_in_buf);
            if (!s.ok()) {
                LOG(WARNING) << "range get shard failed, vid:" << _volume_id
                             << " vbid:" << vbid << " shard_index:" << _shard_index
                             << " offset:" << offset << " length:" << len << " error:" << s;
                return s;
            }
            const aries::pb::SliceDescMeta& slice_desc_meta = extra_info.slice_desc_meta();
            s = _linked_record_serializer->range_deserialize(
                    range_get_buffer, slice_desc_meta, data_offset_in_buf, offset, len, meta, data);
            if (!s.ok()) {
                LOG(WARNING) << "range get shard failed due to range deserialize record failed,"
                             << " vid:" << _volume_id << " vbid:" << vbid << " shard_index:" << _shard_index
                             << " offset:" << offset << " length:" << len << " error:" << s;
                return s;
            }

            // Success
            return s;
        }

        // whole get
        else {
            base::IOBuf buf;
            s = do_get(vbid, loc, meta, &buf);
            if (offset + len > meta->shard_len()) {
                LOG(TRACE) << "get data failed, vbid:" << vbid
                           << " offset: " << offset << " length:" << len;
                return Status(AIE_INVALID_ARGUMENT, "invalid range");
            }
            if (!s.ok()) {
                if (s.code() == AIE_RECORD_META_INCONSISTENT) {
                    data->clear();
                    meta->Clear();
                    if (retry >= max_retry_num) {
                        LOG(FATAL) << "range get shard failed due to record vbid or location inconsistent"
                                   << " since tries out,  vid:" << _volume_id << " vbid:" << vbid
                                   << " shard_index:" << _shard_index << " loc:" << loc;
                        return s;
                    } else {
                        LOG(NOTICE) << "record vbid or location inconsistent when range get shard, just retry,"
                                    << " vid:" << _volume_id << " vbid:" << vbid << " shard_index:" << _shard_index
                                    << " loc:" << loc << " retry_times:" << retry;
                        continue;
                    }
                }
                return s;
            }
            if (offset + len > buf.size()) {
                LOG(WARNING) << "range get failed due to invalid range, vid:" << _volume_id << " vbid:" << vbid
                             << " shard_index:" << _shard_index << " offset:" << offset << " length:" << len << " loc:" << loc;
                return Status(AIE_INVALID_ARGUMENT, "invalid range");
            }
            buf.append_to(data, len, offset);
            // fast range get cannot set shard/blob meta
            uint32_t shard_crc = build_crc(*data);
            meta->set_shard_crc(shard_crc);
            meta->set_shard_len(0);
            meta->set_blob_len(0);
            meta->set_blob_crc(0);

            return s;
        }
    }

    return Status();
}

Status LinkedStore::get_record_index_info(uint64_t vbid, aries::pb::RecordIndexInfo *record_index_info) {
    // set engine type
    record_index_info->set_engine_type(ENGINE_LINKED);
    // get record index info
    Status s;
    RecordLocation loc;
    aries::pb::RecordLocationExtraInfo extra_info;
    s = _indexer->get(vbid, &loc, &extra_info);
    if (!s.ok()) {
        if (s.code() == AIE_MARK_REMOVED) {
            LOG(TRACE) << "get record index info failed due to it has been mark deleted,"
                        << " vid:" << _volume_id << " vbid:" << vbid
                        << " shard_index:" << _shard_index << " error:" << s;
            record_index_info->mutable_linked_record_index_info()->mutable_extra_info()
                    ->set_mark_deleted_timestamp(extra_info.mark_deleted_timestamp());
            return s;
        }
        LOG(WARNING) << "get record index info failed due to read indexer failed,"
                        << " vid:" << _volume_id << " vbid:" << vbid
                        << " shard_index:" << _shard_index << " error:" << s;
        return s;
    }
    // set record index info
    auto pb_loc = record_index_info->mutable_linked_record_index_info()->mutable_record_location();
    loc.serialize_to_pb(pb_loc);
    record_index_info->mutable_linked_record_index_info()->mutable_extra_info()->CopyFrom(extra_info);
    
    return s;
}

Status LinkedStore::do_deserialize_record(
        const common::Buffer &record_buf,
        uint64_t *vbid,
        aries::pb::SliceDescMeta* slice_desc_meta,
        pb::ShardMeta *meta,
        base::IOBuf *data) {
    return do_deserialize_record(record_buf, vbid, slice_desc_meta, meta, data, nullptr, nullptr);
}

// NOTICE: don't use record_data_offset to get shard data from record_buf in standard_record_layout
Status LinkedStore::do_deserialize_record(
        const common::Buffer &record_buf,
        uint64_t *vbid,
        aries::pb::SliceDescMeta* slice_desc_meta,
        pb::ShardMeta *meta,
        base::IOBuf *data,
        uint32_t *record_data_offset,
        bool *is_standard_record_layout) {
    common::TimeMeasure time_measure(&g_engine_deserialize_record_latency);
    uint64_t footer_magic = *reinterpret_cast<uint64_t*>(record_buf.buf() + record_buf.size() - sizeof(ShardRecordGuard));
    Status s;
    if (common::STANDARD_RECORD_FOOTER_MAGIC == footer_magic) {
        uint32_t expected_shard_index = _vlet_info.shard_index();
        uint32_t actual_shard_index = -1;
        s = _standard_record_serializer->deserialize(record_buf, &actual_shard_index, vbid, slice_desc_meta, meta, data);
        if (actual_shard_index != expected_shard_index) {
            LOG(WARNING) << "inconsistent shard_index, expected shard_index:" << expected_shard_index << 
                " actual shard_index:" << actual_shard_index;
            return Status(AIE_SHARD_INDEX_INCONSISTENT, "shard_index inconsistent");
        }
        if (record_data_offset) {
            *record_data_offset = sizeof(common::StandardRecordHeader);
        }
        if (is_standard_record_layout) {
            *is_standard_record_layout = true;
        }
    } else if (SHARD_RECORD_MAGIC == footer_magic) {
        s = _linked_record_serializer->deserialize(record_buf, vbid, slice_desc_meta, meta, data);
        if (record_data_offset && s.ok()) {
            ShardRecordHead &head = *((ShardRecordHead *) record_buf.buf());
            *record_data_offset = head.length;
        }
        if (is_standard_record_layout) {
            *is_standard_record_layout = false;
        }
    } else {
        LOG(FATAL) << "deserialize record failed due to unknown footer magic number,"
                   << " file:" << _filename << " footer_magic:" << footer_magic;
        return Status(AIE_CORRUPT, "unknown footer magic");
    }
    return s;
}

// only read during remove will read corrupted data, and get a crc error
Status LinkedStore::get(uint64_t vbid, aries::pb::ShardMeta* meta, base::IOBuf* data) {
    common::TimeMeasure time_measure(&g_engine_store_read_latency);
    uint32_t max_retry_num = 3;
    Status s;
    for (uint32_t retry = 1; retry <= max_retry_num; ++retry) {
        Status s;
        RecordLocation loc;
        aries::pb::RecordLocationExtraInfo extra_info;
        // get shard location
        {
            aries::common::TimeMeasure time_measure(&g_engine_store_index_get_latency);
            s = _indexer->get(vbid, &loc, &extra_info);
        }
        if (!s.ok()) {
            if (s.code() == AIE_MARK_REMOVED) {
                LOG(TRACE) << "get shard failed due to it has been mark deleted,"
                           << " vid:" << _volume_id << " vbid:" << vbid
                           << " shard_index:" << _shard_index << " error:" << s;
                return s;
            }
            LOG(WARNING) << "get shard failed due to read indexer failed,"
                         << " vid:" << _volume_id << " vbid:" << vbid
                         << " shard_index:" << _shard_index << " error:" << s;
            return s;
        }

        // only check location exist
        if (meta == NULL) {
            assert(data == NULL);
            return Status(AIE_OK);
        }

        bool vbid_mismatch = false;
        auto ret = do_get(vbid, loc, meta, data, &vbid_mismatch);
        if (ret.code() == AIE_RECORD_META_INCONSISTENT && vbid_mismatch && data != nullptr) {
            data->clear();
            meta->Clear();
            if (retry >= max_retry_num) {
                LOG(WARNING) << "record vbid inconsistency detected, reach max retry number"
                    ", file:" << _filename << " vbid:" << vbid;
                return Status(AIE_RECORD_META_INCONSISTENT, "record vbid inconsistent");
            } else {
                LOG(NOTICE) << "record vbid or location inconsistent when get shard, just retry,"
                            << " vid:" << _volume_id << " vbid:" << vbid << " shard_index:" << _shard_index
                            << " loc:" << loc << " retry_times:" << retry;
                continue;
            }
        }
        return ret;
    } // end retry
    return Status(AIE_OK);
}

Status LinkedStore::do_get(uint64_t vbid, RecordLocation& loc,
                             aries::pb::ShardMeta *meta, base::IOBuf *data, bool* vbid_mismatch) {
    uint64_t record_bytes = record_type_page_num(loc.record_type) * _page_size;
    common::Buffer data_buffer;
    data_buffer.reset(record_bytes, _page_size);

    // read record from disk
    auto s = read_shard_record(loc, data_buffer.buf());
    if (!s.ok()) {
        LOG(WARNING) << "get shard failed due to read disk failed,"
                     << " vid:" << _volume_id << " vbid:" << vbid << " shard_index:" << _shard_index
                     << " loc:" << loc << " error:" << s;
        return s;
    }

    // validate record
    {
        common::TimeMeasure time_measure(&g_engine_validate_record_latency);
        s = ShardRecord::validate_record(record_type_page_num(loc.record_type), data_buffer.buf(), _page_size);
    }
    if (!s.ok()) {
        LOG(WARNING) << "get shard failed due to validate record failed,"
                     << " vid:" << _volume_id << " vbid:" << vbid << " shard_index:" << _shard_index
                     << " loc:" << loc << " error:" << s;
        return s;
    }

    // deserialize record
    uint64_t record_vbid = 0;
    s = do_deserialize_record(data_buffer, &record_vbid, nullptr, meta, data);
    if (!s.ok()) {
        LOG(WARNING) << "get shard failed due to deserialize record failed,"
                     << " vid:" << _volume_id << " vbid:" << vbid << " shard_index:" << _shard_index
                     << " loc:" << loc << " error:" << s;
        return s;
    }

    // check record vbid
    if (record_vbid != vbid) {
        LOG(FATAL) << "get shard failed due to record vbid inconsistent,"
                   << " vid:" << _volume_id << " vbid:" << vbid << " shard_index:" << _shard_index
                   << " record_vbid:" << record_vbid << " loc:" << loc;
        return Status(AIE_RECORD_META_INCONSISTENT, "record vbid inconsistent");
    }

    LOG(TRACE) << "get shard succeeded, vid:" << _volume_id << " vbid:" << vbid
               << " shard_index:" << _shard_index << " loc:" << loc;
    return Status(AIE_OK);
}

Status LinkedStore::do_range_get(uint64_t vbid, RecordLocation& loc, RangeGetContext& ctx,
                        common::Buffer* range_get_buffer, uint32_t* data_offset_in_buf) {
    common::TimeMeasure time_measure(&g_engine_record_read_latency);
    assert(sizeof(ShardRecordGuard) == SHARD_RECORD_GUARD_SIZE);

    uint32_t len = ctx.len;
    uint32_t offset = ctx.offset;
    uint32_t record_data_offset = ctx.record_data_offset;

    // check offset and len
    uint32_t record_page_num = record_type_page_num(loc.record_type);
    uint64_t record_bytes = record_page_num * _page_size;
    uint64_t max_range = record_data_offset + offset + len;
    if (max_range > record_bytes - SHARD_RECORD_GUARD_SIZE) {
        LOG(WARNING) << "range get shard failed due to invalid range, vid:" << _volume_id
                     << " vbid:" << vbid << " shard_index:" << _shard_index << " user_range:" << ctx.to_string()
                     << " disk_range:(offset:" << offset << " length:" << len
                     << " record_data_offset:" << record_data_offset << ") loc:" << loc;
        return Status(AIE_INVALID_ARGUMENT, "invalid range");
    }

    // calculate block offset/len
    uint32_t record_block_offset = ((loc.record_id - 1) * record_page_num) % _block_page_num;
    uint32_t data_begin_page = (record_data_offset + offset) / kPageSize;
    uint32_t data_end_page = (record_data_offset + offset + len + kPageSize - 1) / kPageSize;
    uint32_t data_page_num = data_end_page - data_begin_page;
    uint32_t block_page_offset = record_block_offset + data_begin_page;

    // range read block
    range_get_buffer->reset(data_page_num * kPageSize, _page_size);
    int ret = range_read_block(loc.block_id, loc.next_block_id, range_get_buffer->buf(),
                               data_page_num, block_page_offset, false);
    if (ret != 0) {
        LOG(WARNING) << "range get shard failed due to range read disk failed, vid:" << _volume_id
                     << " vbid:" << vbid << " shard_index:" << _shard_index << " range:" << ctx.to_string()
                     << " loc:" << loc << " data_offset:" << block_page_offset * kPageSize
                     << " data_length:" << data_page_num * kPageSize;
        return Status::by_errno(ret, "range read disk failed");
    }
    *data_offset_in_buf = record_data_offset + offset - data_begin_page * kPageSize;

    return Status(AIE_OK);
}

Status LinkedStore::remove(uint64_t vbid, bool mark_delete) {
    uint32_t now = static_cast<uint32_t>(base::gettimeofday_s());
    return remove(vbid, mark_delete, now);
}

Status LinkedStore::remove(uint64_t vbid, bool mark_delete, uint32_t mark_delete_timestamp) {
    RecordLocation location;
    aries::pb::RecordLocationExtraInfo extra_info;
    // Note: record loc may be changed by fill_holes(),
    // so we need to acquire _location_lock to do mark_delete just like restore does;
    std::unique_lock<std::mutex> lock_guard(_location_mutex);
    Status s = _indexer->get(vbid, &location, &extra_info);
    switch (s.code()) {
        case AIE_BLOB_NOT_EXIST:
            return Status();
        case AIE_REMOVED:
            if (mark_delete) {
                return Status();
            }
            return _indexer->remove(vbid, mark_delete);
        case AIE_MARK_REMOVED: {
            if (mark_delete) {
                return Status();
            }
            s = finally_remove(vbid, location);
            if (!s.ok()) {
                LOG(WARNING) << "finally delete shard failed,"
                             << " vid:" << _volume_id << " vbid:" << vbid
                             << " shard_index:" << _shard_index << " error:" << s;
                return s;
            }
            --_mark_deleted_record_num;
            FillHoleType fill_hole_type;
            uint32_t pick_record_type;
            bool pick_succeed = pick_record_type_for_fill_holes(false, &fill_hole_type, &pick_record_type);
            if (pick_succeed) {
                assert(fill_hole_type == FillHoleType::FREE_LAST_BLOCK);
                s = fill_holes(fill_hole_type, pick_record_type);
                if (!s.ok()) {
                    LOG(FATAL) << "fill holes failed during finally delete,"
                               << " vid:" << _volume_id << " vbid:" << vbid << " shard_index:" << _shard_index
                               << " pick_record_type:" << pick_record_type<< " error:" << s;
                }
            }
            LOG(TRACE) << "finally delete shard succeeded, vid:" << _volume_id
                       << " vbid:" << vbid << " shard_index:" << _shard_index;
            return Status();
        }
        case AIE_OK: {
            if (!mark_delete) {
                return Status(AIE_FAIL, "could not finally delete normal shard");
            }
            s = _indexer->remove(vbid, mark_delete, &location, &extra_info, mark_delete_timestamp);
            if (!s.ok()) {
                LOG(WARNING) << "mark delete shard failed due to remove indexer failed,"
                             << " vid:" << _volume_id << " vbid:" << vbid
                             << " shard_index:" << _shard_index << " error:" << s;
                return s;
            }
            ++_mark_deleted_record_num;
            update_fingerprint(vbid);
            LOG(TRACE) << "mark delete shard succeeded, vid:" << _volume_id
                       << " vbid:" << vbid << " shard_index:" << _shard_index;
            return Status();
        }
        default:
            return s;
    }
}

// finally remove
Status LinkedStore::finally_remove(uint64_t vbid, const RecordLocation &location) {
    Status s;
    std::unique_ptr<LinkedStoreIndexer::WriteBatch> batch(_indexer->create_batch());

    auto record_bytes = record_type_page_num(location.record_type) * _page_size;
    auto can_fast_remove = _permit_fast_remove && 
                          (_max_holes_size_for_fast_remove != 0) &&
                          (_min_record_size_for_fast_remove <= record_bytes);
    LOG(TRACE) << "finally remove record, vid:" << _volume_id << " vbid:" << vbid
               << " shard_index:" << _shard_index << " record_type:" << location.record_type
               << " record_bytes:" << record_bytes << " can_fast_remove:" << can_fast_remove;

    if (can_fast_remove) { // optimiaztion
        RecordContainer &container = _record_containers[location.record_type];
        if (container.tail_hole_vbid != 0) {
            RecordLocation hole_location;
            aries::pb::RecordLocationExtraInfo hole_extra_info;
            s = _indexer->get(container.tail_hole_vbid, &hole_location, &hole_extra_info);
            if (s.code() == AIE_IO_ERROR) {
                LOG(WARNING) << "finally remove record failed due to get tail hole vbid location failed,"
                             << " vid:" << _volume_id << " shard_index:" << _shard_index
                             << " tail_hole_vbid:" << container.tail_hole_vbid << " error:" << s;
                return s;
            }
            assert(s.code() == AIE_BLOB_NOT_EXIST && hole_extra_info.has_prev_hole_vbid());
            hole_extra_info.set_next_hole_vbid(vbid);
            batch->put(container.tail_hole_vbid, hole_location, &hole_extra_info);
        }
        aries::pb::RecordLocationExtraInfo extra_info;
        extra_info.set_prev_hole_vbid(container.tail_hole_vbid);
        extra_info.set_next_hole_vbid(0);
        batch->put(vbid, location, &extra_info);
        s = batch->commit();
        if (s.ok()) {
            container.tail_hole_vbid = vbid;
            if (container.head_hole_vbid == 0) {
                container.head_hole_vbid = vbid;
            }
            ++container.hole_record_num;
            g_datanode_holes_counter << 1;
        } else {
            LOG(FATAL) << "finally remove record failed, vid:" << _volume_id
                       << " vbid:" << vbid << " shard_index:" << _shard_index
                       << " head_hole_vbid:" << container.head_hole_vbid
                       << " tail_hole_vbid:" << container.tail_hole_vbid
                       << " error:" << s;
        }
        g_datanode_fast_remove_counter << 1;
    } else {
        batch->remove(vbid, false);
        RecordLocation tail = get_location(location.record_type);
        // need move to plugg the gap
        // if encounters move success and index failure, dirty record had written to disk,
        //   another read to the record would check vbid and found the corruption
        uint64_t tail_vbid = 0;
        if (tail.record_id != location.record_id) {
            aries::pb::RecordLocationExtraInfo tmp_extra_info;
            s = move_shard_record(tail, location, &tail_vbid, &tmp_extra_info);
            if (s.ok()) {
                batch->put(tail_vbid, location, &tmp_extra_info);
                s = batch->commit();
            }
        } else {
            s = batch->commit();
        }
        if (s.ok()) {
            free_location(location.record_type);
        }
        if (!s.ok()) {
            LOG(FATAL) << "remove record failed, vid:" << _volume_id << " vbid:" << vbid
                       << " shard_index:" << _shard_index << " from:" << tail << " to:" << location
                       << " tail_vbid:" << tail_vbid  << " error:" << s;
        }
    }
    return s;
}

bool LinkedStore::pick_record_type_for_fill_holes(bool force_pick,
        FillHoleType* fill_hole_type, uint32_t* record_type) {
    uint32_t record_type_of_free_last_record = 0;
    uint32_t record_type_of_free_last_block = 0;
    uint32_t max_free_block_num_of_record_type = 0;
    uint32_t all_free_block_num = 0;

    for (auto i = 1; i <= (int)_max_record_type; ++i) {
        auto record_page_num = record_type_page_num(i);
        RecordContainer &container = _record_containers[i];
        auto free_block_num = container.free_block_num(record_page_num, _block_page_num);
        auto hole_record_num = container.hole_record_num;

        // A special scenario:
        // min_record_size_for_fast_remove was ever set as a smaller value than now,
        // if these record types have enough holes to release a least a free block,
        // then release free blocks from these record types preferentially.
        if (free_block_num != 0 && record_page_num * PAGE_SIZE < _min_record_size_for_fast_remove) {
            *fill_hole_type = FillHoleType::FREE_LAST_BLOCK;
            *record_type = i;
            return true;
        }
        if (free_block_num > max_free_block_num_of_record_type) {
            record_type_of_free_last_block = i;
            max_free_block_num_of_record_type = free_block_num;
        } else if (free_block_num == 0 && (int)hole_record_num > 0) {
            record_type_of_free_last_record = i;
        }
        all_free_block_num += free_block_num;
    }

    uint64_t can_free_hole_size = all_free_block_num * _block_size;
    if (!force_pick) {
        // If not force pick (only the scenario from deletion),
        // only trigger free blocks.
        if (can_free_hole_size > _max_holes_size_for_fast_remove) {
            *fill_hole_type = FillHoleType::FREE_LAST_BLOCK;
            *record_type = record_type_of_free_last_block;
            return true;
        }
        return false;

    } else {
        // If force pick (the scenario from balance or write without space),
        // firstly free blocks, if these is no any block can be freed, then
        // secondly free records.
        if (record_type_of_free_last_block > 0) {
            *fill_hole_type = FillHoleType::FREE_LAST_BLOCK;
            *record_type = record_type_of_free_last_block;
            return true;
        } else if (record_type_of_free_last_record > 0) {
            *fill_hole_type = FillHoleType::FREE_LAST_RECORD;
            *record_type = record_type_of_free_last_record;
            return true;
        }
        return false;
    }
}

Status LinkedStore::update_hole_meta(LinkedStoreIndexer::WriteBatch* batch,
        RecordContainer& container, uint64_t hole_vbid,
        const aries::pb::RecordLocationExtraInfo& hole_info) {
    LOG(TRACE) << "update hole meta, vid:" << _volume_id << " shard_index:" << _shard_index
               << " hole_vbid:" << hole_vbid << " hole_record_num:" << container.hole_record_num
               << " head_hole_vbid:" << container.head_hole_vbid
               << " tail_hole_vbid:" << container.tail_hole_vbid
               << " hole_info:" << common::pb2json(hole_info);
    Status s;
    // update rocksdb
    if (hole_info.prev_hole_vbid() != 0) {
        RecordLocation prev_location;
        aries::pb::RecordLocationExtraInfo prev_extra_info;
        s = _indexer->get(hole_info.prev_hole_vbid(), &prev_location, &prev_extra_info);
        if (s.code() == AIE_IO_ERROR) {
            LOG(WARNING) << "update hole meta failed due to get prev hole vbid location failed,"
                         << " vid:" << _volume_id << " shard_index:" << _shard_index
                         << " prev_hole_vbid:" << hole_info.prev_hole_vbid() << " error:" << s;
            return s;
        }
        assert(s.code() == AIE_BLOB_NOT_EXIST && prev_extra_info.has_prev_hole_vbid());
        prev_extra_info.set_next_hole_vbid(hole_info.next_hole_vbid());
        batch->put(hole_info.prev_hole_vbid(), prev_location, &prev_extra_info);
    }
    if (hole_info.next_hole_vbid() != 0) {
        RecordLocation next_location;
        aries::pb::RecordLocationExtraInfo next_extra_info;
        s = _indexer->get(hole_info.next_hole_vbid(), &next_location, &next_extra_info);
        if (s.code() == AIE_IO_ERROR) {
            LOG(WARNING) << "update hole meta failed due to get next hole vbid location failed,"
                         << " vid:" << _volume_id << " shard_index:" << _shard_index
                         << " next_hole_vbid:" << hole_info.next_hole_vbid() << " error:" << s;
            return s;
        }
        assert(s.code() == AIE_BLOB_NOT_EXIST && next_extra_info.has_prev_hole_vbid());
        next_extra_info.set_prev_hole_vbid(hole_info.prev_hole_vbid());
        batch->put(hole_info.next_hole_vbid(), next_location, &next_extra_info);
    }
    batch->remove(hole_vbid, false);

    s = batch->commit();
    if (!s.ok()) {
        LOG(WARNING) << "update hole meta failed due to write indexer failed,"
                     << " vid:" << _volume_id << " shard_index:" << _shard_index
                     << " prev_hole_vbid:" << hole_info.prev_hole_vbid()
                     << " next_hole_vbid:" << hole_info.next_hole_vbid()
                     << " error:" << s;
        return s;
    }

    // update memory
    if (hole_info.prev_hole_vbid() == 0) {
        container.head_hole_vbid = hole_info.next_hole_vbid();
    }
    if (hole_info.next_hole_vbid() == 0) {
        container.tail_hole_vbid = hole_info.prev_hole_vbid();
    }
    assert((int)container.hole_record_num >= 1);
    --container.hole_record_num;
    g_datanode_holes_counter << -1;

    return s;
}

Status LinkedStore::fill_holes(FillHoleType fill_hole_type, uint32_t record_type) {
    base::Timer time_l;
    time_l.start();
    Status s;
    RecordContainer &container = _record_containers[record_type];
    auto record_page_num = record_type_page_num(record_type);
    auto block_num = container.cells.size();

    uint32_t block_id = 0;
    uint32_t next_block_id = 0;
    uint32_t offset = 0;    // page unit
    uint32_t len = 0;   // page unit
    if (fill_hole_type == FillHoleType::FREE_LAST_BLOCK) {
        auto quotient = (block_num - 1) * _block_page_num / record_page_num;
        auto remainder = (block_num - 1) * _block_page_num % record_page_num;
        if (remainder == 0) {
            block_id = container.cells[block_num - 1];
        } else {
            block_id = container.cells[block_num - 2];
            next_block_id = container.cells[block_num - 1];
            offset = _block_page_num - remainder;
        }
        len = (container.record_index - quotient) * record_page_num;
    } else {
        assert(fill_hole_type == FillHoleType::FREE_LAST_RECORD);
        RecordLocation tail = get_location(record_type);
        block_id = tail.block_id;
        next_block_id = tail.next_block_id;
        offset = ((tail.record_id - 1) * record_page_num) % _block_page_num;
        len = record_page_num;
    }
    LOG(TRACE) << "read block, vid:" << _volume_id << " total_block_num:" << block_num
               << " record_type:" << record_type << " record_page_num:" << record_page_num
               << " block_id:" << block_id << " next_block_id:" << next_block_id
               << " offset:" << offset << " length:" << len;

    char* buf = (char*) ::memalign(_page_size, len * _page_size);
    assert(buf != NULL);
    std::unique_ptr<char, decltype(&::free)> buffer(buf, &::free);
    int ret = read_block(block_id, next_block_id, buf, len, offset, true);
    if (ret != 0) {
        return Status::by_errno(ret, "read block failed for fill holes");
    }

    RecordLocationHelper loc_helper(this);
    for (uint32_t i = 0; i < (len / record_page_num); ++i) {
        auto off = (len - (i + 1) * record_page_num) * _page_size;
        RecordLocation tail = get_location(record_type);
        uint64_t vbid = 0;
        aries::pb::RecordLocationExtraInfo extra_info;
        bool is_standard_record_layout = false;
        s = loc_helper.get_record_info(tail, buf + off, &vbid, &extra_info, &is_standard_record_layout);
        if (!s.ok()) {
            LOG(FATAL) << "fill holes failed due to parse tail record failed,"
                       << " vid:" << _volume_id << " shard_index:" << _shard_index
                       << " loc:" << tail << " error:" << s;
            return s;
        }

        uint64_t hole_vbid = 0;
        RecordLocation hole_loc;
        aries::pb::RecordLocationExtraInfo hole_extra_info;
        std::unique_ptr<LinkedStoreIndexer::WriteBatch> batch(_indexer->create_batch());
        if (extra_info.has_prev_hole_vbid()) {
            // tail record is hole
            hole_vbid = vbid;
            hole_extra_info.CopyFrom(extra_info);
        } else {
            // tail record is a valid record and move it to head hole location
            hole_vbid = container.head_hole_vbid;
            s = _indexer->get(hole_vbid, &hole_loc, &hole_extra_info);
            if (s.code() == AIE_IO_ERROR) {
                LOG(WARNING) << "fill holes failed due to get head hole vbid location faild,"
                             << " vid:" << _volume_id << " shard_index:" << _shard_index
                             << " head_hole_vbid:" << hole_vbid << " error:" << s;
                return s;
            }
            assert(s.code() == AIE_BLOB_NOT_EXIST && hole_extra_info.has_prev_hole_vbid());
            // FREE_LAST_BLOCK skip the last block's holes
            while ((hole_loc.block_id == container.cells[block_num - 1] ||
                    hole_loc.next_block_id == container.cells[block_num - 1]) &&
                    fill_hole_type == FillHoleType::FREE_LAST_BLOCK) {
                LOG(TRACE) << "skip last block hole's loc:" << hole_loc;
                hole_vbid = hole_extra_info.next_hole_vbid();
                assert(hole_vbid != 0);
                s = _indexer->get(hole_vbid, &hole_loc, &hole_extra_info);
                if (s.code() == AIE_IO_ERROR) {
                    LOG(WARNING) << "fill holes failed due to get hole vbid location failed,"
                                 << " vid:" << _volume_id << " shard_index:" << _shard_index
                                 << " hole_vbid:" << hole_vbid << " error:" << s;
                    return s;
                }
                assert(s.code() == AIE_BLOB_NOT_EXIST && hole_extra_info.has_prev_hole_vbid());
            }
            s = write_shard_record(hole_loc, buf + off, is_standard_record_layout);
            if (!s.ok()) {
                LOG(WARNING) << "fill holes failed due to write disk failed,"
                             << " vid:" << _volume_id << " shard_index:" << _shard_index
                             << " hole_loc:" << hole_loc << " error:" << s;
                return s;
            }
            batch->put(vbid, hole_loc, &extra_info);
        }

        // update hole meta
        s = update_hole_meta(batch.get(), container, hole_vbid, hole_extra_info);
        if (!s.ok()) {
            LOG(WARNING) << "fill holes failed due to update hole meta failed,"
                         << " vid:" << _volume_id << " shard_index:" << _shard_index
                         << " hole_vbid:" << hole_vbid << " hole_loc:" << hole_loc
                         << " error:" << s;
            return s;
        }
        free_location(record_type);
    }
    time_l.stop();
    g_datanode_fill_holes_latency << time_l.u_elapsed();
    g_datanode_filled_hole_counter << (len / record_page_num);
    LOG(TRACE) << "finish fill holes, vid:" << _volume_id << " shard_index:" << _shard_index
               << " fill_hole_num:" << len / record_page_num
               << " fill_hole_type:" << (int)fill_hole_type << " record_type:" << record_type
               << " block_id:" << block_id << " next_block_id:" << next_block_id
               << " cost:" << time_l.u_elapsed() << "us";
    return s;
}

Status LinkedStore::restore(uint64_t vbid) {
    std::unique_lock<std::mutex> lock_guard(_location_mutex);
    RecordLocation location;
    aries::pb::RecordLocationExtraInfo extra_info;
    Status s = _indexer->get(vbid, &location, &extra_info);
    if (s.ok()) {
        return s;
    }
    if (s.code() != AIE_MARK_REMOVED) {
        LOG(WARNING) << "restore shard failed due to read indexer failed,"
                     << " vid:" << _volume_id << " vbid:" << vbid
                     << " shard_index:" << _shard_index << " error:" << s;
        return s;
    }

    // Clear the mark deleted timestamp
    extra_info.clear_mark_deleted_timestamp();

    s = _indexer->put(vbid, location, &extra_info);
    if (!s.ok()) {
        LOG(WARNING) << "restore shard failed due to write indexer failed,"
                     << " vid:" << _volume_id << " vbid:" << vbid
                     << " shard_index:" << _shard_index << " error:" << s;
    } else {
        --_mark_deleted_record_num;
        update_fingerprint(vbid);
    }

    // RestoreShard is a kind of rare operation, here we print a WARNING log.
    LOG(WARNING) << "restore shard succeeded, vid:" << _volume_id << " vbid:" << vbid
                 << " shard_index:" << _shard_index << " loc:" << location;

    return s;
}

// for balance
Status LinkedStore::batch_put_record(const std::vector<aries::pb::CopyVletRecordMeta>& record_meta_list,
            base::IOBuf* iobuf) {
    for (auto& record_meta : record_meta_list) {
        base::IOBuf data;
        auto& meta = record_meta.shard_meta();
        assert(iobuf->cutn(&data, meta.shard_len()) == meta.shard_len());
        auto s = put(record_meta.vbid(), meta, data);
        if (!s.ok()) {
            LOG(WARNING) << "batch put record failed,"
                         << " vid:" << _volume_id << " vbid:" << record_meta.vbid()
                         << " shard_index:" << _shard_index;
            return s;
        }
        if (record_meta.has_mark_deleted_timestamp() && record_meta.mark_deleted_timestamp() > 0) {
            s = remove(record_meta.vbid(), true);
            if (!s.ok()) {
                LOG(WARNING) << "batch put record failed due to mark delete record failed,"
                             << " vid:" << _volume_id << " vbid:" << record_meta.vbid()
                             << " shard_index:" << _shard_index << " error:" << s;
                return s;
            }
        }
    }
    return Status();
}

// for balance
Status LinkedStore::batch_get_record(uint32_t record_type, base::IOBuf *data, uint32_t from_id) {
    assert((int)from_id >= 1);
    {
        // fill holes before balance
        base::Timer time_l;
        time_l.start();
        std::unique_lock<std::mutex> lock_guard(_location_mutex);
        FillHoleType fill_hole_type;
        uint32_t pick_record_type = 0;
        while (pick_record_type_for_fill_holes(true, &fill_hole_type, &pick_record_type)) {
            auto s = fill_holes(fill_hole_type, pick_record_type);
            time_l.stop();
            if (!s.ok() || FLAGS_max_fill_holes_ms <= (uint64_t)time_l.m_elapsed()) {
                return s;
            }
        }
    }
    uint32_t offset_in_block = 0;
    uint32_t block_id = 0;
    uint32_t next_block_id = 0;
    uint32_t batch_size = 0;
    uint32_t record_page_num = record_type_page_num(record_type);
    char* buf = nullptr;
    std::unique_ptr<char, decltype(&::free)> buffer(nullptr, &::free);

    {
        std::unique_lock<std::mutex> lock_guard(_location_mutex);
        RecordContainer &container = _record_containers[record_type];
        uint32_t last_id = container.record_index;
        if (last_id < from_id) {
            return Status();
        }
        batch_size = last_id - from_id + 1;
        if (batch_size * record_page_num > _block_page_num) {
            batch_size = _block_page_num / record_page_num;
        }
        uint32_t offset = ((from_id - 1) * record_page_num);
        uint32_t cell_index = offset / _block_page_num;
        offset_in_block = offset % _block_page_num;
        block_id = container.cells[cell_index];
        if (offset_in_block + batch_size * record_page_num > _block_page_num) {
            next_block_id = container.cells[cell_index + 1];
        }

        LOG(TRACE) << "batch get record, vid:" << _volume_id << " shard_index:" << _shard_index
                   << " record_type:" << record_type << " from_id:" << from_id << " batch_size:"
                   << batch_size << " block_id:" << block_id << " next_block_id:" << next_block_id
                   << " offset_in_block:" << offset_in_block;
        buf = (char *) ::memalign(_page_size, batch_size * record_page_num * _page_size);
        assert(buf != NULL);
        buffer.reset(buf);
        // block data may be replaced by fill_holes() or finally_remove()
        // so we need hold _location_mutex here;
        int ret = read_block(block_id, next_block_id, buf, batch_size * record_page_num, offset_in_block, true);
        if (ret != 0) {
             return Status::by_errno(ret, "read block failed of balance");
        }
    }

    for (uint32_t i = 0; i < batch_size; ++i) {
        auto off = i * record_page_num * _page_size;
        auto s = ShardRecord::validate_record(record_page_num, buf + off, _page_size);
        if (!s.ok()) {
            LOG(FATAL) << "batch get record failed due to record checksum failed,"
                       << " vid:" << _volume_id << " shard_index:" << _shard_index
                       << " record_type:" << record_type << " block_id:" << block_id
                       << " next_block_id:" << next_block_id << " offset_in_block:" << off
                       << " error:" << s;
            return s;
        }
    }
    data->append(buf, batch_size * record_page_num * _page_size);
    return Status();
}

Status LinkedStore::batch_get_segment(GetSegmentContext* ctx) {
    auto response = ctx->response;
    auto segment = ctx->segment;
    auto log_id = ctx->log_id;
    auto batch_info = (BatchRecord*)(segment->ext);
    uint32_t record_type = batch_info->record_type;
    uint32_t from_id = batch_info->from_id;
    uint32_t offset_in_block = 0;
    uint32_t block_id = 0;
    uint32_t next_block_id = 0;
    uint32_t batch_size = batch_info->batch_size;
    uint32_t record_page_num = record_type_page_num(record_type);
    do {

        char* buf = nullptr;
        std::unique_ptr<char, decltype(&::free)> buffer(nullptr, &::free);

        {
            std::unique_lock<std::mutex> lock_guard(_location_mutex);
            RecordContainer &container = _record_containers[record_type];
            uint32_t last_id = container.record_index;
            if (last_id < from_id) {
                // need get every blob
                break;
            }
            uint32_t tmp_batch_size = last_id - from_id + 1;
            batch_size = std::min(tmp_batch_size, batch_size);

            uint32_t offset = ((from_id - 1) * record_page_num);
            uint32_t cell_index = offset / _block_page_num;
            offset_in_block = offset % _block_page_num;
            block_id = container.cells[cell_index];
            if (offset_in_block + batch_size * record_page_num > _block_page_num) {
                next_block_id = container.cells[cell_index + 1];
            }

            segment->real_io_count += 1;
            LOG(TRACE) << "batch get segment, vid:" << _volume_id << " record_type:" << record_type
                << " from_id:" << from_id << " batch_size:" << batch_size << " block_id:" << block_id
                << " next_block_id:" << next_block_id << " offset_in_block:" << offset_in_block;

            buf = (char *) ::memalign(_page_size, batch_size * record_page_num * _page_size);
            assert(buf != NULL);
            buffer.reset(buf);
            int ret = read_block(block_id, next_block_id, buf, batch_size * record_page_num, offset_in_block, true);
            if (ret != 0) {
                // need to set status
                response->mutable_status()->set_code(AIE_IO_ERROR);
                return Status::by_errno(ret, "read block failed for batch get");
            }
        }

        for (uint32_t i = 0; i < batch_size; ++i) {
            auto off = i * record_page_num * _page_size;
            auto s = ShardRecord::validate_record(record_page_num, buf + off, _page_size);
            if (!s.ok()) {
                segment->has_checksum_error = true;
                LOG(FATAL) << "record checksum failed during batch get segment, but we ignore it,"
                        << " vid:" << _volume_id << " shard_index:" << _shard_index << " record_type:" << record_type
                        << " block_id:" << block_id << " next_blob_id:" << next_block_id
                        << " offset_in_block:" << off << " error:" << s;
                continue;
            }
            uint64_t record_vbid = 0;
            aries::pb::ShardMeta meta;
            base::IOBuf data;
            uint32_t record_bytes = record_page_num * _page_size;
            common::Buffer record_buffer(buf + off, record_bytes);
            s = do_deserialize_record(record_buffer, &record_vbid, nullptr, &meta, &data);
            record_buffer.release();
            if (!s.ok()) {
                segment->has_checksum_error = true;
                LOG(FATAL) << "record corruption detected during batch get segment, but we ignore it,"
                           << " vid:" << _volume_id << " vid:" << ctx->vid << " shard_index:" << _shard_index;
                continue;
            }
            if (batch_info->vbids.find(record_vbid) == batch_info->vbids.end()) {
                LOG(NOTICE) << "record duplication detected during batch get segment, but we ignore it,"
                        << " vid:" << _volume_id << " vbid:" << record_vbid << " shard_index:" << _shard_index
                        << " record_type:" << record_type << " block_id:" << block_id
                        << " next_block_id:" << next_block_id
                        << " offset_in_block:" << off;
                continue;
            }

            auto shard_info = response->add_shard_info();
            shard_info->set_vbid(record_vbid);
            shard_info->mutable_status()->set_code(AIE_OK);
            shard_info->mutable_shard_meta()->CopyFrom(meta);
            ctx->cntl->response_attachment().append(data);
            batch_info->vbids.erase(record_vbid);
            segment->data_size += meta.shard_len();
        }
    } while (false);

    // For leaving vbids, use get();
    for (auto iter = batch_info->vbids.begin(); iter != batch_info->vbids.end();) {
        auto vbid = iter->first;
        aries::pb::ShardMeta meta;
        base::IOBuf data;
        auto s = get(vbid, &meta, &data);
        ++segment->real_io_count;
        if (s.code() == AIE_IO_ERROR) {
            LOG(WARNING) << "batch get segment due to get shard failed,"
                         << " vid:" << _volume_id << " vbid:" << vbid
                         << " shard_index:" << _shard_index << " error:" << s;
            response->mutable_status()->set_code(AIE_IO_ERROR);
            return Status(AIE_IO_ERROR, "get shard failed");
        } else if (s.code() == AIE_OK) {
            auto shard_info = response->add_shard_info();
            shard_info->set_vbid(vbid);
            shard_info->mutable_status()->set_code(AIE_OK);
            shard_info->mutable_shard_meta()->CopyFrom(meta);
            ctx->cntl->response_attachment().append(data);
            batch_info->vbids.erase(iter++);
            segment->data_size += meta.shard_len();
        } else {
            auto shard_info = response->add_shard_info();
            shard_info->set_vbid(vbid);
            shard_info->mutable_status()->set_code(s.code());
            batch_info->vbids.erase(iter++);
            if (s.code() == AIE_CHECKSUM) {
                segment->has_checksum_error = true;
            }
        }
    }
    return Status();
}

void LinkedStore::get_mark_deleted_vbids(aries::pb::CopyLinkedVletResponse *response) {
    std::unique_lock<std::mutex> lock_guard(_location_mutex);
    std::unique_ptr<LinkedStoreIndexer::Iterator> it(_indexer->seek(0));
    while (it->valid()) {
        aries::pb::RecordLocationExtraInfo extra_info;
        const RecordLocation *loc = it->value(&extra_info);
        if (extra_info.has_mark_deleted_timestamp() && extra_info.mark_deleted_timestamp() > 0) {
            response->add_mark_deleted_vbid_list(it->key());
            response->add_mark_deleted_timestamp_list(extra_info.mark_deleted_timestamp());
        }
        it->next();
    }
}

Status LinkedStore::put_mark_deleted_vbids(const aries::pb::CopyLinkedVletResponse &response) {
    Status s;
    for (int i = 0; i < response.mark_deleted_vbid_list_size(); ++i) {
        uint64_t vbid = response.mark_deleted_vbid_list(i);
        if (response.mark_deleted_timestamp_list_size() > 0) {
            s = this->remove(vbid, true, response.mark_deleted_timestamp_list(i));
        } else {
            s = this->remove(vbid, true);
        }
        if (!s.ok()) {
            LOG(WARNING) << "delete shard failed, vid:" << _volume_id
                         << " vbid:" << vbid << " shard_index:" << _shard_index;
            return Status(AIE_FAIL, "remove failed");
        }
    }
    LOG(TRACE) << "put mark deleted shards succeeded, vid:" << _volume_id
               << " shard_index:" << _shard_index << " shards_num:" << _mark_deleted_record_num;
    return Status();
}

int LinkedStore::get_record_count(uint32_t record_type) const {
    std::unique_lock<std::mutex> lock_guard(_location_mutex);
    if (record_type == 0 || record_type > _max_record_type) {
        return 0;
    }
    return _record_containers[record_type].record_index;
}

Status LinkedStore::check_self() {
    std::unique_lock<std::mutex> lock_guard(_location_mutex);
    CheckLinkedListContext check_ctx;
    // scan db
    std::unique_ptr<LinkedStoreIndexer::Iterator> it(_indexer->seek(0));
    while (it->valid()) {
        aries::pb::RecordLocationExtraInfo extra_info;
        const RecordLocation *loc = it->value(&extra_info);
        // ignore mark-delete record
        if (loc != NULL) {
            check_ctx.check_rid_map[loc->record_type].push_back(loc->record_id);
            if (extra_info.has_prev_hole_vbid()) {
                check_ctx.check_hole_next_list_map[loc->record_type][extra_info.prev_hole_vbid()] = it->key();
                check_ctx.check_hole_prev_list_map[loc->record_type][extra_info.next_hole_vbid()] = it->key();
            }
        }
        it->next();
    }
    // check linket block list
    return check_linked_list(check_ctx);
}

Status LinkedStore::check_linked_list(CheckLinkedListContext& check_ctx) {
    auto& check_rid_map = check_ctx.check_rid_map;
    auto& check_hole_next_list_map = check_ctx.check_hole_next_list_map;
    auto& check_hole_prev_list_map = check_ctx.check_hole_prev_list_map;

    if (FLAGS_recover_mode && FLAGS_skip_bad_record) {
        LOG(NOTICE) << _filename << " skip_bad_record is open, skip check rid continuity";
    } else {

        // key is record_type, will start from 1;
        for (auto& pair : check_rid_map) {
            bool check_res = check_rid_continuity(pair.second);
            if (!check_res) {
                LOG(FATAL) << _filename << " record_type:" << pair.first << " rid is not continuous, vlet should be repaired";
                return Status(AIE_CORRUPT, "rid not continuous");
            }
        }
    }

    bool check_hole_ok = true;
    for (auto i = 1; i <= (int)_max_record_type; ++i) {
        auto hole_search = check_hole_next_list_map.find(i);
        if (hole_search == check_hole_next_list_map.end()) {
            // check holes num
            // db hole is empty, but memory hole is not empty
            check_hole_ok = (check_hole_ok && (_record_containers[i].hole_record_num == 0));
        }
    }
    for (auto hole_list : check_hole_next_list_map) {
        auto record_type = hole_list.first;
        // check holes num
        check_hole_ok = (check_hole_ok && (check_hole_next_list_map[record_type].size() ==
                    check_hole_prev_list_map[record_type].size()));
        check_hole_ok = (check_hole_ok && (check_hole_next_list_map[record_type].size() ==
                    _record_containers[record_type].hole_record_num));
        // check holes continuous
        check_hole_ok = (check_hole_ok &&
                check_hole_continuity(check_hole_next_list_map[record_type]));
        check_hole_ok = (check_hole_ok &&
                check_hole_continuity(check_hole_prev_list_map[record_type]));
        check_hole_ok = check_hole_ok &&
                check_prev_next_hole_list_consistency(
                                check_hole_prev_list_map[record_type],
                                check_hole_next_list_map[record_type]);
    }
#ifndef _UNIT_TEST
    assert(check_hole_ok);
#else
    if (!check_hole_ok) {   // for ut
        return Status(AIE_CORRUPT, "check holes failed");
    }
#endif
    return Status(AIE_OK);
}

void LinkedStore::update_fingerprint(uint64_t vbid) {
    _fingerprint_manager.update_fingerprint(vbid);
}

uint64_t LinkedStore::get_fingerprint_by_slot(uint32_t slot_id) {
    return _fingerprint_manager.get_fingerprint_by_slot(slot_id);
}

void LinkedStore::update_max_vbid(const uint64_t vbid) {
    uint64_t old_value = _max_vbid.load();
    while (true) {
        if (vbid <= old_value) {
            return;
        }

        if (_max_vbid.compare_exchange_strong(old_value, vbid)) {
            return;
        }
    }
}

#if defined(_CHECK_TEST) || defined(_UNIT_TEST)
std::string LinkedStore::rand_str_for_test(const int len) {
    std::string ret;
    for (int i = 0; i < len; ++i) {
        ret += char(base::fast_rand()%256);
    }
    return ret;
}

Status LinkedStore::write_shard_record_for_test(const RecordLocation &location,
        const char* buf, uint32_t crc) {
    assert(sizeof(ShardRecordGuard) == SHARD_RECORD_GUARD_SIZE);
    int64_t record_bytes = record_type_page_num(location.record_type) * _page_size;
    ShardRecordGuard *guard = (ShardRecordGuard *) (buf + record_bytes - sizeof(ShardRecordGuard));
    guard->record_magic =
            _use_standard_record_layout ? common::STANDARD_RECORD_FOOTER_MAGIC : SHARD_RECORD_MAGIC;
    guard->record_timestamp = base::gettimeofday_us();
    guard->record_id = location.record_id;
    guard->prev_block_id = location.block_id;
    guard->record_type = location.record_type;
    guard->reserved = 0;
    if (crc == 0) {
        guard->record_crc32 = base::crc32c::Value(buf, record_bytes - sizeof(uint32_t));
    } else {
        guard->record_crc32 = crc;
    }
    int ret = write_location(location, buf);
    if (ret != 0) {
        return Status::by_errno(ret, "write shard record failed of test");
    }
    return Status(AIE_OK);
}

Status LinkedStore::put_for_test(RecordLocation& location, uint64_t vbid, const aries::pb::ShardMeta &meta, 
        base::IOBuf &data, int op) {
    uint32_t shard_crc = 0;
    uint32_t input_bytes = ShardRecord::byte_size(meta, data.size(), _page_size);
    uint32_t record_type = bytes_to_type(input_bytes);
    if (record_type > _max_record_type) {
        return Status(AIE_INVALID_ARGUMENT, "data too large");
    }
    aries::pb::ShardCompressOption compress_option;
    get_serialize_config_with_lock(&compress_option);

    Status s;
    if (_use_standard_record_layout) {
        //serialize record
        common::Buffer result;
        aries::pb::SliceDescMeta slice_desc_meta;
        uint32_t record_type;
        uint32_t record_data_offset;
        common::SerializeArgs serialize_args(sizeof(ShardRecordGuard) - sizeof(uint32_t), &record_type, &record_data_offset);
        auto s = do_serialize_record(vbid, data, meta, compress_option, &result, &slice_desc_meta, &serialize_args);
        if (!s.ok()) {
            LOG(WARNING) << "failed to serialize record to buf, vbid:" << vbid
                        << " return error:"<< s;
            return s;
        }
        if (op == 7) {
            for (int i = 0; i < 10; ++i) {
                *(result.buf() + sizeof(common::StandardRecordHeader) + i) += 1;
            }
        }

        if (op == 0 || op == 1) {
            shard_crc = base::crc32c::Value(result.buf(), result.size() - sizeof(uint32_t));
            if (op == 0) {
                *(result.buf() + sizeof(common::StandardRecordHeader) + 5) += char(1);
            } else if (op == 1) {
                common::StandardRecordHeader* header_ptr = reinterpret_cast<common::StandardRecordHeader*>(result.buf());
                header_ptr->record_body_desc_info.record_meta_len += 1;
            }
        }

        std::unique_lock<std::mutex> lock_guard(_location_mutex);
        s = write_shard_record_for_test(location, result.buf(), shard_crc);
        lock_guard.unlock();
        if (!s.ok()) {
            LOG(WARNING) << "write disk failed during put record, vid:" << _volume_id
                         << " vbid:" << vbid << " shard_index:" << _shard_index
                         << "loc:" << location << " error:" << s;
            return s;
        }
    } else {
        if (op == 7) {
            data.pop_back(10);
            std::string tmp = rand_str_for_test(10);
            data.append(tmp);
        }
        uint64_t record_bytes = record_type_page_num(record_type) * _page_size;
        char *buf = (char *) ::memalign(_page_size, record_bytes);
        assert(buf != NULL);
        std::unique_ptr<char, decltype(&::free)> buffer(buf, &::free);

        buf += ShardRecord::serialize_head(vbid, meta, buf);
        data.copy_to(buf);
        buf += data.size();
        memset(buf, 0, record_bytes - input_bytes);

        if (op == 0 || op == 1) {
            shard_crc = base::crc32c::Value(buffer.get(), record_bytes - sizeof(uint32_t));
            if (op == 0) {
                *(buf - 5) += char(1);
            } else if (op == 1) {
                if (base::fast_rand() % 2) { // change key
                    *(buf - data.size() - 1) += char(1);
                } else { // change meta
                    *(buf - data.size() - meta.user_meta().size() - 1) += char(1);
                }
            }
        }
        std::unique_lock<std::mutex> lock_guard(_location_mutex);
        s = write_shard_record_for_test(location, buffer.get(), shard_crc);
        lock_guard.unlock();
        if (!s.ok()) {
            LOG(WARNING) << "write disk failed during put record, vid:" << _volume_id
                         << " vbid:" << vbid << " shard_index:" << _shard_index
                         << "loc:" << location << " error:" << s;
            return s;
        }
    }

    update_max_vbid(vbid);
    LOG(TRACE) << "put record succeeded, vid:" << _volume_id << " vbid:" << vbid
               << " shard_index:" << _shard_index << " loc:" << location;
    return s;
}

Status LinkedStore::change_shard(uint64_t vbid, int op) {
    LOG(NOTICE) << "change shard for vid:" << _volume_id << " vbid:" << vbid
                << " shard_index:" << _shard_index << " op:" << op;
    aries::pb::ShardMeta meta;
    base::IOBuf data;
    std::string tmp = "";
    aries::pb::RecordLocationExtraInfo extra_info;

    char c = '\0';
    uint32_t check_sum = 0;
    uint32_t ori_sum = 0;
    RecordLocation location;
    RecordLocation mlocation;
    Status s = _indexer->get(vbid, &mlocation, &extra_info);
    if (!s.ok() && !(s.code() == AIE_MARK_REMOVED)) {
        return Status(AIE_BLOB_NOT_EXIST, "record not exist");
    }

    get(vbid, &meta, &data);
    if (data.size() != meta.shard_len()) {
        return Status(AIE_INVALID_ARGUMENT, "size not match");
    }
    uint32_t input_bytes = ShardRecord::byte_size(meta, data.size(), _page_size);
    uint32_t record_type = bytes_to_type(input_bytes);
    if (record_type > _max_record_type) {
        return Status(AIE_INVALID_ARGUMENT, "data too large");
    }
    uint64_t record_bytes = record_type_page_num(record_type) * _page_size;
    char *buf = (char *) ::memalign(_page_size, record_bytes);
    assert(buf != NULL);
    std::unique_ptr<char, decltype(&::free)> buffer(buf, &::free);

    switch (op) {
        case 0: // change shard
            break;
        case 7: // change shard and footer crc
            break;
        case 1: // change meta
            break;
        case 8: // change meta and footer crc
            if (meta.key().length() != 0) {
                auto key = rand_str_for_test(meta.key().length());
                meta.set_key(key);
            } else if (meta.user_meta().length() != 0) {
                auto user_meta = rand_str_for_test(meta.user_meta().length());
                meta.set_user_meta(user_meta);
            } else {
                LOG(WARNING) << " vbid:" << vbid << " doesn't has meta";
            }
            break;
        case 2: // change data and crc
            data.pop_back(10);
            tmp = rand_str_for_test(10);
            data.append(tmp);
            check_sum = base::crc32c::Value(data.to_string().c_str(), data.to_string().size()); 
            meta.set_shard_crc(check_sum);
            break;
        case 3: // change meta and crc
            if (meta.key().length() != 0) {
                auto key = rand_str_for_test(meta.key().length());
                meta.set_key(key);
            } else if (meta.user_meta().length() != 0) {
                auto user_meta = rand_str_for_test(meta.user_meta().length());
                meta.set_user_meta(user_meta);
            } else {
                LOG(WARNING) << "vbid:" << vbid << " has no meta";
            }
            check_sum = base::crc32c::Value(meta.key().data(), meta.key().size());
            check_sum = base::crc32c::Extend(check_sum, meta.user_meta().c_str(), meta.user_meta().size());
            meta.set_key_meta_crc(check_sum);
            break;
        case 4: // change location
            {
                std::unique_lock<std::mutex> lock_guard(_location_mutex);
                Status s = _indexer->get(vbid, &location, &extra_info);
                RecordLocation tail = get_location(location.record_type);
                // delete index
                _indexer->remove(vbid, false, NULL);
                // insert new but not continued record id
                tail.record_id += 1;
                _indexer->put(vbid, tail);
            }
            return Status(AIE_OK, "");
        case 5: // delete shard
            s = remove(vbid, true);
            return s;
        case 6: // restore shard
            s = restore(vbid);
            return s;
        default:
            return Status(AIE_FAIL, "not support op");
    }
    put_for_test(mlocation, vbid, meta, data, op);
    return s;
}
#endif

}
}
