/***************************************************************************
 * 
 * Copyright (c) 2017 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/
/**
 * @file datanode/storage/indexer.cpp
 * <AUTHOR>
 * @date 2017/10/25 10:34:03
 * @brief 
 *  
 **/

#include "baidu/inf/aries/datanode/storage/random/linked_store_indexer.h"
#include <rocksdb/convenience.h>

namespace aries {
namespace datanode {

// write batch
RocksdbWriteBatch::RocksdbWriteBatch(uint64_t vid, std::shared_ptr<rocksdb::DB> db) {
    _vid = vid;
    _db = db;
}

void RocksdbWriteBatch::put(uint64_t vbid, const RecordLocation &loc,
    aries::pb::RecordLocationExtraInfo *extra_info) {
    RecordKey key(_vid, vbid);
    std::string buf((const char *)&loc, sizeof(loc));
    // Take it easy:
    //   Protobuf ensures that an empty message would be serialized to an empty string.
    if (extra_info != NULL) { 
        std::string extra_info_str;
        extra_info->SerializeToString(&extra_info_str);
        buf.append(extra_info_str);
    }
    _batch.Put(key.to_slice(), rocksdb::Slice(buf));
}

void RocksdbWriteBatch::remove(uint64_t vbid, bool mark_delete, RecordLocation *loc,
    aries::pb::RecordLocationExtraInfo *extra_info) {
    assert(mark_delete == (loc != NULL));
    assert(mark_delete == (extra_info != NULL));
    if (loc == NULL) {
        // finally detete
        RecordKey key(_vid, vbid);
        _batch.Delete(key.to_slice());
    } else {
        // mark delete
        assert(extra_info != NULL);
        extra_info->set_mark_deleted_timestamp((uint32_t)base::gettimeofday_s());
        std::string extra_info_str;
        assert(extra_info->SerializeToString(&extra_info_str));

        std::string buf((const char*)loc, sizeof(*loc));
        buf.append(extra_info_str);

        RecordKey key(_vid, vbid);
        _batch.Put(key.to_slice(), rocksdb::Slice(buf));
    }
}

Status RocksdbWriteBatch::commit() {
    rocksdb::WriteOptions opts;
    if (FLAGS_db_use_sync) {
        opts.sync = true;
    }
    TEST_SYNC_POINT_RETURN_WITH_INT_VALUE("Linkedstore_RocksdbWriteBatch::commit::return");
    return _db->Write(opts, &_batch);
}


// iterator
RocksdbIterator::RocksdbIterator(uint64_t vid, rocksdb::Iterator *it) {
    _vid = vid;
    _it = it;
}

RocksdbIterator::~RocksdbIterator() {
    if (_it != NULL) {
        delete _it;
    }
}

uint64_t RocksdbIterator::key() const {
    RecordKey *key = (RecordKey *) _it->key().data();
    return key->vbid();
}

const RecordLocation* RocksdbIterator::value(aries::pb::RecordLocationExtraInfo *extra_info) const {
    // old-version mark-delete
    if (_it->value().size() == 0) {
        return NULL;
    }
    // parse extra_info
    if (_it->value().size() > sizeof(RecordLocation)) {
        std::string data(_it->value().data(), _it->value().size());
        assert(extra_info->ParseFromString(data.substr(sizeof(RecordLocation))));
    }
    // value maybe contain RecordLocation and RecordLocationExtraInfo
    // only return RecordLocation
    assert(_it->value().size() >= sizeof(RecordLocation));
    return (RecordLocation *)_it->value().data();
}

bool RocksdbIterator::valid() {
    if (!_it->Valid()) {
        rocksdb::Status s = _it->status();
        if (!s.ok()) {
            LOG(WARNING) << "iterator in rocksdb encounters error:" << s.ToString();
        }
        return false;
    }
    RecordKey *key = (RecordKey *) _it->key().data();
    if (key->vid() != _vid) {
        return false;
    }
    return true;
}

void RocksdbIterator::next() {
    _it->Next();
    trim();
}

RocksdbIterator *RocksdbIterator::trim() {
    while (valid()) {
        // may be a volume key, need check key and value len
        if (_it->key().size() == sizeof(RecordKey) &&
                (_it->value().size() == 0 || _it->value().size() >= sizeof(RecordLocation))) {
            break;
        }
        _it->Next();
    }
    return this;
}

// indexer
Status RocksdbIndexer::get(uint64_t vbid, RecordLocation *loc,
        aries::pb::RecordLocationExtraInfo *extra_info) {
    RecordKey key(_vid, vbid);
    std::string data;
    rocksdb::Status s;

    {
        common::TimeMeasure time_measure(&g_rocksdb_engine_read_index_latency);
        s = _db->Get(rocksdb::ReadOptions(), key.to_slice(), &data);
        TEST_SYNC_POINT_RETURN_WITH_INT_VALUE("Linkedstore_RocksdbIndexer::get::return");
    }

    if (s.ok()) {
        if (data.size() == 0) {
            return Status(AIE_REMOVED);
        }
        if (data.size() == sizeof(RecordLocation)) {
            memcpy(loc, data.data(), data.size());
            return s;
        }

        assert(data.size() > sizeof(RecordLocation));
        aries::pb::RecordLocationExtraInfo tmp_pb;
        memcpy(loc, data.c_str(), sizeof(RecordLocation));
        std::string extra_info_str = data.substr(sizeof(RecordLocation));
        assert(tmp_pb.ParseFromString(extra_info_str));
        if (extra_info != NULL) {
            extra_info->CopyFrom(tmp_pb);
        }
        if (tmp_pb.has_mark_deleted_timestamp() && tmp_pb.mark_deleted_timestamp() > 0) { // is mark deleted
            return Status(AIE_MARK_REMOVED);
        }
        if (tmp_pb.has_prev_hole_vbid()) {   // is hole
            return Status(AIE_BLOB_NOT_EXIST);
        }
        return Status(AIE_OK);
    }

    return s;
}

Status RocksdbIndexer::put(uint64_t vbid, const RecordLocation &loc,
        aries::pb::RecordLocationExtraInfo *extra_info) {
    RecordKey key(_vid, vbid);
    std::string buf((const char *)&loc, sizeof(loc));
    // Take it easy:
    //   Protobuf ensures that an empty message would be serialized to an empty string.
    if (extra_info != NULL) {
        std::string extra_info_str;
        extra_info->SerializeToString(&extra_info_str);
        buf.append(extra_info_str);
    }

    common::TimeMeasure time_measure(&g_rocksdb_engine_write_index_latency);
    rocksdb::WriteOptions opts;
    if (FLAGS_db_use_sync) {
        opts.sync = true;
    }
    TEST_SYNC_POINT_RETURN_WITH_INT_VALUE("Linkedstore_RocksdbIndexer::put::return");
    return _db->Put(opts, key.to_slice(), rocksdb::Slice(buf));
}

Status RocksdbIndexer::remove(uint64_t vbid, bool mark_delete, RecordLocation *loc,
    aries::pb::RecordLocationExtraInfo *extra_info, uint32_t mark_delete_timestamp) {
    assert(mark_delete == (loc != NULL));
    assert(mark_delete == (extra_info != NULL));
    if (loc == NULL) {
        // finally detete
        RecordKey key(_vid, vbid);
        return _db->Delete(rocksdb::WriteOptions(), key.to_slice());
    } else {
        // mark delete
        assert(extra_info != NULL);
        if (mark_delete_timestamp == 0) {
            mark_delete_timestamp = static_cast<uint32_t>(base::gettimeofday_s());
        }
        extra_info->set_mark_deleted_timestamp(mark_delete_timestamp);
        std::string extra_info_str;
        assert(extra_info->SerializeToString(&extra_info_str));

        std::string buf((const char*)loc, sizeof(*loc));
        buf.append(extra_info_str);

        TEST_SYNC_POINT_RETURN_WITH_INT_VALUE("Linkedstore_RocksdbIndexer::remove::returns");

        RecordKey key(_vid, vbid);
        rocksdb::WriteOptions opts;
        if (FLAGS_db_use_sync) {
            opts.sync = true;
        }
        return _db->Put(opts, key.to_slice(), rocksdb::Slice(buf));
    }
}

RocksdbIterator *RocksdbIndexer::seek(uint64_t vbid) {
    RecordKey key(_vid, vbid);
    rocksdb::Iterator *it = _db->NewIterator(rocksdb::ReadOptions());
    it->Seek(key.to_slice());
    return (new RocksdbIterator(_vid, it))->trim();
}

bool RocksdbIndexer::has_volume_key() {
    VolumeKey volume_key;
    volume_key.set_volume_id(_vid);
    volume_key.set_vlet_type(_type);
    rocksdb::Slice key((const char*)&volume_key, kVolumeKeyLen);
    std::string info;
    rocksdb::Status s = _db->Get(rocksdb::ReadOptions(), key, &info);
    return s.ok();
}

LinkedStoreIndexer *LinkedStoreIndexer::create(uint64_t vid, uint32_t type, std::shared_ptr<rocksdb::DB> db) {
    return new RocksdbIndexer(vid, type, db);
}

Status RocksdbIndexer::destroy() {
    RecordKey begin(_vid, 0);
    RecordKey end(_vid, UINT64_MAX);

    rocksdb::WriteBatch batch;
    std::unique_ptr<rocksdb::Iterator> it(_db->NewIterator(rocksdb::ReadOptions()));
    it->Seek(begin.to_slice());
    while (it->Valid()) {
        if (it->key().size() != sizeof(RecordKey)) {
            it->Next();
            continue;
        }
        RecordKey *key = (RecordKey *) it->key().data();
        if (key->vid() != _vid) {
            break;
        }
        batch.Delete(it->key());
        it->Next();
    }
    rocksdb::WriteOptions opts;
    if (FLAGS_db_use_sync) {
        opts.sync = true;
    }
    rocksdb::Status s = _db->Write(opts, &batch);
    if (!s.ok()) {
        return s;
    }
    return Status();
}

}
}

