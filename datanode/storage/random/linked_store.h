/***************************************************************************
 * 
 * Copyright (c) 2017 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/
/**
 * @file linked_store.h
 * <AUTHOR>
 * @date 2017/10/21 15:43:27
 * @brief 
 *  
 **/
#ifndef BAIDU_INF_ARIES_DATANODE_STORAGE_LINKED_STORE_H
#define BAIDU_INF_ARIES_DATANODE_STORAGE_LINKED_STORE_H

#include <atomic>

#include "base/iobuf.h"
#include "rocksdb/db.h"

#include "baidu/inf/aries/common/status.h"
#include "baidu/inf/aries/common/bit_map_manager.h"
#include "baidu/inf/aries/datanode/disk_agent.h"
#include "baidu/inf/aries/datanode/storage/random/linked_store_indexer.h"
#include "baidu/inf/aries/datanode/storage/record/linked_record_serializer.h"
#include "baidu/inf/aries/datanode/storage/store_base.h"
#include "baidu/inf/aries/datanode/util/util.h"
#include "baidu/inf/aries/datanode/io_context.h"
#include "baidu/inf/aries-api/common/proto/datanode.pb.h"
#include "baidu/inf/aries-api/common/lock.h"

namespace aries {
namespace datanode {

class Interruptor;

class LinkedStore : public BaseStore {
public:
    friend class ShardRecordScanner;
    friend class LinkedVlet;
    friend class RecordLocationHelper;
    friend class CacheLinkedStorage;
    struct RecordContainer {
        RecordContainer() : record_index(0), head_hole_vbid(0), tail_hole_vbid(0), hole_record_num(0) {}

        uint32_t free_block_num(uint32_t record_page_num, uint32_t block_page_num) {
            auto block_num = common::calc_ceil(record_page_num * record_index, block_page_num);
            auto block_num_of_excluded_holes =
                common::calc_ceil(record_page_num * (record_index - hole_record_num), block_page_num);
            return block_num - block_num_of_excluded_holes;
        }

        std::vector<uint32_t> cells;
        uint32_t record_index;
        // remove opt
        uint64_t head_hole_vbid; // pop hole
        uint64_t tail_hole_vbid; // push hole
        uint32_t hole_record_num;
    };

    struct RangeGetContext {
        RangeGetContext() : offset(0), len(0), record_data_offset(0) {}
        
        uint32_t offset;
        uint32_t len;
        uint32_t record_data_offset;

        std::string to_string() {
            std::ostringstream oss;
            oss << "(offset:" << offset << " len:" << len 
                << " record_data_offset:" << record_data_offset << ")";
            return oss.str();
        }
    };

    struct CheckLinkedListContext {
        std::map<uint32_t, std::vector<uint32_t>> check_rid_map;
        std::map<uint32_t, std::map<uint64_t, uint64_t>> check_hole_next_list_map;
        std::map<uint32_t, std::map<uint64_t, uint64_t>> check_hole_prev_list_map;
    };

    uint32_t bytes_to_type(uint32_t bytes) {
        if (bytes <= _min_record_page_num * _page_size) {
            return 1;
        }
        auto gap_bytes = _record_gap_page_num * _page_size;
        return common::calc_ceil(bytes - _min_record_page_num * _page_size, gap_bytes) + 1;
    }

    uint32_t record_type_page_num(uint32_t record_type) {
        assert(record_type >= 1 && record_type <= _max_record_type);
        return _min_record_page_num + (record_type - 1) * _record_gap_page_num;
    }

    uint32_t max_record_type() {
        return _max_record_type;
    }

    void refresh_hole_info(uint64_t vid) {
        std::unique_lock<std::mutex> lock_guard(_location_mutex);
        _hole_record_num = 0;
        _hole_size = 0;
        _can_free_hole_size = 0;
        for (auto i = 1; i <= (int)_max_record_type; ++i) {
            RecordContainer container = _record_containers[i];
            _hole_record_num += container.hole_record_num;
            _hole_size += (container.hole_record_num * record_type_page_num(i) * _page_size);
            _can_free_hole_size += (container.free_block_num(record_type_page_num(i), _block_page_num) *
                    _block_size);
            /* 
            LOG(TRACE) << "volume_id:" << vid << " record_type:" << i
                       << " record_num:" << container.record_index
                       << " head_hole_vbid:" << container.head_hole_vbid
                       << " tail_hole_vbid:" << container.tail_hole_vbid
                       << " hole_record_num:" << container.hole_record_num << " hole_size:" <<
                       container.hole_record_num * record_type_page_num(i) * PAGE_SIZE
                       << " can_free_hole_size:" <<
                       container.free_block_num(record_type_page_num(i), _block_page_num) * _block_size;
            */
        }
    }

    // posix utils, return posix errno
    static int posix_read(int fd, void *buf, int64_t size_bytes, int64_t offset_bytes);
    static int posix_write(int fd, const void *buf, int64_t size_bytes, int64_t offset_bytes);
    int preallocate_space(int fd, off_t size);
    static bool check_rid_continuity(std::vector<uint32_t>& rid_vector);
    static bool check_hole_continuity(std::map<uint64_t, uint64_t>& hole_map);
    static bool check_prev_next_hole_list_consistency(
        std::map<uint64_t, uint64_t> &prev_hole_map,
        std::map<uint64_t, uint64_t> &next_hole_map);

public:
    LinkedStore(const StoreOptions &opts);
    virtual ~LinkedStore();

    virtual Status create(const aries::pb::LinkedVletInfo& vlet_info, std::shared_ptr<rocksdb::DB> db,
            const std::string &path);
    virtual Status open(std::shared_ptr<rocksdb::DB> db, const std::string &path, bool readonly = false);
    virtual Status destroy();

    const std::string &name() { return _name; }

    bool permit_data_offset_index() const { return _permit_data_offset_index; }

    // interfaces
    virtual Status put(const uint64_t vbid, const pb::ShardMeta &meta, const base::IOBuf &data) override;
    virtual Status get(uint64_t vbid, aries::pb::ShardMeta *meta, base::IOBuf *data) override;
    virtual Status range_get(uint64_t vbid, uint32_t offset, uint32_t len,
                             aries::pb::ShardMeta *meta, base::IOBuf *data);
    virtual Status get_record_index_info(uint64_t vbid, aries::pb::RecordIndexInfo *record_index_info) override;
    virtual Status restore(uint64_t vbid) override;
    Status remove(uint64_t vbid, bool mark_delete) override;
    Status remove(uint64_t vbid, bool mark_delete, uint32_t mark_delete_timestamp);
    virtual Status batch_put_record(const std::vector<aries::pb::CopyVletRecordMeta>& record_meta_list,
            base::IOBuf* iobuf) override;

#if defined(_CHECK_TEST) || defined(_UNIT_TEST)
    virtual Status change_shard(uint64_t vbid, int op);
#endif

    Status batch_get_segment(GetSegmentContext* ctx);
    Status check_self();

    // update vlet meta;
    void update_meta(const aries::pb::LinkedVletInfo& vlet_info);
    // update shard meta;
    Status update_meta(uint64_t vbid, const aries::pb::ShardMeta &meta);

    UnitIoStats get_io_stats() {
        return _io_stats_collector.get_and_reset();
    }

    // serialize/deserialize record
    virtual Status do_serialize_record(
            const uint64_t vbid,
            const base::IOBuf &data,
            const aries::pb::ShardMeta &meta,
            const aries::pb::ShardCompressOption &compress_option,
            common::Buffer *result,
            aries::pb::SliceDescMeta *slice_desc_meta,
            void *args) override;

    virtual Status do_deserialize_record(
            const common::Buffer &record_buf,
            uint64_t *vbid,
            aries::pb::SliceDescMeta* slice_desc_meta,
            pb::ShardMeta *meta,
            base::IOBuf *data) override;

    Status do_deserialize_record(
            const common::Buffer &record_buf,
            uint64_t *vbid,
            aries::pb::SliceDescMeta* slice_desc_meta,
            pb::ShardMeta *meta,
            base::IOBuf *data,
            uint32_t *record_data_offset,
            bool *is_standard_record_layout);

private:
    Status do_put(
            const uint64_t vbid,
            const uint32_t record_type,
            const uint32_t record_data_offset,
            const aries::pb::SliceDescMeta& slice_desc_meta,
            const common::Buffer& data_buffer);

    Status do_get(uint64_t vbid, RecordLocation& loc,
                    aries::pb::ShardMeta *meta,
                    base::IOBuf *data,
                    bool* vbid_mismatch = nullptr);

    Status do_range_get(uint64_t vbid, RecordLocation& loc, RangeGetContext& ctx,
                        common::Buffer* range_get_buffer, uint32_t * data_offset_in_buf);

    Status batch_get_record(uint32_t record_type, base::IOBuf *data, uint32_t from_id);

    int inline_posix_read(int fd, void* buf, int64_t size_bytes, int64_t offset_bytes);

    int inline_posix_write(int fd, const void* buf, int64_t size_bytes, int64_t offset_bytes);

    void get_mark_deleted_vbids(aries::pb::CopyLinkedVletResponse *response);

    Status put_mark_deleted_vbids(const aries::pb::CopyLinkedVletResponse &response);

    int get_record_count(uint32_t record_type) const;
    uint64_t free_size() const {
        std::unique_lock<std::mutex> lock_guard(_location_mutex);
        return _block_bitmap.free_pos_num() * _block_size + _can_free_hole_size;
    }
    int32_t record_num() {
        return _total_record_num.load() - mark_deleted_record_num() - hole_record_num();
    }
    int32_t mark_deleted_record_num() {
        return _mark_deleted_record_num.load();
    }
    int32_t hole_record_num() {
        return _hole_record_num.load();
    }
    uint64_t hole_size() {
        return _hole_size.load();
    }
    uint64_t can_free_hole_size() {
        return _can_free_hole_size.load();
    }
    uint64_t max_vbid() {
        return _max_vbid.load();
    }

    void update_fingerprint(uint64_t vbid);

    uint64_t get_fingerprint_by_slot(uint32_t slot_id);

    void set_create_worker(Interruptor* create_worker) {
        _create_worker = create_worker;
    }

    // Should call when _location_mutex is holding;
    Status check_linked_list(CheckLinkedListContext& check_ctx);

private:
    Status write_footer(const aries::pb::LinkedVletInfo &footer);
    Status read_footer(aries::pb::LinkedVletInfo *footer);
    void init_meta(const aries::pb::LinkedVletInfo& vlet_info);

    Status check_db();
    Status build_location_map();
    Status recover_index();

    // location management
    void put_location(uint64_t vbid, const RecordLocation &loc,
                      const aries::pb::RecordLocationExtraInfo &extra_info);
    int alloc_location(uint32_t record_type, RecordLocation *location);
    void free_location(uint32_t record_type);
    RecordLocation get_location(uint32_t record_type);
    /** 
     * next_block_id: zero for just one block
     * size: how many pages to read
     * offset: the offset in block, number of pages
     * need_clean_buf: hint to specify whether need clean buf
     */
    int read_block(uint32_t block_id, uint32_t next_block_id, char *buf, uint32_t size,
            uint32_t offset, bool need_clean_buf);
    int range_read_block(uint32_t block_id, uint32_t next_block_id, char *buf, uint32_t size,
        uint32_t offset, bool need_clean_buf);
    int read_location(const RecordLocation &location, char *buf);
    int write_location(const RecordLocation &location, const char *buf);
    int write_buffer_or_dio(const char* buf, int64_t size_bytes, int64_t offset_bytes);

    Status read_shard_record_internal(const RecordLocation &location, char *buf);
    Status read_shard_record(const RecordLocation &location, char *buf);

    Status write_shard_record_internal(const RecordLocation& location,
                                       const char* buf,
                                       const bool use_standard_record_layout = false);
    Status write_shard_record(const RecordLocation& location, const char* buf, const bool use_standard_record_layout);

    Status move_shard_record(const RecordLocation& from, const RecordLocation& to,
                             uint64_t* vbid, aries::pb::RecordLocationExtraInfo* extra_info);

    // fast remove optimization, reduce remove's average IOPS (than normally remove which move last record to hole)
    // 1.remove only update rocksdb's key, generate a hole
    // 2.batch move last block's records to holes if vlet reach some hole's condition
    enum class FillHoleType {
        FREE_LAST_BLOCK = 0,    // move last block's records to holes and free the last block
        FREE_LAST_RECORD = 1,   // move last record to hole and decrease holes' num
    };
    bool pick_record_type_for_fill_holes(bool force_pick,
            FillHoleType* fill_hole_type, uint32_t* record_type);
    Status update_hole_meta(LinkedStoreIndexer::WriteBatch* batch, RecordContainer& container,
            uint64_t hole_vbid, const aries::pb::RecordLocationExtraInfo& hole_info);
    Status fill_holes(FillHoleType fill_hole_type, uint32_t record_type);
    Status finally_remove(uint64_t vbid, const RecordLocation &location);

    void update_max_vbid(const uint64_t vbid);

    void get_serialize_config_with_lock(
            aries::pb::ShardCompressOption* compress_option);

#if defined(_CHECK_TEST) || defined(_UNIT_TEST)
    std::string rand_str_for_test(const int len);
    Status write_shard_record_for_test(const RecordLocation &location,
            const char* buf, uint32_t crc);
    Status put_for_test(RecordLocation& location, uint64_t vbid, const aries::pb::ShardMeta &meta, 
            base::IOBuf &data, int op);
#endif

private:
    // The following member will not change after initialized;
    std::string _name;
    std::string _filename;
    uint64_t _volume_id = 0;
    uint32_t _shard_index = 0;
    StoreOptions _opts;

    int _buffer_io_fd{-1};   // use kernel buffer
    int _dio_fd{-1};         // direct io
    LinkedStoreIndexer *_indexer{nullptr};

    uint32_t _min_record_page_num;
    uint32_t _max_record_page_num;
    uint32_t _record_gap_page_num;
    uint32_t _max_record_type;
    uint32_t _block_page_num;
    uint64_t _block_size;   // byte unit
    uint32_t _block_num;
    uint64_t _page_size;   // byte unit
    // set data_offset_index in db for records, used in fast range get
    bool _permit_data_offset_index{false};
    bool _use_standard_record_layout{false};

    // The following members will be changed by ops and protected by _location_mutex
    common::BitMapManager _block_bitmap;
    RecordContainer *_record_containers;

    // The following variables should be atomic as concurrency reading without lock;
    std::atomic<uint64_t> _max_vbid{0};
    std::atomic<int32_t> _total_record_num{0}; // include mark_deleted records and hole records
    std::atomic<int32_t> _mark_deleted_record_num{0};
    std::atomic<int32_t> _hole_record_num{0};
    std::atomic<uint64_t> _hole_size{0};
    std::atomic<uint64_t>  _can_free_hole_size{0}; // can free blocks' size
    mutable std::mutex _location_mutex;

    // fast remove optimization
    bool _permit_fast_remove;

    FingerprintManager _fingerprint_manager{kSlotNumber};
    std::unique_ptr<common::IRecordSerializer> _linked_record_serializer;
    std::unique_ptr<common::IRecordSerializer> _standard_record_serializer;

    std::mutex _meta_mutex;
    aries::pb::LinkedVletInfo _vlet_info;

    // fast remove optimization
    std::atomic<uint64_t> _max_holes_size_for_fast_remove;
    std::atomic<uint64_t> _min_record_size_for_fast_remove;

    Interruptor* _create_worker = nullptr; // used to interrupt creating process;
    UnitIoStatsCollector _io_stats_collector; // Be thread-safe;
    std::atomic<bool> _fsync_when_create_and_close = false;
};

}
}

#endif

