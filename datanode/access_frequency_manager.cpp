#include <base/logging.h>
#include "baidu/inf/aries/common/common.h"
#include "baidu/inf/aries/datanode/access_frequency_manager.h"

namespace aries {
namespace datanode {

AccessFrequencyManager::AccessFrequencyManager(uint64_t item_count_capacity) {
    _capacity = item_count_capacity;

    _window_size = 0;
    _max_window_size = _capacity * WINDOW_TO_CACHE_SIZE_RATIO;

    // number of frequency counters - roughly equal to the window size divided by error tolerance.
    uint32_t num_counters =
            static_cast<size_t>(std::exp(1.0) * _max_window_size / ERROR_THRESHOLD);
    num_counters = folly::nextPowTwo(num_counters);
    // approximate memory size is 512MB when cache capacity is 1T
    // NOTICE: acoording to the current configuration, the maximum cache capacity is 3085996MB (2.943TB) 
    _access_freq = CountMinSketch(num_counters, HASH_COUNT);

    LOG(TRACE) << "access frequency table using memory size:"
               << (int64_t) (_access_freq.get_byte_size() / common::MB) << "MB";
}

void AccessFrequencyManager::try_grow_access_counters(uint64_t capacity) {
    folly::SharedMutex::WriteHolder w{&_mutex};
    try_grow_access_counters_no_lock(capacity);
}

void AccessFrequencyManager::update_frequency(const ShardId& key) {
    folly::SharedMutex::WriteHolder w{&_mutex};

    _access_freq.increment(std::hash<ShardId>()(key));
    ++_window_size;
    // decay counts every *_max_window_size*.  This avoids having items that were
    // accessed frequently (were hot) but aren't being accessed  (are
    // cold) from staying in cache forever.
    if (_window_size == _max_window_size) {
        _window_size >>= 1;
        _access_freq.decay_counts_by(DECAY_FACTOR);
    }
}

void AccessFrequencyManager::reset_frequency(const ShardId& key) {
    folly::SharedMutex::WriteHolder w{&_mutex};
    _access_freq.reset_count(std::hash<ShardId>()(key));
}

uint32_t AccessFrequencyManager::get_frequency(const ShardId& key) const {
    folly::SharedMutex::ReadHolder r{&_mutex};
    return _access_freq.get_count(std::hash<ShardId>()(key));
}

int64_t AccessFrequencyManager::get_capacity() const {
    folly::SharedMutex::ReadHolder r{&_mutex};
    return _capacity;
}

uint32_t AccessFrequencyManager::get_max_window_size() const {
    folly::SharedMutex::ReadHolder r{&_mutex};
    return _max_window_size;
}

uint64_t AccessFrequencyManager::get_counter_size() const {
    folly::SharedMutex::ReadHolder r{&_mutex};
    return _access_freq.get_byte_size();
}

void AccessFrequencyManager::try_grow_access_counters_no_lock(uint64_t capacity) {
    // If the new capacity ask is more than double the current size, recreate
    // the approx frequency counters.
    if ((uint64_t) CAPACITY_GROW_FACTOR * _capacity > capacity) {
        return;
    } else {
        _capacity = std::max(capacity, (uint64_t) DEFAULT_CAPACITY);
    }

    _window_size = 0;
    _max_window_size = _capacity * WINDOW_TO_CACHE_SIZE_RATIO;

    // Number of frequency counters - roughly equal to the window size divided by
    // error tolerance.
    uint32_t num_counters =
            static_cast<size_t>(std::exp(1.0) * _max_window_size / ERROR_THRESHOLD);
    num_counters = folly::nextPowTwo(num_counters);
    _access_freq = CountMinSketch(num_counters, HASH_COUNT);

    LOG(TRACE) << "access frequency table using memory size grow to:"
               << (int64_t) (_access_freq.get_byte_size() / common::MB) << "MB";
}

}
}
