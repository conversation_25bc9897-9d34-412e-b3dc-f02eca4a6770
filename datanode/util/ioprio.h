// Copyright (c) 2019 Baidu.com, Inc. All Rights Reserved.
// Author: <PERSON><PERSON><PERSON><PERSON><PERSON>@baidu.com
// Data: Mon Dec  9 14:14:33 CST 2019
// Filename: ioprio.h
#pragma once
#include <unistd.h>
#include <sys/syscall.h>

namespace aries {
namespace datanode {

// for <PERSON><PERSON> kernal
#undef __NR_ioprio_set
#define __NR_ioprio_set 1000

static const int g_ioprio_level_high = 0;
static const int g_ioprio_level_middle = 4;
static const int g_ioprio_level_low = 7;

#define IOPRIO_CLASS_SHIFT    (13)
#define IOPRIO_PRIO_MASK    ((1UL << IOPRIO_CLASS_SHIFT) - 1)
#define IOPRIO_PRIO_CLASS(mask)    ((mask) >> IOPRIO_CLASS_SHIFT)
#define IOPRIO_PRIO_DATA(mask)    ((mask) & IOPRIO_PRIO_MASK)
#define IOPRIO_PRIO_VALUE(class, data)    (((class) << IOPRIO_CLASS_SHIFT) | data)
#define ioprio_valid(mask)    (IOPRIO_PRIO_CLASS((mask)) != IOPRIO_CLASS_NONE)

enum {
    IOPRIO_CLASS_NONE,
    IOPRIO_CLASS_RT,
    IOPRIO_CLASS_BE,
    IOPRIO_CLASS_IDLE,
};

enum {
    IOPRIO_WHO_PROCESS = 1,
    IOPRIO_WHO_PGRP,
    IOPRIO_WHO_USER,
};

static int ioprio_set(int which, int who, int ioprio) {
#ifdef SYS_ioprio_set
    return syscall(SYS_ioprio_set, which, who, ioprio);
#else
    errno = ENOSYS;
    return -1;
#endif
}

static int ioprio_get(int which, int who) {
#ifdef SYS_ioprio_set
    return syscall(SYS_ioprio_get, which, who);
#else
    errno = ENOSYS;
    return -1;
#endif
}

}
}