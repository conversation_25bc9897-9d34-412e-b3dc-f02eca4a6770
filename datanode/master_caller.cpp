// Copyright (c) 2016 Baidu.com, Inc. All Rights Reserved
// Author <PERSON><PERSON><PERSON><PERSON><PERSON>(chen<PERSON><PERSON><PERSON>@baidu.com)
// Date: Wed Nov  9 20:14:03 CST 2016

#include "baidu/inf/aries/datanode/master_caller.h"
#include "baidu/inf/aries/datanode/disk_agent.h"
#include "baidu/inf/aries-api/common/master_tracker.h"
#include "baidu/inf/aries/datanode/util/util.h"
#include "baidu/inf/aries/datanode/flags.h"
#include "baidu/inf/aries-api/common/types.h"
#include "baidu/inf/aries-api/common/utils.h"
#include "baidu/inf/aries-api/common/config.h"
#include "baidu/inf/aries-api/common/master_tracker.h"
#include "baidu/inf/aries-api/common/closure.h"
#include "baidu/inf/aries-api/common/rpc_stub.h"
#include "baidu/rpc/channel.h"
#include "base/fast_rand.h"
#include "base/time.h"

namespace aries {
namespace datanode {

std::atomic<uint64_t> MasterCaller::_s_primary_start_time{0};

#ifdef _UNIT_TEST
#define MASTER_DEFAULT_CODE AIE_OK
#else 
#define MASTER_DEFAULT_CODE AIE_INVALID
#endif

#define DATANODE_RPC_WITH_MASTER(CALL, REQ, RESP) \
    int ret_code = MASTER_DEFAULT_CODE; \
    std::vector<base::EndPoint> master_addrs = g_master_tracker->get_master_list(); \
    for (auto & addr : master_addrs) { \
        if (common::endpoint2int(addr) == 0) { \
            continue; \
        } \
        SynchronizedClosure closure; \
        RpcCallOptions options; \
        options.log_id = _log_id; \
        options.call_timeout_ms = _timeout_ms; \
        MasterStub stub; \
        stub.CALL(addr, REQ, RESP, &closure, &options); \
        closure.wait(); \
        if (response->status().code() == AIE_NOT_PRIMARY \
                || response->status().code() == AIE_TIMEOUT) { \
            continue; \
        } else { \
            ret_code = response->status().code(); \
            break; \
        } \
    } \
    return ret_code;

int MasterCaller::list_node_vlet(aries::pb::ListNodeVletRequest* request,
                                 aries::pb::ListNodeVletResponse* response) {
    DATANODE_RPC_WITH_MASTER(list_node_vlet, request, response);
}

int MasterCaller::heartbeat(aries::pb::NodeHeartbeatRequest* request,
                                aries::pb::AckResponse* response) {
    DATANODE_RPC_WITH_MASTER(node_heartbeat, request, response);
}

int MasterCaller::notice_stop(aries::pb::NodeNoticeStopRequest* request,
                                aries::pb::AckResponse* response) {
    DATANODE_RPC_WITH_MASTER(node_notice_stop, request, response);
}

int MasterCaller::report_bad_vlet(aries::pb::ReportBadVletRequest* request,
                                  aries::pb::AckResponse* response) {
    DATANODE_RPC_WITH_MASTER(report_bad_vlet, request, response);
}

int MasterCaller::report_bad_disk(aries::pb::ReportBadDiskRequest* request,
                                  aries::pb::AckResponse* response) {
    DATANODE_RPC_WITH_MASTER(report_bad_disk, request, response);
}

int MasterCaller::report_migrate_vlet(aries::pb::ReportMigrateVletRequest* request,
                                      aries::pb::AckResponse* response) {
    DATANODE_RPC_WITH_MASTER(report_migrate_vlet, request, response);
}


int MasterCaller::report_copy_vlet_finish(aries::pb::ReportMigrateVletRequest* request,
                                      aries::pb::AckResponse* response) {
    DATANODE_RPC_WITH_MASTER(report_copy_vlet_finish, request, response);
}

int MasterCaller::replace_vlet(aries::pb::ReplaceVletRequest* request, 
                               aries::pb::AckResponse* response) {
    DATANODE_RPC_WITH_MASTER(replace_vlet, request, response);
}

int MasterCaller::add_node(aries::pb::AddNodeRequest* request, aries::pb::AckResponse* response) {

    DATANODE_RPC_WITH_MASTER(add_node, 
                             request, 
                             response);
}
    
int MasterCaller::add_disk(aries::pb::MasterAddDiskRequest* request, aries::pb::AckResponse* response) {
    DATANODE_RPC_WITH_MASTER(add_disk, 
                             request, 
                             response);
}

int MasterCaller::get_gc_vlet_token(aries::pb::GetGcVletTokenRequest* request, aries::pb::AckResponse* response) {
    // master is old version, dn can gc
    if (_s_primary_start_time == 0) {
        LOG(TRACE) << "gc disk directly, because master is old version";
        return AIE_OK;
    }
    if (_s_primary_start_time + FLAGS_start_gc_time_second > (uint64_t)::base::gettimeofday_s()) {
        LOG(TRACE) << "skip gc vlet, because master is a new leader";
        return AIE_FAIL;
    }
    DATANODE_RPC_WITH_MASTER(get_gc_vlet_token,
                             request,
                             response);
}

int MasterCaller::get_gc_disk_token(aries::pb::GetGcDiskTokenRequest* request, aries::pb::AckResponse* response) {
    // master is old version, dn can gc
    if (_s_primary_start_time == 0) {
        LOG(TRACE) << "gc disk directly, because master is old version";
        return AIE_OK;
    }
    if (_s_primary_start_time + FLAGS_start_gc_time_second > (uint64_t)::base::gettimeofday_s()) {
        LOG(TRACE) << "skip gc disk, because master is a new leader";
        return AIE_FAIL;
    }
    DATANODE_RPC_WITH_MASTER(get_gc_disk_token,
                             request,
                             response);
}

} // end namespace of datanode
} // end namespace of aries

/* vim: set ts=4 sw=4 sts=4 tw=100 */
