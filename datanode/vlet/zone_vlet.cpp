// Copyright 2021 Baidu Inc. All Rights Reserved.
// @Author: la<PERSON><PERSON><PERSON>@baidu.com
// @Created Time : Thu Jul 29 19:39:35 CST 2021
// @File Name: zone_vlet.cpp
// @Description: vlet based on zone store

#include "baidu/inf/aries/datanode/vlet/zone_vlet.h"

#include <algorithm>

#include "baidu/inf/aries-api/common/rpc_stub.h"
#include "baidu/inf/aries/datanode/copy_vlet.h"
#include "baidu/inf/aries/datanode/datanode.h"
#include "baidu/inf/aries/datanode/vlet/linked_vlet.h"
#include "baidu/inf/aries/datanode/vlet_indexer.h"

namespace aries {
namespace datanode {

ZoneVlet::ZoneVlet(DiskAgentPtr disk_agent, const aries::pb::GenericVletInfo& vlet_info) :
        Vlet(disk_agent, vlet_info.volume_id(), vlet_info.vlet_type()) {

    _shard_index = vlet_info.shard_index();
    _vlet_version = vlet_info.vlet_version();
    _create_time = vlet_info.create_time();
    _volume_version = vlet_info.membership().volume_version();
    _vlet_info = vlet_info;
    base::string_printf(&_name, "ZoneVlet(vid:%lu shard_index:%u) ", _volume_id, _shard_index);
    LOG(NOTICE) << _name << "construct is success.";
}

ZoneVlet::~ZoneVlet() {
    LOG(NOTICE) << _name << " begin to destruction";
    if (!_drop_myself || _create_time) {
        return;
    }
    // volume key was mark dropped when DiskAgent drop vlet
    auto* zone_disk =  _disk_agent->get_zone_disk();
    if (FLAGS_enable_zone_vlet_delay_gc) {
        common::ScopedMutexLock lock(_meta_lock);
        _vlet_info.mutable_zone_vlet_info()->set_dropped_timestamp(base::gettimeofday_s());
        VletIndexer* vlet_indexer = zone_disk->vlet_indexer();
        auto s = vlet_indexer->put(_vlet_info);
        if (!s.ok()) {
            LOG(WARNING) << "zone disk failed to mark delete zone vlet, vid:"
                         << _volume_id << " disk_id:" << _disk_agent->disk_id() << " status:" << s;
        }

        s = zone_disk->add_dropped_vlet(_vlet_info);
        if (!s.ok()) {
            LOG(WARNING) << "zone disk failed to add dropped vlet, vid:"
                    << _volume_id  << " disk_id:" << _disk_agent->disk_id() << " status:" << s;
        }
    } else {
        if (_store != nullptr) {
            _store->destroy();
        }
    }

    g_datanode->vlet_manager()->remove_done(_volume_id);
}

ZoneVlet* ZoneVlet::new_zone_vlet(DiskAgentPtr disk_agent, const aries::pb::GenericVletInfo& vlet_info) {
    ZoneVlet* zone_vlet = new ZoneVlet(disk_agent, vlet_info);
    int ret = zone_vlet->create();

    if (ret != 0) {
        LOG(FATAL) << "create zone_vlet failed with ret:" << ret;
        zone_vlet->set_drop_me(true);
        delete zone_vlet;
        return nullptr;
    }
    return zone_vlet;
}

int ZoneVlet::create() {
    //construct ZoneStore;
    ZoneStore* store = ZoneStore::create(_vlet_info, _disk_agent->db(), _disk_agent->get_zone_disk());
    if (nullptr == store) {
        LOG(FATAL) << "create new zone store for vlet:" << _name << " is failed.";
        return -1;
    }
    _store.reset(store);
    return 0;
}

int ZoneVlet::open(const std::string& data_path) {
    //open ZoneStore
    ZoneStore* store = ZoneStore::open(_vlet_info.volume_id(),
                                       _vlet_info.vlet_type(),
                                       _disk_agent->db(),
                                       _disk_agent->get_zone_disk());
    if (nullptr == store) {
        LOG(FATAL) << "failed to open zone store for vlet:" << _name;
        return -1;
    }

    _store.reset(store);
    LOG(NOTICE) << _name << "load succeeded, vlet:" << _name;
    return 0;
}

int ZoneVlet::put(PutContext* ctx) {
    std::unique_ptr<PutContext> hold_ctx(ctx);
    ctx->add_point("queue=");
    g_datanode_write_request_enqueue_latency << base::gettimeofday_us() - ctx->en_queue_time_us;

    auto request = ctx->request;
    auto response = ctx->response;
    auto status = response->mutable_status();
    uint64_t vbid = request->vbid();

    auto& attachment_data = ctx->cntl->request_attachment();
    uint64_t attachment_size = attachment_data.size();

    if (ctx->priority != aries::HIGHEST) {
        g_datanode_repair_in_throughput_counter << ctx->cntl->request_attachment().size();
        g_datanode_repair_in_data_size_averager << ctx->cntl->request_attachment().size();
    } else {
        g_datanode_user_in_throughput_counter << ctx->cntl->request_attachment().size();
        g_datanode_user_in_data_size_averager << ctx->cntl->request_attachment().size();
    }

    uint64_t curr = base::gettimeofday_ms();
    if ((curr - ctx->en_queue_time) >= ctx->timeout_ms) {
        DN_LOG(TRACE) << "wait too long,no need to process, queue_time:" << curr - ctx->en_queue_time
                      << " timeout:" << ctx->timeout_ms << " vid:" << _volume_id;
        status->set_code(AIE_BUSY);
        status->set_msg("disk is busy,try later");
        return -1;
    }

    if (!_disk_agent->is_used()) {
        status->set_code(AIE_NOT_EXIST);
        status->set_msg("disk is offline");
        DN_LOG(TRACE) << "zone vlet put failed due to disk is offline," <<" vid:" << _volume_id;
        return -1;
    }

    if (is_disk_full()) {
        status->set_code(AIE_DISK_FULL);
        status->set_msg(std::to_string(_volume_id) + " disk full");
        return -1;
    }
    auto& data = ctx->cntl->request_attachment();
    TEST_SYNC_POINT_COVER_IOBUF("ZoneVlet::put::cover_data", &data, data.size() - 2000, 200);
   Status s = _store->put(vbid, request->shard_meta(), data);
    if (FLAGS_enable_cache && s.ok() && request->cache_pin_ttl_secs() > 0) {  // admit into cache directly by user hot entry hint
        auto* cache_mgr = _disk_agent->cache_manager();
        if (cache_mgr != nullptr && cache_mgr->enabled()) {
            _disk_agent->cache_manager()->dispatch_admit_task(ShardId(ctx->vid, ctx->vbid, _shard_index),
                                                              request->shard_meta(),
                                                              ctx->cntl->request_attachment(),
                                                              request->cache_pin_ttl_secs());
        }
    }

    ctx->add_point("store=");
    if (!s.ok()) {
        status->set_code(s.code());
        status->set_msg(s.msg());

        if (s.code() == AIE_DISK_FULL) {
            DN_LOG(WARNING) << _name << " put shard failed due to disk full,"
                            << " path:" << _disk_agent->disk_path();
            set_disk_full();
        }

        if (s.code() == AIE_IO_ERROR) {
            return -2;
        }
        return -1;
    }
    // fill membership
    status->set_code(AIE_OK);
    {
        common::ScopedMutexLock lock(_meta_lock);
        response->set_vlet_version(_vlet_info.vlet_version());
        response->mutable_membership()->CopyFrom(_vlet_info.membership());
    }

    if (FLAGS_enable_index_check) {
        std::shared_ptr<aries::datanode::BlobCheckContext> blob_ctx
            = std::make_shared<aries::datanode::BlobCheckContext>();
        blob_ctx->disk_id = _disk_agent->disk_id();
        blob_ctx->vid = _volume_id;
        blob_ctx->vbid = vbid;
        blob_ctx->log_id = ctx->log_id;
        blob_ctx->shard_index = _shard_index;
        auto checker = g_datanode->data_checker();
        checker->push_diff_blob(blob_ctx, 1);
    }
    return 0;
}

int ZoneVlet::get(GetContext* ctx) {
    std::unique_ptr<GetContext> hold_ctx(ctx);
    ctx->add_point("queue=");
    g_datanode_read_request_enqueue_latency << base::gettimeofday_us() - ctx->en_queue_time_us;

    auto request = ctx->request;
    auto response = ctx->response;

    {
        common::ScopedMutexLock lock(_meta_lock);
        response->set_vlet_version(_vlet_info.vlet_version());
        response->mutable_membership()->CopyFrom(_vlet_info.membership());
    }

    auto status = response->mutable_status();
    uint64_t vbid = request->vbid();

    uint64_t curr_us = base::gettimeofday_us();
    uint64_t curr = curr_us / 1000;
    if ((curr - ctx->en_queue_time) >= ctx->timeout_ms) {
        DN_LOG(TRACE) << "wait too long,no need to process, queue_time:" << curr - ctx->en_queue_time
                      << " timeout:" << ctx->timeout_ms << " vid:" << _volume_id;
        status->set_code(AIE_BUSY);
        status->set_msg("disk is busy,try later");
        return -1;
    }

    if (!_disk_agent->is_used()) {
        status->set_code(AIE_NOT_EXIST);
        status->set_msg("disk is offline");
        return -1;
    }

    int ret = 0;
    Status s;
    if (request->need_data()) {
        if (request->fast_range_get() && request->len() != 0) {
            s = _store->range_get(vbid, request->offset(), request->len(), response->mutable_shard_meta(),
                                  &ctx->cntl->response_attachment());
        } else {
            s = _store->get(vbid, response->mutable_shard_meta(), &ctx->cntl->response_attachment());
            if (FLAGS_enable_cache && _disk_agent->cache_manager()->enabled() && s.ok() && !ctx->skip_admit_to_cache) {
                _disk_agent->cache_manager()->dispatch_admit_task(ShardId(ctx->key, _shard_index),
                                                                  *response->mutable_shard_meta(),
                                                                  ctx->cntl->response_attachment());
            }
            // cut the data
            if (response->shard_meta().compress_type() == 0 && request->len() != 0) {
                base::IOBuf buf;
                ctx->cntl->response_attachment().append_to(&buf, request->len(), request->offset());
                ctx->cntl->response_attachment().clear();
                ctx->cntl->response_attachment().append(buf);
                uint32_t shard_crc = base::crc32c::Value(buf.to_string().data(), buf.size());
                response->mutable_shard_meta()->set_shard_crc(shard_crc);
            }
        }
    } else if (request->need_meta()) {
        s = _store->get(vbid, response->mutable_shard_meta(), NULL);
    } else {
        s = _store->get(vbid, NULL, NULL);
        // fill required fields
        auto* meta = response->mutable_shard_meta();
        meta->set_blob_len(0);
        meta->set_blob_crc(0);
        meta->set_shard_len(0);
    }

    ctx->add_point("read=");

    if (!s.ok()) {
        status->set_code(s.code());
        status->set_msg(s.msg());
        response->clear_shard_meta();

        if (s.code() == AIE_CHECKSUM) {
            found_and_report_bad_vlet(ctx->log_id);
        }

        if (s.code() == AIE_IO_ERROR) {
            return -2;
        }
        return -1;
    }

    if (!request->need_meta()) {
        response->mutable_shard_meta()->clear_key();
        response->mutable_shard_meta()->clear_user_meta();
    }
    if (request->need_data()) {
        if (ctx->priority == aries::HIGHEST || ctx->priority == aries::DEGRADE_HIGH) {
            g_datanode_user_out_throughput_counter << response->shard_meta().shard_len();
            g_datanode_user_out_data_size_averager << response->shard_meta().shard_len();
        } else if (ctx->priority == aries::VERY_LOW) {
            g_datanode_check_out_throughput_counter << response->shard_meta().shard_len();
            g_datanode_check_out_data_size_averager << response->shard_meta().shard_len();
        } else {
            g_datanode_repair_out_throughput_counter << response->shard_meta().shard_len();
            g_datanode_repair_out_data_size_averager << response->shard_meta().shard_len();
        }
    }
    status->set_code(AIE_OK);
    return 0;
}

int ZoneVlet::remove(RemoveContext* ctx) {
    std::unique_ptr<RemoveContext> hold_ctx(ctx);
    ctx->add_point("queue=");

    auto request = ctx->request;
    auto response = ctx->response;

    auto status = response->mutable_status();

    uint64_t curr = base::gettimeofday_ms();
    if ((curr - ctx->en_queue_time) >= ctx->timeout_ms) {
        DN_LOG(TRACE) << "wait too long, no need to process, queue_time:" << curr - ctx->en_queue_time
                      << " timeout:" << ctx->timeout_ms << " vid:" << _volume_id;
        status->set_code(AIE_BUSY);
        status->set_msg("disk is busy, try later");
        return -1;
    }

    if (!_disk_agent->is_used()) {
        status->set_code(AIE_NOT_EXIST);
        status->set_msg("disk is offline");
        return -1;
    }
    if (_balance_job_timestamp > 0) {
        status->set_code(AIE_FAIL);
        status->set_msg("vlet is recovering, disable remove");
        return -1;
    }

    // delete cache item first.
    remove_from_cache(ctx->vid, ctx->vbid);

    Status s;
    if (request->has_force() && request->force()) { // force remove shard
        DN_LOG(WARNING) << "force remove vid:" << request->volume_id()
                        << " vbid:" << request->vbid()
                        << " shard_index:" << request->shard_index();
        s = _store->remove(request->vbid(), true);
        if (s.ok()) {
            s = _store->remove(request->vbid(), false);
        }
    } else {
        s = _store->remove(request->vbid(), request->need_mark_delete());
    }
    ctx->add_point("remove=");
    if (!s.ok()) {
        status->set_code(s.code());
        status->set_msg(s.msg());

        if (s.code() == AIE_CHECKSUM) {
            found_and_report_bad_vlet(ctx->log_id);
        }

        if (s.code() == AIE_DISK_FULL) {
            DN_LOG(WARNING) << _name << " remove shard failed due to disk full,"
                            << " path:" << _disk_agent->disk_path();
            set_disk_full();
        }

        if (s.code() == AIE_IO_ERROR) {
            return -2;
        }
        return -1;
    } else {
        status->set_code(AIE_OK);
        return 0;
    }
}

int ZoneVlet::restore(RestoreContext* ctx) {
    std::unique_ptr<RestoreContext> hold_ctx(ctx);
    ctx->add_point("queue=");

    auto request = ctx->request;
    auto response = ctx->response;

    auto status = response->mutable_status();

    uint64_t curr = base::gettimeofday_ms();
    if ((curr - ctx->en_queue_time) >= ctx->timeout_ms) {
        LOG(TRACE) << "wait too long,no need to process, queue_time:" << curr - ctx->en_queue_time
            << " timeout:" << ctx->timeout_ms << " vid:" << _volume_id;
        status->set_code(AIE_BUSY);
        status->set_msg("disk is busy, try later");
        return -1;
    }

    if (!_disk_agent->is_used()) {
        status->set_code(AIE_NOT_EXIST);
        status->set_msg("disk is offline");
        return -1;
    }
    if (_balance_job_timestamp > 0) {
        status->set_code(AIE_FAIL);
        status->set_msg("vlet is recovering, disable restore");
        return -1;
    }

    Status s = _store->restore(request->vbid());
    ctx->add_point("restore=");
    if (!s.ok()) {
        status->set_code(s.code());
        status->set_msg(s.msg());
        if (s.code() == AIE_DISK_FULL) {
            LOG(WARNING) << _name << " restore shard failed due to disk full,"
                         << " path:" << _disk_agent->disk_path();
            set_disk_full();
        }
        if (s.code() == AIE_IO_ERROR) {
            return -2;
        }
        return -1;
    }
    status->set_code(AIE_OK);
    return 0;
}

int ZoneVlet::update(UpdateVletContext* ctx) {
    std::unique_ptr<UpdateVletContext> hold_ctx(ctx);

    auto request = ctx->request;
    auto response = ctx->response;
    auto status = response->mutable_status();

    if (!_disk_agent->is_used()) {
        status->set_code(AIE_NOT_EXIST);
        status->set_msg("disk is offline");
        LOG(WARNING) << "update membership failed due to disk is offline";
        return -1;
    }
    if (is_disk_full()) {
        status->set_code(AIE_DISK_FULL);
        status->set_msg(std::to_string(_volume_id) + " disk full");
        LOG(WARNING) << "update membership failed due to vlet disk full";
        return -1;
    }

    //TODO: support more store param update;
    bool need_update_meta = false;
    aries::pb::GenericVletInfo new_vlet_info;
    {
        common::ScopedMutexLock lock(_meta_lock);
        auto* membership = _vlet_info.mutable_membership();
        if (membership->volume_version() < request->membership().volume_version()) {
            need_update_meta = true;
            membership->CopyFrom(request->membership());
        }
        if (VletIdentify::has_diff_shard_compress_option(_vlet_info.shard_compress_option(), ctx->shard_compress_option)) {
            need_update_meta = true;
            auto shard_compress_option = _vlet_info.mutable_shard_compress_option();
            shard_compress_option->CopyFrom(ctx->shard_compress_option);
        }
        _volume_version = _vlet_info.membership().volume_version();
        new_vlet_info.CopyFrom(_vlet_info);
    }
    if (!need_update_meta) {
        status->set_code(AIE_OK);
        status->set_msg("Membership.volume_version is smaller than current version");
        return 0;
    }

    LOG(TRACE) << "zone vlet update " << common::pb2json(new_vlet_info);
    auto s = _store->update_meta(new_vlet_info);
    if (!s.ok()) {
        status->set_code(s.code());
        status->set_msg("dump vlet_info to db failed");
        if (s.code() == AIE_DISK_FULL) {
            LOG(WARNING) << _name << " update membership failed due to disk full, path:"
                         << _disk_agent->disk_path();
            set_disk_full();
        } else {
            LOG(WARNING) << _name << "update membership failed due to update meta failed with status:" << s;
        }
        if (s.code() == AIE_IO_ERROR) {
            return -2;
        }
        return -1;
    }
    status->set_code(AIE_OK);
    LOG(NOTICE) << _name << " update membership succeeded, vlet:" << vlet_identify();
    return 0;
}

int ZoneVlet::drop(DropVletContext* ctx) {
    std::unique_ptr<DropVletContext> hold_ctx(ctx);
    auto status = ctx->response->mutable_status();
    status->set_code(AIE_OK);
    status->set_msg("drop_vlet succeeded");

    if (!_disk_agent->is_used()) {
        return -1;
    }

    _drop_myself = true;
    _create_time = 0;
    LOG(NOTICE) << _name << "drop_vlet finished";
    return 0;
}

int ZoneVlet::fill_vlet_info(aries::pb::DatanodeVletInfo* info) {
    info->set_volume_id(_volume_id);
    info->set_shard_index(_shard_index);
    info->set_vlet_version(_vlet_version);
    info->set_create_time(_create_time);
    info->set_vlet_type(_vlet_type);

    info->set_max_vbid(_store->max_vbid());
    info->set_free_size(_store->free_size());
    info->set_total_size(_store->total_size());
    info->set_do_balance(_balance_job_timestamp > 0);
    info->set_blob_num(_store->record_num());
    info->set_existent_blob_num(_store->record_num());
    info->set_mark_deleted_blob_num(_store->mark_deleted_record_num());

    {
        common::ScopedMutexLock lock(_meta_lock);
        if (_vlet_info.has_space_info()) {
            info->mutable_space_info()->CopyFrom(_vlet_info.space_info());
        }
        info->set_volume_version(_volume_version);
        info->set_use_standard_record_layout(_vlet_info.use_standard_record_layout());
        info->mutable_shard_compress_option()->CopyFrom(_vlet_info.shard_compress_option());
    }

    // set disk id
    if (_disk_agent->is_used()) {
        info->set_disk_id(_disk_agent->disk_id());
    } else {
        LOG(NOTICE) << "disk not found,can't get vlet info about disk_id, vid:" << _volume_id
                    << " shard_index:" << _shard_index;
    }
    return 0;
}

VletIdentify ZoneVlet::vlet_identify() {
    VletIdentify id;
    id.volume_id = _volume_id;
    id.shard_index = _shard_index;
    id.vlet_version = _vlet_version;
    id.create_time = _create_time;
    id.volume_version = _volume_version;
    id.use_standard_record_layout = true;
    id.shard_compress_option = _vlet_info.shard_compress_option();
    return id;
}

//TODO: to support later.
int ZoneVlet::batch_get(BatchGetContext* ctx) {
    return -1;
}

int ZoneVlet::do_batch_get(GetSegmentContext* ctx) {
    return -1;
}

int ZoneVlet::copy_vlet(CopyVletWorker* copy_vlet_work) {
    return aries::datanode::copy_vlet(copy_vlet_work, _store);
}

int ZoneVlet::copy_vlet_for_balance(CopyVletContext* ctx) {
    auto request = (::aries::pb::CopyZoneVletRequest*)ctx->request;
    auto response = (::aries::pb::CopyZoneVletResponse*)ctx->response;
    auto status = response->mutable_status();
    uint64_t balance_job_timestamp = request->balance_job_timestamp();
    if (_balance_job_timestamp && balance_job_timestamp != _balance_job_timestamp) {
        status->set_code(AIE_INVALID);
        status->set_msg("duplicated copy vlet job, wait copy timeout and retry again");
        return -1;
    }
    if (_balance_job_timestamp == 0) {
        auto ret = list_blobs(&_vbids);
        if (ret != 0) {
            status->set_code(AIE_FAIL);
            status->set_msg("list blobs failed");
            return -1;
        }
    }
    _last_balance_timestamp = base::gettimeofday_us();
    _balance_job_timestamp = balance_job_timestamp;

    return batch_get_record(ctx, _vbids);
}

int ZoneVlet::copy_vlet_for_check(CopyVletContext* ctx) {
    uint64_t now = base::gettimeofday_s();
    auto* request = (::aries::pb::CopyZoneVletRequest*)ctx->request;
    auto* response = (::aries::pb::CopyZoneVletResponse*)ctx->response;
    auto status = response->mutable_status();

    if (request->progress_offset() == 0) {
        // list blobs at first time in check
        auto ret = list_blobs(&_check_vlet_vbids);
        if (ret != AIE_OK) {
            status->set_code(AIE_FAIL);
            status->set_msg("list blobs failed");
            return -1;
        }
        DN_LOG(NOTICE) << "list blobs for check vid:" << _volume_id;
    }
    return batch_get_record(ctx, _check_vlet_vbids);
}

int ZoneVlet::on_copy_vlet(CopyVletContext* ctx) {
    std::unique_ptr<CopyVletContext> hold_ctx(ctx);
    auto request = (::aries::pb::CopyZoneVletRequest*)ctx->request;
    auto response = (::aries::pb::CopyZoneVletResponse*)ctx->response;
    auto status = response->mutable_status();
    uint64_t curr = base::gettimeofday_ms();
    if ((curr - ctx->en_queue_time) >= ctx->timeout_ms) {
        DN_LOG(TRACE) << "wait too long,no need to process, queue_time:" << curr - ctx->en_queue_time
                      << " timeout:" << ctx->timeout_ms << " vid:" << _volume_id;
        status->set_code(AIE_BUSY);
        status->set_msg("timeout");
        return -1;
    }

    if (request->application() == aries::COPY_VLET_FOR_CHECK) {
        return copy_vlet_for_check(ctx);
    }

    if (request->application() == aries::COPY_VLET_FOR_BALANCE) {
        return copy_vlet_for_balance(ctx);
    }

    DN_LOG(WARNING) << "log_id : " << ctx->cntl->log_id()
                    << " invalid copy application:" << request->application();
    status->set_code(AIE_FAIL);
    status->set_msg("invalid copy application");

    return -1;
}

int ZoneVlet::list_blobs(std::vector<uint64_t>* vbid_list) {
    std::vector<uint64_t> vbids;
    auto status = _store->list_blobs(&vbids);

    if (!status.ok()) {
        LOG(WARNING) << "list blobs on zone vlet failed,"
                     << "vid:" << _volume_id << " status:" << status;
        return -1;
    }
    std::sort(vbids.begin(), vbids.end());
 
    common::ScopedMutexLock lock_guard(_balance_mutex);
    std::swap(*vbid_list, vbids);
    LOG(TRACE) << "list blobs finshed size:" << vbid_list->size() << " vid:" << _volume_id;
    return 0;    
}

int ZoneVlet::batch_get_record(CopyVletContext* ctx, const std::vector<uint64_t>& vbid_list) {
    auto request = (::aries::pb::CopyZoneVletRequest*)ctx->request;
    auto response = (::aries::pb::CopyZoneVletResponse*)ctx->response;
    auto status = response->mutable_status();

    uint64_t last_offset = request->progress_offset();
    uint64_t last_vbid = request->progress_vbid();
    uint64_t offset = 0;
    if (last_offset != 0) {
        if (last_offset >= vbid_list.size() || vbid_list[last_offset] != last_vbid) {
            status->set_code(AIE_FAIL);
            status->set_msg("invalid offset, maybe vbids is changed");
            DN_LOG(WARNING) << "invalid offset, last_offset:" << last_offset
                            << " blob_size:" << vbid_list.size()
                            << " last_vbid should:" << last_vbid;
            return -1;
        }
        if (last_offset == vbid_list.size() - 1) {
            status->set_code(AIE_OK);
            status->set_msg("copy vlet finish");
            DN_LOG(TRACE) << " copy vlet finished, vid:" << _volume_id;
            return 0;
        }
    }

    common::ScopedMutexLock lock_guard(_balance_mutex);
    auto& io_buf = ctx->cntl->response_attachment();
    for (uint64_t i = 0; i < FLAGS_balance_batch_get_record_num; i++) {
        if (last_offset + i >= vbid_list.size()) {
            break;
        }
        uint64_t vbid = vbid_list[last_offset + i];
        pb::ShardMeta meta;
        base::IOBuf data;
        uint32_t mark_deleted_timestamp = 0;
        auto s = _store->get(vbid, &meta, &data, &mark_deleted_timestamp, true);
        if (!s.ok() && s.code() != AIE_MARK_REMOVED) {
            if (s.code() == AIE_BLOB_NOT_EXIST) {
                continue;
            }
            status->set_code(s.code());
            status->set_msg(s.msg());
            response->clear_meta_list();

            if (s.code() == AIE_CHECKSUM) {
                found_and_report_bad_vlet(ctx->log_id);
            }

            if (s.code() == AIE_IO_ERROR) {
                return -2;
            }
            return -1;
        }
        auto record_meta = response->add_meta_list();
        record_meta->mutable_shard_meta()->CopyFrom(meta);
        record_meta->set_vbid(vbid);
        if (mark_deleted_timestamp > 0) {
            record_meta->set_mark_deleted_timestamp(mark_deleted_timestamp);
        }
        io_buf.append(data); 
    }

    offset = std::min((uint64_t)last_offset + FLAGS_balance_batch_get_record_num, (uint64_t)vbid_list.size());
    if (offset >= vbid_list.size()) {
        status->set_code(AIE_OK);
        status->set_msg("copy vlet finish");
        DN_LOG(TRACE) << " copy vlet finished, vid:" << _volume_id
                      << " offset:" << offset  << " vbids size:" << vbid_list.size();
    } else {
        response->set_progress_offset(offset);
        response->set_progress_vbid(vbid_list[offset]);
        status->set_code(AIE_CONTINUE);
        status->set_msg("need continue");
        DN_LOG(TRACE) << " copy vlet continue, vid:" << _volume_id
                      << " remain:" << (vbid_list.size() - offset)
                      << base::string_printf(" src copy offset range[%ld, %ld], start_vbid:%ld, end_vbid:%ld",
                                   last_offset, offset - 1, vbid_list[last_offset], vbid_list[offset - 1]);
    }
    uint32_t total_size = ctx->cntl->response_attachment().size();
    if (request->application() == aries::COPY_VLET_FOR_BALANCE) {
        g_datanode_copy_out_throughput_counter << total_size;
        g_datanode_copy_out_data_size_averager << total_size;
    }
    if (request->application() == aries::COPY_VLET_FOR_CHECK) {
        g_datanode_check_out_throughput_counter << total_size;
        g_datanode_check_out_data_size_averager << total_size;
    }
    
    return 0;
}

int ZoneVlet::get_record_index_info(GetRecordIndexInfoContext* ctx) {
    std::unique_ptr<GetRecordIndexInfoContext> hold_ctx(ctx);
    ctx->add_point("queue=");

    auto request = ctx->request;
    auto response = ctx->response;
    auto status = response->mutable_status();
    auto vbid = request->vbid();
    auto log_id = ctx->log_id;

    uint64_t curr = base::gettimeofday_ms();
    if ((curr - ctx->en_queue_time) >= ctx->timeout_ms) {
        ARIES_RPC_LOG(TRACE) << _name << " get record index info request wait too long,"
                << " need not to process, just discard it," << " vbid:" << vbid
                << " queue_time:" << curr - ctx->en_queue_time << " timeout:" << ctx->timeout_ms;
        status->set_code(AIE_BUSY);
        status->set_msg("disk is busy");
        return -1;
    }

    if (!_disk_agent->is_used()) {
        status->set_code(AIE_NOT_EXIST);
        status->set_msg("disk is offline");
        return -1;
    }

    Status s = _store->get_record_index_info(vbid, response->mutable_record_index_info());
    ctx->add_point("read_index_info=");
    if (!s.ok()) {
        status->set_code(s.code());
        status->set_msg(s.msg());
        if (s.code() != AIE_MARK_REMOVED) {
            response->clear_record_index_info();
        }
        ARIES_RPC_LOG(WARNING) << _name << " get record index info failed, error:" << s;
        if (s.code() == AIE_IO_ERROR) {
            return -2;
        }
        return -1;
    }

    status->set_code(AIE_OK);
    status->set_msg("get record index info succ");
    return 0;
}

int ZoneVlet::check_vlet(ShardCheckContext* ctx) {
    auto log_id = ctx->request->log_id();
    uint64_t balance_time = base::gettimeofday_us();
    uint64_t progress_offset = 0;
    uint64_t progress_vbid = 0;
    base::EndPoint control_addr(base::IP_ANY, common::FLAGS_port);
    base::EndPoint data_addr = common::get_data_service_addr(control_addr);
    baidu::rpc::Channel peer_channel;
    if (peer_channel.Init(data_addr, nullptr) != 0) {
        LOG(FATAL) << " init channel failed, peer:" << data_addr;
        return -1;
    }
    aries::pb::DataNodeDataService_Stub peer(&peer_channel);
    std::vector<aries::pb::CopyVletRecordMeta> record_meta_list;
    bool is_finish = false;

    //read last check result from request
    std::map<uint64_t, aries::pb::ShardCheckedInfo*> check_result_map;
    if (_shard_index != 0) {
        for (int i = 0; i < ctx->request->check_shard_results_size(); i++) {
            auto shard_result = ctx->report->add_check_shard_results();
            shard_result->CopyFrom(ctx->request->check_shard_results(i));
            shard_result->set_checked(false);
            check_result_map[shard_result->vbid()] = shard_result;
        }
    }

    //get shards
    while (!(*ctx->interrupt)) {
        uint64_t curr = base::gettimeofday_ms();
        if ((curr - ctx->en_queue_time) >= ctx->timeout_ms) {
            ARIES_RPC_LOG(WARNING) << "wait too long, no need to process, queue_time_ms:" << curr - ctx->en_queue_time
                << " timeout_ms:" << ctx->timeout_ms << " vid:" << _volume_id;
            return -1;
        }

        //speed limit
        ctx->block_read_token_pool->take(1);
        pb::CopyZoneVletRequest request;
        pb::CopyZoneVletResponse response;
        base::IOBuf iobuf;
        request.set_token(FLAGS_token);
        request.set_volume_id(_volume_id);
        request.set_shard_index(_shard_index);
        request.set_balance_job_timestamp(balance_time);
        request.set_priority(aries::MIDDLE);
        request.set_timeout_ms(FLAGS_call_timeout_ms);
        request.set_application(aries::COPY_VLET_FOR_CHECK);
        request.set_progress_vbid(progress_vbid);
        request.set_progress_offset(progress_offset);
        int tries = FLAGS_balance_retry_times;
        for (; tries > 0; --tries) {
            baidu::rpc::Controller cntl;
            cntl.set_timeout_ms(FLAGS_call_timeout_ms);
            cntl.set_log_id(log_id);
            peer.copy_zone_vlet(&cntl, &request, &response, nullptr);
            if (cntl.Failed()) {
                ARIES_RPC_LOG(WARNING) << " vid:" << _volume_id
                    << " copy_vlet rpc for check failed, remote:"
                    << cntl.remote_side() << " error:" << cntl.ErrorText();
                continue;
            } else if (response.status().code() == AIE_OK) {
                ARIES_RPC_LOG(NOTICE) << " vid:" << _volume_id << " copy_vlet for check finished";
                is_finish = true;
            } else if (response.status().code() != AIE_CONTINUE) {
                ARIES_RPC_LOG(WARNING) << " vid:" << _volume_id << " copy_vlet for check failed, remote:"
                    << cntl.remote_side() << " status:" << common::pb2json(response.status());
                continue;
            }
            record_meta_list.clear();
            for (int i = 0; i < response.meta_list_size(); i++) {
                record_meta_list.push_back(response.meta_list(i));
            }
            cntl.response_attachment().swap(iobuf);
            progress_offset = response.progress_offset();
            progress_vbid = response.progress_vbid();
            break;
        }
        if (tries <= 0) {
            LOG(WARNING) << "in copy vlet, retry time exhausted.";
            return -1;
        }

        if(check_shard(record_meta_list, &iobuf, &check_result_map, ctx) < 0) {
            return -1;
        }
        if (is_finish) {
            ARIES_RPC_LOG(NOTICE) << _name << " check shard finished";
            if (FLAGS_enable_index_check) {
                diff_vbids_with_db(log_id, check_result_map);
            }
            // if vbid not found this datanode but found before sign lost
            for (auto it : check_result_map) {
                if (it.second->mark_deleted()) {
                    continue;
                }
                if (!it.second->checked()) {
                    auto lost_sum = it.second->lost_shard_num();
                    it.second->set_lost_shard_num(lost_sum + 1);
                }
            }
            return 0;
        }
    }
    LOG(WARNING) << _name << " check_vlet thread interrupt, begin to stop";
    return -1;
}

bool ZoneVlet::check_self(CheckVletEngineContext* ctx) {
    std::unique_ptr<CheckVletEngineContext> guard(ctx);
  
    if (!_disk_agent->is_used()) {
        LOG(WARNING) << "log_id:" << ctx->cntl->log_id()
            << " disk is offline, don't check vlet engine.";
        ctx->response->mutable_status()->set_code(AIE_NOT_EXIST);	
        return false;
    }

    auto s = _store->check_self();
    if (s.ok()) {
        ctx->response->mutable_status()->set_code(AIE_OK);
        return true;
    }
    if (s.code() == AIE_CORRUPT) {
        found_and_report_bad_vlet(ctx->cntl->log_id());
    }
    ctx->response->mutable_status()->set_code(AIE_FAIL);
    return false;    
}

#if defined(_CHECK_TEST) || defined(_UNIT_TEST)
bool ZoneVlet::change_shard(ChangeShardContext* ctx) {
    std::unique_ptr<ChangeShardContext> hold_ctx(ctx);
    if (!_disk_agent->is_used()) {
        LOG(WARNING) << "log_id : " << ctx->cntl->log_id()
            << " disk if offline, don't check vlet engine.";
        ctx->res->mutable_status()->set_code(AIE_NOT_EXIST);
        return false;
    }
    auto s = _store->change_shard(ctx->vbid, ctx->op_code);
    if (s.ok()) {
        ctx->res->mutable_status()->set_code(AIE_OK);
        return true;
    }
    ctx->res->mutable_status()->set_code(AIE_FAIL);
    return false;
}
#endif    

UnitIoStats ZoneVlet::get_io_stats() {
    UnitIoStats io_stat;
    return io_stat;
}

uint64_t ZoneVlet::get_fingerprint_by_slot(uint32_t slot_id) {
    return _store->get_fingerprint_by_slot(slot_id);
}

int ZoneVlet::get_for_check(uint64_t vbid) {
    aries::pb::ShardMeta meta;
    base::IOBuf data;
    Status s = _store->get(vbid, &meta, &data);
    return s.code();    
}




}
}
