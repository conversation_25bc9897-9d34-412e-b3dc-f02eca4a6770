/*************************************************************************
 *
 * Copyright (c) Baidu.com, Inc. All Rights Reserved
 *
 ************************************************************************/

/**
 * @file universal_fs/ext_fs.cpp
 * <AUTHOR>
 * @date Tue 07 May 2019 03:48:47 PM CST
 * @brief 
 *
 **/

#include "baidu/inf/aries/common/aries_ut_pread.h"
#include "baidu/inf/aries/datanode/universal_fs/file_open_limit.h"

#include "baidu/inf/aries/datanode/universal_fs/ext_fs.h"
#include "base/file_util.h"
#include <folly/ScopeGuard.h>

#ifndef FALLOC_FL_CONVERT_AND_EXTEND
#define FALLOC_FL_CONVERT_AND_EXTEND 0x100
#endif

namespace aries {
namespace universalfs {
class ExtFileControl : public FileOpenLimitController::FileControl {
public:
    ExtFileControl(ExtFileDescriptor* fd, const std::string& fname, int flag)
            : _fd(fd), _fname(fname), _flag(flag) {}

    bool open() override {
        common::ScopedMutexLock lock(_open_close_mutex);
        if (_fd->_fd < 0) {
            int fd = ::open(_fname.c_str(), _flag);
            if (fd >= 0) { // open succ
                _fd->_fd = fd;
            } else {
                LOG(WARNING) << "open file failed, path:" << _fname << " ret:" << fd
                        << " errno:(" << errno << ") " << strerror(errno);
            }
        }
        return _fd->_fd >= 0;
    }

    bool close() override {
        common::ScopedMutexLock lock(_open_close_mutex);
        if (_fd->_fd >= 0) {
            int err = ::close(_fd->_fd);
            if (err == 0) { // close success
                // set fd = -1 when file is closed
                _fd->_fd = -1;
            } else {
                LOG(WARNING) << "close file failed, path:" << _fname << " fd:" << _fd->_fd
                        << " ret:" << err << " errno:(" << errno << ") " << strerror(errno);
            }
        }
        return _fd->_fd < 0;
    }

    void describe(std::ostream& os) const override {
        os << "ExtFile(fname:" << _fname << " fd:" << _fd->_fd << " flag:" << _flag << ")";
    }

private:
    ExtFileDescriptor* const _fd;
    const std::string _fname;
    const int _flag;
    common::MutexLock _open_close_mutex;
};

/***** ExtFileDescriptor ****/

// assure file descriptor is usable
#define ASSURE_FD_IS_USABLE(do_close) \
    FileOpenLimitController* __controller = _env->file_open_limit_controller();\
    if (_open_limit_handle.valid()) {\
        assert(__controller != nullptr);\
        if (!__controller->ref(_open_limit_handle)) {\
            ARIES_FUNC_LOG(FATAL) << "failed due to FileOpenLimit, fd:" << _fd;\
            return Status(AIE_FAIL, "FileOpenLimit");\
        }\
    }\
    SCOPE_EXIT {\
        if (_open_limit_handle.valid()) {\
            __controller->unref(_open_limit_handle, do_close);\
        }\
    }

Status ExtFileDescriptor::pread(void* buf, uint64_t len, uint64_t offset) {
    ASSURE_FD_IS_USABLE(false);

    Status s;
    ssize_t bytes_read = 0;
#ifdef _UNIT_TEST
    bytes_read = ::aries_ut_pread(_fd, buf, len, offset);
#else
    bytes_read = ::pread(_fd, buf, len, offset);
#endif
    if ((unsigned)bytes_read == len) {
        return s;
    }
    LOG(WARNING) << "read error, fd:" << _fd << " buf:" << buf << " offset:" << offset
        << " size:" << len << " read:" << bytes_read << " errno:(" << errno << ')'
        << strerror(errno);
    return Status::by_errno(strerror(errno));
}

Status ExtFileDescriptor::pwrite(const void* buf, uint64_t len, uint64_t offset) {
    ASSURE_FD_IS_USABLE(false);

    Status s;
    ssize_t bytes_write = 0;
    bytes_write = ::pwrite(_fd, buf, len, offset);
    if ((unsigned)bytes_write != len) {
        return Status::by_errno(strerror(errno));
    }
    if (_use_buffer && _use_sync_file_range) {
        auto ret = ::sync_file_range(_fd, offset, len, sync_file_range_flag);
        if (ret != 0) {
            LOG(WARNING) << "sync error, fd:" << to_string() << " offset:" << offset
                << " size:" << len << " errno:(" << errno << ")";
            return Status::by_errno(strerror(errno));
        }
    }
    if ((unsigned)bytes_write == len) {
        return s;
    }
    LOG(WARNING) << "write error, fd:" << to_string() << " buf:" << buf << " offset:" << offset
        << " size:" << len << " write:" << bytes_write << " errno:(" << errno << ')'
        << strerror(errno);
    return Status::by_errno(strerror(errno));
}

Status ExtFileDescriptor::get_size(uint64_t* size) {
    ASSURE_FD_IS_USABLE(false);

    struct stat file_stat;
    int ret = fstat(_fd, &file_stat);

    if (ret != 0) {
        return Status::by_errno(strerror(errno));
    }

    *size = file_stat.st_size;
    return Status();
}

Status ExtFileDescriptor::fsync() {
    ASSURE_FD_IS_USABLE(true);

    if (::fsync(_fd) < 0) {
        return Status(AIE_FAIL);
    }
    return Status();
}

/***** ExtIOEnv ****/
Status ExtIOEnv::open(const std::string& fname, int flag, mode_t mode,
        const uint64_t size, FileDescriptor** file) {
    *file = nullptr;
#ifdef _UNIT_TEST
    int fd = ::aries_ut_open(fname.c_str(), flag, mode);
#else
    int fd = ::open(fname.c_str(), flag, mode);
#endif
    if (fd < 0) {
        LOG(WARNING) << "open file failed, file:" << fname
            << " errno:(" << errno << ")" << strerror(errno);
        return Status::by_errno(strerror(errno));
    }

    if (O_CREAT & flag) {
        auto ret = ::fallocate(fd, FALLOC_FL_CONVERT_AND_EXTEND, 0, size);
        if (ret != 0) {
            if (errno == EOPNOTSUPP) {
                LOG(NOTICE) << "fallocate failed due to unsupported mode:FALLOC_FL_CONVERT_AND_EXTEND"
                    << ", try without this mode";
                errno = 0;
                ret = ::fallocate(fd, 0, 0, size);
            }
            if (ret != 0) {
                LOG(FATAL) << "open file failed due to fallocate failed,"
                           << " file:" << fname << " fd:" << fd << " size:" << size 
                           << " errno:(" << errno << ')' << strerror(errno);
                ::unlink(fname.c_str());
                return Status::by_errno(strerror(errno));
            }
        }
    }
    std::unique_ptr<ExtFileDescriptor> efd(new ExtFileDescriptor(shared_from_this(), fd, flag));

    int control_flag = flag;
    if (flag & O_EXCL) {
        // The flag passed to ExtFileControl should not has O_EXCL, or subsequent open() syscall will fail.
        control_flag ^= O_EXCL;
    }

    auto control = std::make_unique<ExtFileControl>(efd.get(), fname, control_flag);
    if (auto* c = file_open_limit_controller(); c != nullptr) {
        efd->set_open_limit_handle(c->register_file(std::move(control), true));
    }
    *file = efd.release();
    return Status();
}

Status ExtIOEnv::init(const std::string& meta_path, const std::string& dev_path, int mode) {
    Status s;
    base::FilePath p(meta_path);
    if (!base::PathExists(p)) {
        base::File::Error err;
        if (!base::CreateDirectoryAndGetError(p, &err, true)) {
            LOG(WARNING) << "create directory failed with error:" << err; 
            return Status(AIE_FAIL, "create directory failed");
        }
    }

    if (aries::datanode::FLAGS_open_file_soft_limit != 0) {
        _file_open_limit_controller = FileOpenLimitController::get();
    }

    return s;
}

Status ExtIOEnv::get_disk_capacity(const std::string& path, uint64_t* disk_size, uint64_t* free_size) {
    Status s;
    struct statvfs buf;
    if (statvfs(path.c_str(), &buf)) {
        LOG(WARNING) << "get disk state failed, disk_path:" << path;
        return Status::by_errno(strerror(errno));
    }
    *disk_size = buf.f_bsize * buf.f_blocks;
    *free_size = buf.f_bsize * buf.f_bavail;
    return s;
}

Status ExtIOEnv::unlink(const std::string& path) {
    auto tmp_name = base::FilePath(path);
    if (!base::PathExists(tmp_name)) {
        return Status(AIE_NOT_EXIST, "path not exist");
    }

    bool succ = base::DeleteFile(tmp_name, true);
    if (!succ) {
        return Status::by_errno(strerror(errno));
    }
    return Status();
}

}
}
