/*************************************************************************
 *
 * Copyright (c) Baidu.com, Inc. All Rights Reserved
 *
 ************************************************************************/

/**
 * @file smr_fs.cpp
 * <AUTHOR>
 * @date Tue 23 Apr 2019 11:15:30 AM CST
 * @brief
 *
 **/
#include "baidu/inf/aries/common/aries_ut_pread.h"

#include "baidu/inf/aries/datanode/universal_fs/smr_fs.h"
#include "baidu/inf/aries/datanode/util/util.h"
#include "base/file_util.h"
#include "base/files/file_enumerator.h"
#include "base/fd_guard.h"
#include "json_to_pb.h"
#include "pb_to_json.h"
#include <memory>
#include <string>
#include <fstream>
#include <streambuf>

namespace aries {
namespace universalfs {

/*****    ZoneManager  **********/
ZoneManager::ZoneManager(int32_t base_id, int32_t zone_num) {
    _smr_zone_base_id = base_id;
    _zone_map.init(zone_num);
    _total_zone_num = zone_num;
}

int32_t ZoneManager::init(int32_t base_id, int32_t zone_num) {
    _smr_zone_base_id = base_id;
    _zone_map.init(zone_num);
    _total_zone_num = zone_num;
    return 0;
}

int32_t ZoneManager::free_zone_num() {
    std::lock_guard<std::mutex> lock(this->_mutex);
    return _zone_map.free_num();
}

int ZoneManager::get_disk_capacity(uint64_t* disk_size, uint64_t* free_size) {
    std::lock_guard<std::mutex> lock(_mutex);
    *disk_size = _total_zone_num;
    *free_size = _zone_map.free_num();
    return 0;
}

int32_t ZoneManager::allocate_zone() {
    std::lock_guard<std::mutex> lock(_mutex);
    if (_zone_map.free_num() <= 0) {
        return 0;
    }
    return _zone_map.allocate_pos() + _smr_zone_base_id - 1;
}

bool ZoneManager::set_used(int32_t zone_id) {
    std::lock_guard<std::mutex> lock(_mutex);
    if (!check_zone_id(zone_id)) {
        return false;
    }
    _zone_map.set_used(zone_id - _smr_zone_base_id + 1);
    return true;
}

bool ZoneManager::free_zone(int32_t zone_id) {
    std::lock_guard<std::mutex> lock(_mutex);
    if (!check_zone_id(zone_id)) {
        return false;
    }
    _zone_map.free_pos(zone_id - _smr_zone_base_id + 1); 
    return true;
}

bool ZoneManager::check_zone_id(int32_t zone_id) {
    if (zone_id < _smr_zone_base_id || zone_id >= _smr_zone_base_id + _total_zone_num) {
        LOG(WARNING) << "invalid zone id:" << zone_id; 
        return false;
    }
    return true;
}

/*****    SmrIOEnv  **********/

SmrIOEnv::SmrIOEnv() : IOEnv(), _buffer_fd(-1),
    _dio_fd(-1), _zone_size(0), _base_zone_id(0), _zone_num(0) {
}

// mode == 0, register a empty disk
// mode == 1, format the smr disk
// mode == 2, load a not empty disk
Status SmrIOEnv::init(const std::string& meta_path, const std::string& dev_path, int mode) {
    Status s;
    int ret = 0;

    _meta_path = meta_path;
    if (dev_path.empty()) {
        _dev_path = get_mount_dev(meta_path);
        LOG(NOTICE) << "meta_path:" << meta_path << " mounted device info:" << _dev_path;
    } else {
        _dev_path = dev_path;
    }

    base::FilePath p(meta_path);
    if (!base::PathExists(p)) {
        base::File::Error err;
        if (!base::CreateDirectoryAndGetError(p, &err, true)) {
            LOG(WARNING) << "create directory failed with error:" << err
                << " meta_path:" << meta_path
                << " dev_path:" << _dev_path
                << " mode:" << mode;
        }
    }

    // lock
    s = check_and_lock();
    if (s.code() != AIE_OK) {
        return s;
    }

    // check device info
    s = check_device_info(mode);
    if (s.code() != AIE_OK) {
        return s;
    }

    // init zone info
    s = init_zone_info(_dev_path);
    if (s.code() != AIE_OK) {
        return s;
    }

    // format the smr disk
    if (mode == 1) {
        // rm meta
        base::FilePath to_remove(meta_path);
        base::DeleteFile(to_remove, true); 

        // reset all write pointer of smr zone
        ret = _util.reset_all_write_ptrs();
        if (ret != 0) {
            LOG(FATAL) << "reset all zone wp failed."
                << " meta_path:" << meta_path
                << " dev_path:" << _dev_path
                << " mode:" << mode;
            s = Status(AIE_FAIL, "reset failed");
        }
        return s;
    }

    // check all zone write pointer
    std::string smr_lock_file = _meta_path + "smr.lock";
    std::string smr_dev_info_file = _meta_path + "smr_dev.info";
    std::string err_msg;
    std::map<int32_t, bool> used_zone_ids;

    base::FilePath dir(meta_path);
    base::FileEnumerator f_enum(dir, true, base::FileEnumerator::FILES);
    for (base::FilePath name = f_enum.Next(); !name.empty(); name = f_enum.Next()) {
        uint64_t vid;
        uint32_t shard_index;

        if (base::DirectoryExists(name)) {
            continue;
        }
        struct stat statbuff;
        if (-1 == stat(name.value().c_str(), &statbuff)) {
            LOG(WARNING) << "file:" << name.value() << " can not get size."; 
            continue;
        }

        if (name.value() == smr_lock_file || name.value() == smr_dev_info_file) {
            //ignore smr lock file and smr dev info file
            continue;
        }

        if (statbuff.st_size > 4096) {
            // maybe not zone meta file
            continue;
        }

        std::string filename = name.BaseName().value();
        std::string str;
        if (!base::ReadFileToString(name, &str)) {
            LOG(WARNING) << "read content of file:" << name.value() << " failed."
                << " err:" << errno << " errmsg:" << strerror(errno);
            continue;
        }
        pb::ZoneFileInfo info;
        if (!JsonToProtoMessage(str, &info, &err_msg)) {
            LOG(TRACE) << "parse zone file:" << name.value() << " fail, error:" << err_msg; 
            continue;
        }
        if (info.zone_magic() != ZONE_META_MAGIC) {
            LOG(WARNING) << "file:" << name.value() << " zone magic is not right."
                << info.zone_magic() << " zone:"
                << info.zone_id()
                << " str:" << str.c_str();
            continue;
        }
        if (info.zone_id() == -1) {
            LOG(WARNING) << "file:" << name.value() << " zone id is -1, should allocate before use."; 
            continue;
        }
        if (info.zone_id() < _base_zone_id || info.zone_id() >= _base_zone_id + _zone_num) {
            LOG(FATAL) << "zone file:" << name.value() << " has invalid zone id."
                << " zone id: " << info.zone_id()
                << " meta_path:" << meta_path
                << " dev_path:" << _dev_path
                << " mode:" << mode;
            return Status(AIE_FAIL, "has invalid zone id");
        }
        if (used_zone_ids.find(info.zone_id()) != used_zone_ids.end()) {
            LOG(FATAL) << "zone file:" << name.value() << " has same zone id with previous open file."
                << " zone id: " << info.zone_id()
                << " meta_path:" << meta_path
                << " dev_path:" << _dev_path
                << " mode:" << mode;
            return Status(AIE_FAIL, "has duplicated zone id");
        }
        _zone_manager.set_used(info.zone_id());
        used_zone_ids[info.zone_id()] = true;
    }

    // check empty zone write pointer
    for (auto i = _base_zone_id; i < _zone_num + _base_zone_id; ++i) {
        if (used_zone_ids.find(i) != used_zone_ids.end()) {
            continue;
        }
        if (!_util.is_reset_write_ptr(i)) {
            // LOG(WARNING) << "zone id:" << i << " is not reset write pointer when not used."; 
            // _util.reset_write_ptr(i);
            _zone_wps[i] = 0;
        }
    }

    // open two fd for buffer io and direct io
    _buffer_fd = ::open(_dev_path.c_str(), O_RDWR);
    if (_buffer_fd < 0) {
        LOG(FATAL) << "open buffer fd failed, code:" << errno << " err:" << strerror(errno)
                << " meta_path:" << meta_path
                << " dev_path:" << _dev_path
                << " mode:" << mode;
        return Status(AIE_FAIL, "fail to open buf fd");
    }
    _dio_fd = ::open(_dev_path.c_str(), O_RDWR|O_DIRECT);
    if (_dio_fd < 0) {
        LOG(FATAL) << "open dio fd failed, code:" << errno << " err:" << strerror(errno)
                << " meta_path:" << meta_path
                << " dev_path:" << _dev_path
                << " mode:" << mode;
        return Status(AIE_FAIL, "fail to open dio fd");
    }
    LOG(WARNING) << "init smr env succeeded, meta_path:" << meta_path
                << " dev_path:" << _dev_path
                << " mode:" << mode
                << " buf_fd:" << _buffer_fd
                << " dio_fd:" << _dio_fd;
    return s;
}

Status SmrIOEnv::check_and_lock() {
    Status s;
    std::string lock_file = std::string(_meta_path + "/smr.lock");
    
    _file_lock.reset(new aries::common::FileLock(lock_file));
    int ret = _file_lock->lock();
    if (ret != 0) {
        LOG(FATAL) << "fail to start smr due to lockf " << lock_file << " failed,"
                << " maybe means multi processs in the same directory!";
        return Status(AIE_FAIL, "fail to lock");
    }
    return s;
}

Status SmrIOEnv::check_device_info(int mode) {
    Status s;
    std::string dev_info_file = std::string(_meta_path + "/smr_dev.info");

    base::FilePath p(dev_info_file);
    if (!base::PathExists(p)) {
        // not exist, so write new info to file
        if (mode == 0) {
            base::fd_guard fd(::open(dev_info_file.c_str(), O_WRONLY | O_CREAT | O_TRUNC, 0644));
            if (fd < 0) {
                LOG(WARNING) << "update dev info file failed due to open failed,"
                    << " path:" << dev_info_file << " errno:" << errno;
                return Status::by_errno(errno, "open dev failed.");
            }

            struct Pb2JsonOptions options;
            options.pretty_json = true;
            options.enum_option = OUTPUT_ENUM_BY_NUMBER;
            std::string json_data;
            std::string err_msg;
            pb::ZoneDeviceInfo info;

            info.set_version(1);
            info.set_meta_path(_meta_path);

            if (!ProtoMessageToJson(info, &json_data, options, &err_msg)) {
                LOG(WARNING) << "fail to parse content of :" << dev_info_file;
                return Status(AIE_FAIL, "");
            }

            int64_t size = json_data.size();
            if (::write(fd, json_data.data(), json_data.size()) != size) {
                LOG(WARNING) << "update disk conf failed due to write failed,"
                    << " path:" << dev_info_file << " errno:" << errno;
                return Status::by_errno(errno, "");
            }
        } else {
            return Status(AIE_FAIL, "dev info file not exist");
        }
    }
     
    std::string json_data;
    if (!base::ReadFileToString(base::FilePath(dev_info_file), &json_data)) {
        LOG(FATAL) << "read content of file:" << dev_info_file << " failed.";
        return Status(AIE_FAIL, "");
    }

    std::string err_msg;
    pb::ZoneDeviceInfo info;
    if (!JsonToProtoMessage(json_data, &info, &err_msg)) {
        LOG(FATAL) << "fail to parse content of file:" << dev_info_file;
        return Status(AIE_FAIL, "");
    }

    if (info.meta_path().compare(_meta_path) != 0) {
        LOG(FATAL) << "device info is not consistent expect path:" << info.meta_path()
                << " actual_meta_path:" << _meta_path;
        return Status(AIE_FAIL, "");
    }

    return s;

}

Status SmrIOEnv::init_zone_info(const std::string& dev_path) {
    Status s;
    // init util
    int ret = _util.init(dev_path);
    if (ret != 0) {
        LOG(FATAL) << "zone utils init failed."
                << " dev_path:" << dev_path;
        return Status(AIE_WRONG_DISK_TYPE, "wrong disk type");
    }

    // init zone_manager
    int32_t base_id = 0;
    int32_t zone_num = 0;
    ret = _util.get_zone_info(&base_id, &zone_num, &_zone_size, &_zone_wps);
    if (ret != 0) {
        LOG(FATAL) << "get zone info failed."
                << " dev_path:" << dev_path;
        return Status(AIE_FAIL, "fail to get zone info");
    }
    _zone_manager.init(base_id, zone_num);
    _base_zone_id = base_id;
    _zone_num = zone_num;
    LOG(NOTICE) << "init_zone_info succeeded on dev_path:" << dev_path
                << " base_zone_id:" << _base_zone_id;
    return s;
}

Status SmrIOEnv::update_file_content(int fd, const pb::ZoneFileInfo& info) {
    Status s;
    if (fd < 0) {
        LOG(WARNING) << "fd:" << fd << " not invalid";
        return Status(AIE_INVALID_PATH, "invalid path");
    }

    struct Pb2JsonOptions options;
    options.pretty_json = true;
    options.enum_option = OUTPUT_ENUM_BY_NUMBER;
    std::string json_data;
    std::string err_msg;

    if (!ProtoMessageToJson(info, &json_data, options, &err_msg)) {
        LOG(WARNING) << "fail to parse content of fd:" << fd;
        return Status(AIE_FAIL, "corrupt content");
    }

    // clear old content
    ftruncate(fd, 0);
    lseek(fd, 0, SEEK_SET);

    int64_t size = json_data.size();

    if (::write(fd, json_data.data(), json_data.size()) != size) {
        LOG(WARNING) << "update file content failed due to write failed,"
            << " fd:" << fd << " errno:" << errno << " msg:" << strerror(errno);
        return Status::by_errno(errno, "update file content");
    }

    return s;
}

Status SmrIOEnv::open(const std::string& fname, int flag, mode_t mode, const uint64_t size, FileDescriptor** file) { 
    Status s;

    *file = nullptr;
    int fd = ::open(fname.c_str(), flag & (~O_DIRECT), mode);
    if (fd < 0) {
        LOG(WARNING) << "open file failed, " << fname << " errno:(" << errno << ")" << strerror(errno);
        return Status::by_errno("fail to open meta file");
    }
    std::unique_ptr<SmrFileDescriptor> sfd(new SmrFileDescriptor(shared_from_this(), fd, flag));
    //when open, init file content
    if (flag & O_CREAT) {
        int32_t zone_id = -1;
        uint64_t zone_size = 0;
        get_zone_size(&zone_size);
        if (size != zone_size) {
            LOG(WARNING) << "open file failed due to invalid argument, file:" << fname
                << ", size:" << size << ", zone_size:" << zone_size;
            // create a file failed, need to delete it
            ::unlink(fname.c_str());
            return Status(AIE_INVALID_ARGUMENT, "invalid argument");
        }
        s = fallocate_zone(fd, &zone_id);
        if (s.code() != AIE_OK) {
            LOG(WARNING) << "open file failed due to fallocate failure, file:" << fname
                << ", fd:" << fd << ", size:" << size << ", error:" << s;
            // create a file failed, need to delete it
            ::unlink(fname.c_str());
            return s;
        }
        sfd->set_zone_id(zone_id);
    } else {
        std::string json_data;
        if (!base::ReadFileToString(base::FilePath(fname), &json_data)) {
            LOG(WARNING) << "read content of file:" << fname << " failed.";
            return Status::by_errno("fail to read meta file");
        }

        std::string err_msg;
        pb::ZoneFileInfo info;
        if (!JsonToProtoMessage(json_data, &info, &err_msg)) {
            LOG(WARNING) << "fail to parse content of file:" << fname;
            return Status::by_errno("fail to parse meta file");
        } 

        if (info.zone_magic() != ZONE_META_MAGIC) {
            LOG(WARNING) << "bad zone magic:" << info.zone_magic() << " of file:" << fname;
            return Status(AIE_FAIL, "bad zone magic");
        }

        if (info.zone_id() != -1 && (info.zone_id() < _base_zone_id
                || info.zone_id() >= _base_zone_id + _zone_num)) {
            LOG(WARNING) << "invalid zone id:" << info.zone_id() << " of file:" << fname;
            return Status(AIE_FAIL, "invalid zone id"); 
        }

        sfd->set_zone_id(info.zone_id());

        if (_zone_wps.find(info.zone_id()) == _zone_wps.end()) {
            LOG(WARNING) << "invalid zone id:" << info.zone_id() << " of file:" << fname
                    << " can not be found in zone wps map";
            return Status(AIE_FAIL, "invalid zone id"); 
        }
        LOG(DEBUG) << "set_zone_id:" << info.zone_id() << " set_zone_off:" <<  _zone_wps[info.zone_id()]; 
        sfd->set_zone_off(_zone_wps[info.zone_id()]);
    }

    sfd->close_fd();
    
    *file = sfd.release();
    return s;
}

Status SmrIOEnv::fallocate_zone(int fd, int32_t* zone_id) {
    Status s;
    *zone_id = _zone_manager.allocate_zone();
    if (*zone_id == 0) {
        LOG(WARNING) << "allocate zone failed due to disk full, fd:" << fd;
        return Status(AIE_DISK_FULL, "disk full");
    }
    assert(*zone_id >= _base_zone_id);
    assert(*zone_id < _base_zone_id + _zone_num);
    
    base::Timer reset_lantency_us;
    reset_lantency_us.start();
    auto ret = _util.reset_write_ptr(*zone_id);
    reset_lantency_us.stop();
    LOG(TRACE) << "allocate zone id:" << *zone_id << " reset_us:" << reset_lantency_us.u_elapsed();
    if (ret != 0) {
        LOG(WARNING) << "fallocate fd:" << fd << " but fail to reset zone id:" << *zone_id;
        _zone_manager.free_zone(*zone_id);
        return Status(AIE_FAIL, "reset fail");
    }
    pb::ZoneFileInfo info;
    info.set_version(1);
    info.set_zone_id(*zone_id);
    info.set_zone_magic(ZONE_META_MAGIC);
    // take care!!! should call fallocate before other operations 
    s = update_file_content(fd, info);
    if (!s.ok()) {
        LOG(WARNING) << "fallocate fd:" << fd << " but fail to update meta:" << *zone_id;
        _zone_manager.free_zone(*zone_id);
    }
    return s;
}

Status SmrIOEnv::get_write_off(int32_t zone_id, uint64_t* offset) {
    Status s;
    if (zone_id == -1) {
        LOG(WARNING) << "not init zone, you should allocate a zone before read/write";
        return Status(AIE_FAIL, "zone id is -1");
    }
    *offset = _util.get_write_ptr(zone_id);
    if (*offset == -1ull) {
        return Status(AIE_FAIL, "fail to get write point");
    }
    return s;
}

Status SmrIOEnv::delete_file(const std::string& fname) {
    Status s;

    std::string json_data;
    if (!base::ReadFileToString(base::FilePath(fname), &json_data)) {
        LOG(FATAL) << "read content of file:" << fname << " failed.";
        return Status::by_errno(errno, "fail to read");
    }
    ::unlink(fname.c_str());

    std::string err_msg;
    pb::ZoneFileInfo info;
    if (!JsonToProtoMessage(json_data, &info, &err_msg)) {
        LOG(FATAL) << "fail to parse content of file:" << fname;
        return Status(AIE_FAIL, "corrupt content");
    }

    LOG(INFO) << "unlink " << fname << " zone_id:" << info.zone_id();
    assert(info.zone_id() >= _base_zone_id);
    assert(info.zone_id() < _base_zone_id + _zone_num);
    auto ok = _zone_manager.free_zone(info.zone_id());
    if (!ok) {
        LOG(WARNING) << "unlink file:" << fname << " but fail to free zone id:" << info.zone_id();
        return Status(AIE_FAIL, "fail to reset");
    }
    return s;
}

Status SmrIOEnv::unlink(const std::string& fname) {
    Status s;

    auto tmp_name = base::FilePath(fname);
    if (!base::PathExists(tmp_name)) {
        return Status(AIE_NOT_EXIST, "not exist");
    }

    // maybe a dir
    if (base::DirectoryExists(tmp_name)) {
        base::FileEnumerator f_enum(tmp_name, true, base::FileEnumerator::FILES);
        for (base::FilePath name = f_enum.Next(); !name.empty(); name = f_enum.Next()) {
            s = delete_file(name.value());
            if (s.code() != AIE_OK) {
                return s;
            }
        }
        auto ret = base::DeleteFile(tmp_name, true);
        if (!ret) {
            return Status::by_errno("fail to delete dir");
        }
    } else {
        s = delete_file(fname);
    }
    return s;
}

Status SmrIOEnv::get_disk_capacity(const std::string& path, uint64_t* disk_size, uint64_t* free_size) {
    auto s = _zone_manager.get_disk_capacity(disk_size, free_size);
    if (s == AIE_OK) {
        *disk_size *= _zone_size;
        *free_size *= _zone_size;
    }
    return Status(s, "");
}

Status SmrIOEnv::get_zone_size(uint64_t* size) {
    Status s;
    *size = _zone_size;
    return s;
}

Status SmrIOEnv::get_base_id(int32_t* base_id) {
    Status s;
    *base_id = _base_zone_id;
    return s;
}

/*****    SmrFileDescriptor  **********/

Status SmrFileDescriptor::pread(void* buf, uint64_t len, uint64_t offset) {
    Status s;
    uint64_t bytes_read = 0;

    if (!(((_flag & O_ACCMODE) == O_RDONLY) || (_flag & O_RDWR))) {
        return Status(AIE_FAIL, "can not be read");
    }

    if (_zone_id == -1) {
        LOG(WARNING) << "not init zone, you should allocate a zone before read/write";
        return Status(AIE_FAIL, "zone id is -1");
    }

    //if (offset >= _off) {
    //    LOG(WARNING) << "read offset sholud less than off:" << _off;
    //    return Status(AIE_WRONG_READ_OFF, "wrong read offset");
    //}
    uint64_t zone_size = 0;
    _env->get_zone_size(&zone_size);
    int32_t base_id = 0;
    _env->get_base_id(&base_id);
    assert(_zone_id >= base_id);
    assert(offset <= zone_size); 
#ifdef _UNIT_TEST
    if (_use_buffer) {
        bytes_read = ::aries_ut_pread(_env->buf_fd(), buf, len, offset + _zone_id * zone_size);
    } else {
        bytes_read = ::aries_ut_pread(_env->dio_fd(), buf, len, offset + _zone_id * zone_size);
    }
#else
    if (_use_buffer) {
        bytes_read = ::pread(_env->buf_fd(), buf, len, offset + _zone_id * zone_size);
    } else {
        bytes_read = ::pread(_env->dio_fd(), buf, len, offset + _zone_id * zone_size);
    }
#endif
 
    if (bytes_read == len) {
        return s;
    }

    LOG(WARNING) << "read error, fd:" << to_string() << " buf:" << buf << " offset:" << offset
        << " size:" << len << " read:" << bytes_read << " errno:(" << errno << ")"
        << strerror(errno);
    return Status::by_errno(strerror(errno));
}

Status SmrFileDescriptor::pwrite(const void* buf, uint64_t len, uint64_t offset) {
    Status s;
    uint64_t bytes_written = 0;

    if (!((_flag & O_WRONLY) || (_flag & O_RDWR))) {
        return Status(AIE_FAIL, "can not be written");
    }

    if (_zone_id == -1) {
        LOG(WARNING) << "not init zone, you should allocate a zone before read/write";
        return Status(AIE_FAIL, "zone id is -1");
    }
    if (_off != offset) {
        LOG(WARNING) << "write offset is not right, should equal to off:" << _off;
        return Status(AIE_WRONG_WRITE_OFF, "wrong write offset");
    }
    uint64_t zone_size = 0;
    _env->get_zone_size(&zone_size);
    int32_t base_id = 0;
    _env->get_base_id(&base_id);
    assert(_zone_id >= base_id);
    assert(offset <= zone_size);
    do {
        // take care!!! zone storage device only support dio write
        // details in http://zonedstorage.io/introduction/linux-support/#zbd-support-restrictions
        bytes_written = ::pwrite(_env->dio_fd(), buf, len, offset + _zone_id * zone_size);
    } while (false);

    if (bytes_written == len) {
        _off += len;
        return s;
    }
    LOG(WARNING) << "write error, fd:" << to_string() << " buf:" << buf << " offset:" << offset
        << " size:" << len << " write:" << bytes_written << " errno:(" << errno << ")"
        << strerror(errno);
    return Status::by_errno(strerror(errno));
}

Status SmrFileDescriptor::get_write_off(uint64_t* offset) {
    *offset = _off;
    return Status();
}

Status SmrFileDescriptor::get_size(uint64_t* size) {
    if (_zone_id == -1) {
        LOG(WARNING) << "not init zone, you should allocate a zone before read/write";
        return Status(AIE_FAIL, "zone id is -1");
    }

    int32_t base_id = 0;
    _env->get_base_id(&base_id);
    assert(_zone_id >= base_id);

    _env->get_zone_size(size);
    return Status();
}

Status SmrFileDescriptor::fsync() {
    if (::fsync(_fd) < 0) {
        return Status(AIE_FAIL);
    }
    return Status();
}

}
}
