// Copyright (c) 2016 Baidu.com, Inc. All Rights Reserved
// Author <PERSON><PERSON><PERSON><PERSON><PERSON>(chen<PERSON><PERSON><PERSON>@baidu.com)
// Date: Sun Oct  9 10:03:50 CST 2016

#ifndef BAIDU_INF_ARIES_DATANODE_DISK_AGENT_H
#define BAIDU_INF_ARIES_DATANODE_DISK_AGENT_H

#include <atomic>
#include "baidu/rpc/server.h"
#include "baidu/inf/aries-api/common/common.h"
#include "baidu/inf/aries/datanode/util/kylin.h"    // CAsyncClient
#include "baidu/inf/aries/datanode/vlet_manager.h"  // VletPtr
#include "baidu/inf/aries/datanode/disk_manager.h"  // VletPtr
#include "baidu/inf/aries-api/common/proto/datanode.pb.h"
#include "baidu/inf/aries-api/common/priority_queue.h"
#include "baidu/inf/aries/common/common.h"
#include <deque>
#include <sstream>
#include <shared_mutex>
#include <stdio.h>
#include <unordered_map>
#include <boost/regex.hpp>
#include "baidu/inf/aries/datanode/flags.h"
#include "baidu/inf/aries/datanode/util/util.h"
#include "baidu/inf/aries/datanode/io_executor.h"
#include "baidu/inf/aries/datanode/heavyworker.h"
#include "baidu/inf/aries/datanode/shard_checker.h"
#include "baidu/inf/aries/datanode/storage/zone/zone_disk.h"
#include "baidu/inf/aries/datanode/storage/zone/zone_disk_scanner.h"

namespace rocksdb {
class DB;
class Iterator;
class ColumnFamilyHandle;
class Cache;
}

namespace aries {
namespace datanode {

extern boost::regex _s_trash_pattern;
static constexpr uint32_t kSlotNumber = 256;
#if defined(_UNIT_TEST)
constexpr int MAX_TRACE_NUM = 20;
#else
constexpr int MAX_TRACE_NUM = 256;
#endif

bool parse_vlet(std::string name, uint64_t* vid, uint32_t* shard);

class IoutilCollector;
class HotKeyTokenLimiter;
class IoContext;
class CopyVletWorker;
class RewriteVletWorker;
struct VbidLocation;
class VletIndexer;
class FlowControl;

struct VletIdentify {
    bool operator == (const VletIdentify& b) {
        return (b.volume_id == volume_id
                && b.shard_index == shard_index
                && b.vlet_version == vlet_version
                && b.create_time == create_time);
    }

    static bool has_diff_shard_compress_option(const aries::pb::ShardCompressOption& option,
                                    const aries::pb::ShardCompressOption& shard_compress_option) {
        return option.compress_type() != shard_compress_option.compress_type() ||
                option.compress_level() != shard_compress_option.compress_level() ||
                option.min_compress_ratio() != shard_compress_option.min_compress_ratio() ||
                option.slice_split_size() != shard_compress_option.slice_split_size();
    }
    uint64_t volume_id = 0;
    uint32_t shard_index = 0;
    uint32_t vlet_version = 0;
    uint64_t create_time = 0;
    uint64_t volume_version = 0;
    uint64_t max_holes_size_for_fast_remove = 0;
    uint64_t min_record_size_for_fast_remove = 0;
    uint32_t append_zone_rewrite_rate = 0;
    uint32_t daily_rewrite_start_time = 0;
    uint32_t daily_rewrite_duration_second = 0;
    aries::pb::ShardCompressOption shard_compress_option;
    bool use_standard_record_layout = false;
    bool permit_fast_remove = false;
};

struct VletIdentifyWithMembership {
    struct VletIdentify vlet_id;
    aries::pb::Membership      vlet_membership;
};

inline std::ostream& operator<<(std::ostream& os, const VletIdentify& info) {
    return os << "(vid:" << info.volume_id
        << " shard_index:" << info.shard_index
        << " create_time:" << info.create_time
        << " volume_version:" << info.volume_version
        << " vlet_version:" << info.vlet_version
        << ")";
};

struct BatchRecord {
    uint32_t record_type = 0;
    uint32_t from_id = 0;
    uint32_t block_id = 0;
    uint32_t batch_size = 0;
    std::map<uint64_t, bool> vbids;
};

struct AppendBatchRecord {
    uint16_t file_index = UINT16_MAX;
    uint32_t offset = 0;
    uint32_t len = 0;
    std::map<uint64_t, bool> vbids;
};

struct ContinuousSegment {
    // read iops
    uint32_t real_io_count = 0;
    uint64_t volume_id;
    uint32_t data_size = 0;
    bool has_checksum_error = false;
    bool has_record_corruption = false;
    // need vbids;
    void *ext;
};

struct LoadDiskContext : public AsyncContext {
    LoadDiskContext() {
        InitAsyncContext(this);
        ret = 0;
        sync_point = nullptr;
    }

    int                     ret;
    std::string             disk_path; 
    common::SyncPoint*      sync_point;
};

struct CheckHungContext : public AsyncContext {
    CheckHungContext() {
        InitAsyncContext(this);
        vlet_agent_num = 0;
    }

    std::atomic<int>        vlet_agent_num;
    DiskAgent*              disk_agent;
    std::shared_ptr<common::SyncPoint>      sync_point;
};

/*struct CheckHungContext2 : public AsyncContext {
    CheckHungContext2() {
        InitAsyncContext(this);
        sync_point = nullptr;
    }

    int                     ret = -1;
    DiskAgent*              disk_agent;
    std::shared_ptr<common::SyncPoint>      sync_point;
};*/

struct CheckHungKylinContext : public AsyncContext {
     CheckHungKylinContext() {
        InitAsyncContext(this);
    }
    std::shared_ptr<CheckHungContext> ctx;
};

struct CheckSlowContext : public AsyncContext {
    CheckSlowContext() {
        InitAsyncContext(this);
        is_slow_disk = false;
    }

    std::atomic<bool>       is_slow_disk;
    DiskAgent*              disk_agent;
    std::shared_ptr<common::SyncPoint>      sync_point;
};

struct CheckSlowKylinContext : public AsyncContext {
     CheckSlowKylinContext() {
        InitAsyncContext(this);
    }
    std::shared_ptr<CheckSlowContext> ctx;
};

struct RebuildIndexContext : public LoadDiskContext {
};

struct EventPoint {
    const char*     name;
    uint64_t        time_us;
};

template<int N>
struct EventTrace {
    int         ep_index = 0;
    EventPoint  ep_list[N];
};

struct DatanodeContext : public AsyncContext {
    DatanodeContext() {
        InitAsyncContext(this);
        add_point(nullptr);
    }

    virtual ~DatanodeContext() {
        //As *status is part of rpc response and will be release after calling done->Run() soon,
        //so we saved one copy of *status here for log print.
        int32_t error_code = 0;
        std::string error_msg;
        bool has_status = false;
        if (nullptr != status) {
            error_code = status->code();
            has_status = true;
            if (error_code != 0) {
                error_msg = (status->has_msg() && !status->msg().empty() ? status->msg() : "none");
            }
        }

        if (sync_point) {
            sync_point->signal();
        }
        auto done = done_guard.release();
        if (done) {
            done->Run();
        }

        if (cmd && has_status) {
            uint64_t total = base::cpuwide_time_us();
            uint32_t i = event_trace.ep_index;
            uint64_t tmp = event_trace.ep_list[0].time_us;
            total -= tmp;
            std::stringstream log_stream;

            for (int i = 1; i < event_trace.ep_index; ++i) {
                auto& ep = event_trace.ep_list[i];
                log_stream << " " << ep.name << ep.time_us - tmp;
                tmp = ep.time_us;
            }

            if (vid + vbid > 0) {
                log_stream << " total=" << total << " ] vid:" << vid << " vbid:" << vbid
                            << " disk_id:" << disk_id;
            } else {
                log_stream << " total=" << total << " ]";
            }
            if (LIKELY(error_code == 0)) {
                ARIES_RPC_DONE_LOG(NOTICE) << " cmd:" << cmd << " code:" << error_code << " msg:succ, cost_us:[" << log_stream.str();
            } else if (error_code == AIE_CONTINUE) {
                ARIES_RPC_DONE_LOG(NOTICE) << " cmd:" << cmd << " code:" << error_code << " msg:" << error_msg
                                            << " cost_us:[" << log_stream.str();
            } else {
                ARIES_RPC_DONE_LOG(WARNING) << " cmd:" << cmd << " code:" << error_code << " msg:" << error_msg
                                           << " cost_us:[" << log_stream.str();
            }
        }
    }

    void add_point(const char* name) {
        if (event_trace.ep_index + 1 >= MAX_TRACE_NUM) {
            return;
        }
        uint64_t ts = base::cpuwide_time_us();
        auto & ep = event_trace.ep_list[event_trace.ep_index];
        ep.name = name;
        ep.time_us = ts;
        ++(event_trace.ep_index);
    }

    const char*                 cmd = nullptr;
    aries::pb::Status*          status  = nullptr;
    uint64_t                    log_id = 0;
    uint64_t                    vid = 0;
    uint64_t                    vbid = 0;
    int                         disk_id = -1;
    baidu::rpc::ClosureGuard    done_guard;
    std::shared_ptr<common::SyncPoint>      sync_point = nullptr;
    EventTrace<MAX_TRACE_NUM>               event_trace;
};

struct JoinNodeContext : public DatanodeContext {
    JoinNodeContext () {
        cmd = "join_node";
    }

    const aries::pb::JoinNodeRequest*       request = nullptr;
    aries::pb::AckResponse*                        response = nullptr;
    baidu::rpc::Controller*                 cntl = nullptr;

};

struct JoinDiskContext : public DatanodeContext{
    JoinDiskContext() {
        cmd = "join_disk";
    }
    void push_back(const aries::pb::DatanodeVletInfo& vlet_info) {
        vlets.push_back(vlet_info);
    }
    common::DiskType disk_type;
    std::vector<aries::pb::DatanodeVletInfo> vlets;
    const ::aries::pb::AddDiskWithDataRequest* request = nullptr;
    ::aries::pb::AckResponse* response = nullptr;
    baidu::rpc::Controller*                 cntl = nullptr;
};

class CreateVletFileWorker;

struct CreateVletContext : public DatanodeContext {
    CreateVletContext() {
        cmd = "create_vlet";
    }
    virtual ~CreateVletContext();

    VletPtr                         vlet_ptr;

    // life not owned by me
    const aries::pb::CreateVletRequest*            request = nullptr;
    aries::pb::AckResponse*                        response = nullptr;
    baidu::rpc::Controller*                 cntl = nullptr;
    int                                     create_vlet_file_ret = 0;
    std::shared_ptr<CreateVletFileWorker>   worker = nullptr;
    uint64_t    need_space = 0;
};

struct AddVletContext : public DatanodeContext {
    AddVletContext() {
        cmd = "add_vlet";
    }

    VletPtr                         vlet_ptr;

    // life not owned by me
    const aries::pb::AddVletRequest*       request = nullptr;
    aries::pb::AckResponse*                response = nullptr;
    baidu::rpc::Controller*         cntl = nullptr;
    uint64_t vid;
    uint32_t shard_index;
    off_t file_size;
    std::string file_name;
    std::shared_ptr<rocksdb::DB>    db;
    DiskAgent*                      disk_agent;

};

struct DropVletContext: public DatanodeContext {
    DropVletContext() {
        cmd = "drop_vlet";
    }

    VletPtr                         vlet_ptr;

    // life not owned by me
    const aries::pb::DropVletRequest*              request = nullptr;
    aries::pb::AckResponse*                        response = nullptr;
    baidu::rpc::Controller*                 cntl = nullptr;
};

struct AddDiskWithDataContext : public DatanodeContext {
    AddDiskWithDataContext() {
        cmd = "add_disk_with_data";
    }

    const aries::pb::AddDiskWithDataRequest*       request = nullptr;
    aries::pb::AckResponse*                        response = nullptr;
    baidu::rpc::Controller*                 cntl = nullptr;

};

struct AddDiskContext : public DatanodeContext {
    AddDiskContext() {
        cmd = "add_disk";
    }

    // life not owned by me
    const aries::pb::AddDiskRequest*              request = nullptr;
    aries::pb::AckResponse*                        response = nullptr;
    baidu::rpc::Controller*                 cntl = nullptr;
};

struct DropDiskContext : public DatanodeContext {
    DropDiskContext() {
        cmd = "drop_disk";
    }

    // life not owned by me
    const aries::pb::DropDiskRequest*              request = nullptr;
    aries::pb::AckResponse*                        response = nullptr;
    baidu::rpc::Controller*                 cntl = nullptr;
};

struct UpdateDiskContext : public DatanodeContext {
    UpdateDiskContext() {
        cmd = "update_disk";
    }

    // life not owned by me
    const aries::pb::UpdateDiskRequest*              request = nullptr;
    aries::pb::AckResponse*                        response = nullptr;
    baidu::rpc::Controller*                 cntl = nullptr;
};

struct ShowDiskContext : public AsyncContext {
    ShowDiskContext(common::SyncPoint* sp) : sync_point(sp) {
        InitAsyncContext(this);
    }

    ShowDiskContext() {
        InitAsyncContext(this);
    }

    std::string             ret_str;
    uint64_t                log_id = 0;
    bool                    is_used = false;
    uint64_t                used_size = 0;
    uint64_t                disk_total_size = 0;
    uint64_t                disk_free_size = 0;
    uint64_t                aries_capacity = 0;
    aries::pb::DiskConfigure*      disk_conf = nullptr;
    common::SyncPoint*      sync_point = nullptr;
};

struct GetSmrDiskContext : public AsyncContext {
    GetSmrDiskContext(common::SyncPoint* sp) : sync_point(sp) {
        InitAsyncContext(this);
    }

    GetSmrDiskContext() {
        InitAsyncContext(this);
    }

    uint64_t                log_id = 0;
    aries::pb::SmrDiskInfo  disk_info;
    common::SyncPoint*      sync_point = nullptr;
    aries::Status           status;
};


struct SuicideContext : public AsyncContext {
    SuicideContext() {
        InitAsyncContext(this);
    }

    common::SyncPoint*      sync_point = nullptr;
};

struct DropDiskReq : public DropDiskContext {
    DropDiskReq() {
        request = &pb_req;
        response = &pb_res;
    }

    aries::pb::DropDiskRequest         pb_req;
    aries::pb::AckResponse             pb_res;
};

struct DropVletReq : public DropVletContext {
    DropVletReq(std::shared_ptr<common::SyncPoint> sync_point) {
        request = &pb_req;
        response = &pb_res;
        this->sync_point = sync_point;
    }

    aries::pb::DropVletRequest              pb_req;
    aries::pb::AckResponse                  pb_res;
};

struct ListFingerContext : public AsyncContext {
    ListFingerContext() {
        InitAsyncContext(this);
    }

    baidu::rpc::ClosureGuard        done_guard;
    VletPtr                         vlet_ptr;
    uint64_t                        log_id = 0;

    // life not owned by me
    const aries::pb::ListFingerPrintRequest*   request = nullptr;
    aries::pb::ListFingerPrintResponse*        response = nullptr;
    baidu::rpc::Controller*             cntl = nullptr;

    uint32_t timeout_ms = 30000;
    uint64_t en_queue_time = 0;
};

struct ListIndexContext : public AsyncContext {
    ListIndexContext() {
        InitAsyncContext(this);
    }

    baidu::rpc::ClosureGuard        done_guard;
    VletPtr                         vlet_ptr;
    uint64_t                        log_id = 0;

    // life not owned by me
    const aries::pb::ListIndexRequest*     request = nullptr;
    aries::pb::ListIndexResponse*          response = nullptr;
    baidu::rpc::Controller*         cntl = nullptr;

    uint32_t timeout_ms = 30000;
    uint64_t en_queue_time = 0;
};

struct ListBlobContext : public AsyncContext {
    ListBlobContext() {
        InitAsyncContext(this);
    }

    baidu::rpc::ClosureGuard        done_guard;
    VletPtr                         vlet_ptr;
    uint64_t                        log_id = 0;

    // life not owned by me
    const aries::pb::ListBlobRequest*     request = nullptr;
    aries::pb::ListBlobResponse*          response = nullptr;
    baidu::rpc::Controller*        cntl = nullptr;
};

struct GetSlotLocationContext : public AsyncContext {
    GetSlotLocationContext() {
        InitAsyncContext(this);
    }

    baidu::rpc::ClosureGuard       done_guard;
    VletPtr                        vlet_ptr;
    uint64_t                       log_id = 0;

    const aries::pb::GetLocationRequest*        request = nullptr;
    aries::pb::GetLocationResponse*       response = nullptr;
    baidu::rpc::Controller*        cntl = nullptr;
};

struct DiffDiskContext : public AsyncContext {
    DiffDiskContext() {
        InitAsyncContext(this);
        running = false;
        is_used = false;
        is_hung = true;
        need_to_drop =  false;
        disk_agent = nullptr;
        sync_point = nullptr;
    }

    void reset() {
        is_used = false;
        is_hung = true;
        need_to_drop = false;
        vlet_map.clear();
        node_remove_vlet_list.clear();
        master_drop_vlet_list.clear();
    }

    std::atomic<bool> running;
    std::shared_ptr<common::SyncPoint>      sync_point;
    bool                    is_used;
    bool                    is_hung;
    uint64_t                create_time;
    DiskAgent*              disk_agent;
    bool                    need_to_drop;

    // Input
    std::map<uint64_t, VletIdentifyWithMembership> vlet_map;

    // Output
    std::vector<VletIdentify>   node_remove_vlet_list;
    std::vector<VletIdentify>   master_drop_vlet_list;
    std::vector<uint64_t>       need_update_vlet_list;
};

struct RewriteArgContext : public AsyncContext {
    RewriteArgContext() {
        InitAsyncContext(this);
    }

    std::shared_ptr<RewriteVletWorker> worker = nullptr;
};

struct CopyVletArgContext : public AsyncContext {
    CopyVletArgContext() {
        InitAsyncContext(this);
    }

    uint64_t log_id{0};
    std::shared_ptr<CopyVletWorker> worker{nullptr};
};

struct MigrateVletContext : public AsyncContext {
    MigrateVletContext(const aries::pb::VletInfo& src, const aries::pb::VletInfo& dest,
            const aries::pb::CopyVletInfo& copy_info,
            int purpose2, bool be_success, uint64_t log_id2)
    : src_vlet(src), dest_vlet(dest), copy_vlet_info(copy_info), 
        purpose(purpose2), success(be_success), retry_num(0), log_id(log_id2) {
        InitAsyncContext(this);
    }

    uint64_t                src_node;
    uint64_t                dest_node;

    aries::pb::VletInfo            src_vlet;
    aries::pb::VletInfo            dest_vlet;
    aries::pb::CopyVletInfo        copy_vlet_info;
    int                     purpose;

    bool                    success;
    int                     retry_num;
    uint64_t                log_id;
};

struct FullSelfCheckContext : public AsyncContext {
    FullSelfCheckContext() {
        InitAsyncContext(this);
    }

    std::shared_ptr<common::SyncPoint> sync_point = nullptr;
};

enum DatanodeAction {
    TIMER_ACTION    = 0,

    // read write action
    GET_BLOB_ACTION         =   1,
    PUT_BLOB_ACTION         =   2,
    REMOVE_BLOB_ACTION      =   3,
    QUEUE_GET_ACTION        =   4,
    QUEUE_REBUILD_INDEX_ACTION = 5,
    RESTORE_BLOB_ACTION      =   6,
    RETRY_IO_ACTION         = 7,
    BATCH_GET_ACTION      = 8,
    BATCH_GET_SEGMENT_ACTION = 9,

    // control action
    ADD_DISK_ACTION         =   10,
    DROP_DISK_ACTION        =   11,
    CREATE_VLET_ACTION      =   12,
    DROP_VLET_ACTION        =   13,
    UPDATE_VLET_ACTION      =   14,
    ADD_VLET_ACTION         =   15,
    REBUILD_VLET_ACTION     =   16,
    UPDATE_DISK_ACTION      =   17,

    // Meta action
    LIST_FINGER_ACTION      =   21,
    LIST_INDEX_ACTION       =   22,
    GET_VLET_INFO_ACTION    =   23,
    SHOW_DISK_ACTION        =   24,
    GET_VBID_LOCATION_ACTION =  25,
    GET_SLOT_LOCATION_ACTION =  26,
    UPDATE_META_ACTION       =  27,
    GET_RECORD_INDEX_INFO_ACTION = 28,

    // internel action
    DIFF_DISK_ACTION        =   30,
    LOAD_DISK_ACTION        =   31,
    GC_DISK_ACTION          =   32,
    SUICIDE_ACTION          =   34,
    STOP_AGENT_ACTION       =   35,
    SUICIDE_AGENT_ACTION    =   36,
    CHECK_SLOW_ACTION       =   37,
    CHECK_HUNG_ACTION       =   38,
    CLEAN_LEAK_FILE_ACTION  =   39,
    EN_QUEUE_ACTION         =   40,
    CHECK_OVERDUE_ACTION    =   41,
    ADD_DISK_WITHDATA_ACTION =  42,

    JOIN_NODE_ACTION = 43,
    JOIN_DISK_ACTION = 44,

    // Meta action
    LIST_BLOB_ACTION = 45,
    CHECK_ENGINE_ACTION = 46,
#if defined(_CHECK_TEST) || defined(_UNIT_TEST)
    CHANGE_SHARD_ACTION = 47,
#endif

    // append vlet related
    GET_REWRITE_INFO_ACTION = 48,
    GET_ZONEFILES_DETAIL_ACTION = 50,
    GET_VLET_MAP_ACTION = 51,

    // shard check
    CHECK_SHARD_ACTION = 52,

    //append vlet rewrite io token
    GET_REWRITE_IO_TOKEN_ACTION = 53,

    PURGE_VLET_ACTION = 54,
    TIMER_PURGE_VLET_ACTION = 55,
    UPDATE_SHARD_TTL = 56,
    FULL_SELF_CHECK_ACTION = 80,

    NEW_VLET_ACTION             =   102,
    COPY_VLET_ACTION            =   103,
    SUBMIT_VLET_ACTION          =   104,
    REPORT_MIGRATE_ACTION       =   105,
    AFTER_CREAT_VLET_FILE       =   106,

    // smr disk related
    GET_SMR_DISK_INFO_ACTION  = 107,

    // cache
    GET_BLOB_FROM_CACHE_ACTION = 120,
};

struct IndexKey {
public:
    void set_volume_id(uint64_t volume_id) {
        _volume_id = volume_id;
    }

    uint64_t volume_id() const {
        return _volume_id;
    }

    void set_vbid(uint64_t vbid) {
        _vbid = __builtin_bswap64(vbid >> 8) | (0xFF & vbid);
    }

    uint64_t vbid() const {
        return __builtin_bswap64(_vbid >> 8) | (0xFF & _vbid);
    }

private:
    uint64_t _volume_id;
    uint64_t _vbid;
};

constexpr uint32_t kIndexKeyLen = sizeof(IndexKey);
static_assert(kIndexKeyLen == 16);

struct VolumeKey {
    VolumeKey() = default;
    explicit VolumeKey(uint64_t volume_id, uint32_t vlet_type) :
            _volume_id(volume_id), _vlet_type(vlet_type) {}

    void set_volume_id(uint64_t volume_id) {
        _volume_id = volume_id;
    }

    uint64_t volume_id() const {
        return _volume_id;
    }

    void set_vlet_type(uint32_t vlet_type) {
        _vlet_type = vlet_type;
    }

    uint32_t vlet_type() const {
        return _vlet_type;
    }

private:
    uint64_t _volume_id = 0;
    uint64_t _dummy = 0;
    uint32_t _vlet_type = 0;
    uint32_t _key  = 0;  // reserved colume name
};

constexpr uint32_t kVolumeKeyLen = sizeof(VolumeKey);
static_assert(kVolumeKeyLen == 24);

inline std::ostream& operator<<(std::ostream& os, const VolumeKey& key) {
    return os << "(vid:" << key.volume_id() << " vlet_type:" << key.vlet_type() << ")";
};

class GetContext;

struct DelLeakFileContext : public AsyncContext {
public:
    DelLeakFileContext(DiskAgent* disk_agent_) : disk_agent(disk_agent_) {
        InitAsyncContext(this);
        running = false;
    }

    void reset() {
        vlets.clear();
        trash_vlets.clear();
        zonefiles.clear();
        trash_zone_vlets.clear();
    }

    DiskAgent* disk_agent;
    std::atomic<bool> running;
    std::shared_ptr<common::SyncPoint>      sync_point;
    std::vector<std::string> vlets;
    std::vector<std::string> trash_vlets;
    std::vector<std::string> zonefiles;
    std::vector<VolumeKey> trash_zone_vlets;
};

struct CheckOverdueContext : public AsyncContext {
    CheckOverdueContext() {
        InitAsyncContext(this);
    }
    common::SyncPoint*      sync_point;
};

struct GetVletMapContext : public AsyncContext {
    GetVletMapContext() {
        InitAsyncContext(this);
    }
    std::map<uint64_t, aries::datanode::VletPtr> vlet_map;
    common::SyncPoint*      sync_point;
};

struct CheckVletEngineContext : public AsyncContext {
    CheckVletEngineContext() {
        InitAsyncContext(this);
    }

    VletPtr                     vlet_ptr;
    uint64_t                    log_id = 0;
    baidu::rpc::ClosureGuard    done_guard;
    baidu::rpc::Controller*     cntl = nullptr;
    aries::pb::AckResponse*     response = nullptr;
};

struct AddRecordToCacheContext : public DatanodeContext {
    AddRecordToCacheContext(
            const ::aries::pb::AddRecordToCacheRequest* request_,
            ::aries::pb::AckResponse* response_,
            baidu::rpc::Controller* cntl_)
                    : request(request_), response(response_), cntl(cntl_) {
        cmd = "add_record_to_cache";
    }

    virtual aries::pb::Status* mutable_status() {
        return response->mutable_status();
    }

    uint32_t pin_ttl_secs() {
        return request->pin_ttl_secs();
    }

    ShardId shard_id() {
        uint64_t volume_id = request->volume_id();
        uint64_t vbid = request->vbid();
        uint32_t shard_index = request->shard_index();

        return ShardId(volume_id, vbid, shard_index);
    }

    // life not owned by me
    const ::aries::pb::AddRecordToCacheRequest* request{nullptr};
    ::aries::pb::AckResponse* response{nullptr};
    baidu::rpc::Controller* cntl{nullptr};
    uint64_t                log_id = 0;
};

struct RemoveRecordFromCacheContext : public DatanodeContext {
    RemoveRecordFromCacheContext(
            const ::aries::pb::RemoveRecordFromCacheRequest* request_,
            ::aries::pb::AckResponse* response_,
            baidu::rpc::Controller* cntl_)
                    : request(request_), response(response_), cntl(cntl_) {
        cmd = "remove_record_from_cache";
    }

    virtual aries::pb::Status* mutable_status() {
        return response->mutable_status();
    }

    ShardId shard_id() {
        uint64_t volume_id = request->volume_id();
        uint64_t vbid = request->vbid();
        uint32_t shard_index = request->shard_index();

        return ShardId(volume_id, vbid, shard_index);
    }

    // life not owned by me
    const ::aries::pb::RemoveRecordFromCacheRequest* request{nullptr};
    ::aries::pb::AckResponse* response{nullptr};
    baidu::rpc::Controller* cntl{nullptr};
    uint64_t                log_id = 0;
};

class DiskAgent : public CAsyncClient {
public:
    DiskAgent(DiskManager* disk_mgr, VletManager* vlet_mgr, const aries::pb::DiskConfigure& conf);
    ~DiskAgent();
    uint32_t init(int hostid);
    
    void reload();

    void reload_configure();

    virtual int Release() {
        //int n = CAsyncClient::Release();
        return 1;
    }

    uint32_t disk_id() const {
        return _disk_conf.disk_id();
    }

    const std::string& name() const {
        return _name;
    }

    const std::string& disk_path() const {
        return _disk_conf.disk_path();
    }

    bool db_on_pmem() const {
        return _disk_conf.index_use_pmem();
    }

    std::string disk_trash_path() {
        return disk_path() + "/trash/";
    }

    bool is_close_journal() const {
        return _disk_conf.is_close_journal();
    }

    std::string disk_db_path() {
        if (db_on_pmem()) {
            return _disk_conf.pmem_path() + "/db/";
        } else {
            return _disk_conf.disk_path() + "/db/";
        }
    }

    aries::pb::DiskConfigure& disk_conf() {
        return _disk_conf;
    }

    bool is_used() const {
        return _is_used;
    }

    inline void mark_offline() {
        _is_used = false;
    }
    uint64_t used_size() const;

    uint64_t calc_used_size() const;

    uint64_t disk_ioutil() const {
        return _current_ioutil;
    }
    uint64_t disk_svct() const {
        return _current_svct;
    }
    uint64_t read_iops() const {
        return _current_read_iops;
    }
    uint64_t read_throughput() const {
        return _current_read_throughput;
    }
    uint64_t write_iops() const {
        return _current_write_iops;
    }
    uint64_t write_throughput() const {
        return _current_write_throughput;
    }
    common::DiskType disk_type() const {
        return _disk_type;
    }
    ZoneDeviceType zone_device_type() const {
        return _zone_device_type;
    }

    bool is_support_ioprio() {
        return FLAGS_is_open_ioprio && _is_support_ioprio;
    }
    //just for monitor
    void set_disk_total_size(uint64_t size) {
        _disk_total_size = size;
    }
    void set_disk_free_size(uint64_t size) {
        _disk_free_size = size;
    }
    void set_aries_capacity(uint64_t size) {
        _aries_capacity = size;
    }

    VletManager* vlet_manager() const {
        return _vlet_manager;
    }
    void purge_done(uint64_t volume_id = 0);
    void set_bad_disk(const std::string& msg);
    void stop_for_error();

    int db_add_vlet(const VletPtr& vlet_ptr);
    int add_vlet(VletPtr& ptr);
    int add_vlet_if_absent(VletPtr& vlet_ptr);
    std::map<uint64_t, VletPtr> get_vlet_map();

    Status get_disk_usage(uint64_t* disk_total_size,
                          uint64_t* disk_free_size);

    static int set_block_cache(uint64_t capacity_mb);
    static int set_block_cache_for_ssd(uint64_t capacity_mb);

    std::shared_ptr<rocksdb::DB> db() const {
        return _db;
    }

    ZoneDisk* get_zone_disk() const {
        return _zone_disk.get();
    }

    rocksdb::ColumnFamilyHandle* index() const {
        return _index;
    }

    rocksdb::ColumnFamilyHandle* data() const {
        return _data;
    }

    std::shared_ptr<FlowControl> flow_control() {
        return _flow_control;
    }

    void add_worker(std::shared_ptr<CopyVletWorker> work) {
        common::ScopedMutexLock lock(_mutex);
        _running_copy_works.push_back(work);
    }
    void del_worker(std::shared_ptr<CopyVletWorker> work) {
        common::ScopedMutexLock lock(_mutex);
        auto it = _running_copy_works.begin();
        while (it != _running_copy_works.end()) {
            if (*it == work) {
                _running_copy_works.erase(it);
                return;
            }
            it++;
        }
    }

    void add_create_worker(std::shared_ptr<CreateVletFileWorker> work) {
        common::ScopedMutexLock lock(_mutex);
        _running_create_works.push_back(work);
    }
    void del_create_worker(std::shared_ptr<CreateVletFileWorker> work) {
        common::ScopedMutexLock lock(_mutex);
        auto it = _running_create_works.begin();
        while (it != _running_create_works.end()) {
            if (*it == work) {
                _running_create_works.erase(it);
                return;
            }
            it++;
        }
    }

    std::shared_ptr<common::MultiSpeedTokenPool> disk_qps_limiter() const {
        return _disk_qps_limiter;
    } 

    IOExecutor* io_executor() {
        return &_io_executor;
    }

    HeavyWorker* heavy_worker() {
        return &_heavy_worker;
    }

    folly::Executor::KeepAlive<> bg_executor() {
        return _bg_executor.get();
    }

    CacheManager* cache_manager() {
        if (!FLAGS_enable_cache || _disk_manager == nullptr)  {
            return nullptr;
        }
        return _disk_manager->cache_manager();
    }

    VletIndexer* vlet_indexer();

    static bool match(IoContext* ctx);

    static bool match_all(IoContext* ctx);

    static void* rebuild_vlet(void* arg);

    bool is_reach_qps_limit(IoContext* ctx);

    bool is_zone_disk() const { return _zone_device_type != ZoneDeviceType::INVALID; }
    bool is_file_zone_disk() const { return _zone_device_type == ZoneDeviceType::FILE; }

    template <bool is_stop_agent>

    auto acquire_rewrite_lock()
            -> std::conditional_t<is_stop_agent, std::unique_lock<std::shared_mutex>,
                                    std::shared_lock<std::shared_mutex>> {
        if constexpr (is_stop_agent) {
            return std::unique_lock{_rewrite_mutex};
        } else {
            return std::shared_lock{_rewrite_mutex};
        }
    }

private:
    virtual void OnCompletion(AsyncContext* pCtx);
    void on_after_create_vlet_file(CreateVletContext* ctx);

    void on_load_disk(LoadDiskContext* ctx);

    void on_create_vlet(CreateVletContext* ctx);

    void on_drop_vlet(DropVletContext* ctx);

    void on_drop_disk(DropDiskContext* ctx);

    void on_add_disk(AddDiskContext* ctx);

    void on_rebuild_vlet(AddVletContext* ctx);

    void add_disk_withdata(AddDiskWithDataContext* ctx);

    void on_list_finger(ListFingerContext* ctx);

    void on_list_index(ListIndexContext* ctx);

    void on_diff_disk(DiffDiskContext* ctx);

    void on_show_disk(ShowDiskContext* ctx);

    void on_get_smr_disk_info(GetSmrDiskContext* ctx);

    void on_update_disk(UpdateDiskContext* ctx);

    void on_drop_disk_self(AsyncContext* ctx);

    void on_suicide(SuicideContext* pCtx);

    int diff_vlet(DiffDiskContext* ctx);

    int drop_vlet(VletPtr& ptr);

    int open_linked_vlet(uint64_t volume_id, uint32_t vlet_type, rocksdb::Iterator* iter);

    void check_balance_vlet_task();

    void update_svct_weight();

    void check_zone_device_space();

    void prepare_zone_resource();

    void on_stop_agent();

    void on_timer();

    void on_purge_timer();

    void on_new_vlet(CopyVletArgContext* ctx);

    void on_submit_vlet(CopyVletArgContext* ctx);

    void on_check_slow(std::shared_ptr<CheckSlowContext> ctx);

    void on_check_hung(std::shared_ptr<CheckHungContext> ctx);

    void stop_vlet_agents();

    void stop();

    void stop_create_or_copy_workers();

    void clear_vlet_map();

    void on_check_overdue(CheckOverdueContext* ctx);

    void on_add_vlet(AddVletContext* ctx);

    void on_check_shard(ShardCheckContext* ctx);

    int db_close();

    int db_open();

    //rebuild index and load meta
    int load_stores();
    int load_stores_based_on_db();

    inline void purge_overdue_call(common::SimplePriorityQueueNoLock<IoContext*, QUEUE_LEVEL>* queue) {
        queue->erase(std::bind(&DiskAgent::match, std::placeholders::_1));
    }

    int open_zone_disk();

    void init_ioutil_collector();

    int recover_zone_disk();

    /**
     * update the state of zones in zone_device, zone_disk and db, according to
     * their actual states, as specified in "zone_infos".
     *
     * The state of the zones in "zone_infos" may be changed:
     * - OPEN zones will be closed
     * - OPEN zones will be freed if it do not have data
     * - CLOSED zones will be freed if its zone file do not exist
     */
    int recover_zone_info(const std::map<ZoneID, ScanZoneInfo>& zone_infos);

    // recover vlets and add to "vlets" in the format of <volume_id, shard_index>
    int recover_node_vlet_info(std::unordered_set<std::pair<uint64_t, uint32_t>>* vlets);

    // write index of the records into db
    int recover_record_index(const std::map<ZoneRecordIndexKey, ZoneRecordIndexEntry>& record_infos);

    void start_io_executor();

    void start_heavyworker();

    void compact_db();

    ZoneDeviceBackend* create_zone_device(ZoneDeviceType zone_device_type);

    uint32_t get_zone_file_count() const;
private:
    static constexpr int64_t DEFAULT_SCAN_THREAD_NUM = 8;
    // imuttable data structure
    DiskManager*                _disk_manager;
    VletManager*                _vlet_manager;
    aries::pb::DiskConfigure           _disk_conf;

    std::string                 _name;
    uint64_t                    _create_time = 0;

    // used by other thread
    std::atomic<bool>            _is_used;
    std::atomic<bool>            _is_bad_disk;
    std::atomic<bool>            _is_stop;

    // _used_size and _calc_used_size is only for linked and append engine, it is meaningless for zone engine
    std::atomic<uint64_t>        _used_size;
    std::atomic<uint64_t>        _calc_used_size;

    std::atomic<uint64_t>        _disk_total_size;
    std::atomic<uint64_t>        _disk_free_size;
    std::atomic<uint64_t>        _aries_capacity;
    std::atomic<uint64_t>        _current_ioutil;   // [0, 100], used to report master metrics
    std::atomic<uint64_t>        _current_svct;
    std::atomic<uint64_t>        _current_read_iops;
    std::atomic<uint64_t>        _current_read_throughput;
    std::atomic<uint64_t>        _current_write_iops;
    std::atomic<uint64_t>        _current_write_throughput;
    bool           _is_support_ioprio;

    uint32_t                        _io_thread_num;
    uint32_t                        _inc_id = 0;

    std::map<uint64_t, VletPtr>     _vlet_map;
    std::map<uint64_t, uint64_t>    _svct_map;
    std::map<uint64_t, uint64_t>    _svct_weight_map;

    int32_t                         _stop_agent_wait = 0;

    uint32_t                        _update_svct_cycle_index = 0;
    uint32_t                        _timer_counter = 0;
    AsyncContext                    _timer_context;
    uint64_t                        _total_creating_size = 0;
    AsyncContext                    _purge_timer_context;
    uint64_t                        _last_purge_timestamp = 0;
    std::set<uint64_t>              _purged_vlets;
    std::atomic<bool>               _purge_working;
    std::shared_ptr<rocksdb::DB>        _db;
    rocksdb::ColumnFamilyHandle*        _index = nullptr;
    rocksdb::ColumnFamilyHandle*        _data = nullptr;

    static std::shared_ptr<rocksdb::Cache>     s_block_cache;
    static std::shared_ptr<rocksdb::Cache>     s_block_cache_for_ssd;

    common::MutexLock               _mutex;

    // _rewrite_mutex is acquired on rewrite and drop_disk, to avoid
    // ZoneRewriter still running after DiskAgent stops. Although
    // using std::mutex is ok(since there is only one rewrite thread
    // per disk), read-write lock is used in case there will be more
    // rewrite threads in the future.
    std::shared_mutex                      _rewrite_mutex;

    std::vector<std::shared_ptr<CopyVletWorker>>             _running_copy_works;
    std::vector<std::shared_ptr<CreateVletFileWorker>>       _running_create_works;
    common::SimplePriorityQueueNoLock<IoContext*, QUEUE_LEVEL> _pending_queue;
    std::shared_ptr<common::MultiSpeedTokenPool> _disk_qps_limiter;
    std::unordered_map<uint64_t, bool> _running_volume_id;
    uint64_t _max_iops;
    uint64_t _max_ioutil;  // [0, 100] is valid
    std::unique_ptr<IoutilCollector>     _ioutil_collector;
    common::DiskType _disk_type = common::DT_INVALID;
    std::shared_ptr<ShardChecker> _shard_checker_ptr;
    std::unique_ptr<VletIndexer> _vlet_indexer;

    // destruct order: io_executor -> bg_executor -> zone_disk
    ZoneDeviceType _zone_device_type = ZoneDeviceType::INVALID;
    std::unique_ptr<ZoneDeviceBackend> _device = nullptr;
    std::unique_ptr<ZoneDisk> _zone_disk = nullptr;
    std::shared_ptr<FlowControl> _flow_control = nullptr;

    std::thread _compact_db_thread;
    std::list<uint64_t> _need_compact_db_vlets;

    std::unique_ptr<folly::CPUThreadPoolExecutor> _bg_executor = nullptr;

    // following members are used for new thread-model
    IOExecutor _io_executor;
    HeavyWorker _heavy_worker;
};

} // end namespace of datanode
} // end namespace of aries

#endif //#ifndef BAIDU_INF_ARIES_DATANODE_DISK_AGENT_H

/* vim: set ts=4 sw=4 sts=4 tw=100 */
