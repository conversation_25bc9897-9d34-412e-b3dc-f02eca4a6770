// Copyright (c) 2016 Baidu.com, Inc. All Rights Reserved
// Author <PERSON><PERSON><PERSON><PERSON><PERSON>(chen<PERSON><PERSON><PERSON>@baidu.com)
// Date: Sat Oct  8 15:32:10 CST 2016

#include <memory>
#include "baidu/rpc/server.h"
#include "base/memory/scoped_ptr.h"
#include "baidu/inf/aries-api/common/common.h"
#include "baidu/inf/aries/datanode/service/data_service_impl.h"
#include "baidu/inf/aries/datanode/datanode.h"
#include "baidu/inf/aries/datanode/vlet.h"
#include "baidu/inf/aries/datanode/io_context.h"
#include "baidu/inf/aries/datanode/disk_agent.h"
#include "baidu/inf/aries/datanode/flags.h"
#include "baidu/inf/aries/datanode/throttle/flow_control.h"
#include "baidu/inf/aries-api/common/proto/error_code.pb.h"
#include "baidu/inf/aries-api/common/rpc_call.h"
#include "baidu/inf/aries/datanode/shard_checker.h"
#include "baidu/inf/aries/datanode/io_scheduler.h"

namespace aries {
namespace datanode {

ARIES_BVAR_COUNTER_MIN(datanode, get_total);

std::shared_ptr<common::TokenPool> g_list_index_token_pool = nullptr;

void DataNodeDataServiceImpl::put(::google::protobuf::RpcController* controller,
        const ::aries::pb::ShardPutRequest* request,
        ::aries::pb::ShardPutResponse* response,
        ::google::protobuf::Closure* done) {
    // hold Closure done
    baidu::rpc::ClosureGuard done_guard(done);
    baidu::rpc::Controller* cntl =
            static_cast<baidu::rpc::Controller*>(controller);
    uint64_t log_id = cntl->log_id();

    uint64_t volume_id = request->volume_id();
    uint64_t vbid = request->vbid();
    uint32_t shard_index = request->shard_index();

    ARIES_RPC_RECV_LOG(NOTICE) << " cmd:" << __FUNCTION__
            << " vid:" << volume_id << " vbid:" << vbid
            << " shard_index:" << shard_index
            << " priority:" << request->priority()
            << " network_qos:" << request->network_qos()
            << " pin_ttl_s:" << request->cache_pin_ttl_secs();

    auto status = response->mutable_status();

    if (request->token() != FLAGS_token) {
        status->set_code(AIE_INVALID_TOKEN);
        status->set_msg("invalid token");
        ARIES_RPC_DONE_LOG(NOTICE) << " invalid token";
        return ;
    }

    if (FLAGS_readonly_mode) {
        status->set_code(AIE_READ_ONLY);
        status->set_msg("dn is read only");
        ARIES_RPC_DONE_LOG(NOTICE) << " refused due to dn read only mode";
        return;
    }

    if (vbid <= 0) {
        status->set_code(AIE_INVALID_ARGUMENT);
        status->set_msg("invalid vbid");
        ARIES_RPC_DONE_LOG(NOTICE) << " invalid vbid";
        return ;
    }

    VletManager* vlet_mgr = g_datanode->vlet_manager();
    VletPtr vlet_ptr = vlet_mgr->find(volume_id);

    if (!vlet_ptr || vlet_ptr->shard_index() != shard_index) {
        // target vlet not found
        status->set_code(AIE_NOT_EXIST);
        status->set_msg("target vlet not found");
        ARIES_RPC_DONE_LOG(NOTICE) << " target vlet not found";
        return ;
    }
    
    if (FLAGS_enable_ssd_flow_control) {
        auto disk_agent = vlet_ptr->disk_agent();
        auto flow_control = disk_agent->flow_control();
        FlowType flow_type = OTHERS_FLOW;
        if (request->priority() == aries::HIGHEST) {
            flow_type = USER_FLOW;
        }
        if (!flow_control->try_acquire(cntl->request_attachment().size(), flow_type, WRITE)) {
            status->set_code(AIE_BUSY);
            status->set_msg("get throughput reached limit");
            ARIES_RPC_DONE_LOG(NOTICE) << " get throughput reached limit";
            return;
        }
        flow_control->cal_avg_record_size(cntl->request_attachment().size());
    }

    PutContext* ctx = new PutContext();

    ctx->vlet_ptr = vlet_ptr;
    ctx->request = request;
    ctx->response = response;
    ctx->status = status;
    ctx->cntl = cntl;
    ctx->log_id = cntl->log_id();
    ctx->vid = volume_id;
    ctx->vbid = vbid;
    ctx->done_guard.reset(done_guard.release());

    ctx->en_queue_time = base::gettimeofday_ms();
    ctx->en_queue_time_us = base::gettimeofday_us();
    if (request->has_timeout_ms()) {
        ctx->timeout_ms = request->timeout_ms();
    } else {
        ctx->timeout_ms = FLAGS_call_timeout_ms;
    }
    if (request->has_priority()) {
        ctx->priority = request->priority();
    } else {
        ctx->priority = FLAGS_default_priority;
    }
    if (request->has_speed_coefficient()) {
        ctx->speed_coefficient = request->speed_coefficient();
    }
    ctx->action = PUT_BLOB_ACTION;
    base::get_leaky_singleton<IOScheduler>()->dispatch_task(static_cast<IoContext*>(ctx));
}

void DataNodeDataServiceImpl::update_meta(::google::protobuf::RpcController* controller,
        const ::aries::pb::ShardUpdateMetaRequest* request,
        ::aries::pb::AckResponse* response,
        ::google::protobuf::Closure* done) {
    // hold Closure done
    baidu::rpc::ClosureGuard done_guard(done);
    baidu::rpc::Controller* cntl =
            static_cast<baidu::rpc::Controller*>(controller);
    uint64_t log_id = cntl->log_id();

    uint64_t volume_id = request->volume_id();
    uint64_t vbid = request->vbid();
    uint32_t shard_index = request->shard_index();

    ARIES_RPC_RECV_LOG(NOTICE) << " cmd:" << __FUNCTION__
            << " vid:" << volume_id << " vbid:" << vbid
            << " shard_index:" << shard_index
            << " priority:" << request->priority();

    auto status = response->mutable_status();

    if (request->token() != FLAGS_token) {
        status->set_code(AIE_INVALID_TOKEN);
        status->set_msg("invalid token");
        ARIES_RPC_DONE_LOG(NOTICE) << " invalid token";
        return ;
    }
    if (FLAGS_readonly_mode) {
        status->set_code(AIE_READ_ONLY);
        status->set_msg("dn is read only");
        ARIES_RPC_DONE_LOG(NOTICE) << " refused due to dn read only mode";
        return;
    }

    if (vbid <= 0) {
        status->set_code(AIE_INVALID_ARGUMENT);
        status->set_msg("invalid vbid");
        ARIES_RPC_DONE_LOG(NOTICE) << " invalid vbid";
        return ;
    }

    VletManager* vlet_mgr = g_datanode->vlet_manager();
    VletPtr vlet_ptr = vlet_mgr->find(volume_id);

    if (!vlet_ptr || vlet_ptr->shard_index() != shard_index) {
        // target vlet not found
        status->set_code(AIE_NOT_EXIST);
        status->set_msg("target vlet not found");
        ARIES_RPC_DONE_LOG(NOTICE) << " target vlet not found";
        return ;
    }

    UpdateMetaContext* ctx = new UpdateMetaContext();

    ctx->vlet_ptr = vlet_ptr;
    ctx->request = request;
    ctx->response = response;
    ctx->status = status;
    ctx->cntl = cntl;
    ctx->log_id = cntl->log_id();
    ctx->vid = volume_id;
    ctx->vbid = vbid;
    ctx->done_guard.reset(done_guard.release());

    ctx->en_queue_time = base::gettimeofday_ms();
    ctx->en_queue_time_us = base::gettimeofday_us();
    if (request->has_timeout_ms()) {
        ctx->timeout_ms = request->timeout_ms();
    } else {
        ctx->timeout_ms = FLAGS_call_timeout_ms;
    }
    if (request->has_priority()) {
        ctx->priority = request->priority();
    } else {
        ctx->priority = FLAGS_default_priority;
    }

    ctx->action = UPDATE_META_ACTION;
    base::get_leaky_singleton<IOScheduler>()->dispatch_task(static_cast<IoContext*>(ctx));
}

void DataNodeDataServiceImpl::get(::google::protobuf::RpcController* controller,
        const ::aries::pb::ShardGetRequest* request,
        ::aries::pb::ShardGetResponse* response,
        ::google::protobuf::Closure* done) {
    // hold Closure done
    baidu::rpc::ClosureGuard done_guard(done);
    baidu::rpc::Controller* cntl = static_cast<baidu::rpc::Controller*>(controller);
    uint64_t log_id = cntl->log_id();
    uint64_t volume_id = request->volume_id();
    uint64_t vbid = request->vbid();
    uint32_t shard_index = request->shard_index();

    std::stringstream log_stream;
    log_stream << "[log_id:" << log_id << "]"
        << " [cmd:" << __FUNCTION__ << "]"
        << " recv " << request->GetTypeName()
        << " from " << cntl->remote_side()
        << " vid:" << volume_id << " vbid:" << vbid
        << " shard_index:" << shard_index
        << " need_data:" << request->need_data()
        << " offset:" << request->offset()
        << " len:" << request->len()
        << " need_meta:" << request->need_meta()
        << " priority:" << request->priority()
        << " fast_range_get:" << request->fast_range_get()
        << " network_qos:" << request->network_qos();
    LOG(NOTICE) << log_stream.str();

    auto status = response->mutable_status();

    if (request->token() != FLAGS_token) {
        status->set_code(AIE_INVALID_TOKEN);
        status->set_msg("invalid token");
        ARIES_RPC_DONE_LOG(NOTICE) << " invalid token";
        return ;
    }

    if (vbid <= 0) {
        status->set_code(AIE_INVALID_ARGUMENT);
        status->set_msg("invalid vbid");
        ARIES_RPC_DONE_LOG(NOTICE) << " invalid vbid";
        return ;
    }

    aries::Qos priority = (aries::Qos)FLAGS_default_priority;
    if (request->has_priority()) {
        priority = request->priority();
    }

    VletManager* vlet_mgr = g_datanode->vlet_manager();
    VletPtr vlet_ptr = vlet_mgr->find(volume_id);

    if (!vlet_ptr || vlet_ptr->shard_index() != shard_index) {
        // target vlet not found
        status->set_code(AIE_NOT_EXIST);
        status->set_msg("target vlet not found");
        ARIES_RPC_DONE_LOG(NOTICE) << " target vlet not found";
        return ;
    }

    if (FLAGS_enable_cache && g_datanode->cache_manager()->enabled()) {
        auto* cache_mgr = g_datanode->cache_manager();
        bool is_tracked = cache_mgr->is_tracked(ShardId(volume_id, vbid, (int32_t) shard_index));
        if (is_tracked) {
            g_datanode_get_total_counter << 1;
            auto* done_ptr = done_guard.release();
            int ret = do_cache_get(request, response, cntl, done_ptr, vlet_ptr);
            if (ret == AIE_OK) {
                return;
            } else {
                LOG(NOTICE) << "cache pending queue is full, try in secondary storage";
                done_guard.reset(done_ptr);
            }
        }
        g_datanode_cache_statistics.on_cache_miss(priority);
    }
    
    if (FLAGS_enable_ssd_flow_control) {
        auto disk_agent = vlet_ptr->disk_agent();
        auto flow_control = disk_agent->flow_control();
        FlowType flow_type = OTHERS_FLOW;
        if (priority == aries::HIGHEST || priority == aries::DEGRADE_HIGH) {
            flow_type = USER_FLOW;
        }
        int64_t size;
        if (request->fast_range_get() && request->len() != 0) {
            uint64_t align_size = kPageSize;
            if (disk_agent->is_zone_disk()) {
                align_size = disk_agent->get_zone_disk()->align_size();
            }
            size = need_page_number(request->len() + align_size) * align_size;
        } else {
            size = flow_control->avg_record_size();
        }
        if (!flow_control->try_acquire(size, flow_type, READ)) {
            status->set_code(AIE_BUSY);
            status->set_msg("get throughput reached limit");
            ARIES_RPC_DONE_LOG(NOTICE) << " get throughput reached limit";
            return;
        }
    }

    g_datanode_get_total_counter << 1;
    GetContext* ctx = new GetContext();

    ctx->key = ::aries::make_bid(volume_id, vbid);
    ctx->vlet_ptr = vlet_ptr;
    ctx->request = request;
    ctx->response = response;
    ctx->status = status;
    ctx->cntl = cntl;
    ctx->log_id = cntl->log_id();
    ctx->vid = volume_id;
    ctx->vbid = vbid;
    ctx->done_guard.reset(done_guard.release());
    ctx->en_queue_time = base::gettimeofday_ms();
    ctx->en_queue_time_us = base::gettimeofday_us();
    if (request->has_timeout_ms()) {
        ctx->timeout_ms = request->timeout_ms();
    } else {
        ctx->timeout_ms = FLAGS_call_timeout_ms;
    }
    ctx->priority = priority;
    if (ctx->priority != aries::HIGHEST) {
        ctx->skip_admit_to_cache=true;
    }
    if (request->has_speed_coefficient()) {
        ctx->speed_coefficient = request->speed_coefficient();
    }
    ctx->action = GET_BLOB_ACTION;
    DiskAgentPtr disk_agent = vlet_ptr->disk_agent();
    base::get_leaky_singleton<IOScheduler>()->dispatch_task(static_cast<IoContext*>(ctx));
}

void DataNodeDataServiceImpl::batch_get(::google::protobuf::RpcController* controller,
            const ::aries::pb::ShardBatchGetRequest* request,
            ::aries::pb::ShardBatchGetResponse* response,
            ::google::protobuf::Closure* done) {
    // hold Closure done
    baidu::rpc::ClosureGuard done_guard(done);
    baidu::rpc::Controller* cntl =
            static_cast<baidu::rpc::Controller*>(controller);
    uint64_t log_id = cntl->log_id();

    uint64_t volume_id = request->volume_id();
    uint32_t vbid_size = request->vbids_size();
    uint32_t shard_index = request->shard_index();

    ARIES_RPC_RECV_LOG(NOTICE) << " cmd:" << __FUNCTION__
            << " vid:" << volume_id << " vbid_size:" << vbid_size
            << " shard_index:" << shard_index
            << " need_data:" << request->need_data()
            << " need_meta:" << request->need_meta()
            << " priority:" << request->priority();

    auto status = response->mutable_status();

    if (request->token() != FLAGS_token) {
        status->set_code(AIE_INVALID_TOKEN);
        status->set_msg("invalid token");
        ARIES_RPC_DONE_LOG(NOTICE) << " invalid token";
        return ;
    }

    aries::Qos priority = (aries::Qos)FLAGS_default_priority;
    if (request->has_priority()) {
        priority = request->priority();
    }
    auto flow_limiter = g_datanode->flow_limiter();
    if (!flow_limiter->check_flow(priority, FLAGS_average_shard_size * vbid_size)) {
        status->set_code(AIE_BUSY);
        status->set_msg("get throughput reached limit");
        ARIES_RPC_DONE_LOG(NOTICE) << " get throughput reached limit";
        return;
    }

    VletManager* vlet_mgr = g_datanode->vlet_manager();
    VletPtr vlet_ptr = vlet_mgr->find(volume_id);

    if (!vlet_ptr || vlet_ptr->shard_index() != shard_index) {
        // target vlet not found
        status->set_code(AIE_NOT_EXIST);
        status->set_msg("target vlet not found");
        ARIES_RPC_DONE_LOG(NOTICE) << " target vlet not found";
        return ;
    }

    DiskAgentPtr disk_agent = vlet_ptr->disk_agent();
    if (FLAGS_enable_ssd_flow_control) {
        if (disk_agent) {
            auto flow_control = disk_agent->flow_control();
            if (!flow_control->try_acquire_avg_record_size(vbid_size, OTHERS_FLOW)) {
                status->set_code(AIE_BUSY);
                status->set_msg("get throughput reached limit");
                ARIES_RPC_DONE_LOG(NOTICE) << " get throughput reached limit";
                return;
            }
        }
    }

    BatchGetContext* ctx = new BatchGetContext();
    ctx->cntl = cntl;
    ctx->log_id = cntl->log_id();
    ctx->done_guard.reset(done_guard.release());
    ctx->en_queue_time = base::gettimeofday_ms();
    ctx->en_queue_time_us = base::gettimeofday_us();
    ctx->request = request;
    ctx->response = response;
    ctx->status = status;
    ctx->vlet_ptr = vlet_ptr;
    ctx->vid = volume_id;
    ctx->disk_agent = disk_agent;
    ctx->real_io_count = 0;
    ctx->disk_id = disk_agent->disk_id();
    if (request->has_timeout_ms()) {
        ctx->timeout_ms = request->timeout_ms();
    } else {
        ctx->timeout_ms = FLAGS_call_timeout_ms;
    }
    ctx->priority = priority;
    if (request->has_speed_coefficient()) {
        ctx->speed_coefficient = request->speed_coefficient();
    }
    ctx->action = BATCH_GET_ACTION;
    vlet_ptr->batch_get(ctx);
}

void DataNodeDataServiceImpl::get_record_index_info(::google::protobuf::RpcController* controller,
                     const ::aries::pb::GetRecordIndexInfoRequest* request,
                     ::aries::pb::GetRecordIndexInfoResponse* response,
                     ::google::protobuf::Closure* done) {
    // hold Closure done
    baidu::rpc::ClosureGuard done_guard(done);
    baidu::rpc::Controller* cntl = static_cast<baidu::rpc::Controller*>(controller);
    uint64_t log_id = cntl->log_id();
    uint64_t volume_id = request->volume_id();
    uint64_t vbid = request->vbid();
    uint32_t shard_index = request->shard_index();

    std::stringstream log_stream;
    log_stream << "[log_id:" << log_id << "]"
        << " [cmd:" << __FUNCTION__ << "]"
        << " recv " << request->GetTypeName()
        << " from " << cntl->remote_side()
        << " vid:" << volume_id << " vbid:" << vbid
        << " shard_index:" << shard_index
        << " priority:" << request->priority()
        << " network_qos:" << request->network_qos();
    LOG(NOTICE) << log_stream.str();

    auto status = response->mutable_status();

    if (request->token() != FLAGS_token) {
        status->set_code(AIE_INVALID_TOKEN);
        status->set_msg("invalid token");
        ARIES_RPC_DONE_LOG(NOTICE) << " invalid token";
        return ;
    }

    if (FLAGS_readonly_mode) {
        status->set_code(AIE_READ_ONLY);
        status->set_msg("dn is read only");
        ARIES_RPC_DONE_LOG(NOTICE) << " refused due to dn read only mode";
        return;
    }

    if (vbid <= 0) {
        status->set_code(AIE_INVALID_ARGUMENT);
        status->set_msg("invalid vbid");
        ARIES_RPC_DONE_LOG(NOTICE) << " invalid vbid";
        return ;
    }

    aries::Qos priority = (aries::Qos)FLAGS_default_priority;
    if (request->has_priority()) {
        priority = request->priority();
    }

    VletManager* vlet_mgr = g_datanode->vlet_manager();
    VletPtr vlet_ptr = vlet_mgr->find(volume_id);

    if (!vlet_ptr || vlet_ptr->shard_index() != shard_index) {
        // target vlet not found
        status->set_code(AIE_NOT_EXIST);
        status->set_msg("target vlet not found");
        ARIES_RPC_DONE_LOG(NOTICE) << " target vlet not found";
        return ;
    }

    GetRecordIndexInfoContext* ctx = new GetRecordIndexInfoContext();

    ctx->key = ::aries::make_bid(volume_id, vbid);
    ctx->vlet_ptr = vlet_ptr;
    ctx->request = request;
    ctx->response = response;
    ctx->status = status;
    ctx->cntl = cntl;
    ctx->log_id = cntl->log_id();
    ctx->vid = volume_id;
    ctx->vbid = vbid;
    ctx->done_guard.reset(done_guard.release());
    ctx->en_queue_time = base::gettimeofday_ms();
    ctx->en_queue_time_us = base::gettimeofday_us();
    if (request->has_timeout_ms()) {
        ctx->timeout_ms = request->timeout_ms();
    } else {
        ctx->timeout_ms = FLAGS_call_timeout_ms;
    }
    if (request->has_priority()) {
        ctx->priority = request->priority();
    } else {
        ctx->priority = FLAGS_default_priority;
    }

    ctx->action = GET_RECORD_INDEX_INFO_ACTION;
    DiskAgentPtr disk_agent = vlet_ptr->disk_agent();
    base::get_leaky_singleton<IOScheduler>()->dispatch_task(static_cast<IoContext*>(ctx));
}

void DataNodeDataServiceImpl::update_shard_ttl(::google::protobuf::RpcController* controller,
        const ::aries::pb::UpdateShardTtlRequest* request,
        ::aries::pb::AckResponse* response,
        ::google::protobuf::Closure* done) {
    // hold Closure done
    baidu::rpc::ClosureGuard done_guard(done);
    baidu::rpc::Controller* cntl =
            static_cast<baidu::rpc::Controller*>(controller);
    uint64_t log_id = cntl->log_id();

    uint64_t volume_id = request->volume_id();
    uint32_t shard_index = request->shard_index();

    ARIES_RPC_RECV_LOG(NOTICE) << " cmd:" << __FUNCTION__
            << " vid:" << volume_id << " vbid:" << request->vbid()
            << " shard_index:" << shard_index
            << " blob_ttl_timestamp:" << request->blob_ttl_timestamp()
            << " priority:" << request->priority();

    auto status = response->mutable_status();

    if (request->token() != FLAGS_token) {
        status->set_code(AIE_INVALID_TOKEN);
        status->set_msg("invalid token");
        ARIES_RPC_DONE_LOG(NOTICE) << " invalid token";
        return ;
    }
    if (FLAGS_readonly_mode) {
        status->set_code(AIE_READ_ONLY);
        status->set_msg("dn is read only");
        ARIES_RPC_DONE_LOG(NOTICE) << " refused due to dn read only mode";
        return;
    }

    aries::Qos priority = (aries::Qos)FLAGS_default_priority;
    VletManager* vlet_mgr = g_datanode->vlet_manager();
    VletPtr vlet_ptr = vlet_mgr->find(volume_id);

    if (!vlet_ptr || vlet_ptr->shard_index() != shard_index) {
        // target vlet not found
        status->set_code(AIE_NOT_EXIST);
        status->set_msg("target vlet not found");
        ARIES_RPC_DONE_LOG(NOTICE) << " target vlet not found";
        return ;
    }

    UpdateShardTtlContext* ctx = new UpdateShardTtlContext();
    ctx->cntl = cntl;
    ctx->log_id = cntl->log_id();
    ctx->done_guard.reset(done_guard.release());
    ctx->en_queue_time = base::gettimeofday_ms();
    ctx->en_queue_time_us = base::gettimeofday_us();
    ctx->request = request;
    ctx->response = response;
    ctx->status = status;
    ctx->vlet_ptr = vlet_ptr;
    ctx->vid = volume_id;
    if (request->has_timeout_ms()) {
        ctx->timeout_ms = request->timeout_ms();
    } else {
        ctx->timeout_ms = FLAGS_call_timeout_ms;
    }
    ctx->priority = priority;
    ctx->action = UPDATE_SHARD_TTL;
    base::get_leaky_singleton<IOScheduler>()->dispatch_task(static_cast<IoContext*>(ctx));
}

void DataNodeDataServiceImpl::remove(::google::protobuf::RpcController* controller,
        const ::aries::pb::ShardRemoveRequest* request,
        ::aries::pb::AckResponse* response,
        ::google::protobuf::Closure* done) {
    // hold Closure done
    baidu::rpc::ClosureGuard done_guard(done);
    baidu::rpc::Controller* cntl =
            static_cast<baidu::rpc::Controller*>(controller);
    uint64_t log_id = cntl->log_id();

    uint64_t volume_id = request->volume_id();
    uint64_t vbid = request->vbid();

    ARIES_RPC_RECV_LOG(NOTICE) << " cmd:" << __FUNCTION__
            << " vid:" << volume_id << " vbid:" << vbid
            << " shard_index:" << request->shard_index()
            << " is_mark_delete:" << request->need_mark_delete()
            << " priority:" << request->priority()
            << " network_qos:" << request->network_qos();

    auto status = response->mutable_status();

    if (request->token() != FLAGS_token) {
        status->set_code(AIE_INVALID_TOKEN);
        status->set_msg("invalid token");
        ARIES_RPC_DONE_LOG(NOTICE) << " invalid token";
        return ;
    }

    if (FLAGS_readonly_mode && (request->need_mark_delete() == false)
            && (request->force() == false)) {
        status->set_code(AIE_READ_ONLY);
        status->set_msg("dn is read only");
        ARIES_RPC_DONE_LOG(NOTICE) << " refused due to dn read only mode";
        return;
    }

    if (vbid <= 0) {
        status->set_code(AIE_INVALID_ARGUMENT);
        status->set_msg("invalid vbid");
        ARIES_RPC_DONE_LOG(NOTICE) << " invalid vbid";
        return ;
    }

    VletManager* vlet_mgr = g_datanode->vlet_manager();
    VletPtr vlet_ptr = vlet_mgr->find(volume_id);

    if (!vlet_ptr) {
        // target vlet not found
        status->set_code(AIE_NOT_EXIST);
        status->set_msg("target vlet not found");
        ARIES_RPC_DONE_LOG(NOTICE) << " target vlet not found";
        return ;
    }

    RemoveContext* ctx = new RemoveContext();

    ctx->vlet_ptr = vlet_ptr;
    ctx->request = request;
    ctx->response = response;
    ctx->status = status;
    ctx->cntl = cntl;
    ctx->log_id = cntl->log_id();
    ctx->vid = volume_id;
    ctx->vbid = vbid;
    ctx->done_guard.reset(done_guard.release());
    ctx->en_queue_time = base::gettimeofday_ms();
    ctx->en_queue_time_us = base::gettimeofday_us();
    if (request->has_timeout_ms()) {
        ctx->timeout_ms = request->timeout_ms();
    } else {
        ctx->timeout_ms = FLAGS_call_timeout_ms;
    }
    if (request->has_priority()) {
        ctx->priority = request->priority();
    } else {
        ctx->priority = FLAGS_default_priority;
    }
    if (request->has_speed_coefficient()) {
        ctx->speed_coefficient = request->speed_coefficient();
    }
    ctx->action = REMOVE_BLOB_ACTION;
    base::get_leaky_singleton<IOScheduler>()->dispatch_task(static_cast<IoContext*>(ctx));
}

void DataNodeDataServiceImpl::restore(::google::protobuf::RpcController* controller,
        const ::aries::pb::ShardRestoreRequest* request,
        ::aries::pb::AckResponse* response,
        ::google::protobuf::Closure* done) {
    // hold Closure done
    baidu::rpc::ClosureGuard done_guard(done);
    baidu::rpc::Controller* cntl =
            static_cast<baidu::rpc::Controller*>(controller);
    uint64_t log_id = cntl->log_id();

    uint64_t volume_id = request->volume_id();
    uint64_t vbid = request->vbid();

    ARIES_RPC_RECV_LOG(NOTICE) << " cmd:" << __FUNCTION__
            << " vid:" << volume_id << " vbid:" << vbid
            << " shard_index:" << request->shard_index();

    auto status = response->mutable_status();

    if (request->token() != FLAGS_token) {
        status->set_code(AIE_INVALID_TOKEN);
        status->set_msg("invalid token");
        ARIES_RPC_DONE_LOG(NOTICE) << " invalid token";
        return ;
    }

    if (vbid <= 0) {
        status->set_code(AIE_INVALID_ARGUMENT);
        status->set_msg("invalid vbid");
        ARIES_RPC_DONE_LOG(NOTICE) << " invalid vbid";
        return ;
    }

    VletManager* vlet_mgr = g_datanode->vlet_manager();
    VletPtr vlet_ptr = vlet_mgr->find(volume_id);

    if (!vlet_ptr) {
        // target vlet not found
        status->set_code(AIE_NOT_EXIST);
        status->set_msg("target vlet not found");
        ARIES_RPC_DONE_LOG(NOTICE) << " target vlet not found";
        return ;
    }

    RestoreContext* ctx = new RestoreContext();

    ctx->vlet_ptr = vlet_ptr;
    ctx->request = request;
    ctx->response = response;
    ctx->status = status;
    ctx->cntl = cntl;
    ctx->log_id = cntl->log_id();
    ctx->vid = volume_id;
    ctx->vbid = vbid;
    ctx->done_guard.reset(done_guard.release());
    ctx->en_queue_time = base::gettimeofday_ms();
    ctx->en_queue_time_us = base::gettimeofday_us();
    if (request->has_timeout_ms()) {
        ctx->timeout_ms = request->timeout_ms();
    } else {
        ctx->timeout_ms = FLAGS_call_timeout_ms;
    }
    ctx->priority = request->priority();
    ctx->action = RESTORE_BLOB_ACTION;
    base::get_leaky_singleton<IOScheduler>()->dispatch_task(static_cast<IoContext*>(ctx));
}

void DataNodeDataServiceImpl::list_finger_print(::google::protobuf::RpcController* controller,
        const ::aries::pb::ListFingerPrintRequest* request,
        ::aries::pb::ListFingerPrintResponse* response,
        ::google::protobuf::Closure* done) {
    // hold Closure done
    baidu::rpc::ClosureGuard done_guard(done);
    baidu::rpc::Controller* cntl =
            static_cast<baidu::rpc::Controller*>(controller);
    uint64_t log_id = cntl->log_id();

    uint64_t volume_id = request->volume_id();
    uint32_t shard_index = request->shard_index();

    ARIES_RPC_RECV_LOG(NOTICE) << " cmd:" << __FUNCTION__
            << " vid:" << volume_id << " shard_index:" << shard_index;

    auto status = response->mutable_status();

    if (request->token() != FLAGS_token) {
        status->set_code(AIE_INVALID_TOKEN);
        status->set_msg("invalid token");
        ARIES_RPC_DONE_LOG(NOTICE) << " invalid token";
        return ;
    }

    VletManager* vlet_mgr = g_datanode->vlet_manager();
    VletPtr vlet_ptr = vlet_mgr->find(volume_id);

    if (!vlet_ptr) {
        // target vlet not found
        status->set_code(AIE_NOT_EXIST);
        status->set_msg("target vlet not found");
        ARIES_RPC_DONE_LOG(NOTICE) << " target vlet not found";
        return ;
    }

    if (vlet_ptr->shard_index() != shard_index) {
        // target vlet not found
        status->set_code(AIE_NOT_EXIST);
        status->set_msg("invalid shard_index");
        ARIES_RPC_DONE_LOG(NOTICE) << " invalid shard_index";
        return ;
    }

    ListFingerContext* ctx = new ListFingerContext();
    ctx->request = request;
    ctx->response = response;
    ctx->vlet_ptr = vlet_ptr;
    ctx->cntl = cntl;
    ctx->log_id = cntl->log_id();
    ctx->done_guard.reset(done_guard.release());
    ctx->nAction = LIST_FINGER_ACTION;
    ctx->timeout_ms = FLAGS_call_timeout_ms;
    ctx->en_queue_time = base::gettimeofday_ms();

    DiskAgentPtr disk_agent = vlet_ptr->disk_agent();
    disk_agent->heavy_worker()->dispatch(ctx);
}

void DataNodeDataServiceImpl::list_index(::google::protobuf::RpcController* controller,
        const ::aries::pb::ListIndexRequest* request,
        ::aries::pb::ListIndexResponse* response,
        ::google::protobuf::Closure* done) {
    // hold Closure done
    baidu::rpc::ClosureGuard done_guard(done);
    baidu::rpc::Controller* cntl =
            static_cast<baidu::rpc::Controller*>(controller);
    uint64_t log_id = cntl->log_id();

    uint64_t volume_id = request->volume_id();
    uint32_t shard_index = request->shard_index();

    ARIES_RPC_RECV_LOG(NOTICE) << " cmd:" << __FUNCTION__
            << " vid:" << volume_id << " shard_index:" << shard_index
            << " max_vbid:" << request->max_vbid();

    auto status = response->mutable_status();

    if (request->token() != FLAGS_token) {
        status->set_code(AIE_INVALID_TOKEN);
        status->set_msg("invalid token");
        ARIES_RPC_DONE_LOG(NOTICE) << " invalid token";
        return ;
    }
    // speed limit
    assert(g_list_index_token_pool);
    if (!g_list_index_token_pool->get(1)) {
        status->set_code(AIE_BUSY);
        status->set_msg("list index reach max qps");
        ARIES_RPC_DONE_LOG(NOTICE) << "list index reach max qps";
        return ;
    }

    VletManager* vlet_mgr = g_datanode->vlet_manager();
    VletPtr vlet_ptr = vlet_mgr->find(volume_id);

    if (!vlet_ptr) {
        // target vlet not found
        status->set_code(AIE_NOT_EXIST);
        status->set_msg("target vlet not found");
        ARIES_RPC_DONE_LOG(NOTICE) << " target vlet not found";
        return ;
    }

    if (vlet_ptr->shard_index() != shard_index) {
        // target vlet not found
        status->set_code(AIE_NOT_EXIST);
        status->set_msg("invalid shard_index");
        ARIES_RPC_DONE_LOG(NOTICE) << " invalid shard_index";
        return ;
    }

    ListIndexContext* ctx = new ListIndexContext();
    ctx->request = request;
    ctx->response = response;
    ctx->vlet_ptr = vlet_ptr;
    ctx->cntl = cntl;
    ctx->log_id = cntl->log_id();
    ctx->done_guard.reset(done_guard.release());
    ctx->nAction = LIST_INDEX_ACTION;
    ctx->timeout_ms = FLAGS_call_timeout_ms;
    ctx->en_queue_time = base::gettimeofday_ms();
    DiskAgentPtr    disk_agent = vlet_ptr->disk_agent();
    disk_agent->heavy_worker()->dispatch(ctx);
}

void DataNodeDataServiceImpl::get_location(::google::protobuf::RpcController* controller,
            const ::aries::pb::GetLocationRequest* request,
            ::aries::pb::GetLocationResponse* response,
            ::google::protobuf::Closure* done) {
    // hold Closure done
    baidu::rpc::ClosureGuard done_guard(done);
    baidu::rpc::Controller* cntl =
            static_cast<baidu::rpc::Controller*>(controller);
    uint64_t log_id = cntl->log_id();

    uint64_t volume_id = request->volume_id();
    uint32_t shard_index = request->shard_index();

    ARIES_RPC_RECV_LOG(NOTICE) << " cmd:" << __FUNCTION__
            << " vid:" << volume_id << " shard_index:" << shard_index
            << " max_vbid:" << request->max_vbid();

    auto status = response->mutable_status();

    if (request->token() != FLAGS_token) {
        status->set_code(AIE_INVALID_TOKEN);
        status->set_msg("invalid token");
        ARIES_RPC_DONE_LOG(NOTICE) << " invalid token";
        return ;
    }

    VletManager* vlet_mgr = g_datanode->vlet_manager();
    VletPtr vlet_ptr = vlet_mgr->find(volume_id);

    if (!vlet_ptr) {
        // target vlet not found
        status->set_code(AIE_NOT_EXIST);
        status->set_msg("target vlet not found");
        ARIES_RPC_DONE_LOG(NOTICE) << " target vlet not found get location";
        return;
    }

    if (vlet_ptr->shard_index() != shard_index) {
        // target vlet not found
        status->set_code(AIE_NOT_EXIST);
        status->set_msg("invalid shard_index");
        ARIES_RPC_DONE_LOG(NOTICE) << " invalid shard_index";
        return ;
    }

    GetSlotLocationContext* ctx = new GetSlotLocationContext();
    ctx->request = request;
    ctx->response = response;
    ctx->vlet_ptr = vlet_ptr;
    ctx->cntl = cntl;
    ctx->log_id = cntl->log_id();
    ctx->done_guard.reset(done_guard.release());
    ctx->nAction = GET_SLOT_LOCATION_ACTION;
    DiskAgentPtr disk_agent = vlet_ptr->disk_agent();
    disk_agent->heavy_worker()->dispatch(ctx);
}

void DataNodeDataServiceImpl::list_vlet_info(::google::protobuf::RpcController* controller,
        const ::aries::pb::ListVletInfoRequest* request,
        ::aries::pb::ListVletInfoResponse* response,
        ::google::protobuf::Closure* done) {

    baidu::rpc::ClosureGuard done_guard(done);
    baidu::rpc::Controller* cntl =
            static_cast<baidu::rpc::Controller*>(controller);
    uint64_t log_id = cntl->log_id();
    auto status = response->mutable_status();

    ARIES_RPC_RECV_LOG(NOTICE) << " cmd:" << __FUNCTION__;

    if (request->token() != FLAGS_token) {
        status->set_code(AIE_INVALID_TOKEN);
        status->set_msg("invalid token");
        ARIES_RPC_DONE_LOG(NOTICE) << " invalid token";
        return ;
    }

    VletManager* vlet_mgr = g_datanode->vlet_manager();
    std::vector<VletPtr> vlet_list;
    vlet_mgr->list_vlet(&vlet_list);

    uint32_t vlet_num = vlet_list.size();
    if (!vlet_num) {
        return ;
    }

    // Note: vlet->fill_vlet_info() is memory reference only and should be fast
    // so we do not dispatch it to IOExecutor and execute by current bthread.
    aries::pb::GetVletInfoResponse vlet_info_list;
    for (size_t index = 0; index < vlet_list.size(); ++index) {
        Vlet* vlet_ptr = vlet_list[index].get();
        auto vlet_info = vlet_info_list.add_vlet_info_list();
        vlet_ptr->fill_vlet_info(vlet_info);
    }

    for (int i = 0; i < vlet_info_list.vlet_info_list_size(); ++i) {
        auto& vlet_info = vlet_info_list.vlet_info_list(i);
        if (vlet_info.volume_id() != 0) {
            auto vlet_ret = response->add_vlet_info_list();
            vlet_ret->set_volume_id(vlet_info.volume_id());
            vlet_ret->set_shard_index(vlet_info.shard_index());
            vlet_ret->set_vlet_version(vlet_info.vlet_version());
            vlet_ret->set_create_time(vlet_info.create_time());
            vlet_ret->set_vlet_type((aries::VletType)vlet_info.vlet_type());
            vlet_ret->set_disk_id(vlet_info.disk_id());
        }
    }
}

void DataNodeDataServiceImpl::get_node_info(::google::protobuf::RpcController* controller,
        const ::aries::pb::GetNodeInfoRequest* request,
        ::aries::pb::GetNodeInfoResponse* response,
        ::google::protobuf::Closure* done) {
    // hold Closure done
    baidu::rpc::ClosureGuard done_guard(done);
    baidu::rpc::Controller* cntl =
            static_cast<baidu::rpc::Controller*>(controller);
    uint64_t log_id = cntl->log_id();
    base::EndPoint req_addr = common::int2endpoint(request->req_addr());

    ARIES_RPC_RECV_LOG(NOTICE) << " cmd:" << __FUNCTION__
            << " req_addr:" << common::endpoint2str(req_addr);

    auto status = response->mutable_status();

    if (request->token() != FLAGS_token) {
        status->set_code(AIE_INVALID_TOKEN);
        status->set_msg("invalid token");
        ARIES_RPC_DONE_LOG(NOTICE) << " invalid token";
        return ;
    }
    //get disk info
    DiskManager* disk_mgr = g_datanode->disk_manager();
    const auto& disk_agent_map = disk_mgr->disk_agent_map();
    uint32_t disk_num = disk_agent_map.size();

    std::vector<ShowDiskContext> disk_ctx_list;
    disk_ctx_list.resize(disk_num);

    uint32_t i = 0;
    common::SyncPoint disk_sync_point(disk_num);
    for (auto& kv : disk_agent_map) {
        auto& ctx = disk_ctx_list[i++];
        ctx.sync_point = &disk_sync_point;
        QueueExec(SHOW_DISK_ACTION, kv.second, &ctx);
    }
    disk_sync_point.wait();
    int used_disk_num = 0;
    uint64_t disk_total_size = 0;
    uint64_t disk_free_size = 0;
    uint64_t aries_capacity = 0;

    for (auto& disk_ctx : disk_ctx_list) {
        if (disk_ctx.is_used) {
            disk_total_size += disk_ctx.disk_total_size;
            disk_free_size += disk_ctx.disk_free_size;
            aries_capacity += disk_ctx.aries_capacity;
            used_disk_num++;
        }
    }

    // get vlet info
    VletManager* vlet_mgr = g_datanode->vlet_manager();
    std::vector<VletPtr> vlet_list;
    vlet_mgr->list_vlet(&vlet_list);
    uint32_t vlet_num = vlet_list.size();
    aries::pb::GetVletInfoResponse vlet_info_list;
    for (size_t index = 0; index < vlet_num; ++index) {
        Vlet* vlet_ptr = vlet_list[i].get();
        auto vlet_info = vlet_info_list.add_vlet_info_list();
        vlet_ptr->fill_vlet_info(vlet_info);
    }

    uint64_t vlet_blob_num = 0;
    uint64_t vlet_total_size = 0;
    uint64_t vlet_free_size = 0;

    for (int i = 0; i < vlet_info_list.vlet_info_list_size(); ++i) {
        auto& vlet_info = vlet_info_list.vlet_info_list(i);
        if (vlet_info.volume_id() != 0) { 
            vlet_blob_num += vlet_info.blob_num();
            vlet_total_size += vlet_info.total_size();
            vlet_free_size += vlet_info.free_size();
        } else {
            --vlet_num;
        }
    }

    response->set_total_disk_num(disk_num);
    response->set_used_disk_num(used_disk_num);
    response->set_disk_total_size(disk_total_size);
    response->set_disk_free_size(disk_free_size);
    response->set_aries_capacity(aries_capacity);
    response->set_vlet_total_size(vlet_total_size);
    response->set_vlet_total_num(vlet_num);
    response->set_vlet_free_size(vlet_free_size);
    response->set_vlet_blob_num(vlet_blob_num);
}

void DataNodeDataServiceImpl::get_vlet_info(::google::protobuf::RpcController* controller,
        const ::aries::pb::GetVletInfoRequest* request,
        ::aries::pb::GetVletInfoResponse* response,
        ::google::protobuf::Closure* done) {
    // hold Closure done
    baidu::rpc::ClosureGuard done_guard(done);
    baidu::rpc::Controller* cntl =
            static_cast<baidu::rpc::Controller*>(controller);
    uint64_t log_id = cntl->log_id();

    uint64_t volume_id = request->volume_id();
    uint32_t shard_index = request->shard_index();

    auto start = base::gettimeofday_ms();
    ARIES_RPC_RECV_LOG(NOTICE) << " cmd:" << __FUNCTION__
            << " vid:" << volume_id << " shard_index:" << shard_index;
    SCOPE_EXIT {
         auto cost = base::gettimeofday_ms() - start;
         ARIES_RPC_RECV_LOG(NOTICE) << " cmd:" << __FUNCTION__
           << "finish vid:" << volume_id << " shard_index:" << shard_index << " cost_ms:" << cost;
    };

    auto status = response->mutable_status();

    if (request->token() != FLAGS_token) {
        status->set_code(AIE_INVALID_TOKEN);
        status->set_msg("invalid token");
        ARIES_RPC_DONE_LOG(NOTICE) << " invalid token";
        return ;
    }

    aries::pb::GetVletInfoResponse vlet_info_list;
    if (volume_id > 0) {
        VletManager* vlet_mgr = g_datanode->vlet_manager();
        VletPtr vlet_ptr = vlet_mgr->find(volume_id);

        if (!vlet_ptr) {
            // target vlet not found
            status->set_code(AIE_NOT_EXIST);
            status->set_msg("target vlet not found");
            ARIES_RPC_DONE_LOG(NOTICE) << " target vlet not found";
            return ;
        }

        if (vlet_ptr->shard_index() != shard_index) {
            // target vlet not found
            status->set_code(AIE_NOT_EXIST);
            status->set_msg("invalid shard_index");
            ARIES_RPC_DONE_LOG(NOTICE) << " invalid shard_index";
            return ;
        }

        auto vlet_info = vlet_info_list.add_vlet_info_list();
        vlet_ptr->fill_vlet_info(vlet_info);

    } else {
        VletManager* vlet_mgr = g_datanode->vlet_manager();
        std::vector<VletPtr> vlet_list;
        vlet_mgr->list_vlet(&vlet_list);
        auto after_list_vlet = base::gettimeofday_ms();
        ARIES_RPC_RECV_LOG(NOTICE) << " cmd:" << __FUNCTION__ << "after list_vlet vid:" << volume_id
                        << " shard_index:" << shard_index << " cost_ms:" << after_list_vlet - start;

        uint32_t vlet_num = vlet_list.size();
        if (!vlet_num) {
            return ;
        }

        for (size_t index = 0; index < vlet_num; ++index) {
            Vlet* vlet_ptr = vlet_list[index].get();
            auto vlet_info = vlet_info_list.add_vlet_info_list();
            vlet_ptr->fill_vlet_info(vlet_info);
        }
        auto after_fill_vlet = base::gettimeofday_ms();
        ARIES_RPC_RECV_LOG(NOTICE) << " cmd:" << __FUNCTION__ << "after fill vlet vid:" << volume_id
                                   << " shard_index:" << shard_index << " cost_ms:" << after_fill_vlet - after_list_vlet;
    }

    for (int i = 0; i < vlet_info_list.vlet_info_list_size(); ++i) {
        auto& vlet_info = vlet_info_list.vlet_info_list(i);
        if (vlet_info.volume_id() != 0) {
            auto vlet_ret = response->add_vlet_info_list();
            vlet_ret->CopyFrom(vlet_info);
        }
    }
}

void DataNodeDataServiceImpl::copy_append_vlet(::google::protobuf::RpcController* controller,
        const ::aries::pb::CopyAppendVletRequest* request,
        ::aries::pb::CopyAppendVletResponse* response,
        ::google::protobuf::Closure* done) {
    // hold Closure done
    baidu::rpc::ClosureGuard done_guard(done);
    baidu::rpc::Controller* cntl =
            static_cast<baidu::rpc::Controller*>(controller);
    uint64_t log_id = cntl->log_id();

    uint64_t volume_id = request->volume_id();
    uint32_t shard_index = request->shard_index();

    ARIES_RPC_RECV_LOG(NOTICE) << " cmd:" << __FUNCTION__
            << " vid:" << volume_id << " shard_index:" << shard_index;
    auto status = response->mutable_status();

    if (request->token() != FLAGS_token) {
        status->set_code(AIE_INVALID_TOKEN);
        status->set_msg("invalid token");
        ARIES_RPC_DONE_LOG(NOTICE) << " invalid token";
        return ;
    }

    VletManager* vlet_mgr = g_datanode->vlet_manager();
    VletPtr vlet_ptr = vlet_mgr->find(volume_id);

    if (!vlet_ptr) {
        // target vlet not found
        status->set_code(AIE_NOT_EXIST);
        status->set_msg("target vlet not found");
        ARIES_RPC_DONE_LOG(NOTICE) << " target vlet not found";
        return ;
    }

    if (vlet_ptr->shard_index() != shard_index) {
        // target vlet not found
        status->set_code(AIE_NOT_EXIST);
        status->set_msg("invalid shard_index");
        ARIES_RPC_DONE_LOG(NOTICE) << " invalid shard_index";
        return ;
    }

    CopyVletContext* ctx = new CopyVletContext();

    ctx->vlet_ptr = vlet_ptr;
    ctx->request = request;
    ctx->response = response;
    ctx->status = status;
    ctx->cntl = cntl;
    ctx->log_id = cntl->log_id();
    ctx->vid = volume_id;
    ctx->done_guard.reset(done_guard.release());
    ctx->vlet_type = ENGINE_APPEND;
    if (request->has_timeout_ms()) {
        ctx->timeout_ms = request->timeout_ms();
    } else {
        ctx->timeout_ms = FLAGS_call_timeout_ms;
    }

    ctx->priority = request->priority();
    ctx->en_queue_time = base::gettimeofday_ms();
    ctx->en_queue_time_us = base::gettimeofday_us();
    ctx->nAction = static_cast<int>(COPY_VLET_ACTION);
    // Note: copy append vlet action may traverse db index and read block data,
    // which may slow down IOExecutor worker threads, so we dispatch it to HeavyWorker.
    DiskAgentPtr disk_agent = vlet_ptr->disk_agent();
    ctx->disk_id = disk_agent->disk_id();
    disk_agent->heavy_worker()->dispatch(ctx);
}

void DataNodeDataServiceImpl::copy_linked_vlet(::google::protobuf::RpcController* controller,
        const ::aries::pb::CopyLinkedVletRequest* request,
        ::aries::pb::CopyLinkedVletResponse* response,
        ::google::protobuf::Closure* done) {
    // hold Closure done
    baidu::rpc::ClosureGuard done_guard(done);
    baidu::rpc::Controller* cntl =
            static_cast<baidu::rpc::Controller*>(controller);
    uint64_t log_id = cntl->log_id();

    uint64_t volume_id = request->volume_id();
    uint32_t shard_index = request->shard_index();

    ARIES_RPC_RECV_LOG(NOTICE) << " cmd:" << __FUNCTION__
            << " vid:" << volume_id << " shard_index:" << shard_index;
    auto status = response->mutable_status();

    if (request->token() != FLAGS_token) {
        status->set_code(AIE_INVALID_TOKEN);
        status->set_msg("invalid token");
        ARIES_RPC_DONE_LOG(NOTICE) << " invalid token";
        return ;
    }

    VletManager* vlet_mgr = g_datanode->vlet_manager();
    VletPtr vlet_ptr = vlet_mgr->find(volume_id);

    if (!vlet_ptr) {
        // target vlet not found
        status->set_code(AIE_NOT_EXIST);
        status->set_msg("target vlet not found");
        ARIES_RPC_DONE_LOG(NOTICE) << " target vlet not found";
        return ;
    }

    if (vlet_ptr->shard_index() != shard_index) {
        // target vlet not found
        status->set_code(AIE_NOT_EXIST);
        status->set_msg("invalid shard_index");
        ARIES_RPC_DONE_LOG(NOTICE) << " invalid shard_index";
        return ;
    }

    CopyVletContext* ctx = new CopyVletContext();

    ctx->vlet_ptr = vlet_ptr;
    ctx->request = request;
    ctx->response = response;
    ctx->status = status;
    ctx->cntl = cntl;
    ctx->log_id = cntl->log_id();
    ctx->vid = volume_id;
    ctx->done_guard.reset(done_guard.release());
    ctx->vlet_type = ENGINE_LINKED;
    if (request->has_timeout_ms()) {
        ctx->timeout_ms = request->timeout_ms();
    } else {
        ctx->timeout_ms = FLAGS_call_timeout_ms;
    }

    ctx->priority = request->priority();
    ctx->en_queue_time = base::gettimeofday_ms();
    ctx->en_queue_time_us = base::gettimeofday_us();
    ctx->nAction = static_cast<int>(COPY_VLET_ACTION);
    // Note: copy linked vlet action may read block data, which may
    // slow down IOExecutor worker threads, so we dispatch it to HeavyWorker.
    DiskAgentPtr disk_agent = vlet_ptr->disk_agent();
    ctx->disk_id = disk_agent->disk_id();
    disk_agent->heavy_worker()->dispatch(ctx);
}

void DataNodeDataServiceImpl::copy_zone_vlet(::google::protobuf::RpcController* controller,
        const ::aries::pb::CopyZoneVletRequest* request,
        ::aries::pb::CopyZoneVletResponse* response,
        ::google::protobuf::Closure* done) {
    // hold Closure done
    baidu::rpc::ClosureGuard done_guard(done);
    baidu::rpc::Controller* cntl =
            static_cast<baidu::rpc::Controller*>(controller);
    uint64_t log_id = cntl->log_id();

    uint64_t volume_id = request->volume_id();
    uint32_t shard_index = request->shard_index();

    ARIES_RPC_RECV_LOG(NOTICE) << " cmd:" << __FUNCTION__
            << " vid:" << volume_id << " shard_index:" << shard_index;
    auto status = response->mutable_status();

    if (request->token() != FLAGS_token) {
        status->set_code(AIE_INVALID_TOKEN);
        status->set_msg("invalid token");
        ARIES_RPC_DONE_LOG(NOTICE) << " invalid token";
        return ;
    }

    VletManager* vlet_mgr = g_datanode->vlet_manager();
    VletPtr vlet_ptr = vlet_mgr->find(volume_id);

    if (!vlet_ptr) {
        // target vlet not found
        status->set_code(AIE_NOT_EXIST);
        status->set_msg("target vlet not found");
        ARIES_RPC_DONE_LOG(NOTICE) << " target vlet not found";
        return ;
    }

    if (vlet_ptr->shard_index() != shard_index) {
        // target vlet not found
        status->set_code(AIE_NOT_EXIST);
        status->set_msg("invalid shard_index");
        ARIES_RPC_DONE_LOG(NOTICE) << " invalid shard_index";
        return ;
    }

    DiskAgentPtr disk_agent = vlet_ptr->disk_agent();
    if (FLAGS_enable_ssd_flow_control) {
        if (disk_agent) {
            auto flow_control = disk_agent->flow_control();
            if (!flow_control->try_acquire_avg_record_size(FLAGS_balance_batch_get_record_num, OTHERS_FLOW)) {
                status->set_code(AIE_BUSY);
                status->set_msg("get throughput reached limit");
                ARIES_RPC_DONE_LOG(NOTICE) << " get throughput reached limit";
                return;
            }
        }
    }

    CopyVletContext* ctx = new CopyVletContext();

    ctx->vlet_ptr = vlet_ptr;
    ctx->request = request;
    ctx->response = response;
    ctx->status = status;
    ctx->cntl = cntl;
    ctx->log_id = cntl->log_id();
    ctx->vid = volume_id;
    ctx->done_guard.reset(done_guard.release());
    ctx->vlet_type = ENGINE_ZONE;
    if (request->has_timeout_ms()) {
        ctx->timeout_ms = request->timeout_ms();
    } else {
        ctx->timeout_ms = FLAGS_call_timeout_ms;
    }

    ctx->priority = request->priority();
    ctx->en_queue_time = base::gettimeofday_ms();
    ctx->en_queue_time_us = base::gettimeofday_us();
    ctx->nAction = static_cast<int>(COPY_VLET_ACTION);    
    // Note: copy zone vlet action may traverse db index and read block data,
    // which may slow down IOExecutor worker threads, so we dispatch it to HeavyWorker.
    ctx->disk_id = disk_agent->disk_id();
    disk_agent->heavy_worker()->dispatch(ctx);
}

void DataNodeDataServiceImpl::list_blob(::google::protobuf::RpcController* controller,
        const ::aries::pb::ListBlobRequest* request,
        ::aries::pb::ListBlobResponse* response,
        ::google::protobuf::Closure* done) {
    // hold Closure done
    baidu::rpc::ClosureGuard done_guard(done);
    baidu::rpc::Controller* cntl =
            static_cast<baidu::rpc::Controller*>(controller);
    uint64_t log_id = cntl->log_id();

    uint64_t volume_id = request->volume_id();
    uint32_t shard_index = request->shard_index();

    ARIES_RPC_RECV_LOG(NOTICE) << " cmd:" << __FUNCTION__
            << " vid:" << volume_id << " shard_index:" << shard_index;

    auto status = response->mutable_status();

    if (request->token() != FLAGS_token) {
        status->set_code(AIE_INVALID_TOKEN);
        status->set_msg("invalid token");
        ARIES_RPC_DONE_LOG(NOTICE) << " invalid token";
        return ;
    }

    VletManager* vlet_mgr = g_datanode->vlet_manager();
    VletPtr vlet_ptr = vlet_mgr->find(volume_id);

    if (!vlet_ptr) {
        // target vlet not found
        status->set_code(AIE_NOT_EXIST);
        status->set_msg("target vlet not found");
        ARIES_RPC_DONE_LOG(NOTICE) << " target vlet not found";
        return ;
    }

    if (vlet_ptr->shard_index() != shard_index) {
        // target vlet not found
        status->set_code(AIE_NOT_EXIST);
        status->set_msg("invalid shard_index");
        ARIES_RPC_DONE_LOG(NOTICE) << " invalid shard_index";
        return ;
    }

    ListBlobContext* ctx = new ListBlobContext();
    ctx->request = request;
    ctx->response = response;
    ctx->vlet_ptr = vlet_ptr;
    ctx->cntl = cntl;
    ctx->log_id = cntl->log_id();
    ctx->done_guard.reset(done_guard.release());
    ctx->nAction = LIST_BLOB_ACTION;
    DiskAgentPtr disk_agent = vlet_ptr->disk_agent();
    disk_agent->heavy_worker()->dispatch(ctx);
}

void DataNodeDataServiceImpl::check_vlet_engine(::google::protobuf::RpcController* controller,
        const ::aries::pb::CheckVletEngineRequest* request,
        ::aries::pb::AckResponse* response,
        ::google::protobuf::Closure* done) {
    // hold Closure done
    baidu::rpc::ClosureGuard done_guard(done);
    baidu::rpc::Controller* cntl =
            static_cast<baidu::rpc::Controller*>(controller);
    uint64_t log_id = cntl->log_id();

    uint64_t volume_id = request->volume_id();
    uint32_t shard_index = request->shard_index();

    ARIES_RPC_RECV_LOG(NOTICE) << " cmd:" << __FUNCTION__
            << " vid:" << volume_id << " shard_index:" << shard_index;

    auto status = response->mutable_status();

    if (request->token() != FLAGS_token) {
        status->set_code(AIE_INVALID_TOKEN);
        status->set_msg("invalid token");
        ARIES_RPC_DONE_LOG(NOTICE) << " invalid token";
        return ;
    }

    VletManager* vlet_mgr = g_datanode->vlet_manager();
    VletPtr vlet_ptr = vlet_mgr->find(volume_id);

    if (!vlet_ptr) {
        // target vlet not found
        status->set_code(AIE_NOT_EXIST);
        status->set_msg("target vlet not found");
        ARIES_RPC_DONE_LOG(NOTICE) << " target vlet not found";
        return ;
    }

    if (vlet_ptr->shard_index() != shard_index) {
        // target vlet not found
        status->set_code(AIE_NOT_EXIST);
        status->set_msg("invalid shard_index");
        ARIES_RPC_DONE_LOG(NOTICE) << " invalid shard_index";
        return ;
    }

    CheckVletEngineContext *ctx = new CheckVletEngineContext;
    ctx->vlet_ptr = vlet_ptr;
    ctx->done_guard.reset(done_guard.release());
    ctx->cntl = cntl;
    ctx->log_id = cntl->log_id();
    ctx->response = response;
    ctx->nAction = CHECK_ENGINE_ACTION;
    DiskAgentPtr disk_agent = vlet_ptr->disk_agent();
    disk_agent->heavy_worker()->dispatch(ctx);
}

void DataNodeDataServiceImpl::show_disk(::google::protobuf::RpcController* controller,
        const ::aries::pb::ShowDiskRequest* request,
        ::aries::pb::ShowDiskResponse* response,
        ::google::protobuf::Closure* done) {
    // hold Closure done
    auto status = response->mutable_status();
    baidu::rpc::Controller* cntl =
        static_cast<baidu::rpc::Controller*>(controller);

    RpcGuard done_guard(__FUNCTION__, cntl, done, request, status);

    if (request->token() != FLAGS_token) {
        status->set_code(AIE_INVALID_TOKEN);
        status->set_msg("invalid token");
        return ;
    }

    DiskManager* disk_mgr = g_datanode->disk_manager();
    uint32_t disk_id = request->disk_id();

    // check disk
    DiskAgentPtr disk_agent = disk_mgr->find(disk_id);
    if (!disk_agent) {
        status->set_code(AIE_NOT_EXIST);
        status->set_msg("disk not exist");
        return ;
    }

    if (!disk_agent->is_used()) {
        status->set_code(AIE_OK);
        status->set_msg("disk already dropped");
        return ;
    }

    common::SyncPoint sync_point(1);
    ShowDiskContext context;
    context.disk_conf = response->mutable_disk_conf();
    context.sync_point = &sync_point;

    QueueExec(SHOW_DISK_ACTION, disk_agent, &context);

    sync_point.wait();
}

#if defined(_CHECK_TEST) || defined(_UNIT_TEST)
void DataNodeDataServiceImpl::change_shard(::google::protobuf::RpcController* controller,
        const ::aries::pb::ChangeShardRequest* request,
        ::aries::pb::AckResponse* response,
        ::google::protobuf::Closure* done) {
    // hold Closure done
    baidu::rpc::ClosureGuard done_guard(done);
    baidu::rpc::Controller* cntl =
            static_cast<baidu::rpc::Controller*>(controller);
    uint64_t log_id = cntl->log_id();

    uint64_t volume_id = request->vid();
    uint32_t shard_index = request->shard_index();

    ARIES_RPC_RECV_LOG(NOTICE) << " cmd:" << __FUNCTION__
            << " vid:" << volume_id << " shard_index:" << shard_index;

    auto status = response->mutable_status();

    if (request->token() != FLAGS_token) {
        status->set_code(AIE_INVALID_TOKEN);
        status->set_msg("invalid token");
        ARIES_RPC_DONE_LOG(NOTICE) << " invalid token";
        return ;
    }

    VletManager* vlet_mgr = g_datanode->vlet_manager();
    VletPtr vlet_ptr = vlet_mgr->find(volume_id);

    if (!vlet_ptr) {
        // target vlet not found
        status->set_code(AIE_NOT_EXIST);
        status->set_msg("target vlet not found");
        ARIES_RPC_DONE_LOG(NOTICE) << " target vlet not found";
        return ;
    }

    if (vlet_ptr->shard_index() != shard_index) {
        // target vlet not found
        status->set_code(AIE_NOT_EXIST);
        status->set_msg("invalid shard_index");
        ARIES_RPC_DONE_LOG(NOTICE) << " invalid shard_index";
        return ;
    }

    ChangeShardContext* ctx = new ChangeShardContext;
    ctx->vid = volume_id;
    ctx->vbid = request->vbid();
    ctx->op_code = request->op();
    ctx->vlet_ptr = vlet_ptr;
    ctx->cntl = cntl;
    ctx->log_id = cntl->log_id();
    ctx->res = response;
    ctx->done_guard.reset(done_guard.release());
    ctx->status = status;
    ctx->en_queue_time = base::gettimeofday_ms();
    ctx->en_queue_time_us = base::gettimeofday_us();
    ctx->timeout_ms = FLAGS_call_timeout_ms;
    ctx->priority = FLAGS_default_priority;
    ctx->action = CHANGE_SHARD_ACTION;
    std::shared_ptr<common::SyncPoint> sync_point(new common::SyncPoint(1));
    ctx->sync_point = sync_point;

    base::get_leaky_singleton<IOScheduler>()->dispatch_task(static_cast<IoContext*>(ctx));
    sync_point->wait();
}
#endif

void DataNodeDataServiceImpl::check_shard(::google::protobuf::RpcController* controller,
        const ::aries::pb::CheckVletShardsRequest* request,
        ::aries::pb::AckResponse* response,
        ::google::protobuf::Closure* done) {
    // hold Closure done
    baidu::rpc::ClosureGuard done_guard(done);
    baidu::rpc::Controller* cntl =
            static_cast<baidu::rpc::Controller*>(controller);
    uint64_t log_id = request->log_id();

    uint64_t volume_id = request->volume_id();
    uint32_t shard_index = request->shard_index();
    ARIES_RPC_RECV_LOG(NOTICE) << " cmd:" << __FUNCTION__
            << " vid:" << volume_id << " shard_index:" << shard_index;

    auto status = response->mutable_status();

    if (request->token() != FLAGS_token) {
        status->set_code(AIE_INVALID_TOKEN);
        status->set_msg("invalid token");
        ARIES_RPC_DONE_LOG(NOTICE) << " invalid token";
        return;
    }

    VletManager* vlet_mgr = g_datanode->vlet_manager();
    VletPtr vlet_ptr = vlet_mgr->find(volume_id);

    if (!vlet_ptr) {
        // target vlet not found
        status->set_code(AIE_NOT_EXIST);
        status->set_msg("target vlet not found");
        ARIES_RPC_DONE_LOG(NOTICE) << " target vlet not found";
        return;
    }

    if (vlet_ptr->shard_index() != shard_index) {
        // target vlet not found
        status->set_code(AIE_NOT_EXIST);
        status->set_msg("invalid shard_index");
        ARIES_RPC_DONE_LOG(NOTICE) << " invalid shard_index";
        return;
    }

    ShardCheckContext* ctx = new ShardCheckContext;
    ::aries::pb::CheckVletShardsRequest* check_shard_request = new ::aries::pb::CheckVletShardsRequest;
    check_shard_request->CopyFrom(*request);
    ctx->en_queue_time = base::gettimeofday_ms();
    ctx->en_queue_time_us = base::gettimeofday_us();
    if (request->has_timeout_ms()) {
        ctx->timeout_ms = request->timeout_ms();
    } else {
        ctx->timeout_ms = FLAGS_call_timeout_ms;
    }
    ctx->request.reset(check_shard_request);
    ctx->response = response;
    ctx->report = std::make_shared<::aries::pb::ReportCheckVletShardsRequest>();
    ctx->report->set_uuid(ctx->request->uuid());
    ctx->report->set_shard_index(ctx->request->shard_index());
    ctx->report->set_volume_id(ctx->request->volume_id());
    ctx->report->set_log_id(ctx->request->log_id());
    ctx->vlet_ptr = vlet_ptr;
    std::shared_ptr<common::SyncPoint> sync_point(new common::SyncPoint(1));
    ctx->sync_point = sync_point;

    DiskAgentPtr disk_agent = vlet_ptr->disk_agent();
    QueueExec(CHECK_SHARD_ACTION, disk_agent, ctx);
    sync_point->wait();
}


void DataNodeDataServiceImpl::purge_vlet(::google::protobuf::RpcController* controller,
        const ::aries::pb::PurgeVletRequest* request,
        ::aries::pb::AckResponse* response,
        ::google::protobuf::Closure* done) {
    // hold Closure done
    baidu::rpc::ClosureGuard done_guard(done);
    baidu::rpc::Controller* cntl =
        static_cast<baidu::rpc::Controller*>(controller);
    uint64_t log_id = cntl->log_id();

    uint64_t volume_id = request->volume_id();

    ARIES_RPC_RECV_LOG(NOTICE) << " cmd:" << __FUNCTION__
        << " vid:" << volume_id << " shard_index:" << request->shard_index();

    auto status = response->mutable_status();

    if (request->token() != FLAGS_token) {
        status->set_code(AIE_INVALID_TOKEN);
        status->set_msg("invalid token");
        ARIES_RPC_DONE_LOG(NOTICE) << " invalid token";
        return;
    }

    VletManager* vlet_mgr = g_datanode->vlet_manager();
    VletPtr vlet_ptr = vlet_mgr->find(volume_id);

    if (!vlet_ptr || (int)vlet_ptr->shard_index() != request->shard_index()) {
        // target vlet not found
        status->set_code(AIE_NOT_EXIST);
        status->set_msg("target vlet not found");
        ARIES_RPC_DONE_LOG(NOTICE) << " target vlet not found";
        return;
    }

    PurgeContext* ctx = new PurgeContext();

    ctx->vlet_ptr = vlet_ptr;
    ctx->request = request;
    ctx->response = response;
    ctx->status = status;
    ctx->cntl = cntl;
    ctx->log_id = cntl->log_id();
    ctx->vid = volume_id;
    ctx->done_guard.reset(done_guard.release());
    ctx->en_queue_time = base::gettimeofday_ms();
    ctx->en_queue_time_us = base::gettimeofday_us();
    ctx->timeout_ms = FLAGS_call_timeout_ms;
    ctx->priority = aries::VERY_HIGH;
    ctx->purge_blob_without_ttl = true;
    ctx->action = PURGE_VLET_ACTION;
    base::get_leaky_singleton<IOScheduler>()->dispatch_task(static_cast<IoContext*>(ctx));
}

int DataNodeDataServiceImpl::do_cache_get(const ::aries::pb::ShardGetRequest* request,
                                          ::aries::pb::ShardGetResponse* response,
                                          baidu::rpc::Controller* cntl,
                                          google::protobuf::Closure* done,
                                          VletPtr vlet_ptr) {
    auto* cache_manager = g_datanode->cache_manager();
    if (!FLAGS_enable_cache || cache_manager == nullptr || !cache_manager->enabled()) {
        response->mutable_status()->set_code(AIE_FAIL);
        response->mutable_status()->set_msg("cache is not enabled or not started");
        return AIE_FAIL;
    }

    uint64_t volume_id = request->volume_id();
    uint64_t vbid = request->vbid();
    aries::Qos priority = (aries::Qos)FLAGS_default_priority;
    if (request->has_priority()) {
        priority = request->priority();
    }

    auto* ctx = new GetFromCacheContext();
    ctx->key = ::aries::make_bid(volume_id, vbid);
    ctx->vlet_ptr = std::move(vlet_ptr);
    ctx->request = request;
    ctx->response = response;
    ctx->status = response->mutable_status();
    ctx->cntl = cntl;
    ctx->log_id = cntl->log_id();
    ctx->vid = volume_id;
    ctx->vbid = vbid;
    ctx->done_guard.reset(done);
    ctx->en_queue_time = base::gettimeofday_ms();
    ctx->en_queue_time_us = base::gettimeofday_us();
    if (request->has_timeout_ms()) {
        ctx->timeout_ms = request->timeout_ms();
    } else {
        ctx->timeout_ms = FLAGS_call_timeout_ms;
    }
    ctx->priority = priority;

    int ret = cache_manager->dispatch_get_task(ctx);
    if (ret != AIE_OK) {
        ctx->done_guard.release();
        ctx->status = nullptr;
        delete ctx;
    }
    return ret;
}

} // end namespace of datanode
} // end namespace of aries

/* vim: set ts=4 sw=4 sts=4 tw=100 */

