// Copyright (c) 2016 Baidu.com, Inc. All Rights Reserved
// Author <PERSON><PERSON><PERSON><PERSON><PERSON>(chen<PERSON><PERSON><PERSON>@baidu.com)
// Date: Sat Oct  8 15:32:10 CST 2016

#include "baidu/inf/aries/datanode/service/control_service_impl.h"
#include "baidu/inf/aries/datanode/datanode.h"
#include "baidu/inf/aries/datanode/util/util.h"
#include "baidu/inf/aries/datanode/flags.h"
#include "baidu/inf/aries/datanode/disk_agent.h"
#include "baidu/inf/aries/datanode/copy_vlet.h"
#include "baidu/inf/aries/datanode/vlet/lsm_vlet.h"
#include "baidu/inf/aries/datanode/vlet/linked_vlet.h"
#include "baidu/inf/aries/datanode/io_scheduler.h"
#include "baidu/inf/aries/common/common.h"
#include "baidu/inf/aries/common/sync_point.h"
#include "base/memory/scoped_ptr.h"
#include "baidu/rpc/server.h"
#include <memory>
#include <sys/types.h>
#include <sys/stat.h>
#include <unistd.h>

namespace aries {
namespace datanode {

void DataNodeControlServiceImpl::create_vlet(::google::protobuf::RpcController* controller,
        const ::aries::pb::CreateVletRequest* request,
        ::aries::pb::AckResponse* response,
        ::google::protobuf::Closure* done) {
    // hold Closure done
    auto status = response->mutable_status();
    baidu::rpc::Controller* cntl =
        static_cast<baidu::rpc::Controller*>(controller);

    RpcGuard done_guard(__FUNCTION__, cntl, done, request, status);

    auto& vlet_info = request->vlet_info();
    uint32_t disk_id = vlet_info.disk_id();
    uint64_t volume_id = vlet_info.volume_id();
    uint32_t shard_index = vlet_info.shard_index();
    uint64_t create_time = vlet_info.create_time();
    uint32_t vlet_version = vlet_info.vlet_version();

    if (request->token() != FLAGS_token) {
        status->set_code(AIE_INVALID_TOKEN);
        status->set_msg("invalid token");
        return ;
    }

    if (FLAGS_readonly_mode) {
        status->set_code(AIE_READ_ONLY);
        status->set_msg("dn is read only");
        return;
    }

    if (vlet_version <= 0) {
        status->set_code(AIE_INVALID_ARGUMENT);
        status->set_msg("invalid vlet version");
        return ;
    }

    DiskManager* disk_mgr = g_datanode->disk_manager();
    auto disk_agent = disk_mgr->find(disk_id);

    // check disk
    if (disk_agent == nullptr || disk_agent->is_used() == false) {
        status->set_code(AIE_NOT_EXIST);
        status->set_msg("can not found target disk");
        return ;
    }

    if (disk_agent->disk_type() != common::string2disk_type(request->disk_type())) {
        status->set_code(AIE_FAIL);
        status->set_msg("invalid disk type");
        LOG(WARNING) << "disk id:" << disk_id << " type:"
            << common::disk_type2string(disk_agent->disk_type())
            << " disk_type is not consistent with master type:"
            << request->disk_type();
        return;
    }

    // check vlet info
    VletManager* vlet_mgr = g_datanode->vlet_manager();
    VletPtr vlet_ptr = vlet_mgr->find(volume_id);
    if (vlet_ptr) {
        DiskAgentPtr disk_agent = vlet_ptr->disk_agent();
        if (disk_agent->disk_id() == disk_id &&
                vlet_ptr->shard_index() == shard_index &&
                vlet_ptr->create_time() == create_time) {
            status->set_code(AIE_OK);
            status->set_msg("vlet already finish created");
        } else {
            status->set_code(AIE_EXIST);
            status->set_msg("vlet already exist");
        }
        return;
    }

    auto context = new CreateVletContext();
    context->request = request;
    context->response = response;
    context->status = status;
    context->cntl = cntl;
    context->done_guard.reset(done_guard.release());

    QueueExec(CREATE_VLET_ACTION, disk_agent, context);
}

void DataNodeControlServiceImpl::balance_vlet(::google::protobuf::RpcController* controller,
        const ::aries::pb::BalanceVletRequest* request,
        ::aries::pb::AckResponse* response,
        ::google::protobuf::Closure* done) {
    // hold Closure done
    auto status = response->mutable_status();
    baidu::rpc::Controller* cntl =
        static_cast<baidu::rpc::Controller*>(controller);

    if (FLAGS_readonly_mode) {
        status->set_code(AIE_READ_ONLY);
        status->set_msg("dn is read only");
        return;
    }

    CopyVletArgContext* context = new CopyVletArgContext();
    auto copy_vlet_worker = std::make_shared<CopyVletWorker>(context, cntl, done, request, response);
    context->worker = copy_vlet_worker;
    context->log_id = cntl->log_id();

    int ret = copy_vlet_worker->init();

    if (!ret) {
        copy_vlet_worker->copy_new_vlet();
    } else {
        delete context;
    }
}

void DataNodeControlServiceImpl::update_membership(
        ::google::protobuf::RpcController* controller,
        const ::aries::pb::UpdateMembershipRequest* request,
        ::aries::pb::AckResponse* response,
        ::google::protobuf::Closure* done) {
    // hold Closure done
    auto status = response->mutable_status();
    baidu::rpc::Controller* cntl =
        static_cast<baidu::rpc::Controller*>(controller);

    RpcGuard done_guard(__FUNCTION__, cntl, done, request, status);

    auto& vlet_info = request->vlet_info();

    uint32_t disk_id = vlet_info.disk_id();
    uint64_t volume_id = vlet_info.volume_id();
    uint32_t shard_index = vlet_info.shard_index();
    uint64_t create_time = vlet_info.create_time();

    if (request->token() != FLAGS_token) {
        status->set_code(AIE_INVALID_TOKEN);
        status->set_msg("invalid token");
        return ;
    }

    DiskManager* disk_mgr = g_datanode->disk_manager();

    // check vlet info
    VletManager* vlet_mgr = g_datanode->vlet_manager();
    VletPtr vlet_ptr = vlet_mgr->find(volume_id);
    if (!vlet_ptr) {
        status->set_code(AIE_NOT_EXIST);
        status->set_msg("vlet not exist");
        return ;
    }

    if (vlet_ptr->shard_index() != shard_index
            || vlet_ptr->create_time() != create_time) {
        status->set_code(AIE_NOT_EXIST);
        status->set_msg("invalid create_time or shard_index");
        return ;
    }

    UpdateVletContext* context = new UpdateVletContext();
    context->vlet_ptr = vlet_ptr;
    context->request = request;
    context->status = status;
    context->response = response;
    context->cntl = cntl;
    context->en_queue_time = base::gettimeofday_ms();
    context->en_queue_time_us = base::gettimeofday_us();
    context->timeout_ms = FLAGS_call_timeout_ms;
    context->log_id = request->has_log_id() ? request->log_id() : cntl->log_id();
    context->done_guard.reset(done_guard.release());
    if (vlet_info.has_shard_compress_option()) {
        context->shard_compress_option.CopyFrom(vlet_info.shard_compress_option());
    }
    context->action = UPDATE_VLET_ACTION;
    base::get_leaky_singleton<IOScheduler>()->dispatch_task(static_cast<IoContext*>(context));
}

void DataNodeControlServiceImpl::drop_vlet(::google::protobuf::RpcController* controller,
        const ::aries::pb::DropVletRequest* request,
        ::aries::pb::AckResponse* response,
        ::google::protobuf::Closure* done) {
    // hold Closure done
    auto status = response->mutable_status();
    baidu::rpc::Controller* cntl =
        static_cast<baidu::rpc::Controller*>(controller);
    uint64_t log_id = cntl->log_id();

    RpcGuard done_guard(__FUNCTION__, cntl, done, request, status);

    auto& vlet_info = request->vlet_info();
    uint32_t disk_id = vlet_info.disk_id();
    uint64_t volume_id = vlet_info.volume_id();
    uint32_t shard_index = vlet_info.shard_index();
    uint64_t create_time = vlet_info.create_time();

    if (request->token() != FLAGS_token) {
        status->set_code(AIE_INVALID_TOKEN);
        status->set_msg("invalid token");
        return ;
    }

    // check vlet info
    VletManager* vlet_mgr = g_datanode->vlet_manager();
    VletPtr vlet_ptr = vlet_mgr->find(volume_id);
    if (!vlet_ptr) {
        status->set_code(AIE_NOT_EXIST);
        status->set_msg("vlet not exist");
        return ;
    }

    if (vlet_ptr->disk_agent()->disk_id() != disk_id
            || vlet_ptr->shard_index() != shard_index
            || vlet_ptr->create_time() != create_time) {
        status->set_code(AIE_NOT_EXIST);
        status->set_msg("vlet not exist or invalid create_time or invalid disk_id");
        return ;
    }

    int ret = vlet_mgr->remove(volume_id);
    if (ret < 0) {
        status->set_code(AIE_NOT_EXIST);
        status->set_msg("vlet not exist");
        return ;
    }

    if (ret > 0) {
        status->set_code(AIE_INPROGRESS);
        status->set_msg("vlet is creating");
    }

    ARIES_RPC_LOG(NOTICE) << __FUNCTION__ 
            << " vid:" << volume_id << " shard_index:" << vlet_info.shard_index()
            << " disk_id:" << disk_id;

    auto context = new DropVletContext();
    context->vlet_ptr = vlet_ptr;
    context->request = request;
    context->response = response;
    context->status = status;
    context->log_id = cntl->log_id();
    context->cntl = cntl;
    context->done_guard.reset(done_guard.release());

    QueueExec(DROP_VLET_ACTION, vlet_ptr->disk_agent(), context);
}

void DataNodeControlServiceImpl::add_vlet(::google::protobuf::RpcController* controller,
        const ::aries::pb::AddVletRequest* request,
        ::aries::pb::AckResponse* response,
        ::google::protobuf::Closure* done) {
    auto status = response->mutable_status();
    baidu::rpc::Controller* cntl =
        static_cast<baidu::rpc::Controller*>(controller);
    uint64_t log_id = cntl->log_id();

    RpcGuard done_guard(__FUNCTION__, cntl, done, request, status);

    if (request->token() != FLAGS_token) {
        status->set_code(AIE_INVALID_TOKEN);
        status->set_msg("invalid token");
        return ;
    }

    uint64_t volume_id;
    uint32_t shard_index;
    auto const pos = request->path().find_last_of('/');
    const auto fname = request->path().substr(pos + 1);
    if (!parse_vlet(fname, &volume_id, &shard_index)) {
        status->set_code(AIE_FAIL);
        status->set_msg(request->path() + " wrong filename");
        return ;
    }

    // check vlet info
    VletManager* vlet_mgr = g_datanode->vlet_manager();
    VletPtr vlet_ptr = vlet_mgr->find(volume_id);
    if (vlet_ptr) {
        status->set_code(AIE_EXIST);
        status->set_msg("volume exist,only one shard per node is allowed");
        return ;
    }

    //find disk_agent
    auto& disk_map = g_datanode->disk_manager()->disk_agent_map();
    DiskAgent* disk_agent = nullptr;
    for (auto& kv : disk_map) {
        auto& disk = kv.second->disk_path();
        if (!request->path().compare(0, disk.size(), disk)) {
            //find
            disk_agent = kv.second; 
            break;
        }
    }
    // check disk
    if (disk_agent == nullptr || disk_agent->is_used() == false) {
        status->set_code(AIE_NOT_EXIST);
        status->set_msg("can not found target disk");
        return ;
    }

    ARIES_RPC_LOG(NOTICE) << __FUNCTION__ 
            << " vid:" << volume_id << " shard_index:" << shard_index
            << " file:" << request->path();

    std::string dst(disk_agent->disk_path());
    struct ::stat st;
    // For zone disk, request->path() contains disk path, vid, shard_index, but it's ok if
    // the actual path do not exist. No need to check path existance.
    if (!disk_agent->is_zone_disk()) {
        int ret = ::stat(request->path().c_str(), &st);
        if (ret) {
            status->set_code(AIE_FAIL);
            status->set_msg(request->path() + " not exist");
            return ;
        }

        char& last_char = dst.back();
        if (last_char != '/') {
            dst.append("/");
        }
        dst.append(fname);
        if (request->path() != dst) {
            int ret = ::rename(request->path().c_str(), dst.c_str());
            ARIES_RPC_LOG(NOTICE) << __FUNCTION__ << " rename:" << request->path()
                << " to " << dst << " ret:" << ret;
            if (ret) {
                status->set_code(AIE_FAIL);
                status->set_msg("mv file to workspace failed");
                return;
            }
        }
    }
    auto context = new AddVletContext();
    context->request = request;
    context->response = response;
    context->status = status;
    context->log_id = cntl->log_id();
    context->cntl = cntl;
    context->vid = volume_id;
    context->shard_index = shard_index;
    context->file_size = st.st_size;
    context->file_name = dst;
    context->done_guard.reset(done_guard.release());
   
    QueueExec(REBUILD_VLET_ACTION, disk_agent, context);

}

void DataNodeControlServiceImpl::drop_disk(::google::protobuf::RpcController* controller,
        const ::aries::pb::DropDiskRequest* request,
        ::aries::pb::AckResponse* response,
        ::google::protobuf::Closure* done) {
    // hold Closure done
    auto status = response->mutable_status();
    baidu::rpc::Controller* cntl =
        static_cast<baidu::rpc::Controller*>(controller);

    RpcGuard done_guard(__FUNCTION__, cntl, done, request, status);

    if (request->token() != FLAGS_token) {
        status->set_code(AIE_INVALID_TOKEN);
        status->set_msg("invalid token");
        return ;
    }

    DiskManager* disk_mgr = g_datanode->disk_manager();
    uint32_t disk_id = request->disk_id();

    // check disk
    DiskAgentPtr disk_agent = disk_mgr->find(disk_id);
    if (!disk_agent) {
        auto* cache_disk_conf = disk_mgr->cache_manager()->get_cache_disk_conf(disk_id);
        if (cache_disk_conf == nullptr) {
            status->set_code(AIE_NOT_EXIST);
            status->set_msg("disk not exist");
            return;
        } else {
            int ret = disk_mgr->cache_manager()->drop_cache_disk(disk_id);
            if (ret != AIE_OK) {
                status->set_code(AIE_FAIL);
                status->set_msg("drop cache disk failed");
            } else {
                status->set_code(AIE_OK);
                status->set_msg("drop cache disk success");
            }
            return;
        }

        status->set_code(AIE_NOT_EXIST);
        status->set_msg("disk not exist");
        return ;
    }

    if (!disk_agent->is_used()) {
        status->set_code(AIE_OK);
        status->set_msg("disk already dropped");
        return ;
    }

    auto context = new DropDiskContext();
    context->request = request;
    context->response = response;
    context->cntl = cntl;
    context->log_id = cntl->log_id();
    context->done_guard.reset(done_guard.release());

    QueueExec(DROP_DISK_ACTION, disk_agent, context);
}

void DataNodeControlServiceImpl::add_disk(::google::protobuf::RpcController* controller,
        const ::aries::pb::AddDiskRequest* request,
        ::aries::pb::AckResponse* response,
        ::google::protobuf::Closure* done) {
    // hold Closure done
    auto status = response->mutable_status();
    baidu::rpc::Controller* cntl =
        static_cast<baidu::rpc::Controller*>(controller);

    RpcGuard done_guard(__FUNCTION__, cntl, done, request, status);


    if (request->token() != FLAGS_token) {
        status->set_code(AIE_INVALID_TOKEN);
        status->set_msg("invalid token");
        return ;
    }

    DiskManager* disk_mgr = g_datanode->disk_manager();
    uint32_t disk_id = request->disk_id();

    // check disk
    DiskAgentPtr disk_agent = disk_mgr->find(disk_id);
    if (!disk_agent) {
        auto* cache_disk_conf = disk_mgr->cache_manager()->get_cache_disk_conf(disk_id);
        if (cache_disk_conf == nullptr) {
            status->set_code(AIE_NOT_EXIST);
            status->set_msg("disk not exist");
            return;
        } else {
            // TODO add cache disk in background
            int ret = disk_mgr->cache_manager()->add_cache_disk(disk_id);
            if (ret != AIE_OK) {
                status->set_code(AIE_FAIL);
                status->set_msg("add cache disk failed");
            } else {
                status->set_code(AIE_OK);
                status->set_msg("add cache disk success");
            }
            return;
        }
    }
    if (request->has_disk_type() &&
            common::string2disk_type(request->disk_type()) != disk_agent->disk_type()) {
        status->set_code(AIE_INVALID_ARGUMENT);
        status->set_msg("disk type inconsistent");
        return ;
    }

    auto context = new AddDiskContext();
    context->request = request;
    context->response = response;
    context->status = status;
    context->cntl = cntl;
    context->log_id = cntl->log_id();
    context->done_guard.reset(done_guard.release());

    QueueExec(ADD_DISK_ACTION, disk_agent, context);
}

void DataNodeControlServiceImpl::update_disk(::google::protobuf::RpcController* controller,
        const ::aries::pb::UpdateDiskRequest* request,
        ::aries::pb::AckResponse* response,
        ::google::protobuf::Closure* done) {
    // hold Closure done
    auto status = response->mutable_status();
    baidu::rpc::Controller* cntl =
        static_cast<baidu::rpc::Controller*>(controller);

    RpcGuard done_guard(__FUNCTION__, cntl, done, request, status);

    if (request->token() != FLAGS_token) {
        status->set_code(AIE_INVALID_TOKEN);
        status->set_msg("invalid token");
        return ;
    }

    DiskManager* disk_mgr = g_datanode->disk_manager();
    uint32_t disk_id = (request->disk_conf()).disk_id();

    // check disk
    DiskAgentPtr disk_agent = disk_mgr->find(disk_id);
    if (disk_agent != nullptr) {
        auto context = new UpdateDiskContext();
        context->request = request;
        context->response = response;
        context->status = status;
        context->cntl = cntl;
        context->log_id = cntl->log_id();
        context->done_guard.reset(done_guard.release());
        QueueExec(UPDATE_DISK_ACTION, disk_agent, context);
        return;
    }

    // check cache disk
    auto* cache_mgr = disk_mgr->cache_manager();
    if (cache_mgr == nullptr || cache_mgr->cache_disks_conf() == nullptr) {
        status->set_code(AIE_NOT_EXIST);
        status->set_msg("no cache disk");
        return;
    }
    int ret = cache_mgr->update_disk(request->disk_conf());
    if (ret != AIE_OK) {
        status->set_code(AIE_FAIL);
        status->set_msg("update cache disk failed");
        return;
    }

    disk_mgr->dump_disk_conf();

    status->set_code(AIE_OK);
    status->set_msg("update cache disk succ");
}


void DataNodeControlServiceImpl::disk_join(::google::protobuf::RpcController* controller,
        const ::aries::pb::AddDiskWithDataRequest* request,
        ::aries::pb::AckResponse* response,
        ::google::protobuf::Closure* done) {
    // hold Closure done
    auto status = response->mutable_status();
    baidu::rpc::Controller* cntl =
        static_cast<baidu::rpc::Controller*>(controller);

    RpcGuard done_guard(__FUNCTION__, cntl, done, request, status);


    if (request->token() != FLAGS_token) {
        status->set_code(AIE_INVALID_TOKEN);
        status->set_msg("invalid token");
        return ;
    }

    DiskManager* disk_mgr = g_datanode->disk_manager();
    uint32_t disk_id = request->disk_id();

    // check disk
    DiskAgentPtr disk_agent = disk_mgr->find(disk_id);
    if (!disk_agent) {
        status->set_code(AIE_NOT_EXIST);
        status->set_msg("disk not exist");
        return ;
    }

    if (disk_agent->is_used()) {
        status->set_code(AIE_OK);
        status->set_msg("disk already added");
        return ;
    }

    auto context = new AddDiskWithDataContext();
    context->request = request;
    context->response = response;
    context->status = status;
    context->cntl = cntl;
    context->log_id = cntl->log_id();
    context->done_guard.reset(done_guard.release());

    QueueExec(ADD_DISK_WITHDATA_ACTION, disk_agent, context);
}

void DataNodeControlServiceImpl::join_node(::google::protobuf::RpcController* controller,
                                           const ::aries::pb::JoinNodeRequest* request,
                                           ::aries::pb::AckResponse* response,
                                           ::google::protobuf::Closure* done) {
    auto status = response->mutable_status();
    baidu::rpc::Controller* cntl =
        static_cast<baidu::rpc::Controller*>(controller);

    RpcGuard done_guard(__FUNCTION__, cntl, done, request, status);
    if (request->token() != FLAGS_token) {
        status->set_code(AIE_INVALID_TOKEN);
        status->set_msg("invalid token");
        return ;
    }

    MasterCaller master_caller;
    base::EndPoint local = common::get_local_addr();
    int ret = 0;
    do {
        //step1, add node
        aries::pb::AckResponse ackResponse;
        aries::pb::AddNodeRequest addNodeRequest;
        addNodeRequest.set_token(FLAGS_token);

        addNodeRequest.set_node_addr(common::endpoint2int(local));
        addNodeRequest.set_az_name(request->az_name());
        addNodeRequest.set_rack_name(request->rack_name());
        addNodeRequest.set_idc_name(request->idc_name());
        addNodeRequest.set_group_name(request->group_name());

        ret = master_caller.add_node(&addNodeRequest, &ackResponse);
        if (ret) {
            break;
        }
        //step2, add disk
        DiskManager* disk_mgr = g_datanode->disk_manager();
        auto& disk_map = disk_mgr->disk_agent_map();
        for (auto& kv : disk_map) {
            if (kv.second->is_used()) {
                aries::pb::AckResponse response;
                aries::pb::MasterAddDiskRequest request;
                request.set_token(FLAGS_token);
                request.set_node_addr(common::endpoint2int(local));
                request.set_disk_id(kv.first);
                request.set_disk_type(kv.second->disk_conf().disk_type());
                request.set_is_force_add(true);
                ret = master_caller.add_disk(&request, &response);
                if (ret) {
                    break;
                }
            }
        }

        if (request->is_add_data() == true) {
            VletManager* vlet_manager = g_datanode->vlet_manager();
            std::vector<VletPtr> vec;
            vlet_manager->list_vlet(&vec);
            for (auto& vlet: vec) {
                // get datanode vletinfo
                aries::pb::DatanodeVletInfo info;
                vlet->fill_vlet_info(&info);

                aries::pb::AckResponse response;
                aries::pb::ReplaceVletRequest request;

                fill_replace_vlet_request(request,info);
                master_caller.replace_vlet(&request, &response);
            }
        }
    } while (false);

    if (ret != AIE_OK) {
        status->set_code(ret);
        status->set_msg("failed to join node");
        return ;
    } else {
        status->set_code(AIE_OK);
        status->set_msg("join node succ");
        return ;
    }
}

void DataNodeControlServiceImpl::rewrite_vlet(::google::protobuf::RpcController* controller,
        const ::aries::pb::RewriteVletRequest* request,
        ::aries::pb::AckResponse* response,
        ::google::protobuf::Closure* done) {
    // hold Closure done
    auto status = response->mutable_status();
    baidu::rpc::Controller* cntl =
        static_cast<baidu::rpc::Controller*>(controller);
    uint64_t log_id = cntl->log_id();

    RpcGuard done_guard(__FUNCTION__, cntl, done, request, status);

    uint32_t disk_id = request->disk_id();
    uint64_t volume_id = request->volume_id();
    uint32_t shard_index = request->shard_index();

    if (request->token() != FLAGS_token) {
        status->set_code(AIE_INVALID_TOKEN);
        status->set_msg("invalid token");
        return ;
    }
     std::vector<VletPtr> vlet_list;
    if (request->has_volume_id()) {
        // rewrite one vlet
        VletManager* vlet_mgr = g_datanode->vlet_manager();
        VletPtr vlet_ptr = vlet_mgr->find(volume_id);
        if (!vlet_ptr) {
            status->set_code(AIE_NOT_EXIST);
            status->set_msg("vlet not exist");
            return ;
        }
        if (vlet_ptr->disk_agent()->disk_id() != disk_id
                || vlet_ptr->shard_index() != shard_index) {
            status->set_code(AIE_NOT_EXIST);
            status->set_msg("vlet not exist or invalid disk_id");
            return ;
        }
        vlet_list.push_back(vlet_ptr);
    } else if (request->has_disk_id()) {
        // rewrite one disk
        VletManager* vlet_mgr = g_datanode->vlet_manager();
        vlet_mgr->list_vlet(&vlet_list);
        for (auto iter = vlet_list.begin(); iter !=  vlet_list.end();) {
            auto vlet_ptr = *iter;
            if (!vlet_ptr) {
                continue;
            }
            if (vlet_ptr->disk_agent()->disk_id() !=  disk_id) {
                iter = vlet_list.erase(iter);
            } else {
                ++iter;
            }
        }
    } else {
        // rewrite one node
        VletManager* vlet_mgr = g_datanode->vlet_manager();
        vlet_mgr->list_vlet(&vlet_list);
    }
    ARIES_RPC_LOG(NOTICE) << __FUNCTION__ 
                << " vid:" << volume_id << " shard_index:" << shard_index
                << " disk_id:" << disk_id;
    bool success = true;
    for (auto vlet_ptr : vlet_list) {
        uint64_t now = base::gettimeofday_us();
        auto context = new RewriteVletContext();
        context->vid = vlet_ptr->volume_id();
        context->vlet_ptr = vlet_ptr;
        context->timeout_ms = FLAGS_call_timeout_ms;
        context->options.is_force_rewrite = true;
        context->options.last_rewrite_time = now;
        context->options.append_zone_rewrite_rate = FLAGS_min_zone_hole_rate_for_rewrite;
        context->options.daily_rewrite_start_time = FLAGS_default_daily_rewrite_start_time;
        context->options.daily_rewrite_duration_second = FLAGS_min_daily_rewrite_duration_second;
        context->log_id = cntl->log_id();
        bool ret = g_datanode->rewrite_manager()->force_rewrite_vlet(context);
        if (ret == false) {
            success = false;
        }
    }
    if (!success) {
        status->set_code(AIE_FAIL);
        status->set_msg("failed to gen rewrite task for vlet");
        return ;
    }
}

void DataNodeControlServiceImpl::rewrite_zone(::google::protobuf::RpcController* controller,
        const ::aries::pb::RewriteZoneRequest* request,
        ::aries::pb::AckResponse* response,
        ::google::protobuf::Closure* done) {
    // hold Closure done
    auto status = response->mutable_status();
    baidu::rpc::Controller* cntl =
        static_cast<baidu::rpc::Controller*>(controller);
    uint64_t log_id = cntl->log_id();

    RpcGuard done_guard(__FUNCTION__, cntl, done, request, status);

    if (request->token() != FLAGS_token) {
        status->set_code(AIE_INVALID_TOKEN);
        status->set_msg("invalid token");
        return;
    }

    if (!request->has_disk_id() || !request->has_zone_id()) {
        status->set_code(AIE_INVALID_ARGUMENT);
        status->set_msg("invalid argument");
        return;
    }

    ARIES_RPC_LOG(NOTICE) << __FUNCTION__ 
                << " disk_id:" << request->disk_id()
                << " zone_id" << request->zone_id();
    bool success = g_datanode->rewrite_manager()->force_rewrite_zone(
        request->disk_id(), request->zone_id());

    if (!success) {
        status->set_code(AIE_FAIL);
        status->set_msg("failed to gen rewrite task for zone");
        return ;
    }
}

void DataNodeControlServiceImpl::show_disk(::google::protobuf::RpcController* controller,
        const ::aries::pb::ShowDiskRequest* request,
        ::aries::pb::ShowDiskResponse* response,
        ::google::protobuf::Closure* done) {
    // hold Closure done
    auto status = response->mutable_status();
    baidu::rpc::Controller* cntl =
        static_cast<baidu::rpc::Controller*>(controller);

    RpcGuard done_guard(__FUNCTION__, cntl, done, request, status);

    if (request->token() != FLAGS_token) {
        status->set_code(AIE_INVALID_TOKEN);
        status->set_msg("invalid token");
        return ;
    }

    DiskManager* disk_mgr = g_datanode->disk_manager();
    uint32_t disk_id = request->disk_id();

    // check disk
    DiskAgentPtr disk_agent = disk_mgr->find(disk_id);
    if (disk_agent != nullptr) {
        if (!disk_agent->is_used()) {
            status->set_code(AIE_OK);
            status->set_msg("disk already dropped");
            return ;
        }

        common::SyncPoint sync_point(1);
        ShowDiskContext context;
        context.disk_conf = response->mutable_disk_conf();
        context.sync_point = &sync_point;

        QueueExec(SHOW_DISK_ACTION, disk_agent, &context);

        sync_point.wait();
        return;
    }

    // check cache disk
    if (auto* cache_mgr = disk_mgr->cache_manager(); cache_mgr != nullptr || cache_mgr->cache_disks_conf() != nullptr) {
        for (const auto& cache_disk_conf : cache_mgr->cache_disks_conf()->disks) {
            if (cache_disk_conf.disk_id() == disk_id) {
                response->mutable_disk_conf()->CopyFrom(cache_disk_conf);

                status->set_code(AIE_OK);
                status->set_msg("show cache disk succ");
                return;
            }
        }
    }

    status->set_code(AIE_NOT_EXIST);
    status->set_msg("disk not exist");
    return;
}

void DataNodeControlServiceImpl::get_smr_monitor_info(::google::protobuf::RpcController* controller,
                                                      const ::aries::pb::EmptyMessage* request,
                                                      ::aries::pb::SmrMonitorInfoResponse* response,
                                                      ::google::protobuf::Closure* done) {

    baidu::rpc::Controller* cntl =
            static_cast<baidu::rpc::Controller*>(controller);

    baidu::rpc::ClosureGuard done_guard(done);

    uint64_t log_id = cntl->log_id();
    ARIES_RPC_RECV_LOG(NOTICE) << " cmd:" << __FUNCTION__;

    auto status = response->mutable_status();

    DiskManager* disk_mgr = g_datanode->disk_manager();

    const auto& disk_agent_map = disk_mgr->disk_agent_map();

    uint32_t disk_num = 0;

    std::vector<GetSmrDiskContext> ctx_list;
    for (auto& [disk_id, disk_agent]: disk_agent_map) {
        if (disk_agent->is_used()) {
            ++disk_num;
        }
    }
    if (disk_num == 0) {
        ARIES_RPC_RECV_LOG(NOTICE) << "get smr info failed due to no used disk";
        status->set_code(AIE_FAIL);
        status->set_msg("get smr info failed due to no used disk");
        return;
    }
    ctx_list.resize(disk_num);

    uint32_t i = 0;
    common::SyncPoint sync_point(disk_num);
    for (auto& [disk_id, disk_agent] : disk_agent_map) {
        if (!disk_agent->is_used()) {
            continue;
        }
        auto& ctx = ctx_list[i++];
        ctx.sync_point = &sync_point;
        QueueExec(GET_SMR_DISK_INFO_ACTION, disk_agent, &ctx);
    }
    sync_point.wait();

    auto info = response->mutable_smr_monitor_info();
    std::stringstream log_stream;
    log_stream << "get smr info fail";
    for (const auto& ctx : ctx_list) {
        if (ctx.status.code() != AIE_OK) {
            log_stream << " dev:" << ctx.disk_info.dev_path()
                       << " status:" << ctx.status.to_string();
            continue;
        }
        auto disk_info = info->add_disk_info_list();
        disk_info->CopyFrom(ctx.disk_info);
    }
    if (info->disk_info_list().empty()) {
        status->set_code(AIE_FAIL);
        status->set_msg("get smr info failed due to all get disk info action failed");
    }
    ARIES_RPC_RECV_LOG(NOTICE) << log_stream.str();
}

void DataNodeControlServiceImpl::drop_cache_storage(::google::protobuf::RpcController* controller,
        const ::aries::pb::DropCacheStorageRequest* request,
        ::aries::pb::AckResponse* response,
        ::google::protobuf::Closure* done) {
    // hold Closure done
    auto status = response->mutable_status();
    baidu::rpc::Controller* cntl =
        static_cast<baidu::rpc::Controller*>(controller);
    uint64_t log_id = cntl->log_id();

    RpcGuard done_guard(__FUNCTION__, cntl, done, request, status);

    if (request->token() != FLAGS_token) {
        status->set_code(AIE_INVALID_TOKEN);
        status->set_msg("invalid token");
        return;
    }

    auto* cache_mgr = g_datanode->cache_manager();
    int ret = cache_mgr->drop_storage(request->storage_id());

    status->set_code(ret);
    status->set_msg(::aries::Status(ret).msg());
}

void DataNodeControlServiceImpl::add_record_to_cache(::google::protobuf::RpcController* controller,
        const ::aries::pb::AddRecordToCacheRequest* request,
        ::aries::pb::AckResponse* response,
        ::google::protobuf::Closure* done) {
        // hold Closure done
    auto status = response->mutable_status();
    baidu::rpc::Controller* cntl =
        static_cast<baidu::rpc::Controller*>(controller);
    uint64_t log_id = cntl->log_id();

    RpcGuard done_guard(__FUNCTION__, cntl, done, request, status);

    if (request->token() != FLAGS_token) {
        status->set_code(AIE_INVALID_TOKEN);
        status->set_msg("invalid token");
        return ;
    }

    auto* cache_mgr = g_datanode->cache_manager();
    if (!FLAGS_enable_cache || cache_mgr == nullptr || !cache_mgr->enabled()) {
        status->set_code(AIE_FAIL);
        status->set_msg("cache is not enabled or not started");
        return ;
    }

    if (!request->has_volume_id() || !request->has_vbid() || !request->has_shard_index()) {
        status->set_code(AIE_INVALID_ARGUMENT);
        status->set_msg("invalid shard id");
        return ;
    }

    uint64_t volume_id = request->volume_id();
    uint64_t vbid = request->vbid();
    uint32_t shard_index = request->shard_index();

    bool shard_exist = false;
    ShardId shard_id(volume_id, vbid, shard_index);
    int ret = cache_mgr->exist(shard_id, &shard_exist);
    if (ret != AIE_OK || shard_exist) {
        if (shard_exist) {
            status->set_code(AIE_OK);
            status->set_msg("shard is already in cache");
        } else {
            status->set_code(ret);
            status->set_msg("cache is disable");
        }
        return;
    }

    VletManager* vlet_mgr = g_datanode->vlet_manager();
    VletPtr vlet_ptr = vlet_mgr->find(volume_id);
    if (!vlet_ptr || vlet_ptr->shard_index() != shard_index) {
        // target vlet not found
        status->set_code(AIE_NOT_EXIST);
        status->set_msg("target vlet not found");
        ARIES_RPC_DONE_LOG(NOTICE) << " target vlet not found";
        return ;
    }

    auto* ctx = new AddRecordToCacheContext(request, response, cntl);
    ctx->status = status;
    ctx->log_id = log_id;
    ctx->vid = volume_id;
    ctx->vbid = vbid;
    ctx->done_guard.reset(done_guard.release());
    LOG(NOTICE) << "[log_id:" << log_id << "]"
        << " [cmd:" << __FUNCTION__ << "]"
        << " recv " << request->GetTypeName()
        << " from " << cntl->remote_side()
        << " vid:" << volume_id << " vbid:" << vbid
        << " shard_index:" << shard_index;

    auto* cache_manager = g_datanode->cache_manager();
    ret = cache_manager->dispatch_add_to_cache_task(ctx);
    if (ret != AIE_OK) {
        status->set_code(ret);
        status->set_msg("dispatch remove task from cache failed");
        delete ctx;
    }
}

void DataNodeControlServiceImpl::remove_record_from_cache(::google::protobuf::RpcController* controller,
        const ::aries::pb::RemoveRecordFromCacheRequest* request,
        ::aries::pb::AckResponse* response,
        ::google::protobuf::Closure* done) {
    // hold Closure done
    auto status = response->mutable_status();
    baidu::rpc::Controller* cntl =
        static_cast<baidu::rpc::Controller*>(controller);
    uint64_t log_id = cntl->log_id();

    RpcGuard done_guard(__FUNCTION__, cntl, done, request, status);

    if (request->token() != FLAGS_token) {
        status->set_code(AIE_INVALID_TOKEN);
        status->set_msg("invalid token");
        return ;
    }

    if (!request->has_volume_id() || !request->has_vbid() || !request->has_shard_index()) {
        status->set_code(AIE_INVALID_ARGUMENT);
        status->set_msg("invalid shard id");
        return ;
    }

    uint64_t volume_id = request->volume_id();
    uint64_t vbid = request->vbid();
    uint32_t shard_index = request->shard_index();

    auto* ctx = new RemoveRecordFromCacheContext(request, response, cntl);
    ctx->status = status;
    ctx->log_id = log_id;
    ctx->vid = volume_id;
    ctx->vbid = vbid;
    ctx->done_guard.reset(done_guard.release());
    LOG(NOTICE) << "[log_id:" << log_id << "]"
        << " [cmd:" << __FUNCTION__ << "]"
        << " recv " << request->GetTypeName()
        << " from " << cntl->remote_side()
        << " vid:" << volume_id << " vbid:" << vbid
        << " shard_index:" << shard_index;

    auto* cache_manager = g_datanode->cache_manager();
    if (!FLAGS_enable_cache || cache_manager == nullptr || !cache_manager->enabled()) {
        status->set_code(AIE_FAIL);
        status->set_msg("cache is not enabled or not started");
        delete ctx;
        return ;
    }
    int ret = cache_manager->dispatch_remove_from_cache_task(ctx);
    if (ret != AIE_OK) {
        status->set_code(ret);
        status->set_msg("dispatch remove task from cache failed");
        delete ctx;
    }
}

void DataNodeControlServiceImpl::control_inject_fault(::google::protobuf::RpcController* controller,
                                          const ::aries::pb::ControlInjectFaultRequest* request,
                                          ::aries::pb::AckResponse* response,
                                          ::google::protobuf::Closure* done) {
    // hold Closure done
    auto status = response->mutable_status();
    baidu::rpc::Controller* cntl =
            static_cast<baidu::rpc::Controller*>(controller);
    uint64_t log_id = cntl->log_id();

    RpcGuard done_guard(__FUNCTION__, cntl, done, request, status);

    if (request->token() != FLAGS_token) {
        status->set_code(AIE_INVALID_TOKEN);
        status->set_msg("invalid token");
        return;
    }

#if defined(ENABLE_INJECT_FAULT)
    auto sp = aries::SyncPoint::get_instance();
    if (request->command() == aries::pb::INJECT_FAULT_ENABLE) {
        if (!sp->is_processing()) {
            sp->clear_all_callbacks();
            sp->enable_processing();
        }
    } else if (request->command() == aries::pb::INJECT_FAULT_DISABLE) {
        if (sp->is_processing()) {
            sp->clear_all_callbacks();
            sp->disable_processing();
        }
    } else if (request->command() == aries::pb::INJECT_FAULT_CLEAR_ALL) {
        if (sp->is_processing()) {
            sp->clear_all_callbacks();
            sp->clear_trace();
        }
    } else {
        status->set_code(AIE_NOTSUPPORT);
        status->set_msg("invalid command type, not supported");
        return;
    }
    status->set_code(AIE_OK);
    return;
#else
    status->set_code(AIE_FAIL);
    status->set_msg("compile without BCLOUD_ENABLE_INJECT_FAULT");
#endif
}

void DataNodeControlServiceImpl::enable_inject_fault(::google::protobuf::RpcController* controller,
                                 const ::aries::pb::EnableInjectFaultRequest* request,
                                 ::aries::pb::AckResponse* response,
                                 ::google::protobuf::Closure* done) {
    // hold Closure done
    auto status = response->mutable_status();
    baidu::rpc::Controller* cntl =
            static_cast<baidu::rpc::Controller*>(controller);
    uint64_t log_id = cntl->log_id();

    RpcGuard done_guard(__FUNCTION__, cntl, done, request, status);

    if (request->token() != FLAGS_token) {
        status->set_code(AIE_INVALID_TOKEN);
        status->set_msg("invalid token");
        return;
    }

#if defined(ENABLE_INJECT_FAULT)
    auto sp = aries::SyncPoint::get_instance();
    switch(request->trigger_method()) {
    case aries::pb::TRIGGER_TYPE_ALWAYS:
        if (!request->has_inject_value()) {
            ENABLE_SYNC_POINT(request->inject_fault_name(), request->flags());
        } else if (request->inject_value().has_bool_value()) {
            ENABLE_AND_SET_VALUE(request->inject_fault_name(), request->flags(), request->inject_value().bool_value(), bool);
        } else if (request->inject_value().has_int_value()) {
            ENABLE_AND_SET_VALUE(request->inject_fault_name(), request->flags(), request->inject_value().int_value(), int64_t);
        } else if (request->inject_value().has_str_value()) {
            ENABLE_AND_SET_VALUE(request->inject_fault_name(), request->flags(), request->inject_value().str_value(), std::string);
        } else {
            status->set_code(AIE_NOTSUPPORT);
            status->set_msg("invalid value type, not supported");
            return;
        }
        break;
    case aries::pb::TRIGGER_TYPE_PROB:
        if (!request->has_probability()) {
            status->set_code(AIE_INVALID_ARGUMENT);
            status->set_msg("probability field is not set in random mode");
            return;
        }
        if (!request->has_inject_value()) {
            ENABLE_SYNC_POINT_RANDOM(request->inject_fault_name(), request->probability(), request->flags());
        } else if (request->inject_value().has_bool_value()) {
            ENABLE_RANDOM_FAULT_AND_SET_VALUE(request->inject_fault_name(), request->probability(), request->flags(), request->inject_value().bool_value(), bool);
        } else if (request->inject_value().has_int_value()) {
            ENABLE_RANDOM_FAULT_AND_SET_VALUE(request->inject_fault_name(), request->probability(),request->flags(), request->inject_value().int_value(), int64_t);
        } else if (request->inject_value().has_str_value()) {
            ENABLE_RANDOM_FAULT_AND_SET_VALUE(request->inject_fault_name(), request->probability(),request->flags(), request->inject_value().str_value(), std::string);
        } else {
            status->set_code(AIE_NOTSUPPORT);
            status->set_msg("invalid value type, not supported");
            return;
        }
        break;
    case aries::pb::TRIGGER_TYPE_STEP: {
        if (!request->has_external_value()) {
            status->set_code(AIE_INVALID_ARGUMENT);
            status->set_msg("external_value field is not set");
            return;
        }
        auto external_cb = [step = request->external_value()]() {
            static int i = 1;
            if (i++ % step == 0) {
                return true;
            }
            return false;
        };
        if (!request->has_inject_value()) {
            ENABLE_SYNC_POINT_EXTERNAL(request->inject_fault_name(), external_cb, request->flags());
        } else if (request->inject_value().has_bool_value()) {
            ENABLE_EXTERNA_FAULT_AND_SET_VALUE(request->inject_fault_name(), external_cb, request->flags(), request->inject_value().bool_value(), bool);
        } else if (request->inject_value().has_int_value()) {
            ENABLE_EXTERNA_FAULT_AND_SET_VALUE(request->inject_fault_name(), external_cb, request->flags(), request->inject_value().int_value(), int64_t);
        } else if (request->inject_value().has_str_value()) {
            ENABLE_EXTERNA_FAULT_AND_SET_VALUE(request->inject_fault_name(), external_cb, request->flags(), request->inject_value().str_value(), std::string);
        } else {
            status->set_code(AIE_NOTSUPPORT);
            status->set_msg("invalid value type, not supported");
            return;
        }
        break;
    }
    case aries::pb::TRIGGER_TYPE_INTERVAL: {
        if (!request->has_external_value()) {
            status->set_code(AIE_INVALID_ARGUMENT);
            status->set_msg("external_value(interval) field is not set");
            return;
        }
        auto external_cb = [interval_us = request->external_value()]() {
            static int64_t last_time_us = base::gettimeofday_us();
            if (base::gettimeofday_us() - last_time_us >= interval_us) {
                last_time_us = base::gettimeofday_us();
                return true;
            }
            return false;
        };
        if (!request->has_inject_value()) {
            ENABLE_SYNC_POINT_EXTERNAL(request->inject_fault_name(), external_cb, request->flags());
        } else if (request->inject_value().has_bool_value()) {
            ENABLE_EXTERNA_FAULT_AND_SET_VALUE(request->inject_fault_name(), external_cb, request->flags(), request->inject_value().bool_value(), bool);
        } else if (request->inject_value().has_int_value()) {
            ENABLE_EXTERNA_FAULT_AND_SET_VALUE(request->inject_fault_name(), external_cb, request->flags(), request->inject_value().int_value(), int64_t);
        } else if (request->inject_value().has_str_value()) {
            ENABLE_EXTERNA_FAULT_AND_SET_VALUE(request->inject_fault_name(), external_cb, request->flags(), request->inject_value().str_value(), std::string);
        } else {
            status->set_code(AIE_NOTSUPPORT);
            status->set_msg("invalid value type, not supported");
            return;
        }
        break;
    }
    default:
        status->set_code(AIE_INVALID_ARGUMENT);
        status->set_msg("invalid trigger type, not supported");
        return;
    }

    status->set_code(AIE_OK);
    return;
#else
    status->set_code(AIE_FAIL);
    status->set_msg("compile without option BCLOUD_ENABLE_INJECT_FAULT");
#endif
}

void DataNodeControlServiceImpl::disable_inject_fault(::google::protobuf::RpcController* controller,
                                  const ::aries::pb::DisableInjectFaultRequest* request,
                                  ::aries::pb::AckResponse* response,
                                  ::google::protobuf::Closure* done) {
    // hold Closure done
    auto status = response->mutable_status();
    baidu::rpc::Controller* cntl =
            static_cast<baidu::rpc::Controller*>(controller);
    uint64_t log_id = cntl->log_id();

    RpcGuard done_guard(__FUNCTION__, cntl, done, request, status);

    if (request->token() != FLAGS_token) {
        status->set_code(AIE_INVALID_TOKEN);
        status->set_msg("invalid token");
        return;
    }

#if defined(ENABLE_INJECT_FAULT)
    auto sp = aries::SyncPoint::get_instance();
    sp->clear_callback(request->inject_fault_name());
    status->set_code(AIE_OK);
    return;
#else
    status->set_code(AIE_FAIL);
    status->set_msg("compile without option BCLOUD_ENABLE_INJECT_FAULT");
#endif
}

} // end namespace of datanode
} // end namespace of aries


/* vim: set ts=4 sw=4 sts=4 tw=100 */

