/***************************************************************************
 * 
 * Copyright (c) 2021 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/
 
 /**
 * @file volume_ec_check_manager.cpp
 * <AUTHOR>
 * @date 2021/09/07 10:06:17
 * @version 1.0 
 * @brief 
 *  
 **/

#include "baidu/inf/aries/checkcenter/volume_ec_check_manager.h"

#include <stdio.h>
#include <stdlib.h>
#include <libgen.h>
#include <bthread.h>
#include <bthread_types.h>
#include <fstream>

#include "baidu/inf/aries-api/common/master_tracker.h"
#include "baidu/inf/aries-api/common/rpc_call.h"
#include "baidu/inf/aries-api/common/rpc_stub.h"
#include "baidu/inf/aries-api/common/bvar_define.h"
#include "baidu/inf/aries-api/common/config.h"
#include "baidu/inf/aries/checkcenter/flags.h"
#include "baidu/inf/aries-api/common/utils.h"

namespace aries {
namespace checker {

ARIES_BVAR_COUNTER(ec_checker, reclaim_volume_check);
ARIES_BVAR_COUNTER(ec_checker, check_volume_failed);
ARIES_BVAR_RECORDER(ec_checker, volume_check_queue_size);
ARIES_BVAR_RECORDER(ec_checker, ec_check_progress);
ARIES_BVAR_ADDER(ec_checker, code_decode_err);
ARIES_BVAR_ADDER(ec_checker, bad_blob_size);

VolumeECCheckManager *g_volume_ec_check_manager = new VolumeECCheckManager;

VolumeECCheckManager::VolumeECCheckManager()
        : VolumeManager (g_ec_checker_reclaim_volume_check_counter,
        g_ec_checker_check_volume_failed_counter,
        g_ec_checker_volume_check_queue_size_recorder,
        g_ec_checker_bad_blob_size_adder,
        g_ec_checker_ec_check_progress_recorder,
        FLAGS_ec_check_volume_expire_time_s) {
}

bool VolumeECCheckManager::fetch_volume_to_check(const aries::pb::FetchDataCheckVolumesRequest& request,
        aries::pb::FetchDataCheckVolumesResponse* response) {
    base::Timer timer;
    timer.start();
    auto status = response->mutable_status();
    common::ScopedMutexLock lock(_mutex);
    VolumeInfoPtr volume = nullptr;
    if (!FLAGS_enable_ec_check) {
        status->set_code(AIE_EMPTY);
        status->set_msg("the ec check is disabled");
        return false;
    }
    if (_waiting_volume_queue.size() == 0) {
        status->set_code(AIE_EMPTY);
        status->set_msg("the waiting check volume queue is empty");
        return false;
    }

    volume = _waiting_volume_queue.top();
    _waiting_volume_queue.pop();
    if (volume == nullptr) {
        status->set_code(AIE_EMPTY);
        status->set_msg("no suitable volume to check");
        return false;
    }
    assert(volume->is_checking == false);
    volume->is_checking = true;
    volume->last_fetch_time = base::gettimeofday_us();
    volume->checkworker_addr = request.req_addr();
    bool ok = _running_volumes.insert(std::make_pair(volume->vid, volume)).second;
    assert(ok);
    status->set_code(AIE_OK);
    status->set_msg("ok");
    response->set_volume_id(volume->vid);
    timer.stop();
    LOG(NOTICE) << "fetch a volume to ec check,"
        << " vid:" << volume->vid
        << " checkworker_addr:" << common::endpoint2str(common::int2endpoint(request.req_addr()))
        << " last_check_time:" << volume->last_check_time
        << " cost_ms:" << timer.m_elapsed();

    return true;
}

void VolumeECCheckManager::report_check_result(const aries::pb::ReportDataCheckResultsRequest& request) {
    common::ScopedMutexLock lock(_mutex);
    uint64_t now = base::gettimeofday_us();

    //check success
    for (int i = 0; i < request.volumes_size(); ++i) {
        auto& finished_volume = request.volumes(i);
        auto vid = finished_volume.volume_id();
        auto it = _running_volumes.find(vid);
        if (it == _running_volumes.end()) {
            LOG(WARNING) << "discard report ec check result due to volume is not in checking,"
                << " vid:" << vid;
        } else {
            auto volume = it->second;
            volume->last_check_time = now;
            volume->priority = volume->last_check_time;
            volume->is_checking = false;
            volume->check_failed_cnt = 0;
            _running_volumes.erase(it);
            _checker_finish_counter++;
            _finished_volumes[vid] = true;

            //record bad blobs num
            if (finished_volume.code_decode_err_num() > 0) {
                std::vector<aries::pb::BadBlobErrInfo> bad_blobs;
                for (int i = 0; i < finished_volume.blobs_size(); i++) {
                    auto& blob_info = finished_volume.blobs(i);
                    if (blob_info.code_decode_err()) {
                        aries::pb::BadBlobErrInfo bad_blob_info;
                        bad_blob_info.set_vid(vid);
                        bad_blob_info.set_vbid(blob_info.vbid());
                        bad_blob_info.set_code_decode_err(true);
                        bad_blobs.push_back(bad_blob_info);
                    }
                }
                _bad_blobs[vid] = bad_blobs;
                g_ec_checker_code_decode_err_adder << bad_blobs.size();
            }
            
            LOG(NOTICE) << "finish check volume," << " vid:" << vid
                << " cost_min:" << ((volume->last_check_time - volume->last_fetch_time) / 1000 / 1000 / 60);
        }
    }

    //check failed
    for (int i = 0; i < request.failed_volumes_size(); ++i) {
        auto vid = request.failed_volumes(i);
        uint64_t check_failed_cnt = 0;
        auto it = _running_volumes.find(vid);
        if (ARIES_UNLIKELY(it == _running_volumes.end())) {
            LOG(WARNING) << "discard report check result due to volume is not in checking,"
                << " vid:" << vid;
        } else {
            auto volume = it->second;
            volume->is_checking = false;
            volume->check_failed_cnt += 1;
            check_failed_cnt = volume->check_failed_cnt;
            _running_volumes.erase(it);
            //delay x day in case this task failed frequently
            uint64_t delay = 24ULL * 3600 * 1000 * 1000 * FLAGS_data_check_failed_delay_times;
            volume->priority += delay;
            _waiting_volume_queue.push(volume);
            _check_volume_failed << check_failed_cnt;
        }

        LOG(WARNING) << "check volume fail,"
            << " vid:" << vid << " check_failed_cnt:" << check_failed_cnt;
    }

}

bool VolumeECCheckManager::update_volumes(const aries::pb::ListVolumesResponse& response) {
    if (response.status().code() != AIE_OK || 0 == response.volumes_size()) {
        LOG(WARNING) << "response code is not ok or volumes is empty, skip update volumes";
        return false;
    }
    std::vector<std::pair<uint64_t, uint64_t>> volume_info_list;
    for (int i = 0; i < response.volumes_size(); ++i) {
        auto volume_id = response.volumes(i).volume_id();
        auto create_time = response.volumes(i).create_time();
        volume_info_list.push_back(std::make_pair(volume_id, create_time));
    }
    std::sort(volume_info_list.begin(), volume_info_list.end());
    common::ScopedMutexLock lock(_mutex);
    std::vector<VolumeInfoPtr> new_volumes;
    VolumeInfoPtrQueue new_waiting_volume_queue;
    std::map<uint64_t, VolumeInfoPtr> new_running_volumes;
    auto it = _volumes.begin();
    uint32_t i = 0;
    const uint64_t check_gap = 1UL * 30 * 24 * 3600 * 1000 * 1000;
    uint64_t now = base::gettimeofday_us() - check_gap;
    while (i < volume_info_list.size()) {
        if (it == _volumes.end() || (*it)->vid > volume_info_list.at(i).first) {
            VolumeInfoPtr volume = VolumeInfoPtr(new VolumeInfo());
            volume->vid = volume_info_list.at(i).first;
#ifdef _UNIT_TEST 
            volume->last_check_time = volume_info_list.at(i).second;
#else
            volume->last_check_time = now + base::fast_rand() % check_gap;
#endif
            volume->priority = volume->last_check_time;
            volume->is_checking = false;
            volume->check_failed_cnt = 0;
            volume->last_fetch_time = 0;
            volume->checkworker_addr = 0;
            if (_finished_volumes.find(volume->vid) == _finished_volumes.end()) {
                new_waiting_volume_queue.push(volume);
            }
            new_volumes.push_back(volume);
            ++i;
        }  else if ((*it)->vid == volume_info_list.at(i).first) {
            auto volume = *it;
            new_volumes.push_back(volume);
            if (volume->is_checking) {
                new_running_volumes[volume->vid] = volume;
            } else {
                if (_finished_volumes.find(volume->vid) == _finished_volumes.end()) {
                    new_waiting_volume_queue.push(volume);
                }
            }
            ++i;
            ++it;
        } else {
            LOG(WARNING) << "a volume is not exist on master now, maybe dropped,"
                << " vid:" << (*it)->vid
                << " last_check_time:" << (*it)->last_check_time;
            ++it;
        }
    }
    _volumes.swap(new_volumes);
    _waiting_volume_queue.swap(new_waiting_volume_queue);
    _running_volumes.swap(new_running_volumes);
    _volume_check_queue_size.set_value(_running_volumes.size());
    _progress.set_value(_finished_volumes.size() / volume_info_list.size());
    return true;
}

int VolumeECCheckManager::load_snapshot() {
    common::ScopedMutexLock lock(_mutex);
    std::ifstream snapshot_file(FLAGS_ec_check_snapshot_path_file.c_str(), std::ios_base::in | std::ios_base::binary);
    if (!snapshot_file.is_open()) {
        LOG(WARNING) << "open snapshot file failed, file:" << FLAGS_ec_check_snapshot_path_file; 
        return AIE_NOT_EXIST;
    }
    aries::pb::ECCheckSnapshot snapshot;
    auto ret = snapshot.ParseFromIstream(&snapshot_file);
    if (!ret) {
        LOG(FATAL) << "load snapshot from:" << FLAGS_ec_check_snapshot_path_file << " failed";
        return AIE_FAIL;
    }
    _finished_volumes.clear();
    for (int i = 0; i < snapshot.vid_size(); i++) {
        _finished_volumes[snapshot.vid(i)] = true;
    }
    LOG(NOTICE) << " load snapshot finished count:" << _finished_volumes.size();
    return AIE_OK;
}

int VolumeECCheckManager::save_snapshot() {
    std::ofstream ofs;

    uint64_t time_now = base::gettimeofday_us();
    std::string tmp_file = 
        base::string_printf("%s.%lu.tmp", FLAGS_ec_check_snapshot_path_file.data(), time_now);
    ofs.open(tmp_file, std::ios::binary);
    if (!ofs.is_open()) {
        LOG(WARNING) << "open snapshot file failed, file:" << tmp_file; 
        return AIE_FAIL;
    }

    common::ScopedMutexLock lock(_mutex);
    aries::pb::ECCheckSnapshot snapshot;
    for (auto&it : _finished_volumes) {
        snapshot.add_vid(it.first);
    }
    snapshot.SerializeToOstream(&ofs);
    int ret = ::rename(tmp_file.c_str(), FLAGS_ec_check_snapshot_path_file.c_str());
    if (ret != 0) {
        LOG(FATAL) << "rename snapshot file failed, tmp_file:" << tmp_file
                << " file:" << FLAGS_ec_check_snapshot_path_file;
        return AIE_FAIL;
    }

    return AIE_OK;
}

int VolumeECCheckManager::init() {
    std::string snapshot_file = FLAGS_ec_check_snapshot_path_file;
    char *dirc = strndup(snapshot_file.c_str(), snapshot_file.size());
    char *dname = dirname(dirc);
    std::string snapshot_path = std::string(dname);
    free(dirc);
    if (!common::aries_mkdir(snapshot_path.c_str(), true)) {
        LOG(FATAL) << "init snapshot path failed, snapshot_path:" << snapshot_path;
        return AIE_FAIL;
    }

#ifndef _UNIT_TEST
    auto ret = load_snapshot();
    if (ret != AIE_OK && ret != AIE_NOT_EXIST) {
        LOG(FATAL) << "load snapshot failed";
        return AIE_FAIL;
    }
#endif
    return AIE_OK;

}

}
}

/* vim: set ts=4 sw=4 sts=4 tw=100 */
