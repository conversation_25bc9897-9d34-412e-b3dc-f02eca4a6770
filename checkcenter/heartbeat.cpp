/**
 * ARIES - A Reliable and Impressive EB-level Storage.
 * Copyright (c) 2018 Baidu.com, Inc. All Rights Reserved.
 *
 * Author: <PERSON> (<EMAIL>)
 * Date: 2018/04/20
 * Desciption: Implements of checkworker's heartbeat
 *
 */

#include "baidu/inf/aries-api/common/rpc_call.h"
#include "baidu/inf/aries-api/common/rpc_stub.h"
#include "baidu/inf/aries-api/common/types.h"
#include "baidu/inf/aries-api/common/master_tracker.h"
#include "baidu/inf/aries/checkcenter/heartbeat.h"
#include "baidu/inf/aries/checkcenter/flags.h"

namespace aries {
namespace checker {

#ifdef _UNIT_TEST
bool Heartbeat::_s_is_heartbeat_done = false;
#endif

void Heartbeat::heartbeat_rpc() {
    // refresh interval
    heartbeat_interval_second.store(aries::common::FLAGS_heartbeat_interval_second);
    g_master_tracker->update_refresh_interval_second(aries::common::FLAGS_refresh_master_interval_second);

    aries::pb::AckResponse* response = new aries::pb::AckResponse;
    aries::pb::CheckcenterHeartbeatRequest* request = new aries::pb::CheckcenterHeartbeatRequest;

    ::google::protobuf::Closure* done = ::baidu::rpc::NewCallback(
                            this, &Heartbeat::heartbeat_done, request, response);

    uint64_t req_addr  = common::endpoint2int(_listen_addr);
    request->set_req_addr(req_addr);
    request->set_token(FLAGS_token);
    request->set_version(__ARIES_VERSION_ID__);

    auto basic_metrics = request->mutable_basic_metrics();
    add_basic_metrics_in_heartbeat(basic_metrics);

#ifndef _UNIT_TEST
    base::EndPoint master_addr = g_master_tracker->get_master();
#else
    base::EndPoint master_addr = get_master_addr();
#endif
    LOG(NOTICE) << "heartbeat to master " << common::endpoint2str(master_addr);

    RpcCallOptions options;
    options.need_retry = false;
    options.redirect = true;
    MasterStub stub;
    stub.checkcenter_heartbeat(master_addr, request, response, done, &options);
}

void Heartbeat::heartbeat_done(aries::pb::CheckcenterHeartbeatRequest* request,
                               aries::pb::AckResponse* response) {
#ifdef _UNIT_TEST
    _s_is_heartbeat_done = true;
#endif
    if (response->status().code() != 0) {
        LOG(NOTICE) << "heartbeat with master failed,"
                << " errno:" << response->status().code()
                << " (" << response->status().msg() << ")"; 
    }
    delete request;
    delete response;
}

}
}
