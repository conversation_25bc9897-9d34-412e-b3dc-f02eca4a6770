/***************************************************************************
 * 
 * Copyright (c) 2020 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/
 
 /**
 * @file lostcheck_service.cpp
 * <AUTHOR>
 * @date 2020/05/28 17:26:59
 * @version 1.0 
 * @brief 
 *  
 **/

#include "baidu/inf/aries/checkcenter/volume_lostshard_check_manager.h"
#include "baidu/inf/aries/checkcenter/lostchecker_service.h"

namespace aries {
namespace checker {
void LostShardCheckServiceImpl::fetch_lost_check_volumes(::google::protobuf::Rp<PERSON><PERSON><PERSON>roller* controller,
                       const ::aries::pb::FetchLostShardCheckVolumesRequest* request,
                       ::aries::pb::FetchLostShardCheckVolumesResponse* response,
                       ::google::protobuf::Closure* done) {
    baidu::rpc::Controller* cntl = (baidu::rpc::Controller*)controller;
    uint64_t log_id = cntl->log_id();
    baidu::rpc::ClosureGuard done_guard(done);
    g_volume_lost_check_manager->fetch_volume_to_check(*request, response);
    ARIES_RPC_RECV_LOG(NOTICE) << " volume_id:" << response->volume_id()
        << " code:" << response->status().code()
        << " msg:" << response->status().msg();
}
void LostShardCheckServiceImpl::report_lost_check_results(::google::protobuf::RpcController* controller,
                       const ::aries::pb::ReportLostShardCheckResultsRequest* request,
                       ::aries::pb::AckResponse* response,
                       ::google::protobuf::Closure* done) {
    baidu::rpc::Controller* cntl = (baidu::rpc::Controller*)controller;
    uint64_t log_id = cntl->log_id();
    baidu::rpc::ClosureGuard done_guard(done);
    g_volume_lost_check_manager->report_check_result(*request);
    ARIES_RPC_RECV_LOG(NOTICE) << " succeeded,"
            << " vid:" << request->volume_id();
}
}
}
/* vim: set ts=4 sw=4 sts=4 tw=100 */
