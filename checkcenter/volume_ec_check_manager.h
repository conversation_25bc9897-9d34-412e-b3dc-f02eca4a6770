/***************************************************************************
 * 
 * Copyright (c) 2021 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/
 
 /**
 * @file volume_ec_check_manager.h
 * <AUTHOR>
 * @date 2021/09/07 10:06:21
 * @version 1.0 
 * @brief 
 *  
 **/

#pragma once

#include <unordered_map>

#include "baidu/inf/aries-api/common/proto/checker.pb.h"
#include "baidu/inf/aries/checkcenter/volume_manager.h"

namespace aries {
namespace checker {

class VolumeECCheckManager : public VolumeManager {
public:
    VolumeECCheckManager();

    bool fetch_volume_to_check(const aries::pb::FetchDataCheckVolumesRequest& request,
            aries::pb::FetchDataCheckVolumesResponse* response);

    void report_check_result(const aries::pb::ReportDataCheckResultsRequest& request);

    virtual bool update_volumes(const aries::pb::ListVolumesResponse& response);

    int load_snapshot();

    int save_snapshot();

    int init();

    ~VolumeECCheckManager() {}

private:
    std::map<uint64_t, bool> _finished_volumes;
};

extern VolumeECCheckManager* g_volume_ec_check_manager;

}
}

/* vim: set ts=4 sw=4 sts=4 tw=100 */
