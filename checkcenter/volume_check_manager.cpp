/**
 * ARIES - A Reliable and Impressive EB-level Storage.
 * Copyright (c) 2018 Baidu.com, Inc. All Rights Reserved.
 *
 * Author: <PERSON> (<EMAIL>)
 * Date: 2018/04/010
 * Desciption: Definition of checker volume check manager
 *
 */

#include <stdio.h>
#include <libgen.h>
#include <stdlib.h>
#include <bthread.h>
#include <bthread_types.h>
#include <fstream>

#include "baidu/inf/aries-api/common/bvar_define.h"
#include "baidu/inf/aries-api/common/config.h"
#include "baidu/inf/aries-api/common/master_tracker.h"
#include "baidu/inf/aries-api/common/rpc_call.h"
#include "baidu/inf/aries-api/common/rpc_stub.h"
#include "baidu/inf/aries-api/common/utils.h"
#include "baidu/inf/aries-api/common/proto/master.pb.h"
#include "baidu/inf/aries/checkcenter/volume_check_manager.h"
#include "baidu/inf/aries/checkcenter/flags.h"

namespace aries {
namespace checker {

ARIES_BVAR_COUNTER(checker, reclaim_volume_check);
ARIES_BVAR_COUNTER(checker, check_volume_failed);
ARIES_BVAR_RECORDER(checker, volume_check_queue_size);
ARIES_BVAR_RECORDER(checker, consistence_check_progress);
ARIES_BVAR_ADDER(checker, code_decode_err);
ARIES_BVAR_ADDER(checker, data_crc_err);
ARIES_BVAR_ADDER(checker, meta_not_equal);

ARIES_BVAR_ADDER(checker, bad_blob_size);

VolumeCheckManager *g_volume_check_manager = new VolumeCheckManager;

VolumeCheckManager::VolumeCheckManager()
        : VolumeManager(g_checker_reclaim_volume_check_counter,
        g_checker_check_volume_failed_counter,
        g_checker_volume_check_queue_size_recorder,
        g_checker_bad_blob_size_adder,
        g_checker_consistence_check_progress_recorder,
        FLAGS_check_volume_expire_time_s) {
}

bool VolumeCheckManager::fetch_volume_to_check(
        const aries::pb::FetchDataCheckVolumesRequest& request,
        aries::pb::FetchDataCheckVolumesResponse* response) {
    base::Timer timer;
    timer.start();
    auto status = response->mutable_status();

    common::ScopedMutexLock lock(_mutex);
    VolumeInfoPtr volume = nullptr;
    if (request.is_random_check()) {
        if (_random_waiting_volume_queue.size() == 0) {
            status->set_code(AIE_EMPTY);
            status->set_msg("the random waiting check volume queue is empty");
            return false;
        }
        volume = _random_waiting_volume_queue.top();
        _random_waiting_volume_queue.pop();

        assert(volume->is_checking == false);
        volume->is_checking = true;
        volume->last_fetch_time = base::gettimeofday_us();
        volume->checkworker_addr = request.req_addr();
        bool ok = _random_running_volumes.insert(std::make_pair(volume->vid, volume)).second;
        assert(ok);
    } else {
        if (_waiting_volume_queue.size() == 0) {
            status->set_code(AIE_EMPTY);
            status->set_msg("the waiting check volume queue is empty");
            return false;
        }

        /*
        * 对于io超限的volume进行暂存，避免被重复选取
        * 对于最近check过的volume不再check
        * 对于最终未被选中的volume调低优先级，避免频繁被下次fetch任务选中
        * 1个volume 1天只尝试check 1次
        */
        uint64_t now = base::gettimeofday_us();
        std::vector<VolumeInfoPtr> volume_tmp_storage;
        for (int i = 0; i < FLAGS_fetch_volume_max_retry_times; i++) {
            if (_waiting_volume_queue.size() == 0) {
                break;
            }
            
            volume = _waiting_volume_queue.top();
            _waiting_volume_queue.pop();
            if ((now - volume->last_check_time) < FLAGS_data_check_interval_s * 1000 * 1000) {
                volume_tmp_storage.push_back(volume);
                volume = nullptr;
                continue;
            }
            if ((now - volume->last_fetch_time) < FLAGS_data_check_retry_interval_s * 1000 * 1000) {
                volume_tmp_storage.push_back(volume);
                volume = nullptr;
                continue;
            }
            if (!FLAGS_enable_disk_io_dispatch) {
                break;
            }
            
            if (_disk_io_dispatcher.dispatch(volume->vid)) {
                break;
            }
            volume_tmp_storage.push_back(volume);
            volume = nullptr;
        }
        for (auto& it : volume_tmp_storage) {
            //delay x day when dispatch failed
            uint64_t delay = 24ULL * 3600 * 1000 * 1000 * FLAGS_data_check_failed_delay_times;
            it->priority += delay;
            _waiting_volume_queue.push(it);
        }
        if (volume == nullptr) {
            status->set_code(AIE_EMPTY);
            status->set_msg("no suitable volume to check");
            return false;
        }
        assert(volume->is_checking == false);
        volume->is_checking = true;
        volume->last_fetch_time = base::gettimeofday_us();
        volume->checkworker_addr = request.req_addr();
        bool ok = _running_volumes.insert(std::make_pair(volume->vid, volume)).second;
        assert(ok);
    }
    status->set_code(AIE_OK);
    status->set_msg("ok");
    response->set_volume_id(volume->vid);
    timer.stop();
    LOG(NOTICE) << "fetch a volume to check,"
        << " vid:" << volume->vid
        << " checkworker_addr:" << common::endpoint2str(common::int2endpoint(request.req_addr()))
        << " last_check_time:" << volume->last_check_time
        << " cost_ms:" << timer.m_elapsed();

    return true;
}

void VolumeCheckManager::report_check_result(
        const aries::pb::ReportDataCheckResultsRequest& request) {

    common::ScopedMutexLock lock(_mutex);
    uint64_t now = base::gettimeofday_us();
    int64_t old_bad_blob_size = 0;
    int64_t new_bad_blob_size = 0;
    for (int i = 0; i < request.volumes_size(); ++i) {
        auto& finished_volume = request.volumes(i);
        auto vid = finished_volume.volume_id();
        if (request.is_random_check()) {
            auto it = _random_running_volumes.find(vid);
            if (ARIES_UNLIKELY(it == _random_running_volumes.end())) {
                LOG(WARNING) << "discard report check result due to volume is not in random checking,"
                    << " vid:" << vid;
            } else {
                auto volume = it->second;
                volume->last_check_time = now;
                volume->priority = volume->last_check_time;
                volume->is_checking = false;
                volume->check_failed_cnt = 0;
                _random_running_volumes.erase(it);
                _random_waiting_volume_queue.push(volume);
            }
        } else {
            auto it = _running_volumes.find(vid);
            if (ARIES_UNLIKELY(it == _running_volumes.end())) {
                LOG(WARNING) << "discard report check result due to volume is not in checking,"
                    << " vid:" << vid;
            } else {
                auto volume = it->second;
                volume->last_check_time = now;
                volume->priority = volume->last_check_time;
                volume->is_checking = false;
                volume->check_failed_cnt = 0;
                _running_volumes.erase(it);
                _waiting_volume_queue.push(volume);
                _checker_finish_counter++;
                _finished_volumes[vid] = volume->last_check_time;
                if (FLAGS_enable_disk_io_dispatch) {
                    _disk_io_dispatcher.report(volume->vid);
                }

                //record bad blobs num
                auto bad_blob_counter_it = _bad_blob_counter.find(vid);
                BadBlobCounter counter;
                counter.code_decode_err_num = finished_volume.code_decode_err_num();
                counter.data_crc_err_num = finished_volume.data_crc_err_num();
                counter.meta_not_equal_num = finished_volume.meta_not_equal_num();
                if (counter.code_decode_err_num + counter.data_crc_err_num + counter.meta_not_equal_num > 0) {
                    if (bad_blob_counter_it != _bad_blob_counter.end()) {
                        g_checker_code_decode_err_adder << counter.code_decode_err_num -
                            bad_blob_counter_it->second.code_decode_err_num;
                        g_checker_data_crc_err_adder << counter.data_crc_err_num -
                            bad_blob_counter_it->second.data_crc_err_num;
                        g_checker_meta_not_equal_adder << counter.meta_not_equal_num -
                            bad_blob_counter_it->second.meta_not_equal_num;
                    }
                    _bad_blob_counter[vid] = counter;
                } else {
                    if (bad_blob_counter_it != _bad_blob_counter.end()) {
                        g_checker_code_decode_err_adder << bad_blob_counter_it->second.code_decode_err_num * -1;
                        g_checker_data_crc_err_adder << bad_blob_counter_it->second.data_crc_err_num * -1;
                        g_checker_meta_not_equal_adder << bad_blob_counter_it->second.meta_not_equal_num * -1;
                        _bad_blob_counter.erase(bad_blob_counter_it);
                    }
                }
                LOG(NOTICE) << "finish check volume," << " vid:" << vid
                    << " cost_min:" << ((volume->last_check_time - volume->last_fetch_time) / 1000 / 1000 / 60);
            }
        }

        // remove all old result for vid
        old_bad_blob_size += _bad_blobs[vid].size();
        _bad_blobs.erase(vid);
        new_bad_blob_size += finished_volume.blobs_size();
    }
    _bad_blob_size << (new_bad_blob_size - old_bad_blob_size);
    for (int i = 0; i < request.failed_volumes_size(); ++i) {
        auto vid = request.failed_volumes(i);
        uint64_t check_failed_cnt = 0;
        if (request.is_random_check()) {
            auto it = _random_running_volumes.find(vid);
            if (ARIES_UNLIKELY(it == _random_running_volumes.end())) {
                LOG(WARNING) << "discard report check result due to volume is not in random checking,"
                    << " vid:" << vid;
            } else {
                auto volume = it->second;
                volume->is_checking = false;
                volume->check_failed_cnt += 1;
                check_failed_cnt = volume->check_failed_cnt;
                _random_running_volumes.erase(it);
                _random_waiting_volume_queue.push(volume);
                _check_volume_failed << check_failed_cnt;
            }
        } else {
            auto it = _running_volumes.find(vid);
            if (ARIES_UNLIKELY(it == _running_volumes.end())) {
                LOG(WARNING) << "discard report check result due to volume is not in checking,"
                    << " vid:" << vid;
            } else {
                auto volume = it->second;
                volume->is_checking = false;
                volume->check_failed_cnt += 1;
                check_failed_cnt = volume->check_failed_cnt;
                _running_volumes.erase(it);
                //delay x day in case this task failed frequently
                uint64_t delay = 24ULL * 3600 * 1000 * 1000 * FLAGS_data_check_failed_delay_times;
                volume->priority += delay;
                _waiting_volume_queue.push(volume);
                if (FLAGS_enable_disk_io_dispatch) {
                    _disk_io_dispatcher.report(volume->vid);
                }
                _check_volume_failed << check_failed_cnt;
            }
        }
        LOG(WARNING) << "check volume fail,"
            << " vid:" << vid << " check_failed_cnt:" << check_failed_cnt;
    }
}

void VolumeCheckManager::reclaim_expired_volumes() {
    common::ScopedMutexLock lock(_mutex);
    uint64_t now = base::gettimeofday_us();
    uint64_t expire_time = FLAGS_check_volume_expire_time_s * 1000000;
    for (auto it = _running_volumes.begin(); it != _running_volumes.end();) {
        auto volume = it->second;
        assert(volume->is_checking);
        if (volume->last_fetch_time + expire_time < now) {
            volume->is_checking = false;
            volume->check_failed_cnt += 1;
            _running_volumes.erase(it++);
            //delay x day in case this task failed frequently
            uint64_t delay = 24ULL * 3600 * 1000 * 1000 * FLAGS_data_check_failed_delay_times;
            volume->priority += delay;
            _waiting_volume_queue.push(volume);
            if (FLAGS_enable_disk_io_dispatch) {
                _disk_io_dispatcher.report(volume->vid);
            }
            LOG(WARNING) << "a volume checking task expired,"
                << " vid:" << volume->vid
                << " last_fetch_time:" << volume->last_fetch_time
                << " check_failed_cnt:" << volume->check_failed_cnt
                << " checkworker:" << common::endpoint2str(common::int2endpoint(volume->checkworker_addr));
            _reclaim_volume_check << 1;
        }
        else {
            ++it;
        }
    }
    for (auto it = _random_running_volumes.begin(); it != _random_running_volumes.end();) {
        auto volume = it->second;
        assert(volume->is_checking);
        if (volume->last_fetch_time + expire_time < now) {
            volume->is_checking = false;
            volume->check_failed_cnt += 1;
            _random_running_volumes.erase(it++);
            _random_waiting_volume_queue.push(volume);
            LOG(WARNING) << "a volume random checking task expired,"
                << " vid:" << volume->vid
                << " last_fetch_time:" << volume->last_fetch_time
                << " check_failed_cnt:" << volume->check_failed_cnt
                << " checkworker:" << common::endpoint2str(common::int2endpoint(volume->checkworker_addr));
            _reclaim_volume_check << 1;
        }
        else {
            ++it;
        }
    }
}

int VolumeCheckManager::load_snapshot() {
    common::ScopedMutexLock lock(_mutex);
    std::ifstream snapshot_file(FLAGS_volume_check_snapshot_path_file.c_str(), std::ios_base::in | std::ios_base::binary);
    if (!snapshot_file.is_open()) {
        LOG(WARNING) << "open snapshot file failed, file:" << FLAGS_volume_check_snapshot_path_file; 
        return AIE_NOT_EXIST;
    }
    aries::pb::VolumeCheckSnapshot snapshot;
    auto ret = snapshot.ParseFromIstream(&snapshot_file);
    if (!ret) {
        LOG(FATAL) << "load snapshot from:" << FLAGS_volume_check_snapshot_path_file << " failed";
        return AIE_FAIL;
    }
    _finished_volumes.clear();
    for (int i = 0; i < snapshot.volume_check_info_size(); i++) {
        _finished_volumes[snapshot.volume_check_info(i).vid()] = snapshot.volume_check_info(i).last_check_time();
    }
    LOG(NOTICE) << " load snapshot finished count:" << _finished_volumes.size();
    return AIE_OK;
}

int VolumeCheckManager::save_snapshot() {
    std::ofstream ofs;

    uint64_t time_now = base::gettimeofday_us();
    std::string tmp_file = 
        base::string_printf("%s.%lu.tmp", FLAGS_volume_check_snapshot_path_file.data(), time_now);
    ofs.open(tmp_file, std::ios::binary);
    if (!ofs.is_open()) {
        LOG(WARNING) << "open snapshot file failed, file:" << tmp_file; 
        return AIE_FAIL;
    }

    common::ScopedMutexLock lock(_mutex);
    aries::pb::VolumeCheckSnapshot snapshot;
    for (auto&it : _finished_volumes) {
        auto volume_check_info = snapshot.add_volume_check_info();
        volume_check_info->set_vid(it.first);
        volume_check_info->set_last_check_time(it.second);
    }
    snapshot.SerializeToOstream(&ofs);
    int ret = ::rename(tmp_file.c_str(), FLAGS_volume_check_snapshot_path_file.c_str());
    if (ret != 0) {
        LOG(FATAL) << "rename snapshot file failed, tmp_file:" << tmp_file
                << " file:" << FLAGS_volume_check_snapshot_path_file;
        return AIE_FAIL;
    }

    return AIE_OK;
}

int VolumeCheckManager::init() {
    std::string snapshot_file = FLAGS_volume_check_snapshot_path_file;
    char *dirc = strndup(snapshot_file.c_str(), snapshot_file.size());
    char *dname = dirname(dirc);
    std::string snapshot_path = std::string(dname);
    free(dirc);
    if (!common::aries_mkdir(snapshot_path.c_str(), true)) {
        LOG(FATAL) << "init snapshot path failed, snapshot_path:" << snapshot_path;
        return AIE_FAIL;
    }

    auto ret = load_snapshot();
    if (ret != AIE_OK) {
        if (ret != AIE_NOT_EXIST) {
            LOG(FATAL) << "load snapshot failed";
            return AIE_FAIL;
        }
    }
    _checker_finish_counter = _finished_volumes.size();
    return AIE_OK;
}

}
}
