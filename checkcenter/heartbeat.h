/**
 * ARIES - A Reliable and Impressive EB-level Storage.
 * Copyright (c) 2018 Baidu.com, Inc. All Rights Reserved.
 *
 * Author: GaoTianxing (<EMAIL>)
 * Date: 2018/04/20
 * Desciption: Declaration of checkworker's heartbeat
 *
 */

#ifndef BAIDU_INF_ARIES_CHECKCENTER_HEARTBEAT_H
#define BAIDU_INF_ARIES_CHECKCENTER_HEARTBEAT_H

#include <atomic>
#include <base/endpoint.h>
#include <bthread.h>
#include <bthread_unstable.h>
#include "baidu/inf/aries-api/common/heartbeat.h"
#include "baidu/inf/aries-api/common/proto/common.pb.h"
#include "baidu/inf/aries-api/common/proto/master.pb.h"

namespace aries {
namespace checker {

class Heartbeat : public common::Heartbeat {
public:
    virtual void heartbeat_rpc();
    void heartbeat_done(aries::pb::CheckcenterHeartbeatRequest* request,
                        aries::pb::AckResponse* response);
#ifdef _UNIT_TEST
protected:
    virtual base::EndPoint get_master_addr() {
        return base::EndPoint();
    }
    static bool is_heartbeat_done() {
        return _s_is_heartbeat_done;
    }
    static bool _s_is_heartbeat_done;
#endif
};

}
}

#endif
