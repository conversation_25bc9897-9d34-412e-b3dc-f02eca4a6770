/*************************************************************************
 *
 * Copyright (c) Baidu.com, Inc. All Rights Reserved
 *
 ************************************************************************/

/**
 * @file mock_master.h
 * <AUTHOR>
 * @date Thu 12 Apr 2018 05:08:13 PM CST
 * @brief 
 *
 **/

#ifndef BAIDU_INF_ARIES_CHECKER_TEST_MOCK_MASTER_H
#define BAIDU_INF_ARIES_CHECKER_TEST_MOCK_MASTER_H

#include <baidu/rpc/server.h>
#include "baidu/inf/aries-api/common/proto/master.pb.h"
#include "baidu/inf/aries-api/common/common.h"
#include "baidu/inf/aries-api/common/types.h"

namespace aries {
namespace checker {

class MockMaster : public aries::pb::MasterQueryService {
public:
    virtual void list_volumes(::google::protobuf::RpcController* controller,
            const ::aries::pb::ListVolumesRequest* request,
            ::aries::pb::ListVolumesResponse* response,
            ::google::protobuf::Closure* done) {

    }
private:
};

}
}

#endif
