/*************************************************************************
 *
 * Copyright (c) Baidu.com, Inc. All Rights Reserved
 *
 ************************************************************************/

/**
 * @file checkcenter/test/mock_datanode.h
 * <AUTHOR>
 * @date Fri 20 Apr 2018 06:58:40 PM CST
 * @brief 
 *
 **/

#ifndef BAIDU_INF_ARIES_CHECKER_TEST_MOCK_DATANODE_H
#define BAIDU_INF_ARIES_CHECKER_TEST_MOCK_DATANODE_H

#include <base/crc32c.h>
#include "baidu/rpc/server.h"
#include "baidu/inf/aries-api/common/proto/datanode.pb.h"
#include "baidu/inf/aries-api/common/common.h"
#include "baidu/inf/aries-api/common/types.h"
#include "baidu/inf/aries-api/common/rpc_call.h"
#include "baidu/inf/aries/repairer/global.h"

namespace aries {
namespace checker {

static const uint64_t kTotalShardCount = 200 * 1024;
static uint64_t g_max_vbids[9];
static std::vector<uint64_t> g_mark_deleted_vbids[9];

static std::vector<uint64_t> g_vbid_list[9];

static int g_list_blob_response_errors[9] = {
    AIE_OK, AIE_OK, AIE_OK, AIE_OK, AIE_OK, AIE_OK, AIE_OK, AIE_OK, AIE_OK,
};

/*
 * Mock DataNode DataService
 */
class MockDataNodeDataService : public ::aries::pb::DataNodeDataService {
public:
    MockDataNodeDataService() {}
    virtual ~MockDataNodeDataService() {}

    virtual void list_blob(::google::protobuf::RpcController* controller,
                       const ::aries::pb::ListBlobRequest* request,
                       ::aries::pb::ListBlobResponse* response,
                       ::google::protobuf::Closure* done) {
        int shard_index = request->shard_index();
        int error = g_list_blob_response_errors[shard_index];
        response->mutable_status()->set_code(error);
        response->mutable_status()->set_msg(errno2msg(error));

        if (error == AIE_TIMEOUT) {
            sleep((FLAGS_call_timeout_ms + 3000) / 1000);
        } else if (error == AIE_OK) {
            response->set_max_vbid(g_max_vbids[shard_index]);
            for (size_t i = 0; i < g_vbid_list[shard_index].size(); ++i) {
                response->add_vbid_list(g_vbid_list[shard_index][i]);
            }
            std::vector<uint64_t>& mark_deleted_vbids = g_mark_deleted_vbids[shard_index];
            for (size_t i = 0; i < mark_deleted_vbids.size(); ++i) {
                response->add_mark_deleted_vbid_list(mark_deleted_vbids[i]);
            }
        }

        done->Run();
    }
};

}
}

#endif
