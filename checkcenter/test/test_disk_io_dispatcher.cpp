/***************************************************************************
 * 
 * Copyright (c) 2020 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/
 
 /**
 * @file test_disk_io_dispatcher.cpp
 * <AUTHOR>
 * @date 2020/09/01 11:29:47
 * @version 1.0 
 * @brief 
 *  
 **/

#include <gtest/gtest.h>
#include <base/logging.h>
#include "baidu/inf/aries/checkcenter/flags.h"
#include "baidu/inf/aries-api/common/proto/enum.pb.h"
#include "baidu/inf/aries/checkcenter/disk_io_dispatcher.h"
#include "baidu/inf/aries/checkcenter/test/mock_meta_client.h"

namespace aries {
namespace checker {

class MockDiskIODispatcher : public DiskIODispatcher {
};

class DiskIODispatcherTests : public ::testing::Test {
    DiskIODispatcherTests() {
        g_meta_replica_client.reset(new MockMetaClient());
    }
    virtual ~DiskIODispatcherTests() {}
};

TEST_F(DiskIODispatcherTests, dispatch) {
    FLAGS_max_check_task_per_disk = 1;
    FLAGS_enable_disk_io_dispatch = true;
    FLAGS_fetch_volume_max_retry_times = 2;
    MockMetaClient::init_volumes_with_spaces();
    MockMetaClient::let_init_succ();
    MockMetaClient::let_find_volume_succ();
    MockDiskIODispatcher dispatcher;
    bool ret = dispatcher.dispatch(10000);
    ASSERT_EQ(true, ret);
    ret = dispatcher.dispatch(10001);
    ASSERT_EQ(false, ret);
    dispatcher.report(10000);
    ret = dispatcher.dispatch(10001);
    ASSERT_EQ(true, ret);
}

}
}

int main(int argc, char* argv[]) {
    ::testing::InitGoogleTest(&argc, argv);
    return RUN_ALL_TESTS();
}
/* vim: set ts=4 sw=4 sts=4 tw=100 */
