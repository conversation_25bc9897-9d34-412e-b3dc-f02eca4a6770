/*************************************************************************
 *
 * Copyright (c) Baidu.com, Inc. All Rights Reserved
 *
 ************************************************************************/

/**
 * @file test_lostshard_checker.cpp
 * <AUTHOR>
 * @date Thu 12 Apr 2018 05:08:41 PM CST
 * @brief 
 *
 **/

#include "baidu/inf/aries/checkcenter/lostshard_checker.h"
#include "baidu/inf/aries/checkcenter/test/mock_meta_client.h"
#include <gtest/gtest.h>

namespace aries {
namespace checker {

static int g_master_port = 63178;
static int g_local_port = 63179;

class MockMaster : public aries::pb::MasterQueryService {
public:
    virtual void list_volumes(::google::protobuf::RpcController* controller,
            const ::aries::pb::ListVolumesRequest* request,
            ::aries::pb::ListVolumesResponse* response,
            ::google::protobuf::Closure* done) {
        baidu::rpc::Controller* cntl = (baidu::rpc::Controller*)controller;
        baidu::rpc::ClosureGuard done_guard(done);
        auto status = response->mutable_status();
        
        for (int i = 0; i < 100; ++i) {
            volumes.push_back(i + 100);
        }

        int space_num = 0;
        std::string space;
        uint64_t now = base::gettimeofday_s();
        for (auto& volume : volumes) {
            auto volume_info = response->add_volumes();
            volume_info->set_volume_id(volume);
            volume_info->set_create_time(now);
        }
        status->set_code(AIE_OK);
        status->set_msg("ok");
    }
private:
    std::vector<uint64_t> volumes;
};

class TestLostShardChecker : public ::testing::Test {
public:
    void SetUp() {
        data_path = "ut_checker_store";
        FLAGS_previous_ranks_num = 3;
        system("rm -rf ut_checker_store");
    }
    void TearDown() {
        system("rm -rf ut_checker_store");
    }

private:
    std::string data_path;
};

TEST_F(TestLostShardChecker, fetch_data_from_master) {
    g_meta_replica_client.reset(new MockMetaClient());
    MockMetaClient::init_volumes_with_spaces();
    MockMetaClient::let_init_succ();
    MockMetaClient::let_find_volume_succ();
    MockMetaClient::set_volume_count(3);
    std::shared_ptr<common::TokenPool> checker_token_pool = std::make_shared<common::TokenPool>(3, 1024);
    checker_token_pool->start();

    std::shared_ptr<LostShardChecker> checker = std::make_shared<LostShardChecker>(checker_token_pool);
    MockMaster master_server;
    base::EndPoint listen_addr;
    baidu::rpc::Server server;
    ASSERT_EQ(0, base::str2endpoint("127.0.0.1", g_master_port, &listen_addr));
    ASSERT_EQ(0, server.AddService(&master_server, baidu::rpc::SERVER_DOESNT_OWN_SERVICE));
    ASSERT_EQ(0, server.Start(listen_addr, NULL));
    common::s_master_addr = listen_addr;

    checker->fetch_data_check_volume();

    ASSERT_EQ(3, checker->_cur_volumes.size());
    ASSERT_EQ(3, checker->_check_context->sync_point->count());
}

TEST_F(TestLostShardChecker, delete_old_volumes) {
    std::shared_ptr<common::TokenPool> checker_token_pool = std::make_shared<common::TokenPool>(3, 1024);
    checker_token_pool->start();

    std::shared_ptr<LostShardChecker> checker = std::make_shared<LostShardChecker>(checker_token_pool);
    
    checker->_check_context->lost_shards_store.reset(DataStoreFactory::get_store(data_path, 0));
    RecordValue tmp_value;
    tmp_value.set_data(1234);
    for (int i = 0; i < 100; ++i) {
        if (i % 20 > 0) {
            checker->_cur_volumes.insert(i+100);
        }
        RecordKey key(i+100, i+1, 0);
        checker->_check_context->lost_shards_store->put(key, tmp_value);
    }

    ASSERT_TRUE(checker->delete_old_volumes());

    RecordKey begin_key(0, 0, 0);
    std::unique_ptr<DataStore::Iterator> it(checker->_check_context->lost_shards_store->seek(begin_key));
    size_t delete_num = 0;
    size_t total_num = 0;

    std::shared_ptr<RecordKey> key = nullptr;
    std::shared_ptr<RecordValue> value = nullptr;
    while (it->valid()) {
        key = it->key();
        // volume does not exist in current check rank
        if (checker->_cur_volumes.find(key->vid()) == checker->_cur_volumes.end()) {
            ++delete_num;
        }
        ++total_num;
        it->next();
    }

    ASSERT_EQ(0, delete_num);
    ASSERT_EQ(95, total_num);
}

TEST_F(TestLostShardChecker, list_unrepaired_shards) {
    std::shared_ptr<common::TokenPool> checker_token_pool = std::make_shared<common::TokenPool>(3, 1024);
    checker_token_pool->start();

    std::shared_ptr<LostShardChecker> checker = std::make_shared<LostShardChecker>(checker_token_pool);
    
    checker->_check_context->lost_shards_store.reset(DataStoreFactory::get_store(data_path, 0));
    RecordValue tmp_value;
    for (int i = 0; i < 100; ++i) {
        if (i % 5 > 0) {
            tmp_value.set_data(4);
        } else {
            tmp_value.set_data(2);
        }
        RecordKey key(i+100, i+1, 0);
        checker->_check_context->lost_shards_store->put(key, tmp_value);
    }

    aries::pb::ListUnrepairedShardsResponse response;
    checker->list_unrepaired_shards(0, &response);

    ASSERT_EQ(80, static_cast<int>(response.shards_size()));

    response.Clear();
    checker->list_unrepaired_shards(20, &response);
    ASSERT_EQ(20, static_cast<int>(response.shards_size()));
}

}
}

int main(int argc, char* argv[]) {
    ::testing::InitGoogleTest(&argc, argv);
    return RUN_ALL_TESTS();
}