/*************************************************************************
 *
 * Copyright (c) Baidu.com, Inc. All Rights Reserved
 *
 ************************************************************************/

/**
 * @file aries/checkcenter/diff_common.h
 * <AUTHOR>
 * @date Mon 02 Apr 2018 03:45:37 PM CST
 * @brief 
 *
 **/

#ifndef BAIDU_INF_ARIES_CHECKER_DIFF_COMMON_H
#define BAIDU_INF_ARIES_CHECKER_DIFF_COMMON_H

#include <memory>
#include <queue>
#include <list>
#include "baidu/inf/aries-api/common/proto/checker.pb.h"
#include "baidu/inf/aries-api/common/proto/master.pb.h"
#include "baidu/inf/aries-api/common/common.h"
#include "baidu/inf/aries-api/common/config.h"
#include "baidu/inf/aries-api/common/lock.h"
#include "baidu/inf/aries/checkcenter/flags.h"
#include "baidu/inf/aries/checkcenter/data_store.h"
#include <base/endpoint.h>

namespace aries {
namespace checker {

// copy from diff_worker.h
struct VolumeCheckContext {
    struct BlobCtx {
        struct ShardCtx {
            int shard_index;
            uint32_t version;
            base::EndPoint addr;
            bool is_lost;
            bool is_unknown;
            bool is_mark_deleted;
            bool is_uncommitted;
            ShardCtx() : shard_index(-1), version(0), is_lost(false), is_unknown(false),
                         is_mark_deleted(false), is_uncommitted(false) {}
        };

        uint64_t vbid;
        std::vector<std::shared_ptr<ShardCtx>> shards;
        bool has_uncommitted_shards;

        BlobCtx() : vbid(0), has_uncommitted_shards(false) {}
        ~BlobCtx() {
            for (auto & shard : shards) { shard.reset(); }
            shards.clear();
        }
        void init(int n) {
            shards.resize(n);
        }
    };

    struct VletCtx {
        int shard_index;
        uint32_t version;
        uint64_t total_shard_count;
        base::EndPoint addr;
        bool is_lost;
        bool is_timeout;
        bool is_failed;
        uint64_t slot_num;
        std::vector<uint32_t> finger_prints;
        std::list<uint64_t> mark_deleted_blobs;
        std::list<uint64_t> uncommitted_blobs;
        __gnu_cxx::hash_map<uint32_t, std::set<uint64_t>* > blob_lists; // slotno -> bloblist

        VletCtx() : shard_index(-1), version(0), total_shard_count(0), is_lost(false),
                is_timeout(false), is_failed(false), slot_num(0) {}
        ~VletCtx() {
            for (auto & pair : blob_lists) { delete pair.second; }
            blob_lists.clear();
            finger_prints.clear();
        }
    };

    std::shared_ptr<common::MutexLock> mutex;
    uint64_t volume_id;
    int priority;
    ECOption eco;
    std::vector<std::shared_ptr<VletCtx> > vlets;

    uint32_t slot_num;
    uint64_t max_vbid;
    std::vector<uint32_t> inconsistent_slots;

    __gnu_cxx::hash_map<uint64_t, std::shared_ptr<BlobCtx>> mark_deleted_blobs;
    __gnu_cxx::hash_map<uint64_t, std::shared_ptr<BlobCtx>> need_repaired_blobs;

    // for rpc
    std::shared_ptr<common::SyncPoint> sync_point;
    int8_t pending_vlet_num;
    int8_t succ_vlet_num;
    int8_t failed_vlet_num;
    int8_t timeout_vlet_num;
    uint64_t log_id;

    bool check_failed = false;

    VolumeCheckContext() : volume_id(0) {
        mutex = std::shared_ptr<common::MutexLock>(new common::MutexLock());
        priority = 0;
        slot_num = 0;
        max_vbid = 0;
        log_id = UINT64_MAX;
        init_counters();
    }
    ~VolumeCheckContext() {
        mutex.reset();
        sync_point.reset();
        for (auto & vlet : vlets) { vlet.reset(); }
        vlets.clear();
        for (auto & pair : mark_deleted_blobs) { pair.second.reset(); }
        mark_deleted_blobs.clear();
        for (auto & pair : need_repaired_blobs) { pair.second.reset(); }
        need_repaired_blobs.clear();
    }
    void init_counters() {
        succ_vlet_num = 0;
        failed_vlet_num = 0;
        timeout_vlet_num = 0;
        pending_vlet_num = 0;
    }
};

typedef common::ConcurrentQueue<uint64_t>  VQueue;

struct LostCheckContext {
    // for synchronization between main thead and worker thread
    common::MutexLock mutex;
    std::shared_ptr<common::SyncPoint> sync_point;
    std::shared_ptr<DataStore> lost_shards_store;
};

}
}

#endif
