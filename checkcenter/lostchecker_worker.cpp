/*************************************************************************
 *
 * Copyright (c) Baidu.com, Inc. All Rights Reserved
 *
 ************************************************************************/

/**
 * @file aries/checkcenter/lostchecker_worker.cpp
 * <AUTHOR>
 * @date Tue 03 Apr 2018 10:46:21 AM CST
 * @brief 
 *
 **/

#include "baidu/inf/aries/checkcenter/lostchecker_worker.h"
#include "baidu/inf/aries-api/common/proto/datanode.pb.h"
#include "baidu/inf/aries-api/common/closure.h"

namespace aries {
namespace checker {

LostCheckerWorker::LostCheckerWorker(std::shared_ptr<common::TokenPool> token_pool,
            std::shared_ptr<VQueue> volume_list,
            std::shared_ptr<LostCheckContext> check_context) :
    _is_stopped(true), _token_pool(token_pool), _volumes(volume_list), 
        _check_context(check_context) {
}

LostCheckerWorker::~LostCheckerWorker() {
    if (!_is_stopped) {
        stop();
    }
}

void LostCheckerWorker::stop() {
    _is_stopped = true;
    LOG(WARNING) << "LostCheckerWorker begin to stop";
    join();
}

void LostCheckerWorker::join() {
    LOG(WARNING) << "LostCheckerWorker begin to join";
    _thread.join();
}

bool LostCheckerWorker::start() {
    _is_stopped = false;
    _thread = std::thread(run_thread, this);
    return true;
}

void LostCheckerWorker::run_thread(LostCheckerWorker* worker) {
    worker->run();
}

std::shared_ptr<VolumeCheckContext> LostCheckerWorker::get_volume(uint64_t volume_id) {
    aries::pb::MetaReplica::VolumeInfo volume_info;
    aries::pb::SpaceInfo space_info;
    auto err = g_meta_replica_client->get_volume_info(volume_id, &volume_info, &space_info);
    if ( err != AIE_OK) {
        LOG(WARNING) << "get volume info failed vid:" << volume_id
            << " err:" << err;
        return nullptr;
    }
    if (volume_info.has_location_on_tape()) {
        LOG(NOTICE) << "skip migrated volume, vid:" << volume_id;
        return nullptr;
    }
    if (volume_info.volume_state() == VOLUME_STATE_DROPPED) {
        LOG(NOTICE) << "skip dropped volume, vid:" << volume_id;
        return nullptr;
    }
    std::shared_ptr<VolumeCheckContext> volctx(new VolumeCheckContext);
    volctx->volume_id = volume_id;
    volctx->eco.type = static_cast<ECType>(space_info.ec_type());
    volctx->eco.param.k = space_info.k();
    volctx->eco.param.n = space_info.n();
    volctx->vlets.resize(space_info.n()); // make sure N vlets

    for (ssize_t i = 0; i < volume_info.vlet_info_size(); ++i) {
        const auto& vlet = volume_info.vlet_info(i);
        auto vlet_ctx = std::make_shared<VolumeCheckContext::VletCtx>();
        vlet_ctx->shard_index = vlet.shard_index();
        vlet_ctx->addr = common::int2endpoint(vlet.node_addr());
        volctx->vlets[i] = vlet_ctx;
    }

    return volctx;
}

void LostCheckerWorker::list_blobs(std::shared_ptr<VolumeCheckContext> volctx) {
    assert(volctx != NULL);

    {
        volctx->mutex->lock();

        volctx->init_counters();
        volctx->log_id = base::fast_rand();
        uint64_t log_id = volctx->log_id;
        ARIES_RPC_LOG(NOTICE) << "list blobs, vid:" << volctx->volume_id;

        for (size_t i = 0; i < volctx->vlets.size(); ++i) {
            auto vlet = volctx->vlets[i];
            if (vlet->addr == common::int2endpoint(0)) {
                continue;
            }
            vlet->is_timeout = false;
            vlet->is_failed = false;
            aries::pb::ListBlobResponse* response = new aries::pb::ListBlobResponse;
            aries::pb::ListBlobRequest* request = new aries::pb::ListBlobRequest;
            request->set_token(FLAGS_token);
            request->set_volume_id(volctx->volume_id);
            request->set_shard_index(vlet->shard_index);

            ::google::protobuf::Closure* done = ::baidu::rpc::NewCallback(this,
                    &LostCheckerWorker::list_blobs_done, request, response, volctx);
            
            DataNodeStub stub;
            base::EndPoint data_addr = this->get_data_service_addr(vlet->addr);
            RpcCallOptions options;
            options.log_id = volctx->log_id;
            options.call_timeout_ms = FLAGS_list_blob_timeout_ms;
            stub.list_blob(data_addr, request, response, done, &options);

            ++volctx->pending_vlet_num;
        }
        volctx->sync_point = std::shared_ptr<common::SyncPoint>(
                                        new common::SyncPoint(volctx->pending_vlet_num));
        volctx->mutex->unlock();
    }

    volctx->sync_point->wait();
    volctx->sync_point.reset();
    LOG(NOTICE) << "list blobs finished,"
        << " vid:" << volctx->volume_id
        << " k:" << volctx->eco.param.k << " n:" << volctx->eco.param.n
        << " succ_vlets:" << (int)volctx->succ_vlet_num
        << " failed_vlets:" << (int)volctx->failed_vlet_num
        << " timeout_vlets:" << (int)volctx->timeout_vlet_num;

    find_lost_shards(volctx);
}

void LostCheckerWorker::list_blobs_done(aries::pb::ListBlobRequest* request,
                                             aries::pb::ListBlobResponse* response,
                                             std::shared_ptr<VolumeCheckContext> volctx) {
    assert(request);
    assert(response);
    assert(volctx.get());
    std::unique_ptr<aries::pb::ListBlobRequest> request_guard(request);
    std::unique_ptr<aries::pb::ListBlobResponse> response_guard(response);
    
    // flow controll
    _token_pool->get_force(response->ByteSize());

    uint64_t volume_id = request->volume_id();
    assert(volume_id == volctx->volume_id);

    {
        volctx->mutex->lock();
        
        int shard_index = request->shard_index();
        assert(shard_index >= 0 && shard_index < (int)volctx->vlets.size());
        auto vlet = volctx->vlets[shard_index];
        base::EndPoint data_addr = this->get_data_service_addr(vlet->addr);
        uint64_t log_id = volctx->log_id;

        int code = response->status().code();
        if (code == AIE_TIMEOUT) {
            ARIES_RPC_LOG(WARNING) << "list blobs failed from addr "
                << common::endpoint2str(data_addr)
                << " vid:" << volume_id << " shard_index:" << shard_index
                << " error:" << code << " (" << response->status().msg() << ")";
            vlet->is_timeout = true;
            ++volctx->timeout_vlet_num;
        } else if (code != AIE_OK) {
            ARIES_RPC_LOG(WARNING) << "list blobs failed from addr "
                << common::endpoint2str(data_addr)
                << " vid:" << volume_id << " shard_index:" << shard_index
                << " error:" << code << " (" <<  response->status().msg() << ")";
            vlet->is_failed = true;
            ++volctx->failed_vlet_num;
        } else {
            ARIES_RPC_LOG(NOTICE) << "list blobs succeeded from addr "
                << common::endpoint2str(data_addr)
                << " vid:" << volume_id << " shard_index:" << shard_index;
            ++volctx->succ_vlet_num;

            vlet->version = response->vlet_version();
            
            if (volctx->max_vbid < response->max_vbid()) {
                volctx->max_vbid = response->max_vbid();
            }

            // aggregate deleted blob
            for (int i = 0; i < response->mark_deleted_vbid_list_size(); ++i) {
                uint64_t vbid = response->mark_deleted_vbid_list(i);
                record_mark_deleted_blob(vbid, volctx);
            }

            std::set<uint64_t> *blobs = new std::set<uint64_t>;
            for (int i = 0; i < response->vbid_list_size(); ++i) {
                blobs->insert(response->vbid_list(i));
            }

            // only a slot, include all vbid
            bool ok = vlet->blob_lists.insert(std::make_pair(0, blobs)).second;
            assert(ok);
        }

        volctx->mutex->unlock();
    }
    volctx->sync_point->signal();
}

void LostCheckerWorker::record_mark_deleted_blob(uint64_t vbid,
                        std::shared_ptr<VolumeCheckContext> volctx) {
    assert(volctx != NULL);

    auto it = volctx->mark_deleted_blobs.find(vbid);
    if (it == volctx->mark_deleted_blobs.end()) {
        bool ok = volctx->mark_deleted_blobs.insert(std::make_pair(vbid, nullptr)).second;
        assert(ok);
        it = volctx->mark_deleted_blobs.find(vbid);
        assert(it != volctx->mark_deleted_blobs.end());
    }
}

static bool find_next_vbid(const std::vector<std::set<uint64_t>* >& blobs,
                           const std::vector<std::set<uint64_t>::iterator>& its,
                           uint64_t *vbid) {
    *vbid = UINT64_MAX;
    for (size_t i = 0; i < its.size(); ++i) {
        if (its[i] == blobs[i]->end()) {
            continue;
        }
        uint64_t bid = *(its[i]);
        if (bid < *vbid) {
            *vbid = bid;
        }
    }

    return *vbid != UINT64_MAX;
}

void LostCheckerWorker::find_lost_shards(std::shared_ptr<VolumeCheckContext> volctx) {
    assert(volctx != NULL);

    std::vector<std::shared_ptr<aries::pb::BlobLostShardInfo> > lost_blobs;

    // when vlet is invalid, mock blobs and iterator
    std::set<uint64_t> dummy_set;

    // 1. prepare blob list
    std::vector<std::set<uint64_t>* > blobs;
    std::vector<std::set<uint64_t>::iterator> its;
    for (size_t i = 0; i < volctx->vlets.size(); ++i) {
        auto vlet = volctx->vlets[i];
        if (vlet->addr != common::int2endpoint(0) && !vlet->is_timeout && !vlet->is_failed) {
            auto it = vlet->blob_lists.find(0);
            assert(it != volctx->vlets[i]->blob_lists.end());
            std::set<uint64_t>* s = it->second;
            assert(s != NULL);
            blobs.push_back(s);
            std::set<uint64_t>::iterator bit = s->begin();
            its.push_back(bit);
        } else {
            // vlet is invalid, mock blobs and iterator
            blobs.push_back(&dummy_set);
            std::set<uint64_t>::iterator bit = dummy_set.end();
            its.push_back(bit);
        }
    }

    // 2. do diff
    uint64_t next_vbid = UINT64_MAX;
    uint64_t curr_vbid = 0;
    
    if (ARIES_UNLIKELY(!find_next_vbid(blobs, its, &curr_vbid))) {
        LOG(WARNING) << "the volume has no blob, skip this vid: " << volctx->volume_id; 
        _check_context->sync_point->signal();
        return;
    }

    // init cursor & mark
    // meanings of mark:
    //  1 : shard exist
    //  0 : shard not exist, but vlet exist
    // -1 : vlet not exist
    uint32_t cursor = 0;
    std::vector<int> mark(volctx->eco.param.n, -1);
    std::set<uint32_t> ended_set;
    aries::pb::BlobLostShardInfo blob;
    while (cursor < volctx->vlets.size()) {

        if (ended_set.size() >= its.size()) {
            break;
        }

        uint64_t vbid = 0;
        if (its[cursor] != blobs[cursor]->end()) {
            vbid = *(its[cursor]);
        } else {
            vbid = UINT64_MAX;
            ended_set.insert(cursor);
        }

        if (vbid == curr_vbid) {
            mark[volctx->vlets[cursor]->shard_index] = 1;
            ++its[cursor];
            if (its[cursor] != blobs[cursor]->end() && *(its[cursor]) < next_vbid) {
                next_vbid = *(its[cursor]);
            }
        } else {
            assert(vbid > curr_vbid);
            mark[volctx->vlets[cursor]->shard_index] = 0;
            if (vbid < next_vbid) {
                next_vbid = vbid;
            }
        }

        if (++cursor >= its.size()) {

            if (volctx->mark_deleted_blobs.find(curr_vbid) == volctx->mark_deleted_blobs.end()) {
                std::shared_ptr<aries::pb::BlobLostShardInfo> blob(new aries::pb::BlobLostShardInfo);
                blob->set_vbid(curr_vbid);
                for (size_t i = 0; i < mark.size(); ++i) {
                    if (mark[i] != 1) {
                        auto shard = blob->add_shard_list();
                        shard->set_index(i);
                    }
                }
                // has lost shards
                if (blob->shard_list_size() > 0) {
                    lost_blobs.push_back(blob);
                }
                blob.reset();
            }

            if (next_vbid == UINT64_MAX) {
                if (!find_next_vbid(blobs, its, &curr_vbid)) {
                    break;
                }
            } else {
                curr_vbid = next_vbid;
                next_vbid = UINT64_MAX;
            }

            cursor = 0;
            mark.assign(volctx->eco.param.n, -1);
        }
    }


    // 3. compare volume lost shards with old data 
    
    check_volume(volctx->volume_id, lost_blobs);

    // 4. notify main thread i complete a check volume task
    _check_context->sync_point->signal();
}

void LostCheckerWorker::check_volume(uint64_t volume_id, 
                    std::vector<std::shared_ptr<aries::pb::BlobLostShardInfo> >& lost_blobs) {
    common::ScopedMutexLock lock_guard(_check_context->mutex);
    
    RecordKey begin_key(volume_id, 0, 0);
    RecordKey end_key(volume_id + 1, 0, 0);
    std::shared_ptr<RecordKey> key = nullptr;
    std::shared_ptr<RecordValue> value = nullptr;
    std::unique_ptr<DataStore::Iterator> it(_check_context->lost_shards_store->seek(begin_key, end_key));
    std::unique_ptr<DataStore::WriteBatch> batch(_check_context->lost_shards_store->create_batch());
    size_t cursor = 0;
    int shard_cursor = 0;

    // 1. diff blobs
    while (cursor < lost_blobs.size() && it->in_volume_scope()) {
        key = it->key();
        auto blob = lost_blobs[cursor]; 
        // diff shard
        if (key->vbid() == blob->vbid()) {
            auto value = it->value();
            auto shard = key->shard_index();

            if (shard_cursor < blob->shard_list_size()) {
                auto cur_shard = blob->shard_list(shard_cursor).index();
                if (cur_shard == shard) {
                    // remove old record
                    batch->remove(*key);
                    // insert new record 
                    RecordKey new_key(volume_id, blob->vbid(), shard);
                    RecordValue new_value(value->get_data() + 1);
                    batch->put(new_key, new_value);
                    ++shard_cursor;
                    // warning
                    if (value->get_data() + 1 >= static_cast<uint32_t>(FLAGS_previous_ranks_num)) {
                        LOG(WARNING) << "blob vid:" << key->vid() << " vbid:"
                            << key->vbid() << " shard_index:" << cur_shard
                            << " has lost " << value->get_data() + 1 << " ranks.";
                    }
                    it->next();
                } else if (cur_shard < shard) { // cur_shard is new shard
                    RecordKey new_key(volume_id, blob->vbid(), cur_shard);
                    RecordValue new_value(1);
                    batch->put(new_key, new_value);
                    ++shard_cursor;
                } else { // old shard does not appear in current rank
                    batch->remove(*key);
                    it->next();
                }
            } else {
                // old shard does not appear in current rank
                batch->remove(*key);
                it->next();
            }
        } else if (key->vbid() < blob->vbid()) {
            batch->remove(*key);
            it->next();
        } else {
            for (int i = shard_cursor; i < blob->shard_list_size(); ++i) {
                // key = vid + vbid + shard_index  value = age
                RecordKey new_key(volume_id, blob->vbid(), blob->shard_list(i).index());
                RecordValue new_value(1);
                batch->put(new_key, new_value);
            }
            ++cursor;
            shard_cursor = 0;
        }
    }

    // 2. delete repaired blobs 
    
    while (it->in_volume_scope()) {
        batch->remove(*(it->key()));
        it->next();
    }

    // 3. add new blobs

    while (cursor < lost_blobs.size()) {
        auto blob = lost_blobs[cursor];
        for (int i = shard_cursor; i < blob->shard_list_size(); ++i) {
            // key = vid + vbid + shard_index  value = age
            RecordKey new_key(volume_id, blob->vbid(), blob->shard_list(i).index());
            RecordValue new_value(1);
            batch->put(new_key, new_value);
        }
        ++cursor;
        shard_cursor = 0;
    }

    // 4. commit to store
    batch->commit();

    LOG(NOTICE) << "check vid:" << volume_id
                << " finished.";
}

void LostCheckerWorker::run() {
 
    while (!_is_stopped) {
        uint64_t volume_id = 0;
        timespec ts = base::milliseconds_from_now(10000);

        if(!_volumes->take(&volume_id, &ts)) {
            continue;
        }

        // to limit flow
        if (!_token_pool->take(0)) {
            _check_context->sync_point->signal();
            usleep(FLAGS_worker_wait_timespan_ms * 1000);
            continue;
        }

        auto volctx = get_volume(volume_id);

        if (volctx == nullptr) {
            _check_context->sync_point->signal();
            continue;
        }
    
        list_blobs(volctx);
    }
}

}
    
}
