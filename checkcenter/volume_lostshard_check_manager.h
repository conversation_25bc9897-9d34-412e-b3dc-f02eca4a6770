/***************************************************************************
 * 
 * Copyright (c) 2020 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/
 
 /**
 * @file volume_lostshard_check_manager.h
 * <AUTHOR>
 * @date 2020/06/09 20:42:01
 * @version 1.0 
 * @brief 
 *  
 **/

#pragma once

#include <algorithm>
#include "baidu/inf/aries/checkcenter/data_store.h"
#include "baidu/inf/aries/checkcenter/volume_manager.h"
#include "baidu/inf/aries/checkcenter/diff_common.h"
#include "baidu/inf/aries-api/common/speed_limit.h"

namespace aries {
namespace checker {

class VolumeLostShardCheckManager : public VolumeManager {
public:
    VolumeLostShardCheckManager();
    bool fetch_volume_to_check(const aries::pb::FetchLostShardCheckVolumesRequest& request,
        aries::pb::FetchLostShardCheckVolumesResponse* response);
    void report_check_result(const aries::pb::ReportLostShardCheckResultsRequest& request);
    void check_volume(const aries::pb::ReportLostShardCheckResultsRequest& request);
    void list_unrepaired_shards(uint32_t count, aries::pb::ListUnrepairedShardsResponse* response);

    bool start();
private:
    std::shared_ptr<LostCheckContext> _check_context;
    std::shared_ptr<common::TokenPool> _token_pool;
};

extern VolumeLostShardCheckManager* g_volume_lost_check_manager;

}
}

/* vim: set ts=4 sw=4 sts=4 tw=100 */
