//=============================================================================
// Author: <EMAIL>
// Data: 2016-10-19 13:30
// Filename: meta_data.cpp
// Description: 
//=============================================================================

#include <float.h>
#include "baidu/inf/aries/master/meta/meta_data.h"

namespace aries {
namespace master {

MetaData* g_meta_data = new MetaData;

int TapeLogicalPoolManager::add_pool(const std::shared_ptr<TapeLogicalPool>& pool_ptr) {
    int ret = -1;
    common::ScopedMutexLock lock(_mutex);
    auto pool_iter = _pool_map.find(pool_ptr->pool_name());
    if (pool_iter == _pool_map.end()) {
        _pool_map[pool_ptr->pool_name()] = pool_ptr;
        ret = 0;
    }
    return ret;
}
int TapeLogicalPoolManager::drop_pool(const std::shared_ptr<TapeLogicalPool>& pool_ptr) {
    common::ScopedMutexLock lock(_mutex);
    return _pool_map.erase(pool_ptr->pool_name()) ? 0 : -1;
}
std::shared_ptr<TapeLogicalPool> TapeLogicalPoolManager::get_pool(const std::string& pool_name) {
    common::ScopedMutexLock lock(_mutex);
    auto iter = _pool_map.find(pool_name);
    return iter != _pool_map.end() ? iter->second : nullptr;
}
int TapeLibraryGroupManager::add_library(const std::shared_ptr<TapeLibraryGroup>& library_ptr) {
    int ret = -1;
    common::ScopedMutexLock lock(_mutex);
    auto library_iter = _library_map.find(library_ptr->group_name());
    if (library_iter == _library_map.end()) {
        _library_map[library_ptr->group_name()] = library_ptr;
        ret = 0;
    }
    return ret;
}
int TapeLibraryGroupManager::drop_library(const std::shared_ptr<TapeLibraryGroup>& library_ptr) {
    common::ScopedMutexLock lock(_mutex);
    return _library_map.erase(library_ptr->group_name()) ? 0 : -1;
}
std::shared_ptr<TapeLibraryGroup> TapeLibraryGroupManager::get_library(const std::string& library_name) {
    common::ScopedMutexLock lock(_mutex);
    auto iter = _library_map.find(library_name);
    return iter != _library_map.end() ? iter->second : nullptr;
}
int AZ::add_node(const std::shared_ptr<Node>& node_ptr) {
    common::ScopedMutexLock lock(_mutex);
    auto idc_name = node_ptr->idc_name();
    auto idc_iter = _idc_map.find(idc_name);
    if (idc_iter == _idc_map.end()) {
        auto idc_ptr = std::shared_ptr<IDC>(new IDC(idc_name));
        bool ok = _idc_map.insert(std::make_pair(idc_name, idc_ptr)).second;
        assert(ok);
        return idc_ptr->add_node(node_ptr);
    } else {
        return idc_iter->second->add_node(node_ptr);
    }
}

int AZ::drop_node(const std::shared_ptr<Node>& node_ptr) {
    common::ScopedMutexLock lock(_mutex);
    auto idc_name = node_ptr->idc_name();
    auto idc_iter = _idc_map.find(idc_name);
    if (idc_iter != _idc_map.end()) {
        auto ret = idc_iter->second->drop_node(node_ptr);
        // delete empty idc
        RackVector rack_list;
        idc_iter->second->get_rack_list(&rack_list);
        if (rack_list.size() == 0) {
            _idc_map.erase(idc_iter);
        }
        return ret;
    }
    return -1;
}

void AZ::get_node_list(NodeVector* node_list) {
    common::ScopedMutexLock lock(_mutex);
    for (auto& idc_iter : _idc_map) {
        idc_iter.second->get_node_list(node_list);
    }
}

void AZ::enter_safemode() {
    common::ScopedMutexLock lock(_mutex);
    _is_safemode = true;
}

void AZ::leave_safemode() {
    common::ScopedMutexLock lock(_mutex);
    _is_safemode = false;
}

bool AZ::is_safemode() {
    common::ScopedMutexLock lock(_mutex);
    return _is_safemode;
}

void AZ::enter_light_safemode() {
    common::ScopedMutexLock lock(_mutex);
    _is_light_safemode = true;
}

void AZ::leave_light_safemode() {
    common::ScopedMutexLock lock(_mutex);
    _is_light_safemode = false;
}

bool AZ::is_light_safemode() {
    common::ScopedMutexLock lock(_mutex);
    return _is_light_safemode;
}


int IDC::add_node(const std::shared_ptr<Node>& node_ptr) {
    common::ScopedMutexLock lock(_mutex);
    auto rack_name = node_ptr->rack_name();
    auto rack_iter = _rack_map.find(rack_name);
    if (rack_iter == _rack_map.end()) {
        auto rack_ptr = std::shared_ptr<Rack>(new Rack(rack_name));
        bool ok = _rack_map.insert(std::make_pair(rack_name, rack_ptr)).second;
        assert(ok == true);
        return rack_ptr->add_node(node_ptr);
    } else {
        return rack_iter->second->add_node(node_ptr);
    }
}

int IDC::drop_node(const std::shared_ptr<Node>& node_ptr) {
    common::ScopedMutexLock lock(_mutex);
    auto rack_name = node_ptr->rack_name();
    auto rack_iter = _rack_map.find(rack_name);
    if (rack_iter != _rack_map.end()) {
        auto ret = rack_iter->second->drop_node(node_ptr);
        // delete empty rack
        NodeVector node_list;
        rack_iter->second->get_node_list(&node_list);
        if (node_list.size() == 0) {
            _rack_map.erase(rack_iter);
        }
        return ret;
    }
    return -1;
}

void IDC::get_node_list(NodeVector* node_list) {
    common::ScopedMutexLock lock(_mutex);
    for (auto& rack_iter : _rack_map) {
        rack_iter.second->get_node_list(node_list);
    }
}

void IDC::get_rack_list(RackVector* rack_list) {
    common::ScopedMutexLock lock(_mutex);
    for (auto& rack_iter : _rack_map) {
        rack_list->push_back(rack_iter.second);
    }
}

void Rack::get_node_list(NodeVector* node_list) {
    common::ScopedMutexLock lock(_mutex);
    for (auto& node_iter : _node_map) {
        node_list->push_back(node_iter.second);
    }
}

::base::EndPoint Disk::addr() {
    auto node = this->node();
    return node ? node->addr() : base::EndPoint();
}

// take too long
bool Disk::is_full() {
    VletVector vlet_list;
    get_vlet_list(&vlet_list);
    uint64_t rewrite_overflow_size = 0;
    for (auto & vlet : vlet_list) {
        auto engine_type = common::vlet_type_by_vlet(vlet->vlet_type());
        if (engine_type == ENGINE_APPEND) {
            rewrite_overflow_size += vlet->vlet_engine_info_ptr()->smr_zone_size();
        } else if (engine_type = ENGINE_ZONE) {
            rewrite_overflow_size += _reserved_size_for_rewrite;
        }
    }
    double max_disk_usage = 0.0;
    std::shared_ptr<Node> node = this->node();
    if (node) {
        max_disk_usage = get_max_disk_usage(node->az_name());
    }
    return (disk_free_size() + rewrite_overflow_size) <= disk_reserve_size() || 
        usage() >= max_disk_usage;
}

int Disk::add_vlet(const std::shared_ptr<Vlet>& vlet_ptr) {
    int ret = -1;
    assert(is_vlet_state_not_exist(vlet_ptr->state()) == false);
    // get vlet type will lock space, out of disk lock.
    auto vlet_size = vlet_ptr->vlet_engine_info_ptr()->vlet_size();
    
    common::ScopedMutexLock lock(_mutex);
    uint64_t volume_id = vlet_ptr->volume_id();
    auto iter = _vlet_map.find(volume_id);
    if (iter == _vlet_map.end()) {
        _vlet_map[volume_id] = vlet_ptr;
        _calc_used_size += vlet_size;
        ret = 0;
    } else if (iter->second == vlet_ptr) {
        ret = 0;
    }

    return ret;
}

std::shared_ptr<Vlet> Disk::get_vlet(const uint64_t volume_id) {
    common::ScopedMutexLock lock(_mutex);
    auto iter = _vlet_map.find(volume_id);
    return iter != _vlet_map.end() ? iter->second : nullptr;
}

int Disk::drop_vlet(const std::shared_ptr<Vlet>& vlet) {
    int ret = -1;
    assert(is_vlet_state_not_exist(vlet->state()) == false);
    auto vlet_size = vlet->vlet_engine_info_ptr()->vlet_size();
    auto volume_id = vlet->volume_id();

    common::ScopedMutexLock lock(_mutex);
    auto vlet_iter = _vlet_map.find(volume_id);
    if (vlet_iter != _vlet_map.end()) {
        _calc_used_size -= std::min(_calc_used_size, vlet_size);
        assert(vlet_iter->second == vlet);
        _vlet_map.erase(vlet_iter);
        ret = 0;
    }
    return ret;
}

void Disk::get_exist_vlets(VletVector* vlets) {
    common::ScopedMutexLock lock(_mutex);
    for (auto& vlet : _vlet_map) {
        if (is_vlet_state_not_exist(vlet.second->state())) {
            continue;
        }
        vlets->push_back(vlet.second);
    }
}

void Disk::get_space_exist_vlets(const std::string& space_name, VletVector* vlets) {
    common::ScopedMutexLock lock(_mutex);
    for (auto& vlet : _vlet_map) {
        if (is_vlet_state_not_exist(vlet.second->state())) {
            continue;
        }
        if (vlet.second->space_name() == space_name) {
            vlets->push_back(vlet.second);
        }
    }
}

void Disk::get_vlet_list(VletVector* vlet_list) {
    common::ScopedMutexLock lock(_mutex);
    for (auto& vlet : _vlet_map) {
        vlet_list->push_back(vlet.second);
    }
}

int Disk::get_space_vlet_num(const std::string& space_name) {
    VletVector vlet_list;
    this->get_vlet_list(&vlet_list);
    int ret = 0;
    for (auto& vlet : vlet_list) {
        if (vlet->space_name() == space_name) {
            ++ret;
        }
    }
    return ret;
}

void Node::update_disk_stat(const std::vector<DiskStat>& disk_stat_vector) {
    common::ScopedMutexLock lock(_mutex);

    uint64_t disk_total_size = 0;
    uint64_t disk_free_size = 0;
    uint64_t aries_capacity = 0;
    uint64_t report_used_size = 0;
    uint64_t calc_used_size = 0;

    _max_ioutil = 0;
    for (auto& disk_stat : disk_stat_vector) {
        auto disk = _disk_map.find(disk_stat.id);
        if (disk != _disk_map.end()) {
            //TODO not pick not NORMAL disk.
            disk->second->update_disk_stat(disk_stat);
            disk_total_size += disk_stat.disk_total_size;
            disk_free_size += disk_stat.disk_free_size;
            aries_capacity += disk_stat.aries_capacity;
            report_used_size += disk_stat.used_size;
            calc_used_size += disk->second->used_size();
            if (disk_stat.disk_ioutil > _max_ioutil) {
                _max_ioutil = disk_stat.disk_ioutil;
                _max_ioutil_disk_id = disk_stat.id;
            }
        }
    }

    _calc_used_size = calc_used_size;
    _disk_total_size = disk_total_size;
    _disk_free_size = disk_free_size;
    _aries_capacity = aries_capacity;
    _report_used_size = report_used_size;
    _total_size = (aries_capacity > disk_total_size) ? disk_total_size : aries_capacity;
}

double Node::min_disk_usage() {
    common::ScopedMutexLock lock(_mutex);
    double usage = DBL_MAX;
    for (auto& disk : _disk_map) {
        if (disk.second->usage() < usage) {
            usage = disk.second->usage();
        }
    }
    return usage >= 1.0 ? 1.0 : usage;
}

double Node::usage() {
    common::ScopedMutexLock lock(_mutex);
    uint64_t used = 0;
    for (auto& disk : _disk_map) {
        //TODO not pick not NORMAL disk.
        used += disk.second->used_size();
    }
    double usage = (double)used / (double)(_total_size + 1);
    return usage;
}

std::shared_ptr<Disk> Node::get_disk(const int disk_id) {
    common::ScopedMutexLock lock(_mutex);
    auto disk = _disk_map.find(disk_id);
    return disk != _disk_map.end() ? disk->second : nullptr;
}

std::shared_ptr<Vlet> Node::get_vlet(const uint64_t volume_id) {
    common::ScopedMutexLock lock(_mutex);
    for (auto& disk : _disk_map) {
        auto ret = disk.second->get_vlet(volume_id);
        if (ret != nullptr) {
            return ret;
        }
    }
    return nullptr;
}

void Node::get_disk_list(DiskVector* disk_list) {
    common::ScopedMutexLock lock(_mutex);
    for (auto& disk : _disk_map) {
        disk_list->push_back(disk.second);
    }
}

int Node::add_disk(const std::shared_ptr<Disk>& disk_ptr) {
    int ret = -1;
    common::ScopedMutexLock lock(_mutex);
    auto iter = _disk_map.find(disk_ptr->disk_id());
    if (iter == _disk_map.end()) {
        _disk_map[disk_ptr->disk_id()] = disk_ptr;
        ret = 0;
    }
    return ret;
}

int Node::drop_disk(const int disk_id) {
    int ret = -1;
    common::ScopedMutexLock lock(_mutex);
    auto disk = _disk_map.find(disk_id);
    if (disk != _disk_map.end()) {
        disk->second->set_state(DISK_STATE_DROPED);
        _disk_map.erase(disk);
        return 0;
    }
    return ret;
}

size_t Node::get_vlet_num() {
    common::ScopedMutexLock lock(_mutex);
    size_t count = 0;
    for (auto& disk : _disk_map) {
        count += disk.second->get_vlet_num();
    }
    return count;
}

void Node::get_exist_vlets(VletVector* vlets) {
    common::ScopedMutexLock lock(_mutex);
    for (auto& disk : _disk_map) {
        disk.second->get_exist_vlets(vlets);
    }
}

void Node::get_vlet_list(VletVector* vlet_list) {
    common::ScopedMutexLock lock(_mutex);
    for (auto& disk : _disk_map) {
        disk.second->get_vlet_list(vlet_list);
    }
}

int NodeManager::add_node(const std::shared_ptr<Node>& node_ptr) {
    int ret = -1;
    common::ScopedMutexLock lock(_mutex);
    auto iter = _node_map.find(node_ptr->addr());
    if (iter == _node_map.end()) {
        _node_map[node_ptr->addr()] = node_ptr;
        ++_add_op_counter;
        ret = 0;
    }
    return ret;
}

int NodeManager::drop_node(const base::EndPoint& node_addr) {
    int ret = -1;
    common::ScopedMutexLock lock(_mutex);
    if (_node_map.erase(node_addr)) {
        ++_drop_op_counter;
        ret = 0;
    }
    return ret;
}

std::shared_ptr<Node> NodeManager::get_node(const ::base::EndPoint& addr) {
    common::ScopedMutexLock lock(_mutex);
    auto iter = _node_map.find(addr);
    return iter != _node_map.end() ? iter->second : nullptr;
}

void NodeManager::get_node_list(NodeVector* node_list) {
    common::ScopedMutexLock lock(_mutex);
    for (auto& node : _node_map) {
        node_list->push_back(node.second);
    }
}

std::shared_ptr<Tinker> TinkerManager::get_tinker(const std::string& name) {
    common::ScopedMutexLock lock(_mutex);
    auto it = _tinker_map.find(name);
    return it != _tinker_map.end() ? it->second : nullptr;
}

int TinkerManager::add_tinker(const std::shared_ptr<Tinker>& tinker) {
    std::string name = tinker->name();
    common::ScopedMutexLock lock(_mutex);
    auto it = _tinker_map.find(name);
    if (ARIES_UNLIKELY(it != _tinker_map.end())) {
        return -1;
    } else {
        bool ok = _tinker_map.insert(std::make_pair(name, tinker)).second;
        _add_op_counter += ok ? 1 : 0;
        return ok ? 0 : -1;
    }
}

int TinkerManager::drop_tinker(const std::string& name) {
    common::ScopedMutexLock lock(_mutex);
    bool ok = _tinker_map.erase(name);
    _drop_op_counter += ok ? 1 : 0;
    return ok ? 0 : -1;
}

void TinkerManager::get_tinker_list(TinkerVector* tinker_list) {
    common::ScopedMutexLock lock(_mutex);
    for (auto & pair : _tinker_map) {
        tinker_list->push_back(pair.second);
    }
}

int TapeCenterManager::add_tape_center(const std::shared_ptr<TapeCenter>& tape_center) {
    std::string name = tape_center->name();
    common::ScopedMutexLock lock(_mutex);
    auto it = _tape_center_map.find(name);
    if (ARIES_UNLIKELY(it != _tape_center_map.end())) {
        return -1;
    } else {
        bool ok = _tape_center_map.insert(std::make_pair(name, tape_center)).second;
        _add_op_counter += ok ? 1 : 0;
        return ok ? 0 : -1;
    }
}

int TapeCenterManager::drop_tape_center(const std::string& name) {
    common::ScopedMutexLock lock(_mutex);
    bool ok = _tape_center_map.erase(name);
    _drop_op_counter += ok ? 1 : 0;
    return ok ? 0 : -1;
}

std::shared_ptr<TapeCenter> TapeCenterManager::get_tape_center(const std::string& name) {
    common::ScopedMutexLock lock(_mutex);
    auto it = _tape_center_map.find(name);
    return it != _tape_center_map.end() ? it->second : nullptr;
}

void TapeCenterManager::get_tape_center_list(TapeCenterVector* tape_center_list) {
    common::ScopedMutexLock lock(_mutex);
    for (auto & pair : _tape_center_map) {
        tape_center_list->push_back(pair.second);
    }
}

int TapeNodeManager::add_tape_node(const std::shared_ptr<TapeNode>& tape_node) {
    base::EndPoint addr = tape_node->addr();
    common::ScopedMutexLock lock(_mutex);
    auto it = _tape_node_map.find(addr);
    if (it != _tape_node_map.end()) {
        return -1;
    } else {
        bool ok = _tape_node_map.insert(std::make_pair(addr, tape_node)).second;
        _add_op_counter += ok ? 1 : 0;
        return ok ? 0 : -1;
    }
}

int TapeNodeManager::drop_tape_node(const base::EndPoint& addr) {
    common::ScopedMutexLock lock(_mutex);
    bool ok = _tape_node_map.erase(addr);
    _drop_op_counter += ok ? 1 : 0;
    return ok ? 0 : -1;
}

void TapeNodeManager::get_tape_node_list(TapeNodeVector* tape_node_list) {
    common::ScopedMutexLock lock(_mutex);
    for (auto & pair : _tape_node_map) {
        tape_node_list->push_back(pair.second);
    }
}

std::shared_ptr<TapeNode> TapeNodeManager::get_tape_node(const base::EndPoint& addr) {
    common::ScopedMutexLock lock(_mutex);
    auto it = _tape_node_map.find(addr);
    return it != _tape_node_map.end() ? it->second : nullptr;
}

std::shared_ptr<Repairer> RepairerManager::get_repairer(const base::EndPoint& addr) {
    common::ScopedMutexLock lock(_mutex);
    auto it = _repairer_map.find(addr);
    return it != _repairer_map.end() ? it->second : nullptr;
}

int RepairerManager::add_repairer(const std::shared_ptr<Repairer>& repairer) {
    base::EndPoint addr = repairer->addr();
    common::ScopedMutexLock lock(_mutex);
    auto it = _repairer_map.find(addr);
    if (ARIES_UNLIKELY(it != _repairer_map.end())) {
        return -1;
    } else {
        bool ok = _repairer_map.insert(std::make_pair(addr, repairer)).second;
        return ok ? 0 : -1;
    }
}

int RepairerManager::drop_repairer(const base::EndPoint& addr) {
    common::ScopedMutexLock lock(_mutex);
    return _repairer_map.erase(addr) ? 0 : -1;
}

void RepairerManager::get_repairer_list(RepairerVector* repairer_list) {
    common::ScopedMutexLock lock(_mutex);
    for (auto & pair : _repairer_map) {
        repairer_list->push_back(pair.second);
    }
}

std::shared_ptr<Checkcenter> CheckcenterManager::get_checkcenter(const base::EndPoint& addr) {
    common::ScopedMutexLock lock(_mutex);
    auto it = _checkcenter_map.find(addr);
    return it != _checkcenter_map.end() ? it->second : nullptr;
}

int CheckcenterManager::add_checkcenter(const std::shared_ptr<Checkcenter>& checkcenter) {
    base::EndPoint addr = checkcenter->addr();
    common::ScopedMutexLock lock(_mutex);
    auto it = _checkcenter_map.find(addr);
    if (ARIES_UNLIKELY(it != _checkcenter_map.end())) {
        return -1;
    } else {
        bool ok = _checkcenter_map.insert(std::make_pair(addr, checkcenter)).second;
        return ok ? 0 : -1;
    }
}

int CheckcenterManager::drop_checkcenter(const base::EndPoint& addr) {
    common::ScopedMutexLock lock(_mutex);
    return _checkcenter_map.erase(addr) ? 0 : -1;
}

void CheckcenterManager::get_checkcenter_list(CheckcenterVector* checkcenter_list) {
    common::ScopedMutexLock lock(_mutex);
    for (auto & pair : _checkcenter_map) {
        checkcenter_list->push_back(pair.second);
    }
}

std::shared_ptr<Monitorcenter> MonitorcenterManager::get_monitorcenter(const base::EndPoint& addr) {
    common::ScopedMutexLock lock(_mutex);
    auto it = _monitorcenter_map.find(addr);
    return it != _monitorcenter_map.end() ? it->second : nullptr;
}

int MonitorcenterManager::add_monitorcenter(const std::shared_ptr<Monitorcenter>& monitorcenter) {
    base::EndPoint addr = monitorcenter->addr();
    common::ScopedMutexLock lock(_mutex);
    auto it = _monitorcenter_map.find(addr);
    if (ARIES_UNLIKELY(it != _monitorcenter_map.end())) {
        return -1;
    } else {
        bool ok = _monitorcenter_map.insert(std::make_pair(addr, monitorcenter)).second;
        return ok ? 0 : -1;
    }
}

int MonitorcenterManager::drop_monitorcenter(const base::EndPoint& addr) {
    common::ScopedMutexLock lock(_mutex);
    return _monitorcenter_map.erase(addr) ? 0 : -1;
}

void MonitorcenterManager::get_monitorcenter_list(MonitorcenterVector* monitorcenter_list) {
    common::ScopedMutexLock lock(_mutex);
    for (auto & pair : _monitorcenter_map) {
        monitorcenter_list->push_back(pair.second);
    }
}

std::shared_ptr<VolumeService> VolumeServiceManager::get_volume_service(const base::EndPoint& addr) {
    common::ScopedMutexLock lock(_mutex);
    auto it = _volume_service_map.find(addr);
    return it != _volume_service_map.end() ? it->second : nullptr;
}

int VolumeServiceManager::add_volume_service(const std::shared_ptr<VolumeService>& volume_service) {
    base::EndPoint addr = volume_service->addr();
    common::ScopedMutexLock lock(_mutex);
    auto it = _volume_service_map.find(addr);
    if (ARIES_UNLIKELY(it != _volume_service_map.end())) {
        return -1;
    } else {
        bool ok = _volume_service_map.insert(std::make_pair(addr, volume_service)).second;
        _add_op_counter += ok ? 1 : 0;
        return ok ? 0 : -1;
    }
}

int VolumeServiceManager::drop_volume_service(const ::base::EndPoint& addr) {
    common::ScopedMutexLock lock(_mutex);
    bool ok = _volume_service_map.erase(addr);
    _drop_op_counter += ok ? 1 : 0;
    return ok ? 0 : -1;
}

void VolumeServiceManager::get_volume_service_list(VolumeServiceVector* volume_service_list) {
    common::ScopedMutexLock lock(_mutex);
    for (auto & pair : _volume_service_map) {
        volume_service_list->push_back(pair.second);
    }
}

std::shared_ptr<Allocator> AllocatorManager::get_allocator(const std::string& server_name) {
    common::ScopedMutexLock lock(_mutex);
    auto it = _allocator_map.find(server_name);
    return it != _allocator_map.end() ? it->second : nullptr;
}

int AllocatorManager::add_allocator(const std::shared_ptr<Allocator>& allocator) {
    std::string name = allocator->name();
    common::ScopedMutexLock lock(_mutex);
    auto it = _allocator_map.find(name);
    if (ARIES_UNLIKELY(it != _allocator_map.end())) {
        return -1;
    } else {
        bool ok = _allocator_map.insert(std::make_pair(name, allocator)).second;
        _add_op_counter += ok ? 1 : 0;
        DCHECK(ok);
        return 0;
    }
}

int AllocatorManager::drop_allocator(const std::string& server_name) {
    common::ScopedMutexLock lock(_mutex);
    bool ok = _allocator_map.erase(server_name);
    _drop_op_counter += ok ? 1 : 0;
    return ok ? 0 : -1;
}

std::shared_ptr<DataAgent> DataAgentManager::get_dataagent(const std::DataAgentIdentify& id) {
    common::ScopedMutexLock lock(_mutex);
    auto it = _dataagent_map.find(id);
    return it != _dataagent_map.end() ? it->second : nullptr;
}

int DataAgentManager::add_dataagent(const std::shared_ptr<DataAgent>& dataagent) {
    common::ScopedMutexLock lock(_mutex);
    auto it = _dataagent_map.find(dataagent->dataagent_identify());
    if (ARIES_UNLIKELY(it != _dataagent_map.end())) {
        return -1;
    } else {
        bool ok = _dataagent_map.insert(std::make_pair(dataagent->dataagent_identify(), dataagent)).second;
        _add_op_counter += ok ? 1 : 0;
        assert(ok);
        return 0;
    }
}

int DataAgentManager::drop_dataagent(const std::DataAgentIdentify& id) {
    common::ScopedMutexLock lock(_mutex);
    bool ok = _dataagent_map.erase(id);
    _drop_op_counter += ok ? 1 : 0;
    return ok ? 0 : -1;
}

int StateServiceManager::add_stateservice(std::shared_ptr<StateService> stateservice) {
    base::EndPoint addr = stateservice->addr();
    auto group_id = stateservice->group_id();
    common::ScopedMutexLock lock(_mutex);
    auto it = _stateservice_map.find({ addr, group_id });
    if (ARIES_UNLIKELY(it != _stateservice_map.end())) {
        return -1;
    }
    else {
        bool ok = _stateservice_map.insert(std::make_pair(std::make_pair(addr, group_id), stateservice)).second;
        return ok ? 0 : -1;
    }
}

int StateServiceManager::drop_stateservice(const base::EndPoint& addr, uint64_t group_id) {
    common::ScopedMutexLock lock(_mutex);
    return _stateservice_map.erase(std::make_pair(addr, group_id)) ? 0 : -1;
}

std::shared_ptr<StateService> StateServiceManager::get_stateservice(const base::EndPoint& addr, uint64_t group_id) {
    common::ScopedMutexLock lock(_mutex);
    auto it = _stateservice_map.find(std::make_pair(addr, group_id));
    return it != _stateservice_map.end() ? it->second : nullptr;
}

void StateServiceManager::get_stateservice_list(StateServiceVector* stateservice_list) {
    common::ScopedMutexLock lock(_mutex);
    for (auto& pair : _stateservice_map) {
        stateservice_list->push_back(pair.second);
    }
}

/*
int AZ::add_node(const std::shared_ptr<Node> node_ptr) {
    int ret = -1;
    common::ScopedMutexLock lock(_mutex);
    auto iter = _node_map.find(node_ptr->addr());
    if (iter == _node_map.end()) {
        _node_map[node_ptr->addr()] = node_ptr;
        ret = 0;
    }
    return ret;
}

int AZ::drop_node(const ::base::EndPoint& addr) {
    common::ScopedMutexLock lock(_mutex);
    int ret = -1;
    if (true == _node_map.erase(addr)) {
        ret = 0;
    }
    return ret;
}

void AZ::get_node_vector(NodeVector *node_vector) {
    common::ScopedMutexLock lock(_mutex);
    for (auto& iter : _node_map) {
        node_vector->push_back(iter.second);
    }
}

int AreaManager::add_node(const std::shared_ptr<Node> node_ptr) {
    int ret = -1;
    common::ScopedMutexLock lock(_mutex);
    auto iter = _idc_map.find(node_ptr->az_name());
    if (iter != _az_map.end()) {
        ret = iter->second->add_node(node_ptr);
    } else {
        auto az = std::shared_ptr<AZ>(new AZ(node_ptr->az_name()));
        _az_map[node_ptr->az_name()] = az;
        ret = az->add_node(node_ptr);
    }
    return ret;
}

int AZManager::drop_node(const std::shared_ptr<Node> node_ptr) {
    int ret = -1;
    common::ScopedMutexLock lock(_mutex);
    auto az_iter = _az_map.find(node_ptr->az_name());
    if (az_iter != _az_map.end()) {
        ret = az_iter->second->drop_node(node_ptr->addr());
    }
    return ret;
}

int AreaManager::get_node_vector(std::string& az_name, NodeVector *node_vector) {
    int ret = -1;
    common::ScopedMutexLock lock(_mutex);
    auto iter = _az_map.find(az_name);
    if (iter != _az_map.end()) {
        iter->second->get_node_vector(node_vector);
        ret = 0;
    }
    return ret;
}
*/
uint64_t Space::total_physical_space() {
    VolumeMap tmp_volume_map;
    // take care, avoid deadlock
    {
        common::ScopedMutexLock lock(_mutex);
        tmp_volume_map = _volume_map;
    }
    uint64_t total_physical_size = 0;
    for (auto & iter : tmp_volume_map) {
        auto volume_ptr = iter.second;
        total_physical_size += volume_ptr->per_vlet_size() * _options.k;
    }
    return total_physical_size;
}

int Space::add_volume(const std::shared_ptr<Volume>& volume) {
    common::ScopedMutexLock lock(_mutex);
    auto ok = _volume_map.insert(std::make_pair(volume->volume_id(),
                volume)).second;
    if (ok == true) {
        _last_volume_id = volume->volume_id();
        return 0;
    }
    return -1;
}

int SpaceManager::add_space(const std::shared_ptr<Space>& space_ptr) {
    int ret = -1;
    common::ScopedMutexLock lock(_mutex);
    auto space_iter = _space_map.find(space_ptr->space_name());
    if (space_iter == _space_map.end()) {
        _space_map[space_ptr->space_name()] = space_ptr;
        ret = 0;
    }
    return ret;
}

int SpaceManager::drop_space(const std::shared_ptr<Space>& space_ptr) {
    common::ScopedMutexLock lock(_mutex);
    return _space_map.erase(space_ptr->space_name()) ? 0 : -1;
}

std::shared_ptr<Space> SpaceManager::get_space(const std::string& space_name) {
    common::ScopedMutexLock lock(_mutex);
    auto space_iter = _space_map.find(space_name);
    return space_iter != _space_map.end() ? space_iter->second : nullptr;
}

int Volume::unnormal_vlet_num() {
    int unnormal_num = 0;
    common::ScopedMutexLock lock(_mutex);
    for (auto& vlet : _vlet_vector) {
        if (vlet->state() != VLET_STATE_NORMAL) {
            ++unnormal_num;
        }
    }
    return unnormal_num;
}

void Volume::serialize(aries::pb::VolumeInfo* volume_info) {
    volume_info->set_volume_id(_volume_id);
    volume_info->set_volume_state(_state);
    volume_info->set_space_name(space_name());
    volume_info->set_volume_version(volume_version());
    volume_info->set_create_time(_create_time);
    volume_info->set_per_vlet_size(_per_vlet_size);
    volume_info->set_compact_progress(_compact_progress);
    volume_info->set_compact_vlet_size(_compact_vlet_size);
    volume_info->set_copy_progress(_copy_progress);
    volume_info->set_permit_data_offset_index(_permit_data_offset_index);
    volume_info->set_volume_ttl_timestamp(_volume_ttl_timestamp);
    volume_info->set_is_sealed(_is_sealed);
    if (_location_on_tape != nullptr){
        volume_info->mutable_location_on_tape()->CopyFrom(*_location_on_tape);
    }
    if (_stats_on_tape != nullptr) {
        volume_info->mutable_stats_on_tape()->CopyFrom(*_stats_on_tape);
    }
    volume_info->set_purge_state(_purge_state);
    volume_info->set_last_purge_timestamp(_last_purge_timestamp);

    for (auto& vlet : _vlet_vector) {
        auto vlet_info = volume_info->add_vlet_list();
        vlet->serialize(vlet_info);
    }
}

bool Volume::check_set_volume_create_state() {
    common::ScopedMutexLock lock(_mutex);
    if (_state != VOLUME_STATE_CREATING) {
        return false;
    }
    for (auto& vlet : _vlet_vector) {
        if (vlet->state() == VLET_STATE_CREATING) {
            return false;
        }
    }
    _state = VOLUME_STATE_NORMAL;
    return true;
}

bool Volume::check_finish_volume_compact_progress() {
    common::ScopedMutexLock lock(_mutex);
    if (_state != VOLUME_STATE_READONLY || _compact_progress != RUNNING) {
        return false;
    }
    for (auto& vlet : _vlet_vector) {
        if (vlet->vlet_engine_info_ptr()->vlet_size() != _compact_vlet_size) {
            return false;
        }
    }
    return true;
}


bool Volume::check_finish_volume_copy_progress() {
    int least_copyed_num = _space.lock()->least_copyed_num();
    common::ScopedMutexLock lock(_mutex);
    if (_state != VOLUME_STATE_RECEIVING) {
        return false;
    }
    int copyed_num = 0;
    for (auto& vlet : _vlet_vector) {
        if (vlet->state() == VLET_STATE_COPYED) {
            ++copyed_num;
        }
    }
    return copyed_num >= least_copyed_num;
}

bool Volume::is_dangerous(const std::shared_ptr<Vlet>& drop_vlet) {
    int normal_num = 0;
    auto index = drop_vlet->shard_index();
    int least_normal_num = _space.lock()->least_normal_num();
    if (_state == VOLUME_STATE_CREATING || _state == VOLUME_STATE_RECEIVING) {
        return false;
    }
    common::ScopedMutexLock lock(_mutex);
    for (auto& vlet : _vlet_vector) {
        if (vlet->shard_index() == index) {
            continue;
        }
        if (vlet->state() == VLET_STATE_NORMAL) {
            normal_num++;
        }
    }
    return normal_num < least_normal_num;
}


uint32_t Volume::volume_version() {
    common::ScopedMutexLock lock(_mutex);
    return _volume_version;
}

void Volume::update_membership() {
    common::ScopedMutexLock lock(_mutex);
    _membership = std::shared_ptr<aries::pb::Membership>(new aries::pb::Membership);
    _volume_version++;
    _membership->set_volume_version(_volume_version);
    for (auto& vlet : _vlet_vector) {
        auto vlet_version = vlet->vlet_version();
        _membership->add_vlet_version_set(vlet_version);
    }
}

void Volume::generate_membership() {
    common::ScopedMutexLock lock(_mutex);
    _membership = std::shared_ptr<aries::pb::Membership>(new aries::pb::Membership);
    _membership->set_volume_version(_volume_version);
    for (auto& vlet : _vlet_vector) {
        auto vlet_version = vlet->vlet_version();
        _membership->add_vlet_version_set(vlet_version);
    }
}

void Volume::get_vlet_info_list(VletInfoVector* vlet_info_list) {
    // vlet addr is of disk , not use lock in volume->vlet->disk.
    VletVector vlet_list;
    this->get_vlet_list(&vlet_list);

    for (auto& vlet : vlet_list) {
        std::shared_ptr<aries::pb::VletInfo> vlet_info(new aries::pb::VletInfo);
        vlet_info->set_volume_id(_volume_id);
        vlet_info->set_shard_index(vlet->shard_index());
        vlet_info->set_node_addr(common::endpoint2int(vlet->addr()));
        vlet_info->set_create_time(vlet->create_time());
        vlet_info->set_vlet_version(vlet->vlet_version());
        vlet_info->set_state(vlet->state());
        vlet_info_list->push_back(vlet_info);
    }
}

int Volume::add_vlet(const std::shared_ptr<Vlet>& vlet) {
    int shard_index = vlet->shard_index();
    common::ScopedMutexLock lock(_mutex);

    if (_vlet_vector[shard_index]) {
        return -1;
    }
    _vlet_vector[shard_index] = vlet;
    return 0;
}

int Volume::increase_redundancy(const std::shared_ptr<Vlet>& vlet) {
    uint32_t shard_index = vlet->shard_index();
    common::ScopedMutexLock lock(_mutex);
    if (shard_index != _vlet_vector.size()) {
        return -1;
    }
    _vlet_vector.push_back(vlet);
    return 0;
}

/*
int Volume::drop_vlet(const int shard_index) {
    common::ScopedMutexLock lock(_mutex);
    std::shared_ptr<Vlet> vlet = _vlet_vector[shard_index];
    assert(vlet != nullptr);
    if (!is_vlet_state_not_exist(vlet->state())) {
        if (vlet->state() == VLET_STATE_NORMAL) {
        }
        vlet->set_state(VLET_STATE_DROPED);
        vlet->set_disk_ptr(nullptr);
        return 0;
    }
    return -1;
}
*/

bool Volume::is_node_exist(const base::EndPoint& addr) {
    VletVector vlet_list;
    this->get_vlet_list(&vlet_list);

    for (auto& vlet : vlet_list) {
        if (vlet && vlet->addr() == addr) {
            return true;
        }
    }
    return false;
}

int VolumeManager::add_volume(const std::shared_ptr<Volume>& volume) {
    common::ScopedMutexLock lock(_mutex);
    auto volume_id = volume->volume_id();
    auto cluster_id = volume_id >> 48;
    auto real_cluster_id = g_meta_data->cluster_id();
    if (cluster_id == real_cluster_id && volume_id > _max_volume_id) {
        _max_volume_id = volume_id;
    }
    auto ok = _volume_map.insert(std::make_pair(volume_id, volume)).second;
    return !ok;
}

std::shared_ptr<Volume> VolumeManager::get_volume(const uint64_t volume_id) {
    common::ScopedMutexLock lock(_mutex);
    auto iter = _volume_map.find(volume_id);
    return iter != _volume_map.end() ? iter->second : nullptr;
}

int MetaData::add_node(const std::shared_ptr<Node>& node_ptr) {
    int ret = _node_manager->add_node(node_ptr);
    assert(ret == 0);
    ret = _az_manager->add_node(node_ptr);
    assert(ret == 0);
    return ret;
}

int MetaData::drop_node(const std::shared_ptr<Node>& node_ptr) {
    node_ptr->set_state(NODE_STATE_DROPED);
    int ret = _node_manager->drop_node(node_ptr->addr());
    assert(ret == 0);
    ret = _az_manager->drop_node(node_ptr);
    assert(ret == 0);
    return ret;
}

int MetaData::update_node(const std::shared_ptr<Node>& node_ptr, const NodeOptions& update_node_options) {
    // don't need to update node_manager
    int ret = _az_manager->update_node(node_ptr, update_node_options);
    return ret;
}

int MetaData::add_space(const std::shared_ptr<Space>& space) {
    int ret = -1;
    ret = _space_manager->add_space(space);
    return ret;
}

int MetaData::add_volume(const std::shared_ptr<Volume>& volume) {
    int ret1 = volume->space()->add_volume(volume); 
    int ret2 = _volume_manager->add_volume(volume);
    if (ret1 == 0 && ret2 == 0) {
        return 0;
    }
    return -1;
}

}
}
