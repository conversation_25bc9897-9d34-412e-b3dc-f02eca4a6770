// Filename: meta_data.h
// Description: 
//=============================================================================

#ifndef BAIDU_INF_ARIES_MASTER_META_META_DATA_H
#define BAIDU_INF_ARIES_MASTER_META_META_DATA_H

#include <memory>                   // std::shared_ptr
#include <atomic>
#include <map>                      // std::map
#include <set>                      // std::set
#include <string>                   // std::string
#include <algorithm>
#include <unordered_map>
#include "assert.h"
#include <base/time.h>
#include <base/endpoint.h>          // base::EndPoint
#include <base/memory/singleton.h>  // Singleton
#include "baidu/rpc/policy/hasher.h" // hash
#include <bthread/mutex.h>          // bthread::Mutex
#include "baidu/inf/aries-api/common/common.h"
#include "baidu/inf/aries-api/common/lock.h"
#include "baidu/inf/aries/master/common.h"
#include "baidu/inf/aries/master/conf.h"
#include "baidu/inf/aries/common/common.h"
#include "base/strings/string_util.h"
#include "baidu/inf/aries-api/common/proto/master.pb.h"
#include "baidu/inf/aries-api/common/proto/db.pb.h"

namespace std {
    template<> struct hash<base::EndPoint> {    // for unordered_map's class Hash
        std::size_t operator() (base::EndPoint const& s) const {
            return std::hash<uint64_t>{}(aries::common::endpoint2int(s));
        }
    };

    typedef std::pair<base::EndPoint, std::string> DataAgentIdentify;
    template<> struct hash<DataAgentIdentify> {    // for unordered_map's class Hash
        std::size_t operator() (DataAgentIdentify const& s) const {
            return std::hash<std::string>{}(::aries::common::endpoint2str(s.first) + s.second);
        }
    };
}

namespace aries {
namespace master {

class AZ;
class IDC;
class Rack;
class Group;
class Node;
class Disk;

class Space;
class Volume;
class Vlet;

class Tinker;
class Repairer;
class Checkcenter;
class Monitorcenter;
class Allocator;
class VolumeService;
class DataAgent;
class TapeCenter;
class TapeNode;
class StateService;

class TapeLibraryGroup;
class TapeLogicalPool;

static inline bool is_vlet_state_not_exist(VletState state) {
    // LOG(DEBUG) << "vlet_stat is:" << vletstate2str(state);
    if (state == VLET_STATE_NORMAL ||
            state == VLET_STATE_REPAIRING ||
            state == VLET_STATE_JOINING ||
            state == VLET_STATE_COPYED) {
        return false;
    }
    return true;
}

typedef std::vector<std::shared_ptr<aries::pb::VletInfo>> VletInfoVector;

typedef std::vector<std::shared_ptr<Tinker> > TinkerVector;
typedef std::unordered_map<std::string, std::shared_ptr<Tinker> > TinkerMap;

typedef std::vector<std::shared_ptr<Repairer> > RepairerVector;
typedef std::unordered_map<base::EndPoint, std::shared_ptr<Repairer> > RepairerMap;

typedef std::vector<std::shared_ptr<Checkcenter> > CheckcenterVector;
typedef std::unordered_map<base::EndPoint, std::shared_ptr<Checkcenter> > CheckcenterMap;

typedef std::vector<std::shared_ptr<Monitorcenter> > MonitorcenterVector;
typedef std::unordered_map<base::EndPoint, std::shared_ptr<Monitorcenter> > MonitorcenterMap;

typedef std::vector<std::shared_ptr<VolumeService> > VolumeServiceVector;
typedef std::unordered_map<base::EndPoint, std::shared_ptr<VolumeService> > VolumeServiceMap;

typedef std::vector<std::shared_ptr<Allocator> > AllocatorVector;
typedef std::unordered_map<std::string, std::shared_ptr<Allocator> > AllocatorMap;

typedef std::vector<std::shared_ptr<DataAgent> > DataAgentVector;
typedef std::unordered_map<std::DataAgentIdentify, std::shared_ptr<DataAgent> > DataAgentMap;

typedef std::vector<std::shared_ptr<TapeCenter>> TapeCenterVector;
typedef std::unordered_map<std::string, std::shared_ptr<TapeCenter>> TapeCenterMap;

typedef std::vector<std::shared_ptr<TapeNode>> TapeNodeVector;
typedef std::unordered_map<base::EndPoint, std::shared_ptr<TapeNode>> TapeNodeMap;

typedef std::vector<std::shared_ptr<StateService>> StateServiceVector;

typedef std::vector<std::shared_ptr<Disk> > DiskVector;
typedef std::unordered_map<int, std::shared_ptr<Disk> > DiskMap; //disk id->disk

typedef std::vector<std::shared_ptr<Node> > NodeVector;
typedef std::vector<uint64_t> PNodeVector;
typedef std::unordered_map<base::EndPoint, std::shared_ptr<Node> > NodeMap;

typedef std::vector<std::shared_ptr<AZ> > AZVector;
typedef std::unordered_map<std::string, std::shared_ptr<AZ> > AZMap; //az name -> az

typedef std::vector<std::shared_ptr<IDC> > IDCVector;
typedef std::unordered_map<std::string, std::shared_ptr<IDC> > IDCMap; //az name -> az

typedef std::vector<std::shared_ptr<Rack> > RackVector;
typedef std::unordered_map<std::string, std::shared_ptr<Rack> > RackMap; //az name -> az

typedef std::vector<std::shared_ptr<Group> > GroupVector;
typedef std::unordered_map<std::string, std::shared_ptr<Group> > GroupMap;

typedef std::vector<std::shared_ptr<Vlet> > VletVector;
typedef std::unordered_map<uint64_t, std::shared_ptr<Vlet> > VletMap;

typedef std::vector<std::shared_ptr<Volume> > VolumeVector;
typedef std::unordered_map<uint64_t, std::shared_ptr<Volume> > VolumeMap;

typedef std::vector<std::shared_ptr<Space> > SpaceVector;
typedef std::unordered_map<std::string, std::shared_ptr<Space> > SpaceMap;

typedef std::vector<std::shared_ptr<TapeLibraryGroup> > TapeLibraryGroupVector;
typedef std::unordered_map<std::string, std::shared_ptr<TapeLibraryGroup> > TapeLibraryGroupMap;

typedef std::vector<std::shared_ptr<TapeLogicalPool> > TapeLogicalPoolVector;
typedef std::unordered_map<std::string, std::shared_ptr<TapeLogicalPool> > TapeLogicalPoolMap;

class RaftMetaBase {
public:
    void set_raft_index(uint64_t raft_index) {
        _raft_index = raft_index;
    }
    uint64_t raft_index() const {
        return _raft_index;
    }
private:
    uint64_t _raft_index = 0;
};

class NodeBase : public RaftMetaBase {
public:
    NodeBase() : _addr(base::EndPoint()), _last_active_timestamp(0), _is_alive(false),
        _flapping_counter(0),
        _basic_metrics(std::shared_ptr<aries::pb::BasicMetrics>(new aries::pb::BasicMetrics)) {}

    NodeBase(base::EndPoint addr) : _addr(addr), _last_active_timestamp(0), _is_alive(false),
            _flapping_counter(0),
            _basic_metrics(std::shared_ptr<aries::pb::BasicMetrics>(new aries::pb::BasicMetrics)) {}
    virtual ~NodeBase() {}

    void set_basic_merics(std::shared_ptr<aries::pb::BasicMetrics> metrics) {
        common::ScopedMutexLock lock(_mutex);
        _basic_metrics = metrics;
    }
    std::shared_ptr<aries::pb::BasicMetrics> basic_metrics() {
        common::ScopedMutexLock lock(_mutex);
        return _basic_metrics;
    }
    virtual base::EndPoint addr() const {
        return _addr;
    }
    Version version() {
        common::ScopedMutexLock lock(_mutex);
        return _version;
    }
    void set_version(const Version& version) {
        common::ScopedMutexLock lock(_mutex);
        _version = version;
    }
    uint64_t last_active_timestamp() const {
        return _last_active_timestamp;
    }
    void set_last_active_timestamp(const uint64_t time) {
        _last_active_timestamp = time;
    }
    bool is_alive() const {
        return _is_alive;
    }
    void set_alive(const bool is_alive) {
        _flapping_counter += (_is_alive == is_alive ? 0 : 1);
        _is_alive = is_alive;
    }
    uint64_t flapping_counter() {
        return _flapping_counter;
    }
protected:
    mutable common::MutexLock _mutex;
    base::EndPoint _addr;
    Version _version;
    std::atomic<uint64_t> _last_active_timestamp;
    std::atomic<bool> _is_alive;
    std::atomic<uint64_t> _flapping_counter;
    std::shared_ptr<aries::pb::BasicMetrics> _basic_metrics;
};

class ManagerBase {
public:
    void lock() {
        _mutex.lock();
    }
    void unlock() {
        _mutex.unlock();
    }
    uint64_t get_add_op_counter() {
        return _add_op_counter;
    }

    uint64_t get_drop_op_counter() {
        return _drop_op_counter;
    }
protected:
    uint64_t _add_op_counter = 0;
    uint64_t _drop_op_counter = 0;
    common::MutexLock _mutex;
};

struct DiskStat {
    DiskStat() {}
    DiskStat(int id_,
             uint64_t disk_total_size_,
             uint64_t disk_free_size_,
             uint64_t aries_capacity_,
             uint64_t used_size_,
             uint64_t disk_ioutil_,
             uint64_t read_iops_,
             uint64_t read_throughput_,
             uint64_t write_iops_,
             uint64_t write_throughput_) :
                id(id_),
                disk_total_size(disk_total_size_),
                disk_free_size(disk_free_size_),
                aries_capacity(aries_capacity_),
                used_size(used_size_),
                disk_ioutil(disk_ioutil_),
                read_iops(read_iops_),
                read_throughput(read_throughput_),
                write_iops(write_iops_),
                write_throughput(write_throughput_){}

    void set_calc_used_size(uint64_t calc_used_size_) {
        has_calc_used_size = true;
        calc_used_size = calc_used_size_;
    }

    void set_reserved_size_for_rewrite(uint64_t reserved_size_for_rewrite_) {
        reserved_size_for_rewrite = reserved_size_for_rewrite_;
    }

    int id = -1;
    uint64_t disk_total_size = 0;
    uint64_t disk_free_size = 0;
    uint64_t aries_capacity = 0;
    uint64_t used_size = 0;
    uint64_t disk_ioutil = 0;
    uint64_t read_iops = 0;
    uint64_t read_throughput = 0;
    uint64_t write_iops = 0;
    uint64_t write_throughput = 0;
    // for special case: archive volume used size calculation
    bool has_calc_used_size = false;
    uint64_t calc_used_size = 0;
    uint64_t reserved_size_for_rewrite = 0;
};

class Disk : public RaftMetaBase {
public:
    Disk(const int id, const std::shared_ptr<Node>& node)
        : _disk_id(id), _node_ptr(node) {
    }
    Disk(const int id, const uint64_t time, const std::shared_ptr<Node>& node) :
        _disk_id(id), _create_time(time), _node_ptr(node) {
    }

    void update_disk_stat(const DiskStat& disk_stat) {
        _disk_total_size = disk_stat.disk_total_size;
        _disk_free_size = disk_stat.disk_free_size;
        _aries_capacity = disk_stat.aries_capacity;
        _report_used_size = disk_stat.used_size;
        _total_size = _disk_total_size > _aries_capacity ? _aries_capacity : _disk_total_size;
        _disk_ioutil = disk_stat.disk_ioutil;
        _read_iops = disk_stat.read_iops;
        _read_throughput = disk_stat.read_throughput;
        _write_iops = disk_stat.write_iops;
        _write_throughput = disk_stat.write_throughput;
        _reserved_size_for_rewrite = disk_stat.reserved_size_for_rewrite;
        if (disk_stat.has_calc_used_size) {
            common::ScopedMutexLock lock(_mutex);
            _calc_used_size = disk_stat.calc_used_size;
        }
    }

    bool is_full();

    double usage() const {
    //    return (double)_used_size / (double)_disk_total_size;
        return (double)_calc_used_size / (double)(_total_size + 1);
    }
    double logic_usage() const {
        return (double)_calc_used_size / (double)(logic_total_size() + 1);
    }
    double free_usage() const {
        return (double)_disk_free_size / (double)(_disk_total_size + 1);
    }

    uint64_t create_time() const {
        return _create_time;
    }
    uint64_t disk_free_size() const {
        return _disk_free_size;
    }
    uint64_t used_size() const {
        return _calc_used_size;
    }
    uint64_t reserved_size_for_rewrite() const {
        return _reserved_size_for_rewrite;
    }
    uint64_t aries_capacity() const {
        return _aries_capacity;
    }
    uint64_t disk_total_size() const {
        return _disk_total_size;
    }
    uint64_t total_size() const{
        return _total_size;
    }
    uint64_t logic_total_size() const{
        //total_size is enough now. 
        //when need use : min(_total_size, _disk_free_size + _calc_used_size);
        return total_size();
    }
    uint64_t reported_used_size() const{
        return _report_used_size;
    }
    uint64_t disk_reserve_size() const {
        return (_disk_total_size * FLAGS_disk_reserve_percent) / 100;
    }
    uint64_t ioutil() const{
        return _disk_ioutil;
    }
    uint64_t read_iops() const {
        return _read_iops;
    }
    uint64_t read_throughput() const {
        return _read_throughput;
    }
    uint64_t write_iops() const {
        return _write_iops;
    }
    uint64_t write_throughput() const {
        return _write_throughput;
    }
    ::base::EndPoint addr();
    int disk_id() const {
        return _disk_id;
    }
    std::shared_ptr<Node> node() {
        return _node_ptr.lock();
    }
    void set_state(const DiskState& state) {
        common::ScopedMutexLock lock(_mutex);
        _state = state;
    }
    DiskState state() {
        common::ScopedMutexLock lock(_mutex);
        return _state;
    }
    void set_disk_type(const std::string& type) {
        _disk_type = type;
    }
    void set_engine_type(const EngineType type) {
        common::ScopedMutexLock lock(_mutex);
        _engine_type = type;
    }
    std::string disk_type() const {
        return _disk_type;
    }
    EngineType engine_type() {
        common::ScopedMutexLock lock(_mutex);
        return _engine_type;
    }
    size_t get_vlet_num() const {
        return _vlet_map.size();
    }

    int get_space_vlet_num(const std::string& space_name);
    std::shared_ptr<Vlet> get_vlet(const uint64_t volume_id);
    int add_vlet(const std::shared_ptr<Vlet>& vlet_ptr);
    int drop_vlet(const std::shared_ptr<Vlet>& vlet_ptr);

    void get_exist_vlets(VletVector* Vlets);
    void get_space_exist_vlets(const std::string& space_name, VletVector* vlets);
    void get_vlet_list(VletVector* vlet_list);

    void add_calc_used_size(uint64_t p) {
        common::ScopedMutexLock lock(_mutex);
        _calc_used_size += p;
    }
    void drop_calc_used_size(uint64_t p) {
        common::ScopedMutexLock lock(_mutex);
        if (_calc_used_size >= p) {
            _calc_used_size -= p;
        }
    }
    void serialize(aries::pb::DiskInfo* disk_info) {
        disk_info->set_node_addr(common::endpoint2int(addr()));
        disk_info->set_disk_id(_disk_id);
        disk_info->set_create_time(_create_time);
        disk_info->set_disk_type(_disk_type);
        disk_info->set_engine_type(_engine_type);
    }
    void serialize(aries::pb::DiskInfoInDB* disk_info) {
        common::ScopedMutexLock lock(_mutex);
        disk_info->set_node_addr(common::endpoint2str(addr()));
        disk_info->set_disk_id(_disk_id);
        disk_info->set_create_time(_create_time);
        disk_info->set_disk_type(_disk_type);
        disk_info->set_engine_type(EngineType_Name(_engine_type));
    }

private:
    int _disk_id = -1;
    uint64_t _create_time = 0;
    std::weak_ptr<Node> _node_ptr;
    DiskState _state = DISK_STATE_NORMAL;
    std::string _disk_type;
    EngineType _engine_type = ENGINE_AUTO;
    uint64_t _total_size = 0; //min(_disk_total_size, _aries_capacity)
    uint64_t _disk_total_size = 0; //physical total size reported from dn.
    uint64_t _disk_free_size = 0; //physical free size reported from dn.
    uint64_t _aries_capacity = 0; //_report_used_size should less than _aries_capacity??
    uint64_t _report_used_size = 0; //physical used size reported from dn.
    uint64_t _calc_used_size = 0; //calculated used size according existing vlets.
    uint64_t _disk_ioutil = 0; // disk ioutil reported from dn
    uint64_t _read_iops = 0; // read iops reported from dn
    uint64_t _read_throughput = 0; // read throughput reported from dn
    uint64_t _write_iops = 0; // write iops reported from dn
    uint64_t _write_throughput = 0; // write throughput reported from dn
    uint64_t _reserved_size_for_rewrite; //disk space reserve for rewriting

    VletMap _vlet_map;
    common::MutexLock _mutex;
};

/************************ Begin of DataNode *************************/

struct NodeOptions {
    base::EndPoint addr = base::EndPoint();
    std::string group_name = "";
    std::string az_name = "";
    std::string idc_name = "";
    std::string rack_name = ""; 
    NodeState state = NODE_STATE_NORMAL;
    uint64_t create_time = 0;
};

class Node : public NodeBase {
public:
    Node(const NodeOptions& options) :
        NodeBase(options.addr),
        _group_name(options.group_name),
        _az_name(options.az_name),
        _idc_name(options.idc_name),
        _rack_name(options.rack_name),
        _state(options.state),
        _create_time(options.create_time) {
        _offline_time = _create_time / 1000000L;
    }

    uint64_t create_time() const {
        return _create_time;
    }

    std::string group_name() {
        common::ScopedMutexLock lock(_mutex);
        return _group_name;
    }
    void  set_group_name(const std::string& group_name) {
        common::ScopedMutexLock lock(_mutex);
        _group_name = group_name;
    }
    std::string az_name() {
        common::ScopedMutexLock lock(_mutex);
        return _az_name;
    }
    void  set_az_name(const std::string& az_name) {
        common::ScopedMutexLock lock(_mutex);
        _az_name = az_name;
    }
    std::string idc_name() {
        common::ScopedMutexLock lock(_mutex);
        return _idc_name;
    }
    void  set_idc_name(const std::string& idc_name) {
        common::ScopedMutexLock lock(_mutex);
        _idc_name = idc_name;
    }
    std::string rack_name() {
        common::ScopedMutexLock lock(_mutex);
        return _rack_name;
    }
    void  set_rack_name(const std::string& rack_name) {
        common::ScopedMutexLock lock(_mutex);
        _rack_name = rack_name;
    }
    uint64_t offline_time() {
        return _offline_time;
    }
    void set_offline_time(const uint64_t time) {
        _offline_time = time;
    }
    uint64_t online_time() {
        return _online_time;
    }
    void set_online_time(const uint64_t time) {
        _online_time = time;
    }
    void set_state(const NodeState state) {
        _state = state;
    }
    NodeState state() const {
        return _state;
    }
    bool state_permit_balance_dest() const {
        return _state == NODE_STATE_NORMAL || _state == NODE_STATE_UNBALANCED;
    }

    bool state_permit_balance_src() const {
        return _state == NODE_STATE_NORMAL ||
            _state == NODE_STATE_DECOMMISSIONING;
    }

    bool state_permit_recover() const {
        return _state == NODE_STATE_NORMAL || _state == NODE_STATE_UNBALANCED;
    }

    bool state_permit_create() const {
        return _state == NODE_STATE_NORMAL;
    }

    bool state_permit_compact_balance() const {
        return _state == NODE_STATE_NORMAL || _state == NODE_STATE_UNBALANCED;
    }

    uint64_t max_ioutil() const {
        return _max_ioutil;
    }

    int max_ioutil_disk_id() const {
        return _max_ioutil_disk_id;
    }

    double min_disk_usage();
    double usage();
    double free_usage() {
        return (double)_disk_free_size / (double)(_disk_total_size + 1);
    }

    uint64_t get_free_space() {
        return _disk_free_size;
    }

    // safemode
    bool is_disk_safemode() {
        common::ScopedMutexLock lock(_mutex);
        return _is_disk_safemode;
    }
    uint64_t disk_safemode_uptime() {
        common::ScopedMutexLock lock(_mutex);
        return _disk_safemode_uptime;
    }
    void set_disk_safemode(const bool is_safemode,
        const uint64_t time) {
        common::ScopedMutexLock lock(_mutex);
        _is_disk_safemode = is_safemode;
        _disk_safemode_uptime = time;
    }

    void update_disk_stat(const std::vector<DiskStat>& disk_stat_vector);
    std::shared_ptr<Disk> get_disk(const int disk_id);
    void get_disk_list(DiskVector* disk_list);
    int add_disk(const std::shared_ptr<Disk>& disk_ptr);
    int drop_disk(const int disk_id);
    //int add_vlet(const std::shared_ptr<Vlet> vlet_ptr);
    //int drop_vlet(const std::shared_ptr<Vlet>& vlet_ptr);
    void get_exist_vlets(VletVector* vlets);
    void get_vlet_list(VletVector* vlets);
    size_t get_vlet_num();
    std::shared_ptr<Vlet> get_vlet(const uint64_t volume_id);

    uint64_t used_size() const {
        return _calc_used_size;
    }
    uint64_t total_size() const {
        return _total_size;
    }
    uint64_t free_size() const {
        return _disk_free_size;
    }
    uint64_t disk_total_size() const {
        return _disk_total_size;
    }
    void serialize(aries::pb::NodeInfo* node_info) {
        common::ScopedMutexLock lock(_mutex);
        node_info->set_node_addr(common::endpoint2int(_addr));
        node_info->set_is_alive(_is_alive);
        node_info->set_az_name(_az_name);
        node_info->set_idc_name(_idc_name);
        node_info->set_rack_name(_rack_name);
        node_info->set_group_name(_group_name);
        node_info->set_state(_state);
        node_info->set_create_time(_create_time);
        node_info->set_online_time(_online_time);
        node_info->set_offline_time(_offline_time);
        node_info->set_is_disk_safemode(_is_disk_safemode);
        node_info->set_disk_safemode_uptime(_disk_safemode_uptime);
    }
    void serialize(aries::pb::NodeInfoInDB* node_info) {
        common::ScopedMutexLock lock(_mutex);
        node_info->set_node_addr(common::endpoint2str(_addr));
        node_info->set_is_alive(_is_alive);
        node_info->set_az_name(_az_name);
        node_info->set_idc_name(_idc_name);
        node_info->set_rack_name(_rack_name);
        node_info->set_group_name(_group_name);
        node_info->set_state(NodeState_Name(_state));
        node_info->set_create_time(_create_time);
        node_info->set_online_time(_online_time);
        node_info->set_offline_time(_offline_time);
        node_info->set_is_disk_safemode(_is_disk_safemode);
        node_info->set_disk_safemode_uptime(_disk_safemode_uptime);
    }
private:
    double _usage = 1;
    std::string _group_name = "";
    std::string _az_name = "";
    std::string _idc_name = "";
    std::string _rack_name = "";
    std::atomic<NodeState> _state;
    uint64_t _create_time = 0;
    DiskMap _disk_map;

    uint64_t _offline_time = 0;
    uint64_t _online_time = 0;

    bool _is_disk_safemode = false;         // cannot drop disk
    uint64_t _disk_safemode_uptime = 0;     // enter disk safemode time

    uint64_t _disk_total_size = 0;
    uint64_t _disk_free_size = 0;
    uint64_t _aries_capacity = 0;
    uint64_t _report_used_size = 0;
    uint64_t _calc_used_size = 0;
    uint64_t _total_size = 0;

    int _max_ioutil_disk_id = -1;
    uint64_t _max_ioutil = 0;  // [0, 100]
};

class NodeManager : public ManagerBase {
public:
    int add_node(const std::shared_ptr<Node>& node_ptr);
    int drop_node(const ::base::EndPoint& addr);
    std::shared_ptr<Node> get_node(const ::base::EndPoint& addr);
    void get_node_list(NodeVector* node_list);
    void get_node_addr_list(std::vector<uint64_t>* node_addr_list) {
        common::ScopedMutexLock lock(_mutex);
        for (auto& node : _node_map) {
            node_addr_list->push_back(common::endpoint2int(node.second->addr()));
        }
    }

    NodeMap& get_node_map() {
        return _node_map;
    }

private:
    NodeMap _node_map;
};

/*
class NotAddNodeManager {
    void add_node(const base::EndPoint node) {
        common::ScopedMutexLock lock(_mutex);
        _node_map[node] = time(NULL);
    }
    void get_node_list(std::vector<uint64_t>* node_list) {
        common::ScopedMutexLock lock(_mutex);
        auto time = time(NULL);
        for (auto& node : _node_map) {
            if (time > node.second + FLAGS_datanode_dead_threshold_in_second) {
                _node_map.erase(node);
                continue;
            }
            auto node_addr = common::int2endpoint(node.first);
            auto node_ptr = g_node_manager->get_node(node_addr);
            if (node_ptr != nullptr) {
                continue;
            }
            node_list->push_back(node.first);
        }
    }

private:
    std::map<uint64_t, uint64_t> _node_map;
    common::MutexLock _mutex;
}
*/

class AZ {
public:
    AZ(const std::string& az_name) : _az_name(az_name), _is_safemode(false), _is_light_safemode(false) {}
    int add_node(const std::shared_ptr<Node>& node_ptr);
    int drop_node(const std::shared_ptr<Node>& node_ptr);
    void get_node_list(NodeVector* node_list);
    void get_idc_list(IDCVector* idc_list) {
        common::ScopedMutexLock lock(_mutex);
        for (auto& idc_iter : _idc_map) {
            idc_list->push_back(idc_iter.second);
        }
    }
    std::string az_name() const {
        return _az_name;
    }

    void enter_safemode();
    void leave_safemode();
    bool is_safemode();

    void enter_light_safemode();
    void leave_light_safemode();
    bool is_light_safemode();

private:
    std::string _az_name = "";
    IDCMap _idc_map;
    common::MutexLock _mutex;
    // for az safe mode, not persist in raft fsm, reboot lose state
    std::atomic<bool> _is_safemode;
    std::atomic<bool> _is_light_safemode;
};

/************************ End of DataNode ***************************/

/************************ Begin of Tinker *************************/

class Tinker : public NodeBase {
public:
    Tinker(const std::string& name, const base::EndPoint& addr)
            : NodeBase(addr), _name(name) {
    }

    std::string name() const {
        return _name;
    }

    void serialize(aries::pb::TinkerInfo* tinker_info) {
        tinker_info->set_tinker_name(_name);
        tinker_info->set_tinker_addr(common::endpoint2int(_addr));
        tinker_info->set_is_alive(_is_alive);
    }

private:
    std::string _name;
};

class TinkerManager : public ManagerBase {
public:
    int add_tinker(const std::shared_ptr<Tinker>& tinker);
    int drop_tinker(const std::string& name);
    std::shared_ptr<Tinker> get_tinker(const std::string& name);
    void get_tinker_list(TinkerVector* tinker_list);
    TinkerMap& get_tinker_map() {
        return _tinker_map;
    }

private:
    TinkerMap _tinker_map;
};

/************************ End of Tinker ***************************/

/************************ Begin of Repairer *************************/

class Repairer : public NodeBase {
public:
    Repairer(const base::EndPoint& addr) : NodeBase(addr) {}
};

class RepairerManager : public ManagerBase {
public:
    int add_repairer(const std::shared_ptr<Repairer>& tinker);
    int drop_repairer(const base::EndPoint& addr);
    std::shared_ptr<Repairer> get_repairer(const base::EndPoint& addr);
    void get_repairer_list(RepairerVector* repairer_list);

    RepairerMap& get_repairer_map() {
        return _repairer_map;
    }

private:
    RepairerMap _repairer_map;
};

/************************ End of Repairer ***************************/

/************************ Begin of Checkcenter *************************/

class Checkcenter : public NodeBase {
public:
    Checkcenter(const base::EndPoint& addr) : NodeBase(addr) { }
};

class CheckcenterManager : public ManagerBase {
public:
    int add_checkcenter(const std::shared_ptr<Checkcenter>& checkcenter);
    int drop_checkcenter(const base::EndPoint& addr);
    std::shared_ptr<Checkcenter> get_checkcenter(const base::EndPoint& addr);
    void get_checkcenter_list(CheckcenterVector* checkcenter_list);

    CheckcenterMap& get_checkcenter_map() {
        return _checkcenter_map;
    }
private:
    CheckcenterMap _checkcenter_map;
};

/************************ End of Checkcenter ***************************/

/************************ Begin of Monitorcenter *************************/

class Monitorcenter : public NodeBase {
public:
    Monitorcenter(const base::EndPoint& addr) : NodeBase(addr) {}
};

class MonitorcenterManager : public ManagerBase {
public:
    int add_monitorcenter(const std::shared_ptr<Monitorcenter>& monitorcenter);
    int drop_monitorcenter(const base::EndPoint& addr);
    std::shared_ptr<Monitorcenter> get_monitorcenter(const base::EndPoint& addr);
    void get_monitorcenter_list(MonitorcenterVector* monitorcenter_list);

    MonitorcenterMap& get_monitorcenter_map() {
        return _monitorcenter_map;
    }

private:
    MonitorcenterMap _monitorcenter_map;
};

/************************ End of Monitorcenter ***************************/

/************************ Begin of VolumeService *************************/

class VolumeService : public NodeBase {
public:
    VolumeService(const base::EndPoint& addr) : NodeBase(addr) {}
};

class VolumeServiceManager : public ManagerBase {
public:
    int add_volume_service(const std::shared_ptr<VolumeService>& volumeservice);
    int drop_volume_service(const ::base::EndPoint& addr);
    std::shared_ptr<VolumeService> get_volume_service(const base::EndPoint& addr);
    void get_volume_service_list(VolumeServiceVector* volume_service_list);
    VolumeServiceMap& get_volume_service_map() {
        return _volume_service_map;
    }

private:
    VolumeServiceMap _volume_service_map;
};

/************************ End of VolumeService ***************************/

/************************ Begin of Allocator ************************/

class Allocator : public NodeBase {
public:
    Allocator() : NodeBase() {}
    Allocator(const std::string& name, const base::EndPoint& addr, const uint32_t sequence_id) :
            NodeBase(addr),
            _name(name),
            _sequence_id(sequence_id) {}

    std::string name() const {
        return _name;
    }

    base::EndPoint addr() {
        common::ScopedMutexLock lock(_mutex);
        return _addr;
    }
    void update_addr(const base::EndPoint& addr) {
        common::ScopedMutexLock lock(_mutex);
        _addr = addr;
    }
    uint32_t get_sequence_id() const {
        return _sequence_id;
    }
    void set_sequence_id(const uint32_t sequence_id) {
        _sequence_id = sequence_id;
    }
    void serialize(aries::pb::AllocatorInfo* allocator_info) {
        common::ScopedMutexLock lock(_mutex);
        allocator_info->set_allocator_name(_name);
        allocator_info->set_allocator_addr(common::endpoint2int(_addr));
        allocator_info->set_is_alive(_is_alive);
        allocator_info->set_sequence_id(_sequence_id);
        for (auto& space_free_iter : _space_free_map) {
            auto space_free_info = allocator_info->add_space_free();
            space_free_info->set_space_name(space_free_iter.first);
            space_free_info->set_free_space(space_free_iter.second);
        }
    }
    void clear_space_free_map() {
        common::ScopedMutexLock lock(_mutex);
        _space_free_map.clear();
    }
    void update_space_free_size(const std::string& space_name, const uint64_t size) {
        common::ScopedMutexLock lock(_mutex);
        _space_free_map[space_name] = size;
    }
private:
    std::string _name = "";
    uint32_t _sequence_id = 0;
    std::unordered_map<std::string, uint64_t> _space_free_map;
};

class AllocatorManager : public ManagerBase {
public:
    int add_allocator(const std::shared_ptr<Allocator>& allocator);
    int drop_allocator(const std::string& server_name);
    std::shared_ptr<Allocator> get_allocator(const std::string& server_name);

    AllocatorMap& get_allocator_map() {
        return _allocator_map;
    }

    void clear_space_free_map() {
        common::ScopedMutexLock lock(_mutex);
        for (auto& allocator : _allocator_map) {
            allocator.second->clear_space_free_map();
        }
    }

    void get_allocator_list(AllocatorVector* allocator_list) {
        common::ScopedMutexLock lock(_mutex);
        for (auto& allocator : _allocator_map) {
            allocator_list->push_back(allocator.second);
        }
    }

    int assign_sequence_id(const std::string& name, uint32_t* sequence_id) {
        common::ScopedMutexLock lock(_mutex);
        auto iter = _allocator_map.find(name);
        if (iter != _allocator_map.end()) {
            ++_max_sequence_id;
            iter->second->set_sequence_id(_max_sequence_id);
            *sequence_id = _max_sequence_id;
            return 0;
        } else {
            return -1;
        }
    }

    void inc_allocators_sequence_id() {
        common::ScopedMutexLock lock(_mutex);
        //todo sort;
        for (auto& allocator : _allocator_map ) {
            ++_max_sequence_id;
            allocator.second->set_sequence_id(_max_sequence_id);
        }
    }
    uint32_t get_max_sequence_id() const {
        return _max_sequence_id;
    }
    void set_max_sequence_id(uint32_t id) {
        _max_sequence_id = id;
    }

private:
    AllocatorMap _allocator_map;
    uint32_t _max_sequence_id = 0;
};

/************************ End of Allocator ***************************/

/************************ Begin of DataAgent *************************/

class DataAgent : public NodeBase {
public:
    DataAgent(const base::EndPoint& addr, const std::string name) : NodeBase(addr), _name(name),
            _failed_metrics(std::shared_ptr<aries::pb::ProxyFailedMetrics>(new aries::pb::ProxyFailedMetrics)){
        _id = std::make_pair(addr, name);
    }
    std::string name() {
        return _name;
    }
    std::DataAgentIdentify dataagent_identify() {
        return _id;
    }
    void set_failed_merics(std::shared_ptr<aries::pb::ProxyFailedMetrics> metrics) {
        common::ScopedMutexLock lock(_mutex);
        _failed_metrics = metrics;
    }
    void add_user_io_stats(std::shared_ptr<aries::pb::ProxySpaceUserIoStats> single_io_stats) {
        common::ScopedMutexLock lock(_mutex);
        _user_io_stats[single_io_stats->space_name()] = single_io_stats;
    }
    std::shared_ptr<aries::pb::ProxyFailedMetrics> failed_metrics() {
        common::ScopedMutexLock lock(_mutex);
        return _failed_metrics;
    }
    std::map<std::string, std::shared_ptr<aries::pb::ProxySpaceUserIoStats>> user_io_stats() {
        common::ScopedMutexLock lock(_mutex);
        return _user_io_stats;
    }
private:
    std::string _name;
    std::DataAgentIdentify _id;
    std::shared_ptr<aries::pb::ProxyFailedMetrics> _failed_metrics;
    std::map<std::string, std::shared_ptr<aries::pb::ProxySpaceUserIoStats>> _user_io_stats;
};

class DataAgentManager : public ManagerBase {
public:
    int add_dataagent(const std::shared_ptr<DataAgent>& dataagent);
    int drop_dataagent(const std::DataAgentIdentify& id);
    std::shared_ptr<DataAgent> get_dataagent(const std::DataAgentIdentify& id);
    void get_dataagent_list(DataAgentVector* dataagent_list) {
        common::ScopedMutexLock lock(_mutex);
        for (auto& dataagent : _dataagent_map) {
            dataagent_list->push_back(dataagent.second);
        }
    }
    DataAgentMap& get_dataagent_map() {
        return _dataagent_map;
    }

    double put_sla() {
        double error_put = 0;
        double total_put = 0.001;
        common::ScopedMutexLock lock(_mutex);
        for (auto& dataagent : _dataagent_map) {
            auto cur_total_put_qps = (dataagent.second)->failed_metrics()->total_put_qps();
            if (cur_total_put_qps == 0) {
                continue;
            }
            error_put += (dataagent.second)->failed_metrics()->put_error_qps();
            total_put += cur_total_put_qps;
        }
        return (1 - error_put / total_put);
    }

private:
    DataAgentMap _dataagent_map;
};

/************************ End of DataAgent ****************************/

/************************ Begin of TapeCenter *************************/

class TapeCenter : public NodeBase {
public:
    TapeCenter(const std::string& name, const base::EndPoint& addr)
            : NodeBase(addr), _name(name) {
    }

    std::string name() const {
        return _name;
    }

    void serialize(aries::pb::TapeCenterInfo* tape_center_info) {
        tape_center_info->set_tape_center_name(_name);
        tape_center_info->set_tape_center_addr(common::endpoint2int(_addr));
        tape_center_info->set_is_alive(_is_alive);
        tape_center_info->set_version(_version.to_string());
    }

private:
    std::string _name;
};

class TapeCenterManager : public ManagerBase {
public:
    int add_tape_center(const std::shared_ptr<TapeCenter>& tape_center);
    int drop_tape_center(const std::string& name);
    std::shared_ptr<TapeCenter> get_tape_center(const std::string& name);
    void get_tape_center_list(TapeCenterVector* tape_center_list);
    TapeCenterMap& get_tape_center_map() {
        return _tape_center_map;
    }

private:
    TapeCenterMap _tape_center_map;
};

/************************ Begin of TapeNode *************************/

struct TapeNodeOptions {
    base::EndPoint addr = base::EndPoint();
    std::string logical_pool_name = "";
    std::vector<std::string> physical_pool_list;
    TapeNodeState state = TAPE_NODE_STATE_NORMAL;
    uint64_t create_time = 0;
};

class TapeNode : public NodeBase {
public:
    TapeNode(const TapeNodeOptions& options) :
        NodeBase(options.addr),
        _logical_pool_name(options.logical_pool_name),
        _physical_pool_list(options.physical_pool_list),
        _state(options.state),
        _create_time(options.create_time) {
        _offline_time = _create_time;
    }

    uint64_t create_time() const {
        common::ScopedMutexLock lock(_mutex);
        return _create_time;
    }
    std::string logical_pool_name() {
        common::ScopedMutexLock lock(_mutex);
        return _logical_pool_name;
    }
    void set_logical_pool_name(const std::string& pool_name) {
        common::ScopedMutexLock lock(_mutex);
        _logical_pool_name = pool_name;
    }
    std::vector<std::string> physical_pool_names() {
        common::ScopedMutexLock lock(_mutex);
        return _physical_pool_list;
    }
    void set_physical_pool_name(const std::string& pool_name) {
        common::ScopedMutexLock lock(_mutex);
        _physical_pool_list.push_back(pool_name);
    }
    uint64_t offline_time() {
        common::ScopedMutexLock lock(_mutex);
        return _offline_time;
    }
    void set_offline_time(const uint64_t time) {
        common::ScopedMutexLock lock(_mutex);
        _offline_time = time;
    }
    uint64_t online_time() {
        common::ScopedMutexLock lock(_mutex);
        return _online_time;
    }
    void set_online_time(const uint64_t time) {
        common::ScopedMutexLock lock(_mutex);
        _online_time = time;
    }
    void set_state(const TapeNodeState state) {
        common::ScopedMutexLock lock(_mutex);
        _state = state;
    }
    TapeNodeState state() const {
        common::ScopedMutexLock lock(_mutex);
        return _state;
    }

    void serialize(pb::TapeNodeInfo* tape_node_info) {
        common::ScopedMutexLock lock(_mutex);
        tape_node_info->set_node_addr(common::endpoint2int(_addr));
        tape_node_info->set_is_alive(_is_alive);
        tape_node_info->set_logical_pool_name(_logical_pool_name);
        tape_node_info->set_version(_version.to_string());
        tape_node_info->set_node_state(_state);
        tape_node_info->set_create_time(_create_time);
        tape_node_info->set_online_time(_online_time);
        tape_node_info->set_offline_time(_offline_time);
        for (auto i : _physical_pool_list) {
            tape_node_info->add_physical_pool_names(i);
        }
        // TODO:: add TapeDriver
    }

private:
    std::string _logical_pool_name = "";
    std::vector<std::string> _physical_pool_list;
    std::atomic<TapeNodeState> _state;
    uint64_t _create_time = 0;
    uint64_t _offline_time = 0;
    uint64_t _online_time = 0;
};

class TapeNodeManager : public ManagerBase {
public:
    int add_tape_node(const std::shared_ptr<TapeNode>& tape_node);
    int drop_tape_node(const ::base::EndPoint& addr);
    std::shared_ptr<TapeNode> get_tape_node(const base::EndPoint& addr);
    void get_tape_node_list(TapeNodeVector* tape_node_list);
    TapeNodeMap& get_tape_node_map() {
        return _tape_node_map;
    }

private:
    TapeNodeMap _tape_node_map;
};

/************************ End of TapeNode ***************************/

/************************ Begin of StateService *************************/
class StateService : public NodeBase {
public:
    StateService(const base::EndPoint& addr, uint64_t group_id) : NodeBase(addr),
        _group_id(group_id){}

    uint64_t group_id() {
        return _group_id;
    }
    bool leader() {
        common::ScopedMutexLock lock(_mutex);
        return _leader;
    }
    void set_leader(bool leader) {
        common::ScopedMutexLock lock(_mutex);
        _leader = leader;
    }
    void serialize(pb::StateServiceInfo* info) {
        common::ScopedMutexLock lock(_mutex);
        info->set_addr(common::endpoint2int(_addr));
        info->set_version(_version.to_string());
        info->set_group_id(_group_id);
        info->set_leader(_leader);
    }
private:
    const uint64_t _group_id;
    bool _leader = false;
};

class StateServiceManager : public ManagerBase {
public:
    int add_stateservice(std::shared_ptr<StateService> stateservice);
    int drop_stateservice(const base::EndPoint& addr, uint64_t group_id);
    std::shared_ptr<StateService> get_stateservice(const base::EndPoint& addr, uint64_t group_id);
    void get_stateservice_list(StateServiceVector* stateservice_list);
private:
    std::map<std::pair<base::EndPoint, uint64_t>, std::shared_ptr<StateService>> _stateservice_map;
};
/************************ End of StateService ***************************/

// az idc rack..
class Rack {
public:
    Rack(const std::string& rack_name) : _rack_name(rack_name) {}

    std::string rack_name() const {
        return _rack_name;
    }
    int add_node(const std::shared_ptr<Node>& node_ptr) {
        return _node_map.insert(std::make_pair(node_ptr->addr(), node_ptr)).second ? 0 : -1;
    }
    int drop_node(const std::shared_ptr<Node>& node_ptr) {
        return _node_map.erase(node_ptr->addr()) ? 0 : -1;
    }
    void get_node_list(NodeVector* node_list);

private:
    common::MutexLock _mutex;
    std::string _rack_name = "";
    NodeMap _node_map;
};

class IDC {
public:
    IDC(const std::string& idc_name) : _idc_name(idc_name) {}

    int add_node(const std::shared_ptr<Node>& node_ptr);
    int drop_node(const std::shared_ptr<Node>& node_ptr);
    void get_node_list(NodeVector* node_list);
    void get_rack_list(RackVector* rack_list);
    std::string idc_name() {
        common::ScopedMutexLock lock(_mutex);
        return _idc_name;
    }
private:
    std::string _idc_name = "";
    common::MutexLock _mutex;
    RackMap _rack_map;
};

class AZManager : public ManagerBase {
public:
    int add_node(const std::shared_ptr<Node>& node_ptr) {
        common::ScopedMutexLock lock(_mutex);
        auto az_name = node_ptr->az_name();
        auto az_iter = _az_map.find(az_name);
        if (az_iter == _az_map.end()) {
            auto az_ptr = std::shared_ptr<AZ>(new AZ(az_name));
            bool ok = _az_map.insert(std::make_pair(az_name, az_ptr)).second;
            assert(ok);
            return az_ptr->add_node(node_ptr);
        } else {
            return az_iter->second->add_node(node_ptr);
        }
    }

    int drop_node(const std::shared_ptr<Node>& node_ptr) {
        common::ScopedMutexLock lock(_mutex);
        auto az_name = node_ptr->az_name();
        auto az_iter = _az_map.find(az_name);
        if (az_iter != _az_map.end()) {
            auto ret = az_iter->second->drop_node(node_ptr);
            // delete empty az
            IDCVector idc_list;
            az_iter->second->get_idc_list(&idc_list);
            if (idc_list.size() == 0) {
                _az_map.erase(az_iter);
                // delete disabled az by empty az
                if (_disabled_az_name == az_name) {
                    _disabled_az_name.clear();
                }
            }
            return ret;
        }
        return -1;
    }

    int update_node(const std::shared_ptr<Node>& node_ptr, const NodeOptions& update_node_options) {
        common::ScopedMutexLock lock(_mutex);
        // erase old node
        {
            int ret = -1;
            auto az_name = node_ptr->az_name();
            auto az_iter = _az_map.find(az_name);
            if (az_iter != _az_map.end()) {
                ret = az_iter->second->drop_node(node_ptr);
                IDCVector idc_list;
                az_iter->second->get_idc_list(&idc_list);
                if (idc_list.size() == 0) {
                    _az_map.erase(az_iter);
                    // delete disabled az by empty az
                    if (_disabled_az_name == az_name) {
                        _disabled_az_name.clear();
                    }
                }
            }
            if (ret != 0) {
                LOG(WARNING) << "updata_node failed by erase old node fail.";
                return ret;
            }
        }
        // update node options
        {
            node_ptr->set_group_name(update_node_options.group_name);
            node_ptr->set_az_name(update_node_options.az_name);
            node_ptr->set_idc_name(update_node_options.idc_name);
            node_ptr->set_rack_name(update_node_options.rack_name);
        }
        // add new node
        {
            auto az_name = node_ptr->az_name();
            auto az_iter = _az_map.find(az_name);
            if (az_iter == _az_map.end()) {
                auto az_ptr = std::shared_ptr<AZ>(new AZ(az_name));
                bool ok = _az_map.insert(std::make_pair(az_name, az_ptr)).second;
                assert(ok);
                return az_ptr->add_node(node_ptr);
            } else {
                return az_iter->second->add_node(node_ptr);
            }
        }
    }

    std::shared_ptr<AZ> get_az(const std::string& az_name) {
        common::ScopedMutexLock lock(_mutex);
        auto az_iter = _az_map.find(az_name);
        return az_iter != _az_map.end() ? az_iter->second : nullptr;
    }

    void get_az_list(AZVector* az_list) {
        common::ScopedMutexLock lock(_mutex);
        for (auto& az_iter : _az_map) {
            az_list->push_back(az_iter.second);
        }
    }

    std::string disabled_az_name() {
        common::ScopedMutexLock lock(_mutex);
        return _disabled_az_name;
    }

    // ensure get_az return a valid az
    void disable_az(std::string az_name) {
        common::ScopedMutexLock lock(_mutex);
        _disabled_az_name = az_name;
    }

    // ensure get_az return a valid az
    // az_name == disabled_az_name
    void enable_az(std::string az_name) {
        assert(az_name == _disabled_az_name);
        common::ScopedMutexLock lock(_mutex);
        _disabled_az_name.clear();
    }

private:
    AZMap _az_map;
    // only support single disaster az
    // forbid balance, prevent vlet from normal maybe balance to disaster az
    // if _disble_az_name is not empty, cluster has a disabled az
    std::string _disabled_az_name;
};

struct SpaceOptions {
    std::string space_name = "";
    ECType ec_type = EC_RS_ISAL;
    VletType vlet_type;
    SpaceState state = SPACE_STATE_NORMAL;
    uint32_t k = 0;
    uint32_t n = 0;
    uint32_t reduce_redundancy_running_volume_num = 0;
    uint32_t backup_get_shard_num = 0;
    uint32_t backup_get_meta_num = 0;
    uint32_t put_quorum = 0;
    uint32_t delete_quorum = 0;
    uint32_t allocator_collect_quorum = 0;
    uint32_t membership_quorum = 0;
    uint32_t repair_finish_quorum = 0;
    uint32_t drop_normal_remain = 0;
    uint32_t max_vlet_per_az = 0;
    uint32_t max_vlet_per_idc = 0;
    uint32_t max_vlet_per_rack = 0;
    uint64_t create_time = 0;
    uint64_t update_time = 0;
    std::vector<std::string> az_list;
    std::set<std::string> group_set;
    std::set<std::string> disk_type_set;
    //std::string disk_type;
    PickAZPolicy pick_az_policy = PAZP_ROUND_BY_AZ;
    uint64_t auto_create_volume_num = 0;
    uint64_t least_free_capacity = 0;
    bool permit_rule_balance = false;
    bool read_only = false;
    std::string validator_addr = "";
    uint64_t vlet_reserve_free_size = 0;
    uint64_t max_holes_size_for_fast_remove = 0;
    uint64_t min_record_size_for_fast_remove = 0;
    std::unordered_map<int, int> vlet_fast_remove_policy_map;
    bool permit_batch_repair = false;
    bool permit_data_offset_index = false;
    std::vector<std::pair<std::string, VletType>> disk_vlet_type_map; // need keep order
    std::map<int, std::string> az_place_policy_map;
    std::unordered_map<std::string, int> az_max_vlet_policy_map;
    std::map<int, std::string> disk_type_place_policy_map;
    bool permit_compact_balance = false;
    SpaceCompactState compact_state = NO;
    bool archive_volume = false;
    // rewrite control
    uint32_t append_zone_rewrite_rate = 5;
    uint32_t daily_rewrite_start_time = 0;
    uint32_t daily_rewrite_duration_second = common::DAY_SECOND;
    // copy space info
    bool permit_copy_space = false;
    aries::pb::CopySpaceOption copy_space_option;
    // shard compress option
    aries::pb::ShardCompressOption shard_compress_option;
    bool use_standard_record_layout = false;

    ArchiveMode archive_mode = HDD;
    bool need_archived = false;
    std::string tape_library_group_name = "";
    uint64_t max_volume_repair_task_num = 0;
    uint32_t delay_delete_interval_second = UINT32_MAX;
};

struct TapePhysicalPoolStatus {
    std::string physical_pool_name = "";
    uint32_t healthy_driver_num;
};

struct TapeLogicalPoolOptions {
    std::string pool_name = "";
    uint64_t create_time = 0; 
    uint32_t tape_count = 0; 
    uint64_t physical_capacity = 0; 
    uint64_t tape_nodes = 0;
    TapePhysicalPoolStatus pool_status;
};

class TapeLogicalPoolManager : public ManagerBase {
public:
    int add_pool(const std::shared_ptr<TapeLogicalPool>& pool);
    int drop_pool(const std::shared_ptr<TapeLogicalPool>& pool);
    std::shared_ptr<TapeLogicalPool> get_pool(const std::string& pool_name);
    void get_pool_list(TapeLogicalPoolVector* pool_list) {
        common::ScopedMutexLock lock(_mutex);
        for (auto& pool : _pool_map) {
            pool_list->push_back(pool.second);
        }
    }
private:
    TapeLogicalPoolMap _pool_map;
};

class TapeLogicalPool : public RaftMetaBase {
public:
    TapeLogicalPool(const TapeLogicalPoolOptions& options) {
        _options = options;
    }
    ~TapeLogicalPool() {
    }
    std::string pool_name() {
        common::ScopedMutexLock lock(_mutex);
        return _options.pool_name;
    }
    void update_opt(const TapeLogicalPoolOptions& options) {
        common::ScopedMutexLock lock(_mutex);
        _options = options;
    }
    void update_physical_pool(const std::vector<std::string>& physical_pool_names) {
        common::ScopedMutexLock lock(_mutex);
        _physical_pool_names = physical_pool_names;
    }
    uint64_t create_time() {
        common::ScopedMutexLock lock(_mutex);
        return _options.create_time;
    }
    uint32_t tape_count() {
        common::ScopedMutexLock lock(_mutex);
        return _options.tape_count;
    }
    uint64_t physical_capacity() {
        common::ScopedMutexLock lock(_mutex);
        return _options.physical_capacity;
    }
    std::vector<std::string> physical_pool_names() {
        common::ScopedMutexLock lock(_mutex);
        return _physical_pool_names;
    }
    void add_physical_pool_names(std::string pool_name) {
        common::ScopedMutexLock lock(_mutex);
        return _physical_pool_names.push_back(pool_name);
    }
    void add_physical_pool_status(std::shared_ptr<TapePhysicalPoolStatus> pool) {
        common::ScopedMutexLock lock(_mutex);
        _physical_pools_status.push_back(pool);
    }
    std::vector<std::shared_ptr<TapePhysicalPoolStatus>> physical_pools_status() {
        common::ScopedMutexLock lock(_mutex);
        return _physical_pools_status;
    }
    void serialize(aries::pb::TapeLogicalPool* logical_pool_info) {
        common::ScopedMutexLock lock(_mutex);
        for (auto i : _physical_pool_names) {
            logical_pool_info->add_physical_pool_names(i);
        }
        logical_pool_info->set_pool_name(_options.pool_name);
        logical_pool_info->set_tape_count(_options.tape_count);
        logical_pool_info->set_physical_capacity(_options.physical_capacity);
        logical_pool_info->set_create_time(_options.create_time);
    }
private:
    TapeLogicalPoolOptions _options;
    common::MutexLock _mutex;
    std::vector<std::shared_ptr<TapePhysicalPoolStatus>> _physical_pools_status;
    std::vector<std::string> _physical_pool_names;
};

struct TapeLibraryGroupOptions {
    uint32_t group_id = 0;
    std::string group_name = "";
    std::string vendor = "";
    std::string deploy_mode = ""; 
    std::string access_mode = ""; 
    std::string tape_model = ""; 
    uint64_t capacity_per_tape = 0;
    uint32_t driver_per_node = 0;
    uint64_t create_time = 0;
    uint32_t replication = 0;
};
class TapeLibraryGroupManager : public ManagerBase {
public:
    int add_library(const std::shared_ptr<TapeLibraryGroup>& library);
    int drop_library(const std::shared_ptr<TapeLibraryGroup>& library);
    std::shared_ptr<TapeLibraryGroup> get_library(const std::string& library_name);
    void get_library_list(TapeLibraryGroupVector* library_list) {
        common::ScopedMutexLock lock(_mutex);
        for (auto& library : _library_map) {
            library_list->push_back(library.second);
        }
    }
    void set_group_id(int group_id) {
        _group_id = group_id;
    }
    int group_id() const {
        return _group_id;
    }
private:
    TapeLibraryGroupMap _library_map;
    int _group_id = 0;
};

class TapeLibraryGroup : public RaftMetaBase {
public:
    TapeLibraryGroup(const TapeLibraryGroupOptions& options) {
        _options = options;
    }
    ~TapeLibraryGroup() {
    }
    uint32_t group_id() {
        common::ScopedMutexLock lock(_mutex);
        return _options.group_id;
    }
    std::string group_name() {
        common::ScopedMutexLock lock(_mutex);
        return _options.group_name;
    }
    std::string vendor() {
        common::ScopedMutexLock lock(_mutex);
        return _options.vendor;
    }
    std::string deploy_mode() {
        common::ScopedMutexLock lock(_mutex);
        return _options.deploy_mode;
    }
    std::string access_mode() {
        common::ScopedMutexLock lock(_mutex);
        return _options.access_mode;
    }
    std::string tape_model() {
        common::ScopedMutexLock lock(_mutex);
        return _options.tape_model;
    }
    uint64_t capacity_per_tape() {
        common::ScopedMutexLock lock(_mutex);
        return _options.capacity_per_tape;
    }
    uint32_t driver_per_node() {
        common::ScopedMutexLock lock(_mutex);
        return _options.driver_per_node;
    }
    uint64_t create_time() {
        common::ScopedMutexLock lock(_mutex);
        return _options.create_time;
    }
    uint32_t replication() {
        common::ScopedMutexLock lock(_mutex);
        return _options.replication;
    }
    void add_logical_pool(std::string pool_name) {
        common::ScopedMutexLock lock(_mutex);
        _logical_pools.push_back(pool_name);
    }
    std::vector<std::string> logical_pools() {
        common::ScopedMutexLock lock(_mutex);
        return _logical_pools;
    }
    void serialize(aries::pb::TapeLibraryGroup* group_info) {
        common::ScopedMutexLock lock(_mutex);
        group_info->set_group_name(_options.group_name);
        group_info->set_group_id(_options.group_id);
        group_info->set_vendor(_options.vendor);
        group_info->set_capacity_per_tape(_options.capacity_per_tape);
        group_info->set_deploy_mode(_options.deploy_mode);
        group_info->set_access_mode(_options.access_mode);
        group_info->set_tape_model(_options.tape_model);
        group_info->set_driver_per_node(_options.driver_per_node);
        group_info->set_replication(_options.replication);
        group_info->set_create_time(_options.create_time);
        for (auto pool_name : _logical_pools) {
            auto pool_info = group_info->add_logical_pools();
            pool_info->set_pool_name(pool_name);
        }
    }
private:
    common::MutexLock _mutex;
    std::vector<std::string> _logical_pools;
    TapeLibraryGroupOptions _options;
};

class Space : public RaftMetaBase {
public:
    Space(const SpaceOptions& options) {
        _options = options;
    }
    ~Space() {
    }
    void update_opt(const SpaceOptions& options) {
        common::ScopedMutexLock lock(_mutex);
        auto state = _options.state;
        _options = options;
        _options.state = state;
    }
    void set_is_dropped(const bool is_dropped) {
        common::ScopedMutexLock lock(_mutex);
        _is_dropped = is_dropped;
    }
    void set_space_id(const int space_id) {
        common::ScopedMutexLock lock(_mutex);
        _space_id = space_id;
    }
    int space_id() {
        common::ScopedMutexLock lock(_mutex);
        return _space_id;
    }
    void set_state(const SpaceState state) {
        common::ScopedMutexLock lock(_mutex);
        _options.state = state;
    }
    SpaceState state() {
        common::ScopedMutexLock lock(_mutex);
        return _options.state;
    }
    const SpaceOptions& opt() {
        common::ScopedMutexLock lock(_mutex);
        return _options;
    }
    uint32_t max_vlet_per_rack() {
        common::ScopedMutexLock lock(_mutex);
        return _options.max_vlet_per_rack;
    }
    uint32_t max_vlet_per_idc() {
        common::ScopedMutexLock lock(_mutex);
        return _options.max_vlet_per_idc;
    }
    std::string space_name() {
        common::ScopedMutexLock lock(_mutex);
        return _options.space_name;
    }
    PickAZPolicy pick_az_policy() {
        common::ScopedMutexLock lock(_mutex);
        return _options.pick_az_policy;
    }
    std::set<std::string> group_set() {
        common::ScopedMutexLock lock(_mutex);
        return _options.group_set;
    }
    std::string validator_addr() {
        common::ScopedMutexLock lock(_mutex);
        return _options.validator_addr;
    }
    uint64_t vlet_reserve_free_size() {
        common::ScopedMutexLock lock(_mutex);
        return _options.vlet_reserve_free_size;
    }
    uint64_t max_holes_size_for_fast_remove() {
        common::ScopedMutexLock lock(_mutex);
        return _options.max_holes_size_for_fast_remove;
    }
    uint64_t min_record_size_for_fast_remove() {
        common::ScopedMutexLock lock(_mutex);
        return _options.min_record_size_for_fast_remove;
    }
    uint32_t max_vlet_per_az() {
        common::ScopedMutexLock lock(_mutex);
        uint32_t min_vlet_per_az = _options.n / _options.az_list.size()
            + !!(_options.n % _options.az_list.size());
        if (_options.pick_az_policy != PAZP_MAX_PER_AZ) {
            min_vlet_per_az = _options.n;
        }
        return std::max(_options.max_vlet_per_az, min_vlet_per_az);
    }
    uint64_t create_time() {
        common::ScopedMutexLock lock(_mutex);
        return _options.create_time;
    }
    uint64_t update_time() {
        common::ScopedMutexLock lock(_mutex);
        return _options.update_time;
    }
    void get_az_name_list(std::vector<std::string>* az_list) {
        common::ScopedMutexLock lock(_mutex);
        *az_list = _options.az_list;
    }
    ECType ec_type() {
        common::ScopedMutexLock lock(_mutex);
        return _options.ec_type;
    }
    uint32_t k() {
        common::ScopedMutexLock lock(_mutex);
        return _options.k;
    }
    uint32_t put_quorum() {
        common::ScopedMutexLock lock(_mutex);
        return _options.put_quorum;
    }
    uint32_t n() {
        common::ScopedMutexLock lock(_mutex);
        return _options.n;
    }
    uint32_t reduce_redundancy_running_volume_num() {
        common::ScopedMutexLock lock(_mutex);
        return _options.reduce_redundancy_running_volume_num;
    }
    void set_reduce_redundancy_running_volume_num(uint32_t n) {
        common::ScopedMutexLock lock(_mutex);
        _options.reduce_redundancy_running_volume_num = n;
    }
    void reduce_redundancy_finish() {
        common::ScopedMutexLock lock(_mutex);
        _options.state = SPACE_STATE_NORMAL;
        --_options.n;
    }
    void increase_redundancy_finish() {
        common::ScopedMutexLock lock(_mutex);
        ++_options.n;
    }
    int least_normal_num() {
        common::ScopedMutexLock lock(_mutex);
        return _options.drop_normal_remain;
    }
    uint32_t least_copyed_num() {
        int vlet_n = n();
        common::ScopedMutexLock lock(_mutex);
        // default permit 2 vlet copy by repairer
        uint32_t last_copyed_num = vlet_n > FLAGS_max_copy_failed_vlet_num? vlet_n - FLAGS_max_copy_failed_vlet_num : 0;
        return std::max(_options.drop_normal_remain, last_copyed_num);
    }
    VletType vlet_type() {
        common::ScopedMutexLock lock(_mutex);
        return _options.vlet_type;
    }
    std::set<std::string> disk_type_set() {
        common::ScopedMutexLock lock(_mutex);
        return _options.disk_type_set;
    }
    bool permit_rule_balance() {
        common::ScopedMutexLock lock(_mutex);
        return _options.permit_rule_balance;
    }
    bool permit_data_offset_index() {
        common::ScopedMutexLock lock(_mutex);
        return _options.permit_data_offset_index;
    }
    bool permit_copy_space() {
        common::ScopedMutexLock lock(_mutex);
        return _options.permit_copy_space;
    }
    void set_permit_copy_space(bool permit_copy_space) {
        common::ScopedMutexLock lock(_mutex);
        _options.permit_copy_space = permit_copy_space;
    }
    bool permit_batch_repair() {
        common::ScopedMutexLock lock(_mutex);
        return _options.permit_batch_repair;
    }
    void get_volume_list(VolumeVector* volume_list) {
        common::ScopedMutexLock lock(_mutex);
        for (auto& volume : _volume_map) {
            volume_list->push_back(volume.second); 
        }
    }
    int add_volume(const std::shared_ptr<Volume>& volume);
    int volume_num() {
        common::ScopedMutexLock lock(_mutex);
        return _volume_map.size();
    }
    uint64_t last_volume_id() const {
        return _last_volume_id;
    }
    std::vector<std::pair<std::string, VletType>> disk_vlet_type_set() {
        common::ScopedMutexLock lock(_mutex);
        return _options.disk_vlet_type_map;
    }

    std::map<int, std::string> az_place_policy() {
        common::ScopedMutexLock lock(_mutex);
        return _options.az_place_policy_map;
    }

    std::unordered_map<std::string, int> az_max_vlet_policy_map() {
        common::ScopedMutexLock lock(_mutex);
        return _options.az_max_vlet_policy_map;
    }

    std::unordered_map<int, int> vlet_fast_remove_policy_map() {
        common::ScopedMutexLock lock(_mutex);
        return _options.vlet_fast_remove_policy_map;
    }

    std::map<int, std::string> disk_type_place_policy() {
        common::ScopedMutexLock lock(_mutex);
        return _options.disk_type_place_policy_map;
    }

    void set_permit_compact_balance(bool permit_compact_balance) {
        common::ScopedMutexLock lock(_mutex);
        _options.permit_compact_balance = permit_compact_balance;
    }

    bool permit_compact_balance() {
        common::ScopedMutexLock lock(_mutex);
        return _options.permit_compact_balance;
    }

    void set_compact_state(SpaceCompactState compact_state) {
        common::ScopedMutexLock lock(_mutex);
        _options.compact_state = compact_state;
    }
    
    SpaceCompactState compact_state() {
        common::ScopedMutexLock lock(_mutex);
        return _options.compact_state;
    }
    const aries::pb::CopySpaceOption copy_space_option() {
        common::ScopedMutexLock lock(_mutex);
        return _options.copy_space_option;
    }
    void set_copy_space_option(const aries::pb::CopySpaceOption& option) {
        common::ScopedMutexLock lock(_mutex);
        _options.copy_space_option.CopyFrom(option);
    }
    const aries::pb::ShardCompressOption shard_compress_option() {
        common::ScopedMutexLock lock(_mutex);
        return _options.shard_compress_option;
    }
    void set_shard_compress_option(const aries::pb::ShardCompressOption& option) {
        common::ScopedMutexLock lock(_mutex);
        _options.shard_compress_option.CopyFrom(option);
    }

    bool use_standard_record_layout() {
        common::ScopedMutexLock lock(_mutex);
        return _options.use_standard_record_layout;
    }

    void set_use_standard_record_layout(bool use_standard_record_layout) {
        common::ScopedMutexLock lock(_mutex);
        _options.use_standard_record_layout = use_standard_record_layout;
    }

    void set_append_zone_rewrite_rate(uint32_t append_zone_rewrite_rate) {
        common::ScopedMutexLock lock(_mutex);
        _options.append_zone_rewrite_rate = append_zone_rewrite_rate;
    }

    uint32_t append_zone_rewrite_rate() {
        common::ScopedMutexLock lock(_mutex);
        return _options.append_zone_rewrite_rate;
    }

    void set_daily_rewrite_start_time(uint32_t daily_rewrite_start_time) {
        common::ScopedMutexLock lock(_mutex);
        _options.daily_rewrite_start_time = daily_rewrite_start_time;
    }

    uint32_t daily_rewrite_start_time() {
        common::ScopedMutexLock lock(_mutex);
        return _options.daily_rewrite_start_time;
    }
    
    void set_daily_rewrite_duration_second(uint32_t daily_rewrite_duration_second) {
        common::ScopedMutexLock lock(_mutex);
        _options.daily_rewrite_duration_second = daily_rewrite_duration_second;
    }
    
    uint32_t daily_rewrite_duration_second() {
        common::ScopedMutexLock lock(_mutex);
        return _options.daily_rewrite_duration_second;
    }

    bool archive_volume() {
        common::ScopedMutexLock lock(_mutex);
        return _options.archive_volume;
    }
    void set_archive_volume(bool archive_volume) {
        common::ScopedMutexLock lock(_mutex);
        _options.archive_volume = archive_volume;
    }
    std::string tape_library_group_name() {
        common::ScopedMutexLock lock(_mutex);
        return _options.tape_library_group_name;
    } 

    void set_tape_library_group_name(const std::string& tape_library_group_name) {
        common::ScopedMutexLock lock(_mutex);
        _options.tape_library_group_name = tape_library_group_name;
    }

    uint32_t allocator_collect_quorum() {
        common::ScopedMutexLock lock(_mutex);
        return _options.allocator_collect_quorum;
    }

    void serialize(aries::pb::SpaceInfo* space_info) {
        common::ScopedMutexLock lock(_mutex);
        space_info->set_space_name(_options.space_name);
        space_info->set_group_name(common::set2string(_options.group_set));
        for (auto az_name : _options.az_list) {
            space_info->add_az_name(az_name);
        }
        space_info->set_ec_type(_options.ec_type);
        space_info->set_vlet_type(_options.vlet_type);
        space_info->set_k(_options.k);
        space_info->set_n(_options.n);
        space_info->set_put_quorum(_options.put_quorum);
        space_info->set_delete_quorum(_options.delete_quorum);
        space_info->set_max_vlet_per_az(_options.max_vlet_per_az);
        space_info->set_max_vlet_per_idc(_options.max_vlet_per_idc);
        space_info->set_max_vlet_per_rack(_options.max_vlet_per_rack);
        space_info->set_allocator_collect_quorum(_options.allocator_collect_quorum);
        space_info->set_membership_quorum(_options.membership_quorum);
        space_info->set_drop_normal_remain(_options.drop_normal_remain);
        space_info->set_create_time(_options.create_time);
        space_info->set_update_time(_options.update_time);
        space_info->set_space_id(_space_id);
        space_info->set_repair_finish_quorum(_options.repair_finish_quorum);
        space_info->set_backup_get_shard_num(_options.backup_get_shard_num);
        space_info->set_backup_get_meta_num(_options.backup_get_meta_num);
        space_info->set_disk_type(common::set2string(_options.disk_type_set));

        space_info->set_pick_az_policy(_options.pick_az_policy);
        space_info->set_auto_create_volume_num(_options.auto_create_volume_num);
        space_info->set_least_free_capacity(_options.least_free_capacity);
        space_info->set_reduce_redundancy_running_volume_num(
                _options.reduce_redundancy_running_volume_num);
        space_info->set_state(_options.state);
        space_info->set_permit_rule_balance(_options.permit_rule_balance);
        space_info->set_permit_batch_repair(_options.permit_batch_repair);
        space_info->set_read_only(_options.read_only);
        space_info->set_validator_addr(_options.validator_addr);
        space_info->set_vlet_reserve_free_size(_options.vlet_reserve_free_size);
        space_info->set_max_holes_size_for_fast_remove(_options.max_holes_size_for_fast_remove);
        space_info->set_min_record_size_for_fast_remove(_options.min_record_size_for_fast_remove);
        std::stringstream buffer;
        for (auto&[key, value] : _options.vlet_fast_remove_policy_map) {
            buffer << key << ",";
        }
        std::string vlet_fast_remove_policy_str = buffer.str();
        space_info->set_vlet_fast_remove_policy(vlet_fast_remove_policy_str.substr(0,
                vlet_fast_remove_policy_str.size() - 1));
        space_info->set_permit_compact_balance(_options.permit_compact_balance);
        space_info->set_compact_state(_options.compact_state);
        space_info->set_append_zone_rewrite_rate(_options.append_zone_rewrite_rate);
        space_info->set_daily_rewrite_start_time(_options.daily_rewrite_start_time);
        space_info->set_daily_rewrite_duration_second(_options.daily_rewrite_duration_second);
        space_info->set_permit_copy_space(_options.permit_copy_space);
        space_info->set_permit_data_offset_index(_options.permit_data_offset_index);
        space_info->set_use_standard_record_layout(_options.use_standard_record_layout);
        space_info->set_archive_volume(_options.archive_volume);
        space_info->set_tape_library_group_name(_options.tape_library_group_name);
        space_info->set_need_archived(_options.need_archived);
        space_info->set_archive_mode(_options.archive_mode);
        space_info->set_max_volume_repair_task_num(_options.max_volume_repair_task_num);
        if (_options.delay_delete_interval_second != UINT32_MAX) {
            space_info->set_delay_delete_interval_second(_options.delay_delete_interval_second);
        }
        auto copy_space_option = space_info->mutable_copy_space_option();
        copy_space_option->CopyFrom(_options.copy_space_option);
        auto shard_compress_option  = space_info->mutable_shard_compress_option();
        shard_compress_option->CopyFrom(_options.shard_compress_option);
        for (auto& pair : _options.disk_vlet_type_map) {
            auto disk_vlet = space_info->add_disk_vlet_type();
            disk_vlet->set_disk_type(pair.first);
            disk_vlet->set_vlet_type(pair.second);
        }
        for (auto& pair : _options.az_place_policy_map) {
            auto az_policy = space_info->add_az_place_policy();
            az_policy->set_shard_index(pair.first);
            az_policy->set_az_name(pair.second);
        }
        for (auto& pair : _options.disk_type_place_policy_map) {
            auto disk_policy = space_info->add_disk_type_place_policy();
            disk_policy->set_shard_index(pair.first);
            disk_policy->set_disk_type(pair.second);
        }
        for (auto& pair : _options.az_max_vlet_policy_map) {
            auto az_max_vlet = space_info->add_az_max_vlet_policy();
            az_max_vlet->set_az_name(pair.first);
            az_max_vlet->set_max_vlet(pair.second);
        }
    }
    void serialize(aries::pb::SpaceInfoInDB* space_info) {
        common::ScopedMutexLock lock(_mutex);
        space_info->set_space_name(_options.space_name);
        space_info->set_group_name(common::set2string(_options.group_set));
        std::string azs = "";
        for (auto az_name : _options.az_list) {
            azs += az_name;
            azs += ",";
        }
        if (!azs.empty()) {
            azs.pop_back();
        }
        space_info->set_az_name(azs);
        space_info->set_ec_type(ECType_Name(_options.ec_type));
        space_info->set_vlet_type(VletType_Name(_options.vlet_type));
        space_info->set_k(_options.k);
        space_info->set_n(_options.n);
        space_info->set_put_quorum(_options.put_quorum);
        space_info->set_delete_quorum(_options.delete_quorum);
        space_info->set_max_vlet_per_az(_options.max_vlet_per_az);
        space_info->set_max_vlet_per_idc(_options.max_vlet_per_idc);
        space_info->set_max_vlet_per_rack(_options.max_vlet_per_rack);
        space_info->set_allocator_collect_quorum(_options.allocator_collect_quorum);
        space_info->set_membership_quorum(_options.membership_quorum);
        space_info->set_drop_normal_remain(_options.drop_normal_remain);
        space_info->set_create_time(_options.create_time);
        space_info->set_update_time(_options.update_time);
        space_info->set_space_id(_space_id);
        space_info->set_repair_finish_quorum(_options.repair_finish_quorum);
        space_info->set_backup_get_shard_num(_options.backup_get_shard_num);
        space_info->set_backup_get_meta_num(_options.backup_get_meta_num);
        space_info->set_disk_type(common::set2string(_options.disk_type_set));

        space_info->set_pick_az_policy(PickAZPolicy_Name(_options.pick_az_policy));
        space_info->set_auto_create_volume_num(_options.auto_create_volume_num);
        space_info->set_least_free_capacity(_options.least_free_capacity);
        space_info->set_reduce_redundancy_running_volume_num(
            _options.reduce_redundancy_running_volume_num);
        space_info->set_state(SpaceState_Name(_options.state));
        space_info->set_permit_rule_balance(_options.permit_rule_balance);
        space_info->set_permit_batch_repair(_options.permit_batch_repair);
        space_info->set_read_only(_options.read_only);
        space_info->set_validator_addr(_options.validator_addr);
        space_info->set_vlet_reserve_free_size(_options.vlet_reserve_free_size);
        space_info->set_max_holes_size_for_fast_remove(_options.max_holes_size_for_fast_remove);
        space_info->set_min_record_size_for_fast_remove(_options.min_record_size_for_fast_remove);
        std::stringstream buffer;
        for (auto&[key, value] : _options.vlet_fast_remove_policy_map) {
            buffer << key << ",";
        }
        std::string vlet_fast_remove_policy_str = buffer.str();
        space_info->set_vlet_fast_remove_policy(vlet_fast_remove_policy_str.substr(0,
                vlet_fast_remove_policy_str.size() - 1));
        space_info->set_permit_compact_balance(_options.permit_compact_balance);
        space_info->set_compact_state(SpaceCompactState_Name(_options.compact_state));
        space_info->set_append_zone_rewrite_rate(_options.append_zone_rewrite_rate);
        space_info->set_daily_rewrite_start_time(_options.daily_rewrite_start_time);
        space_info->set_daily_rewrite_duration_second(_options.daily_rewrite_duration_second);
        space_info->set_permit_copy_space(_options.permit_copy_space);
        space_info->set_permit_data_offset_index(_options.permit_data_offset_index);
        space_info->set_copy_space_space_name(_options.copy_space_option.space_name());
        space_info->set_copy_space_space_id(_options.copy_space_option.space_id());
        space_info->set_copy_space_cluster_id(_options.copy_space_option.cluster_id());
        space_info->set_copy_space_master_addr(_options.copy_space_option.master_addr());
        space_info->set_copy_space_token(_options.copy_space_option.token());
        std::string disk_vlet_type_str;
        for (auto& pair : _options.disk_vlet_type_map) {
            disk_vlet_type_str += (pair.first + ":" + VletType_Name(pair.second) + ",");
        }
        if (!disk_vlet_type_str.empty()) {
            disk_vlet_type_str.pop_back();
        }
        space_info->set_disk_vlet_type(disk_vlet_type_str);
        std::string az_place_policy_str;
        for (auto& pair : _options.az_place_policy_map) {
            az_place_policy_str += (std::to_string(pair.first) + ":" + pair.second + ",");
        }
        if (!az_place_policy_str.empty()) {
            az_place_policy_str.pop_back();
        }
        space_info->set_az_place_policy(az_place_policy_str);
        std::string disk_type_place_policy_str;
        for (auto& pair : _options.disk_type_place_policy_map) {
            disk_type_place_policy_str += (std::to_string(pair.first) + ":" + pair.second + ",");
        }
        if (!disk_type_place_policy_str.empty()) {
            disk_type_place_policy_str.pop_back();
        }
        space_info->set_disk_type_place_policy(disk_type_place_policy_str);
        std::string az_max_vlet_policy_str;
        for (auto& pair : _options.az_max_vlet_policy_map) {
            disk_type_place_policy_str += (pair.first + ":" + std::to_string(pair.second) + ",");
        }
        if (!az_max_vlet_policy_str.empty()) {
            az_max_vlet_policy_str.pop_back();
        }
        space_info->set_az_max_vlet_policy(az_max_vlet_policy_str);
        space_info->set_archive_volume(_options.archive_volume);
        space_info->set_tape_library_group_name(_options.tape_library_group_name);
        space_info->set_need_archived(_options.need_archived);
        space_info->set_archive_mode((uint32_t)_options.archive_mode);
        space_info->set_max_volume_repair_task_num(_options.max_volume_repair_task_num);
        if (_options.delay_delete_interval_second != UINT32_MAX) {
            space_info->set_delay_delete_interval_second(_options.delay_delete_interval_second);
        }
    }
    uint64_t total_physical_space();

private:
    bool _is_dropped = false;
    int _space_id = 0;
    uint64_t _last_volume_id = 0;
    VolumeMap _volume_map;
    common::MutexLock _mutex;
    SpaceOptions _options;
};

class SpaceManager : public ManagerBase {
public:
    int add_space(const std::shared_ptr<Space>& space);
    int drop_space(const std::shared_ptr<Space>& space);
    std::shared_ptr<Space> get_space(const std::string& space_name);
    void get_space_list(SpaceVector* space_list) {
        common::ScopedMutexLock lock(_mutex);
        for (auto& space : _space_map) {
            space_list->push_back(space.second);
        }
    }
    void get_space_id_list(std::vector<int>* space_id_list) {
        common::ScopedMutexLock lock(_mutex);
        for (auto& space : _space_map) {
            space_id_list->push_back(space.second->space_id());
        }
    }
    void set_space_id(uint64_t id) {
        _space_id = id;
    }
    uint64_t space_id() const {
        return _space_id;
    }
private:
    SpaceMap _space_map;
    
    int _space_id = 0;
};

struct VolumeOptions {
    std::shared_ptr<Space> space;
    uint64_t volume_id = 0;
    uint64_t create_time = 0;
    uint64_t volume_version = 0;
};

class Volume : public RaftMetaBase {
public:
    Volume(const VolumeOptions& options) :
            _volume_id(options.volume_id),
            _space(options.space),
            _create_time(options.create_time) {
        _vlet_vector.resize(options.space->n());
        _membership = std::shared_ptr<aries::pb::Membership>(new aries::pb::Membership);
        _volume_version = options.volume_version;
        _location_on_tape = nullptr;
        _stats_on_tape = nullptr;
    }

    bool check_set_volume_create_state();

    bool check_finish_volume_compact_progress();

    bool check_finish_volume_copy_progress();
    
    uint64_t create_time() {
        return _create_time;
    }
    uint64_t volume_id() const {
        return _volume_id;
    }
    void set_space(std::shared_ptr<Space> space) {
        common::ScopedMutexLock lock(_mutex);
        _space = space;
    }
    //in use make sure space exist when call spacexx
    std::shared_ptr<Space> space() {
        common::ScopedMutexLock lock(_mutex);
        return _space.lock();
    }
    std::string space_name() {
        common::ScopedMutexLock lock(_mutex);
        return _space.lock()->space_name();
    }

    ECType ec_type() {
        common::ScopedMutexLock lock(_mutex);
        return _space.lock()->ec_type();
    }

    uint32_t k() {
        common::ScopedMutexLock lock(_mutex);
        return _space.lock()->k();
    }
    uint32_t put_quorum() {
        common::ScopedMutexLock lock(_mutex);
        return _space.lock()->put_quorum();
    }
    uint32_t n() {
        common::ScopedMutexLock lock(_mutex);
        return _space.lock()->n();
    }
    uint32_t least_normal_num() {
        common::ScopedMutexLock lock(_mutex);
        return _space.lock()->least_normal_num();
    }
    uint32_t least_copyed_num() {
        common::ScopedMutexLock lock(_mutex);
        return _space.lock()->least_copyed_num();
    }
    bool is_dangerous(const std::shared_ptr<Vlet>& drop_vlet);

    uint32_t volume_version();
    void update_membership();
    void generate_membership();
    bool is_node_exist(const base::EndPoint& addr);

    void serialize(aries::pb::VolumeInfo* volume_info);
    //vlet info vector is output
    void get_vlet_info_list(VletInfoVector* vlet_info_list);
    int add_vlet(const std::shared_ptr<Vlet>& vlet);

    void serialize(aries::pb::VolumeInfoInDB* volume_info) {
        common::ScopedMutexLock lock(_mutex);
        volume_info->set_volume_id(_volume_id);
        volume_info->set_volume_state(VolumeState_Name(_state));
        volume_info->set_space_name(_space.lock()->space_name());
        volume_info->set_volume_version(_volume_version);
        volume_info->set_create_time(_create_time);
        volume_info->set_per_vlet_size(_per_vlet_size);
        volume_info->set_compact_progress(CompactProgress_Name(_compact_progress));
        volume_info->set_compact_vlet_size(_compact_vlet_size);
        volume_info->set_copy_progress(CopyProgress_Name(_copy_progress));
        volume_info->set_permit_data_offset_index(_permit_data_offset_index);
        volume_info->set_raft_index(raft_index());
        volume_info->set_volume_purge_state(VolumePurgeState_Name(_purge_state));
        volume_info->set_last_purge_timestamp(_last_purge_timestamp);
        volume_info->set_is_sealed(_is_sealed);
    }

    std::shared_ptr<Vlet> get_vlet(const int shard_index) {
        common::ScopedMutexLock lock(_mutex);
        return shard_index < (int)_vlet_vector.size() ? _vlet_vector[shard_index] : nullptr;
    }

    void get_vlet_list(VletVector* vlets) {
        common::ScopedMutexLock lock(_mutex);
        *vlets = _vlet_vector;
    }

    std::shared_ptr<aries::pb::Membership> membership() {
        common::ScopedMutexLock lock(_mutex);
        return _membership;
    }
    VolumeState state() {
        return _state;
    }
    void set_state(const VolumeState& state) {
        _state = state;
    }
    void set_unit_az_name(const std::string az_name) {
        common::ScopedMutexLock lock(_mutex);
        _unit_az_name = az_name;
    }
    std::string unit_az_name() {
        common::ScopedMutexLock lock(_mutex);
        return _unit_az_name;
    }
    std::set<std::string> group_set() {
        common::ScopedMutexLock lock(_mutex);
        return _space.lock()->group_set();
    }
    void reduce_redundancy() {
        common::ScopedMutexLock lock(_mutex);
        _vlet_vector.erase(_vlet_vector.end() - 1);
    }
    int increase_redundancy(const std::shared_ptr<Vlet>& vlet);

    int unnormal_vlet_num();

    void set_per_vlet_size(uint64_t vlet_size) {
        common::ScopedMutexLock lock(_mutex);
        _per_vlet_size = vlet_size;
    }

    uint64_t per_vlet_size() {
        common::ScopedMutexLock lock(_mutex);
        return _per_vlet_size;        
    }

    CompactProgress compact_progress() {
        common::ScopedMutexLock lock(_mutex);
        return _compact_progress;
    }

    void set_compact_progress(CompactProgress progress) {
        common::ScopedMutexLock lock(_mutex);
        _compact_progress = progress;
    }

    CopyProgress copy_progress() {
        common::ScopedMutexLock lock(_mutex);
        return _copy_progress;
    }

    void set_copy_progress(CopyProgress progress) {
        common::ScopedMutexLock lock(_mutex);
        _copy_progress = progress;
    }

    uint64_t compact_vlet_size() {
        common::ScopedMutexLock lock(_mutex);
        return _compact_vlet_size;
    }

    void set_compact_vlet_size(uint64_t compact_vlet_size) {
        common::ScopedMutexLock lock(_mutex);
        _compact_vlet_size = compact_vlet_size;
    }

    bool permit_data_offset_index() {
        common::ScopedMutexLock lock(_mutex);
        return _permit_data_offset_index;
    }

    void set_permit_data_offset_index(bool permit) {
        common::ScopedMutexLock lock(_mutex);
        _permit_data_offset_index = permit;
    }

    uint64_t volume_ttl_timestamp() {
        common::ScopedMutexLock lock(_mutex);
        return _volume_ttl_timestamp;
    }

    void set_volume_ttl_timestamp(uint64_t timestamp) {
        common::ScopedMutexLock lock(_mutex);
        _volume_ttl_timestamp = timestamp;
    }

    uint64_t append_logical_size() {
        common::ScopedMutexLock lock(_mutex);
        return _append_logical_size;
    }

    void set_append_logical_size(uint64_t size) {
        common::ScopedMutexLock lock(_mutex);
        _append_logical_size = size;
    }

    void set_location_on_tape(const aries::pb::VolumeLocationOnTape& location_on_tape) {
        common::ScopedMutexLock lock(_mutex);
        _location_on_tape = std::make_shared<aries::pb::VolumeLocationOnTape> ();
        _location_on_tape->CopyFrom(location_on_tape);
    }

    std::shared_ptr<aries::pb::VolumeLocationOnTape> location_on_tape() {
        common::ScopedMutexLock lock(_mutex);
        return _location_on_tape;
    }

    void set_stats_on_tape(const aries::pb::VolumeStatsOnTape& stats_on_tape) {
        common::ScopedMutexLock lock(_mutex);
        _stats_on_tape = std::make_shared<aries::pb::VolumeStatsOnTape> ();
        _stats_on_tape->CopyFrom(stats_on_tape);
    }

    std::shared_ptr<aries::pb::VolumeStatsOnTape> stats_on_tape() {
        common::ScopedMutexLock lock(_mutex);
        return _stats_on_tape;
    }

    void set_purge_state(VolumePurgeState state) {
        common::ScopedMutexLock lock(_mutex);
        _purge_state = state;
    }

    VolumePurgeState purge_state() {
        common::ScopedMutexLock lock(_mutex);
        return _purge_state;
    }

    bool need_purge() {
        common::ScopedMutexLock lock(_mutex);
        return _purge_state == VOLUME_PURGE_STATE_WAITING;
    }

    void set_last_purge_timestamp(uint64_t timestamp) {
        common::ScopedMutexLock lock(_mutex);
        _last_purge_timestamp = timestamp;
    }

    uint64_t last_purge_timestamp() {
        common::ScopedMutexLock lock(_mutex);
        return _last_purge_timestamp;
    }

    void set_is_sealed(bool is_sealed) {
        common::ScopedMutexLock lock(_mutex);
        _is_sealed = is_sealed;
    }

    uint64_t is_sealed() {
        common::ScopedMutexLock lock(_mutex);
        return _is_sealed;
    }

private:
    uint64_t _volume_id = 0;
    VolumeState _state = VOLUME_STATE_CREATING;
    std::weak_ptr<Space> _space;
    VletVector _vlet_vector;
    uint64_t _create_time = 0;
    common::MutexLock _mutex;
    uint64_t _volume_version = 0;
    std::shared_ptr<aries::pb::Membership> _membership;
    std::string _unit_az_name;//just for pick az policy PAZP_UNIT_IN_AZ

    uint64_t _per_vlet_size;
    uint64_t _compact_vlet_size;
    CompactProgress _compact_progress  = NONE;
    CopyProgress _copy_progress  = COPY_NONE;
    bool _permit_data_offset_index = false;

    uint64_t _volume_ttl_timestamp = 0;
    uint64_t _append_logical_size = 0;

    std::shared_ptr<aries::pb::VolumeLocationOnTape> _location_on_tape;
    std::shared_ptr<aries::pb::VolumeStatsOnTape> _stats_on_tape;
    
    VolumePurgeState _purge_state = VOLUME_PURGE_STATE_NONE;
    uint64_t _last_purge_timestamp = 0;
    bool _is_sealed = false;
};

struct VletOptions {
    std::shared_ptr<Volume> volume;
    int shard_index = -1;
    uint32_t vlet_version = 0;
    VletState state = VLET_STATE_CREATING;
    uint64_t create_time = 0;
    uint64_t timestamp = 0;
    std::shared_ptr<Disk> disk_ptr;
    bool check_blob_ttl = false;
};

class Vlet {
public:
    Vlet(const VletOptions& options) :
        _volume(options.volume),
        _shard_index(options.shard_index),
        _vlet_version(options.vlet_version),
        _state(options.state),
        _create_time(options.create_time),
        _timestamp(options.timestamp),
        _disk_ptr(options.disk_ptr),
        _check_blob_ttl(options.check_blob_ttl) {
        _vlet_type = VLET_TYPE_ERROR;
        _vlet_engine_info_ptr = std::shared_ptr<aries::pb::VletEngineInfo>(new aries::pb::VletEngineInfo);
        _vlet_engine_info_ptr->set_vlet_size(0);
        _vlet_engine_info_ptr->set_block_size(0);
        _vlet_engine_info_ptr->set_max_record_size(0);
        _vlet_engine_info_ptr->set_min_record_size(0);
        _vlet_engine_info_ptr->set_record_gap_page_num(0);
        _vlet_engine_info_ptr->set_smr_zone_size(0);
        _vlet_engine_info_ptr->set_align_size(0);
    }

    int shard_index() const {
        return _shard_index;
    }
    std::shared_ptr<Volume> volume() {
        return _volume.lock();
    }

    std::shared_ptr<Space> space() {
        return this->volume()->space();
    }

    uint64_t create_time() const {
        return _create_time;
    }
    void set_create_time(const uint64_t create_time) {
        _create_time = create_time;
    }
    uint64_t volume_id() {
        return this->volume()->volume_id();
    }
    std::string space_name() {
        return this->volume()->space_name();
    }
    VletState state() {
        common::ScopedMutexLock lock(_mutex);
        return _state;
    }
    void set_state(const VletState state) {
        common::ScopedMutexLock lock(_mutex);
        _state = state;
    }
    void set_timestamp(const uint64_t timestamp) {
        _timestamp = timestamp;
    }
    uint64_t timestamp() const {
        return _timestamp;
    }
    void set_vlet_version(const uint32_t version) {
        common::ScopedMutexLock lock(_mutex);
        _vlet_version = version;
    }
    uint32_t vlet_version() const {
        return _vlet_version;
    }
    void set_disk_ptr(const std::shared_ptr<Disk> disk) {
        common::ScopedMutexLock lock(_mutex);
        _disk_ptr = disk;
    }

    bool set_tmp_disk_if(const std::shared_ptr<Disk> disk, const uint32_t version) {
        common::ScopedMutexLock lock(_mutex);
        if (_vlet_version == version) {
            _tmp_disk_ptr = disk;
            return true;
        }
        return false;
    }
    std::shared_ptr<Disk> tmp_disk() {
        common::ScopedMutexLock lock(_mutex);
        return _tmp_disk_ptr.lock();
    }

    std::shared_ptr<Node> tmp_node() {
        auto disk = this->tmp_disk();
        return disk ? disk->node() : nullptr;
    }

    std::shared_ptr<Disk> disk() {
        common::ScopedMutexLock lock(_mutex);
        return _disk_ptr.lock();
    }

    std::shared_ptr<Node> node() {
        auto disk = this->disk();
        return disk ? disk->node() : nullptr;
    }

    //todo if disk ptr exist.
    int disk_id() {
        auto disk = this->disk();
        return disk ? disk->disk_id() : -1;
    }

    base::EndPoint addr() {
        auto disk = this->disk();
        return disk ? disk->addr() : base::EndPoint();
    }

    std::string rack_name() {
        auto disk = this->disk();
        if (disk) {
            auto node = disk->node();
            if (node) {
                return node->rack_name();
            }
        }
        return "";
    }

    std::string idc_name() {
        auto disk = this->disk();
        if (disk) {
            auto node = disk->node();
            if (node) {
                return node->idc_name();
            }
        }
        return "";
    }
    std::string az_name() {
        auto disk = this->disk();
        if (disk) {
            auto node = disk->node();
            if (node) {
                return node->az_name();
            }
        }
        return "";
    }
    std::string group_name() {
        auto disk = this->disk();
        if (disk) {
            auto node = disk->node();
            if (node) {
                return node->group_name();
            }
        }
        return "";
    }
    std::string disk_type_name() {
        auto disk = this->disk();
        if (disk) {
            return disk->disk_type();
        }
        return "";
    }
    
    void serialize(aries::pb::VletInfo* vlet_info) {
        vlet_info->set_volume_id(volume_id());
        vlet_info->set_shard_index(_shard_index);
        vlet_info->set_vlet_version(_vlet_version);
        if (is_vlet_state_not_exist(_state)) {
            vlet_info->set_node_addr(common::endpoint2int(base::EndPoint()));
            vlet_info->set_disk_id(-1);
        } else {
            vlet_info->set_node_addr(common::endpoint2int(this->addr()));
            vlet_info->set_disk_id(this->disk_id());
            vlet_info->set_vlet_type(_vlet_type);
            vlet_info->mutable_vlet_engine_options()->CopyFrom(*_vlet_engine_info_ptr);
        }
        vlet_info->set_create_time(_create_time);
        vlet_info->set_state(_state);
        vlet_info->set_update_time(_timestamp);
        vlet_info->set_check_blob_ttl(_check_blob_ttl);
        auto space_info = this->space();
        if (space_info != nullptr) {
            auto vlet_fast_remove_policy_map = space_info->vlet_fast_remove_policy_map();
            if (vlet_fast_remove_policy_map.find(_shard_index) != vlet_fast_remove_policy_map.end()) {
                vlet_info->set_permit_fast_remove(true);
            }
        }
    }
    void serialize(aries::pb::VletInfoInDB* vlet_info) {
        common::ScopedMutexLock lock(_mutex);
        vlet_info->set_volume_id(volume()->volume_id());
        vlet_info->set_shard_index(_shard_index);
        vlet_info->set_vlet_version(_vlet_version);
        if (is_vlet_state_not_exist(_state)) {
            vlet_info->set_node_addr(common::endpoint2str(base::EndPoint()));
            vlet_info->set_disk_id(-1);
        } else {
            auto disk = _disk_ptr.lock();
            vlet_info->set_node_addr(common::endpoint2str(disk ? disk->addr() : base::EndPoint()));
            vlet_info->set_disk_id(disk ? disk->disk_id() : -1);
            vlet_info->set_vlet_type(VletType_Name(_vlet_type));
        }
        vlet_info->set_create_time(_create_time);
        vlet_info->set_state(VletState_Name(_state));
        vlet_info->set_update_time(_timestamp);
        vlet_info->set_check_blob_ttl(_check_blob_ttl);
    }

    bool update_check_time(uint32_t version, uint64_t check_time) {
        common::ScopedMutexLock lock(_mutex);
        if (_vlet_version == version) {
            _last_check_time = check_time;
            return true;
        }
        return false;
    }
    
    uint64_t last_check_time() const {
        return _last_check_time;
    }
    
    bool update_last_normal_time(uint64_t normal_time) {
        _last_normal_time = normal_time;
        return true;
    }
    
    uint64_t last_normal_time() const {
        return _last_normal_time;
    }
    
    bool update_last_unnormal_time(uint64_t unnormal_time) {
        if (_last_unnormal_time == 0 || unnormal_time == 0) {
            _last_unnormal_time = unnormal_time;
        }
        return true;
    }
    
    uint64_t last_unnormal_time() const {
        return _last_unnormal_time;
    }
    
    void set_drop_reason(common::DropVletReason drop_reason) {
        common::ScopedMutexLock lock(_mutex);
        _drop_reason = drop_reason;
    }
    
    common::DropVletReason drop_reason() {
        common::ScopedMutexLock lock(_mutex);
        return _drop_reason;
    }

    void set_vlet_type(VletType vlet_type) {
        common::ScopedMutexLock lock(_mutex);
        _vlet_type = vlet_type;
    }

    VletType vlet_type() {
        common::ScopedMutexLock lock(_mutex);
        return _vlet_type;
    }

    std::shared_ptr<aries::pb::VletEngineInfo> vlet_engine_info_ptr() {
        common::ScopedMutexLock lock(_mutex);
        return _vlet_engine_info_ptr;
    }

    void set_check_blob_ttl(bool check_blob_ttl) {
        common::ScopedMutexLock lock(_mutex);
        _check_blob_ttl = check_blob_ttl;
    }

    bool check_blob_ttl() {
        common::ScopedMutexLock lock(_mutex);
        return _check_blob_ttl;
    }

    void set_vlet_engine_info(aries::pb::VletEngineInfo& vlet_engine_option) {
        common::ScopedMutexLock lock(_mutex);
        _vlet_engine_info_ptr->set_vlet_size(vlet_engine_option.vlet_size());
        auto engine_type = common::vlet_type_by_vlet(_vlet_type);
        if (engine_type == ENGINE_LINKED) {
            _vlet_engine_info_ptr->set_block_size(vlet_engine_option.block_size());
            _vlet_engine_info_ptr->set_max_record_size(vlet_engine_option.max_record_size());
            _vlet_engine_info_ptr->set_min_record_size(vlet_engine_option.min_record_size());
            _vlet_engine_info_ptr->set_record_gap_page_num(vlet_engine_option.record_gap_page_num());
        } else if (engine_type == ENGINE_APPEND){
            _vlet_engine_info_ptr->set_smr_zone_size(vlet_engine_option.smr_zone_size());
            _vlet_engine_info_ptr->set_align_size(vlet_engine_option.align_size());
        } else if (engine_type == ENGINE_ZONE){
            _vlet_engine_info_ptr->set_align_size(vlet_engine_option.align_size());
        } else {
            LOG(WARNING) << "Error engine type prased by vlet type.";
        }
    }

private:
    std::weak_ptr<Volume> _volume;
    int _shard_index = -1;
    uint32_t _vlet_version = 0;
    VletState _state;
    uint64_t _create_time = 0;
    uint64_t _timestamp = 0;
    std::weak_ptr<Disk> _disk_ptr;
    std::weak_ptr<Disk> _tmp_disk_ptr; //for creating & recovering vlet
    common::MutexLock _mutex;
    uint64_t _last_check_time = 0;
    uint64_t _last_normal_time = base::gettimeofday_s();
    uint64_t _last_unnormal_time = 0;
    common::DropVletReason _drop_reason = common::REASON_OK;
    VletType _vlet_type;
    std::shared_ptr<aries::pb::VletEngineInfo> _vlet_engine_info_ptr;
    bool _check_blob_ttl = false;
};

class VolumeManager : public ManagerBase {
public:
    VolumeManager() : _max_volume_id(0) {}
    ~VolumeManager() {}

    uint64_t get_max_volume_id() const {
        return _max_volume_id;
    }
    void set_max_volume_id(uint64_t id) {
        _max_volume_id = id;
    }
    uint32_t get_volume_map_size() const {
        return _volume_map.size();
    }

    std::shared_ptr<Volume> get_volume(const uint64_t volume_id);
    int add_volume(const std::shared_ptr<Volume>& volume);
    int drop_volume(const uint64_t volume_id) {
        common::ScopedMutexLock lock(_mutex);
        return _volume_map.erase(volume_id) ? 0 : -1;
    }

    void get_volume_list(VolumeVector* volume_list) {
        common::ScopedMutexLock lock(_mutex);
        for (auto& volume : _volume_map) {
            volume_list->push_back(volume.second);
        }
    }

private:
    uint64_t _max_volume_id = 0;
    VolumeMap _volume_map;
};

class MetaBackupKeysManager : public ManagerBase {
public:
    MetaBackupKeysManager() : _encrypt_key("0") {}
    ~MetaBackupKeysManager() {}

    void set_keys(const std::string& access_key, const std::string& secret_key, const std::string& encrypt_key = "") {
        common::ScopedMutexLock lock(_mutex);
        if (!encrypt_key.empty()) {
            _encrypt_key = encrypt_key;
        }
        _access_key = access_key;
        _secret_key = secret_key;
    }

    void get_keys(std::string* access_key, std::string* secret_key, std::string* decrypt_key = nullptr) {
        common::ScopedMutexLock lock(_mutex);
        if (decrypt_key) {
            *decrypt_key = _encrypt_key;
        }
        *access_key = _access_key;
        *secret_key = _secret_key;
    }
    
private:
    std::string _encrypt_key;
    std::string _access_key;
    std::string _secret_key;
};

// MetaData
class MetaData {
public:
    MetaData() : _state(MASTER_STATE_FOLLOWER) {
        _az_manager = new AZManager;
        _node_manager = new NodeManager;
        _space_manager = new SpaceManager;
        _volume_manager = new VolumeManager;
        _volume_service_manager = new VolumeServiceManager;
        _tinker_manager = new TinkerManager;
        _repairer_manager = new RepairerManager;
        _checkcenter_manager = new CheckcenterManager;
        _monitorcenter_manager = new MonitorcenterManager;
        _dataagent_manager = new DataAgentManager;
        _allocator_manager = new AllocatorManager;
        _tape_center_manager = new TapeCenterManager;
        _tape_node_manager = new TapeNodeManager;
        _stateservice_manager = new StateServiceManager;
        _tape_library_manager = new TapeLibraryGroupManager;
        _tape_pool_manager = new TapeLogicalPoolManager;
        _meta_backup_keys_manager = new MetaBackupKeysManager;
        _start_time = ::base::gettimeofday_us();
    }
    ~MetaData() {
        delete _az_manager;
        delete _node_manager;
        delete _space_manager;
        delete _volume_manager;
        delete _volume_service_manager;
        delete _tinker_manager;
        delete _repairer_manager;
        delete _checkcenter_manager;
        delete _monitorcenter_manager;
        delete _dataagent_manager;
        delete _allocator_manager;
        delete _tape_center_manager;
        delete _tape_node_manager;
        delete _stateservice_manager;
        delete _tape_library_manager;
        delete _tape_pool_manager;
    }

    uint64_t start_time() const {
        return _start_time;
    }

    int add_node(const std::shared_ptr<Node>& node_ptr);
    int drop_node(const std::shared_ptr<Node>& node_ptr);
    int add_space(const std::shared_ptr<Space>& space);
    int add_volume(const std::shared_ptr<Volume>& volume);
    int update_node(const std::shared_ptr<Node>& node_ptr,
        const NodeOptions& update_node_option);

    bool is_leader() const {
        return (_state == MASTER_STATE_LEADER_WORKING || 
                _state == MASTER_STATE_LEADER_STARTING);
    }

    bool is_working_leader() const {
        return _state == MASTER_STATE_LEADER_WORKING;
    }

    void set_state(const MasterState state) {
        _state = state;
    }

    MasterState state() const {
        return _state;
    }

    AZManager* az_manager() {
        return _az_manager;
    }

    SpaceManager* space_manager() {
        return _space_manager;
    }

    VolumeManager* volume_manager() {
        return _volume_manager;
    }

    VolumeServiceManager* volume_service_manager() {
        return _volume_service_manager;
    }

    TinkerManager* tinker_manager() {
        return _tinker_manager;
    }
    RepairerManager* repairer_manager() {
        return _repairer_manager;
    }
    CheckcenterManager* checkcenter_manager() {
        return _checkcenter_manager;
    }
    MonitorcenterManager* monitorcenter_manager() {
        return _monitorcenter_manager;
    }
    NodeManager* node_manager() {
        return _node_manager;
    }
    DataAgentManager* dataagent_manager() {
        return _dataagent_manager;
    }
    AllocatorManager* allocator_manager() {
        return _allocator_manager;
    }
    TapeCenterManager* tape_center_manager() {
        return _tape_center_manager;
    }
    TapeNodeManager* tape_node_manager() {
        return _tape_node_manager;
    }
    StateServiceManager* stateservice_manager() {
        return _stateservice_manager;
    }
    TapeLibraryGroupManager* library_manager() {
        return _tape_library_manager;
    }
    TapeLogicalPoolManager* pool_manager() {
        return _tape_pool_manager;
    }
    MetaBackupKeysManager* meta_backup_keys_manager() {
        return _meta_backup_keys_manager;
    }
    uint64_t raft_index() const {
        return _raft_index;
    }
    void set_raft_index(const uint64_t index) {
        _raft_index = index;
    }
    void set_last_snapshot_timestamp(uint64_t time) {
        _last_snapshot_timestamp = time;
    }
    uint64_t last_snapshot_timestamp() const {
        return _last_snapshot_timestamp;
    }
    void update_primary_start_time() {
        _primary_start_time = ::base::gettimeofday_s();
    }
    uint64_t primary_start_time() const {
        return _primary_start_time;
    }
    void set_cluster(const std::string& cluster_name, const uint64_t cluster_id) {
        _cluster_id = cluster_id;
        _cluster_name = cluster_name;
    }
    uint64_t cluster_id() const {
        return _cluster_id;
    }
    std::string cluster_name() const {
        return _cluster_name;
    }

private:
    AZManager* _az_manager;
    NodeManager* _node_manager;
    SpaceManager* _space_manager;
    VolumeManager* _volume_manager;
    VolumeServiceManager* _volume_service_manager;
    TinkerManager* _tinker_manager;
    RepairerManager* _repairer_manager;
    CheckcenterManager* _checkcenter_manager;
    MonitorcenterManager* _monitorcenter_manager;
    DataAgentManager* _dataagent_manager;
    AllocatorManager* _allocator_manager;
    TapeCenterManager* _tape_center_manager;
    TapeNodeManager* _tape_node_manager;
    StateServiceManager* _stateservice_manager;
    TapeLibraryGroupManager* _tape_library_manager;
    TapeLogicalPoolManager* _tape_pool_manager;
    MetaBackupKeysManager* _meta_backup_keys_manager;
    common::MutexLock _mutex;
    MasterState _state = MASTER_STATE_FOLLOWER;
    uint64_t _raft_index = 0;
    uint64_t _last_snapshot_timestamp = 0;
    uint64_t _start_time = 0;
    uint64_t _primary_start_time = 0;
    uint64_t _cluster_id = 0;
    std::string _cluster_name;
};

extern MetaData* g_meta_data;
#define g_az_manager g_meta_data->az_manager()
#define g_space_manager g_meta_data->space_manager()
#define g_volume_manager g_meta_data->volume_manager()
#define g_volume_service_manager g_meta_data->volume_service_manager()
#define g_tinker_manager g_meta_data->tinker_manager()
#define g_repairer_manager g_meta_data->repairer_manager()
#define g_checkcenter_manager g_meta_data->checkcenter_manager()
#define g_monitorcenter_manager g_meta_data->monitorcenter_manager()
#define g_node_manager g_meta_data->node_manager()
#define g_dataagent_manager g_meta_data->dataagent_manager()
#define g_allocator_manager g_meta_data->allocator_manager()
#define g_tape_center_manager g_meta_data->tape_center_manager()
#define g_tape_node_manager g_meta_data->tape_node_manager()
#define g_stateservice_manager g_meta_data->stateservice_manager()
#define g_tape_library_manager g_meta_data->library_manager()
#define g_tape_pool_manager g_meta_data->pool_manager()
#define g_meta_backup_keys_manager g_meta_data->meta_backup_keys_manager()
}
}

#endif //
