//=============================================================================
// Author: <EMAIL>
// Data: 2016-10-20 17:34
// Filename: conf.cpp
// Description:
//=============================================================================

#include <gflags/gflags.h>
#include <base/file_util.h>                 // base::FilePath
#include "baidu/inf/aries/master/conf.h"

namespace aries {
namespace master {

//DEFINE_string(peers, "", "cluster peer set");
DEFINE_string(raft_dir, "local://./", "raft data dir");

DEFINE_string(token, "default_token", "token");

DEFINE_string(admin_token, "default_token", "admin_token");

DEFINE_bool(raft_learner, false, "is raft learner");

DEFINE_int32(pick_disk_level, 3, "pick disk level, 1 is random");
DEFINE_int32(pick_disk_type_policy, 0, "pick disk mode, 0 is pick in space rule type, 1 is pick in same vlet disk type, 2 is pick in space disk type");
DEFINE_int32(pick_node_once_num, 3, "pick node num once");

DEFINE_int32(max_running_control_num, 3, "max runing report request num");
DEFINE_int32(raft_election_timeout_ms, 60000, "raft election time out(ms)");
DEFINE_int32(tinker_scheduler_interval_second, 3600, "Repair scheduler task interval in second");
DEFINE_uint64(vlet_check_interval_least, 3 * 24 * 3600, "vlet tinker check interval least in second");
DEFINE_uint64(vlet_repair_interval_least, 7 * 24 * 3600, "vlet from not normal to normal state interval least in second");
DEFINE_uint64(max_volume_can_create_per_time, 100000, "max volume create per time");
DEFINE_uint64(auto_create_volume_interval_second, 300, "auto create volume interval second");

DEFINE_int32(tinker_dead_threshold_in_second, 180, "Tinker dead threshold in second");
DEFINE_int32(tinker_drop_threshold_in_second, 3600, "drop tinker in second");
DEFINE_int32(repairer_dead_threshold_in_second, 300, "Repairer dead threshold in second");
DEFINE_int32(checkcenter_dead_threshold_in_second, 300, "Checkcenter dead threshold in second");
DEFINE_int32(monitorcenter_dead_threshold_in_second, 300, "Monitorcenter dead threshold in second");
DEFINE_int32(volume_service_dead_threshold_in_second, 300, "VolumeService dead threshold in second");
DEFINE_int32(datanode_dead_threshold_in_second, 180, "DataNode dead threshold in second");
DEFINE_int32(dataagent_dead_threshold_in_second, 300, "DataAgent dead threshold in second");
DEFINE_int32(allocator_dead_threshold_in_second, 180, "Allocator dead threshold in second");
DEFINE_uint64(tape_center_dead_threshold_in_second, 180, "TapeCenter dead threshold in second");
DEFINE_uint64(tape_node_dead_threshold_in_second, 180, "TapeNode dead threshold in second");
DEFINE_uint64(stateservice_dead_threshold_in_second, 180, "StateService dead threshold in second");
DEFINE_uint64(deadnode_auto_drop_in_second, 3 * 24 * 3600, "A dead datanode auto drop time interval in second");
DEFINE_uint64(offline_datanode_check_time_in_second, 3 * 60, " time interval to check offline node need to drop in second");
DEFINE_uint64(unbalanced_datanode_check_time_in_second, 10 * 60, " time interval to check unbalanced node could be normal in second");
DEFINE_uint64(snapshot_interval_s, 7 * 24 * 60 * 60, "time interval to snapshot interval in second");

DEFINE_uint64(high_density_node_capacity_threshold_in_tb, 1024, "high density node capacity threshold, default 1PB");
DEFINE_uint64(high_density_deadnode_auto_drop_in_second, 7 * 24 * 3600, "A dead datanode auto drop time interval for density machine, default one week");

DEFINE_uint64(safemode_datanode_check_time_in_second, 3 * 60, " time interval to check offline node need to drop in second");
DEFINE_uint64(auto_leave_safemode_node_in_second, 3 * 24 * 3600, "A safemode datanode auto leave time interval in second");

DEFINE_double(disk_reserve_percent, 1.0, "disk reserve percent for db and log, default is 1.0%");
DEFINE_string(max_disk_usage, "0.99", "reloadable, max disk usage for recover, set this value per az by {default_value},{az1:value},{az2:value}");
DEFINE_string(max_disk_usage_for_create, "0.95", "reloadable, max disk usage for create, set this value per az by {default_value},{az1:value},{az2:value}");

DEFINE_int32(max_task_per_az, 100, "Max tasks per az in one round");
DEFINE_int32(max_creating_vlet_rpc_num, 10000, "Max num of creating vlet rpc");

DEFINE_uint64(default_task_retry_delay_in_milli, 2 * 1000, "Default task retry delay in second");
DEFINE_uint64(default_task_max_retry_delay_in_milli, 3 * 60 * 1000, "Default task retry delay in second");
DEFINE_uint64(default_task_alarm_threshold_in_milli, 2 * 60 * 60 * 1000, "Default task alarming threshold in second");

DEFINE_uint64(membership_converger_worker_thread_num, 1, "Number of threads for updating membership");
DEFINE_uint64(membership_converger_worker_max_task_in_flight, 10000, "Max number of updating membership tasks in flight");

DECLARE_uint64(membership_converger_worker_max_task_in_flight);

DEFINE_uint64(membership_converger_jitter_percentage, 20, "default jitter percentage for membership update task");

DEFINE_uint64(master_monitor_sleep_interval_ms, 2 * 60 * 1000, "Master monitor sleep interval in millisecond");
DEFINE_uint64(master_monitor_volume_critical_delta, 2, "The delta value when judging volume as critical");
DEFINE_bool(master_monitor_enable_monitor, true, "Enable master monitor");
DEFINE_bool(master_monitor_enable_volumes_collection, true, "Enable master monitor volumes collection");
DEFINE_bool(master_monitor_enable_instance_collection, true, "Enable master monitor instance collection");
DEFINE_bool(master_monitor_enable_capacity_collection, true, "Enable master monitor capacity collection");
DEFINE_bool(master_monitor_enable_free_space_collection, true, "Enable master monitor free space collection");
DEFINE_bool(master_monitor_enable_burn_rate_collection, true, "Enable master monitor burn rate collection");

DEFINE_uint64(drop_disk_token_alloc_time_second, 3600, "alloc a drop disk token per second");
DEFINE_uint64(drop_node_token_alloc_time_second, 12 * 3600, "alloc a drop node token per second");
DEFINE_uint64(drop_vlet_token_alloc_time_second, 60, "alloc a drop vlet token per second");
DEFINE_uint64(gc_vlet_token_alloc_time_second, 600, "alloc a gc vlet token per second");
DEFINE_uint64(gc_disk_token_alloc_time_second, 3600, "alloc a gc disk token per second");
DEFINE_uint64(single_datanode_drop_disk_token_alloc_time_second, 12 * 3600, "alloc a drop disk token for single datanode per second");

DEFINE_uint64(max_drop_disk_token, 18, "max drop disk token same time");
DEFINE_uint64(max_drop_node_token, 4, "max drop node token same time");
DEFINE_uint64(max_drop_vlet_token, 100, "max drop disk token same time");
DEFINE_uint64(max_gc_vlet_token, 7000, "max gc vlet token same time");
DEFINE_uint64(gc_vlet_token_step, 1, "alloc gc vlet token once");
DEFINE_uint64(max_gc_disk_token, 20, "max gc disk token same time");
DEFINE_uint64(max_single_datanode_drop_disk_token, 30, "max drop disk in single datanode token same time");

DEFINE_uint64(do_balance_interval_second, 180, "do balance interval second");
DEFINE_uint64(check_balance_interval_second, 30, "check balance interval second");
DEFINE_uint64(check_balance_interval_times, 10, "check balance when do balance run n times.");
DEFINE_bool(enable_rule_balance, true, "Enable do rule balance");
DEFINE_bool(enable_decommission_balance, true, "Enable do decommission balance");
DEFINE_bool(enable_drop_decommissioned_node, true, "Enable do drop node after decommission_balance finished");
DEFINE_bool(enable_manual_balance, true, "Enable do manual balance");
DEFINE_bool(enable_az_usage_balance, true, "Enable do az usage balance");
DEFINE_bool(enable_usage_balance, true, "Enable do usage balance");
DEFINE_bool(enable_compact_balance, true, "Enable do compact balance");
DEFINE_bool(enable_copy_space, false, "Enable do copy space");
DEFINE_double(balance_min_az_usage_diff_percent, 2, "min az disk usage diff percent from average");
DEFINE_double(balance_min_disk_usage_overflow_diff, 0.02, "min disk usage overflow diff from average");
DEFINE_double(balance_min_disk_usage_lack_diff, 0.05, "min disk usage lack diff from average");
DEFINE_uint64(max_running_balance_task_per_src_disk, 1, "max running balance task in one src disk");
DEFINE_uint64(max_running_balance_task_per_dest_disk, 1, "max running balance task in one dest disk");
DEFINE_uint64(max_running_rule_balance_task_num, 50000, "max running rule balance task in one cluster");
DEFINE_uint64(max_running_usage_balance_task_num, 20000, "max running usage balance task one cluster");
DEFINE_uint64(max_running_az_usage_balance_task_num, 10000, "max running az usage balance task one cluster");
DEFINE_uint64(max_waiting_az_usage_balance_task_num, 20000, "max running compact balance task one cluster");
DEFINE_uint64(max_running_decommission_balance_task_per_disk, 2, "max running decommission balance task in one disk");
DEFINE_uint64(max_running_balance_task_per_src_node, 9, "max running balance task in one src node");
DEFINE_uint64(max_running_balance_task_per_dest_node, 18, "max running balance task in one dest node");
DEFINE_uint64(max_running_decommission_balance_task_per_node, 18, "max running decommission balance task in one node");
DEFINE_uint64(max_running_balance_task_per_dest_unbalanced_disk, 3, "max running balance task in one unbalanced dest disk");
DEFINE_uint64(max_running_balance_task_per_dest_unbalanced_node, 54, "max running balance task in one unbalanced dest node");
DEFINE_uint64(balance_vlet_timeout_second, 14400, "balance vlet timeout");
DEFINE_uint64(balance_volume_timeout_second, 14400 * 10, "balance volume timeout");
DEFINE_uint64(balance_force_update_finish_tasks_interval_s, 30 * 60, "force update finish tasks interval");
DEFINE_uint64(max_cluster_copy_volume_task_size, 100, "max across cluster copy volume task num");
DEFINE_uint64(max_cluster_copy_vlet_task_size, 1000, "max across cluster copy vlet task num");
DEFINE_uint64(max_send_copy_progress_num, 1000, "cross cluster copy max send copy_progress num one loop by dest cluster");
DEFINE_int32(max_copy_failed_vlet_num, 1, "across cluster copy  maximum number of copy not finish vlet allowed on one volume");
DEFINE_uint64(max_balance_waitting_task_size, 10000, "max balance waitting task size");
DEFINE_uint64(max_balance_disk_retry_times, 3, "max balance disk retry times");
DEFINE_int32(max_retry_time_pick_rule_balance, 10, "max rule balance pick disk retry times");
DEFINE_int32(az_light_safemode_threshold, 10, "az enter/leave light safemode threshold, less than az_safemode_threshold");
DEFINE_int32(az_safemode_threshold, 40, "az enter/leave safemode threshold");
DEFINE_int32(max_balance_volume_unnormal_vlet_num, 0, "max balance volume permit un normal vlet num=0");
DEFINE_int32(max_retry_time_pick_dest_disk_on_compact_balance, 3, "max compact balance pick disk retry times");
DEFINE_int32(balance_random_pick_disk_max_retry_time, 5, "max balance pick disk retry times");
DEFINE_uint64(balance_random_pick_disk_num_limit, 1000, "max disk num for balance random pick");
DEFINE_uint64(compact_gap_size, 2, "compact vlet by this gap, GB");
DEFINE_uint64(compact_gap_buffer_size, 300, "reserve a buffer size when compact one volume, MB");
DEFINE_uint64(max_waitting_task_size_compact_balance, 10, "the max volume counts in waiting task");
DEFINE_uint64(max_running_task_size_compact_balance, 5, "the max volume counts in running task");
DEFINE_uint64(compact_one_volume_elapsed_time_s, 86400, "below this elapsed time, the volume cannot be compacted, s");
DEFINE_uint64(check_new_volume_interval_time_s, 604800, "below this interval time, the volume cannot be check, s");

DEFINE_bool(enable_unbalanced_node_state, true, "enable unbalanced node state");
DEFINE_double(avg_disk_usage_diff_ratio_node_become_balanced, 0.7, "if unbalanced node disk_avg_usage>az_avg_disk_usage*thisvalue, it become normal");

DEFINE_bool(enable_drop_space, false, "Enable do drop space");

DEFINE_int32(max_dropping_vlet_rpc_num, 5, "Max num of dropping vlet rpc");

DEFINE_int32(recover_mode, 0, "recover metadata when loading snapshot, 0:normal, 1:use backup and collect from dn");

DEFINE_int32(parallel_process_snapshot_thread_num, 1, "parallel process snapshot thread num");

DEFINE_bool(enable_az_usage_balance_auto_exit, true, "az usage balance will exit automatically after finish");

//allocator recollect
DEFINE_int32(delay_allocator_recollect_second, 30, "delay notify allocator to recollect when create volumes succeed");

// create vlet
DEFINE_int64(create_vlet_call_timeout_ms, 15000, "create vlet rpc call timeout ms");
DEFINE_int64(create_vlet_retry_interval_ms, 1500, "create vlet rpc retry interval ms");

// check version
DEFINE_string(min_supported_allocator_version, "", "version to support work");
RIGISTER_FLAG(min_supported_allocator_version, Version::validate_version);
DEFINE_string(min_supported_volumeservice_version, "", "version to support work");
RIGISTER_FLAG(min_supported_volumeservice_version, Version::validate_version);
DEFINE_string(min_supported_tinker_version, "", "version to support work");
RIGISTER_FLAG(min_supported_tinker_version, Version::validate_version);
DEFINE_string(min_supported_checkcenter_version, "", "version to support work");
RIGISTER_FLAG(min_supported_checkcenter_version, Version::validate_version);
DEFINE_string(min_supported_datanode_version, "", "version to support work");
RIGISTER_FLAG(min_supported_datanode_version, Version::validate_version);
DEFINE_string(min_supported_repairer_version, "", "version to support work");
RIGISTER_FLAG(min_supported_repairer_version, Version::validate_version);
DEFINE_string(min_supported_dataagent_version, "", "version to support work");
RIGISTER_FLAG(min_supported_dataagent_version, Version::validate_version);
DEFINE_string(min_supported_monitorcenter_version, "", "version to support work");
RIGISTER_FLAG(min_supported_monitorcenter_version, Version::validate_version);
DEFINE_string(min_supported_tape_center_version, "", "version to support work");
RIGISTER_FLAG(min_supported_tape_center_version, Version::validate_version);
DEFINE_string(min_supported_tape_node_version, "", "version to support work");
RIGISTER_FLAG(min_supported_tape_node_version, Version::validate_version);
DEFINE_string(min_supported_stateservice_version, "", "version to support work");
RIGISTER_FLAG(min_supported_stateservice_version, Version::validate_version);

DEFINE_double(az_disaster_error_sla_threshold, 0.9, "fail put sla threshold");

// whitelist
DEFINE_bool(enable_check_whitelist, true, "is open check whitelist");
DEFINE_uint64(do_check_whitelist_interval_second, 3600, "do check whitelist interval second");
DEFINE_string(master_bns_address, "bns://master.aries.baidu.com:8889", "master bns address");
DEFINE_string(tinker_bns_address, "bns://tinker.aries.baidu.com:8888", "tinker bns address");
DEFINE_string(allocator_bns_address, "bns://allocator.aries.baidu.com:8887", "allocator bns address");
DEFINE_string(volumeservice_bns_address, "bns://volumeservice.aries.baidu.com:8886", "volumeservice bns address");
DEFINE_string(checkcenter_bns_address, "bns://checkcenter.aries.baidu.com:8885", "checkcenter bns address");
DEFINE_string(repairer_bns_address, "bns://repairer.aries.baidu.com:8884", "repairer bns address");
DEFINE_string(monitorcenter_bns_address, "bns://monitorcenter.aries.baidu.com:8883", "monitorcenter bns address");
DEFINE_string(dataagent_bns_address, "bns://dataagent.aries.baidu.com:8882", "dataagent bns address");
DEFINE_string(dataproxy_bns_address, "bns://dataproxy.aries.baidu.com:8881", "dataproxy bns address");
DEFINE_string(datanode_bns_address, "bns://datanode.aries.baidu.com:8880", "datanode bns address");

// cv filter make volume confusion
DEFINE_double(cv_limit_per_disk, 0.5, "coefficient of variation limit, [0.1, 1]");
DEFINE_int32(cv_limit_disk_size_per_disk, 10, "cv filter disk size for one disk");
DEFINE_bool(is_open_cv_filter, false, "is open coefficient of variation limit, make volume distribution confusion");

// backup
DEFINE_bool(enable_backup_metadata, true, "enable backup metadata to bos");
DEFINE_int32(backup_metadata_interval_second, 60, "backup metadata interval");
DEFINE_string(backup_metadata_bos_endpoint, "", "bos's endpoint for backup metadata");
DEFINE_string(backup_metadata_bos_bucket, "", "bos's bucket name for backup metadata");
DEFINE_int32(backup_metadata_bos_sdk_log_level, 3, "bos's sdk log level, fatal:0 error:1 warn:3 info:7 debug:15");
DEFINE_string(backup_metadata_bos_sdk_log_file, "master_bossdk.log", "bos's sdk log filename, empty is stdout");
DEFINE_int32(backup_metadata_bos_sdk_timeout, 120, "bos's sdk timeout");

// volume purge
DEFINE_uint64(volume_purge_scheduler_check_interval_second, 60, "VolumePurgeScheduler check volume interval");
DEFINE_int32(volume_purge_failed_times_warning_threshold, 5, "failed times warning threshold");
DEFINE_uint64(vlet_purge_call_timeout_ms, 2000, "purge vlet call timeout(ms)");

// mysql sync
DEFINE_bool(enable_mysql_sync, false, "enable mysql sync metadata");
DEFINE_bool(mysql_sync_on_learner, true, "do mysql sync on learner, false means sync on follower");
DEFINE_string(mysql_sync_endpoint, "tcp://{host}:{port}/{db}", "mysql sync endpoint");
DEFINE_string(mysql_sync_user, "aries", "mysql sync user");
DEFINE_string(mysql_sync_password, "", "mysql sync password");
DEFINE_uint64(mysql_sync_thread_num, 8, "mysql sync thread num");
DEFINE_uint64(mysql_sync_max_waiting_size, 500000, "max waiting size of queue for mysql sync");
DEFINE_int32(mysql_sync_retry, 10, "mysql sync max retry times");
DEFINE_int32(mysql_sync_event_ttl, 15 * 24 * 60 * 60, "ttl of mysql event table, 0 means no ttl");
}
}

