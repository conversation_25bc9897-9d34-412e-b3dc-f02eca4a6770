//=============================================================================
// Author: <EMAIL>
// Data: 2016-10-27 16:29
// Filename: disk_picker.cpp
// Description: Implementation of Disk Picker
//=============================================================================

#include <base/rand_util.h>
#include "baidu/inf/aries/master/scheduler/vlet_scheduler.h"
#include "baidu/inf/aries/master/scheduler/balance_helper.h"
#include "baidu/inf/aries/master/conf.h"
#include "baidu/inf/aries-api/common/bvar_define.h"

namespace aries {
namespace master {

ARIES_BVAR_RECORDER(master, balance_high_cv_disk_size);
bool PickFilter::check(const std::string& element) const {
    auto iter = _element_map.find(element);
    if (iter != _element_map.end()) {
        if (iter->second + 1 > _max_element_num) {
            LOG(TRACE) << "element_name:" << element
                << " element_num:" << iter->second
                << " limit:" << _max_element_num;
            return false;
        }
    }
    return true;
}

void PickFilter::add_element(const std::string& element) {
    auto iter = _element_map.find(element);
    if (iter != _element_map.end()) {
        ++iter->second;
    } else {
        _element_map.insert(std::make_pair(element, 1));
    }
}

MultiPickFilter::MultiPickFilter(int default_max,
        std::unordered_map<std::string, int> custom_max) : PickFilter(default_max), _cumstom_max_num_map(custom_max) {
}

bool MultiPickFilter::check(const std::string& element) const {
    auto iter = _element_map.find(element);
    int max = _max_element_num;
    auto iter_custom = _cumstom_max_num_map.find(element);
    if (iter_custom != _cumstom_max_num_map.end()) {
        max = iter_custom->second;
    }
    if (iter != _element_map.end()) {
        if (iter->second + 1 > max) {
            LOG(TRACE) << "element_name:" << element
                << " element_num:" << iter->second
                << " limit:" << max;
            return false;
        }
    } else {
         if (max == 0) {
            return false;
         }
    }
    return true;
}

bool GroupPickFilter::check(const std::string& group_name) const {
    if (_group_set.count(group_name) == 0) {
        return false;
    }
    return true;
}

bool DiskTypePickFilter::check(const std::string& disk_type) const {
    if (_disk_type_set.count(disk_type) == 0) {
        return false;
    }
    return true;
}

bool EngineTypePickFilter::check(const EngineType disk_engine_type) const {
    if (!_engine_type_set.empty() && _engine_type_set.count(disk_engine_type) == 0) {
        return false;
    }
    return true;
}

bool AZPickFilter::check(const std::string& az_name) const {
    if (_az_set.count(az_name) == 0) {
        return false;
    }
    return _filter.check(az_name);
}

bool IDCPickFilter::check(const std::string& idc_name) const {
    if (_idc_set.count(idc_name) == 0) {
        return false;
    }
    return _filter.check(idc_name);
}

bool RackPickFilter::check(const std::string& rack_name) const {
    if (_rack_set.count(rack_name) == 0) {
        return false;
    }
    return _filter.check(rack_name);
}

bool NodePickFilter::check(const uint64_t node_addr) const {
    if (!_node_set.empty() && _node_set.count(node_addr) == 0) {
        return false;
    }
    std::string addr_name = common::endpoint2str(common::int2endpoint(node_addr));
    return _filter.check(addr_name);
}

GroupPickFilter::GroupPickFilter(const std::set<std::string>& group_set)
    : _group_set(group_set){
}

DiskTypePickFilter::DiskTypePickFilter(const std::set<std::string>& disk_type_set) 
    : _disk_type_set(disk_type_set) {
}

EngineTypePickFilter::EngineTypePickFilter(const std::set<EngineType>& engine_type_set) 
    : _engine_type_set(engine_type_set) {
}

AZPickFilter::AZPickFilter(const std::set<std::string>& az_set,
        const NodeVector& node_list,
        const int max,
        std::unordered_map<std::string, int> custom_max)
    : _filter(max, custom_max), _az_set(az_set) {
    for (auto& node : node_list) {
        _filter.add_element(node->az_name());
    }
}

IDCPickFilter::IDCPickFilter(const std::set<std::string>& idc_set,
        const NodeVector& node_list, const int max)
    : _filter(max), _idc_set(idc_set) {
    for (auto& node : node_list) {
        _filter.add_element(node->idc_name());
    }
}

RackPickFilter::RackPickFilter(const std::set<std::string>& rack_set,
        const NodeVector& node_list, const int max)
    : _filter(max), _rack_set(rack_set) {
    for (auto& node : node_list) {
        _filter.add_element(node->rack_name());
    }
}

NodePickFilter::NodePickFilter(const std::unordered_set<uint64_t>& node_set,
        const NodeVector& node_list) 
    : _filter(1), _node_set(node_set) {
    for (auto& node : node_list) {
        _filter.add_element(common::endpoint2str(node->addr()));
    }
}

void DiskPicker::refresh_suitable_manual_disk_list(const std::unordered_set<uint64_t>& disk_set) {
    auto new_dest_manual_balance_cluster = std::make_shared<DiskPickerCluster>();
    uint64_t add_disk_num = 0;
    for (const auto disk_addr : disk_set) {
        auto node = g_node_manager->get_node(common::int2endpoint(disk_addr));
        if (node == nullptr) {
            continue;
        }
        auto disk = node->get_disk(common::int2diskid(disk_addr));

        if (disk == nullptr) {
            continue;
        }
        auto az_name = node->az_name();
        auto group_name = node->group_name();
        auto usage_for_create_limit = get_max_disk_usage_for_create(az_name);
        if (node->is_alive() != true) {
            LOG(NOTICE) << "node:" << common::endpoint2str(node->addr()).c_str() 
                << " is not alive";
            continue;
        }
        auto total_size = disk->total_size();
        auto used_size = disk->used_size();
        int64_t disk_free_size = disk->disk_free_size();
        auto usage = disk->usage();
        int64_t quota_free_size = 
            (int64_t)(total_size * (uint64_t)(usage_for_create_limit * 100)) / 100
            - (int64_t)used_size;

        // if disk full, will not be dest.
        if (disk->is_full()) {
            LOG(NOTICE) << "node:" << common::endpoint2str(node->addr()).c_str() 
                << " disk_id:" << disk->disk_id() << " is full"
                << " usage:" << usage
                << " disk_free_size:" << disk_free_size
                << " quota_free_size:" << quota_free_size;
            continue;
        }

        //for check balance limit.
        if (node->state_permit_balance_dest()
                && usage  < usage_for_create_limit
                && g_balance_helper->permit_balance_on_disk(disk_addr, false)) {

            new_dest_manual_balance_cluster->add_disk(node, disk);
            ++add_disk_num;
            LOG(TRACE) << "node:" << common::endpoint2str(node->addr()).c_str() 
                       << " disk_id:" << disk->disk_id()
                       << " add to be manual balance dest disk.";
        }
    }
    {
        common::ScopedMutexLock lock(_mutex);
        _dest_manual_balance_cluster.swap(new_dest_manual_balance_cluster);
    }
    LOG(NOTICE) << "refresh suitable manual disk_list finished, size: " << add_disk_num;
}

void DiskPicker::refresh_suitable_disk_list(const NodeVector& node_list) {
    std::unordered_map<std::string, uint64_t> new_az_group_disk_type_free_size_map;
    auto new_src_balance_cluster = std::make_shared<DiskPickerCluster>();
    auto new_dest_balance_cluster = std::make_shared<DiskPickerCluster>();
    auto new_recover_cluster = std::make_shared<DiskPickerCluster>();
    auto new_create_cluster = std::make_shared<DiskPickerCluster>();

    int not_alive = 0;
    int total_node_num = 0;
    int total_disk_num = 0;
    int disk_full_num = 0;

    int recover_disk_num = 0;
    int create_disk_num = 0;
    int src_balance_disk_num = 0;
    int dest_balance_disk_num = 0;

    for (auto& node : node_list) {
        auto az_name = node->az_name();
        auto group_name = node->group_name();
        if (node->is_alive() != true) {
            not_alive++;
            LOG(NOTICE) << "node:" << common::endpoint2str(node->addr()).c_str() 
                << " is not alive";
            continue;
        }
        total_node_num++;
        DiskVector disk_list;
        node->get_disk_list(&disk_list);
        auto usage_for_create_limit = get_max_disk_usage_for_create(az_name);
        for (auto& disk : disk_list) {
            ++total_disk_num;

            auto total_size = disk->total_size();
            auto used_size = disk->used_size();
            int64_t disk_free_size = disk->disk_free_size();
            auto usage = disk->usage();
            int64_t quota_free_size = 
                (int64_t)(total_size * (uint64_t)(usage_for_create_limit * 100)) / 100
                - (int64_t)used_size;

            auto disk_type = disk->disk_type();

            if (node->state_permit_balance_src()) {

                new_src_balance_cluster->add_disk(node, disk);

                src_balance_disk_num++;
                LOG(TRACE) << "node:" << common::endpoint2str(node->addr()).c_str() 
                           << " disk_id:" << disk->disk_id()
                           << " add to be balance src disk.";
            }

            // if disk full, will not be dest.
            if (disk->is_full()) {
                disk_full_num++;
                LOG(NOTICE) << "node:" << common::endpoint2str(node->addr()).c_str() 
                    << " disk_id:" << disk->disk_id() << " is full"
                    << " usage:" << usage
                    << " disk_free_size:" << disk_free_size
                    << " quota_free_size:" << quota_free_size;
                continue;
            }

            //for check balance limit.
            auto disk_addr = common::add_diskid2int(
                    common::endpoint2int(node->addr()), disk->disk_id());
            if (node->state_permit_balance_dest()
                    && usage  < usage_for_create_limit
                    && g_balance_helper->permit_balance_on_disk(disk_addr, false)) {

                new_dest_balance_cluster->add_disk(node, disk);

                dest_balance_disk_num++;
                LOG(TRACE) << "node:" << common::endpoint2str(node->addr()).c_str() 
                           << " disk_id:" << disk->disk_id()
                           << " add to be balance dest disk.";
            }

            if (node->state_permit_recover()) {

                new_recover_cluster->add_disk(node, disk);

                recover_disk_num++;
                LOG(TRACE) << "node:" << common::endpoint2str(node->addr()).c_str() 
                           << " disk_id:" << disk->disk_id()
                           << " add to be recover dest disk.";
            }

            if (node->state_permit_create() 
                    && usage < usage_for_create_limit) {

                new_create_cluster->add_disk(node, disk);
                create_disk_num++;

                LOG(NOTICE) << "node:" << common::endpoint2str(node->addr()).c_str()
                    << " disk_id:" << disk->disk_id() << " is as a suitable disk";

                //for auto create pre-judgment how many volume can be create.
                std::string free_size_name = 
                    az_name + "_" + group_name + "_" + disk_type;
                new_az_group_disk_type_free_size_map[free_size_name] += quota_free_size;


                LOG(TRACE) << "node_addr:" << common::endpoint2str(node->addr()).data()
                           << " usage:" << usage
                           << " az:" << az_name
                           << " group:" << group_name
                           << " disk_type:" << disk_type
                           << " az_group_disk_type_free_space:" 
                               << new_az_group_disk_type_free_size_map[free_size_name];
            }
        }
    }

    {
        common::ScopedMutexLock lock(_mutex);
        _az_group_disk_type_free_size_map.swap(new_az_group_disk_type_free_size_map);
        _recover_cluster.swap(new_recover_cluster);
        _create_cluster.swap(new_create_cluster);
        _src_balance_cluster.swap(new_src_balance_cluster);
        _dest_balance_cluster.swap(new_dest_balance_cluster);
    }

    LOG(NOTICE) << "refresh disk picker suitable node list for create and recover,"
                << " not_alive_node_num:" << not_alive
                << " total_node_num:" << total_node_num
                << " total_disk_num:" << total_disk_num
                << " disk_full_num:" << disk_full_num
                << " suitable_disk_num_for_recover:" << recover_disk_num
                << " suitable_disk_num_for_create:" << create_disk_num
                << " suitable_disk_num_for_src_balance:" << src_balance_disk_num
                << " suitable_disk_num_for_dest_balance:" << dest_balance_disk_num;
}

bool DiskPicker::is_suitable_for_create_vlet(
        const std::shared_ptr<Node>& node,
        const std::shared_ptr<Disk>& disk, 
        const VletVector& vlet_list,
        const std::shared_ptr<Vlet>& vlet, 
        const EngineType vlet_engine_type,
        const bool is_manual_balance,
        std::string* err) {
    if (node == nullptr || disk == nullptr) {
        *err = "node or disk is nullptr";
        return false;
    }
    auto volume = vlet->volume();
    if (volume == nullptr) {
        *err = "volume is nullptr";
        return false;
    }
    auto space = volume->space();
    if (space == nullptr) {
        *err = "space is nullptr";
        return false;
    }
    auto max_per_idc = space->max_vlet_per_idc();
    auto max_per_rack = space->max_vlet_per_rack();
    auto max_per_az = space->max_vlet_per_az();
    std::unordered_map<std::string, int> az_max_vlet_policy_map = space->az_max_vlet_policy_map();

    std::set<std::string> suitable_az_set = g_disk_picker->pick_az(space, volume, vlet);
    std::set<std::string> suitable_idc_set;
    std::set<std::string> suitable_rack_set;
    {
        for (auto & az_name : suitable_az_set) {
            auto az = g_az_manager->get_az(az_name);
            if (az == nullptr) {
                LOG(TRACE) << "az_name is not exist in az_manager, az_name: " << az_name;
                continue;
            }
            IDCVector idc_list;
            az->get_idc_list(&idc_list);
            for (auto & idc : idc_list) {
                suitable_idc_set.insert(idc->idc_name());
                RackVector rack_list;
                idc->get_rack_list(&rack_list);
                for (auto & rack : rack_list) {
                    suitable_rack_set.insert(rack->rack_name());
                }
            }
        }
    }

    std::unordered_set<uint64_t> node_set;
    NodeVector node_list;
    for (auto& v : vlet_list) {
        if (is_vlet_state_not_exist(vlet->state())) {
            continue;
        }
        if (v->shard_index() == vlet->shard_index()) {
            continue;
        }
        auto node = v->node();
        if (node) {
            //check node because vlet maybe change after check vlet exist in other thread
            //in state machine it will not change.
            node_list.push_back(node);
        }
    }

    std::set<std::string> group_set = space->group_set();
    std::set<std::string> disk_type_set;
    auto ret = pick_disk_type_by_vlet(vlet, is_manual_balance, disk_type_set);
    if (!ret) {
        LOG(FATAL) << "pick_disk_list failed by pick error disk_type_set.";
        return false;
    } else {
        std::ostringstream disk_type_names;
        for (auto disk_type_name : disk_type_set) {
            disk_type_names << disk_type_name << ",";
        }
        LOG(TRACE) << "pick disk type succ, disk_type_set name: " << disk_type_names.str()
            << " vlet disk_type_name:" << vlet->disk_type_name();
    }
    std::set<EngineType> space_engine_set;
    {
        int engine_type = common::vlet_type_by_vlet(space->vlet_type());
        if (engine_type > 0) {
            space_engine_set.insert((EngineType)engine_type);
        }
        auto disk_vlet_type = space->disk_vlet_type_set();
        for (auto pair : disk_vlet_type) {
            engine_type = common::vlet_type_by_vlet(pair.second);
            if (engine_type > 0) {
                space_engine_set.insert((EngineType)engine_type);
            }
        }
        space_engine_set.insert(ENGINE_AUTO);
    }
    std::set<EngineType> engine_set;
    {
        if (disk->engine_type() != ENGINE_AUTO) {
            engine_set = {disk->engine_type()};
        }
    }
    DiskTypePickFilter disk_type_filter(disk_type_set);
    EngineTypePickFilter space_engine_type_filter(space_engine_set);
    EngineTypePickFilter engine_type_filter(engine_set);
    GroupPickFilter group_filter(group_set);
    AZPickFilter az_filter(suitable_az_set, node_list, max_per_az, az_max_vlet_policy_map);
    IDCPickFilter idc_filter(suitable_idc_set, node_list, max_per_idc);
    RackPickFilter rack_filter(suitable_rack_set, node_list, max_per_rack);
    NodePickFilter node_filter(node_set, node_list);
    do {
        
        if (!space_engine_type_filter.check(vlet_engine_type)) {
            *err = "space vlet_type_filter check err";
            break;
        }
        if (!engine_type_filter.check(vlet_engine_type)) {
            *err = "disk vlet_type_filter check err";
            break;
        }
        if (!disk_type_filter.check(disk->disk_type())) {
            *err = "disk_type_filter check err";
            break;
        }
        if (!group_filter.check(node->group_name())) {
            *err = "group_filter check err";
            break;
        }
        if (!az_filter.check(node->az_name())) {
            *err = "az_filter check err";
            break;
        }
        if (!idc_filter.check(node->idc_name())) {
            *err = "idc_filter check err";
            break;
        }
        if (!rack_filter.check(node->rack_name())) {
            *err = "rack_filter check err";
            break;
        }
        if (!node_filter.check(common::endpoint2int(node->addr()))) {
            *err = "node_filter check err";
            break;
        }
        return true;
    } while (0);

    LOG(TRACE) << "vid:" << volume->volume_id() << " not suitable:" << err->data();
    return false;
}

std::set<std::string> DiskPicker::pick_az_with_lock(const std::shared_ptr<Space>& space,
                    const std::shared_ptr<Volume>& volume,
                    const std::shared_ptr<Vlet>& vlet) {
    common::ScopedMutexLock lock(_mutex);
    return g_disk_picker->pick_az(space, volume, vlet);
}

std::set<std::string> DiskPicker::pick_az(const std::shared_ptr<Space>& space,
                    const std::shared_ptr<Volume>& volume,
                    const std::shared_ptr<Vlet>& vlet) {
    std::set<std::string> pick_az_set;

    std::vector<std::string> az_name_list;
    space->get_az_name_list(&az_name_list);
    int az_name_list_size = az_name_list.size();
    auto shard_index = vlet->shard_index();
    auto group_set = space->group_set();
    auto disk_type_set = space->disk_type_set();
    auto az_place_policy = space->az_place_policy();

    // if has specified az place policy, choose the special az,
    // otherwise choose az within az policy
    if (!az_place_policy.empty()) {
        auto it = az_place_policy.find(shard_index);
        if (it != az_place_policy.end()) {
            auto valid_az = std::find(az_name_list.begin(), az_name_list.end(), it->second);
            if (valid_az != az_name_list.end()) {
                pick_az_set.insert(it->second);
                return std::move(pick_az_set);
            } else {
                LOG(WARNING) << "bad az place policy, for az:" << it->second
                    << " not in space's az list"
                    << " space:" << space->space_name();
            }
        }
    }

    if (space->pick_az_policy() == PAZP_MAX_PER_AZ) {
        for (auto& az_n : az_name_list) {
            pick_az_set.insert(az_n);
        }
        return std::move(pick_az_set);
    }
    else if (space->pick_az_policy() == PAZP_ROUND_BY_AZ) {
        assert(az_name_list_size != 0);
        auto az_index = shard_index % az_name_list_size;
        auto az_name = az_name_list[az_index];
        pick_az_set.insert(az_name);
        return std::move(pick_az_set);
    } else if (space->pick_az_policy() == PAZP_UNIT_IN_AZ) {
        auto volume_size = space->n() * volume->per_vlet_size();
        std::string pick_az_name = "NOT_EXIST_AZ";
        //if volume has a vlet has created, use his az

        VletVector vlet_list;
        volume->get_vlet_list(&vlet_list);
        for (auto& meta_vlet : vlet_list) {
            if (meta_vlet->shard_index() == vlet->shard_index()) {
                continue;
            }
            auto node = meta_vlet->node();
            //pick az will call serialize.
            if (node != nullptr) {
                pick_az_set.insert(node->az_name());
                return std::move(pick_az_set);
            }
        }

        //if volume has unit az , use it.
        auto unit_az_name = volume->unit_az_name();
        if (unit_az_name != "") {
            pick_az_set.insert(unit_az_name);
            return std::move(pick_az_set);
        }

        LOG(TRACE) <<  " volume:" << volume->volume_id()
            << " has not make sure az now, will pick a az";

        //find a az has enouph free disk space.
        int legal_az_num = 0;
        std::vector<int> weight_vector;
        int total_num = 0;
        for (auto& az_n : az_name_list) {

            auto az_group_free = az_group_disk_type_free_size_unlocked(
                    az_n, group_set, disk_type_set);
            int az_group_free_can_create_vlet = 
                    az_group_free / volume_size;

            total_num += az_group_free_can_create_vlet;
            weight_vector.push_back(total_num);
        }
        if (total_num < 1) {
            return std::move(pick_az_set);
        }
        auto random = base::RandInt(1, total_num);
        for (size_t i = 0; i < az_name_list.size(); ++i) {
            if (weight_vector[i] >= random) {
                auto pick_az_name = az_name_list[i];
                LOG(TRACE) << "end of pick az, pick az name:" << pick_az_name
                    << " for space:" << space->space_name()
                    << " volume_id:" << volume->volume_id()
                    << " shard_index:" << vlet->shard_index();

                volume->set_unit_az_name(pick_az_name);
                pick_az_set.insert(std::move(pick_az_name));
                return std::move(pick_az_set);
            }
        }
    }
    assert(false);
}

bool DiskPicker::pick_disk_type_by_vlet(const std::shared_ptr<Vlet>& vlet, 
            const bool is_manual_balance, std::set<std::string>& disk_type_set) {
    auto volume = vlet->volume();
    if (nullptr == volume) {
        return false;
    }
    auto space = volume->space();
    if (nullptr == space) {
        return false;
    }

    // 0.SPACE_RULE_TYPE: pick disk type by disk_type_place_policy.
    if (FLAGS_pick_disk_type_policy == SPACE_RULE_TYPE || is_manual_balance) {
        disk_type_set = space->disk_type_set();
        auto disk_type_place_policy = space->disk_type_place_policy();
        if (!disk_type_place_policy.empty()) {
            auto it = disk_type_place_policy.find(vlet->shard_index());
            if (it != disk_type_place_policy.end()) {
                auto disk_type = disk_type_set.find(it->second);
                if (disk_type != disk_type_set.end()) {
                    disk_type_set.clear();
                    disk_type_set.insert(it->second);
                } else {
                    LOG(WARNING) << "bad disk type place policy, disk_type:" << it->second
                        << " not in space's disk type set."
                        << " space:" << space->space_name();
                }
            }
        }
        return true;
    }
    // 1.SAME_DISK_TYPE: pick disk type by same vlet disk type.
    // MASTER_VLET_TASK_TYPE_CREATE: pick disk type by space vlet type.
    // MASTER_VLET_TASK_TYPE_RECOVER: pick disk type by old vlet type.
    // Others: pick disk type by same vlet disk type.
    if (FLAGS_pick_disk_type_policy == SAME_DISK_TYPE) {
        disk_type_set.clear();
        auto vlet_disk_type = vlet->disk_type_name();
        if (!vlet_disk_type.empty()) {
            disk_type_set.insert(vlet_disk_type);
            return true;
        }
        VletType pick_vlet_type = VLET_TYPE_ERROR;
        pick_vlet_type = vlet->vlet_type();
        //TODO: vlet_type maybe not valid on snapshot load or compact vlet, not only create
        if (pick_vlet_type == VLET_TYPE_ERROR) {
            pick_vlet_type = space->vlet_type();
        }
        bool found = false;
        for (auto & it : space->disk_vlet_type_set()) {
            if (it.second == pick_vlet_type) {
                disk_type_set.insert(it.first);
                found = true;
                break;
            }
        }
        if (!found) {
            disk_type_set = space->disk_type_set();
        }
        return true;
    }
    // 2.SPACE_DISK_TYPE: pick disk type by all space disk type.
    if (FLAGS_pick_disk_type_policy == SPACE_DISK_TYPE) {
        disk_type_set = space->disk_type_set();
        return true;
    } else {
        LOG(FATAL) << "bad pick_disk_type_policy, please check FLAGS_pick_disk_type_policy: "
            << FLAGS_pick_disk_type_policy;
        return false;
    }
}

std::shared_ptr<Disk> DiskPicker::pick_disk_for_vlet(const std::shared_ptr<Vlet>& vlet,
        const MasterVletTaskType& task_type) {
    _timer.start();

    auto volume = vlet->volume();
    if (volume == nullptr) {
        return nullptr;
    }
    auto volume_id = volume->volume_id();
    auto shard_index = vlet->shard_index();
    common::ScopedMutexLock lock(_mutex);
    
    auto space = volume->space();
    //in make task ,make sure space exist.
    if (space == nullptr) {
        return nullptr;
    }

    auto max_per_idc = space->max_vlet_per_idc();
    auto max_per_rack = space->max_vlet_per_rack();
    auto max_per_az = space->max_vlet_per_az();
    std::unordered_map<std::string, int> az_max_vlet_policy_map = space->az_max_vlet_policy_map();

    std::set<std::string> az_set = this->pick_az(space, volume, vlet);
    std::set<std::string> idc_set;
    std::set<std::string> rack_set;
    {
        for (auto & az_name : az_set) {
            auto az = g_az_manager->get_az(az_name);
            if (az == nullptr) {
                LOG(TRACE) << "az_name is not exist in az_manager, az_name: " << az_name;
                continue;
            }
            IDCVector idc_list;
            az->get_idc_list(&idc_list);
            for (auto & idc : idc_list) {
                idc_set.insert(idc->idc_name());
                RackVector rack_list;
                idc->get_rack_list(&rack_list);
                for (auto & rack : rack_list) {
                    rack_set.insert(rack->rack_name());
                }
            }
        }
    }
    std::set<std::string> same_az_set {vlet->az_name()};
    std::set<std::string> same_idc_set {vlet->idc_name()};
    std::set<std::string> same_rack_set {vlet->rack_name()};

    VletVector vlet_list;
    volume->get_vlet_list(&vlet_list);

    std::unordered_set<uint64_t> node_set;
    NodeVector node_list;
    NodeVector all_node_list;
    for (auto& vlet : vlet_list) {
        std::shared_ptr<Node> node;
        if (is_vlet_state_not_exist(vlet->state())) {
            node = vlet->tmp_node();
        } else {
            node = vlet->node();
        }
        if (node) {
            if (vlet->shard_index() != shard_index) {
                node_list.push_back(node);      // recover dst can pick origin node
            }
            all_node_list.push_back(node);
        }
    }

    std::set<std::string> group_set = space->group_set();
    std::set<std::string> disk_type_set;
    bool is_manual_balance = (task_type == MASTER_VLET_TASK_TYPE_MANUAL_BALANCE);
    auto ret = pick_disk_type_by_vlet(vlet, is_manual_balance, disk_type_set);
    if (!ret) {
        LOG(FATAL) << "pick_disk_list failed by pick error disk_type_set.";
        return nullptr;
    } else {
        std::ostringstream disk_type_names;
        for (auto disk_type_name : disk_type_set) {
            disk_type_names << disk_type_name << ",";
        }
        LOG(TRACE) << "pick disk type succ, disk_type_set name: " << disk_type_names.str()
            << " vlet disk_type_name:" << vlet->disk_type_name();
    }
    std::set<EngineType> space_engine_set;
    {
        int engine_type = common::vlet_type_by_vlet(space->vlet_type());
        if (engine_type > 0) {
            space_engine_set.insert((EngineType)engine_type);
        }
        auto disk_vlet_type = space->disk_vlet_type_set();
        for (auto pair : disk_vlet_type) {
            engine_type = common::vlet_type_by_vlet(pair.second);
            if (engine_type > 0) {
                space_engine_set.insert((EngineType)engine_type);
            }
        }
        space_engine_set.insert(ENGINE_AUTO);
    }
    
    std::set<EngineType> rule_engine_set;
    {   
        int engine_type = common::vlet_type_by_vlet(vlet->vlet_type());
        auto disk = vlet->disk();
        if (disk == nullptr || disk->engine_type() == ENGINE_AUTO || engine_type != disk->engine_type()) { 
            rule_engine_set = space_engine_set;
        } else {
            rule_engine_set = {disk->engine_type()};
        }
    }
    DiskTypePickFilter disk_type_filter(disk_type_set);

    // create & recover vlet to ENGINE_AUTO disk
    // manual balance vlet to space rule disk
    // other balance to same engine type disk if old type not ENGINE_AUTO and engine is match 
    EngineTypePickFilter create_engine_type_filter({ENGINE_AUTO});
    EngineTypePickFilter space_engine_type_filter(space_engine_set);
    EngineTypePickFilter rule_engine_type_filter(rule_engine_set);

    GroupPickFilter group_filter(group_set);

    AZPickFilter az_filter(az_set, node_list, max_per_az, az_max_vlet_policy_map);
    AZPickFilter same_az_filter(same_az_set, node_list, max_per_az, az_max_vlet_policy_map);

    IDCPickFilter idc_filter(idc_set, node_list, max_per_idc);
    IDCPickFilter same_idc_filter(same_idc_set, node_list, max_per_idc);

    RackPickFilter rack_filter(rack_set, node_list, max_per_rack);
    RackPickFilter same_rack_filter(same_rack_set, node_list, max_per_rack);

    NodePickFilter node_filter(node_set, node_list);
    NodePickFilter all_node_filter(node_set, all_node_list);

    _disk_list.clear();
    switch (task_type) {
    case MASTER_VLET_TASK_TYPE_RECOVER:
        _recover_cluster->pick_disk_list(&_disk_list, node_filter, rack_filter, idc_filter, az_filter,
            group_filter, create_engine_type_filter, disk_type_filter);
        break;
    case MASTER_VLET_TASK_TYPE_CREATE:
        _create_cluster->pick_disk_list(&_disk_list, node_filter, rack_filter, idc_filter, az_filter,
            group_filter, create_engine_type_filter, disk_type_filter);
        break;
    case MASTER_VLET_TASK_TYPE_RULE_BALANCE:
        _dest_balance_cluster->pick_disk_list(&_disk_list, all_node_filter, rack_filter, idc_filter, az_filter,
            group_filter, rule_engine_type_filter, disk_type_filter);
        break;
    case MASTER_VLET_TASK_TYPE_MANUAL_BALANCE:
        _dest_manual_balance_cluster->pick_disk_list(&_disk_list, all_node_filter, rack_filter, idc_filter, az_filter,
            group_filter, space_engine_type_filter, disk_type_filter);
        break;
    case MASTER_VLET_TASK_TYPE_COMPACT_BALANCE:
        LOG(TRACE) << "pick_disk_list in rack";
        _dest_balance_cluster->pick_disk_list(&_disk_list, all_node_filter, same_rack_filter, same_idc_filter, same_az_filter, 
            group_filter, rule_engine_type_filter, disk_type_filter);
        // first pick in rack, if cannot pick in cluster
        if (_disk_list.size() == 0) {
            LOG(TRACE) << "pick_disk_list in cluster";
            _dest_balance_cluster->pick_disk_list(&_disk_list, all_node_filter, rack_filter, idc_filter, az_filter,
            group_filter, rule_engine_type_filter, disk_type_filter);
        }
        break;
    default:
        assert(0);
    }
    _timer.stop();
    if (_disk_list.size() == 0) {
        LOG(WARNING) << "no suitable node for az,rack,idc,group,disk_type filters,"
            << " please check disk_picker.h WARNING log.";
    }
    _timer.start();
    filter_cv_hight_disk_for_vlet(vlet, &_disk_list);
    _timer.stop();
    ARIES_DEBUG_LOG(TRACE) << "pick_disk_list finished, type:" << MasterVletTaskType_Name(task_type) 
        << " disk_list_size:" << _disk_list.size() << " cost:" << _timer.m_elapsed()
        << " cv filter finished, disk_list_size:" << _disk_list.size() << " cost:" << _timer.m_elapsed() << " end";


    int end_index = _disk_list.size() - 1;
    DiskVector pick_disk_list;

    for (int i = 0; i <= end_index; ++i) {
        if (pick_disk_list.size() >= (uint32_t)FLAGS_pick_disk_level) {
            break;
        }
        auto rand_index = base::RandInt(i, end_index);
        auto int_disk_addr = _disk_list[rand_index];
        std::swap(_disk_list[i], _disk_list[rand_index]);

        auto node_addr = common::int2endpoint(int_disk_addr);
        auto node = g_node_manager->get_node(node_addr);
        if (node == nullptr) {
            continue;
        }
        if (node_filter.check(common::endpoint2int(node_addr)) != true) {
            LOG(TRACE) << "node:" << base::endpoint2str(node->addr()).c_str() 
                << " has same volume's other shard index vlet, can not be pick"
                << " vid:" << volume_id << " shard_index:" << shard_index;
            continue;
        }
        auto disk_id = common::int2diskid(int_disk_addr);
        auto disk = node->get_disk(disk_id);
        if (disk == nullptr) {
            LOG(TRACE) << "node:" << base::endpoint2str(node->addr()).c_str() 
                << " disk_id:" << disk_id << " is null, maybe dropped"
                << " for create vlet, vid:" << volume_id
                << " shard_index:" << shard_index;
            continue;
        }

        // disk->is_full() take too long
        if (disk->usage() > get_max_disk_usage(node->az_name())) {
            LOG(TRACE) << "node:" << base::endpoint2str(node->addr()).c_str() 
                << " disk_id:" << disk_id << " is full, disk full"
                << " task type:" << MasterVletTaskType_Name(task_type)
                << " vid:" << volume_id << " shard_index:" << shard_index;
            continue;
        }

        // double check max_disk_usage_for_create
        if (task_type == MASTER_VLET_TASK_TYPE_CREATE) {
            if (disk->usage() > get_max_disk_usage_for_create(node->az_name())) {
                LOG(TRACE) << "node:" << base::endpoint2str(node->addr()).c_str()
                    << " disk_id:" << disk_id << " usage is over max_disk_usage_for_create"
                    << " task type:" << MasterVletTaskType_Name(task_type)
                    << " vid:" << volume_id << " shard_index:" << shard_index;
                continue;
            }
        }
        pick_disk_list.push_back(disk);
    }

    if (pick_disk_list.size() > 0) {
        int index = 0;
        double usage = pick_disk_list[0]->usage();
        auto n = pick_disk_list.size();
        for (size_t i = 1; i < n; ++i) {
            double usage_i = pick_disk_list[i]->usage();
            if (usage_i < usage) {
                usage = usage_i;
                index = i;
            }
        }
        if (!pick_disk_list[index]->is_full()) {
            LOG(TRACE) << "pick disk for create/recover vlet succ."
                << " task type:" << MasterVletTaskType_Name(task_type)
                << " vid:" << volume_id << " shard_index:" << shard_index
                << " dest_addr:" << common::endpoint2str(pick_disk_list[index]->addr())
                << " disk_id:" << pick_disk_list[index]->disk_id()
                << " disk_usage:" << pick_disk_list[index]->usage();
            return pick_disk_list[index];
        }
    }

    LOG(WARNING) << "pick disk for create/recover vlet failed."
        << " task type:" << MasterVletTaskType_Name(task_type)
        << " vid:" << volume_id << " shard_index:" << shard_index;
    return nullptr;
}

// only for az usage balance
std::shared_ptr<Disk> DiskPicker::pick_az_disk_for_vlet(const std::shared_ptr<Vlet>& vlet,
        const std::string& az_name) {
    // 从指定的 az 中挑选 disk，需保证符合表参数的设定，避免触发 rule balance
    _timer.start();

    auto volume = vlet->volume();
    if (volume == nullptr) {
        return nullptr;
    }
    auto volume_id = volume->volume_id();
    auto shard_index = vlet->shard_index();
    common::ScopedMutexLock lock(_mutex);
    
    auto space = volume->space();
    if (space == nullptr) {
        return nullptr;
    }

    auto max_per_idc = space->max_vlet_per_idc();
    auto max_per_rack = space->max_vlet_per_rack();
    auto max_per_az = space->max_vlet_per_az();
    std::unordered_map<std::string, int> az_max_vlet_policy_map = space->az_max_vlet_policy_map();

    auto az = g_az_manager->get_az(az_name);
    if (az == nullptr) {
        LOG(FATAL) << "az_name is not exist in az_manager, az_name: " << az_name;
        return nullptr;
    }

    std::set<std::string> az_set {az_name};
    std::set<std::string> idc_set;
    std::set<std::string> rack_set;

    IDCVector idc_list;
    az->get_idc_list(&idc_list);
    for (auto & idc : idc_list) {
        idc_set.insert(idc->idc_name());
        RackVector rack_list;
        idc->get_rack_list(&rack_list);
        for (auto & rack : rack_list) {
            rack_set.insert(rack->rack_name());
        }
    }

    VletVector vlet_list;
    volume->get_vlet_list(&vlet_list);

    std::unordered_set<uint64_t> node_set;
    NodeVector node_list;
    NodeVector all_node_list;
    for (auto& vlet : vlet_list) {
        std::shared_ptr<Node> node;
        if (is_vlet_state_not_exist(vlet->state())) {
            node = vlet->tmp_node();
        } else {
            node = vlet->node();
        }
        if (node) {
            if (vlet->shard_index() != shard_index) {
                node_list.push_back(node);
            }
            all_node_list.push_back(node);
        }
    }

    std::set<std::string> group_set = space->group_set();
    std::set<std::string> disk_type_set;
    auto ret = pick_disk_type_by_vlet(vlet, false, disk_type_set);
    if (!ret) {
        LOG(FATAL) << "pick_disk_list failed by pick error disk_type_set.";
        return nullptr;
    } else {
        std::ostringstream disk_type_names;
        for (auto disk_type_name : disk_type_set) {
            disk_type_names << disk_type_name << ",";
        }
        LOG(TRACE) << "pick disk type succ, disk_type_set name: " << disk_type_names.str()
            << " vlet disk_type_name:" << vlet->disk_type_name();
    }

    std::set<EngineType> space_engine_set;
    {
        int engine_type = common::vlet_type_by_vlet(space->vlet_type());
        if (engine_type > 0) {
            space_engine_set.insert((EngineType)engine_type);
        }
        auto disk_vlet_type = space->disk_vlet_type_set();
        for (auto pair : disk_vlet_type) {
            engine_type = common::vlet_type_by_vlet(pair.second);
            if (engine_type > 0) {
                space_engine_set.insert((EngineType)engine_type);
            }
        }
        space_engine_set.insert(ENGINE_AUTO);
    }
    
    std::set<EngineType> rule_engine_set;
    {   
        int engine_type = common::vlet_type_by_vlet(vlet->vlet_type());
        auto disk = vlet->disk();
        if (disk == nullptr || disk->engine_type() == ENGINE_AUTO || engine_type != disk->engine_type()) { 
            rule_engine_set = space_engine_set;
        } else {
            rule_engine_set = {disk->engine_type()};
        }
    }

    DiskTypePickFilter disk_type_filter(disk_type_set);
    EngineTypePickFilter engine_type_filter(rule_engine_set);
    GroupPickFilter group_filter(group_set);
    AZPickFilter az_filter(az_set, node_list, max_per_az, az_max_vlet_policy_map);
    IDCPickFilter idc_filter(idc_set, node_list, max_per_idc);
    RackPickFilter rack_filter(rack_set, node_list, max_per_rack);
    NodePickFilter node_filter(node_set, all_node_list);

    _disk_list.clear();
    _dest_balance_cluster->pick_disk_list(&_disk_list, node_filter, rack_filter, idc_filter, az_filter,
        group_filter, engine_type_filter, disk_type_filter);
    _timer.stop();
    LOG(TRACE) << "pick_disk_list finished, disk_list size:" << _disk_list.size() << " cost:" << _timer.m_elapsed();
    if (_disk_list.size() == 0) {
        LOG(WARNING) << "no suitable node for az,rack,idc,group,disk_type filters,"
            << " please check disk_picker.h WARNING log.";
    }

    int end_index = _disk_list.size() - 1;
    DiskVector pick_disk_list;

    for (int i = 0; i <= end_index; ++i) {
        if (pick_disk_list.size() >= (uint32_t)FLAGS_pick_disk_level) {
            break;
        }
        auto rand_index = base::RandInt(i, end_index);
        auto int_disk_addr = _disk_list[rand_index];
        std::swap(_disk_list[i], _disk_list[rand_index]);

        auto node_addr = common::int2endpoint(int_disk_addr);
        auto node = g_node_manager->get_node(node_addr);
        if (node == nullptr) {
            continue;
        }
        if (node_filter.check(common::endpoint2int(node_addr)) != true) {
            LOG(TRACE) << "node:" << base::endpoint2str(node->addr()).c_str() 
                << " has same volume's other shard index vlet, can not be pick"
                << " vid:" << volume_id << " shard_index:" << shard_index;
            continue;
        }
        auto disk_id = common::int2diskid(int_disk_addr);
        auto disk = node->get_disk(disk_id);
        if (disk == nullptr) {
            LOG(TRACE) << "node:" << base::endpoint2str(node->addr()).c_str() 
                << " disk_id:" << disk_id << " is null, maybe dropped"
                << " for create vlet, vid:" << volume_id
                << " shard_index:" << shard_index;
            continue;
        }

        // disk->is_full() take too long
        if (disk->usage() > get_max_disk_usage(node->az_name())) {
            LOG(TRACE) << "node:" << base::endpoint2str(node->addr()).c_str() 
                << " disk_id:" << disk_id << " is full, disk full"
                << " vid:" << volume_id << " shard_index:" << shard_index;
            continue;
        }
        pick_disk_list.push_back(disk);
    }

    if (pick_disk_list.size() > 0) {
        int index = 0;
        double usage = pick_disk_list[0]->usage();
        auto n = pick_disk_list.size();
        for (size_t i = 1; i < n; ++i) {
            double usage_i = pick_disk_list[i]->usage();
            if (usage_i < usage) {
                usage = usage_i;
                index = i;
            }
        }
        if (!pick_disk_list[index]->is_full()) {
            LOG(TRACE) << "pick disk for az usage balance vlet succ."
                << " vid:" << volume_id << " shard_index:" << shard_index
                << " dest_addr:" << common::endpoint2str(pick_disk_list[index]->addr())
                << " disk_id:" << pick_disk_list[index]->disk_id()
                << " disk_usage:" << pick_disk_list[index]->usage();
            return pick_disk_list[index];
        }
    }

    LOG(WARNING) << "pick disk for az usage balance vlet failed."
        << " vid:" << volume_id << " shard_index:" << shard_index;
    return nullptr;
}

bool DiskPicker::pick_disk_list_by_filter(const std::set<std::string>& az_set,
                            const std::set<std::string>& group_set,
                            const std::set<std::string>& disk_type_set,
                            const std::unordered_set<uint64_t>& node_set,
                            const bool is_src_disk,
                            DiskUint64Vector* disk_list) {
    uint32_t unlimit_num= std::numeric_limits<uint32_t>::max();
    common::ScopedMutexLock lock(_mutex);
    std::set<std::string> idc_set;
    std::set<std::string> rack_set;
    {
        for (auto & az_name : az_set) {
            auto az = g_az_manager->get_az(az_name);
            if (az == nullptr) {
                LOG(TRACE) << "az_name is not exist in az_manager, az_name: " << az_name;
                continue;
            }
            IDCVector idc_list;
            az->get_idc_list(&idc_list);
            for (auto & idc : idc_list) {
                idc_set.insert(idc->idc_name());
                RackVector rack_list;
                idc->get_rack_list(&rack_list);
                for (auto & rack : rack_list) {
                    rack_set.insert(rack->rack_name());
                }
            }
        }
    }
    NodeVector node_list;
    DiskTypePickFilter disk_type_filter(disk_type_set);
    EngineTypePickFilter engine_type_filter({});
    GroupPickFilter group_filter(group_set);
    std::unordered_map<std::string, int> az_max_vlet_policy;
    AZPickFilter az_filter(az_set, node_list, unlimit_num, az_max_vlet_policy);
    IDCPickFilter idc_filter(idc_set, node_list, unlimit_num);
    RackPickFilter rack_filter(rack_set, node_list, unlimit_num);
    //choose in node set， not choose in node list;
    NodePickFilter node_filter(node_set, node_list);

    if (is_src_disk) {
        _src_balance_cluster->pick_disk_list(disk_list, node_filter, rack_filter, idc_filter, az_filter,
            group_filter, engine_type_filter, disk_type_filter);
    } else {
        _dest_balance_cluster->pick_disk_list(disk_list, node_filter, rack_filter, idc_filter, az_filter,
            group_filter, engine_type_filter, disk_type_filter);
    }
    if (disk_list->size() == 0) {
        LOG(WARNING) << "no suitable node for az,rack,idc,group,disk_type,node filters,"
            << " please check disk_picker.h WARNING log.";
        return false;
    }
    int end_index = disk_list->size() - 1;
    DiskUint64Vector tmp_disk_list;
    for (int i = 0; i <= end_index; ++i) {
        auto int_disk_addr = (*disk_list)[i];
        auto node_addr = common::int2endpoint(int_disk_addr);
        auto node = g_node_manager->get_node(node_addr);
        if (node == nullptr) {
            continue;
        }
        auto disk_id = common::int2diskid(int_disk_addr);
        auto disk = node->get_disk(disk_id);
        if (disk == nullptr) {
            LOG(TRACE) << "node:" << base::endpoint2str(node->addr()).c_str() 
                << " disk_id:" << disk_id << " is null, maybe dropped";
            continue;
        }
        if (node->is_alive() != true) {
            LOG(NOTICE) << "node:" << common::endpoint2str(node->addr()).c_str() 
                << " is not alive";
            continue;
        }
        if (is_src_disk) {
            if (!node->state_permit_balance_src()) {
                LOG(TRACE) << "node:" << common::endpoint2str(node->addr()).c_str() 
                           << " node state:" << node->state()
                           << " not permit balance as src";
                continue;
            }
        } else {
            if (!node->state_permit_balance_dest()) {
                LOG(TRACE) << "node:" << common::endpoint2str(node->addr()).c_str() 
                           << " node state:" << node->state()
                           << " not permit balance as dest";
                continue;
            }
            
        }
        tmp_disk_list.push_back(int_disk_addr);
    }
    std::swap(*disk_list, tmp_disk_list);

    return true;
}

// only for usage balance or decommission
bool DiskPicker::pick_dest_disk_list_for_balance(const std::shared_ptr<Vlet>& vlet,
        const VletMoveType& move_type, DiskUint64Vector* disk_list) {
    _timer.start();
    auto volume = vlet->volume();
    if (volume == nullptr) {
        return false;
    }
    auto volume_id = volume->volume_id();
    auto shard_index = vlet->shard_index();
    auto disk = vlet->disk();
    if (disk == nullptr) {
        return false;
    }
    // manaual balance filter
    auto space = volume->space();
    //in make task ,make sure space exist.
    if (space == nullptr) {
        return false;
    }
    
    common::ScopedMutexLock lock(_mutex);
    // suitable azs should include vlet's az
    std::set<std::string> suitable_az_set = this->pick_az(space, volume, vlet);
    auto iter = suitable_az_set.find(vlet->az_name());
    if (iter == suitable_az_set.end()) {
        LOG(TRACE) << "usage balance pick dest disk failed because there is no suitable az,"
            << " volume_id:" << volume_id << " shard_index:" << shard_index
            << " vlet_az:" << vlet->az_name() << " suitable_azs:(" << noflush;
        for (auto suitable_az : suitable_az_set) {
            LOG(TRACE) << suitable_az << "," << noflush;
        }
        LOG(TRACE) << ")";
        return false;
    }
    std::set<std::string> suitable_idc_set;
    std::set<std::string> suitable_rack_set;
    {
        for (auto & az_name : suitable_az_set) {
            auto az = g_az_manager->get_az(az_name);
            if (az == nullptr) {
                LOG(TRACE) << "az_name is not exist in az_manager, az_name: " << az_name;
                continue;
            }
            IDCVector idc_list;
            az->get_idc_list(&idc_list);
            for (auto & idc : idc_list) {
                suitable_idc_set.insert(idc->idc_name());
                RackVector rack_list;
                idc->get_rack_list(&rack_list);
                for (auto & rack : rack_list) {
                    suitable_rack_set.insert(rack->rack_name());
                }
            }
        }
    }
    // usage balance filter
    std::set<std::string> az_set;
    az_set.insert(vlet->az_name());
    std::set<std::string> group_set;
    group_set.insert(vlet->group_name());
    std::set<std::string> disk_type_set;
    disk_type_set.insert(vlet->disk_type_name());
    std::set<VletType> vlet_type_set{vlet->vlet_type()}; 
    
    auto max_per_idc = space->max_vlet_per_idc();
    auto max_per_rack = space->max_vlet_per_rack();
    auto max_per_az = space->max_vlet_per_az();
    std::unordered_map<std::string, int> az_max_vlet_policy_map = space->az_max_vlet_policy_map();

    VletVector vlet_list;
    volume->get_vlet_list(&vlet_list);

    std::unordered_set<uint64_t> node_set;
    NodeVector node_list;
    std::shared_ptr<Node> src_node;
    for (auto& vlet : vlet_list) {
        std::shared_ptr<Node> node;
        if (is_vlet_state_not_exist(vlet->state())) {
            node = vlet->tmp_node();
        } else {
            node = vlet->node();
        }
        if (vlet->shard_index() == shard_index) {
            src_node = node;
            continue;
        }
        if (node) {
            node_list.push_back(node);
        }
    }
    std::set<EngineType> engine_set;
    {   
        auto disk = vlet->disk();
        if (disk) {
            engine_set = {disk->engine_type()};
        }
    }

    DiskTypePickFilter disk_type_filter(disk_type_set);
    EngineTypePickFilter engine_type_filter(engine_set);
    GroupPickFilter group_filter(group_set);
    AZPickFilter az_filter(az_set, node_list, max_per_az, az_max_vlet_policy_map);
    IDCPickFilter idc_filter(suitable_idc_set, node_list, max_per_idc);
    RackPickFilter rack_filter(suitable_rack_set, node_list, max_per_rack);
    if (src_node) { 
        node_list.push_back(src_node);
    }
    NodePickFilter node_filter(node_set, node_list);
    _dest_balance_cluster->pick_disk_list(disk_list, node_filter, rack_filter, idc_filter, az_filter,
        group_filter, engine_type_filter, disk_type_filter);
    _timer.stop();
    LOG(TRACE) << "pick_dest_disk_list_for_balance finished, disk_list size:" << disk_list->size() << " cost:" << _timer.m_elapsed();
    if (disk_list->size() == 0) {
        LOG(WARNING) << "no suitable node for az,rack,idc,group,disk_type filters,"
            << " please check disk_picker.h WARNING log.";
        return false;
    }
    _timer.start();
    filter_cv_hight_disk_for_vlet(vlet, disk_list);
    _timer.stop();
    LOG(TRACE) << "pick_dest_disk_list_for_balance cv finished, disk_list size:" << disk_list->size() << " cost:" << _timer.m_elapsed();

    if (disk_list->size() == 0) {
        LOG(WARNING) << "no suitable node for cv filters,"
            << " please check disk_picker.h WARNING log.";
        return false;
    }

    return true;
}

// we do not know which volume will be balanced, only know the dest disk,
// so we only remove the node with different disk_type or group_name here.
bool DiskPicker::pick_src_disk_list_for_balance(DiskUint64Vector* disk_list,
        const std::shared_ptr<Disk>& dest_disk) {
    _timer.start();
    common::ScopedMutexLock lock(_mutex);

    auto dest_node = dest_disk->node();
    assert(dest_node);
    auto az_name = dest_node->az_name();
    auto group_name = dest_node->group_name();
    auto disk_engine = dest_disk->engine_type();
    auto disk_type = dest_disk->disk_type();
    auto max_per_idc = 1;
    auto max_per_rack = 1;
    auto max_per_az = 1;

    std::set<std::string> az_set;
    az_set.insert(az_name);
    std::set<std::string> group_set;
    group_set.insert(group_name);
    std::set<std::string> idc_set;
    std::set<std::string> rack_set;
    {
        for (auto & az_name : az_set) {
            auto az = g_az_manager->get_az(az_name);
            if (az == nullptr) {
                LOG(TRACE) << "az_name is not exist in az_manager, az_name: " << az_name;
                continue;
            }
            IDCVector idc_list;
            az->get_idc_list(&idc_list);
            for (auto & idc : idc_list) {
                idc_set.insert(idc->idc_name());
                RackVector rack_list;
                idc->get_rack_list(&rack_list);
                for (auto & rack : rack_list) {
                    rack_set.insert(rack->rack_name());
                }
            }
        }
    }

    std::unordered_set<uint64_t> node_set;
    NodeVector node_list;
    std::set<std::string> disk_type_set {disk_type};
    DiskTypePickFilter disk_type_filter(disk_type_set);
    EngineTypePickFilter engine_type_filter({disk_engine});
    GroupPickFilter group_filter(group_set);
    std::unordered_map<std::string, int> az_max_vlet_policy;
    AZPickFilter az_filter(az_set, node_list, max_per_az, az_max_vlet_policy);
    IDCPickFilter idc_filter(idc_set, node_list, max_per_idc);
    RackPickFilter rack_filter(rack_set, node_list, max_per_rack);
    NodePickFilter node_filter(node_set, node_list);

    _src_balance_cluster->pick_disk_list(disk_list, node_filter, rack_filter, idc_filter, az_filter,
            group_filter, engine_type_filter, disk_type_filter);
    _timer.stop();
    LOG(TRACE) << "pick_src_disk_list_for_balance finished , disk_list_size:" << disk_list->size() << " cost:" << _timer.m_elapsed() << noflush;

    if (disk_list->size() == 0) {
        LOG(TRACE) << " end";
        LOG(WARNING) << "no node suitable for rack,idc,group,disk_type filters,"
            << " pick disk for balance source failed.";
        return false;
    }
    _timer.start();
    filter_cv_hight_disk_for_disk(dest_disk, disk_list);
    _timer.stop();
    LOG(TRACE) << " cv filter finished , disk_list_size:" << disk_list->size() << " cost:" << _timer.m_elapsed() << " end";

    if (disk_list->size() == 0) {
        LOG(WARNING) << "no node suitable for cv filters,"
            << " pick disk for balance source failed.";
        return false;
    }
    return true;
}

bool DiskPicker::refresh_cv_hight_disks_map(const NodeVector& node_list) {
    if (!FLAGS_is_open_cv_filter) {
        return false;
    }
    common::ScopedMutexLock lock(_mutex);
    _cv_high_disks_map.clear();
    _space_cover_disk_num.clear();
    SpaceVector space_list;
    g_space_manager->get_space_list(&space_list);
    std::map<std::string, SpaceRules> space_rules_map;
    for (auto& space : space_list) {
        if (space == nullptr) {
            continue;
        }
        SpaceRules space_rules;
        space_rules.group_set = space->group_set();
        space->get_az_name_list(&space_rules.az_list);
        space_rules.disk_type_set = space->disk_type_set();
        space_rules_map[space->space_name()] = space_rules;
    }
    DiskVector total_disk_list;
    for (auto& node : node_list) {
        auto az_name = node->az_name();
        auto group_name = node->group_name();
        if (node->is_alive() != true) {
            LOG(NOTICE) << "node:" << common::endpoint2str(node->addr()).c_str() 
                << " is not alive";
            continue;
        }
        DiskVector disk_list;
        node->get_disk_list(&disk_list);
        for (auto& disk : disk_list) {
            auto az_name = node->az_name();
            auto group_name = node->group_name();
            auto disk_type = disk->disk_type();
            total_disk_list.push_back(disk);
            for (const auto& iter : space_rules_map) {
                auto& space_name = iter.first;
                auto& space_rules = iter.second;
                if (space_rules.group_set.find(group_name) == space_rules.group_set.end()) {
                    continue;
                }
                if (space_rules.disk_type_set.find(disk_type) == space_rules.disk_type_set.end()) {
                    continue;
                }
                if (std::find(space_rules.az_list.begin(), 
                    space_rules.az_list.end(), az_name) == space_rules.az_list.end()) {
                    continue;
                }
                ++_space_cover_disk_num[space_name];
            }
            
        }
    }
    LOG(TRACE) << "refresh cv hight disks, total_disk_num:" << total_disk_list.size();
    bool has_success_flag = false;
    bool success = false;
    for (auto& disk : total_disk_list) {
        success = refresh_cv_hight_disks(disk);
        if (success) {
            has_success_flag = true;
        }
    }
    g_master_balance_high_cv_disk_size_recorder.set_value(_cv_high_disks_map.size());
    return has_success_flag;
}

bool DiskPicker::refresh_cv_hight_disks(const std::shared_ptr<Disk>& disk) {
    if (!FLAGS_is_open_cv_filter) {
        return false;
    }
    if (disk == nullptr || disk->state() != DISK_STATE_NORMAL) {
        LOG(TRACE) << "disk is nullptr or disk is not normal";
        return false;
    }
    DiskUint64Vector tmp_disk_list;
    SpaceVector space_list;
    g_space_manager->get_space_list(&space_list);

    SpaceDiskCVInfo cv_info;
    uint64_t cv_high_disk_num = 0;
    for (auto& space : space_list) {
        auto space_name = space->space_name();
        VletVector disk_vlet_list;
        disk->get_space_exist_vlets(space_name, &disk_vlet_list);
        std::unordered_map<uint64_t, double> disk_vlet_ratio_map;
        uint64_t total_vlet_num = 0;
        double total_disk_vlet_ratio = 0;
        for (auto& disk_vlet : disk_vlet_list) {
            auto volume = disk_vlet->volume();
            auto disk_shard_index = disk_vlet->shard_index();
            VletVector peer_vlet_list;
            volume->get_vlet_list(&peer_vlet_list);
            for (auto& peer_vlet : peer_vlet_list) {
                std::shared_ptr<Disk> peer_disk;
                if (is_vlet_state_not_exist(peer_vlet->state())) {
                    continue;
                } else {
                    peer_disk = peer_vlet->disk();
                }
                if (peer_vlet->shard_index() == disk_shard_index) {
                    continue;
                }
                if (peer_disk) {
                    auto vlet_size = peer_vlet->vlet_engine_info_ptr()->vlet_size();
                    auto disk_addr = common::add_diskid2int(
                    common::endpoint2int(peer_disk->addr()), peer_disk->disk_id());
                    auto peer_vlet_ratio = vlet_size * 1.0 / peer_disk->total_size();
                    total_disk_vlet_ratio += peer_vlet_ratio;
                    disk_vlet_ratio_map[disk_addr] += peer_vlet_ratio;
                    ++total_vlet_num;
                }
            }
        }
        
        uint64_t best_disk_num = std::min(total_vlet_num, _space_cover_disk_num[space_name]);
        if (best_disk_num < disk_vlet_ratio_map.size()) {
            // maybe space_cover_disk_num is smaller
            best_disk_num = disk_vlet_ratio_map.size();
        }
        if (best_disk_num == 0) {
            continue;
        }
        // Calc Coefficient of Variation
        double avg_ratio = total_disk_vlet_ratio / best_disk_num;
        double cv = 0;
        for (const auto& iter : disk_vlet_ratio_map) {
            double disk_vlet_ratio = iter.second;
            cv += (disk_vlet_ratio - avg_ratio) * (disk_vlet_ratio - avg_ratio);
        }
        for (size_t pos = disk_vlet_ratio_map.size(); pos < best_disk_num; ++pos) {
            cv += avg_ratio * avg_ratio;
        }
        cv = std::sqrt(cv / best_disk_num);
        cv = cv / avg_ratio;
        if (cv < FLAGS_cv_limit_per_disk) {
            ARIES_DEBUG_LOG(NOTICE) << "coefficient of variation is less than limit, do not continue filter disk, cv:"
                << cv << " limit:" << FLAGS_cv_limit_per_disk << " addr:" << disk->addr() << " disk_id:" << disk->disk_id() ;
            continue;
        }
        cv_info.space_id = space->space_id();
        cv_info.disk_addr = common::add_diskid2int(common::endpoint2int(disk->addr()), disk->disk_id());
        cv_info.cv = cv;
        ARIES_DEBUG_LOG(TRACE) << "need refresh cv info, space_id:" << cv_info.space_id << " cv:"
                << cv << " limit:" << FLAGS_cv_limit_per_disk << " addr:" 
                << common::endpoint2str(disk->addr()) << " disk_id:" << disk->disk_id() ;
        for (const auto& iter : disk_vlet_ratio_map) {
            if (iter.second > avg_ratio) {
                _cv_high_disks_map[cv_info].push_back(iter.first);
                if (_cv_high_disks_map[cv_info].size() >= (uint32_t)FLAGS_cv_limit_disk_size_per_disk) {
                    break;
                }
            }
        }
    }
    return true;
}

bool DiskPicker::filter_cv_hight_disk_for_vlet(const std::shared_ptr<Vlet>& vlet,
        DiskUint64Vector* disk_list) {
    if (!FLAGS_is_open_cv_filter) {
        return false;
    }
    if (disk_list == nullptr || disk_list->empty()) {
        LOG(TRACE) << "disk list is null or disk_list is empty";
        return false;
    }
    auto volume = vlet->volume();
    auto volume_id = volume->volume_id();
    auto shard_index = vlet->shard_index();

    auto space = volume->space();
    //in make task, make sure space exist.
    if (space == nullptr) {
        LOG(TRACE) << "space is null, vid:" << volume_id;
        return false;
    }
    auto space_name = space->space_name();
    VletVector vlet_list;
    volume->get_vlet_list(&vlet_list);
    std::unordered_set<uint64_t> node_set;
    DiskVector volume_disk_list;
    
    for (auto& vlet : vlet_list) {
        std::shared_ptr<Disk> disk;
        if (is_vlet_state_not_exist(vlet->state())) {
            continue;
        } else {
            disk = vlet->disk();
        }
        if (vlet->shard_index() == shard_index) {
            continue;
        }
        if (disk) {
            volume_disk_list.push_back(disk);
        }
    }
    bool has_success_flag = false;
    bool success = false;
    for (auto& disk : volume_disk_list) {
        success = filter_cv_hight_disk_for_disk(disk, disk_list);
        if (success) {
            has_success_flag = true;
        }
    }
    return has_success_flag;
}

bool DiskPicker::filter_cv_hight_disk_for_disk(const std::shared_ptr<Disk>& disk,
        DiskUint64Vector* disk_list) {
    if (!FLAGS_is_open_cv_filter) {
        return false;
    }
    if (disk == nullptr || disk_list == nullptr || disk_list->empty()) {
        LOG(TRACE) << __FUNCTION__ << ": disk list is null or disk_list is empty";
        return false;
    }
    uint64_t disk_addr = common::add_diskid2int(common::endpoint2int(disk->addr()), disk->disk_id());
    std::vector<int> space_id_list;
    g_space_manager->get_space_id_list(&space_id_list);
    SpaceDiskCVInfo cv_info;
    cv_info.disk_addr = disk_addr;
    std::set<uint64_t> filter_disk_set;
    for (auto space_id : space_id_list) {
        cv_info.space_id = space_id;
        auto iter = _cv_high_disks_map.find(cv_info);
        if (iter != _cv_high_disks_map.end()) {
            filter_disk_set.insert(iter->second.begin(), iter->second.end());
            cv_info.cv = iter->first.cv;
            ARIES_DEBUG_LOG(TRACE) << __FUNCTION__ << ": space_id:" << cv_info.space_id 
                << " disk_addr:" << common::int2endpoint(cv_info.disk_addr) << " cv:" << cv_info.cv;
        } else {
            ARIES_DEBUG_LOG(TRACE) <<  __FUNCTION__  << ": can not find in cv_high_disk_map, space_id:" << cv_info.space_id 
                << " disk_addr:" << common::int2endpoint(cv_info.disk_addr) << " cv:" << cv_info.cv;;
        }
    }
    if (filter_disk_set.empty()) {
        return false;
    }
    DiskUint64Vector filtered_disk_list;
    for (uint32_t pos = 0; pos < disk_list->size(); ++pos) {
        auto addr = disk_list->at(pos);
        if (filter_disk_set.find(addr) == filter_disk_set.end()) {
            filtered_disk_list.push_back(addr);
        }
    }
    ARIES_DEBUG_LOG(TRACE) << __FUNCTION__ << ": disk_num:" << disk_list->size() 
        << " affter filtered disk_num:" << filtered_disk_list.size();
    if (!filtered_disk_list.empty()) {
        // cv_filter will not filter all
        disk_list->swap(filtered_disk_list);
    }
    return true;
}


}
}

