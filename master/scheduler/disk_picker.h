//=============================================================================
// Author: <EMAIL>
// Data: 2016-10-19 10:45
// Filename: disk_picker.h
// Description: Declaration of Disk Picker
//=============================================================================

#ifndef BAIDU_INF_ARIES_MASTER_SCHEDULER_DISK_PICKER_H
#define BAIDU_INF_ARIES_MASTER_SCHEDULER_DISK_PICKER_H

#include <memory>
#include <bthread.h>
#include <bthread_unstable.h>
#include "baidu/inf/aries/master/meta/meta_data.h"
#include "baidu/inf/aries-api/common/buffer.h"
#include "baidu/inf/aries/master/common.h"
#include <unordered_set>
#include <unordered_map>

namespace aries {
namespace master {
typedef std::vector<uint64_t> DiskUint64Vector;

class DiskPicker;
extern DiskPicker *g_disk_picker;

class PickFilter {
public:
    PickFilter(int max) : _max_element_num(max) {}
    virtual bool check(const std::string& element) const;
    virtual void add_element(const std::string& element);
protected:
    std::unordered_map<std::string, int> _element_map;
    int _max_element_num;
};

class MultiPickFilter : public PickFilter {
public:
    MultiPickFilter(int default_max, std::unordered_map<std::string, int> custom_max);
    virtual bool check(const std::string& element) const;
private:
    std::unordered_map<std::string, int> _cumstom_max_num_map;
};

class AZPickFilter {
public:
    AZPickFilter(const std::set<std::string>& az_set, const NodeVector& node_list,
        const int max, std::unordered_map<std::string, int> custom_max);
    bool check(const std::string& az_name) const;
private:
    MultiPickFilter _filter;
     std::set<std::string> _az_set;
};

class DiskTypePickFilter {
public:
    DiskTypePickFilter(const std::set<std::string>& disk_type);
    bool check(const std::string& disk_type) const;
private:
    std::set<std::string> _disk_type_set;
};

class EngineTypePickFilter {
public:
    EngineTypePickFilter(const std::set<EngineType>& engine_type_set);
    bool check(const EngineType engine_type) const;
private:
    std::set<EngineType> _engine_type_set;
};

class GroupPickFilter {
public:
    GroupPickFilter(const std::set<std::string>& group_name);
    bool check(const std::string& group_name) const;
private:
    std::set<std::string> _group_set;
};

class IDCPickFilter {
public:
    IDCPickFilter(const std::set<std::string>& idc_set,
        const NodeVector& node_list, const int max);
    bool check(const std::string& idc_name) const;
private:
    PickFilter _filter;
    std::set<std::string> _idc_set;
};

class RackPickFilter {
public:
    RackPickFilter(const std::set<std::string>& rack_set,
        const NodeVector& node_list, const int max);
    bool check(const std::string& rack_name) const;
private:
    PickFilter _filter;
    std::set<std::string> _rack_set;
};

class NodePickFilter {
public:
    NodePickFilter(const std::unordered_set<uint64_t>& node_set, 
                const NodeVector& node_list);
    bool check(const uint64_t node_addr) const;
private:
    PickFilter _filter;
    std::unordered_set<uint64_t> _node_set;
};

class DiskPickerNode {
public:
    DiskPickerNode(const uint64_t node_addr) : _node_addr(node_addr) {
        _disk_list.reserve(50);
    }
    void add_disk(const std::shared_ptr<Node>& node, const std::shared_ptr<Disk>& disk) {
        uint64_t node_addr = common::endpoint2int(node->addr());
        uint64_t disk_addr = common::add_diskid2int(node_addr, disk->disk_id());
        _disk_list.push_back(disk_addr);
    }
    void pick_disk_list(DiskUint64Vector* disk_list) {
        disk_list->insert(disk_list->end(), _disk_list.begin(), _disk_list.end());
    }
private:
    uint64_t _node_addr;
    std::vector<uint64_t> _disk_list;
};

class DiskPickerRack {
public:
    DiskPickerRack(const std::string rack_name) : _rack_name(rack_name) {} 
    void add_disk(const std::shared_ptr<Node>& node, const std::shared_ptr<Disk>& disk) {
        uint64_t node_addr = common::endpoint2int(node->addr());
        uint64_t disk_addr = common::add_diskid2int(node_addr, disk->disk_id());
        auto node_iter = _node_map.find(node_addr);
        if (node_iter == _node_map.end()) {
            auto node_ptr = std::shared_ptr<DiskPickerNode>(new DiskPickerNode(disk_addr));
            auto ok = _node_map.insert(std::make_pair(node_addr, node_ptr)).second;
            assert(ok == true);
            node_ptr->add_disk(node, disk);
        } else {
            node_iter->second->add_disk(node, disk);
        }
    }
    void pick_disk_list(DiskUint64Vector* disk_list, const NodePickFilter& node_pick_filter) {
        bool flag = false;
        for (auto& node_iter : _node_map) {
            if (node_pick_filter.check(node_iter.first) != true) {
                continue;
            }
            flag = true;
            node_iter.second->pick_disk_list(disk_list);
        }
        if (!flag) {
            LOG(WARNING) << "can not find suitable node for recover or create";
        }
    }
private:
    std::string _rack_name = "";
    std::unordered_map<uint64_t, std::shared_ptr<DiskPickerNode> > _node_map;
};

class DiskPickerIDC {
public:
    DiskPickerIDC(const std::string idc_name) : _idc_name(idc_name) {}
    void add_disk(const std::shared_ptr<Node>& node, const std::shared_ptr<Disk>& disk) {
        auto rack_name = node->rack_name();
        auto rack_iter = _rack_map.find(rack_name);
        if (rack_iter == _rack_map.end()) {
            auto rack_ptr = std::shared_ptr<DiskPickerRack>(new DiskPickerRack(rack_name));
            auto ok = _rack_map.insert(std::make_pair(rack_name, rack_ptr)).second;
            assert(ok == true);
            rack_ptr->add_disk(node, disk);
        } else {
            rack_iter->second->add_disk(node, disk);
        }
    }
    void pick_disk_list(DiskUint64Vector* disk_list, 
            const NodePickFilter& node_pick_filter, 
            const RackPickFilter& rack_pick_filter) {
        bool flag = false;
        for (auto& rack_iter : _rack_map) {
            if (rack_pick_filter.check(rack_iter.first) != true) {
                continue;
            }
            flag = true;
            rack_iter.second->pick_disk_list(disk_list, node_pick_filter);
        }
        if (!flag) {
            LOG(WARNING) << "can not find suitable rack for recover or create";
        }
    }
private:
    std::string _idc_name = "";
    std::unordered_map<std::string, std::shared_ptr<DiskPickerRack>> _rack_map;
};

class DiskPickerAZ {
public:
    DiskPickerAZ(const std::string az_name) : _az_name(az_name) {}
    void add_disk(const std::shared_ptr<Node>& node, const std::shared_ptr<Disk>& disk) {
        auto idc_name = node->idc_name();
        auto idc_iter = _idc_map.find(idc_name);
        if (idc_iter == _idc_map.end()) {
            auto idc_ptr = std::shared_ptr<DiskPickerIDC>(new DiskPickerIDC(idc_name));
            auto ok = _idc_map.insert(std::make_pair(idc_name, idc_ptr)).second;
            assert(ok == true);
            idc_ptr->add_disk(node, disk);
        } else {
            idc_iter->second->add_disk(node, disk);
        }
    }
    void pick_disk_list(DiskUint64Vector* disk_list,
            const NodePickFilter& node_pick_filter, 
            const RackPickFilter& rack_pick_filter,
            const IDCPickFilter& idc_pick_filter) {

        bool flag = false;
        for (auto& idc_iter : _idc_map) {
            if (idc_pick_filter.check(idc_iter.first) != true) {
                continue;
            }
            flag = true;
            idc_iter.second->pick_disk_list(disk_list, node_pick_filter, rack_pick_filter);
        }
        if (!flag) {
            LOG(WARNING) << "can not find suitable idc for recover or create";
        }
    }
private:
    std::string _az_name = "";
    std::unordered_map<std::string, std::shared_ptr<DiskPickerIDC>> _idc_map;
};

class DiskPickerGroup {
public:
    DiskPickerGroup(const std::string group_name) : _group_name(group_name) {}
    void add_disk(const std::shared_ptr<Node>& node, const std::shared_ptr<Disk>& disk) {
        auto az_name = node->az_name();
        auto az_iter = _az_map.find(az_name);
        if (az_iter == _az_map.end()) {
            auto az_ptr = std::shared_ptr<DiskPickerAZ>(new DiskPickerAZ(az_name));
            auto ok = _az_map.insert(std::make_pair(az_name, az_ptr)).second;
            assert(ok == true);
            az_ptr->add_disk(node, disk);
        } else {
            az_iter->second->add_disk(node, disk);
        }
    }
    void pick_disk_list(DiskUint64Vector* disk_list, 
            const NodePickFilter& node_pick_filter, 
            const RackPickFilter& rack_pick_filter,
            const IDCPickFilter& idc_pick_filter,
            const AZPickFilter& az_pick_filter) {

        bool flag = false;
        for (auto& az_iter : _az_map) {
            if (az_pick_filter.check(az_iter.first) != true) {
                continue;
            }
            flag = true;
            az_iter.second->pick_disk_list(disk_list, node_pick_filter, rack_pick_filter, idc_pick_filter);
        }
        if (!flag) {
            LOG(WARNING) << "can not find suitable az for recover or create";
        }
    }
private:
    std::string _group_name = "";
    std::unordered_map<std::string, std::shared_ptr<DiskPickerAZ>> _az_map;
};

class DiskPickerEngineType {
public:
    DiskPickerEngineType(const EngineType engine_type) : _engine_type(engine_type) {}
    void add_disk(const std::shared_ptr<Node>& node, const std::shared_ptr<Disk>& disk) {
        auto group_name = node->group_name();
        auto group_iter = _group_map.find(group_name);
        if (group_iter == _group_map.end()) {
            auto group_ptr = std::shared_ptr<DiskPickerGroup>(new DiskPickerGroup(group_name));
            auto ok = _group_map.insert(std::make_pair(group_name, group_ptr)).second;
            assert(ok == true);
            group_ptr->add_disk(node, disk);
        } else {
            group_iter->second->add_disk(node, disk);
        }
    }
    void pick_disk_list(DiskUint64Vector* disk_list,
            const NodePickFilter& node_pick_filter, 
            const RackPickFilter& rack_pick_filter,
            const IDCPickFilter& idc_pick_filter,
            const AZPickFilter& az_pick_filter,
            const GroupPickFilter& group_pick_filter) {

        bool flag = false;
        for (auto& group_iter : _group_map) {
            if (group_pick_filter.check(group_iter.first) != true) {
                continue;
            }
            flag = true;
            group_iter.second->pick_disk_list(disk_list, node_pick_filter, rack_pick_filter, 
                idc_pick_filter, az_pick_filter);
        }
        if (!flag) {
            LOG(WARNING) << "can not find suitable group for recover or create";
        }
    }

private:
    EngineType _engine_type;
    std::unordered_map<std::string, std::shared_ptr<DiskPickerGroup>> _group_map;
};

class DiskPickerDiskType {
public:
    DiskPickerDiskType(const std::string disk_type) : _disk_type(disk_type) {}
    int disk_num() {
        return _disk_num;
    }
    void add_disk(const std::shared_ptr<Node>& node, const std::shared_ptr<Disk>& disk) {
        auto engine_type = disk->engine_type();
        auto engine_iter = _engine_map.find(engine_type);
        if (engine_iter == _engine_map.end()) {
            auto engine_iter = std::shared_ptr<DiskPickerEngineType>(new DiskPickerEngineType(engine_type));
            auto ok = _engine_map.insert(std::make_pair(engine_type, engine_iter)).second;
            assert(ok == true);
            engine_iter->add_disk(node, disk);
        } else {
            engine_iter->second->add_disk(node, disk);
        }
        _disk_num++;
    }
    void pick_disk_list(DiskUint64Vector* disk_list,
            const NodePickFilter& node_pick_filter, 
            const RackPickFilter& rack_pick_filter,
            const IDCPickFilter& idc_pick_filter,
            const AZPickFilter& az_pick_filter,
            const GroupPickFilter& group_pick_filter,
            const EngineTypePickFilter& engine_pick_filter) {

        bool flag = false;
        for (auto& engine_iter : _engine_map) {
            if (engine_pick_filter.check(engine_iter.first) != true) {
                continue;
            }
            flag = true;
            engine_iter.second->pick_disk_list(disk_list, node_pick_filter, rack_pick_filter, 
                idc_pick_filter, az_pick_filter, group_pick_filter);
        }
        if (!flag) {
            LOG(WARNING) << "can not find suitable engine type for recover or create, disk_type:" << _disk_type;
        }
    }
private:
    std::string _disk_type;
    std::unordered_map<EngineType, std::shared_ptr<DiskPickerEngineType>> _engine_map;
    int _disk_num = 0;
};

class DiskPickerCluster {
public:
    int disk_num() {
        return _disk_num;
    }
    void add_disk(const std::shared_ptr<Node>& node, const std::shared_ptr<Disk>& disk) {
        auto disk_type = disk->disk_type();
        auto disk_type_iter = _disk_type_map.find(disk_type);
        if (disk_type_iter == _disk_type_map.end()) {
            auto disk_type_ptr = std::shared_ptr<DiskPickerDiskType>(new DiskPickerDiskType(disk_type));
            auto ok = _disk_type_map.insert(std::make_pair(disk_type, disk_type_ptr)).second;
            assert(ok == true);
            disk_type_ptr->add_disk(node, disk);
        } else {
            disk_type_iter->second->add_disk(node, disk);
        }
        _disk_num++;
    }

    void pick_disk_list(DiskUint64Vector* disk_list,
            const NodePickFilter& node_pick_filter, 
            const RackPickFilter& rack_pick_filter,
            const IDCPickFilter& idc_pick_filter,
            const AZPickFilter& az_pick_filter,
            const GroupPickFilter& group_pick_filter,
            const EngineTypePickFilter& engine_pick_filter,
            const DiskTypePickFilter& disk_type_pick_filter) {

        bool flag = false;
        for (auto& disk_type_iter : _disk_type_map) {
            if (disk_type_pick_filter.check(disk_type_iter.first) != true) {
                continue;
            }
            flag = true;
            disk_type_iter.second->pick_disk_list(disk_list, node_pick_filter, rack_pick_filter,
                idc_pick_filter, az_pick_filter, group_pick_filter, engine_pick_filter);
            continue;
        }
        if (!flag) {
            LOG(WARNING) << "can not find suitable disk type for recover or create";
        }
    }

private:
    std::unordered_map<std::string, std::shared_ptr<DiskPickerDiskType>> _disk_type_map;
    int _disk_num = 0;
};

class DiskPicker {
public:
    DiskPicker() {
        _recover_cluster = std::shared_ptr<DiskPickerCluster>( new DiskPickerCluster);
        _create_cluster = std::shared_ptr<DiskPickerCluster>( new DiskPickerCluster);
        _src_balance_cluster = std::shared_ptr<DiskPickerCluster>( new DiskPickerCluster);
        _dest_balance_cluster = std::shared_ptr<DiskPickerCluster>( new DiskPickerCluster);
        _dest_manual_balance_cluster = std::shared_ptr<DiskPickerCluster>( new DiskPickerCluster);
    }
    virtual ~DiskPicker() {}

    static bool is_suitable_for_create_vlet(const std::shared_ptr<Node>& node,
            const std::shared_ptr<Disk>& disk, const VletVector& vlet_list,
            const std::shared_ptr<Vlet>& vlet, const EngineType engine_type, 
            const bool is_manual_balance, std::string* err);

    ARIES_VIRTUAL std::shared_ptr<Disk> pick_disk_for_vlet(const std::shared_ptr<Vlet>& vlet,
        const MasterVletTaskType& task_type);

    ARIES_VIRTUAL std::shared_ptr<Disk> pick_az_disk_for_vlet(const std::shared_ptr<Vlet>& vlet,
        const std::string& az_name);

    ARIES_VIRTUAL void refresh_suitable_disk_list(const NodeVector& node_list);

    uint64_t az_group_disk_type_free_size(
            const std::string& az_name,
            const std::set<std::string>& group_name,
            const std::set<std::string>& disk_type) {
        common::ScopedMutexLock lock(_mutex);
        return az_group_disk_type_free_size_unlocked(az_name, group_name, disk_type);
    }

    uint64_t az_group_disk_type_free_size_unlocked(
            const std::string& az_name,
            const std::set<std::string>& group_name,
            const std::set<std::string>& disk_type) {
        uint64_t free_size = 0;
        for (const auto& group_tag : group_name) {
            std::string free_size_name = az_name;
            free_size_name += "_";
            free_size_name += group_tag;
            free_size_name += "_";
            for (const auto& disk_type_tag : disk_type) {
                free_size += _az_group_disk_type_free_size_map.get(free_size_name + disk_type_tag);
            }
            
        }
        return free_size;
    }
    void refresh_suitable_manual_disk_list(const std::unordered_set<uint64_t>& disk_list);
    bool pick_disk_list_by_filter(const std::set<std::string>& az_set,
                            const std::set<std::string>& group_set,
                            const std::set<std::string>& disk_type_set,
                            const std::unordered_set<uint64_t>& node_set,
                            const bool is_src_disk,
                            DiskUint64Vector* disk_list);
    bool pick_src_disk_list_for_balance(DiskUint64Vector* disk_list,
            const std::shared_ptr<Disk>& dest_disk);
    bool pick_dest_disk_list_for_balance(const std::shared_ptr<Vlet>& vlet,
            const VletMoveType& move_type, DiskUint64Vector* disk_list);
    bool filter_cv_hight_disk_for_vlet(const std::shared_ptr<Vlet>& vlet,
            DiskUint64Vector* disk_list);
    bool filter_cv_hight_disk_for_disk(const std::shared_ptr<Disk>& disk,
            DiskUint64Vector* disk_list);
    bool refresh_cv_hight_disks_map(const NodeVector& node_list);
    bool refresh_cv_hight_disks(const std::shared_ptr<Disk>& disk);
    std::set<std::string> pick_az_with_lock(const std::shared_ptr<Space>& space,
                        const std::shared_ptr<Volume>& volume,
                        const std::shared_ptr<Vlet>& vlet);
    static bool pick_disk_type_by_vlet(const std::shared_ptr<Vlet>& vlet, 
                        const bool is_manual_balance, std::set<std::string>& disk_type_set);
private:
    // pick az need _mutex protect  , so must call in func has _mutex
    std::set<std::string> pick_az(const std::shared_ptr<Space>& space,
                        const std::shared_ptr<Volume>& volume,
                        const std::shared_ptr<Vlet>& vlet);

private:
    struct FreeSizeMap {
    public:
        void clear() {
            common::ScopedMutexLock lock(_mutex);
            _map.clear();
        }
        uint64_t get(const std::string& name) {
            common::ScopedMutexLock lock(_mutex);
            return _map[name];
        }
        void add(const std::string& name, uint64_t size) {
            common::ScopedMutexLock lock(_mutex);
            _map[name] += size;
        }
        void swap(std::unordered_map<std::string, uint64_t>& other) {
            common::ScopedMutexLock lock(_mutex);
            _map.swap(other);
        }
    private:
        common::MutexLock _mutex;
        std::unordered_map<std::string, uint64_t> _map;
    };
    struct SpaceRules {
        std::vector<std::string> az_list;
        std::set<std::string> group_set;
        std::set<std::string> disk_type_set;
    };

    struct SpaceDiskCVInfo {
        uint32_t space_id = 0;
        uint64_t disk_addr = 0;
        double cv = 0;
        bool operator==(const SpaceDiskCVInfo& info) const {
            return info.disk_addr == disk_addr && info.space_id == space_id;
        }
    };

    struct SpaceDiskHasher {
        std::size_t operator()(const SpaceDiskCVInfo& space_disk_cv) const {
            return std::hash<uint64_t>{}(space_disk_cv.disk_addr + space_disk_cv.space_id);
        }
    };

private:
    std::shared_ptr<DiskPickerCluster> _src_balance_cluster;
    std::shared_ptr<DiskPickerCluster> _dest_balance_cluster;
    std::shared_ptr<DiskPickerCluster> _recover_cluster;
    std::shared_ptr<DiskPickerCluster> _create_cluster;
    std::shared_ptr<DiskPickerCluster> _dest_manual_balance_cluster;

    DiskUint64Vector _disk_list;
    FreeSizeMap _az_group_disk_type_free_size_map;
    std::unordered_map<std::string, uint64_t> _space_cover_disk_num;
    std::unordered_map<SpaceDiskCVInfo, DiskUint64Vector, SpaceDiskHasher> _cv_high_disks_map;
    common::MutexLock       _mutex;
    base::Timer _timer;
};


}
}

#endif
