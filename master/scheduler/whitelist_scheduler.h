//=============================================================================
// Author: <EMAIL>
// Date: 2020-02-10 11:30
// Description: Declaration of whitelist scheduler
//=============================================================================

#ifndef BAIDU_INF_ARIES_MASTER_SCHEDULER_WHITELIST_SCHEDULER_H
#define BAIDU_INF_ARIES_MASTER_SCHEDULER_WHITELIST_SCHEDULER_H

#include <thread>
#include <atomic>
#include <memory>
#include "master/common.h"
#include "master/meta/meta_data.h"
#include "master/master_control.h"
#include "baidu/inf/aries-api/common/utils.h"
#include "baidu/inf/aries-api/common/master_tracker.h"
#include "baidu/inf/aries-api/common/types.h"

namespace aries {
namespace master {

class WhitelistScheduler;
extern std::shared_ptr<WhitelistScheduler> g_whitelist_scheduler;

class WhitelistScheduler {
public:
    WhitelistScheduler();
    ~WhitelistScheduler();
    void start();
    void stop();
    void join();

private:
    static void* thread_proc(void* arg);

private:
    void run();
    bool update_whitelist_by_bns(const std::string& bns_address, std::vector<base::EndPoint>* whitelist);
    void check_master_by_whitelist();
    void check_allocator_by_whitelist();
    void check_volumeservice_by_whitelist();
    void check_tinker_by_whitelist();
    void check_checkcenter_by_whitelist();
    void check_repairer_by_whitelist();
    void check_dataagent_by_whitelist();
    void check_dataproxy_by_whitelist();
    void check_datanode_by_whitelist();

private:
    std::atomic<bool> _is_stop;
    pthread_t _thread_id;
    int _check_not_in_whitelist_count;      // For UT
    std::vector<base::EndPoint> _master_whitelist;
    std::vector<base::EndPoint> _allocator_whitelist;
    std::vector<base::EndPoint> _volumeservice_whitelist;
    std::vector<base::EndPoint> _tinker_whitelist;
    std::vector<base::EndPoint> _checkcenter_whitelist;
    std::vector<base::EndPoint> _repairer_whitelist;
    std::vector<base::EndPoint> _dataagent_whitelist;
    std::vector<base::EndPoint> _dataproxy_whitelist;
    std::vector<base::EndPoint> _datanode_whitelist;
};

}
}

#endif
