// token scheduler.cpp
// liujian08
//
#include <unistd.h>
#include <base/time.h>
#include <base/logging.h>
#include "master/conf.h"
#include "baidu/inf/aries/master/scheduler/token_scheduler.h"

namespace aries {
namespace master {

DropTokenManager* g_token_manager = new DropTokenManager;

void DropTokenManager::start() {
    pthread_attr_t attr;
    pthread_attr_init(&attr);
    //pthread_attr_setdetachstate(&attr, PTHREAD_CREATE_DETACHED);
    int err = pthread_create(&_thread_id, &attr, thread_proc, (void*)this);
    if (err != 0) {
        LOG(FATAL) << "start DropTokenManager." << std::hex << this
                << " failed due to pthread create failed, errno: " << errno;
        abort();
    } else {
        LOG(NOTICE) << "start DropTokenManager succeeded with pthread_id: " << _thread_id;
    }
}

void* DropTokenManager::thread_proc(void* args) {
    DropTokenManager* token_manager = static_cast<DropTokenManager*>(args);
    token_manager->run();
    return NULL;
}

void DropTokenManager::run() {
    _is_stop = false;
    LOG(NOTICE) << "begin to run DropTokenManager";

    reload();

    while (!_is_stop) {
        alloc_drop_disk_token();
        alloc_drop_node_token();
        alloc_drop_vlet_token();
        alloc_gc_vlet_token();
        alloc_gc_disk_token();
        alloc_single_datanode_drop_disk_token();
        sleep(1);
    }
    LOG(NOTICE) << "end to run DropTokenManager";
}

void DropTokenManager::reload() {
    common::ScopedMutexLock lock(_mutex);
    LOG(NOTICE) << "begin to reload DropTokenManager";
    uint64_t now = time(NULL);

    _drop_disk_token = FLAGS_max_drop_disk_token;
    _last_alloc_drop_disk_token_time = now;

    _drop_node_token = FLAGS_max_drop_node_token;
    _last_alloc_drop_node_token_time = now;

    _drop_vlet_token = FLAGS_max_drop_vlet_token;
    _last_alloc_drop_vlet_token_time = now;

    _gc_vlet_token = FLAGS_max_gc_vlet_token;
    _last_alloc_gc_vlet_token_time = now;

    _gc_disk_token = FLAGS_max_gc_disk_token;
    _last_alloc_gc_disk_token_time = now;

    _single_datanode_drop_disk_token.clear();
    _last_alloc_single_datanode_drop_disk_token_time = now;

    LOG(NOTICE) << "end to reload DropTokenManager";
}

void DropTokenManager::alloc_drop_disk_token() {
    common::ScopedMutexLock lock(_mutex);
    uint64_t now = time(NULL);
    if (now > FLAGS_drop_disk_token_alloc_time_second + _last_alloc_drop_disk_token_time)  {
        _last_alloc_drop_disk_token_time = now;
        if (_drop_disk_token < FLAGS_max_drop_disk_token) {
            ++_drop_disk_token;
            LOG(TRACE) << "update drop disk token to " << _drop_disk_token;
        }
    }
}

void DropTokenManager::alloc_drop_node_token() {
    common::ScopedMutexLock lock(_mutex);
    uint64_t now = time(NULL);
    if (now  > FLAGS_drop_node_token_alloc_time_second + _last_alloc_drop_node_token_time) {
        _last_alloc_drop_node_token_time = now;
        if (_drop_node_token < FLAGS_max_drop_node_token) {
            ++_drop_node_token;
            LOG(TRACE) << "update drop node token to " << _drop_node_token;
        }
    }
}

void DropTokenManager::alloc_drop_vlet_token() {
    common::ScopedMutexLock lock(_mutex);
    uint64_t now = time(NULL);
    if (now > FLAGS_drop_vlet_token_alloc_time_second + _last_alloc_drop_vlet_token_time) {
        _last_alloc_drop_vlet_token_time = now;
        if (_drop_vlet_token < FLAGS_max_drop_vlet_token) {
            ++_drop_vlet_token;
            LOG(TRACE) << "update drop vlet token to " << _drop_vlet_token;
        }
    }
}

void DropTokenManager::alloc_gc_vlet_token() {
    common::ScopedMutexLock lock(_mutex);
    uint64_t now = time(NULL);
    if (now > FLAGS_gc_vlet_token_alloc_time_second + _last_alloc_gc_vlet_token_time) {
        _last_alloc_gc_vlet_token_time = now;
        if (_gc_vlet_token < FLAGS_max_gc_vlet_token) {
            _gc_vlet_token += FLAGS_gc_vlet_token_step;
            LOG(TRACE) << "update gc vlet token to " << _gc_vlet_token;
        }
    }
}

void DropTokenManager::alloc_gc_disk_token() {
    common::ScopedMutexLock lock(_mutex);
    uint64_t now = time(NULL);
    if (now > FLAGS_gc_disk_token_alloc_time_second + _last_alloc_gc_disk_token_time) {
        _last_alloc_gc_disk_token_time = now;
        if (_gc_disk_token < FLAGS_max_gc_disk_token) {
            ++_gc_disk_token;
            LOG(TRACE) << "update gc disk token to " << _gc_disk_token;
        }
    }
}

void DropTokenManager::alloc_single_datanode_drop_disk_token() {
    common::ScopedMutexLock lock(_mutex);
    uint64_t now = time(NULL);
    if (now > FLAGS_single_datanode_drop_disk_token_alloc_time_second + _last_alloc_single_datanode_drop_disk_token_time) {
        _last_alloc_single_datanode_drop_disk_token_time = now;
        for (auto& kv : _single_datanode_drop_disk_token) {
            if (kv.second < FLAGS_max_single_datanode_drop_disk_token) {
                ++kv.second;
            }
        }
    }
}

void DropTokenManager::stop() {
    _is_stop = true;
    LOG(NOTICE) << "stop DropTokenManager";
    pthread_join(_thread_id, NULL);
    LOG(NOTICE) << "join DropTokenManager";
}

}
}//end of name space.
