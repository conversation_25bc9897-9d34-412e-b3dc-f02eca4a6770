// Copyright (c) 2022 Baidu.com, Inc. All Rights Reserved
//
// Author: <EMAIL>

#pragma once

#include <atomic>
#include <chrono>
#include <deque>
#include <thread>
#include <memory>
#include <sstream>

#include <boost/any.hpp>
#include <baidu/feed/mlarch/babylon/lite/concurrent/bounded_queue.h>

#include "baidu/inf/aries/common/mysql_sync.h"
#include "baidu/inf/aries/master/common.h"
#include "baidu/inf/aries/master/conf.h"
#include "baidu/inf/aries/master/master_control.h"

namespace aries {
namespace master {

enum class MetaDataSyncType {
    META_DATA_NONE = 0,
    META_DATA_VOLUME,
    META_DATA_SPACE,
    META_DATA_NODE,
    META_DATA_DISK,
};

struct MetaDataSyncOp {
    MetaDataSyncType type;
    boost::any value;
    MetaDataSyncOp() {

    }
    MetaDataSyncOp(MetaDataSyncType t, uint64_t v): type(t), value(v){
        
    }
    MetaDataSyncOp(MetaDataSyncType t, uint64_t vid, int disk) : type(t),
        value(std::pair<uint64_t, int>(vid, disk)) {

    }
    MetaDataSyncOp(MetaDataSyncType t, const std::string& v) : type(t), value(v) {

    }
};

using ConcurrentBoundedMysqlSyncQueue = ::baidu::feed::mlarch::babylon::ConcurrentBoundedQueue<MetaDataSyncOp>;
using ConcurrentBoundedEventQueue = ::baidu::feed::mlarch::babylon::ConcurrentBoundedQueue<pb::EventInfoInDB>;

class MysqlSyncScheduler;
extern std::shared_ptr<MysqlSyncScheduler> g_mysql_sync_scheduler;

class MysqlSyncScheduler : public MysqlSync {
public:
    MysqlSyncScheduler() : _is_stop(true) {}
    void init() {
        if (!FLAGS_enable_mysql_sync) {
            return;
        }
        _metadata_queue = std::move(ConcurrentBoundedMysqlSyncQueue(FLAGS_mysql_sync_max_waiting_size + 1000));
        _event_queue = std::move(ConcurrentBoundedEventQueue(FLAGS_mysql_sync_max_waiting_size + 1000));
        MysqlSync::init(FLAGS_mysql_sync_endpoint, 
            FLAGS_mysql_sync_user, FLAGS_mysql_sync_password);
    }
    void check_start(const raft::PeerId& leader_peer) {
        bool is_suitable = g_master_control->is_suitable_for_mysql_sync(leader_peer);
        if (is_suitable && _is_stop && FLAGS_enable_mysql_sync) {
            start();
        }
    }
    void stop() {
        _is_stop = true;
        _metadata_queue.push(MetaDataSyncOp{ MetaDataSyncType::META_DATA_NONE, 0 });
        _event_queue.push(pb::EventInfoInDB());
        if (_thread.joinable()) {
            _thread.join();
        }
        if (_event_thread.joinable()) {
            _event_thread.join();
        }
        _metadata_queue.clear();
        _event_queue.clear();
        LOG(NOTICE) << "stop backup MysqlSyncScheduler succeeded";
    }
    void notice_volume_change(uint64_t volume_id) {
        if (!_is_stop) {
            _metadata_queue.push(MetaDataSyncOp{ MetaDataSyncType::META_DATA_VOLUME, volume_id});
        }
    }
    void notice_node_change(uint64_t node) {
        if (!_is_stop) {
            _metadata_queue.push(MetaDataSyncOp{ MetaDataSyncType::META_DATA_NODE, node});
        }
    }
    void notice_disk_change(uint64_t node, int disk_id) {
        if (!_is_stop) {
            _metadata_queue.push(MetaDataSyncOp{ MetaDataSyncType::META_DATA_DISK, node, disk_id});
        }
    }
    void notice_add_node(uint64_t raft_index, uint64_t timestamp, base::EndPoint node) {
        if (_is_stop) {
            return;
        }
        using IT = ConcurrentBoundedEventQueue::Iterator;
        auto push_func = [&](IT event_info, IT) {
            event_info->set_raft_index(raft_index);
            event_info->set_timestamp(timestamp / 1000000);
            event_info->set_type(EventType_Name(EVENT_TYPE_ADD_NODE));
            std::stringstream ss;
            ss << "node_addr:" << common::endpoint2str(node);
            event_info->set_event(ss.str());
        };
        _event_queue.try_push_n<true, true>(push_func, 1);
    }
    void notice_drop_node(uint64_t raft_index, uint64_t timestamp, base::EndPoint node) {
        if (_is_stop) {
            return;
        }
        using IT = ConcurrentBoundedEventQueue::Iterator;
        auto push_func = [&](IT event_info, IT) {
            event_info->set_raft_index(raft_index);
            event_info->set_timestamp(timestamp / 1000000);
            event_info->set_type(EventType_Name(EVENT_TYPE_DROP_NODE));
            std::stringstream ss;
            ss << "node_addr:" << common::endpoint2str(node);
            event_info->set_event(ss.str());
        };
        _event_queue.try_push_n<true, true>(push_func, 1);
    }
    void notice_add_disk(uint64_t raft_index, uint64_t timestamp, base::EndPoint node, int disk_id) {
        if (_is_stop) {
            return;
        }
        using IT = ConcurrentBoundedEventQueue::Iterator;
        auto push_func = [&](IT event_info, IT) {
            event_info->set_raft_index(raft_index);
            event_info->set_timestamp(timestamp / 1000000);
            event_info->set_type(EventType_Name(EVENT_TYPE_ADD_DISK));
            std::stringstream ss;
            ss << "node_addr:" << common::endpoint2str(node);
            ss << ", disk_id:" << disk_id;
            event_info->set_event(ss.str());
        };
        _event_queue.try_push_n<true, true>(push_func, 1);
    }
    void notice_drop_disk(uint64_t raft_index, uint64_t timestamp, base::EndPoint node, int disk_id) {
        if (_is_stop) {
            return;
        }
        using IT = ConcurrentBoundedEventQueue::Iterator;
        auto push_func = [&](IT event_info, IT) {
            event_info->set_raft_index(raft_index);
            event_info->set_timestamp(timestamp / 1000000);
            event_info->set_type(EventType_Name(EVENT_TYPE_DROP_DISK));
            std::stringstream ss;
            ss << "node_addr:" << common::endpoint2str(node);
            ss << ", disk_id:" << disk_id;
            event_info->set_event(ss.str());
        };
        _event_queue.try_push_n<true, true>(push_func, 1);
    }
    void notice_create_space(uint64_t raft_index, uint64_t timestamp, const pb::SpaceInfo& space_info) {
        if (_is_stop) {
            return;
        }
        using IT = ConcurrentBoundedEventQueue::Iterator;
        auto push_func = [&](IT event_info, IT) {
            event_info->set_raft_index(raft_index);
            event_info->set_timestamp(timestamp / 1000000);
            event_info->set_type(EventType_Name(EVENT_TYPE_CREATE_SPACE));
            std::stringstream ss;
            ss << "space_name:" << space_info.space_name();
            ss << ", proto:" << common::pb2json(space_info);
            event_info->set_event(ss.str());
        };
        _event_queue.try_push_n<true, true>(push_func, 1);
        _metadata_queue.push(MetaDataSyncOp{ MetaDataSyncType::META_DATA_SPACE, space_info.space_name() });
    }
    void notice_drop_space(uint64_t raft_index, uint64_t timestamp, const std::string& space_name) {
        if (_is_stop) {
            return;
        }
        using IT = ConcurrentBoundedEventQueue::Iterator;
        auto push_func = [&](IT event_info, IT) {
            event_info->set_raft_index(raft_index);
            event_info->set_timestamp(timestamp / 1000000);
            event_info->set_type(EventType_Name(EVENT_TYPE_DROP_SPACE));
            std::stringstream ss;
            ss << "space_name:" << space_name;
            event_info->set_event(ss.str());
        };
        _event_queue.try_push_n<true, true>(push_func, 1);
        _metadata_queue.push(MetaDataSyncOp{ MetaDataSyncType::META_DATA_SPACE, space_name });
    }
    void notice_update_space(uint64_t raft_index, uint64_t timestamp, const pb::SpaceInfo& space_info) {
        if (_is_stop) {
            return;
        }
        using IT = ConcurrentBoundedEventQueue::Iterator;
        auto push_func = [&](IT event_info, IT) {
            event_info->set_raft_index(raft_index);
            event_info->set_timestamp(timestamp / 1000000);
            event_info->set_type(EventType_Name(EVENT_TYPE_UPDATE_SPACE));
            std::stringstream ss;
            ss << "space_name:" << space_info.space_name();
            ss << ", proto:" << common::pb2json(space_info);
            event_info->set_event(ss.str());
        };
        _event_queue.try_push_n<true, true>(push_func, 1);
        _metadata_queue.push(MetaDataSyncOp{ MetaDataSyncType::META_DATA_SPACE, space_info.space_name() });
    }
    void notice_create_volume(uint64_t raft_index, uint64_t timestamp, uint64_t vid) {
        if (_is_stop) {
            return;
        }
        using IT = ConcurrentBoundedEventQueue::Iterator;
        auto push_func = [&](IT event_info, IT) {
            event_info->set_raft_index(raft_index);
            event_info->set_timestamp(timestamp / 1000000);
            event_info->set_type(EventType_Name(EVENT_TYPE_CREATE_VOLUME));
            std::stringstream ss;
            ss << "volume_id:" << vid;
            event_info->set_event(ss.str());
        };
        _event_queue.try_push_n<true, true>(push_func, 1);
    }
    void notice_drop_volume(uint64_t raft_index, uint64_t timestamp, uint64_t vid) {
        if (_is_stop) {
            return;
        }
        using IT = ConcurrentBoundedEventQueue::Iterator;
        auto push_func = [&](IT event_info, IT) {
            event_info->set_raft_index(raft_index);
            event_info->set_timestamp(timestamp / 1000000);
            event_info->set_type(EventType_Name(EVENT_TYPE_DROP_VOLUME));
            std::stringstream ss;
            ss << "volume_id:" << vid;
            event_info->set_event(ss.str());
        };
        _event_queue.try_push_n<true, true>(push_func, 1);
    }
    void notice_migrate_vlet(uint64_t raft_index, uint64_t timestamp, const pb::ReportMigrateVletRequest& req) {
        if (_is_stop) {
            return;
        }
        using IT = ConcurrentBoundedEventQueue::Iterator;
        auto push_func = [&](IT event_info, IT) {
            event_info->set_raft_index(raft_index);
            event_info->set_timestamp(timestamp / 1000000);
            event_info->set_type(EventType_Name(EVENT_TYPE_MIGRATE_VLET));
            std::stringstream ss;
            ss << "volume_id:" << req.src_vlet().volume_id();
            ss << ", shard_index:" << req.src_vlet().shard_index();
            ss << ", purpose:" << VletMoveType_Name(req.purpose());
            ss << ", src_node:" << common::endpoint2str(common::int2endpoint(req.src_vlet().node_addr()));
            ss << ", src_disk:" << req.src_vlet().disk_id();
            ss << ", dest_node:" << common::endpoint2str(common::int2endpoint(req.dest_vlet().node_addr()));
            ss << ", dest_disk:" << req.dest_vlet().disk_id();
            event_info->set_event(ss.str());
        };
        _event_queue.try_push_n<true, true>(push_func, 1);
    }
    void notice_migrate_vlet(uint64_t raft_index, uint64_t timestamp, const pb::ReplaceVletRequest& req,
            std::shared_ptr<Disk> src_disk) {
        if (_is_stop) {
            return;
        }
        using IT = ConcurrentBoundedEventQueue::Iterator;
        auto push_func = [&](IT event_info, IT) {
            event_info->set_raft_index(raft_index);
            event_info->set_timestamp(timestamp / 1000000);
            event_info->set_type(EventType_Name(EVENT_TYPE_MIGRATE_VLET));
            std::stringstream ss;
            ss << "volume_id:" << req.dest_vlet().volume_id();
            ss << ", shard_index:" << req.dest_vlet().shard_index();
            ss << ", purpose:REPLACE";
            if (src_disk) {
                ss << ", src_node:" << common::endpoint2str(src_disk->addr());
                ss << ", src_disk:" << src_disk->disk_id();
            }
            ss << ", dest_node:" << common::endpoint2str(common::int2endpoint(req.dest_vlet().node_addr()));
            ss << ", dest_disk:" << req.dest_vlet().disk_id();
            event_info->set_event(ss.str());
        };
        _event_queue.try_push_n<true, true>(push_func, 1);
    }
    void notice_create_vlet(uint64_t raft_index, uint64_t timestamp, std::shared_ptr<Vlet> vlet) {
        if (_is_stop) {
            return;
        }
        using IT = ConcurrentBoundedEventQueue::Iterator;
        auto push_func = [&](IT event_info, IT) {
            event_info->set_raft_index(raft_index);
            event_info->set_timestamp(timestamp / 1000000);
            event_info->set_type(EventType_Name(EVENT_TYPE_CREATE_VLET));
            std::stringstream ss;
            ss << "volume_id:" << vlet->volume_id();
            ss << ", shard_index:" << vlet->shard_index();
            ss << ", state:" << VletState_Name(vlet->state());
            ss << ", node:" << vlet->addr();
            ss << ", disk:" << vlet->disk_id();
            event_info->set_event(ss.str());
        };
        _event_queue.try_push_n<true, true>(push_func, 1);
    }
    void notice_drop_vlet(uint64_t raft_index, uint64_t timestamp, std::shared_ptr<Vlet> vlet) {
        if (_is_stop) {
            return;
        }
        using IT = ConcurrentBoundedEventQueue::Iterator;
        auto push_func = [&](IT event_info, IT) {
            event_info->set_raft_index(raft_index);
            event_info->set_timestamp(timestamp / 1000000);
            event_info->set_type(EventType_Name(EVENT_TYPE_DROP_VLET));
            std::stringstream ss;
            ss << "volume_id:" << vlet->volume_id();
            ss << ", shard_index:" << vlet->shard_index();
            ss << ", reason:" << common::drop_vlet_reason2str(vlet->drop_reason());
            event_info->set_event(ss.str());
        };
        _event_queue.try_push_n<true, true>(push_func, 1);
    }

private:
    void start() {
        if (!FLAGS_enable_mysql_sync) {
            return;
        }
        _is_stop = false;
        _thread = std::thread(&MysqlSyncScheduler::run, this);
        LOG(NOTICE) << "start backup MysqlSyncScheduler succeeded";
    }
    void run();
    void run_event();
    bool anaylze_all_tables();
    void retry_anaylze_all_tables();
    bool sync_metadata(MetaDataSyncOp& sync_op);
    bool sync_event(pb::EventInfoInDB& event_info);
    void reconnect();
    void reconnect_event();
    void retry_full_check_sync();
    void try_purge_expired_event();
    bool full_check_sync();
    bool full_check_sync_volume_vlet();
    bool full_check_sync_space();
    bool full_check_sync_node_disk();
    bool check_event_raft_index();
private:
    std::thread _thread;
    std::thread _event_thread;
    std::atomic<bool> _is_stop;
    ConcurrentBoundedMysqlSyncQueue _metadata_queue;
    ConcurrentBoundedEventQueue _event_queue;
    // metadata sync
    std::shared_ptr<sql::Connection> _conn;
    std::shared_ptr<sql::Statement> _n_stat;
    std::shared_ptr<sql::PreparedStatement> _p_stat_volume;
    std::shared_ptr<sql::PreparedStatement> _p_stat_vlet;
    std::shared_ptr<sql::PreparedStatement> _p_stat_space;
    std::shared_ptr<sql::PreparedStatement> _p_stat_node;
    std::shared_ptr<sql::PreparedStatement> _p_stat_disk;
    // event sync
    std::shared_ptr<sql::Connection> _event_conn;
    std::shared_ptr<sql::Statement> _event_stat;
    std::shared_ptr<sql::PreparedStatement> _p_stat_event;
    // avoid duplicate event
    uint64_t _max_raft_index_in_event_table;
};

}
}
