// token scheduler
// liujian08
#ifndef BAIDU_INF_ARIES_MASTER_SCHEDULER_TOKEN_SCHEDULER_H
#define BAIDU_INF_ARIES_MASTER_SCHEDULER_TOKEN_SCHEDULER_H

#include "baidu/inf/aries/master/meta/meta_data.h"

namespace aries {
namespace master {

class DropTokenManager;
extern DropTokenManager* g_token_manager;

class DropTokenManager {
public:
    DropTokenManager() {}
    //void init() {}
    void start();
    void stop();
    void reload();
    bool get_drop_disk_token(uint64_t node_addr) {
        common::ScopedMutexLock lock(_mutex);
        if (_single_datanode_drop_disk_token.count(node_addr) == 0) {
            _single_datanode_drop_disk_token[node_addr] = FLAGS_max_single_datanode_drop_disk_token;
        }
        if (_drop_disk_token > 0 && _single_datanode_drop_disk_token[node_addr] > 0) {
            --_drop_disk_token;
            --_single_datanode_drop_disk_token[node_addr];
            return true;
        }
        return false;
    }
    bool get_drop_node_token() {
        common::ScopedMutexLock lock(_mutex);
        if (_drop_node_token > 0) {
            --_drop_node_token;
            return true;
        }
        return false;
    }
    bool get_drop_vlet_token() {
        common::ScopedMutexLock lock(_mutex);
        if (_drop_vlet_token > 0) {
            --_drop_vlet_token;
            return true;
        }
        return false;
    }
    bool get_gc_vlet_token() {
        common::ScopedMutexLock lock(_mutex);
        if (_gc_vlet_token > 0) {
            --_gc_vlet_token;
            return true;
        }
        return false;
    }
    bool get_gc_disk_token() {
        common::ScopedMutexLock lock(_mutex);
        if (_gc_disk_token > 0) {
            --_gc_disk_token;
            return true;
        }
        return false;
    }

private:
    static void* thread_proc(void* args);

    void alloc_drop_disk_token();

    void alloc_drop_node_token();

    void alloc_drop_vlet_token();

    void alloc_gc_vlet_token();

    void alloc_gc_disk_token();

    void alloc_single_datanode_drop_disk_token();

    void run();

private:
    bool _is_stop = true;
    pthread_t _thread_id;
    common::MutexLock _mutex;
    uint64_t _drop_disk_token = 0;
    uint64_t _last_alloc_drop_disk_token_time = 0;

    uint64_t _drop_node_token = 0;
    uint64_t _last_alloc_drop_node_token_time = 0;

    uint64_t _drop_vlet_token = 0;
    uint64_t _last_alloc_drop_vlet_token_time = 0;

    uint64_t _gc_vlet_token = 0;
    uint64_t _last_alloc_gc_vlet_token_time = 0;

    uint64_t _gc_disk_token = 0;
    uint64_t _last_alloc_gc_disk_token_time = 0;

    std::map<uint64_t, uint64_t> _single_datanode_drop_disk_token;
    uint64_t _last_alloc_single_datanode_drop_disk_token_time = 0;
};

}
}//end of name space 

#endif
