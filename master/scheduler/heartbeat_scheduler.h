//=============================================================================
// Author: <EMAIL>
//         <EMAIL>
// Date: 2016-10-09 14:30
// Description: Declaration of heartbeat scheduler
//=============================================================================

#ifndef BAIDU_INF_ARIES_MASTER_SCHEDULER_HEARTBEAT_SCHEDULER_H
#define BAIDU_INF_ARIES_MASTER_SCHEDULER_HEARTBEAT_SCHEDULER_H

#include <thread>
#include <atomic>
#include <memory>
#include "baidu/inf/aries/master/common.h"
#include "baidu/inf/aries/master/meta/meta_data.h"
#include "baidu/inf/aries/master/master_control.h"

namespace aries {
namespace master {

class HeartbeatScheduler;
extern std::shared_ptr<HeartbeatScheduler> g_heartbeat_scheduler;

class HeartbeatScheduler {
public:
    HeartbeatScheduler();
    ~HeartbeatScheduler();
    void start();
    void stop();
    void join();

private:
    static void* thread_proc(void* arg);

private:
    void run();
    void check_datanode_heartbeat();
    void check_tinker_heartbeat();
    void check_repairer_heartbeat();
    void check_checkcenter_heartbeat();
    void check_monitorcenter_heartbeat();
    void check_dataagent_heartbeat();
    void check_allocator_heartbeat();
    void check_volumeservice_heartbeat();
    void check_offline_datanode();
    void check_unbalanced_datanode();
    void check_safemode_datanode();
    void check_tape_node_heartbeat();
    void check_tape_center_heartbeat();
    void check_stateservice_heartbeat();

    // Introduce this method for testability.
    virtual MasterControl* get_master_control() {
        return g_master_control;
    }

private:
    std::atomic<bool> _is_stop;
    pthread_t _thread_id;
    uint64_t _last_check_offline_datanode_time = 0;
    uint64_t _last_check_unbalanced_datanode_time = 0;
    uint64_t _last_check_safemode_datanode_time = 0;
};

}
}

#endif
