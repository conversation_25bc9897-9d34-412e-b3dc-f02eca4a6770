/*************************************************************************
 *
 * Copyright (c) Baidu.com, Inc. All Rights Reserved
 *
 ************************************************************************/

/**
 * @file master/scheduler/across_cluster_copy_scheduler.h
 * <AUTHOR>
 * @date Tue Aug 25 16:06:27 CST 2020
 * @brief 
 *
 **/
#pragma once

#include <thread>
#include <atomic>
#include <memory>
#include <unordered_set>
#include "baidu/inf/aries/master/common.h"
#include "baidu/inf/aries/master/meta/meta_data.h"
#include "baidu/inf/aries/master/master_control.h"
#include "baidu/inf/aries/master/scheduler/create_volume_common.h"
#include "baidu/inf/aries/master/scheduler/balance_helper.h"
namespace aries {
namespace master {

class AcrossClusterCopyScheduler;
extern AcrossClusterCopyScheduler* g_across_cluster_copy_scheduler;

struct CopyVletTask {
    int shard_index = 0;
    uint64_t last_run_time = 0;
    uint64_t src_disk_addr = 0;
    uint64_t dst_disk_addr = 0;
    uint32_t vlet_version = 0;
    uint32_t retry_times = 0;
};
typedef std::map<int, std::shared_ptr<CopyVletTask>> CopyVletTaskMap;

struct CopyVolumeTask {
    uint64_t log_id = 0;
    uint64_t volume_id = 0;
    uint64_t start_timestamp = 0;
    int32_t  space_id = 0;
    CopyVletTaskMap running_vlet_task_map;
    CopyVletTaskMap pending_vlet_task_map;
    CopyVletTaskMap finished_vlet_task_map;
};
typedef std::map<uint64_t, std::shared_ptr<CopyVolumeTask>> CopyVolumeTaskMap;

struct CopySpaceDetailInfo {
    aries::pb::CopySpaceOption copy_option;
    std::vector<base::EndPoint> master_addr_list;
    std::string src_space_name;
    int src_space_id = 0;
    int n = 0;
    bool check_ok(const ::aries::pb::UpdateCopyVolumeRequest& request) const {
        if (src_space_name != request.src_space_name() ||
            src_space_id != request.src_space_id() ||
            n != request.n() ||
            copy_option.space_name() != request.dest_space_name() || 
			copy_option.space_id() != request.dest_space_id() ||
			copy_option.token() != request.dest_token()) {
				return false;
        }
		return true;
    }
};

static inline std::ostream& operator<<(std::ostream& os, const CopySpaceDetailInfo& info) {
    return os << "(src_space_name:" << info.src_space_name
        << " src_space_id:" << info.src_space_id
        << " n:" << info.n
        << " dest_space_name:" << info.copy_option.space_name()
        << " dest_space_id:" << info.copy_option.space_id()
		<< " dest_token:" << info.copy_option.token()
        << ")";
};

class AcrossClusterCopyScheduler : public VletBalancer {
public:
    AcrossClusterCopyScheduler() : _is_stop(true), _start_time_long_enough(false) { }
    ~AcrossClusterCopyScheduler() { }
    virtual void report_balance_vlet(const aries::pb::ReportMigrateVletRequest& request);
    virtual void update_timeout_tasks();
    virtual void update_finish_tasks();
    virtual void finish_task(bool succ, uint64_t log_id, VletMoveType move_type, int move_shard_index, 
            uint64_t vid, uint64_t src_disk_addr, uint64_t dst_disk_addr);
    // for src master
    void start();
    void stop();
    void run();
    void clear();
    void check_volumes();
    void do_copy_volumes();
    void check_space_volumes(std::shared_ptr<Space> space);
    bool send_copy_vlet_to_dest_cluster(uint64_t log_id, std::shared_ptr<Vlet> vlet);
    base::Status update_copy_volume_progress(const ::aries::pb::UpdateCopyVolumeRequest& request);
    bool notice_copy_volume_task_create(uint64_t log_id, uint32_t space_id, uint64_t volume_id);
    void finish_copy_space(const std::shared_ptr<Space> space);
    void cancel_copy_space(const std::shared_ptr<Space> space);
    void show_copy_space_progress(const aries::pb::ShowCopySpaceRequest& request,
            aries::pb::ShowCopySpaceResponse* response);
    void show_balance_vlets(const aries::pb::ShowBalanceVletsRequest& request,
        aries::pb::ShowBalanceVletsResponse* response);

    bool reached_generate_task_limit(std::shared_ptr<Volume> volume);
    bool create_pending_task_for_src_cluster(uint64_t log_id, uint32_t space_id, uint64_t volume_id, uint64_t shard_index);

    CopyVletTaskMap::iterator update_pending_vlet_task_to_running(
            CopyVolumeTaskMap::iterator volume_iter, CopyVletTaskMap::iterator vlet_iter);
    bool update_running_vlet_task_to_finished(uint64_t log_id, 
            const uint64_t volume_id, const uint64_t shard_index);
    bool update_running_vlet_task_to_pending(uint64_t log_id,
            const uint64_t volume_id, const uint64_t shard_index);
    bool update_finished_vlet_task_to_pending(uint64_t log_id,
            const uint64_t volume_id, const uint64_t shard_index);
    CopyVolumeTaskMap::iterator clear_src_volume_task(CopyVolumeTaskMap::iterator volume_iter);
    void not_finish_task(uint64_t log_id, uint64_t volume_id, int shard_index);
   
    // for dest master
    void send_copy_volume_progress();
    void set_copy_vlet_info(const aries::pb::ReportMigrateVletRequest& request,
        bool is_sync_progress, aries::pb::UpdateCopyVolumeRequest* update_copy_progress);
    void report_copy_volume_progress(const aries::pb::ReportMigrateVletRequest& request);
    bool create_running_task_for_dest_cluster(uint64_t log_id, uint32_t space_id, uint64_t volume_id, uint64_t shard_index, 
        uint64_t src_disk_addr, uint64_t dst_disk_addr);
    base::Status send_copy_vlet_to_node(const aries::pb::CopyVletRequest& request);
    bool erase_dst_running_vlet_task(uint64_t log_id, uint64_t volume_id, int shard_index);
private:
    uint64_t _src_running_vlet_task_num = 0;
    uint64_t _src_pending_vlet_task_num = 0;
    uint64_t _src_finished_vlet_task_num = 0;
    CopyVolumeTaskMap _src_volume_task_map;
    uint64_t _dst_running_vlet_task_num = 0;
    CopyVolumeTaskMap _dst_volume_task_map;
    std::map<int, CopySpaceDetailInfo> _copy_space_detail_map;
    std::atomic<bool> _is_stop;
    common::MutexLock _src_mutex;
    common::MutexLock _dst_mutex;
    common::MutexLock _mutex_finish_vlets;
    std::thread _worker;
    std::atomic<bool> _start_time_long_enough;
    std::vector<aries::pb::UpdateCopyVolumeRequest> _src_copy_volumes_progress;
    common::PtrSharedPriorityQueue<aries::pb::UpdateCopyVolumeRequest, 1> _dest_copy_volumes_progress;
};

}
}