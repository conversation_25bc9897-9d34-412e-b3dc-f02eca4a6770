//=============================================================================
// Author: <EMAIL>
//         <EMAIL>
// Date: 2016-10-09 14:30
// Description: Impelementation of heartbeat scheduler
//=============================================================================

#include "baidu/inf/aries/master/scheduler/heartbeat_scheduler.h"

#include <unistd.h>
#include <base/time.h>
#include <base/logging.h>
#include "baidu/inf/aries/master/conf.h"
#include "baidu/inf/aries/master/scheduler/balance_scheduler.h"
#include "baidu/inf/aries/common/common.h"

namespace aries {
namespace master {

std::shared_ptr<HeartbeatScheduler> g_heartbeat_scheduler(new HeartbeatScheduler);

HeartbeatScheduler::HeartbeatScheduler() {
    _thread_id = -1;
}

HeartbeatScheduler::~HeartbeatScheduler() {
}

void HeartbeatScheduler::start() {
    pthread_attr_t attr;
    pthread_attr_init(&attr);
    //pthread_attr_setdetachstate(&attr, PTHREAD_CREATE_DETACHED);
    int err = pthread_create(&_thread_id, &attr, thread_proc, (void*)this);
    if (err != 0) {
        LOG(FATAL) << "start heartbeat scheduler " << std::hex << this
                << " failed due to pthread create failed, errno: " << errno;
        abort();
    } else {
        LOG(NOTICE) << "start heartbeat scheduler succeeded with pthread_id: " << _thread_id;
    }
}

void HeartbeatScheduler::stop() {
    LOG(NOTICE) << "stop heartbeat scheduler";
    _is_stop = true;
}

void HeartbeatScheduler::join() {
    LOG(NOTICE) << "join heartbeat scheduler";
    pthread_join(_thread_id, NULL);
}

void* HeartbeatScheduler::thread_proc(void* arg) {
    HeartbeatScheduler* scheduler = static_cast<HeartbeatScheduler*>(arg);
    scheduler->run();
    return NULL;
}

void HeartbeatScheduler::run() {
    LOG(NOTICE) << "run heartbeat scheduler";
    _is_stop = false;
    while (!_is_stop) {
#if !defined(_UNITTEST) && !defined(_UNIT_TEST)
        sleep(60);
#else
        sleep(1);
#endif
        check_tinker_heartbeat();
        check_repairer_heartbeat();
        check_checkcenter_heartbeat();
        check_monitorcenter_heartbeat();
        check_datanode_heartbeat();
        check_offline_datanode();
        check_safemode_datanode();
        check_unbalanced_datanode();
        check_dataagent_heartbeat();
        check_volumeservice_heartbeat();
        check_allocator_heartbeat();
        check_tape_center_heartbeat();
        check_tape_node_heartbeat();
        check_stateservice_heartbeat();
    }
    LOG(NOTICE) << "finish heartbeat scheduler";
}

void HeartbeatScheduler::check_tinker_heartbeat() {
    LOG(TRACE) << "checking tinker heartbeat ... ";
    time_t now = time(NULL);
    int dead_count = 0;

    TinkerVector tinker_list;
    g_tinker_manager->get_tinker_list(&tinker_list);
    for (auto& tinker : tinker_list) {
        if (!tinker->is_alive()) {
            ++dead_count;
            continue;
        }

        if (tinker->last_active_timestamp() + FLAGS_tinker_dead_threshold_in_second
                < (uint64_t)now) {
            tinker->set_alive(false);
            ++dead_count;
            LOG(WARNING) << "tinker is dead,"
                << " tinker_addr:" << common::endpoint2str(tinker->addr())
                << " tinker_name:" << tinker->name();
        }
    }

    if (dead_count > 0) {
        LOG(WARNING) << "tinker heartbeat checking result: "
                << dead_count << " tinkers judged as dead";
    }
}

void HeartbeatScheduler::check_repairer_heartbeat() {
    LOG(TRACE) << "checking repairer heartbeat ... ";
    time_t now = time(NULL);
    int dead_count = 0;

    std::vector<base::EndPoint> need_dropped_repairers;
    RepairerVector repairer_list;
    g_repairer_manager->get_repairer_list(&repairer_list);
    for (auto& repairer : repairer_list) {
        if (repairer->last_active_timestamp() + FLAGS_repairer_dead_threshold_in_second
                < (uint64_t)now) {
            need_dropped_repairers.push_back(repairer->addr());
            ++dead_count;
            LOG(WARNING) << "repairer is dead, repairer_addr:"
                    << common::endpoint2str(repairer->addr());
        }
    }

    for (auto & addr : need_dropped_repairers) {
        g_repairer_manager->drop_repairer(addr);
    }

    if (dead_count > 0) {
        LOG(NOTICE) << "repairer heartbeat checking result: "
                << dead_count << " repairers judged as dead";
    }
}

void HeartbeatScheduler::check_checkcenter_heartbeat() {
    LOG(TRACE) << "checking checkcenter heartbeat ... ";
    time_t now = time(NULL);
    int dead_count = 0;

    std::vector<base::EndPoint> need_dropped_checkcenters;
    CheckcenterVector checkcenter_list;
    g_checkcenter_manager->get_checkcenter_list(&checkcenter_list);
    for (auto& checkcenter : checkcenter_list) {
        if (checkcenter->last_active_timestamp() + FLAGS_checkcenter_dead_threshold_in_second
                < (uint64_t)now) {
            ++dead_count;
            need_dropped_checkcenters.push_back(checkcenter->addr());
            LOG(WARNING) << "checkcenter is dead, checkcenter_addr:"
                    << common::endpoint2str(checkcenter->addr());
        }
    }

    for (auto& addr : need_dropped_checkcenters) {
        g_checkcenter_manager->drop_checkcenter(addr);
    }

    if (dead_count > 0) {
        LOG(NOTICE) << "checkcenter heartbeat checking result: "
                << dead_count << " checkcenters judged as dead";
    }
}

void HeartbeatScheduler::check_monitorcenter_heartbeat() {
    LOG(TRACE) << "checking monitorcenter heartbeat ... ";
    time_t now = time(NULL);
    int dead_count = 0;

    std::vector<base::EndPoint> need_dropped_monitorcenters;
    MonitorcenterVector monitorcenter_list;
    g_monitorcenter_manager->get_monitorcenter_list(&monitorcenter_list);
    for (auto& monitorcenter : monitorcenter_list) {
        if (monitorcenter->last_active_timestamp() + FLAGS_monitorcenter_dead_threshold_in_second
                < (uint64_t)now) {
            need_dropped_monitorcenters.push_back(monitorcenter->addr());
            ++dead_count;
            LOG(WARNING) << "monitorcenter is dead, monitorcenter_addr:"
                    << common::endpoint2str(monitorcenter->addr());
        }
    }

    for (auto & addr : need_dropped_monitorcenters) {
        g_monitorcenter_manager->drop_monitorcenter(addr);
    }

    if (dead_count > 0) {
        LOG(NOTICE) << "monitorcenter heartbeat checking result: "
                << dead_count << " monitorcenters judged as dead";
    }
}

void HeartbeatScheduler::check_offline_datanode() {
    uint64_t now = static_cast<uint64_t>(time(NULL));
    if (_last_check_offline_datanode_time + FLAGS_offline_datanode_check_time_in_second > now) {
        return;
    }
    _last_check_offline_datanode_time = now;

    // check safe mode by az
    AZVector az_list;
    g_az_manager->get_az_list(&az_list);
    for (auto& az : az_list) {
        if (az->is_light_safemode()) {
            LOG(WARNING) << "az is in light safemode, skip drop offline node, az_name:" << az->az_name();
            continue;
        }

        NodeVector node_list;
        az->get_node_list(&node_list);
        for (auto& node : node_list) {
            if (node->is_alive()) {
                continue;
            }
            bool is_need_drop = false;
            bool is_high_density_node = node->total_size() >
                (FLAGS_high_density_node_capacity_threshold_in_tb * aries::common::TB) ? true : false;

            if (node->offline_time() + FLAGS_high_density_deadnode_auto_drop_in_second < now) {
                is_need_drop = true;
            } else if (node->offline_time() + FLAGS_deadnode_auto_drop_in_second < now) {
                if (!is_high_density_node) {
                    is_need_drop = true;
                } else {
                    LOG(WARNING) << "a high desity node has offline for days, need to repairer now,"
                        << " node_addr:" << common::endpoint2str(node->addr())
                        << " last_offline_time:" << node->offline_time();
                }
            }

            if (is_need_drop) {
                LOG(WARNING) << "a dead node has offline too long time, will be auto drop,"
                    << " node_addr:" << common::endpoint2str(node->addr())
                    << " last_offline_time:" << node->offline_time()
                    << " is_high_density_node:" << is_high_density_node;
                get_master_control()->drop_offline_node(node);
            }
        }
    }
}

void HeartbeatScheduler::check_safemode_datanode() {
    uint64_t now = static_cast<uint64_t>(time(NULL));
    if (_last_check_safemode_datanode_time + FLAGS_safemode_datanode_check_time_in_second > now) {
        return;
    }
    _last_check_safemode_datanode_time = now;
    // check all safemode node
    NodeVector node_list;
    g_node_manager->get_node_list(&node_list);
    for (auto& node : node_list) {
        if (!node->is_disk_safemode()) {
            continue;
        }

        // check update time, if more, disable safemode
        if (node->disk_safemode_uptime() + FLAGS_auto_leave_safemode_node_in_second < now) {
                LOG(WARNING) << "a node has in disk safemode too long time, will be auto leave,"
                    << " node_addr:" << common::endpoint2str(node->addr())
                    << " last_safemode_time:" << node->disk_safemode_uptime();
            get_master_control()->disable_expired_node_safemode(node);
        }
    }
}

void HeartbeatScheduler::check_unbalanced_datanode() {
    uint64_t now = static_cast<uint64_t>(time(NULL));
    if (_last_check_unbalanced_datanode_time + FLAGS_unbalanced_datanode_check_time_in_second > now) {
        return;
    }
    _last_check_unbalanced_datanode_time = now;

    std::unordered_map<std::string, double> cached_avg_size_map;
    g_balance_scheduler->get_avg_disk_usage_map(&cached_avg_size_map);
    // check safe mode by az
    AZVector az_list;
    g_az_manager->get_az_list(&az_list);
    for (auto& az : az_list) {
        if (az->is_safemode()) {
            LOG(WARNING) << "az is in safemode, skip check unbalanced node, az_name:" << az->az_name();
            continue;
        }

        NodeVector node_list;
        az->get_node_list(&node_list);
        for (auto& node : node_list) {
            if (node->state() != NODE_STATE_UNBALANCED) {
                continue;
            }

            DiskVector disk_list;
            node->get_disk_list(&disk_list);
            if (disk_list.empty()) {
                continue;
            }

            std::string usage_query_name = node->az_name() + "_" + 
                node->group_name() + "_" + disk_list[0]->disk_type();
            auto it = cached_avg_size_map.find(usage_query_name);
            if (it == cached_avg_size_map.end()) {
                continue;
            }
            double avg_disk_size = it->second;

            if (node->usage() > avg_disk_size * FLAGS_avg_disk_usage_diff_ratio_node_become_balanced) {
                LOG(NOTICE) << "one unbalanced node return to normal, node_addr:" 
                        << common::endpoint2str(node->addr()) 
                        << " node_usage:" << node->usage()
                        << " az_group_disktype:" << usage_query_name
                        << " avg_usage:" << avg_disk_size;
                get_master_control()->set_unbalanced_node_to_normal(node);
            }
        }
    }
}

void HeartbeatScheduler::check_datanode_heartbeat() {
    LOG(TRACE) << "checking DataNode heartbeat ... ";

    uint64_t now = static_cast<uint64_t>(time(NULL));
    int dead_count = 0;
    NodeVector dead_list;
    // check heartbeat by az
    AZVector az_list;
    g_az_manager->get_az_list(&az_list);
    for (auto& az : az_list) {
        int az_dead_count = 0;
        NodeVector node_list;
        NodeVector offline_list;
        NodeVector online_list;
        az->get_node_list(&node_list);
        for (auto& node : node_list) {
            if (node->is_alive() == false) {
                if (node->last_active_timestamp() + FLAGS_datanode_dead_threshold_in_second > now) {
                    online_list.push_back(node);
                } else {
                    ++az_dead_count;
                    dead_list.push_back(node);
                }
            } else {
                if (node->last_active_timestamp() + FLAGS_datanode_dead_threshold_in_second < now) {
                    offline_list.push_back(node);
                    dead_list.push_back(node);
                    ++az_dead_count;
                }
            }
        }
        // check light safemode
        if (az_dead_count >= 
                std::min(FLAGS_az_light_safemode_threshold, FLAGS_az_safemode_threshold)) {
            if (!az->is_light_safemode()) {
                az->enter_light_safemode();
                LOG(WARNING) << "from normal to light safemode, az_name:" << az->az_name();
            }
        } else {
            if (az->is_light_safemode()) {
                az->leave_light_safemode();
                LOG(WARNING) << "from light safemode to normal, az_name:" << az->az_name();
            }
        }
        // check safemode
        if (az_dead_count >= FLAGS_az_safemode_threshold) {
            if (!az->is_safemode()) {
                az->enter_safemode();
                LOG(WARNING) << "from normal to safemode, az_name:" << az->az_name();
            }
        } else {
            if (az->is_safemode()) {
                az->leave_safemode();
                LOG(WARNING) << "from safemode to normal, az_name:" << az->az_name();
            }
        }

        dead_count += az_dead_count;
        for (auto& node : online_list) {
            get_master_control()->set_node_online(node);
            LOG(NOTICE) << "node is re-live, node_addr:" << common::endpoint2str(node->addr());
        }
        if (!az->is_safemode() ||  g_az_manager->disabled_az_name() == az->az_name()) {
            for (auto& node : offline_list) {
                get_master_control()->set_node_offline(node);
                bool is_high_density_node = node->total_size() >
                        (FLAGS_high_density_node_capacity_threshold_in_tb * aries::common::TB) ? true : false;

                LOG(WARNING) << "node is dead, node_addr:" << common::endpoint2str(node->addr())
                        << " is_high_density_node:" << is_high_density_node;
            }
        }
    }

    if (dead_count > 0) {
        LOG(WARNING) << "node heartbeat checking result: "
                << dead_count << " nodes judged as dead as follows.";
        for (auto & node : dead_list) {
            LOG(WARNING) << "dead node, node_addr:" << common::endpoint2str(node->addr());
        }
    }
}

void HeartbeatScheduler::check_volumeservice_heartbeat() {
    LOG(TRACE) << "checking volume_service heartbeat ... ";
    time_t now = time(NULL);
    int dead_count = 0;

    std::vector<base::EndPoint> need_dropped_volume_services;
    VolumeServiceVector volume_service_list;
    g_volume_service_manager->get_volume_service_list(&volume_service_list);
    for (auto& volume_service : volume_service_list) {
        if (volume_service->last_active_timestamp() + FLAGS_volume_service_dead_threshold_in_second
                < (uint64_t)now) {
            need_dropped_volume_services.push_back(volume_service->addr());
            ++dead_count;
            LOG(WARNING) << "volume_service is dead, volume_service_addr:"
                    << common::endpoint2str(volume_service->addr());
        }
    }

    for (auto & addr : need_dropped_volume_services) {
        g_volume_service_manager->drop_volume_service(addr);
    }

    if (dead_count > 0) {
        LOG(NOTICE) << "volume_service heartbeat checking result: "
                << dead_count << " volume_services judged as dead";
    }
}

void HeartbeatScheduler::check_dataagent_heartbeat() {
    LOG(TRACE) << "checking dataagent heartbeat ... ";
    time_t now = time(NULL);
    int dead_count = 0;

    g_dataagent_manager->lock();
    std::vector<std::DataAgentIdentify> need_dropped_dataagents;
    DataAgentMap& agents = g_dataagent_manager->get_dataagent_map();
    for (auto & pair : agents) {
        auto dataagent = pair.second;
        if (dataagent->last_active_timestamp() + FLAGS_dataagent_dead_threshold_in_second
                < (uint64_t)now) {
            need_dropped_dataagents.push_back(dataagent->dataagent_identify());
            ++dead_count;
            LOG(WARNING) << "dataagent is dead, dataagent_addr:"
                    << common::endpoint2str(dataagent->addr()) << " proxy_name:" << dataagent->name();
        }
    }
    g_dataagent_manager->unlock();

    for (auto & id: need_dropped_dataagents) {
        g_dataagent_manager->drop_dataagent(id);
    }

    if (dead_count > 0) {
        LOG(NOTICE) << "dataagent heartbeat checking result: "
                << dead_count << " dataagents judged as dead";
    }
}

void HeartbeatScheduler::check_allocator_heartbeat() {
    LOG(TRACE) << "checking allocator heartbeat ... ";
    time_t now = time(NULL);
    int dead_count = 0;

    g_allocator_manager->lock();
    AllocatorMap& allocators = g_allocator_manager->get_allocator_map();
    for (auto & pair : allocators) {
        auto allocator = pair.second;
        if (allocator->last_active_timestamp() + FLAGS_allocator_dead_threshold_in_second
                < (uint64_t)now) {
            allocator->set_alive(false);
            ++dead_count;
            LOG(WARNING) << "allocator is dead,"
                    << " allocator_addr:" << common::endpoint2str(allocator->addr())
                    << " allocator_name:" << allocator->name()
                    << " sequence_id:" << allocator->get_sequence_id();
        }
    }
    g_allocator_manager->unlock();

    if (dead_count > 0) {
        LOG(WARNING) << "allocator heartbeat checking result: "
                << dead_count << " allocators judged as dead";
    }
}

void HeartbeatScheduler::check_tape_center_heartbeat() {
    LOG(TRACE) << "checking tapecenter heartbeat ...";
    uint64_t now = base::gettimeofday_s();
    int dead_count = 0;

    TapeCenterVector tape_centers;
    g_tape_center_manager->get_tape_center_list(&tape_centers);
    for (auto& tape_center : tape_centers) {
        if (tape_center->last_active_timestamp() + FLAGS_tape_center_dead_threshold_in_second < now) {
            tape_center->set_alive(false);
            dead_count++;
            LOG(WARNING) << "tapecenter is dead,"
                    << " tapecenter_addr:" << common::endpoint2str(tape_center->addr())
                    << " tapecenter_name:" << tape_center->name();
        }
    }

    if (dead_count > 0) {
        LOG(WARNING) << "tapecenter heartbeat checking result: "
                << dead_count << " tapecenters judged as dead";
    }
}

void HeartbeatScheduler::check_tape_node_heartbeat() {
    LOG(TRACE) << "checking tapenode heartbeat ...";
    uint64_t now = base::gettimeofday_s();
    int dead_count = 0;

    TapeNodeVector tape_nodes;
    g_tape_node_manager->get_tape_node_list(&tape_nodes);
    for (auto& tape_node : tape_nodes) {
        if (tape_node->last_active_timestamp() + FLAGS_tape_node_dead_threshold_in_second < now) {
            if (tape_node->is_alive()) {
                tape_node->set_alive(false);
                tape_node->set_offline_time(base::gettimeofday_s());
            }
            dead_count++;
            LOG(WARNING) << "tapenode is dead,"
                    << " tapenode_addr:" << common::endpoint2str(tape_node->addr());
        }
    }

    if (dead_count > 0) {
        LOG(WARNING) << "tapenode heartbeat checking result: "
                << dead_count << " tapenodes judged as dead";
    }
}

void HeartbeatScheduler::check_stateservice_heartbeat() {
    LOG(TRACE) << "checking stateservice heartbeat ... ";
    time_t now = time(NULL);
    int dead_count = 0;

    std::vector<std::pair<base::EndPoint, uint64_t>> need_dropped_stateservices;
    StateServiceVector stateservice_list;
    g_stateservice_manager->get_stateservice_list(&stateservice_list);
    for (auto& stateservice : stateservice_list) {
        if (stateservice->last_active_timestamp() + FLAGS_stateservice_dead_threshold_in_second
                < (uint64_t)now) {
            need_dropped_stateservices.push_back(std::make_pair(
                stateservice->addr(), stateservice->group_id()));
            ++dead_count;
            LOG(WARNING) << "stateservice is dead, stateservice addr:"
                    << common::endpoint2str(stateservice->addr())
                    << ", group_id:" << stateservice->group_id();
        }
    }

    for (auto& pair : need_dropped_stateservices) {
        g_stateservice_manager->drop_stateservice(pair.first, pair.second);
    }

    if (dead_count > 0) {
        LOG(NOTICE) << "stateservice heartbeat checking result: "
                << dead_count << " stateservices judged as dead";
    }
}
}
}
