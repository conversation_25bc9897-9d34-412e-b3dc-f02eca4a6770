// Copyright (c) 2021 Baidu.com, Inc. All Rights Reserved
//
// Author: chenz<PERSON><EMAIL>

#pragma once

#include <thread>
#include <atomic>
#include <memory>
#include "bcesdk/bos/client.h"
#include "baidu/inf/aries/master/common.h"
#include "baidu/inf/aries/master/meta/meta_data.h"
#include "baidu/inf/aries/master/master_control.h"
#include "baidu/inf/aries/master/monitor/master_monitor.h"

namespace aries {
namespace master {

class BackupMetaDataScheduler;
extern BackupMetaDataScheduler* g_backup_metadata_scheduler;

class BackupMetaDataScheduler {
public:
    BackupMetaDataScheduler() : _is_stop(true), _is_ready(false) {}
    ~BackupMetaDataScheduler() {}
    void start();
    void stop();
    void check_start(const raft::PeerId& leader_peer);
private:
    struct SingleMetaDataFile {
        std::string entry_name;
        std::string canonical_path;
        uint64_t size = 0;
    };
    void run();
    bool check_and_create_bucket();
    bool scan_local_files(std::vector<SingleMetaDataFile>& files);
    bool check_if_file_existed_remotely(std::vector<SingleMetaDataFile>& files,
        std::vector<std::string>& old_files);
    bool upload_files(std::vector<SingleMetaDataFile>& files);
    void remove_old_files(std::vector<std::string>& old_files);
    bool check_ready();
private:
    std::atomic<bool> _is_stop;
    std::atomic<bool> _is_ready;
    std::thread _thread;
    uint64_t _last_backup_time = 0;
    std::unique_ptr<baidu::bos::cppsdk::Client> _client;
    std::string _metadata_path;

};

}
}
