//=============================================================================
// Author: <EMAIL>
// Date: 2017-12-15 14:30
// Description: Declaration of balance scheduler
//=============================================================================

#ifndef BAIDU_INF_ARIES_MASTER_SCHEDULER_BALANCE_SCHEDULER_H
#define BAIDU_INF_ARIES_MASTER_SCHEDULER_BALANCE_SCHEDULER_H

#include <thread>
#include <atomic>
#include <memory>
#include <map>
#include <unordered_set>
#include <unordered_map>
#include <functional>
#include "baidu/inf/aries-api/common/lock.h"
#include "baidu/inf/aries/master/common.h"
#include "baidu/inf/aries/master/meta/meta_data.h"
#include "baidu/inf/aries/master/master_control.h"
#include "baidu/inf/aries/master/monitor/master_monitor.h"
#include "baidu/inf/aries/master/scheduler/balance_helper.h"

namespace aries {
namespace master {

static const int DISK_USAGE_BALANCE_PRIORITY_LEVEL_NUM = 10;
static const int DISK_AZ_USAGE_BALANCE_PRIORITY_LEVEL_NUM = 10;
static const double DISK_SAFE_USAGE_PERCENT = 0.8;

class BalanceScheduler;
extern BalanceScheduler* g_balance_scheduler;

class BalanceScheduler : public VletBalancer {
public:
    BalanceScheduler() : _is_stopped(true) {}
    ARIES_VIRTUAL ~BalanceScheduler() {}
    virtual void report_balance_vlet(const aries::pb::ReportMigrateVletRequest& request);
    virtual void update_timeout_tasks();
    virtual void finish_task(bool succ, uint64_t log_id, VletMoveType move_type, int move_shard_index, 
            uint64_t vid, uint64_t src_disk_addr, uint64_t dst_disk_addr);
    void start();
    void stop();
    bool is_running_manual_balance();
    void cancel_manual_balance();
    void init_manual_balance(const aries::pb::AddManualBalanceRequest& request);
    bool is_running_az_usage_balance();
    void cancel_az_usage_balance();
    void init_az_usage_balance(const aries::pb::AddAZUsageBalanceRequest& request);
    void reset_az_usage_balance(const aries::pb::UpdateAZUsageBalanceRequest& request); 
    void show_rule_balance(const aries::pb::ShowRuleBalanceRequest& request,
            aries::pb::ShowRuleBalanceResponse* response);
    void show_decommission_balance(const aries::pb::ShowDecommissionBalanceRequest& request,
            aries::pb::ShowDecommissionBalanceResponse* response);
    void show_compact_balance(const aries::pb::ShowCompactBalanceRequest& request,
            aries::pb::ShowCompactBalanceResponse* response);
    void show_manual_balance(const aries::pb::ShowManualBalanceRequest& request,
            aries::pb::ShowManualBalanceResponse* response);
    void show_az_usage_balance(const aries::pb::ShowAZUsageBalanceRequest& request,
            aries::pb::ShowAZUsageBalanceResponse* response);
    void show_usage_balance(const aries::pb::ShowUsageBalanceRequest& request,
            aries::pb::ShowUsageBalanceResponse* response);
    void show_balance_vlets(const aries::pb::ShowBalanceVletsRequest& request,
            aries::pb::ShowBalanceVletsResponse* response);
    bool calculate_compact_size(uint64_t volume_id, uint64_t* compact_size, uint64_t* free_size);
    // for check_unbalanced_datanode
    // key={az_name}_{group_name}_{disk_type}
    void get_avg_disk_usage_map(std::unordered_map<std::string, double>* map);
private:
    enum BalanceRet {
        CREATE_TASK_SUCC = 0,
        CREATE_TASK_FAIL = 1,   // keep task in queue, need retry
        NEED_NOT_BALANCE = 2,   // earse task from queue, need not retry
    };
    static std::string balanceret2str(BalanceRet ret) {
        switch (ret) {
            case CREATE_TASK_SUCC: {
                return "CREATE_TASK_SUCC";
            }
            case CREATE_TASK_FAIL: {
                return "CREATE_TASK_FAIL";
            }
            case NEED_NOT_BALANCE: {
                return "NEED_NOT_BALANCE";
            }
            default: {
                return "unknown";
            }
        }
    }
    enum SelectDiskMethod {
        RANDOM_USAGE = 0,
        RANDOM_USAGE_DISTANCE = 1,
        RANDOM_USAGE_DISTANCE_XY = 2,
    };
    struct TaskBase {
        uint64_t cur_src_disk_addr = 0;
        uint64_t cur_dst_disk_addr = 0;
        uint64_t cur_volume_id = 0;
        uint32_t cur_shard_index = 0;

        uint64_t create_time = 0;
        uint64_t last_run_time = 0;
        uint64_t task_log_id = 0;
        uint64_t finish_vlet_num = 0;
        uint64_t volume_id = 0;
    };
    struct RuleTask : public TaskBase {
        std::string dst_az_name = ""; // use to balance cross az when PickAZPolicy = PAZP_UNIT_IN_AZ.
    };
    struct DecommissionTask : public TaskBase {
        uint64_t disk_addr = 0;
    };
    struct ManualTask : public TaskBase {
        uint64_t disk_addr = 0;
        ManualBalancePolicy manual_balance_policy = DISK_POLICY;
    };

    struct AZUsageTask : public TaskBase {
        std::string dst_az_name = "";
        std::string src_az_name = "";
        uint64_t failed_times = 0;
        int priority = 0;
    };

    struct UsageTask : public TaskBase {
        uint64_t disk_addr = 0;
        uint64_t failed_times = 0;
        int priority = 0;
        bool is_overflow = true;
    };
    struct CompactTask : public TaskBase {
        uint64_t balance_vlet_size = 0;
        std::set<int> finish_vlet_set;
        std::set<int> running_vlet_set;
    };

    struct CompactVolumeContext {
        uint64_t volume_id = 0;
        uint64_t compact_vlet_size = 0;
        VolumeState volume_state;
        CompactProgress compact_progress;
    };

    void run();
    void run_check();
    void clear();
    void set_usage_balance_disk_info(std::shared_ptr<UsageTask> task,
            aries::pb::ShowUsageBalanceResponse* response,
            bool is_overflow, bool is_running);
    void set_az_usage_balance_volume_info(std::shared_ptr<AZUsageTask> task,
            aries::pb::ShowAZUsageBalanceResponse* response, bool is_running);
    void set_manual_balance_disk_info(std::shared_ptr<ManualTask> task,
            aries::pb::ShowManualBalanceResponse* response, bool is_running);
    void set_manual_balance_vlet_info(std::shared_ptr<ManualTask> task,
            aries::pb::ShowManualBalanceResponse* response, bool is_running, bool is_finish);

    //------------------------------------------------//
    //-----------------update data--------------------//
    //------------------------------------------------//
    void update_finish_tasks();
    void finish_balance(const aries::pb::ReportMigrateVletRequest& request);
    void update_average_disk_usage();
    void update_average_disk_usage_az(std::shared_ptr<AZ> az);
    void update_disk_vlet_num();
    void update_az_disk_usage();
    void update_az_usage_balance_info();
    void update_compact_balance_process();
    void load_compact_balance_task(uint64_t volume_id);
    bool check_compact_task_finish(uint64_t volume_id);

    //------------------------------------------------//
    //------------find new balance task---------------//
    //------------------------------------------------//
    void check_rule_balance();
    bool check_volume_rule(uint64_t volume_id);
    void check_decommission_balance();
    void check_manual_balance();
    void check_az_usage_balance();
    void check_usage_balance();
    void check_usage_balance_az(std::shared_ptr<AZ> az);
    void check_compact_balance();
    bool check_volume_on_compact_balance(uint64_t volume_id, const std::set<uint64_t>& running_vids);


    //------------------------------------------------//
    //------try balance next vlet for every task------//
    //------------------------------------------------//
    void do_rule_balance();
    BalanceRet try_volume_rule_balance(std::shared_ptr<RuleTask> task);
    bool try_rule_balance_vlet(std::shared_ptr<Vlet> vlet, std::shared_ptr<RuleTask> task);

    void do_decommission_balance();
    BalanceRet do_disk_decommission_balance(std::shared_ptr<DecommissionTask> task);

    void do_manual_balance();
    BalanceRet do_disk_manual_balance(std::shared_ptr<ManualTask> task);
    BalanceRet do_volume_manual_balance(std::shared_ptr<ManualTask> task);
    bool check_vlet_on_manual_balance(uint64_t volume_id, int shard_index, uint64_t* src_disk_addr);
    ARIES_VIRTUAL bool try_manual_balance_vlet(std::shared_ptr<Vlet> vlet, std::shared_ptr<ManualTask> task);

    void do_az_usage_balance();
    BalanceRet do_volume_az_usage_balance(std::shared_ptr<AZUsageTask> task);
    ARIES_VIRTUAL bool try_az_usage_balance_vlet(std::shared_ptr<Vlet> vlet, std::shared_ptr<AZUsageTask> task);

    void do_usage_balance();
    BalanceRet do_disk_usage_balance(std::shared_ptr<UsageTask> task, bool is_overflow);

    void do_compact_balance();
    BalanceRet try_volume_compact_balance(std::shared_ptr<CompactTask> task);
    ARIES_VIRTUAL bool try_compact_balance_vlet(std::shared_ptr<Vlet> vlet, std::shared_ptr<CompactTask> task);

    //------------------------------------------------//
    //---------------------base-----------------------//
    //------------------------------------------------//
    BalanceRet do_balance_from_disk(std::shared_ptr<Disk> disk, VletMoveType move_type,
            std::shared_ptr<TaskBase> task, bool need_retry_ignore_ave_usage);
    BalanceRet do_balance_to_disk(std::shared_ptr<Disk> disk, VletMoveType move_type,
            std::shared_ptr<TaskBase> task, bool need_retry_ignore_ave_usage);
    std::shared_ptr<Disk> choose_src_disk(
            std::shared_ptr<Disk> dst_disk, bool need_retry_ignore_ave_usage);
    uint64_t random_choose_disk(
            const std::vector<uint64_t>& disk_list, double average_disk_usage,
            bool is_src_disk, bool need_retry_ignore_ave_usage,
            std::string best_idc_name, std::string best_rack_name, uint64_t vlet_size);
    uint64_t random_choose_disk_by_usage(const std::vector<uint64_t>& disk_list,
            double average_disk_usage, bool is_src_disk, uint64_t vlet_size);
    bool choose_az_for_volume(const uint64_t volume_id,
            const std::set<std::string>& src_az_list, const std::set<std::string>& dst_az_list,
            std::string* src_az, std::string* dst_az);

    bool send_update_volume_compact_progress_rpc(std::shared_ptr<CompactVolumeContext> context);

    int get_vlet_num_in_az(const uint64_t volume_id, const std::string az_name, uint32_t* vlet_num);
    void set_az_usage_balance_task_priority(std::shared_ptr<AZUsageTask> task);

    void set_usage_balance_task_priority(std::shared_ptr<UsageTask> task);

    void rule_balance_routine();
    void usage_balance_routine();
    void decommission_balance_routine();
    void manual_balance_routine();
    void compact_balance_routine();
    void az_usage_balance_routine();

    bool can_do_balance();
    bool get_avg_disk_usage(const std::string& key, double* avg_disk_usage);

private:
    struct DiskWeight {
        uint64_t disk_addr;
        double usage_weight;
        uint64_t distance_weight;

        static bool usage_weight_greater(const DiskWeight& a, const DiskWeight& b) {
            return a.usage_weight < b.usage_weight;
        }
        static bool distance_weight_greater(const DiskWeight& a, const DiskWeight& b) {
            return a.distance_weight < b.distance_weight;
        }
    };

    struct AZUsage {
        uint64_t az_total_size = 0;
        uint64_t az_used_size = 0;
        double az_used_percent = 0.0;
    };

    std::atomic<bool> _is_stopped;
    std::thread _run_thread;
    std::thread _check_thread;
    std::thread _rule_balance_thread;
    std::thread _usage_balance_thread;
    std::thread _decommission_balance_thread;
    std::thread _manual_balance_thread;
    std::thread _compact_balance_thread;
    std::thread _az_usage_balance_thread;

    std::unordered_map<uint64_t, std::shared_ptr<RuleTask> > _waiting_rule_tasks;
    std::unordered_map<uint64_t, std::shared_ptr<RuleTask> > _running_rule_tasks;
    std::unordered_multimap<uint64_t, std::shared_ptr<DecommissionTask> > _waiting_decommission_tasks;
    std::unordered_multimap<uint64_t, std::shared_ptr<DecommissionTask> > _running_decommission_tasks;
    std::unordered_map<uint64_t, std::shared_ptr<ManualTask> > _waiting_manual_tasks;
    std::unordered_map<uint64_t, std::shared_ptr<ManualTask> > _running_manual_tasks;
    std::unordered_multimap<uint64_t, std::shared_ptr<ManualTask> > _finish_manual_tasks;
    common::PtrSharedPriorityQueue<AZUsageTask, DISK_AZ_USAGE_BALANCE_PRIORITY_LEVEL_NUM,
            true, uint64_t> _waiting_az_usage_tasks;
    std::unordered_map<uint64_t, std::shared_ptr<AZUsageTask> > _running_az_usage_tasks;
    common::PtrSharedPriorityQueue<UsageTask, DISK_USAGE_BALANCE_PRIORITY_LEVEL_NUM,
            true, uint64_t> _waiting_usage_tasks;
    std::unordered_map<uint64_t, std::shared_ptr<UsageTask> > _running_usage_tasks;
    std::unordered_map<uint64_t, std::shared_ptr<CompactTask> > _waiting_compact_tasks;
    std::unordered_map<uint64_t, std::shared_ptr<CompactTask> > _running_compact_tasks;

    std::unordered_map<std::string, double> _average_disk_usage_map;
    std::unordered_map<std::string, AZUsage> _az_usage_map;
    std::unordered_map<uint64_t, uint64_t> _src_disk_vlet_num_map;

    struct ManualBalance {
        std::string space_name;
        std::set<std::string> src_az_set;
        std::set<std::string> src_group_set;
        std::set<std::string> src_disk_type_set;
        std::unordered_set<uint64_t> src_node_set;
        std::unordered_set<uint64_t> src_disk_set;
        std::vector<std::pair<uint64_t, int>> src_vlet_list;
        std::set<std::string> dest_az_set;
        std::set<std::string> dest_group_set;
        std::set<std::string> dest_disk_type_set;
        std::unordered_set<uint64_t> dest_node_set;
        std::unordered_set<uint64_t> dest_disk_set;
        uint64_t average_disk_vlet_num_after_balance;
        uint64_t total_balance_vlet_num;
        uint64_t finish_balance_vlet_num;
        uint64_t src_vlet_num_in_space;
        uint64_t src_disk_num_in_space;
        uint64_t max_running_task_num;
        uint64_t max_waitting_task_num;
        uint64_t balance_shard_index;
        uint64_t manual_balance_policy;
        aries::pb::AddManualBalanceRequest manual_request;
    };
    ManualBalance _manual_balance;

    struct AZUsageBalance {
        std::set<std::string> space_set;
        std::set<std::string> az_set;
        std::set<std::string> src_az_set;
        std::set<std::string> dest_az_set;
        double avg_az_usage;
        bool is_initialized;
        bool is_first_check;
    };
    AZUsageBalance _az_usage_balance;

    std::vector<aries::pb::ReportMigrateVletRequest> _report_balance_vlets;

    std::unordered_map<uint32_t, uint64_t> _compact_free_space_size_map;
    std::unordered_map<uint64_t, uint64_t> _readonly_volume_size_map;

    common::MutexLock _mutex;
    common::MutexLock _mutex_finish_vlets;
    common::MutexLock _avg_disk_usage_mutex;
    common::MutexLock _rule_balance_mutex;
    common::MutexLock _usage_balance_mutex;
    common::MutexLock _decommission_balance_mutex;
    common::MutexLock _manual_balance_mutex;
    common::MutexLock _compact_balance_mutex;
    common::MutexLock _az_usage_balance_mutex;
    base::Timer _timer;
    uint64_t _last_update_finish_tasks_timestamp;
};

}
}

#endif
