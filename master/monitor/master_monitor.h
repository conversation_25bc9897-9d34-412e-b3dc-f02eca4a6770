
#ifndef ARIES_MASTER_MONITOR_MASTER_MONITOR_H
#define ARIES_MASTER_MONITOR_MASTER_MONITOR_H

#include <boost/scoped_ptr.hpp>
#include "baidu/inf/aries/master/meta/meta_data.h"
#include "baidu/inf/aries/master/monitor/master_monitor_metrics.h"
#include "baidu/inf/aries-api/common/proto/allocator.pb.h"
#include "baidu/inf/aries-api/common/closure.h"
#include "baidu/inf/aries-api/common/lock.h"

namespace aries {
namespace master {

/**
 * 计算活跃vlet数量
 * 只统计状态为VLET_STATE_NORMAL或VLET_STATE_REPAIRING的vlet
 */
static inline uint64_t calc_num_of_active_vlets(VletVector& vlet_vector) {
    uint64_t num_of_active_vlets = vlet_vector.size();
    for (std::shared_ptr<Vlet> vlet : vlet_vector) {
        VletState state = vlet->state();
        // 只有状态为NORMAL或REPAIRING的vlet才算活跃
        if (!(state == VLET_STATE_NORMAL || state == VLET_STATE_REPAIRING)) {
            --num_of_active_vlets;
        }
    }
    return num_of_active_vlets;
}

/**
 * 计算正常vlet数量
 * 只统计状态为VLET_STATE_NORMAL的vlet
 */
static inline uint64_t calc_num_of_normal_vlets(VletVector& vlet_vector) {
    uint64_t num_of_normal_vlets = vlet_vector.size();
    for (std::shared_ptr<Vlet> vlet : vlet_vector) {
        VletState state = vlet->state();
        // 只有状态为NORMAL的vlet才算正常
        if (!(state == VLET_STATE_NORMAL)) {
            --num_of_normal_vlets;
        }
    }
    return num_of_normal_vlets;
}

/**
 * MasterMonitor类用于采集和管理集群各类监控指标
 * 设计说明：该监控类会持有全局锁，可能带来系统额外开销，但在当前master架构下是折中方案。
 * 建议后续重构volume和instance结构，移除该监控类。
 *
 * 核心逻辑总结：
 * - 负责采集volume、服务、数据节点、容量、空闲空间、磁盘使用、IO等多种监控指标
 * - 提供多种查询接口，支持按空间、服务名、AZ等维度获取指标
 * - 采集操作均加锁，保证线程安全
 * - 支持将监控数据序列化为json或protobuf格式输出
 * - 维护各类指标的map，定期采集并更新
 */
class MasterMonitor {
public:
    MasterMonitor();
    virtual ~MasterMonitor();

    // 启动/停止监控线程
    virtual bool start();
    virtual bool stop();
    virtual bool is_running();

    // 获取各类监控指标
    virtual void get_volumes_metrics(std::vector<std::shared_ptr<VolumesMetrics>>&);
    virtual void get_services_metrics(std::vector<std::shared_ptr<ServicesMetrics>>&);
    virtual void get_datanodes_metrics(std::vector<std::shared_ptr<DataNodesMetrics>>&);
    virtual void get_capacity_metrics(std::vector<std::shared_ptr<CapacityMetrics>>&);
    virtual void get_freespace_metrics(std::vector<std::shared_ptr<FreeSpaceMetrics>>&);
    virtual void get_total_freespace_metrics(std::shared_ptr<TotalFreeSpaceMetrics>&);
    virtual void get_burn_rate_metrics(std::vector<std::shared_ptr<BurnRateMetrics>>&);

    // 按名称/空间等维度获取单个指标
    virtual std::shared_ptr<ServicesMetrics> get_services_metrics_by_service_name(std::string);
    virtual std::shared_ptr<DataNodesMetrics> get_datanodes_metrics_by_az_name(std::string);
    virtual std::shared_ptr<VolumesMetrics> get_volumes_metrics_by_space_name(std::string);
    virtual std::shared_ptr<CapacityMetrics> get_capacity_metrics_by_az_name(std::string);
    virtual std::shared_ptr<FreeSpaceMetrics> get_freespace_metrics_by_space_name(std::string);
    virtual std::shared_ptr<BurnRateMetrics> get_burn_rate_metrics_by_space_name(std::string);

    // 获取磁盘使用率，支持json和protobuf格式
    void get_disk_usage(const std::string& format, Json::Value& json_value) {
        common::ScopedMutexLock lock(_mutex);
        _disk_usage_metric.usage2json(format, json_value);
    }
    void get_disk_usage(const ::aries::pb::ListDiskUsageRequest* request,
            aries::pb::ClusterDiskUsageInfo* usage_info) {
        common::ScopedMutexLock lock(_mutex);
        _disk_usage_metric.usage2pb(request, usage_info);
    }

    // 获取磁盘IO统计信息
    void get_disk_io_stats(const std::string& format, Json::Value& json_value) {
        common::ScopedMutexLock lock(_mutex);
        _disk_io_stats_metric.io_stats2json(format, json_value);
    }
    void get_disk_io_stats(const ::aries::pb::ListDiskIoStatsRequest* request,
            aries::pb::ListDiskIoStatsResponse* response) {
        common::ScopedMutexLock lock(_mutex);
        _disk_io_stats_metric.io_stats2pb(request, response);
    }

    // 获取空间使用情况
    void get_space_usage(const ::aries::pb::ShowSpaceRequest* request,
            aries::pb::ShowSpaceUsageResponse* response) {
        common::ScopedMutexLock lock(_mutex);
        if (_freespace_metrics.count(request->space_name()) == 0) {
            response->mutable_status()->set_code(AIE_NOT_EXIST);
            response->mutable_status()->set_msg("not exist");
        } else {
            response->mutable_status()->set_code(AIE_OK);
            response->mutable_status()->set_msg("ok");
            _freespace_metrics[request->space_name()]->metrics2pb(response);
        }
    }

    // 指标数据序列化为json
    void metrics2json(Json::Value& json_value, const VolumesMetrics& metrics, 
        const std::string request_type = "");
    void vlets_metrics2json(Json::Value& json_value);

    // 从allocator获取空间容量信息
    virtual std::shared_ptr<aries::pb::Allocator_CapacityInfo> retrieve_from_allocators_by_space(std::shared_ptr<Space>);

    // 输出空闲空间指标
    virtual void get_freespace_metrics_output(const bool use_html, base::IOBufBuilder& os);

public:
    // 常量定义
    const std::string TINKER = "tinker";
    const std::string ALLOCATOR = "allocator";
    const std::string DATA_AGENT = "dataagent";
    const std::string VOLUME_SERVICE = "volumeservice";
    const uint64_t KB = 1024;
    const uint64_t MB = 1024 * KB;
    const uint64_t GB = 1024 * MB;
    const uint64_t TB = 1024 * GB;

private:
    // 采集各类监控指标的内部方法
    virtual void collect_volumes_metrics();
    virtual void collect_volumes_metrics_by_space(std::shared_ptr<Space>, std::shared_ptr<VolumesMetrics>);
    virtual void collect_volumes_metrics_by_volume(std::shared_ptr<Volume>, std::shared_ptr<VolumesMetrics>);
    virtual void collect_instance_metrics();
    // 以下五个函数不应在本类中单独调用
    virtual void collect_datanodes_metrics_by_az(std::shared_ptr<AZ>, std::shared_ptr<DataNodesMetrics>);
    virtual void collect_tinkers_metrics();
    virtual void collect_allocators_metrics();
    virtual void collect_dataagents_metrics();
    virtual void collect_volumeservices_metrics();
    virtual void collect_capacity_metrics();
    virtual void collect_capacity_metrics_by_az(std::shared_ptr<AZ>, std::shared_ptr<CapacityMetrics>);
    virtual void collect_freespace_metrics();
    virtual void collect_freespace_metrics_by_space(std::shared_ptr<Space>, std::shared_ptr<FreeSpaceMetrics>);
    virtual void collect_total_freespace_metrics(std::shared_ptr<TotalFreeSpaceMetrics>);
    void collect_disk_usage_metrics();
    void collect_disk_io_stats_metrics();
    virtual void collect_burn_rate_metrics_by_space(std::shared_ptr<Space>, std::shared_ptr<aries::pb::Allocator_CapacityInfo>);
    virtual bool is_ready_to_collect();
    virtual std::shared_ptr<ServicesMetrics> get_or_create_service_metrics(const std::string);
    static void* thread_func(void* arg);
    void update_vlet_metric(const VletVector& vlet_list);

private:
    // 只用一个锁保护所有指标map，保证采集操作串行安全
    common::MutexLock _mutex;
    bthread_t _thread_id;
    std::atomic<bool> _quitter;
    uint64_t _last_bvar_dump_timestamp_ms;
    static const uint64_t _sleep_interval_ms = 5 * 1000; // 默认5秒采集一次

    // 各类监控指标的存储map
    std::map<std::string, std::shared_ptr<VolumesMetrics>> _volumes_metrics;
    std::map<std::string, std::shared_ptr<DataNodesMetrics>> _datanodes_metrics;
    std::map<std::string, std::shared_ptr<ServicesMetrics>> _services_metrics;
    std::map<std::string, std::shared_ptr<CapacityMetrics>> _capacity_metrics;
    std::map<std::string, std::shared_ptr<FreeSpaceMetrics>> _freespace_metrics;
    std::shared_ptr<TotalFreeSpaceMetrics> _total_freespace_metrics = nullptr; 
    std::map<std::string, std::shared_ptr<BurnRateMetrics>> _burn_rate_metrics;
    std::shared_ptr<VletsMetrics> _vlet_metric;

    // 其他监控相关成员
    std::shared_ptr<common::BvarLatency> _repair_vlet_latency;
    DiskUsageMetric _disk_usage_metric;
    DiskIoStatsMetric _disk_io_stats_metric;
};

// 全局MasterMonitor指针
extern std::shared_ptr<MasterMonitor> g_master_monitor;

}
}

#endif /* ARIES_MASTER_MONITOR_MASTER_MONITOR_H */
