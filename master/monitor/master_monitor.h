
#ifndef ARIES_MASTER_MONITOR_MASTER_MONITOR_H
#define ARIES_MASTER_MONITOR_MASTER_MONITOR_H

#include <boost/scoped_ptr.hpp>
#include "baidu/inf/aries/master/meta/meta_data.h"
#include "baidu/inf/aries/master/monitor/master_monitor_metrics.h"
#include "baidu/inf/aries-api/common/proto/allocator.pb.h"
#include "baidu/inf/aries-api/common/closure.h"
#include "baidu/inf/aries-api/common/lock.h"

namespace aries {
namespace master {

static inline uint64_t calc_num_of_active_vlets(VletVector& vlet_vector) {
    uint64_t num_of_active_vlets = vlet_vector.size();
    for (std::shared_ptr<Vlet> vlet : vlet_vector) {
        VletState state = vlet->state();
        // vlet whose state is VLET_STATE_NORMAL or VLET_STATE_REPAIRING
        // could consider to be active which is readable and writable
        if (!(state == VLET_STATE_NORMAL || state == VLET_STATE_REPAIRING)) {
            --num_of_active_vlets;
        }
    }
    return num_of_active_vlets;
}

static inline uint64_t calc_num_of_normal_vlets(VletVector& vlet_vector) {
    uint64_t num_of_normal_vlets = vlet_vector.size();
    for (std::shared_ptr<Vlet> vlet : vlet_vector) {
        VletState state = vlet->state();
        // vlet whose state is VLET_STATE_NORMAL or VLET_STATE_REPAIRING
        // could consider to be active which is readable and writable
        if (!(state == VLET_STATE_NORMAL)) {
            --num_of_normal_vlets;
        }
    }
    return num_of_normal_vlets;
}

/*
 * This is a really bad design of a metrics which may hold the global locks
 * and may case additional overhead to system. But with current design of master
 * it is a optimized option. I strongly recommend to refactoring the volume & instances
 * structure and remove this monitor.
 */
class MasterMonitor {
public:
    MasterMonitor();
    virtual ~MasterMonitor();

    virtual bool start();
    virtual bool stop();
    virtual bool is_running();
    virtual void get_volumes_metrics(std::vector<std::shared_ptr<VolumesMetrics>>&);
    virtual void get_services_metrics(std::vector<std::shared_ptr<ServicesMetrics>>&);
    virtual void get_datanodes_metrics(std::vector<std::shared_ptr<DataNodesMetrics>>&);
    virtual void get_capacity_metrics(std::vector<std::shared_ptr<CapacityMetrics>>&);
    virtual void get_freespace_metrics(std::vector<std::shared_ptr<FreeSpaceMetrics>>&);
    virtual void get_total_freespace_metrics(std::shared_ptr<TotalFreeSpaceMetrics>&);
    virtual void get_burn_rate_metrics(std::vector<std::shared_ptr<BurnRateMetrics>>&);
    virtual std::shared_ptr<ServicesMetrics> get_services_metrics_by_service_name(std::string);
    virtual std::shared_ptr<DataNodesMetrics> get_datanodes_metrics_by_az_name(std::string);
    virtual std::shared_ptr<VolumesMetrics> get_volumes_metrics_by_space_name(std::string);
    virtual std::shared_ptr<CapacityMetrics> get_capacity_metrics_by_az_name(std::string);
    virtual std::shared_ptr<FreeSpaceMetrics> get_freespace_metrics_by_space_name(std::string);
    virtual std::shared_ptr<BurnRateMetrics> get_burn_rate_metrics_by_space_name(std::string);
    void get_disk_usage(const std::string& format, Json::Value& json_value) {
        common::ScopedMutexLock lock(_mutex);
        _disk_usage_metric.usage2json(format, json_value);
    }
    void get_disk_usage(const ::aries::pb::ListDiskUsageRequest* request,
            aries::pb::ClusterDiskUsageInfo* usage_info) {
        common::ScopedMutexLock lock(_mutex);
        _disk_usage_metric.usage2pb(request, usage_info);
    }
    void get_disk_io_stats(const std::string& format, Json::Value& json_value) {
        common::ScopedMutexLock lock(_mutex);
        _disk_io_stats_metric.io_stats2json(format, json_value);
    }
    void get_disk_io_stats(const ::aries::pb::ListDiskIoStatsRequest* request,
            aries::pb::ListDiskIoStatsResponse* response) {
        common::ScopedMutexLock lock(_mutex);
        _disk_io_stats_metric.io_stats2pb(request, response);
    }
    void get_space_usage(const ::aries::pb::ShowSpaceRequest* request,
            aries::pb::ShowSpaceUsageResponse* response) {
        common::ScopedMutexLock lock(_mutex);
        if (_freespace_metrics.count(request->space_name()) == 0) {
            response->mutable_status()->set_code(AIE_NOT_EXIST);
            response->mutable_status()->set_msg("not exist");
        } else {
            response->mutable_status()->set_code(AIE_OK);
            response->mutable_status()->set_msg("ok");
            _freespace_metrics[request->space_name()]->metrics2pb(response);
        }
    }
    void metrics2json(Json::Value& json_value, const VolumesMetrics& metrics, 
        const std::string request_type = "");
    void vlets_metrics2json(Json::Value& json_value);
    virtual std::shared_ptr<aries::pb::Allocator_CapacityInfo> retrieve_from_allocators_by_space(std::shared_ptr<Space>);
    virtual void get_freespace_metrics_output(const bool use_html, base::IOBufBuilder& os);
public:
    const std::string TINKER = "tinker";
    const std::string ALLOCATOR = "allocator";
    const std::string DATA_AGENT = "dataagent";
    const std::string VOLUME_SERVICE = "volumeservice";
    const uint64_t KB = 1024;
    const uint64_t MB = 1024 * KB;
    const uint64_t GB = 1024 * MB;
    const uint64_t TB = 1024 * GB;
private:
    // VolumeMetaData Collection
    virtual void collect_volumes_metrics();
    virtual void collect_volumes_metrics_by_space(std::shared_ptr<Space>, std::shared_ptr<VolumesMetrics>);
    virtual void collect_volumes_metrics_by_volume(std::shared_ptr<Volume>, std::shared_ptr<VolumesMetrics>);
    // Instance Collection
    virtual void collect_instance_metrics();
    // Those five functions should not be called individually in this class!
    virtual void collect_datanodes_metrics_by_az(std::shared_ptr<AZ>, std::shared_ptr<DataNodesMetrics>);
    virtual void collect_tinkers_metrics();
    virtual void collect_allocators_metrics();
    virtual void collect_dataagents_metrics();
    virtual void collect_volumeservices_metrics();
    // Capacity Collection
    virtual void collect_capacity_metrics();
    virtual void collect_capacity_metrics_by_az(std::shared_ptr<AZ>, std::shared_ptr<CapacityMetrics>);
    // Freespace Collection
    virtual void collect_freespace_metrics();
    virtual void collect_freespace_metrics_by_space(std::shared_ptr<Space>, std::shared_ptr<FreeSpaceMetrics>);
    virtual void collect_total_freespace_metrics(std::shared_ptr<TotalFreeSpaceMetrics>);
    // disk usage
    void collect_disk_usage_metrics();
    // disk io
    void collect_disk_io_stats_metrics();
    // BurnRate Collection
    virtual void collect_burn_rate_metrics_by_space(std::shared_ptr<Space>, std::shared_ptr<aries::pb::Allocator_CapacityInfo>);
    virtual bool is_ready_to_collect();
    virtual std::shared_ptr<ServicesMetrics> get_or_create_service_metrics(const std::string);
    static void* thread_func(void* arg);
    void update_vlet_metric(const VletVector& vlet_list);

private:
    // It is safe to keep only one lock for all the map of metrics
    // since all the collection operation will be done one by one.
    common::MutexLock _mutex;
    bthread_t _thread_id;
    std::atomic<bool> _quitter;
    uint64_t _last_bvar_dump_timestamp_ms;
    static const uint64_t _sleep_interval_ms = 5 * 1000; // sleep 5 seconds by default
    std::map<std::string, std::shared_ptr<VolumesMetrics>> _volumes_metrics;
    std::map<std::string, std::shared_ptr<DataNodesMetrics>> _datanodes_metrics;
    std::map<std::string, std::shared_ptr<ServicesMetrics>> _services_metrics;
    std::map<std::string, std::shared_ptr<CapacityMetrics>> _capacity_metrics;
    std::map<std::string, std::shared_ptr<FreeSpaceMetrics>> _freespace_metrics;
    std::shared_ptr<TotalFreeSpaceMetrics> _total_freespace_metrics = nullptr; 
    std::map<std::string, std::shared_ptr<BurnRateMetrics>> _burn_rate_metrics;
    std::shared_ptr<VletsMetrics> _vlet_metric;

    std::shared_ptr<common::BvarLatency> _repair_vlet_latency;
    DiskUsageMetric _disk_usage_metric;
    DiskIoStatsMetric _disk_io_stats_metric;
};

extern std::shared_ptr<MasterMonitor> g_master_monitor;

}
}

#endif /* ARIES_MASTER_MONITOR_MASTER_MONITOR_H */
