//=============================================================================
// Author: <EMAIL>
// Data: 2016-11-14 18:45
// Filename: common.cpp
// Description: 
//=============================================================================

#include "baidu/inf/aries/master/meta/meta_data.h"
#include "baidu/inf/aries/master/common.h"

namespace aries {
namespace master {

std::atomic<int> g_running_control_num(0);
const double DEFAULT_MAX_DISK_USAGE = 0.98;
const double DEFAULT_MAX_DISK_USAGE_FOR_CREATE = 0.95;

baidu::rpc::ChannelOptions get_rpc_options() {
    // TODO, argumentized
    baidu::rpc::ChannelOptions options;
    options.connection_type = baidu::rpc::CONNECTION_TYPE_SINGLE;
#ifdef _UNIT_TEST
    options.max_retry = 0;
    options.connect_timeout_ms = 1000;
    options.timeout_ms = 3000;
#else
    options.max_retry = 0;
    options.connect_timeout_ms = 3000;
    options.timeout_ms = 30000;
#endif
    return options;
}

bool check_space_name_illegal(const std::string& space_name, std::string* err_msg) {
    int pre_pos = -1;
    for (size_t pos = 0; pos < space_name.size(); ++pos) {
        const char ch = space_name.at(pos);
        if (!(std::islower(ch) || std::isdigit(ch) || ch == '_')) {
            *err_msg = "space_name is not in '0-9a-z', '_'";
            return false;
        }
        if (ch == '_') {
            if (pos == 0 || pos == space_name.size() - 1) {
                *err_msg = "space_name beginning or ending at '_'";
                return false;
            }
            if ((int)pos == pre_pos + 1) {
                *err_msg = "space_name has at least two consecutive '_'";
                return false;
            }
            pre_pos = pos;
        }
    }
    return true;
}

bool check_space_params_illegal(const ::aries::pb::SpaceInfo* space, std::string* err_msg) {

    auto n = space->n();
    auto k = space->k();
    auto disk_type_set = common::string2set(space->disk_type());
    for (auto disk_type : disk_type_set) {
        if (common::string2disk_type(disk_type) == common::DT_INVALID) {
            *err_msg = "disk_type is invalid";
            return false;
        }
    }
    std::set<std::string> az_name_set;
    for (int i = 0; i < space->az_name_size(); ++i) {
        az_name_set.insert(space->az_name(i));
    }
    //check rewrite control
    if (space->append_zone_rewrite_rate() > 100) {
        *err_msg = "append_zone_rewrite_rate should less than 100";
        return false;
    }
    if (space->daily_rewrite_start_time() > 24 * 3600) {
        *err_msg = "daily_rewrite_start_time should less than 86400";
        return false;
    }
    if (space->daily_rewrite_duration_second() > 24 * 3600) {
        *err_msg = "daily_rewrite_duration_second should less than 86400";
        return false;
    }
    if (space->shard_compress_option().min_compress_ratio() < 1) {
        *err_msg = "if less than 1, no compression gains";
        return false;
    }
    //check disk_type_place_policy
    std::set<int> disk_place_set;
    for (int i = 0; i < space->disk_type_place_policy_size(); ++i) {
        uint32_t shard_index = space->disk_type_place_policy(i).shard_index();
        std::string disk_type = space->disk_type_place_policy(i).disk_type();
        disk_place_set.insert(shard_index);
        if (disk_type_set.find(disk_type) == disk_type_set.end()) {
            *err_msg = "disk_type_place_policy include invalid disk_type:" + disk_type;
            return false;
        }
    }
    if (!disk_place_set.empty() && disk_place_set.size() != n) {
        *err_msg = "disk_type_place_policy not set all vlet index";
        return false;
    }
    //check az_place_policy
    std::set<int> az_place_set;
    for (int i = 0; i < space->az_place_policy_size(); ++i) {
        uint32_t shard_index = space->az_place_policy(i).shard_index();
        std::string az_name = space->az_place_policy(i).az_name();
        az_place_set.insert(shard_index);
        if (az_name_set.find(az_name) == az_name_set.end()) {
            *err_msg = "az_place_policy include invalid az_name:" + az_name;
            return false;
        }
    }

    if (space->az_name_size() == 0) {
        *err_msg = "no az_name";
        return false;
    }

    //check az_max_vlet_policy
    uint32_t vlet_num = 0;
    auto vlet_n = space->n();
    auto az_num = space->az_name_size();
    uint32_t max_vlet_per_az = std::max(vlet_n / az_num + !!(vlet_n % az_num), space->max_vlet_per_az());
    for (int i = 0; i < space->az_max_vlet_policy_size(); ++i) {
        std::string az_name = space->az_max_vlet_policy(i).az_name();
        if (az_name_set.find(az_name) == az_name_set.end()) {
            *err_msg = "az_max_vlet_policy include invalid az_name:" + az_name;
            return false;
        }
        vlet_num += space->az_max_vlet_policy(i).max_vlet();
    }
    vlet_num += (az_num - space->az_max_vlet_policy_size()) * max_vlet_per_az;
    if (vlet_num < vlet_n) {
        *err_msg = "az_max_vlet_policy invalid vlet_num, no enough vlet can be created";
        return false;
    }

    //check vlet_fast_remove_policy
    std::string vlet_fast_remove_policy_str = space->vlet_fast_remove_policy();
    int space_n = space->n();
    if (!vlet_fast_remove_policy_str.empty()
            && vlet_fast_remove_policy_str != "all"
            && vlet_fast_remove_policy_str != "clear") {
        std::vector<std::string> vlet_fast_remove_str_list;
        std::string split(",");
        Tokenize(vlet_fast_remove_policy_str, split, &vlet_fast_remove_str_list);
        for (auto& shard_index_str : vlet_fast_remove_str_list) {
            int shard_index = std::stoi(shard_index_str);
            if (shard_index >= space_n) {
                *err_msg = "vlet_fast_remove_policy invalid shard_index, policy:" + vlet_fast_remove_policy_str;
                return false;
            }
        }
    }

    if (!az_place_set.empty() && az_place_set.size() != n) {
        *err_msg = "az_place_policy not set all vlet index";
        return false;
    }
    if (space->max_vlet_per_idc() == 0 || space->max_vlet_per_rack() == 0) {
        *err_msg = "max vlet per ids/rack can not equal 0";
        return false;
    }
    if (k > n) {
        *err_msg = "check space params fail. n can't < k, illegal space params";
        return false;
    }
    auto p = space->put_quorum();
    if (p < k || p > n) {
        *err_msg = "check space params fail. put_quorum compare k/n err, illegal space params";
        return false;
    }
    auto membership_quorum = space->membership_quorum();
    if (membership_quorum + p <= n) {
        *err_msg = "check space params fail. membership_quorum + put_quorum must > n.";
        return false;
    }
    auto drop_normal_remain = space->drop_normal_remain();
    if (membership_quorum > drop_normal_remain) {
        *err_msg = "membership_quorum had better <= drop_normal_remain.";
        return false;
    }
    if (drop_normal_remain < (k + n - p) || drop_normal_remain >= n) {
        *err_msg = "drop_normal_remain must in [k + (n - p),n)";
        return false;
    }
    auto allocator_collect_quorum = space->allocator_collect_quorum();
    if (allocator_collect_quorum > n || allocator_collect_quorum == 0) {
        *err_msg = "allocator_collect_quorum must in (0,n]";
    }

    return true;
}

double get_max_disk_usage(const std::string& az_name) {
    // thread-safe
    std::string max_disk_usage;
    assert(google::GetCommandLineOption("max_disk_usage", &max_disk_usage));
    std::vector<std::string> split_res = common::string2vec(max_disk_usage);
    try {
        if (split_res.size() == 1) {
            return std::stod(split_res[0]);
        } else {
            std::map<std::string, double> az_map{};
            double default_value = std::stod(split_res[0]);
            for (uint32_t i = 1; i < split_res.size(); i++) {
                auto az_value = common::string2vec(split_res[i], ":");
                az_map[az_value[0]] = std::stod(az_value[1]);
            }
            if (az_map.count(az_name) == 0) {
                return default_value;
            } else {
                return az_map[az_name];
            }
        }
    } catch (...) {
        LOG(FATAL) << "FLAGS_max_disk_usage is not right, FLAGS_max_disk_usage:" << max_disk_usage;
    }
    return DEFAULT_MAX_DISK_USAGE;
}

double get_max_disk_usage_for_create(const std::string& az_name) {
    // thread-safe
    std::string max_disk_usage_for_create;
    assert(google::GetCommandLineOption("max_disk_usage_for_create", &max_disk_usage_for_create));
    std::vector<std::string> split_res = common::string2vec(max_disk_usage_for_create);
    try {
        if (split_res.size() == 1) {
            return std::stod(split_res[0]);
        } else {
            std::map<std::string, double> az_map{};
            double default_value = std::stod(split_res[0]);
            for (uint32_t i = 1; i < split_res.size(); i++) {
                auto az_value = common::string2vec(split_res[i], ":");
                az_map[az_value[0]] = std::stod(az_value[1]);
            }
            if (az_map.count(az_name) == 0) {
                return default_value;
            } else {
                return az_map[az_name];
            }
        }
    } catch (...) {
        LOG(FATAL) << "FLAGS_max_disk_usage_for_create is not right, FLAGS_max_disk_usage_for_create:"
            << max_disk_usage_for_create;
    }
    return DEFAULT_MAX_DISK_USAGE_FOR_CREATE;
}


}  // namespace
}  // namespace
