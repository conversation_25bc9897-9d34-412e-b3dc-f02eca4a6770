//=============================================================================
// Author: <EMAIL>
// Data: 2016-10-18 18:17
// Filename: local_replica.h
// Description: 
//=============================================================================

#ifndef  BAIDU_INF_ARIES_MASTER_COMMON_H
#define  BAIDU_INF_ARIES_MASTER_COMMON_H

#include <thread>
#include <atomic>
#include <limits.h>
#include <base/iobuf.h>
#include <baidu/rpc/channel.h>
#include <raft/raft.h>                  // raft::Node
#include <base/macros.h>                // BAIDU_CACHELINE_ALIGNMENT
#include <base/memory/ref_counted.h>    // base::RefCountedThreadSafe
#include <base/logging.h>
#include <base/rand_util.h>
#include <bthread/countdown_event.h>    // bthread::CountdownEvent
#include <baidu/rpc/controller.h>       // baidu::rpc::Controller
#include "baidu/inf/aries-api/common/common.h"
#include "baidu/inf/aries-api/common/types.h"
#include "baidu/inf/aries-api/common/priority_queue.h"
#include "baidu/inf/aries-api/common/proto/error_code.pb.h"
#include "baidu/inf/aries-api/common/proto/enum.pb.h"
#include "baidu/inf/aries-api/common/utils.h"

namespace aries {
namespace master {

//total running control req num;
extern std::atomic<int> g_running_control_num;

const base::EndPoint INVALID_ADDR = base::EndPoint();

#define CHECK_LEADER() \
    if (!g_meta_data->is_leader()) {                                                 \
        LOG(TRACE) << " log_id: " << log_id                                   \
        << " recv: " << request->GetTypeName()                                    \
        << " from: " << base::endpoint2str(remote_addr).c_str()                          \
        << " fail: not_leader";                                                         \
        response->mutable_status()->set_code(AIE_NOT_PRIMARY);                                  \
        response->mutable_status()->set_msg("not leader");                        \
        base::EndPoint leader_addr = g_master_control->get_raft_leader();            \
        if (leader_addr.port != 0 && leader_addr.ip != base::IP_ANY) {              \
            response->mutable_status()->mutable_leader_hint()                       \
                    ->CopyFrom(common::endpoint2node(leader_addr));                 \
        }   \
        break;                                                                          \
    }

#define CHECK_WORKING() \
    if (!g_meta_data->is_leader()) {                                                 \
        LOG(WARNING) << "log_id: " << log_id                        \
        << " recv: " << request->GetTypeName()                                    \
        << " from: " << base::endpoint2str(remote_addr).c_str()                        \
        << " fail: not_leader";                                                           \
        response->mutable_status()->set_code(AIE_NOT_PRIMARY);                          \
        response->mutable_status()->set_msg("not leader");                        \
        break;                                                                         \
    } else if (!g_meta_data->is_working_leader()) {                                 \
        LOG(WARNING) << "log_id: " << log_id                                    \
        << " recv: " << request->GetTypeName()                                    \
        << " from: " << base::endpoint2str(remote_addr).c_str()                         \
        << " fail: not_working";                                                         \
        response->mutable_status()->set_code(AIE_BUSY);                                  \
        response->mutable_status()->set_msg("leader not init");                        \
        break;                                                                         \
    }

#define CHECK_TOKEN() \
    if (FLAGS_token != request->token()) {    \
        LOG(WARNING) << "log_id: " << log_id                                \
        << " recv: " << request->GetTypeName()                              \
        << " from: " << base::endpoint2str(remote_addr).c_str()                       \
        << " fail: bad_token";                                                         \
        response->mutable_status()->set_code(AIE_INVALID_TOKEN);                       \
        response->mutable_status()->set_msg("token not match");                     \
        break;                                                                        \
    }                                                                                  

#define CHECK_ADMIN_TOKEN() \
    if (FLAGS_admin_token != request->token()) {                                \
        LOG(WARNING) << "log_id: " << log_id                                \
        << " recv: " << request->GetTypeName()                              \
        << " from: " << base::endpoint2str(remote_addr).c_str()                       \
        << " fail: bad_admin_token";                                                  \
        response->mutable_status()->set_code(AIE_INVALID_TOKEN);                      \
        response->mutable_status()->set_msg("admin token not match");                 \
        break;                                                                        \
    }                                                                                  

#define SERVICE_LOG(level) \
    ARIES_RPC_LOG(level) << __FUNCTION__ \
                << " remote_addr:" << base::endpoint2str(remote_addr).c_str() \
                << " req_type:" << request->GetTypeName()                     \
                << " request:" << common::pb2json(*request)                   \
                << " response:" << common::pb2json(*response);

#define SERVICE_EASY_LOG(level) \
    ARIES_RPC_LOG(level) << __FUNCTION__ \
                << " remote_addr:" << base::endpoint2str(remote_addr).c_str()    \
                << " req_type:" << request->GetTypeName()                        \
                << " response_status:" << common::pb2json(response->status());

baidu::rpc::ChannelOptions get_rpc_options();

struct RunningReqGuard {
    RunningReqGuard(std::atomic<int>& num) {
        run_num = &num;
        (*run_num)++;
    }
    ~RunningReqGuard() {
        (*run_num)--;
    }
    std::atomic<int>* run_num;
};

/*
struct TimeLaspse {
    TimeLaspse() {
        b_time = base::gettimeofday_us();
    }
    uint64_t get() {
        return base::gettimeofday_us() - b_time;
    }
    uint64_t b_time = 0;
};
*/

inline int request2data(MasterLogType log_type,
        const ::google::protobuf::Message* request,
        base::IOBuf* data) {
    int ret = 0;
    data->append(&log_type, sizeof(log_type));
    base::IOBufAsZeroCopyOutputStream wrapper(data);
    if (!request->SerializeToZeroCopyStream(&wrapper)) {
        ret = -1;
    }
    return ret;
}

template <typename Request, typename Response>
struct LogClosure : public raft::Closure {
    LogClosure(::baidu::rpc::Controller* controller_,
                        const Request* request_,
                        Response* response_)
            : controller(controller_)
            , request(request_)
            , response(response_)
            , log_id(0) {
        timestamp = base::monotonic_time_us();
        if (controller) {
            log_id = controller->log_id();
            req_addr = controller->remote_side();
        }
        LOG(TRACE) << "recv_cmd:" << request->GetTypeName()
            << " req_addr:" << base::endpoint2str(req_addr).c_str()
            << " log_id:" << log_id
            << " request_msg:" << common::pb2json(*request);
    }
    ~LogClosure() {}

    void wait() {
        event.wait();
    }

    void Run() {

        if (!status().ok()) {
            response->mutable_status()->set_code(status().error_code());
            response->mutable_status()->set_msg(status().error_cstr());
        } else {
            // Success
            response->mutable_status()->set_code(0);
            response->mutable_status()->set_msg("success");
        }
        LOG(NOTICE) << "recv_cmd:" << request->GetTypeName()
            << " time: " << base::monotonic_time_us() - timestamp
            << " from ip:" << base::endpoint2str(req_addr).c_str()
            << " log_id:" << log_id
            << " error_code:" << status().error_code()
            << ", response: " << common::pb2json(*response);
        event.signal();
    }

    int64_t timestamp;
    baidu::rpc::Controller* controller;
    const Request* request;
    Response* response;
    bthread::CountdownEvent event;
    uint64_t log_id;
    base::EndPoint req_addr;
};

struct LogMessage {
    int log_type;
    base::IOBuf msg_buf;
};

static inline void embrace_w_bracket(std::string* str) {
    if (!str) {
        return;
    }
    *str = "[" + *str + "]";
}

static inline std::string vlet_infos2string(const std::vector<std::shared_ptr<aries::pb::VletInfo>>& vlets_info) {
    std::string ret;
    for (std::shared_ptr<aries::pb::VletInfo> vlet_info : vlets_info) {
        if (!vlet_info) {
            continue;
        }
        ret += common::pb2json(*vlet_info) +  " ";
    }
    embrace_w_bracket(&ret);
    return ret;
}

static inline std::string endpoints2string(const std::vector<base::EndPoint>& endpoints) {
    std::string ret;
    for (base::EndPoint ep : endpoints) {
        ret += common::endpoint2str(ep) + " ";
    }
    embrace_w_bracket(&ret);
    return ret;
}

static inline std::string master_log_type2string(int type) {
    if (MasterLogType_IsValid(type)) {
        return MasterLogType_Name((MasterLogType)type);
    }
    return std::to_string(type);
}

static inline std::string generate_random_suffix(const std::string input) {
    std::ostringstream oss;
    oss << input << base::RandInt(1, INT_MAX);
    return oss.str();
}

static inline void retains_element(std::vector<std::shared_ptr<aries::pb::VletInfo>>& from, std::vector<std::shared_ptr<aries::pb::VletInfo>>& to) {
    for (std::shared_ptr<aries::pb::VletInfo> element : to) {
        std::vector<std::shared_ptr<aries::pb::VletInfo>>::iterator position = std::find(
                from.begin(), from.end(), element);

        if (position != from.end()) {
            from.erase(position);
        }
    }
}

class SpaceInfo;
bool check_space_params_illegal(const ::aries::pb::SpaceInfo* space, std::string* err_msg);
bool check_space_name_illegal(const std::string& space_name, std::string* err_msg);

double get_max_disk_usage(const std::string& az_name);
double get_max_disk_usage_for_create(const std::string& az_name);

}  // namespace
}  // namespace

#endif
