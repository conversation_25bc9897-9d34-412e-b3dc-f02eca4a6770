//=============================================================================
// Author: <EMAIL>
// Data: 2016-10-31 14:59
// Filename: test_repair_worker.cpp
// Description: 
//=============================================================================

#include <memory>
#include <gtest/gtest.h>
#include <base/logging.h>
#include <baidu/rpc/server.h>
#include "baidu/inf/aries/common/bmock_util.h"
#include "baidu/inf/aries-api/common/common.h"
#include "baidu/inf/aries-api/common/proto/master.pb.h"
#include "baidu/inf/aries-api/common/proto/datanode.pb.h"
#include "baidu/inf/aries/master/meta/master_state_machine.h"
#include "baidu/inf/aries/master/meta/snapshot.h"
#include "baidu/inf/aries/master/scheduler/vlet_scheduler.h"
#include "baidu/inf/aries/master/scheduler/membership_scheduler.h"
#include "baidu/inf/aries/master/meta/meta_data.h"
#include "baidu/inf/aries/master/common.h"
#include "baidu/inf/aries/master/conf.h"
#include "baidu/inf/aries/master/test/mock_common.h"

namespace aries {
namespace common {
DECLARE_string(comlog_path);
}
namespace master {

using ::testing::Return;

class MockMembershipScheduler : public MembershipScheduler {
public:
    bool update_membership(std::shared_ptr<Volume> volume,
                            uint32_t shard_index,
                            uint32_t vlet_version,
                            int success_threshold,
                            const uint64_t log_id) {
        _success_threshold = success_threshold;
        _be_call = true;
        return true;
    }
private:
    bool _be_call = false;
    int _success_threshold = 0;
};

class MockNode : public aries::pb::DataNodeControlService {
public:
    MockNode() {}

    ~MockNode() {}

    virtual void drop_disk(::google::protobuf::RpcController* controller,
                           const ::aries::pb::DropDiskRequest* request,
                           ::aries::pb::AckResponse* response,
                           ::google::protobuf::Closure* done) {
        _be_call++;
        done->Run();
    }
private:
    int _be_call = 0;
};

class MasterStateMachineTests : public ::testing::Test {
public:
    MasterStateMachineTests() {
    }
    ~MasterStateMachineTests() {
    }

    void clear_meta() {
        {
            common::ScopedMutexLock lock(g_meta_data->_node_manager->_mutex);
            g_meta_data->_node_manager->_node_map.clear();
        }
        {
            common::ScopedMutexLock lock(g_meta_data->_space_manager->_mutex);
            g_meta_data->_space_manager->_space_map.clear();
            g_meta_data->_space_manager->_space_id = 0;
        }
        {
            common::ScopedMutexLock lock(g_meta_data->_volume_manager->_mutex);
            g_meta_data->_volume_manager->_volume_map.clear();
            g_meta_data->_volume_manager->_max_volume_id = 0;
        }
        {
            common::ScopedMutexLock lock(g_meta_data->_az_manager->_mutex);
            g_meta_data->_az_manager->_az_map.clear();
        }
        {
            common::ScopedMutexLock lock(g_meta_data->_allocator_manager->_mutex);
            g_meta_data->_allocator_manager->_allocator_map.clear();
            g_meta_data->_allocator_manager->_max_sequence_id = 0;
        }
        {
            common::ScopedMutexLock lock(g_meta_data->_tinker_manager->_mutex);
            g_meta_data->_tinker_manager->_tinker_map.clear();
        }
        {
            common::ScopedMutexLock lock(g_meta_data->_tape_center_manager->_mutex);
            g_meta_data->_tape_center_manager->_tape_center_map.clear();
        }
        {
            common::ScopedMutexLock lock(g_meta_data->_tape_node_manager->_mutex);
            g_meta_data->_tape_node_manager->_tape_node_map.clear();
        }
        {
            common::ScopedMutexLock lock(g_meta_data->_tape_library_manager->_mutex);
            g_meta_data->_tape_library_manager->_library_map.clear();
        }
        {
            common::ScopedMutexLock lock(g_meta_data->_tape_pool_manager->_mutex);
            g_meta_data->_tape_pool_manager->_pool_map.clear();
        }
        std::string cluster_name = "test";
        g_meta_data->set_cluster(cluster_name, 0);
        g_meta_data->_raft_index = 0;
    }

    void SetUp() {

        MasterSnapshot snapshot;
        std::string path = "./master.snapshot";
        system("rm -rf ./master.snapshot");
        common::FLAGS_comlog_path = "./test_log";
        ::system("mkdir -p test_log");
        _p_meta.init();
        snapshot.save_snapshot(path);
        clear_meta();
        sleep(2);
        snapshot.load_snapshot(path);

        _state_machine = new MasterStateMachine();
        g_meta_data->set_state(MASTER_STATE_FOLLOWER);
        g_master_control = new MockMasterControl;
        g_vlet_scheduler = new MockVletScheduler();
    }

    void TearDown() {
        ::system("rm -rf test_log");
        delete _state_machine;
        clear_meta();
    }
    base::IOBuf prepare_err_data() {
        base::IOBuf data;
        data = "some thing...}";
        return data;
    }

private:
    MasterStateMachine* _state_machine;
    base::EndPoint _null_node;
    base::EndPoint _disk_null_node;
    base::EndPoint _disk_has_vlet_node;
    base::EndPoint _new_node;
    PrepareMetaData _p_meta;
};

TEST_F(MasterStateMachineTests, test_eat_space) {

    {
        //proto error
        base::IOBuf err_data = prepare_err_data();
        base::Status st;
        st = _state_machine->do_eat_space(err_data, NULL, 0);
        ASSERT_EQ(st.error_code(), AIE_FAIL);
    }
    {
        auto space1 = g_space_manager->get_space("test_space1");
        //succ.
        aries::pb::EatSpaceRequest request;
        request.set_eater_space_name("test_space2");
        request.set_eater_space_id(2);
        request.set_token("default_token");
        request.set_food_space_name("test_space1");
        request.set_food_space_id(space1->space_id());

        SpaceOptions space_opt;
        space_opt.n = 2;
        space_opt.k = 1;
        space_opt.space_name = "test_space2";
        space_opt.az_list.push_back("az1");
        space_opt.group_set.insert("group1");
        space_opt.put_quorum = 2;
        space_opt.membership_quorum = 1;
        space_opt.drop_normal_remain = 1;
        space_opt.max_vlet_per_idc = 2;
        space_opt.max_vlet_per_rack = 2;
        space_opt.vlet_type = VLET_TYPE_LINKED_32G_4M_512K;
        space_opt.disk_vlet_type_map = {std::make_pair("HDD", VLET_TYPE_LINKED_32G_4M_512K), std::make_pair("HDD", VLET_TYPE_APPEND_32G_256M_4K)};
        auto space2 = std::shared_ptr<Space>(new Space(space_opt));
        space2->_space_id = 2;
        g_space_manager->add_space(space2);
        g_space_manager->_space_id = 2;

        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);
        base::Status st;
        st = _state_machine->do_eat_space(data, NULL, 0);
        ASSERT_EQ(st.error_code(), AIE_OK);
        auto food_space = g_space_manager->get_space("test_space1");
        ASSERT_EQ(food_space, nullptr);
        auto eater_space = g_space_manager->get_space("test_space2");
        VolumeVector volume_list;
        eater_space->get_volume_list(&volume_list);
        ASSERT_GE(volume_list.size(), 0);
    }
}

TEST_F(MasterStateMachineTests, test_update_node_location) {

    {
        //proto error
        base::IOBuf err_data = prepare_err_data();
        base::Status st;
        st = _state_machine->do_update_node_location(err_data, NULL, 0);
        ASSERT_EQ(st.error_code(), AIE_FAIL);
    }
    {
        //node not exist.
        aries::pb::UpdateNodeLocationRequest request;
        request.set_node_addr(1144);
        request.set_token("default_token");
        request.set_group_name("group1");
        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);
        base::Status st;
        st = _state_machine->do_update_node_location(data, NULL, 0);
        ASSERT_EQ(st.error_code(), AIE_NOT_EXIST);
    }
    {
        //succ group
        aries::pb::UpdateNodeLocationRequest request;
        request.set_node_addr(common::endpoint2int(_p_meta.get_node_addr(1)));
        request.set_token("default_token");
        request.set_group_name("group2");
        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);
        base::Status st;
        st = _state_machine->do_update_node_location(data, NULL, 0);
        ASSERT_EQ(st.error_code(), AIE_OK);
    }
    {
        //succ az
        aries::pb::UpdateNodeLocationRequest request;
        request.set_node_addr(common::endpoint2int(_p_meta.get_node_addr(1)));
        request.set_token("default_token");
        request.set_group_name("group2");
        request.set_az_name("az2");
        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);
        base::Status st;
        st = _state_machine->do_update_node_location(data, NULL, 0);
        ASSERT_EQ(st.error_code(), AIE_OK);
    }
    {
        //succ idc
        aries::pb::UpdateNodeLocationRequest request;
        request.set_node_addr(common::endpoint2int(_p_meta.get_node_addr(1)));
        request.set_token("default_token");
        request.set_group_name("group2");
        request.set_az_name("az2");
        request.set_idc_name("idc2");
        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);
        base::Status st;
        st = _state_machine->do_update_node_location(data, NULL, 0);
        ASSERT_EQ(st.error_code(), AIE_OK);
    }
    {
        //succ rack
        aries::pb::UpdateNodeLocationRequest request;
        request.set_node_addr(common::endpoint2int(_p_meta.get_node_addr(1)));
        request.set_token("default_token");
        request.set_group_name("group2");
        request.set_az_name("az2");
        request.set_idc_name("idc2");
        request.set_rack_name("rack2");
        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);
        base::Status st;
        st = _state_machine->do_update_node_location(data, NULL, 0);
        ASSERT_EQ(st.error_code(), AIE_OK);
    }
    {
        //succ only rack
        aries::pb::UpdateNodeLocationRequest request;
        request.set_node_addr(common::endpoint2int(_p_meta.get_node_addr(1)));
        request.set_token("default_token");
        request.set_rack_name("rack2");
        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);
        base::Status st;
        st = _state_machine->do_update_node_location(data, NULL, 0);
        ASSERT_EQ(st.error_code(), AIE_OK);
    }
}

TEST_F(MasterStateMachineTests, test_add_node) {

    {
        //proto error
        base::IOBuf err_data = prepare_err_data();
        base::Status st;
        st = _state_machine->do_add_node(err_data, NULL, 0);
        ASSERT_EQ(st.error_code(), AIE_FAIL);
    }
    {
        //add node node exist.
        aries::pb::AddNodeRequest request;
        request.set_node_addr(common::endpoint2int(_p_meta.get_node_addr(1)));
        request.set_az_name("az1");
        request.set_rack_name("zone1");
        request.set_token("default_token");
        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);
        base::Status st;
        st = _state_machine->do_add_node(data, NULL, 0);
        ASSERT_EQ(st.error_code(), AIE_EXIST);
    }
    {
        //add node succ
        g_meta_data->set_state(MASTER_STATE_LEADER_WORKING);
        g_vlet_scheduler = new VletScheduler();

        aries::pb::AddNodeRequest request;
        request.set_node_addr(common::endpoint2int(_p_meta.get_node(0)));
        request.set_az_name("Default");
        request.set_rack_name("zone1");
        request.set_token("default_token");
        request.set_state(NODE_STATE_UNBALANCED);
        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);
        base::Status st;
        st = _state_machine->do_add_node(data, NULL, 2001);
        ASSERT_EQ(st.error_code(), AIE_OK);
        auto node_addr = _p_meta.get_node(0);
        auto node = g_node_manager->get_node(node_addr);
        ASSERT_EQ(node->_raft_index, 2001);
        ASSERT_EQ(node->state(), NODE_STATE_UNBALANCED);
    }
}

TEST_F(MasterStateMachineTests, test_create_snapshot) {
    {
        //proto err
        base::IOBuf err_data = prepare_err_data();
        base::Status st;
        st = _state_machine->do_create_snapshot(err_data, NULL, 1);
        ASSERT_EQ(st.error_code(), AIE_FAIL);
    }
    {
        //success.
        aries::pb::CreateSnapshotRequest request;

        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);
        base::Status st;
        st = _state_machine->do_create_snapshot(data, NULL, 1);
        ASSERT_EQ(st.error_code(), AIE_OK);
    }
}

TEST_F(MasterStateMachineTests, test_set_node_state) {
    {
        //proto err
        base::IOBuf err_data = prepare_err_data();
        base::Status st;
        st = _state_machine->do_set_node_state(err_data, NULL, 1);
        ASSERT_EQ(st.error_code(), AIE_FAIL);
    }

    {
        //node not exist.
        aries::pb::UpdateNodeRequest request;
        request.set_node_addr(common::endpoint2int(_p_meta.get_node(0)));
        request.set_token("default_token");
        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);
        base::Status st;
        st = _state_machine->do_set_node_state(data, NULL, 1);
        ASSERT_EQ(st.error_code(), AIE_NOT_EXIST);
    }

    {
        //request has src state but not same with node state.
        aries::pb::UpdateNodeRequest request;
        request.set_node_addr(common::endpoint2int(_p_meta.get_node(1)));
        request.set_token("default_token");
        request.set_state(NODE_STATE_DECOMMISSIONING);
        request.set_src_state(NODE_STATE_DECOMMISSIONED);

        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);
        base::Status st;
        st = _state_machine->do_set_node_state(data, NULL, 1);
        ASSERT_EQ(st.error_code(), AIE_FAIL);
    }
    {
        //success.
        aries::pb::UpdateNodeRequest request;
        request.set_node_addr(common::endpoint2int(_p_meta.get_node(1)));
        request.set_token("default_token");
        request.set_state(NODE_STATE_DECOMMISSIONING);
        request.set_src_state(NODE_STATE_NORMAL);

        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);
        base::Status st;
        st = _state_machine->do_set_node_state(data, NULL, 2001);
        ASSERT_EQ(st.error_code(), AIE_OK);

        auto node_addr = _p_meta.get_node(1);
        auto node = g_node_manager->get_node(node_addr);
        ASSERT_EQ(node->_raft_index, 2001);
    }

    {
        //request has no src state 
        aries::pb::UpdateNodeRequest request;
        request.set_node_addr(common::endpoint2int(_p_meta.get_node(1)));
        request.set_token("default_token");
        request.set_state(NODE_STATE_DECOMMISSIONED);

        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);
        base::Status st;
        st = _state_machine->do_set_node_state(data, NULL, 1);
        ASSERT_EQ(st.error_code(), AIE_OK);
    }
}


TEST_F(MasterStateMachineTests, test_drop_node) {

    {
        //proto err
        base::IOBuf err_data = prepare_err_data();
        base::Status st;
        st = _state_machine->do_drop_node(err_data, NULL, 1);
        ASSERT_EQ(st.error_code(), AIE_FAIL);
    }

    {
        //node not exist.
        aries::pb::DropNodeRequest request;
        request.set_node_addr(common::endpoint2int(_p_meta.get_node(0)));
        request.set_token("default_token");
        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);
        base::Status st;
        st = _state_machine->do_drop_node(data, NULL, 1);
        ASSERT_EQ(st.error_code(), AIE_NOT_EXIST);
    }

    {
        //drop node node exist. with no vlet
        aries::pb::DropNodeRequest request;
        request.set_node_addr(common::endpoint2int(_p_meta.get_node(1)));
        request.set_token("default_token");
        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);
        base::Status st;
        st = _state_machine->do_drop_node(data, NULL, 1);
        ASSERT_EQ(st.error_code(), AIE_OK);
    }
    {
        //drop node suc. with vlet.
        aries::pb::DropNodeRequest request;
        request.set_node_addr(common::endpoint2int(_p_meta.get_node(3)));
        request.set_token("default_token");
        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);
        base::Status st;
        st = _state_machine->do_drop_node(data, NULL, 2002);
        ASSERT_EQ(st.error_code(), AIE_OK);
        auto volume = g_volume_manager->get_volume(3);
        auto vlet = volume->get_vlet(0);
        ASSERT_EQ(vlet->state(), VLET_STATE_DROPPING);
        ASSERT_EQ(volume->_raft_index, 2002);

    }
}

TEST_F(MasterStateMachineTests, test_drop_node_vlets) {

    {
        //proto err
        base::IOBuf err_data = prepare_err_data();
        base::Status st;
        st = _state_machine->do_drop_node_vlets(err_data, NULL, 1);
        ASSERT_EQ(st.error_code(), AIE_FAIL);
    }

    {
        //node not exist.
        aries::pb::DropNodeVletsRequest request;
        request.set_node_addr(common::endpoint2int(_p_meta.get_node(0)));
        request.set_token("default_token");
        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);
        base::Status st;
        st = _state_machine->do_drop_node_vlets(data, NULL, 1);
        ASSERT_EQ(st.error_code(), AIE_NOT_EXIST);
    }

    {
        //drop node vlet succ, with no vlets
        aries::pb::DropNodeVletsRequest request;
        request.set_node_addr(common::endpoint2int(_p_meta.get_node(1)));
        request.set_token("default_token");
        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);
        base::Status st;
        st = _state_machine->do_drop_node_vlets(data, NULL, 1);
        ASSERT_EQ(st.error_code(), AIE_OK);
    }
    {
        //drop node vlets succ, with some vlets.
        aries::pb::DropNodeVletsRequest request;
        request.set_node_addr(common::endpoint2int(_p_meta.get_node(3)));
        request.set_token("default_token");
        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);
        base::Status st;
        st = _state_machine->do_drop_node_vlets(data, NULL, 2002);
        ASSERT_EQ(st.error_code(), AIE_OK);
        auto volume = g_volume_manager->get_volume(3);
        auto vlet = volume->get_vlet(0);
        ASSERT_EQ(vlet->state(), VLET_STATE_DROPPING);
        ASSERT_EQ(volume->_raft_index, 2002);
    }
}

TEST_F(MasterStateMachineTests, test_add_disk) {
    {
        //proto err
        base::IOBuf err_data = prepare_err_data();
        base::Status st;
        st = _state_machine->do_add_disk(err_data, NULL, 0);
        ASSERT_EQ(st.error_code(), AIE_FAIL);
    }

    {
        //node not exist.
        aries::pb::MasterAddDiskRequest request;
        request.set_node_addr(common::endpoint2int(_p_meta.get_node(0)));
        request.set_disk_id(1);
        request.set_token("default_token");
        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);
        base::Status st;
        st = _state_machine->do_add_disk(data, NULL, 0);
        ASSERT_EQ(st.error_code(), AIE_NOT_EXIST);
    }
    {
        //disk is exist.
        aries::pb::MasterAddDiskRequest request;
        request.set_node_addr(common::endpoint2int(_p_meta.get_node(2)));
        request.set_disk_id(1);
        request.set_token("default_token");
        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);
        base::Status st;
        st = _state_machine->do_add_disk(data, NULL, 0);
        ASSERT_EQ(st.error_code(), AIE_EXIST);
    }
    {
        //succ.
        aries::pb::MasterAddDiskRequest request;
        request.set_node_addr(common::endpoint2int(_p_meta.get_node(1)));
        request.set_disk_id(1);
        request.set_token("default_token");
        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);
        base::Status st;
        st = _state_machine->do_add_disk(data, NULL, 0);
        ASSERT_EQ(st.error_code(), AIE_OK);
        // empty type convert to HDD
        auto node = g_node_manager->get_node(_p_meta.get_node(1));
        auto disk = node->get_disk(1);
        EXPECT_EQ(disk->disk_type(), "HDD");
    }
    {
        // add HDD type disk
        aries::pb::MasterAddDiskRequest request;
        request.set_node_addr(common::endpoint2int(_p_meta.get_node(3)));
        request.set_disk_id(2);
        request.set_token("default_token");
        request.set_disk_type("HDD");
        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);
        base::Status st;
        st = _state_machine->do_add_disk(data, NULL, 0);
        ASSERT_EQ(st.error_code(), AIE_OK); 
        auto node = g_node_manager->get_node(_p_meta.get_node(3));
        auto disk = node->get_disk(2);
        EXPECT_EQ(disk->disk_type(), "HDD");
    }
}

TEST_F(MasterStateMachineTests, test_drop_disk) {
    {
        //proto err
        base::IOBuf err_data = prepare_err_data();
        base::Status st;
        st = _state_machine->do_drop_disk(err_data, NULL, 1);
        ASSERT_EQ(st.error_code(), AIE_FAIL);
    }

    {
        //node not exist.
        aries::pb::MasterDropDiskRequest request;
        request.set_node_addr(common::endpoint2int(_p_meta.get_node(0)));
        request.set_disk_id(1);
        request.set_token("default_token");
        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);
        base::Status st;
        st = _state_machine->do_drop_disk(data, NULL, 1);
        ASSERT_EQ(st.error_code(), AIE_NOT_EXIST);
    }
    {
        //disk not exist.
        aries::pb::MasterDropDiskRequest request;
        request.set_node_addr(common::endpoint2int(_p_meta.get_node(1)));
        request.set_disk_id(1);
        request.set_token("default_token");
        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);
        base::Status st;
        st = _state_machine->do_drop_disk(data, NULL, 1);
        ASSERT_EQ(st.error_code(), AIE_NOT_EXIST);
    }
    {
        //succ.
        aries::pb::MasterDropDiskRequest request;
        request.set_node_addr(common::endpoint2int(_p_meta.get_node(2)));
        request.set_disk_id(1);
        request.set_token("default_token");
        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);
        base::Status st;
        st = _state_machine->do_drop_disk(data, NULL, 1);
        ASSERT_EQ(st.error_code(), AIE_OK);
    }
    {
        //succ with vlet.
        aries::pb::MasterDropDiskRequest request;
        aries::pb::AckResponse response;
        request.set_node_addr(common::endpoint2int(_p_meta.get_node(3)));
        request.set_disk_id(1);
        request.set_token("default_token");
        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);
        base::Status st;

        baidu::rpc::Controller cntl;
        LogClosure<aries::pb::MasterDropDiskRequest, aries::pb::AckResponse> closure(&cntl, &request, &response);
        baidu::rpc::Server server;
        auto mock_node = new MockNode;
        CHECK_EQ(0, server.AddService(mock_node, baidu::rpc::SERVER_OWNS_SERVICE));
        ASSERT_EQ(0, server.Start(_p_meta.get_node(3), NULL));

        st = _state_machine->do_drop_disk(data, &closure, 2003);
        sleep(1);
        ASSERT_EQ(st.error_code(), AIE_OK);
        auto volume = g_volume_manager->get_volume(3);
        auto vlet = volume->get_vlet(0);
        ASSERT_EQ(vlet->state(), VLET_STATE_DROPPING);
        ASSERT_EQ(mock_node->_be_call, 1);
        ASSERT_EQ(volume->_raft_index, 2003);

    }
    {
        //fail with cant recover vlet.
        aries::pb::MasterDropDiskRequest request;
        request.set_node_addr(common::endpoint2int(_p_meta.get_node(5)));
        request.set_disk_id(1);
        request.set_token("default_token");
        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);
        base::Status st;
        st = _state_machine->do_drop_disk(data, NULL, 1);
        ASSERT_EQ(st.error_code(), AIE_FAIL);
    }
}

TEST_F(MasterStateMachineTests, test_create_space) {
    {
        //proto err
        base::IOBuf err_data = prepare_err_data();
        base::Status st;
        st = _state_machine->do_create_space(err_data, NULL, 1);
        ASSERT_EQ(st.error_code(), AIE_FAIL);
    }
    {
        //succ
        aries::pb::CreateSpaceRequest request;
        request.set_token("default_token");
        auto space_info = request.mutable_space_info();
        space_info->set_space_name("test_space2");
        space_info->add_az_name("az1");
        space_info->set_ec_type(EC_RS_ISAL);
        space_info->set_n(18);
        space_info->set_k(9);
        space_info->set_validator_addr("bns://xx.yy.zz:2012");
        space_info->set_max_holes_size_for_fast_remove(100);
        space_info->set_min_record_size_for_fast_remove(10);

        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);
        base::Status st;
        st = _state_machine->do_create_space(data, NULL, 2004);
        ASSERT_EQ(st.error_code(), AIE_OK);

        auto space = g_space_manager->get_space("test_space2");
        ASSERT_EQ(space->_raft_index, 2004);
        ASSERT_EQ(space->_options.validator_addr, "bns://xx.yy.zz:2012");
        ASSERT_EQ(space->_options.max_holes_size_for_fast_remove, 100);
        ASSERT_EQ(space->_options.min_record_size_for_fast_remove, 10);
    }
    {
        //space already exist.
        aries::pb::CreateSpaceRequest request;
        request.set_token("default_token");
        auto space_info = request.mutable_space_info();
        space_info->set_space_name("test_space2");
        space_info->set_ec_type(EC_RS_ISAL);
        space_info->set_n(18);
        space_info->set_k(9);

        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);
        base::Status st;
        st = _state_machine->do_create_space(data, NULL, 1);
        ASSERT_EQ(st.error_code(), AIE_EXIST);
    }
    {
        // az not exist.
        aries::pb::CreateSpaceRequest request;
        request.set_token("default_token");
        auto space_info = request.mutable_space_info();
        space_info->set_space_name("test_space3");
        space_info->add_az_name("xxxxxxxxx");
        space_info->set_ec_type(EC_RS_ISAL);
        space_info->set_n(18);
        space_info->set_k(9);

        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);
        base::Status st;
        st = _state_machine->do_create_space(data, NULL, 1);
        ASSERT_EQ(st.error_code(), AIE_FAIL);
    }
}

TEST_F(MasterStateMachineTests, test_update_space) {
    {
        //succ
        aries::pb::UpdateSpaceRequest request;
        request.set_token("default_token");
        auto space_info = request.mutable_space_info();
        space_info->set_space_name("test_space1");
        space_info->set_ec_type(EC_RS_ISAL);
        space_info->set_n(2);
        space_info->set_k(1);
        space_info->set_space_id(1);
        space_info->set_put_quorum(0);
        space_info->set_min_record_size_for_fast_remove(100 * 1024);
        {
            auto disk_vlet = space_info->add_disk_vlet_type();
            disk_vlet->set_disk_type("HDD");
            disk_vlet->set_vlet_type(VLET_TYPE_LINKED_32G_4M_512K);
        }
        {
            auto disk_vlet = space_info->add_disk_vlet_type();
            disk_vlet->set_disk_type("HDD");
            disk_vlet->set_vlet_type(VLET_TYPE_APPEND_32G_256M_4K);
        }
        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);
        base::Status st;
        st = _state_machine->do_update_space(data, NULL, 2005);
        ASSERT_EQ(st.error_code(), AIE_OK);
        auto space = g_space_manager->get_space("test_space1");
        ASSERT_EQ(space->opt().put_quorum, 0);
        ASSERT_EQ(space->opt().min_record_size_for_fast_remove, 100 * 1024);
        ASSERT_EQ(space->_raft_index, 2005);
    }
    {
        //n is not equal
        aries::pb::UpdateSpaceRequest request;
        request.set_token("default_token");
        auto space_info = request.mutable_space_info();
        space_info->set_space_name("test_space1");
        space_info->set_ec_type(EC_RS_ISAL);
        space_info->set_n(19);
        space_info->set_k(9);
        space_info->set_space_id(1);
        space_info->set_put_quorum(18);
        {
            auto disk_vlet = space_info->add_disk_vlet_type();
            disk_vlet->set_disk_type("HDD");
            disk_vlet->set_vlet_type(VLET_TYPE_LINKED_32G_4M_512K);
        }
        {
            auto disk_vlet = space_info->add_disk_vlet_type();
            disk_vlet->set_disk_type("HDD");
            disk_vlet->set_vlet_type(VLET_TYPE_APPEND_32G_256M_4K);
        }
        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);
        base::Status st;
        st = _state_machine->do_update_space(data, NULL, 2005);
        ASSERT_EQ(st.error_code(), AIE_FAIL);
        auto space = g_space_manager->get_space("test_space1");
        ASSERT_EQ(space->opt().n, 2);
    }
}

TEST_F(MasterStateMachineTests, test_drop_space) {
    {
        //proto err
        base::IOBuf err_data = prepare_err_data();
        base::Status st;
        st = _state_machine->do_drop_space(err_data, NULL, 1);
        ASSERT_EQ(st.error_code(), AIE_FAIL);
    }
    {
        //space not exist.
        aries::pb::DropSpaceRequest request;
        request.set_token("default_token");
        request.set_space_name("test_space_2");
        request.set_space_id(0);

        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);
        base::Status st;
        st = _state_machine->do_drop_space(data, NULL, 1);
        ASSERT_EQ(st.error_code(), AIE_NOT_EXIST);
    }
    {
        //space id not right.
        aries::pb::DropSpaceRequest request;
        request.set_token("default_token");
        request.set_space_name("test_space1");
        request.set_space_id(0);

        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);
        base::Status st;
        st = _state_machine->do_drop_space(data, NULL, 1);
        ASSERT_EQ(st.error_code(), AIE_FAIL);
    }
    {
        //succ
        aries::pb::DropSpaceRequest request;
        request.set_token("default_token");
        request.set_space_name("test_space1");
        request.set_space_id(1);

        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);
        base::Status st;
        st = _state_machine->do_drop_space(data, NULL, 1);
        ASSERT_EQ(st.error_code(), AIE_OK);
        ASSERT_EQ(g_meta_data->_space_manager->_space_map.size(), 1);
        ASSERT_EQ(g_meta_data->_volume_manager->_volume_map.size(), 0);
    }
}

TEST_F(MasterStateMachineTests, test_reduce_space_redundancy) {

    //add space
    {
        SpaceOptions space_opt;
        space_opt.n = 18;
        space_opt.k = 9;
        space_opt.space_name = "test_space2";
        space_opt.az_list.push_back("az1");
        space_opt.group_set.insert("group1");
        space_opt.put_quorum = 15;
        space_opt.membership_quorum = 15;
        space_opt.drop_normal_remain = 15;
        space_opt.max_vlet_per_idc = 18;
        space_opt.max_vlet_per_rack = 18;
        space_opt.vlet_type = VLET_TYPE_LINKED_32G_4M_512K;
        auto space = std::shared_ptr<Space>(new Space(space_opt));
        space->_space_id = 2;
        g_space_manager->add_space(space);
        g_space_manager->_space_id = 2;
    }
    //space is empty, reduce space redundancy succ.
    {
        auto space = g_space_manager->get_space("test_space2");
        space->set_state(SPACE_STATE_NORMAL);

        aries::pb::ReduceSpaceRedundancyRequest request;
        request.set_token("default_token");
        request.set_space_name("test_space2");
        request.set_space_id(2);
        request.set_n(17);

        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);
        base::Status st;
        st = _state_machine->do_reduce_space_redundancy(data, NULL, 1);
        ASSERT_EQ(st.error_code(), AIE_OK);
        ASSERT_EQ(space->n(), 17);
    }
    //add volumes
    {
        auto space = g_space_manager->get_space("test_space2");
        for (int vid = 10; vid <= 17; ++vid) {
            VolumeOptions volume_opt;
            volume_opt.space = space;
            volume_opt.volume_id = vid;
            std::shared_ptr<Volume> volume(new Volume(volume_opt));
            volume->_state = VOLUME_STATE_NORMAL;
            for (uint64_t index = 0; index < space->n(); ++index) {
                VletOptions vlet_opt;
                vlet_opt.volume = volume;
                vlet_opt.shard_index = index;
                vlet_opt.state = VLET_STATE_NORMAL;
                vlet_opt.disk_ptr = nullptr;
                auto vlet = std::shared_ptr<Vlet>(new Vlet(vlet_opt));
                volume->add_vlet(vlet);
            }
            g_meta_data->add_volume(volume);
        }
    }
    //reduce space redundancy succ.
    {
        auto space = g_space_manager->get_space("test_space2");
        space->set_state(SPACE_STATE_NORMAL);
        VolumeVector volume_list;
        space->get_volume_list(&volume_list);
        for (auto& volume : volume_list) {
            volume->set_state(VOLUME_STATE_NORMAL);
            VletVector vlets;
            volume->get_vlet_list(&vlets);
            for (auto vlet : vlets) {
                vlet->set_state(VLET_STATE_NORMAL);
            }
        }

        aries::pb::ReduceSpaceRedundancyRequest request;
        request.set_token("default_token");
        request.set_space_name("test_space2");
        request.set_space_id(2);
        request.set_n(16);

        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);
        base::Status st;
        st = _state_machine->do_reduce_space_redundancy(data, NULL, 1);
        ASSERT_EQ(st.error_code(), AIE_OK);
    }
    //reduce space fail due to volume dangerous.
    {
        auto space = g_space_manager->get_space("test_space2");
        space->set_state(SPACE_STATE_NORMAL);
        VolumeVector volume_list;
        space->get_volume_list(&volume_list);
        for (auto& volume : volume_list) {
            volume->set_state(VOLUME_STATE_NORMAL);
            VletVector vlets;
            volume->get_vlet_list(&vlets);
            for (auto vlet : vlets) {
                vlet->set_state(VLET_STATE_CREATING);
            }
        }

        aries::pb::ReduceSpaceRedundancyRequest request;
        request.set_token("default_token");
        request.set_space_name("test_space2");
        request.set_space_id(2);
        request.set_n(16);

        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);
        base::Status st;
        st = _state_machine->do_reduce_space_redundancy(data, NULL, 1);
        ASSERT_EQ(st.error_code(), AIE_FAIL);
    }
    //reduce space fail due to volume creating.
    {
        auto space = g_space_manager->get_space("test_space2");
        space->set_state(SPACE_STATE_NORMAL);
        VolumeVector volume_list;
        space->get_volume_list(&volume_list);
        for (auto& volume : volume_list) {
            volume->set_state(VOLUME_STATE_CREATING);
            VletVector vlets;
            volume->get_vlet_list(&vlets);
            for (auto vlet : vlets) {
                vlet->set_state(VLET_STATE_NORMAL);
            }
        }

        aries::pb::ReduceSpaceRedundancyRequest request;
        request.set_token("default_token");
        request.set_space_name("test_space2");
        request.set_space_id(2);
        request.set_n(16);

        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);
        base::Status st;
        st = _state_machine->do_reduce_space_redundancy(data, NULL, 1);
        ASSERT_EQ(st.error_code(), AIE_FAIL);
    }
    //reduce space fail due to n err.
    {
        auto space = g_space_manager->get_space("test_space2");
        space->set_state(SPACE_STATE_NORMAL);
        aries::pb::ReduceSpaceRedundancyRequest request;
        request.set_token("default_token");
        request.set_space_name("test_space2");
        request.set_space_id(2);
        request.set_n(17);

        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);
        base::Status st;
        st = _state_machine->do_reduce_space_redundancy(data, NULL, 1);
        ASSERT_EQ(st.error_code(), AIE_INVALID_ARGUMENT);
    }
    //reduce space fail due to space state err.
    {
        auto space = g_space_manager->get_space("test_space2");
        space->set_state(SPACE_STATE_REDUCE_REDUNDENCY);

        aries::pb::ReduceSpaceRedundancyRequest request;
        request.set_token("default_token");
        request.set_space_name("test_space2");
        request.set_space_id(2);
        request.set_n(16);

        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);
        base::Status st;
        st = _state_machine->do_reduce_space_redundancy(data, NULL, 1);
        ASSERT_EQ(st.error_code(), AIE_SPACE_NOT_READY);
    }
    //reduce space fail due to space id err.
    {
        auto space = g_space_manager->get_space("test_space2");
        space->set_state(SPACE_STATE_NORMAL);

        aries::pb::ReduceSpaceRedundancyRequest request;
        request.set_space_name("test_space2");
        request.set_space_id(3);
        request.set_n(16);

        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);
        base::Status st;
        st = _state_machine->do_reduce_space_redundancy(data, NULL, 1);
        ASSERT_EQ(st.error_code(), AIE_FAIL);
    }
    //reduce space fail due to space not exist.
    {
        aries::pb::ReduceSpaceRedundancyRequest request;
        aries::pb::AckResponse response;
        request.set_token("default_token");
        request.set_space_name("test_space_err");
        request.set_space_id(3);
        request.set_n(16);

        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);
        base::Status st;
        st = _state_machine->do_reduce_space_redundancy(data, NULL, 1);
        ASSERT_EQ(st.error_code(), AIE_NOT_EXIST);
    }
    //proto err
    {
        base::IOBuf err_data = prepare_err_data();
        base::Status st;
        st = _state_machine->do_reduce_space_redundancy(err_data, NULL, 1);
        ASSERT_EQ(st.error_code(), AIE_FAIL);
    }
}

TEST_F(MasterStateMachineTests, test_increase_space_redundancy) {
    //increase space redundancy succ.
    {
        auto space = g_space_manager->get_space("test_space1");
        int old_n = space->n();
        space->set_state(SPACE_STATE_NORMAL);
        SpaceOptions space_opt = space->opt();
        space_opt.membership_quorum = 2;
        space_opt.drop_normal_remain = 2;
        space->update_opt(space_opt);
        VolumeVector volume_list;
        space->get_volume_list(&volume_list);
        for (auto& volume : volume_list) {
            volume->set_state(VOLUME_STATE_NORMAL);
            VletVector vlets;
            volume->get_vlet_list(&vlets);
            for (auto vlet : vlets) {
                vlet->set_state(VLET_STATE_NORMAL);
            }
        }

        aries::pb::IncreaseSpaceRedundancyRequest request;
        request.set_token("default_token");
        request.set_space_name("test_space1");
        request.set_space_id(1);
        request.set_n(3);

        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);
        base::Status st;
        st = _state_machine->do_increase_space_redundancy(data, NULL, 1);
        ASSERT_EQ(st.error_code(), AIE_OK);
        int new_n = space->n();
        ASSERT_EQ(new_n, old_n + 1);
        for (auto& volume : volume_list) {
            VletVector vlets;
            volume->get_vlet_list(&vlets);
            for (auto vlet : vlets) {
                if (vlet->shard_index() < new_n - 1) {
                    ASSERT_EQ(vlet->state(), VLET_STATE_NORMAL);
                } else {
                    ASSERT_EQ(vlet->state(), VLET_STATE_RECOVERING);
                }
                vlet->set_state(VLET_STATE_NORMAL);
            }
        }
    }
    //increase space redundancy space params illegal.
    {
        auto space = g_space_manager->get_space("test_space1");
        space->set_state(SPACE_STATE_NORMAL);
        SpaceOptions space_opt = space->opt();
        space_opt.membership_quorum = 2;
        space_opt.drop_normal_remain = 1;
        space->update_opt(space_opt);
        VolumeVector volume_list;
        space->get_volume_list(&volume_list);
        for (auto& volume : volume_list) {
            volume->reduce_redundancy();
            volume->set_state(VOLUME_STATE_NORMAL);
            VletVector vlets;
            volume->get_vlet_list(&vlets);
            for (auto vlet : vlets) {
                vlet->set_state(VLET_STATE_NORMAL);
            }
        }

        aries::pb::IncreaseSpaceRedundancyRequest request;
        request.set_token("default_token");
        request.set_space_name("test_space1");
        request.set_space_id(1);
        request.set_n(3);

        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);
        base::Status st;
        st = _state_machine->do_increase_space_redundancy(data, NULL, 1);
        ASSERT_EQ(st.error_code(), AIE_INVALID_ARGUMENT);
    }
    //increase space fail due to n err.
    {
        auto space = g_space_manager->get_space("test_space1");
        space->reduce_redundancy_finish();
        aries::pb::IncreaseSpaceRedundancyRequest request;
        request.set_token("default_token");
        request.set_space_name("test_space1");
        request.set_space_id(1);
        request.set_n(2);

        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);
        base::Status st;
        st = _state_machine->do_increase_space_redundancy(data, NULL, 1);
        ASSERT_EQ(st.error_code(), AIE_INVALID_ARGUMENT);
    }
    //increase space fail due to space state err.
    {
        auto space = g_space_manager->get_space("test_space1");
        space->set_state(SPACE_STATE_REDUCE_REDUNDENCY);

        aries::pb::IncreaseSpaceRedundancyRequest request;
        request.set_token("default_token");
        request.set_space_name("test_space1");
        request.set_space_id(1);
        request.set_n(3);

        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);
        base::Status st;
        st = _state_machine->do_increase_space_redundancy(data, NULL, 1);
        ASSERT_EQ(st.error_code(), AIE_SPACE_NOT_READY);
    }
    //reduce space fail due to space id err.
    {
        auto space = g_space_manager->get_space("test_space1");
        space->set_state(SPACE_STATE_NORMAL);

        aries::pb::IncreaseSpaceRedundancyRequest request;
        request.set_space_name("test_space1");
        request.set_space_id(2);
        request.set_n(1);

        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);
        base::Status st;
        st = _state_machine->do_increase_space_redundancy(data, NULL, 1);
        ASSERT_EQ(st.error_code(), AIE_FAIL);
    }
    //increase space fail due to space not exist.
    {
        aries::pb::IncreaseSpaceRedundancyRequest request;
        aries::pb::AckResponse response;
        request.set_token("default_token");
        request.set_space_name("test_space_err");
        request.set_space_id(1);
        request.set_n(3);

        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);
        base::Status st;
        st = _state_machine->do_increase_space_redundancy(data, NULL, 1);
        ASSERT_EQ(st.error_code(), AIE_NOT_EXIST);
    }
    //proto err
    {
        base::IOBuf err_data = prepare_err_data();
        base::Status st;
        st = _state_machine->do_increase_space_redundancy(err_data, NULL, 1);
        ASSERT_EQ(st.error_code(), AIE_FAIL);
    }
}

TEST_F(MasterStateMachineTests, test_increase_space_redundancy_with_creating_volume) {
    //increase space redundancy succ with volume in createing state.
    {
        auto space = g_space_manager->get_space("test_space1");
        int old_n = space->n();
        space->set_state(SPACE_STATE_NORMAL);
        SpaceOptions space_opt = space->opt();
        space_opt.membership_quorum = 2;
        space_opt.drop_normal_remain = 2;
        space->update_opt(space_opt);
        VolumeVector volume_list;
        space->get_volume_list(&volume_list);
        for (auto& volume : volume_list) {
            volume->set_state(VOLUME_STATE_CREATING);
            VletVector vlets;
            volume->get_vlet_list(&vlets);
            for (auto vlet : vlets) {
                vlet->set_state(VLET_STATE_NORMAL);
            }
        }

        aries::pb::IncreaseSpaceRedundancyRequest request;
        request.set_token("default_token");
        request.set_space_name("test_space1");
        request.set_space_id(1);
        request.set_n(3);

        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);
        base::Status st;
        st = _state_machine->do_increase_space_redundancy(data, NULL, 1);
        ASSERT_EQ(st.error_code(), AIE_OK);
        int new_n = space->n();
        ASSERT_EQ(new_n, old_n + 1);
        for (auto& volume : volume_list) {
            VletVector vlets;
            volume->get_vlet_list(&vlets);
            for (auto vlet : vlets) {
                if (vlet->shard_index() < new_n - 1) {
                    ASSERT_EQ(vlet->state(), VLET_STATE_NORMAL);
                } else {
                    ASSERT_EQ(vlet->state(), VLET_STATE_CREATING);
                }
            }
        }
    }
}

TEST_F(MasterStateMachineTests, test_reduce_space_redundancy_finish) {

    auto space = g_space_manager->get_space("test_space1");
    space->set_state(SPACE_STATE_REDUCE_REDUNDENCY);
    VolumeVector volume_list;
    space->get_volume_list(&volume_list);
    for (auto& volume : volume_list) {
        volume->set_state(VOLUME_STATE_NORMAL);
        VletVector vlets;
        volume->get_vlet_list(&vlets);
        for (auto vlet : vlets) {
            vlet->set_state(VLET_STATE_DROPPED);
        }
    }

    _state_machine->reduce_space_redundancy_finish(space, 1, 100, 0);
    ASSERT_EQ(space->n(), 1);
}

TEST_F(MasterStateMachineTests, test_compact_space) {

    //compact space fail due to permit_compact_balance err.
    {
        auto space = g_space_manager->get_space("test_space1");
        space->set_state(SPACE_STATE_NORMAL);
        space->set_permit_compact_balance(false);
        space->set_compact_state(NO);

        aries::pb::CompactSpaceRequest request;
        request.set_token("default_token");
        request.set_space_name("test_space1");
        request.set_space_id(1);
        request.set_permit_compact_balance(false);

        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);
        base::Status st;
        st = _state_machine->do_compact_space(data, NULL, 1);
        ASSERT_EQ(st.error_code(), AIE_INVALID_ARGUMENT);
    }
    //compact space fail due to space state err.
    {
        auto space = g_space_manager->get_space("test_space1");
        space->set_state(SPACE_STATE_REDUCE_REDUNDENCY);
        space->set_permit_compact_balance(false);
        space->set_compact_state(NO);

        aries::pb::CompactSpaceRequest request;
        request.set_token("default_token");
        request.set_space_name("test_space1");
        request.set_space_id(1);
        request.set_permit_compact_balance(true);

        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);
        base::Status st;
        st = _state_machine->do_compact_space(data, NULL, 1);
        ASSERT_EQ(st.error_code(), AIE_SPACE_NOT_READY);
    }
    //compact space fail due to space id err.
    {
        auto space = g_space_manager->get_space("test_space1");
        space->set_state(SPACE_STATE_NORMAL);
        space->set_permit_compact_balance(false);
        space->set_compact_state(NO);

        aries::pb::CompactSpaceRequest request;
        request.set_token("default_token");
        request.set_space_name("test_space1");
        request.set_space_id(2);
        request.set_permit_compact_balance(true);

        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);
        base::Status st;
        st = _state_machine->do_compact_space(data, NULL, 1);
        ASSERT_EQ(st.error_code(), AIE_FAIL);
    }
    //compact space fail due to space not exist.
    {
        auto space = g_space_manager->get_space("test_space1");
        space->set_state(SPACE_STATE_NORMAL);
        space->set_permit_compact_balance(false);
        space->set_compact_state(NO);

        aries::pb::CompactSpaceRequest request;
        aries::pb::AckResponse response;
        request.set_token("default_token");
        request.set_space_name("test_space_err");
        request.set_space_id(1);
        request.set_permit_compact_balance(true);

        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);
        base::Status st;
        st = _state_machine->do_compact_space(data, NULL, 1);
        ASSERT_EQ(st.error_code(), AIE_NOT_EXIST);
    }
    //compact space fail due to space compact state error.
    {
        auto space = g_space_manager->get_space("test_space1");
        space->set_state(SPACE_STATE_NORMAL);
        space->set_permit_compact_balance(false);
        space->set_compact_state(RUN);

        aries::pb::CompactSpaceRequest request;
        aries::pb::AckResponse response;
        request.set_token("default_token");
        request.set_space_name("test_space1");
        request.set_space_id(1);
        request.set_permit_compact_balance(true);

        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);
        base::Status st;
        st = _state_machine->do_compact_space(data, NULL, 1);
        ASSERT_EQ(st.error_code(), AIE_SPACE_NOT_READY);
    }
    //proto err
    {
        base::IOBuf err_data = prepare_err_data();
        base::Status st;
        st = _state_machine->do_compact_space(err_data, NULL, 1);
        ASSERT_EQ(st.error_code(), AIE_FAIL);
    }
    //compact space succ.
    {        
        auto space = g_space_manager->get_space("test_space1");
        space->set_state(SPACE_STATE_NORMAL);
        space->set_permit_compact_balance(false);
        space->set_compact_state(NO);
        
        aries::pb::CompactSpaceRequest request;
        request.set_token("default_token");
        request.set_space_name("test_space1");
        request.set_space_id(1);
        request.set_permit_compact_balance(true);

        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);
        base::Status st;
        st = _state_machine->do_compact_space(data, NULL, 1);
        ASSERT_EQ(st.error_code(), AIE_OK);
        ASSERT_EQ(space->state(), SPACE_STATE_COMPACT);
        ASSERT_EQ(space->permit_compact_balance(), true);
        ASSERT_EQ(space->compact_state(), RUN);
    }
}

TEST_F(MasterStateMachineTests, test_control_compact_space) {
    auto space = g_space_manager->get_space("test_space1");
    space->set_state(SPACE_STATE_COMPACT);
    space->set_permit_compact_balance(true);
    space->set_compact_state(RUN);
    //control compact space fail due to space id err.
    {
        aries::pb::CompactSpaceRequest request;
        request.set_token("default_token");
        request.set_space_name("test_space1");
        request.set_space_id(2);
        request.set_permit_compact_balance(false);

        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);
        base::Status st;
        st = _state_machine->do_control_compact_space(data, NULL, 1);
        ASSERT_EQ(st.error_code(), AIE_FAIL);
    }
    //control compact space fail due to space not exist.
    {
        auto space = g_space_manager->get_space("test_space1");
        space->set_state(SPACE_STATE_COMPACT);
        space->set_permit_compact_balance(true);
        space->set_compact_state(RUN);

        aries::pb::CompactSpaceRequest request;
        aries::pb::AckResponse response;
        request.set_token("default_token");
        request.set_space_name("test_space_err");
        request.set_space_id(1);
        request.set_permit_compact_balance(false);

        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);
        base::Status st;
        st = _state_machine->do_control_compact_space(data, NULL, 1);
        ASSERT_EQ(st.error_code(), AIE_NOT_EXIST);
    }
    //control compact space fail due to space compact state error.
    {
        auto space = g_space_manager->get_space("test_space1");
        space->set_state(SPACE_STATE_COMPACT);
        space->set_permit_compact_balance(true);
        space->set_compact_state(NO);

        aries::pb::CompactSpaceRequest request;
        aries::pb::AckResponse response;
        request.set_token("default_token");
        request.set_space_name("test_space1");
        request.set_space_id(1);
        request.set_permit_compact_balance(false);

        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);
        base::Status st;
        st = _state_machine->do_control_compact_space(data, NULL, 1);
        ASSERT_EQ(st.error_code(), AIE_SPACE_NOT_READY);
    }
    //proto err
    {
        base::IOBuf err_data = prepare_err_data();
        base::Status st;
        st = _state_machine->do_control_compact_space(err_data, NULL, 1);
        ASSERT_EQ(st.error_code(), AIE_FAIL);
    }
    //control compact space succ, pause.
    {
        auto space = g_space_manager->get_space("test_space1");
        space->set_state(SPACE_STATE_COMPACT);
        space->set_permit_compact_balance(true);
        space->set_compact_state(RUN);
        
        aries::pb::CompactSpaceRequest request;
        request.set_token("default_token");
        request.set_space_name("test_space1");
        request.set_space_id(1);
        request.set_permit_compact_balance(false);

        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);
        base::Status st;
        st = _state_machine->do_control_compact_space(data, NULL, 1);
        aries::pb::SpaceInfo space_info;
        space->serialize(&space_info);
        ASSERT_EQ(st.error_code(), AIE_OK);
        ASSERT_EQ(space->state(), SPACE_STATE_COMPACT);
        ASSERT_EQ(space->permit_compact_balance(), false);
        ASSERT_EQ(space->compact_state(), PAUSE);
    }
    //control compact space succ, start.
    {
        auto space = g_space_manager->get_space("test_space1");
        space->set_state(SPACE_STATE_COMPACT);
        space->set_permit_compact_balance(false);
        space->set_compact_state(PAUSE);
        
        aries::pb::CompactSpaceRequest request;
        request.set_token("default_token");
        request.set_space_name("test_space1");
        request.set_space_id(1);
        request.set_permit_compact_balance(true);

        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);
        base::Status st;
        st = _state_machine->do_control_compact_space(data, NULL, 1);
        aries::pb::SpaceInfo space_info;
        space->serialize(&space_info);
        ASSERT_EQ(st.error_code(), AIE_OK);
        ASSERT_EQ(space->state(), SPACE_STATE_COMPACT);
        ASSERT_EQ(space->permit_compact_balance(), true);
        ASSERT_EQ(space->compact_state(), RUN);
    }
}

TEST_F(MasterStateMachineTests, test_cancel_compact_space) {

    //cancel compact space fail due to space state not ready.
    {
        auto space = g_space_manager->get_space("test_space1");
        space->set_state(SPACE_STATE_NORMAL);
        space->set_permit_compact_balance(false);
        space->set_compact_state(PAUSE);

        aries::pb::CompactSpaceRequest request;
        request.set_token("default_token");
        request.set_space_name("test_space1");
        request.set_space_id(1);

        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);
        base::Status st;
        st = _state_machine->do_cancel_compact_space(data, NULL, 1);
        ASSERT_EQ(st.error_code(), AIE_SPACE_NOT_READY);
    }
    //cancel compact space fail due to space id err.
    {
        auto space = g_space_manager->get_space("test_space1");
        space->set_state(SPACE_STATE_COMPACT);
        space->set_permit_compact_balance(false);
        space->set_compact_state(PAUSE);

        aries::pb::CompactSpaceRequest request;
        request.set_token("default_token");
        request.set_space_name("test_space1");
        request.set_space_id(2);

        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);
        base::Status st;
        st = _state_machine->do_cancel_compact_space(data, NULL, 1);
        ASSERT_EQ(st.error_code(), AIE_FAIL);
    }
    //cancel compact space fail due to space not exist.
    {
        auto space = g_space_manager->get_space("test_space1");
        space->set_state(SPACE_STATE_COMPACT);
        space->set_permit_compact_balance(false);
        space->set_compact_state(PAUSE);

        aries::pb::CompactSpaceRequest request;
        aries::pb::AckResponse response;
        request.set_token("default_token");
        request.set_space_name("test_space_err");
        request.set_space_id(1);

        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);
        base::Status st;
        st = _state_machine->do_cancel_compact_space(data, NULL, 1);
        ASSERT_EQ(st.error_code(), AIE_NOT_EXIST);
    }
    //cancel compact space fail due to space compact state error.
    {
        auto space = g_space_manager->get_space("test_space1");
        space->set_state(SPACE_STATE_COMPACT);
        space->set_permit_compact_balance(false);
        space->set_compact_state(NO);

        aries::pb::CompactSpaceRequest request;
        aries::pb::AckResponse response;
        request.set_token("default_token");
        request.set_space_name("test_space1");
        request.set_space_id(1);

        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);
        base::Status st;
        st = _state_machine->do_cancel_compact_space(data, NULL, 1);
        ASSERT_EQ(st.error_code(), AIE_SPACE_NOT_READY);
    }
    //proto err
    {
        base::IOBuf err_data = prepare_err_data();
        base::Status st;
        st = _state_machine->do_cancel_compact_space(err_data, NULL, 1);
        ASSERT_EQ(st.error_code(), AIE_FAIL);
    }
    //cancel compact space succ
    {        
        auto space = g_space_manager->get_space("test_space1");
        space->set_state(SPACE_STATE_COMPACT);
        space->set_permit_compact_balance(false);
        space->set_compact_state(PAUSE);

        aries::pb::CompactSpaceRequest request;
        request.set_token("default_token");
        request.set_space_name("test_space1");
        request.set_space_id(1);

        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);
        base::Status st;
        st = _state_machine->do_cancel_compact_space(data, NULL, 1);
        aries::pb::SpaceInfo space_info;
        space->serialize(&space_info);
        ASSERT_EQ(st.error_code(), AIE_OK);
        
        // after
        ASSERT_EQ(space->state(), SPACE_STATE_COMPACT);
        ASSERT_EQ(space->permit_compact_balance(), true);
        ASSERT_EQ(space->compact_state(), PAUSE);
    }
}

TEST_F(MasterStateMachineTests, test_finish_compact_space) {

    //finish compact space fail due to space id err.
    {
        auto space = g_space_manager->get_space("test_space1");
        space->set_state(SPACE_STATE_COMPACT);
        space->set_permit_compact_balance(true);
        space->set_compact_state(RUN);

        aries::pb::CompactSpaceRequest request;
        request.set_token("default_token");
        request.set_space_name("test_space1");
        request.set_space_id(4);
        request.set_permit_compact_balance(false);

        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);
        base::Status st;
        st = _state_machine->do_finish_compact_space(data, NULL, 1);
        ASSERT_EQ(st.error_code(), AIE_FAIL);
    }
    //finish compact space fail due to space not exist.
    {
        auto space = g_space_manager->get_space("test_space1");
        space->set_state(SPACE_STATE_COMPACT);
        space->set_permit_compact_balance(true);
        space->set_compact_state(RUN);

        aries::pb::CompactSpaceRequest request;
        request.set_token("default_token");
        request.set_space_name("test_space_err");
        request.set_space_id(1);
        request.set_permit_compact_balance(false);

        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);
        base::Status st;
        st = _state_machine->do_finish_compact_space(data, NULL, 1);
        ASSERT_EQ(st.error_code(), AIE_NOT_EXIST);
    }
    //finish compact space fail due to space state error.
    {
        auto space = g_space_manager->get_space("test_space1");
        space->set_state(SPACE_STATE_COMPACT);
        space->set_permit_compact_balance(true);
        space->set_compact_state(NO);

        aries::pb::CompactSpaceRequest request;
        request.set_token("default_token");
        request.set_space_name("test_space1");
        request.set_space_id(1);
        request.set_permit_compact_balance(false);

        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);
        base::Status st;
        st = _state_machine->do_finish_compact_space(data, NULL, 1);
        ASSERT_EQ(st.error_code(), AIE_SPACE_NOT_READY);
    }
    //finish compact space fail due to volume state error
    {
        auto space = g_space_manager->get_space("test_space1");
        space->set_state(SPACE_STATE_COMPACT);
        space->set_permit_compact_balance(true);
        space->set_compact_state(RUN);
        auto volume = g_volume_manager->get_volume(5);
        volume->set_compact_progress(RUNNING);

        aries::pb::CompactSpaceRequest request;
        request.set_token("default_token");
        request.set_space_name("test_space1");
        request.set_space_id(1);
        request.set_permit_compact_balance(false);

        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);
        base::Status st;
        st = _state_machine->do_finish_compact_space(data, NULL, 1);
        ASSERT_EQ(st.error_code(), AIE_FAIL);
        volume->set_compact_progress(START);
    }
    //proto err
    {
        base::IOBuf err_data = prepare_err_data();
        base::Status st;
        st = _state_machine->do_finish_compact_space(err_data, NULL, 1);
        ASSERT_EQ(st.error_code(), AIE_FAIL);
    }
    //finish compact space succ
    {       
        auto space = g_space_manager->get_space("test_space1");
        space->set_state(SPACE_STATE_COMPACT);
        space->set_permit_compact_balance(true);
        space->set_compact_state(RUN);

        aries::pb::CompactSpaceRequest request;
        request.set_token("default_token");
        request.set_space_name("test_space1");
        request.set_space_id(1);
        request.set_permit_compact_balance(false);

        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);
        base::Status st;
        st = _state_machine->do_finish_compact_space(data, NULL, 1);
        aries::pb::SpaceInfo space_info;
        space->serialize(&space_info);
        ASSERT_EQ(st.error_code(), AIE_OK);
        
        // after
        ASSERT_EQ(space->state(), SPACE_STATE_NORMAL);
        ASSERT_EQ(space->permit_compact_balance(), false);
        ASSERT_EQ(space->compact_state(), FINISH);
    }
}

TEST_F(MasterStateMachineTests, test_update_volume_compact_progress) {

    //before
    auto space = g_space_manager->get_space("test_space1");
    space->set_state(SPACE_STATE_COMPACT);
    space->set_permit_compact_balance(true);
    space->set_compact_state(RUN);
    auto volume3 = g_volume_manager->get_volume(3);
    //update volume meta fail due to volume not exist.
    {
        aries::pb::UpdateVolumeCompactRequest request;
        request.set_token("default_token");
        request.set_volume_id(9);
        request.set_volume_state(VOLUME_STATE_READONLY);
        request.set_compact_progress(RUNNING);
        request.set_compact_vlet_size(28 * aries::common::GB);

        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);
        base::Status st;
        st = _state_machine->do_update_volume_compact_progress(data, NULL, 1);
        ASSERT_EQ(st.error_code(), AIE_NOT_EXIST);
    }
    //update volume meta fail due to volume state error
    {
        volume3->set_state(VOLUME_STATE_DROPPED);
        aries::pb::UpdateVolumeCompactRequest request;
        request.set_token("default_token");
        request.set_volume_id(3);
        request.set_volume_state(VOLUME_STATE_READONLY);
        request.set_compact_progress(RUNNING);
        request.set_compact_vlet_size(28 * aries::common::GB);

        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);
        base::Status st;
        st = _state_machine->do_update_volume_compact_progress(data, NULL, 1);
        ASSERT_EQ(st.error_code(), AIE_FAIL);
        volume3->set_state(VOLUME_STATE_NORMAL);
    }
    //update volume meta fail due to request volume state is error.
    {
        aries::pb::UpdateVolumeCompactRequest request;
        request.set_token("default_token");
        request.set_volume_id(3);
        request.set_volume_state(VOLUME_STATE_CREATING);
        request.set_compact_progress(RUNNING);
        request.set_compact_vlet_size(28 * aries::common::GB);

        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);
        base::Status st;
        st = _state_machine->do_update_volume_compact_progress(data, NULL, 1);
        ASSERT_EQ(st.error_code(), AIE_FAIL);
    }
    //proto err
    {
        base::IOBuf err_data = prepare_err_data();
        base::Status st;
        st = _state_machine->do_update_volume_compact_progress(err_data, NULL, 1);
        ASSERT_EQ(st.error_code(), AIE_FAIL);
    }
    //update volume meta readonly succ
    {
        aries::pb::UpdateVolumeCompactRequest request;
        request.set_token("default_token");
        request.set_volume_id(3);
        request.set_volume_state(VOLUME_STATE_READONLY);
        request.set_compact_progress(RUNNING);
        request.set_compact_vlet_size(28 * aries::common::GB);

        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);
        base::Status st;
        st = _state_machine->do_update_volume_compact_progress(data, NULL, 1);
        ASSERT_EQ(st.error_code(), AIE_OK);
        
        // after
        ASSERT_EQ(volume3->state(), VOLUME_STATE_READONLY);
        ASSERT_EQ(volume3->compact_progress(), RUNNING);
        ASSERT_EQ(volume3->compact_vlet_size(), 28 * aries::common::GB);
        ASSERT_EQ(volume3->per_vlet_size(), 32 * aries::common::GB);
    }
    //update volume meta end failed by some volume compact_vlet_size error.
    {
        volume3->set_compact_vlet_size(0);
        aries::pb::UpdateVolumeCompactRequest request;
        request.set_token("default_token");
        request.set_volume_id(3);
        request.set_volume_state(VOLUME_STATE_NORMAL);
        request.set_compact_progress(END);
        request.set_compact_vlet_size(28 * aries::common::GB);

        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);
        base::Status st;
        st = _state_machine->do_update_volume_compact_progress(data, NULL, 1);
        ASSERT_EQ(st.error_code(), AIE_INVALID_ARGUMENT);
    }
    //update volume meta end failed by some vlet_size error.
    {
        volume3->set_compact_vlet_size(28 * aries::common::GB);
        VletVector vlets;
        volume3->get_vlet_list(&vlets);
        for (auto vlet : vlets) {
            vlet->vlet_engine_info_ptr()->set_vlet_size(0);
        }

        aries::pb::UpdateVolumeCompactRequest request;
        request.set_token("default_token");
        request.set_volume_id(3);
        request.set_volume_state(VOLUME_STATE_NORMAL);
        request.set_compact_progress(END);
        request.set_compact_vlet_size(28 * aries::common::GB);

        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);
        base::Status st;
        st = _state_machine->do_update_volume_compact_progress(data, NULL, 1);
        ASSERT_EQ(st.error_code(), AIE_FAIL);
    }
    //update volume meta end succ.
    {
        volume3->set_compact_vlet_size(28 * aries::common::GB);
        VletVector vlets;
        volume3->get_vlet_list(&vlets);
        for (auto vlet : vlets) {
            vlet->vlet_engine_info_ptr()->set_vlet_size(28 * aries::common::GB);
        }

        aries::pb::UpdateVolumeCompactRequest request;
        request.set_token("default_token");
        request.set_volume_id(3);
        request.set_volume_state(VOLUME_STATE_NORMAL);
        request.set_compact_progress(END);
        request.set_compact_vlet_size(28 * aries::common::GB);

        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);
        base::Status st;
        st = _state_machine->do_update_volume_compact_progress(data, NULL, 1);
        ASSERT_EQ(st.error_code(), AIE_OK);
        ASSERT_EQ(volume3->state(), VOLUME_STATE_NORMAL);
        ASSERT_EQ(volume3->compact_progress(), END);
        ASSERT_EQ(volume3->compact_vlet_size(), 28 * aries::common::GB);
        ASSERT_EQ(volume3->per_vlet_size(), 28 * aries::common::GB);
        // after
        volume3->set_compact_progress(NONE);
        volume3->set_compact_vlet_size(0);
        volume3->set_per_vlet_size(32 * aries::common::GB);
        for (auto vlet : vlets) {
            vlet->vlet_engine_info_ptr()->set_vlet_size(32 * aries::common::GB);
        }
    }
}

TEST_F(MasterStateMachineTests, test_set_cluster_after_create_volumes) {
    //set cluster id
    //restart
    //get max volume id
    delete _state_machine;
    clear_meta();

    MasterSnapshot snapshot;
    std::string path = "./master.snapshot";
    system("rm -rf ./master.snapshot");

    _state_machine = new MasterStateMachine();
    g_meta_data->set_state(MASTER_STATE_LEADER_WORKING);
    g_master_control = new MockMasterControl;
    g_vlet_scheduler = new MockVletScheduler();

    // add volume
    {
        SpaceOptions* opt = new SpaceOptions;
        opt->n = 10;
        opt->put_quorum = 10;
        opt->space_name = "test_space_xxx";
        opt->vlet_type = VLET_TYPE_LINKED_32G_4M_512K;
        std::shared_ptr<Space> space(new Space(*opt));
        VolumeOptions* volume_opt = new VolumeOptions;
        volume_opt->space = space;
        volume_opt->volume_id = 10;
        std::shared_ptr<Volume> volume(new Volume(*volume_opt));
        g_volume_manager->add_volume(volume);
    }

    //set_cluster succ
    aries::pb::SetClusterRequest request;
    request.set_token("default_token");
    request.set_cluster_name("test_2");
    request.set_cluster_id(2);
    base::IOBuf data;
    base::IOBufAsZeroCopyOutputStream wrapper(&data);
    request.SerializeToZeroCopyStream(&wrapper);
    base::Status st;
    st = _state_machine->do_set_cluster(data, NULL, 2006);
    ASSERT_EQ(st.error_code(), AIE_OK);
    ASSERT_EQ(g_meta_data->_cluster_id, 2);
    ASSERT_EQ(g_meta_data->_cluster_name, "test_2");

    //reset fail
    request.set_cluster_id(14);
    data.clear();
    base::IOBufAsZeroCopyOutputStream wrapper2(&data);
    request.SerializeToZeroCopyStream(&wrapper2);
    st = _state_machine->do_set_cluster(data, NULL, 2006);
    ASSERT_EQ(st.error_code(), AIE_FAIL);
    ASSERT_EQ(g_meta_data->_cluster_id, 2);
    ASSERT_EQ(g_meta_data->_cluster_name, "test_2");
}

TEST_F(MasterStateMachineTests, test_create_volumes_after_set_cluster) {
    {
        //after init max volume id is not 0.
        aries::pb::SetClusterRequest request;
        request.set_token("default_token");
        request.set_cluster_name("test");
        request.set_cluster_id(0);

        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);
        base::Status st;
        st = _state_machine->do_set_cluster(data, NULL, 2006);
        ASSERT_EQ(st.error_code(), AIE_OK);
        ASSERT_EQ(g_meta_data->_cluster_id, 0);
        ASSERT_EQ(g_meta_data->_cluster_name, "test");
    }
    {
        //set cluster id
        //restart
        //get max volume id
        delete _state_machine;
        clear_meta();

        MasterSnapshot snapshot;
        std::string path = "./master.snapshot";
        system("rm -rf ./master.snapshot");

        _state_machine = new MasterStateMachine();
        g_meta_data->set_state(MASTER_STATE_LEADER_WORKING);
        g_master_control = new MockMasterControl;
        g_vlet_scheduler = new MockVletScheduler();

        aries::pb::SetClusterRequest request;
        request.set_token("default_token");
        request.set_cluster_name("test_2");
        request.set_cluster_id(12);

        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);
        base::Status st;
        st = _state_machine->do_set_cluster(data, NULL, 2006);
        ASSERT_EQ(st.error_code(), AIE_OK);
        ASSERT_EQ(g_meta_data->_cluster_id, 12);
        ASSERT_EQ(g_meta_data->_cluster_name, "test_2");

        // support reset cluster id
        request.set_cluster_id(13);
        data.clear();
        base::IOBufAsZeroCopyOutputStream wrapper1(&data);
        request.SerializeToZeroCopyStream(&wrapper1);
        st = _state_machine->do_set_cluster(data, NULL, 2006);
        ASSERT_EQ(st.error_code(), AIE_OK);
        ASSERT_EQ(g_meta_data->_cluster_id, 13);
        ASSERT_EQ(g_meta_data->_cluster_name, "test_2");
        
        // if no volume exist and max_volume_id less than cluster_id << 48
        // support reset cluster id
        request.set_cluster_id(14);
        g_meta_data->set_cluster(g_meta_data->_cluster_name, 14);
        data.clear();
        base::IOBufAsZeroCopyOutputStream wrapper2(&data);
        request.SerializeToZeroCopyStream(&wrapper2);
        {
            SpaceOptions* opt = new SpaceOptions;
            opt->n = 10;
            opt->put_quorum = 10;
            opt->space_name = "test_space_xxx";
            opt->vlet_type = VLET_TYPE_LINKED_32G_4M_512K;
            std::shared_ptr<Space> space(new Space(*opt));
            VolumeOptions* volume_opt = new VolumeOptions;
            volume_opt->space = space;
            volume_opt->volume_id = 1000000;
            std::shared_ptr<Volume> volume(new Volume(*volume_opt));
            g_volume_manager->add_volume(volume);
            g_volume_manager->drop_volume(volume_opt->volume_id);
            
        }
        st = _state_machine->do_set_cluster(data, NULL, 2006);
        ASSERT_EQ(st.error_code(), AIE_OK);
        ASSERT_EQ(g_meta_data->_cluster_id, 14);
        ASSERT_EQ(g_meta_data->_cluster_name, "test_2");

        // if volume exist, and max_volume_id greater or equal cluster_id << 48
        // not support reset cluster id
        request.set_cluster_id(15);
        g_meta_data->set_cluster(g_meta_data->_cluster_name, 15);
        data.clear();
        base::IOBufAsZeroCopyOutputStream wrapper3(&data);
        request.SerializeToZeroCopyStream(&wrapper3);
        {
            SpaceOptions* opt = new SpaceOptions;
            opt->n = 10;
            opt->put_quorum = 10;
            opt->space_name = "test_space_xxx";
            opt->vlet_type = VLET_TYPE_LINKED_32G_4M_512K;
            std::shared_ptr<Space> space(new Space(*opt));
            VolumeOptions* volume_opt = new VolumeOptions;
            volume_opt->space = space;
            volume_opt->volume_id = (15UL << 48) + 3;
            std::shared_ptr<Volume> volume(new Volume(*volume_opt));
            g_volume_manager->add_volume(volume);
            LOG(TRACE) << g_volume_manager->_max_volume_id;
            g_volume_manager->drop_volume(volume_opt->volume_id);
        }
        st = _state_machine->do_set_cluster(data, NULL, 2006);
        ASSERT_EQ(st.error_code(), AIE_FAIL);
        
        // if exist volume, diff cluster id, reset cluster id failed
        request.set_cluster_id(16);
        g_meta_data->set_cluster(g_meta_data->_cluster_name, 15); 
        data.clear();
        base::IOBufAsZeroCopyOutputStream wrapper4(&data);
        request.SerializeToZeroCopyStream(&wrapper4);
        {
            SpaceOptions* opt = new SpaceOptions;
            opt->n = 10;
            opt->put_quorum = 10;
            opt->space_name = "test_space_xxx";
            opt->vlet_type = VLET_TYPE_LINKED_32G_4M_512K;
            std::shared_ptr<Space> space(new Space(*opt));
            VolumeOptions* volume_opt = new VolumeOptions;
            volume_opt->space = space;
            volume_opt->volume_id = (16UL << 48) + 3;
            std::shared_ptr<Volume> volume(new Volume(*volume_opt));
            g_volume_manager->add_volume(volume);
        }
        st = _state_machine->do_set_cluster(data, NULL, 2006);
        g_volume_manager->drop_volume((16UL << 48) + 3);
        ASSERT_EQ(st.error_code(), AIE_FAIL);

        g_meta_data->set_cluster(g_meta_data->_cluster_name, 14);
        g_volume_manager->_max_volume_id = ((uint64_t)(12) << 48);
        
        snapshot.save_snapshot(path);
        clear_meta();
        snapshot.load_snapshot(path);

        ASSERT_EQ(g_volume_manager->_max_volume_id, (uint64_t)(12) << 48);
    }
}

TEST_F(MasterStateMachineTests, test_create_volumes) {
    {
        //proto err
        base::IOBuf err_data = prepare_err_data();
        base::Status st;
        st = _state_machine->do_create_volumes(err_data, NULL, 1);
        ASSERT_EQ(st.error_code(), AIE_FAIL);
    }

    {
        //space not exist.
        aries::pb::CreateVolumesRequest request;
        request.set_token("default_token");
        request.set_space_name("test_space2");
        request.set_volume_num(1);

        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);
        base::Status st;
        st = _state_machine->do_create_volumes(data, NULL, 1);
        ASSERT_EQ(st.error_code(), AIE_NOT_EXIST);
    }
    {
        //space state
        auto space = g_space_manager->get_space("test_space1");
        space->set_state(SPACE_STATE_REDUCE_REDUNDENCY);
        aries::pb::CreateVolumesRequest request;
        request.set_token("default_token");
        request.set_space_name("test_space1");
        request.set_volume_num(1);

        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);
        base::Status st;
        st = _state_machine->do_create_volumes(data, NULL, 2006);
        ASSERT_EQ(st.error_code(), AIE_SPACE_NOT_READY);

    }
    {
        //succ
        auto space = g_space_manager->get_space("test_space1");
        space->set_state(SPACE_STATE_NORMAL);
        aries::pb::CreateVolumesRequest request;
        request.set_token("default_token");
        request.set_space_name("test_space1");
        request.set_volume_num(1);

        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);
        base::Status st;
        st = _state_machine->do_create_volumes(data, NULL, 2006);
        ASSERT_EQ(st.error_code(), AIE_OK);
        
        uint64_t volume_id = g_volume_manager->get_max_volume_id();
        auto volume = g_volume_manager->get_volume(volume_id);
        ASSERT_EQ(volume->state(), VOLUME_STATE_CREATING);
        ASSERT_EQ(volume->compact_progress(), NONE);
        ASSERT_EQ(volume->compact_vlet_size(), 0);
        ASSERT_EQ(volume->per_vlet_size(), 32 * aries::common::GB);
    }
    {
        //succ and is leader
        g_meta_data->set_state(MASTER_STATE_LEADER_WORKING);
        aries::pb::CreateVolumesRequest request;
        request.set_token("default_token");
        request.set_space_name("test_space1");
        request.set_volume_num(1);

        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);
        base::Status st;
        st = _state_machine->do_create_volumes(data, NULL, 1);
        ASSERT_EQ(st.error_code(), AIE_OK);
        uint64_t volume_id = g_volume_manager->get_max_volume_id();
        auto volume = g_volume_manager->get_volume(volume_id);
        ASSERT_EQ(volume->state(), VOLUME_STATE_CREATING);
        ASSERT_EQ(volume->compact_progress(), NONE);
        ASSERT_EQ(volume->compact_vlet_size(), 0);
        ASSERT_EQ(volume->per_vlet_size(), 32 * aries::common::GB);
        ASSERT_EQ(volume->permit_data_offset_index(), false);
    }
    {
        //archive volume
        g_meta_data->set_state(MASTER_STATE_LEADER_WORKING);
        auto space = g_space_manager->get_space("test_space1");
        space->set_archive_volume(true);
        aries::pb::CreateVolumesRequest request;
        request.set_token("default_token");
        request.set_space_name("test_space1");
        request.set_volume_num(1);

        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);
        base::Status st;
        st = _state_machine->do_create_volumes(data, NULL, 1);
        ASSERT_EQ(st.error_code(), AIE_OK);
        uint64_t volume_id = g_volume_manager->get_max_volume_id();
        auto volume = g_volume_manager->get_volume(volume_id);
        ASSERT_EQ(volume->state(), VOLUME_STATE_CREATING);
        ASSERT_EQ(volume->compact_progress(), NONE);
        ASSERT_EQ(volume->compact_vlet_size(), 0);
        ASSERT_EQ(volume->per_vlet_size(), 32 * aries::common::GB);
        ASSERT_EQ(volume->permit_data_offset_index(), false);
    }
}

TEST_F(MasterStateMachineTests, test_create_vlet) {
    {
        //proto err
        base::IOBuf err_data = prepare_err_data();
        base::Status st;
        st = _state_machine->do_create_vlet(err_data, NULL, 1);
        ASSERT_EQ(st.error_code(), AIE_FAIL);
    }

    {
        //volume not exist.
        aries::pb::MasterCreateVletRequest request;
        request.set_token("default_token");
        auto vlet_info = request.mutable_vlet_info();
        vlet_info->set_volume_id(100);
        vlet_info->set_shard_index(0);
        vlet_info->set_node_addr(common::endpoint2int(_p_meta.get_node(4)));
        vlet_info->set_disk_id(1);
        vlet_info->set_vlet_type(VLET_TYPE_LINKED_16G_1M_512K);
        vlet_info->set_vlet_version(1);
        auto vlet_engien_info = vlet_info->mutable_vlet_engine_options();
        ASSERT_TRUE(common::vlet_engine_by_vlet_type(VLET_TYPE_LINKED_16G_1M_512K, vlet_engien_info));

        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);
        base::Status st;
        st = _state_machine->do_create_vlet(data, NULL, 1);
        ASSERT_EQ(st.error_code(), AIE_NOT_EXIST);
    }

    {
        //vlet is normal
        aries::pb::MasterCreateVletRequest request;
        request.set_token("default_token");
        auto vlet_info = request.mutable_vlet_info();
        vlet_info->set_volume_id(3);
        vlet_info->set_shard_index(0);
        vlet_info->set_node_addr(common::endpoint2int(_p_meta.get_node(4)));
        vlet_info->set_disk_id(1);
        vlet_info->set_vlet_type(VLET_TYPE_LINKED_16G_1M_512K);
        vlet_info->set_state(VLET_STATE_NORMAL);
        vlet_info->set_vlet_version(1);
        auto vlet_engien_info = vlet_info->mutable_vlet_engine_options();
        ASSERT_TRUE(common::vlet_engine_by_vlet_type(VLET_TYPE_LINKED_16G_1M_512K, vlet_engien_info));

        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);
        base::Status st;
        st = _state_machine->do_create_vlet(data, NULL, 1);
        ASSERT_EQ(st.error_code(), AIE_FAIL);
    }

    {
        //node is not exist
        aries::pb::MasterCreateVletRequest request;
        request.set_token("default_token");
        auto vlet_info = request.mutable_vlet_info();
        vlet_info->set_volume_id(1);
        vlet_info->set_shard_index(0);
        vlet_info->set_node_addr(common::endpoint2int(_p_meta.get_node(0)));
        vlet_info->set_disk_id(1);
        vlet_info->set_vlet_type(VLET_TYPE_LINKED_16G_1M_512K);
        vlet_info->set_state(VLET_STATE_NORMAL);
        vlet_info->set_vlet_version(1);
        auto vlet_engien_info = vlet_info->mutable_vlet_engine_options();
        ASSERT_TRUE(common::vlet_engine_by_vlet_type(VLET_TYPE_LINKED_16G_1M_512K, vlet_engien_info));

        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);
        base::Status st;
        st = _state_machine->do_create_vlet(data, NULL, 1);
        ASSERT_EQ(st.error_code(), AIE_FAIL);
    }
    {
        //node has same volume
        aries::pb::MasterCreateVletRequest request;
        request.set_token("default_token");
        auto vlet_info = request.mutable_vlet_info();
        vlet_info->set_volume_id(4);
        vlet_info->set_shard_index(0);
        vlet_info->set_node_addr(common::endpoint2int(_p_meta.get_node(5)));
        vlet_info->set_disk_id(1);
        vlet_info->set_vlet_type(VLET_TYPE_LINKED_16G_1M_512K);
        vlet_info->set_state(VLET_STATE_NORMAL);
        vlet_info->set_vlet_version(1);
        auto vlet_engien_info = vlet_info->mutable_vlet_engine_options();
        ASSERT_TRUE(common::vlet_engine_by_vlet_type(VLET_TYPE_LINKED_16G_1M_512K, vlet_engien_info));

        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);
        base::Status st;
        st = _state_machine->do_create_vlet(data, NULL, 1);
        ASSERT_EQ(st.error_code(), AIE_FAIL);
    }
    {
        //node state not right
        aries::pb::MasterCreateVletRequest request;
        request.set_token("default_token");
        auto vlet_info = request.mutable_vlet_info();
        vlet_info->set_volume_id(1);
        vlet_info->set_shard_index(0);
        vlet_info->set_node_addr(common::endpoint2int(_p_meta.get_node(1)));
        vlet_info->set_disk_id(1);
        vlet_info->set_vlet_type(VLET_TYPE_LINKED_16G_1M_512K);
        vlet_info->set_state(VLET_STATE_NORMAL);
        vlet_info->set_vlet_version(1);
        auto vlet_engien_info = vlet_info->mutable_vlet_engine_options();
        ASSERT_TRUE(common::vlet_engine_by_vlet_type(VLET_TYPE_LINKED_16G_1M_512K, vlet_engien_info));

        auto node = g_node_manager->get_node(_p_meta.get_node(1));
        node->_state = NODE_STATE_DECOMMISSIONED;

        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);
        base::Status st;
        st = _state_machine->do_create_vlet(data, NULL, 1);
        ASSERT_EQ(st.error_code(), AIE_FAIL);
        std::string msg = "node state error";
        ASSERT_EQ(msg, st.error_cstr());

        node->_state = NODE_STATE_NORMAL;
    }
    {
        //disk not exist
        aries::pb::MasterCreateVletRequest request;
        request.set_token("default_token");
        auto vlet_info = request.mutable_vlet_info();
        vlet_info->set_volume_id(1);
        vlet_info->set_shard_index(0);
        vlet_info->set_node_addr(common::endpoint2int(_p_meta.get_node(1)));
        vlet_info->set_disk_id(10);
        vlet_info->set_vlet_type(VLET_TYPE_LINKED_16G_1M_512K);
        vlet_info->set_state(VLET_STATE_NORMAL);
        vlet_info->set_vlet_version(1);
        auto vlet_engien_info = vlet_info->mutable_vlet_engine_options();
        ASSERT_TRUE(common::vlet_engine_by_vlet_type(VLET_TYPE_LINKED_16G_1M_512K, vlet_engien_info));

        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);
        base::Status st;
        st = _state_machine->do_create_vlet(data, NULL, 1);
        ASSERT_EQ(st.error_code(), AIE_FAIL);
    }
    {
        //succ
        aries::pb::MasterCreateVletRequest request;
        request.set_token("default_token");
        auto vlet_info = request.mutable_vlet_info();
        vlet_info->set_volume_id(1);
        vlet_info->set_shard_index(1);
        vlet_info->set_node_addr(common::endpoint2int(_p_meta.get_node(2)));
        vlet_info->set_disk_id(1);
        vlet_info->set_vlet_type(VLET_TYPE_LINKED_16G_1M_512K);
        vlet_info->set_state(VLET_STATE_NORMAL);
        vlet_info->set_vlet_version(1);
        auto vlet_engien_info = vlet_info->mutable_vlet_engine_options();
        ASSERT_TRUE(common::vlet_engine_by_vlet_type(VLET_TYPE_LINKED_16G_1M_512K, vlet_engien_info));

        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);
        base::Status st;
        st = _state_machine->do_create_vlet(data, NULL, 1);
        ASSERT_EQ(st.error_code(), AIE_OK);
        auto volume = g_volume_manager->get_volume(1);
        auto vlet = volume->get_vlet(1);
        ASSERT_EQ(vlet->state(), VLET_STATE_NORMAL);
        ASSERT_EQ(vlet->vlet_type(), VLET_TYPE_LINKED_16G_1M_512K);
        ASSERT_EQ(vlet->vlet_engine_info_ptr()->vlet_size(), 16 * aries::common::GB);
    }
    //test create all creating state vlet in creating state volume. check volume state
    {
        auto volume = g_volume_manager->get_volume(1);
        ASSERT_EQ(volume->state(), VOLUME_STATE_CREATING);

        aries::pb::MasterCreateVletRequest request;
        request.set_token("default_token");
        auto vlet_info = request.mutable_vlet_info();
        vlet_info->set_volume_id(1);
        vlet_info->set_shard_index(0);
        vlet_info->set_node_addr(common::endpoint2int(_p_meta.get_node(3)));
        vlet_info->set_disk_id(1);
        vlet_info->set_vlet_type(VLET_TYPE_LINKED_16G_1M_512K);
        vlet_info->set_state(VLET_STATE_NORMAL);
        vlet_info->set_vlet_version(1);
        auto vlet_engien_info = vlet_info->mutable_vlet_engine_options();
        ASSERT_TRUE(common::vlet_engine_by_vlet_type(VLET_TYPE_LINKED_16G_1M_512K, vlet_engien_info));

        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);
        base::Status st;
        st = _state_machine->do_create_vlet(data, NULL, 1);
        ASSERT_EQ(st.error_code(), AIE_OK);
        ASSERT_EQ(volume->state(), VOLUME_STATE_NORMAL);
        auto vlet = volume->get_vlet(0);
        ASSERT_EQ(vlet->state(), VLET_STATE_NORMAL);
        ASSERT_EQ(vlet->vlet_type(), VLET_TYPE_LINKED_16G_1M_512K);
        ASSERT_EQ(vlet->vlet_engine_info_ptr()->vlet_size(), 16 * aries::common::GB);
    }
    {
        //vlet to be repairing succ
        aries::pb::MasterCreateVletRequest request;
        request.set_token("default_token");
        auto vlet_info = request.mutable_vlet_info();
        vlet_info->set_volume_id(2);
        vlet_info->set_shard_index(0);
        vlet_info->set_node_addr(common::endpoint2int(_p_meta.get_node(2)));
        vlet_info->set_disk_id(1);
        vlet_info->set_vlet_type(VLET_TYPE_LINKED_16G_1M_512K);
        vlet_info->set_state(VLET_STATE_REPAIRING);
        vlet_info->set_vlet_version(1);
        auto vlet_engien_info = vlet_info->mutable_vlet_engine_options();
        ASSERT_TRUE(common::vlet_engine_by_vlet_type(VLET_TYPE_LINKED_16G_1M_512K, vlet_engien_info));

        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);
        base::Status st;
        st = _state_machine->do_create_vlet(data, NULL, 2007);
        ASSERT_EQ(st.error_code(), AIE_OK);

        auto volume = g_volume_manager->get_volume(2);
        ASSERT_EQ(volume->_raft_index, 2007);
        auto vlet = volume->get_vlet(0);
        ASSERT_EQ(vlet->state(), VLET_STATE_REPAIRING);
        ASSERT_EQ(vlet->vlet_type(), VLET_TYPE_LINKED_16G_1M_512K);
        ASSERT_EQ(vlet->vlet_engine_info_ptr()->vlet_size(), 16 * aries::common::GB);
    }
    {
        //vlet engine opertion error
        aries::pb::MasterCreateVletRequest request;
        request.set_token("default_token");
        auto vlet_info = request.mutable_vlet_info();
        vlet_info->set_volume_id(2);
        vlet_info->set_shard_index(1);
        vlet_info->set_node_addr(common::endpoint2int(_p_meta.get_node(3)));
        vlet_info->set_disk_id(1);
        vlet_info->set_vlet_type(VLET_TYPE_LINKED_VARIENT);
        vlet_info->set_state(VLET_STATE_REPAIRING);
        vlet_info->set_vlet_version(1);

        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);
        base::Status st;
        st = _state_machine->do_create_vlet(data, NULL, 2008);
        ASSERT_EQ(st.error_code(), AIE_FAIL);
    }
    {
        //vlet engine opertion succ
        aries::pb::MasterCreateVletRequest request;
        request.set_token("default_token");
        auto vlet_info = request.mutable_vlet_info();
        vlet_info->set_volume_id(2);
        vlet_info->set_shard_index(1);
        vlet_info->set_node_addr(common::endpoint2int(_p_meta.get_node(3)));
        vlet_info->set_disk_id(1);
        vlet_info->set_vlet_type(VLET_TYPE_LINKED_16G_1M_512K);
        vlet_info->set_state(VLET_STATE_REPAIRING);
        vlet_info->set_vlet_version(1);

        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);
        base::Status st;
        st = _state_machine->do_create_vlet(data, NULL, 2009);
        ASSERT_EQ(st.error_code(), AIE_OK);

        auto volume = g_volume_manager->get_volume(2);
        ASSERT_EQ(volume->_raft_index, 2009);
        auto vlet = volume->get_vlet(0);
        ASSERT_EQ(vlet->state(), VLET_STATE_REPAIRING);
        ASSERT_EQ(vlet->vlet_type(), VLET_TYPE_LINKED_16G_1M_512K);
        ASSERT_EQ(vlet->vlet_engine_info_ptr()->vlet_size(), 16 * aries::common::GB);
    }
}

TEST_F(MasterStateMachineTests, test_vlet_for_repair_finish) {
    {
        //err req
        base::IOBuf err_data = prepare_err_data();
        base::Status st;
        st = _state_machine->do_repair_vlet_finish(err_data, NULL, 1);
        ASSERT_EQ(st.error_code(), AIE_FAIL);
    }

    {
        //vlet to be normal from repairing version not same.
        aries::pb::ReportRepairVletFinishRequest request;
        request.set_token("default_token");
        auto vlet_info = request.mutable_vlet_info();
        vlet_info->set_volume_id(5);
        vlet_info->set_shard_index(0);
        vlet_info->set_node_addr(common::endpoint2int(_p_meta.get_node(2)));
        vlet_info->set_disk_id(1);
        vlet_info->set_vlet_type(VLET_TYPE_LINKED_16G_1M_512K);
        vlet_info->set_state(VLET_STATE_NORMAL);
        vlet_info->set_vlet_version(1);

        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);
        base::Status st;
        st = _state_machine->do_repair_vlet_finish(data, NULL, 1);
        //version not same;
        ASSERT_EQ(st.error_code(), AIE_FAIL);
    }
    {
        //vlet to be normal from repairing succ.
        aries::pb::ReportRepairVletFinishRequest request;
        request.set_token("default_token");
        auto vlet_info = request.mutable_vlet_info();
        vlet_info->set_volume_id(5);
        vlet_info->set_shard_index(0);
        vlet_info->set_node_addr(common::endpoint2int(_p_meta.get_node(2)));
        vlet_info->set_disk_id(1);
        vlet_info->set_vlet_type(VLET_TYPE_LINKED_16G_1M_512K);
        vlet_info->set_state(VLET_STATE_NORMAL);
        vlet_info->set_vlet_version(0);

        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);
        base::Status st;
        st = _state_machine->do_repair_vlet_finish(data, NULL, 2008);
        //version not same;
        ASSERT_EQ(st.error_code(), AIE_OK);

        auto volume = g_volume_manager->get_volume(5);
        ASSERT_EQ(volume->_raft_index, 2008);
    }
    {
        //volume not exist
        aries::pb::ReportRepairVletFinishRequest request;
        request.set_token("default_token");
        auto vlet_info = request.mutable_vlet_info();
        vlet_info->set_volume_id(111);
        vlet_info->set_shard_index(0);
        vlet_info->set_node_addr(common::endpoint2int(_p_meta.get_node(2)));
        vlet_info->set_disk_id(1);
        vlet_info->set_vlet_type(VLET_TYPE_LINKED_16G_1M_512K);
        vlet_info->set_state(VLET_STATE_NORMAL);
        vlet_info->set_vlet_version(0);

        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);
        base::Status st;
        st = _state_machine->do_repair_vlet_finish(data, NULL, 1);
        //version not same;
        ASSERT_EQ(st.error_code(), AIE_NOT_EXIST);
    }
    {
        //vlet not exist
        aries::pb::ReportRepairVletFinishRequest request;
        request.set_token("default_token");
        auto vlet_info = request.mutable_vlet_info();
        vlet_info->set_volume_id(5);
        vlet_info->set_shard_index(9);
        vlet_info->set_node_addr(common::endpoint2int(_p_meta.get_node(2)));
        vlet_info->set_disk_id(1);
        vlet_info->set_vlet_type(VLET_TYPE_LINKED_16G_1M_512K);
        vlet_info->set_state(VLET_STATE_NORMAL);
        vlet_info->set_vlet_version(0);

        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);
        base::Status st;
        st = _state_machine->do_repair_vlet_finish(data, NULL, 1);
        //version not same;
        ASSERT_EQ(st.error_code(), AIE_FAIL);
    }
    {
        //vlet status not right.
        aries::pb::ReportRepairVletFinishRequest request;
        request.set_token("default_token");
        auto vlet_info = request.mutable_vlet_info();
        vlet_info->set_volume_id(5);
        vlet_info->set_shard_index(1);
        vlet_info->set_node_addr(common::endpoint2int(_p_meta.get_node(2)));
        vlet_info->set_disk_id(1);
        vlet_info->set_vlet_type(VLET_TYPE_LINKED_16G_1M_512K);
        vlet_info->set_state(VLET_STATE_NORMAL);
        vlet_info->set_vlet_version(0);

        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);
        base::Status st;
        st = _state_machine->do_repair_vlet_finish(data, NULL, 1);
        //version not same;
        ASSERT_EQ(st.error_code(), AIE_FAIL);
    }
}

TEST_F(MasterStateMachineTests, test_assign_allocator_sequence_id) {
    {
        //proto err
        base::IOBuf err_data = prepare_err_data();
        base::Status st;
        st = _state_machine->do_assign_allocator_sequence_id(err_data, NULL, 1);
        ASSERT_EQ(st.error_code(), AIE_FAIL);
    }
    {
        aries::pb::AssignAllocatorSequenceIdRequest request;
        aries::pb::AssignAllocatorSequenceIdResponse response;
        request.set_allocator_name("notexistAllocator");

        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);
        base::Status st;
        st = _state_machine->do_assign_allocator_sequence_id(data, NULL, 1);
        ASSERT_EQ(st.error_code(), AIE_NOT_EXIST);
    }
    {
        aries::pb::AssignAllocatorSequenceIdRequest request;
        aries::pb::AssignAllocatorSequenceIdResponse response;
        request.set_allocator_name("allocator1");

        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);
        base::Status st;
        st = _state_machine->do_assign_allocator_sequence_id(data, NULL, 1);
        ASSERT_EQ(st.error_code(), AIE_OK);
    }
}

TEST_F(MasterStateMachineTests, test_set_membership_finish) {
    //test vlet is dropping
    {
        g_meta_data->set_state(MASTER_STATE_LEADER_WORKING);
        aries::pb::SetMembershipFinishRequest request;
        auto vlet_info = request.mutable_vlet_info();
        vlet_info->set_volume_id(6);
        vlet_info->set_shard_index(0);
        vlet_info->set_node_addr(common::endpoint2int(_p_meta.get_node(4)));
        vlet_info->set_disk_id(1);
        vlet_info->set_vlet_type(VLET_TYPE_LINKED_16G_1M_512K);
        vlet_info->set_vlet_version(0);
        vlet_info->set_state(VLET_STATE_DROPPING);

        auto volume = g_volume_manager->get_volume(6);
        auto vlet = volume->get_vlet(0);
        ASSERT_EQ(vlet->state(), VLET_STATE_DROPPING);

        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);
        base::Status st;
        st = _state_machine->do_set_membership_finish(data, 1);
        ASSERT_EQ(st.error_code(), AIE_OK);
        ASSERT_EQ(vlet->state(), VLET_STATE_RECOVERING);
    }
    //test vlet is joinning
    {
        aries::pb::SetMembershipFinishRequest request;
        auto vlet_info = request.mutable_vlet_info();
        vlet_info->set_volume_id(7);
        vlet_info->set_shard_index(0);
        vlet_info->set_node_addr(common::endpoint2int(_p_meta.get_node(4)));
        vlet_info->set_disk_id(1);
        vlet_info->set_vlet_type(VLET_TYPE_LINKED_16G_1M_512K);
        vlet_info->set_vlet_version(0);
        vlet_info->set_state(VLET_STATE_JOINING);

        auto volume = g_volume_manager->get_volume(7);
        auto vlet = volume->get_vlet(0);
        ASSERT_EQ(vlet->state(), VLET_STATE_JOINING);

        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);
        base::Status st;
        st = _state_machine->do_set_membership_finish(data, 2009);
        ASSERT_EQ(st.error_code(), AIE_OK);
        ASSERT_EQ(vlet->state(), VLET_STATE_REPAIRING);

        ASSERT_EQ(volume->_raft_index, 2009);
    }
}

TEST_F(MasterStateMachineTests, test_add_and_drop_allocator) {

    //cleart prepair allocator
    g_meta_data->_allocator_manager->_allocator_map.clear();
    g_meta_data->_allocator_manager->_max_sequence_id= 0;

    {
        //proto err
        base::IOBuf err_data = prepare_err_data();
        base::Status st;
        st = _state_machine->do_add_allocator(err_data, NULL, 1);
        ASSERT_EQ(st.error_code(), AIE_FAIL);
        st = _state_machine->do_drop_allocator(err_data, NULL, 1);
        ASSERT_EQ(st.error_code(), AIE_FAIL);
    }
    {
        //succ allocator is not exist.
        aries::pb::AddAllocatorRequest request;
        request.set_token("default_token");
        std::string addr_name("127.0.0.1:8000");
        base::EndPoint addr;
        ASSERT_EQ(0, common::str2endpoint(addr_name.c_str(), &addr));
        request.set_allocator_addr(common::endpoint2int(addr));
        request.set_allocator_name("Allocator1");
        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);

        base::Status st;
        st = _state_machine->do_add_allocator(data, NULL, 2010);
        ASSERT_EQ(st.error_code(), AIE_OK);
        ASSERT_EQ(g_allocator_manager->_max_sequence_id, 1);

    }
    {
        //succ allocator 2
        aries::pb::AddAllocatorRequest request;
        request.set_token("default_token");
        std::string addr_name("127.0.0.9:8000");
        base::EndPoint addr;
        ASSERT_EQ(0, common::str2endpoint(addr_name.c_str(), &addr));
        request.set_allocator_addr(common::endpoint2int(addr));
        request.set_allocator_name("Allocator2");
        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);

        base::Status st;
        st = _state_machine->do_add_allocator(data, NULL, 1);
        ASSERT_EQ(st.error_code(), AIE_OK);
        ASSERT_EQ(g_allocator_manager->_max_sequence_id, 3);
    }
    {
        //succ change ip.
        aries::pb::AddAllocatorRequest request;
        request.set_token("default_token");
        std::string addr_name("*********:8000");
        base::EndPoint addr;
        ASSERT_EQ(0, common::str2endpoint(addr_name.c_str(), &addr));
        request.set_allocator_addr(common::endpoint2int(addr));
        request.set_allocator_name("Allocator1");
        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);

        base::Status st;
        st = _state_machine->do_add_allocator(data, NULL, 1);
        ASSERT_EQ(g_allocator_manager->_max_sequence_id, 3);
        ASSERT_EQ(st.error_code(), AIE_OK);
    }
    {
        //succ allocator is not exist.
        aries::pb::AssignAllocatorSequenceIdRequest request;
        request.set_token("default_token");
        //std::string addr_name("*********:8000");
        //base::EndPoint addr;
        //ASSERT_EQ(0, common::str2endpoint(addr_name.c_str(), &addr));
        //request.set_allocator_addr(common::endpoint2int(addr));
        request.set_allocator_name("Allocator1");
        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);

        base::Status st;
        st = _state_machine->do_assign_allocator_sequence_id(data, NULL, 1);
        ASSERT_EQ(st.error_code(), AIE_OK);
        ASSERT_EQ(g_allocator_manager->_max_sequence_id, 4);
    }
    //todo
    {
        //fail exist
        aries::pb::AddAllocatorRequest request;
        request.set_token("default_token");
        std::string addr_name("*********:8000");
        base::EndPoint addr;
        ASSERT_EQ(0, common::str2endpoint(addr_name.c_str(), &addr));
        request.set_allocator_addr(common::endpoint2int(addr));
        request.set_allocator_name("Allocator1");
        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);

        base::Status st;
        st = _state_machine->do_add_allocator(data, NULL, 1);
        ASSERT_EQ(st.error_code(), AIE_EXIST);
    }
    {
        //drop succ
        aries::pb::DropAllocatorRequest request;
        request.set_token("default_token");
        request.set_allocator_name("Allocator1");
        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);

        base::Status st;
        st = _state_machine->do_drop_allocator(data, NULL, 1);
        ASSERT_EQ(st.error_code(), AIE_OK);
        ASSERT_EQ(g_allocator_manager->_max_sequence_id, 5);
    }
    {
        //drop fail, not exist.
        aries::pb::DropAllocatorRequest request;
        request.set_token("default_token");
        request.set_allocator_name("Allocator1");
        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);

        base::Status st;
        st = _state_machine->do_drop_allocator(data, NULL, 1);
        ASSERT_EQ(st.error_code(), AIE_NOT_EXIST);
    }
}

TEST_F(MasterStateMachineTests, test_add_and_drop_tapecenter) {
    //cleart prepair tapecenter
    g_meta_data->_tape_center_manager->_tape_center_map.clear();

    {
        //proto err
        base::IOBuf err_data = prepare_err_data();
        base::Status st;
        st = _state_machine->do_add_tape_center(err_data, NULL, 1);
        ASSERT_EQ(st.error_code(), AIE_FAIL);
        st = _state_machine->do_drop_tape_center(err_data, NULL, 1);
        ASSERT_EQ(st.error_code(), AIE_FAIL);
    }
    {
        //succ tapecenter is not exist.
        aries::pb::AddTapeCenterRequest request;
        request.set_token("default_token");
        std::string addr_name("127.0.0.1:8920");
        base::EndPoint addr;
        ASSERT_EQ(0, common::str2endpoint(addr_name.c_str(), &addr));
        request.set_tape_center_addr(common::endpoint2int(addr));
        request.set_tape_center_name("tc1");
        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);

        base::Status st;
        st = _state_machine->do_add_tape_center(data, NULL, 2010);
        ASSERT_EQ(st.error_code(), AIE_OK);
        ASSERT_EQ(g_tape_center_manager->_tape_center_map.size(), 1);
    }
    {
        //succ tapecenter 2
        aries::pb::AddTapeCenterRequest request;
        request.set_token("default_token");
        std::string addr_name("127.0.0.9:8920");
        base::EndPoint addr;
        ASSERT_EQ(0, common::str2endpoint(addr_name.c_str(), &addr));
        request.set_tape_center_addr(common::endpoint2int(addr));
        request.set_tape_center_name("tc2");
        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);

        base::Status st;
        st = _state_machine->do_add_tape_center(data, NULL, 1);
        ASSERT_EQ(st.error_code(), AIE_OK);
        ASSERT_EQ(g_tape_center_manager->_tape_center_map.size(), 2);
    }
    {
        //fail tapecenter is  exist.
        aries::pb::AddTapeCenterRequest request;
        request.set_token("default_token");
        std::string addr_name("127.0.0.1:8920");
        base::EndPoint addr;
        ASSERT_EQ(0, common::str2endpoint(addr_name.c_str(), &addr));
        request.set_tape_center_addr(common::endpoint2int(addr));
        request.set_tape_center_name("tc1");
        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);

        base::Status st;
        st = _state_machine->do_add_tape_center(data, NULL, 2010);
        ASSERT_EQ(st.error_code(), AIE_EXIST);
        ASSERT_EQ(g_tape_center_manager->_tape_center_map.size(), 2);
    }
    {
        //drop succ
        aries::pb::DropTapeCenterRequest request;
        request.set_token("default_token");
        std::string addr_name("127.0.0.1:8920");
        base::EndPoint addr;
        ASSERT_EQ(0, common::str2endpoint(addr_name.c_str(), &addr));
        request.set_tape_center_addr(common::endpoint2int(addr));
        request.set_tape_center_name("tc1");
        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);

        base::Status st;
        st = _state_machine->do_drop_tape_center(data, NULL, 1);
        ASSERT_EQ(st.error_code(), AIE_OK);
        ASSERT_EQ(g_tape_center_manager->_tape_center_map.size(), 1);
    }
    {
        //drop fail not exist
        aries::pb::DropTapeCenterRequest request;
        request.set_token("default_token");
        std::string addr_name("127.0.0.1:8920");
        base::EndPoint addr;
        ASSERT_EQ(0, common::str2endpoint(addr_name.c_str(), &addr));
        request.set_tape_center_addr(common::endpoint2int(addr));
        request.set_tape_center_name("tc1");
        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);

        base::Status st;
        st = _state_machine->do_drop_tape_center(data, NULL, 1);
        ASSERT_EQ(st.error_code(), AIE_NOT_EXIST);
        ASSERT_EQ(g_tape_center_manager->_tape_center_map.size(), 1);
    }
}

TEST_F(MasterStateMachineTests, test_add_and_drop_tape_library_group) {
    g_meta_data->_tape_library_manager->_library_map.clear();

    {
        //proto err
        base::IOBuf err_data = prepare_err_data();
        base::Status st;
        st = _state_machine->do_add_tape_library_group(err_data, NULL, 1);
        ASSERT_EQ(st.error_code(), AIE_FAIL);
        st = _state_machine->do_drop_tape_library_group(err_data, NULL, 1);
        ASSERT_EQ(st.error_code(), AIE_FAIL);
    }
    {
        //succ
        aries::pb::AddTapeLibraryGroupRequest request;
        request.set_token("default_token");
        aries::pb::TapeLibraryGroup group_info;
        
        group_info.set_group_name("group01");

        request.set_token(FLAGS_token);
        request.mutable_group_info()->CopyFrom(group_info);

        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);

        base::Status st;
        st = _state_machine->do_add_tape_library_group(data, NULL, 2010);
        ASSERT_EQ(st.error_code(), AIE_OK);
        ASSERT_EQ(g_tape_library_manager->_library_map.size(), 1);
    }
    {
        //succ 2
        aries::pb::AddTapeLibraryGroupRequest request;
        request.set_token("default_token");
        aries::pb::TapeLibraryGroup group_info;
        group_info.set_group_name("group02");

        request.set_token(FLAGS_token);
        request.mutable_group_info()->CopyFrom(group_info);

        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);

        base::Status st;
        st = _state_machine->do_add_tape_library_group(data, NULL, 2010);
        ASSERT_EQ(st.error_code(), AIE_OK);
        ASSERT_EQ(g_tape_library_manager->_library_map.size(), 2);
    }
    {
        //fail is exist.
        aries::pb::AddTapeLibraryGroupRequest request;
        request.set_token("default_token");
        aries::pb::TapeLibraryGroup group_info;
        
        group_info.set_group_name("group01");

        request.set_token(FLAGS_token);
        request.mutable_group_info()->CopyFrom(group_info);

        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);

        base::Status st;
        st = _state_machine->do_add_tape_library_group(data, NULL, 2010);
        ASSERT_EQ(st.error_code(), AIE_EXIST);
        ASSERT_EQ(g_tape_library_manager->_library_map.size(), 2);
    }
    {
        //drop succ
        aries::pb::DropTapeLibraryGroupRequest request;
        request.set_token("default_token");
        request.set_group_name("group01");

        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);

        base::Status st;
        st = _state_machine->do_drop_tape_library_group(data, NULL, 1);
        ASSERT_EQ(st.error_code(), AIE_OK);
        ASSERT_EQ(g_tape_library_manager->_library_map.size(), 1);
    }
    {
        //drop fail not exist
        aries::pb::DropTapeLibraryGroupRequest request;
        request.set_token("default_token");
        request.set_group_name("group01");

        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);

        base::Status st;
        st = _state_machine->do_drop_tape_library_group(data, NULL, 1);
        ASSERT_EQ(st.error_code(), AIE_NOT_EXIST);
        ASSERT_EQ(g_tape_library_manager->_library_map.size(), 1);
    }
}

TEST_F(MasterStateMachineTests, test_add_and_drop_tape_logical_pool) {
    g_meta_data->_tape_pool_manager->_pool_map.clear();

    {
        //proto err
        base::IOBuf err_data = prepare_err_data();
        base::Status st;
        st = _state_machine->do_add_tape_logical_pool(err_data, NULL, 1);
        ASSERT_EQ(st.error_code(), AIE_FAIL);
        st = _state_machine->do_drop_tape_logical_pool(err_data, NULL, 1);
        ASSERT_EQ(st.error_code(), AIE_FAIL);
        st = _state_machine->do_update_tape_logical_pool(err_data, NULL, 1);
        ASSERT_EQ(st.error_code(), AIE_FAIL);
    }
    {
        //add group.
        aries::pb::AddTapeLibraryGroupRequest request;
        request.set_token("default_token");
        aries::pb::TapeLibraryGroup group_info;
        
        group_info.set_group_name("group01");

        request.set_token(FLAGS_token);
        request.mutable_group_info()->CopyFrom(group_info);

        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);

        base::Status st;
        st = _state_machine->do_add_tape_library_group(data, NULL, 2010);
        ASSERT_EQ(st.error_code(), AIE_OK);
        ASSERT_EQ(g_tape_library_manager->_library_map.size(), 1);
    }
    {
        //succ .
        aries::pb::AddTapeLogicalPoolRequest request;
        request.set_token("default_token");
        aries::pb::TapeLogicalPool pool_info;
        
        pool_info.set_pool_name("pool01");
        request.set_group_name("group01");
        request.set_token(FLAGS_token);
        request.mutable_pool_info()->CopyFrom(pool_info);

        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);

        base::Status st;
        st = _state_machine->do_add_tape_logical_pool(data, NULL, 2010);
        ASSERT_EQ(st.error_code(), AIE_OK);
        ASSERT_EQ(g_tape_pool_manager->_pool_map.size(), 1);
    }
    {
        //succ 2
        aries::pb::AddTapeLogicalPoolRequest request;
        request.set_token("default_token");
        aries::pb::TapeLogicalPool pool_info;
        
        pool_info.set_pool_name("pool02");
        request.set_group_name("group01");
        request.set_token(FLAGS_token);
        request.mutable_pool_info()->CopyFrom(pool_info);

        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);

        base::Status st;
        st = _state_machine->do_add_tape_logical_pool(data, NULL, 2010);
        ASSERT_EQ(st.error_code(), AIE_OK);
        ASSERT_EQ(g_tape_pool_manager->_pool_map.size(), 2);
    }
    {
        //update not exist
        aries::pb::UpdateTapeLogicalPoolRequest request;
        request.set_token("default_token");
        aries::pb::TapeLogicalPool pool_info;
        
        pool_info.set_pool_name("pool03");
        request.set_group_name("group01");
        request.set_token(FLAGS_token);
        request.mutable_pool_info()->CopyFrom(pool_info);

        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);

        base::Status st;
        st = _state_machine->do_update_tape_logical_pool(data, NULL, 2010);
        ASSERT_EQ(st.error_code(), AIE_NOT_EXIST);
    }
    {
        //update not exist
        aries::pb::UpdateTapeLogicalPoolRequest request;
        request.set_token("default_token");
        aries::pb::TapeLogicalPool pool_info;
        pool_info.set_tape_count(100);
        pool_info.add_physical_pool_names("physical_pool01");
        pool_info.add_physical_pool_names("physical_pool02");
        
        pool_info.set_pool_name("pool02");
        request.set_group_name("group01");
        request.set_token(FLAGS_token);
        request.mutable_pool_info()->CopyFrom(pool_info);

        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);

        base::Status st;
        st = _state_machine->do_update_tape_logical_pool(data, NULL, 2010);
        ASSERT_EQ(st.error_code(), AIE_OK);
        auto pool = g_tape_pool_manager->get_pool("pool02");
        ASSERT_EQ(pool->tape_count(), 100);
        ASSERT_EQ(pool->physical_pool_names().size(), 2);
    }
    {
        //fail exist.
        aries::pb::AddTapeLogicalPoolRequest request;
        request.set_token("default_token");
        aries::pb::TapeLogicalPool pool_info;
        
        pool_info.set_pool_name("pool01");
        request.set_group_name("group01");
        request.set_token(FLAGS_token);
        request.mutable_pool_info()->CopyFrom(pool_info);

        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);

        base::Status st;
        st = _state_machine->do_add_tape_logical_pool(data, NULL, 2010);
        ASSERT_EQ(st.error_code(), AIE_EXIST);
        ASSERT_EQ(g_tape_pool_manager->_pool_map.size(), 2);
    }
    {
        //drop succ
        aries::pb::DropTapeLogicalPoolRequest request;
        request.set_token("default_token");
        request.set_pool_name("pool01");

        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);

        base::Status st;
        st = _state_machine->do_drop_tape_logical_pool(data, NULL, 1);
        ASSERT_EQ(st.error_code(), AIE_OK);
        ASSERT_EQ(g_tape_pool_manager->_pool_map.size(), 1);
    }
    {
        //drop fail not exist
        aries::pb::DropTapeLogicalPoolRequest request;
        request.set_token("default_token");
        request.set_pool_name("group01");

        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);

        base::Status st;
        st = _state_machine->do_drop_tape_logical_pool(data, NULL, 1);
        ASSERT_EQ(st.error_code(), AIE_NOT_EXIST);
        ASSERT_EQ(g_tape_pool_manager->_pool_map.size(), 1);
    }
}

TEST_F(MasterStateMachineTests, test_drop_volume) {
    {
        //volume not exist.
        aries::pb::VolumeRequest request;
        request.set_token("default_token");
        request.set_volume_id(3131331);

        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);
        base::Status st;
        st = _state_machine->do_drop_volume(data, NULL, 1);
        ASSERT_EQ(st.error_code(), AIE_FAIL);
    }
    {
        //succ.
        aries::pb::VolumeRequest request;
        request.set_token("default_token");
        request.set_volume_id(3);

        auto volume = g_volume_manager->get_volume(3);
        auto space = volume->space();
        volume->set_state(VOLUME_STATE_READONLY);

        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);
        base::Status st;
        st = _state_machine->do_drop_volume(data, NULL, 1);
        ASSERT_EQ(st.error_code(), AIE_OK);
    }
}

TEST_F(MasterStateMachineTests, test_disable_volume) {
    {
        //volume not exist.
        aries::pb::VolumeRequest request;
        request.set_token("default_token");
        request.set_volume_id(3131331);

        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);
        base::Status st;
        st = _state_machine->do_disable_volume(data, NULL, 1);
        ASSERT_EQ(st.error_code(), AIE_NOT_EXIST);
    }
    {
        //volume state not normal
        aries::pb::VolumeRequest request;
        request.set_token("default_token");
        request.set_volume_id(3);
        auto volume = g_volume_manager->get_volume(3);
        volume->set_state(VOLUME_STATE_READONLY);

        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);
        base::Status st;
        st = _state_machine->do_disable_volume(data, NULL, 1);
        ASSERT_EQ(st.error_code(), AIE_FAIL);
    }
    {
        //succ.
        aries::pb::VolumeRequest request;
        request.set_token("default_token");
        request.set_volume_id(3);
        auto volume = g_volume_manager->get_volume(3);
        volume->set_state(VOLUME_STATE_NORMAL);

        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);
        base::Status st;
        st = _state_machine->do_disable_volume(data, NULL, 1);
        ASSERT_EQ(st.error_code(), AIE_OK);
    }
}

TEST_F(MasterStateMachineTests, test_add_and_drop_tapenode) {
    //cleart prepair tapenode
    g_meta_data->_tape_node_manager->_tape_node_map.clear();

    {
        //add group.
        aries::pb::AddTapeLibraryGroupRequest request;
        request.set_token("default_token");
        aries::pb::TapeLibraryGroup group_info;
        
        group_info.set_group_name("group01");

        request.set_token(FLAGS_token);
        request.mutable_group_info()->CopyFrom(group_info);

        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);

        base::Status st;
        st = _state_machine->do_add_tape_library_group(data, NULL, 2010);
        ASSERT_EQ(st.error_code(), AIE_OK);
        ASSERT_EQ(g_tape_library_manager->_library_map.size(), 1);
    }
    {
        //succ .
        aries::pb::AddTapeLogicalPoolRequest request;
        request.set_token("default_token");
        aries::pb::TapeLogicalPool pool_info;
        
        pool_info.set_pool_name("pool01");
        request.set_group_name("group01");
        request.set_token(FLAGS_token);
        request.mutable_pool_info()->CopyFrom(pool_info);

        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);

        base::Status st;
        st = _state_machine->do_add_tape_logical_pool(data, NULL, 2010);
        ASSERT_EQ(st.error_code(), AIE_OK);
        ASSERT_EQ(g_tape_pool_manager->_pool_map.size(), 1);
    }


    {
        //proto err
        base::IOBuf err_data = prepare_err_data();
        base::Status st;
        st = _state_machine->do_add_tape_node(err_data, NULL, 1);
        ASSERT_EQ(st.error_code(), AIE_FAIL);
        st = _state_machine->do_drop_tape_node(err_data, NULL, 1);
        ASSERT_EQ(st.error_code(), AIE_FAIL);
    }
    {
        //succ tapenode is not exist.
        aries::pb::AddTapeNodeRequest request;
        request.set_token("default_token");
        std::string addr_name("127.0.0.1:8920");
        base::EndPoint addr;
        ASSERT_EQ(0, common::str2endpoint(addr_name.c_str(), &addr));
        request.set_node_addr(common::endpoint2int(addr));
        request.set_logical_pool_name("pool01");
        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);

        base::Status st;
        st = _state_machine->do_add_tape_node(data, NULL, 2010);
        ASSERT_EQ(st.error_code(), AIE_OK);
        ASSERT_EQ(g_tape_node_manager->_tape_node_map.size(), 1);
    }
    {
        //succ tapenode 2
        aries::pb::AddTapeNodeRequest request;
        request.set_token("default_token");
        std::string addr_name("127.0.0.9:8920");
        base::EndPoint addr;
        ASSERT_EQ(0, common::str2endpoint(addr_name.c_str(), &addr));
        request.set_node_addr(common::endpoint2int(addr));
        request.set_logical_pool_name("pool01");
        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);

        base::Status st;
        st = _state_machine->do_add_tape_node(data, NULL, 1);
        ASSERT_EQ(st.error_code(), AIE_OK);
        ASSERT_EQ(g_tape_node_manager->_tape_node_map.size(), 2);
    }
    {
        //fail tapenode is exist.
        aries::pb::AddTapeNodeRequest request;
        request.set_token("default_token");
        std::string addr_name("127.0.0.1:8920");
        base::EndPoint addr;
        ASSERT_EQ(0, common::str2endpoint(addr_name.c_str(), &addr));
        request.set_node_addr(common::endpoint2int(addr));
        request.set_logical_pool_name("pool01");
        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);

        base::Status st;
        st = _state_machine->do_add_tape_node(data, NULL, 2010);
        ASSERT_EQ(st.error_code(), AIE_EXIST);
        ASSERT_EQ(g_tape_node_manager->_tape_node_map.size(), 2);
    }
    {
        //drop succ
        aries::pb::DropTapeNodeRequest request;
        request.set_token("default_token");
        std::string addr_name("127.0.0.1:8920");
        base::EndPoint addr;
        ASSERT_EQ(0, common::str2endpoint(addr_name.c_str(), &addr));
        request.set_node_addr(common::endpoint2int(addr));
        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);

        base::Status st;
        st = _state_machine->do_drop_tape_node(data, NULL, 1);
        ASSERT_EQ(st.error_code(), AIE_OK);
        ASSERT_EQ(g_tape_node_manager->_tape_node_map.size(), 1);
    }
    {
        //drop fail not exist
        aries::pb::DropTapeNodeRequest request;
        request.set_token("default_token");
        std::string addr_name("127.0.0.1:8920");
        base::EndPoint addr;
        ASSERT_EQ(0, common::str2endpoint(addr_name.c_str(), &addr));
        request.set_node_addr(common::endpoint2int(addr));
        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);

        base::Status st;
        st = _state_machine->do_drop_tape_node(data, NULL, 1);
        ASSERT_EQ(st.error_code(), AIE_NOT_EXIST);
        ASSERT_EQ(g_tape_node_manager->_tape_node_map.size(), 1);
    }
}

TEST_F(MasterStateMachineTests, test_enable_volume) {
    {
        //volume not exist.
        aries::pb::VolumeRequest request;
        request.set_token("default_token");
        request.set_volume_id(3131331);

        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);
        base::Status st;
        st = _state_machine->do_enable_volume(data, NULL, 1);
        ASSERT_EQ(st.error_code(), AIE_NOT_EXIST);
    }
    {
        //volume state not readonly
        aries::pb::VolumeRequest request;
        request.set_token("default_token");
        request.set_volume_id(3);
        auto volume = g_volume_manager->get_volume(3);
        volume->set_state(VOLUME_STATE_NORMAL);

        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);
        base::Status st;
        st = _state_machine->do_enable_volume(data, NULL, 1);
        ASSERT_EQ(st.error_code(), AIE_FAIL);
    }
    {
        //succ.
        aries::pb::VolumeRequest request;
        request.set_token("default_token");
        request.set_volume_id(3);
        auto volume = g_volume_manager->get_volume(3);
        volume->set_state(VOLUME_STATE_READONLY);

        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);
        base::Status st;
        st = _state_machine->do_enable_volume(data, NULL, 1);
        ASSERT_EQ(st.error_code(), AIE_OK);
    }
}

TEST_F(MasterStateMachineTests, test_drop_vlet) {
    {
        //proto err
        base::IOBuf err_data = prepare_err_data();
        base::Status st;
        st = _state_machine->do_drop_vlet(err_data, NULL, 1);
        ASSERT_EQ(st.error_code(), AIE_FAIL);
    }
    {
        //node not exist.
        aries::pb::MasterDropVletRequest request;
        request.set_token("default_token");
        auto vlet_info = request.mutable_vlet_info();
        vlet_info->set_volume_id(1);
        vlet_info->set_shard_index(0);
        vlet_info->set_node_addr(common::endpoint2int(_p_meta.get_node_addr(0)));
        vlet_info->set_disk_id(1);
        vlet_info->set_vlet_type(VLET_TYPE_LINKED_16G_1M_512K);

        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);
        base::Status st;
        st = _state_machine->do_drop_vlet(data, NULL, 1);
        ASSERT_EQ(st.error_code(), AIE_NOT_EXIST);
    }
    {
        //volume not exist.
        aries::pb::MasterDropVletRequest request;
        request.set_token("default_token");
        auto vlet_info = request.mutable_vlet_info();
        vlet_info->set_volume_id(100);
        vlet_info->set_shard_index(0);
        vlet_info->set_node_addr(common::endpoint2int(_p_meta.get_node_addr(2)));
        vlet_info->set_disk_id(1);
        vlet_info->set_vlet_type(VLET_TYPE_LINKED_16G_1M_512K);

        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);
        base::Status st;
        st = _state_machine->do_drop_vlet(data, NULL, 1);
        ASSERT_EQ(st.error_code(), AIE_NOT_EXIST);
    }
    {
        //shardid not exist.
        aries::pb::MasterDropVletRequest request;
        request.set_token("default_token");
        auto vlet_info = request.mutable_vlet_info();
        vlet_info->set_volume_id(3);
        vlet_info->set_shard_index(0);
        vlet_info->set_node_addr(common::endpoint2int(_p_meta.get_node_addr(5)));
        vlet_info->set_disk_id(1);
        vlet_info->set_vlet_type(VLET_TYPE_LINKED_16G_1M_512K);

        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);
        base::Status st;
        st = _state_machine->do_drop_vlet(data, NULL, 1);
        ASSERT_EQ(st.error_code(), AIE_NOT_EXIST);
    }
    {
        //version not eq.
        aries::pb::MasterDropVletRequest request;
        request.set_token("default_token");
        auto vlet_info = request.mutable_vlet_info();
        vlet_info->set_volume_id(3);
        vlet_info->set_shard_index(1);
        vlet_info->set_node_addr(common::endpoint2int(_p_meta.get_node_addr(5)));
        vlet_info->set_vlet_version(100);
        vlet_info->set_vlet_type(VLET_TYPE_LINKED_16G_1M_512K);

        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);
        base::Status st;
        st = _state_machine->do_drop_vlet(data, NULL, 1);
        ASSERT_EQ(st.error_code(), AIE_NOT_EXIST);
    }
    {
        //succ.
        aries::pb::MasterDropVletRequest request;
        request.set_token("default_token");
        auto vlet_info = request.mutable_vlet_info();
        vlet_info->set_volume_id(3);
        vlet_info->set_shard_index(1);
        vlet_info->set_node_addr(common::endpoint2int(_p_meta.get_node_addr(5)));
        vlet_info->set_vlet_version(0);
        vlet_info->set_vlet_type(VLET_TYPE_LINKED_16G_1M_512K);

        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);
        base::Status st;
        st = _state_machine->do_drop_vlet(data, NULL, 2011);
        ASSERT_EQ(st.error_code(), AIE_OK);

        auto volume = g_volume_manager->get_volume(3);
        ASSERT_EQ(volume->_raft_index, 2011);
    }
    {
        //dangerous
        aries::pb::MasterDropVletRequest request;
        request.set_token("default_token");
        auto vlet_info = request.mutable_vlet_info();
        vlet_info->set_volume_id(4);
        vlet_info->set_shard_index(1);
        vlet_info->set_node_addr(common::endpoint2int(_p_meta.get_node_addr(5)));
        vlet_info->set_vlet_version(0);
        vlet_info->set_vlet_type(VLET_TYPE_LINKED_16G_1M_512K);

        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);
        base::Status st;
        st = _state_machine->do_drop_vlet(data, NULL, 1);
        ASSERT_EQ(st.error_code(), AIE_FAIL);
    }
    {
        //dangerous but foce drop
        aries::pb::MasterDropVletRequest request;
        request.set_is_force_drop(true);
        request.set_token("default_token");
        auto vlet_info = request.mutable_vlet_info();
        vlet_info->set_volume_id(4);
        vlet_info->set_shard_index(1);
        vlet_info->set_node_addr(common::endpoint2int(_p_meta.get_node_addr(5)));
        vlet_info->set_vlet_version(0);
        vlet_info->set_vlet_type(VLET_TYPE_LINKED_16G_1M_512K);

        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);
        base::Status st;
        st = _state_machine->do_drop_vlet(data, NULL, 1);
        ASSERT_EQ(st.error_code(), AIE_OK);
    }
    //drop a normal vlet, but volume is creating.
    {
        auto volume = g_volume_manager->get_volume(3);
        volume->set_state(VOLUME_STATE_CREATING);
        auto vlet = volume->get_vlet(0);
        ASSERT_EQ(vlet->state(), VLET_STATE_NORMAL);

        aries::pb::MasterDropVletRequest request;
        request.set_token("default_token");
        auto vlet_info = request.mutable_vlet_info();
        vlet_info->set_volume_id(3);
        vlet_info->set_shard_index(0);
        vlet_info->set_node_addr(common::endpoint2int(_p_meta.get_node_addr(3)));
        vlet_info->set_vlet_version(0);
        vlet_info->set_vlet_type(VLET_TYPE_LINKED_16G_1M_512K);

        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);
        base::Status st;
        st = _state_machine->do_drop_vlet(data, NULL, 1);
        ASSERT_EQ(st.error_code(), AIE_OK);
        ASSERT_EQ(vlet->state(), VLET_STATE_CREATING);
    }
}

TEST_F(MasterStateMachineTests, test_applys) {
    base::IOBuf err_data = prepare_err_data();
    _state_machine->on_apply_do(MASTER_LOG_ADD_NODE, err_data, 1, NULL);
}

TEST_F(MasterStateMachineTests, test_start_and_stop) {
    FLAGS_datanode_dead_threshold_in_second = 1;
    _state_machine->on_leader_start();
    ASSERT_EQ(g_meta_data->is_working_leader(), true);
    /*
    _state_machine->on_leader_stop();
    ASSERT_EQ(g_meta_data->is_working_leader(), false);
    */
}

TEST_F(MasterStateMachineTests, test_handle_vlet_on_leader_start) {
    MockVletScheduler scheduler;
    g_vlet_scheduler = &scheduler;
    MockVletScheduler* v = &scheduler;
    g_membership_scheduler = new MockMembershipScheduler;
    MockMembershipScheduler* m = static_cast<MockMembershipScheduler*>(g_membership_scheduler);

    g_meta_data->set_state(MASTER_STATE_FOLLOWER);
 
    auto volume = g_volume_manager->get_volume(3);
    auto vlet = volume->get_vlet(0);
    ASSERT_FALSE(_state_machine->handle_vlet_on_leader_start(vlet));
    ASSERT_FALSE(v->_be_call);
    ASSERT_FALSE(m->_be_call);

    g_meta_data->set_state(MASTER_STATE_LEADER_WORKING);
    VletState old_state = vlet->state();
    vlet->set_state(VLET_STATE_CREATING);
    ASSERT_TRUE(_state_machine->handle_vlet_on_leader_start(vlet));
    ASSERT_EQ(vlet->state(), VLET_STATE_CREATING);
    ASSERT_TRUE(v->_be_call);
    v->_be_call = false;

    vlet->set_state(VLET_STATE_RECOVERING);
    ASSERT_TRUE(_state_machine->handle_vlet_on_leader_start(vlet));
    ASSERT_EQ(vlet->state(), VLET_STATE_RECOVERING);
    ASSERT_TRUE(v->_be_call);
    v->_be_call = false;

    vlet->set_state(VLET_STATE_DROPPING);
    ASSERT_TRUE(_state_machine->handle_vlet_on_leader_start(vlet));
    ASSERT_EQ(vlet->state(), VLET_STATE_DROPPING);
    ASSERT_TRUE(m->_be_call);
    m->_be_call = false;

    vlet->set_state(VLET_STATE_JOINING);
    ASSERT_TRUE(_state_machine->handle_vlet_on_leader_start(vlet));
    ASSERT_EQ(vlet->state(), VLET_STATE_JOINING);
    ASSERT_TRUE(m->_be_call);
    m->_be_call = false;

    vlet->set_state(VLET_STATE_NORMAL);
    ASSERT_FALSE(_state_machine->handle_vlet_on_leader_start(vlet));
    ASSERT_EQ(vlet->state(), VLET_STATE_NORMAL);
    ASSERT_FALSE(m->_be_call);

    vlet->set_state(VLET_STATE_REPAIRING);
    ASSERT_FALSE(_state_machine->handle_vlet_on_leader_start(vlet));
    ASSERT_EQ(vlet->state(), VLET_STATE_REPAIRING);
    ASSERT_FALSE(m->_be_call);

    vlet->set_state(old_state);
}

TEST_F(MasterStateMachineTests, test_check_vlet_to_create) {
    MockVletScheduler scheduler;
    g_vlet_scheduler = &scheduler;

    g_meta_data->set_state(MASTER_STATE_FOLLOWER);
    auto volume = g_volume_manager->get_volume(1);
    auto vlet = volume->get_vlet(0);
    ASSERT_FALSE(_state_machine->check_vlet_to_create(vlet));

    g_meta_data->set_state(MASTER_STATE_LEADER_WORKING);

    VolumeState old_volume_state = volume->state();
    VletState old_vlet_state = vlet->state();

    volume->set_state(VOLUME_STATE_CREATING);
    vlet->set_state(VLET_STATE_CREATING);
    ASSERT_TRUE(_state_machine->check_vlet_to_create(vlet));
    ASSERT_EQ(vlet->state(), VLET_STATE_CREATING);

    vlet->set_state(old_vlet_state);
    volume->set_state(old_volume_state);
}

TEST_F(MasterStateMachineTests, test_check_vlet_to_recover) {
    MockVletScheduler scheduler;
    g_vlet_scheduler = &scheduler;

    auto volume = g_volume_manager->get_volume(1);
    auto vlet = volume->get_vlet(0);

    g_meta_data->set_state(MASTER_STATE_FOLLOWER);
    vlet->set_state(VLET_STATE_DROPPING);
    ASSERT_FALSE(_state_machine->check_vlet_to_recover(vlet));
    ASSERT_EQ(scheduler._be_call, false);

    g_meta_data->set_state(MASTER_STATE_LEADER_WORKING);

    volume = g_volume_manager->get_volume(1);
    vlet = volume->get_vlet(1);
    vlet->set_state(VLET_STATE_DROPPING);
    _state_machine->check_vlet_to_recover(vlet);
    ASSERT_EQ(scheduler._be_call, true);
}


TEST_F(MasterStateMachineTests, test_try_set_membership) {
    g_membership_scheduler = new MockMembershipScheduler;
    MockMembershipScheduler* m = static_cast<MockMembershipScheduler*>(g_membership_scheduler);

    g_meta_data->set_state(MASTER_STATE_FOLLOWER);
    auto volume = g_volume_manager->get_volume(3);
    auto vlet = volume->get_vlet(0);
    _state_machine->try_set_membership(vlet, volume, 100);
    ASSERT_FALSE(m->_be_call);

    g_meta_data->set_state(MASTER_STATE_LEADER_WORKING);

    volume = g_volume_manager->get_volume(6);
    vlet = volume->get_vlet(0);
    VletState old_state = vlet->state();

    vlet->set_state(VLET_STATE_DROPPING);
    _state_machine->try_set_membership(vlet, volume, 100);
    ASSERT_TRUE(m->_be_call);
    ASSERT_EQ(m->_success_threshold, 1);
    m->_success_threshold = -1;
    m->_be_call = false;

    vlet->set_state(VLET_STATE_JOINING);
    _state_machine->try_set_membership(vlet, volume, 100);
    ASSERT_TRUE(m->_be_call);
    ASSERT_EQ(m->_success_threshold, 1);
    m->_success_threshold = -1;
    m->_be_call = false;

    vlet->set_state(VLET_STATE_REPAIRING);
    _state_machine->try_set_membership(vlet, volume, 100);
    ASSERT_TRUE(m->_be_call);
    ASSERT_EQ(m->_success_threshold, 0);
    m->_success_threshold = -1;
    m->_be_call = false;

    vlet->set_state(VLET_STATE_NORMAL);
    _state_machine->try_set_membership(vlet, volume, 100);
    ASSERT_TRUE(m->_be_call);
    ASSERT_EQ(m->_success_threshold, 0);
    m->_success_threshold = -1;
    m->_be_call = false;

    vlet->set_state(VLET_STATE_RECOVERING);
    _state_machine->try_set_membership(vlet, volume, 100);
    ASSERT_FALSE(m->_be_call);

    vlet->set_state(VLET_STATE_CREATING);
    _state_machine->try_set_membership(vlet, volume, 100);
    ASSERT_FALSE(m->_be_call);

    vlet->set_state(old_state);
}

TEST_F(MasterStateMachineTests, test_set_node_alive) {
    {
        //node not exist.
        aries::pb::SetNodeAliveState request;
        request.set_node_addr(common::endpoint2int(_p_meta.get_node(0)));
        request.set_timestamp(1);
        request.set_is_alive(true);

        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);
        base::Status st;
        st = _state_machine->do_set_node_alive_state(data, 1);
        ASSERT_EQ(st.error_code(), AIE_NOT_EXIST);
    }
    {
        //node exist.
        aries::pb::SetNodeAliveState request;
        request.set_node_addr(common::endpoint2int(_p_meta.get_node(1)));
        request.set_timestamp(2);
        request.set_is_alive(true);

        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);
        base::Status st;
        st = _state_machine->do_set_node_alive_state(data, 1);
        auto node = g_node_manager->get_node(_p_meta.get_node(1));

        ASSERT_EQ(st.error_code(), AIE_OK);
        ASSERT_EQ(node->online_time(), 2);
    }
    {
        //node exist.
        aries::pb::SetNodeAliveState request;
        request.set_node_addr(common::endpoint2int(_p_meta.get_node(1)));
        request.set_timestamp(3);
        request.set_is_alive(false);

        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);
        base::Status st;
        st = _state_machine->do_set_node_alive_state(data, 1);
        auto node = g_node_manager->get_node(_p_meta.get_node(1));

        ASSERT_EQ(st.error_code(), AIE_OK);
        ASSERT_EQ(node->offline_time(), 3);
    }
}

TEST_F(MasterStateMachineTests, test_on_apply) {
        base::IOBuf err_data = prepare_err_data();
        base::Status st;
        MasterLogType types[] = {
            MASTER_LOG_ADD_NODE,
            MASTER_LOG_DROP_NODE,
            MASTER_LOG_ADD_DISK,
            MASTER_LOG_DROP_DISK,
            MASTER_LOG_CREATE_SPACE,
            MASTER_LOG_UPDATE_SPACE,
            MASTER_LOG_DROP_SPACE,
            MASTER_LOG_CREATE_VOLUMES,
            MASTER_LOG_CREATE_VLET,
            MASTER_LOG_RECOVER_VLET,
            MASTER_LOG_MIGRATE_VLET,
            MASTER_LOG_ADD_VLET,
            MASTER_LOG_REPAIR_VLET_FINISH,
            MASTER_LOG_DROP_VLET,
            MASTER_LOG_ADD_ALLOCATOR,
            MASTER_LOG_DROP_ALLOCATOR,
            MASTER_LOG_ADD_TINKER,
            MASTER_LOG_DROP_TINKER,
            MASTER_LOG_ADD_VOLUME_SERVER,
            MASTER_LOG_DROP_VOLUME_SERVER,
            MASTER_LOG_ASSIGN_ALLOCATOR_SEQUENCE_ID,
            MASTER_LOG_SET_NODE_ALIVE_STATE,
            MASTER_LOG_SET_MEMBERSHIP_FINISH,
            MASTER_LOG_DISABLE_VOLUME,
            MASTER_LOG_ENABLE_VOLUME,
            MASTER_LOG_DROP_VOLUME,
        };
        for (size_t i = 0; i < sizeof(types); ++i) {
            st = _state_machine->on_apply_do(types[i], err_data, 1, NULL);
            ASSERT_NE(st.error_code(), AIE_OK);
        }

}

TEST_F(MasterStateMachineTests, test_check_cancel_task) {
    //creating
    {
        MockVletScheduler scheduler;

        auto volume = g_volume_manager->get_volume(1);
        auto vlet = volume->get_vlet(0);

        g_vlet_scheduler = &scheduler;

        g_vlet_scheduler->create_vlet(vlet);

        _state_machine->check_cancel_task(vlet);
        ASSERT_EQ(scheduler._be_call, true);
    }
    //recovering
    {
        MockVletScheduler scheduler;

        auto volume = g_volume_manager->get_volume(2);
        auto vlet = volume->get_vlet(0);
        auto vlet2 = volume->get_vlet(1);

        g_vlet_scheduler->create_vlet(vlet);
        g_vlet_scheduler = &scheduler;

        _state_machine->check_cancel_task(vlet);
        ASSERT_EQ(scheduler._be_call, true);
    }
    //droping
    {
        g_membership_scheduler = new MembershipScheduler();
        auto volume = g_volume_manager->get_volume(6);
        auto vlet = volume->get_vlet(0);
        auto vlet2 = volume->get_vlet(1);
        g_membership_scheduler->_tasks.clear();
        g_membership_scheduler->_is_stopped = false;

        ASSERT_TRUE(g_membership_scheduler->update_membership(volume, vlet->shard_index(),
                vlet->vlet_version(), 1, 100));
        _state_machine->check_cancel_task(vlet2);
        ASSERT_TRUE(g_membership_scheduler->_tasks.size() != 0);
        MembershipTaskId task_id;
        task_id.volume_id = volume->volume_id();
        task_id.shard_index = vlet->shard_index();
        task_id.vlet_version = vlet->vlet_version();
        auto task = g_membership_scheduler->_tasks.find(task_id);
        ASSERT_TRUE(task->second->is_cancelled != true);

        _state_machine->check_cancel_task(vlet);
        ASSERT_TRUE(task->second->is_cancelled == true);
    }
    //joining
    {
        g_membership_scheduler = new MembershipScheduler();
        auto volume = g_volume_manager->get_volume(7);
        auto vlet = volume->get_vlet(0);
        auto vlet2 = volume->get_vlet(1);
        g_membership_scheduler->_tasks.clear();
        g_membership_scheduler->_is_stopped = false;

        g_membership_scheduler->update_membership(volume, vlet->shard_index(),
                vlet->vlet_version(), 1, 100);
        _state_machine->check_cancel_task(vlet2);
        ASSERT_TRUE(g_membership_scheduler->_tasks.size() != 0);
        MembershipTaskId task_id;
        task_id.volume_id = volume->volume_id();
        task_id.shard_index = vlet->shard_index();
        task_id.vlet_version = vlet->vlet_version();
        auto task = g_membership_scheduler->_tasks.find(task_id);
        ASSERT_TRUE(task->second->is_cancelled != true);

        _state_machine->check_cancel_task(vlet);
        ASSERT_TRUE(task->second->is_cancelled == true);
    }
}

TEST_F(MasterStateMachineTests, test_migrate_vlet) {
    //rule balance fail.

    {
        aries::pb::ReportMigrateVletRequest request;
        aries::pb::AckResponse response;
        auto volume = g_volume_manager->get_volume(3);
        auto space = volume->space();
        space->_options.disk_vlet_type_map = 
            {std::make_pair("HDD", VLET_TYPE_LINKED_32G_4M_512K), std::make_pair("HDD", VLET_TYPE_APPEND_32G_256M_4K)};
        auto vlet = volume->get_vlet(0);
        ASSERT_EQ(vlet->state(), VLET_STATE_NORMAL);

        request.set_success(true);
        request.set_purpose(RULE);
        request.set_token("default_token");
        auto src_vlet = request.mutable_src_vlet();
        src_vlet->set_volume_id(3);
        src_vlet->set_shard_index(0);
        src_vlet->set_vlet_version(0);
        src_vlet->set_vlet_type(VLET_TYPE_LINKED_32G_4M_512K);

        auto dest_vlet = request.mutable_dest_vlet();
        dest_vlet->set_volume_id(3);
        dest_vlet->set_shard_index(0);
        dest_vlet->set_vlet_version(1);
        dest_vlet->set_vlet_type(VLET_TYPE_LINKED_VARIENT);
        auto dest_addr = _p_meta.get_node(2);
        dest_vlet->set_node_addr(common::endpoint2int(dest_addr));
        dest_vlet->set_disk_id(1);

        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);
        base::Status st;
        st = _state_machine->do_migrate_vlet(data, NULL, 2012);
        ASSERT_EQ(st.error_code(), AIE_FAIL);
    }
    //rule balance succ.
    {
        aries::pb::ReportMigrateVletRequest request;
        aries::pb::AckResponse response;
        auto volume = g_volume_manager->get_volume(3);
        auto space = volume->space();
        space->_options.disk_vlet_type_map = 
            {std::make_pair("HDD", VLET_TYPE_LINKED_32G_4M_512K), std::make_pair("HDD", VLET_TYPE_APPEND_32G_256M_4K)};
        auto vlet = volume->get_vlet(0);
        ASSERT_EQ(vlet->state(), VLET_STATE_NORMAL);

        request.set_success(true);
        request.set_purpose(RULE);
        request.set_token("default_token");
        auto src_vlet = request.mutable_src_vlet();
        src_vlet->set_volume_id(3);
        src_vlet->set_shard_index(0);
        src_vlet->set_vlet_version(0);
        src_vlet->set_vlet_type(VLET_TYPE_LINKED_32G_4M_512K);

        auto dest_vlet = request.mutable_dest_vlet();
        dest_vlet->set_volume_id(3);
        dest_vlet->set_shard_index(0);
        dest_vlet->set_vlet_version(1);
        dest_vlet->set_vlet_type(VLET_TYPE_LINKED_32G_4M_512K);
        auto dest_addr = _p_meta.get_node(2);
        dest_vlet->set_node_addr(common::endpoint2int(dest_addr));
        dest_vlet->set_disk_id(1);

        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);
        base::Status st;
        st = _state_machine->do_migrate_vlet(data, NULL, 2012);
        ASSERT_EQ(st.error_code(), AIE_OK);
        ASSERT_EQ(vlet->state(), VLET_STATE_JOINING);

        ASSERT_EQ(volume->_raft_index, 2012);
    }
    // compact balance succ
    {   
        auto space = g_space_manager->get_space("test_space1");
        auto volume3 = g_volume_manager->get_volume(3);
        volume3->set_state(VOLUME_STATE_READONLY);
        volume3->set_compact_progress(RUNNING);
        volume3->set_compact_vlet_size(28 * aries::common::GB);
        volume3->set_per_vlet_size(32 * aries::common::GB);
        VletVector vlets;
        volume3->get_vlet_list(&vlets);
        for (auto vlet : vlets) {
            vlet->set_state(VLET_STATE_NORMAL);
            vlet->vlet_engine_info_ptr()->set_vlet_size(32 * aries::common::GB);
        }

        aries::pb::ReportMigrateVletRequest request;
        aries::pb::AckResponse response;
        auto volume = g_volume_manager->get_volume(3);
        auto vlet = volume->get_vlet(0);
        ASSERT_EQ(vlet->state(), VLET_STATE_NORMAL);

        request.set_success(true);
        request.set_purpose(COMPACT);
        request.set_token("default_token");
        auto src_vlet = request.mutable_src_vlet();
        src_vlet->set_volume_id(3);
        src_vlet->set_shard_index(0);
        src_vlet->set_vlet_version(1);
        src_vlet->set_vlet_type(VLET_TYPE_LINKED_32G_4M_512K);

        auto dest_vlet = request.mutable_dest_vlet();
        dest_vlet->set_volume_id(3);
        dest_vlet->set_shard_index(0);
        dest_vlet->set_vlet_version(2);
        dest_vlet->set_vlet_type(VLET_TYPE_LINKED_VARIENT);
        auto dest_addr = _p_meta.get_node(3);
        dest_vlet->set_node_addr(common::endpoint2int(dest_addr));
        dest_vlet->set_disk_id(1);
        auto vlet_engine_info = dest_vlet->mutable_vlet_engine_options();
        vlet_engine_info->set_vlet_size(28 * aries::common::GB);
        vlet_engine_info->set_block_size(4 * aries::common::MB);
        vlet_engine_info->set_max_record_size(512 * aries::common::KB);
        vlet_engine_info->set_min_record_size(4 * aries::common::KB);
        vlet_engine_info->set_record_gap_page_num(1);

        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);
        base::Status st;
        st = _state_machine->do_migrate_vlet(data, NULL, 2013);
        ASSERT_EQ(st.error_code(), AIE_OK);
        ASSERT_EQ(vlet->state(), VLET_STATE_JOINING);
        ASSERT_EQ(vlet->vlet_type(), VLET_TYPE_LINKED_VARIENT);
        ASSERT_EQ(vlet->vlet_engine_info_ptr()->vlet_size(), 28 * aries::common::GB);

        ASSERT_EQ(volume->_raft_index, 2013);
        ASSERT_EQ(volume->state(), VOLUME_STATE_READONLY);
        ASSERT_EQ(volume->compact_progress(), RUNNING);
        ASSERT_EQ(volume->per_vlet_size(), 32 * aries::common::GB);
    }
    {
        aries::pb::ReportMigrateVletRequest request;
        aries::pb::AckResponse response;
        auto volume = g_volume_manager->get_volume(3);
        auto vlet0 = volume->get_vlet(0);
        vlet0->set_state(VLET_STATE_NORMAL);
        auto vlet = volume->get_vlet(1);
        ASSERT_EQ(vlet->state(), VLET_STATE_NORMAL);

        request.set_success(true);
        request.set_purpose(COMPACT);
        request.set_token("default_token");
        auto src_vlet = request.mutable_src_vlet();
        src_vlet->set_volume_id(3);
        src_vlet->set_shard_index(1);
        src_vlet->set_vlet_version(0);
        src_vlet->set_vlet_type(VLET_TYPE_LINKED_32G_4M_512K);

        auto dest_vlet = request.mutable_dest_vlet();
        dest_vlet->set_volume_id(3);
        dest_vlet->set_shard_index(1);
        dest_vlet->set_vlet_version(1);
        dest_vlet->set_vlet_type(VLET_TYPE_APPEND_VARIENT);
        auto dest_addr = _p_meta.get_node(4);
        dest_vlet->set_node_addr(common::endpoint2int(dest_addr));
        dest_vlet->set_disk_id(1);
        auto vlet_engine_info = dest_vlet->mutable_vlet_engine_options();
        vlet_engine_info->set_vlet_size(28 * aries::common::GB);
        vlet_engine_info->set_smr_zone_size(256 * aries::common::MB);
        vlet_engine_info->set_align_size(4 * aries::common::KB);

        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);
        base::Status st;
        st = _state_machine->do_migrate_vlet(data, NULL, 2014);
        ASSERT_EQ(st.error_code(), AIE_OK);
        ASSERT_EQ(vlet->state(), VLET_STATE_JOINING);
        ASSERT_EQ(vlet->vlet_type(), VLET_TYPE_APPEND_VARIENT);
        ASSERT_EQ(vlet->vlet_engine_info_ptr()->vlet_size(), 28 * aries::common::GB);
        
        ASSERT_EQ(volume->_raft_index, 2014);
        ASSERT_EQ(volume->state(), VOLUME_STATE_NORMAL);
        ASSERT_EQ(volume->compact_progress(), END);
        ASSERT_EQ(volume->per_vlet_size(), 28 * aries::common::GB);
    }
}

TEST_F(MasterStateMachineTests, test_replace_vlet) {
    //succ.
    {
        aries::pb::ReplaceVletRequest request;
        request.set_check_vlet_not_normal(false);
        aries::pb::AckResponse response;
        uint64_t volume_id = 3;
        auto volume = g_volume_manager->get_volume(3);
        auto vlet = volume->get_vlet(0);
        ASSERT_EQ(vlet->state(), VLET_STATE_NORMAL);

        request.set_token("default_token");
        auto dest_vlet = request.mutable_dest_vlet();
        dest_vlet->set_volume_id(3);
        dest_vlet->set_shard_index(0);
        dest_vlet->set_vlet_version(1);
        dest_vlet->set_vlet_type(VLET_TYPE_LINKED_32G_4M_512K);
        auto dest_addr = _p_meta.get_node(2);
        dest_vlet->set_node_addr(common::endpoint2int(dest_addr));
        dest_vlet->set_disk_id(1);

        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);
        base::Status st;
        ASSERT_EQ(volume->volume_version(), 0);
        auto disk = vlet->disk();
        auto get_vlet = disk->get_vlet(volume_id);
        ASSERT_NE(get_vlet, nullptr);

        st = _state_machine->do_replace_vlet(data, NULL, 2013);
        ASSERT_EQ(st.error_code(), AIE_OK);
        ASSERT_EQ(vlet->state(), VLET_STATE_JOINING);
        ASSERT_EQ(volume->volume_version(), 1);
        get_vlet = disk->get_vlet(volume_id);
        ASSERT_EQ(get_vlet, nullptr);

        ASSERT_EQ(volume->_raft_index, 2013);
    }
    //fail
    {
        uint64_t volume_id = 4;
        aries::pb::ReplaceVletRequest request;
        aries::pb::AckResponse response;
        request.set_check_vlet_not_normal(false);
        auto volume = g_volume_manager->get_volume(volume_id);
        auto vlet = volume->get_vlet(1);
        ASSERT_EQ(vlet->state(), VLET_STATE_NORMAL);

        request.set_token("default_token");
        auto dest_vlet = request.mutable_dest_vlet();
        dest_vlet->set_volume_id(4);
        dest_vlet->set_shard_index(1);
        dest_vlet->set_vlet_version(0);
        dest_vlet->set_vlet_type(VLET_TYPE_LINKED_32G_4M_512K);
        auto dest_addr = _p_meta.get_node(2);
        dest_vlet->set_node_addr(common::endpoint2int(dest_addr));
        dest_vlet->set_disk_id(1);

        auto disk = vlet->disk();

        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);
        base::Status st;
        ASSERT_EQ(volume->volume_version(), 0);
        auto get_vlet = disk->get_vlet(volume_id);
        ASSERT_NE(get_vlet, nullptr);

        st = _state_machine->do_replace_vlet(data, NULL, 2014);
        ASSERT_EQ(st.error_code(), AIE_FAIL);
        ASSERT_EQ(vlet->state(), VLET_STATE_NORMAL);
        ASSERT_EQ(volume->volume_version(), 0);
        get_vlet = disk->get_vlet(volume_id);
        ASSERT_NE(get_vlet, nullptr);

        ASSERT_NE(volume->_raft_index, 2014);
    }
    //fail
    {
        aries::pb::ReplaceVletRequest request;
        request.set_check_vlet_not_normal(false);
        aries::pb::AckResponse response;
        uint64_t volume_id = 4;
        auto volume = g_volume_manager->get_volume(volume_id);
        auto vlet = volume->get_vlet(1);
        ASSERT_EQ(vlet->state(), VLET_STATE_NORMAL);

        request.set_token("default_token");
        auto dest_vlet = request.mutable_dest_vlet();
        dest_vlet->set_volume_id(4);
        dest_vlet->set_shard_index(1);
        dest_vlet->set_vlet_version(0);
        dest_vlet->set_vlet_type(VLET_TYPE_LINKED_VARIENT);
        auto dest_addr = _p_meta.get_node(2);
        dest_vlet->set_node_addr(common::endpoint2int(dest_addr));
        dest_vlet->set_disk_id(1);

        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);
        base::Status st;
        ASSERT_EQ(volume->volume_version(), 0);
        auto disk = vlet->disk();
        auto get_vlet = disk->get_vlet(volume_id);
        ASSERT_NE(get_vlet, nullptr);

        st = _state_machine->do_replace_vlet(data, NULL, 2014);
        ASSERT_EQ(st.error_code(), AIE_FAIL);
    }
}

TEST_F(MasterStateMachineTests, test_disable_az) {
    // proto error
    {
        base::IOBuf err_data = prepare_err_data();
        base::Status st;
        st = _state_machine->do_disable_az(err_data, NULL, 0);
        ASSERT_EQ(st.error_code(), AIE_FAIL);
    }
    // az is not exist
    {
        aries::pb::DisableAzRequest request;
        request.set_token("default_token");
        request.set_az_name("cq02");
        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);
        base::Status st;
        st = _state_machine->do_disable_az(data, NULL, 0);
        ASSERT_EQ(st.error_code(), AIE_FAIL);
    }
    // already in disable status
    {
        base::EndPoint endpoint;
        EXPECT_EQ(0, aries::common::str2endpoint("192.168.1.1:8888", &endpoint));

        NodeOptions options;
        options.addr = endpoint;
        options.group_name = "test";
        options.az_name = "test_az";
        options.idc_name = "test_idc";
        auto node_ptr = std::shared_ptr<Node>(new Node(options));
        g_az_manager->add_node(node_ptr);
        g_az_manager->disable_az("test_az");

        aries::pb::DisableAzRequest request;
        request.set_token("default_token");
        request.set_az_name("test_az");
        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);
        base::Status st;
        st = _state_machine->do_disable_az(data, NULL, 0);
        ASSERT_EQ(st.error_code(), AIE_FAIL);

        g_az_manager->enable_az("test_az");
        g_az_manager->drop_node(node_ptr);
    }
    // disable az succeeded
    {
        base::EndPoint endpoint;
        EXPECT_EQ(0, aries::common::str2endpoint("192.168.1.1:8888", &endpoint));

        NodeOptions options;
        options.addr = endpoint;
        options.group_name = "test";
        options.az_name = "test_az";
        options.idc_name = "test_idc";
        auto node_ptr = std::shared_ptr<Node>(new Node(options));
        g_az_manager->add_node(node_ptr);

        aries::pb::DisableAzRequest request;
        request.set_token("default_token");
        request.set_az_name("test_az");
        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);
        base::Status st;
        st = _state_machine->do_disable_az(data, NULL, 0);
        ASSERT_EQ(st.error_code(), AIE_OK);
        ASSERT_FALSE(g_az_manager->disabled_az_name().empty());
        ASSERT_EQ(g_az_manager->disabled_az_name(), "test_az");
        ASSERT_FALSE(node_ptr->is_alive());

        g_az_manager->enable_az("test_az");
        g_az_manager->drop_node(node_ptr);
    }
}

TEST_F(MasterStateMachineTests, test_enable_az) {
    // proto error
    {
        base::IOBuf err_data = prepare_err_data();
        base::Status st;
        st = _state_machine->do_enable_az(err_data, NULL, 0);
        ASSERT_EQ(st.error_code(), AIE_FAIL);
    }
    // az is not exist
    {
        aries::pb::EnableAzRequest request;
        request.set_token("default_token");
        request.set_az_name("cq02");
        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);
        base::Status st;
        st = _state_machine->do_enable_az(data, NULL, 0);
        ASSERT_EQ(st.error_code(), AIE_FAIL);
    }
    // not in disable status
    {
        base::EndPoint endpoint;
        EXPECT_EQ(0, aries::common::str2endpoint("192.168.1.1:8888", &endpoint));

        NodeOptions options;
        options.addr = endpoint;
        options.group_name = "test";
        options.az_name = "test_az";
        options.idc_name = "test_idc";
        auto node_ptr = std::shared_ptr<Node>(new Node(options));
        g_az_manager->add_node(node_ptr);

        aries::pb::EnableAzRequest request;
        request.set_token("default_token");
        request.set_az_name("test_az");
        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);
        base::Status st;
        st = _state_machine->do_enable_az(data, NULL, 0);
        ASSERT_EQ(st.error_code(), AIE_FAIL);

        g_az_manager->drop_node(node_ptr);
    }
    // enable az succeeded
    {
        base::EndPoint endpoint;
        EXPECT_EQ(0, aries::common::str2endpoint("192.168.1.1:8888", &endpoint));

        NodeOptions options;
        options.addr = endpoint;
        options.group_name = "test";
        options.az_name = "test_az";
        options.idc_name = "test_idc";
        auto node_ptr = std::shared_ptr<Node>(new Node(options));
        g_az_manager->add_node(node_ptr);

        g_az_manager->disable_az("test_az");
        aries::pb::EnableAzRequest request;
        request.set_token("default_token");
        request.set_az_name("test_az");
        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);
        base::Status st;
        st = _state_machine->do_enable_az(data, NULL, 0);
        ASSERT_EQ(st.error_code(), AIE_OK);
        ASSERT_TRUE(g_az_manager->disabled_az_name().empty());

        g_az_manager->drop_node(node_ptr);
    }
}

TEST_F(MasterStateMachineTests, test_enable_node_safemode) {
    {
        //proto err
        base::IOBuf err_data = prepare_err_data();
        base::Status st;
        st = _state_machine->do_enable_node_safemode(err_data, NULL, 1);
        ASSERT_EQ(st.error_code(), AIE_FAIL);
    }

    {
        //node not exist.
        aries::pb::EnableNodeSafeModeRequest request;
        request.set_node_addr(common::endpoint2int(_p_meta.get_node(0)));
        request.set_token("default_token");
        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);
        base::Status st;
        st = _state_machine->do_enable_node_safemode(data, NULL, 1);
        ASSERT_EQ(st.error_code(), AIE_NOT_EXIST);
    }

    {
        // node is in disk safemode state
        aries::pb::EnableNodeSafeModeRequest request;
        auto addr = _p_meta.get_node(1);
        request.set_node_addr(common::endpoint2int(addr));
        request.set_token("default_token");
        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);

        uint64_t timestamp = static_cast<uint64_t>(time(NULL));
        std::shared_ptr<Node> node = g_node_manager->get_node(addr);
        node->set_disk_safemode(true, timestamp);

        base::Status st;
        st = _state_machine->do_enable_node_safemode(data, NULL, 1);
        ASSERT_EQ(st.error_code(), AIE_FAIL);
    }
    {
        // enable disk safemode state succc.
        uint64_t timestamp = static_cast<uint64_t>(time(NULL));
        aries::pb::EnableNodeSafeModeRequest request;
        auto addr = _p_meta.get_node(3);
        request.set_node_addr(common::endpoint2int(addr));
        request.set_token("default_token");
        request.set_timestamp(timestamp);
        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);

        std::shared_ptr<Node> node = g_node_manager->get_node(addr);
        node->set_disk_safemode(false, 0);

        base::Status st;
        st = _state_machine->do_enable_node_safemode(data, NULL, 2021);
        ASSERT_EQ(st.error_code(), AIE_OK);
        ASSERT_TRUE(node->is_disk_safemode());
        ASSERT_EQ(node->disk_safemode_uptime(), timestamp);
        ASSERT_EQ(node->_raft_index, 2021);
    }
}

TEST_F(MasterStateMachineTests, test_disable_node_safemode) {
    {
        //proto err
        base::IOBuf err_data = prepare_err_data();
        base::Status st;
        st = _state_machine->do_disable_node_safemode(err_data, NULL, 1);
        ASSERT_EQ(st.error_code(), AIE_FAIL);
    }

    {
        //node not exist.
        aries::pb::DisableNodeSafeModeRequest request;
        request.set_node_addr(common::endpoint2int(_p_meta.get_node(0)));
        request.set_token("default_token");
        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);
        base::Status st;
        st = _state_machine->do_disable_node_safemode(data, NULL, 1);
        ASSERT_EQ(st.error_code(), AIE_NOT_EXIST);
    }

    {
        // node is not in disk safemode state
        aries::pb::DisableNodeSafeModeRequest request;
        auto addr = _p_meta.get_node(1);
        request.set_node_addr(common::endpoint2int(addr));
        request.set_token("default_token");
        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);

        std::shared_ptr<Node> node = g_node_manager->get_node(addr);
        node->set_disk_safemode(false, 0);

        base::Status st;
        st = _state_machine->do_disable_node_safemode(data, NULL, 1);
        ASSERT_EQ(st.error_code(), AIE_FAIL);
    }
    {
        // disable disk safemode state succc.
        uint64_t timestamp = static_cast<uint64_t>(time(NULL));
        aries::pb::DisableNodeSafeModeRequest request;
        auto addr = _p_meta.get_node(3);
        request.set_node_addr(common::endpoint2int(addr));
        request.set_token("default_token");
        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);

        std::shared_ptr<Node> node = g_node_manager->get_node(addr);
        node->set_disk_safemode(true, timestamp);

        base::Status st;
        st = _state_machine->do_disable_node_safemode(data, NULL, 2022);
        ASSERT_EQ(st.error_code(), AIE_OK);
        ASSERT_FALSE(node->is_disk_safemode());
        ASSERT_EQ(node->disk_safemode_uptime(), 0);
        ASSERT_EQ(node->_raft_index, 2022);
    }
}

TEST_F(MasterStateMachineTests, test_space_info2option) {
    aries::pb::SpaceInfo info;
    info.set_state(SPACE_STATE_REDUCE_REDUNDENCY);
    aries::pb::SpaceInfo mr_info;
    SpaceOptions options;

    ASSERT_EQ(options.state, SPACE_STATE_NORMAL);
    _state_machine->space_info2option(info, &options, &mr_info);
    ASSERT_EQ(options.state, SPACE_STATE_REDUCE_REDUNDENCY);

    info.set_disk_type("");
    _state_machine->space_info2option(info, &options, &mr_info);
    ASSERT_TRUE(options.disk_type_set == std::set<std::string>({"HDD"}));

    info.set_disk_type("HDD,SMR");
    _state_machine->space_info2option(info, &options, &mr_info);
    ASSERT_TRUE(options.disk_type_set == std::set<std::string>({"HDD", "SMR"}));

    //default
    ASSERT_TRUE(options.permit_compact_balance == false);
    ASSERT_TRUE(options.compact_state == NO);
    ASSERT_TRUE(options.append_zone_rewrite_rate == 5);
    ASSERT_TRUE(options.daily_rewrite_start_time == 0);
    ASSERT_TRUE(options.daily_rewrite_duration_second == 24 * common::HOUR_SECOND);

    info.set_append_zone_rewrite_rate(10);
    info.set_daily_rewrite_start_time(3600);
    info.set_daily_rewrite_duration_second(3600 * 7);
    _state_machine->space_info2option(info, &options, &mr_info);
    ASSERT_TRUE(options.append_zone_rewrite_rate == 10);
    ASSERT_TRUE(options.daily_rewrite_start_time == 3600);
    ASSERT_TRUE(options.daily_rewrite_duration_second == 3600 * 7);

    info.set_permit_compact_balance(true);
    _state_machine->space_info2option(info, &options, &mr_info);
    ASSERT_TRUE(options.permit_compact_balance);

    info.set_compact_state(RUN);
    _state_machine->space_info2option(info, &options, &mr_info);
    ASSERT_TRUE(options.compact_state == RUN);

    info.set_compact_state(PAUSE);
    _state_machine->space_info2option(info, &options, &mr_info);
    ASSERT_TRUE(options.compact_state == PAUSE);

    info.set_compact_state(FINISH);
    _state_machine->space_info2option(info, &options, &mr_info);
    ASSERT_TRUE(options.compact_state == FINISH);

    info.set_permit_data_offset_index(true);
    _state_machine->space_info2option(info, &options, &mr_info);
    ASSERT_TRUE(options.permit_data_offset_index == true);
}

TEST_F(MasterStateMachineTests, test_control_balance) {
    FLAGS_enable_rule_balance = true;
    FLAGS_enable_decommission_balance = true;
    FLAGS_enable_usage_balance = true;
    
    // rule balance
    aries::pb::ControlBalanceRequest request0;
    request0.set_token("default_token");
    request0.set_enable_rule_balance(false);

    base::IOBuf data0;
    base::IOBufAsZeroCopyOutputStream wrapper0(&data0);
    request0.SerializeToZeroCopyStream(&wrapper0);
    base::Status st;
    st = _state_machine->do_control_balance(data0, NULL, 0);
    ASSERT_TRUE(FLAGS_enable_rule_balance == false);

    // decommission balance
    aries::pb::ControlBalanceRequest request1;
    request1.set_token("default_token");
    request1.set_enable_decommission_balance(false);

    base::IOBuf data1;
    base::IOBufAsZeroCopyOutputStream wrapper1(&data1);
    request1.SerializeToZeroCopyStream(&wrapper1);
    st = _state_machine->do_control_balance(data1, NULL, 0);
    ASSERT_TRUE(FLAGS_enable_decommission_balance == false);

    // rule balance
    aries::pb::ControlBalanceRequest request2;
    request2.set_token("default_token");
    request2.set_enable_usage_balance(false);

    base::IOBuf data2;
    base::IOBufAsZeroCopyOutputStream wrapper2(&data2);
    request2.SerializeToZeroCopyStream(&wrapper2);
    st = _state_machine->do_control_balance(data2, NULL, 0);
    ASSERT_TRUE(FLAGS_enable_usage_balance == false);

    FLAGS_enable_rule_balance = true;
    FLAGS_enable_decommission_balance = true;
    FLAGS_enable_usage_balance = true;
}

TEST_F(MasterStateMachineTests, test_copy_space) {
    //copy space fail due to dest cluster same with src
    {
        auto space = g_space_manager->get_space("test_space1");
        aries::pb::CopySpaceRequest request;
        aries::pb::AckResponse response;
        request.set_token("default_token");
        request.set_src_space_name("test_space1");
        request.set_src_space_id(1);
        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);
        base::Status st;
        st = _state_machine->do_copy_space(data, NULL, 1);
        ASSERT_EQ(st.error_code(), AIE_FAIL);
    }
    //copy space fail due to space state err.
    {
        auto space = g_space_manager->get_space("test_space1");
        
        space->set_state(SPACE_STATE_REDUCE_REDUNDENCY);

        aries::pb::CopySpaceRequest request;
        aries::pb::AckResponse response;
        request.set_token("default_token");
        request.mutable_dest_space_option()->set_cluster_id(1);
        request.set_src_space_name("test_space1");
        request.set_src_space_id(1);
        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);
        base::Status st;
        st = _state_machine->do_copy_space(data, NULL, 1);
        ASSERT_EQ(st.error_code(), AIE_SPACE_NOT_READY);
        space->set_state(SPACE_STATE_NORMAL);
    }
    //copy space fail due to space not exist.
    {
        aries::pb::CopySpaceRequest request;
        aries::pb::AckResponse response;
        request.set_token("default_token");
        request.mutable_dest_space_option()->set_cluster_id(1);
        request.set_src_space_name("test_space_err");
        request.set_src_space_id(1);
        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);
        base::Status st;
        st = _state_machine->do_copy_space(data, NULL, 1);
        ASSERT_EQ(st.error_code(), AIE_NOT_EXIST);
    }
    //copy space fail due to space id is error.
    {
        aries::pb::CopySpaceRequest request;
        aries::pb::AckResponse response;
        request.set_token("default_token");
        request.mutable_dest_space_option()->set_cluster_id(1);
        request.set_src_space_name("test_space1");
        request.set_src_space_id(2);
        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);
        base::Status st;
        st = _state_machine->do_copy_space(data, NULL, 1);
        ASSERT_EQ(st.error_code(), AIE_FAIL);
    }
    // copy space fail due to az disable
    {
        g_az_manager->disable_az("az_disable");
        if (g_az_manager->disabled_az_name().empty() == false) {
            LOG(TRACE) << "disable az:" << g_az_manager->disabled_az_name();
        }

        aries::pb::CopySpaceRequest request;
        aries::pb::AckResponse response;
        request.set_token("default_token");
        request.mutable_dest_space_option()->set_cluster_id(1);
        request.set_src_space_name("test_space1");
        request.set_src_space_id(1);
        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);
        base::Status st;
        st = _state_machine->do_copy_space(data, NULL, 1);
        ASSERT_EQ(st.error_code(), AIE_FAIL);
        if (g_az_manager->disabled_az_name().empty() == false) {
            LOG(TRACE) << "disable az:" << g_az_manager->disabled_az_name();
        }
        g_az_manager->enable_az("az_disable");
        if (g_az_manager->disabled_az_name().empty() == false) {
            LOG(TRACE) << "disable az:" << g_az_manager->disabled_az_name();
        }
    }
    // copy space successed
    {
        auto space = g_space_manager->get_space("test_space1");

        aries::pb::CopySpaceRequest request;
        aries::pb::AckResponse response;
        request.set_token("default_token");
        request.mutable_dest_space_option()->set_cluster_id(1);
        request.set_src_space_name("test_space1");
        request.set_src_space_id(1);
        request.set_permit_copy_space(true);
        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);
        base::Status st;
        st = _state_machine->do_copy_space(data, NULL, 1);
        ASSERT_EQ(st.error_code(), AIE_OK);
        EXPECT_EQ(space->permit_copy_space(), true);
    }
}

TEST_F(MasterStateMachineTests, test_control_copy_space) {

     // prepare
    auto space = g_space_manager->get_space("test_space1");
    //copy space fail due to space state err.
    {
        auto space = g_space_manager->get_space("test_space1");
        space->set_state(SPACE_STATE_REDUCE_REDUNDENCY);

        aries::pb::CopySpaceRequest request;
        aries::pb::AckResponse response;
        request.set_token("default_token");
        request.set_src_space_name("test_space1");
        request.set_src_space_id(1);
        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);
        base::Status st;
        st = _state_machine->do_control_copy_space(data, NULL, 1);
        ASSERT_EQ(st.error_code(), AIE_SPACE_NOT_READY);
        space->set_state(SPACE_STATE_NORMAL);
    }
    //copy space fail due to space not exist.
    {
        aries::pb::CopySpaceRequest request;
        aries::pb::AckResponse response;
        request.set_token("default_token");
        request.set_src_space_name("test_space_err");
        request.set_src_space_id(1);
        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);
        base::Status st;
        st = _state_machine->do_control_copy_space(data, NULL, 1);
        ASSERT_EQ(st.error_code(), AIE_NOT_EXIST);
    }
    //copy space fail due to space id is error.
    {
        aries::pb::CopySpaceRequest request;
        aries::pb::AckResponse response;
        request.set_token("default_token");
        request.set_src_space_name("test_space1");
        request.set_src_space_id(2);
        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);
        base::Status st;
        st = _state_machine->do_control_copy_space(data, NULL, 1);
        ASSERT_EQ(st.error_code(), AIE_FAIL);
    }
    // copy space successed
    {
        auto space = g_space_manager->get_space("test_space1");
        space->set_state(SPACE_STATE_COPY);
        aries::pb::CopySpaceRequest request;
        aries::pb::AckResponse response;
        request.set_token("default_token");
        request.set_src_space_name("test_space1");
        request.set_src_space_id(1);
        request.set_permit_copy_space(false);
        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);
        base::Status st;
        st = _state_machine->do_control_copy_space(data, NULL, 1);
        ASSERT_EQ(st.error_code(), AIE_OK);
        EXPECT_EQ(space->permit_copy_space(), false);
        space->set_state(SPACE_STATE_NORMAL);
    }

}

TEST_F(MasterStateMachineTests, test_cancel_copy_space) {

     // prepare
    auto space = g_space_manager->get_space("test_space1");
    
    //copy space fail due to space state err.
    {
        auto space = g_space_manager->get_space("test_space1");
        space->set_state(SPACE_STATE_REDUCE_REDUNDENCY);

        aries::pb::CopySpaceRequest request;
        aries::pb::AckResponse response;
        request.set_token("default_token");
        request.set_src_space_name("test_space1");
        request.set_src_space_id(1);
        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);
        base::Status st;
        st = _state_machine->do_cancel_copy_space(data, NULL, 1);
        ASSERT_EQ(st.error_code(), AIE_SPACE_NOT_READY);
        space->set_state(SPACE_STATE_NORMAL);
    }
    //copy space fail due to space not exist.
    {
        aries::pb::CopySpaceRequest request;
        aries::pb::AckResponse response;
        request.set_token("default_token");
        request.set_src_space_name("test_space_err");
        request.set_src_space_id(1);
        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);
        base::Status st;
        st = _state_machine->do_cancel_copy_space(data, NULL, 1);
        ASSERT_EQ(st.error_code(), AIE_NOT_EXIST);
    }
    //copy space fail due to space id is error.
    {
        aries::pb::CopySpaceRequest request;
        aries::pb::AckResponse response;
        request.set_token("default_token");
        request.set_src_space_name("test_space1");
        request.set_src_space_id(2);
        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);
        base::Status st;
        st = _state_machine->do_cancel_copy_space(data, NULL, 1);
        ASSERT_EQ(st.error_code(), AIE_FAIL);
    }
    // copy space successed
    {
        auto space = g_space_manager->get_space("test_space1");
        space->set_state(SPACE_STATE_COPY);
        aries::pb::CopySpaceRequest request;
        aries::pb::AckResponse response;
        request.set_token("default_token");
        request.set_src_space_name("test_space1");
        request.set_src_space_id(1);
        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);
        base::Status st;
        st = _state_machine->do_cancel_copy_space(data, NULL, 1);
        ASSERT_EQ(st.error_code(), AIE_OK);
        EXPECT_EQ(space->state(), SPACE_STATE_NORMAL);
        VolumeVector volume_list;
        space->get_volume_list(&volume_list);
        for (auto& volume : volume_list) {
            EXPECT_EQ(volume->copy_progress(), COPY_NONE);
        }

    }
}

TEST_F(MasterStateMachineTests, test_finish_copy_space) {
     // prepare
    auto space = g_space_manager->get_space("test_space1");
    //copy space fail due to space state err.
    {
        auto space = g_space_manager->get_space("test_space1");
        space->set_state(SPACE_STATE_REDUCE_REDUNDENCY);

        aries::pb::CopySpaceRequest request;
        aries::pb::AckResponse response;
        request.set_token("default_token");
        request.set_src_space_name("test_space1");
        request.set_src_space_id(1);
        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);
        base::Status st;
        st = _state_machine->do_finish_copy_space(data, NULL, 1);
        ASSERT_EQ(st.error_code(), AIE_SPACE_NOT_READY);
        space->set_state(SPACE_STATE_NORMAL);
    }
    //copy space fail due to space not exist.
    {
        aries::pb::CopySpaceRequest request;
        aries::pb::AckResponse response;
        request.set_token("default_token");
        request.set_src_space_name("test_space_err");
        request.set_src_space_id(1);
        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);
        base::Status st;
        st = _state_machine->do_finish_copy_space(data, NULL, 1);
        ASSERT_EQ(st.error_code(), AIE_NOT_EXIST);
    }
    //copy space fail due to space id is error.
    {
        aries::pb::CopySpaceRequest request;
        aries::pb::AckResponse response;
        request.set_token("default_token");
        request.set_src_space_name("test_space1");
        request.set_src_space_id(2);
        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);
        base::Status st;
        st = _state_machine->do_finish_copy_space(data, NULL, 1);
        ASSERT_EQ(st.error_code(), AIE_FAIL);
    }
    // copy space successed failed due volume not in COPY END state
    {
        auto space = g_space_manager->get_space("test_space1");

        space->set_state(SPACE_STATE_COPY);
        aries::pb::CopySpaceRequest request;
        aries::pb::AckResponse response;
        request.set_token("default_token");
        request.set_src_space_name("test_space1");
        request.set_src_space_id(1);
        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);
        base::Status st;
        st = _state_machine->do_finish_copy_space(data, NULL, 1);
        ASSERT_EQ(st.error_code(), AIE_FAIL);
        space->set_state(SPACE_STATE_NORMAL);
    }
    // copy space successed 
    {
        auto space = g_space_manager->get_space("test_space1");
        VolumeVector volume_list;
        space->get_volume_list(&volume_list);
        for (auto volume : volume_list) {
            volume->set_copy_progress(COPY_END);
        }
        space->set_state(SPACE_STATE_COPY);
        aries::pb::CopySpaceRequest request;
        aries::pb::AckResponse response;
        request.set_token("default_token");
        request.set_src_space_name("test_space1");
        request.set_src_space_id(1);
        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);
        base::Status st;
        st = _state_machine->do_finish_copy_space(data, NULL, 1);
        ASSERT_EQ(st.error_code(), AIE_OK);
        for (auto volume : volume_list) {
            EXPECT_EQ(volume->copy_progress(), COPY_NONE);
        }
    }
}

aries::pb::CopyVletRequest get_copy_vlet_request() {
    auto space = g_space_manager->get_space("test_space1");
    VolumeVector volume_list;
    space->get_volume_list(&volume_list);

    aries::pb::CopyVletRequest request;
    request.set_token("default_token");
    auto src_vlet_info = request.mutable_copy_vlet_info()->mutable_src_vlet_info();
    auto src_space_info = request.mutable_copy_vlet_info()->mutable_src_space_info();
    src_vlet_info->set_volume_id(volume_list[0]->volume_id());
    src_vlet_info->set_shard_index(0);
    src_space_info->set_n(space->n());
    src_space_info->set_k(space->k());
    src_space_info->set_vlet_type(space->vlet_type());
    src_space_info->set_ec_type(space->ec_type());

    auto dest_space_option = request.mutable_copy_vlet_info()->mutable_dest_space_option();
    dest_space_option->set_cluster_id(g_meta_data->cluster_id());
    dest_space_option->set_space_name(space->space_name());
    dest_space_option->set_space_id(space->space_id());
    return request;
}

TEST_F(MasterStateMachineTests, test_copy_vlet) {

     // copy space failed due space not exist
     {
        aries::pb::CopyVletRequest request;
        aries::pb::AckResponse response;
        request = get_copy_vlet_request();
        request.mutable_copy_vlet_info()->mutable_dest_space_option()->set_space_name("123");
        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);
        base::Status st;
        st = _state_machine->do_set_copying_vlet(data, NULL, 1);
        ASSERT_EQ(st.error_code(), AIE_NOT_EXIST);
     }
     // copy space failed due state not right
     {
        auto space = g_space_manager->get_space("test_space1");
        space->set_state(SPACE_STATE_REDUCE_REDUNDENCY);
        aries::pb::CopyVletRequest request;
        aries::pb::AckResponse response;
        request = get_copy_vlet_request();
        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);
        base::Status st;
        st = _state_machine->do_set_copying_vlet(data, NULL, 1);
        ASSERT_EQ(st.error_code(), AIE_FAIL);
        space->set_state(SPACE_STATE_NORMAL);
     }
     // check_copy_space_request id not right
     {
        auto space = g_space_manager->get_space("test_space1");
        aries::pb::CopyVletRequest request;
        aries::pb::AckResponse response;
        request = get_copy_vlet_request();
        request.mutable_copy_vlet_info()->mutable_dest_space_option()->set_space_id(123);
        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);
        base::Status st;
        st = _state_machine->do_set_copying_vlet(data, NULL, 1);
        ASSERT_EQ(st.error_code(), AIE_FAIL);
     }
     // check_copy_space_request N not right
     {
        auto space = g_space_manager->get_space("test_space1");
        aries::pb::CopyVletRequest request;
        aries::pb::AckResponse response;
        request = get_copy_vlet_request();
        request.mutable_copy_vlet_info()->mutable_src_space_info()->set_n(123);
        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);
        base::Status st;
        st = _state_machine->do_set_copying_vlet(data, NULL, 1);
        ASSERT_EQ(st.error_code(), AIE_FAIL);
     }
     // check_copy_space_request K not right
     {
        auto space = g_space_manager->get_space("test_space1");
        aries::pb::CopyVletRequest request;
        aries::pb::AckResponse response;
        request = get_copy_vlet_request();
        request.mutable_copy_vlet_info()->mutable_src_space_info()->set_k(123);
        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);
        base::Status st;
        st = _state_machine->do_set_copying_vlet(data, NULL, 1);
        ASSERT_EQ(st.error_code(), AIE_FAIL);
     }
     // check_copy_space_request vlet_type not right
     {
        auto space = g_space_manager->get_space("test_space1");
        aries::pb::CopyVletRequest request;
        aries::pb::AckResponse response;
        request = get_copy_vlet_request();
        request.mutable_copy_vlet_info()->mutable_src_space_info()->set_vlet_type(VLET_TYPE_LINKED_16G_1M_512K);
        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);
        base::Status st;
        st = _state_machine->do_set_copying_vlet(data, NULL, 1);
        ASSERT_EQ(st.error_code(), AIE_FAIL);
     }
     // set_copying_vlet volume not exist will create succ
     {
        auto space = g_space_manager->get_space("test_space1");
        aries::pb::CopyVletRequest request;
        aries::pb::AckResponse response;
        request = get_copy_vlet_request();
        request.mutable_copy_vlet_info()->mutable_src_vlet_info()->set_volume_id(123);
        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);
        base::Status st;
        st = _state_machine->do_set_copying_vlet(data, NULL, 1);
        ASSERT_EQ(st.error_code(), AIE_OK);
        auto volume = g_volume_manager->get_volume(123);
        assert(volume != nullptr);
        EXPECT_EQ(volume->state(), VOLUME_STATE_RECEIVING);
        for (int i = 0; i < volume->n(); ++i) {
            auto vlet = volume->get_vlet(i);
            assert(vlet != nullptr);
            if (vlet->shard_index() == request.copy_vlet_info().src_vlet_info().shard_index()) {
                EXPECT_EQ(vlet->state(), VLET_STATE_COPYING);
            } else {
                EXPECT_EQ(vlet->state(), VLET_STATE_RECEIVING);
            }
        }
     }
     // set_copying_vlet succ but vlet or volume state not right, must copy finished
     {
        auto space = g_space_manager->get_space("test_space1");
        VolumeVector volume_list;
        space->get_volume_list(&volume_list);
        auto vlet = volume_list[0]->get_vlet(0);
        aries::pb::CopyVletRequest request;
        aries::pb::AckResponse response;
        request = get_copy_vlet_request();
        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);
        base::Status st;
        st = _state_machine->do_set_copying_vlet(data, NULL, 1);
        ASSERT_EQ(st.error_code(), AIE_OK);
     }
     // copy vlet succ
    {
        auto space = g_space_manager->get_space("test_space1");
        VolumeVector volume_list;
        space->get_volume_list(&volume_list);
        auto vlet = volume_list[0]->get_vlet(0);
        volume_list[0]->set_state(VOLUME_STATE_RECEIVING);
        vlet->set_state(VLET_STATE_RECEIVING);
        aries::pb::CopyVletRequest request;
        aries::pb::AckResponse response;
        request = get_copy_vlet_request();
        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);
        base::Status st;
        st = _state_machine->do_set_copying_vlet(data, NULL, 1);
        ASSERT_EQ(st.error_code(), AIE_OK);
        EXPECT_EQ(vlet->state(), VLET_STATE_COPYING); // receiving -> copying
        volume_list[0]->set_state(VOLUME_STATE_NORMAL);
        vlet->set_state(VLET_STATE_NORMAL);
    }
}

aries::pb::ReportMigrateVletRequest gen_copy_vlet_finish_request() {
    assert(g_node_manager->_node_map.size() >= 2);
    auto volume = g_volume_manager->get_volume(3);
    auto vlet = volume->get_vlet(0);
    volume->set_state(VOLUME_STATE_RECEIVING);
    vlet->set_state(VLET_STATE_COPYING);
    aries::pb::ReportMigrateVletRequest request;
    request.set_token("default_token");
    request.set_success(true);
    request.set_purpose(COPY_SPACE);
    auto src_vlet = request.mutable_src_vlet();
    src_vlet->set_volume_id(3);
    src_vlet->set_shard_index(0);
    src_vlet->set_vlet_version(0);
    src_vlet->set_vlet_type(VLET_TYPE_LINKED_32G_4M_512K);
    auto src_addr = g_node_manager->_node_map.begin()->first;
    src_vlet->set_node_addr(common::endpoint2int(src_addr));
    

    auto dest_vlet = request.mutable_dest_vlet();
    dest_vlet->set_volume_id(3);
    dest_vlet->set_shard_index(0);
    dest_vlet->set_vlet_version(1);
    dest_vlet->set_vlet_type(VLET_TYPE_LINKED_32G_4M_512K);
    auto dest_addr = (++g_node_manager->_node_map.begin())->first;
    dest_vlet->set_node_addr(common::endpoint2int(dest_addr));
    dest_vlet->set_disk_id(1);
    return request;
}
void clear_copy_vlet_finish_request() {
    auto volume = g_volume_manager->get_volume(3);
    auto vlet = volume->get_vlet(0);
    vlet->set_state(VLET_STATE_NORMAL);
}

TEST_F(MasterStateMachineTests, Test_report_copy_vlet_finish) {
    // copy vlet failed due version not match 
    {
        aries::pb::ReportMigrateVletRequest request;
        aries::pb::AckResponse response;
        request = gen_copy_vlet_finish_request();
        request.mutable_src_vlet()->set_vlet_version(2310);
        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);
        base::Status st;
        st = _state_machine->do_copy_vlet_finish(data, NULL, 1);
        ASSERT_EQ(st.error_code(), AIE_FAIL);
        clear_copy_vlet_finish_request();
    }
    // copy vlet failed due volume not exist
    {
        aries::pb::ReportMigrateVletRequest request;
        aries::pb::AckResponse response;
        request = gen_copy_vlet_finish_request();
        request.mutable_src_vlet()->set_volume_id(213);
        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);
        base::Status st;
        st = _state_machine->do_copy_vlet_finish(data, NULL, 1);
        ASSERT_EQ(st.error_code(), AIE_FAIL);
        clear_copy_vlet_finish_request();
    }
    // copy vlet failed due vlet not exist
    {
        aries::pb::ReportMigrateVletRequest request;
        aries::pb::AckResponse response;
        request = gen_copy_vlet_finish_request();
        request.mutable_src_vlet()->set_shard_index(213);
        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);
        base::Status st;
        st = _state_machine->do_copy_vlet_finish(data, NULL, 1);
        ASSERT_EQ(st.error_code(), AIE_FAIL);
        clear_copy_vlet_finish_request();
    }
    // copy vlet failed due src vlet version not match
    {
        aries::pb::ReportMigrateVletRequest request;
        aries::pb::AckResponse response;
        request = gen_copy_vlet_finish_request();
        request.mutable_src_vlet()->set_vlet_version(213);
        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);
        base::Status st;
        st = _state_machine->do_copy_vlet_finish(data, NULL, 1);
        ASSERT_EQ(st.error_code(), AIE_FAIL);
        clear_copy_vlet_finish_request();
    }
    // copy vlet failed due src vlet stste not match
    {
        aries::pb::ReportMigrateVletRequest request;
        aries::pb::AckResponse response;
        request = gen_copy_vlet_finish_request();
        auto volume = g_volume_manager->get_volume(3);
        auto vlet = volume->get_vlet(0);
        vlet->set_state(VLET_STATE_NORMAL);
        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);
        base::Status st;
        st = _state_machine->do_copy_vlet_finish(data, NULL, 1);
        ASSERT_EQ(st.error_code(), AIE_FAIL);
        clear_copy_vlet_finish_request();
    }
    // copy vlet succ
    {
        aries::pb::ReportMigrateVletRequest request;
        aries::pb::AckResponse response;
        request = gen_copy_vlet_finish_request();
        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);
        base::Status st;
        st = _state_machine->do_copy_vlet_finish(data, NULL, 1);
        ASSERT_EQ(st.error_code(), AIE_OK);
        clear_copy_vlet_finish_request();

    }
}

aries::pb::SetMembershipFailedRequest get_set_membership_failed_request() {
    auto volume = g_volume_manager->get_volume(3);
    auto vlet_0 = volume->get_vlet(0);
    volume->set_state(VOLUME_STATE_RECEIVING);
    vlet_0->set_state(VLET_STATE_COPYED);
    auto vlet_1 = volume->get_vlet(1);
    vlet_1->set_state(VLET_STATE_COPYED);
    
    aries::pb::SetMembershipFailedRequest request;
    {
        auto vlet_info = request.add_vlet_info_list();
        vlet_info->set_volume_id(3);
        vlet_info->set_shard_index(0);
        vlet_info->set_node_addr(common::endpoint2int(vlet_0->node()->addr()));
        vlet_info->set_disk_id(vlet_0->disk()->disk_id());
        vlet_info->set_vlet_type(VLET_TYPE_LINKED_16G_1M_512K);
        vlet_info->set_vlet_version(vlet_0->vlet_version());
        vlet_info->set_state(vlet_0->state());
    }
    {
        auto vlet_info = request.add_vlet_info_list();
        vlet_info->set_volume_id(3);
        vlet_info->set_shard_index(1);
        vlet_info->set_node_addr(common::endpoint2int(vlet_1->node()->addr()));
        vlet_info->set_disk_id(vlet_1->disk()->disk_id());
        vlet_info->set_vlet_type(VLET_TYPE_LINKED_16G_1M_512K);
        vlet_info->set_vlet_version(vlet_1->vlet_version());
        vlet_info->set_state(vlet_1->state());

    }  
    return request;
}

void clear_set_membership_failed_request() {
    auto volume = g_volume_manager->get_volume(3);
    volume->set_state(VOLUME_STATE_NORMAL);
    auto vlet_0 = volume->get_vlet(0);
    vlet_0->set_vlet_version(0);
    vlet_0->set_state(VLET_STATE_NORMAL);
    auto vlet_1 = volume->get_vlet(1);
    vlet_1->set_state(VLET_STATE_NORMAL);
    vlet_1->set_vlet_version(0);
}

TEST_F(MasterStateMachineTests, Test_set_membership_failed) {
    g_meta_data->set_state(MASTER_STATE_LEADER_WORKING);
    // volume not exist
    {
        aries::pb::SetMembershipFailedRequest request;
        request = get_set_membership_failed_request();
        request.mutable_vlet_info_list(0)->set_volume_id(123);
        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);
        base::Status st;
        st = _state_machine->do_set_membership_failed(data, NULL, 1);
        ASSERT_EQ(st.error_code(), AIE_NOT_EXIST);
        clear_set_membership_failed_request();
    }
    // volume state not right
    {
        aries::pb::SetMembershipFailedRequest request;
        request = get_set_membership_failed_request();
        auto volume = g_volume_manager->get_volume(3);
        volume->set_state(VOLUME_STATE_NORMAL);
        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);
        base::Status st;
        st = _state_machine->do_set_membership_failed(data, NULL, 1);
        ASSERT_EQ(st.error_code(), AIE_FAIL);
        clear_set_membership_failed_request();
    }
    // vlet state not right, ignore
    {
        
        aries::pb::SetMembershipFailedRequest request;
        request = get_set_membership_failed_request();
        auto volume = g_volume_manager->get_volume(3);
        auto vlet_0 = volume->get_vlet(0);
        vlet_0->set_state(VLET_STATE_NORMAL);
        auto vlet_1 = volume->get_vlet(1);
        vlet_1->set_state(VLET_STATE_NORMAL);
        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);
        base::Status st;
        st = _state_machine->do_set_membership_failed(data, NULL, 1);
        ASSERT_EQ(st.error_code(), AIE_OK);
        clear_set_membership_failed_request();
    }
    // vlet version not right
    {
        aries::pb::SetMembershipFailedRequest request;
        request = get_set_membership_failed_request();
        auto volume = g_volume_manager->get_volume(3);
         auto vlet_0 = volume->get_vlet(0);
        vlet_0->set_vlet_version(123);
        auto vlet_1 = volume->get_vlet(1);
        vlet_1->set_vlet_version(123);
        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);
        base::Status st;
        st = _state_machine->do_set_membership_failed(data, NULL, 1);
        ASSERT_EQ(st.error_code(), AIE_FAIL);
        clear_set_membership_failed_request();
    }
    // succ, will drop old vlet
    {
        auto volume = g_volume_manager->get_volume(3);
        auto vlet_0 = volume->get_vlet(0);
        auto vlet_1 = volume->get_vlet(1);
        aries::pb::SetMembershipFailedRequest request;
        request = get_set_membership_failed_request();
        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);
        base::Status st;
        st = _state_machine->do_set_membership_failed(data, NULL, 1);
        ASSERT_EQ(st.error_code(), AIE_OK);
        EXPECT_EQ(vlet_0->state(), VLET_STATE_RECEIVING);
        EXPECT_EQ(vlet_1->state(), VLET_STATE_RECEIVING);
        EXPECT_EQ(vlet_0->disk(), nullptr);
        EXPECT_EQ(vlet_1->disk(), nullptr);
        clear_set_membership_failed_request();
    }
}

TEST_F(MasterStateMachineTests, test_add_volumes) {
    // proto error
    {
        base::IOBuf err_data = prepare_err_data();
        base::Status st;
        st = _state_machine->do_add_volumes(err_data, NULL, 0);
        ASSERT_EQ(st.error_code(), AIE_FAIL);
    }
    // space is not exist
    {
        aries::pb::AddVolumeRequest request;
        request.set_token("default_token");
        auto m_volume = request.add_volumes();
        m_volume->set_volume_id(1221200);
        m_volume->set_space_name("not_exist_s1");
        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);
        base::Status st;
        st = _state_machine->do_add_volumes(data, NULL, 0);
        ASSERT_EQ(st.error_code(), AIE_OK);
        ASSERT_EQ(nullptr, g_volume_manager->get_volume(1221200));
    }
    // success
    {
        g_meta_data->set_state(MASTER_STATE_FOLLOWER);
        aries::pb::AddVolumeRequest request;
        request.set_token("default_token");
        auto m_volume = request.add_volumes();
        m_volume->set_volume_id(1221201);
        m_volume->set_space_name("test_space1");
        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);
        base::Status st;
        st = _state_machine->do_add_volumes(data, NULL, 0);
        ASSERT_EQ(st.error_code(), AIE_OK);
        ASSERT_NE(nullptr, g_volume_manager->get_volume(1221201));
        ASSERT_EQ("test_space1", g_volume_manager->get_volume(1221201)->space_name());
        VletVector vec;
        g_volume_manager->get_volume(1221201)->get_vlet_list(&vec);
        ASSERT_EQ(vec.size(), g_space_manager->get_space("test_space1")->n());
        ASSERT_TRUE(g_volume_manager->get_max_volume_id() > 1221201);
        g_meta_data->set_state(MASTER_STATE_LEADER_WORKING);
    }
}

TEST_F(MasterStateMachineTests, test_seal_archive_volume) {
    {
        // err iobuf
        base::Status st;
        base::IOBuf err_data = prepare_err_data();
        st = _state_machine->do_seal_archive_volume(err_data, NULL, 1);
        ASSERT_EQ(st.error_code(), AIE_FAIL);
    }
    {
        // volume not exist
        aries::pb::VolumeRequest request;
        request.set_token("default_token");
        request.set_volume_id(12345);

        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);
        base::Status st;
        st = _state_machine->do_seal_archive_volume(data, NULL, 1);
        ASSERT_EQ(st.error_code(), AIE_NOT_EXIST);
    }
    {
        // state is wrong
        auto volume = g_volume_manager->get_volume(1);
        aries::pb::VolumeRequest request;
        request.set_token("default_token");
        request.set_volume_id(1);
        volume->set_is_sealed(true);

        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);
        base::Status st;
        st = _state_machine->do_seal_archive_volume(data, NULL, 1);
        ASSERT_EQ(st.error_code(), AIE_INVALID);
    }
    {
        // succ
        auto volume = g_volume_manager->get_volume(3);
        aries::pb::VolumeRequest request;
        request.set_token("default_token");
        request.set_volume_id(3);

        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);
        base::Status st;
        st = _state_machine->do_seal_archive_volume(data, NULL, 1);
        EXPECT_EQ(st.error_code(), AIE_OK);
        EXPECT_EQ(volume->is_sealed(), true);
    }
}

TEST_F(MasterStateMachineTests, test_set_volume_ttl) {
    {
        // err iobuf
        base::Status st;
        base::IOBuf err_data = prepare_err_data();
        st = _state_machine->do_set_volume_ttl(err_data, NULL, 1);
        ASSERT_EQ(st.error_code(), AIE_FAIL);
    }
    {
        // volume not exist
        aries::pb::SetVolumeTtlRequest request;
        request.set_token("default_token");
        request.set_volume_id(12345);
        request.set_volume_ttl_timestamp(12345);

        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);
        base::Status st;
        st = _state_machine->do_set_volume_ttl(data, NULL, 1);
        ASSERT_EQ(st.error_code(), AIE_NOT_EXIST);
    }
    {
        // state is wrong
        auto volume = g_volume_manager->get_volume(1);
        aries::pb::SetVolumeTtlRequest request;
        request.set_token("default_token");
        request.set_volume_id(1);
        request.set_volume_ttl_timestamp(12345);
        volume->set_is_sealed(false);

        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);
        base::Status st;
        st = _state_machine->do_set_volume_ttl(data, NULL, 1);
        ASSERT_EQ(st.error_code(), AIE_INVALID);
    }
    {
        // succ
        auto volume = g_volume_manager->get_volume(3);
        aries::pb::SetVolumeTtlRequest request;
        request.set_token("default_token");
        request.set_volume_id(3);
        request.set_volume_ttl_timestamp(12345);
        volume->set_is_sealed(true);
        volume->set_volume_ttl_timestamp(0);

        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);
        base::Status st;
        g_meta_data->_volume_manager->_volume_map[3]->space()->set_archive_volume(true);
        st = _state_machine->do_set_volume_ttl(data, NULL, 1);
        EXPECT_EQ(st.error_code(), AIE_OK);
        EXPECT_EQ(volume->volume_ttl_timestamp(), 12345);
    }
}

TEST_F(MasterStateMachineTests, test_update_volume_tape_info) {
    {
        // err iobuf
        base::IOBuf err_data = prepare_err_data();
        base::Status st;
        st = _state_machine->do_update_volume_tape_info(err_data, NULL, 1);
        ASSERT_EQ(st.error_code(), AIE_FAIL);
    }
    {   
        // volume not exist
        aries::pb::UpdateVolumeTapeInfoRequest request;
        request.set_token("default_token");
        request.set_volume_id(12345);
        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);
        
        base::Status st;
        st = _state_machine->do_update_volume_tape_info(data, NULL, 1);
        ASSERT_EQ(st.error_code(), AIE_NOT_EXIST);
    }
    {
        // volume is dropped
        auto volume = g_volume_manager->get_volume(1);
        volume->set_state(VOLUME_STATE_DROPPED);
        aries::pb::UpdateVolumeTapeInfoRequest request;
        request.set_token("default_token");
        request.set_volume_id(1);
        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);
        
        base::Status st;
        st = _state_machine->do_update_volume_tape_info(data, NULL, 1);
        ASSERT_EQ(st.error_code(), AIE_INVALID);
    }
    {
        // succ update location_on_tape and stats_on_tape
        auto volume = g_volume_manager->get_volume(2);

        aries::pb::UpdateVolumeTapeInfoRequest request;
        request.set_token("default_token");
        request.set_volume_id(2);
        auto l = request.mutable_location_on_tape()->add_location_on_tape_list();
        l->set_physical_pool_name("test");
        request.mutable_stats_on_tape()->set_actual_size(1);
        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);

        base::Status st;
        st = _state_machine->do_update_volume_tape_info(data, NULL, 1);
        ASSERT_EQ(st.error_code(), AIE_OK);
        EXPECT_TRUE(volume->location_on_tape() != nullptr);
        EXPECT_TRUE(volume->stats_on_tape() != nullptr);
        EXPECT_EQ(volume->purge_state(), VOLUME_PURGE_STATE_NONE);
        EXPECT_EQ(volume->need_purge(), false);
    }
    {
        // succ update purge state: VOLUME_PURGE_STATE_NONE -> VOLUME_PURGE_STATE_WAITING 
        auto volume = g_volume_manager->get_volume(3);
        aries::pb::VolumeLocationOnTape tmp;
        volume->set_location_on_tape(tmp);
        volume->set_purge_state(VOLUME_PURGE_STATE_NONE);
        
        aries::pb::UpdateVolumeTapeInfoRequest request;
        request.set_token("default_token");
        request.set_volume_id(3);
        request.set_purge_state(VOLUME_PURGE_STATE_WAITING);
        request.set_last_purge_timestamp(123456);
        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);

        base::Status st;
        st = _state_machine->do_update_volume_tape_info(data, NULL, 1);
        ASSERT_EQ(st.error_code(), AIE_OK);
        EXPECT_TRUE(volume->location_on_tape() != nullptr);
        EXPECT_EQ(volume->purge_state(), VOLUME_PURGE_STATE_WAITING);
        EXPECT_EQ(volume->need_purge(), true);
        EXPECT_EQ(volume->last_purge_timestamp(), 123456); 
    }
    {
        // succ update purge state: VOLUME_PURGE_STATE_WAITING -> VOLUME_PURGE_STATE_FINISH 
        auto volume = g_volume_manager->get_volume(4);
        aries::pb::VolumeLocationOnTape tmp;
        volume->set_location_on_tape(tmp);
        volume->set_purge_state(VOLUME_PURGE_STATE_WAITING);
        
        aries::pb::UpdateVolumeTapeInfoRequest request;
        request.set_token("default_token");
        request.set_volume_id(4);
        request.set_purge_state(VOLUME_PURGE_STATE_FINISH);
        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);

        base::Status st;
        st = _state_machine->do_update_volume_tape_info(data, NULL, 1);
        ASSERT_EQ(st.error_code(), AIE_OK);
        EXPECT_TRUE(volume->location_on_tape() != nullptr);
        EXPECT_EQ(volume->purge_state(), VOLUME_PURGE_STATE_FINISH);
        EXPECT_EQ(volume->need_purge(), false); 
    }
    {
        // error update purge state: volume does not have location_on_tape
        auto volume = g_volume_manager->get_volume(5);
        volume->set_purge_state(VOLUME_PURGE_STATE_NONE);
        
        aries::pb::UpdateVolumeTapeInfoRequest request;
        request.set_token("default_token");
        request.set_volume_id(5);
        request.set_purge_state(VOLUME_PURGE_STATE_WAITING);
        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);

        base::Status st;
        st = _state_machine->do_update_volume_tape_info(data, NULL, 1);
        ASSERT_EQ(st.error_code(), AIE_INVALID);
        EXPECT_EQ(volume->purge_state(), VOLUME_PURGE_STATE_NONE);
        EXPECT_EQ(volume->need_purge(), false); 

    }
    {
        // error update purge state: state is wrong
        auto volume = g_volume_manager->get_volume(6);
        aries::pb::VolumeLocationOnTape tmp;
        volume->set_location_on_tape(tmp);
        volume->set_purge_state(VOLUME_PURGE_STATE_NONE);
         
        aries::pb::UpdateVolumeTapeInfoRequest request;
        request.set_token("default_token");
        request.set_volume_id(6);
        request.set_purge_state(VOLUME_PURGE_STATE_FINISH);
        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);

        base::Status st;
        st = _state_machine->do_update_volume_tape_info(data, NULL, 1);
        ASSERT_EQ(st.error_code(), AIE_INVALID);
        EXPECT_EQ(volume->purge_state(), VOLUME_PURGE_STATE_NONE);
        EXPECT_EQ(volume->need_purge(), false); 
    }
}
TEST_F(MasterStateMachineTests, test_set_meta_backup_keys) {
    {
        //proto err
        base::IOBuf err_data = prepare_err_data();
        base::Status st;
        st = _state_machine->do_set_meta_backup_keys(err_data, NULL, 1);
        ASSERT_EQ(st.error_code(), AIE_FAIL);
    }
    {
        // success
        aries::pb::SetMetaBackupKeysRequest request;
        request.set_token("default_token");
        auto keys = request.mutable_keys();
        keys->set_access_key("abcde");
        keys->set_secret_key("fghij");

        base::IOBuf data;
        base::IOBufAsZeroCopyOutputStream wrapper(&data);
        request.SerializeToZeroCopyStream(&wrapper);
        base::Status st;
        st = _state_machine->do_set_meta_backup_keys(data, NULL, 2004);
        ASSERT_EQ(st.error_code(), AIE_OK);
    }
}

}//end of master
}//end of aries


