/**
 * ARIES - A Reliable and Impressive EB-level Storage.
 * Copyright (c) 2016 Baidu.com, Inc. All Rights Reserved.
 *
 * Author: <PERSON> <PERSON> (guo<PERSON>@baidu.com)
 * Date: 2016/11/07
 * Description: Unittest for Heartbeat Scheduler
 *
 */

#include <gtest/gtest.h>
#include <gmock/gmock.h>
#include "bmock.h"
#include <base/logging.h>
#include "baidu/inf/aries-api/common/types.h"
#include "baidu/inf/aries/master/conf.h"
#include "baidu/inf/aries/master/scheduler/heartbeat_scheduler.h"
#include "baidu/inf/aries/master/scheduler/balance_scheduler.h"

namespace aries {
namespace master {

using ::testing::_;
using ::testing::Return;
using ::testing::AtLeast;
using ::testing::SetArgPointee;

// mock
BMOCK_NS_CLASS_METHOD1(aries::master, AZManager, get_az_list,
        void(AZVector*));
BMOCK_NS_CLASS_METHOD1(aries::master, AZ, get_node_list,
        void(NodeVector*));
BMOCK_NS_CLASS_METHOD0(aries::master, HeartbeatScheduler, get_master_control, 
        MasterControl*());
BMOCK_NS_CLASS_METHOD0(aries::master, Node, total_size,
        uint64_t(void));

/*
 * We try to mock master control in this test case.
 * Only set_node_alive_state will be mocked.
 * Please add regarding mock method as needed.
 */
class HeartbeatSchedulerMockMasterControl : public ::aries::master::MasterControl {
public:
    using MasterControl::MasterControl;
    MOCK_METHOD1(set_node_offline, void(const std::shared_ptr<Node>&));
    MOCK_METHOD1(set_node_online, void(const std::shared_ptr<Node>&));
    void drop_offline_node(const std::shared_ptr<Node>& node) {
        g_node_manager->drop_node(node->addr());
    }
    virtual void set_unbalanced_node_to_normal(const std::shared_ptr<Node>& node) override {
        node->set_state(NODE_STATE_NORMAL);
    }
    virtual void disable_expired_node_safemode(const std::shared_ptr<Node>& node) override {
        node->set_disk_safemode(false, 0);
    }
};

/*
 * We only mocked get_master_control() function. Since g_master_control
 * is a global valuable which may impact other unit test.
 * All other function will be called as real.
 */
class MockHeartbeatScheduler : public ::aries::master::HeartbeatScheduler {
public:
    using HeartbeatScheduler::HeartbeatScheduler;
    MOCK_METHOD0(get_master_control, MasterControl*());
};

MasterControl* g_mock_master_control;
class MockHeartbeatScheduler2 : public ::aries::master::HeartbeatScheduler {
public:
    virtual MasterControl* get_master_control() override {
        return g_mock_master_control;
    }
};

class HeartbeatSchedulerTests : public ::testing::Test {
public:
    HeartbeatSchedulerTests() {
        _tinker_name = "127.0.0.1:6000";
        common::str2endpoint("127.0.0.1", 60001, &_datanode_addr);
        common::str2endpoint("127.0.0.1", 60003, &_datanode_addr2);
        common::str2endpoint("127.0.0.1", 60004, &_datanode_addr3);
        common::str2endpoint("127.0.0.1", 60005, &_datanode_addr4);
        common::str2endpoint("127.0.0.1", 60002, &_dataagent_addr);
        _allocator_name = "127.0.0.1:60003";
    }

    void add_tinker() {
        std::shared_ptr<Tinker> tinker(new Tinker(_tinker_name, base::EndPoint()));
        tinker->set_last_active_timestamp(time(NULL));
        ASSERT_EQ(AIE_OK, g_tinker_manager->add_tinker(tinker));
    }

    void remove_tinker() {
        g_tinker_manager->drop_tinker(_tinker_name);
    }

    void add_repairer() {
        std::shared_ptr<Repairer> repairer(new Repairer(_repairer_addr));
        repairer->set_last_active_timestamp(0);
        ASSERT_EQ(AIE_OK, g_repairer_manager->add_repairer(repairer));
    }

    void remove_repairer() {
        g_repairer_manager->drop_repairer(_repairer_addr);
    }

    void add_checkcenter() {
        std::shared_ptr<Checkcenter> checkcenter(new Checkcenter(_checkcenter_addr));
        checkcenter->set_last_active_timestamp(0);
        ASSERT_EQ(AIE_OK, g_checkcenter_manager->add_checkcenter(checkcenter));
    }

    void remove_checkcenter() {
        g_checkcenter_manager->drop_checkcenter(_checkcenter_addr);
    }

    void add_monitorcenter() {
        std::shared_ptr<Monitorcenter> monitorcenter(new Monitorcenter(_monitorcenter_addr));
        monitorcenter->set_last_active_timestamp(time(NULL));
        ASSERT_EQ(AIE_OK, g_monitorcenter_manager->add_monitorcenter(monitorcenter));
    }

    void remove_monitorcenter() {
        g_monitorcenter_manager->drop_monitorcenter(_monitorcenter_addr);
    }

    void add_datanode() {
        NodeOptions opt;
        opt.addr = _datanode_addr;
        opt.create_time = base::gettimeofday_us();
        std::shared_ptr<Node> node(new Node(opt));
        node->set_alive(true);
        node->set_last_active_timestamp(time(NULL));
        ASSERT_EQ(AIE_OK, g_meta_data->add_node(node));

        opt.addr = _datanode_addr2;
        std::shared_ptr<Node> node2(new Node(opt));
        node2->set_alive(false);
        node2->set_last_active_timestamp(0);
        ASSERT_EQ(AIE_OK, g_meta_data->add_node(node2));
    }

    void remove_datanode() {
        auto node = g_node_manager->get_node(_datanode_addr);
        auto node2 = g_node_manager->get_node(_datanode_addr2);
        if (node != nullptr) {
            g_meta_data->drop_node(node);
        }
        if (node != nullptr) {
            g_meta_data->drop_node(node2);
        }
    }

    void add_dataagent() {
        std::shared_ptr<DataAgent> agent(new DataAgent(_dataagent_addr, "xx"));
        agent->set_alive(true);
        agent->set_last_active_timestamp(time(NULL));
        ASSERT_EQ(AIE_OK, g_dataagent_manager->add_dataagent(agent));
    }

    void remove_dataagent() {
        std::DataAgentIdentify id = std::make_pair(_dataagent_addr, "xx");
        g_dataagent_manager->drop_dataagent(id);
    }

    void add_allocator() {
        std::shared_ptr<Allocator> allocator(new Allocator(_allocator_name, base::EndPoint(), 1));
        allocator->set_alive(true);
        allocator->set_last_active_timestamp(time(NULL));
        ASSERT_EQ(AIE_OK, g_allocator_manager->add_allocator(allocator));
    }

    void remove_allocator() {
        g_allocator_manager->drop_allocator(_allocator_name);
    }
    
    void add_stateservice() {
        std::shared_ptr<StateService> stateservice(new StateService(_stateservice_addr, 0));
        stateservice->set_alive(true);
        stateservice->set_last_active_timestamp(0);
        ASSERT_EQ(AIE_OK, g_stateservice_manager->add_stateservice(stateservice));
    }

    void remove_stateservice() {
        g_stateservice_manager->drop_stateservice(_stateservice_addr, 0);
    }

    void test_check_heartbeat() {
        FLAGS_tinker_dead_threshold_in_second = 3;
        FLAGS_repairer_dead_threshold_in_second = 3;
        FLAGS_monitorcenter_dead_threshold_in_second = 3;
        FLAGS_checkcenter_dead_threshold_in_second = 3;
        FLAGS_datanode_dead_threshold_in_second = 3;
        FLAGS_dataagent_dead_threshold_in_second = 3;
        FLAGS_allocator_dead_threshold_in_second = 3;
        FLAGS_stateservice_dead_threshold_in_second = 3;

        FLAGS_deadnode_auto_drop_in_second = 9;
        FLAGS_offline_datanode_check_time_in_second = 1;
        FLAGS_unbalanced_datanode_check_time_in_second = 100;

        add_tinker();
        add_repairer();
        add_checkcenter();
        add_monitorcenter();
        add_datanode();
        add_dataagent();
        add_allocator();
        add_stateservice();

        MockHeartbeatScheduler sched;
        auto master_control = new HeartbeatSchedulerMockMasterControl;
        sched._last_check_unbalanced_datanode_time = INT64_MAX;

        EXPECT_CALL(sched, get_master_control())
            .Times(AtLeast(1)).WillRepeatedly(Return(master_control));
        EXPECT_CALL(*master_control, set_node_offline(_))
            .Times(AtLeast(1));

        sleep(5);
        sched.start();
        sleep(3);
        // datanode2 is not exist
        auto datanode2 = g_node_manager->get_node(_datanode_addr2);
        ASSERT_NE(datanode2, nullptr);

        // Tinker expected to be marked as NOT alive
        std::shared_ptr<Tinker> tinker = g_tinker_manager->get_tinker(_tinker_name);
        ASSERT_FALSE(tinker->is_alive());
        std::shared_ptr<Repairer> repairer = g_repairer_manager->get_repairer(_repairer_addr);
        // ASSERT_FALSE(repairer->is_alive());
        ASSERT_TRUE(repairer == nullptr);
        std::shared_ptr<Checkcenter> checkcenter = g_checkcenter_manager->get_checkcenter(_checkcenter_addr);
        //ASSERT_FALSE(checkcenter->is_alive());
        ASSERT_TRUE(checkcenter == nullptr);
        std::shared_ptr<Monitorcenter> monitorcenter = g_monitorcenter_manager->get_monitorcenter(_monitorcenter_addr);
        // ASSERT_FALSE(monitorcenter->is_alive());
        ASSERT_TRUE(monitorcenter == nullptr);
        // datanode has been marked NOT alive
        std::shared_ptr<Node> datanode = g_node_manager->get_node(_datanode_addr);
        // ASSERT_FALSE(datanode->is_alive());
        // dataagent has been marked NOT alive
        std::DataAgentIdentify id = std::make_pair(_dataagent_addr, "xx");
        std::shared_ptr<DataAgent> dataagent = g_dataagent_manager->get_dataagent(id);
        // ASSERT_FALSE(dataagent->is_alive());
        ASSERT_TRUE(dataagent == nullptr);
        // allocator has been marked NOT alive
        std::shared_ptr<Allocator> allocator = g_allocator_manager->get_allocator(_allocator_name);
        ASSERT_FALSE(allocator->is_alive());

        auto stateservice = g_stateservice_manager->get_stateservice(_stateservice_addr, 0);
        ASSERT_TRUE(stateservice == nullptr);

        datanode->set_alive(false);
        sleep(10);
        datanode = g_node_manager->get_node(_datanode_addr);
        ASSERT_EQ(datanode, nullptr);
        datanode2 = g_node_manager->get_node(_datanode_addr2);
        ASSERT_EQ(datanode2, nullptr);

        sched.stop();
        sched.join();

        remove_tinker();
        remove_repairer();
        remove_checkcenter();
        remove_monitorcenter();
        remove_datanode();
        remove_dataagent();
        remove_allocator();
        remove_stateservice();

        delete master_control;
    }

    void TearDown() override {
        //clear env
        g_meta_data->_node_manager->_node_map.clear();
        g_meta_data->_space_manager->_space_map.clear();
        g_meta_data->_space_manager->_space_id = 0;
        g_meta_data->_volume_manager->_volume_map.clear();
        g_meta_data->_volume_manager->_max_volume_id = 0;
        g_meta_data->_az_manager->_az_map.clear();
        g_meta_data->_allocator_manager->_allocator_map.clear();
        g_meta_data->_allocator_manager->_max_sequence_id = 0;
        g_meta_data->_tinker_manager->_tinker_map.clear();
        g_meta_data->_raft_index = 0;
    }
private:
    std::string _tinker_name;
    base::EndPoint _repairer_addr;
    base::EndPoint _checkcenter_addr;
    base::EndPoint _monitorcenter_addr;
    base::EndPoint _datanode_addr;
    base::EndPoint _datanode_addr2;
    base::EndPoint _datanode_addr3;
    base::EndPoint _datanode_addr4;
    base::EndPoint _dataagent_addr;
    std::string _allocator_name;
    base::EndPoint _stateservice_addr;
};

TEST_F(HeartbeatSchedulerTests, check_disk_safemode_datanode) {
    // prepare node
    auto master_control = new HeartbeatSchedulerMockMasterControl;
    EXPECT_CALL(BMOCK_NS_CLASS_OBJECT(aries::master, HeartbeatScheduler, get_master_control), get_master_control())
            .Times(2)
            .WillRepeatedly(Return(master_control));
    uint64_t now = static_cast<uint64_t>(time(NULL));
    add_datanode();
    auto datanode = g_node_manager->get_node(_datanode_addr);
    auto datanode2 = g_node_manager->get_node(_datanode_addr2);
    datanode->set_disk_safemode(true, now);
    auto ptr = std::shared_ptr<aries::master::HeartbeatScheduler>(new aries::master::HeartbeatScheduler());
    {
        FLAGS_safemode_datanode_check_time_in_second = now + 1;
        ptr->check_safemode_datanode();
        EXPECT_TRUE(datanode->is_disk_safemode());
        EXPECT_FALSE(datanode2->is_disk_safemode());
    }
    {
        FLAGS_safemode_datanode_check_time_in_second = 180;
        FLAGS_auto_leave_safemode_node_in_second = 86400;
        ptr->check_safemode_datanode();
        EXPECT_TRUE(datanode->is_disk_safemode());
        EXPECT_FALSE(datanode2->is_disk_safemode());
    }
    {
        FLAGS_safemode_datanode_check_time_in_second = 0;
        FLAGS_auto_leave_safemode_node_in_second = 0;
        sleep(1);
        ptr->check_safemode_datanode();
        EXPECT_FALSE(datanode->is_disk_safemode());
        EXPECT_FALSE(datanode2->is_disk_safemode());
    }
    {
        datanode2->set_disk_safemode(true, now);
        sleep(1);
        ptr->check_safemode_datanode();
        EXPECT_FALSE(datanode->is_disk_safemode());
        EXPECT_FALSE(datanode2->is_disk_safemode());
    }
}

TEST_F(HeartbeatSchedulerTests, check_unbalanced_datanode) {
    NodeOptions option;
    option.az_name = "test_az";
    option.group_name = "group";
    option.state = NODE_STATE_UNBALANCED;
    option.addr = common::int2endpoint(1);
    auto node1 = std::make_shared<aries::master::Node>(option);
    option.addr = common::int2endpoint(2);
    auto node2 = std::make_shared<aries::master::Node>(option);
    auto disk1 = std::make_shared<Disk>(0, node1);
    auto disk2 = std::make_shared<Disk>(0, node2);
    disk1->set_disk_type("disktype");
    disk2->set_disk_type("disktype");
    node1->add_disk(disk1);
    node2->add_disk(disk2);
    node1->_total_size = 1000;
    node2->_total_size = 1000;
    g_az_manager->add_node(node1);
    g_az_manager->add_node(node2);
    EXPECT_EQ(node1->state(), NODE_STATE_UNBALANCED);
    EXPECT_EQ(node2->state(), NODE_STATE_UNBALANCED);
    FLAGS_avg_disk_usage_diff_ratio_node_become_balanced = 1.0;
    FLAGS_unbalanced_datanode_check_time_in_second = 0;

    AZVector az_list;
    g_az_manager->get_az_list(&az_list);
    auto az = az_list[0];

    auto ptr = std::shared_ptr<aries::master::HeartbeatScheduler>(new aries::master::MockHeartbeatScheduler2());
    disk1->_calc_used_size = 0;
    disk2->_calc_used_size = 800;
    HeartbeatSchedulerMockMasterControl cntl;
    g_mock_master_control = &cntl;
    // safemode
    {
        ptr->_last_check_unbalanced_datanode_time = 0;
        az->enter_safemode();
        ptr->check_unbalanced_datanode();
        EXPECT_EQ(node1->state(), NODE_STATE_UNBALANCED);
        EXPECT_EQ(node2->state(), NODE_STATE_UNBALANCED);
        az->leave_safemode();
    }
    // no avg usage data
    {
        ptr->check_unbalanced_datanode();
        EXPECT_EQ(node1->state(), NODE_STATE_UNBALANCED);
        EXPECT_EQ(node2->state(), NODE_STATE_UNBALANCED);
    }
    g_balance_scheduler->_average_disk_usage_map["test_az_group_disktype"] = 0.4;
    // node2
    {
        ptr->_last_check_unbalanced_datanode_time = 0;
        disk2->_calc_used_size = 800;
        ptr->check_unbalanced_datanode();
        EXPECT_EQ(node1->state(), NODE_STATE_UNBALANCED);
        EXPECT_EQ(node2->state(), NODE_STATE_NORMAL);
    }
    // node1
    {
        ptr->_last_check_unbalanced_datanode_time = 0;
        disk1->_calc_used_size = 300;
        disk2->_calc_used_size = 0;
        FLAGS_avg_disk_usage_diff_ratio_node_become_balanced = 0.5;
        ptr->check_unbalanced_datanode();
        EXPECT_EQ(node1->state(), NODE_STATE_NORMAL);
        EXPECT_EQ(node2->state(), NODE_STATE_NORMAL);
    }
}

TEST_F(HeartbeatSchedulerTests, check_heartbeat) {
    test_check_heartbeat();
}

TEST_F(HeartbeatSchedulerTests, check_safemode) {
    // mock
    auto az = std::shared_ptr<aries::master::AZ>(new aries::master::AZ("test_az"));
    AZVector az_list;
    az_list.push_back(az);
    EXPECT_CALL(BMOCK_NS_CLASS_OBJECT(aries::master, AZManager, get_az_list),
            get_az_list(_))
        .WillRepeatedly(SetArgPointee<0>(az_list));
    NodeOptions option;
    auto node1 = std::shared_ptr<aries::master::Node>(new aries::master::Node(option));
    node1->set_last_active_timestamp(0);
    auto node2 = std::shared_ptr<aries::master::Node>(new aries::master::Node(option));
    node2->set_last_active_timestamp(0);
    NodeVector node_list;
    node_list.push_back(node1);
    node_list.push_back(node2);
    EXPECT_CALL(BMOCK_NS_CLASS_OBJECT(aries::master, AZ, get_node_list),
            get_node_list(_))
        .WillRepeatedly(SetArgPointee<0>(node_list));

    auto ptr = std::shared_ptr<aries::master::HeartbeatScheduler>(new aries::master::HeartbeatScheduler());
    // normal -> safemode
    FLAGS_az_safemode_threshold = 1;
    ptr->check_datanode_heartbeat();
    ASSERT_TRUE(az->is_safemode());
    // safemode -> normal
    {
        EXPECT_CALL(BMOCK_NS_CLASS_OBJECT(aries::master, HeartbeatScheduler, get_master_control), get_master_control())
            .Times(2)
            .WillRepeatedly(Return(g_master_control)); ;
        FLAGS_az_safemode_threshold = 10;
        FLAGS_az_light_safemode_threshold = 5;
        FLAGS_deadnode_auto_drop_in_second = 1;
        FLAGS_offline_datanode_check_time_in_second = 1;
        ptr->_last_check_offline_datanode_time = static_cast<uint64_t>(time(NULL));
        ptr->check_datanode_heartbeat();
        sleep(2);
        ASSERT_FALSE(az->is_safemode());
        ASSERT_FALSE(az->is_light_safemode());
        ptr->check_offline_datanode();
    }
    {
        EXPECT_CALL(BMOCK_NS_CLASS_OBJECT(aries::master, HeartbeatScheduler, get_master_control), get_master_control())
            .Times(0);
        FLAGS_az_safemode_threshold = 10;
        FLAGS_az_light_safemode_threshold = 1;
        FLAGS_deadnode_auto_drop_in_second = 1;
        ptr->check_datanode_heartbeat();
        sleep(2);
        ASSERT_FALSE(az->is_safemode());
        ASSERT_TRUE(az->is_light_safemode());
        ptr->check_offline_datanode();
    }
}

TEST_F(HeartbeatSchedulerTests, set_node_dead_in_disable_az) {
    // mock
    auto az = std::shared_ptr<aries::master::AZ>(new aries::master::AZ("test_az"));
    AZVector az_list;
    az_list.push_back(az);
    EXPECT_CALL(BMOCK_NS_CLASS_OBJECT(aries::master, AZManager, get_az_list),
            get_az_list(_))
        .WillRepeatedly(SetArgPointee<0>(az_list));
    NodeOptions option;
    auto node1 = std::shared_ptr<aries::master::Node>(new aries::master::Node(option));
    node1->_is_alive = false;
    node1->set_last_active_timestamp(0);
    auto node2 = std::shared_ptr<aries::master::Node>(new aries::master::Node(option));
    node1->_is_alive = false;
    node2->set_last_active_timestamp(0);
    NodeVector node_list;
    node_list.push_back(node1);
    node_list.push_back(node2);
    auto mptr = new aries::master::MockHeartbeatScheduler();
    auto ptr = std::shared_ptr<aries::master::HeartbeatScheduler>(mptr);
    EXPECT_CALL(BMOCK_NS_CLASS_OBJECT(aries::master, AZ, get_node_list),
            get_node_list(_))
        .WillRepeatedly(SetArgPointee<0>(node_list));

    auto master_control = new HeartbeatSchedulerMockMasterControl;
    EXPECT_CALL(*mptr, get_master_control())
        .Times(AtLeast(1)).WillRepeatedly(Return(master_control));

    // normal -> safemode
    {
        EXPECT_CALL(*master_control, set_node_offline(_))
            .Times(AtLeast(1));
        FLAGS_az_safemode_threshold = 1;
        ptr->check_datanode_heartbeat();
        ASSERT_TRUE(az->is_safemode());
        g_az_manager->_disabled_az_name = "test";
    }
    // safemode -> normal failed
    //
    node1->_is_alive = false;
    {
        EXPECT_CALL(*master_control, set_node_online(_))
            .Times(AtLeast(1));
        FLAGS_az_safemode_threshold = 1;
        FLAGS_az_light_safemode_threshold = 5;
        FLAGS_deadnode_auto_drop_in_second = 1;
        FLAGS_offline_datanode_check_time_in_second = 1;
        ptr->_last_check_offline_datanode_time = static_cast<uint64_t>(time(NULL));
        node1->set_last_active_timestamp(static_cast<uint64_t>(time(NULL)));
        node2->set_last_active_timestamp(static_cast<uint64_t>(time(NULL)));
        ptr->check_datanode_heartbeat();
        sleep(2);
        ASSERT_FALSE(az->is_safemode());
        ASSERT_FALSE(az->is_light_safemode());
        ptr->check_offline_datanode();
    }
    g_az_manager->_disabled_az_name = "test_az";
    // safemode -> normal
    {
        EXPECT_CALL(*master_control, set_node_online(_))
            .Times(AtLeast(1));

        FLAGS_az_safemode_threshold = 10;
        FLAGS_az_light_safemode_threshold = 5;
        FLAGS_deadnode_auto_drop_in_second = 1;
        FLAGS_offline_datanode_check_time_in_second = 1;
        ptr->_last_check_offline_datanode_time = static_cast<uint64_t>(time(NULL));
        node1->set_last_active_timestamp(static_cast<uint64_t>(time(NULL)));
        node2->set_last_active_timestamp(static_cast<uint64_t>(time(NULL)));
        ptr->check_datanode_heartbeat();
        sleep(2);
        ASSERT_FALSE(az->is_safemode());
        ASSERT_FALSE(az->is_light_safemode());
        ptr->check_offline_datanode();
    }
    // node -> offline;
    {
        node1->set_last_active_timestamp(0);
        node2->set_last_active_timestamp(0);
        EXPECT_CALL(*master_control, set_node_offline(_))
            .Times(AtLeast(1));
        ptr->check_datanode_heartbeat();
    }
}

TEST_F(HeartbeatSchedulerTests, test_check_offline_datanode) {
    // mock
    auto az = std::shared_ptr<aries::master::AZ>(new aries::master::AZ("test_az"));
    AZVector az_list;
    az_list.push_back(az);
    EXPECT_CALL(BMOCK_NS_CLASS_OBJECT(aries::master, AZManager, get_az_list),
            get_az_list(_))
        .WillRepeatedly(SetArgPointee<0>(az_list));

    NodeOptions option;
    option.addr = _datanode_addr3;
    option.create_time = base::gettimeofday_us();
    auto node1 = std::shared_ptr<aries::master::Node>(new aries::master::Node(option));
    node1->set_alive(false);
    node1->set_last_active_timestamp(time(NULL));
    node1->set_offline_time(time(NULL));
    NodeOptions option2;
    option2.addr = _datanode_addr4;
    option2.create_time = base::gettimeofday_us();
    auto node2 = std::shared_ptr<aries::master::Node>(new aries::master::Node(option2));
    node2->set_alive(false);
    node2->set_last_active_timestamp(time(NULL));
    node2->set_offline_time(time(NULL));
    g_node_manager->add_node(node1);
    g_node_manager->add_node(node2);
    NodeVector test_node_list;
    test_node_list.push_back(node1);
    test_node_list.push_back(node2);

    auto mptr = new aries::master::MockHeartbeatScheduler();
    auto ptr = std::shared_ptr<aries::master::HeartbeatScheduler>(mptr);
    auto master_control = new HeartbeatSchedulerMockMasterControl;
    EXPECT_CALL(*mptr, get_master_control())
        .Times(AtLeast(1)).WillRepeatedly(Return(master_control));
    EXPECT_CALL(BMOCK_NS_CLASS_OBJECT(aries::master, AZ, get_node_list),
        get_node_list(_)).WillRepeatedly(SetArgPointee<0>(test_node_list));

    FLAGS_offline_datanode_check_time_in_second = 0;
    FLAGS_deadnode_auto_drop_in_second = 1 * 24 * 3600;
    FLAGS_high_density_deadnode_auto_drop_in_second = 7 * 24 * 3600;
    ptr->_last_check_offline_datanode_time = 0;
    ASSERT_FALSE(az->is_light_safemode());
    auto datanode1 = g_node_manager->get_node(_datanode_addr3);
    auto datanode2 = g_node_manager->get_node(_datanode_addr4);
    ASSERT_NE(datanode1, nullptr);
    ASSERT_NE(datanode2, nullptr);

    // deadnode < one day
    ptr->check_offline_datanode();
    datanode1 = g_node_manager->get_node(_datanode_addr3);
    datanode2 = g_node_manager->get_node(_datanode_addr4);
    ASSERT_NE(datanode1, nullptr);
    ASSERT_NE(datanode2, nullptr);

    // deadnode > one day, drop
    node1->set_offline_time(time(NULL) - FLAGS_deadnode_auto_drop_in_second - 1);
    ptr->check_offline_datanode();
    datanode1 = g_node_manager->get_node(_datanode_addr3);
    ASSERT_EQ(datanode1, nullptr);
    node1->set_alive(true);

    // deadnode > one day, repaire machine
    EXPECT_CALL(BMOCK_NS_CLASS_OBJECT(aries::master, Node, total_size), total_size())
        .WillRepeatedly(Return(FLAGS_high_density_node_capacity_threshold_in_tb * aries::common::TB + 1));
    node2->set_offline_time(time(NULL) - FLAGS_deadnode_auto_drop_in_second - 1);
    ptr->check_offline_datanode();
    datanode2 = g_node_manager->get_node(_datanode_addr4);
    ASSERT_NE(datanode2, nullptr);

    // deadnode > one week, drop node
    node2->set_offline_time(time(NULL) - FLAGS_high_density_deadnode_auto_drop_in_second - 1);
    ptr->check_offline_datanode();
    datanode2 = g_node_manager->get_node(_datanode_addr4);
    ASSERT_EQ(datanode2, nullptr);
}

}
}
