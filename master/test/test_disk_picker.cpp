/**
 * ARIES - A Reliable and Impressive EB-level Storage.
 * Copyright (c) 2016 Baidu.com, Inc. All Rights Reserved.
 *
 * Author: <PERSON> (<EMAIL>)
 * Date: 2016/11/14
 * Description: Unittest for Disk Picker
 *
 */

#include <gtest/gtest.h>
#include <base/logging.h>
#include <baidu/rpc/server.h>
#include "baidu/inf/aries-api/common/types.h"
#include "baidu/inf/aries/master/conf.h"
#include "baidu/inf/aries/master/scheduler/disk_picker.h"
#include "baidu/inf/aries/master/scheduler/balance_scheduler.h"
#include "baidu/inf/aries/master/test/test_utils.h"
#include "baidu/inf/aries/master/test/test_master_test_base.h"

namespace aries {
namespace master {

class DiskPickerTest : public::aries::master::MasterTestBase {
public:
    DiskPickerTest() {
        // This should not be removed since we need initiate
        // necessary entries for the testing.
        FLAGS_disk_reserve_percent = 0.1;
        init_env();
    }
    ~DiskPickerTest() {}
    static void SetUpTestCase() {
        g_balance_scheduler->set_balance_helper(g_balance_helper);
    }
    static void TearDownTestCase() {
    }
private:
    TestUtils _utils;
    const std::string _element_sample = "_element_sample";
};

/******************* PickFilter *******************/

/*
 * A general case test for PickFilter::check()
 */
TEST_F(DiskPickerTest, test_pick_filter_check_expect_true_with_brand_new) {
    PickFilter pick_filter(_utils.max_element_count);
    ASSERT_TRUE(pick_filter.check(_element_sample));
}

/*
 * A general case test for PickFilter::check()
 */
TEST_F(DiskPickerTest, test_pick_filter_check_expect_true_with_existing) {
    PickFilter pick_filter(_utils.max_element_count + 1);
    pick_filter.add_element(_element_sample);
    ASSERT_TRUE(pick_filter.check(_element_sample));
}

/*
 * A general case test for PickFilter::check()
 */
TEST_F(DiskPickerTest, test_pick_filter_check_expect_false) {
    PickFilter pick_filter(_utils.max_element_count);
    pick_filter.add_element(_element_sample);
    ASSERT_FALSE(pick_filter.check(_element_sample));
}

/*
 * A general case test for PickFilter::add()
 */
TEST_F(DiskPickerTest, test_pick_filter_add_element_with_brand_new_element) {
    PickFilter pick_filter(_utils.max_element_count);
    pick_filter.add_element(_element_sample);
    ASSERT_EQ(1, pick_filter._element_map.size());
}

/*
 * A general case test for PickFilter::add()
 */
TEST_F(DiskPickerTest, test_pick_filter_add_element_with_existing_element) {
    PickFilter pick_filter(_utils.max_element_count);
    pick_filter.add_element(_element_sample);
    ASSERT_EQ(1, pick_filter._element_map.size());
    ASSERT_EQ(1, pick_filter._element_map.at(_element_sample));

    // Ok, let't do it again!
    pick_filter.add_element(_element_sample);
    ASSERT_EQ(1, pick_filter._element_map.size());
    ASSERT_EQ(2, pick_filter._element_map.at(_element_sample));
}

/*
 * A general case test for GroupPickFilter::constructor()
 */
TEST_F(DiskPickerTest, test_group_picker_filter_happy_case) {
    std::set<std::string> group_one_mock_set, group_one_mock_set_tmp; 
    group_one_mock_set.insert(_utils.group_one_mock);
    group_one_mock_set_tmp = group_one_mock_set;
    GroupPickFilter group_picker_filter(group_one_mock_set);
    ASSERT_EQ(group_one_mock_set_tmp, group_picker_filter._group_set);
}

/******************* IDCPickFilter *******************/

TEST_F(DiskPickerTest, test_idc_pick_filter_constructor) {
    std::shared_ptr<Volume> volume = _volumes[0];
    VletVector vlet_list;
    volume->get_vlet_list(&vlet_list);
    NodeVector node_list;
    for (auto& vlet : vlet_list) {
        if (is_vlet_state_not_exist(vlet->state())) {
            auto node = vlet->tmp_node();
            if (node) {
                node_list.push_back(node);
            }
            continue;
        }
        auto node = vlet->node();
        if (node) {
            node_list.push_back(node);
        }
    }
    std::set<std::string> idc_set;
    idc_set.insert("idc_default");
    IDCPickFilter pick_filter(idc_set, node_list, _utils.max_element_count);
    ASSERT_FALSE(pick_filter.check("idc_defaultxxx"));
}

/******************* RackPickFilter *******************/

/*
 * A general case test for RackPickFilter::constructor()
 */
TEST_F(DiskPickerTest, test_rack_pick_filter_constructor) {
    std::shared_ptr<Volume> volume = _volumes[0];
    VletVector vlet_list;
    volume->get_vlet_list(&vlet_list);
    NodeVector node_list;
    for (auto& vlet : vlet_list) {
        if (is_vlet_state_not_exist(vlet->state())) {
            auto node = vlet->tmp_node();
            if (node) {
                node_list.push_back(node);
            }
            continue;
        }
        auto node = vlet->node();
        if (node) {
            node_list.push_back(node);
        }
    }
    std::set<std::string> rack_set;
    rack_set.insert("test_rack_0");
    rack_set.insert("test_rack_1");
    rack_set.insert("test_rack_2");
    RackPickFilter pick_filter(rack_set, node_list, _utils.max_element_count);
    // Please refer to MasterTestBase.cpp.
    // By default, the number of racks are three for this volume
     ASSERT_FALSE(pick_filter.check("test_rack_4"));
}

/******************* NodePickFilter *******************/

/*
 * A general case test for NodePickFilter::constructor()
 */
TEST_F(DiskPickerTest, test_node_pick_filter_constructor) {
    std::shared_ptr<Volume> volume = _volumes[0];
    VletVector vlet_list;
    volume->get_vlet_list(&vlet_list);
    NodeVector node_list;
    for (auto& vlet : vlet_list) {
        if (is_vlet_state_not_exist(vlet->state())) {
            auto node = vlet->tmp_node();
            if (node) {
                node_list.push_back(node);
            }
            continue;
        }
        auto node = vlet->node();
        if (node) {
            node_list.push_back(node);
        }
    }
    NodePickFilter pick_filter({}, node_list);
    // Please refer to MasterTestBase.cpp.
    // By default, the vlet could be placed on one node.
    ASSERT_TRUE(pick_filter._filter._element_map.size() <= volume->space()->n());
}

/*
 * A general case test for NodePickFilter::check()
 */
TEST_F(DiskPickerTest, test_node_check_expect_true) {
    std::shared_ptr<Volume> volume = _volumes[0];
    VletVector vlet_list;
    volume->get_vlet_list(&vlet_list);
    NodeVector node_list;
    for (auto& vlet : vlet_list) {
        if (is_vlet_state_not_exist(vlet->state())) {
            auto node = vlet->tmp_node();
            if (node) {
                node_list.push_back(node);
            }
            continue;
        }
        auto node = vlet->node();
        if (node) {
            node_list.push_back(node);
        }
    }
    NodePickFilter pick_filter({}, node_list);
    std::shared_ptr<Node> node(_utils.generate_node("127.0.0.1:1234", _utils.az_one_mock,
            _utils.group_one_mock, _utils.idc_mock, _utils.rack_mock, NODE_STATE_NORMAL));
    // Please refer to MasterTestBase.cpp.
    // By default, the vlet could be placed on one node.
    ASSERT_TRUE(pick_filter._filter._element_map.size() <= volume->space()->n());
    ASSERT_TRUE(pick_filter.check(common::endpoint2int(node->addr())));
}

/*
 * A general case test for NodePickFilter::check()
 */
TEST_F(DiskPickerTest, test_node_check_expect_false) {
    std::shared_ptr<Volume> volume = _volumes[0];
    VletVector vlet_list;
    volume->get_vlet_list(&vlet_list);
    NodeVector node_list;
    for (auto& vlet : vlet_list) {
        if (is_vlet_state_not_exist(vlet->state())) {
            auto node = vlet->tmp_node();
            if (node) {
                node_list.push_back(node);
            }
            continue;
        }
        auto node = vlet->node();
        if (node) {
            node_list.push_back(node);
        }
    }
    NodePickFilter pick_filter({}, node_list);
    // Please refer to MasterTestBase.cpp.
    // By default, the vlet could be placed on one node.
    ASSERT_TRUE(pick_filter._filter._element_map.size() <= volume->space()->n());
    auto node_addr = common::endpoint2int(vlet_list[0]->disk()->node()->addr());
    ASSERT_FALSE(pick_filter.check(node_addr));
}

/******************* DiskPickerNode *******************/

/*
 * A general case test for DiskPickerNode::add_disk()
 */
TEST_F(DiskPickerTest, test_disk_picker_node_add_disk_happy_case) {
    DiskPickerNode picker(_utils.node_mock);
    std::shared_ptr<Node> node = _nodes[0];
    auto disk = node->get_disk(0);
    picker.add_disk(node, disk);
    ASSERT_EQ(_utils.node_mock, picker._node_addr);
    auto disk_addr = common::add_diskid2int(common::endpoint2int(node->addr()), disk->disk_id());
    ASSERT_EQ(disk_addr, picker._disk_list[0]);
}

/*
 * A general case test for DiskPickerRack::pick_disk_list()
 */
TEST_F(DiskPickerTest, test_disk_picker_node_pick_disk_list_happy_case) {
    DiskPickerNode picker(_utils.node_mock);
    std::shared_ptr<Node> node = _nodes[0];
    auto disk = node->get_disk(0);
    picker.add_disk(node, disk);
    ASSERT_EQ(_utils.node_mock, picker._node_addr);
    auto disk_addr = common::add_diskid2int(common::endpoint2int(node->addr()), disk->disk_id());
    ASSERT_EQ(disk_addr, picker._disk_list[0]);

    DiskUint64Vector disk_vector;
    picker.pick_disk_list(&disk_vector);
    ASSERT_EQ(1, disk_vector.size());
}

/******************* DiskPickerRack *******************/

/*
 * A general case test for DiskPickerRack::add_disk()
 */
TEST_F(DiskPickerTest, test_disk_picker_rack_add_disk_with_brand_new_node) {
    DiskPickerRack picker(_utils.rack_mock);
    std::shared_ptr<Node> node = _nodes[0];
    auto disk = node->get_disk(0);
    picker.add_disk(node, disk);
    ASSERT_EQ(_utils.rack_mock, picker._rack_name);
    ASSERT_EQ(1, picker._node_map.size());
}

/*
 * A general case test for DiskPickerRack::add_disk()
 */
TEST_F(DiskPickerTest, test_disk_picker_rack_add_disk_with_brand_exist_node) {
    DiskPickerRack picker(_utils.rack_mock);
    std::shared_ptr<Node> node = _nodes[0];
    auto disk = node->get_disk(0);
    picker.add_disk(node, disk);
    ASSERT_EQ(_utils.rack_mock, picker._rack_name);
    ASSERT_EQ(1, picker._node_map.size());

    auto disk2 = _nodes[0]->get_disk(1);
    picker.add_disk(_nodes[0], disk2);
    ASSERT_EQ(1, picker._node_map.size());
    ASSERT_EQ(2, picker._node_map.find(common::endpoint2int(_nodes[0]->addr()))->second->_disk_list.size());
}

/*
 * A general case test for DiskPickerRack::pick_disk_list()
 */
TEST_F(DiskPickerTest, test_disk_picker_rack_pick_disk_list_happy_case) {
    DiskPickerRack picker(_utils.rack_mock);
    std::shared_ptr<Node> node = _nodes[0];
    auto disk = node->get_disk(0);
    picker.add_disk(node, disk);
    ASSERT_EQ(1, picker._node_map.size());

    VletVector vlet_list;
    NodePickFilter filter({}, {});
    DiskUint64Vector disk_vector;
    picker.pick_disk_list(&disk_vector, filter);
    ASSERT_EQ(1, disk_vector.size());
}

/******************* DiskPickerIDC *******************/

/*
 * A general case test for DiskPickerIDC::add_disk()
 */
TEST_F(DiskPickerTest, test_disk_picker_idc_add_disk_with_brand_new_rack) {
    DiskPickerIDC picker(idc_default);
    std::shared_ptr<Node> node = _nodes[0];
    auto disk = node->get_disk(0);
    picker.add_disk(node, disk);
    ASSERT_EQ(1, picker._rack_map.size());
}

/*
 * A general case test for DiskPickerIDC::add_disk()
 */
TEST_F(DiskPickerTest, test_disk_picker_idc_add_disk_with_existing_rack) {
    DiskPickerIDC picker(idc_default);
    auto disk = _nodes[0]->get_disk(0);
    picker.add_disk(_nodes[0], disk);
    ASSERT_EQ(1, picker._rack_map.size());

    auto disk2 = _nodes[1]->get_disk(0);
    picker.add_disk(_nodes[1], disk2);
    ASSERT_EQ(1, picker._rack_map.size());
}

/*
 * A general case test for DiskPickerIDC::pick_disk_list()
 */
TEST_F(DiskPickerTest, test_disk_picker_idc_pick_disk_list_happy_case) {
    DiskPickerIDC picker(idc_default);
    std::shared_ptr<Node> node = _nodes[0];
    ASSERT_EQ(idc_default, picker._idc_name);
    auto disk = node->get_disk(0);
    std::shared_ptr<Volume> volume = _volumes[0];
    picker.add_disk(node, disk);
    ASSERT_EQ(idc_default, picker._idc_name);
    ASSERT_EQ(1, picker._rack_map.size());

    VletVector vlet_list;
    volume->get_vlet_list(&vlet_list);

    NodeVector node_list;
    for (auto& vlet : vlet_list) {
        if (is_vlet_state_not_exist(vlet->state())) {
            auto node = vlet->tmp_node();
            if (node) {
                node_list.push_back(node);
            }
            continue;
        }
        auto node = vlet->node();
        if (node) {
            node_list.push_back(node);
        }
    }
    std::set<std::string> rack_set;
    rack_set.insert("test_rack_0");
    rack_set.insert("test_rack_1");
    rack_set.insert("test_rack_2");
    RackPickFilter rack_filter(rack_set, node_list, 100);
    NodePickFilter node_filter({}, {});
    DiskUint64Vector disk_vector;
    picker.pick_disk_list(&disk_vector, node_filter, rack_filter);
    ASSERT_EQ(1, disk_vector.size());
}

/******************* DiskPickerGroup *******************/

/*
 * A general case test for DiskPickerGroup::add_disk()
 */
TEST_F(DiskPickerTest, test_disk_picker_group_add_disk_with_brand_new_rack) {
    DiskPickerGroup picker(group_default);
    std::shared_ptr<Node> node = _nodes[0];
    auto disk = node->get_disk(0);
    picker.add_disk(node, disk);
    ASSERT_EQ(1, picker._az_map.size());
}

/*
 * A general case test for DiskPickerGroup::add_disk()
 */
TEST_F(DiskPickerTest, test_disk_picker_group_add_disk_with_existing_rack) {
    DiskPickerGroup picker(group_default);
    auto disk = _nodes[0]->get_disk(0);
    picker.add_disk(_nodes[0], disk);
    ASSERT_EQ(1, picker._az_map.size());

    auto disk2 = _nodes[1]->get_disk(0);
    picker.add_disk(_nodes[1], disk2);
    ASSERT_EQ(1, picker._az_map.size());
}

/*
 * A general case test for DiskPickerRack::pick_disk_list()
 */
TEST_F(DiskPickerTest, test_disk_picker_group_pick_disk_list_happy_case) {
    DiskPickerGroup picker(group_default);
    std::shared_ptr<Node> node = _nodes[0];
    auto disk = node->get_disk(0);
    std::shared_ptr<Volume> volume = _volumes[0];
    picker.add_disk(node, disk);
    ASSERT_EQ(group_default, picker._group_name);
    ASSERT_EQ(1, picker._az_map.size());

    std::set<std::string> az_set;
    az_set.insert(node->az_name());

    VletVector vlet_list;
    volume->get_vlet_list(&vlet_list);

    NodeVector node_list;
    for (auto& vlet : vlet_list) {
        if (is_vlet_state_not_exist(vlet->state())) {
            auto node = vlet->tmp_node();
            if (node) {
                node_list.push_back(node);
            }
            continue;
        }
        auto node = vlet->node();
        if (node) {
            node_list.push_back(node);
        }
    }
    std::set<std::string> idc_set;
    idc_set.insert("idc_default");
    std::set<std::string> rack_set;
    rack_set.insert("test_rack_0");
    rack_set.insert("test_rack_1");
    rack_set.insert("test_rack_2");

    std::unordered_map<std::string, int> az_max_vlet_policy;
    AZPickFilter az_filter(az_set, node_list, 100, az_max_vlet_policy);
    RackPickFilter rack_filter(rack_set, node_list, 100);
    IDCPickFilter idc_filter(idc_set, node_list, 100);
    NodePickFilter node_filter({}, {});
    DiskUint64Vector disk_vector;
    picker.pick_disk_list(&disk_vector, node_filter, rack_filter, idc_filter, az_filter);
    ASSERT_EQ(1, disk_vector.size());

    az_max_vlet_policy.clear();
    disk_vector.clear();
    az_max_vlet_policy[node->az_name()] = 18;
    AZPickFilter az_filter_custom_succ(az_set, node_list, 100, az_max_vlet_policy);
    picker.pick_disk_list(&disk_vector, node_filter, rack_filter, idc_filter, az_filter_custom_succ);
    ASSERT_EQ(1, disk_vector.size());

    az_max_vlet_policy.clear();
    disk_vector.clear();
    az_max_vlet_policy[node->az_name()] = 5;
    AZPickFilter az_filter_custom_failed(az_set, node_list, 100, az_max_vlet_policy);
    picker.pick_disk_list(&disk_vector, node_filter, rack_filter, idc_filter, az_filter_custom_failed);
    ASSERT_EQ(0, disk_vector.size());
}

/******************* DiskPickerAZ *******************/

/*
 * A general case test for DiskPickerAZ::add_disk()
 */
TEST_F(DiskPickerTest, test_disk_picker_cluster_add_disk_with_brand_new_rack) {
    DiskPickerCluster picker;
    std::shared_ptr<Node> node = _nodes[0];
    auto disk = node->get_disk(0);
    picker.add_disk(node, disk);
    ASSERT_EQ(1, picker._disk_type_map.size());
    ASSERT_EQ(1, picker._disk_num);
}

/*
 * A general case test for DiskPickerAZ::add_disk()
 */
TEST_F(DiskPickerTest, test_disk_picker_cluster_add_disk_with_existing_rack) {
    DiskPickerCluster picker;
    auto disk = _nodes[0]->get_disk(0);
    picker.add_disk(_nodes[0], disk);
    ASSERT_EQ(1, picker._disk_type_map.size());

    auto disk1 = _nodes[1]->get_disk(0);
    picker.add_disk(_nodes[1], disk);
    ASSERT_EQ(1, picker._disk_type_map.size());
    ASSERT_EQ(2, picker._disk_num);
}

/*
 * A general case test for DiskPickerAZ::pick_disk_list()
 */
TEST_F(DiskPickerTest, test_disk_picker_cluster_pick_disk_list_happy_case) {
    DiskPickerCluster picker;
    std::shared_ptr<Node> node = _nodes[0];
    auto disk = node->get_disk(0);
    std::shared_ptr<Volume> volume = _volumes[0];
    picker.add_disk(node, disk);
    ASSERT_EQ(1, picker._disk_type_map.size());
    ASSERT_EQ(1, picker._disk_num);

    std::set<std::string> az_set;
    az_set.insert(node->az_name());

    VletVector vlet_list;
    volume->get_vlet_list(&vlet_list);

    NodeVector node_list;
    for (auto& vlet : vlet_list) {
        if (is_vlet_state_not_exist(vlet->state())) {
            auto node = vlet->tmp_node();
            if (node) {
                node_list.push_back(node);
            }
            continue;
        }
        auto node = vlet->node();
        if (node) {
            node_list.push_back(node);
        }
    }
    std::set<std::string> idc_set;
    idc_set.insert("idc_default");
    std::set<std::string> rack_set;
    rack_set.insert("test_rack_0");
    rack_set.insert("test_rack_1");
    rack_set.insert("test_rack_2");
    RackPickFilter rack_filter(rack_set, node_list, 100);
    IDCPickFilter idc_filter(idc_set, node_list, 100);
    GroupPickFilter group_filter({node->group_name()});
    DiskTypePickFilter disk_type_filter({disk->disk_type()});
    EngineTypePickFilter engine_type_filter({});
    std::unordered_map<std::string, int> az_max_vlet_policy;
    AZPickFilter az_filter(az_set, node_list, 100, az_max_vlet_policy);
    NodePickFilter node_filter({}, {});

    DiskUint64Vector disk_vector;
    picker.pick_disk_list(&disk_vector, node_filter, rack_filter, idc_filter, az_filter,
            group_filter, engine_type_filter, disk_type_filter);
    ASSERT_EQ(1, disk_vector.size());
}

/******************* DiskPicker *******************/

/*
 * A general case test for DiskPicker::pick_disk_for_create_vlet()
 */
TEST_F(DiskPickerTest, test_pick_disk_for_create_vlet_happy_case) {
    FLAGS_pick_disk_type_policy = SPACE_RULE_TYPE;
    DiskPicker picker;
    std::shared_ptr<Volume> volume = _volumes[0];
    VletVector vlet_list;
    volume->get_vlet_list(&vlet_list);
    auto vlet = vlet_list[0];
    // let's make the max_vlet_per_rack to 2 for this test.
    volume->space()->_options.max_vlet_per_rack = 100;

    // Let's add nodes to az manager
    NodeVector node_vector;
    for (std::shared_ptr<Node> node : _nodes) {
        node->set_alive(true);
        node_vector.push_back(node);
    }
    picker.refresh_suitable_disk_list(node_vector);

    std::shared_ptr<Disk> disk = picker.pick_disk_for_vlet(vlet,
            MASTER_VLET_TASK_TYPE_CREATE);
    ASSERT_TRUE(disk != nullptr);

    FLAGS_pick_disk_type_policy = SAME_DISK_TYPE;
    std::shared_ptr<Disk> disk1 = picker.pick_disk_for_vlet(vlet,
            MASTER_VLET_TASK_TYPE_CREATE);
    ASSERT_TRUE(disk1 != nullptr);

    FLAGS_pick_disk_type_policy = SPACE_DISK_TYPE;
    std::shared_ptr<Disk> disk2 = picker.pick_disk_for_vlet(vlet,
            MASTER_VLET_TASK_TYPE_CREATE);
    ASSERT_TRUE(disk2 != nullptr);
    FLAGS_pick_disk_type_policy = SPACE_RULE_TYPE;
}

/*
 * A general case test for DiskPicker::pick_disk_for_create_vlet_with_disk_type_place_policy()
 */
TEST_F(DiskPickerTest, test_pick_disk_for_create_vlet_with_disk_type_place_policy) {
    FLAGS_pick_disk_type_policy = SPACE_RULE_TYPE;
    DiskPicker picker;
    std::shared_ptr<Volume> volume = _volumes[0];
    VletVector vlet_list;
    volume->get_vlet_list(&vlet_list);
    auto vlet = vlet_list[0];
    // let's make the max_vlet_per_rack to 2 for this test.
    volume->space()->_options.max_vlet_per_rack = 100;
    std::vector<std::string> disk_type_vec = {"SSD", "HDD", "CMR"};
    for (int i = 0; i < N; ++i) {
        volume->space()->_options.disk_type_place_policy_map[i] = disk_type_vec[i%disk_type_vec.size()];
        volume->space()->_options.az_place_policy_map[i] = "test_az_" + std::to_string((i+2)%3);
    }

    // Let's add nodes to az manager
    NodeVector node_vector;
    for (std::shared_ptr<Node> node : _nodes) {
        node->set_alive(true);
        node_vector.push_back(node);
    }
    picker.refresh_suitable_disk_list(node_vector);

    std::shared_ptr<Disk> disk = picker.pick_disk_for_vlet(vlet,
            MASTER_VLET_TASK_TYPE_CREATE);
    ASSERT_TRUE(disk != nullptr);
    ASSERT_TRUE(disk->disk_type() == "SSD");
    ASSERT_TRUE(disk->node()->az_name() == "test_az_2");

    volume->space()->_options.disk_type_place_policy_map.clear();
    volume->space()->_options.az_place_policy_map.clear();

    volume->space()->_options.disk_type_place_policy_map[0] = "SSD";
    volume->space()->_options.az_place_policy_map[0] = "test_az_8";

    disk = picker.pick_disk_for_vlet(vlet,
            MASTER_VLET_TASK_TYPE_CREATE);
    ASSERT_TRUE(disk != nullptr);
    ASSERT_TRUE(disk->disk_type() == "SSD");

    volume->space()->_options.disk_type_place_policy_map.clear();
    volume->space()->_options.az_place_policy_map.clear();

    volume->space()->_options.disk_type_place_policy_map[0] = "SSD";

    disk = picker.pick_disk_for_vlet(vlet,
            MASTER_VLET_TASK_TYPE_CREATE);
    ASSERT_TRUE(disk != nullptr);

    volume->space()->_options.disk_type_place_policy_map.clear();
    volume->space()->_options.az_place_policy_map.clear();

    volume->space()->_options.disk_type_place_policy_map[1] = "SSD";

    disk = picker.pick_disk_for_vlet(vlet,
            MASTER_VLET_TASK_TYPE_CREATE);
    ASSERT_TRUE(disk != nullptr);

    volume->space()->_options.disk_type_place_policy_map.clear();
    volume->space()->_options.az_place_policy_map.clear();

    volume->space()->_options.disk_type_place_policy_map[0] = "SSD";
    volume->space()->_options.az_place_policy_map[1] = "test_az_0";

    disk = picker.pick_disk_for_vlet(vlet,
            MASTER_VLET_TASK_TYPE_CREATE);
    ASSERT_TRUE(disk != nullptr);

    volume->space()->_options.disk_type_place_policy_map.clear();
    volume->space()->_options.az_place_policy_map.clear();
}

/*
 * A general case test for DiskPicker::pick_node_for_recover_vlet()
 */
TEST_F(DiskPickerTest, test_pick_node_for_recover_vlet_happy_case) {
    FLAGS_pick_disk_type_policy = SPACE_RULE_TYPE;
    DiskPicker picker;
    std::shared_ptr<Volume> volume = _volumes[0];
    auto vlet = volume->get_vlet(0);
    // let's make the max_vlet_per_rack to 2 for this test.
    volume->space()->_options.max_vlet_per_rack = 100;

    NodeVector node_vector;
    for (std::shared_ptr<Node> node : _nodes) {
        node->set_alive(true);
        node_vector.push_back(node);
    }
    picker.refresh_suitable_disk_list(node_vector);

    std::shared_ptr<Disk> disk = picker.pick_disk_for_vlet(vlet, 
            MASTER_VLET_TASK_TYPE_RECOVER);
    ASSERT_TRUE(disk != nullptr);
}

/*
 * A general case test for DiskPicker::pick_disk_for_recover_vlet()
 */
TEST_F(DiskPickerTest, test_pick_disk_for_recover_vlet_happy_case) {
    FLAGS_pick_disk_type_policy = SPACE_RULE_TYPE;
    DiskPicker picker;
    std::shared_ptr<Volume> volume = _volumes[0];
    auto vlet = volume->get_vlet(0);
    // let's make the max_vlet_per_rack to 2 for this test.
    volume->space()->_options.max_vlet_per_rack = 100;

    NodeVector node_vector;
    for (std::shared_ptr<Node> node : _nodes) {
        node->set_alive(true);
        node_vector.push_back(node);
    }
    picker.refresh_suitable_disk_list(node_vector);

    std::shared_ptr<Disk> disk = picker.pick_disk_for_vlet(vlet, MASTER_VLET_TASK_TYPE_RECOVER);
    ASSERT_TRUE(disk != nullptr);
}

/*
 * A general case test for DiskPicker::pick_disk_for_compact_balance_vlet()
 */
TEST_F(DiskPickerTest, test_pick_disk_for_compact_balance_vlet_happy_case) {
    FLAGS_pick_disk_type_policy = SPACE_RULE_TYPE;
    DiskPicker picker;
    std::shared_ptr<Volume> volume = _volumes[0];
    auto vlet = volume->get_vlet(0);
    // let's make the max_vlet_per_rack to 2 for this test.
    volume->space()->_options.max_vlet_per_rack = 100;

    NodeVector node_vector;
    for (std::shared_ptr<Node> node : _nodes) {
        if (node->rack_name() == vlet->node()->rack_name()) {
            node->set_alive(true);
            node_vector.push_back(node);
        }
    }
    picker.refresh_suitable_disk_list(node_vector);

    std::shared_ptr<Disk> disk = picker.pick_disk_for_vlet(vlet, MASTER_VLET_TASK_TYPE_COMPACT_BALANCE);
    ASSERT_TRUE(disk != nullptr);
    ASSERT_EQ(disk->node()->rack_name(), vlet->rack_name());
}

/*
 * A general case test for DiskPicker::pick_disk_list_by_filter()
 */
TEST_F(DiskPickerTest, test_pick_disk_list_by_filter_happy_case) {
    DiskPicker picker;
    std::shared_ptr<Volume> volume = _volumes[0];
    auto space = volume->space();
    VletVector vlet_list;
    volume->get_vlet_list(&vlet_list);
    auto vlet = vlet_list[0];
    auto node = vlet->node();
    NodeVector node_vector;
    for (std::shared_ptr<Node> node : _nodes) {
        node->set_alive(true);
        node_vector.push_back(node);
    }
    picker.refresh_suitable_disk_list(node_vector);
    {
        std::set<std::string> az_set;
        std::set<std::string> group_set;
        std::set<std::string> disk_type_set;
        std::unordered_set<uint64_t> node_set;
        DiskUint64Vector disk_list;

        ASSERT_FALSE(picker.pick_disk_list_by_filter(az_set, group_set, disk_type_set, node_set, true, &disk_list));
        ASSERT_FALSE(picker.pick_disk_list_by_filter(az_set, group_set, disk_type_set, node_set, false, &disk_list));
    }
    {

        std::set<std::string> az_set(std::begin(space->_options.az_list), std::end(space->_options.az_list));
        std::set<std::string> group_set{space->group_set()};
        std::unordered_set<uint64_t> node_set{};
        std::set<std::string> disk_type_set = space->disk_type_set();
        DiskUint64Vector disk_list;
        ASSERT_TRUE(picker.pick_disk_list_by_filter(az_set, group_set, disk_type_set, node_set, true, &disk_list));
        ASSERT_EQ(disk_list.size(), az_count_default * node_count_per_az * disk_count_per_node);
        disk_list.clear();
        ASSERT_TRUE(picker.pick_disk_list_by_filter(az_set, group_set, disk_type_set, node_set, false, &disk_list));
        ASSERT_EQ(disk_list.size(), az_count_default * node_count_per_az * disk_count_per_node);
    }
    {
        std::set<std::string> az_set{node->az_name()};
        std::set<std::string> group_set{node->group_name()};
        std::unordered_set<uint64_t> node_set{};
        std::set<std::string> disk_type_set = space->disk_type_set();
        DiskUint64Vector disk_list;

        ASSERT_TRUE(picker.pick_disk_list_by_filter(az_set, group_set, disk_type_set, node_set, true, &disk_list));
        ASSERT_GE(disk_list.size(), 3 * disk_count_per_node);
        disk_list.clear();
        ASSERT_TRUE(picker.pick_disk_list_by_filter(az_set, group_set, disk_type_set, node_set, false, &disk_list));
        ASSERT_GE(disk_list.size(), 3 * disk_count_per_node);
    }
    {
        std::set<std::string> az_set{node->az_name()};
        std::set<std::string> group_set{node->group_name()};
        std::unordered_set<uint64_t> node_set{common::endpoint2int(node->addr())};
        std::set<std::string> disk_type_set = space->disk_type_set();
        DiskUint64Vector disk_list;

        ASSERT_TRUE(picker.pick_disk_list_by_filter(az_set, group_set, disk_type_set, node_set, true, &disk_list));
        ASSERT_EQ(disk_list.size(), 1 * disk_count_per_node);
        disk_list.clear();
        ASSERT_TRUE(picker.pick_disk_list_by_filter(az_set, group_set, disk_type_set, node_set, false, &disk_list));
        ASSERT_EQ(disk_list.size(), 1 * disk_count_per_node);
    }
}

/*
 * A general case test for DiskPicker::pick_dest_disk_for_balance()
 */
TEST_F(DiskPickerTest, test_pick_dest_disk_for_balance_vlet_happy_case) {
    FLAGS_pick_disk_type_policy = SPACE_RULE_TYPE;
    DiskPicker picker;
    std::shared_ptr<Volume> volume = _volumes[0];
    auto vlet = volume->get_vlet(0);
    VletVector vlets;
    volume->get_vlet_list(&vlets);
    int max_vlet_per_az = 0;
    for (auto tmp_vlet : vlets) {
        if (vlet->az_name() == tmp_vlet->az_name()) {
            max_vlet_per_az++;
        }
    }
    // let's make the max_vlet_per_rack to 2 for this test.
    volume->space()->_options.max_vlet_per_rack = 100;
    volume->space()->_options.max_vlet_per_az = max_vlet_per_az;
    volume->space()->_options.pick_az_policy = PAZP_MAX_PER_AZ;

    NodeVector node_vector;
    DiskUint64Vector disk_list;

    for (std::shared_ptr<Node> node : _nodes) {
        node->set_alive(true);
        node_vector.push_back(node);
    }
    LOG(TRACE) << " node num:" << node_vector.size();
    picker.refresh_suitable_disk_list(node_vector);

    ASSERT_TRUE(picker.pick_dest_disk_list_for_balance(vlet, USAGE_OVERFLOW, &disk_list));
    ASSERT_TRUE(disk_list.size() != 0);

    VletVector vlet_list;
    volume->get_vlet_list(&vlet_list);
    node_vector.clear();
    disk_list.clear();
    for (auto& tmp_vlet : vlet_list) {
        if (tmp_vlet->shard_index() == vlet->shard_index()) {
            continue;
        }
        auto node = tmp_vlet->node();
        node->set_alive(true);
        node_vector.push_back(node);
        LOG(TRACE) << "disk az name:" << node->az_name();
    }
    LOG(TRACE) << "vlet az name:" << vlet->az_name();

    picker.refresh_suitable_disk_list(node_vector);

    ASSERT_FALSE(picker.pick_dest_disk_list_for_balance(vlet, USAGE_OVERFLOW, &disk_list));
    ASSERT_TRUE(disk_list.size() == 0);

    for (std::shared_ptr<Node> node : _nodes) {
        if (node->rack_name() == vlet->node()->rack_name()) {
            node->set_alive(true);
            node_vector.push_back(node);
        }
    }
    picker.refresh_suitable_disk_list(node_vector);

    ASSERT_TRUE(picker.pick_dest_disk_list_for_balance(vlet, USAGE_OVERFLOW, &disk_list));
    ASSERT_TRUE(disk_list.size() != 0);
    
    node_vector.clear();
    disk_list.clear();
    for (std::shared_ptr<Node> node : _nodes) {
        if (node->group_name() == vlet->group_name()) {
            node->set_alive(true);
            node_vector.push_back(node);
        }
    }
    picker.refresh_suitable_disk_list(node_vector);

    ASSERT_TRUE(picker.pick_dest_disk_list_for_balance(vlet, USAGE_OVERFLOW, &disk_list));
    ASSERT_TRUE(disk_list.size() != 0);

    node_vector.clear();
    disk_list.clear();
    for (std::shared_ptr<Node> node : _nodes) {
        if (node->group_name() != vlet->group_name()) {
            node->set_alive(true);
            node_vector.push_back(node);
        }
    }
    picker.refresh_suitable_disk_list(node_vector);

    ASSERT_FALSE(picker.pick_dest_disk_list_for_balance(vlet, USAGE_OVERFLOW, &disk_list));
    ASSERT_TRUE(disk_list.size() == 0);
    volume->space()->_options.pick_az_policy = PAZP_ROUND_BY_AZ;

    FLAGS_pick_disk_type_policy = SAME_DISK_TYPE;
    ASSERT_FALSE(picker.pick_dest_disk_list_for_balance(vlet, USAGE_OVERFLOW, &disk_list));
    ASSERT_TRUE(disk_list.size() == 0);

    FLAGS_pick_disk_type_policy = SPACE_DISK_TYPE;
    ASSERT_FALSE(picker.pick_dest_disk_list_for_balance(vlet, USAGE_OVERFLOW, &disk_list));
    ASSERT_TRUE(disk_list.size() == 0);
    FLAGS_pick_disk_type_policy = SPACE_RULE_TYPE;
}

/*
 * A general case test for DiskPicker::min_usage_disk_of_node()
TEST_F(DiskPickerTest, test_min_usage_disk_of_node_happy_case) {
    DiskPicker picker(az_prefix + std::to_string(0));
    std::shared_ptr<Node> node = _nodes[0];
    DiskVector disk_vector;
    node->get_disk_list(&disk_vector);

    std::shared_ptr<DiskSize> min_disk_size = _utils.generate_disk_size(-1,
            disk_free_size + 2 * TB, disk_total_size, aries_capacity, disk_used_size);
    disk_vector[0]->update_disk_size(*min_disk_size.get());

    ASSERT_EQ(disk_vector[0], picker.min_usage_disk_of_node(node));
}
 */

/*
 * A general case test for DiskPicker::pick_az_disk_for_vlet()
 */
TEST_F(DiskPickerTest, test_pick_az_disk_for_vlet_happy_case) {
    DiskPicker picker;
    std::shared_ptr<Volume> volume = _volumes[0];
    auto vlet = volume->get_vlet(0);
    VletVector vlets;
    volume->get_vlet_list(&vlets);
    int max_vlet_per_az = 0;
    for (auto tmp_vlet : vlets) {
        if (vlet->az_name() == tmp_vlet->az_name()) {
            max_vlet_per_az++;
        }
    }

    volume->space()->_options.max_vlet_per_rack = 100;
    volume->space()->_options.max_vlet_per_az = max_vlet_per_az;

    NodeVector node_vector;
    DiskUint64Vector disk_list;

    // succ case
    for (std::shared_ptr<Node> node : _nodes) {
        node->set_alive(true);
        node_vector.push_back(node);
    }
    LOG(TRACE) << " node num:" << node_vector.size();
    picker.refresh_suitable_disk_list(node_vector);
    auto disk = picker.pick_az_disk_for_vlet(vlet, "test_az_1");
    ASSERT_TRUE(disk != nullptr);

    // fail case due to not exist az
    disk =  picker.pick_az_disk_for_vlet(vlet, "test_az_4");
    ASSERT_TRUE(disk == nullptr);

    // fail case due to node filter limit
    VletVector vlet_list;
    volume->get_vlet_list(&vlet_list);
    node_vector.clear();
    for (auto& tmp_vlet : vlet_list) {
        if (tmp_vlet->shard_index() == vlet->shard_index()) {
            continue;
        }
        auto node = tmp_vlet->node();
        node->set_alive(true);
        node_vector.push_back(node);
    }
    picker.refresh_suitable_disk_list(node_vector);
    disk = picker.pick_az_disk_for_vlet(vlet, "test_az_1");
    ASSERT_TRUE(disk == nullptr);

    // fail case due to az filter limit
    node_vector.clear();
    for (std::shared_ptr<Node> node : _nodes) {
        // same rack means same az in this case
        if (node->rack_name() == vlet->node()->rack_name()) {
            node->set_alive(true);
            node_vector.push_back(node);
        }
    }
    LOG(TRACE) << "vlet rack name:" << vlet->node()->rack_name(); 
    picker.refresh_suitable_disk_list(node_vector);
    disk = picker.pick_az_disk_for_vlet(vlet, "test_az_1");
    ASSERT_TRUE(disk == nullptr);
    
    // succ case: choose space disk type set
    node_vector.clear();
    auto tmp_disk = vlet->disk();
    LOG(TRACE) << "old vlet disk type:" << vlet->disk_type_name();
    LOG(TRACE) << "old vlet engine type:" << vlet->disk()->engine_type();    
    tmp_disk->set_disk_type("SMR");
    for (std::shared_ptr<Node> node : _nodes) {
        node->set_alive(true);
        node_vector.push_back(node);
    }
    picker.refresh_suitable_disk_list(node_vector);
    disk = picker.pick_az_disk_for_vlet(vlet, "test_az_1");
    LOG(TRACE) << " vlet disk type:" << vlet->disk_type_name(); 
    ASSERT_TRUE(disk != nullptr);
    tmp_disk->set_disk_type("HDD"); 

    // succ case: permit pick different group in space 
    node_vector.clear();
    for (std::shared_ptr<Node> node : _nodes) {
        if (node->group_name() == vlet->group_name()) {
            node->set_alive(true);
            node_vector.push_back(node);
        }
    }
    picker.refresh_suitable_disk_list(node_vector);

    disk = picker.pick_az_disk_for_vlet(vlet, "test_az_1");
    ASSERT_TRUE(disk != nullptr);

    node_vector.clear();
    for (std::shared_ptr<Node> node : _nodes) {
        if (node->group_name() != vlet->group_name()) {
            node->set_alive(true);
            node_vector.push_back(node);
        }
    }
    picker.refresh_suitable_disk_list(node_vector);
    disk = picker.pick_az_disk_for_vlet(vlet, "test_az_1");
    ASSERT_TRUE(disk != nullptr);

    // succ case: permit different engine type
    node_vector.clear();
    tmp_disk->set_engine_type(ENGINE_APPEND);
    for (std::shared_ptr<Node> node : _nodes) {
        node->set_alive(true);
        node_vector.push_back(node);
    }
    picker.refresh_suitable_disk_list(node_vector);
    LOG(TRACE) << "cur vlet disk type:" << vlet->disk_type_name();
    LOG(TRACE) << "cur vlet type:" << vlet->vlet_type();
    LOG(TRACE) << "cur vlet engine type:" << vlet->disk()->engine_type(); 
    disk = picker.pick_az_disk_for_vlet(vlet, "test_az_1");
    ASSERT_TRUE(disk != nullptr);
    tmp_disk->set_engine_type(ENGINE_AUTO);

    // succ case
    LOG(TRACE) << "cur vlet disk type:" << vlet->disk_type_name();
    LOG(TRACE) << "cur vlet engine type:" << vlet->disk()->engine_type(); 
    disk = picker.pick_az_disk_for_vlet(vlet, "test_az_1");
    ASSERT_TRUE(disk != nullptr);
}

/*
 * A general case test for DiskPicker::refresh_suitable_node_list()
 */
TEST_F(DiskPickerTest, test_refresh_suitable_node_list_with_abnormal_node_state) {
    DiskPicker picker;

    std::shared_ptr<Node> node(_utils.generate_node(_utils.ep_mock_one, _utils.az_one_mock,
            _utils.group_one_mock, _utils.idc_mock, _utils.rack_mock, NODE_STATE_DROPED));

    NodeVector node_vector;
    node_vector.push_back(node);
    node->set_alive(true);

    picker.refresh_suitable_disk_list(node_vector);
    ASSERT_EQ(0, picker._create_cluster->disk_num());
}

/*
 * A general case test for DiskPicker::refresh_suitable_node_list()
 */
TEST_F(DiskPickerTest, test_refresh_suitable_node_list_with_invalid_free_usage) {
    DiskPicker picker;
    FLAGS_disk_reserve_percent = 1.0;

    std::shared_ptr<Node> node(_utils.generate_node(_utils.ep_mock_one, _utils.az_one_mock,
            _utils.group_one_mock, _utils.idc_mock, _utils.rack_mock, NODE_STATE_NORMAL));
    std::shared_ptr<Disk> disk = _disks[0];
    // let's mark free size to zero and used size as same as total size
    std::shared_ptr<DiskStat> disk_stat = _utils.generate_disk_stat(-1,
            disk_total_size, 0, aries_capacity, disk_total_size);
    disk->update_disk_stat(*disk_stat.get());
    node->add_disk(disk);

    NodeVector node_vector;
    node_vector.push_back(node);
    node->set_alive(true);

    picker.refresh_suitable_disk_list(node_vector);
    ASSERT_EQ(0, picker._create_cluster->disk_num());
}

/*
 * A general case test for DiskPicker::refresh_suitable_node_list()
 */
TEST_F(DiskPickerTest, test_refresh_suitable_node_list_with_invalid_min_disk_usage) {
    DiskPicker picker;
    FLAGS_disk_reserve_percent = 1.0;

    std::shared_ptr<Node> node(_utils.generate_node(_utils.ep_mock_one, _utils.az_one_mock,
            _utils.group_one_mock, _utils.idc_mock, _utils.rack_mock, NODE_STATE_NORMAL));
    std::shared_ptr<Disk> disk = _disks[0];
    // let's mark free size to zero and used size as same as total size
    std::shared_ptr<DiskStat> disk_stat = _utils.generate_disk_stat(-1,
            disk_total_size, 0, aries_capacity, disk_total_size);
    disk->update_disk_stat(*disk_stat.get());
    node->add_disk(disk);

    NodeVector node_vector;
    node_vector.push_back(node);
    node->set_alive(true);

    picker.refresh_suitable_disk_list(node_vector);
    ASSERT_EQ(0, picker._create_cluster->disk_num());
}

/*
 * A general case test for DiskPicker::refresh_suitable_node_list()
 */
TEST_F(DiskPickerTest, test_az_group_disk_type_free_size_map) {
    DiskPicker picker;

    std::shared_ptr<Node> node = _nodes[0];
    NodeVector node_vector;
    node_vector.push_back(node);
    node->set_alive(true);

    picker.refresh_suitable_disk_list(node_vector);
    ASSERT_EQ(10, picker._create_cluster->disk_num());
    ASSERT_GE(picker._az_group_disk_type_free_size_map.get("test_group_0"), 0);

    picker._az_group_disk_type_free_size_map.clear();

    std::string az_name = "az0";
    std::string group_name = "group0";
    std::string disk_type = "";

    std::string free_size_name = 
        az_name + "_" + group_name + "_" + disk_type;
    picker._az_group_disk_type_free_size_map.add(free_size_name, 1);
    picker._az_group_disk_type_free_size_map.add(free_size_name, 11);
    std::set<std::string> group_set;
    group_set.insert(group_name);
    std::set<std::string> disk_type_set;
    disk_type_set.insert(disk_type);
    ASSERT_EQ(picker.az_group_disk_type_free_size(az_name, group_set, disk_type_set), 12);
    picker._az_group_disk_type_free_size_map.clear();
    ASSERT_EQ(picker.az_group_disk_type_free_size(az_name, group_set, disk_type_set), 0);
}

/*
 * A general case test for DiskPicker::suitable_disk_num()
 */
TEST_F(DiskPickerTest, test_suitable_disk_num_happy_case) {
    DiskPicker picker;
    std::shared_ptr<Node> node(_utils.generate_node(_utils.ep_mock_one, _utils.az_one_mock,
            _utils.group_one_mock, _utils.idc_mock, _utils.rack_mock, NODE_STATE_NORMAL));
    NodeVector node_vector;
    node_vector.push_back(node);
    node->set_alive(true);

    picker.refresh_suitable_disk_list(node_vector);
//    ASSERT_EQ(picker._az->disk_num(), picker.suitable_disk_num());
}

TEST_F(DiskPickerTest, test_is_suitable_for_create_vlet) {
    SpaceOptions opt;
    opt.n = 4;
    opt.k = 1;
    opt.space_name = "test_table";
    opt.max_vlet_per_rack = 1;
    opt.max_vlet_per_idc = 2;
    opt.max_vlet_per_az = 4;
    opt.group_set.insert("test_group");
    opt.disk_type_set.insert("SSD");
    opt.az_list.push_back("az1");
    opt.vlet_type = VLET_TYPE_LINKED_32G_4M_512K;
    opt.pick_az_policy = PAZP_ROUND_BY_AZ;
    auto space = std::shared_ptr<Space>(new Space(opt));

    VolumeOptions volume_opt;
    volume_opt.space = space;
    volume_opt.volume_id = 1;
    auto volume = std::shared_ptr<Volume>(new Volume(volume_opt));

    VletVector vlet_list;
    for (uint32_t j = 0; j < space->n(); ++j) {
        VletOptions vlet_opt;
        vlet_opt.shard_index = j;
        vlet_opt.volume = volume;
        auto vlet = std::shared_ptr<Vlet>(new Vlet(vlet_opt));
        vlet->set_state(VLET_STATE_NORMAL);
        vlet->set_vlet_type(VLET_TYPE_LINKED_32G_4M_512K);
        volume->add_vlet(vlet);
        vlet_list.push_back(vlet);
    }
    auto vlet = vlet_list[0];

    NodeVector node_list;
    DiskVector disk_list;
    for (size_t j = 0; j < 4; ++j) {
        uint64_t int_addr = j;
        base::EndPoint addr = common::int2endpoint(int_addr);

        char rack_name[100];
        sprintf(rack_name, "rack:%d", (int)j/2);
        char idc_name[100];
        sprintf(idc_name, "idc%d", 0);
        NodeOptions opt;
        opt.addr = addr;
        opt.rack_name = rack_name;
        opt.idc_name = idc_name;
        opt.az_name = "az1";
        opt.group_name = "test_group";
        auto node = std::shared_ptr<Node>(new Node(opt));
        auto disk = std::shared_ptr<Disk>(new Disk(1, 0, node));
        disk->set_disk_type("SSD");
        node->add_disk(disk);
        node_list.push_back(node);
        disk_list.push_back(disk);
        g_meta_data->add_node(node);
    }

    FLAGS_pick_disk_type_policy = SPACE_RULE_TYPE;
    DiskPicker picker;
    std::string err;
    bool ok = picker.is_suitable_for_create_vlet(node_list[0], disk_list[0], vlet_list, vlet, ENGINE_LINKED, false, &err);
    ASSERT_EQ(ok, true);
    vlet_list[0]->set_disk_ptr(disk_list[0]);

    vlet = vlet_list[1];
    ok = picker.is_suitable_for_create_vlet(node_list[1], disk_list[1], vlet_list, vlet, ENGINE_LINKED, false, &err);
    ASSERT_EQ(ok, false);

    ok = picker.is_suitable_for_create_vlet(node_list[2], disk_list[2], vlet_list, vlet, ENGINE_LINKED, false, &err);
    ASSERT_EQ(ok, true);
    vlet_list[1]->set_disk_ptr(disk_list[2]);

    vlet = vlet_list[2];
    ok = picker.is_suitable_for_create_vlet(node_list[3], disk_list[3], vlet_list, vlet, ENGINE_LINKED, false, &err);
    ASSERT_EQ(ok, false);

    vlet = vlet_list[3];
    vlet_list[0]->set_disk_ptr(nullptr);
    // has disk type place policy
    space->_options.disk_type_place_policy_map.clear();
    space->_options.az_place_policy_map.clear();

    space->_options.disk_type_place_policy_map[3] = "SSD";
    space->_options.az_place_policy_map[3] = "az1";
    ok = picker.is_suitable_for_create_vlet(node_list[0], disk_list[0], vlet_list, vlet, ENGINE_LINKED, false, &err);
    ASSERT_EQ(ok, true);

    space->_options.disk_type_place_policy_map.clear();
    space->_options.az_place_policy_map.clear();

    space->_options.disk_type_place_policy_map[3] = "SSD";
    space->_options.az_list.push_back("az2");
    space->_options.az_place_policy_map[3] = "az2";
    ok = picker.is_suitable_for_create_vlet(node_list[0], disk_list[0], vlet_list, vlet, ENGINE_LINKED, false, &err);
    ASSERT_EQ(ok, false);

    space->_options.disk_type_place_policy_map.clear();
    space->_options.az_place_policy_map.clear();

    space->_options.disk_type_place_policy_map[3] = "CMR";
    space->_options.disk_type_set.insert("CMR");
    space->_options.az_place_policy_map[3] = "az1";
    ok = picker.is_suitable_for_create_vlet(node_list[0], disk_list[0], vlet_list, vlet, ENGINE_LINKED, false, &err);
    ASSERT_EQ(ok, false);
}

TEST_F(DiskPickerTest, refresh_suitable_manual_disk_set) {
    DiskPicker picker;
    {
        base::EndPoint addr = common::int2endpoint(1);
        NodeOptions opt;
        opt.addr = addr;
        opt.rack_name = "rack";
        opt.idc_name = "idc";
        opt.az_name = "az1";
        opt.group_name = "";
        auto node = std::shared_ptr<Node>(new Node(opt));
        auto disk = std::shared_ptr<Disk>(new Disk(1, 0, node));
        disk->_calc_used_size = 50 * aries::common::GB;
        node->add_disk(disk);

        auto& node_map = g_node_manager->_node_map;
        std::shared_ptr<Node> tmp_node = nullptr;
        if (node_map.find(addr) != node_map.end()) {
            tmp_node = node_map[addr];
        }
        node_map[addr] = node;

        std::vector<DiskStat> disk_stat_list;
        DiskStat disk_stat(1, 100 * aries::common::GB, 50 * aries::common::GB,
            100 * aries::common::GB, 50 * aries::common::GB, 0, 0, 0, 0, 0);
        disk_stat_list.push_back(disk_stat);
        node->update_disk_stat(disk_stat_list);

        node->set_alive(true);
        node->set_state(NODE_STATE_NORMAL);

        FLAGS_max_disk_usage = "0.99";
        FLAGS_max_disk_usage_for_create = "0.95";
        FLAGS_disk_reserve_percent = 0.1;

        std::unordered_set<uint64_t> disk_set{common::add_diskid2int(common::endpoint2int(node->addr()), 1)};
        picker.refresh_suitable_manual_disk_list(disk_set);
        ASSERT_EQ(picker._dest_manual_balance_cluster->disk_num(), 1);
        if (tmp_node != nullptr) {
            node_map[addr] = tmp_node;
        } else {
            node_map.erase(addr);
        }
    }
    {
        base::EndPoint addr = common::int2endpoint(1);
        NodeOptions opt;
        opt.addr = addr;
        opt.rack_name = "rack";
        opt.idc_name = "idc";
        opt.az_name = "az1";
        opt.group_name = "";
        auto node = std::shared_ptr<Node>(new Node(opt));
        auto disk = std::shared_ptr<Disk>(new Disk(2, 0, node));
        disk->_calc_used_size = 50 * aries::common::GB;
        node->add_disk(disk);

        auto& node_map = g_node_manager->_node_map;
        std::shared_ptr<Node> tmp_node = nullptr;
        if (node_map.find(addr) != node_map.end()) {
            tmp_node = node_map[addr];
        }
        node_map[addr] = node;

        std::vector<DiskStat> disk_stat_list;
        DiskStat disk_stat(2, 100 * aries::common::GB, 50 * aries::common::GB,
            100 * aries::common::GB, 50 * aries::common::GB, 0, 0, 0, 0, 0);
        disk_stat_list.push_back(disk_stat);
        node->update_disk_stat(disk_stat_list);

        node->set_alive(true);
        node->set_state(NODE_STATE_DECOMMISSIONING);

        FLAGS_max_disk_usage = "0.99";
        FLAGS_max_disk_usage_for_create = "0.95";
        FLAGS_disk_reserve_percent = 0.1;

       std::unordered_set<uint64_t> disk_set{common::add_diskid2int(common::endpoint2int(node->addr()), 2)};
        picker.refresh_suitable_manual_disk_list(disk_set);
        ASSERT_EQ(picker._dest_manual_balance_cluster->disk_num(), 0);
        if (tmp_node != nullptr) {
            node_map[addr] = tmp_node;
        } else {
            node_map.erase(addr);
        }
    }
    {
        base::EndPoint addr = common::int2endpoint(1);
        NodeOptions opt;
        opt.addr = addr;
        opt.rack_name = "rack";
        opt.idc_name = "idc";
        opt.az_name = "az1";
        opt.group_name = "";
        auto node = std::shared_ptr<Node>(new Node(opt));
        auto disk = std::shared_ptr<Disk>(new Disk(1, 0, node));
        disk->_calc_used_size = 50 * aries::common::GB;
        node->add_disk(disk);

        auto& node_map = g_node_manager->_node_map;
        std::shared_ptr<Node> tmp_node = nullptr;
        if (node_map.find(addr) != node_map.end()) {
            tmp_node = node_map[addr];
        }
        node_map[addr] = node;

        std::vector<DiskStat> disk_stat_list;
        DiskStat disk_stat(1, 100 * aries::common::GB, 50 * aries::common::GB,
            100 * aries::common::GB, 50 * aries::common::GB, 0, 0, 0, 0, 0);
        disk_stat_list.push_back(disk_stat);
        node->update_disk_stat(disk_stat_list);

        node->set_alive(true);
        node->set_state(NODE_STATE_NORMAL);

        FLAGS_max_disk_usage = "0.1,az1:0.99";
        FLAGS_max_disk_usage_for_create = "0.1,az1:0.95";
        FLAGS_disk_reserve_percent = 0.1;

        std::unordered_set<uint64_t> disk_set{ common::add_diskid2int(common::endpoint2int(node->addr()), 1) };
        picker.refresh_suitable_manual_disk_list(disk_set);
        ASSERT_EQ(picker._dest_manual_balance_cluster->disk_num(), 1);
        if (tmp_node != nullptr) {
            node_map[addr] = tmp_node;
        } else {
            node_map.erase(addr);
        }
    }
}

TEST_F(DiskPickerTest, refresh_suitable_disk_list) {
    DiskPicker picker;
    {
        NodeVector node_list;
        base::EndPoint addr = common::int2endpoint(1);
        NodeOptions opt;
        opt.addr = addr;
        opt.rack_name = "rack";
        opt.idc_name = "idc";
        opt.az_name = "az1";
        opt.group_name = "";
        auto node = std::shared_ptr<Node>(new Node(opt));
        auto disk = std::shared_ptr<Disk>(new Disk(1, 0, node));
        disk->_calc_used_size = 50 * aries::common::GB;
        node->add_disk(disk);

        std::vector<DiskStat> disk_stat_list;
        DiskStat disk_stat(1, 100 * aries::common::GB, 50 * aries::common::GB,
            100 * aries::common::GB, 50 * aries::common::GB, 0, 0, 0, 0, 0);
        disk_stat_list.push_back(disk_stat);
        node->update_disk_stat(disk_stat_list);

        node->set_alive(true);
        node->set_state(NODE_STATE_NORMAL);
        node_list.push_back(node);

        FLAGS_max_disk_usage = "0.99";
        FLAGS_max_disk_usage_for_create = "0.95";
        FLAGS_disk_reserve_percent = 0.1;

        picker.refresh_suitable_disk_list(node_list);
        ASSERT_EQ(picker._recover_cluster->disk_num(), 1);
        ASSERT_EQ(picker._create_cluster->disk_num(), 1);
        ASSERT_EQ(picker._src_balance_cluster->disk_num(), 1);
        ASSERT_EQ(picker._dest_balance_cluster->disk_num(), 1);
    }
    {
        NodeVector node_list;
        base::EndPoint addr = common::int2endpoint(1);
        NodeOptions opt;
        opt.addr = addr;
        opt.rack_name = "rack";
        opt.idc_name = "idc";
        opt.az_name = "az1";
        opt.group_name = "";
        auto node = std::shared_ptr<Node>(new Node(opt));
        auto disk = std::shared_ptr<Disk>(new Disk(1, 0, node));
        disk->_calc_used_size = 50 * aries::common::GB;
        node->add_disk(disk);

        std::vector<DiskStat> disk_stat_list;
        DiskStat disk_stat(1, 100 * aries::common::GB, 50 * aries::common::GB,
            100 * aries::common::GB, 50 * aries::common::GB, 0, 0, 0, 0, 0);
        disk_stat_list.push_back(disk_stat);
        node->update_disk_stat(disk_stat_list);

        node->set_alive(true);
        node->set_state(NODE_STATE_NORMAL);
        node_list.push_back(node);

        FLAGS_max_disk_usage = "0.1,az1:0.99";
        FLAGS_max_disk_usage_for_create = "0.1,az1:0.95";
        FLAGS_disk_reserve_percent = 0.1;

        picker.refresh_suitable_disk_list(node_list);
        ASSERT_EQ(picker._recover_cluster->disk_num(), 1);
        ASSERT_EQ(picker._create_cluster->disk_num(), 1);
        ASSERT_EQ(picker._src_balance_cluster->disk_num(), 1);
        ASSERT_EQ(picker._dest_balance_cluster->disk_num(), 1);
    }
    {
        NodeVector node_list;
        base::EndPoint addr = common::int2endpoint(1);
        NodeOptions opt;
        opt.addr = addr;
        opt.rack_name = "rack";
        opt.idc_name = "idc";
        opt.az_name = "az1";
        opt.group_name = "";
        auto node = std::shared_ptr<Node>(new Node(opt));
        auto disk = std::shared_ptr<Disk>(new Disk(1, 0, node));
        disk->_calc_used_size = 50 * aries::common::GB;
        node->add_disk(disk);

        std::vector<DiskStat> disk_stat_list;
        DiskStat disk_stat(1, 100 * aries::common::GB, 50 * aries::common::GB,
            100 * aries::common::GB, 50 * aries::common::GB, 0, 0, 0, 0, 0);
        disk_stat_list.push_back(disk_stat);
        node->update_disk_stat(disk_stat_list);

        node->set_alive(true);
        node->set_state(NODE_STATE_DECOMMISSIONING);
        node_list.push_back(node);

        FLAGS_max_disk_usage = "0.99";
        FLAGS_max_disk_usage_for_create = "0.95";
        FLAGS_disk_reserve_percent = 0.1;

        picker.refresh_suitable_disk_list(node_list);
        ASSERT_EQ(picker._recover_cluster->disk_num(), 0);
        ASSERT_EQ(picker._create_cluster->disk_num(), 0);
        ASSERT_EQ(picker._src_balance_cluster->disk_num(), 1);
        ASSERT_EQ(picker._dest_balance_cluster->disk_num(), 0);
    }
    {
        NodeVector node_list;
        base::EndPoint addr = common::int2endpoint(1);
        NodeOptions opt;
        opt.addr = addr;
        opt.rack_name = "rack";
        opt.idc_name = "idc";
        opt.az_name = "az1";
        opt.group_name = "";
        auto node = std::shared_ptr<Node>(new Node(opt));
        auto disk = std::shared_ptr<Disk>(new Disk(1, 0, node));
        disk->_calc_used_size = 50 * aries::common::GB;
        node->add_disk(disk);

        std::vector<DiskStat> disk_stat_list;
        DiskStat disk_stat(1, 100 * aries::common::GB, 50 * aries::common::GB,
            100 * aries::common::GB, 50 * aries::common::GB, 0, 0, 0, 0, 0);
        disk_stat_list.push_back(disk_stat);
        node->update_disk_stat(disk_stat_list);

        node->set_alive(true);
        node->set_state(NODE_STATE_UNBALANCED);
        node_list.push_back(node);

        FLAGS_max_disk_usage = "0.99";
        FLAGS_max_disk_usage_for_create = "0.95";
        FLAGS_disk_reserve_percent = 0.1;

        picker.refresh_suitable_disk_list(node_list);
        ASSERT_EQ(picker._recover_cluster->disk_num(), 1);
        ASSERT_EQ(picker._create_cluster->disk_num(), 0);
        ASSERT_EQ(picker._src_balance_cluster->disk_num(), 0);
        ASSERT_EQ(picker._dest_balance_cluster->disk_num(), 1);
    }
    {
        NodeVector node_list;
        base::EndPoint addr = common::int2endpoint(1);
        NodeOptions opt;
        opt.addr = addr;
        opt.rack_name = "rack";
        opt.idc_name = "idc";
        opt.az_name = "az1";
        opt.group_name = "";
        auto node = std::shared_ptr<Node>(new Node(opt));
        auto disk = std::shared_ptr<Disk>(new Disk(1, 0, node));
        disk->_calc_used_size = 50 * aries::common::GB;
        node->add_disk(disk);

        std::vector<DiskStat> disk_stat_list;
        DiskStat disk_stat(1, 100 * aries::common::GB, 50 * aries::common::GB,
            100 * aries::common::GB, 50 * aries::common::GB, 0, 0, 0, 0, 0);
        disk_stat_list.push_back(disk_stat);
        node->update_disk_stat(disk_stat_list);

        node->set_alive(true);
        node->set_state(NODE_STATE_NORMAL);
        node_list.push_back(node);

        FLAGS_max_disk_usage = "0.99";
        FLAGS_max_disk_usage_for_create = "0.4";
        FLAGS_disk_reserve_percent = 0.1;

        picker.refresh_suitable_disk_list(node_list);
        ASSERT_EQ(picker._recover_cluster->disk_num(), 1);
        ASSERT_EQ(picker._create_cluster->disk_num(), 0);
        ASSERT_EQ(picker._src_balance_cluster->disk_num(), 1);
        ASSERT_EQ(picker._dest_balance_cluster->disk_num(), 0);
    }
    {
        NodeVector node_list;
        base::EndPoint addr = common::int2endpoint(1);
        NodeOptions opt;
        opt.addr = addr;
        opt.rack_name = "rack";
        opt.idc_name = "idc";
        opt.az_name = "az1";
        opt.group_name = "";
        auto node = std::shared_ptr<Node>(new Node(opt));
        auto disk = std::shared_ptr<Disk>(new Disk(1, 0, node));
        disk->_calc_used_size = 50 * aries::common::GB;
        node->add_disk(disk);

        std::vector<DiskStat> disk_stat_list;
        DiskStat disk_stat(1, 100 * aries::common::GB, 50 * aries::common::GB,
            100 * aries::common::GB, 50 * aries::common::GB, 0, 0, 0, 0, 0);
        disk_stat_list.push_back(disk_stat);
        node->update_disk_stat(disk_stat_list);

        node->set_alive(true);
        node->set_state(NODE_STATE_NORMAL);
        node_list.push_back(node);

        FLAGS_max_disk_usage = "0.45";
        FLAGS_max_disk_usage_for_create = "0.4";
        FLAGS_disk_reserve_percent = 0.1;

        picker.refresh_suitable_disk_list(node_list);
        ASSERT_EQ(picker._recover_cluster->disk_num(), 0);
        ASSERT_EQ(picker._create_cluster->disk_num(), 0);
        ASSERT_EQ(picker._src_balance_cluster->disk_num(), 1);
        ASSERT_EQ(picker._dest_balance_cluster->disk_num(), 0);
    }
    {
        NodeVector node_list;
        base::EndPoint addr = common::int2endpoint(1);
        NodeOptions opt;
        opt.addr = addr;
        opt.rack_name = "rack";
        opt.idc_name = "idc";
        opt.az_name = "az1";
        opt.group_name = "";
        auto node = std::shared_ptr<Node>(new Node(opt));
        auto disk = std::shared_ptr<Disk>(new Disk(1, 0, node));
        disk->_calc_used_size = 50 * aries::common::GB;
        node->add_disk(disk);

        std::vector<DiskStat> disk_stat_list;
        DiskStat disk_stat(1, 100 * aries::common::GB, 50 * aries::common::GB,
            100 * aries::common::GB, 50 * aries::common::GB, 0, 0, 0, 0, 0);
        disk_stat_list.push_back(disk_stat);
        node->update_disk_stat(disk_stat_list);

        node->set_alive(true);
        node->set_state(NODE_STATE_NORMAL);
        node_list.push_back(node);

        FLAGS_max_disk_usage = "0.9";
        FLAGS_max_disk_usage_for_create = "0.8";
        FLAGS_disk_reserve_percent = 0.1;

        FLAGS_max_running_balance_task_per_src_disk = 1;
        FLAGS_max_running_balance_task_per_dest_disk = 1;
        auto disk_addr = common::add_diskid2int(
                common::endpoint2int(node->addr()), disk->disk_id());
        g_balance_scheduler->balance_helper()->_disk_running_tasks[disk_addr] = 10;

        picker.refresh_suitable_disk_list(node_list);
        ASSERT_EQ(picker._recover_cluster->disk_num(), 1);
        ASSERT_EQ(picker._create_cluster->disk_num(), 1);
        ASSERT_EQ(picker._src_balance_cluster->disk_num(), 1);
        ASSERT_EQ(picker._dest_balance_cluster->disk_num(), 0);
    }
}

TEST_F(DiskPickerTest, test_refresh_suitable_node_list_happy_case) {
    DiskPicker picker;
    {
        NodeVector node_list;
        base::EndPoint addr = common::int2endpoint(1);
        NodeOptions opt;
        opt.addr = addr;
        opt.rack_name = "rack";
        opt.idc_name = "idc";
        opt.az_name = "az1";
        opt.group_name = "";
        auto node = std::shared_ptr<Node>(new Node(opt));
        auto disk = std::shared_ptr<Disk>(new Disk(1, 0, node));
        disk->_calc_used_size = 50 * aries::common::GB;
        disk->set_disk_type("SSD");
        node->add_disk(disk);

        std::vector<DiskStat> disk_stat_list;
        DiskStat disk_stat(1, 100 * aries::common::GB, 50 * aries::common::GB,
            100 * aries::common::GB, 50 * aries::common::GB, 0, 0, 0, 0, 0);
        disk_stat_list.push_back(disk_stat);
        node->update_disk_stat(disk_stat_list);

        node->set_alive(true);
        node->set_state(NODE_STATE_NORMAL);
        node_list.push_back(node);

        FLAGS_max_disk_usage = "0.99";
        FLAGS_max_disk_usage_for_create = "0.51";
        FLAGS_disk_reserve_percent = 0.1;

        picker.refresh_suitable_disk_list(node_list);
        std::set<std::string> group_set;
        group_set.insert("");
        ASSERT_EQ(picker.az_group_disk_type_free_size("az1", group_set, {"SSD"}), 1 * aries::common::GB);
    }
    //run agine check az_group_disk_type_free_size.
    {
        NodeVector node_list;
        base::EndPoint addr = common::int2endpoint(1);
        NodeOptions opt;
        opt.addr = addr;
        opt.rack_name = "rack";
        opt.idc_name = "idc";
        opt.az_name = "az1";
        opt.group_name = "";
        auto node = std::shared_ptr<Node>(new Node(opt));
        auto disk = std::shared_ptr<Disk>(new Disk(1, 0, node));
        disk->_calc_used_size = 50 * aries::common::GB;
        node->add_disk(disk);

        std::vector<DiskStat> disk_stat_list;
        DiskStat disk_stat(1, 100 * aries::common::GB, 50 * aries::common::GB,
            100 * aries::common::GB, 50 * aries::common::GB, 0, 0, 0, 0, 0);
        disk_stat_list.push_back(disk_stat);
        node->update_disk_stat(disk_stat_list);

        node->set_alive(true);
        node->set_state(NODE_STATE_NORMAL);
        node_list.push_back(node);

        FLAGS_max_disk_usage = "0.99";
        FLAGS_max_disk_usage_for_create = "0.51";
        FLAGS_disk_reserve_percent = 0.1;

        picker.refresh_suitable_disk_list(node_list);
        ASSERT_EQ(picker.az_group_disk_type_free_size("az1", {""}, {""}), 1 * aries::common::GB);
    }
}

static const size_t kRackNum = 50;
static const size_t kNodeNumPerRack = 20;

std::shared_ptr<Space> g_space;
std::shared_ptr<Space> g_space_1;
std::shared_ptr<Space> g_space_2;
VolumeVector g_volumes;

class MetaForDiskPicker {
public:
    void init() {
        add_datanodes();
        add_vlets();
    }

    void add_vlets() {
        SpaceOptions opt;
        opt.n = 18;
        opt.k = 9;
        opt.space_name = "test_table";
        opt.max_vlet_per_rack = 1;
        opt.max_vlet_per_idc = 100;
        opt.max_vlet_per_az = 100;
        opt.group_set.insert("");
        opt.disk_type_set.insert("");
        opt.az_list.push_back("az1");
        opt.vlet_type = VLET_TYPE_LINKED_32G_4M_512K;
        g_space = std::shared_ptr<Space>(new Space(opt));
        opt.pick_az_policy = PAZP_UNIT_IN_AZ;
        g_space_1 = std::shared_ptr<Space>(new Space(opt));
        opt.pick_az_policy = PAZP_MAX_PER_AZ;
        g_space_2 = std::shared_ptr<Space>(new Space(opt));

        for (int i = 0; i < 10000; ++i) {
            VolumeOptions volume_opt;
            if (i % 3 == 0) {
                volume_opt.space = g_space;
            } else if (i % 3 == 1) {
                volume_opt.space = g_space_1;
            } else {
                volume_opt.space = g_space_2;
            }
            volume_opt.volume_id = i;
            auto volume = std::shared_ptr<Volume>(new Volume(volume_opt));
            volume->set_state(VOLUME_STATE_NORMAL);
            volume->set_per_vlet_size(32 * aries::common::GB);
            volume->set_compact_vlet_size(0);
            volume->set_compact_progress(NONE);
            g_volumes.push_back(volume);

            for (uint32_t j = 0; j < g_space->n(); ++j) {
                VletOptions vlet_opt;
                vlet_opt.shard_index = j;
                vlet_opt.volume = volume;
                auto vlet = std::shared_ptr<Vlet>(new Vlet(vlet_opt));
                
                volume->add_vlet(vlet);
            }
        }
    }

    void add_datanodes() {
        for (size_t i = 1; i <= kRackNum; ++i) {
            for (size_t j = 0; j < kNodeNumPerRack; ++j) {
                uint64_t int_addr = i * kNodeNumPerRack + j;
                base::EndPoint addr = common::int2endpoint(int_addr);

                char rack_name[100];
                sprintf(rack_name, "rack:%d", (int)i);
                NodeOptions opt;
                opt.addr = addr;
                opt.rack_name = rack_name;
                opt.idc_name = "idc";
                opt.az_name = "az1";
                opt.group_name = "";
                auto node = std::shared_ptr<Node>(new Node(opt));
                auto disk = std::shared_ptr<Disk>(new Disk(1, 0, node));
                disk->set_disk_type("");
                node->add_disk(disk);

                std::vector<DiskStat> disk_stat_list;
                DiskStat disk_stat(1, 618475290624, 618475290624, 618475290624, 0, 0, 0, 0, 0, 0);
                disk_stat_list.push_back(disk_stat);
                node->update_disk_stat(disk_stat_list);

                node->set_alive(true);
                _node_list.push_back(node);
                g_meta_data->add_node(node);
            }
        }
    }
private:
    NodeVector _node_list;
};

TEST_F(DiskPickerTest, test_1000_pick) {
    MetaForDiskPicker meta;
    meta.init();

    NodeVector node_list = meta._node_list;
    FLAGS_pick_disk_type_policy = SPACE_RULE_TYPE;
    DiskPicker disk_picker;
    disk_picker.refresh_suitable_disk_list(node_list);
    
    for (int i = 0; i < 999; i++) {
        auto volume = g_volumes[i];

        for (uint32_t j = 0; j < g_space->n(); ++j) {
            auto vlet = volume->get_vlet(j);
            auto disk = disk_picker.pick_disk_for_vlet(vlet, MASTER_VLET_TASK_TYPE_RECOVER);

            //total disk can create 1000, 999 must be all succ.
            ASSERT_TRUE(disk != nullptr);

            vlet->set_disk_ptr(disk);
            if (disk) {
                //disk->add_vlet(vlet);
                disk->add_calc_used_size(vlet_size_by_type(VLET_TYPE_LINKED_32G_4M_512K));
            }
        }
    }

    for (int i = 1000; i < 1001; i++) {
        auto volume = g_volumes[i];
        auto vlet = volume->get_vlet(0);

        for (uint32_t j = 0; j < g_space->n(); ++j) {
            auto vlet = volume->get_vlet(j);
            auto disk = disk_picker.pick_disk_for_vlet(vlet, MASTER_VLET_TASK_TYPE_RECOVER);
            vlet->set_disk_ptr(disk);
            if (disk) {
                //disk->add_vlet(vlet);
                disk->add_calc_used_size(vlet_size_by_type(VLET_TYPE_LINKED_32G_4M_512K));
            }
        }
    }

    for (auto& node : node_list) {
        auto diff = fabs(node->usage() - 1);
        // rack limit 1, total 50 rack, 18 + 18 + 14 == 50, left 4, 4/18 == 0.22222
        ASSERT_TRUE(diff < 0.23);
    }
    for (int i = 0; i < 100; i++) {
        auto volume = g_volumes[i];
        if (volume->space() == g_space_1) {
            VletVector vlet_list;
            volume->get_vlet_list(&vlet_list);
            auto az_name = vlet_list[0]->az_name();
            ASSERT_NE(az_name, "");
            for (auto& vlet : vlet_list) {
                ASSERT_EQ(az_name, vlet->az_name());
            }
        }
    }
}

TEST_F(DiskPickerTest, test_engine_type_filter) {
    // all ok
    {
        EngineTypePickFilter engine_type_filter_1({});
        ASSERT_TRUE(engine_type_filter_1.check(ENGINE_AUTO));
        ASSERT_TRUE(engine_type_filter_1.check(ENGINE_AUTO));
        ASSERT_TRUE(engine_type_filter_1.check(ENGINE_AUTO));
        EngineTypePickFilter engine_type_filter_2({ENGINE_AUTO, ENGINE_LINKED, ENGINE_APPEND});
        ASSERT_TRUE(engine_type_filter_2.check(ENGINE_LINKED));
        ASSERT_TRUE(engine_type_filter_2.check(ENGINE_APPEND));
        ASSERT_TRUE(engine_type_filter_2.check(ENGINE_AUTO));
        ASSERT_TRUE(engine_type_filter_2.check(ENGINE_LINKED));
        ASSERT_TRUE(engine_type_filter_2.check(ENGINE_APPEND));
        ASSERT_TRUE(engine_type_filter_2.check(ENGINE_AUTO));
    }
    // linked ok
    {
        EngineTypePickFilter engine_type_filter_1({ENGINE_LINKED});
        ASSERT_FALSE(engine_type_filter_1.check(ENGINE_AUTO));
        ASSERT_FALSE(engine_type_filter_1.check(ENGINE_APPEND));
        ASSERT_TRUE(engine_type_filter_1.check(ENGINE_LINKED));

        EngineTypePickFilter engine_type_filter_2({ENGINE_AUTO, ENGINE_LINKED});
        ASSERT_FALSE(engine_type_filter_2.check(ENGINE_APPEND));
        ASSERT_TRUE(engine_type_filter_2.check(ENGINE_AUTO));
        ASSERT_TRUE(engine_type_filter_2.check(ENGINE_LINKED));
    }
    // append ok
    {
        EngineTypePickFilter engine_type_filter_1({ENGINE_AUTO, ENGINE_APPEND});
        ASSERT_TRUE(engine_type_filter_1.check(ENGINE_AUTO));
        ASSERT_TRUE(engine_type_filter_1.check(ENGINE_APPEND));
        ASSERT_TRUE(engine_type_filter_1.check(ENGINE_AUTO));
        ASSERT_FALSE(engine_type_filter_1.check(ENGINE_LINKED));
    }
}

TEST_F(DiskPickerTest, pick_disk_type_by_vlet) {
    auto volume = _volumes[0];
    auto space = volume->space();
    space->_options.disk_type_set = {"HDD", "SMR", "SSD"};
    
    auto vlet = volume->get_vlet(0);
    auto disk = vlet->disk();
    bool succ = false;
    std::set<std::string> disk_type_set;
    {   
        FLAGS_pick_disk_type_policy = SPACE_RULE_TYPE;
        disk_type_set.clear();
        succ = DiskPicker::pick_disk_type_by_vlet(vlet, false, disk_type_set);
        EXPECT_TRUE(succ);
        EXPECT_EQ(disk_type_set.size(), 3);
        std::set<std::string> tmp_set({"SMR", "HDD", "SSD"});
        EXPECT_EQ(disk_type_set, tmp_set);
        for (int i = 0; i < space->n(); ++i) {
            space->_options.disk_type_place_policy_map.insert(std::make_pair(i, "SMR"));
        }
        disk_type_set.clear();
        succ = DiskPicker::pick_disk_type_by_vlet(vlet, false, disk_type_set);
        EXPECT_TRUE(succ);
        EXPECT_EQ(disk_type_set.size(), 1);
        EXPECT_EQ(disk_type_set, std::set<std::string>{"SMR"});

        // END
        space->_options.disk_type_place_policy_map.clear();
    }
    {  
        FLAGS_pick_disk_type_policy = SAME_DISK_TYPE;
        // other balance pick same disk
        {
            disk_type_set.clear();
            succ = DiskPicker::pick_disk_type_by_vlet(vlet, false, disk_type_set);
            EXPECT_TRUE(succ);
            EXPECT_EQ(disk_type_set.size(), 1);
            EXPECT_EQ(disk_type_set, std::set<std::string>{vlet->disk_type_name()});
        }
        {
            // manual balance will use space rule
            disk_type_set.clear();
            succ = DiskPicker::pick_disk_type_by_vlet(vlet, true, disk_type_set);
            EXPECT_TRUE(succ);
            EXPECT_EQ(disk_type_set.size(), 3);
            std::set<std::string> tmp_set({"SMR", "HDD", "SSD"});
            EXPECT_EQ(disk_type_set, tmp_set);
        }
        // drop vlet, recover / create pick disk by disk_vlet_type_set
        {
            // if vlet has valid vlet type , use vlet vlet_type
            space->_options.disk_vlet_type_map = {std::make_pair("HDD", space->vlet_type()), std::make_pair("SMR", VLET_TYPE_APPEND_64G_256M_4K)};
            vlet->set_disk_ptr(nullptr);
            vlet->set_vlet_type(VLET_TYPE_APPEND_64G_256M_4K);
            disk_type_set.clear();
            succ = DiskPicker::pick_disk_type_by_vlet(vlet, false, disk_type_set);
            EXPECT_TRUE(succ);
            EXPECT_EQ(disk_type_set.size(), 1);
            EXPECT_EQ(disk_type_set, std::set<std::string>({"SMR"}));
       
            // if vlet has valid vlet type , use space vlet_type
            vlet->set_vlet_type(VLET_TYPE_ERROR);
            disk_type_set.clear();
            succ = DiskPicker::pick_disk_type_by_vlet(vlet, false, disk_type_set);
            EXPECT_TRUE(succ);
            EXPECT_EQ(disk_type_set.size(), 1);
            EXPECT_EQ(disk_type_set, std::set<std::string>({"HDD"}));
        }

        // END
        vlet->set_disk_ptr(disk);
        space->_options.disk_type_place_policy_map.clear();
    }
    {
        FLAGS_pick_disk_type_policy = SPACE_DISK_TYPE;

        for (int i = 0; i < space->n(); ++i) {
            space->_options.disk_type_place_policy_map.insert(std::make_pair(i, "SMR"));
        }
        disk_type_set.clear();
        succ = DiskPicker::pick_disk_type_by_vlet(vlet, false, disk_type_set);
        EXPECT_TRUE(succ);
        EXPECT_EQ(disk_type_set.size(), 3);
        std::set<std::string> tmp_set({"SMR", "HDD", "SSD"});
        EXPECT_EQ(disk_type_set, tmp_set);

        // END
        space->_options.disk_type_place_policy_map.clear();
    }
    {
        std::set<std::string> disk_type_set;
        FLAGS_pick_disk_type_policy = 10000;
        disk_type_set.clear();
        succ = DiskPicker::pick_disk_type_by_vlet(vlet, false, disk_type_set);
        EXPECT_FALSE(succ);

        // END
        space->_options.disk_type_place_policy_map.clear();
    }
}

}
}


