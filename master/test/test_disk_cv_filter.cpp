/**
 * ARIES - A Reliable and Impressive EB-level Storage.
 * Copyright (c) 2016 Baidu.com, Inc. All Rights Reserved.
 *
 * Author: <PERSON> (guo<PERSON>@baidu.com)
 * Date: 2016/11/14
 * Description: Unittest for Disk Picker
 *
 */

#include <gtest/gtest.h>
#include <base/logging.h>
#include <baidu/rpc/server.h>
#include "baidu/inf/aries-api/common/types.h"
#include "baidu/inf/aries/master/conf.h"
#include "baidu/inf/aries/master/scheduler/disk_picker.h"
#include "baidu/inf/aries/master/scheduler/balance_scheduler.h"
#include "baidu/inf/aries/master/test/test_utils.h"
#include "baidu/inf/aries/master/test/test_master_test_base.h"

namespace aries {
namespace master {

static const size_t kRackNum = 50;
static const size_t kNodeNumPerRack = 20;

std::shared_ptr<Space> g_space;
std::shared_ptr<Space> g_space_1;
std::shared_ptr<Space> g_space_2;
VolumeVector g_volumes;
NodeVector g_node_list;

class TestEnvironment : public testing::Environment {
public:
    void SetUp() {
        add_datanodes();
        add_vlets();
        pick_disk_for_vlet();
    }

    void add_vlets() {
        SpaceOptions opt;
        opt.n = 18;
        opt.k = 9;
        opt.space_name = "test_table";
        opt.max_vlet_per_rack = 1;
        opt.max_vlet_per_idc = 100;
        opt.max_vlet_per_az = 100;
        opt.group_set.insert("");
        opt.disk_type_set.insert("");
        opt.az_list.push_back("az1");
        opt.vlet_type = VLET_TYPE_LINKED_32G_4M_512K;
        g_space = std::shared_ptr<Space>(new Space(opt));
        g_space->set_space_id(0);
        opt.pick_az_policy = PAZP_UNIT_IN_AZ;
        g_space_1 = std::shared_ptr<Space>(new Space(opt));
        g_space_1->set_space_id(1);
        opt.pick_az_policy = PAZP_MAX_PER_AZ;
        g_space_2 = std::shared_ptr<Space>(new Space(opt));
        g_space_2->set_space_id(2);

        for (int i = 0; i < 500; ++i) {
            VolumeOptions volume_opt;
            if (i % 3 == 0) {
                volume_opt.space = g_space;
            } else if (i % 3 == 1) {
                volume_opt.space = g_space_1;
            } else {
                volume_opt.space = g_space_2;
            }
            volume_opt.volume_id = i;
            auto volume = std::shared_ptr<Volume>(new Volume(volume_opt));
            volume->set_state(VOLUME_STATE_NORMAL);
            volume->set_per_vlet_size(32 * aries::common::GB);
            volume->set_compact_vlet_size(0);
            volume->set_compact_progress(NONE);
            g_volumes.push_back(volume);
            g_space_manager->add_space(volume_opt.space);
            aries::pb::VletEngineInfo vlet_engine_option;
            vlet_engine_option.set_vlet_size(32 * common::GB);
            for (uint32_t j = 0; j < g_space->n(); ++j) {
                VletOptions vlet_opt;
                vlet_opt.shard_index = j;
                vlet_opt.volume = volume;
                vlet_opt.state = VLET_STATE_NORMAL;
                auto vlet = std::shared_ptr<Vlet>(new Vlet(vlet_opt));
                vlet->set_vlet_type(VLET_TYPE_LINKED_32G_4M_512K);
                vlet->set_vlet_engine_info(vlet_engine_option);
                volume->add_vlet(vlet);
            }
        }
    }

    void add_datanodes() {
        for (size_t i = 1; i <= kRackNum; ++i) {
            for (size_t j = 0; j < kNodeNumPerRack; ++j) {
                uint64_t int_addr = i * kNodeNumPerRack + j;
                base::EndPoint addr = common::int2endpoint(int_addr);

                char rack_name[100];
                sprintf(rack_name, "rack:%d", (int)i);
                NodeOptions opt;
                opt.addr = addr;
                opt.rack_name = rack_name;
                opt.idc_name = "idc";
                opt.az_name = "az1";
                opt.group_name = "";
                auto node = std::shared_ptr<Node>(new Node(opt));
                auto disk = std::shared_ptr<Disk>(new Disk(1, 0, node));
                disk->set_disk_type("");
                disk->set_state(DISK_STATE_NORMAL);
                node->add_disk(disk);

                std::vector<DiskStat> disk_stat_list;
                DiskStat disk_stat(1, 618475290624, 618475290624, 618475290624, 0, 0, 0, 0, 0, 0);
                disk_stat_list.push_back(disk_stat);
                node->update_disk_stat(disk_stat_list);

                node->set_alive(true);
                g_node_list.push_back(node);
                g_meta_data->add_node(node);
            }
        }
    }
    void pick_disk_for_vlet() {
        DiskPicker disk_picker;
        disk_picker.refresh_suitable_disk_list(g_node_list);
        for (int i = 0; i < 500; ++i) {
            auto volume = g_volumes[i];
            for (uint32_t j = 0; j < g_space->n(); ++j) {
                auto vlet = volume->get_vlet(j);
                auto disk = disk_picker.pick_disk_for_vlet(vlet, MASTER_VLET_TASK_TYPE_RECOVER);
                vlet->set_disk_ptr(disk);
                if (disk) {
                    disk->add_vlet(vlet);
                    disk->add_calc_used_size(vlet->vlet_engine_info_ptr()->vlet_size());
                }
            }
        }
    }
private:
};


class CVFilterTest : public::testing::Test {
public:
    CVFilterTest() {
        FLAGS_cv_limit_per_disk = 0.1;
    }
    ~CVFilterTest() {}
private:
};

TEST_F(CVFilterTest, test_refresh_cv_hight_disks) {
    auto volume_0 = g_volumes[0];
    auto vlet = volume_0->get_vlet(0);
    auto disk = vlet->disk();

    ASSERT_TRUE(disk != nullptr);

    FLAGS_is_open_cv_filter = true;
    DiskPicker disk_picker;
    bool success = disk_picker.refresh_cv_hight_disks(disk);
    ASSERT_TRUE(success);
    FLAGS_is_open_cv_filter = false;
}

TEST_F(CVFilterTest, test_refresh_cv_hight_disks_map) {
    FLAGS_is_open_cv_filter = true;
    NodeVector node_list = g_node_list;
    DiskPicker disk_picker;
    bool success = disk_picker.refresh_cv_hight_disks_map(node_list);
    ASSERT_TRUE(success);
    FLAGS_is_open_cv_filter = false;
}

TEST_F(CVFilterTest, test_filter_cv_hight_disk_for_vlet) {
    FLAGS_is_open_cv_filter = true;
    auto volume_0 = g_volumes[0];
    auto vlet = volume_0->get_vlet(0);

    DiskUint64Vector disk_list;
    for (auto node : g_node_list) {
        DiskVector disk_list_per_node;
        node->get_disk_list(&disk_list_per_node);
        for (auto disk : disk_list_per_node) {
            disk_list.push_back(common::add_diskid2int(common::endpoint2int(node->addr()), disk->disk_id()));
        }
    }
    DiskPicker disk_picker;
    {
        // not refresh
        auto before_disk_list_size = disk_list.size();
        bool success = disk_picker.filter_cv_hight_disk_for_vlet(vlet, &disk_list);
        auto after_disk_list_size = disk_list.size();
        LOG(TRACE) << "after filter, disk_list size:" << disk_list.size();
        ASSERT_FALSE(success);
        ASSERT_EQ(after_disk_list_size, before_disk_list_size);
    }
    {
        // refresh
        auto success = disk_picker.refresh_cv_hight_disks_map(g_node_list);
        auto before_disk_list_size = disk_list.size();
        LOG(TRACE) << "before filter, disk_list size:" << disk_list.size();
        success = disk_picker.filter_cv_hight_disk_for_vlet(vlet, &disk_list);
        LOG(TRACE) << "after filter, disk_list size:" << disk_list.size();
        auto after_disk_list_size = disk_list.size();
        ASSERT_TRUE(success);
        ASSERT_NE(before_disk_list_size, 0);
        
    }
    FLAGS_is_open_cv_filter = false;
}

TEST_F(CVFilterTest, test_filter_cv_hight_disk_for_disk) {
    FLAGS_is_open_cv_filter = true;
    auto volume_0 = g_volumes[0];
    auto vlet = volume_0->get_vlet(0);
    auto disk = vlet->disk();
    
    DiskUint64Vector disk_list;
    for (auto node : g_node_list) {
        DiskVector disk_list_per_node;
        node->get_disk_list(&disk_list_per_node);
        for (auto disk : disk_list_per_node) {
            disk_list.push_back(common::add_diskid2int(common::endpoint2int(node->addr()), disk->disk_id()));
        }
    }

    DiskPicker disk_picker;
    {
        // not refresh
        auto before_disk_list_size = disk_list.size();
        bool success = disk_picker.filter_cv_hight_disk_for_disk(disk, &disk_list);
        auto after_disk_list_size = disk_list.size();
        LOG(TRACE) << "after filter, disk_list size:" << disk_list.size();
        ASSERT_FALSE(success);
        ASSERT_EQ(after_disk_list_size, before_disk_list_size);
    }
    {
        // refresh
        auto success = disk_picker.refresh_cv_hight_disks_map(g_node_list);
        auto before_disk_list_size = disk_list.size();
        LOG(TRACE) << "before filter, disk_list size:" << disk_list.size();
        success = disk_picker.filter_cv_hight_disk_for_disk(disk, &disk_list);
        LOG(TRACE) << "after filter, disk_list size:" << disk_list.size();
        auto after_disk_list_size = disk_list.size();
        ASSERT_TRUE(success);
        ASSERT_NE(before_disk_list_size, 0);
        
    }
    FLAGS_is_open_cv_filter = false;
}

}
}

int main(int argc, char* *argv) {
    ::testing::AddGlobalTestEnvironment(new aries::master::TestEnvironment());
    ::testing::InitGoogleTest(&argc, argv);
    return RUN_ALL_TESTS();
}


