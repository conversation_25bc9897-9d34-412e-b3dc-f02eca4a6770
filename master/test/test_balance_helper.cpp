/**
 * ARIES - A Reliable and Impressive EB-level Storage.
 * Copyright (c) 2020 Baidu.com, Inc. All Rights Reserved.
 *
 * Author: meng<PERSON><PERSON><PERSON>@baidu.com
 * Date: Tue Sep 29 16:59:07 CST 2020
 * Description: Unittest for BalanceHelper
 *
 */

#include <gtest/gtest.h>
#include <base/logging.h>
#include <baidu/rpc/server.h>
#include "baidu/inf/aries-api/common/types.h"
#include "baidu/inf/aries/master/conf.h"
#include "baidu/inf/aries/master/scheduler/disk_picker.h"
#include "baidu/inf/aries/master/scheduler/balance_scheduler.h"
#include "baidu/inf/aries/master/scheduler/across_cluster_copy_scheduler.h"
#include "baidu/inf/aries/master/test/test_utils.h"
#include "baidu/inf/aries/common/bmock_util.h"
#include "baidu/inf/aries/master/test/test_master_test_base.h"
#include "baidu/inf/aries/master/meta/meta_data.h"

namespace aries {
namespace master {

using ::testing::_;
using ::testing::Invoke;
using ::testing::Return;

class BalanceHelperTest : public::testing::Test {
protected:
    BalanceHelperTest() {
        _cluster_copy_scheduler.set_balance_helper(&_balance_helper);
        _balance_scheduler.set_balance_helper(&_balance_helper);
        for (int i = 0; i < _space_num; ++i) {
             SpaceOptions space_option;
            space_option.n = _vlet_num_per_volume;
            space_option.put_quorum = 10;
            space_option.space_name = "test_" + std::to_string(i);
            space_option.drop_normal_remain = 12;
            space_option.vlet_type = VLET_TYPE_LINKED_32G_4M_512K;
            std::shared_ptr<Space> space = std::make_shared<Space>(space_option);
            _space_vec.push_back(space);
            for (int j = 0; j < _volume_num_per_space; ++j) {
                VolumeOptions volume_option;
                volume_option.space = space;
                volume_option.volume_id = i * _volume_num_per_space + j;
                std::shared_ptr<Volume> volume = std::make_shared<Volume>(volume_option);
                volume->set_state(VOLUME_STATE_NORMAL);
                _volume_vec.push_back(volume);

                NodeOptions node_option;
                node_option.addr = common::int2endpoint(100 + i * _volume_num_per_space + j);
                node_option.state = NODE_STATE_NORMAL;
                std::shared_ptr<Node> node_ptr = std::make_shared<Node>(node_option);
                _node_vec.push_back(node_ptr);
                for (int k = 0; k < (int)space_option.n; ++k) {
                    VletOptions vlet_option;
                    vlet_option.volume = volume;
                    vlet_option.state = VLET_STATE_NORMAL;
                    vlet_option.shard_index = k;
                    std::shared_ptr<Disk> disk_ptr = std::make_shared<Disk>(k, node_ptr);
                    disk_ptr->set_disk_type("HDD");
                    _disk_vec.push_back(disk_ptr);
                    vlet_option.disk_ptr = disk_ptr;
                    std::shared_ptr<aries::master::Vlet> vlet = std::make_shared<aries::master::Vlet>(vlet_option);
                    vlet->set_vlet_type(VLET_TYPE_LINKED_32G_4M_512K);
                    _vlet_vec.push_back(vlet);
                    volume->add_vlet(vlet);
                    node_ptr->add_disk(disk_ptr);
                }
                g_meta_data->add_node(node_ptr);
            }
        }
    }
    virtual ~BalanceHelperTest() {}
    void TearDown() override {
        //clear env
        g_meta_data->_node_manager->_node_map.clear();
        g_meta_data->_space_manager->_space_map.clear();
        g_meta_data->_space_manager->_space_id = 0;
        g_meta_data->_volume_manager->_volume_map.clear();
        g_meta_data->_volume_manager->_max_volume_id = 0;
        g_meta_data->_az_manager->_az_map.clear();
        g_meta_data->_allocator_manager->_allocator_map.clear();
        g_meta_data->_allocator_manager->_max_sequence_id = 0;
        g_meta_data->_tinker_manager->_tinker_map.clear();
        g_meta_data->_raft_index = 0;
        g_across_cluster_copy_scheduler->clear();
    }
private:
    int _space_num = 1;
    int _volume_num_per_space = 10;
    int _vlet_num_per_volume = 15;
    std::vector<std::shared_ptr<Space>> _space_vec;
    std::vector<std::shared_ptr<Volume>> _volume_vec;
    std::vector<std::shared_ptr<Vlet>> _vlet_vec;
    std::vector<std::shared_ptr<Node>> _node_vec;
    std::vector<std::shared_ptr<Disk>> _disk_vec;
    BalanceHelper _balance_helper;
    AcrossClusterCopyScheduler _cluster_copy_scheduler;
    BalanceScheduler _balance_scheduler;
};

TEST_F(BalanceHelperTest, test_balance_helper_disk_task_limit) {
    g_balance_helper->clear();
    FLAGS_max_running_balance_task_per_src_disk = 100;
    FLAGS_max_running_balance_task_per_dest_disk = 100;
    FLAGS_max_running_balance_task_per_src_node = 100;
    FLAGS_max_running_balance_task_per_dest_node = 100;
    FLAGS_max_running_decommission_balance_task_per_disk = 100;
    FLAGS_max_running_decommission_balance_task_per_node = 100;
    g_balance_helper->clear();

    auto disk_addr = common::add_diskid2int(common::endpoint2int(_disk_vec[0]->addr()), _disk_vec[0]->disk_id());
    for (int run_num = 0; run_num < 10; ++run_num) {
        g_balance_helper->create_disk_running_task(disk_addr);
        EXPECT_TRUE(g_balance_helper->permit_balance_on_disk(disk_addr, true));
        EXPECT_TRUE(g_balance_helper->permit_balance_on_disk(disk_addr, false));
        EXPECT_TRUE(g_balance_helper->permit_decommission_balance_on_disk(disk_addr));
    }

    FLAGS_max_running_balance_task_per_src_disk = 11;
    FLAGS_max_running_balance_task_per_dest_disk = 10;
    FLAGS_max_running_balance_task_per_src_node = 11;
    FLAGS_max_running_balance_task_per_dest_node = 11;
    EXPECT_TRUE(g_balance_helper->permit_balance_on_disk(disk_addr, true));
    EXPECT_TRUE(!g_balance_helper->permit_balance_on_disk(disk_addr, false));
    
    FLAGS_max_running_balance_task_per_src_disk = 10;
    FLAGS_max_running_balance_task_per_dest_disk = 11;
    FLAGS_max_running_balance_task_per_src_node = 11;
    FLAGS_max_running_balance_task_per_dest_node = 11;
    EXPECT_TRUE(!g_balance_helper->permit_balance_on_disk(disk_addr, true));
    EXPECT_TRUE(g_balance_helper->permit_balance_on_disk(disk_addr, false));

    FLAGS_max_running_balance_task_per_src_disk = 11;
    FLAGS_max_running_balance_task_per_dest_disk = 11;
    FLAGS_max_running_balance_task_per_src_node = 11;
    FLAGS_max_running_balance_task_per_dest_node = 10;
    EXPECT_TRUE(g_balance_helper->permit_balance_on_disk(disk_addr, true));
    EXPECT_TRUE(!g_balance_helper->permit_balance_on_disk(disk_addr, false));
    
    FLAGS_max_running_balance_task_per_src_disk = 11;
    FLAGS_max_running_balance_task_per_dest_disk = 11;
    FLAGS_max_running_balance_task_per_src_node = 10;
    FLAGS_max_running_balance_task_per_dest_node = 11;
    EXPECT_TRUE(!g_balance_helper->permit_balance_on_disk(disk_addr, true));
    EXPECT_TRUE(g_balance_helper->permit_balance_on_disk(disk_addr, false));

    FLAGS_max_running_balance_task_per_src_disk = 11;
    FLAGS_max_running_balance_task_per_dest_disk = 11;
    FLAGS_max_running_balance_task_per_src_node = 11;
    FLAGS_max_running_balance_task_per_dest_node = 11;
    EXPECT_TRUE(g_balance_helper->permit_balance_on_disk(disk_addr, true));
    EXPECT_TRUE(g_balance_helper->permit_balance_on_disk(disk_addr, false));
    
    FLAGS_max_running_decommission_balance_task_per_disk = 10;
    FLAGS_max_running_decommission_balance_task_per_node = 11;
    EXPECT_TRUE(!g_balance_helper->permit_decommission_balance_on_disk(disk_addr));

    FLAGS_max_running_decommission_balance_task_per_disk = 11;
    FLAGS_max_running_decommission_balance_task_per_node = 10;
    EXPECT_TRUE(!g_balance_helper->permit_decommission_balance_on_disk(disk_addr));

    FLAGS_max_running_decommission_balance_task_per_disk = 11;
    FLAGS_max_running_decommission_balance_task_per_node = 11;
    EXPECT_TRUE(g_balance_helper->permit_decommission_balance_on_disk(disk_addr));

    uint64_t node_addr = common::endpoint2int(common::int2endpoint(disk_addr));
    auto node = g_node_manager->get_node(common::int2endpoint(node_addr));
    node->set_state(NODE_STATE_UNBALANCED);
    NodeVector node_list;
    node_list.push_back(node);
    g_balance_helper->refresh_unbalanced_node_list(node_list);
    FLAGS_max_running_balance_task_per_dest_disk = 10;
    FLAGS_max_running_balance_task_per_dest_node = 10;
    FLAGS_max_running_balance_task_per_dest_unbalanced_disk = 11;
    FLAGS_max_running_balance_task_per_dest_unbalanced_node = 11;
    EXPECT_TRUE(g_balance_helper->permit_balance_on_disk(disk_addr, false));

    FLAGS_max_running_balance_task_per_dest_unbalanced_disk = 11;
    FLAGS_max_running_balance_task_per_dest_unbalanced_node = 10;
    EXPECT_TRUE(!g_balance_helper->permit_balance_on_disk(disk_addr, false));

    FLAGS_max_running_balance_task_per_dest_unbalanced_disk = 10;
    FLAGS_max_running_balance_task_per_dest_unbalanced_node = 11;
    EXPECT_TRUE(!g_balance_helper->permit_balance_on_disk(disk_addr, false));
    std::vector<uint64_t> disk_list;
    std::vector<uint64_t> permit_balance_disk_list;
    disk_list.push_back(disk_addr);
    g_balance_helper->batch_check_permit_balance_on_disk(disk_list, false, &permit_balance_disk_list);
    EXPECT_TRUE(permit_balance_disk_list.empty());
    FLAGS_max_running_balance_task_per_dest_unbalanced_disk = 11;
    FLAGS_max_running_balance_task_per_dest_unbalanced_node = 11;
    g_balance_helper->batch_check_permit_balance_on_disk(disk_list, false, &permit_balance_disk_list);
    EXPECT_TRUE(!permit_balance_disk_list.empty());

    for (int run_num = 0; run_num < 10; ++run_num) {
        g_balance_helper->erase_disk_running_task(disk_addr);   
    }
    EXPECT_TRUE(g_balance_helper->_disk_running_tasks.empty());
    EXPECT_TRUE(g_balance_helper->_node_running_tasks.empty());

}

TEST_F(BalanceHelperTest, test_balance_helper_volume_task_limit) {
    g_balance_helper->clear();
    auto vlet = _vlet_vec[0];
    vlet->set_state(VLET_STATE_DROPPED);
    EXPECT_FALSE(g_balance_helper->permit_balance_vlet(vlet));
    vlet->set_state(VLET_STATE_DROPPING);
    EXPECT_FALSE(g_balance_helper->permit_balance_vlet(vlet));
    vlet->set_state(VLET_STATE_ERROR);
    EXPECT_FALSE(g_balance_helper->permit_balance_vlet(vlet));
    vlet->set_state(VLET_STATE_RECEIVING);
    EXPECT_FALSE(g_balance_helper->permit_balance_vlet(vlet));
    
    FLAGS_max_running_balance_task_per_src_disk = 1;
    FLAGS_max_running_balance_task_per_src_node = 1;
    vlet->set_state(VLET_STATE_REPAIRING);
    EXPECT_TRUE(g_balance_helper->permit_balance_vlet(vlet));
    vlet->set_state(VLET_STATE_JOINING);
    EXPECT_TRUE(g_balance_helper->permit_balance_vlet(vlet));
    vlet->set_state(VLET_STATE_NORMAL);
    EXPECT_TRUE(g_balance_helper->permit_balance_vlet(vlet));

    auto volume = _volume_vec[0];
    EXPECT_FALSE(g_balance_helper->is_balance_volume_dangerous(volume));
    g_balance_helper->create_volume_running_task(volume->volume_id());
    EXPECT_TRUE(g_balance_helper->is_balance_volume_dangerous(volume));
    g_balance_helper->erase_volume_running_task(volume->volume_id());
    EXPECT_FALSE(g_balance_helper->is_balance_volume_dangerous(volume));
}

TEST_F(BalanceHelperTest, test_send_balance_vlet) {
    bool success = false;
    std::shared_ptr<Vlet> vlet = _vlet_vec[0];
    std::shared_ptr<BalanceVletContext> context = std::make_shared<BalanceVletContext>();
    context->log_id = 123;
    auto src_vlet = _vlet_vec[0];
    auto src_disk_addr = common::add_diskid2int(common::endpoint2int(src_vlet->addr()), src_vlet->disk_id());
    auto src_node = src_vlet->node();
    auto dest_node = _node_vec[3];

    context->volume_id = vlet->volume_id();
    context->task_log_id = 456;
    context->src_disk_addr = src_disk_addr;
    context->shard_index = src_vlet->shard_index();

    g_meta_data->_node_manager->_node_map.clear();
    {
        context->move_type = COPY_SPACE;
        aries::pb::CopyVletRequest request;
        context->copy_request = &request;
        auto dest_disk_addr = common::add_diskid2int(common::endpoint2int(dest_node->addr()), 1);
        context->dst_disk_addr = dest_disk_addr;

        _cluster_copy_scheduler.balance_helper()->create_volume_running_task(context->volume_id);
        _cluster_copy_scheduler.balance_helper()->create_disk_running_task(context->src_disk_addr);
        _cluster_copy_scheduler.balance_helper()->create_disk_running_task(context->dst_disk_addr);
        
        success = _cluster_copy_scheduler.send_balance_vlet_req(src_vlet, context);
        EXPECT_FALSE(success);
        g_node_manager->add_node(dest_node);
        success = _cluster_copy_scheduler.send_balance_vlet_req(src_vlet, context);
        EXPECT_TRUE(success);
    }

    // wait done finish
    sleep(1);
}


}
}