//=============================================================================
// Author: <EMAIL>
// Data: 2016-12-02 13:39
// Filename: test_master.cpp
// Description: 
//=============================================================================

#include <memory>
#include <gtest/gtest.h>
#include <gflags/gflags.h>
#include <base/logging.h>
#include <base/endpoint.h>
#include <base/string_printf.h>
#include <base/file_util.h>
#include <baidu/rpc/server.h>
#include <base/logging.h>
#include <baidu/rpc/server.h>
#include "baidu/inf/aries-api/common/utils.h"
#include "baidu/inf/aries-api/common/config.h"
#include "baidu/inf/aries-api/common/common.h"
#include "baidu/inf/aries-api/common/proto/master.pb.h"
#include "baidu/inf/aries/master/meta/master_state_machine.h"
#include "baidu/inf/aries/master/meta/meta_data.h"
#include "baidu/inf/aries/master/common.h"
#include "baidu/inf/aries/master/conf.h"
#include "baidu/inf/aries/master/service/manage_service.h"
#include "baidu/inf/aries/master/master_control.h"
#include "baidu/inf/aries/master/master.h"
#include "baidu/inf/aries-api/common/rpc_stub.h"
#include "baidu/inf/aries-api/common/master_tracker.h"

namespace aries {
namespace master {

class MasterTests : public ::testing::Test {
public:
    MasterTests() {
    }
    virtual ~MasterTests() {}

    void SetUp() {
    }

    void TearDown() {
    }

private:
};

TEST_F(MasterTests, test_fail) {
    {
        Master master;
        master._manage_service = NULL;
        auto ret = master.start();
        ASSERT_TRUE(ret != 0);
    }

    {
        Master master;
        master._heartbeat_service = NULL;
        auto ret = master.start();
        ASSERT_TRUE(ret != 0);
    }

    {
        Master master;
        master._query_service = NULL;
        auto ret = master.start();
        ASSERT_TRUE(ret != 0);
    }
}

TEST_F(MasterTests, test_normal) {
    {
        common::FLAGS_master_address = "test.aries.baidu.com:8888";
        Master master;
        auto ret = master.start();
        ASSERT_TRUE(ret != 0);
    }

    {
        Master master;
        common::FLAGS_port = 8821;
        ::aries::common::init_local_addr();
        common::FLAGS_master_address = 
            "http://127.0.0.1:1234";
        auto ret = master.start();
        ASSERT_TRUE(ret == 0);
        ret = master.stop();
        ASSERT_TRUE(ret == 0);
        ret = master.join();
        ASSERT_TRUE(ret == 0);
    }
}

}//end namespace master
}//end namespace aries

