//=============================================================================
// Author: <EMAIL>
// Data: 2016-11-09 20:45
// Filename: test_manage_service.cpp
// Description: 
//=============================================================================

#include <memory>
#include <gtest/gtest.h>
#include <gflags/gflags.h>
#include <base/logging.h>
#include <base/endpoint.h>
#include <base/string_printf.h>
#include <base/file_util.h>
#include <baidu/rpc/server.h>
#include <base/logging.h>
#include <baidu/rpc/server.h>
#include "bmock.h"
#include "baidu/inf/aries/common/bmock_util.h"
#include "baidu/inf/aries/common/openssl_aes.h"
#include "baidu/inf/aries-api/common/utils.h"
#include "baidu/inf/aries-api/common/common.h"
#include "baidu/inf/aries-api/common/closure.h"
#include "baidu/inf/aries-api/common/proto/master.pb.h"
#include "baidu/inf/aries/master/meta/master_state_machine.h"
#include "baidu/inf/aries/master/meta/meta_data.h"
#include "baidu/inf/aries/master/common.h"
#include "baidu/inf/aries/master/service/manage_service.h"
#include "baidu/inf/aries/master/scheduler/balance_scheduler.h"
#include "baidu/inf/aries/master/master_control.h"
#include "baidu/inf/aries-api/common/rpc_stub.h"
#include "baidu/inf/aries/master/test/mock_common.h"
#include "baidu/inf/aries/master/scheduler/across_cluster_copy_scheduler.h"
#include "baidu/inf/aries/master/scheduler/balance_scheduler.h"
#include "baidu/inf/aries/common/bmock_util.h"

namespace aries {
namespace master {

using ::testing::Return;
using ::testing::_;
using ::testing::SetArgPointee;
// mock
BMOCK_NS_CLASS_METHOD1(aries::master, AZManager, get_az,
        std::shared_ptr<AZ>(const std::string&));
BMOCK_NS_CLASS_METHOD0(aries::master, AZManager, disabled_az_name,
        std::string());
BMOCK_NS_CLASS_METHOD1(aries::master, AcrossClusterCopyScheduler, 
        send_copy_vlet_to_node,
        base::Status(const aries::pb::CopyVletRequest&));
MAKE_BMOCK_NS_CLASS_METHOD(6, aries::common, OpenSSLAES, aes_encrypt, 
    int(const std::string&, const int, const std::string&, std::string*, 
        const aries::common::EncryptMode, const std::string&));
MAKE_BMOCK_NS_CLASS_METHOD(0, aries::master, BalanceScheduler, is_running_az_usage_balance,
        bool());

class ManageServiceTests : public ::testing::Test {
public:
    ManageServiceTests() {
    }
    virtual ~ManageServiceTests() {}

    void SetUp() {
        _p_meta = new PrepareMetaData();
        _p_meta->init();
        ManageServiceImpl* manage_service = new ManageServiceImpl();
        _master_control = new MockMasterControl;
        g_master_control = _master_control;
        CHECK_EQ(0, _server.AddService(manage_service, baidu::rpc::SERVER_OWNS_SERVICE));
        ASSERT_EQ(0, base::str2endpoint("127.0.0.1:60235", &_listen_addr));
        ASSERT_EQ(0, _server.Start(_listen_addr, NULL));
    }

    void TearDown() {
        // stop and join server
        _server.Stop(100);
        _server.Join();
        g_meta_data->_space_manager->_space_map.clear();
        g_meta_data->_volume_manager->_volume_map.clear();
        g_meta_data->_volume_manager->_max_volume_id = 0;
        g_meta_data->_az_manager->_az_map.clear();
        g_meta_data->_node_manager->_node_map.clear();
        g_meta_data->_allocator_manager->_allocator_map.clear();
        g_meta_data->_allocator_manager->_max_sequence_id = 0;
        g_meta_data->_tinker_manager->_tinker_map.clear();
    }

    base::IOBuf prepair_err_data() {
        base::IOBuf data;
        data = "some thing...}";
        return data;
    }

private:
    baidu::rpc::Server _server;
    base::EndPoint _listen_addr;
    MockMasterControl* _master_control;
    PrepareMetaData* _p_meta;
};

TEST_F(ManageServiceTests, test_add_disk) {
    MasterStub stub(_listen_addr);
    g_meta_data->set_state(MASTER_STATE_LEADER_WORKING);

    //add disk succ .
    {
        aries::pb::MasterAddDiskRequest request;
        aries::pb::AckResponse response;
        auto addr = _p_meta->get_node_addr(1);
        request.set_node_addr(common::endpoint2int(addr));
        request.set_disk_id(2);
        request.set_token("default_token");
        SynchronizedClosure rpc_waiter;
        stub.add_disk(_listen_addr, &request, &response, &rpc_waiter, NULL);
        rpc_waiter.wait();
        ASSERT_EQ(response.status().code(), AIE_OK);
    }
    //add disk disk exist.
    {
        aries::pb::MasterAddDiskRequest request;
        aries::pb::AckResponse response;
        auto addr = _p_meta->get_node_addr(2);
        request.set_node_addr(common::endpoint2int(addr));
        request.set_disk_id(1);
        request.set_token("default_token");
        SynchronizedClosure rpc_waiter;
        stub.add_disk(_listen_addr, &request, &response, &rpc_waiter, NULL);
        rpc_waiter.wait();
        ASSERT_EQ(response.status().code(), AIE_EXIST);
    }
    _master_control->_add_disk_ret = false;
    //add disk fail .
    {
        aries::pb::MasterAddDiskRequest request;
        aries::pb::AckResponse response;
        auto addr = _p_meta->get_node_addr(1);
        request.set_node_addr(common::endpoint2int(addr));
        request.set_disk_id(2);
        request.set_token("default_token");
        SynchronizedClosure rpc_waiter;
        stub.add_disk(_listen_addr, &request, &response, &rpc_waiter, NULL);
        rpc_waiter.wait();
        ASSERT_EQ(response.status().code(), AIE_FAIL);
    }
    //force add disk succ
    {
        aries::pb::MasterAddDiskRequest request;
        aries::pb::AckResponse response;
        auto addr = _p_meta->get_node_addr(1);
        request.set_is_force_add(true);
        request.set_node_addr(common::endpoint2int(addr));
        request.set_disk_id(2);
        request.set_token("default_token");
        SynchronizedClosure rpc_waiter;
        stub.add_disk(_listen_addr, &request, &response, &rpc_waiter, NULL);
        rpc_waiter.wait();
        ASSERT_EQ(response.status().code(), AIE_OK);
    }
    // engine type and disk type not match
    {
        aries::pb::MasterAddDiskRequest request;
        aries::pb::AckResponse response;
        auto addr = _p_meta->get_node_addr(1);
        request.set_node_addr(common::endpoint2int(addr));
        request.set_disk_id(2);
        request.set_disk_type("SMR");
        request.set_engine_type(ENGINE_LINKED);
        request.set_token("default_token");
        SynchronizedClosure rpc_waiter;
        stub.add_disk(_listen_addr, &request, &response, &rpc_waiter, NULL);
        rpc_waiter.wait();
        ASSERT_EQ(response.status().code(), AIE_FAIL);
    }
}

TEST_F(ManageServiceTests, test_update_disk) {
    MasterStub stub(_listen_addr);
    g_meta_data->set_state(MASTER_STATE_LEADER_WORKING);

    //update disk disk not exist.
    {
        aries::pb::MasterUpdateDiskRequest request;
        aries::pb::AckResponse response;
        auto addr = _p_meta->get_node_addr(1);
        request.set_node_addr(common::endpoint2int(addr));
        request.set_disk_id(2);
        request.set_engine_type(ENGINE_AUTO);
        request.set_token("default_token");
        SynchronizedClosure rpc_waiter;
        stub.update_disk(_listen_addr, &request, &response, &rpc_waiter, NULL);
        rpc_waiter.wait();
        ASSERT_EQ(response.status().code(), AIE_NOT_EXIST);
    }
    
    //update disk succ .
    {
        aries::pb::MasterUpdateDiskRequest request;
        aries::pb::AckResponse response;
        auto addr = _p_meta->get_node_addr(2);
        request.set_node_addr(common::endpoint2int(addr));
        request.set_disk_id(1);
        request.set_engine_type(ENGINE_AUTO);
        request.set_token("default_token");
        SynchronizedClosure rpc_waiter;
        stub.update_disk(_listen_addr, &request, &response, &rpc_waiter, NULL);
        rpc_waiter.wait();
        ASSERT_EQ(response.status().code(), AIE_OK);
    }
    // engine type and disk type not match
    {
        aries::pb::MasterUpdateDiskRequest request;
        aries::pb::AckResponse response;
        auto addr = _p_meta->get_node_addr(7);
        request.set_node_addr(common::endpoint2int(addr));
        request.set_disk_id(1); // SMR disk
        request.set_engine_type(ENGINE_LINKED);
        request.set_token("default_token");
        SynchronizedClosure rpc_waiter;
        stub.update_disk(_listen_addr, &request, &response, &rpc_waiter, NULL);
        rpc_waiter.wait();
        ASSERT_EQ(response.status().code(), AIE_FAIL);
    }
    // engine type and disk type match
    {
        aries::pb::MasterUpdateDiskRequest request;
        aries::pb::AckResponse response;
        auto addr = _p_meta->get_node_addr(7);
        request.set_node_addr(common::endpoint2int(addr));
        request.set_disk_id(1); // SMR disk
        request.set_engine_type(ENGINE_APPEND);
        request.set_token("default_token");
        SynchronizedClosure rpc_waiter;
        stub.update_disk(_listen_addr, &request, &response, &rpc_waiter, NULL);
        rpc_waiter.wait();
        ASSERT_EQ(response.status().code(), AIE_OK);
    }
    //not set engine type
    {
        aries::pb::MasterUpdateDiskRequest request;
        aries::pb::AckResponse response;
        auto addr = _p_meta->get_node_addr(7);
        request.set_node_addr(common::endpoint2int(addr));
        request.set_disk_id(1);
        request.set_token("default_token");
        SynchronizedClosure rpc_waiter;
        stub.update_disk(_listen_addr, &request, &response, &rpc_waiter, NULL);
        rpc_waiter.wait();
        ASSERT_EQ(response.status().code(), AIE_FAIL);
    }
}

TEST_F(ManageServiceTests, test_add_node) {
    MasterStub stub(_listen_addr);
    g_meta_data->set_state(MASTER_STATE_LEADER_WORKING);

    // add succ
    {
        aries::pb::AddNodeRequest request;
        aries::pb::AckResponse response;
        auto addr = _p_meta->get_node_addr(0);
        request.set_node_addr(common::endpoint2int(addr));
        request.set_az_name("Default");
        request.set_rack_name("zone1");
        request.set_token("default_token");

        SynchronizedClosure rpc_waiter;
        stub.add_node(_listen_addr, &request, &response, &rpc_waiter, NULL);
        rpc_waiter.wait();

        ASSERT_EQ(response.status().code(), 0);
    }
    // node already exist
    {
        g_meta_data->set_state(MASTER_STATE_LEADER_WORKING);
        aries::pb::AddNodeRequest request;
        aries::pb::AckResponse response;
        auto addr = _p_meta->get_node_addr(1);
        request.set_node_addr(common::endpoint2int(addr));
        request.set_az_name("Default");
        request.set_rack_name("zone1");
        request.set_token("default_token");

        SynchronizedClosure rpc_waiter;
        stub.add_node(_listen_addr, &request, &response, &rpc_waiter, NULL);
        rpc_waiter.wait();
        ASSERT_EQ(response.status().code(), AIE_EXIST);
    }
    // disable unbalanced state
    {
        FLAGS_enable_unbalanced_node_state = false;
        g_meta_data->set_state(MASTER_STATE_LEADER_WORKING);
        aries::pb::AddNodeRequest request;
        aries::pb::AckResponse response;
        auto addr = _p_meta->get_node_addr(0);
        request.set_node_addr(common::endpoint2int(addr));
        request.set_az_name("Default");
        request.set_rack_name("zone1");
        request.set_token("default_token");
        request.set_state(NODE_STATE_UNBALANCED);

        SynchronizedClosure rpc_waiter;
        stub.add_node(_listen_addr, &request, &response, &rpc_waiter, NULL);
        rpc_waiter.wait();
        ASSERT_EQ(response.status().code(), AIE_FAIL);
        FLAGS_enable_unbalanced_node_state = true;
    }
}

TEST_F(ManageServiceTests, test_create_snapshot) {
    {
        MasterStub stub(_listen_addr);
        g_meta_data->set_state(MASTER_STATE_LEADER_WORKING);
        aries::pb::CreateSnapshotRequest request;
        request.set_token("default_token");
        aries::pb::AckResponse response;

        SynchronizedClosure rpc_waiter;
        stub.create_snapshot(_listen_addr, &request, &response, &rpc_waiter, NULL);
        rpc_waiter.wait();
        ASSERT_EQ(response.status().code(), AIE_OK);
    }
}

TEST_F(ManageServiceTests, test_update_node_location) {
    {
        // node not exist.
        MasterStub stub(_listen_addr);
        g_meta_data->set_state(MASTER_STATE_LEADER_WORKING);
        aries::pb::UpdateNodeLocationRequest request;
        aries::pb::AckResponse response;
        request.set_node_addr(1314);
        request.set_token("default_token");
        request.set_group_name("change_group");

        SynchronizedClosure rpc_waiter;
        stub.update_node_location(_listen_addr, &request, &response, &rpc_waiter, NULL);
        rpc_waiter.wait();
        ASSERT_EQ(response.status().code(), AIE_FAIL);
    }
    {
        // change group succ.
        MasterStub stub(_listen_addr);
        g_meta_data->set_state(MASTER_STATE_LEADER_WORKING);
        aries::pb::UpdateNodeLocationRequest request;
        aries::pb::AckResponse response;
        auto addr = _p_meta->get_node_addr(1);
        request.set_node_addr(common::endpoint2int(addr));
        request.set_token("default_token");
        request.set_group_name("group2");

        SynchronizedClosure rpc_waiter;
        stub.update_node_location(_listen_addr, &request, &response, &rpc_waiter, NULL);
        rpc_waiter.wait();
        ASSERT_EQ(response.status().code(), AIE_OK);
    }
    {
        // change az fail.
        MasterStub stub(_listen_addr);
        g_meta_data->set_state(MASTER_STATE_LEADER_WORKING);
        aries::pb::UpdateNodeLocationRequest request;
        aries::pb::AckResponse response;
        auto addr = _p_meta->get_node_addr(1);
        request.set_node_addr(common::endpoint2int(addr));
        request.set_token("default_token");
        request.set_group_name("group2");
        request.set_az_name("az2");

        SynchronizedClosure rpc_waiter;
        stub.update_node_location(_listen_addr, &request, &response, &rpc_waiter, NULL);
        rpc_waiter.wait();
        ASSERT_EQ(response.status().code(), AIE_INVALID_ARGUMENT);
    }
    {
        // change idc fail.
        MasterStub stub(_listen_addr);
        g_meta_data->set_state(MASTER_STATE_LEADER_WORKING);
        aries::pb::UpdateNodeLocationRequest request;
        aries::pb::AckResponse response;
        auto addr = _p_meta->get_node_addr(1);
        request.set_node_addr(common::endpoint2int(addr));
        request.set_token("default_token");
        request.set_group_name("group2");
        request.set_idc_name("idc2");

        SynchronizedClosure rpc_waiter;
        stub.update_node_location(_listen_addr, &request, &response, &rpc_waiter, NULL);
        rpc_waiter.wait();
        ASSERT_EQ(response.status().code(), AIE_INVALID_ARGUMENT);
    }
    {
        // change az/idc succ.
        MasterStub stub(_listen_addr);
        g_meta_data->set_state(MASTER_STATE_LEADER_WORKING);
        aries::pb::UpdateNodeLocationRequest request;
        aries::pb::AckResponse response;
        auto addr = _p_meta->get_node_addr(1);
        request.set_node_addr(common::endpoint2int(addr));
        request.set_token("default_token");
        request.set_group_name("group2");
        request.set_az_name("az2");
        request.set_idc_name("idc2");

        SynchronizedClosure rpc_waiter;
        stub.update_node_location(_listen_addr, &request, &response, &rpc_waiter, NULL);
        rpc_waiter.wait();
        ASSERT_EQ(response.status().code(), AIE_OK);
    }
    {
        // change rack succ.
        MasterStub stub(_listen_addr);
        g_meta_data->set_state(MASTER_STATE_LEADER_WORKING);
        aries::pb::UpdateNodeLocationRequest request;
        aries::pb::AckResponse response;
        auto addr = _p_meta->get_node_addr(1);
        request.set_node_addr(common::endpoint2int(addr));
        request.set_token("default_token");
        request.set_group_name("group2");
        request.set_az_name("az2");
        request.set_idc_name("idc2");
        request.set_rack_name("rack2");

        SynchronizedClosure rpc_waiter;
        stub.update_node_location(_listen_addr, &request, &response, &rpc_waiter, NULL);
        rpc_waiter.wait();
        ASSERT_EQ(response.status().code(), AIE_OK);
    }
    {
        // change rack succ.
        MasterStub stub(_listen_addr);
        g_meta_data->set_state(MASTER_STATE_LEADER_WORKING);
        aries::pb::UpdateNodeLocationRequest request;
        aries::pb::AckResponse response;
        auto addr = _p_meta->get_node_addr(1);
        request.set_node_addr(common::endpoint2int(addr));
        request.set_token("default_token");
        request.set_rack_name("rack3");

        SynchronizedClosure rpc_waiter;
        stub.update_node_location(_listen_addr, &request, &response, &rpc_waiter, NULL);
        rpc_waiter.wait();
        ASSERT_EQ(response.status().code(), AIE_OK);
    }
}


TEST_F(ManageServiceTests, test_update_node_state) {
    {
        MasterStub stub(_listen_addr);
        g_meta_data->set_state(MASTER_STATE_LEADER_WORKING);
        aries::pb::UpdateNodeRequest request;
        aries::pb::AckResponse response;
        auto addr = _p_meta->get_node_addr(1);
        request.set_node_addr(common::endpoint2int(addr));
        request.set_token("default_token");
        request.set_state(NODE_STATE_DECOMMISSIONING);
        request.set_src_state(NODE_STATE_DECOMMISSIONED);

        SynchronizedClosure rpc_waiter;
        stub.upd_node_status(_listen_addr, &request, &response, &rpc_waiter, NULL);
        rpc_waiter.wait();
        ASSERT_EQ(response.status().code(), AIE_FAIL);
    }
    {
        MasterStub stub(_listen_addr);
        g_meta_data->set_state(MASTER_STATE_LEADER_WORKING);
        aries::pb::UpdateNodeRequest request;
        aries::pb::AckResponse response;
        auto addr = _p_meta->get_node_addr(1);
        request.set_node_addr(common::endpoint2int(addr));
        request.set_token("default_token");
        request.set_state(NODE_STATE_DECOMMISSIONING);
        request.set_src_state(NODE_STATE_NORMAL);

        SynchronizedClosure rpc_waiter;
        stub.upd_node_status(_listen_addr, &request, &response, &rpc_waiter, NULL);
        rpc_waiter.wait();
        ASSERT_EQ(response.status().code(), AIE_OK);
    }
    {
        FLAGS_enable_unbalanced_node_state = false;
        MasterStub stub(_listen_addr);
        g_meta_data->set_state(MASTER_STATE_LEADER_WORKING);
        aries::pb::UpdateNodeRequest request;
        aries::pb::AckResponse response;
        auto addr = _p_meta->get_node_addr(1);
        request.set_node_addr(common::endpoint2int(addr));
        request.set_token("default_token");
        request.set_state(NODE_STATE_UNBALANCED);
        request.set_src_state(NODE_STATE_DECOMMISSIONING);

        SynchronizedClosure rpc_waiter;
        stub.upd_node_status(_listen_addr, &request, &response, &rpc_waiter, NULL);
        rpc_waiter.wait();
        ASSERT_EQ(response.status().code(), AIE_FAIL);
        FLAGS_enable_unbalanced_node_state = true;
    }
}

TEST_F(ManageServiceTests, test_drop_node_with_non_existing_node) {
    MasterStub stub(_listen_addr);
    g_meta_data->set_state(MASTER_STATE_LEADER_WORKING);
    aries::pb::DropNodeRequest request;
    aries::pb::AckResponse response;
    auto addr = _p_meta->get_node_addr(0);
    request.set_node_addr(common::endpoint2int(addr));
    request.set_token("default_token");

    SynchronizedClosure rpc_waiter;
    stub.drop_node(_listen_addr, &request, &response, &rpc_waiter, NULL);
    rpc_waiter.wait();
    ASSERT_EQ(response.status().code(), AIE_NOT_EXIST);
}

TEST_F(ManageServiceTests, test_drop_node_happy_case) {
    MasterStub stub(_listen_addr);
    g_meta_data->set_state(MASTER_STATE_LEADER_WORKING);
    aries::pb::DropNodeRequest request;
    aries::pb::AckResponse response;
    auto addr = _p_meta->get_node_addr(1);
    request.set_node_addr(common::endpoint2int(addr));
    request.set_token("default_token");

    SynchronizedClosure rpc_waiter;
    stub.drop_node(_listen_addr, &request, &response, &rpc_waiter, NULL);
    rpc_waiter.wait();
    ASSERT_EQ(response.status().code(), AIE_OK);
}

TEST_F(ManageServiceTests, test_replace_vlet) {
    {
        aries::pb::ReplaceVletRequest request;
        aries::pb::AckResponse response;

        request.set_token("default_token");
        request.set_check_vlet_not_normal(false);

        auto dest_vlet = request.mutable_dest_vlet();
        dest_vlet->set_volume_id(3);
        dest_vlet->set_shard_index(0);
        dest_vlet->set_vlet_version(1);
        dest_vlet->set_vlet_type(VLET_TYPE_LINKED_32G_4M_512K);
        auto dest_addr = _p_meta->get_node(2);
        dest_vlet->set_node_addr(common::endpoint2int(dest_addr));
        dest_vlet->set_disk_id(1);

        baidu::rpc::Controller cntl;
        baidu::rpc::Channel channel;
        int ret = channel.Init(_listen_addr, NULL);
        aries::pb::MasterManageService_Stub stub(&channel);
        stub.replace_vlet(&cntl, &request, &response, NULL);

        ASSERT_EQ(response.status().code(), AIE_OK);
    }
    //fail not exist volume.
    {
        aries::pb::ReplaceVletRequest request;
        aries::pb::AckResponse response;

        request.set_token("default_token");
        request.set_check_vlet_not_normal(false);

        auto dest_vlet = request.mutable_dest_vlet();
        dest_vlet->set_volume_id(30);
        dest_vlet->set_shard_index(0);
        dest_vlet->set_vlet_version(1);
        dest_vlet->set_vlet_type(VLET_TYPE_LINKED_32G_4M_512K);
        auto dest_addr = _p_meta->get_node(2);

        auto node_ptr = g_node_manager->get_node(dest_addr);

        dest_vlet->set_node_addr(common::endpoint2int(dest_addr));
        dest_vlet->set_disk_id(1);

        baidu::rpc::Controller cntl;
        baidu::rpc::Channel channel;
        int ret = channel.Init(_listen_addr, NULL);
        aries::pb::MasterManageService_Stub stub(&channel);
        stub.replace_vlet(&cntl, &request, &response, NULL);

        ASSERT_EQ(response.status().code(), AIE_FAIL);
    }
}

TEST_F(ManageServiceTests, test_check_replace_request) {
    //version is same
    {
        aries::pb::VletInfo dest_vlet;

        dest_vlet.set_volume_id(3);
        dest_vlet.set_shard_index(0);
        dest_vlet.set_vlet_version(0);
        dest_vlet.set_vlet_type(VLET_TYPE_LINKED_32G_4M_512K);
        auto dest_addr = _p_meta->get_node(2);
        dest_vlet.set_node_addr(common::endpoint2int(dest_addr));
        dest_vlet.set_disk_id(1);

        std::string err_msg;
        ManageServiceImpl manage_service;
        auto ret = manage_service.check_replace_request(dest_vlet, false, true, &err_msg);
        ASSERT_EQ(ret, false);
        ASSERT_EQ(err_msg, "same vlet version");
    }
    //volume not exist
    {
        aries::pb::VletInfo dest_vlet;

        dest_vlet.set_volume_id(111);
        dest_vlet.set_shard_index(0);
        dest_vlet.set_vlet_version(1);
        dest_vlet.set_vlet_type(VLET_TYPE_LINKED_32G_4M_512K);
        auto dest_addr = _p_meta->get_node(2);
        dest_vlet.set_node_addr(common::endpoint2int(dest_addr));
        dest_vlet.set_disk_id(1);

        std::string err_msg;
        auto ret = ManageServiceImpl::check_replace_request(dest_vlet, false, true, &err_msg);
        ASSERT_EQ(ret, false);
        ASSERT_EQ(err_msg, "volume not exist");
    }
    //vlet not exist
    {
        aries::pb::VletInfo dest_vlet;
        dest_vlet.set_volume_id(3);
        dest_vlet.set_shard_index(3);
        dest_vlet.set_vlet_version(1);
        dest_vlet.set_vlet_type(VLET_TYPE_LINKED_32G_4M_512K);
        auto dest_addr = _p_meta->get_node(2);
        dest_vlet.set_node_addr(common::endpoint2int(dest_addr));
        dest_vlet.set_disk_id(1);

        std::string err_msg;
        auto ret = ManageServiceImpl::check_replace_request(dest_vlet, false, true, &err_msg);
        ASSERT_EQ(ret, false);
        ASSERT_EQ(err_msg, "vlet not exist");
    }
    //dest node not exist
    {
        aries::pb::VletInfo dest_vlet;

        dest_vlet.set_volume_id(3);
        dest_vlet.set_shard_index(0);
        dest_vlet.set_vlet_version(1);
        dest_vlet.set_vlet_type(VLET_TYPE_LINKED_32G_4M_512K);
        auto dest_addr = _p_meta->get_node(0);
        dest_vlet.set_node_addr(common::endpoint2int(dest_addr));
        dest_vlet.set_disk_id(1);

        std::string err_msg;
        auto ret = ManageServiceImpl::check_replace_request(dest_vlet, false, true, &err_msg);
        ASSERT_EQ(ret, false);
        ASSERT_EQ(err_msg, "node not exist");
    }
    //dest disk not exist
    {
        aries::pb::VletInfo dest_vlet;

        dest_vlet.set_volume_id(3);
        dest_vlet.set_shard_index(0);
        dest_vlet.set_vlet_version(1);
        dest_vlet.set_vlet_type(VLET_TYPE_LINKED_32G_4M_512K);
        auto dest_addr = _p_meta->get_node(1);
        dest_vlet.set_node_addr(common::endpoint2int(dest_addr));
        dest_vlet.set_disk_id(1);

        std::string err_msg;
        auto ret = ManageServiceImpl::check_replace_request(dest_vlet, false, true, &err_msg);
        ASSERT_EQ(ret, false);
        ASSERT_EQ(err_msg, "disk not exist"); 
    }
    //vlet type is VLET_TYPE_LINKED_VARIENT
    {
        aries::pb::VletInfo dest_vlet;

        dest_vlet.set_volume_id(3);
        dest_vlet.set_shard_index(0);
        dest_vlet.set_vlet_version(1);
        dest_vlet.set_vlet_type(VLET_TYPE_LINKED_VARIENT);
        auto dest_addr = _p_meta->get_node(2);
        dest_vlet.set_node_addr(common::endpoint2int(dest_addr));
        dest_vlet.set_disk_id(1);

        std::string err_msg;
        auto ret = ManageServiceImpl::check_replace_request(dest_vlet, false, true, &err_msg);
        ASSERT_EQ(ret, false);
    }
    // vlet engine info set
    {
        aries::pb::VletInfo dest_vlet;

        dest_vlet.set_volume_id(3);
        dest_vlet.set_shard_index(0);
        dest_vlet.set_vlet_version(1);
        dest_vlet.set_vlet_type(VLET_TYPE_LINKED_VARIENT);
        auto dest_addr = _p_meta->get_node(2);
        dest_vlet.set_node_addr(common::endpoint2int(dest_addr));
        dest_vlet.set_disk_id(1);
        auto vlet_engine_options = dest_vlet.mutable_vlet_engine_options();
        vlet_engine_options->set_vlet_size(16 * aries::common::GB);
        vlet_engine_options->set_block_size(4 * aries::common::MB);
        vlet_engine_options->set_max_record_size(512 * aries::common::KB);
        vlet_engine_options->set_min_record_size(4 * aries::common::KB);
        vlet_engine_options->set_record_gap_page_num(1);

        std::string err_msg;
        auto ret = ManageServiceImpl::check_replace_request(dest_vlet, false, true, &err_msg);
        ASSERT_EQ(ret, true);
    }
    //bad engine type
    {
        aries::pb::VletInfo dest_vlet;

        dest_vlet.set_volume_id(3);
        dest_vlet.set_shard_index(0);
        dest_vlet.set_vlet_version(1);
 //       dest_vlet.set_vlet_type(VLET_TYPE_LINKED_32G_4M_512K);
        auto dest_addr = _p_meta->get_node(2);
        dest_vlet.set_node_addr(common::endpoint2int(dest_addr));
        dest_vlet.set_disk_id(1);

        std::string err_msg;
        auto ret = ManageServiceImpl::check_replace_request(dest_vlet, false, true, &err_msg);
        ASSERT_EQ(ret, false);
        ASSERT_EQ(err_msg, "bad engine type"); 
    }
    //vlet type not right
    {
        aries::pb::VletInfo dest_vlet;

        dest_vlet.set_volume_id(3);
        dest_vlet.set_shard_index(0);
        dest_vlet.set_vlet_version(1);
        // dest_vlet.set_vlet_type(VLET_TYPE_LINKED_32G_4M_512K);
        dest_vlet.set_vlet_type(VLET_TYPE_LINKED_32G_8M_512K);
        auto dest_addr = _p_meta->get_node(2);
        dest_vlet.set_node_addr(common::endpoint2int(dest_addr));
        dest_vlet.set_disk_id(1);

        std::string err_msg;
        auto ret = ManageServiceImpl::check_replace_request(dest_vlet, false, true, &err_msg);
        ASSERT_EQ(ret, false);
        ASSERT_EQ(err_msg, "vlet type not right"); 
    }
    //vlet state is normal.
    {
        aries::pb::VletInfo dest_vlet;
        auto volume = g_volume_manager->get_volume(3);
        auto vlet = volume->get_vlet(1);
        auto old_state = vlet->state();
        vlet->set_state(VLET_STATE_NORMAL);

        dest_vlet.set_volume_id(3);
        dest_vlet.set_shard_index(1);
        dest_vlet.set_vlet_version(1);
        dest_vlet.set_vlet_type(VLET_TYPE_LINKED_32G_4M_512K);
        auto dest_addr = _p_meta->get_node(2);
        dest_vlet.set_node_addr(common::endpoint2int(dest_addr));
        dest_vlet.set_disk_id(1);

        std::string err_msg;
        auto ret = ManageServiceImpl::check_replace_request(dest_vlet, true, true, &err_msg);
        ASSERT_EQ(ret, false);
        ASSERT_EQ(err_msg, "vlet is normal state");
        vlet->set_state(old_state);
    }
    //vlet state not right.
    {
        aries::pb::VletInfo dest_vlet;
        auto volume = g_volume_manager->get_volume(3);
        auto vlet = volume->get_vlet(1);
        auto old_state = vlet->state();
        vlet->set_state(VLET_STATE_DROPPED);

        dest_vlet.set_volume_id(3);
        dest_vlet.set_shard_index(1);
        dest_vlet.set_vlet_version(1);
        dest_vlet.set_vlet_type(VLET_TYPE_LINKED_32G_4M_512K);
        auto dest_addr = _p_meta->get_node(2);
        dest_vlet.set_node_addr(common::endpoint2int(dest_addr));
        dest_vlet.set_disk_id(1);

        std::string err_msg;
        auto ret = ManageServiceImpl::check_replace_request(dest_vlet, false, true, &err_msg);
        ASSERT_EQ(ret, false);
        ASSERT_EQ(err_msg, "vlet is dropped state"); 
        vlet->set_state(old_state);
    }
    //volume state not right.
    {
        aries::pb::VletInfo dest_vlet;
        auto volume = g_volume_manager->get_volume(1);
        auto vlet = volume->get_vlet(1);

        dest_vlet.set_volume_id(1);
        dest_vlet.set_shard_index(1);
        dest_vlet.set_vlet_version(1);
        dest_vlet.set_vlet_type(VLET_TYPE_LINKED_32G_4M_512K);
        auto dest_addr = _p_meta->get_node(2);
        dest_vlet.set_node_addr(common::endpoint2int(dest_addr));
        dest_vlet.set_disk_id(1);

        std::string err_msg;
        auto ret = ManageServiceImpl::check_replace_request(dest_vlet, false, true, &err_msg);
        ASSERT_EQ(ret, false);
        ASSERT_EQ(err_msg, "volume is not normal");

        ret = ManageServiceImpl::check_replace_request(dest_vlet, false, false, &err_msg);
        ASSERT_EQ(ret, true);
    }
    //dest rack has too many vlet
    {
        auto volume = g_volume_manager->get_volume(3);
        auto space = volume->space();
        space->_options.max_vlet_per_rack = 1;

        aries::pb::VletInfo dest_vlet;
        dest_vlet.set_volume_id(3);
        dest_vlet.set_shard_index(0);
        dest_vlet.set_vlet_version(2);
        dest_vlet.set_vlet_type(VLET_TYPE_LINKED_32G_4M_512K);
        auto dest_addr = _p_meta->get_node(2);
        dest_vlet.set_node_addr(common::endpoint2int(dest_addr));
        dest_vlet.set_disk_id(1);

        std::string err_msg;
        auto ret = ManageServiceImpl::check_replace_request(dest_vlet, false, true, &err_msg);
        ASSERT_EQ(ret, false);
        ASSERT_EQ(err_msg, "dest node not suitable for vlet");
        space->_options.max_vlet_per_rack = 2;
    }
}

TEST_F(ManageServiceTests, test_drop_node_no_token) {
    {
        MasterStub stub(_listen_addr);
        g_meta_data->set_state(MASTER_STATE_LEADER_WORKING);
        aries::pb::DropNodeRequest request;
        aries::pb::AckResponse response;
        auto addr = _p_meta->get_node_addr(1);
        request.set_node_addr(common::endpoint2int(addr));
        request.set_token("default_token");

        g_token_manager->_drop_node_token = 0;

        SynchronizedClosure rpc_waiter;
        stub.drop_node(_listen_addr, &request, &response, &rpc_waiter, NULL);
        rpc_waiter.wait();
        ASSERT_EQ(response.status().code(), AIE_FAIL);
    }

    {
        MasterStub stub(_listen_addr);
        g_meta_data->set_state(MASTER_STATE_LEADER_WORKING);
        aries::pb::DropNodeRequest request;
        aries::pb::AckResponse response;
        auto addr = _p_meta->get_node_addr(1);
        request.set_node_addr(common::endpoint2int(addr));
        request.set_token("default_token");
        request.set_is_force_drop(true);
        g_token_manager->_drop_node_token = 0;

        SynchronizedClosure rpc_waiter;
        stub.drop_node(_listen_addr, &request, &response, &rpc_waiter, NULL);
        rpc_waiter.wait();
        ASSERT_EQ(response.status().code(), AIE_OK);
    }

    {
        MasterStub stub(_listen_addr);
        g_meta_data->set_state(MASTER_STATE_LEADER_WORKING);
        aries::pb::DropNodeRequest request;
        aries::pb::AckResponse response;
        auto addr = _p_meta->get_node_addr(1);
        request.set_node_addr(common::endpoint2int(addr));
        request.set_token("default_token");
        std::shared_ptr<Node> node = g_node_manager->get_node(addr);
        node->set_state(NODE_STATE_DECOMMISSIONED);
        g_token_manager->_drop_node_token = 0;

        SynchronizedClosure rpc_waiter;
        stub.drop_node(_listen_addr, &request, &response, &rpc_waiter, NULL);
        rpc_waiter.wait();
        ASSERT_EQ(response.status().code(), AIE_OK);
    }
}

TEST_F(ManageServiceTests, test_drop_node_with_failure_of_volume_in_danger) {
    MasterStub stub(_listen_addr);
    g_meta_data->set_state(MASTER_STATE_LEADER_WORKING);
    aries::pb::DropNodeRequest request;
    aries::pb::AckResponse response;
    auto addr = _p_meta->get_node_addr(5);
    request.set_node_addr(common::endpoint2int(addr));
    request.set_token("default_token");

    SynchronizedClosure rpc_waiter;
    stub.drop_node(_listen_addr, &request, &response, &rpc_waiter, NULL);
    rpc_waiter.wait();
    ASSERT_EQ(response.status().code(), AIE_FAIL);
}

TEST_F(ManageServiceTests, test_drop_node_with_force_to_success_of_volume_in_danger) {
    MasterStub stub(_listen_addr);
    g_meta_data->set_state(MASTER_STATE_LEADER_WORKING);
    aries::pb::DropNodeRequest request;
    aries::pb::AckResponse response;
    auto addr = _p_meta->get_node_addr(5);
    request.set_node_addr(common::endpoint2int(addr));
    request.set_is_force_drop(true);
    request.set_token("default_token");

    SynchronizedClosure rpc_waiter;
    stub.drop_node(_listen_addr, &request, &response, &rpc_waiter, NULL);
    rpc_waiter.wait();
    ASSERT_EQ(response.status().code(), AIE_OK);
}

TEST_F(ManageServiceTests, test_drop_node_happy_case_able_to_recover) {
    MasterStub stub(_listen_addr);
    g_meta_data->set_state(MASTER_STATE_LEADER_WORKING);
    auto volume = g_volume_manager->get_volume(1);

    aries::pb::DropNodeRequest request;
    aries::pb::AckResponse response;
    auto addr = _p_meta->get_node_addr(3);
    request.set_node_addr(common::endpoint2int(addr));
    request.set_token("default_token");

    SynchronizedClosure rpc_waiter;
    stub.drop_node(_listen_addr, &request, &response, &rpc_waiter, NULL);
    rpc_waiter.wait();
    ASSERT_EQ(response.status().code(), AIE_OK);
}

TEST_F(ManageServiceTests, test_drop_disk_with_non_existing_node) {
    MasterStub stub(_listen_addr);
    g_meta_data->set_state(MASTER_STATE_LEADER_WORKING);
    aries::pb::MasterDropDiskRequest request;
    aries::pb::AckResponse response;
    auto addr = _p_meta->get_node_addr(0);
    request.set_node_addr(common::endpoint2int(addr));
    request.set_disk_id(2);
    request.set_token("default_token");

    SynchronizedClosure rpc_waiter;
    stub.drop_disk(_listen_addr, &request, &response, &rpc_waiter, NULL);
    rpc_waiter.wait();
    ASSERT_EQ(response.status().code(), AIE_NOT_EXIST);
}

TEST_F(ManageServiceTests, test_drop_disk_with_non_existing_disk) {
    MasterStub stub(_listen_addr);
    g_meta_data->set_state(MASTER_STATE_LEADER_WORKING);
    aries::pb::MasterDropDiskRequest request;
    aries::pb::AckResponse response;
    auto addr = _p_meta->get_node_addr(1);
    request.set_node_addr(common::endpoint2int(addr));
    request.set_disk_id(2);
    request.set_token("default_token");

    SynchronizedClosure rpc_waiter;
    stub.drop_disk(_listen_addr, &request, &response, &rpc_waiter, NULL);
    rpc_waiter.wait();
    ASSERT_EQ(response.status().code(), AIE_NOT_EXIST);
}

TEST_F(ManageServiceTests, test_drop_disk_happy_case) {
    MasterStub stub(_listen_addr);
    g_meta_data->set_state(MASTER_STATE_LEADER_WORKING);
    aries::pb::MasterDropDiskRequest request;
    aries::pb::AckResponse response;
    auto addr = _p_meta->get_node_addr(2);
    request.set_node_addr(common::endpoint2int(addr));
    request.set_disk_id(1);
    request.set_token("default_token");
    request.set_is_force_drop(true);

    SynchronizedClosure rpc_waiter;
    stub.drop_disk(_listen_addr, &request, &response, &rpc_waiter, NULL);
    rpc_waiter.wait();
    ASSERT_EQ(response.status().code(), AIE_OK);
}

TEST_F(ManageServiceTests, test_drop_disk_no_token) {
    {
        MasterStub stub(_listen_addr);
        g_meta_data->set_state(MASTER_STATE_LEADER_WORKING);
        aries::pb::MasterDropDiskRequest request;
        aries::pb::AckResponse response;
        auto addr = _p_meta->get_node_addr(2);
        request.set_node_addr(common::endpoint2int(addr));
        request.set_disk_id(1);
        request.set_token("default_token");
        g_token_manager->_drop_disk_token = 0;

        SynchronizedClosure rpc_waiter;
        stub.drop_disk(_listen_addr, &request, &response, &rpc_waiter, NULL);
        rpc_waiter.wait();
        ASSERT_EQ(response.status().code(), AIE_FAIL);
    }
    {
        MasterStub stub(_listen_addr);
        g_meta_data->set_state(MASTER_STATE_LEADER_WORKING);
        aries::pb::MasterDropDiskRequest request;
        aries::pb::AckResponse response;
        auto addr = _p_meta->get_node_addr(2);
        request.set_node_addr(common::endpoint2int(addr));
        request.set_disk_id(1);
        request.set_token("default_token");
        request.set_is_force_drop(true);
        g_token_manager->_drop_disk_token = 0;

        SynchronizedClosure rpc_waiter;
        stub.drop_disk(_listen_addr, &request, &response, &rpc_waiter, NULL);
        rpc_waiter.wait();
        ASSERT_EQ(response.status().code(), AIE_OK);
    }
}

TEST_F(ManageServiceTests, test_drop_disk_with_failure_of_volume_in_danger) {
    MasterStub stub(_listen_addr);
    g_meta_data->set_state(MASTER_STATE_LEADER_WORKING);
    aries::pb::MasterDropDiskRequest request;
    aries::pb::AckResponse response;
    auto addr = _p_meta->get_node_addr(5);
    request.set_node_addr(common::endpoint2int(addr));
    request.set_disk_id(1);
    request.set_token("default_token");

    SynchronizedClosure rpc_waiter;
    stub.drop_disk(_listen_addr, &request, &response, &rpc_waiter, NULL);
    rpc_waiter.wait();
    ASSERT_EQ(response.status().code(), AIE_FAIL);
}

TEST_F(ManageServiceTests, test_drop_disk_with_force_to_success_of_volume_in_danger) {
    MasterStub stub(_listen_addr);
    g_meta_data->set_state(MASTER_STATE_LEADER_WORKING);
    aries::pb::MasterDropDiskRequest request;
    aries::pb::AckResponse response;
    auto addr = _p_meta->get_node_addr(5);
    request.set_node_addr(common::endpoint2int(addr));
    request.set_disk_id(1);
    request.set_is_force_drop(true);
    request.set_token("default_token");

    SynchronizedClosure rpc_waiter;
    stub.drop_disk(_listen_addr, &request, &response, &rpc_waiter, NULL);
    rpc_waiter.wait();
    ASSERT_EQ(response.status().code(), AIE_OK);
}

TEST_F(ManageServiceTests, test_drop_disk_with_node_safemode_status) {
    MasterStub stub(_listen_addr);
    g_meta_data->set_state(MASTER_STATE_LEADER_WORKING);
    aries::pb::MasterDropDiskRequest request;
    aries::pb::AckResponse response;
    auto addr = _p_meta->get_node_addr(5);
    request.set_node_addr(common::endpoint2int(addr));
    request.set_disk_id(1);
    request.set_is_force_drop(false);
    request.set_token("default_token");

    uint64_t timestamp = static_cast<uint64_t>(time(NULL));
    std::shared_ptr<Node> node = g_node_manager->get_node(addr);
    node->set_disk_safemode(true, timestamp);

    SynchronizedClosure rpc_waiter;
    stub.drop_disk(_listen_addr, &request, &response, &rpc_waiter, NULL);
    rpc_waiter.wait();
    ASSERT_EQ(response.status().code(), AIE_FAIL);
}

TEST_F(ManageServiceTests, test_create_space) {
    MasterStub stub(_listen_addr);
    //create space succ
    {
        aries::pb::CreateSpaceRequest request;
        aries::pb::AckResponse response;
        request.set_token("default_token");
        auto space_info = request.mutable_space_info();
        space_info->set_space_name("test_space2");
        space_info->set_ec_type(EC_RS_ISAL);
        space_info->set_n(18);
        space_info->set_k(9);
        space_info->set_put_quorum(15);
        space_info->set_delete_quorum(9);
        space_info->set_membership_quorum(10);
        space_info->set_allocator_collect_quorum(17);
        space_info->set_max_vlet_per_idc(18);
        space_info->set_max_vlet_per_rack(18);
        space_info->set_max_vlet_per_az(18);
        space_info->set_drop_normal_remain(15);
        request.mutable_space_info()->add_az_name("az1");

        SynchronizedClosure rpc_waiter;
        stub.create_space(_listen_addr, &request, &response, &rpc_waiter, NULL);
        rpc_waiter.wait();
        ASSERT_EQ(response.status().code(), AIE_OK);
    }
    //create space fail space exist.
    {
        aries::pb::CreateSpaceRequest request;
        aries::pb::AckResponse response;
        request.set_token("default_token");
        auto space_info = request.mutable_space_info();
        space_info->set_space_name("test_space1");
        space_info->set_ec_type(EC_RS_ISAL);
        space_info->set_n(18);
        space_info->set_k(9);
        space_info->set_put_quorum(9);
        space_info->set_delete_quorum(9);
        space_info->set_membership_quorum(6);
        space_info->set_allocator_collect_quorum(18);
        space_info->set_max_vlet_per_idc(18);
        space_info->set_max_vlet_per_rack(18);
        space_info->set_drop_normal_remain(12);
        request.mutable_space_info()->add_az_name("az1");

        SynchronizedClosure rpc_waiter;
        stub.create_space(_listen_addr, &request, &response, &rpc_waiter, NULL);
        rpc_waiter.wait();
        ASSERT_EQ(response.status().code(), AIE_EXIST);
    }
    //create space fail param not ilegal
    {
        aries::pb::CreateSpaceRequest request;
        aries::pb::AckResponse response;
        request.set_token("default_token");
        auto space_info = request.mutable_space_info();
        space_info->set_space_name("test_space2");
        space_info->set_ec_type(EC_RS_ISAL);
        space_info->set_n(18);
        space_info->set_k(9);
        space_info->set_membership_quorum(6);
        space_info->set_put_quorum(1);
        space_info->set_delete_quorum(9);
        space_info->set_allocator_collect_quorum(18);
        request.mutable_space_info()->add_az_name("az1");

        SynchronizedClosure rpc_waiter;
        stub.create_space(_listen_addr, &request, &response, &rpc_waiter, NULL);
        rpc_waiter.wait();
        ASSERT_EQ(response.status().code(), AIE_INVALID_ARGUMENT);
    }
}

TEST_F(ManageServiceTests, test_drop_space) {
    MasterStub stub(_listen_addr);
    //drop space fail drop space not allowed
    {
        FLAGS_enable_drop_space = false;
        aries::pb::DropSpaceRequest request;
        aries::pb::AckResponse response;
        request.set_token("default_token");
        request.set_space_name("test_space1");
        request.set_space_id(0);

        SynchronizedClosure rpc_waiter;
        stub.drop_space(_listen_addr, &request, &response, &rpc_waiter, NULL);
        rpc_waiter.wait();
        ASSERT_EQ(response.status().code(), AIE_FAIL);

    }
    //drop space succ
    {
        FLAGS_enable_drop_space = true;
        aries::pb::DropSpaceRequest request;
        aries::pb::AckResponse response;
        request.set_token("default_token");
        request.set_space_name("test_space1");
        request.set_space_id(0);

        SynchronizedClosure rpc_waiter;
        stub.drop_space(_listen_addr, &request, &response, &rpc_waiter, NULL);
        rpc_waiter.wait();
        ASSERT_EQ(response.status().code(), AIE_OK);
    }
    //drop space fail space not exist.
    {
        FLAGS_enable_drop_space = true;
        aries::pb::DropSpaceRequest request;
        aries::pb::AckResponse response;
        request.set_token("default_token");
        request.set_space_name("test_space");
        request.set_space_id(0);

        SynchronizedClosure rpc_waiter;
        stub.drop_space(_listen_addr, &request, &response, &rpc_waiter, NULL);
        rpc_waiter.wait();
        ASSERT_EQ(response.status().code(), AIE_NOT_EXIST);
    }
}

TEST_F(ManageServiceTests, test_reduce_space_redundancy) {
    MasterStub stub(_listen_addr);
    //reduce space redundancy succ.
    {
        auto space = g_space_manager->get_space("test_space1");
        VolumeVector volume_list;
        space->get_volume_list(&volume_list);
        for (auto& volume : volume_list) {
            volume->set_state(VOLUME_STATE_NORMAL);
            VletVector vlets;
            volume->get_vlet_list(&vlets);
            for (auto vlet : vlets) {
                vlet->set_state(VLET_STATE_NORMAL);
            }
        }

        aries::pb::ReduceSpaceRedundancyRequest request;
        aries::pb::AckResponse response;
        request.set_token("default_token");
        request.set_space_name("test_space1");
        request.set_space_id(1);
        request.set_n(1);

        SynchronizedClosure rpc_waiter;
        stub.reduce_space_redundancy(_listen_addr, &request, &response, &rpc_waiter, NULL);
        rpc_waiter.wait();
        ASSERT_EQ(response.status().code(), AIE_INVALID_ARGUMENT);
    }
    //reduce space fail due to volume dangerous.
    {
        auto space = g_space_manager->get_space("test_space1");
        VolumeVector volume_list;
        space->get_volume_list(&volume_list);
        for (auto& volume : volume_list) {
            volume->set_state(VOLUME_STATE_NORMAL);
            VletVector vlets;
            volume->get_vlet_list(&vlets);
            for (auto vlet : vlets) {
                vlet->set_state(VLET_STATE_CREATING);
            }
        }

        aries::pb::ReduceSpaceRedundancyRequest request;
        aries::pb::AckResponse response;
        request.set_token("default_token");
        request.set_space_name("test_space1");
        request.set_space_id(1);
        request.set_n(1);

        SynchronizedClosure rpc_waiter;
        stub.reduce_space_redundancy(_listen_addr, &request, &response, &rpc_waiter, NULL);
        rpc_waiter.wait();
        ASSERT_EQ(response.status().code(), AIE_INVALID_ARGUMENT);
    }
    //reduce space fail due to n err.
    {
        aries::pb::ReduceSpaceRedundancyRequest request;
        aries::pb::AckResponse response;
        request.set_token("default_token");
        request.set_space_name("test_space1");
        request.set_space_id(1);
        request.set_n(2);

        SynchronizedClosure rpc_waiter;
        stub.reduce_space_redundancy(_listen_addr, &request, &response, &rpc_waiter, NULL);
        rpc_waiter.wait();
        ASSERT_EQ(response.status().code(), AIE_INVALID_ARGUMENT);
    }
    //reduce space fail due to space state err.
    {
        auto space = g_space_manager->get_space("test_space1");
        space->set_state(SPACE_STATE_REDUCE_REDUNDENCY);

        aries::pb::ReduceSpaceRedundancyRequest request;
        aries::pb::AckResponse response;
        request.set_token("default_token");
        request.set_space_name("test_space1");
        request.set_space_id(1);
        request.set_n(1);

        SynchronizedClosure rpc_waiter;
        stub.reduce_space_redundancy(_listen_addr, &request, &response, &rpc_waiter, NULL);
        rpc_waiter.wait();
        ASSERT_EQ(response.status().code(), AIE_SPACE_NOT_READY);
    }
    //reduce space fail due to space not exist.
    {
        aries::pb::ReduceSpaceRedundancyRequest request;
        aries::pb::AckResponse response;
        request.set_token("default_token");
        request.set_space_name("test_space_err");
        request.set_space_id(1);
        request.set_n(1);

        SynchronizedClosure rpc_waiter;
        stub.reduce_space_redundancy(_listen_addr, &request, &response, &rpc_waiter, NULL);
        rpc_waiter.wait();
        ASSERT_EQ(response.status().code(), AIE_NOT_EXIST);
    }
}

TEST_F(ManageServiceTests, test_increase_space_redundancy) {
    MasterStub stub(_listen_addr);
    //increase space redundancy succ.
    {
        auto space = g_space_manager->get_space("test_space1");
        SpaceOptions space_opt = space->opt();
        space_opt.membership_quorum = 2;
        space_opt.drop_normal_remain = 2;
        space->update_opt(space_opt);

        VolumeVector volume_list;
        space->get_volume_list(&volume_list);
        for (auto& volume : volume_list) {
            volume->set_state(VOLUME_STATE_NORMAL);
            VletVector vlets;
            volume->get_vlet_list(&vlets);
            for (auto vlet : vlets) {
                vlet->set_state(VLET_STATE_NORMAL);
            }
        }

        aries::pb::IncreaseSpaceRedundancyRequest request;
        aries::pb::AckResponse response;
        request.set_token("default_token");
        request.set_space_name("test_space1");
        request.set_space_id(1);
        request.set_n(3);

        SynchronizedClosure rpc_waiter;
        stub.increase_space_redundancy(_listen_addr, &request, &response, &rpc_waiter, NULL);
        rpc_waiter.wait();
        ASSERT_EQ(response.status().code(), AIE_OK);
    }
    //reduce space redundancy space params illegal.
    {
        auto space = g_space_manager->get_space("test_space1");
        SpaceOptions space_opt = space->opt();
        space_opt.membership_quorum = 2;
        space_opt.drop_normal_remain = 1;
        space->update_opt(space_opt);

        VolumeVector volume_list;
        space->get_volume_list(&volume_list);
        for (auto& volume : volume_list) {
            volume->set_state(VOLUME_STATE_NORMAL);
            VletVector vlets;
            volume->get_vlet_list(&vlets);
            for (auto vlet : vlets) {
                vlet->set_state(VLET_STATE_NORMAL);
            }
        }

        aries::pb::IncreaseSpaceRedundancyRequest request;
        aries::pb::AckResponse response;
        request.set_token("default_token");
        request.set_space_name("test_space1");
        request.set_space_id(1);
        request.set_n(3);

        SynchronizedClosure rpc_waiter;
        stub.increase_space_redundancy(_listen_addr, &request, &response, &rpc_waiter, NULL);
        rpc_waiter.wait();
        ASSERT_EQ(response.status().code(), AIE_INVALID_ARGUMENT);
    }
    //increase space fail due to n err.
    {
        aries::pb::IncreaseSpaceRedundancyRequest request;
        aries::pb::AckResponse response;
        request.set_token("default_token");
        request.set_space_name("test_space1");
        request.set_space_id(1);
        request.set_n(2);

        SynchronizedClosure rpc_waiter;
        stub.increase_space_redundancy(_listen_addr, &request, &response, &rpc_waiter, NULL);
        rpc_waiter.wait();
        ASSERT_EQ(response.status().code(), AIE_INVALID_ARGUMENT);
    }
    //increase space fail due to space state err.
    {
        auto space = g_space_manager->get_space("test_space1");
        space->set_state(SPACE_STATE_REDUCE_REDUNDENCY);

        aries::pb::IncreaseSpaceRedundancyRequest request;
        aries::pb::AckResponse response;
        request.set_token("default_token");
        request.set_space_name("test_space1");
        request.set_space_id(1);
        request.set_n(1);

        SynchronizedClosure rpc_waiter;
        stub.increase_space_redundancy(_listen_addr, &request, &response, &rpc_waiter, NULL);
        rpc_waiter.wait();
        ASSERT_EQ(response.status().code(), AIE_SPACE_NOT_READY);
    }
    //increase space fail due to space not exist.
    {
        aries::pb::IncreaseSpaceRedundancyRequest request;
        aries::pb::AckResponse response;
        request.set_token("default_token");
        request.set_space_name("test_space_err");
        request.set_space_id(1);
        request.set_n(1);

        SynchronizedClosure rpc_waiter;
        stub.increase_space_redundancy(_listen_addr, &request, &response, &rpc_waiter, NULL);
        rpc_waiter.wait();
        ASSERT_EQ(response.status().code(), AIE_NOT_EXIST);
    }
}

TEST_F(ManageServiceTests, test_compact_space) {
    MasterStub stub(_listen_addr);
    // prepare
    auto space = g_space_manager->get_space("test_space1");
    space->set_permit_compact_balance(false);
    space->set_compact_state(NO);
    //compact space fail due to space state err.
    {
        auto space = g_space_manager->get_space("test_space1");
        space->set_state(SPACE_STATE_REDUCE_REDUNDENCY);

        aries::pb::CompactSpaceRequest request;
        aries::pb::AckResponse response;
        request.set_token("default_token");
        request.set_space_name("test_space1");
        request.set_space_id(1);
        request.set_permit_compact_balance(true);

        SynchronizedClosure rpc_waiter;
        stub.compact_space(_listen_addr, &request, &response, &rpc_waiter, NULL);
        rpc_waiter.wait();
        ASSERT_EQ(response.status().code(), AIE_SPACE_NOT_READY);
    }
    //compact space fail due to space not exist.
    {
        aries::pb::CompactSpaceRequest request;
        aries::pb::AckResponse response;
        request.set_token("default_token");
        request.set_space_name("test_space_err");
        request.set_space_id(1);
        request.set_permit_compact_balance(true);

        SynchronizedClosure rpc_waiter;
        stub.compact_space(_listen_addr, &request, &response, &rpc_waiter, NULL);
        rpc_waiter.wait();
        ASSERT_EQ(response.status().code(), AIE_NOT_EXIST);
    }
    //compact space fail due to space id is error.
    {
        aries::pb::CompactSpaceRequest request;
        aries::pb::AckResponse response;
        request.set_token("default_token");
        request.set_space_name("test_space1");
        request.set_space_id(2);
        request.set_permit_compact_balance(true);

        SynchronizedClosure rpc_waiter;
        stub.compact_space(_listen_addr, &request, &response, &rpc_waiter, NULL);
        rpc_waiter.wait();
        ASSERT_EQ(response.status().code(), AIE_FAIL);
    }
    //compact space fail due to space compact state error
    {
        space->set_compact_state(RUN);
        aries::pb::CompactSpaceRequest request;
        aries::pb::AckResponse response;
        request.set_token("default_token");
        request.set_space_name("test_space1");
        request.set_space_id(1);
        request.set_permit_compact_balance(true);

        SynchronizedClosure rpc_waiter;
        stub.compact_space(_listen_addr, &request, &response, &rpc_waiter, NULL);
        rpc_waiter.wait();
        ASSERT_EQ(response.status().code(), AIE_SPACE_NOT_READY);
        space->set_compact_state(NO);
    }
    //compact space fail due to permit_compact_balance error
    {
        auto space = g_space_manager->get_space("test_space1");
        space->set_state(SPACE_STATE_NORMAL);
        aries::pb::CompactSpaceRequest request;
        aries::pb::AckResponse response;
        request.set_token("default_token");
        request.set_space_name("test_space1");
        request.set_space_id(1);
        request.set_permit_compact_balance(false);

        SynchronizedClosure rpc_waiter;
        stub.compact_space(_listen_addr, &request, &response, &rpc_waiter, NULL);
        rpc_waiter.wait();
        ASSERT_EQ(response.status().code(), AIE_INVALID_ARGUMENT);
    }
    //compact space succ.
    {
        aries::pb::CompactSpaceRequest request;
        aries::pb::AckResponse response;
        request.set_token("default_token");
        request.set_space_name("test_space1");
        request.set_space_id(1);
        request.set_permit_compact_balance(true);

        SynchronizedClosure rpc_waiter;
        stub.compact_space(_listen_addr, &request, &response, &rpc_waiter, NULL);
        rpc_waiter.wait();
        ASSERT_EQ(response.status().code(), AIE_OK);
    }
    // end
    space->set_permit_compact_balance(false);
    space->set_compact_state(NO);
}

TEST_F(ManageServiceTests, test_cancel_compact_space) {
    MasterStub stub(_listen_addr);
    // prepare
    auto space = g_space_manager->get_space("test_space1");
    space->set_state(SPACE_STATE_COMPACT);
    space->set_permit_compact_balance(true);
    space->set_compact_state(RUN);
    //cancel compact space succ.
    {
        space->set_compact_state(RUN);
        ASSERT_EQ(space->compact_state(), RUN);
        aries::pb::CompactSpaceRequest request;
        aries::pb::AckResponse response;
        request.set_token("default_token");
        request.set_space_name("test_space1");
        request.set_space_id(1);

        SynchronizedClosure rpc_waiter;
        stub.cancel_compact_space(_listen_addr, &request, &response, &rpc_waiter, NULL);
        rpc_waiter.wait();
        ASSERT_EQ(response.status().code(), AIE_OK);
    }
    //cancel compact space fail due to space state err.
    {
        auto space = g_space_manager->get_space("test_space1");
        space->set_state(SPACE_STATE_NORMAL);

        aries::pb::CompactSpaceRequest request;
        aries::pb::AckResponse response;
        request.set_token("default_token");
        request.set_space_name("test_space1");
        request.set_space_id(1);

        SynchronizedClosure rpc_waiter;
        stub.cancel_compact_space(_listen_addr, &request, &response, &rpc_waiter, NULL);
        rpc_waiter.wait();
        ASSERT_EQ(response.status().code(), AIE_SPACE_NOT_READY);
    }
    //cancel compact space fail due to space not exist.
    {
        aries::pb::CompactSpaceRequest request;
        aries::pb::AckResponse response;
        request.set_token("default_token");
        request.set_space_name("test_space_err");
        request.set_space_id(1);

        SynchronizedClosure rpc_waiter;
        stub.cancel_compact_space(_listen_addr, &request, &response, &rpc_waiter, NULL);
        rpc_waiter.wait();
        ASSERT_EQ(response.status().code(), AIE_NOT_EXIST);
    }
    //cancel compact space fail due to space id is error.
    {
        aries::pb::CompactSpaceRequest request;
        aries::pb::AckResponse response;
        request.set_token("default_token");
        request.set_space_name("test_space1");
        request.set_space_id(2);

        SynchronizedClosure rpc_waiter;
        stub.cancel_compact_space(_listen_addr, &request, &response, &rpc_waiter, NULL);
        rpc_waiter.wait();
        ASSERT_EQ(response.status().code(), AIE_FAIL);
    }
    //cancel compact space fail due to space compact state error.
    {
        space->set_compact_state(NO);
        aries::pb::CompactSpaceRequest request;
        aries::pb::AckResponse response;
        request.set_token("default_token");
        request.set_space_name("test_space1");
        request.set_space_id(1);

        SynchronizedClosure rpc_waiter;
        stub.cancel_compact_space(_listen_addr, &request, &response, &rpc_waiter, NULL);
        rpc_waiter.wait();
        ASSERT_EQ(response.status().code(), AIE_SPACE_NOT_READY);
    }
    // end
    space->set_state(SPACE_STATE_NORMAL);
    space->set_permit_compact_balance(false);
    space->set_compact_state(NO);
}

TEST_F(ManageServiceTests, test_control_compact_space) {
    MasterStub stub(_listen_addr);
    // prepare
    auto space = g_space_manager->get_space("test_space1");
    space->set_state(SPACE_STATE_COMPACT);
    space->set_permit_compact_balance(true);
    space->set_compact_state(RUN);
    //control compact space fail due to space not exist.
    {
        aries::pb::CompactSpaceRequest request;
        aries::pb::AckResponse response;
        request.set_token("default_token");
        request.set_space_name("test_space_err");
        request.set_space_id(1);
        request.set_permit_compact_balance(false);

        SynchronizedClosure rpc_waiter;
        stub.control_compact_space(_listen_addr, &request, &response, &rpc_waiter, NULL);
        rpc_waiter.wait();
        ASSERT_EQ(response.status().code(), AIE_NOT_EXIST);
    }
    //control compact space fail due to space id is error.
    {
        aries::pb::CompactSpaceRequest request;
        aries::pb::AckResponse response;
        request.set_token("default_token");
        request.set_space_name("test_space1");
        request.set_space_id(2);
        request.set_permit_compact_balance(false);

        SynchronizedClosure rpc_waiter;
        stub.control_compact_space(_listen_addr, &request, &response, &rpc_waiter, NULL);
        rpc_waiter.wait();
        ASSERT_EQ(response.status().code(), AIE_FAIL);
    }
    //control compact space fail due to space compact state is error.
    {
        space->set_compact_state(NO);
        aries::pb::CompactSpaceRequest request;
        aries::pb::AckResponse response;
        request.set_token("default_token");
        request.set_space_name("test_space1");
        request.set_space_id(1);
        request.set_permit_compact_balance(false);

        SynchronizedClosure rpc_waiter;
        stub.control_compact_space(_listen_addr, &request, &response, &rpc_waiter, NULL);
        rpc_waiter.wait();
        ASSERT_EQ(response.status().code(), AIE_SPACE_NOT_READY);
    }
    //control compact space succ pause.
    {
        space->set_compact_state(RUN);
        aries::pb::CompactSpaceRequest request;
        aries::pb::AckResponse response;
        request.set_token("default_token");
        request.set_space_name("test_space1");
        request.set_space_id(1);
        request.set_permit_compact_balance(false);

        SynchronizedClosure rpc_waiter;
        stub.control_compact_space(_listen_addr, &request, &response, &rpc_waiter, NULL);
        rpc_waiter.wait();
        ASSERT_EQ(response.status().code(), AIE_OK);
    }
    //control compact space succ restart.
    {
        space->set_permit_compact_balance(false);
        aries::pb::CompactSpaceRequest request;
        aries::pb::AckResponse response;
        request.set_token("default_token");
        request.set_space_name("test_space1");
        request.set_space_id(1);
        request.set_permit_compact_balance(true);

        SynchronizedClosure rpc_waiter;
        stub.control_compact_space(_listen_addr, &request, &response, &rpc_waiter, NULL);
        rpc_waiter.wait();
        ASSERT_EQ(response.status().code(), AIE_OK);
    }
    // end
    space->set_state(SPACE_STATE_NORMAL);
    space->set_permit_compact_balance(false);
    space->set_compact_state(NO);
}

TEST_F(ManageServiceTests, test_finish_compact_space) {
    MasterStub stub(_listen_addr);
    // prepare
    auto space = g_space_manager->get_space("test_space1");
    space->set_state(SPACE_STATE_COMPACT);
    space->set_permit_compact_balance(true);
    space->set_compact_state(RUN);
    VolumeVector volume_list;
    space->get_volume_list(&volume_list);
    for (auto& volume : volume_list) {
        volume->set_compact_progress(END); 
    }
    //update space meta fail due to space not exist.
    {
        aries::pb::CompactSpaceRequest request;
        aries::pb::AckResponse response;
        request.set_token("default_token");
        request.set_space_name("test_space_err");
        request.set_space_id(1);
        request.set_permit_compact_balance(false);

        SynchronizedClosure rpc_waiter;
        stub.finish_compact_space(_listen_addr, &request, &response, &rpc_waiter, NULL);
        rpc_waiter.wait();
        ASSERT_EQ(response.status().code(), AIE_NOT_EXIST);
    }
    //update space meta fail due to space id is error.
    {
        aries::pb::CompactSpaceRequest request;
        aries::pb::AckResponse response;
        request.set_token("default_token");
        request.set_space_name("test_space1");
        request.set_space_id(2);
        request.set_permit_compact_balance(false);

        SynchronizedClosure rpc_waiter;
        stub.finish_compact_space(_listen_addr, &request, &response, &rpc_waiter, NULL);
        rpc_waiter.wait();
        ASSERT_EQ(response.status().code(), AIE_FAIL);
    }
    //update space meta fail due to space compact state is error.
    {
        space->set_compact_state(NO);
        aries::pb::CompactSpaceRequest request;
        aries::pb::AckResponse response;
        request.set_token("default_token");
        request.set_space_name("test_space1");
        request.set_space_id(1);
        request.set_permit_compact_balance(false);

        SynchronizedClosure rpc_waiter;
        stub.finish_compact_space(_listen_addr, &request, &response, &rpc_waiter, NULL);
        rpc_waiter.wait();
        ASSERT_EQ(response.status().code(), AIE_SPACE_NOT_READY);
        space->set_compact_state(RUN);
    }
    //update space meta fail due to volume compact state is not END.
    {
        aries::pb::CompactSpaceRequest request;
        aries::pb::AckResponse response;
        request.set_token("default_token");
        request.set_space_name("test_space1");
        request.set_space_id(1);
        request.set_permit_compact_balance(false);
        volume_list[1]->set_compact_progress(RUNNING);
        
        SynchronizedClosure rpc_waiter;
        stub.finish_compact_space(_listen_addr, &request, &response, &rpc_waiter, NULL);
        rpc_waiter.wait();
        ASSERT_EQ(response.status().code(), AIE_FAIL);
        volume_list[1]->set_compact_progress(END);
    }
    //update space meta succ.
    {
        aries::pb::CompactSpaceRequest request;
        aries::pb::AckResponse response;
        request.set_token("default_token");
        request.set_space_name("test_space1");
        request.set_space_id(1);
        request.set_permit_compact_balance(false);
 
        SynchronizedClosure rpc_waiter;
        stub.finish_compact_space(_listen_addr, &request, &response, &rpc_waiter, NULL);
        rpc_waiter.wait();
        ASSERT_EQ(response.status().code(), AIE_OK);
    }
    // end
    space->set_state(SPACE_STATE_NORMAL);
    space->set_permit_compact_balance(false);
    space->set_compact_state(NO);
    for (auto& volume : volume_list) {
        volume->set_compact_progress(NONE); 
    }
}

TEST_F(ManageServiceTests, test_update_volume_compact_progress) {
    MasterStub stub(_listen_addr);
    // prepare
    auto space = g_space_manager->get_space("test_space1");
    space->set_state(SPACE_STATE_COMPACT);
    space->set_permit_compact_balance(true);
    space->set_compact_state(RUN);
    auto volume1 = g_volume_manager->get_volume(1);
    //update volume meta fail due to volume not exist.
    {
        aries::pb::UpdateVolumeCompactRequest request;
        aries::pb::AckResponse response;
        request.set_token("default_token");
        request.set_volume_id(10);
        request.set_volume_state(VOLUME_STATE_READONLY);
        request.set_compact_progress(RUNNING);
        request.set_compact_vlet_size(28 * aries::common::GB);
 
        SynchronizedClosure rpc_waiter;
        stub.update_volume_compact_progress(_listen_addr, &request, &response, &rpc_waiter, NULL);
        rpc_waiter.wait();
        ASSERT_EQ(response.status().code(), AIE_NOT_EXIST);
    }
    //update volume meta fail due to volume state error.
    {
        volume1->set_state(VOLUME_STATE_DROPPED);
        aries::pb::UpdateVolumeCompactRequest request;
        aries::pb::AckResponse response;
        request.set_token("default_token");
        request.set_volume_id(1);
        request.set_volume_state(VOLUME_STATE_READONLY);
        request.set_compact_progress(RUNNING);
        request.set_compact_vlet_size(28 * aries::common::GB);
        
        SynchronizedClosure rpc_waiter;
        stub.update_volume_compact_progress(_listen_addr, &request, &response, &rpc_waiter, NULL);
        rpc_waiter.wait();
        ASSERT_EQ(response.status().code(), AIE_FAIL);
        volume1->set_state(VOLUME_STATE_NORMAL);
    }
    //update volume meta fail due to request volume state is error.
    {
        aries::pb::UpdateVolumeCompactRequest request;
        aries::pb::AckResponse response;
        request.set_token("default_token");
        request.set_volume_id(1);
        request.set_volume_state(VOLUME_STATE_CREATING);
        request.set_compact_progress(RUNNING);
        request.set_compact_vlet_size(28 * aries::common::GB);
 
        SynchronizedClosure rpc_waiter;
        stub.update_volume_compact_progress(_listen_addr, &request, &response, &rpc_waiter, NULL);
        rpc_waiter.wait();
        ASSERT_EQ(response.status().code(), AIE_FAIL);
    }
    //update volume meta running succ.
    {
        aries::pb::UpdateVolumeCompactRequest request;
        aries::pb::AckResponse response;
        request.set_token("default_token");
        request.set_volume_id(1);
        request.set_volume_state(VOLUME_STATE_READONLY);
        request.set_compact_progress(RUNNING);
        request.set_compact_vlet_size(28 * aries::common::GB);
 
        SynchronizedClosure rpc_waiter;
        stub.update_volume_compact_progress(_listen_addr, &request, &response, &rpc_waiter, NULL);
        rpc_waiter.wait();
        ASSERT_EQ(response.status().code(), AIE_OK);
    }
    //update volume meta end failed by some volume compact_vlet_size error.
    {
        volume1->set_compact_vlet_size(0);
        aries::pb::UpdateVolumeCompactRequest request;
        aries::pb::AckResponse response;
        request.set_token("default_token");
        request.set_volume_id(1);
        request.set_volume_state(VOLUME_STATE_NORMAL);
        request.set_compact_progress(END);
        request.set_compact_vlet_size(28 * aries::common::GB);
 
        SynchronizedClosure rpc_waiter;
        stub.update_volume_compact_progress(_listen_addr, &request, &response, &rpc_waiter, NULL);
        rpc_waiter.wait();
        ASSERT_EQ(response.status().code(), AIE_INVALID_ARGUMENT);
    }
    //update volume meta end failed by some vlet_size error.
    {
        volume1->set_compact_vlet_size(28 * aries::common::GB);
        VletVector vlets;
        volume1->get_vlet_list(&vlets);
        for (auto vlet : vlets) {
            vlet->vlet_engine_info_ptr()->set_vlet_size(0);
        }

        aries::pb::UpdateVolumeCompactRequest request;
        aries::pb::AckResponse response;
        request.set_token("default_token");
        request.set_volume_id(1);
        request.set_volume_state(VOLUME_STATE_NORMAL);
        request.set_compact_progress(END);
        request.set_compact_vlet_size(28 * aries::common::GB);
 
        SynchronizedClosure rpc_waiter;
        stub.update_volume_compact_progress(_listen_addr, &request, &response, &rpc_waiter, NULL);
        rpc_waiter.wait();
        ASSERT_EQ(response.status().code(), AIE_FAIL);
    }
    //update volume meta end succ.
    {
        volume1->set_compact_vlet_size(28 * aries::common::GB);
        VletVector vlets;
        volume1->get_vlet_list(&vlets);
        for (auto vlet : vlets) {
            vlet->vlet_engine_info_ptr()->set_vlet_size(28 * aries::common::GB);
        }

        aries::pb::UpdateVolumeCompactRequest request;
        aries::pb::AckResponse response;
        request.set_token("default_token");
        request.set_volume_id(1);
        request.set_volume_state(VOLUME_STATE_NORMAL);
        request.set_compact_progress(END);
        request.set_compact_vlet_size(28 * aries::common::GB);
 
        SynchronizedClosure rpc_waiter;
        stub.update_volume_compact_progress(_listen_addr, &request, &response, &rpc_waiter, NULL);
        rpc_waiter.wait();
        ASSERT_EQ(response.status().code(), AIE_OK);
    }
    // end
    space->set_state(SPACE_STATE_NORMAL);
    space->set_permit_compact_balance(false);
    space->set_compact_state(NO);
    volume1->set_compact_vlet_size(0);
    VletVector vlets;
    volume1->get_vlet_list(&vlets);
    for (auto vlet : vlets) {
        vlet->vlet_engine_info_ptr()->set_vlet_size(32 * aries::common::GB);
    }
}

TEST_F(ManageServiceTests, test_update_space) {
    MasterStub stub(_listen_addr);
    //update space fail, space not exist.
    {
        aries::pb::UpdateSpaceRequest request;
        aries::pb::AckResponse response;
        request.set_token("default_token");
        auto space_info = request.mutable_space_info();
        space_info->set_space_name("test_space");
        space_info->set_ec_type(EC_RS_ISAL);
        space_info->set_n(18);
        space_info->set_k(9);
        space_info->set_put_quorum(9);
        space_info->set_delete_quorum(9);
        space_info->set_membership_quorum(6);
        space_info->set_allocator_collect_quorum(18);
        request.mutable_space_info()->add_az_name("az1");

        SynchronizedClosure rpc_waiter;
        stub.update_space(_listen_addr, &request, &response, &rpc_waiter, NULL);
        rpc_waiter.wait();
        ASSERT_EQ(response.status().code(), AIE_NOT_EXIST);
    }
    //update space fail, because cant update const argument.
    {
        aries::pb::UpdateSpaceRequest request;
        aries::pb::AckResponse response;
        request.set_token("default_token");
        auto space_info = request.mutable_space_info();
        space_info->set_space_name("test_space1");
        space_info->set_ec_type(EC_RS_ISAL);
        space_info->set_n(18);
        space_info->set_k(9);
        space_info->set_put_quorum(9);
        space_info->set_membership_quorum(6);
        space_info->set_delete_quorum(9);
        space_info->set_allocator_collect_quorum(18);
        request.mutable_space_info()->add_az_name("az1");

        SynchronizedClosure rpc_waiter;
        stub.update_space(_listen_addr, &request, &response, &rpc_waiter, NULL);
        rpc_waiter.wait();
        ASSERT_EQ(response.status().code(), AIE_INVALID_ARGUMENT);
    }
    //update space succ.
    {
        aries::pb::UpdateSpaceRequest request;
        aries::pb::AckResponse response;
        request.set_token("default_token");
        auto space_info = request.mutable_space_info();
        space_info->set_space_name("test_space167");
        space_info->set_ec_type(EC_RS_ISAL);
        space_info->set_vlet_type(VLET_TYPE_LINKED_32G_4M_512K);
        space_info->set_n(18);
        space_info->set_k(9);
        space_info->set_membership_quorum(11);
        space_info->set_put_quorum(15);
        space_info->set_delete_quorum(9);
        space_info->set_allocator_collect_quorum(17);
        space_info->set_max_vlet_per_idc(18);
        space_info->set_max_vlet_per_rack(18);
        space_info->set_drop_normal_remain(15);
        request.mutable_space_info()->add_az_name("az1");

        SynchronizedClosure rpc_waiter;
        stub.update_space(_listen_addr, &request, &response, &rpc_waiter, NULL);
        rpc_waiter.wait();
        ASSERT_EQ(response.status().code(), AIE_OK);
    }
}

TEST_F(ManageServiceTests, test_create_volumes) {
    MasterStub stub(_listen_addr);

    //create volume fail, space not exist.
    {
        aries::pb::CreateVolumesRequest request;
        aries::pb::AckResponse response;
        request.set_token("default_token");
        request.set_space_name("test_space");
        request.set_volume_num(1);

        SynchronizedClosure rpc_waiter;
        stub.create_volumes(_listen_addr, &request, &response, &rpc_waiter, NULL);
        rpc_waiter.wait();
        ASSERT_EQ(response.status().code(), AIE_NOT_EXIST);
    }
    //create volume suc
    {
        aries::pb::CreateVolumesRequest request;
        aries::pb::AckResponse response;
        request.set_token("default_token");
        request.set_space_name("test_space1");
        request.set_volume_num(1);

        ((MockMasterControl*)g_master_control)->_is_call = false;

        SynchronizedClosure rpc_waiter;
        FLAGS_delay_allocator_recollect_second = 3;
        stub.create_volumes(_listen_addr, &request, &response, &rpc_waiter, NULL);
        rpc_waiter.wait();
        ASSERT_EQ(response.status().code(), AIE_OK);
        {
            usleep(2 * 1000 * 1000);
            auto is_call = ((MockMasterControl*)g_master_control)->_is_call;
            ASSERT_FALSE(is_call);
        }
        {
            usleep(1.5 * 1000 * 1000);
            auto is_call = ((MockMasterControl*)g_master_control)->_is_call;
            ASSERT_TRUE(is_call);
        }
    }
    //create volume fail, space state err
    {
        auto space = g_space_manager->get_space("test_space1");
        space->set_state(SPACE_STATE_REDUCE_REDUNDENCY);

        aries::pb::CreateVolumesRequest request;
        aries::pb::AckResponse response;
        request.set_token("default_token");
        request.set_space_name("test_space1");
        request.set_volume_num(1);

        SynchronizedClosure rpc_waiter;
        stub.create_volumes(_listen_addr, &request, &response, &rpc_waiter, NULL);
        rpc_waiter.wait();
        ASSERT_EQ(response.status().code(), AIE_SPACE_NOT_READY);
    }
}

TEST_F(ManageServiceTests, test_drop_volume) {
    //drop fail .volume not exist
    {
        aries::pb::VolumeRequest request;
        aries::pb::AckResponse response;
        request.set_token("default_token");
        request.set_volume_id(3);

        baidu::rpc::Controller cntl;
        baidu::rpc::Channel channel;
        int ret = channel.Init(_listen_addr, NULL);
        aries::pb::MasterManageService_Stub stub(&channel);

        stub.drop_volume(&cntl, &request, &response, NULL);
        ASSERT_EQ(response.status().code(), AIE_FAIL);
    }

    //drop succ
    {
        aries::pb::VolumeRequest request;
        aries::pb::AckResponse response;
        request.set_token("default_token");
        request.set_volume_id(3);
        auto volume = g_volume_manager->get_volume(3);
        volume->set_state(VOLUME_STATE_READONLY);

        baidu::rpc::Controller cntl;
        baidu::rpc::Channel channel;
        int ret = channel.Init(_listen_addr, NULL);
        aries::pb::MasterManageService_Stub stub(&channel);

        stub.drop_volume(&cntl, &request, &response, NULL);
        ASSERT_EQ(response.status().code(), AIE_OK);
    }
    //drop succ
    {
        aries::pb::VolumeRequest request;
        aries::pb::AckResponse response;
        request.set_token("default_token");
        request.set_volume_id(3);
        auto volume = g_volume_manager->get_volume(3);
        volume->set_state(VOLUME_STATE_CREATING);

        baidu::rpc::Controller cntl;
        baidu::rpc::Channel channel;
        int ret = channel.Init(_listen_addr, NULL);
        aries::pb::MasterManageService_Stub stub(&channel);

        stub.drop_volume(&cntl, &request, &response, NULL);
        ASSERT_EQ(response.status().code(), AIE_OK);
    }
}

TEST_F(ManageServiceTests, test_enable_volume) {
    //volume not exist
    {
        aries::pb::VolumeRequest request;
        aries::pb::AckResponse response;
        request.set_token("default_token");
        request.set_volume_id(10086);

        baidu::rpc::Controller cntl;
        baidu::rpc::Channel channel;
        int ret = channel.Init(_listen_addr, NULL);
        aries::pb::MasterManageService_Stub stub(&channel);

        stub.enable_volume(&cntl, &request, &response, NULL);
        ASSERT_EQ(response.status().code(), AIE_NOT_EXIST);
    }

    //enable succ
    {
        aries::pb::VolumeRequest request;
        aries::pb::AckResponse response;
        request.set_token("default_token");
        request.set_volume_id(3);

        auto volume = g_volume_manager->get_volume(3);
        volume->set_state(VOLUME_STATE_READONLY);

        baidu::rpc::Controller cntl;
        baidu::rpc::Channel channel;
        int ret = channel.Init(_listen_addr, NULL);
        aries::pb::MasterManageService_Stub stub(&channel);

        stub.enable_volume(&cntl, &request, &response, NULL);
        ASSERT_EQ(response.status().code(), AIE_OK);
    }

    //volume READONLY
    {
        aries::pb::VolumeRequest request;
        aries::pb::AckResponse response;
        request.set_token("default_token");
        request.set_volume_id(3);

        auto volume = g_volume_manager->get_volume(3);
        volume->set_state(VOLUME_STATE_NORMAL);

        baidu::rpc::Controller cntl;
        baidu::rpc::Channel channel;
        int ret = channel.Init(_listen_addr, NULL);
        aries::pb::MasterManageService_Stub stub(&channel);

        stub.enable_volume(&cntl, &request, &response, NULL);
        ASSERT_EQ(response.status().code(), AIE_FAIL);
    }
}

TEST_F(ManageServiceTests, test_disable_volume) {
    //drop fail .volume already disable
    {
        aries::pb::VolumeRequest request;
        aries::pb::AckResponse response;
        request.set_token("default_token");
        request.set_volume_id(3);

        auto volume = g_volume_manager->get_volume(3);
        volume->set_state(VOLUME_STATE_READONLY);

        baidu::rpc::Controller cntl;
        baidu::rpc::Channel channel;
        int ret = channel.Init(_listen_addr, NULL);
        aries::pb::MasterManageService_Stub stub(&channel);

        stub.disable_volume(&cntl, &request, &response, NULL);
        ASSERT_EQ(response.status().code(), AIE_FAIL);
    }

    //drop succ
    {
        aries::pb::VolumeRequest request;
        aries::pb::AckResponse response;
        request.set_token("default_token");
        request.set_volume_id(3);

        auto volume = g_volume_manager->get_volume(3);
        volume->set_state(VOLUME_STATE_NORMAL);

        baidu::rpc::Controller cntl;
        baidu::rpc::Channel channel;
        int ret = channel.Init(_listen_addr, NULL);
        aries::pb::MasterManageService_Stub stub(&channel);

        stub.disable_volume(&cntl, &request, &response, NULL);
        ASSERT_EQ(response.status().code(), AIE_OK);
    }
}

TEST_F(ManageServiceTests, test_permit_drop_volume) {
    //volume not exist.
    {
        std::string err;
        auto ret = ManageServiceImpl::permit_drop_volume(3310, &err);
        ASSERT_EQ(ret, false);
        ASSERT_EQ(err, "volume not exist");
    }
    {
        std::string err;
        auto ret = ManageServiceImpl::permit_drop_volume(3, &err);
        ASSERT_EQ(ret, false);
        ASSERT_EQ(err, "volume state err");
    }
    {
        std::string err;
        auto volume = g_volume_manager->get_volume(3);
        auto space = volume->space();
        volume->set_state(VOLUME_STATE_READONLY);
        space->set_state(SPACE_STATE_REDUCE_REDUNDENCY);
        auto ret = ManageServiceImpl::permit_drop_volume(3, &err);
        ASSERT_EQ(ret, false);
        ASSERT_EQ(err, "space state not normal");
    }
    //succ
    {
        std::string err;
        auto volume = g_volume_manager->get_volume(3);
        auto space = volume->space();
        volume->set_state(VOLUME_STATE_READONLY);
        space->set_state(SPACE_STATE_NORMAL);
        auto vlet = volume->_vlet_vector[0];
        vlet->set_state(VLET_STATE_REPAIRING);
        auto ret = ManageServiceImpl::permit_drop_volume(3, &err);
        ASSERT_EQ(ret, true);
    }
}

TEST_F(ManageServiceTests, test_drop_volume1) {
    //drop fail .volume not exist
    {
        aries::pb::VolumeRequest request;
        aries::pb::AckResponse response;
        request.set_token("default_token");
        request.set_volume_id(399);

        baidu::rpc::Controller cntl;
        baidu::rpc::Channel channel;
        int ret = channel.Init(_listen_addr, NULL);
        aries::pb::MasterManageService_Stub stub(&channel);

        stub.drop_volume(&cntl, &request, &response, NULL);
        ASSERT_EQ(response.status().code(), AIE_FAIL);
    }

    //drop succ
    {
        aries::pb::VolumeRequest request;
        aries::pb::AckResponse response;
        request.set_token("default_token");
        request.set_volume_id(3);

        auto volume = g_volume_manager->get_volume(3);
        auto space = volume->space();
        volume->set_state(VOLUME_STATE_READONLY);

        baidu::rpc::Controller cntl;
        baidu::rpc::Channel channel;
        int ret = channel.Init(_listen_addr, NULL);
        aries::pb::MasterManageService_Stub stub(&channel);

        stub.drop_volume(&cntl, &request, &response, NULL);
        ASSERT_EQ(response.status().code(), AIE_OK);
    }
}

TEST_F(ManageServiceTests, test_drop_vlet) {
    //drop fail .volume not exist
    {
        aries::pb::MasterDropVletRequest request;
        aries::pb::AckResponse response;

        request.set_token("default_token");
        auto vlet_info = request.mutable_vlet_info();
        auto addr = _p_meta->get_node_addr(3);
        vlet_info->set_node_addr(common::endpoint2int(addr));
        vlet_info->set_volume_id(8877);
        vlet_info->set_shard_index(0);
        vlet_info->set_vlet_version(0);

        baidu::rpc::Controller cntl;
        baidu::rpc::Channel channel;
        int ret = channel.Init(_listen_addr, NULL);
        aries::pb::MasterManageService_Stub stub(&channel);
        stub.drop_vlet(&cntl, &request, &response, NULL);

        ASSERT_EQ(response.status().code(), AIE_NOT_EXIST);
    }

    //drop fail .vlet not exist
    {
        aries::pb::MasterDropVletRequest request;
        aries::pb::AckResponse response;

        request.set_token("default_token");
        auto vlet_info = request.mutable_vlet_info();
        auto addr = _p_meta->get_node_addr(3);
        vlet_info->set_node_addr(common::endpoint2int(addr));
        vlet_info->set_volume_id(3);
        vlet_info->set_shard_index(9);
        vlet_info->set_vlet_version(0);

        baidu::rpc::Controller cntl;
        baidu::rpc::Channel channel;
        int ret = channel.Init(_listen_addr, NULL);
        aries::pb::MasterManageService_Stub stub(&channel);
        stub.drop_vlet(&cntl, &request, &response, NULL);

        ASSERT_EQ(response.status().code(), AIE_NOT_EXIST);
    }

    //drop fail. node not exist.
    {
        aries::pb::MasterDropVletRequest request;
        aries::pb::AckResponse response;


        request.set_token("default_token");
        auto vlet_info = request.mutable_vlet_info();
        auto addr = _p_meta->get_node_addr(0);
        vlet_info->set_node_addr(common::endpoint2int(addr));
        vlet_info->set_volume_id(1);
        vlet_info->set_shard_index(0);
        vlet_info->set_vlet_version(0);

        baidu::rpc::Controller cntl;
        baidu::rpc::Channel channel;
        int ret = channel.Init(_listen_addr, NULL);
        aries::pb::MasterManageService_Stub stub(&channel);
        stub.drop_vlet(&cntl, &request, &response, NULL);

        ASSERT_EQ(response.status().code(), AIE_NOT_EXIST);
    }
    //drop fail node exist ,but vlet not exist.
    {
        aries::pb::MasterDropVletRequest request;
        aries::pb::AckResponse response;
        request.set_token("default_token");
        auto vlet_info = request.mutable_vlet_info();
        auto addr = _p_meta->get_node_addr(1);
        vlet_info->set_node_addr(common::endpoint2int(addr));
        vlet_info->set_volume_id(1);
        vlet_info->set_shard_index(0);

        baidu::rpc::Controller cntl;
        baidu::rpc::Channel channel;
        int ret = channel.Init(_listen_addr, NULL);
        aries::pb::MasterManageService_Stub stub(&channel);
        stub.drop_vlet(&cntl, &request, &response, NULL);
        ASSERT_EQ(response.status().code(), AIE_NOT_EXIST);
    }
    //drop succ
    {
        aries::pb::MasterDropVletRequest request;
        aries::pb::AckResponse response;
        request.set_token("default_token");
        auto vlet_info = request.mutable_vlet_info();
        auto addr = _p_meta->get_node_addr(3);
        vlet_info->set_node_addr(common::endpoint2int(addr));
        vlet_info->set_volume_id(3);
        vlet_info->set_shard_index(0);
        vlet_info->set_vlet_version(0);

        baidu::rpc::Controller cntl;
        baidu::rpc::Channel channel;
        int ret = channel.Init(_listen_addr, NULL);
        aries::pb::MasterManageService_Stub stub(&channel);

        stub.drop_vlet(&cntl, &request, &response, NULL);
        ASSERT_EQ(response.status().code(), AIE_OK);
    }
    //no token
    {
        aries::pb::MasterDropVletRequest request;
        aries::pb::AckResponse response;
        request.set_token("default_token");
        auto vlet_info = request.mutable_vlet_info();
        auto addr = _p_meta->get_node_addr(3);
        vlet_info->set_node_addr(common::endpoint2int(addr));
        vlet_info->set_volume_id(3);
        vlet_info->set_shard_index(0);
        vlet_info->set_vlet_version(0);
        g_token_manager->_drop_vlet_token = 0;

        baidu::rpc::Controller cntl;
        baidu::rpc::Channel channel;
        int ret = channel.Init(_listen_addr, NULL);
        aries::pb::MasterManageService_Stub stub(&channel);

        stub.drop_vlet(&cntl, &request, &response, NULL);
        ASSERT_EQ(response.status().code(), AIE_FAIL);
    }
    //no token but force drop.
    {
        aries::pb::MasterDropVletRequest request;
        aries::pb::AckResponse response;
        request.set_token("default_token");
        request.set_is_force_drop(true);
        auto vlet_info = request.mutable_vlet_info();
        auto addr = _p_meta->get_node_addr(3);
        vlet_info->set_node_addr(common::endpoint2int(addr));
        vlet_info->set_volume_id(3);
        vlet_info->set_shard_index(0);
        vlet_info->set_vlet_version(0);
        g_token_manager->_drop_vlet_token = 0;

        baidu::rpc::Controller cntl;
        baidu::rpc::Channel channel;
        int ret = channel.Init(_listen_addr, NULL);
        aries::pb::MasterManageService_Stub stub(&channel);

        stub.drop_vlet(&cntl, &request, &response, NULL);
        ASSERT_EQ(response.status().code(), AIE_OK);
    }
}

TEST_F(ManageServiceTests, test_add_allocator) {
    MasterStub stub(_listen_addr);

    //add allocator ok
    {
        aries::pb::AddAllocatorRequest request;
        aries::pb::AckResponse response;
        request.set_token("default_token");
        request.set_allocator_name("a1");
        request.set_req_addr(1);
        request.set_allocator_addr(1);

        SynchronizedClosure rpc_waiter;
        stub.add_allocator(_listen_addr, &request, &response, &rpc_waiter, NULL);
        rpc_waiter.wait();
        ASSERT_EQ(response.status().code(), AIE_OK);
    }
    //add allocator exist.
    {
        aries::pb::AddAllocatorRequest request;
        aries::pb::AckResponse response;
        request.set_token("default_token");
        request.set_allocator_name("allocator1");
        base::EndPoint addr;
        base::str2endpoint("127.0.0.1:8000", &addr);
        request.set_req_addr(common::endpoint2int(addr));
        request.set_allocator_addr(common::endpoint2int(addr));

        SynchronizedClosure rpc_waiter;
        stub.add_allocator(_listen_addr, &request, &response, &rpc_waiter, NULL);
        rpc_waiter.wait();
        ASSERT_EQ(response.status().code(), AIE_EXIST);
    }
}

TEST_F(ManageServiceTests, test_redirect_allocator) {
    MasterStub stub(_listen_addr);

    //redirect allocator not exist
    {
        aries::pb::RedirectAllocatorRequest request;
        aries::pb::AckResponse response;
        request.set_token("default_token");
        request.set_allocator_name("a1");
        request.set_req_addr(1);
        request.set_allocator_addr(1);

        baidu::rpc::Controller cntl;
        baidu::rpc::Channel channel;
        int ret = channel.Init(_listen_addr, NULL);
        aries::pb::MasterManageService_Stub stub(&channel);
        stub.redirect_allocator(&cntl, &request, &response, NULL);
        ASSERT_EQ(response.status().code(), AIE_NOT_EXIST);
    }

    //redirect allocator when same.
    {

        aries::pb::RedirectAllocatorRequest request;
        aries::pb::AckResponse response;
        request.set_token("default_token");
        request.set_allocator_name("allocator1");

        base::EndPoint addr = _p_meta->get_allocator_addr();
        request.set_allocator_addr(common::endpoint2int(addr));

        baidu::rpc::Controller cntl;
        baidu::rpc::Channel channel;
        int ret = channel.Init(_listen_addr, NULL);
        aries::pb::MasterManageService_Stub stub(&channel);
        stub.redirect_allocator(&cntl, &request, &response, NULL);
        ASSERT_EQ(response.status().code(), AIE_EXIST);
    }
    //redirect allocator ok.
    {
        aries::pb::RedirectAllocatorRequest request;
        aries::pb::AckResponse response;
        request.set_token("default_token");
        request.set_allocator_name("allocator1");

        base::EndPoint addr;
        request.set_allocator_addr(1);

        baidu::rpc::Controller cntl;
        baidu::rpc::Channel channel;
        int ret = channel.Init(_listen_addr, NULL);
        aries::pb::MasterManageService_Stub stub(&channel);
        stub.redirect_allocator(&cntl, &request, &response, NULL);
        ASSERT_EQ(response.status().code(), AIE_OK);

        ASSERT_EQ(_master_control->_name, "allocator1");
        ASSERT_EQ(_master_control->_addr_int, 1);
    }
}

TEST_F(ManageServiceTests, test_set_cluster) {
    MasterStub stub(_listen_addr);

    //cluster id > 256.
    {
        aries::pb::SetClusterRequest request;
        aries::pb::AckResponse response;
        request.set_token("default_token");
        request.set_cluster_name("cluster_BeiJing");;
        request.set_cluster_id(257);;

        SynchronizedClosure rpc_waiter;
        stub.set_cluster(_listen_addr, &request, &response, &rpc_waiter, NULL);
        rpc_waiter.wait();
        ASSERT_EQ(response.status().code(), AIE_FAIL);
    }
    //ok.
    {
        aries::pb::SetClusterRequest request;
        aries::pb::AckResponse response;
        request.set_token("default_token");
        request.set_cluster_name("cluster_BeiJing");;
        request.set_cluster_id(1);;

        SynchronizedClosure rpc_waiter;
        stub.set_cluster(_listen_addr, &request, &response, &rpc_waiter, NULL);
        rpc_waiter.wait();
        ASSERT_EQ(response.status().code(), AIE_OK);
    }
}

TEST_F(ManageServiceTests, test_drop_allocator) {
    MasterStub stub(_listen_addr);

    //add allocator not exist.
    {
        aries::pb::DropAllocatorRequest request;
        aries::pb::AckResponse response;
        request.set_token("default_token");
        request.set_allocator_name("a1");

        SynchronizedClosure rpc_waiter;
        stub.drop_allocator(_listen_addr, &request, &response, &rpc_waiter, NULL);
        rpc_waiter.wait();
        ASSERT_EQ(response.status().code(), AIE_NOT_EXIST);
    }
    //drop allocator ok.
    {
        aries::pb::DropAllocatorRequest request;
        aries::pb::AckResponse response;
        request.set_token("default_token");
        request.set_allocator_name("allocator1");

        SynchronizedClosure rpc_waiter;
        stub.drop_allocator(_listen_addr, &request, &response, &rpc_waiter, NULL);
        rpc_waiter.wait();
        ASSERT_EQ(response.status().code(), AIE_OK);
    }
}

TEST_F(ManageServiceTests, test_assign_allocator_sequence_id) {
    MasterStub stub(_listen_addr);

    {
        aries::pb::AssignAllocatorSequenceIdRequest request;
        aries::pb::AssignAllocatorSequenceIdResponse response;
        request.set_token("default_token");
        request.set_allocator_name("a1");

        SynchronizedClosure rpc_waiter;
        stub.assign_allocator_sequence_id(_listen_addr, &request, &response, &rpc_waiter, NULL);
        rpc_waiter.wait();
        ASSERT_EQ(response.status().code(), AIE_NOT_EXIST);
    }

    {
        aries::pb::AssignAllocatorSequenceIdRequest request;
        aries::pb::AssignAllocatorSequenceIdResponse response;
        request.set_token("default_token");
        request.set_allocator_name("allocator1");

        SynchronizedClosure rpc_waiter;
        stub.assign_allocator_sequence_id(_listen_addr, &request, &response, &rpc_waiter, NULL);
        rpc_waiter.wait();
        ASSERT_EQ(response.status().code(), AIE_OK);
    }
}

TEST_F(ManageServiceTests, drop_tinker) {
    MasterStub stub(_listen_addr);

    {
        aries::pb::DropTinkerRequest request;
        aries::pb::AckResponse response;
        request.set_token("default_token");
        request.set_tinker_name("t1");
        request.set_tinker_addr(12);

        SynchronizedClosure rpc_waiter;
        stub.drop_tinker(_listen_addr, &request, &response, &rpc_waiter, NULL);
        rpc_waiter.wait();
        ASSERT_EQ(response.status().code(), AIE_NOT_EXIST);
    }
}

TEST_F(ManageServiceTests, test_disable_az) {
    MasterStub stub(_listen_addr);
    // bad token
    {
        aries::pb::DisableAzRequest request;
        aries::pb::AckResponse response;
        request.set_token("bad_token");
        request.set_az_name("az1");

        SynchronizedClosure rpc_waiter;
        stub.disable_az(_listen_addr, &request, &response, &rpc_waiter, NULL);
        rpc_waiter.wait();
        ASSERT_EQ(response.status().code(), AIE_INVALID_TOKEN);
    }
    // az is not exist
    {
        aries::pb::DisableAzRequest request;
        aries::pb::AckResponse response;
        request.set_token("default_token");
        request.set_az_name("az1");

        SynchronizedClosure rpc_waiter;
        stub.disable_az(_listen_addr, &request, &response, &rpc_waiter, NULL);
        rpc_waiter.wait();
        ASSERT_EQ(response.status().code(), AIE_FAIL);
    }
    // az is not safemode
    {
        auto az = std::shared_ptr<aries::master::AZ>(new aries::master::AZ("az1"));
        EXPECT_CALL(BMOCK_NS_CLASS_OBJECT(aries::master, AZManager, get_az),
                get_az(_))
            .WillRepeatedly(Return(az));

        aries::pb::DisableAzRequest request;
        aries::pb::AckResponse response;
        request.set_token("default_token");
        request.set_az_name("az1");

        SynchronizedClosure rpc_waiter;
        stub.disable_az(_listen_addr, &request, &response, &rpc_waiter, NULL);
        rpc_waiter.wait();
        ASSERT_EQ(response.status().code(), AIE_FAIL);
    }
    // az is not safemode, force disable
    {
        auto az = std::shared_ptr<aries::master::AZ>(new aries::master::AZ("az1"));
        EXPECT_CALL(BMOCK_NS_CLASS_OBJECT(aries::master, AZManager, get_az),
                get_az(_))
            .WillRepeatedly(Return(az));

        aries::pb::DisableAzRequest request;
        aries::pb::AckResponse response;
        request.set_token("default_token");
        request.set_az_name("az1");
        request.set_is_force_disable(true);

        SynchronizedClosure rpc_waiter;
        stub.disable_az(_listen_addr, &request, &response, &rpc_waiter, NULL);
        rpc_waiter.wait();
        ASSERT_EQ(response.status().code(), AIE_OK);
    }
    // az is safemode
    {
        auto az = std::shared_ptr<aries::master::AZ>(new aries::master::AZ("az1"));
        EXPECT_CALL(BMOCK_NS_CLASS_OBJECT(aries::master, AZManager, get_az),
                get_az(_))
            .WillRepeatedly(Return(az));

        aries::pb::DisableAzRequest request;
        aries::pb::AckResponse response;
        request.set_token("default_token");
        request.set_az_name("az1");

        az->enter_safemode();

        SynchronizedClosure rpc_waiter;
        stub.disable_az(_listen_addr, &request, &response, &rpc_waiter, NULL);
        rpc_waiter.wait();
        ASSERT_EQ(response.status().code(), AIE_OK);
    }
}

TEST_F(ManageServiceTests, test_enable_az) {
    MasterStub stub(_listen_addr);
    // bad token
    {
        aries::pb::EnableAzRequest request;
        aries::pb::AckResponse response;
        request.set_token("bad_token");
        request.set_az_name("az1");

        SynchronizedClosure rpc_waiter;
        stub.enable_az(_listen_addr, &request, &response, &rpc_waiter, NULL);
        rpc_waiter.wait();
        ASSERT_EQ(response.status().code(), AIE_INVALID_TOKEN);
    }
    // az is not exist
    {
        aries::pb::EnableAzRequest request;
        aries::pb::AckResponse response;
        request.set_token("default_token");
        request.set_az_name("az1");

        SynchronizedClosure rpc_waiter;
        stub.enable_az(_listen_addr, &request, &response, &rpc_waiter, NULL);
        rpc_waiter.wait();
        ASSERT_EQ(response.status().code(), AIE_FAIL);
    }
    // az is enabled
    {
        auto az = std::shared_ptr<aries::master::AZ>(new aries::master::AZ("az1"));
        EXPECT_CALL(BMOCK_NS_CLASS_OBJECT(aries::master, AZManager, get_az),
                get_az(_))
            .WillRepeatedly(Return(az));

        aries::pb::EnableAzRequest request;
        aries::pb::AckResponse response;
        request.set_token("default_token");
        request.set_az_name("az1");

        SynchronizedClosure rpc_waiter;
        stub.enable_az(_listen_addr, &request, &response, &rpc_waiter, NULL);
        rpc_waiter.wait();
        ASSERT_EQ(response.status().code(), AIE_FAIL);
    }
    // az is disabled
    {
        auto az = std::shared_ptr<aries::master::AZ>(new aries::master::AZ("az1"));
        EXPECT_CALL(BMOCK_NS_CLASS_OBJECT(aries::master, AZManager, get_az),
                get_az(_))
            .WillRepeatedly(Return(az));
        EXPECT_CALL(BMOCK_NS_CLASS_OBJECT(aries::master, AZManager, disabled_az_name),
                disabled_az_name())
            .WillRepeatedly(Return("az1"));

        aries::pb::EnableAzRequest request;
        aries::pb::AckResponse response;
        request.set_token("default_token");
        request.set_az_name("az1");

        SynchronizedClosure rpc_waiter;
        stub.enable_az(_listen_addr, &request, &response, &rpc_waiter, NULL);
        rpc_waiter.wait();
        ASSERT_EQ(response.status().code(), AIE_OK);
    }
     // az is enabled
    {
        auto az = std::shared_ptr<aries::master::AZ>(new aries::master::AZ("az1"));
        EXPECT_CALL(BMOCK_NS_CLASS_OBJECT(aries::master, AZManager, get_az),
                get_az(_))
            .WillRepeatedly(Return(az));

        aries::pb::EnableAzRequest request;
        aries::pb::AckResponse response;
        request.set_token("default_token");
        request.set_az_name("az1");

        SynchronizedClosure rpc_waiter;
        stub.enable_az(_listen_addr, &request, &response, &rpc_waiter, NULL);
        rpc_waiter.wait();
        ASSERT_EQ(response.status().code(), AIE_OK);
    }
}


TEST_F(ManageServiceTests, test_ip_pre_get) {
    {
        ManageServiceImpl service;
        std::string ip("*******:8100");
        auto ret = service.get_ip_prefix(ip);
        ASSERT_TRUE(ret == "1.1.1.*");
    }
}

TEST_F(ManageServiceTests, test_is_suitable_for_eat) {
    SpaceOptions space_opt;
    space_opt.n = 2;
    space_opt.k = 1;
    space_opt.space_name = "test_space1";
    space_opt.az_list.push_back("az1");
    space_opt.group_set.insert("group1");
    space_opt.put_quorum = 2;
    space_opt.membership_quorum = 1;
    space_opt.drop_normal_remain = 1;
    space_opt.max_vlet_per_idc = 2;
    space_opt.max_vlet_per_rack = 2;
    space_opt.vlet_type = VLET_TYPE_LINKED_32G_4M_512K;

    auto space1 = std::shared_ptr<Space>(new Space(space_opt));
    auto space2 = std::shared_ptr<Space>(new Space(space_opt));
    std::shared_ptr<Space> space3;
    auto status = ManageServiceImpl::is_suitable_for_eat(space2, 0, space3, 0);
    ASSERT_EQ(status.error_code(), AIE_FAIL);
    auto status2 = ManageServiceImpl::is_suitable_for_eat(space1, 0, space2, 0);
    ASSERT_EQ(status2.error_code(), AIE_OK);
    auto status3 = ManageServiceImpl::is_suitable_for_eat(space1, 100, space2, 0);
    ASSERT_EQ(status3.error_code(), AIE_FAIL);
    space1->_options.k = 9;
    auto status4 = ManageServiceImpl::is_suitable_for_eat(space1, 0, space2, 0);
    ASSERT_EQ(status4.error_code(), AIE_FAIL);
}

TEST_F(ManageServiceTests, test_eat_space) {
    SpaceOptions space_opt;
    space_opt.n = 2;
    space_opt.k = 1;
    space_opt.space_name = "test_space1";
    space_opt.az_list.push_back("az1");
    space_opt.group_set.insert("group1");
    space_opt.put_quorum = 2;
    space_opt.membership_quorum = 1;
    space_opt.drop_normal_remain = 1;
    space_opt.max_vlet_per_idc = 2;
    space_opt.max_vlet_per_rack = 2;
    space_opt.vlet_type = VLET_TYPE_LINKED_32G_4M_512K;

    auto space1 = std::shared_ptr<Space>(new Space(space_opt));
    space1->_space_id = 1;
    g_space_manager->add_space(space1);
    g_space_manager->_space_id = 1;

    space_opt.space_name = "test_space2";
    auto space2 = std::shared_ptr<Space>(new Space(space_opt));
    space2->_space_id = 2;
    g_space_manager->add_space(space2);
    g_space_manager->_space_id = 2;

    MasterStub stub(_listen_addr);

    aries::pb::EatSpaceRequest request;
    aries::pb::AckResponse response;
    request.set_token("default_token");
    request.set_eater_space_name("test_space1");
    request.set_eater_space_id(1);
    request.set_food_space_name("test_space2");
    request.set_food_space_id(2);

    SynchronizedClosure rpc_waiter;
    stub.eat_space(_listen_addr, &request, &response, &rpc_waiter, NULL);
    rpc_waiter.wait();
    ASSERT_EQ(response.status().code(), AIE_OK);

    // validator_addr is not same
    space_opt.validator_addr = "xx";
    space2->update_opt(space_opt);
    rpc_waiter.reset();
    stub.eat_space(_listen_addr, &request, &response, &rpc_waiter, NULL);
    rpc_waiter.wait();
    ASSERT_EQ(response.status().code(), AIE_FAIL);

    // group_name is not same
    space_opt.validator_addr.clear();
    space_opt.group_set.clear();
    space_opt.group_set.insert("yy");
    space2->update_opt(space_opt);
    rpc_waiter.reset();
    stub.eat_space(_listen_addr, &request, &response, &rpc_waiter, NULL);
    rpc_waiter.wait();
    ASSERT_EQ(response.status().code(), AIE_FAIL);
}

TEST_F(ManageServiceTests, test_get_gc_vlet_token) {
    // gc vlet be refused because have not token
    {
        MasterStub stub(_listen_addr);
        g_meta_data->set_state(MASTER_STATE_LEADER_WORKING);
        aries::pb::GetGcVletTokenRequest request;
        aries::pb::AckResponse response;
        request.set_token("default_token");

        g_token_manager->_gc_vlet_token = 0;

        SynchronizedClosure rpc_waiter;
        stub.get_gc_vlet_token(_listen_addr, &request, &response, &rpc_waiter, NULL);
        rpc_waiter.wait();
        ASSERT_EQ(response.status().code(), AIE_FAIL);
    }
    // get gc vlet token succeeded
    {
        MasterStub stub(_listen_addr);
        g_meta_data->set_state(MASTER_STATE_LEADER_WORKING);
        aries::pb::GetGcVletTokenRequest request;
        aries::pb::AckResponse response;
        request.set_token("default_token");

        g_token_manager->_gc_vlet_token = 100;

        SynchronizedClosure rpc_waiter;
        stub.get_gc_vlet_token(_listen_addr, &request, &response, &rpc_waiter, NULL);
        rpc_waiter.wait();
        ASSERT_EQ(response.status().code(), AIE_OK);
    }
}

TEST_F(ManageServiceTests, test_get_gc_disk_token) {
    // gc disk be refused because have not token
    {
        MasterStub stub(_listen_addr);
        g_meta_data->set_state(MASTER_STATE_LEADER_WORKING);
        aries::pb::GetGcDiskTokenRequest request;
        aries::pb::AckResponse response;
        request.set_token("default_token");

        g_token_manager->_gc_disk_token = 0;

        SynchronizedClosure rpc_waiter;
        stub.get_gc_disk_token(_listen_addr, &request, &response, &rpc_waiter, NULL);
        rpc_waiter.wait();
        ASSERT_EQ(response.status().code(), AIE_FAIL);
    }
    // get gc disk token succeeded
    {
        MasterStub stub(_listen_addr);
        g_meta_data->set_state(MASTER_STATE_LEADER_WORKING);
        aries::pb::GetGcDiskTokenRequest request;
        aries::pb::AckResponse response;
        request.set_token("default_token");

        g_token_manager->_gc_disk_token = 100;

        SynchronizedClosure rpc_waiter;
        stub.get_gc_disk_token(_listen_addr, &request, &response, &rpc_waiter, NULL);
        rpc_waiter.wait();
        ASSERT_EQ(response.status().code(), AIE_OK);
    }
}

TEST_F(ManageServiceTests, test_add_volume) {
    // not recover
    {
        FLAGS_recover_mode = 0;
        MasterStub stub(_listen_addr);
        g_meta_data->set_state(MASTER_STATE_FOLLOWER);
        aries::pb::AddVolumeRequest request;
        aries::pb::AckResponse response;
        request.set_token("default_token");

        SynchronizedClosure rpc_waiter;
        stub.add_volume(_listen_addr, &request, &response, &rpc_waiter, NULL);
        rpc_waiter.wait();
        ASSERT_EQ(response.status().code(), AIE_NOT_PRIMARY);
    }
    // space not exist
    {
        MasterStub stub(_listen_addr);
        g_meta_data->set_state(MASTER_STATE_LEADER_STARTING);
        FLAGS_recover_mode = 1;
        aries::pb::AddVolumeRequest request;
        aries::pb::AckResponse response;
        request.set_token("default_token");
        auto volume_info = request.add_volumes();
        volume_info->set_space_name("test_space_err");

        SynchronizedClosure rpc_waiter;
        stub.add_volume(_listen_addr, &request, &response, &rpc_waiter, NULL);
        rpc_waiter.wait();
        ASSERT_EQ(response.status().code(), AIE_FAIL);
    }
    // volume already exist
    {
        MasterStub stub(_listen_addr);
        g_meta_data->set_state(MASTER_STATE_LEADER_STARTING);
        FLAGS_recover_mode = 1;
        aries::pb::AddVolumeRequest request;
        aries::pb::AckResponse response;
        request.set_token("default_token");
        auto volume_info = request.add_volumes();
        volume_info->set_space_name("test_space_1");
        volume_info->set_volume_id(3);

        SynchronizedClosure rpc_waiter;
        stub.add_volume(_listen_addr, &request, &response, &rpc_waiter, NULL);
        rpc_waiter.wait();
        ASSERT_EQ(response.status().code(), AIE_FAIL);
    }
    // node not exist
    {
        MasterStub stub(_listen_addr);
        g_meta_data->set_state(MASTER_STATE_LEADER_STARTING);
        FLAGS_recover_mode = 1;
        aries::pb::AddVolumeRequest request;
        aries::pb::AckResponse response;
        request.set_token("default_token");
        auto volume_info = request.add_volumes();
        volume_info->set_space_name("test_space_1");
        volume_info->set_volume_id(2654);
        auto vlet_info = volume_info->add_vlet_list();
        auto addr = _p_meta->get_node_addr(0);
        vlet_info->set_node_addr(common::endpoint2int(addr));

        SynchronizedClosure rpc_waiter;
        stub.add_volume(_listen_addr, &request, &response, &rpc_waiter, NULL);
        rpc_waiter.wait();
        ASSERT_EQ(response.status().code(), AIE_FAIL);
    }
    // disk not exist
    {
        MasterStub stub(_listen_addr);
        g_meta_data->set_state(MASTER_STATE_LEADER_STARTING);
        FLAGS_recover_mode = 1;
        aries::pb::AddVolumeRequest request;
        aries::pb::AckResponse response;
        request.set_token("default_token");
        auto volume_info = request.add_volumes();
        volume_info->set_space_name("test_space_1");
        volume_info->set_volume_id(2654);
        auto vlet_info = volume_info->add_vlet_list();
        auto addr = _p_meta->get_node_addr(1);
        vlet_info->set_node_addr(common::endpoint2int(addr));
        vlet_info->set_disk_id(2);

        SynchronizedClosure rpc_waiter;
        stub.add_volume(_listen_addr, &request, &response, &rpc_waiter, NULL);
        rpc_waiter.wait();
        ASSERT_EQ(response.status().code(), AIE_FAIL);
    }
    // succ
    {
        MasterStub stub(_listen_addr);
        g_meta_data->set_state(MASTER_STATE_LEADER_STARTING);
        FLAGS_recover_mode = 1;
        aries::pb::AddVolumeRequest request;
        aries::pb::AckResponse response;
        request.set_token("default_token");

        SynchronizedClosure rpc_waiter;
        stub.add_volume(_listen_addr, &request, &response, &rpc_waiter, NULL);
        rpc_waiter.wait();
        ASSERT_EQ(response.status().code(), AIE_OK);
    }
}

TEST_F(ManageServiceTests, test_copy_space) {
    MasterStub stub(_listen_addr);
     // prepare
    auto space = g_space_manager->get_space("test_space1");
    //copy space fail due to space state err.
    {
        g_meta_data->set_state(MASTER_STATE_LEADER_WORKING);
        auto space = g_space_manager->get_space("test_space1");
        
        space->set_state(SPACE_STATE_REDUCE_REDUNDENCY);

        aries::pb::CopySpaceRequest request;
        aries::pb::AckResponse response;
        request.set_token("default_token");
        request.set_src_space_name("test_space1");
        request.set_src_space_id(1);

        SynchronizedClosure rpc_waiter;
        stub.copy_space(_listen_addr, &request, &response, &rpc_waiter, NULL);
        rpc_waiter.wait();
        ASSERT_EQ(response.status().code(), AIE_SPACE_NOT_READY);
        space->set_state(SPACE_STATE_NORMAL);
    }
    //copy space fail due to space not exist.
    {
        aries::pb::CopySpaceRequest request;
        aries::pb::AckResponse response;
        request.set_token("default_token");
        request.set_src_space_name("test_space_err");
        request.set_src_space_id(1);

        SynchronizedClosure rpc_waiter;
        stub.copy_space(_listen_addr, &request, &response, &rpc_waiter, NULL);
        rpc_waiter.wait();
        ASSERT_EQ(response.status().code(), AIE_NOT_EXIST);
    }
    //copy space fail due to space id is error.
    {
        aries::pb::CopySpaceRequest request;
        aries::pb::AckResponse response;
        request.set_token("default_token");
        request.set_src_space_name("test_space1");
        request.set_src_space_id(2);

        SynchronizedClosure rpc_waiter;
        stub.copy_space(_listen_addr, &request, &response, &rpc_waiter, NULL);
        rpc_waiter.wait();
        ASSERT_EQ(response.status().code(), AIE_FAIL);
    }
    // copy space fail due to az disable
    {

        BMOCK_NS_CLASS_STOP(aries::master, AZManager, disabled_az_name, std::string());
        g_az_manager->disable_az("az_disable");
        if (g_az_manager->disabled_az_name().empty() == false) {
            LOG(TRACE) << "disable az:" << g_az_manager->disabled_az_name();
        }

        aries::pb::CopySpaceRequest request;
        aries::pb::AckResponse response;
        request.set_token("default_token");
        request.set_src_space_name("test_space1");
        request.set_src_space_id(1);

        SynchronizedClosure rpc_waiter;
        stub.copy_space(_listen_addr, &request, &response, &rpc_waiter, NULL);
        rpc_waiter.wait();
        ASSERT_EQ(response.status().code(), AIE_FAIL);
        if (g_az_manager->disabled_az_name().empty() == false) {
            LOG(TRACE) << "disable az:" << g_az_manager->disabled_az_name();
        }
        g_az_manager->enable_az("az_disable");
        if (g_az_manager->disabled_az_name().empty() == false) {
            LOG(TRACE) << "disable az:" << g_az_manager->disabled_az_name();
        }
    }
    // copy space successed
    {
        auto space = g_space_manager->get_space("test_space1");

        aries::pb::CopySpaceRequest request;
        aries::pb::AckResponse response;
        request.set_token("default_token");
        request.set_src_space_name("test_space1");
        request.set_src_space_id(1);

        SynchronizedClosure rpc_waiter;
        stub.copy_space(_listen_addr, &request, &response, &rpc_waiter, NULL);
        rpc_waiter.wait();
        ASSERT_EQ(response.status().code(), AIE_OK);
    }
}

TEST_F(ManageServiceTests, test_control_copy_space) {

    MasterStub stub(_listen_addr);
     // prepare
    auto space = g_space_manager->get_space("test_space1");
    //copy space fail due to space state err.
    {
        g_meta_data->set_state(MASTER_STATE_LEADER_WORKING);
        auto space = g_space_manager->get_space("test_space1");
        space->set_state(SPACE_STATE_REDUCE_REDUNDENCY);

        aries::pb::CopySpaceRequest request;
        aries::pb::AckResponse response;
        request.set_token("default_token");
        request.set_src_space_name("test_space1");
        request.set_src_space_id(1);

        SynchronizedClosure rpc_waiter;
        stub.control_copy_space(_listen_addr, &request, &response, &rpc_waiter, NULL);
        rpc_waiter.wait();
        ASSERT_EQ(response.status().code(), AIE_SPACE_NOT_READY);
        space->set_state(SPACE_STATE_NORMAL);
    }
    //copy space fail due to space not exist.
    {
        aries::pb::CopySpaceRequest request;
        aries::pb::AckResponse response;
        request.set_token("default_token");
        request.set_src_space_name("test_space_err");
        request.set_src_space_id(1);

        SynchronizedClosure rpc_waiter;
        stub.control_copy_space(_listen_addr, &request, &response, &rpc_waiter, NULL);
        rpc_waiter.wait();
        ASSERT_EQ(response.status().code(), AIE_NOT_EXIST);
    }
    //copy space fail due to space id is error.
    {
        aries::pb::CopySpaceRequest request;
        aries::pb::AckResponse response;
        request.set_token("default_token");
        request.set_src_space_name("test_space1");
        request.set_src_space_id(2);

        SynchronizedClosure rpc_waiter;
        stub.control_copy_space(_listen_addr, &request, &response, &rpc_waiter, NULL);
        rpc_waiter.wait();
        ASSERT_EQ(response.status().code(), AIE_FAIL);
    }
    // copy space successed
    {
        auto space = g_space_manager->get_space("test_space1");
        space->set_state(SPACE_STATE_COPY);
        aries::pb::CopySpaceRequest request;
        aries::pb::AckResponse response;
        request.set_token("default_token");
        request.set_src_space_name("test_space1");
        request.set_src_space_id(1);

        SynchronizedClosure rpc_waiter;
        stub.control_copy_space(_listen_addr, &request, &response, &rpc_waiter, NULL);
        rpc_waiter.wait();
        ASSERT_EQ(response.status().code(), AIE_OK);
        space->set_state(SPACE_STATE_NORMAL);
    }

}

TEST_F(ManageServiceTests, test_cancel_copy_space) {
     MasterStub stub(_listen_addr);
     // prepare
    auto space = g_space_manager->get_space("test_space1");
    
    //copy space fail due to space state err.
    {
        g_meta_data->set_state(MASTER_STATE_LEADER_WORKING);
        auto space = g_space_manager->get_space("test_space1");
        space->set_state(SPACE_STATE_REDUCE_REDUNDENCY);

        aries::pb::CopySpaceRequest request;
        aries::pb::AckResponse response;
        request.set_token("default_token");
        request.set_src_space_name("test_space1");
        request.set_src_space_id(1);

        SynchronizedClosure rpc_waiter;
        stub.cancel_copy_space(_listen_addr, &request, &response, &rpc_waiter, NULL);
        rpc_waiter.wait();
        ASSERT_EQ(response.status().code(), AIE_SPACE_NOT_READY);
        space->set_state(SPACE_STATE_NORMAL);
    }
    //copy space fail due to space not exist.
    {
        aries::pb::CopySpaceRequest request;
        aries::pb::AckResponse response;
        request.set_token("default_token");
        request.set_src_space_name("test_space_err");
        request.set_src_space_id(1);

        SynchronizedClosure rpc_waiter;
        stub.cancel_copy_space(_listen_addr, &request, &response, &rpc_waiter, NULL);
        rpc_waiter.wait();
        ASSERT_EQ(response.status().code(), AIE_NOT_EXIST);
    }
    //copy space fail due to space id is error.
    {
        aries::pb::CopySpaceRequest request;
        aries::pb::AckResponse response;
        request.set_token("default_token");
        request.set_src_space_name("test_space1");
        request.set_src_space_id(2);

        SynchronizedClosure rpc_waiter;
        stub.cancel_copy_space(_listen_addr, &request, &response, &rpc_waiter, NULL);
        rpc_waiter.wait();
        ASSERT_EQ(response.status().code(), AIE_FAIL);
    }
    // copy space successed
    {
        auto space = g_space_manager->get_space("test_space1");
        space->set_state(SPACE_STATE_COPY);
        aries::pb::CopySpaceRequest request;
        aries::pb::AckResponse response;
        request.set_token("default_token");
        request.set_src_space_name("test_space1");
        request.set_src_space_id(1);

        SynchronizedClosure rpc_waiter;
        stub.cancel_copy_space(_listen_addr, &request, &response, &rpc_waiter, NULL);
        rpc_waiter.wait();
        ASSERT_EQ(response.status().code(), AIE_OK);
        space->set_state(SPACE_STATE_NORMAL);
    }
}

TEST_F(ManageServiceTests, test_finish_copy_space) {
    MasterStub stub(_listen_addr);
     // prepare
    auto space = g_space_manager->get_space("test_space1");
    //copy space fail due to space state err.
    {
        g_meta_data->set_state(MASTER_STATE_LEADER_WORKING);
        auto space = g_space_manager->get_space("test_space1");
        space->set_state(SPACE_STATE_REDUCE_REDUNDENCY);

        aries::pb::CopySpaceRequest request;
        aries::pb::AckResponse response;
        request.set_token("default_token");
        request.set_src_space_name("test_space1");
        request.set_src_space_id(1);

        SynchronizedClosure rpc_waiter;
        stub.finish_copy_space(_listen_addr, &request, &response, &rpc_waiter, NULL);
        rpc_waiter.wait();
        ASSERT_EQ(response.status().code(), AIE_SPACE_NOT_READY);
        space->set_state(SPACE_STATE_NORMAL);
    }
    //copy space fail due to space not exist.
    {
        aries::pb::CopySpaceRequest request;
        aries::pb::AckResponse response;
        request.set_token("default_token");
        request.set_src_space_name("test_space_err");
        request.set_src_space_id(1);

        SynchronizedClosure rpc_waiter;
        stub.finish_copy_space(_listen_addr, &request, &response, &rpc_waiter, NULL);
        rpc_waiter.wait();
        ASSERT_EQ(response.status().code(), AIE_NOT_EXIST);
    }
    //copy space fail due to space id is error.
    {
        aries::pb::CopySpaceRequest request;
        aries::pb::AckResponse response;
        request.set_token("default_token");
        request.set_src_space_name("test_space1");
        request.set_src_space_id(2);

        SynchronizedClosure rpc_waiter;
        stub.finish_copy_space(_listen_addr, &request, &response, &rpc_waiter, NULL);
        rpc_waiter.wait();
        ASSERT_EQ(response.status().code(), AIE_FAIL);
    }
    // copy space successed failed due volume not in COPY END state
    {
        auto space = g_space_manager->get_space("test_space1");

        space->set_state(SPACE_STATE_COPY);
        aries::pb::CopySpaceRequest request;
        aries::pb::AckResponse response;
        request.set_token("default_token");
        request.set_src_space_name("test_space1");
        request.set_src_space_id(1);

        SynchronizedClosure rpc_waiter;
        stub.finish_copy_space(_listen_addr, &request, &response, &rpc_waiter, NULL);
        rpc_waiter.wait();
        ASSERT_EQ(response.status().code(), AIE_FAIL);
        space->set_state(SPACE_STATE_NORMAL);
    }
    // copy space successed 
    {
        auto space = g_space_manager->get_space("test_space1");
        VolumeVector volume_list;
        space->get_volume_list(&volume_list);
        for (auto volume : volume_list) {
            volume->set_copy_progress(COPY_END);
        }
        space->set_state(SPACE_STATE_COPY);
        aries::pb::CopySpaceRequest request;
        aries::pb::AckResponse response;
        request.set_token("default_token");
        request.set_src_space_name("test_space1");
        request.set_src_space_id(1);

        SynchronizedClosure rpc_waiter;
        stub.finish_copy_space(_listen_addr, &request, &response, &rpc_waiter, NULL);
        rpc_waiter.wait();
        ASSERT_EQ(response.status().code(), AIE_OK);
        for (auto volume : volume_list) {
            volume->set_copy_progress(COPY_NONE);
        }
    }
}

aries::pb::CopyVletRequest get_copy_vlet_request() {
    auto space = g_space_manager->get_space("test_space1");
    VolumeVector volume_list;
    space->get_volume_list(&volume_list);

    aries::pb::CopyVletRequest request;
    request.set_token("default_token");
    auto src_vlet_info = request.mutable_copy_vlet_info()->mutable_src_vlet_info();
    auto src_space_info = request.mutable_copy_vlet_info()->mutable_src_space_info();
    src_vlet_info->set_volume_id(volume_list[0]->volume_id());
    src_vlet_info->set_shard_index(0);
    src_space_info->set_n(space->n());
    src_space_info->set_k(space->k());
    src_space_info->set_vlet_type(space->vlet_type());
    src_space_info->set_ec_type(space->ec_type());

    auto dest_space_option = request.mutable_copy_vlet_info()->mutable_dest_space_option();
    dest_space_option->set_cluster_id(g_meta_data->cluster_id());
    dest_space_option->set_space_name(space->space_name());
    dest_space_option->set_space_id(space->space_id());
    return request;
}

TEST_F(ManageServiceTests, test_copy_vlet) {
     MasterStub stub(_listen_addr);
     // copy space failed due cluster id not right
     {
        aries::pb::CopyVletRequest request;
        aries::pb::AckResponse response;
        request = get_copy_vlet_request();
        request.mutable_copy_vlet_info()->mutable_dest_space_option()->set_cluster_id(21);
        SynchronizedClosure rpc_waiter;
        stub.copy_vlet(_listen_addr, &request, &response, &rpc_waiter, NULL);

        rpc_waiter.wait();
        ASSERT_EQ(response.status().code(), AIE_FAIL);
     }
     // copy space failed due space not exist
     {
        aries::pb::CopyVletRequest request;
        aries::pb::AckResponse response;
        request = get_copy_vlet_request();
        request.mutable_copy_vlet_info()->mutable_dest_space_option()->set_space_name("123");
        SynchronizedClosure rpc_waiter;
        stub.copy_vlet(_listen_addr, &request, &response, &rpc_waiter, NULL);

        rpc_waiter.wait();
        ASSERT_EQ(response.status().code(), AIE_NOT_EXIST);
     }
     // copy space failed due state not right
     {
        auto space = g_space_manager->get_space("test_space1");
        space->set_state(SPACE_STATE_REDUCE_REDUNDENCY);
        aries::pb::CopyVletRequest request;
        aries::pb::AckResponse response;
        request = get_copy_vlet_request();
        SynchronizedClosure rpc_waiter;
        stub.copy_vlet(_listen_addr, &request, &response, &rpc_waiter, NULL);

        rpc_waiter.wait();
        ASSERT_EQ(response.status().code(), AIE_SPACE_NOT_READY);
        space->set_state(SPACE_STATE_NORMAL);
     }
     // check_copy_space_request id not right
     {
        auto space = g_space_manager->get_space("test_space1");
        aries::pb::CopyVletRequest request;
        aries::pb::AckResponse response;
        request = get_copy_vlet_request();
        request.mutable_copy_vlet_info()->mutable_dest_space_option()->set_space_id(123);
        SynchronizedClosure rpc_waiter;
        stub.copy_vlet(_listen_addr, &request, &response, &rpc_waiter, NULL);

        rpc_waiter.wait();
        ASSERT_EQ(response.status().code(), AIE_FAIL);
     }
     // check_copy_space_request N not right
     {
        auto space = g_space_manager->get_space("test_space1");
        aries::pb::CopyVletRequest request;
        aries::pb::AckResponse response;
        request = get_copy_vlet_request();
        request.mutable_copy_vlet_info()->mutable_src_space_info()->set_n(123);
        SynchronizedClosure rpc_waiter;
        stub.copy_vlet(_listen_addr, &request, &response, &rpc_waiter, NULL);

        rpc_waiter.wait();
        ASSERT_EQ(response.status().code(), AIE_FAIL);
     }
     // check_copy_space_request K not right
     {
        auto space = g_space_manager->get_space("test_space1");
        aries::pb::CopyVletRequest request;
        aries::pb::AckResponse response;
        request = get_copy_vlet_request();
        request.mutable_copy_vlet_info()->mutable_src_space_info()->set_k(123);
        SynchronizedClosure rpc_waiter;
        stub.copy_vlet(_listen_addr, &request, &response, &rpc_waiter, NULL);

        rpc_waiter.wait();
        ASSERT_EQ(response.status().code(), AIE_FAIL);
     }
     // check_copy_space_request vlet_type not right
     {
        auto space = g_space_manager->get_space("test_space1");
        aries::pb::CopyVletRequest request;
        aries::pb::AckResponse response;
        request = get_copy_vlet_request();
        request.mutable_copy_vlet_info()->mutable_src_space_info()->set_vlet_type(VLET_TYPE_LINKED_16G_1M_512K);
        SynchronizedClosure rpc_waiter;
        stub.copy_vlet(_listen_addr, &request, &response, &rpc_waiter, NULL);

        rpc_waiter.wait();
        ASSERT_EQ(response.status().code(), AIE_FAIL);
     }
     // set_copying_vlet succ but volume not exist
     {
        auto space = g_space_manager->get_space("test_space1");
        aries::pb::CopyVletRequest request;
        aries::pb::AckResponse response;
        request = get_copy_vlet_request();
        request.mutable_copy_vlet_info()->mutable_src_vlet_info()->set_volume_id(123);
        SynchronizedClosure rpc_waiter;
        stub.copy_vlet(_listen_addr, &request, &response, &rpc_waiter, NULL);

        rpc_waiter.wait();
        ASSERT_EQ(response.status().code(), AIE_FAIL);
     }
     // set_copying_vlet succ but vlet or volume state not right, must copy finished
     {
        auto space = g_space_manager->get_space("test_space1");
        VolumeVector volume_list;
        space->get_volume_list(&volume_list);
        auto vlet = volume_list[0]->get_vlet(0);
        aries::pb::CopyVletRequest request;
        aries::pb::AckResponse response;
        request = get_copy_vlet_request();
        SynchronizedClosure rpc_waiter;
        stub.copy_vlet(_listen_addr, &request, &response, &rpc_waiter, NULL);

        rpc_waiter.wait();
        ASSERT_EQ(response.status().code(), AIE_OK);
     }
     // set_copying_vlet succ but send to node failed
     {
        EXPECT_CALL(BMOCK_NS_CLASS_OBJECT(aries::master, AcrossClusterCopyScheduler, send_copy_vlet_to_node),
            send_copy_vlet_to_node(_))
        .WillOnce(Return(base::Status(AIE_FAIL, "fail")));
        auto space = g_space_manager->get_space("test_space1");
        VolumeVector volume_list;
        space->get_volume_list(&volume_list);
        auto vlet = volume_list[0]->get_vlet(0);
        volume_list[0]->set_state(VOLUME_STATE_RECEIVING);
        vlet->set_state(VLET_STATE_COPYING);
        aries::pb::CopyVletRequest request;
        aries::pb::AckResponse response;
        request = get_copy_vlet_request();
        SynchronizedClosure rpc_waiter;
        stub.copy_vlet(_listen_addr, &request, &response, &rpc_waiter, NULL);
        rpc_waiter.wait();
        ASSERT_EQ(response.status().code(), AIE_FAIL);
        volume_list[0]->set_state(VOLUME_STATE_NORMAL);
        vlet->set_state(VLET_STATE_NORMAL);
     }
     // copy vlet succ
    {
        EXPECT_CALL(BMOCK_NS_CLASS_OBJECT(aries::master, AcrossClusterCopyScheduler, send_copy_vlet_to_node),
            send_copy_vlet_to_node(_))
        .WillOnce(Return(base::Status(AIE_OK, "ok")));
        auto space = g_space_manager->get_space("test_space1");
        VolumeVector volume_list;
        space->get_volume_list(&volume_list);
        auto vlet = volume_list[0]->get_vlet(0);
        volume_list[0]->set_state(VOLUME_STATE_RECEIVING);
        vlet->set_state(VLET_STATE_COPYING);
        aries::pb::CopyVletRequest request;
        aries::pb::AckResponse response;
        request = get_copy_vlet_request();
        SynchronizedClosure rpc_waiter;
        stub.copy_vlet(_listen_addr, &request, &response, &rpc_waiter, NULL);
        rpc_waiter.wait();
        ASSERT_EQ(response.status().code(), AIE_OK);
        volume_list[0]->set_state(VOLUME_STATE_NORMAL);
        vlet->set_state(VLET_STATE_NORMAL);
    }
}

aries::pb::UpdateCopyVolumeRequest get_update_volume_progress_request() {
    aries::pb::UpdateCopyVolumeRequest request;
    request.set_token("default_token");
    auto space = g_space_manager->get_space("test_space1");
    VolumeVector volume_list;
    space->get_volume_list(&volume_list);
    request.set_volume_id(volume_list[0]->volume_id());
    request.set_src_space_id(space->space_id());
    request.set_src_space_name(space->space_name());
    request.set_n(space->n());
    request.set_copy_progress(COPY_RUNNING);
    request.set_dest_space_id(space->copy_space_option().space_id());
    request.set_dest_space_name(space->copy_space_option().space_name());
    request.set_dest_token(space->copy_space_option().token());
    space->set_state(SPACE_STATE_COPY);
    volume_list[0]->set_copy_progress(COPY_END);
    return request;
}
void clear_get_update_volume_progress_request() {
    auto space = g_space_manager->get_space("test_space1");
    VolumeVector volume_list;
    space->get_volume_list(&volume_list);
    space->set_state(SPACE_STATE_NORMAL);
    volume_list[0]->set_copy_progress(COPY_NONE);
}

TEST_F(ManageServiceTests, test_update_copy_volume_progress) {
    MasterStub stub(_listen_addr);
    auto space = g_space_manager->get_space("test_space1");
    VolumeVector volume_list;
    space->get_volume_list(&volume_list);
    // update_copy_volume_progress vid not exist
    {
        aries::pb::UpdateCopyVolumeRequest request;
        aries::pb::AckResponse response;
        request = get_update_volume_progress_request();
        request.set_volume_id(123);
        SynchronizedClosure rpc_waiter;
        stub.update_copy_volume_progress(_listen_addr, &request, &response, &rpc_waiter, NULL);
        rpc_waiter.wait();
        ASSERT_EQ(response.status().code(), AIE_NOT_EXIST);
        clear_get_update_volume_progress_request();
    }
    // update_copy_volume_progress space state not right
    {
        aries::pb::UpdateCopyVolumeRequest request;
        aries::pb::AckResponse response;
        request = get_update_volume_progress_request();
        space->set_state(SPACE_STATE_NORMAL);
        SynchronizedClosure rpc_waiter;
        stub.update_copy_volume_progress(_listen_addr, &request, &response, &rpc_waiter, NULL);
        rpc_waiter.wait();
        ASSERT_EQ(response.status().code(), AIE_FAIL);
        clear_get_update_volume_progress_request();
    }
    // update_copy_volume_progress src space id not right
    {
        aries::pb::UpdateCopyVolumeRequest request;
        aries::pb::AckResponse response;
        request = get_update_volume_progress_request();
        request.set_src_space_id(123);
        SynchronizedClosure rpc_waiter;
        stub.update_copy_volume_progress(_listen_addr, &request, &response, &rpc_waiter, NULL);
        rpc_waiter.wait();
        ASSERT_EQ(response.status().code(), AIE_FAIL);
        clear_get_update_volume_progress_request();
    }
    // update_copy_volume_progress dest space id not right
    {
        aries::pb::UpdateCopyVolumeRequest request;
        aries::pb::AckResponse response;
        request = get_update_volume_progress_request();
        request.set_dest_space_id(123);
        SynchronizedClosure rpc_waiter;
        stub.update_copy_volume_progress(_listen_addr, &request, &response, &rpc_waiter, NULL);
        rpc_waiter.wait();
        ASSERT_EQ(response.status().code(), AIE_FAIL);
        clear_get_update_volume_progress_request();
    }
    // update_copy_volume_progress succ
    {
        aries::pb::UpdateCopyVolumeRequest request;
        aries::pb::AckResponse response;
        request = get_update_volume_progress_request();
        SynchronizedClosure rpc_waiter;
        stub.update_copy_volume_progress(_listen_addr, &request, &response, &rpc_waiter, NULL);
        rpc_waiter.wait();
        ASSERT_EQ(response.status().code(), AIE_OK);
        clear_get_update_volume_progress_request();
    }
}

TEST_F(ManageServiceTests, test_enable_node_safemode) {
    // node is not exist
    {
        MasterStub stub(_listen_addr);
        g_meta_data->set_state(MASTER_STATE_LEADER_WORKING);
        aries::pb::EnableNodeSafeModeRequest request;
        aries::pb::AckResponse response;
        auto addr = _p_meta->get_node_addr(0);
        request.set_node_addr(common::endpoint2int(addr));
        request.set_token("default_token");

        SynchronizedClosure rpc_waiter;
        stub.enable_node_safemode(_listen_addr, &request, &response, &rpc_waiter, NULL);
        rpc_waiter.wait();
        ASSERT_EQ(response.status().code(), AIE_NOT_EXIST);
    }
    // node is in disk safemode state
    {
        MasterStub stub(_listen_addr);
        g_meta_data->set_state(MASTER_STATE_LEADER_WORKING);
        aries::pb::EnableNodeSafeModeRequest request;
        aries::pb::AckResponse response;
        auto addr = _p_meta->get_node_addr(1);
        request.set_node_addr(common::endpoint2int(addr));
        request.set_token("default_token");

        uint64_t timestamp = static_cast<uint64_t>(time(NULL));
        std::shared_ptr<Node> node = g_node_manager->get_node(addr);
        node->set_disk_safemode(true, timestamp);

        SynchronizedClosure rpc_waiter;
        stub.enable_node_safemode(_listen_addr, &request, &response, &rpc_waiter, NULL);
        rpc_waiter.wait();
        ASSERT_EQ(response.status().code(), AIE_FAIL);
    }

    {
        MasterStub stub(_listen_addr);
        g_meta_data->set_state(MASTER_STATE_LEADER_WORKING);
        aries::pb::EnableNodeSafeModeRequest request;
        aries::pb::AckResponse response;
        auto addr = _p_meta->get_node_addr(1);
        request.set_node_addr(common::endpoint2int(addr));
        request.set_token("default_token");

        std::shared_ptr<Node> node = g_node_manager->get_node(addr);
        node->set_disk_safemode(false, 0);

        SynchronizedClosure rpc_waiter;
        stub.enable_node_safemode(_listen_addr, &request, &response, &rpc_waiter, NULL);
        rpc_waiter.wait();
        ASSERT_EQ(response.status().code(), AIE_OK);
    }
}

TEST_F(ManageServiceTests, test_disable_node_safemode) {
    // node is not exist
    {
        MasterStub stub(_listen_addr);
        g_meta_data->set_state(MASTER_STATE_LEADER_WORKING);
        aries::pb::DisableNodeSafeModeRequest request;
        aries::pb::AckResponse response;
        auto addr = _p_meta->get_node_addr(0);
        request.set_node_addr(common::endpoint2int(addr));
        request.set_token("default_token");

        SynchronizedClosure rpc_waiter;
        stub.disable_node_safemode(_listen_addr, &request, &response, &rpc_waiter, NULL);
        rpc_waiter.wait();
        ASSERT_EQ(response.status().code(), AIE_NOT_EXIST);
    }
    // node is in disk safemode state
    {
        MasterStub stub(_listen_addr);
        g_meta_data->set_state(MASTER_STATE_LEADER_WORKING);
        aries::pb::DisableNodeSafeModeRequest request;
        aries::pb::AckResponse response;
        auto addr = _p_meta->get_node_addr(1);
        request.set_node_addr(common::endpoint2int(addr));
        request.set_token("default_token");

        std::shared_ptr<Node> node = g_node_manager->get_node(addr);
        node->set_disk_safemode(false, 0);

        SynchronizedClosure rpc_waiter;
        stub.disable_node_safemode(_listen_addr, &request, &response, &rpc_waiter, NULL);
        rpc_waiter.wait();
        ASSERT_EQ(response.status().code(), AIE_FAIL);
    }
    // force disable_node_safemode
    {
        MasterStub stub(_listen_addr);
        g_meta_data->set_state(MASTER_STATE_LEADER_WORKING);
        aries::pb::DisableNodeSafeModeRequest request;
        aries::pb::AckResponse response;
        auto addr = _p_meta->get_node_addr(1);
        request.set_node_addr(common::endpoint2int(addr));
        request.set_token("default_token");
        request.set_is_force_disable(true);

        std::shared_ptr<Node> node = g_node_manager->get_node(addr);
        node->set_disk_safemode(false, 0);

        SynchronizedClosure rpc_waiter;
        stub.disable_node_safemode(_listen_addr, &request, &response, &rpc_waiter, NULL);
        rpc_waiter.wait();
        ASSERT_EQ(response.status().code(), AIE_OK);
    }
    {
        MasterStub stub(_listen_addr);
        g_meta_data->set_state(MASTER_STATE_LEADER_WORKING);
        aries::pb::DisableNodeSafeModeRequest request;
        aries::pb::AckResponse response;
        auto addr = _p_meta->get_node_addr(1);
        request.set_node_addr(common::endpoint2int(addr));
        request.set_token("default_token");

        uint64_t timestamp = static_cast<uint64_t>(time(NULL));
        std::shared_ptr<Node> node = g_node_manager->get_node(addr);
        node->set_disk_safemode(true, timestamp);

        SynchronizedClosure rpc_waiter;
        stub.disable_node_safemode(_listen_addr, &request, &response, &rpc_waiter, NULL);
        rpc_waiter.wait();
        ASSERT_EQ(response.status().code(), AIE_OK);
    }
}

TEST_F(ManageServiceTests, test_check_add_volume_request) {
    {
        MasterStub stub(_listen_addr);
        aries::pb::AddVolumeRequest request;
        aries::pb::AckResponse response;
        request.set_token("default_token");
        request.add_volumes();
        request.mutable_volumes(0)->add_vlet_list();

        ManageServiceImpl manage_service;
        std::string str;
        bool res = manage_service.check_add_volume_request(request, &str);
        ASSERT_EQ(res, false);
    }
}

TEST_F(ManageServiceTests, test_add_manual_balance) {
    // is_running_manual_balance
    {
        g_balance_scheduler->_manual_balance.finish_balance_vlet_num = 1;
        g_balance_scheduler->_manual_balance.total_balance_vlet_num = 2;
        MasterStub stub(_listen_addr);
        g_meta_data->set_state(MASTER_STATE_LEADER_WORKING);
        aries::pb::AddManualBalanceRequest request;
        aries::pb::AckResponse response;
        request.set_token("default_token");

        SynchronizedClosure rpc_waiter;
        stub.add_manual_balance(_listen_addr, &request, &response, &rpc_waiter, NULL);
        rpc_waiter.wait();
        ASSERT_EQ(response.status().code(), AIE_FAIL);
    }
    //src_vlet_list is empty
    {
        g_balance_scheduler->_manual_balance.finish_balance_vlet_num = 2;
        g_balance_scheduler->_manual_balance.total_balance_vlet_num = 1;
        MasterStub stub(_listen_addr);
        g_meta_data->set_state(MASTER_STATE_LEADER_WORKING);
        aries::pb::AddManualBalanceRequest request;
        aries::pb::AckResponse response;
        request.set_token("default_token");

        SynchronizedClosure rpc_waiter;
        stub.add_manual_balance(_listen_addr, &request, &response, &rpc_waiter, NULL);
        rpc_waiter.wait();
        ASSERT_EQ(response.status().code(), AIE_FAIL);
    }

    // dest_az_list is empty
    {
        g_balance_scheduler->_manual_balance.finish_balance_vlet_num = 2;
        g_balance_scheduler->_manual_balance.total_balance_vlet_num = 1;
        MasterStub stub(_listen_addr);
        g_meta_data->set_state(MASTER_STATE_LEADER_WORKING);
        aries::pb::AddManualBalanceRequest request;
        aries::pb::AckResponse response;
        request.set_token("default_token");
        aries::pb::AddManualBalanceRequest::VletPosition vlet_pos;
        vlet_pos.set_volume_id(1);
        vlet_pos.set_shard_index(0);
        request.add_src_vlet_list()->CopyFrom(vlet_pos);

        SynchronizedClosure rpc_waiter;
        stub.add_manual_balance(_listen_addr, &request, &response, &rpc_waiter, NULL);
        rpc_waiter.wait();
        ASSERT_EQ(response.status().code(), AIE_FAIL);
    }

    {
        g_balance_scheduler->_manual_balance.finish_balance_vlet_num = 2;
        g_balance_scheduler->_manual_balance.total_balance_vlet_num = 1;
        MasterStub stub(_listen_addr);
        g_meta_data->set_state(MASTER_STATE_LEADER_WORKING);
        aries::pb::AddManualBalanceRequest request;
        aries::pb::AckResponse response;
        request.set_token("default_token");
        aries::pb::AddManualBalanceRequest::VletPosition vlet_pos;
        vlet_pos.set_volume_id(1);
        vlet_pos.set_shard_index(0);
        request.add_src_vlet_list()->CopyFrom(vlet_pos);
        request.add_dest_az_list();
        request.add_dest_group_list();
        request.add_dest_disk_type_list();
        request.set_manual_balance_policy(VOLUME_POLICY);

        SynchronizedClosure rpc_waiter;
        stub.add_manual_balance(_listen_addr, &request, &response, &rpc_waiter, NULL);
        rpc_waiter.wait();
        ASSERT_EQ(response.status().code(), AIE_OK);
    }

    {
        g_balance_scheduler->_manual_balance.finish_balance_vlet_num = 2;
        g_balance_scheduler->_manual_balance.total_balance_vlet_num = 1;
        MasterStub stub(_listen_addr);
        g_meta_data->set_state(MASTER_STATE_LEADER_WORKING);
        aries::pb::AddManualBalanceRequest request;
        aries::pb::AckResponse response;
        request.set_token("default_token");
        request.set_space_name("space_name");
        aries::pb::AddManualBalanceRequest::VletPosition vlet_pos;
        vlet_pos.set_volume_id(1);
        vlet_pos.set_shard_index(0);
        request.add_src_vlet_list()->CopyFrom(vlet_pos);

        SynchronizedClosure rpc_waiter;
        stub.add_manual_balance(_listen_addr, &request, &response, &rpc_waiter, NULL);
        rpc_waiter.wait();
        ASSERT_EQ(response.status().code(), AIE_OK);
    }
}

TEST_F(ManageServiceTests, test_set_meta_backup_keys) {
    {
        // keys are empty
        MasterStub stub(_listen_addr);
        g_meta_data->set_state(MASTER_STATE_LEADER_WORKING);
        aries::pb::SetMetaBackupKeysRequest request;
        aries::pb::AckResponse response;
        request.set_token("default_token");

        // keys are empty
        auto keys = request.mutable_keys();
        keys->set_access_key("");
        keys->set_secret_key("");
        SynchronizedClosure rpc_waiter;
        stub.set_meta_backup_keys(_listen_addr, &request, &response, &rpc_waiter, NULL);
        rpc_waiter.wait();
        ASSERT_EQ(response.status().code(), AIE_INVALID_ARGUMENT);
    }
    {
        // succ
        MasterStub stub(_listen_addr);
        g_meta_data->set_state(MASTER_STATE_LEADER_WORKING);
        aries::pb::SetMetaBackupKeysRequest request;
        aries::pb::AckResponse response;
        request.set_token("default_token");
        auto keys = request.mutable_keys();
        keys->set_access_key("access_key");
        keys->set_secret_key("secret_key");
        SynchronizedClosure rpc_waiter;
        stub.set_meta_backup_keys(_listen_addr, &request, &response, &rpc_waiter, NULL);
        rpc_waiter.wait();
        ASSERT_EQ(response.status().code(), AIE_OK);
    }
}

TEST_F(ManageServiceTests, test_show_meta_backup_keys) {
    // access_key or secret_key is not specified
    {
        g_meta_backup_keys_manager->set_keys("", "");
        MasterStub stub(_listen_addr);
        g_meta_data->set_state(MASTER_STATE_LEADER_WORKING);
        aries::pb::ShowMetaBackupKeysRequest request;
        aries::pb::ShowMetaBackupKeysResponse response;
        request.set_token("default_token");

        SynchronizedClosure rpc_waiter;
        stub.show_meta_backup_keys(_listen_addr, &request, &response, &rpc_waiter, NULL);
        rpc_waiter.wait();
        ASSERT_EQ(response.status().code(), AIE_OK);
        ASSERT_TRUE(response.keys().access_key().empty());
        ASSERT_TRUE(response.keys().secret_key().empty());
    }
    // succ
    {
        std::string encrypt_key = std::to_string(g_meta_data->cluster_id());
        std::string ak = "access_key";
        std::string sk = "secret_key";
        std::string ak_ciphertext, sk_ciphertext;
        aries::common::OpenSSLAES encryptor = aries::common::OpenSSLAES(16);
        bool en_res = encryptor.aes_encrypt(ak, 1, encrypt_key, &ak_ciphertext);
        ASSERT_EQ(en_res, 0);
        en_res = encryptor.aes_encrypt(sk, 1, encrypt_key, &sk_ciphertext);
        ASSERT_EQ(en_res, 0);
        g_meta_backup_keys_manager->set_keys(ak_ciphertext, sk_ciphertext);
        MasterStub stub(_listen_addr);
        g_meta_data->set_state(MASTER_STATE_LEADER_WORKING);
        aries::pb::ShowMetaBackupKeysRequest request;
        aries::pb::ShowMetaBackupKeysResponse response;
        request.set_token("default_token");
        SynchronizedClosure rpc_waiter;
        stub.show_meta_backup_keys(_listen_addr, &request, &response, &rpc_waiter, NULL);
        rpc_waiter.wait();
        ASSERT_EQ(response.status().code(), AIE_OK);
        ASSERT_EQ(response.keys().access_key(), "access_key");
        ASSERT_EQ(response.keys().secret_key(), "secret_key");
    }
}

TEST_F(ManageServiceTests, test_add_az_usage_balance) {
    // az usage balance is running
    {
        BMOCK_CLASS_MOCK_GUARD(BalanceScheduler, is_running_az_usage_balance);
        MasterStub stub(_listen_addr);
        g_meta_data->set_state(MASTER_STATE_LEADER_WORKING);
        aries::pb::AddAZUsageBalanceRequest request;
        aries::pb::AckResponse response;
        request.set_token("default_token");

        EXPECT_CALL(BMOCK_NS_CLASS_OBJECT(aries::master, BalanceScheduler, is_running_az_usage_balance),
                                    is_running_az_usage_balance())
            .WillOnce(Return(true))
            .WillRepeatedly(Return(false));        
        SynchronizedClosure rpc_waiter;
        stub.add_az_usage_balance(_listen_addr, &request, &response, &rpc_waiter, NULL);
        rpc_waiter.wait();
        ASSERT_EQ(response.status().code(), AIE_FAIL);
    }
    // az_list or space list is empty
    {
        MasterStub stub(_listen_addr);
        g_meta_data->set_state(MASTER_STATE_LEADER_WORKING);
        aries::pb::AddAZUsageBalanceRequest request;
        aries::pb::AckResponse response;
        request.set_token("default_token");

        SynchronizedClosure rpc_waiter;
        stub.add_az_usage_balance(_listen_addr, &request, &response, &rpc_waiter, NULL);
        rpc_waiter.wait();
        ASSERT_EQ(response.status().code(), AIE_FAIL);
    }
    // az is not exist
    {
        MasterStub stub(_listen_addr);
        g_meta_data->set_state(MASTER_STATE_LEADER_WORKING);
        aries::pb::AddAZUsageBalanceRequest request;
        aries::pb::AckResponse response;
        request.set_token("default_token");
        request.add_az_list("az1");

        SynchronizedClosure rpc_waiter;
        stub.add_az_usage_balance(_listen_addr, &request, &response, &rpc_waiter, NULL);
        rpc_waiter.wait();
        ASSERT_EQ(response.status().code(), AIE_FAIL);
    }
    // space is not exist
    {
        auto az1 = std::shared_ptr<aries::master::AZ>(new aries::master::AZ("az1"));
        auto az2 = std::shared_ptr<aries::master::AZ>(new aries::master::AZ("az2"));
        EXPECT_CALL(BMOCK_NS_CLASS_OBJECT(aries::master, AZManager, get_az),
                get_az(_))
            .WillOnce(Return(az1))
            .WillRepeatedly(Return(az2));;
        MasterStub stub(_listen_addr);
        g_meta_data->set_state(MASTER_STATE_LEADER_WORKING);
        aries::pb::AddAZUsageBalanceRequest request;
        aries::pb::AckResponse response;
        request.set_token("default_token");
        request.add_az_list("az1");
        request.add_az_list("az2");
        request.add_space_list("test_space");

        SynchronizedClosure rpc_waiter;
        stub.add_az_usage_balance(_listen_addr, &request, &response, &rpc_waiter, NULL);
        rpc_waiter.wait();
        ASSERT_EQ(response.status().code(), AIE_FAIL);
    }
    // space az is not in az list
    {
        auto az2 = std::shared_ptr<aries::master::AZ>(new aries::master::AZ("az1"));
        auto az3 = std::shared_ptr<aries::master::AZ>(new aries::master::AZ("az2"));
        EXPECT_CALL(BMOCK_NS_CLASS_OBJECT(aries::master, AZManager, get_az),
                get_az(_))
            .WillOnce(Return(az2))
            .WillRepeatedly(Return(az3));;
        MasterStub stub(_listen_addr);
        g_meta_data->set_state(MASTER_STATE_LEADER_WORKING);
        aries::pb::AddAZUsageBalanceRequest request;
        aries::pb::AckResponse response;
        request.set_token("default_token");
        request.add_az_list("az2");
        request.add_az_list("az3");
        request.add_space_list("test_space1");

        SynchronizedClosure rpc_waiter;
        stub.add_az_usage_balance(_listen_addr, &request, &response, &rpc_waiter, NULL);
        rpc_waiter.wait();
        ASSERT_EQ(response.status().code(), AIE_FAIL);
    }
    // ok
    {
        auto az1 = std::shared_ptr<aries::master::AZ>(new aries::master::AZ("az1"));
        auto az2 = std::shared_ptr<aries::master::AZ>(new aries::master::AZ("az2"));
        EXPECT_CALL(BMOCK_NS_CLASS_OBJECT(aries::master, AZManager, get_az),
                get_az(_))
            .WillOnce(Return(az1))
            .WillRepeatedly(Return(az2));;
        MasterStub stub(_listen_addr);
        g_meta_data->set_state(MASTER_STATE_LEADER_WORKING);
        aries::pb::AddAZUsageBalanceRequest request;
        aries::pb::AckResponse response;
        request.set_token("default_token");
        request.add_az_list("az1");
        request.add_az_list("az2");
        request.add_space_list("test_space555");

        SpaceOptions space_opt;
        space_opt.n = 2;
        space_opt.k = 1;
        space_opt.space_name = "test_space555";
        space_opt.az_list = {"az1", "az2"};
        space_opt.group_set.insert("group1");
        space_opt.put_quorum = 2;
        space_opt.membership_quorum = 1;
        space_opt.drop_normal_remain = 1;
        space_opt.max_vlet_per_idc = 2;
        space_opt.max_vlet_per_rack = 2;
        space_opt.vlet_type = VLET_TYPE_LINKED_32G_4M_512K;
        auto space1 = std::shared_ptr<Space>(new Space(space_opt));
        g_space_manager->add_space(space1);

        SynchronizedClosure rpc_waiter;
        stub.add_az_usage_balance(_listen_addr, &request, &response, &rpc_waiter, NULL);
        rpc_waiter.wait();
        ASSERT_EQ(response.status().code(), AIE_OK);
    }
}

TEST_F(ManageServiceTests, test_update_az_usage_balance) {
    // az_list or space list is empty
    {
        MasterStub stub(_listen_addr);
        g_meta_data->set_state(MASTER_STATE_LEADER_WORKING);
        aries::pb::UpdateAZUsageBalanceRequest request;
        aries::pb::AckResponse response;
        request.set_token("default_token");

        SynchronizedClosure rpc_waiter;
        stub.update_az_usage_balance(_listen_addr, &request, &response, &rpc_waiter, NULL);
        rpc_waiter.wait();
        ASSERT_EQ(response.status().code(), AIE_OK);
    }
    // az is not exist
    {
        BMOCK_NS_CLASS_STOP(aries::master, AZManager, get_az, std::shared_ptr<AZ>(std::string()));
        MasterStub stub(_listen_addr);
        g_meta_data->set_state(MASTER_STATE_LEADER_WORKING);
        aries::pb::UpdateAZUsageBalanceRequest request;
        aries::pb::AckResponse response;
        request.set_token("default_token");
        request.add_az_list("az111");

        SynchronizedClosure rpc_waiter;
        stub.update_az_usage_balance(_listen_addr, &request, &response, &rpc_waiter, NULL);
        rpc_waiter.wait();
        ASSERT_EQ(response.status().code(), AIE_FAIL);
    }
    // space is not exist
    {
        BMOCK_NS_CLASS_RESUME(aries::master, AZManager, get_az, std::shared_ptr<AZ>(std::string()));
        auto az1 = std::shared_ptr<aries::master::AZ>(new aries::master::AZ("az1"));
        auto az2 = std::shared_ptr<aries::master::AZ>(new aries::master::AZ("az2"));
        EXPECT_CALL(BMOCK_NS_CLASS_OBJECT(aries::master, AZManager, get_az),
                get_az(_))
            .WillOnce(Return(az1))
            .WillRepeatedly(Return(az2));;
        MasterStub stub(_listen_addr);
        g_meta_data->set_state(MASTER_STATE_LEADER_WORKING);
        aries::pb::UpdateAZUsageBalanceRequest request;
        aries::pb::AckResponse response;
        request.set_token("default_token");
        request.add_az_list("az1");
        request.add_az_list("az2");
        request.add_space_list("test_space");

        SynchronizedClosure rpc_waiter;
        stub.update_az_usage_balance(_listen_addr, &request, &response, &rpc_waiter, NULL);
        rpc_waiter.wait();
        ASSERT_EQ(response.status().code(), AIE_FAIL);
    }
    // space az is not in az list
    {
        BMOCK_NS_CLASS_RESUME(aries::master, AZManager, get_az, std::shared_ptr<AZ>(std::string()));
        auto az2 = std::shared_ptr<aries::master::AZ>(new aries::master::AZ("az1"));
        auto az3 = std::shared_ptr<aries::master::AZ>(new aries::master::AZ("az2"));
        EXPECT_CALL(BMOCK_NS_CLASS_OBJECT(aries::master, AZManager, get_az),
                get_az(_))
            .WillOnce(Return(az2))
            .WillRepeatedly(Return(az3));;
        MasterStub stub(_listen_addr);
        g_meta_data->set_state(MASTER_STATE_LEADER_WORKING);
        aries::pb::UpdateAZUsageBalanceRequest request;
        aries::pb::AckResponse response;
        request.set_token("default_token");
        request.add_az_list("az2");
        request.add_az_list("az3");
        request.add_space_list("test_space1");

        SynchronizedClosure rpc_waiter;
        stub.update_az_usage_balance(_listen_addr, &request, &response, &rpc_waiter, NULL);
        rpc_waiter.wait();
        ASSERT_EQ(response.status().code(), AIE_FAIL);
    }
    // only update az list
    {
        auto az1 = std::shared_ptr<aries::master::AZ>(new aries::master::AZ("az1"));
        auto az2 = std::shared_ptr<aries::master::AZ>(new aries::master::AZ("az2"));
        EXPECT_CALL(BMOCK_NS_CLASS_OBJECT(aries::master, AZManager, get_az),
                get_az(_))
            .WillOnce(Return(az1))
            .WillRepeatedly(Return(az2));;
        MasterStub stub(_listen_addr);
        g_meta_data->set_state(MASTER_STATE_LEADER_WORKING);
        aries::pb::UpdateAZUsageBalanceRequest request;
        aries::pb::AckResponse response;
        request.set_token("default_token");
        request.add_az_list("az1");
        request.add_az_list("az2");

        SpaceOptions space_opt;
        space_opt.n = 2;
        space_opt.k = 1;
        space_opt.space_name = "test_space555";
        space_opt.az_list = {"az1", "az2"};
        space_opt.group_set.insert("group1");
        space_opt.put_quorum = 2;
        space_opt.membership_quorum = 1;
        space_opt.drop_normal_remain = 1;
        space_opt.max_vlet_per_idc = 2;
        space_opt.max_vlet_per_rack = 2;
        space_opt.vlet_type = VLET_TYPE_LINKED_32G_4M_512K;
        auto space1 = std::shared_ptr<Space>(new Space(space_opt));
        g_space_manager->add_space(space1);

        SynchronizedClosure rpc_waiter;
        stub.update_az_usage_balance(_listen_addr, &request, &response, &rpc_waiter, NULL);
        rpc_waiter.wait();
        ASSERT_EQ(response.status().code(), AIE_OK);
    }
    // only update space list
    {
        auto az1 = std::shared_ptr<aries::master::AZ>(new aries::master::AZ("az1"));
        auto az2 = std::shared_ptr<aries::master::AZ>(new aries::master::AZ("az2"));
        EXPECT_CALL(BMOCK_NS_CLASS_OBJECT(aries::master, AZManager, get_az),
                get_az(_))
            .WillOnce(Return(az1))
            .WillRepeatedly(Return(az2));;
        MasterStub stub(_listen_addr);
        g_meta_data->set_state(MASTER_STATE_LEADER_WORKING);
        aries::pb::UpdateAZUsageBalanceRequest request;
        aries::pb::AckResponse response;
        request.set_token("default_token");
        request.add_space_list("test_space555");

        SpaceOptions space_opt;
        space_opt.n = 2;
        space_opt.k = 1;
        space_opt.space_name = "test_space555";
        space_opt.az_list = {"az1", "az2"};
        space_opt.group_set.insert("group1");
        space_opt.put_quorum = 2;
        space_opt.membership_quorum = 1;
        space_opt.drop_normal_remain = 1;
        space_opt.max_vlet_per_idc = 2;
        space_opt.max_vlet_per_rack = 2;
        space_opt.vlet_type = VLET_TYPE_LINKED_32G_4M_512K;
        auto space1 = std::shared_ptr<Space>(new Space(space_opt));
        g_space_manager->add_space(space1);

        SynchronizedClosure rpc_waiter;
        stub.update_az_usage_balance(_listen_addr, &request, &response, &rpc_waiter, NULL);
        rpc_waiter.wait();
        ASSERT_EQ(response.status().code(), AIE_OK);
    }
    // ok
    {
        auto az1 = std::shared_ptr<aries::master::AZ>(new aries::master::AZ("az1"));
        auto az2 = std::shared_ptr<aries::master::AZ>(new aries::master::AZ("az2"));
        EXPECT_CALL(BMOCK_NS_CLASS_OBJECT(aries::master, AZManager, get_az),
                get_az(_))
            .WillOnce(Return(az1))
            .WillRepeatedly(Return(az2));;
        MasterStub stub(_listen_addr);
        g_meta_data->set_state(MASTER_STATE_LEADER_WORKING);
        aries::pb::UpdateAZUsageBalanceRequest request;
        aries::pb::AckResponse response;
        request.set_token("default_token");
        request.add_az_list("az1");
        request.add_az_list("az2");
        request.add_space_list("test_space555");

        SpaceOptions space_opt;
        space_opt.n = 2;
        space_opt.k = 1;
        space_opt.space_name = "test_space555";
        space_opt.az_list = {"az1", "az2"};
        space_opt.group_set.insert("group1");
        space_opt.put_quorum = 2;
        space_opt.membership_quorum = 1;
        space_opt.drop_normal_remain = 1;
        space_opt.max_vlet_per_idc = 2;
        space_opt.max_vlet_per_rack = 2;
        space_opt.vlet_type = VLET_TYPE_LINKED_32G_4M_512K;
        auto space1 = std::shared_ptr<Space>(new Space(space_opt));
        g_space_manager->add_space(space1);

        SynchronizedClosure rpc_waiter;
        stub.update_az_usage_balance(_listen_addr, &request, &response, &rpc_waiter, NULL);
        rpc_waiter.wait();
        ASSERT_EQ(response.status().code(), AIE_OK);
    }
}

}//end namespace master
}//end namespace aries

