// Copyright (c) 2021 Baidu.com, Inc. All Rights Reserved
//
// Author: chenz<PERSON><EMAIL>


#include <gtest/gtest.h>
#include <baidu/rpc/server.h>
#include "baidu/inf/aries/master/common.h"
#include "baidu/inf/aries/master/conf.h"
#include "baidu/inf/aries/master/meta/meta_data_recover_helper.h"
#include "baidu/inf/aries/master/test/test_utils.h"
#include "baidu/inf/aries/master/test/test_master_test_base.h"

namespace aries {
namespace master {

class MockNode : public aries::pb::DataNodeDataService {
public:
    MockNode() {}

    ~MockNode() {}

    virtual void get_vlet_info(::google::protobuf::RpcController* controller,
            const ::aries::pb::GetVletInfoRequest* request,
            ::aries::pb::GetVletInfoResponse* response,
            ::google::protobuf::Closure* done) {
        baidu::rpc::ClosureGuard done_guard(done);

        auto status = response->mutable_status();
        status->set_code(AIE_OK);

        auto vlet_0 = response->add_vlet_info_list();
        vlet_0->set_volume_id(0);
        vlet_0->set_shard_index(0);
        vlet_0->set_vlet_version(0);
        vlet_0->set_create_time(0);
        vlet_0->set_blob_num(0);
        vlet_0->set_max_vbid(0);
        vlet_0->set_free_size(0);
        vlet_0->set_total_size(0);

        auto vlet_1 = response->add_vlet_info_list();
        vlet_1->set_volume_id(1);
        vlet_1->set_shard_index(0);
        vlet_1->set_vlet_version(0);
        vlet_1->set_create_time(0);
        vlet_1->set_blob_num(0);
        vlet_1->set_max_vbid(0);
        vlet_1->set_free_size(0);
        vlet_1->set_total_size(0);

        auto vlet_2 = response->add_vlet_info_list();
        vlet_2->set_volume_id(2);
        vlet_2->set_shard_index(0);
        vlet_2->set_vlet_version(0);
        vlet_2->set_create_time(0);
        vlet_2->set_blob_num(0);
        vlet_2->set_max_vbid(0);
        vlet_2->set_free_size(0);
        vlet_2->set_total_size(0);
    }
private:
};

std::string node_addr = "127.0.0.1:60101";

class MetaDataRecoverHelperTests : public::testing::Test {
protected:
    MetaDataRecoverHelperTests() {}
    virtual ~MetaDataRecoverHelperTests() {}
};

TEST_F(MetaDataRecoverHelperTests, recover_mode_1) {
    base::EndPoint endpoint;
    base::str2endpoint(node_addr.c_str(), &endpoint);
    SpaceOptions space_opt;
    space_opt.n = 18;
    space_opt.k = 9;
    space_opt.space_name = "s1";
    auto space = std::shared_ptr<Space>(new Space(space_opt));
    g_space_manager->add_space(space);
    for (int vid = 1; vid <= 3; ++vid) {
        VolumeOptions volume_opt;
        volume_opt.volume_id = vid;
        volume_opt.space = space;
        std::shared_ptr<Volume> volume(new Volume(volume_opt));
        volume->_state = VOLUME_STATE_NORMAL;
        volume->_per_vlet_size = 32 * aries::common::GB;
        for (uint64_t index = 0; index < 2; ++index) {
            VletOptions vlet_opt;
            vlet_opt.volume = volume;
            vlet_opt.shard_index = index;
            vlet_opt.state = VLET_STATE_NORMAL;
            vlet_opt.disk_ptr = nullptr;
            auto vlet = std::shared_ptr<Vlet>(new Vlet(vlet_opt));
            vlet->set_vlet_type(VLET_TYPE_LINKED_32G_4M_512K);
            aries::pb::VletEngineInfo vlet_engine_options;
            aries::common::vlet_engine_by_vlet_type(VLET_TYPE_LINKED_32G_4M_512K, &vlet_engine_options);
            vlet->set_vlet_engine_info(vlet_engine_options);
            volume->add_vlet(vlet);
        }
        g_meta_data->add_volume(volume);
    }
    auto volume1 = g_volume_manager->get_volume(1);
    auto vlet10 = volume1->get_vlet(0);
    auto volume2 = g_volume_manager->get_volume(2);
    auto vlet20 = volume2->get_vlet(0);
    auto volume3 = g_volume_manager->get_volume(3);
    auto vlet30 = volume3->get_vlet(0);

    NodeOptions node_opt;
    node_opt.group_name = "group1";
    node_opt.idc_name = "idc1";
    node_opt.az_name = "az1";
    node_opt.rack_name = "rack1";
    node_opt.addr = endpoint;
    auto node = std::shared_ptr<Node>(new Node(node_opt));
    auto disk = std::shared_ptr<Disk>(new Disk(1, node));

    vlet10->set_disk_ptr(disk);
    disk->add_vlet(vlet10);
    vlet20->set_disk_ptr(disk);
    disk->add_vlet(vlet20);
    vlet30->set_disk_ptr(disk);
    disk->add_vlet(vlet30);
    node->add_disk(disk);
    g_meta_data->add_node(node);

    auto data_service_addr = common::get_data_service_addr(endpoint);
    baidu::rpc::Server server;
    auto mock_node = new MockNode;
    CHECK_EQ(0, server.AddService(mock_node, baidu::rpc::SERVER_OWNS_SERVICE));
    ASSERT_EQ(0, server.Start(data_service_addr, NULL));

    FLAGS_recover_mode = 1;
    g_metadata_recover_helper->run();

    g_metadata_recover_helper->list_nodes();
    ASSERT_EQ(1, g_metadata_recover_helper->_node_list.size());

    {
        common::ScopedMutexLock lock(g_metadata_recover_helper->_has_collected_cond);
        g_metadata_recover_helper->collect_volume_metas();
        g_metadata_recover_helper->_has_collected_cond.wait();
    }
    ASSERT_EQ(1, g_metadata_recover_helper->_node_meta_map.size());
    ASSERT_EQ(3, g_metadata_recover_helper->_node_meta_map[endpoint].vlet_infos.size());

    g_metadata_recover_helper->check_vlets();
    ASSERT_EQ(1, g_metadata_recover_helper->_new_volumes.size());

    g_metadata_recover_helper->clear();
    ASSERT_EQ(0, g_metadata_recover_helper->_node_list.size());
    ASSERT_EQ(0, g_metadata_recover_helper->_node_meta_map.size());
    ASSERT_EQ(0, g_metadata_recover_helper->_new_volumes.size());

    server.Stop(0);
    server.Join();
}

}
}
