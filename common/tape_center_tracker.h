/***************************************************************************
 * 
 * Copyright (c) 2021 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/
 
 /**
 * @file tape_center_tracker.h
 * <AUTHOR>
 * @date 2021/11/22 09:45:12
 * @version 1.0 
 * @brief 
 *  
 **/

#pragma once

#include <base/memory/ref_counted.h>
#include <base/memory/singleton.h>
#include <base/threading/simple_thread.h>

#include "baidu/inf/aries-api/common/leader_tracker.h"

namespace aries::common {

DECLARE_string(tape_center_address);
DECLARE_int32(refresh_tape_center_interval_second);

class TapeCenterTracker : public base::RefCountedThreadSafe<TapeCenterTracker>, public LeaderTracker {
public:
    static TapeCenterTracker* GetInstance() { 
        return Singleton<TapeCenterTracker>::get();
    }

    TapeCenterTracker() {}
    virtual ~TapeCenterTracker() {}

    int start(const base::EndPoint& local_addr,
            std::string token = "",
            std::string tape_center_addr = "",
            uint32_t refresh_interval_second = 30);
    base::EndPoint get_tape_center();

protected:
    void check_leader(const std::vector<base::EndPoint>& addrs, std::map<base::EndPoint, int>* candidates);

private:
    friend struct DefaultSingletonTraits<TapeCenterTracker>;
    friend class base::RefCountedThreadSafe<TapeCenterTracker>;
};

#define g_tape_center_tracker ::aries::common::TapeCenterTracker::GetInstance()

bool is_tape_center_tracker_started();

int start_tape_center_tracker(const base::EndPoint& local_addr,
        std::string token = "",
        std::string tape_center_addr = "",
        int32_t refresh_interval_second = 30);

void stop_tape_center_tracker();

} // end of namespace aries::common

/* vim: set ts=4 sw=4 sts=4 tw=100 */
