// Copyright 2021 Baidu Inc. All Rights Reserved.
// @Author:
// @Created Time : Thu Dec 21 14:04:23 CST 2021

#include "bthread_executor.h"

namespace aries {
namespace common {

int BthreadExecutor::add(Func func) {
    auto* args = new Func(::std::move(func));
    ::bthread_t th;
    if (0 != ::bthread_start_background(&th, NULL, bthread_run, args)) {
        LOG(WARNING) << "start bthread to execute failed";
        delete args;
        return -1;
    }
    return 0;
}

int BthreadExecutor::add_urgent(Func func) {
    auto* args = new Func(::std::move(func));
    ::bthread_t th;
    if (0 != ::bthread_start_urgent(&th, NULL, bthread_run, args)) {
        LOG(WARNING) << "start bthread to execute failed";
        delete args;
        return -1;
    }
    return 0;
}

}
}