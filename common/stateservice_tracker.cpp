/***************************************************************************
 *
 * Copyright (c) 2022 Baidu.com, Inc. All Rights Reserved
 *
 **************************************************************************/

#include "baidu/inf/aries-api/common/stateservice_tracker.h"

#include "baidu/inf/aries-api/common/proto/stateservice.pb.h"

namespace aries::common {

DEFINE_string(stateservice_address, "", "stateservice address");
DEFINE_int32(refresh_stateservice_interval_second, 30, "refresh stateservice interval in second");
DEFINE_uint64(stateservice_raft_group_count, 0, "stateservice raft group count");

bool StateServiceTracker::start(const base::EndPoint& local_addr,
        const std::string& token) {
    if (FLAGS_stateservice_address.empty()) {
        LOG(ERROR) << "--stateservice_address not set";
        return false;
    }
    if (FLAGS_stateservice_raft_group_count == 0) {
        LOG(ERROR) << "--stateservice_raft_group_count not set";
        return false;
    }

    return MultiLeaderTracker::start(local_addr, token,
        FLAGS_stateservice_address, FLAGS_refresh_stateservice_interval_second,
        FLAGS_stateservice_raft_group_count);
}

bool StateServiceTracker::start(const base::EndPoint& local_addr,
        const std::string& token,
        const std::string& addr,
        uint64_t raft_group_count) {

    return MultiLeaderTracker::start(local_addr, token,
        addr, FLAGS_refresh_stateservice_interval_second,
        raft_group_count);
}

void StateServiceTracker::update_raft_groups(const std::vector<base::EndPoint>& addrs) {
    LOG(TRACE) << "StateServiceTracker start update raft groups";
    if (addrs.size() == 0) {
        LOG(WARNING) << "StateServiceTracker update raft groups failed"
            << ", due to empty address list";
        return;
    }
    bool update_success = false;
    for (const auto& addr : addrs) {
        baidu::rpc::Controller cntl;
        baidu::rpc::ChannelOptions options;
        options.max_retry = 0;
        options.connect_timeout_ms = 1500;
        options.timeout_ms = 3000;

        baidu::rpc::Channel channel;
        if (0 != channel.Init(addr, &options)) {
            continue;
        }

        aries::pb::StateServiceGetRaftGroupRequest request;
        request.set_token(_token);
        request.set_req_addr(common::endpoint2int(_local_addr));
        aries::pb::StateServiceGetRaftGroupResponse response;
        aries::pb::StateServiceQueryService_Stub stub(&channel);
        stub.get_raft_group(&cntl, &request, &response, NULL);

        if (cntl.Failed() || AIE_OK != response.status().code()) {
            continue;
        }
        update_success = true;
        ScopedMutexLock lock(_mutex);
        for (auto i = 0; i < response.raft_groups_size(); i++) {
            auto& raft_group = response.raft_groups(i);
            common::str2endpoint(raft_group.leader_addr().c_str(), 
                &_raft_groups[raft_group.raft_group_id()].leader);
            // we can only get peers info from leader
            if (raft_group.raft_peers_size() > 0) {
                _raft_groups[raft_group.raft_group_id()].peers.clear();
                for (auto j = 0; j < raft_group.raft_peers_size(); j++) {
                    base::EndPoint ep;
                    common::str2endpoint(raft_group.raft_peers(j).c_str(), &ep);
                    _raft_groups[raft_group.raft_group_id()].peers.push_back(ep);
                }
            }
        }
        LOG(TRACE) << "StateServiceTracker collect info of raft groups success"
            << ", from address:" << common::endpoint2str(addr);
    }
    if (!update_success) {
        LOG(WARNING) << "StateServiceTracker update raft groups failed"
            << ", all addresses tried out";
    }
}

}
