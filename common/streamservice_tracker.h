/***************************************************************************
 * 
 * Copyright (c) 2023 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/
 
#pragma once

#include <base/memory/singleton.h>

#include "baidu/inf/aries-api/common/multi_leader_tracker.h"

namespace aries::common {

const uint32_t MAX_RAFT_GROUP_COUNT = 256;
DECLARE_string(streamservice_address);
DECLARE_int32(refresh_streamservice_interval_second);
DECLARE_uint64(streamservice_raft_group_count);

class StreamServiceTracker : public MultiLeaderTracker {
public:
    static StreamServiceTracker* GetInstance() {
        return Singleton<StreamServiceTracker>::get();
    }

    StreamServiceTracker() {
        _raft_group_count = 0;
    }
    virtual ~StreamServiceTracker() {}

    bool start(const base::EndPoint& local_addr,
        const std::string& token);

    bool start(const base::EndPoint& local_addr,
        const std::string& token,
        const std::string& addr);
    uint64_t get_raft_group_count() {
        return _raft_group_count;
    }
protected:
    virtual void update_raft_groups(const std::vector<base::EndPoint>& addrs);
private:
    DISALLOW_COPY_AND_ASSIGN(StreamServiceTracker);
    friend struct DefaultSingletonTraits<StreamServiceTracker>;
    uint64_t _raft_group_count;
};

} // end of namespace aries::common

#define g_streamservice_tracker ::aries::common::StreamServiceTracker::GetInstance()

/* vim: set ts=4 sw=4 sts=4 tw=100 */
