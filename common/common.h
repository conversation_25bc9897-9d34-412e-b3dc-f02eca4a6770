/**
 * ARIES - A Reliable and Impressive EB-level Storage.
 * Copyright (c) 2016 Baidu.com, Inc. All Rights Reserved.
 *
 * Author: <PERSON> (<EMAIL>)
 * Date: 2016/08/31
 * Desciption: common definitions
 *
 */

#ifndef BAIDU_INF_ARIES_COMMON_COMMON_H
#define BAIDU_INF_ARIES_COMMON_COMMON_H

#include <map>
#include <string>
#include <set>
#include <vector>
#include <memory>
#include <cmath>
#include <cstdint>
#include <iomanip>
#include <sstream>
#include <optional>

#include "bvar/bvar.h"
#include "boost/any.hpp"
#include "baidu/rpc/server.h"
#include "base/strings/string_util.h"
#include "base/fast_rand.h"
#include "gflags/gflags.h"
#include "uuid/uuid.h"

#include "baidu/inf/aries-api/aries_define.h"
#include "baidu/inf/aries-api/common/rpc_call.h"

typedef unsigned __int128 uint128_t;

namespace logging {
    DECLARE_bool(crash_on_fatal_log);
}

namespace aries {

DECLARE_int32(max_network_flow_limit);

class ARIES_FATAL_LOG {
public:
    ARIES_FATAL_LOG() = default;
    ~ARIES_FATAL_LOG() {
        if (logging::FLAGS_crash_on_fatal_log) {
            LOG(ERROR) << ss.str();
        }else {
            LOG(FATAL) << ss.str();
        }
    }
    template<typename T>
    inline ARIES_FATAL_LOG& operator<<(T val) {
        ss << val;
        return *this;
    }
private:
    std::stringstream ss;
};

//define aries log level
const int ALOG_DEBUG = 6;
const int ALOG_TRACE = 5;
const int ALOG_INFO = 4;
const int ALOG_NOTICE = 3;
const int ALOG_WARNING = 2;
const int ALOG_ERROR = 1;
const int ALOG_FATAL = 0;

#define ARIES_LIKELY(x)           (__builtin_expect((x), 1))
#define ARIES_UNLIKELY(x)         (__builtin_expect((x), 0))
#define ARIES_UNLIKELY_IF(x)      if (__builtin_expect((x), 0))
#define ARIES_LIKELY_IF(x)        if (__builtin_expect((x), 1))

#ifndef likely
#define likely(x)                 __builtin_expect(!!(x), 1)
#endif
#ifndef unlikely
#define unlikely(x)               __builtin_expect(!!(x), 0)
#endif
#ifndef likely_if
#define likely_if(x)              if (likely(x))
#endif
#ifndef unlikely_if
#define unlikely_if(x)            if (unlikely(x))
#endif

#if defined(_UNITTEST) || defined(_UNIT_TEST)
#define ARIES_VIRTUAL virtual
#else
#define ARIES_VIRTUAL
#endif

DECLARE_bool(print_debug_log);
#define ARIES_DEBUG_LOG(level) LOG_IF(level, FLAGS_print_debug_log)
#define ARIES_RPC_DEBUG_LOG(level) \
        LOG_IF(level, FLAGS_print_debug_log) << "[log_id:" << log_id << "] "

#define ARIES_VLOG(level) VLOG(ALOG_##level)
#define ARIES_RPC_VLOG(level) VLOG(ALOG_##level) << "[log_id:" << log_id << "] "

#define ARIES_RPC_LOG(level) LOG(level) << "[log_id:" << log_id << "] "

#define ARIES_RPC_RECV_LOG(level) \
        ARIES_RPC_LOG(level) << "[cmd:" << __FUNCTION__ << "]" \
                << " recv " << request->GetTypeName() \
                << " from " << cntl->remote_side()

#define ARIES_RPC_DONE_LOG(level) \
        ARIES_RPC_LOG(level) << "[cmd:" << __FUNCTION__ << "] finished"

#define RIGISTER_FLAG(name, func)   static const bool dummy_##name = \
    google::RegisterFlagValidator(&FLAGS_##name, &func)

inline void add_basic_metrics_in_heartbeat(aries::pb::BasicMetrics* basic_metrics) {
    basic_metrics->set_cpu_usage(bvar::Variable::describe_exposed("process_cpu_usage"));
    boost::any value;
    if (0 == bvar::Variable::get_exposed("process_io_read_bytes_second", &value)) {
        basic_metrics->set_net_read(boost::any_cast<size_t>(value));
    }
    if (0 == bvar::Variable::get_exposed("process_io_write_bytes_second", &value)) {
        basic_metrics->set_net_write(boost::any_cast<size_t>(value));
    }
    if (0 == bvar::Variable::get_exposed("process_disk_read_bytes_second", &value)) {
        basic_metrics->set_disk_read(boost::any_cast<size_t>(value));
    }
    if (0 == bvar::Variable::get_exposed("process_disk_write_bytes_second", &value)) {
        basic_metrics->set_disk_write(boost::any_cast<size_t>(value));
    }
    std::string memory = bvar::Variable::describe_exposed("process_memory_resident");
    basic_metrics->set_rss_size(atoll(memory.data()));
}

enum ModuleType {
    MODULE_NONE = 0,
    MODULE_MASTER = 1,
    MODULE_ALLOCATOR = 2,
    MODULE_VOLUMESERVICE = 3,
    MODULE_DATAAGENT = 4,
    MODULE_DATANODE = 5,
    MODULE_TINKER = 6,
    MODULE_VALIDATOR = 7,
    MODULE_CLIENT = 8,
};

static inline uint64_t vlet_size_by_type(const VletType& vlet_type) {
    switch (vlet_type) {
    case VLET_TYPE_LINKED_16G_1M_512K:
    case VLET_TYPE_LINKED_16G_2M_512K:
    case VLET_TYPE_APPEND_16G_256M_4K:
    case VLET_TYPE_ZONE_16G_4K:
    case VLET_TYPE_STREAM_16G_4K:
            return 1024 * 1024 * 1024 * 16ull;
    case VLET_TYPE_LINKED_32G_4M_512K:
    case VLET_TYPE_LINKED_32G_8M_512K:
    case VLET_TYPE_LINKED_32G_4M_1M:
    case VLET_TYPE_LINKED_32G_8M_4M:
    case VLET_TYPE_APPEND_32G_256M_4K:
    case VLET_TYPE_ZONE_32G_4K:
    case VLET_TYPE_STREAM_32G_4K:
            return 1024 * 1024 * 1024 * 32ull;
    case VLET_TYPE_LINKED_64G_8M_1M:
    case VLET_TYPE_APPEND_64G_256M_4K:
    case VLET_TYPE_ZONE_64G_4K:
    case VLET_TYPE_STREAM_64G_4K:
            return 1024 * 1024 * 1024 * 64ull;
    case VLET_TYPE_EMPTY:
            return 1ull;                // return 1bytes
    case VLET_TYPE_TEST:
            return 1024 * 1024 * 32ull; // 32MB
    case VLET_TYPE_LINKED_4G_1M_512K:
    case VLET_TYPE_LINKED_4G_2M_512K:
    case VLET_TYPE_LINKED_4G_4M_4M:
    case VLET_TYPE_APPEND_4G_256M_4K:
    case VLET_TYPE_APPEND_4G_64M_4K:
    case VLET_TYPE_STREAM_4G_4K:
            return 1024 * 1024 * 1024 * 4ull;
    case VLET_TYPE_APPEND_8G_256M_4K:
    case VLET_TYPE_LINKED_8G_2M_512K:
    case VLET_TYPE_STREAM_8G_4K:
            return 1024 * 1024 * 1024 * 8ull;
    case VLET_TYPE_LINKED_4G_1M_64K_SSD:
            return 1024 * 1024 * 1024 * 4ull;
    case VLET_TYPE_APPEND_2G_256M_4K:
    case VLET_TYPE_APPEND_2G_64M_4K:
    case VLET_TYPE_STREAM_2G_4K:
            return 1024 * 1024 * 1024 * 2ull;
    default:
            return 0ull;            // return invalid;
    }
    return 0ull;
}

static inline uint64_t block_size_by_type(const BlockSizeType& block_type) {
    switch (block_type) {
    case BLOCK_SIZE_TYPE_256MB:
        return 1024 * 1024 * 256ull;
    case BLOCK_SIZE_TYPE_512MB:
        return 1024 * 1024 * 512ull;
    case BLOCK_SIZE_TYPE_1GB:
        return 1024 * 1024 * 1024ull;
    case BLOCK_SIZE_TYPE_2GB:
        return 1024 * 1024 * 1024 * 2ull;
    case BLOCK_SIZE_TYPE_4GB:
        return 1024 * 1024 * 1024 * 4ull;
    case BLOCK_SIZE_TYPE_8GB:
        return 1024 * 1024 * 1024 * 8ull;
    case BLOCK_SIZE_TYPE_16GB:
        return 1024 * 1024 * 1024 * 16ull;
    case BLOCK_SIZE_TYPE_32GB:
        return 1024 * 1024 * 1024 * 32ull;
    default:
        return 0ull;
    }
    return 0ull;
}

static inline const char* spacestate2str(const int state) {
    SpaceState space_state = static_cast<SpaceState>(state);
    return SpaceState_Name(space_state).data();
}

static inline const char* ectype2str(const int type) {
    ECType ect = static_cast<ECType>(type);
    return ECType_Name(ect).data();
}

static inline const char* vlettype2str(const int type) {
    VletType vt = static_cast<VletType>(type);
    return VletType_Name(vt).data();
}

static inline const char* enginetype2str(const int type) {
    EngineType vt = static_cast<EngineType>(type);
    return EngineType_Name(vt).data();
}

static inline const char* vletstate2str(const int state) {
    VletState vs = static_cast<VletState>(state);
    return VletState_Name(vs).data();
}

static inline const char* nodestate2str(const int state) {
    NodeState ns = static_cast<NodeState>(state);
    return NodeState_Name(ns).data();
}

static inline const char* tape_node_state_to_str(const int state) {
    TapeNodeState ts = static_cast<TapeNodeState>(state);
    return TapeNodeState_Name(ts).data();
}

static inline const char* diskstate2str(const int state) {
    DiskState ns = static_cast<DiskState>(state);
    return DiskState_Name(ns).data();
}

static inline const char* volumestate2str(const int state) {
    VolumeState vs = static_cast<VolumeState>(state);
    return VolumeState_Name(vs).data();
}

static inline const char* volumesessionstate2str(const int state) {
    VolumeSessionState vss = static_cast<VolumeSessionState>(state);
    return VolumeSessionState_Name(vss).data();
}

static inline const char* volumesealstate2str(const int state) {
    VolumeSealState vss = static_cast<VolumeSealState>(state);
    return VolumeSealState_Name(vss).data();
}

static inline const char* volumepurgestate2str(const int state) {
    VolumePurgeState vps = static_cast<VolumePurgeState>(state);
    return VolumePurgeState_Name(vps).data();
}

static inline const char* copyprogress2str(const int progress) {
    CopyProgress cp = static_cast<CopyProgress>(progress);
    return CopyProgress_Name(cp).data();
}

static inline const char* compactprogress2str(const int progress) {
    CompactProgress cp = static_cast<CompactProgress>(progress);
    return CompactProgress_Name(cp).data();
}

static inline const char* compactstate2str(const int state) {
    SpaceCompactState scs = static_cast<SpaceCompactState>(state);
    return SpaceCompactState_Name(scs).data();
}

static inline const char* manualbalancepolicy2str(const int state) {
    ManualBalancePolicy mbp = static_cast<ManualBalancePolicy>(state);
    return ManualBalancePolicy_Name(mbp).data();
}

static inline const char* tinkertasktype2str(const int type) {
    TinkerTaskType ttt = static_cast<TinkerTaskType>(type);
    return TinkerTaskType_Name(ttt).data();
}

static inline const char* vletmovetype2str(const int type) {
    VletMoveType vmt = static_cast<VletMoveType>(type);
    return VletMoveType_Name(vmt).data();
}

struct ECParam {
    int16_t k;
    int16_t n;
    ECParam() : k(0), n(0) {}
    bool operator!=(const ECParam& ecp) const {
        return k != ecp.k || n != ecp.n;
    }
    bool operator==(const ECParam& ecp) const {
        return k == ecp.k && n == ecp.n;
    }
    // operator < just for map
    bool operator<(const ECParam& ecp) const {
        return k < ecp.k || (k == ecp.k && n < ecp.n);
    }
};

struct ECOption {
    ECType type;
    ECParam param;
    ECOption() : type(EC_RS_ISAL) {}
    bool operator!=(const ECOption& eco) const {
        return type != eco.type || param != eco.param;
    }
    bool operator==(const ECOption& eco) const {
        return type == eco.type && param == eco.param;
    }
    // operator < just for map
    bool operator<(const ECOption& eco) const {
        return type < eco.type || (type == eco.type && param < eco.param);
    }
};

static inline ECOption get_ecoption_from_space_info(const ::aries::pb::SpaceInfo& si) {
    ECOption eco;
    eco.type = static_cast<ECType>(si.ec_type());
    eco.param.k = si.k();
    eco.param.n = si.n();
    return eco;
}

enum PriorityLevel {
    PL_NONE = -1,
    PL_HIGH = 0,
    PL_MEDIUM = 1,
    PL_LOW = 2,
};

static const int kPriorityLevelNum = 3;

// version in uint64:
// |--major--|--minor--|------build------|--------------revision--------------|
//     8b        8b            16b                         32b

#pragma pack(push, r1, 1)
struct Version {
    uint8_t major = 0;
    uint8_t minor = 0;
    uint16_t build = 0;
    uint32_t revision = 0;

    static bool validate_version(const char* gflags_filename, const std::string& version_str) {
        if (version_str.empty()) {
            return true;
        }
        auto delimiter_num = 0;
        for (auto c : version_str) {
            if (!isdigit(c) && c != '.') {
                return false;
            }
            if (c == '.') {
                ++delimiter_num;
            }
        }
        std::vector<std::string> field_list;
        std::string delimiter(".");
        auto field_num = Tokenize(version_str, delimiter, &field_list);
        if (field_num < 3 || field_num > 4 || field_num != delimiter_num + (size_t)1) {
            return false;
        }
        for (auto field : field_list) {
            if (field.empty()) {
                return false;
            }
        }
        if (atoi(field_list[0].c_str()) > UINT8_MAX ||
                atoi(field_list[1].c_str()) > UINT8_MAX ||
                atoi(field_list[2].c_str()) > UINT16_MAX) {
            return false;
        }
        if (field_list.size() == 4 && std::stoul(field_list[3]) > UINT32_MAX) {
            return false;
        }
        return true;
    }

    Version(uint64_t version = 0) {
        revision = version;
        build = version >> 32;
        minor = version >> 48;
        major = version >> 56;
    }

    Version(const std::string& version_str) {
        from_string(version_str);
    }

    uint64_t to_uint64() const {
        uint64_t version = 0;
        version += (uint64_t)major << 56;
        version += (uint64_t)minor << 48;
        version += (uint64_t)build << 32;
        version += revision;
        return version;
    }

    std::string to_string(const char sep = '.') const {
        char version[32];
        memset(version, 0, sizeof(version));
        snprintf(version, 32, "%hhu%c%hhu%c%hu%c%u",
                major, sep, minor, sep, build, sep, revision);
        return std::string(version);
    }

    void from_string(const std::string& version_str) {
        std::string str = version_str;
        for (size_t i = 0; i < str.size(); ++i) {
            if (str[i] < '0' || str[i] > '9') { str[i] = ' '; }
        }
        sscanf(str.c_str(), "%hhu %hhu %hu %u", &major, &minor, &build, &revision);
    }

    friend bool operator<(const Version& lhs, const Version& rhs) {
        return lhs.to_uint64() < rhs.to_uint64();
    }
};
#pragma pack(pop, r1)

static inline const std::string& errno2msg(int err) {
    AriesErrno ae = static_cast<AriesErrno>(err);
    return AriesErrno_Name(ae);
}

static inline __uint128_t make_bid(uint64_t vid, uint64_t vbid) {
    return ((__uint128_t)vid << 64) + vbid;
}

static inline uint64_t bid2vid(__uint128_t bid) {
    return (uint64_t)(bid >> 64);
}

static inline uint64_t bid2vbid(__uint128_t bid) {
    return (uint64_t)((bid << 64) >> 64);
}

static inline std::string bid2debug(const __uint128_t &bid) {
    std::ostringstream os;
    const uint64_t *vals = (const uint64_t *) &bid;
    os << "vid:" << vals[1] << " vbid:" << vals[0];
    return os.str();
}

static inline __uint128_t string2uint128(const std::string& s){
    __uint128_t ret = 0;
    for (std::string::const_iterator iter = s.begin(); iter != s.end(); ++iter) {
        ret = ret * 10 + ((__uint128_t)(*iter) - 48);
    }
    return ret;
}

static inline std::string uint1282string(__uint128_t _u128) {
    std::string res;
    while (_u128 > 0) {
        uint32_t tmp = _u128 % 10;
        _u128 = _u128 / 10;
        res = char(tmp + 48) + res;
    }
    return res;
}

// api

static inline std::string bid2str(const uint128_t &bid) {
    std::ostringstream os;
    const uint64_t *vals = (const uint64_t *) &bid;
    os << '(' << vals[1] << ',' << vals[0] << ')';
    return os.str();
}

static inline std::string streamid2str(uint128_t streamid) {
    std::ostringstream os;
    const uint64_t* vals = (const uint64_t*)&streamid;
    os << '(' << vals[1] << ',' << vals[0] << ')';
    return os.str();
}

static inline uint64_t streamid2raftgroupid(uint128_t streamid) {
    return (uint64_t)((streamid << 64) >> 120);
}

static inline uint16_t streamid2clusterid(uint128_t streamid) {
    return (uint16_t)(streamid >> 112);
}

static inline uint64_t streamid2seqid(uint128_t streamid) {
    return (uint64_t)((streamid << 72) >> 72);
}

static inline uint128_t make_streamid(uint16_t clusterid, uint64_t raftgroupid, uint64_t seqid) {
    return ((uint128_t)clusterid << 112) + (raftgroupid << 56) + seqid;
}

static inline void streamid2pb(uint128_t streamid, pb::StreamID* pb) {
    pb->set_high((uint64_t)(streamid >> 64));
    pb->set_low((uint64_t)((streamid << 64) >> 64));
}

static inline uint128_t pb2streamid(const pb::StreamID& streamid) {
    return ((uint128_t)streamid.high() << 64) + streamid.low();
}

static inline uint128_t make_blockid(uint64_t vid, uint32_t block_seq_id, uint32_t vbid) {
    return ((uint128_t)vid << 64) + ((uint64_t)block_seq_id << 32) + vbid;
}

static inline uint64_t blockid2vid(uint128_t blockid) {
    return (uint64_t)(blockid >> 64);
}

static inline uint32_t blockid2blockseqid(uint128_t blockid) {
    return (uint32_t)((blockid << 64) >> 96);
}

static inline uint32_t blockid2vbid(uint128_t blockid) {
    return (uint64_t)((blockid << 96) >> 96);
}

static inline uint32_t vbid2blockseqid(uint64_t vbid) {
    return (uint32_t)(vbid >> 32);
}

static inline std::string blockid2str(uint128_t blockid) {
    std::ostringstream os;
    os << '(' << blockid2vid(blockid) << ','
        << blockid2blockseqid(blockid) << ')';
    return os.str();
}

static inline uint128_t pb2blockid(const pb::BlockID& block_id) {
    return make_blockid(block_id.volume_id(), block_id.block_seq_id(), 0);
}

}   // namespace

// out of namespace, global function
static inline std::ostream& operator<<(std::ostream& os, const __uint128_t &val) {
    const uint64_t *vals = (const uint64_t *) &val;
    return os << "vid:" << vals[1] << " vbid:" << vals[0];
}

static inline std::ostream& operator<<(std::ostream& os, const aries::pb::Status &st) {
    return os << "code:" << st.code() << " msg:" << st.msg();
}

static inline std::ostream& operator<<(std::ostream& os, const aries::pb::Membership &ms) {
    os << "volume_version:" << ms.volume_version() << " membership:[";
    if (ms.vlet_version_set_size() > 0) {
        os << ms.vlet_version_set(0);
        for (int i = 1; i < ms.vlet_version_set_size(); ++i) {
            os << ',' << ms.vlet_version_set(i);
        }
    }
    return os << "]";
}

/*
extern std::string uint64_to_byteunit(uint64_t n);
extern std::string int64_to_byteunit(int64_t n);
extern bool byteunit_to_uint64(const std::string &n, uint64_t *result);
*/
static std::string uint64_to_byteunit(uint64_t n) {
    std::ostringstream oss;
    int power = 0;
    std::string unit;
    if (n >= 1024UL * 1024 * 1024 * 1024 * 1024) {
        power = 5;
        unit = "PB";
    } else if (n >= 1024UL * 1024 * 1024 * 1024) {
        power = 4;
        unit = "TB";
    } else if (n >= 1024UL * 1024 * 1024) {
        power = 3;
        unit = "GB";
    } else if (n >= 1024 * 1024) {
        power = 2;
        unit = "MB";
    } else if (n >= 1024) {
        power = 1;
        unit = "KB";
    }
    if (power > 0) {
        oss << std::setiosflags(std::ios::fixed) << std::setprecision(2)
            << ((double) n / pow(1024, power)) << unit;
    } else {
        oss << n << unit;
    }
    return oss.str();
}

static std::string int64_to_byteunit(int64_t n) {
    if (n >= 0) {
        return uint64_to_byteunit(n);
    }

    std::ostringstream oss;
    oss << "-" << uint64_to_byteunit(-n);
    return oss.str();
}

static bool byteunit_to_uint64(const std::string &n, uint64_t *result) {
    size_t pos = 0;
    for (; pos < n.size(); ++pos) {
        if (n[pos] != '.' && (n[pos] < '0' || n[pos] > '9')) {
            break;
        }
    }
    char *end = NULL;
    *result = strtoull(n.c_str(), &end, 10);
    if (&n[pos] != end) {
        return false;
    }
    std::string unit;
    while (pos < n.size()) {
        unit.append(1, toupper(n[pos++]));
    }
    if (unit.empty() || unit == "B") {
        return true;
    } else if (unit == "K" || unit == "KB") {
        *result *= 1024;
        return true;
    } else if (unit == "M" || unit == "MB") {
        *result *= 1024 * 1024;
        return true;
    } else if (unit == "G" || unit == "GB") {
        *result *= 1024UL * 1024 * 1024;
        return true;
    } else if (unit == "T" || unit == "TB") {
        *result *= 1024UL * 1024 * 1024 * 1024;
        return true;
    }
    return false;
}

static std::string generate_uuid() {
    uuid_t id;
    uuid_generate(id);

    char str[37];
    uuid_unparse(id, str);
    return str;
}

#endif
