// Copyright (c) 2017 Baidu.com, Inc. All Rights Reserved
// Author yaoyiyan(<EMAIL>)
// Date: 2017-01-20

#ifndef BAIDU_INF_ARIES_COMMON_FLOW_CONTROLLER_H
#define BAIDU_INF_ARIES_COMMON_FLOW_CONTROLLER_H

#include <sys/time.h>
#include <cstddef>
#include <stdint.h>

namespace aries {
namespace common {

class FlowController
{
    public:
        /**
         * @brief 构造函数
         *
         * @param [in] flow_var  : uint64_t* 同线程的一个变量，FlowControler从这个变量中读取已经完成的流量，并决定是否进行sleep
         * @param [in] per_sec   : uint32_t 每秒限速数，单位与*flow_var相同
         * @param [in] max_sleep_per_sec   : uint32_t 每秒最多sleep几次，即每发生(per_sec/max_sleep_per_sec)流量，计算一次是否需要sleep
         **/
        FlowController(uint64_t* flow_var, uint32_t per_sec, uint32_t max_sleep_per_sec = 10);

        /**
         * @brief 进行流量控制，每次flow_var变化后，都可以调用control()，内部会根据参数决定是否sleep
         *
         * @return  void
         **/
        void control();

        ~FlowController() {
            _flow_var = NULL;
        }
    private:
        const static uint32_t DEFAULT_SLEEP_EVERY;///< 不限速时每1M进行一次时间采样

    private:
        uint64_t _last_flow;
        uint64_t* _flow_var;
        uint32_t _per_sec;
        uint32_t _sleep_every;
        struct timeval _beg;
};

}}

#endif
