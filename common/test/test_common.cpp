#include "baidu/inf/aries/common/common.h"
#include "bmock.h"

namespace aries {
namespace common { 
class CommonTests : public ::testing::Test {
};

TEST_F(CommonTests, string2disk_type) {
    EXPECT_EQ(DT_HDD, string2disk_type(""));
    EXPECT_EQ(DT_HDD, string2disk_type("HDD"));
    EXPECT_EQ(DT_SMR, string2disk_type("SMR"));
    EXPECT_EQ(DT_SSD, string2disk_type("SSD"));
    EXPECT_EQ(DT_ZONE_SSD, string2disk_type("ZONE_SSD"));
    EXPECT_EQ(DT_INVALID, string2disk_type("ssd"));
}

TEST_F(CommonTests, disk_type2string) {
    EXPECT_EQ("HDD", disk_type2string(DT_HDD));
    EXPECT_EQ("SMR", disk_type2string(DT_SMR));
    EXPECT_EQ("SSD", disk_type2string(DT_SSD));
    EXPECT_EQ("ZONE_SSD", disk_type2string(DT_ZONE_SSD));
}

TEST_F(CommonTests, string2set) {
    auto name_set = string2set(",123,456,789");
    LOG(TRACE) << set2string(name_set);
    EXPECT_TRUE("123" == *(name_set.find("123")));
    EXPECT_TRUE("456" == *(name_set.find("456")));
    EXPECT_TRUE("789" == *(name_set.find("789")));
    name_set = string2set("123,,");
    EXPECT_EQ(1, name_set.size());
    LOG(TRACE) << "set_size:" << name_set.size() << *name_set.begin();
    name_set = string2set("");
    EXPECT_TRUE(name_set.empty());
}

TEST_F(CommonTests, set2string) {
    auto name_str = set2string({"123", "456", "789"});
    LOG(TRACE) << name_str;
    EXPECT_TRUE("123,456,789" == name_str);
    name_str = set2string({"", "123"});
    LOG(TRACE) << name_str;
    EXPECT_TRUE("123" == name_str);
}

TEST_F(CommonTests, parse_vlet_engine_info_from_disk) {
    pb::SpaceInfo info;
    aries::pb::VletEngineInfo vlet_engine_options;
    VletType vlet_type;
    std::string disk_type = "HDD";
    auto pair = info.add_disk_vlet_type();
    pair->set_disk_type(disk_type);
    pair->set_vlet_type((VletType)11);
    bool ret = parse_vlet_engine_info_from_disk(info, disk_type, ENGINE_AUTO, &vlet_type, &vlet_engine_options);
    EXPECT_TRUE(ret);
    EXPECT_EQ(vlet_type, 11);
    EXPECT_EQ(vlet_engine_options.vlet_size(), 32 * GB);
    EXPECT_EQ(vlet_engine_options.block_size(), 4 * MB);
    EXPECT_EQ(vlet_engine_options.max_record_size(), 512 * KB);
    EXPECT_EQ(vlet_engine_options.min_record_size(), 4 * KB);
    EXPECT_EQ(vlet_engine_options.record_gap_page_num(), 1);

    disk_type = "SMR";
    info.clear_disk_vlet_type();
    info.set_vlet_type((VletType)11);
    ret = parse_vlet_engine_info_from_disk(info, disk_type, ENGINE_AUTO, &vlet_type, &vlet_engine_options);
    EXPECT_FALSE(ret);

    disk_type = "SMR";
    pair = info.add_disk_vlet_type();
    pair->set_disk_type(disk_type);
    pair->set_vlet_type((VletType)11);
    ret = parse_vlet_engine_info_from_disk(info, disk_type, ENGINE_AUTO, &vlet_type, &vlet_engine_options);
    EXPECT_FALSE(ret);
    
    disk_type = "smr";
    ret = parse_vlet_engine_info_from_disk(info, disk_type, ENGINE_AUTO, &vlet_type, &vlet_engine_options);
    EXPECT_FALSE(ret);

    disk_type = "SMR";
    pair->set_vlet_type((VletType)80);
    ret = parse_vlet_engine_info_from_disk(info, disk_type, ENGINE_AUTO, &vlet_type, &vlet_engine_options);
    EXPECT_TRUE(ret);
    EXPECT_EQ(vlet_type, 80);
    EXPECT_EQ(vlet_engine_options.vlet_size(), 32 * GB);
    EXPECT_EQ(vlet_engine_options.smr_zone_size(), 256 * MB);
    EXPECT_EQ(vlet_engine_options.align_size(), 4 * KB);
}

TEST_F(CommonTests, vlet_engine_by_vlet_type) {
    aries::pb::VletEngineInfo vlet_engine_options;
    VletType vlet_type = VLET_TYPE_APPEND_32G_256M_4K;
    bool ret = vlet_engine_by_vlet_type(vlet_type, &vlet_engine_options);
    EXPECT_TRUE(ret);

    vlet_type = VLET_TYPE_LINKED_4G_1M_512K;
    ret = vlet_engine_by_vlet_type(vlet_type, &vlet_engine_options);
    EXPECT_TRUE(ret);

    vlet_type = VLET_TYPE_LINKED_32G_8M_512K;
    ret = vlet_engine_by_vlet_type(vlet_type, &vlet_engine_options);
    EXPECT_TRUE(ret);

    vlet_type = VLET_TYPE_EMPTY;
    ret = vlet_engine_by_vlet_type(vlet_type, &vlet_engine_options);
    EXPECT_TRUE(ret);

    vlet_type = VLET_TYPE_TEST;
    ret = vlet_engine_by_vlet_type(vlet_type, &vlet_engine_options);
    EXPECT_TRUE(ret);

    vlet_type = VLET_TYPE_LINKED_VARIENT;
    ret = vlet_engine_by_vlet_type(vlet_type, &vlet_engine_options);
    EXPECT_FALSE(ret);

    vlet_type = VLET_TYPE_APPEND_VARIENT;
    ret = vlet_engine_by_vlet_type(vlet_type, &vlet_engine_options);
    EXPECT_FALSE(ret);
}

TEST_F(CommonTests, drop_vlet_reason2str) {
    EXPECT_EQ("", drop_vlet_reason2str(REASON_OK));
    EXPECT_EQ("due_drop_disk", drop_vlet_reason2str(REASON_DROP_DISK));
    EXPECT_EQ("due_drop_node", drop_vlet_reason2str(REASON_DROP_NODE));
    EXPECT_EQ("due_drop_node_vlets", drop_vlet_reason2str(REASON_DROP_NODE_VLETS));
    EXPECT_EQ("due_drop_vlet", drop_vlet_reason2str(REASON_DROP_VLET));
    EXPECT_EQ("due_drop_volume", drop_vlet_reason2str(REASON_DROP_VOLUME));
    EXPECT_EQ("due_reduce_redundaucy", drop_vlet_reason2str(REASON_REDUCE_REDUNDAUCY));
    EXPECT_EQ("due_unkown", drop_vlet_reason2str(REASON_UNKNOWN));
}

TEST_F(CommonTests, check_vlet_engine_info) {
    aries::pb::VletInfo vlet_info;

    // not set vlet engine options
    vlet_info.set_vlet_type(VLET_TYPE_LINKED_4G_1M_512K);
    auto ret = check_vlet_engine_info(vlet_info);
    EXPECT_TRUE(ret);

    vlet_info.set_vlet_type(VLET_TYPE_APPEND_32G_256M_4K);
    ret = check_vlet_engine_info(vlet_info);
    EXPECT_TRUE(ret);

    vlet_info.set_vlet_type(VLET_TYPE_LINKED_VARIENT);
    ret = check_vlet_engine_info(vlet_info);
    EXPECT_FALSE(ret);

    vlet_info.set_vlet_type(VLET_TYPE_APPEND_VARIENT);
    ret = check_vlet_engine_info(vlet_info);
    EXPECT_FALSE(ret);

    // set vlet engine options
    vlet_info.set_vlet_type(VLET_TYPE_LINKED_4G_1M_512K);
    auto vlet_engine_options = vlet_info.mutable_vlet_engine_options();
    ret = check_vlet_engine_info(vlet_info);
    EXPECT_FALSE(ret);

    vlet_info.set_vlet_type(VLET_TYPE_LINKED_4G_1M_512K);
    vlet_engine_options->set_vlet_size(4 * aries::common::GB);
    ret = check_vlet_engine_info(vlet_info);
    EXPECT_FALSE(ret);

    vlet_info.set_vlet_type(VLET_TYPE_LINKED_4G_1M_512K);
    vlet_engine_options->set_vlet_size(4 * aries::common::GB);
    vlet_engine_options->set_block_size(4 * aries::common::MB);
    vlet_engine_options->set_max_record_size(512 * aries::common::KB);
    vlet_engine_options->set_min_record_size(4 * aries::common::KB);
    ret = check_vlet_engine_info(vlet_info);
    EXPECT_FALSE(ret);

    vlet_info.set_vlet_type(VLET_TYPE_LINKED_4G_1M_512K);
    vlet_engine_options->set_vlet_size(4 * aries::common::GB);
    vlet_engine_options->set_block_size(4 * aries::common::MB);
    vlet_engine_options->set_max_record_size(512 * aries::common::KB);
    vlet_engine_options->set_min_record_size(4 * aries::common::KB);
    vlet_engine_options->set_record_gap_page_num(1);
    ret = check_vlet_engine_info(vlet_info);
    EXPECT_TRUE(ret);

    vlet_info.set_vlet_type(VLET_TYPE_LINKED_VARIENT);
    vlet_engine_options->set_vlet_size(2 * aries::common::GB);
    vlet_engine_options->set_block_size(4 * aries::common::MB);
    vlet_engine_options->set_max_record_size(512 * aries::common::KB);
    vlet_engine_options->set_min_record_size(4 * aries::common::KB);
    vlet_engine_options->set_record_gap_page_num(1);
    ret = check_vlet_engine_info(vlet_info);
    EXPECT_TRUE(ret);

    // Appendvlet
    vlet_info.set_vlet_type(VLET_TYPE_APPEND_32G_256M_4K);
    ret = check_vlet_engine_info(vlet_info);
    EXPECT_FALSE(ret);

    vlet_info.set_vlet_type(VLET_TYPE_APPEND_32G_256M_4K);
    vlet_engine_options->set_vlet_size(32 * aries::common::GB);
    vlet_engine_options->set_smr_zone_size(256 * aries::common::MB);
    vlet_engine_options->set_align_size(4 * aries::common::KB);
    ret = check_vlet_engine_info(vlet_info);
    EXPECT_TRUE(ret);

    vlet_info.Clear();
    vlet_info.set_vlet_type(VLET_TYPE_APPEND_VARIENT);
    auto vlet_engine_options1 = vlet_info.mutable_vlet_engine_options();
    vlet_engine_options1->set_vlet_size(4 * aries::common::GB);
    vlet_engine_options1->set_block_size(4 * aries::common::MB);
    vlet_engine_options1->set_max_record_size(512 * aries::common::KB);
    vlet_engine_options1->set_min_record_size(4 * aries::common::KB);
    vlet_engine_options1->set_record_gap_page_num(1);
    ret = check_vlet_engine_info(vlet_info);
    EXPECT_FALSE(ret);

    vlet_info.set_vlet_type(VLET_TYPE_APPEND_VARIENT);
    vlet_engine_options1 = vlet_info.mutable_vlet_engine_options();
    vlet_engine_options1->set_vlet_size(28 * aries::common::GB);
    vlet_engine_options1->set_smr_zone_size(256 * aries::common::MB);
    vlet_engine_options1->set_align_size(4 * aries::common::KB);
    ret = check_vlet_engine_info(vlet_info);
    EXPECT_TRUE(ret);
}

TEST_F(CommonTests, check_disk_engine_type) {
    
    EXPECT_FALSE(check_disk_engine_type("123", ENGINE_APPEND));
    EXPECT_FALSE(check_disk_engine_type("123", ENGINE_LINKED));
    EXPECT_FALSE(check_disk_engine_type("123", ENGINE_AUTO));

    EXPECT_TRUE(check_disk_engine_type("HDD", ENGINE_LINKED));
    EXPECT_FALSE(check_disk_engine_type("SMR", ENGINE_LINKED));
    EXPECT_TRUE(check_disk_engine_type("CMR", ENGINE_LINKED));
    EXPECT_TRUE(check_disk_engine_type("SSD", ENGINE_LINKED));

    EXPECT_TRUE(check_disk_engine_type("HDD", ENGINE_APPEND));
    EXPECT_TRUE(check_disk_engine_type("SMR", ENGINE_APPEND));
    EXPECT_TRUE(check_disk_engine_type("CMR", ENGINE_APPEND));
    EXPECT_TRUE(check_disk_engine_type("SSD", ENGINE_APPEND));

    EXPECT_TRUE(check_disk_engine_type("HDD", ENGINE_AUTO));
    EXPECT_TRUE(check_disk_engine_type("SMR", ENGINE_AUTO));
    EXPECT_TRUE(check_disk_engine_type("CMR", ENGINE_AUTO));
    EXPECT_TRUE(check_disk_engine_type("SSD", ENGINE_AUTO));
}

TEST_F(CommonTests, is_suitable_for_create) {
    EXPECT_FALSE(is_suitable_for_create("123", ENGINE_APPEND, VLET_TYPE_APPEND_VARIENT));
    EXPECT_FALSE(is_suitable_for_create("123", ENGINE_LINKED, VLET_TYPE_APPEND_VARIENT));
    EXPECT_FALSE(is_suitable_for_create("123", ENGINE_AUTO, VLET_TYPE_APPEND_VARIENT));

    for (auto disk_type : {"HDD", "SSD", "CMR"}) {
        EXPECT_TRUE(is_suitable_for_create(disk_type, ENGINE_APPEND, VLET_TYPE_APPEND_VARIENT));
        EXPECT_FALSE(is_suitable_for_create(disk_type, ENGINE_APPEND, VLET_TYPE_LINKED_VARIENT));
        EXPECT_FALSE(is_suitable_for_create(disk_type, ENGINE_LINKED, VLET_TYPE_APPEND_VARIENT));
        EXPECT_TRUE(is_suitable_for_create(disk_type, ENGINE_LINKED, VLET_TYPE_LINKED_VARIENT));
        EXPECT_TRUE(is_suitable_for_create(disk_type, ENGINE_AUTO, VLET_TYPE_APPEND_VARIENT));
        EXPECT_TRUE(is_suitable_for_create(disk_type, ENGINE_AUTO, VLET_TYPE_LINKED_VARIENT));
    }
    for (auto disk_type : {"SMR"}) {
        EXPECT_TRUE(is_suitable_for_create(disk_type, ENGINE_APPEND, VLET_TYPE_APPEND_VARIENT));
        EXPECT_FALSE(is_suitable_for_create(disk_type, ENGINE_APPEND, VLET_TYPE_LINKED_VARIENT));
        EXPECT_FALSE(is_suitable_for_create(disk_type, ENGINE_LINKED, VLET_TYPE_APPEND_VARIENT));
        EXPECT_FALSE(is_suitable_for_create(disk_type, ENGINE_LINKED, VLET_TYPE_LINKED_VARIENT));
        EXPECT_TRUE(is_suitable_for_create(disk_type, ENGINE_AUTO, VLET_TYPE_APPEND_VARIENT));
        EXPECT_FALSE(is_suitable_for_create(disk_type, ENGINE_AUTO, VLET_TYPE_LINKED_VARIENT));
    }
}

TEST_F(CommonTests, check_vlet_type_convert) {
    // same type convert success
    {
        EXPECT_TRUE(check_vlet_type_convert(VLET_TYPE_LINKED_32G_4M_512K, VLET_TYPE_LINKED_32G_4M_512K));
        EXPECT_TRUE(check_vlet_type_convert(VLET_TYPE_APPEND_32G_256M_4K, VLET_TYPE_APPEND_32G_256M_4K));
    }
    // same engine convert failed
    {
        EXPECT_FALSE(check_vlet_type_convert(VLET_TYPE_LINKED_32G_4M_512K, VLET_TYPE_LINKED_32G_8M_512K));
        EXPECT_FALSE(check_vlet_type_convert(VLET_TYPE_APPEND_32G_256M_4K, VLET_TYPE_APPEND_16G_256M_4K));

    }
    // diff engine diff size convert failed
    {
        EXPECT_FALSE(check_vlet_type_convert(VLET_TYPE_LINKED_32G_4M_512K, VLET_TYPE_APPEND_16G_256M_4K));
    }
    // diff engine same size convert failed
    {
        EXPECT_TRUE(check_vlet_type_convert(VLET_TYPE_LINKED_32G_4M_512K, VLET_TYPE_APPEND_32G_256M_4K));
    }
}
    
}

}
