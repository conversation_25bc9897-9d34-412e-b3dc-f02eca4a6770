#include <random>

#include <gtest/gtest.h>
#include "baidu/inf/aries/common/bmock_util.h"
#include "baidu/inf/aries/common/openssl_aes.h"

namespace aries {
namespace common {

using ::testing::Return;

BMOCK_METHOD3(EVP_EncodeBlock, int(unsigned char*, const unsigned char*, int));
BMOCK_METHOD3(EVP_DecodeBlock, int(unsigned char*, const unsigned char*, int));

class TestEnvironment : public ::testing::Environment {
};

class OpenSSLAESTests : public ::testing::TestWithParam<int> {
    OpenSSLAESTests() {};
    ~OpenSSLAESTests() {};
};

INSTANTIATE_TEST_CASE_P(test_openssl_aes, OpenSSLAESTests, testing::Values(16, 24, 32,48));

TEST_P(OpenSSLAESTests, aes_check_and_pad_test) {
    OpenSSLAES aes_encrypt_test = OpenSSLAES(GetParam());
    int key_len = aes_encrypt_test.key_len();

    // empty str
    std::string empty_str = "";
    aes_encrypt_test.check_and_pad(1, 1, &empty_str);
    EXPECT_EQ(empty_str.size(), key_len);
    empty_str.clear();
    aes_encrypt_test.check_and_pad(2, 1, &empty_str);
    EXPECT_EQ(empty_str.size(), 16);
    // pad
    std::string pad_str = "abcd";
    aes_encrypt_test.check_and_pad(1, 1, &pad_str);
    EXPECT_EQ(pad_str.size(), key_len);
    pad_str = "abcd";
    aes_encrypt_test.check_and_pad(2, 1, &pad_str);
    EXPECT_EQ(pad_str.size(), 16);
    // truncate
    std::string truncate_str;
    for (int i = 0; i < 50; ++i) {
        truncate_str.push_back('a');
    }
    aes_encrypt_test.check_and_pad(1, 1, &truncate_str);
    EXPECT_EQ(truncate_str.size(), key_len);
    truncate_str.clear();
    for (int i = 0; i < 50; ++i) {
        truncate_str.push_back('a');
    }
    aes_encrypt_test.check_and_pad(2, 1, &truncate_str);
    EXPECT_EQ(truncate_str.size(), 16);
}

TEST_P(OpenSSLAESTests, aes_set_key_and_iv_test) {
    int key_len = GetParam();
    OpenSSLAES aes_encrypt_test = OpenSSLAES(key_len);
    
    // set iv and encrypt
    std::string key_str = "abcd";
    std::string iv_str = "dcba";
    AES_KEY aes_key;
    int res = aes_encrypt_test.set_key_and_iv(1, &key_str, &iv_str, &aes_key, EncryptMode::CBC);
    EXPECT_EQ(res, 0);
    // do not set iv and decrypt
    res = aes_encrypt_test.set_key_and_iv(0, &key_str, &iv_str, &aes_key, EncryptMode::ECB);
    EXPECT_EQ(res, 0);
    // unsupported encrypt mode
    res = aes_encrypt_test.set_key_and_iv(2, &key_str, &iv_str, &aes_key, EncryptMode::ECB);
    EXPECT_EQ(res, -1);
}

TEST_P(OpenSSLAESTests, base64_encode_and_decode_test) {
    OpenSSLAES aes_encrypt_test = OpenSSLAES(GetParam());

    {
        // in_str is empty
        std::string empty_in_str = "";
        std::string out_str;
        int res = base64_encode(empty_in_str, 1, &out_str);
        ASSERT_EQ(res, 0);
    }
    {
        // out_str is nullptr
        std::string in_str = "abcd";
        std::string out_str;
        int res = base64_encode(in_str, 1, nullptr);
        ASSERT_EQ(res, -1);
    }
    {
        // encode: encode failed
        std::string in_str = "abcd";
        std::string out_str;
        BMOCK_RESUME(EVP_EncodeBlock, int(unsigned char*, const unsigned char*, int));
        EXPECT_CALL(BMOCK_OBJECT(EVP_EncodeBlock), EVP_EncodeBlock(_, _, _)).WillOnce(Return(-1));
        int res = base64_encode(in_str, 1, &out_str);
        BMOCK_STOP(EVP_EncodeBlock, int(unsigned char*, const unsigned char*, int));
        ASSERT_EQ(res, -1);
    }
    {
        // encode: success, decode failed
        std::string in_str = "abcd";
        std::string out_str;
        std::string de_out_str;
        int res = base64_encode(in_str, 1, &out_str);
        ASSERT_EQ(res, out_str.size());
        EXPECT_EQ(out_str.size(), ceil((double)in_str.size() / 3) * 4);
        BMOCK_RESUME(EVP_DecodeBlock, int(unsigned char*, const unsigned char*, int));
        EXPECT_CALL(BMOCK_OBJECT(EVP_DecodeBlock), EVP_DecodeBlock(_, _, _)).WillOnce(Return(-1));
        res = base64_encode(out_str, 0, &de_out_str);
        ASSERT_EQ(res, -1);
        BMOCK_STOP(EVP_DecodeBlock, int(unsigned char*, const unsigned char*, int));
        std::string error_str = "dew";
        res = base64_encode(error_str, 0, &de_out_str);
        ASSERT_EQ(res, -1);
    }
    {
        // succ
        std::string test_data;
        int len = 10000;
        for (int i = 0; i < len; ++i) {
            int random_num = rand() % 3;
            if (random_num == 0) {
                test_data.push_back(rand() % 10 + 48);
            } else if (random_num == 1) {
                test_data.push_back(rand() % 26 + 97);
            } else {
                test_data.push_back(rand() % 26 + 65);
            }
        }

        for (int i = 0; i < len; ++i) {
            std::string in_str = test_data.substr(0, i);
            std::string out_str;
            std::string de_out_str;
            int res = base64_encode(in_str, 1, &out_str);
            ASSERT_EQ(res, out_str.size());
            ASSERT_EQ(out_str.size(), ceil((double)in_str.size() / 3) * 4);
            res = base64_encode(out_str, 0, &de_out_str);
            ASSERT_EQ(res, i);
            ASSERT_EQ(de_out_str, in_str);
        }

    }
}

TEST_P(OpenSSLAESTests, aes_encrypt_and_decrypt_test) {
    OpenSSLAES aes_encrypt_test = OpenSSLAES(GetParam());

    {
        // in_str is empty
        std::string plaintext = "abcd";
        std::string empty_plaintext = "";
        std::string key = "9fa35951cb60e594603041befaa48164";
        std::string iv = "9fa35951cb60e594";
        std::string ciphertext;
        std::string out_plaintext;
        
        int res = aes_encrypt_test.aes_encrypt(empty_plaintext, 1, key, &ciphertext);
        ASSERT_EQ(res, 0);
        ASSERT_EQ(ciphertext.size(), 0);
        res = aes_encrypt_test.aes_encrypt(ciphertext, 0, key, &out_plaintext);
        ASSERT_EQ(res, 0);
        ASSERT_EQ(out_plaintext.size(), 0);
    }
    {   
        // out_str_ptr is nullptr
        std::string plaintext = "abcd";
        std::string key = "9fa35951cb60e594603041befaa48164";
        std::string iv = "9fa35951cb60e594";
        
        int res = aes_encrypt_test.aes_encrypt(plaintext, 1, key, nullptr);
        ASSERT_EQ(res, -1);
        res = aes_encrypt_test.aes_encrypt(plaintext, 0, key, nullptr);
        ASSERT_EQ(res, -1);
    }
    {
        // unsupported operation type
        std::string plaintext = "abcd";
        std::string key = "9fa35951cb60e594603041befaa48164";
        std::string iv = "9fa35951cb60e594";
        std::string ciphertext;

        int res = aes_encrypt_test.aes_encrypt(plaintext, 2, key, &ciphertext);
        ASSERT_EQ(res, -1);
    }
    {
        std::string plaintext = "abcd";
        std::string key = "9fa35951cb60e594603041befaa48164";
        std::string iv = "9fa35951cb60e594";
        std::string ciphertext;

        // encrypt with unknown encrypt mode
        int res = aes_encrypt_test.aes_encrypt(plaintext, 1, key, &ciphertext, EncryptMode::INVALID, iv);
        ASSERT_EQ(res, -1);

        // decrypt with unknown encrypt mode
        res = aes_encrypt_test.aes_encrypt(plaintext, 0, key, &ciphertext, EncryptMode::INVALID, iv);
        ASSERT_EQ(res, -1);
    }
    {
        // succ
        std::string test_data;
        std::string key = "9fa35951cb60e594603041befaa48164";
        std::string iv = "9fa35951cb60e594";
        
        int len = 1000;
        std::random_device rd;
        // prepare data
        for (int i = 0; i < len; ++i) {
            int random_num = rand() % 3;
            if (random_num == 0) {
                test_data.push_back(rand() % 10 + 48);
            } else if (random_num == 1) {
                test_data.push_back(rand() % 26 + 97);
            } else {
                test_data.push_back(rand() % 26 + 65);
            }
        }
        
        for (int i = 0; i < len; ++i) {
            std::string ciphertext;
            std::string base64_en_str;
            std::string base64_de_str;
            std::string plaintext = test_data.substr(0, i);
            ASSERT_EQ(plaintext.size(), i);
            
            // 1. cbc
            // encrypt
            int res = aes_encrypt_test.aes_encrypt(plaintext, 1, key, &ciphertext, EncryptMode::CBC, iv);
            ASSERT_EQ(res, 0);
            // base64 encode
            res = base64_encode(ciphertext, 1, &base64_en_str);
            ASSERT_EQ(res, (int)ceil((double)ciphertext.size() / 3) * 4);
            ASSERT_EQ(base64_en_str.size(), (int)ceil((double)ciphertext.size() / 3) * 4);
            // base64 decode
            res = base64_encode(base64_en_str, 0, &base64_de_str);
            ASSERT_EQ(res, ciphertext.size());
            ASSERT_EQ(base64_de_str.size(), ciphertext.size());
            // decrypt
            std::string out_plaintext;
            res = aes_encrypt_test.aes_encrypt(base64_de_str, 0, key, &out_plaintext, EncryptMode::CBC, iv);
            ASSERT_EQ(res, 0);
            ASSERT_EQ(out_plaintext.size(), i);
            ASSERT_TRUE(out_plaintext == plaintext);
            
            // 2.ecb
            // encrypt
            res = aes_encrypt_test.aes_encrypt(plaintext, 1, key, &ciphertext, EncryptMode::ECB);
            ASSERT_EQ(res, 0);
            // base64 encode
            res = base64_encode(ciphertext, 1, &base64_en_str);
            ASSERT_EQ(res, (int)ceil((double)ciphertext.size() / 3) * 4);
            ASSERT_EQ(base64_en_str.size(), (int)ceil((double)ciphertext.size() / 3) * 4);
            // base64 decode
            res = base64_encode(base64_en_str, 0, &base64_de_str);
            ASSERT_EQ(res, ciphertext.size());
            ASSERT_EQ(base64_de_str.size(), ciphertext.size());
            // decrypt
            res = aes_encrypt_test.aes_encrypt(base64_de_str, 0, key, &out_plaintext, EncryptMode::ECB);
            ASSERT_EQ(res, 0);
            ASSERT_EQ(out_plaintext.size(), i);
            ASSERT_TRUE(out_plaintext == plaintext);

            plaintext.clear();
        }
    }
}

TEST_P(OpenSSLAESTests, aes_encrypt_long_str_test) {
    OpenSSLAES aes_encrypt_test = OpenSSLAES(GetParam());

    std::string test_data;
    std::string key = "9fa35951cb60e594603041befaa48164";
    std::string iv = "9fa35951cb60e594";
    
    int len = 1024 * 1024 * 100;
    std::random_device rd;
    // prepare data
    for (int i = 0; i < len; ++i) {
        int random_num = rand() % 3;
        if (random_num == 0) {
            test_data.push_back(rand() % 10 + 48);
        } else if (random_num == 1) {
            test_data.push_back(rand() % 26 + 97);
        } else {
            test_data.push_back(rand() % 26 + 65);
        }
    }

    for (int i = 1; i <= 10; ++i) {
        std::string ciphertext;
        std::string base64_en_str;
        std::string base64_de_str;
        size_t sub_len = i * 1024 * 1024 * 10;
        std::string plaintext = test_data.substr(0, sub_len);
        ASSERT_EQ(plaintext.size(), sub_len);
        
        // 1. cbc
        // encrypt
        int res = aes_encrypt_test.aes_encrypt(plaintext, 1, key, &ciphertext, EncryptMode::CBC, iv);
        ASSERT_EQ(res, 0);
        // base64 encode
        res = base64_encode(ciphertext, 1, &base64_en_str);
        ASSERT_EQ(res, (int)ceil((double)ciphertext.size() / 3) * 4);
        ASSERT_EQ(base64_en_str.size(), (int)ceil((double)ciphertext.size() / 3) * 4);
        // base64 decode
        res = base64_encode(base64_en_str, 0, &base64_de_str);
        ASSERT_EQ(res, ciphertext.size());
        ASSERT_EQ(base64_de_str.size(), ciphertext.size());
        // decrypt
        std::string out_plaintext;
        res = aes_encrypt_test.aes_encrypt(base64_de_str, 0, key, &out_plaintext, EncryptMode::CBC, iv);
        ASSERT_EQ(res, 0);
        ASSERT_EQ(out_plaintext.size(), sub_len);
        ASSERT_TRUE(out_plaintext == plaintext);
    }
}

}
}