#include <gtest/gtest.h>
#include <random>
#include "baidu/inf/aries/common/count_min_sketch.h"
#include "baidu/inf/aries/common/common.h"

namespace aries {
namespace common {
typedef unsigned __int128 uint128_t;

template<typename CMS>
int64_t sanitize_ct(int64_t ct, CMS& cms) {
    if (ct > cms.get_max_count()) {
        return cms.get_max_count();
    } else {
        return static_cast<int64_t>(ct);
    }
}

template<typename CMS>
class CountMinSketchTest : public testing::Test {
protected:
    uint128_t get_random_int128() {
        uint64_t hi = base::fast_rand();
        uint64_t lo = base::fast_rand();
        uint128_t ret = hi;
        ret = ret << 64;
        return ret + lo;
    }
    void test_simple() {
        CMS cms{100, 3};
        std::mt19937_64 rg{1};
        std::vector<uint128_t> keys;
        for (uint32_t i = 0; i < 10; i++) {
            keys.push_back(get_random_int128());
            for (uint32_t j = 0; j < i; j++) {
                cms.increment(keys[i]);
            }
        }

        for (uint32_t i = 0; i < keys.size(); i++) {
            LOG(NOTICE) << i << " " << cms.get_count(keys[i]);
            EXPECT_GE(cms.get_count(keys[i]), sanitize_ct(i, cms));
        }
    }

    void test_remove() {
        CMS cms{100, 3};
        std::mt19937_64 rg{1};
        std::vector<uint128_t> keys;
        for (uint32_t i = 0; i < 10; i++) {
            keys.push_back(get_random_int128());
            for (uint32_t j = 0; j < i; j++) {
                cms.increment(keys[i]);
            }
        }

        for (uint32_t i = 0; i < keys.size(); i++) {
            cms.reset_count(keys[i]);
            EXPECT_EQ(0, cms.get_count(keys[i]));
        }
    }

    void test_reset() {
        CMS cms{100, 3};
        std::mt19937_64 rg{1};
        std::vector<uint128_t> keys;
        for (uint32_t i = 0; i < 10; i++) {
            keys.push_back(get_random_int128());
            cms.increment(keys[i]);
        }

        cms.reset();
        for (uint32_t i = 0; i < keys.size(); i++) {
            EXPECT_EQ(0, cms.get_count(keys[i]));
        }
    }

    void test_probability_constructor() {
        CMS cms{0.01, 0.95, 0, 0};
        EXPECT_EQ(200, cms.width());
        EXPECT_EQ(5, cms.depth());
    }

    void test_default() {
        CMS cms{};
        uint128_t key = base::fast_rand();
        EXPECT_EQ(cms.get_count(key), 0);
        EXPECT_NO_THROW(cms.increment(key));
        EXPECT_EQ(cms.get_count(key), 0);
        EXPECT_EQ(0, cms.get_byte_size());
        cms.decay_counts_by(0.1);
        EXPECT_EQ(0, cms.get_count(key));
    }

    void test_move() {
        CMS cms{40, 5};
        uint128_t key = base::fast_rand();
        int cnt = 20;
        for (int i = 0; i < cnt; i++) {
            cms.increment(key);
        }

        EXPECT_GE(cnt, cms.get_count(key));

        auto cms2 = std::move(cms);
        EXPECT_GE(cnt, cms2.get_count(key));
        EXPECT_EQ(0, cms.get_count(key));
    }

    void test_decay_counts() {
        CMS cms{100, 3};
        std::mt19937_64 rg{1};
        std::vector<uint128_t> keys;
        // key i is incremented i times.
        for (uint32_t i = 0; i < 1000; i++) {
            keys.push_back(get_random_int128());
            for (uint32_t j = 0; j < i; j++) {
                cms.increment(keys[i]);
            }
        }

        for (uint32_t i = 0; i < keys.size(); i++) {
            EXPECT_GE(cms.get_count(keys[i]), sanitize_ct(i, cms));
        }

        const double decay_factor = 0.5;
        cms.decay_counts_by(decay_factor);

        for (uint32_t i = 0; i < keys.size(); i++) {
            EXPECT_GE(cms.get_count(keys[i]),
            // Floor to int for a fair comparison. Otherwise may we may be
            // expecting 127 >= 127.5 for uint8_t
                      std::floor(sanitize_ct(i, cms) * decay_factor));
        }
    }

    void test_over_flow() {
        CMS cms{10, 3};
        uint128_t key = base::fast_rand();
        uint64_t max = static_cast<uint64_t>(cms.get_max_count()) + 2;
        // Skip the test for large count type since it times out the unit test.
        if (max < std::numeric_limits<uint32_t>::max()) {
            for (uint64_t i = 0; i < max; i++) {
                cms.increment(key);
            }

            ASSERT_EQ(cms.get_count(key), sanitize_ct(max, cms));
            ASSERT_EQ(cms.get_saturated_counts(), 3);
        }
    }
};

using CountMinSketch4_64 = CountMinSketchSmall<uint64_t>;
using CountMinSketch4_128 = CountMinSketchSmall<uint128_t>;

using CountMinSketch32_128 = CountMinSketchBase<uint32_t, uint128_t>;
using CountMinSketch8_128 = CountMinSketchBase<uint8_t, uint128_t>;
using CountMinSketch16_128 = CountMinSketchBase<uint16_t, uint128_t>;

typedef ::testing::Types<CountMinSketch4_64, CountMinSketch4_128, CountMinSketch32_128, CountMinSketch8_128,  CountMinSketch16_128>
        CMSTypes;

TYPED_TEST_CASE(CountMinSketchTest, CMSTypes);

TYPED_TEST(CountMinSketchTest, Simple) { this->test_simple(); }

TYPED_TEST(CountMinSketchTest, Remove) { this->test_remove(); }

TYPED_TEST(CountMinSketchTest, Reset) { this->test_reset(); }

TYPED_TEST(CountMinSketchTest, ProbabilityConstructor) {
    this->test_probability_constructor();
}

// ensure all the apis return meaningful results on a default constructed
// empty object.
TYPED_TEST(CountMinSketchTest, Default) { this->test_default(); }

TYPED_TEST(CountMinSketchTest, Move) { this->test_move(); }

TYPED_TEST(CountMinSketchTest, DecayCounts) { this->test_decay_counts(); }

// Make sure we don't crash when the count overflows.
TYPED_TEST(CountMinSketchTest, Overflow) { this->test_over_flow(); }

}
}