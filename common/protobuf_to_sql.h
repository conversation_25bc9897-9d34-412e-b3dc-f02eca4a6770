// Copyright (c) 2022 Baidu.com, Inc. All Rights Reserved
//
// Author: chenz<PERSON><EMAIL>

#pragma once

#include <sstream>
#include <base/logging.h>

#include "cppconn/resultset_metadata.h"

#include "baidu/inf/aries-api/common/proto/db.pb.h"

namespace aries {

struct ProtoColumn {
    std::string name;
    int index;
    ::google::protobuf::FieldDescriptor::CppType type = ::google::protobuf::FieldDescriptor::CppType::MAX_CPPTYPE;
};

inline void analyze_sql_result(sql::ResultSetMetaData* result, std::vector<ProtoColumn>* columns) {
    columns->clear();
    for (uint32_t i = 0; i < result->getColumnCount(); i++) {
        ProtoColumn tmp;
        tmp.index = i;
        tmp.name = result->getColumnName(i + 1);
        switch (result->getColumnType(i + 1)){
            case sql::DataType::BIGINT:
                if (result->isSigned(i + 1)) {
                    tmp.type = ::google::protobuf::FieldDescriptor::CppType::CPPTYPE_INT64;
                } else {
                    tmp.type = ::google::protobuf::FieldDescriptor::CppType::CPPTYPE_UINT64;
                }
                break;
            case sql::DataType::INTEGER:
                if (result->isSigned(i + 1)) {
                    tmp.type = ::google::protobuf::FieldDescriptor::CppType::CPPTYPE_INT32;
                } else {
                    tmp.type = ::google::protobuf::FieldDescriptor::CppType::CPPTYPE_UINT32;
                }
                break;
            case sql::DataType::DOUBLE:
                tmp.type = ::google::protobuf::FieldDescriptor::CppType::CPPTYPE_DOUBLE;
                break;
            case sql::DataType::REAL:
                tmp.type = ::google::protobuf::FieldDescriptor::CppType::CPPTYPE_FLOAT;
                break;
            case sql::DataType::TINYINT:
                tmp.type = ::google::protobuf::FieldDescriptor::CppType::CPPTYPE_BOOL;
                break;
            case sql::DataType::VARCHAR:
                tmp.type = ::google::protobuf::FieldDescriptor::CppType::CPPTYPE_STRING;
                break;
            default:
                LOG(WARNING) << "analyze_sql_result find unknown sql type, column:" << tmp.name;
                tmp.type = ::google::protobuf::FieldDescriptor::CppType::MAX_CPPTYPE;
                break;
        }
        columns->push_back(tmp);
    }
}

// [in] msg
// [in] table_name
// [in] primary_keys
// [in] long_string_keys
// [in] index_keys
// [in] self_increment_primary_key
// [in] existing_columns
// [out] output_sql
// [out] columns
inline bool analyze_proto_to_sql(::google::protobuf::Message* msg,
        const std::string& table_name, std::vector<std::string> primary_keys, 
        std::set<std::string> long_string_keys, std::vector<std::string> index_keys,
        bool self_increment_primary_key, const std::vector<ProtoColumn>& existing_columns,
        std::string* output_sql, std::vector<ProtoColumn>* columns) {
    if (primary_keys.size() == 0 || table_name.empty() || msg == nullptr) {
        LOG(WARNING) << "analyze_proto_to_sql failed due to input error";
        return false;
    }
    auto descriptor = msg->GetDescriptor();
    if (self_increment_primary_key && primary_keys.size() != 1) {
        LOG(WARNING) << "analyze_proto_to_sql failed due to primary_keys is improper as self_increment_primary_key"
            << ", table_name:" << table_name;
        return false;
    }
    std::string& sql = *output_sql;
    sql.clear();
    columns->clear();

    size_t target_columns_count = (size_t)descriptor->field_count() + (size_t)self_increment_primary_key;
    if (existing_columns.size() > target_columns_count) {
        LOG(WARNING) << "existing columns more than proto field count"
            << ", target_columns_count:" << target_columns_count
            << ", existing_columns_count:" << existing_columns.size();
    }
    if (existing_columns.size() == 0) {
        sql = "CREATE TABLE `" + table_name + "`(\n";
    } else {
        sql = "ALTER TABLE `" + table_name + "` \n";
    }
    if (self_increment_primary_key) {
        if (existing_columns.size() > 0) {
            if (*primary_keys.begin() != existing_columns[0].name) {
                LOG(WARNING) << "analyze_proto_to_sql failed due to unmatched column name, index:0"
                    << ", primary_key:" << *primary_keys.begin()
                    << ", db_column_name:" << existing_columns[0].name;
                return false;
            }
        } else {
            std::string colume_define = "`" + *primary_keys.begin()
                + "` BIGINT NOT NULL AUTO_INCREMENT,\n";
            sql += colume_define;
        }
    }
    for (auto i = 0; i < descriptor->field_count(); i++) {
        auto field_descriptor = descriptor->field(i);
        size_t e_index = i + (size_t)self_increment_primary_key;
        if (existing_columns.size() > e_index) {
            if (field_descriptor->name() != existing_columns[e_index].name) {
                LOG(WARNING) << "analyze_proto_to_sql failed due to unmatched column name, index:" << e_index
                    << ", proto_column_name:" << field_descriptor->name()
                    << ", db_column_name:" << existing_columns[e_index].name;
                return false;
            }
            if (field_descriptor->cpp_type() != existing_columns[e_index].type) {
                LOG(WARNING) << "analyze_proto_to_sql failed due to unmatched column type, index:" << e_index
                    << ", column_name:" << field_descriptor->name();
                return false;
            }
            columns->push_back(existing_columns[e_index]);
            (*columns)[i].index = i;
            continue;
        }
        std::string sql_type;
        std::string default_value;
        switch (field_descriptor->cpp_type()) {
            case ::google::protobuf::FieldDescriptor::CppType::CPPTYPE_INT32:
                sql_type = "INT";
                default_value = std::to_string(field_descriptor->default_value_int32());
                break;
            case ::google::protobuf::FieldDescriptor::CppType::CPPTYPE_UINT32:
                sql_type = "INT UNSIGNED";
                default_value = std::to_string(field_descriptor->default_value_uint32());
                break;
            case ::google::protobuf::FieldDescriptor::CppType::CPPTYPE_INT64:
                sql_type = "BIGINT";
                default_value = std::to_string(field_descriptor->default_value_int64());
                break;
            case ::google::protobuf::FieldDescriptor::CppType::CPPTYPE_UINT64:
                sql_type = "BIGINT UNSIGNED";
                default_value = std::to_string(field_descriptor->default_value_uint64());
                break;
            case ::google::protobuf::FieldDescriptor::CppType::CPPTYPE_BOOL:
                sql_type = "BOOL";
                default_value = std::to_string(field_descriptor->default_value_bool());
                break;
            case ::google::protobuf::FieldDescriptor::CppType::CPPTYPE_FLOAT:
                sql_type = "FLOAT";
                default_value = std::to_string(field_descriptor->default_value_float());
                break;
            case ::google::protobuf::FieldDescriptor::CppType::CPPTYPE_DOUBLE:
                sql_type = "DOUBLE";
                default_value = std::to_string(field_descriptor->default_value_double());
                break;
            case ::google::protobuf::FieldDescriptor::CppType::CPPTYPE_STRING:
                if (long_string_keys.count(field_descriptor->name()) != 0) {
                    sql_type = "VARCHAR(10000)";
                } else {
                    sql_type = "VARCHAR(255)";
                }
                default_value = "'" + field_descriptor->default_value_string() + "'";
                break;
            default:
                LOG(WARNING) << "analyze_proto_to_sql failed due to wrong proto field type," 
                    << ", field:" << field_descriptor->name();
                return false;
        }
        std::string colume_define = "`" + field_descriptor->name() + "` " + sql_type
            + " NOT NULL DEFAULT " + default_value + ",\n";
        if (existing_columns.size() != 0) {
            sql += "ADD COLUMN ";
        }
        sql += colume_define;
        ProtoColumn tmp;
        tmp.index = i;
        tmp.name = field_descriptor->name();
        tmp.type = field_descriptor->cpp_type();
        columns->push_back(tmp);
    }

    if (existing_columns.size() == 0) {
        for (auto& key : index_keys) {
            sql += "INDEX (`";
            sql += key;
            sql += "`),\n";
        }
        sql += "PRIMARY KEY (";
        for (auto& key : primary_keys) {
            sql += "`";
            sql += key;
            sql += "`,";
        }
        sql.pop_back();
        sql += ")\n";
        sql += ") ENGINE=InnoDB;";
    } else if (existing_columns.size() >= target_columns_count){
        sql.clear();
    } else {
        sql.pop_back();
        sql.pop_back();
        sql += ";";
    }
    LOG(TRACE) << "analyze_proto_to_sql success, table:" << table_name
        << ", ddl_sql:" << sql;
    return true;
};

inline std::string generate_upsert_sql(const std::string& table_name,
        const std::vector<ProtoColumn>& columns, bool use_replace) {
    if (columns.size() == 0) {
        return "";
    }
    std::string sql = "";
    if (use_replace) {
        sql = "REPLACE INTO " + table_name + "(";
    } else {
        sql = "INSERT IGNORE INTO " + table_name + "(";
    }
    
    for (auto& col : columns) {
        sql += col.name;
        sql += ",";
    }
    sql.pop_back();
    sql += ") values(";
    for (uint32_t i = 0; i < columns.size(); i++) {
        sql += "?,";
    }
    sql.pop_back();
    sql += ")";
    LOG(TRACE) << "generate_upsert_sql success, table:" << table_name
        << ", sql:" << sql;
    return sql;
}

}
