// Copyright (c) 2016 Baidu.com, Inc. All Rights Reserved
// Author ch<PERSON><PERSON><PERSON><PERSON>(chen<PERSON><PERSON><EMAIL>)
// Date: Tue Jul 26 14:59:15 CST 2016

#ifndef BAIDU_INF_ARIES_COMMON_LOCK_H
#define BAIDU_INF_ARIES_COMMON_LOCK_H

#include <base/time.h>
#include "bthread.h"
#include <time.h>

namespace aries {
namespace common {

/////////////////////////////////////// bthread //////////////////////////////////////////
/////////////////////////////////////// bthread //////////////////////////////////////////
/////////////////////////////////////// bthread //////////////////////////////////////////
class FutexLock {
public:
    FutexLock() {
        pthread_mutex_init(&_mutex, NULL);
    }

    ~FutexLock() {
        pthread_mutex_destroy(&_mutex);
    }

    void lock() {
        pthread_mutex_lock(&_mutex);
    }

    void unlock() {
        pthread_mutex_unlock(&_mutex);
    }

    pthread_mutex_t*    get_mutex() { // non-const
        return &_mutex;
    }

private:
    pthread_mutex_t _mutex;
private:
    DISALLOW_COPY_AND_ASSIGN(FutexLock);
};

class ScopedFutexLock {
public:
    ScopedFutexLock(pthread_mutex_t *mutex) {
        _mutex = mutex;
        pthread_mutex_lock(_mutex);
    }
    explicit ScopedFutexLock(FutexLock& mutex)
        : _mutex(mutex.get_mutex()) {
        pthread_mutex_lock(_mutex);
    }

    void unlock() {
        if (_mutex != NULL) {
            pthread_mutex_unlock(_mutex);
            _mutex = NULL;
        }
    }

    ~ScopedFutexLock() {
        unlock();
    }
private:
    pthread_mutex_t*    _mutex;
private:
    DISALLOW_COPY_AND_ASSIGN(ScopedFutexLock);
};

class MutexLock {
public:
    MutexLock() {
        bthread_mutex_init(&_mutex, NULL);
    }

    ~MutexLock() {
        bthread_mutex_destroy(&_mutex);
    }

    void lock() {
        bthread_mutex_lock(&_mutex);
    }

    void unlock() {
        bthread_mutex_unlock(&_mutex);
    }

    bthread_mutex_t*    get_mutex() { // non-const
        return &_mutex;
    }

private:
    bthread_mutex_t _mutex;
private:
    DISALLOW_COPY_AND_ASSIGN(MutexLock);
};

class ConditionLock {
public:
    ConditionLock() {
        bthread_mutex_init(&_mutex, NULL);
        bthread_cond_init(&_condition, NULL);
    }

    ~ConditionLock() {
        bthread_mutex_destroy(&_mutex);
        bthread_cond_destroy(&_condition);
    }

    bthread_mutex_t*    get_mutex() { // non-const
        return &_mutex;
    }

    void wait() {
        bthread_cond_wait(&_condition, get_mutex());
    }

    void signal() {
        bthread_cond_signal(&_condition);
    }

    int wait_until(const struct timespec* ts) {
        return bthread_cond_timedwait(&_condition, get_mutex(), ts);
    }

    int wait_ms(const uint64_t ms) {
        auto ts = base::milliseconds_from_now(ms);
        return bthread_cond_timedwait(&_condition, get_mutex(), &ts);
    }

    void signal_all() {
        bthread_cond_broadcast(&_condition);
    }

private:
    bthread_mutex_t         _mutex;
    bthread_cond_t          _condition;
private:
    DISALLOW_COPY_AND_ASSIGN(ConditionLock);
};

class ScopedMutexLock {
public:
    explicit ScopedMutexLock(MutexLock& mutex)
        : _mutex(mutex.get_mutex()) {
        bthread_mutex_lock(_mutex);
    }

    explicit ScopedMutexLock(ConditionLock& cond)
        : _mutex(cond.get_mutex()) {
        bthread_mutex_lock(_mutex);
    }

    ~ScopedMutexLock() {
        bthread_mutex_unlock(_mutex);
    }

private:
    bthread_mutex_t*    _mutex;
private:
    DISALLOW_COPY_AND_ASSIGN(ScopedMutexLock);
};

class SyncPoint {
public:
    explicit SyncPoint(int count) : _count(count) {}

    int count() {
        ScopedMutexLock lock(_count_lock);
        return _count;
    }

    void wait() {
        ScopedMutexLock lock(_count_lock);
        while (_count > 0) {
            _count_lock.wait();
        }

    }

    int wait_until(const timespec* ts) {
        ScopedMutexLock lock(_count_lock);
        if (_count > 0) {
            return _count_lock.wait_until(ts);
        }
        return 0;
    }

    int wait_ms(const uint64_t ms) {
        ScopedMutexLock lock(_count_lock);
        if (_count > 0) {
            return _count_lock.wait_ms(ms);
        }
        return 0;
    }

    int signal() {
        ScopedMutexLock lock(_count_lock);
        --_count;
        if (_count == 0) {
            _count_lock.signal_all();
        }
        return _count;
    }
private:
    int _count;
    ConditionLock _count_lock;
private:
    DISALLOW_COPY_AND_ASSIGN(SyncPoint);
};

class StatefulSyncPoint {
public:
    explicit StatefulSyncPoint(int32_t count = 0, int32_t wait_num = 0) : 
            _count(count), _wait_num(wait_num) {}

    int count() {
        ScopedMutexLock lock(_count_lock);
        return _count;
    }

    void wait() {
        ScopedMutexLock lock(_count_lock);
        while (_count > 0 && _wait_num > 0) {
            _count_lock.wait();
        }
    }
    bool wait(int millis) {
        timespec ts = base::milliseconds_from_now(millis);
        ScopedMutexLock lock(_count_lock);
        if (millis > 0) {
            while (_count > 0 && _wait_num > 0) {
                if (_count_lock.wait_until(&ts)) {
                    break;
                }
            }
        }
        return (_count <= 0);
    }

    bool wait_signal(int millis) {
        timespec ts = base::milliseconds_from_now(millis);
        ScopedMutexLock lock(_count_lock);
        if (millis > 0) {
            if (_wait_num > 0) {
                _count_lock.wait_until(&ts);
            }
        }
        return (_wait_num <= 0);
    }

    bool signal(bool is_change_count) {
        ScopedMutexLock lock(_count_lock);
        --_wait_num;
        if (is_change_count) {
            --_count;
        }
        if (_count <= 0 || _wait_num <= 0) {
            _count_lock.signal_all();
        }
        return (_wait_num <= 0);
    }

private:
    int32_t _count;// response num that we could deal, need not to continue waiting
    int32_t _wait_num;// all response
    common::ConditionLock _count_lock;

private:
    DISALLOW_COPY_AND_ASSIGN(StatefulSyncPoint);
};

class ConcurrencyLimitSyncPoint {
public:
    explicit ConcurrencyLimitSyncPoint(int32_t count = 0, int32_t wait_num = 0, int32_t max_concurrency = -1) : 
            _max_concurrency(max_concurrency), _count(count), _wait_num(wait_num) {}

    int count() {
        ScopedMutexLock lock(_count_lock);
        return _count;
    }

    void add_concurrency() {
        ScopedMutexLock lock(_concurrency_lock);
        while (_max_concurrency > 0 && _running_num >= _max_concurrency) {
            _concurrency_lock.wait();
        }
        ++_running_num;
    }
    void wait() {
        ScopedMutexLock lock(_count_lock);
        while (_count > 0 && _wait_num > 0) {
            _count_lock.wait();
        }
    }
    bool wait(int millis) {
        timespec ts = base::milliseconds_from_now(millis);
        ScopedMutexLock lock(_count_lock);
        if (millis > 0) {
            while (_count > 0 && _wait_num > 0) {
                if (_count_lock.wait_until(&ts)) {
                    break;
                }
            }
        }
        return (_count <= 0);
    }
    bool wait_signal(int millis) {
        timespec ts = base::milliseconds_from_now(millis);
        ScopedMutexLock lock(_count_lock);
        if (millis > 0) {
            if (_wait_num > 0) {
                _count_lock.wait_until(&ts);
            }
        }
        return (_wait_num <= 0);
    }
    bool signal(bool succ) {
        {
            ScopedMutexLock lock(_concurrency_lock);
            --_running_num;
            _concurrency_lock.signal_all();
        }
        ScopedMutexLock lock(_count_lock);
        --_wait_num;
        if (succ) {
            --_count;
        }
        if (_count <= 0 || _wait_num <= 0) {
            _count_lock.signal_all();
        }
        return (_wait_num <= 0);
    }

private:
    const int32_t _max_concurrency;
    int32_t _count = 0;// response num that succ
    int32_t _wait_num = 0;// all requests
    int32_t _running_num = 0;// running requests
    common::ConditionLock _count_lock;
    common::ConditionLock _concurrency_lock;

private:
    DISALLOW_COPY_AND_ASSIGN(ConcurrencyLimitSyncPoint);
};

class SyncPointGuard {
public:
    SyncPointGuard(SyncPoint* sync_point) : _sync_point(sync_point) {}

    ~SyncPointGuard() {
        if (_sync_point) {
            _sync_point->signal();
        }
    }

    SyncPoint* relese() {
        SyncPoint* ret = _sync_point;
        _sync_point = nullptr;
        return ret;
    }

private:
    SyncPoint* _sync_point = nullptr;
};

template<typename T>
class ConcurrentQueue {
public:
    ConcurrentQueue() {
        bthread_mutex_init(&_mutex, NULL);
        bthread_cond_init(&_cond, NULL);
    }
    ~ConcurrentQueue() {
        bthread_mutex_destroy(&_mutex);
        bthread_cond_destroy(&_cond);
    }
    void put(const T &t) {
        bthread_mutex_lock(&_mutex);
        _queue.push_back(t);
        bthread_cond_signal(&_cond);
        bthread_mutex_unlock(&_mutex);
    }
    bool take(T *t, const timespec* ts) {
        bthread_mutex_lock(&_mutex);
        while (_queue.empty()) {
            if (bthread_cond_timedwait(&_cond, &_mutex, ts) != 0) {
                bthread_mutex_unlock(&_mutex);
                return false;
            }
        }
        *t = _queue.front();
        _queue.pop_front();
        bthread_mutex_unlock(&_mutex);
        return true;
    }
private:
    bthread_mutex_t _mutex;
    bthread_cond_t _cond;

    std::deque<T> _queue;
private:
    DISALLOW_COPY_AND_ASSIGN(ConcurrentQueue);
};

/////////////////////////////////////// pthread //////////////////////////////////////////
/////////////////////////////////////// pthread //////////////////////////////////////////
/////////////////////////////////////// pthread //////////////////////////////////////////

class ReadWriteLock {
public:
    ReadWriteLock() {
        pthread_mutex_init(&_lock, NULL);
        pthread_key_create(&_rwlock_key, NULL);
    }

    ~ReadWriteLock() {
        pthread_key_delete(_rwlock_key);
        for (size_t i = 0; i < _tls_locks.size(); ++i) {
            delete _tls_locks[i];
        }
        pthread_mutex_destroy(&_lock);
    }

    pthread_mutex_t *r_lock() {
        pthread_mutex_t *tls_lock = (pthread_mutex_t *) pthread_getspecific(_rwlock_key);
        if (tls_lock == NULL) {
            tls_lock = new pthread_mutex_t;
            pthread_mutex_init(tls_lock, NULL);
            pthread_setspecific(_rwlock_key, tls_lock);
            pthread_mutex_lock(&_lock);
            _tls_locks.push_back(tls_lock);
            pthread_mutex_unlock(&_lock);
        }
        pthread_mutex_lock(tls_lock);
        return tls_lock;
    }

    void r_unlock() {
        pthread_mutex_t *tls_lock = (pthread_mutex_t *) pthread_getspecific(_rwlock_key);
        if (tls_lock != NULL) {
            pthread_mutex_unlock(tls_lock);
        }
    }

    void w_lock() {
        pthread_mutex_lock(&_lock);
        for (size_t i = 0; i < _tls_locks.size(); ++i) {
            pthread_mutex_lock(_tls_locks[i]);
        }
    }

    void w_unlock() {
        for (size_t i = 0; i < _tls_locks.size(); ++i) {
            pthread_mutex_unlock(_tls_locks[i]);
        }
        pthread_mutex_unlock(&_lock);
    }

private:
    std::vector<pthread_mutex_t *> _tls_locks;
    pthread_mutex_t _lock;
    pthread_key_t _rwlock_key;
    DISALLOW_COPY_AND_ASSIGN(ReadWriteLock);
};

class ReadLockGuard {
public:
    ReadLockGuard(ReadWriteLock& rw_lock) {
        _lock = rw_lock.r_lock();
    }
    ~ReadLockGuard() {
        pthread_mutex_unlock(_lock);
    }
private:
    pthread_mutex_t *_lock;
    DISALLOW_COPY_AND_ASSIGN(ReadLockGuard);
};

class WriteLockGuard {
public:
    WriteLockGuard(ReadWriteLock& rw_lock) {
        _rwlock = &rw_lock;
        _rwlock->w_lock();
    }
    ~WriteLockGuard() {
        _rwlock->w_unlock();
    }
private:
    ReadWriteLock *_rwlock;
    DISALLOW_COPY_AND_ASSIGN(WriteLockGuard);
};

} // end namespace of common
} // end namespace of aries

#endif // BAIDU_INF_ARIES_COMMON_LOCK_H

/* vim: set ts=4 sw=4 sts=4 tw=100 */
