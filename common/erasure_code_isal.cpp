/**
 * ARIES - A Reliable and Impressive EB-level Storage.
 * Copyright (c) 2016 Baidu.com, Inc. All Rights Reserved.
 *
 * Author: <PERSON><PERSON>liang (<EMAIL>)
 * Date: 2016-09-01
 *
 */
#include <base/logging.h>
#include <stdio.h>
#include <iostream>
#include <stdlib.h>
#include "isa-l.h"
#include "erasure_code_isal.h"

namespace aries {

const int GFTBLS_CEFFICIENT = 32; // gftbls space must be 32*k*rows 

int ErasureCodeISAL::init(int k, int n) {
    if (_init_flag) {
        //have init before, now free memory
        free(_encode_matrix);
    }
    _k = k;
    _n = n;
    _encode_matrix = (unsigned char*)malloc(_n * _k);
    gf_gen_cauchy1_matrix(_encode_matrix, _n, _k);
    _init_flag = true;
    return 0;
}
ErasureCodeISAL::~ErasureCodeISAL() {
    if (_init_flag) {
        free(_encode_matrix);
    }
}
int ErasureCodeISAL::_check_enc_data(const std::map<int, char *> &enc_data,
                                     unsigned int expect_num) {
    if (enc_data.size() < expect_num) {
        LOG(WARNING) << " enc_data size:" << enc_data.size() << ", expect shard num:" << expect_num;
        return -1;
    } 
    std::map<int, char *>::const_iterator ite = enc_data.begin();
    for (; ite != enc_data.end(); ++ite) {
        if (ite->first < 0 || ite->first >= _n || NULL == ite->second){ 
            LOG(WARNING) << " error shard_id:" << ite->first;
            return -1; 
        }
    }
    return 0;
}
int ErasureCodeISAL::encode(const char * ori_data, 
                            const int ori_save_shard_num,
                            const char * last_ori_data, 
                            std::map<int, char *> &enc_data,
                            int size) {
    if (NULL == ori_data || (!_init_flag) ||
            (0 != _check_enc_data(enc_data, static_cast<unsigned int>(_n)))) {
        LOG(WARNING) << "ori_data null ptr or not init yet or check enc_data failed";
        return -1;
    }
    int parts = _n - _k;
    unsigned char rs_tbls[_k * parts * GFTBLS_CEFFICIENT];
    ec_init_tables(_k, parts, &_encode_matrix[_k * _k], rs_tbls);
    unsigned char * data_ptrs[_k];
    for (int i = 0; i < _k; ++i) {
        if (i < ori_save_shard_num) {
            data_ptrs[i] = (unsigned char *)(const_cast<char *>(ori_data)) + i * size;
        } else {
            data_ptrs[i] = (unsigned char *)(const_cast<char *>(last_ori_data)) + (i - ori_save_shard_num) * size;
        }
    }
    unsigned char * code_ptrs[parts];
    for (int i = 0; i < parts; ++i) {
        code_ptrs[i] = (unsigned char *)(enc_data[_k + i]);
    }
    ec_encode_data(size, _k, parts, rs_tbls, data_ptrs, code_ptrs);
    return 0;
}
int ErasureCodeISAL::_gen_invert_matrix(const std::map<int, char *> &enc_data,
                                       unsigned char * decode_matrix) {
    unsigned char decode_matrix_temp[_k * _k];
    int pos = 0;
    for (auto it = enc_data.begin(); it != enc_data.end() && pos < _k; ++it) {
        memcpy(&decode_matrix_temp[pos * _k], &_encode_matrix[it->first * _k], _k);
        ++pos;
    }
    return gf_invert_matrix(decode_matrix_temp, decode_matrix, _k);
} 
int ErasureCodeISAL::_decode_data(const std::map<int, char *> &enc_data, 
                                  unsigned char * decode_src[], 
                                  unsigned char * decode_dst[], 
                                  int size) {
    //generate decode matrix
    unsigned char decode_matrix[_k * _k];
    int ret = _gen_invert_matrix(enc_data, decode_matrix);
    if (ret != 0) {
        return -1;
    }
    int nloss = 0;
    for (int i = 0; i < _k; ++i) {
        if (enc_data.find(i) == enc_data.end()) {
            if (nloss != i) {
                memcpy(&decode_matrix[nloss * _k], &decode_matrix[i * _k], _k);
                decode_dst[nloss] = decode_dst[i];
            }
            ++nloss;
        }
    }
    unsigned char rs_tbls[_k * nloss * GFTBLS_CEFFICIENT];
    ec_init_tables(_k, nloss, decode_matrix, rs_tbls);
    ec_encode_data(size, _k, nloss, rs_tbls, decode_src, decode_dst);
    return 0;
}
int ErasureCodeISAL::decode(const std::map<int, char *> &enc_data, 
                            char * ori_data,
                            int size) {
    // param check
    if ((!_init_flag) || (0 != _check_enc_data(enc_data, static_cast<unsigned int>(_k)))) {
        LOG(WARNING) << "check enc_data failed or not init yet";
        return -1;
    }
    unsigned char * decode_src[_k];
    unsigned char * decode_dst[_k];
    std::map<int, char *>::const_iterator ite = enc_data.begin();
    bool no_loss = true;
    for (int i = 0; i < _k; ++i, ++ite) {
        if (ite->first != i) {
            no_loss = false;
        }
        decode_src[i] = (unsigned char *)(ite->second);
        decode_dst[i] = (unsigned char *)(ori_data) + i * size;
        if (ite->first < _k) {
            unsigned char * decode_dst_p = (unsigned char *)(ori_data) + (ite->first) * size;
            if (decode_src[i] != decode_dst_p) {
                memcpy(decode_dst_p, decode_src[i], size);
            }
        }
    }
    if (no_loss) {
        return 0;
    }
    if (_decode_data(enc_data, decode_src, decode_dst, size)) {
        LOG(WARNING) << "decode data failed";
        return -1;
    }
    return 0;
}
int ErasureCodeISAL::recover(const std::map<int, char *> &enc_data, 
                             std::map<int, char *> &recover_data,
                             int size) {
    // param check
    if ((!_init_flag) || (0 != _check_enc_data(enc_data, static_cast<unsigned int>(_k)))) {
        LOG(WARNING) << "check enc_data failed or not init yet";
        return -1;
    }
    //do recover index and memory check here
    int want_to_recover_num = recover_data.size();
    if (want_to_recover_num <= 0 || want_to_recover_num > _n - _k) {
        LOG(WARNING) << "recover_data size error,"
                << " want_to_recover_num: " << want_to_recover_num;
        return -1;
    }
    std::map<int, char *>::const_iterator ite = recover_data.begin();
    for (; ite != recover_data.end(); ++ite) {
        if ((ite->first >= _n) || (ite->first < 0) || (NULL == ite->second)) {
            LOG(WARNING) << "recovery index should be in range, [0, n-1],"
                " or recover_data memory null error ";
            return -1;
        }
    }
    unsigned char * decode_src[_k];
    std::map<int, char *>::const_iterator ite_enc_data = enc_data.begin();
    for (int i = 0; i < _k; ++i, ++ite_enc_data) {
        decode_src[i] = (unsigned char *)(ite_enc_data->second);
    }
    // gen invert matrix using encode_data
    unsigned char * decode_matrix = (unsigned char *)malloc(_k * _k);
    int ret = _gen_invert_matrix(enc_data, decode_matrix);
    //if input matrix is singular, invert failed 
    if (ret != 0) {
        LOG(WARNING) << "gf_invert_matrix failed, input matrix is singular matrix";
        free(decode_matrix);
        return -1;
    }
    // construct tmp recovery matrix
    int recovery_matrix_tmp_len = _k * want_to_recover_num;
    unsigned char * recovery_matrix_tmp = (unsigned char *)malloc(recovery_matrix_tmp_len);
    memset(recovery_matrix_tmp, 0, recovery_matrix_tmp_len);
    unsigned char * code_ptrs[want_to_recover_num];
    int recovery_matrix_pos = 0;
    int code_ptrs_pos = 0;
    std::map<int, char *>::const_iterator ite_recovery = recover_data.begin();
    for (; ite_recovery != recover_data.end(); ++ite_recovery) {
        //memory to recovery
        code_ptrs[code_ptrs_pos] = (unsigned char *)(ite_recovery->second);
        code_ptrs_pos++;
        //generate matrix
        for (int j = 0; j < _k; ++j) {
            int index = (ite_recovery->first) * _k + j;
            recovery_matrix_tmp[recovery_matrix_pos++] = _encode_matrix[index];
        }
    }
    // construct recovery matrix, recovery_matrix_tmp * decode_matrix
    unsigned char * recovery_matrix = (unsigned char *)malloc(_k * want_to_recover_num);
    for (int i = 0; i < want_to_recover_num; ++i) {
        for (int j = 0; j < _k; ++j) {
            unsigned char s = 0;
            for (int k = 0; k < _k; ++k) {
                unsigned char number_a = recovery_matrix_tmp[i * _k + k];
                unsigned char number_b = decode_matrix[_k * k + j];
                s ^= gf_mul(number_a, number_b);
            }
            recovery_matrix[i * _k + j] = s;
        }
    }
    // do encovery work
    unsigned char *rs_tbls = (unsigned char *)malloc(_k * _n * GFTBLS_CEFFICIENT);
    ec_init_tables(_k, want_to_recover_num, recovery_matrix, rs_tbls);
    ec_encode_data(size, _k, want_to_recover_num, rs_tbls, decode_src, code_ptrs);
    free(rs_tbls);

    free(recovery_matrix); 
    free(recovery_matrix_tmp);
    free(decode_matrix);
    return 0;
}
}

