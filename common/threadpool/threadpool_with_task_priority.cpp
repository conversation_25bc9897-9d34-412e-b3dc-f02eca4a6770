// Copyright (c) 2019 Baidu.com, Inc. All Rights Reserved.
// Author: men<PERSON><PERSON><PERSON><PERSON>@baidu.com
// Data: Mon Oct 28 19:08:26 CST 2019
// Filename: threadpool_with_task_priority.cpp

#include "base/time.h"

#include "baidu/inf/aries-api/common/proto/error_code.pb.h"
#include "baidu/inf/aries-api/common/lock.h"
#include "baidu/inf/aries/common/threadpool/threadpool_with_task_priority.h"

namespace aries {
namespace common {
namespace threadpool {

void PriorityWorker::execute_task(const PriorityTask& task) {
    _execution_begin_time_us = base::gettimeofday_us();
    task.callable(_threadpool->is_stop());
    _execution_begin_time_us = 0;
}

void PriorityWorker::run() {
    while (true) {
        PriorityTaskRef task = _threadpool->get_task_for_worker();
        if (task != nullptr) {
            execute_task(*task);
            continue;
        }
        assert(_threadpool->is_stop());
        break;
    }
}

::base::Status ThreadpoolWithTaskPriority::start(const StartOption& start_option) {
    _start_option = start_option;
    const uint64_t thread_num = start_option.thread_num;
    if (thread_num == 0) {
        return ::base::Status(static_cast<int>(ThreadpoolErrno::INVALID_ARGUMENT), 
                "thread num is illegal");
    }
    if (start_option.priority_level_num > MaxPriorityLevelNum) {
        return ::base::Status(static_cast<int>(ThreadpoolErrno::INVALID_ARGUMENT), 
                "priority level num is illegal");
    }

    std::unique_lock<std::mutex> lock_guard(_mutex);

    if (is_running()) {
        return ::base::Status(static_cast<int>(ThreadpoolErrno::RUNNING), 
                "state has been running");
    }
    if (is_stop()) {
        return ::base::Status(static_cast<int>(ThreadpoolErrno::STOPPED),
                "state has been stop");
    }

    // Change state before starting threads because threads will check threadpool state
    // immediately after it has been started.
    assert(_thread_pool_state == ThreadpoolState::UNINITIALIZED);
    _thread_pool_state = ThreadpoolState::RUNNING;

    _workers.reserve(thread_num);
    for (uint64_t i = 0; i < thread_num; ++i) {
        _workers.emplace_back(new PriorityWorker(this, i));
    }
    for (const auto& worker : _workers) {
        worker->start();
    }
    return ::base::Status::OK();
}

::base::Status ThreadpoolWithTaskPriority::stop() {
    {
        std::unique_lock<std::mutex> lock_guard(_mutex);

        if (is_uninitialized()) {
            return ::base::Status(static_cast<int>(ThreadpoolErrno::UNINITIALIZED),
                    "thread pool state is init");
        }
        if (is_stop()) {
            return ::base::Status(static_cast<int>(ThreadpoolErrno::STOPPED),
                    "thread pool state is stop");
        }
        assert(_thread_pool_state == ThreadpoolState::RUNNING);
        _thread_pool_state = ThreadpoolState::STOPPING;
    }

    _cond.notify_all();
    for (const auto& worker : _workers) {
        worker->join();
    }
    _thread_pool_state = ThreadpoolState::STOPPED;
    return ::base::Status::OK();
}

::base::Status ThreadpoolWithTaskPriority::submit(std::function<void(bool)> callable) {
    TaskOption option;
    return submit(callable, option);
}

::base::Status ThreadpoolWithTaskPriority::submit(
        std::function<void(bool)> callable,
        const TaskOption& task_option) {
    if (!callable) {
        return ::base::Status(static_cast<int>(ThreadpoolErrno::INVALID_ARGUMENT),
                "callable is illegal");
    }
    PriorityTaskRef task(new PriorityTask(callable, task_option));
    assert(task != nullptr);
    {
        std::unique_lock<std::mutex> lock_guard(_mutex);
        if (is_uninitialized()) {
            return ::base::Status(static_cast<int>(ThreadpoolErrno::UNINITIALIZED),
                    "thread pool state is init");
        }
        if (is_stop()) {
            return ::base::Status(static_cast<int>(ThreadpoolErrno::STOPPED),
                    "thread pool state is stop");
        }

        assert(_thread_pool_state == ThreadpoolState::RUNNING);
        _task_queues[task_option.priority].push_back(std::move(task));
        ++_queue_sizes[task_option.priority];
    }
    _cond.notify_one();

    return ::base::Status::OK();
}

ThreadpoolWithTaskPriority::~ThreadpoolWithTaskPriority() {
    (void)stop();
    assert(_thread_pool_state == ThreadpoolState::STOPPED
            || _thread_pool_state == ThreadpoolState::UNINITIALIZED);
}

uint64_t ThreadpoolWithTaskPriority::get_pending_task_count() const{
    return get_accumulate_pending_task_count(_start_option.priority_level_num - 1);
}

uint64_t ThreadpoolWithTaskPriority::get_accumulate_pending_task_count(uint32_t priority) const {
    assert(priority < _start_option.priority_level_num);
    uint64_t size = 0;
    std::unique_lock<std::mutex> lock_guard(_mutex);
    for (uint32_t level = 0; level <= priority; ++level) {
        size += _queue_sizes.at(level);
    }
    return size;
}

uint64_t ThreadpoolWithTaskPriority::get_single_pending_task_count(uint32_t priority) const {
    std::unique_lock<std::mutex> lock_guard(_mutex);
    return _queue_sizes.at(priority);
}

uint64_t ThreadpoolWithTaskPriority::get_max_task_execution_time_us() const {
    uint64_t begin_time = 0;
    std::unique_lock<std::mutex> lock_guard(_mutex);
    for (const auto& worker : _workers) {
        uint64_t timestamp = worker->get_execution_begin_time_us();
        if (timestamp != 0 && begin_time == 0) {
            begin_time = timestamp;
        }
        if (timestamp != 0 && timestamp < begin_time) {
            begin_time = timestamp;
        }
    }
    if (begin_time != 0) {
        uint64_t now = ::base::gettimeofday_us();
        return (now > begin_time) ? now - begin_time : 0;
    } else {
        return 0;
    }
}

PriorityTaskRef ThreadpoolWithTaskPriority::get_task_for_worker() {
    std::unique_lock<std::mutex> lock_guard(_mutex);
    while (true) {
        auto calc_size = [this] () {
            uint64_t size = 0;
            for (uint32_t level = 0; level < _start_option.priority_level_num; ++level) {
                size += _queue_sizes.at(level);
            }
            return size;
        };
        while (calc_size() == 0 && is_running()) {
           _cond.wait(lock_guard);
        }
        if (!is_running()) {
            return nullptr;
        }
        PriorityTaskRef task = nullptr;
        for (uint32_t level = 0; level < _start_option.priority_level_num; ++level) {
            auto& queue = _task_queues.at(level);
            if (!queue.empty()) {
                task = std::move(queue.front());
                queue.pop_front();
                --_queue_sizes[level];
                break;
            }
        }
        if (task != nullptr) {
            return task;
        }
    }
    assert(0);
}

}
}
}
