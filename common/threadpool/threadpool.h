#pragma once

#include "base/status.h"
#include <folly/executors/thread_factory/ThreadFactory.h>

namespace aries {
namespace common {
namespace threadpool {

enum class ThreadpoolState {
    UNINITIALIZED = 0,
    RUNNING = 1,
    STOPPING = 2,
    STOPPED = 3
};

enum class ThreadpoolErrno {
    OK = 0,
    INVALID_ARGUMENT = 1,
    UNINITIALIZED = 2,
    RUNNING = 3,
    STOPPED = 4
};

using ThreadFactory = folly::ThreadFactory;

class SimpleThreadFactory : public ThreadFactory {
 public:
    using Func = folly::Func;
    std::thread newThread(Func&& func) override {
          return std::thread(
            [func = std::move(func)]() mutable {
                func();
            });
  }
};

}
}
}
