/**
 * ARIES - A Reliable and Impressive EB-level Storage.
 * Copyright (c) 2019 Baidu.com, Inc. All Rights Reserved.
 *
 * Author: <PERSON><PERSON><PERSON> (<EMAIL>)
 * Date: 2019/02/28
 *
 */

#include <base/logging.h>
#include "baidu/inf/aries-api/common/bvar_service.h"
#include "baidu/inf/aries-api/common/rpc_call.h"
#include "baidu/inf/aries-api/common/bvar_monitor.h"

namespace aries {
namespace common {

DEFINE_int32(bvar_service_port, 8701, "bvar monitor service port");
void BvarMonitorServiceImpl::convert_bvar_to_pb(uint64_t log_id, const std::string& name,
                    const std::vector<std::pair<uint64_t, boost::any>>& time_value_list,
                    aries::pb::GetBvarResponse* response) {
    if (response == nullptr) {
        return;
    }
    if (time_value_list.size() == 0) {
        return;
    } 
    boost::any first_value;
    int pos = 0;
    while (pos < (int)time_value_list.size() && first_value.type() == typeid(void)) {
        first_value = time_value_list[pos++].second;
    }
    if (first_value.type() == typeid(uint64_t) 
        || first_value.type() == typeid(int64_t)
        || first_value.type() == typeid(int32_t)
        || first_value.type() == typeid(uint32_t)
        || first_value.type() == typeid(bvar::Stat)) {
        auto bvars = response->add_int_bvars();
        bvars->set_name(name);
        for (const auto& pair : time_value_list) {
            uint64_t timestamp = pair.first;
            bvars->add_timestamp(timestamp);
            const auto& value = pair.second;
            if (value.type() == typeid(uint64_t)) {
                bvars->add_value(boost::any_cast<uint64_t>(value));
            } else if (value.type() == typeid(int64_t)) {
                bvars->add_value(boost::any_cast<int64_t>(value));
            } else if (value.type() == typeid(int32_t)) {
                bvars->add_value(boost::any_cast<int32_t>(value));
            } else if (value.type() == typeid(uint32_t)) {
                bvars->add_value(boost::any_cast<uint32_t>(value));
            } else if (value.type() == typeid(bvar::Stat)) {
                bvars->add_value(boost::any_cast<bvar::Stat>(value).get_average_int());
            } else {
                bvars->add_value(0);
                ARIES_RPC_LOG(TRACE) << "bvar:" << name << ", type:" << value.type().name()
                    << ", type error, use defalut value:0";
            }
        }
    } else if (first_value.type() == typeid(double)
        || first_value.type() == typeid(float)) {
        auto bvars = response->add_double_bvars();
        bvars->set_name(name);
        for (const auto& pair : time_value_list) {
            uint64_t timestamp = pair.first;
            bvars->add_timestamp(timestamp);
            const auto& value = pair.second;
            if (value.type() == typeid(double)) {
                bvars->add_value(boost::any_cast<double>(value));
            } else if (value.type() == typeid(float)) {
                bvars->add_value(boost::any_cast<float>(value));
            } else {
                bvars->add_value(0);
                ARIES_RPC_LOG(TRACE) << "bvar:" << name << ", type:" << value.type().name() 
                    << ", type error, use defalut value:0";
            }
        }
    } else if (first_value.type() == typeid(bool)) {
        auto bvars = response->add_bool_bvars();
        bvars->set_name(name);
        for (const auto& pair : time_value_list) {
            uint64_t timestamp = pair.first;
            bvars->add_timestamp(timestamp);
            const auto& value = pair.second;
            if (value.type() == typeid(bool)) {
                bvars->add_value(boost::any_cast<bool>(value));
            } else {
                bvars->add_value(false);
                ARIES_RPC_LOG(TRACE) << "bvar:" << name << ", type:" << value.type().name() 
                    << ", type error, use defalut value:false";
            }
        }
    } else if (first_value.type() == typeid(std::string)) {
        auto bvars = response->add_string_bvars();
        bvars->set_name(name);
        for (const auto& pair : time_value_list) {
            uint64_t timestamp = pair.first;
            bvars->add_timestamp(timestamp);
            const auto& value = pair.second;
            if (value.type() == typeid(std::string)) {
                bvars->add_value(boost::any_cast<std::string>(value));
            } else {
                bvars->add_value("");
                ARIES_RPC_LOG(TRACE) << "bvar:" << name << ", type:" << value.type().name() 
                    << ", type error, use defalut value:""(empty)";
            }
        }
    } else {
        ARIES_RPC_VLOG(DEBUG) << "bvar:" 
            << name << ", type:" << first_value.type().name() << " is not supported";
    } 
    return;
}

void BvarMonitorServiceImpl::get_bvar(::google::protobuf::RpcController* controller,
            const ::aries::pb::GetBvarRequest* request,
            ::aries::pb::GetBvarResponse* response,
            ::google::protobuf::Closure* done) {
    baidu::rpc::Controller* cntl = static_cast<baidu::rpc::Controller*>(controller);
    uint64_t log_id = cntl->log_id();

    baidu::rpc::ClosureGuard done_guard(done);
    if (!_bvar_monitor) {
        response->mutable_status()->set_code(AIE_FAIL);
        response->mutable_status()->set_msg("bvar monitor not ready");
        return;
    }
    uint64_t last_collect_time = request->last_timestamp();
    // get all bvar
    if (request->bvar_names_size() == 0) {
        std::map<std::string, std::vector<std::pair<uint64_t, boost::any>>> name_time_value_map;
        int ret = _bvar_monitor->get_all_bvar(last_collect_time, &name_time_value_map);
        if (ret != 0) {
            response->mutable_status()->set_code(AIE_FAIL);
            response->mutable_status()->set_msg("failed to get all bvar");
        }
        for (const auto & [name, time_value_list] : name_time_value_map) {
            convert_bvar_to_pb(log_id, name, time_value_list, response);
            ARIES_RPC_VLOG(DEBUG) << "bvar:" << name << " get bvar success, size:" 
                << time_value_list.size() << " last_collect_time:" << last_collect_time; 
        }
        return;
    }
    // get bvar by name
    uint64_t time_stamp = base::gettimeofday_s();
    std::vector<std::pair<uint64_t, boost::any> > time_value_list;
    for (int i = 0; i < request->bvar_names_size(); ++i) {
        auto bvar_name = request->bvar_names(i);
        int ret = _bvar_monitor->get_bvar(bvar_name, last_collect_time, &time_value_list);
        if (ret == 0) {
            convert_bvar_to_pb(log_id, bvar_name, time_value_list, response);
        } else {
            ARIES_RPC_LOG(TRACE) << "bvar:" << bvar_name << " get bvar by name failed";
        }
        ARIES_RPC_VLOG(DEBUG) << "bvar:" << bvar_name << " get bvar success, size:" 
                << time_value_list.size() << " last_collect_time:" << last_collect_time; 
    } 
    
    return;
}

}
}
