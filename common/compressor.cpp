// Copyright (c) 2018 Baidu.com, Inc. All Rights Reserved
// Author zhuming(<EMAIL>)
// Date: 2018/04/11
#include <baidu/rpc/server.h>
#include <base/crc32c.h>
#include <baidu/third-party/lz4/include/lz4.h>
#include <baidu/third-party/zstd/include/zstd.h>
#include <baidu/third-party/snappy/include/snappy-c.h>
#include <brotli/encode.h>
#include <brotli/decode.h>
#include <brunsli/encode.h>
#include <brunsli/decode.h>
#include "isa-l.h"
#include "baidu/inf/aries-api/common/compressor.h"
#include "baidu/inf/aries-api/common/proto/data_agent.pb.h"
#include "baidu/inf/aries-api/common/rpc_call.h"
#include "baidu/inf/aries-api/common/common.h"
namespace aries {
namespace common {

base::Status Compressor::compress_rpc_call(const CompressOption* option,
        const char* input, size_t input_size, char* dest, size_t* max_out_size) {
    base::Status s;
    const RpcCompressOption* rpc_option = dynamic_cast<const RpcCompressOption*>(option);
    uint64_t log_id = rpc_option->log_id;
    aries::pb::CompressRequest request;
    aries::pb::CompressResponse response;

    request.set_token(rpc_option->token);
    request.set_compress_type(rpc_option->compress_type);
    request.set_compress_level(rpc_option->compress_level);
    request.set_data_crc(base::crc32c::Value(input, input_size));
    request.set_timeout_ms(rpc_option->timeout_ms);
    request.set_priority(rpc_option->priority);
    request.set_min_compress_ratio(rpc_option->min_compress_ratio);
    baidu::rpc::Controller cntl;
    cntl.request_attachment().append(input, input_size);
    cntl.set_log_id(rpc_option->log_id);
    cntl.set_timeout_ms(rpc_option->timeout_ms);
    cntl.set_max_retry(rpc_option->compress_max_retry);
    cntl.set_backup_request_ms(rpc_option->timeout_ms / 2);
    aries::pb::CompressService_Stub client(rpc_option->compress_channel);
    client.compress(&cntl, &request, &response, NULL);
    auto remote = base::endpoint2str(cntl.remote_side());
    if (cntl.Failed()) {
         ARIES_RPC_LOG(TRACE) << "compress failed,"
            << " retry_times:" << cntl.retried_count()
            << " has back request:" << cntl.has_backup_request()
            << " remote:" << remote << " rpc_error:"
            << cntl.ErrorCode() << " (" << cntl.ErrorText() << ")"
            << " latency_us:" << cntl.latency_us();
        if (cntl.ErrorCode() == baidu::rpc::ELIMIT) {
            s.set_error(AIE_BUSY, "compress busy");
        } else {
            s.set_error(AIE_FAIL, "rpc error");
        }
        return s;
    } else if (response.status().code() != aries::AIE_OK) {
        ARIES_RPC_LOG(TRACE) << "compress failed,"
            << " retry_times:" << cntl.retried_count()
            << " has back request:" << cntl.has_backup_request()
            << " remote:" << remote << " request error: "
            << response.status().code() << " (" << response.status().msg() << ")"
            << " latency_us:" << cntl.latency_us() ;
        if (response.status().code() == aries::AIE_RATIO_TOO_SMALL) {
            *max_out_size = response.compress_data_len();
        }
        s.set_error(response.status().code(), response.status().msg());
        return s;
    } else {
        ARIES_RPC_LOG(TRACE) << "remote compress successed, remote:" << remote;
    }

    size_t out_size = cntl.response_attachment().size();
    if (out_size == 0 || out_size > *max_out_size) {
        LOG(WARNING) << "compress failed, max_out_size:" << *max_out_size
            << " real_out_size:" << out_size;
        s.set_error(AIE_FAIL, "out_size not match");
        return s;
    }
    cntl.response_attachment().copy_to(dest);
    auto cal_crc_value = base::crc32c::Value(dest, out_size);
    if (cal_crc_value != response.data_crc()) {
        LOG(WARNING) << "compress failed, crc not match, expected:"
                << response.data_crc() << " actual:" << cal_crc_value;
        s.set_error(AIE_CHECKSUM, "crc not match");
        return s;
    }
    *max_out_size = out_size;
    return s;
}

base::Status Compressor::decompress_rpc_call(const CompressOption* option,
        const char* input, size_t input_size, char* dest, size_t* max_out_size) {
    base::Status s;
    const RpcCompressOption* rpc_option = dynamic_cast<const RpcCompressOption*>(option);
    uint64_t log_id = rpc_option->log_id;
    aries::pb::DecompressRequest request;
    aries::pb::DecompressResponse response;

    request.set_token(rpc_option->token);
    request.set_compress_type(rpc_option->compress_type);
    request.set_compress_level(rpc_option->compress_level);
    request.set_data_crc(base::crc32c::Value(input, input_size));
    request.set_timeout_ms(rpc_option->timeout_ms);
    request.set_priority(rpc_option->priority);
    request.set_origin_data_len(rpc_option->origin_data_len);
    baidu::rpc::Controller cntl;
    cntl.request_attachment().append(input, input_size);
    cntl.set_log_id(rpc_option->log_id);
    cntl.set_timeout_ms(rpc_option->timeout_ms);
    cntl.set_max_retry(rpc_option->compress_max_retry);
    cntl.set_backup_request_ms(rpc_option->timeout_ms / 2);
    aries::pb::CompressService_Stub client(rpc_option->compress_channel);
    client.decompress(&cntl, &request, &response, NULL);
    auto remote = base::endpoint2str(cntl.remote_side());
    if (cntl.Failed()) {
         ARIES_RPC_LOG(TRACE) << "decompress failed,"
            << " retry_times:" << cntl.retried_count()
            << " has back request:" << cntl.has_backup_request()
            << " remote:" << remote << " rpc_error:"
            << cntl.ErrorCode() << " (" << cntl.ErrorText() << ")"
            << " latency_us:" << cntl.latency_us();
        if (cntl.ErrorCode() == baidu::rpc::ELIMIT) {
            s.set_error(AIE_BUSY, "compress busy");
        } else {
            s.set_error(AIE_FAIL, "rpc error");
        }
        return s;
    } else if (response.status().code() != aries::AIE_OK) {
        ARIES_RPC_LOG(TRACE) << "decompress failed,"
            << " retry_times:" << cntl.retried_count()
            << " has back request:" << cntl.has_backup_request()
            << " remote:" << remote << " request error: "
            << response.status().code() << " (" << response.status().msg() << ")"
            << " latency_us:" << cntl.latency_us() ;
        s.set_error(response.status().code(), response.status().msg());
        return s;
    } else {
        ARIES_RPC_LOG(TRACE) << "remote decompress successed, remote:" << remote;
    }

    size_t out_size = cntl.response_attachment().size();
    if (out_size == 0 || out_size != rpc_option->origin_data_len) {
        LOG(WARNING) << "decompress failed, expect_out_size:" << rpc_option->origin_data_len
            << " real_out_size:" << out_size;
        s.set_error(AIE_FAIL, "out_size not match");
        return s;
    }
    cntl.response_attachment().copy_to(dest);
    auto cal_crc_value = base::crc32c::Value(dest, out_size);
    if (cal_crc_value != response.data_crc()) {
        LOG(WARNING) << "decompress failed, crc not match, expected:"
                << response.data_crc() << " actual:" << cal_crc_value;
        s.set_error(AIE_CHECKSUM, "crc not match");
        return s;
    }
    *max_out_size = out_size;
    return s;
}

size_t NoneCompressor::compress_bound(size_t source_bytes) const {
    return source_bytes;
}

int64_t NoneCompressor::compress(const CompressOption* option, const char* input, size_t input_size,
        char* dest, size_t max_out_size) const {
    memcpy(dest, input, input_size);
    return input_size;
}

int64_t NoneCompressor::decompress(const CompressOption* option, const char* input, size_t input_size,
        char* dest, size_t max_out_size) const {
    memcpy(dest, input, input_size);
    return input_size;
}

size_t LZ4Compressor::compress_bound(size_t source_bytes) const {
    return LZ4_COMPRESSBOUND(source_bytes);
}

int64_t LZ4Compressor::compress(const CompressOption* option, const char* input, size_t input_size,
        char* dest, size_t max_out_size) const {
    int64_t ret = LZ4_compress_default(input, dest, input_size, max_out_size);
    if (ret == 0) {
        ret = -1;
    }
    return ret;
}

int64_t LZ4Compressor::decompress(const CompressOption* option, const char* input, size_t input_size,
        char* dest, size_t max_out_size) const {
    return LZ4_decompress_safe(input, dest, input_size, max_out_size);
}

size_t ZstdCompressor::compress_bound(size_t source_bytes) const {
    return ZSTD_compressBound(source_bytes);
}

int64_t ZstdCompressor::compress(const CompressOption* option, const char* input, size_t input_size,
        char* dest, size_t max_out_size) const {
    if (option == nullptr) {
        return -1;
    }
    int64_t size = ZSTD_compress(dest, max_out_size, input, input_size, option->compress_level);
    if (ZSTD_isError(size) || size == 0) {
        return -1;
    }
    return size;
}

int64_t ZstdCompressor::decompress(const CompressOption* option, const char* input, size_t input_size,
        char* dest, size_t max_out_size) const {
    int64_t size = ZSTD_decompress(dest, max_out_size, input, input_size);
    if (ZSTD_isError(size)) {
        return -1;
    }
    return size;
}

size_t BrunsliCompressor::compress_bound(size_t source_bytes) const {
    return source_bytes;
}

int64_t BrunsliCompressor::compress(const CompressOption* option, const char* input, size_t input_size,
        char* dest, size_t max_out_size) const {
    int size = -1;
    if (option == nullptr || option->thread_num <= 0) {
        return -1;
    }
    std::string dest_string;
    size = brunsli_compress(input, input_size, dest_string, option->thread_num);
    if (dest_string.size() <= max_out_size && dest_string.size() > 0) {
        size = dest_string.size();
        std::memcpy(dest, dest_string.c_str(), dest_string.size());
    } else {
        size = -1;
    }
    return size;
}


int64_t BrunsliCompressor::decompress(const CompressOption* option, const char* input, size_t input_size,
        char* dest, size_t max_out_size) const {
    int size = -1;
    if (option == nullptr || option->thread_num <= 0) {
        return size;
    }
    std::string dest_string;
    size = brunsli_decompress(input, input_size, dest_string, option->thread_num);
    if (dest_string.size() <= max_out_size && dest_string.size() != 0) {
        size = dest_string.size();
        std::memcpy(dest, dest_string.c_str(), dest_string.size());
    } else {
        size = -1;
    }
    return size;
}

size_t SnappyCompressor::compress_bound(size_t source_bytes) const {
    return snappy_max_compressed_length(source_bytes);
}

int64_t SnappyCompressor::compress(const CompressOption* option, const char* input, size_t input_size,
        char* dest, size_t max_out_size) const {
    size_t dest_length = max_out_size;
    if (snappy_compress(input, input_size, dest, &dest_length) != SNAPPY_OK) {
        return -1;
    }
    return dest_length;
}

int64_t SnappyCompressor::decompress(const CompressOption* option, const char* input, size_t input_size,
        char* dest, size_t max_out_size) const {
    size_t dest_length = 0;
    if (snappy_uncompressed_length(input, input_size, &dest_length) != SNAPPY_OK) {
        return -1;
    }
    if (dest_length > max_out_size) {
        return -1;
    }
    if (snappy_uncompress(input, input_size, dest, &dest_length) != SNAPPY_OK) {
        return -1;
    }
    return dest_length;
}

size_t BrotliCompressor::compress_bound(size_t source_bytes) const {
    return BrotliEncoderMaxCompressedSize(source_bytes);
}

int64_t BrotliCompressor::compress(const CompressOption* option, const char* input, size_t input_size,
        char* dest, size_t max_out_size) const {
    int level = option->compress_level;
    static int lgwin = BROTLI_PARAM_LGWIN;
    static BrotliEncoderMode mode = BROTLI_MODE_GENERIC;
    size_t dest_length = max_out_size;
    if (BrotliEncoderCompress(level, lgwin, mode, input_size, reinterpret_cast<const unsigned char*>(input),
                              &dest_length, reinterpret_cast<unsigned char*>(dest))) {
        return dest_length;
    }
    return -1;
}

int64_t BrotliCompressor::decompress(const CompressOption* option, const char* input, size_t input_size,
        char* dest, size_t max_out_size) const {
    size_t dest_length = max_out_size;
    if (BrotliDecoderDecompress(input_size, reinterpret_cast<const unsigned char*>(input),
                                &dest_length, reinterpret_cast<unsigned char*>(dest))) {
        return dest_length;
    }
    return -1;
}


size_t ISALCompressor::compress_bound(size_t source_bytes) const {
    return ISAL_DEF_MAX_HDR_SIZE + 2 * source_bytes;
}

int isal_level_size_buf[4] = {
#ifdef ISAL_DEF_LVL0_DEFAULT
    ISAL_DEF_LVL0_DEFAULT,
#else
    0,
#endif
#ifdef ISAL_DEF_LVL1_DEFAULT
    ISAL_DEF_LVL1_DEFAULT,
#else
    0,
#endif
#ifdef ISAL_DEF_LVL2_DEFAULT
    ISAL_DEF_LVL2_DEFAULT,
#else
    0,
#endif
#ifdef ISAL_DEF_LVL3_DEFAULT
    ISAL_DEF_LVL3_DEFAULT,
#else
    0,
#endif
};

int64_t ISALCompressor::compress(const CompressOption* option, const char* input, size_t input_size,
        char* dest, size_t max_out_size) const {
    if (option == nullptr) {
        return -1;
    }
    auto level = option->compress_level;
    if (level > 3) {
        return -1;
    }
    struct isal_zstream stream;
    uint8_t* level_buf = NULL;

    if (isal_level_size_buf[level] > 0) {
        level_buf = new uint8_t[isal_level_size_buf[level]];
    }

    isal_deflate_init(&stream);
    stream.end_of_stream = 1;
    stream.flush = 0;
    stream.next_in = (uint8_t*)input;
    stream.avail_in = input_size;
    stream.next_out = (uint8_t*)dest;
    stream.avail_out = max_out_size;
    stream.level = level;
    stream.level_buf = level_buf;
    stream.level_buf_size = isal_level_size_buf[level];
    stream.hist_bits = 0;

    int check = isal_deflate_stateless(&stream);

    delete []level_buf;
    if (check || stream.avail_in) {
        return -1;
    }
    return stream.total_out;
}

int64_t ISALCompressor::decompress(const CompressOption* option, const char* input, size_t input_size,
        char* dest, size_t max_out_size) const {
    struct inflate_state state;
    state.next_in = (uint8_t*)input;
    state.avail_in = input_size;
    state.next_out = (uint8_t*)dest;
    state.avail_out = max_out_size;
    state.crc_flag = ISAL_DEFLATE;
    state.hist_bits = 0;

    if (isal_inflate_stateless(&state)) {
        return -1;
    }
    return state.total_out;
}
}
}
