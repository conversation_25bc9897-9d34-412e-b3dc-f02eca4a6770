/**
 * ARIES - A Reliable and Impressive EB-level Storage.
 * Copyright (c) 2016 Baidu.com, Inc. All Rights Reserved.
 *
 * Author: <PERSON> (guo<PERSON>@baidu.com)
 * Date: 2016/09/08
 * Desciption: Declaration and implementation of priority queue
 *
 */

#ifndef BAIDU_INF_ARIES_COMMON_PRIORITY_QUEUE_H
#define BAIDU_INF_ARIES_COMMON_PRIORITY_QUEUE_H

#include <map>
#include <list>
#include <memory>
#include <pthread.h>
#include <type_traits>
#include <base/logging.h>
#include <unordered_map>
#include <functional>
#include "baidu/inf/aries-api/common/lock.h"

namespace aries {
namespace common {

// CheckExist: check if is exist when push
template<typename T, int PriorityLevelNum, bool CheckExist = false, typename KEY = uint64_t>
class PriorityQueue {
public:
    PriorityQueue() : _priority_level_num(PriorityLevelNum),
                      _check_exist(CheckExist) {
        static_assert(PriorityLevelNum > 0, "PriorityLevelNum should > 0");
        for (int level = 0; level < _priority_level_num; ++level) {
            _queue_sizes[level] = 0;
        }
    }

    virtual ~PriorityQueue() {
    }

    int priority_level_num() const {
        return _priority_level_num;
    }

    bool is_check_exist() const {
        return _check_exist;
    }

    bool push_back(T* val, int prio) {
        assert(prio >= 0 && prio < _priority_level_num);
        assert(!_check_exist);
        common::ScopedMutexLock lock(_mutex);
        _queues[prio].push_back(val);
        ++_queue_sizes[prio];
        return true;
    }

    bool push_front(T* val, int prio) {
        assert(prio >= 0 && prio < _priority_level_num);
        assert(!_check_exist);
        common::ScopedMutexLock lock(_mutex);
        _queues[prio].push_front(val);
        ++_queue_sizes[prio];
        return true;
    }

    bool push_back(T* val, int prio, KEY key) {
        assert(prio >= 0 && prio < _priority_level_num);
        assert(_check_exist);
        common::ScopedMutexLock lock(_mutex);
        if (_map_k2v.find(key) != _map_k2v.end()) {
            return false;
        }
        _queues[prio].push_back(val);
        ++_queue_sizes[prio];
        bool ok1 = _map_k2v.insert(std::make_pair(key, (void*)val)).second;
        bool ok2 = _map_v2k.insert(std::make_pair((void*)val, key)).second;
        assert(ok1);
        assert(ok2);
        return true;
    }

    bool push_front(T* val, int prio, KEY key) {
        assert(prio >= 0 && prio < _priority_level_num);
        assert(_check_exist);
        common::ScopedMutexLock lock(_mutex);
        if (_map_k2v.find(key) != _map_k2v.end()) {
            return false;
        }
        _queues[prio].push_front(val);
        ++_queue_sizes[prio];
        bool ok1 = _map_k2v.insert(std::make_pair(key, (void*)val)).second;
        bool ok2 = _map_v2k.insert(std::make_pair((void*)val, key)).second;
        assert(ok1);
        assert(ok2);
        return true;
    }

    T* pop(int prio = -1) {
        assert(prio >= -1 && prio < _priority_level_num);
        common::ScopedMutexLock lock(_mutex);
        T* val = NULL;
        if (prio != -1) {
            if (_queue_sizes[prio] > 0) {
                val = _queues[prio].front();
                _queues[prio].pop_front();
                --_queue_sizes[prio];
            }
        } else {
            int level = 0;
            while (level < _priority_level_num) {
                if (_queue_sizes[level] > 0) {
                    val = _queues[level].front();
                    _queues[level].pop_front();
                    --_queue_sizes[level];
                    break;
                }
                ++level;
            }
        }
        if (val != NULL && _check_exist) {
            auto it1 = _map_v2k.find((void*)val);
            KEY key = it1->second;
            auto it2 = _map_k2v.find(key);
            void* val2 = it2->second;
            assert(val2 == ((void*)val));
            _map_v2k.erase(it1);
            _map_k2v.erase(it2);
        }
        return val;
    }

    T* peek(int prio = -1) {
        assert(prio >= -1 && prio < _priority_level_num);
        common::ScopedMutexLock lock(_mutex);
        T* val = NULL;
        if (prio != -1) {
            if (_queue_sizes[prio] > 0) {
                val = _queues[prio].front();
            }
        } else {
            int level = 0;
            while (level < _priority_level_num) {
                if (_queue_sizes[level] > 0) {
                    val = _queues[level].front();
                    break;
                }
                ++level;
            }
        }
        return val;
    }

    bool change(KEY key, void (*func)(T*)) {
        assert(_check_exist);
        common::ScopedMutexLock lock(_mutex);
        auto it = _map_k2v.find(key);
        if (it != _map_k2v.end()) {
            T* val = static_cast<T*>(it->second);
            assert(val != NULL);
            func(val);
            return true;
        }
        return false;
    }

    size_t size(int prio = -1) {
        assert(prio >= -1 && prio < _priority_level_num);
        common::ScopedMutexLock lock(_mutex);
        size_t size = 0;
        if (prio == -1) {
            for (int i = 0; i < _priority_level_num; ++i) {
                size += _queue_sizes[i];
            }
        } else {
            size = _queue_sizes[prio];
        }
        return size;
    }

    bool empty() {
        return size() == 0;
    }

private:
    const int _priority_level_num;
    const bool _check_exist;
    common::MutexLock _mutex;
    std::list<T*> _queues[PriorityLevelNum];
    ssize_t _queue_sizes[PriorityLevelNum];
    std::map<KEY, void*> _map_k2v;
    std::map<void*, KEY> _map_v2k;
};

// CheckExist: check if is exist when push
// support erase and update
template<typename T, int PriorityLevelNum, bool CheckExist = false, typename KEY = uint64_t>
class PtrSharedPriorityQueue {
public:
    PtrSharedPriorityQueue() : _priority_level_num(PriorityLevelNum),
                      _check_exist(CheckExist) {
        static_assert(PriorityLevelNum > 0, "PriorityLevelNum should > 0");
        for (int level = 0; level < _priority_level_num; ++level) {
            _queue_sizes[level] = 0;
        }
    }

    virtual ~PtrSharedPriorityQueue() {
    }

    int priority_level_num() const {
        return _priority_level_num;
    }

    bool is_check_exist() const {
        return _check_exist;
    }

    bool push_back(std::shared_ptr<T> val, int prio) {
        assert(prio >= 0 && prio < _priority_level_num);
        assert(!_check_exist);
        common::ScopedMutexLock lock(_mutex);
        _queues[prio].push_back(std::make_pair(KEY(), val));
        ++_queue_sizes[prio];
        return true;
    }

    bool push_front(std::shared_ptr<T> val, int prio) {
        assert(prio >= 0 && prio < _priority_level_num);
        assert(!_check_exist);
        common::ScopedMutexLock lock(_mutex);
        _queues[prio].push_front(std::make_pair(KEY(), val));
        ++_queue_sizes[prio];
        return true;
    }

    bool push_back(std::shared_ptr<T> val, int prio, KEY key) {
        assert(prio >= 0 && prio < _priority_level_num);
        assert(_check_exist);
        common::ScopedMutexLock lock(_mutex);
        if (_map_k2v.find(key) != _map_k2v.end()) {
            return false;
        }
        _queues[prio].push_back(std::make_pair(key, val));
        ++_queue_sizes[prio];
        auto iter = _queues[prio].end();
        --iter;
        bool ok = _map_k2v.insert(std::make_pair(key, 
                    std::make_pair(prio, iter))).second;
        assert(ok);
        return true;
    }

    bool push_front(std::shared_ptr<T> val, int prio, KEY key) {
        assert(prio >= 0 && prio < _priority_level_num);
        assert(_check_exist);
        common::ScopedMutexLock lock(_mutex);
        if (_map_k2v.find(key) != _map_k2v.end()) {
            return false;
        }
        _queues[prio].push_front(std::make_pair(key, val));
        ++_queue_sizes[prio];
        bool ok = _map_k2v.insert(std::make_pair(key, 
                    std::make_pair(prio, _queues[prio].begin()))).second;
        assert(ok);
        return true;
    }

    std::shared_ptr<T> pop(int prio = -1) {
        assert(prio >= -1 && prio < _priority_level_num);
        common::ScopedMutexLock lock(_mutex);
        KVPair pair;
        bool found = false;
        if (prio != -1) {
            if (_queue_sizes[prio] > 0) {
                pair = _queues[prio].front();
                _queues[prio].pop_front();
                --_queue_sizes[prio];
                found = true;
            }
        } else {
            int level = 0;
            while (level < _priority_level_num) {
                if (_queue_sizes[level] > 0) {
                    pair = _queues[level].front();
                    _queues[level].pop_front();
                    --_queue_sizes[level];
                    found = true;
                    break;
                }
                ++level;
            }
        }
        std::shared_ptr<T> val = pair.second;
        if (found && _check_exist) {
            auto it2 = _map_k2v.find(pair.first);
            assert(it2 != _map_k2v.end());
            _map_k2v.erase(it2);
        }
        return val;
    }

    std::shared_ptr<T> peek(int prio = -1) {
        assert(prio >= -1 && prio < _priority_level_num);
        common::ScopedMutexLock lock(_mutex);
        std::shared_ptr<T> val;
        if (prio != -1) {
            if (_queue_sizes[prio] > 0) {
                val = _queues[prio].front().second;
            }
        } else {
            int level = 0;
            while (level < _priority_level_num) {
                if (_queue_sizes[level] > 0) {
                    val = _queues[level].front().second;
                    break;
                }
                ++level;
            }
        }
        return val;
    }

    void list(std::vector<std::shared_ptr<T>>* values, int prio = -1) {
        assert(prio >= -1 && prio < _priority_level_num);
        common::ScopedMutexLock lock(_mutex);
        if (prio != -1) {
            for (auto& it : _queues[prio]) {
                    values->push_back(it.second);
                }
        } else {
            int level = 0;
            while (level < _priority_level_num) {
                for (auto& it : _queues[level]) {
                    values->push_back(it.second);
                }
                ++level;
            }
        }
    }

    void list_key(std::set<KEY>* values, int prio = -1) {
        assert(prio >= -1 && prio < _priority_level_num);
        common::ScopedMutexLock lock(_mutex);
        if (prio != -1) {
            for (auto& it : _queues[prio]) {
                values->insert(it.first);
            }
        }
        else {
            int level = 0;
            while (level < _priority_level_num) {
                for (auto& it : _queues[level]) {
                    values->insert(it.first);
                }
                ++level;
            }
        }
    }

    bool change(KEY key, void (*func)(T*)) {
        assert(_check_exist);
        common::ScopedMutexLock lock(_mutex);
        auto it = _map_k2v.find(key);
        if (it != _map_k2v.end()) {
            T* val = (((it->second).second)->second).get();
            assert(val != NULL);
            func(val);
            return true;
        }
        return false;
    }

    bool erase(KEY key) {
        assert(_check_exist);
        common::ScopedMutexLock lock(_mutex);
        auto it = _map_k2v.find(key);
        if (it != _map_k2v.end()) {
            int level = it->second.first ;
            assert(level < _priority_level_num);
            assert(_queue_sizes[level] > 0);
            _queues[level].erase(it->second.second);
            --_queue_sizes[level];
            _map_k2v.erase(key);
            return true;
        }
        return false;
    }

    bool exist(KEY key) {
        assert(_check_exist);
        common::ScopedMutexLock lock(_mutex);
        auto it = _map_k2v.find(key);
        return it != _map_k2v.end();
    }

    bool update(KEY key, T* value) {
        assert(_check_exist);
        common::ScopedMutexLock lock(_mutex);
        auto it = _map_k2v.find(key);
        if (it != _map_k2v.end()) {
            *(((it->second).second)->second).get() = *value;
            return true;
        }
        return false;
    }

    size_t size(int prio = -1) {
        assert(prio >= -1 && prio < _priority_level_num);
        common::ScopedMutexLock lock(_mutex);
        size_t size = 0;
        if (prio == -1) {
            for (int i = 0; i < _priority_level_num; ++i) {
                size += _queue_sizes[i];
            }
        } else {
            size = _queue_sizes[prio];
        }
        return size;
    }

    size_t accumulate_size(int prio = -1) {
        assert(prio >= -1 && prio < _priority_level_num);
        if (prio == -1) {
            prio = _priority_level_num - 1;
        } 
        common::ScopedMutexLock lock(_mutex);
        size_t size = 0;
        for (int i = 0; i <= prio; i++) {
            size += _queue_sizes[i];
        }
        return size;
    }

    bool empty() {
        return size() == 0;
    }

    void clear() {
        common::ScopedMutexLock lock(_mutex);
        for (int i = 0; i < _priority_level_num; i++) {
            _queues[i].clear();
            _queue_sizes[i] = 0;
        }
        _map_k2v.clear();
    }

private:
    typedef std::pair<KEY, std::shared_ptr<T>> KVPair; // key and value
    typedef std::pair<int, typename std::list<KVPair>::iterator> LVPair; // level and value
    const int _priority_level_num;
    const bool _check_exist;
    common::MutexLock _mutex;
    std::list<KVPair> _queues[PriorityLevelNum];
    ssize_t _queue_sizes[PriorityLevelNum];
    std::unordered_map<KEY, LVPair> _map_k2v;
};

// This simple priority queue sutable for simple type, like int,float ...
template<typename T, int PriorityLevelNum>
class SimplePriorityQueue {
public:
    SimplePriorityQueue() : _priority_level_num(PriorityLevelNum) {
        static_assert(PriorityLevelNum > 0, "PriorityLevelNum should > 0");
        for (int level = 0; level < _priority_level_num; ++level) {
            _queue_sizes[level] = 0;
        }
    }

    virtual ~SimplePriorityQueue() {
    }

    int priority_level_num() const {
        return _priority_level_num;
    }

    bool push_back(T val, int prio) {
        assert(prio >= 0 && prio < _priority_level_num);
        common::ScopedMutexLock lock(_mutex);
        _queues[prio].push_back(val);
        ++_queue_sizes[prio];
        return true;
    }

    bool push_front(T val, int prio) {
        assert(prio >= 0 && prio < _priority_level_num);
        common::ScopedMutexLock lock(_mutex);
        _queues[prio].push_front(val);
        ++_queue_sizes[prio];
        return true;
    }

    bool pop(T* val, int prio = -1) {
        assert(prio >= -1 && prio < _priority_level_num);
        common::ScopedMutexLock lock(_mutex);
        bool has = false;
        if (prio != -1) {
            if (_queue_sizes[prio] > 0) {
                *val = _queues[prio].front();
                _queues[prio].pop_front();
                --_queue_sizes[prio];
                has = true;
            }
        } else {
            int level = 0;
            while (level < _priority_level_num) {
                if (_queue_sizes[level] > 0) {
                    *val = _queues[level].front();
                    _queues[level].pop_front();
                    --_queue_sizes[level];
                    has = true;
                    break;
                }
                ++level;
            }
        }
        return has;
    }

    bool peek(T* val, int prio = -1) {
        assert(prio >= -1 && prio < _priority_level_num);
        common::ScopedMutexLock lock(_mutex);
        bool has = false;
        if (prio != -1) {
            if (_queue_sizes[prio] > 0) {
                *val = _queues[prio].front();
                has = true;
            }
        } else {
            int level = 0;
            while (level < _priority_level_num) {
                if (_queue_sizes[level] > 0) {
                    *val = _queues[level].front();
                    has = true;
                    break;
                }
                ++level;
            }
        }
        return has;
    }

    size_t size(int prio = -1) {
        assert(prio >= -1 && prio < _priority_level_num);
        common::ScopedMutexLock lock(_mutex);
        size_t size = 0;
        if (prio == -1) {
            for (int i = 0; i < _priority_level_num; ++i) {
                size += _queue_sizes[i];
            }
        } else {
            size = _queue_sizes[prio];
        }
        return size;
    }

    size_t accumulate_size(int prio = -1) {
        assert(prio >= -1 && prio < _priority_level_num);
        if (prio == -1) {
            prio = _priority_level_num - 1;
        } 
        common::ScopedMutexLock lock(_mutex);
        size_t size = 0;
        for (int i = 0; i <= prio; i++) {
            size += _queue_sizes[i];
        }
        return size;
       
    }

    void erase(bool (*filter)(T&)) {
        common::ScopedMutexLock lock(_mutex);
        int level = 0;
        while (level < _priority_level_num) {
            auto it = _queues[level].begin();
            while (it != _queues[level].end()) {
                if (filter(*it)) {
                    it = _queues[level].erase(it);
                    --_queue_sizes[level];
                    continue;
                }
                it++;
            }
            ++level;
        }
    }

    void erase(bool (*filter)(T&, uint64_t)) {
        common::ScopedMutexLock lock(_mutex);
        int level = 0;
        while (level < _priority_level_num) {
            uint64_t pos = 0;
            auto it = _queues[level].begin();
            while (it != _queues[level].end()) {
                if (filter(*it, pos++)) {
                    it = _queues[level].erase(it);
                    --_queue_sizes[level];
                    continue;
                }
                it++;
            }
            ++level;
        }
    }

    bool empty() {
        return size() == 0;
    }

private:
    const int _priority_level_num;
    common::MutexLock _mutex;
    std::list<T> _queues[PriorityLevelNum];
    ssize_t _queue_sizes[PriorityLevelNum];
};

// SimplePriorityQueue without lock
template<typename T, int PriorityLevelNum>
class SimplePriorityQueueNoLock {
public:
    SimplePriorityQueueNoLock() : _priority_level_num(PriorityLevelNum) {
        static_assert(PriorityLevelNum > 0, "PriorityLevelNum should > 0");
        for (int level = 0; level < _priority_level_num; ++level) {
            _queue_sizes[level] = 0;
        }
    }

    virtual ~SimplePriorityQueueNoLock() {
    }

    int priority_level_num() const {
        return _priority_level_num;
    }

    bool push_back(T val, int prio) {
        assert(prio >= 0 && prio < _priority_level_num);
        _queues[prio].push_back(val);
        ++_queue_sizes[prio];
        return true;
    }

    bool push_front(T val, int prio) {
        assert(prio >= 0 && prio < _priority_level_num);
        _queues[prio].push_front(val);
        ++_queue_sizes[prio];
        return true;
    }

    bool pop(T* val, int prio = -1) {
        assert(prio >= -1 && prio < _priority_level_num);
        bool has = false;
        if (prio != -1) {
            if (_queue_sizes[prio] > 0) {
                *val = _queues[prio].front();
                _queues[prio].pop_front();
                --_queue_sizes[prio];
                has = true;
            }
        } else {
            int level = 0;
            while (level < _priority_level_num) {
                if (_queue_sizes[level] > 0) {
                    *val = _queues[level].front();
                    _queues[level].pop_front();
                    --_queue_sizes[level];
                    has = true;
                    break;
                }
                ++level;
            }
        }
        return has;
    }

    bool peek(T* val, int prio = -1) {
        assert(prio >= -1 && prio < _priority_level_num);
        bool has = false;
        if (prio != -1) {
            if (_queue_sizes[prio] > 0) {
                *val = _queues[prio].front();
                has = true;
            }
        } else {
            int level = 0;
            while (level < _priority_level_num) {
                if (_queue_sizes[level] > 0) {
                    *val = _queues[level].front();
                    has = true;
                    break;
                }
                ++level;
            }
        }
        return has;
    }
    
    bool find(T* val, std::function<bool(T&)> check_func) {
        for (int level = 0; level < _priority_level_num; ++level) {
            if (_queue_sizes[level] > 0) {
                for (auto it = _queues[level].begin(); it != _queues[level].end(); it++) {
                    if (check_func(*it)) {
                        *val = *it;
                        _queues[level].erase(it);
                        _queue_sizes[level]--;
                        return true;
                    }
                }
            }
        }
        return false;
    }

    size_t size(int prio = -1) {
        assert(prio >= -1 && prio < _priority_level_num);
        size_t size = 0;
        if (prio == -1) {
            for (int i = 0; i < _priority_level_num; ++i) {
                size += _queue_sizes[i];
            }
        } else {
            size = _queue_sizes[prio];
        }
        return size;
    }

    size_t accumulate_size(int prio = -1) {
        assert(prio >= -1 && prio < _priority_level_num);
        if (prio == -1) {
            prio = _priority_level_num - 1;
        } 
        size_t size = 0;
        for (int i = 0; i <= prio; i++) {
            size += _queue_sizes[i];
        }
        return size;
       
    }

    void erase(std::function<bool(T&)> filter) {
        int level = 0;
        while (level < _priority_level_num) {
            auto it = _queues[level].begin();
            while (it != _queues[level].end()) {
                if (filter(*it)) {
                    it = _queues[level].erase(it);
                    --_queue_sizes[level];
                    continue;
                }
                it++;
            }
            ++level;
        }
    }

    bool empty() {
        for (int i = 0; i < _priority_level_num; ++i) {
            if (_queue_sizes[i] > 0) {
                return false;
            }
        }
        return true;
    }

private:
    const int _priority_level_num;
    std::list<T> _queues[PriorityLevelNum];
    ssize_t _queue_sizes[PriorityLevelNum];
};

}
}

#endif
