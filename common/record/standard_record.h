// Copyright 2022 Baidu Inc. All Rights Reserved.
// @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>@baidu.com
// @Created Time : Thu 10 Mar 2022 21:14:23 PM CST
// @File Name: standard_record.h
// @Description:

#pragma once
#include <cmath>
#include <assert.h>
#include <inttypes.h>
#include <stdint.h>
#include <rocksdb/db.h>
#include <base/time.h>
#include <base/iobuf.h>
#include <stdalign.h>

#include "base/crc32c.h"

namespace aries {
namespace common {
// echo "standard_record" | md5sum = 3217c8b6e0cab5dcec64780df8d02745
constexpr uint64_t STANDARD_RECORD_MAGIC = 0x3217c8b6e0cab5dc;
constexpr uint64_t STANDARD_RECORD_LAYOUT_VERSION_V0 = 10;
//echo "standard_record_footer" | md5sum = 7446b3d75d6ea7197a4aa4fd9daf21f3
constexpr uint64_t STANDARD_RECORD_FOOTER_MAGIC = 0x7446b3d75d6ea719;
//echo "zone_record_footer" | md5sum = e2b0389d5b45a30300bf6842f976fb5b
constexpr uint64_t ZONE_RECORD_FOOTER_MAGIC = 0xe2b0389d5b45a303;

enum RecordType {
    UNDEFINED = 0,
    RECORD_DATA = 1,
    RECORD_VLET_INFO = 2,
    RECORD_VOLUME_INFO = 3,
    RECORD_INDEX_INFO = 4,
    RECORD_ZONE_META = 5,
    RECORD_ZONE_NOP_META = 6,
};

#pragma pack(push, 4)
struct SliceHeader {
    uint8_t compress_flag;
    uint8_t reserved[3] = {0};
    uint32_t origin_slice_body_crc;
    uint64_t vbid;
};
static_assert(sizeof(SliceHeader) == sizeof(uint8_t) * 4 + sizeof(uint32_t) + sizeof(uint64_t));

struct RecordBodyDescInfo {
    uint32_t record_type;
    uint32_t record_data_len;
    uint32_t record_meta_len;

    RecordBodyDescInfo() = default;
    RecordBodyDescInfo(
            const uint32_t record_type,
            const uint32_t record_data_len,
            const uint32_t record_meta_len) :
            record_type(record_type),
            record_data_len(record_data_len),
            record_meta_len(record_meta_len) {}
};
static_assert(sizeof(RecordBodyDescInfo) == sizeof(uint32_t) * 3);

struct StandardRecordHeader {
    uint64_t magic_num;
    uint32_t layout_version;
    RecordBodyDescInfo record_body_desc_info;
    uint64_t vid;
    uint64_t vbid;
    uint64_t timestamp;
    uint8_t align_size_type = 0;  // type = 0 means align_size = 512B
    uint8_t shard_index;
    uint8_t reserved[6] = {0};
    uint32_t header_crc;

    void init(
            const uint32_t layout_version,
            const uint32_t record_data_len,
            const uint32_t record_meta_len,
            const uint64_t vid,
            const uint64_t vbid,
            const uint8_t shard_index,
            const uint32_t align_size,
            const uint32_t record_type = RECORD_DATA) {
        this->magic_num = STANDARD_RECORD_MAGIC;
        this->layout_version = layout_version;
        this->timestamp = ::base::gettimeofday_us();
        this->record_body_desc_info.record_type = record_type;
        this->record_body_desc_info.record_data_len = record_data_len;
        this->record_body_desc_info.record_meta_len = record_meta_len;
        this->vid = vid;
        this->vbid = vbid;
        this->shard_index = shard_index;
        this->align_size_type = log2(align_size) - 9;
        memset(reserved, 0, sizeof(reserved));
        this->header_crc = base::crc32c::Value(
                reinterpret_cast<const char*>(this),
                sizeof(StandardRecordHeader) - sizeof(header_crc));
    }

    uint32_t record_align_size() const {
        return 512 << align_size_type;
    }
};
static_assert(sizeof(StandardRecordHeader) == sizeof(uint64_t) * 4 + sizeof(uint32_t) * 2 + sizeof(uint8_t) * 8 + sizeof(RecordBodyDescInfo));
#pragma pack(pop)

struct SerializeArgs {
    uint32_t footer_len = 0;
    uint32_t* record_size_type = nullptr;
    uint32_t* record_data_offset = nullptr;

    SerializeArgs(
            const uint32_t footer_len,
            uint32_t* record_size_type,
            uint32_t* record_data_offset) :
            footer_len(footer_len),
            record_size_type(record_size_type),
            record_data_offset(record_data_offset) {}
    SerializeArgs() = default;
};

}  // namespace datanode
}  // namespace aries
