// Copyright 2022 Baidu Inc. All Rights Reserved.
// @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>@baidu.com
// @Created Time : Tue 17 Mar 2022 14:14:23 PM CST
// @File Name: record_serializer.h
// @Description:serializer and deserializer of record

#pragma once

#include "baidu/inf/sfl/src/class_helper.h"

#include "baidu/inf/aries/common/common.h"
#include "baidu/inf/aries/common/record/standard_record.h"
#include "baidu/inf/aries/common/record/record_data_compressor.h"

namespace aries {
namespace common {

class IRecordSerializer {
public:
    virtual Status serialize(
            const uint64_t vbid,
            const uint32_t shard_index,
            const base::IOBuf& shard_data,
            const aries::pb::ShardMeta& shard_meta,
            const aries::pb::ShardCompressOption& compress_option,
            common::Buffer* result,                     // serialize result in binary
            aries::pb::SliceDescMeta* slice_desc_meta,  // which is used to deserialize result
            void* args) = 0;

    virtual Status serialize(
            const uint64_t vbid,
            const base::IOBuf& shard_data,
            const aries::pb::ShardMeta& shard_meta,
            const aries::pb::ShardCompressOption& compress_option,
            common::Buffer* result,                     
            aries::pb::SliceDescMeta* slice_desc_meta,  
            void* args) = 0;

    virtual Status deserialize(
            const common::Buffer& record_buf,
            uint32_t* shard_index,
            uint64_t* record_vbid,
            aries::pb::SliceDescMeta* slice_desc_meta,
            aries::pb::ShardMeta* shard_meta,
            base::IOBuf* shard_data) = 0;

    virtual Status deserialize(
            const common::Buffer& record_buf,
            uint64_t* record_vbid,
            aries::pb::SliceDescMeta* slice_desc_meta,
            aries::pb::ShardMeta* shard_meta,
            base::IOBuf* shard_data) = 0;

    virtual Status range_deserialize(
            const common::Buffer& slice_buf,
            const aries::pb::SliceDescMeta& slice_desc_meta,
            const uint32_t data_offset_in_buf,
            const uint32_t offset,
            const uint32_t len,
            aries::pb::ShardMeta* shard_meta,
            base::IOBuf* range_data) = 0;

    virtual Status range_deserialize(
            const common::Buffer& slice_buf,
            const aries::pb::SliceDescMeta& slice_desc_meta,
            const uint32_t data_offset_in_buf,
            const uint32_t offset,
            const uint32_t len,
            aries::pb::ShardMeta* shard_meta,
            base::IOBuf* range_data,
            uint64_t* slice_vbid) = 0;

    virtual Status serialize_only_meta_record(
            const ::google::protobuf::Message& meta,
            RecordType record_type,
            common::Buffer* result) = 0;

    virtual Status serialize_only_meta_record(
            const ::google::protobuf::Message& meta,
            RecordType record_type,
            const uint32_t shard_index,
            common::Buffer* result) = 0;

    virtual Status parse_only_meta_record(
            const common::Buffer& record_buf,
            const RecordType& record_type,
            ::google::protobuf::Message* meta) = 0;

    virtual Status parse_only_meta_record(
            const common::Buffer& record_buf,
            const RecordType& record_type,
            uint32_t* shard_index,
            ::google::protobuf::Message* meta) = 0;

    virtual ~IRecordSerializer() = default;
    DISABLE_COPY_AND_MOVE(IRecordSerializer);

protected:
    IRecordSerializer() = default;

    Status check_data_integrity(
            const uint64_t vbid,
            const base::IOBuf& shard_data,
            const aries::pb::ShardMeta& shard_meta);
};

class StandardRecordSerializer : public IRecordSerializer {
public:
    StandardRecordSerializer(
            const uint64_t vid,
            const uint32_t align_size,
            const std::string& path) :
            _vid(vid), _align_size(align_size), _path(path) {}

    StandardRecordSerializer(const aries::pb::LinkedVletInfo& vlet_info, const std::string& path) :
            _vid(vlet_info.volume_id()),
            _align_size(vlet_info.page_size()),
            _path(path),
            _page_size(vlet_info.page_size()),
            _min_record_page_num(vlet_info.min_record_page_num()),
            _record_gap_page_num(vlet_info.record_gap_page_num()) {
        _max_record_type = 1 +
                calc_ceil(vlet_info.max_record_page_num() - _min_record_page_num,
                          vlet_info.record_gap_page_num());
    }

    virtual ~StandardRecordSerializer() = default;

    virtual Status serialize(
            const uint64_t vbid,
            const uint32_t shard_index,
            const base::IOBuf& shard_data,
            const aries::pb::ShardMeta& shard_meta,
            const aries::pb::ShardCompressOption& compress_option,
            common::Buffer* result,                     // serialize result in binary
            aries::pb::SliceDescMeta* slice_desc_meta,  // which is used to deserialize result
            void* args);
            
    virtual Status serialize(
            const uint64_t vbid,
            const base::IOBuf& shard_data,
            const aries::pb::ShardMeta& shard_meta,
            const aries::pb::ShardCompressOption& compress_option,
            common::Buffer* result,                     
            aries::pb::SliceDescMeta* slice_desc_meta,  
            void* args) {
        return serialize(vbid, UINT8_MAX, shard_data, shard_meta, compress_option, result, slice_desc_meta, args);
    };

    virtual Status deserialize(
            const common::Buffer& record_buf,
            uint32_t* shard_index,
            uint64_t* record_vbid,
            aries::pb::SliceDescMeta* slice_desc_meta,
            aries::pb::ShardMeta* shard_meta,
            base::IOBuf* shard_data);

    virtual Status deserialize(
            const common::Buffer& record_buf,
            uint64_t* record_vbid,
            aries::pb::SliceDescMeta* slice_desc_meta,
            aries::pb::ShardMeta* shard_meta,
            base::IOBuf* shard_data) {
        return deserialize(record_buf, nullptr, record_vbid, slice_desc_meta, shard_meta, shard_data);      
    }

    virtual Status range_deserialize(
            const common::Buffer& slice_buf,
            const aries::pb::SliceDescMeta& slice_desc_meta,
            const uint32_t data_offset_in_buf,
            const uint32_t offset,
            const uint32_t len,
            aries::pb::ShardMeta* shard_meta,
            base::IOBuf* range_data);

    virtual Status range_deserialize(
            const common::Buffer& slice_buf,
            const aries::pb::SliceDescMeta& slice_desc_meta,
            const uint32_t data_offset_in_buf,
            const uint32_t offset,
            const uint32_t len,
            aries::pb::ShardMeta* shard_meta,
            base::IOBuf* range_data,
            uint64_t* slice_vbid);

    virtual Status serialize_only_meta_record(
            const ::google::protobuf::Message& meta,
            RecordType record_type,
            common::Buffer* result) {
        return serialize_only_meta_record(meta, record_type, UINT8_MAX, result);
    }

    virtual Status serialize_only_meta_record(
            const ::google::protobuf::Message& meta,
            RecordType record_type,
            const uint32_t shard_index,
            common::Buffer* result);

    virtual Status parse_only_meta_record(
            const common::Buffer& record_buf,
            const RecordType& record_type,
            ::google::protobuf::Message* meta) {
        return parse_only_meta_record(record_buf, record_type, nullptr, meta);
    }
    
    virtual Status parse_only_meta_record(
            const common::Buffer& record_buf,
            const RecordType& record_type,
            uint32_t* shard_index,
            ::google::protobuf::Message* meta);

    DISABLE_COPY_AND_MOVE(StandardRecordSerializer);

private:
    Status calculate_buffer_size(
            SerializeArgs& serialize_args,
            const uint32_t raw_record_len,
            uint32_t* buffer_size,
            bool need_check_record_len);

protected:
    uint64_t _vid;
    uint32_t _align_size;
    std::string _path;
    uint64_t _page_size;
    uint32_t _min_record_page_num;
    uint32_t _record_gap_page_num;
    uint32_t _max_record_type;
};

}
}
