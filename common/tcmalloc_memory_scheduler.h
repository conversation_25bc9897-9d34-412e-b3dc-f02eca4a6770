/**
 * ARIES - A Reliable and Impressive EB-level Storage.
 * Copyright (c) 2017 Baidu.com, Inc. All Rights Reserved.
 *
 * Author: <PERSON> (<EMAIL>)
 * Date: 2017/12/13
 * Desciption: Declaration of tcmalloc memory scheduler
 *
 */

#ifndef BAIDU_INF_ARIES_COMMON_TCMALLOC_MEMORY_SCHEDULER_H
#define BAIDU_INF_ARIES_COMMON_TCMALLOC_MEMORY_SCHEDULER_H

#include <gflags/gflags.h>
#include <bthread.h>
#include <bthread_unstable.h>

namespace aries {
namespace common {

DECLARE_int32(tcmalloc_memory_release_interval_second);

#ifndef NO_TCMALLOC
class TcmallocMemoryScheduler {
public:
    void start();

    static void* thread_proc(void* args);
    void run();
    void release_memory();
    void print_memory_stat();
};
#endif

void start_tcmalloc_memory_scheduler();


}
}

#endif
