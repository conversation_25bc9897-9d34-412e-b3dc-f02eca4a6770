/**
 * ARIES - A Reliable and Impressive EB-level Storage.
 * Copyright (c) 2016 Baidu.com, Inc. All Rights Reserved.
 *
 * Author: <PERSON> <PERSON> (guo<PERSON>@baidu.com)
 * Date: 2016/10/11
 * Desciption: Erasure code factory
 *
 */

#ifndef BAIDU_INF_ARIES_COMMON_ERASURE_CODE_FACTORY_H
#define BAIDU_INF_ARIES_COMMON_ERASURE_CODE_FACTORY_H

#include <memory>
#include <base/logging.h>
#include "baidu/inf/aries-api/common/erasure_code_interface.h"
#include "baidu/inf/aries-api/common/erasure_code_isal.h"
#include "baidu/inf/aries-api/common/lock.h"

namespace aries {
namespace common {

typedef std::shared_ptr<ErasureCodeInterface> ECPtr;

class ErasureCodeFactory {
public:
    // not thread-safe
    static void init();

    // thread-safe
    static ECPtr register_erasure_code(const ECOption& eco);
    static ECPtr get_erasure_code(const ECOption& eco);

private:
    static std::shared_ptr<ErasureCodeFactory> _s_instance;

public:
    ECPtr create(const ECOption &eco);

    ECPtr load(const ECOption &eco);
    ECPtr get(const ECOption &eco);
private:
    std::map<ECOption, ECPtr> _ec_map;
    ReadWriteLock _lock;
};

}
}

#endif
