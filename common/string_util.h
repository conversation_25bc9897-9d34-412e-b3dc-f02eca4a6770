#pragma once

#include <utility>
#include <string>
#include <base/macros.h>
#include <base/string_printf.h>

namespace aries {
namespace common {

class StringUtil {
public:
    StringUtil() = default;
    ~StringUtil() = default;

    static bool start_with(const std::string& raw_str, const std::string& prefix);

    static bool end_with(const std::string& raw_str, const std::string& suffix);

private:
    DISALLOW_COPY_AND_ASSIGN(StringUtil);
};

}
}