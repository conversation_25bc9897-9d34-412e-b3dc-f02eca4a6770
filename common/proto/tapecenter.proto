syntax="proto2";
package aries.pb;
option cc_generic_services = true;

import "enum.proto";
import "common.proto";

message TapeRetrieveTaskInfo {
    message ErrorBlob {
        required uint64 vbid = 1;
        optional uint32 error_no = 2;
    }
    required uint64 task_id = 1;
    required string user_task_id = 2;
    optional string blob_group_tag = 3;
    required uint64 volume_id = 4;
    required uint64 task_begin_timestamp = 5;
    optional uint64 task_finish_timestamp = 6;
    optional uint64 task_deadline = 7;
    optional uint64 retrieve_data_ttl = 8;
    repeated uint64 vbid_list = 9;
    optional VolumeLocationOnTape location_on_tape = 10;
    required string space_name = 11;
    repeated ErrorBlob error_blobs = 12;
}

message TapeTransformTaskInfo {
    required uint64 task_id = 1;
    required uint64 volume_id = 2;
    required uint64 task_begin_timestamp = 3;
    optional VolumeLocationOnTape location_on_tape = 4;
    optional VolumeStatsOnTape stats_on_tape = 5;
    optional uint64 task_finish_timestamp = 6;
}

message TapeDeleteTaskInfo {
    required uint64 task_id = 1;
    required uint64 volume_id = 2;
    required uint64 task_begin_timestamp = 3;
    required VolumeLocationOnTape location_on_tape = 4;
    optional uint64 task_finish_timestamp = 5;
}

message TapeReclaimTaskInfo {
    optional uint64 task_id = 1;
    optional string tape_id = 2;
    optional string physical_pool_name = 3;
    optional bool is_reconcile = 4;
    optional uint64 task_begin_timestamp = 5;
    optional uint64 task_finish_timestamp = 6;
}

message TapeTaskInfo {
    optional TapeTaskType task_type = 1;
    optional TapeTaskState task_state = 2;
    optional uint64 task_id = 3;
    optional TapeTransformTaskInfo transfrom_task_info = 4;
    optional TapeRetrieveTaskInfo retrieve_task_info = 5;
    optional TapeDeleteTaskInfo delete_task_info = 6;
    optional TapeReclaimTaskInfo reclaim_task_info = 7;
}

message TapeCenterLimitParam {
    optional uint32 max_get_blob_num_per_second = 1;
    optional uint32 max_get_blob_flow_kb = 2;
    optional uint32 max_put_blob_num_per_second = 3;
    optional uint32 max_put_blob_flow_kb = 4;
}

message FetchTaskRequest {
    optional string token = 1;
    required uint64 req_addr = 2;
    optional string version = 3;
}

message FetchTransformTaskResponse {
    required Status status = 1;
    optional TapeTransformTaskInfo task = 2;
    optional uint32 timeout = 3;
    optional TapeCenterLimitParam limit_params = 4;
}

message FetchRetrieveTaskResponse {
    required Status status = 1;
    repeated TapeRetrieveTaskInfo task_list = 2;
    optional uint32 timeout = 3;
    optional TapeCenterLimitParam limit_params = 4;
    optional LocationOntape optimal_tape = 5;
}

message TapeRetrieveTaskReportRequest {
    message BlobInfo {
        required uint64 vbid = 1;
        optional uint32 error_no = 2;
    }
    optional string token = 1;
    required uint64 req_addr = 2;
    optional string version = 3;
    required uint64 task_id = 4;
    required uint64 volume_id = 5;
    required uint64 task_begin_timestamp = 6;
    optional bool is_succ = 7;
    repeated BlobInfo blob_list = 8;
    optional LocationOntape read_tape = 9;
}

message TapeTransformTaskReportRequest {
    optional string token = 1;
    required uint64 req_addr = 2;
    optional string version = 3;
    required uint64 task_id = 4;
    required uint64 volume_id = 5;
    optional bool is_succ = 6;
    optional VolumeLocationOnTape location_on_tape = 7;
}

message FetchDeleteTaskRequest {
    optional string token = 1;
    optional uint64 req_addr = 2;
    optional string version = 3;
}

message FetchDeleteTaskResponse {
    optional Status status = 1;
    optional TapeDeleteTaskInfo task = 2;
    optional uint32 timeout = 3;
}

message TapeDeleteTaskReportRequest {
    optional string token = 1;
    optional uint64 req_addr = 2;
    optional string version = 3;
    optional uint64 task_id = 4;
    optional uint64 volume_id = 5;
    optional Status status = 6;
}

message FetchReclaimTaskRequest {
    optional string token = 1;
    optional uint64 req_addr = 2;
    optional string version = 3;
}

message FetchReclaimTaskResponse {
    optional Status status = 1;
    optional TapeReclaimTaskInfo task = 2;
    optional uint32 timeout = 3;
}

message FetchCheckTaskRequest {
    optional string token = 1;
    optional uint64 req_addr = 2;
    optional string version = 3;
}

message FetchCheckTaskResponse {
    optional Status status = 1;
    optional uint64 volume_id = 2;
    optional VolumeLocationOnTape location_on_tape = 3;
    optional uint32 timeout = 4;
}

message CheckTaskReportRequest {
    optional string token = 1;
    required uint64 req_addr = 2;
    optional string version = 3;
    required uint64 volume_id = 4;
    optional bool is_succ = 5;
    optional bool is_consistant = 6;
    optional VolumeLocationOnTape location_on_tape = 7;
}

message TapeReclaimTaskReportRequest {
    optional string token = 1;
    optional uint64 req_addr = 2;
    optional string version = 3;
    optional uint64 task_id = 4;
    optional string src_tape_id = 5;
    optional string dst_tape_id = 6;
    optional bool is_succ = 7;
    message TapeState {
        optional string tape_id = 1;
        optional uint64 reclaimable_percentage = 2; 
        optional string physical_pool_name = 3;
    }
    repeated TapeState tape_state_list = 8;
}

message TapeRetrieveTaskReportResponse {
    optional Status status = 1;
    optional bool remove_volume = 2;
}

service TapeCenterService  {
    rpc check_leader(CheckLeaderRequest) returns (CheckLeaderResponse);
    rpc fetch_transform_task(FetchTaskRequest) returns(FetchTransformTaskResponse);
    rpc fetch_retrieve_task(FetchTaskRequest) returns(FetchRetrieveTaskResponse);
    rpc report_transform_task(TapeTransformTaskReportRequest) returns(AckResponse);
    rpc report_retrieve_task(TapeRetrieveTaskReportRequest) returns(TapeRetrieveTaskReportResponse);
    rpc retrieve_blobs(RetrieveBlobsRequest) returns(AckResponse);
    rpc get_retrieve_task_status(GetRetrieveTaskStatusRequest) returns(GetRetrieveTaskStatusResponse);
    rpc cancel_retrieve_task(CancelRetrieveTaskRequest) returns(AckResponse);
    rpc drop_tape_center_task(DropTapeTaskRequest) returns (AckResponse);
    rpc add_tape_center_peer(AddTapeCenterPeerRequest) returns(AckResponse);
    rpc drop_tape_center_peer(DropTapeCenterPeerRequest) returns(AckResponse);
    rpc update_tape_center_task(UpdateTapeCenterTaskRequest) returns (AckResponse);
    rpc fetch_delete_task(FetchDeleteTaskRequest) returns(FetchDeleteTaskResponse);
    rpc report_delete_task(TapeDeleteTaskReportRequest) returns(AckResponse);
    rpc fetch_reclaim_task(FetchReclaimTaskRequest) returns(FetchReclaimTaskResponse);
    rpc report_reclaim_task(TapeReclaimTaskReportRequest) returns(AckResponse);
    rpc fetch_check_task(FetchCheckTaskRequest) returns(FetchCheckTaskResponse);
    rpc report_check_task(CheckTaskReportRequest) returns(AckResponse);
}

message AddTapeCenterPeerRequest {
    optional string token = 1;
    optional uint64 req_addr = 2;
    required uint64 tape_center_addr = 3;
}

message DropTapeCenterPeerRequest {
    optional string token = 1;
    optional uint64 req_addr = 2;
    required uint64 tape_center_addr = 3;
}

message ListTapeTaskRequest {
    optional string token = 1;
    optional uint64 req_addr = 2;
    optional TapeTaskType task_type = 3;
    optional uint32 count = 4;
}

message ListTapeTaskResponse {
    required Status status = 1;
    repeated TapeTaskInfo tasks = 2;
}

message ShowTapeTaskRequest {
    optional string token = 1;
    optional uint64 req_addr = 2;
    optional uint64 task_id = 3;
}

message ShowTapeTaskResponse {
    optional Status status = 1;
    optional TapeTaskInfo task_info = 2;
    optional uint32 failed_cnt = 3;
    optional uint64 priority = 4;
    optional uint64 start_timestamp = 5;
    optional uint64 task_create_time = 6;
    optional uint64 task_deadline = 7;
    optional uint64 last_active_timestamp = 8;
}

message DropTapeTaskRequest {
    optional string token = 1;
    optional uint64 req_addr = 2;
    optional uint64 task_id = 3;
}

message UpdateTapeCenterTaskRequest {
    optional string token = 1;
    optional uint64 req_addr = 2;
    optional uint64 task_id = 3;
    optional uint64 task_begin_timestamp = 4;
}


service TapeCenterMonitorService {
    rpc list_tape_center_task(ListTapeTaskRequest) returns (ListTapeTaskResponse);
    rpc show_tape_center_task(ShowTapeTaskRequest) returns (ShowTapeTaskResponse);
}

service TapeNodeTaskHeartbeatService {
    rpc tape_node_task_heartbeat(TapeNodeTaskHeartbeatRequest) returns(AckResponse);
}

message NotifyRetrieveTaskStatusRequest {
    message BlobGroup {
        message BlobInfo {
            optional uint64 volume_id = 1;
            optional uint64 vbid = 2;
            optional uint64 code = 3;
        }
        optional string tag = 1;
        repeated BlobInfo blob_infos = 2;
    }
    optional string task_id = 1;
    optional uint32 progress = 2;
    optional uint64 remain_time = 3;
    optional bool is_succ = 4;
    repeated BlobGroup finished_blob_groups = 5;
}

service RecieveNoticeService {
    rpc notify_retrieve_task_status(NotifyRetrieveTaskStatusRequest) returns (AckResponse);
}

message TapeNodeTaskHeartbeatRequest {
    message HeartbeatTaskInfo {
        optional uint64 task_id = 1;
        optional TapeTaskType task_type = 2;
        optional uint64 volume_id = 3;
    }
    optional string token = 1;
    optional uint64 req_addr = 2;
    optional string version = 3;
    repeated HeartbeatTaskInfo task_info_list = 4;
}

message BlobId {
    optional uint64 volume_id = 1;
    optional uint64 vbid = 2;
}

message BlobGroup {
    optional string tag = 1;
    repeated BlobId blob_ids = 2;
}

message RetrieveBlobsRequest {
    optional string token = 1;
    optional uint64 req_addr = 2;
    optional string task_id = 3;
    optional uint64 log_id = 4;
    optional uint64 task_deadline = 5;
    repeated BlobGroup blob_groups = 6;
}

message GetRetrieveTaskStatusRequest {
    optional string token = 1;
    optional uint64 req_addr = 2;
    optional string task_id = 3;
};

message GetRetrieveTaskStatusResponse{
    optional RetrieveTaskState state = 1; 
    optional uint32 progress = 2;
    optional uint64 remain_time = 3;
    optional Status status = 4;
};

message CancelRetrieveTaskRequest {
    optional string token = 1;
    optional uint64 req_addr = 2;
    optional string task_id = 3;
    optional uint64 log_id = 4;
};

message BatchUpdateTaskRequest {
    repeated TapeTaskInfo task_list = 1;
}

