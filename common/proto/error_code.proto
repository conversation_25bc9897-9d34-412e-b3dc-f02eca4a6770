syntax="proto2";

package aries;

enum AriesErrno {
    AIE_OK                  =   0;
    AIE_FAIL                =   1;

    AIE_TIMEOUT             =   2;
    AIE_BUSY                =   3;
    AIE_EXIST               =   4;
    AIE_NOT_EXIST           =   5;
    AIE_CONTINUE            =   6;
    AIE_INPROGRESS          =   7;
    AIE_NOT_PRIMARY         =   8;

    AIE_INVALID             =   9;
    AIE_INVALID_TOKEN       =   10;
    AIE_INVALID_MESSAGE     =   11;
    AIE_INVALID_ARGUMENT    =   12;
    AIE_NOTSUPPORT          =   13;
    AIE_PARTIAL             =   14;
    AIE_REDIRECT            =   15;
    AIE_DUPLICATED          =   16;
    AIE_EXCEED_LIMIT        =   17;
    AIE_EXCEED_QUOTA        =   18;
    AIE_CHECKSUM            =   21;

    AIE_VOLUME_NOT_EXIST        = 22;
    AIE_NO_ENOUGH_VLET          = 24;
    AIE_BLOB_NOT_EXIST          = 25;
    AIE_BLOB_ALREADY_EXIST      = 26;
    AIE_DISK_FULL               = 27;
    AIE_VLET_FULL               = 28;
    AIE_UDATA_CHECKSUM          = 29;
    AIE_EDATA_CHECKSUM          = 30;
    AIE_IO_ERROR                = 33;

    AIE_AGAIN                   = 37;
    AIE_REMOVED                 = 38;

    AIE_SPACE_NOT_EXIST         = 39;
    AIE_SPACE_NOT_COLLECTED     = 40;
    AIE_SPACE_NOT_READY         = 42;
    AIE_NOT_EMPTY               = 43;
    AIE_EMPTY                   = 44;

    AIE_MARK_REMOVED            = 46;

    AIE_WRONG_DISK_TYPE         = 47;
    AIE_WRONG_WRITE_OFF         = 48;
    AIE_WRONG_READ_OFF          = 49;
    AIE_INVALID_PATH            = 50;

    AIE_READ_ONLY               = 51;
    AIE_RATIO_TOO_SMALL         = 52;

    AIE_COMPRESS_FAILED         = 53;
    AIE_PARTIAL_EMPTY           = 54;

    AIE_NEED_UPDATE             = 55;

    AIE_CORRUPT                 = 56;

    AIE_INVALID_SESSION         = 57;
    AIE_VOLUME_IS_FULL          = 58;
    AIE_DEGRADED                = 60;
    AIE_RECORD_META_INCONSISTENT   = 61;

    AIE_INVALID_LIB             = 62;
    AIE_SHARD_INDEX_INCONSISTENT = 63;
}
