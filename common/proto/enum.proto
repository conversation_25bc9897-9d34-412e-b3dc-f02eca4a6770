syntax="proto2";

package aries;

import "api-enum.proto";

enum PickAZPolicy {
    PAZP_ROUND_BY_AZ = 0;
    PAZP_UNIT_IN_AZ = 1;
    PAZP_MAX_PER_AZ = 2;
}

enum PickDiskTypePolicy {
    SPACE_RULE_TYPE = 0;
    SAME_DISK_TYPE = 1;
    SPACE_DISK_TYPE = 2;
}

enum ManualBalancePolicy {
    DISK_POLICY = 0;
    VOLUME_POLICY = 1;
}

enum VletMoveType {
    RULE = 0;
    DECOMMISSION = 1;
    USAGE_OVERFLOW = 2;
    USAGE_LACK = 3;
    MANUAL = 4;
    COMPACT = 5;
    COPY_SPACE = 6;
    AZ_USAGE = 7;
}

enum SpaceCompactState {
    NO = 0;
    RUN = 1;
    PAUSE = 2;
    FINISH = 3;
}

enum CompactProgress {
    NONE = 0;
    START = 1;
    RUNNING = 2;
    END = 3;
}

enum CopyProgress {
    COPY_NONE = 0;
    COPY_START = 1;
    COPY_RUNNING = 2;
    COPY_END = 3;
}

enum BalanceFilter {
    F_OVER = 1;
    F_IN_PROGRESS = 2;
    F_ALL = 3;
}

enum BalanceStatus {
    IN_PROGRESS = 0;
    OK = 1;
    FAIL = 2;
    TIME_OUT = 3;
}

enum ECType {
    EC_RS_ISAL = 0; // Intel ISA-L (RS) as default
    REPLICATION = 10;
}

enum VletType {
    VLET_TYPE_ERROR                     = 0;
    // 16G vlet type
    // DataBlock: 1M, MinType: 1Page, MaxType=128Page
    VLET_TYPE_LINKED_16G_1M_512K        = 1;
    // DataBlock: 2M, MinType: 1Page, MaxType=128Page
    VLET_TYPE_LINKED_16G_2M_512K        = 2;

    // DataBlock: 4M, MinType: 1Page, MaxType=128Page
    VLET_TYPE_LINKED_32G_4M_512K        = 11;
    // DataBlock: 8M, MinType: 1Page, MaxType=128Page
    VLET_TYPE_LINKED_32G_8M_512K        = 12;
    // DataBlock: 4M, MinType: 1Page, MaxType=256Page
    VLET_TYPE_LINKED_32G_4M_1M          = 13;
    // DataBlock: 4M, MinType: 1Page, MaxType=1024Page
    VLET_TYPE_LINKED_32G_8M_4M          = 14;

    // DataBlock: 8M, MinType: 1Page, MaxType=256Page
    VLET_TYPE_LINKED_64G_8M_1M          = 17;

    // DataBlock: 1M, MinType: 1Page, MaxType=128Page
    //for mix ext2
    VLET_TYPE_LINKED_4G_1M_512K        = 41;
    VLET_TYPE_LINKED_4G_4M_4M          = 42;
    VLET_TYPE_LINKED_4G_2M_512K        = 43;

    // 8G
    VLET_TYPE_LINKED_8G_2M_512K        = 51;

    // 4G for ssd
    VLET_TYPE_LINKED_4G_1M_64K_SSD     = 60;

    //for smr
    //TotalSize: 32G, smr_zone_size:256M, align_size:4k
    VLET_TYPE_APPEND_32G_256M_4K       = 80;
    VLET_TYPE_APPEND_16G_256M_4K       = 81;
    VLET_TYPE_APPEND_64G_256M_4K       = 82;
    VLET_TYPE_APPEND_8G_256M_4K        = 83;
    VLET_TYPE_APPEND_4G_256M_4K        = 84;
    VLET_TYPE_APPEND_2G_256M_4K        = 85;
    VLET_TYPE_APPEND_2G_64M_4K         = 86;
    VLET_TYPE_APPEND_4G_64M_4K         = 87;

    //for zns ssd
    //TotalSize: 32/64/16GB, zone_size:depend on disk specification, align_size:4K
    VLET_TYPE_ZONE_32G_4K = 90;
    VLET_TYPE_ZONE_64G_4K = 91;
    VLET_TYPE_ZONE_16G_4K = 92;

    VLET_TYPE_EMPTY                     = 100;
    VLET_TYPE_TEST                      = 101;

    // compact vlet used
    VLET_TYPE_LINKED_VARIENT            = 110;
    VLET_TYPE_APPEND_VARIENT            = 120;
    VLET_TYPE_ZONE_VARIENT              = 130;
    VLET_TYPE_STREAM_VARIENT            = 140;
    VLET_TYPE_STREAM_ZONE_VARIENT        = 141;

    // for stream vlet
    VLET_TYPE_STREAM_64G_4K            = 151;
    VLET_TYPE_STREAM_32G_4K            = 152;
    VLET_TYPE_STREAM_16G_4K            = 153;
    VLET_TYPE_STREAM_8G_4K             = 154;
    VLET_TYPE_STREAM_4G_4K             = 155;
    VLET_TYPE_STREAM_2G_4K             = 156;

    // for stream zns vlet
    VLET_TYPE_STREAM_ZONE_64G_4K            = 171;
    VLET_TYPE_STREAM_ZONE_32G_4K            = 172;
    VLET_TYPE_STREAM_ZONE_16G_4K            = 173;
    VLET_TYPE_STREAM_ZONE_8G_4K             = 174;
    VLET_TYPE_STREAM_ZONE_4G_4K             = 175;
    VLET_TYPE_STREAM_ZONE_2G_4K             = 176;
}

enum EngineType {
    ENGINE_AUTO = 0;
    ENGINE_LINKED = 1;
    ENGINE_APPEND = 2;
    ENGINE_ZONE = 3;
    ENGINE_STREAM = 4;
    ENGINE_STREAM_ZONE = 5;
}

enum VletCopyCause {
    COPY_VLET_FOR_BALANCE = 1;
    COPY_VLET_FOR_CHECK = 2;
}

enum SpaceState {
    SPACE_STATE_NORMAL = 0;
    SPACE_STATE_REDUCE_REDUNDENCY = 1;
    SPACE_STATE_COMPACT = 2;
    SPACE_STATE_COPY = 3;
}

enum DiskState {
    DISK_STATE_ERROR = 0;
    DISK_STATE_NORMAL = 1;
    DISK_STATE_UNBALANCED = 2;
    DISK_STATE_DISABLED = 3;
    DISK_STATE_DROPED = 4;
}

enum NodeState {
    NODE_STATE_ERROR = 0;
    NODE_STATE_NORMAL = 1;
    NODE_STATE_UNBALANCED = 2;
    NODE_STATE_DISABLED = 3;
    NODE_STATE_DROPED = 4;
    NODE_STATE_DECOMMISSIONING = 5;
    NODE_STATE_DECOMMISSIONED = 6;
}

enum TapeNodeState {
    TAPE_NODE_STATE_NONE = 0;
    TAPE_NODE_STATE_NORMAL = 1;
    TAPE_NODE_STATE_REPAIRING_TAPE = 2;
    TAPE_NODE_STATE_DISABLED = 3;
}

enum VletState {
    VLET_STATE_ERROR = 0;
    VLET_STATE_CREATING = 1;
    VLET_STATE_NORMAL = 2;
    VLET_STATE_DROPPING = 3;
    VLET_STATE_RECOVERING = 4;
    VLET_STATE_REPAIRING = 5;
    VLET_STATE_DROPPED = 6;
    VLET_STATE_JOINING = 7;
    // for copy vlet
    VLET_STATE_RECEIVING = 8;
    VLET_STATE_COPYING = 9;
    VLET_STATE_COPYED = 10;
}

enum VolumeState {
    VOLUME_STATE_ERROR = 0;
    VOLUME_STATE_CREATING = 1;
    VOLUME_STATE_NORMAL = 2;
    VOLUME_STATE_DROPPED = 3;
    VOLUME_STATE_READONLY = 4;
    VOLUME_STATE_RECEIVING = 5;
}

enum VolumeSessionState {
    VOLUME_SESSION_STATE_NONE = 0;
    VOLUME_SESSION_STATE_OPENED = 1;
    VOLUME_SESSION_STATE_CLOSED = 2;
}

enum VolumeSealState {
    VOLUME_SEAL_STATE_NONE = 0;
    VOLUME_SEAL_STATE_SEALING = 1;
    VOLUME_SEAL_STATE_SEALED = 2;
}

enum MasterState {
    MASTER_STATE_ERROR = 0;
    MASTER_STATE_FOLLOWER = 1;
    MASTER_STATE_LEADER_STARTING = 2;
    MASTER_STATE_LEADER_WORKING = 3;
    MASTER_STATE_LEARNER = 4;
}

enum MasterLogType {
    MASTER_LOG_ADD_NODE = 1;
    MASTER_LOG_DROP_NODE = 2;

    MASTER_LOG_ADD_DISK = 3;
    MASTER_LOG_DROP_DISK = 4;

    MASTER_LOG_CREATE_SPACE = 5;
    MASTER_LOG_UPDATE_SPACE = 6;
    MASTER_LOG_DROP_SPACE = 7;

    MASTER_LOG_CREATE_VOLUMES = 8;

    MASTER_LOG_CREATE_VLET = 9;
    MASTER_LOG_RECOVER_VLET = 10;
    MASTER_LOG_MIGRATE_VLET = 11;
    MASTER_LOG_ADD_VLET = 12;
    MASTER_LOG_REPAIR_VLET_FINISH = 13;
    MASTER_LOG_DROP_VLET = 14;

    MASTER_LOG_ADD_ALLOCATOR = 15;
    MASTER_LOG_DROP_ALLOCATOR = 16;

    MASTER_LOG_ADD_TINKER = 17;
    MASTER_LOG_DROP_TINKER = 18;

    MASTER_LOG_ADD_VOLUME_SERVER = 19;
    MASTER_LOG_DROP_VOLUME_SERVER = 20;

    MASTER_LOG_ASSIGN_ALLOCATOR_SEQUENCE_ID = 21;

    MASTER_LOG_SET_NODE_ALIVE_STATE = 22;

    MASTER_LOG_SET_MEMBERSHIP_FINISH = 23;

    MASTER_LOG_SET_NODE_STATE = 24;

    MASTER_LOG_REPLACE_VLET = 25;

    MASTER_LOG_CREATE_SNAPSHOT = 26;

    MASTER_LOG_REDUCE_SPACE_REDUNDANCY = 27;
    MASTER_LOG_INCREASE_SPACE_REDUNDANCY = 28;

    MASTER_LOG_CONTROL_BALANCE = 29;

    MASTER_LOG_COMPACT_SPACE = 30;

    MASTER_LOG_CANCEL_COMPACT_SPACE = 31;

    MASTER_LOG_CONTROL_COMPACT_SPACE = 32;

    MASTER_LOG_FINISH_COMPACT_SPACE = 33;

    MASTER_LOG_COPY_SPACE = 34;

    MASTER_LOG_CANCEL_COPY_SPACE = 35;

    MASTER_LOG_CONTROL_COPY_SPACE = 36;

    MASTER_LOG_FINISH_COPY_SPACE = 37;

    MASTER_LOG_COPY_VLET_FINISH = 38;

    MASTER_LOG_SET_COPYING_VLET = 39;

    MASTER_LOG_SET_MEMBERSHIP_FAILED = 40;

    MASTER_LOG_SET_CLUSTER = 61;

    MASTER_LOG_DISABLE_AZ = 62;
    MASTER_LOG_ENABLE_AZ = 63;

    MASTER_LOG_ENABLE_VOLUME = 65;
    MASTER_LOG_DISABLE_VOLUME = 66;
    MASTER_LOG_DROP_VOLUME = 67;

    MASTER_LOG_UPDATE_NODE_LOCATION = 68;
    MASTER_LOG_EAT_SPACE = 69;
    MASTER_LOG_DROP_NODE_VLETS = 70;

    MASTER_LOG_UPDATE_VOLUME_COMPACT_PROGRESS = 71;

    MASTER_LOG_UPDATE_COPY_VOLUME_PROGRESS = 72;

    MASTER_LOG_UPDATE_DISK = 73;

    MASTER_LOG_ADD_VOLUMES = 74;

    MASTER_LOG_ENABLE_NODE_SAFEMODE = 75;

    MASTER_LOG_DISABLE_NODE_SAFEMODE = 76;

    MASTER_LOG_ADD_TAPE_CENTER = 77;
    MASTER_LOG_DROP_TAPE_CENTER = 78;

    //MASTER_LOG_OPEN_ARCHIVE_VOLUME = 79;
    //MASTER_LOG_CLOSE_ARCHIVE_VOLUME = 80;
    //MASTER_LOG_FINISH_CLOSE_ARCHIVE_VOLUME = 81;
    MASTER_LOG_SEAL_ARCHIVE_VOLUME = 82;
    //MASTER_LOG_RENEW_VOLUME_SESSION_LEASE = 83;
    MASTER_LOG_SET_VOLUME_TTL = 84;
    MASTER_LOG_UPDATE_VOLUME_TAPE_INFO = 85;
    MASTER_LOG_PURGE_VOLUME = 86;

    MASTER_LOG_ADD_TAPE_NODE = 87;
    MASTER_LOG_DROP_TAPE_NODE = 88;
    MASTER_LOG_ADD_TAPE_LIBRARY_GROUP = 89;
    MASTER_LOG_DROP_TAPE_LIBRARY_GROUP = 90;
    MASTER_LOG_ADD_TAPE_LOGICAL_POOL = 91;
    MASTER_LOG_DROP_TAPE_LOGICAL_POOL = 92;
    MASTER_LOG_UPDATE_ALLOCATOR_SEQUENCE_ID = 93;

    MASTER_LOG_ADD_STREAM_SERVICE = 94;
    MASTER_LOG_DROP_STREAM_SERVICE = 95;
    MASTER_LOG_ADD_STREAM_SERVICE_NODE = 96;
    MASTER_LOG_DROP_STREAM_SERVICE_NODE = 97;

    MASTER_LOG_SET_META_BACKUP_KEYS = 110;
    MASTER_LOG_UPDATE_TAPE_LOGICAL_POOL = 111;

    MASTER_LOG_ADD_VOLUME_TAPE_LOCATION = 115;
}

enum TinkerTaskType {
    TINKER_TASK_TYPE_ERROR = 0;
    TINKER_TASK_TYPE_VOLUME_CHECK = 1;
    TINKER_TASK_TYPE_VOLUME_REPAIR = 2;
    TINKER_TASK_TYPE_VOLUME_DELETE = 3;
}

enum MasterSnapshotLogType {
    MASTER_SNAPSHOT_LOG_BEGIN = 1000;
    MASTER_SNAPSHOT_NODE_INFO = 1001;
    MASTER_SNAPSHOT_DISK_INFO = 1002;
    MASTER_SNAPSHOT_TINKER_INFO = 1003;
    MASTER_SNAPSHOT_ALLOCATOR_INFO = 1004;
    MASTER_SNAPSHOT_SPACE_INFO = 1005;
    MASTER_SNAPSHOT_VOLUME_INFO = 1006;
    MASTER_SNAPSHOT_VLET_INFO = 1007;
    MASTER_SNAPSHOT_END = 1008;
    MASTER_SNAPSHOT_GLOBAL_INFO = 1009;
    MASTER_SNAPSHOT_TAPE_CENTER_INFO = 1010;
    MASTER_SNAPSHOT_TAPE_NODE_INFO = 1011;
    MASTER_SNAPSHOT_TAPE_LOGICAL_POOL_INFO = 1012;
    MASTER_SNAPSHOT_TAPE_LIBRARY_GROUP_INFO = 1013;
    MASTER_SNAPSHOT_STREAMSERVICE_INFO = 1014;
    MASTER_SNAPSHOT_STREAMSERVICE_NODE_INFO = 1015;
    MASTER_SNAPSHOT_META_BACKUP_KEYS_INFO = 1016;
}

enum MasterVletTaskType {
    MASTER_VLET_TASK_TYPE_NONE = -1;
    MASTER_VLET_TASK_TYPE_CREATE = 0;
    MASTER_VLET_TASK_TYPE_RECOVER = 1;
    MASTER_VLET_TASK_TYPE_USAGE_BALANCE = 2;
    MASTER_VLET_TASK_TYPE_RULE_BALANCE = 3;
    MASTER_VLET_TASK_TYPE_COMPACT_BALANCE = 4;
    MASTER_VLET_TASK_TYPE_MANUAL_BALANCE = 5;
    MASTER_VLET_TASK_TYPE_AZ_USAGE_BALANCE = 6;
}

enum TapeCenterState {
    TAPE_CENTER_STATE_ERROR = 0;
    TAPE_CENTER_STATE_FOLLOWER = 1;
    TAPE_CENTER_STATE_LEADER_STARTING = 2;
    TAPE_CENTER_STATE_LEADER_WORKING = 3;
}

enum TapeTaskType {
    TAPE_TRANSFORM_TASK = 1;
    TAPE_RETRIEVE_TASK = 2;
    TAPE_DELETE_TASK = 3;
    TAPE_RECLAIM_TASK = 4;
    TAPE_CHECK_TASK = 5;
}

enum TapeCenterLogType {
    TAPE_CENTER_LOG_ADD_TASK = 1;
    TAPE_CENTER_LOG_FINISH_TASK = 2;
    TAPE_CENTER_LOG_UPDATE_TASK = 3;
    TAPE_CENTER_LOG_BATCH_UPDATE_TASK = 4;
}

enum TapeCenterSnapshotLogType {
    TAPE_CENTER_SNAPSHOT_BEGIN = 1001;
    TAPE_CENTER_SNAPSHOT_END = 1002;
    TAPE_CENTER_SNAPSHOT_TASK_INFO = 1003;
}

enum TapeNodeSnapshotLogType {
    TAPE_NODE_SNAPSHOT_BEGIN = 1001;
    TAPE_NODE_SNAPSHOT_END = 1002;
    TAPE_NODE_SNAPSHOT_TASK_INFO = 1003;
}

enum ArchiveMode {
    TAPE = 1;
    HDD = 2;
}

enum TapeTaskState {
    TAPE_TASK_WAITING = 1;
    TAPE_TASK_TRANSFORM_RUNNING = 2;
    TAPE_TASK_PURGE_WAITING = 3;
    TAPE_TASK_TRANSFORM_FINISH = 4;
    TAPE_TASK_RETRIEVE_RUNNING = 5;
    TAPE_TASK_NOTIFY_WAITING = 6;
    TAPE_TASK_RETRIEVE_FINISH = 7;
    TAPE_TASK_DROPPED = 8;
    TAPE_TASK_DELETE_RUNNING = 9;
    TAPE_TASK_DELETE_FINISH = 10;
    TAPE_TASK_RECLAIM_RUNNING = 11;
    TAPE_TASK_RECLAIM_FINISH = 12;
}

enum VolumePurgeState {
    VOLUME_PURGE_STATE_NONE = 1;
    VOLUME_PURGE_STATE_WAITING = 2;
    VOLUME_PURGE_STATE_FINISH = 3;
}

enum TapeFileState {
    MIGRATE = 1;
    PREMIGRATE = 2;
    RESISDENT = 3;
}

enum RetrieveTaskState {
    RTS_NONE = 1;
    RTS_WAITING = 2;
    RTS_RUNNING = 3;
    RTS_FINISHED = 4;
    RTS_CANCELED = 5;
}

enum EventType {
    EVENT_TYPE_NONE = 0;
    EVENT_TYPE_ADD_NODE = 1;
    EVENT_TYPE_DROP_NODE = 2;
    EVENT_TYPE_ADD_DISK = 3;
    EVENT_TYPE_DROP_DISK = 4;
    EVENT_TYPE_CREATE_VOLUME = 5;
    EVENT_TYPE_DROP_VOLUME = 6;
    EVENT_TYPE_CREATE_VLET = 7;
    EVENT_TYPE_DROP_VLET = 8;
    EVENT_TYPE_MIGRATE_VLET = 9;
    EVENT_TYPE_CREATE_SPACE = 10;
    EVENT_TYPE_DROP_SPACE = 11;
    EVENT_TYPE_UPDATE_SPACE = 12;
}

enum StateServiceLogType {
    STATE_SERVICE_LOG_OPEN_ARCHIVE_VOLUME = 1;
    STATE_SERVICE_LOG_CLOSE_ARCHIVE_VOLUME = 2;
    STATE_SERVICE_LOG_UPDATE_ARCHIVE_VOLUME_SIZE = 3;
    STATE_SERVICE_LOG_RENEW_VOLUME_SESSION_LEASE = 4;
    STATE_SERVICE_LOG_INIT_ARCHIVE_VOLUME = 5;
    STATE_SERVICE_LOG_SEAL_ARCHIVE_VOLUME = 6;

    STATE_SERVICE_LOG_DROP_SPACE = 18;
}

enum StateServiceSnapshotLogType {
    STATE_SERVICE_SNAPSHOT_BEGIN = 1001;
    STATE_SERVICE_SNAPSHOT_END = 1002;
    STATE_SERVICE_SNAPSHOT_VOLUME_INFO = 1003;
    //STATE_SERVICE_SNAPSHOT_STREAM_LEASE_INFO = 1004;
    STATE_SERVICE_SNAPSHOT_GLOBAL_INFO = 1005;
}

enum StreamServiceLogType {
    STREAM_SERVICE_LOG_CREATE_STREAM = 1;
    STREAM_SERVICE_LOG_UNLINK_STREAM = 2;
    STREAM_SERVICE_LOG_REMOVE_STREAM = 3;
    STREAM_SERVICE_LOG_RESTORE_STREAM = 4;
    STREAM_SERVICE_LOG_TRUNCATE_STREAM = 5;
    STREAM_SERVICE_LOG_INIT_VOLUME_FOR_STREAM = 6;
    STREAM_SERVICE_LOG_SEAL_BLOCK = 7;
    STREAM_SERVICE_LOG_OPEN_STREAM_SESSION = 8;
    STREAM_SERVICE_LOG_CLOSE_STREAM_SESSION = 9;
    STREAM_SERVICE_LOG_RENEW_STREAM_SESSION = 10;
    STREAM_SERVICE_LOG_UPDATE_STREAM_SIZE = 11;
    STREAM_SERVICE_LOG_DROP_SPACE = 12;
    STREAM_SERVICE_LOG_SEAL_STREAM = 13;
    STREAM_SERVICE_LOG_SET_GLOBAL_INFO = 14;
    STREAM_SERVICE_LOG_SETATTR_STREAM = 15;
    STREAM_SERVICE_LOG_LINK_STREAM = 16;
    STREAM_SERVICE_LOG_EXPIRE_RPC_TOKEN = 17;
    STREAM_SERVICE_LOG_FORCE_OPEN_STREAM_SESSION = 18;
    STREAM_SERVICE_LOG_SEAL_INACTIVE_BLOCK = 19;
}

enum StreamServiceSnapshotLogType {
    STREAM_SERVICE_SNAPSHOT_BEGIN = 1001;
    STREAM_SERVICE_SNAPSHOT_END = 1002;
    STREAM_SERVICE_SNAPSHOT_STREAM_LEASE_INFO = 1003;
    STREAM_SERVICE_SNAPSHOT_GLOBAL_INFO = 1004;
    STREAM_SERVICE_SNAPSHOT_RPC_TOKEN = 1005;
    STREAM_SERVICE_SNAPSHOT_RAFT_GROUP_INFO = 1006;
}

enum BlockSizeType {
    BLOCK_SIZE_TYPE_ERROR = 0;
    BLOCK_SIZE_TYPE_256MB = 1;
    BLOCK_SIZE_TYPE_512MB = 2;
    BLOCK_SIZE_TYPE_1GB = 3;
    BLOCK_SIZE_TYPE_2GB = 4;
    BLOCK_SIZE_TYPE_4GB = 5;
    BLOCK_SIZE_TYPE_8GB = 6;
    BLOCK_SIZE_TYPE_16GB = 7;
    BLOCK_SIZE_TYPE_32GB = 8;
}

enum StreamTruncateType {
    STREAM_TRUNCATE_HEAD = 0;
    STREAM_TRUNCATE_TAIL = 1;
    STREAM_TRUNCATE_QUORUM = 2;
    STREAM_TRUNCATE_LAST_SYNC = 3;
}

enum PmemChunkStatus {
    PMEM_CHUNK_FREE = 0;
    PMEM_CHUNK_INUSE = 1;
    PMEM_CHUNK_SEALED = 2;
    PMEM_CHUNK_DIRTY = 3;
}

enum PmemRecordStatus {
    PMEM_RECORD_DELETED = 0;
    PMEM_RECORD_NORMAL = 1;
    PMEM_RECORD_MARK_DELETED = 2;
}
