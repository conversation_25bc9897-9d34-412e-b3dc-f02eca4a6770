//=============================================================================
// Author: <EMAIL>
// Data: 2017-01-13 16:51
// Filename: EditLogInputStream.h
// Description: 
//=============================================================================

#ifndef BAIDU_INF_ARIES_COMMON_JOURNAL_EDITLOG_INPUTSTREAM_H
#define BAIDU_INF_ARIES_COMMON_JOURNAL_EDITLOG_INPUTSTREAM_H

#include <stdint.h>
#include <string.h>
#include <base/iobuf.h>
#include <sys/socket.h>
#include <base/logging.h>
#include "baidu/inf/aries-api/common/journal/journal_format.h"
#include "baidu/inf/aries-api/common/data_buff.h"
#include "baidu/inf/aries-api/common/checksum.h"
#include "baidu/inf/aries-api/common/common.h"
#include <boost/filesystem.hpp>

namespace aries {
namespace common {

namespace fs = boost::filesystem;

class EditLogInputStream {
public:
    EditLogInputStream(const std::string& fname, const uint64_t first_txid, 
                       const uint64_t last_txid, bool is_inprogress)
        : _close(true), _fname(fname), _first_txid(first_txid), _last_txid(last_txid),
        _is_inprogress(is_inprogress), _fd(-1) {
        _fpath = fs::path(_fname);
    }
    ~EditLogInputStream() {
    }
    int open();
    inline uint64_t first_txid() {
        return _first_txid; 
    };
    inline uint64_t last_txid() { 
        return _last_txid; 
    };
    inline bool is_inprogress() { 
        return _is_inprogress; 
    };
    inline fs::path path() { 
        return _fpath;
    };
    int next(uint32_t* op, uint64_t* txid, base::IOBuf* buf, uint32_t* record_len = nullptr); 
    int validate(uint64_t* valid_size);	

    inline void close() {
        _close = true;
        int ret = ::close(_fd);
        assert(ret == 0);
        delete this;
    }
    bool _close;
private:
    std::string _fname;
    fs::path _fpath;
    uint64_t _first_txid;
    uint64_t _last_txid;
    bool _is_inprogress;
    base::IOBuf _iobuf;
    int _fd;
};

}
}

#endif
