//=============================================================================
// Author: <EMAIL>
// Data: 2017-01-13 16:51
// Filename: editlog.h
// Description: 
//=============================================================================

#include "journal_format.h"
#include "common/data_buff.h"
#include "common/checksum.h"
#include <baidu/streaming_log.h>
#include <baidu/rpc/errno.pb.h>

namespace aries{
namespace common {

int journal_serialize(base::IOBuf* out_buf, const uint32_t op,
                          const uint64_t txid, const base::IOBuf& msg_buf) {
    // op[4B] | len[4B] | txid[8B] | records | crc32[4B]
    // len: txid + records + crc32
    // crc32: op + len + txid + records

    int msg_len = sizeof(uint64_t) + msg_buf.length() + sizeof(uint32_t);

    base::IOBuf record_buf;
    common::DataOutputBuffer buf(&record_buf);
    buf.write_uint32(op);
    buf.write_uint32(msg_len);
    buf.write_uint64(txid);
    buf.write_raw(msg_buf);

    int crc = common::Crc32CheckSum::compute(record_buf);
    buf.write_uint32(crc);

    out_buf->append(record_buf);

    return 0;
}

int journal_deserialize(base::IOBuf* in_buf, uint32_t* op,
                            uint64_t* txid, base::IOBuf* msg_buf, uint32_t* record_len) {
    // op[4B] | len[4B] | txid[8B] | records | crc32[4B]
    // len: txid + records + crc32
    // crc32: op + len + txid + records

    size_t in_buf_size = in_buf->size();

    size_t rc = 0;
    char header[sizeof(uint32_t) + sizeof(uint32_t) + sizeof(uint64_t)];
    rc = in_buf->copy_to(header, sizeof(header));
    if (rc != sizeof(header)) {
        LOG(WARNING) << "editlog read incomplete record with header, just skip it as eof,"
                << " in_buf_size:" << in_buf_size;
        return baidu::rpc::SYS_EBADMSG;
    }

    common::DataInputBuffer buf(in_buf);

    rc = buf.read_uint32(op);
    assert(rc == sizeof(uint32_t));

    uint32_t len = 0;
    rc = buf.read_uint32(&len);
    assert(rc == sizeof(uint32_t));

    rc = buf.read_uint64(txid);
    assert(rc == sizeof(uint64_t));

    if (len < sizeof(uint64_t) + sizeof(uint32_t)) {
        LOG(WARNING) << "editlog read incomplete record with too-small len, just skip it as eof,"
                << " in_buf_size:" << in_buf_size
                << " txid:" << *txid
                << " len:" << len;
        return baidu::rpc::SYS_EBADMSG;
    }

    uint32_t raw_len = len - sizeof(uint64_t) - sizeof(uint32_t);
    rc = buf.read_raw(msg_buf, raw_len);
    if (rc != raw_len) {
        LOG(WARNING) << "editlog read incomplete record with raw msg, just skip it as eof,"
                << " in_buf_size:" << in_buf_size
                << " txid:" << *txid
                << " raw_msg_len:" << raw_len;
        return baidu::rpc::SYS_EBADMSG;
    }

    if (record_len) {
        *record_len = len + sizeof (uint32_t) + sizeof(uint32_t);
    }

    uint32_t crc;
    rc = buf.read_uint32(&crc);
    if (rc != sizeof(uint32_t)) {
        LOG(WARNING) << "editlog read incomplete record with crc32, just skip it as eof,"
                << " in_buf_size:" << in_buf_size
                << " txid:" << *txid
                << " len:" << len;
        return baidu::rpc::SYS_EBADMSG;
    }

    int prev_crc = common::Crc32CheckSum::compute(header, sizeof(header));
    int compute_crc = common::Crc32CheckSum::compute(*msg_buf, prev_crc);
    if ((uint32_t)compute_crc != crc) {
        LOG(FATAL) << "editlog record crc32 check failed, txid:" << *txid <<
            " actual_crc:" << compute_crc << " expected_crc:" << crc;
        return baidu::rpc::SYS_EBADMSG;
    }

    return 0;
}

int flush_to_fd(int fd, const ::base::IOBuf& buf) {
    ::base::IOBufAsZeroCopyInputStream stream(buf);
    const void *data = NULL;
    int len = 0;
    while (stream.Next(&data, &len)) {
        ssize_t written = ::write(fd, static_cast<const char*>(data), len);
        if (written != (ssize_t)len) {
            LOG(WARNING) << "Snapshot IO Error. len:" << len << ", written:" << written
                << ", errno: " << errno << ", errmsg: " << strerror(errno);
            return -1;
        }
    }
    return 0;

}

}
}
