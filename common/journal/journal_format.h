//=============================================================================
// Author: <EMAIL>
// Data: 2016-09-29 16:51
// Filename: editlog.h
// Description: 
//=============================================================================

#ifndef BAIDU_INF_ARIES_COMMON_JOURNAL_JOURNAL_FORMAT_H
#define BAIDU_INF_ARIES_COMMON_JOURNAL_JOURNAL_FORMAT_H

#include <stdint.h>
#include <base/iobuf.h>

namespace aries{
namespace common {

enum OPCODE {
    START_LOG_SEGMENT = 0,
    END_LOG_SEGMENT = 1,
    OPCODE_USER_BEGIN = 1000,
};

/** 
 * @brief pack header and msg_buf to out_buf
 * 
 * @param out_buf
 * @param op
 * @param txid
 * @param msg_buf
 * 
 * @return   success return 0; else return -1
 */
int journal_serialize(base::IOBuf* out_buf, const uint32_t op,
                          const uint64_t txid, const base::IOBuf& msg_buf);

/** 
 * @brief parse and cut message from in_buf to msg_buf
 * 
 * @param in_buf
 * @param op
 * @param txid
 * @param msg_buf
 * 
 * @return   success return 0; else return -1
 */
int journal_deserialize(base::IOBuf* in_buf, uint32_t* op,
                            uint64_t* txid, base::IOBuf* msg_buf, uint32_t* record_len = nullptr);

int flush_to_fd(int fd, const ::base::IOBuf& buf); 

}
}

#endif
