/***************************************************************************
 * 
 * Copyright (c) 2021 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/
 
 /**
 * @file leader_tracker.cpp
 * <AUTHOR>
 * @date 2021/11/22 10:04:44
 * @version 1.0 
 * @brief 
 *  
 **/

#include "baidu/inf/aries-api/common/leader_tracker.h"

#include <boost/bind.hpp>

namespace aries::common {
LeaderTracker::LeaderTracker() : _port(0), _refresh_timer(0) {
    _refresh_thread = new ThreadPool("leader_tracker", 1);
    _refresh_thread->AddRef();
    _round_index = 0;
    LOG(NOTICE) << "LeaderTracker " << std::hex << this << " created";
}

LeaderTracker::~LeaderTracker() {
    _refresh_thread->Release();
    LOG(NOTICE) << "LeaderTracker " << std::hex << this << " destroyed";
}

int LeaderTracker::start(const base::EndPoint& local_addr, std::string token,
        std::string module_addr, uint32_t refresh_interval_second) {
    int ret = common::parse_address(module_addr, &_hostname, &_port, &_ns_protocol);
    if (ret != 0) {
        LOG(ERROR) << "invalid module address " << module_addr;
        return -1;
    }

    _local_addr = local_addr;
    _leader_addr = base::EndPoint();
    _module_addrs.clear();
    _token = token;
    _refresh_interval_second = refresh_interval_second;

    do_refresh();
    _refresh_thread->start();
    if (_refresh_timer == 0) {
        CHECK_EQ(0, bthread_timer_add(
                &_refresh_timer,
                base::milliseconds_from_now(_refresh_interval_second * 1000),
                on_refresh_timer, (void *)this));
    }
    _is_started = true;
    LOG(NOTICE) << "LeaderTracker succeeded";

    return 0;
}

void LeaderTracker::stop() {
    bthread_timer_del(_refresh_timer);
    _is_started = false;

    {
        ScopedMutexLock lock(_mutex);
        _leader_addr = base::EndPoint();
        _hostname = "";
        _port = 0;
        _module_addrs.clear();
        _refresh_timer = 0;
    }
    _refresh_thread->stop();
    _refresh_thread->join();
}

base::EndPoint LeaderTracker::get_leader() {
    ScopedMutexLock lock(_mutex);
    if (_leader_addr.ip != base::IP_ANY && _leader_addr.port != 0) {
        return _leader_addr;
    } else if (!_module_addrs.empty()) {
        return _module_addrs[base::fast_rand_less_than(_module_addrs.size())];
    } else {
        return base::EndPoint(base::IP_ANY, 0);
    }
}

std::vector<base::EndPoint> LeaderTracker::get_list() {
    std::vector<base::EndPoint> instance_list;
    instance_list.push_back(get_leader());
    get_followers(instance_list[0], &instance_list);
    return instance_list;
}

void LeaderTracker::get_followers(const base::EndPoint& addr, std::vector<base::EndPoint>* follower_addrs) {
    ScopedMutexLock lock(_mutex);
    for (size_t i = 0; i < _module_addrs.size(); i++) {
        if (_module_addrs[i] != addr) {
            follower_addrs->push_back(_module_addrs[i]);
        }
    }
}
void LeaderTracker::set_leader_and_followers(const base::EndPoint& leader, std::vector<base::EndPoint>* followers) {
    ScopedMutexLock lock(_mutex);
    _leader_addr = leader;
    
    if (followers != nullptr) {
        _module_addrs.swap(*followers);
    }
}

void LeaderTracker::on_refresh_timer(void* arg) {
    LeaderTracker* tracker = static_cast<LeaderTracker*>(arg);
    tracker->refresh();

    ScopedMutexLock lock(tracker->_mutex);
    CHECK_EQ(0, bthread_timer_add(&tracker->_refresh_timer,
                      base::milliseconds_from_now(tracker->refresh_interval_second() * 1000),
                      on_refresh_timer, tracker));
}

void LeaderTracker::refresh() {
    ThreadPool::Priority priority = ThreadPool::HIGH_PRIORITY;
    _refresh_thread->submit(boost::bind(&LeaderTracker::do_refresh, this), priority);
}

void LeaderTracker::do_refresh() {
    struct in_addr addr;
    std::string hostname;
    int port = 0;
    {
        ScopedMutexLock lock(_mutex);
        hostname = _hostname;
        port = _port;
    }

    std::vector<base::EndPoint> addrs;
    std::vector<uint32_t> ips;

    if (_ns_protocol == NameServiceProtocol::NS_BNS) {
        // Refresh as BNS
        int ret = common::get_host_ips_by_bns(hostname, &addrs);
        if (ret != 0) {
            LOG(WARNING) << "LeaderTracker get host by BNS name failed, name:" << hostname;
            return;
        }
        if (addrs.size() > 0 && addrs[0].port == 0) {
            for (auto & addr : addrs) {
                addr.port = port;
            }
        }
    } else if (_ns_protocol == NameServiceProtocol::NS_LIST) {
        // Refresh as LIST
        int ret = common::get_host_ips_by_list(hostname, &addrs);
        if (ret != 0) {
            LOG(WARNING) << "LeaderTracker get host by BNS name failed, name:" << hostname;
            return;
        }
    } else if (_ns_protocol == NameServiceProtocol::NS_DNS) {
        // Refresh as DNS
        int ret = common::get_host_ips_by_dns(hostname, &ips);
        if (ret != 0) {
            LOG(WARNING) << "LeaderTracker get host by DNS name failed, name:" << hostname;
            return;
        }
        for (size_t i = 0; i < ips.size(); ++i) {
            base::EndPoint ep(base::int2ip(ips[i]), port);
            addrs.push_back(ep);
        }
    }

    std::map<base::EndPoint, int> candidates;
    
    check_leader(addrs, &candidates);

    bool ok = false;
    base::EndPoint leader;
    int max_vote = 0;
    for (std::map<base::EndPoint, int>::iterator candidate_it = candidates.begin();
         candidate_it != candidates.end(); ++candidate_it) {
        if (candidate_it->second > max_vote) {
            leader = candidate_it->first;
            max_vote = candidate_it->second;
            ok = true;
        }
    }

    size_t addr_count = addrs.size();
    {
        ScopedMutexLock lock(_mutex);
        _module_addrs.swap(addrs);
        if (ok) {
            _leader_addr = leader;
        } else if (_module_addrs.size() > 0) {
            _leader_addr = _module_addrs[++_round_index % _module_addrs.size()];
        }
    }
    LOG(NOTICE) << "refresh " << hostname << " get " << addr_count
        << " addrs, leader: " << common::endpoint2str(leader);
}

void LeaderTracker::update_refresh_interval_second(uint32_t refresh_interval_second) {
    _refresh_interval_second = refresh_interval_second;
}

}

/* vim: set ts=4 sw=4 sts=4 tw=100 */
