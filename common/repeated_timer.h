/**
 * ARIES - A Reliable and Impressive EB-level Storage.
 * Copyright (c) 2019 Baidu.com, Inc. All Rights Reserved.
 *
 * Author: <PERSON><PERSON><PERSON> (<EMAIL>)
 * Date: 2019/02/28
 *
 */

#pragma once

#include <atomic>
#include <assert.h>
#include <bthread_unstable.h>
#include <condition_variable>
#include <mutex>
#include <bthread.h>

namespace aries {
namespace common {

class RepeatedTimer {
public:
    RepeatedTimer()
    : _timeout_ms(0), _is_stopped(true) {};
    virtual ~RepeatedTimer() {
        if (!_is_stopped) {
            LOG(NOTICE) << "the timer is running, not invoke stop(), tid:" << _timer;
        }
    };
    int start() {
        _is_stopped = false;
        int ret = bthread_timer_add(&_timer,
            base::milliseconds_from_now(_timeout_ms), on_timeout, this);
        LOG(NOTICE) << "start timer, tid:" << _timer;
        return ret;
    }
    void stop() {
        bthread_timer_del(_timer);
        _is_stopped = true;
        LOG(NOTICE) << "stop timer, tid:" << _timer;
    }
protected:
    virtual void run_timer() = 0;
    virtual bool interval_changed() = 0;
    static void on_timeout(void* args) {
        bthread_t tid;
        int ret = bthread_start_background(&tid, NULL, run, args);
        if (ret != 0) {
            LOG(WARNING) << "failed to start bthread";
        }
    }
    static void* run(void* args) {
        RepeatedTimer* scheduler = static_cast<RepeatedTimer*>(args);
        scheduler->run_timer();
        scheduler->interval_changed();
        int ret = bthread_timer_add(&scheduler->_timer,
            base::milliseconds_from_now(scheduler->_timeout_ms), on_timeout, scheduler);
        if (ret != 0) {
            LOG(WARNING) << "failed to add timer";
        }
        return nullptr;
    }
    std::atomic<int> _timeout_ms;
    std::atomic<bool> _is_stopped;
    bthread_timer_t _timer;
};

}
}