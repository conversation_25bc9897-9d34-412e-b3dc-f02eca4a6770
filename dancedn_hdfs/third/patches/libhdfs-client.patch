diff --git a/CMakeLists.txt b/CMakeLists.txt
new file mode 100644
index 0000000..9c59ac3
--- /dev/null
+++ b/CMakeLists.txt
@@ -0,0 +1,29 @@
+cmake_minimum_required(VERSION 3.9.0)
+
+project("CloudFS DanceDN Third Party - libhdfs-client" C CXX)
+
+if (CMAKE_C_COMPILER_TARGET MATCHES "x86_64")
+    set(LIBHDFS_CLIENT_PREFIX ${CMAKE_CURRENT_SOURCE_DIR}/x86_64)
+elseif (CMAKE_C_COMPILER_TARGET MATCHES "aarch64")
+    set(LIBHDFS_CLIENT_PREFIX ${CMAKE_CURRENT_SOURCE_DIR}/aarch64)
+else()
+    set(LIBHDFS_CLIENT_PREFIX ${CMAKE_CURRENT_SOURCE_DIR}/x86_64)
+endif()
+
+message(STATUS "LIBHDFS_CLIENT_PREFIX: ${LIBHDFS_CLIENT_PREFIX}")
+
+
+set(LIB_FILES
+    ${LIBHDFS_CLIENT_PREFIX}/lib/libhdfs_client.so
+)
+
+install(
+    DIRECTORY ${LIBHDFS_CLIENT_PREFIX}/include/
+    DESTINATION include/libhdfs-client
+    FILES_MATCHING PATTERN "*.h"
+)
+
+install(
+    FILES ${LIB_FILES}
+    DESTINATION lib
+)
\ No newline at end of file
