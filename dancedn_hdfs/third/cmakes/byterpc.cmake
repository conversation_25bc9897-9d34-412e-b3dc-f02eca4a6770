set(lib_name byterpc)
ExternalProject_Add(
  ${lib_name}
  GIT_REPOSITORY ******************:storage/byterpc.git
  # brpc_george_zhang_new branch for byterpc, we modify some cmake logic for compile hdfs_release_1.5.1 
  GIT_TAG 277ae7803279f4a7223352d1f08aa0a7da01feec
  GIT_SUBMODULES third/tarzan third/byte_express
  GIT_SHALLOW TRUE
  PREFIX ${CMAKE_CURRENT_BINARY_DIR}/${lib_name}
  DOWNLOAD_DIR ${DOWNLOAD_DIR}
  BUILD_IN_SOURCE FALSE
  CMAKE_ARGS
    ${common_cmake_args}
    "-DCMAKE_CXX_FLAGS=${CXX_COMMON_FLAGS} -Wno-deprecated-declarations -Wno-address -Wno-class-memaccess"
    -DBYTERPC_ENABLE_BYTE_EXPRESS=ON
    -DBYTERPC_ENABLE_LAVATRANS=OFF
    -DBYTERPC_WITH_LIBUNWIND=ON
    -DBYTERPC_WITH_NUMA=OFF
    -DBYTERPC_USE_INTERNAL_BOOST_LIBRARY=ON
    -DBYTERPC_ENABLE_LTO_OPTIMIZATION=OFF
    -DBYTERPC_BUILD_TOOLS=OFF
    -DBYTERPC_BUILD_PERF=OFF
    -DBYTERPC_ENABLE_IOBUF_MTHREADS=ON
    -DBYTERPC_ENABLE_ASAN=OFF
    -DSPDLOG_FMT_EXTERNAL=OFF
    -DBYTERPC_BUILD_TESTS=OFF         # do not build test
    -DBYTERPC_GFLAGS_PROVIDER=package # find already compiled gflags
    -DBYTE_EXPRESS_USE_GLOG=OFF       # TODO: fix why byte_express can not find glog, even we provide the following two flags
    -DBYTERPC_GLOG_PROVIDER=package   
    -DGLOG_INCLUDE_PATH=${CMAKE_INSTALL_PREFIX}/include
  BUILD_COMMAND make -s -j${NUM_JOBS_PER_LIB}
  INSTALL_COMMAND make -s install
  LOG_CONFIGURE TRUE
  LOG_BUILD TRUE
  LOG_INSTALL TRUE
  LOG_OUTPUT_ON_FAILURE TRUE
  )
