set(lib_name libhdfs-client)
ExternalProject_Add(
  ${lib_name}
  GIT_REPOSITORY ******************:dp/libhdfs_client_debian9.git
  GIT_TAG 6860ba35238f8e13ddc95b63a59bdd5957097a9b
  GIT_SUBMODULES ""
  PREFIX ${CMAKE_CURRENT_BINARY_DIR}/${lib_name}
  DOWNLOAD_DIR ${DOWNLOAD_DIR}
  PATCH_COMMAND
    git checkout -- . && git clean -f && patch -p1 < ${CMAKE_SOURCE_DIR}/patches/libhdfs-client.patch
  BUILD_IN_SOURCE FALSE
  CMAKE_ARGS
    ${common_cmake_args}
  BUILD_COMMAND make -s -j${NUM_JOBS_PER_LIB}
  INSTALL_COMMAND make -s install
  LOG_CONFIGURE TRUE
  LOG_BUILD TRUE
  LOG_INSTALL TRUE
  )
