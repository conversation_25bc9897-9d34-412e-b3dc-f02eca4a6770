set(lib_name jerasure)
ExternalProject_Add(
  ${lib_name}
  DOWNLOAD_COMMAND ${CMAKE_COMMAND} -E echo "skip download"
  SOURCE_DIR ${CMAKE_SOURCE_DIR}/build/byte-thirdparty/src/byte-thirdparty
  SOURCE_SUBDIR ${lib_name}
  PREFIX ${CMAKE_CURRENT_BINARY_DIR}/${lib_name}
  DOWNLOAD_DIR ${DOWNLOAD_DIR}
  BUILD_IN_SOURCE TRUE
  CONFIGURE_COMMAND
    ${common_configure_envs}
    "LD_LIBRARY_PATH=${CMAKE_INSTALL_PREFIX}/lib"
    ./configure
        --prefix=${CMAKE_INSTALL_PREFIX}
  BUILD_COMMAND make -s -j${NUM_JOBS_PER_LIB}
  INSTALL_COMMAND make -s install
  LOG_CONFIGURE TRUE
  LOG_BUILD TRUE
  LOG_INSTALL TRUE
  )


ExternalProject_Add_Step(${lib_name} autoreconf
  DEPENDEES download
  DEPENDERS configure
  ALWAYS FALSE
  COMMAND autoreconf --force --install -I m4 .
  WORKING_DIRECTORY <SOURCE_DIR>/${lib_name}
  )

include(ExternalProject)

ExternalProject_Add_Step(${lib_name} copy_after_install
  DEPENDEES install
  COMMAND ${CMAKE_COMMAND} -E make_directory "${CMAKE_INSTALL_PREFIX}/include/jerasure/include"
  COMMAND ${CMAKE_COMMAND} -E copy_directory ${CMAKE_SOURCE_DIR}/build/byte-thirdparty/src/byte-thirdparty/jerasure/include "${CMAKE_INSTALL_PREFIX}/include/jerasure/include"
  COMMAND ${CMAKE_COMMAND} -E remove ${CMAKE_INSTALL_PREFIX}/include/jerasure.h
)