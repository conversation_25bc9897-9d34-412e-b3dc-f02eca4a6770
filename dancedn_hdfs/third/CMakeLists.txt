cmake_minimum_required(VERSION 3.9.0)

project("DanceDN Third Party" C CXX)


# ===============================================
# Set some default configurations.
# ===============================================
if("${CMAKE_CXX_COMPILER_ID}" STREQUAL "GNU" AND CMAKE_CXX_COMPILER_VERSION VERSION_GREATER 8.0)
    set(CMAKE_CXX_STANDARD 17)
else()
    set(CMAKE_CXX_STANDARD 14)
endif()
set(CMAKE_CXX_STANDARD_REQUIRED ON)
set(CMAKE_CXX_EXTENSIONS OFF)
set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -ggdb3 -fPIC -fno-omit-frame-pointer")
if("${CMAKE_CXX_COMPILER_ID}" STREQUAL "GNU" AND CMAKE_CXX_COMPILER_VERSION VERSION_GREATER 10.0)
    set(CMAKE_POSITION_INDEPENDENT_CODE ON)
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -Wno-address-of-packed-member")
endif()
if("${CMAKE_CXX_COMPILER_ID}" STREQUAL "GNU" AND CMAKE_CXX_COMPILER_VERSION VERSION_GREATER 8.0)
    if(CMAKE_CXX_STANDARD LESS 17)
        set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -Wno-aligned-new")
    endif()
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -Wno-class-memaccess")
endif()
if(CMAKE_HOST_SYSTEM_PROCESSOR STREQUAL "aarch64")
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -fsigned-char -ffp-contract=off")
endif()

# ===============================================
# Detect and print building system environment.
# ===============================================
cmake_host_system_information(RESULT num_cores QUERY NUMBER_OF_PHYSICAL_CORES)
cmake_host_system_information(RESULT available_memory_mb QUERY AVAILABLE_PHYSICAL_MEMORY)
execute_process(
  COMMAND ldd --version
  COMMAND head -1
  COMMAND cut -d ")" -f 2
  COMMAND cut -d " " -f 2
  OUTPUT_VARIABLE GLIBC_VERSION
  OUTPUT_STRIP_TRAILING_WHITESPACE
  )

# Set the number of building jobs to jobs_by_cpu,
# iff NUM_JOBS_PER_LIB has not been set or set to 0.
if (NOT ${NUM_JOBS_PER_LIB} OR ${NUM_JOBS_PER_LIB} EQUAL 0)
  set(NUM_JOBS_PER_LIB ${num_cores})
endif()

message(STATUS "Number of online physcial CPU cores: ${num_cores}")
message(STATUS "Available physical memory: ${available_memory_mb} MB")
message(STATUS "Building each third party with ${NUM_JOBS_PER_LIB} jobs")
message(STATUS "GLIBC version: ${GLIBC_VERSION}")

# ===============================================
# Set building configurations.
# ===============================================
set(DOWNLOAD_DIR ${CMAKE_SOURCE_DIR}/downloads)

# reset all third lib install path
if("${CMAKE_INSTALL_PREFIX}" STREQUAL "/usr/local")
  SET(CMAKE_INSTALL_PREFIX ${CMAKE_CURRENT_SOURCE_DIR}/install)
endif()
if("${CMAKE_BUILD_TYPE}" STREQUAL "")
  SET(CMAKE_BUILD_TYPE "ReleaseWithD")
endif()

# Add flags that are common across build types
set(CXX_COMMON_FLAGS "-fno-omit-frame-pointer -fPIC -Wno-unused-result ${COMPILER_FLAGS}")
if(DISABLE_CXX11_ABI)
  set(CXX_COMMON_FLAGS "${CXX_COMMON_FLAGS} -D_GLIBCXX_USE_CXX11_ABI=0")
endif()
set(COMMON_LINKER_FLAGS "-static-libstdc++ -static-libgcc")
message(STATUS "CMAKE_CXX_COMPILER: ${CMAKE_CXX_COMPILER}")
message(STATUS "CMAKE_INSTALL_PREFIX: ${CMAKE_INSTALL_PREFIX}")
message(STATUS "CXX_COMMON_FLAGS: ${CXX_COMMON_FLAGS}")
message(STATUS "COMMON_LINKER_FLAGS: ${COMMON_LINKER_FLAGS}")

# Auto detect CPU instruction set which we can use.
include(../cmakes/cpu_features.cmake)
message(STATUS "Enabled CPU Options: ${COMPILER_FLAGS}")

# Cmake common args
set(common_cmake_args
  "-DCMAKE_INSTALL_PREFIX=${CMAKE_INSTALL_PREFIX}"
  "-DCMAKE_PREFIX_PATH=${CMAKE_INSTALL_PREFIX}"
  "-DCMAKE_CXX_COMPILER=${CMAKE_CXX_COMPILER}"
  "-DCMAKE_C_COMPILER=${CMAKE_C_COMPILER}"
  "-DCMAKE_CXX_FLAGS=${CMAKE_CXX_FLAGS} ${CXX_COMMON_FLAGS}"
  "-DCMAKE_C_FLAGS=${CMAKE_C_FLAGS} ${CXX_COMMON_FLAGS}"
  "-DCMAKE_EXE_LINKER_FLAGS=${COMMON_LINKER_FLAGS}"
  "-DCMAKE_SHARED_LINKER_FLAGS=${COMMON_LINKER_FLAGS}"
  "-DCMAKE_INCLUDE_PATH=${CMAKE_INSTALL_PREFIX}/include"
  "-DCMAKE_LIBRARY_PATH=${CMAKE_INSTALL_PREFIX}/lib"
  "-DCMAKE_BUILD_TYPE=${CMAKE_BUILD_TYPE}"
  )

# Configure common args and env variables
set(common_configure_args
  --prefix=${CMAKE_INSTALL_PREFIX}
  )
set(common_configure_envs
  "env"
  "CC=${CMAKE_C_COMPILER}"
  "CXX=${CMAKE_CXX_COMPILER}"
  "CFLAGS=${CMAKE_C_FLAGS} -O3 ${CXX_COMMON_FLAGS}"
  "CXXFLAGS=${CMAKE_CXX_FLAGS} -O3 ${CXX_COMMON_FLAGS}"
  "CPPFLAGS=-I ${CMAKE_INSTALL_PREFIX}/include ${CXX_COMMON_FLAGS}"
  "LDFLAGS=-L${CMAKE_INSTALL_PREFIX}/lib ${COMMON_LINKER_FLAGS}"
  )

include(ExternalProject)

set(DANCEDN_THIRDPARTY_TARGETS
  # no other lib dependency
  byte-thirdparty
  abseil-cpp
  zlib

  # dependency libs: byte-thirdparty
  glog
  protobuf
  gflags
  gperftools
  spdlog
  nlohmann-json
  msgpack
  ev
  isa-l
  cryptopp
  zookeeper
  gf-complete
  gtest
  
  # dependency libs: byte-thirdparty zlib
  openssl

  # dependency libs: openssl
  zti-sdk

  # dependency libs: gf-complete byte-thirdparty
  jerasure

  # dependency libs: byte-thirdparty abseil-cpp protobuf openssl gflags 
  brpc

  # dependency libs: msgpack spdlog nlohmann-json brpc gflags abseil-cpp spdlog 
  byte

  # dependency libs: byte glog
  byterpc
  
  # dependency libs: byte brpc byterpc ev cryptopp isa-l zookeeper
  bytestore

  # the following libs are used by CFS side
  # libhdfs-client
  # libcurl
  # aws
  )

foreach(target ${DANCEDN_THIRDPARTY_TARGETS})
  message("include cmakes/${target}.cmake")
  include(cmakes/${target}.cmake)
  list(APPEND CLEAN_TARGETS ${target}-clean)
  if (TARGET ${target})
    message(STATUS "Add target ${target}")
  else()
    message(FATAL "Miss target ${target}")
  endif()
endforeach()

add_dependencies(gflags byte-thirdparty)
add_dependencies(glog byte-thirdparty)
add_dependencies(spdlog byte-thirdparty)
add_dependencies(protobuf byte-thirdparty)
add_dependencies(msgpack byte-thirdparty)
add_dependencies(gperftools byte-thirdparty)
add_dependencies(ev byte-thirdparty)
add_dependencies(cryptopp byte-thirdparty)
add_dependencies(isa-l byte-thirdparty)
add_dependencies(zookeeper byte-thirdparty)
add_dependencies(gf-complete byte-thirdparty)
add_dependencies(nlohmann-json byte-thirdparty)
add_dependencies(gtest byte-thirdparty)

add_dependencies(openssl zlib byte-thirdparty)
add_dependencies(jerasure gf-complete byte-thirdparty)

add_dependencies(zti-sdk openssl)

add_dependencies(brpc abseil-cpp protobuf openssl gflags byte-thirdparty)

add_dependencies(byte msgpack spdlog nlohmann-json brpc gflags abseil-cpp spdlog)

add_dependencies(byterpc byte glog)

add_dependencies(bytestore byte brpc byterpc ev cryptopp isa-l zookeeper)


# used by CFS
#add_dependencies(aws libcurl openssl)

add_custom_target(
  clean-all
  DEPENDS ${CLEAN_TARGETS}
  )
