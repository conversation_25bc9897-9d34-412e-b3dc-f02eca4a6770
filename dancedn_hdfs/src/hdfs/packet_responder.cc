// Copyright (c) 2019-present, ByteDance Inc. All rights reserved.

#include "hdfs/packet_responder.h"

#include "byte/include/byte_log.h"
#include "byte/string/format/print.h"
#include "byte/system/timestamp.h"
#include "common/media_flags.h"
#include "common/metrics.h"
#include "gflags/gflags.h"
#include "hdfs/block_receiver.h"
#include "hdfs/cond.h"
#include "hdfs/data_xceiver.h"
#include "hdfs/datanode.h"
#include "hdfs/datanode_info.h"
#include "hdfs/datanode_stream_server.h"
#include "hdfs/extended_block.h"
#include "hdfs/io/connection.h"
#include "hdfs/io/define.h"
#include "hdfs/pipeline_ack.h"
#include "hdfs/replica_in_pipeline.h"
#include "hdfs/util.h"

DECLARE_MEDIA_FLAG_uint32(bytestore_hdfs_slow_io_warning_threshold_ms);

namespace bytestore {
namespace chunkserver {
namespace hdfs {

DECLARE_HISTOGRAM_METRICS_POOL(hdfs_slow_log_reply_ack_upstream_ms);
DECLARE_HISTOGRAM_METRICS_POOL(hdfs_packet_responder_send_ack_latency_us);

std::string PacketResponderTypeString(PacketResponderType type) {
  switch (type) {
    case NON_PIPELINE: return "NON_PIPELINE";
    case LAST_IN_PIPELINE: return "LAST_IN_PIPELINE";
    case HAS_DOWNSTREAM_IN_PIPELINE: return "HAS_DOWNSTREAM_IN_PIPELINE";
    default: return nullptr;
  }
}

std::string AckStatusString(Status status) {
  switch (status) {
    case Status::SUCCESS: return "SUCCESS";
    case Status::ERROR: return "ERROR";
    case Status::ERROR_CHECKSUM: return "ERROR_CHECKSUM";
    case Status::ERROR_INVALID: return "ERROR_INVALID";
    case Status::ERROR_EXISTS: return "ERROR_EXISTS";
    case Status::ERROR_ACCESS_TOKEN: return "ERROR_ACCESS_TOKEN";
    case Status::CHECKSUM_OK: return "CHECKSUM_OK";
    case Status::ERROR_UNSUPPORTED: return "ERROR_UNSUPPORTED";
    case Status::OOB_RESTART: return "OOB_RESTART";
    case Status::OOB_RESERVED1: return "OOB_RESERVED1";
    case Status::OOB_RESERVED2: return "OOB_RESERVED2";
    case Status::OOB_RESERVED3: return "OOB_RESERVED3";
    case Status::IN_PROGRESS: return "IN_PROGRESS";
    default: return nullptr;
  }
}

class PacketAck {
 public:
  PacketAck()
      : seqno_(-2),
        last_packet_in_block_(false),
        offset_in_block_(0),
        ack_enqueue_nano_time_(0),
        is_empty_(true) {}
  PacketAck(const int64_t seqno, const bool last_packet, const int64_t offset,
            const int64_t ack_enqueue_time, const Status ack_status)
      : seqno_(seqno),
        last_packet_in_block_(last_packet),
        offset_in_block_(offset),
        ack_enqueue_nano_time_(ack_enqueue_time),
        ack_status_(ack_status),
        is_empty_(false) {}

  ~PacketAck() {}

  std::string ToString() const {
    return byte::StringPrint(
        "Packet(seqno=%ld, "
        "last_packet_in_block=%b, offset_in_block=%ld, "
        "ack_enqueue_nano_time=%ld, ack_status=%s)",
        seqno_, last_packet_in_block_, offset_in_block_, ack_enqueue_nano_time_,
        AckStatusString(ack_status_));
  }

  bool IsEmpty() {
    return is_empty_;
  }

 public:
  static PacketAck EMPTY_ACK;

 public:
  int64_t seqno_;
  bool last_packet_in_block_;
  int64_t offset_in_block_;
  int64_t ack_enqueue_nano_time_;
  Status ack_status_;
  bool is_empty_;
};

PacketAck PacketAck::EMPTY_ACK;

PacketResponder::PacketResponder(BlockReceiver* receiver,
                                 io::Connection* upstream_out,
                                 io::Connection* downstream_in,
                                 std::vector<DatanodeInfo*> downstreams)
    : receiver_(receiver),
      upstream_(upstream_out),
      downstream_(downstream_in),
      running_(true),
      sending_(false),
      queue_cv_(&queue_mutex_),
      send_cv_(&send_mutex_) {
  // PacketResponderType::NON_PIPELINE will never comeup
  if (downstreams.empty()) {
    type_ = PacketResponderType::LAST_IN_PIPELINE;
  } else {
    type_ = PacketResponderType::HAS_DOWNSTREAM_IN_PIPELINE;
  }
  LOG(DEBUG) << "packet responder type:" << unsigned(type_);

  has_stopped_ = false;
  details_ = byte::StringPrint("PacketResponder: %s, type=%s",
                               receiver_->GetBlock()->ToString(),
                               PacketResponderTypeString(type_));
  if (type_ == PacketResponderType::HAS_DOWNSTREAM_IN_PIPELINE) {
    details_ = byte::StringPrint(
        "%s, downstreams=%d:[%s]", details_, downstreams.size(),
        ObjectJoiner<DatanodeInfo>(",").Join(downstreams));
  }
  SetName("PacketResponder");
}

PacketResponder::~PacketResponder() {
  // delete receiver_ by DataXceiver
  // delete upstream_ by DataXceiver
  // delete downstream_ by DataXceiver
  Stop();
  Join();
}

void PacketResponder::Close() {
  {
    byte::MutexLocker guard(&queue_mutex_);
    queue_cv_.WaitUtil(
        [this]() {
          return !ShouldRun() || ack_queue_.size() == 0 || IsStopped() ||
                 IsInterrupted();
        },
        0);
    LOG(DEBUG) << "close packet_responder";
    MarkStop();
    queue_cv_.Broadcast();
  }
  send_cv_.Broadcast();
}

void PacketResponder::Stop() {
  if (has_stopped_) return;
  has_stopped_ = true;
  Thread::Stop();
  if (type_ == PacketResponderType::HAS_DOWNSTREAM_IN_PIPELINE) {
    BYTE_ASSERT(downstream_ != nullptr);
    downstream_->ShutdownConnection();
  }

  queue_cv_.Broadcast();
  send_cv_.Broadcast();
}

bool PacketResponder::ShouldRun() const {
  auto datanode = receiver_->GetDataNode();
  return IsRunning() && datanode->ShouldRun();
}

bool PacketResponder::IsRunning() const {
  return *(static_cast<volatile bool*>(&running_));
}

void PacketResponder::MarkStop() {
  *(static_cast<volatile bool*>(&running_)) = false;
}

std::string PacketResponder::ToString() const {
  return details_;
}

void PacketResponder::Enqueue(const int64_t seqno, const bool last_packet,
                              const int64_t offset, const Status ack_status) {
  LOG(DEBUG) << details_ << " enqueue: " << ack_status;
  byte::MutexLocker guard(&queue_mutex_);
  if (IsRunning()) {
    ack_queue_.emplace(seqno, last_packet, offset, byte::GetCurrentTimeInNs(),
                       ack_status);
    queue_cv_.Broadcast();
  }
}

PacketAck PacketResponder::WaitForAckHeader(int64_t seqno) {
  byte::MutexLocker guard(&queue_mutex_);
  queue_cv_.WaitUtil(
      [this]() {
        return !ShouldRun() || ack_queue_.size() > 0 || IsStopped() ||
               IsInterrupted();
      },
      0);
  // if the repsonder was interrupted by block_receiver, return empty_ack
  return IsStopped() || IsInterrupted() ? PacketAck::EMPTY_ACK
                                        : ack_queue_.front();
}

void PacketResponder::RemoveAckHead() {
  byte::MutexLocker guard(&queue_mutex_);
  ack_queue_.pop();
  queue_cv_.Broadcast();
}

exceptions::Exception PacketResponder::SendOOB(const Status oob_status) {
  if (!IsRunning()) {
    LOG(INFO) << "Cannot send OOB response " << AckStatusString(oob_status)
              << ". Responder is not running.";
    return exceptions::Exception();
  }

  if (!PipelineAck::IsValidOOB(oob_status)) {
    return exceptions::Exception(
        exceptions::E::kIOException,
        byte::StringPrint("Not an OOB status: %s",
                          AckStatusString(oob_status)));
  }

  {
    byte::MutexLocker guard(&send_mutex_);
    if (*(static_cast<volatile bool*>(&sending_))) {
      send_cv_.WaitUtil(
          [this]() {
            return !*(static_cast<volatile bool*>(&sending_)) || IsStopped() ||
                   IsInterrupted();
          },
          PipelineAck::GetOOBTimeout(oob_status));
      if (IsInterrupted() || IsStopped()) {
        // notify the caller that the method has been interrupted
        return exceptions::Exception(exceptions::E::kInterruptedException);
      }
      if (*(static_cast<volatile bool*>(&sending_))) {
        std::string msg =
            byte::StringPrint("Could not send OOB reponse in time: %s",
                              AckStatusString(oob_status));
        return exceptions::Exception(exceptions::E::kIOException, msg);
      }
    }
    *(static_cast<volatile bool*>(&sending_)) = true;
  }

  LOG(INFO) << "Sending an out of band ack(OOB) of type "
            << AckStatusString(oob_status);
  std::vector<uint64_t> empty_list;
  auto e = SendAckWithoutPending(nullptr, PipelineAck::UNKOWN_SEQNO, 0, 0,
                                 oob_status, empty_list);

  byte::MutexLocker guard(&send_mutex_);
  *(static_cast<volatile bool*>(&sending_)) = false;
  send_cv_.Signal();
  return e;
}

exceptions::Exception PacketResponder::SendAck(
    PipelineAck* ack, int64_t seqno, uint64_t total_ack_time_nanos,
    int64_t offset_in_block, Status cur_status,
    std::vector<uint64_t> per_peer_ack_time_nanos) {
  {
    byte::MutexLocker guard(&send_mutex_);
    send_cv_.WaitUtil(
        [this]() {
          return !*(static_cast<volatile bool*>(&sending_)) || IsStopped() ||
                 IsInterrupted();
        },
        0);
    if (IsInterrupted() || IsStopped()) {
      // The responder was interrupted. Make it go down without
      // interrupting the receiver(writer) thread.
      MarkStop();
      return exceptions::Exception();
    }
    *(static_cast<volatile bool*>(&sending_)) = true;
  }

  if (!IsRunning()) return exceptions::Exception();
  auto e =
      SendAckWithoutPending(ack, seqno, total_ack_time_nanos, offset_in_block,
                            cur_status, per_peer_ack_time_nanos);

  byte::MutexLocker guard(&send_mutex_);
  *(static_cast<volatile bool*>(&sending_)) = false;
  send_cv_.Signal();
  return e;
}

exceptions::Exception PacketResponder::SendAckWithoutPending(
    PipelineAck* ack, int64_t seqno, uint64_t total_ack_time_nanos,
    int64_t offset_in_block, Status cur_status,
    std::vector<uint64_t> per_peer_ack_time_nanos) {
  std::vector<Status> replies;
  if (ack == nullptr) {
    replies.push_back(cur_status);
  } else if (receiver_->HasMirrorError()) {
    replies.push_back(Status::SUCCESS);
    replies.push_back(Status::ERROR);
  } else {
    auto downstream_replies = ack->GetReplies();
    uint32_t ack_len = type_ == PacketResponderType::LAST_IN_PIPELINE
                           ? 0
                           : ack->GetStatusSize();
    // If the mirror has reported that it received a corrupt packet,
    // do self-destruct to mark myself bad, instead of making the
    // mirror node bad. The mirror is guaranteed to be good without
    // corrupt data on disk.
    if (ack_len > 0 && downstream_replies.front() == Status::ERROR_CHECKSUM) {
      LOG(ERROR) << "down stream reported data corrupt";
      std::string msg =
          "Shutting down writer and responder "
          "since the down streams reported the data sent by this "
          "thread is corrupt.";
      return exceptions::Exception(exceptions::E::kIOException, msg);
    }

    replies.push_back(cur_status);
    replies.insert(replies.end(), downstream_replies.begin(),
                   downstream_replies.end());
  }

  PipelineAck reply_ack(seqno, replies, total_ack_time_nanos,
                        per_peer_ack_time_nanos);
  auto replica_info = receiver_->GetReplicaInfo();
  // Update visible length after receiving all acks of one packet from
  // downstream
  if (reply_ack.AllSuccess() &&
      offset_in_block > (int64_t)replica_info->GetBytesAcked()) {
    LOG(DEBUG) << "update bytes acked after receiving all acks success length:"
               << offset_in_block;
    replica_info->SetBytesAcked(offset_in_block);
  }

  uint64_t begin_time_us = byte::GetCurrentTimeInUs();
  auto flag = reply_ack.WriteTo(upstream_);
  if (flag != IO_OK) {
    return exceptions::Exception(exceptions::E::kIOException,
                                 std::to_string(flag));
  }
  uint64_t duration_us = byte::GetCurrentTimeInUs() - begin_time_us;
  METRICS_hdfs_packet_responder_send_ack_latency_us->GetMetric(receiver_->tags_)
      ->Set(duration_us);
  if (duration_us / 1000 > receiver_->datanode_slow_log_threshold_ms_) {
    METRICS_hdfs_slow_log_reply_ack_upstream_ms->GetMetric(receiver_->tags_)
        ->Set(duration_us / 1000);
    LOG(WARNING) << "Slow PacketResponder send ack to upstream took "
                 << duration_us / 1000 << "ms (threshold="
                 << receiver_->datanode_slow_log_threshold_ms_ << "ms), "
                 << details_ << ", replyAck=" << reply_ack.ToString()
                 << ", block=" << receiver_->GetBlock()->ToString();
  }
  LOG(DEBUG) << "send reply ack success, ack:" << reply_ack.ToString();
  // If a corruption was detected in the received data, terminate after
  // sending ERROR_CHECKSUM back.
  if (cur_status == Status::ERROR_CHECKSUM) {
    LOG(ERROR) << "corruption was detected in the received data";
    std::string msg =
        "Shutting down writer and responder "
        "due to a checksum error in received data. The error "
        "response has been sent upstream.";
    return exceptions::Exception(exceptions::E::kIOException, msg);
  }
  return exceptions::Exception();
}

void PacketResponder::Run() {
  bool last_packet_in_block = false;
  uint64_t start_time = byte::GetCurrentTimeInNs();
  auto dn = receiver_->dn_;
  dn->GetDataNodeStreamServer()->AddXceiverOperation(
      receiver_->block_->GetBlockPoolID(),
      DataNodeStreamServer::Operation::RespondPacket);
  while (ShouldRun() && !IsStopped() && !last_packet_in_block) {
    uint64_t total_ack_time_nanos = 0;
    PacketAck pkt = PacketAck::EMPTY_ACK;
    PipelineAck ack;
    int64_t seqno = PipelineAck::UNKOWN_SEQNO;
    int64_t ack_recv_nano_time = 0;
    std::vector<uint64_t> per_peer_ack_time_nanos;
    exceptions::Exception e;
    // If there are downstream nodes, get the ack of them first.
    if (type_ == PacketResponderType::HAS_DOWNSTREAM_IN_PIPELINE &&
        !receiver_->HasMirrorError()) {
      auto flag = ack.ReadFrom(downstream_);
      if (flag != IO_OK) {
        e = exceptions::Exception(exceptions::E::kIOException);
        if (receiver_->PacketSentInTime()) {
          // If upstream has sent a packet in time, then mark downstream bad,
          goto TryMarkDownstreamBad;
        } else {
          // otherwise propagate the error up by closing the connection.
          LOG(WARNING) << "The downstream error might be due to "
                       << "congestion in upstream including this node. "
                       << "Propagating the error: EOFException";
          goto TryCloseConnection;
        }
      }
      ack_recv_nano_time = byte::GetCurrentTimeInNs();
      LOG(DEBUG) << "receive ack:" << ack.ToString()
                 << " responder detail:" << details_;
      // If there is an oob_status in the pipeline, then send it first.
      int index = ack.FindOOB();
      if (index >= 0) {
        Status oob_status = ack.GetReply(index);
        LOG(INFO) << "Relaying an out of band ack of type "
                  << AckStatusString(oob_status);
        std::vector<uint64_t> empty_list;
        e = SendAck(&ack, PipelineAck::UNKOWN_SEQNO, 0, 0, Status::SUCCESS,
                    empty_list);
        if (!e.OK()) {
          goto TryMarkDownstreamBad;
        }
        continue;
      }
      // get seqno from downstream
      seqno = ack.GetSeqno();
    }
    // Get corresponding packet_ack of the current node
    if (seqno != PipelineAck::UNKOWN_SEQNO ||
        type_ == PacketResponderType::LAST_IN_PIPELINE) {
      pkt = WaitForAckHeader(seqno);
      LOG(DEBUG) << "get front ack pkt from ack queue, pkt:" << pkt.ToString();
      if (IsInterrupted() || IsStopped()) {
        goto TryMarkDownstreamBad;
      }
      if (!ShouldRun()) {
        break;
      }
      if (type_ == PacketResponderType::HAS_DOWNSTREAM_IN_PIPELINE) {
        if (seqno != pkt.seqno_) {
          std::string msg =
              byte::StringPrint("%s seqno: expected=%ld, received=%ld",
                                details_, pkt.seqno_, seqno);
          e = exceptions::Exception(exceptions::E::kIOException, msg);
          goto TryMarkDownstreamBad;
        }
        total_ack_time_nanos = ack_recv_nano_time - pkt.ack_enqueue_nano_time_;
        int64_t ack_time_nanos =
            total_ack_time_nanos - ack.GetDownstreamAckTimeNanos();
        if (ack_time_nanos < 0) {
          LOG(DEBUG) << "Calculated invalid ack time: " << ack_time_nanos
                     << "ns.";
        }
        LOG(DEBUG) << "check seqno ok, total_ack_time_nanos:"
                   << total_ack_time_nanos << " down_stream_ack_time_nanos:"
                   << ack.GetDownstreamAckTimeNanos();
        auto ack_peer_nanos = ack.GetPerPeerAckTimeNanos();
        per_peer_ack_time_nanos.push_back((uint64_t)ack_time_nanos);
        per_peer_ack_time_nanos.insert(per_peer_ack_time_nanos.end(),
                                       ack_peer_nanos.begin(),
                                       ack_peer_nanos.end());
      }
      last_packet_in_block = pkt.last_packet_in_block_;
    }
  TryMarkDownstreamBad:
    if (e.GetE() == exceptions::E::kIOException) {
      // continue to run even if can not read from mirror
      // notify client of the error
      // and wait for the client to shut down the pipeline
      receiver_->SetMirrorError(true);
      LOG(INFO) << details_ << " " << e.ToString();
    }
    if (IsInterrupted() || IsStopped()) {
      LOG(INFO) << details_ << ": Thread is interrupted.";
      MarkStop();
      continue;
    }
    // FinalizeBlock if last_packet_in_block or status is FREEZE
    if (last_packet_in_block || pkt.ack_status_ == Status::FREEZE) {
      e = FinalizeBlock(start_time);
      if (!e.OK()) {
        goto TryCloseConnection;
      }
    }

    // send packet_ack with pipeline_ack from downstream
    LOG(DEBUG) << "send ack to upstream";
    e = SendAck(&ack, pkt.seqno_, total_ack_time_nanos, pkt.offset_in_block_,
                pkt.ack_status_, per_peer_ack_time_nanos);
    if (!pkt.IsEmpty()) {
      RemoveAckHead();
      LOG(DEBUG) << "pop ack from ack queue";
    }
  TryCloseConnection:
    if (!e.OK()) {
      if (e.GetE() == exceptions::E::kIOException) {
        LOG(WARNING) << "IOException in packet_responder.run(): "
                     << e.ToString();
      }
      if (IsRunning()) {
        if (e.GetE() == exceptions::E::kIOException) {
          receiver_->GetDataNode()->CheckDiskErrorAsync();
        }
        LOG(INFO) << "try to close, detail:" << details_ << " " << e.ToString();
        MarkStop();
        if (!IsInterrupted() &&
            !IsStopped()) {  // failure not caused by interruption
          receiver_->xceiver_->Stop();
        }
      }
    }
  }
  dn->GetDataNodeStreamServer()->DeleteXceiverOperation(
      receiver_->block_->GetBlockPoolID(),
      DataNodeStreamServer::Operation::RespondPacket);
  LOG(INFO) << "packet responder is terminating, detail:" << details_;
}

exceptions::Exception PacketResponder::FinalizeBlock(uint64_t start_time) {
  uint64_t end_time = byte::GetCurrentTimeInNs();
  return receiver_->FinalizeBlock(start_time, end_time);
}

}  // namespace hdfs
}  // namespace chunkserver
}  // namespace bytestore
