// Copyright (c) 2020-present, ByteDance Inc. All rights reserved.

#pragma once
#ifndef ACCUMULATION_HISTOGRAM_H_
#define ACCUMULATION_HISTOGRAM_H_

#include <cstdint>
#include <string>
#include <unordered_map>
#include <vector>

#include "byte/concurrent/mutex.h"
#include "byte/system/timestamp.h"

namespace bytestore {
namespace chunkserver {
namespace hdfs {

class AccumulationHistogram {
 public:
  AccumulationHistogram();

  class Snapshot {
   public:
    Snapshot() : min_(0), max_(0), avg_(0), sum_(0) {}

    Snapshot(const int64_t& min, const int64_t& max, const int64_t& avg,
             const int64_t& sum)
        : min_(min), max_(max), avg_(avg), sum_(sum) {}

    int64_t GetMin() const {
      return min_;
    }

    int64_t GetMax() const {
      return max_;
    }

    int64_t GetAvg() const {
      return avg_;
    }

    int64_t GetSum() const {
      return sum_;
    }

    std::unordered_map<std::string, int64_t> ToUnorderedMap() const;

   private:
    int64_t min_;
    int64_t max_;
    int64_t avg_;
    int64_t sum_;
  };

  class Item {
   public:
    Item(const int64_t& seconds, const int64_t& value)
        : seconds_(seconds), value_(value) {}

    Item(const Item& rhs) {
      seconds_ = rhs.seconds_;
      value_ = rhs.value_;
    }

    Item& operator=(const Item& rhs) {
      seconds_ = rhs.seconds_;
      value_ = rhs.value_;
      return *this;
    }

    void Update(const int64_t& seconds, const int64_t& value);
    bool Load(const int64_t& ts, int64_t* value) const;

    // for test
    void Reset() {
      seconds_ = 0, value_ = 0;
    }

   private:
    mutable byte::Mutex mutex_;
    int64_t seconds_;
    int64_t value_;
  };

  void Update(const int64_t& value);
  Snapshot GetSnapshot() const;
  std::unordered_map<std::string, std::unordered_map<std::string, int64_t>>
  ToUnorderedMap() const;
  // for test
  void Reset();

  static void SetInvalidInterval(const int32_t& invalid_interval) {
    INVALID_INTERVAL = invalid_interval;
  }

 private:
  static int32_t INVALID_INTERVAL;
  std::vector<Item> items_;
};

}  // namespace hdfs
}  // namespace chunkserver
}  // namespace bytestore

#endif
