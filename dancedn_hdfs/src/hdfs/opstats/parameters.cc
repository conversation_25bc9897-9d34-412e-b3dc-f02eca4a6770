// Copyright (c) 2020-present, ByteDance Inc. All rights reserved.

#include "hdfs/opstats/accumulation_histogram.h"
#include "hdfs/opstats/instant_histogram.h"
#include "hdfs/opstats/op_entry.h"
#include "hdfs/opstats/op_stats.h"

namespace bytestore {
namespace chunkserver {
namespace hdfs {

int32_t OpStats::LATENCY_WINDOW_SIZE = 512;
int32_t OpStats::CAPACITY = 6000;
int64_t OpStats::EXPIRE_TIME = 60000;

int32_t InstantHistogram::INVALID_INTERVAL =
    static_cast<int32_t>(OpStats::EXPIRE_TIME / 1000);

int32_t AccumulationHistogram::INVALID_INTERVAL =
    static_cast<int32_t>(OpStats::EXPIRE_TIME / 1000);

int32_t OpEntry::LATENCY_WINDOW_SIZE = OpStats::LATENCY_WINDOW_SIZE;

}  // namespace hdfs
}  // namespace chunkserver
}  // namespace bytestore
