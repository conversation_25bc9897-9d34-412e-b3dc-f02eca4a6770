// Copyright (c) 2020-present, ByteDance Inc. All rights reserved.

#include "hdfs/opstats/op_stats.h"

#include <gtest/gtest.h>

#include <thread>  // NOLINT

#include "byte/byte_log/byte_log_impl.h"

namespace bytestore {
namespace chunkserver {
namespace hdfs {

class OpStatsTests : public ::testing::Test {
 public:
  OpStatsTests() : helper_() {}

  void SetUp() override {
    OpStats::GetInstance().Init();
  }

  static void SetUpTestCase() {}
  static void TearDownTestCase() {
    byte::GetLoggingSystem()->Shutdown();
  }

 private:
  class Helper {
   public:
    Helper() {
      OpStats::SetExpireTime(3000);
      OpStats::SetLatencyWindowSize(5);
      OpStats::SetCapacity(10);
      OpEntry::SetLatencyWindowSize(5);
      AccumulationHistogram::SetInvalidInterval(3);
      InstantHistogram::SetInvalidInterval(3);
    }
  };

  Helper helper_;
};

TEST_F(OpStatsTests, AddBytes) {
  auto& op_stats = OpStats::GetInstance();
  OpKey key1 = OpKey::New("lwj", "psm1", "dn", "job1", 0, "ios", "id1", "ip1",
                          "bp1", "p1", 0, Operation::ReplaceBlock);
  OpKey key2 = OpKey::New("kay", "psm1", "dn", "job1", 0, "ios", "id1", "ip1",
                          "bp1", "p1", 0, Operation::WriteBlock);
  OpKey key3 = OpKey::New("ibi", "psm1", "dn", "job1", 0, "ios", "id1", "ip1",
                          "bp1", "p1", 0, Operation::ReadBlock);
  OpKey key4 = OpKey::New("lwj", "psm1", "dn", "job1", 0, "ios", "id1", "ip1",
                          "bp1", "p1", 0, Operation::TransferBlock);
  OpKey key5 = OpKey::New("kay", "psm1", "dn", "job1", 0, "ios", "id1", "ip1",
                          "bp1", "p1", 0, Operation::TransferReplica);
  OpKey key6 = OpKey::New("ibi", "psm1", "dn", "job1", 0, "ios", "id1", "ip1",
                          "bp1", "p1", 0, Operation::GetReplicaVisibleLength);
  op_stats.AddBytes(key1, 100);
  op_stats.AddBytes(key1, 100);
  op_stats.AddBytes(key2, 200);
  op_stats.AddBytes(key2, 200);
  op_stats.AddBytes(key3, 300);
  op_stats.AddBytes(key3, 300);
  op_stats.AddBytes(key4, 400);
  op_stats.AddBytes(key4, 400);
  op_stats.AddBytes(key5, 500);
  op_stats.AddBytes(key5, 500);
  op_stats.AddBytes(key6, 600);
  op_stats.AddBytes(key6, 600);
  usleep(1000 * 100);
  auto op_stats_view = op_stats.GetView();
  EXPECT_FALSE(op_stats_view.entries.empty());
  EXPECT_EQ(op_stats_view.entries.size(), 6u);
  for (int i = 0; i < 6; ++i) {
    const auto& bytes_entry = op_stats_view.entries.at(i);
    const auto& bytes_snapshot = bytes_entry.GetBytes().GetSnapshot();
    if (bytes_entry.GetKey() == key1) {
      EXPECT_EQ(bytes_snapshot.GetSum(), 200);
    } else if (bytes_entry.GetKey() == key2) {
      EXPECT_EQ(bytes_snapshot.GetSum(), 400);
    } else if (bytes_entry.GetKey() == key3) {
      EXPECT_EQ(bytes_snapshot.GetSum(), 600);
    } else if (bytes_entry.GetKey() == key4) {
      EXPECT_EQ(bytes_snapshot.GetSum(), 800);
    } else if (bytes_entry.GetKey() == key5) {
      EXPECT_EQ(bytes_snapshot.GetSum(), 1000);
    } else if (bytes_entry.GetKey() == key6) {
      EXPECT_EQ(bytes_snapshot.GetSum(), 1200);
    } else {
      EXPECT_FALSE(true);
    }
  }
  usleep(1000 * 1900);
  op_stats.AddBytes(key1, 100);
  op_stats.AddBytes(key1, 100);
  op_stats.AddBytes(key2, 200);
  op_stats.AddBytes(key2, 200);
  op_stats.AddBytes(key3, 300);
  op_stats.AddBytes(key3, 300);
  op_stats.AddBytes(key4, 400);
  op_stats.AddBytes(key4, 400);
  op_stats.AddBytes(key5, 500);
  op_stats.AddBytes(key5, 500);
  op_stats.AddBytes(key6, 600);
  op_stats.AddBytes(key6, 600);
  usleep(1000 * 100);
  op_stats_view = op_stats.GetView();
  EXPECT_EQ(op_stats_view.entries.size(), 6u);
  for (int i = 0; i < 6; ++i) {
    const auto& bytes_entry = op_stats_view.entries.at(i);
    const auto& bytes_snapshot = bytes_entry.GetBytes().GetSnapshot();
    if (bytes_entry.GetKey() == key1) {
      EXPECT_EQ(bytes_snapshot.GetSum(), 400);
    } else if (bytes_entry.GetKey() == key2) {
      EXPECT_EQ(bytes_snapshot.GetSum(), 800);
    } else if (bytes_entry.GetKey() == key3) {
      EXPECT_EQ(bytes_snapshot.GetSum(), 1200);
    } else if (bytes_entry.GetKey() == key4) {
      EXPECT_EQ(bytes_snapshot.GetSum(), 1600);
    } else if (bytes_entry.GetKey() == key5) {
      EXPECT_EQ(bytes_snapshot.GetSum(), 2000);
    } else if (bytes_entry.GetKey() == key6) {
      EXPECT_EQ(bytes_snapshot.GetSum(), 2400);
    } else {
      EXPECT_FALSE(true);
    }
  }
  usleep(1000 * 1900);
  op_stats_view = op_stats.GetView();
  EXPECT_EQ(op_stats_view.entries.size(), 6u);
  for (int i = 0; i < 6; ++i) {
    const auto& bytes_entry = op_stats_view.entries.at(i);
    const auto& bytes_snapshot = bytes_entry.GetBytes().GetSnapshot();
    if (bytes_entry.GetKey() == key1) {
      EXPECT_EQ(bytes_snapshot.GetSum(), 200);
    } else if (bytes_entry.GetKey() == key2) {
      EXPECT_EQ(bytes_snapshot.GetSum(), 400);
    } else if (bytes_entry.GetKey() == key3) {
      EXPECT_EQ(bytes_snapshot.GetSum(), 600);
    } else if (bytes_entry.GetKey() == key4) {
      EXPECT_EQ(bytes_snapshot.GetSum(), 800);
    } else if (bytes_entry.GetKey() == key5) {
      EXPECT_EQ(bytes_snapshot.GetSum(), 1000);
    } else if (bytes_entry.GetKey() == key6) {
      EXPECT_EQ(bytes_snapshot.GetSum(), 1200);
    } else {
      EXPECT_FALSE(true);
    }
  }
}

TEST_F(OpStatsTests, IncrQps) {
  auto& op_stats = OpStats::GetInstance();
  OpKey key1 = OpKey::New("lwj", "psm1", "dn", "job1", 0, "ios", "id1", "ip1",
                          "bp1", "p1", 0, Operation::ReplaceBlock);
  OpKey key2 = OpKey::New("kay", "psm1", "dn", "job1", 0, "ios", "id1", "ip1",
                          "bp1", "p1", 0, Operation::WriteBlock);
  OpKey key3 = OpKey::New("ibi", "psm1", "dn", "job1", 0, "ios", "id1", "ip1",
                          "bp1", "p1", 0, Operation::ReadBlock);
  OpKey key4 = OpKey::New("lwj", "psm1", "dn", "job1", 0, "ios", "id1", "ip1",
                          "bp1", "p1", 0, Operation::TransferBlock);
  OpKey key5 = OpKey::New("kay", "psm1", "dn", "job1", 0, "ios", "id1", "ip1",
                          "bp1", "p1", 0, Operation::TransferReplica);
  OpKey key6 = OpKey::New("ibi", "psm1", "dn", "job1", 0, "ios", "id1", "ip1",
                          "bp1", "p1", 0, Operation::GetReplicaVisibleLength);
  op_stats.IncrQps(key1);
  op_stats.IncrQps(key2);
  op_stats.IncrQps(key2);
  op_stats.IncrQps(key3);
  op_stats.IncrQps(key3);
  op_stats.IncrQps(key3);
  op_stats.IncrQps(key4);
  op_stats.IncrQps(key4);
  op_stats.IncrQps(key4);
  op_stats.IncrQps(key4);
  op_stats.IncrQps(key5);
  op_stats.IncrQps(key5);
  op_stats.IncrQps(key5);
  op_stats.IncrQps(key5);
  op_stats.IncrQps(key5);
  op_stats.IncrQps(key6);
  op_stats.IncrQps(key6);
  op_stats.IncrQps(key6);
  op_stats.IncrQps(key6);
  op_stats.IncrQps(key6);
  op_stats.IncrQps(key6);
  usleep(1000 * 100);
  auto op_stats_view = op_stats.GetView();
  EXPECT_FALSE(op_stats_view.entries.empty());
  EXPECT_EQ(op_stats_view.entries.size(), 6u);
  for (int i = 0; i < 6; ++i) {
    const auto& qps_entry = op_stats_view.entries.at(i);
    const auto& qps_snapshot = qps_entry.GetQps().GetSnapshot();
    if (qps_entry.GetKey() == key1) {
      EXPECT_EQ(qps_snapshot.GetSum(), 1);
    } else if (qps_entry.GetKey() == key2) {
      EXPECT_EQ(qps_snapshot.GetSum(), 2);
    } else if (qps_entry.GetKey() == key3) {
      EXPECT_EQ(qps_snapshot.GetSum(), 3);
    } else if (qps_entry.GetKey() == key4) {
      EXPECT_EQ(qps_snapshot.GetSum(), 4);
    } else if (qps_entry.GetKey() == key5) {
      EXPECT_EQ(qps_snapshot.GetSum(), 5);
    } else if (qps_entry.GetKey() == key6) {
      EXPECT_EQ(qps_snapshot.GetSum(), 6);
    } else {
      EXPECT_FALSE(true);
    }
  }
  usleep(1000 * 1900);
  op_stats.IncrQps(key1);
  op_stats.IncrQps(key1);
  op_stats.IncrQps(key1);
  op_stats.IncrQps(key1);
  op_stats.IncrQps(key1);
  op_stats.IncrQps(key1);
  op_stats.IncrQps(key2);
  op_stats.IncrQps(key2);
  op_stats.IncrQps(key2);
  op_stats.IncrQps(key2);
  op_stats.IncrQps(key2);
  op_stats.IncrQps(key3);
  op_stats.IncrQps(key3);
  op_stats.IncrQps(key3);
  op_stats.IncrQps(key3);
  op_stats.IncrQps(key4);
  op_stats.IncrQps(key4);
  op_stats.IncrQps(key4);
  op_stats.IncrQps(key5);
  op_stats.IncrQps(key5);
  op_stats.IncrQps(key6);
  usleep(1000 * 100);
  op_stats_view = op_stats.GetView();
  EXPECT_EQ(op_stats_view.entries.size(), 6u);
  for (int i = 0; i < 6; ++i) {
    const auto& qps_entry = op_stats_view.entries.at(i);
    const auto& qps_snapshot = qps_entry.GetQps().GetSnapshot();
    if (qps_entry.GetKey() == key1) {
      EXPECT_EQ(qps_snapshot.GetSum(), 7);
    } else if (qps_entry.GetKey() == key2) {
      EXPECT_EQ(qps_snapshot.GetSum(), 7);
    } else if (qps_entry.GetKey() == key3) {
      EXPECT_EQ(qps_snapshot.GetSum(), 7);
    } else if (qps_entry.GetKey() == key4) {
      EXPECT_EQ(qps_snapshot.GetSum(), 7);
    } else if (qps_entry.GetKey() == key5) {
      EXPECT_EQ(qps_snapshot.GetSum(), 7);
    } else if (qps_entry.GetKey() == key6) {
      EXPECT_EQ(qps_snapshot.GetSum(), 7);
    } else {
      EXPECT_FALSE(true);
    }
  }
  usleep(1000 * 1900);
  op_stats_view = op_stats.GetView();
  EXPECT_EQ(op_stats_view.entries.size(), 6u);
  for (int i = 0; i < 6; ++i) {
    const auto& qps_entry = op_stats_view.entries.at(i);
    const auto& qps_snapshot = qps_entry.GetQps().GetSnapshot();
    if (qps_entry.GetKey() == key1) {
      EXPECT_EQ(qps_snapshot.GetSum(), 6);
    } else if (qps_entry.GetKey() == key2) {
      EXPECT_EQ(qps_snapshot.GetSum(), 5);
    } else if (qps_entry.GetKey() == key3) {
      EXPECT_EQ(qps_snapshot.GetSum(), 4);
    } else if (qps_entry.GetKey() == key4) {
      EXPECT_EQ(qps_snapshot.GetSum(), 3);
    } else if (qps_entry.GetKey() == key5) {
      EXPECT_EQ(qps_snapshot.GetSum(), 2);
    } else if (qps_entry.GetKey() == key6) {
      EXPECT_EQ(qps_snapshot.GetSum(), 1);
    } else {
      EXPECT_FALSE(true);
    }
  }
  sleep(2);
  op_stats_view = op_stats.GetView();
  EXPECT_EQ(op_stats_view.entries.size(), 0u);
}

TEST_F(OpStatsTests, SaveLatency) {
  auto& op_stats = OpStats::GetInstance();
  OpKey key1 = OpKey::New("lwj", "psm1", "dn", "job1", 0, "ios", "id1", "ip1",
                          "bp1", "p1", 0, Operation::ReplaceBlock);
  OpKey key2 = OpKey::New("kay", "psm1", "dn", "job1", 0, "ios", "id1", "ip1",
                          "bp1", "p1", 0, Operation::WriteBlock);
  OpKey key3 = OpKey::New("ibi", "psm1", "dn", "job1", 0, "ios", "id1", "ip1",
                          "bp1", "p1", 0, Operation::ReadBlock);
  OpKey key4 = OpKey::New("lwj", "psm1", "dn", "job1", 0, "ios", "id1", "ip1",
                          "bp1", "p1", 0, Operation::TransferBlock);
  OpKey key5 = OpKey::New("kay", "psm1", "dn", "job1", 0, "ios", "id1", "ip1",
                          "bp1", "p1", 0, Operation::TransferReplica);
  OpKey key6 = OpKey::New("ibi", "psm1", "dn", "job1", 0, "ios", "id1", "ip1",
                          "bp1", "p1", 0, Operation::GetReplicaVisibleLength);
  op_stats.SaveLatency(key1, 1000);
  op_stats.SaveLatency(key1, 2000);
  op_stats.SaveLatency(key1, 3000);
  op_stats.SaveLatency(key1, 4000);
  op_stats.SaveLatency(key1, 5000);
  op_stats.SaveLatency(key2, 6000);
  op_stats.SaveLatency(key3, 7000);
  op_stats.SaveLatency(key4, 8000);
  usleep(1000 * 100);
  auto op_stats_view = op_stats.GetView();
  EXPECT_EQ(op_stats_view.entries.size(), 4u);
  for (int i = 0; i < 4; ++i) {
    const auto& latency_entry = op_stats_view.entries.at(i);
    const auto& latency_view = latency_entry.GetLatencyView();
    if (latency_entry.GetKey() == key1) {
      EXPECT_EQ(latency_view.at("min"), 1000);
      EXPECT_EQ(latency_view.at("max"), 5000);
      EXPECT_EQ(latency_view.at("avg"), 3000);
      EXPECT_EQ(latency_view.at("p50"), 3000);
      EXPECT_EQ(latency_view.at("p75"), 4000);
      EXPECT_EQ(latency_view.at("p99"), 5000);
    } else if (latency_entry.GetKey() == key2) {
      EXPECT_EQ(latency_view.at("min"), 6000);
      EXPECT_EQ(latency_view.at("max"), 6000);
      EXPECT_EQ(latency_view.at("avg"), 6000);
    } else if (latency_entry.GetKey() == key3) {
      EXPECT_EQ(latency_view.at("min"), 7000);
      EXPECT_EQ(latency_view.at("max"), 7000);
      EXPECT_EQ(latency_view.at("avg"), 7000);
    } else if (latency_entry.GetKey() == key4) {
      EXPECT_EQ(latency_view.at("min"), 8000);
      EXPECT_EQ(latency_view.at("max"), 8000);
      EXPECT_EQ(latency_view.at("avg"), 8000);
    } else {
      EXPECT_FALSE(true);
    }
  }
  op_stats.SaveLatency(key1, 6000);
  usleep(1000 * 100);
  op_stats_view = op_stats.GetView();
  for (int i = 0; i < 4; ++i) {
    const auto& latency_entry = op_stats_view.entries.at(i);
    const auto& latency_view = latency_entry.GetLatencyView();
    if (latency_entry.GetKey() == key1) {
      EXPECT_EQ(latency_view.at("min"), 2000);
      EXPECT_EQ(latency_view.at("max"), 6000);
      EXPECT_EQ(latency_view.at("avg"), 4000);
      EXPECT_EQ(latency_view.at("p50"), 4000);
      EXPECT_EQ(latency_view.at("p75"), 5000);
      EXPECT_EQ(latency_view.at("p99"), 6000);
    }
  }
  sleep(4);
  op_stats_view = op_stats.GetView();
  EXPECT_EQ(op_stats_view.entries.size(), 0u);
}

TEST_F(OpStatsTests, DISABLED_BGThreadPerf) {
  OpStats::GetInstance().Init(4);
  auto& op_stats = OpStats::GetInstance();
  std::vector<OpKey> keys;
  keys.emplace_back(OpKey::New("lwj", "", "", "", 0, "", "", "", "", "", 0,
                               Operation::ReplaceBlock));
  keys.emplace_back(OpKey::New("kay", "", "", "", 0, "", "", "", "", "", 1,
                               Operation::WriteBlock));
  keys.emplace_back(OpKey::New("ibi", "", "", "", 0, "", "", "", "", "", 2,
                               Operation::ReadBlock));
  keys.emplace_back(OpKey::New("kay", "", "", "", 0, "", "", "", "", "", 3,
                               Operation::WriteBlock));
  keys.emplace_back(OpKey::New("ibi", "", "", "", 0, "", "", "", "", "", 4,
                               Operation::ReadBlock));
  keys.emplace_back(OpKey::New("lwj", "", "", "", 0, "", "", "", "", "", 5,
                               Operation::TransferBlock));
  keys.emplace_back(OpKey::New("kay", "", "", "", 0, "", "", "", "", "", 6,
                               Operation::TransferReplica));
  keys.emplace_back(OpKey::New("ibi", "", "", "", 0, "", "", "", "", "", 7,
                               Operation::GetReplicaVisibleLength));
  std::vector<std::thread> workers;
  for (int tid = 0; tid < 100; tid++) {
    workers.push_back(std::thread([&keys, &op_stats]() {
      for (int i = 0; i < 10000; i++) {
        switch (i % 3) {
          case 0: {
            op_stats.AddBytes(keys[i % 8], i);
            break;
          }
          case 1: {
            op_stats.IncrQps(keys[i % 8]);
            break;
          }
          case 2: {
            op_stats.SaveLatency(keys[i % 8], i);
            break;
          }
        }
      }
    }));
  }
  EXPECT_NE(op_stats.GetQueueSize(), 0);
  std::for_each(workers.begin(), workers.end(), [](std::thread& t) {
    t.join();
  });
  usleep(1000 * 3000);  // expect to handle 1,000,000 requests in 3 seconds
  EXPECT_EQ(op_stats.GetQueueSize(), 0);
  auto op_stats_view = op_stats.GetView();
  EXPECT_EQ(op_stats_view.entries.size(), 8u);
}

}  // namespace hdfs
}  // namespace chunkserver
}  // namespace bytestore
