// Copyright (c) 2020-present, ByteDance Inc. All rights reserved.

#include "hdfs/opstats/op_stats.h"

#include <algorithm>
#include <utility>

#include "hdfs/opstats/accumulation_histogram.h"
#include "hdfs/opstats/instant_histogram.h"
#include "hdfs/opstats/op_entry.h"

namespace bytestore {
namespace chunkserver {
namespace hdfs {

void OpStatsThread::AddBytes(const OpKey& op_key, const int64_t& bytes) {
  std::shared_ptr<OpStatsOper> op(
      new OpStatsOper(OpStatsOper::OpStatsType::kAddBytes, op_key, bytes, 0));
  op_queue_.Push(op);
  Signal();
}

void OpStatsThread::IncrQps(const OpKey& op_key) {
  std::shared_ptr<OpStatsOper> op(
      new OpStatsOper(OpStatsOper::OpStatsType::kIncrQps, op_key, 0, 0));
  op_queue_.Push(op);
  Signal();
}

void OpStatsThread::SaveLatency(const OpKey& op_key, const int64_t& millis) {
  std::shared_ptr<OpStatsOper> op(new OpStatsOper(
      OpStatsOper::OpStatsType::kSaveLatency, op_key, 0, millis));
  op_queue_.Push(op);
  Signal();
}

void OpStatsThread::AppendView(OpStatsView& view) {
  auto entries = op_entries_.GetEntries();
  view.entries.insert(view.entries.end(), entries.begin(), entries.end());
  view.last_evicted_time_ms =
      std::max(view.last_evicted_time_ms, op_entries_.GetLastEvictedTimeMs());
}

void OpStatsThread::Run() {
  while (!IsStopped()) {
    WaitUtil(
        [this]() {
          return !op_queue_.Empty() || IsStopped();
        },
        0);
    while (!op_queue_.Empty()) {
      LOG_EVERY_SECONDS(INFO, 3, 1)
          << "OpStats queue size: " << op_queue_.Size();
      std::shared_ptr<OpStatsOper> oper;
      op_queue_.Pop(&oper);
      if (oper != nullptr) {
        switch (oper->type_) {
          case OpStatsOper::OpStatsType::kAddBytes: {
            op_entries_.GetOrCreate(oper->op_key_).AddBytes(oper->bytes_);
            break;
          }
          case OpStatsOper::OpStatsType::kIncrQps: {
            op_entries_.GetOrCreate(oper->op_key_).IncrQps();
            break;
          }
          case OpStatsOper::OpStatsType::kSaveLatency: {
            op_entries_.GetOrCreate(oper->op_key_).SaveLatency(oper->millis_);
            break;
          }
          default: {
            LOG(ERROR) << "unknown op type";
          }
        }
      }
    }
  }
  // To speed up, remaining requests are not processed when stopping.
}

void OpStats::Init(size_t num_threads) {
  op_threads_.clear();
  for (size_t i = 0; i < num_threads; ++i) {
    std::unique_ptr<OpStatsThread> op_thread(
        new OpStatsThread(CAPACITY, EXPIRE_TIME));
    op_thread->SetName("opstat_th_" + std::to_string(i));
    op_thread->Start();
    op_threads_.emplace_back(std::move(op_thread));
  }
  LOG(INFO) << "Init opstats bg threads success, size: " << op_threads_.size();
}

void OpStats::SetLatencyWindowSize(const int32_t& latency_window_size) {
  LATENCY_WINDOW_SIZE = latency_window_size;
  OpEntry::SetLatencyWindowSize(latency_window_size);
}

void OpStats::SetCapacity(const int32_t& capacity) {
  CAPACITY = capacity;
}

void OpStats::SetExpireTime(const int64_t& expire_time) {
  EXPIRE_TIME = expire_time;
  InstantHistogram::SetInvalidInterval(
      static_cast<int32_t>(expire_time / 1000));
  AccumulationHistogram::SetInvalidInterval(
      static_cast<int32_t>(expire_time / 1000));
}

void OpStats::AddBytes(const OpKey& op_key, const int64_t& bytes) {
  if (op_threads_.size() == 0) return;
  op_threads_[op_key.GetBlockId() % op_threads_.size()]->AddBytes(op_key,
                                                                  bytes);
}

void OpStats::IncrQps(const OpKey& op_key) {
  if (op_threads_.size() == 0) return;
  op_threads_[op_key.GetBlockId() % op_threads_.size()]->IncrQps(op_key);
}

void OpStats::SaveLatency(const OpKey& op_key, const int64_t& millis) {
  if (op_threads_.size() == 0) return;
  op_threads_[op_key.GetBlockId() % op_threads_.size()]->SaveLatency(op_key,
                                                                     millis);
}

OpStatsView OpStats::GetView() {
  OpStatsView view;
  for (const auto& th : op_threads_) {
    th->AppendView(view);
  }
  return view;
}

size_t OpStats::GetQueueSize() {
  size_t size = 0;
  for (const auto& th : op_threads_) {
    size += th->GetQueueSize();
  }
  return size;
}

}  // namespace hdfs
}  // namespace chunkserver
}  // namespace bytestore
