// Copyright (c) 2020-present, ByteDance Inc. All rights reserved.

#include "hdfs/opstats/instant_histogram.h"

#include <gtest/gtest.h>

namespace bytestore {
namespace chunkserver {
namespace hdfs {

class InstantHistogramTests : public ::testing::Test {
 public:
  InstantHistogramTests() : helper_(), instant_histogram_(1000) {}

  void SetUp() override {
    instant_histogram_.Reset();
  }

  void TearDown() override {
    instant_histogram_.Reset();
  }

 private:
  class Helper {
   public:
    Helper() {
      InstantHistogram::SetInvalidInterval(3);
    }
  };
  Helper helper_;

 public:
  InstantHistogram instant_histogram_;
};

TEST_F(InstantHistogramTests, Update) {
  int index = 0;
  while (index < 500) {
    instant_histogram_.Update(10 * index);
    index++;
  }
  sleep(2);
  while (index < 1000) {
    instant_histogram_.Update(10 * index);
    ++index;
  }
  const auto& view = instant_histogram_.ToUnorderedMap();
  const auto& snapshot_view = view.at("value");
  EXPECT_FALSE(snapshot_view.empty());
  EXPECT_EQ(snapshot_view.at("min"), 0);
  EXPECT_EQ(snapshot_view.at("max"), 9990);
  EXPECT_EQ(snapshot_view.at("avg"), 4995);
  sleep(2);
  const auto& view2 = instant_histogram_.ToUnorderedMap();
  const auto& snapshot_view2 = view2.at("value");
  EXPECT_FALSE(snapshot_view.empty());
  EXPECT_EQ(snapshot_view2.at("min"), 5000);
  EXPECT_EQ(snapshot_view2.at("max"), 9990);
  EXPECT_EQ(snapshot_view2.at("avg"), 7495);
  EXPECT_EQ(snapshot_view2.at("p50"), 7500);
  EXPECT_EQ(snapshot_view2.at("p75"), 8750);
  EXPECT_EQ(snapshot_view2.at("p99"), 9950);
  EXPECT_EQ(snapshot_view2.at("p999"), 9990);
  sleep(2);
  const auto& view3 = instant_histogram_.ToUnorderedMap();
  const auto& snapshot_view3 = view3.at("value");
  EXPECT_FALSE(snapshot_view.empty());
  EXPECT_EQ(snapshot_view3.at("min"), 0);
  EXPECT_EQ(snapshot_view3.at("max"), 0);
  EXPECT_EQ(snapshot_view3.at("avg"), 0);
  EXPECT_EQ(snapshot_view3.at("p50"), 0);
  EXPECT_EQ(snapshot_view3.at("p75"), 0);
  EXPECT_EQ(snapshot_view3.at("p99"), 0);
  EXPECT_EQ(snapshot_view3.at("p999"), 0);
}

TEST_F(InstantHistogramTests, GetSnapShot) {
  int index = 0;
  while (index < 500) {
    instant_histogram_.Update(10 * index);
    index++;
  }
  sleep(2);
  while (index < 1000) {
    instant_histogram_.Update(10 * index);
    ++index;
  }
  const auto& snapshot = instant_histogram_.GetSnapShot();
  EXPECT_EQ(snapshot.GetMin(), 0);
  EXPECT_EQ(snapshot.GetMax(), 9990);
  EXPECT_EQ(snapshot.GetAvg(), 4995);
  sleep(2);
  const auto& snapshot2 = instant_histogram_.GetSnapShot();
  EXPECT_EQ(snapshot2.GetMin(), 5000);
  EXPECT_EQ(snapshot2.GetMax(), 9990);
  EXPECT_EQ(snapshot2.GetAvg(), 7495);
  EXPECT_EQ(snapshot2.GetP50(), 7500);
  EXPECT_EQ(snapshot2.GetP75(), 8750);
  EXPECT_EQ(snapshot2.GetP99(), 9950);
  EXPECT_EQ(snapshot2.GetP999(), 9990);
  sleep(2);
  const auto& snapshot3 = instant_histogram_.GetSnapShot();
  EXPECT_EQ(snapshot3.GetMin(), 0);
  EXPECT_EQ(snapshot3.GetMax(), 0);
  EXPECT_EQ(snapshot3.GetAvg(), 0);
  EXPECT_EQ(snapshot3.GetP50(), 0);
  EXPECT_EQ(snapshot3.GetP75(), 0);
  EXPECT_EQ(snapshot3.GetP99(), 0);
  EXPECT_EQ(snapshot3.GetP999(), 0);
}

}  // namespace hdfs
}  // namespace chunkserver
}  // namespace bytestore
