// Copyright (c) 2018-present, ByteDance Inc. All rights reserved.

#pragma once

#include <memory>
#include <vector>

#include "byte/base/atomic.h"
#include "hdfs//datanode_interface.h"
#include "hdfs/storage_type.h"
#include "hdfs/volume_choosing_policy.h"

namespace bytestore {
namespace chunkserver {

class DeviceManagement;

namespace hdfs {

class Store;

// thread unsafe
class RoundRobinVolumeChoosingPolicy : public VolumeChoosingPolicy {
 public:
  RoundRobinVolumeChoosingPolicy(
      Store* storage, std::shared_ptr<DatanodeInterface> dn_interface);

  ~RoundRobinVolumeChoosingPolicy() noexcept override;

  exceptions::Exception ChooseVolume(StorageType match_type,
                                     const std::vector<Disk*>& volumes,
                                     uint64_t block_size,
                                     Disk** volume) override;

 private:
  Store* storage_;
  std::shared_ptr<DatanodeInterface> dn_interface_;
  byte::Atomic<uint32_t> cur_volume_;
};

}  // namespace hdfs
}  // namespace chunkserver
}  // namespace bytestore
