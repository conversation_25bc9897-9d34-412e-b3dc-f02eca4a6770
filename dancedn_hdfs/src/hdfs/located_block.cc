// Copyright (c) 2019-present, ByteDance Inc. All rights reserved.

#include "hdfs/located_block.h"

#include "byte/include/assert.h"
#include "byte/string/format/print.h"
#include "hdfs/datanode_info.h"
#include "hdfs/datanode_info_with_storage.h"
#include "hdfs/datanode_storage_info.h"
#include "hdfs/extended_block.h"
#include "hdfs/util.h"

namespace bytestore {
namespace chunkserver {
namespace hdfs {

std::vector<DatanodeInfo*> LocatedBlock::EMPTY_LOCS;

LocatedBlock::~LocatedBlock() {
  for (auto loc : locs_) {
    delete loc;
  }
  locs_.clear();
  for (auto cloc : cached_locs_) {
    delete cloc;
  }
  cached_locs_.clear();
  delete original_located_block_;
  delete replica_pipeliner_;
  delete b_;
}

LocatedBlock::LocatedBlock(ExtendedBlock* b,
                           const std::vector<DatanodeInfo*>& locs)
    : LocatedBlock(b, locs, -1, false) {}

LocatedBlock::LocatedBlock(ExtendedBlock* b,
                           const std::vector<DatanodeInfo*>& locs,
                           uint64_t start_offset, bool corrupt)
    : LocatedBlock(b, locs, std::vector<std::string>(),
                   std::vector<StorageType>(), start_offset, corrupt,
                   EMPTY_LOCS) {}

LocatedBlock::LocatedBlock(ExtendedBlock* b,
                           const std::vector<DatanodeStorageInfo*>& storages)
    : LocatedBlock(b, storages, -1, false) {}

LocatedBlock::LocatedBlock(ExtendedBlock* b,
                           const std::vector<DatanodeInfo*>& locs,
                           const std::vector<std::string>& storage_ids,
                           const std::vector<StorageType>& storage_types)
    : LocatedBlock(b, locs, storage_ids, storage_types, -1, false, EMPTY_LOCS) {
}
LocatedBlock::LocatedBlock(ExtendedBlock* b,
                           const std::vector<DatanodeStorageInfo*>& storages,
                           uint64_t start_offset, bool corrupt)
    : LocatedBlock(b, DatanodeStorageInfo::ToDatanodeInfos(storages),
                   DatanodeStorageInfo::ToStorageIDs(storages),
                   DatanodeStorageInfo::ToStorageTypes(storages), -1, false,
                   EMPTY_LOCS) {}

LocatedBlock::LocatedBlock(ExtendedBlock* b,
                           const std::vector<DatanodeInfo*>& locs,
                           const std::vector<std::string>& storage_ids,
                           const std::vector<StorageType>& storage_types,
                           uint64_t start_offset, bool corrupt,
                           const std::vector<DatanodeInfo*>& cache_locs)
    : b_(b),
      offset_(start_offset),
      corrupt_(corrupt),
      storage_ids_(storage_ids),
      storage_types_(storage_types),
      cached_locs_(cache_locs),
      replica_pipeliner_(nullptr),
      original_located_block_(nullptr),
      namenode_backend_() {
  for (size_t i = 0; i < locs.size(); ++i) {
    auto di = locs[i];
    auto storage = new DatanodeInfoWithStorage(
        di, storage_ids.size() > i ? storage_ids[i] : std::string(),
        storage_types.size() > i ? storage_types[i] : StorageType::NULLTYPE);
    locs_.emplace_back(storage);
  }
}
exceptions::Exception LocatedBlock::ToProto(
    hadoop::hdfs::LocatedBlockProto* proto) {
  for (auto loc_with_storage : locs_) {
    auto loc = loc_with_storage->GetDatanodeInfo();
    auto loc_proto = proto->add_locs();
    auto e = loc->ToProto(loc_proto);
    if (!e.OK()) return e;

    auto cache_iterator =
        std::find(cached_locs_.begin(), cached_locs_.end(), loc);
    bool loc_is_cached = cache_iterator != cached_locs_.end();
    proto->add_iscached(loc_is_cached);
    if (loc_is_cached) {
      cached_locs_.erase(cache_iterator);
    }
  }
  BYTE_ASSERT(cached_locs_.size() == 0) << "Found additional cached replica "
                                           "locations that are not in the set"
                                           " of storage-backed locations!";
  for (auto storage_type : storage_types_) {
    hadoop::hdfs::StorageTypeProto type_proto;
    auto e = storage_type.ToProto(&type_proto);
    if (!e.OK()) return e;
    proto->add_storagetypes(type_proto);
  }

  for (auto id : storage_ids_) {
    proto->add_storageids(id);
  }

  if (replica_pipeliner_ != nullptr) {
    auto re_pipe_proto = new hadoop::hdfs::DatanodeInfoWithStorageProto();
    auto e = replica_pipeliner_->ToProto(re_pipe_proto);
    if (!e.OK()) {
      delete re_pipe_proto;
      return e;
    }
    proto->set_allocated_replicapipeliner(re_pipe_proto);
  }

  if (original_located_block_ != nullptr) {
    auto original_proto = new hadoop::hdfs::LocatedBlockProto();
    auto e = original_located_block_->ToProto(original_proto);
    if (!e.OK()) {
      delete original_proto;
      return e;
    }
    proto->set_allocated_originallocatedblock(original_proto);
  }
  auto extended_block_proto = new hadoop::hdfs::ExtendedBlockProto();
  auto e = b_->ToProto(extended_block_proto);
  if (!e.OK()) {
    delete extended_block_proto;
    return e;
  }
  proto->set_allocated_b(extended_block_proto);
  // no need to set token
  auto block_token = new hadoop::common::TokenProto();
  block_token->set_identifier("");
  block_token->set_password("");
  block_token->set_kind("");
  block_token->set_service("");
  proto->set_allocated_blocktoken(block_token);
  proto->set_corrupt(corrupt_);
  proto->set_offset(offset_);
  return exceptions::Exception();
}

void LocatedBlock::SetReplicaPipeliner(
    DatanodeInfoWithStorage* replica_pipeliner) {
  replica_pipeliner_ = replica_pipeliner;
}

std::string LocatedBlock::ToString() const {
  std::string pipeliner =
      (replica_pipeliner_ == nullptr) ? "" : replica_pipeliner_->ToString();
  return byte::StringPrint(
      "LocatedBlock{%s; getBlockSize()=%ld; "
      "corrupt=%b; offset=%ld; locs=[%s]; "
      "replica_pipeliner=%s}",
      b_->ToString(), GetBlockSize(), corrupt_, offset_,
      ObjectJoiner<DatanodeInfoWithStorage>(",").Join(locs_), pipeliner);
}

uint64_t LocatedBlock::GetBlockSize() const {
  return b_->GetNumBytes();
}

}  // namespace hdfs
}  // namespace chunkserver
}  // namespace bytestore
