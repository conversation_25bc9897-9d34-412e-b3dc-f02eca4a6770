// Copyright (c) 2018, ByteDance Inc. All rights reserved.

#pragma once

#include <memory>
#include <string>

#include "hdfs/replica_in_pipeline.h"
#include "hdfs/replica_state.h"

namespace bytestore {
namespace chunkserver {
namespace hdfs {

class ReplicaInfo;
class Store;
class ExtendedBlock;

class ReplicaBeingWritten : public ReplicaInPipeline {
 public:
  ReplicaBeingWritten() {}
  ReplicaBeingWritten(const std::string& bpid, uint64_t block_id, uint64_t gs,
                      const std::string& storage_uuid, bool is_transient,
                      bool checksum_enabled, uint16_t disk_id = 0,
                      PlacementStorageType type = PLM_STORAGE_ANY);
  ReplicaBeingWritten(const ExtendedBlock& block,
                      const std::string& storage_uuid, bool is_transient,
                      bool checksum_enabled, uint16_t disk_id = 0,
                      PlacementStorageType type = PLM_STORAGE_ANY);
  ReplicaBeingWritten(const std::string& bpid, uint64_t block_id, uint64_t len,
                      uint64_t gs, const std::string& storage_uuid,
                      bool is_transient, bool checksum_enabled,
                      uint16_t disk_id = 0,
                      PlacementStorageType type = PLM_STORAGE_ANY);
  explicit ReplicaBeingWritten(const ReplicaInPipeline& from);
  explicit ReplicaBeingWritten(std::shared_ptr<ReplicaInPipeline> from);
  ~ReplicaBeingWritten() {}

  ReplicaState GetState() const override {
    return ReplicaState::RBW;
  }

  int64_t GetVisibleLength() const override;

  std::string ToString() const override;

 private:
  bool unlinked_;
};

}  // namespace hdfs
}  // namespace chunkserver
}  // namespace bytestore
