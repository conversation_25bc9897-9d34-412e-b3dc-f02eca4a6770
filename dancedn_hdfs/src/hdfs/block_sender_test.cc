// Copyright (c) 2018-present, ByteDance Inc. All rights reserved.

#include "hdfs/block_sender.h"

#include <memory>

#include "byte/io/local_filesystem.h"
#include "common/media_flags.h"
#include "common/metrics.h"
#include "gflags/gflags.h"
#include "gmock/gmock.h"
#include "gtest/gtest.h"
#include "hdfs/block.h"
#include "hdfs/chunkserver_store.h"
#include "hdfs/constants.h"
#include "hdfs/data_checksum.h"
#include "hdfs/datanode.h"
#include "hdfs/datanode_id.h"
#include "hdfs/datanode_registration.h"
#include "hdfs/datanode_storage.h"
#include "hdfs/exceptions.h"
#include "hdfs/extended_block.h"
#include "hdfs/finalized_replica.h"
#include "hdfs/io/connection.h"
#include "hdfs/io/define.h"
#include "hdfs/io/io_buf.h"
#include "hdfs/mocks.h"
#include "hdfs/replica.h"
#include "hdfs/replica_being_written.h"
#include "hdfs/replica_under_recovery.h"
#include "hdfs/replica_waiting_to_be_recovered.h"
#include "hdfs/storage_report.h"
#include "hdfs/store.h"

#define SYNC_HANDLE_OPER(FUNC)                                                 \
  {                                                                            \
    byte::CountDownLatch latch(1);                                             \
    google::protobuf::Closure* done = google::protobuf::NewCallback(           \
        this, &BlockSenderTests::IOOperDone, &latch);                          \
    oper->SetCallback(done);                                                   \
    oper->request_.set_disk_id(disk_id);                                       \
    dn_interface_->FUNC(&oper->controller_, &oper->request_, &oper->response_, \
                        oper->done_);                                          \
    latch.Wait();                                                              \
    EXPECT_EQ(BYTESTORE_OK, oper->response_.error_code());                     \
  }

DECLARE_string(bytestore_chunkserver_work_dir);
DECLARE_uint32(bytestore_chunkserver_max_num_discs);
DECLARE_bool(bytestore_chunkserver_truncate_chunk_after_freeze);
DECLARE_MEDIA_FLAG_int64(bytestore_chunkserver_reserved_disk_size);
DECLARE_int32(bytestore_chunkserver_log_level);
DECLARE_bool(bytestore_chunkserver_admit_duplicate_uuid_disk);
DECLARE_uint32(bytestore_hdfs_checksum_mode);
DECLARE_string(bytestore_hdfs_default_user);
DECLARE_MEDIA_FLAG_uint32(bytestore_hdfs_slow_io_warning_threshold_ms);

// Test Data
namespace {

static const char test_bp_id[] = "tbpi";
static const uint64_t test_blk_gs0 = 1;
static const uint64_t test_blk_gs1 = 2;
static const uint64_t test_blk_id = 20190828;
static const uint64_t test_blk_len = 4096;
static bytestore::chunkserver::DiskOptions diskOptions;
std::shared_ptr<bytestore::chunkserver::hdfs::ExtendedBlock> test_block(
    new bytestore::chunkserver::hdfs::ExtendedBlock(test_bp_id, test_blk_id,
                                                    test_blk_len,
                                                    test_blk_gs0));

std::shared_ptr<bytestore::chunkserver::hdfs::ExtendedBlock> test_later_block(
    new bytestore::chunkserver::hdfs::ExtendedBlock(test_bp_id, test_blk_id,
                                                    test_blk_len,
                                                    test_blk_gs1));

static const char storage_uuid[] = "suuid";
std::shared_ptr<bytestore::chunkserver::hdfs::FinalizedReplica>
    test_finalized_replica(new bytestore::chunkserver::hdfs::FinalizedReplica(
        test_block.get(), storage_uuid, true, false));

std::shared_ptr<bytestore::chunkserver::hdfs::FinalizedReplica>
    test_later_finalized_replica(
        new bytestore::chunkserver::hdfs::FinalizedReplica(
            test_later_block.get(), storage_uuid, true, false));

std::shared_ptr<bytestore::chunkserver::hdfs::ReplicaWaitingToBeRecovered>
    test_rwr_replica(
        new bytestore::chunkserver::hdfs::ReplicaWaitingToBeRecovered(
            test_block.get(), storage_uuid, false, false));

static const uint64_t recovery_id = 20180902;

static const char ip_addr[] = "127.0.0.1";
static const char hostname[] = "localhost";
static const char uuid[] = "duuid";
static const int xfer_port = 5060;
static const int info_port = 5061;
static const int ipc_port = 5062;
}  // namespace

namespace bytestore {
namespace chunkserver {
namespace hdfs {

class DataNode;
class MockStore;
class BlockSender;
class ExtendedBlock;

exceptions::Exception GenVersionFileForHdfs(DiskIdConfMap disk_id_conf_map);

class BlockSenderTests : public ::testing::Test {
 public:
  BlockSenderTests() {}

  ~BlockSenderTests() {}

  void SetUp() {
    dn_ = new MockDataNode();
    MockDataTransferManager* dtm = new MockDataTransferManager();
    dn_->SetDataTransferManager(dtm);
    mock_storage_ = new MockStore();
    EXPECT_CALL(*mock_storage_, IsVolumeClosed(::testing::A<Disk*>()))
        .WillRepeatedly(::testing::Return(false));
    dn_->SetStorage(mock_storage_);
    MFLAGS_set(bytestore_chunkserver_reserved_disk_size, TYPE_SATA_HDD,
               3ULL * 1000 * 1000 * 1000);
    metrics_internal::InitFastMetrics();
    std::string work_dir = "./BlockSenderTestDir/";
    namespace_id_ = 1004580134;
    cluster_id_ = "CID-9a628355-6954-473b-a66c-d34d7c2b3805";
    create_time_ = 1546064706393;
    build_version_ = "2.6.0";
    software_version_ = "2.6.3";
    bpid_ = "BP-2026362776-*************-1546064706393";

    byte::LocalFileSystem local_fs;
    EXPECT_TRUE(
        local_fs.CreateDir(std::string(work_dir), byte::CreateOptions()).ok());
    EXPECT_TRUE(
        local_fs
            .CreateDir(std::string(work_dir) + "/disk1", byte::CreateOptions())
            .ok());
    EXPECT_TRUE(
        local_fs
            .CreateDir(std::string(work_dir) + "/disk2", byte::CreateOptions())
            .ok());
    disk_id_conf_map_[1] =
        DiskConfig(1, std::string(work_dir) + "/disk1", CDT_SATA_HDD);
    disk_id_conf_map_[2] =
        DiskConfig(2, std::string(work_dir) + "/disk2", CDT_SATA_HDD);
    auto ex = GenVersionFileForHdfs(disk_id_conf_map_);
    EXPECT_TRUE(ex.OK());
    gflags::FlagSaver saver;
    FLAGS_bytestore_chunkserver_work_dir = std::string(work_dir);
    FLAGS_bytestore_chunkserver_truncate_chunk_after_freeze = true;
    FLAGS_bytestore_chunkserver_admit_duplicate_uuid_disk = true;

    cs_env_ = new Env(work_dir);
    cs_config_.DEBUG_ImportConfig(disk_id_conf_map_);
    EnvOptions env_options;
    env_options.cs_config_ = &cs_config_;
    EXPECT_EQ(cs_env_->Init(env_options), BYTESTORE_OK);
    EXPECT_EQ(cs_env_->Start(), BYTESTORE_OK);
    EXPECT_EQ(cs_env_->UpdateCSStatus(CHUNKSERVER_NORMAL), BYTESTORE_OK);
    dn_interface_.reset(new DatanodeInterface(&cs_config_, cs_env_));

    // block id = 1, bpid = BP-2026362776-*************-1546064706393
    ChunkIdMeta chunk_id1(1, 2026362776, 3232237157, 1546064706393);
    std::unique_ptr<ExtendedBlock> block(
        new ExtendedBlock(bpid_, chunk_id1.block_id_));
    CreateChunk(1, block.get());

    static const uint32_t k_buf_size =
        4096;  // Must be equals to multiple of 32
    char* data = new char[k_buf_size];
    std::unique_ptr<char[]> scoped_data(data);
    for (uint32_t i = 0; i < k_buf_size; ++i) {
      data[i] = i % 32 + 'A';
    }

    WriteChunk(1, block.get(), data, k_buf_size, 0);
    char* write_xattr = new char[XATTR_BYTES];
    memset(write_xattr, 0, 32);
    std::unique_ptr<char[]> write_xattr_deleter(write_xattr);
    BlockMeta* meta = reinterpret_cast<BlockMeta*>(write_xattr);
    // gs_ = 1546064706393, finalized
    meta->gs_ = 1546064706393;
    meta->fin_ = 1;
    meta->checksum_enabled_ = 1;
    SetChunkAttr(1, block.get(), write_xattr);
    FreezeChunk(1, block.get(), k_buf_size);

    char* result = new char[XATTR_BYTES];
    GetChunkAttr(1, block.get(), result);
    EXPECT_EQ(std::string(write_xattr, XATTR_BYTES),
              std::string(result, XATTR_BYTES));
    delete[] result;

    ns_info_ =
        new NameSpaceInfo(namespace_id_, cluster_id_, bpid_, create_time_,
                          build_version_, software_version_);
    conn_ = new MockConnection();
    conn_->SetIsClosed(false);
    EXPECT_CALL(*conn_, WriteBuf())
        .WillRepeatedly(::testing::Return(&write_buf_));
    EXPECT_CALL(*conn_, Write(::testing::_))
        .WillRepeatedly([this](bool is_sync) {
          io::IOChunk* chunk = conn_->WriteBuf()->Front();
          sended_.append(reinterpret_cast<char*>(chunk->UsedData()),
                         chunk->UsedLength());
          io::IOChunk::Destroy(conn_->WriteBuf()->Pop());
          return IO_OK;
        });
  }

  void TearDown() {
    delete conn_;
    cs_env_->Stop();
    byte::DeleteOptions delete_options;
    delete_options.recursively_ = true;
    byte::LocalFileSystem local_fs;
    std::string work_dir = "./BlockSenderTestDir/";
    local_fs.DeleteDir(std::string(work_dir), delete_options);
    delete dn_;
    delete cs_env_;
    delete ns_info_;
  }

  static void SetUpTestCase() {}
  static void TearDownTestCase() {
    byte::GetLoggingSystem()->Shutdown();
  }

  void PrepareForSendBlockTest(DataNode* dn) {
    store_ = new ChunkServerStore(&cs_config_, cs_env_);
    auto e = store_->InitStorage(dn_, &disk_id_conf_map_);
    EXPECT_TRUE(e.OK());
    e = store_->AddDirAndBlock(ns_info_);
    EXPECT_TRUE(e.OK());
    dn_->SetStorage(store_);
    delete mock_storage_;
  }

  void CreateChunk(uint32_t disk_id, ExtendedBlock* block) {
    std::unique_ptr<CreateOper> oper(new CreateOper(
        PRIORITY_REAL_TIME, block, 5000000, ChunkType::TYPE_REPLICATED_CHUNK, 0,
        false, PLM_STORAGE_ANY, 0));
    SYNC_HANDLE_OPER(CreateChunk)
  }

  void WriteChunk(uint32_t disk_id, ExtendedBlock* block, char* data,
                  uint32_t length, uint32_t offset) {
    std::unique_ptr<WriteOper> oper(new WriteOper(
        PRIORITY_ELASTIC, block, 5000000, length, offset, data, false));
    SYNC_HANDLE_OPER(WriteChunk)
  }

  void FreezeChunk(uint32_t disk_id, ExtendedBlock* block, uint32_t length) {
    std::unique_ptr<FreezeOper> oper(new FreezeOper(
        PRIORITY_ELASTIC, block, 5000000, length, false, PLM_STORAGE_ANY));
    SYNC_HANDLE_OPER(FreezeChunk)
  }

  void SetChunkAttr(const uint32_t disk_id, ExtendedBlock* block, char* meta) {
    std::unique_ptr<XAttrSetOper> oper(new XAttrSetOper(
        PRIORITY_ELASTIC, block, 5000000, meta, PLM_STORAGE_ANY));
    SYNC_HANDLE_OPER(SetXATTRChunk)
  }

  void GetChunkAttr(const uint32_t disk_id, ExtendedBlock* block,
                    char* result) {
    std::unique_ptr<XAttrGetOper> oper(
        new XAttrGetOper(PRIORITY_ELASTIC, block, 5000000, PLM_STORAGE_ANY));
    SYNC_HANDLE_OPER(GetXATTRChunk)
    oper->response_.xattr().copy(result, XATTR_BYTES);
  }

  void IOOperDone(byte::CountDownLatch* latch) {
    latch->CountDown();
  }

 public:
  MockDataNode* dn_;
  MockStore* mock_storage_;
  ChunkServerStore* store_;
  Env* cs_env_;
  ChunkServerConfig cs_config_;
  NameSpaceInfo* ns_info_;
  DiskIdConfMap disk_id_conf_map_;
  std::unique_ptr<DatanodeInterface> dn_interface_;
  int namespace_id_ = 1004580134;
  std::string cluster_id_ = "CID-9a628355-6954-473b-a66c-d34d7c2b3805";
  uint64_t create_time_ = 1546064706393;
  std::string build_version_ = "2.6.0";
  std::string software_version_ = "2.6.3";
  std::string bpid_ = "BP-2026362776-*************-1546064706393";
  MockConnection* conn_;
  io::IOBuf write_buf_;
  std::string sended_ = "";
};

TEST_F(BlockSenderTests, IllegalArguments) {
  BlockSender* block_sender = nullptr;
  ExtendedBlock* tmp_block = test_block->Clone();
  auto e = BlockSender::CreateBlockSender(
      nullptr, tmp_block, 0, test_blk_len, true, false, dn_, nullptr,
      FLAGS_bytestore_hdfs_default_user, -1, &block_sender);
  EXPECT_EQ(e.GetE(), exceptions::E::kIllegalArgumentException);
  EXPECT_EQ(block_sender, nullptr);
  delete block_sender;
  delete tmp_block;
}

TEST_F(BlockSenderTests, ReplicaNotFound) {
  MockStore* mock_storage = static_cast<MockStore*>(dn_->GetStorage());
  EXPECT_CALL(*mock_storage, GetReplica(test_bp_id, test_blk_id))
      .Times(1)
      .WillOnce(::testing::ReturnNull());

  BlockSender* block_sender = nullptr;
  ExtendedBlock* tmp_block = test_block->Clone();
  auto e = BlockSender::CreateBlockSender(
      nullptr, tmp_block, 0, test_blk_len, false, false, dn_, nullptr,
      FLAGS_bytestore_hdfs_default_user, -1, &block_sender);
  EXPECT_EQ(e.GetE(), exceptions::E::kReplicaNotFoundException);
  EXPECT_EQ(block_sender, nullptr);
  delete block_sender;
  delete tmp_block;
}

TEST_F(BlockSenderTests, DiskNull) {
  MockStore* mock_storage = static_cast<MockStore*>(dn_->GetStorage());
  EXPECT_CALL(*mock_storage, GetReplica(test_bp_id, test_blk_id))
      .Times(1)
      .WillOnce(::testing::Return(test_finalized_replica));
  EXPECT_CALL(*mock_storage, GetDisk(0))
      .Times(1)
      .WillOnce(::testing::ReturnNull());

  BlockSender* block_sender = nullptr;
  ExtendedBlock* tmp_block = test_block->Clone();
  auto e = BlockSender::CreateBlockSender(
      nullptr, tmp_block, 0, test_blk_len, false, false, dn_, nullptr,
      FLAGS_bytestore_hdfs_default_user, -1, &block_sender);
  EXPECT_EQ(e.GetE(), exceptions::E::kIOException);
  EXPECT_EQ(block_sender, nullptr);
  delete block_sender;
  delete tmp_block;
}

TEST_F(BlockSenderTests, ConnNullAndClose) {
  ExtendedBlock* block = new ExtendedBlock(bpid_, 1, 4096, 1546064706393);
  PrepareForSendBlockTest(dn_);

  BlockSender* block_sender = nullptr;
  auto e = BlockSender::CreateBlockSender(
      nullptr, block, 0, test_blk_len, true, true, dn_, nullptr,
      FLAGS_bytestore_hdfs_default_user, -1, &block_sender);
  EXPECT_EQ(e.GetE(), exceptions::E::kNoException);
  ASSERT_NE(block_sender, nullptr);
  auto op_key = OpKey::New(block->GetBlockPoolID(), block->GetBlockID(),
                           Operation::ReadBlock);
  block_sender->SetOpKey(op_key);

  uint64_t readcount;
  e = block_sender->SendBlock(nullptr, &readcount);
  EXPECT_EQ(e.GetE(), exceptions::E::kIOException);

  conn_->SetIsClosed(true);
  e = block_sender->SendBlock(conn_, &readcount);
  EXPECT_EQ(e.GetE(), exceptions::E::kIOException);
  delete block_sender;
  delete block;
}

TEST_F(BlockSenderTests, WaitForRwbLengthFailed) {
  std::shared_ptr<MockRbwReplica> tmp_rbw_replica =
      std::make_shared<MockRbwReplica>(test_bp_id, test_blk_id, test_blk_gs0,
                                       storage_uuid, true, false);
  MockStore* mock_storage = static_cast<MockStore*>(dn_->GetStorage());
  EXPECT_CALL(*mock_storage, GetReplica(test_bp_id, test_blk_id))
      .Times(1)
      .WillOnce(::testing::Return(tmp_rbw_replica));
  EXPECT_CALL(*mock_storage, GetDisk(0))
      .Times(1)
      .WillOnce(::testing::Return(cs_env_->GetDisk(1)));
  EXPECT_CALL(*mock_storage, GetMediaThrottler(::testing::_))
      .WillOnce(::testing::ReturnNull());
  EXPECT_CALL(*tmp_rbw_replica, GetBytesOnDisk())
      .Times(31)
      .WillRepeatedly(::testing::Return(0));

  BlockSender* block_sender = nullptr;
  ExtendedBlock* tmp_block = test_block->Clone();
  auto e = BlockSender::CreateBlockSender(
      nullptr, tmp_block, 0, test_blk_len, false, false, dn_, nullptr,
      FLAGS_bytestore_hdfs_default_user, -1, &block_sender);
  EXPECT_EQ(e.GetE(), exceptions::E::kIOException);
  EXPECT_NE(e.GetMessage().find("bytes, but only"), std::string::npos);
  delete block_sender;
  delete tmp_block;
  // delete disk;
}

TEST_F(BlockSenderTests, WaitForRwbLengthSucceed) {
  std::shared_ptr<MockRbwReplica> tmp_rbw_replica =
      std::make_shared<MockRbwReplica>(test_bp_id, test_blk_id, test_blk_gs0,
                                       storage_uuid, true, false);
  MockStore* mock_storage = static_cast<MockStore*>(dn_->GetStorage());
  EXPECT_CALL(*mock_storage, GetReplica(test_bp_id, test_blk_id))
      .Times(1)
      .WillOnce(::testing::Return(tmp_rbw_replica));
  EXPECT_CALL(*mock_storage, GetDisk(0))
      .Times(1)
      .WillOnce(::testing::Return(cs_env_->GetDisk(1)));
  EXPECT_CALL(*mock_storage, GetMediaThrottler(::testing::_))
      .WillOnce(::testing::ReturnNull());
  EXPECT_CALL(*tmp_rbw_replica, GetBytesOnDisk())
      .Times(4)
      .WillOnce(::testing::Return(test_blk_len / 3))
      .WillOnce(::testing::Return(test_blk_len / 2))
      .WillOnce(::testing::Return(test_blk_len))
      .WillOnce(::testing::Return(test_blk_len));
  EXPECT_CALL(*tmp_rbw_replica, GetDiskDataLen())
      .Times(1)
      .WillOnce(::testing::Return(test_blk_len));

  BlockSender* block_sender = nullptr;
  ExtendedBlock* tmp_block = test_block->Clone();
  auto e = BlockSender::CreateBlockSender(
      nullptr, tmp_block, 0, test_blk_len, false, false, dn_, nullptr,
      FLAGS_bytestore_hdfs_default_user, -1, &block_sender);
  EXPECT_EQ(e.GetE(), exceptions::E::kNoException);
  delete block_sender;
  delete tmp_block;
}

TEST_F(BlockSenderTests, ReplicaGS_LT_BlockGS) {
  MockStore* mock_storage = static_cast<MockStore*>(dn_->GetStorage());
  EXPECT_CALL(*mock_storage, GetReplica(test_bp_id, test_blk_id))
      .Times(1)
      .WillOnce(::testing::Return(test_finalized_replica));
  EXPECT_CALL(*mock_storage, GetDisk(0))
      .Times(1)
      .WillOnce(::testing::Return(cs_env_->GetDisk(1)));
  EXPECT_CALL(*mock_storage, GetMediaThrottler(::testing::_))
      .WillOnce(::testing::ReturnNull());
  BlockSender* block_sender = nullptr;
  ExtendedBlock* tmp_block = test_later_block->Clone();
  auto e = BlockSender::CreateBlockSender(
      nullptr, tmp_block, 0, test_blk_len, false, false, dn_, nullptr,
      FLAGS_bytestore_hdfs_default_user, -1, &block_sender);
  EXPECT_EQ(e.GetE(), exceptions::E::kIOException);
  EXPECT_NE(e.GetMessage().find("Replica genstamp < block genstamp"),
            std::string::npos);
  EXPECT_EQ(block_sender, nullptr);
  delete block_sender;
  delete tmp_block;
}

TEST_F(BlockSenderTests, ReplicaGS_GT_BlockGS) {
  MockStore* mock_storage = static_cast<MockStore*>(dn_->GetStorage());
  EXPECT_CALL(*mock_storage, GetReplica(test_bp_id, test_blk_id))
      .Times(1)
      .WillOnce(::testing::Return(test_later_finalized_replica));
  EXPECT_CALL(*mock_storage, GetDisk(0))
      .Times(1)
      .WillOnce(::testing::Return(cs_env_->GetDisk(1)));
  EXPECT_CALL(*mock_storage, GetMediaThrottler(::testing::_))
      .WillOnce(::testing::ReturnNull());
  BlockSender* block_sender = nullptr;
  ExtendedBlock* test_fblock = test_block->Clone();
  ASSERT_EQ(test_fblock->GetGS(), test_blk_gs0);
  auto e = BlockSender::CreateBlockSender(
      nullptr, test_fblock, 0, test_blk_len, false, false, dn_, nullptr,
      FLAGS_bytestore_hdfs_default_user, -1, &block_sender);
  EXPECT_EQ(e.GetE(), exceptions::E::kNoException);
  EXPECT_GT(test_fblock->GetGS(), test_blk_gs0);
  EXPECT_EQ(test_fblock->GetGS(), test_blk_gs1);
  EXPECT_NE(block_sender, nullptr);
  delete block_sender;
  delete test_fblock;
}

TEST_F(BlockSenderTests, InvisibleReplica) {
  std::shared_ptr<ReplicaUnderRecovery> test_rur_replica;
  ReplicaUnderRecovery::New(test_rwr_replica, recovery_id, &test_rur_replica);
  MockStore* mock_storage = static_cast<MockStore*>(dn_->GetStorage());
  EXPECT_CALL(*mock_storage, GetReplica(test_bp_id, test_blk_id))
      .Times(2)
      .WillOnce(::testing::Return(test_rwr_replica))
      .WillOnce(::testing::Return(test_rur_replica));
  EXPECT_CALL(*mock_storage, GetDisk(0))
      .Times(2)
      .WillOnce(::testing::Return(cs_env_->GetDisk(1)))
      .WillOnce(::testing::Return(cs_env_->GetDisk(1)));
  EXPECT_CALL(*mock_storage, GetMediaThrottler(::testing::_))
      .WillRepeatedly(::testing::ReturnNull());
  ASSERT_EQ(test_rwr_replica->GetVisibleLength(), -1);
  ASSERT_EQ(test_rur_replica->GetVisibleLength(), -1);

  BlockSender* block_sender = nullptr;
  ExtendedBlock* tmp_block = test_block->Clone();
  auto e = BlockSender::CreateBlockSender(
      nullptr, tmp_block, 0, test_blk_len, false, false, dn_, nullptr,
      FLAGS_bytestore_hdfs_default_user, -1, &block_sender);
  EXPECT_EQ(e.GetE(), exceptions::E::kIOException);
  EXPECT_NE(e.GetMessage().find("Replica is not readable"), std::string::npos);
  EXPECT_NE(e.GetMessage().find(test_rwr_replica->ToString()),
            std::string::npos);
  EXPECT_EQ(block_sender, nullptr);
  delete tmp_block;

  tmp_block = test_block->Clone();
  e = BlockSender::CreateBlockSender(
      nullptr, tmp_block, 0, test_blk_len, false, false, dn_, nullptr,
      FLAGS_bytestore_hdfs_default_user, -1, &block_sender);
  EXPECT_EQ(e.GetE(), exceptions::E::kIOException);
  EXPECT_NE(e.GetMessage().find("Replica is not readable"), std::string::npos);
  EXPECT_NE(e.GetMessage().find(test_rur_replica->ToString()),
            std::string::npos);
  EXPECT_EQ(block_sender, nullptr);

  delete block_sender;
  delete tmp_block;
}

TEST_F(BlockSenderTests, IllegalOffsetOrLength) {
  auto dn_id = std::make_shared<DatanodeID>(ip_addr, hostname, uuid, xfer_port,
                                            info_port, ipc_port, 0);
  auto dn_reg = std::make_shared<DatanodeRegistration>(
      dn_id,
      new bytestore::chunkserver::hdfs::StorageInfo(
          bytestore::chunkserver::hdfs::NodeType::DATA_NODE),
      bytestore::chunkserver::hdfs::DATANODE_SOFTWARE_VERSION);
  EXPECT_CALL(*dn_, GetDNRegistrationForBP(test_bp_id, ::testing::_))
      .WillRepeatedly(
          ::testing::DoAll(::testing::SetArgPointee<1>(dn_reg),
                           ::testing::Return(exceptions::Exception())));
  MockStore* mock_storage = static_cast<MockStore*>(dn_->GetStorage());
  EXPECT_CALL(*mock_storage, GetReplica(test_bp_id, test_blk_id))
      .Times(1)
      .WillOnce(::testing::Return(test_finalized_replica));
  EXPECT_CALL(*mock_storage, GetDisk(0))
      .Times(1)
      .WillOnce(::testing::Return(cs_env_->GetDisk(1)));
  EXPECT_CALL(*mock_storage, GetMediaThrottler(::testing::_))
      .WillOnce(::testing::ReturnNull());
  BlockSender* block_sender = nullptr;
  ExtendedBlock* tmp_block = test_block->Clone();
  auto e = BlockSender::CreateBlockSender(
      nullptr, tmp_block, test_blk_len / 2, test_blk_len, false, false, dn_,
      nullptr, FLAGS_bytestore_hdfs_default_user, -1, &block_sender);
  EXPECT_EQ(e.GetE(), exceptions::E::kIOException);
  EXPECT_NE(e.GetMessage().find("don't match block"), std::string::npos);
  delete block_sender;
  delete tmp_block;
}

TEST_F(BlockSenderTests, OffsetAlignment) {
  ExtendedBlock* tmp_block = test_block->Clone();
  MockStore* mock_storage = static_cast<MockStore*>(dn_->GetStorage());
  EXPECT_CALL(*mock_storage, GetReplica(test_bp_id, test_blk_id))
      .Times(1)
      .WillOnce(::testing::Return(test_finalized_replica));
  EXPECT_CALL(*mock_storage, GetDisk(0))
      .Times(1)
      .WillOnce(::testing::Return(cs_env_->GetDisk(1)));
  EXPECT_CALL(*mock_storage, GetMediaThrottler(::testing::_))
      .WillOnce(::testing::ReturnNull());
  BlockSender* block_sender = nullptr;
  auto e = BlockSender::CreateBlockSender(
      nullptr, tmp_block, 256, test_blk_len / 2, true, true, dn_, nullptr,
      FLAGS_bytestore_hdfs_default_user, -1, &block_sender);
  EXPECT_EQ(e.GetE(), exceptions::E::kNoException);
  EXPECT_EQ(block_sender->GetOffset(), 0);
  delete block_sender;
  delete tmp_block;
}

TEST_F(BlockSenderTests, SendBlock) {
  FLAGS_bytestore_hdfs_checksum_mode = 0;
  ExtendedBlock* block = new ExtendedBlock(bpid_, 1, 4096, 1546064706393);
  PrepareForSendBlockTest(dn_);
  BlockSender* block_sender = nullptr;
  auto e = BlockSender::CreateBlockSender(
      nullptr, block, 0, test_blk_len, false, true, dn_, nullptr,
      FLAGS_bytestore_hdfs_default_user, -1, &block_sender);
  EXPECT_EQ(e.GetE(), exceptions::E::kNoException);
  ASSERT_NE(block_sender, nullptr);
  auto op_key = OpKey::New(block->GetBlockPoolID(), block->GetBlockID(),
                           Operation::ReadBlock);
  block_sender->SetOpKey(op_key);

  uint64_t readcount;
  e = block_sender->SendBlock(conn_, &readcount);
  EXPECT_EQ(e.GetE(), exceptions::E::kNoException);
  EXPECT_EQ(readcount, 4096);
  ASSERT_EQ(sended_.size(), 4158);  // 4158 = 31 * 2 + 4096
  EXPECT_EQ(sended_.substr(31, 4), "ABCD");
  delete block_sender;
  delete block;
}

TEST_F(BlockSenderTests, InvalidChecksum) {
  ExtendedBlock* block = new ExtendedBlock(bpid_, 1, 4096, 1546064706393);
  PrepareForSendBlockTest(dn_);

  DataChecksum* checksum =
      DataChecksum::NewDataChecksum(DataChecksum::TYPE_CRC32C, 51200000);
  auto e = store_->WriteBlockMetaHeader(block, checksum, StorageType::DISK,
                                        PLM_STORAGE_ANY);
  EXPECT_TRUE(e.OK());

  BlockSender* block_sender = nullptr;
  e = BlockSender::CreateBlockSender(
      nullptr, block, 0, test_blk_len, true, true, dn_, nullptr,
      FLAGS_bytestore_hdfs_default_user, -1, &block_sender);
  EXPECT_EQ(e.GetE(), exceptions::E::kIOException);
  EXPECT_EQ(block_sender, nullptr);
  delete checksum;
  delete block;
}

TEST_F(BlockSenderTests, ReadChecksumFailed) {
  ExtendedBlock* block = new ExtendedBlock(bpid_, 1, 4096, 1546064706393);
  PrepareForSendBlockTest(dn_);

  // only create meta file, not write to it
  DataChecksum* checksum =
      DataChecksum::NewDataChecksum(DataChecksum::TYPE_CRC32C, 512);
  auto e = store_->WriteBlockMetaHeader(block, checksum, StorageType::DISK,
                                        PLM_STORAGE_ANY);
  EXPECT_TRUE(e.OK());

  BlockSender* block_sender = nullptr;
  e = BlockSender::CreateBlockSender(
      nullptr, block, 0, test_blk_len, true, true, dn_, nullptr,
      FLAGS_bytestore_hdfs_default_user, -1, &block_sender);
  EXPECT_EQ(e.GetE(), exceptions::E::kNoException);
  ASSERT_NE(block_sender, nullptr);
  auto op_key = OpKey::New(block->GetBlockPoolID(), block->GetBlockID(),
                           Operation::ReadBlock);
  block_sender->SetOpKey(op_key);

  uint64_t readcount;
  e = block_sender->SendBlock(conn_, &readcount);
  EXPECT_EQ(e.GetE(), exceptions::E::kIOException);
  delete checksum;
  delete block_sender;
  delete block;
}

TEST_F(BlockSenderTests, ReadBlockFailed) {
  EXPECT_CALL(*dn_, ReportBadBlock(::testing::_, ::testing::_, ::testing::_))
      .Times(1)
      .WillOnce(::testing::Return(exceptions::Exception()));
  ExtendedBlock* block = new ExtendedBlock(bpid_, 1, 4096, 1546064706393);
  PrepareForSendBlockTest(dn_);
  byte::LocalFileSystem local_fs;
  std::string file_name1 =
      "./BlockSenderTestDir/disk1/data/825/"
      "1.2026362776.3232237157_4171447129.103@1";
  uint64_t file_size = 0;
  EXPECT_TRUE(local_fs.GetFileSize(file_name1, &file_size).ok());
  byte::DeleteOptions delete_options;
  local_fs.DeleteFile(file_name1, delete_options);
  EXPECT_TRUE(local_fs.GetFileSize(file_name1, &file_size).IsNotFound());

  BlockSender* block_sender = nullptr;
  auto e = BlockSender::CreateBlockSender(
      nullptr, block, 0, test_blk_len, true, true, dn_, nullptr,
      FLAGS_bytestore_hdfs_default_user, -1, &block_sender);
  EXPECT_EQ(e.GetE(), exceptions::E::kNoException);
  ASSERT_NE(block_sender, nullptr);
  auto op_key = OpKey::New(block->GetBlockPoolID(), block->GetBlockID(),
                           Operation::ReadBlock);
  block_sender->SetOpKey(op_key);

  uint64_t readcount;
  e = block_sender->SendBlock(conn_, &readcount);
  EXPECT_EQ(e.GetE(), exceptions::E::kIOException);
  delete block_sender;
  delete block;
}

TEST_F(BlockSenderTests, VerifyChecksumFailed) {
  ExtendedBlock* block = new ExtendedBlock(bpid_, 1, 4096, 1546064706393);
  PrepareForSendBlockTest(dn_);

  // create meta file, use incorrect data
  static const uint32_t k_buf_size = 4096;
  char* data = new char[k_buf_size];
  std::unique_ptr<char[]> scoped_data(data);
  for (uint32_t i = 0; i < k_buf_size; ++i) {
    data[i] = i % 31 + 'A';
  }
  DataChecksum* checksum =
      DataChecksum::NewDataChecksum(DataChecksum::TYPE_CRC32C, 512);
  io::IOChunk* data_chunk = new io::IOChunk(k_buf_size);
  io::IOChunk* checksum_chunk =
      new io::IOChunk(checksum->GetChecksumSize(k_buf_size));
  data_chunk->WriteBytes(reinterpret_cast<uint8_t*>(data), k_buf_size);
  auto e = checksum->CalculateChecksum(data_chunk, checksum_chunk);
  EXPECT_TRUE(e.OK());
  e = store_->WriteBlockMetaHeader(block, checksum, StorageType::DISK,
                                   PLM_STORAGE_ANY);
  EXPECT_TRUE(e.OK());
  e = store_->WriteChecksum(block, checksum_chunk, 0, checksum_chunk->Length(),
                            false);
  EXPECT_TRUE(e.OK());

  BlockSender* block_sender = nullptr;
  e = BlockSender::CreateBlockSender(
      nullptr, block, 0, test_blk_len, true, true, dn_, nullptr,
      FLAGS_bytestore_hdfs_default_user, -1, &block_sender);
  EXPECT_EQ(e.GetE(), exceptions::E::kNoException);
  ASSERT_NE(block_sender, nullptr);
  auto op_key = OpKey::New(block->GetBlockPoolID(), block->GetBlockID(),
                           Operation::ReadBlock);
  block_sender->SetOpKey(op_key);

  uint64_t readcount;
  e = block_sender->SendBlock(conn_, &readcount);
  EXPECT_EQ(e.GetE(), exceptions::E::kChecksumException);
  io::IOChunk::Destroy(data_chunk);
  io::IOChunk::Destroy(checksum_chunk);
  delete checksum;
  delete block_sender;
  delete block;
}

TEST_F(BlockSenderTests, ChecksumFinalized) {
  ExtendedBlock* block = new ExtendedBlock(bpid_, 1, 4096, 1546064706393);
  PrepareForSendBlockTest(dn_);

  // create meta file
  static const uint32_t k_buf_size = 4096;
  char* data = new char[k_buf_size];
  std::unique_ptr<char[]> scoped_data(data);
  for (uint32_t i = 0; i < k_buf_size; ++i) {
    data[i] = i % 32 + 'A';
  }
  DataChecksum* checksum =
      DataChecksum::NewDataChecksum(DataChecksum::TYPE_CRC32C, 512);
  io::IOChunk* data_chunk = new io::IOChunk(k_buf_size);
  io::IOChunk* checksum_chunk =
      new io::IOChunk(checksum->GetChecksumSize(k_buf_size));
  data_chunk->WriteBytes(reinterpret_cast<uint8_t*>(data), k_buf_size);
  auto e = checksum->CalculateChecksum(data_chunk, checksum_chunk);
  EXPECT_TRUE(e.OK());
  e = store_->WriteBlockMetaHeader(block, checksum, StorageType::DISK,
                                   PLM_STORAGE_ANY);
  EXPECT_TRUE(e.OK());
  e = store_->WriteChecksum(block, checksum_chunk, 0, checksum_chunk->Length(),
                            false);
  EXPECT_TRUE(e.OK());

  BlockSender* block_sender = nullptr;
  e = BlockSender::CreateBlockSender(
      nullptr, block, 0, test_blk_len, true, true, dn_, nullptr,
      FLAGS_bytestore_hdfs_default_user, -1, &block_sender);
  EXPECT_EQ(e.GetE(), exceptions::E::kNoException);
  ASSERT_NE(block_sender, nullptr);
  auto op_key = OpKey::New(block->GetBlockPoolID(), block->GetBlockID(),
                           Operation::ReadBlock);
  block_sender->SetOpKey(op_key);

  uint64_t readcount;
  e = block_sender->SendBlock(conn_, &readcount);
  EXPECT_EQ(e.GetE(), exceptions::E::kNoException);
  EXPECT_EQ(readcount, 4128);
  ASSERT_EQ(sended_.size(), 4190);  // 4190 = 31 * 2 + 32 + 4096
  EXPECT_EQ(0xda85273U, be32toh(*reinterpret_cast<const uint32_t*>(
                            sended_.substr(31, 4).data())));
  EXPECT_EQ(sended_.substr(63, 4), "ABCD");
  io::IOChunk::Destroy(data_chunk);
  io::IOChunk::Destroy(checksum_chunk);
  delete checksum;
  delete block_sender;
  delete block;
}

TEST_F(BlockSenderTests, ChecksumRBW) {
  FLAGS_bytestore_hdfs_checksum_mode = 2;
  PrepareForSendBlockTest(dn_);
  ExtendedBlock* block = new ExtendedBlock(bpid_, 2, 800, 1546064706393);
  std::shared_ptr<ReplicaInPipeline> replica;
  auto e = store_->CreateRbw(StorageType::DISK, block, false, &replica);
  EXPECT_TRUE(e.OK());

  // create and write meta file
  static const uint32_t k_buf_size = 800;
  char* data = new char[k_buf_size];
  std::unique_ptr<char[]> scoped_data(data);
  for (uint32_t i = 0; i < k_buf_size; ++i) {
    data[i] = i % 32 + 'A';
  }
  DataChecksum* checksum =
      DataChecksum::NewDataChecksum(DataChecksum::TYPE_CRC32C, 512);
  io::IOChunk* data_chunk = new io::IOChunk(k_buf_size);
  io::IOChunk* checksum_chunk =
      new io::IOChunk(checksum->GetChecksumSize(k_buf_size));
  data_chunk->WriteBytes(reinterpret_cast<uint8_t*>(data), k_buf_size);
  e = store_->WriteBlock(block, data_chunk, 0, k_buf_size, false);
  EXPECT_TRUE(e.OK());
  replica->SetDiskDataLen(k_buf_size);
  e = checksum->CalculateChecksum(data_chunk, checksum_chunk);
  EXPECT_TRUE(e.OK());
  e = store_->WriteBlockMetaHeader(block, checksum, StorageType::DISK,
                                   PLM_STORAGE_ANY);
  EXPECT_TRUE(e.OK());
  e = store_->WriteChecksum(block, checksum_chunk, 0, checksum_chunk->Length(),
                            false);
  EXPECT_TRUE(e.OK());

  BlockSender* block_sender = nullptr;
  e = BlockSender::CreateBlockSender(nullptr, block, 200, 500, true, true, dn_,
                                     nullptr, FLAGS_bytestore_hdfs_default_user,
                                     -1,
                                     &block_sender);  // read 200-700
  EXPECT_EQ(e.GetE(), exceptions::E::kNoException);
  ASSERT_NE(block_sender, nullptr);
  auto op_key = OpKey::New(block->GetBlockPoolID(), block->GetBlockID(),
                           Operation::ReadBlock);
  block_sender->SetOpKey(op_key);

  uint64_t readcount;
  e = block_sender->SendBlock(conn_, &readcount);
  EXPECT_EQ(e.GetE(), exceptions::E::kNoException);
  EXPECT_EQ(readcount, 808);
  ASSERT_EQ(sended_.size(), 870);  // 31 * 2 + 808
  EXPECT_EQ(0xda85273U,            // crc32c of data[:512]
            be32toh(*reinterpret_cast<const uint32_t*>(
                sended_.substr(31, 4).data())));
  EXPECT_EQ(0x316e4021U,  // crc32c of data[512:]
            be32toh(*reinterpret_cast<const uint32_t*>(
                sended_.substr(35, 4).data())));
  EXPECT_EQ(sended_.substr(39, 4), "ABCD");

  // Simulate received 50B after create block sender
  replica->SetDiskDataLen(k_buf_size - 50);
  delete block_sender;
  e = BlockSender::CreateBlockSender(nullptr, block, 200, 500, true, true, dn_,
                                     nullptr, FLAGS_bytestore_hdfs_default_user,
                                     -1,
                                     &block_sender);  // read 200-700
  EXPECT_EQ(e.GetE(), exceptions::E::kNoException);
  ASSERT_NE(block_sender, nullptr);
  block_sender->SetOpKey(op_key);

  sended_ = "";
  e = block_sender->SendBlock(conn_, &readcount);
  EXPECT_EQ(e.GetE(), exceptions::E::kNoException);
  EXPECT_EQ(readcount, 758);
  ASSERT_EQ(sended_.size(), 820);  // 31 * 2 + 758
  EXPECT_EQ(0xda85273U,            // crc32c of data[:512]
            be32toh(*reinterpret_cast<const uint32_t*>(
                sended_.substr(31, 4).data())));
  EXPECT_EQ(0xdd325b3eU,  // crc32c of data[512:750]
            be32toh(*reinterpret_cast<const uint32_t*>(
                sended_.substr(35, 4).data())));
  EXPECT_EQ(sended_.substr(39, 4), "ABCD");
  EXPECT_EQ(sended_.substr(39 + 512, 4), "ABCD");

  io::IOChunk::Destroy(data_chunk);
  io::IOChunk::Destroy(checksum_chunk);
  delete checksum;
  delete block_sender;
  delete block;
}

TEST_F(BlockSenderTests, GenerateChecksumFinalized) {
  byte::SetMinLogLevel(byte::LOG_LEVEL_DEBUG);
  FLAGS_bytestore_hdfs_checksum_mode = 1;
  ExtendedBlock* block = new ExtendedBlock(bpid_, 1, 4096, 1546064706393);
  PrepareForSendBlockTest(dn_);

  // Do not create checksum file, generate crc32c from data
  BlockSender* block_sender = nullptr;
  auto e = BlockSender::CreateBlockSender(
      nullptr, block, 0, test_blk_len, true, true, dn_, nullptr,
      FLAGS_bytestore_hdfs_default_user, -1, &block_sender);
  EXPECT_EQ(e.GetE(), exceptions::E::kNoException);
  ASSERT_NE(block_sender, nullptr);
  auto op_key = OpKey::New(block->GetBlockPoolID(), block->GetBlockID(),
                           Operation::ReadBlock);
  block_sender->SetOpKey(op_key);

  uint64_t readcount;
  e = block_sender->SendBlock(conn_, &readcount);
  EXPECT_EQ(e.GetE(), exceptions::E::kNoException);
  EXPECT_EQ(readcount, 4100);
  ASSERT_EQ(sended_.size(), 4162);  // 4162 = 31 * 2 + 4 + 4096
  delete block_sender;
  delete block;
}

TEST_F(BlockSenderTests, GenerateChecksumPerf) {
  byte::SetMinLogLevel(byte::LOG_LEVEL_DEBUG);
  FLAGS_bytestore_hdfs_checksum_mode = 1;
  int64_t start_ms = byte::GetCurrentTimeInMs();
  // block id = 10, bpid = BP-2026362776-*************-1546064706393
  ChunkIdMeta chunk_id(10, 2026362776, 3232237157, 1546064706393);
  static const uint32_t k_large_buf_size = 128 * 1024 * 1024;
  ExtendedBlock* block =
      new ExtendedBlock(bpid_, 10, k_large_buf_size, 1546064706393);
  CreateChunk(1, block);

  char* data = new char[k_large_buf_size];
  std::unique_ptr<char[]> scoped_data(data);
  for (uint32_t i = 0; i < k_large_buf_size; ++i) {
    data[i] = i % 32 + 'A';
  }

  WriteChunk(1, block, data, k_large_buf_size, 0);
  char* write_xattr = new char[XATTR_BYTES];
  memset(write_xattr, 0, 32);
  std::unique_ptr<char[]> write_xattr_deleter(write_xattr);
  BlockMeta* meta = reinterpret_cast<BlockMeta*>(write_xattr);
  meta->gs_ = 1546064706393;
  meta->fin_ = 1;
  meta->checksum_enabled_ = 1;
  SetChunkAttr(1, block, write_xattr);
  FreezeChunk(1, block, k_large_buf_size);

  PrepareForSendBlockTest(dn_);

  int64_t cpt1_ms = byte::GetCurrentTimeInMs();
  std::cerr << "prepare 128M data cost " << cpt1_ms - start_ms << " ms"
            << std::endl;

  // Do not create checksum file, generate crc32c from data
  BlockSender* block_sender = nullptr;
  auto e = BlockSender::CreateBlockSender(
      nullptr, block, 0, k_large_buf_size, true, true, dn_, nullptr,
      FLAGS_bytestore_hdfs_default_user, -1, &block_sender);
  EXPECT_EQ(e.GetE(), exceptions::E::kNoException);
  ASSERT_NE(block_sender, nullptr);
  auto op_key = OpKey::New(block->GetBlockPoolID(), block->GetBlockID(),
                           Operation::ReadBlock);
  block_sender->SetOpKey(op_key);

  uint64_t readcount;
  e = block_sender->SendBlock(conn_, &readcount);
  EXPECT_EQ(e.GetE(), exceptions::E::kNoException);

  int64_t cpt2_ms = byte::GetCurrentTimeInMs();
  std::cerr << "generate checksum and send data cost " << cpt2_ms - cpt1_ms
            << " ms" << std::endl;

  // Create meta file and read from it
  DataChecksum* checksum =
      DataChecksum::NewDataChecksum(DataChecksum::TYPE_CRC32C, 4096);
  io::IOChunk* data_chunk = new io::IOChunk(k_large_buf_size);
  io::IOChunk* checksum_chunk =
      new io::IOChunk(checksum->GetChecksumSize(k_large_buf_size));
  data_chunk->WriteBytes(reinterpret_cast<uint8_t*>(data), k_large_buf_size);
  e = checksum->CalculateChecksum(data_chunk, checksum_chunk);
  EXPECT_TRUE(e.OK());
  e = store_->WriteBlockMetaHeader(block, checksum, StorageType::DISK,
                                   PLM_STORAGE_ANY);
  EXPECT_TRUE(e.OK());
  e = store_->WriteChecksum(block, checksum_chunk, 0, checksum_chunk->Length(),
                            false);
  EXPECT_TRUE(e.OK());

  int64_t cpt3_ms = byte::GetCurrentTimeInMs();
  std::cerr << "write meta file cost " << cpt3_ms - cpt2_ms << " ms"
            << std::endl;

  delete block_sender;
  e = BlockSender::CreateBlockSender(
      nullptr, block, 0, k_large_buf_size, true, true, dn_, nullptr,
      FLAGS_bytestore_hdfs_default_user, -1, &block_sender);
  EXPECT_EQ(e.GetE(), exceptions::E::kNoException);
  ASSERT_NE(block_sender, nullptr);
  block_sender->SetOpKey(op_key);

  sended_ = "";
  uint64_t readcount2;
  e = block_sender->SendBlock(conn_, &readcount2);
  EXPECT_EQ(e.GetE(), exceptions::E::kNoException);
  EXPECT_EQ(readcount, readcount2);

  int64_t end_ms = byte::GetCurrentTimeInMs();
  std::cerr << "read checksum and send data cost " << end_ms - cpt3_ms << " ms"
            << std::endl;

  io::IOChunk::Destroy(data_chunk);
  io::IOChunk::Destroy(checksum_chunk);
  delete checksum;
  delete block_sender;
  delete block;
}

}  // namespace hdfs
}  // namespace chunkserver
}  // namespace bytestore
