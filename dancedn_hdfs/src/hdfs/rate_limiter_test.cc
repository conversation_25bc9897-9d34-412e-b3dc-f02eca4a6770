// copyright (c) 2025-present, bytedance inc. all rights reserved.

#include "hdfs/rate_limiter.h"

#include <algorithm>
#include <cstdint>

#include "byte/system/timestamp.h"
#include "byte/thread/this_thread.h"
#include "gtest/gtest.h"

namespace bytestore {
namespace chunkserver {
namespace hdfs {

bool Eq(double val1, double val2, double delta) {
  if (val1 == val2) {
    return true;
  }
  if (val1 > val2 && val2 + delta > val1) {
    return true;
  }
  if (val2 > val1 && val1 + delta > val2) {
    return true;
  }
  return false;
}

static double EPSILON = 0.01;

TEST(RateLimiterTest, SimpleRate) {
  RateLimiter limiter(5.0, 5);
  ASSERT_TRUE(Eq(5.0, limiter.GetRate(), EPSILON));
}

TEST(RateLimiterTest, SimpleAcquire) {
  RateLimiter limiter(5.0);
  ASSERT_TRUE(Eq(0.0, limiter.Acquire(5.0), EPSILON));
  double wait_time = limiter.Acquire();
  ASSERT_TRUE(Eq(0.0, wait_time, EPSILON));
  wait_time = limiter.Acquire(4);
  ASSERT_TRUE(Eq(0.2, wait_time, EPSILON));
  wait_time = limiter.Acquire(10);
  ASSERT_TRUE(Eq(0.8, wait_time, EPSILON));
  wait_time = limiter.Acquire();
  ASSERT_TRUE(Eq(2.0, wait_time, EPSILON));

  byte::ThisThread::SleepInMs(2000);
  wait_time = limiter.Acquire(5);
  ASSERT_TRUE(Eq(0, wait_time, EPSILON));
}

TEST(RateLimiterTest, SimpleBurstAcquire) {
  RateLimiter limiter(5.0, 2.0);
  ASSERT_TRUE(Eq(0.0, limiter.Acquire(10.0), EPSILON));
  byte::ThisThread::SleepInMs(2000);
  double wait_time = limiter.Acquire(10);
  ASSERT_TRUE(Eq(0.0, wait_time, EPSILON));
  ASSERT_TRUE(Eq(0.0, limiter.Acquire(), EPSILON));
  ASSERT_TRUE(Eq(0.2, limiter.Acquire(), EPSILON));

  RateLimiter limiter2(5.0, 2.0);
  ASSERT_TRUE(Eq(0.0, limiter2.Acquire(10.0), EPSILON));
  byte::ThisThread::SleepInMs(2000);
  double wait_time2 = limiter2.Acquire(20);
  ASSERT_TRUE(Eq(0.0, wait_time2, EPSILON));
  ASSERT_TRUE(Eq(2.0, limiter2.Acquire(), EPSILON));
  ASSERT_TRUE(Eq(0.2, limiter2.Acquire(), EPSILON));
}

}  // namespace hdfs
}  // namespace chunkserver
}  // namespace bytestore
