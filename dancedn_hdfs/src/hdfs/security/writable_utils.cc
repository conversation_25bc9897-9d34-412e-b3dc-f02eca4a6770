// Copyright (c) 2020-present, ByteDance Inc. All rights reserved.

#include "hdfs/security/writable_utils.h"

#include <cstdio>
#include <vector>

namespace bytestore {
namespace chunkserver {
namespace hdfs {

int32_t DecodeVIntSize(const char& value) {
  if (value >= -112) {
    return 1;
  } else if (value < -120) {
    return -119 - value;
  }
  return -111 - value;
}

bool IsNegativeVInt(const char& value) {
  return value < -120 || (value >= -112 && value < 0);
}

bool ReadByte(const std::string& stream, int* position, char* result) {
  if ((*position < 0) ||
      static_cast<std::string::size_type>(*position) >= stream.size())
    return false;
  *result = stream[*position];
  (*position)++;
  return true;
}

// reference from hadoop hdfs WritableUtils.java
bool ReadVLong(const std::string& stream, int* position, int64_t* result) {
  char first_byte;
  auto success = ReadByte(stream, position, &first_byte);
  if (!success) return false;
  int32_t len = DecodeVIntSize(first_byte);
  if (len == 1) {
    *result = first_byte;
    return true;
  }
  int64_t ans = 0;
  for (int32_t i = 0; i < len - 1; ++i) {
    char byte;
    success = ReadByte(stream, position, &byte);
    if (!success) return false;
    ans = ans << 8;
    ans = ans | (byte & 0xFF);
  }
  *result = (IsNegativeVInt(first_byte) ? (ans ^ -1L) : ans);
  return true;
}

bool ReadVInt(const std::string& stream, int* position, int32_t* result) {
  int64_t ans = 0;
  auto success = ReadVLong(stream, position, &ans);
  if (!success) {
    return false;
  }
  if ((ans > INT32_MAX) || (ans < INT32_MIN)) {
    return false;
  }
  *result = ans;
  return true;
}

bool ReadVIntInRange(const std::string& stream, int* position, int32_t* result,
                     const int32_t& lower, const int32_t& upper) {
  int64_t ans = 0;
  auto success = ReadVLong(stream, position, &ans);
  if (!success) {
    return false;
  }
  if (ans < lower) {
    return false;
  }
  if (ans > upper) {
    return false;
  }
  *result = ans;
  return true;
}

bool ReadInt(const std::string& stream, int* positon, int32_t* result) {
  char byte;
  std::vector<uint32_t> chs{0, 0, 0, 0};
  for (int i = 0; i < 4; ++i) {
    auto success = ReadByte(stream, positon, &byte);
    if (!success) return false;
    chs[i] = (unsigned char)byte;
  }
  *result = ((chs[0] << 24) + (chs[1] << 16) + (chs[2] << 8) + (chs[3] << 0));
  return true;
}

bool ReadString(const std::string& stream, int* position, std::string* result) {
  int32_t len = 0;
  auto success = ReadInt(stream, position, &len);
  if (!success) return false;
  std::string res;
  for (int i = 0; i < len; ++i) {
    char byte;
    success = ReadByte(stream, position, &byte);
    if (!success) return false;
    res += byte;
  }
  *result = res;
  return true;
}

bool ReadVString(const std::string& stream, int* postion, std::string* result) {
  int32_t len = 0;
  auto success = ReadVInt(stream, postion, &len);
  if (!success) return false;
  std::string res;
  for (int32_t i = 0; i < len; ++i) {
    char byte;
    success = ReadByte(stream, postion, &byte);
    if (!success) return false;
    res += byte;
  }
  *result = res;
  return true;
}

std::string WriteByte(char c) {
  std::string ans;
  ans += c;
  return ans;
}

std::string WriteVLong(int64_t number) {
  if (number >= -112 && number <= 127) {
    return WriteByte(number);
  }
  int32_t len = -112;
  if (number < 0) {
    number ^= -1L;
    len = -120;
  }
  auto temp = number;
  while (temp != 0) {
    temp = temp >> 8;
    len--;
  }
  auto res = WriteByte(len);
  len = (len < -120) ? -(len + 120) : -(len + 112);
  for (int idx = len; idx != 0; idx--) {
    int32_t shiftbits = (idx - 1) * 8;
    int64_t mask = 0xFFL << shiftbits;
    res += WriteByte(((number & mask) >> shiftbits));
  }
  return res;
}

std::string WriteVInt(int32_t number) {
  return WriteVLong(number);
}

std::string Write(int32_t b) {
  return WriteByte(b);
}

std::string WriteInt(int32_t number) {
  auto res = Write((static_cast<uint32_t>(number) >> 24) & 0xFF);
  res += Write((static_cast<uint32_t>(number) >> 16) & 0xFF);
  res += Write((static_cast<uint32_t>(number) >> 8) & 0xFF);
  res += Write((static_cast<uint32_t>(number) >> 0) & 0xFF);
  return res;
}

std::string WriteString(const std::string& s) {
  int32_t len = s.size();
  auto res = WriteInt(len);
  for (const auto& ch : s) {
    res += WriteByte(ch);
  }
  return res;
}

std::string WriteVString(const std::string& str) {
  int32_t len = str.size();
  auto res = WriteVInt(len);
  for (const auto& ch : str) {
    res += WriteByte(ch);
  }
  return res;
}

std::string StrToHexString(const std::string& str) {
  char buf[16] = {0};
  std::string ans;
  for (const auto& ch : str) {
    snprintf(buf, sizeof(buf), "%02x", (unsigned char)ch);
    ans += buf;
  }
  return ans;
}

}  // namespace hdfs
}  // namespace chunkserver
}  // namespace bytestore
