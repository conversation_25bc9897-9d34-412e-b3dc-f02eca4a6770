// Copyright (c) 2020-present, ByteDance Inc. All rights reserved.

#include "hdfs/security/block_pool_token_secret_manager.h"

#include <utility>

#include "hdfs/extended_block.h"
#include "hdfs/security/block_token_secret_manager.h"
#include "hdfs/security/exported_block_keys.h"

namespace bytestore {
namespace chunkserver {
namespace hdfs {

void BlockPoolTokenSecretManager::AddBlockPool(
    const std::string& bpid,
    std::shared_ptr<BlockTokenSecretManager> secret_manager) {
  // write operation performed
  byte::RwLock::WriterLocker guard(&manager_rw_lock_);
  LOG(DEBUG) << "register secret_manager for bp:" << bpid;
  auto iter = manager_.find(bpid);
  if (iter != manager_.end()) {
    // notice secret_manager can't be used after move
    iter->second = std::move(secret_manager);
  } else {
    manager_.emplace(bpid, secret_manager);
  }
}

std::shared_ptr<BlockTokenSecretManager> BlockPoolTokenSecretManager::Get(
    const std::string& bpid) const {
  if (!manager_.count(bpid)) {
    LOG(WARNING) << "failed to Get BlockTokenSecretManager for bp:" << bpid;
    return nullptr;
  }
  return manager_.at(bpid);
}

bool BlockPoolTokenSecretManager::IsBlockPoolRegistered(
    const std::string& bpid) const {
  // read operation performed
  byte::RwLock::ReaderLocker guard(&manager_rw_lock_);
  return manager_.count(bpid);
}

void BlockPoolTokenSecretManager::AddKeys(
    const std::string& bpid, const ExportedBlockKeys& exported_block_keys) {
  // write operation performed
  byte::RwLock::WriterLocker guard(&manager_rw_lock_);
  auto iter = manager_.find(bpid);
  if (iter != manager_.end()) {
    LOG(DEBUG) << "Addkeys called for block_pool:" << bpid;
    iter->second->AddKeys(exported_block_keys);
  }
}

std::shared_ptr<Token> BlockPoolTokenSecretManager::GenerateToken(
    const ExtendedBlock* block, const std::vector<AccessMode>& modes) {
  // read operation performed
  byte::RwLock::ReaderLocker guard(&manager_rw_lock_);
  std::shared_ptr<BlockTokenSecretManager> block_token_secr_manager =
      Get(block->GetBlockPoolID());
  if (block_token_secr_manager == nullptr) {
    LOG(WARNING) << "failed to generate token for bp:"
                 << block->GetBlockPoolID()
                 << " because correspond BlockTokenSecretManager is missed";
    return nullptr;
  }
  return block_token_secr_manager->GenerateToken(block, modes);
}

exceptions::Exception BlockPoolTokenSecretManager::CheckAccess(
    const Token& token, const std::string& user_id, const ExtendedBlock* block,
    const AccessMode& mode) const {
  // read operation performed
  byte::RwLock::ReaderLocker guard(&manager_rw_lock_);
  std::shared_ptr<BlockTokenSecretManager> block_token_secr_manager =
      Get(block->GetBlockPoolID());
  if (block_token_secr_manager == nullptr) {
    LOG(WARNING)
        << "failed to generate token for bp:" << block->GetBlockPoolID()
        << " because correspond BlockTokenSecretManager is not registered";
    return exceptions::Exception(
        exceptions::kIOException,
        "bp" + block->GetBlockPoolID() + " is not registered");
  }
  return block_token_secr_manager->CheckAccess(token, user_id, block, mode);
}

exceptions::Exception BlockPoolTokenSecretManager::CheckAccess(
    const BlockTokenIdentifier& id, const std::string& user_id,
    const ExtendedBlock* block, const AccessMode& access_mode) const {
  // read operation performed
  byte::RwLock::ReaderLocker guard(&manager_rw_lock_);
  std::shared_ptr<BlockTokenSecretManager> block_token_secr_manager =
      Get(block->GetBlockPoolID());
  if (block_token_secr_manager == nullptr) {
    LOG(WARNING)
        << "failed to generate token for bp:" << block->GetBlockPoolID()
        << " because correspond BlockTokenSecretManager is not registered";
    return exceptions::Exception(
        exceptions::kIOException,
        "bp" + block->GetBlockPoolID() + " is not registered");
  }
  return block_token_secr_manager->CheckAccess(id, user_id, block, access_mode);
}

}  // namespace hdfs
}  // namespace chunkserver
}  // namespace bytestore
