// Copyright (c) 2020-present, ByteDance Inc. All rights reserved.

#include "hdfs/security/writable_utils.h"

#include <limits>
#include <random>

#include "gtest/gtest.h"

namespace bytestore {
namespace chunkserver {
namespace hdfs {

class WritableUtilsTest : public ::testing::Test {
 public:
  WritableUtilsTest() = default;
  ~WritableUtilsTest() override = default;
};

TEST_F(WritableUtilsTest, RWVLong) {
  auto fn = [](int64_t number) -> int64_t {
    auto res = WriteVLong(number);
    int position = 0;
    int64_t result = 0;
    auto success = ReadVLong(res, &position, &result);
    EXPECT_TRUE(success);
    return result;
  };
  std::random_device rd;
  std::mt19937_64 eng(rd());
  std::uniform_int_distribution<int64_t> distribution;
  EXPECT_EQ(INT64_MAX, fn(INT64_MAX));
  EXPECT_EQ(INT64_MIN, fn(INT64_MIN));
  EXPECT_EQ(0, fn(0));
  EXPECT_EQ(-1, fn(-1));
  for (int i = 0; i < 10000; ++i) {
    auto random_number = distribution(eng);
    EXPECT_EQ(random_number, fn(random_number));
  }
}

TEST_F(WritableUtilsTest, RWVInt) {
  auto fn = [](int32_t number) -> int32_t {
    auto res = WriteVInt(number);
    int position = 0;
    int32_t result = 0;
    auto success = ReadVInt(res, &position, &result);
    EXPECT_TRUE(success);
    return result;
  };
  std::random_device rd;
  std::mt19937_64 eng(rd());
  std::uniform_int_distribution<int32_t> distribution;
  EXPECT_EQ(INT32_MIN, fn(INT32_MIN));
  EXPECT_EQ(INT32_MAX, fn(INT32_MAX));
  EXPECT_EQ(0, fn(0));
  EXPECT_EQ(-1, fn(-1));
  for (int i = 0; i < 10000; ++i) {
    auto random_number = distribution(eng);
    EXPECT_EQ(random_number, fn(random_number));
  }
}

TEST_F(WritableUtilsTest, RWString) {
  auto fn = [](const std::string& str) -> std::string {
    auto res = WriteString(str);
    int position = 0;
    std::string result;
    auto success = ReadString(res, &position, &result);
    EXPECT_TRUE(success);
    return result;
  };
  const std::string case1 = "12344";
  const std::string case2;
  const std::string case3 = "hf230yhf0ohwe:MfpiofsjhaenFKD";
  const std::string case4 = "%&%^*&3829iurfhe98whf  02hiewuh(*&(hello world";
  const std::string case5 = "COPY";
  const std::string case6 = "READ";
  const std::string case7 = "WRITE";
  const std::string case8 = "REPLACE";
  EXPECT_EQ(case1, fn(case1));
  EXPECT_EQ(case2, fn(case2));
  EXPECT_EQ(case3, fn(case3));
  EXPECT_EQ(case4, fn(case4));
  EXPECT_EQ(case5, fn(case5));
  EXPECT_EQ(case6, fn(case6));
  EXPECT_EQ(case7, fn(case7));
  EXPECT_EQ(case8, fn(case8));
}

TEST_F(WritableUtilsTest, RWVString) {
  auto fn = [](const std::string& str) -> std::string {
    auto res = WriteVString(str);
    int position = 0;
    std::string result;
    auto success = ReadVString(res, &position, &result);
    EXPECT_TRUE(success);
    return result;
  };
  const std::string case1 = "12344";
  const std::string case2;
  const std::string case3 = "hf230yhf0ohwe:MfpiofsjhaenFKD";
  const std::string case4 = "%&%^*&3829iurfhe98whf  02hiewuh(*&(hello world";
  const std::string case5 = "COPY";
  const std::string case6 = "READ";
  const std::string case7 = "WRITE";
  const std::string case8 = "REPLACE";
  EXPECT_EQ(case1, fn(case1));
  EXPECT_EQ(case2, fn(case2));
  EXPECT_EQ(case3, fn(case3));
  EXPECT_EQ(case4, fn(case4));
  EXPECT_EQ(case5, fn(case5));
  EXPECT_EQ(case6, fn(case6));
  EXPECT_EQ(case7, fn(case7));
  EXPECT_EQ(case8, fn(case8));
}

}  // namespace hdfs
}  // namespace chunkserver
}  // namespace bytestore
