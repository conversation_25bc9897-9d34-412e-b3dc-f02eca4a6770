// Copyright (c) 2020-present, ByteDance Inc. All rights reserved.
#ifndef BLOCK_POOL_TOKEN_SECRET_MANAGER_H_
#define BLOCK_POOL_TOKEN_SECRET_MANAGER_H_

#pragma once

#include <memory>
#include <string>
#include <unordered_map>
#include <vector>

#include "byte/concurrent/rwlock.h"
#include "hdfs/exceptions.h"
#include "hdfs/security/block_token_identifier.h"

namespace bytestore {
namespace chunkserver {
namespace hdfs {

class BlockTokenSecretManager;
class ExtendedBlock;
class Token;
class ExportedBlockKeys;

class BlockPoolTokenSecretManager {
 public:
  BlockPoolTokenSecretManager() = default;

  void AddBlockPool(const std::string& bpid,
                    std::shared_ptr<BlockTokenSecretManager> secret_manager);

  bool IsBlockPoolRegistered(const std::string& bpid) const;

  void AddKeys(const std::string& bpid,
               const ExportedBlockKeys& exported_block_keys);

  std::shared_ptr<Token> GenerateToken(const ExtendedBlock* block,
                                       const std::vector<AccessMode>& modes);

  exceptions::Exception CheckAccess(const BlockTokenIdentifier& id,
                                    const std::string& user_id,
                                    const ExtendedBlock* block,
                                    const AccessMode& access_mode) const;

  exceptions::Exception CheckAccess(const Token& token,
                                    const std::string& user_id,
                                    const ExtendedBlock* block,
                                    const AccessMode& mode) const;

  // public for test
  std::shared_ptr<BlockTokenSecretManager> Get(const std::string& bpid) const;

 private:
  mutable byte::RwLock manager_rw_lock_;
  std::unordered_map<std::string, std::shared_ptr<BlockTokenSecretManager>>
      manager_;
};

}  // namespace hdfs
}  // namespace chunkserver
}  // namespace bytestore

#endif
