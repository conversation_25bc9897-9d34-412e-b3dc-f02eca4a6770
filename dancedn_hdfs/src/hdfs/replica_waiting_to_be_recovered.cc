// Copyright (c) 2019-present, ByteDance Inc. All rights reserved.

#include "hdfs/replica_waiting_to_be_recovered.h"

#include <sstream>
#include <string>

#include "byte/string/format/print.h"
#include "hdfs/block.h"
#include "hdfs/extended_block.h"

namespace bytestore {
namespace chunkserver {
namespace hdfs {

ReplicaWaitingToBeRecovered::ReplicaWaitingToBeRecovered(
    const std::string& bpid, uint64_t block_id, uint64_t len, uint64_t gs,
    const std::string& suuid, bool is_transient, bool checksum_enabled,
    uint16_t disk_id, PlacementStorageType type)
    : ReplicaInfo(bpid, block_id, len, gs, suuid, is_transient,
                  checksum_enabled, disk_id, type),
      unlinked_(false) {}

ReplicaWaitingToBeRecovered::ReplicaWaitingToBeRecovered(
    const ExtendedBlock* block, const std::string& storage_uuid,
    bool is_transient, bool checksum_enabled, uint16_t disk_id,
    PlacementStorageType type)
    : ReplicaInfo(block, storage_uuid, is_transient, checksum_enabled, disk_id,
                  type),
      unlinked_(false) {}

ReplicaWaitingToBeRecovered::ReplicaWaitingToBeRecovered(
    const ReplicaWaitingToBeRecovered& from)
    : ReplicaInfo(from), unlinked_(from.IsUnlinked()) {}

uint64_t ReplicaWaitingToBeRecovered::GetBytesOnDisk() const {
  return GetNumBytes();
}

std::string ReplicaWaitingToBeRecovered::ToString() const {
  std::string str;
  str = byte::StringPrint(
      "ReplicaWaitingToBeRecovered, %s, %s\n"
      "  getNumBytes()      = %ld\n"
      "  getBytesOnDisk()   = %ld\n"
      "  getVisibleLength() = %ld\n"
      "  getDiskId()        = %u\n"
      "  unlinked           = %b",
      GetBlock()->ToString(), GetStateString(), GetNumBytes(), GetBytesOnDisk(),
      GetVisibleLength(), GetDiskId(), unlinked_);
  return str;
}

}  // namespace hdfs
}  // namespace chunkserver
}  // namespace bytestore
