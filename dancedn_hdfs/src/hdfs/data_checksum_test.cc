// Copyright (c) 2022-present, ByteDance Inc. All rights reserved.

#include "hdfs/data_checksum.h"

#include <memory>

#include "byte/byte_log/byte_log_impl.h"
#include "byte/system/timestamp.h"
#include "gtest/gtest.h"
#include "hdfs/io/io_buf.h"

namespace bytestore {
namespace chunkserver {
namespace hdfs {

class DataChecksumTests : public ::testing::Test {
 public:
  DataChecksumTests() {}
  ~DataChecksumTests() {}

  void SetUp() {}
  void TearDown() {}

  static void SetUpTestCase() {}
  static void TearDownTestCase() {
    byte::GetLoggingSystem()->Shutdown();
  }
};

TEST_F(DataChecksumTests, Basic) {
  std::string s = "hello world!";
  DataChecksum* c1 =
      DataChecksum::NewDataChecksum(DataChecksum::TYPE_CRC32, 512);
  EXPECT_EQ(0u, c1->GetChecksumSize(0));
  EXPECT_EQ(4u, c1->GetChecksumSize(511));
  EXPECT_EQ(4u, c1->GetChecksumSize(512));
  EXPECT_EQ(8u, c1->GetChecksumSize(513));
  delete c1;

  DataChecksum* c2 =
      DataChecksum::NewDataChecksum(DataChecksum::TYPE_CRC32C, 512);
  c2->Update(reinterpret_cast<const uint8_t*>(s.data()), 0, s.length());
  EXPECT_EQ(0x49CB5777u, (uint32_t)c2->GetValue());
  EXPECT_EQ(0u, c2->GetChecksumSize(0));
  EXPECT_EQ(4u, c2->GetChecksumSize(511));
  EXPECT_EQ(4u, c2->GetChecksumSize(512));
  EXPECT_EQ(8u, c2->GetChecksumSize(513));

  c2->Reset();
  c2->Update(reinterpret_cast<const uint8_t*>(s.data()), 0, 6);
  c2->Update(reinterpret_cast<const uint8_t*>(s.data()), 6, 6);
  EXPECT_EQ(0x49CB5777u, (uint32_t)c2->GetValue());
  delete c2;
}

TEST_F(DataChecksumTests, CalculateChecksum) {
  std::string s = "hello world!";
  DataChecksum* c1 =
      DataChecksum::NewDataChecksum(DataChecksum::TYPE_CRC32C, 5);
  ASSERT_EQ(12u, c1->GetChecksumSize(s.length()));
  auto data = new io::IOChunk(s.length());
  auto checksum = new io::IOChunk(c1->GetChecksumSize(s.length()));
  data->WriteBytes(reinterpret_cast<const uint8_t*>(s.data()), s.length());
  auto e = c1->CalculateChecksum(data, checksum);
  ASSERT_TRUE(e.OK());
  EXPECT_EQ(0x9a71bb4cU, checksum->ReadFixed32BE());
  EXPECT_EQ(0xc241843U, checksum->ReadFixed32BE());
  EXPECT_EQ(0xeda13ed5U, checksum->ReadFixed32BE());
  io::IOChunk::Destroy(data);
  io::IOChunk::Destroy(checksum);
  delete c1;

  DataChecksum* c2 = DataChecksum::NewDataChecksum(DataChecksum::TYPE_CRC32, 5);
  auto data2 = new io::IOChunk(s.length());
  auto checksum2 = new io::IOChunk(c2->GetChecksumSize(s.length()));
  data2->WriteBytes(reinterpret_cast<const uint8_t*>(s.data()), s.length());
  auto e2 = c2->CalculateChecksum(data2, checksum2);
  ASSERT_TRUE(e2.OK());
  io::IOChunk::Destroy(data2);
  io::IOChunk::Destroy(checksum2);
  delete c2;
}

TEST_F(DataChecksumTests, VerifyChecksum) {
  std::string s = "hello world!";
  DataChecksum* c1 =
      DataChecksum::NewDataChecksum(DataChecksum::TYPE_CRC32C, 5);
  ASSERT_EQ(12u, c1->GetChecksumSize(s.length()));
  auto data = new io::IOChunk(s.length());
  auto checksum = new io::IOChunk(c1->GetChecksumSize(s.length()));
  data->WriteBytes(reinterpret_cast<const uint8_t*>(s.data()), s.length());
  checksum->WriteFixed32BE(0x9a71bb4cU);
  checksum->WriteFixed32BE(0xc241843U);
  checksum->WriteFixed32BE(0xeda13ed5U);
  auto e = c1->VerifyChecksum(data, checksum);
  ASSERT_TRUE(e.OK());
  io::IOChunk::Destroy(data);
  io::IOChunk::Destroy(checksum);
  delete c1;

  DataChecksum* c2 = DataChecksum::NewDataChecksum(DataChecksum::TYPE_CRC32, 5);
  auto data2 = new io::IOChunk(s.length());
  auto checksum2 = new io::IOChunk(c2->GetChecksumSize(s.length()));
  data2->WriteBytes(reinterpret_cast<const uint8_t*>(s.data()), s.length());
  auto e2 = c2->VerifyChecksum(data2, checksum2);
  ASSERT_TRUE(e2.OK());
  io::IOChunk::Destroy(data2);
  io::IOChunk::Destroy(checksum2);
  delete c2;
}

TEST_F(DataChecksumTests, Perf) {
  static const uint32_t k_buf_size = 512000000;
  char* data = new char[k_buf_size];
  std::unique_ptr<char[]> scoped_data(data);
  for (uint32_t i = 0; i < k_buf_size; ++i) {
    data[i] = i % 32 + 'A';
  }
  io::IOChunk* data_chunk = new io::IOChunk(k_buf_size);
  data_chunk->WriteBytes(reinterpret_cast<uint8_t*>(data), k_buf_size);
  DataChecksum* c1 =
      DataChecksum::NewDataChecksum(DataChecksum::TYPE_CRC32C, 512);
  io::IOChunk* checksum = new io::IOChunk(c1->GetChecksumSize(k_buf_size));
  int64_t start = byte::GetCurrentTimeInUs();
  auto e = c1->CalculateChecksum(data_chunk, checksum);
  ASSERT_TRUE(e.OK());
  int64_t end = byte::GetCurrentTimeInUs();
  std::cerr << "It took " << end - start << "us to calc " << k_buf_size / 512
            << " checksums.\n";
  io::IOChunk::Destroy(data_chunk);
  io::IOChunk::Destroy(checksum);
  delete c1;
}

}  // namespace hdfs
}  // namespace chunkserver
}  // namespace bytestore
