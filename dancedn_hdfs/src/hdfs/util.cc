// Copyright (c) 2019-present, ByteDance Inc. All rights reserved.

#include "hdfs/util.h"

#include <endian.h>
#include <sys/time.h>

#include <sstream>

#include "byte/algorithm/random.h"
#include "byte/encoding/int128.h"
#include "byte/include/byte_log.h"
#include "byte/system/uuid.h"
#include "hdfs/constants.h"
#include "hdfs/datanode_info.h"
#include "hdfs/datanode_info_with_storage.h"
#include "hdfs/io/connection.h"
#include "hdfs/io/define.h"
#include "hdfs/io/io_buf.h"

namespace bytestore {
namespace chunkserver {
namespace hdfs {

std::string Joiner::Join(const std::vector<std::string>& v) {
  std::ostringstream os;
  auto itr = v.begin();
  if (itr != v.end()) {
    os << *itr;
    itr++;
  }
  for (; itr != v.end(); itr++) {
    os << separator_ << *itr;
  }
  return os.str();
}

std::string Joiner::Join(const std::set<std::string>& v) {
  std::ostringstream os;
  auto itr = v.begin();
  if (itr != v.end()) {
    os << *itr;
    itr++;
  }
  for (; itr != v.end(); itr++) {
    os << separator_ << *itr;
  }
  return os.str();
}

std::string Joiner::Join(const std::unordered_set<std::string>& v) {
  std::ostringstream os;
  auto itr = v.begin();
  if (itr != v.end()) {
    os << *itr;
    itr++;
  }
  for (; itr != v.end(); itr++) {
    os << separator_ << *itr;
  }
  return os.str();
}

template <typename T>
std::string ObjectJoiner<T>::Join(const std::vector<T*>& v) {
  std::ostringstream os;
  auto itr = v.begin();
  if (itr != v.end()) {
    os << (*itr)->ToString();
    itr++;
  }
  for (; itr != v.end(); itr++) {
    os << separator_ << (*itr)->ToString();
  }
  return os.str();
}

uint32_t Random::Next() {
  static thread_local byte::Random r(pthread_self() & 0xffffffff);
  return r.Next();
}

uint32_t Random::Next(uint32_t bound) {
  return Next() % bound;
}

int64_t Now() {
  struct timeval tm;
  gettimeofday(&tm, nullptr);
  return tm.tv_sec * 1000 + tm.tv_usec / 1000;
}

static void UUIDToTwoUInt64(const std::string& uuid, uint64_t* top,
                            uint64_t* bottom) {
  uint32_t d1 = 0;
  uint32_t d2 = 0;
  uint32_t d3 = 0;
  uint32_t d4 = 0;
  uint64_t d5 = 0;
  sscanf(uuid.c_str(), "%08x-%04x-%04x-%04x-%012lx", &d1, &d2, &d3, &d4, &d5);

  *top = (static_cast<uint64_t>(d1) << 32) | d2 << 16 | d3;
  *bottom = (static_cast<uint64_t>(d4) << 48) | d5;
}

std::string GenerateClientID() {
  uint64_t top = 0;
  uint64_t bottom = 0;
  UUIDToTwoUInt64(byte::GenerateUniqueId(), &top, &bottom);

  union {
    struct {
      uint64_t top_;
      uint64_t bottom_;
    };
    char b_[16];
  } id;

  id.top_ = htobe64(top);
  id.bottom_ = htobe64(bottom);

  return std::string(id.b_, 16);
}

int Align8(int n) {
  return ((n + 7) >> 3) << 3;
}

exceptions::Exception ParseHeaderException(
    const hadoop::common::RpcResponseHeaderProto* header) {
  auto e = exceptions::Exception::FromString(header->exceptionclassname());
  if (e == exceptions::kFalseException) {
    if (header->has_errormsg()) {
      return exceptions::Exception(e, "Unknown exception " +
                                          header->exceptionclassname() + ": " +
                                          header->errormsg());
    }
    return exceptions::Exception(e, "Unknown exception " +
                                        header->exceptionclassname() +
                                        ": no error message");
  }

  if (header->has_errormsg()) {
    return exceptions::Exception(e, header->errormsg());
  }

  return exceptions::Exception(e);
}

int ReadStatusProto(io::Connection* conn, google::protobuf::Message* proto) {
  bool one_chunk = conn->OneReadChunk();
  conn->OneReadChunk(true);
  int ret = conn->ReadBytes(1);
  if (ret != IO_OK) {
    conn->OneReadChunk(one_chunk);
    return ret;
  }
  uint32_t proto_len = 0;
  io::IOChunk* chunk = conn->ReadBuf()->Front();
  if (!chunk->ReadVarint32(&proto_len)) {
    conn->OneReadChunk(one_chunk);
    return IO_ERR;
  }
  if (proto_len != 2) {
    conn->OneReadChunk(one_chunk);
    return IO_ERR;
  }
  ret = conn->ReadBytes(2);
  conn->OneReadChunk(one_chunk);
  if (ret != IO_OK) {
    return ret;
  }
  if (!proto->ParseFromArray(chunk->ReadBytes(proto_len), proto_len)) {
    return IO_ERR;
  }
  return IO_OK;
}

int ReadDelimitedFrom(io::Connection* conn, google::protobuf::Message* proto) {
  // Try to read a varint whose max size is 5 bytes.
  bool one_chunk = conn->OneReadChunk();
  conn->OneReadChunk(true);
  int ret = conn->ReadBytes(io::IOChunk::Varint32MaxBytes());
  if (ret != IO_OK) {
    conn->OneReadChunk(one_chunk);
    LOG(ERROR) << "ReadBytes failed, error code: " << ret;
    return ret;
  }
  uint32_t proto_len = 0;
  io::IOChunk* chunk = conn->ReadBuf()->Front();
  if (!chunk->ReadVarint32(&proto_len)) {
    conn->OneReadChunk(one_chunk);
    LOG(ERROR) << "ReadVarint32 failed";
    return IO_ERR;
  }

  int32_t remain_len = proto_len + io::IOChunk::CalcVarintSize(proto_len) -
                       io::IOChunk::Varint32MaxBytes();
  if (remain_len < 0) {
    conn->OneReadChunk(one_chunk);
    LOG(ERROR) << "read failed since remain length less than 0.";
    return IO_ERR;
  }
  ret = conn->ReadBytes(remain_len);
  conn->OneReadChunk(one_chunk);
  if (ret != IO_OK) {
    LOG(ERROR) << "ReadBytes failed, error code: " << ret;
    return ret;
  }

  if (!proto->ParseFromArray(chunk->ReadBytes(proto_len), proto_len)) {
    LOG(ERROR) << "ParseFromArray failed, "
                  "please check whether the required parameters have been set.";
    return IO_ERR;
  }
  return IO_OK;
}

int WriteDelimitedTo(io::Connection* conn,
                     const google::protobuf::Message& proto) {
  std::string str = proto.SerializeAsString();
  int32_t body_len = io::IOChunk::CalcVarintSize(str.size()) + str.size();
  io::IOChunk* chunk = new io::IOChunk(body_len);
  chunk->WriteVarint(str.size());
  chunk->WriteBytes((const uint8_t*)str.data(), str.size());
  conn->WriteBuf()->Append(chunk);
  return conn->Write(true);
}

int SendResponse(io::Connection* conn, const hadoop::hdfs::Status& st,
                 const std::string& msg) {
  hadoop::hdfs::BlockOpResponseProto resp;
  resp.set_status(st);
  if (!msg.empty()) {
    resp.set_message(msg);
  }
  return WriteDelimitedTo(conn, resp);
}

int SendResponse(io::Connection* conn, const hadoop::hdfs::Status& st) {
  return SendResponse(conn, st, "");
}

hadoop::common::RpcResponseHeaderProto* InitResponseHeader(
    const hadoop::common::RpcRequestHeaderProto* rpc_header) {
  auto response_header = new hadoop::common::RpcResponseHeaderProto();
  response_header->set_callid(rpc_header->callid());
  response_header->set_clientid(rpc_header->clientid());
  response_header->set_retrycount(rpc_header->retrycount());
  response_header->set_serveripcversionnum(RPC_CURRENT_VERSION);
  return response_header;
}

hadoop::common::RpcResponseHeaderProto* BuildResponseHeader(
    const hadoop::common::RpcRequestHeaderProto* rpc_header) {
  auto response_header = InitResponseHeader(rpc_header);
  response_header->set_status(
      hadoop::common::RpcResponseHeaderProto_RpcStatusProto_SUCCESS);
  return response_header;
}

hadoop::common::RpcResponseHeaderProto* BuildResponseHeaderWithException(
    const hadoop::common::RpcRequestHeaderProto* rpc_header,
    const exceptions::Exception& e,
    const hadoop::common::RpcResponseHeaderProto_RpcStatusProto& status,
    const hadoop::common::RpcResponseHeaderProto_RpcErrorCodeProto& errcode) {
  if (e.OK()) return BuildResponseHeader(rpc_header);
  auto response_header = InitResponseHeader(rpc_header);
  response_header->set_status(status);
  response_header->set_errordetail(errcode);
  response_header->set_exceptionclassname(e.GetClass());
  response_header->set_errormsg(e.ToString());
  return response_header;
}

hadoop::common::RpcResponseHeaderProto_RpcStatusProto ClassifyException(
    const exceptions::Exception& e) {
  switch (e.GetE()) {
    case exceptions::kInvalidRpcHeaderException:
      return hadoop::common::RpcResponseHeaderProto_RpcStatusProto_FATAL;
    case exceptions::kAuthorizationException:
      return hadoop::common::RpcResponseHeaderProto_RpcStatusProto_FATAL;
    case exceptions::kNoException:
    default:
      return hadoop::common::RpcResponseHeaderProto_RpcStatusProto_SUCCESS;
  }
}

hadoop::common::RpcResponseHeaderProto_RpcErrorCodeProto TranslateException(
    const exceptions::Exception& e) {
  switch (e.GetE()) {
    case exceptions::kInvalidRpcHeaderException:
      return hadoop::common::
          RpcResponseHeaderProto_RpcErrorCodeProto_FATAL_INVALID_RPC_HEADER;
    case exceptions::kAuthorizationException:
      return hadoop::common::
          RpcResponseHeaderProto_RpcErrorCodeProto_FATAL_UNAUTHORIZED;
    default:
      return hadoop::common::
          RpcResponseHeaderProto_RpcErrorCodeProto_ERROR_APPLICATION;
  }
}

}  // namespace hdfs
}  // namespace chunkserver
}  // namespace bytestore
