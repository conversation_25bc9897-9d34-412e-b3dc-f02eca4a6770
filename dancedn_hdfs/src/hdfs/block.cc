// Copyright (c) 2019-present, ByteDance Inc. All rights reserved.

#include "hdfs/block.h"

#include "byte/string/format/print.h"
#include "hdfs/generation_stamp.h"
#include "hdfs/proto/hdfs.pb.h"

namespace bytestore {
namespace chunkserver {
namespace hdfs {

static const char BLOCK_FILE_PREFIX[] = "blk_";
static const char METADATA_EXTENSION[] = ".meta";

Block::Block() : block_id_(0), num_bytes_(0), gs_(0), is_checksum_(false) {}

Block::Block(uint64_t block_id, uint64_t len, uint64_t gs)
    : block_id_(block_id), num_bytes_(len), gs_(gs), is_checksum_(false) {}

Block::Block(uint64_t block_id, uint64_t len, uint64_t gs, bool is_checksum)
    : block_id_(block_id),
      num_bytes_(len),
      gs_(gs),
      is_checksum_(is_checksum) {}

Block::Block(uint64_t block_id)
    : block_id_(block_id),
      num_bytes_(0),
      gs_(GenerationStamp::GRANDFATHER_GENERATION_STAMP) {}

Block::Block(const Block& block) {
  block_id_ = block.block_id_;
  num_bytes_ = block.num_bytes_;
  gs_ = block.gs_;
  is_checksum_ = block.is_checksum_;
}

Block* Block::Clone() const {
  return new Block(block_id_, num_bytes_, gs_);
}

Block* Block::GetChecksumBlock() const {
  return new Block(block_id_, num_bytes_, gs_, true);
}

bool Block::Equals(const Block* block) const {
  return block_id_ == block->block_id_;
}

std::string Block::GetBlockName() const {
  return byte::StringPrint("%s%llu", BLOCK_FILE_PREFIX, block_id_);
}

std::string Block::ToString() const {
  return byte::StringPrint("%s_%llu", GetBlockName(), gs_);
}

exceptions::Exception Block::ToProto(hadoop::hdfs::BlockProto* proto) const {
  proto->set_blockid(block_id_);
  proto->set_genstamp(gs_);
  proto->set_numbytes(num_bytes_);
  return exceptions::Exception();
}

Block* Block::ParseProto(const hadoop::hdfs::BlockProto& proto) {
  auto res = new Block(proto.blockid(), proto.numbytes(), proto.genstamp());
  return res;
}

}  // namespace hdfs
}  // namespace chunkserver
}  // namespace bytestore
