// Copyright (c) 2018-present, ByteDance Inc. All rights reserved.

#include "hdfs/thread.h"

#include <functional>
#include <memory>

#include "byte/concurrent/mutex.h"
#include "byte/system/timestamp.h"
#include "gtest/gtest.h"

namespace bytestore {
namespace chunkserver {
namespace hdfs {

class MockThread : public Thread {
 public:
  explicit MockThread(std::function<void(Thread*)> fn) : fn_(fn) {}
  ~MockThread() {}

  void Run() {
    fn_(this);
  }

 private:
  std::function<void(Thread*)> fn_;
};

class ThreadTests : public ::testing::Test {
 public:
  void SetUp() {}
  void TearDown() {}
};

TEST_F(ThreadTests, Simple) {
  {
    byte::Mutex mtx;
    mtx.Lock();
    auto actor = new MockThread([&mtx](Thread*) {
      mtx.Unlock();
    });
    actor->Start();
    mtx.Lock();
    actor->Stop();
    mtx.Unlock();
    actor->Join();
    delete actor;
  }
  {
    byte::Mutex mtx;
    mtx.Lock();
    auto actor = new MockThread([&mtx](Thread* actor) {
      mtx.Unlock();
      while (!actor->IsStopped()) {
        usleep(1000 * 20);
      }
    });
    actor->Start();
    mtx.Lock();
    actor->Stop();
    mtx.Unlock();
    actor->Join();
    delete actor;
  }
}

TEST_F(ThreadTests, DoubleStart) {
  auto actor = new MockThread([](Thread*) {
    usleep(1000 * 20);
  });
  ASSERT_EQ(actor->Start(), true);
  ASSERT_EQ(actor->Start(), false);
  actor->Stop();
  actor->Join();
  ASSERT_EQ(actor->Start(), true);
  actor->Stop();
  actor->Join();
  delete actor;
}

TEST_F(ThreadTests, Wait) {
  auto actor = new MockThread([](Thread*) {
    usleep(1000 * 40);
  });
  ASSERT_EQ(actor->Start(), true);
  int64_t start_ms = byte::GetCurrentTimeInMs();
  actor->WaitFor(20);
  int64_t cpt1_ms = byte::GetCurrentTimeInMs();
  ASSERT_GE(cpt1_ms - start_ms, 20);

  ASSERT_EQ(actor->WaitFinished(10), false);
  ASSERT_EQ(actor->WaitFinished(20), true);
  int64_t cpt2_ms = byte::GetCurrentTimeInMs();
  ASSERT_GE(cpt2_ms - cpt1_ms, 20);

  actor->WaitFor(20);  // return immediately since it has been stopped
  int64_t end_ms = byte::GetCurrentTimeInMs();
  ASSERT_LT(end_ms - cpt2_ms, 10);

  actor->Stop();
  actor->Join();
  delete actor;
}

}  // namespace hdfs
}  // namespace chunkserver
}  // namespace bytestore
