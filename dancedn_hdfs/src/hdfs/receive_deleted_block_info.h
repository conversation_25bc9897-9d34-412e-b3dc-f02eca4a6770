// Copyright (c) 2019-present, ByteDance Inc. All rights reserved.

#pragma once

#include <string>

#include "hdfs/exceptions.h"

namespace hadoop {
namespace hdfs {
namespace datanode {

class ReceivedDeletedBlockInfoProto;

}
}  // namespace hdfs
}  // namespace hadoop

namespace bytestore {
namespace chunkserver {
namespace hdfs {

class Block;

class BlockStatus {
 public:
  static const uint8_t RECEIVING_BLOCK = 1;
  static const uint8_t RECEIVED_BLOCK = 2;
  static const uint8_t DELETED_BLOCK = 3;

 public:
  explicit BlockStatus(const uint8_t code) : code_(code) {}
  uint8_t GetCode() const {
    return code_;
  }
  static BlockStatus* FromCode(uint8_t code) {
    switch (code) {
      case RECEIVING_BLOCK: return new BlockStatus(RECEIVING_BLOCK);
      case RECEIVED_BLOCK: return new BlockStatus(RECEIVED_BLOCK);
      case DELETED_BLOCK: return new BlockStatus(DELETED_BLOCK);
      default: return nullptr;
    }
  }
  std::string ToString() const {
    switch (code_) {
      case RECEIVING_BLOCK: return "RECEIVING_BLOCK";
      case RECEIVED_BLOCK: return "RECEIVED_BLOCK";
      case DELETED_BLOCK: return "DELETED_BLOCK";
      default: return "ERROR:NO_USE_STATUS";
    }
  }

 private:
  uint8_t code_;
};

class ReceivedDeletedBlockInfo {
 public:
  ReceivedDeletedBlockInfo();
  ReceivedDeletedBlockInfo(Block* block, const BlockStatus& status,
                           const std::string& del_hints);
  ~ReceivedDeletedBlockInfo();

  Block* GetBlock() const {
    return block_;
  }
  void SetBlock(Block* block) {
    block_ = block;
  }
  std::string GetDelHints() const {
    return del_hints_;
  }
  void SetDelHints(const std::string& del_hints) {
    del_hints_ = del_hints;
  }
  BlockStatus GetStatus() const {
    return status_;
  }
  void SetStatus(const BlockStatus& status) {
    status_ = status;
  }
  std::string ToString() const;
  bool BlockEquals(const Block* b);
  bool IsDeletedBlock() const;
  bool Equals(const ReceivedDeletedBlockInfo* rhs) const;

  exceptions::Exception ToProto(
      hadoop::hdfs::datanode::ReceivedDeletedBlockInfoProto* proto);

 private:
  Block* block_;
  BlockStatus status_;
  std::string del_hints_;
};

}  // namespace hdfs
}  // namespace chunkserver
}  // namespace bytestore
