// Copyright (c) 2019-present, ByteDance Inc. All rights reserved.

#include "hdfs/namespace_info.h"

#include "byte/string/format/print.h"
#include "hdfs/proto/hdfs.pb.h"

namespace bytestore {
namespace chunkserver {
namespace hdfs {

NameSpaceInfo::NameSpaceInfo()
    : StorageInfo(NodeType::NAME_NODE),
      build_version_(""),
      block_pool_id_(""),
      software_version_("") {}

NameSpaceInfo::NameSpaceInfo(int namespace_id, const std::string& cluster_id,
                             const std::string& block_pool_id, uint64_t ctime,
                             const std::string& build_version,
                             const std::string& software_version)
    : StorageInfo(DATANODE_LAYOUT_VERSION, namespace_id, cluster_id, ctime,
                  NodeType::NAME_NODE),
      build_version_(build_version),
      block_pool_id_(block_pool_id),
      software_version_(software_version) {}

std::string NameSpaceInfo::ToString() const {
  return byte::StringPrint("%s;bpid=%s", StorageInfo::ToString(),
                           block_pool_id_);
}

NameSpaceInfo* NameSpaceInfo::ParseProto(
    hadoop::hdfs::VersionResponseProto* proto) {
  if (proto == nullptr) return nullptr;
  if (!proto->has_info()) {
    return nullptr;
  }
  auto ns_info_proto = proto->info();
  auto res = new NameSpaceInfo(
      ns_info_proto.storageinfo().namespceid(),
      ns_info_proto.storageinfo().clusterid(), ns_info_proto.blockpoolid(),
      ns_info_proto.storageinfo().ctime(), ns_info_proto.buildversion(),
      ns_info_proto.softwareversion());
  return res;
}

}  // namespace hdfs
}  // namespace chunkserver
}  // namespace bytestore
