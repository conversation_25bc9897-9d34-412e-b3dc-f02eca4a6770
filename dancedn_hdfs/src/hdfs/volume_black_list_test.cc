// Copyright (c) 2023-present, ByteDance Inc. All rights reserved.

#include "hdfs/volume_black_list.h"

#include <memory>

#include "byte/byte_log/byte_log_impl.h"
#include "byte/io/file_system.h"
#include "byte/io/file_util.h"
#include "byte/io/local_filesystem.h"
#include "gflags/gflags.h"
#include "gtest/gtest.h"
#include "hdfs/exceptions.h"

DECLARE_string(bytestore_hdfs_volume_black_list_file);

namespace bytestore {
namespace chunkserver {
namespace hdfs {

class VolumeBlackListTests : public ::testing::Test {
 public:
  VolumeBlackListTests() {}

  ~VolumeBlackListTests() {}

  void SetUp() {
    // There is no permission to mkdir /opt/tiger/dancedn on the devbox
    FLAGS_bytestore_hdfs_volume_black_list_file =
        work_dir_ + "/volume_black_list";
    black_list_path_ = FLAGS_bytestore_hdfs_volume_black_list_file;
    byte::LocalFileSystem local_fs;
    if (byte::DirectoryExists(work_dir_)) {
      byte::DeleteOptions delete_options;
      delete_options.recursively_ = true;
      byte::Status s = local_fs.DeleteDir(work_dir_, delete_options);
      ASSERT_TRUE(s.ok());
    }
  }

  void TearDown() {
    byte::LocalFileSystem local_fs;
    byte::DeleteOptions delete_options;
    delete_options.recursively_ = true;
    local_fs.DeleteDir(work_dir_, delete_options);
  }

  static void SetUpTestCase() {}
  static void TearDownTestCase() {
    byte::GetLoggingSystem()->Shutdown();
  }

 public:
  std::string black_list_path_;

 private:
  std::string work_dir_ = "./dancedn";
};

TEST_F(VolumeBlackListTests, LifeCycle) {
  byte::LocalFileSystem local_fs;
  // expect that volume black list file does not exist
  ASSERT_FALSE(local_fs.Exists(black_list_path_).ok());
  std::unique_ptr<VolumeBlackList> volume_black_list;
  volume_black_list.reset(new VolumeBlackList());
  auto e = volume_black_list->Init();
  ASSERT_TRUE(e.OK());
  // expect that black list file exists after initialization
  ASSERT_TRUE(local_fs.Exists(black_list_path_).ok());
  auto b_list = volume_black_list->GetBlackListSet();
  ASSERT_EQ(b_list.size(), 0);

  std::string volume10 = "/data10/dancedn";
  std::string volume20 = "/data20/dancedn";
  // append normally
  e = volume_black_list->Append(volume10);
  ASSERT_TRUE(e.OK());
  b_list = volume_black_list->GetBlackListSet();
  ASSERT_EQ(b_list.size(), 1);
  std::unique_ptr<VolumeBlackList> vbl_tmp;
  vbl_tmp.reset(new VolumeBlackList());
  vbl_tmp->Init();
  auto b_list_tmp = vbl_tmp->GetBlackListSet();
  ASSERT_EQ(b_list_tmp.size(), 1);
  // append an existing volume
  e = volume_black_list->Append(volume10);
  ASSERT_TRUE(e.OK());
  ASSERT_TRUE(e.GetMessage().find("already exists") != std::string::npos);
  b_list = volume_black_list->GetBlackListSet();
  ASSERT_EQ(b_list.size(), 1);
  // erase a volume that does not exist
  e = volume_black_list->Erase(volume20);
  ASSERT_TRUE(e.OK());
  ASSERT_TRUE(e.GetMessage().find("does not exist") != std::string::npos);
  b_list = volume_black_list->GetBlackListSet();
  ASSERT_EQ(b_list.size(), 1);
  // erase normally
  e = volume_black_list->Erase(volume10);
  ASSERT_TRUE(e.OK());
  b_list = volume_black_list->GetBlackListSet();
  ASSERT_EQ(b_list.size(), 0);
  vbl_tmp.reset(new VolumeBlackList());
  vbl_tmp->Init();
  b_list_tmp = vbl_tmp->GetBlackListSet();
  ASSERT_EQ(b_list_tmp.size(), 0);
}

}  // namespace hdfs
}  // namespace chunkserver
}  // namespace bytestore
