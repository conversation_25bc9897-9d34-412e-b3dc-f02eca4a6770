// Copyright (c) 2023-present, ByteDance Inc. All rights reserved.

#include <fstream>
#include <memory>
#include <set>
#include <unordered_map>
#include <vector>

#include "byte/concurrent/count_down_latch.h"
#include "byte/io/file_util.h"
#include "byte/io/local_filesystem.h"
#include "byte/system/timestamp.h"
#include "byte/thread/this_thread.h"
#include "chunkserver/common/unittest_common.h"
#include "chunkserver/env.h"
#include "common/async_thread.h"
#include "common/metrics.h"
#include "gflags/gflags.h"
#include "gmock/gmock.h"
#include "gtest/gtest.h"
#include "hdfs/block_list_as_longs.h"
#include "hdfs/block_pool_manager.h"
#include "hdfs/block_pool_service.h"
#include "hdfs/chunkserver_store.h"
#include "hdfs/data_xceiver.h"
#include "hdfs/datanode.h"
#include "hdfs/datanode_config.h"
#include "hdfs/datanode_id.h"
#include "hdfs/datanode_info.h"
#include "hdfs/datanode_storage.h"
#include "hdfs/datanode_stream_server.h"
#include "hdfs/directory_scanner.h"
#include "hdfs/extended_block.h"
#include "hdfs/finalized_replica.h"
#include "hdfs/io/connection.h"
#include "hdfs/io/io_buf.h"
#include "hdfs/migration_scanner.h"
#include "hdfs/mocks.h"
#include "hdfs/namespace_info.h"
#include "hdfs/recovering_block.h"
#include "hdfs/replica_in_pipeline.h"
#include "hdfs/replica_recovery_info.h"
#include "hdfs/replica_under_recovery.h"
#include "hdfs/storage_report.h"
#include "hdfs/storage_type.h"
#include "hdfs/store.h"

#define SYNC_HANDLE_OPER(FUNC)                                                 \
  {                                                                            \
    byte::CountDownLatch latch(1);                                             \
    google::protobuf::Closure* done = google::protobuf::NewCallback(           \
        this, &ChunkServerStoreTests::IOOperDone, &latch);                     \
    oper->SetCallback(done);                                                   \
    oper->request_.set_disk_id(disk_id);                                       \
    dn_interface_->FUNC(&oper->controller_, &oper->request_, &oper->response_, \
                        oper->done_);                                          \
    latch.Wait();                                                              \
    EXPECT_EQ(BYTESTORE_OK, oper->response_.error_code());                     \
  }

DECLARE_string(bytestore_chunkserver_work_dir);
DECLARE_bool(bytestore_chunkserver_truncate_chunk_after_freeze);
DECLARE_MEDIA_FLAG_int64(bytestore_chunkserver_reserved_disk_size);
DECLARE_bool(bytestore_chunkserver_admit_duplicate_uuid_disk);
DECLARE_bool(bytestore_chunkserver_enable_hardlink_on_unfrozen_chunk);
DECLARE_uint64(bytestore_hdfs_recovery_lock_timeout);
DECLARE_uint32(bytestore_hdfs_checksum_mode);
DECLARE_int32(bytestore_chunkserver_delete_manager_interval_time_ms);
DECLARE_bool(bytestore_chunkserver_enable_tiering_migration);
DECLARE_uint32(bytestore_hdfs_migration_scanner_interval_time_ms);
DECLARE_uint32(bytestore_hdfs_directory_scanner_interval_time_ms);
DECLARE_uint64(bytestore_chunkserver_tiering_migration_sync_delay_ms);
DECLARE_uint32(
    bytestore_chunkserver_tiering_migration_low_water_mark_percentage);
DECLARE_int64(
    bytestore_hdfs_tiering_migration_max_read_throughput_bytes_per_disk);
DECLARE_uint32(bytestore_hdfs_warm_up_migration_step_num);

namespace bytestore {
namespace chunkserver {
namespace hdfs {

exceptions::Exception GenVersionFileForHdfs(DiskIdConfMap disk_id_conf_map);

class ChunkServerStoreTests : public ::testing::Test {
 public:
  void SetUp() override {
    FLAGS_bytestore_chunkserver_enable_tiering_migration = true;
    FLAGS_bytestore_chunkserver_tiering_migration_sync_delay_ms = 0;
    FLAGS_bytestore_chunkserver_tiering_migration_low_water_mark_percentage = 0;
    MFLAGS_set(bytestore_chunkserver_reserved_disk_size, TYPE_SATA_HDD,
               3ULL * 1000 * 1000 * 1000);
    metrics_internal::InitFastMetrics();
    std::string work_dir = "./ChunkServerStoreAdvancedTestDir/";
    namespace_id_ = 1004580134;
    cluster_id_ = "CID-9a628355-6954-473b-a66c-d34d7c2b3805";
    create_time_ = 1546064706393;
    build_version_ = "2.6.0";
    software_version_ = "2.6.3";
    bpid_ = "BP-2026362776-*************-1546064706393";
    byte::LocalFileSystem local_fs;
    // if work_dir exists, delete it
    if (byte::DirectoryExists(std::string(work_dir))) {
      byte::DeleteOptions delete_options;
      delete_options.recursively_ = true;
      local_fs.DeleteDir(std::string(work_dir), delete_options);
    }
    EXPECT_TRUE(
        local_fs.CreateDir(std::string(work_dir), byte::CreateOptions()).ok());
    EXPECT_TRUE(
        local_fs
            .CreateDir(std::string(work_dir) + "/disk1", byte::CreateOptions())
            .ok());
    EXPECT_TRUE(
        local_fs
            .CreateDir(std::string(work_dir) + "/disk2", byte::CreateOptions())
            .ok());
    EXPECT_TRUE(
        local_fs
            .CreateDir(std::string(work_dir) + "/disk3", byte::CreateOptions())
            .ok());
    EXPECT_TRUE(
        local_fs
            .CreateDir(std::string(work_dir) + "/disk4", byte::CreateOptions())
            .ok());
    disk_id_conf_map_[1] =
        DiskConfig(1, std::string(work_dir) + "/disk1", CDT_SATA_HDD);
    disk_id_conf_map_[2] =
        DiskConfig(2, std::string(work_dir) + "/disk2", CDT_SATA_HDD);
    disk_id_conf_map_[3] =
        DiskConfig(3, std::string(work_dir) + "/disk3", CDT_NVME_SSD);
    disk_id_conf_map_[4] =
        DiskConfig(4, std::string(work_dir) + "/disk4", CDT_NVME_SSD);
    auto ex = GenVersionFileForHdfs(disk_id_conf_map_);
    EXPECT_TRUE(ex.OK());
    // gflags::FlagSaver saver;
    FLAGS_bytestore_chunkserver_work_dir = std::string(work_dir);
    FLAGS_bytestore_chunkserver_truncate_chunk_after_freeze = true;
    FLAGS_bytestore_chunkserver_admit_duplicate_uuid_disk = true;
    FLAGS_bytestore_chunkserver_delete_manager_interval_time_ms = 1000;

    cs_env_ = new Env(work_dir);
    cs_config_.DEBUG_ImportConfig(disk_id_conf_map_);
    EnvOptions env_options;
    env_options.cs_config_ = &cs_config_;
    EXPECT_EQ(cs_env_->Init(env_options), BYTESTORE_OK);
    ASSERT_EQ(cs_env_->Start(), BYTESTORE_OK);
    EXPECT_EQ(cs_env_->UpdateCSStatus(CHUNKSERVER_NORMAL), BYTESTORE_OK);
    dn_interface_.reset(new DatanodeInterface(&cs_config_, cs_env_));

    // block id = 1, bpid = BP-2026362776-*************-1546064706393
    ChunkIdMeta chunk_id1(1, 2026362776, 3232237157, 1546064706393);
    std::unique_ptr<ExtendedBlock> eblock1(
        new ExtendedBlock(bpid_, chunk_id1.block_id_));
    CreateChunk(1, eblock1.get());
    std::unique_ptr<ExtendedBlock> csum_block1(eblock1->GetChecksumBlock());
    CreateChunk(1, csum_block1.get());

    // block id = 2, bpid = BP-2026362776-*************-1546064706393
    ChunkIdMeta chunk_id2(2, 2026362776, 3232237157, 1546064706393);
    std::unique_ptr<ExtendedBlock> eblock2(
        new ExtendedBlock(bpid_, chunk_id2.block_id_));
    CreateChunk(2, eblock2.get());

    // block id = 3, bpid = BP-2026362776-*************-1546064706393
    ChunkIdMeta chunk_id3(3, 2026362776, 3232237157, 1546064706393);
    std::unique_ptr<ExtendedBlock> eblock3(
        new ExtendedBlock(bpid_, chunk_id3.block_id_));
    CreateChunk(1, eblock3.get());

    // block id = 11, bpid = BP-2026362776-*************-1546064706393
    ChunkIdMeta chunk_id11(11, 2026362776, 3232237157, 1546064706393);
    std::unique_ptr<ExtendedBlock> eblock11(
        new ExtendedBlock(bpid_, chunk_id11.block_id_));
    CreateChunk(3, eblock11.get(), PLM_STORAGE_TIERED_NVME_SSD_HDD);

    // block id = 12, bpid = BP-2026362776-*************-1546064706393
    ChunkIdMeta chunk_id12(12, 2026362776, 3232237157, 1546064706393);
    std::unique_ptr<ExtendedBlock> eblock12(
        new ExtendedBlock(bpid_, chunk_id12.block_id_));
    CreateChunk(3, eblock12.get(), PLM_STORAGE_TIERED_NVME_SSD_HDD);

    // block id = 21, bpid = BP-2026362776-*************-1546064706393
    ChunkIdMeta chunk_id21(21, 2026362776, 3232237157, 1546064706393);
    std::unique_ptr<ExtendedBlock> eblock21(
        new ExtendedBlock(bpid_, chunk_id21.block_id_));
    CreateChunk(4, eblock21.get());

    static const uint32_t k_buf_size =
        4096;  // Must be equals to multiple of 32
    static const uint32_t k_num_rounds = 100;
    char* data = new char[k_buf_size];
    std::unique_ptr<char[]> scoped_data(data);
    for (uint32_t i = 0; i < k_buf_size; ++i) {
      data[i] = i % 32 + 'A';
    }
    io::IOChunk* chunk = new io::IOChunk(k_buf_size);
    chunk->WriteBytes(reinterpret_cast<uint8_t*>(data), k_buf_size);
    DataChecksum* c1 =
        DataChecksum::NewDataChecksum(DataChecksum::TYPE_CRC32C, 512);
    auto clength = c1->GetChecksumSize(k_buf_size);
    io::IOChunk* checksum = new io::IOChunk(clength);
    auto e = c1->CalculateChecksum(chunk, checksum);
    EXPECT_TRUE(e.OK());
    for (uint32_t i = 0; i < k_num_rounds; ++i) {
      WriteChunk(1, eblock1.get(), data, k_buf_size, i * k_buf_size);
      WriteChunk(1, csum_block1.get(),
                 reinterpret_cast<char*>(checksum->Data()), clength,
                 i * clength);
      WriteChunk(2, eblock2.get(), data, k_buf_size, i * k_buf_size);
      WriteChunk(1, eblock3.get(), data, k_buf_size, i * k_buf_size);
      WriteChunk(3, eblock11.get(), data, k_buf_size, i * k_buf_size);
      WriteChunk(3, eblock12.get(), data, k_buf_size, i * k_buf_size);
      WriteChunk(4, eblock21.get(), data, k_buf_size, i * k_buf_size);
    }
    delete c1;
    io::IOChunk::Destroy(chunk);
    io::IOChunk::Destroy(checksum);

    char* write_xattr = new char[XATTR_BYTES];
    memset(write_xattr, 0, 32);
    std::unique_ptr<char[]> write_xattr_deleter(write_xattr);
    BlockMeta* meta = reinterpret_cast<BlockMeta*>(write_xattr);
    // block1, gs_ = 1546064706393, finalized, checksum enabled, hdd
    meta->gs_ = 1546064706393;
    meta->fin_ = 1;
    meta->checksum_enabled_ = 1;
    SetChunkAttr(1, eblock1.get(), write_xattr);
    meta->checksum_type_ = 2;
    meta->bytes_per_checksum_ = 512;
    SetChunkAttr(1, csum_block1.get(), write_xattr);
    // Do not FreezeChunk for append

    // block11, gs_ = 1546064706393, finalized, tiered ssd
    SetChunkAttr(3, eblock11.get(), write_xattr);
    FreezeChunk(3, eblock11.get(), k_num_rounds * k_buf_size,
                PLM_STORAGE_TIERED_NVME_SSD_HDD);

    // block21, gs_ = 1546064706393, finalized, ssd
    SetChunkAttr(4, eblock21.get(), write_xattr);
    // Do not FreezeChunk for append

    // block2, gs_ = 1546064706393, rbw, hdd
    meta->fin_ = 0;
    meta->checksum_enabled_ = 0;
    SetChunkAttr(2, eblock2.get(), write_xattr);

    // block12, gs_ = 1546064706393, rbw, tiered ssd
    SetChunkAttr(3, eblock12.get(), write_xattr);

    // block3, gs_ = 1546064706393, tmp, hdd
    meta->tmp_ = 1;
    SetChunkAttr(1, eblock3.get(), write_xattr);

    dn_ = new MockDataNode();
    dn_->SetConfig(new DataNodeConfig());
    MockDataTransferManager* dtm = new MockDataTransferManager();
    dn_->SetDataTransferManager(dtm);
    EXPECT_CALL(*dn_, NotifyNamenodeDeletedBlock(::testing::_, ::testing::_))
        .WillRepeatedly(::testing::Return());
    EXPECT_CALL(*dn_, NotifyNamenodeReceivedBlock(::testing::_, ::testing::_,
                                                  ::testing::_))
        .WillRepeatedly(::testing::Return());
    EXPECT_CALL(*dn_, NotifyNamenodeMigratedBlock(::testing::_, ::testing::_,
                                                  ::testing::_))
        .WillRepeatedly(::testing::Return());
    EXPECT_CALL(*dn_, ReportBadBlock(::testing::_, ::testing::_, ::testing::_))
        .WillRepeatedly(::testing::Return(exceptions::Exception()));
    ns_info_ =
        new NameSpaceInfo(namespace_id_, cluster_id_, bpid_, create_time_,
                          build_version_, software_version_);

    store_ = new ChunkServerStore(&cs_config_, cs_env_);
    e = store_->InitStorage(dn_, &disk_id_conf_map_);
    EXPECT_TRUE(e.OK());

    e = store_->AddDirAndBlock(ns_info_);
    EXPECT_TRUE(e.OK());

    std::string ip = "127.0.0.1";
    int port =
        5190;  // To run tests in parallel, the port is guaranteed to be unique
    dn_stream_server_ = new DataNodeStreamServer(ip, port, dn_);
    EXPECT_TRUE(dn_stream_server_->Init());
    EXPECT_TRUE(dn_stream_server_->Start());
  }

  void TearDown() override {
    delete store_;
    cs_env_->Stop();
    byte::DeleteOptions delete_options;
    delete_options.recursively_ = true;
    byte::LocalFileSystem local_fs;
    std::string work_dir = "./ChunkServerStoreAdvancedTestDir/";
    local_fs.DeleteDir(std::string(work_dir), delete_options);
    dn_stream_server_->Stop();
    dn_stream_server_->Join();
    delete dn_stream_server_;
    delete cs_env_;
    delete dn_;
    delete ns_info_;
  }

  static void SetUpTestCase() {}
  static void TearDownTestCase() {
    byte::GetLoggingSystem()->Shutdown();
  }

  void CreateChunk(uint32_t disk_id, ExtendedBlock* block,
                   PlacementStorageType type = PLM_STORAGE_ANY) {
    uint32_t rtime = type == PLM_STORAGE_ANY ? -1 : 1;
    std::unique_ptr<CreateOper> oper(new CreateOper(
        PRIORITY_REAL_TIME, block, 5000000, ChunkType::TYPE_REPLICATED_CHUNK, 0,
        false, type, rtime));
    SYNC_HANDLE_OPER(CreateChunk)
  }

  void WriteChunk(uint32_t disk_id, ExtendedBlock* block, char* data,
                  uint32_t length, uint32_t offset) {
    std::unique_ptr<WriteOper> oper(new WriteOper(
        PRIORITY_ELASTIC, block, 5000000, length, offset, data, false));
    SYNC_HANDLE_OPER(WriteChunk)
  }

  void ReadChunk(const uint32_t disk_id, ExtendedBlock* block, uint32_t length,
                 uint32_t offset, std::string* output,
                 PlacementStorageType type = PLM_STORAGE_ANY) {
    std::unique_ptr<ReadOper> oper(new ReadOper(
        PRIORITY_ELASTIC, block, 5000000, length, offset, false, type));
    SYNC_HANDLE_OPER(ReadChunk)
    *output = oper->controller_.response_attachment().to_string();
  }

  void FreezeChunk(uint32_t disk_id, ExtendedBlock* block, uint32_t length,
                   PlacementStorageType type = PLM_STORAGE_ANY) {
    std::unique_ptr<FreezeOper> oper(
        new FreezeOper(PRIORITY_ELASTIC, block, 5000000, length, false, type));
    SYNC_HANDLE_OPER(FreezeChunk)
  }

  void SetChunkAttr(const uint32_t disk_id, ExtendedBlock* block, char* meta,
                    PlacementStorageType type = PLM_STORAGE_ANY) {
    std::unique_ptr<XAttrSetOper> oper(
        new XAttrSetOper(PRIORITY_ELASTIC, block, 5000000, meta, type));
    SYNC_HANDLE_OPER(SetXATTRChunk)
  }

  void GetChunkAttr(const uint32_t disk_id, ExtendedBlock* block,
                    BlockMeta* meta,
                    PlacementStorageType type = PLM_STORAGE_ANY) {
    std::unique_ptr<XAttrGetOper> oper(
        new XAttrGetOper(PRIORITY_ELASTIC, block, 5000000, type));
    SYNC_HANDLE_OPER(GetXATTRChunk)
    oper->response_.xattr().copy(reinterpret_cast<char*>(meta), XATTR_BYTES);
  }

  Errorcode GetChunkMeta(const uint32_t disk_id, ExtendedBlock* block,
                         CSChunkMeta* chunk_meta,
                         PlacementStorageType type = PLM_STORAGE_ANY) {
    std::unique_ptr<GetMetaOper> oper(new GetMetaOper(block, type));
    SYNC_HANDLE_OPER(GetChunkMeta)
    if (oper->response_.chunk_metas(0).error_code() != BYTESTORE_OK) {
      return IntToErrorcode(oper->response_.chunk_metas(0).error_code());
    }
    chunk_meta->Deserialize(oper->response_.chunk_metas(0).chunk_meta());
    return BYTESTORE_OK;
  }

  Errorcode GetFreeSizeInBytes(uint32_t disk_id, uint64_t* free_size_in_bytes) {
    return dn_interface_->GetFreeSizeInBytes(disk_id, free_size_in_bytes);
  }

  void IOOperDone(byte::CountDownLatch* latch) {
    latch->CountDown();
  }

 public:
  ChunkServerStore* store_;
  Env* cs_env_;
  ChunkServerConfig cs_config_;
  NameSpaceInfo* ns_info_;
  DiskIdConfMap disk_id_conf_map_;
  std::unique_ptr<DatanodeInterface> dn_interface_;
  MockDataNode* dn_;
  std::string datanode_uuid_;
  int namespace_id_ = 1004580134;
  std::string cluster_id_ = "CID-9a628355-6954-473b-a66c-d34d7c2b3805";
  uint64_t create_time_ = 1546064706393;
  std::string build_version_ = "2.6.0";
  std::string software_version_ = "2.6.3";
  std::string bpid_ = "BP-2026362776-*************-1546064706393";
  DataNodeStreamServer* dn_stream_server_;
};

TEST_F(ChunkServerStoreTests, UpdateMigratedReplica) {
  std::unordered_map<uint32_t, StorageDirectory>* diskid_sd_map =
      store_->GetDiskIdSdMap();
  auto iter = diskid_sd_map->find(3);
  EXPECT_TRUE(iter != diskid_sd_map->end());
  std::string storage_uuid3 = iter->second.storage_uuid_;

  // simulate the block 1 migrated from ssd 3 to hdd 1
  // whose replica in memory is still in ssd 3
  std::shared_ptr<ReplicaInfo> replica_info = store_->GetReplica(bpid_, 1);
  EXPECT_TRUE(replica_info != nullptr);
  EXPECT_EQ(replica_info->GetDiskId(), 1);
  std::shared_ptr<FinalizedReplica> new_replica =
      std::make_shared<FinalizedReplica>(replica_info->GetBlock(),
                                         storage_uuid3,
                                         replica_info->IsOnTransientStorage(),
                                         replica_info->ChecksumEnabled(), 3);
  store_->GetVolumeMap()->Add(replica_info->GetBlockPoolID(), new_replica);
  std::shared_ptr<ReplicaInfo> replica_info2 = store_->GetReplica(bpid_, 1);
  EXPECT_TRUE(replica_info2 != nullptr);
  EXPECT_EQ(replica_info2->GetDiskId(), 3);

  BlockLock::XLock* xlock = store_->GetRecoveryLock()->Get(bpid_, 1);
  EXPECT_TRUE(xlock->TryLock());
  ExtendedBlock block0(bpid_, 1, 409600, 1546064706393);
  auto e0 = store_->UpdateMigratedReplica(&block0, 1);
  EXPECT_FALSE(e0.OK());
  EXPECT_TRUE(e0.GetE() == exceptions::kIOException);
  EXPECT_TRUE(e0.GetMessage().find("in recovery") != std::string::npos);
  xlock->UnLock();
  store_->GetRecoveryLock()->Release(xlock);

  ExtendedBlock block1(bpid_, 4, 409600, 1546064706393);
  auto e1 = store_->UpdateMigratedReplica(&block1, 1);
  EXPECT_FALSE(e1.OK());
  EXPECT_TRUE(e1.GetE() == exceptions::kReplicaNotFoundException);

  // test RUR replica
  ExtendedBlock block2(bpid_, 2, 409600, 1546064706393);
  std::shared_ptr<ReplicaUnderRecovery> rur;
  std::shared_ptr<ReplicaInfo> replica0 = store_->GetReplica(bpid_, 2);
  EXPECT_TRUE(replica0 != nullptr);
  EXPECT_EQ(replica0->GetDiskId(), 2);
  auto e = ReplicaUnderRecovery::New(replica0, 1546064706394, &rur);
  EXPECT_TRUE(e.OK());
  store_->GetVolumeMap()->Add(bpid_, rur);
  replica0 = store_->GetReplica(bpid_, 2);
  EXPECT_TRUE(replica0 != nullptr);
  EXPECT_EQ(replica0->GetStateString(), "RUR");
  auto e2 = store_->UpdateMigratedReplica(&block2, 1);
  EXPECT_TRUE(e2.OK());
  replica0 = store_->GetReplica(bpid_, 2);
  EXPECT_TRUE(replica0 != nullptr);
  EXPECT_EQ(replica0->GetDiskId(), 1);

  ExtendedBlock block4(bpid_, 1, 409600, 1546064706393);
  auto e4 = store_->UpdateMigratedReplica(&block4, 5);
  EXPECT_FALSE(e4.OK());
  EXPECT_TRUE(e4.GetE() == exceptions::kIOException);
  EXPECT_TRUE(e4.GetMessage().find(
                  "cannot find corresponding disk_id in diskid_sd_map") !=
              std::string::npos);

  ExtendedBlock block5(bpid_, 1, 409600, 1546064706393);
  auto e5 = store_->UpdateMigratedReplica(&block5, 2);
  EXPECT_TRUE(e5.OK());
  std::shared_ptr<ReplicaInfo> replica1 = store_->GetReplica(bpid_, 1);
  EXPECT_TRUE(replica1 != nullptr);
  EXPECT_EQ(replica1->GetDiskId(), 2);

  ExtendedBlock block6(bpid_, 1, 409600, 1546064706393);
  auto e6 = store_->UpdateMigratedReplica(&block6, 2);
  EXPECT_TRUE(e6.OK());
  std::string msg = e6.GetMessage();
  EXPECT_FALSE(msg.empty());
  EXPECT_TRUE(msg.find("src_storage_uuid equals to dst_storage_uuid") !=
              std::string::npos);
  std::shared_ptr<ReplicaInfo> replica2 = store_->GetReplica(bpid_, 1);
  EXPECT_TRUE(replica2 != nullptr);
  EXPECT_EQ(replica2->GetDiskId(), 2);
}

TEST_F(ChunkServerStoreTests, MigrationScanner) {
  FLAGS_bytestore_chunkserver_tiering_migration_sync_delay_ms = 0;
  const DiskId ssd_disk_id = 3;
  std::shared_ptr<ReplicaInfo> replica = store_->GetReplica(bpid_, 11);
  EXPECT_TRUE(replica != nullptr);
  EXPECT_EQ(replica->GetDiskId(), ssd_disk_id);
  Disk* ssd_disk = cs_env_->GetDisk(ssd_disk_id);
  ChunkIdMeta chunk_Id_meta;
  ExtendedBlock block(bpid_, 11, 409600, 1546064706393);
  EXPECT_TRUE(block.GetChunkIdMeta(&chunk_Id_meta));
  ChunkId* chunk_id = chunk_Id_meta.ToChunkId();
  ChunkStore* ssd_chunk_store = ssd_disk->GetChunkStore();
  StorageMeta storage_meta;
  EXPECT_EQ(BYTESTORE_OK,
            ssd_chunk_store->GetStorageMeta(*chunk_id, &storage_meta));
  EXPECT_EQ(storage_meta.meta_.frozen_, 1);
  EXPECT_EQ(storage_meta.meta_.storage_type_,
            PLM_STORAGE_TIERED_NVME_SSD_HDD_PROTO);
  EXPECT_EQ(storage_meta.resident_seconds_, 1);
  EXPECT_EQ(storage_meta.meta_.length_, 409600);
  byte::ThisThread::SleepInSeconds(2);

  std::shared_ptr<MigrationScanner> migration_scanner =
      std::make_shared<MigrationScanner>(store_);

  // let Migration Scanner execute immediately
  FLAGS_bytestore_hdfs_migration_scanner_interval_time_ms = 1000;
  migration_scanner->Start();

  byte::ThisThread::SleepInSeconds(2);
  replica = store_->GetReplica(bpid_, 11);
  EXPECT_TRUE(replica != nullptr);
  EXPECT_LE(replica->GetDiskId(), 2);

  CSChunkMeta chunk_meta;
  EXPECT_EQ(GetChunkMeta(replica->GetDiskId(), &block, &chunk_meta),
            BYTESTORE_OK);
  migration_scanner->Stop();
}

TEST_F(ChunkServerStoreTests, UpdateMigratedReplicaThread_unique) {
  std::unique_ptr<UpdateMigratedReplicaThread> update_migrated_replica_thread;
  update_migrated_replica_thread.reset(new UpdateMigratedReplicaThread(store_));
  EXPECT_TRUE(update_migrated_replica_thread->IsStopped());

  std::unordered_map<uint32_t, StorageDirectory>* diskid_sd_map =
      store_->GetDiskIdSdMap();
  auto iter = diskid_sd_map->find(3);
  EXPECT_TRUE(iter != diskid_sd_map->end());
  std::string storage_uuid3 = iter->second.storage_uuid_;

  // simulate the block 1 migrated from ssd 3 to hdd 1
  // whose replica in memory is still in ssd 3
  std::shared_ptr<ReplicaInfo> replica_info = store_->GetReplica(bpid_, 1);
  EXPECT_TRUE(replica_info != nullptr);
  EXPECT_EQ(replica_info->GetDiskId(), 1);
  std::shared_ptr<FinalizedReplica> new_replica =
      std::make_shared<FinalizedReplica>(replica_info->GetBlock(),
                                         storage_uuid3,
                                         replica_info->IsOnTransientStorage(),
                                         replica_info->ChecksumEnabled(), 3);
  store_->GetVolumeMap()->Add(replica_info->GetBlockPoolID(), new_replica);
  std::shared_ptr<ReplicaInfo> replica_info2 = store_->GetReplica(bpid_, 1);
  EXPECT_TRUE(replica_info2 != nullptr);
  EXPECT_EQ(replica_info2->GetDiskId(), 3);

  ExtendedBlock* block1 = new ExtendedBlock(bpid_, 1, 409600, 1546064706393);
  ExtendedBlock* block2 = new ExtendedBlock(bpid_, 1, 409600, 1546064706393);
  std::shared_ptr<UpdateMigratedReplicaOper> update_oper1(
      new UpdateMigratedReplicaOper(block1, 1));
  std::shared_ptr<UpdateMigratedReplicaOper> update_oper2(
      new UpdateMigratedReplicaOper(block2, 1));
  bool res1 = update_migrated_replica_thread->AddOper(update_oper1);
  bool res2 = update_migrated_replica_thread->AddOper(update_oper2);
  EXPECT_TRUE(res1);
  EXPECT_FALSE(res2);
  EXPECT_EQ(update_migrated_replica_thread->GetQueueSize(), 1);

  EXPECT_TRUE(update_migrated_replica_thread->Start());
  // sleep for aync update_migrated_replica_thread_
  // to update the replica and notify namenode
  byte::ThisThread::SleepInSeconds(1);
  std::shared_ptr<ReplicaInfo> replica = store_->GetReplica(bpid_, 1);
  EXPECT_TRUE(replica != nullptr);
  EXPECT_EQ(replica->GetDiskId(), 1);
  delete block1;
  delete block2;
}

TEST_F(ChunkServerStoreTests, CheckAndUpdate) {
  std::shared_ptr<ReplicaInfo> replica = store_->GetReplica(bpid_, 1);
  EXPECT_TRUE(replica != nullptr);
  EXPECT_EQ(replica->GetDiskId(), 1);
  ExtendedBlock block1(bpid_, 1, 409600, 1546064706393);
  // test replica exists
  auto e1 = store_->CheckAndUpdate(&block1, ReplicaState::FINALIZED, 1, false);
  EXPECT_TRUE(e1.OK());
  replica = store_->GetReplica(bpid_, 1);
  EXPECT_TRUE(replica != nullptr);
  EXPECT_EQ(replica->GetDiskId(), 1);

  // remove FINALIZED replica
  store_->GetVolumeMap()->Remove(block1.GetBlockPoolID(), block1.GetBlockID());
  replica = store_->GetReplica(bpid_, 1);
  EXPECT_EQ(replica, nullptr);

  auto e2 = store_->CheckAndUpdate(&block1, ReplicaState::FINALIZED, 5, false);
  EXPECT_FALSE(e2.OK());
  EXPECT_TRUE(e2.GetE() == exceptions::kIOException);
  EXPECT_TRUE(e2.GetMessage().find(
                  "cannot find corresponding disk_id in diskid_sd_map") !=
              std::string::npos);
  // test CheckAndUpdate for missing FINALIZED replica
  auto e3 = store_->CheckAndUpdate(&block1, ReplicaState::FINALIZED, 1, false);
  EXPECT_TRUE(e3.OK());
  replica = store_->GetReplica(bpid_, 1);
  EXPECT_TRUE(replica != nullptr);
  EXPECT_EQ(replica->GetDiskId(), 1);
  EXPECT_EQ(replica->GetStateString(), "FINALIZED");
  // test CheckAndUpdate for missing RBW replica
  std::shared_ptr<ReplicaInfo> replica2 = store_->GetReplica(bpid_, 2);
  EXPECT_TRUE(replica2 != nullptr);
  EXPECT_EQ(replica2->GetDiskId(), 2);
  ExtendedBlock block2(bpid_, 2, 409600, 1546064706393);
  store_->GetVolumeMap()->Remove(block2.GetBlockPoolID(), block2.GetBlockID());
  replica2 = store_->GetReplica(bpid_, 2);
  EXPECT_EQ(replica2, nullptr);
  auto e4 = store_->CheckAndUpdate(&block2, ReplicaState::RBW, 2, false);
  EXPECT_TRUE(e4.OK());
  replica2 = store_->GetReplica(bpid_, 2);
  // CheckAndUpdate only cares about blocks in FINALIZED status
  EXPECT_EQ(replica2, nullptr);

  // create TEMPORARY replica
  ExtendedBlock block4(bpid_, 4, 409600, 1546064706393);
  std::shared_ptr<ReplicaInPipeline> replica3;
  auto e5 = store_->CreateTemporary(StorageType::DISK, &block4, &replica3);
  EXPECT_TRUE(e1.OK());

  EXPECT_EQ(replica3->GetStateString(), "TEMPORARY");
  EXPECT_EQ(replica3->GetNumBytes(), 0);
  EXPECT_EQ(replica3->GetBytesAcked(), 0);
  EXPECT_EQ(replica3->GetBytesOnDisk(), 0);
  EXPECT_EQ(replica3->GetGS(), 1546064706393);
  EXPECT_FALSE(replica3->IsOnTransientStorage());

  std::shared_ptr<ReplicaInfo> replica4 = store_->GetReplica(bpid_, 4);
  EXPECT_TRUE(replica4 != nullptr);
  EXPECT_EQ(replica4->GetStateString(), "TEMPORARY");
  EXPECT_EQ(replica4->GetNumBytes(), 0);
  EXPECT_EQ(replica4->GetGS(), 1546064706393);
  EXPECT_FALSE(replica4->IsOnTransientStorage());

  uint16_t disk_id = replica4->GetDiskId();
  LOG(INFO) << "disk_id: " << disk_id;
  BlockMeta meta;
  GetChunkAttr(disk_id, &block4, &meta);
  EXPECT_EQ(meta.gs_, 1546064706393);
  EXPECT_EQ(meta.tmp_, 1);
  EXPECT_EQ(meta.fin_, 0);
  CSChunkMeta chunk_meta;
  EXPECT_EQ(GetChunkMeta(disk_id, &block4, &chunk_meta), BYTESTORE_OK);

  store_->GetVolumeMap()->Remove(block4.GetBlockPoolID(), block4.GetBlockID());
  replica4 = store_->GetReplica(bpid_, 4);
  EXPECT_EQ(replica4, nullptr);
  auto e6 =
      store_->CheckAndUpdate(&block4, ReplicaState::TEMPORARY, disk_id, false);
  EXPECT_TRUE(e6.OK());
  replica4 = store_->GetReplica(bpid_, 4);
  EXPECT_EQ(replica4, nullptr);
  // bytestore_chunkserver_delete_manager_interval_time_ms decide when to delete
  // file on the disk
  byte::ThisThread::SleepInSeconds(2);
  // CheckAndUpdate only cares about blocks in FINALIZED status
  EXPECT_EQ(GetChunkMeta(disk_id, &block4, &chunk_meta), BYTESTORE_OK);

  // test chunk delete failed when migrating from ssd to hdd
  ExtendedBlock block11(bpid_, 11, 409600, 1546064706393);
  std::shared_ptr<ReplicaInfo> replica11 = store_->GetReplica(bpid_, 11);
  EXPECT_TRUE(replica11 != nullptr);
  EXPECT_EQ(replica11->GetDiskId(), 3);
  EXPECT_EQ(replica11->GetStateString(), "FINALIZED");
  replica = store_->GetReplica(bpid_, 1);
  EXPECT_TRUE(replica != nullptr);
  EXPECT_EQ(replica->GetDiskId(), 1);
  std::shared_ptr<ReplicaInfo> replica11_2 = std::make_shared<FinalizedReplica>(
      replica11->GetBlock(), replica->GetStorageUuid(), false,
      replica11->ChecksumEnabled(), 1);
  store_->GetVolumeMap()->Add(replica11_2->GetBlockPoolID(), replica11_2);
  replica11 = store_->GetReplica(bpid_, 11);
  EXPECT_TRUE(replica11 != nullptr);
  EXPECT_EQ(replica11->GetDiskId(), 1);
  EXPECT_EQ(replica11->GetStateString(), "FINALIZED");
  auto e7 = store_->CheckAndUpdate(&block11, ReplicaState::FINALIZED, 3, false);
  EXPECT_TRUE(e7.OK());
  byte::ThisThread::SleepInSeconds(2);
  EXPECT_EQ(GetChunkMeta(3, &block11, &chunk_meta),
            BYTESTORE_ERR_CHUNKSERVER_CHUNK_NOT_FOUND);

  // test corrupt chunk with replica exists
  ChunkIdMeta chunk_Id_meta;
  block1.GetChunkIdMeta(&chunk_Id_meta);
  store_->GetDisk(1)->GetChunkStore()->SetChunkCorrupted(
      *chunk_Id_meta.ToChunkId(), false);
  replica = store_->GetReplica(bpid_, 1);
  EXPECT_TRUE(replica != nullptr);
  EXPECT_EQ(replica->GetDiskId(), 1);
  auto e8 = store_->CheckAndUpdate(&block1, ReplicaState::FINALIZED, 1, true);
  EXPECT_TRUE(e8.OK());

  // test corrupt chunk with replica not exists
  store_->GetVolumeMap()->Remove(block1.GetBlockPoolID(), block1.GetBlockID());
  replica = store_->GetReplica(bpid_, 1);
  EXPECT_EQ(replica, nullptr);
  auto e9 = store_->CheckAndUpdate(&block1, ReplicaState::FINALIZED, 1, true);
  EXPECT_TRUE(e9.OK());
  replica = store_->GetReplica(bpid_, 1);
  EXPECT_EQ(replica, nullptr);
}

TEST_F(ChunkServerStoreTests, DirectoryScannerSimple) {
  std::shared_ptr<ReplicaInfo> replica = store_->GetReplica(bpid_, 1);
  EXPECT_TRUE(replica != nullptr);
  EXPECT_EQ(replica->GetDiskId(), 1);
  ExtendedBlock block1(bpid_, 1, 409600, 1546064706393);
  // remove FINALIZED replica
  store_->GetVolumeMap()->Remove(block1.GetBlockPoolID(), block1.GetBlockID());
  replica = store_->GetReplica(bpid_, 1);
  EXPECT_EQ(replica, nullptr);

  std::shared_ptr<DirectoryScanner> directory_scanner =
      std::make_shared<DirectoryScanner>(store_);

  // let Directory Scanner execute immediately
  FLAGS_bytestore_hdfs_directory_scanner_interval_time_ms = 1000;
  directory_scanner->Start();

  byte::ThisThread::SleepInSeconds(2);
  replica = store_->GetReplica(bpid_, 1);
  EXPECT_TRUE(replica != nullptr);
  EXPECT_EQ(replica->GetDiskId(), 1);

  directory_scanner->Stop();
}

TEST_F(ChunkServerStoreTests, ReadWriteDiskChecksum) {
  using ChecksumPtr = std::unique_ptr<DataChecksum>;
  // checksum disabled by FLAGS
  FLAGS_bytestore_hdfs_checksum_mode = 0;
  ExtendedBlock block(bpid_, 4, 409600, 1546064706393);
  std::shared_ptr<ReplicaInPipeline> replica;
  auto e1 = store_->CreateRbw(StorageType::DISK, &block, false, &replica);
  EXPECT_TRUE(e1.OK());
  EXPECT_FALSE(replica->ChecksumEnabled());

  ChecksumPtr checksum2(
      DataChecksum::NewDataChecksum(DataChecksum::TYPE_CRC32C, 512));
  // do nothing since checksum disabled
  auto e2 = store_->WriteBlockMetaHeader(&block, checksum2.get(),
                                         StorageType::DISK, PLM_STORAGE_ANY);
  EXPECT_TRUE(e2.OK());

  DataChecksum* disk_csum;
  auto e3 = store_->ReadDiskChecksum(&block, &disk_csum);
  EXPECT_TRUE(e3.OK());
  ChecksumPtr checksum3(
      DataChecksum::NewDataChecksum(DataChecksum::TYPE_NULL, 512));
  EXPECT_TRUE(disk_csum->Equal(checksum3.get()));
  delete disk_csum;

  // checksum enabled in tests setup
  ExtendedBlock block2(bpid_, 1, 409600, 1546064706393);
  std::unique_ptr<ExtendedBlock> cb2(block2.GetChecksumBlock());
  BlockMeta meta2;
  GetChunkAttr(1, cb2.get(), &meta2);
  EXPECT_EQ(meta2.checksum_type_, 2);
  EXPECT_EQ(meta2.bytes_per_checksum_, 512);

  auto e5 = store_->ReadDiskChecksum(&block2, &disk_csum);
  EXPECT_TRUE(e5.OK());
  EXPECT_TRUE(disk_csum->Equal(checksum2.get()));
  delete disk_csum;

  // checksum enabled by FLAGS
  FLAGS_bytestore_hdfs_checksum_mode = 2;
  ExtendedBlock block3(bpid_, 5, 409600, 1546064706393);
  auto e6 = store_->CreateRbw(StorageType::DISK, &block3, false, &replica);
  EXPECT_TRUE(e6.OK());
  EXPECT_TRUE(replica->ChecksumEnabled());

  auto e7 = store_->WriteBlockMetaHeader(&block3, checksum2.get(),
                                         StorageType::DISK, PLM_STORAGE_ANY);
  EXPECT_TRUE(e7.OK());
  std::unique_ptr<ExtendedBlock> cb3(block3.GetChecksumBlock());
  BlockMeta meta3;
  GetChunkAttr(replica->GetDiskId(), cb3.get(), &meta3);
  EXPECT_EQ(meta3.checksum_type_, 2);
  EXPECT_EQ(meta3.bytes_per_checksum_, 512);

  auto e8 = store_->ReadDiskChecksum(&block3, &disk_csum);
  EXPECT_TRUE(e8.OK());
  EXPECT_TRUE(disk_csum->Equal(checksum2.get()));
  delete disk_csum;

  // bytestore_hdfs_checksum_mode should reset to default
  FLAGS_bytestore_hdfs_checksum_mode = 0;
}

TEST_F(ChunkServerStoreTests, ReadWriteChecksum) {
  FLAGS_bytestore_hdfs_checksum_mode = 2;
  using ChecksumPtr = std::unique_ptr<DataChecksum>;
  ExtendedBlock block(bpid_, 4, 409600, 1546064706393);
  std::shared_ptr<ReplicaInPipeline> replica;
  auto e1 = store_->CreateRbw(StorageType::DISK, &block, false, &replica);
  EXPECT_TRUE(e1.OK());
  EXPECT_TRUE(replica->ChecksumEnabled());

  std::string s = "hello world!";
  DataChecksum* crc =
      DataChecksum::NewDataChecksum(DataChecksum::TYPE_CRC32C, 512);
  crc->Update(reinterpret_cast<const uint8_t*>(s.data()), 0, s.length());
  int checksum_size = 4;
  auto chunk = new io::IOChunk(checksum_size);
  chunk->WriteFixed32BE(crc->GetValue());

  // write without create
  auto e2 = store_->WriteChecksum(&block, chunk, 0, checksum_size, false);
  EXPECT_FALSE(e2.OK());

  ChecksumPtr checksum(
      DataChecksum::NewDataChecksum(DataChecksum::TYPE_CRC32C, 512));
  auto e3 = store_->WriteBlockMetaHeader(&block, checksum.get(),
                                         StorageType::DISK, PLM_STORAGE_ANY);
  EXPECT_TRUE(e3.OK());

  e2 = store_->WriteChecksum(&block, chunk, 0, checksum_size, false);
  EXPECT_TRUE(e2.OK());

  // overwrite
  chunk->Reset();
  chunk->WriteFixed32BE(crc->GetValue());
  e2 = store_->WriteChecksum(&block, chunk, 0, checksum_size, false);
  EXPECT_TRUE(e2.OK());

  // out of range
  e2 = store_->WriteChecksum(&block, chunk, 10, checksum_size, false);
  EXPECT_FALSE(e2.OK());

  auto chunk2 = new io::IOChunk(checksum_size);
  auto e4 = store_->ReadChecksum(&block, chunk2, 0, checksum_size);
  EXPECT_TRUE(e4.OK());
  EXPECT_EQ(0x49CB5777u, chunk2->ReadFixed32BE());

  // out of range
  e4 = store_->ReadChecksum(&block, chunk2, 5, checksum_size);
  EXPECT_FALSE(e4.OK());

  // bytestore_hdfs_checksum_mode should reset to default
  FLAGS_bytestore_hdfs_checksum_mode = 0;
  io::IOChunk::Destroy(chunk);
  io::IOChunk::Destroy(chunk2);
  delete crc;
}

TEST_F(ChunkServerStoreTests, Append) {
  std::shared_ptr<ReplicaInPipeline> replica;
  ExtendedBlock block5(bpid_, 1, 409600, 1546064706393);
  auto e1 = store_->Append(&block5, 1546064706395, 409601, &replica);
  LOG(INFO) << "append fail: " << e1.GetMessage();
  EXPECT_FALSE(e1.OK());

  ExtendedBlock block(bpid_, 1, 409600, 1546064706393);
  auto e2 = store_->Append(&block, 1546064706395, 409600, &replica);
  EXPECT_TRUE(e2.OK());
  EXPECT_EQ(replica->GetStateString(), "RBW");
  EXPECT_EQ(replica->GetNumBytes(), 409600);
  EXPECT_EQ(replica->GetGS(), 1546064706395);
  EXPECT_FALSE(replica->IsOnTransientStorage());

  std::shared_ptr<ReplicaInfo> replica1 = store_->GetReplica(bpid_, 1);
  EXPECT_TRUE(replica1 != nullptr);
  EXPECT_EQ(replica1->GetStateString(), "RBW");
  EXPECT_EQ(replica1->GetNumBytes(), 409600);
  EXPECT_EQ(replica1->GetGS(), 1546064706395);
  EXPECT_FALSE(replica1->IsOnTransientStorage());

  BlockMeta meta;
  GetChunkAttr(1, &block, &meta);
  EXPECT_EQ(meta.gs_, 1546064706395);
  EXPECT_EQ(meta.tmp_, 0);
  EXPECT_EQ(meta.fin_, 0);

  ExtendedBlock block2(bpid_, 4, 409600, 1546064706393);
  auto e3 = store_->Append(&block2, 1546064706395, 409600, &replica);
  LOG(INFO) << "append fail: " << e3.GetMessage();
  EXPECT_FALSE(e3.OK());

  ExtendedBlock block3(bpid_, 1, 409600, 1546064706393);
  auto e4 = store_->Append(&block3, 1546064706391, 409600, &replica);
  LOG(INFO) << "append fail: " << e4.GetMessage();
  EXPECT_FALSE(e4.OK());

  ExtendedBlock block4(bpid_, 2, 409600, 1546064706393);
  auto e5 = store_->Append(&block4, 1546064706395, 409600, &replica);
  LOG(INFO) << "append fail: " << e5.GetMessage();
  EXPECT_FALSE(e5.OK());

  // write block
  static const uint32_t k_buf_size = 4096;
  char* data = new char[k_buf_size];
  std::unique_ptr<char[]> scoped_data(data);
  for (uint32_t i = 0; i < k_buf_size; ++i) {
    data[i] = i % 32 + 'A';
  }
  io::IOChunk* chunk = new io::IOChunk(4096);
  chunk->WriteBytes(reinterpret_cast<uint8_t*>(data), 4096);
  auto e6 = store_->WriteBlock(&block, chunk, 409600, 4096, false);
  EXPECT_TRUE(e6.OK());
  io::IOChunk::Destroy(chunk);

  CSChunkMeta chunk_meta;
  EXPECT_EQ(GetChunkMeta(1, &block, &chunk_meta), BYTESTORE_OK);
  EXPECT_EQ(chunk_meta.length_, 413696);
}

TEST_F(ChunkServerStoreTests, Append_Frozen) {
  const DiskId ssd_disk_id = 3;
  std::shared_ptr<ReplicaInfo> replica = store_->GetReplica(bpid_, 11);
  EXPECT_TRUE(replica != nullptr);
  EXPECT_EQ(replica->GetDiskId(), ssd_disk_id);
  Disk* ssd_disk = cs_env_->GetDisk(ssd_disk_id);
  ChunkIdMeta chunk_Id_meta;
  ExtendedBlock block(bpid_, 11, 409600, 1546064706393);
  EXPECT_TRUE(block.GetChunkIdMeta(&chunk_Id_meta));
  ChunkId* chunk_id = chunk_Id_meta.ToChunkId();
  ChunkStore* ssd_chunk_store = ssd_disk->GetChunkStore();
  StorageMeta storage_meta;
  EXPECT_EQ(BYTESTORE_OK,
            ssd_chunk_store->GetStorageMeta(*chunk_id, &storage_meta));
  EXPECT_EQ(storage_meta.meta_.frozen_, 1);
  EXPECT_EQ(storage_meta.meta_.storage_type_,
            PLM_STORAGE_TIERED_NVME_SSD_HDD_PROTO);
  EXPECT_EQ(storage_meta.resident_seconds_, 1);
  EXPECT_EQ(storage_meta.meta_.length_, 409600);

  std::shared_ptr<ReplicaInPipeline> replica1;
  auto e = store_->Append(&block, 1546064706395, 409600, &replica1);
  EXPECT_FALSE(e.OK());
  EXPECT_EQ(e.GetE(), exceptions::E::kReplicaFrozenException);
}

TEST_F(ChunkServerStoreTests, initReplicaRecovery) {
  // create temp block
  ExtendedBlock block(bpid_, 4, 4096, 1546064706393);
  std::shared_ptr<ReplicaInPipeline> replica;
  auto e1 = store_->CreateTemporary(StorageType::DISK, &block, &replica);
  EXPECT_TRUE(e1.OK());

  // write block
  static const uint32_t k_buf_size = 4096;
  char* data = new char[k_buf_size];
  std::unique_ptr<char[]> scoped_data(data);
  for (uint32_t i = 0; i < k_buf_size; ++i) {
    data[i] = i % 32 + 'A';
  }
  io::IOChunk* chunk = new io::IOChunk(4096);
  chunk->WriteBytes(reinterpret_cast<uint8_t*>(data), 4096);
  auto e2 = store_->WriteBlock(&block, chunk, 0, 4096, false);
  EXPECT_TRUE(e2.OK());
  io::IOChunk::Destroy(chunk);
  replica->SetNumBytes(4096);
  replica->SetDiskDataLen(4096);

  // test InitReplicaRecovery of temp replica
  ReplicaRecoveryInfo* rinfo;
  std::vector<DatanodeInfo*> locs;
  ExtendedBlock* block2 = new ExtendedBlock(bpid_, 4, 4096, 1546064706393);
  RecoveringBlock rblock2(block2, locs, 1546064706395);
  auto e3 = store_->InitReplicaRecovery(&rblock2, &rinfo);
  LOG(INFO) << "initReplicaRecovery fail, exception: " << e3.GetMessage();
  EXPECT_FALSE(e3.OK());

  // test Replica->GetGS() < blk->GetGS()
  ExtendedBlock* block3 = new ExtendedBlock(bpid_, 4, 4096, 1546064706395);
  RecoveringBlock rblock3(block3, locs, 1546064706395);
  auto e4 = store_->InitReplicaRecovery(&rblock3, &rinfo);
  LOG(INFO) << "initReplicaRecovery fail, exception: " << e4.GetMessage();
  EXPECT_FALSE(e4.OK());

  // test recovery_id too small
  ExtendedBlock* block4 = new ExtendedBlock(bpid_, 4, 4096, 1546064706393);
  RecoveringBlock rblock4(block4, locs, 1546064706391);
  auto e5 = store_->InitReplicaRecovery(&rblock4, &rinfo);
  LOG(INFO) << "initReplicaRecovery fail, exception: " << e5.GetMessage();
  EXPECT_FALSE(e5.OK());

  // test block not exist
  ExtendedBlock* block5 = new ExtendedBlock(bpid_, 5, 4096, 1546064706393);
  RecoveringBlock rblock5(block5, locs, 1546064706395);
  auto e6 = store_->InitReplicaRecovery(&rblock5, &rinfo);
  LOG(INFO) << "initReplicaRecovery fail, exception: " << e6.GetMessage();
  EXPECT_TRUE(e6.OK());

  // test InitReplicaRecovery of finalized replica
  ExtendedBlock* block1 = new ExtendedBlock(bpid_, 1, 409600, 1546064706393);
  RecoveringBlock rblock(block1, locs, 1546064706395);
  auto e7 = store_->InitReplicaRecovery(&rblock, &rinfo);
  EXPECT_TRUE(e7.OK());
  EXPECT_EQ(rinfo->GetBlockID(), 1);
  EXPECT_EQ(rinfo->GetGS(), 1546064706393);
  EXPECT_EQ(rinfo->GetNumBytes(), 409600);
  EXPECT_EQ(rinfo->GetOriginalReplicaState(), ReplicaState::FINALIZED);
  delete rinfo;

  std::shared_ptr<ReplicaInfo> replica_new = store_->GetReplica(bpid_, 1);
  EXPECT_TRUE(replica_new != nullptr);
  EXPECT_EQ(replica_new->GetStateString(), "RUR");
  EXPECT_EQ(replica_new->GetNumBytes(), 409600);
  EXPECT_EQ(replica_new->GetGS(), 1546064706393);
  EXPECT_FALSE(replica_new->IsOnTransientStorage());
  EXPECT_EQ(replica_new->GetDiskId(), 1);

  std::shared_ptr<ReplicaUnderRecovery> replica_rur =
      std::dynamic_pointer_cast<ReplicaUnderRecovery>(replica_new);
  EXPECT_TRUE(replica_rur != nullptr);
  EXPECT_EQ(replica_rur->GetRecoveryID(), 1546064706395);
  std::shared_ptr<ReplicaInfo> replica_original =
      replica_rur->GetOriginalReplica();
  EXPECT_EQ(replica_original->GetStateString(), "FINALIZED");
  EXPECT_EQ(replica_original->GetNumBytes(), 409600);
  EXPECT_EQ(replica_original->GetGS(), 1546064706393);
  EXPECT_FALSE(replica_original->IsOnTransientStorage());
  EXPECT_EQ(replica_original->GetDiskId(), 1);

  // test InitReplicaRecovery of rur replica
  ExtendedBlock* block6 = new ExtendedBlock(bpid_, 1, 409600, 1546064706393);
  RecoveringBlock rblock6(block6, locs, 1546064706394);
  auto e8 = store_->InitReplicaRecovery(&rblock6, &rinfo);
  LOG(INFO) << "initReplicaRecovery fail, exception: " << e8.GetMessage();
  EXPECT_FALSE(e8.OK());

  ExtendedBlock* block7 = new ExtendedBlock(bpid_, 1, 409600, 1546064706393);
  RecoveringBlock rblock7(block7, locs, 1546064706396);
  auto e9 = store_->InitReplicaRecovery(&rblock7, &rinfo);
  LOG(INFO) << "initReplicaRecovery fail, exception: " << e9.GetMessage();
  EXPECT_TRUE(e9.OK());
  std::shared_ptr<ReplicaInfo> replica_new2 = store_->GetReplica(bpid_, 1);
  EXPECT_TRUE(replica_new2 != nullptr);
  std::shared_ptr<ReplicaUnderRecovery> replica_rur2 =
      std::dynamic_pointer_cast<ReplicaUnderRecovery>(replica_new2);
  EXPECT_TRUE(replica_rur2 != nullptr);
  EXPECT_EQ(replica_rur2->GetRecoveryID(), 1546064706396);
  delete rinfo;
}

TEST_F(ChunkServerStoreTests, initReplicaRecovery_recoveryLockTimeout) {
  // create rbw and write data
  ExtendedBlock block(bpid_, 4, 4096, 1546064706393);
  std::shared_ptr<ReplicaInPipeline> replica;
  auto e1 = store_->CreateRbw(StorageType::DISK, &block, false, &replica);
  EXPECT_TRUE(e1.OK());

  static const uint32_t k_buf_size = 4096;
  char* data = new char[k_buf_size];
  std::unique_ptr<char[]> scoped_data(data);
  for (uint32_t i = 0; i < k_buf_size; ++i) {
    data[i] = i % 32 + 'A';
  }
  io::IOChunk* chunk = new io::IOChunk(4096);
  chunk->WriteBytes(reinterpret_cast<uint8_t*>(data), 4096);
  auto e2 = store_->WriteBlock(&block, chunk, 0, 4096, false);
  EXPECT_TRUE(e2.OK());
  io::IOChunk::Destroy(chunk);

  replica->SetNumBytes(4096);
  replica->SetDiskDataLen(4096);
  replica->SetBytesAcked(2048);

  EXPECT_TRUE(replica != nullptr);
  EXPECT_EQ(replica->GetStateString(), "RBW");

  ReplicaRecoveryInfo* rinfo = nullptr;
  std::vector<DatanodeInfo*> locs;

  BlockLock* recovery_lock_ = store_->GetRecoveryLock();
  FLAGS_bytestore_hdfs_recovery_lock_timeout = 900;

  // test new initReplicaRecovery request within recoveryLockTimeout
  BlockLock::XLock* xlock1 = recovery_lock_->Get(bpid_, 4);
  EXPECT_TRUE(xlock1->TryLock());

  ExtendedBlock* block2 = new ExtendedBlock(bpid_, 4, 4096, 1546064706393);
  RecoveringBlock rblock2(block2, locs, 1546064706395);
  auto e3 = store_->InitReplicaRecovery(&rblock2, &rinfo);
  EXPECT_EQ(e3.GetE(), exceptions::E::kRecoveryInProgressException);
  EXPECT_NE(e3.GetMessage().find("is already in recovery"), std::string::npos);

  // test new initReplicaRecovery request when recoveryLockTimeout exceeded
  sleep(1);
  ExtendedBlock* block3 = new ExtendedBlock(bpid_, 4, 4096, 1546064706393);
  RecoveringBlock rblock3(block3, locs, 1546064706396);
  auto e4 = store_->InitReplicaRecovery(&rblock3, &rinfo);
  EXPECT_EQ(e4.GetE(), exceptions::E::kIOException);
  EXPECT_NE(e4.GetMessage().find("skip this node"), std::string::npos);

  xlock1->UnLock();
  recovery_lock_->Release(xlock1);

  if (rinfo != nullptr) {
    delete rinfo;
  }
}

TEST_F(ChunkServerStoreTests, InitReplicaRecoveryImpl) {
  // create rbw and write data
  ExtendedBlock block(bpid_, 4, 4096, 1546064706393);
  std::shared_ptr<ReplicaInPipeline> replica;
  auto e1 = store_->CreateRbw(StorageType::DISK, &block, false, &replica);
  EXPECT_TRUE(e1.OK());

  static const uint32_t k_buf_size = 4096;
  char* data = new char[k_buf_size];
  std::unique_ptr<char[]> scoped_data(data);
  for (uint32_t i = 0; i < k_buf_size; ++i) {
    data[i] = i % 32 + 'A';
  }
  io::IOChunk* chunk = new io::IOChunk(4096);
  chunk->WriteBytes(reinterpret_cast<uint8_t*>(data), 4096);
  auto e2 = store_->WriteBlock(&block, chunk, 0, 4096, false);
  EXPECT_TRUE(e2.OK());
  io::IOChunk::Destroy(chunk);

  replica->SetNumBytes(4096);
  replica->SetDiskDataLen(4096);
  replica->SetBytesAcked(2048);

  EXPECT_TRUE(replica != nullptr);
  EXPECT_EQ(replica->GetStateString(), "RBW");

  ReplicaRecoveryInfo* rinfo;
  std::vector<DatanodeInfo*> locs;

  BlockLock* recovery_lock_ = new BlockLock(101);
  uint64_t recoveryLockTimeout = 900;

  // test timeout of recoveryLock when recover lease
  BlockLock::XLock* xlock1 = recovery_lock_->Get(bpid_, 4);
  EXPECT_TRUE(xlock1->TryLock());
  sleep(1);
  ExtendedBlock* block1 = new ExtendedBlock(bpid_, 4, 4096, 1546064706393);
  RecoveringBlock rblock1(block1, locs, 1546064706395);
  auto e3 = store_->InitReplicaRecoveryImpl(&rblock1, &rinfo, xlock1,
                                            recoveryLockTimeout);
  xlock1->UnLock();
  recovery_lock_->Release(xlock1);
  EXPECT_EQ(e3.GetE(), exceptions::E::kRecoveryInProgressException);
  std::shared_ptr<ReplicaInfo> replica2 = store_->GetReplica(bpid_, 4);
  EXPECT_TRUE(replica2 != nullptr);
  EXPECT_EQ(replica2->GetStateString(), "RBW");
  EXPECT_EQ(replica2->GetNumBytes(), 4096);
  EXPECT_EQ(replica2->GetGS(), 1546064706393);
  EXPECT_FALSE(replica2->IsOnTransientStorage());
  EXPECT_LE(replica2->GetDiskId(), 2);

  // test normal case
  BlockLock::XLock* xlock2 = recovery_lock_->Get(bpid_, 4);
  EXPECT_TRUE(xlock2->TryLock());
  ExtendedBlock* block2 = new ExtendedBlock(bpid_, 4, 4096, 1546064706393);
  RecoveringBlock rblock2(block2, locs, 1546064706396);
  auto e4 = store_->InitReplicaRecoveryImpl(&rblock2, &rinfo, xlock2,
                                            recoveryLockTimeout);
  xlock2->UnLock();
  recovery_lock_->Release(xlock2);
  EXPECT_EQ(e4.GetE(), exceptions::E::kNoException);
  std::shared_ptr<ReplicaInfo> replica3 = store_->GetReplica(bpid_, 4);
  EXPECT_TRUE(replica3 != nullptr);
  EXPECT_EQ(replica3->GetStateString(), "RUR");
  EXPECT_EQ(replica3->GetNumBytes(), 4096);
  EXPECT_EQ(replica3->GetGS(), 1546064706393);
  EXPECT_FALSE(replica3->IsOnTransientStorage());
  EXPECT_LE(replica3->GetDiskId(), 2);
  std::shared_ptr<ReplicaUnderRecovery> rur =
      std::dynamic_pointer_cast<ReplicaUnderRecovery>(replica3);
  EXPECT_TRUE(rur != nullptr);
  EXPECT_EQ(rur->GetRecoveryID(), 1546064706396);
  delete rinfo;
  delete recovery_lock_;
}

TEST_F(ChunkServerStoreTests, UpdateReplicaUnderRecovery_Finalized) {
  // recover finalized replica
  ReplicaRecoveryInfo* rinfo;
  std::vector<DatanodeInfo*> locs;
  ExtendedBlock* block = new ExtendedBlock(bpid_, 1, 409600, 1546064706393);
  RecoveringBlock rblock(block, locs, 1546064706395);
  auto e1 = store_->InitReplicaRecovery(&rblock, &rinfo);
  EXPECT_TRUE(e1.OK());

  std::string storage_id;
  ExtendedBlock* block2 = new ExtendedBlock(bpid_, 5, 409600, 1546064706393);
  auto e2 = store_->UpdateReplicaUnderRecovery(block2, 1546064706395, 4096,
                                               &storage_id);
  LOG(INFO) << "UpdateReplicaUnderRecovery exception: " << e2.GetMessage();
  EXPECT_FALSE(e2.OK());
  delete block2;

  ExtendedBlock* block3 = new ExtendedBlock(bpid_, 2, 409600, 1546064706393);
  auto e3 = store_->UpdateReplicaUnderRecovery(block3, 1546064706395, 4096,
                                               &storage_id);
  LOG(INFO) << "UpdateReplicaUnderRecovery exception: " << e3.GetMessage();
  EXPECT_FALSE(e3.OK());
  delete block3;

  ExtendedBlock* block4 = new ExtendedBlock(bpid_, 1, 4096, 1546064706393);
  auto e4 = store_->UpdateReplicaUnderRecovery(block4, 1546064706395, 4096,
                                               &storage_id);
  LOG(INFO) << "UpdateReplicaUnderRecovery exception: " << e4.GetMessage();
  EXPECT_FALSE(e4.OK());
  delete block4;

  ExtendedBlock* block5 = new ExtendedBlock(bpid_, 1, 409600, 1546064706393);
  auto e5 = store_->UpdateReplicaUnderRecovery(block5, 1546064706396, 4096,
                                               &storage_id);
  LOG(INFO) << "UpdateReplicaUnderRecovery exception: " << e5.GetMessage();
  EXPECT_FALSE(e5.OK());
  delete block5;

  ExtendedBlock* block6 = new ExtendedBlock(bpid_, 1, 409600, 1546064706393);
  auto e6 = store_->UpdateReplicaUnderRecovery(block6, 1546064706395, 409601,
                                               &storage_id);
  LOG(INFO) << "UpdateReplicaUnderRecovery exception: " << e6.GetMessage();
  EXPECT_FALSE(e6.OK());
  delete block6;

  auto e10 = store_->UpdateReplicaUnderRecovery(block, 1546064706395, 40960,
                                                &storage_id);
  EXPECT_TRUE(e10.OK());

  std::shared_ptr<ReplicaInfo> replica_new = store_->GetReplica(bpid_, 1);
  EXPECT_TRUE(replica_new != nullptr);
  EXPECT_EQ(replica_new->GetStateString(), "FINALIZED");
  EXPECT_EQ(replica_new->GetNumBytes(), 40960);
  EXPECT_EQ(replica_new->GetGS(), 1546064706395);
  EXPECT_FALSE(replica_new->IsOnTransientStorage());
  EXPECT_EQ(replica_new->GetDiskId(), 1);

  BlockMeta meta;
  GetChunkAttr(1, block, &meta);
  EXPECT_EQ(meta.gs_, 1546064706395);
  EXPECT_EQ(meta.tmp_, 0);
  EXPECT_EQ(meta.fin_, 1);
  CSChunkMeta chunk_meta;
  EXPECT_EQ(GetChunkMeta(1, block, &chunk_meta), BYTESTORE_OK);
  EXPECT_EQ(chunk_meta.length_, 40960);

  auto cblock = block->GetChecksumBlock();
  EXPECT_EQ(GetChunkMeta(1, cblock, &chunk_meta), BYTESTORE_OK);
  EXPECT_EQ(chunk_meta.length_, 320);

  // truncate without aligned
  ReplicaRecoveryInfo* rinfo2;
  ExtendedBlock* b = new ExtendedBlock(bpid_, 1, 40960, 1546064706395);
  RecoveringBlock rblock2(b, locs, 1546064706396);
  auto e11 = store_->InitReplicaRecovery(&rblock2, &rinfo2);
  EXPECT_TRUE(e11.OK());
  auto e12 =
      store_->UpdateReplicaUnderRecovery(b, 1546064706396, 40900, &storage_id);
  ASSERT_TRUE(e12.OK());

  EXPECT_EQ(GetChunkMeta(1, cblock, &chunk_meta), BYTESTORE_OK);
  ASSERT_EQ(chunk_meta.length_, 320);
  auto chunk = new io::IOChunk(320);
  auto e13 = store_->ReadChecksum(cblock, chunk, 316, 4);
  EXPECT_TRUE(e13.OK());
  EXPECT_EQ(0x275bc41cU, chunk->ReadFixed32BE());  // crc32c of data[:452]

  io::IOChunk::Destroy(chunk);
  delete cblock;
  delete rinfo;
  delete rinfo2;
}

TEST_F(ChunkServerStoreTests, UpdateReplicaUnderRecovery_RBW) {
  ExtendedBlock block(bpid_, 4, 8128, 1546064706393);
  std::shared_ptr<ReplicaInPipeline> replica;
  auto e = store_->CreateRbw(StorageType::DISK, &block, false, &replica);
  EXPECT_TRUE(e.OK());

  static const uint32_t k_buf_size = 8128;
  char* data = new char[k_buf_size];
  std::unique_ptr<char[]> scoped_data(data);
  for (uint32_t i = 0; i < k_buf_size; ++i) {
    data[i] = i % 32 + 'A';
  }
  io::IOChunk* chunk = new io::IOChunk(8128);
  chunk->WriteBytes(reinterpret_cast<uint8_t*>(data), 8128);
  auto e2 = store_->WriteBlock(&block, chunk, 0, 8128, false);
  EXPECT_TRUE(e2.OK());

  replica->SetNumBytes(8128);
  replica->SetDiskDataLen(8128);
  replica->SetBytesAcked(8128);

  ReplicaRecoveryInfo* rinfo;
  std::vector<DatanodeInfo*> locs;
  ExtendedBlock* new_block = new ExtendedBlock(bpid_, 4, 8128, 1546064706393);
  RecoveringBlock rblock(new_block, locs, 1546064706395);
  auto e3 = store_->InitReplicaRecovery(&rblock, &rinfo);
  EXPECT_TRUE(e3.OK());

  std::string storage_id;
  auto e4 = store_->UpdateReplicaUnderRecovery(new_block, 1546064706395, 4096,
                                               &storage_id);
  LOG(INFO) << "UpdateReplicaUnderRecovery exception: " << e4.GetMessage();
  ASSERT_TRUE(e4.OK());

  std::shared_ptr<ReplicaInfo> replica_new = store_->GetReplica(bpid_, 4);
  EXPECT_TRUE(replica_new != nullptr);
  EXPECT_EQ(replica_new->GetStateString(), "FINALIZED");
  EXPECT_EQ(replica_new->GetNumBytes(), 4096);
  EXPECT_EQ(replica_new->GetGS(), 1546064706395);
  EXPECT_FALSE(replica_new->IsOnTransientStorage());
  EXPECT_LE(replica_new->GetDiskId(), 2);
  uint32_t new_disk_id = replica_new->GetDiskId();

  BlockMeta meta;
  GetChunkAttr(new_disk_id, &block, &meta);
  EXPECT_EQ(meta.gs_, 1546064706395);
  EXPECT_EQ(meta.tmp_, 0);
  EXPECT_EQ(meta.fin_, 1);

  // test truncate
  std::string output;
  ReadChunk(new_disk_id, &block, 4096, 0, &output);
  static const uint32_t k_buf_size2 = 4096;
  char* data2 = new char[4096];
  std::unique_ptr<char[]> scoped_data2(data2);
  for (uint32_t i = 0; i < k_buf_size2; ++i) {
    data2[i] = i % 32 + 'A';
  }
  EXPECT_EQ(std::string(data2, 4096), output);

  CSChunkMeta chunk_meta;
  EXPECT_EQ(GetChunkMeta(new_disk_id, &block, &chunk_meta), BYTESTORE_OK);
  EXPECT_EQ(chunk_meta.length_, 4096);

  // test append after truncate
  std::shared_ptr<ReplicaInPipeline> replica2;
  ExtendedBlock block2(bpid_, 4, 4096, 1546064706395);
  auto e5 = store_->Append(&block2, 1546064706396, 4096, &replica2);
  EXPECT_TRUE(e5.OK());
  EXPECT_EQ(replica2->GetStateString(), "RBW");
  EXPECT_EQ(replica2->GetNumBytes(), 4096);
  EXPECT_EQ(replica2->GetGS(), 1546064706396);
  EXPECT_FALSE(replica2->IsOnTransientStorage());

  // write block after truncate
  chunk->Reset();
  chunk->WriteBytes(reinterpret_cast<uint8_t*>(data), 8128);
  auto e6 = store_->WriteBlock(&block2, chunk, 4096, 8128, false);
  EXPECT_TRUE(e6.OK());
  EXPECT_EQ(GetChunkMeta(new_disk_id, &block2, &chunk_meta), BYTESTORE_OK);
  EXPECT_EQ(chunk_meta.length_, 12224);

  std::string output2;
  ReadChunk(new_disk_id, &block2, 4096, 0, &output2);
  EXPECT_EQ(std::string(data2, 4096), output2);

  std::string output3;
  ReadChunk(new_disk_id, &block2, 12224, 0, &output3);
  std::string data3 = std::string(data2, 4096) + std::string(data, 8128);
  EXPECT_EQ(data3, output3);

  io::IOChunk::Destroy(chunk);
  delete rinfo;
}

TEST_F(ChunkServerStoreTests, RecoverRbw) {
  // create rbw and write data
  ExtendedBlock block(bpid_, 4, 4096, 1546064706393);
  std::shared_ptr<ReplicaInPipeline> replica;
  auto e1 = store_->CreateRbw(StorageType::DISK, &block, false, &replica);
  EXPECT_TRUE(e1.OK());

  static const uint32_t k_buf_size = 4096;
  char* data = new char[k_buf_size];
  std::unique_ptr<char[]> scoped_data(data);
  for (uint32_t i = 0; i < k_buf_size; ++i) {
    data[i] = i % 32 + 'A';
  }
  io::IOChunk* chunk = new io::IOChunk(4096);
  chunk->WriteBytes(reinterpret_cast<uint8_t*>(data), 4096);
  auto e2 = store_->WriteBlock(&block, chunk, 0, 4096, false);
  EXPECT_TRUE(e2.OK());
  io::IOChunk::Destroy(chunk);

  replica->SetNumBytes(4096);
  replica->SetDiskDataLen(4096);
  replica->SetBytesAcked(2048);

  // test RecoverRbw
  io::Connection* conn = new io::Connection();
  dn_stream_server_->AddXceiver(std::make_shared<MockWriterDataXceiver>(
      conn, dn_stream_server_, replica));
  sleep(1);
  EXPECT_TRUE(replica->ShouldStopWriter());

  io::Connection* conn2 = new io::Connection();
  DataXceiver* xceiver =
      new MockTestDataXceiver(conn2, store_, &block, "recover rbw");
  xceiver->Start();
  xceiver->Join();
  delete xceiver;

  EXPECT_FALSE(replica->ShouldStopWriter());
  EXPECT_EQ(replica->GetStateString(), "RBW");
  EXPECT_EQ(replica->GetNumBytes(), 2048);
  EXPECT_EQ(replica->GetBytesOnDisk(), 2048);
  EXPECT_EQ(replica->GetGS(), 1546064706395);
  EXPECT_FALSE(replica->IsOnTransientStorage());
  EXPECT_LE(replica->GetDiskId(), 2);
  uint32_t new_disk_id = replica->GetDiskId();

  BlockMeta meta;
  GetChunkAttr(new_disk_id, &block, &meta);
  EXPECT_EQ(meta.gs_, 1546064706395);
  EXPECT_EQ(meta.tmp_, 0);
  EXPECT_EQ(meta.fin_, 0);
  CSChunkMeta chunk_meta;
  EXPECT_EQ(GetChunkMeta(new_disk_id, &block, &chunk_meta), BYTESTORE_OK);

  std::string output;
  ReadChunk(new_disk_id, &block, 2048, 0, &output);
  static const uint32_t k_buf_size2 = 2048;
  char* data1 = new char[k_buf_size2];
  std::unique_ptr<char[]> scoped_data2(data1);
  for (uint32_t i = 0; i < k_buf_size2; ++i) {
    data1[i] = i % 32 + 'A';
  }
  EXPECT_EQ(std::string(data1, 2048), output);

  EXPECT_EQ(GetChunkMeta(new_disk_id, &block, &chunk_meta), BYTESTORE_OK);
  EXPECT_EQ(chunk_meta.length_, 2048);

  // error test
  std::shared_ptr<ReplicaInPipeline> replica_new;
  ExtendedBlock block2(bpid_, 5, 409600, 1546064706395);
  auto e4 =
      store_->RecoverRbw(&block2, 1546064706395, 1024, 8192, &replica_new);
  LOG(INFO) << "RecoverRbw exception: " << e4.GetMessage();
  EXPECT_FALSE(e4.OK());

  ExtendedBlock block3(bpid_, 2, 409600, 1546064706395);
  auto e5 =
      store_->RecoverRbw(&block3, 1546064706395, 1024, 8192, &replica_new);
  LOG(INFO) << "RecoverRbw exception: " << e5.GetMessage();
  EXPECT_FALSE(e5.OK());

  ExtendedBlock block4(bpid_, 4, 409600, 1546064706396);
  auto e6 =
      store_->RecoverRbw(&block4, 1546064706395, 1024, 8192, &replica_new);
  LOG(INFO) << "RecoverRbw exception: " << e6.GetMessage();
  EXPECT_FALSE(e6.OK());

  ExtendedBlock block5(bpid_, 4, 409600, 1546064706395);
  auto e7 =
      store_->RecoverRbw(&block5, 1546064706391, 1024, 8192, &replica_new);
  LOG(INFO) << "RecoverRbw exception: " << e7.GetMessage();
  EXPECT_FALSE(e7.OK());

  ExtendedBlock block6(bpid_, 4, 409600, 1546064706395);
  auto e8 =
      store_->RecoverRbw(&block6, 1546064706395, 4096, 8192, &replica_new);
  LOG(INFO) << "RecoverRbw exception: " << e8.GetMessage();
  EXPECT_FALSE(e8.OK());

  ExtendedBlock block7(bpid_, 4, 409600, 1546064706395);
  auto e9 =
      store_->RecoverRbw(&block7, 1546064706395, 1024, 1025, &replica_new);
  LOG(INFO) << "RecoverRbw exception: " << e9.GetMessage();
  EXPECT_FALSE(e9.OK());
}

TEST_F(ChunkServerStoreTests, RecoverAppend) {
  // create rbw and write data
  ExtendedBlock block(bpid_, 4, 4096, 1546064706393);
  std::shared_ptr<ReplicaInPipeline> replica;
  auto e1 = store_->CreateRbw(StorageType::DISK, &block, false, &replica);
  EXPECT_TRUE(e1.OK());

  static const uint32_t k_buf_size = 4096;
  char* data = new char[k_buf_size];
  std::unique_ptr<char[]> scoped_data(data);
  for (uint32_t i = 0; i < k_buf_size; ++i) {
    data[i] = i % 32 + 'A';
  }
  io::IOChunk* chunk = new io::IOChunk(4096);
  chunk->WriteBytes(reinterpret_cast<uint8_t*>(data), 4096);
  auto e2 = store_->WriteBlock(&block, chunk, 0, 4096, false);
  EXPECT_TRUE(e2.OK());
  io::IOChunk::Destroy(chunk);

  replica->SetNumBytes(4096);
  replica->SetDiskDataLen(4096);
  replica->SetBytesAcked(4096);

  // recoverappend rbw
  io::Connection* conn = new io::Connection();
  dn_stream_server_->AddXceiver(std::make_shared<MockWriterDataXceiver>(
      conn, dn_stream_server_, replica));
  sleep(1);
  EXPECT_TRUE(replica->ShouldStopWriter());

  io::Connection* conn2 = new io::Connection();
  DataXceiver* xceiver =
      new MockTestDataXceiver(conn2, store_, &block, "recoverappend rbw");
  xceiver->Start();
  xceiver->Join();
  delete xceiver;
  EXPECT_FALSE(replica->ShouldStopWriter());
  EXPECT_EQ(replica->GetStateString(), "RBW");
  EXPECT_EQ(replica->GetNumBytes(), 4096);
  EXPECT_EQ(replica->GetBytesOnDisk(), 4096);
  EXPECT_EQ(replica->GetGS(), 1546064706395);
  EXPECT_FALSE(replica->IsOnTransientStorage());
  EXPECT_LE(replica->GetDiskId(), 2);
  uint32_t new_disk_id = replica->GetDiskId();

  BlockMeta meta;
  GetChunkAttr(new_disk_id, &block, &meta);
  EXPECT_EQ(meta.gs_, 1546064706395);
  EXPECT_EQ(meta.tmp_, 0);
  EXPECT_EQ(meta.fin_, 0);
  CSChunkMeta chunk_meta;
  EXPECT_EQ(GetChunkMeta(new_disk_id, &block, &chunk_meta), BYTESTORE_OK);

  // recoverappend finalized
  std::shared_ptr<ReplicaInPipeline> replica_new;
  ExtendedBlock block2(bpid_, 1, 409600, 1546064706393);
  auto e4 = store_->RecoverAppend(&block2, 1546064706396, 409600, &replica_new);
  EXPECT_TRUE(e4.OK());
  EXPECT_TRUE(replica_new != nullptr);
  EXPECT_EQ(replica_new->GetStateString(), "RBW");
  EXPECT_EQ(replica_new->GetNumBytes(), 409600);
  EXPECT_EQ(replica_new->GetGS(), 1546064706396);
  EXPECT_FALSE(replica_new->IsOnTransientStorage());
  EXPECT_EQ(replica_new->GetDiskId(), 1);

  BlockMeta meta2;
  GetChunkAttr(1, &block2, &meta2);
  EXPECT_EQ(meta2.gs_, 1546064706396);
  EXPECT_EQ(meta2.tmp_, 0);
  EXPECT_EQ(meta2.fin_, 0);
  EXPECT_EQ(GetChunkMeta(1, &block2, &chunk_meta), BYTESTORE_OK);

  // error tests
  // non-existent block
  ExtendedBlock block3(bpid_, 5, 409600, 1546064706393);
  auto e5 = store_->RecoverAppend(&block3, 1546064706397, 409600, &replica_new);
  LOG(INFO) << "RecoverAppend exception: " << e5.GetMessage();
  EXPECT_FALSE(e5.OK());

  // RWR replica
  ExtendedBlock block4(bpid_, 2, 409600, 1546064706393);
  auto e6 = store_->RecoverAppend(&block4, 1546064706397, 409600, &replica_new);
  LOG(INFO) << "RecoverAppend exception: " << e6.GetMessage();
  EXPECT_FALSE(e6.OK());

  // gs mismatch
  ExtendedBlock block5(bpid_, 1, 409600, 1546064706397);
  auto e7 = store_->RecoverAppend(&block5, 1546064706398, 409600, &replica_new);
  LOG(INFO) << "RecoverAppend exception: " << e7.GetMessage();
  EXPECT_FALSE(e7.OK());

  // gs mismatch
  ExtendedBlock block6(bpid_, 1, 409600, 1546064706393);
  auto e8 = store_->RecoverAppend(&block6, 1546064706395, 409600, &replica_new);
  LOG(INFO) << "RecoverAppend exception: " << e8.GetMessage();
  EXPECT_FALSE(e8.OK());

  // length mismatch
  ExtendedBlock block7(bpid_, 1, 409600, 1546064706393);
  auto e9 = store_->RecoverAppend(&block7, 1546064706397, 409601, &replica_new);
  LOG(INFO) << "RecoverAppend exception: " << e9.GetMessage();
  EXPECT_FALSE(e9.OK());
}

TEST_F(ChunkServerStoreTests, RecoverClose) {
  // create rbw and write data
  ExtendedBlock block(bpid_, 4, 4096, 1546064706393);
  std::shared_ptr<ReplicaInPipeline> replica;
  auto e1 = store_->CreateRbw(StorageType::DISK, &block, false, &replica);
  EXPECT_TRUE(e1.OK());

  static const uint32_t k_buf_size = 4096;
  char* data = new char[k_buf_size];
  std::unique_ptr<char[]> scoped_data(data);
  for (uint32_t i = 0; i < k_buf_size; ++i) {
    data[i] = i % 32 + 'A';
  }
  io::IOChunk* chunk = new io::IOChunk(4096);
  chunk->WriteBytes(reinterpret_cast<uint8_t*>(data), 4096);
  auto e2 = store_->WriteBlock(&block, chunk, 0, 4096, false);
  EXPECT_TRUE(e2.OK());
  io::IOChunk::Destroy(chunk);

  replica->SetNumBytes(4096);
  replica->SetDiskDataLen(4096);
  replica->SetBytesAcked(4096);

  // recoverclose rbw
  io::Connection* conn = new io::Connection();
  dn_stream_server_->AddXceiver(std::make_shared<MockWriterDataXceiver>(
      conn, dn_stream_server_, replica));
  sleep(1);
  EXPECT_TRUE(replica->ShouldStopWriter());

  io::Connection* conn2 = new io::Connection();
  DataXceiver* xceiver =
      new MockTestDataXceiver(conn2, store_, &block, "recoverclose rbw");
  xceiver->Start();
  xceiver->Join();
  delete xceiver;
  EXPECT_FALSE(replica->ShouldStopWriter());

  std::shared_ptr<ReplicaInfo> replica_new = store_->GetReplica(bpid_, 4);
  EXPECT_TRUE(replica_new != nullptr);
  EXPECT_EQ(replica_new->GetStateString(), "FINALIZED");
  EXPECT_EQ(replica_new->GetNumBytes(), 4096);
  EXPECT_EQ(replica_new->GetGS(), 1546064706395);
  EXPECT_FALSE(replica_new->IsOnTransientStorage());
  EXPECT_LE(replica_new->GetDiskId(), 2);
  uint32_t new_disk_id = replica_new->GetDiskId();

  BlockMeta meta;
  GetChunkAttr(new_disk_id, &block, &meta);
  EXPECT_EQ(meta.gs_, 1546064706395);
  EXPECT_EQ(meta.tmp_, 0);
  EXPECT_EQ(meta.fin_, 1);
  CSChunkMeta chunk_meta;
  EXPECT_EQ(GetChunkMeta(new_disk_id, &block, &chunk_meta), BYTESTORE_OK);

  // recoverclose finalized
  ExtendedBlock block2(bpid_, 1, 409600, 1546064706393);
  std::string storage_id;
  auto e5 = store_->RecoverClose(&block2, 1546064706396, 409600, &storage_id,
                                 nullptr);
  EXPECT_TRUE(e5.OK());
  replica_new = store_->GetReplica(bpid_, 1);
  EXPECT_TRUE(replica_new != nullptr);
  EXPECT_EQ(replica_new->GetStateString(), "FINALIZED");
  EXPECT_EQ(replica_new->GetNumBytes(), 409600);
  EXPECT_EQ(replica_new->GetGS(), 1546064706396);
  EXPECT_FALSE(replica_new->IsOnTransientStorage());
  EXPECT_EQ(replica_new->GetDiskId(), 1);

  BlockMeta meta2;
  GetChunkAttr(1, &block2, &meta2);
  EXPECT_EQ(meta2.gs_, 1546064706396);
  EXPECT_EQ(meta2.tmp_, 0);
  EXPECT_EQ(meta2.fin_, 1);
  EXPECT_EQ(GetChunkMeta(1, &block2, &chunk_meta), BYTESTORE_OK);

  std::unordered_map<uint32_t, StorageDirectory>* disk_id_sd_map =
      store_->GetDiskIdSdMap();
  EXPECT_EQ(disk_id_sd_map->size(), 4);
  EXPECT_TRUE(storage_id == (*disk_id_sd_map)[1].storage_uuid_ ||
              storage_id == (*disk_id_sd_map)[2].storage_uuid_);

  // error tests
  // non-existent block
  ExtendedBlock block3(bpid_, 5, 409600, 1546064706393);
  auto e6 = store_->RecoverClose(&block3, 1546064706397, 409600, &storage_id,
                                 nullptr);
  LOG(INFO) << "RecoverClose exception: " << e6.GetMessage();
  EXPECT_FALSE(e6.OK());
}

TEST_F(ChunkServerStoreTests, CopyBlockAcrossFederation) {
  FLAGS_bytestore_chunkserver_enable_hardlink_on_unfrozen_chunk = true;
  ExtendedBlock src_block(bpid_, 1, 409600, 1546064706393);
  std::string bpid2 = "BP-2026362776-192.168.6.102-1546064706393";
  ExtendedBlock dst_block(bpid2, 4, 409600, 1546064706394);
  auto e1 = store_->CopyBlockAcrossFederation(&src_block, &dst_block,
                                              StorageType::DISK);
  EXPECT_TRUE(e1.OK());

  std::shared_ptr<ReplicaInfo> replica_dst = store_->GetReplica(bpid2, 4);
  EXPECT_TRUE(replica_dst != nullptr);

  EXPECT_EQ(replica_dst->GetStateString(), "FINALIZED");
  EXPECT_EQ(replica_dst->GetNumBytes(), 409600);
  EXPECT_EQ(replica_dst->GetGS(), 1546064706394);
  EXPECT_FALSE(replica_dst->IsOnTransientStorage());
  EXPECT_EQ(replica_dst->GetDiskId(), 1);

  std::string output;
  ReadChunk(1, &dst_block, 4096, 0, &output);
  static const uint32_t k_buf_size = 4096;
  char* data = new char[4096];
  std::unique_ptr<char[]> scoped_data(data);
  for (uint32_t i = 0; i < k_buf_size; ++i) {
    data[i] = i % 32 + 'A';
  }
  EXPECT_EQ(std::string(data, 4096), output);

  BlockMeta meta;
  GetChunkAttr(1, &dst_block, &meta);
  EXPECT_EQ(meta.gs_, 1546064706394);
  EXPECT_EQ(meta.tmp_, 0);
  EXPECT_EQ(meta.fin_, 1);
  CSChunkMeta chunk_meta;
  EXPECT_EQ(GetChunkMeta(1, &dst_block, &chunk_meta), BYTESTORE_OK);

  auto cblock = dst_block.GetChecksumBlock();
  GetChunkMeta(1, cblock, &chunk_meta);
  ASSERT_EQ(chunk_meta.length_, 3200);
  auto chunk = new io::IOChunk(4);
  auto e3 = store_->ReadChecksum(cblock, chunk, 0, 4);
  EXPECT_TRUE(e3.OK());
  EXPECT_EQ(0xda85273U, chunk->ReadFixed32BE());  // crc32c of data[:512]
  io::IOChunk::Destroy(chunk);
  delete cblock;

  ExtendedBlock dst_block2(bpid_, 2, 409600, 1546064706393);
  std::shared_ptr<ReplicaInPipeline> replica2;
  auto e2 = store_->CopyBlockAcrossFederation(&src_block, &dst_block,
                                              StorageType::DISK);
  LOG(INFO) << "CopyBlockAcrossFederation exception: " << e2.GetMessage();
  EXPECT_FALSE(e2.OK());
}

TEST_F(ChunkServerStoreTests, ReservedSpaceForMultiTiered) {
  std::vector<uint64_t> free_sz(4);
  for (int i = 0; i < 4; ++i) {
    uint32_t disk_id = i + 1;
    EXPECT_EQ(BYTESTORE_OK, GetFreeSizeInBytes(disk_id, &free_sz[i]));
  }
  uint64_t limit_hdd =
      MFLAGS(bytestore_chunkserver_reserved_disk_size, TYPE_SATA_HDD);
  uint64_t limit_ssd =
      MFLAGS(bytestore_chunkserver_reserved_disk_size, TYPE_NVME_SSD);
  LOG(INFO) << "soft_limit_hdd:" << limit_hdd;
  LOG(INFO) << "soft_limit_ssd:" << limit_ssd;
  for (int i = 0; i < 4; ++i) {
    LOG(INFO) << "disk_id:" << (i + 1) << ", free_size:" << free_sz[i];
  }

  auto diff = [](uint64_t a, uint64_t b) {
    return (a > b ? a - b : b - a);
  };
  EXPECT_LT(diff(free_sz[0], free_sz[1]), 1 << 25);
  EXPECT_LT(diff(free_sz[2], free_sz[3]), 1 << 25);
  EXPECT_LT(diff(diff(limit_ssd, limit_hdd), diff(free_sz[0], free_sz[2])),
            1 << 25);
}

TEST_F(ChunkServerStoreTests, MultiThreadReadWrite) {
  std::vector<std::thread> workers;
  for (int i = 0; i < 100; i++) {
    workers.push_back(std::thread([i, this]() {
      std::shared_ptr<ReplicaInPipeline> replica;
      ExtendedBlock block(bpid_, i + 100, 409600, 1546064706393);
      auto e = store_->CreateRbw(StorageType::DISK, &block, false, &replica);
      EXPECT_TRUE(e.OK());

      uint32_t buf_size = 4000 + i;
      char* data = new char[buf_size];
      std::unique_ptr<char[]> scoped_data(data);
      char* data_read = new char[buf_size];
      std::unique_ptr<char[]> scoped_data_read(data_read);
      for (uint32_t i = 0; i < buf_size; ++i) {
        data[i] = i % 32 + 'A';
      }
      io::IOChunk* chunk = new io::IOChunk(buf_size);
      chunk->WriteBytes(reinterpret_cast<uint8_t*>(data), buf_size);
      e = store_->WriteBlock(&block, chunk, 0, buf_size, false);
      EXPECT_TRUE(e.OK());

      chunk->Reset();
      e = store_->ReadBlock(&block, chunk, 0, buf_size);
      EXPECT_TRUE(e.OK());
      memcpy(data_read, chunk->ReadBytes(buf_size), buf_size);
      EXPECT_EQ(std::string(data, buf_size), std::string(data_read, buf_size));

      io::IOChunk::Destroy(chunk);
    }));
  }

  std::for_each(workers.begin(), workers.end(), [](std::thread& t) {
    t.join();
  });
}

TEST_F(ChunkServerStoreTests, GetMigrationReadThroughputThreshold) {
  FLAGS_bytestore_chunkserver_tiering_migration_low_water_mark_percentage = 20;
  FLAGS_bytestore_hdfs_warm_up_migration_step_num = 8;
  int64_t read_throughput_unit =
      FLAGS_bytestore_hdfs_tiering_migration_max_read_throughput_bytes_per_disk /
      FLAGS_bytestore_hdfs_warm_up_migration_step_num;
  ASSERT_EQ(store_->GetMigrationReadThroughputThreshold(15),
            read_throughput_unit);
  ASSERT_EQ(store_->GetMigrationReadThroughputThreshold(20),
            read_throughput_unit);
  ASSERT_EQ(store_->GetMigrationReadThroughputThreshold(21),
            read_throughput_unit * 2);
  ASSERT_EQ(store_->GetMigrationReadThroughputThreshold(22),
            read_throughput_unit * 3);
  ASSERT_EQ(store_->GetMigrationReadThroughputThreshold(23),
            read_throughput_unit * 4);
  ASSERT_EQ(store_->GetMigrationReadThroughputThreshold(24),
            read_throughput_unit * 5);
  ASSERT_EQ(store_->GetMigrationReadThroughputThreshold(25),
            read_throughput_unit * 6);
  ASSERT_EQ(store_->GetMigrationReadThroughputThreshold(26),
            read_throughput_unit * 7);
  ASSERT_EQ(
      store_->GetMigrationReadThroughputThreshold(27),
      FLAGS_bytestore_hdfs_tiering_migration_max_read_throughput_bytes_per_disk);
  ASSERT_EQ(
      store_->GetMigrationReadThroughputThreshold(70),
      FLAGS_bytestore_hdfs_tiering_migration_max_read_throughput_bytes_per_disk);
}

}  // namespace hdfs
}  // namespace chunkserver
}  // namespace bytestore
