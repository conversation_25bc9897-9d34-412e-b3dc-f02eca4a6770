// Copyright (c) 2019-present, ByteDance Inc. All rights reserved.

#include "hdfs/datanode_stream_server.h"

#include <sys/epoll.h>

#include <memory>
#include <set>
#include <utility>

#include "byte/include/byte_log.h"
#include "byte/util/scope_guard.h"
#include "gflags/gflags.h"
#include "hdfs/data_transfer_throttler.h"
#include "hdfs/data_xceiver.h"
#include "hdfs/datanode.h"
#include "hdfs/datanode_config.h"
#include "hdfs/extended_block.h"
#include "hdfs/io/address.h"
#include "hdfs/io/concurrent_queue.h"
#include "hdfs/io/connection.h"
#include "hdfs/io/define.h"
#include "hdfs/io/net.h"
#include "util/defer.h"

DECLARE_uint32(bytestore_hdfs_max_xceiver_count);
DECLARE_uint32(bytestore_hdfs_user_priority_num);
DECLARE_int32(bytestore_hdfs_user_default_priority);
DECLARE_uint32(bytestore_hdfs_max_xceiver_count_p0);
DECLARE_uint32(bytestore_hdfs_max_xceiver_count_p1);
DECLARE_uint32(bytestore_hdfs_max_xceiver_count_p2);
DECLARE_bool(bytestore_hdfs_priority_xceiver_throttle_enable);

namespace bytestore {
namespace chunkserver {
namespace hdfs {

class XceiverManager : public Thread {
 private:
  enum Type : uint8_t { DELETE, ADD };

 public:
  explicit XceiverManager(DataNodeStreamServer* server)
      : stream_server_(server) {
    SetName("XceiverManager");
    xceiver_to_be_started_count_ = 0;
  }

  ~XceiverManager() {
    Stop();
    Join();
  }

  void Run() {
    LOG(INFO) << "XceiverManager::Run";
    while (!IsStopped()) {
      WaitUtil(
          [this]() {
            return !task_queue_.Empty() || IsStopped();
          },
          0);
      while (!task_queue_.Empty()) {
        byte::MutexLocker guard(&xceivers_mutex_);
        auto item = task_queue_.Front();
        task_queue_.Pop();

        std::shared_ptr<DataXceiver> xceiver = item.second;
        switch (item.first) {
          case XceiverManager::Type::ADD:
            xceiver->Start();
            xceivers_.emplace(xceiver);
            stream_server_->AddXceiverCount();
            xceiver_to_be_started_count_--;
            break;
          case XceiverManager::Type::DELETE:
            xceiver->Stop();
            xceiver->Join();
            xceivers_.erase(xceiver);
            stream_server_->DecXceiverCount();
            break;
        }
      }
    }
    Cleanup();
  }

  void AddXceiver(std::shared_ptr<DataXceiver> xceiver) {
    task_queue_.Push(std::make_pair(XceiverManager::Type::ADD, xceiver));
    xceiver_to_be_started_count_++;
    Signal();
  }

  void DeleteXceiver(std::shared_ptr<DataXceiver> xceiver) {
    task_queue_.Push(std::make_pair(XceiverManager::Type::DELETE, xceiver));
    Signal();
  }

  void SendRestartOOBToPeers() {
    byte::MutexLocker guard(&xceivers_mutex_);
    for (auto xceiver : xceivers_) {
      auto e = xceiver->SendRestartOOB();
      if (e.GetE() == exceptions::E::kIOException) {
        LOG(WARNING) << "Got error when sending OOB message. " << e.ToString();
      } else if (e.GetE() == exceptions::E::kInterruptedException) {
        LOG(WARNING) << "Interrupted when sending OOB message.";
      }
    }
  }

  uint32_t GetXceiverToBeStartedCount() {
    return xceiver_to_be_started_count_.Value();
  }

 private:
  void Cleanup() {
    LOG(INFO) << "XceiverManager::Cleanup";
    // all started xceivers, whether finished or not, are in transferring_set_
    for (auto xceiver : xceivers_) {
      xceiver->Stop();
      xceiver->Join();
      // Finished xceivers already produce a DELETE request
    }
    // At this point, all of started xceivers has called stop and join.
    // All xceivers are either not started or have finished,
    // They can be destructed automatically
  }

 private:
  byte::Mutex xceivers_mutex_;
  std::set<std::shared_ptr<DataXceiver>> xceivers_;
  io::ConcurrentQueue<
      std::pair<XceiverManager::Type, std::shared_ptr<DataXceiver>>>
      task_queue_;
  DataNodeStreamServer* stream_server_;
  byte::Atomic<uint32_t> xceiver_to_be_started_count_;
};

DataNodeStreamServer::DataNodeStreamServer(const std::string& ip, int port,
                                           DataNode* dn) {
  ip_ = ip;
  port_ = port;
  dn_ = dn;
  max_xceiver_count_ = FLAGS_bytestore_hdfs_max_xceiver_count;
  cur_xceiver_count_ = 0;
  estimate_block_size_ = dn->GetDataNodeConfig()->GetEstimateBlockSize();
  listener_ = nullptr;
  xceiver_manager_ = nullptr;
  uint32_t user_priority_num = FLAGS_bytestore_hdfs_user_priority_num;
  for (uint32_t i = 0; i < user_priority_num; ++i) {
    priority_xceiver_count_.push_back(0);
  }
}

DataNodeStreamServer::~DataNodeStreamServer() {
  Stop();
  Join();
  delete listener_;
  delete xceiver_manager_;
}

bool DataNodeStreamServer::Init() {
  if (!io::IPAddress::Parse(&local_address_, ip_.c_str(), port_)) {
    LOG(ERROR) << "Initailize stream server fail "
               << "ip: " << ip_ << ", port: " << port_;
    return false;
  }

  xceiver_manager_ = new XceiverManager(this);
  if (!xceiver_manager_->Start()) {
    LOG(ERROR) << "Start xceiver manager fail";
    return false;
  }

  io::NetFlags flags;
  flags.reuse_addr = true;
  flags.reuse_port = false;
  flags.no_delay = true;
  flags.non_block = true;

  listener_ = io::Listener::Create(local_address_, &flags);
  if (listener_ == nullptr) {
    LOG(ERROR) << "listen " << local_address_.ToString() << " fail";
    return false;
  }

  SetName(byte::StringPrint("StreamSv%d", listener_->Fd()));
  LOG(INFO) << "create listener " << local_address_.ToString()
            << " success, fd:" << listener_->Fd();

  return true;
}

void DataNodeStreamServer::Run() {
  LOG(INFO) << "DataNodeStreamServer::Run"
            << ", is_stop:" << IsStopped()
            << " should_run:" << dn_->ShouldRun();

  const int EVENTS_SIZE = 1024;
  struct epoll_event events[EVENTS_SIZE];
  struct epoll_event ev;
  ev.events = EPOLLIN;
  ev.data.fd = listener_->Fd();

  int epfd = epoll_create(EPOLL_CLOEXEC);
  if (epfd == -1) {
    LOG(ERROR) << "failed to create epoll with error: " << strerror(errno);
    DataNode::kill_self = 1;
    return;
  }

  byte::ScopeGuard guard([epfd] {
    close(epfd);
  });

  if (epoll_ctl(epfd, EPOLL_CTL_ADD, listener_->Fd(), &ev) == -1) {
    LOG(ERROR) << "failed to add listen fd to epoll with error: "
               << strerror(errno);
    DataNode::kill_self = 1;
    return;
  }

  while (!IsStopped() && dn_->ShouldRun()) {
    int nfds = epoll_wait(epfd, events, EVENTS_SIZE, 5000);
    if (nfds < 0) {
      LOG(ERROR) << "failed to execute epoll_wait with error: "
                 << strerror(errno);
      DataNode::kill_self = 1;
      return;
    } else if (nfds == 0) {
      continue;
    }

    assert((nfds == 1) && (event[0].data.fd == listener_->Fd()));
    uint32_t event = events[0].events;

    if (event & EPOLLERR || event & EPOLLHUP) {
      LOG(WARNING) << "unexpcted epoll event happened, just ignore!";
      continue;
    }

    io::Connection* conn = nullptr;
    int ret = listener_->Accept(&conn);
    if (ret == IO_AGAIN) continue;
    if (ret != IO_OK) {
      LOG(ERROR) << "stream_server accept fail " << errno << ":"
                 << strerror(errno);
      DataNode::kill_self = 1;
      return;
    }

    io::NetFlags flags;
    flags.reuse_addr = true;
    flags.no_delay = true;
    flags.Apply(conn->Fd());

    if (cur_xceiver_count_.Value() +
            xceiver_manager_->GetXceiverToBeStartedCount() >=
        max_xceiver_count_.Value()) {
      LOG(ERROR) << "Xceiver count " << cur_xceiver_count_.Value()
                 << " xceiver to be started count "
                 << xceiver_manager_->GetXceiverToBeStartedCount()
                 << " exceeds the limit of concurrent xcievers: "
                 << max_xceiver_count_.Value();
      delete conn;
      continue;
    }

    AddXceiver(conn);
  }

  if (listener_ != nullptr) {
    listener_->Stop();
  }
}

void DataNodeStreamServer::Stop() {
  Thread::Stop();
  Thread::Join();
  // At this point, stream server will not add xceivers to xceiver_manager_
  if (xceiver_manager_ != nullptr) {
    xceiver_manager_->Stop();
  }
}

void DataNodeStreamServer::Join() {
  if (xceiver_manager_ != nullptr) {
    xceiver_manager_->Join();
  }
}

void DataNodeStreamServer::AddXceiver(std::shared_ptr<DataXceiver> xceiver) {
  xceiver_manager_->AddXceiver(xceiver);
}

void DataNodeStreamServer::AddXceiver(io::Connection* conn) {
  AddXceiver(std::make_shared<DataXceiver>(conn, dn_, this));
}

void DataNodeStreamServer::DeleteXceiver(std::shared_ptr<DataXceiver> xceiver) {
  xceiver_manager_->DeleteXceiver(xceiver);
}

void DataNodeStreamServer::SendRestartOOBToPeers() {
  xceiver_manager_->SendRestartOOBToPeers();
}

std::string DataNodeStreamServer::OperationToString(const Operation& op) const {
  switch (op) {
    case Operation::ReadBlock: return "read_block";
    case Operation::CopyBlock: return "copy_block";
    case Operation::WriteBlock: return "write_block_from_client";
    case Operation::WriteBlockInPipeline: return "write_block_in_pipeline";
    case Operation::RespondPacket: return "respond_packet";
    case Operation::ReplaceBlock: return "replace_block";
    case Operation::TransferBlock: return "pipeline_recovery";
    case Operation::CopyBlockAcrossFederation:
      return "copy_block_across_federation";
    case Operation::GetReplicaVisibleLength:
      return "get_replica_visible_length";
    case Operation::GetBlocks: return "get_blocks";
    case Operation::RecoverBlock: return "lease_recovery";
    case Operation::TransferReplica: return "replica_completion";
  }
  return "";
}

void DataNodeStreamServer::AddXceiverOperation(const std::string& bpid,
                                               const Operation& op) {
  byte::MutexLocker guard(&xceiver_map_mutex_);
  auto op_name = OperationToString(op);
  xceiver_map_[bpid][op_name]++;
}

void DataNodeStreamServer::DeleteXceiverOperation(const std::string& bpid,
                                                  const Operation& op) {
  byte::MutexLocker guard(&xceiver_map_mutex_);
  auto op_name = OperationToString(op);
  if (!xceiver_map_.count(bpid) || !xceiver_map_[bpid].count(op_name)) {
    return;
  }
  xceiver_map_[bpid][op_name]--;
  // remove op_name whose value is 0, avoid below 0 values
  if (xceiver_map_[bpid].at(op_name) == 0) xceiver_map_[bpid].erase(op_name);
  if (xceiver_map_.at(bpid).empty()) xceiver_map_.erase(bpid);
}

std::unordered_map<std::string, std::unordered_map<std::string, int32_t>>
DataNodeStreamServer::GetXceiverMap() const {
  byte::MutexLocker guard(&xceiver_map_mutex_);
  return xceiver_map_;
}

exceptions::Exception DataNodeStreamServer::AddPriorityXceiverCount(
    int user_priority) {
  // negative priority means disable throttle for xceiver count
  if (user_priority < 0) {
    return exceptions::Exception();
  }
  if ((uint32_t)user_priority >= priority_xceiver_count_.size()) {
    std::string msg =
        byte::StringPrint("user priority %d is not valid", user_priority);
    return exceptions::Exception(exceptions::E::kIllegalArgumentException, msg);
  }
  uint32_t cur_xceiver_count = ++priority_xceiver_count_[user_priority];
  std::shared_ptr<PriorityXceiverConfig> config =
      dn_->GetPriorityXceiverConfig();
  uint32_t xceiver_count_threshold = 0;
  switch (user_priority) {
    case 0:
      xceiver_count_threshold = config->get_xceiver_count_threshold_p0_();
      break;
    case 1:
      xceiver_count_threshold = config->get_xceiver_count_threshold_p1_();
      break;
    case 2:
      xceiver_count_threshold = config->get_xceiver_count_threshold_p2_();
      break;
  }
  if (dn_->IsPriorityXceiverThrottleEnabled() &&
      cur_xceiver_count > xceiver_count_threshold) {
    std::string msg = byte::StringPrint(
        "Priority %d xceiver count %d exceeds the limit of concurrent "
        "xcievers: %d",
        user_priority, cur_xceiver_count, xceiver_count_threshold);
    return exceptions::Exception(exceptions::E::kIOException, msg);
  }
  return exceptions::Exception();
}

void DataNodeStreamServer::DecPriorityXceiverCount(int user_priority) {
  // negative priority means disable throttle for xceiver count
  if (user_priority < 0 ||
      (uint32_t)user_priority >= priority_xceiver_count_.size()) {
    return;
  }
  --priority_xceiver_count_[user_priority];
}

uint32_t DataNodeStreamServer::GetPriorityXceiverCount(int user_priority) {
  if (user_priority < 0 ||
      (uint32_t)user_priority >= priority_xceiver_count_.size()) {
    return 0;
  }
  return priority_xceiver_count_[user_priority];
}

void PriorityXceiverWrap::Parse(int user_priority, int64_t value,
                                PriorityXceiverWrap* wrap) {
  wrap->user_priority_ = user_priority;
  wrap->value_ = value;
}

std::string PriorityXceiverWrap::ToString() {
  nlohmann::json result;
  result["user_priority"] = user_priority_;
  result["value"] = value_;
  return result.dump();
}

std::shared_ptr<PriorityXceiverConfig> PriorityXceiverConfig::Get() {
  std::shared_ptr<PriorityXceiverConfig> config =
      std::make_shared<PriorityXceiverConfig>();
  config->get_priority_xceiver_throttle_enable_ = []() {
    return FLAGS_bytestore_hdfs_priority_xceiver_throttle_enable;
  };
  config->get_xceiver_count_threshold_p0_ = []() {
    return FLAGS_bytestore_hdfs_max_xceiver_count_p0;
  };
  config->get_xceiver_count_threshold_p1_ = []() {
    return FLAGS_bytestore_hdfs_max_xceiver_count_p1;
  };
  config->get_xceiver_count_threshold_p2_ = []() {
    return FLAGS_bytestore_hdfs_max_xceiver_count_p2;
  };
  return config;
}

bool PriorityXceiverConfig::Set(PriorityXceiverWrap wrap) {
  int user_priority = wrap.user_priority_;
  uint32_t value = wrap.value_;
  if (value < 0) {
    LOG(WARNING) << "set priority xceiver count threshold with negative value:"
                 << value;
    return false;
  }
  switch (user_priority) {
    case 0: FLAGS_bytestore_hdfs_max_xceiver_count_p0 = value; break;
    case 1: FLAGS_bytestore_hdfs_max_xceiver_count_p1 = value; break;
    case 2: FLAGS_bytestore_hdfs_max_xceiver_count_p2 = value; break;
    default:
      LOG(WARNING)
          << "set priority xceiver count threshold with invalid user_priority:"
          << user_priority;
      return false;
  }
  return true;
}

void PriorityXceiverConfig::SetEnable(bool enable) {
  FLAGS_bytestore_hdfs_priority_xceiver_throttle_enable = enable;
}

nlohmann::json PriorityXceiverConfig::ToJson() {
  nlohmann::json result;
  result["enable"] = get_priority_xceiver_throttle_enable_();
  result["xceiver_threshold_p0"] = get_xceiver_count_threshold_p0_();
  result["xceiver_threshold_p1"] = get_xceiver_count_threshold_p1_();
  result["xceiver_threshold_p2"] = get_xceiver_count_threshold_p2_();
  return result;
}

}  // namespace hdfs
}  // namespace chunkserver
}  // namespace bytestore
