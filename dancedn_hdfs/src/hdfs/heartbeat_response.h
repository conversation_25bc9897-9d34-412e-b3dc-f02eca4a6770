// Copyright (c) 2019-present, ByteDance Inc. All rights reserved.

#pragma once

#include <string>
#include <vector>

#include "hdfs/datanode_command.h"
#include "hdfs/nn_ha_status_heartbeat.h"
#include "hdfs/rolling_upgrade_status.h"
#include "hdfs/storage_type.h"

namespace bytestore {
namespace chunkserver {
namespace hdfs {

class HeartbeatResponse {
 public:
  explicit HeartbeatResponse(const std::vector<DatanodeCommand*>& cmds,
                             NNHAStatusHeartbeat ha_status,
                             RollingUpgradeStatus rolling_update_status);
  ~HeartbeatResponse();

  const std::vector<DatanodeCommand*>& Getcommands() const {
    return commands_;
  }
  NNHAStatusHeartbeat GetNameNodeHaState() const {
    return ha_status_;
  }

  RollingUpgradeStatus GetRollingUpgradeStatus() const {
    return rolling_update_status_;
  }

 private:
  std::vector<DatanodeCommand*> commands_;
  NNHAStatusHeartbeat ha_status_;
  RollingUpgradeStatus rolling_update_status_;
};

}  // namespace hdfs
}  // namespace chunkserver
}  // namespace bytestore
