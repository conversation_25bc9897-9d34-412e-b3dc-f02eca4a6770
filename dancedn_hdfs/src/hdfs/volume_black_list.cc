// Copyright (c) 2023-present, ByteDance Inc. All rights reserved.

#include "hdfs/volume_black_list.h"

#include <fstream>
#include <string>

#include "byte/io/file_path.h"
#include "byte/io/local_filesystem.h"
#include "byte/string/format/print.h"
#include "byte/util/scope_guard.h"
#include "gflags/gflags.h"
#include "hdfs/exceptions.h"
#include "hdfs/hdfs_http_service_impl.h"

DECLARE_string(bytestore_hdfs_volume_black_list_file);

namespace bytestore {
namespace chunkserver {
namespace hdfs {

exceptions::Exception VolumeBlackList::Init() {
  black_list_path_ = FLAGS_bytestore_hdfs_volume_black_list_file;
  if (black_list_path_.empty()) {
    return exceptions::Exception(
        exceptions::kIOException,
        "the path of volume black list is not defined.");
  }
  return Read();
}

exceptions::Exception VolumeBlackList::Read() {
  auto e = CheckAndCreate();
  if (!e.OK()) {
    return e;
  }

  {
    byte::RwLock::WriterLocker guard(&rw_lock_);
    black_list_.clear();
    std::ifstream ifs(black_list_path_);
    byte::ScopeGuard auto_close([&ifs] {
      ifs.close();
    });
    if (!ifs.is_open()) {
      std::string msg = byte::StringPrint(
          "cannot open volume black list file: %s", black_list_path_);
      LOG(ERROR) << msg;
      return exceptions::Exception(exceptions::kIOException, msg);
    }
    char read_buf[1024] = {0};
    while (ifs.getline(read_buf, sizeof(read_buf))) {
      black_list_.emplace(read_buf);
    }
  }
  return exceptions::Exception();
}

exceptions::Exception VolumeBlackList::CheckAndCreate() {
  byte::LocalFileSystem local_fs;
  if (!local_fs.Exists(black_list_path_).ok()) {
    std::string dir_path = byte::FilePath::GetFileDir(black_list_path_);
    byte::Status s = local_fs.CreateDir(dir_path, byte::CreateOptions());
    if (!s.ok()) {
      std::string msg = byte::StringPrint(
          "create directory of volume black list file failed: %s", dir_path);
      LOG(ERROR) << msg;
      return exceptions::Exception(exceptions::kIOException, msg);
    }
    LOG(INFO) << "directory of volume black list file created: " << dir_path;
    std::ofstream ofs(black_list_path_,
                      std::ofstream::out | std::ofstream::trunc);
    byte::ScopeGuard auto_close([&ofs] {
      ofs.close();
    });
    if (!ofs.is_open()) {
      std::string msg = byte::StringPrint(
          "cannot open volume black list file: %s", black_list_path_);
      LOG(ERROR) << msg;
      return exceptions::Exception(exceptions::kIOException, msg);
    }
    LOG(INFO) << "volume black list file created: " + black_list_path_;
  }
  return exceptions::Exception();
}

exceptions::Exception VolumeBlackList::Append(std::string volume_path) {
  LOG(INFO) << "try to append volume black list file: " << volume_path;
  {
    byte::RwLock::ReaderLocker guard(&rw_lock_);
    if (black_list_.find(volume_path) != black_list_.end()) {
      std::string msg = byte::StringPrint(
          "volume already exists in volume black list: %s", volume_path);
      LOG(INFO) << msg;
      return exceptions::Exception(exceptions::kNoException, msg);
    }
  }
  std::string new_file_name = black_list_path_ + ".tmp";
  std::ofstream new_ofs(new_file_name,
                        std::ofstream::out | std::ofstream::trunc);
  byte::ScopeGuard auto_close([&new_ofs] {
    new_ofs.close();
  });
  if (!new_ofs.is_open()) {
    std::string msg = byte::StringPrint(
        "cannot open the temporary volume black list file: %s", new_file_name);
    LOG(ERROR) << msg;
    return exceptions::Exception(exceptions::kIOException, msg);
  }
  {
    /* copy -> append -> rename back */
    byte::RwLock::ReaderLocker guard(&rw_lock_);
    for (auto& line : black_list_) {
      new_ofs << line << std::endl;
    }
    new_ofs << volume_path << std::endl;
  }
  auto result = rename(new_file_name.c_str(), black_list_path_.c_str());
  if (result) {
    std::string msg =
        byte::StringPrint("failed rename new volume black list file %s to %s",
                          new_file_name, black_list_path_);
    LOG(ERROR) << msg;
    return exceptions::Exception(exceptions::kIOException, msg);
  }
  {
    /* update in mem */
    byte::RwLock::WriterLocker guard(&rw_lock_);
    black_list_.emplace(volume_path);
  }
  LOG(INFO) << "append volume black list file successfully: " << volume_path;
  return exceptions::Exception();
}

exceptions::Exception VolumeBlackList::Erase(std::string volume_path) {
  LOG(INFO) << "try to erase volume black list file: " << volume_path;
  {
    byte::RwLock::ReaderLocker guard(&rw_lock_);
    if (black_list_.find(volume_path) == black_list_.end()) {
      std::string msg = byte::StringPrint(
          "volume does not exist in volume black list: %s", volume_path);
      LOG(INFO) << msg;
      return exceptions::Exception(exceptions::kNoException, msg);
    }
  }
  std::string new_file_name = black_list_path_ + ".tmp";
  std::ofstream new_ofs(new_file_name,
                        std::ofstream::out | std::ofstream::trunc);
  byte::ScopeGuard auto_close([&new_ofs] {
    new_ofs.close();
  });
  if (!new_ofs.is_open()) {
    std::string msg = byte::StringPrint(
        "cannot open the temporary volume black list file: %s", new_file_name);
    LOG(ERROR) << msg;
    return exceptions::Exception(exceptions::kIOException, msg);
  }
  {
    byte::RwLock::ReaderLocker guard(&rw_lock_);
    /* read(erase) -> rename back */
    for (auto& line : black_list_) {
      if (line == volume_path) {
        continue;
      }
      new_ofs << line << std::endl;
    }
  }
  auto result = rename(new_file_name.c_str(), black_list_path_.c_str());
  if (result) {
    std::string msg =
        byte::StringPrint("failed rename new volume black list file %s to %s",
                          new_file_name, black_list_path_);
    LOG(ERROR) << msg;
    return exceptions::Exception(exceptions::kIOException, msg);
  }
  {
    /* update in mem */
    byte::RwLock::WriterLocker guard(&rw_lock_);
    black_list_.erase(volume_path);
  }
  LOG(INFO) << "erase volume black list file successfully: " << volume_path;
  return exceptions::Exception();
}

uint32_t VolumeBlackList::GetSize() {
  byte::RwLock::ReaderLocker guard(&rw_lock_);
  return black_list_.size();
}

std::set<std::string> VolumeBlackList::GetBlackListSet() {
  byte::RwLock::ReaderLocker guard(&rw_lock_);
  return black_list_;
}

}  // namespace hdfs
}  // namespace chunkserver
}  // namespace bytestore
