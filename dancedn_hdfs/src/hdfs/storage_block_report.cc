// Copyright (c) 2019-present, ByteDance Inc. All rights reserved.

#include "hdfs/storage_block_report.h"

namespace bytestore {
namespace chunkserver {
namespace hdfs {

StorageBlockReport::StorageBlockReport(
    const DatanodeStorage& storage,
    std::shared_ptr<std::vector<uint64_t>> blocks)
    : storage_(storage), blocks_(blocks) {}

StorageBlockReport::~StorageBlockReport() {}

}  // namespace hdfs
}  // namespace chunkserver
}  // namespace bytestore
