// Copyright (c) 2019-present, ByteDance Inc. All rights reserved.

#include "hdfs/packet_receiver.h"

#include <memory>

#include "byte/include/assert.h"
#include "byte/string/format/print.h"
#include "gflags/gflags.h"
#include "hdfs/io/connection.h"
#include "hdfs/io/define.h"
#include "hdfs/io/io_buf.h"
#include "hdfs/packet_header.h"

DECLARE_uint32(bytestore_hdfs_max_packet_size);

namespace bytestore {
namespace chunkserver {
namespace hdfs {

PacketReceiver::PacketReceiver()
    : packet_header_(nullptr),
      checksum_slice_(nullptr),
      data_slice_(nullptr),
      packet_buf_(nullptr) {}

PacketReceiver::~PacketReceiver() {
  delete packet_header_;
  io::IOChunk::Destroy(checksum_slice_);
  io::IOChunk::Destroy(data_slice_);
  io::IOChunk::Destroy(packet_buf_);
}

exceptions::Exception PacketReceiver::ReceiveNextPacket(io::Connection* in) {
  // Each packet looks like:
  //   PLEN    HLEN      HEADER     CHECKSUMS  DATA
  //   32-bit  16-bit   <protobuf>  <variable length>
  //
  // PLEN:      Payload length
  //            = length(PLEN) + length(CHECKSUMS) + length(DATA)
  //            This length includes its own encoded length in
  //            the sum for historical reasons.
  //
  // HLEN:      Header length
  //            = length(HEADER)
  //
  // HEADER:    the actual packet header fields, encoded in protobuf
  // CHECKSUMS: the crcs for the data chunk. May be missing if
  //            checksums were not requested
  // DATA       the actual block data
  BYTE_ASSERT(packet_header_ == nullptr ||
              !packet_header_->IsLastPacketInBlock());

  in->ReadBytes(PacketHeader::PKT_LENGTHS_LEN);
  io::ChunkPtr length_buf(in->ReadBuf()->Pop(), io::IOChunk::Destroy);
  if (length_buf == nullptr ||
      !length_buf->EnoughDataLength(PacketHeader::PKT_LENGTHS_LEN)) {
    return exceptions::Exception(exceptions::E::kIOException,
                                 "Invalid packet length, less than 6 bytes.");
  }
  int32_t packet_len = length_buf->ReadFixed32BE();
  int16_t header_len = length_buf->ReadFixed16BE();
  uint32_t total_len = packet_len + header_len;
  int32_t checksum_data_len = packet_len - 4;

  if (packet_len < 4 || header_len < 0 ||
      total_len > FLAGS_bytestore_hdfs_max_packet_size) {
    std::string msg = byte::StringPrint(
        "Incorrect value of packet size, "
        "packet_len=%d, header_len=%d",
        packet_len, header_len);
    return exceptions::Exception(exceptions::E::kIOException, msg);
  }
  LOG(DEBUG) << "packet_len(4+checksum+data):" << packet_len
             << " header_len:" << header_len;

  // Destructor previous pointers before reuse them
  delete packet_header_;
  io::IOChunk::Destroy(checksum_slice_);
  io::IOChunk::Destroy(data_slice_);
  io::IOChunk::Destroy(packet_buf_);
  packet_header_ = nullptr;
  checksum_slice_ = nullptr;
  data_slice_ = nullptr;
  packet_buf_ = nullptr;

  packet_buf_ = new io::IOChunk(header_len + PacketHeader::PKT_LENGTHS_LEN +
                                checksum_data_len);
  in->ReadBytes(header_len);
  io::ChunkPtr header_buf(in->ReadBuf()->Pop(), io::IOChunk::Destroy);

  if (header_buf == nullptr || !header_buf->EnoughDataLength(header_len)) {
    uint32_t actual_len = header_buf == nullptr ? 0 : header_buf->Length();
    std::string msg = byte::StringPrint(
        "Inconsistent heard length, "
        "got %d bytes while expected %d bytes.",
        actual_len, header_len);
    return exceptions::Exception(exceptions::E::kIOException, msg);
  }
  packet_header_ = new PacketHeader(packet_len);
  if (packet_header_->ReadFromChunk(header_buf.get()) != IO_OK) {
    LOG(ERROR) << "parse packet header failed";
    std::string msg = "parse packet header failed";
    return exceptions::Exception(exceptions::E::kIOException, msg);
  }
  packet_header_->PutInChunk(packet_buf_);

  int32_t data_len = packet_header_->GetDataLength();
  int32_t checksum_len = checksum_data_len - data_len;

  if (checksum_len < 0 || data_len < 0) {
    std::string msg = byte::StringPrint(
        "Incorrect value of checksum_len or data_len, "
        "checksum_len=%d, data_len=%d",
        checksum_len, data_len);
    return exceptions::Exception(exceptions::E::kIOException, msg);
  }

  in->ReadBytes(checksum_len);
  checksum_slice_ = in->ReadBuf()->Pop();
  if (checksum_slice_ == nullptr ||
      !checksum_slice_->EnoughDataLength(checksum_len)) {
    uint32_t actual_len =
        checksum_slice_ == nullptr ? 0 : checksum_slice_->Length();
    std::string msg = byte::StringPrint(
        "Inconsistent checksum length, "
        "got %d bytes while expected %d bytes.",
        actual_len, checksum_len);
    return exceptions::Exception(exceptions::E::kIOException, msg);
  }
  packet_buf_->WriteBytes(checksum_slice_->Data(), checksum_len);

  in->ReadBytes(data_len);
  data_slice_ = in->ReadBuf()->Pop();
  if (data_slice_ == nullptr || !data_slice_->EnoughDataLength(data_len)) {
    uint32_t actual_len = data_slice_ == nullptr ? 0 : data_slice_->Length();
    std::string msg = byte::StringPrint(
        "Inconsistent data length, "
        "got %d bytes while expected %d bytes.",
        actual_len, data_len);
    return exceptions::Exception(exceptions::E::kIOException, msg);
  }
  packet_buf_->WriteBytes(data_slice_->Data(), data_len);

  return exceptions::Exception();
}

exceptions::Exception PacketReceiver::MirrorPacketTo(io::Connection* out) {
  BYTE_ASSERT(packet_buf_ != nullptr && packet_buf_->Length() > 0);
  out->WriteBuf()->Append(packet_buf_);
  packet_buf_ = nullptr;
  auto flag = out->Write(true);
  if (flag != IO_OK) {
    return exceptions::Exception(exceptions::E::kIOException,
                                 std::to_string(flag));
  }
  return exceptions::Exception();
}

}  // namespace hdfs
}  // namespace chunkserver
}  // namespace bytestore
