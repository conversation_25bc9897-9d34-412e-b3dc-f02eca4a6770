// Copyright (c) 2019-present, ByteDance Inc. All rights reserved.

#include "hdfs/inter_datanode_rpc.h"

#include "byte/include/byte_log.h"
#include "hdfs/io/address.h"
#include "hdfs/message/rpc_response_message.h"
#include "hdfs/proto/RpcHeader.pb.h"
#include "hdfs/util.h"

namespace bytestore {
namespace chunkserver {
namespace hdfs {

const char InterDatanodeRpc::PROTOCOL_NAME[] =
    "org.apache.hadoop.hdfs.server.protocol.InterDatanodeProtocol";
const uint32_t InterDatanodeRpc::PROTOCOL_VERSION = 1;

exceptions::Exception InterDatanodeRpc::Call(io::Method method,
                                             const google::protobuf::Message* r,
                                             google::protobuf::Message** res) {
  auto rh = CreateRequestHeader(method);
  message::RpcResponseMessage* ret = nullptr;
  auto e = io_->SendRequest(method, &addr_, rh, r, &ret, timeout_);
  delete rh;
  if (!e.OK()) {
    LOG(ERROR) << "send " << hdfs::io::MethodToString(method)
               << " to datenode failed exception : " << e.ToString();
    return e;
  }

  if (ret->RpcHeader()->status() !=
      hadoop::common::RpcResponseHeaderProto_RpcStatusProto_SUCCESS) {
    auto e = ParseHeaderException(ret->RpcHeader());
    delete ret;
    return e;
  }

  auto body = ret->ReleaseBody();

  delete ret;
  *res = body;
  return exceptions::Exception();
}

hadoop::common::RequestHeaderProto* InterDatanodeRpc::CreateRequestHeader(
    io::Method method_id) {
  auto proto = new hadoop::common::RequestHeaderProto();
  proto->set_methodname(io::MethodToString(method_id));
  proto->set_clientprotocolversion(PROTOCOL_VERSION);
  proto->set_declaringclassprotocolname(PROTOCOL_NAME);
  return proto;
}

}  // namespace hdfs
}  // namespace chunkserver
}  // namespace bytestore
