// Copyright (c) 2019-present, ByteDance Inc. All rights reserved.

#pragma once

#include <string>

#include "hdfs/storage_type.h"

namespace hadoop {
namespace hdfs {
class DatanodeInfoWithStorageProto;
}
}  // namespace hadoop

namespace bytestore {
namespace chunkserver {
namespace hdfs {

class DatanodeInfo;

class DatanodeInfoWithStorage {
 public:
  DatanodeInfoWithStorage(const DatanodeInfo* from,
                          const std::string& storage_id,
                          StorageType storage_type);
  ~DatanodeInfoWithStorage();

  std::string GetStorageID() const {
    return storage_id_;
  }
  StorageType GetStorageType() const {
    return storage_type_;
  }
  DatanodeInfo* GetDatanodeInfo() const {
    return datanode_info_;
  }

  std::string ToString() const;
  exceptions::Exception ToProto(
      hadoop::hdfs::DatanodeInfoWithStorageProto* proto) const;

 private:
  DatanodeInfo* datanode_info_;
  std::string storage_id_;
  StorageType storage_type_;
};

}  // namespace hdfs
}  // namespace chunkserver
}  // namespace bytestore
