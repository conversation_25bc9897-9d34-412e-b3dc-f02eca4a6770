// Copyright (c) 2019-present, ByteDance Inc. All rights reserved.

#pragma once

#include <memory>
#include <string>

#include "hdfs/constants.h"
#include "hdfs/exceptions.h"

namespace hadoop {
namespace hdfs {
class StorageInfoProto;
}
}  // namespace hadoop

namespace bytestore {
namespace chunkserver {
namespace hdfs {

class StorageInfo {
 public:
  explicit StorageInfo(NodeType type);
  StorageInfo(int layout_version, int namespace_id,
              const std::string& cluster_id, uint64_t ctime, NodeType type);
  StorageInfo(const StorageInfo& info);
  virtual ~StorageInfo() {}

  StorageInfo* Clone() const;

  int GetLayoutVersion() const {
    return layout_version_;
  }
  int GetNamespaceID() const {
    return namespace_id_;
  }
  std::string GetClusterID() const {
    return cluster_id_;
  }
  uint64_t GetCTime() const {
    return ctime_;
  }

  std::string GetRegistrationID();
  std::string ToString() const;
  bool Equals(const StorageInfo* rhs) const;

 public:
  // convert to protobuf method
  exceptions::Exception To<PERSON>roto(hadoop::hdfs::StorageInfoProto* proto) const;

  static StorageInfo* ParseProto(const hadoop::hdfs::StorageInfoProto* proto);

 private:
  int layout_version_;      // layout version of the storage data
  int namespace_id_;        // id of the file system
  uint64_t ctime_;          // creation time of the file system state
  NodeType storage_type_;   // type of the node using this storage
  std::string cluster_id_;  // id of the cluster
};

}  // namespace hdfs
}  // namespace chunkserver
}  // namespace bytestore
