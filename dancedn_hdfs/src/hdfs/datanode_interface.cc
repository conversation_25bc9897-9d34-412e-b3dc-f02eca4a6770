// Copyright (c) 2018-present, ByteDance Inc. All rights reserved.

#include "hdfs/datanode_interface.h"

#include <memory>
#include <set>
#include <string>
#include <vector>

#include "chunkserver/common/chunkserver_types.h"
#include "chunkserver/disk.h"
#include "chunkserver/tiering/migration_table.h"

namespace bytestore {
namespace chunkserver {

DatanodeInterface::DatanodeInterface(ChunkServerConfig* cs_config, Env* cs_env)
    : cs_config_(cs_config), cs_env_(cs_env) {
  CSIOServiceOptions opts;
  csmgr_service_impl_.reset(new CSManagementServiceImpl(cs_env_));
  csio_service_impl_.reset(new CSIOServiceImpl<RpcType::BRPC>(cs_env_, opts));
  csmeta_service_impl_.reset(new CSMetaServiceImpl<RpcType::BRPC>(cs_env_));
}

DatanodeInterface::~DatanodeInterface() {}

const DiskIdConfMap* DatanodeInterface::GetDiskConf() {
  if (cs_config_) {
    return &cs_config_->GetDiskConf();
  } else {
    return nullptr;
  }
}

std::string DatanodeInterface::GetFullHostAddr() const {
  return cs_env_->GetFullHostAddr();
}

Errorcode DatanodeInterface::GetCSCapacityInBytes(uint64_t* capacity_in_bytes) {
  return cs_env_->GetDevMgr()->GetCSCapacityInBytes(capacity_in_bytes);
}

Errorcode DatanodeInterface::GetDiskCapacityInBytes(
    uint32_t disk_id, uint64_t* capacity_in_bytes) {
  return cs_env_->GetDevMgr()->GetDiskCapacityInBytes(disk_id,
                                                      capacity_in_bytes);
}

Errorcode DatanodeInterface::GetUsedSizeInBytes(uint32_t disk_id,
                                                uint64_t* used_size_in_bytes) {
  return cs_env_->GetDevMgr()->GetUsedSizeInBytes(disk_id, used_size_in_bytes);
}

Errorcode DatanodeInterface::GetFreeSizeInBytes(uint32_t disk_id,
                                                uint64_t* free_size_in_bytes) {
  return cs_env_->GetDevMgr()->GetFreeSizeInBytes(disk_id, free_size_in_bytes);
}

Errorcode DatanodeInterface::GetDiskInfos(
    std::vector<PerDiskInfo>* disk_infos) {
  auto now_ms = byte::GetCurrentTimeInMs();
  if (last_update_time_ms_ == 0) {
    byte::RwLock::WriterLocker locker(&lock_);
    std::vector<PerDiskInfo> tmp;
    cs_env_->GetDevMgr()->GetDiskInfos(&tmp, false);
    disk_infos_.swap(tmp);
    last_update_time_ms_ = now_ms;
  } else if (now_ms > (UPDATE_TIME_INTERVAL_MS + last_update_time_ms_)) {
    last_update_time_ms_ = now_ms;
    std::vector<PerDiskInfo> tmp;
    cs_env_->GetDevMgr()->GetDiskInfos(&tmp, false);
    byte::RwLock::WriterLocker locker(&lock_);
    disk_infos_.swap(tmp);
  }

  byte::RwLock::ReaderLocker locker(&lock_);
  *disk_infos = disk_infos_;
  return BYTESTORE_OK;
}

uint32_t DatanodeInterface::GetDiskCount() const {
  return cs_env_->GetDevMgr()->GetDiskCount();
}

bool DatanodeInterface::IsDiskClosed(Disk* disk) {
  if (!disk) {
    return true;
  }
  DiskStatus status;
  GetDiskStatus(disk, &status);
  return closed_disk_status_set_.find(status) != closed_disk_status_set_.end();
}

bool DatanodeInterface::IsDiskClosed(DiskId disk_id) {
  Disk* disk = cs_env_->GetDisk(disk_id);
  return IsDiskClosed(disk);
}

bool DatanodeInterface::IsDiskReadonly(Disk* disk) {
  if (!disk) {
    return false;
  }
  DiskStatus status;
  GetDiskStatus(disk, &status);
  return readonly_disk_status_set_.find(status) !=
         readonly_disk_status_set_.end();
}

bool DatanodeInterface::IsDiskReadonly(DiskId disk_id) {
  Disk* disk = cs_env_->GetDisk(disk_id);
  return IsDiskReadonly(disk);
}

bool DatanodeInterface::GetDiskStatus(Disk* disk, DiskStatus* status) const {
  if (!disk) {
    return false;
  }
  *status = disk->GetDiskStatus();
  return true;
}

bool DatanodeInterface::GetDiskStatus(DiskId disk_id,
                                      DiskStatus* status) const {
  Disk* disk = cs_env_->GetDisk(disk_id);
  return GetDiskStatus(disk, status);
}

bool DatanodeInterface::CloseDisk(Disk* disk) {
  return SetDiskStatus(disk, DISK_DECOMMISSION);
}

bool DatanodeInterface::CloseDisk(DiskId disk_id) {
  return SetDiskStatus(disk_id, DISK_DECOMMISSION);
}

bool DatanodeInterface::SetDiskReadonly(Disk* disk) {
  // DISK_DECOMMISSIONING can set chunk invisible,
  // but DISK_READONLY cannot delete chunk or set chunk invisible
  return SetDiskStatus(disk, DISK_DECOMMISSIONING);
}

bool DatanodeInterface::SetDiskReadonly(DiskId disk_id) {
  return SetDiskStatus(disk_id, DISK_DECOMMISSIONING);
}

bool DatanodeInterface::SetDiskStatus(Disk* disk, DiskStatus status) {
  if (!disk) {
    return false;
  }
  return disk->SetDiskStatus(status);
}

bool DatanodeInterface::SetDiskStatus(DiskId disk_id, DiskStatus status) {
  Disk* disk = cs_env_->GetDisk(disk_id);
  return SetDiskStatus(disk, status);
}

Errorcode DatanodeInterface::GetTargetDiskIdForTieredChunk(
    const DiskId& disk_id, const ChunkId& chunk_id, DiskId* target_disk_id) {
  MigrationInfo info;
  MigrationTableManager* manager = cs_env_->GetMigrationTableManager();
  // return BYTESTORE_OK iff type = PLM_STORAGE_TIERED_NVME_SSD_HDD and disk =
  // CDT_NVME_SSD
  Errorcode rc = manager->FindInfo(disk_id, chunk_id, &info);
  if (rc != BYTESTORE_OK) {
    return rc;
  }
  if (info.state_ != MIGRATION_DONE) {
    return BYTESTORE_ERR_CHUNKSERVER_CHUNK_NOT_FOUND;
  }
  Disk* target_disk = cs_env_->GetDisk(info.target_disk_id_);
  if (target_disk == nullptr) {
    LOG(INFO) << "Target disk not found"
              << " chunk_id:" << chunk_id.ToString() << " disk_id:" << disk_id
              << " target_disk_id:" << info.target_disk_id_;
    return BYTESTORE_ERR_CHUNKSERVER_DISK_ID_NOT_FOUND;
  }
  *target_disk_id = target_disk->GetDiskId();
  return BYTESTORE_OK;
}

void DatanodeInterface::GetChunkMeta(
    google::protobuf::RpcController* controller,
    const GetChunkMetaRequest* request, GetChunkMetaResponse* response,
    google::protobuf::Closure* done) {
  csmeta_service_impl_->GetChunkMeta(controller, request, response, done);
}

void DatanodeInterface::CreateChunk(google::protobuf::RpcController* controller,
                                    const CreateChunkRequest* request,
                                    CreateChunkResponse* response,
                                    google::protobuf::Closure* done) {
  csio_service_impl_->CreateChunk(controller, request, response, done);
}

void DatanodeInterface::ReadChunk(google::protobuf::RpcController* controller,
                                  const ReadChunkRequest* request,
                                  ReadChunkResponse* response,
                                  google::protobuf::Closure* done) {
  csio_service_impl_->ReadChunk(controller, request, response, done);
}

void DatanodeInterface::WriteChunk(google::protobuf::RpcController* controller,
                                   const WriteChunkRequest* request,
                                   WriteChunkResponse* response,
                                   google::protobuf::Closure* done) {
  csio_service_impl_->WriteChunk(controller, request, response, done);
}

void DatanodeInterface::FreezeChunk(google::protobuf::RpcController* controller,
                                    const FreezeChunkRequest* request,
                                    FreezeChunkResponse* response,
                                    google::protobuf::Closure* done) {
  csio_service_impl_->FreezeChunk(controller, request, response, done);
}

void DatanodeInterface::HardLinkChunk(
    google::protobuf::RpcController* controller,
    const HardLinkChunkRequest* request, HardLinkChunkResponse* response,
    google::protobuf::Closure* done) {
  csio_service_impl_->HardLinkChunk(controller, request, response, done);
}

void DatanodeInterface::SetXATTRChunk(
    google::protobuf::RpcController* controller,
    const CSSetXattrRequest* request, CSSetXattrResponse* response,
    google::protobuf::Closure* done) {
  csio_service_impl_->SetXATTRChunk(controller, request, response, done);
}

void DatanodeInterface::GetXATTRChunk(
    google::protobuf::RpcController* controller,
    const CSGetXattrRequest* request, CSGetXattrResponse* response,
    google::protobuf::Closure* done) {
  csio_service_impl_->GetXATTRChunk(controller, request, response, done);
}

void DatanodeInterface::DeleteChunks(
    google::protobuf::RpcController* controller,
    const ::bytestore::DeleteChunksRequest* request,
    bytestore::DeleteChunksResponse* response,
    google::protobuf::Closure* done) {
  csmgr_service_impl_->DeleteChunks(controller, request, response, done);
}

void DatanodeInterface::TruncateChunk(
    google::protobuf::RpcController* controller,
    const TruncateChunkRequest* request, TruncateChunkResponse* response,
    google::protobuf::Closure* done) {
  csio_service_impl_->TruncateChunk(controller, request, response, done);
}

void DatanodeInterface::SyncChunk(google::protobuf::RpcController* controller,
                                  const SyncChunkRequest* request,
                                  SyncChunkResponse* response,
                                  google::protobuf::Closure* done) {
  csio_service_impl_->SyncChunk(controller, request, response, done);
}

void DatanodeInterface::ReadaheadChunk(
    google::protobuf::RpcController* controller,
    const ReadaheadChunkRequest* request, ReadaheadChunkResponse* response,
    google::protobuf::Closure* done) {
  csio_service_impl_->ReadaheadChunk(controller, request, response, done);
}

void DatanodeInterface::AddDisk(google::protobuf::RpcController* controller,
                                const ::bytestore::CsAddDiskRequest* request,
                                bytestore::CsAddDiskResponse* response,
                                google::protobuf::Closure* done) {
  csmgr_service_impl_->AddDisk(controller, request, response, done);
}

void DatanodeInterface::RemoveDisk(
    google::protobuf::RpcController* controller,
    const ::bytestore::CsRemoveDiskRequest* request,
    bytestore::CsRemoveDiskResponse* response,
    google::protobuf::Closure* done) {
  csmgr_service_impl_->RemoveDisk(controller, request, response, done);
}

}  // namespace chunkserver
}  // namespace bytestore
