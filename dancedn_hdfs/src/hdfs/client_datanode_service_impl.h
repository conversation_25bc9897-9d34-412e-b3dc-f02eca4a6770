// Copyright (c) 2019-present, ByteDance Inc. All rights reserved.

#pragma once

#include <functional>
#include <memory>
#include <string>
#include <unordered_map>
#include <vector>

#include "hdfs/exceptions.h"
#include "hdfs/opstats/op_key.h"
#include "hdfs/opstats/operation.h"
#include "hdfs/proto/ClientDatanodeProtocol.pb.h"

namespace hadoop {
namespace common {
class RpcRequestHeaderProto;
class RequestHeaderProto;
class RpcResponseHeaderProto;
}  // namespace common
}  // namespace hadoop

namespace hadoop {
namespace hdfs {
class RefreshNamenodesResponseProto;
class ShutdownDatanodeResponseProto;
class TriggerBlockReportResponseProto;
}  // namespace hdfs
}  // namespace hadoop

namespace bytestore {
namespace chunkserver {
namespace hdfs {

class DataNode;

namespace message {
class RpcRequestMessage;
class RpcResponseMessage;
}  // namespace message

class ClientDatanodeServiceImpl final {
 public:
  ClientDatanodeServiceImpl();
  explicit ClientDatanodeServiceImpl(DataNode* datanode);
  ~ClientDatanodeServiceImpl() {
    // delete datanode_ by DataNode
  }

  void SetDataNode(DataNode* datanode) {
    datanode_ = datanode;
  }

  message::RpcRequestMessage* NewRequest(const std::string& name);
  message::RpcResponseMessage* CallMethod(message::RpcRequestMessage* r);

 public:
  static const char PROTOCOL_NAME[];
  static const uint32_t PROTOCOL_VERSION;

 private:
  using RpcResponse = message::RpcResponseMessage;
  using RpcRequest = message::RpcRequestMessage;
  using Method = RpcResponse* (ClientDatanodeServiceImpl::*)(RpcRequest*);

  typedef RpcRequest* (*Builder)();

  using Fn = std::function<RpcResponse*(RpcRequest*)>;

 private:
  OpKey BuildKey(message::RpcRequestMessage* r, const std::string& bpid,
                 uint64_t block_id, const Operation& op);

  void Initialize();

  hadoop::common::RpcResponseHeaderProto* ProcessExceptionAfterCallMethod(
      const exceptions::Exception& e,
      const hadoop::common::RpcRequestHeaderProto* request_header) const;

  void Register(const std::string& name, const Method& method,
                const Builder& builder);

  RpcResponse* getReplicaVisibleLength(RpcRequest* r);

  RpcResponse* getReplicaVisibleLengthV2(RpcRequest* r);

  RpcResponse* refreshNamenodes(RpcRequest* r);

  RpcResponse* deleteBlockPool(RpcRequest* r);

  RpcResponse* getBlockLocalPathInfo(RpcRequest* r);

  RpcResponse* getHdfsBlockLocations(RpcRequest* r);

  RpcResponse* shutdownDatanode(RpcRequest* r);

  RpcResponse* getDatanodeInfo(RpcRequest* r);

  RpcResponse* getReconfigurationStatus(RpcRequest* r);

  RpcResponse* startReconfiguration(RpcRequest* r);

  RpcResponse* listReconfigurableProperties(RpcRequest* r);

  RpcResponse* triggerBlockReport(RpcRequest* r);

  RpcResponse* getBlocks(RpcRequest* r);

  RpcResponse* initReplicaRecovery(RpcRequest* request);

  RpcResponse* updateReplicaUnderRecovery(RpcRequest* request);

 private:
  std::unordered_map<std::string, int> method_ids_;
  std::vector<ClientDatanodeServiceImpl::Method> methods_;
  std::vector<ClientDatanodeServiceImpl::Builder> builders_;
  DataNode* datanode_;
};

}  // namespace hdfs
}  // namespace chunkserver
}  // namespace bytestore
