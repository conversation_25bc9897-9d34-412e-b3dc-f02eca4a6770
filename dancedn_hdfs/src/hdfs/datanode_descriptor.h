// Copyright (c) 2019-present, ByteDance Inc. All rights reserved.

#pragma once

#include <map>
#include <memory>
#include <string>
#include <vector>

#include "byte/concurrent/mutex.h"

namespace bytestore {
namespace chunkserver {
namespace hdfs {

class DatanodeInfo;
class DatanodeID;
class StorageReport;
class VolumeFailureSummary;
class DiskStatSummary;
class BlockReportContext;
class DecommissioningStatus;
class DatanodeStorageInfo;
class DatanodeStorage;

class DatanodeDescriptor {
 public:
  explicit DatanodeDescriptor(std::shared_ptr<DatanodeID> datanode_id);
  DatanodeDescriptor(std::shared_ptr<DatanodeID> datanode_id,
                     const std::string& network_location);
  ~DatanodeDescriptor();

  DatanodeInfo* GetDatanodeInfo() const {
    return datanode_info_;
  }

 private:
  void UpdateHeartbeatState(const std::vector<StorageReport*>& reports,
                            uint32_t xceiver_count, uint32_t volume_failures,
                            const VolumeFailureSummary* volume_failure_summary,
                            const DiskStatSummary* disk_stat_summary);
  DatanodeStorageInfo* UpdateStorage(const DatanodeStorage* storage);
  void UpdateFailedStorage();
  void PruneStorageMap(const std::vector<StorageReport*>& reports);

 private:
  DatanodeInfo* datanode_info_;
  // DecommissioningStatus* decommissioning_status_;

  bool heartheated_since_registration_;
  uint32_t volume_failures_;

  std::unique_ptr<const VolumeFailureSummary> volume_failure_summary_;
  DiskStatSummary* disk_stat_summary_;

  std::map<std::string, DatanodeStorageInfo*> failed_storage_infos_;
  std::map<std::string, DatanodeStorageInfo*> storage_map_;
  mutable byte::Mutex map_mutex_;
};

}  // namespace hdfs
}  // namespace chunkserver
}  // namespace bytestore
