// Copyright (c) 2018, ByteDance Inc. All rights reserved.

#include "hdfs/finalized_replica.h"

#include <sstream>
#include <string>

#include "byte/string/format/print.h"
#include "hdfs/extended_block.h"
#include "hdfs/replica_info.h"
#include "hdfs/store.h"

namespace bytestore {
namespace chunkserver {
namespace hdfs {

FinalizedReplica::FinalizedReplica() : unlinked_(false) {}

FinalizedReplica::FinalizedReplica(const ExtendedBlock* block,
                                   const std::string& storage_uuid,
                                   bool is_transient, bool checksum_enabled,
                                   uint16_t disk_id, PlacementStorageType type)
    : ReplicaInfo(block, storage_uuid, is_transient, checksum_enabled, disk_id,
                  type),
      unlinked_(false) {}

FinalizedReplica::FinalizedReplica(const std::string& bpid, uint64_t block_id,
                                   uint64_t len, uint64_t gs,
                                   const std::string& storage_uuid,
                                   bool is_transient, bool checksum_enabled,
                                   uint16_t disk_id, PlacementStorageType type)
    : ReplicaInfo(bpid, block_id, len, gs, storage_uuid, is_transient,
                  checksum_enabled, disk_id, type),
      unlinked_(false) {}

FinalizedReplica::FinalizedReplica(const FinalizedReplica& from)
    : ReplicaInfo(from), unlinked_(from.IsUnlinked()) {}

int64_t FinalizedReplica::GetVisibleLength() const {
  return GetBlock()->GetNumBytes();
}

uint64_t FinalizedReplica::GetBytesOnDisk() const {
  return GetBlock()->GetNumBytes();
}

std::string FinalizedReplica::ToString() const {
  std::string str;
  str = byte::StringPrint(
      "FinalizedReplica, %s, %s\n"
      "  getNumBytes()      = %ld\n"
      "  getBytesOnDisk()   = %ld\n"
      "  getVisibleLength() = %ld\n"
      "  getDiskId()        = %u\n"
      "  unlinked           = %b",
      GetBlock()->ToString(), GetStateString(), GetNumBytes(), GetBytesOnDisk(),
      GetVisibleLength(), GetDiskId(), unlinked_);
  return str;
}

}  // namespace hdfs
}  // namespace chunkserver
}  // namespace bytestore
