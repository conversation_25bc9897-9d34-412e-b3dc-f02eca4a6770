// Copyright (c) 2019-present, ByteDance Inc. All rights reserved.

#pragma once

#include <memory>
#include <string>

#include "hdfs/exceptions.h"
#include "hdfs/security/exported_block_keys.h"
namespace hadoop {
namespace hdfs {
namespace datanode {

class DatanodeRegistrationProto;
class RegisterDatanodeRequestProto;

}  // namespace datanode
}  // namespace hdfs
}  // namespace hadoop

namespace bytestore {
namespace chunkserver {
namespace hdfs {

class DatanodeID;
class StorageInfo;

class DatanodeRegistration {
 public:
  DatanodeRegistration();
  DatanodeRegistration(std::shared_ptr<DatanodeID> dn, StorageInfo* info,
                       const std::string& software_version);
  DatanodeRegistration(std::shared_ptr<DatanodeID> dn, StorageInfo* info,
                       const std::string& software_version,
                       bool block_report_preserved);
  ~DatanodeRegistration();

  DatanodeRegistration* Clone() const;

  // DatanodeID* mutable_datanode_id() const { return datanode_id_; }
  // DatanodeID* release_datanode_id();

  std::shared_ptr<DatanodeID> GetDatanodeID() const {
    return datanode_id_;
  }
  const StorageInfo* GetStorageInfo() const {
    return storage_info_;
  }
  std::string GetSoftwareVersion() const {
    return software_version_;
  }
  bool IsBlockReportPreserved() const {
    return block_report_preserved_;
  }
  const ExportedBlockKeys& GetExportedBlockKeys() const {
    return exported_block_keys_;
  }

  void SetBlockReportPreserved(bool preseved) {
    block_report_preserved_ = preseved;
  }

  int GetVersion() const;
  std::string GetRegistrationID() const;
  std::string GetAddress() const;

  std::string ToString() const;
  bool Equals(DatanodeRegistration* rhs) const;

 public:
  // convert to protobuf method
  exceptions::Exception ToProto(
      hadoop::hdfs::datanode::DatanodeRegistrationProto* proto) const;

  static DatanodeRegistration* ParseProto(
      const hadoop::hdfs::datanode::DatanodeRegistrationProto* proto);

  exceptions::Exception ToRequestProto(
      hadoop::hdfs::datanode::RegisterDatanodeRequestProto* req) const;

 private:
  std::shared_ptr<DatanodeID> datanode_id_;
  StorageInfo* storage_info_;
  std::string software_version_;
  bool block_report_preserved_;
  ExportedBlockKeys exported_block_keys_;
};

}  // namespace hdfs
}  // namespace chunkserver
}  // namespace bytestore
