// Copyright (c) 2019-present, ByteDance Inc. All rights reserved.

#pragma once

#include <string>
#include <unordered_map>

namespace bytestore {
namespace chunkserver {
namespace hdfs {
namespace exceptions {

enum E : uint8_t {
  kNoException = 0,
  kFalseException = 1,
  kIOException = 2,
  kSocketTimeoutException = 3,
  kSocketException = 4,
  kIncorrectVersionException = 5,
  kUnregisteredNodeException = 6,
  kDisallowedDatanodeException = 7,
  kInvalidRequestException = 8,
  kRemoteException = 9,
  kBPServiceActorActionException = 10,
  kIllegalStateException = 11,
  kIllegalArgumentException = 12,
  kChecksumException = 13,
  kNullPointerException = 14,
  kUnsupportedOperationException = 15,
  kReplicaAlreadyExistsException = 16,
  kReplicaNotFoundException = 17,
  kFileNotFoundException = 18,
  kUnexpectedReplicaStateException = 19,
  kEOFException = 20,
  kRecoveryInProgressException = 21,
  kInterruptedException = 22,
  kDiskOutOfSpaceException = 23,
  kDiskStateNotFoundException = 24,
  kStandbyException = 25,
  kAuthorizationException = 26,
  kRpcServerException = 27,
  kInvalidRpcHeaderException = 28,
  kDiskSourceNotEnough = 29,
  kReplicaFrozenException = 30,
  kMustStopExistingWriter = 31
};

class Exception {
 public:
  Exception() : e_(kNoException) {}
  explicit Exception(E e) : e_(e) {}
  Exception(E e, const std::string& message) : e_(e), message_(message) {}
  ~Exception() {}

  exceptions::E GetE() const {
    return e_;
  }
  std::string GetClass() const;
  std::string GetMessage() const {
    return message_;
  }

  std::string ToString() const;
  bool OK() const {
    return e_ == kNoException;
  }

 public:
  static exceptions::E FromString(const std::string& class_name);

 private:
  exceptions::E e_;
  std::string message_;
};

}  // namespace exceptions
}  // namespace hdfs
}  // namespace chunkserver
}  // namespace bytestore
