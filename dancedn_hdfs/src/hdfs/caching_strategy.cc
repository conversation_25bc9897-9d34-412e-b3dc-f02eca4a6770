// Copyright (c) 2019-present, ByteDance Inc. All rights reserved.

#include "hdfs/caching_strategy.h"

#include "byte/include/assert.h"
#include "byte/include/byte_log.h"
#include "byte/string/format/print.h"
#include "hdfs/generation_stamp.h"
#include "hdfs/io/io_buf.h"
#include "hdfs/proto/hdfs.pb.h"

namespace bytestore {
namespace chunkserver {
namespace hdfs {

CachingStrategy::CachingStrategy() : drop_behind_(false), readahead_(-1) {}

CachingStrategy::CachingStrategy(bool drop_behind, int64_t readahead)
    : drop_behind_(drop_behind), readahead_(readahead) {}

std::string CachingStrategy::ToString() const {
  std::string drop_behind = drop_behind_ == true ? "true" : "false";
  return "CachingStrategy(dropBehind=" + drop_behind +
         byte::StringPrint(", readahead= %ld)", readahead_);
}

exceptions::Exception CachingStrategy::ToProto(
    hadoop::hdfs::CachingStrategyProto* proto) const {
  proto->set_readahead(readahead_);
  proto->set_dropbehind(drop_behind_);
  return exceptions::Exception();
}

CachingStrategy* CachingStrategy::ParseProto(
    const hadoop::hdfs::CachingStrategyProto& proto) {
  auto res = new CachingStrategy();
  res->drop_behind_ = proto.has_readahead() ? proto.dropbehind() : false;
  res->readahead_ = proto.has_readahead() ? proto.readahead() : -1;
  return res;
}

CachingStrategy* CachingStrategy::NewDefaultStrategy() {
  return new CachingStrategy();
}

}  // namespace hdfs
}  // namespace chunkserver
}  // namespace bytestore
