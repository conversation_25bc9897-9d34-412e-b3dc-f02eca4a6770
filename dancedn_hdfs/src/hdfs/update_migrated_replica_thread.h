// copyright (c) 2019-present, bytedance inc. all rights reserved.

#pragma once

#include <stddef.h>

#include <deque>
#include <list>
#include <memory>
#include <queue>
#include <set>
#include <sstream>
#include <string>
#include <unordered_map>
#include <unordered_set>
#include <utility>
#include <vector>

#include "byte/concurrent/count_down_latch.h"
#include "byte/concurrent/mutex.h"
#include "byte/concurrent/rwlock.h"
#include "gflags/gflags.h"
#include "hdfs/available_space_volume_choosing_policy.h"
#include "hdfs/block_lock.h"
#include "hdfs/data_checksum.h"
#include "hdfs/datanode.h"
#include "hdfs/datanode_interface.h"
#include "hdfs/datanode_storage.h"
#include "hdfs/io/io_util.h"
#include "hdfs/io_oper.h"
#include "hdfs/meta.h"
#include "hdfs/namespace_info.h"
#include "hdfs/replica_being_written.h"
#include "hdfs/replica_map.h"
#include "hdfs/round_robin_volume_choosing_policy.h"
#include "hdfs/storage_report.h"
#include "hdfs/storage_type.h"
#include "hdfs/store.h"
#include "hdfs/thread.h"
#include "hdfs/volume_failure_summary.h"

namespace bytestore {
namespace chunkserver {
namespace hdfs {

struct UpdateMigratedReplicaOper {
  std::unique_ptr<ExtendedBlock> block_;
  uint16_t dst_disk_id_;

  UpdateMigratedReplicaOper(ExtendedBlock* e_block, uint16_t disk_id)
      : dst_disk_id_(disk_id) {
    block_.reset(new ExtendedBlock(*e_block));
  }
  ~UpdateMigratedReplicaOper() {}
};

class UpdateMigratedReplicaThread : public Thread {
 public:
  explicit UpdateMigratedReplicaThread(Store* storage) : storage_(storage) {}
  ~UpdateMigratedReplicaThread() {
    Stop();
    Join();
  }

  void Run() {
    while (!IsStopped()) {
      WaitUtil(
          [this]() {
            return !op_queue_.Empty() || IsStopped();
          },
          0);
      while (!op_queue_.Empty()) {
        std::shared_ptr<UpdateMigratedReplicaOper> oper = op_queue_.Front();
        if (oper != nullptr) {
          HandleOper(oper);
        }
        op_queue_.Pop();
      }
    }
    // delete
    while (!op_queue_.Empty()) {
      std::shared_ptr<UpdateMigratedReplicaOper> oper = op_queue_.Front();
      if (oper != nullptr) {
        HandleOper(oper);
      }
      op_queue_.Pop();
    }
  }

  bool AddOper(std::shared_ptr<UpdateMigratedReplicaOper> oper) {
    if (op_queue_.Push(oper->block_->ToString(), oper)) {
      Signal();
      return true;
    }
    return false;
  }

  size_t GetQueueSize() {
    return op_queue_.Size();
  }

  void HandleOper(std::shared_ptr<UpdateMigratedReplicaOper> oper) {
    LOG(DEBUG) << "IOOperReadDone: call UpdateMigratedReplica for "
               << oper->block_->ToString();
    auto e =
        storage_->UpdateMigratedReplica(oper->block_.get(), oper->dst_disk_id_);
    if (!e.OK() && e.GetE() != exceptions::kReplicaNotFoundException) {
      LOG(ERROR) << "IOOperReadDone: update migrated replica failed, exception:"
                 << e.ToString();
    }
  }

 private:
  Store* storage_;
  io::ConcurrentUniqueQueue<std::string,
                            std::shared_ptr<UpdateMigratedReplicaOper>>
      op_queue_;
};

}  // namespace hdfs
}  // namespace chunkserver
}  // namespace bytestore
