// Copyright (c) 2018-present, ByteDance Inc. All rights reserved.

#include "hdfs/block_recovery.h"

#include <memory>
#include <string>
#include <vector>

#include "byte/string/format/print.h"
#include "gmock/gmock.h"
#include "gtest/gtest.h"
#include "hdfs/client_io.h"
#include "hdfs/constants.h"
#include "hdfs/datanode.h"
#include "hdfs/datanode_id.h"
#include "hdfs/datanode_info.h"
#include "hdfs/datanode_registration.h"
#include "hdfs/exceptions.h"
#include "hdfs/inter_datanode_client.h"
#include "hdfs/mocks.h"
#include "hdfs/namenode_client.h"
#include "hdfs/namenode_rpc.h"
#include "hdfs/recovering_block.h"
#include "hdfs/replica_recovery_info.h"
#include "hdfs/replica_state.h"

namespace {

static const char storage_id[] = "suuid";

static const char bp_id[] = "tbpi";
static const uint64_t blk_gs0 = 1;
static const uint64_t blk_gs1 = 2;
static const uint64_t blk_id = 20190828;
static const uint64_t blk_len = 4096;
static const uint64_t recovery_len = 2048;
bytestore::chunkserver::hdfs::ExtendedBlock test_block(bp_id, blk_id, blk_len,
                                                       blk_gs0);
bytestore::chunkserver::hdfs::Block recovery_block(blk_id, recovery_len,
                                                   blk_gs0);

// DatanodeIDs
static const char ip_addr[] = "127.0.0.1";
static const char hostname[] = "localhost";
static const char uuid[] = "duuid001";
static const int xfer_port = 5060;
static const int info_port = 5061;
static const int ipc_port = 5062;
std::shared_ptr<bytestore::chunkserver::hdfs::DatanodeID> local_dn_id(
    new bytestore::chunkserver::hdfs::DatanodeID(ip_addr, hostname, uuid,
                                                 xfer_port, info_port, ipc_port,
                                                 0));

// DatanodeRegistration
auto local_dn_reg =
    std::make_shared<bytestore::chunkserver::hdfs::DatanodeRegistration>(
        local_dn_id,
        new bytestore::chunkserver::hdfs::StorageInfo(
            bytestore::chunkserver::hdfs::NodeType::DATA_NODE),
        bytestore::chunkserver::hdfs::DATANODE_SOFTWARE_VERSION);

}  // namespace

namespace bytestore {
namespace chunkserver {
namespace hdfs {

class BlockRecoveryTests : public ::testing::Test {
 public:
  BlockRecoveryTests() {}

  ~BlockRecoveryTests() {}

  void SetUp() {
    std::vector<DatanodeInfo*> dn_infos = {new DatanodeInfo(local_dn_id),
                                           new DatanodeInfo(local_dn_id),
                                           new DatanodeInfo(local_dn_id)};
    std::vector<DatanodeInfo*> dn_infos2 = {new DatanodeInfo(local_dn_id),
                                            new DatanodeInfo(local_dn_id),
                                            new DatanodeInfo(local_dn_id)};
    rb_.reset(new RecoveringBlock(test_block.Clone(), dn_infos, blk_gs1));
    rb_with_recovery_block_.reset(new RecoveringBlock(
        test_block.Clone(), dn_infos2, blk_gs1, recovery_block.Clone()));
  }

  void TearDown() {}

  static void SetUpTestCase() {}
  static void TearDownTestCase() {
    byte::GetLoggingSystem()->Shutdown();
  }

  void PrepareForTestRecoveryInfos(MockStore* storage, MockDataNode* dn,
                                   MockBlockRecoveryThread* t) {
    EXPECT_CALL(*dn, GetDNRegistrationForBP(bp_id, ::testing::_))
        .WillRepeatedly(
            ::testing::DoAll(::testing::SetArgPointee<1>(local_dn_reg),
                             ::testing::Return(exceptions::Exception())));

    EXPECT_CALL(*dn, CommitBlockSynchronization(bp_id, ::testing::_,
                                                ::testing::_, ::testing::_,
                                                ::testing::_, ::testing::_,
                                                ::testing::_, ::testing::_))
        .WillRepeatedly(::testing::Return(exceptions::Exception()));

    EXPECT_CALL(*dn, NotifyNamenodeReceivedBlock(::testing::_, "", storage_id))
        .WillRepeatedly(::testing::Return());

    dn->SetStorage(storage);
  }

 public:
  std::unique_ptr<RecoveringBlock> rb_;
  std::unique_ptr<RecoveringBlock> rb_with_recovery_block_;
};

TEST_F(BlockRecoveryTests, BPServiceNotFound) {
  MockDataNode mock_dn;
  std::string msg =
      byte::StringPrint("Cannot find BPOfferService for bpid=%s", bp_id);
  EXPECT_CALL(mock_dn, GetDNRegistrationForBP(bp_id, ::testing::_))
      .WillRepeatedly(::testing::Return(
          exceptions::Exception(exceptions::E::kIOException, msg)));

  MockBlockRecoveryThread t(&mock_dn);
  auto e = t.RecoverBlock(rb_.get());
  EXPECT_EQ(e.GetE(), exceptions::E::kIOException);
  EXPECT_NE(e.GetMessage().find("All datanodes failed"), std::string::npos);
}

TEST_F(BlockRecoveryTests, RecoveryInProgress) {
  MockDataNode mock_dn;
  EXPECT_CALL(mock_dn, GetDNRegistrationForBP(bp_id, ::testing::_))
      .WillRepeatedly(
          ::testing::DoAll(::testing::SetArgPointee<1>(local_dn_reg),
                           ::testing::Return(exceptions::Exception())));

  MockStore* mock_storage = new MockStore();
  EXPECT_CALL(*mock_storage, InitReplicaRecovery(rb_.get(), ::testing::_))
      .Times(2)
      .WillOnce(::testing::DoAll(::testing::SetArgPointee<1>(nullptr),
                                 ::testing::Return(exceptions::Exception())))
      .WillOnce(
          ::testing::DoAll(::testing::SetArgPointee<1>(nullptr),
                           ::testing::Return(exceptions::Exception(
                               exceptions::E::kRecoveryInProgressException))));

  mock_dn.SetStorage(mock_storage);

  MockBlockRecoveryThread t(&mock_dn);

  auto e = t.RecoverBlock(rb_.get());
  EXPECT_EQ(e.GetE(), exceptions::E::kRecoveryInProgressException);
}

TEST_F(BlockRecoveryTests, InvalidRecovertyInfo) {
  ReplicaRecoveryInfo* rinfo_with_less_gs = new ReplicaRecoveryInfo(
      blk_id, blk_len, (uint64_t)0, ReplicaState::FINALIZED);
  ReplicaRecoveryInfo* rinfo_with_zero_bytes =
      new ReplicaRecoveryInfo(blk_id, (uint64_t)0, blk_gs1, ReplicaState::RBW);

  MockStore* mock_storage = new MockStore();
  EXPECT_CALL(*mock_storage, InitReplicaRecovery(rb_.get(), ::testing::_))
      .Times(3)
      .WillOnce(::testing::DoAll(::testing::SetArgPointee<1>(nullptr),
                                 ::testing::Return(exceptions::Exception())))
      .WillOnce(
          ::testing::DoAll(::testing::SetArgPointee<1>(rinfo_with_less_gs),
                           ::testing::Return(exceptions::Exception())))
      .WillOnce(
          ::testing::DoAll(::testing::SetArgPointee<1>(rinfo_with_zero_bytes),
                           ::testing::Return(exceptions::Exception())));

  MockDataNode mock_dn;
  MockBlockRecoveryThread mock_thread(&mock_dn);
  PrepareForTestRecoveryInfos(mock_storage, &mock_dn, &mock_thread);

  auto e = mock_thread.RecoverBlock(rb_.get());
  EXPECT_EQ(e.GetE(), exceptions::E::kNoException);
}

TEST_F(BlockRecoveryTests, NoReplicaToBeRecovered) {
  ReplicaRecoveryInfo* rinfo1 =
      new ReplicaRecoveryInfo(blk_id, blk_len / 3, blk_gs0, ReplicaState::RUR);
  ReplicaRecoveryInfo* rinfo2 =
      new ReplicaRecoveryInfo(blk_id, blk_len / 3, blk_gs0, ReplicaState::RUR);
  ReplicaRecoveryInfo* rinfo3 =
      new ReplicaRecoveryInfo(blk_id, blk_len / 3, blk_gs0, ReplicaState::RUR);

  MockStore* mock_storage = new MockStore();
  EXPECT_CALL(*mock_storage, InitReplicaRecovery(rb_.get(), ::testing::_))
      .Times(3)
      .WillOnce(::testing::DoAll(::testing::SetArgPointee<1>(rinfo1),
                                 ::testing::Return(exceptions::Exception())))
      .WillOnce(::testing::DoAll(::testing::SetArgPointee<1>(rinfo2),
                                 ::testing::Return(exceptions::Exception())))
      .WillOnce(::testing::DoAll(::testing::SetArgPointee<1>(rinfo3),
                                 ::testing::Return(exceptions::Exception())));

  MockDataNode mock_dn;
  MockBlockRecoveryThread mock_thread(&mock_dn);
  PrepareForTestRecoveryInfos(mock_storage, &mock_dn, &mock_thread);

  auto e = mock_thread.RecoverBlock(rb_.get());
  EXPECT_EQ(e.GetE(), exceptions::E::kNoException);
}

TEST_F(BlockRecoveryTests, TruncateUnfinalizedReplica) {
  ReplicaRecoveryInfo* rinfo1 =
      new ReplicaRecoveryInfo(blk_id, blk_len / 2, blk_gs0, ReplicaState::RBW);
  ReplicaRecoveryInfo* rinfo2 =
      new ReplicaRecoveryInfo(blk_id, blk_len / 4, blk_gs0, ReplicaState::RWR);
  ReplicaRecoveryInfo* rinfo3 =
      new ReplicaRecoveryInfo(blk_id, blk_len / 3, blk_gs0, ReplicaState::RBW);

  MockStore* mock_storage = new MockStore();
  EXPECT_CALL(*mock_storage, InitReplicaRecovery(rb_.get(), ::testing::_))
      .Times(3)
      .WillOnce(::testing::DoAll(::testing::SetArgPointee<1>(rinfo1),
                                 ::testing::Return(exceptions::Exception())))
      .WillOnce(::testing::DoAll(::testing::SetArgPointee<1>(rinfo2),
                                 ::testing::Return(exceptions::Exception())))
      .WillOnce(::testing::DoAll(::testing::SetArgPointee<1>(rinfo3),
                                 ::testing::Return(exceptions::Exception())));

  // truncate rbw replica to min_length
  EXPECT_CALL(*mock_storage,
              UpdateReplicaUnderRecovery(::testing::_, blk_gs1, blk_len / 3,
                                         ::testing::_))
      .Times(2)
      .WillRepeatedly(
          ::testing::DoAll(::testing::SetArgPointee<3>(storage_id),
                           ::testing::Return(exceptions::Exception())));

  MockDataNode mock_dn;
  MockBlockRecoveryThread mock_thread(&mock_dn);
  PrepareForTestRecoveryInfos(mock_storage, &mock_dn, &mock_thread);

  auto e = mock_thread.RecoverBlock(rb_.get());
  EXPECT_EQ(e.GetE(), exceptions::E::kNoException);
}

TEST_F(BlockRecoveryTests, RecoveryFinalizedReplica) {
  ReplicaRecoveryInfo* rinfo1 = new ReplicaRecoveryInfo(
      blk_id, blk_len, blk_gs0, ReplicaState::FINALIZED);
  ReplicaRecoveryInfo* rinfo2 =
      new ReplicaRecoveryInfo(blk_id, blk_len, blk_gs0, ReplicaState::RBW);
  ReplicaRecoveryInfo* rinfo3 =
      new ReplicaRecoveryInfo(blk_id, blk_len / 2, blk_gs0, ReplicaState::RBW);

  MockStore* mock_storage = new MockStore();
  EXPECT_CALL(*mock_storage, InitReplicaRecovery(rb_.get(), ::testing::_))
      .Times(3)
      .WillOnce(::testing::DoAll(::testing::SetArgPointee<1>(rinfo1),
                                 ::testing::Return(exceptions::Exception())))
      .WillOnce(::testing::DoAll(::testing::SetArgPointee<1>(rinfo2),
                                 ::testing::Return(exceptions::Exception())))
      .WillOnce(::testing::DoAll(::testing::SetArgPointee<1>(rinfo3),
                                 ::testing::Return(exceptions::Exception())));

  // recovery the finalized replica and the rbw replica
  // which has equal length with the former
  EXPECT_CALL(*mock_storage, UpdateReplicaUnderRecovery(::testing::_, blk_gs1,
                                                        blk_len, ::testing::_))
      .Times(2)
      .WillRepeatedly(
          ::testing::DoAll(::testing::SetArgPointee<3>(storage_id),
                           ::testing::Return(exceptions::Exception())));

  MockDataNode mock_dn;
  MockBlockRecoveryThread mock_thread(&mock_dn);
  PrepareForTestRecoveryInfos(mock_storage, &mock_dn, &mock_thread);

  auto e = mock_thread.RecoverBlock(rb_.get());
  EXPECT_EQ(e.GetE(), exceptions::E::kNoException);
}

TEST_F(BlockRecoveryTests, TruncateRecovery) {
  ReplicaRecoveryInfo* rinfo1 = new ReplicaRecoveryInfo(
      blk_id, blk_len, blk_gs0, ReplicaState::FINALIZED);
  ReplicaRecoveryInfo* rinfo2 = new ReplicaRecoveryInfo(
      blk_id, blk_len, blk_gs0, ReplicaState::FINALIZED);
  ReplicaRecoveryInfo* rinfo3 = new ReplicaRecoveryInfo(
      blk_id, blk_len, blk_gs0, ReplicaState::FINALIZED);

  MockStore* mock_storage = new MockStore();
  EXPECT_CALL(*mock_storage,
              InitReplicaRecovery(rb_with_recovery_block_.get(), ::testing::_))
      .Times(3)
      .WillOnce(::testing::DoAll(::testing::SetArgPointee<1>(rinfo1),
                                 ::testing::Return(exceptions::Exception())))
      .WillOnce(::testing::DoAll(::testing::SetArgPointee<1>(rinfo2),
                                 ::testing::Return(exceptions::Exception())))
      .WillOnce(::testing::DoAll(::testing::SetArgPointee<1>(rinfo3),
                                 ::testing::Return(exceptions::Exception())));

  // recovery the finalized replica and the rbw replica
  // which has equal length with the former
  EXPECT_CALL(*mock_storage,
              UpdateReplicaUnderRecovery(::testing::_, blk_gs1, recovery_len,
                                         ::testing::_))
      .Times(3)
      .WillRepeatedly(
          ::testing::DoAll(::testing::SetArgPointee<3>(storage_id),
                           ::testing::Return(exceptions::Exception())));

  MockDataNode mock_dn;
  MockBlockRecoveryThread mock_thread(&mock_dn);
  PrepareForTestRecoveryInfos(mock_storage, &mock_dn, &mock_thread);

  auto e = mock_thread.RecoverBlock(rb_with_recovery_block_.get());
  EXPECT_EQ(e.GetE(), exceptions::E::kNoException);
}

TEST_F(BlockRecoveryTests, RunBlockRecoveryThread) {
  ReplicaRecoveryInfo* rinfo1 = new ReplicaRecoveryInfo(
      blk_id, blk_len, blk_gs0, ReplicaState::FINALIZED);
  ReplicaRecoveryInfo* rinfo2 =
      new ReplicaRecoveryInfo(blk_id, blk_len, blk_gs0, ReplicaState::RBW);
  ReplicaRecoveryInfo* rinfo3 =
      new ReplicaRecoveryInfo(blk_id, blk_len / 2, blk_gs0, ReplicaState::RBW);

  MockStore* mock_storage = new MockStore();
  EXPECT_CALL(*mock_storage, InitReplicaRecovery(rb_.get(), ::testing::_))
      .Times(3)
      .WillOnce(::testing::DoAll(::testing::SetArgPointee<1>(rinfo1),
                                 ::testing::Return(exceptions::Exception())))
      .WillOnce(::testing::DoAll(::testing::SetArgPointee<1>(rinfo2),
                                 ::testing::Return(exceptions::Exception())))
      .WillOnce(::testing::DoAll(::testing::SetArgPointee<1>(rinfo3),
                                 ::testing::Return(exceptions::Exception())));

  // recovery the finalized replica and the rbw replica
  // which has equal length with the former
  EXPECT_CALL(*mock_storage, UpdateReplicaUnderRecovery(::testing::_, blk_gs1,
                                                        blk_len, ::testing::_))
      .Times(2)
      .WillRepeatedly(
          ::testing::DoAll(::testing::SetArgPointee<3>(storage_id),
                           ::testing::Return(exceptions::Exception())));

  const char who[] = "mock namenode";
  auto rb = rb_.release();
  std::vector<RecoveringBlock*>* rblks = new std::vector<RecoveringBlock*>();
  rblks->push_back(rb);

  auto dn = new MockDataNode();

  EXPECT_CALL(*dn, GetDNRegistrationForBP(bp_id, ::testing::_))
      .WillRepeatedly(
          ::testing::DoAll(::testing::SetArgPointee<1>(local_dn_reg),
                           ::testing::Return(exceptions::Exception())));

  EXPECT_CALL(*dn, CommitBlockSynchronization(
                       bp_id, ::testing::_, ::testing::_, ::testing::_,
                       ::testing::_, ::testing::_, ::testing::_, ::testing::_))
      .WillRepeatedly(::testing::Return(exceptions::Exception()));

  EXPECT_CALL(*dn, NotifyNamenodeReceivedBlock(::testing::_, "", storage_id))
      .WillRepeatedly(::testing::Return());

  dn->SetStorage(mock_storage);
  auto t = new MockBlockRecoveryThread(dn, who, rblks);
  t->Start();
  t->Join();
  delete t;
  delete dn;
}

}  // namespace hdfs
}  // namespace chunkserver
}  // namespace bytestore
