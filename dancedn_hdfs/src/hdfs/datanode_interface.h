// Copyright (c) 2018-present, ByteDance Inc. All rights reserved.

#pragma once

#include <memory>
#include <set>
#include <string>
#include <vector>

#include "chunkserver/chunkserver.h"
#include "chunkserver/common/chunkserver_types.h"
#include "chunkserver/device_management.h"
#include "chunkserver/env.h"
#include "concurrent/rwlock.h"

namespace bytestore {
namespace chunkserver {

class Disk;
class ChunkServerConfig;
const int UPDATE_TIME_INTERVAL_MS = 60 * 1000;

class DatanodeInterface {
 public:
  DatanodeInterface(ChunkServerConfig* cs_config, Env* cs_env);

  ~DatanodeInterface();

  // functions for ChunkServer
  const DiskIdConfMap* GetDiskConf();

  // functions for Env
  std::string GetFullHostAddr() const;

  // functions for DeviceManagement
  Errorcode GetCSCapacityInBytes(uint64_t* capacity_in_bytes);
  Errorcode GetDiskCapacityInBytes(uint32_t disk_id,
                                   uint64_t* capacity_in_bytes);
  Errorcode GetUsedSizeInBytes(uint32_t disk_id, uint64_t* used_size_in_bytes);
  // set virtual for unit test
  virtual Errorcode GetFreeSizeInBytes(uint32_t disk_id,
                                       uint64_t* free_size_in_bytes);
  virtual Errorcode GetDiskInfos(std::vector<PerDiskInfo>* disk_infos);
  uint32_t GetDiskCount() const;

  // functions for Disk
  // bool DiskExists(DiskId disk_id);
  // void AtomicSetReferenceCount(DiskId disk_id, const int32_t val);
  // void AtomicIncreReferenceCount(DiskId disk_id);
  // void AtomicDecreReferenceCount(DiskId disk_id);
  // int32_t AtomicGetReferenceCount(DiskId disk_id);
  virtual bool IsDiskClosed(Disk* disk);
  virtual bool IsDiskClosed(DiskId disk_id);
  virtual bool IsDiskReadonly(Disk* disk);
  virtual bool IsDiskReadonly(DiskId disk_id);
  virtual bool GetDiskStatus(Disk* disk, DiskStatus* status) const;
  virtual bool GetDiskStatus(DiskId disk_id, DiskStatus* status) const;
  virtual bool CloseDisk(Disk* disk);
  virtual bool CloseDisk(DiskId disk_id);
  virtual bool SetDiskReadonly(Disk* disk);
  virtual bool SetDiskReadonly(DiskId disk_id);
  virtual bool SetDiskStatus(Disk* disk, DiskStatus status);
  virtual bool SetDiskStatus(DiskId disk_id, DiskStatus status);

  void AddDisk(google::protobuf::RpcController* controller,
               const CsAddDiskRequest* request, CsAddDiskResponse* response,
               google::protobuf::Closure* done);

  void RemoveDisk(google::protobuf::RpcController* controller,
                  const CsRemoveDiskRequest* request,
                  CsRemoveDiskResponse* response,
                  google::protobuf::Closure* done);

  Errorcode GetTargetDiskIdForTieredChunk(const DiskId& disk_id,
                                          const ChunkId& chunk_id,
                                          DiskId* target_disk_id);

  // functions for operations
  void GetChunkMeta(google::protobuf::RpcController* controller,
                    const GetChunkMetaRequest* request,
                    GetChunkMetaResponse* response,
                    google::protobuf::Closure* done);

  void CreateChunk(google::protobuf::RpcController* controller,
                   const CreateChunkRequest* request,
                   CreateChunkResponse* response,
                   google::protobuf::Closure* done);

  void ReadChunk(google::protobuf::RpcController* controller,
                 const ReadChunkRequest* request, ReadChunkResponse* response,
                 google::protobuf::Closure* done);

  void WriteChunk(google::protobuf::RpcController* controller,
                  const WriteChunkRequest* request,
                  WriteChunkResponse* response,
                  google::protobuf::Closure* done);

  void FreezeChunk(google::protobuf::RpcController* controller,
                   const FreezeChunkRequest* request,
                   FreezeChunkResponse* response,
                   google::protobuf::Closure* done);

  void HardLinkChunk(google::protobuf::RpcController* controller,
                     const HardLinkChunkRequest* request,
                     HardLinkChunkResponse* response,
                     google::protobuf::Closure* done);

  void SetXATTRChunk(google::protobuf::RpcController* controller,
                     const CSSetXattrRequest* request,
                     CSSetXattrResponse* response,
                     google::protobuf::Closure* done);

  void GetXATTRChunk(google::protobuf::RpcController* controller,
                     const CSGetXattrRequest* request,
                     CSGetXattrResponse* response,
                     google::protobuf::Closure* done);

  void DeleteChunks(google::protobuf::RpcController* controller,
                    const ::bytestore::DeleteChunksRequest* request,
                    bytestore::DeleteChunksResponse* response,
                    google::protobuf::Closure* done);

  void TruncateChunk(google::protobuf::RpcController* controller,
                     const TruncateChunkRequest* request,
                     TruncateChunkResponse* response,
                     google::protobuf::Closure* done);

  void SyncChunk(google::protobuf::RpcController* controller,
                 const SyncChunkRequest* request, SyncChunkResponse* response,
                 google::protobuf::Closure* done);

  void ReadaheadChunk(google::protobuf::RpcController* controller,
                      const ReadaheadChunkRequest* request,
                      ReadaheadChunkResponse* response,
                      google::protobuf::Closure* done);

 private:
  ChunkServerConfig* cs_config_;
  Env* cs_env_;
  std::unique_ptr<CSManagementServiceImpl> csmgr_service_impl_;        // for ut
  std::unique_ptr<CSIOServiceImpl<RpcType::BRPC>> csio_service_impl_;  // for ut
  std::unique_ptr<CSMetaServiceImpl<RpcType::BRPC>>
      csmeta_service_impl_;  // for ut
  std::set<DiskStatus> closed_disk_status_set_ = {DISK_DECOMMISSION, DISK_DOWN};
  std::set<DiskStatus> readonly_disk_status_set_ = {DISK_DECOMMISSIONING,
                                                    DISK_READONLY};

  std::vector<PerDiskInfo>
      disk_infos_;  // GetDiskInfos is a heavy op, so cache it
  std::atomic<int64_t> last_update_time_ms_{0};
  byte::RwLock lock_;
};

}  // namespace chunkserver
}  // namespace bytestore
