// Copyright (c) 2019-present, ByteDance Inc. All rights reserved.

#pragma once

#include <stdint.h>

#include <string>

#include "hdfs/exceptions.h"

namespace hadoop {
namespace hdfs {
class StorageReportProto;
}
}  // namespace hadoop
namespace bytestore {
namespace chunkserver {
namespace hdfs {

class DatanodeStorage;

class StorageReport {
 public:
  StorageReport(const DatanodeStorage* storage, bool failed,
                std::string storage_path, uint64_t capacity, uint64_t dfs_used,
                uint64_t remaining, uint64_t block_pool_used,
                uint64_t non_dfs_used);
  StorageReport(const DatanodeStorage* storage, bool failed,
                std::string storage_path, uint64_t capacity, uint64_t dfs_used,
                uint64_t remaining, uint64_t block_pool_used,
                uint64_t non_dfs_used, bool closed, int32_t reference_count);
  ~StorageReport();

  DatanodeStorage* GetStorage() const {
    return storage_;
  }
  bool IsFailed() const {
    return failed_;
  }
  std::string GetStoragePath() const {
    return storage_path_;
  }
  uint64_t GetCapacity() const {
    return capacity_;
  }
  uint64_t GetDfsUsed() const {
    return dfs_used_;
  }
  uint64_t GetNonDfsUsed() const {
    return non_dfs_used_;
  }
  uint64_t GetRemaining() const {
    return remaining_;
  }
  uint64_t GetBlockPoolUsed() const {
    return block_pool_used_;
  }
  uint64_t GetClosed() const {
    return closed_;
  }
  uint64_t GetReferenceCount() const {
    return reference_count_;
  }

  exceptions::Exception ToProto(hadoop::hdfs::StorageReportProto* proto) const;

 private:
  DatanodeStorage* storage_;
  bool failed_;
  std::string storage_path_;
  uint64_t capacity_;
  uint64_t dfs_used_;
  uint64_t non_dfs_used_;
  uint64_t remaining_;
  uint64_t block_pool_used_;
  bool closed_;
  int32_t reference_count_;
};

}  // namespace hdfs
}  // namespace chunkserver
}  // namespace bytestore
