// Copyright (c) 2019-present, ByteDance Inc. All rights reserved.

#pragma once

#include <string>

#include "common/store_types.h"
#include "hdfs/replica_state.h"

namespace bytestore {
namespace chunkserver {
namespace hdfs {

class Replica {
 public:
  Replica() {}
  virtual ~Replica() {}

  virtual uint64_t GetBlockID() const = 0;

  virtual uint64_t GetGS() const = 0;

  virtual ReplicaState GetState() const = 0;

  virtual uint64_t GetNumBytes() const = 0;

  virtual uint64_t GetBytesOnDisk() const = 0;

  virtual int64_t GetVisibleLength() const = 0;

  virtual std::string GetStorageUuid() const = 0;

  virtual bool IsOnTransientStorage() const = 0;

  virtual std::string ToString() const = 0;

  virtual uint16_t GetDiskId() const = 0;

  virtual PlacementStorageType GetPlacementType() const = 0;
};

}  // namespace hdfs
}  // namespace chunkserver
}  // namespace bytestore
