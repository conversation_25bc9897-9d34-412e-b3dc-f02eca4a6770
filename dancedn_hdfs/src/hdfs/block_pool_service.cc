// Copyright (c) 2019-present, ByteDance Inc. All rights reserved.

#include "hdfs/block_pool_service.h"

#include <algorithm>
#include <unordered_map>
#include <vector>

#include "byte/include/assert.h"
#include "byte/string/format/print.h"
#include "byte/system/timestamp.h"
#include "concurrent/rwlock.h"
#include "hdfs/block.h"
#include "hdfs/block_pool_actor.h"
#include "hdfs/block_pool_manager.h"
#include "hdfs/datanode.h"
#include "hdfs/datanode_command.h"
#include "hdfs/datanode_id.h"
#include "hdfs/datanode_registration.h"
#include "hdfs/error_report_action.h"
#include "hdfs/extended_block.h"
#include "hdfs/namenode_client.h"
#include "hdfs/namespace_info.h"
#include "hdfs/nn_ha_status_heartbeat.h"
#include "hdfs/receive_deleted_block_info.h"
#include "hdfs/report_bad_block_action.h"
#include "hdfs/rolling_upgrade_status.h"
#include "hdfs/security/block_pool_token_secret_manager.h"
#include "hdfs/store.h"
#include "hdfs/util.h"

namespace bytestore {
namespace chunkserver {
namespace hdfs {

BlockPoolService::BlockPoolService(
    const std::unordered_map<std::string, std::string>& nn_name_to_addr,
    DataNode* dn, const std::string& namespace_name,
    BlockPoolManager* bp_manager)
    : completely_recovered_(false),
      rwlock_(),
      bp_manager_(bp_manager),
      ns_info_(nullptr),
      bp_service_to_active_(nullptr),
      r_(nullptr),
      last_active_claim_txid_(0),
      init_at_(0),
      register_at_(0),
      heart_beat_at_(0),
      refresh_at_(0),
      namespace_name_(namespace_name),
      bp_service_report_mutex_(),
      bpid_(""),
      nn_name_to_addr_(nn_name_to_addr) {
  dn_ = dn;
  // g_ = std::unique_ptr<TaskerGroup>(new TaskerGroup(0));
}

BlockPoolService::~BlockPoolService() {
  //    g_->Stop();
  //    g_->Join();

  //    for (auto actor : actors_) {
  //        actor->Destroy();
  //    }
  actors_.clear();

  delete ns_info_;
}

void BlockPoolService::RefreshNNList(
    const std::unordered_map<std::string, std::string>& addr_to_nn_name,
    const std::vector<std::string>& addrs) {
  // we must assert that nn_name and nn_addr is unique correspondence
  if (addrs.size() != addr_to_nn_name.size()) {
    LOG(WARNING)
        << "check config file, different namenodes may have same ip addr";
    return;
  }
  rwlock_.WriterLock();
  LOG(INFO) << "new addr:" << Joiner(",").Join(addrs);

  std::vector<std::string> new_addrs(addrs);
  std::vector<std::string> old_addrs;
  for (auto actor : actors_) {
    old_addrs.push_back(actor->GetNameNodeAddr());
  }
  LOG(INFO) << "old addr:" << Joiner(",").Join(old_addrs);

  std::sort(new_addrs.begin(), new_addrs.end());
  std::sort(old_addrs.begin(), old_addrs.end());
  std::vector<std::string> added_addrs;
  std::set_difference(new_addrs.begin(), new_addrs.end(), old_addrs.begin(),
                      old_addrs.end(),
                      std::inserter(added_addrs, added_addrs.begin()));
  LOG(INFO) << "add addr:" << Joiner(",").Join(added_addrs);
  for (auto added_addr : added_addrs) {
    auto actor = new BlockPoolActor(addr_to_nn_name.at(added_addr), added_addr,
                                    GetPtr());
    actors_.emplace_back(actor);
    bp_manager_->AddActor(actor);
  }
  std::vector<std::string> remove_addrs;
  std::set_difference(old_addrs.begin(), old_addrs.end(), new_addrs.begin(),
                      new_addrs.end(),
                      std::inserter(remove_addrs, remove_addrs.begin()));
  LOG(INFO) << "remove addr:" << Joiner(",").Join(remove_addrs);
  for (auto remove_addr : remove_addrs) {
    for (auto actor : actors_) {
      if (actor->GetNameNodeAddr() == remove_addr) {
        // if actor shutdown will delete itself so add ref avoid request
        // invalid address
        // actor->AddRef();
        // actor->Exit();
        bp_manager_->DeleteActor(actor);
        // rwlock_.WriterUnlock();
        // when actor exit will call ShutdownActor To Get rw_lock
        // actor->Join();
        // after clean itself can actor delete
        // rwlock_.WriterLock();
        break;
      }
    }
  }
  refresh_at_ = byte::GetCurrentTimeInMs();
  rwlock_.WriterUnlock();
}

void BlockPoolService::UpdateActorStatesFromHeartbeat(
    BlockPoolActor* actor, const NNHAStatusHeartbeat& status) {
  byte::RwLock::WriterLocker guard(&rwlock_);
  BYTE_ASSERT(actor != nullptr) << "update actor states from "
                                   "heartbeat failed for actor "
                                   "is nullptr";

  int64_t txid = status.GetTxid();

  bool nn_claims_active = status.GetState() == HAServiceState::ACTIVE;
  bool bpos_thinks_active = bp_service_to_active_ == actor;
  bool is_more_recent_claim = txid > last_active_claim_txid_;

  if (nn_claims_active && !bpos_thinks_active) {
    LOG(INFO) << "Namenode: " << actor->ToString()
              << " trying to claim ACTIVE state with"
              << "txid= " << txid;
    if (!is_more_recent_claim) {
      // Split-brain scenario - an NN is trying to claim active
      // state when a different NN has already claimed it with a higher
      // txid.
      LOG(WARNING) << "NN" << actor->ToString()
                   << " tried to claim ACTIVE state at "
                   << "txid= " << txid
                   << " but there was already a more recent claim at "
                   << "txid= " << last_active_claim_txid_;
      return;
    } else {
      if (bp_service_to_active_ == nullptr) {
        LOG(INFO) << "Acknowledging ACTIVE Namenode " << actor->ToString();
      } else {
        LOG(INFO) << "Namenode" << actor->ToString()
                  << " taking over ACTIVE state from "
                  << bp_service_to_active_->ToString()
                  << " at higher txid = " << txid;
        bp_service_to_active_->SetActive(false);
      }
      bp_service_to_active_ = actor;
      actor->SetActive(true);
    }
  } else if (!nn_claims_active && bpos_thinks_active) {
    LOG(INFO) << "Namenode" << actor->ToString()
              << " relinquishing ACTIVE state with "
              << "txid = " << txid;
    if (bp_service_to_active_ != nullptr) {
      bp_service_to_active_->SetActive(false);
    }
    bp_service_to_active_ = nullptr;
  }

  // else if {
  //    1. nn_claims_active and bpos_thinks_active
  //      txid can be equals to last active claim txid or large than it
  //    2. !nn_claims_active and !bpos_thinks_active
  //      do not update txid
  // }
  if (bp_service_to_active_ == actor) {
    BYTE_ASSERT(txid >= last_active_claim_txid_) << byte::StringPrint(
        "active actor txid(%d) < last_active_claim_txid_(%d)", txid,
        last_active_claim_txid_);
    last_active_claim_txid_ = txid;
  }
}

void BlockPoolService::ReportBadBlocks(ExtendedBlock* b,
                                       const std::string& storage_uuid,
                                       const StorageType& storage_type) {
  byte::RwLock::ReaderLocker guard(&rwlock_);
  CheckBlock(b);
  for (auto actor : actors_) {
    auto rbb_action =
        new ReportBadBlockAction(b->Clone(), storage_uuid, storage_type);
    actor->BpThreadEnqueue(rbb_action);
  }
}

void BlockPoolService::ReportBadBlocksUnsafe(ExtendedBlock* b,
                                             const std::string& storage_uuid,
                                             const StorageType& storage_type) {
  CheckBlock(b);
  for (auto actor : actors_) {
    auto rbb_action =
        new ReportBadBlockAction(b->Clone(), storage_uuid, storage_type);
    actor->BpThreadEnqueue(rbb_action);
  }
}

bool BlockPoolService::NotifyNamenodeReceivedBlock(
    ExtendedBlock* b, const std::string& del_hint,
    const std::string& storage_uuid) {
  byte::RwLock::ReaderLocker guard(&rwlock_);
  CheckBlock(b);
  auto b_info = std::make_shared<ReceivedDeletedBlockInfo>(
      b->GetBlock()->Clone(), BlockStatus(BlockStatus::RECEIVED_BLOCK),
      del_hint);

  bool res = true;
  for (auto actor : actors_) {
    if (!actor->NotifyNamenodeBlock(b_info, storage_uuid, true)) {
      res = false;
    }
  }
  return res;
}

void BlockPoolService::NotifyNamenodeMigratedBlock(
    ExtendedBlock* b, const std::string& src_storage_uuid,
    const std::string& dst_storage_uuid) {
  byte::RwLock::ReaderLocker guard(&rwlock_);
  CheckBlock(b);
  auto b_info_deleted = std::make_shared<ReceivedDeletedBlockInfo>(
      b->GetBlock()->Clone(), BlockStatus(BlockStatus::DELETED_BLOCK),
      std::string());
  auto b_info_received = std::make_shared<ReceivedDeletedBlockInfo>(
      b->GetBlock()->Clone(), BlockStatus(BlockStatus::RECEIVED_BLOCK), "");
  for (auto actor : actors_) {
    actor->NotifyNamenodeMigratedBlock(b_info_deleted, src_storage_uuid,
                                       b_info_received, dst_storage_uuid, true);
  }
}

void BlockPoolService::NotifyNamenodeDeletedBlock(
    ExtendedBlock* b, const std::string& storage_uuid) {
  byte::RwLock::ReaderLocker guard(&rwlock_);
  CheckBlock(b);
  auto b_info = std::make_shared<ReceivedDeletedBlockInfo>(
      b->GetBlock()->Clone(), BlockStatus(BlockStatus::DELETED_BLOCK),
      std::string());
  for (auto actor : actors_) {
    actor->NotifyNamenodeDeletedBlock(b_info, storage_uuid);
  }
}

bool BlockPoolService::NotifyNamenodeReceivingBlock(
    ExtendedBlock* b, const std::string& storage_uuid) {
  byte::RwLock::ReaderLocker guard(&rwlock_);
  CheckBlock(b);
  auto b_info = std::make_shared<ReceivedDeletedBlockInfo>(
      b->GetBlock()->Clone(), BlockStatus(BlockStatus::RECEIVING_BLOCK),
      std::string());

  bool res = true;
  for (auto actor : actors_) {
    if (!actor->NotifyNamenodeBlock(b_info, storage_uuid, false)) {
      res = false;
    }
  }
  return res;
}

void BlockPoolService::TrySendErrorReport(int32_t error_code,
                                          const std::string& error_msg) {
  byte::RwLock::ReaderLocker guard(&rwlock_);
  for (auto actor : actors_) {
    auto error_report_action = new ErrorReportAction(error_code, error_msg);
    actor->BpThreadEnqueue(error_report_action);
  }
}

void BlockPoolService::TrySendErrorReportUnsafe(int32_t error_code,
                                                const std::string& error_msg) {
  for (auto actor : actors_) {
    auto error_report_action = new ErrorReportAction(error_code, error_msg);
    actor->BpThreadEnqueue(error_report_action);
  }
}

void BlockPoolService::ReportRemoteBadBlock(DatanodeInfo* dn_info,
                                            ExtendedBlock* block) {
  byte::RwLock::WriterLocker guard(&rwlock_);
  for (auto actor : actors_) {
    auto e = actor->ReportRemoteBadBlock(dn_info, block);
    if (e.GetE() == exceptions::E::kIOException) {
      LOG(WARNING) << "Couldn't report bad block " << block->ToString()
                   << " to " << actor->ToString() << e.ToString();
    }
  }
}

exceptions::Exception BlockPoolService::CommitBlockSynchronization(
    const ExtendedBlock* b, uint64_t new_gs, uint64_t new_length,
    bool close_file, bool delete_block,
    const std::vector<std::shared_ptr<DatanodeID>>& new_targets,
    const std::vector<std::string>& new_target_storages) {
  // TODO(cyt): narrow lock range
  byte::RwLock::WriterLocker guard(&rwlock_);
  auto nn = bp_service_to_active_->GetNamenodeClient();
  if (nn == nullptr) {
    std::string msg = byte::StringPrint(
        "Block pool %s has not recognized"
        " an active NN",
        GetBlockPoolIDUnlock());
    return exceptions::Exception(exceptions::E::kIOException, msg);
  }
  return nn->CommitBlockSynchronization(b, new_gs, new_length, close_file,
                                        delete_block, new_targets,
                                        new_target_storages);
}

bool BlockPoolService::Start() {
  for (auto iter = nn_name_to_addr_.begin(); iter != nn_name_to_addr_.end();
       ++iter) {
    actors_.emplace_back(
        new BlockPoolActor(iter->first, iter->second, GetPtr()));
  }
  for (size_t i = 0; i < actors_.size(); i++) {
    if (!actors_[i]->IsStopped()) continue;
    bp_manager_->AddActor(actors_[i]);
  }
  return true;
}

void BlockPoolService::Stop() {
  for (size_t i = 0; i < actors_.size(); i++) {
    if (actors_[i]->IsStopped()) continue;
    bp_manager_->DeleteActor(actors_[i]);
  }
}

void BlockPoolService::Join() {
  for (size_t i = 0; i < actors_.size(); i++) {
    actors_[i]->Join();
  }
}

template <typename T>
exceptions::Exception BlockPoolService::CheckNsEquality(
    const T& left, const T& right, const std::string& message) {
  if (!(left == right)) {
    return exceptions::Exception(exceptions::E::kIOException,
                                 message + " mismatch");
  }
  return exceptions::Exception();
}

exceptions::Exception BlockPoolService::RegistrationSucceeded(
    BlockPoolActor* actor, DatanodeRegistration* r) {
  byte::RwLock::WriterLocker guard(&rwlock_);
  if (r_ != nullptr) {
    auto e = CheckNsEquality<int>(r_->GetStorageInfo()->GetNamespaceID(),
                                  r->GetStorageInfo()->GetNamespaceID(),
                                  "namespace ID");
    if (!e.OK()) return e;

    e = CheckNsEquality<std::string>(r_->GetStorageInfo()->GetClusterID(),
                                     r->GetStorageInfo()->GetClusterID(),
                                     "cluster ID");
    if (!e.OK()) return e;
  } else {
    r_.reset(r->Clone());
  }
  auto bpid = GetBlockPoolIDUnlock();
  dn_->RegistrationSucceeded(r_, bpid);
  if (dn_->IsBlockTokenEnabled() &&
      dn_->GetBlockPoolTokenSecretManager()->IsBlockPoolRegistered(bpid)) {
    dn_->GetBlockPoolTokenSecretManager()->AddKeys(bpid,
                                                   r_->GetExportedBlockKeys());
  }
  register_at_ = byte::GetCurrentTimeInMs();
  return exceptions::Exception();
}

DatanodeRegistration* BlockPoolService::CreateRegistration() {
  byte::RwLock::WriterLocker guard(&rwlock_);

  BYTE_ASSERT(ns_info_ != nullptr)
      << "getRegistration() can only be called after initial handshake";
  return dn_->CreateRegistration(ns_info_);
}

std::string BlockPoolService::ToString() const {
  if (ns_info_ != nullptr) {
    return byte::StringPrint("Block pool %s (Datanode UUID %s)",
                             ns_info_->GetBlockPoolID(), dn_->GetUUID());
  }

  std::string uuid = dn_->GetUUID();
  return byte::StringPrint("Block pool <registering> (Datanode UUID %s)",
                           uuid.empty() ? "unassigned" : uuid);
}

std::string BlockPoolService::LockedToString() const {
  byte::RwLock::ReaderLocker guard(&rwlock_);
  return ToString();
}

NameSpaceInfo* BlockPoolService::GetNameSpaceInfo() const {
  return ns_info_;
}

exceptions::Exception BlockPoolService::VerifyAndSetNamespaceInfo(
    NameSpaceInfo** ns_info) {
  auto info = std::unique_ptr<NameSpaceInfo>(*ns_info);
  *ns_info = nullptr;

  byte::RwLock::WriterLocker guard(&rwlock_);

  if (ns_info_ == nullptr) {
    // Now that we know the namespace ID, etc, we can pass this to the DN.
    // The DN can now initialize its local storage if we are the
    // first BP to handshake, etc.
    ns_info_ = info.release();
    bpid_ = ns_info_->GetBlockPoolID();
    auto e = dn_->InitBlockPool(GetPtr(), &completely_recovered_);
    init_at_ = byte::GetCurrentTimeInMs();
    if (!e.OK()) {
      LOG(ERROR) << "init block pool failed:" << e.ToString();
      init_at_ = 0;
      delete ns_info_;
      ns_info_ = nullptr;
      bpid_ = "";
    }
    return e;
  }

  auto e = CheckNsEquality<std::string>(ns_info_->GetBlockPoolID(),
                                        info->GetBlockPoolID(), "Blockpool ID");
  if (!e.OK()) return e;

  e = CheckNsEquality<int>(ns_info_->GetNamespaceID(), info->GetNamespaceID(),
                           "Namespace ID");
  if (!e.OK()) return e;

  e = CheckNsEquality<std::string>(ns_info_->GetClusterID(),
                                   info->GetClusterID(), "Cluster ID");
  return e;
}

bool BlockPoolService::ShouldRetryInit() {
  byte::RwLock::ReaderLocker guard(&rwlock_);
  return ns_info_ != nullptr || IsAlive();
}

bool BlockPoolService::IsAlive() {
  for (auto actor : actors_) {
    if (actor->IsAlive()) {
      return true;
    }
  }
  return false;
}

bool BlockPoolService::IsCompleteRecovered() {
  return completely_recovered_;
}

bool BlockPoolService::HasBlockPoolID() {
  byte::RwLock::ReaderLocker guard(&rwlock_);
  return ns_info_ != nullptr;
}

std::string BlockPoolService::GetBlockPoolID() const {
  if (!bpid_.empty()) {
    return bpid_;
  }
  byte::RwLock::ReaderLocker guard(&rwlock_);
  return GetBlockPoolIDUnlock();
}

std::string BlockPoolService::GetBlockPoolIDUnlock() const {
  if (ns_info_ == nullptr) {
    LOG(WARNING)
        << "Block pool ID needed, but service not yet registered with NN";
    return "";
  }
  return ns_info_->GetBlockPoolID();
}

void BlockPoolService::CheckBlock(ExtendedBlock* block) {
  BYTE_ASSERT(block != nullptr) << "block is nullptr";
  BYTE_ASSERT_EQ(block->GetBlockPoolID(), GetBlockPoolIDUnlock())
      << byte::StringPrint("block belongs to BP %s instead of BP %s",
                           block->GetBlockPoolID(), GetBlockPoolIDUnlock());
}

void BlockPoolService::ShutDownActor(BlockPoolActor* actor) {
  if (actor == nullptr) return;
  byte::RwLock::WriterLocker guard(&rwlock_);

  if (bp_service_to_active_ == actor) {
    bp_service_to_active_ = nullptr;
  }

  for (auto itr = actors_.begin(); itr != actors_.end(); itr++) {
    if (*itr == actor) {
      actors_.erase(itr);
      break;
    }
  }

  if (actors_.empty()) {
    dn_->ShutdownBlockPool(this);
  }
}

exceptions::Exception BlockPoolService::TriggerBlockReport(
    const BlockReportOptions& options) const {
  rwlock_.ReaderLock();
  for (auto actor : actors_) {
    if (actor != nullptr) {
      auto e = actor->TriggerBlockReport(options);
      if (!e.OK()) {
        rwlock_.ReaderUnlock();
        return e;
      }
    }
  }
  rwlock_.ReaderUnlock();
  return exceptions::Exception();
}

void BlockPoolService::ScheduleBlockReport(uint32_t delay) {
  byte::RwLock::ReaderLocker guard(&rwlock_);
  for (auto actor : actors_) {
    actor->ScheduleBlockReport(delay);
  }
}

void BlockPoolService::ScheduleBlockReportUnsafe(uint32_t delay) {
  for (auto actor : actors_) {
    actor->ScheduleBlockReport(delay);
  }
}

BlockPoolService::DNRegSharedPtr BlockPoolService::GetDatanodeRegistration()
    const {
  byte::RwLock::ReaderLocker guard(&rwlock_);
  return r_;
}

void BlockPoolService::doHeartBeat(BlockPoolActor* bp_actor) {
  if (bp_actor == bp_service_to_active_) {
    heart_beat_at_ = bp_actor->GetLastHeartbeat();
  }
}

void BlockPoolService::ReportActor(const std::string& nn_name,
                                   const BlockPoolActorReport& actor_reports) {
  byte::MutexLocker guard(&bp_service_report_mutex_);
  bp_service_report_.actor_reports[nn_name] = actor_reports;
  bp_service_report_.init_at = init_at_;
  bp_service_report_.register_at = register_at_;
  bp_service_report_.heart_beat_at = heart_beat_at_;
  bp_service_report_.refresh_at = refresh_at_;
  bp_service_report_.bpid = bpid_;
  bp_service_report_.report_ssd = ReportSsdEnabled();
  bp_manager_->ReportBlockPoolService(namespace_name_, bp_service_report_);
}

std::vector<std::string> BlockPoolService::GetActorNames() {
  std::vector<std::string> ret;
  byte::RwLock::ReaderLocker guard(&rwlock_);
  for (auto&& actor : actors_) {
    ret.emplace_back(actor->GetName());
  }
  return ret;
}

void BlockPoolService::ClearIBR(const std::string& name) {
  std::vector<std::string> ret;
  byte::RwLock::ReaderLocker guard(&rwlock_);
  for (auto&& actor : actors_) {
    if (actor->GetName() == name) {
      actor->TryToClearIBR(true);
    }
  }
}

bool BlockPoolService::ReportSsdEnabled() {
  return bp_manager_->ReportSsdEnabled(namespace_name_);
}

}  // namespace hdfs
}  // namespace chunkserver
}  // namespace bytestore
