// Copyright (c) 2019-present, ByteDance Inc. All rights reserved.

#include "hdfs/datanode_info_with_storage.h"

#include "byte/string/format/print.h"
#include "hdfs/datanode_id.h"
#include "hdfs/datanode_info.h"
#include "hdfs/proto/hdfs.pb.h"

namespace bytestore {
namespace chunkserver {
namespace hdfs {

DatanodeInfoWithStorage::DatanodeInfoWithStorage(const DatanodeInfo* from,
                                                 const std::string& storage_id,
                                                 StorageType storage_type)
    : datanode_info_(from->Clone()),
      storage_id_(storage_id),
      storage_type_(storage_type) {
  datanode_info_->SetSoftwareVersion(from->GetSoftwareVersion());
  datanode_info_->SetDependentHostnames(from->GetDependentHostnames());
  datanode_info_->SetLevel(from->GetLevel());
  datanode_info_->SetParent(from->GetParent());
}

DatanodeInfoWithStorage::~DatanodeInfoWithStorage() {
  delete datanode_info_;
}

std::string DatanodeInfoWithStorage::ToString() const {
  return byte::StringPrint("DatanodeInfoWithStorage[%s,%s,%b]",
                           datanode_info_->GetDatanodeID()->ToString(),
                           storage_id_, storage_type_.IsTransient());
}

exceptions::Exception DatanodeInfoWithStorage::ToProto(
    hadoop::hdfs::DatanodeInfoWithStorageProto* proto) const {
  auto dn_info_proto = new hadoop::hdfs::DatanodeInfoProto();
  auto e = datanode_info_->ToProto(dn_info_proto);
  if (!e.OK()) {
    delete dn_info_proto;
    return e;
  }
  proto->set_allocated_info(dn_info_proto);
  proto->set_storageid(storage_id_);
  hadoop::hdfs::StorageTypeProto type_proto;

  e = storage_type_.ToProto(&type_proto);
  if (!e.OK()) {
    return e;
  }
  proto->set_storagetype(type_proto);
  return exceptions::Exception();
}

}  // namespace hdfs
}  // namespace chunkserver
}  // namespace bytestore
