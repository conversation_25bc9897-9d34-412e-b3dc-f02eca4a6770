// Copyright (c) 2018-present, ByteDance Inc. All rights reserved.

#pragma once

#include <stdint.h>

#include <functional>
#include <string>
#include <unordered_map>
#include <vector>

namespace bytestore {
namespace chunkserver {
namespace hdfs {
namespace io {

class Context {
 public:
  Context(void* data, const std::function<void(void*)>& cleanup)
      : data_(data), cleanup_(cleanup) {}

  Context(const Context& ctx) {
    data_ = ctx.data_;
    cleanup_ = ctx.cleanup_;
  }

  ~Context() {
    if (cleanup_ != nullptr) {
      cleanup_(data_);
    }
  }

  void* Data() const {
    return data_;
  }
  void Data(void* data) {
    data_ = data;
  }

 private:
  void* data_;
  std::function<void(void*)> cleanup_;
};

}  // namespace io
}  // namespace hdfs
}  // namespace chunkserver
}  // namespace bytestore
