// Copyright (c) 2018-present, ByteDance Inc. All rights reserved.

#include "hdfs/io/bitmap.h"

#include "gtest/gtest.h"

namespace bytestore {
namespace chunkserver {
namespace hdfs {
namespace io {

class BitMapTests : public ::testing::Test {
 public:
  BitMapTests() {}

  ~BitMapTests() {}

  void SetUp() {}
  void TearDown() {}
};

TEST_F(BitMapTests, Init) {
  BitMap bitmap(127);
  EXPECT_TRUE(bitmap.Test(0));
  EXPECT_TRUE(bitmap.Test(127));
}

TEST_F(BitMapTests, SetTrue) {
  BitMap bitmap(127);
  uint32_t index = 64;
  EXPECT_TRUE(!bitmap.Test(index));
  bitmap.SetTrue(index);
  EXPECT_TRUE(bitmap.Test(index));
}

TEST_F(BitMapTests, SetFalse) {
  BitMap bitmap(127);
  uint32_t index = 31;
  EXPECT_TRUE(!bitmap.Test(index));
  bitmap.SetFalse(index);
  EXPECT_TRUE(!bitmap.Test(index));
}

TEST_F(BitMapTests, GetUnusedAndMask) {
  BitMap bitmap(127);
  uint32_t next = 0;
  EXPECT_TRUE(bitmap.GetUnusedAndMask(&next));
  EXPECT_EQ(next, 31u);
  EXPECT_TRUE(bitmap.GetUnusedAndMask(&next));
  EXPECT_EQ(next, 30u);
}

}  // namespace io
}  // namespace hdfs
}  // namespace chunkserver
}  // namespace bytestore
