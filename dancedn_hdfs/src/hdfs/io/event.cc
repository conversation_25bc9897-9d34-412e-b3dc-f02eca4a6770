// Copyright (c) 2018-present, ByteDance Inc. All rights reserved.

#include "hdfs/io/event.h"

#include "hdfs/io/io_worker.h"

namespace bytestore {
namespace chunkserver {
namespace hdfs {
namespace io {

EventMap::EventMap() {}

EventMap::~EventMap() {
  for (auto& item : events_) {
    free(item.second);
  }
}

EventContext* EventMap::Get(int fd) {
  auto itr = events_.find(fd);
  if (itr == events_.end()) return nullptr;
  return itr->second;
}

EventContext* EventMap::GetOrNew(int fd) {
  auto itr = events_.find(fd);
  if (itr != events_.end()) return itr->second;
  auto event = reinterpret_cast<EventContext*>(malloc(sizeof(EventContext)));
  memset(event, 0, sizeof(EventContext));
  events_.emplace(fd, event);
  return event;
}

}  // namespace io
}  // namespace hdfs
}  // namespace chunkserver
}  // namespace bytestore
