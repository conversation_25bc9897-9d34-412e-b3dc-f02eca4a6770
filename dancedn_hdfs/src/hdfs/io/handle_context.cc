// Copyright (c) 2018-present, ByteDance Inc. All rights reserved.

#include "hdfs/io/handle_context.h"

#include "byte/include/byte_log.h"
#include "hdfs/io/connection.h"
#include "hdfs/io/define.h"
#include "hdfs/io/io_worker.h"

namespace bytestore {
namespace chunkserver {
namespace hdfs {
namespace io {

static int64_t DefaultGetID(void*) {
  return 0;
}

static int DefaultConnectedHandle(IOWorker* worker, Connection* conn) {
  LOG(INFO) << "worker-" << worker->Index() << " " << conn->Address().ToString()
            << "connected";
  return IO_OK;
}

static int DefaultDisconnectedHandle(IOWorker* worker, Connection* conn) {
  return IO_OK;
}

HandleContext::HandleContext() {
  get_id_fn_ = DefaultGetID;
  encode_fn_ = nullptr;
  decode_fn_ = nullptr;
  read_handle_fn_ = nullptr;
  // write_handle_   = nullptr;
  connected_handle_ = DefaultConnectedHandle;
  disconnected_handle_ = DefaultDisconnectedHandle;
}

HandleContext::~HandleContext() {}

HandleContext* HandleContext::GetID(GetIDFn fn) {
  get_id_fn_ = fn;
  return this;
}

HandleContext* HandleContext::EncodeHandle(EncodeFn fn) {
  encode_fn_ = fn;
  return this;
}

HandleContext* HandleContext::DecodeHandle(DecodeFn fn) {
  decode_fn_ = fn;
  return this;
}

HandleContext* HandleContext::ReadHandle(ReadHandleFn fn) {
  read_handle_fn_ = fn;
  return this;
}

// HandleContext* HandleContext::WriteHandle(const IOUserHandler& handle) {
//     write_handle_ = handle;
//     return this;
// }

HandleContext* HandleContext::ConnectedHandle(const IOUserHandler& handle) {
  connected_handle_ = handle;
  return this;
}

HandleContext* HandleContext::DisconnectedHandle(const IOUserHandler& handle) {
  disconnected_handle_ = handle;
  return this;
}

int64_t HandleContext::GetID(void* object) {
  return get_id_fn_(object);
}

int HandleContext::EncodeHandle(void* object, IOBuf* buf) {
  return encode_fn_(object, buf);
}

int HandleContext::DecodeHandle(Connection* conn, Wrapper** w) {
  return decode_fn_(conn, w);
}

int HandleContext::ReadHandle(IOWorker* worker, Connection* conn, void* data) {
  return read_handle_fn_(worker, conn, data);
}

// int HandleContext::WriteHandle(IOWorker* worker, Connection* conn) {
//     return write_handle_(worker, conn);
// }

int HandleContext::ConnectedHandle(IOWorker* worker, Connection* conn) {
  return connected_handle_(worker, conn);
}

int HandleContext::DisconnectedHandle(IOWorker* worker, Connection* conn) {
  return disconnected_handle_(worker, conn);
}

}  // namespace io
}  // namespace hdfs
}  // namespace chunkserver
}  // namespace bytestore
