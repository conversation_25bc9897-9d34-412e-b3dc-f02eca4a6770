// Copyright (c) 2018-present, ByteDance Inc. All rights reserved.

#pragma once

#include <stdint.h>

#include <functional>
#include <string>
#include <unordered_map>
#include <vector>

namespace bytestore {
namespace chunkserver {
namespace hdfs {
namespace io {

class Wrapper {
 public:
  Wrapper() : data_(nullptr), cleanup_(nullptr) {}

  Wrapper(void* data, const std::function<void(void*)>& cleanup)
      : data_(data), cleanup_(cleanup) {}

  Wrapper(const Wrapper& ctx) = delete;
  Wrapper& operator=(const Wrapper& w) = delete;

  ~Wrapper() {
    if (data_ != nullptr && cleanup_ != nullptr) {
      cleanup_(data_);
    }
  }

  void* Data() const {
    return data_;
  }

  void* Release() {
    void* t = data_;
    data_ = nullptr;
    return t;
  }

 private:
  void* data_;
  std::function<void(void*)> cleanup_;
};

}  // namespace io
}  // namespace hdfs
}  // namespace chunkserver
}  // namespace bytestore
