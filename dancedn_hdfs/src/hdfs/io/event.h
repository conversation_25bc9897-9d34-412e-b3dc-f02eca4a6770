// Copyright (c) 2018-present, ByteDance Inc. All rights reserved.

#pragma once

#include <stdint.h>

#include <functional>
#include <string>
#include <unordered_map>
#include <vector>

namespace bytestore {
namespace chunkserver {
namespace hdfs {
namespace io {

class IOWorker;
class Connection;
typedef int (*IOHandler)(IOWorker* worker, int fd, void* data);

typedef struct EventContext {
  uint32_t mask_;
  void* data_;
  I<PERSON>andler read_fn_;
  I<PERSON>andler write_fn_;
} EventContext;

class EventMap {
 public:
  EventMap();
  ~EventMap();

  EventContext* Get(int fd);
  EventContext* GetOrNew(int fd);

 private:
  std::unordered_map<int, EventContext*> events_;
};

}  // namespace io
}  // namespace hdfs
}  // namespace chunkserver
}  // namespace bytestore
