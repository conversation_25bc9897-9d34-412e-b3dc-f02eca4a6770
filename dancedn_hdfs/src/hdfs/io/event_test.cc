// Copyright (c) 2018-present, ByteDance Inc. All rights reserved.

#include "hdfs/io/event.h"

#include "gtest/gtest.h"

namespace bytestore {
namespace chunkserver {
namespace hdfs {
namespace io {

class EventTests : public ::testing::Test {};

TEST_F(EventTests, GetOrNewEvent) {
  EventMap event_map;
  int fd = 0;
  EXPECT_EQ(event_map.Get(fd), nullptr);
  event_map.GetOrNew(fd);
  EXPECT_NE(event_map.Get(fd), nullptr);
}

}  // namespace io
}  // namespace hdfs
}  // namespace chunkserver
}  // namespace bytestore
