// Copyright (c) 2019-present, ByteDance Inc. All rights reserved.

#pragma once

#include <memory>
#include <string>

#include "hdfs/replica.h"

namespace bytestore {
namespace chunkserver {
namespace hdfs {

class Block;
class Store;
class ExtendedBlock;

class ReplicaInfo : public Replica {
 public:
  ReplicaInfo();
  ReplicaInfo(const ExtendedBlock* block, const std::string& storage_uuid,
              bool is_transient, bool checksum_enabled, uint16_t disk_id = 0,
              PlacementStorageType type = PLM_STORAGE_ANY);
  ReplicaInfo(const std::string& bpid, uint64_t block_id, uint64_t len,
              uint64_t gs, const std::string& storage_uuid, bool is_transient,
              bool checksum_enabled, uint16_t disk_id = 0,
              PlacementStorageType type = PLM_STORAGE_ANY);
  ReplicaInfo(const ReplicaInfo& from);
  explicit ReplicaInfo(const std::shared_ptr<ReplicaInfo>& from);
  virtual ~ReplicaInfo();

  bool Equals(const ReplicaInfo* b);

  ExtendedBlock* GetBlock() const {
    return block_;
  }

  virtual bool IsUnlinked() const {
    return true;
  }
  virtual void SetUnlinked() {}

  void SetBlock(const ExtendedBlock* block);

  std::string GetBlockPoolID() const;
  void SetBlockPoolID(const std::string& bpid);

  uint64_t GetBlockID() const override;
  virtual void SetBlockID(uint64_t block_id);

  uint64_t GetGS() const override;
  virtual void SetGS(uint64_t gs);

  uint64_t GetNumBytes() const override;
  virtual void SetNumBytes(uint64_t bytes);

  std::string GetStorageUuid() const override;
  bool IsOnTransientStorage() const override;
  bool ChecksumEnabled() const {
    return checksum_enabled_;
  }

  std::string GetStateString() const;

  uint16_t GetDiskId() const override {
    return disk_id_;
  }
  void SetDiskId(uint16_t disk_id) {
    disk_id_ = disk_id;
  }

  PlacementStorageType GetPlacementType() const override {
    return placement_type_;
  }

 private:
  ExtendedBlock* block_;
  std::string storage_uuid_;
  bool is_transient_;
  bool checksum_enabled_;
  uint16_t disk_id_;
  PlacementStorageType placement_type_;
};

}  // namespace hdfs
}  // namespace chunkserver
}  // namespace bytestore
