// Copyright (c) 2018-present, ByteDance Inc. All rights reserved.

#include <brpc/channel.h>

#include <deque>
#include <fstream>
#include <memory>
#include <set>
#include <unordered_map>
#include <vector>

#include "byte/io/local_filesystem.h"
#include "chunkserver/env.h"
#include "common/media_flags.h"
#include "common/metrics.h"
#include "gflags/gflags.h"
#include "gtest/gtest.h"
#include "hdfs/block.h"
#include "hdfs/block_local_path_info.h"
#include "hdfs/block_pool_actor.h"
#include "hdfs/block_pool_manager.h"
#include "hdfs/block_pool_service.h"
#include "hdfs/block_report_options.h"
#include "hdfs/blocks_with_locations.h"
#include "hdfs/chunkserver_store.h"
#include "hdfs/constants.h"
#include "hdfs/datanode.h"
#include "hdfs/datanode_config.h"
#include "hdfs/datanode_id.h"
#include "hdfs/datanode_registration.h"
#include "hdfs/datanode_storage.h"
#include "hdfs/datanode_stream_server.h"
#include "hdfs/directory_scanner.h"
#include "hdfs/extended_block.h"
#include "hdfs/finalized_replica.h"
#include "hdfs/hdfs_blocks_metadata.h"
#include "hdfs/hdfs_http_service_impl.h"
#include "hdfs/mocks.h"
#include "hdfs/namespace_info.h"
#include "hdfs/replica_in_pipeline.h"
#include "hdfs/replica_info.h"
#include "hdfs/replica_state.h"
#include "hdfs/store.h"
#include "hdfs/volume_black_list.h"
#include "io/file_util.h"
#include "nlohmann/json.hpp"

#define SYNC_HANDLE_OPER(FUNC)                                                 \
  {                                                                            \
    byte::CountDownLatch latch(1);                                             \
    google::protobuf::Closure* done = google::protobuf::NewCallback(           \
        this, &HttpServiceTests::IOOperDone, &latch);                          \
    oper->SetCallback(done);                                                   \
    oper->request_.set_disk_id(disk_id);                                       \
    dn_interface_->FUNC(&oper->controller_, &oper->request_, &oper->response_, \
                        oper->done_);                                          \
    latch.Wait();                                                              \
    EXPECT_EQ(BYTESTORE_OK, oper->response_.error_code());                     \
  }

DECLARE_string(bytestore_hdfs_listen_ip_address);
DECLARE_uint32(bytestore_hdfs_http_port);
DECLARE_uint32(bytestore_hdfs_max_xceiver_count);
DECLARE_uint32(bytestore_hdfs_max_tasks_per_disk);
DECLARE_uint64(bytestore_hdfs_recovery_lock_timeout);
DECLARE_int32(bytestore_hdfs_migration_scanner_concurrency);
DECLARE_uint32(bytestore_hdfs_migration_scanner_interval_time_ms);
DECLARE_int32(bytestore_hdfs_directory_scanner_concurrency);
DECLARE_uint32(bytestore_hdfs_directory_scanner_interval_time_ms);
DECLARE_uint64(bytestore_hdfs_tiering_freeze_size);
DECLARE_MEDIA_FLAG_uint32(bytestore_hdfs_max_replicate_concurrency_per_disk);
DECLARE_string(bytestore_hdfs_bvc_version);
DECLARE_string(bytestore_chunkserver_work_dir);
DECLARE_bool(bytestore_chunkserver_truncate_chunk_after_freeze);
DECLARE_MEDIA_FLAG_int64(bytestore_chunkserver_reserved_disk_size);
DECLARE_bool(bytestore_chunkserver_admit_duplicate_uuid_disk);
DECLARE_uint32(bytestore_hdfs_checksum_mode);
DECLARE_int32(bytestore_chunkserver_delete_manager_interval_time_ms);
DECLARE_bool(bytestore_chunkserver_enable_tiering_migration);
DECLARE_uint64(bytestore_chunkserver_tiering_migration_sync_delay_ms);
DECLARE_uint32(
    bytestore_chunkserver_tiering_migration_low_water_mark_percentage);
DECLARE_int64(
    bytestore_hdfs_tiering_migration_max_read_throughput_bytes_per_disk);
DECLARE_int64(
    bytestore_hdfs_tiering_migration_max_write_throughput_bytes_per_disk);
DECLARE_int32(bytestore_hdfs_user_default_priority);
DECLARE_uint32(bytestore_hdfs_user_priority_num);
DECLARE_uint32(bytestore_hdfs_volume_operation_history_records_max_number);
DECLARE_string(bytestore_hdfs_volume_black_list_file);
DECLARE_string(bytestore_hdfs_user_keywords_p0);
DECLARE_bool(bytestore_hdfs_remove_volume_online_enabled);
DECLARE_bool(bytestore_hdfs_user_space_reclaim_enable);

namespace bytestore {
namespace chunkserver {
namespace hdfs {

exceptions::Exception GenVersionFileForHdfs(DiskIdConfMap disk_id_conf_map);

class HttpServiceTests : public ::testing::Test {
 public:
  HttpServiceTests() {}

  ~HttpServiceTests() {}

  void SetUp() {
    FLAGS_bytestore_chunkserver_enable_tiering_migration = true;
    FLAGS_bytestore_chunkserver_tiering_migration_sync_delay_ms = 0;
    FLAGS_bytestore_chunkserver_tiering_migration_low_water_mark_percentage = 0;
    MFLAGS_set(bytestore_chunkserver_reserved_disk_size, TYPE_SATA_HDD,
               3ULL * 1000 * 1000 * 1000);
    metrics_internal::InitFastMetrics();
    std::string work_dir = "./HttpServiceTestDir";
    namespace_id_ = 1004580134;
    cluster_id_ = "CID-9a628355-6954-473b-a66c-d34d7c2b3805";
    create_time_ = 1546064706393;
    build_version_ = "2.6.0";
    software_version_ = "2.6.3";
    bpid_ = "BP-2026362776-*************-1546064706393";
    byte::LocalFileSystem local_fs;
    // if work_dir exists, delete it
    if (byte::DirectoryExists(std::string(work_dir))) {
      byte::DeleteOptions delete_options;
      delete_options.recursively_ = true;
      local_fs.DeleteDir(std::string(work_dir), delete_options);
    }
    EXPECT_TRUE(
        local_fs.CreateDir(std::string(work_dir), byte::CreateOptions()).ok());
    byte::CreateOptions create_options;
    create_options.create_parents_ = true;
    EXPECT_TRUE(local_fs
                    .CreateDir(std::string(work_dir) + "/disk01/dancedn",
                               create_options)
                    .ok());
    EXPECT_TRUE(local_fs
                    .CreateDir(std::string(work_dir) + "/disk02/dancedn",
                               create_options)
                    .ok());
    EXPECT_TRUE(local_fs
                    .CreateDir(std::string(work_dir) + "/disk03/dancedn",
                               create_options)
                    .ok());
    disk_id_conf_map_[1] =
        DiskConfig(1, std::string(work_dir) + "/disk01/dancedn", CDT_SATA_HDD);
    disk_id_conf_map_[2] =
        DiskConfig(2, std::string(work_dir) + "/disk02/dancedn", CDT_SATA_HDD);
    disk_id_conf_map_[3] =
        DiskConfig(3, std::string(work_dir) + "/disk03/dancedn", CDT_NVME_SSD);
    auto ex = GenVersionFileForHdfs(disk_id_conf_map_);
    EXPECT_TRUE(ex.OK());
    // gflags::FlagSaver saver;
    FLAGS_bytestore_chunkserver_work_dir = std::string(work_dir);
    FLAGS_bytestore_chunkserver_truncate_chunk_after_freeze = true;
    FLAGS_bytestore_chunkserver_admit_duplicate_uuid_disk = true;
    FLAGS_bytestore_chunkserver_delete_manager_interval_time_ms = 1000;

    cs_env_ = new Env(work_dir);
    cs_config_.DEBUG_ImportConfig(disk_id_conf_map_);
    EnvOptions env_options;
    env_options.cs_config_ = &cs_config_;
    EXPECT_EQ(cs_env_->Init(env_options), BYTESTORE_OK);
    EXPECT_EQ(cs_env_->Start(), BYTESTORE_OK);
    EXPECT_EQ(cs_env_->UpdateCSStatus(CHUNKSERVER_NORMAL), BYTESTORE_OK);
    dn_interface_.reset(new DatanodeInterface(&cs_config_, cs_env_));

    // block id = 1, bpid = BP-2026362776-*************-1546064706393
    ChunkIdMeta chunk_id1(1, 2026362776, 3232237157, 1546064706393);
    std::unique_ptr<ExtendedBlock> eblock1(
        new ExtendedBlock(bpid_, chunk_id1.block_id_));
    CreateChunk(1, eblock1.get());

    static const uint32_t k_buf_size =
        4096;  // Must be equals to multiple of 32
    static const uint32_t k_num_rounds = 100;
    char* data = new char[k_buf_size];
    std::unique_ptr<char[]> scoped_data(data);
    for (uint32_t i = 0; i < k_buf_size; ++i) {
      data[i] = i % 32 + 'A';
    }
    io::IOChunk* chunk = new io::IOChunk(k_buf_size);
    chunk->WriteBytes(reinterpret_cast<uint8_t*>(data), k_buf_size);

    for (uint32_t i = 0; i < k_num_rounds; ++i) {
      WriteChunk(1, eblock1.get(), data, k_buf_size, i * k_buf_size);
    }
    io::IOChunk::Destroy(chunk);

    char* write_xattr = new char[XATTR_BYTES];
    memset(write_xattr, 0, 32);
    std::unique_ptr<char[]> write_xattr_deleter(write_xattr);
    BlockMeta* meta = reinterpret_cast<BlockMeta*>(write_xattr);
    // block1, gs_ = 1546064706393, finalized, hdd
    meta->gs_ = 1546064706393;
    meta->fin_ = 1;
    SetChunkAttr(1, eblock1.get(), write_xattr);
    FreezeChunk(1, eblock1.get(), k_num_rounds * k_buf_size);

    datanode_ = new MockDataNode();
    EXPECT_CALL(*datanode_,
                NotifyNamenodeDeletedBlock(::testing::_, ::testing::_))
        .WillRepeatedly(::testing::Return());
    EXPECT_TRUE(datanode_->GetPsm().empty());
    MockDataNodeConfig* dn_config = new MockDataNodeConfig();
    datanode_->SetConfig(dn_config);
    MockDataTransferManager* dtm = new MockDataTransferManager();
    datanode_->SetDataTransferManager(dtm);

    ns_info_ =
        new NameSpaceInfo(namespace_id_, cluster_id_, bpid_, create_time_,
                          build_version_, software_version_);

    store_ = new MockChunkServerStore(&cs_config_, cs_env_);
    auto e = store_->InitStorage(datanode_, &disk_id_conf_map_);
    EXPECT_TRUE(e.OK());

    e = store_->AddDirAndBlock(ns_info_);
    EXPECT_TRUE(e.OK());
    datanode_->SetStorage(store_);

    std::string ip = FLAGS_bytestore_hdfs_listen_ip_address;
    int port =
        5186;  // To run tests in parallel, the port is guaranteed to be unique
    dn_stream_server_ = new DataNodeStreamServer(ip, port, datanode_);
    EXPECT_TRUE(dn_stream_server_->Init());
    EXPECT_TRUE(dn_stream_server_->Start());
    datanode_->SetDataNodeStreamServer(dn_stream_server_);
    LOG(INFO) << "Start stream server success";

    // There is no permission to mkdir /opt/tiger/dancedn on the devbox
    std::string vbl_dir = "./dancedn";
    FLAGS_bytestore_hdfs_volume_black_list_file =
        vbl_dir + "/volume_black_list";
    VolumeBlackList* vbl = new VolumeBlackList();
    e = vbl->Init();
    EXPECT_TRUE(e.OK());
    datanode_->SetVolumeBlackList(vbl);

    http_service_impl_ = std::make_unique<MockHdfsHttpServiceImpl>(datanode_);
    EXPECT_EQ(http_server_.AddService(http_service_impl_.get(),
                                      brpc::SERVER_DOESNT_OWN_SERVICE,
                                      "/api/v1/* => DispatchRequests"),
              0);
    EXPECT_EQ(http_server_.Start(FLAGS_bytestore_hdfs_http_port, nullptr), 0);
    LOG(INFO) << "Start http server success";
  }

  void TearDown() {
    cs_env_->Stop();
    byte::DeleteOptions delete_options;
    delete_options.recursively_ = true;
    byte::LocalFileSystem local_fs;
    std::string work_dir = "./HttpServiceTestDir/";
    local_fs.DeleteDir(std::string(work_dir), delete_options);
    std::string vbl_dir = "./dancedn";
    local_fs.DeleteDir(std::string(vbl_dir), delete_options);
    if (http_server_.Stop(0) != 0) {
      LOG(ERROR) << "stop http server failed";
    } else {
      LOG(INFO) << "stop http server success";
    }
    dn_stream_server_->Stop();
    http_server_.Join();
    // add for UT: brpc server
    http_server_.ReleaseKeytableForUT();
    dn_stream_server_->Join();
    delete cs_env_;
    delete datanode_;
    delete ns_info_;
  }

  static void SetUpTestCase() {}
  static void TearDownTestCase() {
    byte::GetLoggingSystem()->Shutdown();
  }

  bool HttpGet(const std::string& path, std::string* response_str) {
    // A Channel represents a communication line to a Server. Notice that
    // Channel is thread-safe and can be shared by all threads in your program.
    brpc::Channel channel;
    // Initialize the channel, NULL means using default options.
    brpc::ChannelOptions options;
    options.protocol = brpc::PROTOCOL_HTTP;
    std::string uri = byte::StringPrint("%s:%u%s", "localhost",
                                        FLAGS_bytestore_hdfs_http_port, path);
    if (channel.Init(uri.c_str(), &options) != 0) {
      LOG(ERROR) << "Fail to initialize channel";
      return false;
    }

    // We will receive response synchronously, safe to put variables
    // on stack.
    brpc::Controller cntl;
    brpc::HttpMethod method = brpc::HTTP_METHOD_GET;
    cntl.http_request().uri() = uri;
    cntl.http_request().set_method(method);
    LOG(INFO) << "method:" << brpc::HttpMethod2Str(method)
              << ", uri:" << cntl.http_request().uri();

    // Because `done'(last parameter) is NULL, this function waits until
    // the response comes back or error occurs(including timedout).
    channel.CallMethod(NULL, &cntl, NULL, NULL, NULL);
    if (!cntl.Failed()) {
      LOG(INFO) << "Received response from " << cntl.remote_side() << " to "
                << cntl.local_side()
                << " attached=" << cntl.response_attachment()
                << " latency=" << cntl.latency_us() << "us";
    } else {
      LOG(WARNING) << cntl.ErrorText();
      return false;
    }
    *response_str = cntl.response_attachment().to_string();
    return true;
  }

  bool HttpPost(const std::string& path, const std::string attach,
                std::string* response_str) {
    // A Channel represents a communication line to a Server. Notice that
    // Channel is thread-safe and can be shared by all threads in your program.
    brpc::Channel channel;
    // Initialize the channel, NULL means using default options.
    brpc::ChannelOptions options;
    options.protocol = brpc::PROTOCOL_HTTP;
    std::string uri = byte::StringPrint("%s:%u%s", "localhost",
                                        FLAGS_bytestore_hdfs_http_port, path);
    if (channel.Init(uri.c_str(), &options) != 0) {
      LOG(ERROR) << "Fail to initialize channel";
      return false;
    }

    // We will receive response synchronously, safe to put variables
    // on stack.
    brpc::Controller cntl;
    brpc::HttpMethod method = brpc::HTTP_METHOD_POST;
    cntl.http_request().uri() = uri;
    cntl.http_request().set_method(method);
    cntl.request_attachment().append(attach);
    LOG(INFO) << "method:" << brpc::HttpMethod2Str(method)
              << ", uri:" << cntl.http_request().uri()
              << ", attachment:" << attach;

    // Because `done'(last parameter) is NULL, this function waits until
    // the response comes back or error occurs(including timedout).
    channel.CallMethod(NULL, &cntl, NULL, NULL, NULL);
    if (!cntl.Failed()) {
      LOG(INFO) << "Received response from " << cntl.remote_side() << " to "
                << cntl.local_side()
                << " attached=" << cntl.response_attachment()
                << " latency=" << cntl.latency_us() << "us";
    } else {
      LOG(WARNING) << cntl.ErrorText();
      return false;
    }
    *response_str = cntl.response_attachment().to_string();
    return true;
  }

  bool CheckCodeAndMsg(const std::string& response_str, int expect_code = 0,
                       const std::string& expect_msg = "OK") {
    nlohmann::json result;
    int code;
    std::string msg;
    try {
      result = nlohmann::json::parse(response_str);
      code = result["code"].get<int>();
      msg = result["msg"].get<std::string>();
    } catch (nlohmann::json::exception& e) {
      LOG(ERROR) << "parse response failed:" << e.what()
                 << " response_json:" << response_str;
      return false;
    }
    if (code != expect_code || msg.find(expect_msg) == std::string::npos) {
      return false;
    }
    return true;
  }

  template <typename T>
  bool GetAttrJson(const std::string& response_str,
                   const std::string& attr_name, T* ret) {
    nlohmann::json result;
    int code;
    std::string msg;
    try {
      result = nlohmann::json::parse(response_str);
      code = result["code"].get<int>();
      msg = result["msg"].get<std::string>();
    } catch (nlohmann::json::exception& e) {
      LOG(ERROR) << "parse response failed:" << e.what()
                 << " response_json:" << response_str;
      return false;
    }

    if (code != 0 || msg != "OK") {
      return false;
    }
    if (result.find(attr_name) == result.end()) {
      LOG(ERROR) << "result not contain data, result:\n" << result.dump(4);
      return false;
    }
    try {
      *ret = result[attr_name].get<T>();
    } catch (nlohmann::json::exception& e) {
      LOG(ERROR) << "parse attr failed:" << e.what() << " result[" << attr_name
                 << "]:" << result[attr_name];
      return false;
    }
    return true;
  }

  template <typename T>
  bool TestGetConfig(const std::string& config_name, const T& expect_value) {
    std::string resp_get;
    if (!HttpGet("/api/v1/config", &resp_get)) {
      return false;
    }
    nlohmann::json data = nlohmann::json::object();
    if (!GetAttrJson(resp_get, "data", &data)) {
      return false;
    }
    if (data.find(config_name) == data.end()) {
      return false;
    }
    T config_value;
    try {
      config_value = data[config_name].get<T>();
    } catch (nlohmann::json::exception& e) {
      LOG(ERROR) << "parse config value failed:" << e.what() << " data["
                 << config_name << "]:" << data[config_name];
      return false;
    }
    return config_value == expect_value;
  }

  bool TestSetConfig(const std::string& config_name,
                     const std::string& new_value, int expect_code = 0,
                     const std::string& expect_msg = "OK") {
    std::string attach =
        byte::StringPrint("{\"%s\": %s}", config_name, new_value);
    std::string resp_set;
    if (!HttpPost("/api/v1/config", attach, &resp_set)) {
      return false;
    }
    return CheckCodeAndMsg(resp_set, expect_code, expect_msg);
  }

  void CreateChunk(uint32_t disk_id, ExtendedBlock* block,
                   PlacementStorageType type = PLM_STORAGE_ANY) {
    uint32_t rtime = type == PLM_STORAGE_ANY ? -1 : 1;
    std::unique_ptr<CreateOper> oper(new CreateOper(
        PRIORITY_REAL_TIME, block, 5000000, ChunkType::TYPE_REPLICATED_CHUNK, 0,
        false, type, rtime));
    SYNC_HANDLE_OPER(CreateChunk)
  }

  void WriteChunk(uint32_t disk_id, ExtendedBlock* block, char* data,
                  uint32_t length, uint32_t offset) {
    std::unique_ptr<WriteOper> oper(new WriteOper(
        PRIORITY_ELASTIC, block, 5000000, length, offset, data, false));
    SYNC_HANDLE_OPER(WriteChunk)
  }

  void FreezeChunk(uint32_t disk_id, ExtendedBlock* block, uint32_t length,
                   PlacementStorageType type = PLM_STORAGE_ANY) {
    std::unique_ptr<FreezeOper> oper(
        new FreezeOper(PRIORITY_ELASTIC, block, 5000000, length, false, type));
    SYNC_HANDLE_OPER(FreezeChunk)
  }

  void SetChunkAttr(const uint32_t disk_id, ExtendedBlock* block, char* meta,
                    PlacementStorageType type = PLM_STORAGE_ANY) {
    std::unique_ptr<XAttrSetOper> oper(
        new XAttrSetOper(PRIORITY_ELASTIC, block, 5000000, meta, type));
    SYNC_HANDLE_OPER(SetXATTRChunk)
  }

  void IOOperDone(byte::CountDownLatch* latch) {
    latch->CountDown();
  }

  DataNodeConfig::NameServiceMap GenNameServiceMap() {
    DataNodeConfig::NameServiceMap nameservices;
    std::map<std::string, std::string> ips;
    ips.emplace(std::make_pair("dng2", "[fdbd:dc03:1:98::74]:5061"));
    ips.emplace(std::make_pair("ray2", "[fdbd:dc03:1:155::150]:5061"));
    nameservices.emplace(std::make_pair("dngraybackend", ips));
    return nameservices;
  }

  BlockPoolManagerReport GenBlockPoolManagerReport() {
    BlockPoolManagerReport bpmr;
    BlockPoolActorReport bpar1;
    bpar1.nn_addr = "test_addr_1";
    bpar1.is_active = true;
    bpar1.last_heart_beat = 0;
    bpar1.last_block_report = 0;
    bpar1.last_incremental_block_report = 0;
    bpar1.last_cache_report = 0;
    BlockPoolActorReport bpar2 = bpar1;
    bpar2.nn_addr = "test_addr_2";
    bpar2.is_active = false;
    BlockPoolServiceReport bpsr;
    bpsr.bpid = "BP-2026362776-*************-1546064706393";
    bpsr.init_at = 0;
    bpsr.register_at = 0;
    bpsr.heart_beat_at = 0;
    bpsr.refresh_at = 0;
    bpsr.actor_reports.emplace(std::make_pair("te2", bpar1));
    bpsr.actor_reports.emplace(std::make_pair("st2", bpar2));
    bpmr.block_pool_reports.emplace(std::make_pair("testbackend", bpsr));
    return bpmr;
  }

 public:
  MockDataNode* datanode_;
  MockChunkServerStore* store_;
  std::unique_ptr<MockHdfsHttpServiceImpl> http_service_impl_;

 private:
  Env* cs_env_;
  ChunkServerConfig cs_config_;
  NameSpaceInfo* ns_info_;
  DiskIdConfMap disk_id_conf_map_;
  std::unique_ptr<DatanodeInterface> dn_interface_;
  DataNodeStreamServer* dn_stream_server_;
  brpc::Server http_server_;
  std::string datanode_uuid_;
  int namespace_id_ = 1004580134;
  std::string cluster_id_ = "CID-9a628355-6954-473b-a66c-d34d7c2b3805";
  uint64_t create_time_ = 1546064706393;
  std::string build_version_ = "2.6.0";
  std::string software_version_ = "2.6.3";
  std::string bpid_ = "BP-2026362776-*************-1546064706393";
};

TEST_F(HttpServiceTests, UnknownRoute) {
  std::string resp_get;
  ASSERT_TRUE(HttpGet("/api/v1/unknown", &resp_get));
  ASSERT_FALSE(CheckCodeAndMsg(resp_get));
}

/*
{
    "code": 0,
    "md5": "6d84fcc73fcce76bfe2e1b6d9a97966f",
    "msg": "OK"
}
*/
TEST_F(HttpServiceTests, GetBlockMD5) {
  std::string resp_get;
  uint64_t test_blk_id = 1;

  // bpm is null
  std::string path =
      byte::StringPrint("/api/v1/bp/testbackend/block/%u/md5", test_blk_id);
  ASSERT_TRUE(HttpGet(path, &resp_get));
  ASSERT_TRUE(CheckCodeAndMsg(resp_get, -4, "block_pool_manager is null"));

  MockBlockPoolManager* block_pool_manager =
      new MockBlockPoolManager(datanode_);
  EXPECT_CALL(*block_pool_manager, GetBlockPoolID(::testing::_))
      .WillRepeatedly(::testing::Return(
          std::string("BP-2026362776-*************-1546064706393")));
  datanode_->SetBlockPoolManager(block_pool_manager);

  // normally
  std::string resp_get_normally;
  ASSERT_TRUE(HttpGet(path, &resp_get_normally));
  std::string md5;
  ASSERT_TRUE(GetAttrJson(resp_get_normally, "md5", &md5));
  ASSERT_EQ(md5.length(), 32);

  // replica not found
  test_blk_id = 123;
  ASSERT_TRUE(HttpGet(
      byte::StringPrint("/api/v1/bp/testbackend/block/%u/md5", test_blk_id),
      &resp_get));
  ASSERT_TRUE(CheckCodeAndMsg(resp_get, -2, "replica not found"));
}

/*
{
    "code": 0,
    "data": {
        "block_id": 4929521730,
        "disk_id": 12,
        "gs": 8110670045,
        "len": 16777216,
        "placement_type": 0,
        "state": "FINALIZED",
        "storage_uuid": "DS-fb50f650-079e-4091-b1dd-7b4cdbe5c520",
        "vlen": 16777216
    },
    "msg": "OK"
}
*/
TEST_F(HttpServiceTests, GetBlockInfo) {
  std::string resp_get;

  // bpm is null
  uint64_t test_blk_id = 1;
  std::string path =
      byte::StringPrint("/api/v1/bp/testbackend/block/%u/info", test_blk_id);
  ASSERT_TRUE(HttpGet(path, &resp_get));
  ASSERT_TRUE(CheckCodeAndMsg(resp_get, -3, "block_pool_manager is null"));

  MockBlockPoolManager* block_pool_manager =
      new MockBlockPoolManager(datanode_);
  EXPECT_CALL(*block_pool_manager, GetBlockPoolID(::testing::_))
      .WillRepeatedly(::testing::Return(
          std::string("BP-2026362776-*************-1546064706393")));
  datanode_->SetBlockPoolManager(block_pool_manager);

  // normal
  std::string resp_get_normally;
  ASSERT_TRUE(HttpGet(path, &resp_get_normally));
  nlohmann::json data = nlohmann::json::object();
  ASSERT_TRUE(GetAttrJson(resp_get_normally, "data", &data));
  ASSERT_NE(data.find("block_id"), data.end());
  ASSERT_NE(data.find("gs"), data.end());
  ASSERT_NE(data.find("len"), data.end());
  ASSERT_NE(data.find("disk_id"), data.end());
  uint64_t block_id;
  uint64_t gs;
  uint64_t len;
  DiskId disk_id;
  try {
    block_id = data["block_id"].get<uint64_t>();
    gs = data["gs"].get<uint64_t>();
    len = data["len"].get<uint64_t>();
    disk_id = data["disk_id"].get<DiskId>();
  } catch (nlohmann::json::exception& e) {
    LOG(ERROR) << "parse block info failed:" << e.what() << " data:" << data;
    ASSERT_TRUE(false);
  }
  ASSERT_EQ(block_id, 1);
  ASSERT_EQ(gs, 1546064706393);
  ASSERT_EQ(len, 409600);
  ASSERT_EQ(disk_id, 1);

  // replica not found
  test_blk_id = 123;
  ASSERT_TRUE(HttpGet(
      byte::StringPrint("/api/v1/bp/testbackend/block/%u/info", test_blk_id),
      &resp_get));
  ASSERT_TRUE(CheckCodeAndMsg(resp_get, -2, "replica not found"));
}

TEST_F(HttpServiceTests, GetStatus) {
  EXPECT_CALL(*datanode_, GetBpManagerReport())
      .WillRepeatedly(::testing::Return(GenBlockPoolManagerReport()));
  std::string resp_get;
  ASSERT_TRUE(HttpGet("/api/v1/status", &resp_get));
  nlohmann::json data = nlohmann::json::object();
  ASSERT_TRUE(GetAttrJson(resp_get, "data", &data));
  ASSERT_NE(data.find("current_activity"), data.end());
  ASSERT_NE(data.find("serving"), data.end());
  std::unordered_map<std::string, int> current_activity;
  bool serving;
  try {
    current_activity =
        data["current_activity"].get<std::unordered_map<std::string, int>>();
    serving = data["serving"].get<bool>();
  } catch (nlohmann::json::exception& e) {
    LOG(ERROR) << "parse status failed:" << e.what() << " data:" << data;
    ASSERT_TRUE(false);
  }
  ASSERT_EQ(current_activity.size(), 3);
  ASSERT_FALSE(serving);
}

TEST_F(HttpServiceTests, GetAndSetMaxXceiverCount) {
  std::string config_name = "dfs.datanode.max.transfer.threads";
  // get config
  ASSERT_TRUE(
      TestGetConfig(config_name, FLAGS_bytestore_hdfs_max_xceiver_count));
  // set config
  uint32_t new_value = 8192;
  ASSERT_TRUE(TestSetConfig(config_name, byte::IntegerToString(new_value)));
  // get config after set
  ASSERT_TRUE(TestGetConfig(config_name, new_value));

  // abnormally set config: request format problem
  ASSERT_TRUE(TestSetConfig(config_name, "format_problem", -4,
                            "check your request format"));
  // abnormally set config: is not number
  ASSERT_TRUE(TestSetConfig(config_name, "\"is not number\"", -4,
                            "update request error"));
  // abnormally set config: 90
  uint32_t max_xceiver_count_ab = 90;
  ASSERT_TRUE(TestSetConfig(config_name,
                            byte::IntegerToString(max_xceiver_count_ab), -4,
                            "update request error"));
  // get max_xceiver_count after abnormally settings
  ASSERT_TRUE(TestGetConfig(config_name, new_value));
}

TEST_F(HttpServiceTests, GetAndSetMaxTasksPerDisk) {
  std::string config_name = "dfs.datanode.max.tasks.per.disk";
  // get config
  ASSERT_TRUE(
      TestGetConfig(config_name, FLAGS_bytestore_hdfs_max_tasks_per_disk));
  // set config
  uint32_t new_value = 1000;
  ASSERT_TRUE(TestSetConfig(config_name, byte::IntegerToString(new_value)));
  // get config after set
  ASSERT_TRUE(TestGetConfig(config_name, new_value));

  // abnormally set config: is not number
  ASSERT_TRUE(TestSetConfig(config_name, "\"is not number\"", -4,
                            "update request error"));
}

TEST_F(HttpServiceTests, GetAndSetRecoveryLockTimeout) {
  std::string config_name = "dfs.datanode.recovery.lock.timeout";
  // get config
  ASSERT_TRUE(
      TestGetConfig(config_name, FLAGS_bytestore_hdfs_recovery_lock_timeout));
  // set config
  uint32_t new_value = 120 * 1000;
  ASSERT_TRUE(TestSetConfig(config_name, byte::IntegerToString(new_value)));
  // get config after set
  ASSERT_TRUE(TestGetConfig(config_name, new_value));

  // abnormally set config: is not number
  ASSERT_TRUE(TestSetConfig(config_name, "\"is not number\"", -4,
                            "update request error"));
  // abnormally set config: 9 * 1000
  uint32_t recovery_lock_timeout_ab = 9 * 1000;
  ASSERT_TRUE(TestSetConfig(config_name,
                            byte::IntegerToString(recovery_lock_timeout_ab), -4,
                            "update request error"));
}

TEST_F(HttpServiceTests, GetAndSetMigrationScannerConcurrency) {
  std::string config_name = "dfs.datanode.migration_scanner.concurrency";
  // get config
  ASSERT_TRUE(TestGetConfig(
      config_name, FLAGS_bytestore_hdfs_migration_scanner_concurrency));
  // set config
  uint32_t new_value = 1;
  ASSERT_TRUE(TestSetConfig(config_name, byte::IntegerToString(new_value)));
  // get config after set
  ASSERT_TRUE(TestGetConfig(config_name, new_value));

  // abnormally set config: is not number
  ASSERT_TRUE(TestSetConfig(config_name, "\"is not number\"", -4,
                            "update request error"));
}

TEST_F(HttpServiceTests, GetAndSetMigrationScannerInterval) {
  std::string config_name = "dfs.datanode.migration_scanner.interval.ms";
  // get config
  ASSERT_TRUE(TestGetConfig(
      config_name, FLAGS_bytestore_hdfs_migration_scanner_interval_time_ms));
  // set config
  uint32_t new_value = 20 * 60 * 1000;
  ASSERT_TRUE(TestSetConfig(config_name, byte::IntegerToString(new_value)));
  // get config after set
  ASSERT_TRUE(TestGetConfig(config_name, new_value));

  // abnormally set config: is not number
  ASSERT_TRUE(TestSetConfig(config_name, "\"is not number\"", -4,
                            "update request error"));
}

TEST_F(HttpServiceTests, GetAndSetDirectoryScannerConcurrency) {
  std::string config_name = "dfs.datanode.directory_scanner.concurrency";
  // get config
  ASSERT_TRUE(TestGetConfig(
      config_name, FLAGS_bytestore_hdfs_directory_scanner_concurrency));
  // set config
  uint32_t new_value = 2;
  ASSERT_TRUE(TestSetConfig(config_name, byte::IntegerToString(new_value)));
  // get config after set
  ASSERT_TRUE(TestGetConfig(config_name, new_value));

  // abnormally set config: is not number
  ASSERT_TRUE(TestSetConfig(config_name, "\"is not number\"", -4,
                            "update request error"));
}

TEST_F(HttpServiceTests, GetAndSetDirectoryScannerInterval) {
  std::string config_name = "dfs.datanode.directory_scanner.interval.ms";
  // get config
  ASSERT_TRUE(TestGetConfig(
      config_name, FLAGS_bytestore_hdfs_directory_scanner_interval_time_ms));
  // set config
  uint32_t new_value = 2 * 60 * 60 * 1000;
  ASSERT_TRUE(TestSetConfig(config_name, byte::IntegerToString(new_value)));
  // get config after set
  ASSERT_TRUE(TestGetConfig(config_name, new_value));

  // abnormally set config: is not number
  ASSERT_TRUE(TestSetConfig(config_name, "\"is not number\"", -4,
                            "update request error"));
}

TEST_F(HttpServiceTests, GetAndSetFreezeSize) {
  std::string config_name = "dfs.datanode.freeze.size";
  // get config
  ASSERT_TRUE(
      TestGetConfig(config_name, FLAGS_bytestore_hdfs_tiering_freeze_size));
  // set config
  uint32_t new_value = 256 * 1024 * 1024;
  ASSERT_TRUE(TestSetConfig(config_name, byte::IntegerToString(new_value)));
  // get config after set
  ASSERT_TRUE(TestGetConfig(config_name, new_value));

  // abnormally set config: is not number
  ASSERT_TRUE(TestSetConfig(config_name, "\"is not number\"", -4,
                            "update request error"));
}

TEST_F(HttpServiceTests, GetAndSetAccessTokenEnable) {
  std::string config_name = "dfs.block.access.token.enable";
  // get config
  ASSERT_TRUE(TestGetConfig(config_name, datanode_->IsBlockTokenEnabled()));
  // set config
  bool new_value = true;
  ASSERT_TRUE(TestSetConfig(config_name, new_value ? "true" : "false"));
  // get config after set
  ASSERT_TRUE(TestGetConfig(config_name, new_value));

  // abnormally set config: is not boolean
  ASSERT_TRUE(TestSetConfig(config_name, "\"is not boolean\"", -4,
                            "update request error"));
}

TEST_F(HttpServiceTests, GetAndSetBlockTokenAccessList) {
  std::string config_name = "dfs.block.access.allowlist";
  // get config
  ASSERT_TRUE(
      TestGetConfig(config_name, datanode_->GetBpBlockTokenAccessList()));
  // set config
  std::unordered_set<std::string> new_value;
  new_value.emplace("bp1");
  new_value.emplace("bp2");
  ASSERT_TRUE(TestSetConfig(config_name, "[\"bp1\",\"bp2\"]"));
  // get config after set
  ASSERT_TRUE(TestGetConfig(config_name, new_value));

  // abnormally set config: is not array
  ASSERT_TRUE(TestSetConfig(config_name, "\"is not array\"", -4,
                            "update request error"));
  // abnormally set config: elements is not all string
  ASSERT_TRUE(
      TestSetConfig(config_name, "[\"bp1\", 123]", -4, "update request error"));
}

TEST_F(HttpServiceTests, GetAndSetRemoveVolumeOnlineEnabled) {
  std::string config_name = "dfs.datanode.remove.volume.online.enabled";
  // get config
  ASSERT_TRUE(TestGetConfig(config_name,
                            FLAGS_bytestore_hdfs_remove_volume_online_enabled));
  // set config
  ASSERT_TRUE(TestSetConfig(config_name, "false"));
  // get config after set
  bool new_value = false;
  ASSERT_TRUE(TestGetConfig(config_name, new_value));

  // abnormally set config: is not number
  ASSERT_TRUE(TestSetConfig(config_name, "\"is not boolean\"", -4,
                            "update request error"));
}

TEST_F(HttpServiceTests, GetAndSetMaxReplicateConcurrency) {
  std::string hdd_config_name =
      "dfs.datanode.max.replicate.concurrency.per.hdd";
  // hdd: get config
  ASSERT_TRUE(TestGetConfig(
      hdd_config_name, MFLAGS(bytestore_hdfs_max_replicate_concurrency_per_disk,
                              MediaType::TYPE_SATA_HDD)));
  // hdd: set config
  uint32_t hdd_new_value = 3;
  ASSERT_TRUE(
      TestSetConfig(hdd_config_name, byte::IntegerToString(hdd_new_value)));
  // hdd: get config after set
  ASSERT_TRUE(TestGetConfig(hdd_config_name, hdd_new_value));
  // hdd abnormally set config: is not number
  ASSERT_TRUE(TestSetConfig(hdd_config_name, "\"is not number\"", -4,
                            "update request error"));
  // hdd abnormally set config: dtm is nullptr
  DataTransferManager* dtm = datanode_->GetDataTransferManager();
  datanode_->SetDataTransferManager(nullptr);
  ASSERT_TRUE(TestSetConfig(hdd_config_name,
                            byte::IntegerToString(hdd_new_value), -4,
                            "update request error"));
  datanode_->SetDataTransferManager(dtm);

  std::string ssd_config_name =
      "dfs.datanode.max.replicate.concurrency.per.ssd";
  // ssd: get config
  ASSERT_TRUE(TestGetConfig(
      ssd_config_name, MFLAGS(bytestore_hdfs_max_replicate_concurrency_per_disk,
                              MediaType::TYPE_NVME_SSD)));
  // ssd: set config
  uint32_t ssd_new_value = 20;
  ASSERT_TRUE(
      TestSetConfig(ssd_config_name, byte::IntegerToString(ssd_new_value)));
  // ssd: get config after set
  ASSERT_TRUE(TestGetConfig(ssd_config_name, ssd_new_value));
  // ssd abnormally set config: is not number
  ASSERT_TRUE(TestSetConfig(ssd_config_name, "\"is not number\"", -4,
                            "update request error"));
  // ssd abnormally set config: dtm is nullptr
  dtm = datanode_->GetDataTransferManager();
  datanode_->SetDataTransferManager(nullptr);
  ASSERT_TRUE(TestSetConfig(ssd_config_name,
                            byte::IntegerToString(ssd_new_value), -4,
                            "update request error"));
  datanode_->SetDataTransferManager(dtm);
}

TEST_F(HttpServiceTests, GetAndSetPsm) {
  std::string config_name = "dfs.datanode.psm";
  // get config
  std::string empty = "";
  ASSERT_TRUE(TestGetConfig(config_name, empty));
  // set config
  std::string new_value = "data.inf.hdfs_dancedn_test";
  std::string new_value_tr = "\"data.inf.hdfs_dancedn_test\"";
  ASSERT_TRUE(TestSetConfig(config_name, new_value_tr));
  // get config after set
  ASSERT_TRUE(TestGetConfig(config_name, new_value));

  // abnormally set config: psm not changed
  ASSERT_TRUE(
      TestSetConfig(config_name, new_value_tr, -4, "update request error"));
  // abnormally set config: is not string
  ASSERT_TRUE(TestSetConfig(config_name, "123", -4, "update request error"));
}

TEST_F(HttpServiceTests, GetAndSetMigrationThroughputThreshold) {
  std::string read_config_name =
      "dfs.datanode.migration.read.throughput.threshold.per.disk";
  // get config
  ASSERT_TRUE(TestGetConfig(
      read_config_name,
      FLAGS_bytestore_hdfs_tiering_migration_max_read_throughput_bytes_per_disk));
  // set config
  int64_t new_value = 78643200;
  ASSERT_TRUE(
      TestSetConfig(read_config_name, byte::IntegerToString(new_value)));
  // get config after set
  ASSERT_TRUE(TestGetConfig(read_config_name, new_value));
  // abnormally set config: is not number
  ASSERT_TRUE(TestSetConfig(read_config_name, "\"is not number\"", -4,
                            "update request error"));
  // abnormally set config: less than 0
  new_value = -1;
  ASSERT_TRUE(TestSetConfig(read_config_name, byte::IntegerToString(new_value),
                            -4, "update request error"));

  std::string write_config_name =
      "dfs.datanode.migration.write.throughput.threshold.per.disk";
  // get config
  ASSERT_TRUE(TestGetConfig(
      write_config_name,
      FLAGS_bytestore_hdfs_tiering_migration_max_write_throughput_bytes_per_disk));
  // set config
  new_value = 78643200;
  ASSERT_TRUE(
      TestSetConfig(write_config_name, byte::IntegerToString(new_value)));
  // get config after set
  ASSERT_TRUE(TestGetConfig(write_config_name, new_value));
  // abnormally set config: is not number
  ASSERT_TRUE(TestSetConfig(write_config_name, "\"is not number\"", -4,
                            "update request error"));
  // abnormally set config: less than 0
  new_value = -1;
  ASSERT_TRUE(TestSetConfig(write_config_name, byte::IntegerToString(new_value),
                            -4, "update request error"));
}

TEST_F(HttpServiceTests, GetAndSetUserSpaceReclaimConcurrency) {
  std::string read_config_name = "dfs.datanode.user.space.reclaim.concurrency";
  // get config
  ASSERT_TRUE(TestGetConfig(read_config_name, 0));
  // set config
  EXPECT_CALL(*datanode_, SetUserSpaceReclaimThreadNum(::testing::_))
      .WillRepeatedly(::testing::Return(true));
  int64_t new_value = 2;
  ASSERT_TRUE(
      TestSetConfig(read_config_name, byte::IntegerToString(new_value)));
  // get config after set
  // ASSERT_TRUE(TestGetConfig(read_config_name, new_value));
  // abnormally set config: is not number
  ASSERT_TRUE(TestSetConfig(read_config_name, "\"is not number\"", -4,
                            "update request error"));
  // abnormally set config: less than 0
  new_value = -1;
  ASSERT_TRUE(TestSetConfig(read_config_name, byte::IntegerToString(new_value),
                            -4, "update request error"));
}

TEST_F(HttpServiceTests, SetUserDefaultPrioprity) {
  std::string config_name = "dfs.datanode.user.default.priority";
  // set config
  int new_value = -1;
  ASSERT_TRUE(TestSetConfig(config_name, byte::IntegerToString(new_value)));

  // abnormally set config: is not number
  ASSERT_TRUE(TestSetConfig(config_name, "\"is not number\"", -4,
                            "update request error"));
  // abnormally set config: greater than or equals to the user priority num
  new_value = FLAGS_bytestore_hdfs_user_priority_num;
  ASSERT_TRUE(TestSetConfig(config_name, byte::IntegerToString(new_value), -4,
                            "update request error"));
  new_value = FLAGS_bytestore_hdfs_user_priority_num + 1;
  ASSERT_TRUE(TestSetConfig(config_name, byte::IntegerToString(new_value), -4,
                            "update request error"));
}

TEST_F(HttpServiceTests, SetThroughputThrottle) {
  std::string config_name = "dfs.datanode.throughput.throttle";
  // set config
  std::string new_value =
      "{\"storage_type\": \"hdd\", \"throttle_type\": \"read\", "
      "\"user_priority\": 2, \"bytes_per_second\": 16777216}";
  ASSERT_TRUE(TestSetConfig(config_name, new_value));

  // abnormally set config: is not object
  new_value = "\"is not object\"";
  ASSERT_TRUE(
      TestSetConfig(config_name, new_value, -4, "update request error"));
  // abnormally set config: storage_type is not string
  new_value =
      "{\"storage_type\": 123, \"throttle_type\": \"read\", "
      "\"user_priority\": 2, \"bytes_per_second\": 16777216}";
  ASSERT_TRUE(
      TestSetConfig(config_name, new_value, -4, "update request error"));
  // abnormally set config: user_priority is not number
  new_value =
      "{\"storage_type\": \"hdd\", \"throttle_type\": \"read\", "
      "\"user_priority\": \"2\", \"bytes_per_second\": 16777216}";
  ASSERT_TRUE(
      TestSetConfig(config_name, new_value, -4, "update request error"));
  // abnormally set config: storage_type is not hdd or ssd
  new_value =
      "{\"storage_type\": \"unknown\", \"throttle_type\": \"read\", "
      "\"user_priority\": 2, \"bytes_per_second\": 16777216}";
  ASSERT_TRUE(
      TestSetConfig(config_name, new_value, -4, "update request error"));
  // abnormally set config: throttle_type is not read or write
  new_value =
      "{\"storage_type\": \"hdd\", \"throttle_type\": \"unknown\", "
      "\"user_priority\": 2, \"bytes_per_second\": 16777216}";
  ASSERT_TRUE(
      TestSetConfig(config_name, new_value, -4, "update request error"));
  // abnormally set config: storage is nullptr
  Store* original_store = datanode_->GetStorage();
  datanode_->SetStorage(nullptr);
  new_value =
      "{\"storage_type\": \"hdd\", \"throttle_type\": \"read\", "
      "\"user_priority\": 2, \"bytes_per_second\": 16777216}";
  ASSERT_TRUE(
      TestSetConfig(config_name, new_value, -4, "update request error"));
  datanode_->SetStorage(original_store);
  // abnormally set config: user_priority < 0
  new_value =
      "{\"storage_type\": \"hdd\", \"throttle_type\": \"read\", "
      "\"user_priority\": -1, \"bytes_per_second\": 16777216}";
  ASSERT_TRUE(
      TestSetConfig(config_name, new_value, -4, "update request error"));
  // abnormally set config: bytes_per_second < 0
  new_value =
      "{\"storage_type\": \"hdd\", \"throttle_type\": \"read\", "
      "\"user_priority\": 2, \"bytes_per_second\": -1}";
  ASSERT_TRUE(
      TestSetConfig(config_name, new_value, -4, "update request error"));
  // abnormally set config: bytes_per_second is too small
  new_value =
      "{\"storage_type\": \"hdd\", \"throttle_type\": \"read\", "
      "\"user_priority\": 2, \"bytes_per_second\": 1}";
  ASSERT_TRUE(
      TestSetConfig(config_name, new_value, -4, "update request error"));
}

TEST_F(HttpServiceTests, SetByterpcThroughputThrottle) {
  std::string config_name = "dfs.datanode.byterpc.throughput.throttle";
  // set config
  std::string new_value =
      "{\"throttle_type\": \"read\", \"bytes_per_second\": 16777216}";
  ASSERT_TRUE(TestSetConfig(config_name, new_value));

  // abnormally set config: is not object
  new_value = "\"is not object\"";
  ASSERT_TRUE(
      TestSetConfig(config_name, new_value, -4, "update request error"));
  // abnormally set config: throttle_type is not string
  new_value = "{\"throttle_type\": \"123\", \"bytes_per_second\": 16777216}";
  // abnormally set config: throttle_type is not read or write
  new_value =
      "{\"throttle_type\": \"unknown\", \"bytes_per_second\": 16777216}";
  ASSERT_TRUE(
      TestSetConfig(config_name, new_value, -4, "update request error"));
  // abnormally set config: storage is nullptr
  Store* original_store = datanode_->GetStorage();
  datanode_->SetStorage(nullptr);
  new_value = "{\"throttle_type\": \"read\", \"bytes_per_second\": 16777216}";
  ASSERT_TRUE(
      TestSetConfig(config_name, new_value, -4, "update request error"));
  datanode_->SetStorage(original_store);
}

TEST_F(HttpServiceTests, SetPriorityXceiver) {
  std::string config_name = "dfs.datanode.priority.xceiver.throttle";
  // set config
  std::string new_value = "{\"user_priority\": 2, \"value\": 200}";
  ASSERT_TRUE(TestSetConfig(config_name, new_value));

  // abnormally set config: is not object
  new_value = "\"is not object\"";
  ASSERT_TRUE(
      TestSetConfig(config_name, new_value, -4, "update request error"));
  // abnormally set config: user_priority is not number
  new_value = "{\"user_priority\": \"2\", \"value\": 200}";
  ASSERT_TRUE(
      TestSetConfig(config_name, new_value, -4, "update request error"));
  // abnormally set config: user_priority < 0
  new_value = "{\"user_priority\": -1, \"value\": 200}";
  ASSERT_TRUE(
      TestSetConfig(config_name, new_value, -4, "update request error"));
  // abnormally set config: value < 0
  new_value = "{\"user_priority\": \"2\", \"value\": -1}";
  ASSERT_TRUE(
      TestSetConfig(config_name, new_value, -4, "update request error"));
}

TEST_F(HttpServiceTests, SetThrottleEnable) {
  // set throughput throttle enable
  std::string config_name = "dfs.datanode.throughput.throttle.enable";
  ASSERT_TRUE(TestSetConfig(config_name, "true"));
  // abnormally set config: is not boolean
  ASSERT_TRUE(TestSetConfig(config_name, "\"is not boolean\"", -4,
                            "update request error"));

  // set priority xceiver throttle enable
  config_name = "dfs.datanode.priority.xceiver.throttle.enable";
  ASSERT_TRUE(TestSetConfig(config_name, "true"));
  // abnormally set config: is not boolean
  ASSERT_TRUE(TestSetConfig(config_name, "\"is not boolean\"", -4,
                            "update request error"));
}

TEST_F(HttpServiceTests, SetUnknownConfig) {
  std::string config_name = "unknown.config";
  // set config
  ASSERT_TRUE(TestSetConfig(config_name, "\"unknown_value\"", -4,
                            "update request error"));
}

TEST_F(HttpServiceTests, BlocksHandle) {
  EXPECT_CALL(*datanode_, TriggerBlockReport(::testing::_))
      .WillOnce(::testing::Return(exceptions::Exception()))
      .WillOnce(::testing::Return(
          exceptions::Exception(exceptions::E::kIOException, "test error")));
  EXPECT_CALL(*datanode_, TriggerBlockReport(::testing::_, ::testing::_))
      .WillOnce(::testing::Return(exceptions::Exception()))
      .WillOnce(::testing::Return(
          exceptions::Exception(exceptions::E::kIOException, "test error")));

  // EXPECT_CALL TriggerBlockReport successfully first
  std::string attach =
      "{\"op\": \"report\", \"nameservices\": [\"ALL\"], \"incremental\": "
      "false}";
  std::string resp_set;
  ASSERT_TRUE(HttpPost("/api/v1/blocks", attach, &resp_set));
  ASSERT_TRUE(CheckCodeAndMsg(resp_set));

  // EXPECT_CALL TriggerBlockReport failed second
  ASSERT_TRUE(HttpPost("/api/v1/blocks", attach, &resp_set));
  ASSERT_TRUE(CheckCodeAndMsg(resp_set, -2, "test error"));

  // EXPECT_CALL TriggerBlockReport successfully first
  attach =
      "{\"op\": \"report\", \"nameservices\": [\"testbackend\"], "
      "\"incremental\": false}";
  ASSERT_TRUE(HttpPost("/api/v1/blocks", attach, &resp_set));
  ASSERT_TRUE(CheckCodeAndMsg(resp_set));
  // EXPECT_CALL TriggerBlockReport failed second
  ASSERT_TRUE(HttpPost("/api/v1/blocks", attach, &resp_set));
  ASSERT_TRUE(CheckCodeAndMsg(resp_set, -2, "test error"));

  // abnormally other op
  std::string attach_ab =
      "{\"op\": \"other\", \"nameservices\": [\"ALL\"], \"incremental\": "
      "false}";
  std::string resp_set_ab;
  ASSERT_TRUE(HttpPost("/api/v1/blocks", attach_ab, &resp_set_ab));
  ASSERT_TRUE(
      CheckCodeAndMsg(resp_set_ab, -3, "block operation not supported"));

  // abnormally has no op
  attach_ab = "{\"nameservices\": [\"ALL\"], \"incremental\": false}";
  ASSERT_TRUE(HttpPost("/api/v1/blocks", attach_ab, &resp_set_ab));
  ASSERT_TRUE(CheckCodeAndMsg(resp_set_ab, -4, "check your request format"));

  // abnormally has no nameservices and incremental
  attach_ab = "{\"op\": \"report\"}";
  ASSERT_TRUE(HttpPost("/api/v1/blocks", attach_ab, &resp_set_ab));
  ASSERT_TRUE(CheckCodeAndMsg(resp_set_ab, -4, "check your request format"));
}

/*
{
    "code": 0,
    "data": {
        "bvc_version": "1.0.0.452",
        "chunkserver_version": "v1.2",
        "service": "chunkserver"
    },
    "msg": "OK"
}
*/
TEST_F(HttpServiceTests, GetVersion) {
  std::string resp_get;
  ASSERT_TRUE(HttpGet("/api/v1/version", &resp_get));
  nlohmann::json data = nlohmann::json::object();
  ASSERT_TRUE(GetAttrJson(resp_get, "data", &data));
  ASSERT_NE(data.find("bvc_version"), data.end());
  std::string bvc_version;
  try {
    bvc_version = data["bvc_version"].get<std::string>();
  } catch (nlohmann::json::exception& e) {
    LOG(ERROR) << "parse version failed:" << e.what()
               << " data[\"bvc_version\"]:" << data["bvc_version"];
    ASSERT_TRUE(false);
  }
  ASSERT_EQ(bvc_version, FLAGS_bytestore_hdfs_bvc_version);
}

TEST_F(HttpServiceTests, GetVolumes) {
  BlockPoolManager* block_pool_manager = new BlockPoolManager(datanode_);
  std::unordered_map<std::string, std::string> nn_name_to_addr;
  std::string ns = "testbackend";
  nn_name_to_addr.emplace(std::make_pair("test", "ip_test"));
  auto bps = std::make_shared<BlockPoolService>(nn_name_to_addr, datanode_, ns,
                                                block_pool_manager);
  bps->SetBlockPoolID("BP-2026362776-*************-1546064706393");
  block_pool_manager->AddBpService(ns, bps);
  datanode_->SetBlockPoolManager(block_pool_manager);

  std::string resp_get;
  ASSERT_TRUE(HttpGet("/api/v1/volumes", &resp_get));
  nlohmann::json data = nlohmann::json::object();
  ASSERT_TRUE(GetAttrJson(resp_get, "data", &data));
  ASSERT_NE(data.find("bpdirs"), data.end());
  std::vector<nlohmann::json> bpdirs;
  std::map<std::string, nlohmann::json> volumes;
  try {
    bpdirs = data["bpdirs"].get<std::vector<nlohmann::json>>();
    volumes = data["volumes"].get<std::map<std::string, nlohmann::json>>();
  } catch (nlohmann::json::exception& e) {
    LOG(ERROR) << "parse volumes failed:" << e.what() << " data:" << data;
    ASSERT_TRUE(false);
  }
  ASSERT_EQ(bpdirs.size(), 1);
  ASSERT_EQ(volumes.size(), 3);
  // issue: GetDiskInfos does not correctly reflect the block we wrote into
  // /disk01
}

TEST_F(HttpServiceTests, NamenodeHandle) {
  // there is no op
  std::string resp_post;
  std::string attach = byte::StringPrint("{\"nameservices\": [\"ALL\"]}");
  ASSERT_TRUE(HttpPost("/api/v1/namenodes", attach, &resp_post));
  ASSERT_TRUE(CheckCodeAndMsg(resp_post, -4, "check your request format"));

  // there is no nameservices
  attach = byte::StringPrint("{\"op\": \"refresh\"}");
  ASSERT_TRUE(HttpPost("/api/v1/namenodes", attach, &resp_post));
  ASSERT_TRUE(CheckCodeAndMsg(resp_post, -4, "check your request format"));

  // DataNodeConfig::Parse failed
  attach =
      byte::StringPrint("{\"op\": \"refresh\", \"nameservices\": [\"ALL\"]}");
  ASSERT_TRUE(HttpPost("/api/v1/namenodes", attach, &resp_post));
  ASSERT_TRUE(CheckCodeAndMsg(resp_post, -1, "parse config file failed"));

  // op is not refresh
  attach =
      byte::StringPrint("{\"op\": \"other\", \"nameservices\": [\"ALL\"]}");
  ASSERT_TRUE(HttpPost("/api/v1/namenodes", attach, &resp_post));
  ASSERT_TRUE(
      CheckCodeAndMsg(resp_post, -3, "namenodes operation not supported"));

  // TODO(yejieqing): DataNodeConfig::Parse success
}

/*
{
    "code": 0,
    "data": {
        "namenodes": {
            "dngraybackend": {
                "dng2": "[fdbd:dc03:1:98::74]:5061",
                "ray2": "[fdbd:dc03:1:155::150]:5061"
            }
        },
        "version": "*******"
    },
    "msg": "OK"
}
*/
TEST_F(HttpServiceTests, GetNameNodes) {
  MockDataNodeConfig* dn_config =
      static_cast<MockDataNodeConfig*>(datanode_->GetDataNodeConfig());
  EXPECT_CALL(*dn_config, GetNamenodeConfigVersion())
      .WillRepeatedly(::testing::Return(std::string("*******")));
  EXPECT_CALL(*dn_config, GetNNServiceRpcAddressesForCluster())
      .WillRepeatedly(::testing::Return(GenNameServiceMap()));

  std::string resp_get;
  ASSERT_TRUE(HttpGet("/api/v1/namenodes", &resp_get));
  nlohmann::json data = nlohmann::json::object();
  ASSERT_TRUE(GetAttrJson(resp_get, "data", &data));
  ASSERT_NE(data.find("version"), data.end());
  ASSERT_NE(data.find("namenodes"), data.end());
  std::string version;
  DataNodeConfig::NameServiceMap namenodes;
  try {
    version = data["version"].get<std::string>();
    namenodes = data["namenodes"].get<DataNodeConfig::NameServiceMap>();
  } catch (nlohmann::json::exception& e) {
    LOG(ERROR) << "parse namenodes failed:" << e.what() << " data:" << data;
    ASSERT_TRUE(false);
  }
  ASSERT_EQ(version, "*******");
  ASSERT_EQ(namenodes.size(), 1);
  auto itr = namenodes.find("dngraybackend");
  ASSERT_NE(itr, namenodes.end());
  ASSERT_EQ(itr->second.size(), 2);
}

TEST_F(HttpServiceTests, GetOpStats) {
  std::string resp_get;
  ASSERT_TRUE(HttpGet("/api/v1/opstats", &resp_get));
  nlohmann::json data = nlohmann::json::object();
  ASSERT_TRUE(GetAttrJson(resp_get, "data", &data));
  ASSERT_NE(data.find("opstats"), data.end());
  std::vector<nlohmann::json> op_stats;
  try {
    op_stats = data["opstats"].get<std::vector<nlohmann::json>>();
  } catch (nlohmann::json::exception& e) {
    LOG(ERROR) << "parse opstats failed:" << e.what()
               << " data[\"opstats\"]:" << data["opstats"];
    ASSERT_TRUE(false);
  }
}

TEST_F(HttpServiceTests, GetXceivers) {
  std::string resp_get;
  ASSERT_TRUE(HttpGet("/api/v1/xceivers", &resp_get));
  nlohmann::json data = nlohmann::json::object();
  ASSERT_TRUE(GetAttrJson(resp_get, "data", &data));
  ASSERT_NE(data.find("xceivers"), data.end());
  std::vector<nlohmann::json> op_stats;
  try {
    op_stats = data["xceivers"].get<std::vector<nlohmann::json>>();
  } catch (nlohmann::json::exception& e) {
    LOG(ERROR) << "parse xceivers failed:" << e.what()
               << " data[\"xceivers\"]:" << data["xceivers"];
    ASSERT_TRUE(false);
  }
}

TEST_F(HttpServiceTests, VolumesHandle) {
  EXPECT_CALL(*datanode_, IsBlockPoolAllReady())
      .WillRepeatedly(::testing::Return(true));
  EXPECT_CALL(*datanode_, TriggerBlockReport(::testing::_))
      .WillRepeatedly(::testing::Return(exceptions::Exception()));
  EXPECT_CALL(*http_service_impl_,
              DoCloseVolumes(::testing::_, ::testing::_, ::testing::_))
      .WillRepeatedly(::testing::Return(exceptions::Exception()));
  EXPECT_CALL(*http_service_impl_, DoAddVolumes(::testing::_, ::testing::_))
      .WillRepeatedly(::testing::Return(exceptions::Exception()));

  std::string resp_post;
  std::string attach = "{\"op\": \"close\"}";
  // first close volume
  ASSERT_TRUE(HttpPost("/api/v1/volumes", attach, &resp_post));
  ASSERT_TRUE(CheckCodeAndMsg(resp_post));
  // second close volume
  ASSERT_TRUE(HttpPost("/api/v1/volumes", attach, &resp_post));
  ASSERT_TRUE(CheckCodeAndMsg(resp_post));
  // first remove volume
  attach = "{\"op\": \"remove\"}";
  ASSERT_TRUE(HttpPost("/api/v1/volumes", attach, &resp_post));
  ASSERT_TRUE(CheckCodeAndMsg(resp_post));
  // first add volume
  attach = "{\"op\": \"add\"}";
  ASSERT_TRUE(HttpPost("/api/v1/volumes", attach, &resp_post));
  ASSERT_TRUE(CheckCodeAndMsg(resp_post));

  std::list<std::string> list;
  http_service_impl_->ForEachHistory("close", &list);
  ASSERT_EQ(list.size(), 2);
  http_service_impl_->ForEachHistory("remove", &list);
  ASSERT_EQ(list.size(), 1);
  http_service_impl_->ForEachHistory("add", &list);
  ASSERT_EQ(list.size(), 1);

  uint32_t max_number =
      FLAGS_bytestore_hdfs_volume_operation_history_records_max_number;
  // add volume for max_number times
  for (uint32_t i = 0; i < max_number; ++i) {
    ASSERT_TRUE(HttpPost("/api/v1/volumes", attach, &resp_post));
    ASSERT_TRUE(CheckCodeAndMsg(resp_post));
  }
  http_service_impl_->ForEachHistory("add", &list);
  ASSERT_EQ(list.size(), max_number);
}

TEST_F(HttpServiceTests, DoCloseVolumes) {
  BlockPoolManager* block_pool_manager = new BlockPoolManager(datanode_);
  std::unordered_map<std::string, std::string> nn_name_to_addr;
  std::string ns = "testbackend";
  nn_name_to_addr.emplace(std::make_pair("test", "ip_test"));
  auto bps = std::make_shared<BlockPoolService>(nn_name_to_addr, datanode_, ns,
                                                block_pool_manager);
  bps->SetBlockPoolID("BP-2026362776-*************-1546064706393");
  block_pool_manager->AddBpService(ns, bps);
  datanode_->SetBlockPoolManager(block_pool_manager);

  nlohmann::json request = nlohmann::json::parse(
      "{\"op\": \"remove\", \"volumes\": "
      "[\"./HttpServiceTestDir/disk01/dancedn\"]}");
  std::vector<std::string> done;
  auto e = http_service_impl_->HdfsHttpServiceImpl::DoCloseVolumes(request,
                                                                   true, &done);
  ASSERT_FALSE(e.OK());
  auto msg = e.GetMessage();
  ASSERT_FALSE(msg.empty());
  ASSERT_TRUE(msg.find("parse volumes or storageUuids failed") !=
              std::string::npos);
  ASSERT_EQ(done.size(), 0);

  request = nlohmann::json::parse(
      "{\"op\": \"remove\", \"volumes\": [], \"storageUuids\": []}");
  e = http_service_impl_->HdfsHttpServiceImpl::DoCloseVolumes(request, true,
                                                              &done);
  ASSERT_FALSE(e.OK());
  msg = e.GetMessage();
  ASSERT_FALSE(msg.empty());
  ASSERT_TRUE(msg.find("request contains no volumes or storageUuids") !=
              std::string::npos);
  ASSERT_EQ(done.size(), 0);

  request = nlohmann::json::parse(
      "{\"op\": \"remove\", \"volumes\": "
      "[\"./HttpServiceTestDir/disk01/dancedn\", "
      "\"./HttpServiceTestDir/disk02/dancedn\"], \"storageUuids\": "
      "[\"test_storage_id_1\"]}");
  e = http_service_impl_->HdfsHttpServiceImpl::DoCloseVolumes(request, true,
                                                              &done);
  ASSERT_FALSE(e.OK());
  msg = e.GetMessage();
  ASSERT_FALSE(msg.empty());
  ASSERT_TRUE(msg.find("the length of volumes does not match storageUuids") !=
              std::string::npos);
  ASSERT_EQ(done.size(), 0);

  request = nlohmann::json::parse(
      "{\"op\": \"remove\", \"volumes\": "
      "[\"./HttpServiceTestDir/disk11/dancedn\", "
      "\"./HttpServiceTestDir/disk12/dancedn\"], \"storageUuids\": "
      "[\"test_storage_id_1\", "
      "\"test_storage_id_2\"]}");
  bytestore_hdfs_failed_volumes_tolerated = 1;
  e = http_service_impl_->HdfsHttpServiceImpl::DoCloseVolumes(request, true,
                                                              &done);
  ASSERT_FALSE(e.OK());
  msg = e.GetMessage();
  ASSERT_FALSE(msg.empty());
  ASSERT_TRUE(msg.find("too many bad volumes") != std::string::npos);
  ASSERT_EQ(done.size(), 0);

  bytestore_hdfs_failed_volumes_tolerated = 3;
  e = http_service_impl_->HdfsHttpServiceImpl::DoCloseVolumes(request, true,
                                                              &done);
  ASSERT_FALSE(e.OK());
  msg = e.GetMessage();
  ASSERT_FALSE(msg.empty());
  ASSERT_TRUE(msg.find("volume not found") != std::string::npos);
  ASSERT_EQ(done.size(), 0);

  request = nlohmann::json::parse(
      "{\"op\": \"remove\", \"volumes\": "
      "[\"./HttpServiceTestDir/disk01/dancedn\", "
      "\"./HttpServiceTestDir/disk02/dancedn\"], \"storageUuids\": "
      "[\"test_storage_id_11\", "
      "\"test_storage_id_12\"]}");
  e = http_service_impl_->HdfsHttpServiceImpl::DoCloseVolumes(request, true,
                                                              &done);
  ASSERT_FALSE(e.OK());
  msg = e.GetMessage();
  ASSERT_FALSE(msg.empty());
  ASSERT_TRUE(msg.find("storageUuid does not match") != std::string::npos);
  ASSERT_EQ(done.size(), 0);

  request = nlohmann::json::parse(
      "{\"op\": \"remove\", \"volumes\": "
      "[\"./HttpServiceTestDir/disk01/dancedn\", "
      "\"./HttpServiceTestDir/disk02/dancedn\"], \"storageUuids\": "
      "[\"test_storage_id_1\", "
      "\"test_storage_id_2\"]}");
  std::string error_msg = "test error message";
  EXPECT_CALL(*store_, CloseVolume(::testing::_))
      .Times(1)
      .WillOnce(::testing::Return(
          exceptions::Exception(exceptions::kIOException, error_msg)));
  e = http_service_impl_->HdfsHttpServiceImpl::DoCloseVolumes(request, true,
                                                              &done);
  ASSERT_FALSE(e.OK());
  msg = e.GetMessage();
  ASSERT_FALSE(msg.empty());
  ASSERT_TRUE(msg.find(error_msg) != std::string::npos);
  ASSERT_EQ(done.size(), 0);

  EXPECT_CALL(*store_, CloseVolume(::testing::_))
      .WillRepeatedly(::testing::Return(exceptions::Exception()));
  EXPECT_CALL(*store_, RemoveVolume(::testing::_, ::testing::_))
      .Times(1)
      .WillOnce(::testing::Return(
          exceptions::Exception(exceptions::kIOException, error_msg)));
  e = http_service_impl_->HdfsHttpServiceImpl::DoCloseVolumes(request, true,
                                                              &done);
  ASSERT_FALSE(e.OK());
  msg = e.GetMessage();
  ASSERT_FALSE(msg.empty());
  ASSERT_TRUE(msg.find(error_msg) != std::string::npos);
  ASSERT_EQ(done.size(), 0);

  EXPECT_CALL(*store_, RemoveVolume(::testing::_, ::testing::_))
      .WillRepeatedly(::testing::Return(exceptions::Exception()));

  EXPECT_CALL(*datanode_, AppendToBlackList(::testing::_))
      .Times(1)
      .WillOnce(::testing::Return(
          exceptions::Exception(exceptions::kIOException, error_msg)));
  e = http_service_impl_->HdfsHttpServiceImpl::DoCloseVolumes(request, true,
                                                              &done);
  ASSERT_FALSE(e.OK());
  msg = e.GetMessage();
  ASSERT_FALSE(msg.empty());
  ASSERT_TRUE(msg.find(error_msg) != std::string::npos);
  ASSERT_EQ(done.size(), 0);

  // remove volume normally
  EXPECT_CALL(*datanode_, AppendToBlackList(::testing::_))
      .WillRepeatedly(::testing::Return(exceptions::Exception()));
  e = http_service_impl_->HdfsHttpServiceImpl::DoCloseVolumes(request, true,
                                                              &done);
  ASSERT_TRUE(e.OK());
  msg = e.GetMessage();
  ASSERT_FALSE(msg.empty());
  ASSERT_TRUE(msg.find("Has removed 2 volumes online") != std::string::npos);
  ASSERT_EQ(done.size(), 2);
  done.clear();

  // close volume normally
  request = nlohmann::json::parse(
      "{\"op\": \"remove\", \"volumes\": "
      "[\"./HttpServiceTestDir/disk03/dancedn\"], "
      "\"storageUuids\": [\"test_storage_id_3\"]}");
  e = http_service_impl_->HdfsHttpServiceImpl::DoCloseVolumes(request, false,
                                                              &done);
  ASSERT_TRUE(e.OK());
  msg = e.GetMessage();
  ASSERT_FALSE(msg.empty());
  ASSERT_TRUE(msg.find("Has closed 1 volumes online") != std::string::npos);
  ASSERT_EQ(done.size(), 1);
  done.clear();
}

TEST_F(HttpServiceTests, DoAddVolumes) {
  BlockPoolManager* block_pool_manager = new BlockPoolManager(datanode_);
  std::unordered_map<std::string, std::string> nn_name_to_addr;
  std::string ns = "testbackend";
  nn_name_to_addr.emplace(std::make_pair("test", "ip_test"));
  auto bps = std::make_shared<BlockPoolService>(nn_name_to_addr, datanode_, ns,
                                                block_pool_manager);
  bps->SetBlockPoolID("BP-2026362776-*************-1546064706393");
  block_pool_manager->AddBpService(ns, bps);
  datanode_->SetBlockPoolManager(block_pool_manager);

  nlohmann::json request =
      nlohmann::json::parse("{\"op\": \"add\", \"volumes\": []}");
  std::vector<std::string> done;
  auto e =
      http_service_impl_->HdfsHttpServiceImpl::DoAddVolumes(request, &done);
  ASSERT_FALSE(e.OK());
  auto msg = e.GetMessage();
  ASSERT_FALSE(msg.empty());
  ASSERT_TRUE(msg.find("parse volumes or storageTypes failed") !=
              std::string::npos);
  ASSERT_EQ(done.size(), 0);

  request = nlohmann::json::parse(
      "{\"op\": \"add\", \"volumes\": [], \"storageTypes\": []}");
  e = http_service_impl_->HdfsHttpServiceImpl::DoAddVolumes(request, &done);
  ASSERT_FALSE(e.OK());
  msg = e.GetMessage();
  ASSERT_FALSE(msg.empty());
  ASSERT_TRUE(msg.find("request contains no volumes or storageTypes") !=
              std::string::npos);
  ASSERT_EQ(done.size(), 0);

  request = nlohmann::json::parse(
      "{\"op\": \"add\", \"volumes\": "
      "[\"./HttpServiceTestDir/disk01/dancedn\"], "
      "\"storageTypes\": [\"hdd\"]}");
  e = http_service_impl_->HdfsHttpServiceImpl::DoAddVolumes(request, &done);
  ASSERT_FALSE(e.OK());
  msg = e.GetMessage();
  ASSERT_FALSE(msg.empty());
  ASSERT_TRUE(msg.find("volume already exists") != std::string::npos);
  ASSERT_EQ(done.size(), 0);

  request = nlohmann::json::parse(
      "{\"op\": \"add\", \"volumes\": "
      "[\"./HttpServiceTestDir/disk01/dancedn\", "
      "\"./HttpServiceTestDir/disk02/dancedn\"], \"storageTypes\":[\"hdd\"]}");
  e = http_service_impl_->HdfsHttpServiceImpl::DoAddVolumes(request, &done);
  ASSERT_FALSE(e.OK());
  msg = e.GetMessage();
  ASSERT_FALSE(msg.empty());
  ASSERT_TRUE(msg.find("the length of volumes does not match storageTypes") !=
              std::string::npos);
  ASSERT_EQ(done.size(), 0);

  request = nlohmann::json::parse(
      "{\"op\": \"add\", \"volumes\": "
      "[\"./HttpServiceTestDir/disk11/dancedn\", "
      "\"./HttpServiceTestDir/disk12/dancedn\"], \"storageTypes\": [\"hdd\", "
      "\"ssd\"]}");
  std::string error_msg = "test error message";
  EXPECT_CALL(*store_, FormatAndAddVolume(::testing::_, ::testing::_))
      .Times(1)
      .WillOnce(::testing::Return(
          exceptions::Exception(exceptions::kIOException, error_msg)));
  e = http_service_impl_->HdfsHttpServiceImpl::DoAddVolumes(request, &done);
  ASSERT_FALSE(e.OK());
  msg = e.GetMessage();
  ASSERT_FALSE(msg.empty());
  ASSERT_TRUE(msg.find(error_msg) != std::string::npos);
  ASSERT_EQ(done.size(), 0);

  EXPECT_CALL(*store_, FormatAndAddVolume(::testing::_, ::testing::_))
      .WillRepeatedly(::testing::Return(exceptions::Exception()));
  EXPECT_CALL(*datanode_, EraseInBlackList(::testing::_))
      .Times(1)
      .WillOnce(::testing::Return(
          exceptions::Exception(exceptions::kIOException, error_msg)));
  e = http_service_impl_->HdfsHttpServiceImpl::DoAddVolumes(request, &done);
  ASSERT_FALSE(e.OK());
  msg = e.GetMessage();
  ASSERT_FALSE(msg.empty());
  ASSERT_TRUE(msg.find(error_msg) != std::string::npos);
  ASSERT_EQ(done.size(), 0);

  // add volume normally
  EXPECT_CALL(*datanode_, EraseInBlackList(::testing::_))
      .WillRepeatedly(::testing::Return(exceptions::Exception()));
  e = http_service_impl_->HdfsHttpServiceImpl::DoAddVolumes(request, &done);
  ASSERT_TRUE(e.OK());
  msg = e.GetMessage();
  ASSERT_FALSE(msg.empty());
  ASSERT_TRUE(msg.find("Has added 2 volumes online") != std::string::npos);
  ASSERT_EQ(done.size(), 2);
  done.clear();
}

TEST_F(HttpServiceTests, DoSetReadonlyVolumes) {
  BlockPoolManager* block_pool_manager = new BlockPoolManager(datanode_);
  std::unordered_map<std::string, std::string> nn_name_to_addr;
  std::string ns = "testbackend";
  nn_name_to_addr.emplace(std::make_pair("test", "ip_test"));
  auto bps = std::make_shared<BlockPoolService>(nn_name_to_addr, datanode_, ns,
                                                block_pool_manager);
  bps->SetBlockPoolID("BP-2026362776-*************-1546064706393");
  block_pool_manager->AddBpService(ns, bps);
  datanode_->SetBlockPoolManager(block_pool_manager);

  nlohmann::json request = nlohmann::json::parse(
      "{\"op\": \"set_read_only\", \"volumes\": "
      "\"./HttpServiceTestDir/disk01/dancedn\"}");
  std::vector<std::string> done;
  auto e = http_service_impl_->HdfsHttpServiceImpl::DoSetReadonlyVolumes(
      request, &done);
  ASSERT_FALSE(e.OK());
  auto msg = e.GetMessage();
  ASSERT_FALSE(msg.empty());
  ASSERT_TRUE(msg.find("parse volumes failed") != std::string::npos);
  ASSERT_EQ(done.size(), 0);

  request = nlohmann::json::parse("{\"op\": \"remove\", \"volumes\": []}");
  e = http_service_impl_->HdfsHttpServiceImpl::DoSetReadonlyVolumes(request,
                                                                    &done);
  ASSERT_FALSE(e.OK());
  msg = e.GetMessage();
  ASSERT_FALSE(msg.empty());
  ASSERT_TRUE(msg.find("request contains no volumes") != std::string::npos);
  ASSERT_EQ(done.size(), 0);

  request = nlohmann::json::parse(
      "{\"op\": \"remove\", \"volumes\": "
      "[\"./HttpServiceTestDir/disk11/dancedn\", "
      "\"./HttpServiceTestDir/disk12/dancedn\"]}");
  e = http_service_impl_->HdfsHttpServiceImpl::DoSetReadonlyVolumes(request,
                                                                    &done);
  ASSERT_FALSE(e.OK());
  msg = e.GetMessage();
  ASSERT_FALSE(msg.empty());
  ASSERT_TRUE(msg.find("volume not found") != std::string::npos);
  ASSERT_EQ(done.size(), 0);

  request = nlohmann::json::parse(
      "{\"op\": \"remove\", \"volumes\": "
      "[\"./HttpServiceTestDir/disk01/dancedn\", "
      "\"./HttpServiceTestDir/disk02/dancedn\"]}");
  std::string error_msg = "test error message";
  EXPECT_CALL(*store_, SetVolumeReadonly(::testing::_))
      .Times(1)
      .WillOnce(::testing::Return(
          exceptions::Exception(exceptions::kIOException, error_msg)));
  e = http_service_impl_->HdfsHttpServiceImpl::DoSetReadonlyVolumes(request,
                                                                    &done);
  ASSERT_FALSE(e.OK());
  msg = e.GetMessage();
  ASSERT_FALSE(msg.empty());
  ASSERT_TRUE(msg.find(error_msg) != std::string::npos);
  ASSERT_EQ(done.size(), 0);

  // set volume readonly normally
  EXPECT_CALL(*store_, SetVolumeReadonly(::testing::_))
      .WillRepeatedly(::testing::Return(exceptions::Exception()));
  e = http_service_impl_->HdfsHttpServiceImpl::DoSetReadonlyVolumes(request,
                                                                    &done);
  ASSERT_TRUE(e.OK());
  msg = e.GetMessage();
  ASSERT_FALSE(msg.empty());
  ASSERT_TRUE(msg.find("Has set readonly 2 volumes online") !=
              std::string::npos);
  ASSERT_EQ(done.size(), 2);
  done.clear();
}

TEST_F(HttpServiceTests, ModifyUser) {
  FLAGS_bytestore_hdfs_user_keywords_p0 = "[\"bmq\"]";
  ASSERT_TRUE(datanode_->InitUserPriorities());
  std::string config_name = "dfs.datanode.user.priorities";
  // get config
  nlohmann::json user_priority_json = datanode_->GetUserPrioritiesJson();
  ASSERT_TRUE(TestGetConfig(config_name, user_priority_json));

  // expect that batch user is not in config
  std::string batch_user = "batch";
  ASSERT_EQ(user_priority_json.dump().find(batch_user), std::string::npos);
  ASSERT_EQ(datanode_->GetUserPriority(batch_user),
            FLAGS_bytestore_hdfs_user_default_priority);

  // abnormally set config: is not object
  std::string new_value = "\"is not object\"";
  ASSERT_TRUE(
      TestSetConfig(config_name, new_value, -4, "update request error"));
  // abnormally set config: request format is wrong
  new_value = "{\"op\": \"add\"}";
  ASSERT_TRUE(
      TestSetConfig(config_name, new_value, -4, "update request error"));
  // abnormally set config: unknown operation
  new_value = "{\"op\": \"unknown\", \"priority\": 1, \"user\": \"batch\"}";
  ASSERT_TRUE(
      TestSetConfig(config_name, new_value, -4, "update request error"));
  // abnormally add user: invalid priority
  new_value = "{\"op\": \"add\", \"priority\": -1, \"user\": \"batch\"}";
  ASSERT_TRUE(
      TestSetConfig(config_name, new_value, -4, "update request error"));
  // abnormally add user: user already exists
  new_value = "{\"op\": \"add\", \"priority\": 1, \"user\": \"bmq\"}";
  ASSERT_TRUE(
      TestSetConfig(config_name, new_value, -4, "update request error"));
  // normally add user
  new_value = "{\"op\": \"add\", \"priority\": 1, \"user\": \"batch\"}";
  ASSERT_TRUE(TestSetConfig(config_name, new_value));
  ASSERT_EQ(datanode_->GetUserPriority(batch_user), 1);

  // abnormally remove user: invalid priority
  new_value = "{\"op\": \"remove\", \"priority\": -1, \"user\": \"batch\"}";
  ASSERT_TRUE(
      TestSetConfig(config_name, new_value, -4, "update request error"));
  // abnormally remove user: user does not exist
  new_value = "{\"op\": \"remove\", \"priority\": 1, \"user\": \"not_exist\"}";
  ASSERT_TRUE(
      TestSetConfig(config_name, new_value, -4, "update request error"));
  // abnormally remove user: priority is not expected
  new_value = "{\"op\": \"remove\", \"priority\": 0, \"user\": \"batch\"}";
  ASSERT_TRUE(
      TestSetConfig(config_name, new_value, -4, "update request error"));
  // normally remove user
  new_value = "{\"op\": \"remove\", \"priority\": 1, \"user\": \"batch\"}";
  ASSERT_TRUE(TestSetConfig(config_name, new_value));
  ASSERT_EQ(datanode_->GetUserPriority(batch_user),
            FLAGS_bytestore_hdfs_user_default_priority);
}

}  // namespace hdfs
}  // namespace chunkserver
}  // namespace bytestore
