// Copyright (c) 2019-present, ByteDance Inc. All rights reserved.

#pragma once

#include <string>

#include "hdfs/message/rpc_request_message.h"

namespace hadoop {
namespace hdfs {

class TriggerBlockReportRequestProto;
class TriggerBlockReportResponseProto;

}  // namespace hdfs
}  // namespace hadoop

namespace bytestore {
namespace chunkserver {
namespace hdfs {
namespace message {

class TriggerBlockReportMessage : public RpcRequestMessage {
 public:
  ~TriggerBlockReportMessage();

  bool DecodeBody(hdfs::io::IOChunk* chunk, int len) override;
  int Encode(hdfs::io::IOChunk* chunk) override;

  hadoop::hdfs::TriggerBlockReportRequestProto* Body() const {
    return r_;
  }

 public:
  // following method only for test
  void SetRequestBody(hadoop::hdfs::TriggerBlockReportRequestProto* r) {
    r_ = r;
  }

 public:
  static RpcRequestMessage* New();

 private:
  TriggerBlockReportMessage();

 private:
  hadoop::hdfs::TriggerBlockReportRequestProto* r_;
};

}  // namespace message
}  // namespace hdfs
}  // namespace chunkserver
}  // namespace bytestore
