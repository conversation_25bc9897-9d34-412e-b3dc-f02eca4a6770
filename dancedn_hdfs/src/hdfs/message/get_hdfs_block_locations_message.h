// Copyright (c) 2019-present, ByteDance Inc. All rights reserved.

#pragma once

#include <string>

#include "hdfs/message/rpc_request_message.h"

namespace hadoop {
namespace hdfs {

class GetHdfsBlockLocationsRequestProto;
class GetHdfsBlockLocationsResponseProto;

}  // namespace hdfs
}  // namespace hadoop

namespace bytestore {
namespace chunkserver {
namespace hdfs {
namespace message {

class GetHdfsBlockLocationsMessage : public RpcRequestMessage {
 public:
  ~GetHdfsBlockLocationsMessage();

  bool DecodeBody(hdfs::io::IOChunk* chunk, int len) override;
  int Encode(hdfs::io::IOChunk* chunk) override;

  hadoop::hdfs::GetHdfsBlockLocationsRequestProto* Body() const {
    return r_;
  }

 public:
  // following method only for test
  void SetRequestBody(hadoop::hdfs::GetHdfsBlockLocationsRequestProto* r) {
    r_ = r;
  }

 public:
  static RpcRequestMessage* New();

 private:
  GetHdfsBlockLocationsMessage();

 private:
  hadoop::hdfs::GetHdfsBlockLocationsRequestProto* r_;
};

}  // namespace message
}  // namespace hdfs
}  // namespace chunkserver
}  // namespace bytestore
