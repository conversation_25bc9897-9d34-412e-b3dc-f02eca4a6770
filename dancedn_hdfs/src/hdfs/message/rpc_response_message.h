// Copyright (c) 2019-present, ByteDance Inc. All rights reserved.

#pragma once

#include <functional>
#include <string>

#include "hdfs/message/base_rpc_message.h"

namespace google {
namespace protobuf {
class Message;
}
}  // namespace google

namespace hadoop {
namespace common {
class RpcResponseHeaderProto;
}
}  // namespace hadoop

namespace bytestore {
namespace chunkserver {
namespace hdfs {

namespace io {
class IOChunk;
class Connection;
class Context;
}  // namespace io

namespace message {

class RpcResponseMessage : public BaseRpcMessage {
 public:
  RpcResponseMessage();
  RpcResponseMessage(hadoop::common::RpcResponseHeaderProto* header,
                     google::protobuf::Message* body);
  ~RpcResponseMessage();

  hadoop::common::RpcResponseHeaderProto* ReleaseRpcHeader();
  google::protobuf::Message* ReleaseBody();

  const hadoop::common::RpcResponseHeaderProto* RpcHeader() const;
  const google::protobuf::Message* Body() const;

  bool Decode(hdfs::io::Connection* conn, hdfs::io::IOChunk* chunk, int len);

  bool DecodeSync(hdfs::io::Context* ctx, hdfs::io::IOChunk* chunk, int len);

  static int DecodeResponseHeader(
      hdfs::io::IOChunk* chunk, int len,
      hadoop::common::RpcResponseHeaderProto** proto);

  int Encode(hdfs::io::IOChunk* chunk);

 private:
  static google::protobuf::Message* DecodeProto(uint32_t method,
                                                const uint8_t* data, int len);
  void SetHeaderAndBody(hadoop::common::RpcResponseHeaderProto* header,
                        google::protobuf::Message* body);

 private:
  hadoop::common::RpcResponseHeaderProto* header_;
  google::protobuf::Message* body_;
};

}  // namespace message
}  // namespace hdfs
}  // namespace chunkserver
}  // namespace bytestore
