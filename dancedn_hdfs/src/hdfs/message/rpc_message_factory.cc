// Copyright (c) 2019-present, ByteDance Inc. All rights reserved.

#include "hdfs/message/rpc_message_factory.h"

#include "byte/include/byte_log.h"
#include "hdfs/client_datanode_service_impl.h"
#include "hdfs/message/rpc_request_message.h"
#include "hdfs/message/rpc_response_message.h"
#include "hdfs/proto/ProtobufRpcEngine.pb.h"
#include "hdfs/proto/RpcHeader.pb.h"

namespace bytestore {
namespace chunkserver {
namespace hdfs {
namespace message {

RpcRequestMessageFactory::RpcRequestMessageFactory(
    ClientDatanodeServiceImpl* svc)
    : svc_(svc) {}

RpcRequestMessageFactory::~RpcRequestMessageFactory() {
  delete svc_;
}

RpcRequestMessage* RpcRequestMessageFactory::Decode(hdfs::io::IOChunk* chunk,
                                                    int len) {
  hadoop::common::RpcRequestHeaderProto* rpc_header = nullptr;
  int remain_length =
      RpcRequestMessage::DecodeRpcHeader(chunk, len, &rpc_header);
  if (remain_length <= 0) return nullptr;

  hadoop::common::RequestHeaderProto* req_header = nullptr;
  remain_length =
      RpcRequestMessage::DecodeRequestHeader(chunk, remain_length, &req_header);
  if (remain_length <= 0) {
    delete rpc_header;
    return nullptr;
  }

  auto r = svc_->NewRequest(req_header->methodname());
  if (r == nullptr) {
    LOG(WARNING) << "decode got unknown method " << req_header->methodname();

    delete rpc_header;
    delete req_header;
    return nullptr;
  }

  r->RpcHeader(rpc_header);
  r->RequestHeader(req_header);

  if (!r->DecodeBody(chunk, remain_length)) {
    delete r;
    return nullptr;
  }

  return r;
}

int RpcRequestMessageFactory::Encode(hdfs::io::IOChunk* chunk,
                                     RpcRequestMessage* r) {
  auto res = std::unique_ptr<RpcResponseMessage>(svc_->CallMethod(r));
  return res->Encode(chunk);
}

}  // namespace message
}  // namespace hdfs
}  // namespace chunkserver
}  // namespace bytestore
