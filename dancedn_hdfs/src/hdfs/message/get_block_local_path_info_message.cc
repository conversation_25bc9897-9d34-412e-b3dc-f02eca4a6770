// Copyright (c) 2019-present, ByteDance Inc. All rights reserved.

#include "hdfs/message/get_block_local_path_info_message.h"

#include "hdfs/io/io_buf.h"
#include "hdfs/proto/ClientDatanodeProtocol.pb.h"

namespace bytestore {
namespace chunkserver {
namespace hdfs {
namespace message {

GetBlockLocalPathInfoMessage::GetBlockLocalPathInfoMessage() : r_(nullptr) {}

GetBlockLocalPathInfoMessage::~GetBlockLocalPathInfoMessage() {
  delete r_;
}

RpcRequestMessage* GetBlockLocalPathInfoMessage::New() {
  return new GetBlockLocalPathInfoMessage();
}

bool GetBlockLocalPathInfoMessage::DecodeBody(hdfs::io::IOChunk* chunk,
                                              int len) {
  // decode varint body length
  int body_len = 0;
  if (!chunk->ReadVarint32(reinterpret_cast<uint32_t*>(&body_len))) {
    return false;
  }
  len = len - hdfs::io::IOChunk::CalcVarintSize(body_len) - body_len;
  if (len < 0) return false;

  // decode body
  auto proto = new hadoop::hdfs::GetBlockLocalPathInfoRequestProto();
  if (!proto->ParseFromArray(chunk->ReadBytes(body_len), body_len)) {
    delete proto;
    return false;
  }
  r_ = proto;
  return true;
}

int GetBlockLocalPathInfoMessage::Encode(hdfs::io::IOChunk* chunk) {
  int used = RpcRequestMessage::Encode(chunk);
  std::string r = r_->SerializeAsString();
  int len = hdfs::io::IOChunk::CalcVarintSize(r.size()) + r.size();
  chunk->EnsureHasUnused(len);
  chunk->WriteVarint(r.size());
  chunk->WriteBytes((const uint8_t*)r.c_str(), r.size());
  return used + len;
}

}  // namespace message
}  // namespace hdfs
}  // namespace chunkserver
}  // namespace bytestore
