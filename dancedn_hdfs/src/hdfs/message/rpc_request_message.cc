// Copyright (c) 2019-present, ByteDance Inc. All rights reserved.

#include "hdfs/message/rpc_request_message.h"

#include <string>

#include "hdfs/io/define.h"
#include "hdfs/io/io_buf.h"
#include "hdfs/proto/ProtobufRpcEngine.pb.h"
#include "hdfs/proto/RpcHeader.pb.h"
#include "hdfs/util.h"

namespace bytestore {
namespace chunkserver {
namespace hdfs {
namespace message {

RpcRequestMessage::RpcRequestMessage() {
  rpc_header_ = nullptr;
  req_header_ = nullptr;
}

RpcRequestMessage::~RpcRequestMessage() {
  delete rpc_header_;
  delete req_header_;
}

void RpcRequestMessage::RpcHeader(
    hadoop::common::RpcRequestHeaderProto* proto) {
  rpc_header_ = proto;
}

void RpcRequestMessage::RequestHeader(
    hadoop::common::RequestHeaderProto* proto) {
  req_header_ = proto;
}

hadoop::common::RpcRequestHeaderProto* RpcRequestMessage::RpcHeader() const {
  return rpc_header_;
}

hadoop::common::RequestHeaderProto* RpcRequestMessage::RequestHeader() const {
  return req_header_;
}

hadoop::common::RpcRequestHeaderProto* RpcRequestMessage::ReleaseRpcHeader() {
  auto t = rpc_header_;
  rpc_header_ = nullptr;
  return t;
}

hadoop::common::RequestHeaderProto* RpcRequestMessage::ReleaseRequestHeader() {
  auto t = req_header_;
  req_header_ = nullptr;
  return t;
}

int RpcRequestMessage::DecodeRpcHeader(
    hdfs::io::IOChunk* chunk, int len,
    hadoop::common::RpcRequestHeaderProto** proto) {
  // decode varint header length
  int rpc_header_len = 0;
  if (!chunk->ReadVarint32(reinterpret_cast<uint32_t*>(&rpc_header_len)))
    return IO_ERR;
  len =
      len - hdfs::io::IOChunk::CalcVarintSize(rpc_header_len) - rpc_header_len;
  if (len <= 0) return -1;

  // decode rpc_header
  auto rpc_header = new hadoop::common::RpcRequestHeaderProto();
  if (!rpc_header->ParseFromArray(chunk->ReadBytes(rpc_header_len),
                                  rpc_header_len)) {
    delete rpc_header;
    return -1;
  }

  *proto = rpc_header;
  return len;
}

int RpcRequestMessage::DecodeRequestHeader(
    hdfs::io::IOChunk* chunk, int len,
    hadoop::common::RequestHeaderProto** proto) {
  // decode request header length
  int header_len = 0;
  if (!chunk->ReadVarint32(reinterpret_cast<uint32_t*>(&header_len))) {
    return -1;
  }
  len = len - hdfs::io::IOChunk::CalcVarintSize(header_len) - header_len;
  if (len < 0) return -1;

  // decode request header
  auto header = new hadoop::common::RequestHeaderProto();
  if (!header->ParseFromArray(chunk->ReadBytes(header_len), header_len)) {
    delete header;
    return -1;
  }

  *proto = header;
  return len;
}

int RpcRequestMessage::Encode(hdfs::io::IOChunk* chunk) {
  // encode request header length
  std::string rpc_header = rpc_header_->SerializeAsString();
  std::string req_header = req_header_->SerializeAsString();
  int len = hdfs::io::IOChunk::CalcVarintSize(rpc_header.size()) +
            hdfs::io::IOChunk::CalcVarintSize(req_header.size()) +
            rpc_header.size() + req_header.size();
  chunk->EnsureHasUnused(len);
  chunk->WriteVarint(rpc_header.size());
  chunk->WriteBytes((const uint8_t*)rpc_header.c_str(), rpc_header.size());
  chunk->WriteVarint(req_header.size());
  chunk->WriteBytes((const uint8_t*)req_header.c_str(), req_header.size());
  return len;
}

}  // namespace message
}  // namespace hdfs
}  // namespace chunkserver
}  // namespace bytestore
