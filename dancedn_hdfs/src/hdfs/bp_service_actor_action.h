// Copyright (c) 2019-present, ByteDance Inc. All rights reserved.

#pragma once

#include <string>
#include <vector>

#include "hdfs/exceptions.h"

namespace bytestore {
namespace chunkserver {
namespace hdfs {

class NamenodeClient;
class DatanodeRegistration;

enum ActionType : uint8_t {
  BadBlockReport = 0,
  ErrorReport = 1,
};

class BPServiceActorAction {
 public:
  explicit BPServiceActorAction(ActionType action_type)
      : action_type_(action_type) {}
  virtual ~BPServiceActorAction() {}
  virtual exceptions::Exception ReportTo(
      NamenodeClient* namenode_client,
      DatanodeRegistration* dn_registration) = 0;

  ActionType GetType() const {
    return action_type_;
  }

  virtual bool Equals(BPServiceActorAction* action) = 0;

 private:
  ActionType action_type_;
};

}  // namespace hdfs
}  // namespace chunkserver
}  // namespace bytestore
