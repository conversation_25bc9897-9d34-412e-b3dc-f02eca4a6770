// Copyright (c) 2019-present, ByteDance Inc. All rights reserved.

#include "hdfs/data_pipeline_context.h"

#include <utility>
#include <vector>

#include "byte/string/algorithm.h"
#include "byte/string/number.h"

namespace bytestore {
namespace chunkserver {
namespace hdfs {

DataPipelineContext::DataPipelineContext() {
  io_priority_options_ = IOPriorityOptions();
}

DataPipelineContext::DataPipelineContext(
    const IOPriorityOptions& io_priority_options, TraceBaggage trace_baggage)
    : io_priority_options_(io_priority_options),
      trace_baggage_(std::move(trace_baggage)) {
  auto baggages = trace_baggage_.GetBaggages();
  for (auto iter = baggages.cbegin(); iter != baggages.cend(); ++iter) {
    LOG(DEBUG) << "key:" << iter->first << " value:" << iter->second;
  }
}

void DataPipelineContext::SetTraceBaggage(TraceBaggage trace_baggage) {
  trace_baggage_ = std::move(trace_baggage);
}

DataPipelineContext* DataPipelineContext::NewDefaultContext() {
  return new DataPipelineContext();
}

OpKey DataPipelineContext::BuildKey(const std::string& bpid, uint64_t block_id,
                                    const Operation& op) const {
  LOG(DEBUG) << "bpid:" << bpid << " operation:" << OperationToString(op);
  auto trace_baggage = trace_baggage_.GetBaggages();
  for (auto iter = trace_baggage.cbegin(); iter != trace_baggage.cend();
       ++iter) {
    LOG(DEBUG) << "key:" << iter->first << " value:" << iter->second;
  }
  std::string user = trace_baggage["user"];
  std::string psm = trace_baggage["psm"];
  std::string platform = trace_baggage["platform"];
  std::string job = trace_baggage["job"];
  int32_t grade = 0;
  try {
    grade = std::stoi(trace_baggage["grade"]);
  } catch (std::exception& e) {
    LOG(DEBUG) << "grade:" << trace_baggage["grade"]
               << " in trace baggage is not legal"
               << ", bpid:" << bpid
               << ", block_id:" << byte::IntegerToString(block_id)
               << ", operation:" << OperationToString(op);
  }
  std::string app_type = trace_baggage["app_type"];
  std::string appid = trace_baggage["appid"];
  std::string path = trace_baggage["path"];
  std::string client_ip_port = trace_baggage["client"];
  std::string client_ip = client_ip_port;
  client_ip.erase(std::remove(client_ip.begin(), client_ip.end(), '['),
                  client_ip.end());
  client_ip.erase(std::remove(client_ip.begin(), client_ip.end(), ']'),
                  client_ip.end());
  std::size_t found = client_ip.find_last_of(':');
  if (found != std::string::npos) {
    client_ip = client_ip.substr(0, found);
  }
  return OpKey::New(user, psm, platform, job, grade, app_type, appid, client_ip,
                    bpid, path, block_id, op);
}

}  // namespace hdfs
}  // namespace chunkserver
}  // namespace bytestore
