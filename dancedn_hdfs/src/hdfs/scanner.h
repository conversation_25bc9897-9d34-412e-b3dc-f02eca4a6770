// Copyright (c) 2018-present, ByteDance Inc. All rights reserved.

#pragma once

#include <deque>
#include <future>  // NOLINT
#include <memory>
#include <set>
#include <string>
#include <unordered_map>
#include <utility>
#include <vector>

#include "byte/base/atomic.h"
#include "byte/base/closure.h"
#include "byte/concurrent/mutex.h"
#include "chunkserver/common/chunkserver_types.h"
#include "chunkserver/common/io_request.h"
#include "common/memory_pool_repo.h"
#include "common/store_types.h"

namespace bytestore {
class AsyncThread;
class Timer;

namespace chunkserver {
class Disk;

namespace hdfs {

class Store;

template <class T>
class Scanner {
 public:
  Scanner() {}

  ~Scanner() {}

  virtual void Start() = 0;
  virtual void Stop() = 0;

 protected:
  virtual void execute() = 0;
  virtual void reschedule() = 0;
  virtual bool Next(T* next) = 0;
  virtual void Scan(int index, int scan_depth) = 0;
};

}  // namespace hdfs
}  // namespace chunkserver
}  // namespace bytestore
