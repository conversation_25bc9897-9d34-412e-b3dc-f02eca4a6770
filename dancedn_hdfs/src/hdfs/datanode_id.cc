// Copyright (c) 2019-present, ByteDance Inc. All rights reserved.

#include "hdfs/datanode_id.h"

#include "byte/include/byte_log.h"
#include "byte/string/format/print.h"
#include "hdfs/proto/DatanodeProtocol.pb.h"
#include "hdfs/proto/hdfs.pb.h"

namespace bytestore {
namespace chunkserver {
namespace hdfs {

DatanodeID::DatanodeID() {
  Init("0.0.0.0", "", "", 0, 0, 0, 0);
}

DatanodeID::DatanodeID(const std::string& ip_addr, const std::string& hostname,
                       const std::string& uuid, int xfer_port, int info_port,
                       int ipc_port, int byterpc_port) {
  Init(ip_addr, hostname, uuid, xfer_port, info_port, ipc_port, byterpc_port);
}

DatanodeID::DatanodeID(const DatanodeID& datanode) {
  Init(datanode.ip_addr_, datanode.hostname_, datanode.uuid_,
       datanode.xfer_port_, datanode.info_port_, datanode.ipc_port_,
       datanode.byterpc_port_);
  peer_hostname_ = datanode.peer_hostname_;
  eth0_addr_ = datanode.eth0_addr_;
}

DatanodeID::~DatanodeID() {}

DatanodeID* DatanodeID::Clone() const {
  DatanodeID* dn = new DatanodeID(ip_addr_, hostname_, uuid_, xfer_port_,
                                  info_port_, ipc_port_, byterpc_port_);
  dn->peer_hostname_ = peer_hostname_;
  dn->eth0_addr_ = eth0_addr_;
  return dn;
}

void DatanodeID::Init(const std::string& ip_addr, const std::string& hostname,
                      const std::string& uuid, int xfer_port, int info_port,
                      int ipc_port, int byterpc_port) {
  ip_addr_ = ip_addr;
  hostname_ = hostname;
  uuid_ = uuid;
  xfer_port_ = xfer_port;
  info_port_ = info_port;
  ipc_port_ = ipc_port;
  byterpc_port_ = byterpc_port;
  peer_hostname_ = "";
  eth0_addr_ = "";
  info_secure_port_ = 0;

  if (!io::IPAddress::Parse(&xfer_addr_, ip_addr.c_str(), xfer_port) ||
      !io::IPAddress::Parse(&info_addr_, ip_addr.c_str(), info_port_) ||
      !io::IPAddress::Parse(&ipc_addr_, ip_addr.c_str(), ipc_port_) ||
      !io::IPAddress::Parse(&byterpc_addr_, ip_addr.c_str(), byterpc_port_)) {
    LOG(WARNING) << "Invalid IP address " << ip_addr;
    Init("0.0.0.0", hostname, uuid, xfer_port, info_port_, ipc_port_,
         byterpc_port_);
  }
}

std::string DatanodeID::ToString() const {
  return byte::StringPrint(
      "ip_addr:%s,hostname:%s,datanodeUuid:%s,xfer_port:%d"
      ",info_port:%d,ipc_port:%d,byterpc_port",
      ip_addr_, hostname_, uuid_, xfer_port_, info_port_, ipc_port_,
      byterpc_port_);
}

exceptions::Exception DatanodeID::ToProto(
    hadoop::hdfs::DatanodeIDProto* proto) const {
  proto->set_ipaddr(ip_addr_);
  proto->set_hostname(hostname_);
  proto->set_datanodeuuid(uuid_);
  proto->set_xferport(xfer_port_);
  proto->set_infoport(info_port_);
  proto->set_ipcport(ipc_port_);
  proto->set_infosecureport(info_secure_port_);
  proto->set_eth0addr(eth0_addr_);
  proto->set_byterpcport(byterpc_port_);
  return exceptions::Exception();
}

DatanodeID* DatanodeID::ParseProto(const hadoop::hdfs::DatanodeIDProto* proto) {
  if (proto->ipaddr().empty()) {
    return new DatanodeID();
  } else {
    return new DatanodeID(proto->ipaddr(), proto->hostname(),
                          proto->datanodeuuid(), proto->xferport(),
                          proto->infoport(), proto->ipcport(),
                          proto->byterpcport());
  }
}

bool DatanodeID::Equals(const std::shared_ptr<DatanodeID>& rhs) const {
  if (ip_addr_ != rhs->GetIPAddr()) return false;
  if (hostname_ != rhs->GetHostName()) return false;
  if (uuid_ != rhs->GetUUID()) return false;
  if (xfer_port_ != rhs->GetXferPort()) return false;
  if (info_port_ != rhs->GetInfoPort()) return false;
  if (ipc_port_ != rhs->GetIPCPort()) return false;
  if (byterpc_port_ != rhs->GetByterpcPort()) return false;
  return true;
}

}  // namespace hdfs
}  // namespace chunkserver
}  // namespace bytestore
