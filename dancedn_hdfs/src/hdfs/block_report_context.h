// Copyright (c) 2019-present, ByteDance Inc. All rights reserved.

#pragma once

#include <string>

#include "hdfs/exceptions.h"

namespace hadoop {
namespace hdfs {
namespace datanode {

class BlockReportContextProto;

}
}  // namespace hdfs
}  // namespace hadoop

namespace bytestore {
namespace chunkserver {
namespace hdfs {

class BlockReportContext {
 public:
  BlockReportContext(uint32_t total_rpcs, uint32_t current_rpc,
                     int64_t report_id)
      : total_rpcs_(total_rpcs),
        current_rpc_(current_rpc),
        report_id_(report_id) {}
  ~BlockReportContext() {}

  uint32_t GetTotalRPCs() const {
    return total_rpcs_;
  }
  uint32_t GetCurrentRPC() const {
    return current_rpc_;
  }
  int64_t GetReportID() const {
    return report_id_;
  }

  exceptions::Exception ToProto(
      hadoop::hdfs::datanode::BlockReportContextProto* proto) const;

 private:
  uint32_t total_rpcs_;
  uint32_t current_rpc_;
  int64_t report_id_;
};

}  // namespace hdfs
}  // namespace chunkserver
}  // namespace bytestore
