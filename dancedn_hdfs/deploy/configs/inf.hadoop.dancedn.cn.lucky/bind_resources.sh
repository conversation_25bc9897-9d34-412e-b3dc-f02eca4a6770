#!/bin/bash

# cpuset cgroup
echo "set cpuset cgroup"
cpuset_cpath="/sys/fs/cgroup/cpuset"
cpuset_cpath_tiger=${cpuset_cpath}"/tiger"
cpuset_cpath_tiger_dn=${cpuset_cpath_tiger}"/dancedn"
if [[ -w ${cpuset_cpath_tiger} ]]; then
  cat ${cpuset_cpath}"/cpuset.mems" > ${cpuset_cpath_tiger}"/cpuset.mems"
  cat ${cpuset_cpath}"/cpuset.cpus" > ${cpuset_cpath_tiger}"/cpuset.cpus"

  mkdir -p ${cpuset_cpath_tiger_dn} && chmod -R 777 ${cpuset_cpath_tiger_dn}
  if [[ -w ${cpuset_cpath_tiger_dn} ]]; then
    cat ${cpuset_cpath_tiger}"/cpuset.mems" > ${cpuset_cpath_tiger_dn}"/cpuset.mems"
    cpuset1=`numactl -H | grep "node 0 cpus:" | awk -v OFS="," '{print $(NF-3),$(NF-2),$(NF-1),$NF}'`
    cpuset2=`numactl -H | grep "node 1 cpus:" | awk -v OFS="," '{print $(NF-3),$(NF-2),$(NF-1),$NF}'`
    cpus="$cpuset1,$cpuset2"
    if [[ -w ${cpuset_cpath_tiger_dn}/cpuset.cpus ]]; then
      echo $cpus > ${cpuset_cpath_tiger_dn}"/cpuset.cpus"
      echo $$ > ${cpuset_cpath_tiger_dn}"/cgroup.procs"
    fi
  fi
fi
# mem cgroup
mem_cpath_tiger="/sys/fs/cgroup/memory/tiger"
mem_cpath_tiger_dn=${mem_cpath_tiger}"/dancedn"
if [[ -w ${mem_cpath_tiger} ]]; then
  mkdir -p ${mem_cpath_tiger_dn} && chmod -R 777 ${mem_cpath_tiger_dn}
  if [[ -w ${mem_cpath_tiger_dn} ]]; then
    mem_all=`free -m | grep "Mem:" | awk '{print $2}'`
    mem_limit=`expr $mem_all / 2`
    echo "${mem_limit}M" > ${mem_cpath_tiger_dn}"/memory.limit_in_bytes"
    echo $$ > ${mem_cpath_tiger_dn}"/cgroup.procs"
  fi
fi
