#!/bin/bash

set -e -o pipefail
set -x

CUR_DIR=$(dirname "$0")
DANCEDN_DIR=$(cd "$CUR_DIR"; pwd)
BDS_DIR=$(cd ${DANCEDN_DIR}/..; pwd)
BUILD_TYPE="RelWithDebInfo"
BUILD_UNIT_TEST="ON"
JOBS=$(grep -c ^processor /proc/cpuinfo 2>/dev/null)
BVC_BIN_PATH=/opt/tiger/ss_bin/bvc
if [ ! -e $BVC_BIN_PATH ]; then
    BVC_BIN_PATH=/opt/common_tools/bvc
fi
if [ ! -e $BVC_BIN_PATH ]; then
    BVC_BIN_PATH=/usr/bin/bvc
fi
if [ ! -e $BVC_BIN_PATH ]; then
    echo "bvc not exist"
    exit 1
fi
# only support debian10-gcc8 and debian9-gcc6(default) for now
THIRDPARTY_OS_COMPILER_VERSION="debian10-gcc8"
THIRDPARTY_VERSION=""
THIRDPARTY_SCM_REPO=""
THIRDPARTY_INSTALL_DIR=${DANCEDN_DIR}/third/install

usage() {
  echo "
Usage: $0 <options>
  Optional options:
    --thirdparty-os-compiler-version <the-version-of-os-and-compiler>
      The os and compiler of thirdparty, if not specified, it will use debian10-gcc8
      only support debian10-gcc8 and debian9-gcc6 for now.
    --thirdparty-version <the-version-of-thirdparty>
      The scm version of thirdparty, if not specified, it will use the version
      into in file third/current_thirdparty_version.
    --build-type <Debug|Release|RelWithDebInfo>
      Set build type, default to RelWithDebInfo.
    --jobs <number-of-parallel-jobs>
      The number of third parties to build in parallel. Default to the number of CPU.
    --build-unit-test
      If need to build unit test. Default to ON.
    -h, --help
      Print this usage.
  "
}

OPTS=$(getopt \
  -n $0 \
  -o 'h' \
  -l 'thirdparty-os-compiler-version:' \
  -l 'thirdparty-version:' \
  -l 'build-type:' \
  -l 'build-unit-test:' \
  -l 'jobs:' \
  -l 'help' \
  -- "$@")

if [ $? != 0 ] ; then
  usage
  exit 1
fi

eval set -- "$OPTS"
while true; do
  case "$1" in
    --thirdparty-os-compiler-version)
      THIRDPARTY_OS_COMPILER_VERSION="$2"
      shift 2
      ;;
    --thirdparty-version)
      THIRDPARTY_VERSION="$2"
      shift 2
      ;;
    --build-type)
      BUILD_TYPE="$2"
      shift 2
      ;;
    --build-unit-test)
      BUILD_UNIT_TEST="$2"
      shift 2
      ;;
    --jobs)
      JOBS="$2"
      shift 2
      ;;
    -h | --help)
      usage
      exit 0
      ;;
    --)
      shift ; break ;;
    *) usage; exit 1 ;;
  esac
done

if [ "${THIRDPARTY_OS_COMPILER_VERSION}" = "debian10-gcc8" ]; then
    THIRDPARTY_SCM_REPO=$(grep DEBIAN_10_GCC8_SCM_REPO_PATH ${DANCEDN_DIR}/third/current_thirdparty_version | awk -F'=' '{print $NF}')
    if [ -z "${THIRDPARTY_VERSION}" ]; then
        THIRDPARTY_VERSION=$(grep DEBIAN_10_GCC8_SCM_VERSION ${DANCEDN_DIR}/third/current_thirdparty_version | awk -F'=' '{print $NF}')
    fi
fi
if [ "${THIRDPARTY_OS_COMPILER_VERSION}" = "debian9-gcc6" ]; then
    THIRDPARTY_SCM_REPO=$(grep DEBIAN_9_GCC6_SCM_REPO_PATH ${DANCEDN_DIR}/third/current_thirdparty_version | awk -F'=' '{print $NF}')
    if [ -z "${THIRDPARTY_VERSION}" ]; then
        THIRDPARTY_VERSION=$(grep DEBIAN_9_GCC6_SCM_VERSION ${DANCEDN_DIR}/third/current_thirdparty_version | awk -F'=' '{print $NF}')
    fi
fi

echo "====== ${THIRDPARTY_SCM_REPO}:${THIRDPARTY_VERSION} ======"

# download or update third party
if [ -f "${THIRDPARTY_INSTALL_DIR}/current_revision" ]; then
  CURRENT_THIRDPARTY_VERSION=$(cat ${THIRDPARTY_INSTALL_DIR}/current_revision | grep version | awk -F':' '{print $2}')
  if [ "$CURRENT_THIRDPARTY_VERSION" == "$THIRDPARTY_VERSION" ]; then
    echo "thirdparty version is the same as the current version, skip update."
  else
    pushd ${THIRDPARTY_INSTALL_DIR}
    echo "update thirdparty version to ${THIRDPARTY_VERSION}"
    $BVC_BIN_PATH reset ${THIRDPARTY_VERSION}
    popd
  fi
else
  rm -rf ${THIRDPARTY_INSTALL_DIR}
  pushd ${DANCEDN_DIR}/third
  echo "download thirdparty version to ${THIRDPARTY_VERSION}"
  $BVC_BIN_PATH clone ${THIRDPARTY_SCM_REPO} install --version ${THIRDPARTY_VERSION}
  popd
fi

# build dancedn
mkdir -p $DANCEDN_DIR/build
pushd $DANCEDN_DIR/build
cmake -DCMAKE_INSTALL_PREFIX=${THIRDPARTY_INSTALL_DIR} \
      -DCMAKE_PREFIX_PATH=${THIRDPARTY_INSTALL_DIR} \
      -DCMAKE_CXX_COMPILER=/usr/bin/c++ \
      -DCMAKE_C_COMPILER=/usr/bin/cc \
      -DCMAKE_INCLUDE_PATH=${THIRDPARTY_INSTALL_DIR}/include \
      -DCMAKE_LIBRARY_PATH=${THIRDPARTY_INSTALL_DIR}/lib \
      -DCMAKE_BUILD_TYPE=${BUILD_TYPE} \
      -DBUILD_UNIT_TEST=${BUILD_UNIT_TEST} \
      ..
make -j ${JOBS}
popd

# do cpplint
${DANCEDN_DIR}/lint.sh

# generate output
rm -rf $BDS_DIR/output
OUTPUT_DIR=$DANCEDN_DIR/output
rm -rf $OUTPUT_DIR
cp -r $DANCEDN_DIR/deploy $OUTPUT_DIR
cp $DANCEDN_DIR/build/src/hdfs/dancedn $OUTPUT_DIR/bin/chunkserver
cp $DANCEDN_DIR/third/install/current_revision $OUTPUT_DIR/thirdparty_current_revision
mv $OUTPUT_DIR $BDS_DIR/
