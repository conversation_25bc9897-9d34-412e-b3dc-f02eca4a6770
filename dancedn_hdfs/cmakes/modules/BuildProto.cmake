function(build_proto target proto_we_dir proto_include_dir)
  message(STATUS "Will build proto, target: ${target}, proto_we_dir: ${proto_we_dir}, proto_include_dir: ${proto_include_dir}")
  set(proto_files ${ARGN})
  file(MAKE_DIRECTORY ${proto_we_dir})
  file(MAKE_DIRECTORY ${proto_include_dir})
  foreach(proto_file IN LISTS proto_files)
    GET_FILENAME_COMPONENT(proto_file_absolute "${proto_file}" ABSOLUTE)
    GET_FILENAME_COMPONENT(proto_file_absolute_path "${proto_file_absolute}" PATH)
    GET_FILENAME_COMPONENT(proto_file_name_we "${proto_file}" NAME_WE)

    # Generated header and source files
    set(proto_file_name_we_src "${proto_we_dir}/${proto_file_name_we}.pb.cc")
    set(proto_file_name_we_hdr "${proto_we_dir}/${proto_file_name_we}.pb.h")
    set(proto_file_include_hdr "${proto_include_dir}/${proto_file_name_we}.pb.h")

    LIST(APPEND PROTO_SRCS "${proto_file_name_we_src}")
    message(STATUS "use protoc to generate code for ${proto_file}")

    ADD_CUSTOM_COMMAND(
      OUTPUT "${proto_file_name_we_src}" "${proto_file_name_we_hdr}"
      COMMAND ${CMAKE_PREFIX_PATH}/bin/protoc
        --cpp_out="${proto_we_dir}"
        -I "${proto_file_absolute_path}"
        "${proto_file_absolute}"
      COMMAND mv ${proto_file_name_we_hdr} ${proto_file_include_hdr}
      DEPENDS "${proto_file_absolute}")
  endforeach()

  add_library(${target}_src_obj OBJECT ${PROTO_SRCS})
  target_include_directories(${target}_src_obj PRIVATE ${proto_include_dir})
  add_library(${target} STATIC $<TARGET_OBJECTS:${target}_src_obj>)
  target_include_directories(${target} PRIVATE ${proto_include_dir})
endfunction()