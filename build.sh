#!/bin/bash

help_message="$(basename "$0") [--debug] [--release] [--byte_express] [--utcp] [--fiber] [--asan] [--gcov] [--iobuf_mthreads] [--enable_iobuf_mon] [--disable_tlspool_mthreads_check] [--disable_lto] [--mem_check] [--enable_depre_api] [--with_numa] [--enable_extend_header] [--enable_metrics2] [--enable_thrift] [-h]

where:
    -h|--help                         Show this help message.
    --debug                           Build byterpc with debug mode.
    --release                         Build byterpc with release mode.
    --byte_express                    Enable rdma transport.
    --utcp                            Enable utcp transport.
    --fiber                           Enable fiber.
    --asan                            Enable asan sanitizer.
    --gcov                            Enable coverage analysis.
    --iobuf_mthreads                  Enable multiple threads access IOBuf.
    --enable_iobuf_mon                Enable iobuf memory monitor(default is OFF).
    --disable_tlspool_mthreads_check  Disable tls pool multiple thread check(default is ON).
    --disable_lto                     Disable lto(default is ON).
    --mem_check                       Enable memory corruption check.
    --enable_depre_api                Enable deprecated APIs(default it OFF).
    --with_numa                       Build byterpc with numa(default is OFF).
    --enable_extend_header            Enable protocol extend header(default is OFF).
    --enable_metrics2                 Build with byte metrics 2.0(default is OFF).
    --enable_thrift                   Build with thrift protocol(default is OFF)."

SOURCE_DIR=`pwd`
BUILD_DIR=${BUILD_DIR:-./build}
BUILD_TYPE=RelWithDebInfo
CXX=${CXX:-g++}
LOCAL_DEBUG=OFF
ENABLE_BYTE_EXPRESS="OFF"
ENABLE_UTCP="OFF"
ENABLE_FIBER="OFF"
ENABLE_ASAN="OFF"
ENABLE_GCOV="OFF"
ENABLE_IOBUF_MTHREADS="OFF"
ENABLE_IOBUF_MONITOR="OFF"
DISABLE_TLSPOOL_MTHREADS_CHECK="OFF"
ENABLE_LTO="ON"
ENABLE_MEMORY_CHECK="OFF"
ENABLE_DEPRECATED_API="OFF"
WITH_NUMA="OFF"
ENABLE_EXTEND_HEADER="OFF"
ENABLE_METRICS2="OFF"
ENABLE_THRIFT="OFF"

while [[ $# > 0 ]]; do
    key="$1"
    case $key in
        -h|--help)
            echo "$help_message"
            exit
            ;;
        debug|--debug)
            BUILD_TYPE=Debug
            LOCAL_DEBUG="ON"
            echo "enable local debug"
            ;;
        release|--release)
            BUILD_TYPE=RelWithDebInfo
            ;;
        --byte_express|--be)
            ENABLE_BYTE_EXPRESS="ON"
            echo "build with byte express"
            ;;
        --utcp)
            ENABLE_UTCP="ON"
            echo "build with utcp"
            ;;
        --fiber)
            ENABLE_FIBER="ON"
            echo "build with fiber"
            ;;
        --asan)
            ENABLE_ASAN="ON"
            echo "build with asan"
            ;;
        --gcov)
            ENABLE_GCOV="ON"
            echo "build with gcov"
            ;;
        --iobuf_mthreads)
            ENABLE_IOBUF_MTHREADS="ON"
            echo "build with multiple threads access IOBuf"
            ;;
        --enable_iobuf_mon)
            ENABLE_IOBUF_MONITOR="ON"
            echo "build with iobuf memory monitor"
            ;;
        --disable_tlspool_mthreads_check)
            DISABLE_TLSPOOL_MTHREADS_CHECK="ON"
            echo "build with tls pool multiple threads check off"
            ;;
        --disable_lto)
            ENABLE_LTO="OFF"
            echo "build without lto"
            ;;
        --mem_check)
            ENABLE_MEMORY_CHECK="ON"
            echo "build with memory corruption check"
            ;;
        --enable_depre_api)
            ENABLE_DEPRECATED_API="ON"
            echo "build with deprecated APIs enables"
            ;;
        --with_numa)
            WITH_NUMA="ON"
            echo "build with numa"
            ;;
        --enable_extend_header)
            ENABLE_EXTEND_HEADER="ON"
            echo "build with extend header"
            ;;
        --enable_metrics2)
            ENABLE_METRICS2="ON"
            echo "build with byte metrics 2.0"
            ;;
        --enable_thrift)
            ENABLE_THRIFT="ON"
            echo "build with thrift protocol"
    esac
    shift
done

set -e
set -x

git submodule sync --recursive
git submodule update --init --recursive

mkdir -p $BUILD_DIR/ \
  && cd $BUILD_DIR/ \
  && cmake -DCMAKE_BUILD_TYPE=${BUILD_TYPE} \
           -DBUILD_TESTING=ON \
           -DBYTE_BUILD_TESTS=OFF \
           -DBYTERPC_BUILD_TESTS=ON \
           -DBYTERPC_BUILD_PERF=ON \
           -DBYTERPC_BUILD_EXAMPLE=ON \
           -DBYTERPC_WITH_LIBUNWIND=ON \
           -DBYTERPC_ENABLE_THRIFT=$ENABLE_THRIFT \
           -DLOCAL_DEBUG=$LOCAL_DEBUG \
           -DBYTERPC_ENABLE_BYTE_EXPRESS=$ENABLE_BYTE_EXPRESS \
           -DBYTERPC_ENABLE_UTCP=$ENABLE_UTCP \
           -DBYTERPC_ENABLE_FIBER=$ENABLE_FIBER \
           -DBYTERPC_ENABLE_ASAN=$ENABLE_ASAN \
           -DBYTERPC_ENABLE_GCOV=$ENABLE_GCOV \
           -DBYTERPC_ENABLE_IOBUF_MTHREADS=$ENABLE_IOBUF_MTHREADS \
           -DBYTERPC_ENABLE_IOBUF_MONITOR=$ENABLE_IOBUF_MONITOR \
           -DBYTERPC_DISABLE_TLSPOOL_MTHREADS_CHECK=$DISABLE_TLSPOOL_MTHREADS_CHECK \
           -DBYTERPC_WITH_NUMA=$WITH_NUMA \
           -DBYTERPC_ENABLE_EXTEND_HEADER=$ENABLE_EXTEND_HEADER \
           -DBYTERPC_ENABLE_LTO_OPTIMIZATION=$ENABLE_LTO \
           -DBYTERPC_ENABLE_MEMORY_DEBUG=$ENABLE_MEMORY_CHECK \
           -DBYTERPC_ENABLE_DEPRECATED_API=$ENABLE_DEPRECATED_API \
           -DBYTE_ENABLE_METRICS2=$ENABLE_METRICS2 \
           -DBUILD_JERASURE=OFF  $SOURCE_DIR \
  && make -j$(nproc) -s

# TODO(maochuan) add *.cpp
#
# NOTE: Add prefix of third party headers into the array named
# `_THIRD_PARTY_HEADERS_PREFIXS` in the scripts/cpplint.py file,
# if you found a third party header file is recognized as c system
# header.
cd .. && python scripts/cpplint.py  \
  --exclude=include/byterpc/callback.h \
  --exclude=include/byterpc/util/string_splitter_inl.h \
  --exclude=include/byterpc/util/string_splitter.h \
  --exclude=include/byterpc/util/status.h \
  --exclude=include/byterpc/util/ska_flat_hash_map.h \
  --exclude=include/byterpc/util/numautils.h \
  --exclude=src/util/*.cpp \
  --exclude=src/util/*.h \
  --exclude=src/transport/byte_express/*.h \
  --exclude=src/transport/byte_express/*.cpp \
  --exclude=src/builtin/jquery_min_js.cpp \
  --exclude=src/protocol/http/details/http_parser.h \
  --filter=-build/include_subdir \
  `find src/ include/ -name \*.h -print -o -name \*.cc -print -o -name \*.cpp -print -o -name \*.hpp -print`


# Use the following command to run all the unit tests
# at the dir $BUILD_DIR/$BUILD_TYPE :
# CTEST_OUTPUT_ON_FAILURE=TRUE make test

# cd $SOURCE_DIR && doxygen

# scm products
if [ -d "$SOURCE_DIR/output"  ]; then
    rm -rf "$SOURCE_DIR/output"
fi
mkdir "$SOURCE_DIR/output"

cp $SOURCE_DIR/build/perf/byterpc_perf/perf_client $SOURCE_DIR/output/
cp $SOURCE_DIR/build/perf/byterpc_perf/perf_server $SOURCE_DIR/output/
cp $SOURCE_DIR/build/perf/byterpc_perf/perf_*_multi_transport $SOURCE_DIR/output/
cp $SOURCE_DIR/build/perf/multi_connections/mc_client $SOURCE_DIR/output/
cp $SOURCE_DIR/build/perf/multi_connections/mc_server $SOURCE_DIR/output/
