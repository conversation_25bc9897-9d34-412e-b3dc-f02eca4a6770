#!/bin/bash

usage() {
  echo "
Usage: $0 <options>
  Optional options:
    --all
      Format all the code.
    --local
      Format the code diff with local HEAD.
    --origin
      Format the code diff with origin HEAD.
    --branch <branch-name>
      Set the compare branch when --local or --origin is set, default to 'master' branch.
    -h, --help
      Print this usage.

  Note that --all, --local and --origin can not set at the same time.

Eg.
    $0 --scope all                format all code
  "
}

CUR_DIR=`dirname "$0"`
CODE_HOME=`cd "${CUR_DIR}"; pwd`

OPTS=$(getopt \
  -n $0 \
  -o 'h' \
  -l 'all' \
  -l 'local' \
  -l 'origin' \
  -l 'branch:' \
  -l 'help' \
  -- "$@")

if [ $? != 0 ] ; then
  usage
  exit 1
fi

eval set -- "$OPTS"

FORMAT_SCOPE=
NUM_SCOPE=0
BRANCH=master

if [ $# == 1 ] ; then
  # no arguments
  usage
  exit 1
else
  while true; do
    case "$1" in
      --branch) BRANCH=$2 ; shift 2 ;;
      --all) FORMAT_SCOPE="all" ; NUM_SCOPE=$(( $NUM_SCOPE + 1 )) ; shift 1 ;;
      --local) FORMAT_SCOPE="local" ; NUM_SCOPE=$(( $NUM_SCOPE + 1 )) ; shift 1 ;;
      --origin) FORMAT_SCOPE="origin" ; NUM_SCOPE=$(( $NUM_SCOPE + 1 )) ; shift 1 ;;
      -h|--help) usage ; exit 0 ;;
      --) shift ;  break ;;
      *) usage; exit 1 ;;
    esac
  done
fi

if [ $NUM_SCOPE != 1 ]; then
  echo "--all, --local and --origin can not be set at the same time"
  exit 1
fi

case "$FORMAT_SCOPE" in
  "all"|"local"|"origin")
    ;;
  *)
    usage
    exit 1
esac

pushd ${CODE_HOME}
INCLUDE_RE='.*\.(h|hpp|c|cc|cpp|in)$'
# Ignore third-party headers in 'client/hdfs_sdk/third_party/bundled/include'.
# They are very large and will task too much time to format.
EXCLUDE_RE='.*\/third_party\/bundled\/.*$'
if [ "$FORMAT_SCOPE" == "all" ]; then
  git ls-files | egrep ${INCLUDE_RE} | egrep -v ${EXCLUDE_RE} | xargs -r clang-format -style=file -i
elif [ "$FORMAT_SCOPE" == "local" ]; then
  git diff ${BRANCH} --name-only --diff-filter=ACMRT | egrep ${INCLUDE_RE} | egrep -v ${EXCLUDE_RE} | xargs -r clang-format -style=file -i
elif [ "$FORMAT_SCOPE" == "origin" ]; then
  git diff origin/${BRANCH} --name-only --diff-filter=ACMRT | egrep ${INCLUDE_RE} | egrep -v ${EXCLUDE_RE} | xargs -r clang-format -style=file -i
else
  usage
  popd
  exit 1
fi
popd

