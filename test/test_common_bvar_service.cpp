/**
 * ARIES - A Reliable and Impressive EB-level Storage.
 * Copyright (c) 2018 Baidu.com, Inc. All Rights Reserved.
 *
 * Author: <PERSON><PERSON><PERSON> (<EMAIL>)
 * Datae: 2019/04/1
 * Description: Unittest for common/bvar_service.h
 *
 */

#include <gtest/gtest.h>
#include "gmock/gmock.h"
#include <base/logging.h>
#include "baidu/rpc/server.h"
#include "baidu/inf/aries-api/common/utils.h"
#include "baidu/inf/aries-api/common/bvar_service.h"
#include "baidu/inf/aries-api/common/bvar_monitor.h"
#include "baidu/inf/aries-api/common/bvar_define.h"
#include "baidu/inf/aries-api/common/proto/common.pb.h"
#include "baidu/inf/aries-api/common/config.h"
#include "baidu/inf/aries-api/common/rpc_stub.h"

using ::testing::Return;
using ::testing::DoAll;
using ::testing::SetArgPointee;
using ::testing::SetArgReferee;
using ::testing::_;

namespace aries {
namespace common {

class BvarServiceTests : public ::testing::Test {
public:
    BvarServiceTests() {
        _port = 63352;
    }
    void SetUp() {
        start();
    }
    void TearDown() {
        stop();
    }
private:
    void start() {
        if (_monitor_server.AddService(&_service_impl, baidu::rpc::SERVER_DOESNT_OWN_SERVICE)) {
            LOG(TRACE) << "add bvar monitor server failed";
        }
        baidu::rpc::ServerOptions options;
        if (_monitor_server.Start(_port, &options)) {
            LOG(TRACE) << "start monitor server failed";
        }

    }
    void stop() {
        _monitor_server.Stop(0);
    }
    common::BvarMonitorServiceImpl _service_impl;
    baidu::rpc::Server _monitor_server;
    int _port;
};

TEST_F(BvarServiceTests, test_get_bvar) {
    ARIES_BVAR_RECORDER(test_get_bvar, test1);
    bvar::IntRecorder latency;
    bvar::Window<bvar::IntRecorder> test_intrecorder_bvar("test_intrecorder_bvar", &latency, -1);
    latency << 1 << 3 << 5;
    {
        g_test_get_bvar_test1_recorder.set_value(54321);
        aries::pb::GetBvarRequest request;
        aries::pb::GetBvarResponse response;
        //request.set_token(FLAGS_token);
        //auto bvar = request.add_bvars();
        //bvar->name.set("test_get_bvar_test1");
        SynchronizedClosure closure;
        RpcCallOptions option;
        option.log_id = 555;
        BvarMonitorStub stub;
        base::EndPoint remote_addr;
        FLAGS_port = _port;
        common::init_local_addr();
        remote_addr = common::get_local_addr();
        stub.get_bvar(remote_addr, &request, &response, &closure, &option);
        closure.wait();
        EXPECT_EQ(response.status().code(), AIE_FAIL);
    }
    {
        std::shared_ptr<BvarMonitor> monitor = std::make_shared<BvarMonitor>();
        BvarMonitorConf conf;
        FLAGS_bvar_monitor_include = "test*";
        FLAGS_bvar_max_monitor_num = 1000;
        FLAGS_bvar_monitor_interval_second = 3;
        init_bvar_monitor_conf(&conf, nullptr);
        conf.bvar_monitor_include = "test*";
        monitor->init(conf);
        monitor->start();

        usleep(4000 * 1000);
        
        g_test_get_bvar_test1_recorder.set_value(54321);
        _service_impl.set_bvar_monitor(monitor); 
        aries::pb::GetBvarRequest request;
        aries::pb::GetBvarResponse response;
        request.set_token("default_token");
        request.add_bvar_names("test_get_bvar_test1");
        request.set_last_timestamp(0);
        SynchronizedClosure closure;
        RpcCallOptions option;
        option.log_id = 555;
        BvarMonitorStub stub;
        base::EndPoint remote_addr;
        FLAGS_port = _port;
        common::init_local_addr();
        remote_addr = common::get_local_addr();
        stub.get_bvar(remote_addr, &request, &response, &closure, &option);
        closure.wait();
        EXPECT_EQ(response.int_bvars_size(), 1);
        EXPECT_EQ(response.int_bvars(0).name(), "test_get_bvar_test1");
        EXPECT_EQ(54321, response.int_bvars(0).value(0)); 
        EXPECT_EQ(response.status().code(), AIE_OK);

        closure.reset();
        request.clear_bvar_names();
        response.clear_int_bvars();
        request.add_bvar_names("test_intrecorder_bvar");
        stub.get_bvar(remote_addr, &request, &response, &closure, &option);
        closure.wait();
        EXPECT_EQ(1, response.int_bvars_size());
        EXPECT_EQ("test_intrecorder_bvar", response.int_bvars(0).name());
        EXPECT_EQ(3, response.int_bvars(0).value(0)); 
        EXPECT_EQ(response.status().code(), AIE_OK);
        EXPECT_EQ(3, latency.average());

        monitor->stop();
    }
    {
        std::shared_ptr<BvarMonitor> monitor = std::make_shared<BvarMonitor>();
        BvarMonitorConf conf;
        FLAGS_bvar_monitor_include = "test*";
        FLAGS_bvar_max_monitor_num = 1000;
        FLAGS_bvar_monitor_interval_second = 3;
        init_bvar_monitor_conf(&conf, nullptr);
        monitor->init(conf);
        monitor->start();
        usleep(4000 * 1000);
        
        g_test_get_bvar_test1_recorder.set_value(54321);
        _service_impl.set_bvar_monitor(monitor); 
        aries::pb::GetBvarRequest request;
        aries::pb::GetBvarResponse response;
        request.set_token("default_token");
        request.set_last_timestamp(0);
        SynchronizedClosure closure;
        RpcCallOptions option;
        option.log_id = 555;
        BvarMonitorStub stub;
        base::EndPoint remote_addr;
        FLAGS_port = _port;
        common::init_local_addr();
        remote_addr = common::get_local_addr();
        stub.get_bvar(remote_addr, &request, &response, &closure, &option);
        closure.wait();
        monitor->stop();

    }
}

TEST_F(BvarServiceTests, test_convert_bvar_to_pb) {
    BvarMonitorServiceImpl service_impl;
    std::vector<std::pair<uint64_t, boost::any>> time_value_list;
    aries::pb::GetBvarResponse response;
    std::string name = "name";

    time_value_list.push_back(std::make_pair(0, int32_t()));
    service_impl.convert_bvar_to_pb(0, "name", time_value_list, &response);
    EXPECT_EQ(response.int_bvars_size(), 1);
    response.Clear();
    time_value_list.clear();

    time_value_list.push_back(std::make_pair(0, uint32_t()));
    service_impl.convert_bvar_to_pb(0, "name", time_value_list, &response);
    EXPECT_EQ(response.int_bvars_size(), 1);
    response.Clear();
    time_value_list.clear();

    time_value_list.push_back(std::make_pair(0, bool()));
    service_impl.convert_bvar_to_pb(0, "name", time_value_list, &response);
    EXPECT_EQ(response.bool_bvars_size(), 1);
    response.Clear();
    time_value_list.clear();

    time_value_list.push_back(std::make_pair(0, double()));
    service_impl.convert_bvar_to_pb(0, "name", time_value_list, &response);
    EXPECT_EQ(response.double_bvars_size(), 1);
    response.Clear();
    time_value_list.clear();

    time_value_list.push_back(std::make_pair(0, float()));
    service_impl.convert_bvar_to_pb(0, "name", time_value_list, &response);
    EXPECT_EQ(response.double_bvars_size(), 1);
    response.Clear();
    time_value_list.clear();


}

}
}