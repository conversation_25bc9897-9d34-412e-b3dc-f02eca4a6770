// Copyright (c) 2021, ByteDance Inc. All rights reserved.
#include <absl/memory/memory.h>
#include <byte/util/defer.h>
#include <gtest/gtest.h>
#include <string.h>

#include <memory>

#include "byterpc/byterpc_flags.h"
#include "byterpc/exec_ctx.h"
#include "byterpc/rpc.h"
#include "byterpc/util/logging.h"
#include "iobuf/io_rbuf_pipe.h"
#include "protocol/byte_std/byte_message_serdes.h"
#include "protocol/http/details/http_message.h"
#include "protocol/http/http_plugin.h"
#include "protocol/message_handler_manager.h"
#include "rpc/protobuf_serdes.h"
#include "rpc/server_socket.h"
#include "rpc/stateful_controller.h"
#include "status.pb.h"
#include "util/raw_pack.h"

namespace byterpc {
// TODO(crq): copy from server_socket_test.cpp
// maybe we can merge to one, condition compile?

namespace {
int http_packet_len, bytestd_packet_len;

class StatusService : public status {
public:
    StatusService() {}

    void default_method(::google::protobuf::RpcController* controller,
                        const StatusRequest* request,
                        StatusResponse* response,
                        ::google::protobuf::Closure* done) override {}
};

class SimpleController : public StatefulController {
public:
    SimpleController(ServerSocket* s) : _s(s) {}

    void OnRecycle() override {
        delete this;
    }

    void DoRecycle() override {}

    util::EndPoint local_side() override {
        return _s->local_side();
    }

    util::EndPoint remote_side() override {
        return _s->remote_side();
    }

    void IssueRPC(ClientSocket* socket,
                  const google::protobuf::MethodDescriptor* method,
                  const google::protobuf::Message* request,
                  google::protobuf::Message* response,
                  google::protobuf::Closure* done) override {}

    ProtocolType request_protocol() const override {
        return PROTOCOL_HTTP;
    }

private:
    ServerSocket* _s;
};

class SimpleTransport : public Transport {
public:
    SimpleTransport() : _socket(nullptr) {}
    util::EndPoint local_side() const override {
        return util::EndPoint();
    }
    util::EndPoint remote_side() const override {
        return util::EndPoint();
    }
    TransportType GetTransportType() const override {
        return NUM_TRANSPORT_TYPE;
    }
    bool StartRead(size_t read_limit) override {
        return true;
    }
    ssize_t StartWrite(
        IOBlockRef&& first,
        std::unique_ptr<IOBuf>&& second,
        std::unique_ptr<IOBuf>&& third,
        std::vector<std::pair<std::unique_ptr<IOBuf>, RdmaBufInfo>>&& rdma_write_bufs) override {
        size_t total_size =
            first.size() + (second ? second->size() : 0) + (third ? third->size() : 0);
        return total_size;
    }
    void ResetSocket(Socket* replacement) override {
        _socket = replacement;
    }
    void HandleReadEvent() override {}
    void HandleWriteEvent() override {}
    int GetFD() const override {
        return -1;
    }
    uint32_t NumOfResets() const override {
        return 0;
    }
    void Reset(int error_code) override {
        if (_socket) {
            _socket->SetFailed(error_code, "failed");
        }
    }
    void OwnerRef() override {}
    void OwnerUnref() override {}

    int PreConnect() override {
        return 0;
    }

    bool IsConnected() override {
        return false;
    };

    void SetSocket(Socket* socket) {
        _socket = socket;
    }

private:
    Socket* _socket;
};

class MockByteServerMessageHandler : public ServerMessageHandler {
public:
    explicit MockByteServerMessageHandler(size_t size = 0) {
        if (size > 0) {
            _resp.resize(size, 'a');
        }
    }

    std::string GetMessageHeader() const override {
        return "TRPC";
    }

    ProtocolType GetProtocolType() const override {
        return PROTOCOL_BYTE_STD;
    }

    ParseError HandleRequest(ServerSocket* socket, IORbufPipe* rbuf_pipe) override {
        static int i = 0;
        while (true) {
            ParseError pr = ParseIncomingBuffer(rbuf_pipe);
            if (pr != PARSE_OK) {
                return pr;
            }

            Controller* cntl = new SimpleController(socket);
            if (i++ % 2 == 0) {
                std::unique_ptr<IOBuf> att(new IOBuf());
                if (!_resp.empty()) {
                    att->append(_resp);
                }
                IOBlockRef meta_ref;
                socket->SubmitWrite(cntl, std::move(meta_ref), std::move(att));
            } else {
                std::unique_ptr<IOBuf> write_buf(new IOBuf());
                if (!_resp.empty()) {
                    write_buf->append(_resp);
                }
                socket->SubmitWrite(cntl, {}, std::move(write_buf));
            }

            if (rbuf_pipe->length() == 0) {
                break;
            }
        }
        return PARSE_OK;
    }

    ParseError ParseIncomingBuffer(IORbufPipe* rbuf_pipe) {
        // mock, only recognize forefront 4 bytes "TRPC"
        std::string recv_msg_header;
        recv_msg_header.resize(4);
        size_t n = rbuf_pipe->copy_to(const_cast<char*>(recv_msg_header.c_str()), 4);
        if (n < 4) {
            return PARSE_ERROR_NOT_ENOUGH_DATA;
        }
        if (recv_msg_header != GetMessageHeader()) {
            return PARSE_ERROR_TRY_OTHERS;
        }
        // cut consumed data off
        rbuf_pipe->pop_front(bytestd_packet_len);
        return PARSE_OK;
    }

private:
    std::string _resp;
};

class MockHttpServerMessageHandler : public ServerMessageHandler {
public:
    explicit MockHttpServerMessageHandler(size_t size = 0) {
        if (size > 0) {
            _resp.resize(size, 'a');
        }
    }

    std::string GetMessageHeader() const override {
        return "HTTP";
    }

    ProtocolType GetProtocolType() const override {
        return PROTOCOL_HTTP;
    }

    std::unique_ptr<ParsePlugin> GetExtendParser() const override {
        return std::make_unique<HTTPParsePlugin>();
    }

    ParseError HandleRequest(ServerSocket* socket, IORbufPipe* rbuf_pipe) override {
        while (true) {
            ParseError pr = ParseIncomingBuffer(rbuf_pipe);
            if (pr != PARSE_OK) {
                return pr;
            }

            Controller* cntl = new SimpleController(socket);
            std::unique_ptr<IOBuf> write_buf(new IOBuf());
            if (!_resp.empty()) {
                write_buf->append(_resp);
            }
            socket->SubmitWrite(cntl, {}, std::move(write_buf));

            if (rbuf_pipe->length() == 0) {
                break;
            }
        }
        return PARSE_OK;
    }

    ParseError ParseIncomingBuffer(IORbufPipe* rbuf_pipe) {
        // mock, only recognize forefront 4 bytes "GET "
        std::string recv_msg_header;
        recv_msg_header.resize(4);
        size_t n = rbuf_pipe->copy_to(const_cast<char*>(recv_msg_header.c_str()), 4);
        if (n < 4) {
            return PARSE_ERROR_NOT_ENOUGH_DATA;
        }
        if (recv_msg_header != std::string("GET ")) {
            return PARSE_ERROR_TRY_OTHERS;
        }
        // cut consumed data off
        rbuf_pipe->pop_front(http_packet_len);
        return PARSE_OK;
    }

private:
    std::string _resp;
};

static void SerializeByteStdRequest(const ByteStdSerDes::RequestMeta& meta,
                                    const google::protobuf::Message* request,
                                    IOBuf* attachment,
                                    IOBuf* out) {
    auto request_size = request->ByteSize();
    auto payload_size = request_size + meta.attachment_size;
    size_t meta_size = ByteStdSerDes::REQUEST_META_FIX_SIZE + strlen(meta.full_method_name);

    // serialize rpc header and meta
    char header[12];
    ByteStdSerDes::PackRpcHeader(header, meta_size, payload_size);
    out->append(header, sizeof(header));
    out->append(reinterpret_cast<const char*>(&meta), meta_size);

    // Now serialize request itself
    if (request_size) {
        ProtobufSerDes::SerializePbToWriteBuffer(request, out);
    }

    // Now serialize attachment iff there is any
    if (meta.attachment_size > 0) {
        out->append(*attachment);
    }
}

class ServerSocketTest : public testing::Test {
public:
    ServerSocketTest()
        : http_packet(new IOBuf), byte_std_packet(new IOBuf), attachment(new IOBuf) {}

    void SetUp() override {
        attachment->append(std::string("this is attachment"));
        constructHttpPacket();
        constructByteStdPacket();
    }

protected:
    void constructHttpPacket() {
        auto out = absl::make_unique<IOBuf>();
        IOBuf body;
        HttpHeader hdr;
        hdr.SetHeader("X-Test-Header1", "foo");
        hdr.SetHeader("X-Test-Header2", "bar");
        body.append(std::string("this is http body"));
        util::EndPoint ep;
        // Http method should only be GET, for mocking reasons
        MakeRawHttpRequest(out.get(), &hdr, ep, &body);
        http_packet.reset(out.release());
        http_packet_len = http_packet->length();
    }

    void constructByteStdPacket() {
        auto out = absl::make_unique<IOBuf>();
        StatusRequest req;
        StatusService service;
        const google::protobuf::MethodDescriptor* method = service.GetDescriptor()->method(0);

        ByteStdSerDes::RequestMeta meta;
        meta.correlation_id = 42;
        meta.log_id = 42;
        meta.attachment_size = attachment ? attachment->length() : 0;
        strcpy(meta.full_method_name, method->service()->full_name().c_str());
        meta.service_name_size = method->service()->full_name().size();
        strcpy(meta.full_method_name + meta.service_name_size, method->name().c_str());
        SerializeByteStdRequest(meta, &req, attachment.get(), out.get());
        byte_std_packet.reset(out.release());
        bytestd_packet_len = byte_std_packet->length();
    }

    std::unique_ptr<IOBuf> http_packet;
    std::unique_ptr<IOBuf> byte_std_packet;
    std::unique_ptr<IOBuf> attachment;
};

TEST_F(ServerSocketTest, MixedProtocolIO) {
    size_t kPACKETFACTOR = 5;
    std::unique_ptr<MessageHandlerManager> mgr(new MessageHandlerManager());
    mgr->AddMessageHandler(new MockByteServerMessageHandler(10));
    mgr->AddMessageHandler(new MockHttpServerMessageHandler(10));
    std::unique_ptr<Transport> transport(new SimpleTransport);
    auto ss = new ServerSocket(mgr.get(), nullptr, nullptr);
    ss->ReadyToServe(transport.get());

    IOBuf read_buf;
    for (size_t i = 0; i < kPACKETFACTOR; ++i) {
        read_buf.append(*http_packet);
        read_buf.append(*byte_std_packet);
    }

    auto rbuf_pipe = absl::make_unique<IORbufPipe>();
    read_buf.cut_to(rbuf_pipe.get(), read_buf.length());
    ss->HandleReadEvent(rbuf_pipe.get());
    EXPECT_EQ(ss->GetPendingWriteSize(), kPACKETFACTOR * 2);

    size_t cnt = 0;
    while (ss->GetPendingWriteSize() > 0) {
        ss->HandleWriteEvent();
        cnt++;
    }
    EXPECT_EQ(cnt, kPACKETFACTOR * 2);
    EXPECT_EQ(ss->GetPendingWriteSize(), 0);

    ss->OwnerUnref();
}

}  // namespace
}  // namespace byterpc

int main(int argc, char** argv) {
    testing::InitGoogleTest(&argc, argv);
#ifdef BYTERPC_WITH_NUMA
    byterpc::FLAGS_byterpc_enable_numa_aware = true;
    byterpc::FLAGS_byterpc_memory_pool_numa_init_region_num = "1,1";
#endif
    byterpc::InitOptions init_opt;
    BYTERPC_CHECK(0 == byterpc::ExecCtx::Init(init_opt));

    auto fini_guard = byte::defer([]() { byterpc::ExecCtx::Fini(); });
    return RUN_ALL_TESTS();
}
