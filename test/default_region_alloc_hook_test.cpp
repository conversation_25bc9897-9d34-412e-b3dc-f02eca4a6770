// Copyright (c) 2024, ByteDance Inc. All rights reserved.

#include <gtest/gtest.h>

#include "mem/default_region_alloc_hook.h"

#ifdef BYTERPC_WITH_NUMA
#include <numa.h>
#include <numaif.h>
#endif

#include <cstring>
#include <vector>

#include "byterpc/util/numautils.h"

namespace byterpc {

TEST(DefaultRegionAllocHookTest, AllocRegion) {
    std::vector<size_t> sizes{1UL, 1024, 1024 * 1024UL, 1024 * 1024 * 1024UL};

    for (size_t size : sizes) {
        size_t page_size = 0;
        void* addr = DefaultRegionAllocHook::AllocRegion(size, &page_size, -1);
        ASSERT_NE(addr, nullptr);
        std::memset(addr, 0, size);
        ASSERT_EQ(page_size, 4 * 1024);
        DefaultRegionAllocHook::DeallocRegion(addr, size);
    }
}

#ifdef BYTERPC_WITH_NUMA
TEST(DefaultRegionAllocHookTest, NumaAwareAllocRegion) {
    std::vector<size_t> sizes{1UL, 1024, 1024 * 1024UL, 1024 * 1024 * 1024UL};

    int numa_num = util::get_numa_num();
    for (int i = 0; i < numa_num; ++i) {
        for (size_t size : sizes) {
            size_t page_size = 0;
            void* addr = DefaultRegionAllocHook::AllocRegion(size, &page_size, i);
            ASSERT_NE(addr, nullptr);
            std::memset(addr, 0, size);
            ASSERT_EQ(page_size, 4 * 1024);

            int mode = -1;
            bitmask* nodemask = numa_allocate_nodemask();
            // check vaddr mempolicy
            ASSERT_EQ(0,
                      get_mempolicy(&mode, nodemask->maskp, nodemask->size + 1, addr, MPOL_F_ADDR));
            ASSERT_EQ(mode, MPOL_PREFERRED);
            ASSERT_EQ(1, numa_bitmask_isbitset(nodemask, i));
            numa_bitmask_free(nodemask);

            DefaultRegionAllocHook::DeallocRegion(addr, size);
        }
    }
}
#endif

}  // namespace byterpc

int main(int argc, char** argv) {
    testing::InitGoogleTest(&argc, argv);
    return RUN_ALL_TESTS();
}
