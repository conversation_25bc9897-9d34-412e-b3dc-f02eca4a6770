// Copyright (c) 2019, ByteDance Inc. All rights reserved.

#include "byterpc/thread/ev_thread_helper.h"

#include <byte/concurrent/count_down_latch.h>
#include <byte/util/defer.h>

#include "byterpc/exec_ctx.h"
#include "byterpc/util/logging.h"
#include "gtest/gtest.h"

namespace {

void StopEvThread(byterpc::EvThreadHelper* th) {
    EXPECT_EQ(byterpc::GetCurrentThread(), th);
    th->Stop();
}

void CallbackDone(byte::CountDownLatch* latch) {
    latch->CountDown();
}

bool CallbackDoneFunc(byte::CountDownLatch* latch) {
    latch->CountDown();
    return true;
}

}  // namespace

namespace byterpc {

class EvThreadHelperTest : public ::testing::Test {
public:
    EvThreadHelperTest() {}

    ~EvThreadHelperTest() {}

    void SetUp() override {
        InitOptions opt;
        ExecCtx::Init(opt);
    }

    void TearDown() {}
};

TEST_F(EvThreadHelperTest, StartAndStop) {
    EvThreadHelper th;
    ThreadOptions options("test", 1, TYPE_KERNEL_TCP);
    EXPECT_TRUE(th.Init(options));
    EXPECT_TRUE(th.Start());
    EXPECT_TRUE(th.Stop());
    EXPECT_FALSE(th.Start());
    EXPECT_FALSE(th.Stop());
    EXPECT_TRUE(th.Join());
}

TEST_F(EvThreadHelperTest, StopWithoutStart) {
    EvThreadHelper th;
    ThreadOptions options("test", 1, TYPE_KERNEL_TCP, true);
    EXPECT_TRUE(th.Init(options));
    EXPECT_FALSE(th.Stop());
}

TEST_F(EvThreadHelperTest, StopInEvThread) {
    EvThreadHelper th;
    ThreadOptions options("test", 1, TYPE_KERNEL_TCP);
    EXPECT_TRUE(th.Init(options));
    EXPECT_TRUE(th.Start());

    Closure<void>* callback = NewClosure(&StopEvThread, &th);
    th.Invoke(callback);

    EXPECT_TRUE(th.Join());
}

TEST_F(EvThreadHelperTest, StartWithInitFunction) {
    EvThreadHelper th;
    ThreadOptions options("test", 1, TYPE_KERNEL_TCP, true);
    byte::CountDownLatch latch1(1);
    byte::CountDownLatch latch2(1);
    options._init_function = std::bind(CallbackDoneFunc, &latch1);
    options._fini_function = std::bind(CallbackDoneFunc, &latch2);
    options._use_default_poller = false;
    EXPECT_TRUE(th.Init(options));
    EXPECT_FALSE(th.Start([] { return true; }));
    EXPECT_TRUE(th.Start());
    latch1.Wait();
    EXPECT_TRUE(th.Stop());
    EXPECT_TRUE(th.Join());
    latch2.Wait();
}

TEST_F(EvThreadHelperTest, StartWithFailureInitFunction) {
    EvThreadHelper th;
    ThreadOptions options("test", 1, TYPE_KERNEL_TCP);
    EXPECT_TRUE(th.Init(options));
    EXPECT_FALSE(th.Start([] { return false; }));
}

TEST_F(EvThreadHelperTest, InvokeTask) {
    EvThreadHelper th;
    ThreadOptions options("test", 1, TYPE_KERNEL_TCP);
    EXPECT_TRUE(th.Init(options));

    // Invoke without Start
    size_t k_size = 100;
    byte::CountDownLatch latch(k_size);
    Closure<void>* callback = NewClosure(&CallbackDone, &latch);
    EXPECT_FALSE(th.Invoke(callback));
    delete callback;

    EXPECT_TRUE(th.Start());
    for (size_t i = 0; i < k_size; ++i) {
        Closure<void>* callback = NewClosure(&CallbackDone, &latch);
        EXPECT_TRUE(th.Invoke(callback));
    }
    latch.Wait();

    EXPECT_TRUE(th.Stop());
    EXPECT_TRUE(th.Join());
}

}  // namespace byterpc

int main(int argc, char** argv) {
    testing::InitGoogleTest(&argc, argv);
    byterpc::InitOptions init_opt;
    BYTERPC_CHECK(0 == byterpc::ExecCtx::Init(init_opt));

    auto fini_guard = byte::defer([]() { byterpc::ExecCtx::Fini(); });
    return RUN_ALL_TESTS();
}
