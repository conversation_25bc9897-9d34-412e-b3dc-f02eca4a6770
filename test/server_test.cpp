// Copyright (c) 2021, ByteDance Inc. All rights reserved.

#include "byterpc/server.h"

#include <thread>

#include <byte/string/number.h>
#include <byte/util/defer.h>
#include <gtest/gtest.h>

#include "byterpc/exec_ctx.h"
#include "byterpc/util/logging.h"
#include "status.pb.h"
#ifdef BYTERPC_ENABLE_THRIFT
#include "byterpc/protocol/thrift/thrift_service.h"
#endif

namespace byterpc {

extern bool g_inited;

class StatusService : public status {
public:
    StatusService() {}

    void default_method(::google::protobuf::RpcController* controller,
                        const StatusRequest* request,
                        StatusResponse* response,
                        ::google::protobuf::Closure* done) override {}
};

void Test_StartServer(loop_type_t loop_type, bool use_http) {
    g_inited = false;
    Server server;
    StatusService status_service;
    ExecCtx ctx(loop_type);

    // Test: should not register repeated Service
    if (!use_http) {
        EXPECT_EQ(0, server.RegisterService(&status_service, SERVER_DOESNT_OWN_SERVICE));
        EXPECT_EQ(-1, server.RegisterService(&status_service, SERVER_DOESNT_OWN_SERVICE));
        EXPECT_EQ(Server::INIT, server.GetStatus());
    } else {
        EXPECT_EQ(0,
                  server.RegisterService(&status_service,
                                         SERVER_DOESNT_OWN_SERVICE,
                                         "/api/v1/daemon/echo     =>   default_method"));
        EXPECT_EQ(-1,
                  server.RegisterService(&status_service,
                                         SERVER_DOESNT_OWN_SERVICE,
                                         "/api/v1/daemon/echo     =>   default_method"));
        EXPECT_EQ(Server::INIT, server.GetStatus());
    }

    // Test: start failed if NOT invoke ExecCtx::Init
    ServerOptions options(true);
    if (!g_inited) {
        EXPECT_NE(0, server.Start("0.0.0.0:0", options));
    }

    // Test: should not listen repeatedly
    g_inited = true;
    EXPECT_EQ(0, server.Start("0.0.0.0:0", options));
    EXPECT_NE(0, server.Start("0.0.0.0:0", options));
    util::EndPoint listen_ep = server.listen_address();
    EXPECT_NE(0, listen_ep.port);
    EXPECT_EQ(-1, server.Start(util::endpoint2str(listen_ep).c_str(), options));
    EXPECT_EQ(Server::LISTENING, server.GetStatus());

    // Test: NOT specify any one transport
    Server server2;
    options._enable_ktcp = false;
    options._enable_utcp = false;
    EXPECT_EQ(-1, server2.Start("127.0.0.1:0", options));
    EXPECT_EQ(Server::INIT, server2.GetStatus());
    EXPECT_EQ(-1, server2.StopServer());

    // Test: should not enable kernel-tcp and userspace-tcp at the same time
    Server server3;
    options._enable_ktcp = true;
    options._enable_utcp = true;
    EXPECT_EQ(-1, server3.Start("127.0.0.1:0", options));
    EXPECT_EQ(Server::INIT, server3.GetStatus());
    EXPECT_EQ(-1, server3.StopServer());

    // Test: should not enable multiple transport for LOOP_UNTIL_QUIT mode
    if (loop_type == LOOP_UNTIL_QUIT) {
        Server server4;
        options._enable_ktcp = true;
        options._enable_utcp = false;
        options._enable_rdma = true;
        EXPECT_EQ(-1, server4.Start("127.0.0.1:0", options));
        EXPECT_EQ(Server::INIT, server4.GetStatus());
        EXPECT_EQ(-1, server4.StopServer());
    }

    // Test: each server should hold its own Service 
    StatusService status_service_a;
    StatusService status_service_b;
    Server server_a;
    Server server_b;
    EXPECT_NE(server_a.GetSvcReg(), server_b.GetSvcReg());
    EXPECT_EQ(0, server_a.RegisterService(&status_service_a, SERVER_DOESNT_OWN_SERVICE));
    EXPECT_EQ(0, server_b.RegisterService(&status_service_b, SERVER_DOESNT_OWN_SERVICE));

    // Test: start builtin server with same port
    Server server_c;
    ServerOptions opt_c(true);
    opt_c._enable_builtin_service = true;
    int base_port = 18888;
    while (1) {
        std::string addr = "0.0.0.0:" + byte::NumberToString(base_port++);
        if (server_c.Start(addr, opt_c) == 0) {
            break;
        }
    }
    EXPECT_EQ(Server::LISTENING, server_c.GetStatus());
    EXPECT_EQ(0, server_c.StopServer());

    // Test: start listen invalid port
    Server server_d;
    ServerOptions opt_d(true);
    EXPECT_EQ(-1, server_d.Start("0.0.0.0:65536", opt_d));
    EXPECT_EQ(-1, server_d.Start("0.0.0.0:-1", opt_d));
    EXPECT_EQ(-1, server_d.Start(65536, opt_d));
    EXPECT_EQ(-1, server_d.Start(-1, opt_d));
    EXPECT_EQ(Server::INIT, server_d.GetStatus());
    while (server_d.Start(base_port++, opt_d) != 0) {
    }
    EXPECT_EQ(Server::LISTENING, server_d.GetStatus());
    server_d.Reset();
    EXPECT_EQ(0, server_d.StopServer());
    EXPECT_EQ(Server::STOPPING, server_d.GetStatus());
    EXPECT_EQ(-1, server_d.StopServer());

    // Test: Can not register service after start started.
    Server server_e;
    StatusService status_service_e;
    EXPECT_EQ(0, server_e.RegisterService(&status_service_e, SERVER_DOESNT_OWN_SERVICE));
    while (server_e.Start(base_port++, opt_d) != 0) {
    }
    EXPECT_EQ(Server::LISTENING, server_e.GetStatus());  // start ok.
    EXPECT_EQ(-1, server_e.RegisterService(&status_service_e, SERVER_DOESNT_OWN_SERVICE));
    EXPECT_EQ(0, server_e.StopServer());
    // Even after server stopped, we can not allow register new service.
    // check SealRegistry's comments
    EXPECT_EQ(-1, server_e.RegisterService(&status_service_e, SERVER_DOESNT_OWN_SERVICE));
    ExecCtx::ResetThreadNode();
}

TEST(ServerTest, LOOP_UNTIL_QUIT) {
    Test_StartServer(LOOP_UNTIL_QUIT, false);

    Test_StartServer(LOOP_UNTIL_QUIT, true);
}

TEST(ServerTest, LOOP_IF_POSSIBLE) {
    Test_StartServer(LOOP_IF_POSSIBLE, false);

    Test_StartServer(LOOP_IF_POSSIBLE, true);
}

TEST(ServerTest, ServerStartRetry) {
    // Construct a case which server registers restful api but start failed,
    // in such condition server can restart successfully
    ExecCtx ctx(LOOP_IF_POSSIBLE);

    // TODO(fuziang): test SERVER_DOESNT_OWN_SERVICE
    ServerOptions options(true);
    StatusService* status_service = new StatusService;
    Server server;
    EXPECT_EQ(0,
              server.RegisterService(status_service,
                                     SERVER_DOESNT_OWN_SERVICE,
                                     "/api/v1/daemon/echo     =>   default_method"));
    // mock failure then invoking Server::Reset()
    EXPECT_EQ(-1, server.Start("0.0.0.0:188888", options));
    server.Reset();

    BYTERPC_LOG(INFO) << "=====new server";
    EXPECT_EQ(0,
              server.RegisterService(status_service,
                                     SERVER_DOESNT_OWN_SERVICE,
                                     "/api/v1/daemon/echo     =>   default_method"));
    EXPECT_EQ(0, server.Start("0.0.0.0:0", options));

    delete status_service;
    ExecCtx::ResetThreadNode();
}

void TEST_StartDummyServer(loop_type_t loop_type) {
    static bool stop_signal = false;
    static loop_type_t ltype = LOOP_UNKNOWN;

    stop_signal = false;
    ltype = loop_type;
    std::unique_ptr<std::thread> th(new std::thread([] {
        EXPECT_EQ(Server::StartDummyServer("0.0.0.0:65536", &stop_signal, ltype), -1);
        EXPECT_EQ(Server::StartDummyServer("0.0.0.0:0", &stop_signal, ltype), 0);
    }));

    sleep(2);
    stop_signal = true;

    th->join();
}

TEST(DummyServerTest, LOOP_UNTIL_QUIT) {
    TEST_StartDummyServer(LOOP_UNTIL_QUIT);
    ExecCtx::ResetThreadNode();
}

TEST(DummyServerTest, LOOP_IF_POSSIBLE) {
    TEST_StartDummyServer(LOOP_IF_POSSIBLE);
    ExecCtx::ResetThreadNode();
}

#ifdef BYTERPC_ENABLE_THRIFT
class ThriftTestService : public ThriftService {
public:
    void ProcessThriftFramedRequest(Controller* controller,
                                    ThriftFramedMessage* request,
                                    ThriftFramedMessage* response,
                                    ::google::protobuf::Closure* done) {}
};
TEST(ServerTest, ThriftServiceRegister) {
    ExecCtx ctx(LOOP_IF_POSSIBLE);
    Server svr;
    auto thrift_svc = std::make_unique<ThriftTestService>();
    EXPECT_EQ(0,
              svr.RegisterService(thrift_svc.get(), ServiceOwnership::SERVER_DOESNT_OWN_SERVICE));
    EXPECT_EQ(-1,
              svr.RegisterService(thrift_svc.get(), ServiceOwnership::SERVER_DOESNT_OWN_SERVICE));

    Server svr2;
    auto* thrift_svc2 = new ThriftTestService();
    EXPECT_EQ(0, svr2.RegisterService(thrift_svc2, ServiceOwnership::SERVER_OWNS_SERVICE));
}
#endif

}  // namespace byterpc

int main(int argc, char** argv) {
    testing::InitGoogleTest(&argc, argv);
    byterpc::InitOptions init_opt;
    BYTERPC_CHECK(0 == byterpc::ExecCtx::Init(init_opt));

    auto fini_guard = byte::defer([]() { byterpc::ExecCtx::Fini(); });
    return RUN_ALL_TESTS();
}
