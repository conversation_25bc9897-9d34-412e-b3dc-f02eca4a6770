// Copyright (c) 2021, ByteDance Inc. All rights reserved.
#include "rpc/protobuf_channel.h"

#include <google/protobuf/stubs/callback.h>
#include <gtest/gtest.h>

#include <memory>

#include "byterpc/exec_ctx.h"
#include "mock_channel_manager.h"

namespace byterpc {

TEST(ProtobufChannel, Basic) {
    InitOptions opt;
    ExecCtx::Init(opt);
    { ExecCtx ctx(LOOP_IF_POSSIBLE); }
    auto ev_reg = ExecCtx::GetOrNewThreadEventRegistry(TYPE_KERNEL_TCP);
    ProtobufChannel pb_channel(ev_reg, nullptr);
    util::EndPoint remote_endpoint;
    EXPECT_EQ(0, util::str2endpoint("127.0.0.1:8002", &remote_endpoint));
    util::EndPoint local_endpoint;
    EXPECT_EQ(0, util::str2endpoint("127.0.0.1:9002", &remote_endpoint));
    EXPECT_EQ(0, pb_channel.Init(remote_endpoint, Builder::ChannelOptions(), local_endpoint));
    EXPECT_EQ(ev_reg, pb_channel.GetEventRegistry());
    EXPECT_EQ(0, pb_channel.NumInvoked());
    EXPECT_EQ(remote_endpoint, pb_channel.ServerAddress());
    EXPECT_EQ(TYPE_KERNEL_TCP, pb_channel.GetTransportType());

    // CallMethod
    struct Closure : public google::protobuf::Closure {
        void Run() override {}
    };
    Closure closure;
    std::unique_ptr<Controller> cntl(new SimpleController());
    pb_channel.CallMethod(nullptr, cntl.get(), nullptr, nullptr, &closure);
    EXPECT_EQ(1, pb_channel.NumInvoked());
    EXPECT_EQ(util::EndPoint(), cntl->local_side());
    EXPECT_EQ(util::EndPoint(), cntl->remote_side());

    // ResetTransport
    pb_channel.ResetTransport(ETIMEDOUT);
}

}  // namespace byterpc
