// Copyright (c) 2021, ByteDance Inc. All rights reserved.

#include "iobuf/io_block_pool.h"

#include <byte/util/defer.h>
#include <gtest/gtest.h>

#include <atomic>
#include <memory>
#include <thread>
#include <vector>

#include "byterpc/byterpc_flags.h"
#include "byterpc/exec_ctx.h"
#include "byterpc/util/logging.h"

namespace byterpc {

TEST(IOBlockPoolTest, Basic) {
    static int index = 1;
    std::vector<IOBlock*> blks;
    const size_t blk_size = 512 * 1024UL;
    // alloc 100 ioblock through IOBlock::Create
    for (int i = 0; i < 100; ++i) {
        IOBlock* blk = alloc_ioblock_resource<blk_size>();
        EXPECT_NE(blk, nullptr);
        blks.push_back(blk);
    }

    // recycle 100 ioblock to pool
    for (int i = 0; i < 100; ++i) {
        IOBlock* blk = blks.back();
        blks.pop_back();
        blk->dec_ref();
    }
    if (index == 1) {
        EXPECT_EQ(100, IOBlockTlsPool<blk_size>::GetBlockNum());
    }

    // alloc 300 ioblock, the first 100 IOBlock will get from pool,
    // the resident will creat IOBlock
    for (int i = 0; i < 300; ++i) {
        IOBlock* blk = alloc_ioblock_resource<blk_size>();
        EXPECT_NE(blk, nullptr);
        blks.push_back(blk);
    }
    if (index == 1) {
        EXPECT_EQ(0, IOBlockTlsPool<blk_size>::GetBlockNum());
    }

    // recycle 300 ioblock to pool
    for (int i = 0; i < 300; ++i) {
        IOBlock* blk = blks.back();
        blks.pop_back();
        blk->dec_ref();
    }
    if (index == 1) {
        EXPECT_EQ(300, IOBlockTlsPool<blk_size>::GetBlockNum());
    }

    // test get IOBlock with MemoryPool use out, and will create by malloc.
    // return IOBlock number will larger than MAX_IOBLOCK_NUM_OF_TLS_POOL
    for (int i = 0; i < 2100; ++i) {
        IOBlock* blk = alloc_ioblock_resource<blk_size>();
        EXPECT_NE(nullptr, blk);
        blks.push_back(blk);
    }
    for (int i = 0; i < 2100; ++i) {
        IOBlock* blk = blks[i];
        blk->dec_ref();
    }
    blks.clear();

    // test return IOBlock after IOBlockPool exit
    for (int i = 0; i < 1100; ++i) {
        IOBlock* blk = alloc_ioblock_resource<blk_size>();
        EXPECT_NE(nullptr, blk);
        blks.push_back(blk);
    }
    IOBlockTlsPool<blk_size>::g_ioblock_pool_shutdown = true;
    for (int i = 0; i < 1100; ++i) {
        IOBlock* blk = blks.back();
        blks.pop_back();
        blk->dec_ref();
    }
    IOBlockTlsPool<blk_size>::g_ioblock_pool_shutdown = false;
    ++index;
}

}  // namespace byterpc

int main(int argc, char** argv) {
    testing::InitGoogleTest(&argc, argv);
#ifdef BYTERPC_WITH_NUMA
    byterpc::FLAGS_byterpc_enable_numa_aware = true;
    byterpc::FLAGS_byterpc_memory_pool_numa_init_region_num = "1,1";
    byterpc::FLAGS_byterpc_byte_express_memory_pool_max_regions = 2;
#else
    byterpc::FLAGS_byterpc_byte_express_memory_pool_init_regions = 1;
    byterpc::FLAGS_byterpc_byte_express_memory_pool_max_regions = 1;
#endif
    byterpc::InitOptions init_opt;
#ifdef BYTERPC_ENABLE_BYTE_EXPRESS
    init_opt._init_rdma = true;
#endif
    BYTERPC_CHECK(0 == byterpc::ExecCtx::Init(init_opt));

    auto fini_guard = byte::defer([]() { byterpc::ExecCtx::Fini(); });
    return RUN_ALL_TESTS();
}
