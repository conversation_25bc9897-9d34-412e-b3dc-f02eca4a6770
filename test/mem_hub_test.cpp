// Copyright (c) 2023, ByteDance Inc. All rights reserved.
#include <gtest/gtest.h>

#include "mem/hub.h"

#include <algorithm>
#include <memory>
#include <thread>
#include <vector>

#include "byterpc/exec_ctx.h"

namespace byterpc {

namespace {
class FakeDMAObserver : public PageObserver {
public:
    FakeDMAObserver(uint32_t reg_cost_ms) : reg_cost_ms(reg_cost_ms), total_cnt(0) {}
    int RegExtMemPage(const MemPageInfo& page) override {
        if (reg_cost_ms > 0) {
            std::this_thread::sleep_for(std::chrono::milliseconds(reg_cost_ms));
        }
        std::lock_guard<std::mutex> lock(mutex);
        ++reg_pages_cnt[page];
        ++total_cnt;
        return 0;
    }

    int UnregExtMemPage(const MemPageInfo& page) override {
        std::lock_guard<std::mutex> lock(mutex);
        if (reg_pages_cnt[page] == 0) {
            return -1;
        }
        --reg_pages_cnt[page];
        --total_cnt;
        return 0;
    }

    std::string GetObserverTransportType() override {
        return "TYPE_KERNEL_TCP";
    }

private:
    struct cmp {
        bool operator()(const MemPageInfo& a, const MemPageInfo& b) const {
            return a.vaddr < b.vaddr;
        }
    };

private:
    uint32_t reg_cost_ms;
    std::map<MemPageInfo, uint64_t, cmp> reg_pages_cnt;
    uint64_t total_cnt;
    std::mutex mutex;
};

}  // namespace

TEST(MemHubTest, Basic) {
    MemPageRegHub test_hub;
    FakeDMAObserver o1(0), o2(0);
    test_hub.AddPageObserver(&o1);
    test_hub.AddPageObserver(&o2);
    EXPECT_EQ(2, test_hub._observers.size());
    MemPageInfo p1;
    p1.hold_type = HolderType::Default;
    p1.vaddr = malloc(4096);
    p1.len = 4096;
    p1.page_sz = 4096;
    p1.nr_page = 1;
    // 1. test register and unregister one page
    EXPECT_EQ(0, test_hub.SubmitHoldPages({p1}));
    EXPECT_EQ(1, test_hub.GetRegisterPageSize());
    EXPECT_EQ(0, test_hub.DeleteHoldPages(p1));
    EXPECT_EQ(0, test_hub.GetRegisterPageSize());

    // 2. test register page again after unregister it
    EXPECT_EQ(0, test_hub.SubmitHoldPages({p1}));
    EXPECT_EQ(1, test_hub.GetRegisterPageSize());
    EXPECT_EQ(0, test_hub.DeleteHoldPages(p1));
    EXPECT_EQ(0, test_hub.GetRegisterPageSize());

    // 3. test register&unregister the same page twice
    EXPECT_EQ(0, test_hub.SubmitHoldPages({p1}));
    EXPECT_EQ(-1, test_hub.SubmitHoldPages({p1}));
    EXPECT_EQ(1, test_hub.GetRegisterPageSize());
    EXPECT_EQ(0, test_hub.DeleteHoldPages(p1));
    EXPECT_EQ(-1, test_hub.DeleteHoldPages(p1));

    // 4. test register&unregister the same vaddr and len but with different HolderType
    MemPageInfo p2;
    p2.hold_type = HolderType::UserNormal;
    p2.vaddr = p1.vaddr;
    p2.len = 4096;
    p2.page_sz = 4096;
    p2.nr_page = 1;
    EXPECT_EQ(0, test_hub.SubmitHoldPages({p1}));
    EXPECT_EQ(-1, test_hub.SubmitHoldPages({p2}));
    EXPECT_EQ(1, test_hub.GetRegisterPageSize());
    EXPECT_EQ(-1, test_hub.DeleteHoldPages(p2));
    EXPECT_EQ(0, test_hub.DeleteHoldPages(p1));
    EXPECT_EQ(0, test_hub.GetRegisterPageSize());

    // 5. test register page with start vaddr within already registered page
    // already register page:[p1.vaddr, p1.vaddr + 4096),
    // test page:[p1.vaddr + 1024, p1.vaddr + 1024 + 4096)
    EXPECT_EQ(0, test_hub.SubmitHoldPages({p1}));
    p2.vaddr = static_cast<uint8_t*>(p1.vaddr) + 1024;
    EXPECT_EQ(-1, test_hub.SubmitHoldPages({p2}));
    EXPECT_EQ(1, test_hub.GetRegisterPageSize());
    EXPECT_EQ(0, test_hub.DeleteHoldPages(p1));

    // 6. test register page with end vaddr within already registered page
    // already register page:[p1.vaddr, p1.vaddr + 4096)
    // test page:[p1.vaddr - 1024, p1.vaddr - 1024 + 4096)
    EXPECT_EQ(0, test_hub.SubmitHoldPages({p1}));
    p2.vaddr = static_cast<uint8_t*>(p1.vaddr) - 1024;
    EXPECT_EQ(-1, test_hub.SubmitHoldPages({p2}));
    EXPECT_EQ(1, test_hub.GetRegisterPageSize());
    EXPECT_EQ(0, test_hub.DeleteHoldPages(p1));

    // 7. test start vaddr is the end vaddr of registered page
    // already register page:[p1.vaddr, p1.vaddr + 4096)
    // test page:[p1.vaddr + 4096, p1.vaddr + 4096 + 4096)
    EXPECT_EQ(0, test_hub.SubmitHoldPages({p1}));
    p2.vaddr = static_cast<uint8_t*>(p1.vaddr) + 4096;
    EXPECT_EQ(0, test_hub.SubmitHoldPages({p2}));
    EXPECT_EQ(2, test_hub.GetRegisterPageSize());
    EXPECT_EQ(0, test_hub.DeleteHoldPages(p1));
    EXPECT_EQ(0, test_hub.DeleteHoldPages(p2));

    // 8. test end vaddr is the start vaddr of registered page
    // already register page:[p1.vaddr, p1.vaddr + 4096)
    // test page:[p1.vaddr - 4096, p1.vaddr)
    EXPECT_EQ(0, test_hub.SubmitHoldPages({p1}));
    p2.vaddr = static_cast<uint8_t*>(p1.vaddr) - 4096;
    EXPECT_EQ(0, test_hub.SubmitHoldPages({p2}));
    EXPECT_EQ(2, test_hub.GetRegisterPageSize());
    EXPECT_EQ(0, test_hub.DeleteHoldPages(p1));
    EXPECT_EQ(0, test_hub.DeleteHoldPages(p2));

    // 9. test start vaddr is valid, but end vaddr is cross already registered page
    // alread register page:[p1.vaddr, p1.vaddr + 4096)
    // test page:[p1.vaddr - 4096, p1.vaddr + 8192)
    EXPECT_EQ(0, test_hub.SubmitHoldPages({p1}));
    p2.vaddr = static_cast<uint8_t*>(p1.vaddr) - 4096;
    p2.len = 12288;
    p2.nr_page = 3;
    EXPECT_EQ(-1, test_hub.SubmitHoldPages({p2}));
    EXPECT_EQ(1, test_hub.GetRegisterPageSize());
    EXPECT_EQ(0, test_hub.DeleteHoldPages(p1));
    EXPECT_EQ(0, test_hub.GetRegisterPageSize());

    test_hub._observers.clear();
    free(p1.vaddr);
}

static void AddPage(MemPageRegHub* test_hub) {
    MemPageInfo p;
    p.vaddr = malloc(4096);
    p.len = 4096;
    p.hold_type = HolderType::UserNormal;
    p.page_sz = 4096;
    p.nr_page = 1;
    EXPECT_EQ(0, test_hub->SubmitHoldPages({p}));
}

static void RemovePage(MemPageRegHub* test_hub, const MemPageInfo& page) {
    EXPECT_EQ(0, test_hub->DeleteHoldPages(page));
    free(page.vaddr);
}

TEST(MemHubTest, MultiThreadAddPages) {
    MemPageRegHub test_hub;
    FakeDMAObserver o1(0), o2(0), o3(0);
    test_hub.AddPageObserver(&o1);
    test_hub.AddPageObserver(&o2);
    test_hub.AddPageObserver(&o3);
    EXPECT_EQ(3, test_hub._observers.size());

    int kNum = 5;
    std::vector<std::unique_ptr<std::thread>> add_pids;
    for (int i = 0; i < kNum; ++i) {
        std::unique_ptr<std::thread> pid(new std::thread(AddPage, &test_hub));
        add_pids.push_back(std::move(pid));
    }
    for (int i = 0; i < kNum; ++i) {
        add_pids[i]->join();
    }
    EXPECT_EQ(kNum, test_hub.GetRegisterPageSize());
    std::vector<MemPageInfo> pgs;
    for (auto iter = test_hub._pages.begin(); iter != test_hub._pages.end(); ++iter) {
        pgs.push_back(iter->second);
    }
    std::vector<std::unique_ptr<std::thread>> remove_pids;
    for (int i = 0; i < kNum; ++i) {
        std::unique_ptr<std::thread> pid(new std::thread(RemovePage, &test_hub, pgs[i]));
        remove_pids.push_back(std::move(pid));
    }
    for (int i = 0; i < kNum; ++i) {
        remove_pids[i]->join();
    }
    EXPECT_EQ(0, test_hub.GetRegisterPageSize());
}

TEST(MemHubTest, MultiThreadAddSamePages) {
    MemPageRegHub test_hub;
    FakeDMAObserver o1(10), o2(10), o3(10);
    test_hub.AddPageObserver(&o1);
    test_hub.AddPageObserver(&o2);
    test_hub.AddPageObserver(&o3);
    EXPECT_EQ(3, test_hub._observers.size());

    uint32_t thread_num = 10;
    std::vector<std::unique_ptr<std::thread>> threads;

    std::vector<std::vector<MemPageInfo>> pages_list;
    uint32_t v1 = 10;
    uint32_t v2 = 15;
    for (uint32_t i = 0; i < v1; ++i) {
        std::vector<MemPageInfo> pages;
        for (uint32_t j = 0; j < v2; ++j) {
            MemPageInfo p;
            p.vaddr = malloc(4096);
            p.len = 4096;
            p.hold_type = HolderType::UserNormal;
            p.page_sz = 4096;
            p.nr_page = 1;
            pages.push_back(p);
        }
        pages_list.push_back(pages);
    }

    std::atomic<uint32_t> success_num(0);
    for (uint32_t n = 0; n < thread_num; ++n) {
        threads.push_back(std::unique_ptr<std::thread>(new std::thread([&]() {
            for (uint32_t i = 0; i < v1; ++i) {
                int result = test_hub.SubmitHoldPages(pages_list[i]);
                if (result == 0) {
                    success_num.fetch_add(1, std::memory_order_relaxed);
                }
            }
        })));
    }

    for (uint32_t i = 0; i < thread_num; ++i) {
        threads[i]->join();
    }
    threads.clear();

    EXPECT_EQ(success_num.load(std::memory_order_relaxed), v1);
    EXPECT_EQ(test_hub._pages.size(), v1 * v2);
    EXPECT_EQ(o1.total_cnt, v1 * v2);

    success_num.store(0, std::memory_order_relaxed);
    for (uint32_t n = 0; n < thread_num; ++n) {
        threads.push_back(std::unique_ptr<std::thread>(new std::thread([&]() {
            for (uint32_t i = 0; i < v1; ++i) {
                for (uint32_t j = 0; j < v2; ++j) {
                    int result = test_hub.DeleteHoldPages(pages_list[i][j]);
                    if (result == 0) {
                        success_num.fetch_add(1, std::memory_order_relaxed);
                    }
                }
            }
        })));
    }

    for (uint32_t i = 0; i < thread_num; ++i) {
        threads[i]->join();
    }
    threads.clear();

    EXPECT_EQ(success_num.load(std::memory_order_relaxed), v1 * v2);
    EXPECT_EQ(test_hub._pages.size(), 0U);
    EXPECT_EQ(o1.total_cnt, 0U);

    for (uint32_t i = 0; i < v1; ++i) {
        for (uint32_t j = 0; j < v2; ++j) {
            free(pages_list[i][j].vaddr);
        }
    }
}

TEST(MemHubTest, GetAllMemPages) {
    MemPageRegHub test_hub;
    for (size_t i = 1; i < 10; ++i) {
        AddPage(&test_hub);
        ASSERT_EQ(test_hub.GetAllMemPages().size(), i);
    }

    std::vector<MemPageInfo> pages = test_hub.GetAllMemPages();
    for (const MemPageInfo& page : pages) {
        RemovePage(&test_hub, page);
    }

    ASSERT_EQ(test_hub.GetAllMemPages().size(), 0);
}

}  // namespace byterpc
