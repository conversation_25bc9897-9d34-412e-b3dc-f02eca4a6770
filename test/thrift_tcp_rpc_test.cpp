// Copyright (c) 2024, ByteDance Inc. All rights reserved.

#include "byterpc/rpc.h"

#include <byte/util/defer.h>
#include <gtest/gtest.h>

#include <string>
#include <thread>
#include <vector>

#include "byterpc/builder.h"
#include "byterpc/byterpc_flags.h"
#include "byterpc/callback.h"
#include "byterpc/controller.h"
#include "byterpc/exec_ctx.h"
#include "byterpc/protocol/thrift/thrift_message.h"
#include "byterpc/protocol/thrift/thrift_service.h"
#include "byterpc/server.h"
#include "byterpc/util/closure_guard.h"
#include "byterpc/util/logging.h"
#include "echo_types.h"
#include "status.pb.h"

namespace byterpc {

namespace {
int g_client_rpc = 0;
int g_server_rpc = 0;
std::string g_proxy_server_addr{};
}  // namespace

class MyStatusService : public status {
public:
    MyStatusService(int rpc_num, int sleep_us = 0, Server* server = nullptr)
        : _rpc_num(rpc_num), _sleep_us(sleep_us) {}

    void default_method(::google::protobuf::RpcController* controller,
                        const StatusRequest* request,
                        StatusResponse* response,
                        ::google::protobuf::Closure* done) override {
        if (_sleep_us) {
            usleep(_sleep_us);
        }

        Controller* cntl = static_cast<Controller*>(controller);
        if (cntl->HasIncomingAttachment()) {
            IOBuf* buf = cntl->ReleaseIncomingAttachment();
            std::unique_ptr<IOBuf> att = std::make_unique<IOBuf>();
            att->append(*buf);
            cntl->InstallOutgoingAttachment(std::move(att));
            delete buf;
        }

        { util::ClosureGuard done_guard(done); }

        ++g_server_rpc;
        if (g_server_rpc == _rpc_num) {
            ExecCtx::QuitLoop();
        }
    }

private:
    int _rpc_num;
    int _sleep_us;
};

static void StatusCallback(StatusRequest* req,
                           StatusResponse* resp,
                           Controller* cntl,
                           bool* finished,
                           int expect_err,
                           int expect_rpc_num) {
    std::unique_ptr<StatusRequest> req_guard(req);
    std::unique_ptr<StatusResponse> resp_guard(resp);
    // EEOF and ECONNRESET are equivalent
    if (expect_err == EEOF || expect_err == ECONNRESET) {
        EXPECT_TRUE((cntl->ErrorCode() == EEOF) || (cntl->ErrorCode() == ECONNRESET))
            << cntl->ErrorCode();
    } else {
        EXPECT_EQ(cntl->ErrorCode(), expect_err);
    }
    ++g_client_rpc;
    if (g_client_rpc == expect_rpc_num) {
        ExecCtx::QuitLoop();
    }
    if (finished) {
        *finished = true;
    }
}

static void HandleProxyResponse(byterpc::Controller* cntl,
                                ::google::protobuf::Closure* origin_done,
                                int rpc_num) {
    if (cntl->Failed()) {
        BYTERPC_LOG(INFO) << "HandleProxyResponse done run, cntl->Errno: " << cntl->ErrorCode()
                          << ", errmsg:" << cntl->ErrorText();
    } else {
        BYTERPC_LOG(INFO) << "HandleProxyResponse done run, cntl->Errno: " << cntl->ErrorCode();
    }
    origin_done->Run();
    ++g_server_rpc;
    if (g_server_rpc == rpc_num) {
        ExecCtx::QuitLoop();
    }
}

class MyEchoService : public byterpc::ThriftService {
public:
    MyEchoService(int rpc_num, int sleep_us = 0) : _rpc_num(rpc_num), _sleep_us(sleep_us) {}
    void ProcessThriftFramedRequest(byterpc::Controller* cntl,
                                    byterpc::ThriftFramedMessage* request,
                                    byterpc::ThriftFramedMessage* response,
                                    ::google::protobuf::Closure* done) override {
        // For timeout case
        if (_sleep_us) {
            usleep(_sleep_us);
        }

        const std::string& method_name = cntl->ThriftMethodName();
        if (method_name == "Echo") {
            // "Echo" is served as a proxy of "RealEcho".
            // Proxy request/response to RealEcho, note that as a proxy we
            // don't need to Cast the messages to native types.
            _channel = _builder.BuildChannel(g_proxy_server_addr);
            ASSERT_NE(nullptr, _channel.get());
            byterpc::Controller* cntl =
                _builder.CreateSessionController(byterpc::PROTOCOL_THRIFT, 10000000);
            ASSERT_NE(nullptr, cntl);
            byterpc::ThriftStub stub(_channel);
            // NOTE: We DO NOT do Cast<> here, but relay the req & resp instead.
            // And once the proxy request is done, we call the original done to client.
            google::protobuf::Closure* proxy_done =
                byterpc::NewCallback(HandleProxyResponse, cntl, done, _rpc_num);
            stub.CallMethod("RealEcho", cntl, request, response, proxy_done);
        } else if (method_name == "RealEcho") {
            return RealEcho(cntl,
                            request->Cast<example::EchoRequest>(),
                            response->Cast<example::EchoResponse>(),
                            done);
        } else {
            cntl->SetFailed(byterpc::ENOMETHOD,
                            "request a non-existent thrift method: %s",
                            method_name.c_str());
            done->Run();
            return;
        }
    }

    void RealEcho(byterpc::Controller* cntl,
                  const example::EchoRequest* req,
                  example::EchoResponse* res,
                  google::protobuf::Closure* done) {
        byterpc::util::ClosureGuard done_guard(done);
        res->data = req->data + " (RealEcho)";

        // Test when server should exit
        ++g_server_rpc;
        if (g_server_rpc == _rpc_num) {
            ExecCtx::QuitLoop();
        }
    }

private:
    byterpc::Builder _builder;
    std::shared_ptr<byterpc::Builder::Channel> _channel;
    // expected rpc number, server will exit when it's sufficed
    int _rpc_num;
    std::string _listen_addr;
    int _sleep_us;
};

static void ThriftEchoCallback(example::EchoRequest* req,
                               example::EchoResponse* resp,
                               Controller* cntl,
                               bool* finished,
                               int expect_err,
                               int expect_rpc_num) {
    std::unique_ptr<example::EchoRequest> req_guard(req);
    std::unique_ptr<example::EchoResponse> resp_guard(resp);
    EXPECT_EQ(cntl->ErrorCode(), expect_err);
    ++g_client_rpc;
    if (g_client_rpc == expect_rpc_num) {
        ExecCtx::QuitLoop();
    }
    if (finished) {
        *finished = true;
    }
}

static void SendRequest(const std::string& server_addr,
                        ProtocolType protocol,
                        int expect_err,
                        int expect_rpc_num,
                        bool* finished = nullptr,
                        std::string thrift_method_name = "") {
    Builder builder;
    Builder::ChannelOptions options;
    options._connect_timeout_ms = 20000;
    options._rpc_timeout_ms = 10000;
    util::EndPoint server_ep;
    util::str2endpoint(server_addr.c_str(), &server_ep);
    std::shared_ptr<Builder::Channel> channel = builder.BuildChannel(server_ep, options);
    Controller* cntl = builder.CreateSessionController(protocol, 10000000);
    EXPECT_NE(nullptr, channel.get());
    EXPECT_NE(nullptr, cntl);

    // non thrift protocol
    if (protocol != PROTOCOL_THRIFT) {
        auto* req = new StatusRequest;
        auto* resp = new StatusResponse;
        std::vector<char> ch(128, 'a');
        std::unique_ptr<IOBuf> buf = std::make_unique<IOBuf>();
        buf->append(ch.data(), 128);
        cntl->InstallOutgoingAttachment(std::move(buf));

        auto done =
            byterpc::NewCallback<StatusRequest*, StatusResponse*, Controller*, bool*, int, int>(
                StatusCallback, req, resp, cntl, finished, expect_err, expect_rpc_num);
        status_Stub stub(channel.get());
        stub.default_method(cntl, req, resp, done);
    } else {
        example::EchoRequest* req = new example::EchoRequest;
        example::EchoResponse* resp = new example::EchoResponse();
        req->__set_data(std::string(128, 'a'));
        byterpc::ThriftStub stub(channel);
        auto done = byterpc::NewCallback<example::EchoRequest*,
                                         example::EchoResponse*,
                                         Controller*,
                                         bool*,
                                         int,
                                         int>(
            ThriftEchoCallback, req, resp, cntl, finished, expect_err, expect_rpc_num);
        stub.CallMethod(thrift_method_name.c_str(), cntl, req, resp, done);
    }
}

static void WaitFlagReady(std::atomic_bool* ready_flag) {
    while (!ready_flag->load(std::memory_order_acquire))
        usleep(1);
    ready_flag->store(false, std::memory_order_release);
}

TEST(ThriftTcpRPCTest, RequestWithSomeError) {
    std::atomic_bool ready_flag(false);
    Server* svr = nullptr;
    g_client_rpc = 0;
    g_server_rpc = 0;
    int num = 10;

    std::thread server([&]() {
        ExecCtx ctx(LOOP_UNTIL_QUIT);
        svr = new Server();
        svr->RegisterService(new MyEchoService(num), SERVER_OWNS_SERVICE);

        ServerOptions options;
        options._enable_ktcp = true;
        EXPECT_EQ(0, svr->Start("0.0.0.0:0", options));
        ready_flag.store(true, std::memory_order_release);
        ExecCtx::LoopUntilQuit();
    });
    WaitFlagReady(&ready_flag);

    std::thread client([&]() {
        ExecCtx ctx(LOOP_UNTIL_QUIT);
        std::string ip = util::endpoint2str(svr->listen_address()).c_str();
        g_proxy_server_addr = ip;
        for (int i = 0; i < num; ++i) {
            // Echo will be relayed to RealEcho
            if (i % 2 == 0) {
                SendRequest(ip, PROTOCOL_THRIFT, 0, num, nullptr, "Echo");
            } else {
                SendRequest(ip, PROTOCOL_THRIFT, ENOMETHOD, num, nullptr, "NO_Echo");
            }
        }
        ExecCtx::LoopUntilQuit();
    });

    client.join();
    server.join();

    delete svr;
}

TEST(ThriftTcpRPCTest, ThriftProxyMode) {
    std::atomic_bool ready_flag(false);
    Server* svr = nullptr;
    g_client_rpc = 0;
    g_server_rpc = 0;
    int num = 10;

    std::thread server([&]() {
        ExecCtx ctx(LOOP_UNTIL_QUIT);
        svr = new Server();
        svr->RegisterService(new MyEchoService(num * 2), SERVER_OWNS_SERVICE);

        ServerOptions options;
        options._enable_ktcp = true;
        EXPECT_EQ(0, svr->Start("0.0.0.0:0", options));
        ready_flag.store(true, std::memory_order_release);
        ExecCtx::LoopUntilQuit();
    });
    WaitFlagReady(&ready_flag);

    std::thread client([&]() {
        ExecCtx ctx(LOOP_UNTIL_QUIT);
        std::string ip = util::endpoint2str(svr->listen_address()).c_str();
        g_proxy_server_addr = ip;
        for (int i = 0; i < num; ++i) {
            // Echo will be relayed to RealEcho
            SendRequest(ip, PROTOCOL_THRIFT, 0, num, nullptr, "Echo");
        }
        ExecCtx::LoopUntilQuit();
    });

    client.join();
    server.join();

    delete svr;
}

#ifdef BYTERPC_ENABLE_BYTE_STD
TEST(ThriftTcpRPCTest, ThriftMultipleProtocol) {
    std::atomic_bool ready_flag(false);
    Server* svr = nullptr;
    g_client_rpc = 0;
    g_server_rpc = 0;
    int num = 10;

    std::thread server([&]() {
        ExecCtx ctx(LOOP_UNTIL_QUIT);
        svr = new Server();
        svr->RegisterService(new MyStatusService(num), SERVER_OWNS_SERVICE);
        // register thrift service
        svr->RegisterService(new MyEchoService(num), SERVER_OWNS_SERVICE);

        ServerOptions options;
        options._enable_ktcp = true;
        EXPECT_EQ(0, svr->Start("0.0.0.0:0", options));
        ready_flag.store(true, std::memory_order_release);
        ExecCtx::LoopUntilQuit();
    });
    WaitFlagReady(&ready_flag);

    std::thread client([&]() {
        ExecCtx ctx(LOOP_UNTIL_QUIT);
        std::string ip = util::endpoint2str(svr->listen_address()).c_str();
        for (int i = 0; i < num; ++i) {
            ProtocolType type = PROTOCOL_BYTE_STD;
            if (i % 2 == 0) {
                type = PROTOCOL_BYTE_STD;
                SendRequest(ip, type, 0, num);
            } else {
                type = PROTOCOL_THRIFT;
                SendRequest(ip, type, 0, num, nullptr, "RealEcho");
            }
        }
        ExecCtx::LoopUntilQuit();
    });

    client.join();
    server.join();

    delete svr;
}
#endif

TEST(ThriftTcpRPCTest, RequestTimeout) {
    std::atomic_bool ready_flag(false);
    Server* svr = nullptr;
    g_client_rpc = 0;
    g_server_rpc = 0;

    std::thread server([&]() {
        ExecCtx ctx(LOOP_UNTIL_QUIT);
        svr = new Server();
        // register thrift service
        svr->RegisterService(new MyEchoService(1, 15000000), SERVER_OWNS_SERVICE);
        ServerOptions options;
        options._enable_ktcp = true;
        EXPECT_EQ(0, svr->Start("0.0.0.0:0", options));
        ready_flag.store(true, std::memory_order_release);
        ExecCtx::LoopUntilQuit();
    });
    WaitFlagReady(&ready_flag);

    int rpc_num = 1;
    std::thread client([&]() {
        ExecCtx ctx(LOOP_UNTIL_QUIT);
        std::string ip = util::endpoint2str(svr->listen_address()).c_str();
        SendRequest(ip, PROTOCOL_THRIFT, ERPCTIMEDOUT, rpc_num, nullptr, "RealEcho");
        ExecCtx::LoopUntilQuit();
    });

    client.join();
    server.join();

    delete svr;
}

}  // namespace byterpc

int main(int argc, char** argv) {
    testing::InitGoogleTest(&argc, argv);
#ifdef BYTERPC_WITH_NUMA
    byterpc::FLAGS_byterpc_enable_numa_aware = true;
    byterpc::FLAGS_byterpc_memory_pool_numa_init_region_num = "1,1";
#endif
    byterpc::InitOptions init_opt;
    BYTERPC_CHECK(0 == byterpc::ExecCtx::Init(init_opt));

    auto fini_guard = byte::defer([]() { byterpc::ExecCtx::Fini(); });
    return RUN_ALL_TESTS();
}
