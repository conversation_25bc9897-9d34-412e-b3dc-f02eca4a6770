// Copyright (c) 2024, ByteDance Inc. All rights reserved.

#include <gtest/gtest.h>

#include <byte/util/defer.h>
#include <random>

#include <memory>
#include <string>
#include <thread>
#include <vector>

#include "byterpc/exec_ctx.h"
#include "protocol/extend_header.h"

#include "byterpc/util/logging.h"

namespace byterpc {

TEST(ExtendHeaderTest, RawExtendHeaderABI) {
    RawExtendHeader hdr;
    memcpy(hdr.magic, "ERPC", sizeof(hdr.magic));
    hdr.ver = static_cast<uint32_t>(ExtendVer::VER_ONE);
    hdr.crc_mode = 0x01; /* TYPE_FULL */
    hdr.crc_value = 0x12345678;
    EXPECT_EQ(12, sizeof(hdr));

    auto rbuf_pipe = std::make_unique<IORbufPipe>();
    IOBuf buf;
    buf.append(reinterpret_cast<char*>(&hdr), sizeof(hdr));
    buf.cut_to(rbuf_pipe.get(), buf.length());

    ExtendHeader res;
    EXPECT_EQ(PARSE_OK, ExtendHeaderSerDes::MatchExtendHeader(rbuf_pipe.get()));
    EXPECT_EQ(PARSE_OK, ExtendHeaderSerDes::ParseHeader(rbuf_pipe.get(), &res));
    EXPECT_EQ(ExtendVer::VER_ONE, res.ver);
    EXPECT_EQ(CrcMode::TYPE_FULL, res.crc_mode);
    EXPECT_EQ(0x78563412, res.crc_value); /* It goes through endian transfer */
}

TEST(ExtendHeaderTest, PackHeader) {
    char data[ExtendHeaderSerDes::FIXED_HEADER_LENGTH];
    uint32_t crc_value = 0x12345678;
    CrcMode crc_mode = CrcMode::TYPE_META_PB;
    ExtendHeaderSerDes::PackHeader(data, crc_mode, crc_value);

    auto rbuf_pipe = std::make_unique<IORbufPipe>();
    IOBuf buf;
    buf.append(data, sizeof(data));
    buf.cut_to(rbuf_pipe.get(), buf.length());

    ExtendHeader res;
    EXPECT_EQ(PARSE_OK, ExtendHeaderSerDes::MatchExtendHeader(rbuf_pipe.get()));
    EXPECT_EQ(PARSE_OK, ExtendHeaderSerDes::ParseHeader(rbuf_pipe.get(), &res));
    EXPECT_EQ(ExtendVer::VER_ONE, res.ver);
    EXPECT_EQ(crc_mode, res.crc_mode);
    EXPECT_EQ(crc_value, res.crc_value);
}

TEST(ExtendHeaderTest, ParseHeaderCornerCase) {
    auto rbuf_pipe = std::make_unique<IORbufPipe>();
    IOBuf buf;

    // Case 1: Wrong data
    char data[ExtendHeaderSerDes::FIXED_HEADER_LENGTH];
    memset(data, 'x', sizeof(data));
    buf.append(data, sizeof(data));
    buf.cut_to(rbuf_pipe.get(), buf.length());
    ExtendHeader res;
    EXPECT_EQ(PARSE_ERROR_TRY_OTHERS, ExtendHeaderSerDes::MatchExtendHeader(rbuf_pipe.get()));
    rbuf_pipe->clear();
    buf.clear();

    // Case 2: Data not enough
    uint32_t crc_value = 0x12345678;
    CrcMode crc_mode = CrcMode::TYPE_META_PB;
    ExtendHeaderSerDes::PackHeader(data, crc_mode, crc_value);
    char short_data[ExtendHeaderSerDes::FIXED_HEADER_LENGTH - 2];
    memcpy(short_data, data, sizeof(short_data));
    buf.append(short_data, sizeof(short_data));
    buf.cut_to(rbuf_pipe.get(), buf.length());
    EXPECT_EQ(PARSE_ERROR_NOT_ENOUGH_DATA, ExtendHeaderSerDes::MatchExtendHeader(rbuf_pipe.get()));
    rbuf_pipe->clear();
    buf.clear();

    // Case 3: Fast path
    auto ref = IOBlockRef::CreateV2(ExtendHeaderSerDes::FIXED_HEADER_LENGTH);
    crc_value = 0x12345678;
    crc_mode = CrcMode::TYPE_META_PB;
    // subsequent 12B of ref.data() should be valid, or it may raise UB
    ExtendHeaderSerDes::PackHeader(ref.data(), crc_mode, crc_value);
    rbuf_pipe->append(ref);
    EXPECT_EQ(PARSE_OK, ExtendHeaderSerDes::MatchExtendHeader(rbuf_pipe.get()));
    EXPECT_EQ(PARSE_OK, ExtendHeaderSerDes::ParseHeader(rbuf_pipe.get(), &res));
    EXPECT_EQ(ExtendVer::VER_ONE, res.ver);
    EXPECT_EQ(crc_mode, res.crc_mode);
    EXPECT_EQ(crc_value, res.crc_value);
    rbuf_pipe->clear();

    // Case 4: Version incorrect
    ref = IOBlockRef::CreateV2(ExtendHeaderSerDes::FIXED_HEADER_LENGTH);
    crc_value = 0x12345678;
    crc_mode = CrcMode::TYPE_META_PB;
    ExtendHeaderSerDes::PackHeader(ref.data(), crc_mode, crc_value);
    // modify version num to 20
    RawExtendHeader* hdr = reinterpret_cast<RawExtendHeader*>(ref.data());
    ExtendHeaderSerDes::SetVersion(hdr, static_cast<ExtendVer>(20));
    rbuf_pipe->append(ref);
    EXPECT_EQ(PARSE_OK, ExtendHeaderSerDes::MatchExtendHeader(rbuf_pipe.get()));
    EXPECT_EQ(PARSE_ERROR_ABSOLUTELY_WRONG, ExtendHeaderSerDes::ParseHeader(rbuf_pipe.get(), &res));
}

TEST(ExtendHeaderTest, BitwiseValueSetAndGet) {
    RawExtendHeader hdr;
    ExtendHeaderSerDes::SetCrcMode(&hdr, CrcMode::TYPE_META_PB);
    ExtendHeaderSerDes::SetVersion(&hdr, ExtendVer::VER_ONE);
    hdr.reserved = 88;
    EXPECT_EQ(88, hdr.reserved);
    ExtendHeaderSerDes::ResetReserved(&hdr);
    EXPECT_EQ(0, hdr.reserved);

    EXPECT_EQ(CrcMode::TYPE_META_PB, ExtendHeaderSerDes::GetCrcMode(hdr.crc_mode));
    EXPECT_EQ(ExtendVer::VER_ONE, ExtendHeaderSerDes::GetVersion(hdr.ver));
    EXPECT_EQ(ExtendVer::VER_NUM, ExtendHeaderSerDes::GetVersion(0));
    EXPECT_EQ(ExtendVer::VER_NUM, ExtendHeaderSerDes::GetVersion(88));
}

TEST(ExtendHeaderTest, CalculateChecksum) {
    char meta_data[64];
    memset(meta_data, '*', sizeof(meta_data));
    IOBuf meta;
    meta.append(meta_data, sizeof(meta_data));
    IOBuf pb;
    IOBuf att;
    for (int i = 0; i < 10; ++i) {
        auto ref = IOBlockRef::CreateV2(10);
        memset(ref.data(), 'p', ref.length());
        pb.append(ref);

        auto ref2 = IOBlockRef::CreateV2(20);
        memset(ref2.data(), 'x', ref2.length());
        att.append(ref2);
    }

    IOBuf cal_buf;
    cal_buf.append(meta);
    cal_buf.append(pb);
    cal_buf.append(att);
    EXPECT_EQ(0x80ef3d16, ExtendHeaderSerDes::CalCrc32(&cal_buf));

    cal_buf.clear();
    cal_buf.append(meta);
    cal_buf.append(att);
    EXPECT_EQ(0x30c3e6b5, ExtendHeaderSerDes::CalCrc32(&cal_buf));

    cal_buf.clear();
    cal_buf.append(meta);
    cal_buf.append(pb);
    EXPECT_EQ(0xd1b92264, ExtendHeaderSerDes::CalCrc32(&cal_buf));

    cal_buf.clear();
    cal_buf.append(meta);
    EXPECT_EQ(0x1a51b3cd, ExtendHeaderSerDes::CalCrc32(&cal_buf));
}

}  // End of namespace byterpc

int main(int argc, char** argv) {
    testing::InitGoogleTest(&argc, argv);
#ifdef BYTERPC_WITH_NUMA
    byterpc::FLAGS_byterpc_enable_numa_aware = true;
    byterpc::FLAGS_byterpc_memory_pool_numa_init_region_num = "1,1";
#endif
    byterpc::InitOptions init_opt;
    BYTERPC_CHECK(0 == byterpc::ExecCtx::Init(init_opt));

    auto fini_guard = byte::defer([]() { byterpc::ExecCtx::Fini(); });
    return RUN_ALL_TESTS();
}
