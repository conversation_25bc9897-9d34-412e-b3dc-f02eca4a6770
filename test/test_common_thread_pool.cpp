// CDS - Elastic and High performance Cloud Disk Service in BCE.
// Copyright (c) 2016 Baidu.com, Inc. All Rights Reserved
// 
// Author: <PERSON><PERSON><PERSON> (<EMAIL>)
// Date: 2016-05-19

#include <gflags/gflags.h>
#include <gtest/gtest.h>
#include <base/logging.h>
#include <unistd.h>
#include <boost/bind.hpp>
#include <base/memory/ref_counted.h>
#include "baidu/inf/aries-api/common/thread_pool.h"

namespace aries {
namespace common {

class ThreadPoolTests : public ::testing::Test {
};

void job_func(int id, bool need_sleep) {
    if (need_sleep) {
        LOG(INFO) << "thread " << pthread_self() << " sleeping, job " << id;
        sleep(5);
        LOG(INFO) << "thread " << pthread_self() << " sleeped, job " << id;
    } else {
        LOG(INFO) << "thread " << pthread_self() << " run, job " << id;
    }
}

TEST_F(ThreadPoolTests, func) {
    scoped_refptr<ThreadPool> pool(new ThreadPool("test", 5));

    // not start
    std::vector<int64_t> jobs;
    for (int i = 0; i < 100; i++) {
        int64_t job_id = pool->submit(boost::bind(&job_func, i, (i % 10) == 0),
                                       ThreadPool::LOW_PRIORITY);
        ASSERT_TRUE(job_id <= 0);
    }

    ASSERT_EQ(0, pool->start());
    for (int i = 0; i < 100; i++) {
        int64_t job_id = pool->submit(boost::bind(&job_func, i, (i % 10) == 0),
                                       ThreadPool::LOW_PRIORITY);
        ASSERT_TRUE(job_id > 0);
        jobs.push_back(job_id);
    }

    sleep(1);

    ASSERT_EQ(0, pool->start());
    pool->print();

    // 0, 10, 20, 30, 40 is sleeping
    ASSERT_EQ(-EINVAL, pool->cancel(-1));
    ASSERT_EQ(-EEXIST, pool->cancel(jobs[10]));
    ASSERT_EQ(0, pool->cancel(jobs[50]));

    // retry cancel
    ASSERT_EQ(-ECANCELED, pool->cancel(jobs[50]));

    sleep(3);
    // cancel finished job
    ASSERT_EQ(-ENOENT, pool->cancel(jobs[5]));

    pool->stop();
    ASSERT_EQ(-EINVAL, pool->submit(boost::bind(&job_func, 100, false),
                                   ThreadPool::LOW_PRIORITY));
    pool->stop();
    pool->join();
}

}
}

