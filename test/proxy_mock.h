#pragma once

#include "baidu/inf/aries-api/test/bmock_util.h"
#include "baidu/inf/aries-api/proxy/meta_handler.h"
#include "baidu/inf/aries-api/proxy/blob_oper.h"
#include "baidu/inf/aries-api/proxy/shard_util.h"
#include "baidu/inf/aries-api/proxy/blob_allocator.h"
#include "baidu/inf/aries-api/proxy/allocator_client.h"
#include "baidu/inf/aries-api/proxy/preallocator.h"
    
namespace aries {

MAKE_BMOCK_NS_CLASS_METHOD(2, baidu::rpc, Channel, Init,
        int(base::EndPoint, const baidu::rpc::ChannelOptions *));

MAKE_BMOCK_NS_CLASS_METHOD(2, aries, MetaHandler, list_allocator,
        int(uint64_t, std::map<uint64_t, AllocatorInfoPtr> *));
MAKE_BMOCK_NS_CLASS_METHOD(4, aries, MetaHandler, get_volume,
        int(uint64_t, uint64_t, bool, VolumeInfoPtr *));
MAKE_BMOCK_NS_CLASS_METHOD(3, aries, MetaHandler, get_space,
        int(uint64_t, const std::string &, SpaceInfoPtr *));
MAKE_BMOCK_NS_CLASS_METHOD(0, aries, AllocatorClient, init, int());
MAKE_BMOCK_NS_CLASS_METHOD(5, aries, AllocatorClient, allocate_bid, 
        int(uint64_t, const std::string &, uint32_t, __uint128_t *, VolumeInfo*));
MAKE_BMOCK_NS_CLASS_METHOD(3, aries, BlobOper, put,
        int32_t(const PutBlobInfo&, const VolumeInfo&, const RequestOptions&));
MAKE_BMOCK_NS_CLASS_METHOD(3, aries, BlobOper, get,
        int32_t(GetBlobInfo *, VolumeInfoPtr, const RequestOptions&));
MAKE_BMOCK_NS_CLASS_METHOD(3, aries, BlobOper, remove,
        int32_t(const RemoveBlobInfo&, VolumeInfoPtr, const RequestOptions&))
MAKE_BMOCK_NS_CLASS_METHOD(3, aries, BlobAllocator, alloc,
        int(const std::string &, PutBlobInfo &, VolumeInfo *));
MAKE_BMOCK_NS_CLASS_METHOD(3, aries, PreAllocator, alloc,
        int(const std::string &, PutBlobInfo &, VolumeInfo *));
MAKE_BMOCK_NS_CLASS_METHOD(2, aries, PreAllocator, async_alloc,
        void(const std::string &, int));
}
