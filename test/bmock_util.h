/***************************************************************************
 * 
 * Copyright (c) 2017 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/
 
 
 
/**
 * @file test/bmock_util.h
 * <AUTHOR>
 * @date 2017/06/19 13:59:35
 * @brief 
 *
 * seperate bmock declare and defination, add some helpfull macros
 *  
 * using DECLARE_BMOCK_XXX_METHOD to declare in .h files and using DEFINE_BMOCK_XXX_METHOD in .cpp files,
 *  just like the origin bmock macros:
 * xxx.h:
 *      DECLARE_BMOCK_NS_CLASS_METHOD(1 for args_num, myns, MyClass, my_method);
 * xxx.cpp:
 *      DEFINE_BMOCK_NS_CLASS_METHOD(1 for args_num, myns, MyClass, my_method);
 *
 * before using EXPECT_CALL in testsuits, use BMOCK_CLASS_MOCK_GUARD to open lib attach in local scope:
 *      BMOCK_CLASS_MOCK_GUARD(MyClass, my_method);
 *      ... some other codes ...
 *      EXPECT_CALL(BMOCK_NS_CLASS_OBJECT(myns, MyClass, my_method), xxx)
 *          .WillOnce(xxxx)
 *          ...
 **/

#ifndef BAIDU_INF_ARIES_API_TEST_BMOCK_UTIL_H
#define BAIDU_INF_ARIES_API_TEST_BMOCK_UTIL_H

#include <gtest/gtest.h>
#include "gmock/gmock.h"
#include "bmock.h"

#define DEFINE_NS_CLASS_STOP_MOCK_GUARD(n, c, m, F, args...) \
    class StopMock_##c##_##m##_##args {\
    public:\
        StopMock_##c##_##m##_##args() {\
            BMOCK_NS_CLASS_STOP(n, c, m, F);\
        }\
    };\
    static StopMock_##c##_##m##_##args g_stop_mock_##c##_##m##_##args_guard

#define DECLARE_NS_CLASS_STOP_MOCK_GUARD(n, c, m, F, args...) \
    class MockGuard_##c##_##m##_##args {\
    public:\
        MockGuard_##c##_##m##_##args() {\
            BMOCK_NS_CLASS_RESUME(n, c, m, F);\
        }\
        ~MockGuard_##c##_##m##_##args() {\
            BMOCK_NS_CLASS_STOP(n, c, m, F);\
        }\
    }

#define BMOCK_CLASS_MOCK_GUARD(c, m, args...) \
    MockGuard_##c##_##m##_##args c##_##m##_##args_mock_guard

#define DECLARE_BMOCK_NS_CLASS_METHOD(num, n, c, m, F, args...) \
    DECLARATION_NS_CLASS_METHOD##num(n, c, m, F, ##args);\
    BMOCK_BASE_CLASS_METHOD_CLASS##num(c, m, F, ##args);\
    BMOCK_NS_CLASS_METHOD_CLASS##num(n, c, m, F, ##args);\
    DECLARE_NS_CLASS_STOP_MOCK_GUARD(n, c, m, F, args);\
    extern bmock_##c##_##m##_##args BMOCK_CLASS_OBJECT(c, m, ##args);
#define DEFINE_BMOCK_NS_CLASS_METHOD(num, n, c, m, F, args...) \
    OBJECT_DEFINATION_NS_CLASS_METHOD(n, c, m, ##args);\
    BMOCK_FAULT_NS_CLASS_METHOD##num(n, c, m, F, ##args);\
    BMOCK_REAL_NS_CLASS_METHOD##num(n, c, m, F, ##args);\
    DEFINE_NS_CLASS_STOP_MOCK_GUARD(n, c, m, F);
#define MAKE_BMOCK_NS_CLASS_METHOD(num, n, c, m, F, args...) \
    DECLARE_BMOCK_NS_CLASS_METHOD(num, n, c, m, F, ##args) \
    DEFINE_BMOCK_NS_CLASS_METHOD(num, n, c, m, F, ##args)

#define DECLARE_BMOCK_NS_CLASS_STATIC_METHOD(num, n, c, m, F, args...) \
    DECLARATION_CLASS_STATIC_METHOD##num(c, m, F, ##args);\
    BMOCK_BASE_CLASS_METHOD_CLASS##num(c, m, F, ##args);\
    BMOCK_NS_CLASS_METHOD_CLASS##num(n, c, m, F, ##args);\
    DECLARE_NS_CLASS_STOP_MOCK_GUARD(n, c, m, F, args);\
    extern bmock_##c##_##m##_##args BMOCK_CLASS_OBJECT(c, m, ##args);
#define DEFINE_BMOCK_NS_CLASS_STATIC_METHOD(num, n, c, m, F, args...) \
    OBJECT_DEFINATION_CLASS_STATIC_METHOD(c, m, ##args);\
    BMOCK_FAULT_CLASS_STATIC_METHOD##num(c, m, F, ##args); \
    BMOCK_REAL_NS_CLASS_STATIC_METHOD##num(n, c, m, F, ##args);\
    DEFINE_NS_CLASS_STOP_MOCK_GUARD(n, c, m, F);

#endif
