// Copyright (c) 2021, ByteDance Inc. All rights reserved.
#include "transport/tarzan/tarzan_tcp_transport.h"

#include <byte/string/number.h>
#include <byte/thread/this_thread.h>
#include <byte/util/defer.h>
#include <byterpc/exec_ctx.h>
#include <byterpc/io_buf.h>
#include <byterpc/rpc.h>
#include <byterpc/util/owner_ptr.h>
#include <errno.h>
#include <gtest/gtest.h>

#include <atomic>
#include <memory>
#include <mutex>
#include <thread>

#include "byterpc/byterpc_flags.h"
#include "iobuf/io_rbuf_pipe.h"
#include "metrics/byterpc_metrics.h"
#include "rpc/socket.h"
#include "tarzan.h"
#include "transport/tarzan/tarzan_context.h"
#include "transport/tarzan/tarzan_event_dispatcher.h"

#include <byterpc/util/logging.h>

using namespace byterpc;

namespace byterpc {

static int port = 8990;
static const size_t total_len = 1000;
static size_t server_need_recv_len = 0;  // server need receive data len from client,
                                         // if not, wait util receive all data
char check_ch[1000] = {'a'};
// Set true when server socket recycle, used to determine whether can send msg to client
// Need set false when start server every time
std::atomic_bool g_server_finish(false);

extern thread_local int32_t current_thread_tarzan_serial_number;

enum WriteType {
    WriteNone = 0,     // not need send data
    WritePartial = 1,  // send total_len/2 to remove side
    WriteAll = 2,      // send total_len to remove side
};

enum TestIOEventType {
    None = 1,
    FailAddConsumer = 2,
    FailAddEpollOut = 3,
    FailRemoveConsumer = 4,
    FailRemoveEpollOut = 5,
};

static void DefaultFree(void* vaddr, void* param) {
    free(vaddr);
}

class SimpleClientSocket : public Socket {
public:
    SimpleClientSocket() : _owner_ref(0), _read_finish(false), _write_finish(false) {}

    ~SimpleClientSocket() {
        if (!Failed() && static_cast<bool>(_trans)) {
            _trans->Reset(byterpc::ECLOSE);
        }
    }

    void ReadyToSend(Transport* trans) {
        _trans.reset(trans);
    }

    util::EndPoint local_side() const override {
        return util::EndPoint();
    }

    util::EndPoint remote_side() const override {
        return util::EndPoint();
    }

    TransportType GetTransportType() const override {
        return TYPE_USERSPACE_TCP;
    }

    void SubmitWrite(
        Controller* cntl,
        IOBlockRef&& first,
        std::unique_ptr<IOBuf>&& second,
        std::unique_ptr<IOBuf>&& third,
        std::vector<std::pair<std::unique_ptr<IOBuf>, RdmaBufInfo>>&& rdma_write_bufs) override {}

    void OwnerRef() override {
        ++_owner_ref;
    }

    void OwnerUnref() override {
        --_owner_ref;
        if (_owner_ref == 0) {
            _trans->ResetSocket();
            delete this;
        }
    }

    void HandleWriteEvent() override {
        // set finish only when MsgHolder of TarzanTcpTransport send all msg
        if ((static_cast<TarzanTcpTransport*>(_trans.get()))->WriteFinish()) {
            _write_finish = true;
        }
    }

    void HandleReadEvent(IORbufPipe* rbuf_pipe) override {
        if (rbuf_pipe->length() == total_len) {
            char ch[total_len];
            rbuf_pipe->cut_to(ch, total_len);
            EXPECT_EQ(*ch, *check_ch);
            _read_finish = true;
        }
    }
    void SetFailed(int err_code, const std::string& err_msg) override {
        Socket::SetFailed(err_code, err_msg);
        _read_finish = true;
        _write_finish = true;
    }

    uint64_t ConnectionId() const override {
        return _trans->ConnectionId();
    }

    int GetRdmaMemoryRkey(const void* addr, uint32_t* rkey) const override {
        return -1;
    }

    void Reset() {
        Socket::Reset();
        _write_finish = false;
        _read_finish = false;
    }

    bool IsReadFinish() {
        return _read_finish;
    }

    bool IsWriteFinish() {
        return _write_finish;
    }

private:
    util::owner_ptr<Transport> _trans;
    int _owner_ref;
    bool _read_finish;
    bool _write_finish;
};

class SimpleServerSocket : public Socket {
public:
    explicit SimpleServerSocket(WriteType type)
        : _owner_ref(0), _write_finish(false), _write_resp_count(0), _write_type(type) {}

    ~SimpleServerSocket() {
        g_server_finish.store(true, std::memory_order_release);
    }

    void ReadyToServe(Transport* trans) {
        _owner_ref += 1;
        _trans.reset(trans);
    }

    util::EndPoint local_side() const override {
        return util::EndPoint();
    }

    util::EndPoint remote_side() const override {
        return util::EndPoint();
    }

    TransportType GetTransportType() const override {
        return TYPE_USERSPACE_TCP;
    }

    void SubmitWrite(Controller* cntl,
                     IOBlockRef&& first,
                     std::unique_ptr<IOBuf>&& second,
                     std::unique_ptr<IOBuf>&& third,
                     std::vector<std::pair<std::unique_ptr<IOBuf>, RdmaBufInfo>>&& rdma_write_bufs =
                         {}) override {}

    void OwnerRef() override {
        ++_owner_ref;
    }

    void OwnerUnref() override {
        if (--_owner_ref == 0) {
            delete this;
        }
    }
    void HandleWriteEvent() override {
        // set finish only when MsgHolder of TarzanTcpTransport send all msg
        if ((static_cast<TarzanTcpTransport*>(_trans.get()))->WriteFinish()) {
            _write_finish = true;
        }
    }

    void HandleReadEvent(IORbufPipe* rbuf_pipe) override {
        // server wait to process util receive all data from client
        if (rbuf_pipe->length() != server_need_recv_len) {
            return;
        }
        // only check send total_len data, and ignore big packet data
        if (server_need_recv_len == total_len) {
            char ch[total_len];
            rbuf_pipe->copy_to(ch, total_len);
            EXPECT_EQ(*ch, *check_ch);
        }
        rbuf_pipe->clear();
        std::unique_ptr<IOBuf> write_buf = std::make_unique<IOBuf>();
        if (WritePartial == _write_type) {
            // Return partial response to client to test server close when not send enough response
            write_buf->append(check_ch, total_len / 2);
        } else {
            write_buf->append(check_ch, total_len);
        }
        _trans->StartWrite({}, std::move(write_buf));
        // flush write, after write complete,
        // socket write done(HandleWriteEvent) will be called
        _trans->HandleWriteEvent();
        ++_write_resp_count;
        while (!_write_finish) {
            _trans->HandleWriteEvent();
            ++_write_resp_count;
            usleep(10);
        }
    }

    void SetFailed(int err_code, const std::string& err_msg) override {
        Socket::SetFailed(err_code, err_msg);
        _trans->ResetSocket();
        OwnerUnref();
    }

    uint64_t ConnectionId() const override {
        return _trans->ConnectionId();
    }

    int GetRdmaMemoryRkey(const void* addr, uint32_t* rkey) const override {
        return -1;
    }

    bool WriteSuccess() {
        if (_write_type == WriteNone || _write_resp_count) {
            return true;
        }
        return false;
    }

    void SetWriteType(WriteType wr_type) {
        _write_type = wr_type;
    }

private:
    util::owner_ptr<Transport> _trans;
    int _owner_ref;
    bool _write_finish;
    // Record response number send to client
    int _write_resp_count;
    // Used in server to determine whether return all response to client
    WriteType _write_type;
};

class SimpleEventDispatcher : public EventRegistry {
public:
    SimpleEventDispatcher() : _test_type(TestIOEventType::None) {}

    size_t NumRegisteredIOHandler() const override {
        return 0;
    }
    int AddConsumer(IOHandler* h) override {
        if (_test_type == TestIOEventType::FailAddConsumer) {
            return -1;
        }
        return 0;
    }
    int RemoveConsumer(IOHandler* h) override {
        if (_test_type == TestIOEventType::FailRemoveConsumer) {
            return -1;
        }
        return 0;
    }
    int AddEpollOut(IOHandler* h, bool pollin) override {
        if (_test_type == TestIOEventType::FailAddEpollOut) {
            return -1;
        }
        return 0;
    }
    int RemoveEpollOut(IOHandler* h, bool pollin) override {
        if (_test_type == TestIOEventType::FailRemoveEpollOut) {
            return -1;
        }
        return 0;
    }
    TimeoutIterator AddTimeConsumer(TimeEventHandler* h, uint64_t microseconds_since_now) override {
        TimeoutIterator iter;
        return iter;
    }
    int RemoveTimeConsumer(TimeoutIterator iter) override {
        return 0;
    }
    void SetOutActive(IOHandler* h) override {}
    void ClearOutActive(IOHandler* h) override {}

    void SetTestEventType(TestIOEventType type) {
        _test_type = type;
    }

private:
    TestIOEventType _test_type;
};

/**
 * @brief Start tarzan server to process io event.
 *
 * @param listen_addr server listen address
 * @param count server establish connection number
 * @param wr_type server send response data length
 * @param finish_server server stop by human
 * @param start_client client need start write after server accept
 */
static int StartServerImpl(util::EndPoint listen_addr,
                           int count,
                           WriteType wr_type,
                           std::atomic_bool* finish_server,
                           std::atomic_bool* start_client,
                           std::atomic_bool* send_msg) {
    g_server_finish.store(false, std::memory_order_release);
    ExecCtx ctx;
    struct tarzan_sock_opts opts;
    memset(&opts, 0, sizeof(opts));
    opts.has_worker_id = 1;
    opts.worker_id = 0;
    int listen_fd =
        tarzan_listen_on((util::ip2str(listen_addr.ip)).c_str(), listen_addr.port, &opts, NULL);
    if (listen_fd < 0) {
        return -1;
    }
    start_client->store(true, std::memory_order_release);
    BYTERPC_LOG(INFO) << "server start, listen addr=" << listen_addr << ", listen fd=" << listen_fd;
    int index = 0;
    for (int i = 0; i < count; ++i) {
        g_server_finish.store(false, std::memory_order_release);
        tarzan_accept_sock socks[32];
        while (true) {
            tarzan_worker_run(0);
            if (tarzan_accept_burst(0, socks, 32) > 0) {
                break;
            }
            usleep(5);
        }
        // server accept and client can start send msg, start_client
        // only can be set after server accept success
        send_msg->store(true, std::memory_order_release);
        int accept_fd = socks[0].sid;
        BYTERPC_LOG(INFO) << "server accpet, accept fd=" << accept_fd;
        EXPECT_GE(accept_fd, 0);
        SimpleServerSocket* socket = new SimpleServerSocket(wr_type);
        SimpleEventDispatcher ev_reg;
        TarzanTcpTransport* trans = new TarzanTcpTransport(accept_fd, &ev_reg, socket);
        socket->ReadyToServe(trans);
        if (finish_server) {
            // Read event by finish flag, need send one response to client at least
            while (!g_server_finish.load(std::memory_order_acquire) &&
                   (!(finish_server->load(std::memory_order_acquire)) || !socket->WriteSuccess())) {
                trans->HandleReadEvent();
                tarzan_worker_run(0);
                usleep(5);
            }
            if (!g_server_finish.load(std::memory_order_acquire)) {
                trans->Reset(ECLOSE);
                tarzan_worker_run(0);
            }
        } else {
            // Read event util client close
            while (!g_server_finish.load(std::memory_order_acquire)) {
                trans->HandleReadEvent();
                tarzan_worker_run(0);
                usleep(5);
            }
        }
        // Call tarzan_worker_run to close accept_fd successfully.
        // Because we do not know close fd need how many times of tarzan_worker_run,
        // we will run it many times to ensure close successfully.
        while (index < 20) {
            tarzan_worker_run(0);
            ++index;
        }
    }
    tarzan_close(listen_fd);
    index = 0;
    while (index < 20) {
        tarzan_worker_run(0);
        ++index;
    }
    BYTERPC_LOG(INFO) << "server finish";
    return 0;
}

static void StartServer(bool is_ipv4,
                        int count,
                        WriteType wr_type,
                        std::atomic_bool* finish_server,
                        std::atomic_bool* start_client,
                        std::atomic_bool* send_msg) {
    util::ip_t ip = util::my_ip(is_ipv4);
    port = byte::ThisThread::GetId();
    util::EndPoint listen_addr(ip, port);
    while (StartServerImpl(listen_addr, count, wr_type, finish_server, start_client, send_msg) <
           0) {
        // retry other port
        ++listen_addr.port;
    }
}

static void StartClient(bool is_ipv4,
                        SimpleClientSocket** client_socket,
                        TarzanTcpTransport** client_trans,
                        SimpleEventDispatcher** client_ev_reg) {
    ExecCtx ctx;
    current_thread_tarzan_serial_number = 1;
    SimpleEventDispatcher* ev_reg = new SimpleEventDispatcher();
    SimpleClientSocket* socket = new SimpleClientSocket();
    socket->OwnerRef();
    util::ip_t ip = util::my_ip(is_ipv4);
    util::EndPoint server(ip, port);
    BYTERPC_LOG(INFO) << "Try to connect to server=" << server;
    TarzanTcpTransport* trans = new TarzanTcpTransport(server, ev_reg, socket, 2000);
    socket->ReadyToSend(trans);
    std::unique_ptr<IOBuf> send_buf = std::make_unique<IOBuf>();
    send_buf->append(check_ch, total_len);
    server_need_recv_len = total_len;
    if (send_buf->block_num() == 1) {
        IOBlockRef ref = send_buf->block_ref_at(0);
        trans->StartWrite(std::move(ref), nullptr);
    } else {
        trans->StartWrite({}, std::move(send_buf));
    }
    *client_socket = socket;
    *client_trans = trans;
    *client_ev_reg = ev_reg;
}

}  // namespace byterpc

static void TestIOSuccess(bool is_ipv4, bool big_packet) {
    gflags::FlagSaver saver;
    FLAGS_byterpc_tarzan_slow_connect_us = 0;
    std::atomic_bool start_client(false);
    std::atomic_bool send_msg(false);

    // Start server
    std::unique_ptr<std::thread> acceptor(
        new std::thread(StartServer, is_ipv4, 1, WriteAll, nullptr, &start_client, &send_msg));

    while (!start_client.load(std::memory_order_acquire)) {
        usleep(2);
    }
    // Start client
    std::thread client([&]() {
        SimpleClientSocket* socket = nullptr;
        SimpleEventDispatcher* ev_reg = nullptr;
        TarzanTcpTransport* trans = nullptr;
        StartClient(is_ipv4, &socket, &trans, &ev_reg);
        while (!send_msg.load(std::memory_order_acquire)) {
            tarzan_worker_run(1);
            usleep(10);
        }
        if (big_packet) {
            ssize_t data_len = 10 * 1024 * 1024UL;
            // 1. send discontinuous big packet with zerocopy
            std::string data(data_len, 'a');
            std::unique_ptr<IOBuf> buf(new IOBuf());
            buf->append(data);
            IOBlockRef ref = IOBlockRefPrivateAccessor::CreateOne();
            server_need_recv_len += ref.size();
            server_need_recv_len += data_len;
            // 2. send continuous big packet without zerocopy
            MemoryDescriptor meta;
            meta.memory_id = MemoryId::DEFAULT_ALLOCATED;
            meta.length = data_len;
            meta.module_id = 0;
            meta.param = nullptr;
            meta.physical_addr = 0;
            meta.virtual_address = malloc(data_len);
            buf->append(IOBlockRef::TakeOwnership(meta, DefaultFree));
            server_need_recv_len += data_len;
            trans->StartWrite(std::move(ref), std::move(buf));
        }
        do {
            trans->HandleWriteEvent();
            tarzan_worker_run(1);
            usleep(10);
        } while (!socket->IsWriteFinish());

        while (!socket->IsReadFinish()) {
            tarzan_worker_run(1);
            trans->HandleReadEvent();
            usleep(10);
        }
        EXPECT_EQ(0, socket->ErrorCode());
        EXPECT_EQ(0, trans->GetError());

        socket->OwnerUnref();
        delete ev_reg;
        int index = 0;
        while (index < 20) {
            tarzan_worker_run(1);
            ++index;
        }
        BYTERPC_LOG(INFO) << "client finish";
    });

    client.join();
    acceptor->join();
}

TEST(TarzanTcpTransportTest, IOSuccess) {
    TestIOSuccess(true, false);
}

TEST(TarzanTcpTransportTest, IOSuccess_ipv6) {
    TestIOSuccess(false, false);
}

TEST(TarzanTcpTransportTest, SendBigPacket) {
    TestIOSuccess(true, true);
}

TEST(TarzanTcpTransportTest, SendDataWithEpollOutFailed) {
    std::atomic_bool start_client(false);
    std::atomic_bool send_msg(false);
    std::atomic_bool finish_server(false);

    // Start server
    std::unique_ptr<std::thread> acceptor(
        new std::thread(StartServer, true, 1, WriteNone, &finish_server, &start_client, &send_msg));
    while (!start_client.load(std::memory_order_acquire)) {
        usleep(2);
    }

    // Start client
    std::thread client([&]() {
        // Set AddEpollOut() failed when write with EAGAIN error,
        // to test write big packet of IOBuf failed
        SimpleClientSocket* socket = nullptr;
        SimpleEventDispatcher* ev_reg = nullptr;
        TarzanTcpTransport* trans = nullptr;
        StartClient(true, &socket, &trans, &ev_reg);
        ev_reg->SetTestEventType(TestIOEventType::FailAddEpollOut);
        while (!send_msg.load(std::memory_order_acquire)) {
            tarzan_worker_run(1);
            usleep(10);
        }
        // Use malloc memory to write data to send, so that can send many times and
        // need AddEpollout. If memory pool, write buffer will be enough to send.
        std::unique_ptr<IOBuf> buf(new IOBuf());
        ssize_t data_len = 2 * 1024 * 1024UL;
        ssize_t block_size = 8 * 1024UL;
        size_t block_num = data_len / block_size;
        MemoryDescriptor meta;
        meta.memory_id = MemoryId::DEFAULT_ALLOCATED;
        meta.length = block_size;
        meta.module_id = 0;
        meta.param = nullptr;
        meta.physical_addr = 0;
        for (size_t i = 0; i < block_num; ++i) {
            meta.virtual_address = malloc(block_size);
            IOBlockRef ref = IOBlockRef::TakeOwnership(meta, DefaultFree);
            buf->append(ref);
        }
        trans->StartWrite({}, std::move(buf));
        server_need_recv_len += data_len;
        while (!socket->IsWriteFinish()) {
            trans->HandleWriteEvent();
            tarzan_worker_run(1);
            usleep(10);
        }
        EXPECT_TRUE(socket->Failed());
        EXPECT_EQ(EINTERNAL, socket->ErrorCode());
        socket->OwnerUnref();
        delete ev_reg;
        finish_server.store(true, std::memory_order_release);
        int index = 0;
        while (index < 20) {
            tarzan_worker_run(1);
            ++index;
        }
    });

    client.join();
    acceptor->join();
}

TEST(TarzanTcpTransportTest, ServerCloseBeforeClientRecvAllResponse) {
    std::atomic_bool finish_server(false);
    std::atomic_bool start_client(false);
    std::atomic_bool send_msg(false);

    std::unique_ptr<std::thread> server(new std::thread(
        StartServer, true, 1, WritePartial, &finish_server, &start_client, &send_msg));

    while (!start_client.load(std::memory_order_acquire)) {
        usleep(2);
    }

    std::thread client([&]() {
        SimpleClientSocket* socket = nullptr;
        SimpleEventDispatcher* ev_reg = nullptr;
        TarzanTcpTransport* trans = nullptr;
        StartClient(true, &socket, &trans, &ev_reg);
        while (!send_msg.load(std::memory_order_acquire)) {
            tarzan_worker_run(1);
            usleep(10);
        }
        while (!socket->IsWriteFinish()) {
            trans->HandleWriteEvent();
            tarzan_worker_run(1);
            usleep(1);
        }
        finish_server.store(true, std::memory_order_release);

        // NOTE: client will not receive error when server exit, need to close by other way
        while (!socket->IsReadFinish()) {
            tarzan_worker_run(1);
            trans->HandleReadEvent();
            usleep(1);
        }
        socket->OwnerUnref();
        delete ev_reg;
        int index = 0;
        while (index < 10) {
            tarzan_worker_run(1);
            ++index;
        }
        BYTERPC_LOG(INFO) << "client finish";
    });

    server->join();
    client.join();
}

TEST(TarzanTcpTransportTest, ReceiveRpcAfterClientSocketRecycle) {
    std::atomic_bool start_client(false);
    std::atomic_bool send_msg(false);
    std::atomic_bool finish_server(false);

    std::unique_ptr<std::thread> server(
        new std::thread(StartServer, true, 1, WriteAll, &finish_server, &start_client, &send_msg));

    while (!start_client.load(std::memory_order_acquire)) {
        usleep(2);
    }

    std::thread client([&]() {
        SimpleClientSocket* socket = nullptr;
        SimpleEventDispatcher* ev_reg = nullptr;
        TarzanTcpTransport* trans = nullptr;
        StartClient(true, &socket, &trans, &ev_reg);
        trans->OwnerRef();
        while (!send_msg.load(std::memory_order_acquire)) {
            tarzan_worker_run(1);
            usleep(10);
        }
        while (!socket->IsWriteFinish()) {
            trans->HandleWriteEvent();
            tarzan_worker_run(1);
            usleep(1);
        }
        // release client_socket before receive all response
        socket->OwnerUnref();
        finish_server.store(true, std::memory_order_release);

        while (!g_server_finish.load(std::memory_order_acquire)) {
            tarzan_worker_run(1);
            trans->HandleReadEvent();
            usleep(1);
        }
        trans->Reset(ECLOSE);
        trans->OwnerUnref();
        delete ev_reg;
        int index = 0;
        while (index < 10) {
            tarzan_worker_run(1);
            ++index;
        }
    });

    server->join();
    client.join();
}

TEST(TarzanTcpTransportTest, PreConnect) {
    std::atomic_bool start_client(false);
    std::atomic_bool send_msg(false);

    std::unique_ptr<std::thread> acceptor(
        new std::thread(StartServer, true, 1, WriteNone, nullptr, &start_client, &send_msg));

    while (!start_client.load(std::memory_order_acquire)) {
        usleep(2);
    }

    std::thread client([&]() {
        ExecCtx ctx;
        current_thread_tarzan_serial_number = 1;
        SimpleEventDispatcher ev_reg;
        SimpleClientSocket* socket = new SimpleClientSocket();
        socket->OwnerRef();
        util::ip_t ip = util::my_ip(true);
        util::EndPoint server(ip, port);
        TarzanTcpTransport* trans = new TarzanTcpTransport(server, &ev_reg, socket, 2000);
        socket->ReadyToSend(trans);
        EXPECT_EQ(1, trans->PreConnect());
        while (!send_msg.load(std::memory_order_acquire)) {
            tarzan_worker_run(1);
            usleep(10);
        }
        trans->HandleWriteEvent();
        EXPECT_TRUE(trans->IsConnected());
        EXPECT_EQ(TYPE_USERSPACE_TCP, trans->GetTransportType());
        socket->OwnerUnref();
        int index = 0;
        while (index < 20) {
            tarzan_worker_run(1);
            ++index;
        }
    });

    client.join();
    acceptor->join();
}

TEST(TarzanTcpTransportTest, ConnectTimeOut) {
    std::atomic_bool start_client(false);
    std::atomic_bool send_msg(false);

    // Start server
    std::unique_ptr<std::thread> acceptor(
        new std::thread(StartServer, true, 1, WriteNone, nullptr, &start_client, &send_msg));

    while (!start_client.load(std::memory_order_acquire)) {
        usleep(2);
    }

    std::thread client([&]() {
        SimpleClientSocket* socket = nullptr;
        SimpleEventDispatcher* ev_reg = nullptr;
        TarzanTcpTransport* trans = nullptr;
        StartClient(true, &socket, &trans, &ev_reg);
        while (!send_msg.load(std::memory_order_acquire)) {
            tarzan_worker_run(1);
            usleep(10);
        }
        trans->_connect_timeout_handler->HandleTimeEvent();
        EXPECT_EQ(-2, trans->GetFD());
        EXPECT_EQ(ETIMEDOUT, socket->ErrorCode());
        socket->OwnerUnref();
        delete ev_reg;
        int index = 0;
        while (index < 10) {
            tarzan_worker_run(1);
            ++index;
        }
        BYTERPC_LOG(INFO) << "client finish";
    });

    client.join();
    acceptor->join();
}

TEST(TarzanTcpTransortTest, ConnectFail) {
    // 1. Set AddEpollOut failed when establish connection,
    // to test connect failed
    ExecCtx ctx;
    SimpleEventDispatcher* ev_reg = new SimpleEventDispatcher();
    ev_reg->SetTestEventType(TestIOEventType::FailAddEpollOut);
    SimpleClientSocket* socket = new SimpleClientSocket();
    socket->OwnerRef();
    std::string ip = "127.0.0.1:21000";
    util::EndPoint server;
    util::str2endpoint(ip.c_str(), &server);
    TarzanTcpTransport* trans = new TarzanTcpTransport(server, ev_reg, socket, 2000);
    socket->ReadyToSend(trans);
    std::unique_ptr<IOBuf> buf1 = std::make_unique<IOBuf>();
    buf1->append("aaaa");
    ssize_t ret = trans->StartWrite({}, std::move(buf1));
    EXPECT_LT(ret, 0);
    EXPECT_TRUE(socket->Failed());
    EXPECT_EQ(byterpc::ECONNECTFAILED, socket->ErrorCode());
    socket->Reset();

    std::unique_ptr<IOBuf> buf2(new IOBuf());
    buf2->append("aaaa");
    IOBlockRef ref;
    ret = trans->StartWrite(std::move(ref), std::move(buf2));
    EXPECT_LT(ret, 0);
    EXPECT_EQ(byterpc::ECONNECTFAILED, socket->ErrorCode());
    socket->OwnerUnref();
    delete ev_reg;

    // 2. Set AddConsumer failed, to test connection success but add read event failed
    std::atomic_bool start_client(false);
    std::atomic_bool send_msg(false);
    std::unique_ptr<std::thread> acceptor(
        new std::thread(StartServer, true, 1, WriteAll, nullptr, &start_client, &send_msg));
    while (!start_client.load(std::memory_order_acquire)) {
        usleep(2);
    }
    std::thread client([&]() {
        SimpleClientSocket* socket = nullptr;
        SimpleEventDispatcher* ev_reg = nullptr;
        TarzanTcpTransport* trans = nullptr;
        StartClient(true, &socket, &trans, &ev_reg);
        ev_reg->SetTestEventType(TestIOEventType::FailAddConsumer);
        while (!send_msg.load(std::memory_order_acquire)) {
            tarzan_worker_run(1);
            usleep(10);
        }
        trans->HandleWriteEvent();
        EXPECT_FALSE(trans->_connecting);
        EXPECT_FALSE(trans->IsConnected());
        EXPECT_TRUE(socket->Failed());
        EXPECT_EQ(EINTERNAL, socket->ErrorCode());
        socket->OwnerUnref();
        delete ev_reg;
        int index = 0;
        while (index < 10) {
            tarzan_worker_run(1);
            ++index;
        }
        BYTERPC_LOG(INFO) << "client finish";
    });

    client.join();
    acceptor->join();
}

int main(int argc, char** argv) {
    testing::InitGoogleTest(&argc, argv);
    InitOptions opt(true, false);
#ifdef BYTERPC_ENABLE_BYTE_EXPRESS
    opt._init_rdma = true;
#endif
    opt._metric_prefix = "tarzan_tcp_test";
    FLAGS_byterpc_tarzan_worker_num = 2;
    BYTERPC_CHECK(0 == byterpc::ExecCtx::Init(opt));

    auto fini_guard = byte::defer([]() { byterpc::ExecCtx::Fini(); });
    return RUN_ALL_TESTS();
}
