// Copyright (c) 2021, ByteDance Inc. All rights reserved.

#include <atomic>
#include <memory>
#include <thread>

#include <byte/util/defer.h>
#include <gtest/gtest.h>
#include "byterpc/builder.h"
#include "byterpc/byterpc_flags.h"
#include "byterpc/callback.h"
#include "byterpc/exec_ctx.h"
#include "byterpc/io_buf.h"
#include "byterpc/memory_id.h"
#include "byterpc/server.h"
#include "byterpc/util/endpoint.h"
#include "byterpc/util/logging.h"
#include "mem/memory_pool.h"
#include "status.pb.h"

namespace byterpc {

class MyStatusService : public status {
public:
    void default_method(::google::protobuf::RpcController* controller,
                        const StatusRequest* request,
                        StatusResponse* response,
                        ::google::protobuf::Closure* done) override {
        done->Run();
    }
};

void user_done(Controller* cntl, int* rpc_num) {
    EXPECT_FALSE(cntl->Failed());
    ++(*rpc_num);
};

TEST(TarzanRpcTest, TestBigPacket) {
    std::atomic<bool> client_finish(false);
    std::atomic<bool> start_server(false);
    Server* svr = nullptr;

    std::thread server([&]() {
        ExecCtx ctx(LOOP_IF_POSSIBLE);
        svr = new Server();
        svr->RegisterService(new MyStatusService);
        ServerOptions server_opt;
        server_opt._enable_utcp = true;
        util::ip_t ip = util::my_ip(true);
        int port = 32234;
        util::EndPoint listen_addr(ip, port);
        while (!svr->Start(listen_addr, server_opt)) {
            listen_addr.port++;
        }
        start_server.store(true, std::memory_order_release);
        while (!client_finish.load(std::memory_order_acquire)) {
            ExecCtx::LoopOnce(TransportType::TYPE_USERSPACE_TCP);
        }
    });

    std::thread client([&]() {
        while (!start_server.load(std::memory_order_acquire)) {
            usleep(2);
        }
        ExecCtx ctx(LOOP_IF_POSSIBLE);
        util::EndPoint remote_addr = svr->listen_address();
        Builder::ChannelOptions chan_opt;
        chan_opt._connect_timeout_ms = 10000;
        chan_opt._rpc_timeout_ms = 20000;
        chan_opt._trans_type = TransportType::TYPE_USERSPACE_TCP;
        Builder builder;
        std::shared_ptr<Builder::Channel> channel = builder.BuildChannel(remote_addr, chan_opt);
        EXPECT_NE(channel, nullptr);
        size_t att_size = 4 * 1024 * 1024UL - 300;
        void* addr = MemoryPool::Get()->Alloc(att_size);
        EXPECT_NE(addr, nullptr);
        memset(addr, 'a', att_size);
        MemoryDescriptor desc;
        desc.memory_id = MemoryId::UNIFY_ALLOCATED;
        desc.length = att_size;
        desc.physical_addr = 1;
        desc.virtual_address = addr;
        IOBlockRef ref = IOBlockRef::TakeOwnership(desc);
        IOBuf att;
        att.append(std::move(ref));
        std::unique_ptr<status_Stub> stub = std::make_unique<status_Stub>(channel.get());
        StatusRequest req;
        StatusResponse resp;
        int rpc_num = 0;
        int kReqNum = 5;
        for (int i = 0; i < kReqNum; ++i) {
            Controller* cntl = builder.CreateSessionController(PROTOCOL_BYTE_STD);
            cntl->InstallOutgoingAttachment(att);
            google::protobuf::Closure* done =
                byterpc::NewCallback<Controller*, int*>(user_done, cntl, &rpc_num);
            stub->default_method(cntl, &req, &resp, done);
        }
        while (rpc_num != kReqNum) {
            ExecCtx::LoopOnce(TransportType::TYPE_USERSPACE_TCP);
        }
    });

    client.join();
    client_finish.store(true, std::memory_order_release);
    server.join();
    delete svr;
}

}  // namespace byterpc

int main(int argc, char** argv) {
    testing::InitGoogleTest(&argc, argv);
    byterpc::FLAGS_byterpc_tarzan_worker_num = 2;
    byterpc::InitOptions init_opt(true, false);
    BYTERPC_CHECK(0 == byterpc::ExecCtx::Init(init_opt));

    auto fini_guard = byte::defer([]() { byterpc::ExecCtx::Fini(); });
    return RUN_ALL_TESTS();
}
