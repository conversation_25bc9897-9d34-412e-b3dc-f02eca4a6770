/***************************************************************************
 * 
 * Copyright (c) 2019 Baidu.com, Inc. All Rights Reserved
 * $Id$ 
 * 
 **************************************************************************/
 
 /**
 * @file test_proxy_proxy.cpp
 * <AUTHOR>
 * @date 2019/03/14 14:38:34
 * @version $Revision$ 
 * @brief 
 *  proxy/proxy.cpp UT
 **/
#include <cassert>
#include <ctime>
#include "baidu/inf/aries-api/common/checksum.h"
#include "baidu/inf/aries-api/test/bmock_util.h"
#include "baidu/inf/aries-api/proxy/proxy.h"
#include "baidu/inf/aries-api/proxy/util.h"
#include "baidu/inf/aries-api/common/compressor.h"
#include <baidu/third-party/zstd/include/zstd.h>
namespace aries {
using ::testing::Return;
using ::testing::DoAll;
using ::testing::SetArgPointee;
using ::testing::SetArgReferee;
using ::testing::_;
using ::testing::InvokeWithoutArgs;
MAKE_BMOCK_NS_CLASS_METHOD(3, aries, BlobAllocator, alloc, int(const std::string, PutBlobInfo&, VolumeInfo*));
MAKE_BMOCK_NS_CLASS_METHOD(3, aries, BlobOper, put, int32_t(const PutBlobInfo&, const VolumeInfo*, const RequestOption&));
MAKE_BMOCK_NS_CLASS_METHOD(4, aries, MetaHandler, get_volume, int(uint64_t, uint64_t, bool, VolumeInfoPtr*));
MAKE_BMOCK_NS_CLASS_METHOD(3, aries, BlobOper, get, int32_t(GetBlobInfo*, VolumeInfoPtr, const RequestOptions&));
MAKE_BMOCK_NS_CLASS_METHOD(3, aries, BlobOper, remove, int32_t(RemoveBlobInfo*, VolumeInfoPtr, const RequestOptions&));
MAKE_BMOCK_NS_CLASS_METHOD(5, aries::common, ZstdCompressor, compress, int64_t(const CompressOption*, const char*, size_t, char*, size_t));
MAKE_BMOCK_NS_CLASS_METHOD(5, aries::common, ZstdCompressor, decompress, int64_t(const CompressOption*, const char*, size_t, char*, size_t));
MAKE_BMOCK_NS_CLASS_METHOD(1, aries::common, ZstdCompressor, compress_bound, size_t(size_t));
MAKE_BMOCK_NS_CLASS_METHOD(5, aries::common, Compressor, compress_rpc_call, base::Status(const CompressOption*, 
        const char*, size_t, char*, size_t*));
class TestProxy : public ::testing::Test {
public:
    void SetUp() {
        _proxy.reset(new Proxy);
        assert(_proxy != nullptr);
        ProxyConf conf;
        conf.proxy_name = "test_proxy_lib";
        conf.proxy_version = "2.0.0.1";
        conf.token = "test_token";
        conf.ms_server = "bns://master";
        conf.vs_server = "bns://volumeservice";
        conf.allocator_server = "bns://allocator";
        conf.vs_load_balancer = "la";
        conf.bvar_service_port = 8705;
        conf.compressor_server = "bns://commpressservice";
        conf.check_compress_type_info = false;
        int ret = _proxy->init(conf);
        assert(0 == ret);
        _proxy->reload(conf);
    }
    void TearDown() {
        _proxy.reset();
    }
private:
    std::unique_ptr<Proxy> _proxy;
};
TEST_F(TestProxy, init) {
    // use the same proxy name, failed
    ProxyConf conf;
    conf.proxy_name = "test_proxy_lib";
    conf.proxy_version = "2.0.0.2";
    conf.token = "test_token";
    conf.ms_server = "bns://master";
    conf.vs_server = "bns://volumeservice";
    conf.allocator_server = "bns://allocator";
    conf.vs_load_balancer = "la";
    conf.bvar_service_port = 8705;
    Proxy tmp;
    int ret = tmp.init(conf);
    EXPECT_EQ(-1, ret);
    // limit proxy lib num, failed
    Proxy tmp2;
    ProxyConf conf2;
    conf2.proxy_name = "test_proxy_lib2";
    conf2.proxy_version = "2.0.0.3";
    ret = tmp2.init(conf2);
    EXPECT_EQ(-1, ret);
    // port err
    conf.proxy_name = "test_proxy_lib3";
    FLAGS_max_proxy_num = 20;
    common::FLAGS_port = 0;
    Proxy tmp3;
    ret = tmp3.init(conf);
    EXPECT_EQ(-1, ret);
    // no compressor
    conf.proxy_name = "test_proxy_lib4";
    conf.compressor_server = "";
    common::FLAGS_port = 8897;
    Proxy tmp4;
    ret = tmp4.init(conf);
    EXPECT_EQ(-1, ret);
    // no allocator
    conf.proxy_name = "test_proxy_lib5";
    conf.compressor_server = "bns://commpressservice";
    conf.allocator_server = "";
    Proxy tmp5;
    ret = tmp5.init(conf);
    EXPECT_EQ(-1, ret);
    // no bvar port
    conf.proxy_name = "test_proxy_lib6";
    conf.allocator_server = "bns://allocator";
    conf.bvar_service_port = 0;
    Proxy tmp6;
    ret = tmp6.init(conf);
    EXPECT_EQ(-1, ret);
    FLAGS_max_proxy_num = 1;
}
TEST_F(TestProxy, reload) {
    ProxyConf conf;
    conf.connect_timeout_ms = 2012;
    conf.allocator_refresh_ms = 2012;
    conf.heartbeat_interval_second = 2012;
    _proxy->reload(conf);
    EXPECT_EQ(_proxy->_conf.bvar_monitor_include, "dataproxy*");
    EXPECT_EQ(_proxy->_conf.bvar_monitor_exclude, "*80*;*9999*;*cdf*;*percentiles*");
    EXPECT_EQ(_proxy.get()->_conf.connect_timeout_ms, 2012);
    EXPECT_EQ(_proxy.get()->_allocator_client.get()->_allocator_refresh_ms, 2012);
    EXPECT_EQ(_proxy.get()->_heartbeat.get()->heartbeat_interval_second, 2012);
    conf.bvar_monitor_include = "12345";
    conf.bvar_monitor_exclude = "67890";
    _proxy->reload(conf);
    EXPECT_EQ(_proxy->_conf.bvar_monitor_include, "12345");
    EXPECT_EQ(_proxy->_conf.bvar_monitor_exclude, "67890");
}
TEST_F(TestProxy, put) {
    // check failed
    {
        std::string tmp("11");
        // key_meta_size
        auto blob_info = std::make_shared<PutBlobInfo>();
        blob_info->key.resize(2048);
        blob_info->meta = "meta";
        blob_info->api_data = &tmp;
        _proxy->put(blob_info);
        EXPECT_EQ(blob_info->ret, AE_INVALID_ARGUMENT);
    }
    // compress and recheck_compress, succeed
    {
        BMOCK_CLASS_MOCK_GUARD(BlobAllocator, alloc);
        BMOCK_CLASS_MOCK_GUARD(BlobOper, put);
        std::shared_ptr<VolumeInfo> volume_info_ptr = std::make_shared<VolumeInfo>();
        auto& volume_info = * volume_info_ptr;
        volume_info.eco.type = aries::EC_RS_ISAL;
        volume_info.eco.param.k = 4;
        volume_info.eco.param.n = 8;
        _proxy.get()->_conf.recheck_compress = true;
        aries::common::CostTracker cost_tracker;
        std::string data = "11111111112222222222";
        auto blob_info = std::make_shared<PutBlobInfo>();
        blob_info->log_id = 2012;
        blob_info->cost_tracker = &cost_tracker;
        blob_info->origin_blob_len = data.size();
        blob_info->origin_blob_crc = common::Crc32CheckSum::compute(data.c_str(), data.size(), 0);
        blob_info->blob_len = blob_info->origin_blob_len;
        blob_info->blob_crc = blob_info->origin_blob_crc;
        blob_info->compress_type = 1;
        blob_info->min_compress_ratio = 1;
        blob_info->api_data = &data;
        blob_info->shard_size = 4; // 15 / 4
        EXPECT_CALL(BMOCK_NS_CLASS_OBJECT(aries, BlobAllocator, alloc), alloc(_, _, _)).WillOnce(DoAll(SetArgPointee<2>(volume_info), Return(0)));
        EXPECT_CALL(BMOCK_NS_CLASS_OBJECT(aries, BlobOper, put), put(_, _, _)).WillOnce(Return(0));
        _proxy->put(blob_info);
        LOG(NOTICE) << blob_info->cost_tracker->to_string();
        EXPECT_EQ(blob_info->ret, AE_OK);
        _proxy.get()->_conf.recheck_compress = false;
    }
    // small space, succeed
    {
        BMOCK_CLASS_MOCK_GUARD(BlobAllocator, alloc);
        BMOCK_CLASS_MOCK_GUARD(BlobOper, put);
        std::shared_ptr<VolumeInfo> volume_info_ptr = std::make_shared<VolumeInfo>();
        auto& volume_info = * volume_info_ptr;
        volume_info.eco.type = aries::EC_RS_ISAL;
        volume_info.eco.param.k = 4;
        volume_info.eco.param.n = 8;
        aries::common::CostTracker cost_tracker;
        std::string data = "11111111112222222222";
        auto blob_info = std::make_shared<PutBlobInfo>();
        blob_info->log_id = 2012;
        blob_info->cost_tracker = &cost_tracker;
        blob_info->origin_blob_len = data.size();
        blob_info->origin_blob_crc = common::Crc32CheckSum::compute(data.c_str(), data.size(), 0);
        blob_info->blob_len = blob_info->origin_blob_len;
        blob_info->blob_crc = blob_info->origin_blob_crc;
        blob_info->compress_type = 1;
        blob_info->min_compress_ratio = 0;
        blob_info->small_blob_space_name = "xx";
        blob_info->small_blob_extreme_length = 17;
        blob_info->api_data = &data;
        blob_info->shard_size = 4; // 15 / 4
        EXPECT_CALL(BMOCK_NS_CLASS_OBJECT(aries, BlobAllocator, alloc), alloc(_, _, _)).WillOnce(DoAll(SetArgPointee<2>(volume_info), Return(0)));
        EXPECT_CALL(BMOCK_NS_CLASS_OBJECT(aries, BlobOper, put), put(_, _, _)).WillOnce(Return(0));
        _proxy->put(blob_info);
        LOG(NOTICE) << blob_info->cost_tracker->to_string();
        EXPECT_EQ(blob_info->ret, AE_OK);
        EXPECT_EQ(blob_info->space_name, blob_info->small_blob_space_name);
    }
    // encode and recheck, succeed
    {
        BMOCK_CLASS_MOCK_GUARD(BlobAllocator, alloc);
        BMOCK_CLASS_MOCK_GUARD(BlobOper, put);
        std::shared_ptr<VolumeInfo> volume_info_ptr = std::make_shared<VolumeInfo>();
        auto& volume_info = * volume_info_ptr;
        volume_info.eco.type = aries::EC_RS_ISAL;
        volume_info.eco.param.k = 4;
        volume_info.eco.param.n = 8;
        _proxy.get()->_conf.recheck_ec = true;
        aries::common::CostTracker cost_tracker;
        std::string data = "11111111112222222222";
        auto blob_info = std::make_shared<PutBlobInfo>();
        blob_info->log_id = 2012;
        blob_info->cost_tracker = &cost_tracker;
        blob_info->origin_blob_len = data.size();
        blob_info->origin_blob_crc = common::Crc32CheckSum::compute(data.c_str(), data.size(), 0);
        blob_info->blob_len = blob_info->origin_blob_len;
        blob_info->blob_crc = blob_info->origin_blob_crc;
        blob_info->api_data = &data;
        blob_info->shard_size = 5; // 20 / 4
        EXPECT_CALL(BMOCK_NS_CLASS_OBJECT(aries, BlobAllocator, alloc), alloc(_, _, _)).WillOnce(DoAll(SetArgPointee<2>(volume_info), Return(0)));
        EXPECT_CALL(BMOCK_NS_CLASS_OBJECT(aries, BlobOper, put), put(_, _, _)).WillOnce(Return(0));
        _proxy->put(blob_info);
        LOG(NOTICE) << blob_info->cost_tracker->to_string();
        EXPECT_EQ(blob_info->ret, AE_OK);
        _proxy.get()->_conf.recheck_ec = false;
    }
    //encode and recheck, data size = 0, succeed
    {
        BMOCK_CLASS_MOCK_GUARD(BlobAllocator, alloc);
        BMOCK_CLASS_MOCK_GUARD(BlobOper, put);
        std::shared_ptr<VolumeInfo> volume_info_ptr = std::make_shared<VolumeInfo>();
        auto& volume_info = * volume_info_ptr;
        volume_info.eco.type = aries::EC_RS_ISAL;
        volume_info.eco.param.k = 4;
        volume_info.eco.param.n = 8;
        _proxy.get()->_conf.recheck_ec = true;
        aries::common::CostTracker cost_tracker;
        const std::string data = "";
        auto blob_info = std::make_shared<PutBlobInfo>();
        blob_info->log_id = 2012;
        blob_info->cost_tracker = &cost_tracker;
        blob_info->origin_blob_len = data.size();
        blob_info->origin_blob_crc = common::Crc32CheckSum::compute(data.c_str(), data.size(), 0);
        blob_info->blob_len = blob_info->origin_blob_len;
        blob_info->blob_crc = blob_info->origin_blob_crc;
        blob_info->api_data = &data;
        blob_info->shard_size = 0; // 0 / 4
        EXPECT_CALL(BMOCK_NS_CLASS_OBJECT(aries, BlobAllocator, alloc), alloc(_, _, _)).WillOnce(DoAll(SetArgPointee<2>(volume_info), Return(0)));
        EXPECT_CALL(BMOCK_NS_CLASS_OBJECT(aries, BlobOper, put), put(_, _, _)).WillOnce(Return(0));
        _proxy->put(blob_info);
        LOG(NOTICE) << blob_info->cost_tracker->to_string();
        EXPECT_EQ(blob_info->ret, AE_OK);
        _proxy.get()->_conf.recheck_ec = false;
    }
    // alloc failed
    {
        BMOCK_CLASS_MOCK_GUARD(BlobAllocator, alloc);
        aries::common::CostTracker cost_tracker;
        std::string data = "11111111112222222222";
        auto blob_info = std::make_shared<PutBlobInfo>();
        blob_info->log_id = 2012;
        blob_info->cost_tracker = &cost_tracker;
        blob_info->origin_blob_len = data.size();
        blob_info->origin_blob_crc = common::Crc32CheckSum::compute(data.c_str(), data.size(), 0);
        blob_info->blob_len = blob_info->origin_blob_len;
        blob_info->blob_crc = blob_info->origin_blob_crc;
        blob_info->api_data = &data;
        blob_info->shard_size = 5; // 20 / 4
        EXPECT_CALL(BMOCK_NS_CLASS_OBJECT(aries, BlobAllocator, alloc), alloc(_, _, _)).WillOnce(Return(1));
        _proxy->put(blob_info);
        LOG(NOTICE) << blob_info->cost_tracker->to_string();
        EXPECT_EQ(blob_info->ret, AE_FAIL);
    }
    // put blob failed
    {
        BMOCK_CLASS_MOCK_GUARD(BlobAllocator, alloc);
        BMOCK_CLASS_MOCK_GUARD(BlobOper, put);
        std::shared_ptr<VolumeInfo> volume_info_ptr = std::make_shared<VolumeInfo>();
        auto& volume_info = * volume_info_ptr;
        volume_info.eco.type = aries::EC_RS_ISAL;
        volume_info.eco.param.k = 4;
        volume_info.eco.param.n = 8;
        _proxy.get()->_conf.recheck_ec = true;
        _proxy.get()->_conf.recheck_compress = true;
        _proxy.get()->_conf.put_blob_max_retry = 0;
        aries::common::CostTracker cost_tracker;
        std::string data = "11111111112222222222";
        auto blob_info = std::make_shared<PutBlobInfo>();
        blob_info->log_id = 2012;
        blob_info->cost_tracker = &cost_tracker;
        blob_info->origin_blob_len = data.size();
        blob_info->origin_blob_crc = common::Crc32CheckSum::compute(data.c_str(), data.size(), 0);
        blob_info->blob_len = blob_info->origin_blob_len;
        blob_info->blob_crc = blob_info->origin_blob_crc;
        blob_info->compress_type = 1;
        blob_info->min_compress_ratio = 0;
        blob_info->api_data = &data;
        blob_info->shard_size = 4; // 15 / 4
        EXPECT_CALL(BMOCK_NS_CLASS_OBJECT(aries, BlobAllocator, alloc), alloc(_, _, _)).WillOnce(DoAll(SetArgPointee<2>(volume_info), Return(0)));
        EXPECT_CALL(BMOCK_NS_CLASS_OBJECT(aries, BlobOper, put), put(_, _, _)).WillOnce(Return(1));
        _proxy->put(blob_info);
        LOG(NOTICE) << blob_info->cost_tracker->to_string();
        EXPECT_EQ(blob_info->ret, AE_FAIL);
        _proxy.get()->_conf.recheck_ec = false;
        _proxy.get()->_conf.recheck_compress = false;
    }
}
TEST_F(TestProxy, get) {
    // get volume failed
    {
        BMOCK_CLASS_MOCK_GUARD(MetaHandler, get_volume);
        aries::common::CostTracker cost_tracker;
        GetBlobInfo blob_info;
        blob_info.cost_tracker = &cost_tracker;
        EXPECT_CALL(BMOCK_NS_CLASS_OBJECT(aries, MetaHandler, get_volume), get_volume(_, _, _, _)).WillOnce(Return(1));
        _proxy->get(&blob_info);
        LOG(NOTICE) << blob_info.cost_tracker->to_string();
        EXPECT_EQ(blob_info.ret, AE_FAIL);
    }
    // get blob failed
    {
        BMOCK_CLASS_MOCK_GUARD(MetaHandler, get_volume);
        BMOCK_CLASS_MOCK_GUARD(BlobOper, get);
        aries::common::CostTracker cost_tracker;
        GetBlobInfo blob_info;
        blob_info.cost_tracker = &cost_tracker;
        EXPECT_CALL(BMOCK_NS_CLASS_OBJECT(aries, MetaHandler, get_volume), get_volume(_, _, _, _)).WillOnce(Return(0));
        EXPECT_CALL(BMOCK_NS_CLASS_OBJECT(aries, BlobOper, get), get(_, _, _)).WillOnce(Return(1));
        _proxy->get(&blob_info);
        LOG(NOTICE) << blob_info.cost_tracker->to_string();
        EXPECT_EQ(blob_info.ret, AE_FAIL);
    }
    // range get failed by need_meta
    {
        BMOCK_CLASS_MOCK_GUARD(MetaHandler, get_volume);
        GetBlobInfo blob_info;
        aries::common::CostTracker cost_tracker;
        blob_info.cost_tracker = &cost_tracker;
        blob_info.need_range = true;
        blob_info.need_meta = true;
        blob_info.fast_range_get = true;
        blob_info.compress_type = 1;
        std::shared_ptr<VolumeInfo> volume_info_ptr = std::make_shared<VolumeInfo>();
        auto& volume_info = * volume_info_ptr;
        volume_info.shards.resize(2);
        volume_info.eco.param.k = 1;
        EXPECT_CALL(BMOCK_NS_CLASS_OBJECT(aries, MetaHandler, get_volume), get_volume(_, _, _, _)).WillOnce(DoAll(SetArgPointee<3>(volume_info_ptr), Return(0)));
        _proxy->get(&blob_info);
        LOG(NOTICE) << blob_info.cost_tracker->to_string();
        EXPECT_EQ(blob_info.ret, AE_INVALID_ARGUMENT);
    }
    // range get failed by blob_len_hint
    {
        BMOCK_CLASS_MOCK_GUARD(MetaHandler, get_volume);
        GetBlobInfo blob_info;
        aries::common::CostTracker cost_tracker;
        blob_info.cost_tracker = &cost_tracker;
        blob_info.need_range = true;
        blob_info.need_meta = false;
        blob_info.fast_range_get = true;
        blob_info.compress_type = 1;
        blob_info.offset = 0;
        blob_info.len = 10;
        blob_info.blob_len_hint = 1;
        std::shared_ptr<VolumeInfo> volume_info_ptr = std::make_shared<VolumeInfo>();
        auto& volume_info = * volume_info_ptr;
        volume_info.shards.resize(2);
        volume_info.eco.param.k = 1;
        EXPECT_CALL(BMOCK_NS_CLASS_OBJECT(aries, MetaHandler, get_volume), get_volume(_, _, _, _)).WillOnce(DoAll(SetArgPointee<3>(volume_info_ptr), Return(0)));
        _proxy->get(&blob_info);
        LOG(NOTICE) << blob_info.cost_tracker->to_string();
        EXPECT_EQ(blob_info.ret, AE_INVALID_ARGUMENT);
    }
    // blob_len_hint is 0
    {
        BMOCK_CLASS_MOCK_GUARD(MetaHandler, get_volume);
        BMOCK_CLASS_MOCK_GUARD(BlobOper, get);
        GetBlobInfo blob_info;
        aries::common::CostTracker cost_tracker;
        blob_info.cost_tracker = &cost_tracker;
        blob_info.need_range = true;
        blob_info.need_meta = false;
        blob_info.fast_range_get = true;
        blob_info.compress_type = 1;
        blob_info.offset = 0;
        blob_info.len = 10;
        blob_info.blob_len_hint = 0;
        std::shared_ptr<VolumeInfo> volume_info_ptr = std::make_shared<VolumeInfo>();
        auto& volume_info = *volume_info_ptr;
        volume_info.shards.resize(2);
        volume_info.eco.param.k = 1;
        EXPECT_CALL(BMOCK_NS_CLASS_OBJECT(aries, MetaHandler, get_volume), get_volume(_, _, _, _)).WillOnce(DoAll(SetArgPointee<3>(volume_info_ptr), Return(0)));
        EXPECT_CALL(BMOCK_NS_CLASS_OBJECT(aries, BlobOper, get), get(_, _, _)).WillOnce(Return(0));
        _proxy->get(&blob_info);
        LOG(NOTICE) << blob_info.cost_tracker->to_string();
        EXPECT_EQ(blob_info.fast_range_get, false);
    }
    // range get failed
    {
        BMOCK_CLASS_MOCK_GUARD(MetaHandler, get_volume);
        BMOCK_CLASS_MOCK_GUARD(BlobOper, get);
        GetBlobInfo blob_info;
        aries::common::CostTracker cost_tracker;
        blob_info.cost_tracker = &cost_tracker;
        blob_info.need_range = true;
        blob_info.need_meta = false;
        blob_info.need_data = true;
        blob_info.fast_range_get = true;
        blob_info.compress_type = 0;
        blob_info.blob_len = 0;
        std::shared_ptr<VolumeInfo> volume_info_ptr = std::make_shared<VolumeInfo>();
        auto& volume_info = * volume_info_ptr;
        volume_info.shards.resize(2);
        volume_info.eco.param.k = 1;
        EXPECT_CALL(BMOCK_NS_CLASS_OBJECT(aries, MetaHandler, get_volume), get_volume(_, _, _, _)).WillOnce(DoAll(SetArgPointee<3>(volume_info_ptr), Return(0)));
        _proxy->get(&blob_info);
        LOG(NOTICE) << blob_info.cost_tracker->to_string();
        EXPECT_NE(blob_info.ret, 0);
    }
    // succeed
    {
        BMOCK_CLASS_MOCK_GUARD(MetaHandler, get_volume);
        BMOCK_CLASS_MOCK_GUARD(BlobOper, get);
        aries::common::CostTracker cost_tracker;
        GetBlobInfo blob_info;
        blob_info.cost_tracker = &cost_tracker;
        EXPECT_CALL(BMOCK_NS_CLASS_OBJECT(aries, MetaHandler, get_volume), get_volume(_, _, _, _)).WillOnce(Return(0));
        EXPECT_CALL(BMOCK_NS_CLASS_OBJECT(aries, BlobOper, get), get(_, _, _)).WillOnce(Return(0));
        _proxy->get(&blob_info);
        LOG(NOTICE) << blob_info.cost_tracker->to_string();
        EXPECT_EQ(blob_info.ret, AE_OK);
    }
    // decompress fail length error
    {   
        BMOCK_CLASS_MOCK_GUARD(MetaHandler, get_volume);
        BMOCK_CLASS_MOCK_GUARD(BlobOper, get);
        aries::common::CostTracker cost_tracker;
        GetBlobInfo blob_info;
        blob_info.compress_type = 2;
        blob_info.need_data = true;
        blob_info.origin_blob_len = 1;
        blob_info.cost_tracker = &cost_tracker;
        EXPECT_CALL(BMOCK_NS_CLASS_OBJECT(aries, MetaHandler, get_volume), get_volume(_, _, _, _)).WillOnce(Return(0));
        EXPECT_CALL(BMOCK_NS_CLASS_OBJECT(aries, BlobOper, get), get(_, _, _)).WillOnce(Return(0));
        _proxy->get(&blob_info);
        LOG(NOTICE) << blob_info.cost_tracker->to_string();
        EXPECT_EQ(blob_info.ret, AE_FAIL);
    }
    // decompress fail crc error
    {
        BMOCK_CLASS_MOCK_GUARD(MetaHandler, get_volume);
        BMOCK_CLASS_MOCK_GUARD(BlobOper, get);
        aries::common::CostTracker cost_tracker;
        GetBlobInfo blob_info;
        blob_info.compress_type = 2;
        blob_info.compress_level = 1;
        blob_info.need_data = true;
        blob_info.origin_blob_len = 0;
        blob_info.origin_blob_crc = 123;
        blob_info.cost_tracker = &cost_tracker;
        EXPECT_CALL(BMOCK_NS_CLASS_OBJECT(aries, MetaHandler, get_volume), get_volume(_, _, _, _)).WillOnce(Return(0));
        EXPECT_CALL(BMOCK_NS_CLASS_OBJECT(aries, BlobOper, get), get(_, _, _)).WillOnce(Return(0));
        _proxy->get(&blob_info);
        LOG(NOTICE) << blob_info.cost_tracker->to_string();
        EXPECT_EQ(blob_info.ret, AE_FAIL);
    }
    // decompress success
    {
        BMOCK_CLASS_MOCK_GUARD(MetaHandler, get_volume);
        BMOCK_CLASS_MOCK_GUARD(BlobOper, get);
        aries::common::CostTracker cost_tracker;
        GetBlobInfo blob_info;
        blob_info.compress_type = 2;
        blob_info.compress_level = 1;
        blob_info.need_data = true;
        blob_info.origin_blob_len = 0;
        blob_info.origin_blob_crc = 0;
        blob_info.cost_tracker = &cost_tracker;
        EXPECT_CALL(BMOCK_NS_CLASS_OBJECT(aries, MetaHandler, get_volume), get_volume(_, _, _, _)).WillOnce(Return(0));
        EXPECT_CALL(BMOCK_NS_CLASS_OBJECT(aries, BlobOper, get), get(_, _, _)).WillOnce(Return(0));
        _proxy->get(&blob_info);
        LOG(NOTICE) << blob_info.cost_tracker->to_string();
        EXPECT_EQ(blob_info.ret, AE_OK);
    }
    // check exist
    {
        BMOCK_CLASS_MOCK_GUARD(MetaHandler, get_volume);
        BMOCK_CLASS_MOCK_GUARD(BlobOper, get);
        aries::common::CostTracker cost_tracker;
        GetBlobInfo blob_info;
        blob_info.cost_tracker = &cost_tracker;
        blob_info.check_exist = true;
        EXPECT_CALL(BMOCK_NS_CLASS_OBJECT(aries, MetaHandler, get_volume), get_volume(_, _, _, _)).WillOnce(Return(0));
        EXPECT_CALL(BMOCK_NS_CLASS_OBJECT(aries, BlobOper, get), get(_, _, _)).WillOnce(Return(0));
        _proxy->get(&blob_info);
        LOG(NOTICE) << blob_info.cost_tracker->to_string();
        EXPECT_EQ(blob_info.ret, AE_OK);
        EXPECT_EQ(blob_info.need_data, false);
        EXPECT_EQ(blob_info.need_meta, false);
        EXPECT_EQ(blob_info.need_range, false);
        EXPECT_EQ(blob_info.api_data, NULL);
    }
}
TEST_F(TestProxy, remove) {
    // get volume failed
    {
        BMOCK_CLASS_MOCK_GUARD(MetaHandler, get_volume);
        aries::common::CostTracker cost_tracker;
        RemoveBlobInfo blob_info;
        blob_info.cost_tracker = &cost_tracker;
        EXPECT_CALL(BMOCK_NS_CLASS_OBJECT(aries, MetaHandler, get_volume), get_volume(_, _, _, _)).WillOnce(Return(1));
        _proxy->remove(&blob_info);
        LOG(NOTICE) << blob_info.cost_tracker->to_string();
        EXPECT_EQ(blob_info.ret, AE_FAIL);
    }
    // remove blob failed
    {
        BMOCK_CLASS_MOCK_GUARD(MetaHandler, get_volume);
        BMOCK_CLASS_MOCK_GUARD(BlobOper, remove);
        aries::common::CostTracker cost_tracker;
        std::shared_ptr<VolumeInfo> volume_info_ptr = std::make_shared<VolumeInfo>();
        auto& volume_info = * volume_info_ptr;
        volume_info.shards.resize(2);
        RemoveBlobInfo blob_info;
        blob_info.cost_tracker = &cost_tracker;
        EXPECT_CALL(BMOCK_NS_CLASS_OBJECT(aries, MetaHandler, get_volume), get_volume(_, _, _, _)).WillOnce(DoAll(SetArgPointee<3>(volume_info_ptr), Return(0)));
        EXPECT_CALL(BMOCK_NS_CLASS_OBJECT(aries, BlobOper, remove), remove(_, _, _)).WillOnce(Return(1));
        _proxy->remove(&blob_info);
        LOG(NOTICE) << blob_info.cost_tracker->to_string();
        EXPECT_EQ(blob_info.ret, AE_FAIL);
    }
    // succeed
    {
        BMOCK_CLASS_MOCK_GUARD(MetaHandler, get_volume);
        BMOCK_CLASS_MOCK_GUARD(BlobOper, remove);
        aries::common::CostTracker cost_tracker;
        std::shared_ptr<VolumeInfo> volume_info_ptr = std::make_shared<VolumeInfo>();
        auto& volume_info = * volume_info_ptr;
        volume_info.shards.resize(2);
        RemoveBlobInfo blob_info;
        blob_info.cost_tracker = &cost_tracker;
        EXPECT_CALL(BMOCK_NS_CLASS_OBJECT(aries, MetaHandler, get_volume), get_volume(_, _, _, _)).WillOnce(DoAll(SetArgPointee<3>(volume_info_ptr), Return(0)));
        EXPECT_CALL(BMOCK_NS_CLASS_OBJECT(aries, BlobOper, remove), remove(_, _, _)).WillOnce(Return(0));
        _proxy->remove(&blob_info);
        LOG(NOTICE) << blob_info.cost_tracker->to_string();
        EXPECT_EQ(blob_info.ret, AE_OK);
    }
}
TEST_F(TestProxy, test_brunsli_type) {
    BMOCK_CLASS_MOCK_GUARD(BlobAllocator, alloc);
    BMOCK_CLASS_MOCK_GUARD(BlobOper, put); 
    std::shared_ptr<VolumeInfo> volume_info_ptr = std::make_shared<VolumeInfo>();
    auto& volume_info = * volume_info_ptr;
    volume_info.eco.type = aries::EC_RS_ISAL;
    volume_info.eco.param.k = 4;
    volume_info.eco.param.n = 8;
    EXPECT_CALL(BMOCK_NS_CLASS_OBJECT(aries, BlobAllocator, alloc), alloc(_, _, _))
        .WillRepeatedly(DoAll(SetArgPointee<2>(volume_info), Return(0)));
    EXPECT_CALL(BMOCK_NS_CLASS_OBJECT(aries, BlobOper, put), put(_, _, _))
        .WillRepeatedly(Return(0));
    _proxy.get()->_conf.recheck_compress = true;
    aries::common::CostTracker cost_tracker;
    {
        std::string data = "11111111112222222222333333333";
        data[0] = 0xFF;
        data[1] = 0xD8;
        auto blob_info = std::make_shared<PutBlobInfo>();
        blob_info->log_id = 2012;
        blob_info->cost_tracker = &cost_tracker;
        blob_info->origin_blob_len = data.size();
        blob_info->origin_blob_crc = common::Crc32CheckSum::compute(data.c_str(), data.size(), 0);
        blob_info->blob_len = blob_info->origin_blob_len;
        blob_info->blob_crc = blob_info->origin_blob_crc;
        blob_info->compress_type = CompressType::COMPRESS_TYPE_BRUNSLI;
        blob_info->compress_level = 8;
        blob_info->min_compress_ratio = 1.0;
        blob_info->api_data = &data;
        blob_info->shard_size = 4; // 30 / 4
        // rpc brunsli
        _proxy->_conf.default_zstd_compress_level = 15;
        _proxy->put(blob_info);
        LOG(NOTICE) << blob_info->cost_tracker->to_string();
        EXPECT_EQ(blob_info->compress_type, CompressType::COMPRESS_TYPE_NONE);
        EXPECT_EQ(blob_info->compress_level, 0); 
        // compress rpc failed not continue put
        EXPECT_EQ(blob_info->ret, AE_FAIL);
    }
    {
        // not jpg
        std::string data = "11111111112222222222333333333";
        auto blob_info = std::make_shared<PutBlobInfo>();
        blob_info->log_id = 2012;
        blob_info->cost_tracker = &cost_tracker;
        blob_info->origin_blob_len = data.size();
        blob_info->origin_blob_crc = common::Crc32CheckSum::compute(data.c_str(), data.size(), 0);
        blob_info->blob_len = blob_info->origin_blob_len;
        blob_info->blob_crc = blob_info->origin_blob_crc;
        blob_info->compress_type = CompressType::COMPRESS_TYPE_BRUNSLI;
        blob_info->min_compress_ratio = 1.0;
        blob_info->api_data = &data;
        blob_info->shard_size = 4; // 30 / 4
        blob_info->compress_type = CompressType::COMPRESS_TYPE_BRUNSLI;
        // check not jpg, not use compress
        _proxy->put(blob_info);
        LOG(NOTICE) << blob_info->cost_tracker->to_string();
        EXPECT_EQ(blob_info->compress_type, CompressType::COMPRESS_TYPE_NONE);
        EXPECT_EQ(blob_info->ret, AIE_OK);
    }
}
TEST_F(TestProxy, auto_compress_type) {
   
    BMOCK_CLASS_MOCK_GUARD(BlobAllocator, alloc);
    BMOCK_CLASS_MOCK_GUARD(BlobOper, put);
    std::shared_ptr<VolumeInfo> volume_info_ptr = std::make_shared<VolumeInfo>();
    auto& volume_info = * volume_info_ptr;
    volume_info.eco.type = aries::EC_RS_ISAL;
    volume_info.eco.param.k = 4;
    volume_info.eco.param.n = 8;
    _proxy.get()->_conf.recheck_compress = true;
    aries::common::CostTracker cost_tracker;
    std::string data = "11111111112222222222333333333";
    data[0] = 0xFF;
    data[1] = 0xD8;
    auto blob_info = std::make_shared<PutBlobInfo>();
    blob_info->log_id = 2012;
    blob_info->cost_tracker = &cost_tracker;
    blob_info->origin_blob_len = data.size();
    blob_info->origin_blob_crc = common::Crc32CheckSum::compute(data.c_str(), data.size(), 0);
    blob_info->blob_len = blob_info->origin_blob_len;
    blob_info->blob_crc = blob_info->origin_blob_crc;
    blob_info->compress_type = CompressType::COMPRESS_TYPE_AUTO_OFFLINE;
    blob_info->compress_level = 8;
    blob_info->min_compress_ratio = 1.0;
    blob_info->api_data = &data;
    blob_info->shard_size = 4; // 30 / 4
    EXPECT_CALL(BMOCK_NS_CLASS_OBJECT(aries, BlobAllocator, alloc), alloc(_, _, _))
        .WillRepeatedly(DoAll(SetArgPointee<2>(volume_info), Return(0)));
    EXPECT_CALL(BMOCK_NS_CLASS_OBJECT(aries, BlobOper, put), put(_, _, _))
        .WillRepeatedly(Return(0));
    // rpc zstd
    _proxy->_conf.default_zstd_compress_level = 15;
    _proxy->put(blob_info);
    LOG(NOTICE) << blob_info->cost_tracker->to_string();
    EXPECT_EQ(blob_info->compress_type, CompressType::COMPRESS_TYPE_NONE);
    EXPECT_EQ(blob_info->compress_level, 0);
    // compress rpc failed not continue put
    EXPECT_EQ(blob_info->ret, AE_FAIL);
    // local zstd,
    // compress success and ratio valid, use user compress level
    _proxy->_conf.default_zstd_compress_level = 2;
    blob_info->buffer = nullptr;
    blob_info->compress_type = CompressType::COMPRESS_TYPE_AUTO_OFFLINE;
    blob_info->compress_level = 4;
    blob_info->min_compress_ratio = 0;
    blob_info->msg = "succ";
    blob_info->ret = 0;
    _proxy->put(blob_info);
    LOG(TRACE) << blob_info->ret << ":" << blob_info->msg;
    EXPECT_EQ(blob_info->compress_type, CompressType::COMPRESS_TYPE_ZSTD);
    EXPECT_EQ(blob_info->compress_level, 4);
    EXPECT_EQ(blob_info->ret, AE_OK);
    // compress success and ratio valid, use default compress level
    _proxy->_conf.default_zstd_compress_level = 2;
    blob_info->buffer = nullptr;
    blob_info->compress_type = CompressType::COMPRESS_TYPE_AUTO_OFFLINE;
    blob_info->compress_level = 0;
    blob_info->min_compress_ratio = 0;
    blob_info->msg = "succ";
    blob_info->ret = 0;
    _proxy->put(blob_info);
    LOG(TRACE) << blob_info->ret << ":" << blob_info->msg;
    EXPECT_EQ(blob_info->compress_type, CompressType::COMPRESS_TYPE_ZSTD);
    EXPECT_EQ(blob_info->compress_level, 2);
    EXPECT_EQ(blob_info->ret, AE_OK);
    // compress success and ratio valid,user compress level invalid, use default zstd compress level
     _proxy->_conf.default_zstd_compress_level = 2;
    blob_info->buffer = nullptr;
    blob_info->compress_type = CompressType::COMPRESS_TYPE_AUTO_OFFLINE;
    blob_info->compress_level = 0;
    blob_info->min_compress_ratio = 0;
    blob_info->msg = "succ";
    blob_info->ret = 0;
    _proxy->put(blob_info);
    LOG(TRACE) << blob_info->ret << ":" << blob_info->msg;
    EXPECT_EQ(blob_info->compress_type, CompressType::COMPRESS_TYPE_ZSTD);
    EXPECT_EQ(blob_info->compress_level, 2);
    EXPECT_EQ(blob_info->ret, AE_OK);
     // zstd compress success but recheck fail
    _proxy->_conf.default_zstd_compress_level = 2;
    _proxy->_conf.recheck_compress = true;
    blob_info->buffer = nullptr;
    blob_info->compress_type = CompressType::COMPRESS_TYPE_AUTO_OFFLINE;
    blob_info->compress_level = 0;
    blob_info->min_compress_ratio = 0;
    blob_info->msg = "succ";
    blob_info->ret = 0;
    {
        BMOCK_CLASS_MOCK_GUARD(ZstdCompressor, decompress);
        EXPECT_CALL(BMOCK_NS_CLASS_OBJECT(aries::common, ZstdCompressor, decompress), 
                decompress(_, _, _, _, _)).WillOnce(Return(-1)); 
        _proxy->put(blob_info);
    }
    LOG(TRACE) << blob_info->ret << ":" << blob_info->msg;
    EXPECT_EQ(blob_info->compress_type, CompressType::COMPRESS_TYPE_ZSTD);
    EXPECT_EQ(blob_info->compress_level, 2);
    EXPECT_EQ(blob_info->ret, AE_FAIL);
    // brunsli compress success and no local recheck, so success
    _proxy->_conf.default_zstd_compress_level = 15;
    _proxy->_conf.recheck_compress = true;
    blob_info->buffer = nullptr;
    blob_info->compress_type = CompressType::COMPRESS_TYPE_ZSTD;
    blob_info->compress_level = 0;
    blob_info->min_compress_ratio = 0;
    blob_info->msg = "succ";
    blob_info->ret = 0;
    {
        BMOCK_CLASS_MOCK_GUARD(Compressor, compress_rpc_call);
        EXPECT_CALL(BMOCK_NS_CLASS_OBJECT(aries::common, Compressor, compress_rpc_call), 
                compress_rpc_call(_, _, _, _, _)).WillOnce(Return(base::Status::OK())); 
        _proxy->put(blob_info);
    }
    LOG(TRACE) << blob_info->ret << ":" << blob_info->msg;
    EXPECT_EQ(blob_info->compress_type, CompressType::COMPRESS_TYPE_ZSTD);
    EXPECT_EQ(blob_info->compress_level, 15);
    EXPECT_EQ(blob_info->ret, AE_OK);
    // compress success and ratio invalid, continue put
    _proxy->_conf.default_zstd_compress_level = 2;
    blob_info->buffer = nullptr;
    blob_info->compress_type = CompressType::COMPRESS_TYPE_AUTO_OFFLINE;
    blob_info->compress_level = 3;
    blob_info->min_compress_ratio = 200;
    blob_info->msg = "succ";
    blob_info->ret = 0;
    _proxy->put(blob_info);
    LOG(TRACE) << blob_info->ret << ":" << blob_info->msg;
    EXPECT_EQ(blob_info->compress_type, CompressType::COMPRESS_TYPE_NONE);
    EXPECT_EQ(blob_info->compress_level, 0);
    EXPECT_EQ(blob_info->ret, AE_OK);
    // compress failed beacuse of compress algorithm , continue put
    _proxy->_conf.default_zstd_compress_level = 8;
    blob_info->buffer = nullptr;
    blob_info->compress_type = CompressType::COMPRESS_TYPE_AUTO_OFFLINE;
    blob_info->compress_level = 2;
    blob_info->min_compress_ratio = 0;
    blob_info->msg = "succ";
    blob_info->ret = 0;
    {
        BMOCK_CLASS_MOCK_GUARD(ZstdCompressor, compress);
        BMOCK_CLASS_MOCK_GUARD(ZstdCompressor, compress_bound);
        EXPECT_CALL(BMOCK_NS_CLASS_OBJECT(aries::common, ZstdCompressor, compress), 
                compress(_, _, _, _, _)).WillOnce(Return(-1)); 
        EXPECT_CALL(BMOCK_NS_CLASS_OBJECT(aries::common, ZstdCompressor, compress_bound), 
                compress_bound(_)).WillOnce(Return(92)); 
        _proxy->put(blob_info);
    }
    LOG(TRACE) << blob_info->ret << ":" << blob_info->msg;
    EXPECT_EQ(blob_info->compress_type, CompressType::COMPRESS_TYPE_NONE);
    EXPECT_EQ(blob_info->compress_level, 0);
    EXPECT_EQ(blob_info->ret, AE_OK);
}
TEST_F(TestProxy, check_compress_type_info) {
    // check_compress_type_info
    {
        aries::common::CostTracker cost_tracker;
        GetBlobInfo blob_info;
        blob_info.need_meta = false;
        blob_info.cost_tracker = &cost_tracker;
        _proxy.get()->_conf.check_compress_type_info = false;
        _proxy->get(&blob_info);
        EXPECT_EQ(blob_info.need_meta, false);
        blob_info.need_data = true;
        _proxy.get()->_conf.check_compress_type_info = true;
        _proxy->get(&blob_info);
        EXPECT_EQ(blob_info.need_meta, true);
    }
}
TEST_F(TestProxy, gen_space_user_metrics) {
    aries::pb::DataAgentHeartbeatRequest* request = new aries::pb::DataAgentHeartbeatRequest;

    _proxy->_space_user_put_qps->put("space1", 10);
    _proxy->gen_space_user_metrics(request);

    EXPECT_EQ(request->user_io_stats_size(), 1);
    EXPECT_EQ(request->user_io_stats(0).space_name(), "space1");
    delete request;
}
}
/* vim: set ts=4 sw=4 sts=4 tw=100 */
