// Copyright (c) 2024, ByteDance Inc. All rights reserved.

#include "protocol/thrift/thrift_serdes.h"

#include <byte/util/defer.h>
#include <gtest/gtest.h>

#include "byterpc/exec_ctx.h"
#include "byterpc/rpc.h"
#include "byterpc/util/logging.h"
#include "echo_types.h"
#include "iobuf/io_rbuf_pipe.h"

namespace byterpc {

class ThriftSerdesTest : public ::testing::Test {
protected:
    std::string method_name;
    uint64_t cid;

    void SetUp() override {
        method_name = "Echo";
        cid = 1;
    }

    size_t GetBodySizeFromFramedHeader(char* data, size_t len) {
        if (len <= 4) {
            return 0;
        }
        return ntohl(*reinterpret_cast<uint32_t*>(data));
    }

    std::string GetEchoRequestBinary(example::EchoRequest* raw_request) {
        details::ThriftMessageWrapper<example::EchoRequest> raw_request_wrapper(
            const_cast<example::EchoRequest*>(raw_request));
        ThriftFramedMessage request;
        request._raw_instance = &raw_request_wrapper;
        IOBuf out_buffer;
        ThriftSerDes::PackThriftRequest(&request, method_name, cid, &out_buffer);
        return out_buffer.to_string();
    }

    std::string GetEchoResponseBinary(example::EchoResponse* raw_response) {
        details::ThriftMessageWrapper<example::EchoResponse> raw_response_wrapper(
            const_cast<example::EchoResponse*>(raw_response));
        ThriftFramedMessage response;
        response._raw_instance = &raw_response_wrapper;
        IOBuf out_buffer;
        ThriftSerDes::PackThriftResponse(&response, method_name, cid, &out_buffer);
        return out_buffer.to_string();
    }
};

TEST_F(ThriftSerdesTest, PackThriftMessageRequest) {
    // user sets raw request
    example::EchoRequest raw_request;
    raw_request.__set_data("the test data here");

    std::string buffer_str = GetEchoRequestBinary(&raw_request);

    // Case: check body size
    EXPECT_EQ(
        buffer_str.size() - 4,
        GetBodySizeFromFramedHeader(const_cast<char*>(buffer_str.data()), buffer_str.length()));

    // Case: check magic num
    uint32_t magic_num =
        ntohl(*reinterpret_cast<uint32_t*>(const_cast<char*>(buffer_str.data()) + 4));
    EXPECT_EQ(0x80010001, magic_num);

    // Case: check method length and value
    uint32_t method_length =
        ntohl(*reinterpret_cast<uint32_t*>(const_cast<char*>(buffer_str.data()) + 8));
    EXPECT_EQ(4, method_length);
    std::string name(const_cast<char*>(buffer_str.data()) + 12, 4);
    EXPECT_EQ(method_name, name);

    // Case: check seq id
    uint32_t seq_id =
        ntohl(*reinterpret_cast<uint32_t*>(const_cast<char*>(buffer_str.data()) + 16));
    EXPECT_EQ(1, seq_id);

    // Case: check thrift field type and field id
    EXPECT_EQ(::apache::thrift::protocol::TType::T_STRUCT,
              *reinterpret_cast<uint8_t*>(const_cast<char*>(buffer_str.data()) + 20));
    uint32_t field_id =
        ntohs(*reinterpret_cast<uint16_t*>(const_cast<char*>(buffer_str.data()) + 21));
    EXPECT_EQ(1 /*internal request id*/, field_id);

    // Case: compare with original thrift, which is only a struct with exterior message
    auto thrift_buffer = std::make_shared<apache::thrift::transport::TMemoryBuffer>();
    apache::thrift::protocol::TBinaryProtocolT<apache::thrift::transport::TMemoryBuffer> oprot(
        thrift_buffer);
    raw_request.write(&oprot);
    uint8_t* buf;
    uint32_t size;
    thrift_buffer->getBuffer(&buf, &size);

    char* thrift_buffer_start =
        const_cast<char*>(buffer_str.data()) + 23;  // user struct part in final framed message
    EXPECT_EQ(0, ::memcmp(thrift_buffer_start, buf, size));
}

TEST_F(ThriftSerdesTest, MatchThriftMessage) {
    // user sets raw request
    example::EchoRequest raw_request;
    raw_request.__set_data("the test data here");

    std::string buffer_str = GetEchoRequestBinary(&raw_request);
    char* data = const_cast<char*>(buffer_str.data());

    // Case: success
    EXPECT_TRUE(ThriftSerDes::MatchThriftMessage(data, buffer_str.length()));

    // Case: length not enough
    EXPECT_FALSE(
        ThriftSerDes::MatchThriftMessage(data, ThriftSerDes::FIX_THRIFT_HEADER_LENGTH - 1));

    // Case: wrong version
    uint32_t magic_num =
        ntohl(*reinterpret_cast<uint32_t*>((data + sizeof(ThriftSerDes::ThriftHead))));
    magic_num &= 0x80000000;  // clear
    *reinterpret_cast<uint32_t*>((data + sizeof(ThriftSerDes::ThriftHead))) = htonl(magic_num);
    EXPECT_FALSE(ThriftSerDes::MatchThriftMessage(data, buffer_str.length()));
}

TEST_F(ThriftSerdesTest, ParseThriftMessageHeader) {
    // user sets raw request
    example::EchoRequest raw_request;
    raw_request.__set_data("the test data here");

    std::string buffer_str = GetEchoRequestBinary(&raw_request);
    char* data = const_cast<char*>(buffer_str.data());

    uint32_t body_size = 0;
    // Case: success && length of first IOBlockRef is sufficient
    IORbufPipe p1;
    auto ref = IOBlockRef::CreateV2(buffer_str.length());
    memcpy(ref.data(), data, ref.length());
    p1.append(ref);
    EXPECT_EQ(PARSE_OK, ThriftSerDes::ParseThriftMessageHeader(&p1, &body_size));
    EXPECT_EQ(0x2e, body_size);

    // Case: length less than required length, leading to recognize failure
    IORbufPipe p2;
    ref = IOBlockRef::CreateV2(ThriftSerDes::FIX_THRIFT_HEADER_LENGTH - 1);
    memcpy(ref.data(), data, ThriftSerDes::FIX_THRIFT_HEADER_LENGTH - 1);
    p2.append(ref);
    EXPECT_EQ(PARSE_ERROR_NOT_ENOUGH_DATA, ThriftSerDes::ParseThriftMessageHeader(&p2, &body_size));

    // Case: length of first IOBlockRef is not sufficient, less than FIX_THRIFT_HEADER_LENGTH
    IORbufPipe p3;
    size_t first_blk_len = ThriftSerDes::FIX_THRIFT_HEADER_LENGTH - 1;
    size_t second_blk_len = buffer_str.length() - first_blk_len;
    ref = IOBlockRef::CreateV2(first_blk_len);
    auto ref2 = IOBlockRef::CreateV2(second_blk_len);
    memcpy(ref.data(), data, first_blk_len);
    memcpy(ref2.data(), data + first_blk_len, second_blk_len);
    p3.append(ref);
    p3.append(ref2);
    EXPECT_EQ(first_blk_len + second_blk_len, p3.length());
    EXPECT_EQ(first_blk_len, p3.front_ref().length());
    EXPECT_EQ(PARSE_OK, ThriftSerDes::ParseThriftMessageHeader(&p3, &body_size));

    IORbufPipe p4;
    p3.copy_to(&p4, p3.length());

    char origin_char = *reinterpret_cast<char*>(p3.front_ref().data() + 4);
    *reinterpret_cast<char*>(p3.front_ref().data() + 4) = 'a';
    EXPECT_EQ(PARSE_ERROR_TRY_OTHERS, ThriftSerDes::ParseThriftMessageHeader(&p3, &body_size));
    *reinterpret_cast<char*>(p3.front_ref().data() + 4) = origin_char;

    // Case: body size exceeds
    *reinterpret_cast<uint32_t*>(p4.front_ref().data()) = 0x88888888;
    EXPECT_EQ(PARSE_ERROR_NOT_ENOUGH_DATA, ThriftSerDes::ParseThriftMessageHeader(&p4, &body_size));

    // Case: length less than body size
    IORbufPipe p5;
    ref = IOBlockRef::CreateV2(buffer_str.length() - 1);
    memcpy(ref.data(), data, ref.length());
    p5.append(ref);
    EXPECT_EQ(PARSE_ERROR_NOT_ENOUGH_DATA, ThriftSerDes::ParseThriftMessageHeader(&p5, &body_size));
}

TEST_F(ThriftSerdesTest, PackThriftMessageResponse) {
    // user sets raw request
    example::EchoResponse raw_response;
    raw_response.__set_data("the test data response here");

    std::string buffer_str = GetEchoResponseBinary(&raw_response);

    // Case: check body size
    EXPECT_EQ(
        buffer_str.size() - 4,
        GetBodySizeFromFramedHeader(const_cast<char*>(buffer_str.data()), buffer_str.length()));

    // Case: check magic num
    uint32_t magic_num =
        ntohl(*reinterpret_cast<uint32_t*>(const_cast<char*>(buffer_str.data()) + 4));
    EXPECT_EQ(0x80010002, magic_num);

    // Case: check method length and value
    uint32_t method_length =
        ntohl(*reinterpret_cast<uint32_t*>(const_cast<char*>(buffer_str.data()) + 8));
    EXPECT_EQ(4, method_length);
    std::string name(const_cast<char*>(buffer_str.data()) + 12, 4);
    EXPECT_EQ(method_name, name);

    // Case: check seq id
    uint32_t seq_id =
        ntohl(*reinterpret_cast<uint32_t*>(const_cast<char*>(buffer_str.data()) + 16));
    EXPECT_EQ(1, seq_id);

    // Case: check thrift field type and field id
    EXPECT_EQ(::apache::thrift::protocol::TType::T_STRUCT,
              *reinterpret_cast<uint8_t*>(const_cast<char*>(buffer_str.data()) + 20));
    uint32_t field_id =
        ntohs(*reinterpret_cast<uint16_t*>(const_cast<char*>(buffer_str.data()) + 21));
    EXPECT_EQ(0 /*internal response id*/, field_id);

    // Case: compare with original thrift, which is only a struct with exterior message
    auto thrift_buffer = std::make_shared<apache::thrift::transport::TMemoryBuffer>();
    apache::thrift::protocol::TBinaryProtocolT<apache::thrift::transport::TMemoryBuffer> oprot(
        thrift_buffer);
    raw_response.write(&oprot);
    uint8_t* buf;
    uint32_t size;
    thrift_buffer->getBuffer(&buf, &size);

    char* thrift_buffer_start =
        const_cast<char*>(buffer_str.data()) + 23;  // user struct part in final framed message
    EXPECT_EQ(0, ::memcmp(thrift_buffer_start, buf, size));
}

TEST_F(ThriftSerdesTest, PackAndReadThriftExceptionResponse) {
    IOBuf out_buffer;
    const ErrCode error_code = EINTERNAL;
    const std::string error_text = "ErrorText42";
    ThriftSerDes::PackThriftExpectionResponse(
        method_name, cid, error_code, error_text, &out_buffer);
    std::string buffer_str = out_buffer.to_string();

    EXPECT_EQ(
        buffer_str.size() - 4,
        GetBodySizeFromFramedHeader(const_cast<char*>(buffer_str.data()), buffer_str.length()));

    // Case: check magic num
    uint32_t magic_num =
        ntohl(*reinterpret_cast<uint32_t*>(const_cast<char*>(buffer_str.data()) + 4));
    EXPECT_EQ(0x80010003, magic_num);

    // Case: check method length and value
    uint32_t method_length =
        ntohl(*reinterpret_cast<uint32_t*>(const_cast<char*>(buffer_str.data()) + 8));
    EXPECT_EQ(4, method_length);
    std::string name(const_cast<char*>(buffer_str.data()) + 12, 4);
    EXPECT_EQ(method_name, name);

    // Case: check seq id
    uint32_t seq_id =
        ntohl(*reinterpret_cast<uint32_t*>(const_cast<char*>(buffer_str.data()) + 16));
    EXPECT_EQ(1, seq_id);

    // Case: call ReadThriftException
    out_buffer.pop_front(20);
    ::apache::thrift::TApplicationException x;
    ThriftSerDes::ReadThriftException(&out_buffer, &x);
    std::string original_text;
    EXPECT_EQ(error_code, ThriftSerDes::ParseErrCode(x.what(), &original_text));
    EXPECT_EQ(original_text, error_text);
}

TEST_F(ThriftSerdesTest, ReadThriftMessageBegin) {
    // user sets raw request
    example::EchoRequest raw_request;
    raw_request.__set_data("the test data here");

    std::string buffer_str = GetEchoRequestBinary(&raw_request);
    char* data = const_cast<char*>(buffer_str.data());

    IORbufPipe p;
    auto ref = IOBlockRef::CreateV2(buffer_str.length());
    memcpy(ref.data(), data, ref.length());
    p.append(ref);

    // Case: success
    ThriftSerDes::ThriftMeta meta;
    EXPECT_TRUE(ThriftSerDes::ReadThriftMessageBegin(&p, &meta));

    EXPECT_EQ(cid, meta.cid);
    EXPECT_EQ(method_name, meta.method_name);
    EXPECT_EQ(::apache::thrift::protocol::TMessageType::T_CALL, meta.message_type);

    // Case: input data lacks
    IORbufPipe p1;
    ref = IOBlockRef::CreateV2(11);
    memcpy(ref.data(), data, ref.length());
    p1.append(ref);
    EXPECT_FALSE(ThriftSerDes::ReadThriftMessageBegin(&p1, &meta));

    IORbufPipe p2;
    ref = IOBlockRef::CreateV2(
        19); /* 20 = ThriftHead + magic_num + method_length + method_size + seq_id */
    memcpy(ref.data(), data, ref.length());
    p2.append(ref);
    EXPECT_FALSE(ThriftSerDes::ReadThriftMessageBegin(&p2, &meta));

    // Case: method name size larger than its limit
    uint32_t method_length =
        ntohl(*reinterpret_cast<uint32_t*>(const_cast<char*>(buffer_str.data()) + 8));
    EXPECT_EQ(4, method_length);
    *reinterpret_cast<uint32_t*>(const_cast<char*>(buffer_str.data()) + 8) = 0x88888888;
    IORbufPipe p3;
    ref = IOBlockRef::CreateV2(buffer_str.length());
    memcpy(ref.data(), data, ref.length());
    p3.append(ref);
    EXPECT_FALSE(ThriftSerDes::ReadThriftMessageBegin(&p3, &meta));

    // Case: type invalid
    IORbufPipe p4;
    char data2[20]{};
    data2[7] = 0x5;  // invalid type in thrift wire format
    IOBuf tmp_buf;
    tmp_buf.append(data2, sizeof(data2));
    tmp_buf.cut_to(&p4, tmp_buf.size());
    EXPECT_FALSE(ThriftSerDes::ReadThriftMessageBegin(&p4, &meta));
}

TEST_F(ThriftSerdesTest, ParseErrCode) {
    std::string mixed_text;
    std::string original_text;
    // Case: normal case
    mixed_text = "2001__error123";
    EXPECT_EQ(EINTERNAL, ThriftSerDes::ParseErrCode(mixed_text, &original_text));
    // Case: empty input
    mixed_text = "";
    EXPECT_EQ(static_cast<ErrCode>(ThriftSerDes::INVALID_ERRNO),
              ThriftSerDes::ParseErrCode(mixed_text, &original_text));
    // Case: wrong delimiter
    mixed_text = "2001_error123";
    EXPECT_EQ(static_cast<ErrCode>(ThriftSerDes::INVALID_ERRNO),
              ThriftSerDes::ParseErrCode(mixed_text, &original_text));
    // Case: shorter input
    mixed_text = "2001_";
    EXPECT_EQ(static_cast<ErrCode>(ThriftSerDes::INVALID_ERRNO),
              ThriftSerDes::ParseErrCode(mixed_text, &original_text));
    // Case: errno string isn't numeric
    mixed_text = "a2001__error123";
    EXPECT_EQ(static_cast<ErrCode>(ThriftSerDes::INVALID_ERRNO),
              ThriftSerDes::ParseErrCode(mixed_text, &original_text));
}

TEST_F(ThriftSerdesTest, PackThriftMessageCommonWithEmptyMethodName) {
    example::EchoRequest raw_request;
    raw_request.__set_data("the test data here");

    // Empty method name can also succeed when packing thrift message,
    // whereas outside we prevent this case from happening.
    const std::string empty_method_name("");
    details::ThriftMessageWrapper<example::EchoRequest> raw_request_wrapper(
        const_cast<example::EchoRequest*>(&raw_request));
    ThriftFramedMessage request;
    request._raw_instance = &raw_request_wrapper;
    IOBuf out;
    ThriftSerDes::packThriftMessageCommon(
        ::apache::thrift::protocol::TMessageType::T_REPLY, &request, empty_method_name, 0, &out);
    EXPECT_EQ(46, out.size());

    // Only modify method_name length, which would cause an increase in out2.size()
    const std::string method_name("echo");
    details::ThriftMessageWrapper<example::EchoRequest> raw_request_wrapper2(
        const_cast<example::EchoRequest*>(&raw_request));
    ThriftFramedMessage request2;
    request2._raw_instance = &raw_request_wrapper2;
    IOBuf out2;
    ThriftSerDes::packThriftMessageCommon(
        ::apache::thrift::protocol::TMessageType::T_REPLY, &request2, method_name, 0, &out2);
    EXPECT_EQ(50, out2.size());
}

TEST_F(ThriftSerdesTest, PackThriftMessageCommonWithoutRawInstance) {
    example::EchoRequest raw_request;
    raw_request.__set_data("the test data here");

    details::ThriftMessageWrapper<example::EchoRequest> raw_request_wrapper(
        const_cast<example::EchoRequest*>(&raw_request));
    ThriftFramedMessage request;
    request._raw_instance = &raw_request_wrapper;
    IOBuf out;
    ThriftSerDes::PackThriftRequest(&request, "echo", 0, &out);
    // pop out meta data, left serialized raw thrift message
    out.pop_front(20);

    ThriftFramedMessage req_to_relay;
    out.copy_to(&req_to_relay.body, out.length());
    EXPECT_EQ(req_to_relay._raw_instance, nullptr);
    IOBuf out2;
    ThriftSerDes::packThriftMessageCommon(
        ::apache::thrift::protocol::TMessageType::T_CALL, &req_to_relay, method_name, 0, &out2);
    // For a same message, its length should be exactly the same.
    EXPECT_EQ(50, out2.size());
}

}  // namespace byterpc

int main(int argc, char** argv) {
    testing::InitGoogleTest(&argc, argv);
#ifdef BYTERPC_WITH_NUMA
    byterpc::FLAGS_byterpc_enable_numa_aware = true;
    byterpc::FLAGS_byterpc_memory_pool_numa_init_region_num = "1,1";
#endif
    byterpc::InitOptions init_opt;
    BYTERPC_CHECK(0 == byterpc::ExecCtx::Init(init_opt));

    auto fini_guard = byte::defer([]() { byterpc::ExecCtx::Fini(); });
    return RUN_ALL_TESTS();
}
