/***************************************************************************
 * 
 * Copyright (c) 2017 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/
 
 
 
/**
 * @file test/test_meta_handler.cpp
 * <AUTHOR>
 * @date 2017/02/20 15:42:39
 * @brief 
 *  
 **/
#include <gtest/gtest.h>
#include "baidu/inf/aries-api/test/bmock_util.h"
#include "baidu/inf/aries-api/proxy/meta_cache.h"
#include "baidu/inf/aries-api/proxy/meta_handler.h"
#include "baidu/inf/aries-api/common/common.h"

namespace aries {
using ::testing::Return;
using ::testing::DoAll;
using ::testing::SetArgPointee;
using ::testing::_;

MAKE_BMOCK_NS_CLASS_METHOD(4, aries::pb::volumeservice, VolumeService_Stub, get_volume_info,
        void(::google::protobuf::RpcController *,
            const ::aries::pb::volumeservice::GetVolumeInfoRequest* request,
            ::aries::pb::volumeservice::GetVolumeInfoResponse* response,
            ::google::protobuf::Closure *));
MAKE_BMOCK_NS_CLASS_METHOD(4, aries::pb::volumeservice, VolumeService_Stub, get_da_info,
        void(::google::protobuf::RpcController *,
            const ::aries::pb::volumeservice::GetDataAgentInfoRequest* request,
            ::aries::pb::volumeservice::GetDataAgentInfoResponse* response,
            ::google::protobuf::Closure *));
MAKE_BMOCK_NS_CLASS_METHOD(4, aries::pb::volumeservice, VolumeService_Stub, get_space_info,
        void(::google::protobuf::RpcController *,
            const ::aries::pb::volumeservice::GetSpaceInfoRequest* request,
            ::aries::pb::volumeservice::GetSpaceInfoResponse* response,
            ::google::protobuf::Closure *));
MAKE_BMOCK_NS_CLASS_METHOD(4, aries::pb::volumeservice, VolumeService_Stub, list_allocator,
        void(::google::protobuf::RpcController *,
            const ::aries::pb::volumeservice::ListAllocatorRequest* request,
            ::aries::pb::volumeservice::ListAllocatorResponse* response,
            ::google::protobuf::Closure *));

static void mock_retry_rpc_call(google::protobuf::RpcController *control, const google::protobuf::Message *req,
        google::protobuf::Message *rep, google::protobuf::Closure *done) {
    baidu::rpc::Controller *cntl = (baidu::rpc::Controller *) control;
    cntl->SetFailed(EAGAIN, "mock again");
    if (done != NULL) {
        done->Run();
    }
}
class TestMetaHandler : public ::testing::Test {
public:
    void SetUp() {
        _meta_handler.reset(new MetaHandler());
        assert(_meta_handler != nullptr);
    }
    void TearDown() {
    }

    ProxyConf _conf;
    std::unique_ptr<MetaHandler> _meta_handler;
};

TEST_F(TestMetaHandler, init) {
    BMOCK_CLASS_MOCK_GUARD(VolumeService_Stub, get_da_info);
    // init channel failed
    _conf.vs_server = "invalid address";
    _conf.vs_load_balancer = "la";
    int ret = _meta_handler->init(&_conf);
    EXPECT_EQ(-1, ret);

    // init channel succeeded, rpc failed, return 0
    _conf.vs_server = "bns://any";
    _conf.cache_space_name = "test_space:space_test";
    ret = _meta_handler->init(&_conf);
    EXPECT_EQ(0, ret);
    ret = _meta_handler->reload(&_conf);
    EXPECT_EQ(0, ret);
    // mock get_da_info
    ::aries::pb::volumeservice::GetDataAgentInfoResponse res1;
    res1.mutable_status()->set_code(AIE_OK);
    res1.mutable_status()->set_msg("");
    ::aries::pb::volumeservice::GetDataAgentInfoResponse res2;
    res2.mutable_status()->set_code(AIE_OK);
    res2.mutable_status()->set_msg("");
    res2.set_az_name("cq_az");
    EXPECT_CALL(BMOCK_NS_CLASS_OBJECT(aries::pb, VolumeService_Stub, get_da_info),
            get_da_info(_, _, _, _))
        .WillOnce(SetArgPointee<2>(res1))
        .WillOnce(SetArgPointee<2>(res2));

    // request success, response has no az_name
    ret = _meta_handler->init(&_conf);
    EXPECT_EQ(0, ret);
    EXPECT_EQ("", _meta_handler->az_name());
    // request success, response has a az_name
    ret = _meta_handler->init(&_conf);
    EXPECT_EQ(0, ret);
    EXPECT_EQ("cq_az", _meta_handler->az_name());
}

static void failed_rpc_call(google::protobuf::RpcController *base_cntl, const google::protobuf::Message *,
        google::protobuf::Message *, google::protobuf::Closure *) {
    baidu::rpc::Controller *cntl = (baidu::rpc::Controller *) base_cntl;
    cntl->SetFailed(500, "Internal Error");
}

TEST_F(TestMetaHandler, get_volume) {
    _conf.vs_server = "bns://any";
    _conf.cache_space_name = "test_space";
    int ret = _meta_handler->init(&_conf);
    EXPECT_EQ(0, ret);
    BMOCK_CLASS_MOCK_GUARD(VolumeService_Stub, get_volume_info);

    ::aries::pb::volumeservice::GetVolumeInfoResponse failed_res;
    failed_res.mutable_status()->set_code(AIE_NOT_EXIST);
    failed_res.mutable_status()->set_msg("wrong state");
    ::aries::pb::volumeservice::GetVolumeInfoResponse res;
    res.mutable_status()->set_code(AIE_OK);
    res.set_k(9);
    res.set_n(18);
    res.set_put_quorum(12);
    res.set_delete_quorum(12);
    res.add_vlets()->set_node_addr(1);
    res.add_vlets()->set_node_addr(2);
    res.add_vlets()->set_node_addr(0);
    res.add_vlets()->set_node_addr(3);
    res.add_vlets()->set_node_addr(4);
    res.mutable_vlets(0)->set_is_alive(true);
    res.mutable_vlets(1)->set_is_alive(false);
    res.mutable_vlets(2)->set_is_alive(true);
    res.mutable_vlets(3)->set_is_alive(true);
    res.mutable_vlets(4)->set_is_alive(false);
    res.set_space_name("test_space");
    EXPECT_CALL(BMOCK_NS_CLASS_OBJECT(aries::pb, VolumeService_Stub, get_volume_info),
            get_volume_info(_, _, _, _))
        .WillOnce(Invoke(failed_rpc_call))
        .WillOnce(Invoke(mock_retry_rpc_call))
        .WillOnce(SetArgPointee<2>(failed_res))
        .WillOnce(SetArgPointee<2>(res));

    VolumeInfoPtr volume;
    // rpc failed
    ret = _meta_handler->get_volume(1234, 3, true, &volume);
    EXPECT_EQ(AIE_FAIL, ret);

     // rpc failed and need retry
    ret = _meta_handler->get_volume(1234, 3, true, &volume);
    EXPECT_EQ(AIE_AGAIN, ret);

    // request failed
    ret = _meta_handler->get_volume(1234, 3, true, &volume);
    EXPECT_EQ(AIE_NOT_EXIST, ret);

    // all ok
    ret = _meta_handler->get_volume(1234, 3, true, &volume);
    EXPECT_EQ(0, ret);
    EXPECT_EQ(res.k(), volume->eco.param.k);
    EXPECT_EQ(res.n(), volume->eco.param.n);
    EXPECT_EQ(res.put_quorum(), volume->space_info.put_quorum());
    EXPECT_EQ(res.delete_quorum(), volume->space_info.delete_quorum());
    EXPECT_FALSE(volume->use_cache);
    VolumeInfoPtr cache_volume;
    ret = _meta_handler->get_volume(1234, 3, true, &cache_volume);
    EXPECT_TRUE(cache_volume->use_cache);
    EXPECT_EQ(cache_volume->volume_id, volume->volume_id);

    // not alive addr=2 pos(2)  addr=4 pos(3)
    // not exist and addr=0 pos(not exist)
    for (auto shard : volume->shards) {
        EXPECT_NE(0, common::endpoint2int(shard.addr));
    }
    EXPECT_EQ(4, volume->shards.size());
    EXPECT_EQ(2, common::endpoint2int(volume->shards[2].addr));
    EXPECT_EQ(4, common::endpoint2int(volume->shards[3].addr));
}

TEST_F(TestMetaHandler, async_get_volume) {
    _conf.vs_server = "bns://any";
    _conf.cache_space_name = "test_space";
    int ret = _meta_handler->init(&_conf);
    EXPECT_EQ(0, ret);
    BMOCK_CLASS_MOCK_GUARD(VolumeService_Stub, get_volume_info);

    ::aries::pb::volumeservice::GetVolumeInfoResponse failed_res;
    failed_res.mutable_status()->set_code(AIE_NOT_EXIST);
    failed_res.mutable_status()->set_msg("wrong state");
    ::aries::pb::volumeservice::GetVolumeInfoResponse res;
    res.mutable_status()->set_code(AIE_OK);
    res.set_k(9);
    res.set_n(18);
    res.set_put_quorum(12);
    res.set_delete_quorum(12);
    res.add_vlets()->set_node_addr(1);
    res.add_vlets()->set_node_addr(2);
    res.add_vlets()->set_node_addr(0);
    res.add_vlets()->set_node_addr(3);
    res.add_vlets()->set_node_addr(4);
    res.mutable_vlets(0)->set_is_alive(true);
    res.mutable_vlets(1)->set_is_alive(false);
    res.mutable_vlets(2)->set_is_alive(true);
    res.mutable_vlets(3)->set_is_alive(true);
    res.mutable_vlets(4)->set_is_alive(false);
    res.set_space_name("test_space");

    ::aries::pb::volumeservice::GetVolumeInfoResponse* test_res = new ::aries::pb::volumeservice::GetVolumeInfoResponse;
    test_res->CopyFrom(res);
    auto cntl = new baidu::rpc::Controller;

    _meta_handler->on_async_get_volume_info_done(test_res, cntl, 3);
    EXPECT_CALL(BMOCK_NS_CLASS_OBJECT(aries::pb, VolumeService_Stub, get_volume_info),
            get_volume_info(_, _, _, _))
        .WillOnce(SetArgPointee<2>(failed_res));

    VolumeInfoPtr volume;
    
    LOG(TRACE) << "aysnc get volume finished";
    // rpc not exist
    ret = _meta_handler->get_volume(1234, 3, false, &volume);
    EXPECT_EQ(AIE_NOT_EXIST, ret);

    // use cache
    ret = _meta_handler->get_volume(1234, 3, true, &volume);
    EXPECT_EQ(AIE_OK, ret);
    EXPECT_EQ(res.k(), volume->eco.param.k);
    EXPECT_EQ(res.n(), volume->eco.param.n);
    EXPECT_EQ(res.put_quorum(), volume->space_info.put_quorum());
    EXPECT_EQ(res.delete_quorum(), volume->space_info.delete_quorum());
    EXPECT_TRUE(volume->use_cache);
    VolumeInfoPtr cache_volume;
    ret = _meta_handler->get_volume(1234, 3, true, &cache_volume);
    EXPECT_TRUE(cache_volume->use_cache);
    EXPECT_EQ(cache_volume->volume_id, volume->volume_id);
    
    // not alive addr=2 pos(2)  addr=4 pos(3)
    // not exist and addr=0 pos(not exist)
    for (auto shard : volume->shards) {
        EXPECT_NE(0, common::endpoint2int(shard.addr));
    }
    EXPECT_EQ(4, volume->shards.size());
    EXPECT_EQ(2, common::endpoint2int(volume->shards[2].addr));
    EXPECT_EQ(4, common::endpoint2int(volume->shards[3].addr));

}

TEST_F(TestMetaHandler, get_space) {
    _conf.vs_server = "bns://any";
    _conf.cache_space_name = "test-space";
    int ret = _meta_handler->init(&_conf);
    EXPECT_EQ(0, ret);
    BMOCK_CLASS_MOCK_GUARD(VolumeService_Stub, get_space_info);

    ::aries::pb::volumeservice::GetSpaceInfoResponse failed_res;
    failed_res.mutable_status()->set_code(AIE_FAIL);
    failed_res.mutable_status()->set_msg("wrong state");
    ::aries::pb::volumeservice::GetSpaceInfoResponse res;
    res.mutable_status()->set_code(AIE_OK);
    res.mutable_space()->set_k(9);
    res.mutable_space()->set_n(18);
    res.mutable_space()->set_put_quorum(12);
    res.mutable_space()->set_delete_quorum(12);

    EXPECT_CALL(BMOCK_NS_CLASS_OBJECT(aries::pb, VolumeService_Stub, get_space_info),
            get_space_info(_, _, _, _))
        .WillOnce(Invoke(failed_rpc_call))
        .WillOnce(Invoke(mock_retry_rpc_call))
        .WillOnce(SetArgPointee<2>(failed_res))
        .WillOnce(SetArgPointee<2>(res))
        .WillOnce(SetArgPointee<2>(failed_res)) // async
        .WillOnce(SetArgPointee<2>(failed_res));

    // rpc failed
    int code = 0;
    SpaceInfoPtr space;
    code = _meta_handler->get_space(1234, "test-space", &space);
    EXPECT_EQ(nullptr, space);
    EXPECT_EQ(AIE_FAIL, code);

     // rpc failed but need retry
    code = _meta_handler->get_space(1234, "test-space", &space);
    EXPECT_EQ(nullptr, space);
    EXPECT_EQ(AIE_AGAIN, code);

    // request failed
    code = _meta_handler->get_space(1234, "test-space", &space);
    EXPECT_EQ(nullptr, space);
    EXPECT_EQ(AIE_FAIL, code);

    // all ok
    code = _meta_handler->get_space(1234, "test-space", &space);
    EXPECT_NE(nullptr, space);
    EXPECT_EQ(AIE_OK, code);

    SpaceInfoPtr cache_space;
    code = _meta_handler->get_space(1234, "test-space", &cache_space);
    EXPECT_EQ(code, AIE_OK);
    EXPECT_EQ(cache_space.get(), space.get());

    EXPECT_EQ(res.space().k(), space->k());
    EXPECT_EQ(res.space().n(), space->n());
    EXPECT_EQ(res.space().put_quorum(), space->put_quorum());
    EXPECT_EQ(res.space().delete_quorum(), space->delete_quorum());

    ::aries::pb::volumeservice::GetSpaceInfoResponse* test_res_succ = new ::aries::pb::volumeservice::GetSpaceInfoResponse;
    test_res_succ->CopyFrom(res);
    ::aries::pb::volumeservice::GetSpaceInfoResponse* test_res_fail = new ::aries::pb::volumeservice::GetSpaceInfoResponse;
    test_res_fail->CopyFrom(res);
    test_res_fail->mutable_status()->set_code(AIE_FAIL);
    auto cntl1 = new baidu::rpc::Controller;
    cntl1->set_log_id(123123);
    auto cntl2 = new baidu::rpc::Controller;
    cntl2->set_log_id(123123);
    std::string space_name = "test-space";
    _meta_handler->on_async_get_space_info_done(test_res_succ, cntl1, space_name);
    _meta_handler->on_async_get_space_info_done(test_res_fail, cntl2, space_name);
    {
        _meta_handler->_meta_cache->_cache_timeout_s = 3;
        sleep(2);
        // need update
        SpaceInfoPtr cache_space_1;
        code = _meta_handler->get_space(1234, "test-space", &cache_space_1);
        EXPECT_EQ(code, AIE_OK);
        sleep(2);
        
        // timeout
        SpaceInfoPtr cache_space_2;
        code = _meta_handler->get_space(1234, "test-space", &cache_space_2);
        EXPECT_EQ(code, AIE_FAIL);
        _meta_handler->_meta_cache->_cache_timeout_s = 100;
    }
}

TEST_F(TestMetaHandler, list_allocator) {
    BMOCK_CLASS_MOCK_GUARD(VolumeService_Stub, list_allocator);

    std::vector<uint64_t> result;
    result.push_back(1);
    result.push_back(2);
    ::aries::pb::volumeservice::ListAllocatorResponse failed_res;
    failed_res.mutable_status()->set_code(AIE_FAIL);
    failed_res.mutable_status()->set_msg("wrong state");
    ::aries::pb::volumeservice::ListAllocatorResponse res;
    res.mutable_status()->set_code(AIE_OK);
    for (size_t i = 0; i < result.size(); ++i) {
        res.add_allocators()->set_addr(result[i]);
    }

    EXPECT_CALL(BMOCK_NS_CLASS_OBJECT(aries::pb::volumeservice, VolumeService_Stub, list_allocator),
            list_allocator(_, _, _, _))
        .WillOnce(Invoke(failed_rpc_call))
        .WillOnce(SetArgPointee<2>(failed_res))
        .WillOnce(SetArgPointee<2>(res));

    // rpc failed
    std::map<uint64_t, AllocatorInfoPtr> addrs;
    int ret = _meta_handler->list_allocator(1234, &addrs);
    EXPECT_EQ(AIE_FAIL, ret);

    // request failed
    ret = _meta_handler->list_allocator(1234, &addrs);
    EXPECT_EQ(AIE_FAIL, ret);

    // all ok
    ret = _meta_handler->list_allocator(1234, &addrs);
    EXPECT_EQ(AIE_OK, ret);
    for (size_t i = 0; i < result.size(); ++i) {
        EXPECT_EQ(result[i], addrs[i + 1]->addr);
    }
}



}

