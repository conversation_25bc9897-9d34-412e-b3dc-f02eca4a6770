// Copyright (c) 2014 Baidu, Inc.
//
// Author: <PERSON><PERSON>,<PERSON> (<EMAIL>)
// Date: 2010-12-04 11:59

#include "byterpc/util/endpoint.h"

#include <gtest/gtest.h>

#include <map>
#include <string>

#include <byte/thread/this_thread.h>
#include "byterpc/util/logging.h"

namespace byterpc {

TEST(EndPointTest, comparisons) {
    util::EndPoint p1(util::int2ip(1234), 5678);
    util::EndPoint p2 = p1;
    ASSERT_TRUE(p1 == p2 && !(p1 != p2));
    ASSERT_TRUE(p1 <= p2 && p1 >= p2 && !(p1 < p2 || p1 > p2));
    ++p2.port;
    ASSERT_TRUE(p1 != p2 && !(p1 == p2));
    ASSERT_TRUE(p1 < p2 && p2 > p1 && !(p2 <= p1 || p1 >= p2));
    --p2.port;
    p2.ip = util::int2ip(util::ip2int(p2.ip) - 1);
    ASSERT_TRUE(p1 != p2 && !(p1 == p2));
    ASSERT_TRUE(p1 > p2 && p2 < p1 && !(p1 <= p2 || p2 >= p1));
}

TEST(EndPointTest, hash_endpoint) {
    util::EndPointHash hashFunc;

    // simple endpoint
    util::EndPoint simple_ep;
    EXPECT_EQ(0, util::str2endpoint("0.0.0.0:18888", &simple_ep));
    util::EndPoint simple_ep1 = simple_ep;
    util::EndPoint simple_ep2(simple_ep);
    EXPECT_EQ(hashFunc(simple_ep), hashFunc(simple_ep1));
    EXPECT_EQ(hashFunc(simple_ep), hashFunc(simple_ep2));

    // extended endpoint
    util::EndPoint extend_ep;
    EXPECT_EQ(0, util::str2endpoint("unix:/tmp/path", &extend_ep));
    util::EndPoint p_same;
    EXPECT_EQ(0, util::str2endpoint("unix:/tmp/path", &p_same));
    EXPECT_EQ(extend_ep, p_same);

    util::EndPoint extend_ep1 = extend_ep;
    util::EndPoint extend_ep2(extend_ep);
    EXPECT_EQ(hashFunc(extend_ep), hashFunc(extend_ep1));
    EXPECT_EQ(hashFunc(extend_ep), hashFunc(extend_ep2));
    EXPECT_EQ(hashFunc(extend_ep), hashFunc(p_same));
}

TEST(EndPointTest, ip_t) {
    BYTERPC_LOG(INFO) << "INET_ADDRSTRLEN = " << INET_ADDRSTRLEN;
    BYTERPC_LOG(INFO) << "INET6_ADDRSTRLEN = " << INET6_ADDRSTRLEN;

    util::ip_t ip0;
    ASSERT_EQ(0, util::str2ip("*******", &ip0));
    ASSERT_STREQ("*******", util::ip2str(ip0).c_str());
    ASSERT_TRUE(util::is_ipv4(ip0));
    ASSERT_EQ(-1, util::str2ip("30*******", &ip0));
    ASSERT_EQ(-1, util::str2ip("1.-1.1.1", &ip0));
    ASSERT_EQ(-1, util::str2ip("1.1.-101.1", &ip0));
    ASSERT_STREQ("*******", util::ip2str(util::int2ip(1)).c_str());

    util::ip_t ip1, ip2, ip3;
    ASSERT_EQ(0, util::str2ip("***********", &ip1));
    ASSERT_EQ(0, util::str2ip("***********", &ip2));
    ASSERT_TRUE(util::is_ipv4(ip1));
    ASSERT_TRUE(util::is_ipv4(ip2));
    ip3 = ip1;
    ASSERT_LT(ip1, ip2);
    ASSERT_LE(ip1, ip2);
    ASSERT_GT(ip2, ip1);
    ASSERT_GE(ip2, ip1);
    ASSERT_TRUE(ip1 != ip2);
    ASSERT_FALSE(ip1 == ip2);
    ASSERT_TRUE(ip1 == ip3);
    ASSERT_FALSE(ip1 != ip3);

    util::ip_t ip4;
    ASSERT_EQ(0, util::str2ip("fe80::26ae:9646:fea3:457e", &ip4));
    ASSERT_STREQ("fe80::26ae:9646:fea3:457e", util::ip2str(ip4).c_str());
    ASSERT_FALSE(util::is_ipv4(ip4));

    util::ip_t ip5, ip6, ip7;
    ASSERT_EQ(0, util::str2ip("ff02::1", &ip5));
    ASSERT_EQ(0, util::str2ip("ff02::2", &ip6));
    ASSERT_FALSE(util::is_ipv4(ip5));
    ASSERT_FALSE(util::is_ipv4(ip6));
    ip7 = ip5;
    ASSERT_LT(ip5, ip6);
    ASSERT_LE(ip5, ip6);
    ASSERT_GT(ip6, ip5);
    ASSERT_GE(ip6, ip5);
    ASSERT_TRUE(ip5 != ip6);
    ASSERT_FALSE(ip5 == ip6);
    ASSERT_TRUE(ip5 == ip7);
    ASSERT_FALSE(ip5 != ip7);
}

TEST(EndPointTest, show_local_info) {
    BYTERPC_LOG(INFO) << "my_ipv4 is " << util::my_ip() << std::endl
                      << "my_ipv4_cstr is " << util::my_ip_cstr() << std::endl
                      << "my_ipv6 is " << util::my_ip(false) << std::endl
                      << "my_ipv6_cstr is " << util::my_ip_cstr(false) << std::endl
                      << "my_hostname is " << util::my_hostname();
}

TEST(EndPointTest, endpoint) {
    util::EndPoint p1;
    ASSERT_EQ(util::IP_ANY, p1.ip);
    ASSERT_EQ(0, p1.port);

    util::EndPoint p2(util::IP_NONE, -1);
    ASSERT_EQ(util::IP_NONE, p2.ip);
    ASSERT_EQ(-1, p2.port);

    util::EndPoint p3;
    ASSERT_EQ(-1, util::str2endpoint(" 127.0.0.1:-1", &p3));
    ASSERT_EQ(-1, util::str2endpoint(" 127.0.0.1:65536", &p3));
    ASSERT_EQ(0, util::str2endpoint(" 127.0.0.1:65535", &p3));
    ASSERT_EQ(0, util::str2endpoint(" 127.0.0.1:0", &p3));

    util::EndPoint p4;
    ASSERT_EQ(0, util::str2endpoint(" 127.0.0.1: 289 ", &p4));
    ASSERT_STREQ("127.0.0.1", util::ip2str(p4.ip).c_str());
    ASSERT_EQ(289, p4.port);

    util::EndPoint p5;
    for (int i = 0; i < 2; ++i) {
        ASSERT_EQ(-1, hostname2endpoint("localhost:-1", &p5, i));
        ASSERT_EQ(-1, hostname2endpoint("localhost:65536", &p5, i));
        ASSERT_EQ(0, hostname2endpoint("localhost:65535", &p5, i));
        ASSERT_EQ(0, hostname2endpoint("localhost:0", &p5, i));
    }

#ifdef BAIDU_INTERNAL
    util::EndPoint p6;
    ASSERT_EQ(0, hostname2endpoint("tc-cm-et21.tc: 289 ", &p6));
    ASSERT_STREQ("************", util::ip2str(p6.ip).c_str());
    ASSERT_EQ(289, p6.port);
#endif

    util::EndPoint p7;
    ASSERT_EQ(0, util::str2endpoint("::1", 10000, &p7));
    ASSERT_EQ(0, util::str2endpoint("[::1]", 10000, &p7));
}

TEST(EndPointTest, hash_table) {
    std::map<util::EndPoint, int> m;
    util::EndPoint ep1(util::IP_ANY, 123);
    util::EndPoint ep2(util::IP_ANY, 456);
    ++m[ep1];
    ASSERT_TRUE(m.find(ep1) != m.end());
    ASSERT_EQ(1, m.find(ep1)->second);
    ASSERT_EQ(1u, m.size());

    ++m[ep1];
    ASSERT_TRUE(m.find(ep1) != m.end());
    ASSERT_EQ(2, m.find(ep1)->second);
    ASSERT_EQ(1u, m.size());

    ++m[ep2];
    ASSERT_TRUE(m.find(ep2) != m.end());
    ASSERT_EQ(1, m.find(ep2)->second);
    ASSERT_EQ(2u, m.size());

    util::EndPoint ep3(util::int2ip(123), 123);
    util::EndPoint ep4(util::int2ip(456), 456);
    ++m[ep3];
    ASSERT_TRUE(m.find(ep3) != m.end());
    ASSERT_EQ(1, m.find(ep3)->second);
    ASSERT_EQ(3u, m.size());

    ++m[ep3];
    ASSERT_TRUE(m.find(ep3) != m.end());
    ASSERT_EQ(2, m.find(ep3)->second);
    ASSERT_EQ(3u, m.size());

    ++m[ep4];
    ASSERT_TRUE(m.find(ep4) != m.end());
    ASSERT_EQ(1, m.find(ep4)->second);
    ASSERT_EQ(4u, m.size());
}

#ifdef __x86_64__
// NOTE: getaddrinfo does NOT work in ARM env
TEST(EndPointTest, transfer) {
    for (int i = 0; i < 2; ++i) {
        util::ip_t ip;
        char hostname1[64];
        char hostname2[64];
        ASSERT_EQ(0, util::hostname2ip(NULL, &ip, i));
        // hostname1: n149-082-076.byted.org
        ASSERT_EQ(0, util::ip2hostname(ip, hostname1, sizeof(hostname1)));
        // hostname2: n149-082-076
        ASSERT_EQ(0, gethostname(hostname2, sizeof(hostname2)));
        ASSERT_EQ(0, strncmp(hostname1, hostname2, strlen(hostname2)));

        util::EndPoint ep;
        int port = 20001;
        char hostname3[64];
        std::string hostname4;
        ASSERT_EQ(0, util::hostname2endpoint(hostname2, port, &ep, i));
        ASSERT_EQ(0, util::endpoint2hostname(ep, hostname3, 64));
        ASSERT_EQ(0, util::endpoint2hostname(ep, &hostname4));
        ASSERT_EQ(0, strncmp(hostname2, hostname3, strlen(hostname2)));
        ASSERT_EQ(0, strncmp(hostname2, hostname4.c_str(), strlen(hostname2)));

        util::EndPoint ep1;
        util::EndPointStr ep_str = util::endpoint2str(ep);
        ASSERT_EQ(0, util::str2endpoint(ep_str.c_str(), &ep1));
        ASSERT_EQ(ep, ep1);

        std::string hostname5;
        ASSERT_EQ(0, util::ip2hostname(ip, &hostname5));
        ASSERT_EQ(0, gethostname(hostname2, sizeof(hostname2)));
        ASSERT_EQ(0, strncmp(hostname2, hostname5.c_str(), strlen(hostname2)));
    }
}
#endif

TEST(EndPointTest, conversion_ipv4) {
    auto addr = "*******:9912";
    util::EndPoint point;

    ASSERT_EQ(0, util::str2endpoint(addr, &point));

    auto s = endpoint2str(point);
    ASSERT_EQ(std::string(s.c_str()), std::string(addr));
}

TEST(EndPointTest, conversion_ipv6) {
    auto addr = "[::1]:9912";
    util::EndPoint point;

    ASSERT_EQ(0, util::str2endpoint(addr, &point));

    auto s = endpoint2str(point);
    ASSERT_EQ(std::string(s.c_str()), std::string(addr));
}

TEST(EndPointTest, local_ip_test) {
    util::ip_t ip;

    ASSERT_EQ(0, util::str2ip("0.0.0.0", &ip));
    ASSERT_TRUE(util::is_local_ip(ip));

    ASSERT_EQ(0, util::str2ip("127.0.0.1", &ip));
    ASSERT_TRUE(util::is_local_ip(ip));

    ASSERT_TRUE(util::is_local_ip(util::my_ip(true)));

    ASSERT_EQ(0, util::str2ip("*******", &ip));
    ASSERT_FALSE(util::is_local_ip(ip));

    ASSERT_EQ(0, util::str2ip("::", &ip));
    ASSERT_TRUE(util::is_local_ip(ip));

    ASSERT_EQ(0, util::str2ip("0::0", &ip));
    ASSERT_TRUE(util::is_local_ip(ip));

    ASSERT_EQ(0, util::str2ip("::1", &ip));
    ASSERT_TRUE(util::is_local_ip(ip));

    ASSERT_TRUE(util::is_local_ip(util::my_ip(false)));

    ASSERT_EQ(0, util::str2ip("1::1", &ip));
    ASSERT_FALSE(util::is_local_ip(ip));
}

TEST(EndPointTest, tcp_connect) {
    util::EndPoint ep{util::my_ip(), 9913};
    int listend_fd = util::tcp_listen(ep, true, true);
    EXPECT_GT(listend_fd, 0);
    int connect_fd = util::tcp_connect(ep, nullptr);
    EXPECT_GT(connect_fd, 0);
}

TEST(EndPointTest, is_ip_any) {
    util::ip_t ip;

    ASSERT_EQ(0, util::str2ip("0.0.0.0", &ip));
    ASSERT_TRUE(util::is_ip_any(ip));

    ASSERT_EQ(0, util::str2ip("::", &ip));
    ASSERT_TRUE(util::is_ip_any(ip));

    ASSERT_EQ(0, util::str2ip("*******", &ip));
    ASSERT_FALSE(util::is_ip_any(ip));

    ASSERT_EQ(0, util::str2ip("::1", &ip));
    ASSERT_FALSE(util::is_ip_any(ip));
}

}  // namespace byterpc
