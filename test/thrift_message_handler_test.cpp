// Copyright (c) 2024, ByteDance Inc. All rights reserved.

#include <byte/util/defer.h>
#include <gtest/gtest.h>

#include <byte/thread/base_thread_pool.h>
#include <memory>

#include "byterpc/callback.h"
#include "byterpc/exec_ctx.h"
#include "byterpc/io_buf.h"
#include "byterpc/protocol/thrift/thrift_message.h"
#include "byterpc/protocol/thrift/thrift_service.h"
#include "byterpc/rpc.h"
#include "byterpc/util/closure_guard.h"
#include "byterpc/util/endpoint.h"
#include "byterpc/util/logging.h"
#include "echo_types.h"
#include "iobuf/io_rbuf_pipe.h"
#include "protocol/thrift/thrift_client_message_handler.h"
#include "protocol/thrift/thrift_server_message_handler.h"
#include "rpc/client_socket.h"
#include "rpc/server_socket.h"
#include "rpc/socket.h"
#include "status.pb.h"

namespace byterpc {

// Background thread pool
std::unique_ptr<byte::BaseThreadPool> g_thread_pool;

static constexpr const char* kEcho = "Echo";
static constexpr const char* kEchoAlias = "echo";
static constexpr const char* kEchoCrossThread = "EchoCrossThread";
static constexpr const char* kEchoException = "EchoException";
static constexpr const char* kEchoExceptionCrossThread = "EchoExceptionCrossThread";

class SimpleTransport : public Transport {
public:
    SimpleTransport()
        : _socket(nullptr), _rbuf_pipe(nullptr), _owner_ref(0), _fail_send_msg(false) {}

    ~SimpleTransport() {}

    util::EndPoint local_side() const override {
        return util::EndPoint();
    }

    util::EndPoint remote_side() const override {
        return util::EndPoint();
    }

    TransportType GetTransportType() const override {
        return TYPE_KERNEL_TCP;
    }

    bool StartRead(size_t read_limit) override {
        return true;
    }

    void SetIORbufPipe(IORbufPipe* rbuf_pipe) {
        _rbuf_pipe = rbuf_pipe;
    }

    ssize_t StartWrite(std::unique_ptr<IOBuf>&& b) {
        size_t msg_size = b->size();
        b->cut_to(_rbuf_pipe, b->length());
        if (_fail_send_msg) {
            return msg_size;
        } else {
            // return 0 indicate send all msg successfully
            return 0;
        }
    }

    ssize_t StartWrite(IOBlockRef&& first,
                       std::unique_ptr<IOBuf>&& second,
                       std::unique_ptr<IOBuf>&& third,
                       std::vector<std::pair<std::unique_ptr<IOBuf>, RdmaBufInfo>>&&
                           rdma_write_bufs = {}) override {
        auto buf = std::make_unique<IOBuf>();
        if (first.size() > 0) {
            buf->append(std::move(first));
        }
        if (second) {
            buf->append(*second);
        }
        if (third) {
            buf->append(*third);
        }
        return StartWrite(std::move(buf));
    }

    void ResetSocket(Socket* replacement) override {}

    void HandleReadEvent() override {}

    void HandleWriteEvent() override {}

    int GetFD() const override {
        return 0;
    }

    uint32_t NumOfResets() const override {
        return 0;
    }

    void Reset(int error_code) override {}

    int PreConnect() override {
        return 0;
    }

    bool IsConnected() override {
        return false;
    };

    void OwnerRef() override {
        ++_owner_ref;
    }

    void OwnerUnref() override {
        if (--_owner_ref == 0) {
            delete this;
        }
    }

    void SetSocket(Socket* socket) {
        _socket = socket;
    }

    void SetSendMsgFail(bool send_fail) {
        _fail_send_msg = send_fail;
    }

private:
    Socket* _socket;
    IORbufPipe* _rbuf_pipe;
    int _owner_ref;
    bool _fail_send_msg;
};

class MyException : public std::exception {
public:
    const char* what() const noexcept override {
        return "My custom exception occurred!";
    }
};
class MyEchoService : public byterpc::ThriftService {
public:
    explicit MyEchoService(bool set_fail = false) : _set_fail(set_fail) {}

    void ProcessThriftFramedRequest(byterpc::Controller* cntl,
                                    byterpc::ThriftFramedMessage* request,
                                    byterpc::ThriftFramedMessage* response,
                                    ::google::protobuf::Closure* done) override {
        const std::string method_name = cntl->ThriftMethodName();
        if (method_name == kEcho) {
            return Echo(cntl,
                        request->Cast<example::EchoRequest>(),
                        response->Cast<example::EchoResponse>(),
                        done);
        } else if (method_name == kEchoAlias) {
            return Echo(cntl,
                        request->Cast<example::EchoRequest>(),
                        response->Cast<example::EchoResponse>(),
                        done);
        } else if (method_name == kEchoCrossThread) {
            return EchoCrossThread(cntl,
                                   request->Cast<example::EchoRequest>(),
                                   response->Cast<example::EchoResponse>(),
                                   done);
        } else if (method_name == kEchoException) {
            return EchoException(cntl,
                                 request->Cast<example::EchoRequest>(),
                                 response->Cast<example::EchoResponse>(),
                                 done);
        } else if (method_name == kEchoExceptionCrossThread) {
            return EchoExceptionCrossThread(cntl,
                                            request->Cast<example::EchoRequest>(),
                                            response->Cast<example::EchoResponse>(),
                                            done);
        } else {
            cntl->SetFailed(byterpc::ENOMETHOD, "request a non-existent thrift method");
            done->Run();
            return;
        }
    }

    void Echo(byterpc::Controller* cntl,
              const example::EchoRequest* req,
              example::EchoResponse* res,
              google::protobuf::Closure* done) {
        byterpc::util::ClosureGuard done_guard(done);
        res->data = req->data + " (Echo)";
        if (_set_fail) {
            cntl->SetFailed(ELIMIT, "mock error");
        }
    }

    void EchoCrossThread(byterpc::Controller* cntl,
                         const example::EchoRequest* req,
                         example::EchoResponse* res,
                         google::protobuf::Closure* done) {
        if (g_thread_pool) {
            g_thread_pool->AddTask(NewClosure(this, &MyEchoService::exec_crossthread_task, done));
        }
    }

    void EchoException(byterpc::Controller* cntl,
                       const example::EchoRequest* req,
                       example::EchoResponse* res,
                       google::protobuf::Closure* done) {
        byterpc::util::ClosureGuard done_guard(done);
        throw MyException();
    }

    void EchoExceptionCrossThread(byterpc::Controller* cntl,
                                  const example::EchoRequest* req,
                                  example::EchoResponse* res,
                                  google::protobuf::Closure* done) {
        if (g_thread_pool) {
            g_thread_pool->AddTask(NewClosure(this, &MyEchoService::exec_crossthread_task, done));
        }
        // But still raise exception in byterpc thread
        throw MyException();
    }

private:
    void exec_crossthread_task(google::protobuf::Closure* done) {
        byte::ThisThread::SleepInMs(2);
        done->Run();
    }

    bool _set_fail;
};

void MyEchoCallback(Controller* cntl, int error_code, int* num) {
    EXPECT_EQ(cntl->ErrorCode(), error_code);
    ++(*num);
}

void MyEchoCallback2(Controller* cntl,
                     int error_code,
                     int* num,
                     ThriftClientMessageHandler* client_msg_handler,
                     ClientSocket* client_socket,
                     ThriftFramedMessage* req,
                     ThriftFramedMessage* resp) {
    EXPECT_EQ(cntl->ErrorCode(), error_code);
    ++(*num);

    // test a request can deal overcrowded even though it is already timeout
    Controller* cntl2 = client_msg_handler->CreateSessionController(1, CrcMode::TYPE_NONE);
    cntl2->SetThriftMethodName(kEcho);
    EventRegistry* ev_reg = ExecCtx::GetOrNewThreadEventRegistry(TYPE_KERNEL_TCP);
    static_cast<StatefulController*>(cntl2)->SetupTimeoutEventHandler(ev_reg, 0);
    google::protobuf::Closure* done =
        ::byterpc::NewCallback<Controller*, int, int*>(&MyEchoCallback, cntl2, EOVERCROWDED, num);
    static_cast<ThriftRpcCall*>(cntl2)->IssueRPC(client_socket, nullptr, req, resp, done);
    // wait overcrowded event timeout
    if (*num == 5)
        usleep(10);
}

// Normal case
TEST(ThriftMessageHandlerTest, Success) {
    ExecCtx ctx(LOOP_IF_POSSIBLE);
    ExecCtx::GetOrNewThreadEventRegistry(TYPE_KERNEL_TCP);
    SimpleTransport* client_trans = new SimpleTransport();
    std::unique_ptr<IORbufPipe> req_rbuf_pipe = std::make_unique<IORbufPipe>();
    client_trans->SetIORbufPipe(req_rbuf_pipe.get());

    ClientSocket client_socket(util::EndPoint(), nullptr, nullptr, client_trans);
    client_socket.OwnerRef();
    client_trans->SetSocket(&client_socket);

    // client sends requests
    int kNum = 5;
    std::vector<ThriftFramedMessage> req(kNum);
    std::vector<ThriftFramedMessage> resp(kNum);
    for (int i = 0; i < kNum; ++i) {
        auto raw_req_wrapper = new details::ThriftMessageHolder<example::EchoRequest>();
        req[i]._raw_instance = raw_req_wrapper;
        req[i]._own_raw_instance = true;
        auto raw_resp_wrapper = new details::ThriftMessageHolder<example::EchoResponse>();
        resp[i]._raw_instance = raw_resp_wrapper;
        resp[i]._own_raw_instance = true;
    }
    ThriftClientMessageHandler client_msg_handler;
    int num = 0;
    for (int i = 0; i < kNum; ++i) {
        Controller* cntl = client_msg_handler.CreateSessionController(1000, CrcMode::TYPE_NONE);
        if (i % 2 == 0) {
            cntl->SetThriftMethodName(kEcho);
        } else {
            cntl->SetThriftMethodName(kEchoCrossThread);
        }
        EXPECT_EQ(cntl->request_protocol(), PROTOCOL_THRIFT);
        google::protobuf::Closure* done =
            ::byterpc::NewCallback<Controller*, int, int*>(&MyEchoCallback, cntl, 0, &num);
        static_cast<ThriftRpcCall*>(cntl)->IssueRPC(
            &client_socket, nullptr, &req[i], &resp[i], done);
    }

    // server processes requests and sends back responses
    RpcServiceRegistry svc_reg;
    MyEchoService* thrift_svc = new MyEchoService();
    ServiceOptions option;
    option.ownership = SERVER_OWNS_SERVICE;
    svc_reg.RegisterService(thrift_svc, option);
    ServerSocket server_socket(nullptr, &svc_reg, nullptr);
    SimpleTransport* server_trans = new SimpleTransport();
    std::unique_ptr<IORbufPipe> resp_rbuf_pipe = std::make_unique<IORbufPipe>();
    server_trans->SetIORbufPipe(resp_rbuf_pipe.get());
    server_trans->SetSocket(&server_socket);
    server_socket.ReadyToServe(server_trans);

    ThriftServerMessageHandler server_msg_handler;
    EXPECT_EQ(PARSE_ERROR_NOT_ENOUGH_DATA,
              server_msg_handler.HandleRequest(&server_socket, req_rbuf_pipe.get()));

    byte::ThisThread::SleepInMs(1000);
    // Execute remote tasks of cross thread response
    for (int i = 0; i < 64; ++i) {
        ExecCtx::LoopOnce();
    }

    // client receives responses
    EXPECT_EQ(PARSE_ERROR_NOT_ENOUGH_DATA,
              client_msg_handler.HandleResponse(&client_socket, resp_rbuf_pipe.get()));

    EXPECT_EQ(kNum, num);
}

// Send some requests and server returns ELIMIT error
TEST(ThriftMessageHandlerTest, SetFailDeliberately) {
    ExecCtx ctx(LOOP_IF_POSSIBLE);
    SimpleTransport* client_trans = new SimpleTransport();
    std::unique_ptr<IORbufPipe> req_rbuf_pipe = std::make_unique<IORbufPipe>();
    client_trans->SetIORbufPipe(req_rbuf_pipe.get());

    ClientSocket client_socket(util::EndPoint(), nullptr, nullptr, client_trans);
    client_socket.OwnerRef();
    client_trans->SetSocket(&client_socket);

    // client sends requests
    int kNum = 5;
    std::vector<ThriftFramedMessage> req(kNum);
    std::vector<ThriftFramedMessage> resp(kNum);
    for (int i = 0; i < kNum; ++i) {
        auto raw_req_wrapper = new details::ThriftMessageHolder<example::EchoRequest>();
        req[i]._raw_instance = raw_req_wrapper;
        req[i]._own_raw_instance = true;
        auto raw_resp_wrapper = new details::ThriftMessageHolder<example::EchoResponse>();
        resp[i]._raw_instance = raw_resp_wrapper;
        resp[i]._own_raw_instance = true;
    }
    ThriftClientMessageHandler client_msg_handler;
    int num = 0;
    for (int i = 0; i < kNum; ++i) {
        Controller* cntl = client_msg_handler.CreateSessionController(1000, CrcMode::TYPE_NONE);
        cntl->SetThriftMethodName(kEcho);
        EXPECT_EQ(cntl->request_protocol(), PROTOCOL_THRIFT);
        google::protobuf::Closure* done =
            ::byterpc::NewCallback<Controller*, int, int*>(&MyEchoCallback, cntl, ELIMIT, &num);
        static_cast<ThriftRpcCall*>(cntl)->IssueRPC(
            &client_socket, nullptr, &req[i], &resp[i], done);
    }

    // server processes requests and sends back responses
    RpcServiceRegistry svc_reg;
    MyEchoService* thrift_svc = new MyEchoService(true);
    ServiceOptions option;
    option.ownership = SERVER_OWNS_SERVICE;
    svc_reg.RegisterService(thrift_svc, option);
    ServerSocket server_socket(nullptr, &svc_reg, nullptr);
    SimpleTransport* server_trans = new SimpleTransport();
    std::unique_ptr<IORbufPipe> resp_rbuf_pipe = std::make_unique<IORbufPipe>();
    server_trans->SetIORbufPipe(resp_rbuf_pipe.get());
    server_trans->SetSocket(&server_socket);
    server_socket.ReadyToServe(server_trans);

    ThriftServerMessageHandler server_msg_handler;
    EXPECT_EQ(PARSE_ERROR_NOT_ENOUGH_DATA,
              server_msg_handler.HandleRequest(&server_socket, req_rbuf_pipe.get()));

    // client receives responses
    EXPECT_EQ(PARSE_ERROR_NOT_ENOUGH_DATA,
              client_msg_handler.HandleResponse(&client_socket, resp_rbuf_pipe.get()));

    EXPECT_EQ(kNum, num);
}

TEST(ThriftMessageHandlerTest, ServiceNotRegistered) {
    ExecCtx ctx(LOOP_IF_POSSIBLE);
    SimpleTransport* client_trans = new SimpleTransport();
    std::unique_ptr<IORbufPipe> req_rbuf_pipe = std::make_unique<IORbufPipe>();
    client_trans->SetIORbufPipe(req_rbuf_pipe.get());

    ClientSocket client_socket(util::EndPoint(), nullptr, nullptr, client_trans);
    client_socket.OwnerRef();
    client_trans->SetSocket(&client_socket);

    // client sends requests
    int kNum = 5;
    std::vector<ThriftFramedMessage> req(kNum);
    std::vector<ThriftFramedMessage> resp(kNum);
    for (int i = 0; i < kNum; ++i) {
        auto raw_req_wrapper = new details::ThriftMessageHolder<example::EchoRequest>();
        req[i]._raw_instance = raw_req_wrapper;
        req[i]._own_raw_instance = true;
        auto raw_resp_wrapper = new details::ThriftMessageHolder<example::EchoResponse>();
        resp[i]._raw_instance = raw_resp_wrapper;
        resp[i]._own_raw_instance = true;
    }
    ThriftClientMessageHandler client_msg_handler;
    int num = 0;
    for (int i = 0; i < kNum; ++i) {
        Controller* cntl = client_msg_handler.CreateSessionController(1000, CrcMode::TYPE_NONE);
        cntl->SetThriftMethodName(kEcho);
        EXPECT_EQ(cntl->request_protocol(), PROTOCOL_THRIFT);
        google::protobuf::Closure* done =
            ::byterpc::NewCallback<Controller*, int, int*>(&MyEchoCallback, cntl, ENOSERVICE, &num);
        static_cast<ThriftRpcCall*>(cntl)->IssueRPC(
            &client_socket, nullptr, &req[i], &resp[i], done);
    }

    // server processes requests and sends back responses
    // NOT register thrift service here
    RpcServiceRegistry svc_reg;
    ServerSocket server_socket(nullptr, &svc_reg, nullptr);
    SimpleTransport* server_trans = new SimpleTransport();
    std::unique_ptr<IORbufPipe> resp_rbuf_pipe = std::make_unique<IORbufPipe>();
    server_trans->SetIORbufPipe(resp_rbuf_pipe.get());
    server_trans->SetSocket(&server_socket);
    server_socket.ReadyToServe(server_trans);

    ThriftServerMessageHandler server_msg_handler;
    EXPECT_EQ(PARSE_ERROR_NOT_ENOUGH_DATA,
              server_msg_handler.HandleRequest(&server_socket, req_rbuf_pipe.get()));

    // client receives responses
    EXPECT_EQ(PARSE_ERROR_NOT_ENOUGH_DATA,
              client_msg_handler.HandleResponse(&client_socket, resp_rbuf_pipe.get()));

    EXPECT_EQ(kNum, num);
}

TEST(ThriftMessageHandlerTest, InvalidMethodName) {
    ExecCtx ctx(LOOP_IF_POSSIBLE);
    SimpleTransport* client_trans = new SimpleTransport();
    std::unique_ptr<IORbufPipe> req_rbuf_pipe = std::make_unique<IORbufPipe>();
    client_trans->SetIORbufPipe(req_rbuf_pipe.get());

    ClientSocket client_socket(util::EndPoint(), nullptr, nullptr, client_trans);
    client_socket.OwnerRef();
    client_trans->SetSocket(&client_socket);

    // client sends requests
    int kNum = 5;
    std::vector<ThriftFramedMessage> req(kNum);
    std::vector<ThriftFramedMessage> resp(kNum);
    for (int i = 0; i < kNum; ++i) {
        auto raw_req_wrapper = new details::ThriftMessageHolder<example::EchoRequest>();
        req[i]._raw_instance = raw_req_wrapper;
        req[i]._own_raw_instance = true;
        auto raw_resp_wrapper = new details::ThriftMessageHolder<example::EchoResponse>();
        resp[i]._raw_instance = raw_resp_wrapper;
        resp[i]._own_raw_instance = true;
    }
    ThriftClientMessageHandler client_msg_handler;
    int num = 0;
    for (int i = 0; i < kNum; ++i) {
        Controller* cntl = client_msg_handler.CreateSessionController(1000, CrcMode::TYPE_NONE);
        cntl->SetThriftMethodName("InvalidMethodName");
        EXPECT_EQ(cntl->request_protocol(), PROTOCOL_THRIFT);
        google::protobuf::Closure* done =
            ::byterpc::NewCallback<Controller*, int, int*>(&MyEchoCallback, cntl, ENOMETHOD, &num);
        static_cast<ThriftRpcCall*>(cntl)->IssueRPC(
            &client_socket, nullptr, &req[i], &resp[i], done);
    }

    // server processes requests and sends back responses
    RpcServiceRegistry svc_reg;
    MyEchoService* thrift_svc = new MyEchoService();
    ServiceOptions option;
    option.ownership = SERVER_OWNS_SERVICE;
    svc_reg.RegisterService(thrift_svc, option);
    ServerSocket server_socket(nullptr, &svc_reg, nullptr);
    SimpleTransport* server_trans = new SimpleTransport();
    std::unique_ptr<IORbufPipe> resp_rbuf_pipe = std::make_unique<IORbufPipe>();
    server_trans->SetIORbufPipe(resp_rbuf_pipe.get());
    server_trans->SetSocket(&server_socket);
    server_socket.ReadyToServe(server_trans);

    ThriftServerMessageHandler server_msg_handler;
    EXPECT_EQ(PARSE_ERROR_NOT_ENOUGH_DATA,
              server_msg_handler.HandleRequest(&server_socket, req_rbuf_pipe.get()));

    // client receives responses
    EXPECT_EQ(PARSE_ERROR_NOT_ENOUGH_DATA,
              client_msg_handler.HandleResponse(&client_socket, resp_rbuf_pipe.get()));

    EXPECT_EQ(kNum, num);
}

TEST(ThriftMessageHandlerTest, ServerOvercrowded) {
    gflags::FlagSaver saver;
    ExecCtx ctx(LOOP_IF_POSSIBLE);
    SimpleTransport* client_trans = new SimpleTransport();
    std::unique_ptr<IORbufPipe> req_rbuf_pipe = std::make_unique<IORbufPipe>();
    client_trans->SetIORbufPipe(req_rbuf_pipe.get());

    ClientSocket client_socket(util::EndPoint(), nullptr, nullptr, client_trans);
    client_socket.OwnerRef();
    client_trans->SetSocket(&client_socket);

    // client sends requests
    int kNum = 5;
    std::vector<ThriftFramedMessage> req(kNum);
    std::vector<ThriftFramedMessage> resp(kNum);
    for (int i = 0; i < kNum; ++i) {
        auto raw_req_wrapper = new details::ThriftMessageHolder<example::EchoRequest>();
        req[i]._raw_instance = raw_req_wrapper;
        req[i]._own_raw_instance = true;
        auto raw_resp_wrapper = new details::ThriftMessageHolder<example::EchoResponse>();
        resp[i]._raw_instance = raw_resp_wrapper;
        resp[i]._own_raw_instance = true;
    }
    ThriftClientMessageHandler client_msg_handler;
    int num = 0;
    for (int i = 0; i < kNum; ++i) {
        Controller* cntl = client_msg_handler.CreateSessionController(1000, CrcMode::TYPE_NONE);
        cntl->SetThriftMethodName(kEcho);
        EXPECT_EQ(cntl->request_protocol(), PROTOCOL_THRIFT);
        const int expected_error = i == 0 ? 0 : EOVERCROWDED;
        google::protobuf::Closure* done = ::byterpc::NewCallback<Controller*, int, int*>(
            &MyEchoCallback, cntl, expected_error, &num);
        static_cast<ThriftRpcCall*>(cntl)->IssueRPC(
            &client_socket, nullptr, &req[i], &resp[i], done);
    }

    // server processes requests and sends back responses
    RpcServiceRegistry svc_reg;
    MyEchoService* thrift_svc = new MyEchoService();
    ServiceOptions option;
    option.ownership = SERVER_OWNS_SERVICE;
    svc_reg.RegisterService(thrift_svc, option);
    ServerSocket server_socket(nullptr, &svc_reg, nullptr);
    SimpleTransport* server_trans = new SimpleTransport();
    std::unique_ptr<IORbufPipe> resp_rbuf_pipe = std::make_unique<IORbufPipe>();
    server_trans->SetIORbufPipe(resp_rbuf_pipe.get());
    server_trans->SetSocket(&server_socket);
    server_socket.ReadyToServe(server_trans);

    ThriftServerMessageHandler server_msg_handler;
    FLAGS_byterpc_socket_max_unwritten_bytes = 0;
    server_trans->SetSendMsgFail(true);
    EXPECT_EQ(PARSE_ERROR_NOT_ENOUGH_DATA,
              server_msg_handler.HandleRequest(&server_socket, req_rbuf_pipe.get()));

    // client receives responses
    EXPECT_EQ(PARSE_ERROR_NOT_ENOUGH_DATA,
              client_msg_handler.HandleResponse(&client_socket, resp_rbuf_pipe.get()));

    EXPECT_EQ(kNum, num);
}

//
TEST(ThriftMessageHandlerTest, ControllerNotFound) {
    SimpleTransport* client_trans = new SimpleTransport();
    std::unique_ptr<IORbufPipe> req_rbuf_pipe = std::make_unique<IORbufPipe>();
    client_trans->SetIORbufPipe(req_rbuf_pipe.get());

    ClientSocket client_socket(util::EndPoint(), nullptr, nullptr, client_trans);
    client_socket.OwnerRef();
    client_trans->SetSocket(&client_socket);

    // client sends requests
    int kNum = 5;
    std::vector<ThriftFramedMessage> req(kNum);
    std::vector<ThriftFramedMessage> resp(kNum);
    for (int i = 0; i < kNum; ++i) {
        auto raw_req_wrapper = new details::ThriftMessageHolder<example::EchoRequest>();
        req[i]._raw_instance = raw_req_wrapper;
        req[i]._own_raw_instance = true;
        auto raw_resp_wrapper = new details::ThriftMessageHolder<example::EchoResponse>();
        resp[i]._raw_instance = raw_resp_wrapper;
        resp[i]._own_raw_instance = true;
    }
    ThriftClientMessageHandler client_msg_handler;
    int num = 0;
    for (int i = 0; i < kNum; ++i) {
        Controller* cntl = client_msg_handler.CreateSessionController(1000, CrcMode::TYPE_NONE);
        cntl->SetThriftMethodName(kEcho);
        EXPECT_EQ(cntl->request_protocol(), PROTOCOL_THRIFT);
        google::protobuf::Closure* done =
            ::byterpc::NewCallback<Controller*, int, int*>(&MyEchoCallback, cntl, -1, &num);
        static_cast<ThriftRpcCall*>(cntl)->IssueRPC(
            &client_socket, nullptr, &req[i], &resp[i], done);
        // delete cntl from client_socket to test controller not find in client_socket
        static_cast<ThriftRpcCall*>(cntl)->SetFailed(-1, "auto set failed");
    }

    num = 0;
    // server processes requests and sends back responses
    RpcServiceRegistry svc_reg;
    MyEchoService* thrift_svc = new MyEchoService();
    ServiceOptions option;
    option.ownership = SERVER_OWNS_SERVICE;
    svc_reg.RegisterService(thrift_svc, option);
    ServerSocket server_socket(nullptr, &svc_reg, nullptr);
    SimpleTransport* server_trans = new SimpleTransport();
    std::unique_ptr<IORbufPipe> resp_rbuf_pipe = std::make_unique<IORbufPipe>();
    server_trans->SetIORbufPipe(resp_rbuf_pipe.get());
    server_trans->SetSocket(&server_socket);
    server_socket.ReadyToServe(server_trans);

    ThriftServerMessageHandler server_msg_handler;
    EXPECT_EQ(PARSE_ERROR_NOT_ENOUGH_DATA,
              server_msg_handler.HandleRequest(&server_socket, req_rbuf_pipe.get()));

    // client receives responses
    EXPECT_EQ(PARSE_ERROR_NOT_ENOUGH_DATA,
              client_msg_handler.HandleResponse(&client_socket, resp_rbuf_pipe.get()));

    EXPECT_EQ(0, num);
}

TEST(ThriftMessageHandlerTest, ClientOvercrowded) {
    gflags::FlagSaver saver;
    FLAGS_byterpc_socket_max_unwritten_bytes = 0;

    SimpleTransport* client_trans = new SimpleTransport();
    ClientSocket client_socket(util::EndPoint(), nullptr, nullptr, client_trans);
    client_socket.OwnerRef();
    client_trans->SetSocket(&client_socket);
    client_socket.AddUnwrittenBytes(0);

    int kNum = 5;
    std::vector<ThriftFramedMessage> req(2 * kNum);
    std::vector<ThriftFramedMessage> resp(2 * kNum);
    for (int i = 0; i < 2 * kNum; ++i) {
        auto raw_req_wrapper = new details::ThriftMessageHolder<example::EchoRequest>();
        req[i]._raw_instance = raw_req_wrapper;
        req[i]._own_raw_instance = true;
        auto raw_resp_wrapper = new details::ThriftMessageHolder<example::EchoResponse>();
        resp[i]._raw_instance = raw_resp_wrapper;
        resp[i]._own_raw_instance = true;
    }
    ThriftClientMessageHandler client_msg_handler;
    int num = 0;
    /* See comments at ByteMessageHandlerTest.ClientOvercrowded in byte_message_handler_test.cpp */
    {
        ExecCtx ctx(LOOP_IF_POSSIBLE);
        EventRegistry* ev_reg = ExecCtx::GetOrNewThreadEventRegistry(TYPE_KERNEL_TCP);
        std::vector<Controller*> cntl_vec;
        for (int i = 0; i < kNum; ++i) {
            Controller* cntl = client_msg_handler.CreateSessionController(1, CrcMode::TYPE_NONE);
            cntl->SetThriftMethodName(kEcho);
            EXPECT_EQ(cntl->request_protocol(), PROTOCOL_THRIFT);
            static_cast<StatefulController*>(cntl)->SetupTimeoutEventHandler(ev_reg, 0);
            google::protobuf::Closure* done =
                ::byterpc::NewCallback<Controller*,
                                       int,
                                       int*,
                                       ThriftClientMessageHandler*,
                                       ClientSocket*,
                                       ThriftFramedMessage*,
                                       ThriftFramedMessage*>(&MyEchoCallback2,
                                                             cntl,
                                                             EOVERCROWDED,
                                                             &num,
                                                             &client_msg_handler,
                                                             &client_socket,
                                                             &req[kNum + i],
                                                             &resp[kNum + i]);
            static_cast<ThriftRpcCall*>(cntl)->IssueRPC(
                &client_socket, nullptr, &req[i], &resp[i], done);
            cntl_vec.push_back(cntl);
        }
        // Cancel all rpcs, however it still return EOVERCROWDED error
        for (int i = 0; i < kNum; ++i) {
            cntl_vec[i]->StartCancelImpl();
        }

        ExecCtx::LoopOnce();
    }

    EXPECT_EQ(2 * kNum, num);
}

TEST(ThriftMessageHandlerTest, RPCCancelled) {
    gflags::FlagSaver saver;
    FLAGS_byterpc_socket_max_unwritten_bytes = 1024;

    SimpleTransport* client_trans = new SimpleTransport();
    std::unique_ptr<IORbufPipe> req_rbuf_pipe = std::make_unique<IORbufPipe>();
    client_trans->SetIORbufPipe(req_rbuf_pipe.get());
    ClientSocket client_socket(util::EndPoint(), nullptr, nullptr, client_trans);
    client_socket.OwnerRef();
    client_trans->SetSocket(&client_socket);

    // client send request
    int kNum = 8;
    std::vector<ThriftFramedMessage> req(kNum);
    std::vector<ThriftFramedMessage> resp(kNum);
    for (int i = 0; i < kNum; ++i) {
        auto raw_req_wrapper = new details::ThriftMessageHolder<example::EchoRequest>();
        req[i]._raw_instance = raw_req_wrapper;
        req[i]._own_raw_instance = true;
        auto raw_resp_wrapper = new details::ThriftMessageHolder<example::EchoResponse>();
        resp[i]._raw_instance = raw_resp_wrapper;
        resp[i]._own_raw_instance = true;
    }

    ThriftClientMessageHandler client_msg_handler;
    int num = 0;
    {
        ExecCtx ctx(LOOP_IF_POSSIBLE);
        EventRegistry* ev_reg = ExecCtx::GetOrNewThreadEventRegistry(TYPE_KERNEL_TCP);
        std::vector<Controller*> cntl_vec;

        for (int i = 0; i < kNum; ++i) {
            Controller* cntl = client_msg_handler.CreateSessionController(1, CrcMode::TYPE_NONE);
            cntl->SetThriftMethodName(kEcho);
            static_cast<StatefulController*>(cntl)->SetupTimeoutEventHandler(ev_reg, 0);
            int error_code = (i < kNum / 2) ? ECANCELED : EOVERCROWDED;
            google::protobuf::Closure* done = ::byterpc::NewCallback<Controller*, int, int*>(
                &MyEchoCallback, cntl, error_code, &num);
            static_cast<ThriftRpcCall*>(cntl)->IssueRPC(
                &client_socket, nullptr, &req[i], &resp[i], done);
            cntl_vec.push_back(cntl);
            if (i == kNum / 2 - 1) {
                // mark later half with overcrowded
                client_socket.AddUnwrittenBytes(1024);
            }
        }
        // Cancel all rpcs
        for (int i = 0; i < kNum; ++i) {
            cntl_vec[i]->StartCancelImpl();
        }

        ExecCtx::LoopOnce();
    }
    EXPECT_EQ(kNum, num);
}

TEST(ThriftMessageHandlerTest, IssueRPC) {
    ExecCtx ctx(LOOP_IF_POSSIBLE);
    // NOTE: should enable tcp evg poller to execute scheduled tasks
    ExecCtx::GetOrNewThreadEventRegistry(TYPE_KERNEL_TCP);

    SimpleTransport* client_trans = new SimpleTransport();
    std::unique_ptr<IORbufPipe> req_rbuf_pipe = std::make_unique<IORbufPipe>();
    client_trans->SetIORbufPipe(req_rbuf_pipe.get());

    ClientSocket client_socket(util::EndPoint(), nullptr, nullptr, client_trans);
    client_socket.OwnerRef();
    client_trans->SetSocket(&client_socket);

    int num = 0;
    ThriftClientMessageHandler client_msg_handler;
    // Case: Request is empty
    ThriftFramedMessage resp1;
    auto raw_resp_wrapper = new details::ThriftMessageHolder<example::EchoResponse>();
    resp1._raw_instance = raw_resp_wrapper;
    resp1._own_raw_instance = true;
    Controller* cntl1 = client_msg_handler.CreateSessionController(1000, CrcMode::TYPE_NONE);
    cntl1->SetThriftMethodName(kEcho);
    google::protobuf::Closure* done1 =
        ::byterpc::NewCallback<Controller*, int, int*>(&MyEchoCallback, cntl1, EREQUEST, &num);
    static_cast<ThriftRpcCall*>(cntl1)->IssueRPC(&client_socket, nullptr, nullptr, &resp1, done1);
    ExecCtx::LoopOnce();
    EXPECT_EQ(1, num);

    // Case: Request is not ThriftFramedMessage, whose descriptor is nullptr
    StatusRequest req2;
    ThriftFramedMessage resp2;
    auto raw_resp_wrapper2 = new details::ThriftMessageHolder<example::EchoResponse>();
    resp2._raw_instance = raw_resp_wrapper2;
    resp2._own_raw_instance = true;
    Controller* cntl2 = client_msg_handler.CreateSessionController(1000, CrcMode::TYPE_NONE);
    cntl2->SetThriftMethodName(kEcho);
    google::protobuf::Closure* done2 =
        ::byterpc::NewCallback<Controller*, int, int*>(&MyEchoCallback, cntl2, EINVAL, &num);
    static_cast<ThriftRpcCall*>(cntl2)->IssueRPC(&client_socket, nullptr, &req2, &resp2, done2);
    ExecCtx::LoopOnce();
    EXPECT_EQ(2, num);

    // Case: User-assigned method name is invalid
    ThriftFramedMessage req3;
    auto raw_req_wrapper3 = new details::ThriftMessageHolder<example::EchoRequest>();
    req3._raw_instance = raw_req_wrapper3;
    req3._own_raw_instance = true;
    ThriftFramedMessage resp3;
    auto raw_resp_wrapper3 = new details::ThriftMessageHolder<example::EchoResponse>();
    resp3._raw_instance = raw_resp_wrapper3;
    resp3._own_raw_instance = true;
    Controller* cntl3 = client_msg_handler.CreateSessionController(1000, CrcMode::TYPE_NONE);
    cntl3->SetThriftMethodName("");
    google::protobuf::Closure* done3 =
        ::byterpc::NewCallback<Controller*, int, int*>(&MyEchoCallback, cntl3, EINVAL, &num);
    static_cast<ThriftRpcCall*>(cntl3)->IssueRPC(&client_socket, nullptr, &req3, &resp3, done3);
    ExecCtx::LoopOnce();
    EXPECT_EQ(3, num);
}


////////////////
// Corner cases

TEST(ThriftMessageHandlerTest, Server_MessageTypeError) {
    ExecCtx ctx(LOOP_IF_POSSIBLE);
    SimpleTransport* client_trans = new SimpleTransport();
    std::unique_ptr<IORbufPipe> req_rbuf_pipe = std::make_unique<IORbufPipe>();
    client_trans->SetIORbufPipe(req_rbuf_pipe.get());

    ClientSocket client_socket(util::EndPoint(), nullptr, nullptr, client_trans);
    client_socket.OwnerRef();
    client_trans->SetSocket(&client_socket);


    // client sends requests
    ThriftFramedMessage req;
    ThriftFramedMessage resp;
    auto raw_req_wrapper = new details::ThriftMessageHolder<example::EchoRequest>();
    req._raw_instance = raw_req_wrapper;
    req._own_raw_instance = true;
    auto raw_resp_wrapper = new details::ThriftMessageHolder<example::EchoResponse>();
    resp._raw_instance = raw_resp_wrapper;
    resp._own_raw_instance = true;
    ThriftClientMessageHandler client_msg_handler;
    int num = 0;

    byterpc::ErrCode expect_err = EREQUEST;
    Controller* cntl = client_msg_handler.CreateSessionController(1000, CrcMode::TYPE_NONE);
    cntl->SetThriftMethodName(kEcho);
    EXPECT_EQ(cntl->request_protocol(), PROTOCOL_THRIFT);
    google::protobuf::Closure* done =
        ::byterpc::NewCallback<Controller*, int, int*>(&MyEchoCallback, cntl, expect_err, &num);
    static_cast<ThriftRpcCall*>(cntl)->IssueRPC(
        &client_socket, nullptr, &req, &resp, done);

    // modify request content
    char data[req_rbuf_pipe->length()];
    req_rbuf_pipe->cut_to(data, sizeof(data));
    data[7] = 0x3;  // type to T_EXCEPTION
    IOBuf tmp;
    tmp.append(data, sizeof(data));
    tmp.cut_to(req_rbuf_pipe.get(), tmp.size());

    // server processes requests and sends back responses
    RpcServiceRegistry svc_reg;
    MyEchoService* thrift_svc = new MyEchoService();
    ServiceOptions option;
    option.ownership = SERVER_OWNS_SERVICE;
    svc_reg.RegisterService(thrift_svc, option);
    ServerSocket server_socket(nullptr, &svc_reg, nullptr);
    SimpleTransport* server_trans = new SimpleTransport();
    std::unique_ptr<IORbufPipe> resp_rbuf_pipe = std::make_unique<IORbufPipe>();
    server_trans->SetIORbufPipe(resp_rbuf_pipe.get());
    server_trans->SetSocket(&server_socket);
    server_socket.ReadyToServe(server_trans);

    ThriftServerMessageHandler server_msg_handler;
    EXPECT_EQ(PARSE_ERROR_NOT_ENOUGH_DATA,
              server_msg_handler.HandleRequest(&server_socket, req_rbuf_pipe.get()));
    EXPECT_EQ(req_rbuf_pipe->size(), 0);

    // client receives responses
    EXPECT_EQ(PARSE_ERROR_NOT_ENOUGH_DATA,
              client_msg_handler.HandleResponse(&client_socket, resp_rbuf_pipe.get()));

    EXPECT_EQ(1, num);
}

TEST(ThriftMessageHandlerTest, Server_UserException) {
    ExecCtx ctx(LOOP_IF_POSSIBLE);
    SimpleTransport* client_trans = new SimpleTransport();
    std::unique_ptr<IORbufPipe> req_rbuf_pipe = std::make_unique<IORbufPipe>();
    client_trans->SetIORbufPipe(req_rbuf_pipe.get());

    ClientSocket client_socket(util::EndPoint(), nullptr, nullptr, client_trans);
    client_socket.OwnerRef();
    client_trans->SetSocket(&client_socket);

    // client sends requests
    int kNum = 2;
    std::vector<ThriftFramedMessage> req(kNum);
    std::vector<ThriftFramedMessage> resp(kNum);
    for (int i = 0; i < kNum; ++i) {
        auto raw_req_wrapper = new details::ThriftMessageHolder<example::EchoRequest>();
        req[i]._raw_instance = raw_req_wrapper;
        req[i]._own_raw_instance = true;
        auto raw_resp_wrapper = new details::ThriftMessageHolder<example::EchoResponse>();
        resp[i]._raw_instance = raw_resp_wrapper;
        resp[i]._own_raw_instance = true;
    }
    ThriftClientMessageHandler client_msg_handler;
    int num = 0;

    byterpc::ErrCode expect_err = EINTERNAL;
    // Normal exception
    Controller* cntl = client_msg_handler.CreateSessionController(1000, CrcMode::TYPE_NONE);
    cntl->SetThriftMethodName(kEchoException);  // rquest the method causing exception
    EXPECT_EQ(cntl->request_protocol(), PROTOCOL_THRIFT);
    google::protobuf::Closure* done =
        ::byterpc::NewCallback<Controller*, int, int*>(&MyEchoCallback, cntl, expect_err, &num);
    static_cast<ThriftRpcCall*>(cntl)->IssueRPC(&client_socket, nullptr, &req[0], &resp[0], done);
    // Cross thread exception
    Controller* cntl2 = client_msg_handler.CreateSessionController(1000, CrcMode::TYPE_NONE);
    cntl2->SetThriftMethodName(kEchoExceptionCrossThread);  // rquest the method causing exception
    EXPECT_EQ(cntl2->request_protocol(), PROTOCOL_THRIFT);
    google::protobuf::Closure* done2 =
        ::byterpc::NewCallback<Controller*, int, int*>(&MyEchoCallback, cntl2, expect_err, &num);
    static_cast<ThriftRpcCall*>(cntl2)->IssueRPC(&client_socket, nullptr, &req[1], &resp[1], done2);

    // server processes requests and sends back responses
    RpcServiceRegistry svc_reg;
    MyEchoService* thrift_svc = new MyEchoService();
    ServiceOptions option;
    option.ownership = SERVER_OWNS_SERVICE;
    svc_reg.RegisterService(thrift_svc, option);
    ServerSocket server_socket(nullptr, &svc_reg, nullptr);
    SimpleTransport* server_trans = new SimpleTransport();
    std::unique_ptr<IORbufPipe> resp_rbuf_pipe = std::make_unique<IORbufPipe>();
    server_trans->SetIORbufPipe(resp_rbuf_pipe.get());
    server_trans->SetSocket(&server_socket);
    server_socket.ReadyToServe(server_trans);

    ThriftServerMessageHandler server_msg_handler;
    EXPECT_EQ(PARSE_ERROR_NOT_ENOUGH_DATA,
              server_msg_handler.HandleRequest(&server_socket, req_rbuf_pipe.get()));

    byte::ThisThread::SleepInMs(1000);
    // Execute remote tasks of cross thread response
    for (int i = 0; i < 64; ++i) {
        ExecCtx::LoopOnce();
    }

    // client receives responses
    EXPECT_EQ(PARSE_ERROR_NOT_ENOUGH_DATA,
              client_msg_handler.HandleResponse(&client_socket, resp_rbuf_pipe.get()));

    EXPECT_EQ(kNum, num);
}

TEST(ThriftMessageHandlerTest, Server_RequestErrorAlongSuccess) {
    ExecCtx ctx(LOOP_IF_POSSIBLE);
    SimpleTransport* client_trans = new SimpleTransport();
    std::unique_ptr<IORbufPipe> req_rbuf_pipe = std::make_unique<IORbufPipe>();
    client_trans->SetIORbufPipe(req_rbuf_pipe.get());

    ClientSocket client_socket(util::EndPoint(), nullptr, nullptr, client_trans);
    client_socket.OwnerRef();
    client_trans->SetSocket(&client_socket);

    // client sends requests
    int kNum = 10;
    std::vector<ThriftFramedMessage> req(kNum);
    std::vector<ThriftFramedMessage> resp(kNum);
    for (int i = 0; i < kNum; ++i) {
        auto raw_req_wrapper = new details::ThriftMessageHolder<example::EchoRequest>();
        req[i]._raw_instance = raw_req_wrapper;
        req[i]._own_raw_instance = true;
        auto raw_resp_wrapper = new details::ThriftMessageHolder<example::EchoResponse>();
        resp[i]._raw_instance = raw_resp_wrapper;
        resp[i]._own_raw_instance = true;
    }
    ThriftClientMessageHandler client_msg_handler;
    int num = 0;
    for (int i = 0; i < kNum; ++i) {
        Controller* cntl = client_msg_handler.CreateSessionController(1000, CrcMode::TYPE_NONE);
        EXPECT_EQ(cntl->request_protocol(), PROTOCOL_THRIFT);
        int expect_err = 0;
        std::string method_name{};
        if (i % 2 == 0) {
            method_name = kEcho;
            expect_err = 0;
        } else {
            method_name = "aaaa";
            expect_err = ENOMETHOD;
        }
        cntl->SetThriftMethodName(method_name.c_str());
        google::protobuf::Closure* done =
            ::byterpc::NewCallback<Controller*, int, int*>(&MyEchoCallback, cntl, expect_err, &num);
        static_cast<ThriftRpcCall*>(cntl)->IssueRPC(
            &client_socket, nullptr, &req[i], &resp[i], done);
    }

    // server processes requests and sends back responses
    RpcServiceRegistry svc_reg;
    MyEchoService* thrift_svc = new MyEchoService();
    ServiceOptions option;
    option.ownership = SERVER_OWNS_SERVICE;
    svc_reg.RegisterService(thrift_svc, option);
    ServerSocket server_socket(nullptr, &svc_reg, nullptr);
    SimpleTransport* server_trans = new SimpleTransport();
    std::unique_ptr<IORbufPipe> resp_rbuf_pipe = std::make_unique<IORbufPipe>();
    server_trans->SetIORbufPipe(resp_rbuf_pipe.get());
    server_trans->SetSocket(&server_socket);
    server_socket.ReadyToServe(server_trans);

    ThriftServerMessageHandler server_msg_handler;
    EXPECT_EQ(PARSE_ERROR_NOT_ENOUGH_DATA,
              server_msg_handler.HandleRequest(&server_socket, req_rbuf_pipe.get()));

    // client receives responses
    EXPECT_EQ(PARSE_ERROR_NOT_ENOUGH_DATA,
              client_msg_handler.HandleResponse(&client_socket, resp_rbuf_pipe.get()));

    EXPECT_EQ(kNum, num);
}

TEST(ThriftMessageHandlerTest, Server_ResponseErrorAlongSucc) {
    ExecCtx ctx(LOOP_IF_POSSIBLE);
    SimpleTransport* client_trans = new SimpleTransport();
    std::unique_ptr<IORbufPipe> req_rbuf_pipe = std::make_unique<IORbufPipe>();
    client_trans->SetIORbufPipe(req_rbuf_pipe.get());

    ClientSocket client_socket(util::EndPoint(), nullptr, nullptr, client_trans);
    client_socket.OwnerRef();
    client_trans->SetSocket(&client_socket);

    // client sends requests
    int kNum = 5;
    std::vector<ThriftFramedMessage> req(kNum);
    std::vector<ThriftFramedMessage> resp(kNum);
    for (int i = 0; i < kNum; ++i) {
        auto raw_req_wrapper = new details::ThriftMessageHolder<example::EchoRequest>();
        req[i]._raw_instance = raw_req_wrapper;
        req[i]._own_raw_instance = true;
        auto raw_resp_wrapper = new details::ThriftMessageHolder<example::EchoResponse>();
        resp[i]._raw_instance = raw_resp_wrapper;
        resp[i]._own_raw_instance = true;
    }
    ThriftClientMessageHandler client_msg_handler;
    int num = 0;
    for (int i = 0; i < kNum; ++i) {
        Controller* cntl = client_msg_handler.CreateSessionController(1000, CrcMode::TYPE_NONE);
        cntl->SetThriftMethodName(kEcho);
        EXPECT_EQ(cntl->request_protocol(), PROTOCOL_THRIFT);
        int expect_err = 0;
        if (i == 0) {
            expect_err = ERESPONSE;
        }
        google::protobuf::Closure* done =
            ::byterpc::NewCallback<Controller*, int, int*>(&MyEchoCallback, cntl, expect_err, &num);
        static_cast<ThriftRpcCall*>(cntl)->IssueRPC(
            &client_socket, nullptr, &req[i], &resp[i], done);
    }

    // modify first request of 5, whose method_name will be changed to another correct name, but
    // it's inconsistent with client cntl's method_name
    char data[4096];
    memset(data, 0, 4096);
    size_t req_len = req_rbuf_pipe->length();
    req_rbuf_pipe->cut_to(data, req_len);
    data[12] = 'e';  // modify Echo to echo
    IOBuf tmp_buf;
    tmp_buf.append(data, req_len);
    tmp_buf.cut_to(req_rbuf_pipe.get(), req_len);

    // server processes requests and sends back responses
    RpcServiceRegistry svc_reg;
    MyEchoService* thrift_svc = new MyEchoService();
    ServiceOptions option;
    option.ownership = SERVER_OWNS_SERVICE;
    svc_reg.RegisterService(thrift_svc, option);
    ServerSocket server_socket(nullptr, &svc_reg, nullptr);
    SimpleTransport* server_trans = new SimpleTransport();
    std::unique_ptr<IORbufPipe> resp_rbuf_pipe = std::make_unique<IORbufPipe>();
    server_trans->SetIORbufPipe(resp_rbuf_pipe.get());
    server_trans->SetSocket(&server_socket);
    server_socket.ReadyToServe(server_trans);

    ThriftServerMessageHandler server_msg_handler;
    EXPECT_EQ(PARSE_ERROR_NOT_ENOUGH_DATA,
              server_msg_handler.HandleRequest(&server_socket, req_rbuf_pipe.get()));

    // client receives responses
    EXPECT_EQ(PARSE_ERROR_NOT_ENOUGH_DATA,
              client_msg_handler.HandleResponse(&client_socket, resp_rbuf_pipe.get()));

    EXPECT_EQ(kNum, num);
}

}  // namespace byterpc

int main(int argc, char** argv) {
    testing::InitGoogleTest(&argc, argv);
#ifdef BYTERPC_WITH_NUMA
    byterpc::FLAGS_byterpc_enable_numa_aware = true;
    byterpc::FLAGS_byterpc_memory_pool_numa_init_region_num = "1,1";
#endif
    byterpc::InitOptions init_opt;
    BYTERPC_CHECK(0 == byterpc::ExecCtx::Init(init_opt));
    byterpc::g_thread_pool.reset(new byte::BaseThreadPool(1, "backend"));

    auto fini_guard = byte::defer([]() { byterpc::ExecCtx::Fini(); });
    return RUN_ALL_TESTS();
}
