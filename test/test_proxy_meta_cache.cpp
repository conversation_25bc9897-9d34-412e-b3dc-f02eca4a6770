/***************************************************************************
 * 
 * Copyright (c) 2020 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/
 

#include <gtest/gtest.h>
#include "baidu/inf/aries-api/proxy/meta_cache.h"
#include "baidu/inf/aries-api/common/common.h"
#include "base/endpoint.h"

namespace aries {

class TestMetaCache : public ::testing::Test {
public:
    void SetUp() {
        _conf.cache_space_name = "test_space";
        _meta_cache.reset(new MetaCache());
        assert(_meta_cache != nullptr);
        int ret =_meta_cache->init(&_conf);
        EXPECT_EQ(0, ret);
    }
    void TearDown() {
    }
    ProxyConf _conf;
    std::unique_ptr<MetaCache> _meta_cache;
};

TEST_F(TestMetaCache, cache_volume_info) {
    // not exist
    {
        VolumeInfoPtr volume_info;
        int ret = _meta_cache->get_volume_info_ptr(1, &volume_info);
        EXPECT_EQ(ret, AIE_NOT_EXIST);
        EXPECT_EQ(nullptr, volume_info);

        volume_info.reset(new VolumeInfo());
        volume_info->space_info.set_space_name("test_space_1"); // space not exist
        ret = _meta_cache->set_volume_info(1, volume_info);
        EXPECT_EQ(-1, ret);
    }
    // timeout
    {
        VolumeInfoPtr volume_info;
        volume_info.reset(new VolumeInfo());
        volume_info->space_info.set_space_name("test_space");
        int ret = _meta_cache->set_volume_info(1, volume_info);
        EXPECT_EQ(0, ret);
        VolumeInfoPtr new_volume_info;
        ret = _meta_cache->get_volume_info_ptr(1, &new_volume_info);
        EXPECT_EQ(0, ret);
        EXPECT_EQ(volume_info.get(), new_volume_info.get());
        EXPECT_EQ(volume_info->space_info.space_name(), new_volume_info->space_info.space_name());
        _meta_cache->_cache_timeout_s = 0;
        sleep(1);
        ret = _meta_cache->get_volume_info_ptr(1, &new_volume_info);
        EXPECT_EQ(ret, AIE_TIMEOUT);
        _meta_cache->_cache_timeout_s = 100;
    }
    // need update
    {
        VolumeInfoPtr volume_info;
        volume_info.reset(new VolumeInfo());
        volume_info->space_info.set_space_name("test_space");
        int ret = _meta_cache->set_volume_info(1, volume_info);
        EXPECT_EQ(0, ret);
        VolumeInfoPtr new_volume_info;
        ret = _meta_cache->get_volume_info_ptr(1, &new_volume_info);
        EXPECT_EQ(0, ret);
        EXPECT_EQ(volume_info.get(), new_volume_info.get());
        EXPECT_EQ(volume_info->space_info.space_name(), new_volume_info->space_info.space_name());
        _meta_cache->_cache_timeout_s = 3;
        sleep(2);
        ret = _meta_cache->get_volume_info_ptr(1, &new_volume_info);
        EXPECT_EQ(ret, AIE_NEED_UPDATE);
    }
    // ok
    {
        VolumeInfoPtr volume_info;
        volume_info.reset(new VolumeInfo());
        volume_info->space_info.set_space_name("test_space");
        int ret = _meta_cache->set_volume_info(1, volume_info);
        EXPECT_EQ(0, ret);
        VolumeInfoPtr new_volume_info;
        ret = _meta_cache->get_volume_info_ptr(1, &new_volume_info);
        EXPECT_EQ(0, ret);
    }
}

TEST_F(TestMetaCache, cache_space_info) {
    std::string space_name = "test_space";
    // not exist
    {
        SpaceInfoPtr space_info;
        int ret = _meta_cache->get_space_info_ptr(space_name, &space_info);
        EXPECT_EQ(ret, AIE_NOT_EXIST);
        EXPECT_EQ(nullptr, space_info);

        space_info.reset(new aries::pb::SpaceInfo);
        space_info->set_space_name("test_space_1"); // space not exist
        ret = _meta_cache->set_space_info("test_space_1", space_info);
        EXPECT_EQ(-1, ret);
    }
    // timeout
    {
        SpaceInfoPtr space_info;
        space_info.reset(new aries::pb::SpaceInfo);
        space_info->set_space_name(space_name);
        int ret = _meta_cache->set_space_info(space_name, space_info);
        EXPECT_EQ(0, ret);
        SpaceInfoPtr new_space_info;
        ret = _meta_cache->get_space_info_ptr(space_name, &new_space_info);
        EXPECT_EQ(0, ret);
        EXPECT_EQ(space_info.get(), new_space_info.get());
        EXPECT_EQ(space_info->space_name(), new_space_info->space_name());
        _meta_cache->_cache_timeout_s = 0;
        sleep(1);
        ret = _meta_cache->get_space_info_ptr(space_name, &new_space_info);
        EXPECT_EQ(ret, AIE_TIMEOUT);
        _meta_cache->_cache_timeout_s = 100;
    }
    // need update
    {
        SpaceInfoPtr space_info;
        space_info.reset(new aries::pb::SpaceInfo);
        space_info->set_space_name(space_name);
        int ret = _meta_cache->set_space_info(space_name, space_info);
        EXPECT_EQ(0, ret);
        SpaceInfoPtr new_space_info;
        ret = _meta_cache->get_space_info_ptr(space_name, &new_space_info);
        EXPECT_EQ(0, ret);
        EXPECT_EQ(space_info.get(), new_space_info.get());
        EXPECT_EQ(space_info->space_name(), new_space_info->space_name());
        _meta_cache->_cache_timeout_s = 3;
        sleep(2);
        ret = _meta_cache->get_space_info_ptr(space_name, &new_space_info);
        EXPECT_EQ(ret, AIE_NEED_UPDATE);
        _meta_cache->_cache_timeout_s = 100;
    }
    // no size limit
    {
        _conf.cache_space_name = "test_space";
        _conf.meta_cache_size = 0;
        int ret = _meta_cache->init(&_conf);
        EXPECT_EQ(0, ret);
        SpaceInfoPtr space_info;
        space_info.reset(new aries::pb::SpaceInfo);
        space_info->set_space_name(space_name);
        ret = _meta_cache->set_space_info(space_name, space_info);
        EXPECT_EQ(0, ret);
        ret = _meta_cache->get_space_info_ptr(space_name, &space_info);
        EXPECT_EQ(0, ret);
    }
}

}