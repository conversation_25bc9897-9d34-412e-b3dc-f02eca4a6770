// Copyright (c) 2024, ByteDance Inc. All rights reserved.

#include "byterpc/rpc.h"

#include <atomic>
#include <memory>
#include <string>
#include <thread>
#include <vector>

#include <byte/fiber/fiber.h>
#include <byte/util/defer.h>
#include <gtest/gtest.h>

#include "byterpc/builder.h"
#include "byterpc/byterpc_flags.h"
#include "byterpc/exec_ctx.h"
#include "byterpc/server.h"
#include "byterpc/thread/priority_task.h"
#include "byterpc/util/closure_guard.h"
#include "byterpc/util/logging.h"
#include "status.pb.h"

namespace byterpc {

class StatusService : public status {
public:
    void default_method(::google::protobuf::RpcController* controller,
                        const StatusRequest* request,
                        StatusResponse* response,
                        ::google::protobuf::Closure* done) override {
        util::ClosureGuard done_guard(done);
    }
};

static void WaitFlagReady(std::atomic_bool* ready_flag) {
    while (!ready_flag->load(std::memory_order_acquire))
        usleep(1);
    ready_flag->store(false, std::memory_order_release);
}

TEST(FiberByteExpressRPCTest, SetupConnections) {
    gflags::FlagSaver saver;
    FLAGS_byterpc_byte_express_max_inflight_connectors = 1;

    std::atomic_bool ready_flag(false);
    int req_num = 10;
    int done_cnt = 0;
    util::EndPoint local_ep;
    local_ep.ip = util::my_ip();
    local_ep.port = 18882;
    ThreadContext* server_ctx = nullptr;

    std::vector<Server*> server_vec;
    std::thread server([&]() {
        { ExecCtx ctx(LOOP_UNTIL_QUIT); }
        EXPECT_EQ(0, ExecCtx::EnableFiberMode({}));
        for (int i = 0; i < req_num; ++i) {
            Server* srv = new Server;
            ServerOptions options(false, false, true);
            options._enable_builtin_service = true;
            srv->RegisterService(new StatusService);
            while (srv->Start(local_ep, options) < 0) {
                BYTERPC_LOG(ERROR) << "Fail to start server at port=" << local_ep.port;
                local_ep.port++;
                srv->RegisterService(new StatusService);
            }
            // BYTERPC_LOG(INFO) << "Start server success, server=" << local_ep;
            server_vec.push_back(srv);
            local_ep.port++;
        }
        server_ctx = ExecCtx::GetCurrentThreadContext();
        ready_flag.store(true, std::memory_order_release);

        ExecCtx::LoopUntilQuit();

        for (size_t i = 0; i < server_vec.size(); ++i) {
            delete server_vec[i];
        }
        server_vec.clear();
    });

    WaitFlagReady(&ready_flag);

    std::thread client([&]() {
        { ExecCtx ctx(LOOP_UNTIL_QUIT); }
        EXPECT_EQ(0, ExecCtx::EnableFiberMode({}));
        Builder builder;
        Builder::ChannelOptions options;
        options._trans_type = TYPE_RDMA;
        options._connect_timeout_ms = 40000;
        options._rpc_timeout_ms = 60000;
        std::vector<StatusResponse> resps;
        resps.resize(req_num);
        for (size_t i = 0; i < server_vec.size(); ++i) {
            byte::fiber::Create({}, [&, i]() {
                util::EndPoint ep = server_vec[i]->listen_address();
                std::shared_ptr<Builder::Channel> channel = builder.BuildChannel(ep, options);
                std::unique_ptr<status_Stub> stub = std::make_unique<status_Stub>(channel.get());
                // construct req
                std::unique_ptr<StatusRequest> req = std::make_unique<StatusRequest>();
                Controller* cntl = builder.CreateSessionController(PROTOCOL_BAIDU_STD);
                stub->default_method(cntl, req.get(), &resps[i], nullptr);
                EXPECT_EQ(cntl->ErrorCode(), 0);
                ++done_cnt;
                if (done_cnt == req_num) {
                    ExecCtx::QuitLoop();
                }
                cntl->DoRecycle();
            });
        }

        // must build channel before LoopUntilQuit()
        byte::fiber::Yield();
        ExecCtx::LoopUntilQuit();
    });

    client.join();
    BYTERPC_LOG(INFO) << "Client was finished";

    server_ctx->Invoke([]() { ExecCtx::QuitLoop(); });
    server.join();
    BYTERPC_LOG(INFO) << "Server was finished";
}

}  // namespace byterpc

int main(int argc, char** argv) {
    testing::InitGoogleTest(&argc, argv);
#ifdef BYTERPC_WITH_NUMA
    byterpc::FLAGS_byterpc_enable_numa_aware = true;
    byterpc::FLAGS_byterpc_memory_pool_numa_init_region_num = "1,1";
#endif
    byterpc::InitOptions init_opt;
    init_opt._init_rdma = true;
    BYTERPC_CHECK(0 == byterpc::ExecCtx::Init(init_opt));
    auto byterpc_fini_guard = byte::defer([]() { byterpc::ExecCtx::Fini(); });

    BYTERPC_CHECK(0 == byte::fiber::GlobalInit({}));
    auto fiber_global_fini_guard = byte::defer([]() { byte::fiber::GlobalFini(); });

    return RUN_ALL_TESTS();
}
