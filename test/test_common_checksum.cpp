/**
 * ARIES - A Reliable and Impressive EB-level Storage.
 * Copyright (c) 2017 Baidu.com, Inc. All Rights Reserved.
 *
 * Author: <PERSON> <PERSON> (<EMAIL>)
 * Date: 2017/04/14
 * Description: Unittest for common checksum
 *
 */

#include <gtest/gtest.h>
#include <base/logging.h>
#include "baidu/inf/aries-api/common/checksum.h"

namespace aries {
namespace common {

class ChecksumTests : public ::testing::Test {
};

TEST_F(ChecksumTests, compute) {
    const char* buf = NULL;
    uint32_t crc = 0;
    ASSERT_EQ(0, Crc32CheckSum::compute(buf, 0, 0));

    buf = "aaaaaa";
    crc = Crc32CheckSum::compute(buf, strlen(buf), crc);
    ASSERT_EQ(crc, base::crc32c::Value(buf, strlen(buf)));
    ASSERT_EQ(crc, base::crc32c::Extend(0, buf, strlen(buf)));
    uint32_t last_crc = crc;

    std::string data(buf);
    ASSERT_EQ(crc, Crc32CheckSum::compute(data));

    buf = "bbbbbbbb";
    crc = Crc32CheckSum::compute(buf, strlen(buf), last_crc);
    ASSERT_EQ(crc, base::crc32c::Extend(last_crc, buf, strlen(buf)));

}

}
}
