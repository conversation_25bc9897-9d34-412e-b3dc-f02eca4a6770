/***************************************************************************
 * 
 * Copyright (c) 2017 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/
/**
 * @file test/test_blob_allocator.cpp
 * <AUTHOR>
 * @date 2017/03/05 20:40:07
 * @brief 
 *  
 **/
#include "baidu/inf/aries-api/test/bmock_util.h"
#include "baidu/inf/aries-api/proxy/blob_allocator.h"
#include "baidu/inf/aries-api/proxy/meta_handler.h"
#include "baidu/inf/aries-api/proxy/allocator_client.h"

namespace aries {
using ::testing::Return;
using ::testing::DoAll;
using ::testing::SetArgPointee;
using ::testing::SetArgReferee;
using ::testing::_;
// mock
MAKE_BMOCK_NS_CLASS_METHOD(3, aries, MetaHandler, get_space,
        int(uint64_t, const std::string &, SpaceInfoPtr *));
MAKE_BMOCK_NS_CLASS_METHOD(4, aries, MetaHandler, get_volume,
        int(uint64_t, uint64_t, bool, VolumeInfoPtr *));
MAKE_BMOCK_NS_CLASS_METHOD(6, aries, AllocatorClient, allocate_bid,
        int(RpcControl &, const std::string &, uint32_t, __uint128_t *, VolumeInfo*, int));
MAKE_BMOCK_NS_CLASS_METHOD(2, aries, AllocatorClient, get_access_channel,
        bool(const std::string &, RpcControl *));

static void mock_retry_rpc_call(google::protobuf::RpcController *control, const google::protobuf::Message *req,
        google::protobuf::Message *rep, google::protobuf::Closure *done) {
    baidu::rpc::Controller *cntl = (baidu::rpc::Controller *) control;
    cntl->SetFailed(EAGAIN, "mock again");
    if (done != NULL) {
        done->Run();
    }
}

class TestBlobAllocator : public ::testing::Test {
public:
    void SetUp() {
        _conf.allocator_refresh_ms = 100;
        _conf.vs_server = "bns://any";
        _conf.vs_load_balancer = "rr";
        _meta_handler.reset(new MetaHandler());
        assert(_meta_handler != nullptr);
        assert(_meta_handler->init(&_conf) == 0);
        _allocator_client.reset(new AllocatorClient());
        assert(_allocator_client != nullptr);
        _blob_allocator.reset(new BlobAllocator(_meta_handler.get(), _allocator_client.get()));
        assert(_blob_allocator != nullptr);
    }
    void TearDown() {
    }

    ProxyConf _conf;
    std::unique_ptr<MetaHandler> _meta_handler;
    std::unique_ptr<AllocatorClient> _allocator_client;
    std::unique_ptr<BlobAllocator> _blob_allocator;
};

TEST_F(TestBlobAllocator, calc_shard_size) {
    EXPECT_EQ(8, BlobAllocator::calc_shard_size(72, 9));
    EXPECT_EQ(9, BlobAllocator::calc_shard_size(73, 9));
}

TEST_F(TestBlobAllocator, calc_reserve) {
    PutBlobInfo blob;
    blob.shard_size = 1024;
    blob.key = std::string(1024, '*');
    EXPECT_EQ(2048, BlobAllocator::calc_reserve(blob));

    blob.meta = "test";
    EXPECT_EQ(3072, BlobAllocator::calc_reserve(blob));
}

TEST_F(TestBlobAllocator, alloc) {
    BMOCK_CLASS_MOCK_GUARD(MetaHandler, get_space);
    BMOCK_CLASS_MOCK_GUARD(MetaHandler, get_volume);
    BMOCK_CLASS_MOCK_GUARD(AllocatorClient, allocate_bid);
    BMOCK_CLASS_MOCK_GUARD(AllocatorClient, get_access_channel);

    PutBlobInfo blob;
    blob.blob_len = 1024;
    blob.shard_size = 0;
    auto volume_ptr = std::make_shared<VolumeInfo>();
    auto& volume = *volume_ptr;
    volume.space_info.set_space_name("test-space");
    volume.shards.resize(7);

    // for return bid
    __uint128_t ret_bid = 123;
    ret_bid = ret_bid << 64 | 123;

    // get allocator channel list failed
    EXPECT_CALL(BMOCK_NS_CLASS_OBJECT(aries, AllocatorClient, get_access_channel),
            get_access_channel(_, _))
        .WillOnce(Return(false));
    int ret = _blob_allocator->alloc("test-space", blob, &volume);
    EXPECT_EQ(AIE_FAIL, ret);
    EXPECT_EQ("get access allocator channel failed", _blob_allocator->last_error());

    std::map<uint64_t, AllocatorInfoPtr> addrs;
    addrs[1] = AllocatorInfoPtr(new AllocatorInfo(1));
    _allocator_client->_allocators.Modify(AllocatorClient::reset_allocators, addrs);

    SpaceInfoPtr space_info(new aries::pb::SpaceInfo);
    space_info->set_space_name("test-space");
    space_info->set_k(4);
    space_info->set_put_quorum(5);
    EXPECT_CALL(BMOCK_NS_CLASS_OBJECT(aries, MetaHandler, get_space),
            get_space(_,_,_))
        .WillOnce(Return(AIE_FAIL))
        .WillOnce(Return(AIE_AGAIN))
        .WillRepeatedly(DoAll(SetArgPointee<2>(space_info), Return(0)));
    EXPECT_CALL(BMOCK_NS_CLASS_OBJECT(aries, AllocatorClient, get_access_channel),
            get_access_channel(_, _))
        .WillRepeatedly(Return(true));

    // get space failed
    ret = _blob_allocator->alloc("test-space", blob, &volume);
    EXPECT_EQ(AIE_FAIL, ret);
    EXPECT_EQ("get space failed", _blob_allocator->last_error());

    // get space failed but need retry
    ret = _blob_allocator->alloc("test-space", blob, &volume);
    EXPECT_EQ(AIE_AGAIN, ret);

    EXPECT_CALL(BMOCK_NS_CLASS_OBJECT(aries, AllocatorClient, allocate_bid),
            allocate_bid(_, _, _, _, _, _))
        .WillOnce(Return(-1))
        .WillOnce(Return(-1))
        .WillOnce(DoAll(SetArgPointee<3>(ret_bid), Return(0)))
        .WillOnce(Return(AIE_AGAIN));

    EXPECT_CALL(BMOCK_NS_CLASS_OBJECT(aries, MetaHandler, get_volume), get_volume(_,_,_,_))
        .WillOnce(DoAll(SetArgPointee<3>(volume_ptr), Return(0)));

    // twice failed
    ret = _blob_allocator->alloc("test-space", blob, &volume);
    EXPECT_EQ(AIE_TIMEOUT, ret);
    EXPECT_EQ("allocate failed and tries out", _blob_allocator->last_error());

    // timeout
    _blob_allocator->set_timeout_ms(0);
    ret = _blob_allocator->alloc("test-space", blob, &volume);
    EXPECT_EQ(AIE_TIMEOUT, ret);
    EXPECT_EQ("allocate failed and tries out", _blob_allocator->last_error());

    ret = _blob_allocator->alloc("test-space", blob, &volume);
    EXPECT_EQ(0, ret);
    EXPECT_EQ(256, blob.shard_size);

    // allocate_bid failed and need retry
    ret = _blob_allocator->alloc("test-space", blob, &volume);
    EXPECT_EQ(AIE_AGAIN, ret);
}

TEST_F(TestBlobAllocator, try_alloc) {
    BMOCK_CLASS_MOCK_GUARD(MetaHandler, get_volume);
    BMOCK_CLASS_MOCK_GUARD(AllocatorClient, allocate_bid);
    _blob_allocator->_space.reset(new aries::pb::SpaceInfo);
    _blob_allocator->_space->set_space_name("test-space");
    _blob_allocator->_space->set_put_quorum(5);
    __uint128_t bid;
    __uint128_t ret_bid = 123;
    ret_bid = ret_bid << 64 | 123;
    auto volume_ptr = std::make_shared<VolumeInfo>();
    auto& volume = *volume_ptr;

    // test allocate blob condition
    auto incomplete_volume_ptr = std::make_shared<VolumeInfo>();
    auto& incomplete_volume = *incomplete_volume_ptr;
    incomplete_volume.space_info.set_space_name("test-space");
    incomplete_volume.shards.resize(4);
    incomplete_volume.volume_id = ret_bid;
    incomplete_volume.eco.param.n = 4;
    
    auto volume_info_ptr = std::make_shared<VolumeInfo>();
    auto& volume_info = *volume_info_ptr;
    volume_info.space_info.set_space_name("test-space");
    volume_info.shards.resize(7);
    volume_info.volume_id = ret_bid;
    volume_info.eco.param.n = 4;

    EXPECT_CALL(BMOCK_NS_CLASS_OBJECT(aries, AllocatorClient, allocate_bid),
            allocate_bid(_, _, _, _, _, _))
        .WillOnce(DoAll(SetArgPointee<3>(ret_bid), Return(0)))
        .WillOnce(DoAll(SetArgPointee<3>(ret_bid), Return(0)))
        .WillOnce(DoAll(SetArgPointee<3>(ret_bid), Return(0)))
        .WillOnce(DoAll(SetArgPointee<3>(ret_bid), SetArgPointee<4>(incomplete_volume), Return(0)))
        .WillOnce(DoAll(SetArgPointee<3>(ret_bid), SetArgPointee<4>(volume_info), Return(0)));

    EXPECT_CALL(BMOCK_NS_CLASS_OBJECT(aries, MetaHandler, get_volume), get_volume(_,_,_,_))
        .WillOnce(Return(-1))
        .WillOnce(DoAll(SetArgPointee<3>(incomplete_volume_ptr), Return(0)))
        .WillOnce(DoAll(SetArgPointee<3>(volume_info_ptr), Return(0)));

    int ret = _blob_allocator->try_alloc(1024, &bid, &volume);
    EXPECT_EQ(-1, ret);

    volume.eco.param.n = 0;

    ret = _blob_allocator->try_alloc(1024, &bid, &volume);
    EXPECT_EQ(AIE_NO_ENOUGH_VLET, ret);

    volume.eco.param.n = 0;

    ret = _blob_allocator->try_alloc(1024, &bid, &volume);
    EXPECT_EQ(0, ret);

    volume.eco.param.n = 0;

    ret = _blob_allocator->try_alloc(1024, &bid, &volume);
    EXPECT_EQ(AIE_NO_ENOUGH_VLET, ret);

    volume.eco.param.n = 0;

    ret = _blob_allocator->try_alloc(1024, &bid, &volume);
    EXPECT_EQ(0, ret);
}
}

