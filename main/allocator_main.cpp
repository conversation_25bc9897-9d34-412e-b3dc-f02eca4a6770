/**
 * ARIES - A Reliable and Impressive EB-level Storage.
 * Copyright (c) 2016 Baidu.com, Inc. All Rights Reserved.
 *
 * Author: <PERSON> (guo<PERSON>@baidu.com)
 * Date: 2016/09/01
 * Desciption: Main of allocator
 *
 */

#include "baidu/inf/aries-api/common/config.h"
#include "baidu/inf/aries-api/common/utils.h"
#include "baidu/inf/aries/common/tcmalloc_memory_scheduler.h"
#include "baidu/inf/aries/main/main_utils.h"
#include "baidu/inf/aries/allocator/blob_allocator.h"

DECLARE_string(flagfile);
DEFINE_bool(h, false, "print help information");
DEFINE_bool(v, false, "print version information");
DEFINE_bool(stop, false, "stop allocator");
DEFINE_bool(reload, false, "reload allocator flags");

static const char *s_proc_name = NULL;
#if defined( __DATE__ ) && defined ( __TIME__ )
static const char *s_build_time = __DATE__ " " __TIME__;
#else
static const char *s_build_time = "unknown";
#endif

#ifndef __ARIES_VERSION_ID__
#define __ARIES_VERSION_ID__ "unknown"
#endif

#ifndef __ARIES_REVISION_ID__
#define __ARIES_REVISION_ID__ "unknown"
#endif

#ifndef __ARIES_REPO_URL__
#define __ARIES_REPO_URL__   "unknown"
#endif

#ifndef __ARIES_BUILDHOST__
#define __ARIES_BUILDHOST__  "unknown"
#endif

void stop_allocator() {
    aries::allocator::g_blob_allocator->stop();
}

void reload_allocator() {
    aries::common::g_config_reloader->reload();
    aries::allocator::g_blob_allocator->update_conf();
}

void register_signal_handler() {
    SignalHandler::set_module_name("allocator");
    SignalHandler::set_func_on_exit(stop_allocator);
    SignalHandler::set_func_on_reload(reload_allocator);
    SignalHandler::register_signal_handler();
}

int main(int argc, char** argv) {
    int ret = 0;

    // set version
    std::string version_str;
    base::string_printf(&version_str,
                        "Version: %s\n"
                        "Revision: %s\n"
                        "RepoURL: %s\n"
                        "Build: %s %s\n",
                        __ARIES_VERSION_ID__, __ARIES_REVISION_ID__, __ARIES_REPO_URL__,
                        s_build_time, __ARIES_BUILDHOST__);
    google::SetVersionString(version_str);

    // set usage
    s_proc_name = strrchr(argv[0], '/');
    if (s_proc_name == NULL) {
        s_proc_name = argv[0];
    } else {
        ++s_proc_name;
    }
    std::string help_str;
    base::string_printf(&help_str,
                        "Usage: %s [OPTIONS...]\n"
                        "Options:\n"
                        "  -h              Print this help message.\n"
                        "  -v              Print version number.\n"
                        "  -stop           stop gc.\n"
                        "  -flagfile=$path Load flags from file.",
                        s_proc_name);
    google::SetUsageMessage(help_str);

    struct stat st;
    if (0 == stat("conf/allocator.conf", &st) && FLAGS_flagfile.empty()) {
        FLAGS_flagfile = "conf/allocator.conf";
    }
    google::ParseCommandLineFlags(&argc, &argv, false);
    google::SetCommandLineOption("bvar_dump", "false");
    google::SetCommandLineOption("bvar_dump_interval", "60");

    // check -v and -h
    if (FLAGS_v) {
        fprintf(stderr, "%s\n", version_str.c_str());
        _exit(0);
    }
    if (FLAGS_h) {
        fprintf(stderr, "%s\n", help_str.c_str());
        _exit(0);
    }

    // check -stop/-reload
    if (FLAGS_stop || FLAGS_reload) {
        assert(!(FLAGS_stop && FLAGS_reload));
        return check_stop_and_reload("allocator", FLAGS_stop, FLAGS_reload);
    }

    // check and lock
    Files files;
    if (!check_and_lock("allocator", files)) {
        return -1;
    }

    // register signal handler
    register_signal_handler();

    if (0 != ::aries::common::init_local_addr()) {
        LOG(ERROR) << "fail to start allocator due to init local addr failed";
        return -1;
    }
    std::string bvar_monitor_include;
    google::GetCommandLineOption("bvar_monitor_include", &bvar_monitor_include);
    if (bvar_monitor_include.empty()) {
        google::SetCommandLineOption("bvar_monitor_include", "allocator*");
    }
    std::string bvar_monitor_exclude;
    google::GetCommandLineOption("bvar_monitor_exclude", &bvar_monitor_exclude);
    if (bvar_monitor_exclude.empty()) {
        google::SetCommandLineOption("bvar_monitor_exclude", "*80*;*9999*;*cdf*;*percentiles*");
    }
    
    google::SetCommandLineOption("bvar_dump", "true");

    // init log
    if (0 != aries::common::init_comlog()) {
        fprintf(stderr, "fail to init comlog\n");
        return -1;
    }

    ARIES_MKDIR("./meta", "fail to start allocator due to mkdir ./meta failed");

    baidu::rpc::FLAGS_defer_close_second = 120 * 60;

    aries::allocator::g_blob_allocator->init();
    if (aries::allocator::g_blob_allocator->start()) {
        return -1;
    };
    aries::common::start_tcmalloc_memory_scheduler();
    aries::allocator::g_blob_allocator->join();

    LOG(FATAL) << "allocator exit";
    return 0;
}

