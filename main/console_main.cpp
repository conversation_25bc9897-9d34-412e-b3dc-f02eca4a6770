/***************************************************************************
 * 
 * Copyright (c) 2021 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/
 
 /**
 * @file console_main.cpp
 * <AUTHOR>
 * @date 2021/03/17 21:05:52
 * @version 1.0 
 * @brief 
 *  
 **/

#include <base/logging.h>
#include "baidu/inf/aries/main/main_utils.h"
#include "baidu/inf/aries-api/common/config.h"
#include "baidu/inf/aries/cli/console/console.h"
#include "baidu/inf/aries/cli/console/console_service.h"

DECLARE_string(flagfile);
DEFINE_bool(h, false, "print help information");
DEFINE_bool(v, false, "print version information");
DEFINE_bool(stop, false, "stop console");

static const char *s_proc_name = NULL;
#if defined( __DATE__ ) && defined ( __TIME__ )
static const char *s_build_time = __DATE__ " " __TIME__;
#else
static const char *s_build_time = "unknown";
#endif

#ifndef __ARIES_VERSION_ID__
#define __ARIES_VERSION_ID__ "unknown"
#endif

#ifndef __ARIES_REVISION_ID__
#define __ARIES_REVISION_ID__ "unknown"
#endif

#ifndef __ARIES_REPO_URL__
#define __ARIES_REPO_URL__   "unknown"
#endif

#ifndef __ARIES_BUILDHOST__
#define __ARIES_BUILDHOST__  "unknown"
#endif

void stop_console() {
    aries::cli::console::g_console->stop();
    //XXX: exit right now
    _exit(0);
}

void register_signal_handler() {
    SignalHandler::set_module_name("console");
    SignalHandler::set_func_on_exit(stop_console);
    SignalHandler::register_signal_handler();
}

int main(int argc, char* argv[]) {
    // set version
    std::string version_str;
    base::string_printf(&version_str,
                        "Version: %s\n"
                        "Revision: %s\n"
                        "RepoURL: %s\n"
                        "Build: %s %s\n",
                        __ARIES_VERSION_ID__, __ARIES_REVISION_ID__, __ARIES_REPO_URL__,
                        s_build_time, __ARIES_BUILDHOST__);
    google::SetVersionString(version_str);
    // set usage
    s_proc_name = strrchr(argv[0], '/');
    if (s_proc_name == NULL) {
        s_proc_name = argv[0];
    } else {
        ++s_proc_name;
    }
    std::string help_str;
    base::string_printf(&help_str,
                        "Usage: %s [OPTIONS...]\n"
                        "Options:\n"
                        "  -h              Print this help message.\n"
                        "  -v              Print version number.\n"
                        "  -stop           stop console.\n"
                        "  -flagfile=$path Load flags from file.",
                        s_proc_name);
    google::SetUsageMessage(help_str);

    struct stat st;
    if (0 == stat("conf/console.conf", &st) && FLAGS_flagfile.empty()) {
        FLAGS_flagfile = "conf/console.conf";
    }
    google::ParseCommandLineFlags(&argc, &argv, true);
    // check -v and -h
    if (FLAGS_v) {
        fprintf(stderr, "%s\n", version_str.c_str());
        _exit(0);
    }
    if (FLAGS_h) {
        fprintf(stderr, "%s\n", help_str.c_str());
        _exit(0);
    }

    // check and lock
    Files files;
    if (!check_and_lock("console", files)) {
        return -1;
    }

    // register signal handler
    register_signal_handler();

    if (0 != aries::common::init_comlog()) {
        fprintf(stderr, "fail to init comlog\n");
        return -1;
    }
    aries::cli::console::g_console.reset(new aries::cli::console::Console);
    if (aries::cli::console::g_console->start() != 0) {
        LOG(FATAL) << "fail to start console service";
        return -1;
    }

    aries::cli::console::g_console->join();
    LOG(FATAL) << "console service exit";
}

/* vim: set ts=4 sw=4 sts=4 tw=100 */
