// Copyright (c) 2021, ByteDance Inc. All rights reserved.

#pragma once

#include <byte/string/string_piece.h>

#include <string>
#include <utility>

#include "byterpc/protocol/http/details/http_status_code.h"
#include "byterpc/protocol/http/details/uri.h"
#include "byterpc/util/case_insensitive_map.h"

namespace byterpc {

////////////////
// http_method
enum HttpMethod {
    HTTP_METHOD_DELETE = 0,
    HTTP_METHOD_GET = 1,
    HTTP_METHOD_HEAD = 2,
    HTTP_METHOD_POST = 3,
    HTTP_METHOD_PUT = 4,
    HTTP_METHOD_CONNECT = 5,
    HTTP_METHOD_OPTIONS = 6,
    HTTP_METHOD_TRACE = 7,
    HTTP_METHOD_COPY = 8,
    HTTP_METHOD_LOCK = 9,
    HTTP_METHOD_MKCOL = 10,
    HTTP_METHOD_MOVE = 11,
    HTTP_METHOD_PROPFIND = 12,
    HTTP_METHOD_PROPPATCH = 13,
    HTTP_METHOD_SEARCH = 14,
    HTTP_METHOD_UNLOCK = 15,
    HTTP_METHOD_REPORT = 16,
    HTTP_METHOD_MKACTIVITY = 17,
    HTTP_METHOD_CHECKOUT = 18,
    HTTP_METHOD_MERGE = 19,
    HTTP_METHOD_MSEARCH = 20,  // M-SEARCH
    HTTP_METHOD_NOTIFY = 21,
    HTTP_METHOD_SUBSCRIBE = 22,
    HTTP_METHOD_UNSUBSCRIBE = 23,
    HTTP_METHOD_PATCH = 24,
    HTTP_METHOD_PURGE = 25,
    HTTP_METHOD_MKCALENDAR = 26
};

// Returns literal description of `http_method'. "UNKNOWN" on not found.
const char* HttpMethod2Str(HttpMethod http_method);

// Convert case-insensitive `method_str' to enum HttpMethod.
// Returns true on success.
bool Str2HttpMethod(const char* method_str, HttpMethod* method);

////////////////
// http_header
class HttpHeader {
public:
    typedef CaseInsensitiveMap<std::string> HeaderMap;
    typedef HeaderMap::const_iterator HeaderIterator;

    HttpHeader();

    // Exchange internal fields with another HttpHeader.
    void Swap(HttpHeader& rhs);

    // Reset internal fields as if they're just default-constructed.
    void Clear();

    // Get http version, 1.1 by default.
    int major_version() const {
        return _version.first;
    }
    int minor_version() const {
        return _version.second;
    }
    // Change the http version
    void set_version(int http_major, int http_minor) {
        _version = std::make_pair(http_major, http_minor);
    }

    // True if version of http is earlier than 1.1
    bool before_http_1_1() const {
        return (major_version() * 10000 + minor_version()) <= 10000;
    }

    // True if the message is from HTTP2.
    bool is_http2() const {
        return major_version() == 2;
    }

    // Get/set "Content-Type". Notice that you can't get "Content-Type"
    // via GetHeader().
    // possible values: "text/plain", "application/json" ...
    const std::string& content_type() const {
        return _content_type;
    }
    void set_content_type(const std::string& type) {
        _content_type = type;
    }
    void set_content_type(const char* type) {
        _content_type = type;
    }
    std::string& mutable_content_type() {
        return _content_type;
    }

    // Get value of a header which is case-insensitive according to:
    //   https://www.w3.org/Protocols/rfc2616/rfc2616-sec4.html#sec4.2
    // Namely, GetHeader("log-id"), GetHeader("Log-Id"), GetHeader("LOG-ID")
    // point to the same value.
    // Return pointer to the value, NULL on not found.
    // NOTE: Not work for "Content-Type", call content_type() instead.
    const std::string* GetHeader(const char* key) const {
        auto it = _headers.find(std::string(key));
        if (it == _headers.end()) {
            return nullptr;
        }
        return &it->second;
    }
    const std::string* GetHeader(const std::string& key) const {
        auto it = _headers.find(key);
        if (it == _headers.end()) {
            return nullptr;
        }
        return &it->second;
    }

    // Set value of a header.
    // NOTE: Not work for "Content-Type", call set_content_type() instead.
    void SetHeader(const std::string& key, const std::string& value) {
        GetOrAddHeader(key) = value;
    }

    // Remove a header.
    void RemoveHeader(const char* key) {
        _headers.erase(key);
    }
    void RemoveHeader(const std::string& key) {
        _headers.erase(key);
    }

    // Append value to a header. If the header already exists, separate
    // old value and new value with comma(,) according to:
    //   https://www.w3.org/Protocols/rfc2616/rfc2616-sec4.html#sec4.2
    void AppendHeader(const std::string& key, const byte::StringPiece& value);

    // Get header iterators which are invalidated after calling AppendHeader()
    HeaderIterator HeaderBegin() const {
        return _headers.begin();
    }
    HeaderIterator HeaderEnd() const {
        return _headers.end();
    }
    // #headers
    size_t HeaderCount() const {
        return _headers.size();
    }

    const URI& uri() const {
        return _uri;
    }
    URI& uri() {
        return _uri;
    }

    // Get/set http method.
    HttpMethod method() const {
        return _method;
    }
    void set_method(const HttpMethod method) {
        _method = method;
    }

    // Get/set status-code and reason-phrase. Notice that the const char*
    // returned by reason_phrase() will be invalidated after next call to
    // set_status_code().
    int status_code() const {
        return _status_code;
    }
    const char* reason_phrase() const;
    void set_status_code(int status_code);

    // TODO(fuziang): it will be supported in ByteRPC when restful api implements
    // The URL path removed with matched prefix.
    // NOTE: always normalized and NOT started with /.
    //
    // Accessing HttpService.Echo
    //   [URL]                               [unresolved_path]
    //   "/HttpService/Echo"                 ""
    //   "/HttpService/Echo/Foo"             "Foo"
    //   "/HttpService/Echo/Foo/Bar"         "Foo/Bar"
    //   "/HttpService//Echo///Foo//"        "Foo"
    //
    // Accessing FileService.default_method:
    //   [URL]                               [unresolved_path]
    //   "/FileService"                      ""
    //   "/FileService/123.txt"              "123.txt"
    //   "/FileService/mydir/123.txt"        "mydir/123.txt"
    //   "/FileService//mydir///123.txt//"   "mydir/123.txt"
    const std::string& unresolved_path() const {
        return _unresolved_path;
    }

private:
    friend class HttpMessage;
    friend class HttpServerController;

    std::string& GetOrAddHeader(const std::string& key) {
        return _headers[key];
    }

    HeaderMap _headers;
    URI _uri;
    int _status_code;
    HttpMethod _method;
    std::string _content_type;
    std::string _unresolved_path;
    std::pair<int, int> _version;
};

const HttpHeader& DefaultHttpHeader();

}  // namespace byterpc
