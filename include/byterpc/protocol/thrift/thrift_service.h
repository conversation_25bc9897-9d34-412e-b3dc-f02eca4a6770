// Copyright (c) 2024, ByteDance Inc. All rights reserved.
#pragma once

#include "byterpc/controller.h"
#include "byterpc/protocol/thrift/thrift_message.h"

namespace byterpc {

// If user uses thrift protocol, they are supposed to implement service method
// by inheriting `ThriftService`.
class ThriftService {
public:
    virtual ~ThriftService() {}

    // Dispatch respective method by thrift method name parsed from wire format data.
    virtual void ProcessThriftFramedRequest(Controller* controller,
                                            ThriftFramedMessage* request,
                                            ThriftFramedMessage* response,
                                            ::google::protobuf::Closure* done) = 0;
};

}  // namespace byterpc
