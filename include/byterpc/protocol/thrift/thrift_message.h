// Copyright (c) 2024, ByteDance Inc. All rights reserved.

// Copyright (c) 2014 Baidu, Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

#pragma once

#include <thrift/TApplicationException.h>
#include <thrift/Thrift.h>
#include <thrift/protocol/TBinaryProtocol.h>
#include <thrift/transport/TBufferTransports.h>
#include <memory>

#include "byterpc/builder.h"
#include "byterpc/io_buf.h"
#include "byterpc/util/logging.h"

namespace byterpc {

// In order to tackle compatiblity problem
// Problem: TBase is absent in thrift 0.9.3
// Solution: Wrap native messages with templates into instances inheriting
//   from ThriftMessageBase which can be stored and handled uniformly.
class ThriftMessageBase {
public:
    virtual ~ThriftMessageBase() {}
    virtual uint32_t Read(::apache::thrift::protocol::TProtocol* iprot) = 0;
    virtual uint32_t Write(::apache::thrift::protocol::TProtocol* oprot) const = 0;
};

// The message could either contains a raw binary framed data (IOBuf)
// the binary data can be converted / deserialized into thrift message
// or, it may contain a raw thrift message, representing a thrift framed
// request or response.
class ThriftFramedMessage : public ::google::protobuf::Message {
    friend class ThriftStub;

public:
    ThriftFramedMessage();
    virtual ~ThriftFramedMessage();

    ThriftMessageBase* raw_instance() const {
        return _raw_instance;
    }

    // Used in server side, users cast ThriftFramedMessage to raw thrift message.
    template <typename T> T* Cast();

    // Return nullptr, it's specific that this is a thrift message in byterpc
    static const ::google::protobuf::Descriptor* descriptor();

    void Swap(ThriftFramedMessage* other);

    // Implements ::google::protobuf::Message, otherwise it may be an abstract class
    ThriftFramedMessage* New() const override;
    void Clear() override;
    bool IsInitialized() const override;
    int GetCachedSize() const override;
    ::google::protobuf::Metadata GetMetadata() const override;

public:
    // Serialized raw thrift message
    IOBuf body;

    // `field_id` must be set when `body` is set.
    // It represents the type of `body`, including
    // THRIFT_INVALID_FID/ THRIFT_REQUEST_FID/ THRIFT_RESPONSE_FID
    int16_t field_id;

private:
    bool _own_raw_instance;
    ThriftMessageBase* _raw_instance;

    DISALLOW_COPY_AND_ASSIGN(ThriftFramedMessage);
};

// In ByteRPC, we introduce the `ThriftStub`, because there's no automatically
// generated thrift stub since we didn't introduce a sound thrift path now,
// which can result in immense code modification.
class ThriftStub {
public:
    explicit ThriftStub(std::shared_ptr<Builder::Channel> channel) : _channel(channel) {}

    // NOTE: It's the user's duty to manage the lifespan of `raw_request` and `raw_response`,
    //       NONE of them can be released UNTIL the request finishes.
    template <typename REQUEST, typename RESPONSE>
    void CallMethod(const char* method_name,
                    Controller* controller,
                    const REQUEST* raw_request,
                    RESPONSE* raw_response,
                    ::google::protobuf::Closure* done);

    // Typically used for proxy mode, in which user passes ThriftFramedMessage*
    // as another client's input.
    // E.g., in server router method XXXService::ProcessThriftFramedRequest,
    // method A can initialize a stub to send the input ThriftFramedMessage*
    // req & resp to another method B, which could be required in some cases.
    //
    void CallMethod(const char* method_name,
                    Controller* cntl,
                    const ThriftFramedMessage* req,
                    ThriftFramedMessage* res,
                    ::google::protobuf::Closure* done);

private:
    std::shared_ptr<Builder::Channel> _channel;
};

namespace details {

// Used for ThriftStub::CallMethod, in order to encapsulte user-input
// raw thrift messages, since ThriftFramedMessage only accepts `ThriftMessageBase`
// instance. As mentioned in the comment of `ThriftMessageBase`, without using
// TBase, we should implement the wrapper layer.
template <typename T> class ThriftMessageWrapper final : public ThriftMessageBase {
public:
    ThriftMessageWrapper() : msg_ptr(nullptr) {}
    explicit ThriftMessageWrapper(T* msg2) : msg_ptr(msg2) {}
    virtual ~ThriftMessageWrapper() {}
    // NOTE: "T::" makes the function call work around vtable
    uint32_t Read(::apache::thrift::protocol::TProtocol* iprot) override {
        return msg_ptr->T::read(iprot);
    }
    uint32_t Write(::apache::thrift::protocol::TProtocol* oprot) const override {
        return msg_ptr->T::write(oprot);
    }
    T* msg_ptr;
};

template <typename T> class ThriftMessageHolder final : public ThriftMessageBase {
public:
    virtual ~ThriftMessageHolder() {}
    // NOTE: "T::" makes the function call work around vtable
    uint32_t Read(::apache::thrift::protocol::TProtocol* iprot) override {
        return msg.T::read(iprot);
    }
    uint32_t Write(::apache::thrift::protocol::TProtocol* oprot) const override {
        return msg.T::write(oprot);
    }
    T msg;
};

// A wrapper closure to own additional stuffs required by ThriftStub
template <typename RESPONSE> class ThriftDoneWrapper : public ::google::protobuf::Closure {
public:
    explicit ThriftDoneWrapper(::google::protobuf::Closure* done) : _done(done) {}
    void Run() override {
        _done->Run();
        delete this;
    }

private:
    ::google::protobuf::Closure* _done;

public:
    ThriftMessageWrapper<RESPONSE> raw_response_wrapper;
    ThriftFramedMessage response;
};

// Extract thrift message from memory segments in IOBuf, meanwhile it should also
// match the expected_fid, which could be either THRIFT_RESPONSE_FID or THRIFT_REQUEST_FID.
bool ReadThriftStruct(IOBuf* body, ThriftMessageBase* raw_msg, int16_t expected_fid);

}  // namespace details

// Cast binary stream to thrift raw message, merely using in server side now.
template <typename T> T* ThriftFramedMessage::Cast() {
    if (_raw_instance) {
        auto p = dynamic_cast<details::ThriftMessageHolder<T>*>(_raw_instance);
        if (p) {
            return &p->msg;
        }
        delete _raw_instance;
    }

    // `raw_msg_wrapper` will be released by ThriftFramedMessage if `_own_raw_instance`
    // is set.
    auto raw_msg_wrapper = new details::ThriftMessageHolder<T>;
    T* raw_msg = &raw_msg_wrapper->msg;
    _raw_instance = raw_msg_wrapper;
    _own_raw_instance = true;

    if (!body.empty()) {
        if (!details::ReadThriftStruct(&body, _raw_instance, field_id)) {
            BYTERPC_LOG(ERROR) << "Fail to run ReadThriftStruct in ThriftFramedMessage::Cast";
        }
    }
    return raw_msg;
}

// Encapsulte `raw_request` and `raw_response` properly by allocating ThriftFramedMessage for
// each of them, and the lifespan of them are well-managed.
template <typename REQUEST, typename RESPONSE>
void ThriftStub::CallMethod(const char* method_name,
                            Controller* controller,
                            const REQUEST* raw_request,
                            RESPONSE* raw_response,
                            ::google::protobuf::Closure* done) {
    BYTERPC_CHECK(method_name);
    BYTERPC_CHECK(controller);
    controller->SetThriftMethodName(method_name);

    details::ThriftMessageWrapper<REQUEST> raw_request_wrapper(const_cast<REQUEST*>(raw_request));
    ThriftFramedMessage request;
    request._raw_instance = &raw_request_wrapper;

    // Let the new_done own the response and release it after Run(), thereby it can
    // assure that ThriftFramedMessage can be accessible in a right sequence.
    // TODO(fza): may implement fiber mode, as a fiber here we should wait to be awaken
    details::ThriftDoneWrapper<RESPONSE>* new_done = new details::ThriftDoneWrapper<RESPONSE>(done);
    new_done->raw_response_wrapper.msg_ptr = raw_response;
    new_done->response._raw_instance = &new_done->raw_response_wrapper;

    // call ProtobufChannel::CallMethod directly
    _channel->CallMethod(nullptr, controller, &request, &new_done->response, new_done);
}

}  // namespace byterpc
