// Copyright (c) 2024, ByteDance Inc. All rights reserved.
#pragma once

#include <cstdint>
#include <ostream>
#include <string>

namespace byterpc {

// mem holder must have a **unique** mem type
enum class HolderType {
    Default = 1,  // kernel tcp memory
    BE = 2,
    Tarzan = 3,
    UserHuge = 4,    // User alloc memory with hugepage
    UserNormal = 5,  // user alloc memory without hugepage
    Unify = 6,       // Memory pool
};

struct MemPageInfo {
    HolderType hold_type;
    // virtual addr
    void* vaddr;
    // physical addr, fill nullptr
    // TODO(chenmeng) : paddr is an array, and size is nr_page. Because we not use it
    // when register to tarzan now, so fill nullptr.
    uint64_t* paddr;
    // memory size
    uint64_t len;
    // page nr is calculated by (len / page_sz), only used when register to tarzan
    uint32_t nr_page;
    // align page size, only used when register to tarzan
    uint32_t page_sz;

    MemPageInfo()
        : hold_type(HolderType::Default),
          vaddr(nullptr),
          paddr(nullptr),
          len(0),
          nr_page(0),
          page_sz(0) {}

    MemPageInfo(
        HolderType hold_t, void* va, uint64_t* pa, uint64_t mem_sz, uint32_t nr, uint32_t sz)
        : hold_type(hold_t), vaddr(va), paddr(pa), len(mem_sz), nr_page(nr), page_sz(sz) {}

    bool ContainAddr(const void* addr) const {
        return (vaddr <= addr &&
                static_cast<const uint8_t*>(addr) < static_cast<uint8_t*>(vaddr) + len);
    }
};

inline std::ostream& operator<<(std::ostream& os, const HolderType& p) {
    switch (p) {
    case HolderType::Default:
        os << "Default";
        break;
    case HolderType::BE:
        os << "Be";
        break;
    case HolderType::Tarzan:
        os << "Tarzan";
        break;
    case HolderType::UserHuge:
        os << "UserHuge";
        break;
    case HolderType::UserNormal:
        os << "UserNormal";
        break;
    case HolderType::Unify:
        os << "Unify";
        break;
    default:
        os << "Unknown type";
        break;
    }
    return os;
}

inline bool operator!=(const MemPageInfo& lhs, const MemPageInfo& rhs) {
    return (lhs.hold_type != rhs.hold_type || lhs.vaddr != rhs.vaddr || lhs.len != rhs.len);
}

inline std::ostream& operator<<(std::ostream& os, const MemPageInfo& p) {
    os << "T: " << p.hold_type << " VA: " << p.vaddr << " PA: " << p.paddr << " len: " << p.len
       << " nr_page: " << p.nr_page << " page_sz: " << p.page_sz;

    return os;
}

}  // namespace byterpc
