// Copyright (c) 2023, ByteDance Inc. All rights reserved.
#pragma once

#include <memory>
#include <sstream>
#include <string>

#include "byte/include/byte_log.h"
#include "byterpc/util/compiler_specific.h"
#include "byterpc/util/macros.h"

#define BYTERPC_LAZY_STREAM(stream, condition) \
    !(condition) ? (void)0 : byterpc::util::LogMessageVoidify() & (stream)

#define BYTERPC_LOG_IS_ON(severity) (byte::LOG_LEVEL_##severity >= byterpc::GetMinLogLevel())

#define BYTERPC_LOG_STREAM(severity)                                                             \
    (byte::LogMessager(byterpc::byterpc_log_ctx, __FILE__, __LINE__, byte::LOG_LEVEL_##severity) \
         .stream())

#define BYTERPC_LOG(severity) \
    BYTERPC_LAZY_STREAM(BYTERPC_LOG_STREAM(severity), BYTERPC_LOG_IS_ON(severity))

#define BYTERPC_LOG_IF(severity, condition) \
    BYTERPC_LAZY_STREAM(BYTERPC_LOG_STREAM(severity), BYTERPC_LOG_IS_ON(severity) && (condition))

#define DUMPSTACK_LOG_STREAM(severity)                                                             \
    (byte::LogMessager(byterpc::dumpstack_log_ctx, __FILE__, __LINE__, byte::LOG_LEVEL_##severity) \
         .stream())

#define BYTERPC_DUMPSTACK_LOG(severity) \
    BYTERPC_LAZY_STREAM(DUMPSTACK_LOG_STREAM(severity), BYTERPC_LOG_IS_ON(severity))

#define BYTERPC_PLOG_STREAM(severity)                                                     \
    (byterpc::util::ErrnoLogMessage(                                                      \
         byterpc::byterpc_log_ctx, __FILE__, __LINE__, byte::LOG_LEVEL_##severity, errno) \
         .stream())

#define BYTERPC_PLOG(severity) \
    BYTERPC_LAZY_STREAM(BYTERPC_PLOG_STREAM(severity), BYTERPC_LOG_IS_ON(severity))

#define BYTERPC_PLOG_IF(severity, condition) \
    BYTERPC_LAZY_STREAM(BYTERPC_PLOG_STREAM(severity), BYTERPC_LOG_IS_ON(severity) && (condition))

#define BYTERPC_CHECK(condition) \
    BYTERPC_LOG_IF(FATAL, BYTERPC_UNLIKELY(!(condition))) << "Check failed: " #condition " "

#define BYTERPC_CHECK_OP(name, op, val1, val2)                                      \
    while (std::unique_ptr<std::string> _result = byterpc::util::Check##name##Impl( \
               (val1), (val2), "Check failed: " #val1 " " #op " " #val2 " "))       \
    BYTERPC_LOG(FATAL) << (*_result)

#define BYTERPC_CHECK_EQ(val1, val2) BYTERPC_CHECK_OP(EQ, ==, val1, val2)
#define BYTERPC_CHECK_NE(val1, val2) BYTERPC_CHECK_OP(NE, !=, val1, val2)
#define BYTERPC_CHECK_LE(val1, val2) BYTERPC_CHECK_OP(LE, <=, val1, val2)
#define BYTERPC_CHECK_LT(val1, val2) BYTERPC_CHECK_OP(LT, <, val1, val2)
#define BYTERPC_CHECK_GE(val1, val2) BYTERPC_CHECK_OP(GE, >=, val1, val2)
#define BYTERPC_CHECK_GT(val1, val2) BYTERPC_CHECK_OP(GT, >, val1, val2)

#define BYTERPC_LOG_EVERY_INTERNAL(severity, n, logging_occurrences_mod_n) \
    static int logging_occurrences_mod_n = 0;                              \
    if (++logging_occurrences_mod_n > n)                                   \
        logging_occurrences_mod_n -= n;                                    \
    if (logging_occurrences_mod_n == 1)                                    \
    BYTERPC_LOG(severity)

#define BYTERPC_LOG_EVERY(severity, n) \
    BYTERPC_LOG_EVERY_INTERNAL(severity, n, DEFER_3(logging_occurrences_mod_n))

#define BYTERPC_LOG_IF_EVERY_INTERNAL(severity, condition, n, logging_occurrences_mod_n) \
    static int logging_occurrences_mod_n = 0;                                            \
    if ((condition) &&                                                                   \
        (logging_occurrences_mod_n = (logging_occurrences_mod_n + 1) % n) == (1 % n))    \
    BYTERPC_LOG(severity)

#define BYTERPC_LOG_IF_EVERY(severity, condition, n) \
    BYTERPC_LOG_IF_EVERY_INTERNAL(severity, condition, n, DEFER_3(logging_occurrences_mod_n))

#if defined(NDEBUG) && !defined(DCHECK_ALWAYS_ON)
#define BYTERPC_DCHECK_IS_ON() 0
#else
#define BYTERPC_DCHECK_IS_ON() 1
#endif

// debug-only checking.  executed if DCHECK_IS_ON().
#if BYTERPC_DCHECK_IS_ON()

#define BYTERPC_DLOG(severity) BYTERPC_LOG(severity)
#define BYTERPC_DLOG_IF(severity, condition) BYTERPC_LOG_IF(severity, condition)
#define BYTERPC_DCHECK(condition) BYTERPC_CHECK(condition)
#define BYTERPC_DCHECK_EQ(val1, val2) BYTERPC_CHECK_EQ(val1, val2)
#define BYTERPC_DCHECK_NE(val1, val2) BYTERPC_CHECK_NE(val1, val2)
#define BYTERPC_DCHECK_LE(val1, val2) BYTERPC_CHECK_LE(val1, val2)
#define BYTERPC_DCHECK_LT(val1, val2) BYTERPC_CHECK_LT(val1, val2)
#define BYTERPC_DCHECK_GE(val1, val2) BYTERPC_CHECK_GE(val1, val2)
#define BYTERPC_DCHECK_GT(val1, val2) BYTERPC_CHECK_GT(val1, val2)

#else  // !BYTERPC_DCHECK_IS_ON()

#define BYTERPC_DLOG(severity) BYTERPC_LAZY_STREAM(BYTERPC_LOG_STREAM(severity), (false))
#define BYTERPC_DLOG_IF(severity, condition) \
    BYTERPC_LAZY_STREAM(BYTERPC_LOG_STREAM(severity), (false && (condition)))
#define BYTERPC_DCHECK(condition) \
    while (false)                 \
    BYTERPC_CHECK(condition)
#define BYTERPC_DCHECK_EQ(val1, val2) \
    while (false)                     \
    BYTERPC_CHECK_EQ(val1, val2)
#define BYTERPC_DCHECK_NE(val1, val2) \
    while (false)                     \
    BYTERPC_CHECK_NE(val1, val2)
#define BYTERPC_DCHECK_LE(val1, val2) \
    while (false)                     \
    BYTERPC_CHECK_LE(val1, val2)
#define BYTERPC_DCHECK_LT(val1, val2) \
    while (false)                     \
    BYTERPC_CHECK_LT(val1, val2)
#define BYTERPC_DCHECK_GE(val1, val2) \
    while (false)                     \
    BYTERPC_CHECK_GE(val1, val2)
#define BYTERPC_DCHECK_GT(val1, val2) \
    while (false)                     \
    BYTERPC_CHECK_GT(val1, val2)

#endif

namespace byterpc {

extern byte::LogContext* byterpc_log_ctx;
extern byte::LogContext* dumpstack_log_ctx;
extern byte::LogLevel rpc_log_level;
extern byte::LogLevel GetMinLogLevel();

namespace util {

// This class is used to explicitly ignore values in the conditional
// logging macros.  This avoids compiler warnings like "value computed
// is not used" and "statement has no effect".
class LogMessageVoidify {
public:
    LogMessageVoidify() {}
    // This has to be an operator with a precedence lower than << but
    // higher than ?:
    void operator&(std::ostream&) {}
};

// Build the error message string.  This is separate from the "Impl"
// function template because it is not performance critical and so can
// be out of line, while the "Impl" code should be inline.  Caller
// takes ownership of the returned string.
template <typename T1, typename T2>
std::unique_ptr<std::string> MakeCheckOpString(const T1& v1, const T2& v2, const char* exprtext) {
    std::ostringstream oss;
    oss << exprtext << " (" << v1 << " vs. " << v2 << ")";
    return std::make_unique<std::string>(std::string(oss.str()));
}

// Helper functions for CHECK_OP macro.
// The (int, int) specialization works around the issue that the compiler
// will not instantiate the template version of the function on values of
// unnamed enum type.
#define BYTERPC_DEFINE_CHECK_OP_IMPL(name, op)                                                    \
    template <class T1, class T2>                                                                 \
    inline std::unique_ptr<std::string> Check##name##Impl(                                        \
        const T1& v1, const T2& v2, const char* exprtext) {                                       \
        if (v1 op v2)                                                                             \
            return nullptr;                                                                       \
        else                                                                                      \
            return MakeCheckOpString(v1, v2, exprtext);                                           \
    }                                                                                             \
    inline std::unique_ptr<std::string> Check##name##Impl(int v1, int v2, const char* exprtext) { \
        if (v1 op v2)                                                                             \
            return nullptr;                                                                       \
        else                                                                                      \
            return MakeCheckOpString(v1, v2, exprtext);                                           \
    }
BYTERPC_DEFINE_CHECK_OP_IMPL(EQ, ==)
BYTERPC_DEFINE_CHECK_OP_IMPL(NE, !=)
BYTERPC_DEFINE_CHECK_OP_IMPL(LE, <=)
BYTERPC_DEFINE_CHECK_OP_IMPL(LT, <)
BYTERPC_DEFINE_CHECK_OP_IMPL(GE, >=)
BYTERPC_DEFINE_CHECK_OP_IMPL(GT, >)
#undef BYTERPC_DEFINE_CHECK_OP_IMPL

class ErrnoLogMessage {
public:
    ErrnoLogMessage(byte::LogContext* log_context,
                    const char* file,
                    int line,
                    byte::LogLevel level,
                    int err,
                    bool use_customized_logger = false)
        : err_(err), log_message_(log_context, file, line, level, use_customized_logger) {}

    ~ErrnoLogMessage();

    std::ostream& stream() {
        return log_message_.stream();
    }

private:
    int err_;
    byte::LogMessager log_message_;

    DISALLOW_COPY_AND_ASSIGN(ErrnoLogMessage);
};

}  // namespace util

}  // namespace byterpc
