// Copyright (c) 2011 Baidu, Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

// Author: <PERSON><PERSON>,<PERSON> (<EMAIL>)
// Date: Mon. Nov 7 14:47:36 CST 2011

// Wrappers of IP and port.
#pragma once

#include <byte/base/hash.h>
#include <netinet/in.h>  // in_addr, in6_addr
#include <string.h>
#include <sys/un.h>

#include <iostream>  // std::ostream
#include <string>
#include <vector>

namespace byterpc {
namespace util {

// Type of an IPV6 address
typedef struct in6_addr ip_t;

static const in_addr IP4_ANY = {INADDR_ANY};
static const ip_t IP_ANY = in6addr_any;
static const ip_t IP_NONE = {
    255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255};
static const int UDS_PATH_MAX_LEN = sizeof(sockaddr_un::sun_path);

// Convert Ipv4 address to Ipv6 address
ip_t ipv4_to_ipv6(const in_addr& ipv4);
// Convert Ipv6 address to Ipv4 address
in_addr ipv6_to_ipv4(const ip_t& ipv6);

// Check `ip' is ipv4 or not
bool is_ipv4(const ip_t& ip);

// Check `ip` is any (IP_ANY for ipv6 or IP4_ANY for ipv4)
bool is_ip_any(const ip_t& ip);

// Convert |ip| to an integral
// For Ipv4, return ip.s_addr
// For Ipv6, return the last uint32_t inside in6_addr
inline in_addr_t ip2int(ip_t ip) {
#if defined(OS_MACOSX)
    return ip.__u6_addr.__u6_addr32[3];
#else
    return ip.s6_addr32[3];
#endif
}

inline void ip2Uints(ip_t ip, std::vector<uint32_t>* values) {
    if (is_ipv4(ip)) {
        values->push_back(ip2int(ip));
        return;
    }
    for (int i = 0; i < 4; ++i) {
#if defined(OS_MACOSX)
        values->push_back(ip.__u6_addr.__u6_addr32[i]);
#else
        values->push_back(ip.s6_addr32[i]);
#endif
    }
}

// Convert integral |ip_value| to an IP
ip_t int2ip(in_addr_t ip_value);  // network byte order

// Convert string `ip_str' to ip_t *ip, `ip_str' can be Ipv4 or Ipv6.
// IPv4 dotted-quad format: `127.0.0.1', `************' ...
// Ipv6 hexadecimal format: `::1', `ABCD:EF01:2345:6789:ABCD:EF01:2345:6789'
// Returns 0 on success, -1 otherwise.
int str2ip(const char* ip_str, ip_t* ip);

struct IPStr {
    char _buf[INET6_ADDRSTRLEN];

    const char* c_str() const {
        return _buf;
    }
};

// Convert IP to c-style string. Notice that you can serialize ip_t to
// std::ostream directly. Use this function when you don't have streaming log.
// Example: printf("ip=%s\n", ip2str(some_ip).c_str());
IPStr ip2str(ip_t ip);

// Convert `hostname' to ip_t *ip. If `hostname' is NULL, use hostname
// of this machine.
// `hostname' is typically in this form: `tc-cm-et21.tc' `db-cos-dev.db01' ...
// Returns 0 on success, -1 otherwise.
int hostname2ip(const char* hostname, ip_t* ip, bool ipv4 = true);

// Convert `ip' to `hostname'.
// Returns 0 on success, -1 otherwise and errno is set.
int ip2hostname(ip_t ip, char* hostname, size_t hostname_len);
int ip2hostname(ip_t ip, std::string* hostname);

// Hostname of this machine, "" on error.
// NOTE: This function caches result on first call.
const char* my_hostname();

// IP of this machine, IP_ANY on error.
// NOTE: This function caches result on first call.
ip_t my_ip(bool ipv4 = true);
// String form.
const char* my_ip_cstr(bool ipv4 = true);

// Check `ip' is local machine's ip
bool is_local_ip(const ip_t& ip);

class ExtendedEndPoint;

// ipv6 + port
struct EndPoint {
    ip_t ip;
    int port;

    EndPoint() : ip(IP_ANY), port(0) {}
    EndPoint(ip_t ip2, int port2);
    explicit EndPoint(const sockaddr_in6& in) : ip(in.sin6_addr), port(ntohs(in.sin6_port)) {}
    explicit EndPoint(const sockaddr_in& in)
        : ip(ipv4_to_ipv6(in.sin_addr)), port(ntohs(in.sin_port)) {}

    ~EndPoint();
    EndPoint(const EndPoint&);
    void operator=(const EndPoint&);

    void reset();
    bool is_extended() const;
};

static_assert(sizeof(EndPoint) == sizeof(EndPoint::ip) + sizeof(EndPoint::port),
              "EndPoint size mismatch with the one in POD-style, may cause ABI problem");

struct EndPointStr {
    // Size of _buf is safe here, since sizeof(sockaddr_un::sun_path) whose alias is
    // UDS_PATH_MAX_LEN, is typically larger than 100 Bytes in various platforms, far exceeding the
    // previous sizeof(INET6_ADDRSTRLEN)+16 of value 62.
    char _buf[sizeof("unix:") + UDS_PATH_MAX_LEN];

    const char* c_str() const {
        return _buf;
    }
};

struct EndPointHash {
    size_t operator()(const EndPoint& rhs) const {
        return (size_t)byte::XXHash(reinterpret_cast<const char*>(&rhs), sizeof(rhs));
    }
};

// Convert EndPoint to c-style string. Notice that you can serialize
// EndPoint to std::ostream directly. Use this function when you don't
// have streaming log.
// Example: printf("point=%s\n", endpoint2str(point).c_str());
EndPointStr endpoint2str(const EndPoint&);

// Convert string `ip_and_port_str' to a EndPoint *point.
// Returns 0 on success, -1 otherwise.
int str2endpoint(const char* ip_and_port_str, EndPoint* point);
int str2endpoint(const char* ip_str, int port, EndPoint* point);

// Convert `hostname_and_port_str' to a EndPoint *point.
// Returns 0 on success, -1 otherwise.
int hostname2endpoint(const char* ip_and_port_str, EndPoint* point, bool ipv4 = true);
int hostname2endpoint(const char* name_str, int port, EndPoint* point, bool ipv4 = true);

// Convert `endpoint' to `hostname'.
// Returns 0 on success, -1 otherwise and errno is set.
int endpoint2hostname(const EndPoint& point, char* hostname, size_t hostname_len);
int endpoint2hostname(const EndPoint& point, std::string* host);

// Convert `endpoint' to `sockaddr_storage'
int endpoint2sockaddr(const EndPoint& point, struct sockaddr_storage* sockaddr, socklen_t* len);
int sockaddr2endpoint(EndPoint* point, struct sockaddr_storage* sockaddr, socklen_t size);

// Create a TCP socket and connect it to `server'. Write port of this side
// into `self_port' if it's not NULL.
// Returns the socket descriptor, -1 otherwise and errno is set.
int tcp_connect(EndPoint server, int* self_port);

// Create and listen to a TCP socket bound with `ip_and_port'. If `reuse_addr'
// is true, ports in TIME_WAIT will be bound as well.
// Returns the socket descriptor, -1 otherwise and errno is set.
int tcp_listen(EndPoint ip_and_port, bool reuse_addr, bool reuse_port, bool reuse_uds_path = false);

// Get the local end of a socket connection
int get_local_side(int fd, EndPoint* out);

// Get the other end of a socket connection
int get_remote_side(int fd, EndPoint* out);

}  // namespace util
}  // namespace byterpc

#ifndef IN6_ADDR_OPERATOR
#define IN6_ADDR_OPERATOR
// Since ip_t is defined from in_addr which is globally defined, due to ADL
// we have to put overloaded operators globally as well.
inline bool operator<(byterpc::util::ip_t lhs, byterpc::util::ip_t rhs) {
    return memcmp(&lhs, &rhs, sizeof(lhs)) < 0;
}
inline bool operator>(byterpc::util::ip_t lhs, byterpc::util::ip_t rhs) {
    return rhs < lhs;
}
inline bool operator>=(byterpc::util::ip_t lhs, byterpc::util::ip_t rhs) {
    return !(lhs < rhs);
}
inline bool operator<=(byterpc::util::ip_t lhs, byterpc::util::ip_t rhs) {
    return !(rhs < lhs);
}
inline bool operator==(byterpc::util::ip_t lhs, byterpc::util::ip_t rhs) {
    return memcmp(&lhs, &rhs, sizeof(lhs)) == 0;
}
inline bool operator!=(byterpc::util::ip_t lhs, byterpc::util::ip_t rhs) {
    return !(lhs == rhs);
}

inline std::ostream& operator<<(std::ostream& os, const byterpc::util::IPStr& ip_str) {
    return os << ip_str.c_str();
}
inline std::ostream& operator<<(std::ostream& os, byterpc::util::ip_t ip) {
    return os << byterpc::util::ip2str(ip);
}
#endif

namespace byterpc {
namespace util {
// Overload operators for EndPoint in the same namespace due to ADL.
inline bool operator<(EndPoint p1, EndPoint p2) {
    return (p1.ip != p2.ip) ? (p1.ip < p2.ip) : (p1.port < p2.port);
}
inline bool operator>(EndPoint p1, EndPoint p2) {
    return p2 < p1;
}
inline bool operator<=(EndPoint p1, EndPoint p2) {
    return !(p2 < p1);
}
inline bool operator>=(EndPoint p1, EndPoint p2) {
    return !(p1 < p2);
}
inline bool operator==(EndPoint p1, EndPoint p2) {
    return p1.ip == p2.ip && p1.port == p2.port;
}
inline bool operator!=(EndPoint p1, EndPoint p2) {
    return !(p1 == p2);
}

inline std::ostream& operator<<(std::ostream& os, const EndPoint& ep) {
    return os << endpoint2str(ep).c_str();
}
inline std::ostream& operator<<(std::ostream& os, const EndPointStr& ep_str) {
    return os << ep_str.c_str();
}

}  // namespace util
}  // namespace byterpc
