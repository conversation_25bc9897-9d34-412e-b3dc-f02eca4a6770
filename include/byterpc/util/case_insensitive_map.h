// Copyright (c) 2021, ByteDance Inc. All rights reserved.
#pragma once

#include <string.h>

#include <map>
#include <string>

namespace byterpc {

struct CaseInsensitiveComparator {
    bool operator()(const std::string& a, const std::string& b) const noexcept {
        return ::strcasecmp(a.c_str(), b.c_str()) < 0;
    }
};

template <typename T>
using CaseInsensitiveMap = std::map<std::string, T, CaseInsensitiveComparator>;

}  // namespace byterpc
