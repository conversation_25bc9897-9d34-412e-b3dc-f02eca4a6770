// Copyright (c) 2021, ByteDance Inc. All rights reserved.
#pragma once
#include <stdint.h>

#cmakedefine BYTERPC_GIT_HASH "@BYTERPC_GIT_HASH@"
#cmakedefine BYTERPC_GIT_BRANCH "@BY<PERSON><PERSON><PERSON>_GIT_<PERSON>ANCH@"

namespace byterpc {

// NOTE: Please recheck the VERSION before launching a release!
// And we define the macro in case of acquiring the version by `strings` the binary.
#define __BYTERPC_VERSION_MAJOR__ 1
#define __BYTERPC_VERSION_MINOR__ 3 
#define __BYTERPC_VERSION_PATCH__ 1

static const int32_t kByterpcVersionMajor = __BYTERPC_VERSION_MAJOR__;
static const int32_t kByterpcVersionMinor = __BYTERPC_VERSION_MINOR__;
static const int32_t kByterpcVersionPatch = __BYTERPC_VERSION_PATCH__;

static const int32_t kMaxVersionSize = 100;

#define TO_BYTERPC_VERSION(major, minor, patch)       \
    ((major) * kMaxVersionSize * kMaxVersionSize  \
     + (minor) * kMaxVersionSize + (patch))          \

const int32_t kByterpcVersion = TO_BYTERPC_VERSION(
    kByterpcVersionMajor, kByterpcVersionMinor, kByterpcVersionPatch);
}  // namespace byterpc
