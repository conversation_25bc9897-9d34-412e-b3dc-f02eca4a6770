// Copyright (c) 2021, ByteDance Inc. All rights reserved.
#pragma once

// If some headers are exposed to user,
// we should keep our logging.h **not** inside this header,
// so for inline function inside header file,
// we should include this header to keep user logging untouched.

#include "byterpc/util/compiler_specific.h"

namespace byterpc {
namespace util {

extern void BYTERPC_PLOG_ERR(const char* s);
extern void BYTERPC_LOG_FATAL(const char* s);

}  // namespace util
}  // namespace byterpc

#define STRINGIZE_DETAIL(x) #x
#define STRINGIZE(x) STRINGIZE_DETAIL(x)

// As one important drawback is we will lose line info during function call,
// so we add a simple macro to supply file and line info.
#define _BYTERPC_PLOG_ERR(txt)                                                        \
    do {                                                                              \
        ::byterpc::util::BYTERPC_PLOG_ERR(__FILE__ ":" STRINGIZE(__LINE__) ": " txt); \
    } while (false)

#define _BYTERPC_LOG_FATAL(txt)                                                        \
    do {                                                                               \
        ::byterpc::util::BYTERPC_LOG_FATAL(__FILE__ ":" STRINGIZE(__LINE__) ": " txt); \
    } while (false)

#define _BYTERPC_CHECK(cond, txt)        \
    do {                                 \
        if (BYTERPC_UNLIKELY(!(cond))) { \
            _BYTERPC_LOG_FATAL(txt);     \
        }                                \
    } while (false)

// _BYTERPC_DCHECK follow same switch rule with glog
// copy from glog
#if defined(NDEBUG) && !defined(DCHECK_ALWAYS_ON)
#define _BYTERPC_DCHECK(cond)
#else
#define _BYTERPC_DCHECK(cond) _BYTERPC_CHECK(cond, "")
#endif
