// Copyright (c) 2021, ByteDance Inc. All rights reserved
#pragma once

#include <thread>
#include <vector>

namespace byterpc {
namespace util {

// Return the number of logical cores per NUMA node
size_t num_lcores_per_numa_node();

// Return a list of logical cores in \p numa_node
std::vector<size_t> get_lcores_for_numa_node(size_t numa_node);

// Bind \p thread to the \p cpu_index core
bool bind_to_core(std::thread& thread, size_t cpu_index);
// Bind current thread to the \p cpu_index core
bool bind_this_to_core(size_t cpu_index);

// Bind \p thread to the core with index numa_local_index on the socket == numa_node
bool bind_to_core(std::thread& thread, size_t numa_node, size_t numa_local_index);
// Bind current thread to the core with index numa_local_index on the socket == numa_node
bool bind_this_to_core(size_t numa_node, size_t numa_local_index);

int get_numa_num();

int get_numa_id_Impl();

// Get numa node id which the thread call this function.
// Return numa node id if success; otherwise return -1.
inline int get_numa_id_of_this_thread() {
    thread_local int current_thread_numa_id = get_numa_id_Impl();
    return current_thread_numa_id;
}

// Get numa node id by specified thread id.
// Return numa node id if success; otherwise return -1.
int get_numa_id_by_thread_id(pthread_t thread_id);

// Set current thread's memory policy to `MPOL_PREFERRED`.
// Return 0 if success; on error, -1 is returned and errno is set to indicate the error.
int set_thread_mempolicy_to_preferred(size_t numa_node);

// Set memory policy to `MPOL_PREFERRED` for specified memory.
// Return 0 if success; on error, -1 is returned and errno is set to indicate the error.
int set_mempolicy_to_preferred(void* addr, size_t len, size_t numa_node);

// Return the node id of the node on which the `addr` is allocated. If no page has yet been
// allocated for the specified address, this will allocate a page as if the thread had performed a
// read (load) access to that address, and return the id of the node where that page was allocated
int get_numa_id_of_mem(void* addr);

// Save current thread's memory policy and restore it when destructed.
// Works only when `enable` is true, otherwise do nothing.
class ThreadMemoryPolicyGuard {
public:
    ThreadMemoryPolicyGuard(bool enable);
    ~ThreadMemoryPolicyGuard();

private:
    class Impl;
    std::unique_ptr<Impl> _impl;
};

}  // namespace util
}  // namespace byterpc
