// Copyright (c) 2021, ByteDance Inc. All rights reserved.
#pragma once

#include <cstddef>
#include <cstdint>
#include <string>
#include <utility>

// TODO(dpw): move into `mem` dir ?

namespace byterpc {

// A sequences of definations about memory id.
// Memory that has registered to byterpc should use `UNIFY_ALLOCATED`, otherwise
// `DEFAULT_ALLOCATED`.
enum class MemoryId : uint16_t {
    DEFAULT_ALLOCATED = 0,
    TARZAN_ALLOCATED,
    BYTE_EXPRESS_ALLOCATED,
    UNIFY_ALLOCATED,

    // Add the defination of new memory id here and remember
    // to update the value of NUM_MEMORY_ID.

    NUM_MEMORY_ID,
};

inline std::string MemoryIdToString(MemoryId memory_id) {
    switch (memory_id) {
    case MemoryId::DEFAULT_ALLOCATED:
        return "default_allocated";
    case MemoryId::TARZAN_ALLOCATED:
        return "tarzan_allocated";
    case MemoryId::BYTE_EXPRESS_ALLOCATED:
        return "byte_express_allocated";
    case MemoryId::UNIFY_ALLOCATED:
        return "unify_allocated";
    default:
        return "unknown type";
    }
}

// A structure used to describe the meta of a memory region.
struct MemoryDescriptor {
    // if the virtual_address get by byterpc::Allocate, use the default value UNIFY_ALLOCATED
    MemoryId memory_id = MemoryId::UNIFY_ALLOCATED;

    // used for memory monitor
    // default 0, to keep user code unchanged.
    // At present byterpc support 16 modules, however module_id 0
    // is already used by byterpc internal, user can use from 1 to 15.
    uint8_t module_id = 0;

    size_t length;

    // 1. Physical address of this memory region, set zero if no such physical address exists.
    // 2. If tarzan send registered external memory, 0 means copy, non 0 means zero-copy.
    uint64_t physical_addr;

    void* virtual_address;

    // A param used for `MemoryHolder::free`.
    void* param;
};

}  // namespace byterpc
