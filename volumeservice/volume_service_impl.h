// Copyright (c) 2014 Baidu.com, Inc. All Rights Reserved
// Author: <EMAIL>
// Data: 2017-01-26 16:50
// Filename: volume_service_impl.h

#ifndef BAIDU_INF_ARIES_VOLUMESERVICE_VOLUME_SERVICE_IMPL_H
#define BAIDU_INF_ARIES_VOLUMESERVICE_VOLUME_SERVICE_IMPL_H 

#include "baidu/inf/aries-api/common/proto/volume_service.pb.h"
#include "baidu/inf/aries/meta_replica/meta_subscriber.h"
#include "public/common/base/strings/string_util.h"

namespace aries {
namespace volumeservice {

class VolumeServiceImpl : public ::aries::pb::volumeservice::VolumeService {
public:
    VolumeServiceImpl(aries::meta_replica::MetaSubscriber *meta_client)
        : _meta_client(meta_client) {};
    virtual ~VolumeServiceImpl() {};

    virtual void list_allocator(google::protobuf::RpcController* cntl_base,
        const ::aries::pb::volumeservice::ListAllocatorRequest* request,
        ::aries::pb::volumeservice::ListAllocatorResponse* response,
        google::protobuf::Closure* done);

    virtual void get_volume_info(google::protobuf::RpcController* cntl_base,
            const ::aries::pb::volumeservice::GetVolumeInfoRequest* request,
            ::aries::pb::volumeservice::GetVolumeInfoResponse* response,
            google::protobuf::Closure* done);

    virtual void get_space_info(google::protobuf::RpcController* cntl_base,
            const ::aries::pb::volumeservice::GetSpaceInfoRequest* request,
            ::aries::pb::volumeservice::GetSpaceInfoResponse* response,
            google::protobuf::Closure* done);

    virtual void get_da_info(google::protobuf::RpcController* cntl_base,
            const ::aries::pb::volumeservice::GetDataAgentInfoRequest* request,
            ::aries::pb::volumeservice::GetDataAgentInfoResponse* response,
            google::protobuf::Closure* done);
private:
    aries::meta_replica::MetaSubscriber *_meta_client;
};

}//end of namespace
}

#endif

