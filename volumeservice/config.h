/***************************************************************************
 * 
 * Copyright (c) 2017 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/
/**
 * @file volumeservice/config.h
 * <AUTHOR>
 * @date 2017/02/06 17:32:10
 * @brief 
 *  
 **/

#ifndef BAIDU_INF_ARIES_VOLUMESERVICE_CONFIG_H
#define BAIDU_INF_ARIES_VOLUMESERVICE_CONFIG_H

#include <gflags/gflags.h>
#include "baidu/inf/aries-api/common/config.h"

namespace aries {
namespace volumeservice {

DECLARE_string(token);

DECLARE_bool(recover_mode);

}
}

#endif

