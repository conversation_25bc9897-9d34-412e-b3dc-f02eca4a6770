/Makefile
/log
/output
.bcloud.cache/*
.vscode/*
tool/dashboard/*
.baidu.inf.aries.001.dc
.baidu.inf.aries.COMAKE.CONFIGS.SCM
.baidu.inf.aries.002.df
.baidu.inf.aries.BCLOUD.CONFIGS.SCM
.bcloud.flatten.workspace
.comake2.warnings
comake2.md5
logger_client_log
.resolution_info

# Compiled Object files
*.slo
*.lo
*.o
*.obj

# Precompiled Headers
*.gch
*.pch

# Compiled Dynamic libraries
*.so
*.dylib
*.dll

# Generate files
*.mod
*.smod
*.pb.h
*.pb.cc

# Compiled Static libraries
*.lai
*.la
*.a
*.lib

# Executables
*.exe
*.out
*.app
