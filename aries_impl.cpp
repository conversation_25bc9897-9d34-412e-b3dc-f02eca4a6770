/**
 * ARIES - A Reliable and Impressive EB-level Storage.
 * Copyright (c) 2016 Baidu.com, Inc. All Rights Reserved.
 *
 * Author: <PERSON> (<EMAIL>)
 * Date: 2016/08/31
 * Desciption: aries api entrance
 *
 */
#include "baidu/inf/aries-api/aries_impl.h"

#include <sstream>
#include <base/crc32c.h>
#include <base/fast_rand.h>

#include "baidu/inf/aries-api/aries.h"
#include "baidu/inf/aries-api/common/lock.h"
#include "baidu/inf/aries-api/aries_impl.h"
#include <base/crc32c.h>
#include <base/fast_rand.h>

#define ARIES_API_LOG(level) LOG(level) << "[trace_id: " << trace_id << "] "

namespace aries {

DEFINE_int64(log_threshold_for_get_latency_us, 1000000, "latency track will be logged if get latency exceeds the threshold, 1s by default");
DEFINE_int64(log_threshold_for_put_latency_us, 1000000, "latency track will be logged if put latency exceeds the threshold, 1s by default");
DEFINE_int64(log_threshold_for_remove_latency_us, 1000000, "latency track will be logged if remove latency exceeds the threshold, 1s by default");
DECLARE_int64(call_timeout_ms);
AriesRetryPolicy g_aries_retry_policy;
BAIDU_RPC_VALIDATE_GFLAG(log_threshold_for_get_latency_us, baidu::rpc::NonNegativeInteger);
BAIDU_RPC_VALIDATE_GFLAG(log_threshold_for_put_latency_us, baidu::rpc::NonNegativeInteger);
BAIDU_RPC_VALIDATE_GFLAG(log_threshold_for_remove_latency_us, baidu::rpc::NonNegativeInteger);

class NoticeLogGuard {
public:
    ~NoticeLogGuard() {
        LOG(NOTICE);
    }
};

class SharedProxyManager {
public:
    SharedProxyManager() {};
    ~SharedProxyManager() {};
    std::shared_ptr<Proxy> get_proxy(const ProxyConf& conf) {
        common::ScopedMutexLock lock(_mutex);
        if (_proxy_map.count(conf.proxy_name) == 0) {
            auto proxy = std::make_shared<Proxy>();
            if (proxy->init(conf) == 0) {
                LOG(NOTICE) << "global proxy:" << conf.proxy_name << " init succeeded";
                _proxy_map[conf.proxy_name] = proxy;
            } else {
                ARIES_FATAL_LOG() << "init proxy failed:" << conf.proxy_name;
                return nullptr;
            }
        }
        return _proxy_map[conf.proxy_name];
    }
private:
    common::MutexLock _mutex;
    // proxy_name <-> proxy
    std::map<std::string, std::shared_ptr<Proxy>> _proxy_map;
};

inline SharedProxyManager g_proxy_manager;

template<typename T1, typename T2>
void local_first_call(void (aries::pb::DADataService_Stub::*f)(::google::protobuf::RpcController*,
            const T1*, T2*, ::google::protobuf::Closure*),
        baidu::rpc::Channel *local, baidu::rpc::Channel *remote,
        baidu::rpc::Controller *cntl, const T1 *req, T2 *res) {
    if (local != NULL) {
        aries::pb::DADataService_Stub local_stub(local);
        (local_stub.*f)(cntl, req, res, NULL);
        if (!cntl->Failed()) {
            return;
        }
        int64_t timeout = cntl->timeout_ms() - cntl->latency_us() / 1000;
        uint64_t trace_id = cntl->log_id();
        if (timeout <= 0) {
            return;
        }
        ARIES_API_LOG(WARNING) << "access local channel failed, error:" << cntl->ErrorText()
            << ", try remote, remain_timeout_ms:" << timeout;
        base::IOBuf attachment;
        cntl->request_attachment().swap(attachment);
        cntl->Reset();
        cntl->set_log_id(trace_id);
        cntl->request_attachment().swap(attachment);
        cntl->set_timeout_ms(timeout);
    }
    aries::pb::DADataService_Stub stub(remote);
    (stub.*f)(cntl, req, res, NULL);
}

void LocalRpcWrapper::put(baidu::rpc::Controller* controller,
        const ::aries::pb::BlobPutRequest* request,
        ::aries::pb::BlobPutResponse* response,
        ::google::protobuf::Closure* done) {
    local_first_call(&aries::pb::DADataService_Stub::put, _local_channel, _channel,
            controller, request, response);
    if (done != NULL) {
        done->Run();
    }
}

void LocalRpcWrapper::get(baidu::rpc::Controller* controller,
        const ::aries::pb::BlobGetRequest* request,
        ::aries::pb::BlobGetResponse* response,
        ::google::protobuf::Closure* done) {
    local_first_call(&aries::pb::DADataService_Stub::get, _local_channel, _channel,
            controller, request, response);
    if (done != NULL) {
        done->Run();
    }
}

void LocalRpcWrapper::remove(baidu::rpc::Controller* controller,
        const ::aries::pb::BlobRemoveRequest* request,
        ::aries::pb::AckResponse* response,
        ::google::protobuf::Closure* done) {
    local_first_call(&aries::pb::DADataService_Stub::remove, _local_channel, _channel,
            controller, request, response);
    if (done != NULL) {
        done->Run();
    }
}

int LocalRpcWrapper::init_local(int local_port) {
    _local_channel = new baidu::rpc::Channel;
    baidu::rpc::ChannelOptions options = _channel->options();
    options.max_retry = 0;
    if (_local_channel->Init("127.0.0.1", local_port, &options) != 0) {
        LOG(WARNING) << "init local channel failed on port " << local_port;
        return AE_FAIL;
    }
    LOG(WARNING) << "init local channel succeeded on port " << local_port;
    return 0;
}

int ProxyWrapper::init(const ProxyConf& conf, bool use_shared_proxy) {
    _conf = conf; 
    if (_conf.proxy_name.empty()) {
        _conf.proxy_name = "dataproxy";
    }
    if (_conf.proxy_name.find("dataproxy") != 0) {
        ARIES_FATAL_LOG() << "proxy_name configure should start with dataproxy";
        return AE_FAIL;
    }
    _conf.proxy_version = __ARIES_VERSION_ID__;

    if (use_shared_proxy) {
        LOG(WARNING) << "proxy:" << _conf.proxy_name << " try to use shared proxy";
        _proxy = g_proxy_manager.get_proxy(_conf);
        if (!_proxy) {
            return AE_FAIL;
        }
    } else {
        _proxy = std::make_shared<Proxy>();
        if (_proxy->init(_conf) != 0) {
            ARIES_FATAL_LOG() << "init proxy failed";
            return AE_FAIL;
        }
        LOG(WARNING) << "proxy:" << _conf.proxy_name << " init succeeded";
    }
    return 0;
}

Errno ProxyWrapper::get(RequestOption& request_option, uint128_t bid, const BlobRange* range,
        std::string* data, BlobMeta* blob_meta, pb::BlobRequestErrorInfo* request_error_info) {
    uint64_t log_id = request_option.log_id;
    uint64_t trace_id = log_id;
    aries::common::CostTracker cost_tracker;

    GetBlobInfo blob_info(bid);
    blob_info.log_id = log_id;
    blob_info.cost_tracker = &cost_tracker;
    blob_info.timeout_ms = request_option.timeout_ms;
    blob_info.need_data = (data != NULL);
    blob_info.need_meta = (blob_meta != NULL);
    blob_info.priority = request_option.priority;
    blob_info.network_qos = request_option.network_qos;
    blob_info.api_data = data;  // result buffer
    blob_info.request_error_info = request_error_info;
    if (range != NULL) {
        blob_info.need_range = true;
        blob_info.offset = range->offset;
        blob_info.len = range->len;
        blob_info.blob_len_hint = range->blob_len;
        blob_info.fast_range_get = request_option.fast_range_get;
    }

    if (data == NULL && blob_meta == NULL && range == NULL) {
        blob_info.check_exist = true;
    }

    ARIES_API_LOG(DEBUG) << std::dec << "get blob, bid:" << bid2str(bid) << " need_data:" << (data != NULL)
        << " need_meta:" << (blob_meta != NULL) << " fast_range_get:" << request_option.fast_range_get
        << " range len:" <<  blob_info.len
        << " network_qos:" << request_option.network_qos << " timeout_ms:" << request_option.timeout_ms;


    _proxy->get(&blob_info);
    if (blob_info.ret != AE_OK) {
        ARIES_API_LOG(WARNING) << std::dec << "fail to get blob, bid:" << bid2str(bid)
            << " error:" << blob_info.ret << " (" << blob_info.msg << ")"
            << " cost_track_us:" << blob_info.cost_tracker->to_string();
        return (aries::Errno)blob_info.ret;
    }

    if (blob_meta != NULL) {
        blob_meta->key = blob_info.key;
        blob_meta->meta = blob_info.meta;
        blob_meta->create_time = blob_info.create_time_us;
        blob_meta->blob_crc = (blob_info.compress_type == 0 ? blob_info.blob_crc : blob_info.origin_blob_crc);
        blob_meta->blob_len = (blob_info.compress_type == 0 ? blob_info.blob_len : blob_info.origin_blob_len);
        blob_meta->compress_ratio = blob_info.compress_ratio;
        blob_meta->compress_type = (aries::CompressType)blob_info.compress_type;
        blob_meta->compress_level = blob_info.compress_level;
    }
    if (blob_info.api_data != NULL) {
        if (blob_info.need_range) {
            if (!blob_info.reuse_buffer) {
                memmove(&(*blob_info.api_data)[0], &(*blob_info.api_data)[blob_info.offset], blob_info.len);
                blob_info.api_data->resize(blob_info.len);
            }
        } else {
            blob_info.api_data->resize(blob_info.compress_type == 0 ? blob_info.blob_len : blob_info.origin_blob_len);
        }
    }
    if (blob_info.cost_tracker->total_us() >= FLAGS_log_threshold_for_get_latency_us) {
        ARIES_API_LOG(NOTICE) << std::dec << "get blob succeeded, bid:" << bid2str(blob_info.blob_id)
            << " size:" << blob_info.blob_len << " data/meta:" << blob_info.need_data << "," << blob_info.need_meta
            << " offset:" << blob_info.offset << " range_len:" << blob_info.len
            << " cost_track_us:" << blob_info.cost_tracker->to_string();
    }
    return AE_OK;
}

Errno ProxyWrapper::put(RequestOption &request_option, const std::string& space_name, const std::string& key,
        const std::string& meta, const std::string& data, uint128_t* bid, BlobMeta* blob_meta,
        pb::BlobRequestErrorInfo* request_error_info) {
    uint64_t log_id = request_option.log_id;
    uint64_t trace_id = log_id;
    aries::common::CostTracker cost_tracker;

    ARIES_RPC_VLOG(TRACE) << std::dec << "put blob, space:" << space_name 
        << " network_qos:" << request_option.network_qos << noflush;

    if (!request_option.small_blob_space_name.empty()) {
        ARIES_RPC_VLOG(TRACE) << " small_blob_space:" << request_option.small_blob_space_name
            << " small_blob_extreme_length:" << request_option.small_blob_extreme_length << noflush;
    }
    ARIES_RPC_VLOG(TRACE) << " key:" << key << " timeout_ms:" << request_option.timeout_ms;
    auto blob_info = std::make_shared<PutBlobInfo>();
    blob_info->log_id = log_id;
    blob_info->cost_tracker = &cost_tracker;
    blob_info->timeout_ms = request_option.timeout_ms;
    blob_info->cache_pin_ttl_secs = request_option.cache_pin_ttl_secs;
    blob_info->create_time_us = base::gettimeofday_us();
    blob_info->space_name = space_name;
    blob_info->priority = request_option.priority;
    blob_info->network_qos = request_option.network_qos;
    blob_info->need_meta = (blob_meta != NULL);
    blob_info->compress_type = request_option.compress_type;
    blob_info->min_compress_ratio = request_option.min_compress_ratio;
    blob_info->compress_level = request_option.compress_level;
    blob_info->origin_blob_len = data.size();
    blob_info->origin_blob_crc = common::Crc32CheckSum::compute(data.c_str(), data.size(), 0);
    blob_info->blob_len = blob_info->origin_blob_len;
    blob_info->blob_crc = blob_info->origin_blob_crc;
    blob_info->api_data = &data;
    blob_info->request_error_info = request_error_info;
    if (!request_option.small_blob_space_name.empty()) {
        blob_info->small_blob_space_name = request_option.small_blob_space_name;
        blob_info->small_blob_extreme_length = request_option.small_blob_extreme_length;
    }
    if (!key.empty() || !meta.empty()) {
        uint32_t meta_crc = 0;
        if (!key.empty()) {
            blob_info->key = key;
            meta_crc = base::crc32c::Extend(meta_crc, key.data(), key.size());
        }
        if (!meta.empty()) {
            blob_info->meta = meta;
            meta_crc = base::crc32c::Extend(meta_crc, meta.data(), meta.size());
        }
        blob_info->meta_crc = meta_crc;
    }

    _proxy->put(blob_info);
    if (blob_info->ret != AE_OK) {
        ARIES_API_LOG(WARNING) << std::dec << "fail to put blob,"
            << " error:" << blob_info->ret << " (" << blob_info->msg << ")"
            << " cost_track_us:" << blob_info->cost_tracker->to_string();
        return (aries::Errno)blob_info->ret;
    }
    *bid = blob_info->blob_id;
    if (blob_meta != NULL) {
        blob_meta->key = blob_info->key;
        blob_meta->meta = blob_info->meta;
        blob_meta->create_time = blob_info->create_time_us;
        blob_meta->blob_crc = blob_info->origin_blob_crc;
        blob_meta->blob_len = blob_info->origin_blob_len;
        blob_meta->compress_type = (aries::CompressType)blob_info->compress_type;
        blob_meta->compress_level = blob_info->compress_level;
        blob_meta->compress_ratio = blob_info->compress_ratio;
    }
    size_t compressed_size = data.size();
    if (blob_meta != NULL && blob_meta->compress_type != 0) {
        compressed_size = blob_meta->blob_len / blob_meta->compress_ratio;
    }

    if (blob_info->cost_tracker->total_us() >= FLAGS_log_threshold_for_put_latency_us) {
        ARIES_API_LOG(NOTICE) << std::dec << "put blob succeeded, bid:" << bid2str(blob_info->blob_id)
            << " space:" << blob_info->space_name << " data_size:" << data.size()
            << " compressed_size:" << compressed_size << " meta_size:" << meta.size()
            << " cost_track_us:" << blob_info->cost_tracker->to_string();
    }
    return AE_OK;
}

Errno ProxyWrapper::remove(RequestOption& request_option, uint128_t bid,
        pb::BlobRequestErrorInfo* request_error_info) {
    uint64_t log_id = request_option.log_id;
    uint64_t trace_id = log_id;
    aries::common::CostTracker cost_tracker;

    ARIES_API_LOG(NOTICE) << std::dec << "remove blob, bid:" << bid2str(bid)
        << " network_qos:" << request_option.network_qos
        << " timeout_ms:" << request_option.timeout_ms;

    RemoveBlobInfo blob_info(bid);
    blob_info.log_id = log_id;
    blob_info.cost_tracker = &cost_tracker;
    blob_info.timeout_ms = request_option.timeout_ms;
    blob_info.priority = request_option.priority;
    blob_info.network_qos = request_option.network_qos;
    blob_info.request_error_info = request_error_info;

    _proxy->remove(&blob_info);
    if (blob_info.ret != AE_OK) {
        ARIES_API_LOG(WARNING) << std::dec << "fail to remove blob, bid:" << bid2str(bid)
            << " error:" << blob_info.ret << " (" << blob_info.msg << ")"
            << " cost_track_us:" << blob_info.cost_tracker->to_string();
        return (aries::Errno)blob_info.ret;
    }

    if (blob_info.cost_tracker->total_us() >= FLAGS_log_threshold_for_remove_latency_us) {
        ARIES_API_LOG(NOTICE) << std::dec << "remove blob succeeded, bid:" << bid2str(blob_info.blob_id)
            << " cost_track_us:" << blob_info.cost_tracker->to_string();
    }
    return AE_OK;
}

Errno ProxyWrapper::allocate_volume(RequestOption& request_option,
        const std::string& space_name, uint64_t volume_id_hint, uint64_t* volume_id) {
    return _proxy->allocate_volume(request_option, space_name, volume_id_hint, volume_id);
}

Errno ProxyWrapper::retrieve_blobs(RequestOption& request_option, const RetrieveOption& option) {
    return _proxy->retrieve_blobs(request_option, option);
}

Errno ProxyWrapper::get_retrieve_task_status(RequestOption& request_option, 
        const std::string& task_id, RetrieveTaskStatus* status) {
    return _proxy->get_retrieve_task_status(request_option, task_id, status);
}

Errno ProxyWrapper::cancel_retrieve_task(RequestOption& request_option, const std::string& task_id) {
    return _proxy->cancel_retrieve_task(request_option, task_id);
}

Errno ProxyWrapper::append_blob(RequestOption& request_option,
                                uint64_t volume_id,
                                const std::string& key,
                                const std::string& meta,
                                const std::string& data,
                                uint128_t *bid) {
    uint64_t log_id = request_option.log_id;
    uint64_t trace_id = log_id;
    aries::common::CostTracker cost_tracker;

    ARIES_API_LOG(NOTICE) << std::dec << "append blob, volume_id:" << volume_id
        << " network_qos:" << request_option.network_qos << noflush;

    LOG(NOTICE) << " key:" << key << " timeout_ms:" << request_option.timeout_ms;

    auto blob_info = std::make_shared<PutBlobInfo>();
    blob_info->log_id = log_id;
    blob_info->cost_tracker = &cost_tracker;
    blob_info->archive_volume_id = volume_id;
    blob_info->timeout_ms = request_option.timeout_ms;
    blob_info->cache_pin_ttl_secs = request_option.cache_pin_ttl_secs;
    blob_info->create_time_us = base::gettimeofday_us();
    blob_info->priority = request_option.priority;
    blob_info->network_qos = request_option.network_qos;
    blob_info->need_meta = false;
    blob_info->compress_type = request_option.compress_type;
    blob_info->min_compress_ratio = request_option.min_compress_ratio;
    blob_info->compress_level = request_option.compress_level;
    blob_info->origin_blob_len = data.size();
    blob_info->origin_blob_crc = common::Crc32CheckSum::compute(data.c_str(), data.size(), 0);
    blob_info->blob_len = blob_info->origin_blob_len;
    blob_info->blob_crc = blob_info->origin_blob_crc;
    blob_info->api_data = &data;
    if (!key.empty() || !meta.empty()) {
        uint32_t meta_crc = 0;
        if (!key.empty()) {
            blob_info->key = key;
            meta_crc = base::crc32c::Extend(meta_crc, key.data(), key.size());
        }
        if (!meta.empty()) {
            blob_info->meta = meta;
            meta_crc = base::crc32c::Extend(meta_crc, meta.data(), meta.size());
        }
        blob_info->meta_crc = meta_crc;
    }

    _proxy->put(blob_info);
    if (blob_info->ret != AE_OK) {
        ARIES_API_LOG(WARNING) << std::dec << "fail to append blob,"
            << " error:" << blob_info->ret << " (" << blob_info->msg << ")"
            << " cost_track_us:" << blob_info->cost_tracker->to_string();
        return (aries::Errno)blob_info->ret;
    }

    *bid = blob_info->blob_id;
    ARIES_API_LOG(NOTICE) << std::dec << "append blob succeeded, bid:" << bid2str(blob_info->blob_id)
        << " space:" << blob_info->space_name << " data_size:" << data.size() << " meta_size:" << meta.size()
        << " cost_track_us:" << blob_info->cost_tracker->to_string();
    return AE_OK;
}

Errno ProxyWrapper::release_volume(RequestOption& request_option, uint64_t volume_id) {
    return _proxy->release_volume(request_option, volume_id);
}

Errno ProxyWrapper::get_volume_size(uint64_t volume_id, int64_t* size) {
    return _proxy->get_volume_size(volume_id, size);
}

Errno ProxyWrapper::seal_volume(RequestOption& request_option, uint64_t volume_id) {
    return _proxy->seal_volume(request_option, volume_id);
}

Errno ProxyWrapper::set_volume_ttl(RequestOption& request_option,
    uint64_t volume_id, uint64_t volume_ttl) {
    return _proxy->set_volume_ttl(request_option, volume_id, volume_ttl);
}

Errno ProxyWrapper::set_blob_ttl(RequestOption& request_option, uint128_t bid, uint32_t blob_ttl) {
    uint64_t log_id = request_option.log_id;
    uint64_t trace_id = log_id;
    aries::common::CostTracker cost_tracker;

    ARIES_API_LOG(NOTICE) << std::dec << "set blob ttl, bid:" << bid2str(bid) 
        << " ttl:" << blob_ttl
        << " network_qos:" << request_option.network_qos
        << " timeout_ms:" << request_option.timeout_ms;

    UpdateTtlInfo blob_info(bid);
    blob_info.log_id = log_id;
    blob_info.cost_tracker = &cost_tracker;
    blob_info.timeout_ms = request_option.timeout_ms;
    blob_info.priority = request_option.priority;
    blob_info.network_qos = request_option.network_qos;
    blob_info.blob_ttl_timestamp = blob_ttl;
    _proxy->set_blob_ttl(&blob_info);

    if (blob_info.ret != AE_OK) {
        ARIES_API_LOG(WARNING) << std::dec << "fail to update blob ttl, bid:" << bid2str(bid) << " msg:"
            << blob_info.ret << " (" << blob_info.msg << ") cost_track_us:" << blob_info.cost_tracker->to_string();
        return (aries::Errno)blob_info.ret;
    }

    ARIES_API_LOG(NOTICE) << std::dec << "update blob ttl succeeded, bid:" << bid2str(blob_info.blob_id)
        << " cost_track_us:" << blob_info.cost_tracker->to_string();
    return AE_OK;
}

Errno ProxyWrapper::show_space(RequestOption& request_option, const std::string& space_name,
        aries::pb::SpaceInfo* space_info) {
    return _proxy->show_space(request_option, space_name, space_info);
}

Errno ProxyWrapper::list_disk_usage(RequestOption& request_option, bool show_az, bool show_idc,
        bool show_rack, bool show_group, bool show_disk_type,
        aries::pb::ClusterDiskUsageInfo* usage_info) {
    return _proxy->list_disk_usage(request_option, show_az, show_idc,
        show_rack, show_group, show_disk_type, usage_info);
}

Errno ProxyWrapper::list_disk_io_stats(RequestOption& request_option, bool show_az, bool show_idc,
        bool show_rack, bool show_group, bool show_disk_type, bool show_user,
        aries::pb::ClusterDiskIoStatsInfo* io_stats_info) {
    return _proxy->list_disk_io_stats(request_option, show_az, show_idc,
        show_rack, show_group, show_disk_type, show_user, io_stats_info);
}

Errno ProxyWrapper::show_space_usage(RequestOption& request_option, const std::string& space_name,
        aries::pb::SpaceUsageInfo* usage_info) {
    return _proxy->show_space_usage(request_option, space_name, usage_info);
}

Errno ProxyWrapper::create_stream(CreateStreamOptions& opt, uint128_t* stream_id) {
    return _proxy->create_stream(opt, stream_id);
}

Errno ProxyWrapper::unlink_stream(RequestOption& request_option, uint128_t stream_id) {
    return _proxy->unlink_stream(request_option, stream_id);
}

Errno ProxyWrapper::truncate_stream(RequestOption& request_option, uint128_t stream_id, 
        uint64_t tail_offset, std::optional<uint64_t> stream_version) {
    return _proxy->truncate_stream(request_option, stream_id, 
        tail_offset, stream_version);
}

Errno ProxyWrapper::head_truncate_stream(RequestOption& request_option, uint128_t stream_id,
        uint64_t head_offset, std::optional<uint64_t> stream_version) {
    return _proxy->head_truncate_stream(request_option, stream_id, 
        head_offset, stream_version);
}

Errno ProxyWrapper::stat_stream(RequestOption& request_option, uint128_t stream_id, StreamStatus* st) {
    return _proxy->stat_stream(request_option, stream_id, st);
}

Errno ProxyWrapper::stat_streams(RequestOption& request_option, const std::vector<uint128_t>& stream_ids,
        std::vector<StreamStatus>* stream_status) {
    return _proxy->stat_streams(request_option, stream_ids, stream_status);
}

Errno ProxyWrapper::show_stream_space_statistics(RequestOption& request_option,
        const std::string& space_name, aries::pb::StreamSpaceStatistics* statistic) {
    return _proxy->show_stream_space_statistics(request_option, space_name, statistic);
}

Errno ProxyWrapper::link_stream(RequestOption& request_option, uint128_t stream_id) {
    return _proxy->link_stream(request_option, stream_id);
}

Errno ProxyWrapper::utime_stream(RequestOption& request_option, uint128_t stream_id,
        std::optional<uint64_t> atime, std::optional<uint64_t> mtime) {
    return _proxy->utime_stream(request_option, stream_id, atime, mtime);
}

Errno ProxyWrapper::set_stream_cache_policy(StreamCacheOptions& opt) {
    return _proxy->set_stream_cache_policy(opt);
}

Errno ProxyWrapper::open_stream_writer(StreamWriterOptions& opt, std::shared_ptr<StreamWriter>* writer) {
    return _proxy->open_stream_writer(opt, writer);
}

Errno ProxyWrapper::force_open_stream_writer(StreamWriterOptions& opt, std::shared_ptr<StreamWriter>* writer) {
    return _proxy->force_open_stream_writer(opt, writer);
}

Errno ProxyWrapper::open_stream_reader(StreamReaderOptions& opt, std::shared_ptr<StreamReader>* reader) {
    return _proxy->open_stream_reader(opt, reader);
}

class AriesDiskTypeTransform : public AriesInfoTransform {
public:
    void transform(aries::pb::SpaceUsageInfo* usage_info) override {
        // nothing need to do
    }

    void transform(aries::pb::SpaceInfo* space_info) override {
        const aries::pb::SpaceInfo copy(*space_info);

        // disk_type
        if (copy.has_disk_type()) {
            space_info->clear_disk_type();

            std::string disk_type_before = copy.disk_type();
            std::vector<std::string> disk_type_list;
            Tokenize(disk_type_before, ",", &disk_type_list);
            std::set<std::string> disk_type_set(disk_type_list.begin(), disk_type_list.end());
            if (disk_type_set.erase("ZONE_SSD") != 0) {
                disk_type_set.insert("SSD");
            }
            std::string disk_type_after = set2string(disk_type_set, ",");
            space_info->set_disk_type(disk_type_after);
        }

        // disk_vlet_type
        if (copy.disk_vlet_type_size() != 0) {
            space_info->clear_disk_vlet_type();

            std::map<std::string, std::set<VletType>> disk_vlet_type;
            for (int i = 0; i < copy.disk_vlet_type_size(); ++i) {
                const std::string& disk_type = copy.disk_vlet_type(i).disk_type();
                VletType vlet_type = copy.disk_vlet_type(i).vlet_type();

                disk_vlet_type[is_ssd(disk_type) ? "SSD" : disk_type].insert(vlet_type);
            }
            for (const auto& [disk_type, vlet_types] : disk_vlet_type) {
                for (const VletType& vlet_type : vlet_types) {
                    auto* vt = space_info->add_disk_vlet_type();
                    vt->set_disk_type(disk_type);
                    vt->set_vlet_type(vlet_type);
                }
            }
        }

        // disk_type_place_policy
        if (copy.disk_type_place_policy_size() != 0) {
            space_info->clear_disk_type_place_policy();

            for (int i = 0; i < copy.disk_type_place_policy_size(); ++i) {
                const std::string& disk_type = copy.disk_type_place_policy(i).disk_type();
                auto* tpp = space_info->add_disk_type_place_policy();
                tpp->set_shard_index(copy.disk_type_place_policy(i).shard_index());
                tpp->set_disk_type(is_ssd(disk_type) ? "SSD" : disk_type);
            }
        }
    }

    void transform(aries::pb::ClusterDiskUsageInfo* usage_info) override {
        const aries::pb::ClusterDiskUsageInfo copy(*usage_info);

        // disk_type_disk_usages
        if (copy.disk_type_disk_usages_size() != 0) {
            usage_info->clear_disk_type_disk_usages();

            size_t ssd_num{0};
            aries::pb::DiskUsageInfo ssd_usage_info;
            for (const auto& du : copy.disk_type_disk_usages()) {
                if (is_ssd(du.disk_type_name())) {
                    merge_disk_usage(du.disk_usage(), &ssd_usage_info);
                    ssd_num += du.disk_num();
                } else {
                    usage_info->add_disk_type_disk_usages()->CopyFrom(du);
                }
            }

            if (ssd_num != 0) {
                aries::pb::ClusterDiskUsageInfo_DiskTypeDiskUsage ssd_usage;
                ssd_usage.set_disk_type_name("SSD");
                ssd_usage.set_disk_num(ssd_num);
                ssd_usage.mutable_disk_usage()->CopyFrom(ssd_usage_info);

                usage_info->add_disk_type_disk_usages()->CopyFrom(ssd_usage);
            }
        }
    }

    void transform(aries::pb::ClusterDiskIoStatsInfo* io_stats_info) override {
        aries::pb::ClusterDiskIoStatsInfo copy(*io_stats_info);
        if (copy.disk_type_io_stats_size() != 0) {
            io_stats_info->clear_disk_type_io_stats();

            size_t ssd_num{0};
            aries::pb::ClusterDiskIoStatsInfo_IoStatsInfo ssd_io_stats;
            for (const auto& di : copy.disk_type_io_stats()) {
                if (is_ssd(di.disk_type_name())) {
                    merge_disk_io_stats(di.disk_num(), di.io_stats(), ssd_num, &ssd_io_stats);
                    ssd_num += di.disk_num();
                } else {
                    io_stats_info->add_disk_type_io_stats()->CopyFrom(di);
                }
            }

            if (ssd_num != 0) {
                aries::pb::ClusterDiskIoStatsInfo_DiskTypeDiskIoStats ssd_stats;
                ssd_stats.set_disk_type_name("SSD");
                ssd_stats.set_disk_num(ssd_num);
                ssd_stats.mutable_io_stats()->CopyFrom(ssd_io_stats);

                io_stats_info->add_disk_type_io_stats()->CopyFrom(ssd_stats);
            }
        }
    }

private:
    bool is_ssd(const std::string& type) {
        if (type == "SSD" || type == "ZONE_SSD") {
            return true;
        } else {
            return false;
        }
    }

    std::string set2string(const std::set<std::string>& name_set, const std::string& delimiter) {
        std::string name_str;
        for (const auto& name : name_set) {
            if (!name.empty()) {
                name_str += name + delimiter;
            }
        }
        if (!name_str.empty()) {
            name_str = name_str.substr(0, name_str.size() - delimiter.size());
        }
        return name_str;
    }

    void merge_disk_usage(const aries::pb::DiskUsageInfo& src, aries::pb::DiskUsageInfo* dest) {
        const aries::pb::DiskUsageInfo copy(*dest);
        if (src.disk_total_size() != 0) {
            dest->set_disk_total_size(copy.disk_total_size() + src.disk_total_size());
            dest->set_disk_free_size(copy.disk_free_size() + src.disk_free_size());
            dest->set_disk_used_percent(double(dest->disk_total_size() - dest->disk_free_size()) / double(dest->disk_total_size()));
        }

        if (src.aries_capacity() != 0) {
            dest->set_aries_capacity(copy.aries_capacity() + src.aries_capacity());
            dest->set_aries_free_size(copy.aries_free_size() + src.aries_free_size());
            dest->set_aries_used_size(copy.aries_used_size() + src.aries_used_size());
            dest->set_aries_used_percent(double(dest->aries_used_size()) / double(dest->aries_capacity()));
        }
    }

    double calc_avg(double v1, size_t w1, double v2, size_t w2) {
        if (w1 + w2 != 0) {
            return double(v1 * w1 + v2 * w2) / double(w1 + w2);
        } else {
            return 0;
        }
    }

    void merge_disk_io_stats(size_t src_size, const aries::pb::ClusterDiskIoStatsInfo_IoStatsInfo& src, size_t dest_size, aries::pb::ClusterDiskIoStatsInfo_IoStatsInfo* dest) {
        const aries::pb::ClusterDiskIoStatsInfo_IoStatsInfo copy(*dest);
        dest->set_avg_ioutil(calc_avg(copy.avg_ioutil(), dest_size, src.avg_ioutil(), src_size));
        dest->set_avg_read_iops(calc_avg(copy.avg_read_iops(), dest_size, src.avg_read_iops(), src_size));
        dest->set_avg_write_iops(calc_avg(copy.avg_write_iops(), dest_size, src.avg_write_iops(), src_size));
        dest->set_avg_read_throughput(calc_avg(copy.avg_read_throughput(), dest_size, src.avg_read_throughput(), src_size));
        dest->set_avg_write_throughput(calc_avg(copy.avg_write_throughput(), dest_size, src.avg_write_throughput(), src_size));
    }
};

AriesClient *AriesClient::new_aries_client(const ConfInfo& conf_info) {
    auto instance = new AriesClientImpl();
    int ret = instance->init(conf_info);
    if (ret != 0) {
        delete instance;
        instance = NULL;
    }
    return instance;
}

AriesStreamClient* AriesStreamClient::new_aries_client(const ConfInfo& conf_info) {
    auto instance = new AriesStreamClientImpl();
    int ret = instance->init(conf_info);
    if (ret != 0) {
        delete instance;
        instance = NULL;
    }
    return instance;
}

void AriesClientImpl::init_rpc_option(RequestOption &request_option, baidu::rpc::Controller &ctl) {
    request_option.timeout_ms = request_option.timeout_ms < 0 ? _conf_info.timeout_ms : request_option.timeout_ms;
    request_option.retry_timeout_ms = request_option.retry_timeout_ms < 0 ? _conf_info.retry_timeout_ms
        : request_option.retry_timeout_ms;

    ctl.set_log_id(request_option.log_id);
    ctl.set_timeout_ms(request_option.timeout_ms);
    if (request_option.retry_timeout_ms > 0 && request_option.retry_timeout_ms < request_option.timeout_ms) {
        ctl.set_backup_request_ms(request_option.retry_timeout_ms);
    }
}

Errno AriesClientImpl::init(const ConfInfo &conf_info) {
    _conf_info = conf_info;
    if (!conf_info.use_proxy_lib) {
        baidu::rpc::ChannelOptions options;
        options.timeout_ms = conf_info.timeout_ms;
        options.max_retry = conf_info.max_retry;
        options.retry_policy = &g_aries_retry_policy;
        if (_channel.Init(_conf_info.server.c_str(), _conf_info.load_balancer.c_str(),
                    &options) != 0) {
            LOG(WARNING) << "fail to init aries due to init dataagent channel failed";
            return AE_FAIL;
        }
        if (conf_info.local_port >= 0) {
            if (_stub.init_local(conf_info.local_port) != 0) {
                LOG(WARNING) << "fail to init aries due to init local port failed";
                return AE_FAIL;
            }
        }
    } else {
        _proxy_wrapper = new ProxyWrapper();
        assert(conf_info.proxy_conf.token == conf_info.token);
        if (_proxy_wrapper->init(conf_info.proxy_conf, conf_info.use_shared_proxy) != 0) {
            delete _proxy_wrapper;
            _proxy_wrapper = NULL;
            LOG(WARNING) << "fail to init aries due to init blob proxy failed";
            return AE_FAIL;
        }
    }

    if (!conf_info.is_aries_internal_user) {
        _info_transform = std::make_unique<AriesDiskTypeTransform>();
    }

    LOG(WARNING) << "init aries-api succeeded, version info:\n"
        << "version  : " << __ARIES_VERSION_ID__ << "\n"
        << "platform : " << __ARIES_BUILDHOST__ << "\n"
        << "repo     : " << __ARIES_REPO_URL__ << "\n";
    return AE_OK;
}

Errno AriesClientImpl::put(RequestOption &request_option,
            const std::string& space_name,
            const std::string& key,
            const std::string& meta,
            const std::string& data,
            uint128_t* bid,
            BlobMeta* blob_meta,
            pb::BlobRequestErrorInfo* request_error_info) {
    uint64_t &trace_id = request_option.log_id;
    baidu::rpc::Controller ctl;
    init_rpc_option(request_option, ctl);
    const std::string& small_blob_space_name = request_option.small_blob_space_name;
    const uint32_t small_blob_extreme_length = request_option.small_blob_extreme_length;
    std::string error_msg;
    if (space_name.empty()) {
        error_msg = "empty space name!";
    } else if (small_blob_space_name.empty() && small_blob_extreme_length != 0) {
        error_msg = "small_blob_extreme_length must be zero when small_blob_space_name is empty";
    } else if (small_blob_extreme_length > MAX_BLOB_SIZE) {
        error_msg = "small_blob_extreme_length must equal to or less than 64MB";
    } else if (data.size() > MAX_BLOB_SIZE) {
        error_msg = "blob size must equal to or less than 64MB";
    } else if (bid == NULL) {
        error_msg = "bid is null pointer";
    } else if (key.size() + meta.size() > MAX_KEY_META_SIZE) {
        error_msg = "key meta size too large";
    } else if (ctl.timeout_ms() <= 0) {
        error_msg = "timeout_ms must greater than zero";
    }
    if (!error_msg.empty()) {
        ARIES_API_LOG(WARNING) << std::dec << "fail to put blob due to invalid argument, cause: " << error_msg;
        return AE_INVALID_ARGUMENT;
    }

    if (_proxy_wrapper) {
        return _proxy_wrapper->put(request_option, space_name, key, meta, data, bid, blob_meta, request_error_info);
    }

    // construct request
    aries::pb::BlobPutRequest req;
    aries::pb::BlobPutResponse res;
    req.set_space_name(space_name);
    req.set_token(_conf_info.token);
    req.set_small_blob_space_name(small_blob_space_name);
    req.set_small_blob_extreme_length(small_blob_extreme_length);
    req.set_data_crc(::base::crc32c::Value(data.data(), data.size()));
    req.mutable_rpc_info()->set_timeout_ms(ctl.timeout_ms());
    req.mutable_rpc_info()->set_req_timestamp_ms(base::gettimeofday_ms());
    req.mutable_rpc_info()->set_priority(request_option.priority);
    req.mutable_rpc_info()->set_network_qos(request_option.network_qos);

    req.set_compress_type(request_option.compress_type);
    req.set_min_compress_ratio(request_option.min_compress_ratio);
    req.set_compress_level(request_option.compress_level);

    if (request_option.cache_pin_ttl_secs >= 0) {
        req.set_cache_pin_ttl_secs(request_option.cache_pin_ttl_secs);
    }
    if (!key.empty() || !meta.empty()) {
        uint32_t key_meta_crc = 0;
        if (!key.empty()) {
            req.set_key(key);
            key_meta_crc = base::crc32c::Extend(key_meta_crc, key.data(), key.size());
        }
        if (!meta.empty()) {
            req.set_meta(meta);
            key_meta_crc = base::crc32c::Extend(key_meta_crc, meta.data(), meta.size());
        }
        req.set_key_meta_crc(key_meta_crc);
    }
    if (blob_meta != nullptr) {
        req.set_need_meta(true);
    }

    ctl.request_attachment().append(data.data(), data.size());
    int64_t start_time = base::cpuwide_time_us();
    _stub.put(&ctl, &req, &res, NULL);
    int64_t cost = base::cpuwide_time_us() - start_time;

    if (ctl.Failed()) {
        ARIES_API_LOG(WARNING) << std::dec << "fail to put blob due to rpc failure,"
            << " remote:" << ctl.remote_side() << " error:" << ctl.ErrorCode() << " (" << ctl.ErrorText() << ")"
            << " time_us:" << cost;
        if (g_aries_retry_policy.DoRetry(&ctl)) {
            return AE_AGAIN;
        }
        return AE_FAIL;
    }
    Errno rc = (Errno) res.status().code();
    if (rc != AE_OK) {
        ARIES_API_LOG(WARNING) << std::dec << "fail to put blob,"
            << " bid:" << bid2str(*bid) << " remote:" << ctl.remote_side()
            << " error:" << rc << " (" << res.status().msg() << ") time_us:" << cost;
        return rc;
    }
    *bid = ((uint128_t) res.vid() << 64) | res.vbid();
    size_t compressed_size = data.size();

    if (res.has_blob_meta() && blob_meta != NULL) {
        const aries::pb::BlobMeta &meta = res.blob_meta();
        blob_meta->key = meta.key();
        blob_meta->meta = meta.meta();
        blob_meta->create_time = meta.timestamp();
        blob_meta->blob_crc = meta.blob_crc();
        blob_meta->blob_len = meta.blob_len();
        blob_meta->compress_type = (aries::CompressType)meta.compress_type();
        blob_meta->compress_ratio = meta.compress_ratio();
        blob_meta->compress_level = meta.compress_level();
    }
    if (blob_meta != NULL && blob_meta->compress_type != 0) {
        compressed_size = blob_meta->blob_len / blob_meta->compress_ratio;
    }
    ARIES_API_LOG(NOTICE) << std::dec << "put blob succeeded, remote:" << ctl.remote_side()
        << " space:" << space_name << " data_size:" << data.size() 
        << " compressed_size:" << compressed_size << " meta_size:" << meta.size() 
        << " bid:" << bid2str(*bid) << " time_us:" << cost;

    return AE_OK;
}

Errno AriesClientImpl::get(RequestOption &request_option, uint128_t bid, const BlobRange *range,
        std::string* data, BlobMeta *blob_meta,
        pb::BlobRequestErrorInfo* request_error_info) {
    uint64_t &trace_id = request_option.log_id;
    baidu::rpc::Controller ctl;
    init_rpc_option(request_option, ctl);
    std::string error_msg;
    if (ctl.timeout_ms() <= 0) {
        error_msg = "timeout_ms must greater than zero";
    }
    if (request_option.fast_range_get == true && blob_meta != NULL) {
        error_msg = "fast_range_get cannot set with blob_meta at the same time";
    }
    if (!error_msg.empty()) {
        ARIES_API_LOG(WARNING) << std::dec << "fail to get blob due to invalid argument, cause: " << error_msg;
        return AE_INVALID_ARGUMENT;
    }
    if (data != NULL) {
        (*data).clear();
    }

    if (_proxy_wrapper) {
        return _proxy_wrapper->get(request_option, bid, range, data, blob_meta, request_error_info);
    }

    aries::pb::BlobGetRequest req;
    aries::pb::BlobGetResponse res;
    req.set_token(_conf_info.token);
    if (data != NULL) {
        req.set_need_data(true);
    }
    if (blob_meta != NULL) {
        req.set_need_meta(true);
    }

    uint64_t vid = bid >> 64;
    uint64_t vbid = bid & ~0L;
    req.set_vid(vid);
    req.set_vbid(vbid);
    req.mutable_rpc_info()->set_timeout_ms(ctl.timeout_ms());
    req.mutable_rpc_info()->set_req_timestamp_ms(base::gettimeofday_ms());
    req.mutable_rpc_info()->set_priority(request_option.priority);
    req.mutable_rpc_info()->set_network_qos(request_option.network_qos);
    if (range != NULL) {
        req.mutable_range()->set_offset(range->offset);
        req.mutable_range()->set_len(range->len);
        req.set_blob_len_hint(range->blob_len);
        req.set_fast_range_get(request_option.fast_range_get);
    }

    int64_t start_time = base::cpuwide_time_us();
    _stub.get(&ctl, &req, &res, NULL);
    int64_t cost = base::cpuwide_time_us() - start_time;

    if (ctl.Failed()) {
        ARIES_API_LOG(WARNING) << std::dec << "fail to get blob due to rpc failure,"
            << " remote:" << ctl.remote_side() << " error:" << ctl.ErrorCode() << " (" << ctl.ErrorText() << ")"
            << " time_us:" << cost;
        if (g_aries_retry_policy.DoRetry(&ctl)) {
            return AE_AGAIN;
        }
        return AE_FAIL;
    }
    Errno rc = (Errno) res.status().code();
    if (rc != AE_OK) {
        ARIES_API_LOG(WARNING) << std::dec << "fail to get blob,"
            << " bid:" << bid2str(bid) << " remote:" << ctl.remote_side()
            << " error:" << rc << " (" << res.status().msg() << ") time_us:" << cost;
        return rc;
    }
    // flush log when exit block
    NoticeLogGuard log_guard;
    ARIES_API_LOG(NOTICE) << std::dec << "finish get blob, bid:" << bid2str(bid)
        << " remote:" << ctl.remote_side() << " time_us:" << cost << noflush;
    if (data != NULL) {
        ctl.response_attachment().copy_to(data);
        LOG(NOTICE) << std::dec << " data_size:" << data->size() << noflush;
    }
    if (res.has_blob_meta()) {
        const aries::pb::BlobMeta &meta = res.blob_meta();
        LOG(NOTICE) << std::dec << " meta_key:" << meta.key() << " user_meta_size:" << meta.meta().size();
        if (range == NULL && meta.has_blob_crc() && data != NULL) {
            uint32_t actual_crc = ::base::crc32c::Value(data->data(), data->size());
            if (meta.blob_crc() != actual_crc) {
                ARIES_API_LOG(WARNING) << std::dec << "fail to get blob data due to data crc error,"
                    << " bid:" << bid2str(bid) << " expected_crc:" << meta.blob_crc()
                    << " actual_crc:" << actual_crc;
                return AE_CHECKSUM;
            }
        }
        if (blob_meta != NULL) {
            blob_meta->key = meta.key();
            blob_meta->meta = meta.meta();
            blob_meta->create_time = meta.timestamp();
            blob_meta->blob_crc = meta.blob_crc();
            blob_meta->blob_len = meta.blob_len();
            blob_meta->compress_ratio = meta.compress_ratio();
            blob_meta->compress_type = static_cast<CompressType>(meta.compress_type());
            blob_meta->compress_level = meta.compress_level();
        }
    }
    return AE_OK;
}

Errno AriesClientImpl::remove(RequestOption &request_option, uint128_t bid,
        pb::BlobRequestErrorInfo* request_error_info) {
    uint64_t &trace_id = request_option.log_id;
    baidu::rpc::Controller ctl;
    init_rpc_option(request_option, ctl);
    std::string error_msg;
    if (ctl.timeout_ms() <= 0) {
        error_msg = "timeout_ms must greater than zero";
    }
    if (!error_msg.empty()) {
        ARIES_API_LOG(WARNING) << "fail to remove blob due to invalid argument, cause: " << error_msg;
        return AE_INVALID_ARGUMENT;
    }

    if (_proxy_wrapper) {
        return _proxy_wrapper->remove(request_option, bid, request_error_info);
    }

    aries::pb::BlobRemoveRequest req;
    aries::pb::AckResponse res;

    uint64_t vid = bid >> 64;
    uint64_t vbid = bid & ~0L;
    req.set_token(_conf_info.token);
    req.set_vid(vid);
    req.set_vbid(vbid);
    req.mutable_rpc_info()->set_timeout_ms(ctl.timeout_ms());
    req.mutable_rpc_info()->set_req_timestamp_ms(base::gettimeofday_ms());
    req.mutable_rpc_info()->set_priority(request_option.priority);
    req.mutable_rpc_info()->set_network_qos(request_option.network_qos);

    int64_t start_time = base::cpuwide_time_us();
    _stub.remove(&ctl, &req, &res, NULL);
    int64_t cost = base::cpuwide_time_us() - start_time;

    if (ctl.Failed()) {
        ARIES_API_LOG(WARNING) << std::dec << "fail to remove blob due to rpc failure,"
            << " remote:" << ctl.remote_side() << " error:" << ctl.ErrorCode() << "(" << ctl.ErrorText() << ")"
            << " time_us:" << cost;
        if (g_aries_retry_policy.DoRetry(&ctl)) {
            return AE_AGAIN;
        }
        return AE_FAIL;
    }

    Errno rc = (Errno) res.status().code();
    if (rc != AE_OK) {
        ARIES_API_LOG(WARNING) << std::dec << "fail to remove blob, bid:" << bid2str(bid) 
            << " remote:" << ctl.remote_side()
            << " error:" << rc << " (" << res.status().msg() << ") time_us:" << cost;
        return rc;
    }

    ARIES_API_LOG(NOTICE) << std::dec << "remove blob succeeded, bid:" << bid2str(bid)
            << " remote:" << ctl.remote_side() << " time_us:" << cost;
    return AE_OK;
}

Errno AriesClientImpl::allocate_volume(uint64_t trace_id,
        const std::string& space_name,
        uint64_t volume_id_hint,
        uint64_t* volume_id) {
    RequestOption request_option;
    request_option.log_id = trace_id;
    if (_proxy_wrapper) {
        return _proxy_wrapper->allocate_volume(request_option, space_name, volume_id_hint, volume_id);
    } else {
        ARIES_API_LOG(WARNING) << "open volume can only be used in proxy mode";
        return AE_FAIL;
    }
}

Errno AriesClientImpl::append_blob(RequestOption& request_option, 
                                   uint64_t volume_id,
                                   const std::string& key,
                                   const std::string& meta,
                                   const std::string& data,
                                   uint128_t *bid) {
    uint64_t& trace_id = request_option.log_id;
    if (_proxy_wrapper) {
        return _proxy_wrapper->append_blob(request_option, volume_id,
            key, meta, data, bid);
    } else {
        ARIES_API_LOG(WARNING) << "append blob can only be used in proxy mode";
        return AE_FAIL;
    }
}

Errno AriesClientImpl::release_volume(uint64_t volume_id) {
    uint64_t trace_id = base::fast_rand();
    RequestOption request_option;
    request_option.log_id = trace_id;
    if (_proxy_wrapper) {
        return _proxy_wrapper->release_volume(request_option, volume_id);
    } else {
        ARIES_API_LOG(WARNING) << "close volume can only be used in proxy mode";
        return AE_FAIL;
    }
}

Errno AriesClientImpl::seal_volume(uint64_t volume_id) {
    uint64_t trace_id = base::fast_rand();
    RequestOption request_option;
    request_option.log_id = trace_id;
    if (_proxy_wrapper) {
        return _proxy_wrapper->seal_volume(request_option, volume_id);
    } else {
        ARIES_API_LOG(WARNING) << "seal volume can only be used in proxy mode";
        return AE_FAIL;
    }
}

Errno AriesClientImpl::get_volume_size(uint64_t volume_id, int64_t* size) {
    if (_proxy_wrapper) {
        return _proxy_wrapper->get_volume_size(volume_id, size);
    } else {
        LOG(WARNING) << "get volume size can only be used in proxy mode";
        return AE_FAIL;
    }
}

Errno AriesClientImpl::set_blob_ttl(uint128_t bid, uint32_t blob_ttl) {
    uint64_t trace_id = base::fast_rand();
    RequestOption request_option;
    request_option.log_id = trace_id;
    request_option.timeout_ms = FLAGS_call_timeout_ms;
    if (_proxy_wrapper) {
        return _proxy_wrapper->set_blob_ttl(request_option, bid, blob_ttl);
    } else {
        ARIES_API_LOG(WARNING) << "set blobs ttl can only be used in proxy mode";
        return AE_FAIL;
    }
}

Errno AriesClientImpl::set_volume_ttl(uint64_t volume_id, uint64_t volume_ttl) {
    uint64_t trace_id = base::fast_rand();
    RequestOption request_option;
    request_option.log_id = trace_id;
    if (_proxy_wrapper) {
        return _proxy_wrapper->set_volume_ttl(request_option, volume_id, volume_ttl);
    } else {
        ARIES_API_LOG(WARNING) << "set volume ttl can only be used in proxy mode";
        return AE_FAIL;
    }
}

Errno AriesClientImpl::retrieve_blobs(uint64_t trace_id, const RetrieveOption& option) {
    RequestOption request_option;
    request_option.log_id = trace_id;
    if (_proxy_wrapper) {
        return _proxy_wrapper->retrieve_blobs(request_option, option);
    } else {
        ARIES_API_LOG(WARNING) << "open volume can only be used in proxy mode";
        return AE_FAIL;
    }
}

Errno AriesClientImpl::get_retrieve_task_status(uint64_t trace_id, const std::string& task_id, RetrieveTaskStatus* status) {
    RequestOption request_option;
    request_option.log_id = trace_id;
    if (_proxy_wrapper) {
        return _proxy_wrapper->get_retrieve_task_status(request_option, task_id, status);
    } else {
        ARIES_API_LOG(WARNING) << "open volume can only be used in proxy mode";
        return AE_FAIL;
    }    
}

Errno AriesClientImpl::cancel_retrieve_task(uint64_t trace_id, const std::string& task_id) {
    RequestOption request_option;
    request_option.log_id = trace_id;
    if (_proxy_wrapper) {
        return _proxy_wrapper->cancel_retrieve_task(request_option, task_id);
    } else {
        ARIES_API_LOG(WARNING) << "open volume can only be used in proxy mode";
        return AE_FAIL;
    }
}

Errno AriesClientImpl::show_space(RequestOption& request_option, const std::string& space_name,
        aries::pb::SpaceInfo* space_info) {
    uint64_t& trace_id = request_option.log_id;
    if (_proxy_wrapper) {
        Errno ret = _proxy_wrapper->show_space(request_option, space_name, space_info);
        _info_transform->transform(space_info);
        return ret;
    } else {
        ARIES_API_LOG(WARNING) << "show space can only be used in proxy mode";
        return AE_FAIL;
    }
}

Errno AriesClientImpl::list_disk_usage(RequestOption& request_option, bool show_az, bool show_idc,
        bool show_rack, bool show_group, bool show_disk_type,
        aries::pb::ClusterDiskUsageInfo* usage_info) {
    uint64_t& trace_id = request_option.log_id;
    if (_proxy_wrapper) {
        Errno ret =  _proxy_wrapper->list_disk_usage(request_option, show_az, show_idc,
                            show_rack, show_group, show_disk_type, usage_info);
        _info_transform->transform(usage_info);
        return ret;
    } else {
        ARIES_API_LOG(WARNING) << "list disk usage can only be used in proxy mode";
        return AE_FAIL;
    }
}

Errno AriesClientImpl::list_disk_io_stats(RequestOption& request_option, bool show_az, bool show_idc,
        bool show_rack, bool show_group, bool show_disk_type, bool show_user,
        aries::pb::ClusterDiskIoStatsInfo* io_stats_info) {
    uint64_t& trace_id = request_option.log_id;
    if (_proxy_wrapper) {
        Errno ret = _proxy_wrapper->list_disk_io_stats(request_option, show_az, show_idc,
                            show_rack, show_group, show_disk_type, show_user, io_stats_info);
        _info_transform->transform(io_stats_info);
        return ret;
    } else {
        ARIES_API_LOG(WARNING) << "list disk io stats can only be used in proxy mode";
        return AE_FAIL;
    }
}

Errno AriesClientImpl::show_space_usage(RequestOption& request_option, const std::string& space_name,
        aries::pb::SpaceUsageInfo* usage_info) {
    uint64_t& trace_id = request_option.log_id;
    if (_proxy_wrapper) {
        Errno ret = _proxy_wrapper->show_space_usage(request_option, space_name, usage_info);
        _info_transform->transform(usage_info);
        return ret;
    } else {
        ARIES_API_LOG(WARNING) << "show space usage can only be used in proxy mode";
        return AE_FAIL;
    }
}

Errno AriesStreamClientImpl::create_stream(CreateStreamOptions& opt, uint128_t* stream_id) {
    uint64_t trace_id = opt.log_id;
    if (opt.timeout_ms <= 0) {
        opt.timeout_ms = FLAGS_call_timeout_ms;
    }
    if (!stream_id) {
        return AE_INVALID_ARGUMENT;
    }
    if (_proxy_wrapper) {
        return _proxy_wrapper->create_stream(opt, stream_id);
    } else {
        ARIES_API_LOG(WARNING) << "create_stream can only be used in proxy mode";
        return AE_FAIL;
    }
}

Errno AriesStreamClientImpl::unlink_stream(RequestOption& request_option, uint128_t stream_id) {
    uint64_t trace_id = request_option.log_id;
    if (request_option.timeout_ms <= 0) {
        request_option.timeout_ms = FLAGS_call_timeout_ms;
    }
    if (_proxy_wrapper) {
        return _proxy_wrapper->unlink_stream(request_option, stream_id);
    } else {
        ARIES_API_LOG(WARNING) << "unlink_stream can only be used in proxy mode";
        return AE_FAIL;
    }
}

Errno AriesStreamClientImpl::truncate_stream(RequestOption& request_option, uint128_t stream_id,
        uint64_t tail_offset, std::optional<uint64_t> stream_version) {
    uint64_t trace_id = request_option.log_id;
    if (request_option.timeout_ms <= 0) {
        request_option.timeout_ms = FLAGS_call_timeout_ms;
    }
    if (_proxy_wrapper) {
        return _proxy_wrapper->truncate_stream(request_option, stream_id,
            tail_offset, stream_version);
    } else {
        ARIES_API_LOG(WARNING) << "truncate_stream can only be used in proxy mode";
        return AE_FAIL;
    }
}

Errno AriesStreamClientImpl::head_truncate_stream(RequestOption& request_option, uint128_t stream_id,
        uint64_t head_offset, std::optional<uint64_t> stream_version) {
    uint64_t trace_id = request_option.log_id;
    if (request_option.timeout_ms <= 0) {
        request_option.timeout_ms = FLAGS_call_timeout_ms;
    }
    if (_proxy_wrapper) {
        return _proxy_wrapper->head_truncate_stream(request_option, stream_id,
            head_offset, stream_version);
    } else {
        ARIES_API_LOG(WARNING) << "head_truncate_stream can only be used in proxy mode";
        return AE_FAIL;
    }
}

Errno AriesStreamClientImpl::stat_stream(RequestOption& request_option, uint128_t stream_id, StreamStatus* st) {
    uint64_t trace_id = request_option.log_id;
    if (request_option.timeout_ms <= 0) {
        request_option.timeout_ms = FLAGS_call_timeout_ms;
    }
    if (!st) {
        return AE_INVALID_ARGUMENT;
    }
    if (_proxy_wrapper) {
        return _proxy_wrapper->stat_stream(request_option, stream_id, st);
    } else {
        ARIES_API_LOG(WARNING) << "stat_stream can only be used in proxy mode";
        return AE_FAIL;
    }
}

Errno AriesStreamClientImpl::stat_streams(RequestOption& request_option, const std::vector<uint128_t>& stream_ids,
        std::vector<StreamStatus>* stream_status) {
    uint64_t trace_id = request_option.log_id;
    if (request_option.timeout_ms <= 0) {
        request_option.timeout_ms = FLAGS_call_timeout_ms;
    }
    if (!stream_status) {
        return AE_INVALID_ARGUMENT;
    }
    if (_proxy_wrapper) {
        return _proxy_wrapper->stat_streams(request_option, stream_ids, stream_status);
    } else {
        ARIES_API_LOG(WARNING) << "stat_streams can only be used in proxy mode";
        return AE_FAIL;
    }
}

Errno AriesStreamClientImpl::show_stream_space_statistics(RequestOption& request_option,
        const std::string& space_name, aries::pb::StreamSpaceStatistics* statistic) {
    uint64_t trace_id = request_option.log_id;
    if (request_option.timeout_ms <= 0) {
        request_option.timeout_ms = FLAGS_call_timeout_ms;
    }
    if (!statistic) {
        return AE_INVALID_ARGUMENT;
    }
    if (_proxy_wrapper) {
        return _proxy_wrapper->show_stream_space_statistics(request_option,
            space_name, statistic);
    } else {
        ARIES_API_LOG(WARNING) << "show_stream_space_statistics can only be used in proxy mode";
        return AE_FAIL;
    }
}

Errno AriesStreamClientImpl::link_stream(RequestOption& request_option, uint128_t stream_id) {
    uint64_t trace_id = request_option.log_id;
    if (request_option.timeout_ms <= 0) {
        request_option.timeout_ms = FLAGS_call_timeout_ms;
    }
    if (_proxy_wrapper) {
        return _proxy_wrapper->link_stream(request_option,
            stream_id);
    } else {
        ARIES_API_LOG(WARNING) << "link_stream can only be used in proxy mode";
        return AE_FAIL;
    }
}

Errno AriesStreamClientImpl::utime_stream(RequestOption& request_option, uint128_t stream_id,
        std::optional<uint64_t> atime, std::optional<uint64_t> mtime) {
    uint64_t trace_id = request_option.log_id;
    if (request_option.timeout_ms <= 0) {
        request_option.timeout_ms = FLAGS_call_timeout_ms;
    }
    if (_proxy_wrapper) {
        return _proxy_wrapper->utime_stream(request_option,
            stream_id, atime, mtime);
    } else {
        ARIES_API_LOG(WARNING) << "utime_stream can only be used in proxy mode";
        return AE_FAIL;
    }
}

Errno AriesStreamClientImpl::set_stream_cache_policy(StreamCacheOptions& opt) {
    uint64_t trace_id = opt.log_id;
    if (opt.timeout_ms <= 0) {
        opt.timeout_ms = FLAGS_call_timeout_ms;
    }
    if (_proxy_wrapper) {
        return _proxy_wrapper->set_stream_cache_policy(opt);
    } else {
        ARIES_API_LOG(WARNING) << "set_stream_cache_policy can only be used in proxy mode";
        return AE_FAIL;
    }
}

Errno AriesStreamClientImpl::open_stream_writer(StreamWriterOptions& opt, std::shared_ptr<StreamWriter>* writer) {
    uint64_t trace_id = opt.log_id;
    if (opt.timeout_ms <= 0) {
        opt.timeout_ms = FLAGS_call_timeout_ms;
    }
    if (!writer) {
        return AE_INVALID_ARGUMENT;
    }
    if (_proxy_wrapper) {
        return _proxy_wrapper->open_stream_writer(opt, writer);
    } else {
        ARIES_API_LOG(WARNING) << "open_stream_writer can only be used in proxy mode";
        return AE_FAIL;
    }
}

Errno AriesStreamClientImpl::force_open_stream_writer(StreamWriterOptions& opt, std::shared_ptr<StreamWriter>* writer) {
    uint64_t trace_id = opt.log_id;
    if (opt.timeout_ms <= 0) {
        opt.timeout_ms = FLAGS_call_timeout_ms;
    }
    if (!writer) {
        return AE_INVALID_ARGUMENT;
    }
    if (_proxy_wrapper) {
        return _proxy_wrapper->force_open_stream_writer(opt, writer);
    } else {
        ARIES_API_LOG(WARNING) << "force_open_stream_writer can only be used in proxy mode";
        return AE_FAIL;
    }
}

Errno AriesStreamClientImpl::open_stream_reader(StreamReaderOptions& opt, std::shared_ptr<StreamReader>* reader) {
    uint64_t trace_id = opt.log_id;
    if (opt.timeout_ms <= 0) {
        opt.timeout_ms = FLAGS_call_timeout_ms;
    }
    if (!reader) {
        return AE_INVALID_ARGUMENT;
    }
    if (_proxy_wrapper) {
        return _proxy_wrapper->open_stream_reader(opt, reader);
    } else {
        ARIES_API_LOG(WARNING) << "open_stream_reader can only be used in proxy mode";
        return AE_FAIL;
    }
}
}
